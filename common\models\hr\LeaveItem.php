<?php

/**
 * This is the model class for table "ivy_as_leave_item".
 *
 * The followings are the available columns in table 'ivy_as_leave_item':
 * @property integer $id
 * @property integer $staff_uid
 * @property string $branchid
 * @property integer $classid
 * @property integer $leave_id
 * @property integer $type
 * @property integer $startdate
 * @property integer $enddate
 * @property integer $hours
 * @property integer $applydate
 * @property integer $status
 * @property integer $approvedate
 * @property integer $approver
 * @property integer $flag
 * @property integer $created
 * @property integer $creator
 */
class LeaveItem extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return LeaveItem the static model class
	 */
    public $balance;
	public $startTime;
	public $endTime;
    const STATUS_WAITING = 0;
    const STATUS_AGREE = 10;
    const STATUS_REJECT = 30;
    const STATUS_INVALID = 40;
    const DOC_FLAG_HAVE = 1;
    const DOC_STATUS_HAVE = 1;
    const DOC_STATUS_NOTHING = 0;
    const OT_DURATION ='%s-8-31'; //调休有效期
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_as_leave_item';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
            array('branchid, category, staff_uid, leave_id, type, startdate, enddate, startTime, endTime, hours, applydate, created, creator, month', 'required', 'on' => 'otherLeave,annualLeave'),
            array('staff_uid, category, classid, leave_id, type, startdate, enddate, hours, applydate, status, approvedate, approver, flag, created, creator, doc_flag, doc_status, doc_uid, doc_created, month', 'numerical', 'integerOnly' => true),
            array('branchid', 'length', 'max' => 10),
            array('apply_desc', 'length', 'max' => 255),
            array('check_desc', 'length', 'max' => 255),
            array('hours', 'checkHours', 'on' => 'annualLeave'),
            // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            array('id, category, staff_uid, branchid, classid, leave_id, type, startdate, enddate, hours, applydate, status, approvedate, approver, flag, created, creator, doc_flag, doc_status, doc_uid, doc_created, month, apply_desc, check_desc', 'safe', 'on' => 'search'),
        );
	}

    //验证小时与分钟的值必须选择并且剩余假期不能为空
    public function checkHours(){
        if (!$this->hasErrors()) {
            if ($this->hours == 0) {
                $this->addError('hours', Yii::t('user', '结束时间应大于开始时间！'));
                return true;
            }
            if (($this->type == UserLeave::TYPE_ANNUAL_LEAVE) || ($this->type == UserLeave::TYPE_DAYS_OFF)){
                if ($this->leave_id && $this->staff_uid){
                    //查询假期总数
                    $leaveModel = UserLeave::model()->findByPk($this->leave_id);
                    $num = $leaveModel->hours;
                    //已使用假期数
                    $useLeave = $this->model()->getUsedLeaveList($this->staff_uid,array($this->leave_id),FALSE);
                    if (is_array($useLeave) && count($useLeave)){
                       $useNum = $useLeave[$this->leave_id]['sum'];
                    }
                    if ($num-$useNum-$this->hours<0){
                        $this->addError('hours',  Yii::t('user', '假期剩余不足！'));
                    }else{
                        $this->balance = $num-$useNum-$this->hours;
                    }
                }else{
                    $this->addError('hours',  Yii::t('user', '假期剩余不足！'));
                }
            }
        }
    }

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
             'userLeave' => array(self::BELONGS_TO, 'UserLeave', 'leave_id','with'=>'profile'),
             'creatorUser' => array(self::BELONGS_TO, 'User', 'creator','with'=>'profile'),
             'checkUser' => array(self::BELONGS_TO, 'User', 'approver','with'=>'profile'),
             'appUser' => array(self::BELONGS_TO, 'User', 'staff_uid','with'=>'profile'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'staff_uid' => 'Staff Uid',
			'branchid' => 'Branchid',
			'classid' => 'Classid',
			'leave_id' => 'Leave',
			'type' => 'Type',
			'startdate' => Yii::t('otlabels', 'Start Date'),
			'enddate' => Yii::t('otlabels','End Date'),
			'hours' => Yii::t('labels', 'Timeslot'),
			'apply_desc' => Yii::t('labels', 'Application Memo'),
			'check_desc' => Yii::t('labels', 'Approve Memo'),
			'applydate' => 'Applydate',
			'status' => 'Status',
			'approvedate' => 'Approvedate',
			'approver' => 'Approver',
			'flag' => 'Flag',
			'created' => 'Created',
			'creator' => 'Creator',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('staff_uid',$this->staff_uid);
		$criteria->compare('branchid',$this->branchid,true);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('leave_id',$this->leave_id);
		$criteria->compare('type',$this->type);
		$criteria->compare('startdate',$this->startdate);
		$criteria->compare('enddate',$this->enddate);
		$criteria->compare('hours',$this->hours);
		$criteria->compare('applydate',$this->applydate);
		$criteria->compare('status',$this->status);
		$criteria->compare('approvedate',$this->approvedate);
		$criteria->compare('approver',$this->approver);
		$criteria->compare('flag',$this->flag);
		$criteria->compare('created',$this->created);
		$criteria->compare('creator',$this->creator);

		return new CActiveDataProvider(get_class($this), array(
			'criteria'=>$criteria,
		));
	}

    /*
     * 获取已某员工已休假列表
     * @param $staffUid 员工号
     * @param $leaveId  假期分录表ID
     * @param $show     (TRUE:已使用假期分类详细信息FALSE:已使用假期分类汇总)
     * @return array()
     */
    public function getUsedLeaveList($staffUid,$leaveId,$show = true,$status = array(self::STATUS_WAITING,  self::STATUS_AGREE)){
        $leaveList = array();
        if (!$staffUid || !count($leaveId)){
            return $leaveList;
        }
        $leaveData = Yii::app()->db->createCommand()
                    ->select()
                    ->from('ivy_as_leave_item')
                    ->where('staff_uid=:staff_uid',array(':staff_uid'=>$staffUid))
                    ->andWhere(array('in','leave_id',$leaveId))
                    ->andWhere(array('in','status',$status))
                    ->order('startdate ASC')
                    ->queryAll();
        if (is_array($leaveData) && count($leaveData)){
            foreach ($leaveData as $k=>$val){
                if ($show === true){
                    $leaveList[$val['leave_id']] = $val;
                }else{
                    $leaveList[$val['leave_id']]['sum'] = isset($leaveList[$val['leave_id']]['sum']) ? $leaveList[$val['leave_id']]['sum']+$val['hours'] : $val['hours'];
                }
            }
        }
        return $leaveList;
    }

    /*
     * 获取某员工所有休假信息
     * @retun Array
     */
    static public function getUseLeaveAll($staffUid){
        $result = array();
        $leaveData = Yii::app()->db->createCommand()
                    ->select()
                    ->from('ivy_as_leave_item')
                    ->where('staff_uid=:staff_uid',array(':staff_uid'=>$staffUid))
                    ->andWhere(array('in','status',array(self::STATUS_WAITING,  self::STATUS_AGREE)))
                    ->order('startdate ASC')
                    ->queryAll();
        if (is_array($leaveData) && count($leaveData)){
            foreach ($leaveData as $val){
                $result[$val['type']]['hours'] = isset($result[$val['type']]['hours']) ? $result[$val['type']]['hours'] + $val['hours'] : $val['hours'];
            }
        }
        unset($leaveData);
        return $result;
    }

     /*
     * 获取某员工已审核的或等待审核请假信息
     * @return Array
     */
    static public function getAllLeaveItem($staffUid){
        $result = array();
        $leaveData = Yii::app()->db->createCommand()
                    ->select()
                    ->from('ivy_as_leave_item')
                    ->where('staff_uid=:staff_uid',array(':staff_uid'=>$staffUid))
                    ->order('startdate ASC')
                    ->queryAll();
        if (is_array($leaveData) && count($leaveData)){
            foreach ($leaveData as $val){
                if ($val['status'] == self::STATUS_AGREE){
                    $result[$val['type']]['agree']['hours'] = isset($result[$val['type']]['agree']['hours']) ? $result[$val['type']]['agree']['hours'] + $val['hours'] : $val['hours'];
                }elseif($val['status'] == self::STATUS_WAITING){
                    $result[$val['type']]['waiting']['hours'] = isset($result[$val['type']]['waiting']['hours']) ? $result[$val['type']]['waiting']['hours'] + $val['hours'] : $val['hours'];
                }
            }
        }
        unset($leaveData);
        return $result;
    }

    public static function getStatus($id = -1){
        $data = array(
            self::STATUS_WAITING=>  Yii::t('hr', '待审核'),
            self::STATUS_AGREE=>  Yii::t('hr', '通过'),
            self::STATUS_REJECT=>  Yii::t('hr', '拒绝'),
        );
        return ($id >=0) ? $data[$id] : $data;
    }

}