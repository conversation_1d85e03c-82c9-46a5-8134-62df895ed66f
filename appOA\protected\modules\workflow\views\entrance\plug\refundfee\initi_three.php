<!-- Modal -->
<?php 
    $labels = Invoice::model()->attributeLabels();
?>
<?php echo CHtml::form('/workflow/entrance/index', 'post',array('class'=>'J_ajaxForm'));?>
<div class="modal fade" id="<?php echo $data['modalNameId']?>" tabindex="-1" role="dialog" aria-labelledby="workflowModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel"><?php echo CommonUtils::autoLang($data['workflow']->definationObj->defination_name_cn,$data['workflow']->definationObj->defination_name_en);?> - <?php echo CommonUtils::autoLang($data['workflow']->nodeObj->node_name_cn,$data['workflow']->nodeObj->node_name_cn);?></h4>
            </div>
            <div class="modal-body" id="workflow-modal-body">
                <ul class="nav nav-wizard mb20">
                    <li style="width:33%;"><a>第一步：选择退费孩子</a></li>
                    <li style="width:33%;"><a>第二步：选择退费账单</a></li>
                    <li class="active" style="width:33%;"><a>第三步：确认退费金额</a></li>
                </ul>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <td><?php echo $labels['title'];?></td>
                                <td><?php echo $labels['payment_type'];?></td>
                                <td><?php echo $labels['amount'];?></td>
                                <td><?php echo $labels['startdate'];?></td>
                                <td><?php echo $labels['enddate'];?></td>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($data['invoices'] as $invoice):
                                   $exte2 = ($data['workflow']->getOperateValue('exte2')) ? $data['workflow']->getOperateValue('exte2') : $invoice->enddate;
                                ?>
                            <tr>
                                <td><?php echo $invoice->title;?></td>
                                <td><?php echo $invoice->payment_type;?></td>
                                <td><?php echo $invoice->amount;?></td>
                                <td><?php echo OA::formatDateTime($invoice->startdate);?></td>
                                <td><?php echo OA::formatDateTime($invoice->enddate);?></td>
                            </tr>
                            <tr>
                                <td colspan="2">
                                    <div class="form-group" style="width: 250px;">
                                        <?php echo CHtml::textField("InvoiceChildRefund[".$invoice->invoice_id."][title]", '', array('placeholder'=>'退费标题','class'=>'form-control'));?>
                                    </div>
                                    <div class="form-group" style="width: 250px;">
                                        <?php echo CHtml::textField("InvoiceChildRefund[".$invoice->invoice_id."][amount]", $data['refundList'][$invoice->payment_type][$invoice->invoice_id], array('placeholder'=>'退费金额','class'=>'form-control'));?>
                                        <p class="bg-warning" style="padding:4px;">系统自动计算金额，可根据实际情况更改！</p>
                                    </div>
                                </td>
                                <td colspan="3">
                                    <div class="form-group" style="width: 250px;">
                                        <?php echo CHtml::textField("InvoiceChildRefund[".$invoice->invoice_id."][startdate]", OA::formatDateTime($data['workflow']->opertationObj->exte1), array('placeholder'=>'退费开始日期','class'=>'form-control'));?>
                                    </div>
                                    <div class="form-group" style="width: 250px;">
                                        <?php echo CHtml::textField("InvoiceChildRefund[".$invoice->invoice_id."][enddate]", OA::formatDateTime($exte2), array('placeholder'=>'退费结束日期','class'=>'form-control'));?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach;?>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <?php echo CHtml::hiddenField('definationId', $data['workflow']->getWorkflowId());?>
                <?php echo CHtml::hiddenField('nodeId', $data['workflow']->getCurrentNodeId());?>
                <?php echo CHtml::hiddenField('branchId', $data['workflow']->schoolId);?>
                <?php echo CHtml::hiddenField('operationId', $data['workflow']->getOperateId());?>
                <?php echo CHtml::hiddenField('step', 'three');?>
                <?php echo CHtml::hiddenField('buttonCategory', '');?>
                <?php echo CHtml::hiddenField('action', 'publicSave');?>
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('workflow', 'Cancel');?></button>
                <button type="button" class="btn btn-info J_ajax_submit_btn" data-category="previous">上一步</button>
                <button type="button" class="btn btn-primary J_ajax_submit_btn" data-category="next">提交</button>
            </div>
        </div>
    </div>
</div>
<?php echo CHtml::endForm();?>