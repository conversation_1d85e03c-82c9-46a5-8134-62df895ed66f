$('#showDialog1').click(function(){
    var $dialog = $('#dialog1');
    $dialog.show();
    $dialog.find('.default').one('click', function (e) {
        e.preventDefault();
        $dialog.hide();
    });
    $dialog.find('.primary').one('click', function (e) {
        e.preventDefault();
        $dialog.hide();
        var url = $(this).attr('href');
        $.ajax({
            type: 'post',
            url: url,
            dataType: 'json',
            success: function(data){
                if(data.state == 'success'){
                    var $toast = $('#toast');
                    if ($toast.css('display') != 'none') {
                        return;
                    }

                    $toast.show();
                    setTimeout(function () {
                        $toast.hide();
                        wx.closeWindow();
                    }, 2000);
                }
            }
        });
    });
});

wx.hideOptionMenu();

$(function() {
    $('.ajax_submit').click(function (e) {
        e.preventDefault();
        $('#loadingToast').show();
        var data = $('.ajax_submit').parents("form").serializeArray();
        var msg = '';
        $.ajax({
            type: 'POST',
            dataType: 'json',
            data: data,
            timeout: 5000,
            success:function(data) {
                if (data.state == 'success') {
                    $('.weui_cells_form').children().removeClass('weui_cell_warn');
                    showMessage();
                }
                if (data.state == 'fail') {
                    $('.weui_cells_form').children().removeClass('weui_cell_warn');
                    var prefix = $('.ajax_submit').attr('data-prefix');
                    //$.each(data.error, function(i,val){
                    //    var cell = $('#' +prefix+ '_'+i).parent().parent()
                    //    cell.find('i').attr('title',val[0]);
                    //    cell.addClass('weui_cell_warn');
                    //});
                }
            },
            error:function() {
            },
            complete:function() {
                $('#loadingToast').hide();
            }
        });
    });
});

/**
 * 完成弹出框
 * @param  {String} msg     [显示文字]
 * @param  {Number} timeOut [停留时间]
 * @return {[type]}         [description]
 */
function showMessage(msg, timeOut) {
    msg = msg || '已完成';
    timeOut = timeOut || 1000;
    $('#toast p').text(msg);
    $('#toast').show();
    setTimeout(function() {
        $('#toast').hide();
    },timeOut);
}

function showTips(title, content) {
    title = title || '弹窗标题';
    content = content || '弹窗提示';
    var dialog = $('#dialog2');
    $('#dialog2 .weui_dialog_title').text(title);
    $('#dialog2 .weui_dialog_bd').text(content);
    dialog.show();
}