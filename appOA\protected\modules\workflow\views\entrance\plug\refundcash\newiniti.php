<!-- Modal -->
<?php echo CHtml::form('/workflow/entrance/index', 'post',array('class'=>'J_ajaxForm'));?>
<div class="modal fade" id="<?php echo $data['modalNameId']?>" tabindex="-1" role="dialog" aria-labelledby="workflowModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel"><?php echo CommonUtils::autoLang($data['workflow']->definationObj->defination_name_cn,$data['workflow']->definationObj->defination_name_en);?> - <?php echo CommonUtils::autoLang($data['workflow']->nodeObj->node_name_cn,$data['workflow']->nodeObj->node_name_cn);?></h4>
            </div>
            <div class="modal-body" id="workflow-modal-body">
                <ul class="nav nav-wizard mb20">
                    <li class="active" style="width:50%;"><a>第一步：选择孩子</a></li>
                    <li style="width:50%;"><a>第二步：提现金额</a></li>
                </ul>
                <div class="form-group">
                    <label for="searchChild">查询孩子</label>
                    <?php $this->widget('ext.search.ChildSearchBox', array(
                            'acInputCSS' => 'form-control',
                            'allowMultiple' => false,
                            'allowMultipleSchool' => false,
                            'simpleDisplay' => false,
                            'extendCss' => false,
                            'useModel' => false,
                            'withAlumni' => true,
                            'name' => 'RefundCash[childid]',
                            'htmlOptions' => array('class'=>'form-control'),
                            'select'=>$data['select'],
                            'data'=>$data['ftsChild']
                        )) ?>
                </div>
                <div class="form-group">
                    <label>意见</label>
                    <?php echo CHtml::textArea('RefundCash[memo]',$data['processDesc'],array('class'=>'form-control','row'=>3));?>
                </div>
            </div>
            <div class="modal-footer">
                <?php echo CHtml::hiddenField('definationId', $data['workflow']->getWorkflowId());?>
                <?php echo CHtml::hiddenField('nodeId', $data['workflow']->getCurrentNodeId());?>
                <?php echo CHtml::hiddenField('branchId', $data['workflow']->schoolId);?>
                <?php echo CHtml::hiddenField('operationId', $data['workflow']->getOperateId());?>
                <?php echo CHtml::hiddenField('step', 'one');?>
                <?php echo CHtml::hiddenField('buttonCategory', '');?>
                <?php echo CHtml::hiddenField('action', 'publicSave');?>
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('workflow', 'Cancel');?></button>
                <button type="button" class="btn btn-primary J_ajax_submit_btn" data-category="next">下一步</button>
            </div>
        </div>
    </div>
</div>
<?php CHtml::endForm();?>
<?php 
$str =<<<js
    <script language="javascript" type="text/javascript"> 
        function mySelect(elem){
            var v=$(elem).attr('uid');
            jQuery('#searchChild').data('autocomplete').close();
            $('#searchChild').val("");
            if(v>0)
            $('#RefundCash_childid').append('<option value="'+v+'" selected="selected">'+$(elem).html()+'</option>');
        }
        jQuery('#searchChild').autocomplete({'minLength':'1','delay':1000,'position':{'my':'left top','at':'left bottom','collision':'none'},'source':'/backend/search/childoutput?displayType=&withAlumni=withAlumni&allowMultipleSchool=1'});
        jQuery('#searchChild').data( 'autocomplete' )._renderItem = function( ul, item ) {
				return $( '<li onclick=\'mySelect(this);return false;\'></li>' )
					.attr('uid',item.value)
					.data( 'item.autocomplete', item )
					.append( item.label)
					.appendTo( ul );			
        };
    </script>
js;
    echo $str;
?>