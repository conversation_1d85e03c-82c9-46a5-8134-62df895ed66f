<?php $form = $this->beginWidget('CActiveForm', array(
    'id' => 'visits-form',
    'enableAjaxValidation' => false,
    'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form'),
));
$branchList = Branch::model()->getBranchList();
$cfgs = AdmissionsDs::getConfig();
if ($this->branchId == "BJ_SLT") {
    $class = $cfgs['grade_slt'];
}else if ($this->branchId == "BJ_DS") {
    $class = $cfgs['grade'];
}

?>
<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span>
    </button>
    <h4 class="modal-title"><?php echo $modalTitle; ?></h4>
</div>
<div class="modal-body">
    <div class="panel panel-default">
        <div class="panel-heading"><?php echo Yii::t('site', 'Student Information') ?></div>
        <div class="panel-body">
            <!-- 英文名 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'school_id'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->dropDownList($model, 'school_id', $schoolList, array('class' => 'form-control')); ?>
                </div>
            </div>
            <?php if($model->status == AdmissionsDs::STATS_DROPOUT){ ?>
                <div class="form-group">
                    <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'status'); ?></label>
                    <div class="col-xs-10">
                        <?php echo $form->dropDownList($model, 'status', $cfg['states'], array('class' => 'form-control')); ?>
                    </div>
                </div>
            <?php } ?>
            <!-- 英文名 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'en_name'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->textField($model, 'en_name', array('maxlength' => 255, 'class' => 'form-control')); ?>
                </div>
            </div>

            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'en_name_middle'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->textField($model, 'en_name_middle', array('maxlength' => 255, 'class' => 'form-control')); ?>
                </div>
            </div>

            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'en_name_last'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->textField($model, 'en_name_last', array('maxlength' => 255, 'class' => 'form-control')); ?>
                </div>
            </div>
            <!-- 中文名 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'cn_name'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->textField($model, 'cn_name', array('maxlength' => 255, 'class' => 'form-control')); ?>
                </div>
            </div>

            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'cn_name_last'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->textField($model, 'cn_name_last', array('maxlength' => 255, 'class' => 'form-control')); ?>
                </div>
            </div>
            <!-- 性别 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'gender'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->dropDownList($model, 'gender', $cfgs['gender'], array('class' => 'form-control')); ?>
                </div>
            </div>
            <!-- 出生日期 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'birthday'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->textField($model, 'birthday', array('maxlength' => 255, 'class' => 'datepicker form-control', 'readonly' => 'readonly')); ?>
                </div>
            </div>
            <!-- 国籍和民族 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'nationality'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->dropDownList($model, 'nationality', $cfgs['countries'], array('class' => 'form-control', 'prompt' => '请选1择')); ?>
                </div>
            </div>
            <!-- 护照号/身份证 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'passportid'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->textField($model, 'passportid', array('maxlength' => 255, 'class' => 'form-control')); ?>
                </div>
            </div>
            <!-- 母语 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'native_lang'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->dropDownList($model, 'native_lang', $cfgs['language'], array('class' => 'form-control')); ?>
                </div>
            </div>
            <!-- 其它语言 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'other_lang'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->dropDownList($model, 'other_lang', $cfgs['language'], array('class' => 'form-control', 'prompt' => '请选择', 'prompt' => '请选择')); ?>
                </div>
            </div>
            <!-- 入学年级 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'start_grade'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->dropDownList($model, 'start_grade', $class, array('class' => 'form-control', 'onchange' => 'classWhy()')); ?>
                </div>
            </div>
            <!-- 入学日期 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'start_date'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->textField($model, 'start_date', array('maxlength' => 255, 'class' => 'datepicker form-control')); ?>
                </div>
            </div>
            <!-- 入学学年 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'start_year'); ?></label>
                <div class="col-xs-10">
                    <?php echo $form->dropDownList($model, 'start_year', $calender, array('class' => 'form-control')); ?>
                </div>
            </div>
            <!-- 是否为员工 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'is_staff'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->dropDownList($model, 'is_staff', array(Yii::t('user', 'No'), Yii::t('user', 'Yes')), array('class' => 'form-control')); ?>
                </div>
            </div>
            <!-- is_siblings -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'is_siblings'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->dropDownList($model, 'is_siblings', array(1 => Yii::t('user', 'No'), 2 => Yii::t('user', 'Yes')), array('class' => 'form-control')); ?>
                </div>
            </div>
            <!-- is_recommended -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'is_recommended'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->dropDownList($model, 'is_recommended', array(1=> Yii::t('user', 'No'), 2=>Yii::t('user', 'Yes')), array('class' => 'form-control')); ?>
                </div>
            </div>

            <!--是否参观过校园-->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'is_visit'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->dropDownList($model, 'is_visit', array(1 => Yii::t('user','No'), 2 => Yii::t('user','Yes')), array('class' => 'form-control')); ?>
                </div>
            </div>

            <!--是否为北京户籍-->
            <!--<div class="form-group">
                <label class="col-xs-2 control-label"><php /*echo $form->labelEx($model, 'account_location'); */?></label>

                <div class="col-xs-10">
                    <php /*echo $form->dropDownList($model, 'account_location', array(Yii::t('user', 'No'), Yii::t('user', 'Yes')), array('class' => 'form-control')); */?>
                </div>
            </div>-->

            <!-- 家庭地址 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'home_address'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->textField($model, 'home_address', array('maxlength' => 255, 'class' => 'form-control')); ?>
                </div>
            </div>
        </div>
    </div>

    <?php if(in_array($model->school_id, array('DS','SLT'))): ?>
        <div class="panel panel-default">
            <div class="panel-heading"><?php echo Yii::t('site', '学籍情况') ?></div>
            <div class="panel-body">
                <!--入学年级是学前班和一年级的-->
                <div class="form-group" style="display: none" id="account">
                    <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'student_status1'); ?></label>

                    <div class="col-xs-10">
                        <?php echo $form->dropDownList($model, 'student_status1', $cfgs['student_status'], array('class' => 'form-control', 'empty' => '请选择学籍情况', 'onchange' => 'schoolRoll()')); ?>
                    </div>
                </div>

                <!--京籍外区-->
                <div style="display: none" id="student_status1_2">
                    <div class="form-group">
                        <label class="col-xs-2 control-label"></label>

                        <div class="col-xs-10">
                            <?php echo $form->dropDownList($model, 'house1', $cfgs['house'], array('class' => 'form-control', 'empty' => '居住情况')); ?>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-xs-2 control-label"></label>

                        <div class="col-xs-10">
                            <?php echo $form->dropDownList($model, 'social_security', $cfgs['yes_no'], array('class' => 'form-control', 'empty' => '父母一方是否有朝阳区社保')); ?>
                        </div>
                    </div>
                </div>

                <!--非京籍-->
                <div style="display: none" id="student_status1_3">
                    <div class="form-group">
                        <label class="col-xs-2 control-label"></label>

                        <div class="col-xs-10">
                            <?php echo $form->dropDownList($model, 'house2', $cfgs['house'], array('class' => 'form-control', 'empty' => '居住情况')); ?>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-xs-2 control-label"></label>

                        <div class="col-xs-10">
                            <?php echo $form->dropDownList($model, 'parentsSituation', $cfgs['yes_no'], array('class' => 'form-control', 'empty' => '父母一方是否有朝阳区工作居住证（含子女随迁页）')); ?>
                        </div>
                    </div>
                </div>

                <!--外籍-->
                <div style="display: none" id="student_status1_4">
                    <div class="form-group">
                        <label class="col-xs-2 control-label"></label>

                        <div class="col-xs-10">
                            <?php echo $form->dropDownList($model, 'house3', $cfgs['house'], array('class' => 'form-control', 'empty' => '居住情况')); ?>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-xs-2 control-label"></label>

                        <div class="col-xs-10">
                            <?php echo $form->dropDownList($model, 'visa_type', $cfgs['visa_type'], array('class' => 'form-control', 'empty' => '签证类型')); ?>
                        </div>
                    </div>
                </div>

                <!--入学年级大于一年级-->
                <div class="form-group" style="display: none" id="account_2">
                    <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'student_status1'); ?></label>

                    <div class="col-xs-10">
                        <?php echo $form->dropDownList($model, 'student_status2', $cfgs['student_status2'], array('class' => 'form-control', 'empty' => '请选择学籍情况')); ?>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <div class="panel panel-default">
        <div class="panel-heading"><?php echo Yii::t('site', 'School History') ?></div>
        <div class="panel-body">
            <!-- 学习经历 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'school_history'); ?></label>

                <div class="col-xs-10 table-responsive">
                    <table class="table table-bordered">
                        <?php if ($model->school_history): ?>
                            <tr>
                                <th><?php echo Yii::t('user', 'School Name') ?></th>
                                <th><?php echo Yii::t('user', 'City, Country') ?></th>
                                <th><?php echo Yii::t('user', 'Dates Attended') ?></th>
                                <th><?php echo Yii::t('user', 'Grade') ?></th>
                                <th><?php echo Yii::t('user', 'Curriculum') ?></th>
                                <th><?php echo Yii::t('user', 'Instruction Language') ?></th>
                            </tr>
                        <?php endif; ?>
                        <?php foreach (json_decode($model->school_history) as $v): ?>
                            <tr>
                                <td><input class="form-control" type="text" name="study-history[school][]"
                                           value="<?php echo $v->school; ?>"></td>
                                <td><input class="form-control" type="text" name="study-history[location][]"
                                           value="<?php echo $v->location; ?>"></td>
                                <td><input class="form-control" type="text" name="study-history[studytime][]"
                                           value="<?php echo $v->studytime; ?>"></td>
                                <td><input class="form-control" type="text" name="study-history[grade][]"
                                           value="<?php echo $v->grade; ?>"></td>
                                <td><input class="form-control" type="text" name="study-history[curriculum][]"
                                           value="<?php echo $v->curriculum; ?>"></td>
                                <td><input class="form-control" type="text" name="study-history[language][]"
                                           value="<?php echo $v->language; ?>"></td>
                            </tr>
                        <?php endforeach; ?>
                        <tr>
                            <td colspan="6">
                                <button type="button" class="btn btn-default btn-xs"
                                        onclick="addStudyHistory(this)"><?php echo Yii::t('user', 'Add') ?></button>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="panel panel-default">
        <div class="panel-heading"><?php echo Yii::t('site', 'Sibling Information') ?></div>
        <div class="panel-body">
            <!-- 兄弟姐妹信息 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'sibling_info'); ?></label>

                <div class="col-xs-10 table-responsive">
                    <table class="table table-bordered">
                        <?php if ($model->sibling_info): ?>
                            <tr>
                                <th><?php echo Yii::t('user', 'Name') ?></th>
                                <th><?php echo Yii::t('user', 'Age') ?></th>
                                <th><?php echo Yii::t('user', 'Gender') ?></th>
                                <th><?php echo Yii::t('user', 'Current School') ?></th>
                                <th><?php echo Yii::t('user', 'Grade') ?></th>
                            </tr>
                        <?php endif; ?>
                        <?php foreach (json_decode($model->sibling_info) as $v): ?>
                            <tr>
                                <td><input class="form-control" type="text" name="sibling-info[name][]"
                                           value="<?php echo $v->name; ?>"></td>
                                <td><input class="form-control" type="text" name="sibling-info[age][]"
                                           value="<?php echo $v->age; ?>"></td>
                                <td><input class="form-control" type="text" name="sibling-info[gender][]"
                                           value="<?php echo $v->gender; ?>"></td>
                                <td><input class="form-control" type="text" name="sibling-info[school][]"
                                           value="<?php echo $v->school; ?>"></td>
                                <td><input class="form-control" type="text" name="sibling-info[grade][]"
                                           value="<?php echo $v->grade; ?>"></td>
                            </tr>
                        <?php endforeach; ?>
                        <tr>
                            <td colspan="6">
                                <button type="button" class="btn btn-default btn-xs" onclick="addSibling(this)"><?php echo Yii::t('user', 'Add') ?></button>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="panel panel-default">
        <div class="panel-heading"><?php echo Yii::t('userinfo', 'Parent Information') ?></div>
        <div class="panel-body">
            <!-- 父亲姓名 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'father_name'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->textField($model, 'father_name', array('maxlength' => 255, 'class' => 'form-control')); ?>
                </div>
            </div>
            <!-- 父亲国籍 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'father_nation'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->dropDownList($model, 'father_nation', $cfgs['countries'], array('class' => 'form-control', 'prompt' => '请选择')); ?>
                </div>
            </div>
            <!-- 父亲母语 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'father_lang'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->dropDownList($model, 'father_lang', $cfgs['language'], array('class' => 'form-control', 'prompt' => '请选择')); ?>
                </div>
            </div>
            <!-- 父亲家庭使用语言 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'father_home_lang'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->dropDownList($model, 'father_home_lang', $cfgs['language'], array('class' => 'form-control', 'prompt' => '请选择')); ?>
                </div>
            </div>
            <!-- 父亲工作单位 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'father_employer'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->textField($model, 'father_employer', array('maxlength' => 255, 'class' => 'form-control')); ?>
                </div>
            </div>
            <!-- 父亲职位 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'father_position'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->textField($model, 'father_position', array('maxlength' => 255, 'class' => 'form-control')); ?>
                </div>
            </div>
            <!-- 父亲学历 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'father_education'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->dropDownList($model, 'father_education', $cfgs['education'], array('class' => 'form-control', 'prompt' => '请选择')); ?>
                </div>
            </div>
            <!-- 父亲电话 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'father_phone'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->textField($model, 'father_phone', array('maxlength' => 255, 'class' => 'form-control')); ?>
                </div>
            </div>
            <!-- 父亲邮箱 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'father_email'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->textField($model, 'father_email', array('maxlength' => 255, 'class' => 'form-control')); ?>
                </div>
            </div>
            <hr/>
            <!-- 母亲姓名 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'mother_name'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->textField($model, 'mother_name', array('maxlength' => 255, 'class' => 'form-control')); ?>
                </div>
            </div>
            <!-- 母亲国籍 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'mother_nation'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->dropDownList($model, 'mother_nation', $cfgs['countries'], array('class' => 'form-control', 'prompt' => '请选择')); ?>
                </div>
            </div>
            <!-- 母亲母语 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'mother_lang'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->dropDownList($model, 'mother_lang', $cfgs['language'], array('class' => 'form-control', 'prompt' => '请选择')); ?>
                </div>
            </div>
            <!-- 母亲家庭使用语言 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'mother_home_lang'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->dropDownList($model, 'mother_home_lang', $cfgs['language'], array('class' => 'form-control', 'prompt' => '请选择')); ?>
                </div>
            </div>
            <!-- 母亲工作单位 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'mother_employer'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->textField($model, 'mother_employer', array('maxlength' => 255, 'class' => 'form-control')); ?>
                </div>
            </div>
            <!-- 母亲职位 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'mother_position'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->textField($model, 'mother_position', array('maxlength' => 255, 'class' => 'form-control')); ?>
                </div>
            </div>
            <!-- 母亲学历 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'mother_education'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->dropDownList($model, 'mother_education', $cfgs['education'], array('class' => 'form-control', 'prompt' => '请选择')); ?>
                </div>
            </div>
            <!-- 母亲电话 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'mother_phone'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->textField($model, 'mother_phone', array('maxlength' => 255, 'class' => 'form-control')); ?>
                </div>
            </div>
            <!-- 母亲邮箱 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'mother_email'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->textField($model, 'mother_email', array('maxlength' => 255, 'class' => 'form-control')); ?>
                </div>
            </div>
        </div>
    </div>

    <div class="panel panel-default">
        <div class="panel-heading"><?php echo Yii::t('site', 'Other Information') ?></div>
        <div class="panel-body">
            <!-- 天赋 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'talent'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->textArea($model, 'talent', array('class' => 'form-control')); ?>
                </div>
            </div>
            <!-- 学习困难 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'learn_diffculties'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->textArea($model, 'learn_diffculties', array('maxlength' => 255, 'class' => 'form-control')); ?>
                </div>
            </div>
            <!-- 残疾或障碍 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'physical_disabilities'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->textArea($model, 'physical_disabilities', array('class' => 'form-control')); ?>
                </div>
            </div>
            <!-- 健康方面问题 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'medical_conditions'); ?></label>

                <div class="col-xs-10">
                    <?php echo $form->textArea($model, 'medical_conditions', array('class' => 'form-control')); ?>
                </div>
            </div>
        </div>
    </div>

    <div class="panel panel-default">
        <div class="panel-heading"><?php echo Yii::t('site', 'Attachment upload and download') ?></div>
        <div class="panel-body">
            <!-- 近两年学业报告 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'academic_report'); ?></label>

                <div class="col-xs-10">
                    <?php
                    foreach ($model->academic_report as $file) {
                        echo CHtml::link('file', $this->createUrl('ossfileRedit', array('filePath' => $file)), array('target' => '_blank', 'class' => 'btn btn-default btn-xs mb5')) . ' ';
                    }
                    ?>
                    <?php echo $form->fileField($model, 'reportFiles[]', array('maxlength' => 255, 'class' => '', 'multiple' => 'multiple')); ?>
                </div>
            </div>
            <!-- 户口本或护照复印件 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'persion_copy'); ?></label>

                <div class="col-xs-10">
                    <?php
                    foreach ($model->persion_copy as $file) {
                        echo CHtml::link('file', $this->createUrl('ossfileRedit', array('filePath' => $file)), array('target' => '_blank', 'class' => 'btn btn-default btn-xs mb5')) . ' ';
                    }
                    ?>
                    <?php echo $form->fileField($model, 'persionFiles[]', array('maxlength' => 255, 'class' => '', 'multiple' => 'multiple')); ?>
                </div>
            </div>

            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'recommendation_form'); ?></label>

                <div class="col-xs-10">
                    <?php
                    foreach ($model->recommendation_form as $files) {
                        echo CHtml::link('file', $this->createUrl('ossfileRedit', array('filePath' => $files)), array('target' => '_blank', 'class' => 'btn btn-default btn-xs mb5')) . ' ';
                    }
                    ?>
                    <?php echo $form->fileField($model, 'recommendation[]', array('maxlength' => 255, 'class' => '', 'multiple' => 'multiple')); ?>
                </div>
            </div>
            <!-- 孩子头像 -->
            <div class="form-group">
                <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'child_avatar'); ?></label>

                <div class="col-xs-10">
                    <?php
                    if ($model->child_avatar) {
                        echo CHtml::image($model->child_avatar, 'child_avatar', array('class' => 'img-rounded img-responsive mb5'));
                    }
                    ?>
                    <?php echo $form->fileField($model, 'avatarFile', array('maxlength' => 255, 'class' => '')); ?>
                </div>
            </div>
        </div>
    </div>

    <!-- 备注 -->
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'remark'); ?></label>

        <div class="col-xs-10">
            <?php echo $form->textField($model, 'remark', array('maxlength' => 255, 'class' => 'form-control')); ?>
        </div>
    </div>

</div>
<div class="modal-footer">
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
    <button type="button" class="btn btn-default"
            data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
</div>

<?php $this->endWidget(); ?>
<script>
    $('.datepicker').datepicker({
        changeMonth: true,
        changeYear: true,
        dateFormat: 'yy-mm-dd'
    });
    // 回调：添加成功
    function cbVisit() {
        $('#modal').modal('hide');
        $.fn.yiiGridView.update('confirmed-visit-grid');
    }
    // 添加学习经历
    function addStudyHistory(e) {
        $(e).parent().parent().before('<tr><td><input class="form-control" type="text" name="study-history[school][]"></td><td><input class="form-control" type="text" name="study-history[location][]"></td><td><input class="form-control" type="text" name="study-history[studytime][]"></td><td><input class="form-control" type="text" name="study-history[grade][]"></td><td><input class="form-control" type="text" name="study-history[curriculum][]"></td><td><input class="form-control" type="text" name="study-history[language][]"></td></tr>');
    }
    // 添加兄弟姐妹
    function addSibling(e) {
        $(e).parent().parent().before('<tr><td><input class="form-control" type="text" name="sibling-info[name][]"></td><td><input class="form-control" type="text" name="sibling-info[age][]"></td><td><input class="form-control" type="text" name="sibling-info[gender][]"></td><td><input class="form-control" type="text" name="sibling-info[school][]"></td><td><input class="form-control" type="text" name="sibling-info[grade][]"></td></tr>');
    }

    var start = '<?php echo $model->start_grade ?>';
    var student_status1 = '<?php echo $model->student_status1 ?>';
    if(start == 3 || start == 4){
        $("#account").css('display','block');
        $("#student_status1_" + student_status1).css('display','block');
    }

    if(start > 4){
        $("#account_2").css('display','block');
    }

    function classWhy() {
        $("#account").css('display','none');
        $("#AdmissionsDs_student_status1").val("");
        $("#account_2").css('display','none');
        $("#AdmissionsDs_student_status2").val("");
        $("#student_status1_2").css('display','none');
        $("#student_status1_3").css('display','none');
        $("#student_status1_4").css('display','none');
        var class_statr = $("#AdmissionsDs_start_grade").val();
        if(class_statr == 3 || class_statr == 4){
            $("#account").css('display','block');
        }
        if(class_statr > 4){
            $("#account_2").css('display','block');
        }
    }
    function schoolRoll() {
        var account = $("#AdmissionsDs_student_status1").val();
        $("#account_2").css('display','none');
        $("#student_status1_2").css('display','none');
        $("#student_status1_3").css('display','none');
        $("#student_status1_4").css('display','none');

        //　京籍外区
        if(account == 2){
            $("#student_status1_" + account).css('display','block')
        }

        //　非京籍
        if(account == 3){
            $("#student_status1_" + account).css('display','block')
        }

        // 外籍
        if(account == 4){
            $("#student_status1_" + account).css('display','block')
        }
    }
</script>