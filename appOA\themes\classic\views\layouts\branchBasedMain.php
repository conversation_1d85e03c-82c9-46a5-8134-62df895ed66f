<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta name="language" content="en" />

	<!-- blueprint CSS framework -->
	<link rel="stylesheet" type="text/css" href="<?php echo Yii::app()->request->baseUrl; ?>/css/screen.css" media="screen, projection" />
	<link rel="stylesheet" type="text/css" href="<?php echo Yii::app()->request->baseUrl; ?>/css/print.css" media="print" />
	<!--[if lt IE 8]>
	<link rel="stylesheet" type="text/css" href="<?php echo Yii::app()->request->baseUrl; ?>/css/ie.css" media="screen, projection" />
	<![endif]-->

	<link rel="stylesheet" type="text/css" href="<?php echo Yii::app()->request->baseUrl; ?>/css/main.css" />
	<link rel="stylesheet" type="text/css" href="<?php echo Yii::app()->request->baseUrl; ?>/css/form.css" />

	<title><?php echo CHtml::encode($this->pageTitle); ?></title>
</head>

<body>
<?php Yii::app()->clientScript->registerCoreScript('jquery');?>
<div class="header">
<?php
if ( !empty($this->mainMenu) ):
	echo CHtml::openTag("div", array("id"=>"mainMenu"));
	$this->widget('zii.widgets.CMenu',array(
		'items'=> $this->mainMenu
	));
	echo CHtml::closeTag("div");
endif;
?>

<?php
if ( $this->branchBased ):
	echo CHtml::openTag("div", array("id"=>"branchSelector"));
	$this->widget('ext.branchSelector.branchSelector', $this->branchSelectParams);
	echo CHtml::closeTag("div");
endif;
?>

<?php
if ( !empty($this->mainMenu) ):
	echo CHtml::openTag("div", array("id"=>"subMenu"));
	$this->widget('zii.widgets.CMenu',array(
		'items'=> $this->subMenu
	));
	echo CHtml::closeTag("div");
	echo CHtml::openTag("div", array("class"=>"clear"));
	echo CHtml::closeTag("div");
endif;
?>
</div>

<div class="container" id="page">

<!--	<div id="mainmenu">
		<?php $this->widget('zii.widgets.CMenu',array(
			'items'=>array(
				array('label'=>'Logout ('.Yii::app()->user->name.')', 'url'=>array('/site/logout'), 'visible'=>!Yii::app()->user->isGuest)
			),
		)); ?>
	</div><!-- mainmenu -->
	<?php if(isset($this->breadcrumbs)):?>
		<?php $this->widget('zii.widgets.CBreadcrumbs', array(
			'links'=>$this->breadcrumbs,
		)); ?><!-- breadcrumbs -->
	<?php endif?>

	<?php echo $content; ?>

	<div class="clear"></div>



</div><!-- page -->

</body>
</html>
