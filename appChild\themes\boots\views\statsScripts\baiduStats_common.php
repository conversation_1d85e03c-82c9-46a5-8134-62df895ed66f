<div style="display:none;">
<script type="text/javascript">
var _bdhmProtocol = (("https:" == document.location.protocol) ? " https://" : " http://");
document.write(unescape("%3Cscript src='" + _bdhmProtocol + "hm.baidu.com/h.js%3F6f3075fb3e7c4d750ce828023a439e7b' type='text/javascript'%3E%3C/script%3E"));
</script>
</div>

<script>
  //_hmt.push(['_trackPageview', '/<?php echo $this->getRoute();?>']);
  _hmt.push(['_trackEvent', 'ivyRoute', 'visit', '<?php echo $this->getRoute();?>']);
  var _isGuest = <?php echo intval(Yii::app()->user->isGuest);?>;
  _hmt.push(['_setCustomVar', 1, 'ivyGuest', _isGuest, 2]); 
</script>