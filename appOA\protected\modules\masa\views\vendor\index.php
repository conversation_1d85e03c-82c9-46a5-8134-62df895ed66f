<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Routines'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('site', 'ASA Management')?></li>
    </ol>

    <div class="row">
        <div class="col-md-2 col-sm-2 mb10">
        <?php
        $this->widget('zii.widgets.CMenu', array(
            'items' => $this->module->getMenu(),
            'id' => 'pageCategory',
            'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
            'activeCssClass' => 'active',
        ));
        ?>
        </div>
        <div class="col-md-10 col-sm-10">

            <div class="page-header">
                <h3><?php echo Yii::t("asa","Staffs and Vendors");?>
                    <small><?php echo Yii::t("asa","course providers and operational team");?></small>
                    <a class="btn btn-primary pull-right J_modal" href="<?php echo $this->createUrl('updateTeacher', array('vendor_id' => 0)); ?>">

                    <span class="glyphicon glyphicon-plus" aria-hidden="true"></span> <?php echo Yii::t("asa","Add Vendor");?></a>
                </h3>
            </div>

            <div class="mb10 row">
                <!-- 搜索框 -->
                <form  class="" style="float: left;width: 100%" action="<?php echo $this->createUrl('index'); ?>" method="get">
                    <?php echo Chtml::hiddenField('branchId',$this->branchId); ?>
                    <div class="col-sm-2 form-group">
                        <?php echo Chtml::dropDownList('teacherType', $_GET['teacherType'], array('1'=>'内部个人', '3'=>'外部个人', '2' => '外部公司'),array('class'=>'form-control',  'empty' => Yii::t('teaching', '教师类型'))); ?>
                    </div>
                    <div class="col-sm-2 form-group">
                        <?php echo Chtml::dropDownList('feeMerge', $_GET['feeMerge'], array('0'=>'不合并','1'=>'合并'),array('class'=>'form-control',  'empty' => Yii::t('teaching', '费用合并（IVY KG 使用）'))); ?>
                    </div>
                    <div class="">
                        <div class="">
                            <button class="btn btn-default ml5" type="submit"><span class="glyphicon glyphicon-search"> </span> </button>
                        </div>
                    </div>
                </form>
            </div>

            <?php
            $this->widget('ext.ivyCGridView.BsCGridView', array(
                'id' => 'course-grid',
                'afterAjaxUpdate' => 'js:function(){head.Util.modal();head.Util.ajaxDel()}',
                'dataProvider' => $teacherModel,
                'template' => "{items}{pager}",
                //状态为无效时标红
                'rowCssClassExpression' => '( $data->active == 0 ? "active" : "" )',
                'colgroups' => array(
                    array(
                        "colwidth" => array(100, 150, 150, 200, 200, 150, 200),
                    )
                ),
                'columns' => array(
                    array(
                        'name'=>'type',
                        'value'=>array($this,'getType'),
                        /*'value'=>'$data->getTypelist("type")',*/
                    ),
                    array(
                        'name' => Yii::t("asa","Vendor Name"),
                        'value' => '$data->getName()',
                    ),
                    'cellphone',
                    'email',
                    array(
                        'name' => Yii::t('asa', '银行信息'),
                        'value' => array($this, 'getBank'),
                    ),
                    array(
                        'name' => '费用合并',
                        'value' => '$data->fee_merge == 1 ? "合并" : "" ',
                    ),
                    array(
                        'name' => Yii::t('global', 'Action'),
                        'value' => array($this, 'getButton'),
                    ),
                ),

            ));
            ?>
        </div>
    </div>
</div>

<script>
    var modal = '<div class="modal fade" id="modal" class="1123" tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog" role="document"><div class="modal-content"></div></div></div>';
    $('body').append(modal);
    function cbCourse() {
        $('#modal').modal('hide');
        $.fn.yiiGridView.update('course-grid');
    }

    var type = '<?php echo json_encode($type['type'])?>';
    var job_type = '<?php echo json_encode($type['job_type'])?>';
    var teacherdata = [];
    var courseId = undefined;
    function cbTeacher() {
        //var groupId = '<?php echo $model->id; ?>';
        $('#modal').modal('hide');
        window.location.reload(true);
    }

</script>


<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>