<h4>Charts Theme</h4>
<div class="row">
    <?php foreach($themeMenu as $thekey=>$themem):?>
    <div class="col-md-2">
        <label class="checkbox-inline">
            <?php echo CHtml::image(Yii::app()->theme->baseUrl.'/images/'.$thekey.'.png', '', array('class'=>'img-responsive'));?>
            <div class="text-center"><input type="radio" name="theme" value="<?php echo $thekey?>" <?php echo $themem['active'] ? 'checked' : '' ?>> <?php echo $themem['label']?></div>
        </label>
    </div>
    <?php endforeach;?>
</div>

<?php Yii::app()->clientScript->registerCoreScript('cookie'); ?>
<script>
    $('input[name=theme]').change(function(){
        var thekey = 'chartstheme_<?php echo Yii::app()->user->id;?>';
        $.cookie(thekey, $(this).val(), {expires: 7, path: '/'});
    })
</script>