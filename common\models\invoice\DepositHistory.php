<?php

/**
 * This is the model class for table "ivy_child_deposit_history".
 *
 * The followings are the available columns in table 'ivy_child_deposit_history':
 * @property integer $did
 * @property integer $childid
 * @property integer $yid
 * @property double $amount
 * @property double $balance
 * @property string $inout
 * @property integer $tran_id
 * @property integer $timestamp
 * @property integer $create_uid
 * @property integer $ufida_status
 */
class DepositHistory extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return DepositHistory the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_child_deposit_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('yid, amount', 'required'),
			array('childid, yid, tran_id, timestamp, create_uid, ufida_status', 'numerical', 'integerOnly'=>true),
			array('amount, balance', 'numerical'),
			array('inout', 'length', 'max'=>3),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('did, childid, yid, amount, balance, inout, tran_id, timestamp, create_uid, ufida_status', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'calendarInfo' => array(self::BELONGS_TO,'Calendar','yid'),
			'invoiceTransaction' => array(self::HAS_ONE, 'InvoiceTransaction', array('id'=>'tran_id')),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'did' => 'Did',
			'childid' => 'Childid',
			'yid' => Yii::t("portfolio",'School Year'),
			'amount' => Yii::t("payment",'Amount'),
			'balance' => 'Balance',
			'inout' => 'Inout',
			'tran_id' => 'Tran',
			'timestamp' => Yii::t("payment",'Transaction Time'),
			'create_uid' => 'Create Uid',
			'ufida_status' => 'Ufida Status',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('did',$this->did);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('amount',$this->amount);
		$criteria->compare('balance',$this->balance);
		$criteria->compare('inout',$this->inout,true);
		$criteria->compare('tran_id',$this->tran_id);
		$criteria->compare('timestamp',$this->timestamp);
		$criteria->compare('create_uid',$this->create_uid);
		$criteria->compare('ufida_status',$this->ufida_status);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
	
	/**
	 * 取孩子某学年预交学费余额
	 * @param int $childId
	 * @param int $calendarId
	 * @return int $amount
	 * <AUTHOR>
	 */
	public function getChildDepositBalance($childId,$calendarId = 0)
	{
		$amount = 0;
		$depositIn = 0;
		$depositOut = 0;
		$criteria = new CDbCriteria;
		$criteria->select = 'amount,`inout`';
		$criteria->condition = 'childid = :childid';
		$criteria->params = array(':childid'=>$childId);
		if ($calendarId)
		{
			$criteria->compare('yid', $calendarId);
		}
		$obj = $this->findAll($criteria);
		if (is_array($obj) && count($obj))
		{
			foreach ($obj as $v)
			{
				if ($v->inout == 'in')
				{
					$depositIn += $v->amount;
				}
				else
				{
					$depositOut += $v->amount;		
				}
			}
		}
		$amount = $depositIn - $depositOut;
		return $amount;
	}

	/**
	 * 取孩子指定学校某学年预交学费余额
	 * @param int $childId
	 * @param int $calendarId
	 * @return int $amount
	 * <AUTHOR>
	 */
	public function getChildDepositBalanceBySchool($childId, $schoolid, $calendarId = 0)
	{
		Yii::import('common.models.invoice.InvoiceTransaction');
		$amount = 0;
		$depositIn = 0;
		$depositOut = 0;
		$criteria = new CDbCriteria;
		$criteria->select = 't.amount, t.`inout`';
		$criteria->compare('t.childid', $childId);
		$criteria->compare('invoiceTransaction.schoolid', $schoolid);
		$criteria->with = 'invoiceTransaction';
		if ($calendarId)
		{
			$criteria->compare('t.yid', $calendarId);
		}
		$obj = $this->findAll($criteria);
		if (is_array($obj) && count($obj))
		{
			foreach ($obj as $v)
			{
				if ($v->inout == 'in')
				{
					$depositIn += $v->amount;
				}
				else
				{
					$depositOut += $v->amount;		
				}
			}
		}
		$amount = $depositIn - $depositOut;
		return $amount;
	}
	
	public function renderInout($template=null){
		//to do , parse template
		$title = ($this->inout == "in") ? Yii::t("payment", "Refund to Tuition Deposit") : Yii::t("payment", "Use Tuition Deposit");
		return sprintf("<em class='inout-icon-%s' title='%s'></em>", $this->inout, $title);
	}

	public function renderSchoolYear($data)
	{
		$schoolYear = $data.'-'.($data+1);
		return $schoolYear;
	}
}