<?php

class ueditor_1_5_0 extends CInputWidget {
    
    /**
	 * Editor language
	 * Supports: zh-cn  or en
	 */
	public $language = 'zh-cn';
    
    /**
	 * Editor toolbars
	 * Supports: 
	 */
	public $toolbars = '';
    
    /**
	 * Html options that will be assigned to the text area
	 */
	public $htmlOptions = array();
    
	/**
	 * Editor options that will be passed to the editor
	 */
	public $editorOptions = array();
    
	/**
	 * Debug mode
	 * Used to publish full js file instead of min version
	 */
    public $debug = false;
    
    /**
	 * Editor theme
     * Supports: default
	 */
	public $theme = 'default';

    public $configFile = '';
    
    //自定义插件
    public $plugFile = '';
    
    // 班级id
    public $classId = '';
    /**
	 * Display editor
	 */
    public function run() {
	
		// Resolve name and id
		list($name, $id) = $this->resolveNameID();

		// Get assets dir
        $baseDir = dirname(__FILE__);
        $assets = Yii::app()->getAssetManager()->publish($baseDir.DIRECTORY_SEPARATOR.'ueditor_1_5_0', false, -1,YII_DEBUG);

		// Publish required assets
		$cs = Yii::app()->getClientScript();

        if($this->configFile){
            $cs->registerScriptFile($assets.'/'.$this->configFile.'.js');
        }
        else{
            $cs->registerScriptFile($assets.'/ueditor.config.js');
        }
        
		$jsFile = $this->debug ? 'ueditor.all.js' : 'ueditor.all.min.js';
		$cs->registerScriptFile($assets.'/' . $jsFile);
        
        if ($this->plugFile){
            $cs->registerScriptFile($assets.'/'.$this->plugFile.'.js');
        }
        
        $this->htmlOptions['id'] = $id;

        if($this->toolbars){
            $this->editorOptions['toolbars'][] = $this->toolbars;
            $this->editorOptions['serverUrl'][] = $assets.'/php/controller.php';
        }

		$options = CJSON::encode(array_merge(array('lang' => $this->language),$this->editorOptions));
		// Register js code
//		$cs->registerScript('Yii.'.get_class($this).'#'.$id, $js, CClientScript::POS_READY);
	
		// Do we have a model
		if($this->hasModel()) {
            $html = CHtml::activeTextArea($this->model, $this->attribute, $this->htmlOptions);
        } else {
            $html = CHtml::textArea($name, $this->value, $this->htmlOptions);
        }

		echo $html;

        $filePathFormat = Yii::app()->params['OAUploadBasePath'] . '/classNotes/' . $this->classId . '/{time}{rand:6}';
        $fileManagerListPath = Yii::app()->params['OAUploadBasePath'] . '/classNotes/' . $this->classId;
		$OAUploadBaseUrl = Yii::app()->params['OAUploadBaseUrl'] . '/classNotes/' . $this->classId . '/';
        $js =<<<EOP
        <script>
var ue{$id}=UE.getEditor('$id',$options);
ue{$id}.ready(function() {
    ue{$id}.execCommand('serverparam', {
    	// 文件保存路径
        'filePathFormat': '$filePathFormat',
        'imagePathFormat': '$filePathFormat',
        // 文件访问前缀
        'fileUrlPrefix': '$OAUploadBaseUrl',
        'imageUrlPrefix': '$OAUploadBaseUrl',
        // 列出指定目录下的文件
        'fileManagerListPath': '$fileManagerListPath',
        'imageManagerListPath': '$fileManagerListPath',
        // 文件访问路径前缀
        'fileManagerUrlPrefix': '$OAUploadBaseUrl',
        'imageManagerUrlPrefix': '$OAUploadBaseUrl',

    });
});
</script>
EOP;
        echo $js;
    }
    
}