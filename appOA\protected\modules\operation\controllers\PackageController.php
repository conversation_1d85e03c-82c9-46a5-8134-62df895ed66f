<?php

class PackageController extends ProtectedController
{
	/**
	 * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
	 * using two-column layout. See 'protected/views/layouts/column2.php'.
	 */
	public $layout='//layouts/column2';

	/**
	 * Creates a new model.
	 * If creation is successful, the browser will be redirected to the 'view' page.
	 */
	public function actionCreate()
	{
		$id = Yii::app()->request->getParam('id',null);
		if (empty($id))
		{
			$model = new IvyPPackage;
		}
		else
		{
			$model = $this->loadModel($id);
		}
		// Uncomment the following line if AJAX validation is needed
		// $this->performAjaxValidation($model);

		if(isset($_POST['IvyPPackage']))
		{
			$model->attributes=$_POST['IvyPPackage'];
			$model->update_timestamp = time();
			$model->create_userid = Yii::app()->user->id;
			if($model->save())
				$this->redirect(array('admin'));
		}

		$this->render('create',array(
			'model'=>$model,
		));
	}

	/**
	 * Deletes a particular model.
	 * If deletion is successful, the browser will be redirected to the 'index' page.
	 * @param integer $id the ID of the model to be deleted
	 */
	public function actionDelete($id)
	{
		if(Yii::app()->request->isPostRequest)
		{
			$exists = IvyPPackageLink::model()->exists('package_id=:package_id',array(':package_id'=>$id));
			if ($exists)
			{
				throw new CHttpException(400,'Not to be deleted.');
			}
			else
			{
				$this->loadModel($id)->delete();
			}
			// if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
			if(!isset($_GET['ajax']))
				$this->redirect(isset($_POST['returnUrl']) ? $_POST['returnUrl'] : array('admin'));
		}
		else
			throw new CHttpException(400,'Invalid request. Please do not repeat this request again.');
	}

	/**
	 * Manages all models.
	 */
	public function actionAdmin()
	{
		$model=new IvyPPackage('search');
		$model->unsetAttributes();  // clear any default values
		if(isset($_GET['IvyPPackage']))
			$model->attributes=$_GET['IvyPPackage'];

		$this->render('admin',array(
			'model'=>$model,
		));
	}

	/**
	 * Returns the data model based on the primary key given in the GET variable.
	 * If the data model is not found, an HTTP exception will be raised.
	 * @param integer the ID of the model to be loaded
	 */
	public function loadModel($id)
	{
		$model=IvyPPackage::model()->findByPk($id);
		if($model===null)
			throw new CHttpException(404,'The requested page does not exist.');
		return $model;
	}

	/**
	 * Performs the AJAX validation.
	 * @param CModel the model to be validated
	 */
	protected function performAjaxValidation($model)
	{
		if(isset($_POST['ajax']) && $_POST['ajax']==='ivy-ppackage-form')
		{
			echo CActiveForm::validate($model);
			Yii::app()->end();
		}
	}
	
	public function actionJoin()
	{
		$id = Yii::app()->request->getParam('id',null);
		$str = '';
		if (!empty($id))
		{
			$model = $this->loadModel($id);
			$productList = IvyPPackageLink::model()->getProduct($id);
			if (!empty($productList))
			{
				foreach ($productList as $value)
				{
					$str .= "<option value='$value->product_id'>".$value->Product->title."</option>";
				}
			}
		}
		else
		{
			throw new CHttpException(404,'The requested page does not exist.');
		}
		$this->renderPartial('join', array('model'=>$model,'str'=>$str));
	}
	
	public function actionSearchProduct()
	{
		$title = Yii::app()->request->getParam('title',null);
		if (!empty($title))
		{
			$product = IvyPProduct::model()->searchProduct($title);
			$str = '';
			if (!empty($product))
			{
				foreach ($product as $value)
				{
					$str .= "<option value='$value->id'>".$value->title."</option>";
				}
			}
			else
			{
				$str .= "<option value='0'>".Yii::t('operations', '没找到相关产品')."</option>";
			}
			echo $str;
		}
	}
	
	public function actionAssignProduct()
	{
		$packageId = Yii::app()->request->getParam('packageId',0);
		$productIds = Yii::app()->request->getParam('productIds',null);
		if ($packageId && $productIds)
		{
			$productIds = explode(',', $productIds);
			IvyPPackageLink::model()->deleteAll('package_id=:package_id',array(':package_id'=>$packageId));
			foreach ($productIds as $value)
			{
				$model = new IvyPPackageLink();
				$model->product_id = $value;
				$model->package_id = $packageId;
				$model->save();
			}
			$productList = IvyPPackageLink::model()->getProduct($packageId);
			if (!empty($productList))
			{
				$str = '';
				foreach ($productList as $value)
				{
					$str .= "<option value='$value->product_id'>".$value->Product->title."</option>";
				}
				echo $str;
			}
		}
	}
	
	public function actionDelProduct()
	{
		$packageId = Yii::app()->request->getParam('packageId',0);
		$productIds = Yii::app()->request->getParam('productIds',null);
		if ($packageId && $productIds)
		{
			$productIds = explode(',', $productIds);
			foreach ($productIds as $value)
			{
				 $model = IvyPPackageLink::model()->find('package_id=:package_id AND product_id=:product_id',array(':package_id'=>$packageId,':product_id'=>$value));
				 $model->delete();
			}
			$productList = IvyPPackageLink::model()->getProduct($packageId);
			if (!empty($productList))
			{
				$str = '';
				foreach ($productList as $value)
				{
					$str .= "<option value='$value->product_id'>".$value->Product->title."</option>";
				}
				echo $str;
			}
		}
	}
}
