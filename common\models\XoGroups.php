<?php

/**
 * This is the model class for table "ivy_groups".
 *
 * The followings are the available columns in table 'ivy_groups':
 * @property integer $groupid
 * @property string $name
 * @property string $description
 * @property string $group_type
 * @property integer $is_entire
 */
class XoGroups extends CActiveRecord
{
    const USERGROUP  = 2; // 注册用户组
    const STAFFGROUP = 4; // 员工组
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return XoGroups the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
	
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_groups';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('description, group_type', 'required'),
			array('is_entire', 'numerical', 'integerOnly'=>true),
			array('name', 'length', 'max'=>50),
			array('group_type', 'length', 'max'=>30),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('groupid, name, description, group_type, is_entire', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'groupid' => 'Groupid',
			'name' => 'Name',
			'description' => 'Description',
			'group_type' => 'Group Type',
			'is_entire' => 'Is Entire',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('groupid',$this->groupid);
		$criteria->compare('name',$this->name,true);
		$criteria->compare('description',$this->description,true);
		$criteria->compare('group_type',$this->group_type,true);
		$criteria->compare('is_entire',$this->is_entire);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}