<?php 
/*
 * <PERSON><PERSON>
* 2012-7-23
*
*/

Yii::import('ext.BinPower');
$binPower = new BinPower();
Mims::<PERSON>adHelper('HtoolKits');
$tk = new HtoolKits();
$config = Mims::LoadConfig('CfgParentSurvey');
$survey_binary_flag = $config['survey_binary_flag'];


$binPower->setPower( $topic->binary_flag);
$has_input = $binPower->isPower($survey_binary_flag['allow_feedback']);
$is_required = $binPower->isPower($survey_binary_flag['option_required']);

$f = new CFormatter();
?>
<div class="topic clearfix" id="topic_<?php echo $topic->id;?>"
	topic_id="<?php echo $topic->id;?>"
	<?php if(!empty($topic->option_group) && $is_required){
		echo 'is_required="true"';
	}else{echo 'is_required="false"';
	} ?>>

	<h3>
		<?php echo $topic->topicNum;?>
	</h3>

	<div class="item <?php echo $langKey;?>">
		<span class="warn"> <?php
		//请完成本题目后再提交，谢谢！
		echo Yii::t("survey", 'Please finish this topic before submit.');
		?>
		</span>
		<?php if(true === $this->topicDiglossia):?>
		<p class="title cn">
			<?php if(!empty($topic->option_group) && !$is_required):?>
			<span>（可选填）</span>
			<?php 	endif;?>
			<?php 	echo $f->formatNtext($topic->title_cn);?>
		</p>
		<p class="title en">
			<?php if(!empty($topic->option_group) && !$is_required):?>
			<span>(Optional) </span>
			<?php 	endif;?>
			<?php 	echo $f->formatNtext($topic->title_en);?>
		</p>
		<?php 	else:?>
		<p class="title <?php echo $langKey;?>">
			<?php if(!empty($topic->option_group) && !$is_required):?>
			<span> <?php
			//可选填
			echo Yii::t("survey", 'Optional');
			?>
			</span>
			<?php 	endif;?>
			<?php 	echo $f->formatNtext($tk->getContentByLang($topic->title_cn,$topic->title_en));?>
		</p>
		<?php 	endif;?>
		<div class="userinput">
			<ul>
				<?php 	if(!empty($topic->option_group) || !empty($has_fb)):?>
				<hr>
				<?php 	endif;?>

				<?php 	
				// 将渲染 选项的内容 独立出来
				$this->renderInternal($this_dir.'/topicOptionView.php',array('topic'=>$topic,'langKey'=>$langKey));
				
				?>
				<?php 	if($has_input):?>
				<p class="feedback">
					<span> <?php
					//附加注释：
					echo Yii::t("survey", 'Comment');
					?>
					</span> <br>
					<textarea name="feedback[<?php echo $topic->id;?>]"
						id="feedback_<?php echo $topic->id;?>" rows=2 cols=60></textarea>
				</p>
				<?php 	endif;?>
			</ul>
		</div>

	</div>
</div>
