<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Support'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('site', 'Parent Information') ?></li>
        <li class="active"><?php echo Yii::t('site', '结构管理') ?></li>
    </ol>
    <div class="row">
        <div class="col-md-2 col-sm-2">
            <?php
            $this->widget('zii.widgets.CMenu',array(
                'items'=> $this->getMenu(),
                'id'=>'visitConfiguration',
                'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked background-gray mb10'),
                'activeCssClass'=>'active',
                'itemCssClass'=>''
            ));
            ?>
        </div>
        <div class="col-md-10">
            <div class="mb10">
                <a href="<?php echo $this->createUrl('Edit', array('category'=>'grade'));?>" class="btn btn-primary J_modal" title="添加部门">
                    <span class="glyphicon glyphicon-plus"></span>
                    <?php echo Yii::t('site', 'Add category'); ?>
                </a>
                <a href="<?php echo $this->createUrl('Edit', array('category'=>'class'));?>" class="btn btn-primary J_modal" title="添加职位">
                    <span class="glyphicon glyphicon-plus"></span>
                    <?php echo Yii::t('site', 'Add sub-category'); ?>
                </a>
            </div>
            <?php
            if($structureObj):
                foreach($structureObj as $titleitem):
                    ?>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <div class="pull-left"><?php echo CommonUtils::autoLang($titleitem->title_cn, $titleitem->title_en)?></div>
                                    <div class="pull-right">
                                        <?php echo CHtml::link('<i class="glyphicon glyphicon-pencil"></i>',
                                            array('Edit', 'id'=>$titleitem->id, 'category' => 'grade'),
                                            array('class'=>'J_modal btn btn-info btn-xs', 'title'=>Yii::t('global', 'Edit')));?>
                                        <?php echo CHtml::link('<i class="glyphicon glyphicon-trash"></i>',
                                            array('gradeDel', 'id'=>$titleitem->id),
                                            array('class'=>'J_ajax_del btn btn-danger btn-xs', 'title'=>Yii::t('global', 'Delete')));?>
                                    </div>
                                    <div class="clearfix"></div>
                                </div>
                                <div class="panel-body">
                                    <?php
                                    if($items):
                                    foreach($items as $title):?>
                                        <?php if($titleitem->id == $title->pid): ?>
                                            <div class="mb10">
                                                <?php echo CHtml::link('<i class="glyphicon glyphicon-pencil"></i>',
                                                    array('Edit', 'id'=>$title->id, 'category' => 'class'),
                                                    array('class'=>'J_modal btn btn-info btn-xs', 'title'=>Yii::t('global', 'Edit')));?>
                                                <?php echo CHtml::link('<i class="glyphicon glyphicon-trash"></i>',
                                                    array('classDel', 'id'=>$title->id),
                                                    array('class'=>'J_ajax_del btn btn-danger btn-xs', 'title'=>Yii::t('global', 'Delete')));?>
                                                <?php echo CommonUtils::autoLang($title->title_cn, $title->title_en)?>
                                            </div>
                                        <?php endif; ?>
                                    <?php endforeach;
                                    endif;
                                    ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php
                endforeach;
            endif;
            ?>
        </div>
    </div>
</div>
<script>
    var modal = '<div class="modal fade" id="modal" tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog" role="document"><div class="modal-content"></div></div></div>';
    $('body').append(modal);
</script>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>