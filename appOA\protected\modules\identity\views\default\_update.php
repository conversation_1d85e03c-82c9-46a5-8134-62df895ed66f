<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
    </button>
    <h4 class="modal-title" id="exampleModalLabel">更改卡信息</h4>
</div>
<?php
$form = $this->beginWidget('CActiveForm', array(
    'id' => 'updatedCards',
    'enableAjaxValidation' => false,
    'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form', 'enctype'=>"multipart/form-data"),
)); ?>
<div class="modal-body">
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'cid'); ?></label>
        <div class="col-xs-8">
            <div><?php echo $model->cid ?></div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'childid'); ?></label>
        <div class="col-xs-8">
            <div><?php
                $data = json_decode($model->data);
                echo $data->child ?></div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'updated'); ?></label>
        <div class="col-xs-8">
            <div><?php echo date("Y-m-d", $model->updated) ?></div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'sign'); ?></label>
        <div class="col-xs-8">
            <?php echo $form->DropDownList($model, 'sign', Cards::getConfig(), array('class' => 'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'uploadFile'); ?></label>
        <div class="col-xs-8">
            <?php echo $form->fileField($model, 'uploadFile'); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"></label>
        <div class="col-xs-8">
            <img style="width:200px;" src="<?php echo Yii::app()->params['OAUploadBaseUrl'] . '/crads/' . $data->photo  . '?' . time(); ?>">
        </div>
    </div>

</div>
<div class="modal-footer">
    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
</div>
<?php $this->endWidget(); ?>
