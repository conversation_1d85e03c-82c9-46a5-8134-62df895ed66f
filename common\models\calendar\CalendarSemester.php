<?php

/**
 * This is the model class for table "ivy_calendar_semester".
 *
 * The followings are the available columns in table 'ivy_calendar_semester':
 * @property integer $sid
 * @property integer $yid
 * @property integer $semester_flag
 * @property integer $staff_start_timestamp
 * @property integer $staff_end_timestamp
 * @property integer $school_start_timestamp
 * @property integer $school_end_timestamp
 */
class CalendarSemester extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CalendarSemester the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_calendar_semester';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('yid, semester_flag', 'required'),
			array('yid, semester_flag, staff_start_timestamp, staff_end_timestamp, school_start_timestamp, school_end_timestamp', 'numerical', 'integerOnly'=>true),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('sid, yid, semester_flag, staff_start_timestamp, staff_end_timestamp, school_start_timestamp, school_end_timestamp', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'assignSchool' => array(self::HAS_MANY, 'CalendarSchool', array('yid'=>'yid')),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'sid' => 'Sid',
			'yid' => 'Yid',
			'semester_flag' => 'Semester Flag',
			'staff_start_timestamp' => 'Staff Start Timestamp',
			'staff_end_timestamp' => 'Staff End Timestamp',
			'school_start_timestamp' => 'School Start Timestamp',
			'school_end_timestamp' => 'School End Timestamp',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('sid',$this->sid);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('semester_flag',$this->semester_flag);
		$criteria->compare('staff_start_timestamp',$this->staff_start_timestamp);
		$criteria->compare('staff_end_timestamp',$this->staff_end_timestamp);
		$criteria->compare('school_start_timestamp',$this->school_start_timestamp);
		$criteria->compare('school_end_timestamp',$this->school_end_timestamp);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * 查询校历中上下学年的结束日期
	 * @param iint $calendar_id
	 * @return array
	 * <AUTHOR>
	 * <AUTHOR> updated 2016-5-10
	 */
	public function getSemesterTimeStamp($calendar_id = 0){
		$ret = array();
		if($calendar_id){
			Yii::import('common.models.calendar.Calendar');
			$model = Calendar::model()->findByPk($calendar_id);
			$timepoints = explode(',', $model->timepoints);
			$ret['fall_start'] = $timepoints[0];
			$ret['fall_end'] = $timepoints[1];
			$ret['spring_start'] = $timepoints[2];
			$ret['spring_end'] = $timepoints[3];
		}
		return $ret;

		/**
		$crite = new CDbCriteria;
		$crite->compare('yid', $calendar_id);
		$crite->select = 'semester_flag,school_start_timestamp,school_end_timestamp';
		$calendarSemesterArr = $this->findAll($crite);
		$calendarSemesterList = array();
		if (is_array($calendarSemesterArr) && count($calendarSemesterArr)){
			foreach ($calendarSemesterArr as $csa){
				if ($csa->semester_flag == 10)
				{
					$calendarSemesterList["fall_start"] = $csa->school_start_timestamp;
					$calendarSemesterList["fall_end"] = $csa->school_end_timestamp;
				}
				else
				{
					$calendarSemesterList["spring_start"] = $csa->school_start_timestamp;
					$calendarSemesterList["spring_end"] = $csa->school_end_timestamp;
            	}
            }
		}
        return $calendarSemesterList;
		 */
	}
	
	public function getCalendarRange($calendarIds=array()){
		if(!is_array($calendarIds)) $calendarIds = array(intval($calendarIds));
		$result = array();
		$calendarRanges = Yii::app()->db->createCommand()
			->select('yid, MIN(`school_start_timestamp`) AS starttime, MAX(`school_end_timestamp`) AS endtime')
			->from($this->tableName())
			->where(array('in','yid',$calendarIds))
			->group('yid')
			->queryAll();
		if(!empty($calendarRanges)){
			foreach($calendarRanges as $cr){
				$result[$cr['yid']]=$cr;
			}
		}
		return $result;
	}
}