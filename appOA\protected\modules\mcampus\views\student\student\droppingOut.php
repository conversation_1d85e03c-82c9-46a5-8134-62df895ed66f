<?php
Yii::import('common.models.invoice.*');
$invoiceType = array('tuition');

Yii::import('common.models.invoice.*');
$type= Yii::app()->request->getParam('t', '');
$basicModel = ChildProfileBasic::model();
$criteria = new CDbCriteria;
$criteria->compare('status', '=' . ChildProfileBasic::STATS_DROPPINGOUT);
$criteria->compare('schoolid', $this->branchId);

if($type == 'expired'){
    $criteria->compare('est_quit_date', '<'.time());
}
if($type == 'not_expired'){
    $criteria->compare('est_quit_date','>='. time());
}

$criteria->group ='updated_timestamp desc,childid desc';
$dataProvider = new CActiveDataProvider('ChildProfileBasic', array(
    'criteria' => $criteria,
    'pagination' => array(
        'pageSize' => 20,
    ),
));
$criteria = new CDbCriteria;
$criteria->compare('status', '=' . ChildProfileBasic::STATS_DROPPINGOUT);
$criteria->compare('schoolid', $this->branchId);
$all_total = $basicModel->count($criteria);

$criteria = new CDbCriteria;
$criteria->compare('status', '=' . ChildProfileBasic::STATS_DROPPINGOUT);
$criteria->compare('schoolid', $this->branchId);
$criteria->compare('est_quit_date', '<'.time());
$expired_total = $basicModel->count($criteria);

$criteria = new CDbCriteria;
$criteria->compare('status', '=' . ChildProfileBasic::STATS_DROPPINGOUT);
$criteria->compare('schoolid', $this->branchId);
$criteria->compare('est_quit_date', '>='.time());
$not_expired_total = $basicModel->count($criteria);


$profileMenu = array(
    'all' => array(
        'label' => Yii::t('global','All')."($all_total)",
        'url' => array('//mcampus/student/index', "category"=>"droppingOut"),
        'active'=>empty($type) ?true: false
    ),
    'expired' => array(
        'label' => '已过预计退学时间 '."($expired_total)",
        'url' => array('//mcampus/student/index', "category"=>"droppingOut","t" => "expired"),
        'active'=>$type==='expired' ? true: false
    ),
    'not_expired' => array(
        'label' => '未过预计退学时间 '."($not_expired_total)",
        'url' => array('//mcampus/student/index', "category"=>"droppingOut","t" => "not_expired"),
        'active'=>$type ==='not_expired' ? true: false
    ),
);
// var_dump($dataProvider);die();
?>


<div class="col-md-10">
    <div class="row mb15">
        <div class="col-md-12">
            <h1 class="flex align-items font24 fontBold mt20">
                <span class="dropdown text-primary">
                    <a data-toggle=dropdown href="#"><span class="glyphicon glyphicon-list glyphiconList"></span></a>
                    <ul class="dropdown-menu">
                        <li>
                            <a onclick="exportChildInfo(this)" href="javascript:void(0)">
                                <span class="glyphicon glyphicon-export"></span> <?php echo Yii::t('site', 'Export Name List'); ?>
                            </a>
                        </li>
                    </ul>
                </span>
                <span class='ml12'><?php echo Yii::t('site', 'Dropping Out'); ?></span>
                <span class="label label-default ml8" id="total-number"></span>
            </h1>
        </div>
    </div>
    <div class="col-md-12 mb10" style="padding-left: 0">
        <?php
        $this->widget('zii.widgets.CMenu', array(
            'items' => $profileMenu,
            'id' => 'pageCategory',
            'htmlOptions' => array('class' => 'nav nav-tabs'),
            'activeCssClass' => 'active',
        ));
        ?>
    </div>
    <div class="row">
        <div class="col-md-12">
            <?php
            $this->widget('ext.ivyCGridView.BsCGridView', array(
                'id' => 'child-list',
                'dataProvider' => $dataProvider,
                'template' => "{items}{pager}",
                'colgroups' => array(
                    array(
                        "colwidth" => array(80, 150,120, 50, 120, 120, 120, 120),
                    )
                ),
                'columns' => array(
                    'childid' => array(
                        'name' => 'childid',
                        'header' => Yii::t("labels", "ID"),
                    ),
                    'name' => array(
                        'type' => 'raw',
                        'header' => Yii::t('child', 'Name'),
                        'value' => 'CHtml::link($data->getChildName(), array("//child/index/index","childid"=>$data->childid), array("target"=>"_blank"))',
                    ),
                    'class'=>array(
                        'name'=>'classid',
                        'header'=>Yii::t('child', 'Class'),
                        'value'=>'OA::getLastClassTitle($data->childid)',
                    ),
                    'gender' => array(
                        'name' => 'gender',
                        'value' => 'OA::renderChildGender($data->gender)',
                    ),
                    'age' => array(
                        'name' => 'birthday',
                        'header' => Yii::t('reg', 'Age'),
                        'value'=>'CommonUtils::getAge($data->birthday)'
                    ),

                    'credit' => array(
                        'name' => 'credit',
                    ),
                    'regdate' => array(
                        'name' => 'updated_timestamp',
                        'header' => Yii::t('global', 'Update Time'),
                        'value' => 'OA::formatDateTime($data->updated_timestamp)',
                    ),
                    'expectedquitdate' => array(
                        'name' => 'est_quit_date',
                        'header' => Yii::t('child', 'Estimated Leave Date'),
                        'value' => 'OA::formatDateTime($data->est_quit_date)',
                    ),
//                    'quitdate' => array(
//                        'name' => '退学操作日期',
//                        'value' => 'OA::formatDateTime($data->stat->updated_timestamp)',
//                    )
                )
            ));
            ?>
        </div>
    </div>
</div>


<script>
    // 导出孩子列表
    exportChildInfo = function (obj) {
        var url = "<?php echo $this->createUrl('//mcampus/student/exportChildInfo', array('category' => $category)); ?>";
        $.ajax({
            url: url,
            type: 'POST',
            // dataType: 'json',
            data: {},
            success: function (res) {
                res = eval("(" + res + ")");
                if (res.state == 'success') {
                    var data = res.data.items;
                    const filename = res.data.title;
                    const ws_name = "SheetJS";

                    const worksheet = XLSX.utils.aoa_to_sheet(data);
                    const workbook = XLSX.utils.book_new();
                    XLSX.utils.book_append_sheet(workbook, worksheet, ws_name);
                    // generate Blob
                    const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                    const blob = new Blob([wbout], {type: 'application/octet-stream'});
                    // save file
                    let link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    link.download = filename;
                    link.click();
                    setTimeout(function () {
                        // 延时释放掉obj
                        URL.revokeObjectURL(link.href);
                        link.remove();
                    }, 500);
                }
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                // alert(XMLHttpRequest.readyState + XMLHttpRequest.status + XMLHttpRequest.responseText);
            },
        }).done(function (data, textStatus, jqXHR) {
            // console.log(jqXHR.status);
        });
    }
</script>
