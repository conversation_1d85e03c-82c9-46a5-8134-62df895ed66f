<?php

class GlobalController extends ProtectedController
{
    public $actionAccessAuths = array(
        'discount'          => 'o_X_Access',
        'GetDiscounts'      => 'o_X_Access',
        'AddDiscount'       => array('access'=>'o_X_Access', 'admin'=>'o_X_Adm_Common'),
        'DiscountCate'      => 'o_X_Access',
        'AddDiscountCate'   => array('access'=>'o_X_Access', 'admin'=>'o_X_Adm_Common'),
        'DelDiscountCate'   => 'o_X_Adm_Common',
    );

    public $dialogWidth = 400;

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'hqOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','HQ Operations');
    }

    /**
     * 折扣主页面
     */
    public function actionDiscount()
    {
        Yii::import('common.models.invoice.*');
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');

        $this->render('discount');
    }

    /**
     * 显示制定学校的折扣
     * @param $branchId
     */
    public function actionGetDiscounts($branchId){
        Yii::import('common.models.invoice.*');
        $crit = new CDbCriteria();
        $crit->compare('schoolid', $branchId);
        $crit->order = '`discount` DESC';
        $discountModels = DiscountSchool::model()->with(array('discountTitle','approver'))->findAll($crit);
        $return = array();
        foreach($discountModels as $discount){
            $return[]=array(
                'info' => $discount->attributes,
                'expire' => OA::formatDateTime($discount->expire_date),
                'title' => $discount->discountTitle->attributes,
                'approver'=>array(
                    'name' => is_null($discount->approver)? '' : $discount->approver->getName(),
                    'uid' => is_null($discount->approver)? null : $discount->approver->uid
                )
            );
        }

        $this->addMessage('state', 'success');
        $this->addMessage('data', $return);
        $this->showMessage();
    }

    /**
     * 添加折扣页面和提交处理
     * @param int $id
     * @param string $schoolid
     */
    public function actionAddDiscount($id=0, $schoolid='')
    {
        $this->layout='//layouts/dialog';
        $this->dialogWidth = 500;
        Yii::import('common.models.invoice.DiscountCategory');
        Yii::import('common.models.invoice.DiscountSchool');

        $model = DiscountSchool::model()->findByPk($id);
        if($model===null){
            $model = new DiscountSchool();
            $model->schoolid=$schoolid;
        }

        if(isset($_POST['DiscountSchool'])){
            $model->attributes = $_POST['DiscountSchool'];
            $model->stat = isset($_POST['DiscountSchool']['stat']) && $_POST['DiscountSchool']['stat'] ? 10 : 90;
            $model->expire_date = strtotime($model->expire_date);
            $model->is_sentmail = $model->mailto ? 1 : 0;
            $model->allow_bind2child = 1;
            $model->updated_timestamp = time();
            $model->userid = Yii::app()->user->id;
            if($model->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','Data saved!'));
                $this->addMessage('callback', 'callback');
                $this->addMessage('data', array('branchid'=>$schoolid));
            }
            else{
                $this->addMessage('state', 'fail');
                $errs = current($model->getErrors());
                $this->addMessage('message', $errs?$errs[0]:Yii::t('message','Saving Failed!'));
            }
            $this->showMessage();
        }

        $criteria = new CDbCriteria();
        $criteria->order='updated_timestamp desc';
        $items = DiscountCategory::model()->findAll($criteria);
        $model->expire_date = OA::formatDateTime($model->expire_date);
        $data = $model->approver !== null ? array($model->approver->uid=>$model->approver->getName()) : array();
        $this->render('adddiscount', array('model'=>$model, 'items'=>$items, 'data'=>$data));
    }

    /**
     * 折扣类别主页面
     */
    public function actionDiscountCate()
    {
        Yii::import('common.models.invoice.DiscountCategory');

        $criteria = new CDbCriteria();
        $criteria->order='updated_timestamp desc';
        $items = DiscountCategory::model()->findAll($criteria);
        $this->render('discountcate', array('items'=>$items));
    }

    /**
     * 添加折扣类别页面和提交处理
     * @param int $id
     */
    public function actionAddDiscountCate($id=0)
    {
        $this->layout='//layouts/dialog';
        $this->dialogWidth = 500;
        Yii::import('common.models.invoice.DiscountCategory');
        $model = DiscountCategory::model()->findByPk($id);
        if($model===null){
            $model = new DiscountCategory();
        }

        if(isset($_POST['DiscountCategory'])){
            $model->attributes = $_POST['DiscountCategory'];
            $model->updated_timestamp = time();
            $model->userid = Yii::app()->user->id;
            if($model->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('refresh', true);
                $this->addMessage('message', Yii::t('message','Data saved!'));
            }
            else{
                $this->addMessage('state', 'fail');
                $errs = current($model->getErrors());
                $this->addMessage('message', $errs?$errs[0]:Yii::t('message','Saving Failed!'));
            }
            $this->showMessage();
        }

        $this->render('adddiscountcate', array('model'=>$model));
    }

    /**
     * 删除折扣类别
     */
    public function actionDelDiscountCate()
    {
        if(Yii::app()->request->isPostRequest){
            Yii::import('common.models.invoice.DiscountCategory');
            Yii::import('common.models.invoice.DiscountSchool');
            $id = Yii::app()->request->getParam('id', 0);

            if(DiscountSchool::model()->countByAttributes(array('category_id'=>$id))){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '删除失败，学校已经使用此折扣！');
            }
            else{
                if(DiscountCategory::model()->deleteByPk($id)){
                    $this->addMessage('state', 'success');
                    $this->addMessage('refresh', true);
                    $this->addMessage('message', '删除成功！');
                }
                else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '删除失败！');
                }
            }
            $this->showMessage();
        }
    }
}