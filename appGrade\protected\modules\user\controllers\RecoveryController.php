<?php

class RecoveryController extends Controller {

    public $defaultAction = 'recovery';
    public $layout = '//layouts/auth.login';

    /**
     * Recovery password
     */
    public function actionRecovery() {
		$this->setSubPageTitle(Yii::t("user","Password Recovery"));
        $form = new UserRecoveryForm;
        if (Yii::app()->user->id) {
            $this->redirect(Yii::app()->controller->module->returnUrl);
        } else {
            $email = ((isset($_GET['email'])) ? $_GET['email'] : '');
            $activkey = ((isset($_GET['activkey'])) ? $_GET['activkey'] : '');
            if ($email && $activkey) {
                $form2 = new UserChangePassword;
                $find = User::model()->findByAttributes(array('email' => $email));
                if (isset($find) && substr($find->pass, 3, 6) == $activkey) {
                    if (isset($_POST['UserChangePassword'])) {
                        $form2->attributes = $_POST['UserChangePassword'];
                        if ($form2->validate()) {
                            $find->pass = Yii::app()->controller->module->encrypting($form2->password);
                            if ($find->save()) {
                                Yii::app()->user->setFlash('success', Yii::t("user", "New password saved! Please use your account login."));
                                $this->redirect(Yii::app()->controller->module->loginUrl);
                            }
                        }
                    }
                    $this->layout = '//layouts/auth.blank';
                    $this->render('changepassword', array('form' => $form2));
                } else {
                    Yii::app()->user->setFlash('recoveryMessage', Yii::t("user", "Incorrect recovery link."));
                    $this->redirect(Yii::app()->controller->module->recoveryUrl);
                }
            } else {
                if (isset($_POST['UserRecoveryForm'])) {
                    $form->attributes = $_POST['UserRecoveryForm'];
                    if ($form->validate()) {
                        $user = User::model()->active()->findbyPk($form->user_id);
                        $authcode = substr($user->pass, 3, 6);
                        $activation_url = $this->createUrl(implode(Yii::app()->controller->module->recoveryUrl), array("activkey" => $authcode, "email" => $user->email));

                        // 找回密码
                        //邮件发送给要找回密码的帐号EMAIL，若家长回复则回复至校园support
                        $sendEmail = $this->widget('ext.sendEmail.Send', array(
                            'viewName' => 'lostPassword',
                            'toParty' => true, //发送至 当事人 -参数 party_email
                            'toParent' => false, //发送至 父母 -
                            'toSupport' => false, //发送至 校园支持 -参数 support_email
                            'ccSpouse' => false, //抄送至 配偶 -
                            'ccParent' => false, //抄送至 父母 -
                            'ccSupport' => false, //抄送至 校园支持 -参数 support_email
                            'bccItDev' => true, // 密抄至 ItDev -
                            'replyToSupport' => true, // 回复至 校园支持 -参数 support_email
                            'params' => array(
                                'party_email' => $user->email,
                                'support_email' => '<EMAIL>',
                                'user_id' => $form->user_id,
                                'activation_url' => $activation_url,
                                'date_time' => time(),
                            ),
                                ));

                        Yii::app()->user->setFlash('recoveryMessage', Yii::t("user", "Instruction on how to recover your password has been sent to your email address.  Please check your mailbox."));
                        $this->refresh();
                    }
                }
                $this->render('recovery', array('form' => $form));
            }
        }
    }

}