<div class="bd" style="height: 100%;">
    <?php if ($schoolObj->type == 50):?>
    <div class="weui_cells_title"><?php echo Yii::t('portfolio', 'Lunch Menu'); ?></div>
    <?php endif;?>
    <div class="weui_tab">

        <?php if ($schoolObj->type != 50):?>
        <div class="weui_cells_title"><?php echo Yii::t('navigations', 'Cancel Lunch'); ?></div>
        <?php endif;?>

        <div class="">
            <div class="weui_cells" style="margin-top: 0">
                <?php
                //选择孩子
                $this->widget('ext.wechat.ChildSelector', array(
                    'childObj' => $childObj,
                    'myChildObjs' => $this->myChildObjs,
                    'jumpUrl' => 'cateringMenu',
                ));
                ?>
                <?php if ($childObj->status < 100): ?>
                <?php if ($schoolObj->type != 50):?>
                <div class="tab1" >
                    <div class="weui_cells_form">
                        <?php if (empty($invoiceDays)): ?>
                            <div class="msg">
                                <div class="weui_msg">
                                    <div class="weui_icon_area"><i class="weui_icon_msg weui_icon_info"></i></div>
                                    <div class="weui_text_area">
                                        <p class="weui_msg_desc"><?php echo Yii::t('lunch', 'Your child currently does not subscribe to our lunch service.'); ?></p>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                        <?php foreach ($invoiceDays as $invoiceDay): ?>
                            <div class="weui_cell weui_cell_switch">
                                <div
                                    class="weui_cell_hd weui_cell_primary"><?php echo date('Y/m/d', strtotime($invoiceDay)); ?></div>
                                <div class="weui_cell_ft">
                                    <input type="checkbox" class="weui_switch"
                                           value="<?php echo $invoiceDay; ?>"<?php if (!in_array($invoiceDay, $refundLunchDays)) {
                                        echo 'checked';
                                    } ?>/>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif;?>
                <div class="tab2" <?php if ($schoolObj->type != 50):?>style="display: none"<?php endif;?>>
                    <div class="weui_cells_access">
                        <?php foreach ($schedule as $week => $menu): ?>
                            <a href="<?php echo $this->createUrl('cateringMenuDetail', array('commonId' => $menu['common'], 'allergyId' => $menu['allergy'])); ?>"
                               class="weui_cell js_grid" data-id="article">
                                <div class="weui_cell_bd weui_cell_primary">
                                    <p>Week <?php echo str_pad($week, 2, 0, STR_PAD_LEFT); ?></p>
                                </div>
                                <div class="weui_cell_ft"><?php echo date('m/d', $weekDays[$week]); ?>
                                    - <?php echo date('m/d', $weekDays[$week] + 4 * 24 * 3600); ?></div>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <?php else: ?>
                <div class="msg">
                    <div class="weui_msg">
                        <div class="weui_icon_area"><i class="weui_icon_msg weui_icon_info"></i></div>
                        <div class="weui_text_area">
                            <p class="weui_msg_desc"><?php echo Yii::t('wechat', 'Your child is not in a state of reading.'); ?></p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
    // tab 切换
    $('#checkTab1').on('click', function () {
        $('#checkTab2').addClass('weui_bar_item_on');
        $(this).removeClass('weui_bar_item_on');
        $('.tab1').show();
        $('.tab2').hide();
    });
    $('#checkTab2').on('click', function () {
        $('#checkTab1').addClass('weui_bar_item_on');
        $(this).removeClass('weui_bar_item_on');
        $('.tab2').show();
        $('.tab1').hide();
    });

    // 取消用餐相关
    $('.weui_cells_form .weui_cell_ft input').on('change', function (e) {
        var operate = 'DO';
        var inputObj = $(this);
        if (inputObj.is(':checked')) {
            operate = 'UNDO';
        }
        var day = inputObj.val();
        $('#loadingToast').show();

        $.ajax({
            type: 'POST',
            url: '<?php echo $this->createUrl('cancelLunch', array('childid'=>$this->getChildId())); ?>',
            data: {day: day, operate: operate},
            dataType: 'JSON',
            timeout: 10000,
            success: function (data) {
                data = eval('(' + data + ')');
                if (data.state == 'success') {
                    showMessage('<?php echo Yii::t('global', 'Success!'); ?>');
                } else {
                    inputToggle(inputObj);
                    showTips('<?php echo Yii::t('global', 'Failed!'); ?>', data.message);
                }
            },
//            error: function () {
//                inputToggle(inputObj);
//                showTips('<?php //echo Yii::t('global', 'Failed!'); ?>//', '<?php //echo Yii::t('global', '服务器错误'); ?>//');
//            },
            complete: function () {
                $('#loadingToast').hide();
            }
        });
    });

    //切换input的选中状态
    function inputToggle(inputObj) {
        if (inputObj.is(':checked')) {
            inputObj.prop('checked', false);
        } else {
            inputObj.prop('checked', true);
        }
    }
</script>
