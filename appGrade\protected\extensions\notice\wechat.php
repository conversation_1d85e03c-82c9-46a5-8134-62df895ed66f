<?php
class Wechat extends CWidget{    	
	public function run(){
		Yii::import('application.modules.weixin.models.*');
		//$wechatUser = WechatUser::model()->findByAttributes(array('userid'=>Yii::app()->user->getId(), 'valid'=>1));
		//if(is_null($wechatUser)){
			Yii::app()->clientScript->registerCssFile(Yii::app()->theme->baseUrl . "/css/notice.wechat.css?t=" . Yii::app()->params['refreshAssets']);
			$this->render('wechat');
		//}
	}
}