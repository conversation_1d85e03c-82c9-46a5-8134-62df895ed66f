<?php
if(!defined("IVY_POLICY"))
throw new CException('DO NOT USE THIS FILE DIRECTLY');

return array( 'policy'=>array(

	//是否老生老办法
	ENABLE_CONSTANT_TUITION => true,

	//年保育费
	//ANNUAL_CHILDCARE_AMOUNT => 8000,

	//是否开放课外活动账单
	ENABLE_AFTERSCHOOLS => true,

	//图书馆工本费
	FEETYPE_LIBCARD => 10,

	//学期四个时间点；取前后两端
	TIME_POINTS   => array(201509,201512,201601,201607),

	//寒暑假月份，及其每月计费天数
	//HOLIDAY_MONTH => array(201402=>20,201408=>20),

	//年付开通哪些缴费类型
	PAYGROUP_ANNUAL => array(
		ITEMS => array(
			FEETYPE_TUITION,
			FEETYPE_LUNCH,
			FEETYPE_SCHOOLBUS,
		),
        EXCEPTS => array(
            OTHER_PROGRAM_CHN
        ),
		TUITION_CALC_BASE => BY_MONTH,
	),

	//学期付开通哪些缴费类型
	PAYGROUP_SEMESTER => array(
		ITEMS => array(
			FEETYPE_TUITION,
			FEETYPE_LUNCH,
			FEETYPE_SCHOOLBUS,
		),
        EXCEPTS => array(
            OTHER_PROGRAM_CHN
        ),
		TUITION_CALC_BASE => BY_MONTH,
	),

	//月份开通哪些缴费类型
	PAYGROUP_MONTH => array(
		ITEMS => array(
			FEETYPE_TUITION,
			FEETYPE_LUNCH,
			FEETYPE_SCHOOLBUS,
		),
		TUITION_CALC_BASE => BY_MONTH,
	),

	//计算基准：BY_MONTH, 或者 BY_SETTING, 前者表示所有的学费都基于月费用计算，后者表示所有的学费都基于学期或学年
	TUITION_CALCULATION_BASE => array(

		BY_MONTH => array(

			//月收费金额
			AMOUNT => array(
                HALF5D => 2900,
				OTHER_PROGRAM_CHN => 650,
                PROGRAM_IBS => 5050,
                PROGRAM_IA => 7065,
			),

			//收费月份，及其权重
			MONTH_FACTOR => array(
				201509=>1,
				201510=>1,
				201511=>1,
				201512=>1,
				201601=>1,
				201602=>.6,
				201603=>1,
				201604=>1,
				201605=>1,
				201606=>1,
				201607=>1
			),

			//折扣
			GLOBAL_DISCOUNT => array(
				DISCOUNT_ANNUAL => '0.95:1:2015-12-02',
				DISCOUNT_SEMESTER1 => '0.97:1:2015-11-02',
				DISCOUNT_SEMESTER2 => '0.97:1:2016-06-02'
			)

		),

		//东湖配置实例

		BY_ANNUAL => array(
			AMOUNT => array(
                HALF5D => 29200,
                PROGRAM_IBS => 50850,
                PROGRAM_IA => 71150,
			),
		),

		BY_SEMESTER1 => array(
			AMOUNT => array(
                HALF5D => 11250,
                PROGRAM_IBS => 19600,
                PROGRAM_IA => 27410,
			),
		),

		BY_SEMESTER2 => array(
			AMOUNT => array(
                HALF5D => 18570,
                PROGRAM_IBS => 32330,
                PROGRAM_IA => 45230,
			),
		),

	),

	//每餐费用
	LUNCH_PERDAY => 25,

	//账单到期日和账单开始日之差
	DUEDAYS_OFFSET => 1,

    //代金卷金额
    //VOUCHER_AMOUNT => 500,

));

?>