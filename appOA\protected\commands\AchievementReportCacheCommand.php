<?php

class AchievementReportCacheCommand extends CConsoleCommand {
    public $batchNum = 200;
    public function init()
    {
    	Yii::import('common.models.timetable.*');
        Yii::import('common.models.secondary.*');
    }

    // 课表时间安排  TimetableTimes
    public function actionSaveCache($yid,$schoolid)
    {
        $criteria = new CDbCriteria;
        $criteria->compare('calendar', $yid);
        $criteria->compare('schoolid', $schoolid);
        $criteria->index = 'id';
        $reportModel = AchievementReport::model()->findAll($criteria);

        $criteria = new CDbCriteria;
        $criteria->compare('yid', $yid);
        $criteria->compare('schoolid', $schoolid);
        // $criteria->compare('status', 1);
        $timeTbaleModel = Timetable::model()->find($criteria);

        $data = array();
        if($reportModel && $timeTbaleModel){
            $crit = new CDbCriteria;
            $crit->compare('status', 1);
            $coursecodeModel = TimetableCourses::model()->findAll($crit);

            foreach ($coursecodeModel as $k=>$courseCode){
                $frequencys = array();
                $scoreNum = array();

                // 课程总分
                $criteria = new CDbCriteria;
                $criteria->compare('timetable_records_code', $courseCode->course_code);
                $criteria->compare('calender_id', $yid);
                $childTotal = AchievementReportChildTotal::model()->findAll($criteria);

                // 拿到总分的数据的数组
                $totle = array();
                if ($childTotal) {
                    foreach ($childTotal as $val) {
                        if ($val->courseScoresid) {
                            $totle['score'] += $val->oprion_value;
                            $totle['num'] += 1;
                            $totle['fractionNum'][$val->oprion_value] += 1;
                        }
                    }
                }


                $childNum = array();
                $crit = new CDbCriteria;
                $crit->compare('timetable_records_code', $courseCode->course_code);
                $crit->compare('report_id', array_keys($reportModel));
                $total = AchievementReportChildFraction::model()->count($crit);

                $cycle = ceil($total/$this->batchNum);
                for($i=0; $i<$cycle; $i++) {
                    $criteria->limit = $this->batchNum;
                    $criteria->offset = $i * $this->batchNum;
                    $childFractionModel = AchievementReportChildFraction::model()->findAll($crit);
                    if ($childFractionModel) {
                        // 拿到整合数据的数组
                        foreach ($childFractionModel as $val) {
                            if ($val->courseScoresid) {
                                if (!isset($scoreNum[$val->report->cycle][$val->courseStandard->title_cn])) {
                                    $scoreNum[$val->report->cycle][$val->courseStandard->title_cn]['score'] = $val->courseScoresid->fraction;
                                    $scoreNum[$val->report->cycle][$val->courseStandard->title_cn]['num'] = 1;
                                } else {
                                    $scoreNum[$val->report->cycle][$val->courseStandard->title_cn]['score'] += $val->courseScoresid->fraction;
                                    $scoreNum[$val->report->cycle][$val->courseStandard->title_cn]['num'] += 1;
                                }
                                if (!isset($frequencys['data'][$val->report->cycle][$val->courseStandard->title_cn][$val->courseScoresid->fraction])) {
                                    $frequencys['data'][$val->report->cycle][$val->courseStandard->title_cn][$val->courseScoresid->fraction] = 1;
                                } else {
                                    $frequencys['data'][$val->report->cycle][$val->courseStandard->title_cn][$val->courseScoresid->fraction] += 1;
                                }
                                $childNum[$val->childid] = $val->childid;
                            }
                        }
                    }
                }


                $data = $this->splice($scoreNum, $frequencys, $timeTbaleModel->id,$courseCode->course_code, $childNum, $totle);

                $criteria = new CDbCriteria;
                $criteria->compare('program', $courseCode->program);
                $criteria->compare('course_code', $courseCode->course_code);
                $model = AchievementReportCache::model()->find($criteria);

                if(!$model){
                    $model = new AchievementReportCache();
                    $model->yid = $yid;
                    $model->program = $courseCode->program;
                    $model->course_code = $courseCode->course_code;
                    $model->status = 1;
                    $model->created_at = time();
                    $model->created_by = 5;
                }
                $model->average_data = json_encode($data['average']);
                $model->frequency_data = json_encode($data['frequency']);
                $model->updated_at = time();
                $model->updated_by = 5;
                $model->save();
                echo $k . ' - OK' . "\n";
            }
        }
        $criteria = new CDbCriteria;
        $criteria->compare('school_id', $schoolid);
        $criteria->compare('yid', $yid);
        $criteria->compare('type', 'report');
        $stateManageModel = StateManage::model()->find($criteria);

        if($stateManageModel){
            $stateManageModel->status = 1;
            $stateManageModel->save();
        }
        //return $data;
    }




    /*
     * $scoreNum   计算平均分数的数据组
     * $frequencys  计算人数的数据组
     * $id     类型
     * $childNum  孩子总人数
     * $totle  计算总分的的数据在
     */
    public function splice($scoreNum,$frequencys,$tid,$course_code,$childNum,$totle)
    {
        $array = array(
            'score1' => array(
                'peroid1' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'peroid2' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'peroid3' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'peroid4' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'yearend' => 0,
            ),
            'score2' => array(
                'peroid1' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'peroid2' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'peroid3' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'peroid4' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'yearend' => 0,
            ),
            'score3' => array(
                'peroid1' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'peroid2' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'peroid3' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'peroid4' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'yearend' => 0,
            ),
            'score4' => array(
                'peroid1' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'peroid2' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'peroid3' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'peroid4' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'yearend' => 0,
            ),
            'score5' => array(
                'peroid1' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'peroid2' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'peroid3' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'peroid4' => array('A' => 0, 'B' => 0, 'C' => 0, 'D' => 0),
                'yearend' => 0,
            ),
        );


        // 按照分数计算周期ABCE的人数
        if(isset($frequencys['data'])){
            foreach ($frequencys['data'] as $report_id => $item) {
                foreach ($item as $standard_id => $val) {
                    foreach ($val as $k => $num) {
                        if($k == 0){
                            $array['score1']['peroid' . $report_id][$standard_id] += $num;
                        }
                        if(in_array($k, array(1,2))){
                            $array['score2']['peroid' . $report_id][$standard_id] += $num;
                        }
                        if(in_array($k, array(3,4))){
                            $array['score3']['peroid' . $report_id][$standard_id] += $num;
                        }
                        if(in_array($k, array(5,6))){
                            $array['score4']['peroid' . $report_id][$standard_id] += $num;
                        }
                        if(in_array($k, array(7,8))){
                            $array['score5']['peroid' . $report_id][$standard_id] += $num;
                        }
                    }
                }
            }
        }

        // 按照分数计算总人数
        if($totle && isset($totle['fractionNum'])){
            foreach ($totle['fractionNum'] as $k=>$val){
                if($k == 0){
                    $array['score1']['yearend'] += $val;
                }
                if(in_array($k, array(1,2))){
                    $array['score2']['yearend'] += $val;
                }
                if(in_array($k, array(3,4))){
                    $array['score3']['yearend'] += $val;
                }
                if(in_array($k, array(5,6))){
                    $array['score4']['yearend'] += $val;
                }
                if(in_array($k, array(7,8))){
                    $array['score5']['yearend'] += $val;
                }
            }
        }

        //$courseCode->course_code;
        $criteria = new CDbCriteria;
        $criteria->compare('course_code', $course_code);
        $criteria->compare('status', 1);
        $courseModel = TimetableCourses::model()->find($criteria);

        $criteria = new CDbCriteria;
        $criteria->compare('course_id', $courseModel->id);
        $criteria->compare('tid', $tid);
        $criteria->compare('status', 1);
        $courseTeacherModel = TimetableCourseTeacher::model()->find($criteria);

        $cnName = '';
        $enName = '';
        if($courseTeacherModel){
            $userModel = User::model()->findByPk($courseTeacherModel->teacher_id);
            $cnName =  $userModel->getName();
            $enName =  $userModel->getName();
        }

        $data['average'] = array(
            'name_cn' => $course_code . ' - ' . $courseModel->title_cn,
            'name_en' => $course_code . ' - ' . $courseModel->title_en,
            'teacherNameCn' => $cnName,
            'teacherNameEn' => $enName,
            'total_students' => count($childNum)
        );
        $data['frequency'] = array(
            'name_cn' => $course_code . ' - ' . $courseModel->title_cn,
            'name_en' => $course_code . ' - ' . $courseModel->title_en,
            'teacherNameCn' => $cnName,
            'teacherNameEn' => $enName,
            'total_students' => count($childNum),
            'score1' => $array['score1'],
            'score2' => $array['score2'],
            'score3' => $array['score3'],
            'score4' => $array['score4'],
            'score5' => $array['score5'],
        );

        if($scoreNum){
            $peroid1 = isset($scoreNum[1]) ? $scoreNum[1] : array();
            $peroid1Data1 = array('-','-','-','-');
            if($peroid1){
                $peroid1Data1[0] = isset($peroid1['A']) ? sprintf('%.2f', $peroid1['A']['score'] / $peroid1['A']['num']) : '-';
                $peroid1Data1[1] = isset($peroid1['B']) ? sprintf('%.2f', $peroid1['B']['score'] / $peroid1['B']['num']) : '-';
                $peroid1Data1[2] = isset($peroid1['C']) ? sprintf('%.2f', $peroid1['C']['score'] / $peroid1['C']['num']) : '-';
                $peroid1Data1[3] = isset($peroid1['D']) ? sprintf('%.2f', $peroid1['D']['score'] / $peroid1['D']['num']) : '-';
            }

            $peroid2 = isset($scoreNum[2]) ? $scoreNum[2] : array();
            $peroid1Data2 = array('-','-','-','-');
            if($peroid2){
                $peroid1Data2[0] = isset($peroid2['A']) ? sprintf('%.2f', $peroid2['A']['score'] / $peroid2['A']['num']) : '-';
                $peroid1Data2[1] = isset($peroid2['B']) ? sprintf('%.2f', $peroid2['B']['score'] / $peroid2['B']['num']) : '-';
                $peroid1Data2[2] = isset($peroid2['C']) ? sprintf('%.2f', $peroid2['C']['score'] / $peroid2['C']['num']) : '-';
                $peroid1Data2[3] = isset($peroid2['D']) ? sprintf('%.2f', $peroid2['D']['score'] / $peroid2['D']['num']) : '-';
            }
            $peroid3 = isset($scoreNum[3]) ? $scoreNum[3] : array();
            $peroid1Data3 = array('-','-','-','-');
            if($peroid3){
                $peroid1Data3[0] = isset($peroid3['A']) ? sprintf('%.2f', $peroid3['A']['score'] / $peroid3['A']['num']) : '-';
                $peroid1Data3[1] = isset($peroid3['B']) ? sprintf('%.2f', $peroid3['B']['score'] / $peroid3['B']['num']) : '-';
                $peroid1Data3[2] = isset($peroid3['C']) ? sprintf('%.2f', $peroid3['C']['score'] / $peroid3['C']['num']) : '-';
                $peroid1Data3[3] = isset($peroid3['D']) ? sprintf('%.2f', $peroid3['D']['score'] / $peroid3['D']['num']) : '-';
            }
            $peroid4 = isset($scoreNum[4]) ? $scoreNum[4] : array();
            $peroid1Data4 = array('-','-','-','-');
            if($peroid4){
                $peroid1Data4[0] = isset($peroid4['A']) ? sprintf('%.2f', $peroid4['A']['score'] / $peroid4['A']['num']) : '-';
                $peroid1Data4[1] = isset($peroid4['B']) ? sprintf('%.2f', $peroid4['B']['score'] / $peroid4['B']['num']) : '-';
                $peroid1Data4[2] = isset($peroid4['C']) ? sprintf('%.2f', $peroid4['C']['score'] / $peroid4['C']['num']) : '-';
                $peroid1Data4[3] = isset($peroid4['D']) ? sprintf('%.2f', $peroid4['D']['score'] / $peroid4['D']['num']) : '-';
            }

            $yearend = 0;
            if($totle){
                $yearend = sprintf('%.2f', $totle['score'] / $totle['num']);
            }
        }
        $data['average']['peroid1'] = isset($peroid1Data1) ? $peroid1Data1 : array('-','-','-','-');
        $data['average']['peroid2'] = isset($peroid1Data2) ? $peroid1Data2 : array('-','-','-','-');
        $data['average']['peroid3'] = isset($peroid1Data3) ? $peroid1Data3 : array('-','-','-','-');
        $data['average']['peroid4'] = isset($peroid1Data4) ? $peroid1Data4 : array('-','-','-','-');
        $data['average']['yearend'] = isset($yearend) ? $yearend : '-';
        return $data;
    }
}