<?php

class StaffProfileController extends BranchBasedController
{
    public $tasks;
    public $selectedTask;

    public $actionAccessAuths = array(
        'index'                     => 'o_A_Access',
        'GetStaff'                  => 'o_A_Access',
        'StaffInfo'                 => 'o_A_Access',
        'Staff'                     => 'o_A_Access',
        'UpAvatar'                  => 'o_A_Access',
        'SaveAvatar'                => 'o_A_Access',
        'Profile'                   => 'o_A_Access',
        'SaveProfile'               => 'o_A_Access',
        'DelAvatar'                 => 'o_A_Access',
        'ExportExcel'               => 'o_H_Export_Staff',
        'CheckNewStaff'             => 'o_H_Adm_Common',
        'DelNewStaff'               => 'o_A_Access',
        'DelAttachment'             => 'o_A_Access',
        'downloads'                 => 'o_H_Adm_Common',
        'auditemployee'             => 'o_H_Adm_Common',
        'getNewstaff'             => 'o_H_Adm_Common',
    );

    public function init(){
        parent::init();

        $this->tasks = array(
            'index' => Yii::t('ivyer', 'Staff Profiles'),
            'registration' => Yii::t('ivyer', 'New Staff'),
            'alumni' => Yii::t('ivyer', 'Alumni')
        );

        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = false;
        $this->branchSelectParams['urlArray'] = array('//mcampus/staffProfile/index');
    }

    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    /*
     * 员工名录
     */
    public function actionIndex(){

        $this->selectedTask = Yii::app()->request->getParam('task', null);
        $this->selectedTask = in_array($this->selectedTask, array_keys($this->tasks)) ?
		$this->selectedTask : array_shift(array_keys($this->tasks));

        Yii::import('common.models.classTeacher.*');

        $taskfun = 'task'.ucfirst($this->selectedTask);
        $taskData = $this->$taskfun();

        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/plupload/plupload.full.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/cropper/cropper.min.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl.'/base/js/cropper/cropper.min.css');
       // Yii::msg($taskData);
        $initBranchId =  $this->branchId;

        $this->render('ivyers', array('initBranchId'=>$initBranchId, 'taskData'=>$taskData));
    }

    public function taskIndex(){
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
    }

    public function taskRegistration(){
        Yii::import('common.models.staff.Staff');
        $criteria = new CDbCriteria;
        $criteria->compare('level', array(-1,-2,-3));
        $criteria->compare('rank', 0);
        $criteria->compare('isstaff', 1);
        $criteria->compare('profile.branch', $this->branchId);
        $criteria->with=array('profile','staff');

        $dataProvider = new CActiveDataProvider('User', array(
            'criteria'=>$criteria,
            'sort' => array(
                'defaultOrder' => 'uname asc',
            ),
        ));

        $sql = "SELECT p.branch as branch,count(*) as count FROM ivy_users u join ivy_user_profile p on u.uid=p.uid WHERE u.level=-2 group by p.branch";
        $ret = Yii::app()->db->createCommand($sql)->queryAll();
        foreach($ret as $rt){
            $this->branchSelectParams['branchCount'][$rt['branch']] = $rt['count'];
        }

        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        return array('dataProvider'=>$dataProvider);
    }

	public function actionAuditEmployee()
	{
		Yii::import('common.models.staff.*');
        Yii::import('common.models.hr.*');
        $id = Yii::app()->request->getParam('id', 0);
        $level = Yii::app()->request->getParam('level', -2);
		$why = Yii::app()->request->getParam('why', '');
        $model = User::model()->findByPk($id);
		$models = Staff::model()->findByPk($id);
		$attachment = $models->attachment;
		$certainAttachment = array();
		foreach($attachment as $k=>$v){
			$certainAttachment[$k] = $v['photo'];
		}
		if(!empty($model)){
			if($certainAttachment['hiring'] && $certainAttachment['registration'] && $certainAttachment['bankcard'] && $certainAttachment['idcard']){
				if($level == -1 || $why != ""){
					$staffs = Staff::model()->findByPk($id);
					$branchId = $this->branchId;
					$model->level = $level;
					$staffs->detection = $why;
					if ($model->save()){
						if($staffs->save()){
							$mailer = Yii::createComponent('common.extensions.mailer.EMailer');
							if($level == -3){//未通过
								$mailer->Subject = sprintf('%s入职申请未通过', $model->getName());
								$mailer->AddAddress($this->branchObj->info->support_email);
								//$mailer->AddCC('<EMAIL>');
								if($branchId == 'BJ_DS'){
									$mailer->getView('checkNewStaff', array('why' => $staffs->detection,'branchId' => $branchId), 'todsparent');
								}else{
									$mailer->getView('checkNewStaff', array('why' => $staffs->detection,'branchId' => $branchId), 'main');
								}
							}elseif($level == -1){//通过
								$mailer->Subject = sprintf('%s入职申请已通过', $model->getName());
								$mailer->AddAddress('<EMAIL>');
								//$mailer->AddCC('<EMAIL>');
								if($branchId == 'BJ_DS'){
									$mailer->getView('checkNewStaff', array('branchId' => $branchId), 'todsparent');
								}else{
									$mailer->getView('checkNewStaff', array('branchId' => $branchId), 'main');
								}
							}
							$mailer->iniMail( OA::isProduction()); // 此行代码要放到AddAddress, AddCC方法下面
							$mailer->Send();
							$this->addMessage('state', 'success');
							$this->addMessage('message', Yii::t('message','success'));
							$this->addMessage('callback', 'cbAddNewStaffs');
						}
					}
					$this->showMessage();
				}else{
					$this->addMessage('state', 'fail');
					if($level == -1){
						$this->addMessage('message', Yii::t('message','审核状态不能为空'));
					}else{
						$this->addMessage('message', Yii::t('message','审核不通过请写明原因'));
					}

					$this->showMessage();
				}
			}else{
				$this->addMessage('state', 'fail');
				$this->addMessage('message', Yii::t('message','录取审批表、身份证复印件、银行卡复印件、人事登记表为必填项'));
				$this->showMessage();
			}
        }
	}

    public function actionSaveNewStaff()
    {
        Yii::import('common.models.staff.*');
        Yii::import('common.models.hr.*');
        $id = Yii::app()->request->getParam('id', 0);
        $model = User::model()->findByPk($id);

		if (!is_object($model)){
            $model = new User;
            $model->profile = new UserProfile;
            $model->staff = new Staff;
            $model->setScenario('addStaff');
            $model->profile->setScenario('addStaff');
            $model->staff->setScenario('addStaff');
        }
        else {
            $model->setScenario('editStaff');
            $model->profile->setScenario('editStaff');
            $model->staff->setScenario('editUser');
        }
        $model->staff->setScenario('addUsers');
        if ( isset($_POST['User'], $_POST['UserProfile'], $_POST['Staff']) ){
            $model->attributes = $_POST['User'];
            $model->profile->attributes = $_POST['UserProfile'];
            $model->staff->attributes = $_POST['Staff'];

            $model->email = strtolower($model->email);

            $newRecord = $model->isNewRecord;

            if(!$newRecord && $model->level == -1){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '请联系HR取消审核后再修改！');
                $this->showMessage();
            }

            if ($newRecord){
                $model->user_regdate = time();
            }
            $model->isstaff = 1;
            //if($newRecord)
			$model->level = -2;

            $model->iniPassword = 'barneybarney'; # 为了配合场景 激活帐号会重置密码
            $model->staff->emailPass = 'barneybarney'; # 为了配合场景 激活帐号会重置密码

            $model->profile->branch = $this->branchId;
            $model->profile->first_name = ucfirst(strtolower($model->profile->first_name));
            $model->profile->last_name = ucfirst(strtolower($model->profile->last_name));
            $model->uname = $model->profile->first_name.' '.substr($model->profile->last_name, 0, 1);
            $model->timezone_offset = 8.0;
            $model->flag = 2;
            $model->staff->startdate = strtotime($_POST['Staff']['startdate']);

			$model->staff->dagree_file = CUploadedFile::getInstance($model->staff,'dagree_file');
			$model->staff->releaseLetter_file = CUploadedFile::getInstance($model->staff,'releaseLetter_file');
			$model->staff->registration_file = CUploadedFile::getInstance($model->staff,'registration_file');
			$model->staff->bankcard_file = CUploadedFile::getInstance($model->staff,'bankcard_file');
			$model->staff->idcard_file = CUploadedFile::getInstance($model->staff,'idcard_file');
			$model->staff->hiring_file = CUploadedFile::getInstance($model->staff,'hiring_file');
			$model->staff->other_file = CUploadedFile::getInstance($model->staff,'other_file');


			$dagree = $model->staff->dagree;
			$releaseLetter = $model->staff->releaseLetter;

			//根据ID查找文件名称
            $valid=$model->validate();
            $valid=$model->profile->validate() && $valid;
            $valid=$model->staff->validate() && $valid;
            if ($valid){
				if(!$model->staff->dagree_file && !$_POST['Staff']['dagree'] && !$dagree['photo'] ){//&& !$dagree['said']
					$this->addMessage('state', 'fail');
					$this->addMessage('message', Yii::t('message','学历和学历证明图片和说明必填一项'));
				}elseif(!$model->staff->releaseLetter_file && !$_POST['Staff']['releaseLetter'] && !$releaseLetter['photo']){// && !$releaseLetter['said']
					$this->addMessage('state', 'fail');
					$this->addMessage('message', Yii::t('message','离职证明照片和说明必填一项'));
				}else{
					$model->save(false);
					$staffprimarygroup = 0;
					# Xoops 权限
					$staffgroups = array(XoGroups::STAFFGROUP);
					$auth = DepPosLink::model()->findByPk($model->profile->occupation_en);
					if ($auth){
						XoGroupsUsersLink::model()->removeAllGroup($model->uid);
						$staffgroups = array_merge($staffgroups, explode(',', $auth->authority));
						XoGroupsUsersLink::model()->addUserToGroup(XoGroups::USERGROUP, $model->uid);
						foreach ($staffgroups as $groupid){
							XoGroupsUsersLink::model()->addUserToGroup($groupid, $model->uid);
							if (!$staffprimarygroup)
								$staffprimarygroup = $groupid;
						}
					}

					# Yii 权限
					Yii::import('srbac.models.*');
					if ($model->uid){
						$criteria=new CDbCriteria();
						$criteria->compare('userid', $model->uid);
						$criteria->compare('itemname', '<>superDude');
						Assignments::model()->deleteAll($criteria);

						/*$criteria=new CDbCriteria();
						$criteria->compare('groupid', $staffgroups);
						$gs = XoGroups::model()->findAll($criteria);
						foreach ($gs as $g){
							$amodel = new Assignments;
							$amodel->itemname = $g->group_type;
							$amodel->userid = $model->uid;
							$amodel->save();
						}*/

						# 如果放开上面的代码 请注释本段代码
						$criteria = new CDbCriteria();
						$criteria->compare('title_id', $model->profile->occupation_en);
						$gs = HrTitleRole::model()->findAll($criteria);
						foreach ($gs as $g){
							$amodel = new Assignments;
							$amodel->itemname = $g->role;
							$amodel->userid = $model->uid;
							$amodel->save();
						}
					}

					# 同步搜索表
					StaffSync::model()->sync($model->uid);

					$model->profile->uid=$model->uid;
					$model->profile->staffprimarygroup=$staffprimarygroup;
					$model->profile->save(false);
					$model->staff->sid=$model->uid;
					$model->staff->department = $auth->department_id;
					$model->staff->save(false);

					$this->addMessage('state', 'success');
					$this->addMessage('message', Yii::t('message','success'));
					$this->addMessage('callback', 'cbAddNewStaff');
				}
			}else{
				$this->addMessage('state', 'fail');
				$errs = current($model->getErrors());
				$errs = !$errs ? current($model->profile->getErrors()) : $errs;
				$errs = !$errs ? current($model->staff->getErrors()) : $errs;
				$this->addMessage('message', $errs?$errs[0]:Yii::t('message','Failed!'));
			}
            $this->showMessage();
        }
    }

    public function getButton($data)
    {
        if ($data->level == -2) {
            echo '<a title="审核通过" class="btn btn-warning btn-xs" href="javascript:;" onclick="addNewStaffs('.$data->uid.',1)"><span class="glyphicon glyphicon-unchecked"></span></a> ';
        }
        if ($data->level == -1) {
            echo '<a title="取消审核" class="btn btn-success btn-xs" href="javascript:;" onclick="addNewStaffs('.$data->uid.',1)"><span class="glyphicon glyphicon-check"></span></a> ';
        }
		if	($data->level == -3){
			echo '<a title="审核未通过" class="btn btn-primary btn-xs" href="javascript:;" onclick="addNewStaffs('.$data->uid.',1)"><span class="glyphicon glyphicon-record"></span></a> ';
		}
        echo '<a class="btn btn-info btn-xs" href="javascript:;" onclick="addNewStaff('.$data->uid.',0)"><span class="glyphicon glyphicon-pencil"></span></a> ';
        echo '<a class="J_ajax_del btn btn-danger btn-xs" href="'.$this->createUrl("delNewStaff", array("id"=>$data->uid)).'"><span class="glyphicon glyphicon-remove"></span></a>';
    }

	/**
     * 下载新员工附件
     */
	public function actionDownloads()
	{
        ob_end_clean();
        $sid = Yii::app()->request->getParam('id', 0);
		$conditions = Yii::app()->request->getParam('attachment', 0);

		Yii::import('common.models.staff.*');
		//根据ID查找文件名称
		$criteria = new CDbCriteria();
		$criteria->select = "attachment";
		$criteria->compare('sid', $sid);
		$fileNames = Staff::model()->find($criteria);
		$name = $fileNames->attachment;

		foreach($name as $k =>$v){
			if($k == $conditions){
				$fileName = $v['photo'];
			}
		}
		$file_dir = Yii::app()->params['xoopsVarPath'] . '/staff/';
		$info = pathinfo($fileName);

		$fileres = file_get_contents($file_dir . $fileName);
		if(strtolower($info['extension']) == 'pdf'){
			header('Content-type: application/pdf');
		}
		else{
            header('Content-type: image/jpeg');
		}

		echo $fileres;
	}
    /**
     * 审核新员工
     */
    public function actionCheckNewStaff()
    {
        $uid = Yii::app()->request->getParam('uid',0);
        if(Yii::app()->request->isAjaxRequest && $uid != 0){
            $model = User::model()->findByPk($uid);
            if ($model->level == -1) {
                $model->level = -2;
            }else {
                $model->level = -1;
            }
            if ($model->save()) {
                echo CJSON::encode(array(
                    'msg' => 'success',
                ));
            }
        }
    }

    /**
     * 判断新员工状态
     */
    public function getStatus($data)
    {

        if ($data->level == -2){
            return Yii::t('message', 'Awaiting for approval');
        }elseif ($data->level == -1){
			return Yii::t('message', '等待创建');
		}else{
			return Yii::t('message', '审核未通过</br>'.$data->staff->detection);
		}
    }

    public function actionGetNewstaff($uid=0)
    {
        if($uid){
            Yii::import('common.models.staff.*');
            $model = User::model()->with('profile', 'staff')->findByPk($uid);
            if($model != null){
                if ($model->level == -1 || $model->level == -2 || $model->level == -3) {
                    $ret = array(
                        'uid' => $uid,
                        'firstname' => $model->profile->first_name,
                        'lastname' => $model->profile->last_name,
                        'name' => $model->name,
                        'gender' => $model->profile->user_gender,
                        'nationality' => $model->profile->nationality,
                        'occupation' => $model->profile->occupation_en,
                        'email' => $model->email,
                        'pemail' => $model->staff->pemail,
                        'startdate' => date('Y-m-d', $model->staff->startdate),
                        'attachment' => $model->staff->attachment,
						'detection' =>$model->staff->detection,
						'mobile_telephone' =>$model->staff->mobile_telephone,
                        'card_id' =>$model->staff->card_id,
                        'contract_type' =>$model->staff->contract_type,
                    );
                    echo CJSON::encode($ret);
                }
            }
        }
    }

    public function actionGetNewstaffs($uid=0)
    {
        if($uid){
            Yii::import('common.models.staff.*');
            $model = User::model()->with('profile', 'staff')->findByPk($uid);
            if($model != null){
                if ($model->level == -1 || $model->level == -2 || $model->level == -3) {
                    $ret = array(
                        'uid' => $uid,
                        'firstname' => $model->profile->first_name,
                        'lastname' => $model->profile->last_name,
                        'name' => $model->name,
                        'gender' => $model->profile->user_gender,
                        'nationality' => $model->profile->nationality,
                        'occupation' => $model->profile->occupation_en,
                        'email' => $model->email,
                        'pemail' => $model->staff->pemail,
                        'startdate' => date('Y-m-d', $model->staff->startdate),
                        'attachment' => $model->staff->attachment,
                        'detection' =>$model->staff->detection,
                        'mobile_telephone' =>$model->staff->mobile_telephone,
                        'card_id' =>$model->staff->card_id,
                        'contract_type' =>$model->staff->contract_type,
                    );
                    echo CJSON::encode($ret);
                }
            }
        }
    }


    public function actionDelNewStaff()
    {
        if(Yii::app()->request->isAjaxRequest){
            $id = Yii::app()->request->getParam('id', 0);
            if($id){
                Yii::import('common.models.staff.*');
                $model = User::model()->findByPk($id);
                $profile = UserProfile::model()->findByPk($id);
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '删除失败');
                if($profile->branch == $this->branchId){
                    if($model->level == -2 || $model->level == -3){
                        $model->level = -99;
                        if($model->save()){
                            $this->addMessage('state', 'success');
                            $this->addMessage('message', '删除成功');
                            $this->addMessage('callback', 'cbDelNewStaff');
                        }
                        /*$staff = Staff::model()->findByPk($id);
						$criteria = new CDbCriteria();
						$criteria->select = "attachment";
						$criteria->compare('sid', $id);
						$attachment_name = Staff::model()->find($criteria);
						if($ass = $attachment_name->attachment){
							$picPath = Yii::app()->params['xoopsVarPath'] . '/staff/';
							foreach($ass as $k =>$v){
								unlink($picPath . $v["photo"]);
							}
						}
                        if($staff->delete()){
                            if($profile->delete()){
                                if($model->delete()){
                                    // 删除同步表和搜索表
                                    StaffSync::model()->deleteByPk($id);
                                    FtsStaff::model()->deleteByPk($id);

                                    $this->addMessage('state', 'success');
                                    $this->addMessage('message', '删除成功');
                                    $this->addMessage('callback', 'cbDelNewStaff');
                                }
                            }
                        }*/
                    }
                    else{
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', '审核通过不能删除');
                    }
                }
                $this->showMessage();
            }
        }
    }

    public function taskAlumni(){
        $page = Yii::app()->request->getParam('page', 1);
        $email = Yii::app()->request->getParam('email', '');
        $criteria = new CDbCriteria;
        if ($email) {
            $criteria->compare('email', $email, true);
        }
        $criteria->compare('level', 0);
        $criteria->compare('rank', 0);
        $criteria->compare('isstaff', 1);
        $criteria->compare('profile.branch', $this->branchId);
        $criteria->with='profile';
        $criteria->order = 'uname asc';
        $count = User::model()->count($criteria);
        $criteria->limit = 20;
        $criteria->offset = ($page-1)*20;
        $items = User::model()->findAll($criteria);
        $cdata = array();
        foreach($items as $item){
            $cdata[] = array(
                'uid' => $item->uid,
                'name_en' => $item->profile->first_name.' '.$item->profile->last_name,
                'name_cn' => $item->name,
                'gender' => $item->profile->getGender(),
                'email' => $item->email,
                'occupation' => $item->profile->occupation ? $item->profile->occupation->getName() : '',
            );
        }
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        return array('cdata'=>$cdata, 'count'=>$count);
    }

    public function actionReentry()
    {
        if(Yii::app()->request->isPostRequest && Yii::app()->request->isAjaxRequest){
            Yii::import('common.models.staff.*');

            $uid = Yii::app()->request->getPost('uid', 0);
            $entryDate = Yii::app()->request->getPost('entryDate', '');

            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message','Failed!'));
            $model = User::model()->with('profile')->findByPk($uid);
            $staff = Staff::model()->findByPk($uid);
            $staff->setScenario('reentry');
            if($model->level == 0 && $model->profile->branch == $this->branchId){
                $staff->startdate = strtotime($entryDate);
                if($staff->save()){
                    $model->level = -2;
                    if($model->save()){
                        $this->addMessage('state', 'success');
                        $this->addMessage('callback', 'cb');
                        $this->addMessage('message', Yii::t('message','Data Saved!'));
                    }
                    else{
                        $this->addMessage('state', 'fail');
                        $errs = current($model->getErrors());
                        $this->addMessage('message', $errs?$errs[0]:Yii::t('message','Failed!'));
                    }
                }
                else{
                    $this->addMessage('state', 'fail');
                    $errs = current($staff->getErrors());
                    $this->addMessage('message', $errs?$errs[0]:Yii::t('message','Failed!'));
                }
            }
            $this->showMessage();
        }
    }


    /*
     * 按校园查询所有员工
     */
    public function getStaffByBranch($branchId){
        Yii::import('common.components.general.CommonContentFetcher');
        $userData = CommonContentFetcher::getStaffByBranch($branchId, $withPubData=true);

        return $userData;
    }

    /**
     * 打开上传员工头像弹窗
     */
    public function actionUpAvatar()
    {
        $staffid    = intval( Yii::app()->request->getParam('staffid',0) );
        if($staffid){
            $file = CUploadedFile::getInstanceByName('file');
            if($file){
                $ext =  $file->getExtensionName();
                $ext = empty( $ext ) ? 'jpg' : $ext;
                $filePath = Yii::app()->params['OAUploadBasePath'].'/infopub/staff/';
                $fileName = strtolower( 'tmp_'.$staffid. '.' . $ext );
                $file->saveAs($filePath.$fileName);
                echo CJSON::encode(array(
                    'url' => Yii::app()->params['OAUploadBaseUrl'].'/infopub/staff/'.$fileName.'?v'.time(),
                    'ext' => $ext,
                ));
            }
        }
    }

    /**
     * 上传员工头像
     */
    public function actionSaveAvatar()
    {
        $staffid    = intval( Yii::app()->request->getParam('staffid',0) );
        $data       = Yii::app()->request->getParam('data','');
        $ext        = strtolower(Yii::app()->request->getParam('ext',''));
        if($staffid && $data && $ext){
            $file = Yii::app()->params['OAUploadBasePath'].'/infopub/staff/tmp_'.$staffid.'.'.$ext;
            if(file_exists($file)){
                Yii::import('common.models.classTeacher.InfopubStaffExtend');
                Yii::import('application.extensions.image.Image');
                $image = new Image($file);
                $image->quality(100);
                //$image->sharpen(100);
                $data = CJSON::decode($data);
                $image->crop($data['width'], $data['height'], $data['y'], $data['x']);
                $image->resize(90,120);
                $filename = 'sp_'.uniqid().'.'.$ext;
                $subDir = 'infopub/staff/';
                $uploadRootPath = rtrim(Yii::app()->params['OAUploadBasePath'], '/').'/';
                $image->save( $uploadRootPath . $subDir . $filename);
                $aliYunOss['new'][] = $subDir . $filename;
                @unlink($file);
                $model = InfopubStaffExtend::model()->with('profile')->findByPk($staffid);
                if($model === null){
                    $model = new InfopubStaffExtend;
                    $model->userid = $staffid;
                }
                else{
                    if($model->profile->branch != $this->branchId){
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('message','Saving Failed!'));
                        $this->showMessage();
                    }
                    if($model->staff_photo != 'blank.jpg') {
                        @unlink( $uploadRootPath . $subDir . $model->staff_photo);
                        $aliYunOss['del'][] = $subDir . $model->staff_photo;
                    }
                }
                $model->staff_photo = $filename;
                $model->save(false);

                CommonUtils::processAliYunOSS($aliYunOss, $uploadRootPath);

                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','Data saved!'));
                $this->addMessage('callback', 'cbPhoto');
                $this->addMessage('data', array(
                    'url'=>rtrim(Yii::app()->params['OAUploadBaseUrl'], '/') . '/' . $subDir . $filename,
                    'staffid'=>$staffid));
            }
        }
        else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '请选头像！');
        }
        $this->showMessage();
    }

    /**
     * 编辑窗口，请求简介信息
     * @param int $staffid
     */
    public function actionProfile($staffid=0)
    {
        if($staffid){
            Yii::import('common.models.classTeacher.InfopubStaffExtend');
            $profile = UserProfile::model()->findByPk($staffid);
            $model = InfopubStaffExtend::model()->findByPk($staffid);
            if($profile->branch == $this->branchId){
                if($model === null){
                    $model = new InfopubStaffExtend;
                    $model->userid = $staffid;
                }
                echo CJSON::encode(array('en'=>$model->intro_en, 'cn'=>$model->intro_cn, 'staffid'=>$staffid));
            }
        }
    }

    /**
     * 读取员工离职设置
     * @param int $staffid
     */
    public function actionGetResignInfo($staffid=0){
        $staffid = intval($staffid);
        if($staffid){
            Yii::import('common.models.staff.*');
            $profileModel = UserProfile::model()->findByPk($staffid);
            $resignModel = StaffResign::model()->findByAttributes(array(
                'staff_uid' => $staffid,
                'processed' => 0
            ));
            if($profileModel->branch == $this->branchId){
                if(empty($resignModel)) {
                    $resignModel = new StaffResign();
                    $resignModel->staff_uid = $staffid;
                }

                $data = $resignModel->getAttributes();
                $data['resign_timestamp'] = ( $resignModel->resign_timestamp )?
                   OA::formatDateTime($resignModel->resign_timestamp) : '';
                $data['account_close_timestamp'] = ( $resignModel->account_close_timestamp )?
                   OA::formatDateTime($resignModel->account_close_timestamp) : '';

                $this->addMessage('state', 'success');
                $this->addMessage('data', $data);
            }else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', 'Invalid Request');
            }
            $this->showMessage();
        }
    }

    public function actionSaveResign(){
        if(isset($_POST['StaffResign'])){
            $uid = intval($_POST['StaffResign']['staff_uid']);
            if($uid){
                Yii::import('common.models.staff.*');
                $mailer = Yii::createComponent('common.extensions.mailer.EMailer');

                $user = User::model()->with('profile')->findByPk($uid);

                //根据当前用户ID拿到
                $currentUser = User::model()->findByPk(Yii::app()->user->id);

                //根据校园的ID拿到现在校园的名称
                $schoolAll = Branch::model()->getBranchList();
                //已经离职的
                if( $user->level < 1 ){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('ivyer', 'User has already quit or not active, cannot cancel'));
                    $this->showMessage();
                }
                //不是本校的员工
                if( $user->profile->branch != $this->branchId ){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('global', 'Invalid Request'));
                    $this->showMessage();
                }

                $resignModel = StaffResign::model()->findByAttributes(array(
                    'staff_uid' => $uid,
                    'branch_id' => $this->branchId,
                    'processed' => 0,
                ));

                //删除
                if( isset( $_POST['StaffResignRemove'] ) ){

                    StaffResign::model()->deleteAllByAttributes(array(
                        'staff_uid' => $uid,
                        'processed' => 0,
                        'branch_id' => $this->branchId,
                        'id' => intval($_POST['StaffResign']['id'])
                    ));

                    //操作成功 给HR发送邮件
                    $mailer->Subject = sprintf('%s员工离职取消通知', $schoolAll[$this->branchId]);
                    $mailer->AddAddress('<EMAIL>');
                    $mailer->getView('saveResign', array('status' => 0, 'name' =>$user->getName(), 'position' => $user->profile->occupation->getName(), 'resignModel' => $resignModel, 'userid' =>$currentUser->getName(), 'branchId' => $schoolAll[$this->branchId]), 'main');
                    $mailer->iniMail( OA::isProduction()); // 此行代码要放到AddAddress, AddCC方法下面
                    $mailer->Send();

                    $this->addMessage('state','success');
                    $this->addMessage('message', Yii::t('message', 'Data Saved!'));
                    $this->addMessage('data', array('deleted'=>$uid, 'staff_uid'=>$uid));
                    $this->addMessage('callback', 'cbResignSaved');
                    $this->showMessage();

                }else{//新增或修改
                    if(!$resignModel){
                        $resignModel = new StaffResign();
                    }
                    $resignModel->setAttributes(array(
                        'branch_id' => $this->branchId,
                        'staff_uid' => $uid,
                        'entry_timestamp' => $user->staff->startdate,
						'departure_why' => $_POST['StaffResign']['departure_why'],
                        'resign_timestamp' => strtotime($_POST['StaffResign']['resign_timestamp']),
                        'account_close_timestamp' => strtotime($_POST['StaffResign']['account_close_timestamp']),
                        'updated_user' => Yii::app()->user->id,
                        'processed' => 0,
                        'updated' => time()
                    ));

                    if(!$resignModel->save()){
                        $this->addMessage('state', 'fail');
                        $this->addMessage('error', $resignModel->getErrors());
                        $this->addMessage('message', Yii::t('message', 'Data Saving Failed!'));
                    }else{

                        //需要立即禁用用户帐号
                        $today = mktime(0,0,0,date('m'), date('d'), date('Y'));
                        if($resignModel->account_close_timestamp < $today){
                            $user->level = 0;
                            $user->save();
                            /* Staff::updateWechat($uid,'delete');*/
                        }

                        //离职设置成功后给HR发邮件
                        $mailer->Subject = sprintf('%s员工离职通知', $schoolAll[$this->branchId]);
                        $mailer->AddAddress('<EMAIL>');
                        $mailer->getView('saveResign', array('status' => 1, 'name' =>$user->getName() ,'position' => $user->profile->occupation->getName(), 'resignModel'=>$resignModel, 'userid' =>$currentUser->getName(), 'branchId' => $schoolAll[$this->branchId]), 'main');
                        $mailer->iniMail( OA::isProduction()); // 此行代码要放到AddAddress, AddCC方法下面
                        $mailer->Send();

                        $this->addMessage('data', array(
                            'staff_uid' => $resignModel->staff_uid,
                            'resign_timestamp' => OA::formatDateTime($resignModel->resign_timestamp),
                            'account_close_timestamp' => OA::formatDateTime($resignModel->account_close_timestamp)
                        ));
                        $this->addMessage('state', 'success');
                        $this->addMessage('callback', 'cbResignSaved');
                        $this->addMessage('message', Yii::t('message', 'Data Saved!'));
                        Yii::log(sprintf('%d set %d resign', Yii::app()->user->getId(), $resignModel->staff_uid),
                            CLogger::LEVEL_INFO, 'staff.resign'
                        );
                    }
                    $this->showMessage();
                }
            }
        }
    }

    /**
     * 保存员工简介信息
     */
    public function actionSaveProfile()
    {
        $staffid    = intval( Yii::app()->request->getParam('staffid',0) );
        if($staffid){
            Yii::import('common.models.classTeacher.InfopubStaffExtend');
            $model = InfopubStaffExtend::model()->with('profile')->findByPk($staffid);
            if($model === null){
                $model = new InfopubStaffExtend;
                $model->userid = $staffid;
            }
            else{
                if($model->profile->branch != $this->branchId){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('message','Saving Failed!'));
                    $this->showMessage();
                }
            }
            $model->attributes = $_POST['InfopubStaffExtend'];
            $model->updated_user = Yii::app()->user->id;
            $model->updated_time = time();
            if($model->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','Data saved!'));
                $this->addMessage('callback', 'cbProfile');
                $this->addMessage('data', array('branchid'=>$this->branchId, 'staffid'=>$staffid, 'cn'=>Yii::app()->format->ntext($model->intro_cn), 'en'=>Yii::app()->format->ntext($model->intro_en)));
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message','Saving Failed!'));
            }
            $this->showMessage();
        }
    }

    /**
     * 删除某员工头像
     */
    public function actionDelAvatar()
    {
        $staffid    = intval( Yii::app()->request->getParam('staffid',0) );
        Yii::import('common.models.classTeacher.InfopubStaffExtend');
        $criteria = new CDbCriteria();
        $criteria->compare('t.userid', $staffid);
        $criteria->compare('profile.branch', $this->branchId);
        $model = InfopubStaffExtend::model()->with('profile')->find($criteria);
        if($model != null){
            $filename = $model->staff_photo;
            $model->staff_photo = '';
            if($model->save(false)){
                if($filename != 'blank.jpg')
                    @unlink( Yii::app()->params['OAUploadBasePath'].'/infopub/staff/'.$filename );
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','Data saved!'));
                $this->addMessage('callback', 'cbDelAvatar');
                $this->addMessage('data', array('id'=>$staffid));
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message','Saving Failed!'));
            }
        }
        else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message','Saving Failed!'));
        }
        $this->showMessage();
    }

	public function actionDelAttachment()
    {
		$id  = Yii::app()->request->getParam('id', 0);
		if($id){
			$conditions  = Yii::app()->request->getParam('attachment', '');
			Yii::import('common.models.Staff.*');
			Yii::import('common.models.hr.*');

			$model = User::model()->findByPk($id);
			if($model->level == -2 || $model->level == -3){
				$model = Staff::model()->findByPk($id);
				$attachment = $model->attachment;
				$picPath = Yii::app()->params['xoopsVarPath'] . '/staff/';
				foreach($attachment as $k =>$v){
					if($k == $conditions){
						unlink($picPath . $v["photo"]);
						unset($attachment[$k]['photo']);
					}
				}
				$model->attachment = CJSON::encode($attachment);
				if($model->save(false)){
					$this->addMessage('state', 'success');
					$this->addMessage('message', '删除成功');
				}
				else{
					$this->addMessage('state', 'fail');
					$this->addMessage('message', Yii::t('message','Saving Failed!'));
				}
			}else{
				$this->addMessage('state', 'fail');
				$this->addMessage('message', '请联系HR取消审核后再修改！');
			}
			$this->addMessage('callback', 'cbAddNewStaff');
			$this->showMessage();
		}
	}

    public function actionEditAccount($uid=0)
    {
        if(!$uid)
            $uid = Yii::app()->request->getPost('id', 0);
        if($uid){
            Yii::import('common.models.staff.*');
            $model = User::model()->with('profile','staff')->findByPk($uid);
            if($model->profile->branch == $this->branchId){
                if(isset($_POST['User'], $_POST['UserProfile'])){
                    //必填项判断
                    if(empty($_POST['UserProfile']['occupation_en'])){
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('message', '请选择职位职责'));
                        $this->showMessage();
                    }
                    if($_POST['Staff']['mobile_telephone'] && !preg_match("/^1[345789]\d{9}$/", $_POST['Staff']['mobile_telephone'])){
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('message', '手机号格式不对'));
                        $this->showMessage();
                    }
                    $occupation_en = $model->profile->occupation_en;
                    $model->attributes = $_POST['User'];
                    $model->profile->attributes = $_POST['UserProfile'];
                    $model->staff->attributes = $_POST['Staff'];
                    if($model->save()){
                        if($model->profile->save()){
                            if($model->staff->save(false)) {
                                Yii::import('common.models.Staff.*');
                                if($occupation_en != $_POST['UserProfile']['occupation_en']){
                                    $operating_day =  strtotime(date("Y-m-d", time()));
                                    $criteria = new CDbCriteria();
                                    $criteria->compare('uid', $uid);
                                    $criteria->compare('operating_day', $operating_day);
                                    $positionModel = PositionChangeRecord::model()->find($criteria);
                                    if(!$positionModel){
                                        $positionModel = new PositionChangeRecord();
                                        $positionModel->uid = $uid;
                                        $positionModel->schoolid = $this->branchId;
                                        $positionModel->old_position = $occupation_en;
                                        $positionModel->operating_day = $operating_day;
                                        $positionModel->status = 0;
                                        $positionModel->created_at = time();
                                        $positionModel->created_by = $this->staff->uid;
                                    }
                                    $positionModel->new_position = $_POST['UserProfile']['occupation_en'];
                                    $positionModel->updated_at = time();
                                    $positionModel->updated_by = $this->staff->uid;
                                    $positionModel->save();
                                }

                                Staff::updateWechat($model->uid, 'update');
                                $this->addMessage('state', 'success');
                                $this->addMessage('message', Yii::t('message', 'Data saved!'));
                                $this->addMessage('callback', 'cbEditAccount');
                                $this->addMessage('data', array(
                                    'branchid' => $this->branchId,
                                    'staffid' => $uid,
                                    'name' => $model->getName(),
                                    'mobile_telephone' => $model->staff->mobile_telephone,
                                    'card_id' => $model->staff->card_id,
                                    'contract_type' => $model->staff->contract_type,
                                    'positionId' => $model->profile->occupation_en,
                                    'positionTitle' => $model->profile->occupation->getName(),
                                    'countryId' => $model->profile->nationality,
                                    'user_sig' => $model->user_sig,
                                ));
                                Yii::log(sprintf('%d edited %d', Yii::app()->user->getId(),
                                    $model->profile->uid),
                                    CLogger::LEVEL_INFO, 'staff.save');
                            }
                        }
                    }
                    // Yii 权限
                    Yii::import('srbac.models.*');
                    if ($model->uid){
                        $criteria = new CDbCriteria();
                        $criteria->compare('userid', $model->uid);
                        $criteria->compare('itemname', '<>superDude');
                        Assignments::model()->deleteAll($criteria);

                        $criteria = new CDbCriteria();
                        $criteria->compare('title_id', $model->profile->occupation_en);
                        $gs = HrTitleRole::model()->findAll($criteria);
                        foreach ($gs as $g){
                            $amodel = new Assignments;
                            $amodel->itemname = $g->role;
                            $amodel->userid = $model->uid;
                            $amodel->save();
                        }
                    }
                    $this->showMessage();
                }
                echo CJSON::encode(array(
                    'uid' =>$uid,
                    'mobile_telephone' => $model->staff->mobile_telephone,
                    'card_id' => $model->staff->card_id,
                    'contract_type' => $model->staff->contract_type,
                    'email' => $model->email,
                    'firstname' => $model->profile->first_name,
                    'lastname' => $model->profile->last_name,
                    'name' => $model->name,
                    'gender' => $model->profile->user_gender,
                    'nationality' => $model->profile->nationality,
                    'position' => $model->profile->occupation_en,
                    'dwelling_place' => $model->staff->dwelling_place,
                    'user_sig' => $model->user_sig,
                ));
            }
        }
    }

    public function actionExportExcel()
    {
        if($this->branchId){
            Yii::import('common.models.staff.Staff');
            $criteria = new CDbCriteria();
            $criteria->compare('profile.branch', $this->branchId);
            $items = User::model()->with(array('profile', 'staff'))->activeStaff()->findAll($criteria);

            $filename = 'Name list-'.$this->branchObj->abb;
            $countrys = Country::model()->getData();

            header("Content-type:application/vnd.ms-excel");
            header("Content-Disposition:attachment;filename=$filename.xls");
            echo $this->_t("id")."\t";
            echo $this->_t("first_name")."\t";
            echo $this->_t("last_name")."\t";
            echo $this->_t("中文姓名")."\t";
            echo $this->_t("性别")."\t";
            echo $this->_t("部门")."\t";
            echo $this->_t("职位")."\t";
            echo $this->_t("邮件")."\t";
            if(Yii::app()->user->checkAccess('ivystaff_hr')) {
                echo $this->_t("电话")."\t";
                echo $this->_t("帐号创建日期")."\t";
                echo $this->_t("入职日期")."\t";
                echo $this->_t("私人邮件")."\t";
                echo $this->_t("住址")."\t";
            }
            echo $this->_t("国籍")."\n";

            foreach($items as $item){
                $occupationName = '';
                $departmentName = '';
                if (isset($item->profile->occupation)) {
                    $occupation = $item->profile->occupation;
                    $department = $item->profile->occupation->link->department;
                    $occupationName = $occupation->cn_name .' '. $occupation->en_name;
                    $departmentName = $department->cn_name .' '. $department->en_name;
                }
                echo $this->_t($item->uid)."\t";
                echo $this->_t($item->profile->first_name)."\t";
                echo $this->_t($item->profile->last_name)."\t";
                echo $this->_t($item->name)."\t";
                echo $this->_t($item->profile->getGender())."\t";
                echo $this->_t($occupationName)."\t";
                echo $this->_t($departmentName)."\t";
                echo $item->email."\t";
                if(Yii::app()->user->checkAccess('ivystaff_hr')) {
                    echo $this->_t($item->staff->mobile_telephone) . "\t";
                    echo ($item->user_regdate ? date('Y-m-d', $item->user_regdate) : '') . "\t";
                    echo ($item->staff->startdate ? date('Y-m-d', $item->staff->startdate) : '') . "\t";
                    echo $this->_t($item->staff->pemail) . "\t";
                    echo $this->_t($item->staff->dwelling_place)."\t";
                }
                echo $this->_t($countrys[$item->profile->nationality])."\n";
            }
        }
    }

    public function _t($str='')
    {
        return iconv('utf-8', 'gbk', $str);
    }

    public function actionChangePassword()
    {
        if (Yii::app()->request->isAjaxRequest && Yii::app()->request->isPostRequest){
            $uid = Yii::app()->request->getParam('uid', '');
            $newPass = Yii::app()->request->getParam('newPass', '');
            $rePass = Yii::app()->request->getParam('rePass', '');
            if ($uid && $newPass && $rePass) {
                if ($newPass != $rePass) {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '确认密码不一致');
                    $this->showMessage();
                }
                if (!preg_match('/(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])[A-Za-z0-9]{8,20}/', $newPass)) {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '密码必须包含大写字母、小写字母和数字且8-20位之间');
                    $this->showMessage();
                }

                $staff = User::model()->findByPk($uid);
                if (!in_array('superDude', Yii::app()->user->getRoles())){
                    $criteria = new CDbCriteria();
                    $criteria->compare('uid', Yii::app()->user->id);
                    $criteria->compare('type', AdmBranchLink::ADM_TYPE_CD);
                    $count = AdmBranchLink::model()->count($criteria);
                    if (!$count) {
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', '请联系园长/校长操作');
                        $this->showMessage();
                    }

                    if ($staff->profile->branch != $this->branchId) {
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', '校园错误');
                        $this->showMessage();
                    }
                }

                Yii::import('common.models.staff.ResetMailPassword');
                $criteria = new CDbCriteria();
                $criteria->compare('branch_id', $this->branchId);
                $criteria->compare('updated', '>'.(time()-86400));
                $count = ResetMailPassword::model()->count($criteria);
                if ($count > 10) {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '一天当中不能重设超过10次邮箱密码');
                    $this->showMessage();
                }

                $model = new ResetMailPassword();
                $model->target_uid = $uid;
                $model->target_email = $staff->email;
                $model->pass = md5($newPass);
                $model->branch_id = $this->branchId;
                $model->updated = time();
                $model->updated_by = Yii::app()->user->id;
                if (!$model->save()) {
                    $errs = current($model->getErrors());
                    $this->addMessage('state', 'fail');
                    $this->addMessage('error', $model->getErrors());
                    $this->addMessage('message', $errs?$errs[0]:'保存错误');
                    $this->showMessage();
                }

                if(OA::isProduction()){
                    Yii::import('application.components.user.ExMail');
                    $exMail = new ExMail();
                    $postParams = array(
                        'action' => 3,
                        'Alias' => trim($model->target_email),
                        'Password' => $model->pass,
                        'Md5' => 1,
                    );
                    $ret = $exMail->AddUser($postParams);
                    if($ret != 1){
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', '重设失败');
                        $this->showMessage();
                    }
                }

                $branchInfo = BranchInfo::model()->findByPk($this->branchId);
                $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
                $mailer->AddAddress($branchInfo->support_email);
                $mailer->AddCC($model->target_email);

                $mailer->Subject = '企业邮箱密码重置通知';

                $mailer->iniMail( OA::isProduction() ); // 此行代码要放到AddAddress, AddCC方法下面
                $mailer->getView('reset_mail_password', array('data' => array(
                    'staffName' => $staff->getName(),
                    'staffEmail' => $model->target_email,
                    'cdName' => User::model()->findByPk(Yii::app()->user->id)->getName(),
                    'updated' => date('Y-m-d H:i', $model->updated),
                )), 'main');
                $mailer->Send();

                $this->addMessage('state', 'success');
                $this->addMessage('callback', 'cbModifyEmailPassword');
                $this->addMessage('message', '重置成功');

            }
            else {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '请填写完成信息');
            }
            $this->showMessage();
        }
    }
}
