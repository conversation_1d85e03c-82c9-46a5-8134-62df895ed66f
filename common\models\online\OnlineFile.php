<?php

/**
 * This is the model class for table "ivy_online_file".
 *
 * The followings are the available columns in table 'ivy_online_file':
 * @property integer $id
 * @property string $school_id
 * @property integer $pid
 * @property string $display
 * @property string $type
 * @property string $path_original
 * @property string $path_processed
 * @property string $handle
 * @property integer $handle_status
 * @property string $size
 * @property string $thumbnail
 * @property string $introduction_cn
 * @property string $introduction_en
 * @property integer $authority
 * @property integer $status
 * @property integer $created_at
 * @property integer $created_by
 * @property integer $updated_at
 * @property integer $updated_by
 */
class OnlineFile extends CActiveRecord
{
    const STATUS_VALID = 1;
    const STATUS_INVALID = 0;
    const STATUS_DELETE = 99;
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_online_file';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('created_at, created_by, updated_at, updated_by', 'required'),
			array('pid, handle_status, authority, status, created_at, created_by, updated_at, updated_by', 'numerical', 'integerOnly'=>true),
			array('school_id, display, type, path_original, path_processed, size, thumbnail', 'length', 'max'=>255),
			array('handle, introduction_cn, introduction_en', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, school_id, pid, display, type, path_original, path_processed, handle, handle_status, size, thumbnail, introduction_cn, introduction_en, authority, status, created_at, created_by, updated_at, updated_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'createdBy' => array(self::HAS_ONE, 'User', array('uid' => 'created_by')),
            'updatedBy' => array(self::HAS_ONE, 'User', array('uid' => 'updated_by')),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'school_id' => 'School',
			'pid' => 'Pid',
			'display' => 'Display',
			'type' => 'Type',
			'path_original' => 'Path Original',
			'path_processed' => 'Path Processed',
			'handle' => 'Handle',
			'handle_status' => 'Handle Status',
			'size' => 'Size',
			'thumbnail' => 'Thumbnail',
			'introduction_cn' => 'Introduction Cn',
			'introduction_en' => 'Introduction En',
			'authority' => 'Authority',
			'status' => 'Status',
			'created_at' => 'Created At',
			'created_by' => 'Created By',
			'updated_at' => 'Updated At',
			'updated_by' => 'Updated By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('school_id',$this->school_id,true);
		$criteria->compare('pid',$this->pid);
		$criteria->compare('display',$this->display,true);
		$criteria->compare('type',$this->type,true);
		$criteria->compare('path_original',$this->path_original,true);
		$criteria->compare('path_processed',$this->path_processed,true);
		$criteria->compare('handle',$this->handle,true);
		$criteria->compare('handle_status',$this->handle_status);
		$criteria->compare('size',$this->size,true);
		$criteria->compare('thumbnail',$this->thumbnail,true);
		$criteria->compare('introduction_cn',$this->introduction_cn,true);
		$criteria->compare('introduction_en',$this->introduction_en,true);
		$criteria->compare('authority',$this->authority);
		$criteria->compare('status',$this->status);
		$criteria->compare('created_at',$this->created_at);
		$criteria->compare('created_by',$this->created_by);
		$criteria->compare('updated_at',$this->updated_at);
		$criteria->compare('updated_by',$this->updated_by);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return OnlineFile the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public static function switchSize($filesize) {
        if($filesize >= **********) {
            $filesize = round($filesize / ********** * 100) / 100 . ' GB';
        } elseif($filesize >= 1048576) {
            $filesize = round($filesize / 1048576 * 100) / 100 . ' MB';
        } elseif($filesize >= 1024) {
            $filesize = round($filesize / 1024 * 100) / 100 . ' KB';
        } else {
            $filesize = $filesize . ' bytes';
        }
        return $filesize;
    }

    //首页OKR链接
    public static function getOkrUrl()
    {
        $serverConfs = CommonUtils::LoadConfig('CfgOnlineServer');
        $sConfs = (!OA::isProduction()) ? $serverConfs['dev'] : $serverConfs['prod'];
        $pid = 854;
        $crit = new CDbCriteria();
        $crit->compare('pid', $pid);
        // 上传人限制为李冰跟jack
        $crit->compare('created_by', array(8001584, 8001508));
        $crit->compare('status', OnlineFile::STATUS_VALID);
        $crit->order = 'introduction_cn DESC';
        $crit->limit = 2;
        $file = OnlineFile::model()->findAll($crit);
        $okrUrl = array('cn' => '', 'en' => '');
        foreach ($file as $item) {
            if (stripos($item->display, 'okr') !== false) {
                if (stripos($item->display, 'cn') !== false) {
                    $okrUrl['cn'] = $sConfs['url'] . '/' . $item->path_original;
                } else {
                    $okrUrl['en'] = $sConfs['url'] . '/' . $item->path_original;
                }
            }
        }
        return $okrUrl;
    }
}
