<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
    <h4 class="modal-title" id="exampleModalLabel"><?php echo $model->getTitle() ?></h4>
</div>

<div class="modal-body">
    <?php if(!$childidList): ?>
        <h4 class="text-center">无相关孩子</h4>
    <?php else: ?>
        <table class="table table-bordered">
            <colgroup>
                <col width="50">
                <col width="80">
                <col width="80">
                <col width="100">
                <col width="100">
                <col width="100">
                <col width="50">
                <col width="50">
                <col width="100">
            </colgroup>
            <thead>
                <tr>
                    <th>序号</th>
                    <th>中文姓名</th>
                    <th>英文姓名</th>
                    <th>班级</th>
                    <th>父亲手机</th>
                    <th>母亲手机</th>
                    <th>父亲邮箱</th>
                    <th>母亲邮箱</th>
                    <th>账单</th>
                    <!--<th>退费</th>-->
                </tr>
            </thead>
            <tbody>
        <?php

        $status = array(1=>"提交", 2=>"退回需补充材料", 3=>"已确认" ,4=>"提交退款处理", 5=>"已退费");
            $num=0;
            if($childidList){
                foreach ($childidList as $child) {
                    $num++;
                    $parents = array('father'=>"", "mother"=>"");
                    $childNamecn = "";
                    $childNameen = "";
                    if($child['childId']){
                        $parents = $childList[$child['childId']]->getParents();
                    }
                    if($child['childId']){
                        $childNamecn = $childList[$child['childId']]->name_cn;
                        if (in_array($childList[$child['childId']]->schoolid, array('BJ_DS', 'BJ_SLT'))) {
                            $childNameen = $childList[$child['childId']]->nick;
                        }
                        else {
                            $childNameen = $childList[$child['childId']]->first_name_en . " " . $childList[$child['childId']]->middle_name_en . " " . $childList[$child['childId']]->last_name_en;
                        }
                    }
                    echo '<tr>';
                    echo '<td>'.$num.'</td>';
                    echo '<td>' . $childNamecn . '</td>';
                    echo '<td>' . $childNameen . '</td>';
                    echo '<td>' . $childList[$child['childId']]->ivyclass->title . '</td>';
                    echo '<td>' . ($parents['father'] ? $parents['father']->parent->mphone : '') . '</td>';
                    echo '<td>' . ($parents['mother'] ? $parents['mother']->parent->mphone : '') . '</td>';
                    echo '<td>' . ($parents['father'] ? $parents['father']->email : '') . '</td>';
                    echo '<td>' . ($parents['mother'] ? $parents['mother']->email : '') . '</td>';
                    if($child['time'] && $child['stat'] == 20){
                        echo '<td style="color: red">' . $child['status'] . ' <a class="btn btn-default btn-xs"  target="_black"  href="'. $this->createUrl("invoice/index", array("childid" => $child['childId'], 'classid' => $childList[$child['childId']]->classid, 'status' => 'on')).'">退款</a></td>';
                    }else if( $child['stat'] == 50){
                        echo '<td style="color: #5bc0de">' . $child['status'] . ' <a class="btn btn-default btn-xs"  target="_black"  href="'. $this->createUrl("invoice/index", array("childid" => $child['childId'], 'classid' => $childList[$child['childId']]->classid, 'status' => 'on')).'">退款</a></td>';
                    }else{
                        if($child['stat'] == 20){
                            echo '<td>' . $child['status'] . ' <a class="btn btn-default btn-xs"  target="_black"  href="'. $this->createUrl("invoice/index", array("childid" => $child['childId'], 'classid' => $childList[$child['childId']]->classid, 'status' => 'on')).'">退款</a></td>';
                        }else{
                            echo '<td>' . $child['status'] . '</td>';
                        }
                    }
                    echo '</td>' ;
                    echo '</tr>';
                }
            }
        ?>
            </tbody>
        </table>
        <a class="btn btn-info" href="<?php echo  $this->createUrl('exportCourseInvoice', array('id' => $id));?>">导出</a>
    <?php endif; ?>
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
</div>
