<?php

class LearnInfoController extends TeachBasedController
{
    // 访问action的初级权限
    public $actionAccessAuths = array(
        'index' => 'o_T_Access'
    );

    public $toReplyNum;
    public $type;
    public $managetypeList;

    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'teaching';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Teaching Tasks');

        //初始化选择校园页面
        $this->branchSelectParams['hideKG'] = true;
        $this->branchSelectParams['urlArray'] = array('//mteaching/learnInfo/index', 'type' => $_GET['type']);
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl() . '/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/vue2.js');
        // 七牛上传所需文件
        // $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/plupload/plupload.full.min.js');
        // $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/qiniu.min.js');
        // 引入图表所需文件
        // $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/echarts.min.js');
        // 初始化权限
        //标签字体需要的文件
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/label/iconfont.css?v=20231124');
        Yii::import('common.models.portfolio.*');
    }

    public function actionIndex()
    {
        $this->render('index');
    }
    public function actionGetChildAttenanceProfile()
    {
        $childid = Yii::app()->request->getParam('childid');
        $yid = Yii::app()->request->getParam('yid');
        $res = CommonUtils::requestDsOnline("timetable/getChildAttenanceProfile", array(
            'childId' => $childid,
            'yid' => $yid,
            'schoolId' => $this->branchId,
        ), 'get');
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    public function actionshowStudentAttendByYid()
    {
        $childid = Yii::app()->request->getParam('childid');
        $yid = Yii::app()->request->getParam('yid');
        $type = Yii::app()->request->getParam('type');
        $period = Yii::app()->request->getParam('period');
        $res = CommonUtils::requestDsOnline("timetable/showStudentAttendByYid", array(
            'childid' => $childid,
            'yid' => $yid,
            'type' => $type,
            'period' => $period,
            'schoolId' => $this->branchId,
        ), 'get');
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    /**
     * 根据孩子id获取评估报告 start_year学年可选默认当前学年
     */
    public function actionGetAssessmentReportsByChild()
    {
//        $calendarYids = $this->getCalendars();
        $child_id = Yii::app()->request->getParam('child_id');
        $startyear = Yii::app()->request->getParam('start_year', 0);
//        $type = Yii::app()->request->getParam('type');
//        $startyear = $this->calendarStartYear[$calendarYids['currentYid']];
        $data = array(
            "child_id" => $child_id,
            "start_year" => $startyear,
//            "type" => $type,
        );
        $res = CommonUtils::requestDsOnline('assessments/getAssessmentReportsByChild', $data);
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionGetListByChildId()
    {
        $childId = Yii::app()->request->getParam('child_id');#学生id
        $start_year = Yii::app()->request->getParam('start_year');#学年
        $classId = Yii::app()->request->getParam('class_id', 0);#班级id 校长的信息需要使用classid获取班级所在学部 再去 grade_group_id 查询
        $grade_group_id = Yii::app()->request->getParam('grade_group_id', 0);#ES小学 MS初中 HS高中
        $messages = Yii::app()->request->getParam('messages', 0);# 0只显示普通的，1只显示特别沟通，2显示所有

        if (!$this->isCd()) {
            $messages = 0;
        }
        // 当前页码
        $pageNum = Yii::app()->request->getParam('pageNum', 1);
        $pageSize = Yii::app()->request->getParam('pageSize', 20);
        $filter = array(
            'school_id' => $this->branchId,
            'start_year' => $start_year,
            'status' => 1,
            'targets' => $childId,
            'classId' => $classId,
            'grade_group_id' => $grade_group_id,
        );
        $skip = ($pageNum - 1) * $pageSize;
        $take = $pageSize;
        $pushData = array(
            'skip' => (int)$skip,
            'take' => (int)$take,
            'filter' => $filter,
            'messages' => (int)$messages,
            'orderBy' => array('publish_at', 'DESC'),
        );
        $res = CommonUtils::requestDsOnline('journal/getListByChild', $pushData);
        if ($res['code'] === 1) {
            $this->addMessage('state', 'fail');
            $this->addMessage("data", $res['msg']);
            $this->showMessage();
        }
        $data = $res['data'];
        $signIdList = array();
        foreach ($data['list'] as $item) {
            $signIdList[] = $item['sign_as_uid'];
        }
        $signIdList = array_unique($signIdList);
        $userList = $this->getUserInfo($signIdList);
        // 计算分页
        $total = $data['count'];
        $pages = ceil($total / $pageSize);
        $pagination = array(
            'pages' => $pages,
            'pageNum' => $pageNum,
            'pageSize' => $pageSize,
            'total' => $total
        );

        $this->addMessage('state', 'success');
        $this->addMessage('data', array(
            'list' => $data['list'],
            'pagination' => $pagination,
            'userList' => $userList,
            'schoolYear' => $data['schoolYear'],
            'checkYear' => $data['start_year'],
            'groupMap' => $data['groupMap'],
//            'subjectMap' => $data['subjectMap'],
        ));
        $this->showMessage();
    }

    //根据 journal的id获取留言信息 带分页
    public function actionCommentItem(){
        $id = Yii::app()->request->getParam('id');
        $page_size = Yii::app()->request->getParam('page_size',0);
//        $skip = Yii::app()->request->getParam('skip',0);
        $pageNum = Yii::app()->request->getParam('page_num',1);
        $child_id = Yii::app()->request->getParam('child_id',0);
        $skip = ($pageNum - 1) * $page_size;
        $res = CommonUtils::requestDsOnline("journal/getCommentByJournalId",
            array(
                'link_id' => $id,
                'page_size' => (int)$page_size,
                'skip' => (int)$skip,
                'child_id' => $child_id,
            ),'get');
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage("state", "success");
            $this->addMessage("data", $data);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    public function actionYearClass()
    {
        $calendarYids = $this->getCalendars();
        $startyear = $this->calendarStartYear[$calendarYids['currentYid']];
        $schoolId = $this->branchId;
        $res = CommonUtils::requestDsOnline('assessments/indexData/' . $schoolId . '/' . $startyear);
        if ($res['code'] == 0) {
            // 获取管理的校园
            parent::initExt();
            if ($this->myClasses) {
                $newClassList = array();
                foreach ($res['data']['classList'] as $item) {
                    if (in_array($item['id'], $this->myClasses)) {
                        $newClassList[] = $item;
                    }
                }
                $res['data']['classList'] = $newClassList;
            }
            $this->addMessage("state", "success");
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionGetStudentByClass(){
        $classId = Yii::app()->request->getParam('classId');
        $data = array(
            "classId" => $classId,
        );
        $res = CommonUtils::requestDsOnline('assessments/getStudentByClass/', $data);
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    //获取孩子的评语
    public function actionGetChildCollegeCounseling()
    {
        $child_id = Yii::app()->request->getParam('child_id');
        $yid= Yii::app()->request->getParam('yid',0);
        $class_id= Yii::app()->request->getParam('class_id');
        if(empty($class_id)){
            $class_id = 0;
        }
        $data = array(
            "child_id" => $child_id,
            "yid" => $yid,
            "class_id" => $class_id
        );
        $res = CommonUtils::requestDsOnline('assessments/getChildCollegeCounseling', $data);
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    /**
     * 根据用户ID获取名称，照片，职位等信息
     *
     * @param array $userIdList 用户ID
     * @return array
     */
    public function getUserInfo($userIdList)
    {
        $userModels = User::model()->findAllByPk($userIdList);
        $userInfoList = array();
        foreach ($userModels as $userModel) {
            $staffPhoto = empty($userModel->staffInfo->staff_photo) ? "blank.jpg" : $userModel->staffInfo->staff_photo;
            $userInfoList[] = array(
                'uid' => $userModel->uid,
                'name' => $userModel->getName(),
                'photo' => OA::CreateOAUploadUrl('infopub/staff', $staffPhoto),
                'title' => CommonUtils::autoLang($userModel->profile->occupation->cn_name, $userModel->profile->occupation->en_name),
                'title_cn' => $userModel->profile->occupation->cn_name,
                'title_en' => $userModel->profile->occupation->en_name,
            );
        }
        return $userInfoList;
    }

    public function actionGetChildInfo(){
        $child_id = Yii::app()->request->getParam('child_id',0);
        $res = CommonUtils::requestDsOnline('child/getChildInfo', array(
            'child_id' => $child_id,
        ));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage("state", "success");
            $this->addMessage("data", $data);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    public function isCd(){
        return Yii::app()->user->checkAccess('ivystaff_cd');
    }

}