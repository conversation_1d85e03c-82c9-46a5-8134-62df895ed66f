<style>
    [v-cloak] {
        display: none;
    }
    .flexWidth {
        width: 130px
    }
    .text-ellipsis {
        text-overflow: -o-ellipsis-lastline;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        font-size:14px
    }
    .lineHeight {
        line-height: 48px
    }
    .image{
        width: 48px; 
        height: 48px;
        object-fit:cover;
    }
    .loading{
        width:100%;
        height:100%;
        background:#fff;
        position: absolute; 
        opacity: 0.5;
        z-index: 99
    }
    .loading span{
        width:100%;
        height:100%;
        display:block;
        background: url("<?php echo Yii::app()->theme->baseUrl?>/images/loading.gif")no-repeat center center ;
    }
    .maxHieght{
        max-height:300px;
        overflow-y:auto;
        border: 1px solid #ccc;
        padding: 10px;
        border-radius: 5px;
    }
    .defaultBg{
        background:#e9e9e9;
        color:#666
    }
    .content img{
        max-width:100%;
        margin-bottom: 1em; 
    }
    .badgeNum{
        display: inline-block;
        width: 6px;
        height: 6px;
        background: #d9534f;
        border-radius: 50%;
        margin-top: 6px;
    } 
    .color94442FF{
        color:#A94442FF
    }
    .point{
        width: 5px;
        height: 5px;
        background: #333333;
        display: inline-block;
        border-radius: 50%;
        border: 1px solid #333333;
        position: absolute;
        left: 0;
        top: 8px;
    }
    .border{
        border: 2px solid #428BCA;
    }
    .childImage{
        width: 32px; 
        height: 32px;
        object-fit:cover;
    }
    .tooltip-inner{
        width:130px;
    }
    .shareContent{
        width:375px;
        /* border-radius: 18px; */
        margin:0 auto
    }
    .shareStatus{
        position: absolute;
        left:0;
        top:0
    }
    .bgPto{
        background-repeat: no-repeat;
        background-size: contain;
    }
    .sharePto .logo{
        width:150px;
        margin-top:32px;
    }
    .sharePto .title{
        font-size: 26px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #FFFFFF;
        line-height: 38px;
        margin-top:24px
    }
    .sharePto .view{
        width: 100%;
        margin-top:8px;
        background: #E69119;       
        font-size: 14px;
        color:#fff;
        padding:6px
    }
    .shareContent .contentText{
        margin:16px 16px 0;
        border-radius: 16px;
    }
    .contentText .title{
        font-size:16px;
        line-height: 20px;
        margin-bottom:4px
    }
    .contentText .summary{
        font-size: 13px;
        line-height: 19px;
        margin-bottom:4px
    }
    .shareContent .bottom{
        margin:0 16px
    }
    .shareContent .bottom img{
        width: 100%;
        margin-bottom:16px
    }
    .contentText .wechat{
        border-top: 1px dashed #D0D0D0;
        margin-top: 20px;
        text-align: center;
    }
    .wechat{
        justify-content: center;
        align-items: center;
    }
    .fontWight{
        font-weight:600
    }
    .white{
        color:#fff
    }
    .Thumbnail{
        position: absolute;
        right: 0;
        top: 0;
        width:100px;
        text-align:center
    }
    .Thumbnail img{
        width:90px;
        padding:10px
    }
    .Thumbnail div{
        background:#F7F7F8
    }
    .Thumbnail .checkImg{
        border: 2px solid #4D88D2;
        border-radius: 8px;
    }
    .wechatImg{
        width:120px;
        height:120px
    }
    .shareAvatar{
        width:40px;
        height:40px;
        object-fit:cover;
        border:1px solid #fff
    }
    .white08{
        opacity: 0.8;
    }
    .pt5{
        padding-top:5px
    }
    .p24{
        padding:24px !important
    }
    .p8{
        padding:8px
    }
    .Options{
        position: absolute;
        left: 0;
        top: 0;
        width:200px
    }
    .pt2{
        padding-top:2px
    }
    .modal-body{
        overflow:hidden
    }
    .readBorder{
        border: 2px solid #D9534F;
        display: inline-block;
        border-radius: 50%;
        box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.4);
    }
    .readBorder img{
       width:36px;
       height:36px
    }
    .replyColor{
        color:#F0AD4E
    }
    .schoolTitle,.schoolTitle1{
        color: #fff;
        font-size: 16px;
        margin-top: 12px;
        position: relative;
        display: inline-block;
    }
    .schoolTitle:before, .schoolTitle:after {
        content: ''; 
        position: absolute; 
        top: 50%;
        background: #fff; 
        width: 25px;
        height: 1px;
    }
    .schoolTitle:before,.schoolTitle1:before{
        left: -35px;
    }
    .schoolTitle:after,.schoolTitle1:after {
        right: -35px;
    }
    .schoolTitle1:before, .schoolTitle1:after {
        content: ''; 
        position: absolute; 
        top: 50%;
        background: #061F9D; 
        width: 25px;
        height: 1px;
    }
    .contentAvatar {
        width: 44px;
        height: 44px;
        border-radius: 50%;
        object-fit: cover;
    }
    .tagLabel {
        background: #EBEDF0;
    }
    .contentPostObject{
        width: 36px;
        height: 36px;
        border-radius: 50%;
        object-fit:cover;
    }
    .postMore{
        font-size: 14px;
        display: inline-block;
        height: 36px;
        line-height: 36px;
        background: #4D88D2;
        color: #fff;
        padding: 0 10px;
        border-radius: 18px;
        vertical-align: middle;
    }
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Teaching Tasks'), array('//mteaching/default/index')) ?></li>
        <li><?php echo Yii::t('newDS', 'Journal') ?></li>
        <li><?php echo Yii::t("newDs", "All Journals");?></li>
    </ol>
    <div class="row" id='container' v-cloak>
        <div class="col-md-2 col-sm-12">
            <div class="list-group" id="classroom-status-list">
                <a href="<?php echo $this->createUrl('index'); ?>" class="list-group-item status-filter active"><?php echo Yii::t("newDS", "All Journals");?></a>
                <a href="<?php echo $this->createUrl('edit'); ?>" class="list-group-item status-filter"><?php echo Yii::t("newDS", "Mine");?></a>
                <a href="<?php echo $this->createUrl('feedback'); ?>" class="list-group-item status-filter">
                <span v-if='toReplyNum>"0"' class='badgeNum pull-right'></span><?php echo Yii::t("newDS", "Parent Feedback");?></a>
            </div>
        </div>
        <div >
            <div class='loading'  v-if='loading'>
                <span></span>
            </div>
            <div class='col-md-5 col-sm-5'>
                <div class="panel panel-default">
                    <div class="panel-heading flex">
                        <h4 class='flex1 '>Recent Journals from Teachers</h4>
                        <div class='pull-right'>
                            <div class="btn-group mr10">
                                <button type="button" class="btn btn-default dropdown-toggle  btn-sm" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    {{teacherFilter[teacherFilterType]}} <span class="caret"></span>
                                </button>
                                <ul class="dropdown-menu">
                                    <li v-for='(list,key,index) in teacherFilter'><a href="javascript:void(0)" @click='teacherFilterType=key,getList("teacher",key)'>{{list}}</a></li>
                                </ul>
                            </div>
                            <?php if(in_array('teacher', $this->managetypeList)): ?>
                            <a href="<?php echo $this->createUrl('show', array('type' => 'teacher')); ?>" class="btn  btn-primary btn-sm pull-right" role="button"><span class='glyphicon glyphicon-plus'></span>  <?php echo Yii::t("newDS", "New");?></a>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="panel-body relative">
                        <div class="replyColor mb16 font14">
                            <span class="glyphicon glyphicon-exclamation-sign" aria-hidden="true"></span> <?php echo Yii::t('directMessage', 'This page only displays journal, for Direct Message, please go to "Mine" page'); ?>
                        </div>
                        <div class='loading'  v-if='teacherLoading'>
                            <span></span>
                        </div>
                        <p class='text-center' v-if='teacherList.length==0 && !loading' ><?php echo Yii::t("newDS", "No items found");?></p>
                        <div v-else>
                            <div v-for='(list,idx) in teacherList'>
                                <p class='text-ellipsis  text-primary cur-p fontBold' data-toggle="modal"  @click='viewContent(list,"teacher")'>{{list.title}}</p>
                                <div>
                                    <span class="label label-default defaultBg defaultBg mr5 mb5 fontWeightNormal pull-left">{{journalCategory[list.category]}}</span>
                                    <span class="label label-info mr5 mb5 fontWeightNormal pull-left" v-for='(item,id) in list.subjects'>{{subjectList[item]}}</span>
                                </div>
                                <div class='clearfix'></div>
                                <div class="media ">
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)">
                                            <img class="media-object img-circle image" :src="teacherUser(list.sign_as_uid,'photo')" data-holder-rendered="true" >
                                        </a>
                                    </div>
                                    <div class="media-right pull-right pt10 text-right select_4">
                                        <span style="font-size:24px" class="glyphicon glyphicon-qrcode cur-p pull-left" @click='oneQrcode(list,"teacher")' aria-hidden="true"></span>
                                        <h4 class="media-heading font12" v-if='list.status=="1"'>{{list.format_publish_at}}</h4>
                                        <div class='text-muted'>
                                            <span  v-if='list.targets_num && list.targets_num>0'>
                                             {{list.targets_num}}<?php echo Yii::t("newDS", " subscriber assigned");?>丨
                                             <?php if(in_array('leader', $this->managetypeList)): ?>
                                                <span class="text-primary cur-p"  @click='reviewedList(list)'>{{list.reviewed_num}} <?php echo Yii::t("newDS", "read");?></span> 
                                             <?php else:?>
                                                    <span class="text-primary cur-p">{{list.reviewed_num}} <?php echo Yii::t("newDS", "read");?></span> 
                                            <?php endif;?>
                                            </span>

                                            <div class='color94442FF pull-right' v-else onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" title="<?php echo Yii::t("newDS", "Add subscriber when editing a journal");?>" data-placement="left"> 
                                                <span  class='glyphicon glyphicon-exclamation-sign'></span>
                                                <span><?php echo Yii::t("newDS", "no subscriber assigned");?></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="media-body pt10 media-middle">
                                        <h4  class="media-heading font12"><a :href='getUrl(list.sign_as_uid)' target="_blank" onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" title="<?php echo Yii::t("site", "All journals");?>" data-placement="top"> {{teacherUser(list.sign_as_uid,'name')}}</a></h4>
                                        <div class='text-muted'>
                                            {{teacherUser(list.sign_as_uid,'title')}}
                                        </div>
                                    </div>
                                </div>
                                <hr  v-if='idx!=teacherList.length-1'>
                            </div>
                        </div>
                    </div>
                    <nav aria-label="Page navigation" v-if='teacherCopyPage.pages>1'  class="text-left ml10"> 
                        <ul class="pagination">
                            <li v-if='teacherPageNum >1'>
                                <a href="javascript:void(0)" @click="plus('teacher',1)" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            <li v-else class="disabled">
                                <a aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            <li class="previous" v-if='teacherPageNum >1'>
                                <a href="javascript:void(0)" @click="prev('teacher',teacherPageNum)">‹</a>
                            </li>
                            <li class="disabled" v-else>
                                <a href="javascript:void(0)">‹</a>
                            </li>
                            <li v-for='(data,index) in teacherPage.pages' :class="{ active:data==teacherPageNum }">
                                <a href="javascript:void(0)" @click="plus('teacher',data)">{{data}}</a>
                            </li>
                            <li class="previous" v-if='teacherPageNum <teacherCopyPage.pages'>
                                <a href="javascript:void(0)" @click="next('teacher',teacherPageNum)">›</a>
                            </li>
                            <li class="previous disabled" v-else>
                                <a href="javascript:void(0)">›</a>
                            </li>
                            <li v-if='teacherPageNum <teacherCopyPage.pages'>
                                <a href="javascript:void(0)" @click="plus('teacher',teacherCopyPage.pages)" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li v-else class="disabled">
                                <a aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                        </ul>
                        <div class='summary mb10'>第 <span v-if='teacherPageNum*20-20==0'>1</span><span v-else>{{teacherPageNum*20-20}}</span>-{{teacherPageNum*20}} 条, 共 {{teacherCopyPage.total}} 条.</div>
                    </nav>
                </div>
            </div>
            <div class='col-md-5 col-sm-5'>
                <div class="panel panel-default">
                    <div class="panel-heading flex">
                        <h4 class='flex1 '>Recent Journals from School Leaders</h4>
                        <div class='flexWidth'>
                            <div class="btn-group">
                                <button type="button" class="btn btn-default dropdown-toggle  btn-sm" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    {{gradeGroupType=='all'?'<?php echo Yii::t("dashboard", "All");?>':gradeGroupList[gradeGroupType]}} <span class="caret"></span>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a href="javascript:void(0)" @click='gradeGroupType="all",getList("leader","",)'><?php echo Yii::t("dashboard", "All");?></a></li>
                                    <li v-for='(list,key,index) in gradeGroupList'><a href="javascript:void(0)" @click='gradeGroupType=key,getList("leader",key)'>{{list}}</a></li>
                                </ul>
                            </div>
                            <?php if(in_array('leader', $this->managetypeList)): ?>
                            <a href="<?php echo $this->createUrl('show', array('type' => 'leader')); ?>" class="btn  btn-primary btn-sm pull-right ml5" role="button"><span class='glyphicon glyphicon-plus'></span> <?php echo Yii::t("newDS", "New");?></a>
                            <?php endif;?>
                        </div>
                    </div>
                    <div class="panel-body relative">
                        <div class='loading'  v-if='leaderLoading'>
                            <span></span>
                        </div>
                        <p class='text-center' v-if='leaderList.length==0 && !loading' ><?php echo Yii::t("newDS", "No items found");?></p>
                        <div v-else>
                            <div v-for='(list,index) in leaderList'>
                                <p class='text-ellipsis  text-primary cur-p fontBold' data-toggle="modal" @click='viewContent(list,"leader")'>{{list.title}}</p>
                                <div>
                                    <span class="label label-default defaultBg defaultBg mr5 fontWeightNormal pull-left">{{journalCategory[list.category]}}</span>
                                    <span class="label label-default defaultBg defaultBg mr5 fontWeightNormal" v-for='(item,id) in list.grade_group_id'>{{gradeGroupList[item]}}</span>
                                </div>
                                <div class='clearfix'></div>
                                <div class="media ">
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)">
                                            <img class="media-object img-circle image" :src="leaderUser(list.sign_as_uid,'photo')" data-holder-rendered="true" >
                                        </a>
                                    </div>
                                    <div class="media-right pull-right  text-right select_4 pt10" >
                                        <div style="font-size:24px" class="glyphicon glyphicon-qrcode cur-p pull-left " @click='oneQrcode(list,"leader")' aria-hidden="true"></div>
                                        <h4 class="media-heading font12">{{list.format_publish_at}}</h4>
                                        <span class='text-primary cur-p' @click='leaderRead(list)'>{{list.reviewed_num}} <?php echo Yii::t("newDS", "read");?></span> 
                                    </div>
                                    <div class="media-body pt10 media-middle">
                                        <h4 class="media-heading font12">{{leaderUser(list.sign_as_uid,'name')}}</h4>
                                        <div class='text-muted'>
                                            {{leaderUser(list.sign_as_uid,'title')}}
                                        </div>
                                    </div>
                                </div>
                                <hr v-if='index!=leaderList.length-1'>
                            </div>
                        </div>
                    </div>
                    <nav aria-label="Page navigation" v-if='leaderCopyPage.pages>1'  class="text-left ml10">
                        <ul class="pagination">
                            <li v-if='leaderPageNum >1'>
                                <a href="javascript:void(0)" @click="plus('leader',1)" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            <li v-else class="disabled">
                                <a aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            <li class="previous" v-if='leaderPageNum >1'>
                                <a href="javascript:void(0)" @click="prev('leader',leaderPageNum)">‹</a>
                            </li>
                            <li class="disabled" v-else>
                                <a href="javascript:void(0)">‹</a>
                            </li>
                            <li v-for='(data,index) in leaderPage.pages' :class="{ active:data==leaderPageNum }">
                                <a href="javascript:void(0)" @click="plus('leader',data)">{{data}}</a>
                            </li>
                            <li class="previous" v-if='leaderPageNum <leaderCopyPage.pages'>
                                <a href="javascript:void(0)" @click="next('leader',leaderPageNum)">›</a>
                            </li>
                            <li class="previous disabled" v-else>
                                <a href="javascript:void(0)">›</a>
                            </li>
                            <li v-if='leaderPageNum <leaderCopyPage.pages'>
                                <a href="javascript:void(0)" @click="plus('leader',leaderCopyPage.pages)" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li v-else class="disabled">
                                <a aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                        </ul>
                        <div class='summary mb10'>第 <span v-if='leaderPageNum*20-20==0'>1</span><span v-else>{{leaderPageNum*20-20}}</span>-{{leaderPageNum*20}} 条, 共 {{leaderCopyPage.total}} 条.</div>
                    </nav>
                </div>
            </div>
            <!-- 查看内容 -->
            <div class="modal fade" id="contentModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "Content");?></h4>
                    </div>
                    <div class="modal-body">
                        <form class="form-horizontal" v-if='contentData.journalData'>
                            <div class="form-group" v-if='contentData.journalData.type=="teacher"'>
                                <div class="col-sm-12">
                                    <h3><strong class='font18 titleCn'> {{contentData.journalData.title}}</strong></h3>
                                </div>
                            </div>
                            <div  v-if='contentData.journalData.type=="leader"'>
                                <div class="form-group">
                                    <div class="col-sm-12">
                                        <h3><strong class='font18 titleCn'> {{contentData.journalData.title_cn}}</strong></h3>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-sm-12">
                                    <h3><strong class='font18 titleEn'>{{contentData.journalData.title_en}}</strong></h3>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group" >
                                <div class="col-sm-12 pt7">
                                    <span class="label label-default defaultBg defaultBg mr5 mb5" > {{journalCategory[contentData.journalData.category]}}</span>
                                </div>
                            </div>
                            <div class="form-group"  v-if='contentData.journalData.type=="leader"'>
                                <div class="col-sm-12 pt7">
                                    <span class="label label-default defaultBg defaultBg mr5" v-for='(item,id) in contentData.journalData.grade_group_id'>{{gradeGroupList[item]}}</span>
                                </div>
                            </div>
                            <div class="form-group" v-if='contentData.journalData.type=="teacher"'>
                                <div class="col-sm-12 pt7">
                                <span class="label label-info mr5 mb5 fontWeightNormal pull-left" v-for='(item,id) in contentData.journalData.subjects'>{{subjectList[item]}}</span>
                                </div>
                            </div>       
                            <div class="media mt24 mb24">
                                <div class="media-left pull-left media-middle">
                                    <a href="javascript:void(0)">
                                        <img class="contentAvatar" :src="contentData.userInfo[contentData.journalData.sign_as_uid].photoUrl" data-holder-rendered="true" >
                                    </a>
                                </div>
                                <div class="media-body pt8 media-middle">
                                    <h4  class="media-heading font12">{{contentData.userInfo[contentData.journalData.sign_as_uid].name}}<span class="label label-default color6 tagLabel  ml5"><?php echo Yii::t("directMessage", "Signature"); ?></span></h4>
                                    <div class='text-muted'>{{contentData.journalData.sign_as_title}}</div>
                                </div>
                            </div>
                            <div class="form-group" v-if='contentData.journalData.type=="teacher"'>
                                <div class="col-sm-12 pt7" style="padding: 0 30px;">
                                    <div class='content contentCn' id='contentCn' v-html='contentData.journalData.content'></div>
                                </div>
                            </div>
                            <div v-if='contentData.journalData.type=="leader"'>
                                <div class="form-group">
                                    <div class="col-sm-12 pt7" style="padding: 0 30px;">
                                        <div class='content contentCn' id='contentCn' v-html='contentData.journalData.content'></div>
                                    </div>
                                </div>
                                <div class="form-group" >
                                    <div class="col-sm-12 pt7" style="padding: 0 30px;">
                                        <div class='content contentEn'  id='contentEn'  v-html='contentData.journalData.content_en'></div>
                                    </div>
                                </div>
                            </div>
                            <div class='mt24'  v-if='contentData.journalData.attachments.length!=0'>
                                <p class='color3 font14'><strong><?php echo Yii::t("curriculum", "Attachments"); ?></strong> <span class="badge ml5">{{contentData.journalData.attachments.length}}</span></p>
                                <div v-for='(list,index) in contentData.journalData.attachments' class='mt16' style='word-break:break-all;'>
                                    <a :href="list.file_key" target='_blank'><span class='glyphicon glyphicon-paperclip mr4'></span>{{list.title}}</a>    
                                </div>
                            </div>
                            <div class='mt24'>
                                <p class='color3 font14'><strong><?php echo Yii::t("newDS", "Subscribers"); ?></strong> <span class="badge ml5">{{contentData.journalData.targets_num}}</span></p>
                                <div class='mt16'>
                                    <img class="contentPostObject mr16 mb8"  v-for='(list,index) in contentData.journalData.targets'  :src="contentData.childData[list].avatar" data-holder-rendered="true" >    
                                    <span class='postMore cur-p' v-if='contentData.journalData.targets_num>10' @click='showMore(contentData.journalData)'>+ {{contentData.journalData.targets_num-10}} ></span>
                                </div>
                            </div>
                        </form>
                    </div>
                    </div>
                </div>
            </div>
            <!-- 查看全部发布对象 -->
            <div class="modal fade" id="postObjectModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "查看全部发布对象"); ?></h4>
                        </div>
                        <div class="modal-body" >
                            <div class="form-group">
                                <div class="input-group">
                                <div class="input-group-addon"><span class='glyphicon glyphicon-search'></span></div>
                                <input type="text" class="form-control" v-model='search' placeholder="请输入姓名">
                                </div>
                            </div>
                            <div class='font14'>
                                <span class='glyphicon glyphicon-user color6'> </span><span class='color3 pl4'>共{{Object.keys(searchData).length}}人</span>
                            </div>
                            <div>
                                <div class="media mt10 mb10 col-md-6 col-sm-6"  v-for='(list,key,index) in searchData' >
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)">
                                            <img class="contentAvatar" :src="list.avatar" data-holder-rendered="true" >
                                        </a>
                                    </div>
                                    <div class="media-body pt8 media-middle">
                                        <h4  class="media-heading font12">{{list.name}}</h4>
                                        <div class='text-muted'>{{list.class_name}}</div>
                                    </div>
                                </div>  
                                <div class='clearfix'></div>  
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 二维码 -->
            <div class="modal fade" id="qrcodeModal" tabindex="-1" role="dialog"  >
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t("newDS", "Qr-codes list");?></h4>
                    </div>
                    <div class="modal-body">
                        <div class='col-md-6 col-sm-6 text-center'>
                            <p><strong><?php echo Yii::t("newDS", "For Preview Only");?></strong> </p>                   
                            <div><?php echo Yii::t("newDS", "Don't Share");?></div>        
                            <div class='mt20'>
                                <img class='qrcodeImg' :src="previewQrcode" alt="">
                            </div>    
                            <div class='mt15 mb20'>
                                <button type="button" class="btn btn-primary"  @click='shareImg("view")'><?php echo Yii::t("reg", "Beautify this QrCode");?></button>
                            </div>       
                        </div>
                        <div class='col-md-6 col-sm-6 text-center'>
                            <p><strong><?php echo Yii::t("newDS", "For Parent Sharing");?></strong></p>                   
                            <div><?php echo Yii::t("newDS", "Need Parent Identity");?></div> 
                            <div class='mt20'>
                                <img class='qrcodeImg' :src="shareQrcode" alt="">
                            </div> 
                            <div class='mt15 mb20'>
                                <button type="button" class="btn btn-primary" @click='shareImg("parent")'><?php echo Yii::t("reg", "Beautify this QrCode");?></button>
                            </div>            
                        </div>
                        <div class='clearfix'></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    </div>
                    </div>
                </div>
            </div>
            <div class="modal fade" id="shareModal" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t("reg", "QrCode template");?></h4>
                    </div>
                    <div class="modal-body">
                        <?php $this->renderPartial("share");?>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    </div>
                    </div>
                </div> 
            </div>
            <!-- 阅读统计 -->
            <div class="modal fade" id="readModal" tabindex="-1" role="dialog"  >
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t("newDS", "Read Report");?></h4>
                    </div>
                    <div class="modal-body">
                    <div class='loading'  v-if='readLoading'>
                        <span></span>
                    </div>
                        <div v-if='targetsChildId!=""'>
                            <div class='col-md-8 pb20'  style='border-right:1px solid #E4E7EDFF'>
                                <div class='col-md-12'>
                                    <div class='flex mt10' >
                                        <div style='width:85px'>
                                            <img :src="reviewChilds[targetsChildId].avatar" style='width:70px;height:70px' class='media-object img-circle image' alt="">
                                        </div>
                                        <div class='flex1 mt20'>
                                            <p class='font14 color3 mb5'><strong>{{reviewChilds[targetsChildId].name}}</strong> </p>
                                            <div class='font12 color6'>{{reviewChilds[targetsChildId].class_name}}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class='col-md-6' v-for="(list,index) in targetsParent">
                                    <p class='font14 color3 mb10 relative mt15'><span class='point'></span><span class='ml15'>{{list.parent_relationship_name}}</span> </p>
                                    <div class='ml15'>
                                        <p class='mb10 font14 flex'><span style='width:15px;padding-top: 3px;' class='glyphicon glyphicon-user color6'></span><span class='ml10 flex1'>{{list.parent_name}}</span></p>
                                        <p class='mb10 font14 color6 flex' ><span style='width:15px;padding-top: 3px;' class='glyphicon glyphicon-earphone'></span> <span class='ml10 flex1'>{{list.parent_tel}}</span></p>
                                        <p class='mb10 font14 color6 flex'><span style='width:15px;padding-top: 3px;' class='glyphicon glyphicon-envelope'></span> <span class='ml10 flex1' style='word-break:break-all;'>{{list.parent_email}}</span></p>
                                    </div>
                                </div>
                            </div>
                            <div class='col-md-4 pb20'>
                                <p><strong><?php echo Yii::t("newDS", "Wechat message receivers");?></strong>  </p>
                                <div class=' scroll-box' style='max-height:185px;overflow-y:auto' v-if='wxReceivers && wxReceivers.length!=0'>
                                    <div class='flex mt10'  v-for='(list,index) in wxReceivers'>
                                        <div style='width:50px'>
                                            <img v-if='list.headimgurl!=""' :src="list.headimgurl" style='width:40px;height:40px' class='media-object img-circle image' alt="">
                                            <img v-else src="http://m2.files.ivykids.cn/cloud01-file-8025768Fn6dkbMWLydi9RSVfmuPHU2CITQ6.png" style='width:40px;height:40px' class='media-object img-circle image' alt="">
                                        </div>
                                        <div class='flex1'>
                                            <p><span class="label label-info" v-if='list.isDev'>IT DEV</span> <strong> {{list.nickname}}</strong></p>
                                            <div><?php echo Yii::t("newDS", "Recent received:");?> {{list.recent}}</div>
                                        </div>
                                    </div>
                                </div>
                                <div v-else class="alert alert-warning" role="alert"><?php echo Yii::t("newDS", "No messsage sent successfully in recent 60 days.");?></div>
                            </div>
                        </div>
                        <div class='col-md-12' :style='targetsChildId!=""?"border-top:1px solid #E4E7EDFF":""'>
                            <div class='col-md-6 col-sm-6 text-center ' :style='clientWidth>"750"?"border-right:1px solid #E4E7EDFF":""'>     
                                <div id='echart' style='height:400px;width:100%'></div>     
                            </div>
                            <div class='col-md-6 col-sm-6 scroll-box' style='max-height:400px;overflow-y:auto;padding:5px 18px'>
                                <p class='font14 color3 mb5'><?php echo Yii::t("newDS", "Subscribers List");?></p>
                                <div class='pt10' style='padding-left:20px' v-if='reviewTargets.length!=0'>
                                    <div class='pull-left flex wechat' v-for='(list,index) in reviewed'  style='width:50px;height:50px;text-align:center;' >
                                        <span class='inline-block' :class='list==targetsChildId?"readBorder":""' style='margin:8px 10px'>
                                            <img class='img-circle childImage cur-p'  onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' :title="`<div>${reviewChilds[list].name}</div>${reviewChilds[list].class_name}`"  data-placement="top" @click='childInfo(list)'  :src="reviewChilds[list].avatar" alt="">
                                        </span>
                                    </div>
                                    <div class='pull-left flex wechat' v-for='(list,index) in unreadList'  style='width:50px;height:50px;text-align:center;' >
                                        <span class='inline-block' :class='list==targetsChildId?"readBorder":""'  style='margin:8px 10px;opacity:0.4;'>
                                            <img class='img-circle childImage cur-p'  onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' :title="`<div>${reviewChilds[list].name}</div>${reviewChilds[list].class_name}`"  data-placement="top"  @click='childInfo(list)' :src="reviewChilds[list].avatar" alt="">
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class='clearfix'></div>
                    </div>
                    </div>
                </div>
            </div>
            <div class="modal fade" id="parentReplyModal" tabindex="-1" role="dialog" >
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title" id="parentReplyLabel">{{readData.title}}</h4>
                        </div>
                        <div class="modal-body">
                            <div class="well well-sm">  
                                <span class='text-muted mr10'><?php echo Yii::t("newDS", "Not-Read ");?><?php echo Yii::t("newDS", "(Gray)");?> </span> 
                                <a href="javascript:;" class='mr10'><?php echo Yii::t("newDS", "Read ");?><?php echo Yii::t("newDS", "(Blue)");?></a> 
                            </div>
                            <div class="panel panel-default" v-for='(list,index) in reviewedChild'>
                                <div class="panel-heading">
                                    {{list.title}}
                                </div> 
                                <div class="panel-body row">
                                    <ul class="nav nav-pills">
                                        <li  v-for='(item,key) in list.item'>
                                            <a target="_blank" :class='readChildid(item.id,"view")?"":"text-muted"' :href="'<?php echo $this->createUrl('/child/index/index'); ?>&childid='+item.id" class="labels">{{item.name}}
                                            </a>
                                        </li>
                                    </ul> 
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
$(function () { $(".childImage").tooltip({html : true })});
    var data = <?php echo json_encode($data); ?>;
    var type = <?php echo json_encode($type); ?>;
    var toReplyNum = '<?php echo $this->toReplyNum; ?>';
    var container = new Vue({
        el: "#container",
        data: {
            journalTypeList: data.journalTypeList,
            gradeGroupList: data.gradeGroupList,
            managetypeList: data.managetypeList,
            classList: data.classList,
            leaderData: data.leaderList,
            subjectList:data.subjectList,
            toReplyNum:toReplyNum,
            journalCategory:data.journalCategory,
            teacherList: [],
            leaderList: [],
            leaderPage: {},
            teacherPage: {},
            leaderCopyPage: {},
            teacherCopyPage: {},
            teacherPageNum:1,
            leaderPageNum:1,
            leaderuserList: {},
            teacheruserList: {},
            teacherFilter: {
                'all': '<?php echo Yii::t("dashboard", "All");?>',
                // 'messages': '<?php echo Yii::t("newDS", "Only direct messages");?>',
                // 'journals': '<?php echo Yii::t("newDS", "Only normal journals");?>',
                'noAssigned': '<?php echo Yii::t("newDS", "No subscribers assigned");?>',
                'isMine': '<?php echo Yii::t("newDS", "Only my publish");?>',
                // 'myClass': '<?php echo Yii::t("newDS", "Only my class publish");?>'
            },
            teacherFilterType: 'all',
            listType: ["teacher", "leader"],
            gradeGroupType: 'all',
            teacherLoading:false,
            leaderLoading:false,
            contentData:{},
            loading:false,
            attachments:[],
            previewQrcode:'',
            shareQrcode:'',
            reviewChilds:{},
            reviewed:[],
            unreadList:[],
            reviewTargets:[],
            targetsChildId:'',
            targetsParent:[],
            readLoading:false,
            clientWidth:'',
            readData:{},
            reviewedChild:[],
            parentReviewed:[],
            wxReceivers:[],
            shareList:{},
            shareType:'',
            showSummary:true,
            showPublisher:true,
            htmlContent:1,
            sharetUser:'',
            school_title:'',
            forwardCategory:'',
            postObjectList:{},
            search:''
        },
        watch: {},
        created: function() {
            // $('#readModal').modal()
            let that = this
            for (var i = 0; i < this.listType.length; i++) {
                this.getList(that.listType[i], '')
            }
        },
        computed: {
            searchData: function() {
                var search = this.search;
                var searchVal = ''; //搜索后的数据
                if(search) {
                    searchVal =Object.values(this.postObjectList).filter(function(product) {
                        return Object.keys(product).some(function(key) {
                            return String(product['name']).indexOf(search) !== -1;
                        })
                    })
                    return searchVal;
                }
                return this.postObjectList;
            }
        },
        methods: {
            shareImg(type){
                this.htmlContent=1
                this.showPublisher=true
                this.shareType=type
                $('#shareModal').modal('show')
            },
            newDate(dateTime){
                let time = new Date(dateTime).getTime();
                const date = new Date(time);
                const Y = date.getFullYear() + '-';
                const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
                const D = (date.getDate() < 10 ? '0'+date.getDate() : date.getDate()) + '  ';
                const h = (date.getHours() < 10 ? '0'+date.getHours() : date.getHours()) + ':';
                const m = (date.getMinutes() <10 ? '0'+date.getMinutes() : date.getMinutes()) + ':';
                const s = date.getSeconds()<10 ? '0'+date.getSeconds() : date.getSeconds() // 秒
                const dateString = Y + M + D + h + m + s;
                return dateString;
            },
            classId(id) {
                var title
                this.classList.forEach(item => {
                    if (item.classid == id) {
                        title = item.title
                    }
                })
                return title
            },
            leaderUser(id, type) {
                var data
                this.leaderuserList.forEach(item => {
                    if (item.uid == id) {
                        data = item[type]
                    }
                })
                return data
            },
            teacherUser(id, type) {
                var data
                for(var i=0;i<this.teacheruserList.length;i++){
                    if (this.teacheruserList[i].uid == id) {
                        data = this.teacheruserList[i][type]
                    }
                }
                return data
            },
            getList(typedata, filter, page) {
                var _this = this;
                if(page){
                    _this[typedata + 'Loading'] = true
                }else{
                    _this.loading = true
                }
                var dataList
                if (typedata == 'teacher') {
                    dataList = {
                        type: typedata,
                        isMine: filter == 'isMine' ? '1' : '',
                        myClass: filter == 'myClass' ? '1' : '',
                        messages: filter == 'messages' ? '1' : '',
                        journals: filter == 'journals' ? '1' : '',
                        noAssigned: filter == 'noAssigned' ? '1' : '',
                        pageNum: page ? this.teacherPageNum : 1,
                    }
                } else {
                    dataList = {
                        type: typedata,
                        gradeGroup: filter=='all'?'':filter,
                        pageNum: page ? this.leaderPageNum : 1,     
                    }
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("getList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: dataList,
                    success: function(data) {
                        if (data.state == 'success') {
                            _this.school_title=data.data.school_title
                            if(data.data.list!=null){
                                _this[typedata+'List']=data.data.list
                            }
                            _this[typedata + 'CopyPage'] = data.data.pagination
                            _this[typedata + 'userList'] = data.data.userList
                            if(page){
                                _this[typedata + 'Loading'] = false
                            }else{
                                _this[typedata + 'Page'] = data.data.pagination
                                _this[typedata + 'PageNum'] = 1
                                _this.initPage(typedata)
                                _this.loading = false
                            }
                        }
                    },
                    error: function(data) {
                        if(page){
                            _this[typedata + 'Loading'] = false
                        }else{
                            _this.loading = false
                        }
                    },
                })
            },
            initPage(typedata){
                var _this = this;
                _this[typedata + 'CopyPage']=JSON.parse(JSON.stringify(_this[typedata + 'Page']))
                if(_this[typedata + 'CopyPage'].pages>=10){
                    _this[typedata + 'Page'].pages=[1,2,3,4,5,6,7,8,9,10]
               }else{
                    var numPage=[]
                    for(var i=1;i<=_this[typedata + 'CopyPage'].pages;i++){
                        numPage.push(i)
                    }
                    _this[typedata + 'Page'].pages=numPage
                
               }
            },
            plus(type, index) { 
                var _this = this;
                _this[type + 'PageNum'] = Number(index)
                this.pagesSize(type)
                if (type == 'teacher') {
                    this.getList(type, this.teacherFilterType, 'page')
                } else {
                    this.getList(type, this.gradeGroupType, 'page')
                }
            },
            pagesSize(type){
                var _this = this;
                if(_this[type + 'PageNum']<10){
                    if(_this[type + 'CopyPage'].pages>=10){
                        _this[type + 'Page'].pages=[1,2,3,4,5,6,7,8,9,10]
                    }else{
                        var numPage=[]
                        for(var i=1;i<=_this[type + 'CopyPage'].pages;i++){
                            numPage.push(i)
                        }
                        _this[type + 'Page'].pages=numPage
                    }
                }else if(_this[type + 'PageNum']<=_this[type + 'CopyPage'].pages){
                    if(_this[type + 'CopyPage'].pages-_this[type + 'PageNum']>=4){
                        var minPage=_this[type + 'PageNum']-5
                        var maxPage=_this[type + 'PageNum']+4
                    }else{
                        var minPage=_this[type + 'PageNum']-(9-((_this[type + 'CopyPage'].pages-_this[type + 'PageNum'])))
                        var maxPage=_this[type + 'PageNum']+(_this[type + 'CopyPage'].pages-_this[type + 'PageNum'])
                    }
                    var numPage=[]
                    for(var i=minPage;i<=maxPage;i++){
                        numPage.push(i)
                    }
                    _this[type + 'Page'].pages=numPage
                }
            },
            next(type, index){
                var _this = this;
                _this[type + 'PageNum'] = Number(index) + 1
                this.pagesSize(type)
                if (type == 'teacher') {
                    this.getList(type, this.teacherFilterType, 'page')
                } else {
                    this.getList(type, this.gradeGroupType, 'page')
                }
            },
            prev(type, index) {
                var _this = this;
                _this[type + 'PageNum'] = Number(index) - 1
                this.pagesSize(type)
                if (type == 'teacher') {
                    this.getList(type, this.teacherFilterType, 'page')
                } else {
                    this.getList(type, this.gradeGroupType, 'page')
                }
            },
            viewContent(list){
                this.contentData=list
                var that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("directMessage/view") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        journal_id: list._id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           that.contentData=data.data
                           $('#contentModal').modal('show')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            showMore(list){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("directMessage/subscribers") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        journal_id:list.journal_id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.postObjectList=data.data
                            $('#postObjectModal').modal('show')
                        }
                    },
                    error: function(data) {
                        
                    },
                })
            },
            oneQrcode(list,type){
                let that=this
                that.sharetUser=type
                $.ajax({
                    url: '<?php echo $this->createUrl("getQrCode") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:list._id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.previewQrcode=data.data.preview
                            that.shareQrcode=data.data.share
                            $('#qrcodeModal').modal('show')
                        }
                    },
                    error: function(data) {
                        
                    },
                })
                $.ajax({
                    url: '<?php echo $this->createUrl("getOne") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id: list._id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           that.shareList=data.data
                           that.shareList.school_title=that.school_title
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            reviewedList(list){
                this.clientWidth=document.body.clientWidth
                let that=this
                that.targetsChildId=''
                that.reviewChilds={}
                that.reviewed=[]
                that.reviewTargets=[]
                that.unreadList=[]
                $.ajax({
                    url: '<?php echo $this->createUrl("getReviewed") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:list._id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            $('#echart').removeAttr('_echarts_instance_');
                            that.reviewChilds=data.data.childs
                            that.reviewed=[...new Set(data.data.reviewed.map(Number))]
                            for(var i=0;i<data.data.targets.length;i++){
                                if(that.reviewed.indexOf(data.data.targets[i])==-1){
                                    that.unreadList.push(data.data.targets[i])
                                }
                            }
                            that.reviewTargets=data.data.targets
                            let noRead=data.data.targets.length-data.data.reviewed.length
                            var data=[{value: data.data.reviewed.length, name: '<?php echo Yii::t("newDS", "Read");?>'},
                                {value: noRead, name: '<?php echo Yii::t("newDS", "Unread");?>'},]
                            $('#readModal').modal('show')
                            $('#readModal').on('shown.bs.modal', function (e) {
                                var myCharts = echarts.init(document.getElementById('echart'))
                                var option = that.optionData(myCharts,data)
                                myCharts.setOption(option,true);
                                myCharts.resize()
                            })
                        }
                    },
                    error: function(data) {
                        
                    },
                })
            },
            childInfo(list){
                this.readLoading=true
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getParents") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        child_id:list,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.targetsParent=data.data.parentData
                            that.wxReceivers=data.data.wxReceivers
                            that.targetsChildId=list
                            that.readLoading=false
                        }
                    },
                    error: function(data) {
                        
                    },
                })
            },
            optionData(myCharts, destData){
                var option = {
                    tooltip: {
                        trigger: 'item',
                        formatter: '{a} <br/>{b} : {c} ({d}%)'
                    },
                    legend: {
                        // orient: 'vertical',
                        bottom: 10,
                        left: 'center',
                    },
                    color:['#F8C42DFF','#5B8FF9FF'],
                    series: [
                        {
                            name: '<?php echo Yii::t("newDS", "Read Report");?>',
                            type: 'pie',
                            radius: '55%',
                            center: ['50%', '45%'],
                            data:destData,
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            }
                        }
                    ]
                };
                return option
            },
            leaderRead(list){
                this.readData=list
                this.parentReviewed=[]
                this.reviewedChild=[]
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getReviewed") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:list._id,
                    },
                    success: function(data) {
                        that.reviewedChild=data.data.reviewedChild
                        that.parentReviewed=data.data.reviewed
                        $('#parentReplyModal').modal('show')
                    }
                })
            },
            readChildid(id){
                if(this.parentReviewed.indexOf(id)!=-1){
                    return true
                }
            },
            getUrl(uid) {
                return '<?php echo $this->createUrl('edit'); ?>' + "&staff=" + uid;
            }
        }
    })
</script>