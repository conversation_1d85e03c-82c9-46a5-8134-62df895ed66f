body {
  overflow-x: hidden; /* Prevent scroll on narrow devices */
}
a{
  text-decoration: none !important;
}
a:hover{
  color: #51aded;
}
#wrap{
min-height: 100%;
height: auto;
margin: 0 auto -60px;
padding: 0 0 60px;
}
#wrap > .container {
  padding: 70px 15px 0;
}
.pull-left > .navbar-toggle{
    margin-left:15px !important;
}
.article-cover > h3{margin-top: 6px}

.bs-callout {
    margin: 20px 0;
    padding: 20px;
    border-left: 4px solid #eee;
}

.bs-callout-info {
    background-color: #f4f8fa;
    border-color: #5bc0de;
}
.comment{
    margin-bottom: 20px;
}
.comment-wrapper{margin-left:60px;}
.comment-wrapper > .comment-meta{color:#999}
.comment-wrapper > .comment-main{font-size: 14px;}
/*
 * Off Canvas
 * --------------------------------------------------
 */
.list-group a{
    font-size: 16px;
    /*font-weight: bold;*/
}
.highlight {
    padding: 9px 14px;
    margin-bottom: 14px;
    background-color: #428bca;
    border: 1px solid #428bca;
    border-radius: 4px;
    color:#fff;
}
.highlight a{
    color: #fff
}
.article-view{
    font-size: 16px;
    padding: 20px 0;
    border-top: 2px dotted #f5f5f5;
    border-bottom: 2px dotted #f5f5f5;
}
.shadow-img{border:2px solid #fff; box-shadow: 4px 4px 2px #ccc; -moz-box-shadow: 4px 4px 2px #ccc; -webkit-box-shadow: 4px 4px 2px #ccc; -khtml-box-shadow: 4px 4px 2px #ccc; margin-bottom: 14px;}
.bs-callout h1{
    margin: 8px 0;
}
#article-wrap span.author{padding-left: 4px;}
.article-cover .glyphicon-user{
    margin-right: 4px;
}
.article-meta{
    position: relative;
    z-index: 2;
}
.article-meta h3{
    bottom: 30px;
    position: absolute;
    line-height: 30px;
    max-width: 600px;
    margin: 0;
    padding: 0 10px;
    background: #ff6e27;
}
.article-meta h3 a{
    color: #fff;
}
@media screen and (min-width: 1200px) {
    .sidebar .affix {
        width: 278px;
    }
}
@media screen and (max-width: 1199px) {
    .sidebar .affix {
        width: 227px;
    }
}
@media screen and (max-width: 991px) {
    .sidebar .affix {
        width: 172px;
    }
}
@media screen and (max-width: 767px) {
.bs-callout {
    margin: 10px 0;
    padding: 4px;
    border-left: 4px solid #eee;
}
.bs-callout h1{
    font-size: 24px;
    margin: 8px 0;
}
  .sidebar .affix {
      z-index:999;
      width: 287px;
  }
  .row-offcanvas {
    position: relative;
    -webkit-transition: all 0.25s ease-out;
    -moz-transition: all 0.25s ease-out;
    transition: all 0.25s ease-out;
  }

  .row-offcanvas-right
  .sidebar-offcanvas {
    right: -50%; /* 6 columns */
  }

  .row-offcanvas-left
  .sidebar-offcanvas {
    left: -50%; /* 6 columns */
  }

  .row-offcanvas-right.active {
    right: 50%; /* 6 columns */
  }

  .row-offcanvas-left.active {
    left: 50%; /* 6 columns */
  }

  .sidebar-offcanvas {
    position: absolute;
    top: 0;
    width: 50%; /* 6 columns */
  }
  .row-offcanvas-left .affix{
    left: -50%;
  }
  .row-offcanvas-left.active .affix{
    left: 0;
  }
  .article-view{
    font-size: 14px;
  }
  .shadow-img{
    margin-bottom: 10px;
  }
}
@media screen and (max-width: 639px) {
    .sidebar .affix {
        width: 212px;
    }
}
@media screen and (max-width: 478px) {
    .sidebar .affix {
        width: 138px;
    }
}

