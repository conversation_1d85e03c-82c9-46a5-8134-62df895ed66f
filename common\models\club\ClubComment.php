<?php

/**
 * This is the model class for table "ivy_club_comment".
 *
 * The followings are the available columns in table 'ivy_club_comment':
 * @property integer $id
 * @property integer $userid
 * @property integer $article_id
 * @property integer $parent_id
 * @property integer $mention_uid
 * @property string $content
 * @property integer $created
 * @property integer $status
 * @property integer $privacy
 */
class ClubComment extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return ClubComment the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_club_comment';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('userid, article_id, parent_id, mention_uid, content, created, status, privacy', 'required'),
			array('userid, article_id, parent_id, mention_uid, created, status, privacy', 'numerical', 'integerOnly'=>true),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, userid, article_id, parent_id, mention_uid, content, created, status, privacy', 'safe', 'on'=>'search'),
            array('content', 'length', 'min'=>2),
            array('content', 'match', 'pattern'=>'/^((?!<script).)*$/i', 'message'=>'内容不被允许'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'uname'=>array(self::BELONGS_TO, 'ClubUsers', 'userid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'userid' => 'Userid',
			'article_id' => 'Article',
			'parent_id' => 'Parent',
			'mention_uid' => 'Mention Uid',
			'content' => 'Content',
			'created' => 'Created',
			'status' => 'Status',
			'privacy' => 'Privacy',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('userid',$this->userid);
		$criteria->compare('article_id',$this->article_id);
		$criteria->compare('parent_id',$this->parent_id);
		$criteria->compare('mention_uid',$this->mention_uid);
		$criteria->compare('content',$this->content,true);
		$criteria->compare('created',$this->created);
		$criteria->compare('status',$this->status);
		$criteria->compare('privacy',$this->privacy);

		return new CActiveDataProvider(get_class($this), array(
			'criteria'=>$criteria,
		));
	}
}