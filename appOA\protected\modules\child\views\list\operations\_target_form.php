<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'target-form',
	'enableAjaxValidation'=>false,
	'htmlOptions'=>array('class'=>'J_ajaxForm'),
));
?>
<div class="pop_cont pop_table" style="height:auto;">
    <table width="100%">
        <colgroup>
            <col class="th" />
            <col />
        </colgroup>
        <tbody>
            <tr>
                <th><?php echo $form->labelEx($model,'classid'); ?></th>
                <td><?php echo $model->title; ?></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'fall_target'); ?></th>
                <td><?php echo $form->numberField($model,'fall_target', array('class'=>'input length_2')); ?></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'spring_target'); ?></th>
                <td><?php echo $form->numberField($model,'spring_target', array('class'=>'input length_2')); ?></td>
            </tr>
        </tbody>
    </table>
</div>
<div class="pop_bottom">
    <button type="button" class="btn fr" id="J_dialog_close"><?php echo Yii::t('global','Cancel');?></button>
    <button class="btn fr btn_submit mr10 J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
</div>
<?php $this->endWidget(); ?>