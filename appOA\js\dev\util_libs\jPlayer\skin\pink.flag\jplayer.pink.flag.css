/*
 * Skin for jPlayer Plugin (jQuery JavaScript Library)
 * http://www.jplayer.org
 *
 * Skin Name: Pink Flag
 *
 * Copyright (c) 2011 Happyworm Ltd
 * Dual licensed under the MIT and GPL licenses.
 *  - http://www.opensource.org/licenses/mit-license.php
 *  - http://www.gnu.org/copyleft/gpl.html
 *
 * Author: <PERSON><PERSON><PERSON>
 * Skin Version: 1.0 (jPlayer 2.1.0)
 * Date: 1st September 2011
 */

div.jp-audio,
div.jp-video {

	/* Edit the font-size to counteract inherited font sizing.
	 * Eg. 1.25em = 1 / 0.8em
	 */

	font-size:1.25em; /* 1.25em for testing in site pages */ /* No parent CSS that can effect the size in the demos ZIP */

	font-family:Verdana, Arial, sans-serif;
	line-height:1.6;
	color: #fff;
	border-top:1px solid #554461;
	border-left:1px solid #554461;
	border-right:1px solid #180a1f;
	border-bottom:1px solid #180a1f;
	background-color:#3a2a45;
	position:relative;
}
div.jp-audio {
	width:201px;
	padding:20px;
}


div.jp-video-270p {
	width:480px;
}
div.jp-video-360p {
	width:640px;
}
div.jp-video-full {
	/* Rules for IE6 (full-screen) */
	width:480px;
	height:270px;
	/* Rules for IE7 (full-screen) - Otherwise the relative container causes other page items that are not position:static (default) to appear over the video/gui. */
	position:static !important; position:relative
}

div.jp-video-full div.jp-jplayer {
	top: 0;
	left: 0;
	position: fixed !important; position: relative; /* Rules for IE6 (full-screen) */
	overflow: hidden;
	z-index:1000;
}

div.jp-video-full div.jp-gui {
	position: fixed !important; position: static; /* Rules for IE6 (full-screen) */
	top: 0;
	left: 0;
	width:100%;
	height:100%;
	z-index:1000;
}
div.jp-video-full div.jp-interface {
	position: absolute !important; position: relative; /* Rules for IE6 (full-screen) */
	bottom: 0;
	left: 0;
	z-index:1000;
}

div.jp-interface {
	position: relative;
	width:100%;
	background-color:#3a2a45; /* Required for the full screen */
}


div.jp-audio .jp-interface {
	height: 80px;
	padding-top:30px;
}

/* @group CONTROLS */

div.jp-controls-holder {
	clear: both;
	width:440px;
	margin:0 auto 10px auto;
	position: relative;
	overflow:hidden;
}

div.jp-interface ul.jp-controls {
	background: url("jplayer.pink.flag.jpg") 0 0 no-repeat;
	list-style-type:none;
	padding: 1px 0 2px 1px;
	overflow:hidden;
	width: 201px;
	height: 34px;
}

div.jp-audio ul.jp-controls {
	margin:0 auto;
}

div.jp-video ul.jp-controls {
	margin:0 0 0 115px;
	float:left;
	display:inline; /* need this to fix IE6 double margin */
}

div.jp-interface ul.jp-controls li {
	display:inline;
	float: left;
}
div.jp-interface ul.jp-controls a {
	display:block;
	overflow:hidden;
	text-indent:-9999px;
	height: 34px;
	margin: 0 1px 2px 0;
	padding: 0;
}


/* @group single player controls */

div.jp-type-single  .jp-controls li a{
	width: 99px;
}

div.jp-type-single  .jp-play {
	background: url("jplayer.pink.flag.jpg") 0px -40px no-repeat;
}

div.jp-type-single  .jp-play:hover {
	background: url("jplayer.pink.flag.jpg") -100px -40px no-repeat;
}

div.jp-type-single  .jp-pause {
	background: url("jplayer.pink.flag.jpg") 0px -120px no-repeat;
}

div.jp-type-single  .jp-pause:hover {
	background: url("jplayer.pink.flag.jpg") -100px -120px no-repeat;
}

div.jp-type-single  .jp-stop {
	background: url("jplayer.pink.flag.jpg") 0px -80px no-repeat;
}

div.jp-type-single  .jp-stop:hover {
	background: url("jplayer.pink.flag.jpg") -100px -80px no-repeat;
}

/* @end */

/* @group playlist player controls */

div.jp-type-playlist .jp-controls li a{
	width: 49px;
}

div.jp-type-playlist .jp-play {
	background: url("jplayer.pink.flag.jpg") -24px -40px no-repeat;
}

div.jp-type-playlist .jp-play:hover {
	background: url("jplayer.pink.flag.jpg") -124px -40px no-repeat;
}

div.jp-type-playlist .jp-pause {
	background: url("jplayer.pink.flag.jpg") -24px -120px no-repeat;
}

div.jp-type-playlist .jp-pause:hover {
	background: url("jplayer.pink.flag.jpg") -124px -120px no-repeat;
}

div.jp-type-playlist .jp-stop {
	background: url("jplayer.pink.flag.jpg") -24px -80px no-repeat;
}

div.jp-type-playlist .jp-stop:hover {
	background: url("jplayer.pink.flag.jpg") -124px -80px no-repeat;
}

div.jp-type-playlist .jp-previous {
	background: url("jplayer.pink.flag.jpg") -24px -200px no-repeat;
}

div.jp-type-playlist .jp-previous:hover {
	background: url("jplayer.pink.flag.jpg") -124px -200px no-repeat;
}

div.jp-type-playlist .jp-next {
	background: url("jplayer.pink.flag.jpg") -24px -160px no-repeat;
}

div.jp-type-playlist .jp-next:hover {
	background: url("jplayer.pink.flag.jpg") -124px -160px no-repeat;
}

/* @end */

/* @end */




/* @group TOGGLES */

ul.jp-toggles {
	list-style-type:none;
	padding:0;
	margin:0 auto;
	z-index:20;
	overflow:hidden;
}

div.jp-audio ul.jp-toggles {
	width:55px;
}

div.jp-audio .jp-type-single ul.jp-toggles {
	width:25px;
}

div.jp-video ul.jp-toggles {
	width:100px;
	margin-top: 10px;
}

ul.jp-toggles li{
	display:block;
	float:right;
}

ul.jp-toggles li a{
	display:block;
	width:25px;
	height:18px;
	text-indent:-9999px;
	line-height:100%; /* need this for IE6 */
}

.jp-full-screen {
	background: url("jplayer.pink.flag.jpg") 0 -420px no-repeat;
	margin-left: 20px;
}

.jp-full-screen:hover {
	background: url("jplayer.pink.flag.jpg") -30px -420px no-repeat;
}

.jp-restore-screen {
	background: url("jplayer.pink.flag.jpg") -60px -420px no-repeat;
	margin-left: 20px;
}

.jp-restore-screen:hover {
	background: url("jplayer.pink.flag.jpg") -90px -420px no-repeat;
}

.jp-repeat {
	background: url("jplayer.pink.flag.jpg") 0 -440px no-repeat;
}

.jp-repeat:hover {
	background: url("jplayer.pink.flag.jpg") -30px -440px no-repeat;
}

.jp-repeat-off {
	background: url("jplayer.pink.flag.jpg") -60px -440px no-repeat;
}

.jp-repeat-off:hover {
	background: url("jplayer.pink.flag.jpg") -90px -440px no-repeat;
}

.jp-shuffle {
	background: url("jplayer.pink.flag.jpg") 0 -460px no-repeat;
	margin-left: 5px;
}

.jp-shuffle:hover {
	background: url("jplayer.pink.flag.jpg") -30px -460px no-repeat;
}

.jp-shuffle-off {
	background: url("jplayer.pink.flag.jpg") -60px -460px no-repeat;
	margin-left: 5px;
}

.jp-shuffle-off:hover {
	background: url("jplayer.pink.flag.jpg") -90px -460px no-repeat;
}


/* @end */

/* @group progress bar */

/* The seeking class is added/removed inside jPlayer */
div.jp-seeking-bg {
	background: url("jplayer.pink.flag.seeking.gif");
}

.jp-progress {
	background: url("jplayer.pink.flag.jpg") 0px -240px no-repeat;
	width: 197px;
	height: 13px;
	padding: 0 2px 2px 2px;
	margin-bottom: 4px;
	overflow:hidden;
}

div.jp-video .jp-progress {
	border-top:1px solid #180a1f;
	border-bottom: 1px solid #554560;
	width:100%;
	background-image: none;
	padding: 0;
}

.jp-seek-bar {
	background: url("jplayer.pink.flag.jpg") 0px -260px repeat-x;
	width:0px;
	height: 100%;
	overflow:hidden;
	cursor:pointer;
}

.jp-play-bar {
	background: url("jplayer.pink.flag.jpg") 0px -280px repeat-x;
	width:0px;
	height: 100%;
	overflow:hidden;
}


/* @end */

/* @group volume controls */

div.jp-interface ul.jp-controls a.jp-mute,
div.jp-interface ul.jp-controls a.jp-unmute,
div.jp-interface ul.jp-controls a.jp-volume-max {
	background: url("jplayer.pink.flag.jpg") 0px -330px no-repeat;
	position: absolute;
	width: 16px;
	height: 11px;
}

div.jp-audio ul.jp-controls a.jp-mute,
div.jp-audio ul.jp-controls a.jp-unmute {
	top:-6px;
	left: 0;
}

div.jp-audio ul.jp-controls a.jp-volume-max {
	top:-6px;
	right: 0;
}


div.jp-video ul.jp-controls a.jp-mute,
div.jp-video ul.jp-controls a.jp-unmute {
	left: 0;
	top:14px;
}

div.jp-video ul.jp-controls a.jp-volume-max {
	left: 84px;
	top:14px;
}

div.jp-interface ul.jp-controls a.jp-mute:hover {
	background: url("jplayer.pink.flag.jpg") -25px -330px no-repeat;
}

div.jp-interface ul.jp-controls a.jp-unmute {
	background: url("jplayer.pink.flag.jpg") -60px -330px no-repeat;
}

div.jp-interface ul.jp-controls a.jp-unmute:hover {
	background: url("jplayer.pink.flag.jpg") -85px -330px no-repeat;
}

div.jp-interface ul.jp-controls a.jp-volume-max {
	background: url("jplayer.pink.flag.jpg") 0px -350px no-repeat;
}

div.jp-interface ul.jp-controls a.jp-volume-max:hover {
	background: url("jplayer.pink.flag.jpg") -25px -350px no-repeat;
}

.jp-volume-bar {
	background: url("jplayer.pink.flag.jpg") 0px -300px repeat-x;
	position: absolute;
	width: 197px;
	height: 4px;
	padding: 2px 2px 1px 2px;
	overflow: hidden;
}

.jp-volume-bar:hover {
	cursor:  pointer;
}


div.jp-audio .jp-interface .jp-volume-bar {
	top:10px;
	left: 0;
}

div.jp-video .jp-volume-bar {
	top: 0;
	left: 0;
	width:95px;
	border-right:1px solid #000;
	margin-top: 30px;
}

.jp-volume-bar-value {
	background: url("jplayer.pink.flag.jpg") 0px -320px repeat-x;
	height: 4px;
}

/* @end */

/* @group current time and duration */

.jp-current-time, .jp-duration {
	width:70px;
	font-size:.5em;
	color: #8c7a99;
}

.jp-current-time {
	float: left;
}

.jp-duration {
	float: right;
	text-align:right;
}

.jp-video .jp-current-time {
	padding-left:20px;
}

.jp-video .jp-duration {
	padding-right:20px;
}

/* @end */

/* @group playlist */

.jp-title ul,
.jp-playlist ul {
	list-style-type:none;	
	font-size:.7em;
	margin: 0;
	padding: 0;
}

.jp-video .jp-title ul {
	margin: 0 20px 10px;
}


.jp-video .jp-playlist ul {
	margin: 0 20px;
}

.jp-title li,
.jp-playlist li {
	position: relative;
	padding: 2px 0;
	border-top:1px solid #554461;
	border-bottom:1px solid #180a1f;
	overflow: hidden;
}

.jp-title li{
	border-bottom:none;
	border-top:none;
	padding:0;
	text-align:center;
}


/* Note that the first-child (IE6) and last-child (IE6/7/8) selectors do not work on IE */

div.jp-type-playlist div.jp-playlist li:first-child {
	border-top:none;
	padding-top:3px;
}

div.jp-type-playlist div.jp-playlist li:last-child {
	border-bottom:none;
	padding-bottom:3px;
}

div.jp-type-playlist div.jp-playlist a {
	color: #fff;
	text-decoration:none;
}

div.jp-type-playlist div.jp-playlist a:hover {
	color: #e892e9;
}

div.jp-type-playlist div.jp-playlist li.jp-playlist-current {
	background-color: #26102e;
	margin: 0 -20px;
	padding: 2px 20px;
	border-top: 1px solid #26102e;
	border-bottom: 1px solid #26102e;
}

div.jp-type-playlist div.jp-playlist li.jp-playlist-current a{
	color: #e892e9;
}

div.jp-type-playlist div.jp-playlist a.jp-playlist-item-remove {
	float:right;
	display:inline;
	text-align:right;
	margin-left:10px;
	font-weight:bold;
	color:#8C7A99;
}
div.jp-type-playlist div.jp-playlist a.jp-playlist-item-remove:hover {
	color:#E892E9;
}

div.jp-type-playlist div.jp-playlist span.jp-free-media {
	float: right;
	display:inline;
	text-align:right;
	color:#8C7A99;
}

div.jp-type-playlist div.jp-playlist span.jp-free-media a{
	color:#8C7A99;
}

div.jp-type-playlist div.jp-playlist span.jp-free-media a:hover{
	color:#E892E9;
}
span.jp-artist {
	font-size:.8em;
	color:#8C7A99;
}

/* @end */


div.jp-video div.jp-video-play {
	position:absolute;
	top:0;
	left:0;
	width:100%;
	cursor:pointer;
	background-color:rgba(0,0,0,0); /* Makes IE9 work with the active area over the whole video area. IE6/7/8 only have the button as active area. */
}
div.jp-video-270p div.jp-video-play {
	height:270px;
}
div.jp-video-360p div.jp-video-play {
	height:360px;
}
div.jp-video-full div.jp-video-play {
	height:100%;
	z-index:1000;
}
a.jp-video-play-icon {
	position:relative;
	display:block;
	width: 112px;
	height: 100px;

	margin-left:-56px;
	margin-top:-50px;
	left:50%;
	top:50%;

	background: url("jplayer.pink.flag.video.play.png") 0 0 no-repeat;
	text-indent:-9999px;
}
div.jp-video-play:hover a.jp-video-play-icon {
	background: url("jplayer.pink.flag.video.play.png") 0 -100px no-repeat;
}


div.jp-jplayer audio,
div.jp-jplayer {
	width:0px;
	height:0px;
}

div.jp-jplayer {
	background-color: #000000;
}

/* @group NO SOLUTION error feedback */

.jp-no-solution {
	position:absolute;
	width:390px;
	margin-left:-202px;
	left:50%;
	top: 10px;

	padding:5px;
	font-size:.8em;
	background-color:#3a2a45;
	border-top:2px solid #554461;
	border-left:2px solid #554461;
	border-right:2px solid #180a1f;
	border-bottom:2px solid #180a1f;
	color:#FFF;
	display:none;
}

.jp-no-solution a {
	color:#FFF;
}

.jp-no-solution span {
	font-size:1em;
	display:block;
	text-align:center;
	font-weight:bold;
}

.jp-audio .jp-no-solution {
	width:190px;
	margin-left:-102px;
}

/* @end */
