<style>
    [v-cloak] {
        display: none;
    }
    .dataList{
        background: #FFFFFF;
        border-radius: 8px;
        border: 1px solid #E8EAED;
        padding:24px;
        cursor: pointer;
        height:280px
    }
    .dataList:hover{
        background: rgba(77,136,210,0.08);
        border-radius: 8px;
        border: 1px solid #4D88D2;
    }
    .dropdownLeft{
        right:0;
        left:auto;
        min-width: auto
    }
    .contentAvatar{
        width: 44px;
        height: 44px;
        border-radius: 50%;
        object-fit: cover;
    }
    .optionSearch{
        height:auto
    }
    .imageSearch{
        width: 44px;
        height: 44px;
        object-fit: cover;
    }
    .text_overflow {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        line-height: 14px;
    }
    .addImg{
        width: 24px;
        height: 24px;
        border-radius: 50%;
        object-fit: cover;
        border:1px solid #fff
    }
    .ml-4{
        margin-left:-6px
    }
    .icon{
        line-height:1;
        padding:2px 4px
    }
    .text2{
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        /* height:40px */
    }
    .noStaff{
        color: #F0AD4E;
    }
    .staffName{
        background: #fff;
        border-radius: 2px;
        border: 1px solid #E8EAED;
        padding:2px 4px;
        display:inline-block;
        margin: 4px;
    }
    .staffAdd{
        border: 1px solid #ccc;
        background-color: #eeeeee;
        border-radius: 4px;
        cursor: pointer;
        min-height: 30px;
    }
    .list-complete-item {
        transition: transform .3s;
    }
    .list-item {
        transition: transform .3s;
    }
    .move{
        cursor: move;
    }
    .switch {
        position: relative;
        display: inline-block;
    }
    .switch input {display:none;}
    .slider {
        margin: 0;
        display: inline-block;
        position: relative;
        width: 40px;
        height: 20px;
        border: 1px solid #dcdfe6;
        outline: none;
        border-radius: 10px;
        box-sizing: border-box;
        background: #dcdfe6;
        cursor: pointer;
        transition: border-color .31s,background-color .3s;
        vertical-align: middle;
    }
    .slider:before {
        content: "";
        position: absolute;
        top: 1px;
        left: 1px;
        border-radius: 50%;
        transition: all .3s;
        width: 16px;
        height: 16px;
        background-color: #fff;
    }
    input:checked + .slider {
        background-color: #428bca;
    }
    input:checked + .slider:before {
        left: 100%;
        margin-left: -17px;
    }
    .select{
        line-height: 30px;
        padding-left: 12px;
        color: #555555;
    }
    .red{
        color:#D9534F
    }
    .more{
        width: 24px;
        height: 24px;
        border: 1px solid #fff;
        background: #EBEDF0;
        display: inline-block;
        border-radius: 50%;
        text-align: center;
        line-height: 24px;
        color: #333333;
        font-weight: 600;
        margin-left: -6px;
    }
    label{
        line-height:1.2
    }
    .breakAll{
        word-break: break-all;
    }
    .inputWidth{
        width:100px !important;
    }
    .popperRule{
        padding:0;
        border-radius: 15px;
    }
    .popperRule[x-placement^=bottom] .popper__arrow::after,.popperRule[x-placement^=top] .popper__arrow::after  {
        display:none
    }
</style>
<div class="container-fluid" id='container' v-cloak>
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Advanced'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('site', '家长联系部门管理') ?></li>
    </ol>
    <div class="row">
<!--        <div class="col-md-2 col-sm-12 leftBox ">-->
<!--            <div class="list-group " id="classroom-status-list">-->
<!--                <a href="--><?php //echo $this->createUrl('index'); ?><!--" class="list-group-item status-filter active">--><?php //echo Yii::t("newDS", "部门管理"); ?><!--</a>-->
<!--            </div>-->
<!--        </div>-->
        <div class='col-sm-12 p0'>
            <div class='col-md-12 col-sm-12 mb24'>
                <div class="mb10">
                    <div class="alert alert-info" role="alert">家长在微信端可以主动联系相关部门，部门成员会收到对应的问题反馈，员工可在 <a href="<?php echo $this->createUrl('/mteaching/directMessage/feedback');?>">特殊沟通-家长反馈</a> 中进行回复。</div>
                </div>
                <button type="button" class="btn btn-primary btn-sm" aria-label="Left Align" @click.stop='editData("add")'>
                    <span class="glyphicon glyphicon-plus" aria-hidden="true"></span> 创建部门
                </button>
            </div>
            <transition-group class="listDatas" >
            <div  class='col-md-4 col-sm-12 list-item' v-for='(list,index) in dataList.items':key="list.id">
                <div class='dataList mb24' @click='viewData(list)'  
                        draggable= "true"
                        @dragend="handleDragEnd($event, list)" 
                        @dragstart="handleDragStart(index)"
                        @dragenter="handleDragEnter($event, index,list)"
                        @dragover="handleDragOver($event, index)"
                     >
                    <div class='flex'>
                        <div class='flex1'>
                            <div><label class='color3 font14 mb4'>{{list.name_cn}}</label></div> 
                            <div><label class='color3 font14 mb12'>{{list.name_en}}</label></div>
                        </div>
                        <div>
                            <button type="button" class="btn btn-default icon mr4" aria-label="Left Align" @click.stop='clickPop($event,list)'>
                                <span class='glyphicon glyphicon-qrcode' aria-hidden="true"></span>
                            </button>
                            <button type="button" class="btn btn-default icon mr4" aria-label="Left Align" @click.stop='editData(list)'>
                                <span class='glyphicon glyphicon-edit' aria-hidden="true"></span>
                            </button>
                            <button type="button" class="btn btn-default icon mr4" aria-label="Left Align" @click.stop='delData(list)'>
                                <span class='glyphicon glyphicon-trash' aria-hidden="true"></span>
                            </button>
                            <el-tooltip class="item" effect="dark" content="可拖动" placement="top">
                                <button type="button" class="btn btn-default icon move">
                                    <span class='glyphicon glyphicon-move' aria-hidden="true"></span>
                                </button>
                            </el-tooltip>
                        </div>
                    </div>
                    <div style='height:90px'>
                        <div class='color9 font12 mb4 text2'>{{list.desc_cn}}</div>
                        <div class='color9 font12 mb16 text2'>{{list.desc_en}}</div>
                    </div>
                   
                    <div class='flex align-items mb8'>
                        <span class='glyphicon glyphicon-earphone font14 color6 mr12'></span>
                        <div class='flex1 font14 color3'>{{list.area}}{{list.number}}{{list.ext}}</div>
                    </div>
                    <div class='flex align-items mb16'>
                        <span class='glyphicon glyphicon-envelope font14 color6 mr12'></span>
                        <div class='flex1 font14 color3'>{{list.email}}</div>
                    </div>
                    <div class='flex'>
                        <div class='flex1'>
                            <div v-if='list.staffs.length!=0'>
                                <img class='addImg' v-for='(item,index) in list.staffs.slice(0,5)' :class='index!=0?"ml-4":""' :src="dataList.staffInfo[item].photoUrl" alt="">
                                <span class='more cur-p'  v-if='list.staffs.length>5'>+{{list.staffs.length-5}}</span>
                            </div>
                            <div class='flex align-items font14 noStaff' v-else>
                                <span class='glyphicon glyphicon-info-sign font14 mr10'></span>
                                <div class='flex1 font14'>未添加部门成员</div>
                            </div>
                        </div>
                        <div>
                            <span  @click.stop>
                                <label class="switch">
                                    <input type="checkbox" :checked='list.status==1?true:false' value='' :disabled='list.staffs.length==0?true:false' @change.stop='tabShow(list)'>
                                    <span class="slider"></span>
                                </label>
                                <span class='ml5' style='padding-top:3px'><?php echo Yii::t('ptc', '上线'); ?></span>
                            </span>
                        </div>
                    </div>
                    
                </div>
            </div>
            </transition-group>
        </div>
    </div>
    <el-popover
        v-if='showPop'
        ref='pop'
        :reference='reference'
        placement="bottom"
        width="300"
        trigger="click"
        popper-class='popperRule'
    >
        <div v-if='activeId.id'>
            <img :src="qrcode" alt="" style='width:100%'>
        </div>
    </el-popover>
     <div class="modal fade" id="editModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "创建部门"); ?></h4>
                </div>
                <div class="modal-body" >
                    <div class="form-horizontal" >
                        <div class="form-group">
                            <label for="inputEmail3" class="col-sm-3 control-label">部门名称（中文）：</label>
                            <div class="col-sm-9">
                            <input type="text" class="form-control" v-model='editList.name_cn'  placeholder="请输入">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="inputPassword3" class="col-sm-3 control-label">部门名称（英文）：</label>
                            <div class="col-sm-9">
                            <input type="text" class="form-control" v-model='editList.name_en' placeholder="请输入">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="inputEmail3" class="col-sm-3 control-label">介绍（中文）：</label>
                            <div class="col-sm-9">
                            <textarea class="form-control" rows="3"  v-model='editList.desc_cn'></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="inputPassword3" class="col-sm-3 control-label">介绍（英文）：</label>
                            <div class="col-sm-9">
                                <textarea class="form-control" rows="3"  v-model='editList.desc_en'></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="inputEmail3" class="col-sm-3 control-label">电话：</label>
                            <div class="col-sm-9">
                            <div class="form-inline">
                                <input type="number" class="form-control inputWidth" placeholder="区号" v-model='area'>                               
                                -
                                <input type="number" class="form-control"  placeholder="电话/手机号" v-model='number'>                             
                                -
                                <input type="number" class="form-control inputWidth"  placeholder="分机号" v-model='ext'>                              
                            </div>
                            <!-- <input type="text" class="form-control"  placeholder="请输入"  > -->
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="inputPassword3" class="col-sm-3 control-label">邮箱：</label>
                            <div class="col-sm-9">
                            <input type="text" class="form-control" placeholder="请输入" v-model='editList.email'>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="inputPassword3" class="col-sm-3 control-label">成员：</label>
                            <div class="col-sm-9">
                                <div class='staffAdd' @click='showStaff()'>
                                    <span v-if='editList.staffs && editList.staffs.length!=0'>
                                    <span v-for='(list,index) in editList.staffs' class='staffName'>{{dataList.staffInfo[list].name}}</span>
                                    </span>
                                    <span v-else class='select'>请选择</span>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="inputPassword3" class="col-sm-3 control-label">上线：</label>
                            <div class="col-sm-9">
                                <div class='mt5'>
                                    <label class="switch">
                                        <input type="checkbox" :checked='editList.status==1?true:false' @change='editStatus()'>
                                        <span class="slider"></span>
                                    </label>
                                    <span class='ml5' style='padding-top:3px'><?php echo Yii::t('ptc', '上线'); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button" class="btn btn-primary" @click='saveData'><?php echo Yii::t("newDS", "确定");?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 授权列表 -->
    <div class="modal fade" id="authorizedModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" >
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "添加部门新成员"); ?></h4>
                </div>
                <div class="modal-body">
                    <div >
                        <div class='color6 font14 mt24'>搜索</div>
                        <div  class='flex mt16'>
                            <el-select
                                v-model="staffId"
                                filterable
                                remote
                                class='inline-input flex1'
                                reserve-keyword
                                placeholder="请输入关键词"
                                :remote-method="remoteMethod"
                                prefix-icon="el-icon-search"
                                :loading="loading">
                                <el-option
                                    v-for="item in options"
                                    :key="item.uid"
                                    :label="item.name"
                                    class='optionSearch mb8'
                                    :value="item.uid">
                                    <div class="media">
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                                <img :src="item.photoUrl" data-holder-rendered="true" class="media-object img-circle imageSearch">
                                            </a>
                                        </div> 
                                        <div class="media-body mt5 media-middle">
                                            <h4 class="media-heading font14 color3 text_overflow">{{item.name}}</h4> 
                                            <div class="text-muted text_overflow font12">{{ item.hrPosition }}</div>
                                        </div>
                                    </div>
                                </el-option>
                            </el-select>
                            <button type="button" class="btn btn-primary ml16"  :disabled='staffId==""?true:false' @click='confirmAdd'>确认添加</button>
                        </div>
                        <div v-if='editList.staffs && editList.staffs.length!=0'>
                            <div class='color6 font14 mt24 mb16'><span>已添加的用户</span>  <span class="badge">{{Object.keys(editList.staffs).length}}</span></div>
                            <div class='mb16 col-md-4 col-sm-4' v-for='(list,index) in editList.staffs'>
                                <div class="media bgGrey authorizedMedia" >
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)"><img :src="dataList.staffInfo[list].photoUrl" data-holder-rendered="true" class="contentAvatar"></a>
                                    </div>
                                    <div class="media-right pull-right pt12 text-right">
                                        <span class='el-icon-circle-close font16' @click='delStaff(list,index)'></span>
                                    </div>
                                    <div class="media-body pt4 media-middle">
                                        <div class="lineHeight20  text-primary">
                                            <span class='font14 color3 nowrap'>{{dataList.staffInfo[list].name}}</span>
                                        </div>
                                        <div class="font12 color6 nowrap">{{dataList.staffInfo[list].hrPosition}}</div>
                                    </div>
                                </div>
                            </div>
                            <div class='clearfix'></div>
                        </div>
                        
                    </div> 
                </div>
            </div>
        </div>
    </div>
    <!-- 删除确认框 -->
    <div class="modal fade" id="delModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel">
                        <span><?php echo Yii::t("newDS", "删除"); ?></span> 
                    </h4>
                </div>
                <div class="modal-body" >
                    <div>确认删除吗</div>
                    
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button" class="btn btn-primary" @click='delData()'><?php echo Yii::t("newDS", "确定");?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 详情 -->
    <div class="modal fade" id="detailsModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel">
                        <span><?php echo Yii::t("newDS", "详情"); ?></span> 
                    </h4>
                </div>
                <div class="modal-body" v-if='viewList.name_cn'>
                    <div class='color3 font14 flex align-items'>
                        <div class='flex1'>
                          <div><label >{{viewList.name_cn}}</label> </div>  
                          <div><label >{{viewList.name_en}}</label> </div>  
                        </div>
                        
                        <div>
                            <button class="btn btn-default btn-sm" type="button" @click='editData(viewList)'>修改</button>
                            <button class="btn btn-default btn-sm ml16" type="button"  @click='delData(viewList)'>删除</button>
                        </div>
                    </div>
                    <div class='color9 font14 mt16 mb4 breakAll'>{{viewList.desc_cn}}</div>
                    <div class='color9 font14 mb16 breakAll'>{{viewList.desc_en}}</div>
                    <div class='flex align-items mb8'>
                        <span class='glyphicon glyphicon-earphone font14 color6 mr12'></span>
                        <div class='flex1 font14 color3'>{{area}}{{number}}{{ext}}</div>
                    </div>
                    <div class='flex align-items mb16'>
                        <span class='glyphicon glyphicon-envelope font14 color6 mr12'></span>
                        <div class='flex1 font14 color3'>{{viewList.email}}</div>
                    </div>
                    <div class='font14 color6'>部门成员</div>
                    
                        <div v-if='viewList.staffs.length!=0'>
                            <div class="media mt20" v-for='(list,index) in viewList.staffs'>
                                <div class="media-left pull-left media-middle">
                                    <a href="javascript:void(0)">
                                        <img :src="dataList.staffInfo[list].photoUrl" data-holder-rendered="true" class="contentAvatar">
                                    </a>
                                </div> 
                                <div class="media-body pt8 media-middle">
                                    <h4 class="media-heading font14"><strong>{{dataList.staffInfo[list].name}}</strong></h4> 
                                    <div class="text-muted">{{dataList.staffInfo[list].hrPosition}}</div>
                                </div>
                            </div>
                        </div>
                        <div class='flex align-items font14 noStaff mt16 red' v-else>
                            <span class='glyphicon glyphicon-info-sign font14 mr10'></span>
                            <div class='flex1 font14'>未添加部门成员</div>
                        </div>
                </div>
            </div>
        </div>
    </div>
</div>


<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    $(document).ready(function () {
    // 通过该方法来为每次弹出的模态框设置最新的zIndex值，从而使最新的modal显示在最前面
        $(document).on('show.bs.modal', '.modal', function (event) {
            var zIndex = 1040 + (10 * $('.modal:visible').length);
            $(this).css('z-index', zIndex);
            setTimeout(function() {
                $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');
            }, 0);
        });
    });
    var container = new Vue({
        el: "#container",
        data: {
            toReplyNum:0,
            dataList:{},
            editList:{},
            staffList:[],
            loading:false,
            staffId:'',
            options:[],
            editType:'',
            staffOptions:{},
            viewList:{},
            // 源对象的下标
            dragIndex: '',
            // 目标对象的下标
            enterIndex: '',
            timeout: null,
            copydataList:{},
            area:'',
            number:'',
            ext:'',
            activeId:'',
            reference:{},
            showPop: false,
            qrcode:''
        },
        watch: {},
        created: function() {
            this.init()
        },
        destroyed() {
        // 每次离开当前界面时，清除定时器
            clearInterval(this.timeout)
            this.timeout = null
        },
        methods: {
            clickPop(event,item){
                if (this.activeId === item && this.showPop) return
                this.showPop = false
                this.activeId = item
                this.reference = event.target;
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("deptQrcode") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        deptId: item.id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.qrcode=data.data.qrcode
                            that.$nextTick(() => {
                                that.showPop = true
                                that.$nextTick(() => {
                                    that.$refs.pop.doShow()
                                })
                            })
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
                
            },
            init(){
                let that=this
                this.activeId=''
                $.ajax({
                    url: '<?php echo $this->createUrl("deptList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            data.data.items.forEach((item,index) => {
                                let phone_number=item.phone_number.split('-')
                                if(phone_number[0] && phone_number[0]!=''){
                                    item.area=phone_number[0]+'-'
                                }else{
                                    item.area=''
                                }
                                item.number=phone_number[1]
                                if(phone_number[2] && phone_number[2]!=''){
                                    item.ext='-'+phone_number[2]
                                }else{
                                    item.ext=''
                                }
                            })
                            that.dataList=data.data
                            that.copydataList=JSON.parse(JSON.stringify(data.data));
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            viewData(list){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("deptView") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        deptId:list.id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                        that.viewList=data.data
                        that.viewList.id=list.id
                        let phone_number=data.data.phone_number.split('-') 
                        if(phone_number[0]!=''){
                            that.area=phone_number[0]+'-'
                        }
                        that.number=phone_number[1]
                        if(phone_number[2]!=''){
                            that.ext='-'+phone_number[2]
                        }
                        $('#detailsModal').modal('show')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            editData(data){
                if(data=='add'){
                    this.editList={
                        desc_cn: "",
                        desc_en: "",
                        email:"", 
                        name_cn:"", 
                        name_en:"", 
                        phone_number:"", 
                        staffs:[],
                        id:'',
                        status:'0'
                    }
                    this.area=''
                    this.number=''
                    this.ext=''
                    this.editType='add'
                    this.staffList=[]
                }else{
                    this.editList= JSON.parse(JSON.stringify(data))
                    let phone_number=this.editList.phone_number.split('-') 
                    this.area=phone_number[0]
                    this.number=phone_number[1]
                    this.ext=phone_number[2]
                    this.editType='edit'
                }     
                this.showPop = false           
                $('#detailsModal').modal('hide')
                $('#editModal').modal('show')
            },
            showStaff(){
                this.options = []
                this.staffOptions = {}
                $('#authorizedModal').modal('show')
            },
            remoteMethod(query) {
                let that=this
                if (query !== '') {
                    this.loading = true;
                    $.ajax({
                        url: '<?php echo $this->createUrl("teacherSearch") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            searchString:query
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.options = Object.values(data.data) ;
                                that.staffOptions = data.data ;
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                            that.loading = false;
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.loading = false;
                        },
                    }) 
                } else {
                    this.options = [];
                }
            },
            
            confirmAdd(){
                let that=this
                if(this.editType=='add'){
                    that.editList.staffs.push(that.staffId)
                    that.dataList.staffInfo[that.staffId]={}
                    that.dataList.staffInfo[that.staffId]=this.staffOptions[that.staffId]
                    return
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("deptStaffAdd") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        deptId:this.editList.id,
                        staffId:this.staffId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.editList.staffs.push(that.staffId)
                            that.dataList.staffInfo[that.staffId]={}
                            that.dataList.staffInfo[that.staffId]=data.data
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            delStaff(key,index){
                let that=this
                if(this.editType=='add'){
                    that.editList.staffs.splice(index,1)
                    return
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("deptStaffDel") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        deptId:this.editList.id,
                        staffId:key
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.editList.staffs.splice(index,1)
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            tabShow(item){
                item.status=item.status==1?0:1
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("deptSave") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        deptId:item.id,
                        data:item
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.init()
                            resultTip({
                                msg: data.message
                            });
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: 'data.message'
                        });
                    },
                })
            },
            editStatus(){
                this.editList.status=this.editList.status==1?0:1
            },
            saveData(){
                let that=this
                this.editList.phone_number=this.area+'-'+this.number+'-'+this.ext
                // var patrn = /^1[3456789]\d{9}$/; //手机电话
                // var zjdh =/^((0\d{2,3})-)(\d{7,8})(-(\d{3,}))?$/; //座机电话
                // if(patrn.test(this.editList.phone_number)==false && zjdh.test(this.editList.phone_number)==false){
                //     resultTip({
                //         error: 'warning',
                //         msg: '输入格式错误！请输入正确的联系方式！'
                //     });
                //     return;
                // }
                if(this.editList.staffs.length==0 && this.editList.status==1){
                    resultTip({
                        error: 'warning',
                        msg: '未添加部门成员'
                    });
                    return
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("deptSave") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        deptId:this.editList.id,
                        data:this.editList
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.init()
                            $('#editModal').modal('hide')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            
            delData(data){
                let that=this
                if(data){
                    this.delList=data
                    $('#delModal').modal('show')
                    $('#detailsModal').modal('hide')
                    return
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("deptDel") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        deptId:this.delList.id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.init()
                            $('#delModal').modal('hide')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            scalarArrayEquals(array1,array2) {
                return array1.length==array2.length && array1.every(function(v,i) { return v === array2[i]});
            },
            handleDragEnd(e, item) {
                this.dragging = null
                var fileList=[];
                var copyfileList=[];
                this.dataList.items.forEach((item,index) => {
                    fileList.push(item.id)
                })
                this.copydataList.items.forEach((item,index) => {
                    copyfileList.push(item.id)
                })
                if(this.scalarArrayEquals(fileList,copyfileList)){
                    return
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("deptSort") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        deptIdList:fileList,

                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg:'排序成功'
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })

            },
            handleDragStart(index) {
                this.dragIndex = index
            },
            handleDragEnter(e,index,item) {  
                e.preventDefault();
                this.enterIndex = index
                if( this.timeout !== null) {
                    clearTimeout(this.timeout)
                }
                // 拖拽事件的防抖
                this.timeout = setTimeout(() => {
                    if( this.dragIndex !== index){
                        const source = this.dataList.items[this.dragIndex]
                        this.dataList.items.splice(this.dragIndex,1)
                        this.dataList.items.splice(index, 0 , source )
                        this.dragIndex = index;
                    }
                }, 100);
            },
            handleDragOver(e, index) {
                e.preventDefault();
            }
        }
    })
</script>
