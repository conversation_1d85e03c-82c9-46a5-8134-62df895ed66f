<?php
$nav = $this->nav['resource']['items'][$category]['label'];
$this->breadcrumbs = array(
    Yii::t("navigations", "Resources") => array('/child/resource/index'),
    $nav,
);
?>

<style>
    #catalog{
        float: right;
        border: 1px solid #999;
        background: #fff;
        margin-left: 20px;
        background: #fff;
    }
    #catalog h3{
        padding: 4px 20px;
        background: #E8E7DA;
        color: #666;
        border-bottom: 1px solid #EFEEE5;
        font-size: 14px;
        margin-bottom: 0;
        line-height: 24px;
    }
    #catalog-list{
        margin: 4px 20px 20px;
    }
    #catalog-list li a{
        line-height: 22px;
        text-decoration: none;
        border-bottom: 1px dotted #999;
    }
    #catalog-list li.active a{
        color: #333;
    }
</style>

<div id="inner-main">

    <div id="catalog">
        <h3><?php echo Yii::t("resource", 'Index'); ?></h3>
        <?php
        $this->widget('zii.widgets.CMenu', array(
            'items' => Content::model()->getTitleList($contentAll),
            'id' => 'catalog-list',
        ));
        ?>
    </div>

    <?php
    if ($id && isset($contentAll[$id])) {
        $item = $contentAll[$id];
    } else {
        $item = array_shift($contentAll);
    }

    echo CHtml::openTag('h2');
    echo $item->getLangContent("title");
    echo CHtml::closeTag('h2');

    //echo $item->getLangContent("content");
    ?>

    <?php
    Mims::LoadHelper('HtoolKits');
    foreach ($testimonialList as $tes):
        ?>
        <p class="p-testimonials"><em class="quo"></em>
            <label><?php echo HtoolKits::getContentByLang($tes->title_cn,$tes->title_en); ?></label><br/>
            <img class="photo" src="<?php echo Mims::CreateUploadUrl( 'testimonial/' . $tes->avatar ); ?>">
            <?php echo HtoolKits::getContentByLang(Yii::app()->format->ntext($tes->content_cn), Yii::app()->format->ntext($tes->content_en)); ?>
        </p>

        <div class="clear"></div>
        <?php
    endforeach;
    ?>



</div>

<div class="clear"></div>

<style>
    .p-testimonials {
        border-top: 1px dotted #fff;
        padding-top: 10px;
    }
    .p-testimonials label{
        margin-bottom: 4px;
        font-weight: bold;
        display: inline-block;
    }
    .p-testimonials img.photo{
        float: left;
        border: 5px solid #FFFFFF;
        box-shadow: 2px 2px 3px #666666;
        float: left;
        margin: 0 20px 0 0;
        width: 120px;
    }
</style>