<?php

/**
 * This is the model class for table "ivy_workflow_payment_vendor".
 *
 * The followings are the available columns in table 'ivy_workflow_payment_vendor':
 * @property integer $id
 * @property integer $type
 * @property integer $state
 * @property string $name
 * @property string $bank_name
 * @property string $bank_user
 * @property string $bank_account
 * @property string $social_credit_code
 * @property integer $created_by
 * @property integer $created_at
 * @property integer $updated_by
 * @property integer $updated_at
 */
class PaymentVendor extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_workflow_payment_vendor';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('name, created_by, created_at, updated_by, updated_at', 'required'),
			array('type, state, created_by, created_at, updated_by, updated_at', 'numerical', 'integerOnly'=>true),
			array('name, bank_name, bank_user, bank_account, social_credit_code', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, type, state, name, bank_name, bank_user, bank_account, social_credit_code, created_by, created_at, updated_by, updated_at', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'type' => 'Type',
			'state' => 'State',
			'name' => 'Name',
			'bank_name' => 'Bank Name',
			'bank_user' => 'Bank User',
			'bank_account' => 'Bank Account',
			'social_credit_code' => 'Social Credit Code',
			'created_by' => 'Created By',
			'created_at' => 'Created At',
			'updated_by' => 'Updated By',
			'updated_at' => 'Updated At',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('type',$this->type);
		$criteria->compare('state',$this->state);
		$criteria->compare('name',$this->name,true);
		$criteria->compare('bank_name',$this->bank_name,true);
		$criteria->compare('bank_user',$this->bank_user,true);
		$criteria->compare('bank_account',$this->bank_account,true);
		$criteria->compare('social_credit_code',$this->social_credit_code,true);
		$criteria->compare('created_by',$this->created_by);
		$criteria->compare('created_at',$this->created_at);
		$criteria->compare('updated_by',$this->updated_by);
		$criteria->compare('updated_at',$this->updated_at);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return PaymentVendor the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
