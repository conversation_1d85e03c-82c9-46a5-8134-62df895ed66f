<?php

/**
 * This is the model class for table "ivy_workflow_child_credit".
 *
 * The followings are the available columns in table 'ivy_workflow_child_credit':
 * @property integer $id
 * @property integer $cid
 * @property integer $childid
 * @property string $schoolid
 * @property string $amount
 * @property string $memo
 * @property integer $status
 * @property integer $uid
 * @property integer $updatetime
 */
class WorkflowChildCredit extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_workflow_child_credit';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('schoolid, amount, memo', 'required'),
			array('cid, childid, status, uid, updatetime', 'numerical', 'integerOnly'=>true),
			array('schoolid', 'length', 'max'=>12),
			array('amount', 'length', 'max'=>10),
			array('memo', 'length', 'max'=>200),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, cid, childid, schoolid, amount, memo, status, uid, updatetime', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'child'=>array(self::BELONGS_TO,'ChildProfileBasic','childid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'cid' => 'Cid',
			'childid' => 'Childid',
			'schoolid' => 'Schoolid',
			'amount' => 'Amount',
			'memo' => 'Memo',
			'status' => 'Status',
			'uid' => 'Uid',
			'updatetime' => 'Updatetime',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('cid',$this->cid);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('amount',$this->amount,true);
		$criteria->compare('memo',$this->memo,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('uid',$this->uid);
		$criteria->compare('updatetime',$this->updatetime);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return WorkflowChildCredit the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
