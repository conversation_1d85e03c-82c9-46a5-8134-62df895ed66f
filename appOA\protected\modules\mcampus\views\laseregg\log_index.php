<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('default/index'));?></li>
        <li class="active"><?php echo Yii::t('laseregg','Record management') ;?></li>
    </ol>
    <div class="row">
        <div class="col-md-2">
            <div class="list-group" id="classroom-status-list">
                <a href="<?php echo $this->createUrl('logIndex'); ?>" class="list-group-item status-filter active"><?php echo Yii::t('laseregg','Record management') ;?></a>
                <a href="<?php echo $this->createUrl('index'); ?>" class="list-group-item status-filter "><?php echo Yii::t('laseregg','Sensor management') ;?></a>
            </div>
        </div>
        <div class="col-md-10">
            <div class="mb10">
                <button class="btn btn-primary" onclick="add()"><span class="glyphicon glyphicon-plus"></span> <?php echo Yii::t('laseregg','Add sensor') ;?></button>
            </div>
            <div class="panel panel-default">
                <div class="panel-body">
                <?php
                $this->widget('ext.ivyCGridView.BsCGridView', array(
                    'id'=>'points-product-grid',
                    'afterAjaxUpdate'=>'js:function(){head.Util.ajaxDel()}',
                    'dataProvider'=>$model->search(),
                    'colgroups'=>array(
                        array(
                            "colwidth"=>array(100,100,100,100,100),
                        )
                    ),
                    'columns'=>array(
                        array(
                            'name'=>Yii::t('laseregg','Sensor name'),
                            'value'=>'$data->lasereggInfo->showName()',
                        ),
                        'laseregg_num',
                    	array(
                            'name'=>'add_type',
                            'value'=>'LasereggLog::AUTO==$data->add_type?Yii::t("laseregg","Automatically"):Yii::t("laseregg","Manually")',
                        ),
                        array(
                            'name'=>'log_timestamp',
                            'value'=>'date("Y-m-d H:i", $data->log_timestamp)',
                        ),
	                	array(
	                	    'name'=>Yii::t('laseregg','Edit'),
	                	    'value'=>array($this,'showLogBtn'),
	                	),
                    ),
                )); ?>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- 显示底部学校选择栏 -->
<?php $this->renderPartial('//layouts/common/branchSelectBottom'); ?>
<!-- 模态框 -->
<div class="modal fade" id="modal" tabindex="-1" role="dialog" aria-labelledby="modal">
  <div class="modal-dialog" role="document">
    <div class="modal-content">

    </div>
  </div>
</div>
<!-- script代码 -->
<script>
//添加新记录
function add () {
    $('#modal .modal-content').load('<?php echo $this->createUrl("logAdd"); ?>',function () {
        $('#modal').modal({
          show: true,
          backdrop: 'static'
        });
        head.Util.ajaxForm( $('#modal') );
    });
}

//显示编辑界面
function update (id) {
    $('#modal .modal-content').load('<?php echo $this->createUrl("logUpdate"); ?>'+'&id='+id,function () {
        $('#modal').modal({
          show: true,
          backdrop: 'static'
        });
        head.Util.ajaxForm( $('#modal') );
    });
}

// 回调：商品添加成功
function cbLasereggInfo() {
    $('#modal').modal('hide');
    $.fn.yiiGridView.update('points-product-grid');
}
</script>
