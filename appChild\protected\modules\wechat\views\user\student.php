<div class="weui_cells_title"><?php echo Yii::t('wechat', 'My Children'); ?></div>
<div class="panel">
	<div class="bd">
		<div class="weui_panel weui_panel_access">
			<div class="weui_panel_bd">
				<?php $isOpen = false; ?>
				<?php foreach ($childObjs as $childObj):?>
					<?php if(in_array($childObj->schoolid, array('BJ_OE', 'TJ_SA', 'TJ_EC'))){$isOpen = true;} ?>
					<a href="<?php echo $this->createUrl('info',array('childid'=>$childObj->childid)); ?>" class="weui_media_box weui_media_appmsg">
						<div class="weui_media_hd">
							<?php 
								if($childObj->photo):echo CHtml::image(CommonUtils::childPhotoUrl($childObj->photo, 'small'),'',array('class'=>'weui_media_appmsg_thumb'));endif;?>
						</div>
						<div class="weui_media_bd">
							<h4 class="weui_media_title"><?php echo $childObj->getChildName(); ?></h4>
							<p class="weui_media_desc"><?php echo Yii::t('wechat', 'Birthday: ') . date('Y/m/d', $childObj->birthday); ?></p>
							<?php if ($childObj->school->type == 50 && $childObj->educational_id) :?>
							<p class="weui_media_desc"><?php echo Yii::t('labels', 'Educational Student ID') .Yii::t('global', ': '). $childObj->educational_id; ?></p>
							<?php endif ?>
						</div>
					</a>
				<?php endforeach; ?>
			</div>
			<!-- 接送卡 -->
			<?php if($isOpen): ?>
			<div class="weui_btn_area">
			    <a class="weui_btn weui_btn_primary" href="<?php echo $this->createUrl('cards'); ?>"><?php echo Yii::t('wechat', 'Pick-up Card Manage'); ?></a>
			</div>			
			<div class="weui_btn_area">
				<div class="weui_cells_title"><?php echo Yii::t('wechat', 'No card? click to get ID QRcode for authentication.'); ?></div>
			    <a class="weui_btn weui_btn_primary" href="<?php echo $this->createUrl('checkinQrcode'); ?>"><?php echo Yii::t('wechat', 'Get ID QRcode'); ?></a>
			</div>
			<?php endif; ?>
		</div>
	</div>
</div>