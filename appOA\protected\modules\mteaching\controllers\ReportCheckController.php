<?php

class ReportCheckController extends BranchBasedController
{
    public $actionAccessAuths = array(
        'branch'           => 'o_E_Edu_Common',
        'addTeacher'           => 'o_E_Edu_Common',
        'delTeacher'           => 'o_E_Edu_Common',
    );

    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

        $this->branchSelectParams['urlArray'] = array('//mteaching/reportCheck/index');

        // jquery ui
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue2.js');
        Yii::import('common.models.learning.*');
        Yii::import('common.models.portfolio.*');
        Yii::import('common.models.calendar.*');
    }

    // 显示所有领域
    public function actionIndex()
    {
        $yid = Yii::app()->request->getParam('yid','');
        $semester = Yii::app()->request->getParam('semester', 1);
        $classid = Yii::app()->request->getParam('classid', '');
        $stat = Yii::app()->request->getParam('stat', '');

        $crits = new CDbCriteria;
        $crits->compare('t.branchid', $this->branchId);
        $calendarSchool = CalendarSchool::model()->findAll($crits);
        $schoolList = array();
        $startYearList = array();
        foreach ($calendarSchool as $schoolData){
            $startYearList[$schoolData->yid] = $schoolData->startyear;
            $endTime = $schoolData->startyear + 1;
            $schoolList[$schoolData->yid] = $schoolData->startyear . ' - ' . $endTime;
            if($schoolData->is_selected){
                $nowYid = $schoolData->yid;
            }
        }
        $yid = ($yid) ? $yid : $nowYid;

        $crits = new CDbCriteria;
        $crits->compare('t.schoolid', $this->branchId);
        $crits->compare('t.yid', $yid);
        $crits->compare('t.semester', $semester);
        if($classid){
            $crits->compare('t.classid', $classid);
        }
        if($stat){
            $crits->compare('t.stat', $stat);
        }else{
            $crits->compare('t.stat', array(14,15,16,20));
        }
        $srReportModel = SReport::model()->findAll($crits);

        $reportList = array();
        $childNames = array();
        $classModel = array();
        $userName = array();
        if($srReportModel){
            $config = SReport::getStatConfig();
            foreach ($srReportModel as $val){
                $reportList[$val->classid][] = array(
                    'reportid' => $val->id,
                    'childid' => $val->childid,
                    'timestamp' => date("Y-m-d H:i:s", $val->timestamp),
                    'stat' => $val->stat,
                    'statName' => $config[$val->stat],
                    'uid' => $val->uid,
                );
                $childids[$val->childid] = $val->childid;
                $classes[$val->classid] = $val->classid;
                $uids[$val->uid] = $val->uid;
            }
            $childModel = ChildProfileBasic::model()->findAllByPk($childids);

            // 孩子姓名
            foreach ($childModel as $child){
                $childNames[$child->childid] = $child->getChildName();
            }

            // 操作用户名字
            $userModel = User::model()->findAllByPk($uids);
            foreach ($userModel as $user){
                $userName[$user->uid] = $user->getName();
            }

            //班级信息
            $crit = new CDbCriteria();
            $crit->compare('classid', $classes);
            $crit->order='child_age ASC, title ASC';
            $classModel = IvyClass::model()->findAll($crit);
        }

        $classsModle = IvyClass::getClassList($this->branchId, $yid);
        $classData = array();
        foreach ($classsModle as $class){
            $classData[$class->classid] = $class->title;
        }
        $previewurl = 'previewReport2';
        // IBS 学校特殊处理
        if ($this->branchObj->group == 20 && ($startYearList[$yid] + $semester) > 2020) {
            $previewurl = 'previewReport3';
        }
        $this->render('index', array(
            'schoolList' => $schoolList,
            'reportList' => $reportList,
            'yid' => $yid,
            'branchId' => $this->branchId,
            'semester' => $semester,
            'childNames' => $childNames,
            'classModel' => $classModel,
            'classData' => $classData,
            'classid' => $classid,
            'stat' => $stat,
            'userName' => $userName,
            'previewurl' => $previewurl,
        ));
    }

    // 上线下线操作
    public function actionUpdateStatus()
    {
        $type = Yii::app()->request->getParam('type','');
        $reportid = Yii::app()->request->getParam('reportid','');
        if($type && $reportid){
            $crit = new CDbCriteria();
            $crit->compare('branchId', $this->branchId);
            $crit->compare('uid', $this->staff->uid);
            $count = SreportCheck::model()->count($crit);
            if(!$count){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('global','No permission, please contact EDU team.'));
                $this->showMessage();
            }

            $model = SReport::model()->findByPk($reportid);
            if($model->stat == 20){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', '非法操作'));
                $this->showMessage();
            }
            if($type == 'offline'){
                $model->stat = SReport::STATUS_CHECK;
            }
            if($type == 'online'){
                $model->pdf_file = 0;
                $model->stat = SReport::STATUS_PASS;
            }
            if($type == 'overrule'){
                $model->stat = SReport::STATUS_OVERRULE;
            }
            $model->timestamp = time();
            $model->uid = $this->staff->uid;
            $model->save();
            $html = '';
            if($model->stat == SReport::STATUS_PASS) {
                $html = '<a class="btn btn-success J_ajax_del btn-xs" data-msg="' . Yii::t('global', '确定取消通过审核吗?') . '" href="' . $this->createUrl('updateStatus', array('reportid' => $model->id, "type" => "offline")) . '">' . Yii::t("global", "Confirmed") . '</a>';
            }else if($model->stat == SReport::STATUS_CHECK) {
                $html = '<a class="btn btn-primary J_ajax_del btn-xs" data-msg="' . Yii::t('global', '确定通过审核吗?') . '" href="' . $this->createUrl('updateStatus', array('reportid' => $model->id, "type" => "online")) . '">' . Yii::t("labels", "Yes") . '</a> <a class="btn btn-danger J_modal btn-xs" href="' . $this->createUrl('overrule', array('reportid' => $model->id)) . '">' . Yii::t("teaching", "驳回") . '</a>';
            }
            Yii::log(time() . '-'. Yii::app()->user->id . ' - ' . $model->id  . ' - ' . $model->stat, 'info', 'reportCheckUpdateStatus');
            $userModel = User::model()->findByPk($model->uid);
            $config = SReport::getStatConfig();
            $data = array('id' => $model->id, 'stat' => $model->stat, 'statName' => $config[$model->stat], 'html' => $html, 'timestamp' => date("Y-m-d H:i:s", $model->timestamp), 'uid' => $userModel->getName());
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
            $this->addMessage('callback', 'cb_updated');
            $this->addMessage('message', Yii::t('message', 'Success'));
            $this->showMessage();
        }
    }

    public function actionOverrule()
    {
        $reportid = Yii::app()->request->getParam('reportid','');
        $SReport = Yii::app()->request->getParam('SReport','');
        $model = SReport::model()->findByPk($reportid);
        if(Yii::app()->request->isPostRequest){
            if(!$SReport['overrule']){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', '驳回原因不能为空'));
                $this->showMessage();
            }
            $model->stat = 14;
            $model->overrule = $SReport['overrule'];
            $model->timestamp = time();
            $model->uid = $this->staff->uid;
            $model->save();
            Yii::log(time() . '-'. Yii::app()->user->id . ' - ' . $model->id . ' - ' . $model->stat, 'info', 'reportCheckOverrule');
            $html = '';
            $userModel = User::model()->findByPk($model->uid);
            $config = SReport::getStatConfig();
            $data = array('id' => $model->id, 'stat' => $model->stat, 'statName' => $config[$model->stat], 'html' => $html, 'timestamp' => date("Y-m-d H:i:s", $model->timestamp), 'uid' => $userModel->getName());
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
            $this->addMessage('callback', 'cb_updated');
            $this->addMessage('message', Yii::t('message', 'Success'));
            $this->showMessage();
        }
        $this->renderPartial('overrule', array('model' => $model));
    }

    // 安校园显示分配权限老师
    public function actionBranch()
    {
        $branchId = Yii::app()->request->getParam('branchId','');

        // 所有校园
        $crits = new CDbCriteria;
        $crits->compare('t.status', 10);
        $crits->compare('t.type', 20);
        $branches = Branch::model()->with('info')->findAll($crits);
        $model = new SreportCheck();

        $criteria = new CDbCriteria;
        $criteria->compare('branchId', $branchId);
        $criteria->compare('status', 1);
        $srportModel = SreportCheck::model()->findAll($criteria);
        $teacherModel = array();
        if($srportModel){
            $teacherIds = array();
            foreach ($srportModel as $val){
                $teacherIds[] = $val->uid;
            }

            $criteria = new CDbCriteria;
            $criteria->compare('uid', $teacherIds);
            $criteria->index = 'uid';
            $teacherModel = User::model()->findAll($criteria);
        }

        $this->render('branch', array(
            'branches' => $branches,
            'branchId' => $branchId,
            'model' => $model,
            'srportModel' => $srportModel,
            'teacherModel' => $teacherModel,
        ));
    }

    // 增加可以操作老师
    public function actionAddTeacher()
    {
        $this->layout='//layouts/dialog';
        $branchId = Yii::app()->request->getParam('branchId');
        $sreportCheck = Yii::app()->request->getParam('SreportCheck');
        $user = UserProfile::model()->findByPk($sreportCheck['uid']);
        if(!$user){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '没有该老师');
            $this->showMessage();
        }

        if($_POST['SreportCheck']){
            $criteria = new CDbCriteria;
            $criteria->compare('branchId', $branchId);
            $criteria->compare('uid', $user->uid);
            $model = SreportCheck::model()->find($criteria);
            if(!$model){
                $model = new SreportCheck();
                $model->branchId = $branchId;
                $model->uid = $user->uid;
            }
            $model->status = 1;
            $model->updated_at = time();
            $model->updated_by = $this->staff->uid;
            $model->save();
        }

        $this->addMessage('state', 'success');
        $this->addMessage('refresh', true);
        $this->addMessage('message', Yii::t('message', 'Success'));
        $this->showMessage();
    }

    public function actionDelTeacher()
    {
        $id = Yii::app()->request->getParam('id','');
        $model = SreportCheck::model()->findByPk($id);
        if (!$model) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '数据错误');
            $this->showMessage();
        }
        $model->status = 0;
        $model->updated_at = time();
        $model->updated_by = $this->staff->uid;
        $model->save();

        $this->addMessage('state', 'success');
        $this->addMessage('message', 'success');
        $this->addMessage('refresh', true);
        $this->showMessage();
    }


    // 菜单项
    public function getReportMenu()
    {
        $attendMenu = array(
            array('label' => Yii::t('user', '报告审核'), 'url' => array("/mteaching/reportCheck/index")),
            array('label' => Yii::t('user', ' 分配审核人员'), 'url' => array("/mteaching/reportCheck/branch")),
        );
        return $attendMenu;
    }
}
