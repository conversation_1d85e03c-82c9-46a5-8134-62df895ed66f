<?php

/**
 * This is the model class for table "ivy_asa_vendor".
 *
 * The followings are the available columns in table 'ivy_asa_vendor':
 * @property integer $vendor_id
 * @property string $site_id
 * @property string $type
 * @property string $name_cn
 * @property string $name_en
 * @property string $cellphone
 * @property string $fee_merge
 * @property string $email
 * @property integer $active
 * @property integer $ivy_uid
 * @property string $memo
 * @property string $updated
 * @property string $updated_by
 */
class AsaVendor extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_asa_vendor';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(

			array('site_id,type,cellphone,active,updated', 'required'),
			array('active, ivy_uid, fee_merge', 'numerical', 'integerOnly'=>true),
			array('site_id, type, name_cn, name_en, cellphone, email, updated, updated_by', 'length', 'max'=>255),
			array('memo', 'safe'),
			array("email","email"),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('vendor_id, site_id, type, name_cn, name_en, cellphone, email, active, ivy_uid, memo, updated, updated_by, fee_merge', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'vendorInfo' => array(self::BELONGS_TO, 'AsaVendorInfo', "vendor_id"),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'vendor_id' => 'Vendor',
			'site_id' => '学校',
			'type' => Yii::t('asa', 'Vendor Type'),
			'name_cn' => '中文名字',
			'name_en' => '英文名字',
			'cellphone' => Yii::t('asa', 'Cell Phone'),
			'email' => Yii::t('asa', 'Email'),
			'active' => Yii::t('asa', 'Status'),
			'ivy_uid' => '老师id',
			'fee_merge' => '费用合并',
			'memo' => 'Memo',
			'updated' => 'Updated',
			'updated_by' => 'Updated By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('vendor_id',$this->vendor_id);
		$criteria->compare('site_id',$this->site_id,true);
		$criteria->compare('type',$this->type,true);
		$criteria->compare('name_cn',$this->name_cn,true);
		$criteria->compare('name_en',$this->name_en,true);
		$criteria->compare('cellphone',$this->cellphone,true);
		$criteria->compare('email',$this->email,true);
		$criteria->compare('active',$this->active);
		$criteria->compare('ivy_uid',$this->ivy_uid);
		$criteria->compare('memo',$this->memo,true);
		$criteria->compare('updated',$this->updated,true);
		$criteria->compare('updated_by',$this->updated_by,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AsaVendor the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	public function getName()
	{
		$sub = " (%s)";
		$cnName = $this->name_cn;
		$enName = $this->name_en;
		$enName = trim($enName);

		if($enName == "") return $cnName;
		elseif($cnName == "") return $enName;

		switch (Yii::app()->language){
			case "zh_cn":
				return $cnName . sprintf($sub, $enName);
				break;
			case "en_us":
				return $enName . sprintf($sub, $cnName);
				break;
		}
	}
 
    public function getActiveText()
    {
    	return $this->active == 1 ? Yii::t('asa', 'Active') : Yii::t('asa', 'Inactive');
    }
}
