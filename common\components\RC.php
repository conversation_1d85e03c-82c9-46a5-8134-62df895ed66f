<?php
class RC
{
    //通用
    const E_NO_ERROR = 0;
    const E_NO_PERMISSION = 10;
    const E_ILLEGAL_USER = 10010;
    //付费
    const ERR_TC_DEPOSIT_NOT_PAID = 210001;
    const ERR_TC_TUITION_WITH_DEPOSIT_NOT_PAID = 210002;
    const ERR_TC_DEPOSIT_IN_REFUND_PROGRESS = 210003;
    const ERR_TC_PAYMENT_310001 = 310001;
    const ERR_TC_PAYMENT_310002 = 310002;
    const ERR_TC_PAYMENT_310003 = 310003;
    const ERR_TC_PAYMENT_310004 = 310004;
    const ERR_TC_PAYMENT_310005 = 310005;
    const ERR_TC_PAYMENT_310006 = 310006;
    const ERR_TC_PAYMENT_310007 = 310007;
    const ERR_TC_PAYMENT_310008 = 310008;
    const ERR_TC_PAYMENT_310009 = 310009;
    const ERR_TC_PAYMENT_310010 = 310010;
    const ERR_TC_PAYMENT_310011 = 310011;
    const ERR_TC_PAYMENT_310012 = 310012;
    const ERR_TC_PAYMENT_310013 = 310013;
    //lunch
    const E_LUNCH_CANCEL_SUCCESS = 0; 				//操作成功
	const E_LUNCH_CANCEL_NOT_SCHOOLDAY = 1001;		//目标日期不是学天日
	const E_LUNCH_CANCEL_NOT_PAID = 1002;			//目标日期未缴费
	const E_LUNCH_CANCEL_ALREADY_DONE = 1003;		//目标日期已经取消过
	const E_LUNCH_CANCEL_IN_WORKFLOW = 1004;		//目标日期已经在工作流中提交过申请
	const E_LUNCH_CANCEL_NO_REFUND_CONFIG = 1005;	//未找到退费金额配置
	const E_LUNCH_CANCEL_REFUNDED = 1006;			//目标日期退费已经完成，不能恢复
	const E_LUNCH_CANCEL_EXPIRED = 1007;			//其他错误代码
	const E_LUNCH_CANCEL_OTHER = 1009;				//其他错误代码
	const E_LUNCH_CANCEL_DATE = 10011;				//无效日期
	const E_LUNCH_CANCEL_UNCANCELLED = 10012;  //当天没有取消过用餐
    
    private static function Tips()
    {
        return array(
            self::ERR_TC_DEPOSIT_NOT_PAID => Yii::t("payment", '预缴学费还未付款'),
            self::E_ILLEGAL_USER => Yii::t("payment", '非法用户'),
            self::E_NO_PERMISSION => Yii::t("message", 'No permission'),
            self::ERR_TC_TUITION_WITH_DEPOSIT_NOT_PAID => Yii::t("payment", '有使用预交学费的学费账单未完成付款，所以不能开学费账单，请先作废学费账单或完成学费付款'),
            self::ERR_TC_DEPOSIT_IN_REFUND_PROGRESS => Yii::t("payment", '有预缴学费正在退费'),
            self::E_LUNCH_CANCEL_NOT_SCHOOLDAY => Yii::t("lunch", '目标日期不是教学日'),
            self::E_LUNCH_CANCEL_NOT_PAID => Yii::t("lunch", '目标日期未缴费'),
            self::E_LUNCH_CANCEL_ALREADY_DONE => Yii::t("lunch", '目标日期已经取消过'),
            self::E_LUNCH_CANCEL_IN_WORKFLOW => Yii::t("lunch", '目标日期已经在工作流中提交过申请'),
            self::E_LUNCH_CANCEL_NO_REFUND_CONFIG => Yii::t("lunch", '未找到退费金额配置'),
            self::E_LUNCH_CANCEL_REFUNDED => Yii::t("lunch", '目标日期退费已经完成，不能恢复'),
            self::E_LUNCH_CANCEL_DATE => Yii::t("lunch", '无效日期'),
            self::E_LUNCH_CANCEL_OTHER => Yii::t("lunch", '其他错误代码'),
            self::E_LUNCH_CANCEL_EXPIRED => Yii::t("lunch", '只能操作未来教学日的午餐，当天操作请在早上9点前进行'),
            self::E_LUNCH_CANCEL_UNCANCELLED => Yii::t("lunch", '当天没有取消过用餐'),
        );
    }


    public static function getTip($code)
    {
        if ($code){
            $tips = self::Tips();
            return isset($tips[$code]) ? $tips[$code] : $code;
        }
    }
    
    public static function isErrCode($code)
    {
        $pattern = "/^\d{6}$/";
        if (preg_match($pattern, $code))
        {
            return true;
        }
        return false;
    }
}