<style>
 #signature-div {
        width: 100%;
        height: 100%;
        display: none;
        position: fixed;
        left: 0;
        top: 0;
        z-index: 999;
        background-color: white;
    }

    #signature-div p {
        position: absolute;
        bottom: 0;
        left: 50%;
        margin-left: -91px
    }

    .signature-pad {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: white;
    }

    .signCan {
        position: relative;
        -webkit-box-flex: 1;
        -ms-flex: 1;
        flex: 1;
        border: 1px solid #f4f4f4;
    }

    .signBody {
        position: relative;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
        font-size: 10px;
        width: 100%;
        height: 100%;
        max-height: 300px;
        background-color: #fff;
        border-radius: 4px;
        padding: 16px;
    }
    #signature-data img{
        width:100px
    }
    .signText{
        padding:16px;
    }
</style>
<?php echo $this->renderPartial('steps_header', array('currentStep'=>11)); ?>
<div class="container-fluid">
    <h5 class="htitle"><span class="fas fa-file-signature"></span> <?php echo $this->getStepTitle($this->step); ?></h5>
    <?php echo $this->renderPartial('_nouser_header'); ?>
    <div class="edit-content">
    <?php
        $content = RegConfig::getContent($this->regStudent->reg_id, $this->step);
        if ($content) {
            echo CommonUtils::autoLang(base64_decode($content['cn']['safe']), base64_decode($content['en']['safe']));
        }
     ?>
    </div>
    <div>
        <!-- 英文名 -->
        <?php $form = $this->beginWidget('CActiveForm', array(
            'id' => 'sep8-form',
            'enableAjaxValidation' => false,
            'htmlOptions' => array(
                // 'class'=>'J_ajaxForm form-horizontal',
                'role' => 'form',
            ),
        )); ?>
        <div class="form-group">
            <?php echo $this->renderPartial('_nouser_header'); ?>
            <div class="edit-content">
                <?php
                $content = RegConfig::getContent($this->regStudent->reg_id, $this->step);
                if ($content) {
                    echo CommonUtils::autoLang(base64_decode($content['cn']['material']), base64_decode($content['en']['material']));
                }
                ?>
            </div>
            <input type="text" value='1' name='pay' class='hidden'>
        </div>
        <div class="mt-3">
            <button type="submit" class="btn btn-primary btn-lg btn-block examine"><?php echo Yii::t('reg', 'Complete') ?></button>
            <p class="text-black-50 mt-3 text-center  examine_status">

            </p>
        </div>
        <div class="mt-3" style="height: 10px"></div>
        <?php $this->endWidget(); ?>
    </div>
<style>
.hidden{
    display:none
}
</style>