
<style type="text/css">
 [v-cloak] {
	display: none;
}
.att-report th {
	background-color: #f5f5f5;
}
.att-report tbody td {
	text-align: center;
}
th div.progress {
	margin-bottom: 0px;
}
.table{
	color:#606266
}
.table tr th {
	vertical-align:middle !important;
	text-align:center;
	color:#606266
}
.pink{
	background:#FFECEE
}
.progress{
	background:#fff;
}
.loading{
	width:98%;
	height:90%;
	background:#fff;
	position: absolute; 
	opacity: 0.5;
	z-index: 99
}
.loading span{
	width:100%;
	height:60%;
	display:block;
	background: url("<?php echo Yii::app()->theme->baseUrl?>/images/loading.gif")no-repeat center center ;
}
.piont{
	width: 6px;
	height: 6px;
	background: #666666;
	border-radius: 50%;
	display:inline-block
}
</style>
<div id='container'  v-cloak>
	<h3 class='color3 font14'><strong style='font-size:18px' class='mr20'><?php echo Yii::t('attends', 'Grade Attendance'); ?></strong></h3>
	<div class="form-inline mt15 mb20"> 
		<select class="form-control input-sm mb5"  v-model="classId" @change='showData() '>
			<option value="-1" selected  disabled><?php echo Yii::t('global','Please Select') ?></option>
			<option v-for="(list,key,index) in classArr2" :value="list.classid">{{list.name}}</option>
		</select>
	</div>
	<div class='loading'  v-if='showLoading'>
		<span></span>
	</div>
	<table class="table table-bordered att-report" style='table-layout: fixed' v-if='Object.keys(childInfoList).length != 0 '>
		<thead>
			<th width='120'><?php echo Yii::t("attends", "Students");?></th>
			<template v-for='(_item,key,i) in numberTypes'>
				<th v-if='showTh(key)!=""'>
					{{showTh(key)}}
				</th>
			</template>
		</thead>
		<tbody>
			<tr v-for='(list,key,index) in childInfoList'>
				<th>
					<p>{{list.name}}</p>
				</th>
				<template v-for='(_item,keys,i) in item[key]'>
					<td >
						<a href="javascript:;" v-if='_item.total!=0' @click='showStu(keys,key)'>{{_item.total}}</a>
						<span class="label ml5" v-if='_item.total!=0' :class='_item.percent<80?"label-danger":"label-success"'>{{ _item.percent}}%</span>
					</td>
				</template>
			</tr>
		</tbody>
	</table>
	<div class="modal fade" id="childModel" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false">
		<div class="modal-dialog modal-lg" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" ><?php echo Yii::t('attends','Student List') ?> <span id="remarks_t"></span></h4>
				</div>
				<div class="form-horizontal">
					<div class="modal-body">
						<div class='p10'>
							<ul class="nav nav-pills mb20" role="tablist">
								<li role="presentation"  :class='key==type?"active":""'  v-for='(list,key,index) in childData.countList' @click='showStu(key,stuId)'><a href="javascript:;">{{numberTypes[key]}} <span class="badge">{{list}}</span></a></li>
							</ul>
							<div class="alert alert-danger mt20"  v-if='childData.studentInfo && childData.studentInfo.length==0' role="alert"><?php echo Yii::t('asa','No data found.') ?></div>
							<div class='col-md-4 mt10 mb10' v-else v-for='(list,index) in childData.studentInfo' >
								<div class='flex'>
									<div style='width:16px'>
										<span class='piont'></span>
									</div>
									<div class='flex1'>
										<p class='color3 font14'>{{list.target_date}}</p>
										<div class='color6'>#{{list.period}} <span class='ml5'>({{childData.timetableTimes[list.period].timeslot}})</span> </div>
									</div>
								</div>
							</div>
							<div class='clearfix'></div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<script>
    var classArr  = <?php echo CJSON::encode($data['classArr'] ); ?>;
    var classArr2  = <?php echo CJSON::encode($data['classArr2'] ); ?>;
    classArr2.sort((a, b) => {
        if (a.child_age < b.child_age) {
            return -1;
        }
        if (a.child_age > b.child_age) {
            return 1;
        }
        // field1 相等时，按 field2 排序
        if (a.name < b.name) {
            return -1;
        }
        if (a.name > b.name) {
            return 1;
        }
        return 0;
    });
	var container = new Vue({
        el: "#container",
        data: {
			classArr:classArr,
            classArr2:classArr2,
			classId:'-1',
			childInfoList:{},
			item:{},
			showLoading:false,
			childData:{},
			numberTypes:{},
			stuId:'',
			type:''
        },
		created(){
		},
        methods: {
			progress(num,total){
				var len=0
				for(var key in total){
					len+=total[key].total
				}
				var progress=len <= 0? "0" : Math.round((num / len) * 10000) / 100.0;
				return progress
			},
			showTh(id){
				var text=''
				for(var key in this.item){
					if(this.item[key][id]){
						text=this.numberTypes[id]
					}else{
						text=''
					}
				}
				return text
			},
			showData(){
				this.showLoading=true
				let that=this
				$.ajax({
                    url: '<?php echo $this->createUrl("reportR04") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
						class_id:this.classId
                    },
                    success: function(data) {
                        if(data.state=='success'){  
							that.childInfoList=data.data.childInfoList
							that.item=data.data.item
							that.numberTypes=data.data.numberTypes
                            that.showLoading=false
                        }else{
                            that.showLoading=false 
                        }
                    }
                })
			},
			showStu(type,stuId){
				let that=this
				this.stuId=stuId
				this.type=type
				$.ajax({
                    url: '<?php echo $this->createUrl("showStudentAttend") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
						targetDate:this.targetDate,
						childid:this.stuId,
						classId:this.classId,
						type:type
                    },
                    success: function(data) {
                        if(data.state=='success'){  
							that.childData=data.data
                        	$('#childModel').modal('show');
                        }else{
                        }
                    }
                })
			}
		}
    })
</script>
