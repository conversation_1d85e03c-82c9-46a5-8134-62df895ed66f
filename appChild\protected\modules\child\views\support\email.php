<?php
$this->breadcrumbs=array(
	Yii::t("navigations", 'Support')=>array('/child/support'),
	Yii::t("navigations", 'Email Support'),
);?>

<?php if(Yii::app()->user->getIsGuest()):?>

	<?php echo Yii::t("global","Not available to visitors.");?>

<?php else:?>

	<h1><label><?php echo Yii::t("navigations","Email Support") ?></label></h1>
	<p><?php echo Yii::t("support","You will receive the response within one working day.");?></p>

	<?php
		foreach(Yii::app()->user->getFlashes() as $key => $message) {
			echo '<div class="flash-' . $key . '">' . $message . "</div>\n";
		}
	?>

	<div class="form email-campus-bg">
	<?php $form=$this->beginWidget('CActiveForm', array(
		'id'=>'support-form',
		'enableAjaxValidation'=>true,
		'enableClientValidation'=>true,
	)); ?>

		<?php //echo CHtml::errorSummary($model); ?>

		<dl class="row">
			<dt>
			<?php echo CHtml::activeLabelEx($model,'to') ?>
			</dt>
			<dd class="read">
			<?php echo CHtml::activeTextField($model,'to',array("size"=>50,'readonly'=>'readonly','class'=>'readonly')); ?>
			<?php echo CHtml::error($model,'to') ?>
			</dd>
		</dl>
        <?php if(in_array($child->status, array(ChildProfileBasic::STATS_ACTIVE_WAITING,ChildProfileBasic::STATS_ACTIVE)) && $child->classid > 0){ ?>
            <dl class="row">
                <dt>
                    <label>班级老师</label>
                </dt>
                <dd class="read">
                    <label>
                    <input type="checkbox" value="1" name="for_teacher"/>
                        选中后同时发送给班级老师
                    </label>
                </dd>
            </dl>
        <?php } ?>
		<dl class="row">
			<dt>
			<?php echo CHtml::activeLabelEx($model,'cc') ?>
			</dt>
			<dd class="read">
			<?php echo CHtml::activeTextField($model,'cc',array("size"=>50)); ?>
			<?php echo CHtml::hiddenField('parent_email',$model->cc); ?>
			<?php echo CHtml::error($model,'cc') ?>
			</dd>
		</dl>

		<dl class="row">
			<dt>
			<?php echo CHtml::activeLabelEx($model,'subject') ?>
			</dt>
			<dd class="read">
			<?php echo CHtml::activeTextField($model,'subject',array("size"=>50)); ?>
			<?php echo CHtml::error($model,'subject') ?>
			</dd>
		</dl>

		<dl class="row">
			<dt>
			<?php echo CHtml::activeLabelEx($model,'content') ?>
			</dt>
			<dd class="read">
			<?php echo CHtml::activetextArea($model,'content',array("rows"=>10,"cols"=>70)); ?>
			<?php echo CHtml::error($model,'content') ?>
			</dd>
		</dl>

		<dl class="row submit">
			<dt>
			</dt>
			<dd>
			<?php
			if(Yii::app()->user->checkAccess('sendEmail',array("childid"=>$this->childid))){
				echo CHtml::submitButton(Yii::t("support", "Send"), array("class"=>"w100 bigger"));
			}else{
				echo CHtml::submitButton(Yii::t("support", "Send"), array("class"=>"w100 bigger", "disabled"=>"disabled"));
			}
			?>
			</dd>
		</dl>


	<?php $this->endWidget(); ?>
	</div><!-- form -->
<?php endif;?>