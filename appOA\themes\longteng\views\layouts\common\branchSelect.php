<div class="container-fluid">

    <p class="text-center p20">
        <a style="" id="branch-selector" class="btn btn-primary">
            <?php echo Yii::t('campus','Select a Campus');?>
        </a>
    </p>
    <div id="branch-list" class="hidden">
        <ul class="nav nav-pills">
            <?php
            $allbranches = $this->getAllBranch();

            foreach($this->accessBranch as $_branch):
                if(isset($allbranches[$_branch])):
                    if(!$this->branchSelectParams['hideOffice'] || $allbranches[$_branch]['type'] != 10):
                        $text = '<span class="glyphicon glyphicon-tags program'.$allbranches[$_branch]['group'].'"></span> '.$allbranches[$_branch]['title'];
                        ?>
                        <li><?php echo CHtml::link($text, array_merge($this->branchSelectParams['urlArray'], array($this->branchSelectParams['keyId']=>$_branch)));?></li>
                    <?php
                    endif;
                endif;
            endforeach;

            ?>
        </ul>
    </div>


</div>
<style>
    .popover{max-width: 45%}
</style>
<script>
    $(function() {
        var _opts = {placement:'bottom',html:'true',content:$('#branch-list').html()};
        $('a#branch-selector').popover(_opts);
        $('a#branch-selector').popover('show');
    });
</script>