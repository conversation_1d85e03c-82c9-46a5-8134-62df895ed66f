<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                aria-hidden="true">×</span></button>
    <h4 class="modal-title"><?php echo Yii::t('campus', '修改备注信息'); ?></h4>
</div>
<?php
$childType = ChildVacation::getConfig();
unset($childType[40]);
unset($childType[50]);
$form = $this->beginWidget('CActiveForm', array(
    'id' => 'visits-form',
    'enableAjaxValidation' => false,
    'action' => $this->createUrl('saveVacation', array('id' => $model->id)),
    'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form'),
));
?>
<div class="modal-body">
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model, 'child_id'); ?></label>
        <div class="col-xs-9">
            <?php echo $childModel->getChildName(); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model, 'class_id'); ?></label>
        <div class="col-xs-9">
            <?php echo $classModel->title; ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model, 'type'); ?></label>
        <div class="col-xs-9">
            <?php echo $childType[$model->type]; ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model, 'vacation_time_start'); ?></label>
        <div class="col-xs-9">
            <?php echo date("Y-m-d", $model->vacation_time_start); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model, 'vacation_time_end'); ?></label>
        <div class="col-xs-9">
            <?php echo date("Y-m-d", $model->vacation_time_end); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model, 'vacation_reason'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textArea($model, 'vacation_reason', array('class' => 'form-control')); ?>
        </div>
    </div>
</div>
<div class="modal-footer">
    <button type="submit"
            class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
    <button type="button" class="btn btn-default"
            data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
</div>
<?php $this->endWidget(); ?>