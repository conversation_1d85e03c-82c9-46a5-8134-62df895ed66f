@font-face {
  font-family: "iconfont"; /* Project id  */
  src: url('../ttf/iconfont.ttf?t=1665199499212') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 14px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.icon-ellipsis-vertical:before {
  content: "\e76a";
}

.icon-clock-fill:before {
  content: "\e7d7";
}

.icon-plus-circle-fill:before {
  content: "\e7e0";
}

.icon-times-circle-fill:before {
  content: "\e7e1";
  color: #D9534F;
  font-size: 17px;
}

.icon-check-circle-fill:before {
  content: "\e7e2";
  color: #5CB85C;
  font-size: 17px;
}

.icon-clock:before {
  content: "\e819";
}

.icon-plus-circle:before {
  content: "\e822";
}

.icon-times-circle:before {
  content: "\e823";
}

.icon-check-circle:before {
  content: "\e824";
}
.icon-more:before {
  content: "\e602";
}
.icon-a-icon-copy:before {
  content: "\e601";
}
