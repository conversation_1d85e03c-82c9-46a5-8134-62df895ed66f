<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','System Management'), array('/msettings'))?></li>
        <li class="active">校园及办公室</li>
    </ol>

    <div class="row">
        <div class="col-md-1">
            <?php
            $mainMenu = array(
                array('label'=>Yii::t('user','基本信息'), 'url'=>array("//msettings/global/branch","category"=>"basic")),
                array('label'=>Yii::t('user','校园图标'), 'url'=>array("//msettings/global/branch","category"=>"logo")),
                array('label'=>Yii::t('user','账单脚注'), 'url'=>array("//msettings/global/branch","category"=>"bankinfo")),
                array('label'=>Yii::t('user','积分兑换'), 'url'=>array("//msettings/global/branch","category"=>"points")),
                array('label'=>Yii::t('user','班级类型'), 'url'=>array("//msettings/global/branch","category"=>"classcfg")),
                array('label'=>Yii::t('user','假期配置'), 'url'=>array("//msettings/global/branch","category"=>"holidaycfg")),
            );

            $this->widget('zii.widgets.CMenu',array(
                'items'=> $mainMenu,
                'id'=>'pageCategory',
                'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked text-right background-gray'),
                'activeCssClass'=>'active',
                'itemCssClass'=>''
            ));
            ?>
        </div>
        <?php
            if(!empty($category))
            switch($category){
                default:
                    $this->renderPartial('branch/'.$category);
                    break;
            }
        ?>

    </div>

</div>