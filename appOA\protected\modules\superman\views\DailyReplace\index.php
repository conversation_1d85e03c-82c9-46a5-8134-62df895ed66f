<style>
    .switch {
        position: relative;
        display: inline-block;
    }

    .switch input {
        display: none;
    }

    .slider {
        margin: 0;
        display: inline-block;
        position: relative;
        width: 40px;
        height: 20px;
        border: 1px solid #dcdfe6;
        outline: none;
        border-radius: 10px;
        box-sizing: border-box;
        background: #dcdfe6;
        cursor: pointer;
        transition: border-color .31s, background-color .3s;
        vertical-align: middle;
    }

    .slider:before {
        content: "";
        position: absolute;
        top: 1px;
        left: 1px;
        border-radius: 50%;
        transition: all .3s;
        width: 16px;
        height: 16px;
        background-color: #fff;
    }

    input:checked+.slider {
        background-color: #2196F3;
    }

    input:checked+.slider:before {
        left: 100%;
        margin-left: -17px;
    }
</style>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <h1 class="page-header">
                <span class="glyphicon glyphicon-cog"></span> 中学课表按天特殊调整
            </h1>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <div class="col-md-2 form-group">
                <select class="form-control mr20" id="school_year">
                    <option  value=''>请选择学年</option>
                    <?php foreach ($data['calendarSchool'] as $item){ ?>
                         <option value='<?php echo $item['yid'].'-'.$item['startyear']?>'>
                             <?php echo $item['startyear'].'-'.($item['startyear']+1)?>学年
                         </option>
                    <?php }?>
                </select>
            </div>

            <div class="col-md-2">
               <span>被调整的日期：</span><input id="datepicker1" class="form-control" name="date" value="" readonly style="cursor: pointer;width: 50%;display: inline-block">
            </div>
            <div class="col-md-2">
                <span>所上课程日期：</span><input id="datepicker2" class="form-control" name="date" value="" readonly style="cursor: pointer;width: 50%;display: inline-block">
            </div>
            <div class="col-md-2">
                <button class="btn btn-primary mb5" onclick="addData(this)">新增</button>
            </div>
        </div>
        <div class="col-md-12">
            <h3><span class="glyphicon glyphicon-list" style="padding-right:10px"></span>配置列表</h3>
            <table class="table reportTbody">
                <thead>
                <tr>
                    <td>school_id</td>
                    <td>yid</td>
                    <td>学年</td>
                    <td>被调整的日期（所在周号）</td>
                    <td>所上课程日期[星期]</td>
                    <td>是否周六日的换课</td>
                    <td>所上的课程的版本</td>
                    <td>操作</td>
                </tr>
                </thead>
                <tbody class="reportTbody">
                <?php foreach ($data['timetableDailyReplace'] as $item){?>
                    <tr>
                        <td width="150"><?php echo $item['school_id']?></td>
                        <td width="150"><?php echo $item['yid']?></td>
                        <td width="150"><?php echo $item['start_year'].'-'.($item['start_year']+1)?> 学年</td>
                        <td width="150"><?php echo date('Y-m-d',strtotime($item['target_day'])).' (#'.$item['target_weeknum'].')'?></td>
                        <td width="150"><?php echo date('Y-m-d',strtotime($item['source_day']))?> [week-<?php echo $item['replace_day']?>]</td>
                        <td width="150"><?php echo $item['is_weekends'] ? '是' : '否' ?></td>
                        <td width="150"><?php echo $item['replace_schedule']?>课表</td>
                        <td width="150">
                            <button type="button" class="btn btn-primary btn-xs"  onclick='del(<?php echo $item['id']?>)'> 删除</button>
                        </td>
                    </tr>
                <?php }?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
    $('#datepicker1').datepicker({
        dateFormat: 'yy-mm-dd',
    });
    $('#datepicker2').datepicker({
        dateFormat: 'yy-mm-dd',
    });
    function del(id) {
        if (confirm("确定删除吗？")) {
            $.ajax({
                url: '<?php echo $this->createUrl('delDailyReplace'); ?>',
                data: {"id": id},
                type: 'POST',
                dataType: 'json',
                success: function(data) {
                    if (data.state === 'fail') {
                        head.dialog.alert(data.message);
                        return false;
                    }else{
                        resultTip({msg: data.message});
                        location.reload();
                    }
                },
                error: function() {
                    alert("访问异常！");
                }
            });
        }
    }
    function addData(btn) {
        $(btn).attr('disabled', 'disabled');
        var datepicker1 = $('#datepicker1').val();
        var datepicker2 = $('#datepicker2').val();
        var school_year = $('#school_year').val();
        if(!school_year){
            resultTip({
                'error': 'warning',
                'msg': '请选择学年'
            })
            $(btn).removeAttr('disabled');
            return false;
        }
        if (!datepicker1||!datepicker2) {
            resultTip({
                'error': 'warning',
                'msg': '日期不能为空'
            })
            $(btn).removeAttr('disabled');
            return false;
        }
        var url = '<?php echo $this->createUrl('addData'); ?>';
        var params = {
            'time1': datepicker1,
            'time2': datepicker2,
            'school_year':school_year
        }
        $.post(url, params, function(data) {
            if (data.state === 'success') {
                resultTip({
                    msg: data.message
                });
                location.reload();
            } else if (data.state === 'fail') {
                head.dialog.alert(data.message);
            }
            $(btn).removeAttr('disabled');
        }, 'json');
    }
</script>


