<?php $tk = new HtoolKits(); ?>
<div id="contact" class="pop-box">
    <h5>
        <?php echo Yii::t("portfolio", 'Family Directory'); ?>&nbsp;
        <?php
        if (true == $use_class_list) {
            echo CHtml::dropDownList('contact_class', $currentClassid, $class_list,array('onChange'=>"changeContactClass(this);"));
        } else {
            echo $class_list;
        }
        ?>
    </h5>
    <table class="gtab" style="width:90%;" cellpadding="1" cellspacing="1" border="0">
        <tr>
            <th width="20%"><?php echo Yii::t("labels", 'Child Name'); ?></th>
            <th width="30%"><?php echo Yii::t("labels", 'Mobile Phone'); ?></th>
            <th width="30%"><?php echo Yii::t("labels", 'Email'); ?></th>
            <th width="20%"><?php echo Yii::t("labels", 'Telephone'); ?></th>
        </tr>
        <?php
        $i = 0;
        foreach ($ret as $d) {
            ?>
            <tr class="<?php echo (($i++) % 2 == 0) ? "bg1" : "bg2"; ?>">
                <td align="center"><?php echo CHtml::image(CommonUtils::childPhotoUrl($d['photo'], 'small'), $d['name'], array('class'=>'face')) ?><br><span><?php echo $d['name'] ?></span><?php if ($claOpen && $d['statText'] ):?><br><span><?php echo $d['statText'];?></span><?php endif;?></td>
                <td>
                    <div class="info father">
                        <em title="<?php echo Yii::t("labels", "Father's Mobile Number"); ?>"></em>
                        <span><?php echo $d['fmp']; ?></span>
                    </div>
                    <div class="info mother">
                        <em title="<?php echo Yii::t("labels", "Mother's Mobile Number"); ?>"></em>
                        <span><?php echo $d['mmp']; ?></span>
                    </div>
                </td>
                <td>
                    <div class="info father">
                        <em title="<?php echo Yii::t("labels", "Father's Email Address"); ?>"></em>
                        <span><?php echo $d['fmail']; ?></span>
                    </div>
                    <div class="info mother">
                        <em title="<?php echo Yii::t("labels", "Mother's Email Address"); ?>"></em>
                        <span><?php echo $d['mmail']; ?></span>
                    </div>
                </td>
                <td>
                    <div class="info">
                        <span><?php echo $d['tel']; ?></span>
                    </div>
                </td>
            </tr>
        <?php } ?>
    </table>
</div>