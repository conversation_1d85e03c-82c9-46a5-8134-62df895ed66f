<?php

/**
 * This is the model class for table "ivy_monthly_fee_planner".
 *
 * The followings are the available columns in table 'ivy_monthly_fee_planner':
 * @property integer $id
 * @property integer $childid
 * @property integer $startyear
 * @property string $feetype
 * @property string $settings
 * @property integer $userid
 * @property integer $updated
 */
class Monthly<PERSON>eePlanner extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return MonthlyFeePlanner the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_monthly_fee_planner';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('feetype, settings', 'required'),
			array('childid, startyear, userid, updated', 'numerical', 'integerOnly'=>true),
			array('feetype', 'length', 'max'=>64),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, childid, startyear, feetype, settings, userid, updated', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'task' => array(self::HAS_MANY, 'MonthlyFeeTask', 'planid', 'order'=>'month asc', 'condition'=>'schoolid=:schoolid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'childid' => 'Childid',
			'startyear' => 'Startyear',
			'feetype' => 'Feetype',
			'settings' => 'Settings',
			'userid' => 'Userid',
			'updated' => 'Updated',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('startyear',$this->startyear);
		$criteria->compare('feetype',$this->feetype,true);
		$criteria->compare('settings',$this->settings,true);
		$criteria->compare('userid',$this->userid);
		$criteria->compare('updated',$this->updated);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}