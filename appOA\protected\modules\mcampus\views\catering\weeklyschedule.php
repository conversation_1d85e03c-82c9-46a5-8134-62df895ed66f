<style>
    .icon-success {color:#5cb85c;}
    .icon-warning {color:#f0ad4e;}
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
         <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('//mcampus/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Campus Support'), array('//mcampus/default/index'))?></li>
        <li class="active"><?php echo Yii::t('site','周食谱管理');?></li>
    </ol>
    <div class="row">
        <div class="col-md-12">
            <table class="table table-striped table-hover">
                <thead>
                <tr>
                    <td width="5%"><?php echo Yii::t('lunch', 'Week');?></td>
                    <td width="20%" class="nosort"><?php echo Yii::t('lunch', '日期');?></td>
                    <td width="20%" class="nosort"><?php echo Yii::t('lunch', '状态');?></td>
                    <td width="20%" class="nosort"><?php echo Yii::t('lunch', 'Normal Lunch');?></td>
                    <td width="20%" class="nosort"><?php echo Yii::t('lunch', 'Special Lunch');?></td>
                    <td width="15%" class="nosort"><?php echo Yii::t('lunch', '操作');?></td>
                </tr>
                </thead>
                <tbody>
                <?php
                $weekData = array(0=>'待审核', 1=>'已审核');
                foreach($weeks as $week):
                    $commonData = $menuCommon;
                    $allergyData = $menuAllergy;
                    $link_id = isset($schedule[$week->weeknumber]['id']) ? $schedule[$week->weeknumber]['id'] : 0;

                    $weekStatus = isset($schedule[$week->weeknumber]['status']) ? $schedule[$week->weeknumber]['status'] : 0;
                    $commonValue = $schedule[$week->weeknumber]['data']['common']['id'] ? $schedule[$week->weeknumber]['data']['common']['id'] : 0;
                    $allergyValue = $schedule[$week->weeknumber]['data']['allergy']['id'] ? $schedule[$week->weeknumber]['data']['allergy']['id'] : 0;

                    $statusData = '';

                    $btnText = '<span class="glyphicon glyphicon-plus"></span> 添加';
                    $btnText2 = '<span class="glyphicon glyphicon-plus"></span> 添加';
                    $delete = '<span class="glyphicon glyphicon-trash"></span>';

                    if ( isset($schedule[$week->weeknumber]['status']) ) {
                        if ($schedule[$week->weeknumber]['status'] == 1) {
                            $statusData = '<span class="glyphicon glyphicon-ok-sign icon-success"> </span> ' . $weekData[$schedule[$week->weeknumber]['status']] . '<br><small style="color: #999;">'.$schedule[$week->weeknumber]['review_by'].' '.$schedule[$week->weeknumber]['review_at'].'</small>';
                        } else {
                            $statusData = '<span class="glyphicon glyphicon-question-sign icon-warning"> </span> ' . $weekData[$schedule[$week->weeknumber]['status']];
                        }
                        if($commonValue){
                            $btnText = '<span class="glyphicon glyphicon-edit"></span> 编辑';
                        }
                        if($allergyValue){
                            $btnText2 = '<span class="glyphicon glyphicon-edit"></span> 编辑';
                        }

                    }

                    if($commonValue && !in_array($commonValue, $menuCommon)){
                        $commonData += array($commonValue=>$schedule[$week->weeknumber]['data']['common']['title'].'');
                    }
                    if($allergyValue && !in_array($allergyValue, $menuAllergy)){
                        $allergyData += array($allergyValue=>$schedule[$week->weeknumber]['data']['allergy']['title']);
                    }

                ?>
                <tr>
                    <td><?php echo $week->weeknumber;?></td>
                    <td><?php echo ''.date('Y/m/d', $week->monday_timestamp).' - '.date('Y/m/d', ($week->monday_timestamp+345600)).'';?></td>
                    <td><?php echo $statusData ?></td>
                    <td>
                        <?php
                            if($weekStatus){
                                if($commonValue) {
                                    echo CHtml::link($btnText, array('editNew', 'id' => $link_id, 'type' => 'menu', 'week' => $week->weeknumber, 'monday' => $week->monday_timestamp), array('class' => 'btn btn-primary btn-xs'));
                                }else{
                                    echo "<div class='btn btn-primary btn-xs' onclick='onMael()'>".$btnText."</div>";
                                }
                            }else{
                                echo CHtml::link($btnText, array('editNew', 'id' => $link_id, 'type' => 'menu', 'week' => $week->weeknumber, 'monday' => $week->monday_timestamp), array('class' => 'btn btn-primary btn-xs'));
                            }
                        ?>
                        <?php
                        if($commonValue)
                            echo CHtml::link('<span class="glyphicon glyphicon-print"></span> '.Yii::t('global', 'Print'), array('print', 'id'=>$commonValue, 'week'=>$week->weeknumber, 'monday'=>$week->monday_timestamp), array('class'=>'btn btn-default btn-xs', 'target'=>'_blank'));
                        ?>
                    </td>
                    <td>
                        <?php
                        if($weekStatus){
                            if($allergyValue) {
                                echo CHtml::link($btnText2, array('editNew', 'id' => $link_id, 'type' => 'allergy', 'week' => $week->weeknumber, 'monday' => $week->monday_timestamp), array('class' => 'btn btn-primary btn-xs'));
                            }else{
                                echo "<div class='btn btn-primary btn-xs' onclick='onMael()'>".$btnText2."</div>";
                            }
                        }else{
                            echo CHtml::link($btnText2, array('editNew', 'id' => $link_id, 'type' => 'allergy', 'week' => $week->weeknumber, 'monday' => $week->monday_timestamp), array('class' => 'btn btn-primary btn-xs'));
                        }
                        ?>
                        <?php
                            if($allergyValue)
                                echo CHtml::link('<span class="glyphicon glyphicon-print"></span> '.Yii::t('global', 'Print'), array('print', 'id'=>$allergyValue, 'week'=>$week->weeknumber, 'monday'=>$week->monday_timestamp), array('class'=>'btn btn-default btn-xs', 'target'=>'_blank'));
                        ?>
                    </td>
                    <td>
                        <?php echo "<div class='btn btn-danger btn-xs' onclick='onDelete($link_id, $commonValue, $allergyValue,$week->weeknumber )'>".$delete."</div>" ?>
                    </td>
                </tr>
                <?php endforeach;?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<div class="modal fade" tabindex="-1" role="dialog" id='lunchDel'>
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title">删除</h4>
          </div>

            <?php $form=$this->beginWidget('CActiveForm', array(
                'id'=>'log-form',
                'enableAjaxValidation'=>false,
                'htmlOptions'=>array('class'=>'J_ajaxForm'),
                'action' => $this->createUrl("detWeeklink")
            ));
            ?>
          <div class="modal-body">
                <div class="form-horizontal" id='lunchForm'>
                  <div class="form-group">
                    <label for="inputEmail3" class="col-sm-2 control-label">周数:</label>
                    <div class="col-sm-10">
                        <p class="form-control-static">第<span id='delWeek'></span>周</p>
                    </div>
                  </div>
                  <div class="form-group">
                    <label for="inputEmail3" class="col-sm-2 control-label">餐类:</label>
                    <div class="col-sm-10">
                        <input type="text" name='id' id='delLunch' hidden>
                        <div id='htmlLunch'></div>
                    </div>
                  </div>
                </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
            <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global','Submit');?></button>
            <span id="J_fail_info" class="text-warning"></span>
          </div>
          <?php $this->endWidget(); ?>
        </div><!-- /.modal-content -->
      </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>

<script>
    $(document).ready(function() {
        var table = $('.table').DataTable({
            aaSorting: [0, 'asc'], // 默认排序
            paging: false,
            info: false,
            searching: false,
            columnDefs: [{
                "targets": 'nosort',
                "orderable": false
            }],
        });
    });
    function setMenu(_this, type, weeknum, value)
    {
        var id = $(_this).val();
        $.post('<?php echo $this->createUrl('setMenu')?>', {id: id, weeknum: weeknum, type: type}, function(data){
            if(data.state == 'success'){
                var err = 0;
            }
            else{
                var err = 1;
                $(_this).val(value);

            }
            resultTip({msg: data.message, error: err});
        }, 'json');
    }

    function onMael() {
        resultTip({error: 'warning', msg: '已审核！添加请联系后勤部！'});
    }
    function onDelete(id,menu,allergy,week){
        if(menu==0 && allergy==0){
            resultTip({error: 'warning', msg: '未增加不可删除'});
            return
        }
        var menuStr='<label class="checkbox-inline" id="menu"><input type="checkbox" id="inlineCheckbox1" name="type[menu]" checked value="1">正常餐</label>'
        var allergyStr='<label class="checkbox-inline" id="allergy"><input type="checkbox" id="inlineCheckbox1" name="type[allergy]" checked value="1">特殊餐</label>'
        if(menu!=0 && allergy!=0){
            $('#htmlLunch').html(menuStr+allergyStr)
        }else if(allergy!=0){
            $('#htmlLunch').html(allergyStr)
        }else if(menu!=0){
             $('#htmlLunch').html(menuStr)
        }
        $('#delWeek').html(week)
        $('#delLunch').val(id)
        $('#J_fail_info').html('')
        $('#lunchDel').modal('show')
    }
    function cbSuccess(){
        setTimeout(function(){
            location.reload()
        },1000)

    }
</script>
