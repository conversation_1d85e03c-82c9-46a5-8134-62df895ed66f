<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta name="language" content="en" />
</head>
<body>
<script language="javascript">
    function viewSchedule(_this)
    {
        window.location = "<?php echo Yii::app()->getController()->createUrl('//portfolio/portfolio/schedule', array('classid'=>$class_model->classid));?>&weeknum="+_this.value;
    }
    
    var bgColorMapping = {
		188:'#FFDBDB',	//自选活动
		189:'#D5FFC6',	//英文圆圈
		190:'#D2D6FF',	//间点
		191:'#DBF0FF',	//户外活动
		192:'#AFCC6A',	//午餐
		193:'#CCCCCC',	//午睡
		194:'#FFFFDB',	//中文圆圈
		195:'#C55186',	//园外活动
		196:'#DBF0FF',	//特色课
        1624:'#CF9996',	//桌面游戏
        1625:'#D4BfA7',	//日程设置
        1626:'#F6F0EA'	//反思
	};
</script>
<?php
    $scheduleData = isset($schedule->data) ? base64_decode($schedule->data->data) : '';
?>
        <div id="schedule">
            <h5><?php echo addColon(Yii::t("portfolio", 'Weekly Schedule'));?><?php echo $class_model->title;?></h5>
            <div class="sh-tools">
                <div class="fl"><?php echo CHtml::dropDownList('weeknum', $weeknum, $weeks, array('onchange'=>'viewSchedule(this)'));?></div>
                <div class="fr" style="line-height: 24px; margin-right: 20px;">
                    <?php
                    $minw = min(array_keys($weeks));
                    $maxw = max(array_keys($weeks));
                    ?>
                    <?php if ($weeknum != $minw):?>
                    <?php
                    $linkText = "<span class='prev'>" . Yii::t("portfolio", "Prev Week") ."</span>";
                    echo CHtml::link($linkText, Yii::app()->getController()->createUrl('//portfolio/portfolio/schedule', array('classid'=>$class_model->classid, 'weeknum'=>$weeknum-1)), array('class'=>'alink'))?>
                    <?php endif;?>
                    <?php if ($weeknum != $maxw):?>
                    <?php
                    $linkText = "<span class='next'>" . Yii::t("portfolio", "Next Week") ."</span>";
                    echo CHtml::link($linkText, Yii::app()->getController()->createUrl('//portfolio/portfolio/schedule', array('classid'=>$class_model->classid, 'weeknum'=>$weeknum+1)), array('class'=>'alink'))?>
                    <?php endif;?>
                </div>
                <div class="clear"></div>
            </div>
            
            <div id="sLeft">
                <ul><li class="hli">Time</li></ul>

                <!--place holder-->
                <ul class="hul" id="timeNodes"></ul>
            </div>
            <div id="sRight">
                <ul>
                    <?php for ($i = 0; $i <= 4; $i++) { ?>
                        <?php echo CHtml::openTag('li', array('class'=>"hli fl thx daytitle", 'id'=>'wd_'.$i, 'dayindex'=>$i));?>
                        <label><?php echo Yii::app()->locale->getWeekDayName($i + 1); ?></label>
                        <?php echo CHtml::closeTag('li');?>
                    <?php } ?>
                    <li class="clear"></li>
                </ul>
                <?php if($scheduleData){?>
                <ul>
                    <?php for ($i = 0; $i <= 4; $i++) { ?>
                        <li id='day-<?php echo $i?>' class="fl thx J_sde daydata" data-id='<?php echo $i?>'></li>
                    <?php } ?>
                    <li class="clear"></li>
                </ul>
                <?php }else{?>
                    <div class="journal-box" style="width: 600px;margin: 50px auto;padding: 10px; 0;<?php if(Yii::app()->language=='zh_cn'){echo 'text-align:center;';}?>"><?php echo Yii::t('lunch', 'Weekly schedule information for this week is not yet available and are usually uploaded on Fridays at 6pm (except during school holidays).');?></div>
                <?php }?>
            </div>
            <div class="clear"></div>
        </div>

<script type="text/template" id="item-timeslot-template">
<h3><%= title %></h3><% print(nl2br(content)) %>
</script>
    
<script type="text/javascript">
var DisplayLengthRatio = 2.8;
var activityIds = new Array;
var scheduleWeekData = new Array;

<?php
//初始化数据
if(!empty($scheduleData)): ?>
	var dataStr = <?php echo $scheduleData;?>;
	for(var i=0;i<5;i++){
		var dayData = 'scheduleWeekData[i] = ' + dataStr[i] + ';';
		eval( dayData );
	}
<?php else: ?>
	for(var i=0;i<5;i++){
		scheduleWeekData[i] = new Array;
	}
<?php endif; ?>

var DailySchedules = new Array;

function getTimeSlots(classStart, classEnd){
	timeNodes = new Array();
	var i=0;
	var _t = classStart;
	
	var timeNodes = new Array;
	if(_t.getMinutes() != 0 && _t.getMinutes() != 30){
		timeNodes[i] = {
			period: 60 - _t.getMinutes(),
			text: ( ( _t.getHours() < 10) ? '0' + _t.getHours() : _t.getHours() ) + ':' + _t.getMinutes(),
			flag: 'first',
			odd: ( i % 2 ) ? 1 : 0
		};
		_t.setMinutes( 60 );
		i++;
	}
	while(_t < classEnd){
		timeNodes[i] = {
			flag: (i==0)?'first':'',
			period: 30,
			text: ( ( _t.getHours() < 10) ? '0' + _t.getHours() : _t.getHours() ) + ':' + ( ( _t.getMinutes() < 10) ? '0' + _t.getMinutes() : _t.getMinutes() ),
			odd: ( i % 2 ) ? 1 : 0
		};
		_t.setMinutes(_t.getMinutes() + 30, 0, 0);
		i++;
	}
	if(_t.getTime() == classEnd.getTime()){
		timeNodes[--i].flag = 'end';
		timeNodes[i].period = 30;
	}else{
		timeNodes[--i].flag = 'end';
		timeNodes[i].period = (classEnd.getMinutes() > 30 ) ? classEnd.getMinutes() - 30 : classEnd.getMinutes();
	}
	
	return timeNodes;
}

$(function(){
	var classStart = new Date(parseInt(<?php echo ( $classTime['start'] * 1000 );?>));
	var classEnd = new Date(parseInt(<?php echo ( $classTime['end'] * 1000 );?>));
	
	var timeNodes = new Array;
	timeNodes = getTimeSlots(classStart, classEnd);
	var timeNodesCollection = new Backbone.Collection;
	
	var timeNodeView = Backbone.View.extend({
		tagName: 'li',
		className: 'tb',
		attributes:{
		},
		initialize:function(){
			this.className = this.model.get('odd') ? 'bg1' : 'bg2';
		},
		render: function(){
			this.$el.html( _.template('<div class="tbt"><%= text %></div>', this.model.attributes) );
			this.$el.attr('style','height:'+ DisplayLengthRatio * this.model.get('period') + 'px;');
			this.$el.addClass( this.model.get('odd') ? 'bg1' : 'bg2' );
			return this;
		}
	});
	var timeNodesView = Backbone.View.extend({
		el: $("#timeNodes"),
		initialize: function(){
			this.listenTo(timeNodesCollection, 'reset', this.addAll);
			timeNodesCollection.reset(timeNodes);
		},
		addAll: function(){
			timeNodesCollection.each(this.addOne,this);
		},
		addOne: function(timeNode){
			var item = new timeNodeView({model: timeNode});
			this.$el.append(item.render().el);
		}
	});
    new timeNodesView;
    var TimeSlot = Backbone.Model.extend({
		defaults: function(){
			return {
				day: 0,
				sindex: 0,
				period: 0,
				title: '',
				activityId: 0,
				content: '',
				startTime: 0,
				color: ''
			}
		}
	});
	var DailySchedule = Backbone.Collection.extend({
		model: TimeSlot
	});
	var timeSlotView = Backbone.View.extend({
		className: 'timeslot',
		attributes:{
		},
		template: _.template($('#item-timeslot-template').html()),
		initialize:function(){
			this.className = this.model.get('odd') ? 'bg1' : 'bg2';
		},
		render: function(){
			this.$el.html( this.template(this.model.attributes) );
			this.$el.css('height', DisplayLengthRatio * this.model.get('period'));
			this.$el.attr('sindex', this.model.get('sindex'));
			this.$el.attr('day', this.model.get('day'));
			this.$el.attr('id', 'ts-' + this.model.get('day') + '-' + this.model.get('sindex'));
			
			var pid = parseInt(this.model.get('activityId'));
			// if ( 187 < pid && pid < 197 ){
			if ( typeof bgColorMapping[pid] != "undefined" ){
				this.$el.css('background-color', bgColorMapping[pid]);
			}
			return this;
		}
	});
	
	
	for(var i=0;i<5; i++){
		DailySchedules[i] = new DailySchedule;
	}

	var DailyView = Backbone.View.extend({
		attributes: {
			sindex: 0,
			day: 0
		},
		initialize: function(){
			this.listenTo(this.collection, 'reset', this.addAll);
		},
		addAll: function(){
			activityIds[ this.attributes.day ] = new Array;
			this.$el.html('');
			this.attributes.sindex=0;
			this.collection.each(this.addOne, this);
		},
		addOne: function(timeslot){
			timeslot.set('sindex', this.attributes.sindex);
			timeslot.set('day', this.attributes.day);
			if( timeslot.get('activityId') ){
				activityIds[ this.attributes.day ].push(timeslot.get('activityId'));
			}
			var slot = new timeSlotView({model:timeslot});
			this.$el.append(slot.render().el);
			this.attributes.sindex++;
			
		}
	});
	
	var Daily = new Array;
	for(var i=0;i<5; i++){
		Daily[i] = new DailyView({
			collection: DailySchedules[i],
			el: $('#day-'+i),
			attributes:{
				day: i,
				sindex: 0
			}
		});
	}
	
	for(var i=0;i<5; i++){
		DailySchedules[i].reset(scheduleWeekData[i]);
	}

});
</script>
    </body>
</html>