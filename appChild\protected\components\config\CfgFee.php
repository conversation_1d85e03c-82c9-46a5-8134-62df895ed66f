<?php
//invoice_state
define("MIMS_INVOICE_STATUS_WAIT", 10);
define("MIMS_INVOICE_STATUS_PAID", 20);
define("MIMS_INVOICE_STATUS_PARTIALLY", 30);
define("MIMS_INVOICE_STATUS_WAIT_DISCOUNT", 40);
define("MIMS_INVOICE_STATUS_WAIT_AUDIT", 77);
define("MIMS_INVOICE_STATUS_AUDIT_HISTORY", 88);
define("MIMS_INVOICE_STATUS_INVALID", 99);

//transaction_type
define("MIMS_PAYMENT_CASH", 10);
define("MIMS_PAYMENT_BANKTRANSFER", 20);
define("MIMS_PAYMENT_CHECK", 30);
define("MIMS_PAYMENT_USECREDIT", 40);
define("MIMS_PAYMENT_TOCREDIT", 50);
define("MIMS_PAYMENT_BANKCARD", 60);
define("MIMS_PAYMENT_POS", 70);
define("MIMS_PAYMENT_ONLINEPAYMENT", 80);
define("MIMS_PAYMENT_POS1", 71);
define("MIMS_PAYMENT_ONLINEPAYMENT1", 81);
define("MIMS_PAYMENT_WECHATPAY", 85);
define("MIMS_PAYMENT_WECHATPAY1", 86);
define("MIMS_PAYMENT_VOUCHER", 90);

return array(
		"invoice_state"=>array(
				MIMS_INVOICE_STATUS_WAIT => array(
						"cn" => "等待付款",
						"en" => "Unpaid"
				),
				MIMS_INVOICE_STATUS_PAID =>array(
						"cn" => "款付清",
						"en" => "Paid"
				),
				MIMS_INVOICE_STATUS_PARTIALLY => array(
						"cn" => "款部分付清",
						"en" => "Partially Paid"
				),
				MIMS_INVOICE_STATUS_WAIT_DISCOUNT => array(
						"cn" => "等待折扣审核",
						"en" => "Wait Discount Approval"
				),
				MIMS_INVOICE_STATUS_WAIT_AUDIT => array(
						"cn" => "帐单更改待审核",
						"en" => "Wait Audit"
				),
				MIMS_INVOICE_STATUS_AUDIT_HISTORY => array(
						"cn" => "帐单更改历史",
						"en" => "Audit History"
				),
				MIMS_INVOICE_STATUS_INVALID => array(
						"cn" => "付款通知单作废",
						"en" => "Voided"
				),
		),
		"transaction_type"=>array(
				MIMS_PAYMENT_CASH => array(
						"cn" => "现金",
						"en" => "Cash"
				),
				MIMS_PAYMENT_BANKTRANSFER =>array(
						"cn" => "转帐",
						"en" => "Bank Transfer"
				),
				MIMS_PAYMENT_CHECK => array(
						"cn" => "支票",
						"en" => "Check"
				),
			//使用个人帐户
				MIMS_PAYMENT_USECREDIT => array(
						"cn" => "使用个人帐户",
						"en" => "Use Credit"
				),
				MIMS_PAYMENT_BANKCARD => array(
						"cn" => "银行卡-非艾毅POS机",
						"en" => "Bank Card"
				),
				MIMS_PAYMENT_POS => array(
						"cn" => "POS",
						"en" => "POS"
				),
				MIMS_PAYMENT_POS1 => array(
						"cn" => "POS",
						"en" => "POS"
				),
				MIMS_PAYMENT_ONLINEPAYMENT => array(
						"cn" => "网银支付",
						"en" => "Online Payment"
				),
				MIMS_PAYMENT_ONLINEPAYMENT1 => array(
						"cn" => "网银支付",
						"en" => "Online Payment"
				),
				MIMS_PAYMENT_WECHATPAY => array(
						"cn" => "微信支付",
						"en" => "Wechat Payment"
				),
				MIMS_PAYMENT_WECHATPAY1 => array(
						"cn" => "微信支付",
						"en" => "Wechat Payment"
				),
				MIMS_PAYMENT_TOCREDIT =>array(
						"cn" => "转到个人帐户",
						"en" => "To Credit"
				),
				MIMS_PAYMENT_VOUCHER =>array(
						"cn" => "代金券",
						"en" => "代金券"
				)
		)
);