<?php

/**
 * This is the model class for table "ivy_reset_mail_password".
 *
 * The followings are the available columns in table 'ivy_reset_mail_password':
 * @property integer $id
 * @property integer $target_uid
 * @property string $target_email
 * @property string $pass
 * @property string $branch_id
 * @property integer $updated
 * @property integer $updated_by
 */
class ResetMailPassword extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_reset_mail_password';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('target_uid, target_email, pass, branch_id, updated, updated_by', 'required'),
			array('target_uid, updated, updated_by', 'numerical', 'integerOnly'=>true),
			array('target_email', 'length', 'max'=>255),
			array('pass, branch_id', 'length', 'max'=>32),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, target_uid, target_email, pass, branch_id, updated, updated_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'target_uid' => 'Target Uid',
			'target_email' => 'Target Email',
			'pass' => 'Pass',
			'branch_id' => 'Branch',
			'updated' => 'Updated',
			'updated_by' => 'Updated By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('target_uid',$this->target_uid);
		$criteria->compare('target_email',$this->target_email,true);
		$criteria->compare('pass',$this->pass,true);
		$criteria->compare('branch_id',$this->branch_id,true);
		$criteria->compare('updated',$this->updated);
		$criteria->compare('updated_by',$this->updated_by);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return ResetMailPassword the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}