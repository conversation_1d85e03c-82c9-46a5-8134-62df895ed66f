<?php

class SurveyPreviewController extends ProtectedController{

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

    }

    public function actionIndex($surveyId)
    {
        
        $surveyId = intval($surveyId);
        if ($surveyId) {
            Yii::import('common.models.survey.*');
            $cs = Yii::app()->clientScript;
            $cs->registerCoreScript('jquery.ui');

            $survey = WSurvey::model()->findByPk($surveyId);
            if ($survey) {
                $templateId = $survey->getOnlyTemplateId();
                $topicData = WSurveyTopic::model()->getSurveyTopics($templateId);
                $optionsData = WSurveyTopic::model()->getTopicOptions($templateId);

                $this->render('index', array(
                    'survey' => $survey,
                    'topicData' => $topicData,
                    'optionsData' => $optionsData,
                ));
            }
        }
    }
}
