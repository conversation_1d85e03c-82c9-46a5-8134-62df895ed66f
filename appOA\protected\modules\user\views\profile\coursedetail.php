<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
    <h4 class="modal-title" id="exampleModalLabel"><?php echo (current($model)->courseTitle) ? current($model)->courseTitle->getTitle() : current($model)->groupTitle->getName() ?> - <?php echo substr(end($model)->service_start, 0, 6)  ?></h4>
</div>

<div class="modal-body">
    <table class="table table-bordered">
        <thead>
            <tr>
                <th><?php echo Yii::t('site','Duty Time'); ?></th>
                <th><?php echo Yii::t('invoice','Created By'); ?></th>
                <th><?php echo Yii::t('invoice','Add Timestamp'); ?></th>
            </tr>
        </thead>
        <?php foreach ($model as $item) :
            if ($item->service_start == $item->service_end) {
                $date = date("Y/m/d", strtotime($item->service_start));
            } else {
                $date = date("Y/m/d", strtotime($item->service_start)) .'-'. date("Y/m/d", strtotime($item->service_end));
            }
        ?>
            <tbody>
            <tr>
                <td><?php echo $date; ?></td>
                <td><?php echo $item->userName->getName() ?></td>
                <td><?php echo date("Y-m-d", $item->updated)?></td>
            </tr>
            </tbody>
        <?php endforeach; ?>
    </table>
</div>


