<?php 

/*
 * <PERSON><PERSON>
* 2012-7-11
*
*/

//  投票

$tk = new HtoolKits();
// 如果是多选 超出允许的选择个数 有弹框提示
$this->beginWidget('zii.widgets.jui.CJuiDialog', array(
		'id'=>'mydialog',
		// additional javascript options for the dialog plugin
		'options'=>array(
				// 提示
				'title'=>Yii::t("vote", 'Hint'),
				'autoOpen'=>false,
		),
));

$this->endWidget('zii.widgets.jui.CJuiDialog');
$beyond_maximum_notice = '';
if($vote_info->option_number > 1){
	$beyond_maximum_notice = sprintf(Yii::t("vote", 'You can only cast a maximum of %s votes'),$vote_info->option_number);
}

?>

<div class="votebox">
	<h4>
		<span><b><?php echo Yii::t("vote", 'Please Vote');?> </b> </span><span
			class="alignRight"><b></b> </span>
	</h4>
	<div id="vote_info" class="voteinfo">
		<?php if(!empty($vote_info)){?>
		<div class="poll pd10 prebg">
			<span class="vtitle"><a href="javascript:void(0)"><?php echo $tk->getContentByLang($vote_info->title_cn, $vote_info->title_en);?><em>
						<?php 
						if($vote_info->option_number > 1){
							echo '('.$vote_info->option_number.')';
						}else{
							echo '('.Yii::t("vote", 'Single Selection').')';
						}
						?>
				</em> </a> </span>
			<div class="pollother">
				<span class="fr txt"><em><?php echo Yii::t("vote", 'End Date');?>
				</em><span><?php echo Mims::formatDateTime($vote_info->end_time);?></span>
				</span> <span class="fr txt"><em><?php echo Yii::t("vote", 'Start Date');?>
				</em><span><?php echo Mims::formatDateTime($vote_info->start_time);?> </span>
				</span> <span class="fr txt"><em><?php echo Yii::t("vote", 'Votes Casted');?>
				</em><span><?php echo $vote_info->votesCasted;?> </span> </span> <span
					class="fr txt"><em><?php echo Yii::t("vote", 'Total Voters');?>
				</em><span><?php echo $vote_info->totalVoters;?> </span> </span>
			</div>
			<div style="clear: both;"></div>
			<div class="polldesc">
				<?php echo $tk->getContentByLang($vote_info->detail_description_cn, $vote_info->detail_description_en);?>
			</div>
			<?php $form = $this->beginWidget('CActiveForm', array(
					'id'=>'vote-form',
					'enableAjaxValidation'=>false,
			));
			?>
			<div id="option_info"></div>
			<div id="operate">
				<?php if(true == $is_voted){?>
				<div class="flash-success">
					<p><?php echo Yii::t("vote", 'Congratulations! You have successfully casted your votes! ');?>
					</p>
					<p><?php echo Yii::t("vote", 'You voted on').' '.$voted_time;?>
					</p>
				</div>
				<?php }else{?>
				<?php if(true == $invalid_time){?>
				<ul class="hint">
					<li><?php echo Yii::t("vote", 'Voting has either not started or has already completed at this time.');?>
					</li>
				</ul>
				<?php }else{?>
				
				<?php if(Yii::app()->user->checkAccess('adminChild',array("childid"=>$this->getController()->getChildId()))): ?>
					<div class=btn>
						<span class="btn001"> <?php 
	
						$ajaxOptions = array(
								'type'=>'POST',
								'dataType'=>'JSON',
								'data'=> 'js:jQuery(this).parents("form").serialize()',
								'beforeSend'=>'voteBeforeSend',
								'success'=>'voteSuccess',
						);
						echo CHtml::ajaxLink('<span>'.Yii::t("vote", 'Vote').'</span>', $this->controller->createUrl('vote/voting'),$ajaxOptions);?>
	
						</span> <input type="hidden" id="vote_id" name="vote_id"
							value="<?php echo $vote_info->id;?>"><input type="hidden"
							id="operate_id" name="operate_id"
							value="<?php echo $vote_info->operate_id;?>">
					</div>
					<?php else:?>
						<p class="tar">
							<?php echo Yii::t("portfolio","Submit not available to visitors.");?>
						</p>
					<?php endif;?>
				<?php } 
			}?>
			</div>
			<?php $this->endWidget(); ?>
		</div>

		<?php }else{?>
		<div id="operate">
			<ul class="hint">
				<li><?php echo Yii::t("vote", 'No Result');?>
				</li>
			</ul>
		</div>
		<?php }?>
	</div>
	<div class="c"></div>
</div>

<script type="text/javascript">
						
    $(document).ready(function(){
        var data = [];
        var option_count = <?php echo $option_count;?>;
        if (option_count > 0) {
        
            var vo_list = <?php echo $vo_list_json;?>;
            //显示 单选按钮或多选框
            //this.operate_input_text = '';
            for (var i = 0; i < option_count; i++) {
                // 标题 投票数 操作按钮 详细描述 头标题 冒号
                data[i] = [vo_list[i]["title"], vo_list[i]["result_count"], vo_list[i]['operate_input_html'],vo_list[i]['desc'],vo_list[i]['header_title'],vo_list[i]['option_colon'],vo_list[i]['id']];
            }
            
            //var desc = '([$one_vbi.desc])';
            var bar1 = new bar();
            bar1.id = 'option_info';
            bar1.title = "";
            bar1.bgimg = "<?php echo $this->assetsHomeUrl.'/images/plan.gif';?>";
            bar1.data = data;
            bar1.show();
        }
       
    });

    function validateCheckedNum(_this, option_num){
			var checkedNum = $('input[type=checkbox][name^=vote_option]:checked').length;
			if(checkedNum > option_num){
				$(_this).attr("checked", false);
				$("#mydialog").html("<div><?php echo $beyond_maximum_notice;?></div>");
		    	$("#mydialog").dialog("open");
			}
    }

    function voteBeforeSend(){

    	var checkedNum = $('input[name^=vote_option]:checked').length;
		if(checkedNum < 1){
			$("#mydialog").html("<div><?php echo Yii::t("vote", 'Please make at least 1 vote');?></div>");
	    	$("#mydialog").dialog("open");
	    	return false;
		}
    }

    function voteSuccess(data){
    	var notice_info = "";
		var redirect_url = "";
    	if('success' == data.flag){
    		notice_info = '<?php echo Yii::t("vote", 'Success');?>';
    		redirect_url = "<?php echo $this->controller->createUrl('vote/index',array('id'=>$vote_info->id));?>";
    	}else if('failed' == data.flag){
    		notice_info = '<?php echo Yii::t("vote", 'Failed');?>';
    	}else if('unauthorized' == data.flag){
    		notice_info = '<?php echo Yii::t("vote", 'Unauthorized');?>';
    	}
    	$("#mydialog").html("<div>"+notice_info+"</div>");
    	$("#mydialog").dialog("open");
    	if(redirect_url){
    		setTimeout('location.href = "'+redirect_url+'";',2000);
		}
		
    	
    }
        
</script>
