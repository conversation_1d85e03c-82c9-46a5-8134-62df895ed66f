<?php

/**
 * This is the model class for table "ivy_achievement_report_child".
 *
 * The followings are the available columns in table 'ivy_achievement_report_child':
 * @property integer $id
 * @property integer $calender
 * @property string $report_id
 * @property integer $childid
 * @property integer $classid
 * @property string $is_stat
 * @property string $counselor_message_cn
 * @property string $counselor_message_en
 * @property string $counselor_temp
 * @property integer $counselor_user
 * @property integer $counselor_time
 * @property string $social_innovation
 * @property string $college_counseling
 * @property integer $college_user
 * @property integer $college_time
 * @property string $cas_ee_comment
 * @property string $cas_temp
 * @property integer $cas_user
 * @property integer $cas_time
 * @property string $ee_comment
 * @property string $ee_temp
 * @property integer $ee_user
 * @property integer $ee_time
 * @property string $evaluate_number
 * @property string $absent_days
 * @property integer $uid
 * @property integer $create_time
 * @property integer $update_time
 */
class AchievementReportChild extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_achievement_report_child';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('report_id, childid, classid, evaluate_number, uid, create_time, update_time', 'required'),
			array('calender, childid, classid, uid, create_time, update_time, is_cache', 'numerical', 'integerOnly'=>true),
			array('report_id, is_stat, cache_pdf, schoolid, lang', 'length', 'max'=>255),
			array('counselor_message_en', 'length', 'max'=>1000),
			array('counselor_message_cn, absent_days, social_innovation, college_counseling,college_user,college_time,cas_ee_comment,cas_temp,cas_user,cas_time,ee_comment,ee_temp,ee_user,ee_time', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, calender, report_id, childid, classid, is_stat, counselor_message_cn, counselor_message_en, evaluate_number, absent_days, uid, create_time, update_time', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			"report" => array(self::BELONGS_TO, 'AchievementReport', array('report_id' => "id")),
			"childName" => array(self::BELONGS_TO, 'ChildProfileBasic', "childid"),
			"childClass" => array(self::BELONGS_TO, 'IvyClass', "classid"),
			'calendarInfo' => array(self::BELONGS_TO, 'Calendar', 'calender'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'calender' => 'Calender',
			'report_id' => 'Report',
			'childid' => 'Childid',
			'classid' => 'Classid',
			'is_stat' => 'Is Stat',
			'counselor_message_cn' => 'Counselor Message Cn',
			'counselor_message_en' => 'Counselor Message En',
			'evaluate_number' => 'Evaluate Number',
			'absent_days' => 'Absent Days',
			'uid' => 'Uid',
			'create_time' => 'Create Time',
			'update_time' => 'Update Time',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('calender',$this->calender);
		$criteria->compare('report_id',$this->report_id,true);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('is_stat',$this->is_stat,true);
		$criteria->compare('counselor_message_cn',$this->counselor_message_cn,true);
		$criteria->compare('counselor_message_en',$this->counselor_message_en,true);
		$criteria->compare('evaluate_number',$this->evaluate_number,true);
		$criteria->compare('absent_days',$this->absent_days,true);
		$criteria->compare('uid',$this->uid);
		$criteria->compare('create_time',$this->create_time);
		$criteria->compare('update_time',$this->update_time);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AchievementReportChild the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
