<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yiic message' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE, this file must be saved in UTF-8 encoding.
 *
 * @version $Id: $
 */
return array (
    ' (:startY - :endY)' => '',
    'Child Name' => '孩子姓名',
    'Create Invoice' => '生成账单',
    'Created By' => '操作人',
    'Add Timestamp'=>'操作时间',
    'Discount' => '折扣',
    'Due Date' => '到期日期',
    'End Date' => '结束时间',
    'Fee Type' => '账单类型',
    'Fee Types' => '账单类型',
    'Invoice Peroid' => '账单区间',
    'Invoice Status' => '账单状态',
    'Fapiao Amount' => '发票金额',
    'Invoice Title' => '账单标题',
    'Invoice Detail' => '账单详情',
    'Fapiao Number' => '发票号',
    'Fapiao Title' => '发票抬头',
    'Fapiao Info' => '发票信息',
    'Language' => '账单语言',
    'Preview Invoice' => '预览账单',
    'School Calendar' => '校历',
    'School / Class' => '校园/班级',
    'Service Information' => '服务类别',
    'Start Date' => '开始时间',
    'Overdue Invoices' => '过期帐单',
    'Unpaid' => '未付帐单',
    '付款方式' => '',
    '付款日期' => '',
    '付款金额' => '',
    '入园材料费及预缴学费' => '',
    '其他账单' => '',
    '初始账单' => '',
    '发票号' => '',
    '发票抬头' => '',
    '图书卡工本费' => '',
    'Memo' => '备注',
    '学期付费' => '',
    '寒暑假' => '',
    '常规账单' => '',
    '年付费' => '',
    '年保育费' => '',
    '操作人' => '',
    '更新时间' => '',
    '月付费' => '',
    '标题' => '',
    '状态' => '',
    '课外活动及亲子班' => '',
    '退款期间' => '',
    '退款金额' => '',
    'Title' => '标题',
    'Memo' => '备注',
    'Amount' => '退款金额',
    'Startdate' => '退费开始日期',
    'Enddate' => '退费结束日期',
    'Timestamp' => '操作日期',
    'Refund Peroid' => '退款期间',
    'Status' => '状态',
    'create an invoice' => '开账单',
    'show invoice title' => '显示账单标题',
    'create batch invoices' => '批量开账单',
    'Multiple discounted invoices found, please correct.' => '存在多个有折扣的账单，请修正。',
);
