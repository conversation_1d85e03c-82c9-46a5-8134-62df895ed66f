<?php

/**
 * This is the model class for table "ivy_branch_detail".
 *
 * The followings are the available columns in table 'ivy_branch_detail':
 * @property string $branchid
 * @property string $title_cn
 * @property string $title_en
 * @property string $tel
 * @property string $email
 * @property string $support_email
 * @property string $fax
 * @property string $official_name
 * @property string $admissions_tel
 * @property string $admissions_email
 * @property string $interview_address_cn
 * @property string $interview_address_en
 * @property string $address_cn
 * @property string $address_en
 * @property string $desc_cn
 * @property string $desc_en
 * @property string $memo
 * @property integer $status
 * @property integer $update_user
 * @property integer $update_timestamp
 */
class BranchInfo extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return BranchInfo the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_branch_detail';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('branchid', 'required'),
			array('status, update_user, update_timestamp', 'numerical', 'integerOnly'=>true),
			array('branchid', 'length', 'max'=>10),
			array('title_cn, title_en, tel, email, fax', 'length', 'max'=>255),
			array('admissions_tel, admissions_email, interview_address_cn, interview_address_en, official_name, address_cn, address_en', 'length', 'max'=>500),
			array('desc_cn, desc_en, memo, support_email', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('branchid, title_cn, title_en, tel, email, fax, admissions_tel, admissions_email, interview_address_cn, interview_address_en, official_name, address_cn, address_en, desc_cn, desc_en, memo, status, update_user, update_timestamp', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'branchid' => 'Branchid',
			'title_cn' => 'Title Cn',
			'title_en' => 'Title En',
			'tel' => Yii::t("labels",'Tel'),
			'email' => Yii::t("labels",'Email'),
			'fax' => Yii::t("labels",'Fax'),
			'official_name' => Yii::t("labels",'official_name'),
			'admissions_tel' => Yii::t("labels",'招生部电话'),
			'admissions_email' => Yii::t("labels",'招生部邮箱'),
			'interview_address_cn' => Yii::t("labels",'面试地址'),
			'interview_address_en' => Yii::t("labels",'面试地址'),
			'address_cn' => Yii::t("labels",'Address'),
			'support_email' => Yii::t("labels",'Email'),
			'address_en' => Yii::t("labels",'Address'),
			'desc_cn' => 'Desc Cn',
			'desc_en' => 'Desc En',
			'memo' => 'Memo',
			'status' => 'Status',
			'update_user' => 'Update User',
			'update_timestamp' => 'Update Timestamp',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('branchid',$this->branchid,true);
		$criteria->compare('title_cn',$this->title_cn,true);
		$criteria->compare('title_en',$this->title_en,true);
		$criteria->compare('tel',$this->tel,true);
		$criteria->compare('email',$this->email,true);
		$criteria->compare('fax',$this->fax,true);
		$criteria->compare('official_name',$this->official_name,true);
		$criteria->compare('admissions_tel',$this->admissions_tel,true);
		$criteria->compare('admissions_email',$this->admissions_email,true);
		$criteria->compare('interview_address_cn',$this->interview_address_cn,true);
		$criteria->compare('interview_address_en',$this->interview_address_en,true);
		$criteria->compare('address_cn',$this->address_cn,true);
		$criteria->compare('address_en',$this->address_en,true);
		$criteria->compare('desc_cn',$this->desc_cn,true);
		$criteria->compare('desc_en',$this->desc_en,true);
		$criteria->compare('memo',$this->memo,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('update_user',$this->update_user);
		$criteria->compare('update_timestamp',$this->update_timestamp);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
	
	public function getLangContent($cat){
		$col = (Yii::app()->language == "zh_cn") ? $cat."_cn" : $cat."_en";
		return $this->getAttribute($col);
	}

    public function getTitle(){
        if (Yii::app()->language == 'zh_cn')
            return $this->title_cn;
        else
            return $this->title_en;
    }
}