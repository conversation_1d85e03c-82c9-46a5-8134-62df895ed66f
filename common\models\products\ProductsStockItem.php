<?php

/**
 * This is the model class for table "ivy_products_stock_item".
 *
 * The followings are the available columns in table 'ivy_products_stock_item':
 * @property integer $id
 * @property integer $cid
 * @property integer $pid
 * @property integer $paid
 * @property integer $psid
 * @property integer $num
 * @property integer $memo
 * @property string $school_id
 * @property integer $updated_userid
 * @property integer $updated_time
 */
class ProductsStockItem extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_products_stock_item';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('cid, pid, paid, psid, num, memo, school_id, updated_userid, updated_time', 'required'),
			array('cid, pid, paid, psid, num, updated_userid, updated_time', 'numerical', 'integerOnly'=>true),
			array('school_id', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, cid, pid, paid, psid, num, memo, school_id, updated_userid, updated_time', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'cid' => 'Cid',
			'pid' => 'Pid',
			'paid' => 'Paid',
			'psid' => 'Psid',
			'num' => Yii::t('invoice','数量'),
			'memo' => Yii::t('invoice','Memo'),
			'school_id' => 'School',
			'updated_userid' => 'Updated Userid',
			'updated_time' => 'Updated Time',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('cid',$this->cid);
		$criteria->compare('pid',$this->pid);
		$criteria->compare('paid',$this->paid);
		$criteria->compare('psid',$this->psid);
		$criteria->compare('num',$this->num);
		$criteria->compare('memo',$this->memo);
		$criteria->compare('school_id',$this->school_id,true);
		$criteria->compare('updated_userid',$this->updated_userid);
		$criteria->compare('updated_time',$this->updated_time);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return ProductsStockItem the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
