div.welcome{
	min-height:120px;
}
div.welcome h2{
	margin-bottom:0.2em;
	font-size: 14px;
	color:#333;
	border-bottom: 1px dotted #999;
	position: relative;
	height:24px;
}
div.welcome p{
	margin-bottom:0.2em;
}
div.welcome h2 span{
	display:inline-block;
	height: 24px;
	right: 0;
	top: 0px;
	position: absolute;
	font-size:12px;
}
div.welcome h2 span a{
	text-decoration:none;
}
div.welcome h2 span select{
	margin: 0;
	padding: 0;
    background-color: #E8E7DA;
    border: 0 none;
	font-family:"Microsoft Yahei";
	text-align:right;
}
.weather_en_us h3,.weather_zh_cn h3{
	text-align: right;
	font-size:12px;
	font-weight:normal;
}
.weather_en_us .city, .weather_zh_cn .city{
	text-align:center;
}
.welcome .city img{
	margin-bottom:0.4em;
}
.welcome .city h4{
	margin-bottom:0.2em;
	font-size:14px;
}
.params li{
	color:#333;
	font-size:12px;
	line-height:22px;
}
.weather_en_us .params li label{
	color:#999;
	font-size:11px;
	padding-right: 4px;
}
.weather_zh_cn .params li label{
	color:#999;
	font-size:12px;
	padding-right: 4px;
}
.journal-list li{
	line-height:22px;
	font-size:12px;
}
.feedback-list li{
	line-height:22px;
	font-size:12px;
}
.feedback-list li label{
	color:#999;
	font-size:11px;
	padding-right: 4px;	
}

.welcome .summary-box{
	height: 115px;
	overflow-y: auto;
	
}

#recent-items h5,#recent-items h6,#recent-items p{
	margin-bottom: 2px;
}
#recent-items p{
	height: 16px;
	line-height: 16px;
}
#recent-items h5{
	height: 22px;
	line-height: 22px;
}
#recent-items .item .title{
	display: inline-block;
	white-space: nowrap;
	float: left;
}
#recent-items .item:hover .title h5{
	/*background:#333;*/
		background-color: rgb(255, 0, 145);
		/*background-color: rgba(255, 0, 145, 0.5);	*/
}
#recent-items .item .title h5{
	height: 50px;
	line-height: 50px;
	padding: 0 8px;
	background:#999;
	width: 50px;
	text-align: center;
	font-size: 18px;
	color:#fff;
}
#recent-items .week-item .title h5{
	padding: 0 4px;
}
#recent-items .week-item .desc{
	margin-left: 64px !important;
}
#recent-items .item .title h6{
	font-size: 10px;
	text-align: center;
	color:#999;
	font-weight: normal;
}
#recent-items .item .desc{
	margin-left: 80px;
	margin-right: 10px;
	text-align: right;
	color:#e9e9e7;
}
#recent-items .item .desc h5{
	text-align: right;
	font-size: 16px;
	color:#999;
}
#recent-items .desc h5.ml-title{
	text-align: left;
	line-height: 50px;
	height: 40px;
	font-size: 18px;
	font-weight: normal;
}
#recent-items .item .desc p{
	font-size: 10px;
	color:#999;
}
#recent-items .item .desc .image-list{
    height: 125px;
}
#recent-items .item .desc .image-list li.ite{
/*	display: inline-block;*/
    float: left;
	width: 68px;
	height: 60px;
	border: 1px solid #dedede;
    background-position: center center;
	cursor: pointer;
}
#recent-items .hover{
	/*visibility: hidden;*/
	
}
#recent-items .item{
	padding-bottom: 10px;
	margin-bottom: 6px;
}
#recent-items .item:hover .desc h5{
	/*visibility: visible;*/
	color: #333;
}
#recent-items .item:hover{
	background: #fff;
}
.week-misc{
	width: 166px;
	height: 125px;
	/*display: inline-block;*/
	position: relative;
}
.week-misc{
	/**display: inline;*/
}
.week-misc .title{
	position: absolute;
	top: 0;
	left: 0;
	height: 38px;
	width: 156px;
}
.week-misc .menu a, .week-misc .calendar a{
	position: absolute;
	width: 76px;
	height: 60px;
}
.week-misc .menu a{
	left: 2px;
	top: 1px;
}
.week-misc .calendar a{
	left: 80px;
	top: 64px;
}
.v-line-12{
	display: inline-block;
	margin: 2px 0;
	width: 0;
	height: 12px;
	border: 0;
	border-left: 1px dotted #999;
}
.v-line-12{
	*display: inline;
}