<?php

/**
 * This is the model class for table "ivy_teaching_monthly_content".
 *
 * The followings are the available columns in table 'ivy_teaching_monthly_content':
 * @property integer $id
 * @property string $content_cn
 * @property string $content_en
 * @property string $objectives_cn
 * @property string $objectives_en
 * @property string $content_mik_en
 */
class TeachingMonthlyContent extends CActiveRecord
{
    public $title_cn='';
    public $title_en='';
    public $questions_cn='';
    public $questions_en='';
    public $activities_cn='';
    public $activities_en='';
    public $trips_cn='';
    public $trips_en='';
    public $song_cn='';
    public $song_en='';
    public $festivals_cn='';
    public $festivals_en='';

    public $language_cn='';
    public $language_en='';
    public $mathematical_cn='';
    public $mathematical_en='';
    public $science_cn='';
    public $science_en='';
    public $physical_cn='';
    public $physical_en='';
    public $social_cn='';
    public $social_en='';
    public $art_cn='';
    public $art_en='';
    public $social_studies_cn='';
    public $social_studies_en='';

    public $words='';
    public $sentences='';
    public $letters='';
    public $numbers='';
    public $colors='';
    public $shapes='';
    public $animals='';
    public $body='';
    public $others='';


    public $songs='';
    public $stories='';
    public $functional='';

    public $newPhysical_cn;
    public $newPhysical_en;
    public $newLanguage_cn;
    public $newLanguage_en;
    public $newSocial_cn;
    public $newSocial_en;
    public $newScience_cn;
    public $newScience_en;
    public $newArt_cn;
    public $newArt_en;
    public $newIdea_cn;
    public $newIdea_en;

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_teaching_monthly_content';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('id', 'required'),
			array('id', 'numerical', 'integerOnly'=>true),
			array('content_mik_en, content_cn, content_en, objectives_cn, objectives_en', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, content_cn, content_en, objectives_cn, objectives_en, content_mik_en', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'content_cn' => 'Content Cn',
			'content_en' => 'Content En',
			'objectives_cn' => 'Objectives Cn',
			'objectives_en' => 'Objectives En',
			'content_mik_en' => 'Content Mik En',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('content_cn',$this->content_cn,true);
		$criteria->compare('content_en',$this->content_en,true);
		$criteria->compare('objectives_cn',$this->objectives_cn,true);
		$criteria->compare('objectives_en',$this->objectives_en,true);
		$criteria->compare('content_mik_en',$this->content_mik_en,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return TeachingMonthlyContent the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public function expVista()
    {
        $content_cn = $this->content_cn ? CJSON::decode(base64_decode($this->content_cn)) : null;
        $content_en = $this->content_en ? CJSON::decode(base64_decode($this->content_en)) : null;
        $objectives_cn = $this->objectives_cn ? CJSON::decode(base64_decode($this->objectives_cn)) : null;
        $objectives_en = $this->objectives_en ? CJSON::decode(base64_decode($this->objectives_en)) : null;
        $content_mik = $this->content_mik_en ? CJSON::decode(base64_decode($this->content_mik_en)) : null;

        $content_keys = array('title', 'newIdea', 'questions', 'activities', 'trips', 'song', 'festivals');
        if($content_cn){
            foreach($content_keys as $tag){
                $this->{$tag.'_cn'} = $content_cn[$tag];
            }
        }
        if($content_en){
            foreach($content_keys as $tag){
                $this->{$tag.'_en'} = $content_en[$tag];
            }
        }

        $obj_keys = array('language', 'mathematical', 'science', 'physical', 'social', 'art', 'social_studies','newPhysical','newLanguage','newSocial','newScience','newArt');
        if($objectives_cn){
            foreach($obj_keys as $tag){
                $this->{$tag.'_cn'} = $objectives_cn[$tag];
            }
        }
        if($objectives_en){
            foreach($obj_keys as $tag){
                $this->{$tag.'_en'} = $objectives_en[$tag];
            }
        }

        //$mik_keys = array('words', 'sentences', 'letters', 'numbers', 'colors', 'shapes', 'animals', 'body', 'others');
        $mik_keys = array('words', 'sentences', 'letters', 'songs', 'stories', 'functional');
        if($content_mik){
            foreach($mik_keys as $tag){
                $this->$tag = $content_mik[$tag];
            }
        }
    }
}
