<div class="container-fluid">
    <!-- 面包屑导航 -->
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Teaching Tasks'), array('//mteaching/learningdomains/index')) ?></li>
        <li><?php echo CHtml::link($this->typeList[$model->type], array('//mteaching/learningdomains/index', 'type' => $model->type)); ?></li>
        <li><?php echo $model->title_cn; ?></li>
        <li class="active"><?php echo Yii::t('curriculum','教学标准');?></li>

    </ol>
    <div class="row">
        <!-- 左侧菜单栏 -->
        <div class="col-md-2">
            <div class="list-group">
                <?php
                foreach ($this->typeList as $key => $value) {
                    $active = ($key == $_GET['type']) ? 'active' : '';
                    echo CHtml::link(Yii::t('curriculum', $value), array('index', 'type' => $key), array('class' => 'list-group-item status-filter ' . $active));
                }
                ?>
            </div>
        </div>
        <div class="col-md-10">
            <?php echo CHtml::link(Yii::t('curriculum', '新建标准'), array('updateItems', 'pid' => $model->id), array('class' => 'btn btn-primary J_modal')); ?>
            <?php $this->widget('ext.ivyCGridView.BsCGridView', array(
                'id' => 'item-grid',
                'dataProvider' => $dataProvieder,
                'afterAjaxUpdate' => 'js:head.Util.modal',
                'colgroups' => array(
                    array(
                        "colwidth" => array(50, 50, 50, 50, 80),
                    )
                ),
                'columns' => array(
                    'title_cn',
                    'title_en',
                    array(
                        'name' => 'updated_at',
                        'value' => 'date("Y-m-d H:i:s", $data->updated_at)',
                    ),
                    array(
                        'name' => Yii::t('curriculum', 'Operation'),
                        'value' => array($this, 'getItemButton'),
                    ),
                ),
            ));
            ?>
        </div>
    </div>
</div>

<script>
    var modal = '<div class="modal fade" id="modal" tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog modal-lg" role="document"><div class="modal-content"></div></div></div>';
    $('body').append(modal);

    function cb_modal() {
        $('#modal').modal('hide');
        $.fn.yiiGridView.update('item-grid', {
            complete: function() {
                head.Util.modal()
                head.Util.ajaxDel()
            }
        });
    }

    function cb_del(params) {
        $.fn.yiiGridView.update('item-grid', {
            complete: function() {
                head.Util.modal()
                head.Util.ajaxDel()
            }
        });
    }
</script>