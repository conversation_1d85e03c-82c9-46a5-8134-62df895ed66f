<?php
if(!defined("IVY_POLICY"))
throw new CException('DO NOT USE THIS FILE DIRECTLY');

return array( 'policy'=>array(

	//是否老生老办法
	ENABLE_CONSTANT_TUITION => true,
    FENTAN_FACTOR => DAILY,

	//年保育费
	//ANNUAL_CHILDCARE_AMOUNT => 8000,

	//是否开放课外活动账单
	ENABLE_AFTERSCHOOLS => true,

	//图书馆工本费
	FEETYPE_LIBCARD => 10,

	//学期四个时间点；取前后两端
	TIME_POINTS   => array(201708,201801,201802,201806),

	//年付开通哪些缴费类型
	PAYGROUP_ANNUAL => array(
		ITEMS => array(
			FEETYPE_TUITION,
			FEETYPE_LUNCH,
			FEETYPE_SCHOOLBUS,
		),
		TUITION_CALC_BASE => BY_ANNUAL,
	),

	//学期付开通哪些缴费类型
	PAYGROUP_SEMESTER => array(
		ITEMS => array(
			FEETYPE_TUITION,
			FEETYPE_LUNCH,
			FEETYPE_SCHOOLBUS,
		),
		TUITION_CALC_BASE => BY_SEMESTER,
	),
//
//	//月份开通哪些缴费类型
	PAYGROUP_MONTH => array(
		ITEMS => array(
			FEETYPE_TUITION,
			FEETYPE_LUNCH,
			FEETYPE_SCHOOLBUS,
		),
		TUITION_CALC_BASE => BY_MONTH,
	),

	//计算基准：BY_MONTH, 或者 BY_SETTING, 前者表示所有的学费都基于月费用计算，后者表示所有的学费都基于学期或学年
	TUITION_CALCULATION_BASE => array(

		BY_MONTH => array(

			//月收费金额
			AMOUNT => array(
				FULL5D => 16500,
				HALF5D => 11940,
				HALF3D => 8710,
				HALF2D => 6100
			),

			//收费月份，及其权重
			MONTH_FACTOR => array(
				201709=>1,
				201710=>1,
				201711=>1,
				201712=>1,
				201801=>1,
				201802=>1,
				201803=>1,
				201804=>1,
				201805=>1,
				201806=>1,
			),

			//折扣

			GLOBAL_DISCOUNT => array(
				DISCOUNT_ANNUAL => '1:1:2017-12-01'
			)


		),

		//东湖配置实例

		BY_ANNUAL => array(
			AMOUNT => array(
				FULL5D => 162320,
				// HALF2D => 56850,
				// HALF3D => 81850,
				HALF5D => 114900,
			),
		),

		BY_SEMESTER1 => array(
			AMOUNT => array(
				FULL5D => 86925,
				// HALF2D => 28500,
				// HALF3D => 41500,
				HALF5D => 61531,
			),
		),

		BY_SEMESTER2 => array(
			AMOUNT => array(
				FULL5D => 75395,
				// HALF2D => 28500,
				// HALF3D => 41500,
				HALF5D => 53369,
			),
		),

	),

	//每餐费用
	LUNCH_PERDAY => 38,

	//账单到期日和账单开始日之差
	DUEDAYS_OFFSET => 0,

));