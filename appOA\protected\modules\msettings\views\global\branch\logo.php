<?php
$crit = new CDbCriteria;
$crit->index = 'branchid';
$crit->compare('type', '<>'.Branch::TYPE_OFFICE);
$crit->compare('status', Branch::STATUS_ACTIVE);
$crit->order = '`group` ASC';
$models = Branch::model()->findAll($crit);
?>
<div class="col-md-8">
    <p class="background-gray p15">If custom logo is set, default logo will be ignored.<br>Please set the logo size to ??x?? before upload.</p>
    <table class="table">
        <thead>
            <tr>
                <th >Campus</th>
                <th class="text-center" colspan="2">Custom Logo</th>
                <th class="text-center">Default Logo</th>
            </tr>
        </thead>
        <tbody>
        <?php
        foreach($models as $branchid => $branch):
        ?>
            <tr>
                <th style="vertical-align:middle"><?php echo '<span class="glyphicon glyphicon-tags program'.$branch->group.'"></span> '. $branch['title']; ?></th>
                <td class="text-center">
                    <?php if($branch->logo):?>
                    <?php echo CHtml::image(Yii::app()->params['OAUploadBaseUrl'].'/branch/thumbs/'.$branch->logo , "", array('class'=>'img-responsive'));?>
                    <?php echo CHtml::link('<span class="glyphicon glyphicon-remove"></span>', array('delLogo', 'id'=>$branchid), array('class'=>'btn btn-default btn-xs J_ajax_del'));?>
                    <?php endif;?>
                </td>
                <td>
                    <button type="button" class="btn btn-default btn-lg J_addlogo" data-branchid="<?php echo $branchid;?>">
                        <span class="glyphicon glyphicon-floppy-open"></span>
                    </button>
                </td>
                <td class="text-center">
                    <img class="img-responsive" src="<?php echo Yii::app()->params['OAUploadBaseUrl']?>/branch/schooltype_<?php echo $branch->group;?>.jpg">
                </td>
            </tr>
        <?php
        endforeach;
        ?>
        </tbody>
    </table>
    <?php echo CHtml::beginForm('', 'post', array('class'=>'J_ajaxForm hidden')); ?>
        <?php echo CHtml::fileField('logo');?>
        <?php echo CHtml::hiddenField('branchid');?>
    <?php echo CHtml::endForm(); ?>
</div>

<script>
    $('.J_addlogo').click(function(){
        $('#logo').click();
        $('#branchid').val( $(this).data('branchid') );
    });
    $('#logo').change(function(){
        $(this.form).ajaxSubmit({
            dataType    : 'json',
            beforeSubmit: function(arr, $form, options){

            },
            success     : function(data, statusText, xhr, $form){
                if(data.state == 'success'){
                    resultTip({msg: data.message, callback: function(){
                        if(data.refresh === true){
                            if(data.referer) {
                                location.href = data.referer;
                            }else {
                                reloadPage(window);
                            }
                        }
                    }});
                }
                else if( data.state === 'fail' ) {
                    head.dialog.alert(data.message);
                }
            }
        });
    });
</script>
