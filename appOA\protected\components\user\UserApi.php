<?php
class UserApi
{
    /**
     * Create parent
     * @param type $childId
     * @param type $model User model
     * @param type $gender 1: male, 2: female
     * @return boolean
     */
    public static function createParent($childId, $model, $gender)
    {
        if ($childId && $model && $gender){
            $child = ChildProfileBasic::model()->findByPk($childId);
            if ($child->fid && $child->mid)
                return false;
            
            $otherId = $gender == 1 ? $child->mid : $child->fid;
            
            $otherUser = null;
            if ($otherId)
                $otherUser = IvyParent::model()->findByPk($otherId);
            
            $password_text = $model->pass;
            $model->pass = md5($password_text);
            $model->level = 1;
            $model->user_regdate = time();
            $model->timezone_offset = '8.0';
            $model->save();
            
            $model->profile = new UserProfile;
            $model->profile->uid = $model->uid;
            $model->profile->user_gender = $gender;
            $model->profile->save();
            
            $model->parent = new IvyParent;
            $model->parent->setScenario('staffCreate');
            $model->parent->pid = $model->uid;
            $model->parent->password_text = $password_text;
            $model->parent->gender = $gender;
            $model->parent->cn_name = $model->name;
            $model->parent->mphone = $model->mphone;
            $model->parent->childs = $otherUser === null ? serialize(array($childId)) : $otherUser->childs;
            $model->parent->family_id = $child->family_id;
            $model->parent->save();
            
            $model->profilesite = new UserProfileSite;
            $model->profilesite->uid = $model->uid;
            $model->profilesite->save();
            
            $model->createParentRole();
            
            if ($gender == 1)
                $child->fid = $model->uid;
            else
                $child->mid = $model->uid;
            $child->save();
            
            return $model;
        }
        return false;
    }
}
