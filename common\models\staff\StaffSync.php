<?php

/**
 * This is the model class for table "ivy_staff_sync".
 *
 * The followings are the available columns in table 'ivy_staff_sync':
 * @property integer $id
 * @property integer $flag
 * @property integer $timestamp
 */
class StaffSync extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return StaffSync the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_staff_sync';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('id, flag, timestamp', 'required'),
			array('flag, timestamp', 'numerical', 'integerOnly'=>true),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, flag, timestamp', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'user'=>array(self::HAS_ONE, 'User', 'uid'),
//            'profile'=>array(self::HAS_ONE, 'UserProfile', 'id'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'flag' => 'Flag',
			'timestamp' => 'Timestamp',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('flag',$this->flag);
		$criteria->compare('timestamp',$this->timestamp);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
    
    public function sync($uid)
    {
        if ($uid){
            $model = $this->findByPk($uid);
            if ($model === null)
                $model = new StaffSync;
            $model->id = $uid;
            $model->flag = 1;
            $model->timestamp = time();
            if ($model->save())
                return true;
            else
                return false;
        }
        return false;
    }
}