<?php
$model = new TransferSchoolHistory();
$calandarList = CalendarSchool::model()->getSchoolCalender($this->branchId);
$minYear = min($calandarList);
foreach ($calandarList as $key=>$val){
    $calandarList[$key] = $val.'-'.($val+1);
    $calanderKey[$val] = $key;
}
if ($type === 'in'){
    $schoolType =  'from_schoolid';
    $panelTitle = Yii::t('campus', 'Accepted transfers');
}else{
    $schoolType =  'to_schoolid';
    $panelTitle = Yii::t('campus', 'Outgoing transfers');
}
$startTime = Yii::app()->request->getParam('startTime','');
$endTime = Yii::app()->request->getParam('endTime','');
?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo Yii::t('site','Campus Workspace');?></li>
        <li><?php echo Yii::t('site', 'Routines'); ?></li>
        <li class="active"><?php echo Yii::t('campus', 'Group transfer to next year'); ?></li>
    </ol>
    <div class="row">
        <div class="col-md-12">
            <div class="btn-group" data-toggle="buttons">
                <label class="btn btn-default action-group-by <?php if ($type==='in'):?>active<?php endif;?>" data="program" onclick="jumpUrl('in');">
                    <span class="glyphicon glyphicon-list"></span> <?php echo Yii::t('campus', 'Accepted transfers');?>
                </label>
                <label class="btn btn-default action-group-by <?php if ($type==='out'):?>active<?php endif;?>" data="opentime" onclick="jumpUrl('out');">
                    <span class="glyphicon glyphicon-list"></span> <?php echo Yii::t('campus', 'Outgoing transfers');?>
                </label>
                <button type="button" class="btn btn-primary" id="reload-general-page" onclick="popModal();">
                    <span class="glyphicon glyphicon-plus"></span> <?php echo Yii::t('campus', 'Initiate transfers');?>
                </button>
            </div>
        </div>
    </div>
    <div class="mt20"></div>
    <div class="row">
        <div class="col-md-10 col-sm-12">
            <div class="mb10 row">
                <!-- 搜索框 -->
                <form  class="" style="float: left;width: 100%" action="<?php echo $this->createUrl('index'); ?>" method="get">
                    <?php echo CHtml::hiddenField('branchId',$this->branchId); ?>
                    <?php echo CHtml::hiddenField('type',$_GET['type']); ?>
                    <!-- 状态 -->
                    <div class="col-sm-2 form-group">
                        <input class="datepicker form-control" autocomplete="off" placeholder="开始时间" name="startTime" type="text" value="<?php echo $startTime ?>">
                    </div>
                    <!-- 模式 -->
                    <div class="col-sm-2 form-group">
                        <input class="datepicker form-control" autocomplete="off" placeholder="结束时间" name="endTime" type="text" value="<?php echo $endTime ?>">
                    </div>
                    <!-- 内容匹配 -->
                    <div class="">
                        <button class="btn btn-default ml5"  type="submit"><span class="glyphicon glyphicon-search"> </span> </button>
                        <?php if($startTime && $endTime && $status == 1): ?>
                            <a href="javascript:void(0);" onclick="exportData('<?php echo $this->createUrl("exportClv", array('type' => $type, 'startTime' => $startTime, 'endTime' => $endTime))?>')" class="btn btn-info" ><?php echo Yii::t('user', 'Export');?></a>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>
    </div>


    <div class="row">
        <div class="col-md-12">
            <div class="panel panel-default">
                <div class="panel-heading"><?php echo $panelTitle;?></div>
                <div class="panel-body">
                    <?php
                    if($status == 1) {
                        $this->widget('ext.ivyCGridView.BsCGridView', array(
                            'id' => 'account-grid',
                            'dataProvider' => $transModel,
                            // 'filter' => $transModel,
                            'colgroups' => array(
//                            array(
//                            "colwidth"=>array(null,150,150,150),
//                            )
                            ),
                            'columns' => array(
                                array(
                                    'name' => 'childid',
                                    'type' => 'raw',
                                    'value' => array($this, 'renderChildName'),
                                ),
                                array(
                                    'name' => Yii::t('campus', 'Family information'),
                                    'type' => 'raw',
                                    'value' => array($this, 'renderParent'),
                                ),
                                array(
                                    'name' => $schoolType,
                                    'value' => array($this, 'renderSchool'),
                                ),
                                array(
                                    'name' => 'classid',
                                    'value' => '$data->class->title',
                                ),
                                array(
                                    'name' => 'transfer_time',
                                    'value' => 'OA::formatDateTime($data->transfer_time)',
                                ),
                                array(
                                    'name' => 'timestamp',
                                    'value' => 'OA::formatDateTime($data->timestamp)',
                                ),
                                array(
                                    'name' => 'status',
                                    'type' => 'raw',
                                    'value' => 'TransferSchoolHistory::renderStatus($data->status,1)',
                                ),

                                array(
                                    'name' => '个人账户（入学日期）',
                                    'value' => array($this, 'timestampBalance'),
                                ),
                                array(
                                    'name' => '个人账户（操作时间）',
                                    'value' => '$data->balance',
                                ),
                                array(
                                    'class' => 'CButtonColumn',
                                    'template' => '{confirm}{remove}',
                                    'buttons' => array(
                                        'remove' => array(
                                            'visible' => function ($row, $data) {
                                                return ($data->status == 0 && $_GET['type'] == 'out') ? true : false;
                                            },
                                            'url' => 'Yii::app()->controller->createUrl("/mcampus/transfer/delete",array("branchId"=>Yii::app()->controller->getBranchId(),"id"=>$data->id))',
                                            'label' => '<span class="glyphicon glyphicon-remove"></span>',
                                            'options' => array('class' => 'J_ajax_del', 'title' => '撤销转校申请', 'data-msg' => '您确认撤销转校申请吗？')
                                        ),
                                        'confirm' => array(
                                            'visible' => function ($row, $data) {
                                                return ($data->status == 0 && $_GET['type'] == 'out') ? true : false;
                                            },
                                            'url' => 'Yii::app()->controller->createUrl("/mcampus/transfer/confirm",array("branchId"=>Yii::app()->controller->getBranchId(),"id"=>$data->id))',
                                            'label' => '<span class="glyphicon glyphicon-ok"></span>',
                                            'options' => array('class' => 'J_ajax_del', 'title' => '同意转校申请', 'data-msg' => '您确认同意转校申请吗？')
                                        )
                                    )
                                ),
                            ),
                        ));
                    }else if($status == 3){
                        echo '开始时间和结束时间不可相差60天。';
                    }else{
                        echo '开始时间和结束时间必填';
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <?php echo CHtml::form('/mcampus/transfer/appSave', 'post', array('class'=>'J_ajaxForm'));?>
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel"><?php echo Yii::t('campus', 'Initiate transfers');?></h4>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="searchChild"><?php echo Yii::t('campus', 'Search');?></label>
                    <?php $this->widget('ext.search.ChildSearchBox', array(
                            'acInputCSS' => 'form-control',
                            'allowMultiple' => false,
                            'allowMultipleSchool' => true,
                            'simpleDisplay' => false,
                            'extendCss' => false,
                            'useModel' => true,
                            'withAlumni' => true,
                            'model' => $model,
                            'attribute' => 'childid',
                            'htmlOptions' => array('class'=>'form-control')
                        )) ?>
                </div>
                <div class="form-group">
                    <label for=""><?php echo Yii::t('campus', 'Choose the school year');?></label>
                    <?php echo CHtml::activeDropDownList($model, 'yid', $calandarList,array('class'=>'form-control','empty'=>Yii::t('global', 'Please Select')));?>
                </div>
                <div class="form-group hide" id="student-classid">
                    <label for="TransferSchoolHistory_classid"><?php echo Yii::t('campus', 'Assigned class');?></label>
                    <?php echo CHtml::activeDropDownList($model, 'classid', array(),array('class'=>'form-control'));?>
                </div>
                <div class="form-group hide" id="reg-date">
                    <label for="TransferSchoolHistory_yid"><?php echo Yii::t('campus', 'Student start date');?></label>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="input-group">
                                <span class="input-group-addon" style="width:30px;background-color:#fff;border:0;">
                                    <input type="radio" name="TransferSchoolHistory[rank]" value="0" onclick="changeDisabled(this);" checked="checked"> 立即
                                </span>
                            </div><!-- /input-group -->
                        </div>
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-addon">
                                    <input type="radio" name="TransferSchoolHistory[rank]" onclick="changeDisabled(this);"  value="1">
                                </span>
                                <?php
                                $model->unsetAttributes();
                                $this->widget('zii.widgets.jui.CJuiDatePicker',array(
                                    'model'=>$model,
                                    'attribute'=>'transfer_time',
                                    'options'=>array(
                                        'dateFormat'=>'yy-mm-dd',
                                        'minDate'=>  date('Y-m-d',  time()),
                                    ),
                                    'htmlOptions'=>array(
                                        'placeholder'=>'指定时间',
                                        'class'=>'form-control',
                                        'disabled'=>'disabled',
                                        'style'=>'z-index:1051'
                                    ),
                                ));
                                ?>
                            </div><!-- /input-group -->
                        </div><!-- /.col-lg-6 -->
                    </div>
                </div>
                <div class="form-group">
                    <label for=""><?php echo Yii::t('campus', 'Comments');?></label>
                    <?php echo CHtml::activeTextArea($model, 'remark',array('class'=>'form-control'));?>
                </div>
            </div>
            <div class="modal-footer">
                <?php echo CHtml::hiddenField('branchId',$this->branchId);?>
                <button type="button" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Save');?></button>
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Close');?></button>
            </div>
        </div>
        <?php echo CHtml::endForm();?>
    </div>
</div>

<!-- Modal -->
<div class="modal fade" id="showChildModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel">孩子及家长常用信息</h4>
            </div>
            <div class="modal-body" id="show-child-info">

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script type="text/template" id="show-child-info-template">
    <div class="media">
        <div class="pull-left">
            <a href="#">
                <%=photo%>
            </a>
        </div>
        <div class="media-body">
            <h4 class="media-heading"><%=name%></h4>
            <p><%=birthday%></p>
            <p><%=gender%></p>
        </div>
    </div>
    <div class="row" style="padding-top:15px;">
        <div class="col-md-6">
            <dl>
                <dt>妈妈信息</dt>
                <dd>姓名：<%if (mother.name){%><%=mother.name%><%}else{%>无<%}%></dd>
                <dd>邮箱：<%if (mother.email){%><%=mother.email%><%}else{%>无<%}%></dd>
                <dd>手机：<%if (mother.mphone){%><%=mother.mphone%><%}else{%>无<%}%></dd>
            </dl>
        </div>
        <div class="col-md-6">
             <dl>
                <dt>爸爸信息</dt>
                <dd>姓名：<%if (father.name){%><%=father.name%><%}else{%>无<%}%></dd>
                <dd>邮箱：<%if (father.email){%><%=father.email%><%}else{%>无<%}%></dd>
                <dd>手机：<%if (father.mphone){%><%=father.mphone%><%}else{%>无<%}%></dd>
            </dl>
        </div>
    </div>
</script>

<script type="text/javascript">

    $('.datepicker').datepicker({
        changeMonth: true,
        changeYear: true,
        dateFormat: 'yy-mm-dd'
    });
    //跳转
    var currentCalendarId = <?php echo $calanderKey[$minYear]?>;
    jumpUrl = function(type){
        var url = '';
        if (type==='in'){
            url = "<?php echo $this->createUrl('/mcampus/transfer/index',array('type'=>'in'));?>";
        }else{
            url = "<?php echo $this->createUrl('/mcampus/transfer/index',array('type'=>'out'));?>";
        }
        window.location.href = url;
    };
    //弹出模态框
    popModal = function(){
        $('#myModal').modal();
    };

    //改变状态
    changeDisabled = function(_this){
        var value = $(_this).val();
        if (value==1){
            $("#TransferSchoolHistory_transfer_time").attr('disabled',false);
        }else{
            $("#TransferSchoolHistory_transfer_time").attr('disabled',true);
        }
    };
    var classList = {};
    $("#TransferSchoolHistory_yid").change(function(){
        if (this.value == currentCalendarId){
            $("#reg-date").removeClass('hide');
        }else{
            $("#reg-date").addClass('hide');
        }
        var value = this.value;
        if (this.value){
            $("#student-classid").removeClass('hide');
            if (_.isUndefined(classList[value])){
                $.ajax({
                    type: "POST",
                    dataType: "json",
                    async: false,
                    url: "<?php echo $this->createUrl('//mcampus/transfer/getClass',array('branchId'=>$this->branchId)); ?>",
                    data:{yid:this.value}
                }).done(function(data) {
                    classList[value] = data;
                    $('#TransferSchoolHistory_classid').html(classList[value]);
                });
            }else{
                $('#TransferSchoolHistory_classid').html(classList[value]);
            }
        }else{
            $("#student-classid").addClass('hide');
        }
    });

    callbackRefresh = function(){
        $.fn.yiiGridView.update('account-grid');
    };

    //查询孩子与父母信息
    viewParent  = function(_this){
        var childid = $(_this).data('childid');
        if (childid){
            $.ajax({
                    url: "<?php echo $this->createUrl('//mcampus/student/getParent');?>",
                    type: 'POST',
                    dataType: 'json',
                    data: {childid:childid}
            }).done(function(data) {
                showWindow(data);
            });
        }
    };

    //弹出窗口
    var childInfoTemplate = _.template($('#show-child-info-template').html());
    showWindow = function(data){
        if (data.state === 'success'){
            $('#showChildModal').modal();
            $('#show-child-info').empty();
            $('#show-child-info').append(childInfoTemplate(data.data));
        }
    }

    function exportData(url) {
        $.ajax({
            url: url,
            type: 'POST',
            success: function (res) {
                if (res.state == 'success') {
                    var data = res.data.items;
                    const filename = res.data.title;
                    const ws_name = "SheetJS";

                    const worksheet = XLSX.utils.aoa_to_sheet(data);
                    const workbook = XLSX.utils.book_new();
                    XLSX.utils.book_append_sheet(workbook, worksheet, ws_name);
                    // generate Blob
                    const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                    const blob = new Blob([wbout], {type: 'application/octet-stream'});
                    // save file
                    let link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    link.download = filename;
                    link.click();
                    setTimeout(function() {
                        // 延时释放掉obj
                        URL.revokeObjectURL(link.href);
                        link.remove();
                    }, 500);
                }
            },
            dataType: 'json'
        });
    }

</script>
<?php $this->renderPartial('//layouts/common/branchSelectBottom');?>
