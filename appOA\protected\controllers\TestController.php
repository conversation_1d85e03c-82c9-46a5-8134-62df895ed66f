<?php

class TestController extends Controller
{
    public function init() {
        parent::init();
        if (OA::isProduction()){
            Yii::app()->end();
        }
    }
	
	public function actionDefault(){
	
	}

    public function actionRotatePhoto(){
        require_once( Yii::getPathOfAlias('common.components.qiniu.rs') . '.php' );
        require_once( Yii::getPathOfAlias('common.components.qiniu.io') . '.php' );
        Qiniu_SetKeys($QINIU_ACCESS_KEY, $QINIU_SECRET_KEY);

        $client = new Qiniu_MacHttpClient(null);
        echo 'a';
    }


    public function actionIndex()
	{
        $this->layout='//layouts/blank';
        $pass = 'barney';
        $host = '127.0.0.1';
        $port = 80;
        $url = Yii::app()->params['OABaseUrl'].'/login.php';
        $cookie_file = Yii::app()->params['OABaseUploadPath'].'/cookie.txt';
        
        $cArr = array(
            '<EMAIL>' => 'TS & TT Jason Zhu 祝建升',
            '<EMAIL>' => 'PD of HH CP, CD OF EL, Ryan Cardwell',
            '<EMAIL>' => 'Steven',
            '<EMAIL>' => 'EL Tracy',
            '<EMAIL>' => 'OE Betty',
            '<EMAIL>' => 'CP 罗娜',
            '<EMAIL>' => 'OG Judy',
            '<EMAIL>' => 'TS Loli',
            '<EMAIL>' => 'TT 蒋颖',
            '<EMAIL>' => 'LJ 李岩',
            '<EMAIL>' => 'CF 姚瑶',
            '<EMAIL>' => 'SQ Abby',
            '<EMAIL>' => 'HH Jodie',
            '<EMAIL>' => 'EC Tina',
            '<EMAIL>' => 'SA Ivy',
            '<EMAIL>' => 'SA Ivy',
            '<EMAIL>' => 'GJ Vicky',
            '<EMAIL>' => 'YJ 贺琳',
            '<EMAIL>' => 'OP Yuki',
            '<EMAIL>' => 'Marketing Lisa',
            '<EMAIL>' => 'Finance Lucy',
            '<EMAIL>' => 'LJ 老师',
            '<EMAIL>' => 'SA 老师',
			'<EMAIL>' => 'SA 老师2',
            '<EMAIL>' => 'LJ 外教',
        );
        
        if (Yii::app()->request->isAjaxRequest){
            $email = Yii::app()->request->getParam('email', '');
            Yii::app()->user->logout();
            
            $argv = array('cookie' => array('email' => $email, 'pass' => $pass, 'op' => 'login'));
            foreach ($argv['cookie'] as $key => $value) {
                $params[] = $key . '=' . $value;
            }
            
            $data = implode('&', $params);
            
            $curl = curl_init();
            curl_setopt($curl, CURLOPT_URL, $url);
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, 0);
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, 1);
            curl_setopt($curl, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
            curl_setopt($curl, CURLOPT_FOLLOWLOCATION, 1);
            curl_setopt($curl, CURLOPT_AUTOREFERER, 1);
            curl_setopt($curl, CURLOPT_POST, 1);
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
            curl_setopt($curl, CURLOPT_COOKIEJAR, $cookie_file);
            curl_setopt($curl, CURLOPT_COOKIEFILE, $cookie_file);
            curl_setopt($curl, CURLOPT_TIMEOUT, 30);
            curl_setopt($curl, CURLOPT_HEADER, 0);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
            $tmpInfo = curl_exec($curl);
            if (curl_errno($curl)) {
                echo 'Errno'.curl_error($curl);
            }
            curl_close($curl);
            
            $cookie = @file($cookie_file);
            @unlink($cookie_file);
            
            if (isset($cookie[4])){
                $cooarr = explode('	', $cookie[4]);
                setcookie(trim($cooarr[5]), trim($cooarr[6]), trim($cooarr[4]), trim($cooarr[2]), trim($cooarr[0]));
            }
            
            Yii::app()->end();
        }
        
		$this->render('index', array('cArr'=>$cArr));
	}

    public function actionCdReport()
    {
        $sql = "select * from ivy_cr_main_activites a join ivy_calendar_week b on (a.yid=b.yid and a.weeknumber=b.weeknumber) where b.monday_timestamp>1389542400 and a.schoolid='CD_FC'";
        $rows = Yii::app()->db->createCommand($sql)->queryAll();
        echo <<<EOT
<style>
        *{font-size: 12px;line-height:20px;}
        .item{margin-bottom: 10px;}
        .title{font-size: 16px;font-weight:bold;}
</style>
EOT;

        foreach($rows as $row){
            echo '<p class="item">';
            echo '<div class="title">'.$row['weeknumber'].'周 '.date('Y年m月d日', $row['monday_timestamp']).' - '.date('Y年m月d日', $row['monday_timestamp']+345600).'</div>';
            echo '<div>本周主要活动</div>';
            echo '<div>'.Yii::app()->format->ntext($row['accontent']).'</div>';
            echo '</p>';
        }
    }

    public function actionSReport()
    {
        $sql = "select * from ivy_notes_sch_cla a join ivy_calendar_week b on (a.yid=b.yid and a.weeknumber=b.weeknumber) where b.monday_timestamp>1389542400 and a.schoolid='CD_FC' and a.type=10";
        $rows = Yii::app()->db->createCommand($sql)->queryAll();
        echo <<<EOT
<style>
        *{font-size: 12px;line-height:20px;}
        .item{margin-bottom: 10px;}
        .title{font-size: 16px;font-weight:bold;}
</style>
EOT;

        foreach($rows as $row){
            echo '<p class="item">';
            echo '<div class="title">'.$row['weeknumber'].'周 '.date('Y年m月d日', $row['monday_timestamp']).' - '.date('Y年m月d日', $row['monday_timestamp']+345600).'</div>';
            echo '<div>'.$row['en_important'].'</div>';
            echo '<div>'.$row['en_content'].'</div>';
            echo '</p>';
        }
    }

    public function actionEmployee()
    {
        echo <<<EOT
<style>
        *{font-size: 12px;line-height:20px;margin:0;padding:0;}
        .pagenext{page-break-after: always;}
</style>
EOT;
        $template = array(
            1=>'我知道对我的工作要求。',
            2=>'我有做好我的工作的所需要的材料和设备（或其他资源）/公司提供了适合我工作类型的工作环境。',
            3=>'我每天都有机会做我最擅长做的事情。',
            4=>'在过去的7天里，我因工作出色而受到表扬。',
            5=>'我觉得我的主管或同事关心我的个人情况。',
            6=>'工作单位有人鼓励我的发展。',
            7=>'在工作中，我觉得我的意见受到重视。',
            8=>'公司的使命使我觉得我的工作重要。',
            9=>'我的同事们致力于高质量的工作。',
            10=>'我在工作单位有一个最（很）要好的朋友。',
            11=>'在过去的6个月内，有人和我谈及我的进步。',
            12=>'过去1年里，我在工作中有机会学习和成长。',
            13=>'我认为公司氛围良好，并经常向朋友介绍说我的公司是个好公司，愿意推荐他/她来我的公司。',
            14=>'公司目前的薪酬对我有激励作用。',
            15=>'我的工作表现能够得到客观公正的衡量。',
            16=>'公司给我提供了一个清晰的职业发展规划。',
            17=>'我对公司总体满意',
            18=>'如果你可以选择改变艾毅的一件事情，会是什么事情？请详细描述：',
        );
        $path = 'D:/SQ.csv';
        $arr = file($path);
        $d = array();
        foreach($arr as $ar){
            $data = explode(',', $ar);
            $d[$data[0]][$data[1]] = $data[1] == 18 ? $data[3] : $data[2];
        }
        foreach($d as $page){
            echo '<div class="pagenext">';
            foreach($page as $id=>$val){
                echo '<div>'.$id.'. '.$template[$id].'</div>';
                echo '<div style="text-indent:2em;margin-bottom:10px;">'.iconv('GB2312', 'UTF-8', $val).'</div>';
            }
            echo '</div>';
        }
    }
}