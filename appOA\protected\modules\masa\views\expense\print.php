<style>
    #clsa td {
        border: solid 1px #ccc;
    }

</style>

<?php if($model->status == AsaExpense::STATS_PAST){ ?>
    <div class="panel panel-default">
        <table class="table table-bordered" id="clsa">
            <thead>
            <tr>
                <th colspan="3"
                    width="500"><?php echo Yii::t("asa", "校园/公司：") ?><?php echo $schoolsName[$model->branch]['title'] ?>  编号：<?php echo sprintf("%08d",$model->id); ?></th>
                <th colspan="3" width="550"><?php echo Yii::t("asa", "凭证编号：") ?></th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td rowspan="2" width="60"><?php echo Yii::t("asa", "项次") ?></td>
                <td colspan="2" rowspan="2" class="text-center"><?php echo Yii::t("asa", "内容摘要") ?></td>
                <td colspan="3" class="text-center"><?php echo Yii::t("asa", "费用分配") ?></td>
            </tr>
            <tr>
                <td width="180" class="text-center"><?php echo Yii::t("asa", "班级/部门") ?></td>
                <td width="180" class="text-center"><?php echo Yii::t("asa", "会计科目") ?></td>
                <td width="180" class="text-center"><?php echo Yii::t("asa", "金额") ?></td>
            </tr>
            <?php if ($model->asaExpenseItem) { ?>
                <?php foreach ($model->asaExpenseItem as $k => $item) { ?>
                    <tr>
                        <td><?php echo $k + 1 ?></td>
                        <td colspan="2"><?php echo $item->content ?></td>
                        <?php if(in_array($this->branchId, array('TJ_ES', 'TJ_EB'))): ?>
                            <td</td>
                        <?php else: ?>
                            <td><?php echo ($item->groupid) ? $course[$item->groupid][$item->courseid] . " / " . $group[$item->groupid] : '课后课' ?></td>
                        <?php endif; ?>
                        <td></td>
                        <td><?php echo $item->amount ?> <?php echo Yii::t('asa', '元'); ?></td>
                    </tr>
                <?php } ?>
            <?php } else { ?>
                <tr>
                    <td>1</td>
                    <td colspan="2"></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
            <?php } ?>
            <tr style="border-bottom: 2px solid #ccc">
                <th colspan="3"><?php echo Yii::t('asa', '金额合计'); ?></th>
                <td colspan="2"><?php echo Yii::t('asa', '大写：人民币 '); ?>
                    <span class="capitalNum"></span>
                </td>
                <td><?php echo $model->amount ?> <?php echo Yii::t('asa', '元'); ?></td>
            </tr>
            <tr>
                <td rowspan="4">1</td>
                <td width="130"><?php echo Yii::t("asa", "申请人<br>日期：") ?></td>
                <td width="300"><?php echo "" . $userName->getName() . " /<br> " . date("Y-m-d", $model->created) ?></td>
                <td width="50">3</td>
                <td><?php echo Yii::t("asa", "PRD/PD/VP<br>日期：") ?></td>
                <td></td>
            </tr>

            <tr>
                <td rowspan="3"><?php echo Yii::t("asa", "收款人<br>银行信息<br>（如有必要）") ?></td>
                <td><?php echo Yii::t("asa", "户名: ") . $model->payee_user ?></td>
                <td>4</td>
                <td><?php echo Yii::t("asa", "CXO/CEO<br>日期：") ?></td>
                <td></td>
            </tr>

            <tr>
                <td><?php
                    $bankStatu = ($model->payee_bank != '') ? $bank[$model->payee_bank] : "" ;
                    echo Yii::t("asa", "开户行：") . " " . $bankStatu; ?>
                    <?php
                        if ($model->type == 2) {
                            echo '<br>';
                            echo Yii::t("asa", "支行信息：") . " " . $model->payee_branch;
                        }
                     ?>
                     </td>
                <td>5</td>
                <td><?php echo Yii::t("asa", "财务部审核/<br>日期：") ?></td>
                <td></td>
            </tr>
            <tr>
                <td><?php echo Yii::t("asa", "账号：") . " " . $model->payee_account ?></td>
                <td>6</td>
                <td><?php echo Yii::t("asa", "付款方式：") ?></td>
                <td>
                    <label class="checkbox-inline">
                        <input type="checkbox" id="" value="1"> <?php echo Yii::t("asa", "网银") ?>
                    </label>
                    <label class="checkbox-inline">
                        <input type="checkbox" id="" value="2"> <?php echo Yii::t("asa", "现金") ?>
                    </label>
                </td>
            </tr>
            <tr>
                <td>2</td>
                <td><?php echo Yii::t("asa", "园长/经理<br> 日期：") ?></td>
                <td></td>
                <td>7</td>
                <td><?php echo Yii::t("asa", "网银转账/<br>现金签收<br> 日期：") ?></td>
                <td></td>
            </tr>
            </tbody>
        </table>
    </div>
<?php }else{ ?>
    <div class="alert alert-warning" role="alert"><?php echo Yii::t('user','等待审核状态为“通过”，从系统打印付款申请单，与原始票据一起邮寄回总部'); ?></div>
<?php } ?>
<script>

    var _amount = <?php echo json_encode($model->amount)?>;
    function capitalNum(n) {
        if (!/^(0|[1-9]\d*)(\.\d+)?$/.test(n)) return "数据非法";
        var unit = "亿万仟佰拾兆万仟佰拾亿仟佰拾万仟佰拾元角分", str = "";
        n += "00";
        var p = n.indexOf('.');
        if (p >= 0)
            n = n.substring(0, p) + n.substr(p + 1, 2);
        unit = unit.substr(unit.length - n.length);
        for (var i = 0; i < n.length; i++) str += '零壹贰叁肆伍陆柒捌玖'.charAt(n.charAt(i)) + unit.charAt(i);
        return str.replace(/零(仟|佰|拾|角)/g, "零").replace(/(零)+/g, "零").replace(/零(兆|万|亿|元)/g, "$1").replace(/(兆|亿)万/g, "$1").replace(/(兆)亿/g, "$1").replace(/兆/g, "$1").replace(/(兆|亿|仟|佰|拾)(万?)(.)仟/g, "$1$2零$3仟").replace(/^元零?|零分/g, "").replace(/(元|角)$/g, "$1整");
    }
    $('.capitalNum').text(capitalNum(_amount));
</script>