<div id="bottom_nav_journal">
    <div class="nav-jorunal-title">
        <i></i>
        <label onclick="minmax()">
            <?php echo Yii::t("portfolio", 'My feedback')?>
        </label>
    </div>
    <div style="position: relative;">
        <div style="position: absolute; right: 0; top: -20px;">
            <span title="<?php echo Yii::t("portfolio", 'Minimize Window')?>" class="min-max themin" onclick="themin()"></span>
            <span title="<?php echo Yii::t("portfolio", 'Maximize Window')?>" class="min-max themax" onclick="themax()"></span>
        </div>
    </div>
    <div>
        <div style="display: none;" class="nav-jorunal-content">
            <div class="replay">
                <div class="bgparentcomment" id="comments_replys"></div>
            </div>
            <div class="addform">
<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'coment_form',
	'enableAjaxValidation'=>true,
    'action'=>Yii::app()->getController()->createUrl("//portfolio/portfolio/savefeedback"),
    'clientOptions'=>array('validateOnSubmit'=>true, 'validateOnType'=>false),
)); ?>
                    <table width="100%" class="parent-input">
                        <tr>
                            <td colspan="2">
                                <div class="mood_input_title">
                                    <div class="fl">
                                        <label for="com_content"><?php echo addColon(Yii::t("portfolio", 'Your feedback to this week\'s report'));?></label>
                                    </div>
                                    <div class="fr">
                                        <?php echo CHtml::link(Yii::t("portfolio", 'View All Feedback'), Yii::app()->getController()->createUrl("//portfolio/portfolio/journal", array('type'=>'feedback')))?>
                                    </div>
                                    <div class="c"></div>
                                </div>
                                <div>
                                    <?php echo $form->textArea($model, 'com_content', array('class'=>'con'))?>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td id="status_msg"></td>
                            <td class="rbtn">
								<?php if(Yii::app()->user->checkAccess('sendFeedback', array("childid" => $this->getController()->getChildId()))):?>
									<?php echo $form->hiddenField($model, 'com_class_id', array('value'=>$classId))?>
									<?php echo $form->hiddenField($model, 'com_week_num', array('value'=>$weekNum))?>
									<?php
										echo CHtml::ajaxSubmitButton(Yii::t("portfolio", 'Submit'), Yii::app()->getController()->createUrl("//portfolio/portfolio/savefeedback"), array('beforeSend'=>'function(){bsend()}', 'complete'=>'function(){clte()}', 'dataType'=>'json', 'success'=>'function(data){successFun(data)}'), array('id'=>'asub'))
									?>
								<?php else:?>
									<?php echo Yii::t("portfolio","Submit not available to visitors.");?>
								<?php endif;?>
                            </td>
                        </tr>
                    </table>
<?php $this->endWidget(); ?>
            </div>
        </div>
    </div>
</div>
<script>
function minmax()
{
    if ($('.nav-jorunal-content').css('display') == 'none'){
        themax();
    }
    else {
        themin();
    }
}
function themax()
{
    getComments();
    $('.nav-jorunal-content').show();
    $('#Comments_com_content').focus();
    $.cookie('feedbackwindow', 'open', {expires: 90, path:'/'});
}
function themin()
{
    $('.nav-jorunal-content').hide();
    $.cookie('feedbackwindow', 'close', {expires: 90, path:'/'});
}
function successFun(data)
{
    if (data.stat == 10){
        clearStat();
        getComments();
        $('textarea.con').val('');
    }
    else {
        $('#status_msg').text(data.tip);
        //setTimeout('clearStat()', 3000);
    }
}

function bsend()
{
    $('#asub').attr('disabled', true);
}

function clte()
{
    $('#asub').attr('disabled', false);
}

function getComments()
{
    $.get("<?php echo Yii::app()->getController()->createUrl("//portfolio/portfolio/getcomments", array('classid'=>$classId, 'weeknum'=>$weekNum))?>", null, function(data){
        $('#comments_replys').html(data);
        var jh = $('#bottom_nav_journal').height();
        var wh = $(window).height();
        if (jh > (wh-50)){
            $('#comments_replys').css('height', wh-200+'px');
            $("#comments_replys").jscroll({W:"6px",Btn:{btn:false}});
        }
    })
}

function clearStat()
{
    $('#status_msg').text('');
}
function cArea(_this)
{
    $(_this).css('display', 'none');
    $('#carea').css('display', 'block');
    $('#carea textarea').focus();
    $("#comments_replys").jscroll({W:"6px",Btn:{btn:false}});
}
function showinput()
{
    if ($('#carea textarea').val() == ''){
        $('#carea').css('display', 'none');
        $('#cinput').css('display', 'block');
        $("#comments_replys").jscroll({W:"6px",Btn:{btn:false}});
    }
}
if ($.cookie('feedbackwindow') == 'open' || typeof($.cookie('feedbackwindow')) == 'undefined'){
    themax();
}

if (<?php echo $co?> > 0){
    getComments();
    $('.nav-jorunal-content').show();
    $('#Comments_com_content').focus();
}
</script>