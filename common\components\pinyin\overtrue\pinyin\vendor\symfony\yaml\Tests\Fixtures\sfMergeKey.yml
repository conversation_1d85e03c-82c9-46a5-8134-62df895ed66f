--- %YAML:1.0
test: Simple In Place Substitution
brief: >
    If you want to reuse an entire alias, only overwriting what is different
    you can use a << in place substitution. This is not part of the official
    YAML spec, but a widely implemented extension. See the following URL for
    details: http://yaml.org/type/merge.html
yaml: |
    foo: &foo
        a: <PERSON>
        b: <PERSON>
        c: Brian
        e: notnull
    bar:
        a: before
        d: other
        e: ~
        <<: *foo
        b: new
        x: Oren
        c:
            foo: bar
            bar: foo
    bar_inline: {a: before, d: other, <<: *foo, b: new, x: Oren, c: { foo: bar, bar: foo}}
    foo2: &foo2
        a: Ballmer
    ding: &dong [ fi, fei, fo, fam]
    check:
        <<:
            - *foo
            - *dong
        isit: tested
    head:
        <<: [ *foo , *dong , *foo2 ]
    taz: &taz
        a: Steve
        w:
            p: 1234
    nested:
        <<: *taz
        d: Doug
        w: &nestedref
            p: 12345
        z:
            <<: *nestedref
    head_inline: &head_inline { <<: [ *foo , *dong , *foo2 ] }
    recursive_inline: { <<: *head_inline, c: { <<: *foo2 } }
php: |
    [
        'foo' => ['a' => '<PERSON>', 'b' => '<PERSON>', 'c' => '<PERSON>', 'e' => 'notnull'],
        'bar' => ['a' => 'before', 'd' => 'other', 'e' => null, 'b' => 'new', 'c' => ['foo' => 'bar', 'bar' => 'foo'], 'x' => 'Oren'],
        'bar_inline' => ['a' => 'before', 'd' => 'other', 'b' => 'new', 'c' => ['foo' => 'bar', 'bar' => 'foo'], 'e' => 'notnull', 'x' => 'Oren'],
        'foo2' => ['a' => 'Ballmer'],
        'ding' => ['fi', 'fei', 'fo', 'fam'],
        'check' => ['a' => 'Steve', 'b' => 'Clark', 'c' => 'Brian', 'e' => 'notnull', 'fi', 'fei', 'fo', 'fam', 'isit' => 'tested'],
        'head' => ['a' => 'Steve', 'b' => 'Clark', 'c' => 'Brian', 'e' => 'notnull', 'fi', 'fei', 'fo', 'fam'],
        'taz' => ['a' => 'Steve', 'w' => ['p' => 1234]],
        'nested' => ['a' => 'Steve', 'w' => ['p' => 12345], 'd' => 'Doug', 'z' => ['p' => 12345]],
        'head_inline' => ['a' => 'Steve', 'b' => 'Clark', 'c' => 'Brian', 'e' => 'notnull', 'fi', 'fei', 'fo', 'fam'],
        'recursive_inline' => ['a' => 'Steve', 'b' => 'Clark', 'c' => ['a' => 'Ballmer'], 'e' => 'notnull', 'fi', 'fei', 'fo', 'fam'],
    ]
