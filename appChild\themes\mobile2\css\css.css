body {
  line-height: 1.6;
  font-family: -apple-system-font, "Helvetica Neue", sans-serif;
}
ul {
    list-style: none
}
.page {
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  z-index: 1;
}

.container, .page {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.container {
  overflow: hidden;
}

.page, body {
  background-color: #f8f8f8;
}

.page__hd {
  padding: 25px;
}

.page__desc {
  margin-top: 5px;
  color: #888;
  text-align: left;
  font-size: 14px;
}

.weui-panel_access {
  margin-bottom: 50px;
}

#fixed-bottom {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 50px;
  line-height: 50px;
  background-color: #5c5c5c;
  color: #ffffff;
  z-index: 9999;
}

.shopping-cart .shopping-count {
  width: 61.8%;
  float: left;
}

.shopping-cart .shopping-btn {
    width: 38.2%;
    background-color: #E64340;
    float: right;
    text-align: center;
    border-radius: 0;
    border: 2px solid #E64340;
}

.shopping-cart .shopping-btn a {
  display: block;
  color: #fff;
}

.bottom-text {
  padding-left: 10px;
}

.fl {
  float: left;
}

/*example.css*/

.page.js_show {
    opacity: 1
}

.page__hd {
    padding: 40px
}

.page__ft {
    padding-top: 40px;
    padding-bottom: 10px;
    text-align: center
}

.page__ft img {
    height: 19px
}

.page__ft.j_bottom {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0
}
.page.home .page__bd li {
    background-color: #fff;
    overflow: hidden;
    border-radius: 2px;
    margin: 10px 0;
}
.page.home .page__bd li.js_show .weui-flex {
    opacity: .4
}

.page.home .page__bd li.js_show .page__category {
    height: auto
}

.page.home .page__bd li.js_show .page__category-content {
    opacity: 1;
    -webkit-transform: translateY(0);
    transform: translateY(0)
}
.page.home .page__bd li:first-child {
    margin-top: 0
}

.page.home .page__category {
    height: 0;
    overflow: hidden
}

.page.home .page__category-content {
    opacity: 0;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    -webkit-transition: .3s;
    transition: .3s
}

.page.home .weui-flex {
    padding: 20px;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-transition: .3s;
    transition: .3s
}

.page.home .weui-cells {
    margin-top: 0
}

.page.home .weui-cells:after, .page.home .weui-cells:before {
    display: none
}

.page.home .weui-cell {
    padding-left: 20px;
    padding-right: 20px
}

.page.home .weui-cell:before {
    left: 20px;
    right: 20px
}

.page.button .page__bd {
    padding: 0 15px
}

.page.button .button-sp-area {
    margin: 0 auto;
    padding: 15px 0;
    width: 60%
}
