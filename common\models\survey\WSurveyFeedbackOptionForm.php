<?php


class WSurveyFeedbackOptionForm extends CFormModel
{
	public $option_answer = null;

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
				array('fb_id, survey_id, topic_id, classid, childid, userid, ic_id, followup', 'numerical', 'integerOnly'=>true),
				array('schoolid', 'length', 'max'=>255),
				array('option_answer', 'length', 'max'=>25),
				// The following rule is used by search().
				// Please remove those attributes that should not be searched.
				array('id, fb_id, survey_id, topic_id, classid, childid, userid, ic_id, schoolid, option_answer, followup', 'safe', 'on'=>'search'),
		);
	}


	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
				'id' => 'ID',
				'fb_id' => 'Fb',
				'survey_id' => 'Survey',
				'topic_id' => 'Topic',
				'classid' => 'Classid',
				'childid' => 'Childid',
				'userid' => 'Userid',
				'ic_id' => 'Ic',
				'schoolid' => 'Schoolid',
				'option_answer' => 'Option Answer',
				'followup' => 'Followup',
		);
	}

}