<?php
$this->breadcrumbs=array(
	Yii::t("user", "Login") => array('/user/login'),
	Yii::t("user", "Password Recovery"),
);
?>

<h1><?php echo Yii::t("user", "Password Recovery"); ?></h1>

<?php if(Yii::app()->user->hasFlash('recoveryMessage')): ?>
<div class="success">
<?php echo Yii::app()->user->getFlash('recoveryMessage'); ?>
</div>

	<div class="form">
		<div class="row submit">
			<?php echo CHtml::submitButton(
				Yii::t("global", "Back"),
				array(
					'class'=>'wider bigger',
					'onclick'=>'javascript:location.href=\''.Yii::app()->homeUrl.'\'',
					)
				); ?>
		</div>
	</div>

<?php else: ?>

<div class="form">
<?php echo CHtml::beginForm(); ?>

	<?php echo CHtml::errorSummary($form, ''); ?>
	
	<div class="row">
		<?php echo CHtml::activeLabel($form,'login_or_email'); ?>
		<?php echo CHtml::activeTextField($form,'login_or_email',array('class'=>'wider')) ?>
		<p class="hint"><?php echo Yii::t("user", "Please enter your login email address."); ?></p>
	</div>
	
	<div class="row submit">
		<?php echo CHtml::submitButton(Yii::t("global", "Submit"),array('class'=>'wider bigger')); ?>
	</div>

<?php echo CHtml::endForm(); ?>
</div><!-- form -->
<?php endif; ?>