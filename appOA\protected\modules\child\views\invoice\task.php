<div class="tips_light tips_block">
    <ul>
    <li><span>月付费计划将自动跳过寒暑假（如有）月份，请根据孩子情况手动开具寒暑假账单。</span></li>
    <li><span>如果计划已经生成过账单，必须将该账单作废并恢复任务为初始状态之后才能重新生成任务。</span></li>
    </ul>
</div>

<?php
foreach ($policy->configs[PAYGROUP_MONTH][ITEMS] as $ikey):
$tit=$paymentMappings['titleFeeType'][$ikey];
$dbt=$paymentMappings['dbFeeType'][$ikey];
?>
<div class="h_a">
    <?php echo Yii::t("payment", ":feeType Yearly Plan", array(":feeType"=>Yii::t("payment", $tit)) );?> 
    <?php
    if ( isset($planeds[$dbt]) ){
        echo CHtml::openTag("span");
        echo CHtml::link('重新生成未运行的计划', array('/child/invoice/createTasks', 'fee'=>$ikey), array('class'=>'J_dialog', 'title'=>Yii::t("payment", ":feeType Yearly Plan", array(":feeType"=>Yii::t("payment", $tit)) )));
        echo CHtml::closeTag("span");
    }
    ?>
</div>


<div class="table_full" style="margin-bottom: 20px;">
    <table width=100%>
        <colgroup>
            <col width="50">
            <col width="200">
            <col width="80">
            <col width="100">
            <col width="100">
            <col>
        </colgroup>
        <?php if ( isset($planeds[$dbt]) ):?>
            <tr>
                <th></th>
                <th>账单标题</th>
                <th>账单金额</th>
                <th>生成账单时间</th>
                <th>实际生成时间</th>
                <th></th>
            </tr>
            <tbody>
        
            <?php foreach($planeds[$dbt]->task as $task):?>
            <tr>
                <td class="tac"><span onclick='setplandate(<?php echo $task->id;?>)' class="<?php echo ($task->plantime)? 'enabled_icon':'disabled_icon';?>"></span></td>
                <td><?php echo $task->title?></td>
                <td><?php echo $task->amount?></td>
                <td><?php echo OA::formatDateTime(strtotime($task->plantime))?></td>
                <td><?php echo $task->runtime?OA::formatDateTime(strtotime($task->runtime)):'未生成'?></td>
                <td class="prompt_text">
                    <?php
                    if ($task->runtime):
                        echo CHtml::link('再次生成账单', 'javascript:;', array('class'=>'btn mr10','onclick'=>'saveplan('.$task->id.')'));
                        echo CHtml::link('作废生成的账单并恢复初始状态', array('renewTask', 'id'=>$task->id), array('class'=>'btn mr10 J_dialog', 'title'=>'本计划的关联账单'));
                    else:
                        echo CHtml::link('生成账单', 'javascript:;', array('class'=>'btn mr10','onclick'=>'saveplan('.$task->id.')')).' ';
                        if ($task->plantime):
                            echo CHtml::link('取消自动生成', 'javascript:;', array('class'=>'btn mr10','onclick'=>'setplandate('.$task->id.')'));
                        else:
                            echo CHtml::link('恢复自动生成', 'javascript:;', array('class'=>'btn mr10','onclick'=>'setplandate('.$task->id.')'));
                        endif;
                    endif;?>
                    
                    <?php
                    if(!empty($task->invoiceid)):
                        $invoiceIds = explode(",", $task->invoiceid);
                    ?>
                        <ul>
                            <?php
                            foreach($invoiceIds as $iId):
                                echo CHtml::openTag('li');
                                echo CHtml::link(Yii::t("payment","查看账单"), array('//child/invoice/viewInvoice', 'invoiceid'=>$iId), array('target'=>'_blank'));
                                echo CHtml::closeTag('li');
                            endforeach;
                            ?>
                        </ul>
                    <?php endif;?>
                </td>
            </tr>
            <?php endforeach; ?>
        <?php else:?>
        <tr>
            <td colspan="5">还没有设置计划 <?php echo CHtml::link('设置计划', array('/child/invoice/createTasks', 'fee'=>$ikey), array('class'=>'J_dialog', 'title'=>Yii::t("payment", ":feeType Yearly Plan", array(":feeType"=>Yii::t("payment", $tit)) )));?></td>
        </tr>
        <?php endif;?>
        </tbody>
    </table>
</div>
<?php endforeach; ?>

<script type="text/javascript" src="<?php echo Yii::app()->themeManager->baseUrl; ?>/base/js/common.js"></script>