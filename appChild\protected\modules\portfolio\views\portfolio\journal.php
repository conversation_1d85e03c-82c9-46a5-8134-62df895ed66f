<?php
$this->breadcrumbs=array(
	'Portfolio'=>array('/portfolio/portfolio/classes'),
	Yii::t("navigations",'Weekly Journal'),
);
$is_iPad = (bool) strpos($_SERVER['HTTP_USER_AGENT'],'iPad');
if (!$is_iPad){
    $colorbox = $this->widget('application.extensions.colorbox.JColorBox');
    $colorbox->addInstance('.colorbox_l', array('iframe'=>true, 'width'=>'930', 'height'=>'60%'));
    $colorbox->addInstance('.colorbox_s', array('iframe'=>true, 'width'=>'1030', 'height'=>'80%'));
    $cl = array('class'=>'colorbox_l');
    $cs = array('class'=>'colorbox_s');
}
else {
    $cl = array('target'=>'_blank');
    $cs = array('target'=>'_blank');
}
?>

<div id="left-col" class="no-bg span-6">
	<ul class="sub-nav" id="subnav">
    <?php foreach ($classe as $_year=>$cla):?>
        <li <?php if ($startyear == $_year):?>class="active"<?php endif;?>><a href="<?php echo Yii::app()->getController()->createUrl('//portfolio/portfolio/journal', array('startyear'=>$_year))?>"><?php echo IvyClass::formatSchoolYear($_year)?> <?php echo Yii::t("portfolio", 'School Year')?></a><em></em>
		<?php if ($startyear == $_year):?>
		<?php if($weeks):?>
		<p class="wtitle"><span><?php echo Yii::t("portfolio","Week List");?></span></p>
		<?php endif;?>
        <p class="week-list <?php if ( count($weeks) < 9 ) echo 'alignRight'; ?>">
			<?php foreach ($weeks as $w=>$wt):?>
				<?php if (isset($tweek[$w])):?>
					<span class="journal-week withphoto" alt="<?php echo $wt?>"><a href="<?php echo Yii::app()->getController()->createUrl('//portfolio/portfolio/journal', array('startyear'=>$_year, 'classid'=>$tweek[$w]['classid'], 'weeknum'=>$w));?>" <?php if($w==$weekNum) echo "class='active'"?>><?php echo $w;?></a></span>
				<?php elseif (isset($sctp[$w])):?>
                    <span class="journal-week" alt="<?php echo $wt?>"><a href="<?php echo Yii::app()->getController()->createUrl('//portfolio/portfolio/journal', array('startyear'=>$_year, 'classid'=>$sctp[$w], 'weeknum'=>$w));?>" <?php if($w==$weekNum) echo "class='active'"?>><?php echo $w;?></a></span>
				<?php else:?>
					<span class="no-journal" alt="<?php echo Yii::t("portfolio", 'No weekly journal found.')?>"><span><?php echo $w;?></span></span>
				<?php endif;?>
			<?php endforeach;?>
		<span id="week-number-hint" class="hover">
			<span></span>
		</span>
		</p>
        <?php endif;?>
	</li>
    <?php endforeach;?>
	<?php if(!empty($classe)):?>
	<li>
		<?php echo CHtml::link(Yii::t("navigations","All Feedback & Replies"), array("//portfolio/portfolio/journal", "type"=>"feedback"));?>
	</li>
	<?php endif;?>
	</ul>
</div>
<script>
$("p.week-list span").hover(
  function () {
      var dv = $(this).attr("alt");
      if (dv != '' && typeof(dv) != 'undefined'){
        var offset = $(this).position();
        var _top = parseFloat(offset.top-30);
        var _left = parseFloat(offset.left+10);
        var html = "<span>"+dv+"</span>";
        $("#week-number-hint").html(html);
        $("#week-number-hint").css('top',_top+'px');
        $("#week-number-hint").css('left',_left+'px');
        $("#week-number-hint").css('visibility','visible');
    }
  },
  function () {
	$("#week-number-hint").css('visibility','hidden');
  }
);

</script>

<?php
$this->widget('application.extensions.dit.Dit', array(
        'bId'=>'subnav',
        'cId'=>'mainbox',
    )
);
?>

<div id="main-col" class="span-18 last">

<?php if ($classId):?>
<h1>
	<label><?php echo Yii::t("navigations", 'Weekly Journal');?></label>
	<span><?php echo CHtml::link($class_model->title, array('//portfolio/portfolio/classes'));?></span></h1>
    <?php $jdate=isset($weeks[$weekNum])?$weeks[$weekNum]:''; $ar = explode(Yii::t("global","("), $jdate);?>
    <?php if($jdate){?>
    <p class="journal-date"><?php echo $ar[0];?><span><?php echo Yii::t("global","(").$ar[1];?></span></p>
    <?php }?>

<div class="weekly-misc">
	<ul>
        <?php if(Yii::app()->params['ownerConfig']['key'] != 'BJ_DS'):?>
		<li class="schedule">
            <?php echo CHtml::link(Yii::t("portfolio", "Weekly Schedule"), array('//portfolio/portfolio/schedule', 'classid'=>$classId, 'weeknum'=>$weekNum), $cs);?>
		</li>
        <?php endif;?>
		<li class="lunchMenu">
            <?php echo CHtml::link(Yii::t("portfolio", "Lunch Menu"), array('//portfolio/portfolio/lunch', 'schoolid'=>$schoolId, 'yid'=>$yId, 'weeknum'=>$weekNum), $cl);?>
		</li>
        <?php if($startyear > 2013):?>
        <li class="teaching">
            <?php echo CHtml::link(Yii::t("portfolio", "Unit Teaching Plan"), array('//portfolio/portfolio/teachingContent', 'classid'=>$classId, 'yid'=>$yId, 'weeknum'=>$weekNum), $cl);?>
		</li>
        <?php endif;?>
	</ul>
	<div class="clear"></div>
</div>

<?php
$this->beginWidget('application.extensions.schoolnews.SchoolNews', array(
        'yId'=>$yId,
        'weekNum'=>$weekNum,
        'schoolId'=>$schoolId,
    )
);
?>

<?php $this->endWidget();?>

<?php
$this->widget('application.extensions.classnews.ClassNews', array(
        'classId'=>$classId,
        'yId'=>$yId,
        'weekNum'=>$weekNum,
    )
);
?>

<?php
$this->widget('application.extensions.specialnotes.SpecialNotes', array(
        'classId'=>$classId,
        'yId'=>$yId,
        'weekNum'=>$weekNum,
        'childId'=>$this->childid
    )
);
?>


<?php
$this->widget('application.extensions.childreport.ChildReport', array(
        'classId'=>$classId,
        'yId'=>$yId,
        'weekNum'=>$weekNum,
        'childId'=>$this->childid
    )
);
?>

<?php
if ($classId && $weekNum):
$this->widget('application.extensions.feedback.Feedback', array(
        'classId'=>$classId,
        'weekNum'=>$weekNum,
    )
);
endif;
?>
</div>

<?php else:?>
<?php echo Yii::t("portfolio","No weekly journal found.");?>
<?php endif;?>

<div class="clear"></div>

<?php
$this->widget('ext.guide.Guide', array(
    'options'=>array(
        'guides'=>array(
            0=>array(
                'selector'=>'#pfolio-sub-nav ul li:eq(2)',
                'caption'=>'portfolio_classes',
				'style'=>array(
					'arrow'=>array(
						'direction'=>'up',
						'top'=>0,
						'left'=>0,
					),
					'mainbox'=>array(
						'leftOffset'=>100, //guide窗口与主窗口(.page) 左侧的距离
						'top'=>50,
					),
				),
				'next'=>10,
            ),

            10=>array(
                'selector'=>'#bottom_nav_journal',
                'fixed'=>1,
				'style'=>array(
					'arrow'=>array(
						'direction'=>'downright',
						'top'=>-250,
						'left'=>-30,
					),
					'mainbox'=>array(
                        'pos'=>'arrow',
						'leftOffset'=>-550,
						'top'=>-590,
					),
				),
				'next'=>10,
            ),
            10=>array(
                'selector'=>'#pfolio-sub-nav .pf-sub-nav li:eq(3)',
				'style'=>array(
					'arrow'=>array(
						'direction'=>'up',
						'top'=>-2,
						'left'=>30,
					),
					'mainbox'=>array(
						'leftOffset'=>140,
						'top'=>85,
					),
				),
                'next'=>20,
				'prev'=>0,
            ),
            20=>array(
                'selector'=>'#pfolio-sub-nav .pf-sub-nav li:eq(4)',
				'style'=>array(
					'arrow'=>array(
						'direction'=>'up',
						'top'=>-2,
						'left'=>2,
					),
					'mainbox'=>array(
						'leftOffset'=>140,
						'top'=>85,
					),
				),
				'prev'=>10,
            ),
        ),
    )
));
?>
