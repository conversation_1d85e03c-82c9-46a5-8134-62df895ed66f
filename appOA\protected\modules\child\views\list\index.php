<?php
$this->renderPartial('header');

$this->menu=array(
	array('label'=>Yii::t('child', '注册孩子'), 'url'=>array('update'), 'role'=>'add'),
);

$this->widget('ext.ivyCGridView.IvyCGridView', array(
	'id'=>'child-list',
	'dataProvider'=>$dataProvider,
	'template'=>"<div class='table_list'>{items}</div><div class='table_list'>{summary}</div><div class='table_list'>{pager}</div>",
    'colgroups'=>array(
        array(
            "colwidth"=>array(80,150,90, 120, 120),
        )
    ),
	'columns'=>array(
		'childid'=>array(
			'name'=>'childid',
			'header'=>Yii::t("labels","ID"),
		),
		'name'=>array(
			'type'=>'raw',
			'header' => Yii::t('child', 'Name'),
            'value'=>'CHtml::link($data->getChildName(), array("//child/index/index","childid"=>$data->childid), array("target"=>"_blank"))',
        ),
		'gender'=>array(
            'name'=>'gender',
            'value'=>'OA::renderChildGender($data->gender)',
        ),
		'birthday'=>array(
            'name'=>'birthday_search',
        ),
		'credit'=>array(
            'name'=>'credit',
        ),
		'regdate'=>array(
            'name'=>'created_timestamp',
			'header'=>Yii::t('labels', 'Created Time'),
            'value'=>'OA::formatDateTime($data->created_timestamp)',
        )
	)
)); ?>


