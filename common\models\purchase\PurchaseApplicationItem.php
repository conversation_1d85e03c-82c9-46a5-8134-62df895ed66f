<?php

/**
 * This is the model class for table "ivy_purchase_application_item".
 *
 * The followings are the available columns in table 'ivy_purchase_application_item':
 * @property integer $id
 * @property integer $aid
 * @property integer $pid
 * @property string $title
 * @property double $price
 * @property string $num
 * @property double $total_price
 * @property integer $type
 */
class PurchaseApplicationItem extends CActiveRecord
{
	const PURCHASE_PRODUCT = 1;            //商品库商品
	const PURCHASE_STAND   = 2;            //标配商品
	const PURCHASE_CUSTOM  = 3;            //自定义商品
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_purchase_application_item';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('aid, pid, title, price, num, total_price, type, cid', 'required'),
			array('aid, pid, type, cid', 'numerical', 'integerOnly'=>true),
			array('price, total_price', 'numerical'),
			array('title', 'length', 'max'=>255),
			array('num', 'length', 'max'=>10),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, aid, pid, title, price, num, total_price, type, cid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'application'=>array(self::BELONGS_TO ,'PurchaseApplication' ,'aid'),
			'company' => array(self::HAS_MANY ,'PurchaseProductsCompany' , array('pid' => 'pid')),
			'products' => array(self::HAS_MANY ,'PurchaseProducts' , array('id' => 'pid')),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'aid' => 'Aid',
			'pid' => 'Pid',
			'title' => 'Title',
			'price' => 'Price',
			'num' => 'Num',
			'total_price' => 'Total Price',
			'type' => 'Type',
			'cid' => 'Cate',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('aid',$this->aid);
		$criteria->compare('pid',$this->pid);
		$criteria->compare('title',$this->title,true);
		$criteria->compare('price',$this->price);
		$criteria->compare('num',$this->num,true);
		$criteria->compare('total_price',$this->total_price);
		$criteria->compare('type',$this->type);
		$criteria->compare('cid',$this->cid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return PurchaseApplicationItem the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * 获取采购商品的类型
	 * @return [type] [description]
	 */
	public function getType()
	{
		if ($this->type == PurchaseApplicationItem::PURCHASE_PRODUCT) {
			return '库商品';
		}
		if ($this->type == PurchaseApplicationItem::PURCHASE_STAND) {
			return '标配';
		}
		if ($this->type == PurchaseApplicationItem::PURCHASE_CUSTOM) {
			return '自定义商品';
		}
		return '无';
	}

	/**
	 * 根据采购类型生成链接
	 * @param  string $value [description]
	 * @return [type]        [description]
	 */
	public function getLink()
	{
		if ($this->type == PurchaseApplicationItem::PURCHASE_PRODUCT) {
			return CHtml::link($this->title, Yii::app()->createUrl('moperation/purchaseProducts/view',array('id'=>$this->pid)), array('target'=>'_blank'));
		}
		if ($this->type == PurchaseApplicationItem::PURCHASE_STAND) {
			return CHtml::link($this->title, Yii::app()->createUrl('moperation/purchaseStandard/view',array('id'=>$this->pid)), array('target'=>'_blank'));
		}
		if ($this->type == PurchaseApplicationItem::PURCHASE_CUSTOM) {
			return $this->title;
		}
		return '无';
	}
}
