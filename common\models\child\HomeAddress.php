<?php

/**
 * This is the model class for table "ivy_child_home_contact".
 *
 * The followings are the available columns in table 'ivy_child_home_contact':
 * @property integer $childid
 * @property integer $fid
 * @property integer $mid
 * @property string $en_address
 * @property string $en_province
 * @property string $en_city
 * @property string $en_district
 * @property string $en_postcode
 * @property string $en_telephone
 * @property string $family_id
 */
class HomeAddress extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return HomeAddress the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_child_home_contact';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('en_address', 'required'),
			array('en_province, en_city, en_district, en_postcode', 'safe'),
			//array('childid, fid, mid', 'numerical', 'integerOnly'=>true),
			array('en_address, en_province, en_city, en_district, en_postcode, en_telephone', 'length', 'max'=>255),
            array('family_id', 'length', 'max' => 32),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('childid, fid, mid, en_address, en_province, en_city, en_district, en_postcode, en_telephone', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'childid' => 'Childid',
			'fid' => 'Fid',
			'mid' => 'Mid',
			'en_address' => Yii::t("labels", 'Address'),
			'en_province' => Yii::t("labels", 'Province'),
			'en_city' => Yii::t("labels", 'City'),
			'en_district' => Yii::t("labels", 'District'),
			'en_postcode' => Yii::t("labels", 'Postcode'),
			'en_telephone' => Yii::t("labels", 'Telephone'),
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('childid',$this->childid);
		$criteria->compare('fid',$this->fid);
		$criteria->compare('mid',$this->mid);
		$criteria->compare('en_address',$this->en_address,true);
		$criteria->compare('en_province',$this->en_province,true);
		$criteria->compare('en_city',$this->en_city,true);
		$criteria->compare('en_district',$this->en_district,true);
		$criteria->compare('en_postcode',$this->en_postcode,true);
		$criteria->compare('en_telephone',$this->en_telephone,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}