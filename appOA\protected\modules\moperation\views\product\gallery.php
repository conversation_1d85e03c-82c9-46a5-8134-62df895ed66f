<style>
<!--
.photos {
    margin-top: 20px;
}
ul, ol {
    list-style-type: none;
    margin: 0;
    padding: 0;
}
.photos ul li a {
    display: block;
    height: 130px;
    margin: 4px;
    width: 200px;
}
.photos ul li {
    border: 1px solid #D8D8D8;
    float: left;
    margin-bottom: 20px;
    margin-right: 20px;
    position: relative;
}
.photos ul li:hover {
    border-color: #9A9FA4;
    box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.85);
}
.photos ul li:hover a.btn {
    display: block;
}
.photos ul li:hover a.btn:hover {
    background-position: 147px 25px;
}
.photos ul li a.btn:first-of-type {
    background-position: 30px 0;
    left: 0;
    right: auto;
}
.photos ul li a.btn:first-of-type:hover {
    background-position: 30px 25px;
}
.photos ul li a.cover {
    background-position: 30px 25px !important;
    display: block;
}
.photos ul li a span.thumb {
    display: block;
    height: 100%;
    width: 100%;
    background-position: center center;
    background-repeat: no-repeat;
}
.photos ul li a span.title {
    background: none repeat scroll 0 0 #FAFAFA;
    bottom: 0;
    display: block;
    height: 25px;
    line-height: 25px;
    overflow: hidden;
    position: absolute;
    text-indent: 3px;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
}
.photos ul li a.btn {
    background-color: transparent;
    background-image: url("/images/controls.png");
    background-position: 147px 0;
    border: 0 none;
    cursor: pointer;
    display: none;
    height: 24px;
    position: absolute;
    right: 0;
    top: 0;
    width: 24px;
    padding:0;
}
.photos ul li a.cover {
    background-position: 30px 25px !important;
    display: block;
}
.plupload_filelist_footer{
    height: auto !important;
}
.plupload_scroll .plupload_filelist{
    height: 100px !important;
}
-->
</style>
<script type="text/javascript">
function fupload(up, file, response)
{
    var obj = jQuery.parseJSON(response.response);
    if(obj.result == 1){
        var html = "<li id='pic_"+obj.id+"'>";
        html+= '<a class="btn" href="javascript:;" onclick="cover('+obj.id+')" id="cover_'+obj.id+'"></a>';
        html+= '<a class="btn" href="javascript:;" onclick="delpic('+obj.id+')"></a>';
        html += "<a style='background-image: url("+obj.pic+")' class='J_dialog' href='<?php echo $hrefto=$this->createUrl('/operations/product/pic');?>/id/"+obj.id+"'>";
        html += "</a></li>";
        $('#photos ul').prepend(html);
        //$('#pic_'+obj.id+' a.dialogform').formDialog({'onSuccess':function(data, e){alert(data.message)}});
    }
}
function delpic(id)
{
    $.post('<?php echo $this->createUrl("/moperation/product/delpic")?>', {id:id}, function(data){
        if(data.status == 'success'){
            $('#pic_'+id).remove();
        }
        else {
            alert(data.message)
        }
    }, 'json');
}
function cover(id)
{
    $.post('<?php echo $this->createUrl("/moperation/product/cover")?>', {id:id}, function(data){
        if(data.status == 'success'){
            $('.photos ul a.btn').removeClass('cover');
            $('#cover_'+id).addClass('cover');
        }
    }, 'json');
}
</script>

<div class="p5" style="height: 400px;overflow-y: auto;">
    <h4><?php echo $model->getContent()?></h4>
    <?php
    if($this->checkActionAccess('o_X_Adm_Common')):
    $this->widget('application.extensions.gallery.PluploadWidget', array(
        'config' => array(
            'runtimes' => 'flash,silverlight,browserplus,html5',
            'url' => $this->createUrl('upload', array('productid'=>$productid)),
            'max_file_size' => '100mb',
            'chunk_size' => '1mb',
            'filters' => array(
                array('title' => Yii::t("bview", 'Images files'), 'extensions' => 'jpg,jpeg,gif,png'),
            ),
           'language' => Yii::app()->language,
           'max_file_number' => 100,
           'autostart' => false,
           'jquery_ui' => FALSE,
           'reset_after_upload' => false,
        ),
        'id' => 'uploader',
        'callbacks' => array(
            'FileUploaded' => 'fupload',
        )
    ));
    endif;
    ?>
    <div id="photos" class="photos">
        <ul>
            <?php foreach ($imageModel as $item):?>
            <li id="pic_<?php echo $item->id?>">
                <?php
                    $hrefto=$this->createUrl('/operations/product/pic/', array('id'=>$item->id)); // 图片排序功能 暂时停用
                ?>
                <a class="btn <?php if ($model->cover == $item->id):?>cover<?php endif;?>" href="javascript:;" onclick="cover(<?php echo $item->id?>)" title="<?php if ($model->cover == $item->id):echo Yii::t("points", "封面");else:echo Yii::t("points", "设为封面");endif;?>" id="cover_<?php echo $item->id?>"></a>
                <a class="btn" href="javascript:;" onclick="delpic(<?php echo $item->id?>)" title="<?php echo Yii::t("points", "删除")?>"></a>
                <a href="javascript:;" class="" title="<?php echo $model->getContent()?>">
                    <span class="thumb" style="background-image: url('<?php echo $imageUrl.$item->image?>');"></span>
                </a>
            </li>
            <?php endforeach;?>
        </ul>
    </div>
</div>