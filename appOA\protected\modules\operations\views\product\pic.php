<?php
$form = $this->beginWidget('CActiveForm', array(
    'id' => 'campuspic-form',
    'enableAjaxValidation' => true,
    'htmlOptions'=>array('class'=>'J_ajaxForm'),
));
?>

<div class="pop_cont pop_table" style="height:auto;">
    <table width="100%">
        <colgroup>
            <col class="th" />
            <col />
        </colgroup>
        <tbody>
            <tr>
                <th></th>
                <td><?php echo CHtml::image($imageUrl . $model->image, '', array('style' => 'width:200px;')) ?><?php echo $form->hiddenField($model, 'id') ?></td>
            </tr>
            <tr>
                <th>排序</th>
                <td><?php echo $form->numberField($model, 'weight', array('class'=>'input length_2')); ?></td>
            </tr>
        </tbody>
    </table>
</div>
<div class="pop_bottom">
    <button type="button" class="btn fr" id="J_dialog_close"><?php echo Yii::t('global','Cancel');?></button>
    <button class="btn fr btn_submit mr10 J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
</div>

<?php $this->endWidget(); ?>