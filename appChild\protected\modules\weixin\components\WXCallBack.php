<?php
class WXCallBack
{
	private $token;
	private $userMsg=null;
	
	public function __construct($token){
		if(empty($token)) $token = 'I34vyOn92L456in13e';
		$this->token = $token;
	}
	
	//初次接入API时的方法
	public function initialValidate()
    {
        $echoStr = $_GET["echostr"];

        //valid signature , option
        if($this->checkSignature()){
        	echo $echoStr;
        	Yii::app()->end();
        }
    }
	
	public function getUserMsg(){
		return $this->userMsg;
	}
	
	public function validateRequest(){
		return $this->checkSignature();
	}
	
	public function parseMsg(){
		$postStr = $GLOBALS["HTTP_RAW_POST_DATA"];
		Yii::app()->cache->set("weixin003", $postStr);
		//$postStr = '<xml> <ToUserName><![CDATA[fromUser]]></ToUserName> <FromUserName><![CDATA[toUser]]></FromUserName> <CreateTime>1375438545</CreateTime> <MsgType><![CDATA[text]]></MsgType> <Content><![CDATA[test]]></Content> <FuncFlag>0</FuncFlag> </xml>';
		if (!empty($postStr)){
			$this->userMsg = simplexml_load_string($postStr, 'SimpleXMLElement', LIBXML_NOCDATA);
		}else{
			$this->userMsg = null;
		}
	}

    public function responseMsg()
    {
		//get post data, May be due to the different environments
		$postStr = $GLOBALS["HTTP_RAW_POST_DATA"];
//		$postStr = '<xml>
// <ToUserName><![CDATA[toUser]]></ToUserName>
// <FromUserName><![CDATA[fromUser]]></FromUserName>
// <CreateTime>12345678</CreateTime>
// <MsgType><![CDATA[text]]></MsgType>
// <Content><![CDATA[content]]></Content>
// </xml>';
      	//extract post data
		if (!empty($postStr)){
		
				$fromCache = Yii::app()->cache->get('weixin');
				echo CHtml::encode($fromCache);
				echo '<hr>';
                
              	$postObj = simplexml_load_string($postStr, 'SimpleXMLElement', LIBXML_NOCDATA);
				
				//print_r($postObj);
				
                $fromUsername = $postObj->FromUserName;
                $toUsername = $postObj->ToUserName;
                $keyword = trim($postObj->Content);
                $time = time();
                $textTpl = "<xml>
							<ToUserName><![CDATA[%s]]></ToUserName>
							<FromUserName><![CDATA[%s]]></FromUserName>
							<CreateTime>%s</CreateTime>
							<MsgType><![CDATA[%s]]></MsgType>
							<Content><![CDATA[%s]]></Content>
							<FuncFlag>0</FuncFlag>
							</xml>";             
				if(!empty( $keyword ))
                {
					switch($keyword){
						case 'test':
							$newsTpl = "<xml>
								<ToUserName><![CDATA[%s]]></ToUserName>
								<FromUserName><![CDATA[%s]]></FromUserName>
								<CreateTime>%s</CreateTime>
								<MsgType><![CDATA[%s]]></MsgType>
								<ArticleCount>3</ArticleCount>
								<Articles>
								<item>
								<Title><![CDATA[%s]]></Title> 
								<Description><![CDATA[%s]]></Description>
								<PicUrl><![CDATA[%s]]></PicUrl>
								<Url><![CDATA[%s]]></Url>
								</item>
								<item>
								<Title><![CDATA[%s]]></Title>
								<Description><![CDATA[%s]]></Description>
								<PicUrl><![CDATA[%s]]></PicUrl>
								<Url><![CDATA[%s]]></Url>
								</item>
								<item>
								<Title><![CDATA[%s]]></Title>
								<Description><![CDATA[%s]]></Description>
								<PicUrl><![CDATA[%s]]></PicUrl>
								<Url><![CDATA[%s]]></Url>
								</item>
								</Articles>
								</xml> 
							";
							$msgType = "news";
							$news = array(
								'0'=>array(
									'title'=>'《好孕妈妈》专访：园长解读宝宝开心入园四步曲',
									'description'=>'2013年6月，艾毅教育机构多元智能总监顾克女士接受了《好孕妈妈》杂志专访。',
									'picurl'=>'http://www.ivyschools.com/upload/news/thumbs/news_51f75317406d5.jpg',
									'url'=>'http://www.ivyschools.com/Information/News/792?lang=zh_cn',
								),
								'1'=>array(
									'title'=>'艾毅新城国际校园探秘艺术世界',
									'description'=>'2013年7月20日，艾毅国际幼儿园新城国际校园第二次在新城国际社区中心花园与小朋友和家长见面，大家的热情依然有增无减。',
									'picurl'=>'http://www.ivyschools.com/upload/news/thumbs/news_51f22e49e2ba4.jpg',
									'url'=>'http://www.ivyschools.com/Information/News/790?lang=zh_cn',									
								),
								'2'=>array(
									'title'=>'绑定艾毅在线帐号',
									'description'=>'绑定艾毅在线帐号',
									'picurl'=>'http://www.ivyschools.com/upload/news/thumbs/news_51f22e49e2ba4.jpg',
									'url'=>'http://www.ivyonline.cn/weixin/user/index',									
								)
							);
							$contentStr = "http://www.ivyschools.com";
							$resultStr = sprintf($newsTpl, $fromUsername, $toUsername, $time, $msgType,
												$news[0]['title'],
												$news[0]['description'],
												$news[0]['picurl'],
												$news[0]['url'],
												$news[1]['title'],
												$news[1]['description'],
												$news[1]['picurl'],
												$news[1]['url'],
												$news[2]['title'],
												$news[2]['description'],
												$news[2]['picurl'],
												$news[2]['url']
												);
						break;
						default:
							$msgType = "text";
							$contentStr = "指令列表\r\n";
							$contentStr = "1. 链接信息 <a href='http://www.ivyonline.cn/weixin/user/index'>测试</a>";
							$resultStr = sprintf($textTpl, $fromUsername, $toUsername, $time, $msgType, $contentStr);						
						break;
					}
					Yii::app()->cache->set("weixin", $resultStr);
                	echo '|'.$resultStr.'|';
                }else{
                	echo "Input something...";
                }

        }else {
        	echo "";
        	exit;
        }
    }
		
	private function checkSignature()
	{
        $signature = $_GET["signature"];
        $timestamp = $_GET["timestamp"];
        $nonce = $_GET["nonce"];	
        		
		$tmpArr = array($this->token, $timestamp, $nonce);
		sort($tmpArr, SORT_STRING);
		$tmpStr = implode( $tmpArr );
		$tmpStr = sha1( $tmpStr );
		
		if( $tmpStr == $signature ){
			return true;
		}else{
			return false;
		}
	}
}
?>