<script src="<?php echo Yii::app()->theme->baseUrl; ?>/js/vue.js"></script>
<style>
	.strong{
		position: absolute;
	    left: 0;
	    bottom: 6px;
	    width: 100%;
	    height: 10px;
	    display: block;
	}
	.red{
		width: 8px;
	    height: 8px;
	    display: inline-block;
	    border-radius: 4px;
	    background:#FFBE00;
	    margin:2px;
	}
	.yellow{
		width: 8px;
	    height: 8px;
	    display: inline-block;
	    border-radius: 4px;
	    background:#10AEFF;
	  	 margin:2px;
	}
	.green{
		width: 8px;
	    height: 8px;
	    display: inline-block;
	    border-radius: 4px;
	    background:green;
	    margin:2px;
	}
	.typeColor{
		position: relative;
		width: 95px;
		padding-left:15px;
	}
	.colorpot{
		width: 8px;
	    height: 8px;
	    display: inline-block;
	    border-radius: 4px;
	    position:absolute;
	    left:0;
	    top:50%;
	    margin-top: -4px
	}
	.listred{
	    background:#FFBE00;
	}
	.listyellow{
	    background:#10AEFF;
	}
	.tipred{
		width: 8px;
	    height: 8px;
	    display: inline-block;
	    border-radius: 4px;
	    background:#FFBE00;
	}
	.tipyellow{
		width: 8px;
	    height: 8px;
	    display: inline-block;
	    border-radius: 4px;
	    background:#10AEFF;
	}
	.tipgreen{
		width: 8px;
	    height: 8px;
	    display: inline-block;
	    border-radius: 4px;
	    background:green;
	}
	.table{
		width: 100%;
	    border-width: 1px;
	    border-color: #999999;
	    border-collapse: collapse;
	    color: #666;
	}
	.table, .table td, .table th {
	    text-align: center;
	    height:40px;
        position:relative;
	}
	.btn{
		background:#10AEFF;
		color: #fff;
		display: inline-block;
		width: 100%;
		border-radius:2px;
	}
	.line{
		margin-top: 0
	}
	.line:after{
		border-bottom: none;
	}
	.line:before{
		border-top: none;
	}
	.bg{
		position: relative;
		display: inline-block;
	}
	.bg i{
		position: absolute;
		right:-7px;
		top:-7px;
		display: inline-block;
		background:#FFBE00;
		width:16px;
		height:16px;
		border-radius:50%;
		text-align: center;
		color: #fff;
		font-style:normal;
		line-height: 16px;
	}
	i{
		font-style: normal;
		font-size:12px;
	}
	.monBtn li{
		font-style: normal;
		float: left;
		width:10%;
		margin-right:2%;
		text-align: center;
		border: 1px solid #ddd;
		border-radius:3px;
		background: #f6f6f6;
		color:#999;
	}
	.monBtn li:last-child{
		margin-right:0%;
	}
	.clearfix{
		clear: both;
	}
	.org{
		font-size: 12px;
		color:#10AEFF
	}
	.weui_panel{
		margin-top: 0
	}
	 [v-cloak]{display: none;}
	.month ul {
	    margin: 0;
	    padding: 0;
	}
	 
	.month ul li {
	    color: #666;
	    font-size:18px;
	    text-transform: uppercase;
	}
	.month .prev {
	    float: left;
	    content: " ";
	    display: inline-block;
	    -webkit-transform: rotate(585deg);
	    transform: rotate(585deg);
	    height:10px;
	    width:10px;
	    border-width: 2px 2px 0 0;
	    border-color: #666;
	    border-style: solid;
	    position: relative;
	    left:20px;
	    top:9px;
	}
	 ul {list-style-type: none;}
	.month .next {
	    float: right;
	    content: " ";
	    display: inline-block;
	    -webkit-transform: rotate(45deg);
	    transform: rotate(45deg);
	    height:10px;
	    width:10px;
	    border-width: 2px 2px 0 0;
	    border-color: #666;
	    border-style: solid;
	    position: relative;
	    right:20px;
	    top:9px;
	}
	.showlist{
	    padding:0 15px;
	}
	.title{
		padding-left: 0;
	}
	.down:after{
		transform: rotate(135deg) !important;
	}
	a{
		color:#383838;
	}
	.stateText{
		margin-right:10px;
	}
	.textlist{
		margin:0 !important;
	}
	.tips{
		margin-top:10px;
		padding:10px 0 0 0;
	}
	.tips:before{
		left:0;
	}
	.redsyellows span{

	}
</style>
<div class=" weui_cells_access">
    <a class="weui_cell showlist" href="javascript:;" onclick="showlist()">
        <div class="weui_cell_bd weui_cell_primary">
            <p class='weui_cells_title title'></p>
        </div>
        <div class="weui_cell_ft down">
        </div>
    </a>
</div>
<div class="weui_panel" style="margin: 0">
	<?php
		$this->widget('ext.wechat.ChildSelector',array(
		    'childObj'=>$this->childObj,
		    'myChildObjs'=>$this->myChildObjs,
		    'jumpUrl'=>'calendar',
		));
	?>
</div>
<div class=" line" id='monlist' v-cloak>
    <div v-if='calendarStatus==10'>
		<div v-for='(data,key,id) in mon' class="weui_panel">
			<div class="weui_panel_bd">
	            <div class="weui_media_box weui_media_text">
	                <h4 class="weui_media_title">{{key}} <?php echo Yii::t("support", "Year"); ?></h4>
	                <ul class="monBtn">
						<li v-for='(date,mon,index) in data' class="bg" >
							<span  @click='change(mon)' :class="{ 'btn' : mon == bg}" >{{parseInt(mon.substring(4,6))}} <i v-if='date!=0'>{{date}}</i></span>
						</li>
					</ul>
					<div class="clearfix"></div>
	            </div>
	        </div>
		</div>
	</div>
	<div v-else>
		<?php echo $this->renderPartial('../bind/_null', array('text' => Yii::t("global", "School calendar is not published, please contact campus."), 'title' => Yii::t("survey", "No Data"))); ?>
	</div>
	<div id="actionSheet_wrap">
	    <div class="weui_mask_transition" id="mask"></div>
	    <div class="weui_actionsheet" id="weui_actionsheet">
	        <div class="weui_actionsheet_menu">
	        	<div class="weui_actionsheet_cell" v-for='(data,key,id) in calendar'>
	        	<a :href ="data.url"  style="display: block;">{{data.title}}</a></div>
	        </div>
	        <div class="weui_actionsheet_action">
	            <div class="weui_actionsheet_cell" id="actionsheet_cancel"><?php echo Yii::t("global", "Cancel"); ?></div>
	        </div>
	    </div>
	</div>
</div>
<div id="detail">
<?php if( $calendarStatus == '10' ): ?>
<?php foreach ($monthArr as $month => $num): ?>
	<div id="<?php echo $month; ?>"  style="display: none;">
		<div class='weui_panel' style="margin-top: 10px">
			<div class="weui_article">
			<!-- 构造日历 -->
				<div class="month">
					<ul>
					    <li class="prev" onclick="prev('<?php echo $month ?>')"></li>
					    <li class="next" onclick="next('<?php echo $month ?>')" ></li>
					    <li style="text-align:center" class="datemon"></li>
					</ul>
					<ul>
						<li style="text-align: center;">
							<span class="org num">
								<?php if (isset($monthDayArr[$month])) {
									echo Yii::t("support", 'School Days :num', array(':num'=>$monthDayArr[$month]));
								}else{
									echo Yii::t("support", 'School Days :num', array(':num'=>'0'));
								} ?>
							</span>
						</li>	
				</div>
				<table class="table">
					<thead>
						<tr>
						    <th>S</th>
						    <th>M</th>
						    <th>T</th>
						    <th>W</th>
						    <th>T</th>
						    <th>F</th>
						    <th>S</th>
						</tr>
					</thead>
					<tbody>
						<?php 
							$first = date('w', strtotime($month . '01'));
							for ($i=0; $i < 6; $i++) {
								echo '<tr>';
								for ($j=0; $j < 7; $j++) {
									$day = $i*7 + $j-$first + 1;
									if ($i == 0 && $j < $first) {
										$day = '';
									}
									$cday = strtotime($month . '01') + $day * 86400 -1;
									if ($day && date('Ym', $cday) != $month ){
										continue;
									}
									$class = "";
									if (isset($data[$month][date('Ymd', $cday)])) {
										$span = array();
										foreach ($data[$month][date('Ymd', $cday)] as $item) {
											if($item['type'] == '10'){
												$span[0] = '<span class="red"></span>';
											}
											if($item['type'] == '20'){
												$span[1] = '<span class="yellow"></span>';
											}
											if($item['type'] == '30'){
												$span[2] = '<span class="green"></span>';
											}
										}
										$span = implode($span, '');
										echo '<td><a href="#holiday'.$month.'" style="display:block;color:#666" class='.$day.'>'.$day.'</a><strong class="strong">'.$span.'</strong> </td>';										
									}else{
										echo '<td>'.$day.'</td>';
									}
								}
								echo '</tr>';
							} 
						?>
					</tbody>
				</table>
				<div class="weui_cell tips">
		            <div class="weui_cell_bd weui_cell_primary">
		                <p class="textlist">
		                	<span class="tipred"></span> <span class="stateText"> <?php echo Yii::t("support", "Holiday"); ?></span>
				   			<span class="tipyellow"></span> <span class="stateText"> <?php echo Yii::t("support", "Event"); ?></span>
				   			<span class="tipgreen"></span> <span class="stateText"> <?php echo Yii::t("support", "Schoolday Make-up"); ?></span>
		                </p>
		            </div>
		        </div>
			</div>
		</div>
		<?php if (isset($data[$month])) : ?>
			<div class="weui_cells_title"><?php echo Yii::t("support", "Holidays & Events of this Month"); ?></div>
			<div class="weui_panel" style="margin-bottom: 10px" id='holiday<?php echo $month ?>'>
					<?php foreach ($data[$month] as $k2 => $item): ?>
						<?php foreach ($item as $k3 => $item3): ?>
				        <div class="weui_cell">
				            <div class="weui_cell_bd weui_cell_primary">
				            	 <?php if( $item3['type'] == '10' ): ?>
		                      		<p class="typeColor"><span class="listred colorpot"></span> <?php echo $item3['datestr'] ?></p>
			                    <?php elseif( $item3['type'] == '20' ): ?>
		                      		<p class="typeColor"><span class="listyellow colorpot"></span> <?php echo $item3['datestr'] ?></p>
			                    <?php else: ?>
			                        <p class="typeColor"><span class="tipgreen colorpot"></span> <?php echo $item3['datestr'] ?></p>
			                    <?php endif; ?>
				            </div>
				            <div class="weui_cell_ft"><?php echo $item3['title'] ?></div>
				        </div>
						<?php endforeach; ?>
					<?php endforeach; ?>
				</div>
		<?php endif; ?>
	</div>
<?php endforeach; ?>
<?php endif; ?>
</div>
<script>
function sortKey(array,key){
	return array.sort(function(a,b){
         var x = parseInt(a[key])
         var y =parseInt(b[key]);
         return ((x>y)?-1:(x<y)?1:0)
     })
}
var list=<?php echo json_encode($monthArr); ?> 
var monthDayArr=<?php echo json_encode($monthDayArr); ?>;
var calendarArr=<?php echo json_encode($calendarArr); ?>;
var calendarStatus='<?php echo $calendarStatus?>';
	var monlist = new Vue({
	        el: "#monlist",
	        data: {
	           monlist:list,
	           mon:'',
	           bg:'',
	           month:'',
	           calendarArr:calendarArr,
	           yearmon:'',
	           calendarStatus:calendarStatus,
	        }, 
	        created: function () {
	        	var date=[]
	        	var yearmon=[]
	        	for(key in this.monlist){
	        		yearmon.push(key)
	        		date.push(key.substring(0,4))
	        	}
	        	this.yearmon=yearmon
	        	var month = date.filter(function(element,index,self){
	                return self.indexOf(element) === index;
	             });
	        	this.month=month
	        	var list={}
        		for(i=0;i<month.length;i++){
        			var data={}
        			for(key in this.monlist){
	        			if(key.substring(0,4)==month[i]){
	        				data[key]=this.monlist[key]
	        			}
	        		}
	        		list[month[i]]=data
	        	}
	        	this.mon=list
	        	var date=new Date;
				var year=date.getFullYear(); 
				var month=date.getMonth()+1;
				month =(month<10 ? "0"+month:month); 
				var mydate = (year.toString()+month.toString());
				this.bg=mydate
				$('.datemon').html(mydate.substring(0,4)+'-'+mydate.substring(4,6))
				this.change(mydate)
				$('.title').html(this.month[0]+'－'+this.month[1]+'<?php echo Yii::t("support", "School Calendar"); ?>')
	        },
	        computed:{
                    calendar:function(){
                    	var data=[]
                    	for(key in this.calendarArr){
                    		this.calendarArr[key].id =key;
                    		data.push(this.calendarArr[key])
    					}
                         return sortKey(data,'id')
                     }
                 },
	        methods: {
	        	
	        	hideActionSheet(weuiActionsheet, mask) {
                    weuiActionsheet.removeClass('weui_actionsheet_toggle');
                    mask.removeClass('weui_fade_toggle');
                    mask.on('transitionend', function () {
                        mask.hide();
                    }).on('webkitTransitionEnd', function () {
                        mask.hide();
                    })
                },
	        	change(month) {
	        		var data='';
	        		for(var i=0;i<this.yearmon.length;i++){
	        			if(this.yearmon[i]==month){
	        				data=month
	        				this.bg=month
	        			}
		        	}
		        	if(data==''){
		        		data=this.yearmon[0]
		        		this.bg=this.yearmon[0]
		        	}
	        		$('.datemon').html(data.substring(0,4)+'-'+data.substring(4,6))
					$('#detail').children().hide();
					$('#'+data).show();
				}	
	        }
	    })
	function showlist(){
		var mask = $('#mask');
        var weuiActionsheet = $('#weui_actionsheet');
        weuiActionsheet.addClass('weui_actionsheet_toggle');
        mask.show()
        .focus()//加focus是为了触发一次页面的重排(reflow or layout thrashing),使mask的transition动画得以正常触发
        .addClass('weui_fade_toggle').one('click', function () {
            monlist.$options.methods.hideActionSheet(weuiActionsheet, mask);
        });
        mask.unbind('transitionend').unbind('webkitTransitionEnd');
        $('#actionsheet_cancel').one('click', function () {
          	monlist.$options.methods.hideActionSheet(weuiActionsheet, mask);
        });
	}
function prev(mon){
	var data=[]
	for(key in list){
		data.push(key)
	}
	if(data.indexOf(mon)==0){
		monlist.bg=data[data.length-1]
		monlist.change(data[data.length-1])
		$('.datemon').html(data[data.length-1].substring(0,4)+'-'+data[data.length-1].substring(4,6))
	}else{
		var mon=data[data.indexOf(mon)-1]
		monlist.bg=mon
		monlist.change(mon)
		$('.datemon').html(mon.substring(0,4)+'-'+mon.substring(4,6))
	}
}
function next(mon){
	var data=[]
	for(key in list){
		data.push(key)
	}
	if(data.indexOf(mon)==data.length-1){
		monlist.bg=data[0]
		monlist.change(data[0])
		$('.datemon').html(data[0].substring(0,4)+'-'+data[0].substring(4,6))
	}else{
		var mon=data[data.indexOf(mon)+1]
		monlist.bg=mon
		monlist.change(mon)
		$('.datemon').html(mon.substring(0,4)+'-'+mon.substring(4,6))
	}
}
</script>