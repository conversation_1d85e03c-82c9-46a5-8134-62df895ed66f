<?php

class StudentController extends TeachBasedController
{

    // 访问action的初级权限
    public $actionAccessAuths = array(
//        'index' => 'o_T_Access',
    );
    public $userMenu = array();

    public function beforeAction($action)
    {
        $actionId = strtolower($action->getId());
        foreach ($this->accessBranch as $key => $item) {
            if ($item == 'BJ_QFF' && !in_array($actionId,array('childlist'))) {
                unset($this->accessBranch[$key]);
            }
        }
        return parent::beforeAction($action);
    }
    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'teaching';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Teaching Tasks');

//        foreach ($this->accessBranch as $key => $item) {
//            if ($item == 'BJ_QFF') {
//                unset($this->accessBranch[$key]);
//            }
//        }

        //初始化选择校园页面
        $this->branchSelectParams['hideKG'] = true;
        if (Yii::app()->user->checkAccess('ivystaff_it') || Yii::app()->user->checkAccess('ivystaff_opschool')) {
            $category = 'allBehavior';
        } else {
            $category = 'myList';
        }
        $this->branchSelectParams['urlArray'] = array('//mteaching/student/index', 'category' => $category);
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl() . '/jui/css/base/jquery-ui.css');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/viewer/viewer.css');

        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/vue.global.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.js');

        // 七牛上传所需文件
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/plupload/plupload.full.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/qiniu.min.js');

        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/viewer/viewer.js');

        $this->userMenu = $this->getUserMenuList();

    }

    public function actionIndex()
    {
        $category = Yii::app()->request->getParam('category');
        $this->checkStaffAccess($category, $this->userMenu);
        
        $id_num  = Yii::app()->request->getParam('id_num', '');
        $res = CommonUtils::requestDsOnline('behavior/getBehaviorConfig', array('school_id' => $this->branchId));
        $showAdministrativeGroupsRes = CommonUtils::requestDsOnline('behavior/showAdministrativeGroups', array('school_id' => $this->branchId));
        $department = array();
        if ( Yii::app()->user->checkAccess('ivystaff_it') || Yii::app()->user->checkAccess('ivystaff_opschool') || $showAdministrativeGroupsRes) {
            if($this->branchId == 'BJ_SLT'){
                //三里屯只有小学
                $department = array('ES');
            }else{
                //北皋 中小学
                $depModel = DepPosLink::model()->findByPk($this->staff->profile->occupation_en);
                if ($depModel->department_id == 137) { // 中学部
                    $department = array('MS');
                } elseif ($depModel->department_id == 134) {
                    $department = array('ES');
                } else {
                    $department = array('ES', 'MS');
                }
            }
        }
        $this->render('index', array(
            'config' => $res['data'],
            'department' => $department,
            'id_num'=>$id_num
        ));
    }

    public function getMenuItems()
    {
        $menuItems = array();

        $allMenu = array(
            'allBehavior' => array(
                'label' => Yii::t('behavior', 'All Entries'),
                'url' => array("//mteaching/student/index", "category" => "allBehavior")
            ),
            'myList' => array(
                'label' => Yii::t('behavior', 'My Submission'),
                'url' => array("//mteaching/student/index", "category" => "myList")
            ),
            'adminGroups' => array(
                'label' => Yii::t('behavior', 'Administrative Groups'),
                'url' => array("//mteaching/student/index", "category" => "adminGroups","category2"=>"supportList"),
                'active' => Yii::app()->request->getParam('category2'),//点击二级菜单时选中
            ),
            'recordOnly' => array(
                'label' => Yii::t('behavior', 'All Entries (Record Only)'),
                'url' => array("//mteaching/student/index", "category" => "recordOnly"),
            )
        );

        foreach ($this->userMenu as $v) {
            if (isset($allMenu[$v])) {
                $menuItems[] = $allMenu[$v];
            }
        }

        return $menuItems;
    }

    public function checkStaffAccess($operation, $params) {
        if(!in_array($operation, $params)){
            if(Yii::app()->request->isAjaxRequest){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', 'Unauthorized operation, please contact IT Dept.');
                $this->showMessage();
                Yii::app()->end();
            }
            else{
                if (count($params) > 0) {
                    $redirectUrl = $this->createUrl('index', array('category' => $params[0]));
                    $this->redirect($redirectUrl);
                }
                $this->render('//denied/index');
                Yii::app()->end();
            }
        }
        return true;
    }

    public function getUserMenuList() {
        $school_id = $_GET['branchId'];
        $Menu = array();
        //中学组老师展示 'recordOnly' 只是记录行为单的菜单
        $depModel = DepPosLink::model()->findByPk($this->staff->profile->occupation_en);
        if($depModel->department_id == 137){
            $Menu = array('recordOnly');
        }
        if (Yii::app()->user->checkAccess('ivystaff_cd')) {
            if ($school_id == 'BJ_DS'){
                $Menu = array('recordOnly');
            }
            return array_merge(array('allBehavior', 'myList', 'adminGroups'),$Menu);
        }

        if (Yii::app()->user->checkAccess('ivystaff_teacher') || Yii::app()->user->checkAccess('ivystaff_opschool')) {
            if ($this->showGroup()) {
                if ($school_id == 'BJ_DS'){
                    $Menu = array('recordOnly');
                }
                //校长办公室人员 展示所有按钮
                return array_merge(array('allBehavior', 'myList', 'adminGroups'),$Menu);
            }elseif($this->showAll()){
                if ($school_id == 'BJ_DS'){
                    $Menu = array('recordOnly');
                }
                //支持计划 年级组长 校长办公室 展示全部提请单按钮
                return array_merge(array('allBehavior', 'myList'),$Menu);
            } else {
                //展示我的提请单按钮
                return array_merge(array('myList'),$Menu);
            }
        }

        return array();
    }

    //展示全部提请单【支持计划 年级组长 校长办公室可查看】
    public function showAll() {
        $res = CommonUtils::requestDsOnline('behavior/showAllEntries', array('school_id' => $this->branchId));
        return $res['data'];
    }


    //展示行为管理团队【校长办公室人员可以查看】
    public function showGroup() {
        $res = CommonUtils::requestDsOnline('behavior/showAdministrativeGroups', array('school_id' => $this->branchId));
        return $res['data'];
    }

    //我的提请单
    public function actionMyList()
    {
        $this->render('myList');
    }

    //需要处理的提请单
    public function actionHandleList()
    {
        $this->render('handleList');
    }

    //支持计划列表
    public function actionSupportList()
    {
        $this->render('supportList');
    }

    //校长办公室
    public function actionOffice()
    {
        $this->render('office');
    }

    public function actionSubject()
    {
        $this->render('subject');
    }

    //已处理
    public function actionGetProcessed()
    {
        $this->render('processed');
    }

    public function actionAllBehavior()
    {
        $this->render('allBehavior');
    }

    //只记录的提请单
    public function actionRecordOnly()
    {
        $this->render('recordOnly');
    }


    //我的提请单
    public function actionGetMyList()
    {
        $page = Yii::app()->request->getParam('page', 1);
        $order = Yii::app()->request->getParam('order', '');
        $orderProp = Yii::app()->request->getParam('orderProp', '');
        $child_name = Yii::app()->request->getParam('name', array());
        $id_num = Yii::app()->request->getParam('id_num', '');
        $requestUrl = 'behavior/getMyList';
        $this->remote($requestUrl, array(
            'page' => $page,
            'order' => $order,
            'orderProp' => $orderProp,
            'name' => $child_name,
            'id_num' => $id_num,
        ));

    }

    public function actionGetBehaviorNewId()
    {
        $requestUrl = 'behavior/getBehaviorNewId';
        $this->remote($requestUrl);
    }

    //保存提请单
    public function actionSaveBehaviorForm()
    {
        $id = Yii::app()->request->getParam('id');
        $data = Yii::app()->request->getParam('data', '');
        $requestUrl = 'behavior/saveBehaviorForm';
        $this->remote($requestUrl, array(
            'id' => $id,
            'data' => $data,
        ));
    }

    //删除提请单
    public function actionDelBehavior()
    {
        $id = Yii::app()->request->getParam('id', '');
        $requestUrl = 'behavior/delBehavior';
        $this->remote($requestUrl, array(
            'id' => $id,
        ));
    }

    public function actionAdminDelBehavior()
    {
        $id = Yii::app()->request->getParam('id', '');
        $requestUrl = 'behavior/adminDelBehavior';
        $this->remote($requestUrl, array(
            'id' => $id,
        ));
    }

    //ISP老师处理
    public function actionISPProcess()
    {
        $id = Yii::app()->request->getParam('id', '');
        $data = Yii::app()->request->getParam('data', '');
        $requestUrl = 'behavior/ISPProcess';
        $this->remote($requestUrl, array(
            'id' => $id,
            'data' => $data,
        ));
    }

    //年级组长处理
    public function actionGradeleadProcess()
    {
        $id = Yii::app()->request->getParam('id', '');
        $data = Yii::app()->request->getParam('data', '');
        $requestUrl = 'behavior/gradeleadProcess';
        $this->remote($requestUrl, array(
            'id' => $id,
            'data' => $data,
        ));
    }

    //校长办公室人员处理
    public function actionOfficeProcess()
    {
        $id = Yii::app()->request->getParam('id', '');
        $data = Yii::app()->request->getParam('data', '');
        $requestUrl = 'behavior/officeProcess';
        $this->remote($requestUrl, array(
            'id' => $id,
            'data' => $data,
        ));
    }

    public function actionSubjectLeadProcess()
    {
        $id = Yii::app()->request->getParam('id', '');
        $data = Yii::app()->request->getParam('data', '');
        $requestUrl = 'behavior/subjectLeadProcess';
        $this->remote($requestUrl, array(
            'id' => $id,
            'data' => $data,
        ));
    }

    //校长办公室人员追加评论
    public function actionOfficeProcessAddComment()
    {
        $id = Yii::app()->request->getParam('id', '');
        $comment = Yii::app()->request->getParam('comment', '');
        $requestUrl = 'behavior/addComment';
        $this->remote($requestUrl, array(
            'id' => $id,
            'comment' => $comment,
        ));
    }

    //按照名字搜索孩子
    public function actionStudentSearch()
    {
        $child_name = Yii::app()->request->getParam('child_name', '');
        $group = Yii::app()->request->getParam('group', '');
        $requestUrl = 'behavior/studentSearch';
        $this->remote($requestUrl, array(
            'child_name' => $child_name,
            'group'=>$group
        ));
    }

    //按照名字搜索老师
    public function actionTeacherSearch()
    {
        $teacher_name = Yii::app()->request->getParam('teacher_name', '');
        $group = Yii::app()->request->getParam('group', '');
        $requestUrl = 'behavior/teacherSearch';
        $this->remote($requestUrl, array(
            'teacher_name' => $teacher_name,
            'group'=>$group
        ));
    }

    //操作支持计划老师
    public function actionSetSupportTeacher()
    {
        $teacher_id = Yii::app()->request->getParam('teacher_id', '');
        $type = Yii::app()->request->getParam('type');
        $status = Yii::app()->request->getParam('status');
        $group = Yii::app()->request->getParam('group');
        $requestUrl = 'behavior/setSupportTeacher';
        $this->remote($requestUrl, array(
            'teacher_id' => $teacher_id,
            'type' => $type,
            'status' => $status,
            'group' => $group,
        ));
    }

    //获取支持计划信息
    public function actionGetSupport()
    {
        $group = Yii::app()->request->getParam('group', '');
        $requestUrl = 'behavior/getSupport';
        $this->remote($requestUrl, array(
            'group' => $group
        ));
    }

    //获取提请单详情
    public function actionGetBehaviorDetail()
    {
        $id = Yii::app()->request->getParam('id', '');
        $requestUrl = 'behavior/getBehaviorDetail';
        $this->remote($requestUrl, array(
            'id' => $id,
        ));
    }

    // 根据班级ID 获取孩子
    public function actionChildList()
    {
        $class_id = Yii::app()->request->getParam('classId', '');
        $requestUrl = 'behavior/classStudent';
        $this->remote($requestUrl, array(
            'class_id' => $class_id,
        ));
    }

    //设置支持计划
    public function actionSetLabel()
    {
        $child_id = Yii::app()->request->getParam('child_id', array());
        $position = Yii::app()->request->getParam('position', '');
        $num = Yii::app()->request->getParam('num', '');
        $requestUrl = 'student/setSupportLabel';
        $this->remote($requestUrl, array(
            'child_id' => $child_id,
            'position' => $position,
            'num' => $num
        ));
    }

    //校长办公室人员管理
    public function actionOfficeUser()
    {
        $office_user = Yii::app()->request->getParam('teacher_id', '');
        $type = Yii::app()->request->getParam('type');
        $group = Yii::app()->request->getParam('group');
        $requestUrl = 'behavior/officeUser';
        $this->remote($requestUrl, array(
            'office_user' => $office_user,
            'type' => $type, #1增加 2删除
            'group' => $group, #中学MS   小学ES
        ));
    }

    //获取校长办公室人员
    public function actionOfficeUserList()
    {
        $group = Yii::app()->request->getParam('group');
        $requestUrl = 'behavior/officeUserList';
        $this->remote($requestUrl, array(
            'group' => $group
        ));
    }



    public function actionGetHandleList()
    {
        $type = Yii::app()->request->getParam('type');
        $order = Yii::app()->request->getParam('order');
        $page = Yii::app()->request->getParam('page', 1);
        $orderProp = Yii::app()->request->getParam('orderProp');
        $requestUrl = 'behavior/getHandleList';
        $this->remote($requestUrl, array(
            'type' => $type,
            'page' => $page,
            'order' => $order,
            'orderProp' => $orderProp,
        ));
    }

    public function actionStatBehavior()
    {
        $requestUrl = 'behavior/statBehavior';
        $this->remote($requestUrl);
    }

    public function actionShowTab()
    {
        $requestUrl = 'behavior/showTab';
        $this->remote($requestUrl);
    }



    // 获取七牛上传的token
    public function actionGetQiniuToken()
    {
        $linkId = Yii::app()->request->getParam('linkId');
        if (!$linkId) {
            $this->addMessage("state", "fail");
            $this->addMessage('message', "参数错误");
            $this->showMessage();
        }
        $data = array(
            'linkType' => 'behavior',
            'linkId' => $linkId,
            'isPrivate' => 1,
        );
        $res = CommonUtils::requestDsOnline('getQiniuToken', $data);
        $this->addMessage("state", "success");
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    // 删除附件
    public function actionDeleteAttachment()
    {
        $attachmentId = Yii::app()->request->getParam('attachmentId');
        $res = CommonUtils::requestDsOnline('deleteFile/' . $attachmentId);
        $this->addMessage("state", "success");
        $this->addMessage('data', $res);
        $this->showMessage();
    }

    public function actionUpdateAttachmentName()
    {
        $attachmentId = Yii::app()->request->getParam('attachmentId');
        $attachmentName = Yii::app()->request->getParam('attachmentName');
        $data = array(
            'id' => $attachmentId,
            'title' => $attachmentName
        );
        $res = CommonUtils::requestDsOnline('updateFileTitle', $data);
        $this->addMessage("state", "success");
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    public function actionAfreshAffirm()
    {
        $id = Yii::app()->request->getParam('id');
        $data = Yii::app()->request->getParam('data', '');
        $requestUrl = 'behavior/afreshAffirm';
        $this->remote($requestUrl, array(
            'id' => $id,
            'data' => $data,
        ));
    }

    public function actionGetClassTeacherList()
    {
        $id = Yii::app()->request->getParam('class_id');
        $requestUrl = 'behavior/getClassTeacherList';
        $this->remote($requestUrl, array(
            'class_id' => $id,
        ));
    }

    public function actionSaveFollowup()
    {
        $id = Yii::app()->request->getParam('id');
        $data = Yii::app()->request->getParam('data');
        $requestUrl = 'behavior/saveFollowup';
        $this->remote($requestUrl, array(
            'id' => $id,
            'data' => $data,
        ));
    }

    public function actionSendBehaviorMsgToTeacher()
    {

        $id = Yii::app()->request->getParam('id');
        $teacher_ids = Yii::app()->request->getParam('teacher_ids');
        $requestUrl = 'behavior/sendBehaviorMsgToTeacher';
        $this->remote($requestUrl, array(
            'id' => $id,
            'teacher_ids' => $teacher_ids,
        ));
    }

    public function actionAffirmRecordList()
    {
        $child_id = Yii::app()->request->getParam('child_id');
        $requestUrl = 'behavior/affirmRecordList';
        $this->remote($requestUrl, array(
            'child_id' => $child_id,
        ));
    }

    public function actionSetOver()
    {
        $id = Yii::app()->request->getParam('id');
        $teacher_ids = Yii::app()->request->getParam('teacher_ids',array());
        $requestUrl = 'behavior/setOver';
        $this->remote($requestUrl, array(
            'id' => $id,
            'teacher_ids' => $teacher_ids,
        ));
    }

    public function actionAllBehaviorListIndex()
    {
        $grade_group = Yii::app()->request->getParam('grade_group');
        $start_year = Yii::app()->request->getParam('start_year');
        $requestUrl = 'behavior/allBehaviorListIndex';
        $this->remote($requestUrl, array(
            'grade_group' => $grade_group,
            'start_year' => $start_year,
        ));
    }

    public function actionAlBehaviorWarning()
    {
        $grade_group = Yii::app()->request->getParam('grade_group');
        $start_year = Yii::app()->request->getParam('start_year');
        $requestUrl = 'behavior/allBehaviorWarning';
        $this->remote($requestUrl, array(
            'grade_group' => $grade_group,
            'start_year' => $start_year,
        ));
    }

    public function actionAllNewBehaviorList()
    {
        $grade_group = Yii::app()->request->getParam('grade_group');
        $start_year = Yii::app()->request->getParam('start_year');
        $time = Yii::app()->request->getParam('time');
        $page = Yii::app()->request->getParam('page');
        $order = Yii::app()->request->getParam('order');
        $orderProp = Yii::app()->request->getParam('orderProp');
        $recordOnly = Yii::app()->request->getParam('recordOnly');
        $child_id = Yii::app()->request->getParam('child_id');
        $class_id = Yii::app()->request->getParam('class_id');
        $problem_behavior = Yii::app()->request->getParam('problem_behavior','');
        $requestUrl = 'behavior/allNewBehaviorList';
        $this->remote($requestUrl, array(
            'grade_group' => $grade_group,
            'start_year' => $start_year,
            'time' => $time,
            'page' => $page,
            'order' => $order,
            'orderProp' => $orderProp,
            'recordOnly' => $recordOnly,
            'child_id' => $child_id,
            'class_id' => $class_id,
            'problem_behavior' => $problem_behavior,
        ));
    }

    public function actionAllBehaviorList()
    {
        $grade_group = Yii::app()->request->getParam('grade_group');
        $start_year = Yii::app()->request->getParam('start_year');
        $type = Yii::app()->request->getParam('type');
        $page = Yii::app()->request->getParam('page');
        $order = Yii::app()->request->getParam('order');
        $orderProp = Yii::app()->request->getParam('orderProp');
        $child_id = Yii::app()->request->getParam('child_id');
        $class_id = Yii::app()->request->getParam('class_id');
        $problem_behavior = Yii::app()->request->getParam('problem_behavior');
        $requestUrl = 'behavior/allBehaviorList';
        $this->remote($requestUrl, array(
            'grade_group' => $grade_group,
            'start_year' => $start_year,
            'type' => $type,
            'page' => $page,
            'order' => $order,
            'orderProp' => $orderProp,
            'child_id' => $child_id,
            'class_id' => $class_id,
            'problem_behavior' => $problem_behavior,
        ));
    }

    public function actionAllBehaviorAffirmRecordList()
    {
        $child_id = Yii::app()->request->getParam('child_id');
        $start_year = Yii::app()->request->getParam('start_year',0);
        $requestUrl = 'behavior/allBehaviorAffirmRecordList';
        $this->remote($requestUrl, array(
            'child_id' => $child_id,
            'start_year' => $start_year,
        ));
    }

    public function actionAllBehaviorAffirmRecordList2()
    {
        $child_id = Yii::app()->request->getParam('child_id');
        $start_year = Yii::app()->request->getParam('start_year',0);
        $requestUrl = 'behavior/allBehaviorAffirmRecordList2';
        $this->remote($requestUrl, array(
            'child_id' => $child_id,
            'start_year' => $start_year,
        ));
    }

    public function actionWarningStudentMore()
    {
        $grade_group = Yii::app()->request->getParam('grade_group');
        $start_year = Yii::app()->request->getParam('start_year');
        $requestUrl = 'behavior/warningStudentMore';
        $this->remote($requestUrl, array(
            'grade_group' => $grade_group,
            'start_year' => $start_year,
        ));
    }

    public function actionWarningClassMore()
    {
        $grade_group = Yii::app()->request->getParam('grade_group');
        $start_year = Yii::app()->request->getParam('start_year');
        $class_id = Yii::app()->request->getParam('class_id');
        $requestUrl = 'behavior/warningClassMore';
        $this->remote($requestUrl, array(
            'grade_group' => $grade_group,
            'start_year' => $start_year,
            'class_id' => $class_id,
        ));

    }

    public function actionGetGradelead()
    {
        $group = Yii::app()->request->getParam('group');
        $requestUrl = 'behavior/getGradelead';
        $this->remote($requestUrl, array(
            'group' => $group
        ));
    }

    public function actionSaveGradelead()
    {
        $grade = Yii::app()->request->getParam('grade');
        $teacher_id = Yii::app()->request->getParam('teacher_id');
        $requestUrl = 'behavior/saveGradelead';
        $this->remote($requestUrl, array(
            'grade' => $grade,
            'teacher_id' => $teacher_id,
        ));
    }

    public function actionDelGradelead()
    {
        $grade = Yii::app()->request->getParam('grade');//年级 e1,e2...
        $teacher_id = Yii::app()->request->getParam('teacher_id');
        $requestUrl = 'behavior/delGradelead';
        $this->remote($requestUrl, array(
            'grade' => $grade,
            'teacher_id' => $teacher_id,
        ));
    }


    public function actionGetSubjectLead()
    {
        $group = Yii::app()->request->getParam('group');
        $requestUrl = 'behavior/getSubjectLead';
        $this->remote($requestUrl, array(
            'group' => $group
        ));
    }

    public function actionSaveSubjectLead()
    {
        $subject = Yii::app()->request->getParam('subject');
        $groupType = Yii::app()->request->getParam('groupType');
        $teacher_id = Yii::app()->request->getParam('teacher_id');
        $requestUrl = 'behavior/saveSubjectLead';
        $this->remote($requestUrl, array(
            'subject' => $subject,
            'teacher_id' => $teacher_id,
            'groupType' => $groupType,
        ));
    }

    public function actionDelSubjectLead()
    {
        $subject = Yii::app()->request->getParam('subject');
        $teacher_id = Yii::app()->request->getParam('teacher_id');
        $groupType = Yii::app()->request->getParam('groupType');
        $requestUrl = 'behavior/delSubjectLead';
        $this->remote($requestUrl, array(
            'subject' => $subject,
            'teacher_id' => $teacher_id,
            'groupType' => $groupType,
        ));
    }

    public function actionFollowupNode()
    {
        $requestUrl = 'behavior/followupNode';
        $group = Yii::app()->request->getParam('group','');
        $child_id = Yii::app()->request->getParam('child_id',array());
        $this->remote($requestUrl, array(
            'group' => $group,
            'child_id' => $child_id,
        ));
    }

    public function actionGetRecordOnlyNumberByClass()
    {
        $requestUrl = 'behavior/getRecordOnlyNumberByClass';
        $start_year = Yii::app()->request->getParam('start_year','');
        $grade_group = Yii::app()->request->getParam('grade_group','');
        $this->remote($requestUrl, array(
            'start_year' => $start_year,
            'grade_group' => $grade_group,
        ));
    }

    public function actionGetRecordOnlyNumberByChild()
    {
        $requestUrl = 'behavior/getRecordOnlyNumberByChild';
        $start_year = Yii::app()->request->getParam('start_year','');
        $grade_group = Yii::app()->request->getParam('grade_group','');
        $this->remote($requestUrl, array(
            'start_year' => $start_year,
            'grade_group' => $grade_group,
        ));
    }

    public function actionGetChildNumberByStatus()
    {
        $grade_group = Yii::app()->request->getParam('grade_group');
        $start_year = Yii::app()->request->getParam('start_year');
        $type = Yii::app()->request->getParam('type');
        $problem_behavior = Yii::app()->request->getParam('problem_behavior');
        $requestUrl = 'behavior/getChildNumberByStatus';
        $this->remote($requestUrl, array(
            'grade_group' => $grade_group,
            'start_year' => $start_year,
            'type' => $type,
            'problem_behavior' => $problem_behavior,
        ));
    }

    public function actionGetClassNumberByStatus()
    {
        $grade_group = Yii::app()->request->getParam('grade_group');
        $start_year = Yii::app()->request->getParam('start_year');
        $type = Yii::app()->request->getParam('type');
        $problem_behavior = Yii::app()->request->getParam('problem_behavior');
        $requestUrl = 'behavior/getClassNumberByStatus';
        $this->remote($requestUrl, array(
            'grade_group' => $grade_group,
            'start_year' => $start_year,
            'type' => $type,
            'problem_behavior' => $problem_behavior,
        ));
    }

    public function actionGetNodeTeacher()
    {
        $requestUrl = 'behavior/getNodeTeacher';
        $node = Yii::app()->request->getParam('node','');
        $child_id = Yii::app()->request->getParam('child_id',array());
        $subject = Yii::app()->request->getParam('subject','');
        $this->remote($requestUrl, array(
            'node' => $node,
            'child_id' => $child_id,
            'subject' => $subject,
        ));
    }


    public function actionClassList()
    {
        $grade = Yii::app()->request->getParam('group','');
        $yid = $this->branchObj->schcalendar;
        $data = $this->getClassList($yid,$grade);
        $this->addMessage('state', 'success');
        $this->addMessage('data', $data);
        $this->showMessage();
    }
    // 获取用户管理的班级列表
    public function getManageClassList($yid,$grade='')
    {
        $classList = array();
        if (Yii::app()->user->checkAccess('t_A_CampusAdm')) {
            $classList = $this->getClassList($yid,$grade);
        } else {
            Yii::import("common.models.classTeacher.ClassTeacher");
            $crit = new CDbCriteria();
            $crit->compare("t.schoolid", $this->branchId);
            $crit->compare("t.yid", $yid);
            $crit->compare("t.teacherid",  Yii::app()->user->getId());
            $crit->with = "classInfo";
            $models = ClassTeacher::model()->findAll($crit);
            foreach ($models as $item) {
                $classList[] = array(
                    'classid' => $item->classInfo->classid,
                    'title' => $item->classInfo->title,
                    'class_type' => $item->classInfo->classtype,
                );
            }
        }
        return $classList;
    }
    // 获取班级列表
    public function getClassList($yid,$grade='')
    {
        if(!empty($grade)){
            if($grade == 'ES'){
                $classType = array('mk','e1','e2','e3','e4','e5');
            }elseif ($grade == 'MS'){
                $classType = array('e6','e7','e8','e9','e10','e11','e12');
            }else{
                $classType = array();
            }
        }else{
            $classType = array('mk','e1','e2','e3','e4','e5','e6','e7','e8','e9','e10','e11','e12');
        }
        $classList = array();
        $models = IvyClass::getClassList($this->branchId, $yid);
        foreach ($models as $class) {
            if(in_array($class->classtype,$classType)){
                $classList[] = array(
                    'classid' => $class->classid,
                    'title' => $class->title,
                    'class_type' => $class->classtype,
                );
            }

        }
        return $classList;
    }

    public function remote($requestUrl, $requestData = array())
    {
        $requestData['school_id'] = $this->branchId;
        $res = CommonUtils::requestDsOnline2($requestUrl, $requestData);
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        }
        $this->showMessage();
    }


}