
<div class="nav" style="float: right;padding-top: 20px;">
<?php
		$this->widget('zii.widgets.CMenu',array(
			'items'=> $this->catList,
			'activeCssClass'=>'current',
			'activateParents' => true,
			'htmlOptions'=>array(
				'class'=>'cc sf-menu',
			)
		));
?>
</div>
<div class="cc"></div>
<div class="h_a">待审核</div>
<?php
$this->widget('ext.ivyCGridView.IvyCGridView', array(
	'id'=>'checking_for_me',
	'dataProvider'=>$data['dataProvider'],
    'htmlOptions'=>array(
        'class'=>'table_full',
    ),
	'template'=>"<div class='user_group' style='border:none;'>{items}</div><div class='table_full'>{summary}</div><div class='table_full'>{pager}</div>",
    'colgroups'=>array(
        array(
            "colwidth"=>array(80,150,90, 120, 120),
        )
    ),
	'columns'=>array(
		'leave_id'=>array(
			'name'=>'leave_id',
			'value'=>'UserLeave::getStartYear($data->userLeave->startyear)',
		),
		'type'=>array(
			'name'=>'type',
            'value'=>'UserLeave::getLeaveType($data->type)',
        ),
		'startdate'=>array(
            'name'=>'startdate',
            'value'=>'OA::formatDateTime($data->startdate)',
        ),
        'enddate'=>array(
            'name'=>'startdate',
            'value'=>'OA::formatDateTime($data->enddate)',
        ),
		'hours'=>array(
            'name'=>'hours',
            'value'=>'UserLeave::getLeaveFormat($data->hours)',
        ),
		'applydate'=>array(
            'name'=>'applydate',
            'value'=>'OA::formatDateTime($data->applydate)',
        ),
        array(
            'class'=>'CButtonColumn',
            'template' => '{delete}',
            'deleteButtonUrl'=>'Yii::app()->createUrl("/user/hr/delete", array("id" =>$data->id,"t"=>leave))',
        ),
	)
)); ?>
<div class="h_a">请假历史</div>
<?php
$this->widget('ext.ivyCGridView.IvyCGridView', array(
	'id'=>'checked_for_me',
	'dataProvider'=>$data['providerdForMe'],
    'htmlOptions'=>array(
        'class'=>'table_full',
    ),
	'template'=>"<div class='user_group' style='border:none;'>{items}</div><div class='table_full'>{summary}</div><div class='table_full'>{pager}</div>",
    'colgroups'=>array(
        array(
            "colwidth"=>array(100,150,90, 120, 120),
        )
    ),
	'columns'=>array(
		'leave_id'=>array(
			'name'=>'leave_id',
			'value'=>'UserLeave::getStartYear($data->userLeave->startyear)',
		),
		'type'=>array(
			'name'=>'type',
            'value'=>'UserLeave::getLeaveType($data->type)',
        ),
		'startdate'=>array(
            'name'=>'startdate',
            'value'=>'OA::formatDateTime($data->startdate)',
        ),
        'enddate'=>array(
            'name'=>'startdate',
            'value'=>'OA::formatDateTime($data->enddate)',
        ),
		'hours'=>array(
            'name'=>'hours',
            'value'=>'UserLeave::getLeaveFormat($data->hours)',
        ),
		'applydate'=>array(
            'name'=>'applydate',
            'value'=>'OA::formatDateTime($data->applydate)',
        )
	)
)); ?>

<div id="user-leave-info">
<h5>个人当前假期统计（2013.09.01 - 2014.09.30）</h5>
<table class="table-b">
    <tr>
        <td>
            <div class="user-leave-header">
                <div class="fl u-type">
                    <div class="tac">年假</div>
                </div>
                <div class="fl u-count">
                    <div>【剩余】：10天</div>
                    <div>【正在进行】：1天</div>
                    <div>【总共】：<?php echo $data['leaveList'][UserLeave::TYPE_ANNUAL_LEAVE]['hours'];?></div>
                </div>
                <div class="c"></div>
            </div>
            <div class="user-leave-foot tac">
                <a class="btn btn_submit">申请</a>
            </div>
        </td>
        <td>
            <div class="user-leave-header">
                <div class="fl u-type">
                    <div class="tac">病假</div>
                </div>
                <div class="fl u-count">
                    <div>【剩余】：10天</div>
                    <div>【正在进行】：1天</div>
                    <div>【总共】：20天</div>
                </div>
                <div class="c"></div>
            </div>
            <div class="user-leave-foot tac">
                 <a class="btn btn_submit">申请</a>
            </div>
        </td>
        <td>
            <div class="user-leave-header">
                <div class="fl u-type">
                    <div class="tac">事假</div>
                </div>
                <div class="fl u-count">
                    <div>【剩余】：10天</div>
                    <div>【正在进行】：1天</div>
                    <div>【总共】：20天</div>
                </div>
                <div class="c"></div>
            </div>
            <div class="user-leave-foot tac">
                <a class="btn btn_submit">申请</a>
            </div>
        </td>
        <td>
            <div class="user-leave-header">
                <div class="fl u-type">
                    <div class="tac">调休</div>
                </div>
                <div class="fl u-count">
                    <div>【剩余】：10天</div>
                    <div>【正在进行】：1天</div>
                    <div>【总共】：20天</div>
                </div>
                <div class="c"></div></div>
            <div class="user-leave-foot tac">
                <a class="btn btn_submit">申请</a>
            </div>
        </td>
    </tr>
   
    <tr>
        <td>
            <div class="user-leave-header">
                <div class="fl u-type">
                    <div class="tac">产假</div>
                </div>
                <div class="fl u-count">
                    <div>【剩余】：10天</div>
                    <div>【正在进行】：1天</div>
                    <div>【总共】：20天</div>
                </div>
                <div class="c"></div></div>
            </div>
            <div class="user-leave-foot tac">
                <a class="btn btn_submit">申请</a>
            </div>
        </td>
        <td>
            <div class="user-leave-header">
                <div class="fl u-type">
                    <div class="tac">丧假</div>
                </div>
                <div class="fl u-count">
                    <div>【剩余】：10天</div>
                    <div>【正在进行】：1天</div>
                    <div>【总共】：20天</div>
                </div>
                <div class="c"></div></div>
            </div>
            <div class="user-leave-foot tac">
                <a class="btn btn_submit">申请</a>
            </div>
        </td>
        <td>
            <div class="user-leave-header">
                <div class="fl u-type">
                    <div class="tac">婚假</div>
                </div>
                <div class="fl u-count">
                    <div>【剩余】：10天</div>
                    <div>【正在进行】：1天</div>
                    <div>【总共】：20天</div>
                </div>
                <div class="c"></div></div>
            </div>
            <div class="user-leave-foot tac">
                <a class="btn btn_submit">申请</a>
            </div>
        </td>
        <td>
            <div class="user-leave-header">
                <div class="fl u-type">
                    <div class="tac">其它</div>
                </div>
                <div class="fl u-count">
                    <div>【剩余】：10天</div>
                    <div>【正在进行】：1天</div>
                    <div>【总共】：20天</div>
                </div>
                <div class="c"></div></div>
            </div>
            <div class="user-leave-foot tac">
                <a class="btn btn_submit">申请</a>
            </div>
        </td>
    </tr>

</table>
</div>