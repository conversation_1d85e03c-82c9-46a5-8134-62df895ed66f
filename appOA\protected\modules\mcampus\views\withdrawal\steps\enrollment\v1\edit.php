<div>
    <div class='flex align-items  mb24 mt10'>
        <span class='font14 color3'>处理人：</span>
        <div class='flex1 flex align-items lib_class_handle'>
        </div>
    </div>
    <div class='reject redColor'>
        <div class='font14'><span class='el-icon-warning mr5 font16'></span>已驳回</div>
        <div class='mt8'>
        驳回理由：<span class='rejectMemo'></span>
        </div>
    </div>
    <form class="J_ajaxForm formStu" action="<?php echo $this->createUrl("saveNode");?>" method="post" onsubmit="return check(this)">
        <p class='mt5 pt4'>
            <label class="radio-inline font14 color6 lableHeight">
                <input type="radio"  name="balance" value="1"> 有学籍
            </label>
            <label class="radio-inline font14 color6 lableHeight ml20">
                <input type="radio"  name="balance" value="0"> 无学籍
            </label>
        </p>
        <p class='xueji hide'>
            <span class='font14 color3'> 学籍状态：</span>
            <span class='xuejiStatus'>
            </span>
        </p>
        <textarea class="form-control" rows="3" placeholder="<?php echo Yii::t('withdrawal','Comment');?>" name="remark" id='remark'  class='font14'></textarea>
        <p class='mb10 mt20'>
           <span class='font14 color3 mr16 ' >展示在家长端确认页面</span> 
            <label class="switch">
                <input type="checkbox" name="show_prompt" id='show_prompt'  />
                <span class="slider"></span>
            </label>
            <div class='prompt hide'>
                <textarea class="form-control" rows="2" name='prompt' id='prompt' placeholder="<?php echo Yii::t('leave','Input');?>" class='font14'></textarea>
            </div>
        </p>
        
        <input type="hidden" name="type" id="type"  value='enrollment'>
        <input type="hidden" name="applicationId" id="applicationId">
        <input type="hidden" name="node" id="node">
        <div class='modal-footer borderTopNone'>
            <label class="checkbox-inline">
                <input type="checkbox" name="status" value="1"> <?php echo Yii::t('withdrawal', 'Mark this step as completed'); ?>
            </label>
            <button type="submit" class="btn btn-primary ml24 submitSend"><?php echo Yii::t('global','Submit');?></button>
            <button type="button" class="btn btn-primary J_ajax_submit_btn ml24"><?php echo Yii::t('global','Submit');?></button>
        </div>
    </form>
</div>

<script>
   
     lib()
     $(document).ready(function() {
        $('input[name="amount[]"]').on('input', function() {
            var value = $(this).val();
            if (!/^[1-9]\d*|0$/.test(value)) {
                $(this).val('');
            }
        });
    });
    function lib(){
        let forms=childDetails.forms.find(item2 =>item2.key === 'enrollment')
        for(var key in forms.owner_info){
            if (key !== '2' && key !== '5') {
            $('.lib_class_handle').append('<div class="flex align-items mr16"><img src='+forms.owner_info[key].photoUrl+' alt="" class="img28"/><div class="font14 color3 ml8">'+forms.owner_info[key].name+'</div></div>')
            }
        }
        $('#type').val(forms.key)
        if(forms.confirm_status==2){
            $('.reject').show()
            $('.rejectMemo').html(forms.reject_memo)
        }else{
            $('.reject').hide()
            $('.rejectMemo').html(forms.reject_memo)
        }
        if(forms.status=='1'){
            $('input[type="checkbox"][name="status"][value='+forms.status+']').prop('checked', true);
        }
        if(forms.data.balance){
            $('input[type="radio"][name="balance"][value='+forms.data.balance+']').prop('checked', true);
        }
        $('.xuejiStatus').html('')
        for(let i=0;i<container.indexDataList.enrollmentStateList.length;i++){
            $('.xuejiStatus').append('<label class="radio-inline font14 color6 lableHeight mr20"><input type="radio" name="enrollment_state" value='+container.indexDataList.enrollmentStateList[i].key+'> '+container.indexDataList.enrollmentStateList[i].value+'</label>')
        }
        if(forms.data.balance=='1'){
            $('input[type="radio"][name="enrollment_state"][value='+forms.data.enrollment_state+']').prop('checked', true);
            $('.xueji').removeClass('hide')
        }
        if(forms.data.remark!=''){
            $('#remark').val(forms.data.remark);
        }
        if(forms.data.prompt!=''){
            $('#prompt').val(forms.data.prompt);
        }
        
        if(forms.data.show_prompt=='1'){
            $('.prompt').removeClass('hide')
            $('input[name="show_prompt"]').prop('checked', true);
        }else{
            $('input[name="show_prompt"]').prop('checked', false);
            $('.prompt').addClass('hide')
        }
        
     }
    $('input[name="show_prompt"]').click(function () {
        if ($(this).is(':checked')) {
            $('.prompt').removeClass('hide')
        }
        else {
            $('.prompt').addClass('hide')
        }
    })
    $('input[name="balance"]').click(function () {
        if ($(this).val() == '1') {
            // $('.xuejiStatus').html('')
            // for(let i=0;i<container.indexDataList.enrollmentStateList.length;i++){
            //     $('.xuejiStatus').append('<label class="radio-inline font14 color6 lableHeight mr20"><input type="radio"  name="enrollment_state" value='+container.indexDataList.enrollmentStateList[i].key+'> '+container.indexDataList.enrollmentStateList[i].value+'</label>')
            // }
            $('.xueji').removeClass('hide')
        }
        else {
            $('.xueji').addClass('hide')
        }
    })
    $('.submitSend').show()
    $('.J_ajax_submit_btn').hide()
    function check(_this){
        let balanceId=$('input[name="balance"]:checked').val()
        $('#J_fail_info').remove();
        var button = $(_this).find('button[type="submit"]');
        var flag = true, msg=[];
        if($('input[name="show_prompt"]').is(':checked')){
            $('input[name="show_prompt"]').val('1')
            flag=true
        }else{
            $('input[name="show_prompt"]').val('0')
            $('#prompt').val('');
            flag=true
        }
        if(!balanceId){
            flag = false;
            msg.push('<?php echo Yii::t("teaching","请选择学籍");?>');
        }
        if(balanceId=='1'){
            let enrollment_state=$('input[name="enrollment_state"]:checked').val()
            if(!enrollment_state){
                flag = false;
                msg.push('<?php echo Yii::t("teaching","请选择学籍状态");?>');
            }
        }
        if(flag){
            $('.submitSend').hide()
            $('.J_ajax_submit_btn').show()
            $('.formStu .J_ajax_submit_btn').click();
            return false;
        }
        else{
            $( '<span id="J_fail_info" class="text-warning"><i class="glyphicon glyphicon-remove text-warning"></i> ' + msg.join('、') + '！</span>' ).appendTo(button.parent()).fadeIn( 'fast' );
            return false;
        }
    }
</script>
<style> 
/* The switch - the box surrounding the slider */
.switch {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 20px;
    border-radius: 50px;
}
 
/* Hide default HTML checkbox */
.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}
 
/* The slider */
.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: .4s;
  transition: .4s;
  border-radius:60px
}
 
.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left:2px;
    bottom: 2px;
    background-color: white;
    -webkit-transition: .4s;
    transition: .4s;
    border-radius: 50%;
}
 
input:checked + .slider {
  background-color: #428bca ;
}
 
input:checked + .slider:before {
  -webkit-transform: translateX(19px);
  -ms-transform: translateX(19px);
  transform: translateX(19px);
}
 
/* Rounded sliders */
.slider.round {
  border-radius: 34px;
}
 
.slider.round:before {
  border-radius: 50%;
}
input::placeholder {
  color: #ccc; /* 灰色的placeholder文本 */
}
.xueji{
    padding:8px 10px;
    background:#F7F7F8;
    border-radius:4px;
    margin-bottom:16px
}
</style>