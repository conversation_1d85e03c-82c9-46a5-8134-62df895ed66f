<?php 
Yii::import('common.models.invoice.*');
$invoiceType = array('tuition');

Yii::import('common.models.invoice.*');
$basicModel = ChildProfileBasic::model();
$criteria = new CDbCriteria;
$criteria->with = 'paid_tuition';
$criteria->compare('t.status', '>'.ChildProfileBasic::STATS_GRADUATED);
$criteria->compare('t.schoolid', $this->branchId);
$criteria->group = 't.updated_timestamp desc,t.childid desc';
//$criteria->order = 't.updated_timestamp desc,t.childid desc';

$dataProvider = new CActiveDataProvider('ChildProfileBasic', array(
    'criteria' => $criteria,
    'pagination' => array(
        'pageSize' => 20,
    ),
));
// var_dump($dataProvider);die();
?>
<div class="col-md-10">
    <div class="row mb15">
        <div class="col-md-12">
            <h1 class=" flex align-items font24 fontBold mt20">
                <span class="dropdown text-primary">
                    <a data-toggle=dropdown href="#"><span class="glyphicon glyphicon-list glyphiconList"></span></a>
                    <ul class="dropdown-menu">
                        <li>
                            <a onclick="exportChildInfo(this)" href="javascript:void(0)">
                                <span class="glyphicon glyphicon-export"></span> <?php echo Yii::t('site', 'Export Name List');?>
                            </a>
                        </li>
                    </ul>
                </span>
                <span class='ml12'><?php echo Yii::t('site','Students withdraw in the middle of the school year');?> </span> 
                <span class="label label-default ml8" id="total-number"></span>
            </h1>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <?php 
                $this->widget('ext.ivyCGridView.BsCGridView', array(
                    'id'=>'child-list',
                    'dataProvider'=>$dataProvider,
                    'template'=>"{items}{pager}",
                    'colgroups'=>array(
                        array(
                            "colwidth"=>array(80,150,90, 120, 120, 90,90),
                        )
                    ),
                    'columns'=>array(
                        'childid'=>array(
                            'name'=>'childid',
                            'header'=>Yii::t("labels","ID"),
                        ),
                        'name'=>array(
                            'type'=>'raw',
                            'header' => Yii::t('child', 'Name'),
                            'value'=>'CHtml::link($data->getChildName(), array("//child/index/index","childid"=>$data->childid), array("target"=>"_blank"))',
                        ),
                        'gender'=>array(
                            'name'=>'gender',
                            'value'=>'OA::renderChildGender($data->gender)',
                        ),
                        'birthday'=>array(
                            'name'=>'birthday_search',
                        ),
                        'credit'=>array(
                            'name'=>'credit',
                        ),
                        'regdate'=>array(
                            'name'=>'created_timestamp',
                            'header'=>Yii::t('labels', 'Created Time'),
                            'value'=>'OA::formatDateTime($data->created_timestamp)',
                        ),
                        'quitdate'=>array(
                            'name'=>'updated_timestamp',
                            'header'=>'退学操作日期',
                            'value'=>'OA::formatDateTime($data->updated_timestamp)',
                        )
                    )
                ));
            ?>
        </div>
    </div>
</div>

<script>
    // 导出孩子列表
    exportChildInfo = function (obj) {
        var url = "<?php echo $this->createUrl('//mcampus/student/exportChildInfo', array('category' => $category)); ?>";
        $.ajax({
          url: url,
          type: 'POST',
          // dataType: 'json',
          data: {},
          success: function (res) {
            res = eval("("+res+")");
            if (res.state == 'success') {
                var data = res.data.items;
                const filename = res.data.title;
                const ws_name = "SheetJS";

                const worksheet = XLSX.utils.aoa_to_sheet(data);
                const workbook = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(workbook, worksheet, ws_name);
                // generate Blob
                const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                const blob = new Blob([wbout], {type: 'application/octet-stream'});
                // save file
                let link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = filename;
                link.click();
                setTimeout(function() {
                    // 延时释放掉obj
                    URL.revokeObjectURL(link.href);
                    link.remove();
                }, 500);
            }
          },
          error: function(XMLHttpRequest, textStatus, errorThrown){  
            // alert(XMLHttpRequest.readyState + XMLHttpRequest.status + XMLHttpRequest.responseText);
          },
        }).done(function ( data, textStatus, jqXHR) {
            // console.log(jqXHR.status);
        });
    }
</script>
