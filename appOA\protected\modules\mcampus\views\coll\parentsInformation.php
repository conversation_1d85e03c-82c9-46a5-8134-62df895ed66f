<div id='homeAddress'>
    <div class="progress">
        <div class="progress-bar progress-bar-success" :style="'width:'+ parsefloat(filled/total) +'%;'">
            <span v-if='filled!=0' :title="filled+'/'+total">{{parsefloat(filled/total)}}% ({{filled}}/{{total}})</span>
            <span v-else>0% ({{filled}}/{{total}})</span>
        </div>
    </div>
    <div class='pull-left' id="nameSearchComp"></div>
    <p class='pull-right'>
        <button type="button" class="btn btn-default btn-sm"  v-if='type=="filled"' :disabled='btnCon' @click='batchPass("modal")'>批量通过</button>
        <button type="button" class="btn btn-default btn-sm ml10"  @click='exportTab()'>导出表格</button>
    </p>
    <div class='clearfix'></div>
    <div class='relative'>
        <div class='loading' v-show='loading'>
            <span></span>
        </div>
        <ul class="nav nav-tabs" role="tablist">
            <li role="presentation" class="active"><a href="#home" aria-controls="home" role="tab" data-toggle="tab"  @click='getInfo("filled")'>已填写 <span class="badge">{{filled}}</span></a></li>
            <li role="presentation"><a href="#unstart" aria-controls="unstart" role="tab" data-toggle="tab" @click='getInfo("unfilled")'>未填写 <span class="badge badge-error">{{unfilled}}</span></a></li>
        </ul>
        <div class="tab-content">
            <div role="tabpanel" class="tab-pane active" id="home">
                <table class="table table-bordered mt20" id='filled' v-show='list.length!=0'>
                    <thead>
                    <tr>
                        <th width='30' class="nosort">
                            <input type="checkbox" value="option1"  v-model='all'  @change='checkAll($event)'>
                        </th>
                        <th width='150'><?php echo Yii::t('global', 'Name') ?></th>
                        <th width='200'><?php echo Yii::t('labels', 'Class') ?></th>
                        <th width='150'><?php echo Yii::t('labels', 'Date of Birth') ?></th>
                        <th width='150'><?php echo Yii::t('labels', 'Status') ?></th>
                        <th width='150'>提交时间</th>
                        <th width='80' class="nosort"><?php echo Yii::t('global', 'Action') ?></th>
                    </tr>
                    </thead>
                    <tbody v-if='isShow'>
                    <tr v-for='(item,index) in list'>
                        <th>
                            <input type="checkbox" :value="item.childid" v-model='child_ids' :disabled='item.step_status==4?true:false'  @change='childCheck($event)'>
                        </th>
                        <td >
                            <a href="javascript:;" @click="overalls(item.childid)">
                                {{item.name}}
                            </a>
                        </td>
                        <td > {{item.class_name}}</td>
                        <td >{{item.birthday}}</td>
                        <td >
                            <i class='glyphicon glyphicon-remove-sign' v-if='item.step_status==3' style='color:#D5514E'></i>
                            <i class='glyphicon glyphicon-ok-sign'  v-if='item.step_status==4' style='color:#5cb85c'></i>
                            <i class='glyphicon glyphicon-registration-mark'  v-if='item.step_status==1' style='color:#f0ad4e'></i>
                            <i class='glyphicon glyphicon-question-sign'  v-if='item.step_status==0' style='color:#f0ad4e'></i>
                            {{statusList[item.step_status]}}</td>

                        <td >{{item.step_data.submitted_at}} </td>
                        <td >
                            <a href='javascript:;' @click='auditList(item)'><?php echo Yii::t('global', 'View Detail') ?></a>
                        </td>
                    </tr>
                    </tbody>
                </table>

                <div class="mt15" v-show='list.length==0'>
                    <div class="alert alert-warning" role="alert"><?php echo Yii::t('ptc', 'No Data') ?></div>
                </div>
            </div>
            <!-- 未填写的数据 -->
            <div role="tabpanel" class="tab-pane " id="unstart">
                <table class="table table-bordered mt20" id='unfilled' v-show='unList.length!=0'>
                    <thead>
                    <tr>
                        <th><?php echo Yii::t('global', 'Name') ?></th>
                        <th><?php echo Yii::t('labels', 'Class') ?></th>
                        <th><?php echo Yii::t('labels', 'Date of Birth') ?></th>
                        <th><?php echo Yii::t('labels', 'Status') ?></th>
                    </tr>
                    </thead>
                    <tbody  v-if='isShow'>
                    <tr v-for='(item,index) in unList'>
                        <td >
                            <a href="javascript:;"  @click="overalls(item.childid)">
                                {{item.name}}
                            </a>
                        </td>
                        <td > {{item.class_name}}</td>
                        <td >{{item.birthday}}</td>
                        <td >{{statusList[item.step_status]}}</td>
                    </tr>
                    </tbody>
                </table>

                <div class="mt15" v-show='unList.length==0'>
                    <div class="alert alert-warning" role="alert"><?php echo Yii::t('ptc', 'No Data') ?></div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" tabindex="-1" role="dialog" id='auditDialog' data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title"><?php echo Yii::t('global', 'View Detail') ?></h4>
                </div>
                <div class="modal-body" v-if='Object.keys(rejectionList).length != 0'>
                    <div class="media col-md-12 ">
                        <div class="media-left pull-left media-middle">
                            <a href="javascript:void(0)">
                                <img :src="rejectionList.avatar" data-holder-rendered="true" class="media-object img-circle avatarImage">
                            </a>
                        </div>
                        <div class="media-body pt10 media-middle">
                            <h4 class="media-heading font14">{{rejectionList.name}}</h4>
                            <div class="text-muted">{{rejectionList.class_name}}</div>
                        </div>
                    </div>
                    <div class='clearfix'></div>
                    <div class='col-md-12 mt20 mb10'>
                        <p><?php echo Yii::t('reg', '母亲') ?>：</p>
                        <div class="col-md-12 mt10 mb20">
                            <p>姓：<span>{{rejectionList.step_data.minfo.last_name}}</span></p>
                            <p>中间名：<span>{{rejectionList.step_data.minfo.middle_name}}</span></p>
                            <p>名：<span>{{rejectionList.step_data.minfo.first_name}}</span></p>
                            <p>英文名/昵称：<span>{{rejectionList.step_data.minfo.english_name}}</span></p>
                            <p>手机号：<span>{{rejectionList.step_data.minfo.mobile}}</span></p>
                            <p>电子邮箱：<span>{{rejectionList.step_data.minfo.email}}</span></p>
                        </div>
                    </div>

                    <div class='col-md-12 mt10 mb10'>
                        <p><?php echo Yii::t('reg', '父亲') ?>：</p>
                        <div class="col-md-12 mt10 mb10">
                            <p>姓：<span>{{rejectionList.step_data.finfo.last_name}}</span></p>
                            <p>中间名：<span>{{rejectionList.step_data.finfo.middle_name}}</span></p>
                            <p>名：<span>{{rejectionList.step_data.finfo.first_name}}</span></p>
                            <p>英文名/昵称：<span>{{rejectionList.step_data.finfo.english_name}}</span></p>
                            <p>手机号：<span>{{rejectionList.step_data.finfo.mobile}}</span></p>
                            <p>电子邮箱：<span>{{rejectionList.step_data.finfo.email}}</span></p>
                        </div>
                    </div>
                    <div class='col-md-12 mt10 mb10'>
                        <p><?php echo Yii::t('reg', '第一联系人') ?>：</p>
                        <div v-if="rejectionList.step_data.primary_contact.type == 3" class="col-md-12 mt10 mb10">
                            <p>姓名：<span>{{rejectionList.step_data.primary_contact.name}}</span></p>
                            <p>关系：<span>{{rejectionList.step_data.primary_contact.relation}}</span></p>
                            <p>手机号：<span>{{rejectionList.step_data.primary_contact.mobile}}</span></p>
                            <p>邮箱：<span>{{rejectionList.step_data.primary_contact.email}}</span></p>
                        </div>
                        <div v-else class="col-md-12 mt10 mb10">
                            <p v-if="rejectionList.step_data.primary_contact.type == 1">母亲</p>
                            <p v-else>父亲</p>
                        </div>
                    </div>
                    <!-- <div class='col-md-12 mt20 mb10'>
                        <p><?php echo Yii::t('reg', '紧急联系人') ?>：</p>
                        <div class="col-md-12 mt10 mb10" v-for="(item,index) in rejectionList.step_data.einfo" :key="index">
                            <p>姓名： <span>{{item.name}}</span></p>
                            <p>手机号：<span>{{item.mobile}}</span></p>
                            <p>邮箱：<span>{{item.email}}</span></p>
                        </div>
                    </div> -->

                    <div class='col-md-12 mt10 mb10' v-if='rejectionList.sign_url!=""'>
                        <p><?php echo Yii::t('event', 'Signature') ?>：</p>
                        <img :src="rejectionList.sign_url" alt="" class='signImgDetails'>
                    </div>
                    <div class='clearfix'></div>
                    <hr>
                    <div class='col-md-12 col-sm-12 font14'>
                        <div class='col-md-6 col-sm-6 borderRight scroll-box' style='max-height:250px;overflow-y:auto'>
                            <p><strong>当前审核状态</strong></p>

                            <div>
                                {{statusList[rejectionList.step_status]}}
                                <button type="button" class="btn btn-primary  btn-xs pull-right" @click='resetStatus()'  v-if='rejectionList.step_status==3 || rejectionList.step_status==4'><?php echo Yii::t('global', 'Reset') ?></button>
                            </div>
                            <p class='mt10'><strong>审核记录</strong></p>
                            <div>
                                <div v-for='(list,index) in rejectionList.audit_log' class='ml10' style='border-left:1px dashed #ccc'>
                                    <p style='margin-left:-7px;background: #fff;' >
                                <span v-if='list.status==4'>
                                <span class='glyphicon glyphicon-ok-sign greenIcon'></span><span> <?php echo Yii::t('global', 'Confirmed') ?></span>
                                </span>
                                        <span v-if='list.status==3'>
                                <span class='glyphicon glyphicon-info-sign redIcon'></span> <span> <?php echo Yii::t('reg', 'Rejected') ?></span>
                                </span>
                                        <span v-if='list.status==0'>
                                <span class='glyphicon glyphicon-question-sign yellowIcon'></span> <span> <?php echo Yii::t('global', 'Reset') ?></span>
                                </span>
                                    </p>
                                    <p class='ml10' style='background:#F7F7F8;line-height:22px'  v-if='list.comment!="" && list.comment!=null'><?php echo Yii::t('reg', '备注：') ?>{{list.comment}}</p>
                                    <p class='pl10 font12'>{{list.date}} {{list.user}}</p>
                                </div>
                            </div>
                        </div>
                        <div class='col-md-6 col-sm-6' >
                            <p><strong>审核</strong></p>
                            <div class='p10 grey'>
                                <label class="radio ml20">
                                    <input type="radio" id="inlineradio1" v-model='audit_status' value="4"> <?php echo Yii::t('labels', 'Yes') ?>
                                </label>
                                <label class="radio ml20">
                                    <input type="radio" id="inlineradio2" v-model='audit_status' value="3"> 未通过
                                </label>
                                <textarea class="form-control" rows="3" placeholder='请输入备注' v-model='comment'></textarea>
                                <div v-if='audit_status==3 || type_status==2'>
                                    <p class='mt10'>修改建议将直接显示在家长端，请准确措辞。</p>   
                                    <div class="checkbox">
                                        <label>
                                            <input type="checkbox"  v-model='send_wechat'>将审核结果发送至家长微信
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class='mt20'>
                                <button type="button"  class="btn btn-primary pull-right" :disabled='btnCon' @click='saveInfo()'><?php echo Yii::t("newDS", "确认");?></button>
                                <button type="button" class="btn btn-default  pull-right mr10" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                            </div>
                        </div>
                    </div>
                    <div class='clearfix'></div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="stuDetails" tabindex="-1" role="dialog" >
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" ><?php echo Yii::t('attends', '整体情况') ?> <span id="remarks_t"></span></h4>
                </div>
                <div class="modal-body color6" v-if='childData.child'>
                    <p><strong class='font14 color3'>学生资料</strong></p>
                    <div class='flex'>
                        <div style='width:80px'><img :src="childData.child.avatar" class='stuImg'></div>
                        <div class='flex1'>
                            <p class='mt10'><strong class='font14 color3 studentName'>{{childData.child.name}}</strong> </p>
                            <p class='mb20'>{{childData.child.class_name}}</p>
                            <div  v-if='childData.parents.finfo!=null'>
                                <div class='col-md-4'>
                                    <strong class='color3'><?php echo Yii::t('global', 'Father') ?>：</strong>
                                    <span class='glyphicon glyphicon-user'></span> {{childData.parents.finfo.name}}
                                </div>
                                <div class='col-md-4'>
                                    <span class='glyphicon glyphicon-phone'></span> {{childData.parents.finfo.mphone}}
                                </div>
                                <div class='col-md-4'>
                                    <span class='glyphicon glyphicon-envelope'></span> {{childData.parents.finfo.email}}
                                </div>
                            </div>
                            <div class='clearfix'></div>
                            <div class='mt10' v-if='childData.parents.minfo!=null'>
                                <div class='col-md-4'>
                                    <strong class='color3'><?php echo Yii::t('global', 'Mother') ?>：</strong>
                                    <span class='glyphicon glyphicon-user'></span> {{childData.parents.minfo.name}}
                                </div>
                                <div class='col-md-4'>
                                    <span class='glyphicon glyphicon-phone'></span> {{childData.parents.minfo.mphone}}
                                </div>
                                <div class='col-md-4'>
                                    <span class='glyphicon glyphicon-envelope'></span> {{childData.parents.minfo.email}}
                                </div>
                            </div>
                        </div>
                    </div>
                    <ul class="nav nav-wizard pb20 mt20">
                        <li v-for='(list,index) in childData.step_status'>
                            <a href="javascript::" class='color6' :data-tab="list.step_id" :data-status="list.status" :data-name="childData.child.name"><span class="number">{{index+1}}</span>{{list.title}}</a>
                            <p class='mt10 color6' >
                                <span :class="list.status==4?'greenStep':list.status==3?'redStep':list.status==1 || list.status==2?'orgStep':''">{{ statusList[list.status]}}</span></p>
                        </li>
                    </ul>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel') ?></button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="tipsModal" tabindex="-1" role="dialog" >
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" ><?php echo Yii::t('attends', '批量通过') ?> <span id="remarks_t"></span></h4>
                </div>
                <div class="modal-body color6" >
                    <div class='color3 font14'>确认批量通过审核吗？</div> 
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel') ?></button>
                    <button type="button" class="btn btn-primary" :disabled='btnTips' @click='batchPass()'><?php echo Yii::t('global', '确定') ?></button>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    var type_status = '<?php echo $type_status?>';

    var step_id = '<?php echo $step_id?>';
    var coll_id = '<?php echo $coll_id?>';
    var type = '<?php echo $type?>';
    var  registrations = new Vue({
        el: "#homeAddress",
        data: {
            type_status:type_status,
            list:[],
            unfilled:0,
            coll_id:coll_id,
            filled:0,
            total:0,
            type:type,
            logList:[],
            statusList:[],
            audit_status:'',
            comment:'',
            child_id:'',
            childData:{},
            child_ids:[],
            rejectionList:{},
            unList:{},
            btnCon:false,
            send_wechat:true,
            isShow:true,
            loading:false,
            all:false,
            btnTips:false,
        },
        created: function() {
            this.getInfo(this.type)
            eventBus.$on('listChanged', list => {
                if(sessionStorage.getItem('tabType')  === 'filled'){
                    this.list = list;
                }else{
                    this.unList = list
                }
            });
        },
        watch: {
        },
        methods: {
            overalls(id){
                let that=this
                $.ajax({
                    url:'<?php echo $this->createUrlReg('getStudentOverall'); ?>',
                    type: 'get',
                    dataType:'json',
                    data:{
                        child_id:id,
                        coll_id:coll_id,
                    },
                    success:function(data){
                        if(data.state=="success"){
                            that.childData=data.data
                            $('#stuDetails').modal('show')
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            parsefloat(num) {
                return Number(num * 100).toFixed(2);
            },
            getInfo(type){
                let that = this
                this.type=type
                this.isShow=false
                $.ajax({
                    url:'<?php echo $this->createUrlReg('stepTemplateInfo'); ?>',
                    type: 'get',
                    dataType:'json',
                    data:{
                        coll_id:coll_id,
                        step_id:step_id,
                        type:type
                    },
                    success:function(data){
                        if(data.state=="success"){
                            if(type=='unfilled'){
                                that.unList = data.data.list
                            }else{
                                that.list = data.data.list
                            }
                            that.unfilled = data.data.unfilled
                            that.filled = data.data.filled
                            that.total = data.data.count
                            that.statusList=data.data.statusList
                            that.logList=data.data.logList
                            that.isShow=true
                            sessionStorage.setItem('tabType', type);
                            sessionStorage.setItem('listUpdated', JSON.stringify(data.data.list));
                            that.$forceUpdate();
                            that.$nextTick(()=>{
                                that.DataTable(data)
                            })
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            DataTable(data){
                let that=this
                that.$nextTick(()=>{
                    $('#stuDetails').modal('hide');
                    $('#nameSearchComp').html(data.data.nameSearchComp)
                    if(that.type=='filled'){
                        var table = $('#filled').DataTable({
                            aaSorting: [5, 'desc'], // 默认排序
                            paging: false,
                            info: false,
                            searching: false,
                            "destroy": true,
                            columnDefs: [ {
                                "targets": 'nosort',
                                "orderable": false
                            } ],
                        });
                    }else{
                        var table = $('#unfilled').DataTable({
                            aaSorting: [1, 'desc'], // 默认排序
                            paging: false,
                            info: false,
                            "destroy": true,
                            searching: false,
                        });
                    }
                })

            },
            auditList(list){
                this.rejectionList=list
                this.child_id=list.childid
                this.audit_status=''
                this.comment=''
                $('#auditDialog').modal('show')

            },
            exportTab() {
                if(this.type=='filled'){
                    const filename ='家庭身份证件已填写.xlsx';
                    const ws_name = "SheetJS";
                    var exportDatas = [];
                    for(var i=0;i<this.list.length;i++){
                        var contact=''
                        if(this.list[i].step_data.primary_contact.type==3){
                            contact='姓名：'+this.list[i].step_data.primary_contact.name+';关系：'+this.list[i].step_data.primary_contact.relation+';手机号：'+this.list[i].step_data.primary_contact.mobile+';邮箱：'+this.list[i].step_data.primary_contact.email
                        }else if(this.list[i].step_data.primary_contact.type==1){
                            contact='母亲'
                        }else{
                            contact='父亲'
                        }
                        exportDatas.push(
                            {
                                'ID':this.list[i].childid,
                                '<?php echo Yii::t('global', 'Name') ?>':this.list[i].name,
                                "<?php echo Yii::t('labels', 'Class') ?>":this.list[i].className,
                                "<?php echo Yii::t('labels', 'Date of Birth') ?>":this.list[i].birthday,
                                "母亲姓":this.list[i].step_data.minfo.last_name,
                                "母亲中间名":this.list[i].step_data.minfo.middle_name,
                                "母亲名":this.list[i].step_data.minfo.first_name,
                                "母亲英文名":this.list[i].step_data.minfo.english_name,
                                "母亲手机号":this.list[i].step_data.minfo.mobile,
                                "母亲邮箱":this.list[i].step_data.minfo.email,
                                "父亲姓":this.list[i].step_data.finfo.last_name,
                                "父亲中间名":this.list[i].step_data.finfo.middle_name,
                                "父亲名":this.list[i].step_data.finfo.first_name,
                                "父亲英文名":this.list[i].step_data.finfo.english_name,
                                "父亲手机号":this.list[i].step_data.finfo.mobile,
                                "父亲邮箱":this.list[i].step_data.finfo.email,
                                '第一联系人':contact,
                                "<?php echo Yii::t('labels', 'Status') ?>":this.statusList[this.list[i].step_status],
                                "提交时间":this.list[i].step_data.submitted_at,
                            });
                    }
                    var wb=XLSX.utils.json_to_sheet(exportDatas,{
                        origin:'A1',// 从A1开始增加内容
                        header: ['ID','<?php echo Yii::t('global', 'Name') ?>', '<?php echo Yii::t('labels', 'Class') ?>','<?php echo Yii::t('labels', 'Date of Birth') ?>','母亲姓','母亲中间名','母亲名','母亲英文名','母亲手机号','母亲邮箱','父亲姓','父亲中间名','父亲名','父亲英文名','父亲手机号','父亲邮箱','第一联系人','<?php echo Yii::t('labels', 'Status') ?>','提交时间'],
                    });
                    const workbook = XLSX.utils.book_new();
                    XLSX.utils.book_append_sheet(workbook, wb, ws_name);
                    const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                    const blob = new Blob([wbout], {type: 'application/octet-stream'});
                    let link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    link.download = filename;
                    link.click();
                    setTimeout(function() {
                        // 延时释放掉obj
                        URL.revokeObjectURL(link.href);
                        link.remove();
                    }, 500);
                }else{
                    var title='家庭身份证件未填写.xlsx'
                    const ws_name = "SheetJS";
                    var exportDatas = [];
                    for(var i=0;i<this.unList.length;i++){
                        exportDatas.push(
                            {
                            'ID':this.unList[i].childid,
                            '<?php echo Yii::t('global', 'Name') ?>':this.unList[i].name,
                            "<?php echo Yii::t('labels', 'Class') ?>":this.unList[i].className,
                            "<?php echo Yii::t('labels', 'Date of Birth') ?>":this.unList[i].birthday,
                            "<?php echo Yii::t('labels', 'Status') ?>":this.statusList[this.unList[i].step_status],
                            });
                    }
                    var wb=XLSX.utils.json_to_sheet(exportDatas,{
                        origin:'A1',// 从A1开始增加内容
                        header: ['ID','<?php echo Yii::t('global', 'Name') ?>', '<?php echo Yii::t('labels', 'Class') ?>','<?php echo Yii::t('labels', 'Date of Birth') ?>','<?php echo Yii::t('labels', 'Status') ?>'],
                    });
                    const workbook = XLSX.utils.book_new();
                    XLSX.utils.book_append_sheet(workbook, wb, ws_name);
                    const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                    const blob = new Blob([wbout], {type: 'application/octet-stream'});
                    let link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    link.download = title;
                    link.click();
                    setTimeout(function() {
                        // 延时释放掉obj
                        URL.revokeObjectURL(link.href);
                        link.remove();
                    }, 500);
                }
            },
            checkAll(e){
                this.child_ids=[]
                if(e.target.checked){
                    for(var i=0;i<this.list.length;i++){
                        if(this.list[i].step_status!=4){
                            this.child_ids.push(this.list[i].childid)
                        }
                    }
                    this.all=true
                }else{
                    this.all=false
                }
            },
            childCheck(e){
                let checkListLen=this.list.filter((item)=>{return item.step_status!=4});
                if(e.target.checked){
                    if(this.child_ids.length==checkListLen.length){
                        this.all=true
                    }else{
                        this.all=false
                    }
                }else{
                    this.all=false
                }
            },
            batchPass(modal){
                let that = this
                if(this.child_ids.length==0){
                    resultTip({error: 'warning', msg: '请选择学生'});
                    return
                }
                if(modal){
                    $('#tipsModal').modal('show')
                    return
                }
                this.loading=true
                this.btnCon=true
                this.btnTips=true
                $.ajax({
                    url:'<?php echo $this->createUrlReg('batchPass'); ?>',
                    type: 'post',
                    dataType:'json',
                    data:{
                        child_ids:this.child_ids,
                        step_id:step_id,
                        coll_id:this.coll_id
                    },
                    success:function(data){
                        if(data.state=="success"){
                            resultTip({
								msg: data.state
							});
                            that.getInfo(that.type)
                            $('#auditDialog').modal('hide')
                            $('#tipsModal').modal('hide')
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                        that.btnCon=false
                        that.loading=false
                        that.btnTips=false
                    },
                    error:function(data){
                        that.btnCon=false
                        that.loading=false
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            resetStatus(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrlReg("stepAudit") ?>',
                    type: "get",
                    dataType: 'json',
                    data:{
                        child_id:this.child_id,
                        step_id:step_id,
                        audit_status:0,
                        coll_id:this.coll_id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg: data.state
                            });
                            that.getInfo(that.type)
                            $('#auditDialog').modal('hide')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                    },
                })
            },
            saveInfo(){
                let that = this
                if(this.audit_status==''){
                    resultTip({
                        error: 'warning',
                        msg: '请选择审核状态'
                    });
                    return
                }
                that.btnCon=true
                var dataList={
                    child_id:this.child_id,
                    step_id:step_id,
                    audit_status:this.audit_status, 
                    coll_id:this.coll_id,
                    comment: this.comment,
                }
                if(this.audit_status==3 || this.type_status==2){
                   var dataList=Object.assign({send_wechat:that.send_wechat?1:0},dataList)
                }
                $.ajax({
                    url:'<?php echo $this->createUrlReg('stepAudit'); ?>',
                    type: 'get',
                    dataType:'json',
                    data:dataList,
                    success:function(data){
                        if(data.state=="success"){
                            resultTip({
                                msg: data.state
                            });
                            that.getInfo(that.type)
                            $('#auditDialog').modal('hide')
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                        that.btnCon=false
                    },
                    error:function(data){
                        that.btnCon=false
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            bigImg(list){
                var opacityBottom = '<div class="opacityBottom" style = "display:none"><img class="bigImg" src="' + list + '"></div>';
                $(document.body).append(opacityBottom);
                this.toBigImg();//变大函数
            },
            toBigImg() {
                $(".opacityBottom").addClass("opacityBottom");//添加遮罩层
                $(".opacityBottom").show();
                $("html,body").addClass("none-scroll");//下层不可滑动
                $(".bigImg").addClass("bigImg");//添加图片样式
                $(".opacityBottom").click(function () {//点击关闭
                    $("html,body").removeClass("none-scroll");
                    $(".opacityBottom").remove();
                });
            }
        }
    })
</script>
<style>
    a[data-status="0"] {
        cursor: default;
        text-decoration: none;
    }
</style>
