<?php

/**
 * This is the model class for table "ivy_hr_department".
 *
 * The followings are the available columns in table 'ivy_hr_department':
 * @property integer $id
 * @property string $cn_name
 * @property string $en_name
 * @property integer $num
 * @property string $memo
 * @property integer $weight
 * @property integer $category
 * @property integer $status
 * @property integer $userid
 * @property integer $timestamp
 */
class HrDepartment extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return HrDepartment the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_hr_department';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('cn_name, en_name, userid, timestamp, category', 'required'),
			array('num, weight, status, userid, timestamp', 'numerical', 'integerOnly'=>true),
			array('cn_name, en_name', 'length', 'max'=>255),
			array('memo', 'length', 'max'=>500),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, cn_name, en_name, num, memo, weight, status, userid, timestamp', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'title' => array(self::HAS_MANY, 'DepPosLink', 'department_id', 'with'=>array('position'=>array('condition'=>'deleted_at=0', 'order'=>'position.weight'))),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'cn_name' => 'Cn Name',
			'en_name' => 'En Name',
			'num' => 'Num',
			'memo' => 'Memo',
			'weight' => 'Weight',
			'status' => 'Status',
			'userid' => 'Userid',
			'timestamp' => 'Timestamp',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('cn_name',$this->cn_name,true);
		$criteria->compare('en_name',$this->en_name,true);
		$criteria->compare('num',$this->num);
		$criteria->compare('memo',$this->memo,true);
		$criteria->compare('weight',$this->weight);
		$criteria->compare('status',$this->status);
		$criteria->compare('userid',$this->userid);
		$criteria->compare('timestamp',$this->timestamp);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
	
     /**
	 * 取部门名称
	 * @return ArrayIterator;
	 * <AUTHOR>
	 * @copyright IvyGroup
	 */
	public function getNameInfo($status = 0 )
	{
		$nameList = array();
		$criteria = new CDbCriteria;
		$criteria->select = 'id,cn_name,en_name';
		if ($status == 0 || $status == 1)
		{
			$criteria->compare('status', $status);
		}
		$nameInfo = $this->findAll($criteria);
		if (!empty($nameInfo))
		{
			foreach ($nameInfo as $v)
			{
				$nameList[$v->id] = (Yii::app()->language == "zh_cn") ?  $v->cn_name : $v->en_name;
			}
		}
		return $nameList;
	}
	
	public function getName()
	{
		return (Yii::app()->language == "zh_cn") ?  $this->cn_name : $this->en_name;
	}
}