<?php

class DefaultController extends AppController
{
	public function actionIndex()
	{
		Yii::import('common.models.invoice.InvoiceTransaction');
		Yii::import('common.models.points.PointsOrder');

		$this->point = InvoiceTransaction::model()->getPointsCredit($this->getChildId(), 1370016000); // 2013-6-1
		$this->render('index');
	}

	public function actionProfile()
	{
		echo 'Profile';
	}

	public function actionCredit()
	{
		echo 'Credit';
	}

	public function actionPoint()
	{
		echo 'Point';
	}

	public function actionJournal()
	{
		echo 'Journal';
	}

	public function actionLunch()
	{
		echo 'Lunch';
	}

	public function actionMates()
	{
		echo 'Mates';
	}

	public function actionCancelLunch()
	{
		echo 'CancelLunch';
	}

	public function actionUnpaid()
	{
		echo 'Unpaid';
	}

	public function actionInvoice()
	{
		echo 'Invoice';
	}

	public function actionSettings()
	{
		echo 'Settings';
	}
}