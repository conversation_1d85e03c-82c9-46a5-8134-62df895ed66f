
		<div class="mb10">
			<button class="btn btn-primary" onclick="updates(0)"><span class="glyphicon glyphicon-plus"></span> 增加孩子受伤报告</button>
		</div>
<!-- Modal -->
 <div class="panel-default">
			<div class="panel-body">
				<?php
				$this->widget('ext.ivyCGridView.BsCGridView', array(
					'id'=>'injured_report',
					'afterAjaxUpdate' => 'js:function(){head.Util.ajaxDel()}',
					'dataProvider' => $dataProvider,
					'columns' => array(
						array(
							'name' => 'schoolid',
							'value' => '$data->class_name->title',
						),
						array(
							'name' => 'childid',
							'value' => '$data->child_name->getChildName()',
						),
						'injuryreport',
						'parentback',
						array(
							'name' => '操作',
							'value' => array($this,'showBtn'),
						),
					),
				)); ?>
			</div>
</div>

<!-- 模态框 -->
<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
  <div class="modal-dialog" role="document">
    <div class="modal-content" id="cancelinvoices">
      
    </div>
  </div>
</div>
<script>



//显示编辑界面
function updates(id) {
		var yid = '<?php echo $yid ?>'
		var weeknumber = '<?php echo $weeknumber ?>'
		var app = 1
		$.ajax({
            type: "POST",
			url: "<?php echo $this->createUrl('updata'); ?>",
			data: {"id":id,"found":yid,"app":app,"weeknumber":weeknumber}, 
			dataType: "json",
			success: function(data){
				if(data){
					$('#cancelinvoices').html(data.data)
					$('#myModal').modal('show');
					head.Util.ajaxForm( $('#myModal') );
				}
			}
		});
}

function daletes(id) {
		$.ajax({
            type: "POST",
			url: "<?php echo $this->createUrl('detete'); ?>",
			data: {"id":id}, 
			dataType: "json",
			success: function(data){
				if(data){
					$('#cancelinvoices').html(data.data)
					$('#myModal').modal('show');
					head.Util.ajaxForm( $('#myModal') );
				}
			}
		});
}



$(function() {
	$("#task").change(function(){
		getclasschild()
	});
});

function getclasschild(){
		$.get('<?php echo $this->createUrl('getShops')?>', {
			'classid': $('#task').val()
		}, function(htmlcontent) {
			$('#CrInjuriesReport_childid').html(htmlcontent)
			.find('option[value=<?php echo $model->childid?>]')
			.selected()
			
		});
}
<?php if(!$model->isNewRecord): ?>
		getclasschild()
<?php endif;?>

</script>



