<?php

/**
 * This is the model class for table "ivy_survey_return".
 *
 * The followings are the available columns in table 'ivy_survey_return':
 * @property integer $id
 * @property integer $tid
 * @property string $school_id
 * @property integer $yid
 * @property string $class_type
 * @property integer $start_timestamp
 * @property integer $end_timestamp
 * @property integer $status
 * @property integer $created_at
 * @property integer $created_by
 * @property string $updated_at
 * @property integer $updated_by
 */
class SurveyReturn extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_survey_return';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('tid, school_id, yid, class_type, start_timestamp, end_timestamp, status, created_at, created_by, updated_at, updated_by', 'required'),
			array('tid,yid, start_timestamp, end_timestamp, status, created_at, created_by, updated_by', 'numerical', 'integerOnly' => true),
			array('school_id, class_type', 'length', 'max' => 255),
			array('updated_at', 'length', 'max' => 11),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, tid, school_id, yid, class_type, start_timestamp, end_timestamp, status, created_at, created_by, updated_at, updated_by', 'safe', 'on' => 'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array();
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'tid' => 'Tid',
			'school_id' => 'School',
			'yid' => 'Yid',
			'class_type' => 'Class Type',
			'start_timestamp' => 'Start Timestamp',
			'end_timestamp' => 'End Timestamp',
			'status' => 'Status',
			'created_at' => 'Created At',
			'created_by' => 'Created By',
			'updated_at' => 'Updated At',
			'updated_by' => 'Updated By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria = new CDbCriteria;

		$criteria->compare('id', $this->id);
		$criteria->compare('tid', $this->tid);
		$criteria->compare('school_id', $this->school_id, true);
		$criteria->compare('yid', $this->yid);
		$criteria->compare('class_type', $this->class_type, true);
		$criteria->compare('start_timestamp', $this->start_timestamp);
		$criteria->compare('end_timestamp', $this->end_timestamp);
		$criteria->compare('status', $this->status);
		$criteria->compare('created_at', $this->created_at);
		$criteria->compare('created_by', $this->created_by);
		$criteria->compare('updated_at', $this->updated_at, true);
		$criteria->compare('updated_by', $this->updated_by);

		return new CActiveDataProvider($this, array(
			'criteria' => $criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return SurveyReturn the static model class
	 */
	public static function model($className = __CLASS__)
	{
		return parent::model($className);
	}

    /**
     * 根据qId获取跳转题目
     * @param $id
     * @param $surveyId
     * @return null
     */
    private static function getJumpToQuestionById($id, $surveyId)
    {
        $criteria = new CDbCriteria();
        $criteria->compare('survey_id', $surveyId);
        $criteria->compare('id', $id);
        $criteria->compare('jump_id', '>0');
        $criteria->compare('status', 1);
        $qSelf = SurveyReturnQuestions::model()->find($criteria);

        if ($qSelf) {
            $criteria = new CDbCriteria();
            $criteria->compare('survey_id', $surveyId);
            $criteria->compare('id', $qSelf->jump_id);
            $criteria->compare('type', array('question', 'content'));
            $criteria->compare('status', 1);
            return SurveyReturnQuestions::model()->find($criteria);
        }

        return null;
    }

	/**
	 * 根据 pid 与 sid 获取下一道题
	 *
	 * @param integer $sid survey ID
	 * @param integer $qid question ID
	 * @param integer $oid option ID
	 * @return void
	 */
	public static function getNext($sid, $qid, $state,$oid = false)
	{
		$model = null;
		// 先根据选项查是否有下一题
		if ($oid) {

            // 选项是否有跳转
            $model = self::getJumpToQuestionById($oid, $sid);

            if (!$model) {
                $criteria = new CDbCriteria();
                $criteria->compare('survey_id', $sid);
                $criteria->compare('type', array('question', 'content'));
                $criteria->compare('pid', $oid);
                $criteria->compare('status', 1);
                $model = SurveyReturnQuestions::model()->find($criteria);
            }
		}
		// 根据题目查是否有下一题
		if (!$model) {

            // 题目是否有跳转
            if ($qid) {
                $model = self::getJumpToQuestionById($qid, $sid);
            }

            if (!$model) {
                $criteria = new CDbCriteria();
                $criteria->compare('survey_id', $sid);
                $criteria->compare('type', array('question', 'content'));
    			$criteria->compare('pid', $qid);
                $criteria->compare('status', 1);
                $criteria->order = 'jump_id DESC';
                $model = SurveyReturnQuestions::model()->find($criteria);
            }
		}
		$data = array('question' => array(), 'options' => array());
		if ($model) {
			// 判断该题是不是首题
			$order = 'middle';
			if ($qid == 0) {
				$order = 'first';
			}
			$pids = array($model->id);
			if ($model->type == 'content') {
				$showType = Yii::t('survey', 'Notice');
			} else {
				if ($model->is_multiple) {
					$showType = Yii::t('survey', 'Select all that applies');
				} else {
					$showType = Yii::t('survey', 'Please select');
				}
			}
			$data['question'] = array(
				'preid' => $qid,
				'qid' => $model->id,
				'pid' => $model->pid,
				'type' => $model->type,
				'show_type' => $showType,
				'title' => CommonUtils::autoLang($model->cn_title, $model->en_title),
				'is_required' => $model->is_required,
				'is_signature' => $model->is_signature,
				'is_multiple' => $model->is_multiple,
				'content_ext' => CommonUtils::autoLang($model->content_ext, $model->content_ext_en),
				'is_memo' => $model->is_memo,
                'to_survey' =>  $model->to_survey ? Yii::app()->createUrl('/wechat/regWelcome/reEnrollment', array('sid' => $model->to_survey, 'state' => $state)) : "",
			);
			// 查找关联的选项
			$itemModels = SurveyReturn::getOptions($model->id);
			foreach ($itemModels as $itemModel) {
				$pids[] = $itemModel->id;
				$data['options'][] = array(
					'oid' => $itemModel->id,
					'type' => $itemModel->type,
					'title' => CommonUtils::autoLang($itemModel->cn_title, $itemModel->en_title),
				);
			}
			// 判断是否为最后一道题
			if ($order != 'frist') {
				$criteria = new CDbCriteria();
				$criteria->compare('pid', $pids);
				$criteria->compare('status', 1);
				$count = SurveyReturnQuestions::model()->count($criteria);
				if ($count == 0 && $model->jump_id <= 0) {
					$order = 'last';
				}
			}
			$data['question']['order'] = $order;
		}
		return $data;
	}

	// 获取上一道题
	public static function getPre($returnid, $pid)
	{
		$returnChildModel = SurveyReturnChild::model()->findByPk($returnid);
		if (!$returnChildModel) {
			return array();
		}
		$questionModel = SurveyReturnQuestions::model()->findByPk($pid);
		if (!$questionModel) {
			return array();
		}
		if ($questionModel->type == 'option') {
			$pid = $questionModel->pid;
			$questionModel = SurveyReturnQuestions::model()->findByPk($pid);
		}
		$pids = array($questionModel->id);
		if ($questionModel->type == 'content') {
            $showType = Yii::t('survey', 'Hint');
        } else {
            if ($questionModel->is_multiple) {
                $showType = Yii::t('survey', 'Select all that applies');
            } else {
                $showType = Yii::t('survey', 'Please select');
            }
        }
		$data['options'] = array();
		$data['question'] = array(
			'qid' => $questionModel->id,
			'pid' => $questionModel->pid,
			'type' => $questionModel->type,
			'show_type' => $showType,
			'title' => CommonUtils::autoLang($questionModel->cn_title, $questionModel->en_title),
			'is_required' => $questionModel->is_required,
			'is_multiple' => $questionModel->is_multiple,
			'is_signature' => $questionModel->is_signature,
			'content_ext' => CommonUtils::autoLang($questionModel->content_ext, $questionModel->content_ext_en),
			'is_memo' => $questionModel->is_memo,
            'to_survey' => $questionModel->to_survey,
		);
		// 查找关联的选项
		$itemModels = SurveyReturn::getOptions($questionModel->id);
		foreach ($itemModels as $itemModel) {
			$pids[] = $itemModel->id;
			$data['options'][] = array(
				'oid' => $itemModel->id,
				'type' => $itemModel->type,
				'title' => CommonUtils::autoLang($itemModel->cn_title, $itemModel->en_title),
			);
		}
		$order = 'middle';
		if ($questionModel->pid == 0) {
			$order = 'frist';
		}
		if ($order != 'frist') {
			$criteria = new CDbCriteria();
			$criteria->compare('pid', $pids);
			$criteria->compare('status', 1);
			$count = SurveyReturnQuestions::model()->count($criteria);
			if ($count == 0) {
				$order = 'last';
			}
		}
        $data['question']['order'] = $order;
		return $data;
	}

	// 根据 pid 获取选项组
	public static function getOptions($pid)
	{
		// 查找关联的选项
		$criteria = new CDbCriteria();
		$criteria->compare('type', 'option');
		$criteria->compare('pid', $pid);
		$criteria->compare('status', 1);
		$criteria->order = 'option_sort ASC';
		$itemModels = SurveyReturnQuestions::model()->findAll($criteria);
		return $itemModels;
	}

	// 根据 pid 获取答案
	public static function getAnswer($childid, $sid, $qid)
	{
		// 查找最后答题的历史
		$criteria = new CDbCriteria();
		$criteria->compare('childid', $childid);
		$criteria->compare('survey_id', $sid);
		$criteria->compare('q_id', $qid);
		$criteria->order = "updated_at DESC";
		$model = SurveyReturnAnswer::model()->find($criteria);
		$data = array('q_pre' => 0, 'q_val' => '', 'q_memo' => '', 'q_signature' => '');
		if ($model) {
			$data['q_pre'] = $model->pre_id;
			$data['q_val'] = json_decode($model->q_val);
			$data['q_memo'] = $model->q_memo;
			$data['q_signature'] = ($model->q_signature) ? SurveyReturn::getThumbOssImageUrl($model->q_signature) : '' ;
		}
		return $data;
	}

    public static function getThumbOssImageUrl($fileName, $style = '')
    {
        $fileName = 'survey/' . $fileName;
        // 获取文件临时地址
        $osscs = CommonUtils::initOSSCS('private');

        $object = $fileName;
        // $style = 'style/w200';
        $imageUrl = $osscs->getImageUrl($object, $style);
        return $imageUrl;
    }

}
