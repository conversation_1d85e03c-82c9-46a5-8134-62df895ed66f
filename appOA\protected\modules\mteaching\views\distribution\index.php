<style>
    .fixed-width {
        max-width: 250px;
        /* 固定列宽度 */
        word-wrap: break-word;
        /* 自动换行 */
    }

    .period-width {
        width: 20%;
        /* 固定列宽度 */
        word-wrap: break-word;
        /* 自动换行 */
    }
</style>
<div class="container-fluid" id="container">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Teaching Tasks'), array('//mteaching/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('principal', 'Secondary School Achievement Report'), array('//mteaching/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('principal', 'Assign Course Standards'); ?></li>
    </ol>
    <div class="row">
        <template>
            <div class="col-md-2">
                <div class="list-group">
                    <span
                        v-for="(title, key) in classArr"
                        :key="key"
                        class="list-group-item"
                        :class="{ 'list-group-item': true, 'active': key == classNum }"
                        @click="switchCourse(key)">
                        {{ title }}
                    </span>
                </div>
            </div>
            <div class="col-md-10" v-if="classNum > 0">
                <table class="table table-bordered" id="student-table">
                    <thead>
                        <tr>
                            <th class="fixed-width">
                                <h4><?php echo Yii::t('attends', 'Course Title'); ?></h4>
                            </th>
                            <th class="period-width" v-for="period in [1, 2, 3, 4]">
                                <h4 :title="achieveList[period].title">{{`周期 ${period}`}}</h4>
                                <p>ID {{achieveList[period].tid}}: {{achieveList[period].title}}</p>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="(classCourse, programCode) in classCourses">
                            <td class="fixed-width">
                                <strong>{{classCourse.title}}</strong>
                                <div>
                                    <span v-for="code in classCourse.codeArr" class="label label-default mr5">{{code}}</span>
                                </div>
                            </td>

                            <td v-for="index in [1, 2, 3, 4]" :key="index">
                                <select
                                    v-model="classCourse[`period${index}`]"
                                    class="form-control"
                                    :data-tid="achieveList[index].tid"
                                    @change="syncSelection(programCode, index)">
                                    <option value="0">请选择</option>
                                    <option v-for="item in achieveList[index].items" :key="item.id" :value="item.id">
                                        {{ item.title }}
                                    </option>
                                </select>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div class="text-right">
                    <button type="button" class="btn btn-primary" @click="saveAchievement()" :disabled="isLoading"><?php echo Yii::t('global', 'Submit'); ?></button>
                </div>
            </div>
        </template>
    </div>
</div>

<script>
    var classArr = <?php echo CJSON::encode($classArr); ?>;
    var courses = <?php echo CJSON::encode($courseList); ?>;
    var secodarArr = <?php echo CJSON::encode($secodarArr); ?>;
    var gradeTemplate = <?php echo json_encode($gradeTemplate, JSON_FORCE_OBJECT); ?>;
    var gradeTemplateData = <?php echo json_encode($gradeTemplateData, JSON_FORCE_OBJECT); ?>;
    var classNum = <?php echo $_GET['class'] ? $_GET['class'] : 0;  ?>

    function cbSuccess(data) {
        courses[classid_global] = data[classid_global];
        resultTip({
            msg: '保存成功'
        });
        $(".btn").attr("disabled", false);
    }

    var vm = new Vue({
        el: "#container",
        data: {
            'classArr': classArr,
            'courses': courses,
            'secodarArr': secodarArr,
            'gradeTemplate': gradeTemplate,
            'gradeTemplateData': gradeTemplateData,
            'templatePeriod': {},
            'classNum': 0,
            'classCourses': {},
            'achieveList': {},
            'isLoading': false
        },
        created: function() {
            if (classNum > 0) {
                this.switchCourse(classNum);
            }
        },
        methods: {
            syncSelection(code, periodNum) {
                const tid = this.templatePeriod[periodNum]
                for (const key in this.templatePeriod) {
                    const _tid = this.templatePeriod[key];
                    if (tid == _tid) {
                        this.classCourses[code]['period' + key] = this.classCourses[code]['period' + periodNum]
                    }
                }
                // 查找同periodNum的period
            },
            switchCourse(classNum) {
                this.classNum = classNum;
                this.templatePeriod = this.gradeTemplate[classNum] ? this.gradeTemplate[classNum] : {}

                let classStr = '' + classNum
                if (classStr.length == 1) {
                    classStr = '0' + classStr;
                }
                this.classCourses = courses[classStr] ? courses[classStr] : {}
                for (let index = 1; index < 5; index++) {
                    this.achieveList[index] = this.getAchievementList(index)
                }
            },
            getAchievementList(period) {
                let items = [];

                let templateId = this.templatePeriod[period];

                if (!templateId) {
                    templateId = this.templatePeriod[0]
                }
                if (!templateId) {
                    items = [];
                } else {
                    items = this.secodarArr[templateId] ? this.secodarArr[templateId] : [];
                }
                templateId = templateId ? templateId : 0
                return {
                    'tid': templateId ? templateId : 0,
                    'title': this.gradeTemplateData[templateId] ? this.gradeTemplateData[templateId].title : '',
                    'items': items
                }
            },
            saveAchievement() {
                let _this = this
                this.isLoading = true; // 设置按钮不可点击

                let savaData = {}
                for (let code in this.classCourses) {
                    const itemData = {
                        'period1': this.classCourses[code].period1,
                        'period2': this.classCourses[code].period2,
                        'period3': this.classCourses[code].period3,
                        'period4': this.classCourses[code].period4,
                    };
                    savaData[code] = itemData;
                }

                $.ajax({
                    url: '<?php echo $this->createUrl("saveAchievement") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        savaData: savaData,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg: data.message
                            });
                            _this.classCourses = data.data
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        _this.isLoading = false
                    },
                    error: function(data) {},
                })
            },
        }
    })
</script>