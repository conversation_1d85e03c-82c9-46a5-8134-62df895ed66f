<div>
    <div class='flex align-items  mb24 mt10'>
        <span class='font14 color3'>处理人：</span>
        <div class='flex1 flex align-items lib_class_handle'>
        </div>
    </div>
    <div class='flex align-items mb20 settlement'>
        <span class='redColor font14 settlement_date'></span> 
        <span class='color6 font12 ml15'><span class='el-icon-warning-outline mr5'></span>以此日期为在学截止日进行费用清算</span>
    </div>
    <div style='border:1px solid #000;margin-bottom:24px'>
        <table class="table table-bordered tableCss m0" id='totalTable'></table>
    </div>
    <div style='border:1px solid #000;margin-bottom:24px'>
        <table class="table table-bordered tableCss m0" id='viewTable'></table>
    </div>
    <div><img src="" alt="" class='signImg' id='signImg' style='width:300px;float:right'></div>
    <div>
        <form class="J_ajaxForm" action="<?php echo $this->createUrl("financeConfirmAll");?>" method="post">
            <input type="hidden" name="applicationId">
            <div id='allPassed'></div>

        </form>
    </div>
    <div class='clearfix'></div>
    <div id='panelList' class='mt16'></div>
    <div class='allSuccessForm'>    
        <div class='pushWechat'>
            <div class='font14 color3 fontBold'><?php echo Yii::t('withdrawal', 'Send for Parent Confirmation'); ?></div>
            <div class='font14 color6 mt12'><?php echo Yii::t('withdrawal', 'Send to'); ?></div>
            <div class='' id='wechatBox'>
            </div>
            <div class='font14 color6 mt12'><?php echo Yii::t('withdrawal', 'Content'); ?></div>
                <div class='mb16 mt12'>
                    <textarea class="form-control" rows="3" name='memo' placeholder='家长端推送内容'></textarea>
                </div>
                <div class='flex align-items'>
                    <div class='flex1 text-right font14 color3 mr24 relative'>
                        <span class='cur-p qrcodeShow' onclick='showQrcode()'  ><span class='glyphicon glyphicon-qrcode  qrcodeIcon'></span></span>
                        <div id="popover_content_wrapper"  style="display: none;">
                            <div class='flex' >
                                <div class='mr20 text-center'>
                                    <div class='font14 color3 text-center'><?php echo Yii::t('newDS','For Preview Only');?></div>
                                    <div  class='font14 color6 text-center mt4 mb5'><?php echo Yii::t('newDS',"Don't Share");?></div>
                                    <div id='qrcodeView'>
                                    </div>
                                </div>
                                <div class='ml20 text-center'>
                                    <div class='font14 color3 '>分享给家长</div>
                                    <div class='font14 color6 mt4 mb5'>需家长身份才能查看</div>
                                    <div id='qrcodeShare'>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <button type="button" class="btn btn-primary " id='sendButton' onclick='pushParent()'><?php echo Yii::t('withdrawal', 'Send'); ?></button>
                </div>
            </form>
            <div class='clearfix'></div>
            <div class='parentPushList'>
                <div>
                    <span class='color6 font14 '><?php echo Yii::t('withdrawal', 'Parent Confirmation'); ?></span>
                </div>
                <div class='flex relative mt16'>
                    <div class='financeLine'></div>
                    <div class='step1' >
                    </div>
                    <div class='flex1 ml12 stepHeight'>
                        <div class='mt5'>
                            <span class='font14 color3 fontBold '><?php echo Yii::t('withdrawal', 'Send'); ?></span>
                            <span class='font12 yellow cur-p pushDate'></span>
                        </div>
                        <div class='font12 color6 mt8'><span class='cur-p lateDate' onclick='pushList()'></span></span></div>
                        <div class='font12 color9 mt4 pushAmount'> </div>
                    </div>
                </div>
                <div class='flex relative mt16'>
                    <div class='financeLine'></div>
                    <div class='step2'>
                    </div>
                    <div class='flex1 ml12 stepHeight'>
                        <div  class='mt5'>
                            <span class='font14 color3 fontBold parentWait'></span> 
                            <span class='font12 redColor cur-p parentFeedback' onclick='parentConfirmList()'><span  class='el-icon-warning-outline'></span> 家长有反馈，请联系家长</span>
                        </div>
                        <div class='font12 color6 mt8 parentDateHide'><span class='cur-p' onclick='parentConfirmList()'><span class='parentDate'></span><span class='el-icon-arrow-right'></span></span></div>
                        <div class='font12 color9 mt4 parentAmount'></div>
                        <div class='bankInfo'>
                            <div class='font12 color3'>收款账户：</div>
                            <div class='flex mt8 font12 bankTitle'>
                            </div>
                        </div>
                    </div>
                </div>
                <div class='flex relative mt16'>
                    <div class='step3'></div>
                    <div class='flex1 ml12 stepHeight'>
                        <div  class='mt5'><span class='font14 color3 fontBold'><?php echo Yii::t('withdrawal', 'Confirmed'); ?></span> </div>
                        <div class='font14 mt8 signView'>
                            <span class='cur-p bluebg' onclick='download()'>查看和打印</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <form class="J_ajaxForm" action="<?php echo $this->createUrl("saveNode");?>" method="post">
            <input type="hidden" name="type" id="type" value='calc'>
            <input type="hidden" name="applicationId" id="applicationId">
            <input type="hidden" name="node" id="node">
            <div class='modal-footer borderTopNone p0 mt24'>
                <span id="J_fail_info" class="text-warning"></span>
                <label class="checkbox-inline"><input type="checkbox" name="status" value="1"> <?php echo Yii::t('withdrawal', 'Mark this step as completed'); ?></label><button type="button" class="btn btn-primary J_ajax_submit_btn ml24"><?php echo Yii::t('global','Submit');?></button>
            </div>
        </form>
    </div>
    
</div>

<script>
    lib()
    
    function lib() {
        if(childDetails.info.appInfo.settlement_date){
            $('.settlement_date').text('清算日期：'+childDetails.info.appInfo.settlement_date)
        }else{
            $('.settlement').hide()
        }
        $('[data-toggle="popover"]').popover()
        let forms = childDetails.forms[0]
        for(var key in forms.owner_info){
            if (key !== '2' && key !== '5') {
                $('.lib_class_handle').append('<div class="flex align-items mr16"><img src='+forms.owner_info[key].photoUrl+' alt="" class="img28"/><div class="font14 color3 ml8">'+forms.owner_info[key].name+'</div></div>')
            }
        }
        if (forms.pass_all == 0) {
            $('#sendButton').prop('disabled', true);    
            $('#allPassed').html('<button type="button" class="btn btn-success pull-right J_ajax_submit_btn">全部通过</button>');
        } else if (forms.pass_all == 2) {
            $('#sendButton').prop('disabled', true);    
            $('#allPassed').html('<button type="button" class="btn btn-success pull-right noPass" disabled>全部通过</button>');
        } else {
            $('#allPassed').html("<span class='bluebg font14 pull-right ml20 mt5 cur-p' onclick='cancalSuccess()'><?php echo Yii::t('withdrawal', 'Cancel Approval'); ?></span><span class='allSuccess'><span class='glyphicon glyphicon-ok-sign'></span> <?php echo Yii::t('withdrawal', 'All Approved'); ?></span>");
            $('#sendButton').prop('disabled', false);    
        }
        if(forms.data.parent_feedback_log){
            $('textarea[name="memo"]').val('')
            $('.allSuccessForm').show();
            initSend()
            if(childDetails.forms[0].data.parent_feedback_log.length!=0 || childDetails.forms[0].data.send_log.length!=0){
                parentStep()
            }else{
                $('.parentWait').text('<?php echo Yii::t('withdrawal', 'Pending Confirmation'); ?>') 
                $('.parentFeedback').hide()
                $('.bankInfo').hide()
                $('.step1').html("<span class='financeStepNone'>1</span>")
                $('.step2').html("<span class='financeStepNone'>2</span>")
                $('.step3').html("<span class='financeStepNone'>3</span>")
                $('.parentAmount').hide()
                $('.parentDateHide').hide()
                $('.signView').hide()
            }
        }else{
            $('.allSuccessForm').hide();
        }
        tableHtml(forms)
        $('input[name="applicationId"]').val(container.applicationId)
    }
    function tableHtml(forms){
        var domData = ''
        var viewData = ''
        var rejectDom = ''
        var viewRejectDom = ''
        var totalEnd = 0
        var explainList={
            1:'亲爱的家长您好，根据北京市社会医疗保险（一老一小）的要求，在校学生的社会医疗保险需要跟随学生的就读学校进行迁移。由于学生需要从启明星学校转出，我校将不再为其办理下一年度社会医疗保险的续保事宜。您孩子的社会医疗保险关系即日起将从本校转出，建议您尽快联系孩子的新学校或户口所在社区进行续保，以确保不会断保。',
            2:'亲爱的家长您好，根据北京市社会医疗保险（一老一小）的要求，学生的保险需要跟随学生的所在学校/学籍进行迁移。对于已经离校的学生，启明星学校原则上不再为学生办理一老一小保险的参保及续保手续。对于未到转出学籍窗口期，学生虽已离校，但学籍仍需暂时由我校代为保管的情况，需要家长知晓：1. 请家长在转出学籍窗口期尽快完成学籍转移，并同时联系校医室办理一老一小保险的转出。2. 由于学籍暂未转出原因，需要代管一老一小保险的最长代管期为一年，逾期将默认进行减员。3. 请家长主动联系校医室申请一老一小保险代管。未在学生转走一个月内联系校医室，学生的一老一小保险将默认进行减员。'
        }
        const viewTable = forms.return.filter(item => item.key === 'enrollment' || item.key === 'insurance');
        let amountTable = forms.return.filter((i) => i.key != 'enrollment' && i.key != 'insurance')
        if (forms.pass_all == 1) {
            for (let k = 0; k < amountTable.length; k++) {
                totalEnd += Number(amountTable[k].data.totalAmount)
                if (amountTable[k].confirm_status == 2) {
                    domData += '<tbody style="background:rgba(217, 83, 79, 0.04) !important">'
                } else {
                    domData += '<tbody>'
                } if (amountTable[k].key == 'it_equ') {
                    domData += '<tr>' +
                        '<td width="200" class="relative">'
                    if (amountTable[k].confirm_status == 2 || amountTable[k].confirm_status == 3) {
                        domData += '<div class="fontBold font14 color3 text-center mt20">' + amountTable[k].title + '</div>' +
                            '</td>'
                    } else {
                        domData += '<div class="fontBold font14 color3 text-center">' + amountTable[k].title + '</div>' +
                            '</td>'
                    }

                    if (amountTable[k].confirm_status == 2) {
                        domData += '<td colspan="2">'
                        domData += '<div class="flex mt20">'
                    } else if (amountTable[k].confirm_status == 3) {
                        domData += '<td colspan="2" style="background:#FAFAFA">'
                        domData += '<div class="flex mt20">'
                    } else {
                        domData += '<td colspan="2" style="background:#FAFAFA">'
                        domData += '<div class="flex align-items">'
                    }
                    domData += '<div class=" font14 flex1">'
                    if (amountTable[k].data.balance == '0') {
                        domData += '<span>未归还</span>'
                    } else if (amountTable[k].data.balance == '1') {
                        domData += '<span>已归还—状态完好</span>'
                    } else {
                        domData += '<span>已归还—存在设备损坏</span>'
                    }
                    if (amountTable[k].reject_log.length > 0) {
                        domData += '<span class="el-icon-document bluebg ml8 font16" data-toggle="tooltip" data-placement="top" title="驳回记录"  data-name=' + amountTable[k].key + ' onclick="rejectedList(this)"></span>'
                    }
                    domData += '</div>' +
                        '<div class="totalAmount color3 font16">' +
                        '<div class="amount "><?php echo Yii::t('payment','Subtotal');?>：' + formatAmount(amountTable[k].data.totalAmount) + '</div>'

                    domData += '</div>' +
                        '</div>' +
                        '</td>' +
                        '</tr>'
                } else if (amountTable[k].data.balance == '1' && !Array.isArray(amountTable[k].data.balance)) {
                    domData += '<tr>' +
                        '<td width="200" class="relative">'
                    if (amountTable[k].confirm_status == 2 || amountTable[k].confirm_status == 3) {
                        domData += '<div class="fontBold font14 color3 text-center mt20">' + amountTable[k].title + '</div>' +
                            '</td>'
                    } else {
                        domData += '<div class="fontBold font14 color3 text-center">' + amountTable[k].title + '</div>' +
                            '</td>'
                    }

                    if (amountTable[k].confirm_status == 2) {
                        domData += '<td colspan="2">'
                        domData += '<div class="flex mt20">'
                    } else if (amountTable[k].confirm_status == 3) {
                        domData += '<td colspan="2" style="background:#FAFAFA">'
                        domData += '<div class="flex mt20">'
                    } else {
                        domData += '<td colspan="2" style="background:#FAFAFA">'
                        domData += '<div class="flex align-items">'
                    }
                    domData += '<div class="greenColor font14 flex1"><?php echo Yii::t('withdrawal','Fully Reimbursed');?> '
                    if (amountTable[k].reject_log.length > 0) {
                        domData += '<span class="el-icon-document bluebg ml8 font16" data-toggle="tooltip" data-placement="top" title="驳回记录"  data-name=' + amountTable[k].key + ' onclick="rejectedList(this)"></span>'
                    }
                    domData += '</div>' +
                        '<div class="totalAmount color3 font16">' +
                        '<div class="amount "><?php echo Yii::t('payment','Subtotal');?>：' + formatAmount(amountTable[k].data.totalAmount) + '</div>'

                    domData += '</div>' +
                        '</div>' +
                        '</td>' +
                        '</tr>'
                } else {
                    for (let i = 0; i < amountTable[k].data.goods.length; i++) {
                        domData += '<tr>'
                        if (i == 0) {
                            domData += '<td width="200" class="relative" rowspan=' + amountTable[k].data.goods.length + 1 + '>'
                            if (amountTable[k].confirm_status == 2 || amountTable[k].confirm_status == 3) {
                                domData += '<div class="fontBold font14 color3 text-center mt20">' + amountTable[k].title + '</div>' +
                                    '</td>'
                            } else {
                                domData += '<div class="fontBold font14 color3 text-center">' + amountTable[k].title + '</div>' +
                                    '</td>'
                            }
                        }
                        domData += '<td>' +
                            '<div class="color3 font14">' + amountTable[k].data.goods[i].name + '</div>'
                        if (amountTable[k].data.goods[i].memo != '') {
                            domData += '<div class="color6 font12 mt4"><?php echo Yii::t('withdrawal','Comment');?>：' + amountTable[k].data.goods[i].memo + '</div>'
                        }
                        domData += '</td>' +
                            '<td width="200">' +
                            '<div class="totalAmount">'
                        if (amountTable[k].data.goods[i].balance == '1' || (amountTable[k].data.balance == '0' && !Array.isArray(amountTable[k].data.balance))) {
                            domData += '<div class="color3 font14 amount" style="text-align:right">-' + formatAmount(amountTable[k].data.goods[i].amount) + '</div>'
                        } else {
                            domData += '<div class="color3 font14 amount" style="text-align:right">' + formatAmount(amountTable[k].data.goods[i].amount) + '</div>'
                        }
                        domData += '</div>' +
                            '</tr>'
                    }
                    if (amountTable[k].confirm_status == 2) {
                        domData += '<tr>'
                    } else {
                        domData += '<tr  class="amountBg">'
                    }
                    domData += '<td colspan="2">' +
                        '<div class="flex align-items">' +
                        '<div class="flex1">'
                    if (amountTable[k].reject_log.length > 0) {
                        domData += '<span class="el-icon-document bluebg font16" data-toggle="tooltip" data-placement="top" title="驳回记录"  data-name=' + amountTable[k].key + ' onclick="rejectedList(this)"></span>'
                    }
                    domData += '</div>' +
                        '<div class="totalAmount font16">'
                    if (amountTable[k].data.totalAmount < 0) {
                        domData += '<div class="amount redColor"><?php echo Yii::t('payment','Subtotal');?>：' + formatAmount(amountTable[k].data.totalAmount) + '</div>'
                    } else {
                        domData += '<div class="amount "><?php echo Yii::t('payment','Subtotal');?>：' + formatAmount(amountTable[k].data.totalAmount) + '</div>'
                    }
                    domData += '</div>' +
                        '</div>' +
                        '</td>' +
                        '</tr>'
                }
                domData += '</tbody>'
            }
            domData += '<tbody>' +
                '<tr style="background:#F2F3F5">' +
                '<td class="fontBold font14 color3 text-center"><?php echo Yii::t('asa','Total');?></td>' +
                '<td colspan="2">' +
                '<div class="flex">' +
                '<div class="greenColor font14 flex1">' +
                '<div class="color3 font14"><?php echo Yii::t('withdrawal','Positive value refund to parent');?></div>' +
                '<div class="color3 font14 mt4"><?php echo Yii::t('withdrawal','Negative value deduction from parent');?></div>' +
                '</div>' +
                '<div class="totalAmount ">' +
                '<div class="color3 font22 amount" style="font-size:22px">' + formatAmount(totalEnd) + '</div>' +
                '</div>' +
                '</div>' +
                '</td>' +
                '</tr>' +
                '</tbody>'
            $('#totalTable').html(domData);

            for (let k = 0; k < viewTable.length; k++) {
                viewData += '<tbody>'+
                    '<tr>' +
                    '<td width="200" class="relative">' + 
                        '<div class="fontBold font14 color3 text-center">' + viewTable[k].title + '</div>' +
                    '</td>'+
                    '<td colspan="2">'+
                    '<div class="flex ">'+
                    '<div class="font14 flex1">'
                    if(viewTable[k].key=='enrollment'){
                        if(viewTable[k].data.balance=='1'){
                            viewData+='<div class="color3 font14">'+
                            '<span>有学籍</span>'
                            if (viewTable[k].data.enrollment_state) {
                                viewData += '<span class="xuejiCss">'+showState(viewTable[k].data.enrollment_state)+'</span>'
                            }
                            if (viewTable[k].reject_log.length > 0) {
                                viewData += '<span class="el-icon-document bluebg ml8 font16" data-toggle="tooltip" data-placement="top" title="驳回记录"  data-name=' + viewTable[k].key + ' onclick="rejectedList(this)"></span>'
                            }
                            
                            viewData+='</div>'
                        }else{
                            viewData+='<div class="color3 font14">'+
                            '<span>无学籍</span>'
                            if (viewTable[k].reject_log.length > 0) {
                                viewData += '<span class="el-icon-document bluebg ml8 font16" data-toggle="tooltip" data-placement="top" title="驳回记录"  data-name=' + viewTable[k].key + ' onclick="rejectedList(this)"></span>'
                            }
                            viewData+='</div>'
                        }
                    }
                    if(viewTable[k].key=='insurance'){
                        viewData+='<div class="color3 font14">'
                        if(viewTable[k].data.balance=='1'){
                            viewData+='<span><?php echo Yii::t('withdrawal', 'Insured through Daystar, transferred out.'); ?></span>'
                        }else if(viewTable[k].data.balance=='2'){
                            viewData+='<span><?php echo Yii::t('withdrawal', 'Insured through Daystar, not transferred out yet.'); ?></span>'
                        }else if(viewTable[k].data.balance=='0'){
                            viewData+='<span><?php echo Yii::t('withdrawal', 'Not insured through Daystar.'); ?></span>'
                        }
                        if (viewTable[k].reject_log.length > 0) {
                            viewData += '<span class="el-icon-document bluebg ml8 font16" data-toggle="tooltip" data-placement="top" title="驳回记录"  data-name=' + viewTable[k].key + ' onclick="rejectedList(this)"></span>'
                        }
                        viewData+='</div>'
                        if (viewTable[k].data.balance=='1' || viewTable[k].data.balance=='2') {
                            viewData += '<div class="color6 font12 mt4"><?php echo Yii::t('withdrawal','说明');?>：' +explainList[viewTable[k].data.balance]+ '</div>'
                        }
                    }
                    if (viewTable[k].data.remark != '') {
                        viewData += '<div class="color6 font12 mt4"><?php echo Yii::t('withdrawal','Comment');?>：' + viewTable[k].data.remark + '</div>'
                    }
                viewData+='</div>' +
                    '</div>' +
                    '</td>' +
                    '</tr>'+
                '</tbody>'
            }
            $('#viewTable').html(viewData);
        } else {
            for (let k = 0; k < amountTable.length; k++) {
                totalEnd += Number(amountTable[k].data.totalAmount)
                if (amountTable[k].confirm_status == 2) {
                    rejectDom = '<span class="rejected">' +
                        ' <div class="dropdown">' +
                        '<span class="dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true"><span class="glyphicon glyphicon-exclamation-sign mr5"></span>已驳回<span class="caret ml5"></span></span>' +
                        '<ul class="dropdown-menu" aria-labelledby="dropdownMenu1">' +
                        '<li><a href="javascript:;" data-name=' + amountTable[k].key + ' onclick="cancelRejected(this)">撤销驳回</a></li>' +
                        '<li><a href="javascript:;" data-name=' + amountTable[k].key + ' onclick="rejectedList(this)">驳回记录</a></li>' +
                        '</ul>' +
                        '</div>' +
                        '</span>'
                } else if (amountTable[k].confirm_status == 3) {
                    rejectDom = '<span class="resubmitted">' +
                        ' <div class="dropdown">' +
                        '<span class="dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true"><span class="glyphicon glyphicon-info-sign mr5"></span>已重新提交<span class="caret ml5"></span></span>' +
                        '<ul class="dropdown-menu" aria-labelledby="dropdownMenu1">' +
                        '<li><a href="javascript:;" data-name=' + amountTable[k].key + ' onclick="rejectedList(this)">驳回记录</a></li>' +
                        '</ul>' +
                        '</div>' +
                        '</span>'
                } else {
                    rejectDom = ''
                }
                if (amountTable[k].confirm_status == 2) {
                    domData += '<tbody style="background:rgba(217, 83, 79, 0.04) !important">'
                } else {
                    domData += '<tbody>'
                }
                if (amountTable[k].key == 'it_equ') {
                    domData += '<tr>' +
                        '<td width="200" class="relative">' + rejectDom
                    if (amountTable[k].confirm_status == 2 || amountTable[k].confirm_status == 3) {
                        domData += '<div class="fontBold font14 color3 text-center mt20">' + amountTable[k].title + '</div>' +
                            '</td>'
                    } else {
                        domData += '<div class="fontBold font14 color3 text-center">' + amountTable[k].title + '</div>' +
                            '</td>'
                    }
                    if (amountTable[k].confirm_status == 2) {
                        domData += '<td colspan="2">'
                        domData += '<div class="flex mt20">'
                    } else if (amountTable[k].confirm_status == 3) {
                        domData += '<td colspan="2" style="background:#FAFAFA">'
                        domData += '<div class="flex mt20">'
                    } else {
                        domData += '<td colspan="2" style="background:#FAFAFA">'
                        domData += '<div class="flex">'
                    }
                    domData += '<div class=" font14 flex1">'
                    if (amountTable[k].data.balance == '0') {
                        domData += '<span>未归还</span>'
                    } else if (amountTable[k].data.balance == '1') {
                        domData += '<span>已归还—状态完好</span>'
                    } else {
                        domData += '<span>已归还—存在设备损坏</span>'
                    }
                    domData += '</div>' +
                        '<div class="totalAmount color3 font16">'
                    if (amountTable[k].data.totalAmount < 0) {
                        domData += '<div class="amount redColor"><?php echo Yii::t('payment','Subtotal');?>：' + formatAmount(amountTable[k].data.totalAmount) + '</div>'
                    } else {
                        domData += '<div class="amount "><?php echo Yii::t('payment','Subtotal');?>：' + formatAmount(amountTable[k].data.totalAmount) + '</div>'

                    }
                    domData += '<span class="iconWidth">'
                    if (amountTable[k].confirm_status == 2) {
                        domData += '<span class="glyphicon glyphicon-share-alt rotateY color9"></span></span>'
                    } else {
                        domData += '<span class="glyphicon glyphicon-share-alt rotateY redColor" data-toggle="tooltip" data-placement="top" title="驳回"  data-name=' + amountTable[k].key + ' onclick="checkReject(this)"></span></span>'
                    }
                    domData += '</div>' +
                        '</div>' +
                        '</td>' +
                        '</tr>'
                } else if (amountTable[k].data.balance == '1' && !Array.isArray(amountTable[k].data.balance)) {
                    domData += '<tr>' +
                        '<td width="200" class="relative">' + rejectDom
                    if (amountTable[k].confirm_status == 2 || amountTable[k].confirm_status == 3) {
                        domData += '<div class="fontBold font14 color3 text-center mt20">' + amountTable[k].title + '</div>' +
                            '</td>'
                    } else {
                        domData += '<div class="fontBold font14 color3 text-center">' + amountTable[k].title + '</div>' +
                            '</td>'
                    }

                    if (amountTable[k].confirm_status == 2) {
                        domData += '<td colspan="2">'
                        domData += '<div class="flex mt20">'
                    } else if (amountTable[k].confirm_status == 3) {
                        domData += '<td colspan="2" style="background:#FAFAFA">'
                        domData += '<div class="flex mt20">'
                    } else {
                        domData += '<td colspan="2" style="background:#FAFAFA">'
                        domData += '<div class="flex">'
                    }
                    domData += '<div class="greenColor font14 flex1"><?php echo Yii::t('withdrawal','Fully Reimbursed');?></div>' +
                        '<div class="totalAmount color3 font16">'
                    if (amountTable[k].data.totalAmount < 0) {
                        domData += '<div class="amount redColor"><?php echo Yii::t('payment','Subtotal');?>：' + formatAmount(amountTable[k].data.totalAmount) + '</div>'
                    } else {
                        domData += '<div class="amount "><?php echo Yii::t('payment','Subtotal');?>：' + formatAmount(amountTable[k].data.totalAmount) + '</div>'

                    }
                    domData += '<span class="iconWidth">'
                    if (amountTable[k].confirm_status == 2) {
                        domData += '<span class="glyphicon glyphicon-share-alt rotateY color9"></span></span>'
                    } else {
                        domData += '<span class="glyphicon glyphicon-share-alt rotateY redColor" data-toggle="tooltip" data-placement="top" title="驳回"  data-name=' + amountTable[k].key + ' onclick="checkReject(this)"></span></span>'
                    }
                    domData += '</div>' +
                        '</div>' +
                        '</td>' +
                        '</tr>'
                } else {
                    for (let i = 0; i < amountTable[k].data.goods.length; i++) {
                        domData += '<tr>'
                        if (i == 0) {
                            domData += '<td width="200" class="relative" rowspan=' + amountTable[k].data.goods.length + 1 + '>' + rejectDom
                            if (amountTable[k].confirm_status == 2 || amountTable[k].confirm_status == 3) {
                                domData += '<div class="fontBold font14 color3 text-center mt20">' + amountTable[k].title + '</div>' +
                                    '</td>'
                            } else {
                                domData += '<div class="fontBold font14 color3 text-center">' + amountTable[k].title + '</div>' +
                                    '</td>'
                            }
                        }
                        domData += '<td>' +
                            '<div class="color3 font14">' + amountTable[k].data.goods[i].name + '</div>'
                        if (amountTable[k].data.goods[i].memo != '') {
                            domData += '<div class="color6 font12 mt4"><?php echo Yii::t('withdrawal','Comment');?>：' + amountTable[k].data.goods[i].memo + '</div>'
                        }
                        domData += '</td>' +
                            '<td width="200">' +
                            '<div class="totalAmount">'
                        if (amountTable[k].data.goods[i].balance == '1' || (amountTable[k].data.balance == '0' && !Array.isArray(amountTable[k].data.balance))) {
                            domData += '<div class="color3 font14 amount" style="text-align:right">-' + formatAmount(amountTable[k].data.goods[i].amount) + '</div>'
                        } else {
                            domData += '<div class="color3 font14 amount" style="text-align:right">' + formatAmount(amountTable[k].data.goods[i].amount) + '</div>'
                        }
                        domData += '<span class="iconWidth"></span>' +
                            '</div>' +
                            '</tr>'
                    }
                    if (amountTable[k].confirm_status == 2) {
                        domData += '<tr>'
                    } else {
                        domData += '<tr  class="amountBg">'
                    }
                    domData += '<td colspan="2">' +
                        '<div class="flex">' +
                        '<div class="greenColor  flex1"></div>' +
                        '<div class="totalAmount font16">'
                    if (amountTable[k].data.totalAmount < 0) {
                        domData += '<div class="redColor  amount"><?php echo Yii::t('payment','Subtotal');?>：' + formatAmount(amountTable[k].data.totalAmount) + '</div>'
                    } else {
                        domData += '<div class=" amount"><?php echo Yii::t('payment','Subtotal');?>：' + formatAmount(amountTable[k].data.totalAmount) + '</div>'
                    }
                    domData += '<span class="iconWidth">'
                    if (amountTable[k].confirm_status == 2) {
                        domData += '<span class="glyphicon glyphicon-share-alt rotateY color9"></span></span>'
                    } else {
                        domData += '<span class="glyphicon glyphicon-share-alt rotateY redColor" data-toggle="tooltip" data-placement="top" title="驳回" data-name=' + amountTable[k].key + ' onclick="checkReject(this)"></span></span>'
                    }
                    domData += '</div>' +
                        '</div>' +
                        '</td>' +
                        '</tr>'
                }
                domData += '</tbody>'
            }
            domData += '<tbody>' +
                '<tr style="background:#F2F3F5">' +
                '<td class="fontBold font14 color3 text-center"><?php echo Yii::t('asa','Total');?></td>' +
                '<td colspan="2">' +
                '<div class="flex">' +
                '<div class="greenColor font14 flex1">' +
                '<div class="color3 font14"><?php echo Yii::t('withdrawal','Positive value refund to parent');?></div>' +
                '<div class="color3 font14 mt4"><?php echo Yii::t('withdrawal','Negative value deduction from parent');?></div>' +
                '</div>' +
                '<div class="totalAmount ">' +
                '<div class="color3 font22 amount" style="font-size:22px">' + formatAmount(totalEnd) + '</div>' +
                '<span class="iconWidth"></span>' +
                '</div>' +
                '</div>' +
                '</td>' +
                '</tr>' +
                '</tbody>'
            $('#totalTable').html(domData);

            for (let k = 0; k < viewTable.length; k++) {
                if (viewTable[k].confirm_status == 2) {
                    viewRejectDom = '<span class="rejected">' +
                        ' <div class="dropdown">' +
                        '<span class="dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true"><span class="glyphicon glyphicon-exclamation-sign mr5"></span>已驳回<span class="caret ml5"></span></span>' +
                        '<ul class="dropdown-menu" aria-labelledby="dropdownMenu1">' +
                        '<li><a href="javascript:;" data-name=' + viewTable[k].key + ' onclick="cancelRejected(this)">撤销驳回</a></li>' +
                        '<li><a href="javascript:;" data-name=' + viewTable[k].key + ' onclick="rejectedList(this)">驳回记录</a></li>' +
                        '</ul>' +
                        '</div>' +
                        '</span>'
                } else if (viewTable[k].confirm_status == 3) {
                    viewRejectDom = '<span class="resubmitted">' +
                        ' <div class="dropdown">' +
                        '<span class="dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true"><span class="glyphicon glyphicon-info-sign mr5"></span>已重新提交<span class="caret ml5"></span></span>' +
                        '<ul class="dropdown-menu" aria-labelledby="dropdownMenu1">' +
                        '<li><a href="javascript:;" data-name=' + viewTable[k].key + ' onclick="rejectedList(this)">驳回记录</a></li>' +
                        '</ul>' +
                        '</div>' +
                        '</span>'
                } else {
                    viewRejectDom = ''
                }
                if (viewTable[k].confirm_status == 2) {
                    viewData += '<tbody style="background:rgba(217, 83, 79, 0.04) !important">'
                } else {
                    viewData += '<tbody>'
                }
                // if (viewTable[k].data.balance == '1' && !Array.isArray(viewTable[k].data.balance)) {
                    viewData += '<tr>' +
                        '<td width="200" class="relative">' + viewRejectDom
                    if (viewTable[k].confirm_status == 2 || viewTable[k].confirm_status == 3) {
                        viewData += '<div class="fontBold font14 color3 text-center mt20">' + viewTable[k].title + '</div>' +
                            '</td>'
                    } else {
                        viewData += '<div class="fontBold font14 color3 text-center">' + viewTable[k].title + '</div>' +
                            '</td>'
                    }

                    if (viewTable[k].confirm_status == 2) {
                        viewData += '<td colspan="2">'
                        viewData += '<div class="flex ">'
                    } else if (viewTable[k].confirm_status == 3) {
                        viewData += '<td colspan="2" style="background:#FAFAFA">'
                        viewData += '<div class="flex ">'
                    } else {
                        viewData += '<td colspan="2" style="background:#FAFAFA">'
                        viewData += '<div class="flex">'
                    }
                    viewData += '<div class="font14 flex1">'
                        if(viewTable[k].key=='enrollment'){
                            if(viewTable[k].data.balance=='1'){
                                viewData+='<div class="color3 font14">有学籍'
                                if (viewTable[k].data.enrollment_state) {
                                    viewData += '<span class="xuejiCss">'+showState(viewTable[k].data.enrollment_state)+'</span>'
                                }
                                viewData+='</div>'
                            }else{
                                viewData+='<div class="color3 font14">无学籍</div>'
                            }
                        }
                        if(viewTable[k].key=='insurance'){
                            viewData+='<div class="color3 font14">'
                            if(viewTable[k].data.balance=='1'){
                              viewData+='<span><?php echo Yii::t('withdrawal', 'Insured through Daystar, transferred out.'); ?></span>'
                            }else if(viewTable[k].data.balance=='2'){
                              viewData+='<span><?php echo Yii::t('withdrawal', 'Insured through Daystar, not transferred out yet.'); ?></span>'
                            }else if(viewTable[k].data.balance=='0'){
                              viewData+='<span><?php echo Yii::t('withdrawal', 'Not insured through Daystar.'); ?></span>'
                            }
                            if (viewTable[k].reject_log.length > 0) {
                                viewData += '<span class="el-icon-document bluebg ml8 font16" data-toggle="tooltip" data-placement="top" title="驳回记录"  data-name=' + viewTable[k].key + ' onclick="rejectedList(this)"></span>'
                            }
                            viewData+='</div>'
                            if (viewTable[k].data.balance=='1' || viewTable[k].data.balance=='2') {
                                viewData += '<div class="color6 font12 mt4"><?php echo Yii::t('withdrawal','说明');?>：' +explainList[viewTable[k].data.balance]+ '</div>'
                            }
                        }
                        if (viewTable[k].data.remark != '') {
                            viewData += '<div class="color6 font12 mt4"><?php echo Yii::t('withdrawal','Comment');?>：' + viewTable[k].data.remark + '</div>'
                        }
                    viewData+=  '</div>' +
                        '<div class="totalAmount color3 font16">'+
                        '<div class="amount redColor"></div>'+
                        '<span class="iconWidth">'
                    if (viewTable[k].confirm_status == 2) {
                        viewData += '<span class="glyphicon glyphicon-share-alt rotateY color9"></span></span>'
                    } else {
                        viewData += '<span class="glyphicon glyphicon-share-alt rotateY redColor" data-toggle="tooltip" data-placement="top" title="驳回"  data-name=' + viewTable[k].key + ' onclick="checkReject(this)"></span></span>'
                    }
                    viewData += '</div>' +
                        '</div>' +
                        '</td>' +
                        '</tr>'
                // }
                viewData += '</tbody>'
            }
            $('#viewTable').html(viewData);
        }
    }
    function showState(id){
        const value = container.indexDataList.enrollmentStateList.filter(item => item.key ==id);
        return value[0].value
    }
    function cancalSuccess(){
        $.ajax({
            url: '<?php echo $this->createUrl("financeCancelConfirmAll") ?>',
            type: "post",
            dataType: 'json',
            data: {
                applicationId: container.applicationId,
            },
            success: function(data) {
                if (data.state == 'success') {
                    resultTip({
                        msg: data.message
                    });
                    cbSuccess2()
                } else {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                }
            },
            error: function(data) {
            },
        })
       
    }
    function checkReject(html) {
        container.rejectType = $(html).attr('data-name')
        container.rejectMemo = ''
        $('#rejectModal').modal('show')
    }
    function cancelRejected(html) {
        container.cancelRejectType = $(html).attr('data-name')
        $('#cancelRejectModal').modal('show')
    }
    function rejectedList(html) {
        container.getrejectedList($(html).attr('data-name'))
    }
    function initSend(){
        var list=''
        var latest_send={}
        let send_list=childDetails.forms[0].send_list
        let openids = send_list.map(user => user.openid);
        if(childDetails.forms[0].data.latest_send){
            latest_send=childDetails.forms[0].data.latest_send
        }
        if(send_list.length==0){
            return
        }
        $.ajax({
            url: '<?php echo $this->createUrl("getParentsFreq") ?>',
            type: "post",
            dataType: 'json',
            data: {
                childId: container.child_id,
                openIds:openids,
            },
            success: function(data) {
                if (data.state == 'success') {
                    for(let i=0;i<send_list.length;i++){
                        let parentType=send_list[i].parentType=='father'?"父亲":"母亲"
                        let total=Object.values(data.data[send_list[i].openid]).reduce((acc, curr) => acc + curr, 0)
                        var commentClass = total === 0 ? 'sendTag' : 'sendTagNum';
                        var interactionList = `
                            <div style='width:200px;padding:7px 6px 3px'>
                                <div class='font12 color3 fontBold mb12'>互动次数记录</div>
                                <div class='flex mb8 font12'><span class='flex1 color6'>沟通</span><span class='color3'>${data.data[send_list[i].openid].comment}</span></div>
                                <div class='flex mb8 font12'><span class='flex1 color6'>参与资料收集</span><span class='color3'>${data.data[send_list[i].openid].coll}</span></div>
                                <div class='flex mb8 font12'><span class='flex1 color6'>参与问卷</span><span class='color3'>${data.data[send_list[i].openid].survey}</span></div>
                                <div class='flex  font12'><span class='flex1 color6'>参与调查</span><span class='color3'>${data.data[send_list[i].openid].research}</span></div>
                            </div>
                        `;
                        if(commentClass=='sendTagNum'){
                            var question='<div class="mt12 interaction"><span class="mr8 interactionPopover '+commentClass+'" data-html="true" data-content="'+interactionList+'" tabindex="0" data-placement="bottom"  role="button" data-trigger="focus" data-container="body" data-toggle="popover" >互动 '+total+' 次</span></div>'
                        }else{
                            var question='<div class="mt12 interaction"><span class="mr8 '+commentClass+'" >互动 '+total+' 次</span></div>'
                        }
                        if(latest_send[send_list[i].openid]){
                            list="<div class='wechatList'>"
                            if(latest_send[send_list[i].openid].can_send){
                                list+="<input type='checkbox'  value='"+send_list[i].openid+"' name='openids'><div class='flex1 ml15'><div class='flex align-items'><img src="+send_list[i].headimgurl+" alt='' class='img28'><div class='font14 color3 ml8'>"+send_list[i].nickname+"</div><div class='font14 color6'>｜"+parentType+"</div></div>"+question+"<div class='font12 color6 mt10 cur-p' onclick='sendLog(this)' data-id="+send_list[i].openid+">最近一次推送："+latest_send[send_list[i].openid].date+" <span class='el-icon-arrow-right'></span></div>"
                            }else{
                                list+="<input type='checkbox'disabled  value='"+send_list[i].openid+"' name='openids'><div class='flex1 ml10'><div class='flex align-items'><img src="+send_list[i].headimgurl+" alt='' class='img28'><div class='font14 color3 ml8'>"+send_list[i].nickname+"</div><div class='font14 color6'>｜"+parentType+"</div></div>"+question+"<div class='font12 color6 mt10 cur-p' onclick='sendLog(this)' data-id="+send_list[i].openid+">最近一次推送："+latest_send[send_list[i].openid].date+" <span class='el-icon-arrow-right'></span></div>"
                            }
                        
                            if(!latest_send[send_list[i].openid].can_send){
                                list+="<div class='mt5 yellow'>"+latest_send[send_list[i].openid].interval_date+"可再次推送</div></div></div>"
                            }
                        }else{
                            list="<div class='wechatList'><input type='checkbox' value='"+send_list[i].openid+"' name='openids'><div class='flex1 ml15'><div class='flex align-items'><img src="+send_list[i].headimgurl+" alt='' class='img28'><div class='font14 color3 ml8'>"+send_list[i].nickname+"</div><div class='font14 color6'>｜"+parentType+"</div></div>"+question+"<div class='font12 color9 mt10'><?php echo Yii::t('withdrawal', 'No Records'); ?></div></div></div>"
                        }
                        $('#wechatBox').append(list)
                        $('.interactionPopover').popover();
                        $('.interactionPopover').on('shown.bs.popover', function () {
                            $(this).addClass('clicked');
                        }).on('hidden.bs.popover', function () {
                            $(this).removeClass('clicked');
                        });
                    }
                } else {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                }
            },
            error: function(data) {
            },
        })       
    }
    function sendLog(that){
        let list=$(that).attr('data-id')
        let log=childDetails.forms[0].data.openid_send_log
        var html=''
        for(let i=0;i<log[list].length;i++){
            html+="<div class='flex relative'>"+
                "<div class='dot'></div>"
                if(log[list].length>i+1){
                    html+="<span class='stepBorderLeft'></span>"
                }
                html+="<div class='ml8 pb24 flex1 width90' >"+
                    "<div>"+
                        "<span class='font14 color3 fontBold'>已推送</span>"+
                    "</div>"+
                    "<div class='color3 font14 mt5'>推送时间："+log[list][i].date+"</div>"+
                "</div>"+
            "</div>"
        }
        $('#dateList').html(html) 
        $('#dateListModal').modal('show')

    }
    function pushParent(){
        const checkboxes = document.querySelectorAll('input[type="checkbox"][name="openids"]:checked');
        const values = Array.from(checkboxes).map(checkbox => checkbox.value); 
        if(values.length==0){
            resultTip({
                error: 'warning',
                msg: '请勾选发送人员'
            });
            return
        }
        $('#sendButton').prop('disabled', true);
        $('#sendButton').text('发送中');
        $.ajax({
            url: '<?php echo $this->createUrl("financePush") ?>',
            type: "post",
            dataType: 'json',
            data: {
                applicationId: container.applicationId,
                node:'finance_confirm',
                task:'calc',
                openids:values,
                memo: $('textarea[name="memo"]').val()
            },
            success: function(data) {
                if (data.state == 'success') {
                    container.openModal(container.applicationId,'')
                    resultTip({
                        msg: data.state
                    });
                $('#sendButton').prop('disabled', false);
                $('#sendButton').text('推送');
                } else {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                    $('#sendButton').text('推送');
                    $('#sendButton').prop('disabled', false);
                }
            },
            error: function(data) {
                $('#sendButton').text('推送');
                $('#sendButton').prop('disabled', false);
            },
        })
    }
    function showQrcode(){
        if ($("#popover_content_wrapper").is(":visible")) {
            $('#popover_content_wrapper').toggle()
        }else{
            $('#qrcodeView').html('')
            $('#qrcodeShare').html('')
            $.ajax({
                url: '<?php echo $this->createUrl("getQrcode") ?>',
                type: "post",
                dataType: 'json',
                data: {
                    applicationId: container.applicationId,
                    type:'finance'
                },
                success: function(data) {
                    if (data.state == 'success') {
                        $('#qrcodeView').qrcode({
                            width:120,
                            height:120,
                            text:data.data.qrcodePreviewUrl,
                        });
                        $('#qrcodeShare').qrcode({
                            width: 120,
                            height: 120,
                            text:data.data.qrcodeUrl,
                        });
                        $('#popover_content_wrapper').toggle()
                    } else {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                },
                error: function(data) {
                },
            })
        }
    }
    function pushList() {
        $('#pushListModal').modal('show')
    }
    function parentConfirmList() {
        $('#parentConfirmModal').modal('show')
    }
    function parentStep(){
        if(childDetails.forms[0].data.send_log){        
            let sendLog=childDetails.forms[0].data.send_log
            if(sendLog.length>0){
                let log=sendLog[sendLog.length-1]
                $('input[name="memo"]').val(log.memo)
                $('.pushAmount').text('核算退款金额 '+formatAmount(log.amount)+' 元')
                $('.lateDate').html('最近一次推送：'+log.date+' <span class="el-icon-arrow-right">')
                if(childDetails.forms[0].data.send_log_latest.interval_date!=''){
                    $('.pushDate').html('<span class="el-icon-info"></span> '+childDetails.forms[0].data.send_log_latest.interval_date+'后可再次推送')
                }
            }
        }
        $('.parentFeedback').hide()
        
        // if(childDetails.forms[0].data.parent_confirm_status==1 || childDetails.forms[0].data.parent_confirm_status==2){
            if(childDetails.forms[0].data.parent_feedback_log && childDetails.forms[0].data.parent_feedback_log.length>0){
                let parentFeedback=childDetails.forms[0].data.parent_feedback_log
                let feedback=parentFeedback[parentFeedback.length-1]
                $('.parentWait').text('<?php echo Yii::t('withdrawal', 'Parent Confirmation'); ?>')
                $('.parentAmount').text('核算退款金额 '+formatAmount(feedback.amount)+' 元')
                $('.parentDate').text('最近一次确认：'+feedback.date) 
                if(Math.sign(feedback.amount) == 1 && childDetails.forms[0].data.bank_city && childDetails.forms[0].data.bank_city!=null){
                    $('.bankTitle').html("<div class='flex1'><div ><span class='bankWidth color6 '>所在城市</span><span class=' color3'>"+childDetails.forms[0].data.bank_city+"</span></div><div class='mt4'><span class='bankWidth color6  '>帐户名</span><span class=' color3'>"+childDetails.forms[0].data.bank_user+"</span></div></div><div class='flex1'><div><span class='bankWidth1 color6'>银行名称含支行</span> <span class=' color3'>"+childDetails.forms[0].data.bank_name+"</span></div><div class='mt4'><span class='bankWidth1 color6'>帐户号码</span> <span class=' color3'>"+childDetails.forms[0].data.bank_account+"</span></div></div>") 
                    $('.bankInfo').show()
                }else{
                    $('.bankInfo').hide()
                }
                if(feedback.status==2 && childDetails.forms[0].data.parent_confirm_status==2){
                    $('.parentFeedback').show()
                    
                }
                $('.step1').html("<span class='financeStep'><span class='el-icon-check'></span></span>")
            $('.step2').html("<span class='financeStep'><span class='el-icon-check'></span></span>")
            $('.step3').html("<span class='financeStepNone'>3</span>")
            // }
           
        }else{
            $('.parentWait').text('<?php echo Yii::t('withdrawal', 'Pending Confirmation'); ?>') 
            $('.step1').html("<span class='financeStep'><span class='el-icon-check'></span></span>")
            $('.step2').html("<span class='financeStepNone'>2</span>")
            $('.step3').html("<span class='financeStepNone'>3</span>")
            $('.parentAmount').hide()
            $('.bankInfo').hide()
            $('.parentDateHide').hide()
        }
        if(childDetails.forms[0].data.parent_confirm_status==1){
            $('.signView').show()
            $('.signImg').attr('src', childDetails.forms[0].data.sign_url);
            $('.step1').html("<span class='financeStep'><span class='el-icon-check'></span></span>")
            $('.step2').html("<span class='financeStep'><span class='el-icon-check'></span></span>")
            $('.step3').html("<span class='financeStep'><span class='el-icon-check'></span></span>")
        }else{
            $('.signView').hide()
        }
    }
    function download(){
        window.open('<?php echo $this->createUrl('print', array('branchId' => $this->branchId)); ?>'+'&appId='+container.applicationId)
    }
    function dataURLtoBlob(dataurl) {
        var arr = dataurl.split(',');
        var mime = arr[0].match(/:(.*?);/)[1];
        var bstr = atob(arr[1]);
        var n = bstr.length;
        var u8arr = new Uint8Array(n);
        while(n--) {
            u8arr[n] = bstr.charCodeAt(n);
        }
        return new Blob([u8arr], {type:mime});
    }
</script>
<style>
    .rejectInput {
        display: none
    }

    .pushWechat {
        background: #F0F5FB;
        border-radius: 4px;
        padding: 24px;
    }

    .financeStep {
        width: 28px;
        height: 28px;
        background: #4D88D2;
        display: inline-block;
        font-size: 18px;
        border-radius: 50%;
        text-align: center;
        line-height: 28px;
        color: #fff;
    }

    .financeStep1 {
        width: 28px;
        height: 28px;
        background: #4D88D2;
        display: inline-block;
        font-size: 14px;
        border-radius: 50%;
        text-align: center;
        line-height: 28px;
        color: #fff;
    }
    .financeStepNone{
        width: 28px;
        height: 28px;
        background: #F0F5FB;
        display: inline-block;
        font-size: 14px;
        border-radius: 50%;
        text-align: center;
        line-height: 26px;
        color: #4D88D2;
        border: 1px solid #4D88D2;
    }
    .financeLine {
        position: absolute;
        width: 1px;
        height: 100%;
        left: 13px;
        top: 28px;
        background: #4D88D2;
    }

    .resubmit {
        background: #F0F5FB;
        border-radius: 2px;
        color: #4D88D2;
        padding: 4px 6px;
        font-size: 12px;
        margin-left: 16px;
    }

    .totalAmount {
        width: 183px;
        display: flex;
        align-items: center
    }

    .amount {
        text-align: right;
        flex: 1;
        padding-right: 20px;
        color: #333;
    }

    .rotateY {
        transform: rotateY(180deg);
    }

    .iconWidth {
        width: 14px;
        font-size: 14px
    }

    .amountBg {
        background: #FAFAFA
    }

    .font22 {
        font-size: 22px
    }

    .rejected {
        position: absolute;
        top: 0;
        left: 0;
        background: rgba(217, 83, 79, 0.10);
        border-radius: 0px 12px 12px 0px;
        padding: 4px 12px;
        font-size: 14px;
        color: #D9534F !important;
    }

    .resubmitted {
        position: absolute;
        top: 0;
        left: 0;
        background: rgba(240, 173, 78, 0.10);
        border-radius: 0px 12px 12px 0px;
        padding: 4px 12px;
        font-size: 14px;
        color: #F0AD4E !important;
    }

    .m0 {
        margin: 0 !important
    }

    .allSuccess {
        color: #5CB85C;
        padding: 8px 12px;
        background: #F2F9F2;
        float: right
    }

    .noPass[disabled],
    .noPass:hover {
        background: #CCCCCC;
        border: 1px solid #CCCCCC;
    }
    .qrcode{
        width: 120px;
        height:120px;
        margin:12px
    }
    .wechatList{
        height:135px;
        background: #fff;
        border-radius: 4px;
        display:inline-flex;
        padding:16px;
        margin-right:16px;
        align-items: flex-start;
        margin-top:12px;
    }
    .popover{
        max-width:500px
    }
    .qrcodeIcon{
        font-size: 18px;
        padding: 6px;
        background: #fff;
        border-radius: 4px;
    }
    #popover_content_wrapper{
        padding: 24px 48px;
        position: absolute;
        right: 0;
        top:35px;
        background: #fff;
        border-radius: 4px
    }
    .stepHeight{
        min-height:50px
    }
    .signImg{
        display: none
    }
    .bankInfo{
        padding:10px 12px;
        background: #FAFAFA;
        border-radius: 4px;
        margin-top:8px
    }
    .bankWidth{
        width:65px;
        display: inline-block;
    }
    .bankWidth1{
        width:100px;
        display: inline-block;
    }
    .xuejiCss{
        color: #ED6A0C;
        background: rgba(237, 106, 12, 0.10);
        padding: 0 6px;
        border-radius: 10px;
        margin-left: 5px;
        font-size: 12px;
    }
    .sendTagNum{
        padding:4px 6px;
        font-size:12px;
        border-radius: 2px;
        background:#F0F5FB;
        color: #4D88D2;
    }
    .sendTag{
        padding:4px 6px;
        font-size:12px;
        border-radius: 2px;
        background:#F2F3F5;
        color: #333;
    }
    .interactionPopover{
        cursor: pointer;
    }
    .interactionPopover:hover{
        background: #4D88D2;
        color: #fff;
    }
    .clicked {
        background: #4D88D2;
        color: #fff;
    }
    .popover{
        border:none !important;
        z-index:1999 !important
    }
    .popover.bottom > .arrow{
        border-bottom-color: #fff;
        top: -10px;
    }
</style>