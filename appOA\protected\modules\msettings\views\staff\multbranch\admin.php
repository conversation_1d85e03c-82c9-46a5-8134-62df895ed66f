<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','System Management'), array('/msettings'))?></li>
        <li class="active">管理多校园</li>
    </ol>

    <div class="row">
        <div class="col-md-2">
            <?php
            $this->widget('zii.widgets.CMenu',array(
                'items'=> $mainMenu,
                'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked text-right background-gray'),
                'activeCssClass'=>'active',
                'itemCssClass'=>''
            ));
            ?>
        </div>
        <div class="col-md-10">
            <div class="table-responsive">
                <table width="100%" class="table table-hover">
                    <thead>
                    <tr class="background-gray">
                        <td colspan="3">
                            <a href="<?php echo $this->createUrl('addMultBranch', array('type'=>$type));?>" class="btn btn-primary J_dialog" title="添加管理校园">
                                <span class="glyphicon glyphicon-plus"></span>
                                添加用户
                            </a>
                        </td>
                    </tr>
                    <tr>
                        <th width="200">用户</th>
                        <th>管辖校园</th>
                        <th class="text-center" width="100">操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php foreach ($userList as $ul): ?>
                        <tr>
                            <th><?php echo $ul->getName();?></th>
                            <td>
                                <?php
                                $activeList = array();
                                if (!empty($ul->multBranchWithParam)):
                                    foreach($ul->multBranchWithParam as $mb):
                                        echo CHtml::openTag('span',array('class'=>'mr10'));
                                        echo $branchList[$mb->schoolid];
                                        echo CHtml::closeTag('span');
                                        $activeList[] = $mb->schoolid;
                                    endforeach;
                                endif;
                                ?>
                                </ul>
                            </td>
                            <td class="text-center">
                                <?php echo CHtml::link('<i class="glyphicon glyphicon-pencil"></i>',
                                    array('schoolslist', 'id'=>$ul->uid, 'type'=>$type),
                                    array('class'=>'J_dialog btn btn-info btn-xs', 'title'=>'分配校园：'.$ul->getName()));?>
                                <?php echo CHtml::link('<i class="glyphicon glyphicon-remove"></i>',
                                    array('deleteMult', 'type'=>$type, 'uid'=>$ul->uid),
                                    array('class'=>'J_ajax_del btn btn-danger btn-xs', 'title'=>Yii::t('global', 'Delete')));?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                    </tbody>
                </table>
        </div>
    </div>

</div>