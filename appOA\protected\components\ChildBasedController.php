<?php
/**
 * 对某一个孩子操作的Controller基类，统一实现权限控制
 */
class ChildBasedController extends ProtectedController
{
	public $childId = 0;
	public $childObj = null;
	//public $layout = '//layouts/childBased';
	public $returnAction = '//child/message/error';
	
	public function init(){
		parent::init();
		$this->multipleBranch = $this->accessMultipleBranches();
	}
	
	public function beforeAction($action){
        Yii::import('common.models.invoice.ChildReserve');

		$this->childId = Yii::app()->request->getParam('childid',null);
		$this->childObj = ChildProfileBasic::model()->with('stat.classInfo')->findByPk($this->childId);
		if(empty($this->childObj)){
			$this->forward($this->returnAction);
		}
		
		$childBranchId = $this->childObj->schoolid;
		$nextBranchId = $this->childObj->nextYear->schoolid;
		if($this->multipleBranch){
			if( !empty($childBranchId) && !$this->checkBranchAccess($childBranchId) ){
				if($nextBranchId == $this->staff->profile->branch){
					return true;
				}			
				$this->forward($this->returnAction);
			}
		}else{
			if($nextBranchId == $this->staff->profile->branch){
				return true;
			}
			if($childBranchId != $this->staff->profile->branch){
				$this->forward($this->returnAction);
			}
		}
		return true;
	}
	
	public function checkBranchAccess($branchId){
		//代码
		$resultAccess = false;
		if (!empty($branchId))
		{
			if(in_array($branchId, $this->accessBranch)){
				$resultAccess = true;
			}
		}
		return $resultAccess;
	}
	
	public function accessMultipleBranches(){
		//TODO: 需要加入判断该用户是否在可以管理多所校园的用户名单里
		$officeBranch = CommonUtils::LoadConfig('CfgOffice');
		$myBranch = $this->staff->profile->branch;
		if( in_array($myBranch, $officeBranch) ||  $this->staff->admtype )
		{
			$adminBranch = AdmBranchLink::model()->getMultipleBranches(Yii::app()->user->getId());
			if (in_array($myBranch, $officeBranch))
			{
				$this->accessBranch = array_keys($this->getAllBranch(array(10, 99)));
				$this->adminBranch = $adminBranch;
			}
			else 
			{
				$this->accessBranch = $adminBranch;
				$this->adminBranch = $adminBranch;
			}
			return true;
		}
		else
			return false;
	}
}