<?php

class WeChatBasedController extends Controller
{
	protected $token = 'I34vyOn92L456in13e';
	protected $securityKey = 'A23sW343eL8934ov2332E';
	
	public $layout = '//layouts/main';
	public $wechatObj = null;
	public $userMsg = null;
	public $wechatUser = null;
	public $isBound = 0;
	public $myChildObjs = array();
	public $activeChildIds = array();
	public $campusId = '';
	public $branchList = null;
	
	const STAT_BOUND = 10; //已经绑定
	const STAT_BOUND_EXPIRED = 2; //绑定过，当已取消绑定
	const STAT_BOUND_NONE = 0; //没有绑定
	
	public function init(){
		$this->siteTitle = Yii::t("global","IvyOnline");
		Yii::app()->theme = 'mobile';
		$this->wechatObj = new WXCallBack($this->token);
		$this->wechatObj->parseMsg();
		$this->userMsg = $this->wechatObj->getUserMsg();
		$this->wechatUser = WechatUser::model()->findByPk($this->userMsg->FromUserName);
		if(is_null($this->wechatUser)){
			$this->isBound = self::STAT_BOUND_NONE;
		}else{
			if($this->wechatUser->valid && $this->wechatUser->userid)
				$this->isBound = self::STAT_BOUND;
			else
				$this->isBound = self::STAT_BOUND_EXPIRED;
		}
		
		if( $this->isBound == self::STAT_BOUND ){
			$parent = IvyParent::model()->findByPk($this->wechatUser->userid);
			if (!empty($parent) && count(@unserialize($parent->childs))) {
				$cids = @unserialize($parent->childs);
				$this->myChildObjs = ChildProfileBasic::model()->findAllByPk($cids, array("index" => "childid","order"=>"childid DESC"));
				
				foreach($this->myChildObjs as $child){
					if($child->status < 100){
						$this->activeChildIds[] = $child->childid;
						if(!empty($child->schoolid)){
							$this->campusId = $child->schoolid;
						}
					}
				}
				//孩子id从大到小排列
				$this->branchList = Branch::model()->getBranchList(null, true);
				rsort($this->activeChildIds);
			}
		}
	}
}