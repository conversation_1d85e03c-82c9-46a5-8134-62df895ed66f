<div id='homeAddress'>
    <div class="progress">
        <div class="progress-bar progress-bar-success" :style="'width:'+ parsefloat(filled/total) +'%;'">
            <span v-if='filled!=0' :title="filled+'/'+total">{{parsefloat(filled/total)}}% ({{filled}}/{{total}})</span>
            <span v-else>0% ({{filled}}/{{total}})</span>
        </div>
<!--        <div class="progress-bar progress-bar-warning progress-bar-striped" style="'width:0%">-->
<!--            <span >0% (0/--><?php //echo $data['count']?><!--)</span>-->
<!--        </div>-->
<!--        <div class="progress-bar progress-bar-danger" :style="'width:'+ parsefloat(unfilled/total) +'%;'">-->
<!--            <span v-if='unfilled!=0' :title="unfilled+'/'+total">{{parsefloat(unfilled/total)}}% ({{unfilled}}/{{total}})</span>-->
<!--            <span v-else>0% ({{unfilled}}/{{total}})</span>-->
<!--        </div>-->
    </div>
    <div class='pull-left' id="nameSearchComp"></div>
    <p class='pull-right'>
        <button type="button" class="btn btn-default btn-sm"  v-if='type=="filled"' :disabled='btnCon' @click='batchPass("modal")'>批量通过</button>
        <button type="button" class="btn btn-default btn-sm ml10"  @click='exportTab()'>导出表格</button>
    </p>
    <div class='clearfix'></div>
    <div class='relative'>
        <div class='loading' v-show='loading'>
            <span></span>
        </div>
        <ul class="nav nav-tabs" role="tablist">
            <li role="presentation" class="active"><a href="#home" aria-controls="home" role="tab" data-toggle="tab"  @click='getInfo("filled")'>已填写 <span class="badge">{{filled}}</span></a></li>
            <li role="presentation"><a href="#unstart" aria-controls="unstart" role="tab" data-toggle="tab" @click='getInfo("unfilled")'>未填写 <span class="badge badge-error">{{unfilled}}</span></a></li>
        </ul>
        <div class="tab-content">
            <div role="tabpanel" class="tab-pane active" id="home">
                <table class="table table-bordered mt20" id='filled' v-show='list.length!=0'>
                    <thead>
                    <tr>
                        <th width='30' class="nosort">
                            <input type="checkbox" id="inlineCheckbox1" value="option1"  v-model='all'  @change='checkAll($event)'>
                        </th>
                        <th width='150'><?php echo Yii::t('global', 'Name') ?></th>
                        <th width='200'><?php echo Yii::t('labels', 'Class') ?></th>
                        <th width='150'><?php echo Yii::t('labels', 'Date of Birth') ?></th>
                        <th width='150'><?php echo Yii::t('labels', 'Status') ?></th>
                        <th width='150'>提交时间</th>
                        <th width='80' class="nosort"><?php echo Yii::t('global', 'Action') ?></th>
                    </tr>
                    </thead>
                    <tbody v-if='isShow'>
                        <tr v-for='(item,index) in list'>
                            <th>
                                <input type="checkbox" id="inlineCheckbox1" :value="item.childid" :disabled='item.step_status==4?true:false' v-model='child_ids' @change='childCheck($event)'>
                            </th>
                            <td >
                                <a href="javascript:;"  @click="overalls(item.childid)">
                                    {{item.name}}
                                </a>
                            </td>
                            <td > {{item.class_name}}</td>
                            <td >{{item.birthday}}</td>
                            <td >
                                <i class='glyphicon glyphicon-remove-sign' v-if='item.step_status==3' style='color:#D5514E'></i>
                                <i class='glyphicon glyphicon-ok-sign'  v-if='item.step_status==4' style='color:#5cb85c'></i>
                                <i class='glyphicon glyphicon-registration-mark'  v-if='item.step_status==1' style='color:#f0ad4e'></i>
                                <i class='glyphicon glyphicon-question-sign'  v-if='item.step_status==0' style='color:#f0ad4e'></i>    
                            {{statusList[item.step_status]}}</td>
                            
                            <td >{{item.step_data.submitted_at}} </td>
                            <td >
                                <a class="btn btn-primary btn-xs" href='javascript:;' @click='auditList(index,"no")'><?php echo Yii::t('global', 'View Detail') ?></a>
<!--                              <button type="button" class="btn btn-primary ml10" @click='printMedical(index,item)'>打印</button>-->
                                <a  class="btn btn-primary btn-xs" :href="'<?php echo $this->createUrlReg('PrintDetail', array('branchId' => $this->branchId,'coll_id'=>$coll_id)); ?>&childid='+item.childid+'&step_id=medical'" target="_blank">
                                    <?php echo Yii::t('global', 'Print') ?>
                                </a>
                            </td>
                        </tr>
                    </tbody>
                </table>
                
                <div class="mt15" v-show='list.length==0'>
                    <div class="alert alert-warning" role="alert"><?php echo Yii::t('ptc', 'No Data') ?></div>
                </div>
            </div>
            <!-- 未填写的数据 -->
            <div role="tabpanel" class="tab-pane " id="unstart">    
                <table class="table table-bordered mt20" id='unfilled' v-show='unList.length!=0'>
                    <thead>
                    <tr>
                        <th><?php echo Yii::t('global', 'Name') ?></th>
                        <th><?php echo Yii::t('labels', 'Class') ?></th>
                        <th><?php echo Yii::t('labels', 'Date of Birth') ?></th>
                        <th><?php echo Yii::t('labels', 'Status') ?></th>
                    </tr>
                    </thead>
                    <tbody  v-if='isShow'>
                    <tr v-for='(item,index) in unList'>
                            <td >
                                <a href="javascript:;"  @click="overalls(item.childid)">
                                    {{item.name}}
                                </a>
                            </td>
                            <td > {{item.class_name}}</td>
                            <td >{{item.birthday}}</td>
                            <td >{{statusList[item.step_status]}}</td>
                        </tr>
                    </tbody>
                </table>
                
                <div class="mt15" v-show='unList.length==0'>
                    <div class="alert alert-warning" role="alert"><?php echo Yii::t('ptc', 'No Data') ?></div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" tabindex="-1" role="dialog" id='auditDialog' data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title"><?php echo Yii::t('global', 'View Detail') ?></h4>
            </div>
            <div class="modal-body" v-if='Object.keys(rejectionList).length != 0'>
                <div class="media col-md-12 ">
                    <div class="media-left pull-left media-middle">
                        <a href="javascript:void(0)">
                            <img :src="rejectionList.avatar" data-holder-rendered="true" class="media-object img-circle avatarImage">
                        </a>
                    </div> 
                    <div class="media-body pt10 media-middle">
                        <h4 class="media-heading font14">{{rejectionList.name}}</h4> 
                        <div class="text-muted">{{rejectionList.class_name}}</div>
                    </div>
                </div>  
                <div class='clearfix'></div>
                <div class='mt20 mb20'>
                    <div class='col-md-6 mt10 color6'><span class='color3 mr10'><?php echo Yii::t('reg', '个人医疗/意外保险信息 （没有请填无）') ?>：</span>{{rejectionList.step_data.insurance_companies}}</div> 
                    <div class='col-md-12 mt20 mb20'>
                        <p><span class='color3 mr10'><?php echo Yii::t('reg', '请上传保险卡正反面照片') ?> ：</p> 
                        <img v-for='(list,index) in rejectionList.step_data.insurance_card_photos'  @click='bigImg(list)'  :src="list" alt="" class='stepUpImg'>
                    </div>
                    <div  class='col-md-12 mt10 color3'><strong><?php echo Yii::t('reg', 'Preferred Hospital') ?></strong></div>
                    <div class='col-md-6 mt10 color6' v-for='(list,index) in rejectionList.step_data.intended_hospitals'><span class='color3 mr10'> <?php echo Yii::t('reg', 'Preferred Hospital') ?>{{index+1}}：</span>{{list}}</div> 
                    <!-- <div  class='col-md-12 mt10 color3'><strong><?php echo Yii::t('site', 'Emergency Contact') ?></strong></div> -->
                     <div class='col-md-12 mt20 mb10'>
                        <p><?php echo Yii::t('site', 'Emergency Contact') ?>：</p>
                        <div class="col-md-12 mt10 mb10" v-for="(item,index) in rejectionList.step_data.emergency_contact" :key="index">
                            <p>姓名： <span>{{item.name}}</span></p>
                            <p>手机号：<span>{{item.mobile}}</span></p>
                            <p>邮箱：<span>{{item.email}}</span></p>
                        </div>
                    </div>
                    <div class='col-md-6 mt10 color6'><span class='color3 mr10'><?php echo Yii::t('reg', 'ADD/ADHD') ?>：</span>{{rejectionList.step_data.is_adhd==1?'是':'否'}}</div> 
                    <div class='col-md-6 mt10 color6'><span class='color3 mr10'><?php echo Yii::t('reg', 'Heart Disorder') ?>：</span>{{rejectionList.step_data.is_heart_disease==1?'是':'否'}}</div> 
                    <div class='col-md-6 mt10 color6'><span class='color3 mr10'><?php echo Yii::t('reg', 'Tuberculosis') ?>：</span>{{rejectionList.step_data.tuberculosisFour==1?'是':'否'}}</div> 
                    <div class='col-md-6 mt10 color6'><span class='color3 mr10'><?php echo Yii::t('reg', '过敏') ?>：</span>{{rejectionList.step_data.allergies==1?'是':'否'}}</div> 
                    <div class='col-md-6 mt10 color6' v-if='rejectionList.step_data.allergies==1'><span class='color3 mr10'><?php echo Yii::t('reg', '食物过敏') ?>：</span>{{rejectionList.step_data.allergies_food}}</div>
                    <div class='col-md-6 mt10 color6' v-if='rejectionList.step_data.allergies==1'><span class='color3 mr10'><?php echo Yii::t('reg', '药物过敏') ?>：</span>{{rejectionList.step_data.allergies_medication}}</div>
                    <div class='col-md-6 mt10 color6' v-if='rejectionList.step_data.allergies==1'><span class='color3 mr10'><?php echo Yii::t('reg', '其他过敏') ?>：</span>{{rejectionList.step_data.allergies_other}}</div>
                    <div class='col-md-6 mt10 color6'><span class='color3 mr10'><?php echo Yii::t('reg', 'Other Illnesses (please specify)') ?>：</span>{{rejectionList.step_data.other}}</div> 
                </div>
                <div class='clearfix'></div>
                <div class='col-md-12 mt20 mb20'>
                    <p><span class='color3 mr10'><?php echo Yii::t('reg', 'Physical Examination Report') ?> ：</p> 
                    <img v-for='(list,index) in rejectionList.step_data.examination_reports'  @click='bigImg(list)'  :src="list" alt="" class='stepUpImg'>
                </div>
                <div class='col-md-12 mt20 mb20'>
                    <p><?php echo Yii::t('reg', 'A Copy of the Vaccination Record') ?>：</p>
                     <img v-for='(list,index) in rejectionList.step_data.vaccines_reports'  @click='bigImg(list)' :src="list" alt="" class='stepUpImg'>
                </div>
                <div class='col-md-12 mt20 mb20' v-if='rejectionList.sign_url!=""'>
                    <p><?php echo Yii::t('event', 'Signature') ?>：</p> 
                    <img :src="rejectionList.sign_url" alt="" class='signImgDetails'>
                </div>
                <div class='clearfix'></div>    
                <div class="panel panel-default mt20">
                    <div class="panel-heading">
                        <h3 class="panel-title"><?php echo Yii::t('reg', '审核') ?></h3>
                    </div>
                    <div class="panel-body">
                        <div class='col-md-12 col-sm-12 font14'>
                            <div class='col-md-6 col-sm-6 borderRight scroll-box' style='max-height:250px;overflow-y:auto'>
                                <p><strong>当前审核状态</strong></p>
                                
                                <div>
                                    {{statusList[rejectionList.step_status]}}
                                    <button type="button" class="btn btn-primary  btn-xs pull-right" @click='resetStatus()'  v-if='rejectionList.step_status==3 || rejectionList.step_status==4'><?php echo Yii::t('global', 'Reset') ?></button>
                                </div>
                                <p class='mt10'><strong>审核记录</strong></p>
                                <div>
                                    <div v-for='(list,index) in rejectionList.audit_log' class='ml10' style='border-left:1px dashed #ccc'>
                                    <p style='margin-left:-7px;background: #fff;' >
                                        <span v-if='list.status==4'>
                                        <span class='glyphicon glyphicon-ok-sign greenIcon'></span><span> <?php echo Yii::t('global', 'Confirmed') ?></span> 
                                        </span>
                                        <span v-if='list.status==3'>
                                        <span class='glyphicon glyphicon-info-sign redIcon'></span> <span> <?php echo Yii::t('reg', 'Rejected') ?></span>
                                        </span>
                                        <span v-if='list.status==0'>
                                        <span class='glyphicon glyphicon-question-sign yellowIcon'></span> <span> <?php echo Yii::t('global', 'Reset') ?></span>
                                        </span>
                                    </p> 
                                    <p class='ml10' style='background:#F7F7F8;line-height:22px'  v-if='list.comment!="" && list.comment!=null'><?php echo Yii::t('reg', '备注：') ?>{{list.comment}}</p>
                                    <p class='pl10 font12'>{{list.date}} {{list.user}}</p>
                                    </div>
                                </div>
                            </div>
                            <div class='col-md-6 col-sm-6' >
                                <p><strong>审核</strong></p>
                                <div class='p10 grey'>
                                    <label class="radio ml20">
                                        <input type="radio" id="inlineradio1" v-model='audit_status' value="4"> <?php echo Yii::t('labels', 'Yes') ?>
                                    </label>
                                    <label class="radio ml20">
                                        <input type="radio" id="inlineradio2" v-model='audit_status' value="3"> 未通过
                                    </label>
                                    <textarea class="form-control" rows="3" placeholder='请输入备注' v-model='comment'></textarea>
                                    <div v-if='audit_status==3 || type_status==2'>
                                        <p class='mt10'>修改建议将直接显示在家长端，请准确措辞。</p>   
                                        <div class="checkbox">
                                            <label>
                                                <input type="checkbox"  v-model='send_wechat'>将审核结果发送至家长微信
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class='mt20'>
                                    <button type="button"  class="btn btn-primary pull-right" :disabled='btnCon' @click='saveInfo()'><?php echo Yii::t("newDS", "确认");?></button>
                                    <button type="button" class="btn btn-default  pull-right mr10" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class='clearfix'></div>
                <div class="panel panel-default mt20">
                    <div class="panel-heading">
                        <h3 class="panel-title"><?php echo Yii::t('reg', 'Summary of Nurse Review Status') ?></h3>
                    </div>
                    <div class="panel-body">
                        <div>
                            <div><label for=""><?php echo Yii::t('reg', 'Entrance Physical Examination') ?></label></div>
                            <label class="radio-inline" >
                                <input type="radio" name="physicalExamination" v-model='nurse.physicalExamination' value="2"> <?php echo Yii::t('reg', 'Submitted') ?>
                            </label>
                            <label class="radio-inline">
                                <input type="radio" name="physicalExamination" v-model='nurse.physicalExamination' value="1"> <?php echo Yii::t('reg', 'Not submitted') ?>
                            </label>
                            <input type="text" class="form-control mt10"  placeholder="请输入备注信息" v-model='nurse.physicalExaminationDetail'>
                        </div>
                        <div class='mt20'> 
                            <div><label for=""><?php echo Yii::t('reg', 'Long-term Medication Needs') ?></label></div>
                            <label class="radio-inline" v-for='(list,key,i) in common'>
                                <input type="radio" name="longMedication" v-model='nurse.longMedication' :value="key"> {{list}}
                            </label>
                            <input type="text" class="form-control mt10"  placeholder="请输入备注信息" v-model='nurse.longMedicationDetail'>
                        </div>
                        <div class='mt20'>
                            <div><label for=""><?php echo Yii::t('reg', 'Medical History') ?></label></div>
                            <label class="radio-inline" v-for='(list,key,i) in common'>
                                <input type="radio" name="medicalHistory" v-model='nurse.medicalHistory' :value="key"> {{list}}
                            </label>
                            <input type="text" class="form-control mt10"  placeholder="请输入备注信息"   v-model='nurse.medicalHistoryDetail'>
                        </div>
                        <div class='mt20'>
                            <div><label for=""><?php echo Yii::t('reg', 'Allergy') ?></label></div>
                            <label class="radio-inline" v-for='(list,key,i) in common'>
                                <input type="radio" name="allergy" v-model='nurse.allergy' :value="key"> {{list}}
                            </label>
                            <div v-if='nurse.allergy==2' >
                                <div class='p10 grey mt10'>
                                    <div class="checkbox">
                                        <label>
                                            <input type="checkbox" name="foodAllergy" v-model='nurse.foodAllergy'> <?php echo Yii::t('reg', 'Food Allergy') ?>
                                        </label>
                                    </div>
                                    <input type="text" class="form-control mt10"  placeholder="请输入备注信息" v-if='nurse.foodAllergy' v-model='nurse.foodAllergyDetail'>

                                    <div class="checkbox">
                                        <label>
                                            <input type="checkbox" name="otherAllergy" v-model='nurse.otherAllergy' > <?php echo Yii::t('reg', 'Other Allergy') ?>
                                        </label>
                                    </div>   
                                    <input type="text" class="form-control mt10"  placeholder="请输入备注信息" v-if='nurse.otherAllergy' v-model='nurse.otherAllergyDetail'>

                                    <div class="checkbox">
                                        <label>
                                            <input type="checkbox" name="medAllergy" v-model='nurse.medAllergy' > <?php echo Yii::t('reg', 'Medical Allergy') ?>
                                        </label>
                                    </div>   
                                    <input type="text" class="form-control mt10"  placeholder="请输入备注信息" v-if='nurse.medAllergy' v-model='nurse.medAllergyDetail'>
                                </div>
                                
                            </div>
                        </div>
                        <div class='mt20'>
                            <div><label for=""><?php echo Yii::t('reg', 'Vaccination') ?></label></div>
                            <label class="checkbox-inline" v-for='(list,key,i) in immunizationList'>
                                <input type="checkbox" id="inlineCheckbox1" :value="key" v-model='nurse.immunization'> {{list}}
                            </label>
                            
                            <input type="text" class="form-control mt10"  placeholder="请输入备注信息" v-model='nurse.immunizationDetail'>
                        </div>
                        <div class='mt20'>
                            <div><label for=""><?php echo Yii::t('reg', 'COVID-19 Vaccine') ?></label></div>
                            <label class="radio-inline">
                                <input type="radio" name="inlineRadioOptions"  value="1" v-model='nurse.isCOVID'> <?php echo Yii::t('reg', 'Vaccinated') ?>
                            </label>
                            <label class="radio-inline">
                                <input type="radio" name="inlineRadioOptions"  value="2"  v-model='nurse.isCOVID'> <?php echo Yii::t('reg', 'Not Vaccinated') ?>
                            </label>
                            <div class='p10 grey mt10' v-if='nurse.isCOVID==1'>
                                <div>
                                    <button type="button" class="btn btn-primary" @click='addCOVID'>
                                        <span class="glyphicon glyphicon-plus" aria-hidden="true"></span> <?php echo Yii::t('reg', 'Date of Vaccinations') ?>
                                    </button>
                                </div>
                                <div v-for='(list,i) in COVID' class='mt10'>
                                    <span><?php echo Yii::t("reg", " ");?>{{i+1}}<?php echo Yii::t("reg", "shot");?>：</span> 
                                    <input type="text" class="form-control select_2 inline-block mr10 ml0"  :id="'COVID_' + i" v-model='list.date'  placeholder="<?php echo Yii::t("newDS", "Select a date"); ?>"  :value='list.date'>
                                    <span class='glyphicon glyphicon-trash ml20' @click='delCOVID(i)'></span>
                                </div>
                            </div>
                            <input type="text" class="form-control mt10"  placeholder="请输入备注信息" v-model='nurse.COVIDDetail'>
                        </div>
                        <div class='mt20'>
                            <div><label for=""><?php echo Yii::t('reg', 'Other') ?></label></div>
                            <textarea class="form-control" rows="3" v-model='nurse.otherDetail'></textarea>
                        </div>                        <div class='mt20'>
                            <button type="button"  class="btn btn-primary pull-right ml10" :disabled='btnCon' @click='saveRemarks()'><?php echo Yii::t("reg", "Submit");?></button>
                            <button type="button" class="btn btn-default  pull-right" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>
    </div>
    <div class="modal fade" id="stuDetails" tabindex="-1" role="dialog" >
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" ><?php echo Yii::t('attends', '整体情况') ?> <span id="remarks_t"></span></h4>
                </div>
                <div class="modal-body color6" v-if='childData.child'>
                    <p><strong class='font14 color3'>学生资料</strong></p>
                    <div class='flex'>
                        <div style='width:80px'><img :src="childData.child.avatar" class='stuImg'></div>
                        <div class='flex1'>
                            <p class='mt10'><strong class='font14 color3 studentName'>{{childData.child.name}}</strong> </p>
                            <p class='mb20'>{{childData.child.class_name}}</p>
                            <div  v-if='childData.parents.finfo!=null'>
                                <div class='col-md-4'>
                                <strong class='color3'><?php echo Yii::t('global', 'Father') ?>：</strong>  
                                    <span class='glyphicon glyphicon-user'></span> {{childData.parents.finfo.name}}
                                </div>
                                <div class='col-md-4'>
                                    <span class='glyphicon glyphicon-phone'></span> {{childData.parents.finfo.mphone}}
                                </div>
                                <div class='col-md-4'>
                                    <span class='glyphicon glyphicon-envelope'></span> {{childData.parents.finfo.email}}
                                </div>
                            </div>
                            <div class='clearfix'></div>
                            <div class='mt10' v-if='childData.parents.minfo!=null'>
                                <div class='col-md-4'>
                                <strong class='color3'><?php echo Yii::t('global', 'Mother') ?>：</strong> 
                                    <span class='glyphicon glyphicon-user'></span> {{childData.parents.minfo.name}}
                                </div>
                                <div class='col-md-4'>
                                    <span class='glyphicon glyphicon-phone'></span> {{childData.parents.minfo.mphone}}
                                </div>
                                <div class='col-md-4'>
                                    <span class='glyphicon glyphicon-envelope'></span> {{childData.parents.minfo.email}}
                                </div>
                            </div>
                        </div>
                    </div>
                    <ul class="nav nav-wizard pb20 mt20">
                        <li v-for='(list,index) in childData.step_status'>
                            <a href="javascript::" class='color6' :data-tab="list.step_id" :data-status="list.status" :data-name="childData.child.name"><span class="number">{{index+1}}</span>{{list.title}}</a>
                            <p class='mt10 color6' >
                                <span :class="list.status==4?'greenStep':list.status==3?'redStep':list.status==1 || list.status==2?'orgStep':''">{{ statusList[list.status]}}</span></p>
                        </li>
                    </ul>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel') ?></button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="tipsModal" tabindex="-1" role="dialog" >
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" ><?php echo Yii::t('attends', '批量通过') ?> <span id="remarks_t"></span></h4>
                </div>
                <div class="modal-body color6" >
                    <div class='color3 font14'>确认批量通过审核吗？</div> 
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel') ?></button>
                    <button type="button" class="btn btn-primary" :disabled='btnTips' @click='batchPass()'><?php echo Yii::t('global', '确定') ?></button>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    
    var step_id = '<?php echo $step_id?>';
    var coll_id = '<?php echo $coll_id?>';
    var type = '<?php echo $type?>';
    var type_status = '<?php echo $type_status?>';
    var  registrations = new Vue({
        el: "#homeAddress",
        data: {
            type_status:type_status,
            list:[],
            unfilled:0,
            coll_id:coll_id,
            filled:0,
            total:0,
            type:type,
            logList:[],
            statusList:[],
            audit_status:'',
            comment:'',
            child_id:'',
            childData:{},
            child_ids:[],
            rejectionList:{},
            unList:{},
            btnCon:false,
            send_wechat:true,
            isShow:true,
            nurse:{},
            common:{
                1:'<?php echo Yii::t('reg', 'No') ?>',
                2:'<?php echo Yii::t('reg', 'Yes') ?>',
            },
            immunizationList:{
                1:'<?php echo Yii::t('reg', 'G4 A+C Completed') ?>',
                2:'<?php echo Yii::t('reg', 'G7 Hepatitis B Completed') ?>',
                3:'<?php echo Yii::t('reg', 'Pre-7 Years Vaccines Completed') ?>',
                4:'<?php echo Yii::t('reg', 'Vaccines Need To Be Tracking') ?>',
            },
            resetStatusIndex:'',
            loading:false,
            all:false,
            btnTips:false,
            COVID:[],
            
        },
        created: function() {
            this.getInfo(this.type)
            eventBus.$on('listChanged', list => {
                if(sessionStorage.getItem('tabType')  === 'filled'){
                    this.list = list;
                }else{
                    this.unList = list
                }
            });
        },
        watch: {
        },
        methods: {
            addCOVID(){
                this.COVID.push({
                    date:''
                })
                let that=this
                this.$nextTick(()=>{
                    for(var i=0;i<this.COVID.length;i++){
                        $('#COVID_'+i).datepicker({
                            dateFormat: 'yy-mm-dd',
                            selectOtherMonths: false,
                            showOtherMonths: false,
                            changeMonth: true,
                            changeYear: true,
                            firstDay: 0,
                            onSelect: function (date,str) {
                                console.log(i)
                                console.log(that.COVID[i-1])
                                let id=str.id
                                that.COVID[i-1].date=date
                            }
                        });
                    }
                })
            },
            delCOVID(index){
                this.COVID.splice(index, 1); 
            },
            overalls(id){
                let that=this
                $.ajax({
                    url:'<?php echo $this->createUrlReg('getStudentOverall'); ?>',
                    type: 'get',
                    dataType:'json',
                    data:{
                        child_id:id,
                        coll_id:coll_id,
                    },
                    success:function(data){
                        if(data.state=="success"){
                            that.childData=data.data
                            $('#stuDetails').modal('show')
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            parsefloat(num) {
                return Number(num * 100).toFixed(2);
            },
            getInfo(type){
                let that = this
                this.type=type
                this.isShow=false
                $.ajax({
                    url:'<?php echo $this->createUrlReg('stepTemplateInfo'); ?>',
                    type: 'get',
                    dataType:'json',
                    data:{
                        coll_id:coll_id,
                        step_id:step_id,
                        type:type
                    },
                    success:function(data){
                        if(data.state=="success"){
                            if(type=='unfilled'){
                                that.unList = data.data.list
                            }else{
                                that.list = data.data.list
                            }
                            that.unfilled = data.data.unfilled
                            that.filled = data.data.filled
                            that.total = data.data.count
                            that.statusList=data.data.statusList
                            that.logList=data.data.logList
                            that.isShow=true
                            if(that.resetStatusIndex!==''){
                                that.auditList(that.resetStatusIndex)
                            }
                            sessionStorage.setItem('tabType', type);
                            sessionStorage.setItem('listUpdated',  JSON.stringify(data.data.list));
                            that.$forceUpdate();
                            that.$nextTick(()=>{
                                that.DataTable(data)
                            })
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            DataTable(data){
                let that=this
                    that.$nextTick(()=>{
                        $('#stuDetails').modal('hide');
                        $('#nameSearchComp').html(data.data.nameSearchComp)
                        if(that.type=='filled'){
                            var table = $('#filled').DataTable({
                                aaSorting: [5, 'desc'], // 默认排序
                                paging: false,
                                info: false,
                                searching: false,
                                "destroy": true,
                                columnDefs: [ {
                                    "targets": 'nosort',
                                    "orderable": false
                                } ],
                            });
                        }else{
                            var table = $('#unfilled').DataTable({
                                aaSorting: [1, 'desc'], // 默认排序
                                paging: false,
                                info: false,
                                "destroy": true,
                                searching: false,
                            });
                        }                    
                    })  
               
            },
            auditList(index,type){
                this.resetStatusIndex=index
                this.rejectionList=this.list[index]
                if(this.list[index].step_data.remarks && this.list[index].step_data.remarks.length!=0){
                    this.nurse=this.list[index].step_data.remarks
                    if(this.nurse.isCOVID==1 && this.nurse.COVID){
                        this.COVID=this.nurse.COVID
                    }
                    if(this.nurse.immunization==undefined || !Array.isArray(this.nurse.immunization)){
                        this.nurse.immunization=[]
                    }
                }else{
                    this.nurse={
                        longMedication:0,
                        longMedicationDetail:'',
                        medicalHistory:0,
                        medicalHistoryDetail:'',
                        allergy:0,
                        otherAllergy:false,
                        medAllergy:false,
                        foodAllergy:false,
                        otherAllergyDetail:'',
                        medAllergyDetail:'',
                        foodAllergyDetail:'',
                        immunization:[],
                        immunizationDetail:'',
                        physicalExamination:'',
                        physicalExaminationDetail:'',
                        COVIDDetail:'',
                        COVID:[],
                        isCOVID:'',
                        otherDetail:''
                    }
                    this.COVID=[]
                }
                this.child_id=this.list[index].childid
                this.audit_status=''
                this.comment=''
                let that=this
                if(type){
                    $('#auditDialog').modal('show')       
                    if(this.COVID.length>0){
                        this.$nextTick(()=>{
                            for(var i=0;i<this.COVID.length;i++){
                                $('#COVID_'+i).datepicker({
                                    dateFormat: 'yy-mm-dd',
                                    selectOtherMonths: false,
                                    showOtherMonths: false,
                                    changeMonth: true,
                                    changeYear: true,
                                    firstDay: 0,
                                    onSelect: function (date,str) {
                                        console.log(i)
                                        console.log(that.COVID[i-1])
                                        let id=str.id
                                        that.COVID[i-1].date=date
                                    }
                                });
                            }
                        })
                    }             
                    
                }
                
            },
            exportTab() {
                if(this.type=='filled'){
                    const filename ='医疗信息已填写.xlsx';
                    const ws_name = "SheetJS";
                    var exportDatas = [];
                    for(var i=0;i<this.list.length;i++){
                        var contact=''
                        for(var j=0;j<this.list[i].step_data.emergency_contact.length;j++){
                            contact+='联系人'+(j+1) + '：（姓名：'+this.list[i].step_data.emergency_contact[j].name+'电话：'+this.list[i].step_data.emergency_contact[j].mobile+'邮箱：'+this.list[i].step_data.emergency_contact[j].email+'）'
                        }
                        var data={
                            'ID':this.list[i].childid,
                            '<?php echo Yii::t('global', 'Name') ?>':this.list[i].name,
                            "<?php echo Yii::t('labels', 'Class') ?>":this.list[i].className,
                            "<?php echo Yii::t('labels', 'Date of Birth') ?>":this.list[i].birthday,
                            "<?php echo Yii::t('labels', 'Status') ?>":this.statusList[this.list[i].step_status],

                            "<?php echo Yii::t('labels', 'Barcode') ?>":this.list[i].barcode,
                            "<?php echo Yii::t('labels', 'Educational Student ID') ?>":this.list[i].educational_id,
                            "<?php echo Yii::t("labels",'中文法定名')?>": this.list[i].is_legel_cn_name==1 ? '是':'否',
                            "<?php echo Yii::t("labels",'Chinese Name')?>":this.list[i].cn_name,
                            "<?php echo Yii::t("labels",'Legal First Name')?>":this.list[i].first_name_en,
                            "<?php echo Yii::t("labels",'Legal Middle Name')?>":this.list[i].middle_name_en,
                            "<?php echo Yii::t("labels",'Legal Last Name')?>":this.list[i].last_name_en,
                            "<?php echo Yii::t("labels",'Preferred Name')?>":this.list[i].nick,
                            "<?php echo Yii::t("labels",'Gender')?>":this.list[i].gender == 1 ? '男' : '女',
                            "<?php echo Yii::t("labels",'Nationality')?>":this.list[i].country,
                            "<?php echo Yii::t("labels",'Language')?>":this.list[i].lang,
                            "<?php echo Yii::t("labels",'Father Name')?>":this.list[i].father_name,
                            "<?php echo Yii::t("labels",'Father Email')?>":this.list[i].father_email,
                            "<?php echo Yii::t("labels",'Father Mobile')?>":this.list[i].father_phone,
                            "<?php echo Yii::t("labels",'Mother Name')?>":this.list[i].mather_name,
                            "<?php echo Yii::t("labels",'Mother Email')?>":this.list[i].mather_email,
                            "<?php echo Yii::t("labels",'Mother Mobile')?>":this.list[i].mather_phone,
                            "入学日期":this.list[i].attend_school_time,
                            "<?php echo Yii::t("labels",'Dwelling Place')?>":this.list[i].address,
                            "<?php echo Yii::t("labels",'ID NO./Passport NO.')?>":this.list[i].identity,
                            "入学学年":this.list[i].first_startyear,

                            "提交时间":this.list[i].step_data.submitted_at,
                            "意向医院1":this.list[i].step_data.intended_hospitals[0],
                            "意向医院2":this.list[i].step_data.intended_hospitals[1],
                            "紧急联系人":contact,
                            "多动症":this.list[i].step_data.is_adhd==1?'是':'否',
                            "心脏病":this.list[i].step_data.is_heart_disease==1?'是':'否',
                            "结核":this.list[i].step_data.tuberculosisFour==1?'是':'否',
                            "过敏":this.list[i].step_data.allergies==1?'是':'否',
                            "食物过敏":this.list[i].step_data.allergies==1?this.list[i].step_data.allergies_food:'',
                            "药物过敏":this.list[i].step_data.allergies==1?this.list[i].step_data.allergies_medication:'',
                            "其他过敏":this.list[i].step_data.allergies==1?this.list[i].step_data.allergies_other:'',
                            "其他说明":this.list[i].step_data.other,
                            "护士审核":this.list[i].step_data.remarks!=null?'是':'否',
                        }
                        if(this.list[i].step_data.remarks!=null){
                            var immunization=''
                            if(this.list[i].step_data.remarks.immunization && this.list[i].step_data.remarks.immunization.length!=0){
                                for(var j=0;j<this.list[i].step_data.remarks.immunization.length;j++){
                                    immunization+=this.immunizationList[this.list[i].step_data.remarks.immunization[j]]+' '
                                }
                            }
                            var COVIDList=''
                            if(this.list[i].step_data.remarks.COVID && this.list[i].step_data.remarks.COVID.length!=0){
                                for(var k=0;k<this.list[i].step_data.remarks.COVID.length;k++){
                                    COVIDList+='第'+(k+1)+'针'+ this.list[i].step_data.remarks.COVID[k].date+' '
                                }
                            }
                            data=Object.assign(data,{
                                "入学体检":this.list[i].step_data.remarks.physicalExamination==2?'已提交':'未提交',
                                "入学体检备注":this.list[i].step_data.remarks.physicalExaminationDetail,
                                "长期用药需求":this.list[i].step_data.remarks.longMedication==2?'是':'否',
                                "长期用药需求备注":this.list[i].step_data.remarks.longMedicationDetail,
                                "既往病史":this.list[i].step_data.remarks.medicalHistory==2?'是':'否',
                                "既往病史备注":this.list[i].step_data.remarks.medicalHistoryDetail,
                                "过敏史":this.list[i].step_data.remarks.allergy==2?'是':'否',
                                "是否食物过敏":this.list[i].step_data.remarks.foodAllergy=='true'?'是':'否',
                                "食物过敏备注":this.list[i].step_data.remarks.foodAllergyDetail,
                                "其它过敏":this.list[i].step_data.remarks.otherAllergy=='true'?'是':'否',
                                "其它过敏备注":this.list[i].step_data.remarks.otherAllergyDetail,
                                "是否药物过敏":this.list[i].step_data.remarks.medAllergy=='true'?'是':'否',
                                "药物过敏备注":this.list[i].step_data.remarks.medAllergyDetail,
                                "疫苗":immunization,
                                "疫苗备注":this.list[i].step_data.remarks.immunizationDetail,
                                '新冠疫苗':COVIDList,
                                "新冠疫苗备注":this.list[i].step_data.remarks.COVIDDetail,
                                "其他备注":this.list[i].step_data.remarks.otherDetail,
                            })
                        }
                        exportDatas.push(data)
                    }
                    var wb=XLSX.utils.json_to_sheet(exportDatas,{
                        origin:'A1',// 从A1开始增加内容
                        header: ['ID',
                            '<?php echo Yii::t('global', 'Name') ?>',
                            '<?php echo Yii::t('labels', 'Class') ?>',
                            '<?php echo Yii::t('labels', 'Date of Birth') ?>',
                            '<?php echo Yii::t('labels', 'Status') ?>',
                            '<?php echo Yii::t('labels', 'Barcode') ?>',
                            '<?php echo Yii::t('labels', 'Educational Student ID') ?>',
                            '<?php echo Yii::t('labels','中文法定名')?>',
                            '<?php echo Yii::t('labels','Chinese Name')?>',
                            '<?php echo Yii::t('labels','Legal First Name')?>',
                            '<?php echo Yii::t('labels','Legal Middle Name')?>',
                            '<?php echo Yii::t('labels','Legal Last Name')?>',
                            '<?php echo Yii::t('labels','Preferred Name')?>',
                            '<?php echo Yii::t('labels','Gender')?>',
                            '<?php echo Yii::t('labels','Nationality')?>',
                            '<?php echo Yii::t('labels','Language')?>',
                            '<?php echo Yii::t('labels','Father Name')?>',
                            '<?php echo Yii::t('labels','Father Email')?>',
                            '<?php echo Yii::t('labels','Father Mobile')?>',
                            '<?php echo Yii::t('labels','Mother Name')?>',
                            '<?php echo Yii::t('labels','Mother Email')?>',
                            '<?php echo Yii::t('labels','Mother Mobile')?>',
                            '入学日期',
                            '<?php echo Yii::t('labels','Dwelling Place')?>',
                            '<?php echo Yii::t('labels','ID NO./Passport NO.')?>',
                            '入学学年',

                            '提交时间',
                            '意向医院1',
                            '意向医院2',
                            '紧急联系人',
                            '多动症',
                            '心脏病',
                            '结核',
                            '过敏',
                            '食物过敏',
                            '药物过敏',
                            '其他过敏',
                            '其他',
                            '其他说明',
                            '护士审核',
                            '入学体检',
                            '入学体检备注',
                            '长期用药需求',
                            '长期用药需求备注',
                            '既往病史',
                            '既往病史备注',
                            '过敏史',
                            '是否食物过敏',
                            '食物过敏备注',
                            '其它过敏',
                            '其它过敏备注',
                            '是否药物过敏',
                            '药物过敏备注',
                            '疫苗',
                            '疫苗备注',
                            '新冠疫苗',
                            '新冠疫苗备注',
                            '其他备注']
                    });
                    const workbook = XLSX.utils.book_new();
                    XLSX.utils.book_append_sheet(workbook, wb, ws_name);
                    const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                    const blob = new Blob([wbout], {type: 'application/octet-stream'});
                    let link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    link.download = filename;
                    link.click();
                    setTimeout(function() {
                        // 延时释放掉obj
                        URL.revokeObjectURL(link.href);
                        link.remove();
                    }, 500);
                }else{   
                    var title='医疗信息未填写.xlsx'
                    const ws_name = "SheetJS";
                    var exportDatas = [];
                    for(var i=0;i<this.unList.length;i++){
                        exportDatas.push(
                            {
                            'ID':this.unList[i].childid,
                            '<?php echo Yii::t('global', 'Name') ?>':this.unList[i].name,
                            "<?php echo Yii::t('labels', 'Class') ?>":this.unList[i].className,
                            "<?php echo Yii::t('labels', 'Date of Birth') ?>":this.unList[i].birthday,
                            "<?php echo Yii::t('labels', 'Status') ?>":this.statusList[this.unList[i].step_status],

                            "<?php echo Yii::t('labels', 'Barcode') ?>":this.unList[i].barcode,
                            "<?php echo Yii::t('labels', 'Educational Student ID') ?>":this.unList[i].educational_id,
                            "<?php echo Yii::t("labels",'中文法定名')?>": this.unList[i].is_legel_cn_name==1 ? '是':'否',
                            "<?php echo Yii::t("labels",'Chinese Name')?>":this.unList[i].cn_name,
                            "<?php echo Yii::t("labels",'Legal First Name')?>":this.unList[i].first_name_en,
                            "<?php echo Yii::t("labels",'Legal Middle Name')?>":this.unList[i].middle_name_en,
                            "<?php echo Yii::t("labels",'Legal Last Name')?>":this.unList[i].last_name_en,
                            "<?php echo Yii::t("labels",'Preferred Name')?>":this.unList[i].nick,
                            "<?php echo Yii::t("labels",'Gender')?>":this.unList[i].gender == 1 ? '男' : '女',
                            "<?php echo Yii::t("labels",'Nationality')?>":this.unList[i].country,
                            "<?php echo Yii::t("labels",'Language')?>":this.unList[i].lang,
                            "<?php echo Yii::t("labels",'Father Name')?>":this.unList[i].father_name,
                            "<?php echo Yii::t("labels",'Father Email')?>":this.unList[i].father_email,
                            "<?php echo Yii::t("labels",'Father Mobile')?>":this.unList[i].father_phone,
                            "<?php echo Yii::t("labels",'Mother Name')?>":this.unList[i].mather_name,
                            "<?php echo Yii::t("labels",'Mother Email')?>":this.unList[i].mather_email,
                            "<?php echo Yii::t("labels",'Mother Mobile')?>":this.unList[i].mather_phone,
                            "入学日期":this.unList[i].attend_school_time,
                            "<?php echo Yii::t("labels",'Dwelling Place')?>":this.unList[i].address,
                            "<?php echo Yii::t("labels",'ID NO./Passport NO.')?>":this.unList[i].identity,
                            "入学学年":this.unList[i].first_startyear,

                            });
                    }
                    var wb=XLSX.utils.json_to_sheet(exportDatas,{
                        origin:'A1',// 从A1开始增加内容
                        header: ['ID',
                            '<?php echo Yii::t('global', 'Name') ?>',
                            '<?php echo Yii::t('labels', 'Class') ?>',
                            '<?php echo Yii::t('labels', 'Date of Birth') ?>',
                            '<?php echo Yii::t('labels', 'Status') ?>',

                            '<?php echo Yii::t('labels', 'Barcode') ?>',
                            '<?php echo Yii::t('labels', 'Educational Student ID') ?>',
                            '<?php echo Yii::t('labels','中文法定名')?>',
                            '<?php echo Yii::t('labels','Chinese Name')?>',
                            '<?php echo Yii::t('labels','Legal First Name')?>',
                            '<?php echo Yii::t('labels','Legal Middle Name')?>',
                            '<?php echo Yii::t('labels','Legal Last Name')?>',
                            '<?php echo Yii::t('labels','Preferred Name')?>',
                            '<?php echo Yii::t('labels','Gender')?>',
                            '<?php echo Yii::t('labels','Nationality')?>',
                            '<?php echo Yii::t('labels','Language')?>',
                            '<?php echo Yii::t('labels','Father Name')?>',
                            '<?php echo Yii::t('labels','Father Email')?>',
                            '<?php echo Yii::t('labels','Father Mobile')?>',
                            '<?php echo Yii::t('labels','Mother Name')?>',
                            '<?php echo Yii::t('labels','Mother Email')?>',
                            '<?php echo Yii::t('labels','Mother Mobile')?>',
                            '入学日期',
                            '<?php echo Yii::t('labels','Dwelling Place')?>',
                            '<?php echo Yii::t('labels','ID NO./Passport NO.')?>',
                            '入学学年'
                        ],
                    });
                    const workbook = XLSX.utils.book_new();
                    XLSX.utils.book_append_sheet(workbook, wb, ws_name);
                    const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                    const blob = new Blob([wbout], {type: 'application/octet-stream'});
                    let link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    link.download = title;
                    link.click();
                    setTimeout(function() {
                        // 延时释放掉obj
                        URL.revokeObjectURL(link.href);
                        link.remove();
                    }, 500);
                }
                
            },
            checkAll(e){
                this.child_ids=[]
                if(e.target.checked){
                    for(var i=0;i<this.list.length;i++){
                        if(this.list[i].step_status!=4){
                            this.child_ids.push(this.list[i].childid)
                        }
                    }
                    this.all=true
                }else{
                    this.all=false
                }
            },
            childCheck(e){
                let checkListLen=this.list.filter((item)=>{return item.step_status!=4});
                if(e.target.checked){
                    if(this.child_ids.length==checkListLen.length){
                        this.all=true
                    }else{
                        this.all=false
                    }
                }else{
                    this.all=false
                }
            },
            batchPass(modal){
                let that = this
                if(this.child_ids.length==0){
                    resultTip({error: 'warning', msg: '请选择学生'});
                    return
                }
                if(modal){
                    $('#tipsModal').modal('show')
                    return
                }
                this.loading=true
                this.btnCon=true
                this.btnTips=true
                $.ajax({
                    url:'<?php echo $this->createUrlReg('batchPass'); ?>',
                    type: 'post',
                    dataType:'json',
                    data:{
                        child_ids:this.child_ids,
                        step_id:step_id,
                        coll_id:this.coll_id
                    },
                    success:function(data){
                        if(data.state=="success"){
                            resultTip({
								msg: data.state
							});
                            that.getInfo(that.type)
                            $('#auditDialog').modal('hide')
                            $('#tipsModal').modal('hide')
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                        that.btnCon=false
                        that.loading=false
                        that.btnTips=false
                    },
                    error:function(data){
                        that.btnCon=false
                        that.loading=false
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            resetStatus(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrlReg("stepAudit") ?>',
                    type: "get",
                    dataType: 'json',
                    data:{
                        child_id:this.child_id,
                        step_id:step_id,
                        audit_status:0, 
                        coll_id:this.coll_id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
								msg: data.state
							});
                            that.getInfo(that.type)
                            // $('#auditDialog').modal('hide')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                    },
                })                
            },
            saveInfo(){
                let that = this
                if(this.audit_status==''){
                    resultTip({
                        error: 'warning',
                        msg: '请选择审核状态'
                    });
                    return
                }
                that.btnCon=true
                var dataList={
                    child_id:this.child_id,
                    step_id:step_id,
                    audit_status:this.audit_status, 
                    coll_id:this.coll_id,
                    comment: this.comment,
                }
                if(this.audit_status==3 || this.type_status==2){
                   var dataList=Object.assign({send_wechat:that.send_wechat?1:0},dataList)
                }
                $.ajax({
                    url:'<?php echo $this->createUrlReg('stepAudit'); ?>',
                    type: 'get',
                    dataType:'json',
                    data:dataList,
                    success:function(data){
                        if(data.state=="success"){
                            resultTip({
								msg: data.state
							});
                            that.getInfo(that.type)
                            // $('#auditDialog').modal('hide')
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                        that.btnCon=false
                    },
                    error:function(data){
                        that.btnCon=false
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            bigImg(list){
                var opacityBottom = '<div class="opacityBottom" style = "display:none"><img class="bigImg" src="' + list + '"></div>';
                $(document.body).append(opacityBottom);
                this.toBigImg();//变大函数
            },
            toBigImg() {
                $(".opacityBottom").addClass("opacityBottom");//添加遮罩层
                $(".opacityBottom").show();
                $("html,body").addClass("none-scroll");//下层不可滑动
                $(".bigImg").addClass("bigImg");//添加图片样式
                $(".opacityBottom").click(function () {//点击关闭
                    $("html,body").removeClass("none-scroll");
                    $(".opacityBottom").remove();
                });
            },
            saveRemarks(){
                let that = this
                if(this.nurse.longMedication=='2' && (this.nurse.longMedicationDetail=='' || this.nurse.longMedicationDetail==null)){
                    resultTip({
                        error: 'warning',
                        msg: '请填写长期用药需求详情'
                    });
                    return
                }
                if(this.nurse.medicalHistory=='2' && (this.nurse.medicalHistoryDetail=='' || this.nurse.medicalHistoryDetail==null)){
                    resultTip({
                        error: 'warning',
                        msg: '请填写既往病史需求详情'
                    });
                    return
                }
                if(this.nurse.isCOVID==undefined || this.nurse.isCOVID==null){
                    Vue.set(this.nurse, "COVID", []);
                    Vue.set(this.nurse, "isCOVID", '');
                }
                if(this.nurse.isCOVID==1){
                    this.nurse.COVID=this.COVID
                }else{
                    Vue.set(this.nurse, "COVID", []);
                }
                $.ajax({
                    url:'<?php echo $this->createUrlReg('saveRemarks'); ?>',
                    type: 'get',
                    dataType:'json',
                    data:{
                        child_id:this.child_id,
                        step_id:step_id,
                        remarks:this.nurse, 
                        coll_id:this.coll_id,
                        
                    },
                    success:function(data){
                        if(data.state=="success"){
                            resultTip({
								msg: data.state
							});
                            that.getInfo(that.type)
                            // $('#auditDialog').modal('hide')
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            printMedical(index,item){
                console.log(item)
                let childid = item.childid
                let coll_id = this.coll_id
                console.log(step_id,childid,coll_id);
                window.open('<?php echo $this->createUrlReg('PrintDetail',array(''=>childid)); ?>','_blank');
            }
        }
    })



</script>
<style>
    a[data-status="0"] {
        cursor: default;
        text-decoration: none;
    }
</style>