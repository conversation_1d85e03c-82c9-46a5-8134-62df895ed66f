<?php
    Yii::import('common.components.reports.*');
    $template = new IvySemesterReport($taskData['templateId']);
    $treeData = $template->getTree();
?>

<script>
    var currentData = {};
    var schoolGroup = <?php echo $schoolGroup; ?>;
    var childDataRaw = <?php echo CJSON::encode($taskData['children']); ?>;
    var childData = _.sortBy(childDataRaw, 'name');
    var reportTemplate = <?php echo CJSON::encode( $treeData );?>;
    var reportItemGuide = {};

    var childReportData = <?php echo CJSON::encode( $childReportData );?>;

    var cycleReportItems = null; //显示树结构
    var cycleReportChildItems = null; //显示树项的子结构
    var addItem = null; //用户添加的内容变量
    var addExtraContentToTree = null; //把用户添加的影响树结构的内容显示到树中；
    var addChildReportData = null; //把已添加的孩子报告信息显示出来

    var openLearningDomainModal = null; //新建学习领域窗口
    var openLearningProjectModal = null; //新建学习项目窗口
    var drawTreeView = null;

    var showChildList = null; //显示孩子列表；
    var loadReportViewByChild = null; //点击孩子显示报告编辑信息
    var loadItemForm = null; //加载某一项的表单及数据
    var addUIEffect = null;
    var editContent = null;
    var cbSaveReportItem = null; //保存完学期报告ITEM的回调
    var cancelEdit = null;
    var cbPhotoSelected = null; //选择完照片
    var selectPhoto = null; //选择照片
    var showReportStats = null;
    var setReportStatus = null; //更新学期报告状态
    var reportPreview = null; //预览学期报告
    var applyPhotoToOthers = null; //把封底照片分配给其他人
    var setReportLang = null; //设置报告语言
    var isAvatar = null; //设置头像
    var cropper_items_img = null; //设置头像
    
    var _edit = '<span class="glyphicon glyphicon-edit"></span> ';
    var _minus = '<span class="glyphicon glyphicon-chevron-right"></span> ';
    var _remove = '<span class="glyphicon glyphicon-remove"></span> ';
    var userAdded = [];

    currentData.learningDomains = <?php echo CJSON::encode($template->getLearningDomains());?>;
    currentData.photoCache = {};
    currentData.bigPhotoCache = {};
    currentData.onlined = [];
    currentData.submits = [];
    currentData.pass = [];
    currentData.overrule = [];
    currentData.overruleText = [];
    currentData.onlined = <?php echo CJSON::encode( $taskData['onlined'] );?>; //已经上线的孩子ID；数组 20
    currentData.submits = <?php echo CJSON::encode( $taskData['submits'] );?>; //已经提交的孩子ID；数组 15
    currentData.pass = <?php echo CJSON::encode( $taskData['pass'] );?>; // 已经审核的孩子ID；数组 16
    currentData.overrule = <?php echo CJSON::encode( $taskData['overrule'] );?>; //驳回的孩子ID；数组 14
    currentData.overruleText = <?php echo CJSON::encode( $taskData['overruleText'] );?>; //驳回的孩子内容；数组 14

    $(function(){
        //初始化表单条目提示信息
        _.each(reportTemplate, function(_items){
            reportItemGuide[_items.sign] = {};
            _.each(_items.items, function(_item){
                reportItemGuide[_items.sign][_item.sign] = _item.desc;
            })
        });
        cbSaveReportItem = function(data){
            addChildReportData(data.data);
            var editbox = $('#form-data-wrapper').parents('div.editing').hide();
            editbox.siblings('div.preview').show();
            $('#report-tree a.edit-btn').show();
        };

        //添加项（IA用）
        addItem = function(type){
            if(type == 'learning-domain-item'){
                openLearningDomainModal();
            }else if(type == 'learning-project-item'){
                openLearningProjectModal();
            }
        };

        selectPhoto = function(obj){
            currentData.selectPhotoBtn = $(obj);
            _SM_selectPhotos();
        };

        cbPhotoSelected = function(data){
            if(isAvatar=='Avatar'){
                var _img = $('<img class="img-thumbnail" />')
                _img.show();
                currentData.selectPhotoBtn.siblings('img.img-thumbnail').remove();
                currentData.selectPhotoBtn.siblings('div').remove();
                var newUrl;
                var rotate=0
                
                _.each(data, function(imgUrl, imgId){
                    if (imgUrl.indexOf("!w600A") > 0 || imgUrl.indexOf("!ww600A") > 0) {
                        newUrl= imgUrl.replace(/!w600A|!ww600A/g, "!A")
                        rotate=90
                    } else if (imgUrl.indexOf("!w600B") > 0 || imgUrl.indexOf("!ww600B") > 0) {
                        newUrl = imgUrl.replace(/!w600B|!ww600B/g, "!B")
                        rotate=180
                    } else if (imgUrl.indexOf("!w600C") > 0 || imgUrl.indexOf("!ww600C") > 0) {
                        newUrl = imgUrl.replace(/!w600C|!ww600C/g, "!C")
                        rotate=270
                    } else {
                        newUrl= imgUrl.replace(/!w600|!ww600/g, "")
                        rotate=0
                    }
                    _img.attr('src', newUrl + '?imageMogr/auto-orient')
                    currentData.selectPhotoBtn.parents('div.photo-select').prepend(_img);
                        $('#form-data-wrapper #SReportItem_media_id').val(imgId);
                        currentData.photoCache[imgId] = imgUrl;
                        currentData.bigPhotoCache[imgId] = imgUrl;
                })
                cropper_items_img = new Cropper(_img[0], {
                    aspectRatio: 4 / 4,
                    cropBoxResizable: false,
                    viewMode: 1,
                    autoCropArea: 0.999,
                    zoomable: false,
                    rotatable: false,
                    preview: '',
                    scalable:false,
                    crop: function(event) {
                        if(rotate==0){
                            var cropper_data = '?imageMogr2/imageMogr/auto-orient/crop/!' + event.detail.width + 'x' + event.detail.height + 'a' + event.detail.x + 'a' + event.detail.y;
                        }else{
                            var cropper_data = '?imageMogr2/imageMogr/auto-orient/rotate/' + rotate + '/crop/!' + event.detail.width + 'x' + event.detail.height + 'a' + event.detail.x + 'a' + event.detail.y;
                        }
                        
                        $('#SReportItem_img_cropper').val(cropper_data);
                    },
                });
            }else{
                var name = currentData.selectPhotoBtn.data('name');
                if (name) {
                    _.each(data, function(imgUrl, imgId){
                        $('#text_' + name).val(imgId)
                        $('#img_' + name).attr('src', imgUrl)
                    })
                } else {
                    currentData.selectPhotoBtn.siblings('img.img-thumbnail').remove();
                    _.each(data, function(imgUrl, imgId){
                        var _img = $('<img class="img-thumbnail" />').attr('src', imgUrl);
                        currentData.selectPhotoBtn.parents('div.photo-select').prepend(_img);
                        $('#form-data-wrapper #SReportItem_media_id').val(imgId);
                        currentData.photoCache[imgId] = imgUrl;
                        currentData.bigPhotoCache[imgId] = imgUrl;
                    })
                }
            }
            
        };
        
        //将封底照片应用与其他学期报告未上线的孩子
        applyPhotoToOthers = function(obj, category){
            var _currentPhotoId = $('#form-data-wrapper #SReportItem_media_id').val();
            if(!_.isEmpty(_currentPhotoId) && _currentPhotoId>0){
                $.get('<?php echo $this->createUrl('getBcoverChildren');?>', {id: _currentPhotoId, classid: currentData.report.classid, category: category}, function(data){
                    var assignHtml = '';
                    var assignChildren = childDataRaw;
                    for(var childid in assignChildren){
                        var templateData = childDataRaw[childid];
                        if(currentData.report.childid != childid){
                            if(data.indexOf(childid) > -1){
                                templateData['selected'] = 1;
                            }
                            else{
                                templateData['selected'] = 0;
                            }
                            assignHtml += _.template($('#assign-cover-template').html(), templateData);
                        }
                    }
                    assignHtml += '<div class="clearfix"></div>';
                    $('#assignCover .modal-body>p').html(assignHtml);
                    $('#assignCover #pid').val(_currentPhotoId);
                    $('#assignCover #category').val(category);
                    $('#assignCover #childid').val(currentData.childId);
                    $('#assignCover').modal();
                }, 'json');
            }
        };

        // 设置报告语言
        setReportLang = function() {
            var lang = $('input[name=langRadio]:checked').val()
            $.ajax({
                type: 'post',
                url: '<?php echo $this->createUrl('//mteaching/semester/setReportLang');?>',
                dataType: 'json',
                data: {lang: lang, id: currentData.report.id}
            }).done(function(data){
                if(data.state == 'success'){
                    resultTip({msg: data.message});
                    if (lang == "cn") {
                        $("#lang_text").text("<?php echo Yii::t('teaching', 'Chinese'); ?>");
                    } else {
                        $("#lang_text").text("<?php echo Yii::t('teaching', 'English'); ?>");
                    }
                    hideLang();
                }else{
                    resultTip({msg: data.message, error: 1});
                }
            });
        }

        cycleReportChildItems = function(item){
            var _li = $('<li></li>').attr("sign",item.sign);
            if(!_.isUndefined(item.content)){
                _li.attr("item-content-type", item.content);
            }
            if(!_.isUndefined(item['items'])){
                var _p = $('<span class="item clickable"></span>');
                _p.append(_minus).append(item.title);
                _li.append(_p);
                if(!_.isUndefined(item.ops)){
                    userAdded.push(item.sign);
                    _.each(item.ops, function(_item, _index){
                        switch (_index){
                            case 'add':
                                _li.append(' <a title="<?php echo Yii::t('teaching', 'Add New');?>" href="javascript:void(0)" onclick="addItem(\''+ item.ops.add +'\')"> <span class="glyphicon glyphicon-plus"></span></a>');
                                break;
                            case 'sort':
                                _li.append(' <a title="<?php echo Yii::t('teaching', 'Sort');?>" href="javascript:void(0)" onclick="sortItem(\''+ item.ops.sort +'\')"> <span class="glyphicon glyphicon-sort-by-attributes"></span></a>');
                                break;
                        }
                    })
                }
                if(item.sign=="TeacherMessage"){
                    _li.append($('<ul></ul><p><dl style="padding-left:40px;"><dt><img src="http://m2.files.ivykids.cn/cloud01-file-8025768Fkngr6JoYSUWrQEFmLf5KvYBF3oM.png" style="width:170px" class="img-thumbnail"></dt><dd style="line-height:40px">请使用企业微信扫描二维码签字</dd></dl></p>'));

                }else{
                    _li.append($('<ul></ul>'));

                }
                cycleReportItems(item['items'], _li.find('ul'));
            }else{
                var _contentItem = contentItemTpl(item);
                _li.html(_contentItem);
            }
            return _li;
        };

        addChildReportData = function(dataObj){
            if(_.isUndefined(dataObj)){
                var _tmpData = currentData.reportItems;
            }else{
                var _tmpData = {one: dataObj}
            }
            _.each(_tmpData, function(_data, _key){
                var pLI = $('#report-tree>ul>li.parent_li[sign|="'+_data.category+'"]');
                var LI = pLI.find("li[sign|='"+_data.subcategory+"']");
                var _wrapper = LI.find('div.preview');
                var _view = rptPreviewData(_data);
                _wrapper.html(_view);
            })
            if (cropper_items_img) {
                if(dataObj && dataObj.category=='Avatar'){
                    if (cropper_items_img.getCroppedCanvas()) {
                        var objSrc = cropper_items_img.getCroppedCanvas().toDataURL('image/jpeg');
                        currentData.bigPhotoCache[dataObj.media_id] = objSrc;
                        var pLI = $('#report-tree>ul>li.parent_li[sign|="'+dataObj.category+'"]');
                        var LI = pLI.find("li[sign|='"+dataObj.subcategory+"']");
                        var _wrapper = LI.find('div.preview');
                        var _view = rptPreviewData(dataObj);
                        _wrapper.html(_view);
                    }
                    cropper_items_img.destroy();
                }
            }
        };

        cycleReportItems = function(items, rootUL){
            rootUL.empty();
            if(items.length > 0){
                _.each(items,function(item,index){
                    var _li = cycleReportChildItems(item);
                    rootUL.append(_li);
                })
            }
        };

        cancelEdit = function(obj){
            var editBox = $(obj).parents('div.editing');
            editBox.siblings('div.preview').show();
            editBox.hide();
            $('#report-tree a.edit-btn').show();
        };

        addExtraContentToTree = function(){
            _.each(userAdded, function(_key, _index){
                var dataItemAttr = _key + '-item';
                var parentUL = $('li[sign|="'+_key+'"]').find( 'ul' );
                parentUL.find('li[data-item|="'+dataItemAttr+'"]').remove();
                _.each(childReportData[_key]['items'], function(_item, _index){
                    _item.dataindex = _index;
                    _item.content = childReportData[_key]['content'];
                    _item.sign = 'item';
                    var li = $('<li></li>');
                    li.addClass('level-2').attr('extra-data',true).attr('sign','item').attr('item-content-type', childReportData[_key]['content']);
                    li.html(extraContentItemTpl(_item));
                    parentUL.append(li);
                })
            })
        };

        setReportStatus = function(status, childid, obj){
            if( parseInt(currentData.report.childid) == parseInt(childid) && parseInt(childid) > 0 ){
                $(obj).attr('disabled', 'disabled');
                $.ajax({
                    type: 'post',
                    url: '<?php echo $this->createUrl('//mteaching/semester/setReportStatus');?>',
                    dataType: 'json',
                    data: {status:status,report_id:currentData.report.id}
                }).done(function(data){
                    if(data.state == 'success'){
                        if(data.data.stat==10){
                            currentData.submits = _.without(currentData.submits, parseInt(data.data.childid));
                        }
                        if(data.data.stat==15){
                            currentData.overrule = _.without(currentData.overrule, parseInt(data.data.childid));
                            currentData.submits.push(parseInt(data.data.childid));
                            currentData.submits = _.uniq(currentData.submits);
                        }
                        if(data.data.stat==16){
                            currentData.onlined = _.without(currentData.onlined, parseInt(data.data.childid));
                            currentData.pass.push(parseInt(data.data.childid));
                            currentData.pass = _.uniq(currentData.pass);
                        }
                        if(data.data.stat==20){
                            currentData.pass = _.without(currentData.pass, parseInt(data.data.childid));
                            currentData.onlined.push(parseInt(data.data.childid));
                            currentData.onlined = _.uniq(currentData.onlined);
                        }
                        if(currentData.report.custom_pdf==''){
                           loadReportViewByChild(data.data.childid)
                        }
                        resultTip({msg: data.message});
                        $('#selected-child-box').html( childInfoTpl(childDataRaw[data.data.childid]) );
                        showReportStats();
                    }else{
                        resultTip({msg: data.message, error: 1});
                    }
                    setReportPdf(currentData.report.id);
                    $(obj).removeAttr('disabled');
                });
            }
        };

        showChildList = function(){
            $('#semester-report-child-list').empty();
            _.each(childData, function(_child){
                var _view = childTpl(_child);
                $('#semester-report-child-list').append(_view);
            });
            showReportStats();
        };

        showReportStats = function(){
            $('#semester-report-child-list li[childid]').removeClass('online');
            _.each(currentData.onlined, function(_childId){
                $('#semester-report-child-list li[childid|="'+_childId+'"]').addClass('online');
            })
        };

        drawTreeView = function(){
            cycleReportItems(reportTemplate, $('#report-tree>ul'));
            addExtraContentToTree();
        };

        var ajaxing = false;
        var getChildReport = '<?php
            echo $this->createUrl('getChildSemesterReport',array(
                'classid'=>$this->selectedClassId,
                'semester'=>$this->selectedSemester
                ))
                ?>';
        loadReportViewByChild = function(childId){
            //todo, 若当前form有未保存的内容，给出提示

            if(!ajaxing){
                $('.report-edit-zone').hide();
                $('.loadViewByChild').removeClass('loadViewByChilded')
                $('#child'+childId).addClass('loadViewByChilded')
                currentData.childId = childId;
                userAdded = [];
                ajaxing = !ajaxing;
                $.ajax({
                    async: false,
                    type: 'post',
                    url: getChildReport,
                    dataType: 'json',
                    data: {childid:currentData.childId}
                }).done(function(data){
                    ajaxing = false;
                    if(data.state == 'fail'){
                        alert(data.message);
                        return;
                    }
                    //显示当前选中的孩子
                    currentData.report = data.data.report;
                    currentData.reportItems = data.data.items;
                    currentData.photoCache = data.data.photos;

                    currentData.bigPhotoCache = data.data.bigphotos;

                    $('#selected-child-box').html( childInfoTpl(childDataRaw[childId]) );

                    $('#report-tree').empty();
                    $('#report-tree').append('<ul class="top"></ul>');
                    drawTreeView();
                    addUIEffect();

                    addChildReportData();
                    $('.report-edit-zone').show('blind',{},500);
                    // 显示孩子健康数据
                    $('#health_childid').val(childId);
                    var media1, media2, media3;
                    var imgsrc1, imgsrc2, imgsrc3;
                    media1 = data.data.health.img1
                    media2 = data.data.health.img2
                    media3 = data.data.health.img3
                    imgsrc1 =  media1 ? data.data.bigphotos[media1] : '';
                    imgsrc2 =  media2 ? data.data.bigphotos[media2] : '';
                    imgsrc3 =  media3 ? data.data.bigphotos[media3] : '';
                    $('#text_health1').val(media1);
                    $('#img_health1').attr('src', imgsrc1);

                    $('#text_health2').val(media2);
                    $('#img_health2').attr('src', imgsrc2);

                    $('#text_health3').val(media3);
                    $('#img_health3').attr('src', imgsrc3);


                });
                setReportPdf(currentData.report.id)
            }
        };

        function setReportPdf(report_id) {
            var uploader = new plupload.Uploader({
                runtimes: 'html5,flash,silverlight,html4',
                browse_button: 'select-photo',
                container: document.getElementById('container'),
                url: '<?php echo $this->createUrl('setReportPdf') ?>',
                flash_swf_url: '<?php echo Yii::app()->themeManager->baseUrl . '/base/js/plupload/Moxie.swf' ?>',
                silverlight_xap_url: '<?php echo Yii::app()->themeManager->baseUrl . '/base/js/plupload/Moxie.xap' ?>',
                multipart_params: {report_id:report_id},
                filters: {
                    max_file_size: '50mb',
                    mime_types: [
                        {title: "<?php echo Yii::t('teaching', 'Image files'); ?>", extensions: "pdf"}
                    ]
                },

                init: {
                    QueueChanged: function (up) {
                        uploader.start();
                    },

                    UploadProgress: function(up, file) {
                        $('#'+file.id+' dt span').hide();
                        $('#'+file.id+' div.progress-bar').attr('aria-valuenow', file.percent).css('width', file.percent+'%').html(file.percent+'%');
                    },

                    FileFiltered: function(up, file) {
                        // Called when file successfully files all the filters
                        queueHtml(file);
                        $("#modal").modal();
                        $('#'+file.id+' span').click(function(){
                            $('#'+file.id).remove();
                            uploader.removeFile(file);
                        });
                    },

                    FileUploaded: function (up, file, info) {
                        var json = eval('('+info.response+')');
                        $('#' + file.id).remove();

                        var html = '<a class="btn btn-info" target="_blank" href="'+ json.pdfUrl + '"><span class="glyphicon glyphicon-file"></span> PDF</a>';
                        $("#viewReportPDF").html(html);
                        $('#modal').modal('hide');
                    }
                }
            });
            uploader.init();
        }
        function queueHtml(file)
        {
            var filelist = $('#filelist');
            var queueHtml = '<dl id="' + file.id + '"><dt><span class="glyphicon glyphicon-remove"></span> ';
            queueHtml += file.name+' <small>('+plupload.formatSize(file.size)+')</small>';
            queueHtml += '</dt><dd class="progress">';
            queueHtml += '<div class="progress-bar" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>';
            queueHtml += '</div></dd></dl>';
            filelist.find('div.panel-body').append(queueHtml);
        }

        addUIEffect = function(){
            $('.tree li:has(ul)').addClass('parent_li');
            $('.tree li.parent_li > span.clickable').on('click', function (e) {
                var children = $(this).parent('li.parent_li').find(' > ul > li');
                if (children.is(":visible")) {
                    children.hide();
                    $(this).find('span.glyphicon').addClass('glyphicon-chevron-down').removeClass('glyphicon-minus-sign');
                } else {
                    children.show();
                    $(this).find('span.glyphicon').addClass('glyphicon-chevron-right').removeClass('glyphicon-plus-sign');
                }
                e.stopPropagation();
            });
        };

        showChildList();

        var getChildReportItem = '<?php
            echo $this->createUrl('getChildSemesterReportItem',array(
                'classid'=>$this->selectedClassId,
                'semester'=>$this->selectedSemester
                ))
                ?>';
        editContent = function(obj){
            //todo 如果有其他编辑的窗口没有保存，请提示
            if(ajaxing) return true;

            //不让同时编辑其他信息；隐藏编辑按钮
            $('a.edit-btn').hide();
            $(obj).parents('div.content-item').find('div.preview').hide();
            $(obj).parents('div.content-item').find('div.editing').show();

            $('.content-item .editing').empty();

            var formData = {};
            var _liWrapper = $(obj).parents('li[item-content-type]');
            var contentType = _liWrapper.attr('item-content-type');
            formData.subcategory = $(obj).parents('div[data-content-type]').attr('sign');
            formData.category = $(obj).parents('li.parent_li').attr('sign');
            isAvatar=$(obj).parents('li.parent_li').attr('sign');
            ajaxing = !ajaxing;
            $('#report-item-form').hide();
            $.ajax({
                async: false,
                type: 'post',
                url: getChildReportItem,
                dataType: 'json',
                data: {childid:currentData.childId, report_id: currentData.report.id,
                    category: formData.category, subcategory: formData.subcategory
                }
            }).done(function(data){
                ajaxing = false;
                currentData.reportItem = {};
                currentData.reportItem = data.data.reportItem;
                currentData.reportItem.contentType = contentType.split('+');
            });

            //todo 从数据库取数据
            var _formView = rptFormData(currentData.reportItem);
            $('#form-data-wrapper').empty().html(_formView);

            _liWrapper.find('div.editing').html( $('#report-item-form').html() );

            //backbone不能赋值的内容，在这里赋值
            $('#form-data-wrapper #SReportItem_ld_id').val(currentData.reportItem.ld_id);

            var guideBox = _liWrapper.find('div.editing').find('div.user-input-guide');
            if(guideBox){
                guideBox.html("");
                _.each(reportItemGuide[currentData.reportItem.category][currentData.reportItem.subcategory],
                    function(_guide){
                    guideBox.append('<p>'+_guide+'</p>')
                })
            }


            head.Util.ajaxForm($('.report-edit-zone'));


        };



        reportPreview = function(){
            var url = "<?php echo $this->createUrl($taskData['previewurl'], array(
                    'childid'=>'-childid-',
                    'id'=>'-id-',
                ));?>";
            url = url.replace('-childid-', currentData.report.childid);
            url = url.replace('-id-', currentData.report.id);
            window.open(url);
        };

        // 封底分配给其他孩子成功回调
        cbAssign = function(){
            $('#assignCover').modal('hide');
        };

        clearPDF = function(id){
            $.post('<?php echo $this->createUrl('clearPDF')?>', {id: id}, function(data){
                if(data.state == 'success'){
                    var err = 0;
                }
                else{
                    var err = 1;
                }
                var  msg = data.message;
                resultTip({msg: msg, error: err});
            }, 'json');
        }
    })
</script>

<div class="row">

    <div class="col-md-3 col-lg-2">
        <div class="panel panel-default">
            <div class="panel-heading">
                <?php echo Yii::t("teaching", "Child List"); ?>
            </div>
            <div class="panel-body">
                <ul class="media-list" id="semester-report-child-list"></ul>
            </div>
        </div>
    </div>

    <div class="col-md-9 col-lg-10">

        <div class="panel panel-default">
            <div class="panel-heading">
                <?php echo Yii::t('teaching','Semester Report');?>
            </div>
            <div class="panel-body report-edit-zone" style="display: none;">

                <div id="selected-child-box">
                    <!--当前选中孩子的信息-->
                </div>

                <div class="page-header">
                    <h4><?php echo Yii::t('teaching','Semester Report');?></h4>
                </div>

                <div id="report-tree" class="tree">
                    <!--模版结构占位符-->
                </div>
                <!--<php if($schoolGroup != 10 && $taskData['templateId'] == 'Ivy03'): ?>
                <div class="tree">
                    <ul class="top">
                        <li class="parent_li">
                            <span class="item"><span class="glyphicon glyphicon-chevron-right"></span> 身体生长发育</span>
                            <ul>
                                <li>
                                    <h4>选择配图</h4>
                                    <div class="panel panel-success">
                                        <div class="panel-heading">
                                            <form id="health" class="J_ajaxForm"  action="<php /*echo $this->createUrl('//mcampus/health/saveHealthMedia');*/?>" method="post">
                                                <div class="form-group">
                                                    <img src="" class="img-thumbnail img-responsive" id="img_health1">
                                                    <br>
                                                    <button type="button" class="btn btn-default" data-name='health1' onclick="selectPhoto(this);">
                                                        <span class="glyphicon glyphicon-picture"></span> <php /*echo Yii::t('teaching','配图1');*/?>
                                                    </button>
                                                    <input type="hidden" name="img1" value="" id="text_health1">
                                                </div>
                                                <div class="form-group">
                                                    <img src="" class="img-thumbnail img-responsive" id="img_health2">
                                                    <br>
                                                    <button type="button" class="btn btn-default" data-name='health2' onclick="selectPhoto(this);">
                                                        <span class="glyphicon glyphicon-picture"></span> <php /*echo Yii::t('teaching','配图2');*/?>
                                                    </button>
                                                    <input type="hidden" name="img2" value="" id="text_health2">
                                                </div>
                                                <div class="form-group">
                                                    <img src="" class="img-thumbnail img-responsive" id="img_health3">
                                                    <br>
                                                    <button type="button" class="btn btn-default" data-name='health3' onclick="selectPhoto(this);">
                                                        <span class="glyphicon glyphicon-picture"></span> <php /*echo Yii::t('teaching','配图3');*/?>
                                                    </button>
                                                    <input type="hidden" name="img3" value="" id="text_health3">
                                                </div>
                                                <input type="hidden" name="classid" value="<php /*echo $this->selectedClassId;*/?>">
                                                <input type="hidden" name="semester" value="<php /*echo $this->selectedSemester;*/?>">
                                                <input type="hidden" name="childid" id="health_childid">
                                                <button type="submit" class="btn btn-primary J_ajax_submit_btn"><php /*echo Yii::t("global", 'Save');*/?></button>
                                            </form>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </div>
                <php endif; ?>-->
                <div id="report-item-form" style="display: none;">
                    <div class="panel panel-warning">
                        <div class="panel-heading">
                            <form class="form-horizontal J_ajaxForm" id="form-data-wrapper"
                                action="<?php echo $this->createUrl('//mteaching/semester/reportSave');?>"
                                method="post"
                                >
                                <!--表单内容占位符-->
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>

<div class="modal fade" id="assignCover">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title"><?php echo Yii::t("teaching", 'Select a child (Mouse hover to show child name)');?></h4>
            </div>
            <?php echo CHtml::form($this->createUrl('saveBcover'), 'post', array('class'=>'J_ajaxForm'));?>
            <div class="modal-body">
                <p></p>
            </div>
            <div class="modal-footer pop_bottom">
                <input type="hidden" name="pid" id="pid">
                <input type="hidden" name="category" id="category">
                <input type="hidden" name="classid" value="<?php echo $this->selectedClassId;?>">
                <input type="hidden" name="semester" value="<?php echo $this->selectedSemester;?>">
                <input type="hidden" name="childid" id="childid">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", 'Cancel');?></button>
                <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t("global",
                        'Save');?></button>
            </div>
            <?php echo CHtml::endForm();?>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<?php

$this->widget('ext.selectMedia.SelectMedia', array(
    'weeknum'=>8,
    'classid'=>$this->selectedClassId,
    'branchid'=>$this->branchId,
    'startyear'=>$taskData['calendarData']['startyear'],
    'yid'=>$taskData['calendarData']['yid'],
    'children'=> $taskData['children'],
    'multiple'=>false,
    'callback'=>'cbPhotoSelected',
    'buttonLabel' => false,
    'fetchBigPhoto' => true //返回大图地址
));

?>

<?php
    //$this->renderPartial("_modals");
    $this->renderPartial("_sreport_item_tpl", array('template'=>$template));
?>
