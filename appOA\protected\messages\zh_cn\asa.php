<?php
return array (

  'Edit' => '编辑',
  'Assign Teachers' => '分配老师',
  'Edit Description' => '编辑详细介绍',
  'Edit Basic Info' => '编辑基本信息',
  'NameList' => '学生名单',
  'WishList' => '心愿单名单',
  'Update NameList' => '更新人数',
  'Edit' => '编辑',
  'Delete' => '删除',

  'submitted' => '已提交',
  'submitted information' => '退费管理已提交信息',
  'confirmed' => '已确认',
  'confirmed information' => '退费管理已确认信息',
  'refund' => '已退费',
  'refund information' => '退费管理已退费信息',
  'detail' => '详细信息',
  'Staffs' => '工作人员',
  'Mgt team and operation team' => '管理及运营成员',
  'Add a staff' => '添加员工',
  'ASA courses' => '课后课课程',
  'Course List' => '课程列表',
  'Create a course' => '添加课程',
  'Create an ASA Project' => '开启课后课计划',
  'ASA Project' => '课后课项目',
  'Program Management' => '课程管理',
  'Invoice Management' => '账单管理',
  'Invoice List' => '账单汇总',
  'Course Name (CN)' => '课程名（中文）',
  'Course Name (EN)' => '课程名（英文）',
  'Staff Attendance' => '考勤管理',
  'Cash Handover' => '现金交接',
  'Refunds' => '退费信息',
  'Parents Feedbacks' => '家长反馈',
  'Reimbursement' => '报销管理',
  'Class Switch' => '调换课程',
  'Reports' => '报销列表',
  'Price/Class' => '课时单价',
  'Class Number' => '总课时数',
  'Capacity' => '班级容量',
  'Staffs and Vendors' => '员工及机构',
  'course providers and operational team' => '课程的提供方及运营团队',
  'Vendor Name' => '姓名或机构名',
  'Active' => '有效',
  'Phone' => '电话',
  "Cell Phone" => "手机号",
  'Email' => '电邮',
  'Inactive' => '无效',
  'Status' => '状态',
  'Vendor Type' => '类型',
  'Add Vendor' => '添加',
  'Edit Vendor' => '编辑',
  'In-hourse Staff' => '内部个人',
  /*'3rd-party Company' => '外部第三方机构',*/
  '3rd-party Company' => '外部公司',
  'Freelance' => '外部个人',
  '3rd-party Company Staff' => '外部个人',
  'Newly Registered' => '新注册学生',
  'ASA invoices' => '课后课账单',
  'Issued Invoice List' => '已开账单',
  'Create Invoice' => '开账单',
  'New Invoice' => '新开账单',
  'Choose an active ASA project' => '选择课后课项目',
  'Only active ASA projects are listed' => '只能选择已开启的课后课项目',
  'Invoice Title' => '账单标题',
  'Class List' => '班级列表',
  'NameList' => '学生名录',
  'Confirm to delete this invoice?' => '确定删除账单？',
  'Invoice deleted?' => '账单已删除？',
  'Payment made successfully.' => '付款已成功',
  'Payment failed.' => '付款失败',
  "Click to proceed" => "点击继续",
    "Invalid amount, please confirm" => "金额错误，请再次确认",
    "No data found." => "暂无数据",
    "Payment Info" => "付款信息",
    "Bank Name" => "开户行",
    "Bank Account" => "银行帐号",
    "Beneficiary" => "开户名",
    "Identity Number" => "身份证或护照号",
    "For tax submission usage only." => "报税需使用",
    "In-hourse staff can only be selected from the exsistence list." => "自有老师自能从系统中选择。",
    "Input the CN and EN the same content if the name is not in bilingual." => "名称中若没有双语，填入同一语言内容即可。",
    "Input to filter, press down key(↓) to list all." => "输入内容搜索老师，按方向下键“↓”显示所有老师。",

    "Name of the 3rd-party Company (CN)" => "请输入第三方机构公司名称（中文）",
    "Name of the 3rd-party Company (EN)" => "请输入第三方机构公司名称（英文）",

    "Name of freelance staff (CN)" => "外部个人名字（中文）",
    "Name of freelance staff (EN)" => "外部个人名字（英文）",
    "Select an ASA project" => "选择课后课项目",
    "action" => "操作",

    "course_title" => "课程标题",
    "teacher_name" => "推荐上课老师",
    "teacher_contact" => "推荐上课老师电话",
    "memo" => "推荐内容",
    "Parent Name" => "家长姓名",
    'type' => '反馈类型',
    'text' => '反馈内容',
    'bank' => '银行',
    'accountName' => '姓名',
    'accountNumber' => '账号',

    '{year}Yr' => '{year}岁',
    'Gr{class}' => '{class}年级',

    'INVOICE' => '账单',
    'Invoice' => '账单',
    'Due Date:' => '账单到期日：',
    'For Student:' => '孩子姓名：',
    'Class:' => '班级：',
    'Total' => '合计',
    '{{childData.first_name_en}} {{childData.last_name_en}}' => '{{childData.name_cn}}',
    'Please scan the QR code with Wechat to make the payment online.' => '微信扫一扫绑定艾毅帐号，即可在线付款',
    'ASA Staff Attendance' => '课后课考勤记录',
    'Parent' => '家长课程',
    'Students Count' => '分配孩子数量',
    'Program Revenue' => '课程收支',


    'Submit' => 'Submit',
    'Purchase' => '收款金额',


    'Refund' => '退款金额',
    'Revenue' => '有效收入',
    'Staff Salary' => '员工工资',
    'Adjustiment' => '调整金额',
    'Vendor Payment' => '第三方付款',
    'Profit' => '课程利润',
    'Profit Margin' => '利润率',
    'VAT (6%)' => 'VAT (6%)',
    'Wechat Fee (0.6%)' => '微信交易费（0.6%）',
    'General Operations' => '整体运营',

);
