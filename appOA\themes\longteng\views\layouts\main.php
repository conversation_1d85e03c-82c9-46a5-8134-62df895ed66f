<!DOCTYPE html>
<!--
  _______ _            _____            _____
 |__   __| |          |_   _|          / ____|
    | |  | |__   ___    | |_   ___   _| |  __ _ __ ___  _   _ _ __
    | |  | '_ \ / _ \   | \ \ / / | | | | |_ | '__/ _ \| | | | '_ \
    | |  | | | |  __/  _| |\ V /| |_| | |__| | | | (_) | |_| | |_) |
    |_|  |_| |_|\___| |_____\_/  \__, |\_____|_|  \___/ \__,_| .__/
                                  __/ |                      | |
                                 |___/                       |_|
-->
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <script>
        var GV = {
            'themeManagerBaseUrl': '<?php echo Yii::app()->themeManager->baseUrl;?>',
            'themeBaseUrl': '<?php echo Yii::app()->theme->baseUrl;?>',
            'lang': "<?php echo Yii::app()->language;?>"
        };
    </script>
    <title><?php echo CHtml::encode($this->pageTitle); ?></title>

    <!-- Bootstrap core CSS -->
    <link href="<?php echo Yii::app()->theme->baseUrl; ?>/css/bootstrap.css" rel="stylesheet">
    <link href="<?php echo Yii::app()->theme->baseUrl; ?>/css/css.css" rel="stylesheet">

    <script src="<?php echo Yii::app()->themeManager->baseUrl; ?>/base/js/head.min.js"></script>
    <?php Yii::app()->clientScript->registerCoreScript('jquery'); ?>
    <script src="<?php echo Yii::app()->themeManager->baseUrl; ?>/base/js/bootstrap.min.js"></script>
    <script>var LANG="<?php echo Yii::app()->language;?>";</script>

</head>

<body style="padding-top: 70px;">
<div class="navbar navbar-fixed-top navbar-inverse" role="navigation">
    <div class="container-fluid">
        <div class="navbar-header">
            <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>
            <div class="navbar-brand">

                <?php echo CHtml::link(Yii::t('site',Yii::app()->name), array('/backend/apps'), array('class'=>'navbar---brand'));?>

                <?php if(!empty($this->modernMenuCategoryTitle)):?>
                    <?php
                    echo ' <span class="glyphicon glyphicon-chevron-right"></span> ';
                    echo CHtml::openTag('span', array('style'=>"position: relative"));
                    echo CHtml::link($this->modernMenuCategoryTitle.' <b class="caret"></b>', 'javascript:void(0);', array('class'=>'dropdown-toggle','data-toggle'=>"dropdown", 'id'=>'dropdown-maincate'));?>
                    <div class="dropdown-menu main-menu-toggle" role="menu" aria-labelledby="dropdown-maincate">
                            <?php
                                foreach($this->modernMenu['main'] as $_mainMenu){
                                    echo CHtml::openTag('dl',array('class'=>'dl-horizontal'));
                                    echo CHtml::openTag('dt');
                                    echo $_mainMenu['label'];
                                    echo CHtml::closeTag('dt');
                                    echo CHtml::openTag('dd');
                                    foreach($_mainMenu['items'] as $_item){
                                        echo CHtml::link($_item['label'],$_item['url'],isset($_item['linkOptions']) ? $_item['linkOptions'] : array());
                                    }
                                    echo CHtml::closeTag('dd');
                                    echo CHtml::closeTag('dl');
                                }
                            ?>
                    </div>
                    <?php echo CHtml::closeTag('span');?>
                <?php endif;?>
            </div>


        </div>
        <div class="collapse navbar-collapse">
            <?php

            $userItems = array(
                array('label'=>'English', 'url'=>array("//site/lang", "lang" => "english"), 'linkOptions'=>array('class'=>'J_lang', 'lang'=>'en_us')),
                array('label'=>'中文', 'url'=>array("//site/lang", "lang" => "schinese_utf8"), 'linkOptions'=>array('class'=>'J_lang','lang'=>'zh_cn'))
            );

            if(Yii::app()->user->id){
                $userItems[] = array('label'=>Yii::t('user','Logout'), 'url'=>Yii::app()->user->logoutUrl);
            }

            $this->widget('zii.widgets.CMenu', array(
                'id'=>'lang-switch',
                'items'=> $userItems,
                'htmlOptions'=>array('class' => 'nav navbar-nav navbar-right'),
                'encodeLabel'=>false,
            ));
            ?>
            <?php
            if(isset($this->modernMenu) && isset($this->modernMenu[$this->modernMenuFlag])):
                $this->widget('zii.widgets.CMenu', array(
                    'id'=>'main-nav',
                    'items'=>$this->modernMenu[$this->modernMenuFlag],
                    'htmlOptions'=>array('class' => 'nav navbar-nav'),
                    'encodeLabel'=>false,
                ));
            endif;
            ?>
        </div><!-- /.nav-collapse -->
    </div><!-- /.container -->
</div><!-- /.navbar -->

<?php echo $content; ?>


<script type="text/javascript">
    /*<![CDATA[*/
    jQuery(function($) {
        jQuery('body').on('click','.J_lang',function(){jQuery.ajax({'dataType':'json','success':function(data, textStatus, jqXHR){window.location.reload()},'url':$(this).attr('href'),'cache':false});return false;});
        $('#lang-switch a[lang|='+LANG+']').parent().addClass('active');
    });
    /*]]>*/
</script>
<script type="text/javascript" src="<?php echo Yii::app()->themeManager->baseUrl; ?>/base/js/common.js"></script>
</body>
</html>