<?php
class ContentFetcher {

    /**
     * 生成学期报告html
     * @param $params = array(
     *      'schoolid' => $this->branchId,
     *      'childid' => $childId,
     *      'id' => $reportId
     *      );
     * @return string
     */
    static function getSemesterReport($params, $withToolBar=false, $downloadActionUrl=""){
        $html = "";
        if(!empty($params)){
            Yii::import('common.models.portfolio.*');
            Yii::import('common.models.classTeacher.*');
            $report = SReport::model()->with('items')->findByAttributes($params);
            if($report){

                $data = ContentFetcher::getSemesterReportData($report, true);

                $viewFile = Yii::getPathOfAlias('common.components.teaching.views.semesterReport') . '.php';
                $html = Yii::app()->getController()->renderFile($viewFile,
                    array(
                        'params' => $params,
                        'data' => $data,
                        'withToolBar' => $withToolBar,
                        'downloadActionUrl' => $downloadActionUrl
                    ),
                    true);
            }
        }
        return $html;
    }

    static function getSemesterReport2($params, $withToolBar=false, $downloadActionUrl=""){

        $html = "";
        if(!empty($params)){
            Yii::import('common.models.portfolio.*');
            $report = SReport::model()->with('items')->findByAttributes($params);
            if($report){
                $template = 'semesterReport2';
                $data = ContentFetcher::getSemesterReportData2($report, true);
                if ($report->startyear > 2020) {
                    // 根据条件选择模板
                    $schoolType = 'ivy';
                    if ($params['schoolid'] == 'BJ_QFF') {
                        $schoolType = 'qf';
                    }
                    $version = 2;
                    $template = "semesterReport_{$schoolType}_{$version}";
                    if ($params['schoolid'] == 'BJ_IASLT' && $data['classType'] == "k") {
                        $template = "semesterReport_{$schoolType}_{$version}_ibs";
                    }
                }

                $viewFile = Yii::getPathOfAlias('common.components.teaching.views.'.$template) . '.php';
                $html = Yii::app()->getController()->renderFile($viewFile,
                    array(
                        'params' => $params,
                        'data' => $data,
                        'withToolBar' => $withToolBar,
                        'downloadActionUrl' => $downloadActionUrl
                    ),
                    true);
            }
        }
        return $html;
    }

    static function getSemesterReport3($params, $withToolBar=false, $downloadActionUrl=""){
        $html = "";
        if(!empty($params)){
            Yii::import('common.models.portfolio.*');
            $report = SReport::model()->with('items')->findByAttributes($params);
            if($report){
                $template = 'semesterReport3';
                $data = ContentFetcher::getSemesterReportData2($report, true);
                if ($report->startyear > 2020) {
                    // 根据条件选择模板
                    $schoolType = 'ivy';
                    if ($params['schoolid'] == 'BJ_QFF') {
                        $schoolType = 'qf';
                    }
                    $version = 2;
                    $template = "semesterReport_{$schoolType}_{$version}_ibs";
                    if ($params['schoolid'] == 'BJ_OE' && in_array($data['classType'], array('n', 'b'))) {
                        $template = "semesterReport_{$schoolType}_{$version}";
                    }
                }

                $viewFile = Yii::getPathOfAlias('common.components.teaching.views.'.$template) . '.php';
                $html = Yii::app()->getController()->renderFile($viewFile,
                    array(
                        'params' => $params,
                        'data' => $data,
                        'withToolBar' => $withToolBar,
                        'downloadActionUrl' => $downloadActionUrl
                    ),
                    true);
            }
        }
        return $html;
    }

    public static function reportSupportOfDs($esl='', $csl='', $leap, $support, $other='')
    {
        $ret = '';
        if($esl){
            $ret = 'ESL ';
        }
        if($leap){
            $ret .= 'LEAP ';
        }
        if($csl){
            $ret .= 'Yuyue ';
        }
        if($support){
            $ret .= Yii::t('report', 'Learning Support');
        }
        if($other){
            if($ret){
                $ret .= '<br>'.$other;
            }
            else{
                $ret .= $other;
            }
        }
        return $ret;
    }

    /**
     * 生成小学学期报告HTML
     * @param type $params
     * @param type $withToolBar
     * @param type $downloadActionUrl
     * @return string
     */
    public static function getSemesterReportOfDs($params, $withToolBar = false, $downloadActionUrl = "")
    {
        $html = "";
        if (empty($params)) {
            return $html;
        }
        Yii::import('common.models.reportCards.*');
        Yii::import('common.models.grades.*');
        Yii::import('common.models.classTeacher.*');
        Yii::import('common.models.hr.HrPosition');
        Yii::import('common.models.calendar.Calendar');
        $uid = Yii::app()->user->getId();
        $id = (isset($params['id']) && $params['id']) ? $params['id'] : 0;
        $classid = (isset($params['classid']) && $params['classid']) ? $params['classid'] : 0;
        $childid = (isset($params['childid']) && $params['childid']) ? $params['childid'] : 0;
        $branchId = $params['schoolid'];
        $data['reportSemester'] = array(
            '1' => CommonUtils::autoLang('第一学期', 'Semester 1'),
            '2' => CommonUtils::autoLang('第二学期', 'Semester 2'),
        );
        $coverUrl = Yii::app()->controller->createUrl('//reportHtml/cover', array(
            'rid' => $id,
            'childId' => $childid,
            'type' => 'es',
            'lang' => Yii::app()->language,
        ));
        $headerUrl = Yii::app()->controller->createUrl('//reportHtml/header', array(
            'rid' => $id,
            'childId' => $childid,
            'type' => 'es',
            'lang' => Yii::app()->language,
        ));
        $footerUrl = Yii::app()->controller->createUrl('//reportHtml/footer', array('lang' => Yii::app()->language,));
        $childInfo = ChildProfileBasic::model()->findByPk($childid);
        $data['child'] = $childInfo;
        $yid = 0;
        $semester = 1;
        $startyear = '';
        $optionGroupIds = array();
        if ($classid) {
            $classObj = IvyClass::model()->with('schoolInfo')->findByPk($classid);
            $grade = $classObj->classtype;
            $data['classTitle'] = $classObj->title;
            $data['campusTitle'] = $classObj->schoolInfo->title;
            $yid = $classObj->yid;
            $startyear = Calendar::model()->findByPk($yid)->startyear;
            $reportsData = self::getReportsData($yid,$id,$classid,$childid);
            $semester = $reportsData['semester'];
            $customTeacherList = $reportsData['customTeacherList'];
            $data['calendar_schooldays'] = $reportsData['calendarSchooldays'];
            $data['report_base'] = $reportsData['reportBase'];
            $data['report_attendance'] = $reportsData['reportAttendance'];
            $data['report'] = $reportsData['reportsOption'];
            $getTeacherList = self::getTeacherList($branchId, $startyear, $semester,$classid, $customTeacherList);
            $data['subjectTeacher'] = $getTeacherList['teacherListBySubject'];#科目老师
            $data['teachers'] = $getTeacherList['teachersInfo'];#科目老师
            $template_id = 0;
            $cate = current($reportsData['reportsOption']);
            $cate = end($cate);
            $cateModel = ReportsCategory::model()->findByPk($cate['category_id']);
            if ($cateModel != null) {
                $template_id = $cateModel->template_id;
            }
            if ($startyear < 2023) {
                if (!$template_id) {
                    $criteria = new CDbCriteria();
                    $criteria->compare('t.branch_id', $branchId);
                    $criteria->compare('t.for_age', $grade);
                    $criteria->compare('template.active', 1);
                    $template = ReportCardTemplateClass::model()->with('template')->find($criteria);
                    $template_id = !empty($template->template_id) ? $template->template_id : 0;
                }
                $res = ReportsCategory::model()->getCategoryData($branchId, $template_id, $uid);
            } else {
                $res = ReportsCategory::model()->getCategoryDataNew($branchId, $startyear, $grade, $uid, $semester, 'preview');
            }
            $data['root'] = array_values($res['roots']);
            $data['subs'] = $res['subs'];
            $data['items'] = $res['items'];
            $optionGroupIds = $res['optionGroupIds'];
        }
        //OPTION GROUP
        $getOptionsList = self::getOptionsList($yid, $optionGroupIds);
        $flag = $startyear + $semester;
        if($flag > 2024 + 1){
            $viewFile = Yii::getPathOfAlias('common.components.teaching.views.semesterReportDsV3').'.php';
        } else{
            $viewFile = Yii::getPathOfAlias('common.components.teaching.views.semesterReportDsV2').'.php';
        }
        $html = Yii::app()->getController()->renderFile($viewFile,
            array(
                'params'            => $params,
                'data'              => $data,
                'withToolBar'       => $withToolBar,
                'downloadActionUrl' => $downloadActionUrl,
                'optionsList'       => $getOptionsList['optionsListByGroupId'],#按照group_id
                'optionsList2'      => $getOptionsList['optionsListByOptionId'],#按照id分组
                'yid'               => $yid,
                'coverUrl'          => $coverUrl,
                'headerUrl'         => $headerUrl,
                'footerUrl'         => $footerUrl,
            ),
            true);
        return $html;
    }

    static function getReportsData($yid,$id,$classid,$childid){
        #每学期的天数
        $crit = new CDbCriteria;
        $crit->compare('yid', $yid);
        $calendar_schooldays = CalendarSchoolDays::model()->findAll($crit);
        $calendarSchooldays = array(1 => 0, 2 => 0);
        foreach ($calendar_schooldays as $item) {
            if ($item->semester_flag == 10) {
                $calendarSchooldays[1] += $item->schoolday;#第一学期天数
            } else {
                $calendarSchooldays[2] += $item->schoolday;#第二学期天数
            }
        }
//        $reports = ReportsData::model()->with('ext')->findAllByPk($id);
        $criteria = new CDbCriteria();
        $criteria->compare('class_id', $classid);
        $criteria->compare('child_id', $childid);
        $criteria->compare('semester', 1);
        $report = ReportsData::model()->find($criteria);
        $criteria = new CDbCriteria();
        $criteria->compare('class_id', $classid);
        $criteria->compare('child_id', $childid);
        $reports = ReportsData::model()->with('ext')->findAll($criteria);
        $reportsOne = ReportsData::model()->findByPk($id);
        #当前报告自定义的老师
        $customTeacherList = !empty($report->custom_teacher) ? json_decode($report->custom_teacher, true) : array();
        $reportsOption = array();
        $reportBase = array();
        $reportAttendance = array();
        $semester = $reportsOne->semester;
        if(!empty($reports)){
            foreach ($reports as $report) {
                #分数评语信息
                if (!empty($report->ext)) {
                    foreach ($report->ext as $ext) {
                        $reportsOption[$report->semester][$ext->category_id]['category_id'] = $ext->category_id;
                        $reportsOption[$report->semester][$ext->category_id]['memo'] = $ext->extra_data;
                        $reportsOption[$report->semester][$ext->category_id]['status'] = $ext->status;
                        $reportsOption[$report->semester][$ext->category_id]['option'] = CJSON::decode($ext->item_data);
                    }
                }
                $reportBase[$report->id]['startyear'] = $report->startyear;
                $reportBase[$report->id]['semester'] = $report->semester;
                $reportBase[$report->id]['pdf_file'] = $report->pdf_file;
                $reportBase[$report->id]['updated'] = !empty($report->updated) ? date('F d, Y', $report->updated) : '';
                #考勤信息
                $attendance_ext = CJSON::decode($report->attendance_ext);
                $reportAttendance[$report->semester]['absent'] = $attendance_ext['absent'];
                $reportAttendance[$report->semester]['tardy'] = $attendance_ext['tardy'];
                $reportAttendance[$report->semester]['present'] = $calendarSchooldays[$report->semester] - $attendance_ext['absent'];
                $reportAttendance[$report->semester]['support'] = self::reportSupportOfDs($attendance_ext['esl'], $attendance_ext['csl'], $attendance_ext['leap'], $attendance_ext['support'], $attendance_ext['other_data']);
            }
        }
        return array(
            'calendarSchooldays' => $calendarSchooldays,
            'customTeacherList'  => $customTeacherList,
            'reportBase'         => $reportBase,
            'reportsOption'      => $reportsOption,
            'reportAttendance'   => $reportAttendance,
            'semester'           => $semester,
        );
    }

    static function getTeacherList($branchId,$startyear,$semester,$classid,$customTeacherList){
        $teacherIds = array();
        $teachersInfo = array();
        $criteria = new CDbCriteria();
        $criteria->compare('classid', $classid);
        //自2022-2023学年开始 只展示主班老师
        if ($startyear >= 2022) {
            $criteria->compare('isheadteacher', 1);//只要主班老师
        }
        $criteria->order = 't.weight asc';
        #班级主班老师
        $classTeacher = ClassTeacher::model()->with('userWithProfile')->findAll($criteria);
        foreach ($classTeacher as $t) {
            $cnName = $t->userWithProfile->name;
            $enName = $t->userWithProfile->profile->first_name." ".$t->userWithProfile->profile->last_name;
            $enName = trim($enName);
            if ($enName == "") {
                $_name = isset($forPrint) && $forPrint ? sprintf('<span class="cn">%s</span>', $cnName) : $cnName;
            } elseif ($cnName == "") {
                $_name = isset($forPrint) && $forPrint ? sprintf('<span class="en">%s</span>', $enName) : $enName;
            } else {
                $_name = isset($forPrint) && $forPrint ? sprintf('<span class="cn">%s</span> <span class="en">%s</span>', $cnName, $enName) :
                    sprintf('%s %s', $cnName, $enName);
            }
            $teachersInfo[$t->teacherid] = array(
                'name'      => $t->userWithProfile->getNameFixedCn(0),
                'weight'    => $t->weight,
                'is_custom' => 0,
                'teacherid' => $t->teacherid,
                'position'  => $t->userWithProfile->profile->occupation ? $t->userWithProfile->profile->occupation->getName() : '',
            );
            $teacherIds[$t->teacherid] = $t->teacherid;
        }
        // 查找自定义老师
        if (!empty($customTeacherList)) {
            // 过滤重复的教师id
            $newCustomTeacherList = array();
            foreach ($customTeacherList as $item) {
                $teacherid = $item['teacherid'];
                if (!in_array($teacherid, $teacherIds)) {
                    $newCustomTeacherList[] = $item;
                    $teacherIds[$teacherid] = $teacherid;
                }
            }
            $customTeacherIds = array();
            $teacher2weight = array();
            foreach ($newCustomTeacherList as $customTeacher) {
                $customTeacherIds[] = $customTeacher['teacherid'];
                $teacher2weight[$customTeacher['teacherid']] = $customTeacher['weight'];
            }
            $customTeacherModelList = User::model()->findAllByPk($customTeacherIds);
            foreach ($customTeacherModelList as $customTeacherModel) {
                $teachersInfo[$customTeacherModel->uid] = array(
                    'name'      => $customTeacherModel->getNameFixedCn(0),
                    'weight'    => $teacher2weight[$customTeacherModel->uid],
                    'is_custom' => 1,
                    'teacherid' => $customTeacherModel->uid,
                    'position'  => $customTeacherModel->profile->occupation->getName()
                );
            }
        }
        //查询老师所教科目
        if (count($teacherIds) > 0) {
            $criter = new CDbCriteria();
            $criter->compare('teacher_uid', $teacherIds);
            $criter->compare('schoolid', $branchId);
            $teacherFlag = TeacherSubjectLink::model()->findAll($criter);
            $teacherFlag2 = array();
            if (!empty($teacherFlag)) {
                foreach ($teacherFlag as $k=> $item){
                    $teacherFlag2[$k] = $item->getAttributes();
                    $teacherFlag2[$k]['weight'] = !empty($teachersInfo[$item['teacher_uid']]['weight']) ? $teachersInfo[$item['teacher_uid']]['weight'] : 9;
                    $teacherFlag2[$k]['is_custom'] = !empty($teachersInfo[$item['teacher_uid']]['is_custom']) ? $teachersInfo[$item['teacher_uid']]['is_custom'] : 0;
                }
            }
        }
        // 排序
        uasort($teacherFlag2, function ($a, $b) {
            if ($a['weight'] == $b['weight']) {
                if ($a['is_custom'] == 1 && $b['is_custom'] == 0) {
                    return 1;
                }
                return 0;
            }
            return ($a['weight'] < $b['weight']) ? -1 : 1;
        });
        $teacherListBySubject = array();
        foreach ($teacherFlag2 as $item){
            $teacherListBySubject[$item['subject_flag']][] = $item['teacher_uid'];
        }
        return array(
            'teacherListBySubject' => $teacherListBySubject,
            'teachersInfo'         => $teachersInfo,
        );
    }

    static function getOptionsList($yid,$optionGroupIds){
        $criteria = new CDbCriteria();
        $criteria->compare('group.active', 1);
        $criteria->compare('group.id', $optionGroupIds);
        $criteria->order = ' group.id, t.weight';
        $options = ReportsOption::model()->with('group')->findAll($criteria);
        $optionsList = array();
        $optionsList2 = array();
        if (!empty($options)) {
            foreach ($options as $option) {
                if (!in_array($yid, array(117, 135)) && $option->label == 'NE') {
                    continue;
                }
                $optionsList[$option->group_id]['title'] = trim(CommonUtils::autoLang($option->group->title_cn, $option->group->title_en));
                $optionAttributes = $option->getAttributes();
                $optionAttributes['title'] = CommonUtils::autoLang($optionAttributes['title_cn'],$optionAttributes['title_en']);
                $optionAttributes['desc'] = CommonUtils::autoLang($optionAttributes['desc_cn'],$optionAttributes['desc_en']);
                $optionsList[$option->group_id]['options'][$option->id] = $optionAttributes;
                $optionsList2[$option->id] = $optionAttributes;
            }
        }

        return array(
            'optionsListByGroupId'  => $optionsList,
            'optionsListByOptionId' => $optionsList2
        );
    }

     static function getSchoolDetail($branchId){
        $criteria = new CDbCriteria();
        $criteria->compare('branchid', $branchId);
        $schoolInfo = BranchInfo::model()->find($criteria);
        $schoolDetail['school_title'] = CommonUtils::autoLang($schoolInfo->title_cn, $schoolInfo->title_en);
        $schoolDetail['address'] = CommonUtils::autoLang($schoolInfo->address_cn, $schoolInfo->address_en);
        $schoolDetail['email'] = $schoolInfo->email;
        $schoolDetail['tel'] = $schoolInfo->tel;
        return $schoolDetail;
    }

    //取学期报告数据
    static function getSemesterReportData($report, $forPrint=false){
        $data = array();
        $data['report'] = $report->getAttributes();
        $photoIds = $data['photos'] = array();
        foreach ($report->items as $_item) {
            $data['items'][$_item->subcategory] = $_item->getAttributes();
            if(empty($data['items'][$_item->subcategory]['content'])){
                $data['items'][$_item->subcategory]['langClass'] = 'en';
            }else{
                $content = trim( $data['items'][$_item->subcategory]['content'] );
                $content = substr($content, 0, 32);
                if(CommonUtils::hasCNChars($content) > 0 ){
                    $data['items'][$_item->subcategory]['langClass'] = 'cn';
                }else{
                    $data['items'][$_item->subcategory]['langClass'] = 'en';
                }

            }
            if($_item->media_id)
                $photoIds[] = $_item->media_id;
        }

        ChildMedia::setStartYear($report->startyear);
        if($photoIds){
            $photoObjs = ChildMedia::model()->findAllByPk($photoIds);
            foreach ($photoObjs as $photo) {
                $data['photos'][$photo->id] = $photo->getMediaUrl(false);
            }
        }

        $criteria = new CDbCriteria();
        $criteria->compare('classid', $report->classid);
        $criteria->order = 't.weight asc';
        $classTeacher = ClassTeacher::model()->with('userWithProfile')->findAll($criteria);

        foreach($classTeacher as $t){
            $cnName = $t->userWithProfile->name;
            $enName = $t->userWithProfile->profile->first_name . " " . $t->userWithProfile->profile->last_name;
            $enName = trim($enName);

            if($enName == ""){
                $_name = $forPrint ? sprintf('<span class="cn">%s</span>', $cnName) : $cnName;
            }elseif($cnName == ""){
                $_name = $forPrint? sprintf('<span class="en">%s</span>', $enName) : $enName;
            }else{
                $_name = $forPrint? sprintf('<span class="cn">%s</span> <span class="en">%s</span>',
                    $cnName, $enName) :
                    sprintf('%s %s', $cnName, $enName);
            }
            $data['teachers'][] = $_name;
        }

        $data['child'] = ChildProfileBasic::model()->findByPk($report->childid);

        $classObj = IvyClass::model()->with('schoolInfo')->findByPk($report->classid);
        $data['classTitle'] = $classObj->title;
        $data['classtype'] = $classObj->classtype;
        $data['campusTitle'] = $classObj->schoolInfo->title;

        Yii::import('common.components.reports.*');
        $template = new IvySemesterReport('Ivy01');
        $data['learningDomains'] = $template->getLearningDomains();

        return $data;
    }

    //取学期报告数据
    static function getSemesterReportData2($report, $forPrint=false){
        Yii::import('common.models.learning.*');
        Yii::import('common.models.wechat.*');
        Yii::import('common.models.classTeacher.*');

        $data = array();
        $data['report'] = $report->getAttributes();
        $photoIds = $data['photos'] = array();
        $imgCropper = array();
        foreach ($report->items as $_item) {
            $data['items'][$_item->subcategory] = $_item->getAttributes();
            if(empty($data['items'][$_item->subcategory]['content'])){
                $data['items'][$_item->subcategory]['langClass'] = 'en';
            }else{
                $content = trim( $data['items'][$_item->subcategory]['content'] );
                $content = substr($content, 0, 32);
                if(CommonUtils::hasCNChars($content) > 0 ){
                    $data['items'][$_item->subcategory]['langClass'] = 'cn';
                }else{
                    $data['items'][$_item->subcategory]['langClass'] = 'en';
                }

            }
            if($_item->media_id) {
                $photoIds[] = $_item->media_id;
                if ($_item->img_cropper) {
                    $imgCropper[] = $_item->media_id;
                }
            }
        }

        $criteria = new CDbCriteria();
        $criteria->compare('childid', $report->childid);
        $criteria->compare('yid', $report->yid);
        $criteria->compare('semester', $report->semester);
        $criteria->compare('classid', $report->classid);
        $criteria->compare('status', 1);
        $criteria->index='lid';
        $childLearningDomains = LearningSemester::model()->findAll($criteria);
        $photoCropper = array();
        foreach ($childLearningDomains as $item) {
            if($item->intro_img) {
                $photoIds[] = $item->intro_img;
                $photoCropper[$item->intro_img] =  $item->intro_img_cropper;
            }
            if($item->items_img) {
                $photoIds[] = $item->items_img;
                $photoCropper[$item->items_img] =  $item->items_img_cropper;
            }
        }

        ChildMedia::setStartYear($report->startyear);
        if($photoIds){
            $photoObjs = ChildMedia::model()->findAllByPk($photoIds);
            foreach ($photoObjs as $photo) {
                $data['photos'][$photo->id] = $photo->getOriginal();
                if (in_array($photo->id, $imgCropper)) {
                    $data['photos'][$photo->id] = $photo->getOriginal(true);
                }
            }
        }

        $exSchools = array('BJ_QFF');
        $data['cdName'] = '';
        $data['teachersName'] = array();
        $data['signature'] = array();
        if (in_array($report->schoolid, $exSchools)) {
            $cdid = 54;
            if ($report->schoolid == "BJ_QFF") {
//                $cdid = 182;
                $cdid = 561;
            }
            $criteria = new CDbCriteria();
            $criteria->compare('t.level', 1);
            $criteria->compare('profile.occupation_en', $cdid);
            $criteria->compare('profile.branch', $report->schoolid);
            $cdObj = User::model()->with('profile')->find($criteria);
            $data['cdName'] = $cdObj ? $cdObj->getNameLang() : '';
        }

        $criteria = new CDbCriteria();
        $criteria->compare('classid', $report->classid);
        $criteria->order = 't.weight asc';
        $classTeacher = ClassTeacher::model()->with('userWithProfile')->findAll($criteria);
        $signature = array();
        foreach($classTeacher as $t){
            $cnName = $t->userWithProfile->name;
            $enName = $t->userWithProfile->profile->first_name . " " . $t->userWithProfile->profile->last_name;
            $enName = trim($enName);

            if($enName == ""){
                $_name = $forPrint ? sprintf('<span class="cn">%s</span>', $cnName) : $cnName;
            }elseif($cnName == ""){
                $_name = $forPrint? sprintf('<span class="en">%s</span>', $enName) : $enName;
            }else{
                $_name = $forPrint? sprintf('<span class="cn">%s</span> <span class="en">%s</span>',
                    $cnName, $enName) :
                    sprintf('%s %s', $cnName, $enName);
            }
            $data['teachers'][] = $_name;
            $data['teachersName'][] = $t->userWithProfile->getNameLang();
            $signature[] = $t->userWithProfile->uid;
            // if(!in_array($report->schoolid, $exSchools)) {
            //     $data['teachersName'][] = $t->userWithProfile->getNameLang();
            //     $signature[] = $t->userWithProfile->uid;
            // }
        }

        if($signature){
            $criteria = new CDbCriteria();
            $criteria->compare('belong_user', $signature);
            $criteria->compare('status', 1);
            $userSignature = WxworkFile::model()->findAll($criteria);

            if($userSignature){
                $osscs = CommonUtils::initOSSCS('private');
                foreach ($userSignature as $val){
                    $style = 'image/resize,h_45';
                    $data['signature'][] = $osscs->getImageUrl($val->src, $style);
                }
            }
        }

        $data['child'] = ChildProfileBasic::model()->findByPk($report->childid);

        $classObj = IvyClass::model()->with('schoolInfo')->findByPk($report->classid);
        $branchType = Branch::branchType();
        $data['classTitle'] = $classObj->title;
        $data['classtype'] = $classObj->classtype;
        $data['campusTitle'] = $classObj->schoolInfo->info->getLangContent('title');
        $data['campusT'] = $classObj->schoolInfo->title;
        $data['campusType'] = $branchType[$classObj->schoolInfo->group];
        if (in_array($report->schoolid, array("CD_LT", "CD_LH"))) {
            $data['campusType'] = $branchType[20];
        }
        $data['campusGroup'] = $classObj->schoolInfo->group;

        $schoolType = 'ivy';
        $version = 1;
        if ($classObj->schoolInfo->branchid == "BJ_QFF") {
            $schoolType = 'qf';
        }
        if ($report->startyear >= 2021) {
            $version = 2;
        }

        $learningDomainsType = $classObj->classtype;
        // 查找是否自定义了学习领域标准
        if ($report && $report->learning_type) {
            $learningDomainsType = $report->learning_type;
        }


        $criteria = new CDbCriteria();
        $criteria->compare('t.type', array($learningDomainsType, 'ibs'));
        $criteria->compare('t.status', 1);
        // $criteria->compare('t.id', array_keys($childLearningDomains));
        $criteria->compare('school_type', $schoolType);
        $criteria->compare('version', $version);
        $criteria->order='t.sort asc';
        $learningDomains = LearningDomains::model()->with('items')->findAll($criteria);

        $criteria = new CDbCriteria();
        $criteria->compare('childid', $report->childid);
        $criteria->compare('yid', $report->yid);
        $criteria->compare('semester', $report->semester);
        $criteria->compare('classid', $report->classid);
        $criteria->compare('status', 1);
        $criteria->index='itemid';
        $childLDItems = LearningSemesterItems::model()->findAll($criteria);

        $data['learningDomains'] = $learningDomains;
        $data['childLearningDomains'] = $childLearningDomains;
        $data['childLDItems'] = $childLDItems;
        $data['flagnum'] = $report->startyear + $report->semester;
        $data['classType'] = $learningDomainsType;

        return $data;
    }

    //查看某校园之前的校历ID
    static function getHistoricalStartYear($branchId, $includeCurrent=false){
        Yii::import('common.models.calendar.*');
        $schoolCalendars = CalendarSchool::model()->findAllByAttributes(array(
            'branchid' => $branchId
        ));
        foreach($schoolCalendars as $_sc){
            if( $_sc->is_selected ){
                $current = $_sc->startyear;
            }
            $data[$_sc->startyear] = $_sc->yid;
        }
        ksort($data);
        foreach($data as $_startyear=>$_yid){
            if(($current < $_startyear) || ($current == $_startyear && !$includeCurrent) ){
                unset($data[$_startyear]);
            }
        }
        return $data;
    }

    //获取历史记录中曾经就读于某班的孩子ID
    static function getHistoricalStudentsByClass($classid=0, $branchid=''){
        if($classid && $branchid){
            Yii::import('common.models.portfolio.ChildStudyHistory');
            $crit = new CDbCriteria();
            $crit->select = 'childid';
            $crit->compare('classid', $classid);
            $crit->compare('schoolid', $branchid);
            $histories = ChildStudyHistory::model()->findAll($crit);
            $data = CHtml::listData($histories, 'childid', 'childid');
            return $data;
        }
    }

    /**
     * 生成中学学期报告HTML
     * @param $params
     * @param $schools
     * @param int $withToolBar
     * @return string
     * @throws CException
     * @notice 展示学课程状态为 1和99的课程 其中状态99的课程只展示打过分的（不打分的不显示）
     */
    public static function getReportOfDs($params, $schools, $withToolBar = 0)
    {
        $report_id = $params['id'];
        $class_id = $params['classid'];
        $html = "";
        $criter = new CDbCriteria();
        $criter->compare('classid', $class_id);
        $classInfo = IvyClass::model()->find($criter);
        $criteria = new CDbCriteria();
        $criteria->compare('calendar', $params['yid']);
        $criteria->compare('schoolid', $schools);
        $criteria->order = 'start_time ASC';
        $report_list = AchievementReport::model()->findAll($criteria); //可评估的期数总数
        #长期评估的ReportId
        $longReport = array();
        foreach ($report_list as $_report) {
            if ($_report->type == 1) {
                if ($_report->cycle == 2) {
                    $kk = 1;
                    $name = CommonUtils::autoLang('第一学期', 'Semester 1');
                } else {
                    $kk = 2;
                    $name = CommonUtils::autoLang('第二学期', 'Semester 2');
                }
                $longReport[$kk] = array('report_id' => $_report->id, 'name' => $name);
            }
        }
        $report = AchievementReport::model()->findByPk($params['id']); //本期信息，校长评语等
        Yii::import('common.models.timetable.*');
        $selected_class_id = '';
        if($report) {
            $cycle = $report->cycle;
            if (in_array($report->type, array(1,2))){
                $criteria = new CDbCriteria();
                $criteria->compare('t.calender', $params['yid']);
                $criteria->compare('t.childid', $params['childid']);
                // $criteria->compare('t.classid', $params['classid']);
                if ($withToolBar != 1) {
//                    $criteria->compare('t.is_stat', 1);
                }
                $criteria->order = "report.start_time";
                $reportchild = AchievementReportChild::model()->with("report")->findAll($criteria); //辅导员评语,csa评语,出勤天数
                $additionalComment = array();#班主任&CAS评语
                $teacher = array();
                foreach ($reportchild as $_reportchild){
                    if($_reportchild->report_id == $params['id']){
                       $selected_class_id =  $_reportchild->classid;
                    }
                    #获取当前的数据
                    if($_reportchild->report_id == $report->id){
                        $additionalComment['cas_ee_comment'] = $_reportchild->cas_ee_comment;
                        $additionalComment['cas_ee_user'] = $_reportchild->cas_user;
                        $teacher['cas_user'] = $_reportchild->cas_user;
                        $additionalComment['counselor_message_en'] = $_reportchild->counselor_message_en;
                        $additionalComment['counselor_user'] = $_reportchild->counselor_user;
                        $teacher['counselor_user'] = $_reportchild->counselor_user;
                    }
                }
                //评语老师和校长协调员信息
                $leaders = json_decode($report->leaders, true);
                $teacher = array_merge($leaders,$teacher);
                $crit = new CDbCriteria();
                $crit->index = 'uid';
                $crit->compare('uid', $teacher);
                $teacher_name = User::model()->findAll($crit);
                $teacherName = array();
                foreach ($teacher as $k => $v) {
                    $teacherName[$k] = ($teacher_name[$v]) ? $teacher_name[$v]->getNameFixedCn(0) : "";
                }
                $crit = new CDbCriteria();
                $crit->compare('reportid', $report->id);
                $crit->compare('childid', $params['childid']);
                $crit->order = 'reportCourse.weight ASC';//
                $childcourse = AchievementReportChildCourse::model()->with('reportCourse')->findAll($crit);
                $reportCourseInfoCourseid = array();#已打分课程信息所有Courseid
                $assigned = array(); // 获取孩子已分配且打过分的所有课程
                $standard = array();//获取使用课程标准打过分的课程
                foreach ($childcourse as $k=>$_childcourse){
                    $reportCourseInfoCourseid[] = $_childcourse->courseid;
                    $assigned[] = $_childcourse->timetable_records_code;
                    $standard[$_childcourse->courseid] = $_childcourse->timetable_records_code;
                }
                $reportCourseStandard = AchievementReportCourseStandard::model()->findAllByAttributes(array('courseid' => $reportCourseInfoCourseid));
                $reportCourseStandardByCourseid = array();
                $reportCourseStandardIds = array();
                foreach ($reportCourseStandard as $_reportCourseStandard){
                    $reportCourseStandardByCourseid[$_reportCourseStandard->courseid][] = $_reportCourseStandard->getAttributes();
                    $reportCourseStandardIds[$_reportCourseStandard->id] = $_reportCourseStandard->id;
                }
                #分数id对应的分值 NA NE Pass Fail Pass
                $crit = new CDbCriteria();
                $crit->compare('kid', $reportCourseStandardIds);
                $crit->index = 'id';
                $courseScoresFractionById = AchievementCourseScores::model()->findAll($crit);
                $courseScoresFractionByIdArr = array();
                foreach ($courseScoresFractionById as $k => $_courseScoresFractionById) {
                    $courseScoresFractionByIdArr[$k] = $_courseScoresFractionById->getAttributes();
                }

                $report_lists = array();
                $options = array();
                $options_by_timetable_records_code = array();
                $introduction = array();
                foreach ($reportchild as $_repoert) {
                    $report_lists[] = $_repoert->report_id;
                }

                // ---------------查询类别总的介绍  stat -----------------//
                #最终成绩和年终分数
                $crit = new CDbCriteria();
                $crit->compare('calender_id', $params['yid']);
                $crit->compare('childid', $params['childid']);
                $childTotal = AchievementReportChildTotal::model()->findAll($crit);
                $fitotle = array();
                $final = array();#最终分数的id
                foreach($childTotal as $_final){
                    $frecyion_total = json_decode($_final->frecyion_total, true);
                    foreach($frecyion_total as $k=>$_frecyion_total){
                        $final[$k] = $_frecyion_total;
                    }
                    $fitotle[$_final->timetable_records_code] = $frecyion_total;#最终成绩
                    $fitotleNum[$_final->timetable_records_code] = $_final->oprion_value; // 年终分数
                }
                $final_list = array();
                foreach($courseScoresFractionById as $_childScores){
                    if(in_array($_childScores->id, $final)){
                        $final_list['x' . $_childScores->id] = nl2br($_childScores->getName());
                    }
                }
                if ($report_lists) {
                    $crit = new CDbCriteria();
                    $crit->compare('report_id', $report_lists);
                    $crit->compare('childid', $params['childid']);
                    $fFractions = AchievementReportChildFraction::model()->findAll($crit);
                    if ($fFractions) {
                        foreach ($fFractions as $_fFractions) {
                            $options[$_fFractions->courseid][$_fFractions->optionid][$_fFractions->report_id] = $_fFractions->value;
                            $options_by_timetable_records_code[$_fFractions->timetable_records_code][$_fFractions->report_id][] = $_fFractions->value;
                        }
                        foreach ($fFractions as $_fFractions) {
                            if ($_fFractions->courseScoresid) {
                                $introduction['x' . $_fFractions->value] = nl2br($_fFractions->courseScoresid->getName());
                            }
                        }
                        $childScoresList = $final_list + $introduction;
                    }
                }

                $childData = ChildProfileBasic::model()->findByPk($params['childid']);
                // ---------------   end   -----------------//
                $downloadActionUrl = Yii::app()->createAbsoluteUrl('//mteaching/report/downloadReportDs', array(
                        'branchId' => $schools
                    ));
                if ($report->type == 1) {#24次评估
                    $viewFile = Yii::getPathOfAlias('common.components.teaching.views.reportDsNewBig') . '.php';
                } else {#13次评估
                    $viewFile = Yii::getPathOfAlias('common.components.teaching.views.reportDsNewSmall') . '.php';
                }
                $yid = $params['yid'];
                $childid = $params['childid'];
                $timeTable = Timetable::model()->findByAttributes(array('yid' => $yid, 'schoolid' => $schools));
                $childcourse2 = array();
                if ($timeTable) {
                    $special = array('2206301', '2207301', '2208301', '0106401', '0106402', '0107401', '0108401', '0106501', '0107501', '0108501');
                    $tid = $timeTable->id;
                    $criteria = new CDbCriteria();
                    $criteria->compare('tid', $tid);
                    $criteria->compare('child_id', $childid);
                    $criteria->compare('status', array(1,99));
                    // 排除特殊名单的课程
                    $criteria->addNotInCondition('course_code', $special);
                    $criteria->index = "course_id";
                    $courseStudents = TimetableStudentData::model()->findAll($criteria);
                    $course_ids = array_keys($courseStudents);
                    //获取所有课程对应的课程标准
                    $TimetableCoursesData = TimetableCourses::model()->findAllByPk($course_ids);
                    $code_report_course_id = array();
                    $one_report_course_id = 0;
                    foreach ($TimetableCoursesData as $v){
                        $cycleField = 'period' . $cycle;
                        if (isset($v->$cycleField) && $v->$cycleField) {
                            $one_report_course_id = $v->$cycleField;
                        }
                        $code_report_course_id[$v->course_code] = $v->report_course_id;//获取课程code对应的课程标准id 不同的课程课程对应相同的课程标准 例如高级课和初级课分配绑定的都是一个标准
                    }
                    $sampleProgerssReport = false;
                    // 根据标准判断是否为 sample progress report
                    if($one_report_course_id) {
                        $achievementReportCourse = AchievementReportCourse::model()->findByPk($one_report_course_id);
                        if ($report->startyear >= 2024 && $achievementReportCourse && in_array($achievementReportCourse->td, array(6))) {
                            $sampleProgerssReport = true;
                        }
                    }
                    $i = count($childcourse);
                    $termCourse = array();
                    $term = $report->cycle > 2 ? 2 : 1;
                    $in_category_where = $term==1 ? array('A','B') : array('C','D');
                    if($tid>=5){
                        $in_category_where = array('A','B');
                    }
                    $category_data = Yii::app()->db->createCommand()
                        ->select("sa.id, sa.course_id, sa.course_code, sa.class_room_name, ca.category, ca.weekday,
                ca.period, ca.timeslot_flag,sa.status")
                        ->from('mimssub.ivy_timetable_student_data sa')
                        ->where(array('in','child_id',$childid))
                        ->andwhere(array('in','category',$in_category_where))
                        ->andwhere('sa.status in (1,99)')
                        ->andwhere('ca.status=1')
                        ->andwhere('sa.tid = :tid',array(':tid'=>$tid))
                        ->leftJoin('mimssub.ivy_timetable_course_data ca', 'sa.course_id=ca.course_id')
                        ->queryAll();
                    $category_data_by_course_id = array();
                    $category_data_by_status = array();
                    foreach ($category_data as $value){
                        $category_data_by_course_id[$value['course_id']] = $value['course_code'];
                        $category_data_by_status[$value['status']][$value['course_id']] = $value['course_code'];
                    }
                    //是否更换了班级
                    $is_change_class = 0;
                    $child_class_id = $childData->classid;#孩子当前的班级
                    if(!empty($selected_class_id) && $child_class_id != $selected_class_id){
                        $is_change_class = 1;
                    }
                    //换课的情况 不需要查询timetable的数据否则课程会冲突
                    if($is_change_class === 1){
                        foreach ($childcourse as $item){
                            $report_child_course[] = $item->timetable_records_code;
                        }
                        if(!empty($report_child_course)){
                            $termCourse = array_unique($report_child_course);
                        }
                    }else{
                        foreach ($courseStudents as $item) {
                            // 排除非当前学期的课程
                            if($item->term != 0 && !array_key_exists($item->course_id, $category_data_by_course_id)){
                                continue;
                            }
                            //排除当前期没有成绩且状态为99的课程
                            if(!empty($category_data_by_status[99]) && in_array($item->course_code, $category_data_by_status[99], true)
                                && empty($options_by_timetable_records_code[$item->course_code][$report_id])){
                                continue;
                            }
                            //排除当前学期没有成绩 且 相同标准下有另外一门课有成绩 的课程
//                            $code_report_course_id[$item->course_code];//课程code对应的课程标准
//                            $standard;//已经使用课程标准打分的课程
                            if(empty($options_by_timetable_records_code[$item->course_code][$report_id]) && !empty($standard[$code_report_course_id[$item->course_code]])){
                                continue;
                            }
//                            //排除当前期没有成绩但是在其他期有成绩的
//                            if(empty($options_by_timetable_records_code[$item->course_code][$report_id]) && !empty($options_by_timetable_records_code[$item->course_code])){
//                                continue;
//                            }
                            // 排除已做报告的课程
                            $course_code = $item->tableCourse->course_code;
                            $termCourse[] = $course_code;
                            if (in_array($course_code, $assigned, false)) {
                                continue;
                            }
                            // 查询报告标准
                            $cycleField = 'period' . $cycle;
                            if (isset($item->tableCourse->$cycleField) && $item->tableCourse->$cycleField) {
                                $report_course_id = $item->tableCourse->$cycleField;
                            } else {
                                $report_course_id = $item->tableCourse->report_course_id;
                            }
                            $stand = array();
                            $standards = AchievementReportCourseStandard::model()->findAllByAttributes(array('courseid' => $report_course_id));
                            if ($standards) {
                                foreach ($standards as $standard_item) {
                                    $stand[] = $standard_item->title_cn;
                                }
                            }
                            if (!$stand) {
                                continue;
                            }
                            $childcourse2[$i]['teachername'] = $item->courseTeacher->user?$item->courseTeacher->user->getNameFixedCn(0):'';
                            $childcourse2[$i]['coursename'] = $item->tableCourse->getTitle();
                            $childcourse2[$i]['stand'] = $stand;
                            $i++;
                        }
                    }
                }

                $goalData = array();
                $homeroomTeacher = array();
                $flag = $report->startyear + $report->cycle;
                // 新的学期报告模板
                if ($sampleProgerssReport) {
                    // 查找学生目标数据
                    $url = 'learningGoal/studentGoalData';
                    $requestData = array(
                        'yid' => $yid,
                        'school_id' => $schools,
                        'child_id' => $childid,
                        'quarter' => $cycle,
                    );
                    $res = CommonUtils::requestDsOnline($url, $requestData);
                    $homeroomCode = !empty($res['data']['homeroom_code']) ? $res['data']['homeroom_code'] : array();
                    foreach ($courseStudents as $item) {
                        if (in_array($item->course_code, $homeroomCode)) {
                            $homeroomTeacher[] = $item->courseTeacher->user?$item->courseTeacher->user->getNameLang():'';
                        }
                    }
                    $goalData = $res['code'] == 0  ? $res['data'] : array();
                    $viewFile = Yii::getPathOfAlias('common.components.teaching.views.reportDsSampleProgress') . '.php';
                }else if($flag >= 2024 + 4 && in_array($report->cycle,array(2,4)) ){
                    #2024年第4期评估以后的2、4次评估使用新模板
                    $viewFile = Yii::getPathOfAlias('common.components.teaching.views.reportDsSampleProgress2') . '.php';
                }
                $coverUrl = Yii::app()->controller->createUrl('//reportHtml/cover', array(
                    'rid' => $params['id'],
                    'childId' => $childid,
                    'type' => 'ms',
                    'lang' => Yii::app()->language,
                ));
                $headerUrl = Yii::app()->controller->createUrl('//reportHtml/header', array(
                    'rid' => $params['id'],
                    'childId' => $childid,
                    'type' => 'ms',
                    'lang' => Yii::app()->language,
                ));
                $footerUrl = Yii::app()->controller->createUrl('//reportHtml/footer', array('lang' => Yii::app()->language,));
                $html = Yii::app()->getController()->renderFile($viewFile,
                    array(
                        'report'                         => $report,
                        'reportchild'                    => $reportchild, #辅导员评语,csa评语,出勤天数
                        'childcourse'                    => $childcourse,#已经打分的课程数据 已排序
                        'report_list'                    => $report_list,#可评估的期数总数
                        'teacherName'                    => $teacherName,#中学校长和协调员& CAS，班主任等评语填写人的信息
                        'options'                        => $options,#本学年所有打分数信息 按照courseid分组的 courseid=>[report_course_standard_id=>reportid=>course_scores_id]
                        'params'                         => $params,#传递过来的参数
                        'data'                           => $childData,
                        'introduction'                   => $childScoresList,#评估标准的介绍
                        'withToolBar'                    => $withToolBar,
                        'downloadActionUrl'              => $downloadActionUrl,
                        'childcourse2'                   => $childcourse2,#没有打分的数据
                        'termCourse'                     => $termCourse,#本学期可展示的课程
                        'final'                          => $fitotle,#最终成绩水平
                        'fitotleNum'                     => $fitotleNum,#年终分数
                        'goalData'                       => $goalData,#目标数据 短期报告使用
                        'homeroomTeacher'                => $homeroomTeacher,#班主任信息  短期报告使用
                        //新的长期评估数据
                        'longReport'                     => $longReport,#长期报告的信息
                        'reportCourseStandardByCourseid' => $reportCourseStandardByCourseid,#各科评估标准 ABCD
                        'courseScoresFractionByIdArr'    => $courseScoresFractionByIdArr,#分数id对应的分值 NA NE Pass Fail Pass
                        'additionalComment'              => $additionalComment,#CAS & 班主任评语
                        'classInfo'                      => $classInfo,#班级信息
                        'coverUrl'                       => $coverUrl,
                        'headerUrl'                      => $headerUrl,
                        'footerUrl'                      => $footerUrl,
                    ), true);
            }else{
                $html = ContentFetcher::getReportOfDs2($params, $schools, $withToolBar);
            }
        }
        return $html;
    }

    public static function getReportOfDs2($params, $schools, $withToolBar = 0)
    {
        $html = "";
        $criteria = new CDbCriteria();
        $criteria->compare('calendar', $params['yid']);
        $criteria->compare('schoolid', $schools);
        $criteria->order = 'start_time ASC';
        $report_list = AchievementReport::model()->findAll($criteria); //期数总数
        $report = AchievementReport::model()->findByPk($params['id']); //校长评语和期数
        if($report){
            $criteria = new CDbCriteria();
            $criteria->compare('t.calender', $params['yid']);
            $criteria->compare('t.childid', $params['childid']);
            //$criteria->compare('t.classid', $params['classid']);
            $criteria->order = "report.start_time";
            $reportchild = AchievementReportChild::model()->with("report")->findAll($criteria); //辅导员评语和出勤天数

            $crit = new CDbCriteria();
            $crit->compare('reportid', $report->id);
            $crit->compare('childid', $params['childid']);
            $crit->order = 'reportCourse.weight ASC' ;//
            $childcourse = AchievementReportChildCourse::model()->with('reportCourse')->findAll($crit);

            $teacher = json_decode($report->leaders,true);

            $crit = new CDbCriteria();
            $crit->index = 'uid';
            $crit->compare('uid', $teacher);
            $teacher_name = User::model()->findAll($crit);
            $teacherName = array();
            foreach($teacher as $k=>$v){
                $teacherName[$k]  =  ($teacher_name[$v]) ? $teacher_name[$v]->getName() : "";
            }

            $report_lists = array();
            $options = array();
            $introduction = array();
            foreach($report_list as $_repoert){
                $report_lists[] =$_repoert->id;
            }

            // ---------------查询类别总的介绍  stat -----------------//
            $crit = new CDbCriteria();
            $crit->compare('calender_id', $params['yid']);
            $crit->compare('childid', $params['childid']);
            $childTotal = AchievementReportChildTotal::model()->findAll($crit);

            $fi = array();
            $final = array();
            foreach($childTotal as $_final){
                $fi[] = json_decode($_final->frecyion_total, true);
            }

            foreach($fi as $_finals){
                foreach($_finals as $k=>$_final){
                    $final[$k] = $_final;
                }
            }

            $crit = new CDbCriteria();
            $crit->compare('id', $final);
            $childScores = AchievementCourseScores::model()->findAll($crit);
            $final_list = array();
            foreach($childScores as $_childScores){
                $final_list['x' . $_childScores->id] = nl2br($_childScores->getName());
            }

            $crit = new CDbCriteria();
            $crit->compare('report_id', $report_lists);
            $crit->compare('childid', $params['childid']);
            $fFractions = AchievementReportChildFraction::model()->findAll($crit);

            $data = ChildProfileBasic::model()->findByPk($params['childid']);

            foreach($fFractions as $_fFractions){
                $options[$_fFractions->courseid][$_fFractions->optionid][$_fFractions->report_id]= $_fFractions->value;
            }

            foreach($fFractions as $_fFractions) {
                if($_fFractions->courseScoresid){
                    $introduction['x' . $_fFractions->value] = nl2br($_fFractions->courseScoresid->getName());
                }
            }
            $childScoresList = $final_list + $introduction;
            // ---------------   end   -----------------//
            $downloadActionUrl = Yii::app()->createAbsoluteUrl(
                '//mteaching/report/downloadReportDs',
                array(
                    'branchId'=>$schools
                ));

            $viewFile = Yii::getPathOfAlias('common.components.teaching.views.reportDs') . '.php';

            $html = Yii::app()->getController()->renderFile($viewFile,
                array(
                    'report' => $report,
                    'reportchild' => $reportchild,
                    'childcourse' => $childcourse,
                    'report_list' => $report_list,
                    'teacherName' => $teacherName,
                    'options' => $options,
                    'params' => $params,
                    'data' => $data,
                    'introduction' => $childScoresList,
                    'withToolBar' => $withToolBar,
                    'downloadActionUrl' => $downloadActionUrl,
                ),true);
        }
        return $html;
    }
}
