<?php
$this->renderPartial('plug/' . $dirname . '/' . $template, array('data' => $data));
// 判断是否为发起或是审批流程
if ($data['workflow']->useRole && $data['workflow']->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_UNOP || $template != 'show') {
    $params = '{backdrop: "static"}';
} else {
    $params = '';
}
if (isset($data['jsFunction'])):
    if (isset($data['showContract'])) {
        $str = <<<js
        <script language="javascript" type="text/javascript"> 
            {$data['jsFunction']} = function(){ 
                $('#{$data['modalNameId']}').modal($params);
            }
        </script>
js;
    } else {
    $str = <<<js
    <script language="javascript" type="text/javascript"> 
        {$data['jsFunction']} = function(){ 
            $('#{$data['modalNameId']}').modal($params);
            head.Util.ajaxForm();
        }
    </script>
js;
}
    echo $str;
endif;
