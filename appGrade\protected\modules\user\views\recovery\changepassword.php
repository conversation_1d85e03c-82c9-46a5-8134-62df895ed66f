<?php
$this->pageTitle = Yii::app()->name . ' - ' . Yii::t("user", "Change Password");
$this->breadcrumbs = array(
    Yii::t("user", "Login") => array('/user/login'),
    Yii::t("user", "Change Password"),
);
?>

<h1><?php echo Yii::t("user", "Change Password"); ?></h1>

<div class="span-12">
    <div class="form">
        <?php echo CHtml::beginForm(); ?>

        <p class="note"><?php echo Yii::t("user", 'Fields with <span class="required">*</span> are required.'); ?></p>
        <?php echo CHtml::errorSummary($form); ?>

        <dl class="row">
            <dt>
            <?php echo CHtml::activeLabelEx($form, 'password'); ?>
            </dt>
            <dd>
                <?php echo CHtml::activePasswordField($form, 'password'); ?>
                <p class="hint" id="hint_pass1">
                    <?php // echo Yii::t("user", "Minimal password length 4 symbols."); ?>
                    <?php echo Yii::t("user", "Combination of letters and numbers, min 8-length."); ?>
                </p>
                <?php echo CHtml::error($form, 'password'); ?>
            </dd>
        </dl>

        <dl class="row">
            <dt>
            <?php echo CHtml::activeLabelEx($form, 'verifyPassword'); ?>
            </dt>
            <dd>
                <?php echo CHtml::activePasswordField($form, 'verifyPassword', array('disabled' => 'disabled')); ?>
                <p class="hint hint-error" id="hint_pass2">
                    <span class="hide">
                        <?php echo Yii::t("user", "Password do not match."); ?>
                    </span>
                </p>
                <?php echo CHtml::error($form, 'verifyPassword'); ?>
            </dd>
        </dl>


        <dl class="row submit">
            <dt>&nbsp;</dt>
            <dd><?php echo CHtml::submitButton(Yii::t("user", "Save"), array('class' => 'bigger', 'style' => 'width:120px;', 'id' => 'updatePassButton', 'disabled' => 'disabled')); ?></dd>
        </dl>

        <?php echo CHtml::endForm(); ?>
    </div><!-- form -->
</div>
<div class="span-12 last">
</div>

<script lang="text/javascript">

    $(document).ready(function() {

        var pass1 = $('#UserChangePassword_password'),
        pass2 = $('#UserChangePassword_verifyPassword'),
        submit = $('#updatePassButton');

        pass1.focus();
        $('.form input:password').val('');
        pass1.keyup(function(){

            var showError = false;
            var pv = pass1.val();
            if(pv.length >7){
                var num = new RegExp(/.*[0-9]+/).test(pv) ? true : false;
                var let = new RegExp(/.*[a-zA-Z]+/).test(pv) ? true : false;
                if(num == true && let == true){
                    pass2.removeAttr('disabled');
                    $('#hint_pass1').removeClass('hint-error');
                }else{
                    showError = true;
                }
            }else{
                showError = true;
                if(pv.length == 0){
                    pass2.val('');
                }
            }
            if(showError == true){
                pass2.attr('disabled','true');
                $('#hint_pass1').addClass('hint-error');
                submit.attr('disabled','true');
            }


        });

        pass2.keyup(function(){
            if(pass2.val() == pass1.val()){
                submit.removeAttr('disabled');
                $('#hint_pass2 span').addClass('hide');
            }
            else{
                submit.attr('disabled','true');

                $('#hint_pass2 span').removeClass('hide');
            }
        });


    });


</script>