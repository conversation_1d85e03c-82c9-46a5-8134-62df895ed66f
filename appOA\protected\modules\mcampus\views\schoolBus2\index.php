<style>
    .boxShadow{
        box-shadow: 0px 2px 6px 0px rgba(0,0,0,0.08);
    }
    [v-cloak] {
		display: none;
	}
    .colorRed{
        color:#D9534F
    }
    .colorGreen{
        color:#5CB85C
    }
    .greenTag{
        padding: 0 6px;
        font-size: 12px;
        color: #5CB85C;
        background: #F2F9F2;
        border-radius: 2px;
        white-space: nowrap;
        display: inline-block;
        height: 20px;
        line-height: 20px;
    }
    .redTag{
        padding:4px 6px;
        font-size: 12px;
        color: #D9534F;
        background: #FCF1F1;
        border-radius: 2px;
        white-space: nowrap;
    }
    .yellowTag{
        padding:4px 6px;
        font-size: 12px;
        color: #F0AD4E;
        background: #FCF0DE;
        border-radius: 2px;
        white-space: nowrap;
    }
    .defaultTag{
        padding:4px 6px;
        font-size: 12px;
        color: #333333;
        background: #E5E6EB;
        border-radius: 2px;
        white-space: nowrap; 
    }
    .labelSite{
        display:inline-block;
        background: #F2F3F5;
        border-radius: 2px;
        width:32px;
        height:32px;
        text-align: center;
        line-height: 32px
    }
    .img24{
        width: 20px;
        height:20px
    }
    .img16{
        width:16px;
        height:16px
    }
    .colorBlue{
        color: #4D88D2;
    }
    .lineHeight1{
        line-height:1
    }
    .amap-info-content{
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.08);
    }
    .mapWindowTitle{
        font-weight: 500;
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        margin-bottom:8px
    }
    .widthWindow{
        display: inline-block;
        width:90px
    }
    .windowInfo{
        white-space: nowrap;
    }
    .setPosition{
        position: absolute;
        top: 24px;
        width: 86%;
        left: 24px;
    }
    .el-select-dropdown__item{
        height: auto;
    }
    /* .el-select .el-range-editor.is-active, .el-range-editor.is-active:hover, .el-select .el-input.is-focus .el-input__inner{
        border:none
    } */
    .parking{
        padding:16px;
        background: #F7F7F8;
        border-radius: 4px;
        margin-top:16px
    }
    .lineBus{
       padding:16px 24px;
        background: #FFFFFF;
        border-radius: 4px;
        border: 1px solid #E5E6EB;
        margin-top:16px
    }
    .online{
        background: #F2F9F2;
        border-radius: 2px;
        color: #5CB85C;
        padding:2px 4px
    }
    .lineContainer {
      display: flex;
      flex-wrap: wrap; 
      gap: 16px; 
      overflow: hidden;
    }
    .lineSiteData {
        flex: 0 0 calc(12.5% - 14px); 
        box-sizing: border-box;
        padding:8px;
        border: 1px solid #ccc;
        background: #FAFAFA;
        border-radius: 4px;
        border: 1px solid #E5E6EB;
        position: relative;
        color:#666
    }
    .lineSiteData:hover, .currentLineSiteData{
        cursor: pointer;
        background: #F0F5FB;
        border-radius: 4px;
        border: 1px solid #4D88D2;
        color:#4D88D2 !important;
    }
    .lineSiteData:hover .siteTitle ,.currentLineSiteData .siteTitle{
        color:#4D88D2 !important;
    }
    .siteTitle{
        font-size:12px;
        color:#333;
        font-weight:500;
        margin-bottom:4px
    }
    .borderTop{
        width: 18px;
        position: absolute;
        height: 1px;
        border-top: 1px solid #D9D9D9;
        z-index: 0;
        top: 50%;
        left: -18px;
    }
    .font11{
        font-size:11px
    }
    .fontWeight{
        font-weight:600
    }
    .text_overflow{
        overflow: hidden; /* 必须 */
        text-overflow: ellipsis; /* 当文本溢出时显示省略号 */
        white-space: nowrap; /* 强制文本在一行内显示 */
    }
    .widthFlex{
        width: 0;
    }
    .custom-content-marker {
        width: 167px;
        height: 28px;
        position: relative;
        text-align: center;
    }
    .custom-content-marker .textCenter {
        text-align: center;
        display: inline-block;
        background: #ffffff;
        box-shadow: 0px 4px 6px 0px rgba(0, 0, 0, 0.2);
        border-radius: 28px;
        padding: 2px;
    }
    .custom-content-marker .textCenter .img {
        width: 26px;
        height: 26px;
        background: #4396ff;
        border-radius: 50%;
        text-align: center;
        display: inline-block;           
    }
    .custom-content-marker .textCenter .img span {
        background-image: url(https://m2.files.ivykids.cn/cloud01-file-8025768FgU1Y5Yh8a_LL569RQ5U9KhQ3T-w.png);
        background-size: 16px 16px;
        background-repeat: no-repeat;
        width: 16px;
        height: 16px;
        margin-top: 5px;
        display: inline-block;
        }
    .custom-content-marker .textCenter .close-btn {
        font-size: 12px;
        color: #323233;
        margin: 0 8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: inline-block;
    }
    .custom-content-marker .textCenter .close-btn  span {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .custom-content-marker .textCenter::before {
        content: "";
        position: absolute;
        bottom: -8px;
        left: 50%;
        margin-left: -4px;
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        border-top: 8px solid #fff;
    }
    .custom-content-marker-add {
        width: 167px;
        height: 28px;
        text-align: center;
        position: relative;    
    }
    .custom-content-marker-add .textCenter {
      text-align: center;
      display: inline-block;
      background: #4396ff;
      box-shadow: 0px 4px 6px 0px rgba(0, 0, 0, 0.2);
      border-radius: 28px;
      padding: 2px;
    }
    .custom-content-marker-add .textCenter  .img {
        width: 26px;
        height: 26px;
        background: #fff;
        border-radius: 50%;
        text-align: center;
        display: inline-block;        
    }
    .custom-content-marker-add .textCenter  .img  span {
        background-image: url(https://m2.files.ivykids.cn/cloud01-file-8025768FphViejWH_Cewjc1FMWzvkKwniB1.png);
        background-size: 16px 16px;
        background-repeat: no-repeat;
        width: 16px;
        height: 16px;
        margin-top: 5px;
        display: inline-block;
    }
    .custom-content-marker-add .textCenter .close-btn {
        font-size: 12px;
        color: #fff;
        margin: 0 8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: inline-block;
    }
    .custom-content-marker-add .textCenter .close-btn  span {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .custom-content-marker-add .textCenter::before {
      content: "";
      position: absolute;
      bottom: -8px;
      left: 50%;
      margin-left: -4px;
      border-left: 8px solid transparent;
      border-right: 8px solid transparent;
      border-top: 8px solid #4396ff;
    }
    .siteDetail-popup {
        bottom: 90px;
        width: 80%;
        padding: 20px;
        box-shadow: 0px 4px 6px 0px rgba(0, 0, 0, 0.11);
        top: auto;
        transform: translate3d(-50%, 0, 0);
        position: absolute;
        z-index: 999;
        left: 50%;
        background: #fff;
        border-radius: 8px
    }
    .siteDetail-popup .siteDetail {
        position: relative;
    }
    .siteDetail-popup .siteDetail .close {
        font-size: 16px;
        color: #666;
        opacity: 1;
    }
    .siteDetail-popup .siteDetail .label {
        color: #fff;
        font-size: 12px;
        background: #4d88d2;
        border-radius: 2px;
        padding: 1px 4px;
    }
    .lineSite {
        display: flex;
        align-items: center;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
    .lineSite .lineImg {
        width: 12px;
        height: 12px;
    }
    .startImg {
        width: 25px;
        height: 34px;
    }
    .lineText {
        padding: 4px 8px;
        background: #4396ff;
        box-shadow: 0px 4px 6px 0px rgba(0, 0, 0, 0.2);
        border-radius: 4px;
        color: #fff;
        margin-left: 10px;
    }
    .lineText .title {
        font-size: 12px;
    }
    .lineText  .text {
        font-size: 11px;
        color: rgba(255, 255, 255, 0.9);
        margin-top: 2px;
    }
    .lineText::before {
        content: "";
        position: absolute;
        top: 50%;
        left: 15px;
        margin-top: -8px;
        border-right: 8px solid #4396ff;
        border-bottom: 8px solid transparent;
        border-top: 8px solid transparent;
    }
    .lineTextSelect {
        background: #5cb85c;
    }
    .lineTextSelect::before {
         border-right: 8px solid #5cb85c;
    }
    .start::before {
        left: 28px;
    }
    .viewLineOut{
        position: absolute;
        top: 24px;
        width:calc(100% - 48px) ;
        left: 24px;
        background: #fff;
        box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08);
        padding: 10px 16px;
        border-radius: 4px;
    }
    .viewLineOutTag{
        background: #E5E6EB;
        border-radius: 2px;
        font-size: 12px;
        color: #333333;
        padding:2px 6px
    }
    .timeWeight{
        background: #F0F5FB;
        border-radius: 4px;
    }
    .pointText{
        background: rgba(77,136,210,0.08);
        border-radius: 4px 4px 0px 0px;
        font-weight: 500;
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        padding:6px;
        text-align:center
    }
    .checkSite{
        padding:8px
    }
    .checkSite:hover{
        background: #F2F3F5;
        border-radius: 4px;
        cursor: pointer;
    }
    .borderLeftpos{
        border-left: 1px solid #EBEDF0;
        position: absolute;
        left: 50%;
        height: 100%;
        width: 1px;
        top: 0;
    }
    .scroll-box::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius   : 10px;
        background-color: skyblue;
        background-color: #D8D8D8;
        background-image: none
    }
    .scroll-box::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0);
        background   : #fff;
        border-radius: 10px;
    }
    .table thead{
        background: #FAFAFA;
        color:#333
    }
    .table > thead > tr > th{
        border-bottom: 1px solid #dddddd;
    }
    .table thead th,.table tbody td{
        vertical-align: middle !important;
        padding: 16px 8px !important
    }
    .isDataNot{
        position: absolute;
        top: 2px;
        left: 0;
    }
    .currentYear{
        padding: 4px 6px;
        font-size: 12px;
        color: #4D88D2;
        background: #F0F5FB;
        border-radius: 2px;
    }
    .upload-container {
        position: relative;
        display: inline-block;
    }
    #upload_file {
        position: absolute;
        left: 0;
        top: 0;
        opacity: 0;
        width: 100%;
        height: 100%;
        cursor: pointer;
    }
    .lnglat{
        width: 140px;
        text-align: center;
    }
    .lnglat .imgMove img{
        width:24px;
        height:36px;
    }
    .top-center {
        background-color: #343a40;
        color: white;
        padding: 6px 12px;
        border-radius: 5px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        font-size: 14px;
        margin-bottom:10px
    }

    .top-center::before {
        content: "";
        position: absolute;
        bottom: -8px;
        left: 50%;
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        border-top: 8px solid #343a40;
        transform: translateX(-50%);
    }
    .dropdown-menu{
        min-width: 100px;
    }
    #wechatQrcode{
        width: 120px;
        height: 120px;
        margin: 0 auto;
    }
    .displayflex{
        display: inline-flex
    }
    .childList{
        background: #FFFFFF;
        border-radius: 4px;
        border: 1px solid #D9D9D9;
        cursor: pointer;
    }
    .avatar40{
        width:40px;
        height:40px;
        border-radius:50%;
        object-fit: cover;
    }
    .avatar24{
        width:20px;
        height:20px;
        border-radius:50%;
        margin-right:-4px;
        object-fit: cover;
        float: left;
    }
    .discount{
        background:#F2F3F5;
        color: #666666;
        padding: 0 6px;
        font-size: 12px;
        border-radius: 2px;
        white-space: nowrap;
        display: inline-block;
        height: 20px;
        line-height: 20px;
    }
    .dot{
        width: 6px;
        height: 6px;
        background: #D9534F;
        border-radius:50%;
        display: inline-flex
    }
    .borderLine{
        border-top: 1px solid #E5E6EB;
    }
    .m16{
        margin:0 16px 16px
    }
    .pt20{
        padding-top:20px
    }
    .busNav > li > a{
        padding:6px 12px
    }
    .lineBill{
       padding:24px 24px 24px 0;
        background: #F7F7F8;
        border-radius: 8px;
        margin-bottom:24px;
    }
    .lineLeft{
        width: 4px;
        height: 16px;
        background: #4D88D2;
        display: inline-flex
    }
    .billSiteData {
        flex: 0 0 calc(16.666% - 14px); 
        box-sizing: border-box;
        padding:8px;
        border: 1px solid #ccc;
        background: #fff;
        border-radius: 4px;
        border: 1px solid #E5E6EB;
        position: relative;
        color:#666;
    }
    .currentBillSiteData{
        cursor: pointer;
        background: #F0F5FB;
        border: 1px solid #4D88D2;
    }
    .addChild{
        width: 20px;
        height: 20px;
        background: #F0F5FB;
        display: inline-block;
        text-align: center;
        line-height: 20px;
        border-radius: 50%;
        color: #4D88D2;
        font-size: 14px;
    }
    .moreChild{
        width: 21px;
        height: 21px;
        background: #E5E6EB;
        border: 1px solid #FFFFFF;
        display: inline-block;
        text-align: center;
        line-height:18px;
        border-radius: 50%;
        color: #666666;
        font-size: 14px;
        cursor: pointer;
    }
    .moreChild:hover{
        background: #4D88D2;
        color: #fff;
    }
    .up{
        background: #4D88D2;
        color: #fff;
    }
    .width100{
        width:80px;
    }
    .addChildBill{
        background: #FFFFFF;
        border-radius: 4px;
        border: 1px solid #E5E6EB;
        padding:6px 24px;
    }                                                       
    .invalidBill{
        padding:8px 16px;
        background: #F7F7F8;
        border-radius: 4px;
    }
    .highlight-row {
        background-color: #F0F5FB !important; /* 浅绿色背景 */
    }
    .zindex{
        z-index:1000 !important
    }
    .tableInvoice tr td,.tableInvoice tr th{
        padding:10px 16px !important;
        color:#333;
        vertical-align: middle !important;
    }
    .tableInvoice tr th{
        background: #FAFAFA;
    }
    .table > tbody + tbody{
        border-top: 1px solid #dddddd !important;
    }
</style>
<div class="container-fluid" id='box' v-cloak>
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('schoolBus', '校车管理'); ?></li>
    </ol>
    <div class='loadingData'  v-if='loadingData'>
        <div class='mb16'>
            <el-select v-model="startYear" size='small' @change='handleClick("tabyear")' placeholder="请选择">
                <el-option
                v-for="item in startYearList"
                :key="item.key"
                :label="item.value"
                :value="item.key">
                </el-option>
            </el-select>
            <button type="button" class="btn btn-default ml16" @click='copySite' v-if='(((activeName=="line" || activeName=="site")) && carSiteList.length==0 )|| (activeName=="billPay" && SchoolBusListData.route_data && SchoolBusListData.route_data.sites.length==0)'>导入数据</button>
            <!-- <button type="button" class="btn btn-danger ml16" @click='clearSiteAll("clear")' v-else >清空数据</button> -->
            <el-popover
                placement="bottom"
                width="200"
                trigger="click">
                <div class='text-center p12' v-show='showQrcode'>
                    <div class='color3 font14 fontBold'><?php echo Yii::t("schoolTeams", "SchoolBus Routes");?></div>
                    <div class='color3 font12 mb16 fontBold'>{{branchAbb}} {{showTitle(startYear)}}</div>
                    <div id='wechatQrcode' class='wechatQrcode' ></div>
                </div>
                <button type="button" class="btn btn-default ml16" slot="reference" @click="getQrcode()">
                    <span class="glyphicon glyphicon-qrcode" aria-hidden="true"></span> 家长端二维码
                </button>
            </el-popover>
            <button type="button" class="btn btn-link ml16" @click='newPage()'><?php echo Yii::t("busSite", "Route-site overall");?></button>
        </div>
        <div  v-loading="tabLoading">
            <el-tabs v-model="activeName" @tab-click="handleClick">
                <el-tab-pane label="站点管理" name="site"></el-tab-pane>
                <el-tab-pane label="线路规划" name="line"></el-tab-pane>
                <el-tab-pane label="校车名单" name="billPay"></el-tab-pane>
            </el-tabs>
            <div v-if='activeName=="site"'>
                <div class='mt10 flex align-items mb20'>
                    <div class="btn-group mr16">
                        <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            添加站点 <span class="caret"></span>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a href="javascript:;" @click='addSite()'>手动添加</a></li>
                            <li><a href="javascript:;" class="upload-container">
                            <div class="upload-button">批量添加</div>
                            <input type="file" ref="upload_file" accept=".xls,.xlsx" id="upload_file" :key="fileInputKey" @change="readExcel($event)" />
                            </a></li>
                        </ul>
                    </div>
                    <button type="button" class="btn btn-default mr16" @click='addSite("school")'>设置学校位置</button>
                    <button type="button" class="btn btn-default mr16" @click='exportSite("template")'>下载模版</button>
                    <button type="button" class="btn btn-default mr16" @click='exportSite()'>导出站点</button>
                    <div class='flex1 color6 font14'>共 {{carSiteList.length}} 个站点</div>
                    <el-input
                        style='width:200px'
                        size='small'
                        placeholder="搜索站点"
                        prefix-icon="el-icon-search"
                        clearable
                        v-model="searchTable">
                    </el-input>
                </div>
            </div>
            <div class='row mt10'  v-if='activeName=="line" || activeName=="site"'>
                <div class='col-md-4'>
                    <div id='mapExample' :style="activeName=='site'?'height:' + (height-355) + 'px':'height:' + (height-305) + 'px'" ></div>
                    <div v-if='activeName=="site" && drawPosition' class='setPosition'  >
                        <el-select v-model="addressMarker" class='boxShadow' collapse-tags style='width:100%' :automatic-dropdown="dropdownAuto" ref="mySelect" placeholder="请选择" @change="selectChange">
                            <el-option
                                v-for="(item,index) in completeList"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id">
                                <div style='line-height: 22px;padding:5px 0'>
                                    <div class='font14 colorBlue fontWeight'>{{ item.name }}</div>
                                    <div class='font12 color6 flex'><div class='flex1 widthFlex text_overflow'> {{ item.district }}{{ item.address }}</div></div>
                                </div>
                            </el-option>
                        </el-select>
                        <div class='mt12 flex'>
                            <div class='flex1'></div>
                            <button type="button" class="btn btn-danger boxShadow" @click='defaultPot()' ><?php echo Yii::t("global", "Cancel"); ?></button>
                            <button type="button" class="btn btn-primary  boxShadow ml16"  :disabled='btnDisanled'  @click='updateLocation()'><?php echo Yii::t("global", "OK"); ?></button>
                        </div>
                    </div>
                    <div v-if='activeName=="line" && viewLine'  class='viewLineOut'  >
                        <div class='flex align-items'>
                            <div class='flex1'><span class='font16 color3 fontWeight'>{{viewLineData.title}} </span> <span class='viewLineOutTag'>{{siteLineType=="1"?"<?php echo Yii::t("coll", "pick-up"); ?>":"<?php echo Yii::t("coll", "drop-off"); ?>"}}</span></div>
                            <div class='colorBlue font14 cur-p' @click='viewOut'><span class='el-icon-close'></span> </div>
                        </div>
                    </div>
                    <div v-if='siteDetail && activeName=="line"' class="siteDetail-popup">
                        <div class="siteDetail" v-if="point != ''">
                            <div class=" mb5 flex">
                                <div class='font16 color3 flex1 fontWeight'>{{ pointIndex.title }} <span class='viewLineOutTag'>{{pointIndex.code}}</span> </div>
                                <span class='el-icon-close close' @click="outLine('del')" ></span>
                            </div>
                            <div class="font12 color6 mb8">{{ pointIndex.address }}</div>
                            <div class="mb12" >
                                <div v-if='pointIndex.link_routes.length'>
                                    <span class='greenTag mr5 mb5' v-for='(list,index) in pointIndex.link_routes'>{{carRouterData[list]?carRouterData[list].title:''}}</span>
                                </div>
                                <div v-else><span class='redTag'>未关联线路</span> </div>
                            </div>
                            <!-- <div class="color6 font12 mb8"><span class='el-icon-location'></span><span class="ml5">停车位置</span></div> -->
                            <div class=" mb8">
                            <span class="label"><?php echo Yii::t("coll", "Pick-up point"); ?></span> <span class="font14 color3 fontWeight ml5">{{ pointIndex.pickup_point }}</span>
                            </div>
                            <div class="">
                            <span class="label"><?php echo Yii::t("coll", "Drop-off point"); ?></span> <span class="font14 color3 fontWeight ml5">{{ pointIndex.dropoff_point }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class='col-md-8' v-if='activeName=="site"'>
                    <div v-if='carSiteList.length'>
                        <el-table 
                        :data="filterTable"
                         :height="height-355"
                          style="width: 100%"
                        :header-cell-style="{background:'#fafafa',color:'#333'}"
                        >
                            <el-table-column prop="title"  min-width='120'  label="站点名称" >
                                <template  slot-scope="scope">
                                    <span>{{scope.row.title}}</span>
                                    <img v-if='scope.row.type==2' style='width: 20px;height: 20px;vertical-align: sub;' src='https://m2.files.ivykids.cn/cloud01-file-8025768FhYoaJ7mTg8NfL4DPh_eJvGFL7HL.png' >
                                </template>
                            </el-table-column>
                            <el-table-column prop="name" min-width='250' label="停车经纬度" >
                                <template  slot-scope="scope" slot="header">
                                        <div>停车经纬度</div>
                                        <div class='color6 font12' style='font-weight:normal'>
                                            <span class='displayflex align-items'><img class='img16 mr4' src="https://m2.files.ivykids.cn/cloud01-file-8025768Fp6yE6GIkNVEzVZiR-zhgOR3MxBZ.png" alt="">上学&放学</span>
                                            <span class='ml16 displayflex align-items'><img class='img16 mr4' src="https://m2.files.ivykids.cn/cloud01-file-8025768FmlbkbZK2M4CrCHcrrs3ciUhzs5K.png" alt="">上学</span>
                                            <span  class='ml16 displayflex align-items'><img class='img16 mr4' src="https://m2.files.ivykids.cn/cloud01-file-8025768FlQuQBflHSaFQ2Ze53bt0Il4__Ke.png" alt="">放学</span>
                                        </div>
                                </template>
                                <template  slot-scope="scope">
                                    <div v-if='scope.row.same_point==0'>
                                        <div class='flex align-items'>
                                            <div class='labelSite'>
                                                <img class='img24' src="https://m2.files.ivykids.cn/cloud01-file-8025768FmlbkbZK2M4CrCHcrrs3ciUhzs5K.png" alt="">
                                            </div>
                                            <div class='flex1 ml8'>
                                                <div class='font12 color3 lineHeight1 cur-p' @click='showlocation("pickup",scope.row)'>{{scope.row.pickup_point}}</div>
                                                <div class='colorBlue font12 lineHeight1 pt4 cur-p' v-if='scope.row.pickup_longitude!="" && scope.row.pickup_latitude!=""' @click='showlocation("pickup",scope.row)'>{{scope.row.pickup_longitude}} {{scope.row.pickup_latitude}}</div>
                                                <div v-else class='colorRed font12 lineHeight1 mt4'>无经纬度</div>
                                            </div>
                                            <div class='colorBlue font14 cur-p mr24' @click='setAddress("pickup_point",scope.$index)'>
                                                <span class='el-icon-location-information mr4'></span>画点位
                                            </div>
                                        </div>
                                        <div class='flex align-items mt10'>
                                            <div class='labelSite'>
                                                <img class='img24' src="https://m2.files.ivykids.cn/cloud01-file-8025768FlQuQBflHSaFQ2Ze53bt0Il4__Ke.png" alt="">
                                            </div>
                                            <div class='flex1 ml8'>
                                                <div class='font12 color3 lineHeight1 cur-p' @click='showlocation("dropoff",scope.row)'>{{scope.row.dropoff_point}}</div>
                                                <div class='colorBlue font12 lineHeight1 pt4 cur-p'  v-if='scope.row.dropoff_longitude!="" && scope.row.dropoff_latitude!=""' @click='showlocation("dropoff",scope.row)'>{{scope.row.dropoff_longitude}} {{scope.row.dropoff_latitude}}</div>
                                                <div v-else class='colorRed font12 lineHeight1 mt4'>无经纬度</div>
                                                
                                            </div>
                                            <div class='colorBlue font14 cur-p mr24' @click='setAddress("dropoff_point",scope.$index)'>
                                                <span class='el-icon-location-information mr4'></span>画点位
                                            </div>
                                        </div>
                                    </div>
                                    <div v-if='scope.row.same_point==1'>
                                    <div class='flex align-items'>
                                            <div class='labelSite'>
                                                <img class='img24' src="https://m2.files.ivykids.cn/cloud01-file-8025768Fp6yE6GIkNVEzVZiR-zhgOR3MxBZ.png" alt="">
                                            </div>
                                            <div class='flex1 ml8'>
                                                <div class='font12 color3 lineHeight1 cur-p' @click='showlocation("pickup",scope.row)'>{{scope.row.pickup_point}}</div>
                                                <div class='colorBlue font12 lineHeight1 pt4 cur-p' v-if='scope.row.pickup_longitude!="" && scope.row.pickup_latitude!=""' @click='showlocation("pickup",scope.row)'>{{scope.row.pickup_longitude}} {{scope.row.pickup_latitude}}</div>
                                                <div v-else class='colorRed font12 lineHeight1 mt4'>无经纬度</div>
                                            </div>
                                            <div class='colorBlue font14 cur-p mr24' @click='setAddress("pickup_point",scope.$index)'>
                                                <span class='el-icon-location-information mr4'></span>画点位
                                            </div>
                                        </div>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column prop="code" min-width='100' label="站点编号">
                            </el-table-column>
                            <el-table-column 
                                prop="id" 
                                label="关联线路"  
                                :filters="tableFilter"
                                :filter-method="filterTag"
                                filter-placement="bottom-end"
                                min-width='115'
                            >
                                <template  slot-scope="scope">
                                    <div v-if='scope.row.link_routes.length'>
                                        <span v-if='scope.row.link_routes.length>4 && !scope.row.showAll'>
                                            <span class='greenTag mr5 mb5' v-for='(list,index) in scope.row.link_routes.slice(0,4)'>{{carRouterData[list]?carRouterData[list].title:''}}</span>
                                            <span class='el-icon-arrow-down cur-p colorBlue ' @click='scope.row.showAll=true'></span>
                                        </span>
                                        <span v-else>
                                            <span class='greenTag mr5 mb5' v-for='(list,index) in scope.row.link_routes'>{{carRouterData[list]?carRouterData[list].title:''}}</span><span v-if='scope.row.link_routes.length>4 && scope.row.showAll' class='el-icon-arrow-up cur-p colorBlue ' @click='scope.row.showAll=false'></span>
                                        </span>
                                    </div>
                                    <div v-else><span class='redTag'>未关联线路</span> </div>
                                </template>
                            </el-table-column>
                            <el-table-column  label="操作" width='110'>
                                <template  slot-scope="scope">
                                    <span class="cur-p colorBlue mr16" @click='addSite(scope.row)'><?php echo Yii::t('schoolTeams','Edit');?></span>
                                    <span class="cur-p colorBlue" @click='delSite(scope.row.id)'><?php echo Yii::t("newDS", "Delete");?></span>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                    <div v-else>
                    <el-empty description="暂无数据"></el-empty>
                    </div>
                </div>
                <div class='col-md-8' v-if='activeName=="line"'>
                    <div class='flex align-items '>
                        <div class='flex1'>
                            <button type="button" class="btn btn-primary" @click='editLine()'><span class='el-icon-circle-plus-outline'></span> 新建线路</button>
                        </div>
                        <div class="btn-group btn-group-sm" role="group" aria-label="...">
                            <button type="button" class="btn" :class='siteLineType=="1"?"btn-primary":"btn-default"' @click='tabLineType("1")'><?php echo Yii::t("coll", "pick-up"); ?></button>
                            <button type="button" class="btn" :class='siteLineType=="2"?"btn-primary":"btn-default"' @click='tabLineType("2")'><?php echo Yii::t("coll", "drop-off"); ?></button>
                        </div>
                    </div>
                    <div class='lineList mt8 overflow-y scroll-box' v-if='lineList.routes && lineList.routes.length' :style="'max-height:'+(height-345)+'px;overflow-x: hidden;'">
                        <div class='lineBus' v-for='(list,index) in lineList.routes'>
                            <div class='flex align-items'>
                                <span class='font16 color3 fontWeight mr16'>{{list.title}}</span>
                                <div  class='flex1'>
                                    <div class="dropdown">
                                        <span class="el-dropdown-link online cur-p" v-if='list.status==10' data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                                        <span class='el-icon-success'></span> 已上线<i class="el-icon-arrow-down el-icon--right"></i>
                                        </span>
                                        <span class="el-dropdown-link redTag cur-p" v-if='list.status==20' data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                                        <span class='el-icon-warning'></span> 未上线<i class="el-icon-arrow-down el-icon--right"></i>
                                        </span>
                                        <ul class="dropdown-menu" aria-labelledby="dropdownMenu1">
                                            <li v-if='list.status==10'><a href="javascript:;" @click='offline(list,"20")'>下线</a></li>
                                            <li v-if='list.status==20'><a href="javascript:;" @click='offline(list,"10")'>上线</a></li>
                                        </ul>
                                    </div>
                                </div>
                                <div class='colorBlue font14 mr24 cur-p' v-if='lineList.route2Site[list.id]' >
                                    <img src="https://m2.files.ivykids.cn/cloud01-file-8025768FmJ2lwQQ0jJkPqR3Z0-Gvo2r8nPg.png" alt="" style='width:16px;height:16px'>
                                    <span  @click='viewLineRoute(list)'><?php echo Yii::t('coll','View routes');?></span>
                                </div>
                                <div class="dropdown">
                                    <button class="btn btn-default dropdown-toggle" style='padding: 1px 4px 6px;line-height: 1;' type="button" id="dropdownMenu1" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                                        ...
                                    </button>
                                    <ul class="dropdown-menu" aria-labelledby="dropdownMenu1" style='left:auto;right:0'>
                                        <li><a href="javascript:;" @click='setSiteLine(list)'>设置途经站点</a></li>
                                        <li><a href="javascript:;" @click='editLine(list)'><?php echo Yii::t('schoolTeams','Edit');?></a></li>
                                        <li><a href="javascript:;" @click='delLine(list.id)'><?php echo Yii::t("newDS", "Delete");?></a></li>
                                    </ul>
                                </div>
                            </div>
                            <div class='mt16 lineContainer' v-if='lineList.route2Site[list.id]'>
                                <div class='lineSiteData' v-for='(item,i) in siteLineType=="1"?lineList.route2Site[list.id]:lineList.route2Site_drop[list.id]'  @click='markerClick(item.sid)' :class='currentRouteSite==item.sid?"currentLineSiteData":""'>
                                    <div class='borderTop'></div>
                                    <div class='siteTitle flex'><span class='flex1 widthFlex font11 text_overflow'>{{lineList.sitesObj[item.sid].title}}</span></div>
                                    <div class='flex align-items'><span class='el-icon-location-outline mr4'></span><span class='flex1 widthFlex font11 text_overflow'>{{siteLineType=="1"?lineList.sitesObj[item.sid].pickup_point:lineList.sitesObj[item.sid].dropoff_point}}</span></div>
                                    <div class=''  v-if='(siteLineType=="1" && item.pickup_time!="" && item.pickup_time!=null) || (siteLineType=="2" && item.dropoff_time!="" && item.dropoff_time!=null)'><span class='el-icon-time mr4'></span><span class='font11'>{{siteLineType=="1"?item.pickup_time:item.dropoff_time}}</span></div>
                                </div>
                            </div>
                            <div v-else class='font12 color9 text-center mt24 mb24'>
                                    未设置途径站点
                            </div>
                        </div>
                    </div>
                    <div v-else-if='lineList.routes'>
                        <el-empty description="暂无数据"></el-empty>
                    </div>
                </div>
            </div>
            <div class='row' v-if='activeName=="billPay"'>
                <div class='col-lg-3 col-sm-12'>
                    <div class='mb20'>
                        <el-input
                            size="small"
                            placeholder="搜索学生"
                            clearable
                            prefix-icon="el-icon-search"
                            v-model="searchChild">
                        </el-input>
                    </div>    
                    <div  class=' overflow-y scroll-box' :style="'height:'+(height-345)+'px;overflow-x: hidden;margin-right:-15px;padding-right:10px'">   
                        <div v-if='familyConfig.config'>
                        <div class='childList mb16' :class='childSiteIndex==index?"currentBillSiteData":""' v-for='(list,index) in searchData' @click='filterchildSite(familyConfig.list_by_family[list],index)'>
                            <div class='redTag mb4' v-if='familyConfig.discount_error.indexOf(list)!=-1'><span class='el-icon-warning font14 mr8'></span>异常折扣：多子女家庭仅一名学生坐校车</div>
                            <div class='m16 pt20' v-for='(item,i) in familyConfig.list_by_family[list]' :class='i!=0?"borderLine":""'>
                                <div class="flex align-items">
                                    <img :src="item.avatar" alt="" class="avatar40"> 
                                    <div class="ml10 flex1">
                                        <!-- {{list}} -->
                                        <div class="bluebg">{{item.child_name}}</div> 
                                        <div class="font12 color6" style="line-height: 1;">{{item.class_name}}</div>
                                    </div>
                                    <span class="cur-p " :class='item.discount==null?"discount":"greenTag"'>
                                        {{discountTitle(item.discount)}}<span v-if='item.discount==99'>优惠{{item.off_ratio}}%</span>
                                    </span>
                                    <!-- <el-dropdown trigger="click">
                                        <span class="cur-p " :class='item.discount==""?"discount":"greenTag"'>
                                            {{discountTitle(item.discount)}}<i class="el-icon-arrow-down el-icon--right"></i>
                                        </span>
                                        <el-dropdown-menu slot="dropdown">
                                        <el-dropdown-item v-for='(_item,idx) in familyConfig.config.discount' :key='idx' @click.native='setDiscountTitle(item,_item)'>{{_item.title}}</el-dropdown-item>
                                        </el-dropdown-menu>
                                    </el-dropdown> -->
                                </div>
                                <div class='mt16 flex align-items'>
                                    <span class='el-icon-location-outline font14 color9'></span>
                                    <div v-if='item.site_list.length==0' class='color9 ml10'>无站点</div>
                                    <div v-else>
                                        <div  class='flex align-items' v-for='(site,p) in item.site_list'>
                                            <span class='font12 color6 ml10'>{{site.line_name}} {{site.site_name}}</span>
                                            <span class='discount ml8'>{{site.journey==1?"上学":site.journey==2?"放学":"双程"}}</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class='mt12 flex' v-if='item.invoice_list.length==0'>
                                    <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/bill.png' ?>" alt="" style='width:14px;height:14px'>
                                    <div class='flex1 ml10'>
                                        <div class='color9'>无账单</div>
                                    </div>
                                </div>  
                                <div class='mt12 flex' v-else>
                                    <!-- {{item.invoice_list}} -->
                                    <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/bill.png' ?>" alt="" style='width:14px;height:14px'>
                                    <div class='flex1 ml10'>
                                        <div class='flex align-items color6' v-for='(_item,idx) in item.invoice_list'>
                                            <span class='flex1 widthFlex text_overflow'>{{_item.amount}}｜{{familyConfig.config.fee_level[_item.fee_level].title}} {{discountTitle(_item.discount)}}<span v-if='_item.discount==99'>优惠{{_item.off_ratio}}%</span></span>
                                            <span class='colorRed ml10' v-if='!_item.paid'><span class='dot'></span> 未付款</span> 
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        </div>
                    </div>                
                </div>
                <div class='col-lg-9 col-sm-12'>
                    <?php if(Yii::app()->user->checkAccess('ivystaff_it')){?>
                    <div class='mb20 row'>
                        <button type="button" class="btn btn-default pull-right mr15" @click='handleSelectedImg' id='filesPhoto' :disabled='importBtn'>
                            <span class="glyphicon glyphicon-log-in" aria-hidden="true"></span> 
                            <span v-if='importBtn==false'><?php echo Yii::t('site','导入数据');?></span>
                            <span v-else><?php echo Yii::t('site','正在导入中');?></span> 
                        </button>
                    </div>
                    <?php }?>
                    <div class=' overflow-y scroll-box' :style="'max-height:'+(height-345)+'px;overflow-x: hidden;margin-right:-15px;padding-right:10px'">
                        <div v-if='SchoolBusListData.route_data'>
                            <div v-if='SchoolBusListData.route_data.routes.length'>
                                <div class='lineBill' v-for='(item,index) in SchoolBusListData.route_data.routes'>
                                    <div class='flex align-items'>
                                        <span class='lineLeft'></span>
                                        <div class='flex1 font16 color3 ml20'>{{item.title}}</div>
                                        <span class="colorBlue font14 cur-p" @click='printData(item)'>打印名单</span>
                                    </div>
                                    <div class='ml24'>
                                        <div class='mt16 lineContainer' >
                                            <div class='billSiteData' :class='childSiteId.indexOf(_item.sid)!=-1?"currentBillSiteData":""' v-for='(_item,idx) in SchoolBusListData.route_data.route2Site[item.id]' >
                                                <div style='padding:4px 8px'>
                                                    <div class='borderTop'></div>
                                                    <div class='font14 color3 fontBold flex'><div class='flex1 widthFlex text_overflow'>{{SchoolBusListData.sitesObj[_item.sid].title}}</div> </div>
                                                    <div  class='mt4' style='height:17px'>
                                                        <span :class='SchoolBusListData.sitesObj[_item.sid].fee_level==1?"defaultTag":SchoolBusListData.sitesObj[_item.sid].fee_level==2?"greenTag":SchoolBusListData.sitesObj[_item.sid].fee_level==3?"yellowTag":SchoolBusListData.sitesObj[_item.sid].fee_level==4?"redTag":""' v-if='SchoolBusListData.sitesObj[_item.sid].fee_level'>{{familyConfig.config.fee_level[SchoolBusListData.sitesObj[_item.sid].fee_level].title}}</span> 
                                                    </div>
                                                    <div class='flex align-items mt12'>
                                                        <div class='flex1 flex align-items'>
                                                            <!-- <span class='addChild cur-p' @click='addChildBill(_item)'><span class='el-icon-plus'></span></span> -->
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/userSite.png' ?>" alt="" style='width:16px;height:16px'>
                                                            <span class='font14 color3 ml4' v-if='SchoolBusListData.list[item.id] && SchoolBusListData.list[item.id][_item.sid]'>{{SchoolBusListData.list[item.id][_item.sid].length}}</span>
                                                            <span v-else class='font14 color3 ml4'>0</span>
                                                        </div>
                                                        <div v-if='SchoolBusListData.list[item.id] && SchoolBusListData.list[item.id][_item.sid]'>
                                                            <img v-for='(img,i) in SchoolBusListData.list[item.id][_item.sid].slice(0, 3)' :src="img.avatar" alt="" class="avatar24">
                                                            <el-popover
                                                                placement="bottom"
                                                                width="280"
                                                                popper-class='zindex'
                                                                :ref="`popover-${_item.link_id}`"
                                                                v-model="tipVisibles[_item.link_id]"
                                                                trigger="click">
                                                                <div class=''>
                                                                    <!-- <div class='flex'>
                                                                        <div class='flex1 color3 font14 fontBold'>{{SchoolBusListData.sitesObj[_item.sid].title}}</div>
                                                                        <div><span class='el-icon-close font16 color6'></span></div>
                                                                    </div> -->
                                                                    <div class=' overflow-y scroll-box' style='max-height: 200px;overflow-y: auto; margin-right: -16px; padding-right: 16px;'>
                                                                        <div class='mt4 mb4' style='border-radius: 4px;padding:4px'  v-for='(child,ind) in SchoolBusListData.list[item.id][_item.sid]'>
                                                                            <div class='flex align-items'>
                                                                                <img :src="child.avatar" alt="" class="avatar40">
                                                                                <div class='flex1 ml8'>
                                                                                    <div class='font14 color3'>{{child.child_name}}</div>
                                                                                    <div class='font12 color6'>{{child.class_name}}</div>
                                                                                </div>
                                                                                <span class='discount ml8'>{{child.journey==1?"上学":child.journey==2?"放学":"双程"}}</span>
                                                                                <!-- <span class='el-icon-delete font14 cur-p' @click='deleteBusStudent(child)'></span> -->
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <span class='moreChild' :class='tipVisibles[_item.link_id]?"up":""'  @click='popoverHide(index)' slot="reference" >
                                                                    <span class='el-icon-arrow-up' v-if='tipVisibles[_item.link_id]'></span>
                                                                    <span class='el-icon-arrow-down' v-else></span>
                                                                </span>
                                                            </el-popover>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else>
                                <el-empty description="暂无数据"></el-empty>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 编辑站点 -->
    <div class="modal fade" id="addSiteModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static" >
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel">{{ addSiteData.url=='addSite'?'新建站点':'编辑站点'}}</h4>
                </div>
                <div class="modal-body p24 overflow-y scroll-box" :style="'max-height:'+(height-180)+'px;overflow-x: hidden;'">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <span class="col-sm-2 font14 color6">站点编号</span>
                            <div class="col-sm-10">
                                <el-input v-model='addSiteData.code' size='small'  placeholder="<?php echo Yii::t("leave", "Input"); ?>"></el-input>
                            </div>
                        </div>
                        <div class="form-group">
                            <span class="col-sm-2 font14 color6">小区名称 (中文)</span>
                            <div class="col-sm-10">
                                <el-input v-model='addSiteData.title_cn' size='small'  placeholder="<?php echo Yii::t("leave", "Input"); ?>"></el-input>
                            </div>
                        </div>
                        <div class="form-group">
                            <span class="col-sm-2 font14 color6">小区名称 (英文)</span>
                            <div class="col-sm-10">
                                <el-input v-model='addSiteData.title_en' size='small'  placeholder="<?php echo Yii::t("leave", "Input"); ?>"></el-input>
                            </div>
                        </div>
                        <div class="form-group">
                            <span class="col-sm-2 font14 color6">上学和放学停车位置</span>
                            <div class="col-sm-10">
                                <div>
                                    <label class="radio-inline mr12">
                                        <input type="radio" v-model='addSiteData.same_point' value="0">停车位置不同
                                    </label>
                                    <label class="radio-inline mr12">
                                        <input type="radio" v-model='addSiteData.same_point' value="1">停车位置相同
                                    </label>
                                </div>
                                <div class='parking' v-if='addSiteData.same_point===0 || addSiteData.same_point==="0"'>
                                    <div class='font14 color3 fontWeight'>上学停车位置：</div>
                                    <div class='row mt12'>
                                        <div class='col-md-6'>
                                            <div class='flex align-items'>
                                                <span class='color6 font14 mr16'>中文</span>
                                                <el-input class='flex1' v-model='addSiteData.pickup_point_cn' size='small'  placeholder="<?php echo Yii::t("leave", "Input"); ?>"></el-input>
                                            </div>
                                        </div>
                                        <div class='col-md-6'>
                                            <div class='flex align-items'>
                                                <span class='color6 font14 mr16'>英文</span>
                                                <el-input class='flex1' v-model='addSiteData.pickup_point_en' size='small'  placeholder="<?php echo Yii::t("leave", "Input"); ?>"></el-input>
                                            </div>
                                        </div>
                                    </div>
                                    <div class='font14 color3 fontWeight mt16'>放学停车位置：</div>
                                    <div class='row mt12'>
                                        <div class='col-md-6'>
                                            <div class='flex align-items'>
                                                <span class='color6 font14 mr16'>中文</span>
                                                <el-input class='flex1' v-model='addSiteData.dropoff_point_cn' size='small'  placeholder="<?php echo Yii::t("leave", "Input"); ?>"></el-input>
                                            </div>
                                        </div>
                                        <div class='col-md-6'>
                                            <div class='flex align-items'>
                                                <span class='color6 font14 mr16'>英文</span>
                                                <el-input class='flex1' v-model='addSiteData.dropoff_point_en' size='small'  placeholder="<?php echo Yii::t("leave", "Input"); ?>"></el-input>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class='parking' v-if='addSiteData.same_point==1'>
                                    <div class='font14 color3 fontWeight'>上学和放学停车位置：</div>
                                    <div class='row mt12'>
                                        <div class='col-md-6'>
                                            <div class='flex align-items'>
                                                <span class='color6 font14 mr16'>中文</span>
                                                <el-input class='flex1' v-model='addSiteData.pickup_point_cn' size='small'  placeholder="<?php echo Yii::t("leave", "Input"); ?>"></el-input>
                                            </div>
                                        </div>
                                        <div class='col-md-6'>
                                            <div class='flex align-items'>
                                                <span class='color6 font14 mr16'>英文</span>
                                                <el-input class='flex1' v-model='addSiteData.pickup_point_en' size='small'  placeholder="<?php echo Yii::t("leave", "Input"); ?>"></el-input>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" :disabled='btnDisanled' @click='saveAddSite()'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 批量添加 -->
    <div class="modal fade" id="batchAddSiteModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel"  data-backdrop="static" data-keyboard="false"  >
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel">批量添加</h4>
                </div>
                <div class="modal-body p24 overflow-y scroll-box" :style="'max-height:'+(height-180)+'px;overflow-x: hidden;'">
                    <table  class="table">
                        <thead>
                            <tr>
                                <th  width='40'>序号</th>
                                <th>站点名称</th>
                                <th>上学停车位置</th>
                                <th>经纬度</th>
                                <th>放学停车位置</th>
                                <th>经纬度</th>
                                <th  width='70'>上学放学<br>停车位置</th>
                                <th>站点编号</th>
                                <th width='40'>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <template v-for='(list,index) in batchList'>
                            <tr :style='list.data_repeat || list.code_repeat?"background:#F7F7F8;position: relative":""'>
                                <td>
                                    <div class='color6 font12'>{{index+1}}</div>
                                </td>
                                <td>
                                    <div v-if='list.data_repeat' class='colorRed isDataNot'><span class='el-icon-warning'></span> 数据不完整无法添加</div>
                                    <div v-else-if='list.code_repeat' class='colorRed isDataNot'><span class='el-icon-warning'></span> 站点编号相同无法添加</div>
                                    <div class='color6 font12'>{{list.title_cn}}</div>
                                    <div class='color6 font12'>{{list.title_en}}</div>
                                </td>
                                <td>
                                    <div class='color6 font12'>{{list.pickup_point_cn}}</div>
                                    <div class='color6 font12'>{{list.pickup_point_en}}</div>
                                </td>
                                <td>
                                    <div class='color6 font12'>{{list.pickup_longitude}}</div>
                                    <div class='color6 font12'>{{list.pickup_latitude}}</div>
                                </td>
                                <td>
                                    <div class='color6 font12'>{{list.dropoff_point_cn}}</div>
                                    <div class='color6 font12'>{{list.dropoff_point_en}}</div>
                                </td>
                                <td>
                                    <div class='color6 font12'>{{list.dropoff_longitude}}</div>
                                    <div class='color6 font12'>{{list.dropoff_latitude}}</div>
                                </td>
                                <td>
                                    <div class='color6 font12'>{{list.same_point=='1'?"相同":list.same_point=='0'?"不同":""}}</div>
                                </td>
                                <td>
                                    <div class='color6 font12'>{{list.code}}</div>
                                </td>
                                <td>
                                    <span class='cur-p colorBlue' @click='delBatchSite(index,batchList)'>删除</span>
                                </td>
                            </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" :disabled='btnDisanled' @click='addSiteBatch()'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 删除 -->
    <div class="modal fade" id="delModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t('newDS', 'Warning') ?></h4>
                </div>
                <div class="modal-body p24" >
                    <div v-if='delType=="clearSite"'>
                        确认清空数据吗？
                    </div>
                    <div v-else-if='delType=="cancel" || delType=="cancelAll"'>
                        确认作废账单吗？
                    </div>
                    <div v-else>
                        <?php echo Yii::t('directMessage', 'Proceed to remove?') ?>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" v-if='delType=="site"' :disabled='btnDisanled' @click='delSite()'><?php echo Yii::t("global", "OK"); ?></button>
                    <button type="button" class="btn btn-primary" v-if='delType=="line"' :disabled='btnDisanled' @click='delLine()'><?php echo Yii::t("global", "OK"); ?></button>
                    <button type="button" class="btn btn-primary" v-if='delType=="bill"' :disabled='btnDisanled' @click='deleteBusStudent()'><?php echo Yii::t("global", "OK"); ?></button>
                    <button type="button" class="btn btn-primary" v-if='delType=="cancel"' :disabled='btnDisanled' @click='cancelBill()'><?php echo Yii::t("global", "OK"); ?></button>
                    <button type="button" class="btn btn-primary" v-if='delType=="cancelAll"' :disabled='btnDisanled' @click='cancelBillAll()'><?php echo Yii::t("global", "OK"); ?></button>
                    <button type="button" class="btn btn-primary" v-if='delType=="clearSite"' :disabled='btnDisanled' @click='clearSiteAll()'><?php echo Yii::t("global", "OK"); ?></button>
                    
                </div>
            </div>
        </div>
    </div>
    <!-- 编辑线路 -->
    <div class="modal fade" id="editLineModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static" >
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel">{{ addLineData.url=='addRoute'?'新建线路':'编辑线路'}}</h4>
                </div>
                <div class="modal-body p24 overflow-y scroll-box" :style="'max-height:'+(height-180)+'px;overflow-x: hidden;'">
                    <div class='font14 color6 mb12'>线路名称</div>
                    <div class='relative'>
                        <el-input v-model='addLineData.title' size='small'  placeholder="<?php echo Yii::t("leave", "Input"); ?>"></el-input>
                    </div>
                    <div class='font14 color6 mb12 mt24'>车牌号</div>
                    <div class='relative'>
                        <el-input v-model='addLineData.license_plate' size='small'  placeholder="<?php echo Yii::t("leave", "Input"); ?>"></el-input>
                    </div>
                    <div class='flex mt24'>
                        <div class='flex1 mr8'>
                            <div class='font14 color6 mb12'>司机姓名</div>
                            <div class='relative'>
                                <el-input v-model='addLineData.driver_name' size='small'  placeholder="<?php echo Yii::t("leave", "Input"); ?>"></el-input>
                            </div>
                        </div>
                        <div class='flex1 ml8'>
                            <div class='font14 color6 mb12'>司机手机号</div>
                            <div class='relative'>
                                <el-input v-model='addLineData.driver_mobile' size='small'  placeholder="<?php echo Yii::t("leave", "Input"); ?>"></el-input>
                            </div>
                        </div>
                    </div>
                    <div class='flex mt24'>
                        <div class='flex1 mr8'>
                            <div class='font14 color6 mb12'>阿姨姓名</div>
                            <div class='relative'>
                                <el-input v-model='addLineData.aunt_name' size='small'  placeholder="<?php echo Yii::t("leave", "Input"); ?>"></el-input>
                            </div>
                        </div>
                        <div class='flex1 ml8'>
                            <div class='font14 color6 mb12'>阿姨手机号</div>
                            <div class='relative'>
                                <el-input v-model='addLineData.aunt_mobile' size='small'  placeholder="<?php echo Yii::t("leave", "Input"); ?>"></el-input>
                            </div>
                        </div>
                    </div>
                    <div class='font14 color6 mb12 mt24'>状态</div>
                    <div class='relative'>
                        <el-switch
                            v-model="addLineData.status"
                            active-color="#4D88D2"
                            inactive-color="#CCCCCC"
                            active-value="10"
                            inactive-value="20">
                        </el-switch>
                        <span>上线</span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" :disabled='btnDisanled' @click='saveLineData()'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 途径站点 -->
    <div class="modal fade" id="LineSiteModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static" >
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel">设置途径站点</h4>
                </div>
                <div class="modal-body p24 overflow-y scroll-box" v-if='setSiteLineData.title' >
                    <div class='flex align-items'>
                        <div class='flex1 font14 color3 fontWeight'>{{setSiteLineData.title}}</div>
                        <button type="button" class="btn btn-primary mr16" @click='addSiteLine'>添加站点</button>
                    </div>
                    <div class='mt24'>
                    <el-table
                        :max-height="height-290"
                        :data="setSiteLineData.tableData"
                        :header-cell-style="{background:'#fafafa',color:'#333'}"
                        style="width: 100%">
                        <el-table-column
                            prop="id"
                            label="站点"
                            width="120">
                            <template  slot-scope="scope">
                                <div>{{lineList.sitesObj[scope.row.sid].title}}</div>
                                <div>{{lineList.sitesObj[scope.row.sid].code}}</div>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="name"
                            label="上学停车位置"
                            width="330">
                            <template  slot-scope="scope">
                                <div class='timeWeight'>
                                    <div class='pointText'>{{lineList.sitesObj[scope.row.sid].pickup_point}}</div>
                                    <div class='flex align-items p16'>
                                        <div class='flex1 flex align-items mr8'>
                                            <span>时间：</span>
                                            <el-time-picker
                                                v-model="scope.row.pickup_time"
                                                format='HH:mm'
                                                size="small"
                                                class='flex1'
                                                style='width:110px'
                                                value-format="HH:mm"
                                                placement="bottom-start"
                                                placeholder="请选择">
                                            </el-time-picker>
                                        </div>
                                        <div class='flex1 flex align-items ml8'>
                                            <span>序号：</span>
                                            <el-select v-model="scope.row.weight" clearable  @focus='pickupSort(scope.$index,"weight")' class='flex1' placeholder="请选择" size="small">
                                                <el-option
                                                v-for="(item,index) in setSiteLineData.sort"
                                                :key="index"
                                                :disabled="item.disabled"
                                                :label="item.key"
                                                :value="item.key">
                                                </el-option>
                                            </el-select>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="address"
                            width="330"
                            label="放学停车位置">
                            <template  slot-scope="scope">
                                <div class='timeWeight'>
                                    <div class='pointText'>{{lineList.sitesObj[scope.row.sid].dropoff_point}}</div>
                                    <div class='flex align-items p16'>
                                        <div class='flex1 flex align-items mr8'>
                                            <span>时间：</span>
                                            <el-time-picker
                                                v-model="scope.row.dropoff_time"
                                                format='HH:mm'
                                                size="small"
                                                class='flex1'
                                                style='width:110px'
                                                value-format="HH:mm"
                                                placement="bottom-start"
                                                placeholder="请选择">
                                            </el-time-picker>
                                        </div>
                                        <div class='flex1 flex align-items ml8'>
                                            <span>序号：</span>
                                            <el-select v-model="scope.row.drop_weight" clearable  @focus='pickupSort(scope.$index,"drop_weight")' class='flex1' placeholder="请选择" size="small">
                                                <el-option
                                                v-for="(item,index) in setSiteLineData.sort"
                                                :key="index"
                                                :disabled="item.disabled"
                                                :label="item.key"
                                                :value="item.key">
                                                </el-option>
                                            </el-select>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column
                            prop="address"
                            width="50"
                            label="操作">
                            <template  slot-scope="scope">
                               <span class='el-icon-delete cur-p' @click='delCheckSite(scope.$index,setSiteLineData.tableData)'></span>
                            </template>
                        </el-table-column>
                        </el-table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" :disabled='btnDisanled' @click='saveLineMiddleData()'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 添加站点 -->
    <div class="modal fade" id="addLineSiteModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static" >
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel">添加站点</h4>
                </div>
                <div class="modal-body p24 ">
                <span class='borderLeftpos'></span>
                   <div class='row'>
                        <div class='col-md-6'>
                            <div>
                                <el-input
                                    size='small'
                                    placeholder="请输入内容"
                                    clearable
                                    prefix-icon="el-icon-search"
                                    v-model="searchSite">
                                </el-input>
                            </div>
                            <div class='overflow-y scroll-box mt16'  :style="'max-height:'+(height-280)+'px;overflow-x: hidden;'">
                                <div class='flex align-items checkSite' v-for='(list,index) in filteredItems'>
                                    <div class="checkbox flex1" style='margin:0'>
                                        <label style='line-height:1.5'>
                                        <input type="checkbox" :value='list.id' v-model='checkSite' :disabled='list.dropoff_latitude=="" || list.pickup_latitude=="" ?true:false'> <span class='font14 color3 ml8'>{{list.title}}</span><span class='colorRed' v-if='list.dropoff_latitude=="" || list.pickup_latitude==""'>（无经纬度）</span>
                                        </label>
                                    </div>
                                    <div class='font12 color6'>{{list.code}}</div>
                                </div>
                            </div>
                        </div>
                        <div class='col-md-6'>
                            <div  class='font12 color6 mb16'>已选择 {{checkSite.length}} 个站点</div>
                            <div class='overflow-y scroll-box'  :style="'max-height:'+(height-280)+'px;overflow-x: hidden;'">
                                <div class='flex align-items checkSite' v-for='(list,index) in checkSite'>
                                    <div class='flex1'>
                                        <div class="font14 color3">{{lineList.sitesObj[list].title}}</div>
                                        <div class='font12 color6'>{{lineList.sitesObj[list].code}}</div>
                                    </div>
                                    <span class='el-icon-upload2 font16 color6 mr8' @click='topCheckSite(index)'></span>
                                    <span class='el-icon-close font16 color6' @click='delCheckSite(index,checkSite)'></span>
                                </div>
                            </div>
                        </div>
                   </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" :disabled='btnDisanled' @click='saveAdd()'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 复制站点 -->
    <div class="modal fade" id="copyModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel">导入数据</h4>
                </div>
                <div class="modal-body p24">
                    <div class='font14 color3'>导入数据到当前学年：</div>
                    <div>
                        <div  v-for='(list,index) in startYearList'>
                            <div class="radio" v-if='list.key==startYear'>
                                <label>
                                <input type="radio" :value='list.key' v-model='target_year'><span class='color3 font14'> {{list.value}}</span> <span class='currentYear ml5' v-if='list.key==startYear'>当前学年</span>
                                </label>
                            </div>
                            <div class="radio" v-else>
                                <label>
                                <input type="radio" :value='list.key' v-model='target_year'><span class='color3 font14'> {{list.value}}</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" :disabled='btnDisanled' @click='saveCopySite()'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="importModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" @click='unUploadFiles()'><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "提示");?></h4>
            </div>
            <div class="modal-body">
                <!-- <p class='color3 font14'><label>将清空“假期、事件、周末调休”所有数据</label> </p> -->
                <input ref="img-upload-input" class="img-upload-input hide" type="file" accept=".xlsx" @change="submitUpload">
                <button type="button" class="btn btn-default mr10 " @click='handleSelectedImg' id='filesPhoto' :disabled='importBtn'>
                    <span class="glyphicon glyphicon-log-in" aria-hidden="true"></span> 
                    <span v-if='importBtn==false'><?php echo Yii::t('site','导入');?></span> 
                    <span v-else><?php echo Yii::t('site','正在导入中');?></span> 
                </button>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                <!-- <button type="button" class="btn btn-primary" @click='clearEvent()'><?php echo Yii::t("global", "OK");?></button> -->
            </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
  window._AMapSecurityConfig = {
    serviceHost: "https://apps.ivyonline.cn/_AMapService",
  };
</script>
<script type="text/javascript" src="https://webapi.amap.com/maps?v=2.0&key=166c9d81f042ef0ae55c7a2c85e19a7e&plugin=AMap.Driving"></script>
<script>
    var height=document.documentElement.clientHeight;
    $(document).ready(function () {
        // 通过该方法来为每次弹出的模态框设置最新的zIndex值，从而使最新的modal显示在最前面
        $(document).on('show.bs.modal', '.modal', function (event) {
            var zIndex = 1040 + (10 * $('.modal:visible').length);
            $(this).css('z-index', zIndex);
            setTimeout(function() { 
                $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');
            }, 0);
        });
    });
    
    var branchAbb = '<?php echo $this->branchObj->abb;?>';
    var container=new Vue({
        el: "#box",
        data: {
            branchAbb:branchAbb,
            height:height,
            startYearList: [],
            startYear:'',
            value: '',
            activeName: 'site',
            map:null,
            drawPosition:false,
            carSiteList:[],
            carRouterData:{},
            tableFilter:[],
            defaultMarker:[],
            addressMarker:'',
            infoWindow:{},
            searchMarker:{},
            completeList:[],
            addSiteData:{},
            btnDisanled:false,
            siteDetail:false,
            point:'',
            delId:'',
            delType:'',
            lineList:{},
            siteLineType:'1',
            addLineData:{},
            updateLocationType:'',
            updateLocationData:{},
            currentRouteSite:'',
            lat: "", //上学放学字段
            lon: "", //上学放学字段
            pointLine: "",
            routeLine: {}, //当前线路
            lineMarker: [], //当前线路点
            viewLine:false,
            viewLineData:{},
            loadingData:false,
            setSiteLineData:{},
            checkSite:[],
            searchSite:'',
            pointIndex:{},
            moveLng:'',
            moveLat:'',
            batchList:[],
            fileInputKey:0,
            dropdownAuto:false,
            searchTable:'',
            target_year:'',
            tabLoading:false,
            showQrcode:false,
            searchChild:'',
            familyConfig:{},
            leftSort_family:[],
            SchoolBusListData:{},
            addChildSiteData:{},
            loading:false,
            semesterAdd:'',
            semesterBill:'',
            tableInvoice:{},         
            childSiteId:[],
            childSiteIndex:null,
            tipVisibles:{},
            importBtn:false
        },
        created: function() {
			let that=this
            that.loadingData=false
            this.activeName='site'
            $.ajax({
                url: '<?php echo $this->createUrl("api") ?>',
                type: "post",
                dataType: 'json',
                data:{
                    url:'indexData',
                },
                success: function(data) {
                    if (data.state == 'success') {
                       that.startYearList=data.data.startYearList
                       let year=data.data.startYearList.filter((i) => i.current ==1)
                       that.startYear=year[0].key
                      that.handleClick()
                    }else{
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                },
                error: function(data) {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                },
            })
        },
        computed: {
            filteredItems() {
                if (!this.searchSite) {
                    return this.carSiteList; // 如果搜索框为空，则返回所有项
                }
                return this.carSiteList.filter(item => 
                    item.title.toLowerCase().includes(this.searchSite.toLowerCase())
                );
            },
            filterTable() {
                if (!this.searchTable) {
                    return this.carSiteList; // 如果搜索框为空，则返回所有项
                }
                return this.carSiteList.filter(item => 
                    (item.title+item.code).toLowerCase().includes(this.searchTable.toLowerCase())
                );
            },
            searchData: function() {
                var search = this.searchChild.trim();
                var family=[]
                let that=this
                if(search) {
                    for(let key in that.familyConfig.list_by_family){
                        let group=that.familyConfig.list_by_family[key]
                            group.forEach(item => {
                                if (item.child_name.toUpperCase().includes(search.toUpperCase())  ) {
                                    family.push(item.family_id);
                                }
                            });
                    }
                    family = [...new Set(family)];
                    return family
                }
                return that.familyConfig.sort_family;
            },
        },
        methods: {
            popoverHide(index){
               Vue.set(this.tipVisibles, index, false);
            },
            showTitle(id){
                let year = this.startYearList.find(i => i.key ==id);
                return year.value
            },
            getQrcode(){
                let that=this
                $('.wechatQrcode').html('')
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "get",
                    dataType: 'json',
                    data:{
                        url:'qrcode',
                        start_year:this.startYear
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.showQrcode=true
                            console.log('家长端校车地址：'+data.data)
                            that.$nextTick(() => {
                                $('.wechatQrcode').qrcode({
                                    width:120,
                                    height:120,
                                    text:data.data,
                                });
                            })
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            getSiteList(type){
                let that=this
                if(type){
                    this.tabLoading=true
                }
                this.showQrcode=false
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        url:'siteList',
                        start_year:this.startYear
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            if(type){
                                data.data.siteData.sort((a, b) => {
                                    if (a.type === 2 && b.type !== 2) return -1;
                                    if (b.type === 2 && a.type !== 2) return 1;
                                    const aHasEmpty = !a.pickup_latitude || !a.pickup_longitude || !a.dropoff_latitude || !a.dropoff_longitude;
                                    const bHasEmpty = !b.pickup_latitude || !b.pickup_longitude || !b.dropoff_latitude || !b.dropoff_longitude;
                                    if (aHasEmpty && !bHasEmpty) return -1;
                                    if (bHasEmpty && !aHasEmpty) return 1;
                                    
                                    if (a.link_routes.length ==0 && b.link_routes.length !=0) return -1;
                                });
                            }
                            data.data.siteData.forEach((item)=>{
                                item.showAll=false
                            })
                            that.carSiteList=data.data.siteData
                            that.carRouterData=data.data.routerData
                            for(let key in data.data.routerData){
                                that.tableFilter.push({ text: data.data.routerData[key].title, value:key })
                            }
                            that.tabLoading=false
                            if(type=='init'){
                                that.$nextTick(()=>{
                                    that.init()
                                })
                            }else if(type=='line'){
                                that.getLineList()
                                that.$nextTick(()=>{
                                    that.linePot()
                                })
                            }else{
                                that.defaultPot()
                            }
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            init() {
                this.map = new AMap.Map("mapExample", {
                    zoom: 12,
                    center: [116.39, 39.92],
                    resizeEnable: true
                });
                this.defaultPot()
			},
            removeMarker(){
                this.map.remove(this.defaultMarker);
                this.map.remove(this.lineMarker);
                this.map.remove(this.routeLine);
                this.map.remove(this.searchMarker);
            },
			defaultPot(){
                this.removeMarker();
                this.drawPosition=false
                this.addressMarker=''
                this.defaultMarker=[]
                var Icon = new AMap.Icon({
                    image: "https://m2.files.ivykids.cn/cloud01-file-8025768Fj-fp-tPj9YC4PxNZnlri8vNvSbY.png",
                    size: new AMap.Size(24, 32),
                    imageSize: new AMap.Size(24, 32),
                });
				for(var i = 0; i < this.carSiteList.length; i++) {
                    // 放学点
                    if(this.carSiteList[i].dropoff_longitude != '' && this.carSiteList[i].dropoff_latitude != ''){
                        let marker = new AMap.Marker({
                            position: new AMap.LngLat(this.carSiteList[i].dropoff_longitude, this.carSiteList[i].dropoff_latitude), // 注意这里经度在前，纬度在后
                            icon: Icon,
                            offset: new AMap.Pixel(-13, -30),
                            extData: { index: i },
                        });
                        marker.id = 'dropoff_'+this.carSiteList[i].id
                        marker.content = "<div class='windowInfo'><div class='mapWindowTitle'>"+ this.carSiteList[i].title + "</div>" +
                                        "<p class='color6'><span class='widthWindow'>放学停车位置：</span><span>" + this.carSiteList[i].dropoff_point + "</span></p>" +
                                        "<p class='color6'><span class='widthWindow'>经度：</span><span>" + this.carSiteList[i].dropoff_longitude + "°</span></p>" +
                                        "<p class='color6'><span class='widthWindow'>纬度：</span><span>" + this.carSiteList[i].dropoff_latitude + "°</span></p>" +
                                        "</div>";
                        marker.on('click', this.markerClickInfo);
                        this.defaultMarker.push(marker);
                    }
                    // 上学点
                    if(this.carSiteList[i].pickup_longitude != '' && this.carSiteList[i].pickup_latitude != ''){
                        let marker = new AMap.Marker({
                            position: new AMap.LngLat(this.carSiteList[i].pickup_longitude, this.carSiteList[i].pickup_latitude), // 注意这里经度在前，纬度在后
                            icon: Icon,
                            offset: new AMap.Pixel(-13, -30),
                            extData: { index: i },
                        });
                        marker.id = 'pickup_'+this.carSiteList[i].id
                        marker.content = "<div class='windowInfo'><div class='mapWindowTitle'>"+ this.carSiteList[i].title + "</div>" +
                                        "<p class='color6'><span class='widthWindow'>上学停车位置：</span><span>" + this.carSiteList[i].pickup_point + "</span></p>" +
                                        "<p class='color6'><span class='widthWindow'>经度：</span><span>" + this.carSiteList[i].pickup_longitude + "°</span></p>" +
                                        "<p class='color6'><span class='widthWindow'>纬度：</span><span>" + this.carSiteList[i].pickup_latitude + "°</span></p>" +
                                        "</div>";
                        marker.on('click', this.markerClickInfo);
                        this.defaultMarker.push(marker);
                    }
                }
                this.map.add(this.defaultMarker);
                this.infoWindow = new AMap.InfoWindow({offset: new AMap.Pixel(0, -34)}); // 信息窗口
                this.map.setFitView(); 
			},
            filterTag(value, row) {
                return row.link_routes.indexOf(parseInt(value)) !=-1;
            },
            showlocation(type,data){
                this.drawPosition=false
                this.map.remove(this.searchMarker);
                this.addressMarker=''
                let id=type+'_'+data.id
                let marker = this.defaultMarker.find(marker => marker.id === id);
                if (marker) {
                    this.infoWindow = new AMap.InfoWindow({offset: new AMap.Pixel(0, -34)}); // 信息窗口
                    marker.emit('click', { target: marker });
                    this.map.setFitView([marker], false, [60, 60, 60, 60], 13);
                }
            },
            infoWindowColse(){
                if( Object.keys(this.infoWindow).length != 0){
                    this.infoWindow.close()
                }
            },
            markerClickInfo(e) {
                this.infoWindow.setContent(e.target.content);
                this.infoWindow.open(this.map, e.target.getPosition());
            },
            setAddress(type,index){
                this.updateLocationType=type
                this.updateLocationData=this.carSiteList[index]
                this.drawPosition=true
                let that=this
                AMap.plugin("AMap.AutoComplete", function () {
                    var autoOptions = {
                        city: "010",
                    };
                    var autoComplete = new AMap.AutoComplete(autoOptions);
                    autoComplete.search(that.carSiteList[index].title, function (status, result) {
                        if (status === "complete" && result.info === "OK") {
                            that.completeList = result.tips.filter(tip => tip.adcode.startsWith('11')); 
                            that.dropdownAuto=true
                            that.$nextTick(() => { // 使用$nextTick确保DOM更新完成
                                that.$refs.mySelect.focus(); // 然后重新获得焦点以显示下拉框
                            });
                        }
                    });
                });
            },
            selectChange() {
                let that=this
                this.infoWindowColse()
                this.dropdownAuto=false
                let list = this.completeList.find(item => item.id === this.addressMarker);
                this.map.remove(this.searchMarker);
                this.moveLng= list.location.lng
                this.moveLat= list.location.lat
                this.searchMarker = new AMap.Marker({
                    position:new AMap.LngLat(list.location.lng, list.location.lat),
                    draggable: true,
                    cursor: 'move',
                    offset: new AMap.Pixel(-70, -98),
                    content: '<div class="lnglat">'+
                    '<div class="relative"><div class="top-center">'+
                        '<div >经度：<span class="moveLat">'+that.moveLat+'</span></div>'+
                        '<div>纬度：<span class="moveLng">'+that.moveLng+'</span></div>'+
                    '</div></div>'+
                    '<div class="imgMove"><img src="https://m2.files.ivykids.cn/cloud01-file-8025768Fo0A-RXgCOqjWO8t1g6-htinKkd5.png"></div>'+
                    '</div>'
                });
                this.map.add(this.searchMarker);
                this.map.setFitView([this.searchMarker], true, [60, 60, 60, 60], 16);
                this.searchMarker.setTitle('拖动点定位');
                this.searchMarker.on('dragging',function(e) {
                    var lnglat = e.lnglat; 
                    that.moveLng= lnglat.getLng()
                    that.moveLat= lnglat.getLat()
                    const moveLng = document.getElementsByClassName("moveLng");
                    const moveLat = document.getElementsByClassName("moveLat");
                    moveLat[0].innerText=that.moveLat
                    moveLng[0].innerText=that.moveLng
                });                            
            },
            updateLocation(){
                let that=this
                let list = this.completeList.find(item => item.id === this.addressMarker);
                this.btnDisanled=true
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        url:'updateLocation/'+this.updateLocationData.id,
                        start_year:this.startYear,
                        type:this.updateLocationType=='dropoff_point'?'dropoff':'pickup',
                        lng:that.moveLng,
                        lat:that.moveLat,
                        address:list.district+list.name,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.getSiteList()
                            resultTip({
                                msg: data.message
                            });
                            that.infoWindowColse()
                            that.btnDisanled=false
                            that.addressMarker=''
                            that.drawPosition=false
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.btnDisanled=false
                        }
                    },
                    error: function(data) {
                        that.btnDisanled=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            addSite(editData){
                this.drawPosition=false
                this.map.remove(this.searchMarker);
                this.addressMarker=''
                this.addSiteData = {
                    url: editData && editData!='school' ? 'updateSite/' + editData.id : 'addSite',
                    start_year: this.startYear,
                    code: editData && editData!='school' ? editData.code : '',
                    title_cn: editData && editData!='school' ? editData.title_cn : '',
                    title_en: editData && editData!='school' ? editData.title_en : '',
                    same_point: editData && editData!='school' ? editData.same_point : '',
                    pickup_point_cn: editData && editData!='school' ? editData.pickup_point_cn : '',
                    pickup_point_en: editData && editData!='school' ? editData.pickup_point_en : '',
                    dropoff_point_cn: editData && editData!='school' ? editData.dropoff_point_cn : '',
                    dropoff_point_en: editData && editData!='school' ? editData.dropoff_point_en : ''
                };
                if (editData === 'school') {
                    const schoolData = this.carSiteList.find(item => item.type === 2);
                    if(schoolData){
                        this.addSiteData=schoolData
                        this.addSiteData.url='updateSite/' + schoolData.id
                    }else{
                        this.addSiteData.type = 2;
                    }
                }
                $('#addSiteModal').modal('show');
            },
            saveAddSite(){
                if(this.addSiteData.code.trim()==''){
                    resultTip({
                        error: 'warning',
                        msg: '请输入站点编号'
                    });
                    return
                }
                if(this.addSiteData.title_cn.trim()==''){
                    resultTip({
                        error: 'warning',
                        msg: '请输入小区名称（中文）'
                    });
                    return
                }
                if(this.addSiteData.title_en.trim()==''){
                    resultTip({
                        error: 'warning',
                        msg: '请输入小区名称（英文）'
                    });
                    return
                }
                if(this.addSiteData.same_point==''){
                    resultTip({
                        error: 'warning',
                        msg: '请选择停车位置是否相同'
                    });
                    return
                }
                if(this.addSiteData.same_point==1){
                    this.addSiteData.dropoff_point_cn=this.addSiteData.pickup_point_cn
                    this.addSiteData.dropoff_point_en=this.addSiteData.pickup_point_en
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data:this.addSiteData,
                    success: function(data) {
                        if (data.state == 'success') {
                            that.getSiteList()
                            $('#addSiteModal').modal('hide')
                            resultTip({
                                    msg: data.message
                                });
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            delSite(id){
                if(id){
                    this.delId=id
                    this.delType='site'
                    $("#delModal").modal('show')
                    return
                }
                let that=this
                that.btnDisanled=true
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        url:'deleteSite/'+this.delId,
                        start_year:this.startYear,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.getSiteList()
                            resultTip({
                                msg: data.message
                            });
                            $("#delModal").modal('hide')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.btnDisanled=false
                    },
                    error: function(data) {
                        that.btnDisanled=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            handleClick(type){
                if(!this.map){
                    if(this.activeName=="site" || this.activeName=="line" ){
                        this.$nextTick(()=>{
                            this.map = new AMap.Map("mapExample", {
                                zoom: 12,
                                center: [116.39, 39.92],
                                resizeEnable: true
                            });
                        })
                    }
                }
                this.loadingData=true
                if(this.activeName=="site"){
                    this.getSiteList('init')
                    if(this.map){
                    this.defaultPot()
                    }
                }else if(this.activeName=="line"){
                    if(type || this.carSiteList.length==0){
                        this.getSiteList("line") 
                    }else{
                        this.getLineList()
                    }
                    this.currentRouteSite=''
                    this.siteDetail=false
                    this.viewLine=false
                    
                    setTimeout(() => {
                        if(this.map && this.carSiteList.length){
                            this.infoWindowColse()
                            this.linePot()
                        }
                    }, 500);
                }else{
                    if(this.map){
                        this.map.destroy();
                    }
                    this.map=null
                    this.addChildSiteData={}
                    this.getBillConfig('init')
                }
            },
            getLineList(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        url:'routeList',
                        start_year:this.startYear,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.lineList=data.data
                            var sitesObj={}
                            let carSiteList=JSON.parse(JSON.stringify(that.carSiteList));
                            carSiteList.forEach(item => {
                                sitesObj[item.id]=item
                            });
                            that.lineList.sitesObj=sitesObj
                            let route2Site_drop={}
                            let route2Site=JSON.parse(JSON.stringify(data.data.route2Site));
                            for(let key in route2Site){
                                route2Site_drop[key]=route2Site[key].sort(function(a, b) {
                                    return a.drop_weight - b.drop_weight;
                                })
                            }
                            that.lineList.route2Site_drop=route2Site_drop  
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.btnDisanled=false
                    },
                    error: function(data) {
                        that.btnDisanled=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            editLine(data){
                if(data){
                    this.addLineData={
                        url:'updateRoute/'+data.id,
                        start_year:this.startYear,
                        title:data.title,
                        license_plate:data.license_plate,
                        driver_name:data.driver_name,
                        driver_mobile:data.driver_mobile,
                        aunt_name:data.aunt_name,
                        aunt_mobile:data.aunt_mobile,
                        status:data.status+''
                    }
                }else{
                    this.addLineData={
                        url:'addRoute',
                        start_year:this.startYear,
                        title:'',
                        license_plate:'',
                        driver_name:'',
                        driver_mobile:'',
                        aunt_name:'',
                        aunt_mobile:'',
                        status:'20'
                    }
                }
                $('#editLineModal').modal('show')
            },
            offline(data,type){
                this.addLineData={
                    url:'updateRoute/'+data.id,
                    start_year:this.startYear,
                    title:data.title,
                    license_plate:data.license_plate,
                    driver_name:data.driver_name,
                    driver_mobile:data.driver_mobile,
                    aunt_name:data.aunt_name,
                    aunt_mobile:data.aunt_mobile,
                    status:type
                }
                this.saveLineData()
            },
            saveLineData(){
                let that=this
                if(this.addLineData.title==''){
                    resultTip({
                        error: 'warning',
                        msg: '请输入线路名称'
                    });
                    return
                }
                if(this.addLineData.license_plate==''){
                    resultTip({
                        error: 'warning',
                        msg: '请输入车牌号'
                    });
                    return
                }
                if(this.addLineData.driver_name==''){
                    resultTip({
                        error: 'warning',
                        msg: '请输入司机姓名'
                    });
                    return
                }
                if(this.addLineData.driver_mobile==''){
                    resultTip({
                            error: 'warning',
                            msg: '请输入司机电话'
                        });
                    return
                }
                if(this.addLineData.aunt_name==''){
                    resultTip({
                            error: 'warning',
                            msg: '请输入阿姨姓名'
                        });
                    return
                }
                if(this.addLineData.aunt_mobile==''){
                    resultTip({
                            error: 'warning',
                            msg: '请输入阿姨电话'
                        });
                    return
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data:this.addLineData,
                    success: function(data) {
                        if (data.state == 'success') {
                            that.getLineList()
                            $('#editLineModal').modal('hide')
                            resultTip({
                                msg: data.message
                            });
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            delLine(id){
                if(id){
                    this.delId=id
                    this.delType='line'
                    $("#delModal").modal('show')
                    return
                }
                let that=this
                that.btnDisanled=true
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        url:'deleteRoute/'+this.delId,
                        start_year:this.startYear,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.getLineList()
                            resultTip({
                                msg: data.message
                            });
                            $("#delModal").modal('hide')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.btnDisanled=false
                    },
                    error: function(data) {
                        that.btnDisanled=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            tabLineType(type){
                this.siteLineType=type
                if(this.viewLine){
                    this.viewLineRoute(this.viewLineData)
                }else{
                    this.siteDetail = false;
                    this.currentRouteSite = '';
                    this.linePot()
                }
            },
            linePot(){
                let that=this
                if(this.siteLineType=='1'){
                    type='pickup'
                }else{
                    type='dropoff'
                }
                this.map.remove(this.defaultMarker);
                this.defaultMarker=[]
                this.removeMarker();
                for(var i = 0; i < this.carSiteList.length; i++) {
					// 创建点覆盖物
                    if(this.carSiteList[i][type+'_longitude'] != '' && this.carSiteList[i][type+'_latitude'] != ''){
                        let Icon = new AMap.Icon({
                            image: "https://m2.files.ivykids.cn/cloud01-file-8025768FimXp_5hCVp8GYtq4Ogx-0i5phaa.png",
                            size: new AMap.Size(40, 40),
                            imageSize: new AMap.Size(40, 40),
                        });
                        var marker = new AMap.Marker({
                            position: new AMap.LngLat(this.carSiteList[i].pickup_longitude, this.carSiteList[i].pickup_latitude),
                            icon: Icon,
                            offset: new AMap.Pixel(-13, -30),
                            extData: { id:this.carSiteList[i].id },
                        });
                        let markerContent = "" + '<div class="comment comment' + this.carSiteList[i].id + ' custom-content-marker" style="width: 167px;"><div class="textCenter">' + '<span class="img"><span></span></span>' + '<span class="close-btn" style="max-width: 114px;"><span>' + this.carSiteList[i].title + "</span></span>" + "</div></div>";
                        marker.setContent(markerContent);
                        marker.setOffset(new AMap.Pixel(-86, -41));
                        marker.on("click", (e) => {
                            let item = e.target.getExtData();
                            that.currentRouteSite=item.id
                            that.markerClick(item.id,'map');
                        });
                        marker.id=this.carSiteList[i].id
                        this.defaultMarker.push(marker)
                    }
				}
                this.map.add(this.defaultMarker);
				this.map.setFitView()
            },
            outLine(type) {
                this.siteDetail = false;
                if(type){
                    this.currentRouteSite = '';
                }
                var comments = document.querySelectorAll(".comment");
                if (comments.length) {
                    comments.forEach(function (comment) {
                    comment.classList.replace("custom-content-marker-add", "custom-content-marker");
                    });
                }
            },
            markerClick(id,type){
                if(!type){
                    if(this.currentRouteSite==id){
                        this.currentRouteSite = '';
                    }else{
                        this.currentRouteSite = id;
                    }
                    if(this.viewLine){
                        this.viewLine=false
                        this.map.remove(this.lineMarker);
                        this.map.remove(this.routeLine);
                        this.linePot()
                    }
                }
                let point = id;
                this.pointIndex = this.carSiteList.find(marker => marker.id === id);
                let marker = this.defaultMarker.find(marker => marker.id === id);
                this.map.setFitView([marker], true, [60, 260, 60, 60], 14);
                this.outLine();
                const textCenter = document.getElementsByClassName("comment" + point);
                if (!textCenter) return;
                if (this.point == point) {
                    this.point = "";
                    this.currentRouteSite = '';
                    textCenter[0].classList.replace("custom-content-marker-add", "custom-content-marker");
                    this.siteDetail = false;
                } else {
                    this.point = point;
                    textCenter[0].classList.replace("custom-content-marker", "custom-content-marker-add");
                    this.siteDetail = true;
                }
            },
            viewOut(){
                this.viewLine=false
                this.linePot()
            },
            viewLineRoute(list){
                this.viewLine=true
                this.siteDetail = false;
                this.currentRouteSite = '';
                this.point=''
                this.viewLineData=list
                const isPickup = this.siteLineType === '1';
                let lineListSource = isPickup ? this.lineList.route2Site[list.id] : this.lineList.route2Site_drop[list.id];
                this.lat = isPickup ? "pickup_latitude" : "dropoff_latitude";
                this.lon = isPickup ? "pickup_longitude" : "dropoff_longitude";
                this.pointLine = isPickup ? "pickup_point" : "dropoff_point";
                let lineTime = isPickup ? "pickup_time" : "dropoff_time";
                this.mapList = lineListSource.map(id => {
                    const matchedItem = this.carSiteList.find(item => {
                        if (item.type === 1) {
                            return id[lineTime] !== null && id[lineTime] !== '' && item.id === id.sid;
                        } else if (item.type === 2) {
                            return item.id === id.sid;
                        }
                    });
                    return matchedItem;
                }).filter(item => item); 
                this.removeMarker();
                this.drawPolyline();
                this.markPoints();
            },
            markPoints() {
                this.lineMarker = [];
                this.mapList.forEach((item, index) => {
                    const isFirst = index === 0;
                    const isLast = index + 1 === this.mapList.length;
                    const markerContent = this.createMarkerContent(item, isFirst, isLast);
                    const offset = isFirst || isLast ? new AMap.Pixel(-12, -36) : new AMap.Pixel(-7, -23);
                    const marker = new AMap.Marker({
                    content: markerContent,
                    map: this.map,
                    offset: offset,
                    });
                    this.lineMarker.push(marker);
                    marker.setPosition(new AMap.LngLat(item[this.lon], item[this.lat]));
                    this.map.add(marker);
                });
                this.map.setFitView(this.lineMarker);
            },
            createMarkerContent(item, isFirst, isLast) {
                const imgSrc =
                    isFirst || isLast
                    ? isFirst
                        ? "https://m2.files.ivykids.cn/cloud01-file-8025768Fo2S6IiXsrTrlVvmjXGXBxekmIpo.png" // Start/End Selected
                        : "https://m2.files.ivykids.cn/cloud01-file-8025768FkLP4hoEGFMUuKCbOqDJ59vtJBGO.png" // Start/End Unselected
                    : "https://m2.files.ivykids.cn/cloud01-file-8025768FiGGoRw98TLfQINTAs0nwBr45dAK.png"; // Middle Unselected
                const style = isFirst || isLast ? "width:25px;height:34px" : "width:12px;height:12px";
                return `<div class="lineSite">
                    <img class="${isFirst || isLast ? "startImg" : "lineImg"}" style="${style}" src="${imgSrc}" />
                    <div  class="${isFirst || isLast ? "lineText start" : "lineText"}">
                    <div class="title">${item.title}</div>
                    <div class="text">${item[this.pointLine]}</div>
                    </div>
                </div>`;
            },
            drawPolyline() {
                let that = this;
                var driving = new AMap.Driving({
                    policy: 2,
                });
                var marker = [];
                this.mapList.forEach((item, index) => {
                    if (index != 0 && index < this.mapList.length){ marker.push(new AMap.LngLat(item[this.lon], item[this.lat]))};
                });
                driving.search(
                    new AMap.LngLat(this.mapList[0][this.lon], this.mapList[0][this.lat]),
                    new AMap.LngLat(this.mapList[this.mapList.length - 1][this.lon], this.mapList[this.mapList.length - 1][this.lat]),
                    {
                    waypoints: marker,
                    },
                    function (status, result) {
                    if (status === "complete") {
                        if (result.routes && result.routes.length) {
                        that.drawRoute(result.routes[0]);
                        }
                    } else {
                        // log.error('获取驾车数据失败：' + result)
                    }
                    }
                );
            },
            drawRoute(route) {
                var path = this.parseRouteToPath(route);
                var canvasDir = document.createElement("canvas");
                var width = 24;
                canvasDir.width = width;
                canvasDir.height = width;
                var context = canvasDir.getContext("2d");
                context.strokeStyle = "#fff";
                context.lineJoin = "round";
                context.lineWidth = 10;
                context.moveTo(-4, width - 4);
                context.lineTo(width / 2, 6);
                context.lineTo(width + 4, width - 4);
                context.stroke();
                this.routeLine = new AMap.Polyline({
                    path: path,
                    isOutline: false,
                    showDir: true,
                    dirImg: canvasDir,
                    strokeWeight: 7,
                    strokeOpacity: 1,
                    strokeColor: "#4396FF",
                    lineJoin: "bevel",
                });
                this.map.add(this.routeLine);
                this.map.setFitView(this.routeLine, true);
            },
            parseRouteToPath(route) {
                var path = [];
                for (var i = 0, l = route.steps.length; i < l; i++) {
                    var step = route.steps[i];
                    for (var j = 0, n = step.path.length; j < n; j++) {
                    path.push(step.path[j]);
                    }
                }
                return path;
            },
            setSiteLine(list){
                let route2Site=JSON.parse(JSON.stringify(this.lineList.route2Site));
                let sort=[]
                if(route2Site[list.id]){
                    route2Site[list.id].forEach((item,index) => {
                        sort.push({key:index+1,disabled: false})
                    });
                }
                
                this.setSiteLineData={
                    title:list.title,
                    id:list.id,
                    tableData:route2Site[list.id]?route2Site[list.id]:[],
                    sort:sort
                }
                $("#LineSiteModal").modal('show')
            },
            addSiteLine(){
                this.checkSite=[]
                this.setSiteLineData.tableData.forEach(item => {
                    this.checkSite.push(item.sid)
                });
                $("#addLineSiteModal").modal('show')
            },
            delCheckSite(index,data){
                data.splice(index,1)
            },
            topCheckSite(index){
                const temp = this.checkSite[index - 1];
                Vue.set(this.checkSite, index - 1, this.checkSite[index]);
                Vue.set(this.checkSite, index, temp);
            },
            saveAdd(){
                const checkLength = this.checkSite.length;
                let newArray = [];
                this.checkSite.forEach((id,index) => {
                    let foundObject = this.setSiteLineData.tableData.find(obj => obj.sid === id);
                    if (foundObject) {
                        foundObject.weight=index+1
                        foundObject.drop_weight=checkLength - index
                        newArray.push(foundObject);
                    } else {
                        newArray.push({
                            "sid": id,
                            "pickup_time": "",
                            "dropoff_time": "",
                            "weight":index+1,
                            "drop_weight": checkLength - index
                        });
                    }
                });
                this.setSiteLineData.sort=[]
                newArray.forEach((item,index) => {
                    this.setSiteLineData.sort.push({key:index+1,disabled: false})
                });
                this.setSiteLineData.tableData=newArray
                $("#addLineSiteModal").modal('hide')
            },
            pickupSort(index,type){
                // let weight=[]
                // this.setSiteLineData.tableData.forEach((item,i) => {
                //     if(i!=index){
                //         weight.push(item[type])
                //     }
                // });
                // this.setSiteLineData.sort.forEach(item => {
                //     if(weight.indexOf(item.key)!=-1){
                //         item.disabled=true
                //     }else{
                //         item.disabled=false
                //     }
                // });
            },
            saveLineMiddleData(){
                let that=this
                for(let i=0;i<this.setSiteLineData.tableData.length;i++){
                   let name=this.setSiteLineData.tableData[i]
                    // if(name.pickup_time==''){
                    //     resultTip({
                    //         error: 'warning',
                    //         msg: '请选择上学停车时间'
                    //     });
                    //     return
                    // }
                    // if(name.dropoff_time==''){
                    //     resultTip({
                    //         error: 'warning',
                    //         msg: '请选择放学停车时间'
                    //     });
                    //     return
                    // }
                    if(name.weight==''){
                        resultTip({
                            error: 'warning',
                            msg: '请选择上学停车序号'
                        });
                        return
                    }
                    if(name.drop_weight==''){
                        resultTip({
                            error: 'warning',
                            msg: '请选择放学停车序号'
                        });
                        return
                    }
                }
                that.btnDisanled=true
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        url:'updateRouteSite/'+this.setSiteLineData.id,
                        start_year:this.startYear,
                        sites:this.setSiteLineData.tableData.length?this.setSiteLineData.tableData:0
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.getLineList()
                            resultTip({
                                msg: data.message
                            });
                            $("#LineSiteModal").modal('hide')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.btnDisanled=false
                    },
                    error: function(data) {
                        that.btnDisanled=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            readExcel(e){
                let that = this;
                const files = e.target.files;
                if (files.length <= 0) {
                    return false;
                } else if (!/\.(xls|xlsx)$/.test(files[0].name.toLowerCase())) {
                    this.$message({
                    message: "上传格式不正确，请上传xls或者xlsx格式",
                    type: "warning",
                    });
                    return false;
                }
                
                const fileReader = new FileReader();
                fileReader.onload = (ev) => {
                    try {
                    const data = ev.target.result;
                    const workbook = XLSX.read(data, {
                        type: "binary",
                    });
                    const wsname = workbook.SheetNames[0]; //取第一张表
                    const ws = XLSX.utils.sheet_to_json(workbook.Sheets[wsname]); //生成json表格内容
                    that.batchList = [];
                    ws.forEach((item,index) => {
                        that.batchList.push({
                            code: item["站点编号"],
                            title_cn: item["站点名称中文"],
                            title_en: item["站点名称英文"],
                            pickup_point_cn: item["上学停车位置中文"],
                            pickup_point_en: item["上学停车位置英文"],
                            pickup_longitude: item["上学停车经度"],
                            pickup_latitude: item["上学停车纬度"],
                            dropoff_point_cn: item["放学停车位置中文"],
                            dropoff_point_en: item["放学停车位置英文"],
                            dropoff_longitude: item["放学停车经度"],
                            dropoff_latitude: item["放学停车纬度"],
                            same_point: item["上学放学停车位置是否相同"],
                            fee_level: item["收费档"],
                        });
                    });
                    this.repeatData()
                    $("#batchAddSiteModal").modal('show')  
                    } catch (e) {
                    return false;
                    }
                };
                fileReader.readAsBinaryString(files[0]);
                that.fileInputKey += 1;
                that.$refs.upload_file.value = '';
            },
            exportSite(type){
                let year = this.startYearList.find(i => i.key ==this.startYear);
				let branchId = '<?php echo $_GET['branchId'] ?>';
                if(!type){
                    var title=year.value+' '+ branchId +' 校车站点.xlsx'
                }else{
                    var title=year.value+' '+ branchId +' 校车站点模版.xlsx'
                }
				const ws_name = "SheetJS";
				var exportDatas = [];
                if(!type){
                    for(var i=0;i<this.carSiteList.length;i++){
					    exportDatas.push(
						{
						'站点编号':this.carSiteList[i].code,
						'站点名称中文':this.carSiteList[i].title_cn,
						'站点名称英文':this.carSiteList[i].title_en,
						'上学停车位置中文':this.carSiteList[i].pickup_point_cn,
						'上学停车位置英文':this.carSiteList[i].pickup_point_en,
						'上学停车经度':this.carSiteList[i].pickup_longitude,
						'上学停车纬度':this.carSiteList[i].pickup_latitude,
                        '放学停车位置中文':this.carSiteList[i].dropoff_point_cn,
						'放学停车位置英文':this.carSiteList[i].dropoff_point_en,
						'放学停车经度':this.carSiteList[i].dropoff_longitude,
						'放学停车纬度':this.carSiteList[i].dropoff_latitude,
                        '上学放学停车位置是否相同':this.carSiteList[i].same_point,
                        '收费档':this.carSiteList[i].fee_level,
						});
				    }
                }
				
				var wb=XLSX.utils.json_to_sheet(exportDatas,{
					origin:'A1',// 从A1开始增加内容
					header: ['站点编号','站点名称中文','站点名称英文','上学停车位置中文','上学停车位置英文','上学停车经度','上学停车纬度','放学停车位置中文','放学停车位置英文','放学停车经度','放学停车纬度','上学放学停车位置是否相同','收费档'
					],
				});
				const workbook = XLSX.utils.book_new();
				XLSX.utils.book_append_sheet(workbook, wb, ws_name);
				const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
				const blob = new Blob([wbout], {type: 'application/octet-stream'});
				let link = document.createElement('a');
				link.href = URL.createObjectURL(blob);
				link.download = title;
				link.click();
				setTimeout(function() {
					// 延时释放掉obj
					URL.revokeObjectURL(link.href);
					link.remove();
				}, 500);
			},
            repeatData(){
                let samePointCountMap = {};
                let allArray = [...this.carSiteList, ...this.batchList];
                allArray.forEach(item => {
                    if (!samePointCountMap[item.code]) {
                        samePointCountMap[item.code] = {count: 0};
                    }
                    samePointCountMap[item.code].count++;
                });
                const fieldsToCheck = ["title_cn", "title_en", "pickup_point_cn", "pickup_point_en", "dropoff_point_cn", "dropoff_point_en", "same_point", "code"];
                this.batchList = this.batchList.map(item => {
                    let count = samePointCountMap[item.code].count;
                    let isDataNot = fieldsToCheck.some(field => !item[field] || item[field].trim() === "");
                    return {
                        ...item,
                        ...(isDataNot ? {data_repeat: true} : {data_repeat: false}),
                        ...(count > 1 ? {code_repeat: true} : {code_repeat: false})
                    };
                });
            },
            delBatchSite(index,data){
                this.batchList.splice(index,1)
                this.repeatData()
            },
            addSiteBatch(){
                let that=this
                that.btnDisanled=true
                let batchData=[]
                that.batchList.forEach(item => {
                    if(!item.data_repeat && !item.code_repeat){
                        batchData.push(item)
                    }
                });
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        url:'addSiteBatch',
                        start_year:this.startYear,
                        data:batchData
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.getSiteList()
                            resultTip({
                                msg: data.message
                            });
                            $("#batchAddSiteModal").modal('hide')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.btnDisanled=false
                    },
                    error: function(data) {
                        that.btnDisanled=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            copySite(){
                $("#copyModal").modal('show')  
            },
            saveCopySite(){
                let that=this
                that.btnDisanled=true
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        url:'importData',
                        start_year:this.startYear,
                        target_year:this.target_year
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.getSiteList()
                            resultTip({
                                msg: data.message
                            });
                            $("#copyModal").modal('hide')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.btnDisanled=false
                    },
                    error: function(data) {
                        that.btnDisanled=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            getBillConfig(init){
                let that=this
                if(init){
                    that.tabLoading=true
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        url:'getIndexLeft',
                        start_year:this.startYear,
                        target_year:this.target_year
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.familyConfig=data.data
                            that.semesterAdd=data.data.semester
                            that.semesterBill=data.data.semester
                            that.leftSort_family=data.data.sort_family
                            that.tabLoading=false
                            if(init){
                                that.getSiteStudent()
                            }
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.tabLoading=false
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.tabLoading=false
                    },
                })  
            },
            filterchildSite(list,index){
                if(this.childSiteIndex==index){
                    this.childSiteId=[]
                    this.childSiteIndex=null
                }else{
                    let siteIds = list.flatMap(child => child.site_list.map(site => site.site_id));
                      siteIds = [...new Set(siteIds)];
                    if(siteIds.length){
                        this.childSiteId=siteIds
                        this.childSiteIndex=index
                    }
                }
            },
            getSiteStudent(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        url:'getSiteStudent',
                        start_year:this.startYear,
                        target_year:this.target_year
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            let sitesObj={}
                            let carSiteList=JSON.parse(JSON.stringify(data.data.route_data.sites));
                            carSiteList.forEach(item => {
                                item.showClass=false
                                sitesObj[item.id]=item
                            });
                            that.tipVisibles = Object.values(data.data.route_data.route2Site).reduce((acc, list) => {
                                list.forEach(item => {
                                    acc[item.link_id] = false;
                                });
                                return acc;
                                }, {});
                            that.SchoolBusListData=data.data
                            that.SchoolBusListData.sitesObj=sitesObj
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })  
            },
            discountTitle(id){
                return this.familyConfig.config.discount.find(i => i.id === parseInt(id))?.title || '未设置';
            },
            semesterTitle(id){
                return this.familyConfig.config.semester.find(i => i.value === id)?.title;
            },
            newPage(){
                window.open("<?php echo $this->createUrl('busLine', array('branchId' => $this->branchId)); ?>")
            },
            printData(list){
                let year = this.startYearList.find(i => i.key ==this.startYear);
				let branchId = '<?php echo $_GET['branchId'] ?>';
                var title=year.value+'_'+ branchId+"_"+ list.title+'_校车名单.xlsx'
				const ws_name = "SheetJS";
				var exportDatas = [];
                this.SchoolBusListData.route_data.route2Site[list.id].forEach(item => {
                    if(this.SchoolBusListData.list[list.id][item.sid]){
                        let child=this.SchoolBusListData.list[list.id][item.sid]
                        child.forEach(_item => {                        
                            exportDatas.push({
                                'ChildId':_item.child_id,
                                'ChildName':_item.child_name,
                                'Class':_item.class_name,
                                'SiteName':this.SchoolBusListData.sitesObj[item.sid].title,
                                'SiteCode':this.SchoolBusListData.sitesObj[item.sid].code,
                                'Journey':_item.journey==1?"上学":_item.journey==2?"放学":"双程",
                            })
                        });
                    }
                });
				var wb=XLSX.utils.json_to_sheet(exportDatas,{
					origin:'A1',// 从A1开始增加内容
					header: ['ChildId','ChildName','Class','SiteName','SiteCode','Journey']
				});
				const workbook = XLSX.utils.book_new();
				XLSX.utils.book_append_sheet(workbook, wb, ws_name);
				const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
				const blob = new Blob([wbout], {type: 'application/octet-stream'});
				let link = document.createElement('a');
				link.href = URL.createObjectURL(blob);
				link.download = title;
				link.click();
				setTimeout(function() {
					// 延时释放掉obj
					URL.revokeObjectURL(link.href);
					link.remove();
				}, 500);
            },
            importData(){
                $('#importModal').modal('show')
            },
            handleSelectedImg() {
                this.$refs['img-upload-input'].click()
            },
            //选好图片之后点击打开按钮
            submitUpload(e) {
                this.files = e.target.files
                // const rawFile = this.files[0] // only use files[0]
                // if (!rawFile) return
                // $('#fileprogress').modal('show')
                this.uploadSectionFile(this.files[0])
            },
            unUploadFiles(){
                this.$refs['img-upload-input'].value = null
            },
            uploadFiles(){
                this.uploadSectionFile(this.files[0])
            },
            uploadSectionFile(file) {
                let that=this
                this.importBtn=true
                const reader = new FileReader();
                reader.readAsBinaryString(file);
                reader.onload = (e) => {
                    const data = e.target.result;
                    const my_excel = window.XLS.read(data, {
                        type: 'binary'
                    });
                    const sheet2JSONOpts = {
                        defval: ''//给defval赋值为空的字符串
                    }
                    let xlx_json = window.XLS.utils.sheet_to_json(my_excel.Sheets[my_excel.SheetNames[0]],sheet2JSONOpts);
                    let importData = {url:'importSiteStudent',start_year:this.startYear,data:xlx_json};
                    $.post(
                        '<?php echo $this->createUrl('api'); ?>',
                        importData,
                        function (res){
                            if(res.state == 'success') {
                                resultTip({
                                    msg: res.message
                                });
                                that.importBtn=false
                                that.$refs['img-upload-input'].value = null
                                $('#importModal').modal('hide')
                                that.getBillConfig('init')
                            } else {
                                resultTip({
                                    error: 'warning',
                                    msg: res.message
                                });
                                that.importBtn=false
                            }
                        },
                        'json'
                    )
                }
            },
            clearSiteAll(list){
                if(list){
                    this.delType='clearSite'
                    $("#delModal").modal('show')
                    return
                }
                let that=this
                that.btnDisanled=true
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        url:'clearData',
                        start_year:this.startYear,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.handleClick('clear')
                            resultTip({
                                msg: data.message
                            });
                            $("#delModal").modal('hide')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.btnDisanled=false
                    },
                    error: function(data) {
                        that.btnDisanled=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            }
        }
    })
</script>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
