<?php

class AttendController extends BranchBasedController
{
    public $printFW = array();
    public $report;
    public function createUrl($route, $params = array(), $ampersand = '&', $parentOnly = false)
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

        $this->branchSelectParams['urlArray'] = array('//masa/attend/index');

        // jquery ui
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue2.js');
    }

	public function actionIndex()
    {
        //点击日历获取的日期
        $thedate = Yii::app()->request->getParam('thedate', date('Y-m-d'));
        //该日期为星期几（1-7）
        $weekday = date('N', strtotime($thedate));
        //课程组id
        $groupId = intval(Yii::app()->request->getParam('groupId', 0));
        $dateTime = array();
        if($groupId){
            $criteria = new CDbCriteria;
            $criteria->compare('gid',$groupId);
            $courseObj = AsaCourse::model()->findAll($criteria);
            $groupArr = array();
            if($courseObj){
                foreach($courseObj as $item){
                    $groupArr[$item->schedule_id] = $item->schedule_id;
                }
            }
            if($groupArr){
                $scheduleObj = AsaSchedule::model()->findAllByPk($groupArr);
                if($scheduleObj){
                    foreach($scheduleObj as $scheduleObjItem){
                        foreach($scheduleObjItem->items as $item){
                            $dateTime[$item->schedule_date] = date("Y-m-d", strtotime($item->schedule_date));
                        }
                    }
                }
            }

            $criteria = new CDbCriteria;
            $criteria->compare('gid',$groupId);
            $scheduleItemReviseModel = AsaScheduleItemRevise::model()->findAll($criteria);

            if($scheduleItemReviseModel){
                foreach($scheduleItemReviseModel as $item){
                    $dateTime[$item->schedule_date] = date("Y-m-d", strtotime($item->schedule_date));
                }
            }

        }

        if( !Yii::app()->request->isAjaxRequest ) {
            //获取所有课程组  AJAX 不用
            $criteria = new CDbCriteria;
            $criteria->compare('t.schoolid', $this->branchId);
            $criteria->compare('t.program_type', "<>10");
            $criteria->order = "status desc, open_date_start ASC";
            $courseGroupObjs = AsaCourseGroup::model()->findAll($criteria);


            //取校园所有员工信息  AJAX 不用
            if ($groupId) {
                $groupShareSite = array();
                $groupShare = AsaCourseGroupShare::model()->findAllByAttributes(array('group_id'=>$groupId));
                if($groupShare) {
                    foreach($groupShare as $_group){
                        $groupShareSite[] = $_group->site_id;
                    }
                }
                array_unshift($groupShareSite, $this->branchId);

                $vendorObjs = AsaVendor::model()->findAllByAttributes(
                    array('site_id' => $groupShareSite)
                );
            }
        }

        //取项目下所有课程信息，管理课程所分配的员工
        $criteria = new CDbCriteria;
        $criteria->compare('t.gid', $groupId);
        // $criteria->compare('t.schoolid', $this->branchId);
        $criteria->compare('coursestaff.status', 1);
        $criteria->with = "coursestaff";
        $courseObjs = AsaCourse::model()->findAll($criteria);


        //取课程时间安排信息
        // $courseScheduleObjs = AsaCourseSchedule::model()->findAllByAttributes(
        //     array('program_id' => $groupId, 'weekday'=>$weekday)
        // );
        // $courseScheduleObjs = AsaSchedule::model()->findAllByAttributes(
        //     array('program_id' => $groupId, 'week_day'=>$weekday)
        // );


        //取已考勤数据
        $attendedObjs = AsaStaffAttendance::model()->findAllByAttributes(
            array('program_id' => $groupId, 'service_start' => date('Ymd', strtotime($thedate)))
        );

        if( !Yii::app()->request->isAjaxRequest ) {
            //循环课程组 不用AJAX
            $courseGroupData = array();
            foreach ($courseGroupObjs as $courseGroup) {
                $courseGroupData[$courseGroup->id] = array(
                    'id' => $courseGroup->id,
                    'title' => $courseGroup->getName(),
                    'status' => $courseGroup->status,
                    'date_start' => date("Y-m-d", $courseGroup->open_date_start),
                    'date_end' => date("Y-m-d", $courseGroup->open_date_end),
                    'startyear' => $courseGroup->startyear,
                );
            }

            usort($courseGroupData, function($a, $b) {
                return $b['startyear']-$a['startyear'];
            });
            $newCourseGroup = array();
            foreach ($courseGroupData as $v) {
                $newCourseGroup[$v['startyear']][] = $v;
            }
            $startyearArr = array_keys($newCourseGroup);
        }

        // $asaCourseGroup = AsaCourseGroup::model()->find($groupId);
        //循环课程时间安排
        // $scheduleData = array();
        // foreach ($courseScheduleObjs as $schedule) {
        //     foreach ($schedule->courses as $course) {
        //         foreach ($schedule->items as $item) {
        //             $scheduleId = $item->schedule_id . '_' . $item->course_index;
        //             $scheduleData[$course->id][$scheduleId] = $item->getAttributes(array("time_start", "time_end"));
        //             $scheduleData[$course->id][$scheduleId]['status'] = 1;
        //             $scheduleData[$course->id][$scheduleId]['scheduleId'] = $scheduleId;
        //             if($asaCourseGroup->program_type == AsaCourseGroup::PROGRAM_ACTIVE){
        //                 $scheduleData[$course->id][$item->schedule_id . '_' . $item->course_index]['time_end'] = "";
        //                 $scheduleData[$course->id][$item->schedule_id . '_' . $item->course_index]['time_start'] = "";
        //             }
        //         }
        //     }
        // }

        //循环课程，获取课程名称及该课程下所分配老师及职位
        $scheduleData = array();
        $courseData = array();
        $attendanceByDayData = array();

        foreach ($courseObjs as $course) {
            $courseData[$course->id]['title'] = $course->getTitle();
            foreach ($course->coursestaff as $staff) {
                $courseData[$course->id]['vendors'][$staff->vendor_id] = array(
                    "job_type" => $staff->job_type
                );
            }
            //循环课程时间安排
            foreach ($course->schedule->items as $item) {
                $scheduleId = $course->id . '_' . $item->schedule_id . '_' . $item->course_index;

                $criteria = new CDbCriteria;
                $criteria->compare('t.schedule_item_id', $item->id);
                $criteria->compare('t.cid', $course->id);
                $criteria->order  = "updated DESC";
                $criteria->limit  = '1';
                $reviseModel = AsaScheduleItemRevise::model()->find($criteria);

                if($item->schedule_date == date("Ymd",strtotime($thedate)) || (isset($reviseModel) && $reviseModel->schedule_date == date("Ymd",strtotime($thedate)))){
                    if(isset($reviseModel)) {
                       if($reviseModel->cid == $course->id && $reviseModel->schedule_date == date("Ymd",strtotime($thedate))){
                            $scheduleData[$course->id][$scheduleId] = $item->scheduleItemRevise->getAttributes(array("time_start", "time_end"));
                            $scheduleData[$course->id][$scheduleId]['status'] = 1;
                            $scheduleData[$course->id][$scheduleId]['scheduleId'] = $scheduleId;
                       }
                    }else{
                        $scheduleData[$course->id][$scheduleId] = $item->getAttributes(array("time_start", "time_end"));
                        $scheduleData[$course->id][$scheduleId]['status'] = 1;
                        $scheduleData[$course->id][$scheduleId]['scheduleId'] = $scheduleId;
                    }
                }
            }

            $courseData[$course->id]['schedules'] = $scheduleData[$course->id];
            $attendedSchedules = array();
            foreach(array_keys($scheduleData[$course->id]) as $_sid){
                $attendedSchedules[$_sid] = array();
            }

            $attendanceByDayData[$course->id]['attendeds'] = $attendedSchedules;
            $courseData[$course->id]['attendeds'] = array();
        }

        if( !Yii::app()->request->isAjaxRequest ) {
            //循环vendor  AJAX 不用
            $vendorData = array();
            $vendorTypeData = array();
            foreach ($vendorObjs as $vendor) {
                $vendorData[$vendor->vendor_id] = array(
                    'title' => $vendor->getName(),
                    'type' => $vendor->type,
                    'active' => $vendor->active,
                    'ivy_uid' => $vendor->ivy_uid
                );
                $vendorTypeData[$vendor->type][] = $vendor->vendor_id;
            }
        }

        //循环已考勤数据

        foreach ($attendedObjs as $attended) {
            if($attended->status != 0 ){
                $attendanceByDayData[$attended->course_id]['attendeds'][$attended->course_schedule_id][$attended->vendor_id] = array(
                    'vendor_id' => $attended->vendor_id,
                    'status' => $attended->status,
                    'substituter' => $attended->substituter
                );
                $vendorData[$attended->course_id]['attendeds'] = $attendanceByDayData[$attended->course_id]['attendeds'];
            }

        }

        if( Yii::app()->request->isAjaxRequest ){
            $data = array(
                'attendanceByDayData' => $attendanceByDayData,
                'scheduleData' => $scheduleData,
            );
            echo CJSON::encode($data);
        } else {
            $this->render('index', array(
                'groupId'=>$groupId,
//                'courseGroupData' => $courseGroupData,
                'thedate' => $thedate,
                'courseData' => $courseData,
                'scheduleData' => $scheduleData,
                'vendorData' => $vendorData, //所有老师
                'vendorTypeData' => $vendorTypeData,
                'attendanceByDayData' => $attendanceByDayData,
                'dateTime' => $dateTime,
                'startyearArr' => $startyearArr,
                'newCourseGroup' => $newCourseGroup,

            ));
        }

	}

    /**
     * [ 增加老师考勤]
     * @param  [type] $scheduleId [课程时间表ID]
     * @param  [type] $vendorId [分配老师ID]
     * @param  [type] $substituter [替补老师ID] 可以为空
     * @param  [type] $status [状态]
     * @param  [type] $serviceTime [时间]
     * @return [type]          [本门课程老师签到的记录]
     */

    public function actionAddAttendance()
    {
        Yii::import('common.models.asainvoice.*');
        $scheduleId = Yii::app()->request->getParam('scheduleId', '');
        $substituter = Yii::app()->request->getParam('substituter', array());
        $status = Yii::app()->request->getParam('status', array());
        $serviceTime = Yii::app()->request->getParam('serviceTime', '');
        if($status){
            $criteria = new CDbCriteria;
            $criteria->compare('course_schedule_id', $scheduleId);
            $criteria->compare('service_start', date("Ymd",strtotime($serviceTime)));
            $criteria->compare('status', 1);
            $countf = AsaStaffAttendance::model()->count($criteria);

            if(!$countf){
                $array = explode('_', $scheduleId);
                $course_id = $array[0];
                $schedule_id = $array[1];
                $course_index = $array[2];
                $attendance = AsaScheduleItem::model()->findAllByAttributes(array(
                    'schedule_id' => $schedule_id,
                    'course_index' => $course_index
                ));
                if(!$attendance){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('global', "参数错误"));
                    $this->showMessage();
                }
                $course = AsaCourse::model()->findByPk($course_id);
                if(!$course){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('global', "参数错误"));
                    $this->showMessage();
                }
                $program_id = $course->gid;

                $criteria = new CDbCriteria;
                $criteria->compare('program_id', $program_id);
                $criteria->compare('course_id', $course_id);
                $courseStaff = AsaCourseStaff::model()->findAll($criteria);
                $staffId = array();

                foreach($courseStaff as $staff){
                    $staffId[$staff->id] = $staff->vendor_id;
                }


                foreach($status as $teacherId=>$_vendoeId){
                    $model = new AsaStaffAttendance;
                    $model->program_id = $program_id;
                    $model->course_id = $course_id;
                    $model->vendor_id = $staffId[$teacherId];
                    $model->status = $_vendoeId;
                    $model->substituter = ($substituter[$staffId[$teacherId]]) ? $substituter[$staffId[$teacherId]] : "";
                    $model->course_schedule_id = $scheduleId;
                    $model->service_start = date("Ymd",strtotime($serviceTime));
                    $model->service_end = date("Ymd",strtotime($serviceTime));
                    $model->course_staff_id = $teacherId;
                    $model->updated = time();
                    $model->updated_by = Yii::app()->user->id;
                    $model->save();
                }

                $courseList = array();

                $criteria = new CDbCriteria;
                $criteria->compare('course_schedule_id', $attendance->schedule_id);
                $criteria->compare('status', ">0");
                $criteria->compare('service_start', date("Ymd",strtotime($serviceTime)));
                $attendance = AsaStaffAttendance::model()->findAll($criteria);

                if($attendance){
                    foreach($attendance as $k=>$_teachder){
                        $courseList['courseid'] = $_teachder->course_id;
                        $courseList['attends'][$_teachder->course_schedule_id][$_teachder->vendor_id] = array(
                            'course_schedule_id' => $_teachder->course_schedule_id,
                            'status' => $_teachder->status,
                            'substituter' => $_teachder->substituter ,
                        );
                    }
                }

                $this->addMessage('state', 'success');
                $this->addMessage('data', $courseList);
                $this->addMessage('message', Yii::t('message', "保存成功"));
                $this->addMessage('callback', 'attendanced');
                $this->showMessage();
            }else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('global', "已经有签到,清先删除签到"));
                $this->showMessage();
            }
        }else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('global', "没有安排的老师"));
            $this->showMessage();
        }
    }

    // 根据时间 课程的节数 来获取要签到的老师的数据

    public function actionAddTeacher()
    {
        
        $scheduleId = Yii::app()->request->getParam('scheduleId', '');
        $courseId = Yii::app()->request->getParam('courseId', '');

        $criteria = new CDbCriteria;
        $criteria->compare('course_schedule_id', $scheduleId);
        $criteria->compare('status', ">0");
        $criteria->index = 'vendor_id';
        $attendedObjs = AsaStaffAttendance::model()->findAll($criteria);

        $attObj = array();
        if($attendedObjs){
            foreach($attendedObjs as $item){
                $attObj[$item->vendor_id] = $item->course_staff_id;
            }
        }
        $criteria = new CDbCriteria;
        $criteria->compare('course_id', $courseId);
        $criteria->compare('status', 1);
        $criteria->index = 'vendor_id';
        $courseStaffObjs = AsaCourseStaff::model()->findAll($criteria);

        if($courseStaffObjs){
            foreach($courseStaffObjs as $item){
                $attObj[$item->vendor_id] = $item->id;
            }
        }

        echo CJSON::encode($attObj);

    }

    /**
     * [ 删除老师考勤]
     * @param  [type] $scheduleId [课程时间表ID]
     * @return [type]          [本门课程老师签到的记录]
     */

    public function actionDelAttendance()
    {
        Yii::import('common.models.asainvoice.*');

        $scheduleId = Yii::app()->request->getParam('course_schedule_id', '');
        $targetDate = Yii::app()->request->getParam('targetDate', '');


        $criteria = new CDbCriteria;
        $criteria->compare('course_schedule_id', $scheduleId);
        $criteria->compare('service_start', date("Ymd",strtotime($targetDate)));
        $criteria->compare('status', "<>0");
        $courseStaff = AsaStaffAttendance::model()->findAll($criteria);

        if($courseStaff){
            $reporitArr = array();
            $thirdreporitArr = array();
            foreach($courseStaff as $item){
                if($item->report_id){
                    if(isset($item->course)){
                        if($item->course->unit_type == 99){
                            $thirdreporitArr[$item->report_id] = $item->report_id;
                        }
                        $reporitArr[$item->report_id] = $item->report_id;
                    }
                }
            }

            $reportCount = 0;
            $thirdreportCount = 0;
            if($reporitArr){
                $criteria = new CDbCriteria;
                $criteria->compare('id', $reporitArr);
                $criteria->compare('status', array(AsaMonthReport::SUBMIT, AsaMonthReport::CONFIRM));
                $criteria->compare('schoolid', $this->branchId);
                $criteria->compare('report_month', date("Ym",strtotime($targetDate)));
                $reportCount = AsaMonthReport::model()->count($criteria);
            }
            if($thirdreporitArr) {
                $criteria = new CDbCriteria;
                $criteria->compare('id', $thirdreporitArr);
                $criteria->compare('status', 1);
                $criteria->compare('school_id', $this->branchId);
                $criteria->compare('report_month', date("Ym", strtotime($targetDate)));
                $thirdreportCount = AsaThirdpartReport::model()->count($criteria);
            }

            if($reportCount == 0 && $thirdreportCount == 0){
                $data = array();
                foreach($courseStaff as $_courseStaff){
                    $data['scheduleId'] =  $scheduleId;
                    $data['courseid'] =  $_courseStaff->course_id;
                    $_courseStaff->status = 0;
                    $_courseStaff->save();
                }
                $courseId = explode('_', $scheduleId);


                $criteria = new CDbCriteria;
                $criteria->compare('course_id', $courseId[0]);
                $criteria->compare('schedule_date', date("Ymd",strtotime($targetDate)));
                $asaThirdpartReportItemModel = AsaThirdpartReportItem::model()->find($criteria);
                if($asaThirdpartReportItemModel){
                    $asaThirdpartReportItemModel->delete();
                }
                $course_month = $courseId[0] . '_' . substr(date("Ymd",strtotime($targetDate)), 0,6);
                $criteria = new CDbCriteria;
                $criteria->compare('course_id', $courseId[0]);
                $criteria->compare('course_month', $course_month);
                $thirdPartCountModel = AsaThirdpartReportItem::model()->findAll($criteria);

                $criteria = new CDbCriteria;
                $criteria->compare('course_id', $courseId[0]);
                $criteria->compare('school_id', $this->branchId);
                $criteria->compare('report_month', substr(date("Ymd",strtotime($targetDate)), 0,6));
                $thirdPartModel = AsaThirdpartReport::model()->find($criteria);

                if(count($thirdPartCountModel)){
                    $number = 0;
                    foreach ($thirdPartCountModel as $val){
                        $number += $val->number;
                    }

                    if($thirdPartModel){
                        $thirdPartModel->total_amount = $thirdPartModel->unit_price * $number;
                        $thirdPartModel->save();
                    }
                }else{
                    if($thirdPartModel) {
                        $thirdPartModel->delete();
                    }
                }

                $this->addMessage('state', 'success');
                $this->addMessage('data', $data);
                $this->addMessage('message', Yii::t('message', "取消签到成功"));
                $this->addMessage('callback', 'cancelAttendance');
                $this->showMessage();
            }else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', "已经生成报告提交，不可修改"));
                $this->showMessage();
            }
        }
    }

    public function actionOtherStaffs()
    {
        $this->branchSelectParams['urlArray'] = array('//masa/attend/otherStaffs');

        Yii::import('common.models.asainvoice.*');
        $groupId = intval(Yii::app()->request->getParam('groupId', 0));

        //获取所有课程组
        $criteria = new CDbCriteria;
        $criteria->compare('t.schoolid', $this->branchId);
        $criteria->compare('t.status',AsaCourseGroup::STATUS_ACTIVE);
        $criteria->index = 'id';
        $asaCourseGroup = AsaCourseGroup::model()->findAll($criteria);

        $course_group=array();
        //循环课程组
        foreach($asaCourseGroup as $key=>$_courseGroup){
            $course_group[$_courseGroup->id] = array(
                'id' => $_courseGroup->id,
                'courseGroupName' => $_courseGroup->getName(), 
                'courseGroupId' => $_courseGroup->id, 
                'dateStart'=> date("Y-m-d",$_courseGroup->open_date_start), 
                'dateEnd' => date("Y-m-d",$_courseGroup->open_date_end),
                'startyear' => $_courseGroup->startyear,
            );
        }

        usort($course_group, function($a, $b) {
            return $b['startyear']-$a['startyear'];
        });
        $newCourseGroup = array();
        foreach ($course_group as $v) {
            $newCourseGroup[$v['startyear']][] = $v;
        }
        $startyearArr = array_keys($newCourseGroup);
        if($groupId){
            $groupObj = AsaCourseGroup::model()->findByPk($groupId);
            $operatorlist=array();
            $all_operatorlist=array();
            $jobTypeNum = 10;
            //获取所有工作人员
            $criteria = new CDbCriteria;
            $criteria->compare('t.program_id', $groupId);
            $criteria->compare('t.site_id', $this->branchId);
            $criteria->compare('t.job_type', ">={$jobTypeNum}");
            $cStaff = AsaCourseStaff::model()->findAll($criteria);

            if($groupObj->program_type != AsaCourseGroup::PROGRAM_DELAYCARE){
                //获取所有签过到的工作人员

                $criteria = new CDbCriteria;
                $criteria->compare('t.program_id', $groupId);
                $criteria->compare('course.site_id', $this->branchId);
                $criteria->compare('course.job_type', ">={$jobTypeNum}");
                $criteria->with='course';
                $staffAttendance = AsaStaffAttendance::model()->findAll($criteria);

                //获取数据为空则返回空数组

                $typeList = CommonUtils::LoadConfig('CfgASA');
                //循环已签到工作人员
                foreach($staffAttendance as $key=>$val){
                    $operatorlist[$val->program_id][$val->id] = array(
                        'id' => $val->id,
                        'courseStaffId'=>$val->course->id,
                        'operatorId'=> $val->vendor_id,
                        'dateStart' => date("Y-m-d",strtotime($val->service_start)),
                        'dateEnd' => date("Y-m-d",strtotime($val->service_end)),
                        'operatorName' => $val->vendorTeacher->name_cn,
                        'operatorPrice' => $val->course->unit_salary,
                        'operatorType' => $val->course->job_type,
                        'operatorTypeName' => $typeList['job_type'][$val->course->job_type]['cn'],
                        'checkInType'=> $val->course->unit_type,
                        'checkInTypeName' => $typeList['unit_type'][$val->course->unit_type]['cn'],
                    );
                }

              /*  //获取所有工作人员
                $criteria = new CDbCriteria;
                $criteria->compare('t.program_id', $groupId);
                $criteria->compare('t.site_id', $this->branchId);
                $criteria->compare('t.job_type', ">={$jobTypeNum}");
                $cStaff = AsaCourseStaff::model()->findAll($criteria);*/

                //定义时间（按天/月）
                $_today = date("Y-m-d");
                $_month_start = date("Y-m-d", mktime(0, 0, 0, (date('m')), 1, (date('Y'))));
                $_month_end = date("Y-m-d", mktime(0, 0, 0, date('n', strtotime($_month_start))+1, date('j', strtotime($_month_start))-1, date('Y', strtotime($_month_start))));

                //循环工作人员
                foreach($cStaff as $keys=>$vals){
                    //定义时间（按期）
                    $_season_start = $course_group[$vals->program_id]['dateStart'];
                    $_season_end = $course_group[$vals->program_id]['dateEnd'];

                    $pid= $vals->program_id;
                    $vid= $vals->id;
                    $uty=$vals->unit_type;
                    $all_operatorlist[$pid][$vid]= array(
                        'operatorName' => $vals->vendor->getName(),
                        'courseStaffId' => $vals->id,
                        'operatorId' => $vals->vendor_id,
                        'operatorPrice' => $vals->unit_salary,
                        'operatorTypeName' => $typeList['job_type'][$vals->job_type]['cn'],
                        'operatorType'=> $vals->job_type,
                    );
                    $all_operatorlist[$pid][$vid]['unitType'] = array(
                        'title' => $vals->unit_salary.'RMB/'.$typeList['unit_type'][$vals->unit_type]['cn'],
                        'unitType'=> $vals->unit_type,
                        'dateStart' => ($vals->unit_type==2) ? $_today : ( ($vals->unit_type==3) ? $_month_start : $_season_start ),
                        'dateEnd' => ($vals->unit_type==2) ? $_today : ( ($vals->unit_type==3) ? $_month_end : $_season_end ),
                    );
                }
            }else{

                //签到老师 数据
                $criteria = new CDbCriteria;
                $criteria->compare('program_id', $groupId);
                $attendanceObj = AsaStaffAttendance::model()->findAll($criteria);
                $attendanceObjTeacher = array();
                if($attendanceObj){
                    foreach($attendanceObj as $attrndItem){
                        $attendanceObjTeacher[$attrndItem->service_start][$attrndItem->course_id]['teacher'] = $attrndItem->course_staff_id;
                        $attendanceObjTeacher[$attrndItem->service_start][$attrndItem->course_id]['id'] = $attrndItem->id;
                    }
                }

                $criteria = new CDbCriteria;
                $criteria->compare('site_id', $this->branchId);
                $criteria->compare('program_id', $groupId);
                $criteria->compare('status', '<>6');
                $refundObj = AsaRefund::model()->findAll($criteria);
                $refundArr = array();
                if($refundObj){
                    foreach($refundObj as $refundItem){
                        $refundArr[$refundItem->invoice_item_id][$refundItem->course_id][$refundItem->childid] = $refundItem->status;
                    }
                }

                $criteria = new CDbCriteria;
                $criteria->compare('t.course_group_id', $groupId);
                $criteria->with = "asainvoiceitem";
                $criteria->order = "extra_date desc";
                $itemExtra = AsaInvoiceItemExtra::model()->findAll($criteria);


                $criteria = new CDbCriteria;
                $criteria->compare('t.schoolid', $this->branchId);
                //$criteria->compare('t.status', array(10,20));
                $criteria->index = "childid";
                $childProfileBasic = ChildProfileBasic::model()->findAll($criteria);

                $criteria = new cDbCriteria;
                $criteria->compare('stat', IvyClass::STATS_OPEN);
                $criteria->compare('schoolid', $this->branchId);
                $criteria->order = 'title';
                $criteria->index = 'classid';
                $classes = IvyClass::model()->findAll($criteria);

                $childExtra = array();
                foreach($itemExtra as $k=>$item){
                    if($item->asainvoiceitem->status == AsaInvoice::STATS_PAID){
                        $childExtra[$item->extra_date]['time'] = date("Y-m-d", strtotime($item->extra_date));
                        $childStatus = isset($refundArr[$item->invoice_item_id]) ? (($refundArr[$item->invoice_item_id][$item->course_id][$item->asainvoice->childid] == 5)? "(已退费)" : "(正在退费中)" ) : "";
                        $childExtra[$item->extra_date][$item->course_id]['classes'][$childProfileBasic[$item->asainvoice->childid]->classid][] = array(
                            'id' => $childProfileBasic[$item->asainvoice->childid]->getChildName(),
                            'status' => $childStatus,
                        );
                        $childExtra[$item->extra_date][$item->course_id]['classes'][$childProfileBasic[$item->asainvoice->childid]->classid]['className'] = $classes[$childProfileBasic[$item->asainvoice->childid]->classid]->title;
                        $childExtra[$item->extra_date][$item->course_id]['teacher'] = isset($attendanceObjTeacher) && isset($attendanceObjTeacher[$item->extra_date][$item->course_id]) ? $attendanceObjTeacher[$item->extra_date][$item->course_id]: "" ;
                        $childExtra[$item->extra_date][$item->course_id]['courseId'] = $item->course_id;
                        $childExtra[$item->extra_date][$item->course_id]['courseName'] = $item->asacourse->getTitle();
                    }
                }
                $teacherList = array();
                foreach($cStaff as $staff){
                    $teacherList[$staff->id] = $staff->vendor->getName();
                }
            }
        }

        $this->render('otherstaffs', array(
            'groupId'  => $groupId,
            'groupObj'  => $groupObj,
            'course_group'=>$course_group,
            'all_operatorlist'=>$all_operatorlist,
            'operatorlist'=>$operatorlist,
            'teacherList'=>$teacherList,
            'childExtra'=>$childExtra,
            'startyearArr' => $startyearArr,
            'newCourseGroup' => $newCourseGroup,
        ));
    }

    public function actionAddOtherStaffs()
    {
        Yii::import('common.models.asainvoice.*');
        if (Yii::app()->request->isPostRequest){
            //接收传来的参数
            $id = Yii::app()->request->getParam('id', ''); //正常签到的老师id
            $program_id = Yii::app()->request->getParam('courseGroupId', '');//课程组id
            $courseStaffId = Yii::app()->request->getParam('courseStaffId', ''); //主键id
            $service_start = Yii::app()->request->getParam('dateStart', '');
            $time_end = Yii::app()->request->getParam('dateEnd', ''); //结束时间

            if($program_id){
                $groupObj = AsaCourseGroup::model()->findByPk($program_id);



                if(strtotime($service_start) < $groupObj->open_date_start || strtotime($time_end) > $groupObj->open_date_end){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('message', '签到时间必须在课程组开放时间之内'));
                    $this->showMessage();
                }



                //查询需要的参数
                $criteria = new CDbCriteria;
                $criteria->compare('t.id',$courseStaffId);
                $criteria->with='vendor';
                $finds = AsaCourseStaff::model()->find($criteria);

                //判断开始时间是否大于结束时间
                $start = strtotime($service_start);
                $end = strtotime($time_end);

                //当按日查询时定义结束时间
                if($time_end){
                    //有返回结束时间
                    if($start>$end){
                        //判断开始时间是否大于结束时间
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('message', '开始时间不能大于结束时间'));
                        $this->showMessage();
                    }else{
                        $service_end = $time_end;
                    }
                }else{
                    //无返回结束时间
                    $service_end = $service_start;
                }

                $stime = date('Ymd',strtotime($service_start));
                $etime = date('Ymd',strtotime($service_end));

                //查询数据是否已经存在（存在=修改 不存在=添加）
                $model = AsaStaffAttendance::model()->findByPk($id);

                if (!$model) {
                    $model = new AsaStaffAttendance;

                    //判断工作人员是否已经签到
                    $criteria = new CDbCriteria;
                    $criteria->compare('t.program_id',$program_id);
                    $criteria->compare('t.vendor_id', $finds->vendor->vendor_id);
                    $criteria->compare('t.service_start', "<={$etime}");
                    $criteria->compare('t.service_end',">={$stime}");
                    $criteria->compare('course.id', $courseStaffId);
                    // $criteria->compare('course.unit_type',$unit_type);
                    // $criteria->compare('course.job_type',$job_type);
                    $criteria->with='course';
                    $aTten = AsaStaffAttendance::model()->count($criteria);

                    if($aTten){
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('message', '该工作人员已经签过到'));
                        $this->showMessage();
                    }
                }

                $model->setAttributes(array(
                    'service_start' => $stime,
                    'service_end' => $etime,
                    'status' =>1,
                    'course_staff_id' => $courseStaffId,
                    'course_id' => $finds->course_id,
                    'program_id' => $program_id,
                    'vendor_id' => $finds->vendor->vendor_id,
                    'course_schedule_id' => $courseStaffId,
                    'updated'=> time(),
                    'updated_by' => $this->staff->uid,
                ));

                $typeList = CommonUtils::LoadConfig('CfgASA');
                if ($model->save()){
                    //保存成功之后返回信息
                    $callback = array(
                        'id' => $model->id,
                        'dateStart' => date('Y-m-d',strtotime($stime)),
                        'dateEnd' => date('Y-m-d',strtotime($etime)),
                        'operatorName' => $finds->vendor->name_cn,
                        'operatorId' => $finds->vendor->vendor_id,
                        'checkInTypeName' => $typeList['unit_type'][$finds->unit_type]['cn'],
                        'checkInType' => $finds->unit_type,
                        'operatorType' => $finds->job_type,
                        'operatorTypeName' => $typeList['job_type'][$finds->job_type]['cn'],
                        'operatorPrice' => $finds->unit_salary,
                        'courseStaffId' => $courseStaffId,
                    );

                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message','success'));
                    $this->addMessage('data', $callback);
                    $this->addMessage('callback', 'addoperator');
                }else{
                    $error = current($model->getErrors());
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('message', $error[0]));
                }
                $this->showMessage();
            }
        }
    }


    public function actionAddDelay()
    {
        if(Yii::app()->request->isPostRequest){
            $teacherId = intval(Yii::app()->request->getParam('teacherId', 0)); //签到的老师id
            $groupId = Yii::app()->request->getParam('groupId', '');//课程组id
            $courseId = Yii::app()->request->getParam('courseId', ''); //课程ID
            $date = Yii::app()->request->getParam('date', ''); //结束
            $dataTime = date("Ymd", strtotime($date));

            $staffObj = AsaCourseStaff::model()->findByPk($teacherId);
            $model = new AsaStaffAttendance();
            $model->program_id = $groupId;
            $model->course_id = $courseId;
            $model->vendor_id = $staffObj->vendor->vendor_id;
            $model->status = 10;
            $model->course_schedule_id = 0;
            $model->service_start = $dataTime;
            $model->service_end = $dataTime;
            $model->course_staff_id = $teacherId;
            $model->updated = time();
            $model->updated_by = Yii::app()->user->id;
            if($model->save()){
                $data = array('data' => $date, 'id' =>$model->id,  'courseId' => $model->course_id, "teacherId" => $model->course_staff_id);
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('data', $data);
                $this->addMessage('callback', 'addDelay');
            }else{
                $error = current($model->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', $error[0]));
            }
            $this->showMessage();

        }
    }

    public function actionDeleteOtherStaffs()
    {
        Yii::import('common.models.asainvoice.*');

        if (Yii::app()->request->isPostRequest){
            $id = Yii::app()->request->getParam('id', '');
            if($id){
                $criteria = new CDbCriteria;
                $criteria->compare('t.id',$id);
                $criteria->compare('course.site_id',$this->branchId);
                $criteria->with='course';
                $model = AsaStaffAttendance::model()->find($criteria);

                $reportCount = 0;
                if ($model->report_id) {
                    $criteria = new CDbCriteria;
                    $criteria->compare('id', $model->report_id);
                    $criteria->compare('status', AsaMonthReport::SUBMIT);
                    $reportCount = AsaMonthReport::model()->count($criteria);
                }
                if($reportCount == 0){
                    if ($model->deleteByPk($id)){
                        $callback=array(
                           'id'=>$id,
                        );
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', Yii::t('message','删除成功!'));
                        $this->addMessage('data', $callback);
                        $this->addMessage('callback', 'deloperator');
                    }else{
                        $error = current($model->getErrors());
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('message', $error[0]));
                    }
                    $this->showMessage();
                }else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('message', "已经生成报告，不可删除"));
                    $this->showMessage();
                }
            }
        }        
    }


    public function actionDeleteExtra()
    {
        Yii::import('common.models.asainvoice.*');

        if (Yii::app()->request->isPostRequest){
            $id = Yii::app()->request->getParam('id', '');
            if($id){
                $criteria = new CDbCriteria;
                $criteria->compare('t.id',$id);
                $criteria->compare('course.site_id',$this->branchId);
                $criteria->with='course';
                $model = AsaStaffAttendance::model()->find($criteria);
                $callback=array('courseId'=>$model->course_id,'data' => date("Y-m-d", strtotime($model->service_start)));
                if ($model->deleteByPk($id)){
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message','删除成功!'));
                    $this->addMessage('data', $callback);
                    $this->addMessage('callback', 'delDelay');
                }else{
                    $error = current($model->getErrors());
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('message', $error[0]));
                }
                $this->showMessage();
            }
        }
    }

    //查询当前课程组下的所有课程和工作人员
    public function actionInfoByDate()
    {
        if (Yii::app()->request->isPostRequest){
            $groupId = intval(Yii::app()->request->getParam('groupId', 0));
            $dayTime = Yii::app()->request->getParam('dayTime', '');

            $criteria = new CDbCriteria;
            $criteria->compare('schedule_date',date("Ymd", strtotime($dayTime)));
            $criteria->index = 'schedule_id';
            $courseItemTimeObj = AsaScheduleItem::model()->findAll($criteria);
            $array =array('daytime' => $dayTime,'course'=>array());
            if($courseItemTimeObj){
                $status = 99;
                $criteria = new CDbCriteria;
                $criteria->compare('schedule_id', array_keys($courseItemTimeObj));
                $criteria->compare('gid', $groupId);
                $criteria->compare('status', "<>{$status}");
                $courseObj = AsaCourse::model()->findAll($criteria);
                $array =array('daytime' => $dayTime);
                foreach($courseObj as $item){
                    $array['course'][$item->id] = $item->getTitle();
                }
            }

            $num = 10;
            $criteria = new CDbCriteria;
            $criteria->compare('job_type', ">={$num}");
            $criteria->compare('program_id', $groupId);
            $criteria->index = 'id';
            $courseStafObj = AsaCourseStaff::model()->findAll($criteria);
            $typeList = CommonUtils::LoadConfig('CfgASA');

            $criteria = new CDbCriteria;
            $criteria->compare('course_staff_id', array_keys($courseStafObj));
            $criteria->compare('program_id', $groupId);
            $criteria->compare('service_start', date("Ymd", strtotime($dayTime)));
            $criteria->index = 'course_staff_id';
            $courseAttendancObj = AsaStaffAttendance::model()->findAll($criteria);
            foreach($courseStafObj as $item){
                $job_type = (Yii::app()->language == "zh_cn") ? $typeList['job_type'][$item->job_type]['cn'] : $typeList['job_type'][$item->job_type]['en'];
                if(isset($courseAttendancObj[$item->id])){
                    $array['courseStaff'][$item->id] = $item->vendor->getName() . " <code>" . $job_type.'</code>';
                }
            }
            echo CJSON::encode($array);
        }
    }

    // 考勤汇总
    public function actionReport()
    {
        $this->branchSelectParams['urlArray'] = array('//masa/attend/report');

        Yii::import('common.models.asainvoice.*');

        $criteria = new  CDbCriteria();
        $criteria->compare('schoolid', $this->branchId);
        $criteria->order = 'report_month DESC';
        $reports = AsaMonthReport::model()->findAll($criteria);

        // 查询已存在的报表
        $list = array();
        if ($reports) {
            foreach ($reports as $report) {
                $year = substr($report->report_month, 0, 4);
                $month = substr($report->report_month, 4, 2);
                $list[$year][$month]['status'] = $report->status;
                $list[$year][$month]['amount'] = $report->total_amount;
            }
        }

        $cYear = date('Y', time());
        $cMonth = date('m', time());
        if (!isset($list[$cYear][$cMonth])) {
            $list[$cYear][$cMonth]['status'] = 0;
            $list[$cYear][$cMonth]['amount'] = 0;
        }

        // 获取漏签到的数据
        $criteria = new CDbCriteria();
        $criteria->with = 'course';
        $criteria->compare('course.site_id', $this->branchId);
        $criteria->compare('course.unit_type', array(1,2,3,4));
        $criteria->compare('t.status', array(1,3,10));
        $criteria->compare('t.service_end', '>' . '20170831');
        $criteria->compare('t.service_end', '<' . $cYear . $cMonth . '01');
        $criteria->addCondition('`report_id` IS NULL');
        $attendances = AsaStaffAttendance::model()->findAll($criteria);
        $data = array();
        $i = 0;
        foreach ($attendances as $attendance) {
            // 判断是否已经补签
            $criteria = new cDbCriteria();
            $criteria->compare('attendance_id', $attendance->id);
            $isRepatch = AsaMonthReportRepatch::model()->exists($criteria);
            if ($isRepatch) {
                continue;
            }
            $data[$i]['id'] = $attendance->id;
            $data[$i]['course'] = isset($attendance->courseTitle)?$attendance->courseTitle->title_cn:$attendance->course->courseGroup->title_cn;
            $data[$i]['vendor'] = $attendance->vendorTeacher->getName();
            if (isset($attendance->vendor)) {
                $data[$i]['vendor'] = $attendance->vendorTeacher->getName() . "（代课：{$attendance->vendor->getName()}）";
                $data[$i]['substituter'] = $attendance->vendor->getName();
            }
            $data[$i]['service_start'] = $attendance->service_start;
            $data[$i]['service_end'] = $attendance->service_end;
            $i++;
        }

        $yearArray = array_keys($list);
        rsort($yearArray);


        $this->render('report', array('list'=>$list, 'data'=>$data, 'yearArray'=>$yearArray));
    }

    // 考勤汇总提交
    public function actionReportSubmit()
    {
        if (Yii::app()->request->isPostRequest) {
            $month = Yii::app()->request->getParam('month', '');
            $report = AsaMonthReport::model()->findByAttributes(array('schoolid'=>$this->branchId, 'report_month'=>$month));
            if ($report) {
                $nMonth = date('Ym', strtotime($report->report_month .'01 '. '+1 month'));
                // 查询是否有未生成的数据
                $criteria = new CDbCriteria();
                $criteria->with = 'course';
                $criteria->compare('course.site_id', $this->branchId);
                $criteria->compare('course.unit_type', array(1,2,3,4));
                $criteria->compare('t.status', array(1,3,10));
                $criteria->compare('t.service_end', '>=' . $month . '01');
                $criteria->compare('t.service_end', '<' . $nMonth . '01');
                $criteria->addCondition('`report_id` IS NULL');
                $attendances = AsaStaffAttendance::model()->exists($criteria);
                // 由于外部公司不计算在内，暂时关闭提交限制
                // if ($attendances) {
                //     $this->addMessage('state', 'fail');
                //     $this->addMessage('message', '当月数据未完全生成，请重新生成数据后再次提交');
                //     $this->showMessage();
                // }

                if ($report->status == AsaMonthReport::SUBMIT) {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '报表已提交，不可再更改');
                    $this->showMessage();
                }

                $report->status = AsaMonthReport::SUBMIT;
                if (!$report->save()) {
                    $error = $report->getErrors();
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', $error[0]);
                    $this->showMessage();
                }
                $this->addMessage('state', 'success');
                $this->showMessage();
            }
        }
    }

    // 考勤报表生成或查看数据
    public function actionShowReport()
    {
        if (!Yii::app()->request->isPostRequest) {
            $this->addMessage('state', 'fail');
            $this->showMessage();
        }
        $month = Yii::app()->request->getParam('month', '');
        $action = Yii::app()->request->getParam('action', '');

        if (!$month || !$action) {
            $this->addMessage('state', 'fail');
            $this->showMessage();
        }
        $report = AsaMonthReport::model()->findByAttributes(array('schoolid'=>$this->branchId, 'report_month'=>$month));
        if (!$report) {
            $report = new AsaMonthReport();
            $report->schoolid = $this->branchId;
            $report->report_month = $month;
            $report->total_amount = 0;
            $report->status = AsaMonthReport::PREPAR;
            $report->uid = $this->staff->uid;
            $report->save();
        }
        // 查看数据
        if ($action == 'show') {
            $data = $report->showData(array('schoolid'=>$this->branchId, 'report_month'=>$month));
            $data['school'] = $this->branchObj->abb;
            echo json_encode($data);
        }
        // 生成数据
        if ($action == 'general') {
            if ($report->status == AsaMonthReport::SUBMIT) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '报表已提交，不可再更改');
                $this->showMessage();
            }
            $reportRepatchDate = $report->generalData();

            $data = ($reportRepatchDate) ? array('total_amount'=>$report->total_amount, 'reportRepatchDate' => $reportRepatchDate) :array('total_amount'=>$report->total_amount) ;
            // $data = $report->showData(array('schoolid'=>$this->branchId, 'report_month'=>$month));
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
            $this->showMessage();
        }
    }

    // 考勤报表补签到
    public function actionRepatch()
    {
        if (!Yii::app()->request->isPostRequest) {
            $this->addMessage('state', 'fail');
            $this->showMessage();
        }
        $month = Yii::app()->request->getParam('month', '');
        $attendanceId = Yii::app()->request->getParam('attendanceId', '');

        if (!$month || !$attendanceId) {
            $this->addMessage('state', 'fail');
            $this->showMessage();
        }
        if ($month > date('Ym', strtotime('today'))) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '补签月份错误');
            $this->showMessage();
        }
        $repatch = AsaMonthReportRepatch::model()->findByAttributes(array('attendance_id'=>$attendanceId));
        if ($repatch) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '该签到记录已补签');
            $this->showMessage();
        }

        $report = AsaMonthReport::model()->findByAttributes(array('schoolid'=>$this->branchId, 'report_month'=>$month));
        if (!$report) {
            $report = new AsaMonthReport();
            $report->schoolid = $this->branchId;
            $report->report_month = $month;
            $report->total_amount = 0;
            $report->status = AsaMonthReport::PREPAR;
            $report->uid = $this->staff->uid;
            $report->save();
        }
        if ($report->status == AsaMonthReport::SUBMIT) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '报表已提交，不可再更改');
            $this->showMessage();
        }

        foreach ($attendanceId as $id) {
            $model = new AsaMonthReportRepatch();
            $model->report_id = $this->branchId . $month;
            $model->attendance_id = $id;
            $model->update_time = time();
            $model->uid = $this->staff->uid;
            $model->save();
        }
        // 更新报表
        $report->generalData();

        $this->addMessage('state', 'success');
        $this->showMessage();
    }

    // 考勤报表价格调整
    public function actionVariation()
    {
        if (!Yii::app()->request->isPostRequest) {
            $this->addMessage('state', 'fail');
            $this->showMessage();
        }
        $reportId = Yii::app()->request->getParam('reportId', '');
        $vendorId = Yii::app()->request->getParam('vendorId', '');
        $amount = Yii::app()->request->getParam('amount', '');
        $memo = Yii::app()->request->getParam('memo', '');
        $settle = Yii::app()->request->getParam('settle', '1');

        $report = AsaMonthReport::model()->findByPk($reportId);
        if (!$reportId || !$vendorId || !is_numeric($amount)) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '参数错误');
            $this->showMessage();
        }
        if (!$report) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '报表不存在');
            $this->showMessage();
        }
        if ($report->status == AsaMonthReport::SUBMIT) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '报表已提交，不可再更改');
            $this->showMessage();
        }
        $criteria = new CDbCriteria();
        $criteria->compare('report_id', $reportId);
        $criteria->compare('vendor_id', $vendorId);
        $model = AsaMonthReportVariation::model()->find($criteria);
        $oAmount = 0;
        if (!$model) {
            $model = new AsaMonthReportVariation();
            // 第一次设置未结算状态
            if ($settle == 0) {
                $oAmount = AsaMonthReport::getVendorAmount($reportId, $vendorId); 
                // 更新报表总金额
                $report->total_amount = $report->total_amount - $oAmount;
                $report->unsettle_amount += ($oAmount + $amount);
            }
        } else {
            // 不结算=>结算
            if ($settle == 1 && $model->settlement == 0) {
                $oAmount = AsaMonthReport::getVendorAmount($reportId, $vendorId); 
                // 更新报表总金额
                $report->total_amount += ($oAmount + $amount);
                $report->unsettle_amount -= ($oAmount + $model->variation_amout);
            // 结算=>不结算
            } elseif($settle == 0 && $model->settlement == 1) {
                $oAmount = AsaMonthReport::getVendorAmount($reportId, $vendorId); 
                // 更新报表总金额
                $report->total_amount -= ($oAmount + $amount);
                $report->unsettle_amount += ($oAmount + $amount);
            } elseif($settle == 0 && $model->settlement == 0) {
                $oAmount = $model->variation_amout;              
                $report->unsettle_amount = $report->unsettle_amount - $oAmount + $amount;
            } else {
                $oAmount = $model->variation_amout;
                // 更新报表总金额
                $report->total_amount = $report->total_amount - $oAmount + $amount;
            }
        }
        $model->report_id = $reportId;
        $model->vendor_id = $vendorId;
        $model->variation_amout = $amount;
        $model->memo = $memo;
        $model->uid = $this->staff->uid;
        $model->update_time = time();
        $model->settlement = $settle;
        if ($model->save()) {
            $report->save();
            $this->addMessage('state', 'success');
            $this->addMessage('data', array('total_amount'=>$report->total_amount));
            $this->showMessage();
        } else {
            $error = current($model->getErrors());
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $error[0]);
            $this->showMessage();
        }
    }

    // 打印考勤报表
    
    public function actionPrintReport()
    {
        $this->layout = "//layouts/print";
        $this->printFW = $this->branchObj->getPrintHeader();

        $month = Yii::app()->request->getParam('month', '');
        $schoolid = Yii::app()->request->getParam('branchId', '');

        // 获取固定费用数据
        $this->report = AsaMonthReport::model()->findByAttributes(array('report_month'=>$month, 'schoolid' => $schoolid));
        if($this->report) {
            $by_month = $this->report->showData();
            // 获取分成统计数据
            $by_count = $this->report->showDividedData();
            // 其它月结算到当前月数据
            $others = $this->report->otherMonthData();
            $data = array(
                'total' => 0,
                'schoolStatus' => ($this->branchObj->type == 50) ? 1 : 0,
                'month' => $this->report->report_month,
                'update_time' => date('Y-m-d', $this->report->update_time),
                'vendors' => array()
            );
            foreach ($by_month['vendors'] as $vendor_id => $item) {
                $data['total'] += $item['amount'];
                if (!isset($data['vendors'][$vendor_id])) {
                    $data['vendors'][$vendor_id] = $item;
                    unset($data['vendors'][$vendor_id]['courses']);
                } else {
                    $data['vendors'][$vendor_id]['amount'] += $item['amount'];
                    $data['vendors'][$vendor_id]['variation_amout'] += $item['variation_amout'];
                    $data['vendors'][$vendor_id]['variation_memo'] += $item['variation_memo'];
                }

                foreach ($item['courses'] as $month => $courses) {
                    foreach ($courses as $course) {
                        $data['vendors'][$vendor_id]['amount'] -= $course['assistant_pay'];
                        $data['total'] -= $course['assistant_pay'];
                        $data['vendors'][$vendor_id]['courses'][$month][] = $course;
                    }
                }
            }

            foreach ($by_count['vendors'] as $vendor_id => $item) {
                $data['total'] += $item['amount'];
                if (!isset($data['vendors'][$vendor_id])) {
                    $data['vendors'][$vendor_id] = $item;
                    unset($data['vendors'][$vendor_id]['courses']);
                } else {
                    $data['vendors'][$vendor_id]['amount'] += $item['amount'];
                    $data['vendors'][$vendor_id]['variation_amout'] += $item['variation_amout'];
                    $data['vendors'][$vendor_id]['variation_memo'] += $item['variation_memo'];
                }
                foreach ($item['courses'] as $month => $courses) {
                    foreach ($courses as $course) {
                        $data['vendors'][$vendor_id]['amount'] -= $course['assistant_pay'];
                        $data['total'] -= $course['assistant_pay'];
                        $data['vendors'][$vendor_id]['courses'][$month][] = $course;
                    }
                }
            }
            foreach ($others as $vendor_id => $item) {
                $data['total'] += $item['amount'];
                if (!isset($data['vendors'][$vendor_id])) {
                    $data['vendors'][$vendor_id] = $item;
                    unset($data['vendors'][$vendor_id]['courses']);
                } else {
                    $data['vendors'][$vendor_id]['amount'] += $item['amount'];
                    $data['vendors'][$vendor_id]['variation_amout'] += $item['variation_amout'];
                    $data['vendors'][$vendor_id]['variation_memo'] += $item['variation_memo'];
                }
                foreach ($item['courses'] as $month => $courses) {
                    foreach ($courses as $course) {
                        $data['vendors'][$vendor_id]['amount'] -= $course['assistant_pay'];
                        $data['total'] -= $course['assistant_pay'];
                        $data['vendors'][$vendor_id]['courses'][$month][] = $course;
                    }
                }
            }
            sort($data['vendors']);
            foreach ($data['vendors'] as $i => $items) {
                foreach ($items['variation_amout'] as $variation_amout) {
                    $data['total'] += $variation_amout;
                    $data['vendors'][$i]['amount'] += $variation_amout;
                }
            }
            if ($this->report->status == AsaMonthReport::PREPAR) {
                $this->report->total_amount = $data['total'];
                $this->report->save();
            }
        }
        //Yii::msg($data);
        $data['school'] = $this->branchObj->title;
        $this->render('printreport', array('data'=>$data));
    }

    // 分成统计
    public function actionStatistics()
    {
        $this->branchSelectParams['urlArray'] = array('//masa/attend/statistics');
        $timeData = Yii::app()->request->getParam('timeData', "");

        $strDate = "201709";
        $month = array();
        while ($strDate <= date("Ym",time())){
            $month[$strDate] =  $strDate;
            $strDate = date('Ym',strtotime($strDate . "01 +1 month"));
        }

        $time = date("Ym",time());
        $afterDate = date('Ym',strtotime($time . "01 +6 month"));
        $lastDate = date('Ym',strtotime($time . "01 -6 month"));

        $settleMonth = array();
        while ($lastDate <= $afterDate){
            $settleMonths[$lastDate] =  $lastDate;
            $lastDate = date('Ym',strtotime($lastDate . "01 +1 month"));
        }
        $settleMonth = array();
        foreach($settleMonths as $k=>$item){
            $settleMonth[$k] = date("Y-m", strtotime($item . "01"));
        }

        if($timeData){
            $timeStarDate = $timeData . '01' ;
            $timeEndDate = date('Ymd',strtotime($timeData . "01 +1 month"));
            $coursedataArr = $this->setCourse($timeData);
            $thirdpartdataArr = $this->setThirdpart($timeData);
            $courseTimeNumber = $this->setCourseTimeNumber(array_keys($coursedataArr),$timeData);
            $teacherAttendance = $this->setTachertAttendance(array_keys($coursedataArr), $timeStarDate, $timeEndDate,1);

            // 实时获取签到数据 根据老师签到算出实时总人数
            if($teacherAttendance){
                foreach($coursedataArr as $key=>$item){
                    foreach($teacherAttendance[$key] as $keys=>$items){
                        foreach($items['status'] as $k=>$v){
                            if($courseTimeNumber[$key][$k]['peopleNum'] && in_array($v,array(1,3)) ){
                                $teacherAttendance[$key][$keys]['moneyNum'] += $courseTimeNumber[$key][$k]['peopleNum'];
                            }
                        }
                    }
                }

                foreach($thirdpartdataArr as $key => $item){
                    foreach($item as $k=>$val){
                        $thirdpartdataArr[$key][$k]['num'] = $teacherAttendance[$key][$val['teacherId']];
                    }
                }
            }

            $courseData = array();

            foreach($coursedataArr as $k=>$item){
                $courseData[$k] = array(
                    'id' => $item['courseId'],
                    'schedule' => $item['schedule'],
                    'courseTitle' => $item['courseTitle'],
                    'number' => $item['number'],
                    'assistant_fee' => $item['assistant_fee'],
                );
                $courseData[$k]['status'] = (isset($courseTimeNumber[$k])) ? (count($item['schedule']) == count($courseTimeNumber[$k]) ? 1 : 2 ) : 2;
            }

            $dataArr = array(
                'course' =>$courseData,
                'teacher' => $thirdpartdataArr ,
                'courseNumber' =>$courseTimeNumber,
                'teacherAttendance' =>$teacherAttendance,
            );

            echo CJSON::encode($dataArr);

        }else{
            usort($month, function($a, $b) {
                return $b-$a;
            });
            $this->render('statistics' ,array(
                'month' => $month,
                'moneySum' => $this->moneySum(),
                'settleMonths' => $settleMonth,
            ));
        }
    }

    //  给日期增加人数
    //  当月日期全部填满， 则直接增加 老师的数据
    public function actionUpdateThirdpart()
    {
        if(Yii::app()->request->isPostRequest)
        {
            $timeNumberArr = Yii::app()->request->getParam('timeNumberArr', array());
            /*$timeNumberArr = array(20171009 => 1,20171023 => 3,20171030 => 4,);*/
            $courseId = Yii::app()->request->getParam('courseId', "");
            $timeArr = array_keys($timeNumberArr);

            $tmp_year = substr($timeArr[0],0,4);
            $tmp_mon = substr($timeArr[0],4,2);
            $tmp_month = substr($timeArr[0],0,6) . "01";
            $tmp_nextmonth = date("Ymd", mktime(0,0,0,$tmp_mon+1,1,$tmp_year));

            $courseModel = AsaCourse::model()->findByPk($courseId);

            if(!array_filter($timeNumberArr)){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', "增加人数不能为空");
                $this->showMessage();
            }

            $crit = new CDbCriteria();
            $crit->compare('service_start', array_keys($timeNumberArr));
            $crit->compare('course_id', $courseId);
            $crit->compare('status', ">0");
            $statusAttendanceCount = AsaStaffAttendance::model()->count($crit);
            if(!$statusAttendanceCount){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', "当前课程未签到或者已经取消签到");
                $this->showMessage();
            }

            if($courseModel){
                $schoolArr = array($courseModel->coursesgroup->schoolid => $courseModel->coursesgroup->schoolid);
                $crit = new CDbCriteria();
                $crit->compare('group_id', $courseModel->gid);
                $shareModel = AsaCourseGroupShare::model()->findAll($crit);
                if($shareModel){
                    foreach ($shareModel as $item){
                        $schoolArr[$item->site_id] = $item->site_id;
                    }
                }
                if(in_array($this->branchId, $schoolArr)) {
                    $course_month = $courseModel->id . '_' . $tmp_year . $tmp_mon;

                    $crit = new CDbCriteria();
                    $crit->compare('course_month', $course_month);
                    //$crit->compare('status', array(AsaThirdpartReport::STATUS_ACTIVE, AsaThirdpartReport::STATUS_LSSUE));
                    $thirdpartReportCount = AsaThirdpartReport::model()->findAll($crit);
                    $month = 0;
                    $vendor_arr_id = array();
                    foreach ($thirdpartReportCount as $val){
                        $month = $val->report_month;
                        $vendor_arr_id[] = $val->course_staff_id;
                    }

                    $crit = new CDbCriteria();
                    $crit->compare('schoolid', $this->branchId);
                    $crit->compare('report_month', $tmp_year . $tmp_mon);
                    $modelReport = AsaMonthReport::model()->find($crit);

                    $statusReport = 1;
                    if(isset($modelReport) && $modelReport->status != 10){
                        $statusReport = 0;
                    }

                    if ($statusReport) {
                        $crit = new CDbCriteria();
                        $crit->compare('schedule_id', $courseModel->schedule_id);
                        $crit->compare('schedule_date', $timeArr);
                        $scheduleModel = AsaScheduleItem::model()->findAll($crit);

                        $crit = new CDbCriteria();
                        $crit->compare('schedule_id', $courseModel->schedule_id);
                        $crit->compare('schedule_date', $timeArr);
                        $scheduleItemRevise = AsaScheduleItemRevise::model()->findAll($crit);

                        $schedeleArr = array();
                        if ($scheduleModel) {
                            foreach ($scheduleModel as $val) {
                                //if(!isset($val->scheduleItemRevise)){
                                $schedeleArr[] = array(
                                    'id' => $val->id,
                                    'schedule_date' => $val->schedule_date,
                                );
                                //}
                            }
                        }
                        if ($scheduleItemRevise) {
                            foreach ($scheduleItemRevise as $val) {
                                if ($val->cid == $courseId) {
                                    $schedeleArr[] = array(
                                        'id' => $val->schedule_item_id,
                                        'schedule_date' => $val->schedule_date,
                                    );
                                }
                            }
                        }

                        if ($schedeleArr) {
                            $course_month = "";
                            $tmp_mon = "";
                            $courseNum = 0;
                            foreach ($schedeleArr as $item) {
                                $crit = new CDbCriteria();
                                $crit->compare('course_id', $courseModel->id);
                                $crit->compare('service_start', $item['schedule_date']);
                                $crit->compare('status', ">0");
                                $attModel = AsaStaffAttendance::model()->findAll($crit);
                                if (in_array($item['schedule_date'], $timeArr) && $attModel) {
                                    $tmp_mon = substr($item['schedule_date'], 0, 6);
                                    $course_month = $courseModel->id . "_" . $tmp_mon;
                                    $this->updataThirdpartReportItem($courseModel->id, $course_month, $item['id'], $item['schedule_date'], $timeNumberArr[$item['schedule_date']]);
                                    $courseNum += $timeNumberArr[$item['schedule_date']];
                                } else {
                                    $criteria = new CDbCriteria();
                                    if(!$timeArr){
                                        $criteria->addNotInCondition('schedule_date', $timeArr);
                                    }
                                    $criteria->compare('course_id', $courseModel->id);
                                    $criteria->compare('schedule_item_id', $item['id']);
                                    $reportItemModel = AsaThirdpartReportItem::model()->find($criteria);
                                    if ($reportItemModel) {
                                        $reportItemModel->delete();
                                    }
                                }
                            }

                            $sttendanceArr = $this->setTachertAttendance($courseModel->id, $tmp_month, $tmp_nextmonth);

                            $modelArr = array();
                            $moneyNum = array();
                            $teachers = array();
                            if ($sttendanceArr[$courseModel->id]) {
                                foreach ($sttendanceArr[$courseModel->id] as $key => $item) {
                                    foreach ($item['status'] as $k => $v) {
                                        if ($timeNumberArr[$k] && in_array($v, array(1, 3))) {
                                            $moneyNum[$key] += $timeNumberArr[$k];
                                        }
                                    }
                                }

                                foreach ($sttendanceArr[$courseModel->id] as $key => $item) {
                                    $totalAmount = 0;
                                    if (isset($moneyNum) && isset($moneyNum[$key])) {
                                        $totalAmount = $item['unit_salary'] * $moneyNum[$key];
                                    }

                                    $assistant_fee = array();
                                    if($courseModel->assistant_fee == 10 && $item['vendor_staff'] == 1){
                                        $assistant_fee = $this->setAssistantTachertAttendance($courseModel->id, $tmp_month, $tmp_nextmonth);

                                    }

                                    $data = array(
                                        'id' => $item['id'],
                                        'school' => $this->branchId,
                                        'course_month' => $course_month,
                                        'report_month' => $tmp_mon,
                                        'program_id' => $courseModel->gid,
                                        'course_id' => $courseModel->id,
                                        'staffId' => $key,
                                        'unit_price' => $item['unit_salary'],
                                        'total_amount' => $totalAmount,
                                        'assistant_pay' => ($assistant_fee && $assistant_fee[$courseModel->id]) ? $assistant_fee[$courseModel->id] : 0 ,
                                        'repatch_amount' => "",
                                        'repatch_reason' => "",
                                        'settle_month' => "",
                                        'status' => 0,
                                    );

                                    $modelArr[] = $this->updataThirdpartReport($data);
                                }

                                if ($modelArr) {
                                    foreach ($modelArr as $item) {
                                        $teachers[$item->course_id][$item->id] = array(
                                            'id' => $item->id,
                                            'teacher' => $item->vendor->getName(),
                                            'unit_price' => $item->unit_price,
                                            'total_amount' => $item->total_amount-$item->assistant_pay,
                                            'total_amount_x' => $item->total_amount,
                                            'repatch_amount' => $item->repatch_amount,
                                            'repatch_reason' => $item->repatch_reason,
                                            'assistant_pay' => $item->assistant_pay,
                                            'settle_month' => ($item->settle_month > 1) ? date("Ym", $item->settle_month) : "-1",
                                            'status' => $item->status,
                                            'num' => $sttendanceArr[$item->course_id][$item->course_staff_id],
                                            'teacherId' => $item->course_staff_id,
                                        );
                                    }


                                    $crit = new CDbCriteria();
                                    $crit->addNotInCondition('course_staff_id', array_keys($sttendanceArr[$courseModel->id]));//与上面正好相法，是NOT IN
                                    $crit->compare('report_month', $tmp_mon);
                                    $crit->compare('course_id', $courseModel->id);
                                    $crit->compare('status', "<1");
                                    $thirdpartReportModela = AsaThirdpartReport::model()->findAll($crit);
                                    if ($thirdpartReportModela) {
                                        foreach ($thirdpartReportModela as $val) {
                                            $val->delete();
                                        }
                                    }
                                }
                            }

                            $money = $this->moneySum(substr($timeArr[0], 0, 6));
                            $status = (count($timeArr) == count($schedeleArr)) ? 1 : 2;
                            $data = array("teacher" => $teachers, 'moneySum' => $money, 'status' => $status);

                            $this->addMessage('state', 'success');
                            $this->addMessage('data', $data);
                            $this->showMessage();
                        } else {
                            $this->addMessage('state', 'fail');
                            $this->addMessage('message', "课程里没有当前时间");
                            $this->showMessage();
                        }
                    } else {
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', "已经有提交，不可修改人数");
                        $this->showMessage();
                    }
                }else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', "校园不对");
                    $this->showMessage();
                }
            }else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', "课程参数不对");
                $this->showMessage();
            }
        }
    }

    // 给每个老师调整钱数和日期
    public function actionRevisionThirdpart()
    {
        if(Yii::app()->request->isPostRequest)
        {
            $thirdpartReportId = Yii::app()->request->getParam('thirdpartReportId', "");
            $repatch_amount = Yii::app()->request->getParam('repatch_amount', "");
            $repatch_reason = Yii::app()->request->getParam('repatch_reason', "");
            $settle_month = Yii::app()->request->getParam('settle_month', "");
            $newTotalAmount = Yii::app()->request->getParam('newTotalAmount', "");
            $status = Yii::app()->request->getParam('status', false);
            $model = AsaThirdpartReport::model()->findByPk($thirdpartReportId);

            $crit = new CDbCriteria();
            $crit->compare('course_month', $model->course_month);
            $thirdpartReportItemModel = AsaThirdpartReportItem::model()->findAll($crit);
            $timeNumberArr = array();
            foreach($thirdpartReportItemModel as $item){
                $timeNumberArr[$item->schedule_date] = $item->number;
            }

            $moneyNum = array();
            $tmp_month = $model->report_month . "01";
            $tmp_nextmonth = date('Ym',strtotime($model->report_month . "01 +1 month"));
            $sttendanceArr = $this->setTachertAttendance($model->course_id, $tmp_month, $tmp_nextmonth);

            if($sttendanceArr[$model->course_id]) {
                foreach ($sttendanceArr[$model->course_id] as $key => $item) {
                    foreach ($item['status'] as $k => $v) {
                        if ($timeNumberArr[$k] && in_array($v,array(1,3))) {
                            $moneyNum[$key] += $timeNumberArr[$k];
                        }
                    }
                }
            }

            $mews = 0;
            if(isset($moneyNum) && $moneyNum[$model->course_staff_id]){
                $mews = $model->unit_price * $moneyNum[$model->course_staff_id];
            }

            if($newTotalAmount == $mews){
                if($model && $model->status != AsaThirdpartReport::STATUS_ACTIVE) {
                    $model->repatch_amount = $repatch_amount;
                    $model->repatch_reason = $repatch_reason;
                    if($model->total_amount != $newTotalAmount){
                        $model->total_amount = $newTotalAmount;
                    }
                    /*if($settle_month){
                        $settle_month = strtotime($settle_month . "01");
                    }
                    $model->settle_month = $settle_month;*/
                    $model->status = ($status) ? 1 : 0;
                    //$model->status = 0;
                    if (!$model->save()) {
                        $error = current($model->getErrors());
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', $error[0]);
                        $this->showMessage();
                    }


                    $startTime = $model->report_month . "01";
                    $endTime = date('Ym',strtotime($model->report_month . "01 +1 month"));

                    if($model->status == 1){
                        $crit = new CDbCriteria();
                        $crit->compare('course_id', $model->course_id);
                        $crit->compare('service_start', ">={$startTime}");
                        $crit->compare('service_end', "<={$endTime}");
                        $crit->compare('status', "<>0");
                        $staffAttendanceModel = AsaStaffAttendance::model()->findAll($crit);
                        if($staffAttendanceModel){
                            foreach($staffAttendanceModel as $items){
                                if(($items->status == 1 && $items->vendor_id == $model->course_staff_id) || ($items->status == 3 && $items->substituter == $model->course_staff_id) ){
                                   $items->report_id = $model->id;
                                   $items->save();
                                }
                            }
                        }
                    }

                    $moeny = $this->moneySum($model->report_month);
                    $data['money'] = array($model->report_month => $moeny[$model->report_month]);
                    $data['status'] = array(
                        'status' =>$model->status,
                        'id' =>$model->id,
                    );
                    $this->addMessage('state', 'success');
                    $this->addMessage('data', $data);
                    $this->addMessage('message', Yii::t('message', '提交成功!'));
                    $this->showMessage();
                }else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('message', '已经提交, 不可修改'));
                    $this->showMessage();
                }
            }else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', '老师签到记录发生变化,请重新点击确定人数'));
                $this->showMessage();
            }
        }
    }

    // 拿到助教老师的薪资
    public function setAssistantTachertAttendance($courseId = '', $startDate = '', $endDate = '', $status = 0)
    {
        $assistantTachertAttendance = array();

        if($courseId && $startDate && $endDate){
            $criteria = new CDbCriteria();
            $criteria->compare('t.course_id', $courseId);
            $criteria->compare('course.job_type', 2);
            $criteria->compare('t.service_start', ">={$startDate}");
            $criteria->compare('t.service_start', "<={$endDate}");
            //if($status){$criteria->compare('t.status', ">=1");}
            $criteria->compare('t.status', "<>0");
            $criteria->with = "course";
            $staffModel = AsaStaffAttendance::model()->findAll($criteria);

            if($staffModel){
                foreach ($staffModel as $item) {
                    $assistantTachertAttendance[$item->course_id] += $item->course->unit_salary;
                }
            }
        }

        return $assistantTachertAttendance;
    }

    public function setTachertAttendance($courseId = array(), $startDate = '', $endDate = '', $status = 0)
    {
        $teacherAttendance = array();

        if($courseId && $startDate && $endDate){
            $criteria = new CDbCriteria();
            $criteria->compare('t.course_id', $courseId);
            $criteria->compare('course.unit_type', 99);
            $criteria->compare('t.service_start', ">={$startDate}");
            $criteria->compare('t.service_start', "<={$endDate}");
            //if($status){$criteria->compare('t.status', ">=1");}
            $criteria->compare('t.status', "<>0");
            $criteria->with = "course";
            $staffModel = AsaStaffAttendance::model()->findAll($criteria);

            if($staffModel){
                foreach ($staffModel as $item) {
                    $teacherId = ($item->substituter) ? $item->substituter : $item->vendor_id ;
                    $teacherName = ($item->substituter) ? $item->vendor->getName() : $item->vendorTeacher->getName() ;
                    $teacherAttendance[$item->course_id][$teacherId]['status'][$item->service_start] = $item->status;
                    $teacherAttendance[$item->course_id][$teacherId]['vendor_staff'] = $item->status;
                    $teacherAttendance[$item->course_id][$teacherId]['unit_salary'] = $item->course->unit_salary;
                    $teacherAttendance[$item->course_id][$teacherId]['moneyNum'] = "";
                    $teacherAttendance[$item->course_id][$teacherId]['teacherName'] = $teacherName;
                    $teacherAttendance[$item->course_id][$teacherId]['id'] = $item->id;
                }
            }
        }
        //Yii::msg($teacherAttendance);
        return $teacherAttendance;
    }

    // 获取已经老师的数据
    public function setThirdpart($time = 0)
    {
        $thirdpartReport = array();
        if($time)
        {
            $criteria = new CDbCriteria();
            $criteria->compare('report_month', $time);
            $criteria->compare('school_id', $this->branchId);
            $thirdpartReportModel = AsaThirdpartReport::model()->findAll($criteria);

            if($thirdpartReportModel){
                foreach($thirdpartReportModel as $thirdpartItem){
                    $thirdpartReport[$thirdpartItem->course_id][$thirdpartItem->id] = array(
                        'id' => $thirdpartItem->id,
                        'teacher' => $thirdpartItem->vendor->getName(),
                        'unit_price' => $thirdpartItem->unit_price,
                        'total_amount' => $thirdpartItem->total_amount - $thirdpartItem->assistant_pay,
                        'repatch_amount' => $thirdpartItem->repatch_amount,
                        'total_amount_x' => $thirdpartItem->total_amount,
                        'repatch_reason' => $thirdpartItem->repatch_reason,
                        'settle_month' => ($thirdpartItem->settle_month == "-1") ? -1 : date("Ym", $thirdpartItem->settle_month),
                        'status' => $thirdpartItem->status,
                        'teacherId' => $thirdpartItem->course_staff_id,
                        'assistant_pay' => ($thirdpartItem->assistant_pay) ? $thirdpartItem->assistant_pay : 0,
                        'num' => array(),
                    );
                }
            }
        }
        return $thirdpartReport;
    }


    // 增加老师数据
    public function updataThirdpartReport($data)
    {
        $crit = new CDbCriteria();
        $crit->compare('t.school_id', $data['school']);
        $crit->compare('t.course_id', $data['course_id']);
        $crit->compare('t.course_staff_id', $data['staffId']);
        $crit->compare('t.course_month', $data['course_month']);
        $model = AsaThirdpartReport::model()->find($crit);

        if(!$model){
            $model = new AsaThirdpartReport();
            $model->repatch_amount = $data['repatch_amount'];
            $model->repatch_reason = $data['repatch_reason'];
            $model->settle_month = -1;
        }
        $model->total_amount = $data['total_amount'];
        $model->assistant_pay = $data['assistant_pay'];
        $model->school_id = $data['school'];
        $model->course_month = $data['course_month'];
        $model->report_month = $data['report_month'];
        $model->program_id = $data['program_id'];
        $model->course_id = $data['course_id'];
        $model->course_staff_id = $data['staffId'];
        $model->unit_price = $data['unit_price'];
        $model->status = ($data['status']) ? $data['status'] : 0 ;
        $model->update_time = time();
        $model->uid = Yii::app()->user->id;

        if(!$model->save()){
            $err = current($model->getErrors());
            return $err[0];
        }

        $attendModel = AsaStaffAttendance::model()->findByPk($data['id']);
        if($attendModel){
            $attendModel->report_id = $model->id;
            $attendModel->save();
        }

        return $model;
    }

    /*
     * $course_id   课程ID
     * $course_month 课程和时间组合唯一ID
     * $schedule_item_id 课程item关联ID
     * $schedule_date   课程时间
     * $number          人数
     */
    public function updataThirdpartReportItem($course_id,$course_month,$schedule_item_id,$schedule_date,$number)
    {
        $crit = new CDbCriteria();
        $crit->compare('t.course_id', $course_id);
        $crit->compare('t.schedule_date', $schedule_date);
        $model = AsaThirdpartReportItem::model()->find($crit);
        if(!$model){
            $model = new AsaThirdpartReportItem();
        }
        $model->course_id = $course_id;
        $model->course_month = $course_month;
        $model->schedule_item_id = $schedule_item_id;
        $model->schedule_date = $schedule_date;
        $model->number = $number;
        if(!$model->save()){
            $err = current($model->getErrors());
            return $err[0];
        }
        return $model;
    }

    public function setCourseTimeNumber($course = array(), $timeData = 0)
    {
        $reportItem = array();
        if($course && $timeData)
        {
            $tmp_year = substr($timeData,0,6);
            $course_month = array();
            foreach($course as $item){
                $course_month[] =  $item . "_" . $tmp_year;
            }

            $crit = new CDbCriteria();
            $crit->compare('course_month', $course_month);
            $thirdpartReportItemModel = AsaThirdpartReportItem::model()->findAll($crit);

            if($thirdpartReportItemModel){
                foreach($thirdpartReportItemModel as $item){
                    $reportItem[$item->course_id][$item->schedule_date] = array(
                        'peopleNum' => $item->number,
                    );
                }
            }
        }
        return $reportItem;
    }

    // $timeData   时间
    // 分出统计查询符合条件的课程
    public function setCourse($timeData = 0)
    {
        $course = array();
        if($timeData)
        {
            // 根据时间 拿到以时间表ID 为数组的数据
            $tmp_year = substr($timeData,0,4);
            $tmp_mon = substr($timeData,4,2);
            $tmp_nextmonth = mktime(0,0,0,$tmp_mon+1,1,$tmp_year);
            $fm_next_month = date("Ym",$tmp_nextmonth);
            $scheduleStr = $timeData . "01";
            $scheduleEnd = $fm_next_month . "01";

            $scheduleItemData = array();
            $scheduleArr = array();
            $crit = new CDbCriteria();
            $crit->compare('t.schedule_date', ">={$scheduleStr}");
            $crit->compare('t.schedule_date', "<{$scheduleEnd}");
            $crit->compare('scheduleT.schoolid', $this->branchId);
            $crit->with = "scheduleT";
            $crit->index = "id";
            $scheduleItemModel = AsaScheduleItem::model()->findAll($crit);
            if($scheduleItemModel){
                foreach($scheduleItemModel as $item){
                    $scheduleItemData[$item->schedule_id][$item->schedule_date] = array(
                        'time' => $item->schedule_date,
                        'id' => $item->id,
                    );
                }
            }

            $criteria = new CDbCriteria();
            //$criteria->compare('schedule_item_id', array_keys($scheduleItemModel));
            $criteria->compare('schedule_date', ">={$scheduleStr}");
            $criteria->compare('schedule_date', "<{$scheduleEnd}");
            $scheduleItemReviseObj = AsaScheduleItemRevise::model()->findAll($criteria);

            $scheduleItemRevise = array();
            if($scheduleItemReviseObj){
                foreach($scheduleItemReviseObj as $item){
                    $scheduleArr[] = $item->schedule_id;
                    $scheduleItemRevise[$item->cid][$item->schedule_id][$item->schedule_date] = array(
                        'time' =>  $item->schedule_date,
                        'id' =>  $item->schedule_item_id,
                    );
                }
            }

            $criteria = new CDbCriteria();
            $criteria->compare('unit_type', 99);
            $criteria->compare('site_id', $this->branchId);
            $criteria->compare('status', 1);
            $criteria->index = "course_id";
            $staffModel = AsaCourseStaff::model()->findAll($criteria);

            $courseModel = array();
            if($scheduleItemData && $staffModel){
                //所有课程
                $scheduleArrid = ($scheduleArr) ? array_merge($scheduleArr, array_keys($scheduleItemData)) : array_keys($scheduleItemData);

                $crit = new CDbCriteria();
                $crit->compare('id', array_keys($staffModel));
                $crit->compare('schoolid', $this->branchId);
                $crit->compare('schedule_id', $scheduleArrid);
                // $crit->compare('courseNumber.number_taken', ">0");
                $crit->with = "courseNumber";
                $crit->index = "id";
                $courseModel = AsaCourse::model()->findAll($crit);
            }

            if($courseModel){
                foreach($courseModel as $item){
                    $schedule = array();
                    foreach($item->schedule->items as $scheduleVal){
                        if($scheduleVal->schedule_date >= $scheduleStr && $scheduleVal->schedule_date < $scheduleEnd){
                            $schedule[$scheduleVal->schedule_id] = $scheduleItemData[$scheduleVal->schedule_id];
                        }
                    }

                    if(isset($scheduleItemRevise) && isset($scheduleItemRevise[$item->id]) && count($schedule) > 0){
                        $key = array_keys($schedule);
                        $array  = $schedule[$key[0]] + $scheduleItemRevise[$item->id][$key[0]];
                        ksort($array);
                        $schedule[$key[0]] = $array;
                    }

                    if($schedule){
                        $course[$item->id] = array(
                            'courseId' => $item->id,
                            'schedule' => $schedule[$item->schedule_id],
                            'courseTitle' => $item->getTitle() . " - "  .$item->coursesgroup->getName(),
                            'assistant_fee' => ($item->assistant_fee)  ? $item->assistant_fee : 20 ,
                            'number' => $item->courseNumber->number_taken,
                        );
                    }
                }
            }
        }
        return $course;
    }

    public function moneySum($month = 0)
    {
        $criteria = new CDbCriteria();
        $criteria->select = "total_amount, report_month, repatch_amount,assistant_pay";
        $criteria->compare('school_id', $this->branchId);
        if($month){$criteria->compare('report_month', $month);}
        $thirdpartReportModel = AsaThirdpartReport::model()->findAll($criteria);

        $moneySum = array();
        if($thirdpartReportModel)
        {
            foreach($thirdpartReportModel as $val)
            {
                $moneySum[$val->report_month] += ($val->total_amount + $val->repatch_amount - $val->assistant_pay);
            }
        }

        return $moneySum;
    }

    public function getButton($data)
    {
        echo CHtml::link('编辑', array('updataStaffCourse', 'teacherid' => $data->id,"groupId"=>$data->program_id, "branchId"=>Yii::app()->controller->branchId), array('class' => 'J_modal btn btn-xs btn-info')) . ' ';
        echo CHtml::link('删除', array('deleteStaff', 'id' => $data->id, "branchId"=>Yii::app()->controller->branchId), array('class' => 'J_ajax_del btn btn-xs btn-info'));
    }
}