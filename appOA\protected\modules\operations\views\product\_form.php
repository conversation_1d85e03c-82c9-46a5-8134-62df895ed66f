<div class="h_a">添加商品</div>

<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'points-product-form',
	'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm'),
)); ?>
<div class="table_full">
    <table width="100%" class="" style="margin-bottom:0;">
        <col width="150" />
        <col width="400" />
        <col />
        <tbody>
            <tr>
                <th><?php echo $form->labelEx($model,'en_title'); ?></th>
                <td>
                    <?php echo $form->textField($model,'en_title',array('maxlength'=>255,'class'=>'input length_6')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'en_title'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'cn_title'); ?></th>
                <td>
                    <?php echo $form->textField($model,'cn_title',array('maxlength'=>255,'class'=>'input length_6')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'cn_title'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'en_memo'); ?></th>
                <td>
                    <?php echo $form->textArea($model,'en_memo',array('class'=>'length_6')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'en_memo'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'cn_memo'); ?></th>
                <td>
                    <?php echo $form->textArea($model,'cn_memo',array('class'=>'length_6')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'cn_memo'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'credits'); ?></th>
                <td>
                    <?php echo $form->textField($model,'credits',array('maxlength'=>255,'class'=>'input length_2')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'credits'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'p_price'); ?></th>
                <td>
                    <?php echo $form->textField($model,'p_price',array('maxlength'=>255,'class'=>'input length_2')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'p_price'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'m_price'); ?></th>
                <td>
                    <?php echo $form->textField($model,'m_price',array('maxlength'=>255,'class'=>'input length_2')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'m_price'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'status'); ?></th>
                <td>
                    <?php echo $form->dropDownList($model,'status', array(Yii::t('points', '隐藏'),Yii::t('points', '显示')), array('class'=>'select_2', 'empty'=>Yii::t("global", 'Please Select'))); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'status'); ?></div></td>
            </tr>
        </tbody>
    </table>
</div>
<div class="btn_wrap">
    <div class="btn_wrap_pd">
        <button class="btn btn_submit mr10 J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
    </div>
</div>
<?php $this->endWidget(); ?>