<?php
class CommonContentFetcher {
    static function getStaffByBranch($branchId, $withPubInfo=false, $titleBilingual=false, $withPass=false, $staffmap=array()){
        Yii::import('common.models.hr.*');
        Yii::import('common.models.staff.WechatTeacherBind');

        $criter = new CDbCriteria;
        $criter->compare('profile.branch', $branchId);
        $criter->compare('t.isstaff', 1);
        $criter->addCondition('t.level>0 and t.rank<>7');

        $withData = ($withPubInfo) ? array('profile','staffInfo') : 'profile';

        $userModels1 = User::model()->with( $withData )->findAll($criter);

        $userModels2 = array();
        if ($staffmap) {
            $userModels2 = User::model()->with( $withData )->findAllByPk(array_keys($staffmap));
        }

        $userModels = array_merge($userModels1, $userModels2);

        $crit = new CDbCriteria();
        $crit->order = 'weight ASC';
        $crit->index = 'id';

        //所有职位
        $positionModels = HrPosition::model()->findAll($crit);

        $userData = array();
        $userIdList = array();
        foreach($userModels as $uModel){
            $userIdList[] = $uModel->uid;
            $_n = intval($uModel->profile->nationality);

            if ($staffmap && in_array($uModel->uid, array_keys($staffmap))) {
                $uModel->profile->occupation_en = $staffmap[$uModel->uid];
            }

            $userData[$uModel->uid] = array(
                'uid' => $uModel->uid,
                'name' => $uModel->getName(),
                'photo' => $uModel->user_avatar,
                'intrest' => $uModel->user_intrest,
                'email' => $uModel->email,
                'gender' => $uModel->profile->user_gender,
                'nationality' => in_array($_n, array(0, 36, 175)) ? $_n : 999,
                'countryId' => $_n,
                'positionId' => $uModel->profile->occupation_en,
                'weight' => $positionModels[$uModel->profile->occupation_en]->weight,
                'positionTitle' => $titleBilingual ?
                        $positionModels[$uModel->profile->occupation_en]->cn_name . ' ' .
                        $positionModels[$uModel->profile->occupation_en]->en_name
                        : ( Yii::app()->language == 'en_us' ?
                        $positionModels[$uModel->profile->occupation_en]->en_name :
                        $positionModels[$uModel->profile->occupation_en]->cn_name ),
//                'isOnTYG' => $uModel->isOnTYG(),
                'isOnTYG' => 0,
            );

            if($withPass) {
                $userData[$uModel->uid]['pass'] = $uModel->pass;
            }

            if($withPubInfo){
                $userData[$uModel->uid]['pubPhoto'] =
                    empty($uModel->staffInfo) || empty($uModel->staffInfo->staff_photo) ?
                        'blank.jpg' : $uModel->staffInfo->staff_photo;
                $userData[$uModel->uid]['pubCn'] =
                    empty($uModel->staffInfo) ? '' : Yii::app()->format->ntext($uModel->staffInfo->intro_cn);
                $userData[$uModel->uid]['pubEn'] =
                    empty($uModel->staffInfo) ? '' : Yii::app()->format->ntext($uModel->staffInfo->intro_en);
            }
        }
        // 查询微信绑定信息
        $crit = new CDbCriteria();
        $crit->index = 'teacher_id';
        $crit->compare('teacher_id', $userIdList);
        $crit->compare('state', 1);
        $bindModels = WechatTeacherBind::model()->findAll($crit);
        foreach ($userData as $uid => $item) {
            if (isset($bindModels[$uid])) {
                $userData[$uid]['wechatBind'] = 1;
                $userData[$uid]['wechatName'] = $bindModels[$uid]->nickname;
                $userData[$uid]['wechatPhoto'] = $bindModels[$uid]->headimgurl;
            } else {
                $userData[$uid]['wechatBind'] = 0;
                $userData[$uid]['wechatName'] = '';
                $userData[$uid]['wechatPhoto'] = '';
            }
        }
        return $userData;
    }

    /**
     * @param int $surveyId
     * @param null $branchId
     * 生成调查问卷报告
     */
    static function genSurveyReport($surveyId=0, $branchId=null)
    {
        $surveyId = intval($surveyId);
        if($surveyId && $branchId) {
            Yii::import('common.models.survey.*');
            $survey = WSurvey::model()->findByPk($surveyId);

            //删除之前的报告信息
            if(true) {
                $crit = new CDbCriteria();
                $crit->compare('survey_id', $surveyId);
                $crit->compare('schoolid', $branchId);
                $crit->index = 'id';
                $reports = WSurveyReport::model()->findAll($crit);
                $reportKeys = array_keys($reports);
                if(!empty($reportKeys) && count($reportKeys) > 0) {
                    $delCrit = new CDbCriteria();
                    $delCrit->compare('report_id', $reportKeys);
                    WSurveyReportDetail::model()->deleteAll($delCrit);
                }
                WSurveyReport::model()->deleteAll($crit);

                WSurveyReportExtra::model()->deleteAllByAttributes(array(
                    'survey_id' => $surveyId,
                    'branch_id' => $branchId
                ));
            }

            //重新生成报告信息
            $batchSize = 400;
            $fbCrit = new CDbCriteria();
            $fbCrit->compare('schoolid', $branchId);
            $fbCrit->compare('survey_id', $surveyId);
            $total = WSurveyFeedbackOption::model()->count($fbCrit);
            $cycle = ceil($total/$batchSize);
            $fbCrit->limit = $batchSize;
            for($i=0; $i<$cycle; $i++) {
                $fbCrit->offset = $i * $batchSize;
                $options = WSurveyFeedbackOption::model()->findAll($fbCrit);
                foreach($options as $option) {
                    $countByTopic[$option->topic_id][$option->fb_id] = $option->fb_id;
                    $classid = intval($option->classid);

                    if(!isset($reportData[$option->topic_id][$classid][$option->option_answer])){
                        $reportData[$option->topic_id][$classid][$option->option_answer] = 1;
                    } else {
                        $reportData[$option->topic_id][$classid][$option->option_answer]++;
                    }

                }
            }

            $userid = empty(Yii::app()->user) ? 0 : Yii::app()->user->getId();
            if(!empty($countByTopic)):
            foreach($countByTopic as $topicID => $_arr){
                $report = new WSurveyReport();
                $report->setAttributes(array(
                    'survey_id' => $surveyId,
                    'topic_id' => $topicID,
                    'schoolid' => $branchId,
                    'votes' => count($_arr),
                    'status' => 0,
                    'update_user' => $userid,
                    'update_time' => time()
                ));
                if($report->save()){
                    $extraData = array();
                    foreach($reportData[$topicID] as $_classid => $_votes) {
                        $sql = 'INSERT INTO `ivy_survey_report_detail` ' .
                            ' (`report_id`, `option_answer`, `vote`, ' .
                            ' `classid`, `update_user`, `update_time`) VALUES ';
                        $sqldata = array();
                        foreach($_votes as $val=>$vote) {
                            $extraData[$_classid][$val] = $vote;
                            $sqldata[] = sprintf("(%d, %d, %d, %d, %d, %d)",
                                $report->id, $val, $vote, $_classid, $userid, time()
                            );
                        }
                        $sql .= implode(', ', $sqldata);
                        $insertCmd = Yii::app()->db->createCommand();
                        $insertCmd->setText($sql);
                        $insertCmd->execute();

                    }
                    $surveyReportExtra = new WSurveyReportExtra();
                    $surveyReportExtra->setAttributes(array(
                        'id' => $report->id,
                        'topic_id' => $topicID,
                        'survey_id' => $surveyId,
                        'branch_id' => $branchId,
                        'report_data' => base64_encode(serialize($extraData))
                    ));
                    $surveyReportExtra->save();
                }
            }
            endif;
        }
    }

    static function getStaffSurveyReportData($surveyId=0)
    {
        $batchNum = 100;
        $surveyId = intval($surveyId);
        if($surveyId) {
            Yii::import('common.models.survey.*');
            $reportCount = WSurveyReport::model()->countByAttributes(array('survey_id'=>$surveyId));
//            echo "Total count: " . $reportCount . "\n";
            $cycle01 = ceil($reportCount/$batchNum);
            for($i=0;$i<$cycle01;$i++){
                $reportRows = Yii::app()->db->createCommand()
                    ->select('*')
                    ->where('survey_id=:survey_id',array(':survey_id'=>$surveyId))
                    ->limit($batchNum)
                    ->offset($i * $batchNum)
                    ->from('ivy_survey_report')
                    ->queryAll();

                $reportExtraRows = Yii::app()->db->createCommand()
                    ->select('*')
                    ->where('survey_id=:survey_id',array(':survey_id'=>$surveyId))
                    ->limit($batchNum)
                    ->offset($i * $batchNum)
                    ->from('ivy_survey_report_extra')
                    ->queryAll();

                foreach($reportRows as $_row){
                    $reportData[$_row['schoolid']][$_row['topic_id']]['votes'] = $_row['votes'];
                }

                foreach($reportExtraRows as $_row){
                    $reportData[$_row['branch_id']][$_row['topic_id']]['data'] =
                        unserialize( base64_decode($_row['report_data']) );
                }
            }
            return $reportData;
        }
    }

    static function getChildNoteYears($childid) {
        $result = array();
        $rows = $command = Yii::app()->db->createCommand()
            ->selectDistinct('startyear')
            ->from('ivy_notes_child')
            ->where('childid=:childid', array(':childid'=>$childid))
            ->order('startyear ASC')
            ->queryAll();
        foreach($rows as $row) {
            if(!empty($row['startyear'])) {
                $result[] = $row['startyear'];
            }
        }
        return $result;
    }

    static function getChildNoteWeekRange($childid) {
        $result = array();
        $methods = array('min', 'max');
        foreach($methods as $method) {
            $rows = $command = Yii::app()->db->createCommand()
                ->select($method.'(weeknumber) as '.$method.',startyear')
                ->from('ivy_notes_child')
                ->where('childid=:childid', array(':childid'=>$childid))
                ->group('startyear')
                ->queryAll();

            foreach($rows as $row) {
                $result[$row['startyear']][$method] = $row[$method];
            }
        }
        return $result;
    }
}
?>
