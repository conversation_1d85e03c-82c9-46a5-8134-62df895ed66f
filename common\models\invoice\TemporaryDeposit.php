<?php

/**
 * This is the model class for table "ivy_child_temporary_deposit".
 *
 * The followings are the available columns in table 'ivy_child_temporary_deposit':
 * @property integer $invoice_id
 * @property integer $childid
 * @property integer $yid
 * @property double $amount
 */
class TemporaryDeposit extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return TemporaryDeposit the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_child_temporary_deposit';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('invoice_id, childid, yid, amount', 'required'),
			array('invoice_id, childid, yid', 'numerical', 'integerOnly'=>true),
			array('amount', 'numerical'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('invoice_id, childid, yid, amount', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'invoice_id' => 'Invoice',
			'childid' => 'Childid',
			'yid' => 'Yid',
			'amount' => 'Amount',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('invoice_id',$this->invoice_id);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('amount',$this->amount);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
	
	/**
	 *　获得符合条件的预留学费
	 * @param int $invoiceId
	*  @param array|string $param
	 * @return array|string|obj
	 * <AUTHOR>
	 */
	public function getDeposit($invoiceId,$param = array())
	{
		if ($invoiceId)
		{
			$obj = $this->findByPk($invoiceId);
			if (is_object($obj) && count($obj))
			{
				if (is_array($param) && count($param))
				{
					return $obj->getAttributes($param);
				}
				elseif (is_string($param) && strlen($param))
				{
					return $obj->getAttribute($param);
				}
				else
				{
					return $obj;
				}
			}
		}
		return null;
	}
}