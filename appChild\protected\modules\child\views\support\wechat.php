<?php
$this->breadcrumbs = array(
    Yii::t("navigations", 'Support') => array('/child/support'),
    Yii::t("navigations", 'Wechat Support'),
);

?>

<h1><label><?php echo Yii::t("navigations", "Wechat Support") ?></label></h1>

<div class="span-12 colborder">
        <?php if (Yii::app()->language == "en_us"): ?>
            <p class="desc">
            <img src="<?php echo Yii::app()->theme->baseUrl . '/images/icons/wechat_logo_en.png';?>" class="fl mr20" />
            You can now access the system directly from <a href="http://www.wechat.com/en/" target="_blank"><span>Wechat</span></a> (a popular messaging app in China for smart phones)!
            </p>
            <p class="desc bm15">With Wechat, you can do the following:</p>
            <ul class="desc withIndent">
                <li>View weekly report, lunch menu and your child’s classmates</li>
                <li>Cancel or resume lunch</li>
                <li>Retrieve recent invoices and account credits</li>
                <li>Upload your child’s profile photo</li>
                <li>Contact the campus support team</li>
            </ul>

           

            <div class="mb20">
                <?php if(Mims::unIvy()):?>
                    <img src="<?php echo Yii::app()->theme->baseUrl . '/images/wechat03.jpg';?>" />
                    <img src="<?php echo Yii::app()->theme->baseUrl . '/images/wechat04.jpg';?>" />
                <?php else:?>
                    <img src="<?php echo Yii::app()->theme->baseUrl . '/images/wechat01.jpg';?>" />
                    <img src="<?php echo Yii::app()->theme->baseUrl . '/images/wechat02.jpg';?>" />
                <?php endif;?>
            </div>
            
            <p>More features are coming soon!</p>

        <?php else: ?>
            <p class="desc">
            <img src="<?php echo Yii::app()->theme->baseUrl . '/images/icons/wechat_logo.png';?>" class="fl mr20" />
            <a href="http://weixin.qq.com/" target="_blank"><span>微信</span></a>是一个手机应用，如果您正在使用微信，您可以通过获取定制二维码关注“<?php echo Mims::unIvy()?Yii::app()->params['ownerConfig']['wechatTitle']:'艾毅幼儿园家长服务'?>”并绑定您的系统帐号。</p>
            <p class="desc bm15">绑定帐号之后您可以使用下面的功能。</p>
            <ul class="desc withIndent">
                <li>查看周报告、食谱及班级小伙伴</li>
                <li>取消或恢复订餐</li>
                <li>查看近期账单及个人账户余额</li>
                <li>上传孩子头像</li>
                <li>联系校园等</li>
            </ul>
            
            <div class="mb20">
            <?php if(Mims::unIvy()):?>
                <img src="<?php echo Yii::app()->theme->baseUrl . '/images/wechat03.jpg';?>" />
                <img src="<?php echo Yii::app()->theme->baseUrl . '/images/wechat04.jpg';?>" />
            <?php else:?>
                <img src="<?php echo Yii::app()->theme->baseUrl . '/images/wechat01.jpg';?>" />
                <img src="<?php echo Yii::app()->theme->baseUrl . '/images/wechat02.jpg';?>" />
            <?php endif;?>
            </div>
            
            <p class="desc bm15">更多的功能正在计划推出，敬请关注。</p>
        <?php endif; ?>
    
    <?php if($boundCount < $this->wechatPerAccount):?>    
        <h2><?php echo Yii::t("support", "Auto-link Wechat using QR Code");?></h2>
        <div class="flash-info2"><?php echo Yii::t("support", 'Note: The QR Code below will allow any person to access your child\'s the system account.  Please do not share it for security purposes.');?></div>
        <button id="btn-fetchQr" disabled=disabled class="wc-btn-disabled" style="padding:20px; font-size:18px;"><?php echo Yii::t('support', 'Obtain QR Code');?></button>
        <div id="Qr-return"></div>
        
        <?php if(Yii::app()->user->checkAccess('adminChild', array("childid" => $this->childid))):?>
        <script>
        $(function(){
            $('#btn-fetchQr').click(function(){fetchQrCode();})
            $('#btn-fetchQr').removeAttr('disabled');
            $('#btn-fetchQr').removeClass('wc-btn-disabled');
            $('#btn-fetchQr').addClass('wc-btn');
            
            function fetchQrCode(){
                $('#Qr-return').html('<div class="loading pl-28"><?php echo Yii::t('support', 'Connecting to Wechat server...');?></div>')
                $('#btn-fetchQr').attr('disabled','disabled');
                $('#btn-fetchQr').removeClass('wc-btn');
                $('#btn-fetchQr').addClass('wc-btn-disabled');
                $.ajax({
                    url: '<?php echo $this->createUrl('//child/support/wechat');?>',
                    dataType: 'json',
                    type: 'POST',
                    data:{
                        command: 'doRemote'
                    }
                }).done(function(data){
                    if(data.state=='success'){
                        $('#Qr-return').html('').hide();
                        var img = $('<img width="215" />').attr('src', 'https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket='+data.data.ticket);
                        $('#Qr-return').append('<p><?php echo Yii::t('support','From Wechat, click on "Scan QR Code" under "Discover".');?></p>');
                        $('#Qr-return').append(img).show();
                    }
                    $('#btn-fetchQr').removeAttr('disabled');
                    $('#btn-fetchQr').removeClass('wc-btn-disabled');
                    $('#btn-fetchQr').addClass('wc-btn');
                });
            }
        });
        </script>
        <?php endif;?>
        
    <?php else:?>
        <h2><?php echo Yii::t("support", "Security Alert: Too Many Accounts Linked");?></h2>
        <div class="flash-info2">
        <?php echo Yii::t('support', 'For security purposes, only :number Wechat accounts can be auto-linked to your IvyOnline account. If you are sure that the current links are legitimate, you can link additional accounts manually.', array(':number'=>$this->wechatPerAccount));?>
        </div>
        <p><?php echo Yii::t('support', 'From Wechat, click on "Scan QR Code" under "Discover".');?></p>
        <div class="mb20">
            <img src="<?php echo Yii::app()->theme->baseUrl . '/images/wechatqrcode.jpg';?>" />
        </div>
        
    <?php endif;?>
</div>

<div class="span-5 last">
    <div id="bound-box">
        <h3 class="wc-btn" id="summary" title="<?php echo Yii::t('support','Click to view');?>">
            <em id="bound-number"><?php echo $boundCount;?></em>
            <?php echo Yii::t('support', 'Number of linked accounts');?> 
        </h3>
        <div id="bound-list">
        
        </div>
    </div>
</div>

<script>
var defaultPhoto = '<?php echo Yii::app()->theme->baseUrl . '/images/wechat_default.jpg';?>';
var wechatUpdating = false;
var linktoFatherMsg = "<?php echo Yii::t('support', 'Linked to father\'s account');?>";
var linktoMotherMsg = "<?php echo Yii::t('support', 'Linked to mother\'s account');?>";
$(function(){
    $('#bound-box h3#summary').click(function(){
        if(wechatUpdating) return false;
        wechatUpdating = true;
        $('#bound-list').html("<div class='small loading pl-28'><?php echo Yii::t('support', 'Connecting to Wechat server...');?></div>");
        $.ajax({
            url: "<?php echo $this->createUrl('//child/support/wechatUserList');?>",
            dataType: 'json',
            type: 'POST',
            data: {
                actionType: 'userList'
            }
        }).done(function(data){
            displayWechatUserList(data);
            wechatUpdating = false;
        });
    });
    
    function displayWechatUserList(data){
        $('#bound-list').html("");
        var mainBox = $('#bound-list').append('<ul></ul>');
        var count=0;
        for(i in data){
            count++;
            var li=$('<li class="wechat-user"></li>').attr('openid',data[i]['openid']);
            li.html('<h3>'+data[i]['nickname']+'</h3>');
            var hasAdd=false;
            var location=$('<div class="location"></div>');
            if(data[i]['country']!=''){
                location.append('<span>'+data[i]['country']+'</span>');
                hasAdd=true;
            }
            if(data[i]['province']!=''){
                location.append('<span>'+data[i]['province']+'</span>');
                hasAdd=true;
            }
            if(data[i]['city']!=''){
                location.append('<span>'+data[i]['city']+'</span>');
                hasAdd=true;
            }
            if(hasAdd){
                li.append(location);
            }
            var _msg = (data[i]['boundto']=='f') ? linktoFatherMsg : linktoMotherMsg;
            var div=$('<div class="small"></div>').html(_msg);
            li.append(div);
            var div=$('<div class="small"></div>').html('<?php echo Yii::t('support','Recent access: ');?>'+((data[i]['lastcontact']!="")?data[i]['lastcontact']:'<?php echo Yii::t('support','N/A');?>'));
            var em=$('<em class="hover unbind" onclick="unbind(this)" title="<?php echo Yii::t('support', 'Remove Link');?>"></em>').html('<?php echo Yii::t('support', 'Remove Link');?>');
            li.append(div).append(em);
            mainBox.children('ul').append(li);
        }
        $('#bound-number').html( count );
    }
});
var unbinding=false;
function unbind(obj){
    if(unbinding) return false;
    if(!confirm('<?php echo Yii::t('support','This Wechat user will no longer be able to access IvyOnline. Confirm to proceed?');?>')) return false;
    var li = $(obj).parents('li.wechat-user');
    var openid = li.attr('openid');
    unbinding=true;
    $(obj).addClass('loading');
        $.ajax({
            url: "<?php echo $this->createUrl('//child/support/wechatUserList');?>",
            dataType: 'json',
            type: 'POST',
            data: {
                openid:openid,
                userid:<?php echo Yii::app()->user->id;?>,
                actionType: 'userDisable'
            }
        }).done(function(data){
            if(data.state=='success'){
                li.remove();
                var _od = parseInt($('#bound-number').html()) - 1;
                $('#bound-number').html( _od );
            }else{
                $(obj).removeClass('loading');
                alert(data.message);
            }
            unbinding = false;
        });
    
}
</script>
