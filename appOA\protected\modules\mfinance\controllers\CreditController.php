<?php

class CreditController extends BranchBasedController
{
    public $dcategory = 'finance';
    public $dsubcategory = 'handover';
    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'main';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Finance Mgt');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mfinance/credit/index');
    }

    /**
     * 根据条件读取孩子账单列表
     */
	public function actionIndex($branchId = 0,$time = 0)
	{
        Yii::import('common.models.invoice.*');
        Yii::import('common.models.child.ChildCreditNow');

        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        
        if(!$time = strtotime($time)){
            //将时间转换为当日零点
            $time = strtotime(date("Y-m-d"));
            $date = date('Y-m-d',$time);
        }else{
            $date = date('Y-m-d',$time);
            $time = strtotime($date);
        }
        $sql = "select tt.childid,tt.childname,tt.classid,tt.classname,tt.schoolid,tt.surplus,tt.updated_timestamp from 
                (select childid,childname,classid,classname,schoolid,surplus,updated_timestamp from ivy_child_credit_now order by updated_timestamp desc)as tt 
                where tt.updated_timestamp < $time group by childid having tt.schoolid = '$branchId' and tt.surplus !=0 order by classid";

        $sql = "select f.*
from (
   select childid, max(updated_timestamp) as updated
   from ivy_child_credit_now where updated_timestamp < $time group by childid
) as x inner join ivy_child_credit_now as f on f.childid = x.childid and f.updated_timestamp = x.updated where f.surplus>0 and schoolid='$branchId' order by f.classid";
        $creditChild = ChildCreditNow::model()->findAllBySql($sql);
        $this->render('index',array('credit'=>$creditChild,));
	}
    /**
     * 导出孩子账单
     */
    public function actionExport($branchId=0,$time=0)
    {
        Yii::import('common.models.invoice.*');
        Yii::import('common.models.child.ChildCreditNow');

        if(!$time = strtotime($time)){
            //将时间转换为当日零点
            $time = strtotime(date("Y-m-d")); 
            $date = date('Y-m-d',$time); 
        }else{
            $date = date('Y-m-d',$time);
            $time = strtotime($date);
        }
        Yii::import('common.models.invoice.*');
        Yii::import('common.models.portfolio.ChildStudyHistory');

        $sql = "select tt.childid,tt.childname,tt.classid,tt.classname,tt.schoolid,tt.surplus,tt.updated_timestamp from 
                (select childid,childname,classid,classname,schoolid,surplus,updated_timestamp from ivy_child_credit_now order by updated_timestamp desc)as tt 
                where tt.updated_timestamp < $time group by childid having tt.schoolid = '$branchId' and tt.surplus !=0 order by classid";

        $sql = "select f.*
from (
   select childid, max(updated_timestamp) as updated
   from ivy_child_credit_now where updated_timestamp < $time group by childid
) as x inner join ivy_child_credit_now as f on f.childid = x.childid and f.updated_timestamp = x.updated where f.surplus>0 and schoolid='$branchId' order by f.classid";
        $creditChild = ChildCreditNow::model()->findAllBySql($sql);
        $credit = ChildCreditNow::model()->findAllBySql($sql);
        $title = $this->branchObj->abb.'-'.$date;
        header("Content-type:application/vnd.ms-excel");
        header("Content-Disposition:attachment;filename=".$title.".xls");
        echo $this->_t("学校 \t 班级 \t 班级ID \t 姓名 \t 孩子ID \t 年龄 \t 状态 \t 余额 \n");
        $sum = '';
        foreach($credit as $v){
            $sum += $v['surplus'];

            echo $this->_t($v['schoolid']."\t");
            echo $this->_t($v['classname']."\t");
            echo $this->_t($v['classid']."\t");
            echo $this->_t($v->child->getChildName()."\t");
            echo $this->_t($v['childid']."\t");
            echo $this->_t(CommonUtils::getAge($v->child['birthday'])."\t");
            echo $this->_t($v->child->getStatus()."\t");
            echo $this->_t($v['surplus']."\n");
        }
        echo $this->_t("\t\t\t\t\t\t\t总余额：{$sum} \n");
    }
    public function _t($str)
    {
        return iconv('utf-8', 'GBK', $str);

    }

}