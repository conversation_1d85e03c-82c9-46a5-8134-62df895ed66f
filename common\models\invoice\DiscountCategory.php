<?php

/**
 * This is the model class for table "ivy_discount_category".
 *
 * The followings are the available columns in table 'ivy_discount_category':
 * @property integer $id
 * @property string $title_en
 * @property string $title_cn
 * @property integer $stat
 * @property integer $updated_timestamp
 * @property integer $userid
 */
class DiscountCategory extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return DiscountCategory the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_discount_category';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('title_en, title_cn, stat, updated_timestamp, userid', 'required'),
			array('stat, updated_timestamp, userid', 'numerical', 'integerOnly'=>true),
			array('title_en, title_cn', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, title_en, title_cn, stat, updated_timestamp, userid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'title_en' => 'Title En',
			'title_cn' => 'Title Cn',
			'stat' => 'Stat',
			'updated_timestamp' => 'Updated Timestamp',
			'userid' => 'Userid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('title_en',$this->title_en,true);
		$criteria->compare('title_cn',$this->title_cn,true);
		$criteria->compare('stat',$this->stat);
		$criteria->compare('updated_timestamp',$this->updated_timestamp);
		$criteria->compare('userid',$this->userid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}