<?php

// uncomment the following to define a path alias
// Yii::setPathOfAlias('local','path/to/local-folder');

// This is the main Web application configuration. Any writable
// CWebApplication properties can be configured here.

$dbname = "dbname";
$dbuser = "dbuser";
$dbpass = "dbpass";
$oaRedirect = true;

$cookieDomain = "www.site.com"; //孩子前台专用，无需共享
$oaUrl = "http://oa.site.com"; //OA 地址

$appsUrl = 'http://apps.mims.cn';

$ivyschoolsUrl = 'http://www.ivyschools.com';
$useIS = true;
$uploadBaseUrl = $oaUrl . "/uploads/"; //开发时为显示图片可以设置为实际OA的upload URL地址
$uploadPath = '/path/to/oa/uploads/'; 
$xoopsVarPath = '/path/to/XOOPS-data';
$onlineBanking = "sandbox"; //sandbox, production 
$YeepayRespondeUrl= $oaUrl . '/responder/responder.php';
$AlipayResponse = array(
    "return" => $oaUrl . '/responder/alipay_sync_notify.php',
    "notify" => $oaUrl . '/responder/alipay_async_notify.php',
);
$sessionName = "MIMSCSID";  //sessionId的名字，别跟XOOPS那边的一样，可随意设定
$basePath = dirname(__FILE__) . DIRECTORY_SEPARATOR . '..';


$sessionTableName ="ivy_session_cc"; //为孩子前台分离出的session新表，与ivy_session表结构完全一样

$sessionKeyPrefix = md5($basePath);

require_once $basePath . DIRECTORY_SEPARATOR . 'components' . DIRECTORY_SEPARATOR . 'utils.php';


$baseUrl = "http://" . $cookieDomain;

return array(
	'basePath'=>$basePath,
	'name'=>'IvyOnline Child Center',
	'homeUrl'=>$baseUrl,

	// preloading 'log' component
	'preload'=>array('log'),
    'theme'=>'highlight',
    'defaultController'=>'user/login',

	// autoloading model and component classes
	'import'=>array(
		'common.models.*',
		'application.components.*',
	),

    'sourceLanguage'=>'en_us',
    'language'=>'zh_cn',
	'localeDataPath'=>dirname(__FILE__).DIRECTORY_SEPARATOR.'..'.'/components/locale_data/',
	'modules'=>array(
		// uncomment the following to enable the Gii tool
	/*	
		'gii'=>array(
			'class'=>'system.gii.GiiModule',
			'password'=>'test!',
		 	// If removed, Gii defaults to localhost only. Edit carefully to taste.
			'ipFilters'=>array('127.0.0.1','::1'),
		),
        */
		'user'=>array(
			"OAUrl" => $oaUrl,
			"OALoginUrl" => $oaUrl . "/login.php"
		),
		'weixin',
        'child'=>array(),
        'portfolio'=>array(),
	),

	// application components
	'components'=>array(
	
        'session'=>array(
            'autoCreateSessionTable'=>false,
            'class'=>'application.components.CXoDbHttpSession',
            'cookieParams'=>array('domain'=>$cookieDomain,'lifetime'=>0),
            'connectionID' => 'db',
            'timeout' => 28800,
            'sessionTableName' =>$sessionTableName,
            'sessionName' => $sessionName,
        ),
		'user'=>array(
			// enable cookie-based authentication
            'class'=>'application.components.CXoWebUser',
            'allowAutoLogin'=>true,
			'_keyPrefix' => $sessionKeyPrefix,
		),
        'authManager'=>array(
            'class'=>'CDbAuthManager',
            'connectionID' => 'db',
            'defaultRoles'=>array('visitor')
        ),
		// uncomment the following to enable URLs in path-format
		'urlManager'=>array(
            'baseUrl'=>$baseUrl,
			'urlFormat'=>'path',
            //'urlSuffix'=>'.ivy',
			'rules'=>array(
				//'<controller:\w+>/<id:\d+>'=>'<controller>/view',
                'v/<id:\w+>'=>'access/index',
                'scholarship/<startyear:\d+>'=>'child/scholarship/index',
				'lostpass/<activkey:\w+>/<email:.*>'=>'user/recovery/recovery',
                '<childid:\d+>' => 'child/profile/welcome',
                '<childid:\d+>/portfolio/portfolio/journal/<startyear:\d+>-<classid:\d+>-<weeknum:\d+>' => 'portfolio/portfolio/journal',
                '<childid:\d+>/portfolio/portfolio/semester/<startyear:\d+>-<semester:\d+>' => 'portfolio/portfolio/semester',
                '<childid:\d+>/child/resource/content/<category:\w+>' => 'child/resource/content',
                '<childid:\d+>/child/profile/profile/<t:\w+>' => 'child/profile/profile',
				
                '<childid:\d+>/<module:\\w+>/<controller:\w+>/<action:\w+>' => '<module>/<controller>/<action>',
                '<childid:\d+>/<module:\\w+>/<controller:\w+>/<action:\w+>/<classid:\d+>' => '<module>/<controller>/<action>',
				'<childid:\d+>/<controller:\w+>/<action:\w+>/<id:\d+>'=>'<controller>/<action>',
				'<childid:\d+>/<controller:\w+>/<action:\w+>'=>'<controller>/<action>',
			),
		),
		/*
        'themeManager'=>array(
            'baseUrl'=>'http://static.iw.cn'
        ),
		*/
		// uncomment the following to use a MySQL database
		'db'=>array(
			'connectionString' => 'mysql:host=localhost;dbname='.$dbname,
			'emulatePrepare' => true,
			'username' => $dbuser,
			'password' => $dbpass,
			'charset' => 'utf8',
			'schemaCachingDuration' => 300,
		),
		'cache'=>array(
				'class'=>'CFileCache',
		),
		'weatherCache'=>array(
				'class'=>'CDbCache',
				'connectionID'=>'db',
				'autoCreateCacheTable'=>false,
				'keyPrefix'=>'weather_',
				'cacheTableName'=>'ivy_yii_dbcache',
		),		
        'request'=>array(
            'enableCookieValidation'=>true,  
        ),
		'errorHandler'=>array(
			// use 'site/error' action to display errors
            'errorAction'=>'site/error',
        ),
		'log'=>array(
			'class'=>'CLogRouter',
			'routes'=>array(
				array(
					'class'=>'CFileLogRoute',
					'levels'=>'error, warning',
				),
				// uncomment the following to show log messages on web pages
				/*
				array(
					'class'=>'CWebLogRoute',
				),
				*/
			),
		),
	),

	// application-level parameters that can be accessed
	// using Yii::app()->params['paramName']
	'params'=>array(
		'baiduStats'=>false,
		// this is used in contact page
		'adminEmail'=>'<EMAIL>',
		'uploadBaseUrl'=>$uploadBaseUrl,
		'uploadPath'=>$uploadPath,
		// xoops data 目录 用于存放 在线支付的日志文件
		'xoopsVarPath'=>$xoopsVarPath,
		// 商户接收支付成功数据的地址
		'YeepayRespondeUrl'=>$YeepayRespondeUrl,
		'AlipayResponds'=>array(
			"return"=>$AlipayResponse['return'],
			"notify"=>$AlipayResponse['notify'],
		),
		'onlineBanking' => $onlineBanking, //sandbox, production 
		'refreshAssets' => 'refreshAssets' ,
		'oaRedirect' => $oaRedirect,
        'ivyschoolsUrl' => $ivyschoolsUrl,
        'useIS' => $useIS,
        'appsUrl' => $appsUrl,
	),
);
