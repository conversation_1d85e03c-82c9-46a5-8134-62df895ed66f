<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'to-siblings-form',
	'enableAjaxValidation'=>false,
	'htmlOptions'=>array('class'=>'J_ajaxForm'),
));
?>

<div class="pop_cont pop_table" style="height:auto;">
    <div class="tips_light"><?php echo Yii::t("payment", '余额转移只能在兄弟姐们间进行')?></div>
    <h2><?php echo Yii::t("payment", '当前余额');?><?php echo $this->childObj->credit?></h2>
    <table width="100%">
        <colgroup>
            <col class="th" />
            <col />
        </colgroup>
        <tbody>
            <tr>
                <th><?php echo $form->labelEx($model,'to'); ?></th>
                <td><?php echo $form->dropDownList($model,'to',$sibs,array('empty'=>Yii::t("global", 'Please Select')));?></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'amount'); ?></th>
                <td><?php echo $form->textField($model,'amount', array('maxlength'=>255,'class'=>'input length_2')); ?></td>
            </tr>
        </tbody>
    </table>
</div>

<div class="pop_bottom">
    <button type="button" class="btn fr" id="J_dialog_close"><?php echo Yii::t('global','Cancel');?></button>
    <button class="btn fr btn_submit mr10 J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
</div>
<?php $this->endWidget(); ?>