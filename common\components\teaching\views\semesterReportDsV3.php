<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="language" content="en" />
    <title><?php echo CHtml::encode($this->pageTitle); ?></title>
    <style>
        #page-1{
            margin-top:60px
        }
        .navbar-inverse {
            background-color: #333333;
            border-color: #efefef;
        }
        .navbar-inverse form {
            line-height: 50px;
            height: 50px;
            text-align: center;
        }
        .navbar-inverse form input {
            margin-top: 13px;
        }
        .navbar-fixed-top {
            top: 0;
            position: fixed;
            right: 0;
            left: 0;
            z-index: 1030;
            -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
        }
        .report-page{
            /* min-height:877px; */
            position: relative;
            background-size: 100%;
            background-color: #ffffff;
            /* margin-top:12px */
        }
        .pull-content {
            margin:0 auto;
            width: 580px;
            padding:24px 0
        }
        .flex{
            display: -webkit-box; 
            display: -ms-flexbox; 
            display: -webkit-flex;
            display: flex;    
        }
        .flex1{
            -webkit-box-flex: 1;
            -webkit-flex: 1;
            flex: 1;
        }
        .align_items{
            -webkit-align-items: center;
            align-items: center;
        }
        .mt4{
            margin-top:4px
        }
        .mt16{
            margin-top:16px
        }
        .ml10{
            margin-left:10px
        }
        .mr10{
            margin-right:10px
        }
        .bold{
            font-weight:bold
        }
        img{
            max-width:100%;
            display:inline !important;
        }
        .missionTop{
            background: #F0F5FB;
            border-radius: 8px;
            padding:30px 32px;
            background-image: url('https://m2.files.ivykids.cn/cloud01-file-8025768Fr2nYqqUAEhRYOnVKT31337Ts2F1.jpg');
            background-repeat:no-repeat;
            background-size: 224px;
            background-position: left bottom;
            color:#093190;
        }
        .missionLeft{
            width: 180px;
            font-size: 18px;
            color: #093190;
            line-height: 25px;
            text-align: left;
            font-weight:bold;
            <?php if (Yii::app()->language == 'zh_cn'): ?>
                font-family:"Noto Sans SC";
            <?php else: ?>
                font-family:'Arial Black','Arial';
            <?php endif; ?>
        }
        .missionRight{
            width: 274px;
            font-weight: bold;
            font-size: 13px;
            line-height: 18px;
            margin-left:40px
        }
        .introduction{
            margin:20px 0 32px;
        }
        .introduction div {
            font-size: 12px;
            color: #333333;
            line-height: 18px;
            margin-bottom:20px
        }
        .titleBgComm{
            font-weight: bold;
            font-size: 18px !important;
            color: #333333 !important;
            line-height: 20px !important;
            margin-top:16px;
            display:inline-block;
        }
        .titleBgComm .bg{
            background:url('https://m2.files.ivykids.cn/cloud01-file-8025768Fg6SnQeMcXK5FPVCvRGZEsd3l50n.png');
            /* background: linear-gradient( 270deg, #FFFFFF 0%, #AFCFFF 100%); */
            background-size:100% 10px;
            background-position: left bottom;
            height:10px;
            display:block;
            margin-top:-8px
        }
        .arrowTitle {
            margin-top:20px
        }
        .arrowTitle .title{
            font-weight: bold;
            font-size: 14px;
            color: #093190;
            line-height: 16px;
        }
        .arrowTitle .title img{
            margin-right:10px;
        }
        .arrowTitle div{
            font-size: 12px;
            color: #333333;
            line-height: 18px;
            margin-top:16px
        }
        .custom-table{
            width: 100%;
            border-collapse: collapse;
            margin-top:24px;
            margin-bottom:24px;
        }
        .custom-table th,
        .custom-table td {
            border: 1px solid #E5E6EB;
            padding:10px 12px;
            vertical-align: middle;
            font-size: 12px;
            color: #333333;
            line-height: 18px;
        }
        .level_tr th{
            font-size:12px;
            white-space: nowrap;
            font-weight: bold;
            color:#333
        }
        .nowrap{
            white-space: nowrap;
        }
        .custom-table th {
            background-color: #F2F3F5;
            text-align: center;
            font-weight:bold;
            color:#333
        }
        .academic{
            margin:0 0 20px 0 ;
            flex-wrap: wrap;
            gap: 20px 0; 

        }
        .academic .flex1{
            padding:20px;
            background: #F7F7F8;
            border-radius: 8px;
            flex: 0 0 calc(50% - 10px);
            box-sizing: border-box;
        }
        .academic .flex1 .title{
            font-weight: bold;
            font-size: 14px;
            color: #333333;
            line-height: 18px;
            text-align: center;
            margin-bottom:4px
        }
        .level{
            display: -webkit-box; 
            display: -ms-flexbox; 
            display: -webkit-flex;
            display: flex;    
            -webkit-align-items: center;
            align-items: center;
            margin-top:12px
        }
        .level .levelStatus{
            width: 40px;
            height: 20px;
            border-radius: 10px;
            font-weight: 500;
            font-size: 12px;
            color: #FFFFFF;
            line-height:22px;
            text-align: center;
        }
        .level .levelText {
            font-size: 12px;
            color: #333333;
            line-height:22px;
            margin-left:16px;
        }
        .attendanceText{
            margin-top:12px;
            font-size: 12px;
            color: #333333;
            line-height: 16px;
            margin-bottom:24px
        }
        .teacherList{
            margin-top:8px
        }
        .teacher{
            margin-right:16px
        }
        .teacher img{
            float: left;
        }
        .teacher span{
            font-size: 12px;
            color: #333333;
            line-height: 16px;
            text-align: left;
            margin-left:4px;
            float: left;
            margin-right: 16px
        }
        .semester1{
            background: #F2F9F2;
            text-align:center
        }
        .semester2{
            background: #F0F5FB;
            text-align:center
        }
        .explainText{
            margin-top: 16px;
            font-size: 12px;
            color: #333333;
            line-height: 16px;
        }
        body {
            font-family:Arial,'PingFang SC';
            margin: 0;
            padding: 0;
            color: #333333;
        }
        <?php if($withToolBar):?>
        body{
            background-color: #f2f2f2;
        }
        <?php endif;?>
        .page-break {
            width: 210mm;
            /* height: 257mm; */
            margin: 0 auto;
            padding: 0 20mm 1mm;
            box-sizing: border-box;
            background-color: #fff;
            page-break-after: always;
        }
        .iframe {
            width: 210mm;
            height: 297mm;
            overflow: hidden;
            border-width: 0px;
        }
        .report-wrapper{
            width: 210mm;
            margin: 0 auto;
        }
        .iframeDiv{
            margin-top:60px
        }
        .headerIframe{
            width: 210mm;
            height:31mm;
            background:#fff
        }
        .headerIframe iframe{
            width: 210mm;
            height:31mm;
            border-width: 0px;
        }
        .mission,.attendance,.subject{
            box-sizing: border-box;
            page-break-after: always;
        }
        .text_center{
            text-align:center
        }
        .clearFix{
            clear:both
        }
        .mt0 {
            /* margin-top:0 !important */
        }
        .mb20{
            margin-bottom:20px
        }
        .footerIframe{
            width: 210mm;
            height:23mm;
            background:#fff
        }
        .footerIframe iframe{
            width: 210mm;
            height:23mm;
            border-width: 0px;
        }
        .mission{
            padding-top:16px
        }
         .grades img{
            float: left;
            margin-top: 4px;
            margin-right: 8px;
         }
        .grades .flex1 {
            font-weight: bold;
            font-size: 14px;
            color: #093190;
            line-height: 18px;
            text-align: left;
            font-style: normal;
            white-space: nowrap;
            float: left;
            margin:0
        }
        .mt28{
            margin-top:28px !important
        }
    </style>
</head>
<body>
    <?php if($withToolBar):?>
        <div class="navbar navbar-fixed-top navbar-inverse" role="navigation">
            <?php
            echo CHtml::form( $downloadActionUrl );
            echo CHtml::hiddenField('schoolid',     $params['schoolid']);
            echo CHtml::hiddenField('childid',      $params['childid']);
            echo CHtml::hiddenField('classid',      $params['classid']);
            echo CHtml::hiddenField('id',           $params['id']);
            echo CHtml::submitButton('Download PDF / 下载 PDF', array('style'=>'font-family: Microsoft Yahei'));
            echo CHtml::endForm();
            ?>
        </div>
    <?php endif;?>
    <div class="report-wrapper">
        <?php if($withToolBar):?>
        <div class='iframeDiv'>
            <iframe src="<?php echo $coverUrl; ?>"  class='iframe'></iframe>
        </div>
        <div class="headerIframe">
            <iframe src="<?php echo $headerUrl; ?>" ></iframe>
        </div>
        <?php endif;?>
        <div class="page-break">
            <div class="mission">
                <div class='missionTop flex'>
                    <div class='missionLeft'>
                    <?php echo Yii::t('principal', 'The Mission of Daystar Academy') ?>
                    </div>
                    <div class='flex1 missionRight'>
                        <div><?php echo Yii::t('principal', 'We cultivate social innovators to inspire the best in humanity and create a world that is more equitable, sustainable and united.') ?></div>
                        <div class='mt16'><?php echo Yii::t('principal', 'We nurture students’ character, curiosity and motivation to positively impact their communities,demonstrating the courage to change and inspire.') ?></div>
                    </div>
                  </div>
                  <?php if (Yii::app()->language == 'zh_cn'): ?>
                    <div class='introduction'>
                        <div class='titleBgComm'>小学项目介绍
                            <span class='bg'></span>
                        </div>
                        <div>启明星学校是一所位于北京的PreK-12年级私立非营利走读学校，获得了国际学校理事会（CIS）和西部学校与学院协会（WASC）的认证，并且是一所完全授权的IB世界学校，提供完整的IB项目，包括适用于一至五年级的小学项目（PYP）、适用于六至十年级的中学项目（MYP）和适用于十一和十二年级的大学预科项目（DP）。
                        </div>
                        <div>严谨的PYP框架强调基于探究的学习，学生每年通过六个超学科探究单元（UOI）的学习，培养批判性思维、协作能力和全球意识。启明星学校的小学项目采用综合的双语课程模式，学生的学术学习时间大约50%使用英文，50%使用中文。
                        </div>
                    </div>
                    <div class='arrowTitle'>
                        <div class='flex align_items title'>
                            <img src="https://m2.files.ivykids.cn/cloud01-file-8025768Fl-o_jS7_GTj8QkaYgTMSiK3X8rQ.png" alt="" style='width:18px'>
                            <span>学生评估报告的次数</span>
                        </div>
                        <div class='mb20'>每个学年分为两个大致相等的学期。学年从八月底开始，到六月底结束。为了有效评估学生的成长和发展情况，小学每年提供两次全面的评估报告——分别在第一学期和第二学期结束时，提供关于学生成就、技能发展和学术进展的详细说明。</div>
                    </div>
                    <?php else: ?>
                    <div class='introduction'>
                        <div class='titleBgComm'>Introduction of Elementary School Program
                            <span class='bg'></span>
                        </div>
                        <div>Daystar Academy is a PreK-12 private not-for-profit day school in Beijing. It holds accreditation from the Council of International Schools (CIS) and the Western Association of Schools and Colleges (WASC) and is a fully authorized IB World School offering the complete IB continuum programmes, including the Primary Years Programme (PYP) for grades one to five, Middle Years Programme (MYP) for grades six to ten, and Diploma Programme (DP) for grades eleven and twelve. 
                        </div>
                        <div>The rigorous PYP framework emphasizes inquiry-based learning, with students engaging in six transdisciplinary Units of Inquiry (UOI) annually to develop critical thinking skills, collaborative capabilities, and global awareness. Daystar Academy elementary schools follow an integrated dual-language curriculum model, where students spend approximately 50% of their academic learning time in English and 50% in Chinese.
                        </div>
                    </div>
                    <div class='arrowTitle'>
                        <div class='flex align_items title'>
                            <img src="https://m2.files.ivykids.cn/cloud01-file-8025768Fl-o_jS7_GTj8QkaYgTMSiK3X8rQ.png" alt="" style='width:18px'>
                            <span>Frequency of Reporting of Student Progress</span>
                        </div>
                        <div class='mb20'>The academic year is separated into two approximately equal semesters. The academic year begins towards the end of August and ends at the end of June. To effectively monitor student growth and development, elementary schools provide comprehensive reports twice a year—at the conclusion of Semester 1 and Semester 2—offering detailed insights into student achievement, skills development, and academic progress.</div>
                    </div>
                 <?php endif; ?>
            </div>
            <div class="attendance">
                <div class='titleBgComm mt0'>
                    <?php echo Yii::t('principal', 'Attendance Summary') ?>
                    <span class='bg'></span>
                </div>
                <table class="custom-table">
                    <tbody>
                        <tr>
                            <th rowspan="2"><?php echo Yii::t('principal', 'Total School Days') ?> (<?php echo($data['calendar_schooldays'][1]+$data['calendar_schooldays'][2]); ?>)</th>
                            <?php foreach ($data['reportSemester'] as $keyItem=>$items):?>
                                <th colspan="2">
                                    <?php echo $items ?> (<?php echo $data['calendar_schooldays'][$keyItem] ?>)
                                </th>
                            <?php endforeach;?>
                        </tr>
                        <tr>
                            <th><?php echo Yii::t('principal', 'Days') ?></th>
                            <th><?php echo Yii::t('principal', 'Percentage') ?></th>
                            <th><?php echo Yii::t('principal', 'Days') ?></th>
                            <th><?php echo Yii::t('principal', 'Percentage') ?></th>
                        </tr>
                        <tr class="text_center">
                        <td><?php echo Yii::t('principal', 'Present') ?></td>
                        <?php foreach ($data['reportSemester'] as $keyItem=>$items):?>
                            <td >
                                <?php if ($data['report_attendance'][$keyItem]): ?>
                                    <?php echo $data['report_attendance'][$keyItem]['present'] ?>
                                <?php endif;?> 
                            </td>
                            <td>
                                <?php if ($data['report_attendance'][$keyItem]): ?>
                                    <?php $ratio = $data['report_attendance'][$keyItem]['present'] / $data['calendar_schooldays'][$keyItem]?>
                                    <?php echo  round($ratio*100,2).'%'?>
                                <?php endif;?> 
                            </td>
                        <?php endforeach;?>
                        </tr>
                        <tr class="text_center">
                        <td><?php echo Yii::t('principal', 'Tardy') ?></td>
                        <?php foreach ($data['reportSemester'] as $keyItem=>$items):?>
                            <td>
                                <?php if ($data['report_attendance'][$keyItem]): ?>
                                    <?php echo $data['report_attendance'][$keyItem]['tardy'] ?>
                                <?php endif;?> 
                            </td>
                            <td>
                                <?php if ($data['report_attendance'][$keyItem]): ?>
                                    <?php $ratio = $data['report_attendance'][$keyItem]['tardy'] / $data['calendar_schooldays'][$keyItem]?>
                                    <?php echo  round($ratio*100,2).'%'?>
                                <?php endif;?> 
                            </td>
                        <?php endforeach;?>
                        </tr>
                        <tr class="text_center">
                        <td><?php echo Yii::t('principal', 'Absent') ?></td>
                        <?php foreach ($data['reportSemester'] as $keyItem=>$items):?>
                            <td>
                                <?php if ($data['report_attendance'][$keyItem]): ?>
                                    <?php echo $data['report_attendance'][$keyItem]['absent'] ?>
                                <?php endif;?> 
                            </td>
                            <td>
                                <?php if ($data['report_attendance'][$keyItem]): ?>
                                    <?php $ratio = $data['report_attendance'][$keyItem]['absent'] / $data['calendar_schooldays'][$keyItem]?>
                                    <?php echo  round($ratio*100,2).'%'?>
                                <?php endif;?> 
                            </td>
                        <?php endforeach;?>
                        </tr>
                    </tbody>
                </table>
                <div class='titleBgComm'>
                <?php echo Yii::t('report', 'Grade-Level Proficiency') ?>
                    <span class='bg'></span>
                </div>
                <div class='attendanceText'><?php echo Yii::t('principal', 'Students are assessed against the following measures of proficiency:') ?></div>
                <div class="flex academic">
                    <?php 
                    $colorList = array('#09318F', '#009944', '#28A7E1', '#F6AB00', '#666666', '#666666', '#666666', '#666666');
                    $outerIndex = 0;
                    foreach ($optionsList as $keyItem => $items): 
                        $class = ($outerIndex % 2 === 0) ? 'mr10' : 'ml10';
                        $innerIndex = 0; 
                    ?>
                        <div class="flex1 <?php echo $class ?>">
                            <div class="title"><?php echo $items['title'] ?></div>
                            <?php foreach ($items['options'] as $option): ?>
                                <?php 
                                    $bgColor = $colorList[$innerIndex % count($colorList)];
                                    $innerIndex++;
                                ?>
                                <div class="level">
                                    <div class="levelStatus" style="background:<?php echo $bgColor ?>;">
                                        <?php echo $option['label'] ?>
                                    </div>
                                    <div class="levelText"><?php echo $option['title'] ?></div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <?php $outerIndex++; ?>
                    <?php endforeach; ?>
                </div>
                <div class='attendanceText'><span class='bold'><?php echo Yii::t('principal', 'Note: ') ?></span><?php echo Yii::t('principal', 'Please refer to the additional information and explanation towards the end of this report.') ?></div>
            </div>
            <?php foreach ($data['root'] as $list): ?>
            <div class="subject">
                <div class='titleBgComm mt0'>
                    <?php echo $list['title'] ?>
                    <span class='bg'></span>
                </div>
                <div class='teacherList'>
                    <?php foreach ($data['subjectTeacher'][$list['program']] as $item): ?>
                        <span class='teacher'>
                            <img src="https://m2.files.ivykids.cn/cloud01-file-8025768FlH-6abpVmGnERlYujyjl75USp0Z.png" alt="" style='width:14px'>
                            <span><?php echo $data['teachers'][$item]['name'] ?></span>
                        </span>
                    <?php endforeach; ?>
                </div>
                <div class='clearFix'></div>
                <?php foreach ($data['reportSemester'] as $keyItem => $items): ?>
                    <?php if ($data['report'][$keyItem] && $data['report'][$keyItem][$list['id']] && $data['report'][$keyItem][$list['id']]['memo']!="") : ?>
                    <div class='arrowTitle'>
                        <div class="grades">
                            <img src="https://m2.files.ivykids.cn/cloud01-file-8025768Fl-o_jS7_GTj8QkaYgTMSiK3X8rQ.png" alt="" style="width:18px">
                            <div class="flex1 bold"><?php echo $items ?></div>
                        </div>
                        <div class='clearFix'></div>
                        <div class='mt28'><?php echo nl2br($data['report'][$keyItem][$list['id']]['memo']); ?></div>
                    </div>
                    <?php endif;?>
                <?php endforeach; ?>
                <table class="custom-table level_tr">
                    <?php foreach ($data['subs'][$list['id']] as $keyItem => $items): ?>
                        <tbody>
                            <tr>
                                <th ><?php echo $items['title'] ?></th>
                                <?php foreach ($data['reportSemester'] as $_item):?>
                                    <th style='width:80px'>
                                        <?php echo $_item ?>
                                    </th>
                                <?php endforeach;?>
                            </tr>
                            <?php foreach ($data['items'][$items['id']] as $key => $_item):?>
                                <tr>
                                    <td> <?php echo $_item ?></td>
                                    <?php foreach ($data['reportSemester'] as $keys => $semester):
                                         $class = ($keys === 1) ? 'semester1' : 'semester2';?>
                                        <td class='<?php echo $class ?>'>
                                            <span>
                                            <?php if ($data['report'][$keys] && $data['report'][$keys][$list['id']] && $data['report'][$keys][$list['id']]['option'][$keyItem] && $data['report'][$keys][$list['id']]['option'][$keyItem][$key]) : ?>
                                                <?php echo $optionsList2[$data['report'][$keys][$list['id']]['option'][$keyItem][$key]]['label'] ?>
                                             <?php endif;?>
                                            </span>
                                        </td>
                                    <?php endforeach; ?>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    <?php endforeach; ?>
                </table>
            </div>
            <?php endforeach; ?>
            <div class="explain">
                <?php if(Yii::app()->language == 'zh_cn'):?>
                    <div class='titleBgComm'>
                        评估报告注释
                        <span class='bg'></span>
                    </div>
                    <div class='explainText'>启明星学校首要目标是让每位学生通过学习取得学业上的成功，发展人际交往能力。评估报告的目的是根据年级评定标准，反映 学生进步的信息。在语言艺术、社会学、科学等学科上，使用专业标准来评估每位学生。</div>
                    <div class='arrowTitle'>
                        <div class='flex align_items title'>
                            <img src="https://m2.files.ivykids.cn/cloud01-file-8025768Fl-o_jS7_GTj8QkaYgTMSiK3X8rQ.png" alt="" style='width:18px'>
                            <span>学业</span>
                        </div>
                        <div>小学评估报告上学业部分的成绩有四个级别反映学生的水平：起步、发展中、达标、优秀。下面的表格提供了各个水平的详细描述：</div>
                    </div>
                    <table  class='custom-table' >
                        <tr class='level_tr'>
                            <th>水平</th>
                            <th colspan='2'>说明</th>
                        </tr>
                        <tr>
                            <th rowspan='2'>E</th>
                            <td class='text_center'>优秀</td>
                            <td>学生始终轻松掌握年级标准，而且经常超过标准所要求的认知水平。学生应用并拓展年级所学的重要概念、流程和技能。 </td>
                        </tr>
                        <tr>
                            <td class='text_center'>专家</td>
                            <td>学生可以向他人展示如何运用技能并且准确地评估技能运用的有效程度（自我调整）</td>
                        </tr>
                        <tr>
                            <th rowspan='2'>P</th>
                            <td class='text_center'>达标</td>
                            <td>学生掌握了年级标准，而且处于标准所要求的认知水平。学生始终掌握重要概念、流程和技能，且应用时错误很少。</td>
                        </tr>
                        <tr>
                            <td class='text_center'>实践者</td>
                            <td>学生自信有效地运用技能（展示）</td>
                        </tr>
                        <tr>
                            <th rowspan='2'>L</th>
                            <td class='text_center'>发展中</td>
                            <td>学生掌握了一些年级标准，但没有一贯稳定地掌握和应用重要概念、流程和技能，有明显的错误。</td>
                        </tr>
                        <tr>
                            <td class='text_center'>学习者</td>
                            <td>学生模仿他人使用技能，但需要在辅助和指导下使用技能（模仿）</td>
                        </tr>
                        <tr>
                            <th rowspan='2'>N</th>
                            <td class='text_center'>起步</td>
                            <td>学生对标准理解较有限，未能达到年级标准。即使在指导和辅助下，学生的表现也不稳定。</td>
                        </tr>
                        <tr>
                            <td class='text_center'>新手</td>
                            <td>学生初次了解技能，可以观看他人操作技能（观察）</td>
                        </tr>
                        <tr>
                            <th>N/A</th>
                            <td class='text_center'>不评估</td>
                            <td>此次未评估这个标准或学习领域。 </td>
                        </tr>
                        <tr>
                            <th>N/E</th>
                            <td class='nowrap text_center'>没有证据</td>
                            <td>没有足够的学习证据，无法按照要求对学生的表现情况做出公正的评估。</td>
                        </tr>
                    </table>
                    <div class='arrowTitle'>
                        <div class='flex align_items title'>
                            <img src="https://m2.files.ivykids.cn/cloud01-file-8025768Fl-o_jS7_GTj8QkaYgTMSiK3X8rQ.png" alt="" style='width:18px'>
                            <span>非学业</span>
                        </div>
                        <div>小学评估报告上非学业部分的成绩有四个级别水平，反映了学生表现出非学业行为或技能的频率，它们分别是：很少、有时、经常、总是。下面的表格提供了各个水平的详细描述：</div>
                    </div>
                    <table  class='custom-table' >
                        <tr>
                            <th>水平</th>
                            <th colspan='2'>说明</th>
                        </tr>
                        <tr>
                            <th>C</th>
                            <td class='text_center'>总是</td>
                            <td>表明学生总是（76%-100%的情况下）表现是希望的行为。</td>
                        </tr>
                        <tr>
                            <th>O</th>
                            <td class='text_center'>经常</td>
                            <td>表明学生经常（51%-75%的情况下）表现出希望的行为。</td>
                        </tr>
                        <tr>
                            <th>S</th>
                            <td class='text_center'>有时</td>
                            <td>表明学生有时（26%-50%的情况下）表现出希望的行为。</td>
                        </tr>
                        <tr>
                            <th>R</th>
                            <td class='text_center'>很少</td>
                            <td>表明学生很少（0%-25%的情况下）表现出希望的行为。</td>
                        </tr>
                    </table>
                <?php else:?>
                    <div class='titleBgComm'>
                        Explanation of Elementary School Report
                        <span class='bg'></span>
                    </div>
                    <div class='explainText'>The purpose of this report is to give more information about each child’s progress in relation to the grade-level standards. Each grade-level assesses students on the most significant standards in the areas of language arts, social studies, and science.</div>
                    <div class='arrowTitle'>
                        <div class='flex align_items title'>
                            <img src="https://m2.files.ivykids.cn/cloud01-file-8025768Fl-o_jS7_GTj8QkaYgTMSiK3X8rQ.png" alt="" style='width:18px'>
                            <span>Academic</span>
                        </div>
                        <div>Academic scores on this report are recorded on a four-level scale that reflects students’proficiency: novice, learner, proficient, and exemplary. The following chart provides a more detailed description of each level:</div>
                    </div>
                    <table  class="custom-table" >
                        <tr class='level_tr'>
                            <th>Level</th>
                            <th colspan='2'>Description</th>
                        </tr>
                        <tr>
                            <th rowspan='2'>E</th>
                            <td class='text_center'>Exemplary </td>
                            <td >The student demonstrates mastery of the grade-level standards with ease and consistency, and often exceeds the cognitive level of the standard. The student applies and extends the key concepts, processes and skills of the grade level.</td>
                        </tr>
                        <tr>
                            <td class='text_center'>Expert </td>
                            <td >Student can show others how to use the skill and accurately assess how effectively the skill is used (self-regulation)</td>
                        </tr>
                        <tr>
                            <th rowspan='2'>P</th>
                            <td class='text_center'>Proficient</td>
                            <td>The student demonstrates mastery of grade-level standards at the cognitive level the standard is written. The student consistently grasps and applies the key concepts, processes and skills with limited errors.</td>
                            
                        </tr>
                        <tr>
                            <td class='text_center'>Practitioner</td>
                            <td>Students employ the skill confidently and effectively  (demonstration)</td>
                        </tr>
                        <tr>
                            <th rowspan='2'>L</th>
                            <td class='text_center'>Learner</td>
                            <td>The student demonstrates mastery of some grade-level standards. The student inconsistently grasps and applies the key concepts, processes and skills with significant errors. </td>
                        </tr>
                        <tr>
                            <td class='text_center'>Learner</td>
                            <td>Students copy others who use the skill and use the skill with scaffolding and guidance (emulation) </td>
                        </tr>
                        <tr>
                            <th rowspan='2'>N</th>
                            <td class='text_center'>Novice</td>
                            <td>The student has minimal understanding and does not meet grade-level standards. Performance is inconsistent, even with guidance and support. </td>
                        </tr>
                        <tr>
                            <td class='text_center'>Novice</td>
                            <td>Students are introduced to the skill, and can watch others performing it  (observation)</td>
                        </tr>
                        <tr>
                            <th>N/A</th>
                            <td class='nowrap text_center'>Not Assessed</td>
                            <td>The standards or areas of learning have not been assessed at this time.</td>
                        </tr>
                        <tr>
                            <th>N/E</th>
                            <td class='text_center'>No Evidence</td>
                            <td>Insufficient evidence of learning exists to make a fair evaluation of student performance against expectations.</td>
                        </tr>
                    </table>
                    <div class='arrowTitle'>
                        <div class='flex align_items title'>
                            <img src="https://m2.files.ivykids.cn/cloud01-file-8025768Fl-o_jS7_GTj8QkaYgTMSiK3X8rQ.png" alt="" style='width:18px'>
                            <span>Non-Academic</span>
                        </div>
                        <div>Non-academic scores on this report are recorded on a four-level scale that reflects the frequency of which students demonstrate non-academic behaviors or skills: rarely, sometimes, often, and consistently. The following chart provides a more detailed description of each level:</div>
                    </div>
                    <table  class="custom-table" >
                        <tr class='level_tr'>
                            <th>Level</th>
                            <th colspan='2'>Description</th>
                        </tr>
                        <tr>
                            <th>C</th>
                            <td class='text_center'>Consistently</td>
                            <td>Indicates that the student consistently (76%-100% of the times) displays the desired behaviors.</td>
                        </tr>
                        <tr>
                            <th>O</th>
                            <td class='text_center'>Often</td>
                            <td>Indicates that the student often (51%-75% of the times) displays the desired behaviors.</td>
                        </tr>
                        <tr>
                            <th>S</th>
                            <td class='text_center'>Sometimes</td>
                            <td>Indicates that the student occasionally (26%-50% of the time) displays the desired behaviors.</td>
                        </tr>
                        <tr>
                            <th>R</th>
                            <td class='text_center'>Rarely</td>
                            <td>Indicates that the student seldom (0%-25% of the time) displays the desired behaviors.</td>
                        </tr>
                    </table>
                <?php endif;?>
            </div>
        </div>
        <?php if($withToolBar):?>
        <div class="footerIframe">
            <iframe src="<?php echo $footerUrl; ?>" ></iframe>
        </div>
        <?php endif;?>
    </div>
</body>
</html>
