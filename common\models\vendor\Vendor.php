<?php

/**
 * This is the model class for table "ivy_vendor".
 *
 * The followings are the available columns in table 'ivy_vendor':
 * @property integer $vendor_id
 * @property string $cn_title
 * @property string $en_title
 * @property string $contact_method
 * @property integer $is_company
 * @property integer $vendor_type_id
 * @property integer $is_corporatechain
 * @property string $vendor_city
 * @property string $digfield_arr
 * @property string $website
 * @property string $linkman
 * @property string $faxemail
 * @property string $address
 * @property string $email
 * @property integer $dept_id
 * @property string $lead_time
 * @property integer $is_signed
 * @property string $intro
 * @property string $memo
 * @property integer $status
 * @property integer $update_timestamp
 * @property integer $update_user
 */
class Vendor extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_vendor';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('cn_title, contact_method, linkman, dept_id, vendor_type_id', 'required'),
			array('is_company, vendor_type_id, is_corporatechain, dept_id, is_signed, status, update_timestamp, update_user', 'numerical', 'integerOnly'=>true),
			array('cn_title, en_title', 'length', 'max'=>150),
			array('contact_method, website, linkman, address, email, lead_time', 'length', 'max'=>225),
			array('vendor_city, digfield_arr', 'length', 'max'=>255),
			array('fax', 'length', 'max'=>15),
			array('intro, memo', 'length', 'max'=>1024),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('vendor_id, cn_title, en_title, contact_method, is_company, vendor_type_id, is_corporatechain, vendor_city, digfield_arr, website, linkman, fax, address, email, dept_id, lead_time, is_signed, intro, memo, status, update_timestamp, update_user', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
        return array(
            'bankAccount' => array(self::HAS_ONE, 'VendorBankAccount', 'vendor_id')
        );
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'vendor_id' => 'Vendor',
			'cn_title' => 'Cn Title',
			'en_title' => 'En Title',
			'contact_method' => 'Contact Method',
			'is_company' => 'Is Company',
			'vendor_type_id' => 'Vendor Type',
			'is_corporatechain' => 'Is Corporatechain',
			'vendor_city' => 'Vendor City',
			'digfield_arr' => 'Digfield Arr',
			'website' => 'Website',
			'linkman' => 'Linkman',
			'fax' => 'Fax',
			'address' => 'Address',
			'email' => 'Email',
			'dept_id' => 'Dept',
			'lead_time' => 'Lead Time',
			'is_signed' => 'Is Signed',
			'intro' => 'Intro',
			'memo' => 'Memo',
			'status' => 'Status',
			'update_timestamp' => 'Update Timestamp',
			'update_user' => 'Update User',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('vendor_id',$this->vendor_id);
		$criteria->compare('cn_title',$this->cn_title,true);
		$criteria->compare('en_title',$this->en_title,true);
		$criteria->compare('contact_method',$this->contact_method,true);
		$criteria->compare('is_company',$this->is_company);
		$criteria->compare('vendor_type_id',$this->vendor_type_id);
		$criteria->compare('is_corporatechain',$this->is_corporatechain);
		$criteria->compare('vendor_city',$this->vendor_city,true);
		$criteria->compare('digfield_arr',$this->digfield_arr,true);
		$criteria->compare('website',$this->website,true);
		$criteria->compare('linkman',$this->linkman,true);
		$criteria->compare('fax',$this->fax,true);
		$criteria->compare('address',$this->address,true);
		$criteria->compare('email',$this->email,true);
		$criteria->compare('dept_id',$this->dept_id);
		$criteria->compare('lead_time',$this->lead_time,true);
		$criteria->compare('is_signed',$this->is_signed);
		$criteria->compare('intro',$this->intro,true);
		$criteria->compare('memo',$this->memo,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('update_timestamp',$this->update_timestamp);
		$criteria->compare('update_user',$this->update_user);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return Vendor the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
