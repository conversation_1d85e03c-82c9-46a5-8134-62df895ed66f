<?php

class SMSCommand extends CConsoleCommand {
    
    public $host = 'vboss.263.net';
    public $port = 15555;
    public $un = 'ivygroup.info';
    public $pw = 'OonK3YtKrY1VCp7h';
    public $businessid = 14;
    public $uid = '<EMAIL>';

    public function actionIndex()
    {
        echo $this->getHelp();
    }

    public function actionEmailTest()
    {
        $mailer = Yii::createComponent('common.extensions.mailer.Aliyun');

        $subject = '阿里云发送邮件测试';
        $mailer->Subject = $subject;
	$mailer->Body = 'test';
        $mailer->AddAddress('<EMAIL>');
        $mailer->AddCC('<EMAIL>');
        $isProduction = defined("IS_PRODUCTION") ? true : false;
        $mailer->iniMail($isProduction); // 此行代码要放到AddAddress, AddCC方法下面
        var_dump($mailer);
        $mailer->Send();
    }
    
    public function actionTest()
    {
        $fp=stream_socket_client($this->host.':'.$this->port, $errno, $errstr);
        if (!$fp){
            echo "ERROR: ".$errno."-".$errstr;
        }
        else {
            fwrite($fp, "SMSINTERFACE|0|\n");
            $_read1 = fread($fp, 64);
            
            $readArr = explode('|', $_read1);
            
            if ($readArr[0] != '+OK'){
                die('请求异常');
            }
            
            $key = md5($this->un.trim($readArr[1]).$this->pw);
            
            $xml = "SMSINTERFACE|1|";
            $params = "<id>001</id>";
            $params .= "<userid>".$this->uid."</userid>";
            $params .= "<text><![CDATA[接&教%20委通知'因'大雪４影<响>交通，《明日》]]></text>";
            //$params .= "<text><![CDATA[接&教%20委通知'因'大雪４影<响>交通，《明日》丶1月4日学校停课一天，并将于周二1月5日正常开学。对由此带来的不便我们表示歉意。艾毅双语幼儿园远洋园。 School will be closed tomorrow due to snow and open on Jan 5. Ivy OE]]></text>";
            $params .= "<mobilelist>***********</mobilelist>";
            $params .= "<key>".$key."</key>";
            $params .= "<businessid>".$this->businessid."</businessid>";
            
//            echo iconv('utf-8', 'GBK', $params)."\n";die;
            $params = base64_encode($params);
            
            fwrite($fp, $xml.$params."\n");
            $_read2 = fread($fp, 64);
            echo $_read2."\n";
        }
    }
    
}
