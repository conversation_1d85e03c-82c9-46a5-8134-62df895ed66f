<?php
$this->renderPartial('header');

$this->widget('zii.widgets.CMenu',array(
    'items'=> $pageSubMenu,
    'activeCssClass'=>'current',
    'id'=>'page-subMenu',
    'htmlOptions'=>array('class'=>'pageSubMenu')
));
echo CHtml::openTag("div", array("class"=>"c"));
echo CHtml::closeTag("div");
?>
<div class="h_a">校车</div>
<div class="prompt_text">
	<ul>
		<li>
            校车管理
        </li>
	</ul>
</div>

<div class="mb10">
    <a href="<?php echo $this->createUrl('/child/list/operations', array('category'=>'busList', 'act'=>'editBusList'));?>" title="<?php echo Yii::t("labels", "添加校车")?>" class="btn J_dialog">
        <span class="add"></span>
        <?php echo Yii::t("labels", "添加校车")?>
    </a>
</div>

<?php
$this->widget('ext.ivyCGridView.IvyCGridView', array(
    'dataProvider'=>$dataProvider,
    'id'=>'bus',
    'colgroups'=>array(
        array(
            "colwidth"=>array(100,100,100,120,null,110,100,100,100,null,150,90),
        )
    ),
    'columns'=>array(
        'bus_title',
        'bus_code',
        'bus_type',
        'acctual_children',
        'driver_name',
        'driver_mobile',
        'aunt_name',
        'aunt_mobile',
        array(
            'name'=>'state',
            'value'=>'$data->getState()',
        ),
        array(
            'name'=>'校车所属公司',
            'value'=>'CommonUtils::autoLang($data->vendor->cn_title,$data->vendor->en_title)'
        ),
        array(
            'name'=>'update_timestamp',
            'value'=>'OA::formatDateTime($data->update_timestamp, "medium", "medium")',
        ),
        array(
            'class'=>'CButtonColumn',
            'template'=>'{update}&nbsp;&nbsp;{delete}',
            'updateButtonImageUrl'=>false,
            'updateButtonUrl'=>'Yii::app()->controller->createUrl("/child/list/operations", array("category"=>"buslist", "act"=>"editBusList", "id"=>$data->bid))',
            'updateButtonOptions'=>array('class'=>'J_dialog'),
            'deleteButtonImageUrl'=>false,
            'deleteButtonUrl'=>'Yii::app()->controller->createUrl("/child/list/operations", array("category"=>"buslist", "act"=>"delBusList", "id"=>$data->bid))',
        )
    ),
));
