<div id='container' class="container-fluid"  v-cloak>
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Teaching Tasks'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('labels','Student Tags');?></li>
    </ol>
    <div class='row'>
        <div class='col-md-2' v-if='groupList.list'>
            <div v-for='(list,index) in groupList.list' class='flex group' :class='groupIndex==index?"groupEd":""'>
                <div class='flex1'  @click='getGroupItem(list,index)'  >{{list.group_name}}</div> 
                <el-dropdown trigger="click" class='pr10'>
                    <span class='el-icon-more'></span>
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item @click.native='delGroup(list,"group")' v-if='list.id!=-1'><?php echo Yii::t("global", "Delete"); ?></el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
            </div>
            <div class='mt16'  v-if='!showAddGroup'>
                <button type="button" v-if='groupList.list.length<groupList.max' class="btn btn-default  btn-block addBtn" @click='addGroup'><span class='el-icon-circle-plus-outline'></span> <?php echo Yii::t("global", "Add");?></button>
            </div>
        </div>
        <div v-if='!showAddGroup'>
            <div class='col-md-2 borderLeft overflow-y scroll-box scroll'  :style="'height:'+(modalHeight)+'px;overflow-x: hidden;'" >
                <div v-loading="itemLoading" :class='itemLoading?"mt20":""'></div>
                <div class='itemMove'>
                    <div v-for='(list,index) in groupListItemList' :key='index'  class='groupList' :class='groupItemIndex==index?"groupListEd":""'>
                            <span class='order color3'>{{list.flag_num}}</span>
                            <!-- <el-tooltip class="item" effect="dark" content='<?php echo Yii::t("campus", "Sort");?>' placement="top" v-if='list.id!=""'>
                                <span class='el-icon-rank move color3'></span>
                            </el-tooltip> -->
                            <div v-if='list.id!=""' class='pa16' @click='getGroupDetail(list,index)'>
                                <div class='flex align-items'>
                                    <div class='font14 fontBold mr5' v-if='list.name!="" && list.name!=null' :style="'color:rgb('+(list.color)+')'"  v-html='list.name'></div>
                                    <div class='flex1 font14  fontBold' :style="'color:rgb('+(list.color)+')'" >{{list.flag_text}}</div>
                                    <span class='font12 color6'>
                                        <span class='el-icon-user'></span>
                                        <span>{{list.child_num}}</span>
                                    </span>
                                </div>
                                <div class='mt8 text_over color6' v-if='list.desc!="" && list.desc!=null'>{{list.desc}}</div>
                            </div>
                            <div class='addItem' @click='editLabel(list,index,"add")' v-else>
                                <span class='el-icon-circle-plus-outline'></span> <?php echo Yii::t("global", "Add");?>
                            </div>
                        </div>
                    </div>
                </div>
            <div class='col-md-8'>
                <div v-loading="loading" :class='loading?"mt20":""'>
                    <div v-if='itemDetail.detail'>
                        <div class='flex mb16 align-items' >
                            <div class='flex1'>
                                <span class='font16 mr4 fontBold' :style="'color:rgb('+(itemDetail.detail.color)+')'" >
                                    <span class='flex1 detailIcon mr8' v-if='itemDetail.detail.name!="" && itemDetail.detail.name!=null'  v-html='itemDetail.detail.name'></span>{{itemDetail.detail.flag_text}}</span>
                                    <!--  -->
                                <span class='ml12 cur-p tag'  @click='getSchoolData(itemDetail.detail,-1,"add")'><?php echo Yii::t("labels", "Owner");?>：<span v-if='itemDetail.detail.teacher_ids!=null'>{{itemDetail.detail.teacher_ids.length}}</span><span v-else>0</span></span>
                                <span class='ml8 cur-p tag' @click='getchildList(itemDetail)' ><?php echo Yii::t("labels", "Tagged students");?>：{{itemDetail.child_num}}</span>
                                <span v-if='itemDetail.detail.is_dynamic==1' class='dynamicLabel'>动态标签</span>
                            </div>
                            <div>
                                <button type="button" class="btn btn-default"  @click='exportData(itemDetail)' v-if='itemDetail.child_list.child_list.length!=0'>导出数据</button>
                                <button class="btn btn-default" type="submit" @click='delLabel(itemDetail,"item")' v-if='itemDetail.child_list.child_list.length==0'> <?php echo Yii::t("global", "Delete"); ?></button>
                                <button type="button" class="btn btn-primary ml12" @click='editLabel(itemDetail.detail,itemDetail,"edit")'><?php echo Yii::t("global", "Edit");?></button>
                            </div>
                        </div>
                        <div class='font14 color6 word-wrap'>{{itemDetail.detail.desc}} </div>
                        <el-divider></el-divider>
                    </div>
                    <div v-if='Object.keys(childList).length!=0'>
                    <div class='mb16 font14 fontBold'>
                        <?php echo Yii::t("labels", "Students with this tag");?>  
                    </div>
                        <div class='mb16 flex'>
                            <ul class="nav nav-pills navBtn flex1" role="tablist">
                                <li role="presentation" :class="tabId=='10'?'active':''" @click='tabId=10'><a href="javascript:void(0)"><?php echo Yii::t("labels", "On the roster");?> </a></li>
                                <li role="presentation" :class="tabId=='999'?'active':''"  @click='tabId=999'><a href="javascript:void(0)"><?php echo Yii::t("child", "Dropped Out");?></a></li>
                            </ul>
                            <div v-if='Object.keys(childList[tabId]).length>0'>
                                <button type="button" class="btn btn-default mr10 btn-sm" @click='clearChild("ES")'><?php echo Yii::t("labels", "Clear All (ES)");?></button>
                                <button type="button" class="btn btn-default btn-sm" @click='clearChild("MS")'><?php echo Yii::t("labels", "Clear All (SS)");?></button>
                            </div>
                        </div>
                        <div v-if='Object.keys(childList[tabId]).length>0' class='overflow-y scroll-box' :style="'max-height:'+(modalHeight-203)+'px;overflow-x: hidden;'" >
                            <div class='' v-for='(list,key,index) in childList[tabId]'>
                                <div v-if='list.length'>                               
                                    <div class='font14 color3'>{{key}}  <span class="badge ml10">{{list.length}}</span></div>
                                    <div class='row'>
                                        <div class='col-md-4 col-lg-4'  v-for='(item,_index) in list'>
                                            <div class='flex child align-items'>
                                                <div><img :src="item.avatar" alt="" class='img32'></div>
                                                <div class='font14 color3 flex1 ml8'>
                                                   <div>{{item.name}}</div> 
                                                   <div  v-if='item.time'>
                                                       <div  v-if='item.time.start_type==1 && item.time.end_type==1'></div> 
                                                       <div class='trendsColor' v-else><span class='el-icon-time mr5'></span>{{item.time.start}}～{{item.time.end_type==1?'—':item.time.end}}</div> 
                                                    </div>
                                                </div>
                                                <div><span class='el-icon-delete cur-p font16 fontIcons' @click='delChild(item,_index,key)'></span></div>
                                            </div>
                                        </div>
                                    </div> 
                                    <el-divider v-if='index+1<Object.keys(childList[tabId]).length'></el-divider>
                                </div>
                            </div>
                        </div>
                        <div v-else>
                            <el-empty description="<?php echo Yii::t("attends", "No students");?>"></el-empty>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class='col-md-10 borderLeft' v-if='showAddGroup'>
            <div class='flex mb16 align-items'>
                <div class='flex1'>
                </div>
                <div>
                    <button class="btn btn-default" type="submit" @click='delAddGroup'><?php echo Yii::t("global", "Close");?></button>
                    <button type="button" class="btn btn-primary ml12" :disabled='disBtn' @click='saveGroup'><?php echo Yii::t("global", "Save");?></button>
                </div>
            </div>
            <!-- class='overflow-y scroll-box' :style="'max-height:'+(modalHeight)+'px;overflow-x: hidden;'" -->
            <div>
                <table class='table table-hover'>
                    <thead>
                        <tr>
                            <th  width="80" style='z-index:10'></th>
                            <th width="140"><?php echo Yii::t("labels", "Tag Icon");?></th>
                            <th><?php echo Yii::t("labels", "Tag Title");?></th>
                            <th width="80"><?php echo Yii::t("labels", "Tag Color");?></th>
                            <th><?php echo Yii::t("labels", "Description");?></th>
                            <!-- <th width="80" ><?php echo Yii::t("labels", "Owner");?></th> -->
                            <th width="60" class='text-center'><?php echo Yii::t("campus", "Sort");?></th>
                        </tr>
                    </thead>
                    <tbody class='table_count' > 
                        <tr v-for='(list,index) in tableList'>
                            <td>{{index+1}}</td>
                            <td>
                                <el-popover
                                    :ref="`popover-${index}`"
                                    placement="bottom"
                                    title="<?php echo Yii::t("labels", "Tag Icon");?>"
                                    width="166"
                                    trigger="click"
                                    v-model="tipVisibles[index]"
                                >
                                <div>
                                    <span v-for="(item,idx) in iconList" v-html='item' @click='list.name=item;popoverHide(index)' class='iconList'></span>
                                </div>
                                    <div slot="reference" size='small' class='popoverBtn' style='width:115px' >
                                        <span class='flex iconBtn'>
                                            <span v-html='list.name'v-if='list.name!=""' class='flex1'></span>
                                            <span class='flex1 select' v-else><?php echo Yii::t("global", "Please Select");?></span> 
                                            <span class='el-icon-arrow-down' v-if='list.name==""'></span>
                                            <span class='el-icon-circle-close font14 color9' v-if='list.name!=""' @click.stop='list.name=""'></span>
                                        </span>
                                    </div>
                                </el-popover>
                            </td>
                            <td>
                                <el-input v-model="list.flag_text"  size="small" placeholder='<?php echo Yii::t("teaching", "Input");?>'></el-input>
                            </td>
                            <td>
                                <el-color-picker v-model="list.color"  size="mini" color-format='rgb'></el-color-picker>
                            </td>
                            <td>
                                <el-input v-model="list.desc"  size="small" placeholder="<?php echo Yii::t("teaching", "Input");?>"  type="textarea" :autosize="{ minRows: 1, maxRows: 2}"></el-input>
                            </td>
                            <!-- <td>
                                <span class='coloeBlue cur-p' @click='getSchoolData(list,index)'>{{list.teacher_ids.length}} 人</span>
                            </td> -->
                            <td class='text-center'><span class='el-icon-rank cur-p handle'></span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
  
    <div class="modal fade" id='editModal' tabindex="-1" role="dialog" data-backdrop="static" data-keyboard="false" data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">{{editLabelData.id!=0?"<?php echo Yii::t("global", "Edit");?>":"<?php echo Yii::t("global", "Add");?>"}}</h4>
            </div>
            <div class="modal-body" v-if='Object.keys(editLabelData).length!=0'>
                <div v-if='editLabelData.id!=0'>
                    <div >
                        <span class='font16 mr16 fontBold' :style="'color:rgb('+(editLabelData.color)+')'" >
                            <span class='flex1 detailIcon mr5' v-html='editLabelData.name' v-if='editLabelData.name!="" && editLabelData.name!=null'></span>{{editLabelData.flag_text}}</span>
                        <el-tag type="info" size="mini" class='color3'><?php echo Yii::t("labels", "Owner");?>：<span v-if='editLabelData.teacher_ids!=null'> {{editLabelData.teacher_ids.length}}</span><span v-else>0</span></el-tag>
                        <el-tag type="info" size="mini" class='color3 ml8' v-if='itemDetail.child_list'><?php echo Yii::t("labels", "Tagged students");?>：{{itemDetail.child_list.child_list.length}}</el-tag>
                    </div>
                    <div class='font14 color6 mt15 word-wrap'>{{editLabelData.desc}} </div>
                    <hr>
                </div>
                <div class='row'>
                    <div class='col-md-8'>
                        <div class='color3 font14 fontBold mb20'><?php echo Yii::t("labels", "Settings");?></div>
                        <div class="form-horizontal font14">
                            <div class="form-group">
                                <span for="inputEmail3" class="col-sm-2 control-label"><?php echo Yii::t("leave", "Title");?></span>
                                <div class="col-sm-10">
                                    <div class='flex'>   
                                        <el-popover
                                            placement="bottom"
                                            title="<?php echo Yii::t("labels", "Tag Icon");?>"
                                            width="166"
                                            trigger="click"
                                            v-model="visible"
                                        >
                                        <div> 
                                            <span  class='iconList' @click='editLabelDataCopy.name="null";visible = false'><?php echo Yii::t("coll", "None");?></span><span v-for="(item,index) in iconList" v-html='item' @click='editLabelDataCopy.name=item;visible = false' class='iconList'></span>
                                        </div>
                                            <div slot="reference" size='small' class='popoverBtn' style='width:120px' >
                                                <span class='flex iconBtn'>
                                                    <span class='flex1'>
                                                        <span v-if='editLabelDataCopy.name=="null"' class=''><?php echo Yii::t("coll", "None");?></span>
                                                        <span v-html='editLabelDataCopy.name' v-else-if='editLabelDataCopy.name!=""' class=''></span>
                                                        <span class=' select' v-else><?php echo Yii::t("labels", "Tag Icon");?></span> 
                                                    </span>
                                                    <span class='el-icon-arrow-down' v-if='editLabelDataCopy.name==""'></span>
                                                    <span class='el-icon-circle-close font14 color9' v-if='editLabelDataCopy.name!=""' @click.stop='editLabelDataCopy.name=""'></span>
                                                </span>
                                            </div>
                                        </el-popover>
                                        <el-input v-model="editLabelDataCopy.flag_text" clearable class='flex1 ml10 mr10' size="small" placeholder="<?php echo Yii::t("leave", "Title");?>"></el-input>
                                        <el-tooltip class="item" effect="dark" content="<?php echo Yii::t("labels", "Color");?>" placement="top">
                                            <el-color-picker v-model="editLabelDataCopy.color"  size="small" color-format='rgb'></el-color-picker>
                                        </el-tooltip>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <span  class="col-sm-2 control-label"><?php echo Yii::t("labels", "Title");?></span>
                                <div class="col-sm-10">
                                    <div class="checkbox mb5">
                                        <label>
                                            <input type="checkbox" value="" v-model='editLabelDataCopy.hide_desc'>
                                            <span class='color6'><?php echo Yii::t("labels", "Enable Privacy (Title won't be displayed when hover)");?></span>
                                        </label>
                                    </div>
                                    <el-input v-model="editLabelDataCopy.desc" type="textarea"  :rows="3" size="small" placeholder="<?php echo Yii::t("teaching", "Input");?>"></el-input>
                                </div>
                            </div>
                            <div class="form-group">
                                <span  class="col-sm-2 control-label"><?php echo Yii::t("labels", "Description");?></span>
                                <div class="col-sm-10">
                                    <div class="mb5" style='color:#F0AD4E'>
                                        <span class='el-icon-info'></span>
                                        <span class='ml10'><?php echo Yii::t("labels", "Description is only visible in administrative page");?></span>
                                    </div>
                                    <el-input v-model="editLabelDataCopy.desc2" type="textarea"  :rows="3" size="small" placeholder="<?php echo Yii::t("teaching", "Input");?>"></el-input>
                                </div>
                            </div>

                            <div class="form-group">
                                <span  class="col-sm-2 control-label"><?php echo Yii::t("labels", "Dynamic");?></span>
                                <div class="col-sm-10 mt8">
                                    <el-switch
                                    v-model="editLabelDataCopy.is_dynamic_Switch"
                                    @change='dynamic_Switch'
                                    active-color="#428bca">
                                    </el-switch>
                                    <?php echo Yii::t("newDS", "Open");?>
                                    <span class='font14 mb12 ml16' style='color:#F0AD4E'>
                                        <span class='el-icon-info'></span>
                                        <span class='ml10'><?php echo Yii::t("labels", "Dynamic tags can be set based on scheduling");?></span>
                                    </span>
                                </div>
                            </div>
                            <div class='color3 font14 fontBold pt24 mb20'><?php echo Yii::t("coll", "Others");?></div>
                            <div class="form-group">
                                <span  class="col-sm-2 control-label"><?php echo Yii::t("labels", "Owner");?></span>
                                <div class="col-sm-10 mt8">
                                    <span class='coloeBlue numHover cur-p font14' @click='getSchoolData(editLabelDataCopy,-1)'><span v-if='editLabelDataCopy.teacher_ids!=null'>{{editLabelDataCopy.teacher_ids.length}}</span><span v-else>0</span> </span>
                                    <span class='font14 ml20' style='color:#F0AD4E'><span class='el-icon-info'></span><span class='ml10'><?php echo Yii::t("labels", "Who can add or remove this tag to students");?></span></span>
                                </div>
                            </div>
                            <div class="form-group">
                                <span  class="col-sm-2 control-label"><?php echo Yii::t("labels", "Student");?></span>
                                <div class="col-sm-10 mt8">
                                    <span class='coloeBlue numHover cur-p font14' @click='getchildList()'><span v-if='childListCopy.length!=0'>{{childListCopy.length}}</span><span v-else>0</span> </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class='col-md-4'>
                        <div class='color3 font14 fontBold  mb20'><?php echo Yii::t("teaching", "Preview");?></div>
                        <div class='boxPreview' >
                            <div >    
                                <div v-if='!editLabelDataCopy.hide_desc'>                   
                                    <div class='el-tooltip__popper is-dark showooltip' v-if='editLabelDataCopy.desc!=""'>
                                        {{editLabelDataCopy.desc}}
                                        <div x-arrow="" class="popper__arrow" ></div>
                                    </div>
                                </div>    
                                <div v-else>
                                    <div class='el-tooltip__popper is-dark showooltip' v-if='editLabelDataCopy.desc!=""'>
                                        <?php echo Yii::t("labels", "Protected Tag Info");?>
                                        <div x-arrow="" class="popper__arrow" ></div>
                                    </div>
                                </div>
                                <div>
                                    <span :style="'color:'+(editLabelDataCopy.color)+''"><span v-if='editLabelDataCopy.name!="null"' v-html='editLabelDataCopy.name'></span><span class='font16 ml5' v-if='editLabelDataCopy.flag_text!=""'>{{editLabelDataCopy.flag_text}}</span> </span> 
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                <button type="button" class="btn btn-primary" @click='saveEdit("edit","label")'><?php echo Yii::t("global", "Save");?></button>
            </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
    <!-- 添加老师 -->
    <div class="modal fade" id="addStaffModal" tabindex="-1" role="dialog" data-backdrop="static" data-keyboard="false" data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t("labels", "Select a Owner");?><span class='color6 font12 ml16'><?php echo Yii::t("labels", "Who can add or remove this tag to students");?></span></h4>
                </div>
                <div class="modal-body relative" style='padding:0'>
                    <span class='borderLeftpos'></span>
                    <div style='max-height:600px;' class='p24 row'>
                        <div class='col-md-6 col-sm-6'>
                            <div>
                                <el-select v-model="school_id"  size='small' style='width:100%' placeholder="<?php echo Yii::t('teaching', 'Please select') ?>" @change='getSchoolData()'>
                                    <el-option
                                        v-for="(item,key,index) in schoolList"
                                        :key="key"
                                        :label="item.title"
                                        :value="key">
                                    </el-option>
                                </el-select>
                            </div>
                            <div class='mt10'>
                                <el-input
                                size='small'
                                placeholder="<?php echo Yii::t('global', 'Search') ?>"
                                v-model='searchText' 
                                clearable>
                                </el-input>
                            </div>
                            <div  class="tab-pane active mt15 scroll-box" id="class" v-if='searchText==""'  style='max-height:460px;overflow-y:auto'>
                                <div v-for='(list,index) in currentDept.list' class='relative mb16'>
                                    <p  @click='showDepName(list)'>
                                        <span  class='font14 color606 cur-p'>{{list.dep_name}} </span>
                                        <span class='el-icon-arrow-down ml5' v-if='dep_name!=list.dep_name'></span>
                                        <span class='el-icon-arrow-up ml5' v-else></span>
                                    </p>
                                    <div  class='border scroll-box mr10 childList' v-if='dep_name==list.dep_name'>
                                        <div class="flex align-items listMedia" v-for='(item,key,idx) in list.user'>
                                            <div class='flex flex1' v-if='currentDept.user_info[item.uid]'>
                                                <img :src="currentDept.user_info[item.uid].photoUrl" data-holder-rendered="true" class="media-object img-circle img42">
                                                <div class="flex1 ml10 flex1Text">
                                                    <div class=" font14 mt2 color3 text_overflow">{{currentDept.user_info[item.uid].name}}</div>
                                                    <div class="font12 color6 text_overflow">{{currentDept.user_info[item.uid].hrPosition}}</div>
                                                </div>
                                            </div>
                                            <div >
                                                <span class='cur-p font16 text-primary  el-icon-circle-plus-outline' v-if='!currentDept.user_info[item.uid].disabled' @click='assignStaff(item,index,idx)'></span>
                                                <span v-else><?php echo Yii::t('directMessage', 'selected') ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else>
                                <div v-if='searchStaffList.length!=0' class='mt24 scroll-box'  style='max-height:460px;overflow-y:auto'>  
                                    <div class="flex align-items listMedia" v-for='(item,idx) in searchStaffList'>
                                        <div class='flex flex1' >
                                            <img :src="item.photoUrl" data-holder-rendered="true" class="media-object img-circle img42">
                                            <div class="flex1 ml10 flex1Text">
                                                <div class=" font14 mt2 color3 text_overflow">{{item.name}}</div>
                                                <div class="font12 color6 text_overflow">{{item.hrPosition}}</div>
                                            </div>
                                        </div>
                                        <div >
                                            <span class='cur-p font16 text-primary  el-icon-circle-plus-outline' v-if='!currentDept.user_info[item.uid].disabled' @click='assignStaff(item,idx,"search")'></span>
                                            <span v-else><?php echo Yii::t('directMessage', 'selected') ?></span>
                                        </div>
                                    </div> 
                                </div>
                                <div v-else-if='searchText!=""'>
                                        <div class='font14 color6 text-center mt20'><?php echo Yii::t('ptc', 'No Data)') ?></div>    
                                </div>
                            </div>
                        </div>
                        <div class='col-md-6 col-sm-6 borderLeft'>
                            <p class='mt10 font14 color6'>
                            {{translate("<?php echo Yii::t('labels', '%s owner(s) selected'); ?>", staffSelected.length)}}
                                <!-- <button class="btn btn-link pull-right btn-xs font14" v-if='staffSelected.length!=0' type="button" @click='batchDel("modal")'><?php echo Yii::t("newDS", "Clear All");?></button> -->
                            </p>
                            <div class='scroll-box p10 overflow-y' style='height:500px'>
                                <div class="flex align-items listMedia" v-for='(item,idx) in staffSelected'>
                                    <div class='flex flex1' v-if='allDept[item]'>
                                        <img :src="allDept[item].photoUrl" data-holder-rendered="true" class="media-object img-circle img42">
                                        <div class="flex1 ml10 flex1Text">
                                            <div class=" font14 mt2 color3 text_overflow">{{allDept[item].name}}</div>
                                            <div class="font12 color6 text_overflow">{{allDept[item].hrPosition}}</div>
                                        </div>
                                    </div>
                                    <div @click='Unassign(item,idx)'>
                                        <span class='closeChild cur-p mt10 font16 el-icon-circle-close'></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class='clearfix'></div>
                    </div>
                </div> 
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel");?></button>
                    <button type="button" class="btn btn-primary"  @click='confirmSatff()'><?php echo Yii::t("global", "OK");?></button>
                </div>
            </div>
        </div>
    </div>
     <!-- 选择学生 -->
     <div class="modal fade" id="addClassModal" tabindex="-1" role="dialog" data-backdrop="static" data-keyboard="false" >
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t("referral", "Student"); ?></h4>
                </div>
                <div class="modal-body p0 relative">
                    <span class='borderLeftpos'></span>
                    <div style='max-height:600px;' class=' row'>
                        <div class='col-md-6 col-sm-6'>
                            <!-- <div>
                                <el-input
                                placeholder="<?php echo Yii::t("global", "Search"); ?>"
                                v-model='searchText' 
                                clearable>
                                </el-input>
                            </div> -->
                            <div  class="tab-pane active mt15 scroll-box" id="class" v-if='searchText==""'  style='max-height:500px;overflow-y:auto'>
                                <div v-for='(list,index) in classList' class='relative mb16'>
                                    <p  @click='getChild(list)'>
                                        <span  class='font14 color606 cur-p'>{{list.title}} </span>
                                        <span class='el-icon-arrow-down ml5' v-if='classId!=list.classid'></span>
                                        <span class='el-icon-arrow-up ml5' v-else></span>
                                    </p>
                                    <p  class='allCheck' v-if='classId==list.classid'><button class="btn btn-default pull-right btn-xs" type="button" @click='selectAll(list,index)'  v-if='list.childData && list.childData.length!=0'><?php echo Yii::t("global", "Select All");?></button></p>
                                    <div  class='border scroll-box mr10 childList' v-if='classId==list.classid'>
                                        <div v-if='!childLoading'>
                                            <div class='' v-if='list.childData && list.childData.length!=0'>
                                                <div class="media mt10 listMedia" v-for='(item,idx) in list.childData'>
                                                    <div class="media-left pull-left media-middle">
                                                        <a href="javascript:void(0)">
                                                            <img :src="item.avatar" data-holder-rendered="true" class="media-object img-circle img42">
                                                        </a>
                                                    </div>
                                                    <div v-if='item.stuLoading'>
                                                        <div v-if='item.disabled' class="media-right pull-right text-muted lineHeight mt12">
                                                            <span class='cur-p mt10 color9'><?php echo Yii::t("newDS", "Subscribed");?></span>
                                                        </div>
                                                        <div v-else class="media-right pull-right text-muted" @click='assignChildren(item,index,idx)'>
                                                            <span class='cur-p font16 bluebg mt12 el-icon-circle-plus-outline'></span>
                                                        </div>
                                                    </div>
                                                    <div class='childLoading' v-else>
                                                        <span></span>
                                                    </div>
                                                    <div class="media-body media-middle">
                                                        <div class=" font14 mt12 color3 text_overflow">{{item.name}}</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div v-else>
                                                <div class='font12 text-muted text-center'><?php echo Yii::t("newDS", "no student in this class");?></div>
                                            </div>
                                        </div>
                                        <div class='loading' v-else>
                                            <span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else>
                                <div v-if='searchChildList.length!=0'  class='scroll-box'   style='max-height:500px;overflow-y:auto'>                               
                                    <div class="media mt10 listMedia" v-for='(item,idx) in searchChildList'>
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                                <img :src="item.avatar" data-holder-rendered="true" class="media-object img-circle img42">
                                            </a>
                                        </div>
                                        <div v-if='item.disabled' class="media-right pull-right text-muted lineHeight mt12">
                                            <span class='cur-p mt10'><?php echo Yii::t("newDS", "Subscribed");?></span>
                                        </div>
                                        <div v-else class="media-right pull-right text-muted" @click='assignChildren(item,idx,"search")'>
                                            <span class='cur-p font16 bluebg mt12 el-icon-circle-plus-outline'></span>
                                        </div>
                                        <div class="media-body media-middle">
                                            <h4 class="media-heading font14 color3">{{item.name}}</h4>
                                            <div class="text-muted color6">{{item.className}}</div>
                                        </div>
                                    </div>
                                </div>
                                <div v-else-if='searchText!=""'>
                                        <div class='font14 color6 text-center mt20'><?php echo Yii::t("ptc", "No Data"); ?></div>    
                                </div>
                            </div>
                        </div>
                        <div class='col-md-6 col-sm-6 borderLeft'>
                            <p class='mt10 font14 color606'>
                            <?php echo Yii::t("labels", "Tagged students");?>：{{childSelected.length}}
                            <!-- <?php echo Yii::t("newDS", " ");?>{{childSelected.length}}<?php echo Yii::t("newDS", " student(s) selected");?> -->
                                <button class="btn btn-link pull-right btn-xs font14" v-if='childSelected.length!=0' type="button" @click='batchDel("modal")'><?php echo Yii::t("directMessage", "Clear All");?></button>
                            </p>
                            <div class='scroll-box p10 overflow-y' style='height:500px'>
                                <div class="media m0 listMedia" v-for='(list,index) in childSelected'>
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)">
                                            <img :src="list.avatar" data-holder-rendered="true" class="media-object img-circle img42">
                                        </a>
                                    </div>
                                    <div class="media-right pull-right text-muted" @click='childUnassign(list,index)'>
                                        <span class='closeChild cur-p mt10 font16 el-icon-circle-close'></span>
                                    </div>
                                    <div class="media-body media-middle">
                                        <div class="font14 color3 mt4">{{list.name}} <span v-if='list.status==999' class='font12 color9 ml5'><?php echo Yii::t("labels", "(Dropped Out)");?></span></div>
                                        <div class="text-muted color6">{{list.className}}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class='clearfix'></div>
                    </div>
                </div> 
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" @click='confirmChild()'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id='delGroupModal' tabindex="-1" role="dialog">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title"><?php echo Yii::t("global", "Close");?></h4>
            </div>
            <div class="modal-body" >
                取消将不保存当前填写的数据，是否确认取消？
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                <button type="button" class="btn btn-primary" @click='confirmDelAdd()'><?php echo Yii::t("message", "OK");?></button>
            </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
    <div class="modal fade" id='delModal'  tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">
                    <span v-if='delType=="clear"'>
                        <span v-if='delData=="ES"'><?php echo Yii::t("labels", "Clear All (ES)");?></span> 
                        <span v-if='delData=="MS"'><?php echo Yii::t("labels", "Clear All (SS)");?></span> 
                    </span> 
                    <div v-else-if='delType=="label"'>
                        <?php echo Yii::t("newDS", "Warning");?>
                    </div>
                    <span v-else><?php echo Yii::t("global", "Delete"); ?></span> 
                </h4>
            </div>
            <div class="modal-body" >
                <div v-if='delType=="clear"'>
                    <?php echo Yii::t("labels", "Are you sure to claer all?");?>
                </div>
                <div v-else-if='delType=="label"'>
                    有其他校园正在使用这个动态标签，是否确认操作
                </div>
                <div v-else>
                    <?php echo Yii::t("directMessage", "Proceed to remove?");?> 
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                <button type="button" class="btn btn-primary" @click='delGroup(delData,"del")' v-if='delType=="group"'><?php echo Yii::t("message", "OK");?></button>
                <button type="button" class="btn btn-primary" @click='delLabel(delData,"del")' v-if='delType=="item"'><?php echo Yii::t("message", "OK");?></button>
                <button type="button" class="btn btn-primary" @click='delChild(delData,delIndex,delKey,"del")' v-if='delType=="delChild"'><?php echo Yii::t("message", "OK");?></button>
                <button type="button" class="btn btn-primary" @click='clearChild(delData,"del")' v-if='delType=="clear"'><?php echo Yii::t("message", "OK");?></button>
                <!-- <button type="button" class="btn btn-primary" @click='saveEdit("edit")' v-if='delType=="label"'><?php echo Yii::t("message", "OK");?></button> -->
                <button type="button" class="btn btn-primary" @click='saveSwitch()' v-if='delType=="label"'><?php echo Yii::t("message", "OK");?></button>
            </div>
            </div>
        </div>
        </div>
    </div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    $(document).ready(function () {
        var modalLen = $(".modal-backdrop").length,
          zIndex = $(".modal-backdrop").eq(0).css("z-index");
      for(var i = 1; i < modalLen; i ++){
        $(".modal-backdrop").eq(i).css({
          "z-index": zIndex + i * 10 + 1
        });
        $(".modal.in").eq(i).css({
          "z-index": zIndex + (i + 1) * 10 + 1
        });
      }
    });
    var height=document.documentElement.clientHeight;
    var container=new Vue({
        el: "#container",
        data: {
            modalHeight:height-190,
            color2:null,
            tableList: [],
            groupList:{},
            groupListItem:{},
            groupListItemList:[],
            showAddGroup:false,
            sortListData:[],
            iconList:[],
            school_id:'',
            schoolList:[],
            searchText:'',
            currentDept:{},
            staffSelected:[],
            allDept:{},
            dep_name:'',
            currentTeacher:{},
            currentTeacherIndex:'',
            itemDetail:{},
            groupItemIndex:-1,
            groupItemList:{},
            groupIndex:-1,
            childList:{},
            tabId:'',
            loading:false,
            itemLoading:false,
            editLabelData:{},
            editLabelDataCopy:{},
            currentGroupList:{},
            delData:{},
            delType:'',
            tipVisibles:[],
            visible: false,
            disBtn:false,
            editLabelIndex:-1,
            addChildType:'',
            searchText:'',
            classList:[],
            classId:'',
            childSelected:[],
            searchChildList:[],
            addOwner:false,
            addChild:false,
            childListCopy:[],
            delIndex:'',
            delKey:'',
            showooltip:true
        },
        created: function() {
            this.getGroup()
            this.getSchool()
            this.getClass()
            let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getIcon") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           that.iconList=data.data
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {},
                })
        },
        watch:{
           
        },
        computed: {
            searchStaffList: function() {
                var search = this.searchText;
                var searchVal = ''; //搜索后的数据
                if(search) {
                    searchVal =Object.values(this.currentDept.user_info).filter(function(product) {
                        return Object.keys(product).some(function(key) {
                            return String(product['name'].toLowerCase()).indexOf(search.toLowerCase()) !== -1;
                        })
                    })
                    return searchVal;
                }
                return this.searchStaffList;
            },
        },
        methods: {
            popoverHide(index){
               Vue.set(this.tipVisibles, index, false);
            },
            translate(text, ...args) {
                return text.replace(/%s/g, () => args.shift());
            },
            
            getGroup(init){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getGroup") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           that.groupList=data.data
                           if(init){
                                that.getGroupItem(data.data.list[data.data.list.length-1],data.data.list.length-1)
                            }else{

                                that.getGroupItem(data.data.list[0],0)
                            }
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {},
                })
            },
            delGroup(list,type){
                this.delType='group'
                if(type!='del'){
                    this.delData=list
                    $('#delModal').modal('show')
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("delGroup") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        group_id:this.delData.id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           that.getGroup()
                           $('#delModal').modal('hide')
                           resultTip({
                                msg: data.state
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {},
                })
            },
            delAddGroup(){
                var flag = true;
                for(var i=0;i<this.tableList.length;i++){
                   if(this.tableList[i].flag_text!='' || this.tableList[i].name!=''|| this.tableList[i].desc!=''|| this.tableList[i].color!=''|| this.tableList[i].teacher_ids.length!=0){
                    flag = false;
                    //只要flag,跳出循环
                    break;
                   }
                }
                if(!flag){
                    $('#delGroupModal').modal('show')
                }else{
                    this.confirmDelAdd()
                }
            },
            confirmDelAdd(){
                this.groupList.list.splice(this.groupList.list.length - 1, 1); 
                $('#delGroupModal').modal('hide')
                this.showAddGroup=false
                if(this.groupIndex<this.groupList.list.length){
                    this.getGroupItem(this.currentGroupList,this.groupIndex)
                }
            },
            addGroup(){
                this.groupList.list.push({
                    group_name:'Group '+ (this.groupList.list.length+1),
                    id:'-1'
                })
                this.showAddGroup=true
                this.groupIndex=this.groupList.list.length-1
                this.tableList=[]
                var listData=[]
                for(var i=0;i<64;i++){
                    this.tableList.push({
                        flag_text:'',
                        name:'',
                        desc:'',
                        color:'',
                        teacher_ids:[]
                    })
                }
                var that=this
                that.$nextTick(function () {
                    const el = document.querySelector('.table_count')
                    new Sortable(el, {
                        animation: 150,
                        handle: '.handle',
                        ghostClass: 'blue-background-class',
                        onEnd: function ({ newIndex, oldIndex }) { //拖拽完毕之后发生该事件
                            var list=JSON.parse( JSON.stringify (that.tableList))
                            list.splice(newIndex, 0, list.splice(oldIndex, 1)[0])
                            var newArray = list.slice(0)
                            that.tableList = []
                            that.$nextTick(function () {
                                that.tableList = newArray
                            })
                        }
                    });
                })
            },
            saveGroup(){
                let that=this
                let data={
                    item:this.tableList,
                    group_name:'Group '+ (that.groupList.list.length)
                }
                this.disBtn=true
                $.ajax({
                    url: '<?php echo $this->createUrl("addGroup") ?>',
                    type: "post",
                    dataType: 'json',
                    data:data,
                    success: function(data) {
                        if (data.state == 'success') {
                          that.getGroup('add')
                          that.showAddGroup=false
                           resultTip({
                                msg: data.state
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.disBtn=false
                    },
                    error: function(data) {
                        that.disBtn=false
                    },
                })
            },
            getSchool(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("schoolList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.schoolList=data.data.list
                            that.school_id=data.data.school_id
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            getSchoolData(list,index,type){
                let that=this
                if(type){
                    this.addOwner=true
                    this.editLabelDataCopy=JSON.parse( JSON.stringify (list))
                    this.editLabelData=JSON.parse( JSON.stringify (list))
                }else{
                    this.addOwner=false
                }
                if(list){
                    this.currentTeacher=list
                    this.currentTeacherIndex=index
                    if(index==-1){
                        that.allDept=Object.assign(that.allDept,that.itemDetail.teacher_info)
                    }
                    if(list.teacher_ids!=null &&  list.teacher_ids.length ){
                        this.staffSelected=JSON.parse( JSON.stringify (list.teacher_ids)) 
                    }else{
                        this.staffSelected=[]
                    }
                }
                this.dep_name=''
                $.ajax({
                    url: '<?php echo $this->createUrl("schoolTeacher") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        school_id:this.school_id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.currentDept=data.data
                            for(let key in that.currentDept.user_info){
                                if(that.staffSelected.indexOf(key+'')!=-1){
                                    Vue.set(that.currentDept.user_info[key], 'disabled', true);
                                }
                            }
                            that.allDept=Object.assign(that.allDept, data.data.user_info)
                            if(list){
                                $("#addStaffModal").modal('show')
                            }
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            confirmSatff(){
                if(this.currentTeacherIndex==-1){
                    Vue.set(this.editLabelDataCopy, 'teacher_ids', this.staffSelected);
                }else{
                    Vue.set(this.tableList[this.currentTeacherIndex], 'teacher_ids', this.staffSelected);
                }
                if(this.addOwner){
                    this.saveEdit('teacher')
                }
                $("#addStaffModal").modal('hide')  
            },
            
            showDepName(list){
                if(this.dep_name==list.dep_name){
                    this.dep_name=''
                    return
                }
                this.dep_name=list.dep_name
            },
            assignStaff(list,index,idx){
                if(idx=='search'){
                    this.searchStaffList[index].disabled=true
                }else{
                    Vue.set(this.currentDept.user_info[list.uid], 'disabled', true);
                }
                this.staffSelected.push(list.uid+'')
            },
            Unassign(list,index){
                if(this.currentDept.user_info[list]){
                    Vue.set(this.currentDept.user_info[list], 'disabled', false);
                }
                this.staffSelected.splice(index,1)
            },
           
            getGroupItem(list,index,type){
                let that=this
                this.groupIndex=index
                this.currentGroupList=list
                this.itemDetail={}
                this.childList={}
                if(!type){
                    this.groupItemIndex=-1
                }
                if(this.showAddGroup){
                    this.delAddGroup()
                    return
                }
                this.itemLoading=true
                this.groupListItem={}
                this.groupListItemList=[]
                $.ajax({
                    url: '<?php echo $this->createUrl("getGroupItem") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        group_id:list.id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           that.groupListItem=data.data
                           that.groupListItemList=data.data.list
                           that.itemLoading=false
                           that.$nextTick(function () {
                                var itemList=JSON.parse( JSON.stringify (that.groupListItemList))
                                const el = document.querySelector('.itemMove')
                                new Sortable(el, {
                                    animation: 150,
                                    handle: '.move',
                                    ghostClass: 'blue-background-class',
                                    onEnd: function ({
                                            newIndex,
                                            oldIndex
                                            }) { 
                                        that.groupListItemList.splice(newIndex, 0, that.groupListItemList.splice(oldIndex, 1)[0])
                                        var newArray = that.groupListItemList.slice(0)
                                        that.groupListItemList = []
                                        that.sortItem(newArray)
                                        that.$nextTick(function () {
                                            that.groupListItemList = newArray
                                        })
                                    }
                                });
                            })
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.itemLoading=false
                        }
                    },
                    error: function(data) {
                        that.itemLoading=false
                    },
                })
            },
            sortItem(newArray){
                let that=this
                var data=[]
                for(var i=0;i<newArray.length;i++){
                    data.push({id:newArray[i].id,group_id:this.currentGroupList.id})
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("labelSort") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{data:data},
                    success: function(data) {
                        if (data.state == 'success') {
                           resultTip({
                                msg: data.state
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {},
                })
            },
            getGroupDetail(list,index,type){
                this.groupItemList=list
                this.groupItemIndex=index
                let that=this
                this.loading=true
                this.itemDetail={}
                this.childList={}
                $.ajax({
                    url: '<?php echo $this->createUrl("getDetail") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        flag: list.flag,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           that.itemDetail=data.data
                           that.childList=data.data.child_list.list
                           if(type){
                            that.tabId=that.tabId
                           }else{
                            that.tabId='10'
                           }
                           that.loading=false
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.loading=false
                        }
                    },
                    error: function(data) {
                        that.loading=false
                    },
                })
            },
            delLabel(data,type){
                this.delType='item'
                if(type!='del'){
                    this.delData=data
                    $('#delModal').modal('show')
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("delLabel") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{id: this.delData.detail.id},
                    success: function(data) {
                        if (data.state == 'success') {
                            that.itemDetail={}
                            that.childList={}
                           that.getGroupItem(that.currentGroupList,that.groupIndex,'edit')
                           $('#delModal').modal('hide')
                           resultTip({
                                msg: data.state
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {},
                })
            },
            editLabel(data,itemDetail,type){
                if(type=='add'){
                    this.groupItemIndex=itemDetail
                    this.itemDetail={}
                    this.childList={}
                }
                this.editLabelDataCopy=JSON.parse( JSON.stringify (data))
                this.editLabelData=data
                if(this.editLabelData.id==''){
                    this.editLabelDataCopy.teacher_ids=[]
                    this.childListCopy=[]
                    this.editLabelDataCopy.id=0
                    Vue.set(this.editLabelDataCopy, 'is_dynamic_Switch', false);
                    this.editLabelDataCopy.hide_desc=false
                }
                if(data.color!=''){
                    this.editLabelDataCopy.color='rgb('+(data.color)+')'
                    this.editLabelData.childNum=itemDetail.child_num
                    this.editLabelDataCopy.childNum=itemDetail.child_num
                    if(this.editLabelDataCopy.teacher_ids==null){
                        this.editLabelDataCopy.teacher_ids=[]
                    }
                    if(this.editLabelDataCopy.childNum==0){
                        this.childListCopy=[]
                    }else{
                        this.childListCopy=itemDetail.child_list.child_list
                    }
                    if(this.editLabelDataCopy.name==null){
                        this.editLabelDataCopy.name=''
                    }
                    Vue.set(this.editLabelDataCopy, 'is_dynamic_Switch', data.is_dynamic==1?true:false);
                    this.editLabelDataCopy.hide_desc=this.editLabelDataCopy.hide_desc==1?true:false
                }
                $('#editModal').modal('show')
            },
            dynamic_Switch(){
                let is_dynamic=this.editLabelDataCopy.is_dynamic_Switch?1:0
                if(is_dynamic==0 && this.editLabelData.is_dynamic!=is_dynamic && this.editLabelData.otherSchoolChild.length!=0){
                    this.delType='label'
                    this.editLabelDataCopy.is_dynamic_Switch=true
                    $('#delModal').modal('show')
                }
            },
            saveSwitch(){
                this.editLabelDataCopy.is_dynamic_Switch=false
                $('#delModal').modal('hide')
            },
            saveEdit(type,save){
                let that=this
                if(this.editLabelDataCopy.name=='' && this.editLabelDataCopy.flag_text==''){
                    resultTip({
                        error: 'warning',
                        msg: '<?php echo Yii::t("leave", "Input");?> <?php echo Yii::t("leave", "Title");?>'
                    });
                    return
                }
                // if(this.editLabelDataCopy.color==''){
                //     resultTip({
                //         error: 'warning',
                //         msg: '请选择标签颜色'
                //     });
                //     return
                // }
                if(this.childListCopy.length==0){
                    this.editLabelDataCopy.child_ids=''                    
                }else{
                    this.editLabelDataCopy.child_ids=[]
                    this.childListCopy.forEach(item => {
                        this.editLabelDataCopy.child_ids.push(item.id)
                    });
                }
                let is_dynamic=this.editLabelDataCopy.is_dynamic_Switch?1:0
                // if(is_dynamic==0 && this.editLabelData.is_dynamic!=is_dynamic && this.editLabelData.otherSchoolChild.length!=0){
                //     if(save){
                //         this.delType='label'
                //         $('#delModal').modal('show')
                //         return
                //     }
                // }
                this.editLabelDataCopy.hide_desc=this.editLabelDataCopy.hide_desc?1:0
                this.editLabelDataCopy.name=this.editLabelDataCopy.name=='null'?'':this.editLabelDataCopy.name
                if(Array.isArray(this.editLabelDataCopy.teacher_ids) && this.editLabelDataCopy.teacher_ids.length==0){
                    this.editLabelDataCopy.teacher_ids=''                    
                }
                if(type=='teacher'){
                    delete this.editLabelDataCopy.child_ids
                    this.editLabelDataCopy.is_dynamic = this.itemDetail.detail.is_dynamic
                }else if(type=='child'){
                    delete this.editLabelDataCopy.teacher_ids
                    this.editLabelDataCopy.is_dynamic = this.itemDetail.detail.is_dynamic
                }else{
                    this.editLabelDataCopy.is_dynamic=this.editLabelDataCopy.is_dynamic_Switch?1:0
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("saveLabel") ?>',
                    type: "post",
                    dataType: 'json',
                    data: this.editLabelDataCopy,
                    success: function(data) {
                        if (data.state == 'success') {
                           resultTip({
                                msg: data.state
                            });
                            $('#editModal').modal('hide')
                            $('#delModal').modal('hide')
                            if(type!='teacher'){
                                that.getGroupItem(that.currentGroupList,that.groupIndex,'edit')
                            }
                            that.getGroupDetail(that.editLabelDataCopy,that.groupItemIndex)
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {},
                })
                
            },
            getchildList(list){
                this.classId=''
                this.addOwner=false
                if(list){
                    this.addChild=true
                    this.editLabelDataCopy=JSON.parse( JSON.stringify (list.detail))
                    this.childSelected=JSON.parse( JSON.stringify (list.child_list.child_list)) 
                    this.editLabelData=JSON.parse( JSON.stringify (list.detail))
                }else{
                    this.addChild=false
                    this.childSelected=JSON.parse( JSON.stringify (this.childListCopy)) 
                }
                this.childSelected=this.childSelected.sort(function(a, b) {
                    return a.status - b.status;
                });
                $('#addClassModal').modal('show')
            },
            confirmChild(){
                this.childListCopy=JSON.parse( JSON.stringify (this.childSelected))
                if(this.addChild){
                    console.log(1)
                    this.saveEdit('child')
                }
                $("#addClassModal").modal('hide')  
            },
            getClass(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("classList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            data.data.forEach(item => {
                                item.childData=[]
                            })
                            that.classList=data.data

                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            getChild(list){
                let that=this
                if(that.classId==list.classid){
                    that.classId=''
                    return
                }
                that.classId=list.classid
                var childId=[]
                if(this.childSelected!=null){
                    for(var i=0;i<this.childSelected.length;i++){
                        childId.push(this.childSelected[i].id)
                    }
                }
                for(var i=0;i<that.classList.length;i++){
                    if(that.classList[i].classid==list.classid){
                        if(that.classList[i].childData.length!=0){
                            for(var j=0;j<that.classList[i].childData.length;j++){
                                if (childId.indexOf(that.classList[i].childData[j].id)!=-1) {
                                    that.classList[i].childData[j].disabled=true
                                }else{
                                    that.classList[i].childData[j].disabled=false
                                }
                            }
                            that.$forceUpdate()
                            return
                        }
                    }
                }
                that.childLoading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("/mteaching/student/childList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        classId:list.classid
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            for(var i=0;i<data.data.length;i++){
                                data.data[i].classTitle=list.title
                                data.data[i].stuLoading=true
                                data.data[i].loading=true
                                if (childId.indexOf(data.data[i].id)!=-1) {
                                    data.data[i].disabled=true
                                }else{
                                    data.data[i].disabled=false
                                }
                            }
                            that.sortData(data.data)
                            for(var i=0;i<that.classList.length;i++){
                                if(that.classList[i].classid==list.classid){
                                    that.classList[i].childData=data.data
                                }
                            }
                            that.childLoading=false
                            that.$forceUpdate()
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            assignChildren(list,index,idx){
                if(idx=='search'){
                    this.searchChildList[index].disabled=true
                }else{
                    Vue.set(this.classList[index].childData[idx], 'disabled', true);
                }
                this.$forceUpdate()
                this.childSelected.push(list)
            },
            childUnassign(data,index,type){
                for(var i=0;i<this.classList.length;i++){
                    for(var j=0;j<this.classList[i].childData.length;j++){
                        if(data.id==this.classList[i].childData[j].id){
                            Vue.set(this.classList[i].childData[j], 'disabled', false);
                        }
                    }
                }
                for(var i=0;i<this.searchChildList.length;i++){
                    if(data.id==this.searchChildList[i].id){
                        Vue.set(this.searchChildList[i], 'disabled', false);
                    }
                }
                this.$forceUpdate()
                this.childSelected.splice(index,1)
                if(type){
                    this.getTeacher()
                }
                
            },
            batchDel(){
                this.childSelected=[]
                for(var i=0;i<this.classList.length;i++){
                    for(var j=0;j<this.classList[i].childData.length;j++){
                        this.classList[i].childData[j].disabled=false
                    }                        
                }                
                this.$forceUpdate() 
            },
            sortData(list){
                <?php if (Yii::app()->language == 'zh_cn') { ?>
                    list.sort((a, b)=> a.name.localeCompare(b.name, 'zh'));
                <?php } else { ?>
                    list.sort((a, b) => a.name.charCodeAt(0) - b.name.charCodeAt(0));
                <?php } ?>
            },
            selectAll(list,index){
                for(var i=0;i<list.childData.length;i++){
                    if (!list.childData[i].disabled) {
                        this.childSelected.push(list.childData[i])
                        Vue.set(this.classList[index].childData[i], 'disabled', true);
                    }
                }
                this.$forceUpdate()
            },
            delChild(list,index,key,type){
                this.delType='delChild'
                if(type!='del'){
                    this.delData=list
                    this.delIndex=index
                    this.delKey=key
                    $('#delModal').modal('show')
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("SaveChildLabel") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        "child_ids":[this.delData.id],
                        "label":this.itemDetail.detail.flag, 
                        "type":2
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.groupListItemList[that.groupItemIndex].child_num=that.groupListItemList[that.groupItemIndex].child_num-1
                            that.itemDetail.child_list.child_list=that.itemDetail.child_list.child_list.filter((a,i)=>{ 
                                return  a.id!=that.delData.id
                            }) 
                            that.itemDetail.child_list.list[that.tabId][that.delKey].splice(that.delIndex, 1); 
                           $('#delModal').modal('hide')
                            resultTip({
                                msg: data.message
                            });
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            clearChild(data,type){
                let that=this
                if(type!='del'){
                    this.delData=data
                    this.delType='clear'
                    $('#delModal').modal('show')
                    return
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("delDeptChildLabel") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        flag:this.groupItemList.flag,
                        dept:this.delData,
                        child_status:this.tabId,
                        school_id:this.school_id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg: data.message
                            });
                            that.getGroupItem(that.currentGroupList,that.groupIndex,'edit')
                            that.getGroupDetail(that.groupItemList,that.groupItemIndex,'del')
                            $('#delModal').modal('hide')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            $('#delModal').modal('hide')
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            exportData(itemDetail){
                console.log(itemDetail)
                const desc = itemDetail.detail.desc
                const labelTypes = itemDetail.detail.is_dynamic == 1 ? '动态标签数据' : '标签数据'
                const filename = labelTypes + '-' + desc + '.xlsx';
                const ws_name = "SheetJS";
                var exportDataArr = [];
                const list = itemDetail.child_list.child_list
                console.log(itemDetail.detail)
                console.log(itemDetail.detail.is_dynamic)
                if(itemDetail.detail.is_dynamic == 0){
                    for(let i=0;i<list.length;i++){
                        var data={
                            'ID':list[i].id,
                            '<?php echo Yii::t('global', 'Name') ?>':list[i].bilingual_name,
                            "<?php echo Yii::t('labels', 'Class') ?>":list[i].className,
                            "<?php echo Yii::t("labels",'Gender')?>":list[i].gender == 1 ? '<?php echo Yii::t("child", "Male");?>' : '<?php echo Yii::t("child", "Female");?>',
                            "<?php echo Yii::t("labels",'学生状态')?>":list[i].status == 999 ? '<?php echo Yii::t("child", "Dropped Out");?>' : '<?php echo Yii::t("labels", "On the roster");?>',
                        }
                        let medicineLen={}
                        data=Object.assign(data,medicineLen)
                        exportDataArr.push(data)
                    }
                    var xlslHeader= [
                        'ID',
                        '<?php echo Yii::t('global', 'Name') ?>',
                        '<?php echo Yii::t('labels', 'Class') ?>',
                        '<?php echo Yii::t('labels','Gender')?>',
                        '<?php echo Yii::t('labels','学生状态')?>',
                    ]
                }else{
                    //动态标签
                    for(let i=0;i<list.length;i++){
                        const labelTime = list[i].time.start_type==1 && list[i].time.end_type==1 ? '长期' : (list[i].time.start + '～' + (list[i].time.end_type==1 ? '—' : list[i].time.end))
                        var data={
                            'ID':list[i].id,
                            '<?php echo Yii::t('global', 'Name') ?>':list[i].bilingual_name,
                            "<?php echo Yii::t('labels', 'Class') ?>":list[i].className,
                            "<?php echo Yii::t("labels",'Gender')?>":list[i].gender == 1 ? '<?php echo Yii::t("child", "Male");?>' : '<?php echo Yii::t("child", "Female");?>',
                            "<?php echo Yii::t("labels",'学生状态')?>":list[i].status == 999 ? '<?php echo Yii::t("child", "Dropped Out");?>' : '<?php echo Yii::t("labels", "On the roster");?>',
                            "<?php echo Yii::t('labels','标记时间')?>":labelTime
                        }
                        let medicineLen={}
                        data=Object.assign(data,medicineLen)
                        exportDataArr.push(data)
                    }
                    var xlslHeader= [
                        'ID',
                        '<?php echo Yii::t('global', 'Name') ?>',
                        '<?php echo Yii::t('labels', 'Class') ?>',
                        '<?php echo Yii::t('labels','Gender')?>',
                        '<?php echo Yii::t('labels','学生状态')?>',
                        '<?php echo Yii::t('labels','标记时间')?>',
                    ]
                }

                var wb=XLSX.utils.json_to_sheet(exportDataArr,{
                    origin:'A1',// 从A1开始增加内容
                    header: xlslHeader
                });
                const workbook = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(workbook, wb, ws_name);
                const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                const blob = new Blob([wbout], {type: 'application/octet-stream'});
                let link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = filename;
                link.click();
                setTimeout(function() {
                    // 延时释放掉obj
                    URL.revokeObjectURL(link.href);
                    link.remove();
                }, 500);
            }
        },
       
    })

</script>
<style>
    .group{
        font-size:14px;
        color:#333333;
        align-items:center
    }
    .group .flex1{
        padding: 10px;
    }
    .group .pr10{
        padding-right: 10px;
    }
    .group span{
        display: inline-block;
        width: 20px;
        height: 20px;
        text-align: center;
        line-height: 20px;
    }
    .groupEd{
        background:rgba(77, 136, 210, 0.10) !important;
        cursor: pointer;
    }
    .group:hover span, .groupEd span{
        background:#F2F3F5;
        border-radius: 4px;
    }
    .group:hover {
        background:#F7F7F8;
        cursor: pointer;
    }
    .groupList{
        background: #FFFFFF;
        border-radius: 4px;
        border: 1px solid #E5E6EB;
        
        margin-bottom:16px;
        position: relative;
    }
    .order{
        position: absolute;
        left: 0;
        top: 0;
        background: #F2F3F5;
        padding: 0px 5px;
        display: inline-block;
        height: 14px;
        font-size: 12px;
        border-radius: 2px;
        line-height: 14px;
    }
    .groupList:hover,.groupListEd{
        cursor: pointer;
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        border: 1px solid #4D88D2;
    }
    .groupList:hover .order{
        background:rgba(77, 136, 210, 0.10)
    }
    .text_over{
        overflow:hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
    .img32{
        width: 32px;
        height: 32px;
        border-radius: 50%;
        object-fit: cover;
    }
    .child{
        background: #FAFAFA;
        border-radius: 4px;
        padding:8px;
        margin-top:12px
    }
    .fontIcons{
        display: none
    }
    .child:hover{
        background:rgba(77, 136, 210, 0.10);
        cursor: pointer;
    }
    .child:hover .fontIcons{
        display: block;
        color:rgba(77, 136, 210, 1)
    }
    .borderLeft{
        border-left:1px solid #ddd
    }
    .addBtn{
        color: #4D88D2;
        background-color: #ffffff;
        border-color: #4D88D2;
        font-size:14px
    }
    .addBtn:hover{
        background-color: #4D88D2;
        color: #fff;
        border-color: #4D88D2;
    }
    .coloeBlue{
        color: #4D88D2;
    }
    .img42{
        width: 42px;
        height: 42px;
        object-fit: cover;
        border-radius:50%
    }
    .listMedia{
        padding:8px;
        border: 1px solid #fff;
    }
    .listMedia:hover{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        border: 1px solid #4D88D2;
        cursor: pointer;
    }
    .iconList{
        display: inline-block;
        /* width: 20px;
        height: 20px; */
        text-align: center;
        line-height: 20px;
        font-size: 16px;
        background: rgba(0, 0, 0, 0.08);
        margin: 4px;
        cursor: pointer;
        border-radius:4px;
        padding: 0 2px;
    }
    .iconList .iconfont{
        font-size:16px
    }
    .pa16{
        padding:16px 20px;
    }
    .addItem{
        /* height: 50px; */
        text-align: center;
        /* line-height: 50px; */
        font-size: 14px;
        cursor: pointer;
        color: #4D88D2;
        padding:16px 20px;
    }
    .move{
        position: absolute;
        right: 0;
        top: 0;
        width: 20px;
        height: 20px;
        background: #F2F3F5;
        border-radius: 0px 4px 0px 12px;
        border: 1px solid #F2F3F5;
        text-align: center;
        line-height: 18px;
    }
    .iconfont {
        font-size:18px
    }
    .detailIcon .iconfont {
        font-size:16px !important
    }
    .el-divider--horizontal{
        margin:16px 0
    }
    .word-wrap{
        word-wrap: break-word;
        word-break: break-all;
    }
    .el-dropdown-menu__item{
        line-height:32px
    }
    .el-dropdown-menu{
        padding:5px 0;
    }
    [v-cloak]{
        display:none;
    }
    .scroll-box::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius   : 10px;
        background-color: #ccc;
        background-image: none
    }
    .scroll-box::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0);
        background   : #fff;
        border-radius: 10px;
        border:none
    }
    .table > thead > tr > th{
        border-bottom: 1px solid #dddddd;
        background: rgb(247, 247, 248);
        color: #666666;
        padding: 12px 0;
    }
    .table {
        table-layout: fixed;
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
    }
   {
        width: 100%;
        overflow: auto;
    }
    .table thead tr th {
        position: sticky;
        top:0px;
        z-index: 1;
        font-size:14px
    }
     /* 首列固定/最后一列固定*/
     .table th:first-child,
     .table td:first-child{
        position: sticky;
        left: 0px;
        right: 0px;
        z-index: 1;
        padding-left: 24px;
    }
    .control-label{
        text-align:left !important
    }
    .iconBtn{
        align-items:center;

    }
    .iconBtn .flex1{
        text-align:left;
        
    }
    .iconBtn .flex1 .iconfont {
        font-size:12px
    }
    .iconBtn .select{
        color:#b5b6b7
    }
    .navBtn li a{
        padding: 7px 13px
    }
    .modal-body{
        padding:24px
    }
    .scroll::-webkit-scrollbar-thumb {
        background-color: #fff;
    }
    .scroll:hover::-webkit-scrollbar-thumb:hover {
        background-color: #ccc;
    }
    .scroll:hover::-webkit-scrollbar-thumb:vertical {
        background-color: #ccc;
    }
    .table > tbody > tr > td{
        vertical-align: middle;
    }
    .mt2{
        margin-top:2px
    }
    .popoverBtn{
        width: 100px;
        border: 1px solid #dcdfe6;
        padding: 6px 10px;
        font-size: 12px;
        border-radius: 3px;
        cursor: pointer;
        height:31px
    }
    .tag{
        background: rgba(77, 136, 210, 0.10);
        color: #4D88D2;
        display: inline-block;
        padding: 2px 7px;
        border-radius: 2px;
    }
    .allCheck {
        position: absolute;
        right: 10px;
        top: 4px;
    }
    .border {
        border: 1px solid #E8EAED;
        border-radius: 4px;
        padding:12px
    }
    .trendsColor{
        color:#E69625;
        font-size:12px
    }
    .dynamicLabel{
        background: #F0AD4E;
        color: #fff;
        display: inline-block;
        padding: 2px 7px;
        border-radius: 2px;
        margin-left:12px
    }
    .boxPreview{
        width: 100%;
        height: 150px;
        background: rgba(77, 136, 210, 0.08);
        border: 1px dashed #4D88D2;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .showooltip{
        position: relative;
        display: inline-block;
        margin-bottom:5px;
        max-width:200px
    }
    .showooltip .popper__arrow{
        left: 50%;
        bottom: -6px;
        border-top-color: #303133;
        border-bottom-width: 0;
        margin-left: -6px;
    }
    .numHover{
        width: 25px;
        display: inline-block;
        text-align: center;
        border-radius: 2px;
    }
    .numHover:hover{
        background:#F7F7F8;       
    }
</style>