<div class="row">
    <?php
    $form = $this->beginWidget('CActiveForm', array(
        'id' => 'learning-form' . $model->id,
        'action' => $this->createUrl(
            'saveHealthInfo',
            array(
                'classid' => $this->selectedClassId,
                'semester' => $this->selectedSemester,
                'childid' => $this->selectedChildId
            )
        ),
        'htmlOptions' => array('class' => 'J_ajaxForm', 'enctype' => 'multipart/form-data'),
    ));
    ?>
    <table class="table table-bordered">
        <h3>幼儿体检信息：</h3>
        <tr>
            <td>本次身高：</td>
            <td>
                <?php echo $form->textField($formModel, 'shengao', array('class' => 'form-control')); ?>
            </td>
            <td>身高/年龄评价：</td>
            <td><?php echo $form->textField($formModel, 'shengao_nianling', array('class' => 'form-control')); ?></td>
        </tr>
        <tr>
            <td>去年同期：</td>
            <td><?php echo $oldFormModel->shengao; ?></td>
            <td>增长是否合格：</td>
            <td><?php echo $form->textField($formModel, 'shengao_zengzhang', array('class' => 'form-control')); ?></td>
        </tr>
        <tr>
            <td>本次体重：</td>
            <td><?php echo $form->textField($formModel, 'tizhong', array('class' => 'form-control')); ?></td>
            <td>体重/年龄评价：</td>
            <td><?php echo $form->textField($formModel, 'tizhong_nianling', array('class' => 'form-control')); ?></td>
        </tr>
        <tr>
            <td>去年同期：</td>
            <td><?php echo $oldFormModel->tizhong; ?></td>
            <td>增长是否合格：</td>
            <td><?php echo $form->textField($formModel, 'tizhong_zengzhang', array('class' => 'form-control')); ?></td>
        </tr>
        <tr>
            <td>本次身高/体重：</td>
            <td><?php echo $formModel->shengao . '/' . $formModel->tizhong; ?></td>
            <td>去年同期身高/体重：</td>
            <td><?php echo $oldFormModel->shengao . '/' . $oldFormModel->tizhong; ?></td>
        </tr>
        <tr>
            <td>血色素：</td>
            <td><?php echo $form->textField($formModel, 'xuesesu', array('class' => 'form-control')); ?></td>
            <td>口腔</td>
            <td><?php echo $form->textField($formModel, 'kouqiang', array('class' => 'form-control')); ?></td>
        </tr>
        <tr>
            <td>听力</td>
            <td><?php echo $form->textField($formModel, 'tingli', array('class' => 'form-control')); ?></td>
            <td>内科</td>
            <td><?php echo $form->textField($formModel, 'neike', array('class' => 'form-control')); ?></td>
        </tr>
        <tr>
            <td>视力</td>
            <td>
                左眼：<?php echo $form->textField($formModel, 'zuoyan', array('class' => 'form-control')); ?>
                右眼：<?php echo $form->textField($formModel, 'youyan', array('class' => 'form-control')); ?>
            </td>
            <td>其他：（血压） </td>
            <td><?php echo $form->textField($formModel, 'qita', array('class' => 'form-control')); ?></td>
        </tr>
    </table>
    <div class="form-group">
        <p><?php echo $form->labelEx($formModel, 'pingjia', array('class' => '')); ?></p>
        <?php echo $form->textArea($formModel, 'pingjia', array('class' => 'form-control', 'row' => 3)); ?>
    </div>
    <div class="form-group">
        <p><?php echo $form->labelEx($formModel, 'shengzhang', array('class' => '')); ?></p>
        <?php echo $form->radioButtonList($formModel, 'shengzhang', array(
            1 => '您孩子体重和身高生长正常',
            2 => '体重较同龄儿童重',
            3 => '体重较同龄儿童稍轻',
            4 => '身高生长较同龄儿童高',
            5 => '身高生长较同龄儿童稍',
        )); ?>
    </div>
    <div class="form-group">
        <p><?php echo $form->labelEx($formModel, 'xuechanggui', array('class' => '')); ?></p>
        <?php echo $form->radioButtonList($formModel, 'xuechanggui', array(
            1 => '您的孩子血常规正常',
            2 => '您孩子的血常规需要到医院做进一步的检查',
        )); ?>
    </div>
    <div class="form-group">
        <p><?php echo $form->labelEx($formModel, 'shili', array('class' => '')); ?></p>
        <?php echo $form->radioButtonList($formModel, 'shili', array(
            1 => '您的孩子视力正常',
            2 => '您孩子的视力需要到医院做进一步的检查 ',
        )); ?>
    </div>
        <div class="form-group">
        <p><?php echo $form->labelEx($formModel, 'tingli2', array('class' => '')); ?></p>
        <?php echo $form->radioButtonList($formModel, 'tingli2', array(
            1 => '您的孩子听力检查正常',
            2 => '您的孩子需要到医院做进一步的检查 ',
        )); ?>
    </div>
    <div class="form-group">
        <p><?php echo $form->labelEx($formModel, 'quchi', array('class' => '')); ?></p>
        <?php echo $form->radioButtonList($formModel, 'quchi', array(
            1 => '您的孩子无龋齿',
            2 => '您孩子有龋齿，需要到医院做进一步的检查 ',
        )); ?>
    </div>
    <div class="form-group">
        <p><?php echo $form->labelEx($formModel, 'neike2', array('class' => '')); ?></p>
        <?php echo $form->radioButtonList($formModel, 'neike2', array(
            1 => '您的孩子内科检查正常',
            2 => '您的孩子需要到医院做进一步的检查 ',
        )); ?>
    </div>

    <h3>体能测试：</h3>
    <table class="table table-bordered">
        <tr>
            <th>项目</th>
            <th>评价结果</th>
            <th>项目</th>
            <th>评价结果</th>
        </tr>
        <tr>
            <td><?php echo $form->labelEx($formModel, 'zuoweiti', array('class' => '')); ?></td>
            <td><?php echo $form->textField($formModel, 'zuoweiti', array('class' => 'form-control')); ?></td>
            <td><?php echo $form->labelEx($formModel, 'lidingtiao', array('class' => '')); ?></td>
            <td><?php echo $form->textField($formModel, 'lidingtiao', array('class' => 'form-control')); ?></td>
        </tr>
        <tr>
            <td><?php echo $form->labelEx($formModel, 'shimipao', array('class' => '')); ?></td>
            <td><?php echo $form->textField($formModel, 'shimipao', array('class' => 'form-control')); ?></td>
            <td><?php echo $form->labelEx($formModel, 'wangqiu', array('class' => '')); ?></td>
            <td><?php echo $form->textField($formModel, 'wangqiu', array('class' => 'form-control')); ?></td>
        </tr>
        <tr>
            <td><?php echo $form->labelEx($formModel, 'pinghengmu', array('class' => '')); ?></td>
            <td><?php echo $form->textField($formModel, 'pinghengmu', array('class' => 'form-control')); ?></td>
            <td><?php echo $form->labelEx($formModel, 'lianxutiao', array('class' => '')); ?> </td>
            <td><?php echo $form->textField($formModel, 'lianxutiao', array('class' => 'form-control')); ?></td>
        </tr>
    </table>
    <p>根据以上数据，反馈出宝宝以下机能发展现状：</p>
    <div class="form-group">
        <p><?php echo $form->labelEx($formModel, 'rouren', array('class' => '')); ?></p>
        <?php echo $form->radioButtonList($formModel, 'rouren', array(1 => '良好',2 => '有待提高',)); ?>
    </div>
    <div class="form-group">
        <p><?php echo $form->labelEx($formModel, 'baofa', array('class' => '')); ?></p>
        <?php echo $form->radioButtonList($formModel, 'baofa', array(1 => '良好',2 => '有待提高',)); ?>
    </div>
    <div class="form-group">
        <p><?php echo $form->labelEx($formModel, 'lingmin', array('class' => '')); ?></p>
        <?php echo $form->radioButtonList($formModel, 'lingmin', array(1 => '良好',2 => '有待提高',)); ?>
    </div>
    <div class="form-group">
        <p><?php echo $form->labelEx($formModel, 'shangzhi', array('class' => '')); ?></p>
        <?php echo $form->radioButtonList($formModel, 'shangzhi', array(1 => '良好',2 => '有待提高',)); ?>
    </div>
    <div class="form-group">
        <p><?php echo $form->labelEx($formModel, 'pingheng', array('class' => '')); ?></p>
        <?php echo $form->radioButtonList($formModel, 'pingheng', array(1 => '良好',2 => '有待提高',)); ?>
    </div>
    <div class="form-group">
        <p><?php echo $form->labelEx($formModel, 'xietiao', array('class' => '')); ?></p>
        <?php echo $form->radioButtonList($formModel, 'xietiao', array(1 => '良好',2 => '有待提高',)); ?>
    </div>
    <div>
        <button type="submit" class="btn btn-primary J_ajax_submit_btn">保存</button>
    </div>
    <?php $this->endWidget(); ?>
</div>