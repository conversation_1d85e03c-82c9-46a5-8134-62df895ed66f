<?php
class latestJournal extends CWidget{
	public $classId;
    public $yId;
    public $schoolId;
	
	public function init(){
        
	}
	
	public function run(){

        Yii::import('common.models.portfolio.*');
        Yii::import('common.models.calendar.Calendar');
        Yii::import('common.models.calendar.CalendarWeek');
        
        $childid = $this->getController()->getChildId();
        
        $criteria = new CDbCriteria();
        $criteria->compare('t.childid', $childid);
        $criteria->compare('t.stat', 20);
        $criteria->order='t.updated_timestamp desc';
        $criteria->limit=1;
        $report = NotesChild::model()->with('yInfo')->find($criteria);
        
        $childmedia = array();
            
        if ($report){
            $startyear = $report->yInfo->startyear;

            ChildMedia::setStartYear($startyear);
            ChildMediaLinks::setStartYear($startyear);

            $criteria = new CDbCriteria();
            $criteria1 = new CDbCriteria();
            $criteria->compare('t.childid', $childid);
            $criteria1->compare('t.childid', 0);
            $criteria->mergeWith($criteria1, false);
            $criteria->compare('t.classid', $report->classid);
            $criteria->compare('t.weeknum', $report->weeknumber);
            $criteria->compare('t.category', 'week');
            $criteria->order='RAND()';
            $criteria->limit=3;
            $childmedia=ChildMediaLinks::model()->with('photoInfo')->findAll($criteria);
            
            $this->render('latestjournal',array(
                'report'=>$report,
                'childmedia'=>$childmedia,
            ));
        }
	}
	
}