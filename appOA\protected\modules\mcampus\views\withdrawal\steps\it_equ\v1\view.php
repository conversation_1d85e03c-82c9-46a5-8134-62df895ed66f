<div>
    <div class='flex align-items  mb24 mt10'>
        <span class='font14 color3'>处理人：</span>
        <div class='flex1 flex align-items lib_class_handle'>
        </div>
    </div>
    <div class="radio mb16">
        <span class='notAllowed'>
            <label class="radio-inline font14 color6 lableHeight eventsNone">
                <input type="radio" name="balance" value='1'>已归还—状态完好
            </label>
        </span>
    </div>
    <div class="radio mb16">
        <span class='notAllowed'>
            <label class="radio-inline font14 color6 lableHeight eventsNone">
                <input type="radio" name="balance" value='2'>已归还—存在设备损坏
            </label>
        </span>
    </div>
    <div class="radio ">
        <span class='notAllowed'>
            <label class="radio-inline font14 color6 lableHeight eventsNone">
                <input type="radio" name="balance" value='0'>未归还
            </label>
        </span>
    </div>
    <div class='hide amountInput ml20'>
        <input type="number" min="0" step="0.01" class="form-control length_6" name="balance_amount" disabled placeholder="请输入金额" value=''>
    </div>
    <textarea class="form-control mb16 mt24" rows="3" placeholder="<?php echo Yii::t('withdrawal','Comment');?>" disabled name="remark" id='remark'  class='font14'></textarea>
    <div id='img' class='row'>
    </div>
    <div class='text-right font14 color3  mt20'><span class='submit'></span>  <span class='cur-p ml10 redColor canRevoke' onclick='cancelSubmit()'>撤销</span></div>
</div>

<script>
     lib()
    function viewImg(that){
        let url=$(that).prop('src');
        window.open(url)
    }
    $(function () {
        $('[data-toggle="tooltip"]').tooltip()
    })
     function lib(){
        let forms=childDetails.forms.find(item2 =>item2.key === 'it_equ')
        for(var key in forms.owner_info){
            if (key !== '2' && key !== '5') {
                if(forms.implementer==key && forms.data._id){
                    $('.lib_class_handle').append('<div class="flex align-items mr16 relative"><img src='+forms.owner_info[key].photoUrl+' alt="" class="avatar38"/><div class="ml8"><div class="font14 color3 ">'+forms.owner_info[key].name+'</div><div class="font12 color6">处理时间：'+forms.implementAt+'</div></div></div>')
                }else if(!forms.data._id){
                    $('.lib_class_handle').append('<div class="flex align-items mr16"><img src='+forms.owner_info[key].photoUrl+' alt="" class="img28"/><div class="font14 color3 ml8">'+forms.owner_info[key].name+'</div></div>')
                }
            }
        }
        $('#type').val(forms.key)
        if(forms.confirm_status==2){
            $('.reject').show()
            $('.rejectMemo').html(forms.reject_memo)
        }else{
            $('.reject').hide()
            $('.rejectMemo').html(forms.reject_memo)
        }
        if(forms.status=='1'){
            $('input[type="checkbox"][name="status"][value='+forms.status+']').prop('checked', true);
        }
        if(forms.data.remark!=''){
            $('#remark').val(forms.data.remark);
        }
        if(forms.data.balance){
            $('input[type="radio"][name="balance"][value='+forms.data.balance+']').prop('checked', true);
            if (forms.data.balance != '1') {
                $('.amountInput').removeClass('hide')
            }
            $('input[name="balance_amount"]').val(forms.data.balance_amount)
        }
        if(forms.data.attachments_url && forms.data.attachments_url.length!=0){
            for(let i=0;i<forms.data.attachments_url.length;i++){
                $('#img').append(
                    '<div class="col-md-4 col-sm-4 mb16">'+
                        '<div class="bgGrey flex align-items">'+
                            '<div class="flex1 nowrap text-primary font14">'+
                                '<a target="_blank" href='+forms.data.attachments_url[i].file_key+' class="flex1" style="line-height: 26px;">'+
                                    '<span class="el-icon-paperclip font16"></span>'+
                                    '<span class="ml4">'+forms.data.attachments_url[i].title+'</span>'+
                                    '<input type="hidden" name="attachments[]" value='+forms.data.attachments_url[i]._id+' id="type">'+
                                '</a>'+
                            '</div>' +
                        '</div>'+
                    '</div>'
                );
            }
        }
        if(forms.canRevoke){
            $('.canRevoke').show()
            $('.submit').html('由 '+childDetails.staffInfo[forms.implementer].name+' <?php echo Yii::t('global','Submit');?>')
        }else{
            $('.canRevoke').hide()
            $('.submit').html()
        }
    }
    function cancelSubmit(){
        container.cancelSubmitData('it_equ')
    }
</script>
<style>
    .img50{
        width: 70px;
        height: 70px;
        
    }
    .imgList{
        margin-top: 20px;
        margin-right: 20px;
        display:inline-block
    }
    .delImg{    
        position: absolute;
        right: -5px;
        top: -5px;
        font-size: 16px;
    }
    .delImg:hover{
        color:#D9534F;
        cursor: pointer;
    }
    .bgGrey {
        background: #FAFAFA;
        border-radius: 4px;
        padding: 10px;
        border: 1px solid #FAFAFA;
    }
    .nowrap {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .closeFile {
        display: none;
    }
    .bgGrey:hover .closeFile {
        display:block
    }
</style>
