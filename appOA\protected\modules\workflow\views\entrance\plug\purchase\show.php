<!-- Modal -->
<?php echo CHtml::form('/workflow/entrance/index', 'post',array('class'=>'J_ajaxForm'));?>
<div class="modal fade" id="<?php echo $data['modalNameId']?>" tabindex="-1" role="dialog" aria-labelledby="workflowModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel"><?php echo CommonUtils::autoLang($data['workflow']->definationObj->defination_name_cn,$data['workflow']->definationObj->defination_name_en);?> - <?php echo CommonUtils::autoLang($data['workflow']->nodeObj->node_name_cn,$data['workflow']->nodeObj->node_name_en);?><a href="#" id="print"> [<?php echo Yii::t('workflow', 'print'); ?>]</a></h4>
            </div>
            <div class="modal-body" id="workflow-modal-body">
                <h4><?php echo Yii::t('workflow', 'Purchase request'); ?><?php echo $data['purchaseApplication']->budget?'<span>'.Yii::t('workflow', '(within budget)').'</span>':'<span class="text-danger">'.Yii::t('workflow', '(out of budget)').'</span>';?>：</h4>
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <td><?php echo Yii::t('workflow', 'Items'); ?></td>
                            <td><?php echo Yii::t('workflow', 'Category'); ?></td>
                            <td><?php echo Yii::t('workflow', 'Unit price'); ?></td>
                            <td><?php echo Yii::t('workflow', 'Number of units'); ?></td>
                            <td><?php echo Yii::t('workflow', 'Total price'); ?></td>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($data['purchaseApplication']->applicationItem as $applicationItem) {
                            echo '<tr><td>' .$applicationItem->getLink(). '</td>';
                            echo '<td>' .$applicationItem->getType(). '</td>';
                            echo '<td class="text-right">' .$applicationItem->price. '</td>';
                            echo '<td class="text-right">' .$applicationItem->num. '</td>';
                            echo '<td class="text-right">' .$applicationItem->total_price. '</td></tr>';
                        } ?>
                        <tr><td colspan="4"><?php echo Yii::t('workflow', 'Total:'); ?></td><td class="text-right"><?php echo $data['purchaseApplication']->price;?></td></tr>
                        <tr><td colspan="5"><?php echo Yii::t('workflow', 'Attachments:'); ?></td></tr>
                        <?php foreach ($data['workflow']->getAttachmentList() as $uploadFile): ?>
                        <tr>
                            <td colspan="5"><a target="_blank" href="<?php echo $uploadFile['bigimages']; ?>"><?php echo $uploadFile['notes']; ?></a></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                <div class="retshow">
                    <h3><?php echo Yii::t('workflow', 'Comments:');?></h3>
                     <?php 
                        $processList = $data['workflow']->getWorkflowProcess();
                        foreach ($processList as $process):?>
                    <h4>
                        <em><?php echo Yii::t('workflow', 'Date submitted:'); ?> <?php echo OA::formatDateTime($process->start_time);?></em>
                        <span><?php echo $process->user->getName();?></span>
                    </h4>
                    <div class="clearfix"></div>
                    <div class="comment">
                        <label><?php echo Yii::t('workflow', 'Option:');?></label>
                        <p><?php echo nl2br(htmlspecialchars($process->process_desc));?></p>
                    </div>
                    <?php endforeach;?>
                </div>
                <?php if ($data['workflow']->useRole && $data['workflow']->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_UNOP && !$data['workflow']->isFristNode()):?>
                <div class="form-group">
                    <?php if ($data['workflow']->opertationObj->state == WorkflowOperation::WORKFLOW_STATS_UNOP) {
                        echo '<label for="">' .Yii::t('workflow','Option:'). '</label>';
                        echo CHtml::textArea('memo','',array('class'=>'form-control','row'=>3));
                        //判断商品价格是否发生变动
                        if ($data['purchaseApplication']->status == PurchaseApplication::CHANGED)
                        echo '<br><strong class="text-danger">'.Yii::t('workflow','Note: please do not approve the request as the unit price has been changed.'). '</strong>';
                    } ?>
                </div>
                <?php endif; ?>
            </div>
            <?php if ($data['workflow']->useRole && $data['workflow']->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_UNOP && !$data['workflow']->isFristNode()):?>
            <div class="modal-footer">
                <?php echo CHtml::hiddenField('definationId', $data['workflow']->definationObj->defination_id);?>
                <?php echo CHtml::hiddenField('nodeId', $data['workflow']->nodeObj->node_id);?>
                <?php echo CHtml::hiddenField('operationId', $data['workflow']->opertationObj->id);?>
                <?php echo CHtml::hiddenField('branchId', $data['workflow']->opertationObj->branchid);?>
                <?php echo CHtml::hiddenField('action', 'show');?>
                <?php echo CHtml::hiddenField('buttonCategory','');?>
                <?php if (!$data['purchaseApplication']->status == PurchaseApplication::CHANGED){ ?>
                <button type="button" class="btn btn-primary J_ajax_submit_btn" data-category="<?php echo WorkflowOperation::WORKFLOW_STATS_OPED;?>"><?php echo Yii::t('workflow','Yes');?></button>
                <?php }?>
                <button type="button" class="btn btn-danger J_ajax_submit_btn" data-category="<?php echo WorkflowOperation::WORKFLOW_STATS_OPDENY;?>"><?php echo Yii::t('workflow','No');?></button>
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php CHtml::endForm();?>

<script>
    //打印
    $(function(){  
         $("#print").click(function(){
            $("#workflow-modal-body a").removeAttr('href');
            $("#workflow-modal-body").printThis({popTitle: '采购申请单'});
         })
    }); 
</script>