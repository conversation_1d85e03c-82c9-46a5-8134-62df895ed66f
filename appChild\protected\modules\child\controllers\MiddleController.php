<?php

/**
 * Middle school info show
 */
class MiddleController extends ProtectedController{
    
    public function actionVideo(){
        $this->render('video');
    }

    public function actionSpeed()
    {
        Yii::import('common.models.attendance.*');
        $childid= Yii::app()->request->getParam('childid', '');
        $id = Yii::app()->request->getParam('id', '');
        $pid = Yii::app()->request->getParam('pid', '');
        $model = ChildProfileBasic::model()->findByPk($childid);
        Yii::app()->clientScript->registerCssFile(Yii::app()->theme->baseUrl."/css/portfolio.css?t=".Yii::app()->params['refreshAssets']);
        if($model->schoolid){
            $criteria = new CDbCriteria;
            $criteria->compare('schoolid', $model->schoolid);
            $criteria->compare('pid', $id);
            $criteria->order = 'updated';
            $inforObj = InformationFrame::model()->findAll($criteria);
            $cagegory = 0;
            if($inforObj){
                $cagegory = ($pid) ? $pid : $inforObj[0]->id;
            }
            $criteria = new CDbCriteria;
            $criteria->compare('schoolid', $model->schoolid);
            $criteria->compare('cagegory', $cagegory);
            $criteria->compare('published', 1);
            $creditList = new CActiveDataProvider('InformationDs', array(
                'criteria' => $criteria,
                'pagination' => array("pagesize" => 30),
                'sort' => array(
                    'defaultOrder'=>array(
                        'times'=>CSort::SORT_DESC,
                    )
                ),
            ));

        }
        $this->render('speeds', array(
            'depositList' => $creditList,
            'inforObj' => $inforObj,
            'id' => $id,
            'pid' => $cagegory,
        ));


    }
    
    public function actionSummary(){
        $fileName = (Yii::app()->language == 'zh_cn') ? '2-CN.swf' : '2-EN.swf';
        $this->render('summary',array(
            'fileName'=>$fileName
        ));
    }

    public function actionNewstaff()
    {
        $this->render('newstaff');
    }

    public function actionConstruction()
    {
        $this->render('construction');
    }

    public function actionSchedule()
    {
        $this->render('schedule');
    }

    public function actionPypIntro(){
        $fileName = (Yii::app()->language == 'zh_cn') ? '3-CN.swf' : '3-EN.swf';
        $this->render('pypIntro',array(
            'fileName'=>$fileName
        ));
    }    

    // 启明星的发展
    public function actionDaystarDevelopment(){
        $fileName = (Yii::app()->language == 'zh_cn') ? '4.swf' : '4.swf';
        $this->render('daystarDevelopment',array(
            'fileName'=>$fileName
        ));
    }

    // 在家支持英语教学
    public function actionSupportingEnglish(){
        $fileName = (Yii::app()->language == 'zh_cn') ? '5.swf' : '5.swf';
        $this->render('supportingEnglish',array(
            'fileName'=>$fileName
        ));
    }    

    // 启明星上学和放学流程
    public function actionWelcomeBack(){
        $fileName = (Yii::app()->language == 'zh_cn') ? '6.swf' : '6.swf';
        $this->render('welcomeBack',array(
            'fileName'=>$fileName
        ));
    }    

    // （总校长）茶话会备忘录
    public function actionCoffeeTalkMemo(){
        $fileName = (Yii::app()->language == 'zh_cn') ? '7.swf' : '7.swf';
        $this->render('coffeeTalkMemo',array(
            'fileName'=>$fileName
        ));
    }
} 

