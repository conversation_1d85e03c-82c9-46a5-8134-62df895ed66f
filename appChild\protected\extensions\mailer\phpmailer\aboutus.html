<html>
<head>
<style>
body, p, li, td {
  font-family: Arial, Helvetica, sans-serif;
  font-size: 12px;
}
ul {
  margin:0 0px 0 15px;
  padding:0; 
}
div.width {
  width: 760px;
  text-align: left;
}
</style>
<script>
<!--
var popsite="http://phpmailer.codeworxtech.com"
var withfeatures="width=960,height=760,scrollbars=1,resizable=1,toolbar=1,location=1,menubar=1,status=1,directories=0"
var once_per_session=0
function get_cookie(Name) {
  var search = Name + "="
  var returnvalue = "";
  if (document.cookie.length > 0) {
    offset = document.cookie.indexOf(search)
    if (offset != -1) { // if cookie exists
      offset += search.length
      // set index of beginning of value
      end = document.cookie.indexOf(";", offset);
      // set index of end of cookie value
      if (end == -1)
         end = document.cookie.length;
      returnvalue=unescape(document.cookie.substring(offset, end))
      }
   }
  return returnvalue;
}
function loadornot(){
  if (get_cookie('popsite')=='') {
    loadpopsite()
    document.cookie="popsite=yes"
  }
}
function loadpopsite(){
  win2=window.open(popsite,"",withfeatures)
  win2.blur()
  window.focus()
}
if (once_per_session==0) {
  loadpopsite()
} else {
  loadornot()
}
-->
</script>
</head>
<body>
<center>
<div class="width">
<hr>
The http://phpmailer.codeworxtech.com/ website now carries a few
advertisements through the Google Adsense network to help offset
some of our costs.<br />
Thanks ....<br />
<hr>
<p>PHPMailer is the world's leading email transport class and downloaded an
average of more than 26,000 each month. In March 2009, PHPMailer was downloaded
more than 31,000 times -- that's an average of 1,000 downloads daily. Our thanks
to our new users and loyal users. We understand you have many choices available
to select from and we thank you for select our fast and stable tool for your
website and projects.</p>
<p>Credits:<br>
PHPMailer's original founder is Brent Matzelle. The current team is:<br>
Project Administrator: Andy Prevost (codeworxtech),
<a href="mailto:<EMAIL>">
<EMAIL></a><br>
Author: Andy Prevost (codeworxtech) <EMAIL><br>
Author: Marcus Bointon (coolbru) <a href="mailto:<EMAIL>">
<EMAIL></a></p>
<p>PHPMailer is used in many projects ranging from Open Source to commercial
packages. Our LGPL licensing terms are very flexible and allow for including
PHPMailer to enhance projects of all types. If you discover PHPMailer being used
in a project, please let us know about it.</p>
<p><strong>WHY USE OUR TOOLS &amp; WHAT&#39;S IN IT FOR YOU?</strong></p>
<p>A valid question. We're developers too. We've been writing software, primarily for the internet, for more than 15 years. Along the way, there are two major things that had tremendous impact of our company: PHP and Open Source. PHP is without doubt the most popular platform for the internet. There has been more progress in this area of technology because of Open Source software than in any other IT segment. We have used many open source tools, some as learning tools, some as components in projects we were working on. To us, it's not about popularity ... we're committed to robust, stable, and efficient tools you can use to get your projects in your user's hands quickly. So the shorter answer: what's in it for you? rapid development and rapid deployment without fuss and with straight forward open source licensing.</p>
<p>Now, here's our team:</p>
<table width="100%" cellpadding="5" style="border-collapse: collapse" border="1">
  <tr>
    <th><b>About Andy Prevost, AKA "codeworxtech".</b></th>
    <th><b>About Marcus Bointon, AKA "coolbru".</b></th>
  </tr>
  <tr>
    <td width="50%" valign="top">
      <p><a href="http://www.codeworxtech.com">www.codeworxtech.com</a> for more information.<br>
		Web design, web applications, forms: <a href="http://www.worxstudio.com">WorxStudio.com</a><br />
      </p>
      <p>Our company, <strong>Worx International Inc.</strong>, is the publisher of several Open Source applications and developer tools as well as several commercial PHP applications. The Open Source applications are ttCMS and DCP Portal. The Open Source developer tools include QuickComponents (QuickSkin and QuickCache) and now PHPMailer.
      We have staff and offices in the United States, Caribbean, the Middle
      East, and our primary development center in Canada. Our company is represented by
      agents and resellers globally.</p>
      <p><strong>Worx International Inc.</strong> is at the forefront of developing PHP applications. Our staff are all Zend Certified university educated and experts at object oriented programming. While <strong>Worx International Inc.</strong> can handle any project from trouble shooting programs written by others all the way to finished mission-critical applications, we specialize in taking projects from inception all the way through to implementation - on budget, and on time. If you need help with your projects, we&#39;re the team to get it done right at a reasonable price.</p>
      <p>Over the years, there have been a number of tools that have been constant favorites in all of our projects. We have become the project administrators for most of these tools.</p>
      <p>Our developer tools are all Open Source. Here&#39;s a brief description:</p>
      <ul>
        <li><span style="background-color: #FFFF00"><strong>PHPMailer</strong></span>. Originally authored by Brent Matzelle, PHPMailer is the leading "email transfer class" for PHP. PHPMailer is downloaded more than 
		26000 times each and every month by developers looking for a fast, stable, simple email solution. We used it ourselves for years as our favorite tool. It&#39;s always been small (the entire footprint is 
		less than 100 Kb), stable, and as complete a solution as you can find. 
		Other tools are nowhere near as simple. Our thanks to Brent Matzelle for this superb tool - our commitment is to keep it lean, keep it focused, and compliant with standards. Visit the PHPMailer website at
        <a href="http://phpmailer.codeworxtech.com/">http://phpmailer.codeworxtech.com/</a>. <br />
        Please note: <strong>all of our focus is now on the PHPMailer for PHP5.</strong><br />
        <span style="background-color: #FFFF00">PS. While you are at it, please visit our sponsor&#39;s sites, click on their ads.
        It helps offset some of our costs.</span><br />
        Want to help? We're looking for progressive developers to join our team of volunteer professionals working on PHPMailer. Our entire focus is on PHPMailer 
		for PHP5. If you are interested, let us know.<br />
        <br />
        </li>
        <li><strong><span style="background-color: #FFFF00">QuickCache</span></strong>. Originally authored by Jean Pierre Deckers as jpCache, QuickCache is an HTTP OpCode caching strategy that works on your entire site with only one line of code at the top of your script. The cached pages can be stored as files or as database objects. The benefits are absolutely astounding: bandwidth savings of up to 80% and screen display times increased by 8 - 10x. Visit the QuickCache website at
        <a href="http://quickcache.codeworxtech.com/">http://quickcache.codeworxtech.com/</a>.<br />
        <br />
        </li>
        <li><strong><span style="background-color: #FFFF00">QuickSkin</span></strong>. Originally authored by Philipp v. Criegern and named "SmartTemplate". The project was taken over by Manuel 'EndelWar' Dalla Lana and now by "codeworxtech". QuickSkin is one of the truly outstanding templating engines available, but has always been confused with Smarty Templating Engine. QuickSkin is even more relevant today than when it was launched. It&#39;s a small footprint with big impact on your projects. It features a built in caching technology, token based substitution, and works on the concept of one single HTML file as the template. The HTML template file can contain variable information making it one small powerful tool for your developer tool kit. Visit the QuickSkin website at
        <a href="http://quickskin.codeworxtech.com/">http://quickskin.codeworxtech.com/</a>.<br />
        <br />
        </li>
      </ul>
      <p>We're committed to PHP and to the Open Source community.</p>
      <p>Opportunities with <strong>Worx International Inc.</strong>:</p>
      <ul>
      <li><span style="background-color: #FFFF00">Resellers/Agents</span>: We're always interested in talking with companies that
      want to represent
      <strong>Worx International Inc.</strong> in their markets. We also have private label programs for our commercial products (in certain circumstances).</li>
      <li>Programmers/Developers: We are usually fully staffed, however, if you would like to be considered for a career with
      <strong>Worx International Inc.</strong>, we would be pleased to hear from you.<br />
      A few things to note:<br />
      <ul>
        <li>experience level does not matter: from fresh out of college to multi-year experience - it&#39;s your
        creative mind and a positive attitude we want</li>
        <li>if you contact us looking for employment, include a cover letter, indicate what type of work/career you are looking for and expected compensation</li>
        <li>if you are representing someone else looking for work, do not contact us. We have an exclusive relationship with a recruiting partner already and not interested in altering the arrangement. We will not hire your candidate under any circumstances unless they wish to approach us individually.</li>
        <li>any contact that ignores any of these points will be discarded</li>
      </ul></li>
      <li>Affiliates/Partnerships: We are interested in partnering with other firms who are leaders in their field. We clearly understand that successful companies are built on successful relationships in all industries world-wide. We currently have innovative relationships throughout the world that are mutually beneficial. Drop us a line and let&#39;s talk.</li>
      </ul>
      Regards,<br />
      Andy Prevost (aka, codeworxtech)<br />
      <a href="mailto:<EMAIL>"><EMAIL></a><br />
      <br />
      We now also offer website design. hosting, and remote forms processing. Visit <a href="http://www.worxstudio.com/" target="_blank">WorxStudio.com</a> for more information.<br />
    </td>
    <td width="50%" valign="top">
		<p>Marcus is the technical director of <a href="http://www.synchromedia.co.uk/">Synchromedia Limited</a>, a UK-based company providing online business services. Synchromedia's main services are:</p>
        <h2>Smartmessages.net</h2>
			<p><a href="https://www.smartmessages.net/"><img src="http://www.synchromedia.co.uk/uploads/images/smlogo.gif" width="292" height="48" alt="Smartmessages.net logo" /><br />Smartmessages.net</a> is Synchromedia's large-scale mailing list management system, providing email delivery services for a wide range of businesses, from sole traders to corporates.
			We pride ourselves on personal service, and realise that every one of your subscribers is a precious asset to be handled with care.
			We provide fast, reliable, high-volume delivery (some of our customers have lists of more than 1,000,000 subscribers) with fine-grained tracking while ensuring you stay fully compliant with UK, EC and US data protection laws. Smartmessages of course uses PHPMailer at its heart!</p>
		<h2>info@hand</h2>
			<p><a href="http://www.synchromedia.co.uk/what-we-do/info-at-hand-crm/"><img src="http://www.synchromedia.co.uk/uploads/images/infoathand-large.png" width="250" height="40" alt="info@hand logo" /></a><br />Synchromedia is the official UK distributor of <a href="http://www.thelongreach.com/">info@hand</a>, a class-leading open-source web-based CRM system. We provide licenses, hosting, planning, support and training for this very fully-featured system at very competitive prices. info@hand also uses PHPMailer!</p>
		<h2>How can we help you?</h2>
			<p>In addition to our headline services, we also provide consulting, development, hosting and sysadmin services, so if you just need a simple web hosting package, we can do that too. Not surprisingly, we know rather a lot about email, so you can talk to us about that too.</p>
			<p>Please <a href="http://www.synchromedia.co.uk/about-us/contact-us/">contact us</a> if you'd like to know more.</p>
			<p>Marcus is a regular attendee at <a href="http://www.phplondon.org/">PHP London</a>, and occasionally speaks on email at technical conferences.</p>
	</td>
  </tr>
</table>
</div>
</center>
</body>
</html>