<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('/mcampus/default/index'))?></li>
        <li class="active"><?php echo Yii::t('site','签字列表') ?></li>
    </ol>

    <div class="row">
        <div class="col-md-12 col-sm-12">
            <?php
            if($userModel) {
                $this->widget('ext.ivyCGridView.BsCGridView', array(
                    'id' => 'event-grid',
                    'dataProvider' => $dataProvider,
                    'template' => "{items}{pager}{summary}",
                    'colgroups' => array(
                        array(
                            "colwidth" => array(null, 150, 150),
                            //            "htmlOptions"=>array("align"=>"left", "span"=>2)
                        )
                    ),
                    'columns' => array(
                        array(
                            'name' => Yii::t('event', '姓名'),
                            'value' => array($this, "getUserName"),
                        ),
                        array(
                            'name' => Yii::t('event', '签字'),
                            'value' => array($this, "getOssImageUrl"),
                        ),
                        array(
                            'name' => Yii::t('event', '增加时间'),
                            'value' => '$data->created_at',
                        ),
                    ),
                ));
            }else{
                ?>
                <div class="alert alert-danger" role="alert">
                    <span class="glyphicon glyphicon-exclamation-sign" aria-hidden="true"></span>
                    <span class="sr-only">Error:</span>
                    该校园暂无老师
                </div>
                <?php
            }?>
        </div>
    </div>
</div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
