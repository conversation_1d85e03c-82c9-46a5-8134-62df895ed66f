<?php

/**
 * This is the model class for table "ivy_scholarship".
 *
 * The followings are the available columns in table 'ivy_scholarship':
 * @property integer $id
 * @property string $schoolid
 * @property integer $yid
 * @property integer $startyear
 * @property string $student
 * @property integer $fid
 * @property integer $mid
 * @property string $reason
 * @property string $attach
 * @property integer $status
 * @property integer $created_at
 * @property integer $created_by
 * @property integer $updated_at
 * @property integer $updated_by
 */
class Scholarship extends CActiveRecord
{
    const STATS_ACTIVE = 10;            // 有效状态
    const STATS_INVALID = 99;            // 无效状态
    public $parent;
    public $agree;
    public $file;

    /**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_scholarship';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('schoolid, yid, startyear, student, reason, attach, status, created_at, created_by, updated_at, updated_by', 'required'),
			array('yid, startyear, fid, mid, status, created_at, created_by, updated_at, updated_by', 'numerical', 'integerOnly'=>true),
			array('schoolid', 'length', 'max'=>32),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, schoolid, yid, startyear, student, fid, mid, reason, attach, status, created_at, created_by, updated_at, updated_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'schoolid' => 'Schoolid',
			'yid' => 'Yid',
			'startyear' => 'Startyear',
			'student' => 'Student',
			'fid' => 'Fid',
			'mid' => 'Mid',
			'reason' => 'Reason',
			'attach' => 'Attach',
			'status' => 'Status',
			'created_at' => 'Created At',
			'created_by' => 'Created By',
			'updated_at' => 'Updated At',
			'updated_by' => 'Updated By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('startyear',$this->startyear);
		$criteria->compare('student',$this->student,true);
		$criteria->compare('fid',$this->fid);
		$criteria->compare('mid',$this->mid);
		$criteria->compare('reason',$this->reason,true);
		$criteria->compare('attach',$this->attach,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('created_at',$this->created_at);
		$criteria->compare('created_by',$this->created_by);
		$criteria->compare('updated_at',$this->updated_at);
		$criteria->compare('updated_by',$this->updated_by);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return Scholarship the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	public static function getStatus()
    {
        $statusArr = array(
            Scholarship::STATS_ACTIVE => '申请',
            Scholarship::STATS_INVALID => '作废',
        );
        return $statusArr;
    }
}
