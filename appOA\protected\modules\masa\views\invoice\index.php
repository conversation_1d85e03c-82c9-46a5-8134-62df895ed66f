<?php $reFundList = CommonUtils::LoadConfig('CfgASA');
$reFundType = array();
foreach ($reFundList['reFundType'] as $k => $v) {
    $reFundType[$k] = (Yii::app()->language == 'zh_cn') ? $v['cn'] : $v['en'];
}

unset($reFundType[10]);
unset($reFundType[4]);
unset($reFundType[5]);

$refundReason = array();
foreach ($reFundList['refundReason'] as $k => $v) {
    $refundReason[$k] = (Yii::app()->language == 'zh_cn') ? $v['cn'] : $v['en'];
}
unset($refundReason[10]);
unset($refundReason[4]);
unset($refundReason[5]);

?>
    <style>
        .invoiceLoading {
            background: url("<?php echo Yii::app()->theme->baseUrl?>/images/loading.gif") no-repeat scroll center center transparent;
            height: 50px;
        }

        .canNotCheck {
            position: relative;
            width: 64%;
            padding: 0 20px;
        }

        .canNotCheck:after {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: #999;
            opacity: .3;
        }
    </style>
    <div class="container-fluid">
        <ol class="breadcrumb">
            <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
            <li><?php echo CHtml::link(Yii::t('site', 'Routines'), array('//mcampus/default/index')) ?></li>
            <li class="active"><?php echo Yii::t('site', 'ASA Management') ?></li>
        </ol>

        <div class="row">
            <div class="col-md-2 col-sm-2 mb10">
                <?php
                $this->widget('zii.widgets.CMenu', array(
                    'items' => $this->module->getMenu(),
                    'id' => 'pageCategory',
                    'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                    'activeCssClass' => 'active',
                ));
                ?>
            </div>
            <!-- <div class="col-md-10 col-sm-12"> -->
            <div class="col-md-2" id='classes'>
                <h5><?php echo CommonUtils::addColon(Yii::t("asa", "Class List")); ?></h5>
                <ul class="nav nav-pills nav-stacked background-gray">
                    <?php
                    $classData = array();
                    foreach ($classes as $class) {
                        if (!$class->children) {
                            continue;
                        }
                        $href = 'javascript:void(0);';
                        echo "<li class='{$active}'><a id='class_{$class->classid}' onclick='showChildren({$class->classid}, this)' href='{$href}'>";
                        echo $class->title;
                        echo '</a></li>';

                        $html = '';
                        foreach ($class->children as $child) {
                            // $href = $this->createUrl('getInvoice', array('classid' => $child->childid));
                            $href = 'javascript:void(0);';
                            $html .= '<ul class="nav nav-pills nav-stacked background-gray">';
                            $html .= "<li class='{$active}'><a href='{$href}' id='child_{$child->childid}' onclick='showInvoice({$child->childid}, this)'>";
                            $html .= $child->getChildName();
                            $html .= '</a></li></ul>';
                            $classData[$class->classid] = $html;
                        }
                    }
                    // 新注册孩子
                    if ($newChildObj) {
                        $html = '';
                        foreach ($newChildObj as $newChild) {
                            // $href = $this->createUrl('getInvoice', array('classid' => 0));
                            $href = 'javascript:void(0);';
                            $html .= '<ul class="nav nav-pills nav-stacked background-gray">';
                            $html .= "<li class='{$active}'><a href='{$href}' onclick='showInvoice({$newChild->childid}, this)'>";
                            $html .= $newChild->getChildName();
                            $html .= '</a></li></ul>';
                            $classData[0] = $html;
                        }
                        echo "<li class='{$active}'><a onclick='showChildren(0, this)' href='javascript:void(0);'>";
                        echo Yii::t('asa', 'Newly Registered');
                        echo '</a></li>';
                    }
                    ?>
                </ul>
            </div>
            <div class="col-md-2">
                <div id="children">
                    <ul class="nav nav-pills nav-stacked background-gray">
                        <?php
                        // foreach ($classes[0]->children as $child) {
                        //     $href = $this->createUrl('getInvoice', array('classid' => $child->childid));
                        //     echo "<li class='{$active}'><a href='{$href}' class='J_ajax'>";
                        //     echo $child->name_cn;
                        //     echo '</a></li>';
                        // }
                        ?>
                    </ul>
                </div>
            </div>
            <div class="col-md-6" id="group" hidden="hidden">

                <div class="page-header">
                    <h3 id="selected-childname">
                        <span class="glyphicon glyphicon-user" aria-hidden="true"></span> <span
                            class="child-name"></span>
                        <small><?php echo Yii::t("asa", "ASA invoices"); ?></small>
                    </h3>
                </div>

                <div class="col-xs-12">
                    <div class="invoiceLoading" style="display: none"></div>
                    <h4 id="invoiceTitle"
                        hidden="hidden"><?php echo CommonUtils::CONST_DOTSYMBOL . Yii::t("asa", "Issued Invoice List"); ?></h4>
                </div>

                <div class="col-xs-12">

                    <!-- 已支付账单 -->
                    <div id="paidinvoice" class="col-md-12">

                    </div>
                    <!-- 未支付账单 -->
                    <div id="unpayinvoice" class="col-md-12">

                    </div>

                </div>

                <form id="invoiceForm" class="J_ajaxForm  form-horizontal" method="post"
                      action="<?php echo $this->createUrl('saveInvoice'); ?>">
                    <!-- 课程组信息 -->
                    <div class="col-xs-12">
                        <h4><?php echo CommonUtils::CONST_DOTSYMBOL . Yii::t("asa", "New Invoice"); ?>
                            <small
                                class="text-warning"><?php echo Yii::t("asa", "Only active ASA projects are listed"); ?></small>
                        </h4>
                    </div>

                    <div class="col-xs-12">
                        <div class="col-xs-5">
                            <select id="group-name" class="form-control ol-xs-5" name="group" onchange=showCourse(this)>
                                <option value="0"><?php echo Yii::t("asa", "Choose an active ASA project"); ?></option>
                                <?php
                                $time = date("Y-m-d", time());
                                $courseData = array();
                                $groupSelect = array();
                                foreach ($group as $v):
                                    if ($v->discount) {
                                        $select = '<label for="discount" class="control-label">减免计划：</label> ';
                                        if ($v->discount) {
                                            $select .= "<select class='form-control' name='discount' id='discount'>";
                                            $select .= "<option value='0'>选择减免计划</option>";
                                            foreach ($v->discount as $discount) {
                                                $select .= "<option value='{$discount->id}'>{$discount->title}</option>";
                                            }
                                            $select .= '</select>';
                                        }
                                    }
                                    $groupSelect[$v->id] = $select;
                                    if ($v->program_type != AsaCourseGroup::PROGRAM_DELAYCARE) {
                                        $courseData[$v->id] .= "<table class='table table-bordered' ><thead><th>课程名称</th><th>减免计划</th><th>单价</th><th>课时</th></thead>";
                                    } else {
                                        $courseData[$v->id] .= "<table class='table table-bordered' ><thead><th>课程名称</th><th>单价</th><th>时间</th></thead>";
                                    }
                                    foreach ($v->course as $course) {
                                        $select = '';
                                        if ($v->program_type != AsaCourseGroup::PROGRAM_DELAYCARE) {
                                            if ($course->discount) {
                                                $select .= "<select name='course-discount[{$course->id}]'>";
                                                $select .= "<option value='0'>选择减免计划</option>";
                                                foreach ($course->discount as $discount) {
                                                    $select .= "<option value='{$discount->id}'>{$discount->title}</option>";
                                                }
                                                $select .= '</select>';
                                            }
                                        }
                                        if ($course->status != AsaCourse::STATUS_INVALID) {
                                            if ($v->program_type != AsaCourseGroup::PROGRAM_DELAYCARE) {
                                                $courseData[$v->id] .= "<tr><td><input name='itemId[]' value='{$course->id}' type='checkbox'> {$course->title_cn}</td>";
                                                $courseData[$v->id] .= "<td>{$select}</td>";
                                                $courseData[$v->id] .= "<td>{$course->unit_price}</td>";
                                                $courseData[$v->id] .= "<td><input name='itemCount_{$course->id}' type='number' value='{$course->default_count}' class=''></td></tr>";
                                            } else {
                                                $courseData[$v->id] .= "<tr><td><input name='itemId[]' value='{$course->id}' type='checkbox'> {$course->title_cn}</td>";
                                                $courseData[$v->id] .= "<td>{$course->unit_price}</td>";
                                                $courseData[$v->id] .= "<td><input name='itemCount_{$course->id}' type='text' value='{$time}' class='datepicker'></td></tr>";
                                            }
                                        }
                                    }
                                    $courseData[$v->id] .= "</table>";
                                    ?>
                                    <option value=<?php echo $v->id; ?>><?php echo $v->title_cn; ?></option>
                                <?php endforeach; ?>
                            </select>
                            <br>
                        </div>

                        <div class="col-xs-12" id="courseInfo">
                        </div>
                        <br>

                        <div class="col-xs-12" id="invoiceInfo" hidden="hidden">
                            <div class="form-group col-xs-8 form-inline">
                                <label for="invoice-title"
                                       class="control-label"><?php echo CommonUtils::addColon(Yii::t("asa", "Invoice Title")); ?></label>
                                <input id="invoice-title" name="title" type="text" class="form-control"
                                       placeholder="<?php echo Yii::t("asa", "Invoice Title"); ?>" size="50">
                            </div>
                            <div class="form-group col-xs-8 form-inline" id="group-discount">
                            </div>
                            <div class="form-group col-xs-12">
                                <button type="submit"
                                        class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t("asa", "Create Invoice"); ?></button>
                            </div>
                            <input type="hidden" id="classid" name="classid" value="">
                            <input type="hidden" id="childid" name="childid" value="">
                        </div>
                    </div>

                </form>
            </div>
            <!-- </div> -->
        </div>
    </div>

    <!-- 微信支付模态框 -->
    <div class="modal fade" id="modal" tabindex="-1" role="dialog" aria-labelledby="modal">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel"><?php echo Yii::t("payment", "Wechat Payment"); ?></h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-lg-12">
                            <p class="text-info">提示信息：请让家长打开微信钱包中的收付款，用条码扫描枪扫描或手动输入条形码</p>
                        </div>
                        <div class="col-lg-3">
                        </div>
                        <div class="col-lg-6">
                            <div class="input-group">
                                <span class="input-group-addon">扫描授权码</span>
                                <input onkeypress="return wechatPay(event)" type="text" id="auth_code" name="auth_code"
                                       class="form-control">
                                <input type="hidden" id='wpInvoice'>
                            </div>
                        </div>
                        <div class="col-lg-3">
                        </div>
                        <!-- <h3>异常记录</h3> -->
                        <div class="col-lg-12" hidden="hidden">
                            <br>

                            <p class="text-danger">支付受阻，请用户在手机上输入支付密码后点击按钮继续</p>

                            <div class="confirm"></div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default"
                            data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                </div>
            </div>
        </div>
    </div>

    <!--退费模态框-->
    <div class="modal fade" id="refundModal" tabindex="-1" role="dialog" data-backdrop="static" aria-labelledby="modal">
        <div class="modal-dialog" role="document">
            <form action="<?php echo $this->createUrl('refund/save') ?>" method="post" class="J_ajaxForm"
                  id="refundForm">
                <div class="modal-content">
                    <div id="refundVue" v-if="refundData">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                    aria-hidden="true">&times;</span></button>
                            <div class="page-header">
                                <h2><?php echo Yii::t("asa", "退费"); ?>
                                    <small>{{refundData.title}}</small>
                                </h2>

                            </div>

                            <ul>
                                <li><?php echo CommonUtils::addColon(Yii::t("asa", "支付时间")); ?><span
                                        class="text-info">{{refundData.pay_date}}</span></li>
                                <li><?php echo CommonUtils::addColon(Yii::t("asa", "支付方式")); ?><span
                                        class="text-info">{{refundData.pay_type_txt}}</span></li>
                                <li><?php echo CommonUtils::addColon(Yii::t("asa", "支付钱数")); ?><span
                                        class="text-info">{{refundData.amount_actual}}</span></li>
                            </ul>


                        </div>
                        <div class="modal-body">
                            <div class="row col-md-12">
                                <div>
                                    <h4><?php echo Yii::t("asa", "Course Items"); ?>
                                        <small><?php echo Yii::t("asa", "Check course to refund"); ?></small>
                                    </h4>
                                    <ul class="nav nav-tabs mb10" id="myTab">
                                        <li id='#tab1' class="active">
                                            <a href='#tab1' role="tab" checktab='tab1' checknum='tab1'
                                               @click="switchTab(1)"
                                               data-toggle="tab" class="tabs">
                                                按课程
                                            </a>
                                        </li>
                                        <li id='#tab2'>
                                            <a href='#tab2' role="tab" checktab='tab2' checknum='tab2'
                                               @click="switchTab(2)" disabled="disabled"
                                               data-toggle="tab" class="tabs">
                                                按账单
                                            </a>
                                        </li>
                                    </ul>
                                    <div class="tab-content">
                                        <div class="tab-pane fade active in" id="tab1">
                                            <div class="col-md-12" v-for="(course,id) in refundData.course">

                                                <div class="panel panel-default">
                                                    <div class="panel-heading">
                                                        <label>
                                                            <input type="checkbox" v-model="course.state"
                                                                   @click="isChecked(id , course.isCheck)"
                                                                   :name="'itemid['+id+']'"
                                                                   v-bind="{ disabled: course.status != 0 || course.refundStatus == 2 || disabledFlag != 1}"/>
                                                            {{course.title}}
                                                        </label>
                                                        <span class="text-warning">{{course.statusName}}</span>

                                                        <span class="label label-info pull-right">{{course.price}} × {{course.count}}</span>
                                                    </div>
                                                    <div v-if="course.isCheck && groupProgramType != 1"
                                                         class="panel-body">
                                                        <div class="form-horizontal" :_value="id" :class="'form' + id">
                                                            <div class="form-group">
                                                                <label
                                                                    class="col-md-3 control-label"><?php echo Yii::t("asa", "退费时间"); ?></label>

                                                                <div class="col-md-8">
                                                                    <input class="form-control tabOneDatepicker"
                                                                           :name="'dropout_date[' + id + ']'"
                                                                           v-bind="{ 'disabled': disabledFlag != 1}">

                                                                    <p class="help-block">
                                                                        从哪天起不再上课，如果1号是最后一节课，请选择2号</p>
                                                                </div>
                                                            </div>
                                                            <div class="form-group">
                                                                <label
                                                                    class="col-md-3 control-label"><?php echo Yii::t("asa", "退费类型"); ?></label>

                                                                <div class="col-md-8" v-if="summerCampStatus != 1">
                                                                    <select class="form-control refund_type"
                                                                            v-bind="{ 'disabled': disabledFlag != 1}"
                                                                            v-model="course.crefundType"
                                                                            :name="'refund_type[' + id + ']'"
                                                                            @change="selectRefundtype(id)">
                                                                        <option value="" selected>请选择</option>
                                                                        <option v-for="(type ,value) in reFundType"
                                                                                v-bind="{ disabled: (value == 1) && course.refundStatus == 3}"
                                                                                :value="value">{{type}}
                                                                        </option>
                                                                    </select>
                                                                </div>
                                                                <div class="col-md-8" v-else>
                                                                    <select class="form-control refund_type"
                                                                            v-model="course.crefundType"
                                                                            :name="'refund_type[' + id + ']'">
                                                                        <option value="" selected>请选择</option>
                                                                        <option v-for="(type ,value) in reFundType"
                                                                                v-bind="{ disabled: (value == 1) && course.refundTypeStat != 0}"
                                                                                :value="value">{{type}}
                                                                        </option>
                                                                    </select>
                                                                </div>
                                                            </div>

                                                            <div class="form-group" v-show="groupProgramType != 1">
                                                                <label
                                                                    class="col-md-3 control-label"><?php echo Yii::t("asa", "退费原因"); ?></label>

                                                                <div class="col-md-8">
                                                                    <select class="form-control"
                                                                            v-bind="{ 'disabled': disabledFlag != 1}"
                                                                            :name="'refund_reason[' + id + ']'">
                                                                        <option value="">请选择</option>
                                                                        <option
                                                                            v-for="(reason ,value) in reFundReason"
                                                                            :value="value">{{reason}}
                                                                        </option>
                                                                    </select>
                                                                </div>
                                                            </div>

                                                            <div class="form-group">
                                                                <label
                                                                    class="col-md-3 control-label"><?php echo Yii::t("asa", "退费内容"); ?></label>

                                                                <div class="col-md-8">
                                                                <textarea class="form-control"
                                                                          v-bind="{ 'disabled': disabledFlag != 1}"
                                                                          :name="'memo[' + id + ']'"
                                                                          rows="3"></textarea>
                                                                </div>
                                                            </div>

                                                            <div class="form-group">
                                                                <label
                                                                    class="col-md-3 control-label"><?php echo Yii::t("asa", "退费文件"); ?></label>

                                                                <div class="col-md-8">
                                                                    <div id="attachment" class="mb5"
                                                                         v-if="course.files">
                                                                <span class="mr5 mb5"
                                                                      v-for="(files,index) in course.files">
                                                                    <input type="hidden"
                                                                           v-bind="{ 'disabled': disabledFlag != 1}"
                                                                           :name="'refund_files[' + id + '][]'"
                                                                           :value="files.saveName">
                                                                    <a class="btn btn-info btn-xs" target="_blank"
                                                                       :href="files.url">{{files.saveName}}</a>
                                                                    <span class="btn btn-danger btn-xs"
                                                                          @click="delFiles(id,index,files.saveName)"><i
                                                                            class="glyphicon glyphicon-remove"></i></span>
                                                                </span>
                                                                        <input type="file" :id="'inputfile_' + id"
                                                                               v-bind="{ 'disabled': disabledFlag != 1}"
                                                                               :onchange="'uploadata(' + id + ')'"
                                                                               class="mb5">
                                                                            <span id="fileinfo"
                                                                                  class="text-warning"></span>

                                                                        <p class="help-block">家长退费说明，行政退费申请等附加材料</p>

                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <div class="form-group" v-if="summerCampStatus != 1">
                                                                <label class="col-md-3 control-label">
                                                                    <!-- <?php echo Yii::t("asa", "退费节数"); ?> -->
                                                                    <p class="help-block"><?php echo CommonUtils::addColon(Yii::t("asa", "Total")); ?>{{course.count}} 节</p>
                                                                    <p class="text-danger"><?php echo CommonUtils::addColon(Yii::t("asa", "已退")); ?>{{course.refund_count}} 节</p>
                                                                </label>

                                                                <div class="col-md-8 refundCount">
                                                                    <div
                                                                        :class="{'canNotCheck': course.crefundType == 1 || course.crefundType == 2 || course.crefundType == ''}">
                                                                        <div class="checkbox"
                                                                             :class="{'disabled': course.crefundType == 1 || course.crefundType == 2 || course.crefundType == ''}"
                                                                             v-for="(count,index) in course.courseTime">
                                                                            <label>
                                                                                <input type="checkbox"
                                                                                       :name="'refund_count[' + id + '][]'"
                                                                                       v-model="course.selected"
                                                                                       :value="index"/>
                                                                                {{count.dateText}} {{count.ready_s}}
                                                                                -
                                                                                {{count.ready_e}}
                                                                            </label>
                                                                        </div>
                                                                    </div>
                                                                    <p class="help-block"><?php echo CommonUtils::addColon(Yii::t("asa", "退费钱数")); ?>
                                                                        {{(course.price *
                                                                        course.selected.length)}} </p>
                                                                </div>
                                                            </div>
                                                            <div class="form-group"
                                                                 v-if="course.crefundType != '' && course.crefundType != 1 && summerCampStatus == 1">
                                                                <label
                                                                    class="col-md-3 control-label"><?php echo Yii::t("asa", "退款金额"); ?></label>

                                                                <div class="col-md-8">
                                                                    <input class="form-control"
                                                                           v-bind="{ 'disabled': disabledFlag != 1}"
                                                                           :name="'refundAmount[' + id + ']'">

                                                                    <p class="help-block">总金额：({{course.courseMoney}})
                                                                        请输入退款金额</p>
                                                                </div>
                                                            </div>
                                                            <div class="form-group"
                                                                 v-if="course.discountStatus == 1">
                                                                <label
                                                                    class="col-md-3 control-label"><?php echo Yii::t("asa", "按账单退款"); ?></label>

                                                                <div class="col-md-8">
                                                                    <input class="form-control"
                                                                           v-bind="{ 'disabled': disabledFlag != 1}"
                                                                           :name="'refund_morry[' + id + ']'">

                                                                    <p class="help-block">请输入退款金额</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="tab-pane fade" id="tab2">
                                            <div class="col-md-12">
                                                <div class="panel panel-default">
                                                    <div class="panel-heading">
                                                        账单
                                                    </div>
                                                    <div class="panel-body">
                                                        <div class="form-horizontal">


                                                            <div class="form-group" v-if="groupProgramType == 1">
                                                                <label
                                                                    class="col-md-3 control-label"><?php echo Yii::t('asa', '确定按账单退费') ?>
                                                                </label>

                                                                <div class="col-md-8">

                                                                    <label>

                                                                        <p class="help-block">
                                                                            <input type="checkbox" v-model="delayFlag"/>
                                                                            这将退掉该账单的全部课程</p>
                                                                    </label>
                                                                </div>
                                                            </div>
                                                            <div v-if="groupProgramType != 1">

                                                                <div class="form-group">
                                                                    <label
                                                                        class="col-md-3 control-label"><?php echo Yii::t('asa', '退费时间') ?>
                                                                    </label>

                                                                    <div class="col-md-8">
                                                                        <input name="dropout_date"
                                                                               class="form-control datepicker"
                                                                               v-bind="{ 'disabled': disabledFlag != 2}">

                                                                        <p class="help-block">
                                                                            从哪天起不再上课，如果1号是最后一节课，请选择2号</p>
                                                                    </div>
                                                                </div>
                                                                <div class="form-group"><label
                                                                        class="col-md-3 control-label"><?php echo Yii::t('asa', '退费类型') ?></label>

                                                                    <div class="col-md-8">
                                                                        <select name="refund_type" class="form-control"
                                                                                v-bind="{ 'disabled': disabledFlag != 2}">
                                                                            <option value="">请选择</option>
                                                                            <option value="1">退出课程：开课前全额退款</option>
                                                                            <option value="2">退出课程：开课后部分退款</option>
                                                                            <option value="3">特殊退款</option>
                                                                        </select>
                                                                    </div>
                                                                </div>
                                                                <div class="form-group"><label
                                                                        class="col-md-3 control-label"><?php echo Yii::t('asa', '退费原因') ?></label>

                                                                    <div class="col-md-8">
                                                                        <select name="refund_reason"
                                                                                class="form-control"
                                                                                v-bind="{ 'disabled': disabledFlag != 2}">
                                                                            <option value="">请选择</option>
                                                                            <option value="1">课程质量不佳</option>
                                                                            <option value="2">老师频繁更换</option>
                                                                            <option value="3">其它特殊原因</option>
                                                                        </select></div>
                                                                </div>
                                                                <div class="form-group"><label
                                                                        class="col-md-3 control-label"><?php echo Yii::t('asa', '退费介绍') ?></label>

                                                                    <div class="col-md-8">
                                                                    <textarea name="memo" rows="3"
                                                                              v-bind="{ 'disabled': disabledFlag != 2}"
                                                                              class="form-control"></textarea>
                                                                    </div>
                                                                </div>
                                                                <div class="form-group"><label
                                                                        class="col-md-3 control-label"><?php echo Yii::t('asa', '退费文件') ?></label>

                                                                    <div class="col-md-8">
                                                                        <div id="attachment" class="mb5">
                                                                        <span class="mr5 mb5"
                                                                              v-for="(files,index) in accountFiles">
                                                                            <input type="hidden"
                                                                                   v-bind="{ 'disabled': disabledFlag != 2}"
                                                                                   :name="'refund_files[]'"
                                                                                   :value="files.saveName">
                                                                            <a class="btn btn-info btn-xs"
                                                                               target="_blank" :href="files.url">{{files.saveName}}</a>
                                                                            <span class="btn btn-danger btn-xs"
                                                                                  @click="delFiles(-1,index,files.saveName,1)">
                                                                                <i class="glyphicon glyphicon-remove"></i>
                                                                            </span>
                                                                        </span>
                                                                            <input type="file" id="inputfile_-1"
                                                                                   onchange="uploadata(-1,1)"
                                                                                   v-bind="{ 'disabled': disabledFlag != 2}"
                                                                                   class="mb5">
                                                                            <span id="fileinfo"
                                                                                  class="text-warning"></span>

                                                                            <p class="help-block">家长退费说明，行政退费申请等附加材料</p>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <input type="hidden" name="invoiceId"
                                                                   v-bind="{ 'disabled': disabledFlag != 2 || (groupProgramType == 1 && !delayFlag)}"
                                                                   :value="refundData.invoiceId">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <h4><?php echo Yii::t("asa", "收款人信息"); ?></h4>

                                    <div v-if="refundData.pay_type == 1">
                                        <table class="table table-hover">
                                            <thead>
                                            <tr>
                                                <td>银行</td>
                                                <td>姓名</td>
                                                <td>账号</td>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <tr>
                                                <td><input type="text" class="form-control" id="payBank" name="bank"
                                                           placeholder="请输入银行卡支行信息"/></td>
                                                <td><input type="text" class="form-control" id="payAccountName"
                                                           name="account_name" placeholder="请输入姓名"/></td>
                                                <td><input type="text" class="form-control" id="payAccountNumber"
                                                           name="account_number" placeholder="请输入银行账号"/></td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    <div v-else class="col-md-12">
                                        <p class="help-block"><?php echo Yii::t("asa", "账单是由微信支付完成，退款将由微信支付原路退回。"); ?></p>
                                    </div>

                                </div>


                            </div>
                        </div>

                        <div class="clearfix"></div>
                    </div>
                    <div class="modal-footer">
                        <span id="J_fail_info" class="text-warning"></span>
                        <button type="button" class="btn btn-primary J_ajax_submit_btn submitsx">保存</button>
                        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                        <input type="reset" class="_reset" style="display: none"/>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <script>
        var children, course, reFundReason, gid, reFundType;
        children = <?php echo json_encode($classData); ?>;
        course = <?php echo json_encode($courseData); ?>;
        reFundReason = <?php echo json_encode($refundReason); ?>;
        reFundType = <?php echo json_encode($reFundType); ?>;
        groupSelect = <?php echo json_encode($groupSelect); ?>;
        // 显示指定班级的学生


        function showChildren(classid, ele) {
            if (classid != undefined) {
                $('#classes li').removeClass('active');
                $(ele).parent().addClass('active');
                $('#children').html('<h5><?php echo CommonUtils::addColon(Yii::t("asa","NameList"));?></h5>' + children[classid]);
                $('#classid').val(classid);
            }
        }

        // 显示指定学生的未付账单信息
        function showInvoice(childid, ele) {

            $('.invoiceLoading').show()
            if (childid != undefined) {
                if (ele) {
                    $('#children li').removeClass('active');
                    $(ele).parent().addClass('active');
                }
                $('#childid').val(childid);

                var url = '<?php echo $this->createUrl('getInvoice');?>';
                $.ajax({
                    type: 'POST',
                    dataType: 'JSON',
                    url: url,
                    data: {childid: childid},
                    success: function (data) {
                        var table = '';
                        if (data != undefined && data != '') {
                            table += '<table class="table table-bordered"><colgroup><col width="100"><col width="150"><col width="50"><col width="150"><col width="50"></colgroup><thead class="text-danger"><th>未支付账单</th><th>账单明细</th><th>账单金额</th><th></th></thead>';
                            $.each(data, function (i, e) {
                                table += '<tr><td>' + e.title + '</td>';
                                table += '<td>' + e.info + '</td>';
                                table += '<td>' + e.amount + '</td>';
                                table += '<td><button onclick="cashPay(' + i + ', ' + e.amount + ')" class="btn btn-xs mb5">现金支付</button> <button onclick="showModal(' + i + ')" class="btn btn-success btn-xs mb5">微信支付</button> <a href="<?php echo $this->createUrl('printInvoice') ?>&invoiceId='+ i +'" target="_blank" class="btn btn-xs btn-default mb5">打印账单</a> <button onclick="delInvoice(' + i + ')" class="btn btn-danger btn-xs mb5"> <span class="glyphicon glyphicon-remove" aria-hidden="true"></span></button></td></tr>';
                            });
                            table += '</table>';
                        }
                        if (table == '') {
                            $('#unpayinvoice').hide();
                        } else {
                            $('#unpayinvoice').html(table);
                            $('#unpayinvoice').show();
                        }
                    }
                }).always(function () {
                    showPaidInvoice(childid);
                    $('#group').show();
                    $('.invoiceLoading').hide()
                });
            }
            changeInvoiceTitle();
        }

        // 显示已支付订单
        function showPaidInvoice(childid) {
            if (childid != undefined) {
                $('#childid').val(childid);

                var url = '<?php echo $this->createUrl('paidInvoice');?>';
                $.ajax({
                    type: 'POST',
                    dataType: 'JSON',
                    url: url,
                    data: {childid: childid},
                    success: function (data) {
                        var table = '';
                        if (data != undefined && data != '') {
                            table = '<table class="table table-bordered"><colgroup><col width="100"><col width="150"><col width="50"><col width="150"><col width="50"></colgroup><thead><th>已支付账单</th><th>账单明细</th><th>账单金额</th><th>操作人</th><th>退费</th></thead>';
                            $.each(data, function (i, e) {
                                var display = e.exchange ? 'disabled=disabled' : '';
                                table += '<tr><td>' + e.title + '</td>';
                                table += '<td>' + e.info + '</td>';
                                table += '<td>' + e.amount + '</td>';
                                table += '<td>' + e.user + '——' + e.date + '</td>';
                                table += '<td><button ' +display+ ' onclick="onrefundModal(' + e.id + ')" class="btn btn-sm">申请退费</button></td></tr>';
                            });
                            table += '</table>';
                        }
                        if (table == '') {
                            $('#paidinvoice').hide();
                        } else {
                            $('#paidinvoice').html(table);
                            $('#paidinvoice').show();
                        }
                    }
                }).always(function () {
                        if ($('#paidinvoice :visible').length == 0 && $('#unpayinvoice :visible').length == 0) {
                            $("#invoiceTitle").hide();
                        } else {
                            $("#invoiceTitle").show();
                        }
                    }
                );
            }
        }

        // 下拉菜单显示课程
        function showCourse(ele) {
            gid = $(ele).val();
            /*var html = '<table class="table table-bordered" ><thead><th>课程名称</th><th>减免计划</th><th>单价</th><th>课时</th></thead>';
             html += course[gid] + '</table>';*/
            $('#courseInfo').html(course[gid] ? course[gid] : '<?php echo Yii::t("asa","No data found.");?>');

            if (gid == 0) {
                $("#invoiceInfo").hide();
            } else {
                $("#invoiceInfo").show();
                $('.datepicker').datepicker({
                    changeMonth: true,
                    changeYear: true,
                    dateFormat: 'yy-mm-dd'
                });

            }
            changeInvoiceTitle(gid);
        }

        // 修改默认账单标题
        function changeInvoiceTitle(gid) {
            var childName = $('#children .active a').text();
            var title = childName + ' ' + $('#group-name').find('option:selected').text();
            $('#invoice-title').val(title);
            $('#group-discount').html(groupSelect[gid]);
            $("#selected-childname .child-name").html(childName);
        }

        // 账单保存成功回调
        function cbShow(data) {
            showInvoice(data.childid, '');
        }

        // 现金付款
        function cashPay(id, amount) {
            var realAmount = prompt('请输入现金付款的金额');
            if (realAmount != null) {
                if (realAmount != amount) {
                    alert('<?php echo Yii::t("asa","Invalid amount, please confirm");?>');
                } else {
                    $.ajax({
                        type: 'POST',
                        dataType: 'JSON',
                        data: {id: id, amount: amount},
                        url: '<?php echo $this->createUrl("cashPay"); ?>',
                        success: function (data) {
                            if (data.state == 'fail') {
                                alert('<?php echo Yii::t("asa","Payment failed.");?> ' + data.message);
                            }
                            if (data.state == 'success') {
                                alert('<?php echo Yii::t("asa","Payment made successfully.");?>');
                                showInvoice(data.data.childid, '');
                            }
                        }
                    });
                }
            }
        }

        // 显示微信支付的模态框
        function showModal(id) {
            $('.confirm').parent().hide();
            $('#wpInvoice').val(id);
            $.ajax({
                type: 'POST',
                dataType: 'JSON',
                data: {invoiceId: id},
                url: '<?php echo $this->createUrl("unpayWechat"); ?>',
                success: function (data) {
                    if (data != '') {
                        var html = '';
                        $.each(data, function (i, v) {
                            html += '<button onclick="wechatConfirm(' + "'" + v + "'" + ')" class="btn btn-default"><?php echo Yii::t("asa","Click to proceed");?></button> ';
                        });
                        $('.confirm').html(html);
                        $('.confirm').parent().show();
                    }
                }
            });
            $('#modal').modal('show');
        }

        // 微信支付自动聚焦input
        $('#modal').on('shown.bs.modal', function (e) {
            $('#auth_code')[0].focus();
        });

        // 微信支付
        function wechatPay(ele) {
            if (ele.keyCode == 13) {
                var authCode = $('#auth_code').val();
                var invoiceId = $('#wpInvoice').val();
                if (!authCode || !invoiceId) {
                    return false;
                }
                $.ajax({
                    type: 'POST',
                    dataType: 'JSON',
                    data: {authCode: authCode, invoiceId: invoiceId},
                    url: '<?php echo $this->createUrl("wechatPay"); ?>',
                    success: function (data) {
                        if (data.return_code == 'SUCCESS') {
                            if (data.result_code == 'SUCCESS') {
                                resultTip({msg: '<?php echo Yii::t("asa","Payment made successfully.");?>'});
                                showInvoice($('#childid').val(), '');
                                $('#modal').modal('hide');
                            }
                            else {
                                if (data.err_code == 'AUTHCODEEXPIRE') {
                                    resultTip({msg: data.err_code_des, error: 1});
                                }
                                else if (data.err_code == 'USERPAYING') {
                                    showMicropay(data.out_trade_no);
                                }
                            }
                        }
                    }
                });
            }
        }

        // 查询账单状态
        function showMicropay(orderid) {
            var html = '<button onclick="wechatConfirm(\'' + orderid + '\')" class="btn btn-default"><?php echo Yii::t("asa","Click to proceed");?></button> ';
            $('.confirm').html(html);
            $('.confirm').parent().show();
        }

        // 微信付款确认
        function wechatConfirm(orderid) {
            var url = '<?php echo $this->createUrl("queryWechat"); ?>';
            $.ajax({
                type: 'POST',
                dataType: 'JSON',
                url: url,
                data: {orderid: orderid},
                success: function (data) {
                    if (data.state == 'success') {
                        resultTip({msg: '<?php echo Yii::t("asa","Payment made successfully.");?>'});
                        showInvoice($('#childid').val(), '');
                        $('#modal').modal('hide');
                    } else {
                        resultTip({msg: 'Payment failure', error: 'warning'});
                    }
                }

            });
        }

        // 作废账单
        function delInvoice(invoiceId) {
            if (confirm('<?php echo Yii::t("asa","Confirm to delete this invoice?");?>')) {
                $.ajax({
                    type: 'POST',
                    dataType: 'JSON',
                    data: {invoiceId: invoiceId},
                    url: '<?php echo $this->createUrl("delInvoice"); ?>',
                    success: function (data) {
                        if (data.state == 'success') {
                            resultTip({msg: '<?php echo Yii::t("asa","Invoice deleted");?>'});
                            showInvoice(data.data.childid);
                        }
                    }
                });
            }
        }
        var refundData, discountFlag = false, disabledFlag = 1, accountFiles = [];
        //退费模型
        var refundVueModal = new Vue({
            el: "#refundVue",
            data: {
                refundData: refundData,
                reFundReason: reFundReason,
                reFundType: reFundType,
                discountFlag: discountFlag,
                disabledFlag: disabledFlag,
                accountFiles: accountFiles,
                groupProgramType: "",
                delayFlag: false,
                summerCampStatus: ""

            },
            updated: function () {
                //日期选择
                $('.datepicker').datepicker({
                    dateFormat: 'yy-mm-dd',
                    onSelect: function (dateText, inst) {    //选中事件 第一个参数是选中时间 第二个参数是选中的对象

                    }
                });
                $('.tabOneDatepicker').datepicker({
                    dateFormat: 'yy-mm-dd',
                    onSelect: function (dateText, inst) {    //选中事件 第一个参数是选中时间 第二个参数是选中的对象
                        if (refundVueModal.summerCampStatus != 1) {
                            var _refundType = $('#' + inst.id).parents('.form-horizontal').find('.refund_type').val();
                            var _classNum = $('#' + inst.id).parents('.form-horizontal').attr('_value');
                            if (_refundType == 1) {
                                refundVueModal.refundData.course[_classNum].selected = [];
                                $.each(refundVueModal.refundData.course[_classNum].courseTime, function (i, date) {
                                    refundVueModal.refundData.course[_classNum].selected.push(i)
                                });
                            } else if (_refundType == 2) {
                                refundVueModal.refundData.course[_classNum].selected = [];
                                $.each(refundVueModal.refundData.course[_classNum].courseTime, function (i, date) {
                                    if (new Date(dateText) < new Date(date.dateText)) {
                                        refundVueModal.refundData.course[_classNum].selected.push(i)
                                    }
                                });
                            }
                        }
                    }
                });
            },
            created: function () {
            },
            methods: {
                isChecked: function (id, check) {
                    this.$set(this.refundData.course[id], 'isCheck', !check);
                },
                setCheck: function (id) {
                    Vue.set(this.refundData.course[id], 'isCheck', false)
                },
                setSelect: function (id) {
                    Vue.set(this.refundData.course[id], 'selected', [])
                },
                setRefundType: function (id) {
                    Vue.set(this.refundData.course[id], 'crefundType', '')
                },
                setTest: function (id) {
                    Vue.set(this.refundData.course[id], 'test', [{
                        "date": "2017-07-01",
                        "canChange": "1"
                    }, {"date": "2017-07-02", "canChange": "0"}, {"date": "2017-07-03", "canChange": "1"}])
                },
                setUploadFile: function (id) {
                    Vue.set(this.refundData.course[id], 'files', [])
                },
                uploadFiles: function (id, url, saveName) {
                    this.refundData.course[id].files.push({'url': url, 'saveName': saveName})
                },
                accountUploadFiles: function (id, url, saveName) {
                    this.accountFiles.push({'url': url, 'saveName': saveName})
                },
                delFiles: function (id, index, filename, isAccount) {
                    $(".submitsx").attr("disabled", "disabled");
                    $.ajax({
                        url: '<?php echo $this->createUrl('refund/delFiles'); ?>',
                        data: {fileName: filename},
                        type: 'post',
                        dataType: 'json',
                        success: function (data) {
                            $(".submitsx").removeAttr("disabled");
                            if (data.state == 'success') {
                                if (isAccount == 1) {
                                    Vue.delete(refundVueModal.accountFiles, index)
                                } else {
                                    Vue.delete(refundVueModal.refundData.course[id].files, index)
                                }
                            } else {
                                $("#fileinfo").text(data.message);
                            }
                        },
                        error: function (data) {
                            $("#fileinfo").text(data.message);
                        }
                    });
                },
                switchTab: function (num) {
                    this.disabledFlag = num;
                },
                selectRefundtype: function (id) {
                    var pickerVal = $('.form' + id).find('.tabOneDatepicker').val();
                    var selectVal = refundVueModal.refundData.course[id].crefundType;
                    if (pickerVal) {
                        if (selectVal == 1) {
                            refundVueModal.refundData.course[id].selected = [];
                            $.each(refundVueModal.refundData.course[id].courseTime, function (i, date) {
                                refundVueModal.refundData.course[id].selected.push(i)
                            });
                        } else if (selectVal == 2) {
                            refundVueModal.refundData.course[id].selected = [];
                            $.each(refundVueModal.refundData.course[id].courseTime, function (i, date) {
                                if (new Date(pickerVal) < new Date(date.dateText)) {
                                    refundVueModal.refundData.course[id].selected.push(i)
                                }
                            });
                        } else if (selectVal == 3) {

                        } else {
                            refundVueModal.refundData.course[id].selected = [];
                        }
                    }
                }
            },
            computed: {
                allCourseStatus: function () {
                    var _this = this, _flag;
                    $.each(_this.refundData.course, function (i, rlist) {
                        if (rlist.status != 0) {
                            _flag = true
                        }
                    });
                    if (_flag) {
                        return false;
                    } else {
                        return true;
                    }
                }
            }
        });

        //退费
        function onrefundModal(invoiceid) {
            $('._reset').click();
            var _this = this;
            $.ajax({
                type: 'get',
                dataType: 'JSON',
                data: {invoiceid: invoiceid},
                url: '<?php echo $this->createUrl('refund/getCourse') ?>',
                success: function (data) {
                    if(data.state && data.state==='fail'){
                        resultTip({msg: data.message, error: 'warning'});
                    }else{
                        refundVueModal.refundData = data;
                        refundVueModal.summerCampStatus = data.status;
                        refundVueModal.disabledFlag = 1;
                        if (data.groupProgramType == 1) {
                            Vue.set(refundVueModal, 'groupProgramType', 1)
                        } else {
                            Vue.set(refundVueModal, 'groupProgramType', "")
                        }
                        $('#refundForm').prop('action', data.url);
                        $.each(refundVueModal.refundData.course, function (i, l) {
                            refundVueModal.setCheck(i);
                            refundVueModal.setSelect(i);
                            refundVueModal.setRefundType(i);
                            refundVueModal.setTest(i);
                            refundVueModal.setUploadFile(i);
                        });
                        setTimeout(function () {
                            if (data.payee_info != null) {
                                $('#payBank').val(data.payee_info.bank);
                                $('#payAccountName').val(data.payee_info.accountName);
                                $('#payAccountNumber').val(data.payee_info.accountNumber);
                            }
                        }, 0);
                        $('#refundModal').modal();
                    }

                }, error: function () {
                    alert('error')
                }
            });
            $('#myTab a:first').tab('show');
        }
        //退费回调
        function refundSuccess() {
            $('#refundModal').modal('hide');
        }

        function uploadata(id, isAccount) {
            $(".submitsx").attr("disabled", "disabled");
            $("#fileinfo").text('');
            var data = new FormData();
            $.each($('#inputfile_' + id)[0].files, function (i, file) {
                data.append('upload_file', file);
            });
            $.ajax({
                url: '<?php echo $this->createUrl('refund/refundFiles'); ?>',
                data: data,
                type: 'post',
                dataType: 'json',
                contentType: false,    //不可缺
                processData: false,    //不可缺
                success: function (data) {
                    $(".submitsx").removeAttr("disabled");
                    if (data.msg == 'success') {
                        //清空文件域
                        $("#inputfile_" + id).after($("#inputfile_" + id).clone().val(""));
                        $("#inputfile_" + id).remove();
                        //显示附件
                        if (isAccount == 1) {
                            refundVueModal.accountUploadFiles(id, data.url, data.saveName);
                        } else {
                            refundVueModal.uploadFiles(id, data.url, data.saveName);
                        }
                    } else {
                        $("#fileinfo").text(data.msg);
                    }
                },
                error: function (data) {
                    $("#fileinfo").text(data.msg);
                }
            });
        }
        $(document).on('click', '.delfile', function () {
            var _this = $(this)
            var filename = $(this).attr('_value');
            $.ajax({
                url: '<?php echo $this->createUrl('refund/delFiles'); ?>',
                data: {fileName: filename},
                type: 'post',
                dataType: 'json',
                success: function (data) {
                    if (data.state == 'success') {
                        _this.parent().remove()
                    } else {
                        $("#fileinfo").text(data.message);
                    }
                },
                error: function (data) {
                    $("#fileinfo").text(data.message);
                }
            });
        });
        var _status = '<?php echo $status; ?>';
        var _childId = '<?php echo $childid; ?>';
        var _classId = '<?php echo $classid; ?>';
        if(_status === 'on' && _childId != 0){
            showChildren(_classId,document.getElementById('class_' + _classId));
            showInvoice(_childId,document.getElementById('child_' + _childId));
        }
    </script>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
