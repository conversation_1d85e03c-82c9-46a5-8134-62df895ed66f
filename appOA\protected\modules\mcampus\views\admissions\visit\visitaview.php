<?php $form=$this->beginWidget('CActiveForm', array(
    'id'=>'visits-form',
    'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
));
?>
<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
    <h4 class="modal-title"><?php echo $modalTitle;?></h4>
</div>
<div class="modal-body">
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'visit_date'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'visit_date',array('maxlength'=>255,'class'=>'form-control datepicker','readonly'=>'readonly')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'visit_time'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'visit_time',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'child_name'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'child_name',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'parent_name'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'parent_name',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'phone'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'phone',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'email'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'email',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'status'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->radioButtonList($model, 'status', array(
                1=>'信息准确，人准备来参观（用户信息将进入潜客列表，需后续跟进）',
                2=>'信息准确，人不来（用户信息将进入潜客列表，需后续跟进）',
                3=>'过期、虚假或重复信息（用户信息将不进入潜客列表，无需后续跟进）'),
            array('template'=>'<div class="radio">{input} {label}</div>', 'separator'=>'')
            ); ?>
        </div>
    </div>
</div>
<div class="modal-footer">
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
    <button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
</div>

<?php $this->endWidget(); ?>
<script>
    $('.datepicker').datepicker({'dateFormat':'yy-mm-dd'});
    // 回调：添加成功
    function cbVisit() {
        $('#modal').modal('hide');
        $.fn.yiiGridView.update('pending-visit-grid');
    }
</script>