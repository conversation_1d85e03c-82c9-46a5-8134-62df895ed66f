<?php $schoolType = $child->school->type;?>
<?php $schoolAbb = $child->school->branchid;?>
<div class="h_a"><?php echo Yii::t("campus", "Child Profile");?></div>
<div class="table_full">
    <table width="100%">
        <colgroup>
            <col width="100">
        </colgroup>
        <tbody>
            <tr>
                <th rowspan="2" style="font-size:14px;font-weight:600;color: #333">
                    <?php echo $child->getChildName()?>
                    <?php
                        if($child->photo):
                            echo CHtml::image(CommonUtils::childPhotoUrl($child->photo), '', array("width"=>80));
                        endif;
                    ?>
                </th>
                <td>
                    <ul>
                        <li class="mb5"><?php $allBranch = $this->getController()->getAllBranch(); echo CHtml::link($allBranch[$child->schoolid]['title'], array('//mcampus/student/index', 'category'=>'current', 'branchId'=>$child->schoolid)); ?></li>
                        <li class="mb5"><?php echo OA::getChildStatus($child->status);?> &nbsp; <?php if($child->classid && $child->stat) echo $child->stat->classInfo->title; else echo Yii::t("campus", "No class assigned");?></li>
                        <li></li>
                        <li class="mb5"><?php echo OA::renderChildGender($child->gender);?> &nbsp; <?php echo sprintf("%s (%s)", $child->birthday_search, OA::getAge($child->birthday));?></li>
                        <!--此处使用vue-->
                        <li id="child_info_container">
                            <?php $this->render("_child_label_modal");?>
                        </li>
                        <li>
                            <?php echo CHtml::link(Yii::t('teaching', 'Frontend Preview'), OA::genCCUrlHome($child->childid), array('target'=>'_blank')); ?>&nbsp;
                            <?php
                            if(in_array($child->schoolid, array('BJ_DS', 'BJ_SLT','BJ_QFF'))){
                                echo CHtml::link(Yii::t('newDS', 'Academic Center'), array('/mteaching/learnInfo/index', 'branchId' => $child->schoolid,'#'=>'Journals'));
                            }
                            ?>
                        </li>
                    </ul>
                </td>
            </tr>
            <tr>
                <td height=30>
                    <div>
                    <?php $this->widget('ext.search.ChildSearch', array(
                        'inputCSS' => 'length_5'
                    )) ?>
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</div>
<div class="bg-mask"></div>
<div class="nav">
<?php

    $childMainMenu = array(
        'profile'=>array(
            'label' => Yii::t('child', 'Basic Profile'),
            'url' => array('/child/profile/index')
        ),
        'account'=>array(
            'label' => Yii::t('child', 'Parent Account'),
            'url' => array('/child/profile/accounts')
        ),
        'finance'=>array(
            'label' => Yii::t('child', 'Finance'),
            'url' => array('/child/invoice/finance', 'childid'=>$child->childid)
        ),
        'invoice'=>array(
            'label' => Yii::t('child', 'Create Invoice'),
            'url' => array('/child/invoice/index', 'childid'=>$child->childid)
        ),
        'status'=>array(
            'label' => Yii::t('child', 'Status & Class'),
            'url' => array('/child/index/status', 'childid'=>$child->childid)
        ),
        'lunch'=>array(
            'label' => Yii::t('child', 'Cancel Lunch'),
            'url' => array('/child/index/cancelLunch', 'childid'=>$child->childid)
        ),
        'asa'=>array(
            'label' => Yii::t('child', '课后课账单'),
            'url' => array('/child/invoice/asaInvoice', 'childid'=>$child->childid)
        ),
    );

    if($schoolType == 50 || $schoolAbb == "BJ_QFF"){
//        unset($childMainMenu['invoice']);
        unset($childMainMenu['lunch']);
    }
	$this->widget('zii.widgets.CMenu',array(
		'items'=> $childMainMenu,
		'activeCssClass'=>'current',
		'htmlOptions'=>array(
			'class'=>'cc',
		)
	));

	$posClass = $cashClass = $wxposClass = 'btn mr10 ';
	$posClass .= ($this->selectedPayFlag == 'pos') ? "btn_submit" : "";
	$cashClass .= ($this->selectedPayFlag == 'cash') ? "btn_submit" : "";
	$wxposClass .= ($this->selectedPayFlag == 'micropay') ? "btn_submit" : "";

	echo CHtml::link(Yii::t('payment',"POS Payment"),array('/child/invoice/unpaid', 't'=>'pos'),array('class'=>$posClass));
	echo CHtml::link(Yii::t('payment',"Cash Payment"),array('/child/invoice/unpaid', 't'=>'cash'),array('class'=>$cashClass));
    echo CHtml::link(Yii::t('payment',"微信刷卡支付"),array('/child/invoice/unpaid', 't'=>'micropay'),array('class'=>$wxposClass));
?>
</div>

<?php $this->render("_childinfo_js");?>
