<div class="row">
    <div class="col-md-12">
        <div class="panel panel-default">
            <div class="panel-heading"><?php echo Yii::t('teaching','Class Report');?></div>
            <div class="panel-body">
                <?php echo CHtml::form('', 'post', array('class'=>'form-horizontal J_ajaxForm', 'role'=>'form'));?>
                <?php if(0 && $this->branchObj->type == 50):?>
                <div class="form-group">
                    <label class="col-sm-2 control-label"><?php echo Yii::t('teaching','Photos');?></label>
                    <div class="col-sm-10">
                        <div class="mb10">
                            <?php $this->widget('ext.selectMedia.SelectMedia', array(
                                'weeknum'=>$this->selectedWeeknum,
                                'classid'=>$this->selectedClassId,
                                'branchid'=>$this->branchId,
                                'startyear'=>$this->calendarModel->startyear,
                                'yid'=>$this->calendarId,
                                'multiple'=>true,
                                'callback'=>'callback',
                                'buttonLabel' => Yii::t('global','Add'),
                            ));
                            ?>
                        </div>
                        <div id="items-class"></div>
                    </div>
                </div>
                <?php endif;?>
                <div class="form-group">
                    <label class="col-sm-2 control-label"><?php echo Yii::t('labels','Content');?></label>
                    <div class="col-sm-10">
                        <?php
                        $toolbars = array('fullscreen', 'source', '|', 'undo', 'redo', '|','bold', 'italic', 'underline', 'strikethrough', 'superscript', 'subscript', '|','insertorderedlist', 'insertunorderedlist', '|','link', 'unlink', 'attachment');
                        if (Yii::app()->params['siteFlag'] == 'daystar') {
                            $toolbars = array('fullscreen', 'source', '|', 'undo', 'redo', '|','bold', 'italic', 'underline', 'strikethrough', 'superscript', 'subscript', '|','insertorderedlist', 'insertunorderedlist', '|','link', 'unlink', 'attachment', 'insertimage');
                        }
                        $this->widget('common.extensions.ueditor.ueditor_1_5_0',array(
                            'model' => $taskData['model'],
                            'attribute' => 'en_content',
                            'configFile' => 'ueditor.teacher.config',
                            'language' =>Yii::app()->language == 'en_us' ? 'en' : 'zh-cn',
                            'editorOptions' => array('initialFrameWidth'=>'80%'),
                            'toolbars'=>$toolbars,
                        ));
                        ?>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label"><?php echo Yii::t('teaching','Make Online');?></label>
                    <div class="col-sm-10">
                        <div class="checkbox">
                            <label>
                                <?php echo CHtml::activeCheckBox($taskData['model'], 'stat', array('value'=>20));?>
                                &nbsp;
                            </label>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2"></label>
                    <div class="col-sm-10">
                        <button class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Save');?></button>
                    </div>
                </div>
                <?php echo CHtml::endForm();?>
            </div>
        </div>
    </div>

</div>

<style>
    .customWH{width: 460px;height: 85px !important;}
</style>

<script type="text/template" id="photo-item">
    <div id="item<%= id%>" data-isnew="<%= isNew%>">
        <input name="pid[]" value="<%= id%>" type="hidden">
        <div class="pull-left mr10">
            <div><img class="img-thumbnail" src="<%= url%>"></div>
            <div class="text-center p10"><span class="glyphicon glyphicon-remove" title="<?php echo Yii::t('global','Delete');?>" style="cursor: pointer;" onclick="delItem(<%= id%>);"></span></div>
        </div>
        <div class="pull-left">
            <textarea class="form-control customWH mb10" name="caption[]" placeholder="<?php echo Yii::t('labels','Description');?>"><%= val%></textarea>
            <input type="text" name="weight[]" class="form-control length_2 pull-left mr10" placeholder="<?php echo Yii::t('global','Sort Number');?>" value="<%= weight%>">
            <p class="form-control-static"><?php echo Yii::t('teaching','sort order, photos with small number displays first.');?></p>
        </div>
        <div class="clearfix"></div>
        <hr>
    </div>
</script>

<script>
    var photoData = <?php echo CJSON::encode($taskData['photoData'])?>;
    for(var i=0; i<photoData.length; i++){
        var item = _.template($('#photo-item').html(), photoData[i]);
        $('#items-class').append(item);
    }
    function callback(data)
    {
        var pids = [];
        $('#items-class input[name="pid[]"]').each(function(){
            pids.push( $(this).val() );
        });
        $.each(data, function(index, value){
            if(_.indexOf(pids, index) == -1){
                var item = _.template($('#photo-item').html(), {id: index, url: value, val: '', weight: '', isNew: 1});
                $('#items-class').append(item);
            }
        });
    }

    function callback1(data)
    {
        $('#items-class > div').each(function(){
            $(this).data('isnew', 0);
        });
    }

    function delItem(id)
    {
        if(confirm('<?php echo Yii::t("message","Sure to delete?");?>')){
            var obj=$('#item'+id);
            if(obj.data('isnew') == 1){
                obj.remove();
            }
            else{
                $.post('<?php echo $this->createUrl('delPhotoLink', array('category'=>'classreport'))?>', {pid: id, classid: classid, weeknum: weeknum}, function(){
                    obj.remove();
                });
            }
        }
    }
</script>