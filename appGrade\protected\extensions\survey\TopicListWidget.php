<?php
/*
 * 结构参见 http://www.cnblogs.com/davidhhuan/archive/2012/01/09/2316851.html
*/
class TopicListWidget extends CWidget {

	/*
	 * 孩子ID
	*/
	public $childId = 0;
	/*
	 * 问卷ID
	*/
	public $surveyId = 0;
	/*
	 * 题目是否显示双语
	*/
	public $topicDiglossia = false;
	/*
	 * 资源(images css js)的主目录
	*/
	public $assetsHomeUrl = null;
	/*
	 * 脚本文件 true 为默认 null 为不加载脚本文件
	*/
	public $scriptFile = true;
	/*
	 * css 文件 true 为默认 null 为不加载css文件
	*/
	public $cssFile = true;

	/*
	 * 是否允许反馈
	* 如果允许，则会添加ajax提交的按钮，否则 只供查看
	*/
	public $allowFeedback = false;
	/*
	 * 如果允许反馈的条件下
	* 设定请求地址 写法请参见 默认地址
	*/
	public $normalizeUrl = null;
	/*
	 * 自定义ajax提交的按钮
	*/
	public $ajaxSubmitButton = null;
	/*
	 * 视图名称
	*/
	public $view = 'topicList';

	/*
	 * 初始化 设定属性的默认值
	* 此方法会被 CController::beginWidget() 调用
	*/
	public function init() {

		Yii::import('ext.survey.actions.*');
		Yii::import('common.models.survey.*');
		if(empty($this->childId)){
			$this->childId  = $this->getController()->getChildId();
		}
		$cs = Yii::app()->getClientScript();
		if(empty($this->assetsHomeUrl)){
			$this->assetsHomeUrl = Yii::app()->getAssetManager()->publish(dirname(__FILE__).'/assets',false, -1, YII_DEBUG);
		}
		if($this->scriptFile == true){
			$this->scriptFile = 'survey.js';
		}
		//if($this->cssFile == true){
		//	$this->cssFile = 'survey.css';
		//}
		if(!empty($this->scriptFile)){
			$cs->registerScriptFile($this->assetsHomeUrl.'/js/'.$this->scriptFile,CClientScript::POS_HEAD);
		}
		//if(!empty($this->cssFile)){
		//	$cs->registerCssFile($this->assetsHomeUrl.'/css/'.$this->cssFile);
		//	$cs->registerCssFile(Yii::app()->theme->baseUrl.'/css/vote_survey.css?t='.Yii::app()->params['refreshAssets']);
		//}
		$cs->registerCssFile(Yii::app()->theme->baseUrl.'/css/survey.css?t='.Yii::app()->params['refreshAssets']);

	}
	/*
	 * 此方法会被 CController::endWidget() 调用
	*/
	public function run() {
		/*
		 * 为什么不放在 init 里 而放在这儿呢
		*/
		if(empty($this->normalizeUrl)){
			// 默认为 家长反馈
			// 如果是员工反馈 只需设置此属性并添加相应的处理请求的方法即可
			$this->normalizeUrl = CHtml::normalizeUrl(array('/child/survey/feedbackModel'));
		}
		if(empty($this->ajaxSubmitButton)){
			$ajaxSubmitButton = CHtml::ajaxSubmitButton(
					'Submit',
					$this->normalizeUrl,
					array(
							'type'=>'POST',
							'dataType'=>'JSON',
							'beforeSend'=>'beforeSendFunc',
							'success'=>'successFunc',
					)
			);
			//$this->ajaxSubmitButton = $ajaxSubmitButton;
			$this->ajaxSubmitButton = CHtml::ajaxLink(
					'<span>'.Yii::t("survey", 'Submit').'</span>',
					$this->normalizeUrl,
					array(
							'type'=>'POST',
							'dataType'=>'JSON',
							'beforeSend'=>'beforeSendFunc',
//							'beforeSend'=>'js:return false;',
							'success'=>'successFunc',
					),
					array(
						'id'=>'submitSurvey',
					)
			);
		}

		/*
		 * 因为 setNormalizeUrl
		*/

		$survey_cri = new CDbCriteria();
		$survey_cri->compare('status', 0);
		$survey_cri->compare('id', $this->surveyId);
		$survey_cri->limit = 1;
		// 得到问卷信息 目的是得到 模板ID的列表
		$survey_info = WSurvey::model()->find($survey_cri);
		$template_count = 0;
		if(!empty($survey_info->template_id)){
			$template_id = unserialize($survey_info->template_id);
			// 遍历模板ID
			foreach ($template_id as $tid) {
				$topic_cri = new CDbCriteria();
				$topic_cri->compare('t.status', 0);
				$topic_cri->compare('t.template_id', $tid);
				$topic_cri->order = 't.display_order ASC';
				// 分别取每一个模板下边的题目列表 这里既有 渴求式加载方式(with) 也有懒惰式加载(在视图文件中体现)
				// 注意 表关联 关系
				$topic_list[$tid] = WSurveyTopic::model()->with('optionGroup')->findAll($topic_cri);
				$template_count ++;
			}
			// 得到模板的标题信息
			$template_list = WSurveyTemplate::model()->getTemplateTitle($template_id,true);
		}
		$langKey = Yii::app()->language == 'en_us' ? 'en' : 'cn';
		// 输出 变量到 视图文件
		$view_data = array(
				'survey_info'=>$survey_info,
				'topic_list'=>$topic_list,
				'template_count'=>$template_count,
				'template_list'=>$template_list,
				'langKey'=>$langKey,
		);
		$this->render($this->view,$view_data);
	}

}