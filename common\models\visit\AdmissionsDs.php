<?php

/**
 * This is the model class for table "ivy_admissions_ds".
 *
 * The followings are the available columns in table 'ivy_admissions_ds':
 * @property integer $id
 * @property string $school_id
 * @property string $en_name
 * @property string $cn_name
 * @property string $en_name_last
 * @property string $en_name_middle
 * @property string $cn_name_last
 * @property string $gender
 * @property integer $birthday
 * @property integer $nationality
 * @property string $passportid
 * @property integer $valid_date
 * @property integer $resident_date
 * @property integer $native_lang
 * @property integer $other_lang
 * @property integer $start_date
 * @property string $start_grade
 * @property integer $has_xueji
 * @property integer $is_staff
 * @property integer $require_bus
 * @property string $home_address
 * @property string $start_year
 * @property string $pinyin_first
 * @property string $pinyin_last
 * @property string $school_history
 * @property string $sibling_info
 * @property string $father_name
 * @property string $father_nation
 * @property string $father_lang
 * @property string $father_home_lang
 * @property string $father_employer
 * @property string $father_position
 * @property string $father_education
 * @property string $father_phone
 * @property string $father_email
 * @property string $mother_name
 * @property string $mother_nation
 * @property string $mother_lang
 * @property string $mother_home_lang
 * @property string $mother_employer
 * @property string $mother_position
 * @property string $mother_education
 * @property string $mother_phone
 * @property string $mother_email
 * @property string $emergency_contact_name
 * @property string $emergency_contact_phone
 * @property string $emergency_contact_email
 * @property string $learn_diffculties
 * @property string $learn_support
 * @property string $special_needs
 * @property string $psychological_evaluation
 * @property string $been_expelled
 * @property string $medical_conditions
 * @property string $physical_disabilities
 * @property string $academic_report
 * @property string $persion_copy
 * @property string $recommendation_form
 * @property string $child_avatar
 * @property integer $add_timestamp
 * @property string $talent
 * @property integer $status
 * @property string $remark
 * @property string $school_roll
 * @property string $account_location
 * @property string $is_siblings
 * @property string $is_recommended
 */
class AdmissionsDs extends CActiveRecord
{

    const STATS_REGISTERED = 0;        	//申请完成的默认值  安排面试
    const STATS_BILLING = 10;             //开账单
    const STATS_PAYMENT = 20;             //缴费
    const STATS_HAS_BEEN_PAID = 30;             //缴费结束
    const STATS_WAITINTERVIEW = 35;             //缴费结束
    const STATS_INTERVIEN_DONE  = 37;             //面试完成
    const STATS__INTERVIEW_PASS = 40;            //面试通过
    const STATS__INTERVIEW_OUT = 41;            //面试未通过
    const STATS__INTERVIEW_ALTERNATE = 42;            //候补
    const STATS__TRANSFER_CHILD = 50;            //转移孩子
    const STATS__STATS_NOTCOMING = 51;            //转移孩子
    const STATS_STATS_TEMPORARY = 89;            //返回重写

    const STATS_DROPOUT = 99;           //失效状态

    public $avatarFile;
    public $reportFiles;
    public $recommendation;
    public $persionFiles;
    public $specialNeedsFile;
    public $evaluationFile;

    public $student_status1;
    public $student_status2;
    public $house1;
    public $house2;
    public $house3;
    public $visa_type;
    public $parentsSituation;
    public $social_security;
    public $number1;
    public $number2;
    public $interviewDate;
    public $interviewDate_t;

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_admissions_ds';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('gender, birthday, school_id, nationality, passportid, native_lang, start_grade, is_staff, home_address, home_address, add_timestamp', 'required'),
			array('birthday, nationality, valid_date, resident_date, native_lang, other_lang, has_xueji, is_staff, require_bus, add_timestamp', 'numerical', 'integerOnly'=>true),
			array('school_id, en_name, cn_name, en_name_last, en_name_middle, cn_name_last, gender, passportid, start_grade, father_name, father_nation, father_lang, father_home_lang, father_employer, father_position, father_education, father_phone, father_email, mother_name, mother_nation, mother_lang, mother_home_lang, mother_employer, mother_position, mother_education, mother_phone, mother_email, emergency_contact_name, emergency_contact_phone, emergency_contact_email, remark, account_location, is_visit', 'length', 'max'=>255),
			array('school_history, sibling_info, school_roll, learn_diffculties, learn_support, special_needs, psychological_evaluation, been_expelled, medical_conditions, physical_disabilities, academic_report, persion_copy, child_avatar, talent', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, school_id, en_name, cn_name, en_name_last, school_roll, en_name_middle, cn_name_last, pinyin_first, pinyin_last, start_year, gender, birthday, nationality, passportid, valid_date, resident_date, native_lang, other_lang, start_date, start_grade, has_xueji, is_staff, require_bus, home_address, school_history, sibling_info, father_name, father_nation, father_lang, father_home_lang, father_employer, father_position, father_education, father_phone, father_email, mother_name, mother_nation, mother_lang, mother_home_lang, mother_employer, mother_position, mother_education, mother_phone, mother_email, emergency_contact_name, emergency_contact_phone, emergency_contact_email, learn_diffculties, learn_support, special_needs, psychological_evaluation, been_expelled, medical_conditions, physical_disabilities, academic_report, persion_copy, recommendation_form, child_avatar, add_timestamp, talent, status, is_siblings, is_recommended ,remark, account_location, is_visit', 'safe', 'on'=>'search'),
            array("avatarFile", "file", "types" => "jpg, gif, png, jpeg", "maxSize" => 1024*1024*5, "allowEmpty" => true),
            array("specialNeedsFile, evaluationFile", "file", "types" => "pg, png, pdf, zip, xlsx, xls, docx, doc", "maxSize" => 1024*1024*5, "allowEmpty" => true),
            array("reportFiles, recommendation, persionFiles", "file", "types" => "pg, png, pdf, zip, xlsx, xls, docx, doc", "maxSize" => 1024*1024*5, "allowEmpty" => true),
        );
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'school_id' => Yii::t('user', '校园'),
			'en_name' => Yii::t('user', '英文姓'),
			'cn_name' => Yii::t('user', '中文姓'),
			'en_name_last' => Yii::t('user', '英文名'),
			'en_name_middle' => Yii::t('user', '英文中间名'),
			'cn_name_last' => Yii::t('user', '中文名'),
			'pinyin_first' => Yii::t('user', '拼音姓'),
			'pinyin_last' => Yii::t('user', '拼音名'),
			'start_year' => Yii::t('user', '入学年份'),
			'gender' => Yii::t('user', 'Gender'),
			'birthday' => Yii::t('user', 'Date Of Bith'),
			'nationality' => Yii::t('user', 'Nationality'),
			'passportid' => Yii::t('user', 'Passport/ID Card No.'),
			'valid_date' => Yii::t('user', 'Valid Date for Visa'),
			'resident_date' => Yii::t('user', 'Valid Date for Resident Permit'),
			'native_lang' => Yii::t('user', 'Native Language'),
			'other_lang' => Yii::t('user', 'Other Languages'),
			'start_date' => Yii::t('user', 'Requested Starting Date'),
			'start_grade' => Yii::t('user', 'Requested Starting Gender'),
			'has_xueji' => Yii::t('user', 'Requires Xueji (Chinese student only)'),
			'is_staff' => Yii::t('user', 'IVY/Daystar Staff'),
			'require_bus' => Yii::t('user', 'Requires Bus Service'),
			'home_address' => Yii::t('user', 'Home Address'),
			'school_history' => Yii::t('user', 'School History'),
			'sibling_info' => Yii::t('user', 'Sibling Information'),
			'father_name' => Yii::t('user', 'Father Name'),
			'father_nation' => Yii::t('user', 'Father Nation'),
			'father_lang' => Yii::t('user', 'Father Lang'),
			'father_home_lang' => Yii::t('user', 'Father Home Lang'),
            'father_employer' => Yii::t('user', 'Father Employer'),
            'father_position' => Yii::t('user', 'Father Position'),
            'father_education' => Yii::t('user', 'Father Education'),
            'father_phone' => Yii::t('user', 'Father Phone'),
            'father_email' => Yii::t('user', 'Father Email'),
            'mother_name' => Yii::t('user', 'Mother Name'),
            'mother_nation' => Yii::t('user', 'Mother Nation'),
            'mother_lang' => Yii::t('user', 'Mother Lang'),
            'mother_home_lang' => Yii::t('user', 'Mother Home Lang'),
            'mother_employer' => Yii::t('user', 'Mother Employer'),
            'mother_position' => Yii::t('user', 'Mother Position'),
            'mother_education' => Yii::t('user', 'Mother Education'),
            'mother_phone' => Yii::t('user', 'Mother Phone'),
            'mother_email' => Yii::t('user', 'Mother Email'),
            'emergency_contact_name' => Yii::t('user', 'Emergency Contact'),
            'emergency_contact_phone' => Yii::t('user', 'Phone Number'),
            'emergency_contact_email' => Yii::t('user', 'Email'),
            'learn_diffculties' => Yii::t('user', 'Learning Difficulties'),
            'learn_support' => Yii::t('user', 'Tutorial'),
            'special_needs' => Yii::t('user', 'Special education course attachment'),
            'psychological_evaluation' => Yii::t('user', 'Psychological consultation attachment'),
            'been_expelled' => Yii::t('user', 'Ever been fired'),
            'medical_conditions' => Yii::t('user', 'Health problems'),
            'physical_disabilities' => Yii::t('user', 'Disability or disorder'),
            'academic_report' => Yii::t('user', 'Copies of progress reports or transcripts for the last two years'),
            'persion_copy' => Yii::t('user', 'Hu kou book or passport attachment'),
            'recommendation_form' => Yii::t('user', 'Recommendation Form'),
            'child_avatar' => Yii::t('user', 'Child Avatar'),
            'add_timestamp' => Yii::t('user', 'Application date'),
            'talent' => Yii::t('user', 'Talent'),
            'status' => Yii::t('user', '状态'),
            'remark' => Yii::t('user', 'Remark'),
			'account_location' => 'Account Location',
			'school_roll' => 'school Roll',
			'is_visit' => 'is Visit',
			'student_status1' => Yii::t('user','户口所在地'),
			'is_siblings' => Yii::t('user','是否已有子女在启明星就读'),
			'is_recommended' => Yii::t('user','是否为启明星家长推荐'),
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('school_id',$this->school_id,true);
		$criteria->compare('en_name',$this->en_name,true);
		$criteria->compare('cn_name',$this->cn_name,true);
		$criteria->compare('en_name_last',$this->en_name_last,true);
		$criteria->compare('en_name_middle',$this->en_name_middle,true);
		$criteria->compare('cn_name_last',$this->cn_name_last,true);
		$criteria->compare('gender',$this->gender,true);
		$criteria->compare('start_year',$this->start_year,true);
		$criteria->compare('pinyin_first',$this->pinyin_first,true);
		$criteria->compare('pinyin_last',$this->pinyin_last,true);
		$criteria->compare('birthday',$this->birthday);
		$criteria->compare('nationality',$this->nationality);
		$criteria->compare('passportid',$this->passportid,true);
		$criteria->compare('valid_date',$this->valid_date);
		$criteria->compare('resident_date',$this->resident_date);
		$criteria->compare('native_lang',$this->native_lang);
		$criteria->compare('other_lang',$this->other_lang);
		$criteria->compare('start_date',$this->start_date);
		$criteria->compare('start_grade',$this->start_grade,true);
		$criteria->compare('has_xueji',$this->has_xueji);
		$criteria->compare('is_staff',$this->is_staff);
		$criteria->compare('require_bus',$this->require_bus);
		$criteria->compare('home_address',$this->home_address,true);
		$criteria->compare('school_history',$this->school_history,true);
		$criteria->compare('sibling_info',$this->sibling_info,true);
		$criteria->compare('father_name',$this->father_name,true);
		$criteria->compare('father_nation',$this->father_nation,true);
		$criteria->compare('father_lang',$this->father_lang,true);
		$criteria->compare('father_home_lang',$this->father_home_lang,true);
		$criteria->compare('father_employer',$this->father_employer,true);
		$criteria->compare('father_position',$this->father_position,true);
		$criteria->compare('father_education',$this->father_education,true);
		$criteria->compare('father_phone',$this->father_phone,true);
		$criteria->compare('father_email',$this->father_email,true);
		$criteria->compare('mother_name',$this->mother_name,true);
		$criteria->compare('mother_nation',$this->mother_nation,true);
		$criteria->compare('mother_lang',$this->mother_lang,true);
		$criteria->compare('mother_home_lang',$this->mother_home_lang,true);
		$criteria->compare('mother_employer',$this->mother_employer,true);
		$criteria->compare('mother_position',$this->mother_position,true);
		$criteria->compare('mother_education',$this->mother_education,true);
		$criteria->compare('mother_phone',$this->mother_phone,true);
		$criteria->compare('mother_email',$this->mother_email,true);
		$criteria->compare('emergency_contact_name',$this->emergency_contact_name,true);
		$criteria->compare('emergency_contact_phone',$this->emergency_contact_phone,true);
		$criteria->compare('emergency_contact_email',$this->emergency_contact_email,true);
		$criteria->compare('learn_diffculties',$this->learn_diffculties,true);
		$criteria->compare('learn_support',$this->learn_support,true);
		$criteria->compare('special_needs',$this->special_needs,true);
		$criteria->compare('psychological_evaluation',$this->psychological_evaluation,true);
		$criteria->compare('been_expelled',$this->been_expelled,true);
		$criteria->compare('medical_conditions',$this->medical_conditions,true);
		$criteria->compare('physical_disabilities',$this->physical_disabilities,true);
		$criteria->compare('academic_report',$this->academic_report,true);
		$criteria->compare('persion_copy',$this->persion_copy,true);
		$criteria->compare('recommendation_form',$this->recommendation_form,true);
		$criteria->compare('child_avatar',$this->child_avatar,true);
		$criteria->compare('add_timestamp',$this->add_timestamp);
		$criteria->compare('talent',$this->talent,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('remark',$this->remark,true);
		$criteria->compare('account_location',$this->account_location,true);
		$criteria->compare('school_roll',$this->school_roll,true);
		$criteria->compare('is_visit',$this->is_visit,true);
		$criteria->compare('is_siblings',$this->is_siblings,true);
		$criteria->compare('is_recommended',$this->is_recommended,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AdmissionsDs the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}


    public static function getConfig($lang=null)
    {
        return array(

            //性别
            'gender' => array(
                1 => Yii::t('user', 'Male'),
                2 => Yii::t('user', 'Female')
            ),
            'grade' => array(
                //1 => Yii::t('user', 'Toddler (Age 2-3)', array(), null, $lang),
                2 => Yii::t('user', 'Casa (Age 3-6)', array(), null, $lang),
                3 => Yii::t('user', 'Kinder (Age 5-6)', array(), null, $lang),
                4 => Yii::t('user', 'Grade 1', array(), null, $lang),
                5 => Yii::t('user', 'Grade 2', array(), null, $lang),
                6 => Yii::t('user', 'Grade 3', array(), null, $lang),
                7 => Yii::t('user', 'Grade 4', array(), null, $lang),
                8 => Yii::t('user', 'Grade 5', array(), null, $lang),
                9 => Yii::t('user', 'Grade 6', array(), null, $lang),
                10 => Yii::t('user', 'Grade 7', array(), null, $lang),
                11=> Yii::t('user', 'Grade 8', array(), null, $lang),
                12=> Yii::t('user', 'Grade 9', array(), null, $lang),
                13=> Yii::t('user', 'Grade 10', array(), null, $lang),
            ),

            //学历
            'education' => array(
                1 => Yii::t('user', 'College', array(), null, $lang),
                2 => Yii::t('user', 'Undergraduate', array(), null, $lang),
                3 => Yii::t('user', 'Postgraduate', array(), null, $lang),
                4 => Yii::t('user', 'Doctorate', array(), null, $lang),
                5 => Yii::t('user', 'Others', array(), null, $lang),
            ),


            'grade_ayalt' => array(
                21 => Yii::t('user', 'Nursery', array(), null, $lang),
                22 => Yii::t('user', 'Pre-K1', array(), null, $lang),
                23 => Yii::t('user', 'Pre-K2', array(), null, $lang),
                24 => Yii::t('user', 'Kindergarten', array(), null, $lang),
            ),
            'grade_slt' => array(
                4 => Yii::t('user', 'Grade 1', array(), null, $lang),
                5 => Yii::t('user', 'Grade 2', array(), null, $lang),
                6 => Yii::t('user', 'Grade 3', array(), null, $lang),
                7 => Yii::t('user', 'Grade 4', array(), null, $lang),
                8 => Yii::t('user', 'Grade 5', array(), null, $lang),
            ),

            //面试状态
            'states' => array(
                AdmissionsDs::STATS_REGISTERED => Yii::t('management', '默认', array(), null, $lang),
                AdmissionsDs::STATS_BILLING => Yii::t('management', '已安排面试账单未开', array(), null, $lang),
                AdmissionsDs::STATS_PAYMENT => Yii::t('management', '等待缴费', array(), null, $lang),
                AdmissionsDs::STATS_HAS_BEEN_PAID => Yii::t('management', '已缴费 ( 未安排面试 )', array(), null, $lang),
                AdmissionsDs::STATS_WAITINTERVIEW => Yii::t('management', '等待面试', array(), null, $lang),
                AdmissionsDs::STATS_INTERVIEN_DONE => Yii::t('management', '面试结束', array(), null, $lang),
                AdmissionsDs::STATS__INTERVIEW_PASS => Yii::t('management', 'Interview passed', array(), null, $lang),
                AdmissionsDs::STATS__INTERVIEW_OUT => Yii::t('management', 'Interview failed', array(), null, $lang),
                AdmissionsDs::STATS__INTERVIEW_ALTERNATE => Yii::t('management', '候补学生', array(), null, $lang),
                AdmissionsDs::STATS__TRANSFER_CHILD => Yii::t('management', 'Admission in progress', array(), null, $lang),
                AdmissionsDs::STATS_DROPOUT => Yii::t('management', '过期', array(), null, $lang),
                AdmissionsDs::STATS_STATS_TEMPORARY => Yii::t('management', '驳回重填', array(), null, $lang),
                AdmissionsDs::STATS__STATS_NOTCOMING => Yii::t('management', '录取不来', array(), null, $lang),
            ),
            'language' => Diglossia::model()->getLanguage(),
            'countries' => Country::model()->getCountryList(),

            'student_status' => array(
                1 => '京籍朝阳',
                2 => '京籍外区',
                3 => '非京籍（含中国香港、澳门、台湾）',
                4 => '外籍'
            ),
            'house' => array(
                1 => '朝阳房产',
                2 => '朝阳租房',
                3 => '朝阳无租房无房产',
            ),
            'visa_type' => array(
                1 => '旅行证',
                2 => '通行证',
                3 => '外交签证',
                4 => '工作签',
                5 => '其他',
            ),
            'yes_no' => array(
                1 => '是',
                2 => '否',
            ),
            'student_status2' => array(
                1 => '北京市朝阳区学籍',
                2 => '北京市非朝阳区学籍',
                3 => '外省市学籍',
                4 => '无学籍'
            ),
            'grade_en' => array(
                //1 => Yii::t('user', 'Toddler (Age 2-3)', array(), null, $lang),
                2 => 'Casa (Age 3-6)',
                3 => 'Kinder (Age 5-6)',
                4 => 'Grade 1',
                5 => 'Grade 2',
                6 => 'Grade 3',
                7 => 'Grade 4',
                8 => 'Grade 5',
                9 => 'Grade 6',
                10 => 'Grade 7',
                11=> 'Grade 8',
                12=> 'Grade 9',
                13=> 'Grade 10',
            ),
        );
    }

    protected function beforeSave()
    {
        if (parent::beforeSave()) {

            $avatarFile = CUploadedFile::getInstance($this, 'avatarFile');
            $specialNeedsFile = CUploadedFile::getInstance($this, 'specialNeedsFile');
            $evaluationFile = CUploadedFile::getInstance($this, 'evaluationFile');
            $reportFiles = CUploadedFile::getInstances($this, 'reportFiles');
            $recommendation = CUploadedFile::getInstances($this, 'recommendation');
            $persionFiles = CUploadedFile::getInstances($this, 'persionFiles');

            $oss = CommonUtils::initOSS('private');
            $objectPath = 'admissions/' . date('Ym') . '/';
            $id = uniqid();

            if ($avatarFile) {
                $newFileName = $id . '_avatar' .'.'. $avatarFile->getExtensionName();

                if ($oss->uploadFile($objectPath . $newFileName, $avatarFile->getTempName())) {
                    // 删除旧附件
                    if ($this->child_avatar) {
                        $oss->delete_object('admissions/' . $this->child_avatar);
                    }
                    $this->child_avatar = date('Ym', time()) . '/' . $newFileName;
                }
            }

            if ($specialNeedsFile) {
                $newFileName = $id . '_specialneeds' .'.'. $specialNeedsFile->getExtensionName();

                if ($oss->uploadFile($objectPath . $newFileName, $specialNeedsFile->getTempName())) {
                    // 删除旧附件
                    if ($this->specialNeedsFile) {
                        $oss->delete_object('admissions/' . $this->specialNeedsFile);
                    }
                    $this->special_needs = date('Ym', time()) . '/' . $newFileName;
                }
            }

            if ($evaluationFile) {
                $newFileName = $id . '_evaluation' .'.'. $evaluationFile->getExtensionName();

                if ($oss->uploadFile($objectPath . $newFileName, $evaluationFile->getTempName())) {
                    // 删除旧附件
                    if ($this->evaluationFile) {
                        $oss->delete_object('admissions/' . $this->evaluationFile);
                    }
                    $this->psychological_evaluation = date('Ym', time()) . '/' . $newFileName;
                }
            }

            if ($reportFiles) {
                $filesArray = array();
                $i = 0;
                foreach ($reportFiles as $file) {
                    $newFileName = $id . '_report_' .$i.'.'. $file->getExtensionName();

                    if ($oss->uploadFile($objectPath . $newFileName, $file->getTempName())) {
                        $filesArray[] = date('Ym', time()) . '/' . $newFileName;
                    }
                    $i++;
                }
                if (!empty($filesArray)) {
                    foreach (json_decode($this->academic_report) as $v) {
                        // 删除旧附件
                        if ($v) {
                            $oss->delete_object('admissions/' . $v);
                        }
                    }
                    $this->academic_report = json_encode($filesArray);
                }
            }

            if ($recommendation) {
                $filesArray = array();
                $i = 0;
                foreach ($recommendation as $file) {
                    $newFileName = $id . '_report_' .$i.'.'. $file->getExtensionName();

                    if ($oss->uploadFile($objectPath . $newFileName, $file->getTempName())) {
                        $filesArray[] = date('Ym', time()) . '/' . $newFileName;
                    }
                    $i++;
                }
                if (!empty($filesArray)) {
                    foreach (json_decode($this->recommendation_form) as $v) {
                        // 删除旧附件
                        if ($v) {
                            $oss->delete_object('admissions/' . $v);
                        }
                    }
                    $this->recommendation_form = json_encode($filesArray);
                }
            }

            if ($persionFiles) {
                $filesArray = array();
                $i=0;
                foreach ($persionFiles as $file) {
                    $newFileName = $id . '_person_' .$i.'.'. $file->getExtensionName();

                    if ($oss->uploadFile($objectPath . $newFileName, $file->getTempName())) {
                        $filesArray[] = date('Ym', time()) . '/' . $newFileName;
                    }
                    $i++;
                }
                if (!empty($filesArray)) {
                    foreach (json_decode($this->persion_copy) as $v) {
                        // 删除旧附件
                        if ($v) {
                            $oss->delete_object('admissions/' . $v);
                        }
                    }
                    $this->persion_copy = json_encode($filesArray);
                }
            }
            return true;
        }
        return false;
    }

    public function getCatename($cate)
    {
        $cfg = $this->getConfig();
        return $cfg[$cate][$this->start_grade];
    }

    public function getCatestates($cate)
    {
        $cfg = $this->getConfig();
        return $cfg[$cate][$this->status];
    }

    public function getName()
    {
        switch (Yii::app()->language) {
            case "zh_cn":
                return ($this->cn_name) ? $this->cn_name . $this->cn_name_last : $this->en_name . " " . $this->en_name_middle  . " " . $this->en_name_last ;
                break;
            case "en_us":
                return ($this->en_name) ? $this->en_name . " " . $this->en_name_middle  . " " . $this->en_name_last : $this->pinyin_first . $this->pinyin_last;
                break;
        }
    }

    // 修改孩子状态
    public function changeStatus($status)
    {
        if ($this->status < $status) {
            $this->status = $status;
        }
        if ($this->save()) {
            return true;
        }
        return false;
    }

    // 计算岁数   $data = 时间戳 return  5岁11月
    public function computeAge($data) {
        $birthday = date("Y-m-d", $data);
        list($byear, $bmonth, $bday) = explode('-', $birthday);
        list($year, $month, $day) = explode('-', date('Y-m-d'));
        $bmonth = intval($bmonth);
        $bday = intval($bday);
        if ($bmonth < 10) {
            $bmonth = '0' . $bmonth;
        }
        if ($bday < 10) {
            $bday = '0' . $bday;
        }
        $bi = intval($byear . $bmonth . $bday);
        $ni = intval($year . $month . $day);
        $not_birth = 0;
        if ($bi > $ni) {
            $not_birth = 1;
            $tmp = array($byear, $bmonth, $bday);
            list($byear, $bmonth, $bday) = array($year, $month, $day);
            list($year, $month, $day) = $tmp;
            list($bi, $ni) = array($ni, $bi);
        }
        $years = 0;
        while (($bi + 10000) <= $ni) {//先取岁数
            $bi += 10000;
            $years++;
            $byear++;
        }//得到岁数后 抛弃年
        list($m, $d) = AdmissionsDs::getMD(array($year, $month, $day), array($byear, $bmonth, $bday));
        // $years . '岁' . $m . '月' . $d .'天'
        $ageData = '';
        if($years && $m){
            $ageData = $years . ' 岁 ' . $m . ' 月';
        }else if ($years && !$m){
            $ageData = $years . ' 岁';
        }else if (!$years && $m){
            $ageData = $m . ' 月';
        }
        return $ageData;
    }

    /**
     * 只能用于一年内计算
     * @param type $ymd
     * @param type $bymd
     */
    public function getMD($ymd, $bymd) {
        list($y, $m, $d) = $ymd;
        list($by, $bm, $bd) = $bymd;
        if (($m . $d) < ($bm . $bd)) {
            $m +=12;
        }
        $month = 0;
        while ((($bm . $bd) + 100) <= ($m . $d)) {
            $bm++;
            $month++;
        }
        if ($bd <= $d) {//同处一个月
            $day = $d - $bd;
        } else {//少一个月
            $mdays = $bm > 12 ? AdmissionsDs::_getMothDay( ++$by, $bm - 12) : AdmissionsDs::_getMothDay($by, $bm);
            $day = $mdays - $bd + $d;
        }
        return array($month, $day);
    }

    public function _getMothDay($year, $month) {
        switch ($month) {
            case 1:
            case 3:
            case 5:
            case 7:
            case 8:
            case 10:
            case 12:
                $day = 31;
                break;
            case 2:
                $day = (intval($year % 4) ? 28 : 29); //能被4除尽的为29天其他28天
                break;
            default:
                $day = 30;
                break;
        }
        return $day;
    }
}
