<?php
class WorkflowApi extends Workflow{
    public $operationId;
    public $endNode = false;
    
    public function getOperationObj(){
        if (empty($this->opertationObj)){
            Yii::import('common.models.workflow.WorkflowOperation');
            $this->opertationObj = WorkflowOperation::model()->findByPk($this->operationId);
        }
    }

    /**
    * 工作流节点完成发送邮件
    * @param string $subject 邮件标题
    * @param string $body    邮件内容
    * @param boolean $addCC  是否抄送给当前节点用户
    * @param int $nodeId     工作流节点ID
    * @return boolean
    */
    public function sendEmail($subject,$body,$addCC=false,$nodeId=0){
        return true;
        Yii::import('application.components.OA');
        $mailer = Yii::createComponent('common.extensions.mailer.Aliyun');
        //判断工作流发信是否为最后节点
        $mailAddress = '';
        if ($this->isEndNode() === true || $this->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_OPDENY || $this->endNode){
            $userList = $this->getSavedUserRoleList($this->nodeIds);
            foreach ($userList as $val){
                if ($val['nodeid'] != $this->nodeObj->node_id)
                {
                   $mailer->AddAddress($val['email']); 
                   $mailAddress .= ($mailAddress) ? ';'. $val['email']: $val['email'];
                }else{
                    if ($addCC == true){
                        $mailer->AddCC($val['email']); 
                    }
                }
            }
        }else{
            $nodeId = ($nodeId) ? $nodeId : $this->getNextNodeId();
            $userList = $this->getSavedUserRoleList($nodeId);
            foreach ($userList as $val){
                $mailer->AddAddress($val['email']); 
                $mailAddress .= ($mailAddress) ? ';'. $val['email']: $val['email'];
            }
            if ($addCC == true){
                $userList = $this->getNodeAllowModUser();
                foreach ($userList as $val){
                    $mailer->AddAddress($val['email']); 
                }
            }
        }
        //抄送给工作流发起人
        $cc = '';
        if (!$this->isEndNode()) {
            $cc = $this->opertationObj->userInfo->email;
            if ($cc) {
                $mailer->AddCC($cc);
            }
        }
        $body = (OA::isProduction()) ? $body : $body."<p style='color:red;'> 正式环境邮件会发送给：{$mailAddress}，抄送：{$cc}</p>";
        $mailer->Subject = (OA::isProduction()) ?  $subject : '[Test] '.$subject ;
        $mailer->iniMail( OA::isProduction() ); // 此行代码要放到AddAddress, AddCC方法下面
        $mailer->getView('workflow', array('body' => $body), 'main');
        if (!$mailer->Send()){
            return false;
        }
        return true;
    }
    
    /**
    * 获取工作流进程数据
    * @param boolean $currentNode 默认获取所有进程数据
    * @return object 进程对象或进程数组对象
    */
    public function getWorkflowProcess($currentNode = false){
        if (!empty($this->opertationObj)){
            
            if ($currentNode === true){
                $crite = new CDbCriteria;
                $crite->compare('operation_id', $this->opertationObj->id);
                $crite->compare('current_node_index', $this->nodeObj->node_id);
                $crite->order = 'updatetime ASC';
                $processModel = WorkflowProcess::model()->find($crite);
            }else{
                $crite = new CDbCriteria;
                $crite->compare('operation_id', $this->opertationObj->id);
                $crite->order = 'updatetime ASC';
                $processModel = WorkflowProcess::model()->with('user')->findAll($crite);
            }
            return $processModel;
        }
        return null;
    }
    
    /**
    * 读取证明资料
    * @return array 返回附件数组
    */
    public function getAttachmentList(){
        if (!$this->opertationObj->id) {
            return null;
        }
        $crite = new CDbCriteria;
        $crite->compare('link_id', $this->opertationObj->id);
        $crite->compare('mod_name', 'workflow');
        // $crite->compare('func_name', 'certificate');
        $crite->order = 'id ASC';
        $model = Uploads::model()->findAll($crite);
        if (!empty($model)) {
            $thumb_type = array('gif', 'jpeg', 'pjpeg', 'png' ,'jpg');
            foreach ($model as $k => $val) {
                $file_ext = strtolower(pathinfo($val->file_name, PATHINFO_EXTENSION));
                $attachmentList[$k]['is_image'] = false;
                // if (in_array($file_ext, $thumb_type)) {
                //     $attachmentList[$k]['is_image'] = true;
                //     $attachmentList[$k]['images'] = Yii::app()->params['OABaseUrl'] . "/modules/myspace/task/getthumb.php?type=small&img=" . $val->file_name;
                // } else {
                //     $attachmentList[$k]['images'] = Yii::app()->params['OABaseUrl'] . "/images/filetype/file_ext_" . $file_ext . ".png";
                // }
                // $attachmentList[$k]['bigimages'] = Yii::app()->params['OABaseUrl'] . "/modules/myspace/task/getthumb.php?img=" . $val->file_name;
                $attachmentList[$k]['images'] = sprintf("/backend/default/attachment?file=%s", $val->file_name);
                $attachmentList[$k]['bigimages'] = sprintf("/backend/default/attachment?file=%s", $val->file_name);
                $attachmentList[$k]['notes'] = $val->notes;
                $attachmentList[$k]['id'] = $val->id;
            }
            return $attachmentList;
        }
        return null;
    }
    
    /**
    * 删除已发起的工作流程（仅限第一个结点）
    * @return boolean
    */
    public function deleteOperationNode($transaction){
        //判断是否有删除节点权限
        if ($this->isFristNode() === true && $this->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_STEP) {
            if (!empty($this->opertationObj)){
                //delete process
                if (!WorkflowProcess::model()->deleteAllByAttributes(array('operation_id'=>$this->getOperateId()))){
                    $transaction->rollBack();
                    return false;
                }
                //delete role
                if (!WorkflowRole::model()->deleteAllByAttributes(array('operation_id'=>$this->opertationObj->id))){
                    $transaction->rollBack();
                    return false;
                }
                //delete operation
                if (!$this->opertationObj->delete()){
                    $transaction->rollBack();
                    return false;
                }else{
                    WorkflowOperationDetail::model()->deleteAllByAttributes(array('operation_id'=>$this->getOperateId()));
                }
                return true;
            }
        }
        return false;
    }
    
    /**
    * 工作流程中间节点保存跳转函数
    * @param string  $desc           进程描述函数
    * @param int     $nodeStatus     流程状态
    * @param object  $transaction    开启事务对象
    * @param array   $userList 外部传过用户
    * @return boolean
    */
    public function saveWorkflow($desc,$nodeStatus,$transaction,$userList=array()){
        //状态是拒绝或操作为最后一节点
        if (($nodeStatus == WorkflowOperation::WORKFLOW_STATS_OPDENY) || ($this->isEndNode()=== true) || $this->endNode){
            //更新当前用户权限状态
            if (!WorkflowRole::model()->updateAll(array('state'=>WorkflowOperation::WORKFLOW_STATS_OPED),'operation_id=:operation_id and user_node_id=:user_node_id and assign_user=:assign_user', 
                               array(':operation_id'=>$this->opertationObj->id,':user_node_id'=>$this->nodeObj->node_id,':assign_user'=>Yii::app()->user->id))){
                $transaction->rollBack();
                return false;
            }
            if (!empty($this->opertationObj)){
                $this->opertationObj->current_node_index = $this->nodeObj->node_id;
                $this->opertationObj->state = $nodeStatus;
                $this->opertationObj->end_time = time();
                if (!$this->opertationObj->save()){
                    $transaction->rollBack();
                    return false;
                }
                if (!WorkflowRole::model()->updateAll(array('current_operator'=>WorkflowRole::WORKFLOW_ROLE_CURRENT_CHECKED_USER),'operation_id=:operation_id and user_node_id=:user_node_id', 
                           array(':operation_id'=>$this->opertationObj->id,':user_node_id'=>$this->nodeObj->node_id))){
                    $transaction->rollBack();
                    return false;
                }

                if ($this->saveWorkflowProess(true,$desc,$nodeStatus,$transaction) === false){
                    return false;
                }
           }
        }elseif ($nodeStatus == WorkflowOperation::WORKFLOW_STATS_UNOP) {
            //如果用户停留在某一个节点
            $this->opertationObj->current_node_index = $this->getNextNodeId();
            if (!$this->opertationObj->save()){
                $transaction->rollBack();
                return false;
            }
            if (!WorkflowRole::model()->updateAll(array('state'=>WorkflowOperation::WORKFLOW_STATS_OPED),'operation_id=:operation_id and user_node_id=:user_node_id and assign_user=:assign_user', 
                               array(':operation_id'=>$this->opertationObj->id,':user_node_id'=>$this->nodeObj->node_id,':assign_user'=>Yii::app()->user->id))){
                $transaction->rollBack();
                return false;
            }
            if (!WorkflowRole::model()->updateAll(array('current_operator'=>WorkflowRole::WORKFLOW_ROLE_CURRENT_CHECKED_USER),'operation_id=:operation_id and user_node_id=:user_node_id', 
                           array(':operation_id'=>$this->opertationObj->id,':user_node_id'=>$this->nodeObj->node_id))){
                    $transaction->rollBack();
                    return false;
            }
            //创建下一节点审核用户
            if ($this->createRole($this->getNextNodeId(), $this->opertationObj->id,$transaction,$userList) === false){
                return false;
            }
            //更新进程状态
            if ($this->saveWorkflowProess(false,$desc,$nodeStatus,$transaction) === false){
                return false;
            }
        }elseif ($nodeStatus == WorkflowOperation::WORKFLOW_STATS_STEP){
            //更新进程状态
            if ($this->saveWorkflowProess(false,$desc,$nodeStatus,$transaction) === false){
                return false;
            }
        }elseif ($nodeStatus == WorkflowOperation::WORKFLOW_STATS_RESET){
            //更新进程状态
            if ($this->saveWorkflowProess(false,$desc,$nodeStatus,$transaction) === false){
                return false;
            }
        }
        return true;
    }
    
    /**
    * 工作流程进程保存函数
    * @param int $processId 进程ID
    * @param boolean $proessFinish 进程默认没有结束
    * @return boolean
    */
    public function saveWorkflowProess($proessFinish=false,$desc=null,$nodeStatus=null,$transaction){
        $crit = new CDbCriteria();
        $crit->compare('operation_id', $this->getOperateId());
        $crit->compare('current_node_index', $this->getCurrentNodeId());
        $crit->compare('state', 0);
        $processModel = WorkflowProcess::model()->find($crit);
        if (empty($processModel)){
            $processModel = new WorkflowProcess();
        }
        $processModel->defination_id = $this->definationObj->defination_id;
        $processModel->operation_id = $this->getOperateId();
        $processModel->process_desc = $desc;
        $processModel->current_node_index = $this->getCurrentNodeId();
        $processModel->start_time = time();
        if ($proessFinish == true){
            $processModel->finish_time = time();
        }
        $processModel->state = $nodeStatus;
        $processModel->start_user = Yii::app()->user->id;
        $processModel->updatetime = time();
        if (!$processModel->save()){
            $transaction->rollBack();
            return false;
        }
        return true;
    }
    
    /**
     * 工作业务流程保存
     * @param array $data 
     * @return boolean | WorkflowOperation
     */
    public function saveOperation($data,$transaction){
        extract($data);
        if (isset($id) && $id){
            $opModel = WorkflowOperation::model()->findByPk($id);
        }else{
            $opModel = new WorkflowOperation();
        }
        $opModel->operation_type= $operation_type;
        $opModel->operation_object_id = $operation_object_id;
        $opModel->defination_id = $defination_id;
        $opModel->branchid = $branchid;
        $opModel->state = $state;
        $opModel->current_node_index = $current_node_index;
        $opModel->exte1 = $exte1;
        $opModel->exte2 = $exte2;
        $opModel->exte3 = $exte3;
        $opModel->exte4 = $exte4;
        $opModel->start_user = Yii::app()->user->id;
        $opModel->start_time = time();
        if (!$opModel->save()){
            $transaction->rollBack();
            return false;
        }
        $this->opertationObj = $opModel;
        return $opModel;
    }
    
    /**
     * 工作流业务中间表保存
     */
    public function saveOperationLink($data,$transaction){
        if (is_array($data['relationList']) && count($data['relationList'])){
            foreach ($data['relationList'] as $operation_object_id){
                $link = new WorkflowOperationDetail();
                $link->operation_id = $data['operation_id'];
                $link->operation_object_id = $operation_object_id;
                if (!$link->save()){
                    $transaction->rollBack();
                    return false;
                }
            }
        }
        return true;
    }
    
    /**
     * 第一个节点（终级）保存函数
     * @param array  $operation          业务流水表必要字段
     * @param array  $relationList       业务关系中间表必要字段
     * @param object $transaction        事务开启对象
     * @param array $userList            外部传过用户(uid=>0)
     * @return boolean
     */
    public function saveFirstNodeForWorkflow($operation,$relationList=array(),$memo,$transaction,$userList=array()){
        //工作流业务流水表
        $operationRet = $this->saveOperation($operation,$transaction);
        if ($operationRet === false){
            return $operationRet;
        }
        //工作流业务中间表
        $link = $this->saveOperationLink(array('operation_id'=>$operationRet->id,'relationList'=>$relationList),$transaction);
        if ($link === false){
            return false;
        }

        $firstUser = array();
        //工作流权限创建
        $handle = ucfirst($this->definationObj->defination_handle);
        if (in_array($handle, array('Discount', 'DiscountIvy'))) {
            $firstUser = array();
        } else {
            $firstUser = $userList;
            $userList = array();
        }

        // 创建第一步的角色
        $role = $this->createRole($this->nodeObj->node_id,$operationRet->id,$transaction,$firstUser);
        if ($role === false){
            return false;
        }
        //更新工作流低层流程
        if ($this->saveWorkflow($memo,$this->opertationObj->state,$transaction,$userList) === false){
            return false;
        }
        return $operationRet->id;
    }
    
    /**
     * 获取进度节点列表
     */
    public function getNodeListUseHtml(){
        $config = WorkflowConfig::getType($this->getOperateValue('operation_type'),$this->getOperateObjId());
        $branchList = Branch::model()->getBranchList();
        switch ($this->getOperateValue('state')){
            case WorkflowOperation::WORKFLOW_STATS_STEP:
                $activeLabel = 'active';
                break;
            case WorkflowOperation::WORKFLOW_STATS_UNOP:
                $activeLabel = 'success';
                break;
            case WorkflowOperation::WORKFLOW_STATS_OPED:
                $activeLabel = 'done';
                break;
            case WorkflowOperation::WORKFLOW_STATS_OPDENY:
                $activeLabel = 'rejected';
                break;
        }
        $str = CHtml::tag('div',array('class'=>'task-data','id'=>'operation_id_'.$this->getOperateId()),false,false);
            if ($this->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_STEP):
                $str .= CHtml::link('<span aria-hidden="true">×</span>',
                        Yii::app()->controller->createUrl('/workflow/entrance/index', array('definationId'=>$this->getWorkflowId(),'nodeId'=>$this->getCurrentNodeId(),'operationId'=>$this->getOperateId(),'branchId'=>$this->schoolId,'action'=>'delete')), 
                        array('type'=>'button','class'=>'close J_ajax_del','aria-label'=>'Close','style'=>'color:red;font-size:28px;')
                );
            endif;
            $str .=  CHtml::tag('ul',array('class'=>'task-info','style'=>'width: 610px;padding-left:2px;'),false,false);
                $str .= CHtml::tag('li',null,false,false);
                    $str .= CommonUtils::autoLang($this->definationObj->defination_name_cn,$this->definationObj->defination_name_en).'&nbsp;';
                    if ($config['href']){
                        $str .= CHtml::link($config['function'], $config['href'],array('target'=>'_blank'));
                    }else{
                        $str .= $config['function'];
                    }
                $str .= CHtml::closeTag('li');
                //school
                $str .= CHtml::tag('li',null,false,false);
                    $str .= Yii::t('workflow', '所在校园');
                    $str .= $branchList[$this->schoolId];
                $str .= CHtml::closeTag('li');
                //发起人
                $str .= CHtml::tag('li',null,false,false);
                    $str .= Yii::t('workflow', '发起人');
                    $str .= $this->opertationObj->userInfo->getName();
                $str .= CHtml::closeTag('li');
                //发起时间
                $str .= CHtml::tag('li',null,false,false);
                    $str .= Yii::t('workflow', '发起时间');
                    $str .= OA::formatDateTime($this->opertationObj->start_time,'medium','short');
                $str .= CHtml::closeTag('li');
            $str .= CHtml::closeTag('ul');
            $str .= CHtml::tag('div',array('class'=>'clearfix'),false,false);
            $str .= CHtml::closeTag('div');
            //nav-wizard
            $str .= CHtml::tag('ul',array('class'=>'nav nav-wizard','style'=>'padding-bottom: 20px;'),false,false);
                foreach ($this->nodeIds as $node):
                    $active = ($node == $this->getOperateValue('current_node_index')) ? array('class'=>$activeLabel) : null;
                    $str .= CHtml::tag('li',$active,false,false);
                        $str .= CHtml::link(CommonUtils::autoLang($this->nodesList[$node]->node_name_cn,$this->nodesList[$node]->node_name_en), Yii::app()->controller->createUrl('/workflow/entrance/index',array('definationId'=>$this->getWorkflowId(),'nodeId'=>$node,'operationId'=>$this->getOperateId(),'branchId'=>$this->schoolId,'action'=>'show')), array('class'=>'J_ajax','data-method'=>'get'));
                    $str .= CHtml::closeTag('li');
                endforeach;
            $str .= CHtml::closeTag('ul');
        $str .= CHtml::closeTag('div');
        return $str;
    }
    
    /**
     * 跳节点函数
     */
    public function jumpNode($desc,$nodeStatus,$transaction,$nodeId=0,$userList=array()){
        $nodeId = ($nodeId) ? $nodeId : $this->getNextNodeId();
         //如果用户停留在某一个节点
        $this->opertationObj->current_node_index = $nodeId;
        if (!$this->opertationObj->save()){
            $transaction->rollBack();
            return false;
        }
        if (!WorkflowRole::model()->updateAll(array('state'=>WorkflowOperation::WORKFLOW_STATS_OPED),'operation_id=:operation_id and user_node_id=:user_node_id and assign_user=:assign_user', 
                           array(':operation_id'=>$this->opertationObj->id,':user_node_id'=>$this->nodeObj->node_id,':assign_user'=>Yii::app()->user->id))){
            $transaction->rollBack();
            return false;
        }
        if (!WorkflowRole::model()->updateAll(array('current_operator'=>WorkflowRole::WORKFLOW_ROLE_CURRENT_CHECKED_USER),'operation_id=:operation_id and user_node_id=:user_node_id', 
                       array(':operation_id'=>$this->opertationObj->id,':user_node_id'=>$this->nodeObj->node_id))){
                $transaction->rollBack();
                return false;
        }
        //更新进程状态
        if ($this->saveWorkflowProess(false,$desc,$nodeStatus,$transaction) === false){
            return false;
        }
        //创建下一节点审核用户
        if ($this->createRole($nodeId, $this->opertationObj->id,$transaction,$userList) === false){
            return false;
        }
        return true;
    }

    /**
     * 检查孩子是否在某一学校就读过
     * @param  [type] $chilidId [孩子ID]
     * @param  [type] $schoolId [学校ID]
     * @return [boolean]           [description]
     */
    public function hadStudyInSchool($chilidId, $schoolId = false)
    {
        if (!$schoolId) 
            $schoolId = $this->schoolId;
        Yii::import('common.models.portfolio.ChildStudyHistory');
        $criter = new CDbCriteria();
        $criter->compare('childid', $chilidId);
        $criter->compare('schoolid', $schoolId);
        $result = ChildStudyHistory::model()->count($criter);
        return $result;
    }

    /**
     * copy 上面的方法
     * 解决问题：提现工作流调用了此方法，应该以账单为准
     * <AUTHOR>
     * @param $chilidId
     * @param bool $schoolId
     * @return CDbDataReader|mixed|string
     * @throws CException
     */
    public function hasInvoiceInSchool($chilidId, $schoolId = false)
    {
        if (!$schoolId)
            $schoolId = $this->schoolId;
        Yii::import('common.models.invoice.Invoice');
        $criter = new CDbCriteria();
        $criter->compare('childid', $chilidId);
        $criter->compare('schoolid', $schoolId);
        $result = Invoice::model()->count($criter);
        return $result;
    }

    public function wechatPush()
    {
        if ($this->definationObj->defination_handle == 'RefundFee') {
            Yii::log("wechatPush start: " . $this->getOperateId() ."-". $this->getCurrentNodeId(), 'info', 'workflow');
        }
        
        if ($this->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_OPDENY || $this->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_RESET) {
            $this->wechatPushRequest('reject');
        } else {
            if ($this->isEndNode() || $this->endNode){
                $this->wechatPushRequest('complete');
            }else{
                $this->wechatPushRequest('pass');
            }
        }

        if ($this->definationObj->defination_handle == 'RefundFee') {
            Yii::log("wechatPush end", 'info', 'workflow');
        }
    }

    public function wechatPushRequest($type)
    {
        $requestUrl = "workflow/push/$type";
        $requestData = array(
            'operationId' =>  $this->getOperateId(),
            'currentNodeId' =>  $this->getCurrentNodeId(),
        );
        if ($this->definationObj->defination_handle == 'RefundFee') {
            Yii::log("wechatPush request: " . $requestUrl .";". $this->getOperateId() ."-". $this->getCurrentNodeId(), 'info', 'workflow');
        }
        CommonUtils::requestDsOnline($requestUrl, $requestData);
    }

    public static function  notify($operationId, $currentNodeId)
    {
        $requestUrl = "workflow/push/pass";
        $requestData = array(
            'operationId' =>  $operationId,
            'currentNodeId' =>  $currentNodeId,
        );
        return CommonUtils::requestDsOnline($requestUrl, $requestData);
    }
}
