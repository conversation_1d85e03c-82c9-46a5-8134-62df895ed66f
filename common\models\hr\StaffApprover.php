<?php

/**
 * This is the model class for table "ivy_staff_approver".
 *
 * The followings are the available columns in table 'ivy_staff_approver':
 * @property integer $staff_uid
 * @property integer $approver
 * @property integer $replacer
 * @property integer $replacer_due
 * @property integer $updated
 * @property integer $updator
 */
class StaffApprover extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return StaffApprover the static model class
	 */
        public $approvers = null;
        public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_staff_approver';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
//			array('approver', 'required','on'=>'addStaff, editStaff, addUser, editUser, checkUser'),
			array('staff_uid, approver, replacer, replacer_due, updated, updator', 'numerical', 'integerOnly'=>true),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('staff_uid, approver, replacer, replacer_due, updated, updator', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'user'=>array(self::HAS_ONE,'User','uid','condition'=>'user.level=1 and user.isstaff=1')
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'staff_uid' => 'Staff Uid',
			'approver' => 'Approver',
			'replacer' => 'Replacer',
			'replacer_due' => 'Replacer Due',
			'updated' => 'Updated',
			'updator' => 'Updator',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('staff_uid',$this->staff_uid);
		$criteria->compare('approver',$this->approver);
		$criteria->compare('replacer',$this->replacer);
		$criteria->compare('replacer_due',$this->replacer_due);
		$criteria->compare('updated',$this->updated);
		$criteria->compare('updator',$this->updator);

		return new CActiveDataProvider(get_class($this), array(
			'criteria'=>$criteria,
		));
	}

    /*
     * 获取某员工的上级审核人
     * @return int uid
     */
    static public function getApproverUser($staffId){
        $model = StaffApprover::model()->findByPk($staffId);
        $time = strtotime('today');
        if ($model->replacer && $model->replacer_due>=$time){
            return $model->replacer;
        }else{
            return $model->approver;
        }
    }

    /*
     * 根据条件学校ID，取得所增加用户上级审核人列表
     * @param string branchid
     * @return array data
     */
    static public function getApproverUserBySystem($branchid){
        $data = array();
        $branchModel = Branch::model()->findByPk($branchid);
        if (!empty($branchModel)){
            //学校查询园长+PD
            if ($branchModel->type != Branch::TYPE_OFFICE){
                $cdModel = User::model()->getUserByPosition($branchid,54);
                if (empty($cdModel)){
                    $cdModel = AdmBranchLink::model()->getUserBySchool($branchid,AdmBranchLink::ADM_TYPE_PD);
                    foreach ($cdModel as $val){
                        $data[$val->uid] = $val->user->getName();
                    }
                }else{
                    foreach ($cdModel as $val){
                        $data[$val->uid] = $val->getName();
                    }
                }
            }else{
                //查询boss
                $positionIds = array();
                $link = DepPosLink::model()->findAll('is_lead=:is_lead',array(':is_lead'=>1));
                foreach ($link as $val){
                    if ($val->position_id != 54){
                        $positionIds[$val->position_id] = $val->position_id;
                    }
                }
                $cdModel = User::model()->getUserByPosition(null,$positionIds);
                foreach ($cdModel as $val) {
                    $data[$val->uid] = $val->getName();
                }
            }
        }
        return $data;
    }
}