<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'draw-form',
	'enableAjaxValidation'=>false,
	'htmlOptions'=>array('class'=>'J_ajaxForm'),
));
?>

<div class="pop_cont pop_table" style="height:auto;">
    <div class="tips_light"><?php echo Yii::t("payment", '余额提现是在收到家长提出的提现申请之后，将孩子个人账户的部分或全部余额以银行转账的形式汇入家长提供的银行账户，网银操作完成之后需要财务人员使用本功能进行记录。')?></div>
    <h2><?php echo Yii::t("payment", '当前余额');?><?php echo $this->childObj->credit?></h2>
    <table width="100%">
        <colgroup>
            <col class="th" />
            <col />
        </colgroup>
        <tbody>
            <tr>
                <th><?php echo $form->labelEx($model,'amount'); ?></th>
                <td><?php echo $form->textField($model,'amount', array('maxlength'=>255,'class'=>'input length_2')); ?></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'memo'); ?></th>
                <td><?php echo $form->textArea($model,'memo', array('class'=>'length_4', 'placeholder'=>Yii::t("payment", '请记录网银操作回单号、家长的银行账户信息等'))); ?></td>
            </tr>
        </tbody>
    </table>
</div>

<div class="pop_bottom">
    <button type="button" class="btn fr" id="J_dialog_close"><?php echo Yii::t('global','Cancel');?></button>
    <button class="btn fr btn_submit mr10 J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
</div>
<?php $this->endWidget(); ?>