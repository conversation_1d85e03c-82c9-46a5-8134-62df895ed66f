<?php
$teachingKey = ( Yii::app()->params['siteFlag'] == 'daystar' ) ?
    'ds' : 'mi';

?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><button type="button" id="showSelectTaskModal" class="btn btn-info">
                <?php echo Yii::t('site','Semester Tasks');?> <span class="glyphicon glyphicon-chevron-right"></span></button></li>
        <?php if($this->selectedClassId):?>
        <li><?php echo $this->branchObj->title;?></li>
        <li><?php echo $this->schoolClasses['items'][$this->selectedClassId];?></li>
        <li><?php echo Yii::t('teaching', 'Semester :s', array(':s'=>$this->selectedSemester));?></li>
        <?php endif;?>
    </ol>

    <?php if($this->selectedClassId):?>
    <ul class="nav nav-pills navbar">
        <?php
        foreach($this->semesterTasks[$teachingKey] as $_k => $_t){
            echo CHtml::openTag('li', array('class'=>$_k==$this->selectedTask?'active':''));
            $_params = array(
                'index', 'classid'=>$this->selectedClassId, 'semester'=>$this->selectedSemester, 'task'=>$_k
            );
            if ($this->showHistory){
                $_params['showHistory'] = $this->showHistory;
            }
            if ($this->historyYid){
                $_params['historyYid'] = $this->historyYid;
            }

            echo CHtml::link($_t, $_params);
            echo CHtml::closeTag('li');
        }
        ?>
    </ul>
    <?php endif;?>
</div>


<!-- Modal -->
<div class="modal" id="selectTaskModal" tabindex="-1" role="dialog" aria-labelledby="selectTaskModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel">
                    <?php echo Yii::t('teaching','Select teaching task');?> <small>
                        <?php echo $this->branchObj->title;?></small></h4>
            </div>
            <?php echo CHtml::form($this->createUrl('index'), 'get', array('class'=>'form-horizontal', 'onsubmit'=>'return checkSubmit(this)'));?>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="well">
                            <ul class="nav nav-pills" id="cless-selector">
                                <?php
                                foreach($this->schoolClasses['items'] as $_classid=>$title){
                                    if($this->myClasses){
                                        if(in_array($_classid, $this->myClasses)){
                                            echo CHtml::openTag('li');
                                            echo CHtml::link($title, '#', array('data-classid'=>$_classid));
                                            echo CHtml::closeTag('li');
                                        }
                                    }
                                    else{
                                        echo CHtml::openTag('li');
                                        echo CHtml::link($title, '#', array('data-classid'=>$_classid));
                                        echo CHtml::closeTag('li');
                                    }
                                }
                                ?>
                            </ul>
                        </div>

                        <div class="well">
                            <ul class="nav nav-pills" id="semester-selector">
                                <?php
                                foreach($this->semesters as $semester):
                                    ?>
                                    <li><a href="#" data-semester="<?php echo $semester;?>">
                                            <?php echo Yii::t('teaching', 'Semester :s', array(':s'=>$semester)) ?></a></li>
                                <?php
                                endforeach;
                                ?>
                            </ul>
                        </div>

                        <div class="well">
                            <ul class="nav nav-pills" id="task-selector">
                                <?php
                                foreach($this->semesterTasks[$teachingKey] as $_k => $_t):
                                ?>
                                    <li><a href="#" data-task="<?php echo $_k;?>"><?php echo $_t; ?></a></li>
                                <?php
                                endforeach;
                                ?>
                            </ul>
                        </div>

                        <div id="form-data">
                            <?php echo CHtml::hiddenField('classid');?>
                            <?php echo CHtml::hiddenField('semester');?>
                            <?php echo CHtml::hiddenField('task');?>
                            <?php if($this->showHistory) {
                                echo CHtml::hiddenField('showHistory', $this->showHistory); }?>
                            <?php if($this->historyYid)  {
                                echo CHtml::hiddenField('historyYid', $this->historyYid); }?>
                        </div>
                    </div>
                </div>
                <div>
                    <!--选取历史年-->
                    <div id="show-history-wrapper" class="form-inline">
                        <?php if($this->showHistory):?>
                            <?php echo CHtml::link(Yii::t('teaching','Back to current year'),
                                array('//mteaching/semester/index'));?>
                        <?php else:?>
                            <span class="btn btn-default" onclick="loadHistorical();return false;">
                            <span class="glyphicon glyphicon-chevron-right"></span>
                                <?php echo Yii::t('teaching', 'Access historical data'); ?></span>
                            <span class="hint" style="display: none;">Loading...</span>
                            <select class="form-control" style="display: none" id="select-history-box"></select>
                        <?php endif;?>
                    </div>
                </div>
            </div>
            <div class="modal-footer pop_bottom">
                <button type="submit" class="btn btn-primary"><?php echo Yii::t('global','OK');?></button>
                <button data-dismiss="modal" type="button" class="btn btn-default"><?php echo Yii::t('global','Cancel');?></button>
            </div>
            <?php echo CHtml::endForm();?>
        </div>
    </div>
</div>

<script>
    var openSelectTaskModal;
    var classid = <?php echo $this->selectedClassId?>;
    var semester = '<?php echo $this->selectedSemester?>';
    var task = '<?php echo $this->selectedTask?>';

    loadingHistorical = false;
    loadHistorical = function(){
        if(loadingHistorical) return;

        loadingHistorical = true;

        $.ajax({
            type: 'post',
            url: '<?php echo Yii::app()->createUrl('//common/getHistoryYid');?>',
            dataType: 'json',
            data: {branchid:currentBranchId}
        }).done(function(data){
            if(data.state == 'success'){
                $('#show-history-wrapper .hint').show();
                $('#show-history-wrapper #select-history-box').empty();
                $('#show-history-wrapper #select-history-box').append('<option>'+
                    '<?php echo Yii::t("global", "Please Select");?>' +'</option>');

                _.each(data.data, function(_cid, _year){
                    var _display = _year + " - " + (parseInt(_year) + 1 );
                    $('#show-history-wrapper #select-history-box').append('<option value="'+_cid+'">'+
                        _display +'</option>');
                });
                $('#show-history-wrapper .hint').hide();
                $('#show-history-wrapper #select-history-box').show();
            }
            loadingHistorical = false;
        });
    }

    $(function(){
        $('#show-history-wrapper #select-history-box').change(function(){
            var baseUrl = '<?php echo $this->createAbsoluteUrl('//mteaching/semester/index',
            array('showHistory'=>1));?>';
            window.location.href = baseUrl + '&historyYid=' + $(this).children('option:selected').val();
        });

        openSelectTaskModal = function(){
            $('#selectTaskModal').modal();//{backdrop:'static', keyboard:false}
        };

        $('#showSelectTaskModal').click(function(){
            openSelectTaskModal();
        });

        if(!classid || !semester || !task)
            openSelectTaskModal();

        if(classid){
            var obj = $('#cless-selector li a[data-classid="'+classid+'"]');
            if( obj.length ){
                obj.parent().addClass('active');
                $('#form-data input#classid').val( classid );
            }
        }
        if(semester){
            var obj = $('#semester-selector li a[data-semester="'+semester+'"]');
            if( obj.length ){
                obj.parent().addClass('active');
                $('#form-data input#semester').val( semester );
            }
        }
        if(task){
            var obj = $('#task-selector li a[data-task="'+task+'"]');
            if( obj.length ){
                obj.parent().addClass('active');
                $('#form-data input#task').val( task );
            }
        }

        $('#cless-selector').on('click', 'a', function(e){
            e.preventDefault();
            $('#cless-selector li').removeClass('active');
            $(this).parent().addClass('active');
            $('#form-data input#classid').val( $(this).data('classid') );
        });

        $('#semester-selector').on('click', 'a', function(e){
            e.preventDefault();
            $('#semester-selector li').removeClass('active');
            $(this).parent().addClass('active');
            $('#form-data input#semester').val( $(this).data('semester') );
        });

        $('#task-selector').on('click', 'a', function(e){
            e.preventDefault();
            $('#task-selector li').removeClass('active');
            $(this).parent().addClass('active');
            $('#form-data input#task').val( $(this).data('task') );
        });

    });

    function checkSubmit(_this) {
        $('#J_fail_info').remove();
        var button = $(_this).find('button[type="submit"]');
        var flag = true, msg=[];

        if( $(_this).find('input#classid').val() == '' ){
            flag = false;
            msg.push('<?php echo Yii::t("teaching","class");?>');
        }
        if( $(_this).find('input#semester').val() == '' ){
            flag = false;
            msg.push('<?php echo Yii::t("teaching","semester");?>');
        }
        if( $(_this).find('input#task').val() == '' ){
            flag = false;
            msg.push('<?php echo Yii::t("teaching","task");?>');
        }
        if(flag){
            return true;
        }
        else{
            $( '<span id="J_fail_info" class="text-warning"><i class="glyphicon glyphicon-remove text-warning"></i> <?php echo Yii::t("global","Please Select");?> ' + msg.join('、') + '！</span>' ).appendTo(button.parent()).fadeIn( 'fast' );
            return false;
        }
    }
</script>

<?php
$this->branchSelectParams['extraUrlArray'] = array('//mteaching/semester/index');
$this->renderPartial('//layouts/common/branchSelectBottom');
?>