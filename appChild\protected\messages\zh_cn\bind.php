<?php
return array(
	'Link the account' => '绑定系统账号',
	'Submit' => '验证',
	'Confirm' => '确定',
	'Cancel' => '取消',

	'Phone' => '手机号',
	'Code' => '验证码',
	'Please use your Ivyonline/Daystar registered phone number to verify' => '请使用艾毅幼儿园或者启明星学校注册登记的手机进行验证',
	'Your phone number' => '请输入手机号',
	'The verification code' => '请输入验证码',
	'Send' => '获取验证码',
	'Send Again' => '重新发送',
	'Verify and link my account' => '验证并绑定账号',
	
	'Please ensure that your phone number has been registered' => '请确保您的手机号已在校园登记',
	'Verification failed' => '验证失败',
	'Save failed' => '保存失败',
	'Wrong Phone' => '手机号错误',
	'Request too frequent, please try again later' => '验证码发送过快，请稍后再试',
	'Account information: Search via phone number mphone' => '帐号信息：通过手机号码 mphone 查找',
	'Try again' => '重新绑定',
	'Wrong information? <br/> Please use other phone numbers rebind_link or contact the school to correct the information.' => '信息不正确？<br>您可以使用其它手机号 rebind_link <br/>或者联系校园报告错误信息。',

	'Account' => '账户',
	'Password' => '密码',
	'Log in via Ivyonline/Daystar account' => '使用艾毅、启明星在线系统账户登陆',
	'Please use your Ivyonline/Daystar account to verify' => '请使用艾毅在线或者启明星在线帐号进行验证',
	'Your e-mail address' => '请输入邮件地址',
	'The password' => '请输入密码',
	'Log in via registered phone number' => '请使用艾毅/启明星注册手机号登陆',
	'Confirm linking the account' => '确认绑定账户',

	'Technical support by Ivy Education Group' => '由艾毅教育提供支持',

	'The account information registered via this phone number is as follows, please verify your information and continue to link to our WeChat Official Account' => '使用本手机号注册的帐号信息如下，请确认信息并继续绑定至本公众号',
	'This phone number has registered for multiple accounts, please verify your information and select the correct account to link to our WeChat Official Account' => '该手机号已经注册了多个账号，请确认信息并选择正确帐号绑定至本公众号',
);
