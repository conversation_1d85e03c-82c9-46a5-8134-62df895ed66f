<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Workspace'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Workflow Workspace'), array('//workflow/index/index')) ?></li>
        <li class="active">我的合同</li>
    </ol>

    <div class="row">
        <?php if ($dataProvider) { ?>
            <div class="col-md-12 col-sm-12">
                <div class="panel panel-default">
                    <div class="panel-body">
                        <?php
                            $this->widget('ext.ivyCGridView.BsCGridView', array(
                                'id' => 'confirmed-visit-grid',
                                'afterAjaxUpdate' => 'js:head.Util.modal',
                                'dataProvider' => $dataProvider,
                                'template' => "{items}{pager}{summary}",
                                'colgroups' => array(
                                    array(
                                        "colwidth" => array(100, 100, 100, 100, 100, 100, 100, 100),
                                    )
                                ),
                                'columns' => array(
                                    array(
                                        'name' => '合同标题',
                                        'value' => array($this, 'showContractTitle'),
                                    ),
                                    array(
                                        'name' => '合同类型',
                                        'value' => '$data->contract->categoryTitle()',
                                    ),
                                    array(
                                        'name' => '付款周期',
                                        'value' => '$data->contract->cycleTitle()',
                                    ),
                                    array(
                                        'name' => '合同金额',
                                        'value' => 'number_format($data->contract->price_total/100, 2)',
                                    ),
                                    array(
                                        'name' =>  '已付金额',
                                        'value' => array($this, 'showContractPaymentPrice'),
                                    ),
                                    array(
                                        'name' =>  '开始时间',
                                        'value' => 'date("Y-m-d", $data->contract->start_date)',
                                    ),
                                    array(
                                        'name' =>  '结束时间',
                                        'value' => 'date("Y-m-d", $data->contract->end_date)',
                                    ),
                                    array(
                                        'name' =>  '操作',
                                        'value' => array($this, 'showContractButton'),
                                    ),
                                ),
                            ));
                            ?>
                    </div>
                </div>
            </div>
        <?php } ?>
    </div>
</div>
<div id="workflow-modal-template"></div>
<div id="workflow-modal-template2"></div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>

<script>
    function cb_del(params) {
        $.fn.yiiGridView.update('confirmed-visit-grid', {
            complete: function() {
                head.Util.modal()
                head.Util.ajax()
                head.Util.ajaxDel()
            }
        });
    }
</script>