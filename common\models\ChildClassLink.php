<?php

/**
 * This is the model class for table "ivy_child_school_class".
 *
 * The followings are the available columns in table 'ivy_child_school_class':
 * @property integer $childid
 * @property integer $hid
 * @property string $schoolid
 * @property integer $classid
 * @property integer $calendar
 * @property integer $semester
 * @property integer $stat
 * @property integer $updated_timestamp
 * @property integer $userid
 */
class ChildClassLink extends CActiveRecord
{


    //School_class中的状态
    const STATS_REG 		= 0;	//已注册，未分班
	const STATS_AWAITING 	= 10; 	//已分班，等待上学
	const STATS_ONGOING  	= 20; 	//在读中
	const STATS_GRADUATED 	= 100; 	//已毕业
	const STATS_DROPPINGOUT	= 888; 	//退学处理中
	const STATS_DROPOUT 	= 999; 	//已退学


	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ChildClassLink the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_child_school_class';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('childid, schoolid, classid, updated_timestamp, userid', 'required'),
			array('childid, hid, classid, calendar, semester, stat, updated_timestamp, userid', 'numerical', 'integerOnly'=>true),
			array('schoolid', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('childid, hid, schoolid, classid, calendar, semester, stat, updated_timestamp, userid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'classInfo' => array(self::BELONGS_TO, 'IvyClass', array("classid"=>"classid")),
			'calenderInfo' => array(self::BELONGS_TO, 'Calendar', 'calendar'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'childid' => 'Childid',
			'hid' => 'Hid',
			'schoolid' => 'Schoolid',
			'classid' => 'Classid',
			'calendar' => 'Calendar',
			'semester' => 'Semester',
			'stat' => 'Stat',
			'updated_timestamp' => 'Updated Timestamp',
			'userid' => 'Userid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('childid',$this->childid);
		$criteria->compare('hid',$this->hid);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('calendar',$this->calendar);
		$criteria->compare('semester',$this->semester);
		$criteria->compare('stat',$this->stat);
		$criteria->compare('updated_timestamp',$this->updated_timestamp);
		$criteria->compare('userid',$this->userid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}