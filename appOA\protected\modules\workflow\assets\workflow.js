$(function(){
    
    authorityCallback = function(data){
        resultTip({msg:data,error:1});
    }
    
    //审核页面窗口
    showCheckList = function(data){
        if ($('#workflow-modal-template').html()){
            $(".modal-backdrop").remove();
        }
        $('#workflow-modal-template').html(data.template);
        if (!_.isUndefined(data.callback)){
            eval(data.callback+'()');
        }
    }
    
    showCheckList2 = function(data){
        if ($('#workflow-modal-template2').html()){
            $(".modal-backdrop").remove();
        }
        $('#workflow-modal-template2').html(data.template);
        if (!_.isUndefined(data.callback)){
            eval(data.callback+'()');
        }
    }
    
    //--------------------------------------------折扣绑定工作流------------------------------------------
    //查询孩子
    findChildren = function(_this){
        var classid = $(_this).val();
        if (classid){
            $.post(postUrl,{classid:classid},function(data, status){
                var option = '';
                if (status === 'success'){
                    _.each(data,function(val,key){
                        var optionClass = '';
                        var optionTet = '';
                        if (val.active){
                            optionClass = 'class="text-warning" disabled="disabled"';
                            optionTet = ' - 已绑定';
                        }
                        option += '<option value='+val.childid+' flag='+val.active+' '+optionClass+'>'+val.name+' '+optionTet+'</option>'
                    })
                }
                $('#l_childid').html(option);
            }, 'json');
        }else{
            $('#l_childid').empty();
        }
    }
    
    moveRight = function(){
        var toAdds = $("#l_childid option:selected").filter(function(index) {
            return $(this).attr("flag") == 0;
        });
        toAdds.each(function() {
            $("#r_childid").append("<option value='" + $(this).val() + "'>" + $(this).text() + "</option>");
            $("#already_choose_child_hidden").append('<input type="hidden" name="ChildDiscountLink[childid][]" value="' + $(this).val() + '" id="ChildDiscountLink_childid_'+$(this).val()+'"/>');
            $(this).remove();
        });
    }

    moveLeft = function() {
        var toAdds = $("#r_childid option:selected");
        toAdds.each(function() {
            $("#l_childid").append("<option value='" + $(this).val() + "' flag='0'>" + $(this).text() + "</option>");
            $("#ChildDiscountLink_childid_"+$(this).val()).remove();
            $(this).remove();
        });
    }
    
    //--------------------------------------------------------退费工作流-------------------------------------------------------------
    getRefundType = function(_this){
        var type = $(_this).val();
        if (type == 20 || type == 50){
            $('#refund-date').html('<label>选择日期</label><input class="input form-control ui-autocomplete-input" placeholder="选择日期" id="RefundFee_leave_date" type="text" name="RefundFee[leave_date]">');
            $('#RefundFee_leave_date').datepicker({'dateFormat':'yy-mm-dd'});
        }else{
            $('#refund-date').html('<label>退费日期</label><div class="row"><div class="col-md-6"><input class="input form-control" placeholder="退费开始日期" id="RefundFee_refund_startdate" type="text" name="RefundFee[refund_startdate]"></div><div class="col-md-6"><input class="input form-control" placeholder="退费结束日期" id="RefundFee_refund_enddate" type="text" name="RefundFee[refund_enddate]"></div></div>');
            $('#RefundFee_refund_startdate').datepicker({'dateFormat':'yy-mm-dd'});
            $('#RefundFee_refund_enddate').datepicker({'dateFormat':'yy-mm-dd'});
        }
    }
    
    /**
     *删除正在进行中工作流回调调函 
     */
    deleteWorkflowOperator = function(data){
       $('#operation_id_'+data).remove();
        head.Util.ajaxDel();
        head.Util.ajaxForm();
        head.Util.ajax($('#operation_id_'+data.operatorId));
    }
    
    /**
     * 保存成功后AJAX节点进度调度
     */
    saveWorkflowOperator = function(data){
        $('#operation_id_'+data.operatorId).remove();
        $('#wait-check-list').prepend(data.html);
        $('#'+data.modalNameId).modal('hide');
        head.Util.ajaxDel();
        head.Util.ajaxForm();
        head.Util.ajax($('#operation_id_'+data.operatorId));
    }
    
})
