<?php if ($language === 'en'){?>
    <p>Student Name: <?php echo $name;?></p>
    <?php
    $i = 1;
    foreach ($invoiceList['info'] as $val){?>
        <p> <?php if (count($invoiceList['info'])>1){echo $i.". ";}?>Invoice Title: <?php echo $val['title'];?><br>
            <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>Campus: <?php echo $allbranches[$val['schoolid']]['title'];?><br>
            <?php if (isset($invoiceList['unpay']) && count($invoiceList['unpay']) == count($invoiceList['info']) && $schoolid !== "BJ_QF"&& $schoolid !== "BJ_QFF"):?>
                <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>Date Due: <?php echo OA::formatDateTime($val['duetime']);?><br>
            <?php endif; ?>
            <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>Invoice Amount: <?php echo number_format($val['payAmounts'],2);?> RMB<br>
            <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>Amount Paid: <?php echo number_format($val['tAmount'],2);?> RMB<br>
            <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>Amount Unpaid: <?php echo number_format($val['amount'],2);?> RMB<br>
<!--            --><?php //if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?><!--Date Due: --><?php //echo OA::formatDateTime($val['duetime']);?><!--<br>-->
        </p>
        <?php
        $i++;
    }?>

<?php }else{?>
    <p>姓名: <?php echo $name;?> </p>
    <?php
    $i = 1;
    foreach ($invoiceList['info'] as $val){?>
        <p>
            <?php if (count($invoiceList['info'])>1){echo $i.". ";}?>账单标题：<?php echo $val['title'];?><br>
            <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>校园：<?php echo $allbranches[$val['schoolid']]['title'];?><br>
            <?php if (isset($invoiceList['unpay']) && count($invoiceList['unpay']) == count($invoiceList['info']) && $schoolid !== "BJ_QF" && $schoolid !== "BJ_QFF"):?>
                <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>付款截止日期：<?php echo OA::formatDateTime($val['duetime']);?><br>
            <?php endif;?>
            <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>账单金额：<?php echo number_format($val['payAmounts'],2);?> RMB<br>
            <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>已付金额：<?php echo number_format($val['tAmount'],2);?> RMB<br>
            <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>未付金额：<?php echo number_format($val['amount'],2);?> RMB<br>
<!--            --><?php //if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?><!--账单截止日：--><?php //echo OA::formatDateTime($val['duetime']);?><!--<br>-->

        </p>
        <?php
        $i++;
    }?>
<?php }?>
