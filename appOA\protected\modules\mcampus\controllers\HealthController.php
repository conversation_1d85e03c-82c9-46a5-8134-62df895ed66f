<?php

class HealthController extends TeachBasedController
{

    public $selectedClassId = 0;
    public $selectedSemester = '';
    public $selectedTask = '';
    public $selectedChildId = 0;
    public $semesters = array(1, 2);
    public $ivyAccessToken = '';
    public $dsAccessToken = '';
    public $accessToken = '';

    public $printFW = array();

    // 访问action的初级权限
    public $actionAccessAuths = array(
        // 'index'                             => 'o_T_Access',
    );

    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'teaching';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Teaching Tasks');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mcampus/health/index');

        // 重新定义一下可选择菜单
        $this->semesterTasks = array(
            'mi' => array(
                "health" => Yii::t("teaching", "身体生长发育"),
            ),
            'ds' => array(
                "dsreport01" => Yii::t("teaching", "Semester Report"),
                "ptc" => Yii::t("teaching", "Parent-Teacher Conference"),
            )
        );


        Yii::import('common.models.portfolio.*');
        Yii::import('common.models.learning.*');
    }

    public function beforeAction($action)
    {
        if (!parent::beforeAction($action)) {
            return false;
        }

        $this->selectedClassId = Yii::app()->request->getParam('classid', 0);
        $this->selectedSemester = Yii::app()->request->getParam('semester', 0);
        $this->selectedTask = Yii::app()->request->getParam('task', 0);
        $this->selectedChildId = Yii::app()->request->getParam('childid', 0);

        return true;
    }

    public function actionIndex($classid = 0, $semester = '', $task = '')
    {
        parent::initExt();

        //该校园或班级不支持此功能
        $stopped = array(
            Branch::PROGRAM_DAYSTAR => array(
                'schecklist' //, 'ptc'
            )
        );

        //校园支持本功能
        $stopping = false;

        if (isset($stopped[$this->branchObj->type])) {
            if (in_array($task, $stopped[$this->branchObj->type])) {
                //校园不支持本功能
                $stopping = true;
            }
        }
        $taskData = array();
        $taskData['stopping'] = $stopping;

        if (!$stopping) {
            if ($classid && $task) {
                $taskfun = 'task' . ucfirst($task);
                $taskData = $this->$taskfun();
            }
        }
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/plupload/plupload.full.min.js');


        $schoolGroup = $this->branchObj->group;

        $this->render('index', array(
            'taskData' => $taskData,
            'schoolGroup' => $schoolGroup,
            'classid' => $classid,
            'semester' => $semester,
            'type' => $this->branchObj->type,
        ));
    }

    public function taskHealth()
    {
        Yii::import('common.models.reportCards.*');

        $crit = new CDbCriteria();
        $crit->compare('class_id', $this->selectedClassId);
        $crit->compare('semester', $this->selectedSemester);
        $crit->compare('status', 1);
        $crit->index = 'child_id';

        $taskData['onlined'] = array_keys(ReportsData::model()->findAll($crit));
        $taskData['children'] = $this->getNameList($this->selectedClassId);


        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/backbone-min.js');

        return $taskData;
    }

    public function actionGetHealthInfo()
    {
        $childid = $this->selectedChildId;
        $classid = $this->selectedClassId;
        $semester = $this->selectedSemester;
        $calendarData = $this->getYidStartYearByClassIdAndBranchId($classid, $this->branchId);

        if (!$calendarData) {
            return false;
        }
        $yid = $calendarData['yid'];

        $formModel = new HealthForm();
        $oldFormModel = new HealthForm();

        $crit = new CDbCriteria();
        $crit->compare('yid', $yid);
        $crit->compare('semester', $semester);
        $crit->compare('childid', $childid);
        $crit->compare('classid', $classid);
        $crit->compare('status', 1);
        $model = LearningHealth::model()->find($crit);
        if ($model) {
            $content = json_decode($model->content1, true);
            $formModel->attributes = $content;
        }

        echo $this->renderPartial('_healthinfo', array(
            'formModel' => $formModel,
            'oldFormModel' => $oldFormModel
        ));
    }

    public function actionSaveHealthInfo()
    {
        if ($_POST['HealthForm']) {
            $formModel = new HealthForm();
            $formModel->attributes = $_POST['HealthForm'];
            $childid = $this->selectedChildId;
            $classid = $this->selectedClassId;
            $semester = $this->selectedSemester;
            $calendarData = $this->getYidStartYearByClassIdAndBranchId($classid, $this->branchId);
            $yid = $calendarData['yid'];
            
            $crit = new CDbCriteria();
            $crit->compare('yid', $yid);
            $crit->compare('semester', $semester);
            $crit->compare('childid', $childid);
            $crit->compare('classid', $classid);
            $crit->compare('status', 1);
            $model = LearningHealth::model()->find($crit);
            if (!$model) {
                $model = new LearningHealth();
                $model->status = 1;
                $model->created_at = time();
                $model->created_by = $this->staff->uid;
            }
            $model->content1 = json_encode($formModel->attributes);

            $model->yid = $yid;
            $model->semester = $semester;
            $model->childid = $childid;
            $model->classid = $classid;
            $model->updated_at = time();
            $model->updated_by = $this->staff->uid;

            if ($model->save()) {
                $this->addMessage('state', 'success');
                $this->addMessage('message', 'Success');
                $this->showMessage();
            }
            $error = current($model->getErrors());
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $error[0]);
            $this->showMessage();
        }
    }

    public function actionSaveHealthMedia()
    {
        if ($_POST) {
            $img1= $_POST['img1'];
            $img2= $_POST['img2'];
            $img3= $_POST['img3'];
            
            $childid = $this->selectedChildId;
            $classid = $this->selectedClassId;
            $semester = $this->selectedSemester;
            $calendarData = $this->getYidStartYearByClassIdAndBranchId($classid, $this->branchId);
            $yid = $calendarData['yid'];
            
            $crit = new CDbCriteria();
            $crit->compare('yid', $yid);
            $crit->compare('semester', $semester);
            $crit->compare('childid', $childid);
            $crit->compare('classid', $classid);
            $crit->compare('status', 1);
            $model = LearningHealth::model()->find($crit);
            if (!$model) {
                $model = new LearningHealth();
                $model->status = 1;
                $model->created_at = time();
                $model->created_by = $this->staff->uid;
            }

            $model->yid = $yid;
            $model->semester = $semester;
            $model->childid = $childid;
            $model->img1 = $img1;
            $model->img2 = $img2;
            $model->img3 = $img3;
            $model->classid = $classid;
            $model->updated_at = time();
            $model->updated_by = $this->staff->uid;

            if ($model->save()) {
                $this->addMessage('state', 'success');
                $this->addMessage('message', 'Success');
                $this->showMessage();
            }
            $error = current($model->getErrors());
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $error[0]);
            $this->showMessage();
        }
    }

    public function getYidStartYearByClassIdAndBranchId($classid, $branchId)
    {
        $result = Yii::app()->db->createCommand()
            ->select('c.yid as yid, y.startyear as startyear')
            ->from('ivy_class_list c')
            ->join('ivy_calendar_yearly y', 'y.yid=c.yid')
            ->where(
                'c.classid=:classid and c.schoolid=:schoolid',
                array(
                    ':classid' => $classid,
                    ':schoolid' => $branchId
                )
            )
            ->queryRow();
        return $result;
    }
}
