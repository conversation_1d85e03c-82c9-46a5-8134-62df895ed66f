<?php
$this->breadcrumbs = array(
    Yii::t("navigations", "Scholarship Application")
);
?>
<h1><label><?php echo Yii::t("navigations", "Scholarship Application"); ?></label></h1>
<style>
.add{
    display:inline-block;width:22px;height:22px;border:1px solid #fff;text-align:center;line-height:100%;font-size:20px;cursor:pointer;background:green;color:#fff
}
#popLayer {
    display: none;
    background-color: #000;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index:9000;
    -moz-opacity: 0.8;
    opacity:.80;
    filter: alpha(opacity=80);/* 只支持IE6、7、8、9 */
}

/*弹出层*/
#popBox {
    display: none;
    background-color: #FFFFFF;
    z-index:9999;
    width:50%;
    max-height:80%;
    position:fixed;
    top:10%;
    right:0;
    left:0;
    /* bottom:0; */
    margin:auto;
    overflow-y:auto;
    /* border-radius:10px */
}

#popBox .close{
    text-align: right;
    margin-right:15px;
    padding-top:1%;
    height:4%;
}

/*关闭按钮*/
#popBox .close a {
    text-decoration: none;
    color: #2D2C3B;
}
.content{
    max-height:95%
}
.content h1{
    padding:0 0 10px 0;
    font-weight:600;
}
.text{
    padding:20px;
    overflow-y:auto;
    height:82%;
    font-weight:600
}
.text p{
    display:flex;
}
.text p span{
   flex:1;
   margin-left:5px
}
.filled{
    border:1px solid #ccc;
    padding:10px 20px;
    background:#DFF0D8;
    color:#3C763D !important;
    border-radius:5px
}
.students span{
    display:inline-block;
    width:28%
}
.students label{
    display:inline-block;
    width:20%
}
p i{
    font-size:12px;
    color:#000;
}
.tooltip{
    position: relative;
    display: inline-block;
    margin: 5px 5px;
    opacity: 1;
}
.tooltip .tooltip-arrow {
    top: 50%;
    left:-5px;
    margin-top: -5px;
    border-width: 5px 5px 5px 0;
    border-right-color: #000;
}
.tooltip-arrow {
    position: absolute;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
}
.tooltip-inner {
    /* max-width: 200px; */
    padding:2px 8px 4px 8px;
    color: #fff;
    text-align: center;
    background-color: #000;
    border-radius: 4px;
}
.tooltip-inner span{
    font-size:16px
}
div.form label{
    font-weight:normal
}
.floatLeft{
    float:left;
    margin-right:10px;
    width:30px;
    height:30px
}
div.form .desc{
    padding: 10px 0 0;
}
.Required{
    color:red;
    padding-left:5px;
}
</style>
<?php
foreach (Yii::app()->user->getFlashes() as $key => $message) {
    echo '<div class="flash-' . $key . '">' . $message . "</div>\n";
}
$colorbox = $this->widget('application.extensions.colorbox.JColorBox');
$colorbox->addInstance('.colorbox', array('iframe' => false, 'width' => '750'));
?>
<?php if ($model->isNewRecord): ?>
    <div class="form misc_bg">
    <?php
    $form = $this->beginWidget('CActiveForm', array('htmlOptions'=>array('enctype' => 'multipart/form-data')));
    ?>
    <?php echo CHtml::errorSummary($model); ?>
    <dl class="row">
        <dt><?php echo CHtml::activeLabelEx($model,Yii::t("navigations", 'Student Information') ) ?><span class='Required'>*</span></dt>
        <dd>
            <p class="desc"><?php echo Yii::t("navigations", 'Please list all of your children currently studying at Daystar Academy:') ?></p>
            <p></p>
            <?php foreach ($myChildObjs as $child):?>
                <p class='students'>
                   <input type="checkbox" name="childid[]" class='child' id='childList<?php echo $child->childid?>' value="<?php echo $child->childid?>">
                   <label for='childList<?php echo $child->childid?>'><i><?php echo $child->getChildName();?></i></label>
                    <span><?php echo Yii::t("navigations", "Birthdate: ") ?><i><?php echo date('Y-m-d', $child->birthday);?></i></span>
                    <?php echo Yii::t("navigations", "Grade: ") ?><i><?php echo $child->ivyclass->title;?></i>
                </p>
            <?php endforeach;?>
        </dd>
    </dl>
    <dl class="row">
        <dt><?php echo CHtml::activeLabelEx($model,  Yii::t("navigations", 'Parent Information')) ?></dt>
        <dd>
             <p class="desc"><?php echo Yii::t("navigations", "Father's Name: ") ?><i><?php echo $parentData['f']['name'];?></i></p>
             <p></p>
             <p><span style='display:inline-block;width:50%'><?php echo Yii::t("navigations", "Phone: ") ?><i><?php echo $parentData['f']['phone'];?></i></span> <?php echo Yii::t("navigations", "E-mail: ") ?><i><?php echo $parentData['f']['email'];?></i></p>
             <p><?php echo Yii::t("navigations", "Mother's Name: ") ?><i><?php echo $parentData['m']['name'];?></i></p>
             <p><span style='display:inline-block;width:50%'><?php echo Yii::t("navigations", "Phone: ") ?><i><?php echo $parentData['m']['phone'];?></i></span> <?php echo Yii::t("navigations", "E-mail: ") ?> <i><?php echo $parentData['m']['email'];?></i></p>
        </dd>
    </dl>
    <dl class="row">
        <dt><?php echo CHtml::activeLabelEx($model, Yii::t("navigations", 'Reason')) ?><span class='Required'>*</span></dt>
        <dd>
            <p class="desc"><?php echo Yii::t("navigations", 'Please state your reason for requesting a scholarship. Please state your family\'s income, expenditure and savings status. Mention any financial circumstances which may place a burden on your family.') ?></p>
            <?php echo CHtml::activeTextArea($model, 'reason', array("rows" =>6, "cols" =>75)); ?>
        </dd>
    </dl>
    <dl class="row">
        <dt><?php echo CHtml::activeLabelEx($model, Yii::t("navigations", 'Attach') ) ?><span class='Required'>*</span></dt>
        <dd>
            <p class="desc"><?php echo Yii::t("navigations", 'previous year\'s income statement for ALL wage-earning parents in order for your application to be considered.') ?></p>
            <p></p>
            <div id='fileList'>
                <div><input type="file" name="Scholarship[file][]" id='files'><span class='add' onclick='addFile()'>+</span>
                <div class="tooltip" role="tooltip">
                    <div class="tooltip-arrow"></div>
                    <div class="tooltip-inner">
                    <?php echo Yii::t("navigations", 'Adding more supporting documents')?>
                    </div>
                    </div>
                </div>
            </div>
           <p></p>
        </dd>
    </dl>
    <dl class="row">
        <dt><label for="<?php echo Yii::t("navigations", 'Declaration'); ?>"><?php echo Yii::t("navigations", 'Declaration'); ?></label><span class='Required'>*</span></dt>
        <dd>
            <p class="desc"><?php echo Yii::t("navigations", 'I certify that all information on this form, as well as supporting documentation, is true, correct, and complete to the best of my/our knowledge and that all household income has been reported. We agree to abide by <a href="javascript:void(0)" onclick="popBox()">the Scholarship Policies and Procedures</a>. Further, we acknowledge that failure to abide by these policies and procedures and/or deliberate misrepresentation of this information may result in the scholarship being denied or revoked, and that the parent/guardian will be held responsible for any monies owed to the school.')?></p>
        </dd>
    </dl>
    <dl class="row">
        <dd>
            <p> 
                <?php echo CHtml::activeCheckBox($model, 'agree'); ?>        
                <label for='Scholarship_agree'><?php echo Yii::t("navigations", 'I hereby acknowledge that I have read and agree to the above declaration')?></label>
            </p>
        </dd>
    </dl>
    <dl class="row submit">
        <dt></dt>
        <dd>
            <?php echo CHtml::submitButton(Yii::t("global", "Submit"), array("class" => "w100 bigger","id"=>"submit")); ?>
        </dd>
    </dl>
    <?php $this->endWidget(); ?>  
</div><!-- form -->
<div id="popLayer" onclick="closeBox()"></div>
<div id="popBox">
    <div class="close">
        <a href="javascript:void(0)" onclick="closeBox()">X</a>
    </div>
    <div class="content">
            <h1 style='text-align:center'><?php echo Yii::t("navigations", 'Daystar Scholarship Program - Policy and Procedures')?></h1>
            <div class='text'>
            <p>
                <label>1) </label> 
                <span><?php echo Yii::t("navigations", 'The Board is responsible to provide the school with the eligible tuition amount for each student. Students must be registered, enrolled, and/or attending the school at the time application is submitted to the Board.')?></span>
            </p>
            <p>
                <label>2)</label> 
                <span><?php echo Yii::t("navigations", 'The scholarship will be paid directly to the school on behalf of the student. The family shall not receive any direct cash benefit from the scholarship.')?></span>
                </p> 
            <p>
                <label>3)</label><span><?php echo Yii::t("navigations", 'Scholarships will be issued to the school before the school year begins.')?></span>
            </p>
            <p>
                <label>4)</label> 
                <span><?php echo Yii::t("navigations", 'The school and parent/guardian must notify the school immediately if a student withdraws or is removed from the school')?></span>
            </p>
            <p>
                <label>5)</label> 
                <span><?php echo Yii::t("navigations", 'Scholarship funds for students who withdraw or are removed from the school, or become ineligible for the scholarship prior to the end of the school year must be refunded to the school within 30 days following the date of withdrawal, removal or determination of ineligibility of the student(s)</label> <span>. The scholarship reimbursement shall be pro-rated based on the amount of time remaining for the school term. ')?></span>
            </p>
            <p>
                <label>6)</label> 
                <span><?php echo Yii::t("navigations", 'Acceptance of this scholarship does not exempt the family or student from abiding by the policies of the school and the school has the right to remove a child from the program at any time for failure to abide by their policies. ')?></span>
            </p>
            <p>
                <label>7)</label> 
                <span><?php echo Yii::t("navigations", 'Scholarship funds may not be used to hold a space open for a student who is not actively attending classes or enrolled. ')?></span>
            </p>
            <p>
                <label>8)</label> 
                <span><?php echo Yii::t("navigations", 'Selection for scholarship shall be determined without regard to race, gender, religion, or similar characteristics of the applicants. Scholarship amounts are based on financial need, as well as upon the availability of funds and the number of eligible applicants. ')?></span>
            </p>
            <p>
                <label>9)</label> 
                <span><?php echo Yii::t("navigations", 'All application information must be fully completed and submitted on time by the applicant in order for the application to be considered, including the Application Form and all relevant documents submitted according to the application deadline policy.')?></span>
            </p>
            <p>
                <label>10)</label> 
                <span><?php echo Yii::t("navigations", 'The school is not responsible for lost, missing, misdirected, or late applications or supplemental information. ')?></span>
            </p>
            <p>
                <label>11)</label> 
                <span><?php echo Yii::t("navigations", 'All application information will be maintained by the Board and school in the strictest confidentiality, including income information.')?></span>
            </p>
            
            <!-- <p>By signing this form, we agree to abide by the Scholarship Policies and Procedures. Further, we acknowledge that failure to abide by these policies and procedures may result in the student’s removal from the scholarship program and that the parent/guardian will be held responsible for any monies owed to the school. THIS FORM MUST BE RETURNED WITH THE APPLICATION  </p> -->
            <!-- <p>
            Please Print Child(rens) Name(s):
            </p>
            <p>
            Signature of Parent or Guardian 	         Date			   Printed Name
            </p>
            <p>Please Submit the Application by 15 April 2020 for Decision</p> -->
        </div>
    </div>
</div>
<?php else: ?>
    <h2 class='filled'><img src="/themes/highlight/images/msgbox_success.png" alt="" class='floatLeft'><?php echo Yii::t("navigations", 'Thank you for your apply!')?> </h2>
<?php endif; ?>
<script>   
    function popBox() {
        var popBox = document.getElementById("popBox");
        var popLayer = document.getElementById("popLayer");
        popBox.style.display = "block";
        popLayer.style.display = "block";
    };

    function closeBox() {
        var popBox = document.getElementById("popBox");
        var popLayer = document.getElementById("popLayer");
        popBox.style.display = "none";
        popLayer.style.display = "none";
    } 

    function addFile(){
        var div = document.getElementById("fileList");
        var input = document.createElement("input");
        input.type="file";
        input.setAttribute('class','file'),
        input.name="Scholarship[file][]";
        var del = document.createElement("span");
        del.setAttribute('class','add'),
        del.innerHTML='-'
        del.onclick = function d(){
            this.parentNode.parentNode.removeChild(this.parentNode);
        }
        var innerdiv = document.createElement("div");
        innerdiv.appendChild(input);
        innerdiv.appendChild(del);
        div.appendChild(innerdiv);
    }

    var submitBtn = document.getElementById("submit");
    if(submitBtn!=null){
        submitBtn.onclick = function (event) {
            var dats=document.querySelector("input[type=file]").files.length
            if(dats==0){
                alert("请上传文件");
                return false
            }
            var reason=document.getElementById("Scholarship_reason")
            if(reason.value == ""){
                alert("请输入申请原因");
                return false
            }
         
            var check = document.getElementById("Scholarship_agree").checked;
                if(!check){
                    alert("请勾选并同意奖学金政策!");
                    return false
            　　}
           
            var a=document.getElementsByName('childid[]');
            var b=0;  
                for(var i=0;i<a.length;i++){
                    if(a[i].checked==true){
                        return true;
                    }else{
                        if(a[i].checked==false){
                            b++;
                        } 
                        if(b==a.length){
                            alert('请选择申请的孩子');
                            return false
                        }
                    }
                }
          
        }
    }
</script>