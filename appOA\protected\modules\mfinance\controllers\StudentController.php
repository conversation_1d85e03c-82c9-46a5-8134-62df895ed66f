<?php
class StudentController extends BranchBasedController
{
    public $actionAccessAuths = array(
        'Index'                     => 'o_A_Adm_Finance',
        'GetStudentInvoice'         => 'o_A_Adm_Finance',
        'Export'                    => 'o_A_Adm_Finance',
    );

    public function createUrl($route, $params = array(), $ampersand = '&', $parentOnly = false)
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand, $parentOnly);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'main';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Finance Mgt');
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/xlsx.full.min.js');
        //此类仅适用于启明星
        if (Yii::app()->params['siteFlag'] !== 'daystar'){
            $this->render('//denied/index');
        }

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mfinance/student/index');
        Yii::import('common.models.invoice.*');
    }

    /*
     * 学生学年费用报表
     */
    public function actionIndex(){
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/jquery.number.min.js');
        $this->render('index');
    }

    /*
     * 获取班级学生学年费用报表
     */
    public function actionGetStudentInvoice(){
        $classid = Yii::app()->request->getPost('classid',0);
        $calendarid = Yii::app()->request->getPost('calendar',0);
        $semester = Yii::app()->request->getPost('semester',0);
        $result = array();
        if (Yii::app()->request->isAjaxRequest && $calendarid){
            Yii::import('common.models.calendar.*');
            $result = $this->getStudentInvoices($classid,$calendarid,$semester);
        }
        echo CJSON::encode(array('data'=>$result,'state'=>'success'));
        Yii::app()->end();
    }
    //获取所有班级学生的费用报表
    public function actionGetAllStudentInvoice(){
        $calendarId = Yii::app()->request->getParam('calendar',0);
        $semester = Yii::app()->request->getParam('semester',0);
        $result = array();
        if (Yii::app()->request->isAjaxRequest && $calendarId){
            Yii::import('common.models.calendar.*');
            $classList = IvyClass::model()->getClassList($this->branchId, $calendarId);
            foreach ($classList as $val){
                $result[] = $this->getStudentInvoices($val->classid,$calendarId,$semester);
            }
            $result[] = $this->getStudentInvoices(0,$calendarId,$semester);
        }
        echo CJSON::encode(array('data'=>$result,'state'=>'success'));
        Yii::app()->end();
    }

    /*
     * 获取班级学生学年费用公用数据
     */
    public function getStudentInvoices($classid,$calendarid,$semester = 0){
        $classObj = IvyClass::model()->findByAttributes(array('classid'=>$classid));
        $calendarObj = Calendar::model()->findByPk($calendarid);
        $startYear = $calendarObj->startyear;
        $timePoints = explode(',', $calendarObj->timepoints);
        $criteria = new CDbCriteria();
        $criteria->compare('yid', $calendarid);
        $criteria->compare('branchid', $this->branchId);
        $criteria->compare('is_selected', 1);
        $count = CalendarSchool::model()->count($criteria);
        $cModel = ChildProfileBasic::model();
        $cModel->getMetaData()->addRelation(
            '_studentsInvoices',
            array(
                CActiveRecord::HAS_MANY,
                'Invoice',
                'childid',
                'on' => '_studentsInvoices.calendar_id=:calendar_id and _studentsInvoices.status<:status and _studentsInvoices.payment_type in ("tuition","lunch","bus","deposit","uniform")',
                'params' => array(':calendar_id'=>$calendarid, ':status'=>Invoice::STATS_CHANGE_ARCHIVED),
                'with'=>'invoiceTransaction'
            )
        );

        $nextStatus = $cModel->getStatusList();
        $crit = new CDbCriteria;
        $crit->compare('t.`status`', '<>100');
        // 是否为当前学年
        if ($count) {
            $crit->with = '_studentsInvoices';
            $crit->compare('t.schoolid', $this->branchId);
            // 是否为无班级学生
            if ($classid == 0) {
                $classArray = array();
                $classList = IvyClass::model()->getClassList($this->branchId, $calendarid);
                foreach ($classList as $v) {
                    $classArray[] = $v->classid;
                }
                $crit->addNotInCondition('t.classid', $classArray);
            } else {
                $crit->compare('t.classid', $classid);
            }
        } else {
            $crit->with = array('_studentsInvoices', 'nextYear');
            // 是否为无班级学生
            if ($classid == 0) {
                // 查找已分班学生
                $reserve = ChildReserve::model()->findAllByAttributes(array(
                    'schoolid'=>$this->branchId,
                    'calendar'=>$calendarid,
                    'stat'=>20
                ), array('index'=>'childid'));
                $crit->addNotInCondition('t.childid', array_keys($reserve));
                $crit->compare('t.schoolid', $this->branchId);
            } else {
                $crit->compare('nextYear.schoolid', $this->branchId);
                $crit->compare('nextYear.classid', $classid);
            }
        }
        /*$crit->compare('_studentsInvoices.calendar_id', $calendarid);
        $crit->compare('_studentsInvoices.payment_type', array("tuition","lunch","bus","registration","entrance","deposit","uniform"));
        $crit->compare('_studentsInvoices.status', '<' . Invoice::STATS_CHANGE_ARCHIVED);*/

        $withDrawalStudents = $this->getWithdrawalStudentListByYear($startYear);

        $models = $cModel->findAll($crit);
        $result['calendars'][$calendarid] = $timePoints;
        foreach($models as $_model){
            /*if($_model->status == ChildProfileBasic::STATS_DROPOUT && !$_model->_studentsInvoices){
                continue;
            }*/
            //无班级的学生
            if (!$classid) {
                if (!($_model->status == 0 || ((in_array($_model->status, array(ChildProfileBasic::STATS_DROPPINGOUT,ChildProfileBasic::STATS_DROPOUT))) && $_model->_studentsInvoices))) {
                    continue;
                }

                if (!$count && $_model->nextYear->classid) {
                    continue;
                }
            }

            $eName1 = trim(sprintf("%s %s", trim($_model->nick), trim($_model->last_name_en)));
            $eName2 = trim(sprintf("%s %s", trim($_model->first_name_en), trim($_model->last_name_en)));
            $eName = ($_model->is_legel_cn_name = 1) ? $eName1 : $eName2 ;
//            if (trim($_model->last_name_en) != "")
//            $eName = trim(sprintf("%s %s", trim($_model->first_name_en), trim($_model->last_name_en)));
//            else
//                $eName = trim($_model->first_name_en);
            $cName = trim($_model->name_cn);
            $isnew = $calendarObj->startyear == $_model->first_startyear ? '新生' : '';
            $withDrawalTxt = '';
            if (isset($withDrawalStudents[$_model->childid])) {
                $withDrawalTxt = $withDrawalStudents[$_model->childid]['type'] == 10 ? '退学' : '不返校';
                if ($withDrawalStudents[$_model->childid]['withdrawal_date']) {
                    $withDrawalTxt = $withDrawalTxt . date('Ymd', $withDrawalStudents[$_model->childid]['withdrawal_date']);
                }
            }
            $result['students'][$_model->childid] = array(
                'name' => $_model->getChildName(),
                'cName' => $cName,
                'eName' => $eName,
                'status' => (!$count && $classid) ? $nextStatus[$_model->nextYear->stat] : $_model->getStatus(),
                'id' => $_model->childid,
                'className' => $classObj->title,
                'credit' => $_model->credit,
                'isnew' => $isnew,
                'withDrawalTxt' => $withDrawalTxt,
                'discount' => '',
                'fee_type' => '',
            );
            // 查找这个孩子最早一条已付学费账单判断是否为新生
            $firstInvoice = null;
            // 查找这个孩子最近一条有效学费账单判断折扣类型及付款类型
            $lastInvoice = null;
            foreach($_model->_studentsInvoices as $_invoice){
                // 账单筛选，上学期
                if ($semester == 1) {
                    if ($_invoice->fee_type != 1 && $_invoice->startdate >= $timePoints[2]) {
                        continue;
                    }
                }
                // 账单筛选，下学期
                if ($semester == 2) {
                    if ($_invoice->fee_type != 1 && $_invoice->startdate < $timePoints[2]) {
                        continue;
                    }
                }
                if ($_invoice->inout === Invoice::INVOICE_INOUT_IN){

                    if ($_invoice->payment_type == 'tuition') {
                        if (!$lastInvoice) {
                            $lastInvoice = $_invoice;
                        }
                        if ($lastInvoice && $_invoice->invoice_id > $lastInvoice->invoice_id) {
                            $lastInvoice = $_invoice;
                        }
                    }

                    //应收费用
                    $result['invoices'][$_model->childid][$_invoice->payment_type]['accountsAmount'] = isset($result['invoices'][$_model->childid][$_invoice->payment_type]['accountsAmount']) ? $result['invoices'][$_model->childid][$_invoice->payment_type]['accountsAmount'] +$_invoice->amount : $_invoice->amount;

                    //已收费用
                    if (!empty($_invoice->invoiceTransaction)){
                        foreach ($_invoice->invoiceTransaction as $_transaction){
                            if ($_transaction->inout == 'in') {
                                $result['invoices'][$_model->childid][$_transaction->payment_type]['payedAmount'] =
                                isset($result['invoices'][$_model->childid][$_transaction->payment_type]['payedAmount']) ?
                                    $result['invoices'][$_model->childid][$_transaction->payment_type]['payedAmount'] +
                                    $_transaction->amount :
                                    $_transaction->amount;
                                $result['invoices'][$_model->childid]['sum']['payedAmount'] += $_transaction->amount;
                            } else {
                                $result['invoices'][$_model->childid][$_transaction->payment_type]['payedAmount'] =
                                    isset($result['invoices'][$_model->childid][$_transaction->payment_type]['payedAmount']) ?
                                        $result['invoices'][$_model->childid][$_transaction->payment_type]['payedAmount'] -
                                        $_transaction->amount :
                                        0 - $_transaction->amount;
                                $result['invoices'][$_model->childid]['sum']['payedAmount'] -= $_transaction->amount;
                            }
                        }

                    }
                    //未收费用
                    $result['invoices'][$_model->childid][$_invoice->payment_type]['unpayAmount'] = $result['invoices'][$_model->childid][$_invoice->payment_type]['accountsAmount'] - $result['invoices'][$_model->childid][$_invoice->payment_type]['payedAmount'];
                    $result['invoices'][$_model->childid]['sum']['accountsAmount'] += $_invoice->amount;
                    $result['invoices'][$_model->childid]['sum']['unpayAmount'] = $result['invoices'][$_model->childid]['sum']['accountsAmount'] - $result['invoices'][$_model->childid]['sum']['payedAmount'];
                }else{
                    //已退学费
                    $refundAmount = 0;
                    if (!empty($_invoice->invoiceTransaction)){
                        foreach ($_invoice->invoiceTransaction as $_transaction){
                            $refundAmount += $_transaction->amount;
                            $result['invoices'][$_model->childid][$_transaction->payment_type]['refundAmount'] = isset($result['invoices'][$_model->childid][$_transaction->payment_type]['refundAmount']) ? $result['invoices'][$_model->childid][$_transaction->payment_type]['refundAmount']+$_transaction->amount : $_transaction->amount;
                        }
                        $result['invoices'][$_model->childid]['sum']['refundAmount'] += $refundAmount;
                    }
                }
            }

            if ($lastInvoice) {
                if ($lastInvoice->fee_type == 1) {
                    $result['students'][$lastInvoice->childid]['fee_type'] = '学年';
                }
                if ($lastInvoice->fee_type == 2) {
                    if (date('m', $lastInvoice->startdate) >= 8) {
                        $result['students'][$lastInvoice->childid]['fee_type'] = '上学期';
                    } else {
                        $result['students'][$lastInvoice->childid]['fee_type'] = '下学期';
                    }
                }
                if ($lastInvoice->discount && $lastInvoice->discount->discountTitle) {
                    $result['students'][$lastInvoice->childid]['discount'] = $lastInvoice->discount->discountTitle->title_cn;
                }
            }
        };
        // 整合汇总
        foreach ($result['invoices'] as $childId => $invoices){
            foreach ($invoices as $type => $payments){
                if (in_array($type, array('tuition', 'deposit'))){
                    foreach ($payments as $payment => $amount) {
                        $result['invoices'][$childId]['tuition_sum'][$payment] += $amount;
                    }
                }
            }
        }
        return $result;
    }

    /*
     * 导出EXCEL
     */
    public function actionExport(){
        $calendarId = Yii::app()->request->getParam('calendarId',0);
        $startYear = Yii::app()->request->getParam('startYear','');
        $semester = Yii::app()->request->getParam('semester',0);
        Yii::import('common.models.calendar.*');
        if ($calendarId && $startYear){
            $endYear = $startYear+1;
            $classList = IvyClass::model()->getClassList($this->branchId, $calendarId);
            $filename = $startYear.'-'.$endYear.$this->branchObj->title."学生费用报表";
            $this->exprotHeardToExcel($filename);
            ob_start();
            foreach ($classList as $val){
                $ret = $this->getStudentInvoices($val->classid,$calendarId,$semester);
                $this->exportDataToExcel($ret);
            }
            $ret = $this->getStudentInvoices(0,$calendarId,$semester);
            $this->exportDataToExcel($ret);
            ob_end_flush();
        }
    }

    /*
     * 生成EXCEL文件头
     */
    public function exprotHeardToExcel($filename){
        header("Content-type:application/vnd.ms-excel");
        header("Content-Disposition:attachment;filename=$filename.xls");
        $paymentTypeCss = $this->paymentTypeCss();
        echo $this->_t("序号")."\t";
        echo $this->_t("English Name")."\t";
        echo $this->_t("姓名")."\t";
        echo $this->_t("状态")."\t";
        echo $this->_t("年级")."\t";
        echo $this->_t("退学流程")."\t";
        echo $this->_t("账户余额")."\t";
        echo $this->_t("是否新生")."\t";
        echo $this->_t("付款类型")."\t";
        echo $this->_t("使用折扣")."\t";
        echo $this->_t("学费	")."\t";
        echo "\t";
        echo "\t";
        echo $this->_t("预缴学费")."\t";
        echo "\t";
        echo "\t";
        echo "\t";
        echo $this->_t("学费汇总")."\t";
        echo "\t";
        echo "\t";
        echo "\t";
        echo $this->_t("校车费")."\t";
        echo "\t";
        echo "\t";
        echo "\t";
        echo $this->_t("午餐费")."\t";
        echo "\t";
        echo "\t";
        echo "\t";
        echo $this->_t("校服费")."\t";
        echo "\t";
        echo "\t";
        echo "\t";
        echo $this->_t("汇总")."\t";
        echo "\t";
        echo "\t";
        echo "\n";
        echo "\t";
        echo "\t";
        echo "\t";
        echo "\t";
        echo "\t";
        echo "\t";
        echo "\t";
        echo "\t";
        echo "\t";
        echo "\t";
        for($i=1;$i<=7;$i++){
            foreach ($paymentTypeCss as $val){
                echo $this->_t($val['title'])."\t";
            }
        }
    }

    /*
     * 导入数据到EXCEL
     */
    public function exportDataToExcel($ret){
        //孩子的基本信息
        Yii::import('application.components.policy.*');
        $policyApi = new PolicyApi($this->branchId);
        $invoiceTypes = $this->invoiceTypes();
        $paymentTypeCss = array_keys($this->paymentTypeCss());
        if (isset($ret['students'])){
            foreach ($ret['students'] as $val){
                echo "\n";
                echo $this->_t($val['id'])."\t";
                echo $this->_t($val['eName'])."\t";
                echo $this->_t($val['cName'])."\t";
                echo $this->_t($val['status'])."\t";
                echo $this->_t($val['className'])."\t";
                echo $this->_t($val['withDrawalTxt'])."\t";
                echo $this->_t($val['credit'])."\t";
                echo $this->_t($val['isnew'])."\t";
                echo $this->_t($val['fee_type'])."\t";
                echo $this->_t($val['discount'])."\t";
                // 孩子的费用信息
                if (isset($ret['invoices'][$val['id']])){
                    foreach ($invoiceTypes as $type){
                        foreach ($paymentTypeCss as $payment){
                            if (isset($ret['invoices'][$val['id']][$type][$payment])){
                                echo number_format($ret['invoices'][$val['id']][$type][$payment],2)."\t";
                            }else{
                                echo "0.00\t";
                            }
                        }
                    }
                }else{
                    for($i=1;$i<=28;$i++){
                        echo 0 . "\t";
                    }
                }
            }
        }
    }

    public function _t($str='')
    {
        return iconv('utf-8', 'GBK', $str);
    }

    public function paymentTypeCss(){
        return array(
                    'accountsAmount'=>array(
                        'title'=>Yii::t('payment', '应收'),
                        'css'=> 'label-info',
                        'default'=> true,
                    ),
                    'payedAmount'=> array(
                        'title'=>Yii::t('payment', '已收'),
                        'css'=> 'label-success',
                        'default'=> false,
                    ),
                    'unpayAmount'=> array(
                        'title'=>Yii::t('payment', '未收'),
                        'css'=> 'label-danger',
                        'default'=> true,
                    ),
                    'refundAmount'=> array(
                        'title'=>Yii::t('payment', '已退'),
                        'css'=> 'label-warning',
                        'default'=> true,
                    ),
                );
    }

    public function invoiceTypes(){
        global $paymentMappings;
        return array(
                $paymentMappings['dbFeeType'][FEETYPE_TUITION],
                $paymentMappings['dbFeeType'][FEETYPE_DEPOSIT],
                'tuition_sum',
                $paymentMappings['dbFeeType'][FEETYPE_SCHOOLBUS],
                $paymentMappings['dbFeeType'][FEETYPE_LUNCH],
                $paymentMappings['dbFeeType'][FEETYPE_UNIFORM],
                'sum'
        );
    }

    function getWithdrawalStudentListByYear($year)
    {
        if (!in_array($this->branchId, CommonUtils::dsSchoolList())) {
            return array();
        }
        $requestUrl = 'withdrawal/studentList';
        $requestData['school_id'] = $this->branchId;
        $requestData['year'] = $year;

        $res = CommonUtils::requestDsOnline($requestUrl, $requestData);
        if ($res['code'] == 0) {
            return $res['data'];
        } else {
            return array();
        }
    }
}
