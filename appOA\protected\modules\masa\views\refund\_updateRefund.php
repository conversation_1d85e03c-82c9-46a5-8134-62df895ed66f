<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
    </button>
    <h4 class="modal-title" id="exampleModalLabel">查看退费信息</h4>
</div>
<?php $form = $this->beginWidget('CActiveForm', array(
    'id' => 'refund',
    'enableAjaxValidation' => false,
    'action' => $this->createUrl('updateRefund', array('id'=>$model->id)),
    'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form'),
)); ?>
<div class="modal-body">
    <div class="form-group">
        <label class="col-xs-2 text-right"> # </label>

        <div class="col-xs-9">
            <div><?php echo $model->id ?></div>
        </div>
    </div>
    <!--课程名字-->
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'course_id'); ?></label>
        <div class="col-xs-10 control-label">
            <label class="pull-left"><?php echo $model->asaCourse->title ?></label>
        </div>
    </div>

    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo Yii::t('child','孩子姓名'); ?></label>
        <div class="col-xs-10 control-label">
            <label class="pull-left"><?php echo ($childsName) ? $childsName->getChildName() : "" ?></label>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo Yii::t('child','班级'); ?></label>
        <div class="col-xs-10 control-label">
            <label class="pull-left"><?php echo ($childClass) ? $childClass->title : "" ?></label>
        </div>
    </div>
    <!--退费节数-->
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'refund_class_count'); ?></label>
        <div class="col-xs-10">
            <?php echo $form->textField($model, 'refund_class_count', array('class' => 'form-control')); ?>
        </div>
    </div>
    <!--退费生效日期-->
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'dropout_date'); ?></label>
        <div class="col-xs-10">
            <?php echo $form->textField($model, 'dropout_date', array('class' => 'form-control', 'id' => 'datepicker')); ?>
        </div>
    </div>
    <!--在什么时候退费 开课前，开课中，特殊-->
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'refund_type'); ?></label>
        <div class="col-xs-10">
            <?php echo $form->DropDownList($model, 'refund_type', $type, array('class' => 'form-control')); ?>
        </div>
    </div>
    <!--退费原因-->
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'refund_reason'); ?></label>
        <div class="col-xs-10">
            <?php echo $form->DropDownList($model, 'refund_reason', $refundReason, array('maxlength' => 255, 'class' => 'form-control')); ?>
        </div>
    </div>
    <!--退费说明-->
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'memo'); ?></label>
        <div class="col-xs-10">
            <?php echo $form->textArea($model, 'memo', array('class' => 'form-control')); ?>
        </div>
    </div>
    <!--存款人信息-->
    <?php if($model->refund_method == 2){ ?>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'payee_info'); ?></label>
        <div class="col-xs-10">
            <table class="table table-bordered">
                <tbody>
                <tr>
                    <td><?php echo Yii::t('asa', 'bank'); ?></td>
                    <td><?php echo Yii::t('asa', 'accountName'); ?></td>
                    <td><?php echo Yii::t('asa', 'accountNumber'); ?></td>
                </tr>
                <tr>
                    <?php $info = CJSON::decode($model->payee_info); ?>
                    <td><?php echo  CHtml::textField('payeeInfo[银行]', $info['银行'], array('class' => 'form-control')) ?></td>
                    <td><?php echo  CHtml::textField('payeeInfo[姓名]', $info['姓名'], array('class' => 'form-control')) ?></td>
                    <td><?php echo  CHtml::textField('payeeInfo[账号]', $info['账号'], array('class' => 'form-control')) ?></td>
                </tr>
                </tbody>
            </table>

        </div>
    </div>
    <?php } ?>

    <?php if(in_array($model->refund_type, array(4,5)) && $exchengeRefund): ?>
        <div class="form-group">
            <label class="col-xs-2 text-right">换课信息</label>

            <div class="col-xs-9">
                <h4>旧课信息</h4>
                <table class="table table-bordered col-xs-12">
                    <tr>
                        <td class="col-xs-2"><?php echo Yii::t('asa', '课程名称'); ?></td>
                        <td class="col-xs-2"><?php echo Yii::t('asa', '课程课时和价格'); ?></td>
                        <td class="col-xs-2"><?php echo Yii::t('asa', '已上课程'); ?></td>
                        <td class="col-xs-2"><?php echo Yii::t('asa', '已退课时'); ?></td>
                        <td class="col-xs-2"><?php echo Yii::t('asa', '旧课余额'); ?></td>
                    </tr>
                    <tr>
                        <td class="col-xs-2"><?php echo $exchengeRefund['oldCourseTitle']; ?></td>
                        <td class="col-xs-2"><?php echo $exchengeRefund['oldCourse']; ?></td>
                        <td class="col-xs-2"><?php echo $exchengeRefund['oldCourseNum']; ?></td>
                        <td class="col-xs-2"><?php echo $exchengeRefund['oldRefundCount']; ?></td>
                        <td class="col-xs-2"><?php echo $exchengeRefund['oldAmount']; ?></td>
                    </tr>
                </table>
                <h4>新课信息</h4>
                <table class="table table-bordered col-xs-12">
                    <tr>
                        <td class="col-xs-2"><?php echo Yii::t('asa', '新课名称'); ?></td>
                        <td class="col-xs-2"><?php echo Yii::t('asa', '课程课时和价格'); ?></td>
                        <td class="col-xs-2"></td>
                        <td class="col-xs-2"></td>
                        <td class="col-xs-2"><?php echo Yii::t('asa', ' 新课所需金额'); ?></td>
                    </tr>
                    <tr>
                        <td class="col-xs-2"><?php echo $exchengeRefund['newCourseTitle']->getTitle(); ?></td>
                        <td class="col-xs-2"><?php echo $exchengeRefund['newCourse']; ?></td>
                        <td class="col-xs-2"></td>
                        <td class="col-xs-2"></td>
                        <td class="col-xs-2"><?php echo $exchengeRefund['newCourseNum']; ?></td>
                    </tr>
                </table>

                <div>
                    <span>价格差额</span>
                    <span class="pull-right"><?php echo $exchengeRefund['difference'] ?></span>
                </div>
            </div>
        </div>
    <?php endif;?>

    <!--退费文件-->
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'refund_files'); ?></label>
        <div class="col-xs-10">
            <div class="mb5" id="attachment">
            <?php
                foreach(CJSON::decode($model->refund_files) as $k=>$file){

            ?>
                <span class="mt delFund_<?php echo $k ?>" >
                    <?php  echo  CHtml::hiddenField('refund_files[]',$file); ?>
                    <a href="<?php echo $this->createUrl('downloadsRefund',array('fileName' => $file))?>" target="_blank" class="btn btn-info btn-xs"><span class="glyphicon glyphicon-file"></span> 点击查看图片</a>
                    <span class="btn btn-danger btn-xs delfile"  onclick="delrefund('<?php echo $model->id ?>','<?php echo $file ?>','<?php  echo "delFund_".$k ?>')" value="<?php echo $file ?>"><i class="glyphicon glyphicon-remove"></i></span>
                </span>
            <?php }
               ?>
            </div>
            <input id="inputfile" onchange="uploadata()" type="file" >
        </div>
    </div>
    <!--操作时间-->
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'updated'); ?></label>
        <div class="col-xs-10">
            <div><?php echo date('Y-m-d H:i:s', $model->updated)?></div>
        </div>
    </div>
    <!--操作时间-->
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'status'); ?></label>
        <div class="col-xs-10">
            <?php echo $form->checkBox($model, 'status', array('1'), array('class' => 'form-control')); ?>
            <div>是否重新提交</div>
        </div>
    </div>
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button></div>
<?php $this->endWidget(); ?>
<script>
    $('#datepicker').datepicker({
        changeMonth: true,
        changeYear: true,
        dateFormat:'yy-mm-dd'
    });

    function uploadata () {
        $("#fileinfo").text('');
        var data = new FormData();
        $.each($('#inputfile')[0].files, function(i, file) {
            data.append('upload_file', file);
        });
        $.ajax({
            url:'<?php echo $this->createUrl('refundFiles'); ?>',
            data:data,
            type:'post',
            dataType:'json',
            contentType: false,    //不可缺
            processData: false,    //不可缺
            success : function (data) {
                if (data.msg == 'success') {
                    //清空文件域
                    $("#inputfile").after($("#inputfile").clone().val(""));
                    $("#inputfile").remove();
                    //显示附件
                    var atta = '<span class="mr5"><a class="btn btn-info btn-xs" target="_blank" href="'+data.url+'">'+data.saveName+' <span></span></a><input type="hidden" name="refund_files[]" value="'+data.saveName+'" > <span class="btn btn-danger btn-xs delfile" _value="' + data.saveName + '"><i class="glyphicon glyphicon-remove"></i></span></span> ';
                    $("#attachment").append(atta);
                }else{
                    $("#fileinfo").text(data.msg);
                }
            },
            error : function (data) {
                $("#fileinfo").text(data.msg);
            }
        });
    }

    function delrefund (id,fileName,detFunde) {
        $.ajax({
            url:'<?php echo $this->createUrl('delReFundFiles'); ?>',
            data:{fileName : fileName,id:id},
            type:'post',
            dataType:'json',
            success : function (data) {
                if (data.state == 'success') {
                    $('.' + detFunde).remove();
                }else{
                    $("#fileinfo").text(data.message);
                }
            },
            error : function (data) {
                $("#fileinfo").text(data.message);
            }
        });
    }

    $(document).on('click','.delfile',function(){
        var _this = $(this);
        var filename = $(this).attr('_value');
        $.ajax({
            url:'<?php echo $this->createUrl('refund/delFiles'); ?>',
            data:{fileName : filename},
            type:'post',
            dataType:'json',
            success : function (data) {
                if (data.state == 'success') {
                    _this.parent().remove()
                }else{
                    $("#fileinfo").text(data.message);
                }
            },
            error : function (data) {
                $("#fileinfo").text(data.message);
            }
        });
    })


    function cbUpdateRefind()
    {
        location=location;
    }

</script>