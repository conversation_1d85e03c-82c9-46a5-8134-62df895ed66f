<?php

/**
 * This is the model class for table "ivy_semester_report".
 *
 * The followings are the available columns in table 'ivy_semester_report':
 * @property integer $id
 * @property string $schoolid
 * @property integer $classid
 * @property integer $childid
 * @property integer $yid
 * @property integer $semester
 * @property integer $pre_cover
 * @property integer $suf_cover
 * @property string $comments
 * @property integer $custom
 * @property string $custom_pdf
 * @property integer $stat
 * @property integer $timestamp
 * @property integer $uid
 */
class SemesterReport extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return SemesterReport the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_semester_report';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('comments', 'required'),
			array('classid, childid, yid, semester, pre_cover, suf_cover, custom, stat, timestamp, uid', 'numerical', 'integerOnly'=>true),
			array('schoolid', 'length', 'max'=>15),
			array('custom_pdf', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, schoolid, classid, childid, yid, semester, pre_cover, suf_cover, comments, custom, custom_pdf, stat, timestamp, uid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'classInfo'=>array(self::BELONGS_TO, 'IvyClass', 'classid'),
            'schoolInfo'=>array(self::BELONGS_TO, 'Branch', 'schoolid'),
            'yInfo'=>array(self::BELONGS_TO, 'Calendar', 'yid'),
            'extInfo'=>array(self::HAS_MANY, 'SemesterReportExt', 'reportid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'schoolid' => 'Schoolid',
			'classid' => 'Classid',
			'childid' => 'Childid',
			'yid' => 'Yid',
			'semester' => 'Semester',
			'pre_cover' => 'Pre Cover',
			'suf_cover' => 'Suf Cover',
			'comments' => 'Comments',
			'custom' => 'Custom',
			'custom_pdf' => 'Custom Pdf',
			'stat' => 'Stat',
			'timestamp' => 'Timestamp',
			'uid' => 'Uid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('semester',$this->semester);
		$criteria->compare('pre_cover',$this->pre_cover);
		$criteria->compare('suf_cover',$this->suf_cover);
		$criteria->compare('comments',$this->comments,true);
		$criteria->compare('custom',$this->custom);
		$criteria->compare('custom_pdf',$this->custom_pdf,true);
		$criteria->compare('stat',$this->stat);
		$criteria->compare('timestamp',$this->timestamp);
		$criteria->compare('uid',$this->uid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}