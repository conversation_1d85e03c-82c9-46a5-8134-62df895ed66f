<?php
class InvoiceController extends Controller{
    public function __construct($id, $module = null) {
        parent::__construct($id, $module);
//        $token = Yii::app()->request->getParam('token','');
//        if ($token != md5(CommonUtils::SECURITY_KEY)){
//            $this->module->_sendResponse(403);
//        }
    }
    
    /**
     * 生成定位金账单
     */
    public function actionGenInvoice(){
        $childid = Yii::app()->request->getParam('childid', 0);
        $startyear = Yii::app()->request->getParam('startyear', 0);
        $semester = Yii::app()->request->getParam('semester', 1);
        $classid = Yii::app()->request->getParam('classid', 0);
        // 折扣类型，1 学期九五折（同时开第一学期九七折），2 学期九七折，3 兄弟姐妹折扣
        $discountType = Yii::app()->request->getParam('discountType', 0);
        $authKey = Yii::app()->request->getParam('authKey','');

        if($authKey != md5($childid.$startyear.$semester.$classid.$discountType.CommonUtils::SECURITY_KEY)){
            $this->module->_sendResponse(403);
        }

        $result = array();
        if ($childid && $startyear && $classid) {
            Yii::import('common.models.invoice.Invoice');
            Yii::import('common.models.calendar.*');
            Yii::import('application.components.policy.*');

            $childObj = ChildProfileBasic::model()->findByPk($childid);
            $classObj = IvyClass::model()->findByPk($classid);
            if (!$classObj) {
                $this->module->_sendResponse(200, CJSON::encode(array("msg" => "班级不存在")));
            }
            $schoolId = $classObj->schoolid;

            $feeType = IvyClass::getClassTypeForFee();
            $policy = new IvyPolicy("pay", $startyear, $schoolId);
            if (!$policy) {
                $this->module->_sendResponse(200, CJSON::encode(array("msg" => "收退费政策不存在")));
            }
            $monthAmount = $policy->configs['TUITION_CALCULATION_BASE'][$feeType[$classObj->classtype]]['BY_MONTH']['AMOUNT']['FULL5D'];

            if ($schoolId == "BJ_QFF") {
                $monthAmount = $policy->configs['TUITION_CALCULATION_BASE']['BY_MONTH']['AMOUNT']['FULL5D'];
                if ($classObj->classtype == "k") {
                    $monthAmount = $policy->configs['TUITION_CALCULATION_BASE']['BY_MONTH']['AMOUNT']['FULL5D2'];
                }
            }

            if ($monthAmount <= 0) {
                $this->module->_sendResponse(200, CJSON::encode(array("msg" => "未找到匹配月付金额")));
            }
            
            $schoolyear = CalendarSchool::model()->with('cTemplate')->findByAttributes(array(
                'startyear' => $startyear,
                'branchid' => $schoolId,
            ));
            if (!$schoolyear) {
                $this->module->_sendResponse(200, CJSON::encode(array("msg" => "校历不存在")));
            }
            $result = array("invoiceid" => array());
            if ($childObj && $schoolyear) {
                $discountid = 0;
                $amount = $monthAmount*5;

                $disModel = $this->getDiscountid($discountType, $schoolId);

                if ($discountType > 0) {
                    $discountid = $disModel['id'];
                    $amount = round($amount*$disModel['discount']);
                }
                $invoiceId = $this->generalInvoice($schoolyear, $classid, $childObj, $amount, $policy, $semester, $discountid);
                $result['invoiceid'][] = $invoiceId;

                // 额外生成学年账单
                if ($discountType == 1) {
                    $disModel = $this->getDiscountid(4, $schoolId);
                    $discountid = $disModel['id'];
                    $amount = $monthAmount*10;
                    $amount = round($amount*$disModel['discount']);
                    $semester = 0;
                    $invoiceId2 = $this->generalInvoice($schoolyear, $classid, $childObj, $amount, $policy, $semester, $discountid);
                    $result['invoiceid'][] = $invoiceId2;
                }

            }
        }

        $this->module->_sendResponse(200, CJSON::encode($result));
    }

    // 生成账单
    protected function generalInvoice($schoolyear, $classid, $childObj, $amount, $policy, $semester, $discountid)
    {
        $timepoints = explode(',', $schoolyear->cTemplate->timepoints);
        $fee_type = 2;
        if ($semester == 1) {
            $byType = 'PAYGROUP_SEMESTER';
            $startDate = strtotime(date('Y', $timepoints[0]) . '-09-01');
            $endDate = $timepoints[1];
         } elseif ($semester == 2) {
             $byType = 'PAYGROUP_SEMESTER';
             $startDate = $timepoints[2];
             $endDate = $timepoints[3];
         } else {
            $fee_type = 1;
            $byType = 'PAYGROUP_ANNUAL';
            $startDate = strtotime(date('Y', $timepoints[0]) . '-09-01');
            $endDate = $timepoints[3];
         }

         $params = array(
             'byType' => $byType,
             'feeType' => 'FEETYPE_TUITION',
             'startDate' => $startDate,
             'endDate' => $endDate,
             'feeProgram' => 'FULL5D',
         );

        $invoiceAttr = array(
            'calendar_id' => $schoolyear->yid,
            'amount' => $amount,
            'original_amount' => $amount,
            'schoolid' => $childObj->schoolid,
            'childid' => $childObj->childid,
            'classid' => $classid,
            'payment_type' => 'tuition',
            'inout' => Invoice::INVOICE_INOUT_IN,
            'startdate' => $startDate,
            'enddate' => $endDate,
            'fee_type' => $fee_type,
            'fee_program' => 'FULL5D',
            'duetime' => time()+7*86400,
            'title' => $policy->genInvoiceTitle($params),
            'discount_id' => $discountid,
            'userid' => 1,
            'timestamp' => time(),
            'status' => Invoice::STATS_UNPAID,
            'child_service_info' => 'a:5:{s:3:"mon";i:20;s:3:"tue";i:20;s:3:"wed";i:20;s:3:"thu";i:20;s:3:"fri";i:20;}',
        );
        $model = new Invoice();
        $model->setAttributes($invoiceAttr);
        if($model->save()){
            Yii::import('common.components.AliYun.MQ.MQProducer');
            CommonUtils::addProducer(MQProducer::TAG_APPLY, "Tuition.first", $model->invoice_id, 10080);
            return $model->invoice_id;
        }
    }

    // 获取折扣ID
    protected function getDiscountid($discountType, $schoolid)
    {
        $arr = array(
            'BJ_DS' => array(
                1 => array('id' => 506, 'discount' => 0.97), // 学期 97 折
                2 => array('id' => 506, 'discount' => 0.97), // 学期 97 折
                3 => array('id' => 235, 'discount' => 0.90), // 兄弟姐妹 90 折
                4 => array('id' => 507, 'discount' => 0.95), // 学年 95 折
            ),
            'BJ_SLT' => array(
                1 => array('id' => 508, 'discount' => 0.97), // 学期 97 折
                2 => array('id' => 508, 'discount' => 0.97), // 学期 97 折
                3 => array('id' => 263, 'discount' => 0.90), // 兄弟姐妹 90 折
                4 => array('id' => 509, 'discount' => 0.95), // 学年 95 折
            ),
            'BJ_QFF' => array(
                1 => array('id' => 510, 'discount' => 0.97), // 学期 97 折
                2 => array('id' => 510, 'discount' => 0.97), // 学期 97 折
                3 => array('id' => 515, 'discount' => 0.90), // 兄弟姐妹 90 折
                4 => array('id' => 511, 'discount' => 0.95), // 学年 95 折
            ),
        );
        return $arr[$schoolid][$discountType];
    }

    public function actionPaid()
    {
        $invoiceids = Yii::app()->request->getParam('invoiceids', '');
        $authKey = Yii::app()->request->getParam('authKey','');

        if($authKey != md5($invoiceids.CommonUtils::SECURITY_KEY)){
            $this->module->_sendResponse(403);
        }

        $result = array();
        if($invoiceids) {
            Yii::import('common.models.invoice.*');

            $invoiceids = explode(',', $invoiceids);

            $items = Invoice::model()->findAllByPk($invoiceids);

            foreach ($items as $item) {
                $result[$item->invoice_id] = array(
                    'title' => $item->title,
                    'status' => $item->status,
                    'amount' => $item->amount,
                    'duetime' => date('Y-m-d H:i:s', $item->duetime),
                    'paidSum' => $item->paidSum,
                );
            }
        }

        $this->module->_sendResponse(200, CJSON::encode($result));
    }

    public function actionOrder()
    {
        $invoiceid = Yii::app()->request->getParam('invoiceid', '');
        $amount = Yii::app()->request->getParam('amount', 0);
        $openid = Yii::app()->request->getParam('openid', '');
        $authKey = Yii::app()->request->getParam('authKey','');

        if($authKey != md5($invoiceid.$amount.$openid.CommonUtils::SECURITY_KEY)){
            $this->module->_sendResponse(403);
        }
        $result = array();
        if($invoiceid) {
            Yii::import('common.models.invoice.*');

            $wxpayInfo = CommonUtils::LoadConfig('CfgWxPayGlobal');
            $invoice = Invoice::model()->findByPk($invoiceid);
            if (in_array($invoice->status, array(10, 30)) && $amount <= ($invoice->amount-$invoice->paidSum)) {
                $childObj = ChildProfileBasic::model()->findByPk($invoice->childid);
                $schoolId = $invoice->schoolid;
                $cfg = $wxpayInfo[$schoolId];
                if ($orderId = $this->createOrder($cfg, $childObj, $amount, array($invoice))) {
                    $result = array(
                        'orderId' => $orderId,
                        'title' => $invoice->title,
                        'schoolId' => $schoolId,
                    );
                }
            }
        }
        $this->module->_sendResponse(200, CJSON::encode($result));
    }


    public function createOrder($cfg, $childObj, $totalFee, $invoiceObjs, $payType = 'JSAPI')
    {
        Yii::import('common.models.wxpay.*');
        Yii::import('common.models.invoice.*');

        $invoiceId = current($invoiceObjs)->invoice_id;
        $number_code = $cfg['number_code'];
        $time = time();
        $wechatPayOrder = new WechatPayOrder();
        $wechatPayOrder->orderid = $wechatPayOrder->genOrderID($number_code.$invoiceId);
        $wechatPayOrder->payable_amount = $totalFee;
        $wechatPayOrder->fact_amount = 0;
        $wechatPayOrder->schoolid = current($invoiceObjs)->schoolid;
        $wechatPayOrder->childid = $childObj->childid;
        $wechatPayOrder->type = $payType;
        $wechatPayOrder->status = 0;
        $wechatPayOrder->settlement_status = 0;
        $wechatPayOrder->order_time = $time;
        $wechatPayOrder->update_timestamp = $time;
        $wechatPayOrder->uid = 1;
        if ($wechatPayOrder->save()) {
            foreach ($invoiceObjs as $invoice) {
                $wechatPayOrderItem = new WechatPayOrderItem();
                $wechatPayOrderItem->orderid = $wechatPayOrder->orderid;
                $wechatPayOrderItem->invoice_id = $invoice->invoice_id;
                $wechatPayOrderItem->amount = Invoice::model()->renderDueAmount($invoice);
                if (count($invoiceObjs)==1)
                    $wechatPayOrderItem->amount = $totalFee;
                $wechatPayOrderItem->save();
            }
            return $wechatPayOrder->orderid;
        }
        else {
            print_r($wechatPayOrder->getErrors());
        }
        return false;
    }
}
