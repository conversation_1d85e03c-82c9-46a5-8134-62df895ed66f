<div style="color:#274257;">
    <div style="font-family: 'Microsoft Yahei'">
        <p>尊敬的家长:</p>
        <!------------------------------逾期的--------------------------------->
        <?php if (isset($invoiceList['overdues']) && count($invoiceList['overdues']) == count($invoiceList['info'])):?>

            <?php
            $titleInfo = array();
            $amountInfo = 0;
            foreach ($invoiceList['info'] as $key =>$val){
                $titleInfo[$key] =  $val["title"];
                $amountInfo += $val['amount'];
            }
            ?>
            <?php if($schoolid === 'BJ_QF' || $schoolid === 'BJ_QFF'){ ?>
                <p>您的账单已经在两次提醒后仍尚有未付金额，账单详情如下：</p>
<!--            <p>您的账单已经在两次提醒后仍尚有未付金额，账单详情如下：</p>-->
            <?php }else{?>
<!--                <p>您的账单已经在两次提醒后仍尚有未付金额，账单详情如下：</p>-->
                <p>从系统反馈的收款数据显示，您的孩子<?php echo $name;?>同学已逾期未缴纳 <?php echo implode(' / ',$titleInfo);?>，金额为人民币<?php echo number_format($amountInfo,2)?>元。</p>
            <p>上学期关校转在线教学期间，您如缴纳了校餐校车费，退费已退至您孩子的个人账户余额，并默认用于第二学期的未支付账单。</p>
            <?php }?>

            <p>姓名: <?php echo $name;?></p>
            <?php
            $i = 1;
            foreach ($invoiceList['info'] as $val):?>
                <p> <?php if (count($invoiceList['info'])>1){echo $i.". ";}?>账单标题：
                    <?php echo $val['title'];?>
                    <br>
                    <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>校园：<?php echo $allbranches[$val['schoolid']]['title'];?><br>
                    <?php if (isset($invoiceList['unpay']) && count($invoiceList['unpay']) == count($invoiceList['info']) && $schoolid !== "BJ_QF" && $schoolid !== "BJ_QFF"):?>
                        <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>付款截止日期：<?php echo OA::formatDateTime($val['duetime']);?><br>
                    <?php endif;?>
                    <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>账单金额：<?php echo number_format($val['payAmounts'],2);?> RMB<br>
                    <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>已付金额：<?php echo number_format($val['tAmount'],2);?> RMB<br>
                    <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>未付金额：<?php echo number_format($val['amount'],2);?> RMB<br>
                    <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>账单截止日：<?php echo OA::formatDateTime($val['duetime']);?><br>

                </p>
                <?php
                $i++;
            endforeach;?>

            <p>
                为保证您孩子的顺利返校，以及学校提供服务的延续性，请您在本通知单日期5个工作日内足额缴费。如您已经缴费，请忽略此通知。
            </p>

<!--        提示更换了学校名字-->
            <?php if($schoolid === 'BJ_QF' || $schoolid === 'BJ_QFF'){?>
<!--                <p>另外需要提醒您的是，学校的中文名称已从“北京市朝阳区启明星双语小规模幼儿园”变更为“北京市朝阳区启明星小规模幼儿园”银行信息也已相应变更。如果您从境内以网银转账或支票的方式支付，麻烦相应修改银行信息，请将收款人中的“双语”二字去掉，银行/分行和帐户号码都保持不变；如果您选择从境外汇款，学校的英文名称没有改变，仍是“Daystar Academy ELC”。</p>-->
            <?php }else{?>
<!--                <p>另外再次提醒您的是，学校的中文名称已从“北京市朝阳区启明星学校”变更为“北京市朝阳区启明星学校”，银行信息也已相应变更。如果您从境内以网银转账或支票的方式支付，麻烦相应修改银行信息，请将收款人中的“双语”二字去掉，银行/分行和帐户号码都保持不变；如果您选择从境外汇款，学校的英文名称没有改变，仍是“Daystar Academy”。</p>-->
            <?php }?>
            <!----------提示更换了学校名字end---------->
            <!----------款方式提示-------->
            <?php if($schoolid === 'BJ_QF' || $schoolid === 'BJ_QFF'){ ?>
                <p>如果您选用网银转账的方式支付，<span style="color:red;">请务必在转账的备注里标明孩子的姓名</span>，以便财务及时确认您的付款。如有任何问题请和财务部门联系(<EMAIL>)。谢谢！</p>
            <?php }else if ($schoolid === 'BJ_SLT' ){?>
                <p>如果您选用网银转账的方式支付，<span style="color:red;">请务必在转账的备注里标明孩子的姓名，班级/年级还有所选的校区（北皋/三里屯）以便财务及时确认您的付款。</span>如有任何问题请和财务部门联系:<EMAIL>。谢谢</p>
            <?php }else{ ?>
                <p>如果您选用网银转账的方式支付，<span style="color:red;">请务必在转账的备注里标明孩子的姓名，班级/年级还有所选的校区（北皋/三里屯）以便财务及时确认您的付款。</span>如有任何问题请和财务部门联系:<EMAIL>。谢谢</p>
            <?php } ?>
            <!----------付款方式提示end---------->
        <!------------------------------------------------逾期提示结束----------------------------------------------------------------------->
        <!----------------------------------------------未逾期提示----------------------------------------------------------------------->
        <?php elseif (isset($invoiceList['unpay']) && count($invoiceList['unpay']) == count($invoiceList['info'])):?>
            <?php if($schoolid === 'BJ_QF' || $schoolid === 'BJ_QFF'){ ?>
                <p>请查收您孩子的2023-2024学年第一学期学费账单并在付款截止日2023年5月15日前支付。</p>
            <?php }else{?>
                <p>请查收您孩子的2023-2024学年第一学期学费账单并在付款截止日2023年5月15日前支付。</p>
<!--                <p>请注意，早鸟年费账单的截至日期是2023年4月15日，过期后账单将自动作废。</p>-->
<!--                <p>如果您选择按学期学费的标准分学期缴纳学费，请您忽略早鸟年费账单，第一学期的学期学费账单将于4月下旬开具。</p>-->
            <?php }?>

            <p>姓名: <?php echo $name;?></p>
            <?php
            $i = 1;
            foreach ($invoiceList['info'] as $val):?>
                <p> <?php if (count($invoiceList['info'])>1){echo $i.". ";}?>账单标题：<?php echo $val['title'];?><br>
                    <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>校园：<?php echo $allbranches[$val['schoolid']]['title'];?><br>
                    <?php if (isset($invoiceList['unpay']) && count($invoiceList['unpay']) == count($invoiceList['info']) && $schoolid !== "BJ_QF" && $schoolid !== "BJ_QFF"):?>
                        <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>付款截止日期：<?php echo OA::formatDateTime($val['duetime']);?><br>
                    <?php endif;?>
                    <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>账单金额：<?php echo number_format($val['payAmounts'],2);?> RMB<br>
                    <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>已付金额：<?php echo number_format($val['tAmount'],2);?> RMB<br>
                    <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>未付金额：<?php echo number_format($val['amount'],2);?> RMB<br>
                    <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>账单截止日：<?php echo OA::formatDateTime($val['duetime']);?><br>

                </p>
                <?php
                $i++;
            endforeach;?>

            <!----------提示更换了学校名字---------->
            <?php if($schoolid === 'BJ_QF' || $schoolid === 'BJ_QFF'){?>
                <p>另外需要提醒您的是，2022年4月学校的中文名称已从“北京市朝阳区启明星双语小规模幼儿园”变更为“北京市朝阳区启明星小规模幼儿园”银行信息也已相应变更。如果您从境内以网银转账或支票的方式支付，烦请相应修改银行信息，银行/分行和帐户号码都保持不变:如果您选择从境外汇款，学校的英文名称没有改变，仍是Daystar Academy ELC"。</p>
            <?php }else{?>
                <p>另外再次提醒您的是，2022年4月学校的中文名称已从“北京市朝阳区启明星双语学校”变更为“北京市朝阳区启明星学校”，银行信息也已相应变更。如果您从境内以网银转账或支票的方式支付，麻烦相应修改银行信息，请将收款人中的“双语”二字去掉，银行/分行和帐户号码都保持不变；如果您选择从境外汇款，学校的英文名称没有改变，仍是“Daystar Academy”。</p>
            <?php }?>
            <!----------提示更换了学校名字end---------->
            <!----------款方式提示-------->
            <?php if($schoolid === 'BJ_QF' || $schoolid === 'BJ_QFF'){ ?>
                <p>如果您选用网银转账的方式支付，<span style="color:red;">请务必在转账的备注里标明孩子的姓名</span>，以便财务及时确认您的付款。谢谢！</p>
            <?php }else if ($schoolid === 'BJ_SLT' ){?>
                <p>如果您选用网银转账的方式支付，<span style="color:red;">请务必在转账的备注里标明孩子的姓名，班级/年级还有所选的校区（北皋/三里屯）以便财务及时确认您的付款。</span>如有任何问题请和财务部门联系(<EMAIL>)。谢谢！</p>
            <?php }else{ ?>
                <p>如果您选用网银转账的方式支付，<span style="color:red;">请务必在转账的备注里标明孩子的姓名，班级/年级还有所选的校区（北皋/三里屯）以便财务及时确认您的付款。</span>如有任何问题请和财务部门联系(<EMAIL>)。谢谢！</p>
            <?php } ?>
            <!----------款方式提示结束-------->
        <?php else:?>
        <!----------------------------------------------未逾期提示结束----------------------------------------------------------------------->

            <!---------------------------------------------逾期和未逾期的一起发送邮件的提示--------------------------------------------->
            <?php if($schoolid === 'BJ_QF' || $schoolid === 'BJ_QFF'){ ?>
                <p>以下是您2022-2023学年第二学期账单，截止日为2023年2月2日，请您按时完成支付。</p>
                <p>您还有2022-2023第一学期未付账单，也请您在2023年2月2日前足额缴费。</p>
            <?php }else{?>
                <p>以下是您2022-2023学年第二学期账单，截止日为2023年1月13日，请您按时完成支付。</p>
                <p>您还有2022-2023第一学期未付账单，也请您在2023年1月13日前足额缴费。</p>
            <?php }?>
            <p>姓名: <?php echo $name;?></p>
            <?php
            $i = 1;
            foreach ($invoiceList['info'] as $val):?>
                <p> <?php if (count($invoiceList['info'])>1){echo $i.". ";}?>账单标题：<?php echo $val['title'];?><br>
                    <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>校园：<?php echo $allbranches[$val['schoolid']]['title'];?><br>
                    <?php if (isset($invoiceList['unpay']) && count($invoiceList['unpay']) == count($invoiceList['info']) && $schoolid !== "BJ_QF" && $schoolid !== "BJ_QFF"):?>
                        <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>付款截止日期：<?php echo OA::formatDateTime($val['duetime']);?><br>
                    <?php endif;?>
                    <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>账单金额：<?php echo number_format($val['payAmounts'],2);?> RMB<br>
                    <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>已付金额：<?php echo number_format($val['tAmount'],2);?> RMB<br>
                    <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>未付金额：<?php echo number_format($val['amount'],2);?> RMB<br>
                    <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>账单截止日：<?php echo OA::formatDateTime($val['duetime']);?><br>

                </p>
                <?php
                $i++;
            endforeach;?>
            <!------提示更换了学校名字------>
            <?php if($schoolid === 'BJ_QF' || $schoolid === 'BJ_QFF'){?>
<!--                <p>另外需要提醒您的是，学校的中文名称已从 “北京市朝阳区启明星双语小规模幼儿园”变更为“北京市朝阳区启明星小规模幼儿园”银行信息也已相应变更。如果您从境内以网银转账或支票的方式支付，麻烦相应修改银行信息，请将收款人中的“双语”二字去掉，银行/分行和帐户号码都保持不变；如果您选择从境外汇款，学校的英文名称没有改变，仍是“Daystar Academy ELC”。</p>-->
            <?php }else{?>
<!--                <p>另外再次提醒您的是，学校的中文名称已从“北京市朝阳区启明星学校”变更为“北京市朝阳区启明星学校”，银行信息也已相应变更。如果您从境内以网银转账或支票的方式支付，麻烦相应修改银行信息，请将收款人中的“双语”二字去掉，银行/分行和帐户号码都保持不变；如果您选择从境外汇款，学校的英文名称没有改变，仍是“Daystar Academy”。</p>-->
            <?php }?>
            <!------提示更换了学校名字end------>
            <!----------款方式提示-------->
            <?php if($schoolid === 'BJ_QF' || $schoolid === 'BJ_QFF'){ ?>
                <p>如果您选用网银转账的方式支付，<span style="color:red;">请务必在转账的备注里标明孩子的姓名</span>，以便财务及时确认您的付款。如有任何问题请和财务部门联系。谢谢！</p>
            <?php }else if ($schoolid === 'BJ_SLT' ){?>
                <p>如果您选用网银转账的方式支付，<span style="color:red;">请务必在转账的备注里标明孩子的姓名，班级/年级还有所选的校区（北皋/三里屯）以便财务及时确认您的付款。</span>谢谢！</p>
            <?php }else{ ?>
                <p>如果您选用网银转账的方式支付，<span style="color:red;">请务必在转账的备注里标明孩子的姓名，班级/年级还有所选的校区（北皋/三里屯）以便财务及时确认您的付款。</span>谢谢！</p>
            <?php } ?>
            <!----------款方式提示结束-------->

        <!---------------------------------------------逾期和未逾期的一起发送邮件的提示结束--------------------------------------------->
        <?php endif;?>

        <?php if (isset($invoiceList['overdues']) && count($invoiceList['overdues']) == count($invoiceList['info'])):?>
<!--            <p>-->
<!--                如果您有任何疑问，请联系启明星财务部：-->
<!--                 --><?php //if($schoolid == 'BJ_DS') {
//                     echo '<EMAIL>';
//                 }elseif($schoolid == 'BJ_SLT'){
//                     echo '<EMAIL>';
//                 }else{
//                     echo '<EMAIL>';
//                 } ?>
<!--            </p>-->
            <?php if($schoolid === 'BJ_QF' || $schoolid === 'BJ_QFF'){?>
                <p>诚挚的祝福</p>
                <p>启明星幼儿园</p>
            <?php }else{?>
                <p>启明星感谢您一直以来的信任和支持！</p>
                <p>启明星财务部</p>
            <?php }?>
        <?php elseif (isset($invoiceList['unpay']) && count($invoiceList['unpay']) == count($invoiceList['info'])):?>
            <p>
                <?php if($schoolid === 'BJ_QF' || $schoolid === 'BJ_QFF'){?>
                    如果您有任何疑问，请联系启明星幼儿园行政部：
                <?php }else{?>
<!--                    如果您有任何疑问，请联系启明星财务部：-->
                <?php }?>
                <?php if($schoolid === 'BJ_DS') {
//                    echo '<EMAIL>';
                }elseif($schoolid === 'BJ_SLT'){
//                    echo '<EMAIL>';
                }else{
                    echo '<EMAIL>。<p>感谢您长久以来的合作与支持。</p>';
                } ?>
            </p>
            <?php if($schoolid === 'BJ_QF' || $schoolid === 'BJ_QFF'){?>
                <p>诚挚的祝福</p>
                <p>启明星幼儿园</p>
            <?php }else{?>
                <p>启明星感谢您一直以来的信任和支持！</p>
                <p>启明星财务部</p>
            <?php }?>

        <?php else:?>
            <p>
                <?php if($schoolid === 'BJ_QF' || $schoolid === 'BJ_QFF'){?>
                    如果您有任何疑问，请联系启明星幼儿园行政部：
                <?php }else{?>
                    如果您有任何疑问，请联系启明星财务部：
                <?php }?>
                <?php if($schoolid === 'BJ_DS') {
                    echo '<EMAIL>';
                }elseif($schoolid === 'BJ_SLT'){
                    echo '<EMAIL>';
                }else{
                    echo '<EMAIL>。感谢您长久以来的合作与支持。';
                } ?>
            </p>
            <?php if($schoolid === 'BJ_QF' || $schoolid === 'BJ_QFF'){?>
                <p>诚挚的祝福</p>
                <p>启明星幼儿园</p>
            <?php }else{?>
                <p>启明星感谢您一直以来的信任和支持！</p>
                <p>启明星财务部</p>
            <?php }?>
        <?php endif;?>
        <p>自动邮件，无需回复
            <!--，如有问题请直接联系校园（<a href="mailto:p /*echo $supportEmail;*/?>">php /*echo $supportEmail;*/?></a>--></p>
        <p><?php echo OA::formatDateTime(time());?></p>
    </div>
</div>
<?php
echo '<br/><br/>';
?>
<div style="color:#644436;">
    <div style="font-family: 'Trebuchet MS','Microsoft Yahei',Arial,Helvetica,sans-serif">
        <p>Dear Parents:</p>
        <!------------------------------逾期的--------------------------------->
        <?php if (isset($invoiceList['overdues']) && count($invoiceList['overdues']) == count($invoiceList['info'])):?>
            <?php if($schoolid === 'BJ_QF' || $schoolid === 'BJ_QFF'){ ?>
                <p>This is a kind reminder that you still have unpaid invoice(s) after two overdue notices. Please see the details of the outstanding invoice(s) as below:  </p>
            <?php }else{?>
                <p>This is a kind reminder that you have following overdue invoice(s): </p>
<!--                <p>This is a kind reminder that you still have unpaid invoice(s) after two overdue notices. Please see the details of the outstanding invoice(s) as below:  </p>-->
            <p>For the online learning period during campus close in Term 1, prepaid school bus and school meal (if any) has been credited to your child's student account, and has been used against your Term 2 invoices by default.</p>
            <?php }?>


            <p>Student Name: <?php echo $name;?></p>
            <?php
            $i = 1;
            foreach ($invoiceList['info'] as $val):?>
                <p> <?php if (count($invoiceList['info'])>1){echo $i.". ";}?>Invoice Title: <?php echo $val['title'];?><br>
                    <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>Campus: <?php echo $allbranches[$val['schoolid']]['title'];?><br>
                    <?php if (isset($invoiceList['unpay']) && count($invoiceList['unpay']) == count($invoiceList['info']) && $schoolid !== "BJ_QF"&& $schoolid !== "BJ_QFF"):?>
                        <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>Date Due: <?php echo OA::formatDateTime($val['duetime']);?><br>
                    <?php endif; ?>

                    <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>Invoice Amount: <?php echo number_format($val['payAmounts'],2);?> RMB<br>
                    <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>Amount Paid: <?php echo number_format($val['tAmount'],2);?> RMB<br>
                    <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>Amount Unpaid: <?php echo number_format($val['amount'],2);?> RMB<br>
                    <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>Date Due: <?php echo OA::formatDateTime($val['duetime']);?><br>
                </p>
                <?php
                $i++;
            endforeach;?>

            <p>
                Please make the above payment(s) in five business days from this e-mail date, in order to allow your child to return on time and to enjoy continuous services from Daystar. If you have already made the payments, please disregard this e-mail.
            </p>
            <!---------------提示更换了学校名字--------------->
            <?php if($schoolid === 'BJ_QF' || $schoolid === 'BJ_QFF'){?>
<!--                <p>We’d like to bring it to your attention that, the re-registration for our school’s Chinese name from “北京市朝阳区启明星双语小规模幼儿园” to “北京市朝阳区启明星小规模幼儿园” is completed, and the bank information has also changed accordingly. If you choose to make the payment through bank transfer or cheque within China, please change the beneficiary’s name to “北京市朝阳区启明星小规模幼儿园” with “双语” deleted from the old name. If you choose to make the payment through bank transfer from overseas, the beneficiary’s English name is still “Daystar Academy ELC” with no change. The bank/branch and account number also stay the same with no change.</p>-->
            <?php }else{?>
<!--                <p>Again we’d like to bring it to your attention that, the re-registration for our school’s Chinese name from “北京市朝阳区启明星学校” to “北京市朝阳区启明星学校” is completed, and the bank information has also changed accordingly. If you choose to make the payment through bank transfer or cheque within China, please change the beneficiary’s name to “北京市朝阳区启明星学校” with “双语” deleted from the old name. If you choose to make the payment through bank transfer from overseas, the beneficiary’s English name is still “Daystar Academy” with no change. The bank/branch and account number also stay the same with no change.</p>-->
            <?php }?>
        <!---------------提示更换了学校名字end--------------->
        <!---------------付款方式提示--------------->
            <?php if($schoolid === 'BJ_QF' || $schoolid === 'BJ_QFF'){ ?>
                <p>If you choose to pay through bank transfer, <span style="color:red;">please clearly state your child’s name</span>, on the bank transfer in order for Finance to confirm your payment in time. If you have any questions, please contact the finance office (<EMAIL>). Thank you！</p>
            <?php }else if ($schoolid === 'BJ_SLT' ){?>
                <p>If you choose to pay through bank transfer, <span style="color:red;">please clearly state your child’s name, class/grade and campus (Beigao/SLT) on the bank transfer</span> in order for Finance to confirm your payment in time. Should you have any questions, please feel free to contact Daystar <NAME_EMAIL>. </p>
            <?php }else{ ?>
                <p>If you choose to pay through bank transfer, <span style="color:red;">please clearly state your child’s name, class/grade and campus (Beigao/SLT) on the bank transfer</span> in order for Finance to confirm your payment in time. Should you have any questions, please feel free to contact Daystar <NAME_EMAIL>. </p>
            <?php } ?>
        <!---------------付款方式提示end--------------->
        <!------------------------------逾期的结束--------------------------------->

        <!------------------------------未逾期的--------------------------------->
        <?php elseif (isset($invoiceList['unpay']) && count($invoiceList['unpay']) == count($invoiceList['info'])):?>
            <?php if($schoolid === 'BJ_QF' || $schoolid === 'BJ_QFF'){ ?>
                <p>Please refer to the following 2023-2024 Term One tuition invoice and make the payment by the due date May 15,2023.</p>
            <?php }else{?>
                <p>Please refer to the following 2023-2024 Term One tuition invoice and make the payment by the due date May 15, 2023.  </p>
<!--                <p>Please be kindly noted that the deadline for Early-bird Annual Fee payment is April 15th, 2023, and the invoice will be revoked automatically when past due.</p>-->
<!--                <p>If you’d like to pay by term at Term Fee, please ignore the Early-bird Annual Fee invoice. The Term Fee invoice for Term 1 will be issued in late April.</p>-->
            <?php }?>

            <p>Student Name: <?php echo $name;?></p>
            <?php
            $i = 1;
            foreach ($invoiceList['info'] as $val):?>
                <p> <?php if (count($invoiceList['info'])>1){echo $i.". ";}?>Invoice Title: <?php echo $val['title'];?><br>
                    <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>Campus: <?php echo $allbranches[$val['schoolid']]['title'];?><br>
                    <?php if (isset($invoiceList['unpay']) && count($invoiceList['unpay']) == count($invoiceList['info']) && $schoolid !== "BJ_QF"&& $schoolid !== "BJ_QFF"):?>
                        <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>Date Due: <?php echo OA::formatDateTime($val['duetime']);?><br>
                    <?php endif; ?>

                    <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>Invoice Amount: <?php echo number_format($val['payAmounts'],2);?> RMB<br>
                    <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>Amount Paid: <?php echo number_format($val['tAmount'],2);?> RMB<br>
                    <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>Amount Unpaid: <?php echo number_format($val['amount'],2);?> RMB<br>
                    <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>Date Due: <?php echo OA::formatDateTime($val['duetime']);?><br>
                </p>
                <?php
                $i++;
            endforeach;?>

            <!----------提示更换了学校名字---------->
            <?php if($schoolid === 'BJ_QF' || $schoolid === 'BJ_QFF'){?>
                <p>We would like to bring to your attention that the re-registration of our school's Chinese name from "北京市朝阳区启明星双语小规模幼儿园" to "北京市朝阳区启明星小规模幼儿园" has been completed, and the bank information has also been changed accordingly. If you choose to make the payment through bank transfer or cheque within China, please change the beneficiary's name to "北京市朝阳区启明星小规模幼儿园" with "双语" deleted from the old name. If you choose to make the payment through a bank transfer from overseas, the beneficiary's English name is still "Daystar Academy ELC" with no change. The bank/branch and account number also remain the same with no change.</p>
            <?php }else{?>
                <p>Again we’d like to bring it to your attention that, the re-registration for our school’s Chinese name from “北京市朝阳区启明星双语学校” to “北京市朝阳区启明星学校” was completed in April 2022, and the bank information has also changed accordingly. If you choose to make the payment through bank transfer or cheque within China, please change the beneficiary’s name to “北京市朝阳区启明星学校” with “双语” deleted from the old name. If you choose to make the payment through bank transfer from overseas, the beneficiary’s English name is still “Daystar Academy” with no change. The bank/branch and account number also stay the same with no change.</p>
            <?php }?>
            <!----------提示更换了学校名字end---------->
            <?php if($schoolid === 'BJ_QF' || $schoolid === 'BJ_QFF'){ ?>
                <p>If you choose to pay through bank transfer, please make sure to include your child's name on the bank transfer in order for our Finance Department to confirm your payment in time. </p>
            <?php }else if ($schoolid === 'BJ_SLT' ){?>
                <p>If you choose to pay through bank transfer, <span style="">please clearly state your child’s name, class/grade and campus (Beigao/SLT) on the bank transfer</span> in order for Finance to confirm your payment in time.  If you have any questions, please contact the finance office (<EMAIL>). Thank you！</p>
            <?php }else{ ?>
                <p>If you choose to pay through bank transfer, <span style="">please clearly state your child’s name, class/grade and campus (Beigao/SLT) on the bank transfer</span> in order for Finance to confirm your payment in time.  If you have any questions, please contact the finance office (<EMAIL>). Thank you！ </p>
            <?php } ?>
        <!------------------------------未逾期的结束--------------------------------->
        <?php else:?>
        <!--------------------------------逾期和未逾期的一起发送邮件的提示-------------------------------->
            <?php if($schoolid === 'BJ_QF' || $schoolid === 'BJ_QFF'){ ?>
                <p>Please see your 2022-2023 Term Two invoice(s) as shown below. The due date is February 2nd, 2023. Please make your payment before the due date. </p>
                <p>You still have the following 2022-23 Term One unpaid invoice(s), please also make the full payment by February 2nd, 2023.</p>
            <?php }else{?>
                <p>Please see your 2022-2023 Term Two invoice(s) as shown below. The due date is January 13th, 2023. Please make your payment before the due date. </p>
                <p>You still have the following 2022-23 Term One unpaid invoice(s), please also make the full payment by January 13th, 2023.</p>
            <?php }?>
            <p>Student Name: <?php echo $name;?></p>
            <?php
            $i = 1;
            foreach ($invoiceList['info'] as $val):?>
                <p> <?php if (count($invoiceList['info'])>1){echo $i.". ";}?>Invoice Title: <?php echo $val['title'];?><br>
                    <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>Campus: <?php echo $allbranches[$val['schoolid']]['title'];?><br>
                    <?php if (isset($invoiceList['unpay']) && count($invoiceList['unpay']) == count($invoiceList['info']) && $schoolid !== "BJ_QF"&& $schoolid !== "BJ_QFF"):?>
                        <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>Date Due: <?php echo OA::formatDateTime($val['duetime']);?><br>
                    <?php endif; ?>

                    <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>Invoice Amount: <?php echo number_format($val['payAmounts'],2);?> RMB<br>
                    <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>Amount Paid: <?php echo number_format($val['tAmount'],2);?> RMB<br>
                    <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>Amount Unpaid: <?php echo number_format($val['amount'],2);?> RMB<br>
                    <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>Date Due: <?php echo OA::formatDateTime($val['duetime']);?><br>
                </p>
                <?php
                $i++;
            endforeach;?>
            <!------------------提示更换了学校名字------------------>
            <?php if($schoolid === 'BJ_QF' || $schoolid === 'BJ_QFF'){?>
<!--                <p>We’d like to bring it to your attention that, the re-registration for our school’s Chinese name from “北京市朝阳区启明星双语小规模幼儿园” to “北京市朝阳区启明星小规模幼儿园” is completed, and the bank information has also changed accordingly. If you choose to make the payment through bank transfer or cheque within China, please change the beneficiary’s name to “北京市朝阳区启明星小规模幼儿园” with “双语” deleted from the old name. If you choose to make the payment through bank transfer from overseas, the beneficiary’s English name is still “Daystar Academy ELC” with no change. The bank/branch and account number also stay the same with no change.</p>-->
            <?php }else{?>
<!--                <p>Again we’d like to bring it to your attention that, the re-registration for our school’s Chinese name from “北京市朝阳区启明星学校” to “北京市朝阳区启明星学校” is completed, and the bank information has also changed accordingly. If you choose to make the payment through bank transfer or cheque within China, please change the beneficiary’s name to “北京市朝阳区启明星学校” with “双语” deleted from the old name. If you choose to make the payment through bank transfer from overseas, the beneficiary’s English name is still “Daystar Academy” with no change. The bank/branch and account number also stay the same with no change.</p>-->
            <?php }?>
            <!------------------提示更换了学校名字end------------------>

            <?php if($schoolid === 'BJ_QF' || $schoolid === 'BJ_QFF'){ ?>
                <p>If you choose to pay through bank transfer, <span style="color:red;">please clearly state your child’s name</span>, on the bank transfer in order for Finance to confirm your payment in time. </p>
            <?php }else if ($schoolid === 'BJ_SLT' ){?>
                <p>If you choose to pay through bank transfer, <span style="color:red;">please clearly state your child’s name, class/grade and campus (Beigao/SLT) on the bank transfer</span> in order for Finance to confirm your payment in time. Thank you！</p>
            <?php }else{ ?>
                <p>If you choose to pay through bank transfer, <span style="color:red;">please clearly state your child’s name, class/grade and campus (Beigao/SLT) on the bank transfer</span> in order for Finance to confirm your payment in time. Thank you！ </p>
            <?php } ?>
            <!--------------------------------逾期和未逾期的一起发送邮件的提示结束-------------------------------->
        <?php endif;?>


        <?php if (isset($invoiceList['overdues']) && count($invoiceList['overdues']) == count($invoiceList['info'])):?>
            <p>
<!--                Should you have any questions, please feel free to contact Daystar Finance at-->
<!--                --><?php //if($schoolid == 'BJ_DS') {
//                    echo '<EMAIL>';
//                }elseif($schoolid == 'BJ_SLT'){
//                    echo '<EMAIL>';
//                }else{
//                    echo '<EMAIL>';
//                } ?>
                Your continuous support is highly appreciated.
            </p>
<!--            <p>Sincerely,</p>-->
            <p>Best Regards</p>
        <?php if($schoolid === 'BJ_QF' || $schoolid === 'BJ_QFF'){ ?>
                <p>Daystar Early Learning Center</p>
        <?php }else{?>
                <p>Daystar Finance</p>
        <?php }?>

        <?php elseif (isset($invoiceList['unpay']) && count($invoiceList['unpay']) == count($invoiceList['info'])):?>
            <p>
                <?php if($schoolid === 'BJ_QF' || $schoolid === 'BJ_QFF'){?>
                    If you have any questions, please do not hesitate to contact Daystar ELC Admin at
                <?php }else{?>
<!--                    Should you have any questions, please feel free to contact Daystar Finance at-->
                <?php }?>
                <?php if($schoolid === 'BJ_DS') {
//                    echo '<EMAIL>';
                }elseif($schoolid === 'BJ_SLT'){
//                    echo '<EMAIL>';
                }else{
                    echo '<EMAIL>.';
                } ?>
                We appreciate your continuous support.
            </p>
            <p>Sincerely,</p>
            <p>Best Regards</p>
        <?php if($schoolid === 'BJ_QF' || $schoolid === 'BJ_QFF'){ ?>
                <p>Daystar Early Learning Center</p>
        <?php }else{?>
                <p>Daystar Academy Finance Department</p>
        <?php }?>
        <?php else:?>
            <p>
                <?php if($schoolid === 'BJ_QF' || $schoolid === 'BJ_QFF'){?>
                    Should you have any questions, please feel free to contact Daystar ELC Admin at
                <?php }else{?>
                    Should you have any questions, please feel free to contact Daystar Finance at
                <?php }?>
                <?php if($schoolid === 'BJ_DS') {
                    echo '<EMAIL>';
                }elseif($schoolid === 'BJ_SLT'){
                    echo '<EMAIL>';
                }else{
                    echo '<EMAIL>';
                } ?>
                Your continuous support is highly appreciated.
            </p>
            <p>Sincerely,</p>
            <p>Best Regards</p>
        <?php if($schoolid === 'BJ_QF' || $schoolid === 'BJ_QFF'){ ?>
            <p>Daystar Early Learning Center</p>
        <?php }else{?>
             <p>Daystar Academy Finance Department</p>
        <?php }?>
        <?php endif; ?>
        <!-- <p>For questions, please contact your campus administrators directly (<a href="mailto:?php /*echo $supportEmail;*/?>">?php /*echo $supportEmail;*/?></a>).</p>-->
        <p> <?php echo OA::formatDateTime(time());?></p>
    </div>
</div>
<p></p>
<hr>
<div>
    <p style="font-weight: bold;">Bank Information / 银行信息</p>
    <table cellpadding="0" border="0">
        <?php foreach($bankinfo as $info):?>
            <tr style="padding: 5px 0;">
                <td style="font-family: 'Trebuchet MS','Microsoft Yahei',Arial,Helvetica,sans-serif;font-size: 12px;" valign="top">
                    <?php echo $info['title_en'];?>：<br/>
                    <?php echo $info['title_cn'];?>：
                </td>
                <td style="font-family: 'Trebuchet MS','Microsoft Yahei',Arial,Helvetica,sans-serif;font-size: 12px;">
                    <?php  echo $info['content'];?>
                </td>
            </tr>
        <?php endforeach;?>
    </table>
</div>

