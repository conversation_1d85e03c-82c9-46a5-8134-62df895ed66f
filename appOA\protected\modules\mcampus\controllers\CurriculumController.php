<?php

class CurriculumController extends ProtectedController
{
    public $actionAccessAuths = array(
        'EditProject'           => 'o_E_Edu_Common',
        'EditActivity'          => 'o_E_Edu_Common',
        'EditProject'           => 'o_E_Edu_Common',

        'DeleteProject'         => 'o_E_Edu_Common',
        'DeleteActivity'         => 'o_E_Edu_Common',
        'Uncheckedactivity'     => 'o_E_Edu_Common',
        'CopyFavorites'     => 'o_E_Edu_Common',

        'SimilarActivity'     => 'o_E_Edu_Common',
        'DeleteSimilarActivity'     => 'o_E_Edu_Common',
    );

    public $leftMenu;

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

        //初始化选择校园页面
//        $this->branchSelectParams['hideOffice'] = true;
//        $this->branchSelectParams['urlArray'] = array('//mcampus/curriculum/project');

        //左侧菜单栏
        $this->leftMenu = array(array('project',Yii::t('Curriculum','Projects List')),
            array('activity',Yii::t('Curriculum','Activity List')),
            array('favorites',Yii::t('Curriculum','My Projects')),
            array('tags',Yii::t('Curriculum','Tags')),
            );
        if (Yii::app()->user->checkAccess('o_E_Edu_Common')) {
            $this->leftMenu[] = array('uncheckedactivity',Yii::t('Curriculum','Activity Candidates'));
        }
    }

    /**
     * 所有项目列表
     */
    public function actionProject()
    {
        Yii::import('common.models.portfolio.*');
        Yii::import('common.models.Term');
 
        $termModel = new Term;
        $diglossia = array();
        $diglossia['age'] = $termModel->getAgeList();
        $diglossia['season'] = array(
            50=>Yii::t('curriculum','Any Season'),
            10=>Yii::t('curriculum','Spring'),
            20=>Yii::t('curriculum','Summer'),
            30=>Yii::t('curriculum','Fall'),
            40=>Yii::t('curriculum','Winter'),
            );
        $diglossia['official'] = array(
            1=>Yii::t('curriculum','Education Team'),
            0=>Yii::t('curriculum','Other Team'),
            3=>Yii::t('curriculum','Any Team'),
            );

        $criteria = new CDbCriteria;
        //构建搜索条件
        $criteria->condition = "appeared = 0";
        $season = Yii::app()->request->getParam('season', '');
        $age = Yii::app()->request->getParam('age', '');
        $official = Yii::app()->request->getParam('official', 3);
        $search = Yii::app()->request->getParam('search', '');
        if(!empty($search)){
            $criteria->condition .= " AND concat_ws(',',t.cn_title,t.en_title,t.cn_desc,t.en_desc) like '%{$search}%' ";
        }
        if (!empty($season)){
            $criteria->condition .= " AND FIND_IN_SET('$season',t.season) ";
        }
        if (!empty($age)){
            $criteria->condition .= " AND FIND_IN_SET('$age',t.age) ";
        }
        if ($official!=3) {
            $criteria->condition .= " AND t.official = $official ";
        }
        $criteria->order = 'official desc,pid desc';
        $projects = new CActiveDataProvider('CurProjects',array('criteria'=>$criteria,));
        $this->render('project',array('projects'=>$projects,'diglossia'=>$diglossia,));
    }

    /**
     * 单个项目详情
     */
    public function actionShowProject($pid=0)
    {
        Yii::import('common.models.portfolio.*');

        // 查找标签
        $criteria = new CDbCriteria;
        $criteria->condition = "tag_catid = 1 AND tag_itemid = $pid ";
        $tag = tagLink::model()->findAll($criteria);
        //查找项目
        $project = CurProjects::model()->findByPk($pid);
        if ($project) {
            $project->attachments = unserialize($project->attachments);
            //查找相关活动
            $criteria = new CDbCriteria;
            $criteria->condition .= "FIND_IN_SET('$pid',t.pids) ";
            $activity = CurActivityInfo::model()->findAll($criteria);
            $this->render('showproject',array(
                'project'=>$project,
                'activity'=>$activity,
                'tag'=>$tag,
                ));
            die();
        }
        $this->redirect(array('project'));
    }

    /**
     * 新建/编辑项目
     */
    public function actionEditProject($pid=0)
    {
        Yii::import('common.models.portfolio.*');       
        Yii::import('common.models.Term');
        
        //查找标签
        $criteria = new CDbCriteria;
        $criteria->condition = "tag_catid = 1 AND tag_itemid = $pid ";
        $tag = tagLink::model()->findAll($criteria);

        $termModel = new Term;
        $age = $termModel->getAgeList();
        $season = array(10=>'春季',20=>'夏季',30=>'秋季',40=>'冬季',50=>'全部季节');

        $project = CurProjects::model()->findByPk($pid);      
        if($project == null){
            $project = new CurProjects();
            $project->userid = $this->staff->uid;
        }else{
            //判断是否具有编辑权限
            if (Yii::app()->user->checkAccess('o_E_Edu_Common') || $this->staff->uid == $project->userid){
                $project->age = explode(',',$project->age);
                $project->attachments = unserialize($project->attachments);
            }else{
                $this->redirect(array('project'));
                die();
            }
        }
        if(isset($_POST['CurProjects'])){
            $allFile = $project->attachments;
            if($file = CUploadedFile::getInstanceByName('upload_file')){
                if ($file->getExtensionName() == 'pdf') {
                   $filePath = Yii::app()->params['OAUploadBasePath'] . '/curriculum/';
                   $ext = $file->getExtensionName();
                   $fileName = '5_' . uniqid() . '.' . $ext; 
                   $allFile[$fileName] = $file->name;
                }
            }
            $oldFile = Yii::app()->request->getParam('allfile');
            if (!empty($oldFile)) {
                foreach ($oldFile as $key => $value) {
                    $allFile[$key] = $value;
                }
            }
            $project->attributes = $_POST['CurProjects'];
            $project->age = implode(',',$_POST['CurProjects']['age']);
            $project->state = 10;
            $project->update_timestamp = time();
            if (!empty($allFile)) {
                $project->attachments = serialize($allFile);
            }
            if($project->save()){
                $pid = $project->pid;
                //删除旧标签
                $criteria = new CDbCriteria;
                $criteria->compare('tag_catid',1);
                $criteria->compare('tag_itemid',$pid);
                $oldTagLink = tagLink::model()->findAll($criteria);
                foreach ($oldTagLink as $key => $value) {
                    $value->tag->tag_count--;
                    $value->tag->save();
                }
                tagLink::model()->deleteAll($criteria);
                //存储新标签
                $tag = array_unique($_POST['tag']);
                if (!empty($tag)) {
                    foreach ($tag as $value) {
                        $tagObj = tagTag::model()->find('tag_term=:tag_term',array(':tag_term'=>$value));
                        $tagLinkObj = new tagLink;
                        if (!empty($tagObj)) {
                            $tagObj->tag_count++;
                            if ($tagObj->save()) {
                                $tagLinkObj->tag_id = $tagObj->tag_id;  
                                $tagLinkObj->tag_modid = 73;  
                                $tagLinkObj->tag_catid = 1;  
                                $tagLinkObj->tag_itemid = $pid;  
                                $tagLinkObj->tag_time = time(); 
                                $tagLinkObj->save(); 
                            }
                        }else{
                            $tagObj = new tagTag;
                            $tagObj->tag_term = $value;
                            $tagObj->tag_count = 1;
                            if($tagObj->save()){
                                $tagLinkObj->tag_id = $tagObj->tag_id; 
                                $tagLinkObj->tag_modid = 73;  
                                $tagLinkObj->tag_catid = 1;  
                                $tagLinkObj->tag_itemid = $pid;  
                                $tagLinkObj->tag_time = time();  
                                $tagLinkObj->save();
                            }
                        }
                    }
                }
                //存储附件
                if ($file) {
                    $file->saveAs($filePath . $fileName);
                }

                $this->addMessage('state', 'success');
                $this->addMessage('refresh', true);
                $this->addMessage('referer', $this->createUrl('showproject', array('pid'=>$project->pid)));
                $this->addMessage('message', Yii::t('message', 'Data Saved!'));
            }
            else{
                $this->addMessage('state', 'fail');
                $err = current($project->getErrors());
                $this->addMessage('message', $err[0]);
            }
            $this->showMessage();
        }
        $this->render('editproject',array(
            'project'=>$project,
            'age'=>$age,
            'season'=>$season,
            'tag'=>$tag,
            ));
    }

    /**
     * 删除项目附件
     */
    public function actionDeleteProAttachments()
    {
        Yii::import('common.models.portfolio.*');
        
        $atta = unserialize(Yii::app()->request->getParam('atta'));
        $id = Yii::app()->request->getParam('id','');
        $pid = Yii::app()->request->getParam('pid','');
        if(!empty($pid)){
            foreach ($atta as $key => $value) {
                if ($key == $id) {
                    unset($atta[$key]);
                    break;
                }
            }
            $project = CurProjects::model()->findByPk($pid);
            $project->attachments = serialize($atta);
            if ($project->save()) {
                $data['msg'] = 'success';
                unlink(Yii::app()->params['OAUploadBasePath'].'/curriculum/'.$id);
            }else{
                $data['msg'] = 'error';
                $data['info'] = $activity->getErrors();
            }
        }else{
            $data['msg'] = 'error';
            $data['info'] = '未找到项目';
            // unlink(Yii::app()->params['OAUploadBasePath'].'/curriculum/'.$id);
        }

        echo json_encode($data);
    }

    /**
     * 删除项目
     */
    public function actionDeleteProject()
    {
        Yii::import('common.models.portfolio.*');
        
        $pid = Yii::app()->request->getParam('pid',0);
        $project = CurProjects::model()->findByPk($pid);
        $atta = unserialize($project->attachments);
        //判断是否有权限删除
        if (Yii::app()->user->checkAccess('o_E_Edu_Common') || $this->staff->uid == $project->userid) {
            //项目中活动不为空
            $criteria = new CDbCriteria;
            $criteria->condition .= "FIND_IN_SET('$pid',t.pids) ";
            $activity = CurActivityInfo::model()->findAll($criteria);
            if(!empty($activity)) {
                $msg = '项目不为空，不允许删除';
            }else{
                if(CurProjects::model()->deleteByPk($pid)){
                    //删除关联的标签
                    $criteria = new CDbCriteria;
                    $criteria->compare('tag_catid',1);
                    $criteria->compare('tag_itemid',$pid);
                    tagLink::model()->deleteAll($criteria);
                    //删除关联的附件
                    if (!empty($atta)) {
                        $filePath = Yii::app()->params['OAUploadBasePath'] . '/curriculum/';
                        foreach ($atta as $key=>$value) {
                            unlink($filePath . $key);
                        }
                    }
                    $msg = 'success';
                }else{
                    $msg = '删除项目失败';
                }
            }
        }else{
            $msg = '没有权限';
        }
        $data = array('msg'=>$msg,'pid'=>$pid,);
        echo json_encode($data);
    }

    /**
     * 添加收藏
     */
    public function actionCollect()
    {
        Yii::import('common.models.portfolio.*');

        $pid = Yii::app()->request->getParam('pid',0);
        $res = 0;
        if (!empty($pid)) {
            $favorites = CurFavorites::model()->findAll('userid = :userid',array(':userid'=>$this->staff->uid));
            foreach ($favorites as $key => $value) {
                if ($pid == $value->pid) {
                    $res = 1;
                }
            }
            if (!$res) {
                $favorites = new CurFavorites;
                $favorites->pid = $pid;
                $favorites->classid = 0;
                $favorites->userid = $this->staff->uid;
                $favorites->update_timestamp = time();
                if ($favorites->save()) {
                    echo CJSON::encode(array(
                        'msg' => 'success',
                    ));
                    return;
                }
            }
            echo CJSON::encode(array(
                'msg' => 'error',
            ));
        }
    }

    /**
     * 取消收藏
     */
    public function actionRemoveCollect()
    {
        Yii::import('common.models.portfolio.*');

        $pid = Yii::app()->request->getParam('pid',0);
        if (!empty($pid)) {
            $favorites = CurFavorites::model()->find('pid = :pid',array(':pid'=>$pid));
            if ($favorites) {
                if($favorites->delete()){
                    echo CJSON::encode(array(
                        'msg' => 'success',
                    ));
                    return;
                }
            }
            echo CJSON::encode(array(
                'msg' => 'error',
            ));
        }
    }

    /**
     * 收藏项目
     */
    public function actionFavorites()
    {
       Yii::import('common.models.portfolio.*');

       $userid = $this->staff->uid;
       $projects = CurProjects::model()->findAll('userid=:userid',array(':userid'=>$userid));
       $favorites = CurFavorites::model()->findAll('userid=:userid',array(':userid'=>$userid));
       $this->render('favorites',array(
            'projects'=>$projects,
            'favorites'=>$favorites,
            ));
    }

    /**
     * 所有活动列表
     */
    public function actionActivity()
    {
        Yii::import('common.models.portfolio.*');
        Yii::import('common.models.Term');
        $termModel = new Term;
        $diglossia = array();
        $diglossia['age'] = $termModel->getAgeList();
        $diglossia['intelligence'] = $termModel->getMiList();
        $diglossia['learning'] = $termModel->getLdList();
        $diglossia['cultural'] = $termModel->getCulturalList();
        $diglossia['season'] = array(
            50=>Yii::t('curriculum','Any Season'),
            10=>Yii::t('curriculum','Spring'),
            20=>Yii::t('curriculum','Summer'),
            30=>Yii::t('curriculum','Fall'),
            40=>Yii::t('curriculum','Winter'),
            );
        $diglossia['official'] = array(
            1=>Yii::t('curriculum','Education Team'),
            0=>Yii::t('curriculum','Other Team'),
            3=>Yii::t('curriculum','Any Team'),
            );
        $diglossia['room'] = array(
            40=>Yii::t('curriculum','Suggested Place'),
            30=>Yii::t('curriculum','Any Place'),
            10=>Yii::t('curriculum','Indoor'),
            20=>Yii::t('curriculum','Outdoor'),
            );
        $diglossia['state'] = array(
            10=>Yii::t('curriculum','Normal'),
            20=>Yii::t('curriculum','Disabled'),
            );

        $criteria = new CDbCriteria;
        //构建搜索条件
        //pid为8的常规活动不显示
        $criteria->condition = 'pid != 8 ';
        $criteria->compare('state',10);

        $age = Yii::app()->request->getParam('age','');
        $intelligence = Yii::app()->request->getParam('intelligence','');
        $learning = Yii::app()->request->getParam('learning','');
        $cultural = Yii::app()->request->getParam('cultural','');
        $season = Yii::app()->request->getParam('season','');
        $room = Yii::app()->request->getParam('room',40);
        $official = Yii::app()->request->getParam('official',3);
        $search = Yii::app()->request->getParam('search','');

        if (!empty($search)){
            $criteria->condition .= " AND concat_ws(',',t.cn_title,t.en_title,t.cn_learning_obj,t.en_learning_obj,t.materials_cn_content,t.materials_en_content) like '%$search%' ";
        }
        if (!empty($age)){
            $criteria->condition .= "AND FIND_IN_SET('$age',t.age) ";
        }
        if (!empty($intelligence)){
            $criteria->condition .= "AND FIND_IN_SET('$intelligence',t.intelligence) ";
        }
        if (!empty($learning)){
            $criteria->condition .= "AND FIND_IN_SET('$learning',t.learning) ";
        }
        if (!empty($cultural)){
            $criteria->condition .= "AND FIND_IN_SET('$cultural',t.cultural) ";
        }
        if (!empty($season)){
            $criteria->condition .= "AND t.activity_type=$season ";
        }
        if ($room != 40){
            $criteria->condition .= "AND t.activity_type=$room ";
        }
        if ($official != 3){
            $criteria->condition .= "AND FIND_IN_SET('$official',t.official) ";
        }

        $criteria->order = 'aid desc';

        $userid = $this->staff->uid;
        $favorites = CurFavorites::model()->findAll('userid=:userid',array(':userid'=>$userid));

        $projects = CurProjects::model()->findAll('userid=:userid',array(':userid'=>$userid));

        $activity = new CActiveDataProvider('CurActivityInfo',array(
            'criteria'=>$criteria,
            ));
        $this->render('activity',array(
            'activity'=>$activity,
            'favorites'=>$favorites,
            'projects'=>$projects,
            'diglossia'=>$diglossia,));
    }

    /**
     * 获取活动名称(CN)
     */
    public function getActivityName($data)
    {
        if($data->copyfrom != 0){
            return $data->cn_title.'('.Yii::t('curriculum','Duplication').')';
        }
        echo $data->cn_title;
    }

    /**
     * 获取活动名称(EN)
     */
    public function getEnActivityName($data)
    {
        if($data->copyfrom != 0){
            return $data->en_title.'('.Yii::t('curriculum','Duplication').')';
        }
        echo $data->en_title;
    }

    /**
     * 未审核活动
     */
    public function actionUncheckedActivity($state=20)
    {
        Yii::import('common.models.portfolio.*');

        if($state == 20){
            $criteria->condition = 'state=:state';
            $criteria->params = array('state'=>$state);
            $criteria->order = 'aid desc';
        }
        $activity = new CActiveDataProvider('CurActivityInfo',array(
            'criteria'=>$criteria,
            ));
        $this->render('uncheckedactivity',array('activity'=>$activity,));
    }

    /**
     * 单个活动详情
     */
    public function actionShowActivity($aid=0)
    {
        Yii::import('common.models.portfolio.*');
        Yii::import('common.models.Term');
        
        $termModel = new Term;
        $diglossia = array();
        $diglossia['age'] = $termModel->getAgeList();
        $diglossia['intelligence'] = $termModel->getMiList();
        $diglossia['learning'] = $termModel->getLdList();
        $diglossia['cultural'] = $termModel->getCulturalList();
        $diglossia['season'] = array(
            50=>Yii::t('curriculum','Any Season'),
            10=>Yii::t('curriculum','Spring'),
            20=>Yii::t('curriculum','Summer'),
            30=>Yii::t('curriculum','Fall'),
            40=>Yii::t('curriculum','Winter'),
            );
        $diglossia['official'] = array(
            1=>Yii::t('curriculum','Official'),
            0=>Yii::t('curriculum','Other Team'),
            3=>Yii::t('curriculum','Any Team'),
            );
        $diglossia['room'] = array(
            40=>Yii::t('curriculum','Suggested Place'),
            30=>Yii::t('curriculum','Any Place'),
            10=>Yii::t('curriculum','Indoor'),
            20=>Yii::t('curriculum','Outdoor'),
            );
        $diglossia['state'] = array(
            10=>Yii::t('curriculum','Normal'),
            20=>Yii::t('curriculum','Disabled'),
            );

        // 查找标签
        $criteria = new CDbCriteria;
        $criteria->condition = "tag_catid = 2 AND tag_itemid = $aid ";
        $tag = tagLink::model()->findAll($criteria);
        //查找相关活动
        $criteria = new CDbCriteria;
        $criteria->compare('appeared',0);
        $criteria->order = 'pid desc';
        $projects = CurProjects::model()->findAll($criteria);
        $activity = CurActivityInfo::model()->findByPk($aid);
        if ($activity) {
            //活动所属的项目pids
            $pids = explode(',' , $activity->pids);
            $belongPro = CurProjects::model()->findAllByPk($pids);
            $sc_idObj = CurSimilarLink::model()->findAll('bc_id=:bc_id',array(':bc_id'=>$aid));
            $sc_id = array();
            foreach ($sc_idObj as $key => $value) {
                $sc_id[] = $value->sc_id;
            }
            $similarActivity = CurActivityInfo::model()->findAllByPk($sc_id);
            $this->render('showactivity',array('activity'=>$activity,
                'similarActivity'=>$similarActivity,
                'diglossia'=>$diglossia,
                'projects'=>$projects,
                'belongPro'=>$belongPro,
                'pids'=>$pids,
                'tag'=>$tag,
                ));
            die();
        }
        $this->redirect(array('activity'));
    }

    /**
     * 显示活动附件
     */
    public function showAttachments($atta)
    {
        $baseUrl = Yii::app()->params['OAUploadBaseUrl'].'/curriculum';
        $atta = unserialize(base64_decode($atta));
        foreach ($atta as $key => $value) {
            if ($value['mimetype']=='image/jpeg' || $value['mimetype']=='image/pjpeg' || $value['mimetype']=='image/png') {
                echo "<div><img src='{$baseUrl}/thumbs/{$value['name_saved']}' /><br /><br />";
                echo "<label class='btn btn-success btn-xs'> <a style='color:#fff' target='_blank' href='{$baseUrl}/{$value['name_saved']}' >{$value['name_display']}</a> <span class='glyphicon glyphicon-remove' aria-hidden='true' onclick='removeatta(this,{$key})'></span></label><hr /></div>";
            }else{
                echo "<div><label class='btn btn-success btn-xs'> <a style='color:#fff' target='_blank' href='{$baseUrl}/{$value['name_saved']}' >{$value['name_display']}</a> <span class='glyphicon glyphicon-remove' aria-hidden='true' onclick='removeatta(this,{$key})'></span></label><hr /></div>";
            }
        }
    }

    /**
     * 处理并保存上传的活动附件
     */
    public function actionSaveAttachments()
    {
        Yii::import('common.models.portfolio.*');
        Yii::import('application.extensions.image.Image');

        $file = CUploadedFile::getInstanceByName('upload_file');
        if ($file) {
            if ($file->size > 2*1024*1024) {
                $msg = '文件过大';
            } else{
                $needType = array('jpg','jpeg','png','rar','doc','txt','pdf','gif');
                if (!in_array($file->getExtensionName(), $needType)) {
                    $msg = '此文件类型不允许上传';
                }else{
                    $filePath = Yii::app()->params['OAUploadBasePath'] . '/curriculum/';
                    $tempFilePath = Yii::app()->params['OAUploadBasePath'] . '/curriculum/temps/';
                    $ext = $file->getExtensionName();
                    $fileName = '5_' . uniqid() . '.' . $ext;
                    if ($file->saveAs($tempFilePath . $fileName)){
                        //如果是图片类型则生成缩略图
                        if ($ext == 'jpg' || $ext == 'jpeg' ||$ext == 'png'){
                            $image = new Image($tempFilePath.$fileName);
                            $image->quality(100);
                            $image->sharpen(100);
                            $image->resize(500,500);
                            $image->save( $tempFilePath . 'thumbs/' . $fileName);
                        }
                        $aid = Yii::app()->request->getParam('aid',0);
                        //判断是新建还是编辑活动
                        if ($aid!=0) {
                            $baseUrl = Yii::app()->params['OAUploadBaseUrl'].'/curriculum/';
                            $activity = CurActivityInfo::model()->findByPk($aid);
                            $attachments = unserialize(base64_decode($activity->attachments));
                            $attachments[time()]['name_saved'] = $fileName;
                            $attachments[time()]['name_display'] = $file->name;
                            $attachments[time()]['mimetype'] = $file->type;
                            $attachments[time()]['num_download'] = 0;
                            $attachments = base64_encode(serialize($attachments));
                            $activity->attachments = $attachments;
                            if($activity->save()){
                                rename($tempFilePath . $fileName, $filePath . $fileName);
                                rename($tempFilePath . 'thumbs/' . $fileName, $filePath . 'thumbs/' . $fileName);
                                $msg = 'success';
                            }else{
                                unlink($tempFilePath . $fileName);
                                unlink($tempFilePath .'thumbs/'. $fileName);
                                $msg = '文件保存失败';
                            }
                        }else {
                            $baseUrl = Yii::app()->params['OAUploadBaseUrl'].'/curriculum/temps/';
                            $atta[time()]['name_saved'] = $fileName;
                            $atta[time()]['name_display'] = $file->name;
                            $atta[time()]['mimetype'] = $file->type;
                            $atta[time()]['num_download'] = 0;
                            $atta = serialize($atta);
                            $msg = 'success';
                        }
                    }else{
                        $msg = '文件上传失败';
                    }
                }
            }

            echo CJSON::encode(array(
                'url' => $baseUrl . $fileName . '?v' . time(),
                'ext' => $ext,
                'fileName' => $fileName,
                'name' => $file->name,
                'msg' => $msg,
                'atta' => $atta,
            ));
        }
    }

    /**
     * 删除活动附件
     */
    public function actionDeleteAttachments()
    {
        Yii::import('common.models.portfolio.*');
        
        $atta = unserialize(base64_decode($_POST['atta']));
        //如果是编辑状态下删除附件
        if($aid = $_POST['aid']){
            $id = $_POST['id'];
            foreach ($atta as $key => $value) {
                if ($key == $id) {
                    unset($atta[$key]);
                    $fileName = $value['name_saved'];
                }
            }
            $atta = base64_encode(serialize($atta));
            $activity = CurActivityInfo::model()->findByPk($aid);
            $activity->attachments = $atta;
            if ($activity->save()) {
                $data['msg'] = 'success';
                unlink(Yii::app()->params['OAUploadBasePath'].'/curriculum/'.$fileName);
                unlink(Yii::app()->params['OAUploadBasePath'].'/curriculum/thumbs/'.$fileName);
            }else{
                $data['msg'] = 'error';
                $data['info'] = $activity->getErrors();
            }
        }else{
            $data['msg'] = 'success';
            // unlink(Yii::app()->params['OAUploadBasePath'].'/curriculum/temps/'.$fileName);
            // unlink(Yii::app()->params['OAUploadBasePath'].'/curriculum/temps/thumbs/'.$fileName);
        }

        echo json_encode($data);
    }

    /**
     * 添加相关活动
     */
    public function actionSimilarActivity()
    {
        Yii::import('common.models.portfolio.*');

        $data = array();
        if ($_POST['type'] == 'search') {
            $pid = Yii::app()->request->getParam('pid','');
            $criteria = new CDbCriteria;
            $criteria->condition .= "FIND_IN_SET('$pid',t.pids) ";
            $activity = CurActivityInfo::model()->findAll($criteria);

            if ($activity) {
                foreach ($activity as $key => $value) {
                    $data['activity'][$value->aid] = $value->cn_title.' '.$value->en_title;
                }
                $data['msg'] = 'success';
            }else{
                $data['msg'] = 'error';
            }
        }
        if ($_POST['type'] == 'save') {
            $sc_id = $_POST['sc_id'];
            $bc_id = $_POST['bc_id'];
            $slModel = new CurSimilarLink();

            $criteria = new CDbCriteria;
            $criteria->condition = "bc_id = $bc_id AND sc_id = $sc_id";
            if($bc_id == $sc_id){
                $data['msg'] = 'error';
                $data['info'] = '不能添加自己';
            }else{
                $data['msg'] = 'success';
                $data['info'] = '添加相关活动成功';
            }
        }
        echo json_encode($data);
    }

    /**
     * 删除相关活动
     */
    public function actionDeleteSimilarActivity()
    {
        Yii::import('common.models.portfolio.*');

        $bc_id = $_POST['bc_id'];
        $sc_id = $_POST['sc_id'];

        $criteria = new CDbCriteria;
        $criteria->condition = "bc_id = $bc_id AND sc_id = $sc_id";
        if(CurSimilarLink::model()->deleteAll($criteria)){
            $data['msg']='success';
        }
        echo json_encode($data);
    }

    /**
     * 新建/编辑活动
     */
    public function actionEditActivity($aid=0)
    {
        Yii::import('common.models.portfolio.*');
        Yii::import('common.models.Term');
        
        // 查找标签
        $criteria = new CDbCriteria;
        $criteria->condition = "tag_catid = 2 AND tag_itemid = $aid ";
        $tag = tagLink::model()->findAll($criteria);

        $termModel = new Term;
        $diglossia = array();
        $diglossia['age'] = $termModel->getAgeList();
        $diglossia['intelligence'] = $termModel->getMiList();
        $diglossia['learning'] = $termModel->getLdList();
        $diglossia['cultural'] = $termModel->getCulturalList();
        $diglossia['season'] = array(
            50=>Yii::t('curriculum','Any Season'),
            10=>Yii::t('curriculum','Spring'),
            20=>Yii::t('curriculum','Summer'),
            30=>Yii::t('curriculum','Fall'),
            40=>Yii::t('curriculum','Winter'),
            );
        $diglossia['official'] = array(
            1=>Yii::t('curriculum','Education Team'),
            0=>Yii::t('curriculum','Other Team'),
            3=>Yii::t('curriculum','Any Team'),
            );
        $diglossia['room'] = array(
            40=>Yii::t('curriculum','Suggested Place'),
            30=>Yii::t('curriculum','Any Place'),
            10=>Yii::t('curriculum','Indoor'),
            20=>Yii::t('curriculum','Outdoor'),
            );
        $diglossia['state'] = array(
            10=>Yii::t('curriculum','Normal'),
            20=>Yii::t('curriculum','Disabled'),
            );

        $projects = array();
        $criteria = new CDbCriteria;
        $criteria->condition = "appeared = 0";
        $criteria->order = "pid desc";
        $projectsObj = CurProjects::model()->findAll($criteria);
        foreach ($projectsObj as $key => $value) {
            $projects[$value->pid] = $value->cn_title.' '.$value->en_title;
           
        }
        $projectPid = CurActivityInfo::model()->findAll(array('select'=>'pid'));
        $projectTitle = CurActivityInfo::model()->findAll(array('select'=>'cn_title'));
              
        $activity = CurActivityInfo::model()->findByPk($aid);
        if($activity == null){
            $activity = new CurActivityInfo();
        }else{
            //判断是否具有编辑权限
            if (Yii::app()->user->checkAccess('o_E_Edu_Common') || $this->staff->uid == $activity->userid){
                $activity->age = explode(',',$activity->age);
                $activity->cultural = explode(',',$activity->cultural);
                $activity->pids = explode(',',$activity->pids);
                $activity->intelligence = explode(',',$activity->intelligence);
                $activity->learning = explode(',',$activity->learning);
                // 查找相关活动
                $sc_idObj = CurSimilarLink::model()->findAll('bc_id=:bc_id',array(':bc_id'=>$aid));
                $sc_id = array();
                foreach ($sc_idObj as $key => $value) {
                    $sc_id[] = $value->sc_id;
                }
                $similarActivity = CurActivityInfo::model()->findAllByPk($sc_id);
            }else{
                $this->redirect(array('activity'));
                die();
            }
        }
        //处理提交的表单
        if(isset($_POST['CurActivityInfo'])){
            $activity->attributes = $_POST['CurActivityInfo'];
            $sc_id = array();
            $sc_id = Yii::app()->request->getParam('sc_id');
            $activity->userid = $this->staff->uid;
            $activity->addtime = time();
            $activity->update_timestamp = time();
            $atta = Yii::app()->request->getParam('atta');
            if (!empty($atta)) {
                $attachments = array();
                $fileName = array();    #需要移动的附件列表
                foreach ($atta as $key => $value) {
                    $attachments += unserialize($value);
                }
                foreach ($attachments as $key => $value) {
                    $fileName[] = $value['name_saved'];
                }
                $attachments = base64_encode(serialize($attachments));
                $activity->attachments = $attachments;
            }
            $activity->age = implode(',',$_POST['CurActivityInfo']['age']);
            $activity->pids = implode(',',$_POST['CurActivityInfo']['pids']);
            $activity->intelligence = implode(',',$_POST['CurActivityInfo']['intelligence']);
            $activity->learning = implode(',',$_POST['CurActivityInfo']['learning']);
            $activity->cultural  = implode(',',$_POST['CurActivityInfo']['cultural']);
            if($activity->save()){
                $aid = $activity->aid;
                //移动临时目录的附件
                if (!empty($fileName)) {
                    $filePath = Yii::app()->params['OAUploadBasePath'] . '/curriculum/';
                    $tempFilePath = Yii::app()->params['OAUploadBasePath'] . '/curriculum/temps/';
                    foreach ($fileName as $key => $value) {
                        rename($tempFilePath . $value, $filePath . $value);
                        rename($tempFilePath .'thumbs/'. $value, $filePath .'thumbs/'. $value);
                    }
                }
                //删除旧的相关活动
                $criteria = new CDbCriteria;
                $criteria->compare('bc_id',array($aid));
                CurSimilarLink::model()->deleteAll($criteria);
                //存储新的相关活动
                foreach ($sc_id as $key => $value) {
                    $similarActivity = new CurSimilarLink();
                    $similarActivity->sc_id = $value;
                    $similarActivity->bc_id = $aid;
                    $similarActivity->save();
                }
                //删除旧标签
                $criteria = new CDbCriteria;
                $criteria->compare('tag_catid',2);
                $criteria->compare('tag_itemid',$aid);
                $oldTagLink = tagLink::model()->findAll($criteria);
                foreach ($oldTagLink as $key => $value) {
                    $value->tag->tag_count--;
                    $value->tag->save();
                }
                tagLink::model()->deleteAll($criteria);
                //存储新标签
                $tag = array_unique($_POST['tag']);
                if (!empty($tag)) {
                    foreach ($tag as $value) {
                        $tagObj = tagTag::model()->find('tag_term=:tag_term',array(':tag_term'=>$value));
                        $tagLinkObj = new tagLink;
                        if (!empty($tagObj)) {
                            $tagObj->tag_count++;
                            if ($tagObj->save()) {
                                $tagLinkObj->tag_id = $tagObj->tag_id;  
                                $tagLinkObj->tag_catid = 2;  
                                $tagLinkObj->tag_modid = 73;  
                                $tagLinkObj->tag_itemid = $aid;  
                                $tagLinkObj->tag_time = time(); 
                                $tagLinkObj->save(); 
                            }
                        }else{
                            $tagObj = new tagTag;
                            $tagObj->tag_term = $value;
                            $tagObj->tag_count = 1;
                            if($tagObj->save()){
                                $tagLinkObj->tag_id = $tagObj->tag_id;  
                                $tagLinkObj->tag_catid = 2; 
                                $tagLinkObj->tag_modid = 73;  
                                $tagLinkObj->tag_itemid = $aid;  
                                $tagLinkObj->tag_time = time();  
                                $tagLinkObj->save();
                            }
                        }
                    }
                }
                $this->addMessage('state', 'success');
                $this->addMessage('refresh', true);
                $this->addMessage('referer', $this->createUrl('showactivity', array('aid'=>$activity->aid)));
                $this->addMessage('message', Yii::t('message', 'Data Saved!'));
            }
            else{
                $this->addMessage('state', 'fail');
                $err = current($activity->getErrors());
                $this->addMessage('message', $err[0]);
            }
            $this->showMessage();
        }
        $this->render('editactivity',array(
            'activity'=>$activity,
            'similarActivity'=>$similarActivity,
            'projects'=>$projects,
            'diglossia'=>$diglossia,
            'tag'=>$tag,
        ));
    }

    /**
     * 删除活动
     */
    public function actionDeleteActivity()
    {
        Yii::import('common.models.portfolio.*');
        
        $aid = Yii::app()->request->getParam('aid',0);
        $activity = CurActivityInfo::model()->findByPk($aid);
        $atta = unserialize(base64_decode($activity->attachments));
        //判断是否有权限删除
        if (Yii::app()->user->checkAccess('o_E_Edu_Common') || $this->staff->uid == $activity->userid) {
            if(CurActivityInfo::model()->deleteByPk($aid)){
                //删除关联的标签
                $criteria = new CDbCriteria;
                $criteria->compare('tag_catid',2);
                $criteria->compare('tag_itemid',$aid);
                tagLink::model()->deleteAll($criteria);
                //删除关联的附件
                if (!empty($atta)) {
                    $filePath = Yii::app()->params['OAUploadBasePath'] . '/curriculum/';
                    foreach ($atta as $value) {
                        unlink($filePath . $value['name_saved']);
                        unlink($filePath .'thumbs/'. $value['name_saved']);
                    }
                }

                $msg = 'success';
            }else{
                $msg = '删除活动失败';
            }
        }else{
            $msg = '没有权限';
        }

        $data = array('msg'=>$msg,'aid'=>$aid,);
        echo json_encode($data);
    }

    /**
     * 复制活动
     */
    public function actionCopyFavorites()
    {
        Yii::import('common.models.portfolio.*');

        $userid = $this->staff->uid;
        $aid = Yii::app()->request->getParam('aid');
        $pid = Yii::app()->request->getParam('pid');
        $activity = CurActivityInfo::model()->findByPk($aid);
        if ($activity->userid == $userid) {
            $msg = 'error';
            $info = Yii::t('curriculum','Can Not Copy Yourself Activity');
            $data = array('msg'=>$msg,'info'=>$info);
            echo json_encode($data);
            return;
        }
        $activity->isNewRecord = true;
        unset($activity->aid);
        $activity->copyfrom = $activity->userid;
        $activity->userid = $userid;
        $activity->state = 20;
        $activity->pids = $pid;
        $activity->update_timestamp = time();
        $activity->addtime = time();
        if($activity->save()){
            $msg = 'success';
            $info = Yii::t('curriculum','Copy Success');
        }else{
            $msg = 'error';
            $info = Yii::t('curriculum','Copy Fail');
        }
        $data = array('msg'=>$msg,'info'=>$info);
        echo json_encode($data);
    }

    /**
     * 活动状态控制
     */
    public function actionState()
    {
        Yii::import('common.models.portfolio.*');
        
        $aid = $_POST['aid'];
        $activity = CurActivityInfo::model()->findByPk($aid);
        if ($activity->state == 10) {
            $activity->state = 20;
            $btnClass = 'btn btn-danger btn-xs';
            $btnValue = Yii::t('curriculum', 'Disabled');
        }else{
            $activity->state = 10;
            $btnClass = 'btn btn-primary btn-xs';
            $btnValue = Yii::t('curriculum', 'Normal');
        }
        if ($activity->save()) {  
            $msg = 'success';
        }else{
            $msg = $activity->getErrors();
        }
        $data = array('msg'=>$msg,'btnclass'=>$btnClass,'btnvalue'=>$btnValue,'state'=>$activity->state,);
        echo json_encode($data);
    }

    /**
     * 所有标签
     */
    public function actionTags()
    {
        Yii::import('common.models.portfolio.*');

        $tag_catid = Yii::app()->request->getParam('tag_catid',0);
        $tag_id = Yii::app()->request->getParam('tag_id',0);

        
        $criteria = new CDbCriteria;
        $criteria->compare('tag_catid',$tag_catid);
        $criteria->compare('tag_id',$tag_id);
        $tagObj = tagLink::model()->findAll($criteria);
   
        $sql = "select *,count(*) num from ivy_tag_link group by tag_id,tag_catid order by tag_catid";
        $tags = tagLink::model()->findAllBySql($sql);
        $this->render('tags',array(
            'tags'=>$tags,
            'tag_id'=>$tag_id,
            'tag_catid'=>$tag_catid,
            'tagObj'=>$tagObj,
            ));
    }

    /**
     * 项目操作按钮
     */
    public function getProButton($data)
    {
        //查看按钮
        echo CHtml::link(Yii::t('curriculum', 'View'), array('showproject', 'pid'=>$data->pid), array('class'=>'btn btn-primary btn-xs')).' ';

        //收藏按钮
        $favorites = CurFavorites::model()->findAll('userid = :userid',array(':userid'=>$this->staff->uid));
        foreach ($favorites as $value) {
            if ($data->pid == $value->pid) {
                $flag = 1;
            }
        }
        if (isset($flag)) {
            echo '<button class="btn btn-primary btn-xs" onclick="collect(this, '.$data->pid.')">'.Yii::t('curriculum', 'Collected').'</button> ';
        }else{
            echo '<button class="btn btn-primary btn-xs" onclick="collect(this, '.$data->pid.')">'.Yii::t('curriculum', 'Collect').'</button> ';
        }
        //是否为教育部门
        if (Yii::app()->user->checkAccess('o_E_Edu_Common')) {
            //编辑按钮
            echo CHtml::link(Yii::t('curriculum', 'Edit'), array('editproject', 'pid'=>$data->pid), array('class'=>'btn btn-primary btn-xs')).' ';
            //删除按钮
            echo '<button class="btn btn-danger btn-xs" onclick="deletePro(this, '.$data->pid.')">'.Yii::t('curriculum', 'Delete').'</button>';
        }
    }

    /**
     * 活动操作按钮
     */
    public function getActButton($data)
    {
        // 查看按钮
        echo CHtml::link(Yii::t('curriculum', 'View'), array('showactivity', 'aid'=>$data->aid), array('class'=>'btn btn-primary btn-xs')).' ';


        // 判断是否为教育部门
        if (Yii::app()->user->checkAccess('o_E_Edu_Common')) {
            //复制项目
            if ($this->getAction()->getId() == 'activity') {
                echo '<button class="btn btn-primary btn-xs" data-toggle="modal" data-target="#myModal" onclick="copyaid(this,'.$data->aid.')">'.Yii::t('curriculum', 'Copy').'</button> ';
            }
            // 编辑按钮
            echo CHtml::link(Yii::t('curriculum', 'Edit'), array('editactivity', 'aid'=>$data->aid), array('class'=>'btn btn-primary btn-xs')).' ';
            // 删除按钮
            echo '<button class="btn btn-danger btn-xs" onclick="deleteAct(this,'.$data->aid.')">'.Yii::t('curriculum', 'Delete').'</button> ';
            //状态控制
            $btnClass = $data->state == 10 ?'btn-primary':'btn-danger';
            $btnValue = $data->state == 10 ?Yii::t('curriculum', 'Normal'):Yii::t('curriculum', 'Disabled');
            echo '<button class="btn '.$btnClass.' btn-xs" onclick="toggle(this,'.$data->aid.')">'.$btnValue.'</button>';
        }
    }

    /**
     * 获取发布人
     */
    public function getUserName($data)
    {
        if ($data->official == 1) {
            return Yii::t('curriculum','Education Team');
        }
        return $data->user->getName();
    }
}