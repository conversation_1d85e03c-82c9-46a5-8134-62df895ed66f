var cb = {
	flag: false,
	data: {},
	message: null,
	// 刷新当前页面
	reload: function() {
		location.reload();
	},

	// 在当前窗口打开新页面
	openUrl: function() {
		location.href = cb.data.url;
	},
    
	info: function() {
		alert(cb.message);
		location.href = cb.data.url;
	},
	
	msgreload: function() {
		alert(cb.message);
		location.reload();
	}
};

function callback(param1,param2){
	if(param2 == "info"){
		alert(param1.ret);
	}
	if(param2 == "reload"){
		location.reload();
	}
	if(param2 == "msgreload"){
		alert(param1.ret);
		location.reload();
	}
	
}