<?php

/**
 * This is the model class for table "ivy_secondary_report".
 *
 * The followings are the available columns in table 'ivy_secondary_report':
 * @property integer $id
 * @property string $school
 * @property string $report_title_cn
 * @property string $report_title_en
 * @property string $stat
 * @property integer $time
 * @property integer $change_time
 * @property integer $uid
 */
class SecondaryReport extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_secondary_report';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('report_title_cn, report_title_en', 'required'),
			array('time, change_time, uid', 'numerical', 'integerOnly'=>true),
			array('school, report_title_cn, report_title_en, stat', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, school, report_title_cn, report_title_en, stat, time, change_time, uid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'position' => array(self::HAS_MANY, 'AchievementReportCourse', array('td' => "id")),
			'grade' => array(self::HAS_MANY, 'SecondaryReportGrade', array('template_id' => "id")),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'school' => 'School',
			'report_title_cn' => Yii::t('curriculum','Cn Title'),
			'report_title_en' => Yii::t('curriculum','En Title'),
			'stat' => Yii::t('curriculum','State'),
			'time' => 'Time',
			'change_time' => 'Change Time',
			'uid' => 'Uid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('school',$this->school,true);
		$criteria->compare('report_title_cn',$this->report_title_cn,true);
		$criteria->compare('report_title_en',$this->report_title_en,true);
		$criteria->compare('stat',$this->stat,true);
		$criteria->compare('time',$this->time);
		$criteria->compare('change_time',$this->change_time);
		$criteria->compare('uid',$this->uid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return SecondaryReport the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	public function getName()
	{
		switch (Yii::app()->language) {
			case "zh_cn":
				return  $this->report_title_cn ;
				break;
			case "en_us":
				return  $this->report_title_en;
				break;
		}
	}
}
