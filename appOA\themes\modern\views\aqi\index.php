<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="Ivy AQI">
    <meta name="author" content="The Ivy Group">
    <link rel="icon" href="http://oa.ivyonline.cn/themes/ivy/favicon.ico">

    <title>Ivy AQI</title>

    <link href="http://osspub.ivykids.cn/webmail/bootstrap/3.2.0/css/bootstrap.min.css" rel="stylesheet">
    <script type="text/javascript" src="http://osspub.ivykids.cn/webmail/js/jquery/1.9.0/jquery.min.js"></script>
    <style>
        .data{position: relative;}
        .data span{position: absolute; top: 0; right:3px;}
    </style>
</head>
<body>
<div class="container-fluid">
    <h3 class="text-center">Ivy PM2.5 Air Quality Live Data</h3>
    <div class="row">
        <?php foreach($branchLasereggs as $bid=>$lasereggs):?>
            <div class="col-md-2">
                <div class="list-group">
                    <a href="javascript:;" class="list-group-item disabled">
                        <?php
                        echo isset($branch[$bid]) ? $branch[$bid] : 'DS';
                        ?>
                    </a>
                    <?php
                    foreach($lasereggs as $mid=>$laseregg):
                        $n = isset($aqi[$mid]['num']) ? $aqi[$mid]['num'] : '-';
                        $t = isset($aqi[$mid]['time']) ? $aqi[$mid]['time'] : '';
                        if($n<51){
                            $css = '';
                        }
                        elseif($n<101){
                            $css = 'style="background-color: #f2dede;"';
                        }
                        elseif($n<151){
                            $css = 'style="background-color: #f2dede;"';
                        }
                        else{
                            $css = 'style="background-color: #f2dede;"';
                        }
                        ?>
                        <a href="javascript:;" onclick="showAQI('<?php echo $mid;?>')" class="list-group-item text-center data" <?php echo $css?>>
                            <h4><strong><?php echo $n?></strong></h4>
                            <span class="text-muted"><?php echo $laseregg?></span>
                            <span class="text-muted" style="bottom: 0;top: auto;"><?php echo $t?></span>
                        </a>
                    <?php endforeach;?>
                </div>
            </div>
        <?php endforeach;?>
    </div>
    <hr>
    <div class="row">
        <?php foreach($citys as $cid=>$city):?>
            <div class="col-md-2">
                <div class="input-group input-group-lg">
                    <span class="input-group-addon"><?php echo $city;?></span>
						<span class="input-group-btn">
							<button class="btn btn-default" type="button" id="cid_<?php echo $cid;?>"><strong>0</strong></button>
						</span>
                </div>
            </div>
        <?php endforeach;?>
        <div class="col-md-2"></div>
    </div>
</div>
<div id="aqiModal" class="modal fade">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-body">
                <p id="spline_aqi"></p>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
</body>
<script type="text/javascript" src="http://osspub.ivykids.cn/webmail/bootstrap/3.2.0/js/bootstrap.min.js"></script>
<script type="text/javascript" src="http://osspub.ivykids.cn/webmail/highcharts/4.0.1/highcharts.js"></script>
<script>
    var getcity = function(){
        $.get('<?php echo $this->createUrl('cityAqi');?>', {}, function(data){
            for(var cid in data){
                $('#cid_'+cid+' strong').text( data[cid]['pm2_5'] );
            }
        }, 'json');
    }
    getcity();

    var n, d, s = true;
    Highcharts.setOptions({
        global: {
            useUTC: false
        }
    });
    function showAQI(laseregg)
    {
        if(s){
            n=$('#data_'+laseregg+' span').text();
            d = laseregg;
            $('#aqiModal').modal('show');
        }
    }

    $('#aqiModal').on('shown.bs.modal', function (e) {
        s=false;

        var preData;
        $.ajax({
            url: '<?php echo $this->createUrl('aqi');?>',
            dataType: 'json',
            async: false,
            data: {laseregg: d}
        }).done(function(data){
            preData = data;
        });

        $('#spline_aqi').highcharts({
            chart: {
                type: 'area',
                animation: Highcharts.svg,
                marginRight: 10,
            },
            title: {
                text: n
            },
            xAxis: {
                type: 'datetime',
                tickPixelInterval: 150
            },
            yAxis: {
                title: {
                    text: 'AQI CN'
                },
                min: 0,
                max: 500,
                plotLines: [{
                    value: 0,
                    width: 1,
                    color: '#808080'
                }]
            },
            legend: {
                enabled: false
            },
            exporting: {
                enabled: false
            },
            credits: {
                enabled: false
            },
            plotOptions: {
                area: {
                    marker: {
                        enabled: false,
                        symbol: 'circle',
                        radius: 2,
                        states: {
                            hover: {
                                enabled: true
                            }
                        }
                    }
                }
            },
            series: [{
                name: 'AQI',
                data: preData
            }]
        });
    })

    $('#aqiModal').on('hidden.bs.modal', function (e) {
        s=true;
        $('#spline_aqi').highcharts().destroy();
    })

    setInterval(function(){location.reload()}, 3600000);
</script>
</html>