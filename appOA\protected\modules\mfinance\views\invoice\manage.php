<?php
$invoiceLabels = Invoice::model()->attributeLabels();
$childProfileLabels = ChildProfileBasic::model()->attributeLabels();
$refundLabels = InvoiceChildRefund::model()->attributeLabels();
?>
<div class="container-fluid">
    <div class="row">
        <div class="col-md-5">
            <div class="panel panel-primary">
                <!-- Default panel contents -->
                <div class="panel-heading"><h5><strong><?php echo Yii::t('billing','Invoice Information');?></strong></h5></div>
                <div class="panel-body" style="position: relative;z-index: 1">
                    <div class="invoice-child-photo">
                        <?php
                        echo CHtml::image(CommonUtils::childPhotoUrl($invoice->childprofile->photo),'',array('class'=>'img-circle'));
                        ?>
                    </div>
                    <div class="invoice-info">
                        <dl class="dl-horizontal">
                            <dt><?php echo Yii::t('child','Name');?></dt>
                            <dd><?php echo $invoice->childprofile->getChildName();?></dd>
                        </dl>
                        <dl class="dl-horizontal">
                            <dt><?php echo $childProfileLabels['birthday_search'];?></dt>
                            <dd><?php echo $invoice->childprofile->birthday_search;?></dd>
                        </dl>
                        <dl class="dl-horizontal">
                            <dt><?php echo $invoiceLabels['title'];?></dt>
                            <dd><?php echo $invoice->title;?></dd>
                        </dl>
                        <?php if($invoice->classid):?>
                        <dl class="dl-horizontal">
                            <dt><?php echo $invoiceLabels['classid'];?></dt>
                            <dd><?php $ab = $this->getAllBranch(); echo $ab[$invoice->schoolid]['title'];?>/<?php echo $invoice->classInfo->title;?></dd>
                        </dl>
                        <?php endif;?>
                        <dl class="dl-horizontal">
                            <dt><?php echo $invoiceLabels['calendar_id'];?></dt>
                            <dd>
                                <?php echo $invoice->calendar->title;?>
                                <?php echo Yii::t("invoice", " (:startY - :endY)", array(":startY"=>$invoice->calendar->startyear, ":endY"=>$invoice->calendar->startyear + 1))?>
                            </dd>
                        </dl>
                        <dl class="dl-horizontal">
                            <dt><?php echo $invoiceLabels['payment_type'];?></dt>
                            <dd><?php echo $policyApi->renderFeeType($invoice->payment_type);?></dd>
                        </dl>
                        <dl class="dl-horizontal">
                            <dt><?php echo Yii::t("invoice","Invoice Peroid");?></dt>
                            <dd><?php echo ($invoice->startdate * $invoice->enddate) ?
					$invoice->startdate . ' ~ '. $invoice->enddate : 'N/A';?></dd>
                        </dl>
                        <dl class="dl-horizontal">
                            <dt><?php echo $invoiceLabels['duetime'];?></dt>
                            <dd><?php echo ($invoice->duetime) ? OA::formatDateTime($invoice->duetime) : 'N/A';?></dd>
                        </dl>
                        <dl class="dl-horizontal">
                            <dt><?php echo $invoiceLabels['status'];?></dt>
                            <dd><?php echo $policyApi->renderInvoiceStatus($invoice->status);?></dd>
                        </dl>
                        <dl class="dl-horizontal">
                            <dt><?php echo $invoiceLabels['amount'];?></dt>
                            <dd><?php echo OA::formatMoney($invoice->amount);?></dd>
                        </dl>
                        <dl class="dl-horizontal">
                            <dt><?php echo $invoiceLabels['memo'];?></dt>
                            <dd><?php echo Yii::app()->format->ntext($invoice->memo);?></dd>
                        </dl>
                    </div>

                </div>

                <!-- Table -->
                <table class="table">
                    <tbody>
                    <?php if(count($invoice->invoiceTransaction)):?>
                    <tr>
                        <th colspan="6" class="active">付款记录</th>
                    </tr>
                    <tr>
                        <th><?php echo Yii::t("invoice","发票号");?></th>
                        <th><?php echo Yii::t("invoice","付款金额");?></th>
                        <th><?php echo Yii::t("invoice","付款日期");?></th>
                        <th><?php echo Yii::t("invoice","付款方式");?></th>
                        <th><?php echo Yii::t("invoice","操作人");?></th>
                        <th><?php echo Yii::t("invoice","发票抬头");?></th>
                    </tr>
                    <?php foreach ($invoice->invoiceTransaction as $trans):?>
                    <tr>
                        <td><?php echo $trans->invoice_number;?></td>
                        <td><?php echo $trans->amount;?></td>
                        <td><?php echo OA::formatDateTime($trans->timestampe, 'medium', 'medium');?></td>
                        <td><?php echo $policyApi->renderPaymentType($trans->transactiontype);?></td>
                        <td><?php echo $userNames[$trans->operator_uid];?></td>
                        <td><?php echo $trans->invoice_title;?></td>
                    </tr>
                    <?php endforeach;?>
                    <?php endif;?>
                    <?php if(count($invoice->workflowRefund)):?>
                    <tr>
                        <th colspan="6" class="active">退费记录</th>
                    </tr>
                    <tr>
                        <th><?php echo $refundLabels['title'];?></th>
                        <th><?php echo $refundLabels['amount'];?></th>
                        <th><?php echo $refundLabels['peroid'];?></th>
                        <th><?php echo $refundLabels['status'];?></th>
                        <th><?php echo $refundLabels['timestamp'];?></th>
                        <th><?php echo $refundLabels['memo'];?></th>
                    </tr>
                    <?php foreach ($invoice->workflowRefund as $refund):?>
                    <tr>
                        <td><?php echo $refund->title;?></td>
                        <td><?php echo ($refund->newamount > 0) ? $refund->newamount : $refund->amount;?></td>
                        <td><?php echo ($refund->startdate * $refund->enddate) ?
                            OA::formatDateTime($refund->startdate) . ' ~ '. OA::formatDateTime($refund->enddate) : 'N/A';?></td>
                        <td><span class="org"><?php echo $policyApi->renderRefundStatus($refund->status);?></span></td>
                        <td><?php echo OA::formatDateTime($refund->timestamp);?></td>
                        <td><?php echo $refund->memo;?></td>
                    </tr>
                    <?php endforeach;?>
                    <?php endif;?>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="col-md-7">
            <div class="actions">
<!--                <a class="btn btn-primary btn-lg" href="javascript:void(0);" onclick="editInvoice();">-->
<!--                    <span class="glyphicon glyphicon-pencil"></span> --><?php //echo Yii::t('global','Edit');?>
<!--                </a>-->
                <?php if($invoice->status == Invoice::STATS_UNPAID):?>
<!--                    <a class="btn btn-info btn-lg"-->
<!--                       href="--><?php //echo $this->createUrl('//child/index/unpaid',
//                            array('t'=>'cash','childid'=>$invoice->childid));?><!--" target="_blank">-->
<!--                        <span class="glyphicon glyphicon-star"></span> --><?php //echo Yii::t('payment','Make Payment');?>
<!--                    </a>-->
<!--                    <a class="btn btn-danger btn-lg J_ajax_del" href="--><?php //echo $this->createUrl('//mfinance/invoice/invalidInvoice',array('invoiceId'=>$invoice->invoice_id));?><!--">-->
<!--                        <span class="glyphicon glyphicon-trash"></span> --><?php //echo Yii::t('global','Delete');?>
<!--                    </a>-->
                <?php elseif($invoice->status == Invoice::STATS_PAID):?>
<!--                    <a class="btn btn-warning btn-lg" href="javascript:void(0);" onclick="refundInvoice();">-->
<!--                        <span class="glyphicon glyphicon-share"></span> --><?php //echo Yii::t('payment','Make Refund');?>
<!--                    </a>-->
                <?php elseif($invoice->status == Invoice::STATS_PARTIALLY_PAID):?>
<!--                    <a class="btn btn-info btn-lg"-->
<!--                       href="--><?php //echo $this->createUrl('//child/index/unpaid',
//                            array('t'=>'cash','childid'=>$invoice->childid));?><!--" target="_blank">-->
<!--                        <span class="glyphicon glyphicon-star"></span> --><?php //echo Yii::t('payment','Make Payment');?>
<!--                    </a>-->
                <?php endif;?>
            </div>
            <div id="show-template"></div>
        </div>
    </div>
</div>

<style>
    .invoice-child-photo{
        position: absolute;
        right: 10px;
        top: 10px;
        z-index: 0;
    }
    .invoice-info{
        z-index: 1;
    }
</style>

<!--template-->
<script type="text/template" id="edit-invoice">
<?php echo CHtml::form($this->createUrl('//mfinance/invoice/saveEdit'), 'post', array('class' => 'form-horizontal J_ajaxForm', 'role' => 'form', 'id'=>'edit-invoice-form')); ?>
<div class="panel panel-default mt20">
    <div class="panel-heading"><?php echo Yii::t('payment', '编辑账单'); ?></div>
    <div class="panel-body">
        <%_.each(editItem[invoice.status],function(val,key){
                    var field = invoiceLabels[val];
                    var value = invoice[val];
        %>
        <div class="form-group">
            <?php echo CHtml::label('<%=field%>', null, array('class' => 'col-sm-2 control-label','encode'=>false)); ?>
            <div class="col-sm-10">
            <% if(val == 'memo'){%>
                <?php echo CHtml::textArea('Invoice[<%=val%>]', '<%=value%>', array('class' => 'form-control length_5', 'placeholder'=>'<%=field%>','encode'=>false)); ?>
            <% }else{ %>
                <?php echo CHtml::textField('Invoice[<%=val%>]', '<%=value%>', array('class' => 'form-control length_5', 'placeholder'=>'<%=field%>','encode'=>false)); ?>
            <%}%>
            </div>
        </div>
        <%})%>
    </div>
    <div class="panel-footer">
        <?php echo CHtml::hiddenField('invoiceId', '<%=invoice.invoice_id%>',array('encode'=>false));?>
        <button type="button" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Save');?></button>
        <button type="reset" class="btn btn-default"><?php echo Yii::t('global', 'Reset');?></button>
    </div>
</div>
<?php echo CHtml::endForm(); ?>
</script>

<script type="text/template" id="refund-invoice">
<?php echo CHtml::form($this->createUrl('//mfinance/invoice/refundInvoice'), 'post', array('class' => 'form-horizontal J_ajaxForm', 'role' => 'form', 'id'=>'refund-invoice-form')); ?>
<div class="panel panel-default mt20">
    <div class="panel-heading"><?php echo Yii::t('payment', '账单退费'); ?></div>
    <div class="panel-body">
        <div class="form-group" model-attribute="title">
            <?php echo CHtml::label($refundLabels['title'], null, array('class' => 'col-sm-2 control-label','encode'=>false)); ?>
            <div class="col-sm-10">
                <?php echo CHtml::textField('InvoiceChildRefund[title]', '', array('class' => 'form-control length_5', 'placeholder'=>$refundLabels['title'],'encode'=>false)); ?>
            </div>
        </div>
        <div class="form-group" model-attribute="amount">
            <?php echo CHtml::label($refundLabels['amount'], null, array('class' => 'col-sm-2 control-label','encode'=>false)); ?>
            <div class="col-sm-10">
                <?php echo CHtml::textField('InvoiceChildRefund[amount]', '', array('class' => 'form-control length_5', 'placeholder'=>$refundLabels['amount'],'encode'=>false)); ?>
            </div>
        </div>
        <div class="form-group" model-attribute='startdate'>
            <?php echo CHtml::label($refundLabels['startdate'], null, array('class' => 'col-sm-2 control-label','encode'=>false)); ?>
            <div class="col-sm-10">
                <?php echo CHtml::textField('InvoiceChildRefund[startdate]', '', array('class' => 'form-control length_5', 'placeholder'=>$refundLabels['startdate'],'encode'=>false)); ?>
            </div>
        </div>
        <div class="form-group" model-attribute='enddate'>
            <?php echo CHtml::label($refundLabels['enddate'], null, array('class' => 'col-sm-2 control-label','encode'=>false)); ?>
            <div class="col-sm-10">
                <?php echo CHtml::textField('InvoiceChildRefund[enddate]', '', array('class' => 'form-control length_5', 'placeholder'=>$refundLabels['enddate'],'encode'=>false)); ?>
            </div>
        </div>
        <div class="form-group" model-attribute='timestamp'>
            <?php echo CHtml::label($refundLabels['timestamp'], null, array('class' => 'col-sm-2 control-label','encode'=>false)); ?>
            <div class="col-sm-10">
                <?php echo CHtml::textField('InvoiceChildRefund[timestamp]', '', array('class' => 'form-control length_5', 'placeholder'=>$refundLabels['timestamp'],'encode'=>false)); ?>
            </div>
        </div>
        <div class="form-group" model-attribute='memo'>
            <?php echo CHtml::label($refundLabels['memo'], null, array('class' => 'col-sm-2 control-label','encode'=>false)); ?>
            <div class="col-sm-10">
                <?php echo CHtml::textArea('InvoiceChildRefund[memo]', '', array('class' => 'form-control length_5', 'placeholder'=>$refundLabels['memo'],'encode'=>false)); ?>
            </div>
        </div>
    </div>
    <div class="panel-footer">
        <?php echo CHtml::hiddenField('invoiceId', '<%=invoice.invoice_id%>',array('encode'=>false));?>
        <button type="button" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Save');?></button>
        <button type="reset" class="btn btn-default"><?php echo Yii::t('global', 'Reset');?></button>
    </div>
</div>
<?php echo CHtml::endForm(); ?>
</script>

<script>
    var invoice = <?php echo CJSON::encode($invoice);?>;
    var editItem = <?php echo CJSON::encode($this->invoiceEdit);?>;
    var invoiceLabels = <?php echo CJSON::encode($invoiceLabels);?>;
    var editInvoiceTemplate = _.template($("#edit-invoice").html());
    var refundInvoiceTemplate = _.template($("#refund-invoice").html());
    //编辑账单
    editInvoice = function(){
        var showTemplate = $("#show-template");
        showTemplate.empty();
        showTemplate.append(editInvoiceTemplate({invoice:invoice,editItem:editItem}));
        if ($("#Invoice_startdate")){
            $('#edit-invoice-form #Invoice_startdate').datepicker({'changeMonth':true,'changeYear':true,'dateFormat':'yy-mm-dd'});
        }
        if ($("#Invoice_enddate")){
            $('#edit-invoice-form #Invoice_enddate').datepicker({'changeMonth':true,'changeYear':true,'dateFormat':'yy-mm-dd'});
        }
        if ($("#Invoice_duetime")){
            $('#edit-invoice-form #Invoice_duetime').datepicker({'changeMonth':true,'changeYear':true,'dateFormat':'yy-mm-dd'});
        }
        head.Util.ajaxForm();
    }

    //退费
    refundInvoice = function(){
        var showTemplate = $("#show-template");
        showTemplate.empty();
        showTemplate.append(refundInvoiceTemplate({invoice:invoice}));
        $('#refund-invoice-form #InvoiceChildRefund_startdate').datepicker({'changeMonth':true,'changeYear':true,'dateFormat':'yy-mm-dd'});
        $('#refund-invoice-form #InvoiceChildRefund_enddate').datepicker({'changeMonth':true,'changeYear':true,'dateFormat':'yy-mm-dd'});
        $('#refund-invoice-form #InvoiceChildRefund_timestamp').datepicker({'changeMonth':true,'changeYear':true,'dateFormat':'yy-mm-dd'});
        head.Util.ajaxForm();
    }
</script>