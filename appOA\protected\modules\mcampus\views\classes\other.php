<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('//mcampus/default/index'))?></li>
        <li class="active"><?php echo Yii::t('site','Basic');?></li>
    </ol>

    <div class="row">
        <div class="col-md-1">
            <?php
            $mainMenu = array(
                array('label'=>Yii::t('user','本学年班级'), 'url'=>array("//mcampus/classes/index","category"=>"current")),
                array('label'=>Yii::t('user','下学年班级'), 'url'=>array("//mcampus/classes/index","category"=>"next")),
                array('label'=>Yii::t('user','历史数据'), 'url'=>array("//mcampus/classes/index","category"=>"other")),
            );

            $this->widget('zii.widgets.CMenu',array(
                'items'=> $mainMenu,
                'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked text-right background-gray'),
                'activeCssClass'=>'active',
                'itemCssClass'=>''
            ));
            ?>
        </div>
        <div class="col-md-1">
            <div class="list-group" id="year_list">
                <?php foreach($yearData as $yid=>$year):?>
                <a class="list-group-item" href="#" data-yid="<?php echo $yid;?>">
                    <?php echo IvyClass::formatSchoolYear($year);?>
                </a>
                <?php endforeach;?>
            </div>
        </div>
        <div class="col-md-10">
            <div id="year_classes"></div>
        </div>
    </div>
</div>

<div id="class-edit-template" style="display: none;">
    <ul class="dropdown-menu">
        <li deleteAction><a href="javascript:void(0);" onclick="classDelete(this);"><span class="glyphicon glyphicon-remove text-danger"></span> 删除班级</a></li>
    </ul>
</div>

<?php
$this->branchSelectParams['extraUrlArray'] = array('//mcampus/classes/index','category'=>$category);
$this->renderPartial('//layouts/common/branchSelectBottom');
?>

<script type="text/template" id="class-type-template">
    <div class="panel panel-default">
        <div class="panel-heading"><%= classtype%></div>
        <ul class="list-group"></ul>
    </div>
</script>

<script type="text/template" id="class-template">
    <li class="list-group-item">
        <h4>
            <span class="dropdown">
                <a class="class-edit-actions" href="javascript:void(0)" data-toggle="dropdown"><span class="glyphicon glyphicon-th-list"></span></a>
            </span>
            <label><%= title%></label> <span title="班级容量" class="badge"><%= capacity%></span>
            <span class="pull-right"><span title="有效学生" class="text-primary"><%= child_inclass%></span>/<span title="在册学生"><%= child_total%></span></span>
        </h4>
        <p><span class="glyphicon glyphicon-time"></span> <%= periodtime%></p>
        <div class="class-teacher-list text-right"></div>
    </li>
</script>

<script type="text/template" id="class-teacher-template">
    <a class="teacher-link <% if(isHead==1)print('head-teacher'); %>" data-placement="top" href="javascript:;" data-toggle="tooltip" title="<% print(teacherData[teacherId].name); %>">
        <img src="<%= teacherPhotoBaseUrl %><% print(teacherData[teacherId].photo); %>" teacherId=<%= teacherId %> class="face img-thumbnail">
    </a>
</script>

<script>
    var itemTypeView;
    var itemClassView;
    var typeData;
    var teacherData = <?php echo CJSON::encode($teacherData);?>;
    var classTeacherData = <?php echo CJSON::encode($classTeachers)?>;
    var classData = <?php echo CJSON::encode($classData);?>;
    var typeList = <?php echo CJSON::encode($typeList);?>;
    var teacherPhotoBaseUrl = '<?php echo Yii::app()->params['OAUploadBaseUrl'];?>/infopub/staff/';

    var types = new Backbone.Collection;

    itemTypeView = Backbone.View.extend({
        tagName : 'div',
        className: 'col-md-4 class-group-box',
        template : _.template($('#class-type-template').html()),
        initialize : function(){
            this.model.bind('change', this.render, this);
        },
        render: function() {
            this.$el.html( this.template(this.model.attributes) );
            return this;
        }
    });

    typeData = Backbone.View.extend({
        el: $('#year_classes'),
        initialize: function(){
            this.listenTo(types, 'add', this.addOne);
            this.listenTo(types, 'reset', this.addAll);
        },
        addOne : function(tModel){
            var view = new itemTypeView({model:tModel});
            var classViews = '';
            _.each(tModel.attributes.cdata, function(value, key){
                var classTeachers = '';
                _.each(classTeacherData[key], function(_value, _key){
                    var __view = _.template($('#class-teacher-template').html(), _value);
                    classTeachers += __view;
                });
                var _view = $(_.template($('#class-template').html(), value));
                _view.find('div.class-teacher-list').html(classTeachers);
                classViews += '<li class="list-group-item" data-classid="'+key+'">'+_view.html()+'</li>';
            });
            var classHtml = view.render().el;
            $(classHtml).find('ul.list-group').html(classViews);
            this.$el.append(classHtml);
        },
        addAll: function(){
            this.$el.html('');
            types.each(this.addOne, this);
        }
    });
    var typeobj = new typeData;

    $('#year_list a').on('click', function(e){
        e.preventDefault();
        $('#year_list a').removeClass('background-gray');
        $(this).addClass('background-gray');

        types.reset();

        var yid = $(this).data('yid');
        for(var cdata in classData[yid]){
            types.add( {classtype: typeList[cdata], cdata: classData[yid][cdata]} );
        }
        $('a.teacher-link').tooltip();
    });

    $('#year_classes').on('click', 'a.class-edit-actions', function(){
        if(_.isUndefined($(this).attr('dropdown-ini'))){
            $(this).after($('#class-edit-template').html());
            $(this).attr('dropdown-ini',1);
            var _tcount = $(this).parents('.list-group-item').children('.class-teacher-list').children('a.teacher-link').length;
            if(_tcount > 0){
                $(this).parents('span.dropdown').children('.dropdown-menu').children('li[deleteAction]').addClass('disabled');
            }
        }
    });

    classDelete = function(obj){
        var _classId = $(obj).parents('.list-group-item').data('classid');
        if(_classId){
            head.load('<?php echo Yii::app()->themeManager->baseUrl;?>/base/js/dialog/dialog.js',function() {
                var postData = {classId:_classId};
                if(!_.isUndefined(classTeacherData) && !_.isUndefined(classTeacherData[_classId]) && classTeacherData[_classId].length>0){
                    head.dialog.alert('只有没有分配过老师、学生以及没有关联账单的班级才可以删除');
                    return;
                }
                $.ajax({
                    url: "<?php echo $this->createUrl('//mcampus/classes/dropClass');?>",
                    type: 'POST',
                    dataType: 'json',
                    data: postData
                }).done(function(data) {
                    if(data.state == 'success'){
                        _.each(types, function(value, key){
                            if(!_.isUndefined(types.models[key].attributes.cdata[_classId])){
                                delete types.models[key].attributes.cdata[_classId];
                            }
                        });
                        types.reset(types.toJSON());
                        $('a.teacher-link').tooltip();
                        resultTip({msg:data.message});
                    }
                    else{
                        head.dialog.alert(data.message);
                    }
                });
            })
        }
    }
</script>

<style>
    img.face{width: 50px}
    a.teacher-link:hover{text-decoration: none;}
    a.head-teacher .img-thumbnail {border-color: #f0ad4e; background-color: #fdf7f7}
    div.hover a{visibility: hidden}
    div.hover:hover .text-muted{color: #428bca}
    div.hover:hover a{visibility: visible}
</style>