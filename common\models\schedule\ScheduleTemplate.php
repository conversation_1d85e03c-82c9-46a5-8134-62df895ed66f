<?php

/**
 * This is the model class for table "ivy_class_schedule_template".
 *
 * The followings are the available columns in table 'ivy_class_schedule_template':
 * @property integer $id
 * @property integer $userid
 * @property integer $scheduleid
 * @property string $tempname
 */
class ScheduleTemplate extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ScheduleTemplate the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_class_schedule_template';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('tempname', 'required'),
			array('userid, scheduleid', 'numerical', 'integerOnly'=>true),
			array('tempname', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, userid, scheduleid, tempname', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'data' => array(self::HAS_ONE, 'ClassScheduleData', array('id'=>'scheduleid'))
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'userid' => 'Userid',
			'scheduleid' => 'Scheduleid',
			'tempname' => 'Tempname',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('userid',$this->userid);
		$criteria->compare('scheduleid',$this->scheduleid);
		$criteria->compare('tempname',$this->tempname,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}