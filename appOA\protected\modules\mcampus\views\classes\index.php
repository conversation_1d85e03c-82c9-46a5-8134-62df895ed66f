<?php
$branchObj = Branch::model()->findByPk($this->branchId);
$category=Yii::app()->request->getParam('category','current');
$teachers = $this->getCampusTeachers();
$calendars = $this->getCalendars();
$hasClass = 0;
$calendarId = ($category == 'current') ? $calendars['currentYid'] : $calendars['nextYid'];
if(!is_null($calendarId)){

    $classes = $this->getClassList($this->branchId, $calendarId);
    $classTeacherIds = array();
    // 如果已分配的老师转到别的校园，依旧显示
    foreach ($classes as $class) {
        foreach ($class->teacherInfo as $key => $value) {
            if (!isset($teachers[$key])) {
                $classTeacherIds[] = $key;
            }
        }
    }
    if ($classTeacherIds) {
        $crit = new CDbCriteria();
        $crit->compare('t.uid', $classTeacherIds);
        $crit->index = 'uid';
        $classTeacherList = User::model()->with(array('profile','staffInfo'))->findAll($crit);
        $teachers = $teachers + $classTeacherList;
    }
    $hasClass = count($classes);

    $assignManyList = array();
    $teacherDatas = array();
    foreach($teachers as $tid=>$teacher){
        $teacherData[$tid] = array(
            'id' => $teacher->uid,
            'name' => $teacher->getName(),
            'photo' => empty($teacher->staffInfo->staff_photo) ? 'blank.jpg' : $teacher->staffInfo->staff_photo,
            'active' => intval($teacher->level),
            'assignMany' => intval($teacher->profile->multiple_class)
        );
        if($teacher->level) {
            $teacherDatas[$tid] = array(
                'id' => $teacher->uid,
                'name' => $teacher->getName(),
                'photo' => empty($teacher->staffInfo->staff_photo) ? 'blank.jpg' : $teacher->staffInfo->staff_photo,
                'active' => intval($teacher->level),
                'assignMany' => intval($teacher->profile->multiple_class)
            );
            if($teacher->profile->multiple_class){
                $assignManyList[] = "{$tid}";
            }
        }
    }
}

?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('//mcampus/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Basic'), array('//mcampus/default/index'))?></li>
        <li class="active"><?php echo Yii::t('site','Class Management');?></li>
    </ol>

    <div class="row">
        <div class="col-md-1">
            <div class="btn-group mb10">
                <button <?php if(is_null($calendars['currentYid']) && is_null($calendars['nextYid'])) echo "disabled"; ?> type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
                    <?php echo Yii::t('campus','Create Class');?> <span class="caret"></span></button>
                <ul class="dropdown-menu" role="menu">
                    <?php if(!is_null($calendars['currentYid'])): ?>
                    <li><a href="#" onclick="createClass(<?php echo $calendars['currentYid'];?>);" calendarid=""><?php echo Yii::t('campus','Current School-year');?></a></li>
                    <?php endif;?>
                    <?php if(!is_null($calendars['nextYid'])): ?>
                    <li class="divider"></li>
                    <li><a href="#" onclick="createClass(<?php echo $calendars['nextYid'];?>);"><?php echo Yii::t('campus','Next School-year');?></a></li>
                    <?php endif;?>
                </ul>
            </div>

            <?php
            $mainMenu = array(
                array('label'=>Yii::t('campus','Current School-year'), 'url'=>array("//mcampus/classes/index","category"=>"current")),
                array('label'=>Yii::t('campus','Next School-year'), 'url'=>array("//mcampus/classes/index","category"=>"next")),
//                array('label'=>Yii::t('user','自定义班级'), 'url'=>array("//mcampus/classes/index","category"=>"custom")),
                array('label'=>Yii::t('campus','Historical Data'), 'url'=>array("//mcampus/classes/index","category"=>"other")),
            );

            $this->widget('zii.widgets.CMenu',array(
                'items'=> $mainMenu,
                'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked text-right background-gray'),
                'activeCssClass'=>'active',
                'itemCssClass'=>''
            ));
            ?>
        </div>
        <div class="col-md-11">
            <?php if($hasClass):?>
            <div class="row">
                <div class="col-md-12">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#accordion" href="#collapseOne">
                                   <span class="glyphicon glyphicon-chevron-down"></span> <?php echo Yii::t('campus','Configure teachers can assign to multiple classes');?>
                                </a>
                            </h4>
                        </div>
                        <div id="collapseOne" class="panel-collapse collapse">
                            <div class="panel-body" id="set-staff-to-multiple-box">

                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif;?>

            <div class="row">
                <?php
                $typeList = IvyClass::getClassTypes(true, $branchObj->type);
                foreach($typeList as $_m=>$_t):
                    $newCol = true;
                    $endCol = true;
                    if ($branchObj->type == 50) {
                        // 特殊处理，e1,e2在一列，e3,e4在一列, e5,e6在一列

                        // e1,e3,e5 开始新列不结束
                        if (in_array($_m, array('e1', 'e3','e5'))) {
                            $newCol = true;
                            $endCol = false;
                        }
                        // e2,e4,65 不开始新列结束当前列
                        if (in_array($_m, array('e2', 'e4','e6'))) {
                            $newCol = false;
                            $endCol = true;
                        }
                    }


                    if($newCol): ?>
                        <div class="col-md-3 class-group" style="display: none;">
                    <?php
                    endif;
                    ?>
                    <div class="class-group-box" style="display: none;">
                        <div class="panel panel-default">
                            <!-- Default panel contents -->
                            <div class="panel-heading"><?php echo $_t;?></div>
                            <!-- List group -->
                            <ul class="list-group" classGroup="<?php echo $_m;?>">
                            </ul>
                        </div>
                    </div>
                    <?php if($endCol): ?>
                    </div>
                    <?php endif;?>
                <?php endforeach;?>

                <?php

                //新建班级时的默认MODEL
                $classData[0] = array(
                    'classid' => 0,
                    'schoolid' => $this->branchId,
                    'yid' => 0,
                    'title' => '',
                    'capacity' => 20,
                    'classtype' => '',
                    'periodtime' => '',
                    'periodData' => array(),
                    'introduction' => '',
                );

                if($hasClass):
                foreach($classes as $class):
                    $classData[$class->classid] = array(
                        'classid' => $class->classid,
                        'schoolid' => $class->schoolid,
                        'yid' => $class->yid,
                        'title' => $class->title,
                        'childage' => $class->child_age,
                        'capacity' => $class->capacity,
                        'classtype' => $class->classtype,
                        'periodtime' => $class->periodtime,
                        'periodData' => explode('|', $class->periodtime),
                        'introduction' => $class->introduction,
                        'stat' => $class->stat,
                    );
                    ?>
                <li class="list-group-item <?php echo in_array($class->stat, array(0, 20)) ? 'disabled' : ''?>"
                    classType="<?php echo ($class->classtype=='0') ? 'c' : $class->classtype;?>"
                    classId="<?php echo $class->classid;?>"
                    childAge="<?php echo $class->child_age;?>"
                    teacherDisplayMode="photo"
                    style="display: none;">
                    <h4>
                        <span class="dropdown">
                            <a class="class-edit-actions" href="javascript:void(0)" data-toggle="dropdown"><span class="glyphicon glyphicon-th-list"></span></a>
                            <!--submenu appended here-->
                        </span>
                        <label><?php echo $class->title;?></label> <span title="班级容量" class="badge"><?php echo $class->capacity;?></span>
                    <span class="pull-right"><span title="有效学生" class="text-primary"><?php echo $class->child_inclass;?></span>/<span title="在册学生"><?php echo $class->child_total;?></span></span>
                    </h4>
                    <?php if($class->child_age < 7):?>
                    <p><span class="glyphicon glyphicon-time"></span> <?php echo strtr('0:1 ~ 2:3', explode('|',$class->periodtime));?></p>
                    <?php endif;?>
                    <div class="class-teacher-list text-right">
                        <?php foreach($class->teacherInfo as $tid=>$tv):?>
                            <?php
                                $classTeachers[$class->classid][] = array(
                                    'teacherId' => $tid,
                                    'isHead' => $tv->isheadteacher,
                                    'isHelp' => $tv->ishelpteacher,
                                    'weight' => $tv->weight
                                );
                            ?>
                        <?php endforeach;?>
                    </div>
                </li>

                <?php
                endforeach;
                else:?>

                    <div class="col-md-4">
                        <div class="alert alert-danger">尚未建立班级</div>
                    </div>

                <?php
                endif;
                ?>
            </div>
        </div>
    </div>
</div>
<div style="display: none" id="class-menu">

</div>

<div id="class-edit-template" style="display: none;">
    <ul class="dropdown-menu">
        <li><a href="javascript:void(0);" onclick="classEdit(this);">
                <span class="glyphicon glyphicon-pencil"></span> <?php echo Yii::t('campus','Edit Class Info');?></a></li>
        <li><a href="javascript:void(0);" onclick="classStaff(this);">
                <span class="glyphicon glyphicon-user"></span> <?php echo Yii::t('campus','Assign Class Teachers');?></a></li>
        <li class="divider"></li>
        <li deleteAction><a href="javascript:void(0);" onclick="classDelete(this);">
                <span class="glyphicon glyphicon-remove text-danger"></span> <?php echo Yii::t('campus','Delete Class');?></a></li>
    </ul>
</div>
<?php

$this->renderPartial('class/_classAssignModal', array('calendarId'=>$calendarId));
$this->renderPartial('class/_classEditModal', array('calendarId'=>$calendarId, 'branchObj'=>$branchObj));


$this->branchSelectParams['extraUrlArray'] = array('//mcampus/classes/index','category'=>$category);
$this->renderPartial('//layouts/common/branchSelectBottom');
?>

<?php if(1 || $hasClass):?>

    <script type="text/template" id="teacher-item-template">
        <a class="teacher-link <% if(isHead==1)print('head-teacher'); %>"
           data-placement="top"
           href="/mpub/default/staff?id=<%= teacherId %>"
           target="_blank"
           data-toggle="tooltip"
           title="<% print(teacherData[teacherId].name); %>">
            <img src="<%= teacherPhotoBaseUrl %><% print(teacherData[teacherId].photo); %>" teacherId=<%= teacherId %> class="face img-thumbnail">
        </a>
    </script>

    <script type="text/template" id="teacher-link-item-template">
        <h5><a class="teacher-link <% if(isHead==1)print('head-teacher'); %>"
           href="/mpub/default/staff?id=<%= teacherId %>"
           target="_blank"
            >
           <span class="glyphicon glyphicon-user"></span>
           <% print(teacherData[teacherId].name); %>
        </a></h5>
    </script>

    <script type="text/template" id="assignMany-item-yes-template">
        <div class="col-md-3 hover p2 item" teacherid=<%= id %> assignMany=<%= assignMany %>>
        <span class="wrap program20">
            <span class="glyphicon glyphicon-star flag"></span>
            <%= name %>
            <a href="javascript:void(0)" onclick="postAssignMany('revoke',this)" teacherid=<%= id %> ><span class="glyphicon glyphicon-minus-sign"></span></a>
        </span>
        </div>
    </script>

    <script type="text/template" id="assignMany-item-no-template">
        <div class="col-md-3 hover p2 item" teacherid=<%= id %> assignMany=<%= assignMany %>>
        <span class="wrap text-muted">
            <span class="glyphicon glyphicon-ok flag" style="visibility: hidden"></span>
            <%= name %>
            <a href="javascript:void(0)" onclick="postAssignMany('invoke',this)" teacherid=<%= id %> ><span class="glyphicon glyphicon-plus-sign"></span></a>
        </span>
        </div>
    </script>

    <script>
        var classObjs; //Jquery 元素
        var teacherData = <?php echo CJSON::encode($teacherData);?>; //教师基本信息
        var teacherDatas = <?php echo CJSON::encode($teacherDatas);?>; //教师基本信息
        var classTeacherData = <?php echo CJSON::encode($classTeachers)?>; //班级教师信息
        var showTeachersInfo; //显示班级老师信息函数
        var classEdit; //编辑班级信息函数
        var classStaff; //分配班级老师函数
        var classDelete; //删除班级
        var teacherPhotoBaseUrl = '<?php echo Yii::app()->params['OAUploadBaseUrl'];?>/infopub/staff/';
        var template = _.template($('#teacher-item-template').html());
        var linkTemplate = _.template($('#teacher-link-item-template').html());
        var assignManyYestemplate = _.template($('#assignMany-item-yes-template').html());
        var assignManyNotemplate = _.template($('#assignMany-item-no-template').html());
        var teacherCandidates = []; //可分配的教师
        var assignedTeachers = []; //已分配的教师
        var assignManyList = <?php echo CJSON::encode($assignManyList);?>; //可分配至多个班级的教师
        var initialManyAssign;
        var postAssignMany;
        var classData = <?php echo CJSON::encode($classData);?>;
        var createClass; //新建班级
        var isDaystar = <?php echo $this->branchObj->type == 50 ? 1 : 0; ?>

        var switchTeacherDisplay = null;
        var switchTeacherDisplayButton = '<h5 class="pull-left display-switch"><a href="javascript:;" onclick="switchTeacherDisplay(this)"><span class="glyphicon glyphicon-list-alt"></span></a></h5>';


        if(_.isNull(classTeacherData)) classTeacherData = {};
        $(function(){
            initialManyAssign = function(){
                var _teacherSorts = _.sortBy(teacherDatas, 'name');
                _.each(_teacherSorts, function (_teacher) {
                    if (_teacher.active == 1) {
                        if (_teacher.assignMany == 1)
                            var str = assignManyYestemplate(_teacher)
                        else
                            var str = assignManyNotemplate(_teacher)
                        $('#set-staff-to-multiple-box').append(str);
                    }
                });
            }

            postAssignMany = function(action, obj){
                var teacherId = parseInt($(obj).attr('teacherid'));
                var postData = {teacherId:teacherId,doType:action};
                if(teacherId){
                    $.ajax({
                        url: "<?php echo $this->createUrl('//mcampus/classes/setAssignMany');?>",
                        type: 'POST',
                        dataType: 'json',
                        data: postData
                    }).done(function(data) {
                        if(data.state == 'success'){
                            var tid = data.data.teacherId;
                            if(data.data.doType=="invoke"){
                                teacherData[tid].assignMany = 1;
                                var str = assignManyYestemplate(teacherData[tid]);
                                assignManyList = _.union(assignManyList, tid);
                            }else{
                                teacherData[tid].assignMany = 0;
                                var str = assignManyNotemplate(teacherData[tid]);
                                assignManyList = _.without(assignManyList, tid);
                            }
                            $('#set-staff-to-multiple-box .item[teacherid|="'+tid+'"]').replaceWith(str);
                        }else{
                            alert(data.message);
                        }
                    });
                }
            };

            showTeachersInfo = function(classid){
                var _teachersContainer = $('li.list-group-item[classId="'+classid+'"]').find('.class-teacher-list');
                _teachersContainer.empty();
                if(classTeacherData!=null && !_.isUndefined(classTeacherData[classid])){
                    classTeacherData[classid].forEach(function(item){
                        if(!_.isUndefined(teacherData[item.teacherId])){
                            var teacherView = template(item);
                            _teachersContainer.append(teacherView);
                        }
                    });
                    _teachersContainer.prepend(switchTeacherDisplayButton);
                }
            };

            switchTeacherDisplay = function(obj){
                var _tmpO = $(obj).parents('.list-group-item');
                var _classid = _tmpO.attr('classid');
                var _currentMode = _tmpO.attr('teacherDisplayMode');
                if(_currentMode == 'photo'){
                    $(obj).parents('h5.display-switch').siblings('a.teacher-link').remove();
                    classTeacherData[_classid].forEach(function(item){
                        if(!_.isUndefined(teacherData[item.teacherId])){
                            var teacherView = linkTemplate(item);
                            $(obj).parents('.class-teacher-list').append(teacherView);
                        }
                    });
                    _tmpO.attr('teacherDisplayMode','text');
                    $(obj).find('span.glyphicon').removeClass('glyphicon-list-alt').addClass('glyphicon-user');
                }else{
                    showTeachersInfo(_classid);
                    _tmpO.attr('teacherDisplayMode','photo');
                    $(obj).find('span.glyphicon').removeClass('glyphicon-user').addClass('glyphicon-list-alt');
                }
            }

            classDelete = function(obj){
                var _classId = $(obj).parents('.list-group-item').attr('classId');
                if(_classId){
                    head.load('<?php echo Yii::app()->themeManager->baseUrl;?>/base/js/dialog/dialog.js',function() {
                        var postData = {classId:_classId};
                        if(!_.isUndefined(classTeacherData) && !_.isUndefined(classTeacherData[_classId]) && classTeacherData[_classId].length>0){
                            head.dialog.alert('只有没有分配过老师、学生以及没有关联账单的班级才可以删除');
                            return;
                        }
                        $.ajax({
                            url: "<?php echo $this->createUrl('//mcampus/classes/dropClass');?>",
                            type: 'POST',
                            dataType: 'json',
                            data: postData
                        }).done(function(data) {
                            if(data.state == 'success'){
                                $('li.list-group-item[classid="'+data.data.classId+'"]').remove();
                                delete classData[_classId];

                                resultTip({msg:data.message});
                            }
                            else{
                                head.dialog.alert(data.message);
                            }
                        });
                    });
                }
            }

            classObjs = $('.list-group-item[classType]');
            for(var i=0; i<classObjs.length;i++){
                var _classType = $(classObjs[i]).attr('classType');
                var _groupToObj = $('.list-group[classGroup="'+_classType+'"]');
                $(classObjs[i]).appendTo(_groupToObj);
                $(classObjs[i]).show();
                _groupToObj.parents('.class-group-box').show();
                _groupToObj.parents('.class-group').show();
            }

            for(var i=0; i<classObjs.length;i++){

                var _classid = $(classObjs[i]).attr('classId');
                showTeachersInfo(_classid);
            }
            $('a.teacher-link').tooltip();

            initialManyAssign();

            $('a.class-edit-actions').click(function(){
                if(_.isUndefined($(this).attr('dropdown-ini'))){
                    $(this).after($('#class-edit-template').html());
                    $(this).attr('dropdown-ini',1);
                    var _tcount = $(this).parents('.list-group-item').children('.class-teacher-list').children('a.teacher-link').length;
                    if(_tcount > 0){
                        $(this).parents('span.dropdown').children('.dropdown-menu').children('li[deleteAction]').addClass('disabled');
                    }
                }
            });

        })
    </script>

    <style>
        img.face{width: 50px}
        a.teacher-link:hover{text-decoration: none;}
        a.head-teacher .img-thumbnail {border-color: #f0ad4e; background-color: #fdf7f7}
        div.hover a{visibility: hidden}
        div.hover:hover .text-muted{color: #428bca}
        div.hover:hover a{visibility: visible}
    </style>

<?php endif;?>