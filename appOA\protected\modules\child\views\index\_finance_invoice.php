<?php
	
	function renderRefundInfo($invoice){
		$ret = "";
		if(!empty($invoice->workflowRefund)){
			$ret = CHtml::openTag("span", array("class"=>"org"));
			$ret .= Yii::t("payment", "Refund Found");
			$ret .= CHtml::closeTag("span");
		}
		return $ret;
	}
	function renderInvoiceLink($invoice){
		return CHtml::link($invoice->title, array("//child/invoice/viewInvoice", 'invoiceid'=>$invoice->invoice_id), array('class'=>'mr5 J_dialog', 'non-resize'=>1));
	}
    function renderExtraLink($invoice){
        $status = '';
        if( $invoice->status == Invoice::STATS_UNPAID){
            $status .= '<br>'.CHtml::link(Yii::t("payment","Cancel"), 'javascript:;', array('class'=>'mr5', 'onclick'=>'cancelInvoice('.$invoice->invoice_id.')')) . ' ' . CHtml::link(Yii::t("payment","打印"), array('/child/invoice/printInvoice', 'invoice_id'=>$invoice->invoice_id), array('class'=>'mr5', 'target'=>'_blank'));
        }
        elseif( $invoice->status == Invoice::STATS_PARTIALLY_PAID ){
            if(in_array(Yii::app()->user->id, array(2,5))){ //暂时写死 下月开放
                $status .= '<br>'.CHtml::link('退费', 'javascript:;', array('class'=>'mr5', 'onclick'=>'cancelPay('.$invoice->invoice_id.');')) . ' ' . CHtml::link(Yii::t("payment","打印"), array('/child/invoice/printInvoice', 'invoice_id'=>$invoice->invoice_id), array('class'=>'mr5', 'target'=>'_blank'));
            }
            else{
                $status .= '<br>'.CHtml::link(Yii::t("payment","打印"), array('/child/invoice/printInvoice', 'invoice_id'=>$invoice->invoice_id), array('class'=>'mr5', 'target'=>'_blank'));
            }
        }
        elseif($invoice->status == Invoice::STATS_CANCELLED){
			return CHtml::openTag("span",array("class"=>'ml5',"title"=>Yii::t("payment","作废时间"))) . OA::formatDateTime($invoice->send_timestamp) . CHtml::closeTag("span");
		}
        return $status;
    }
    
    $itype = Yii::app()->request->getParam('iType', null);
    $invalid = Yii::app()->request->getParam('invalid', null);
    echo CHtml::form($this->createUrl('/child/index/finance', array('t'=>'invoice')), 'get', array('class'=>'search_type', 'id'=>'ft'));
    echo CHtml::openTag('div', array('class'=>'mb10 J_check_wrap'));
    echo CHtml::checkBox('alltype', true, array('class'=>'J_check_all', 'data-checklist'=>'J_check_c1', 'data-direction'=>'x'));
     echo CHtml::openTag('label', array('for'=>'alltype'));
    echo '<strong>全部账单类型</strong>';
    echo CHtml::closeTag('label');
    echo '<br>';
    echo CHtml::checkBoxList('iType', isset($itype) ? $itype : array_keys($this->policyApi->getInvoiceType()), $this->policyApi->getInvoiceType(), array('separator'=>'&nbsp;&nbsp;', 'class'=>'J_tof J_check', 'data-xid'=>'J_check_c1'));
    echo CHtml::closeTag('div');
    echo CHtml::openTag('div', array('class'=>'mb10'));
    echo CHtml::checkBox('invalid', !$invalid ? false : true , array('class'=>'J_tof'));
    echo CHtml::openTag('label', array('for'=>'invalid'));
    echo '<strong>显示作废账单</strong>';
    echo CHtml::closeTag('label');
    echo CHtml::closeTag('div');
    echo CHtml::openTag('div', array('class'=>'mb10 btn_side'));
    echo CHtml::submitButton('确定', array('class'=>'btn'));
    echo CHtml::closeTag('div');
    echo CHtml::endForm();
    
	
	$this->widget('ext.ivyCGridView.IvyCGridView', array(
		'id'=>'child-invoice',
		'dataProvider'=>$dataProvider,
		'template'=>"<div class='table_list'>{items}</div><div class='table_list'>{summary}</div><div class='table_list'>{pager}</div>",
		'colgroups'=>array(
			array(
				"colwidth"=>array(200,160,180,150,150),
			)
		),
		'ajaxUpdate'=>false,
		'columns'=>array(
			'title'=>array(
				'name'=>'title',
				'type'=>'raw',
				'value'=>'renderInvoiceLink($data)',
			),
			'amount'=>array(
				'name'=>'amount',
			),
			'payment_type'=>array(
				'name'=>'payment_type',
				'value'=>'Yii::app()->controller->policyApi->renderFeeType($data->payment_type)',
			),
			'createTime'=>array(
				'name'=>'timestamp',
				'header'=>Yii::t("payment", "Issue Date"),
				'value'=>'OA::formatDateTime($data->timestamp)'
			),
			'status'=>array(
				'header'=>Yii::t("payment", "Invoice Status"),
				//'value'=> 'getInvoiceStatus($data->status)'
				'type'=>'raw',
				'value'=> 'Yii::app()->controller->policyApi->renderInvoiceStatus($data->status, null, true).renderExtraLink($data)'
			),
			'refund'=>array(
				'header'=>Yii::t("payment", "Refund"),
				'type'=>'raw',
				'value'=>'renderRefundInfo($data)'
			)
			
		),
	));							

?>

<script type="text/javascript">
function cancelInvoice(id)
{
    if (confirm("<?php echo Yii::t('message', 'Sure to preceed?');?>")){
        $.post('<?php echo $this->createUrl("/child/invoice/processCancelInvoice");?>', {id:id}, function(data){
            if (data.state === 'success'){
                location.reload();
            }
            else {
                alert(data.message);
            }
        }, 'json');
    }
}
function cancelPay(id){
    if (confirm("<?php echo Yii::t('message', 'Sure to preceed?');?>")){
        $.post('<?php echo $this->createUrl("processCancelPay");?>', {id:id}, function(data){
            if (data.state === 'success'){
                location.reload();
            }
            else {
                alert(data.message);
            }
        }, 'json');
    }
}
</script>