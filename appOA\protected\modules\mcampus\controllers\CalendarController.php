<?php

class CalendarController extends BranchBasedController
{
    public $actionAccessAuths = array(
        'index'             => 'o_A_Access',
        'GetCalendarDays'   => 'o_A_Access',
        'SaveCalendarDays'  => 'o_A_Adm_Calendar',
        'DelDay'            => 'o_A_Adm_Calendar',
    );

    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }
    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mcampus/calendar/index');

        Yii::import('common.models.calendar.*');
    }

    /**
     * 校园管理校历主页面
     */
    public function actionIndex()
    {
        //跳转到新的页面
        $this->redirect(array('//mcampus/calendarNew/index'));
        die();
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile($cs->getCoreScriptUrl().'/jui/js/jquery-ui-i18n.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/phpfunc.js');
        $cs->registerCssFile(Yii::app()->theme->baseUrl.'/css/calendar.css');

        $this->render('index');
    }

    /**
     * 保存校历日
     */
    public function actionSaveCalendarDays(){
        $editingDays = Yii::app()->request->getParam('editingDays', '');

        if($editingDays && isset($_POST['CalendarDay']) && $_POST['CalendarDay']['yid']){
            $postYid = $_POST['CalendarDay']['yid'];
            //确保该模版已经分配给校园

            $find = CalendarSchool::model()->findByAttributes(array('yid'=>$postYid,'branchid'=>$this->branchId));
            if($find){
                $editingDaysArr = explode(',', $editingDays);
                $editingDayTime = array();
                foreach($editingDaysArr as $_time){
                    $editingDayTime[] = strtotime($_time);
                }
                $criteria = new CDbCriteria();
                $criteria->compare('yid', $postYid);
                $criteria->compare('date_timestamp', $editingDayTime);
                $criteria->compare('schoolid', $this->branchId);
                $criteria->index = 'date_timestamp';
                $models = CalendarDay::model()->findAll($criteria);

                foreach($editingDayTime as $daytime){
                    /*if($models[$daytime] === null)
                        $model = new CalendarDay();
                    else{
                        //模版内容不能修改，跳过
                        $school = ($models[$daytime]->schoolid) ? 1 : 0;
                        if($school == 0){
                            continue;
                        }else{
                            $model = $models[$daytime];
                        }
                    }*/

                    if($models[$daytime]){
                        $model = $models[$daytime];
                    }else{
                        $criteria = new CDbCriteria();
                        $criteria->compare('yid', $_POST['CalendarDay']['yid']);
                        $criteria->compare('date_timestamp', $daytime);
                        $criteria->compare('schoolid', 0);
                        $modelCounts = CalendarDay::model()->count($criteria);
                        if($modelCounts){
                            continue;
                        }else{
                            $model = new CalendarDay();
                        }
                    }

                    $model->attributes = $_POST['CalendarDay'];
                    $model->event = 20; //强制类型为事件或活动
                    $model->date = date('Ymd', $daytime);
                    $model->sid = 0;
                    $model->date_timestamp = $daytime;
                    $model->updated_timestamp = time();
                    $model->userid = Yii::app()->user->id;
                    $model->schoolid = $this->branchId;
                    if(!$model->save()){
                        $this->addMessage('state', 'fail');
                        $this->addMessage('error', $model->getErrors());
                        $errs = current($model->getErrors());
                        $this->addMessage('message', $errs?$errs[0]:Yii::t('message','Saving Failed!'));
                        break;
                    }
                    else{
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', Yii::t('message','Data saved!'));
                        $data[$model->date] = $model->attributes;
                        $data[$model->date]['datestr'] = date('Y-m-d', $model->date_timestamp);
                        $data[$model->date]['title_cn'] = CHtml::encode($model->title_cn);
                        $data[$model->date]['memo_cn'] = CHtml::encode($model->memo_cn);
                        $data[$model->date]['title_en'] = CHtml::encode($model->title_en);
                        $data[$model->date]['memo_en'] = CHtml::encode($model->memo_en);
                        $data[$model->date]['url'] = isset($branchid) ? $this->createUrl('//moperation/calendar/delDay', array('branchId' => $branchid, 'did' => $model->did, 'yid' => $model->yid)) : $this->createUrl('//moperation/calendar/delDay', array('did' => $model->did, 'yid' => $model->yid));
                        $this->addMessage('callback', 'cbc');

//                        $schoolCalendar = new SchoolCalendar();
//                        $schoolCalendar->updateSchoolDayForMonth($model->yid, date('Ym', $daytime));
                    }
                }
                $this->addMessage('data', $data);
                $this->showMessage();
            }
        }
    }

    /**
     * 删除校历日
     */
    public function actionDelDay()
    {
        if(Yii::app()->request->isPostRequest){
            $did = Yii::app()->request->getParam('did', 0);
            $yid = Yii::app()->request->getParam('yid', 0);
            if($did && $yid){
                $model = CalendarDay::model()->findByAttributes(array('did'=>$did, 'yid'=>$yid, 'schoolid'=>$this->branchId));
                //确保该模版已经分配给校园
                if($model){
                    $event = $model->event;
                    $day = $model->date;
                    $mm = date('Ym', $model->date_timestamp);
                    if($model->delete()){
                        if($event != 20){
                            $schoolCalendar = new SchoolCalendar();
                            $schoolCalendar->updateSchoolDayForMonth($yid, $mm);
                        }

                        $this->addMessage('state', 'success');
                        $this->addMessage('message', '删除成功！');
                        $this->addMessage('callback', 'deldayCallback');
                        $this->addMessage('data', array('day'=>$day));
                        $this->showMessage();
                    }
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '删除失败！');
                    $this->showMessage();
                }
            }
        }
    }
}