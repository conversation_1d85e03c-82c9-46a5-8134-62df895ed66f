<?php

/**
 * This is the model class for table "ivy_catering_menu_weeklink".
 *
 * The followings are the available columns in table 'ivy_catering_menu_weeklink':
 * @property integer $id
 * @property integer $menu_id
 * @property integer $allergy_id
 * @property integer $week_num
 * @property integer $monday_timestamp
 * @property string $schoolid
 * @property integer $yid
 * @property integer $status
 * @property integer $update_user
 * @property integer $update_timestamp
 */
class CateringMenuWeeklink extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return CateringMenuWeeklink the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_catering_menu_weeklink';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('yid', 'required'),
			array('menu_id, allergy_id, week_num, monday_timestamp, yid, status, update_user, update_timestamp, review_by, review_at', 'numerical', 'integerOnly'=>true),
			array('schoolid', 'length', 'max'=>25),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, menu_id, allergy_id, week_num, monday_timestamp, schoolid, yid, status, update_user, update_timestamp', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'branch' => array(self::BELONGS_TO, 'Branch', array('schoolid'=>'branchid')),
			'calendar' => array(self::BELONGS_TO, 'Calendar', array('yid'=>'yid')),
            'common' => array(self::BELONGS_TO, 'CateringMenu', 'menu_id'),
            'allergy' => array(self::BELONGS_TO, 'CateringMenu', 'allergy_id'),
            'review' => array(self::BELONGS_TO, 'User', array('review_by'=>'uid'))
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'menu_id' => 'Menu',
			'allergy_id' => 'Allergy',
			'week_num' => 'Week Num',
			'monday_timestamp' => 'Monday Timestamp',
			'schoolid' => 'Schoolid',
			'yid' => 'Yid',
			'status' => 'Status',
			'update_user' => 'Update User',
			'update_timestamp' => 'Update Timestamp',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('menu_id',$this->menu_id);
		$criteria->compare('allergy_id',$this->allergy_id);
		$criteria->compare('week_num',$this->week_num);
		$criteria->compare('monday_timestamp',$this->monday_timestamp);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('status',$this->status);
		$criteria->compare('update_user',$this->update_user);
		$criteria->compare('update_timestamp',$this->update_timestamp);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}