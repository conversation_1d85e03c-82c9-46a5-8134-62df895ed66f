<?php

class PreviewController extends TeachBasedController
{
    public $selectedClassId=0;
    public $selectedSemester='';
    public $selectedTask='';
    public $selectedChildId=0;
    public $semesters=array(1,2);

	public $printFW = array();

    // 访问action的初级权限
    public $actionAccessAuths = array(
        'setAllDs'              => 'o_MS_RrportMgt',
        'setDsreportStatus'     => 'o_MS_RrportMgt',
        'coures'                => 'o_MS_Rrport',
        'saveDsreport'          => 'o_MS_Rrport',
        'updatReport'           => 'o_MS_Rrport',
        'delDsreport'           => 'o_MS_Rrport',
        'reportchild'           => 'o_MS_Rrport',
    );

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'teaching';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Teaching Tasks');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mteaching/preview/index');
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile($cs->getCoreScriptUrl().'/jui/js/jquery-ui-i18n.min.js');

        $cs->registerCssFile(Yii::app()->themeManager->baseUrl.'/base/css/floatingscroll.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/jquery.floatingscroll.min.js');

        Yii::import('common.models.portfolio.*');
        Yii::import('common.models.secondary.*');
        Yii::import('common.models.timetable.*');
    }

    public function beforeAction($action){
        if(!parent::beforeAction($action)){
            return false;
        }

        $this->selectedClassId = Yii::app()->request->getParam('classid', 0);
        $this->selectedSemester = Yii::app()->request->getParam('semester', 0);
        $this->selectedTask = Yii::app()->request->getParam('task', 0);
        $this->selectedChildId = Yii::app()->request->getParam('childid', 0);

        return true;
    }


    public function actionIndex($semester='')
    {
        parent::initExt();

        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');

        // 查找当前tid
        $yid = $this->branchObj->schcalendar;

        // 当前学年的所有报告
        $crit = new CDbCriteria();
        $crit->compare('calendar', $yid);
        $crit->compare('schoolid', $this->branchId);
        $crit->order = "start_time";
        $crit->index = "id";
        $report = AchievementReport::model()->findAll($crit);
        $cReport = isset($report[$semester]) ? $report[$semester] : array();

        $timeTable = Timetable::model()->findByAttributes(array('yid' => $yid, 'schoolid' => $this->branchId));
        if (!$timeTable) {
            return false;
        }
        $tid = $timeTable->id;
        $data = array(
            'yid' => $yid,
            'semester' => $semester
            );

        $criteria = new CDbCriteria();
        $criteria->compare('yid', $yid);
        $criteria->compare('classtype', array("e6", "e7",'e8','e9', 'e10', 'e11', 'e12'));
        $criteria->compare('stat', 10);
        $criteria->order='child_age ASC, title ASC';
        $classModel = IvyClass::model()->findAll($criteria);
        //  所有中学班级
        $classArr = array();
        foreach ($classModel as $val) {
            $classArr[$val->classid] = $val->title;
        }

        $criteria = new CDbCriteria();
        $criteria->compare('classid', array_keys($classArr));
        $criteria->compare('schoolid', $this->branchId);
        $criteria->compare('status', array(ChildProfileBasic::STATS_ACTIVE_WAITING, ChildProfileBasic::STATS_ACTIVE));
        $childModel = ChildProfileBasic::model()->findAll($criteria);

        // 所有中学班级里的所有孩子
        $childArr = array();
        $classData = array();
        foreach ($childModel as $val) {
            $childArr[$val->classid][$val->childid] = $val->childid;
        }

        foreach ($classArr as $key=>$val) {
            if(isset($childArr[$key])){
                $classData[$key] = $val;
            }
        }

        $data['classData'] = $classData;

        $this->render('index', array(
            'data'=>$data,
            'report'=>$report,
            'semester'=>$semester,
        ));
    }


    public function actionShowChildReport()
    {
        $classid = Yii::app()->request->getParam('classid', 0);
        $yid = Yii::app()->request->getParam('yid', 0);
        $semester = Yii::app()->request->getParam('semester', 0);

        $reportModel = AchievementReport::model()->findByPk($semester);

        $term = $reportModel->cycle > 2 ? 2 : 1;

        $criteria = new CDbCriteria();
        $criteria->compare('classid', $classid);
        $criteria->compare('schoolid', $this->branchId);
        $criteria->compare('status', array(ChildProfileBasic::STATS_ACTIVE_WAITING,
            ChildProfileBasic::STATS_ACTIVE,
            ChildProfileBasic::STATS_DROPPINGOUT,
            ChildProfileBasic::STATS_DROPOUT
        ));
        $criteria->index = 'childid';
        $childModel = ChildProfileBasic::model()->findAll($criteria);
        $childData = array();
        foreach ($childModel as $val) {
            $childids[] = $val->childid;
            $childData[$val->childid] = array(
                'childid' => $val->childid,
                'childname' => $val->getChildName(false,false,true),
                'childPhoto' => ($val->photo) ? CommonUtils::childPhotoUrl($val->photo) : CommonUtils::childPhotoUrl('blank.gif'),
                'type' => $reportModel->type,
                'cycle' => $reportModel->cycle,
            );
        }

        // 查询出当前班级下每个孩子的所有的课程
        $criteria = new CDbCriteria();
        $criteria->compare('yid', $yid);
        $criteria->compare('status', 1);
        $timeTbaleModel = Timetable::model()->find($criteria);

        $criteria = new CDbCriteria();
        // $criteria->compare('class_id', $classid);
        $criteria->compare('child_id', $childids);
        $criteria->compare('status', array(1,99));
        $criteria->compare('tid', $timeTbaleModel->id);
        $model = TimetableStudentData::model()->findAll($criteria);
        $in_category_where = $term==1 ? array('A','B') : array('C','D');
        if($timeTbaleModel->id>=5){
            $in_category_where = array('A','B');
        }
        $category_data = Yii::app()->db->createCommand()
            ->select("sa.id, sa.course_id, sa.course_code, sa.class_room_name, ca.category, ca.weekday,
                ca.period, ca.timeslot_flag,sa.status,child_id")
            ->from('mimssub.ivy_timetable_student_data sa')
            ->where(array('in','child_id',$childids))
            ->andwhere(array('in','category',$in_category_where))
//            ->andwhere('sa.status=1')
            ->andwhere('sa.status in (1,99)')
            ->andwhere('ca.status=1')
            ->andwhere('sa.tid = :tid',array(':tid'=>$timeTbaleModel->id))
            ->leftJoin('mimssub.ivy_timetable_course_data ca', 'sa.course_id=ca.course_id')
            ->queryAll();
        $category_data_by_course_id = array();
        $category_data_by_status = array();
        foreach ($category_data as $value){
            $category_data_by_course_id[$value['course_id']] = $value['course_code'];
            $category_data_by_status[$value['child_id']][$value['status']][$value['course_id']] = $value['course_code'];
        }
        foreach ($model as $key => $item) {
            #去掉不是本学期的课程
            if($item->course_id!=0 && !array_key_exists($item->course_id, $category_data_by_course_id)){
                unset($model[$key]);
            }
//            if ($item->term != 0 && $item->term != $term) {
//               unset($model[$key]);
//            }
        }

        $childCourses = array();
        $courseids = array();
        $cycle = $reportModel->cycle;
        $one_report_course_id = 0;
        foreach ($model as $val){
            #只显示设分配了课程标准的数据
            $cycleField = 'period' . $cycle;
            if (isset($val->tableCourse->$cycleField) && $val->tableCourse->$cycleField) {
                $report_course_id = $val->tableCourse->$cycleField;
                $one_report_course_id = $val->tableCourse->$cycleField;
            } else {
                $report_course_id = $val->tableCourse->report_course_id;
            }
            // if ($report_course_id > 0) {
            //     $one_report_course_id = $report_course_id;
            // }
            
            if($report_course_id != 0){
                $courseids[] =  $report_course_id;
                $childCourses[$val->child_id][] = array(
                    'title' => $val->tableCourse->getTitle(),
                    'report_course' => $report_course_id,
                    'course_code' => $val->tableCourse->course_code
                );
            }
        }

        // 辅导员填写的报告信息
        $criteria = new CDbCriteria();
        $criteria->compare('calender', $yid);
        $criteria->compare('report_id', $reportModel->id);
        $criteria->compare('classid', $classid);
        $criteria->index = 'childid';
        $childReportsModel = AchievementReportChild::model()->findAll($criteria);

        $childTotalArr = array();
        $finalScoresData = array();
        $finalData = array();
        #第四次评估有总分最终成绩等数据
        if($reportModel->cycle == 4){
            // 查询出当前孩子所需课程给的总分
            $criteria = new CDbCriteria();
            $criteria->compare('t.calender_id', $yid);
            $criteria->compare('t.childid', $childids);
            $criteria->compare('t.courseid', $courseids);
            $childTotalmodel = AchievementReportChildTotal::model()->findAll($criteria);
            $finalScoresIds = array();
            foreach ($childTotalmodel as $val){
                $childTotalArr[$val->childid][$val->courseid] = $val->oprion_value;
                $finalData[$val->childid][$val->timetable_records_code] = array_values(json_decode($val->frecyion_total,true));
                $frecyion = json_decode($val->frecyion_total,true);
                foreach ($frecyion as $val1){
                    if(is_numeric($val1)){
                        $finalScoresIds[$val1] = $val1;
                    }
                }
            }

            if($finalScoresIds){
                $scoresModel = AchievementCourseScores::model()->findAllByPk($finalScoresIds);
                if($scoresModel){
                    foreach ($scoresModel as $val){
                        $finalScoresData[$val->id] = $val->fraction;
                    }
                }
            }
        }
        $sampleProgerssReport = false;
        // 根据标准判断是否为 sample progress report
        if($one_report_course_id) {
            $achievementReportCourse = AchievementReportCourse::model()->findByPk($one_report_course_id);
            if ($reportModel->startyear >= 2024 && $achievementReportCourse && in_array($achievementReportCourse->td, array(6))) {
                $sampleProgerssReport = true;
            }
        }

        // 查找每个学生的报告
        $criteria = new CDbCriteria();
        $criteria->compare('t.calender', $yid);
        $criteria->compare('t.childid', $childids);
        $criteria->compare('t.courseid', $courseids);
        $criteria->compare('t.reportid', $semester);
//        var_dump($yid,$courseids,$semester);die;
        $reportModels = AchievementReportChildCourse::model()->findAll($criteria);
        $reports = array();
        foreach ($reportModels as $item){
            $reportModelsByChild[$item['childid']][$item['timetable_records_id']] = $item['timetable_records_code'];
        }
        foreach ($childCourses as $child_id=>$item){
            foreach ($item as $k=>$v){
                #去掉 当前期没有成绩且状态为99的课程
                if(!empty($category_data_by_status[$child_id][99])
                    && in_array($v['course_code'],$category_data_by_status[$child_id][99])
                    && !in_array($v['course_code'],$reportModelsByChild[$child_id])){
                    unset($childCourses[$child_id][$k]);
                }
            }
        }

        foreach ($reportModels as $item) {
            foreach ($item->childFraction as $fraction) {
                $reports[$item->childid][$item->timetable_records_code][] = ($fraction->courseScoresid)? $fraction->courseScoresid->fraction : "-";
            }
            if($reportModel->type == 1){
                $reports[$item->childid][$item->timetable_records_code]['teacher_message_cn'] = $item->teacher_message_en;
                // $reports[$item->childid][$item->timetable_records_code]['student_message_cn'] = $item->student_message_en;
            }
            if($reportModel->type == 1 && $reportModel->cycle == 4){
                $reports[$item->childid][$item->timetable_records_code]['oprion_value'] = ($childTotalArr && $childTotalArr[$item->childid][$item->courseid]) ? $childTotalArr[$item->childid][$item->courseid] : '-';
            }
        }
//        var_dump($reports);

        $classModel = IvyClass::model()->findByPk($classid);
        $data = array();
        foreach ($childData as $childid => $val){
            if (in_array($classModel->classtype, array('e11', 'e12'))) {
                $stand = array('-',
                    Yii::t('teaching','Comment from teacher'),
//                    Yii::t('teaching','Year-End Score')
                );
                $array = array(999999 => '-', 9999994=>'-', 9999996=>'-');
            }
            else {
                $stand = array('A', 'B', 'C', 'D', Yii::t('teaching','Comment from teacher'), Yii::t('teaching','Year-End Score'));
                $array = array(999999 => '-', 9999991 => '-', 9999992 => '-', 9999993=>'-', 9999994=>'-', 9999996=>'-');
            }
            if($val['cycle'] != 4 && $val['type'] == 1){
                if (in_array($classModel->classtype, array('e11', 'e12'))) {
                    $stand = array('-', Yii::t('teaching','Comment from teacher'));
                    $array = array(999999 => '-', 9999994=>'-');
                }
                else {
                    $stand = array('A', 'B', 'C', 'D', Yii::t('teaching','Comment from teacher'));
                    $array = array(999999 => '-', 9999991 => '-', 9999992 => '-', 9999993=>'-', 9999994=>'-');
                }
            }
            if($val['type'] == 2){
                if (in_array($classModel->classtype, array('e11', 'e12'))) {
                    $stand = array('-');
                    $array = array(999999 => '-');
                }
                else {
                    $stand = array('A', 'B', 'C', 'D');
                    $array = array(999999 => '-', 9999991 => '-', 9999992 => '-', 9999993=>'-');
                }
            }
            if($sampleProgerssReport){
                $stand = array(CommonUtils::autoLang('任务完成情况', 'Task Completion'), CommonUtils::autoLang('课堂参与度', 'Class Engagement and Participation'));
                
                $array = array(999999 => '-', 9999991 => '-');
            }
            $courseList = array();
            $data['sample_progerss'] = $sampleProgerssReport;
            $data['cycle'] = $val['cycle'];
            $data['class_type'] = $classModel->classtype;
            $data['stand'] = $stand;
            if($childCourses[$childid]){
                foreach ($childCourses[$childid] as $item){
                    $items = (isset($reports[$childid]) && isset($reports[$childid][$item['course_code']])) ? $reports[$childid][$item['course_code']] : $array ;
                    if($val['cycle'] == 4 && $val['type'] == 1) {
                        $final = ($finalData && $finalData[$childid][$item['course_code']]) ? $finalData[$childid][$item['course_code']] : array();
                        $courseList[] = array(
                            'title' => $item['title'],
                            'items' => $items,
                            'final' => $final
                        );
                    }else{
                        $courseList[] = array(
                            'title' => $item['title'],
                            'items' => $items,
                        );
                    }
                }
            };

            $data['item'][$childid] = array(
                'child_name' => $val['childname'],
                'child_photo' => $val['childPhoto'],
                'counselor' => isset($childReportsModel[$childid]) ? $childReportsModel[$childid]->counselor_message_en : '',
                'college_counseling' => isset($childReportsModel[$childid]) ? $childReportsModel[$childid]->college_counseling : '',
                'social_innovation' => isset($childReportsModel[$childid]) ? $childReportsModel[$childid]->social_innovation : '',
                'is_stat' => isset($childReportsModel[$childid]) ? $childReportsModel[$childid]->is_stat : 0,
                'is_cache' => isset($childReportsModel[$childid]) ? $childReportsModel[$childid]->is_cache : 0,
                'course_num' => isset($childCourses[$childid]) ? count($childCourses[$childid]) : 0,
                'courseList' => $courseList,
                'finalScoresData' => $finalScoresData,
            );
        }
       /* $dataa = array(
            'stand' => array('A', 'B', 'C', 'D', '教师评语', '学生评语', '总分'),
            'item' => array(
                2418 => array(
                    'child_name' => '孩子姓名',
                    'course_num' => 3,
                    'counselor' => '辅导员评语',
                    'is_star' => 1,
                    'courseList' => array(
                        0 => array(
                            'title' => '课程名称',
                            'items' => array(1,2,3,4,'教师评语', '学生评语', '8'),
                        ),
                        1 => array(
                            'title' => '课程名称',
                            'items' => array(1,2,3,4,'教师评语', '学生评语', '8'),
                        ),
                        2 => array(
                            'title' => '课程名称',
                            'items' => array(1,2,3,4,'教师评语', '学生评语', '8'),
                        )
                    ),
                ),
                2518 => array(
                    'child_name' => '孩子姓名',
                    'course_num' => 3,
                    'counselor' => '辅导员评语',
                    'is_star' => 0,
                    'courseList' => array(
                        0 => array(
                            'title' => '课程名称',
                            'items' => array(1,2,3,4,'教师评语', '学生评语', '8'),
                        ),
                        1 => array(
                            'title' => '课程名称',
                            'items' => array(1,2,3,4,'教师评语', '学生评语', '8'),
                        ),
                        2 => array(
                            'title' => '课程名称',
                            'items' => array(1,2,3,4,'教师评语', '学生评语', '8'),
                        )
                    ),
                ),
                2618 => array(
                    'child_name' => '孩子姓名',
                    'course_num' => 2,
                    'counselor' => '辅导员评语',
                    'is_star' => 0,
                    'courseList' => array(
                        0 => array(
                            'title' => '课程名称',
                            'items' => array(),
                        ),
                        1 => array(
                            'title' => '课程名称',
                            'items' => array(1,2,3,4,'教师评语', '学生评语', '8'),
                        ),
                    ),
                ),
                2718 => array(
                    'child_name' => '孩子姓名',
                    'course_num' => 2,
                    'counselor' => '辅导员评语',
                    'is_star' => 0,
                    'courseList' => array(

                    ),
                ),
            )
        );*/

        echo json_encode($data);
    }



    // 上线下线功能
    public function actionSaveReportMessage()
    {
        if(Yii::app()->request->isAjaxRequest && Yii::app()->request->isPostRequest){
            Yii::import('common.models.reportCards.*');

            $childid = Yii::app()->request->getPost('childid');
            $semester = Yii::app()->request->getPost('semester');
            $yid = Yii::app()->request->getPost('yid');
            $status = Yii::app()->request->getPost('status');

            if($childid && $semester && $yid){
                $reportModel = AchievementReport::model()->findByPk($semester);
                $criteria = new CDbCriteria();
                $criteria->compare('calender', $yid);
                $criteria->compare('report_id', $semester);
                $criteria->compare('childid', $childid);
                $model = AchievementReportChild::model()->find($criteria);

                if(!$model){
                    $childModel = ChildProfileBasic::model()->findByPk($childid);
                    $model = new AchievementReportChild();
                    $model->calender = $yid;
                    $model->schoolid = $this->branchId;
                    $model->report_id = $semester;
                    $model->childid = $childid;
                    $model->classid = $childModel->classid;
                    $model->uid = $this->staff->uid;
                    $model->create_time = time();
                    $model->update_time = time();
                }
                $model->evaluate_number = $reportModel->getStudentsTotalDays();
                $model->absent_days = $reportModel->getStudentsObsentDays($childid);
                $model->is_stat = ($status == 'online') ? 1 : 0;
                $model->is_cache = 0;
                if($model->save()){
                    $this->addMessage('data', $model->is_stat);
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message', 'Data Saved!'));
                }
                else{
                    $this->addMessage('state', 'fail');
                    $err = current($model->getErrors());
                    $this->addMessage('message', $err[0]);
                }
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('teaching', '参数错误！'));
            }
            $this->showMessage();
        }
    }

    //预览中学报告
    public function actionNewPreviewReportDs()
    {
        Yii::import('common.components.teaching.*');
        $childid = Yii::app()->request->getParam('childid', '');
        $report_id = Yii::app()->request->getParam('report_id', '');
        $classid = Yii::app()->request->getParam('classid', '');
        $yid = Yii::app()->request->getParam('yid', '');

        $criteria = new CDbCriteria();
        $criteria->compare('calender', $yid);
        $criteria->compare('report_id', $report_id);
        $criteria->compare('childid', $childid);
        $model = AchievementReportChild::model()->find($criteria);
        $params['childid'] = $childid;
        $params['id'] = $report_id;
        $params['classid'] = $classid;
        $params['yid'] = $yid;
        $params['courseid'] = $model->id;
        if($params){
            $reportHtml =  ContentFetcher::getReportOfDs(
                $params,
                $this->branchId,
                1
            );
            echo $reportHtml;
        }
    }

    //批量上下线  暂时无用
    public function actionSetNewAllDs()
    {
        $classid = Yii::app()->request->getParam('classid_global', ''); //班级ID
        $yid = Yii::app()->request->getParam('yid', '');  //校历ID
        $type = Yii::app()->request->getParam('type', ''); //上线还是下线
        $semester = Yii::app()->request->getParam('semester', ''); //报告ID

        $reportModel = AchievementReport::model()->findByPk($semester);

        $criteria = new CDbCriteria();
        $criteria->compare('classid', $classid);
        $criteria->compare('status', array(10,20));
        $chuldModel = ChildProfileBasic::model()->findAll($criteria);

        if($chuldModel){
            $onlines = array();
            foreach ($chuldModel as $val){
                $criteria = new CDbCriteria();
                $criteria->compare('calender', $yid);
                $criteria->compare('report_id', $semester);
                $criteria->compare('classid', $classid);
                $criteria->compare('childid', $val->childid);
                $reportChild = AchievementReportChild::model()->find($criteria);
                if($reportChild){
                    $reportChild->evaluate_number = $reportModel->getStudentsTotalDays();
                    $reportChild->absent_days = $reportModel->getStudentsObsentDays($val->childid);
                    $reportChild->is_stat = ($type == "online") ? 1 : 0 ;
                    $reportChild->save();
                }else{
                    $model = new AchievementReportChild();
                    $model->evaluate_number = $reportModel->getStudentsTotalDays();
                    $model->absent_days = $reportModel->getStudentsObsentDays($val->childid);
                    $model->calender = $yid;
                    $model->schoolid = $this->branchId;
                    $model->report_id = $semester;
                    $model->childid = $val->childid;
                    $model->classid = $classid;
                    $model->uid = $this->staff->uid;
                    $model->is_stat = ($type == "online") ? 1 : 0 ;
                    $model->create_time = time();
                    $model->update_time = time();
                    $model->save();
                }
                $onlines[] = $val->childid;
            }

            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('teaching', '有:successNum条保存成功', array(':successNum'=>count($chuldModel))));
            $this->addMessage('data', $onlines);
            $this->showMessage();
        }
    }

    // 暂时无用
    public function saveCounselor($childid,$semester,$yid,$entext = '')
    {
        if (!$childid) {
            $this->addMessage('message', '孩子ID不能为空');
            $this->showMessage();
        }
        $childModel = ChildProfileBasic::model()->findByPk($childid);
        if (!$childModel) {
            $this->addMessage('message', '孩子不存在');
            $this->showMessage();
        }

        $report = AchievementReport::model()->findByPk($semester);

        $criteria = new CDbCriteria();
        $criteria->compare('calender', $yid);
        $criteria->compare('report_id', $semester);
        $criteria->compare('childid', $childid);
        $model = AchievementReportChild::model()->find($criteria);
        if (!$model) {
            $model = new AchievementReportChild();
            $model->is_stat = 0;
            $model->create_time = time();
        }
        // 评估天数
        $model->evaluate_number = $report->getStudentsTotalDays();
        // 缺勤天数
        $model->absent_days = $report->getStudentsObsentDays($childid);

        $model->calender = $yid;
        $model->schoolid = $this->branchId;
        $model->report_id = $semester;
        $model->childid = $childid;
        $model->classid = $childModel->classid;
        $model->counselor_message_en = $entext;
        $model->uid = $this->staff->uid;
        $model->update_time = time();
        if (!$model->save()) {
            return false;
        }
        return true;
    }

}
