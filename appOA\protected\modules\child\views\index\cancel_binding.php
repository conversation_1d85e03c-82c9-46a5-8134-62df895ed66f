<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'draw-form',
    'action'=>$this->createUrl('/child/index/processCancelBinding',array('childid'=>$bindingDiscount->childid,'bindingId'=>$bindingDiscount->id)),
	'enableAjaxValidation'=>false,
	'htmlOptions'=>array('class'=>'J_ajaxForm'),
));
?>
<?php if (!empty($model)):?>
<div class="pop_cont pop_table" style="min-height: 80px;height:auto;">
    <div class="tips_light">系统检查到该折扣已经开出了下面的账单，选中的账单将在取消折扣绑定后自动作废。</div>
    <table width="100%">
        <colgroup>
            <col class="th" />
            <col />
        </colgroup>
        <tbody>
            <?php foreach ($model as $val):?>
            
                    <tr>
                        <td>
                            <?php
                            if ($val->status == Invoice::STATS_PARTIALLY_PAID):
                                $disabled = true;
                                $checked = false;
                                $extTit = ' (部分付清,不能作废)';
                            else:
                                $checked = true;
                                $disabled = false;
                            endif;
                            echo $form->checkBox($val, 'invoice_id', array('name'=>'Invoice[invoice_id]['.$val->invoice_id.']', 'disabled'=>$disabled,'value'=>$val->invoice_id,'checked'=>$checked));
                            echo $val->title.$extTit;
                            ?>
                        </td>
                    </tr>
               
            <?php endforeach;?>
        </tbody>
    </table>
</div>
<?php else: ?>
<div class="pop_cont pop_table" style="height:80px;">
    <h2>确定要取消绑定吗？</h2>
</div>
<?php endif;?>
<div class="pop_bottom">
    <button type="button" class="btn fr" id="J_dialog_close"><?php echo Yii::t('global','Cancel');?></button>
    <button class="btn fr btn_submit mr10 J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
</div>
<?php $this->endWidget(); ?>
<script>
    function callback(data)
    {
        parent.window.removeElement(data);
    }
</script>