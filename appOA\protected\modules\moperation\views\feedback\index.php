<?php 
$purchaseProducts = Yii::app()->request->getParam('PurchaseProducts','');
 ?>
 <div class="container-fluid">
 <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','HQ Operations'), array('default/index'));?></li>
        <li><?php echo CHtml::link(Yii::t('site','Support'), array('default/index'));?></li>
        <li class="active"><?php echo Yii::t('site','评论列表') ;?></li>
    </ol>
 <div class="panel-default">
			<div class="panel-body">
				<?php
				$this->widget('ext.ivyCGridView.BsCGridView', array(
					'id'=>'comment',
					'afterAjaxUpdate' => 'js:function(){head.Util.ajaxDel()}',
					'dataProvider' => $model->search(),
					'colgroups' => array(
						array(
							'class' => 'form-control',//复选框 
							"colwidth" => array(100),
						)
					),
					'columns' => array(
						'id',
						'content',
						array(
							'name' => 'uid',
							'value' =>  '$data->userinfo->getName()',
						),
						array(
							'name' => '时间',
							'value' => 'CommonUtils::formatDateTime($data->times, "medium", "short")',
							),
						array(
							'name' => '操作',
							'value' => array($this,'showBtn'),
						),
					),
				)); ?>
			</div>
</div>
</div>
<script>
function cbFeedbacks() {
 
	   $.fn.yiiGridView.update('comment');
    }
</script>