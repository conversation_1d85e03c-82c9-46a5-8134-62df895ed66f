<?php

/**
 * This is the model class for table "ivy_workflow_contract_apply".
 *
 * The followings are the available columns in table 'ivy_workflow_contract_apply':
 * @property integer $id
 * @property string $school_id
 * @property string $title
 * @property integer $category
 * @property string $party_a
 * @property string $party_b
 * @property integer $start_date
 * @property integer $end_date
 * @property integer $vendor_id
 * @property string $bank_user
 * @property string $bank_account
 * @property string $bank_name
 * @property integer $budget
 * @property string $price_total
 * @property integer $state
 * @property integer $payment_flow_id
 * @property integer $created_by
 * @property integer $created_at
 * @property integer $updated_by
 * @property integer $updated_at
 * @property integer $cycle
 */
class ContractApply extends CActiveRecord
{
	
	const CANCEL = -2;
	const REFUSE = -1;
	const INPROGRESS = 0;
	const AGREE = 1;
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_workflow_contract_apply';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('school_id, title, party_a, party_b, bank_user, bank_account, bank_name, created_by, created_at, updated_by, updated_at', 'required'),
			array('category, start_date, end_date, vendor_id, budget, state, payment_flow_id, created_by, created_at, updated_by, updated_at, cycle', 'numerical', 'integerOnly'=>true),
			array('school_id, title, party_a, party_b, bank_user, bank_account, bank_name', 'length', 'max'=>255),
			array('price_total', 'length', 'max'=>20),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, school_id, title, category, party_a, party_b, start_date, end_date, vendor_id, bank_user, bank_account, bank_name, budget, price_total, state, payment_flow_id, created_by, created_at, updated_by, updated_at, cycle', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'school_id' => 'School',
			'title' => 'Title',
			'category' => 'Category',
			'party_a' => 'Party A',
			'party_b' => 'Party B',
			'start_date' => 'Start Date',
			'end_date' => 'End Date',
			'vendor_id' => 'Vendor',
			'bank_user' => 'Bank User',
			'bank_account' => 'Bank Account',
			'bank_name' => 'Bank Name',
			'budget' => 'Budget',
			'price_total' => 'Price Total',
			'state' => 'State',
			'payment_flow_id' => 'Payment Flow',
			'created_by' => 'Created By',
			'created_at' => 'Created At',
			'updated_by' => 'Updated By',
			'updated_at' => 'Updated At',
			'cycle' => 'Cycle',
			'desc' => 'Desc',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('school_id',$this->school_id,true);
		$criteria->compare('title',$this->title,true);
		$criteria->compare('category',$this->category);
		$criteria->compare('party_a',$this->party_a,true);
		$criteria->compare('party_b',$this->party_b,true);
		$criteria->compare('start_date',$this->start_date);
		$criteria->compare('end_date',$this->end_date);
		$criteria->compare('vendor_id',$this->vendor_id);
		$criteria->compare('bank_user',$this->bank_user,true);
		$criteria->compare('bank_account',$this->bank_account,true);
		$criteria->compare('bank_name',$this->bank_name,true);
		$criteria->compare('budget',$this->budget);
		$criteria->compare('price_total',$this->price_total,true);
		$criteria->compare('state',$this->state);
		$criteria->compare('payment_flow_id',$this->payment_flow_id);
		$criteria->compare('created_by',$this->created_by);
		$criteria->compare('created_at',$this->created_at);
		$criteria->compare('updated_by',$this->updated_by);
		$criteria->compare('updated_at',$this->updated_at);
		$criteria->compare('cycle',$this->cycle);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return ContractApply the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	public static function getPaymentList($contractId)
	{
		// 查找已付金额
		Yii::import('common.models.workflow.payment.PaymentApply');

		$crit = new CDbCriteria();
		$crit->compare('contract_id', $contractId);
		$crit->compare('state', array(0, 1));
		$paymentModels = PaymentApply::model()->findAll($crit);
		$paymentTotal = 0.00;
		$paymentItems = array();
		foreach ($paymentModels as $paymentModel) {
			// 0 申请中，1 待付款，2已付款
			$state = 0;
			if ($paymentModel->state == 1) {
				$state = $paymentModel->payment_user > 0 ? 2 : 1;
			}
			$paymentItems[] = array(
				'id' => $paymentModel->id,
				'title' => $paymentModel->title,
				'state' =>  $state,
				'apply_date' => date('Y-m-d', $paymentModel->created_at),
				'date' => $state == 2 ? date('Y-m-d', $paymentModel->payment_time) : '',
				'amount' => round($paymentModel->price_total / 100, 2),
			);
			$paymentTotal += $paymentModel->price_total;
		}
		if ($paymentTotal > 0) {
			$paymentTotal = round($paymentTotal / 100, 2);
		}
		return array(
			'total' => $paymentTotal,
			'stateList' => array(0 => '申请中', 1 => '待付款', 2 => '已付款'),
			'items' => $paymentItems,
		);
	}

	public static function getCategoryList()
    {
        return array(
            2 => '框架合同',
            3 => '采购合同',
            1 => '装修合同',
            4 => '其他合同',
        );
    }

    public static function getCycleList()
    {
        return array(
            1 => '单次',
            2 => '多次',
            3 => '按月',
            4 => '按年',
            5 => '按期',
        );
    }

	public function categoryTitle() {
		$list = self::getCategoryList();
		return $list[$this->category];
	}
	public function cycleTitle() {
		$list = self::getCycleList();
		return $list[$this->cycle];
	}

	function getContractInfoLink() {
		// 查找对应的工作流
		$definationModels = WorkflowDefination::model()->findAllByAttributes(array('defination_handle' => array('Contract', 'ContractHq')));
		$definationIds = array();
		foreach ($definationModels as $definationModel) {
			$definationIds[] = $definationModel->defination_id;
		}
		$operationModel = WorkflowOperation::model()->findByAttributes(array('operation_object_id' => $this->id, 'defination_id' => $definationIds));
		return CHtml::link(
			$this->title,
			Yii::app()->createUrl("//workflow/entrance/index", array(
				"definationId" => $operationModel->defination_id,
				"nodeId" => $operationModel->current_node_index,
				"operationId" => $operationModel->id,
				"branchId" => $operationModel->branchid,
				"action" => "show",
				"showContract" => "1",
			)),
			array("class" => "J_ajax btn btn-primary btn-xs J_ajax", "data-method" => "get")
		);
	}
}
