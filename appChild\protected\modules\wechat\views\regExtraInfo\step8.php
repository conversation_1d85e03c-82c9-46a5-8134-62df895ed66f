<style>
 #signature-div {
        width: 100%;
        height: 100%;
        display: none;
        position: fixed;
        left: 0;
        top: 0;
        z-index: 999;
        background-color: white;
    }
    
    #signature-div p {
        position: absolute;
        bottom: 0;
        left: 50%;
        margin-left: -91px
    }
    
    .signature-pad {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: white;
    }
    
    .signCan {
        position: relative;
        -webkit-box-flex: 1;
        -ms-flex: 1;
        flex: 1;
        border: 1px solid #f4f4f4;
    }
    
    .signBody {
        position: relative;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
        font-size: 10px;
        width: 100%;
        height: 100%;
        max-height: 300px;
        background-color: #fff;
        border-radius: 4px;
        padding: 16px;
    }
    #signature-data img{
        width:100px
    }
    .signText{
        padding:16px;
    }
</style>
<?php echo $this->renderPartial('steps_header', array('currentStep'=>8)); ?>
<div class="container-fluid">
    <h5 class="htitle"><span class="fas fa-file-signature"></span> <?php echo $this->getStepTitle($this->step); ?></h5>
    <?php echo $this->renderPartial('_nouser_header'); ?>
    <div class="edit-content">
    <?php 
        $content = RegConfig::getContent($this->regStudent->reg_id, $this->step);
        if ($content) {
            echo CommonUtils::autoLang(base64_decode($content['cn']['safe']), base64_decode($content['en']['safe']));
        }
     ?>
    </div>
    <div>
        <!-- 英文名 -->
        <?php $form = $this->beginWidget('CActiveForm', array(
            'id' => 'sep8-form',
            'enableAjaxValidation' => false,
            'htmlOptions' => array(
                // 'class'=>'J_ajaxForm form-horizontal',
                'role' => 'form',
            ),
        )); ?>
        <input type="text" name="RegStep8Form[agree]" value="1" hidden="hidden">
        <button class="btn btn-info btn-sm" type="button" id="signature-save">
                <span class="fa fa-pen"></span> <?php echo Yii::t('reg', "Agree & sign") ?>
        </button>
        <div class="wrapper" id="signature-div">
            <div class="signBody">
                <div class="signCan">
                    <canvas id="signature-pad" class="signature-pad"></canvas>
                </div>
            </div>
           <div class="signText"><?php echo Yii::t('reg', "Rotate your phone horizontal for better view to sign") ?></div>
            <canvas id = "watermark" width = "100%"  height = "150px" style="display:none;"></canvas>
            <p>
                <button type="button" class="btn btn-primary exit"><?php echo Yii::t('bind', "Confirm") ?></button>
                <button type="button" class="btn btn-outline-primary clear"><?php echo Yii::t('reg', "Rewrite") ?></button>
                <button type="button" class="btn btn-outline-dark dropOut"><?php echo Yii::t('bind', "Cancel") ?></button>
            </p>
        </div>
        <p id="signature-data">
            <?php if($formModel8->filedata): ?>
                <img src="<?php echo Mims::OssUploadUrl($formModel8->filedata) ?>" >
            <?php endif; ?>
        </p>
        <input type="text" id="filedata" name="RegStep8Form[filedata]" value="<?php echo $formModel8->filedata ?$formModel8->filedata : ''?>" hidden="hidden">
        <div class="mt-3">
            <button type="submit" class="btn btn-primary btn-lg btn-block examine"><?php echo Yii::t('reg', 'Submit') ?></button>
            <p class="text-black-50 mt-3 text-center  examine_status">

            </p>
        </div>
        <div class="mt-3" style="height: 10px"></div>
        <?php $this->endWidget(); ?>
    </div>
    <script>
    $('.exit').click(function() {
        $('html').css({
            'overflow': 'initial',
            'position': 'initial'
        });
        $('html,body').scrollTop(scrollTops);
        var canvas = document.getElementById('signature-pad');
        var data = canvas.toDataURL("image/png");
        // $("#signature-data").attr('src', data);
        $("#signature-data").html('<img src=' + data + ' >')
        $("#filedata").val(data);
        $("#signature-data").show();
        $('#signature-div').hide();
        var width = canvas.width / 8
        var height = canvas.height / 8
        $('#signature-data img').height(height)
        $('#signature-data img').width(width)
    })
    // 重写
    $('.clear').click(function() {
        signatureInit();
    })
    var canvas = document.getElementById('signature-pad');
    $('.dropOut').click(function(){
        $('html').css({
            'overflow': 'initial',
            'position': 'initial'
        });
        $('html,body').scrollTop(scrollTops);
        $('#signature-div').hide();
        clea = canvas.getContext("2d");
        clea.clearRect(0,0,500,500);
    })
    // 点击全名书写
    $('#signature-save').click(function() {
        //canvas全屏
        $('#signature-div').show();
        signatureInit();
    });
    // 初始化画板
    var scrollTops;
    function signatureInit() {
        scrollTops = $(window).scrollTop();
        $('html').css({
            'overflow': 'hidden',
            'position': 'fixed'
        })
        ratio = Math.max(window.devicePixelRatio || 1, 1);
        canvas.width = canvas.offsetWidth * ratio;
        canvas.height = canvas.offsetHeight * ratio;
        canvas.getContext("2d").scale(ratio, ratio);
        var signaturePad = new SignaturePad(canvas);
        watermark(canvas);
    }

    function hengshuping() {
        if(window.orientation == 180 || window.orientation == 0) {
            if($('.wrapper').is(':visible')){
                clea = canvas.getContext("2d");
                clea.clearRect(0,0,500,500);
                setTimeout("signatureInit()",200);
            }
        }
        if(window.orientation == 90 || window.orientation == -90) {
            if($('.wrapper').is(':visible')){
                clea = canvas.getContext("2d");
                clea.clearRect(0,0,500,500);
                setTimeout("signatureInit()",200);
            }
        }
    }
    window.addEventListener("onorientationchange" in window ? "orientationchange" : "resize", hengshuping, false);
    // 绘制水印
    function watermark(canvas) {
        var date = new Date();
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        var strDate = date.getDate();
        if(month >= 1 && month <= 9) {
            month = "0" + month;
        }
        if(strDate >= 0 && strDate <= 9) {
            strDate = "0" + strDate;
        }
        var currentdate = year + month + strDate
        var cw = $('#watermark')[0];   
        cw.width = $(document).width();
        var ctx = cw.getContext("2d");   //返回一个用于在画布上绘图的环境
        
       
        ctx.fillStyle = "rgba(100,100,100,0.1)";
        if(window.orientation == 90 || window.orientation == -90) {
            ctx.font="30px 黑体";  
            ctx.rotate(-10*Math.PI/180);
            $('.signText').hide()
            ctx.fillText("<?php echo Yii::t('reg', "School safety agreement") ?>" + currentdate + "",100, 130);
        } else if(window.orientation == 0 || window.orientation == 180) {
            ctx.font="20px 黑体";  
            ctx.rotate(-18*Math.PI/180);
             $('.signText').show()
           ctx.fillText("<?php echo Yii::t('reg', "School safety agreement") ?>" + currentdate + "", 0, 130);
        }
        
        ctx.rotate('20*Math.PI/180');  //坐标系还原
        var crw =canvas;  
        ctxr = canvas.getContext("2d");
        ctxr.width = $(window).height();
        ctxr.clearRect(0,0,500,500);  //清除整个画布 
        var pat = ctxr.createPattern(cw, "repeat");    //在指定的方向上重复指定的元素  
        ctxr.fillStyle = pat;  
        ctxr.fillRect(0, 0, crw.width, crw.height);
    }
    </script>