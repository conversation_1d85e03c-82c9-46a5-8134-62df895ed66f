<?php
$hour = array('00' => '00', '01' => '01', '02' => '02', '03' => '03', '04' => '04', '05' => '05', '06' => '06', '07' => '07','08' => '08', '09' => '09', 10 => 10, 11 => 11, 12 => 12, 13 => 13, 14 => 14, 15 => 15, 16 => 16, 17 => 17, 18 => 18, 19 => 19, 20 => 20, 21 => 21, 22 => 22, 23 => 23);
$minute = array('00' => '00', '05' => '05', 10 => 10, 15 => 15, 20 => 20, 25 => 25, 30 => 30, 35 => 35, 40 => 40, 45 => 45, 50 => 50, 55 => 55);
$cYear = date("Y", time());
$cMonth = date("n", time());
if ($cMonth > 7) {
    $tmp = $cYear + 1;
    $startyearList = array($cYear => $cYear, $tmp => $tmp);
} else {
    $tmp = $cYear - 1;
    $startyearList = array($tmp => $tmp, $cYear => $cYear); 
}
if ($model->startyear) {
    $startyearList[$model->startyear] = $model->startyear;
}
?>
<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
    <h4 class="modal-title" id="exampleModalLabel"><?php echo $model->id?'更新：':'添加：'; ?></h4>
</div>
<?php $form=$this->beginWidget('CActiveForm', array(
    'id'=>'courseGroup-form',
    'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
)); ?>

<div class="modal-body">

    <div class="alert alert-info" role="alert">
        <p>课程组标题规范，注明明确的学年信息和时间信息，该标题将为默认的账单标题，如：</p>
        <br/>
        <ul>
            <li>2016-2017学年秋季课后课</li>
            <li>2016-2017学年第二季课后课</li>
        </ul>
    </div>

    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'startyear'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->dropDownList($model,'startyear', $startyearList, array('class'=>'form-control'));?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'season'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->dropDownList($model,'season', $seasonArr, array('class'=>'form-control'));?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'title_cn'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'title_cn',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'title_en'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'title_en',array('maxlength'=>255,'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'open_date_start'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'open_date_start',array('maxlength'=>255,'class'=>'form-control datepicker')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'open_date_end'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'open_date_end',array('maxlength'=>255,'class'=>'form-control datepicker')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'show_from'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'show_from',array('class'=>'form-control datepicker')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'show_end'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model,'show_end',array('class'=>'form-control datepicker')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'buy_from'); ?></label>
        <div class="col-xs-5">
            <?php echo $form->textField($model,'buy_from',array('class'=>'form-control datepicker')); ?>
        </div>
        <div class="col-xs-3">
            <div class="form-inline" id="">
                <?php echo CHtml::dropDownList('buyFrom[hour]', $buyFromStatr['hour'], $hour, array('class' => "form-control mr10", 'empty' => '时'));?>
                <?php echo CHtml::dropDownList('buyFrom[minute]', $buyFromStatr['minute'], $minute, array('class' => "form-control mr10", 'empty' => '分'));?>

            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'buy_end'); ?></label>
        <div class="col-xs-5">
            <?php echo $form->textField($model,'buy_end',array('class'=>'form-control datepicker')); ?>
        </div>
        <div class="col-xs-3">
            <div class="form-inline" id="">
                <?php echo CHtml::dropDownList('buyEnd[hour]', $buyFromEnd['hour'], $hour, array('class' => "form-control mr10", 'empty' => '时'));?>
                <?php echo CHtml::dropDownList('buyEnd[minute]', $buyFromEnd['minute'], $minute, array('class' => "form-control mr10", 'empty' => '分'));?>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'progress'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->checkBox($model,'progress'); ?> 选中表示家长在微信端购买时可看到课程报名进度
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'follower'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->checkBox($model,'follower'); ?> 选中表示家长可以在满班之后使用心愿单功能，校方可根据心愿单登记数量决定是否开新班
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'datecheck'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->checkBox($model,'datecheck'); ?> 选中表示家长购买时检查同时段是否存在已购买课程
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'program_type'); ?></label>
        <div class="col-xs-9">
            <?php
            if($model->id){
                echo $form->dropDownList($model,'program_type', $statusArr, array('class'=>'form-control', 'disabled'=>'disabled', 'empty' => Yii::t('asa', '请选择')));
            }else{
                echo $form->dropDownList($model,'program_type', $statusArr, array('class'=>'form-control', 'empty' => Yii::t('asa', '请选择')));
            }
            ?>
        </div>
    </div>

    <?php if($model->id): ?>
        <div class="form-group">
            <label class="col-sm-2 control-label required"><?php echo Yii::t('asa', '退费截至') ?></label>
            <div class="col-xs-6">
                <?php echo $form->radioButtonList($model, "refundStatus", array(1 => '按时间: 截止到某天', 2 => '按课时: (在某节课开始之前)'), array('template' => "<span class='classTimeSelect'> {input} {label} </span>", "separator" => "&nbsp;")); ?>
            </div>

            <div class="col-xs-3">
                <?php echo $form->textField($model, 'refundTime', array('class' => 'form-control datepicker','style' => 'display:none','disabled' => 'disabled', 'id'=>"refundClassIndexTime")); ?>
            </div>

            <div class="col-xs-3">
                <?php echo $form->dropDownList($model, 'refundTime', AsaCourseGroup::getRefund(), array('class' => 'form-control','style' => 'display:none','disabled' => 'disabled')); ?>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label required"></label>
            <div class="col-xs-9">
                <small>在这里修改时间，整个课程组下的所有课程退费时间都会修改,要先加课程才会修改</small>
            </div>
        </div>
    <?php endif;?>

    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'status'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->dropDownList($model,'status',array(1=>'有效', 0=>'无效'),array('class'=>'form-control')); ?>
        </div>
    </div>
</div>
<div class="modal-footer">
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
    <button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
</div>

<?php $this->endWidget(); ?>

<script>
    $('.datepicker').datepicker({
        changeMonth: true,
        changeYear: true,
        dateFormat:'yy-mm-dd'
    });
    $('.classTimeSelect').on('click',':radio',function(){
        if($(this).val() == 1){
            $('#refundClassIndexTime').removeProp('disabled').show();
            $('#AsaCourseGroup_refundTime').prop('disabled','disabled').hide();
        }else {
            $('#refundClassIndexTime').prop('disabled','disabled').hide();
            $('#AsaCourseGroup_refundTime').removeProp('disabled').show();
        }
    });
    if($('#AsaCourse_refundClassStatus').find(':radio:checked').val() == 1){
        $('#refundClassIndexTime').removeProp('disabled').show();
        $('#AsaCourse_refundClassIndex').prop('disabled','disabled').hide();
    }else if ($('#AsaCourse_refundClassStatus').find(':radio:checked').val() == 2){
        $('#refundClassIndexTime').prop('disabled','disabled').hide();
        $('#AsaCourse_refundClassIndex').removeProp('disabled').show();
    }
    function cbGroup() {
        var groupId = '<?php echo $model->id; ?>';
        $('#modal').modal('hide');
        window.location.reload(true);
    }
</script>
