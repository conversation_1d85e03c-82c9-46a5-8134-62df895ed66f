<?php

/**
 * 将 widget 的 action 请求写在widget结构之内便于管理
 * 参考 http://www.yiiframework.com/wiki/146/how-to-use-a-widget-as-an-action-provider/(没有用这个啊 看 3713 )
 * why do you directly config the actions function of controller oh
 * <AUTHOR>
 * @date 2012-08-02
 */
class weatherAction extends CAction{
	/*
	 * 配置 显示 空气质量的城市
	*/
	protected static $showAqCities = array('Beijing');

	public function run(){


		if(Yii::app()->request->isAjaxRequest){
			$json_data['flag'] = 'failed';

			Yii::import('ext.weather.include.WeatherConfig');
			Yii::import('ext.weather.class.*');
			Yii::import('ext.weather.Weather');

			$wc = new WeatherConfig();

			$cityName = Yii::app()->getRequest()->getPost("cityName",'Beijing');
			$weatherImpl = new WeatherImpl($cityName);
			$wi = $weatherImpl->getWeather($wc->getWeatherApiForCn(),$wc->getWeatherApiForEn());
			if(!empty($wi)){
				$json_data['flag'] = 'success';
				$aqiView = Yii::app()->language == 'en_us' ? $wc->getWeatherApiForEn().'View' : $wc->getWeatherApiForCn().'View';

				$aq = null;
				if(in_array($cityName, $wc->getShowAqCities())){
					$aq = $weatherImpl->getAirQualityInfo();
				}

				$weather = new Weather();
				$json_data['weatherInfo'] = $weather->render($aqiView,array('wi'=>$wi,'aq'=>$aq),true);
			}

			echo CJSON::encode($json_data);
		}
	}

}