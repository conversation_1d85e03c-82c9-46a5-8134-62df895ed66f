function ubb2html(c){function g(b){return null!=b&&""!=b?!isNaN(b):!1}var b,l=[],e=0,m="10px,13px,16px,18px,24px,32px,48px".split(",");b=(""+c).replace(/[<>&"]/g,function(b){return{"<":"&lt;",">":"&gt;","&":"&amp;",'"':"&quot;"}[b]});b=b.replace(/\r?\n/g,"<br />");b=b.replace(/\[code\s*(?:=\s*([^\]]+?))?\]([\s\S]*?)\[\/code\]/ig,function(b){e++;l[e]=b;return"[\tubbcodeplace_"+e+"\t]"});b=b.replace(/\[(\/?)(b|u|i|s|sup|sub)\]/ig,"<$1$2>");b=b.replace(/\[color\s*=\s*([^\]"]+?)(?:"[^\]]*?)?\s*\]/ig,
'<font color="$1">');b=b.replace(/\[font\s*=\s*([^\]"]+?)(?:"[^\]]*?)?\s*\]/ig,'<font face="$1">');b=b.replace(/\[\/(color|font)\]/ig,"</font>");b=b.replace(/\[size\s*=\s*([^\]"]+?)(?:"[^\]]*?)?\s*\]/ig,function(b,a){a.match(/^\d+$/)&&(a=m[a-1]);return'<span style="font-size:'+a+';">'});b=b.replace(/\[back\s*=\s*([^\]"]+?)(?:"[^\]]*?)?\s*\]/ig,'<span style="background-color:$1;">');b=b.replace(/\[\/(size|back)\]/ig,"</span>");for(c=0;3>c;c++)b=b.replace(/\[align\s*=\s*([^\]"]+?)(?:"[^\]]*?)?\s*\](((?!\[align(?:\s+[^\]]+)?\])[\s\S])*?)\[\/align\]/ig,
'<p align="$1">$2</p>');b=b.replace(/\[img\]\s*(((?!")[\s\S])+?)(?:"[\s\S]*?)?\s*\[\/img\]/ig,'<img src="$1" alt="" />');b=b.replace(/\[img\s*=([^,\]]*)(?:\s*,\s*(\d*%?)\s*,\s*(\d*%?)\s*)?(?:,?\s*(\w+))?\s*\]\s*(((?!")[\s\S])+?)(?:"[\s\S]*)?\s*\[\/img\]/ig,function(b,a,c,j,e,q){b='<img src="'+q+'" alt="'+a+'"';e=e?e:!g(c)?c:"";g(c)&&(b+=' width="'+c+'"');g(j)&&(b+=' height="'+j+'"');e&&(b+=' align="'+e+'"');return b+" />"});b=b.replace(/\[emot\s*=\s*([^\]"]+?)(?:"[^\]]*?)?\s*\/\]/ig,'<img emot="$1" />');
b=b.replace(/\[url\]\s*(((?!")[\s\S])*?)(?:"[\s\S]*?)?\s*\[\/url\]/ig,'<a href="$1">$1</a>');b=b.replace(/\[url\s*=\s*([^\]"]+?)(?:"[^\]]*?)?\s*\]\s*([\s\S]*?)\s*\[\/url\]/ig,'<a href="$1">$2</a>');b=b.replace(/\[email\]\s*(((?!")[\s\S])+?)(?:"[\s\S]*?)?\s*\[\/email\]/ig,'<a href="mailto:$1">$1</a>');b=b.replace(/\[email\s*=\s*([^\]"]+?)(?:"[^\]]*?)?\s*\]\s*([\s\S]+?)\s*\[\/email\]/ig,'<a href="mailto:$1">$2</a>');b=b.replace(/\[quote\]/ig,"<blockquote>");b=b.replace(/\[\/quote\]/ig,"</blockquote>");
b=b.replace(/\[flash\s*(?:=\s*(\d+)\s*,\s*(\d+)\s*)?\]\s*(((?!")[\s\S])+?)(?:"[\s\S]*?)?\s*\[\/flash\]/ig,function(b,a,c,e){a||(a=480);c||(c=400);return'<embed type="application/x-shockwave-flash" src="'+e+'" wmode="opaque" quality="high" bgcolor="#ffffff" menu="false" play="true" loop="true" width="'+a+'" height="'+c+'"/>'});b=b.replace(/\[media\s*(?:=\s*(\d+)\s*,\s*(\d+)\s*(?:,\s*(\d+)\s*)?)?\]\s*(((?!")[\s\S])+?)(?:"[\s\S]*?)?\s*\[\/media\]/ig,function(b,a,c,e,g){a||(a=480);c||(c=400);return'<embed type="application/x-mplayer2" src="'+
g+'" enablecontextmenu="false" autostart="'+("1"==e?"true":"false")+'" width="'+a+'" height="'+c+'"/>'});b=b.replace(/\[table\s*(?:=\s*(\d{1,4}%?)\s*(?:,\s*([^\]"]+)(?:"[^\]]*?)?)?)?\s*\]/ig,function(b,a,c){b="<table";a&&(b+=' width="'+a+'"');c&&(b+=' bgcolor="'+c+'"');return b+">"});b=b.replace(/\[tr\s*(?:=\s*([^\]"]+?)(?:"[^\]]*?)?)?\s*\]/ig,function(b,a){return"<tr"+(a?' bgcolor="'+a+'"':"")+">"});b=b.replace(/\[td\s*(?:=\s*(\d{1,2})\s*,\s*(\d{1,2})\s*(?:,\s*(\d{1,4}%?))?)?\s*\]/ig,function(b,
a,c,e){return"<td"+(1<a?' colspan="'+a+'"':"")+(1<c?' rowspan="'+c+'"':"")+(e?' width="'+e+'"':"")+">"});b=b.replace(/\[\/(table|tr|td)\]/ig,"</$1>");b=b.replace(/\[\*\]((?:(?!\[\*\]|\[\/list\]|\[list\s*(?:=[^\]]+)?\])[\s\S])+)/ig,"<li>$1</li>");b=b.replace(/\[list\s*(?:=\s*([^\]"]+?)(?:"[^\]]*?)?)?\s*\]/ig,function(b,a){var c="<ul";a&&(c+=' type="'+a+'"');return c+">"});b=b.replace(/\[\/list\]/ig,"</ul>");b=b.replace(/\[hr\/\]/ig,"<hr />");for(c=1;c<=e;c++)b=b.replace("[\tubbcodeplace_"+c+"\t]",
l[c]);return b=b.replace(/(^|<\/?\w+(?:\s+[^>]*?)?>)([^<$]+)/ig,function(b,a,c){return a+c.replace(/[\t ]/g,function(a){return{"\t":"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"," ":"&nbsp;"}[a]})})}
function html2ubb(c){function g(a,c,h,d){if(!h)return d;var a=[],c=[],f;if(f=h.match(/ face\s*=\s*"\s*([^"]+)\s*"/i))a.push("[font="+f[1]+"]"),c.push("[/font]");if(f=h.match(/ size\s*=\s*"\s*(\d+)\s*"/i))a.push("[size="+f[1]+"]"),c.push("[/size]");if(f=h.match(/ color\s*=\s*"\s*([^"]+)\s*"/i))a.push("[color="+b(f[1])+"]"),c.push("[/color]");return a.join("")+d+c.join("")}function b(a){var b;if(b=a.match(/\s*rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/i)){for(a=(65536*b[1]+256*b[2]+1*b[3]).toString(16);6>
a.length;)a="0"+a;a="#"+a}return a=a.replace(/^#([0-9a-f])([0-9a-f])([0-9a-f])$/i,"#$1$1$2$2$3$3")}var l=/\s+src\s*=\s*(["']?)\s*(.+?)\s*\1(\s|$)/i,e=/\s+width\s*=\s*(["']?)\s*(\d+(?:\.\d+)?%?)\s*\1(\s|$)/i,m=/\s+height\s*=\s*(["']?)\s*(\d+(?:\.\d+)?%?)\s*\1(\s|$)/i,n=/(?:background|background-color|bgcolor)\s*[:=]\s*(["']?)\s*((rgb\s*\(\s*\d{1,3}%?,\s*\d{1,3}%?\s*,\s*\d{1,3}%?\s*\))|(#[0-9a-f]{3,6})|([a-z]{1,20}))\s*\1/i,a,o=[],j=0;a=(""+c).replace(/\s*\r?\n\s*/g,"");a=a.replace(/<(script|style)(\s+[^>]*?)?>[\s\S]*?<\/\1>/ig,
"");a=a.replace(/<\!--[\s\S]*?--\>/ig,"");a=a.replace(/<br(\s+[^>]*)?\/?>/ig,"\r\n");a=a.replace(/\[code\s*(=\s*([^\]]+?))?\]([\s\S]*?)\[\/code\]/ig,function(a){j++;o[j]=a;return"[\tubbcodeplace_"+j+"\t]"});a=a.replace(/<(\/?)(b|u|i|s)(\s+[^>]*?)?>/ig,"[$1$2]");a=a.replace(/<(\/?)strong(\s+[^>]*?)?>/ig,"[$1b]");a=a.replace(/<(\/?)em(\s+[^>]*?)?>/ig,"[$1i]");a=a.replace(/<(\/?)(strike|del)(\s+[^>]*?)?>/ig,"[$1s]");a=a.replace(/<(\/?)(sup|sub)(\s+[^>]*?)?>/ig,"[$1$2]");a=a.replace(/<(font)(\s+[^>]*?)?>(((?!<\1(\s+[^>]*?)?>)[\s\S]|<\1(\s+[^>]*?)?>((?!<\1(\s+[^>]*?)?>)[\s\S]|<\1(\s+[^>]*?)?>((?!<\1(\s+[^>]*?)?>)[\s\S])*?<\/\1>)*?<\/\1>)*?)<\/\1>/ig,
g);a=a.replace(/<(font)(\s+[^>]*?)?>(((?!<\1(\s+[^>]*?)?>)[\s\S]|<\1(\s+[^>]*?)?>((?!<\1(\s+[^>]*?)?>)[\s\S])*?<\/\1>)*?)<\/\1>/ig,g);a=a.replace(/<(font)(\s+[^>]*?)?>(((?!<\1(\s+[^>]*?)?>)[\s\S])*?)<\/\1>/ig,g);for(c=0;3>c;c++)a=a.replace(/<(span)(?:\s+[^>]*?)?\s+style\s*=\s*"((?:[^"]*?;)*\s*(?:font-family|font-size|color|background|background-color)\s*:[^"]*)"(?: [^>]+)?>(((?!<\1(\s+[^>]*?)?>)[\s\S]|<\1(\s+[^>]*?)?>((?!<\1(\s+[^>]*?)?>)[\s\S]|<\1(\s+[^>]*?)?>((?!<\1(\s+[^>]*?)?>)[\s\S])*?<\/\1>)*?<\/\1>)*?)<\/\1>/ig,
function(a,c,h,d){var a=h.match(/(?:^|;)\s*font-family\s*:\s*([^;]+)/i),c=h.match(/(?:^|;)\s*font-size\s*:\s*([^;]+)/i),f=h.match(/(?:^|;)\s*color\s*:\s*([^;]+)/i),h=h.match(/(?:^|;)\s*(?:background|background-color)\s*:\s*([^;]+)/i),e=[],k=[];a&&(e.push("[font="+a[1]+"]"),k.push("[/font]"));c&&(e.push("[size="+c[1]+"]"),k.push("[/size]"));f&&(e.push("[color="+b(f[1])+"]"),k.push("[/color]"));h&&(e.push("[back="+b(h[1])+"]"),k.push("[/back]"));return e.join("")+d+k.join("")});for(c=0;3>c;c++)a=a.replace(/<(div|p)(?:\s+[^>]*?)?[\s"';]\s*(?:text-)?align\s*[=:]\s*(["']?)\s*(left|center|right)\s*\2[^>]*>(((?!<\1(\s+[^>]*?)?>)[\s\S])+?)<\/\1>/ig,
"[align=$3]$4[/align]");for(c=0;3>c;c++)a=a.replace(/<(center)(?:\s+[^>]*?)?>(((?!<\1(\s+[^>]*?)?>)[\s\S])*?)<\/\1>/ig,"[align=center]$2[/align]");for(c=0;3>c;c++)a=a.replace(/<(p|div)(?:\s+[^>]*?)?\s+style\s*=\s*"(?:[^;"]*;)*\s*text-align\s*:([^;"]*)[^"]*"(?: [^>]+)?>(((?!<\1(\s+[^>]*?)?>)[\s\S]|<\1(\s+[^>]*?)?>((?!<\1(\s+[^>]*?)?>)[\s\S]|<\1(\s+[^>]*?)?>((?!<\1(\s+[^>]*?)?>)[\s\S])*?<\/\1>)*?<\/\1>)*?)<\/\1>/ig,function(a,b,c,d){return"[align="+c+"]"+d+"[/align]"});a=a.replace(/<a(?:\s+[^>]*?)?\s+href=(["'])\s*(.+?)\s*\1[^>]*>\s*([\s\S]*?)\s*<\/a>/ig,
function(a,b,c,d){if(!c||!d)return"";a="url";c.match(/^mailto:/i)&&(a="email",c=c.replace(/mailto:(.+?)/i,"$1"));b="["+a;c!=d&&(b+="="+c);return b+"]"+d+"[/"+a+"]"});a=a.replace(/<img(\s+[^>]*?)\/?>/ig,function(a,b){var c=b.match(/\s+emot\s*=\s*(["']?)\s*(.+?)\s*\1(\s|$)/i);if(c)return"[emot="+c[2]+"/]";var c=b.match(l),d=b.match(/\s+alt\s*=\s*(["']?)\s*(.*?)\s*\1(\s|$)/i),f=b.match(e),i=b.match(m),k=b.match(/\s+align\s*=\s*(["']?)\s*(\w+)\s*\1(\s|$)/i),g="[img";if(!c)return"";d=""+d[2];if(f||i)d+=
","+(f?f[2]:"")+","+(i?i[2]:"");k&&(d+=","+k[2]);d&&(g+="="+d);return g+="]"+c[2]+"[/img]"});a=a.replace(/<blockquote(?:\s+[^>]*?)?>/ig,"[quote]");a=a.replace(/<\/blockquote>/ig,"[/quote]");a=a.replace(/<embed((?:\s+[^>]*?)?(?:\s+type\s*=\s*"\s*application\/x-shockwave-flash\s*"|\s+classid\s*=\s*"\s*clsid:d27cdb6e-ae6d-11cf-96b8-4445535400000\s*")[^>]*?)\/?>/ig,function(a,b){var c=b.match(l),d=b.match(e),f=b.match(m),i="[flash";if(!c)return"";d&&f&&(i+="="+d[2]+","+f[2]);i+="]"+c[2];return i+"[/flash]"});
a=a.replace(/<embed((?:\s+[^>]*?)?(?:\s+type\s*=\s*"\s*application\/x-mplayer2\s*"|\s+classid\s*=\s*"\s*clsid:6bf52a52-394a-11d3-b153-00c04f79faa6\s*")[^>]*?)\/?>/ig,function(a,b){var c=b.match(l),d=b.match(e),f=b.match(m),i=b.match(/\s+autostart\s*=\s*(["']?)\s*(.+?)\s*\1(\s|$)/i),g="[media",j="0";if(!c)return"";i&&"true"==i[2]&&(j="1");d&&f&&(g+="="+d[2]+","+f[2]+","+j);g+="]"+c[2];return g+"[/media]"});a=a.replace(/<table(\s+[^>]*?)?>/ig,function(a,b){var c="[table";if(b){var d=b.match(e),f=b.match(n);
d&&(c+="="+d[2],f&&(c+=","+f[2]))}return c+"]"});a=a.replace(/<tr(\s+[^>]*?)?>/ig,function(a,b){var c="[tr";if(b){var d=b.match(n);d&&(c+="="+d[2])}return c+"]"});a=a.replace(/<(?:th|td)(\s+[^>]*?)?>/ig,function(a,b){var c="[td";if(b){var d=b.match(/\s+colspan\s*=\s*(["']?)\s*(\d+)\s*\1(\s|$)/i),f=b.match(/\s+rowspan\s*=\s*(["']?)\s*(\d+)\s*\1(\s|$)/i),g=b.match(e),d=d?d[2]:1,f=f?f[2]:1;if(1<d||1<f||g)c+="="+d+","+f;g&&(c+=","+g[2])}return c+"]"});a=a.replace(/<\/(table|tr)>/ig,"[/$1]");a=a.replace(/<\/(th|td)>/ig,
"[/td]");a=a.replace(/<ul(\s+[^>]*?)?>/ig,function(a,b){var c;b&&(c=b.match(/\s+type\s*=\s*(["']?)\s*(.+?)\s*\1(\s|$)/i));return"[list"+(c?"="+c[2]:"")+"]"});a=a.replace(/<ol(\s+[^>]*?)?>/ig,"[list=1]");a=a.replace(/<li(\s+[^>]*?)?>/ig,"[*]");a=a.replace(/<\/li>/ig,"");a=a.replace(/<\/(ul|ol)>/ig,"[/list]");a=a.replace(/<h([1-6])(\s+[^>]*?)?>/ig,function(a,b){return"\r\n\r\n[size="+(7-b)+"][b]"});a=a.replace(/<\/h[1-6]>/ig,"[/b][/size]\r\n\r\n");a=a.replace(/<address(\s+[^>]*?)?>/ig,"\r\n[i]");a=
a.replace(/<\/address>/ig,"[i]\r\n");a=a.replace(/<hr(\s+[^>]*?)?\/>/ig,"[hr/]");for(c=0;3>c;c++)a=a.replace(/<(p)(?:\s+[^>]*?)?>(((?!<\1(\s+[^>]*?)?>)[\s\S]|<\1(\s+[^>]*?)?>((?!<\1(\s+[^>]*?)?>)[\s\S]|<\1(\s+[^>]*?)?>((?!<\1(\s+[^>]*?)?>)[\s\S])*?<\/\1>)*?<\/\1>)*?)<\/\1>/ig,"\r\n\r\n$2\r\n\r\n");for(c=0;3>c;c++)a=a.replace(/<(div)(?:\s+[^>]*?)?>(((?!<\1(\s+[^>]*?)?>)[\s\S]|<\1(\s+[^>]*?)?>((?!<\1(\s+[^>]*?)?>)[\s\S]|<\1(\s+[^>]*?)?>((?!<\1(\s+[^>]*?)?>)[\s\S])*?<\/\1>)*?<\/\1>)*?)<\/\1>/ig,"\r\n$2\r\n");
a=a.replace(/((\s|&nbsp;)*\r?\n){3,}/g,"\r\n\r\n");a=a.replace(/^((\s|&nbsp;)*\r?\n)+/g,"");a=a.replace(/((\s|&nbsp;)*\r?\n)+$/g,"");for(c=1;c<=j;c++)a=a.replace("[\tubbcodeplace_"+c+"\t]",o[c]);a=a.replace(/<[^<>]+?>/g,"");var p={lt:"<",gt:">",nbsp:" ",amp:"&",quot:'"'};a=a.replace(/&(lt|gt|nbsp|amp|quot);/ig,function(a,b){return p[b]});return a=a.replace(/\[([a-z]+)(?:=[^\[\]]+)?\]\s*\[\/\1\]/ig,"")};
