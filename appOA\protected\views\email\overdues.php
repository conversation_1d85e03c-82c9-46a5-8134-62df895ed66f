<div style="color:#274257;">
    <div style="font-family: 'Microsoft Yahei'">
        <p>亲爱的家长:</p>
        <?php if (isset($invoiceList['overdues']) && count($invoiceList['overdues']) == count($invoiceList['info'])):?>
            <p>我们非常诚挚地提醒您，下面的<?php echo count($invoiceList['info']);?>张账单已经过期：</p>
        <?php elseif (isset($invoiceList['unpay']) && count($invoiceList['unpay']) == count($invoiceList['info'])):?>
            <p>在此提醒您以下的账单即将到期，请您尽快安排付款，感谢您的合作！</p>
        <?php endif;?>
        <p><?php echo CommonUtils::addColon('姓名');?><?php echo $name;?></p>
        <?php
        $i = 1;
        foreach ($invoiceList['info'] as $val):?>
        <p> <?php if (count($invoiceList['info'])>1){echo $i.". ";}?>账单标题：<?php echo $val['title'];?><br>
            <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>过期时间：<?php echo OA::formatDateTime($val['duetime']);?><br>
            <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>未付金额：<?php echo number_format($val['amount'],2);?> RMB <?php if ($val['tAmount']):?>（已付 <?php echo number_format($val['tAmount'], 2);?>）<?php endif;?>
        </p>
        <?php
        $i++;
        endforeach;?>
        <p></p>
        <p>您可以登录到<a href="http://www.ivyonline.cn" target="_blank">艾毅在线</a>查看账单信息或使用在线支付功能进行支付。如果您有其他任何问题，请随时与我们联系。</p>
        <p>谢谢</p>
        <p>艾毅在线</p>
        <p>自动邮件，请不要回复，如有问题请直接联系校园（<a href="mailto:<?php echo $supportEmail;?>"><?php echo $supportEmail;?></a>）</p>
        <p><?php echo OA::formatDateTime(time());?></p>
    </div>
</div>
<div style="color:#644436;">
    <div style="font-family: 'Trebuchet MS','Microsoft Yahei',Arial,Helvetica,sans-serif">
        <p>Dear Parents:</p>
        <?php if (isset($invoiceList['overdues']) && count($invoiceList['overdues']) == count($invoiceList['info'])):?>
            <p>This is a kind reminder that you have <?php echo count($invoiceList['info']);?> overdue invoice(s) as follows:</p>
        <?php elseif (isset($invoiceList['unpay']) && count($invoiceList['unpay']) == count($invoiceList['info'])):?>
            <p>This is a kind reminder that the following invoice(s) will be due soon.</p>
        <?php endif;?>
        <p>Student Name: <?php echo $name;?></p>
        <?php
        $i = 1;
        foreach ($invoiceList['info'] as $val):?>
        <p> <?php if (count($invoiceList['info'])>1){echo $i.". ";}?>Invoice Title: <?php echo $val['title'];?><br>
            <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>Date Due: <?php echo OA::formatDateTime($val['duetime']);?><br>
            <?php if (count($invoiceList['info'])>1){echo "&nbsp;&nbsp;&nbsp;";}?>Unpaid Amount: <?php echo number_format($val['amount'],2);?> RMB <?php if ($val['tAmount']):?> ( <?php echo number_format($val['tAmount'], 2);?> Paid )<?php endif;?>
        </p>
        <?php
        $i++;
        endforeach;?>
        <p></p>
        <p>For convenience, you can also make payment online or check payment information by logging on to <a href="http://www.ivyonline.cn">IvyOnline</a>.</p>
        <p>This is a system-generated email, please do not reply to this email. </p>
        <p>For questions, please contact your campus administrators directly (<a href="mailto:<?php echo $supportEmail;?>"><?php echo $supportEmail;?></a>).</p>
        <p>Regards,</p>
        <p>IvyOnline</p>
        <p> <?php echo OA::formatDateTime(time());?></p>
    </div>
</div>