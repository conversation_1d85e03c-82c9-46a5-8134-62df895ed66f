<?php

/**
 * UserIdentity represents the data needed to identity a user.
 * It contains the authentication method that checks if the provided
 * data can identity the user.
 */
class OAUserIdentity extends CUserIdentity
{
	
	private $_id;
    const ERROR_NONE=0;
    const ERROR_PASSWORD_INVALID=1;
	const ERROR_USERNAME_INVALID=2;
	const ERROR_EMAIL_INVALID=3;
	const ERROR_STATUS_NOTACTIV=4;
	const ERROR_STATUS_BAN=5;
	const ERROR_IS_STAFF=8;
	const ERROR_IS_NOT_STAFF=10;
	const ERROR_WRONG_CAMPUS=11;
	const ERROR_PROFILE_MISSING=12;
	/**
	 * Authenticates a user.
	 * The example implementation makes sure if the username and password
	 * are both 'demo'.
	 * In practical applications, this should be changed to authenticate
	 * against some persistent user identity storage (e.g. database).
	 * @return boolean whether authentication succeeds.
	 */
	public function authenticate()
	{
		$user=User::model()->notsafe()->findByAttributes(array('email'=>$this->username));
		if($user===null){
            if (strpos($this->username,"@")) {
                $this->errorCode=self::ERROR_EMAIL_INVALID;
            } else {
                $this->errorCode=self::ERROR_USERNAME_INVALID;
            }
        }
		else if($user->level==0)
			$this->errorCode=self::ERROR_STATUS_NOTACTIV;
		else if(Yii::app()->getModule('user')->loginUserType == 'staff' && $user->isstaff==0)
			$this->errorCode=self::ERROR_IS_NOT_STAFF;
		else if(Yii::app()->getModule('user')->loginUserType == 'parent' && $user->isstaff==1)
			$this->errorCode=self::ERROR_IS_STAFF;
		else if(Yii::app()->getModule('user')->encrypting($this->password)!==$user->pass)
			$this->errorCode=self::ERROR_PASSWORD_INVALID;
		else {


            if(empty( $user->profile )){
                $this->errorCode = self::ERROR_PROFILE_MISSING;
            }else{

                //管理多校园的员工不受限制；以后要细化admtype的值
                if($user->admtype == 0){

                    if(!is_null( Yii::app()->getModule('user')->excludeCampusIds )){
                        $_exclude = Yii::app()->getModule('user')->excludeCampusIds;
                        $excludes = is_array($_exclude) ? $_exclude: array($_exclude);
                        if(in_array($user->profile->branch, $excludes)){
                            $this->errorCode = self::ERROR_WRONG_CAMPUS;
                            return !$this->errorCode;
                        }
                    }
                    if(!is_null( Yii::app()->getModule('user')->limitCampusIds )){
                        $_limit = Yii::app()->getModule('user')->limitCampusIds;
                        $limits = is_array($_limit) ? $_limit: array($_limit);
                        if(!in_array($user->profile->branch, $limits)){
                            $this->errorCode = self::ERROR_WRONG_CAMPUS;
                            return !$this->errorCode;
                        }

                    }
                }

                $this->_id=$user->uid;
                $this->username=$user->getName();
                Yii::app()->user->setState("__isstaff", $user->uid);
                $this->errorCode=self::ERROR_NONE;
            }


		}


		return !$this->errorCode;
	}
    
    /**
    * @return integer the ID of the user record
    */
	public function getId()
	{
		return $this->_id;
	}
}