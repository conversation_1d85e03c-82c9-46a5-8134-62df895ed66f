var reports = new Backbone.Collection;
var visits = new Backbone.Collection;

function getCampusReportData(branchId)
{
    var progress = 0;
    var step = ( 100 *( 1 / getCampusDataRequest.report.length) ).toFixed(2);
    var complete = 0;
    var tpl, teaching, lunch, visit;
    $('#overall-progress .progress-bar').css('width','0').html('0%');
    $('#overall-progress').modal({keyboard: false,backdrop:'static'});

    for(var i=0; i<getCampusDataRequest.report.length;i++){
        if(i == 0 && $('#report-item-template').length > 0) {
            progress = parseInt(progress) + parseInt(step);
            complete +=1;
            continue;
        }
        $.ajax({
            type: "POST",
            url: getCampusDataRequest.report[i].url,
            data: getCampusDataRequest.report[i].data,
            dataType: 'json',
            async: true,
            success: function(data) {
                progress = parseInt(progress) + parseInt(step);
                complete +=1;
                if(progress > 100) progress = 100;
                if(complete == getCampusDataRequest.report.length) progress = 100;

                if(data.type == 'append'){
                    tpl = data.data;
                }
                else if(data.type == 'teaching'){
                    teaching = data;
                }
                else if(data.type == 'lunch'){
                    lunch = data;
                }
                else if(data.type == 'visit'){
                    visit = data;
                }

                $('#overall-progress .progress-bar').css('width',progress+'%').html(progress+'%');
                if(progress == 100){
                    $('body').append(tpl);
                    var _container = $('#container-campus-'+branchId+' .campus-main');
                    _container.html('').html($('#report-structure-template').html());

                    showReportData(branchId, teaching);

                    var cglunch = typeof lunch.general_lunch != 'undefined' && lunch.general_lunch == 1 ? 'glyphicon-ok text-success' : 'glyphicon-exclamation-sign text-danger';
                    var aglunch = '<a href="'+data.oaUrl+'/mcampus/catering/weeklySchedule?branchId='+branchId+'" target="_blank"><span class="glyphicon '+cglunch+'"></span></a>';
                    $('#general_lunch').html(aglunch);

                    var cslunch = typeof lunch.special_lunch != 'undefined' && lunch.special_lunch == 1 ? 'glyphicon-ok text-success' : 'glyphicon-exclamation-sign text-danger';
                    var aslunch = '<a href="'+data.oaUrl+'/mcampus/catering/weeklySchedule.php?branchId='+branchId+'" target="_blank"><span class="glyphicon '+cslunch+'"></span></a>';
                    $('#special_lunch').html(aslunch);

                    showVisitData(branchId, visit);

                    $('#e-data').hide();
                    $('#overall-progress .hint').html('Preparing Data...');
                    $( "#specified-date2" ).datepicker({
                        dateFormat: 'yy-mm-dd',
                        maxDate: todayDate,
                        onSelect: function(date){
                            specifiedDate = date;
                            loadCampusInfo({schoolid:currentBranchId}, $('.list-group a[datatype="report"]'));
                        }
                    });
                    $('#specified-date2').val(specifiedDate);
                    setTimeout("js:$('#overall-progress').modal('hide')", 500);
                    $('#e-data').fadeIn(500);
                }
            }
        });
    }
}

function showReportData(branchId, data)
{
    if(data){
        var _data = [];

        for(var __index in data.classtitle){
            var index = data.classtitle[__index]['id'];
            var _tojb={};

            var cschedule = typeof data.schedule != 'undefined' && data.schedule[index] == 1 ? 'glyphicon-ok text-success' : 'glyphicon-exclamation-sign text-danger';
            var aschedule = '<a href="'+data.oaUrl+'/mteaching/weekly/index?task=schedule&branchId='+branchId+'&weeknum='+data.weeknum+'&classid='+index+'" target="_blank"><span class="glyphicon '+cschedule+'"></span></a>';

            var cclassreport = typeof data.classreport != 'undefined' && data.classreport[index] == 1 ? 'glyphicon-ok text-success' : 'glyphicon-exclamation-sign text-danger';
            var aclassreport = '<a href="'+data.oaUrl+'/mteaching/weekly/index?task=classnews&branchId='+branchId+'&weeknum='+data.weeknum+'&classid='+index+'" target="_blank"><span class="glyphicon '+cclassreport+'"></span></a>';

            _tojb.title = typeof data.classtitle != 'undefined' ? data.classtitle[__index].title : '';
            _tojb.schedule = aschedule;
            _tojb.classreport = aclassreport;

            var crt = typeof data.childreport != 'undefined' ? data.childreport[index] : null;
            var ccd = typeof data.countchild != 'undefined' ? data.countchild[index] : 0;
            var achildreport = '<a href="'+data.oaUrl+'/mteaching/weekly/index?task=childreport&branchId='+branchId+'&weeknum='+data.weeknum+'&classid='+index+'" target="_blank">'+count(crt)+'/'+ccd+'</a>';
            _tojb.childreport = achildreport;

            var count_invalid = typeof data.childreport_invalid != "undefined" ? count(data.childreport_invalid[index]) : 0;
            if(count_invalid > 0){
                var childidstr='';
                for(var i in data.childreport_invalid[index]){
                    childidstr += i+',';
                }
                var cinvalid = '<a href="javascript:;" cids="'+childidstr.substr(0, childidstr.length-1)+'" tourl="'+data.oaUrl+'/modules/teaching/child_notes_v2.php?yid='+data.yid+'&wnumber='+data.weeknum+'&classid='+index+'" onclick="showChildren(this)">'+count_invalid+'</a>';
            }
            else{
                var cinvalid = 0;
            }
            _tojb.childreport_invalid = cinvalid;

            _data.push(_tojb);
        }

        var classext = (typeof data.schoolreport != 'undefined' && data.schoolreport == 1) ? 'glyphicon-ok text-success' : 'glyphicon-exclamation-sign text-danger';
        var aext = '<a href="'+data.oaUrl+'/mteaching/weekly/index?task=schoolnews&branchId='+branchId+'&classid='+index+'&weeknum='+data.weeknum+'" target="_blank"><span class="glyphicon '+classext+'"></span></a>';
        $('#school_report').html(aext);

        var itemView = Backbone.View.extend({
            tagName: 'tr',
            template: _.template($('#report-template').html()),
            initialize: function(){
                this.model.bind('change', this.render, this);
            },
            render: function(){
                this.$el.html( this.template(this.model.attributes) );
                return this;
            }
        });

        var itemsView = Backbone.View.extend({
            el: $('#c_data'),
            initialize: function(){
                reports.bind('reset', this.addAll, this);
                reports.reset(_data);
            },
            addOne: function(reportModel){
                var view = new itemView({model: reportModel});
                $('#c_data tbody').append(view.render().el);
            },
            addAll: function(){
                $('#c_data tbody').html('');
                reports.each(this.addOne);
            }
        });

        new itemsView();
    }
    else{
        $('#e-data').hide();
    }
}

function showVisitData(branchId, data)
{
    if(data.data){
        var _data = [];
        var currentDate = new Date(dateObject.getFullYear()+'-'+(dateObject.getMonth()+1)+'-'+dateObject.getDate());
        var currentTime = currentDate.getTime();
        var w1 = currentTime - (currentDate.getDay()+1) * 86400000; // 86400000 = 3600000*24
        var w2 = w1 - 604800000; // 604800000 = 7*3600000*24
        var w3 = w2 - 604800000; // 604800000 = 7*3600000*24
        var w4 = w3 - 604800000; // 604800000 = 7*3600000*24

        for(var index in data.data){
            var _obj;
            _obj = data.data[index];
            var prestr = _obj.period.substr(0, 4)+'-'+_obj.period.substr(4, 2)+'-'+_obj.period.substr(6, 2);
            var preDate = new Date(prestr);
            if(preDate.getTime() >= w1){
                _obj.period = '本周';
            }
            else if(preDate.getTime() >= w2 && preDate.getTime() < w1){
                _obj.period = '近2周';
            }
            else if(preDate.getTime() >= w3 && preDate.getTime() < w2){
                _obj.period = '近3周';
            }
            else if(preDate.getTime() >= w4 && preDate.getTime() < w3){
                _obj.period = '近4周';
            }
            else{
                _obj.period = prestr;
            }
            _data.push( _obj );
        }

        var itemView = Backbone.View.extend({
            tagName: 'tr',
            template: _.template($('#visit-template').html()),
            initialize: function(){
                this.model.bind('change', this.render, this);
            },
            render: function(){
                this.$el.html( this.template(this.model.attributes) );
                return this;
            }
        });

        var itemsView = Backbone.View.extend({
            el: $('#v_data'),
            initialize: function(){
                visits.comparator = function(visit) {
                    return visit.get('index');
                };
                visits.bind('reset', this.addAll, this);
                visits.reset(_data);
            },
            addOne: function(reportModel){
                var view = new itemView({model: reportModel});
                $('#v_data tbody').append(view.render().el);
            },
            addAll: function(){
                $('#v_data tbody').html('');
                visits.each(this.addOne);
            }
        });

        new itemsView();
    }
}