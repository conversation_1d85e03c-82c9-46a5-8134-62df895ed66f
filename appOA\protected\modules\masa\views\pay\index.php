<style>
/*    .nav-wizard > li:not(:first-child) > a:before {
    width: 0;
    height: 0;
    border-top:29px inset transparent;
    border-bottom: 25px inset transparent;
    border-left: 20px solid #ffffff;
    position: absolute;
    content: "";
    top: 0;
    left: 0;
}
.nav-wizard > li:not(:last-child) > a:after {
    width: 0;
    height: 0;
    border-top: 29px inset transparent;
    border-bottom: 25px inset transparent;
    border-left: 20px solid #eeeeee;
    position: absolute;
    content: "";
    top: 0;
    right: -20px;
    z-index: 2;
}
.nav-wizard > li.active > a:after {
    border-left-color: #428bca;
}*/
.nav-wizard > li>a:hover{
   background:#d5d5d5
}
.nav-wizard > li{
    margin:5px 0
}
 [v-cloak] {
            display: none;
        }
.reportAmend{
    display: none;
}
.reportAmendCheckbox {
            cursor: pointer;
        }

.rotate_half {
    transform: rotate(-180deg);
    -ms-transform: rotate(-180deg);
    -moz-transform: rotate(-180deg);
    -webkit-transform: rotate(-180deg);
}
 .reportTbody > tr > td, .reportTbody > tr > th {
    vertical-align: middle !important;
   text-align: center !important;
}
.spinner {
  margin: 100px auto;
  width: 90px;
  height: 90px;
  position: relative;
  text-align: center;
   
  -webkit-animation: rotate 2.0s infinite linear;
  animation: rotate 2.0s infinite linear;
}
 
.dot1, .dot2 {
  width: 60%;
  height: 60%;
  display: inline-block;
  position: absolute;
  top: 0;
  background-color: #428BCA;
  border-radius: 100%;
   
  -webkit-animation: bounce 2.0s infinite ease-in-out;
  animation: bounce 2.0s infinite ease-in-out;
}
 
.dot2 {
  top: auto;
  bottom: 0px;
  -webkit-animation-delay: -1.0s;
  animation-delay: -1.0s;
}
 
@-webkit-keyframes rotate { 100% { -webkit-transform: rotate(360deg) }}
@keyframes rotate { 100% { transform: rotate(360deg); -webkit-transform: rotate(360deg) }}
 
@-webkit-keyframes bounce {
  0%, 100% { -webkit-transform: scale(0.0) }
  50% { -webkit-transform: scale(1.0) }
}
 
@keyframes bounce {
  0%, 100% {
    transform: scale(0.0);
    -webkit-transform: scale(0.0);
  } 50% {
    transform: scale(1.0);
    -webkit-transform: scale(1.0);
  }
}
</style>
<div class="container-fluid">
<ol class="breadcrumb">
    <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
    <li><?php echo CHtml::link(Yii::t('site', 'Routines'), array('//mcampus/default/index')) ?></li>
    <li class="active"><?php echo Yii::t('site', 'ASA Management') ?></li>
</ol>

<div class="row">
    <div class="col-md-2 mb10">
        <?php
        $this->widget('zii.widgets.CMenu', array(
            'items' => $this->module->getMenu(),
            'id' => 'pageCategory',
            'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
            'activeCssClass' => 'active',
        ));
        ?>
    </div>
    <div class="col-md-2">
        <?php
        $this->widget('zii.widgets.CMenu', array(
            'items' => $this->module->getAttendSubMenu(),
            'id' => 'pageCategory',
            'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
            'activeCssClass' => 'active',
        ));
        ?>
    </div>
    <div class="col-md-8" >

        <div class="row">
            <div class="col-sm-2 form-group">
            <div>
              <button type="button" class="btn btn-default dropdown-toggle month" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                请选择 <span class="caret"></span>
              </button>
              
              <ul class="dropdown-menu" id="mon">
                <li v-for="mon in month">
<a :href ="'<?php echo Yii::app()->createUrl('/masa/pay/index', array('branchId' => $this->branchId)); ?>&month='+mon+''" >{{mon}}</a></li>

              </ul>
              <?php
                $month = $_GET['month'];
                if ($month) {
                    $link = $this->createUrl('/masa/attend/printreport', array('month' => $month));
                    echo '<a type="button" class="btn btn-default" target="__blank" href="'.$link.'">打印</a>';
                }
              ?>
            </div>          
            <div>
            </div>      
            </div>
        </div>
        <div class="spinner">
          <div class="dot1"></div>
          <div class="dot2"></div>
        </div>
        <div id="tablelist" v-cloak>
        
        <div class="listul">
            <ul class="nav nav-wizard" style="padding-bottom: 20px;">
                <li>
                    <a href='javascript:;'>{{time}} ASA工资结算 </a>
                </li>
                <li>
                    <a href="<?php echo $this->createUrl('attend/index'); ?>">课程考勤</a>
                </li>
                <li>
                    <a href="<?php echo $this->createUrl('attend/otherStaffs'); ?>">工作人员考勤</a>
                </li>
                <li>
                    <a href="<?php echo $this->createUrl('attend/statistics'); ?>">分成统计</a>
                </li>
                <li class="active">
                    <a href='javascript:void(0)'>特殊调整 / 结算设置</a>
                </li>
                <li>
                    <a href='<?php echo $this->createUrl('summaryTable'); ?>'>工资汇总表</a>
                </li>
                 <li>
                    <a href="javascript:void(0)">提交</a>
                </li>
            </ul>
        </div>
        <div class="clearfix"></div>
         
        <div class="panel panel-default tablelists">
          <div class="panel-body" v-if="neibu.length==0 && waibugongsi.length==0 && waibugeren.length==0" >
               暂无数据
           </div>
          <div class="panel-body" v-else>    
              <div class="panel panel-default"  v-if="neibu.length>0" >
              <div class="panel-heading">内部员工</div>
              <table class="table">
              <thead>
                  <colgroup>
                      <col style="width:10%">
                      <col style="width:15%">
                      <col style="width:15%">
                      <col style="width:15%">
                      <col style="width:15%">
                      <col style="width:15%">
                      <col style="width:15%">
                  </colgroup>
              </thead>
                 <tbody  class="reportTbody" >
                  <tr>
                      <th width="50">#</th>
                      <th width="50">姓名</th>                     
                      <th width="50">职位</th>
                      <th width="50">课程</th>
                      <th width="50">课时费×课时=小计</th>
                      <th width="50">调整金额</th>
                      <th width="50">总计</th>                                
                      </tr>
                     
                     <template  v-for="(tData,indexs) in neibu">
                    <template v-if='tData.courses.constructor!=Array'>
                     <tr  v-for="(course,cid,index) in tData.courses">
                        <td width="50" :rowspan='arrLength(tData.courses)' :data-vendor='tData.vendor_id' class="vendorId"  v-if='index===0'>{{indexs+1}}</td>
                        <td width="50" :rowspan='arrLength(tData.courses)' :data-settle='tData.settle' class="settle"  v-if='index===0'>{{tData.name}} <span v-if="tData.fee_merge == 1" class="label label-primary">费用合并</span></td>
                        <td width="50">{{course.position}}</td>
                        <td width="50">{{course.course_name}}
                            <p v-if='course.variation_amout==0 || course.variation_amout==0.00' class="subject_money hidden" 
                            :data-name="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][amount]'" 
                            :data-programIdName="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][program_id]'" 
                            :data-courseIdName="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][course_id]'" 
                            :data-programId="course.program_id"  
                            :data-courseId='course.course_id'></p>
                            
                            <p v-else class="subject_money hidden" 
                            :data-name="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][amount]'" 
                            :data-programIdName="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][program_id]'" 
                            :data-courseIdName="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][course_id]'" 
                            :data-programId="course.program_id"  
                            :data-courseId='course.course_id'>{{course.variation_amout}}</p>

                            <p class="subject_remark hidden" 
                            :data-name="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][memo]'" 
                            :data-programIdName="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][program_id]'" 
                            :data-courseIdName="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][course_id]'" 
                            :data-programId="course.program_id"  
                            :data-courseId='course.course_id'>{{course.variation_memo}}</p>
                              
                        </td>
                        <td width="50">    
                            <div v-if="course.course_count.length == '1'">
                                 <p v-if='course.assistant_pay>0'>{{course.unit_price}} × {{len1(course.course_count)}} - {{course.assistant_pay}} =
                                 {{parsefloat(course.unit_price) * course.course_count - parsefloat(course.assistant_pay)}}</p>
                                 <p v-else>{{course.unit_price}} × {{len1(course.course_count)}} =
                                 {{parsefloat(course.unit_price) * course.course_count}}</p>    
                            </div>
                            <div v-else>
                              <p v-if='course.assistant_pay>0'>{{course.unit_price}} ×{{and(course.course_count)}} - {{course.assistant_pay}}={{parsefloat(course.unit_price) *add(course.course_count)  - parsefloat(course.assistant_pay)}}</p>
                              <p v-else>
                                  {{course.unit_price}} ×{{and(course.course_count)}}={{parsefloat(course.unit_price) *add(course.course_count)}}
                              </p>
                            </div>        
                        </td>                   
                        <td width="50" :rowspan='arrLength(tData.courses)'  v-if='index===0'>
                          <input type="text" class="inputmoney hidden " :value='tData.variation_amout'>
                          <input type="text" class="inputremark hidden" :value='tData.variation_memo'>
                            <div v-if="tData.variation_amout==0 || tData.variation_amout==0.00"></div>
                            <div v-else>金额：{{tData.variation_amout}}</div>
                            <div v-if="tData.variation_memo"  v-html="'备注：'+ tData.variation_memo+''" ></div>
                            <h5><button class="btn btn-info btn-xs" @click='modify(tData.vendor_id,indexs,1)'>调整金额</button></h5>
                        </td>
                        <td width="50" :rowspan='arrLength(tData.courses)'  v-if='index===0'>{{parsefloat(tData.amount)}}</td>
                    </tr>
                     </template>

                    <template v-else> 
                       <tr  v-for="(course,index) in tData.courses">
                        <td width="50" :rowspan='arrLength(tData.courses)' :data-vendor='tData.vendor_id' class="vendorId"  v-if='index===0'>{{indexs+1}}</td>
                        <td width="50" :rowspan='arrLength(tData.courses)' :data-settle='tData.settle' class="settle"  v-if='index===0'>{{tData.name}} <span v-if="tData.fee_merge == 1" class="label label-primary">费用合并</span></td>
                        <td width="50">{{course.position}}</td>
                        <td width="50">{{course.course_name}}
                            <p v-if='course.variation_amout==0 || course.variation_amout==0.00' class="subject_money hidden" 
                            :data-name="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][amount]'" 
                            :data-programIdName="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][program_id]'" 
                            :data-courseIdName="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][course_id]'" 
                            :data-programId="course.program_id"  
                            :data-courseId='course.course_id'></p>
                            
                            <p v-else class="subject_money hidden" 
                            :data-name="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][amount]'" 
                            :data-programIdName="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][program_id]'" 
                            :data-courseIdName="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][course_id]'" 
                            :data-programId="course.program_id"  
                            :data-courseId='course.course_id'>{{course.variation_amout}}</p>

                            <p class="subject_remark hidden" 
                            :data-name="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][memo]'" 
                            :data-programIdName="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][program_id]'" 
                            :data-courseIdName="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][course_id]'" 
                            :data-programId="course.program_id"  
                            :data-courseId='course.course_id'>{{course.variation_memo}}</p>
                        </td>
                        <td width="50">    
                                <div v-if="course.course_count.length == '1'">
                                     <p v-if='course.assistant_pay>0'>{{course.unit_price}} × {{len1(course.course_count)}} - {{course.assistant_pay}} =
                                     {{parsefloat(course.unit_price) * course.course_count - parsefloat(course.assistant_pay)}}</p>
                                     <p v-else>{{course.unit_price}} × {{len1(course.course_count)}} =
                                     {{parsefloat(course.unit_price) * course.course_count}}</p>    
                                </div>
                                <div v-else>
                                  <p v-if='course.assistant_pay>0'>{{course.unit_price}} ×{{and(course.course_count)}} - {{course.assistant_pay}}={{parsefloat(course.unit_price) *add(course.course_count)  - parsefloat(course.assistant_pay)}}</p>
                                  <p v-else>
                                      {{course.unit_price}} ×{{and(course.course_count)}}={{parsefloat(course.unit_price) *add(course.course_count)}}
                                  </p>
                                </div>        
                        </td>                   
                        <td width="50" :rowspan='arrLength(tData.courses)'  v-if='index===0'>
                            <input type="text" class="inputmoney hidden " :value='tData.variation_amout'>
                            <input type="text" class="inputremark hidden" :value='tData.variation_memo'>
                           <div v-if="tData.variation_amout==0 || tData.variation_amout==0.00"></div>
                            <div v-else>金额：{{tData.variation_amout}}</div>
                            <div v-if="tData.variation_memo" v-html="'备注：'+ tData.variation_memo+''"></div>
                            <h5><button class="btn btn-info btn-xs" @click='modify(tData.vendor_id,indexs,1)'>调整金额</button></h5>
                        </td>
                        <td width="50" :rowspan='arrLength(tData.courses)'  v-if='index===0'>{{parsefloat(tData.amount)}}</td>
                    </tr>
                    </template>
                     </template>
                   
                </tbody>
              </table>
            </div>
          <div class="panel panel-default" v-if="waibugongsi.length>0">
              <div class="panel-heading">外部公司</div>
              <table class="table">
              <thead>
                  <colgroup>
                      <col style="width:10%">
                      <col style="width:15%">
                      <col style="width:15%">
                      <col style="width:15%">
                      <col style="width:15%">
                      <col style="width:15%">
                      <col style="width:15%">
                  </colgroup>
              </thead>
                 <tbody  class="reportTbody" >
                   <tr>
                      <th width="50">#</th>
                      <th width="50">姓名</th>                     
                      <th width="50">职位</th>
                      <th width="50">课程</th>
                      <th width="50">课时费×课时=小计</th>
                      <th width="50">调整金额</th>
                      <th width="50">总计</th>                                
                   </tr>
                 
                   <template v-for="(tData,indexs) in waibugongsi">
                    <template v-if='tData.courses.constructor!=Array'>
                   <tr  v-for="(course,cid,index) in tData.courses">
                        <td width="50" :rowspan='arrLength(tData.courses)' :data-vendor='tData.vendor_id' class="vendorId" v-if='index===0'>{{indexs+1}}</td>
                        <td width="50" :rowspan='arrLength(tData.courses)' :data-settle='tData.settle' class="settle"  v-if='index===0'>{{tData.name}} <span v-if="tData.fee_merge == 1" class="label label-primary">费用合并</span></td>
                        <td width="50"> {{course.position}}</td>                    
                        <td width="50">{{course.course_name}}
                            <p v-if='course.variation_amout==0 || course.variation_amout==0.00' class="subject_money hidden" 
                            :data-name="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][amount]'" 
                            :data-programIdName="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][program_id]'" 
                            :data-courseIdName="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][course_id]'" 
                            :data-programId="course.program_id"  
                            :data-courseId='course.course_id'></p>
                            
                            <p v-else class="subject_money hidden" 
                            :data-name="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][amount]'" 
                            :data-programIdName="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][program_id]'" 
                            :data-courseIdName="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][course_id]'" 
                            :data-programId="course.program_id"  
                            :data-courseId='course.course_id'>{{course.variation_amout}}</p>

                            <p class="subject_remark hidden" 
                            :data-name="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][memo]'" 
                            :data-programIdName="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][program_id]'" 
                            :data-courseIdName="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][course_id]'" 
                            :data-programId="course.program_id"  
                            :data-courseId='course.course_id'>{{course.variation_memo}}</p>
                        </td>
                        </td>
                        <td width="50">
                                 <div v-if="course.course_count.length == '1'">
                                     <p v-if='course.assistant_pay>0'>{{course.unit_price}} × {{len1(course.course_count)}} - {{course.assistant_pay}} =
                                     {{parsefloat(course.unit_price) * course.course_count - parsefloat(course.assistant_pay)}}</p>
                                     <p v-else>{{course.unit_price}} × {{len1(course.course_count)}} =
                                     {{parsefloat(course.unit_price) * course.course_count}}</p>
                                 </div>
                                 <div v-else>
                                      <p v-if='course.assistant_pay>0'>{{course.unit_price}} ×{{and(course.course_count)}} - {{course.assistant_pay}}={{parsefloat(course.unit_price) *add(course.course_count)  - parsefloat(course.assistant_pay)}}</p>
                                      <p v-else>
                                          {{course.unit_price}} ×{{and(course.course_count)}}={{parsefloat(course.unit_price) *add(course.course_count)}}
                                      </p>
                                 </div>
                              
                        </td>                   
                        <td width="50" :rowspan='arrLength(tData.courses)' v-if='index===0'>
                         <input type="text" class="inputmoney hidden " :value='tData.variation_amout'>
                          <input type="text" class="inputremark hidden" :value='tData.variation_memo'>
                            <div v-if="tData.variation_amout==0 || tData.variation_amout==0.00"></div>
                            <div v-else>金额：{{tData.variation_amout}}</div>
                            <div v-if="tData.variation_memo" v-html="'备注：'+ tData.variation_memo+''"></div>
                              <h5><button class="btn btn-info btn-xs" @click='modify(tData.vendor_id,indexs,2)'>调整金额</button></h5>
                        </td>
                        <td width="50" :rowspan='arrLength(tData.courses)'  v-if='index===0'>{{parsefloat(tData.amount)}}</td>
                    </tr>
                    </template>
                    <template v-else>
                     <tr  v-for="(course,index) in tData.courses">
                        <td width="50" :rowspan='arrLength(tData.courses)' :data-vendor='tData.vendor_id' class="vendorId" v-if='index===0'>{{indexs+1}}</td>
                        <td width="50" :rowspan='arrLength(tData.courses)' :data-settle='tData.settle' class="settle"  v-if='index===0'>{{tData.name}} <span v-if="tData.fee_merge == 1" class="label label-primary">费用合并</span></td>
                        <td width="50"> {{course.position}}</td>                    
                        <td width="50">{{course.course_name}}
                            <p v-if='course.variation_amout==0 || course.variation_amout==0.00' class="subject_money hidden" 
                            :data-name="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][amount]'" 
                            :data-programIdName="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][program_id]'" 
                            :data-courseIdName="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][course_id]'" 
                            :data-programId="course.program_id"  
                            :data-courseId='course.course_id'></p>
                            
                            <p v-else class="subject_money hidden" 
                            :data-name="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][amount]'" 
                            :data-programIdName="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][program_id]'" 
                            :data-courseIdName="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][course_id]'" 
                            :data-programId="course.program_id"  
                            :data-courseId='course.course_id'>{{course.variation_amout}}</p>

                            <p class="subject_remark hidden" 
                            :data-name="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][memo]'" 
                            :data-programIdName="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][program_id]'" 
                            :data-courseIdName="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][course_id]'" 
                            :data-programId="course.program_id"  
                            :data-courseId='course.course_id'>{{course.variation_memo}}</p>
                        </td>
                        </td>
                        <td width="50">
                                 <div v-if="course.course_count.length == '1'">
                                     <p v-if='course.assistant_pay>0'>{{course.unit_price}} × {{len1(course.course_count)}} - {{course.assistant_pay}} =
                                     {{parsefloat(course.unit_price) * course.course_count - parsefloat(course.assistant_pay)}}</p>
                                     <p v-else>{{course.unit_price}} × {{len1(course.course_count)}} =
                                     {{parsefloat(course.unit_price) * course.course_count}}</p>
                                 </div>
                                 <div v-else>
                                      <p v-if='course.assistant_pay>0'>{{course.unit_price}} ×{{and(course.course_count)}} - {{course.assistant_pay}}={{parsefloat(course.unit_price) *add(course.course_count)  - parsefloat(course.assistant_pay)}}</p>
                                      <p v-else>
                                          {{course.unit_price}} ×{{and(course.course_count)}}={{parsefloat(course.unit_price) *add(course.course_count)}}
                                      </p>
                                 </div>
                              
                        </td>                   
                        <td width="50" :rowspan='arrLength(tData.courses)' v-if='index===0'>
                         <input type="text" class="inputmoney hidden " :value='tData.variation_amout'>
                          <input type="text" class="inputremark hidden" :value='tData.variation_memo'>
                            <div v-if="tData.variation_amout==0 || tData.variation_amout==0.00"></div>
                            <div v-else>金额：{{tData.variation_amout}}</div>
                            <div v-if="tData.variation_memo" v-html="'备注：'+ tData.variation_memo+''"></div>
                              <h5><button class="btn btn-info btn-xs" @click='modify(tData.vendor_id,indexs,2)'>调整金额</button></h5>
                        </td>
                        <td width="50" :rowspan='arrLength(tData.courses)'  v-if='index===0'>{{parsefloat(tData.amount)}}</td>
                    </tr>
                    </template>
                    </template>
                   
                </tbody>
              </table>
            </div>
              <div class="panel panel-default"  v-if="waibugeren.length>0" >
              <div class="panel-heading">外部个人</div>
              <table class="table">
              <thead>
                  <colgroup>
                      <col style="width:10%">
                      <col style="width:15%">
                      <col style="width:15%">
                      <col style="width:15%">
                      <col style="width:15%">
                      <col style="width:15%">
                      <col style="width:15%">
                  </colgroup>
              </thead>
                 <tbody  class="reportTbody">
                    <tr >
                      <th width="50">#</th>
                      <th width="50">姓名</th>                     
                      <th width="50">职位</th>
                      <th width="50">课程</th>
                      <th width="50">课时费×课时=小计</th>
                      <th width="50">调整金额</th>
                      <th width="50">总计</th>                     
                   </tr>
                   
                   <template v-for="(tData,indexs) in waibugeren">  
                   <template v-if='tData.courses.constructor!=Array'>
                   <tr  v-for="(course,cid,index) in tData.courses">
                        <td width="50"  :rowspan='arrLength(tData.courses)' :data-vendor='tData.vendor_id' class="vendorId" v-if='index===0'>{{indexs+1}}</td>
                        <td width="50"  :rowspan='arrLength(tData.courses)' v-if='index===0'>{{tData.name}} <span v-if="tData.fee_merge == 1" class="label label-primary">费用合并</span></td>
                        <td width="50">{{course.position}}</td>
                        <td width="50">{{course.course_name}}
                            <p v-if='course.variation_amout==0 || course.variation_amout==0.00' class="subject_money hidden" 
                            :data-name="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][amount]'" 
                            :data-programIdName="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][program_id]'" 
                            :data-courseIdName="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][course_id]'" 
                            :data-programId="course.program_id"  
                            :data-courseId='course.course_id'></p>
                            
                            <p v-else class="subject_money hidden" 
                            :data-name="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][amount]'" 
                            :data-programIdName="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][program_id]'" 
                            :data-courseIdName="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][course_id]'" 
                            :data-programId="course.program_id"  
                            :data-courseId='course.course_id'>{{course.variation_amout}}</p>

                            <p class="subject_remark hidden" 
                            :data-name="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][memo]'" 
                            :data-programIdName="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][program_id]'" 
                            :data-courseIdName="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][course_id]'" 
                            :data-programId="course.program_id"  
                            :data-courseId='course.course_id'>{{course.variation_memo}}</p>
                        </td>
                        </td>
                        <td width="50">
                             <div v-if="course.course_count.length == '1'">
                                 <p v-if='course.assistant_pay>0'>{{course.unit_price}} × {{len1(course.course_count)}} - {{course.assistant_pay}}=
                                 {{parsefloat(course.unit_price) * course.course_count - parsefloat(course.assistant_pay)}}</p>
                                 <p v-else>{{course.unit_price}} × {{len1(course.course_count)}} =
                                 {{parsefloat(course.unit_price) * course.course_count}}</p>                
                             </div>
                             <div v-else>
                                  <p v-if='course.assistant_pay>0'>{{course.unit_price}} ×{{and(course.course_count)}} - {{course.assistant_pay}}={{parsefloat(course.unit_price) *add(course.course_count)  - parsefloat(course.assistant_pay)}}</p>
                                  <p v-else>
                                      {{course.unit_price}} ×{{and(course.course_count)}}={{parsefloat(course.unit_price) *add(course.course_count)}}
                                  </p>
                             </div>
                        </td>                           
                        <td width="50"  :rowspan='arrLength(tData.courses)' v-if='index===0'>
                         <input type="text" class="inputmoney hidden " :value='tData.variation_amout'>
                          <input type="text" class="inputremark hidden" :value='tData.variation_memo'>
                            <div v-if="tData.variation_amout==0 || tData.variation_amout==0.00"></div>
                            <div v-else>金额：{{tData.variation_amout}}</div>
                            <p v-if="tData.variation_memo" v-html="'备注：'+ tData.variation_memo+''"></p>
                            <div v-if='status==10'>
                            <div v-if="tData.settle==1"  class="btn-group btn-group-xs">
                                <button type="button" class="btn btn-default btn-success fixedbtn"  data-id='1' onclick='tab(this)'>结算</button>
                                <button type="button" class="btn btn-default fixedbtn"  data-id='0' onclick='tab(this)'>不结算</button>
                            </div>
                             <div v-if="tData.settle==0"  class="btn-group btn-group-xs">
                                <button type="button" class="btn btn-default fixedbtn"  data-id='1' onclick='tab(this)'>结算</button>
                                <button type="button" class="btn btn-default btn-success fixedbtn"  data-id='0' onclick='tab(this)'>不结算</button>
                            </div>
                             <div v-if="tData.settle==2"  class="btn-group btn-group-xs">
                                <button type="button" class="btn btn-default  fixedbtn"  data-id='1' onclick='tab(this)'>结算</button>
                                <button type="button" class="btn btn-default  fixedbtn"  data-id='0' onclick='tab(this)'>不结算</button>
                            </div>
                            </div>
                            <div v-else>
                                 <div v-if="tData.settle==1"  class="btn-group btn-group-xs">
                                <button type="button" class="btn btn-default btn-success fixedbtn"  data-id='1'>结算</button>
                                <button type="button" class="btn btn-default fixedbtn"  data-id='0'>不结算</button>
                            </div>
                             <div v-if="tData.settle==0"  class="btn-group btn-group-xs">
                                <button type="button" class="btn btn-default fixedbtn"  data-id='1'>结算</button>
                                <button type="button" class="btn btn-default btn-success fixedbtn"  data-id='0' >不结算</button>
                            </div>
                             <div v-if="tData.settle==2"  class="btn-group btn-group-xs">
                                <button type="button" class="btn btn-default  fixedbtn"  data-id='1'>结算</button>
                                <button type="button" class="btn btn-default  fixedbtn"  data-id='0' >不结算</button>
                            </div>
                            </div>
                            <h5><button class="btn btn-info btn-xs" @click='modify(tData.vendor_id,indexs,3)'>调整金额</button></h5>
                        </td>
                        <td width="50"  :rowspan='arrLength(tData.courses)' v-if='index===0'>{{parsefloat(tData.amount)}}</td>
                    </tr>
                    </template>
                    <template v-else>
                    <tr v-for="(course,index) in tData.courses">
                        <td width="50"  :rowspan='arrLength(tData.courses)' :data-vendor='tData.vendor_id' class="vendorId" v-if='index===0'>{{indexs+1}}</td>
                        <td width="50"  :rowspan='arrLength(tData.courses)' v-if='index===0'>{{tData.name}} <span v-if="tData.fee_merge == 1" class="label label-primary">费用合并</span></td>
                        <td width="50">{{course.position}}</td>
                        <td width="50">{{course.course_name}}
                            <p v-if='course.variation_amout==0 || course.variation_amout==0.00' class="subject_money hidden" 
                            :data-name="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][amount]'" 
                            :data-programIdName="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][program_id]'" 
                            :data-courseIdName="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][course_id]'" 
                            :data-programId="course.program_id"  
                            :data-courseId='course.course_id'></p>
                            
                            <p v-else class="subject_money hidden" 
                            :data-name="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][amount]'" 
                            :data-programIdName="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][program_id]'" 
                            :data-courseIdName="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][course_id]'" 
                            :data-programId="course.program_id"  
                            :data-courseId='course.course_id'>{{course.variation_amout}}</p>

                            <p class="subject_remark hidden" 
                            :data-name="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][memo]'" 
                            :data-programIdName="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][program_id]'" 
                            :data-courseIdName="'amount_item[' + tData.vendor_id+ ']['+course.course_id+'][course_id]'" 
                            :data-programId="course.program_id"  
                            :data-courseId='course.course_id'>{{course.variation_memo}}</p>
                        </td>
                        </td>
                        <td width="50">
                             <div v-if="course.course_count.length == '1'">
                                 <p v-if='course.assistant_pay>0'>{{course.unit_price}} × {{len1(course.course_count)}} - {{course.assistant_pay}}=
                                 {{parsefloat(course.unit_price) * course.course_count - parsefloat(course.assistant_pay)}}</p>
                                 <p v-else>{{course.unit_price}} × {{len1(course.course_count)}} =
                                 {{parsefloat(course.unit_price) * course.course_count}}</p>                
                             </div>
                             <div v-else>
                                  <p v-if='course.assistant_pay>0'>{{course.unit_price}} ×{{and(course.course_count)}} - {{course.assistant_pay}}={{parsefloat(course.unit_price) *add(course.course_count)  - parsefloat(course.assistant_pay)}}</p>
                                  <p v-else>
                                      {{course.unit_price}} ×{{and(course.course_count)}}={{parsefloat(course.unit_price) *add(course.course_count)}}
                                  </p>
                             </div>
                        </td>                           
                        <td width="50"  :rowspan='arrLength(tData.courses)' v-if='index===0'>
                         <input type="text" class="inputmoney hidden " :value='tData.variation_amout'>
                          <input type="text" class="inputremark hidden" :value='tData.variation_memo'>
                            <div v-if="tData.variation_amout==0 || tData.variation_amout==0.00"></div>
                            <div v-else>金额：{{tData.variation_amout}}</div>
                            <p v-if="tData.variation_memo" v-html="'备注：'+ tData.variation_memo+''"></p>
                            <div v-if='status==10'>
                            <div v-if="tData.settle==1"  class="btn-group btn-group-xs">
                                <button type="button" class="btn btn-default btn-success fixedbtn"  data-id='1' onclick='tab(this)'>结算</button>
                                <button type="button" class="btn btn-default fixedbtn"  data-id='0' onclick='tab(this)'>不结算</button>
                            </div>
                             <div v-if="tData.settle==0"  class="btn-group btn-group-xs">
                                <button type="button" class="btn btn-default fixedbtn"  data-id='1' onclick='tab(this)'>结算</button>
                                <button type="button" class="btn btn-default btn-success fixedbtn"  data-id='0' onclick='tab(this)'>不结算</button>
                            </div>
                             <div v-if="tData.settle==2"  class="btn-group btn-group-xs">
                                <button type="button" class="btn btn-default  fixedbtn"  data-id='1' onclick='tab(this)'>结算</button>
                                <button type="button" class="btn btn-default  fixedbtn"  data-id='0' onclick='tab(this)'>不结算</button>
                            </div>
                            </div>
                            <div v-else>
                                 <div v-if="tData.settle==1"  class="btn-group btn-group-xs">
                                <button type="button" class="btn btn-default btn-success fixedbtn"  data-id='1'>结算</button>
                                <button type="button" class="btn btn-default fixedbtn"  data-id='0'>不结算</button>
                            </div>
                             <div v-if="tData.settle==0"  class="btn-group btn-group-xs">
                                <button type="button" class="btn btn-default fixedbtn"  data-id='1'>结算</button>
                                <button type="button" class="btn btn-default btn-success fixedbtn"  data-id='0' >不结算</button>
                            </div>
                             <div v-if="tData.settle==2"  class="btn-group btn-group-xs">
                                <button type="button" class="btn btn-default  fixedbtn"  data-id='1'>结算</button>
                                <button type="button" class="btn btn-default  fixedbtn"  data-id='0' >不结算</button>
                            </div>
                            </div>
                            <h5><button class="btn btn-info btn-xs" @click='modify(tData.vendor_id,indexs,3)'>调整金额</button></h5>
                        </td>
                        <td width="50"  :rowspan='arrLength(tData.courses)' v-if='index===0'>{{parsefloat(tData.amount)}}</td>
                    </tr>
                    </template>
                  </template>
                </tbody>
              </table>
            </div>
             <p class="pull-right"  v-if='status==10'><span class="text-danger texts"></span><button @click='tijiao()' role="button" class="btn btn-sm  btn-primary modifybtn" id="btnsub"> 确认 </button> </p>
             <p class="pull-right" v-else><button role="button" class="btn btn-sm  btn-primary modifybtn" disabled="disabled"> 已确认 </button> </p>
          </div>
          <div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" data-keyboard="false" data-backdrop="static">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×
                        </button>
                        <h4 class="modal-title">
                          {{check.name}}
                        </h4>
                    </div>
                    <div class="modal-body">
                        <div class="form-horizontal"  v-for="(course,cid,index) in check.courses">
                            <p class="help-block">{{course.course_name}}:</p>
                            <div class="form-group">
                            <span class="addke hidden">{{cid}}</span>
                            <label class="col-xs-2 control-label"><label for="AsaRefund_course_id">金额</label></label>
                            <div class="col-xs-10 control-label" v-if='course.variation_amout==0 || course.variation_amout==0.00'>
                            <input type="number" value="" class="form-control input-sm eject_money"/>
                            </div>
                            <div class="col-xs-10 control-label" v-else>
                            <input type="number" :value="course.variation_amout" class="form-control input-sm eject_money"/>
                            </div>
                            </div>
                            <div class="form-group">
                            <label class="col-xs-2 control-label"><label for="AsaRefund_course_id">备注</label></label>
                            <div class="col-xs-10 control-label">
                            <textarea :value="course.variation_memo" class="form-control mb10 eject_remark"></textarea>
                            </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button  v-if='status==10' type="button" class="btn btn-primary paybtnsub" @click='remarks(checkedrole,checkedindex)'>
                            确认
                        </button>
                        <button  v-else type="button" class="btn btn-primary paybtnsub" disabled="disabled">
                            已确认
                        </button>
                        <button type="button" class="btn btn-default" data-dismiss="modal">
                            取消
                        </button>
                         <input type="reset" class="_reset" style="display: none"/>
                    
                    </div>
                    </div>
                  
                </div>
            </div>   
         </div>     
        </div>
        </div>
    <script>
        var date1 = '2017-11';
        var date=new Date;       
        var dataArr = []; 
        var month=date.getMonth()+1;
        month =(month<10 ? "0"+month:month); 
        var date2 = (date.getFullYear().toString()+'-'+month.toString());
        date1 = date1.split('-');
        date1 = parseInt(date1[0]) * 12 + parseInt(date1[1]);
        date2 = date2.split('-');
        date2 = parseInt(date2[0]) * 12 + parseInt(date2[1]);
        var cha = Math.abs(date1 - date2);       
        date.setMonth(date.getMonth()+1, 1)

        for (var i = 0; i < cha; i++) {
            date.setMonth(date.getMonth() - 1);
            var month=date.getMonth()+1;
            month =(month<10 ? "0"+month:month); 
            dataArr.push(date.getFullYear().toString()+month.toString())
        }

     var mon = new Vue({
        el: "#mon",
        data: {          
            month:dataArr,
        },
            computed: {}
      });
    function getQueryString(name) { 
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i"); 
        var r = window.location.search.substr(1).match(reg); 
        if (r != null) return unescape(r[2]); return null; 
    }    
    $('.listul').hide()
    $('.tablelists').hide()
    if(getQueryString("month")==null || getQueryString("month")==''){
            $('.spinner').remove()
        }
    if(getQueryString("month")!=null && getQueryString("month")!=''){
         $('.month').html(getQueryString("month") +' <span class="caret"></span>')
         $.ajax({
            url:'<?php echo $this->createUrl('getIndexData'); ?>',
            type: 'get',
            dataType:'json',
            success:function(data){
               // console.log(data)
                 $('.spinner').remove()
                 if(data.state=='success'){                        
                    tablelist.neibu=data.data[1]
                    tablelist.waibugongsi=data.data[2]
                    tablelist.waibugeren=data.data[3]                   
                     $('.listul').show()
                     $('.tablelists').show()   
                     tablelist.list=data.data
                 }else{
                    resultTip({error: 'warning', msg: data.message});
                 }
               
            },error:function(data){
                resultTip({error: 'warning', msg: '请求错误'});
                 $('.spinner').remove()
            }
        })

        var tablelist = new Vue({
            el: "#tablelist",
            data: {
                neibu:'',
                waibugongsi:'',
                waibugeren:'',
                time:getQueryString("month"),
                status: '<?php echo $this->report->status; ?>',
                list:'' ,
                check:'',
                checkedindex:'',//修改的下标
                checkedrole:''// 修改的角色           
               },
               methods: {
                parsefloat(num) {
                    return parseFloat(num).toFixed(2);
                },
                arrLength(arr) {
                   return Object.keys(arr).length;
                },
                add(add) {
                  var sum = 0;
                  for(var i= 0 ;i< add.length; i++) {
                        sum += parseFloat(add[i] || 0);
                  }
                  return sum;
                },
                and(add) {
                  var sum=''
                  for(var i= 0 ;i< add.length; i++) {
                          sum+=add[i]+'+'
                     }
                     var and='('+sum.substring(0,sum.length-1)+')'
                  return and;
                },
                 len1(add) {
                  var sum=''
                  for(var i= 0 ;i< add.length; i++) {
                          sum+=add[i]
                     }
                  return sum;
                },
                modify(vendor,index,then){
                     $('#myModal').modal('show')
                      this.checkedindex=index
                      this.checkedrole=then
                      this.check=this.list[then][index]
                },
                remarks(checkedrole,checkedindex){

                    var eject_money=[]
                    var eject_remarks=''
                    var addmoney=[]
                    var addremarks=[]
                    var courses=[]
                    $('.eject_money').each(function(){
                        if($(this).val()!=''){
                            eject_money.push($(this).val()) 
                        }
                       addmoney.push($(this).val()) 
                      })
                    $('.eject_remark').each(function(){
                        if($(this).val()!=''){
                           eject_remarks+=$(this).val()+'<br>'
                        } 
                        addremarks.push($(this).val())                    
                      })
                     $('.addke').each(function(){
                         courses.push($(this).text())                    
                      })

                     $('#myModal').modal('hide')
                    let total=this.list[checkedrole][checkedindex].amount-this.list[checkedrole][checkedindex].variation_amout+this.$options.methods.add(eject_money)

                    Vue.set(this.list[checkedrole][checkedindex], 'variation_memo', eject_remarks);
                    Vue.set(this.list[checkedrole][checkedindex], 'variation_amout', this.$options.methods.parsefloat(this.$options.methods.add(eject_money)));
                    Vue.set(this.list[checkedrole][checkedindex], 'amount', total);
                    
                    for(var i=0;i<courses.length;i++){
                        Vue.set(this.list[checkedrole][checkedindex].courses[courses[i]], 'variation_memo',addremarks[i]);
                        Vue.set(this.list[checkedrole][checkedindex].courses[courses[i]], 'variation_amout',addmoney[i]);
                    }
                },
                tijiao(){
                 $('.modifybtn').addClass('disabled').attr('disabled', 'disabled'); 
                 $('#btnsub').html('确认中...')              
                  var settle=[]
                  var amount=[]  
                  var memo=[] 
                  var id=[]  
                  var subject_money=[]
                  var subject_remark=[]
                  var course_money=[]
                  var course_remark=[]
                  var subject_courseId=[]
                  var subject_programId=[]
                  var course_courseId=[]
                  var course_programId=[]
                  var subject_programIdName=[]
                  var subject_courseIdName=[]
                  var course_courseIdName=[]
                  var course_programIdName=[]
                  if( $(".btn-group .btn-success").length<$(".btn-group").length){
                        $('.texts').html('请选择结算或者不结算')
                        $('.modifybtn').removeClass('disabled').removeAttr('disabled');
                        $('#btnsub').html('确认')  
                        return
                  } 
                   
                      $('.vendorId').each(function(){
                        id.push($(this).attr('data-vendor'))                   
                      })
                      $('.settle').each(function(){
                        settle.push($(this).attr('data-settle'))
                      })
   
                    $(".btn-group .btn-success").each(function(){                     
                       settle.push($(this).attr('data-id'))
                    });
                    $(".inputmoney").each(function(){                       
                        amount.push($(this).val())                                               
                    });
                     $(".inputremark").each(function(){
                       memo.push($(this).val()) 
                         
                    });
                     $(".subject_money").each(function(){
                       subject_money.push($(this).attr('data-name'))
                       subject_remark.push($(this).text())  
                       subject_courseId.push($(this).attr('data-courseId'))
                       subject_programId.push($(this).attr('data-programId'))
                       subject_programIdName.push($(this).attr('data-programIdName'))
                       subject_courseIdName.push($(this).attr('data-courseIdName'))
                    });
                    
                      $(".subject_remark").each(function(){
                       course_remark.push($(this).text()) 
                       course_money.push($(this).attr('data-name')) 
                       course_courseId.push($(this).attr('data-courseId'))
                       course_programId.push($(this).attr('data-programId'))
                       course_programIdName.push($(this).attr('data-programIdName'))
                       course_courseIdName.push($(this).attr('data-courseIdName'))
                    });
                
                    var c_amount = subject_money.concat(course_money)
                    var mon= subject_remark.concat(course_remark)
                    var courseId = subject_courseId.concat(course_courseId)
                    var programId= subject_programId.concat(course_programId)

                    var programIdName = subject_programIdName.concat(course_programIdName)
                    var courseIdName= subject_courseIdName.concat(course_courseIdName)
                   
                    var data={}
                    data.id=id
                    data.amount=amount
                    data.memo=memo
                    data.settle=settle
                    for(var i=0;i<c_amount.length;i++){
                      data[c_amount[i]]=mon[i]
                    }
                    for(var i=0;i<programIdName.length;i++){
                     
                      data[programIdName[i]]=programId[i]
                    }
                     for(var i=0;i<courseIdName.length;i++){
                     
                      data[courseIdName[i]]=courseId[i]
                    }
                   

                    $.ajax({
                        url:'<?php echo $this->createUrl('saveIndividual'); ?>',
                        type: 'post',
                        dataType:'json',

                        data:data,
                        success:function(data){
                           $('.modifybtn').removeClass('disabled').removeAttr('disabled');
                           $('#btnsub').html('确认')  
                             if(data.state=='success'){
                                resultTip({"msg": data.message})
                                  setTimeout(function(){
                                       reloadPage(window);
                                 },2000);
                             }else{
                                resultTip({error: 'warning', msg: data.message});
                             }
                           
                        },error:function(data){
                             resultTip({error: 'warning', msg: '请求错误'});
                        }
                     })

                }
            },
        });
               
    }
        
    function tab(obj){
       $(obj).addClass('btn-success').siblings().removeClass('btn-success')
    }
   

    </script>                
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
