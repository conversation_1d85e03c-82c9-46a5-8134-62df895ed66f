<?php

/**
 * This is the model class for table "ivy_user_profile".
 *
 * The followings are the available columns in table 'ivy_user_profile':
 * @property integer $uid
 * @property string $personal_description
 * @property integer $personal_description_level
 * @property integer $user_regdate
 * @property integer $user_regdate_level
 * @property integer $user_lastlogin
 * @property integer $user_lastlogin_level
 * @property string $nickname
 * @property integer $nickname_level
 * @property string $promotioncode
 * @property integer $promotioncode_level
 * @property string $businesscode
 * @property integer $businesscode_level
 * @property integer $businesscodestatus
 * @property integer $businesscodestatus_level
 * @property integer $credits
 * @property integer $credits_level
 * @property string $birthday
 * @property integer $birthday_level
 * @property string $first_name
 * @property integer $first_name_level
 * @property string $last_name
 * @property integer $last_name_level
 * @property integer $user_gender
 * @property integer $user_gender_level
 * @property string $nationality
 * @property integer $nationality_level
 * @property string $location
 * @property integer $location_level
 * @property string $timezone
 * @property integer $timezone_level
 * @property string $occupation_en
 * @property integer $occupation_en_level
 * @property string $knowwokai
 * @property integer $knowwokai_level
 * @property integer $mauto_newsletter
 * @property integer $mauto_newsletter_level
 * @property integer $update_newsletter
 * @property integer $update_newsletter_level
 * @property integer $no_invisible_see
 * @property integer $no_invisible_see_level
 * @property string $mystory
 * @property integer $mystory_level
 * @property string $whycontribute
 * @property integer $whycontribute_level
 * @property string $branch
 * @property integer $branch_level
 * @property integer $staffprimarygroup
 * @property integer $staffprimarygroup_level
 * @property string $occupation_cn
 * @property integer $occupation_cn_level
 * @property integer $multiple_class
 * @property integer $multiple_class_level
 * @property integer $dep_lead
 * @property integer $dep_lead_level
 */
class UserProfile extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return UserProfile the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_user_profile';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('occupation_en, knowwokai', 'safe'),
			array('uid, personal_description_level, user_regdate, user_regdate_level, user_lastlogin, user_lastlogin_level, nickname_level, promotioncode_level, businesscode_level, businesscodestatus, businesscodestatus_level, credits, credits_level, birthday_level, first_name_level, last_name_level, user_gender, user_gender_level, nationality_level, location_level, timezone_level, occupation_en_level, knowwokai_level, mauto_newsletter, mauto_newsletter_level, update_newsletter, update_newsletter_level, no_invisible_see, no_invisible_see_level, mystory_level, whycontribute_level, branch_level, staffprimarygroup, staffprimarygroup_level, occupation_cn_level, multiple_class, multiple_class_level, dep_lead, dep_lead_level', 'numerical', 'integerOnly'=>true),
			array('promotioncode, businesscode, first_name, last_name', 'length', 'max'=>32),
			array('nationality, occupation_en, knowwokai, occupation_cn', 'length', 'max'=>150),
			array('location', 'length', 'max'=>8),
			array('timezone', 'length', 'max'=>255),
			array('branch', 'length', 'max'=>20),
			array('personal_description, nickname, birthday, mystory, whycontribute', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('uid, personal_description, personal_description_level, user_regdate, user_regdate_level, user_lastlogin, user_lastlogin_level, nickname, nickname_level, promotioncode, promotioncode_level, businesscode, businesscode_level, businesscodestatus, businesscodestatus_level, credits, credits_level, birthday, birthday_level, first_name, first_name_level, last_name, last_name_level, user_gender, user_gender_level, nationality, nationality_level, location, location_level, timezone, timezone_level, occupation_en, occupation_en_level, knowwokai, knowwokai_level, mauto_newsletter, mauto_newsletter_level, update_newsletter, update_newsletter_level, no_invisible_see, no_invisible_see_level, mystory, mystory_level, whycontribute, whycontribute_level, branch, branch_level, staffprimarygroup, staffprimarygroup_level, occupation_cn, occupation_cn_level, multiple_class, multiple_class_level, dep_lead, dep_lead_level', 'safe', 'on'=>'search'),

            # appOA 员工管理场景 addStaff editStaff
            array('first_name, last_name, user_gender, nationality, branch, occupation_en', 'required', 'on'=>'addStaff, editStaff, addUser, editUser, checkUser'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'occupation'=>array(self::BELONGS_TO, 'HrPosition', 'occupation_en'),
            'country'=>array(self::BELONGS_TO, 'Country', 'nationality'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'uid' => 'Uid',
			'personal_description' => 'Personal Description',
			'personal_description_level' => 'Personal Description Level',
			'user_regdate' => 'User Regdate',
			'user_regdate_level' => 'User Regdate Level',
			'user_lastlogin' => 'User Lastlogin',
			'user_lastlogin_level' => 'User Lastlogin Level',
			'nickname' => 'Nickname',
			'nickname_level' => 'Nickname Level',
			'promotioncode' => 'Promotioncode',
			'promotioncode_level' => 'Promotioncode Level',
			'businesscode' => 'Businesscode',
			'businesscode_level' => 'Businesscode Level',
			'businesscodestatus' => 'Businesscodestatus',
			'businesscodestatus_level' => 'Businesscodestatus Level',
			'credits' => 'Credits',
			'credits_level' => 'Credits Level',
			'birthday' => 'Birthday',
			'birthday_level' => 'Birthday Level',
			'first_name' => Yii::t("userinfo",'Preferred First Name'),
			'first_name_level' => 'First Name Level',
			'last_name' => Yii::t("userinfo",'Preferred Last Name'),
			'last_name_level' => 'Last Name Level',
			'user_gender' => Yii::t("labels",'Gender'),
			'user_gender_level' => 'User Gender Level',
			'nationality' => Yii::t("labels",'Nationality'),
			'nationality_level' => 'Nationality Level',
			'location' => 'Location',
			'location_level' => 'Location Level',
			'timezone' => 'Timezone',
			'timezone_level' => 'Timezone Level',
			'occupation_en' => Yii::t("labels",'Position'),
			'occupation_en_level' => 'Occupation En Level',
			'knowwokai' => 'Knowwokai',
			'knowwokai_level' => 'Knowwokai Level',
			'mauto_newsletter' => 'Mauto Newsletter',
			'mauto_newsletter_level' => 'Mauto Newsletter Level',
			'update_newsletter' => 'Update Newsletter',
			'update_newsletter_level' => 'Update Newsletter Level',
			'no_invisible_see' => 'No Invisible See',
			'no_invisible_see_level' => 'No Invisible See Level',
			'mystory' => 'Mystory',
			'mystory_level' => 'Mystory Level',
			'whycontribute' => 'Whycontribute',
			'whycontribute_level' => 'Whycontribute Level',
			'branch' => Yii::t("labels",'Campus'),
			'branch_level' => 'Branch Level',
			'staffprimarygroup' => 'Staffprimarygroup',
			'staffprimarygroup_level' => 'Staffprimarygroup Level',
			'occupation_cn' => 'Occupation Cn',
			'occupation_cn_level' => 'Occupation Cn Level',
			'multiple_class' => 'Multiple Class',
			'multiple_class_level' => 'Multiple Class Level',
			'dep_lead' => 'Dep Lead',
			'dep_lead_level' => 'Dep Lead Level',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('uid',$this->uid);
		$criteria->compare('personal_description',$this->personal_description,true);
		$criteria->compare('personal_description_level',$this->personal_description_level);
		$criteria->compare('user_regdate',$this->user_regdate);
		$criteria->compare('user_regdate_level',$this->user_regdate_level);
		$criteria->compare('user_lastlogin',$this->user_lastlogin);
		$criteria->compare('user_lastlogin_level',$this->user_lastlogin_level);
		$criteria->compare('nickname',$this->nickname,true);
		$criteria->compare('nickname_level',$this->nickname_level);
		$criteria->compare('promotioncode',$this->promotioncode,true);
		$criteria->compare('promotioncode_level',$this->promotioncode_level);
		$criteria->compare('businesscode',$this->businesscode,true);
		$criteria->compare('businesscode_level',$this->businesscode_level);
		$criteria->compare('businesscodestatus',$this->businesscodestatus);
		$criteria->compare('businesscodestatus_level',$this->businesscodestatus_level);
		$criteria->compare('credits',$this->credits);
		$criteria->compare('credits_level',$this->credits_level);
		$criteria->compare('birthday',$this->birthday,true);
		$criteria->compare('birthday_level',$this->birthday_level);
		$criteria->compare('first_name',$this->first_name,true);
		$criteria->compare('first_name_level',$this->first_name_level);
		$criteria->compare('last_name',$this->last_name,true);
		$criteria->compare('last_name_level',$this->last_name_level);
		$criteria->compare('user_gender',$this->user_gender);
		$criteria->compare('user_gender_level',$this->user_gender_level);
		$criteria->compare('nationality',$this->nationality,true);
		$criteria->compare('nationality_level',$this->nationality_level);
		$criteria->compare('location',$this->location,true);
		$criteria->compare('location_level',$this->location_level);
		$criteria->compare('timezone',$this->timezone,true);
		$criteria->compare('timezone_level',$this->timezone_level);
		$criteria->compare('occupation_en',$this->occupation_en,true);
		$criteria->compare('occupation_en_level',$this->occupation_en_level);
		$criteria->compare('knowwokai',$this->knowwokai,true);
		$criteria->compare('knowwokai_level',$this->knowwokai_level);
		$criteria->compare('mauto_newsletter',$this->mauto_newsletter);
		$criteria->compare('mauto_newsletter_level',$this->mauto_newsletter_level);
		$criteria->compare('update_newsletter',$this->update_newsletter);
		$criteria->compare('update_newsletter_level',$this->update_newsletter_level);
		$criteria->compare('no_invisible_see',$this->no_invisible_see);
		$criteria->compare('no_invisible_see_level',$this->no_invisible_see_level);
		$criteria->compare('mystory',$this->mystory,true);
		$criteria->compare('mystory_level',$this->mystory_level);
		$criteria->compare('whycontribute',$this->whycontribute,true);
		$criteria->compare('whycontribute_level',$this->whycontribute_level);
		$criteria->compare('branch',$this->branch,true);
		$criteria->compare('branch_level',$this->branch_level);
		$criteria->compare('staffprimarygroup',$this->staffprimarygroup);
		$criteria->compare('staffprimarygroup_level',$this->staffprimarygroup_level);
		$criteria->compare('occupation_cn',$this->occupation_cn,true);
		$criteria->compare('occupation_cn_level',$this->occupation_cn_level);
		$criteria->compare('multiple_class',$this->multiple_class);
		$criteria->compare('multiple_class_level',$this->multiple_class_level);
		$criteria->compare('dep_lead',$this->dep_lead);
		$criteria->compare('dep_lead_level',$this->dep_lead_level);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

    public function getGender()
    {
        $genders = OA::getChildGenderList();
        return in_array($this->user_gender, array_keys($genders)) ? $genders[$this->user_gender] : '';
    }
}