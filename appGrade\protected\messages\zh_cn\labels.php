<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yiic message' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE, this file must be saved in UTF-8 encoding.
 *
 * @version $Id: $
 */
return array (
  'Class' => '班级',
  'Access Code' => '访客口令',
  'Address' => '地址',
  'Alternative Lunch Menu' => '特殊餐',
  'Bcc' => '密送',
  'Campus' => '校园',
  'Cc' => '抄送至',
  'Cellphone' => '手机',
  'Mobile Phone' => '手机',
  'Child Name' => '姓名',
  'Chinese Name' => '中文名字',
  'City' => '城市',
  'Company' => '公司',
  'Content' => '内容',
  'Date of Birth' => '生日',
  'District' => '区',
  'Email' => '邮件',
  'Emergency Contact' => '紧急联系人',
  'Father\'s Email Address' => '父亲电邮地址',
  'Father\'s Mobile Number' => '父亲手机号',
  'Fax' => '传真',
  'First Name' => '英文名',
  'Gender' => '性别',
  'Home Telephone Number' => '家庭电话',
  'Hospital' => '医院或诊所',
  'Invoice Title' => '账单标题',
  'Job' => '职位',
  'Language' => '母语',
  'Last Name' => '英文姓',
  'Mother\'s Email Address' => '母亲电邮地址',
  'Mother\'s Mobile Number' => '母亲手机号',
  'Name' => '姓名',
  'Nationality' => '国籍',
  'Preferred Name' => '昵称/英文名',
  'Operator' => '操作人',  
  'Paid Date' => '付款时间',
  'Photo' => '照片',
  'Photo Authorization' => '照片授权',
  'Pick Up Contact' => '接送授权',
  'Postcode' => '邮政编码',
  'Privacy' => '隐私',
  'Province' => '省份',
  'Replyto' => '回复至',
  'Special Needs' => '特殊需求',
  'Subject' => '主题',
  'Tel' => '电话',
  'Telephone' => '家庭电话',
  'To' => '发送至',
  'Time' => '增加时间',
  'Educational Student ID' => '教育ID号',
  'Class list is not available at this time.' => '分班还未完成，请耐心等待',
    "If any of the information above is inaccurate, <NAME_EMAIL> to update them." => "如果任何消息不准确, 请发邮件到 <EMAIL> 进行修改",
    'If any of the information above is inaccurate, <NAME_EMAIL> to update them.' => '如果任何消息不准确, 请发邮件到 <EMAIL> 进行修改',
    'If any of the information above is inaccurate, <NAME_EMAIL> to update them.' => '如果任何消息不准确, 请发邮件到 <EMAIL> 进行修改',
    "If any of the information above is inaccurate, <NAME_EMAIL> to update them." => "如果任何消息不准确, 请发邮件到 <EMAIL> 进行修改",
    "If any of the information above is inaccurate, <NAME_EMAIL> to update them." => "如果任何消息不准确, 请发邮件到 <EMAIL> 进行修改",
    "If any of the information above is inaccurate, <NAME_EMAIL> to update them." => "如果任何消息不准确, 请发邮件到 <EMAIL> 进行修改",
);
