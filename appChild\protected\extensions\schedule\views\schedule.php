<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta name="language" content="en" />
</head>
<body>
<script language="javascript">
    function viewSchedule(_this)
    {
        window.location = "<?php echo Yii::app()->getController()->createUrl('//portfolio/portfolio/schedule', array('classid'=>$class_model->classid));?>&weeknum="+_this.value;
    }
</script>
<?php $tk = new HtoolKits();?>
<div id="schedule" class="pop-box">
    <h5><?php echo addColon(Yii::t("portfolio", 'Weekly Schedule'));?><?php echo $class_model->title;?></h5>
    <div class="sh-tools">
        <div class="fl"><?php echo CHtml::dropDownList('weeknum', $weeknum, $weeks, array('onchange'=>'viewSchedule(this)'));?></div>
        <div class="fr" style="line-height: 24px; margin-right: 20px;">
            <?php
            $minw = min(array_keys($weeks));
            $maxw = max(array_keys($weeks));
            ?>
            <?php if ($weeknum != $minw):?>
            <?php
            $linkText = "<span class='prev'>" . Yii::t("portfolio", "Prev Week") ."</span>";
            echo CHtml::link($linkText, Yii::app()->getController()->createUrl('//portfolio/portfolio/schedule', array('classid'=>$class_model->classid, 'weeknum'=>$weeknum-1)), array('class'=>'alink'))?>
            <?php endif;?>
            <?php if ($weeknum != $maxw):?>
            <?php
            $linkText = "<span class='next'>" . Yii::t("portfolio", "Next Week") ."</span>";
            echo CHtml::link($linkText, Yii::app()->getController()->createUrl('//portfolio/portfolio/schedule', array('classid'=>$class_model->classid, 'weeknum'=>$weeknum+1)), array('class'=>'alink'))?>
            <?php endif;?>
        </div>
        <div class="clear"></div>
    </div>
    <div id="sLeft">
        <ul><li class="hli"><?php echo Yii::t("portfolio","Time");?></li></ul>
        <ul class="hul">
            <?php
            foreach($tArr as $k=>$t){
                ?>
            <li class="tb <?php echo $t['cssname'];?> <?php if (isset($t['end'])){?>tbend<?php }?>">
            <?php
                if (isset($t['part'])){
            ?>
                <div class="tbt"><?php echo $t['t'];?></div>
            <?php }?>
            </li>
            <?php }?>
        </ul>
    </div>
    <div id="sRight">
        <ul>
            <?php for ($i=1;$i<=5;$i++){?>
            <li class="hli fl thx"><?php echo Yii::app()->locale->getWeekDayName($i);?></li>
            <?php }?>
            <li class="clear"></li>
        </ul>
        <ul>
            <?php for ($i=1;$i<=5;$i++){?>
            <li class="fl thx">
                <?php
                if (isset($scheduleday[$i])){
                foreach ($scheduleday[$i] as $s){
                    if ($s['model']->activity_id != 0 && $s['model']->activityInfo->is_routine != 1){
                        $activity_html = '<div style="overflow-x:hidden; overflow-y:auto; padding: 0 3px; text-align:left;">1. '.$tk->getContentByLang($s['model']->activityInfo->cn_title, $s['model']->activityInfo->en_title).'<br><br></div>';
                    }
                    else {
                        $activity_html = '';
                    }
                    ?>
                <div class="sT <?php echo strtolower(str_replace("#","-","bg".$s['model']->color));?>" style="height:<?php echo $s['h'];?>px;">
                    <div class="sT-tit"><?php echo $s['model']->title;?></div>
                    <?php echo $activity_html;?>
                    <div class="sT-txt"><?php echo nl2br($s['model']->content);?></div>
                </div>
                <?php }}else{?>
                &nbsp;
                <?php }?>
            </li>
            <?php }?>
            <li class="clear"></li>
        </ul>
    </div>
    <div class="clear"></div>
</div>
    </body>
</html>