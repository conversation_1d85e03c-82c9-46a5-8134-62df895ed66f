
<?php
/*
 * <PERSON><PERSON>
 * 2012-8-9
 *
 */
?>

<div class="print-info-wrapper">

    <div class="print-info" id="print_info">

        <style type="text/css">
            .print-info .line {
                background: #000000;
                width: 98%;
                border-bottom: 1px solid #999999;
                margin: 5px;
                padding: 0px;
                clear: both;
                height: 0;
                _height: 0;
                line-height: 0;
                font-size: 0;
                overflow: hidden;
                margin: 5px !important;
                padding: 0px !important;
            }

            .print-info .bolder2 {
                border-bottom: 2px solid #0A0A0A;
                margin: 10px 5px;
            }

            .print-info .bolder3 {
                border-bottom: 3px solid #0A0A0A;
                margin: 10px 5px;
            }

            .print-info .bolder5 {
                border-bottom: 5px solid #0A0A0A;
                margin: 10px 5px;
            }

            .print-info .shorter {
                width: 42%;
                float: right;
            }

            .school-info {
                margin: 10px;
            }

            .school-info li {
                margin-bottom: 10px;
            }

            .school-info .address {
                float: left;
                width: 430px;
                text-align: left;
            }

            .school-info .logo {
                float: right;
            }

            .child-info {
                margin: 10px;
            }

            .child-info .title {
                font-size: 18px;
                font-weight: bolder;
                text-align: center;
                clear: both;
            }

            .child-info .content li {
                width: 300px;
                float: left;
                margin: 10px;
                height: 18px;
            }

            .child-info .content li span,.invoice-wrapper ul li span {
                display: inline-block;
                width: 130px;
                font-weight: bolder;
                text-align: right;
                padding-right: 10px;
            }

            .invoice-info {
                margin: 10px;
            }

            .invoice-wrapper ul li {
                float: left;
                width: 320px;
                margin: 10px 0;
                height: 18px;
            }

            /***     bank account       **/
            .transfer-accounts li {
                float: left;
                margin: 10px;
            }

            .transfer-accounts .bi_title {
                width: 120px;
                text-align: left;
            }

            .transfer-accounts .bi_content {
                width: 420px;
                text-align: left;
            }
        </style>
        <div class="school-info">
            <ul>
                <li class="address"><?php echo $base_info['school_desc']; ?>
                </li>
                <li class="logo"><?php echo CHtml::image($base_info['school_logo_url']); ?>
                </li>
            </ul>
            <div class="clearfix"></div>
            <ul>
                <li class="line bolder3"></li>
            </ul>
        </div>
        <div class="clearfix"></div>
        <div class="child-info">
            <ul class="title">
                <li><?php echo Yii::t("payment", 'Invoice'); ?></li>
            </ul>
            <ul class="content">
                <li><span><?php echo Yii::t("labels", 'Child Name'); ?> </span> <?php echo $base_info['child_name']; ?>
                </li>
                <li><span><?php echo Yii::t("navigations", 'General Credit'); ?> </span>
                    <?php echo addColon(Yii::t("payment","Balance")).$base_info['credit']; ?>
                </li>
                <li><span><?php echo Yii::t("labels", 'Class'); ?> </span> <?php echo $base_info['class_name']; ?>
                </li>
                <?php if (!in_array($base_info['school_id'], array('BJ_DS', 'BJ_SLT', 'BJ_QFF'))) : ?>
                <li><span><?php echo Yii::t("navigations", 'Tuition Deposit'); ?> </span> <?php echo addColon(Yii::t("payment","Balance")).$base_info['deposit']; ?>
                </li>
                <?php endif;?>
            </ul>
            <div class="clearfix"></div>

        </div>
        <div class="clearfix"></div>
        <div class="invoice-wrapper">
            <?php
            if (!empty($invoice_list)) {
                $nf = new CNumberFormatter('zh-cn');
                foreach ($invoice_list as $invoice) {
                    ?>
                    <div class="invoice-info">
                        <ul>
                            <li class="line bolder2"></li>
                        </ul>
                        <ul>
                            <li><span style="width:auto;">
                                    <?php
                                    if (!empty($invoice->paymentTypeTitle)) {
                                        echo '[' . $invoice->paymentTypeTitle . ']';
                                    }
                                    ?>
                                </span> <?php echo $invoice->title; ?>
                            </li>
                            <li><span><?php echo Yii::t("payment", 'Due Date'); ?> </span> <?php echo $invoice->dueDate; ?>
                            </li>
                        </ul>
                        <ul>
                            <li class="line"></li>
                        </ul>
                        <ul>
                            <li><span><?php echo sprintf("%010d", $invoice->invoice_id); ?> </span>
                            </li>
                            <!-- 启明星隐藏账单区间 -->
                            <?php if (!in_array($invoice->schoolid, array('BJ_DS', 'BJ_SLT', 'BJ_QFF'))) : ?>
                            <li><span><?php echo Yii::t("payment", 'Duration'); ?> </span>
                                <?php echo $invoice->durDate; ?>
                            </li>
                            <?php endif ?>
                        </ul>
                        <ul>
                            <li class="line"></li>
                        </ul>
                        <?php if ($invoice->discount_id) { ?>
                            <ul>
                                <li><?php echo $invoice->discountTitle . '<' . $invoice->discountPercent . '>'; ?>
                                </li>
                                <li><span><?php echo Yii::t("payment", 'Amount after discount'); ?> </span>
                                    <?php echo $nf->format('#,##0.00', $invoice->original_amount); ?></li>
                            </ul>
                        <?php } else { ?>

                            <ul>
                                <li></li>
                                <li><span><?php echo Yii::t("payment", 'Amount'); ?> </span> <?php echo $nf->format('#,##0.00', $invoice->original_amount); ?>
                                </li>
                            </ul>

                            <?php
                        }
                        if ($invoice->depositAmount) {
                            ?>
                            <ul>
                                <li><span>[<?php echo Yii::t("payment", 'Deposit'); ?>]</span></li>
                                <li><span><?php echo Yii::t("payment", 'Amount Paid'); ?> </span> <?php echo $nf->format('#,##0.00', $invoice->depositAmount); ?>
                                </li>
                            </ul>

                            <?php
                        }

                        if ($invoice->invoiceTransaction) {

                            foreach ($invoice->invoiceTransaction as $tran) {
                                ?>
                                <ul>
                                    <li><span style="width:140px;">[<?php echo $tran->transactionTypeTitle; ?>]</span><?php echo $tran->paidTime; ?></li>
                                    <li><span><?php echo Yii::t("payment", 'Amount Paid'); ?> </span> <?php echo $nf->format('#,##0.00', $tran->amount); ?>
                                    </li>
                                </ul>
                                <?php
                            }
                        }
                        ?>
                        <ul>
                            <li class="line shorter"></li>
                        </ul>
                        <ul>
                            <li></li>
                            <li><span><?php echo Yii::t("payment", 'Subtotal'); ?> </span> <?php echo $nf->format('#,##0.00', $invoice->dueAmount); ?>
                            </li>
                        </ul>
                    </div>
                    <?php
                }
                ?>
                <div>
                    <ul>
                        <li class="line shorter bolder5"></li>
                    </ul>
                    <ul>
                        <li></li>
                        <li><span><?php echo Yii::t("payment", 'Total'); ?> </span> <?php echo $nf->format('#,##0.00', $unpaid_total); ?>
                        </li>
                    </ul>
                    <ul>
                        <li style="width: 240px; margin: 10px; float: right;"><?php echo addColon(Yii::t("payment", 'Office Signature')); ?></li>
                        <li style="width: 240px; margin: 10px; float: right;"><?php echo addColon(Yii::t("payment", 'Parent Signature')); ?></li>
                    </ul>
                </div>

                <?php
            } else {
                ?>
                <div class="no-data"></div>
            <?php } ?>

        </div>

        <?php if (!empty($bank_info)) { ?>
            <div class="transfer-accounts">
                <ul>
                    <li class="line bolder3"></li>
                </ul>
                <!--银行信息-->
                <?php foreach ($bank_info as $bank) { ?>
                    <ul>
                        <li class="bi_title clearfix"><?php
            if ($bank['title_en']) {
                echo '<b>' . $bank['title_en'] . '</b>';
            }
                    ?> <br> <?php
                    if ($bank['title_cn']) {
                        echo '<b>' . $bank['title_cn'] . '</b>';
                    }
                    ?>
                        </li>
                        <li class="bi_content">
                            <?php echo $bank['content'];?>
                        </li>
                    </ul>
                    <div class="clear"></div>
                <?php } ?>
            </div>

        <?php } ?>



    </div>
</div>
