<?php

class Semester<PERSON>ontroller extends TeachBasedController
{
    public $selectedClassId=0;
    public $selectedSemester='';
    public $selectedTask='';
    public $selectedChildId=0;
    public $semesters=array(1,2);
    public $ivyAccessToken = '';
    public $dsAccessToken = '';
    public $accessToken = '';

	public $printFW = array();

    // 访问action的初级权限
    public $actionAccessAuths = array(
        'index'                             => 'o_T_Access',
        'SetPlanStatus'                     => 'o_T_Adm_Common',
        'SetReportStatus'                   => 'o_T_Adm_Common',
        'GetChildSemesterReport'            => 'o_T_Access',
        'GetChildSemesterReportItem'        => 'o_T_Access',
        'ReportSave'                        => 'o_T_Adm_Common',
        'GetChildData'                      => 'o_T_Access',
        'SaveDuration'                      => 'o_T_Adm_Common',
        'SaveTimeslot'                      => 'o_T_Adm_Common',
        'GetTimeslot'                       => 'o_T_Access',
        'DelTimeslot'                       => 'o_T_Adm_Common',
        'DelTimeslotSaveTimeslotwithDay'    => 'o_T_Adm_Common',
        'GetTimeslotInfo'                   => 'o_T_Access',
        'SaveTimeslotwithItem'              => 'o_T_Adm_Common',
        'GetDayInfo'                        => 'o_T_Access',
        'ClearChild'                        => 'o_T_Adm_Common',
        'PrintPtc'                          => 'o_T_Access',
    );

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'teaching';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Teaching Tasks');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mteaching/semester/index');


        Yii::import('common.models.portfolio.*');
    }

    public function beforeAction($action){
        if(!parent::beforeAction($action)){
            return false;
        }

        $this->selectedClassId = Yii::app()->request->getParam('classid', 0);
        $this->selectedSemester = Yii::app()->request->getParam('semester', 0);
        $this->selectedTask = Yii::app()->request->getParam('task', 0);
        $this->selectedChildId = Yii::app()->request->getParam('childid', 0);

        return true;
    }

	public function actionIndex($classid=0, $semester='', $task='')
	{
        parent::initExt();

        //该校园或班级不支持此功能
        $stopped = array(
            Branch::PROGRAM_DAYSTAR => array(
                'schecklist'//, 'ptc'
            )
        );

        //校园支持本功能
        $stopping = false;

        if(isset($stopped[$this->branchObj->type])){
            if(in_array($task, $stopped[$this->branchObj->type] )){
                //校园不支持本功能
                $stopping = true;
            }
        }
        $taskData = array();
        $taskData['stopping'] = $stopping;

        if(!$stopping){
            if($classid && $task){
                $taskfun = 'task'.ucfirst($task);
                $taskData = $this->$taskfun();
            }
        }
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/plupload/plupload.full.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/cropperv1.5.6/cropper.min.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl.'/base/js/cropperv1.5.6/cropper.min.css');

        $schoolGroup = $this->branchObj->group;

        $this->render('index', array(
            'taskData'=>$taskData,
            'schoolGroup'=>$schoolGroup,
            'classid'=>$classid,
            'semester'=>$semester,
            'type'=>$this->branchObj->type,
        ));
	}

    public function taskSreport()
    {
        Yii::import('common.models.portfolio.*');

        $crit = new CDbCriteria();
        $crit->compare('classid', $this->selectedClassId);
        $crit->compare('semester', $this->selectedSemester);
        $crit->compare('stat', 20);
        $crit->index = 'childid';
        $taskData['onlined'] = array_keys(SReport::model()->findAll($crit));

        $crit = new CDbCriteria();
        $crit->compare('classid', $this->selectedClassId);
        $crit->compare('semester', $this->selectedSemester);
        $crit->compare('stat', 16);
        $crit->index = 'childid';
        $taskData['pass'] = array_keys(SReport::model()->findAll($crit));

        $crit = new CDbCriteria();
        $crit->compare('classid', $this->selectedClassId);
        $crit->compare('semester', $this->selectedSemester);
        $crit->compare('stat', 14);
        $crit->index = 'childid';
        $overrule = SReport::model()->findAll($crit);

        $overruleText = array();
        if($overrule){
            $taskData['overrule'] = array_keys($overrule);
            foreach ($overrule as $childid => $val){
                $overruleText[$childid] = $val->overrule;
            }
        }
        $taskData['overruleText'] = $overruleText;
        $crits = new CDbCriteria();
        $crits->compare('classid', $this->selectedClassId);
        $crits->compare('semester', $this->selectedSemester);
        $crits->compare('stat', 15);
        $crits->index = 'childid';
        $taskData['submits'] = array_keys(SReport::model()->findAll($crits));

        $taskData['children'] = $this->getNameList($this->selectedClassId);

        $taskData['calendarData'] = $this->getYidStartYearByClassIdAndBranchId($this->selectedClassId, $this->branchId);
        $taskData['templateId'] = $this->getTemplateId($taskData['calendarData']['startyear'], $this->selectedClassId);
        $taskData['previewurl'] = $taskData['templateId'] == 'Ivy03' ? 'previewReport2' : 'previewReport';
        // IBS 学校特殊处理
        if ($this->branchObj->group == 20 && ($taskData['calendarData']['startyear'] + $this->selectedSemester) > 2020) {
            $taskData['previewurl'] = 'previewReport3';
        }

        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');

        return $taskData;
    }

    public function getYidStartYearByClassIdAndBranchId($classid, $branchId){
        $result = Yii::app()->db->createCommand()
            ->select('c.yid as yid, y.startyear as startyear')
            ->from('ivy_class_list c')
            ->join('ivy_calendar_yearly y', 'y.yid=c.yid')
            ->where('c.classid=:classid and c.schoolid=:schoolid',
                array(
                    ':classid'=>$classid,
                    ':schoolid'=>$branchId
                ))
            ->queryRow();
        return $result;
    }

    //设置预约计划状态
    public function actionSetPlanStatus(){
        /**
         * POST过来变量1. status, 检查必须是 online 或者 offline 之一
         * POST过来变量2. plan_id, 预约计划主键
         *
         * 1. 根据plan_id 查出 ParentMeetingPlan 的AR
         * 2. 判断school_id是否一致 $this->branchId
         * 3. 设置状态字段 status 的值，online 对应 SReport::STATUS_ONLINE; offline 对应 SReport::STATUS_OFFLINE
         * 4. 返回data值json数据格式 {id: xxx, status: zzz}
         **/
        if($this->checkTeachers()){
            Yii::import('common.models.ptc.*');
            $status = Yii::app()->request->getPost('status', '');
            $plan_id = Yii::app()->request->getPost('plan_id', 0);
            $item = ParentMeetingPlan::model()->findByPk($plan_id);

            if($item && $item->items) {
                $schoolid = $item->school_id;
                if ($status == "online" || "offline") {
                    if ($schoolid == $this->branchId) {
                        if ($status == "online") {
                            $item->status = ParentMeetingPlan::STATUS_ONLINE;
                        } else {
                            $item->status = ParentMeetingPlan::STATUS_OFFLINE;
                        }
                    }
                }
                if ($item->save()) {
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('teaching', '设置成功！'));
                    $this->addMessage('data', array('id' => $item->id, 'status' => $item->status));
                } else {
                    $this->addMessage('state', 'fail');
                    $err = current($item->getErrors());
                    $this->addMessage('message', $err[0]);
                }
            }else {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '未设置时间段');
            }
        }
        else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('teaching', '班级错误！'));
        }
		$this->showMessage();
    }

        //设置学期报告状态
    public function actionSetReportStatus(){
        /**
         * POST过来变量1. status, 检查必须是 online 或者 offline 之一
         * POST过来变量2. report_id, 学期报告主键
         *
         * 1. 根据report_id 查出 SReport 的AR
         * 2. 判断schoolid是否一致 $this->branchId
         * 3. 设置状态字段 stat 的值，online 对应 SReport::STATUS_ONLINE; offline 对应 SReport::STATUS_OFFLINE
         * 4. 返回data值json数据格式 {id: xxx, childid: yyy, stat: zzz}
         **/
        Yii::import('common.models.portfolio.*');
		$status = Yii::app()->request->getPost('status', '');
		$report_id = Yii::app()->request->getPost('report_id', 0);
		$item = SReport::model()->with('items')->findByPk($report_id);

		$schoolid = $item->schoolid;

		if($status == "online" || "offline" || "pass" || "quxiao"){

			if($schoolid == $this->branchId){
				if($status == "online"){
					$item->stat = SReport::STATUS_CHECK;
				}else if($status == "pass"){
				    if($item->stat != SReport::STATUS_PASS){
                        $this->addMessage('state','fail');
                        $this->addMessage('message', '非法操作！');
                        $this->showMessage();
                    }
					$item->stat = SReport::STATUS_ONLINE;
                }else if($status == "offline"){
                    $item->stat = SReport::STATUS_PASS;
                }else{
                    $item->stat = SReport::STATUS_OFFLINE;
                }

                if($item->template_id == 'Ivy01' || $item->template_id == 'Ivy03'){
                    //检查上线是否符合标准
                    // if($status == "online"){
                    //     if($this->branchObj->group != 10){
                    //         $finished = ( count($item->items) == 6 ) ? true : false;
                    //         $validate['itemTotal'] = count($item->items);
                    //         if($finished){
                    //             $validate['picTotal'] = $validate['contentTotal'] = $validate['ldTotal'] = 0;
                    //             foreach($item->items as $_item){
                    //                 $validate['picTotal']       += empty($_item->media_id) ? 0 : 1;
                    //                 $validate['contentTotal']   += empty($_item->content) ? 0 : 1;
                    //                 $validate['ldTotal']        += empty($_item->ld_id) ? 0 : 1;
                    //             }
                    //             if($validate['picTotal'] < 5 || $validate['contentTotal'] < 4 || $validate['ldTotal'] < 2 ){
                    //                 $finished = false;
                    //             }
                    //         }

                    //         if(!$finished){
                    //             $this->addMessage('state', 'fail');
                    //             $this->addMessage('message', Yii::t('teaching',
                    //                 'Report items not finished, can not make online.'));
                    //             $this->addMessage('data', $validate);
                    //             $this->showMessage();
                    //         }
                    //     }else if($this->branchObj->group == 10 && empty($item->custom_pdf)){
                    //         $this->addMessage('state', 'fail');
                    //         $this->addMessage('message', Yii::t('teaching', '学期报告未全部完成或没有学期报告的PDF文件，请完成后再上线'));
                    //         $this->showMessage();
                    //     }
                    // }

                    if($item->save()){
                        Yii::log(time() . '-'. Yii::app()->user->id . ' - ' . $item->id  .' - ' . $item->stat, 'info', 'semesterSetReportStatus');
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', Yii::t('teaching', '设置成功！'));
                        $this->addMessage('data', array('id' => $item->id, 'childid' => $item->childid, 'stat' => $item->stat));
                    }else{
                        $this->addMessage('state', 'fail');
                        $err = current($item->getErrors());
                        $this->addMessage('message', $err[0]);
                    }

                    $this->showMessage();
                }
            }
		}

        $this->addMessage('state','fail');
        $this->addMessage('message', 'no permission');
        $this->showMessage();

    }

    public function actionSetReportPdf()
    {
        set_time_limit(300);
        $report_id = Yii::app()->request->getPost('report_id', 0);
        if (Yii::app()->request->isPostRequest) {
            $item = SReport::model()->findByPk($report_id);
            $file = CUploadedFile::getInstanceByName('file');
            $oldFile = $item->custom_pdf;
            $filePath = Yii::app()->params['xoopsVarPath'] . '/semester_report/custom_pdf/';

            if ($file) {
                $fileName = strtolower('products_' . uniqid() . '.' . $file->getExtensionName());
                $file->saveAs($filePath . $fileName);

                $item->custom = 1;
                $item->custom_pdf = $fileName;
                $item->timestamp = time();
                $item->uid = Yii::app()->user->id;

                if($item->save()){
                    Yii::log(time() . '-'. Yii::app()->user->id . ' - '. $item->id . ' - ' . $item->stat, 'info', 'semesterSetReportPdf');
                    if($oldFile){
                        unlink($filePath . $oldFile);
                    }
                    echo CJSON::encode(array(
                        'pdfUrl' => $this->createUrl('showPdf', array('reportId'=>$item->id)),
                    ));
                }
            }
        }
    }

    public function actionSetReportPdfDs()
    {

        set_time_limit(300);
        $report_id = Yii::app()->request->getPost('report_id', 0);

        if (Yii::app()->request->isPostRequest) {
            Yii::import('common.models.reportCards.*');
            $item = ReportsData::model()->findByPk($report_id);
            $file = CUploadedFile::getInstanceByName('file');
            $oldFile = $item->custom_pdf;
            $filePath = Yii::app()->params['xoopsVarPath'] . '/semester_report/custom_pdf/';

            if ($file) {
                $fileName = strtolower('ds_products_' . uniqid() . '.' . $file->getExtensionName());
                $file->saveAs($filePath . $fileName);

                $item->custom = 1;
                $item->custom_pdf = $fileName;
                $item->updated = time();
                $item->updated_user = Yii::app()->user->id;

                if($item->save()){
                    $templateId = $this->getTemplateId($item->startyear, $this->selectedClassId);
                    $criteria = new CDbCriteria();
                    $criteria->compare('schoolid', 'BJ_DS');
                    $criteria->compare('classid', $item->class_id);
                    $criteria->compare('childid', $item->child_id);
                    $criteria->compare('startyear', $item->startyear);
                    $criteria->compare('semester', $item->semester);
                    $srModel = SReport::model()->find($criteria);
                    if(!$srModel){
                        $criteria = new CDbCriteria();
                        $criteria->compare('startyear', $item->startyear);
                        $criteria->compare('branchid', 'BJ_DS');
                        $schoolModel = CalendarSchool::model()->find($criteria);
                        $srModel = new SReport();
                        $srModel->schoolid = 'BJ_DS';
                        $srModel->classid = $item->class_id;
                        $srModel->childid = $item->child_id;
                        $srModel->yid = $schoolModel->yid;
                        $srModel->startyear = $item->startyear;
                        $srModel->semester = $item->semester;
                        $srModel->template_id = $templateId;
                        $srModel->stat = 10;
                        $srModel->pdf_file = 0;
                    }

                    $srModel->custom = 1;
                    $srModel->custom_pdf = $fileName;
                    $srModel->timestamp = time();
                    $srModel->uid = Yii::app()->user->id;
                    if(!$srModel->save()){
                        $err = current($srModel->getErrors());
                        Yii::msg($err[0]);
                    }
                    Yii::log(time() . '-'. Yii::app()->user->id . ' - '. $srModel->id . ' - ' . $srModel->stat, 'info', 'semesterSetReportPdfDs');
                    if($oldFile){
                        unlink($filePath . $oldFile);
                    }
                    echo CJSON::encode(array(
                        'pdfUrl' => $this->createUrl('showPdfDs', array('reportId'=>$item->id)),
                    ));
                }
            }
        }
    }

    public function actionShowPdf($reportId = 0)
    {
        if($reportId){
            $speportModel = SReport::model()->findByPk($reportId);
            ob_end_clean();
            $file_dir = Yii::app()->params['xoopsVarPath'] . '/semester_report/custom_pdf/';
            if(file_exists($file_dir. $speportModel->custom_pdf)) {
                $fileres = file_get_contents($file_dir . $speportModel->custom_pdf);
                header('Content-type: application/pdf');
                echo $fileres;
            }
            else {
                $oss = CommonUtils::initOSS('private');
                $ossObj = $oss->get_object('semester_report/custom_pdf/'.$speportModel->custom_pdf);
                if ( $ossObj->status == 200) {
                    header(sprintf("content-length: %s", $ossObj->header['content-length']));
                    header('Content-type: application/pdf');
                    $bytes = $ossObj->body;
                    echo $bytes;
                } else {
                    die('cannot find file on OSS');
                }
            }

            echo $fileres;

        }
    }

    public function actionShowPdfDs($reportId = 0)
    {
        Yii::import('common.models.reportCards.*');
        if($reportId){
            $speportModel = ReportsData::model()->findByPk($reportId);
            ob_end_clean();
            $file_dir = Yii::app()->params['xoopsVarPath'] . '/semester_report/custom_pdf/';

            if (substr($speportModel->custom_pdf, 0, 4) == 'http') {
                echo '<script>location.href="'.$speportModel->custom_pdf.'";</script>';
            } else {
                if(file_exists($file_dir. $speportModel->custom_pdf)) {
                    $fileres = file_get_contents($file_dir . $speportModel->custom_pdf);
                    header('Content-type: application/pdf');
                    echo $fileres;
                }
                else {
                    $oss = CommonUtils::initOSS('private');
                    $ossObj = $oss->get_object('semester_report/custom_pdf/'.$speportModel->custom_pdf);
                    if ( $ossObj->status == 200) {
                        header(sprintf("content-length: %s", $ossObj->header['content-length']));
                        header('Content-type: application/pdf');
                        $bytes = $ossObj->body;
                        echo $bytes;
                    } else {
                        die('cannot find file on OSS');
                    }
                }
            }
        }
    }

    //取学期报告数据
    public function actionGetChildSemesterReport(){
        Yii::import('common.models.portfolio.*');
        Yii::import('common.models.childdev.DevData');
        $childId = Yii::app()->request->getPost('childid', 0);
        //取yid和startyear
        $extraData = $this->getYidStartYearByClassIdAndBranchId($this->selectedClassId, $this->branchId);
        $templateId  = $this->getTemplateId($extraData['startyear'], $this->selectedClassId);

        $params = array(
            'schoolid' => $this->branchId,
            'classid' => $this->selectedClassId,
            'semester' => $this->selectedSemester,
            'template_id' => $templateId,
            'childid' => $childId
        );

        $display = $this->selectedSemester == 1 ? 'fall_display' : 'spring_display';
        $dev = DevData::model()->countByAttributes(array('classid'=>$this->selectedClassId, 'childid'=>$childId, $display=>2));

        // if(!$dev && $this->branchObj->group != 10){
        //     $this->addMessage('state','fail');
        //     $this->addMessage('message', Yii::t('teaching', 'Please complete the Child Development Checklist and make it Online first.'));
        //     $this->showMessage();
        // }

        // todo 查询用户添加的结构性内容 IA专用
        $report = SReport::model()->with('items')->findByAttributes($params);
        if(is_null($report)){
            $report = new SReport();

            if(empty($extraData)){
                $this->addMessage('state','fail');
                $this->addMessage('message', 'invalid request');
                $this->showMessage();
            }

            $report->setAttributes(array_merge($params,array(
                'yid' => $extraData['yid'],
                'startyear' => $extraData['startyear'],
                'timestamp' => time(),
                'uid' => Yii::app()->user->getId(),
                'stat' => SReport::STATUS_OFFLINE,
            )));

            if(!$report->save()){
                $this->addMessage('state','fail');
                $this->addMessage('message', $report->getErrors());
                $this->showMessage();
            }
        }
        $data['report'] = $report->getAttributes();
        $data['report']['pdfUrl'] = $this->createUrl('showPdf', array('reportId'=>$report->id));
        $photoIds = $data['photos'] = array();
        $data['bigphotos'] = array();
        $imgCropper = array();
        foreach ($report->items as $_item) {
            $data['items'][$_item->id] = $_item->getAttributes();
            $data['items'][$_item->id]['content'] = Yii::app()->format->ntext($_item->content);
            if($_item->media_id) {
                $photoIds[] = $_item->media_id;
                if ($_item->img_cropper) {
                    $imgCropper[$_item->media_id] = $_item->img_cropper;
                }
            }
        }
        // 查找孩子身体成长信息
        Yii::import('common.models.learning.LearningHealth');
        $crit = new CDbCriteria();
        $crit = new CDbCriteria();
        $crit->compare('yid', $extraData['yid']);
        $crit->compare('semester', $this->selectedSemester);
        $crit->compare('childid', $childId);
        $crit->compare('classid', $this->selectedClassId);
        $crit->compare('status', 1);
        $model = LearningHealth::model()->find($crit);
        $data['health'] = array(
            'img1'=>'',
            'img2'=>'',
            'img3'=>'',
        );
        if ($model) {
            $data['health'] = array(
                'img1'=>$model->img1,
                'img2'=>$model->img2,
                'img3'=>$model->img3,
            );
            if ($model->img1) {
                $photoIds[] = $model->img1;
            }
            if ($model->img2) {
                $photoIds[] = $model->img2;
            }
            if ($model->img3) {
                $photoIds[] = $model->img3;
            }

        }

        ChildMedia::setStartYear($report->startyear);
        if($photoIds){
            $photoObjs = ChildMedia::model()->findAllByPk($photoIds);
            foreach ($photoObjs as $photo) {
                $data['photos'][$photo->id] = $photo->getMediaUrl(true);
                $data['bigphotos'][$photo->id] = $photo->getMediaUrl();
                if (isset($imgCropper[$photo->id])) {
                    $data['photos'][$photo->id] = $photo->getOriginal(true) . $imgCropper[$photo->id] . '/thumbnail/200x200';
                    $data['bigphotos'][$photo->id] = $photo->getOriginal(true) . $imgCropper[$photo->id] . '/thumbnail/600x600';
                }
            }
        }

        $this->addMessage('state','success');
        $this->addMessage('data',$data);
        $this->showMessage();
    }

    //取学期报告某一项的数据
    public function actionGetChildSemesterReportItem(){
        Yii::import('common.models.portfolio.*');
        $report_id = Yii::app()->request->getPost('report_id', 0);
        $childId = Yii::app()->request->getPost('childid', 0);
        $category = Yii::app()->request->getPost('category', 0);
        $subcategory = Yii::app()->request->getPost('subcategory', 0);

        $report = SReport::model()->findByAttributes(array(
            'id'=>$report_id,
            'schoolid'=>$this->branchId,
            'classid'=>$this->selectedClassId));
        $params = array(
            'report_id' => $report_id,
            'category' => $category,
            'subcategory' => $subcategory
        );
        if(!is_null($report)){
            $reportItem = SReportItem::model()->findByAttributes($params);
            if(!is_null($reportItem)){
                //todo 根据不同类型处理数据
                $reportItemData = $reportItem->getAttributes();
            }else{
                $reportItem = new SReportItem;
                $reportItem->setAttributes($params);
                $reportItemData = $reportItem->getAttributes();
                if ($category == 'FrontCover') {
                    $reportItemData['content'] = CommonUtils::autoLang('***创作于****年**月', 'Created by name on month year');
                }
            }
            $data['reportItem'] = $reportItemData;
            $this->addMessage('data', $data);
            $this->addMessage('state', 'success');
            $this->showMessage();
        }
    }

    //保存学期报告项
    public function actionReportSave(){
        Yii::import('common.models.portfolio.*');
        $valid = false;
        if(Yii::app()->request->isPostRequest && Yii::app()->request->isAjaxRequest){
            $report_id = $_POST['SReportItem']['report_id'];
            $report = SReport::model()->findByPk($report_id);
            if($report) {
                if (in_array($report->stat, array(SReport::STATUS_PASS,SReport::STATUS_CHECK,SReport::STATUS_ONLINE))) {
                    $this->addMessage('message', '已经提交或者已审核，不可修改');
                    $this->addMessage('state', 'fail');
                    $this->showMessage();
                }
            }


            $ld_id = $_POST['SReportItem']['ld_id'];
            $subcategory = $_POST['SReportItem']['subcategory'];

            if($_POST['SReportItem']['category'] == 'Strength'){
                $_ld_id = $ld_id%2==1 ? ($ld_id+1) : ($ld_id-1);
                $criteria = new CDbCriteria();
                $criteria->compare('ld_id', $ld_id);
                $criteria->compare('ld_id', $_ld_id, false, 'or');
                $criteria->compare('report_id', $report_id);
                $criteria->compare('subcategory', '<>'.$subcategory);
                $count = SReportItem::model()->count($criteria);
                if($count){
                    $this->addMessage('message', Yii::t('teaching', 'Please do not use the same learing domain again.'));
                    $this->addMessage('state', 'fail');
                    $this->showMessage();
                }
            }

            if(isset($_POST['SReportItem']['id']) && $_POST['SReportItem']['id'] > 0 ){
                $reportItem = SReportItem::model()->with('report')->findByPk( $_POST['SReportItem']['id'] );
                if($reportItem->report->schoolid == $this->branchId && $reportItem->report->childid == $_POST['childid']){
                    $valid = true;
                    $reportItem->report->pdf_file = 0;
                    $reportItem->report->save();
                }
            }else{
                if($report->schoolid == $this->branchId && $report->childid == $_POST['childid']){
                    $valid = true;
                }
                $reportItem = new SReportItem();
            }

            //安全检查
            if($valid){
                $reportItem->attributes = $_POST['SReportItem'];
                if(!$reportItem->save()){
                    $this->addMessage('message', print_r($reportItem->getErrors(), false));
                    $this->addMessage('state', 'fail');
                    $this->showMessage();
                }else{
                    $this->addMessage('state','success');
                    $data = $reportItem->getAttributes();
                    $data['content'] = Yii::app()->format->ntext($reportItem->content);
                    $this->addMessage('data',array('data'=>$data));
                    $this->addMessage('callback','cbSaveReportItem');
                    $this->addMessage('message', 'Saved!');
                    $this->showMessage();
                }
            }
        }

        $this->addMessage('message', 'System Error');
        $this->addMessage('state', 'fail');
        $this->showMessage();

    }

    //预览学期报告
    public function actionPreviewReport()
    {
        Yii::import('common.models.portfolio.*');
        Yii::import('common.components.teaching.*');

        $childId = Yii::app()->request->getParam('childid', 0);
        $reportId = Yii::app()->request->getParam('id', 0);

        if($childId && $reportId){
            $params = array(
                'schoolid' => $this->branchId,
                'childid' => $childId,
                'id' => $reportId,
            );

            $withToolBar = true;

            $downloadActionUrl = Yii::app()->createAbsoluteUrl(
                '//mteaching/semester/downloadReport',
                array('branchId'=>$this->branchId));
            $reportHtml =  ContentFetcher::getSemesterReport($params,
                $withToolBar,
                $downloadActionUrl
            );
            echo $reportHtml;
        }
    }

    public function actionPreviewReport2()
    {
        Yii::import('common.components.teaching.*');

        $childId = Yii::app()->request->getParam('childid', 0);
        $reportId = Yii::app()->request->getParam('id', 0);

        if($childId && $reportId){
            $params = array(
                'schoolid' => $this->branchId,
                'childid' => $childId,
                'id' => $reportId,
            );

            $withToolBar = true;

            $downloadActionUrl = Yii::app()->createAbsoluteUrl(
                '//mteaching/semester/downloadReport',
                array('branchId'=>$this->branchId));
            $reportHtml =  ContentFetcher::getSemesterReport2($params,
                $withToolBar,
                $downloadActionUrl
            );
            echo $reportHtml;
        }
    }

    // ibs 专用
    public function actionPreviewReport3()
    {
        Yii::import('common.components.teaching.*');

        $childId = Yii::app()->request->getParam('childid', 0);
        $reportId = Yii::app()->request->getParam('id', 0);

        if($childId && $reportId){
            $params = array(
                'schoolid' => $this->branchId,
                'childid' => $childId,
                'id' => $reportId,
            );

            $withToolBar = true;

            $downloadActionUrl = Yii::app()->createAbsoluteUrl(
                '//mteaching/semester/downloadReport',
                array('branchId'=>$this->branchId));
            $reportHtml =  ContentFetcher::getSemesterReport3($params,
                $withToolBar,
                $downloadActionUrl
            );
            echo $reportHtml;
        }
    }

    /*
     * Ds 报告下载
     */
    public function actionDownloadReportDs()
    {
        $params['schoolid'] = Yii::app()->request->getParam('schoolid', null);
        $childId = $params['childid'] = Yii::app()->request->getParam('childid', 0);
        $id = $params['id'] = Yii::app()->request->getParam('id', 0);
        $params['classid'] = Yii::app()->request->getParam('classid', 0);

        Yii::import('common.models.reportCards.*');
        Yii::import('common.components.teaching.*');
        $report = ReportsData::model()->findByAttributes(array('id'=>$id, 'child_id'=>$childId));
        if ($report) {
            $html = ContentFetcher::getSemesterReportOfDs($params);
            $html = base64_encode($html);
            $flag = $report->startyear + $report->semester;
            if ($flag < 2024 + 2) {
                $postParams = array(
                    'id' => $id,
                    'classid' => $report->class_id,
                    'lang' => Yii::app()->language == 'en_us' ? 'en' : 'cn',
                    'str' => $html,
                    'zoom' => 1.28,
                    'mac' => md5($id . $report->class_id . $html . $this->securityKey)
                );
            } else {
                $coverUrl = Yii::app()->controller->createUrl('//reportHtml/cover', array(
                    'rid' => $id,
                    'childId' => $childId,
                    'type' => 'es',
                    'lang' => Yii::app()->language,
                ));
                $headerUrl = Yii::app()->controller->createUrl('//reportHtml/header', array(
                    'rid' => $id,
                    'childId' => $childId,
                    'type' => 'es',
                    'lang' => Yii::app()->language,
                ));
                $footerUrl = Yii::app()->controller->createUrl('//reportHtml/footer', array('lang' => Yii::app()->language,));
                $postParams = array(
                    'str' => $html,
                    'headerHtml' => $headerUrl,
                    'footerHtml' => $footerUrl,
                    'coverHtml' => $coverUrl,
                    'marginTop' => '30mm',
                    'marginBottom' => '30mm',
                    'id' => $id,
                    'classid' => $report->class_id,
                    'lang' => Yii::app()->language == 'en_us' ? 'en' : 'cn',
                    'mac' => md5($id . $report->class_id . $html . $this->securityKey)
                );
            }
            try {
                $url = 'https://transfer.api.ivykids.cn/api/html2pdf';
                if (!CommonUtils::isProduction()) {
                    $url = 'http://192.168.149.137:3000/html2pdf';
                }
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                curl_setopt($ch, CURLOPT_POST, 1);
                curl_setopt($ch, CURLOPT_URL, $url);
                curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                    'Content-Type: application/json',
                ));
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postParams));
                $result = curl_exec($ch);
                curl_close($ch);
                echo '<script>location.href="'.$result.'";setTimeout(function(){history.back()}, 10000);</script>';
            } catch (Exception $e) {
                return false;
            }
        }
    }

    public function actionSaveDsreportAttendance()
    {
        if(Yii::app()->request->isAjaxRequest && Yii::app()->request->isPostRequest){
            Yii::import('common.models.reportCards.*');

            $childid = Yii::app()->request->getPost('childid', 0);
            $categoryid = Yii::app()->request->getPost('category_id', 0);
            $classid = Yii::app()->request->getPost('classid', 0);
            $semester = Yii::app()->request->getPost('semester', 0);
            $absent = Yii::app()->request->getPost('absent', 0);
            $tardy = Yii::app()->request->getPost('tardy', 0);
            $esl = Yii::app()->request->getPost('esl', 0);
            $csl = Yii::app()->request->getPost('csl', 0);
            $support = Yii::app()->request->getPost('support', 0);
            $leap = Yii::app()->request->getPost('leap', 0);
            $other = Yii::app()->request->getPost('other', 0);
            $other_data = Yii::app()->request->getPost('other_data', '');
            $uptime = Yii::app()->request->getPost('uptime', '');
            $this->checkReportDataEditable($semester);
            $calendarData = $this->getYidStartYearByClassIdAndBranchId($classid, $this->branchId);
            $criteira = new CDbCriteria();
            $criteira->compare('class_id', $classid);
            $criteira->compare('child_id', $childid);
            $criteira->compare('startyear', $calendarData['startyear']);
            $criteira->compare('semester', $semester);
            $model = ReportsData::model()->find($criteira);
            if($model === null){
                $model = new ReportsData();
            }
            $model->class_id = $classid;
            $model->child_id = $childid;
            $model->startyear = $calendarData['startyear'];
            $model->semester = $semester;
            $model->pdf_file = 0;
            $model->uptime = $uptime > 0 ? strtotime($uptime) : NULL;
            $model->attendance_ext = CJSON::encode(array(
                'absent' => $absent,
                'tardy' => $tardy,
                'esl' => $esl,
                'csl' => $csl,
                'support' => $support,
                'leap' => $leap,
                'other' => $other,
                'other_data' => $other_data,
            ));
            $model->updated_user = Yii::app()->user->id;
            $model->updated = time();
            if($model->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message', 'Data Saved!'));
                $this->addMessage('callback', 'cbSuccess');
            } else{
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err[0]);
            }
        }
        $this->showMessage();
    }

    //报告数据是否可修改的权限判断
    public function checkReportDataEditable($semester)
    {
        Yii::import('common.models.calendar.*');
        //只可以修改当前学年当前学期的数据
        $criteira = new CDbCriteria();
        $criteira->compare('branchid', $this->branchId);
        $criteira->compare('is_selected', 1);
        $selectedCalendar = CalendarSchool::model()->find($criteira);
        if($semester == 1){
            $semester_flag = 10;

//            //方案二：修改期限限制为当前学年下学期的结束时间
//            $criteira = new CDbCriteria();
//            $criteira->compare('yid', $selectedCalendar->yid);
//            $criteira->compare('semester_flag', 20);
//            $selectedCalendarSemester = CalendarSemester::model()->find($criteira);
//            $endTime = $selectedCalendarSemester->staff_start_timestamp;//本学年第二学期的开始时间
        }else{
            $semester_flag = 20;

//            //方案二：修改期限限制为当前学年下学期的结束时间
//            $selectedCalendar->startyear;//当前学年
//            $nextYear = $selectedCalendar->startyear+1;//下学年
//            //下学年yid
//            $criteira = new CDbCriteria();
//            $criteira->compare('branchid', $this->branchId);
//            $criteira->compare('startyear', $nextYear);
//            $nextCalendar = CalendarSchool::model()->find($criteira);
//            $nextYid = $nextCalendar->yid;//下学年yid
//            $criteira = new CDbCriteria();
//            $criteira->compare('yid', $nextYid);
//            $criteira->compare('semester_flag', 10);
//            $nextCalendarSemester = CalendarSemester::model()->find($criteira);
//            $endTime = $nextCalendarSemester->staff_start_timestamp;//下学年的第一学期开始时间
        }

        //方案一：修改期限限制为当前学年各学期的结束时间
        $criteira = new CDbCriteria();
        $criteira->compare('yid', $selectedCalendar->yid);
        $criteira->compare('semester_flag', $semester_flag);
        $selectedCalendarSemester = CalendarSemester::model()->find($criteira);
        $endTime = $selectedCalendarSemester->school_end_timestamp;

        // 小学校长助理可以随时修改
        if (!in_array($this->staff->profile->occupation_en, array(280)) && time() > $endTime) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message',  Yii::t("principal","Reporting Deadline").':'.date('Y-m-d',$selectedCalendarSemester->school_end_timestamp));
            $this->showMessage();
        }
    }

    function canSave($yid, $semester)
    {
        $semester_flag = $semester * 10;

        $criteira = new CDbCriteria();
        $criteira->compare('yid', $yid);
        $criteira->compare('semester_flag', $semester_flag);
        $selectedCalendarSemester = CalendarSemester::model()->find($criteira);
        $endTime = $selectedCalendarSemester->school_end_timestamp;

        // 小学校长助理可以随时修改
        return in_array($this->staff->profile->occupation_en, array(280)) || $endTime >= time();
          
    }

    public function actionDownloadReport()
    {
        $params['schoolid'] = Yii::app()->request->getParam('schoolid', null);
        $params['childid'] = Yii::app()->request->getParam('childid', 0);
        $params['id'] = Yii::app()->request->getParam('id', 0);

        $report = SReport::model()->findByAttributes($params);
        if($report){
            if ($report->custom) {
                $pdfUrl = CommonUtils::getPdfUrl($report->custom_pdf, $report->custom);
            } else {
                Yii::import('common.components.teaching.*');
                if ($report->lang == 'en') {
                    Yii::app()->language = 'en_us';
                } else {
                    Yii::app()->language = 'zh_cn';
                }
                $version = 1;
                if (strtolower($report->template_id) == 'ivy03') {
                    $version = 2;
                }
                // IBS 学校特殊处理
                if ($report->branch->group == 20 && ($report->startyear + $report->semester) > 2020) {
                    $version = 3;
                }
                if ($version == 1) {
                    $html = ContentFetcher::getSemesterReport($params);
                } elseif ($version == 2) {
                    $html = ContentFetcher::getSemesterReport2($params);
                } elseif ($version == 3) {
                    $html = ContentFetcher::getSemesterReport3($params);
                }
                $html = base64_encode($html);
    
                try{
                    $postParams = array(
                        'id'=> $params['id'],
                        'classid'=>$report->classid,
                        'str'=>$html,
                        'zoom' => 1.28,
                        'mac'=>md5($params['id'].$report->classid.$html.$this->securityKey)
                    );
                    $url = 'https://transfer.api.ivykids.cn/api/html2pdf';
                    if (!CommonUtils::isProduction()) {
                        $url = 'http://192.168.149.137:3000/html2pdf';
                    }
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                    curl_setopt($ch, CURLOPT_POST, 1);
                    curl_setopt($ch, CURLOPT_URL, $url);
                    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                        'Content-Type: application/json',
                    ));
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postParams));
                    $pdfUrl = curl_exec($ch);
                    curl_close($ch);
                }
                catch (Exception $e) {
                    echo $e->getMessage();
                }
            }
            echo '<script>location.href="'.$pdfUrl.'";setTimeout(function(){history.back()}, 15000);</script>';
        }
    }

    public function actionDownloadReport2()
    {
        $params['schoolid'] = Yii::app()->request->getParam('schoolid', null);
        $childid = Yii::app()->request->getParam('childid', 0);
        $id = $params['id'] = Yii::app()->request->getParam('id', 0);

        $report = SReport::model()->findByAttributes(array('id'=>$id, 'childid'=>$childid));
        if($report && $report->stat == 20 && $report->pdf_file == 1){
            $pdfUrl = CommonUtils::getPdfUrl($report->custom_pdf, $report->custom);
            echo '<script>location.href="'.$pdfUrl.'";setTimeout(function(){history.back()}, 10000);</script>';
        }
    }

    public function actionDownloadReport3()
    {
        $params['schoolid'] = Yii::app()->request->getParam('schoolid', null);
        $params['childid'] = Yii::app()->request->getParam('childid', 0);
        $params['id'] = Yii::app()->request->getParam('id', 0);

        $report = SReport::model()->findByAttributes($params);
        if($report && $report->stat == 20 && $report->pdf_file == 1){
            $pdfUrl = CommonUtils::getPdfUrl($report->custom_pdf, $report->custom);
            echo '<script>location.href="'.$pdfUrl.'";setTimeout(function(){history.back()}, 10000);</script>';
        }
    }

    public function taskSchecklist()
    {
        Yii::import('common.models.childdev.*');

        $classObj = IvyClass::model()->findByPk($this->selectedClassId);

        if(Yii::app()->request->isPostRequest && Yii::app()->request->isAjaxRequest){
            $childid        = Yii::app()->request->getPost('childid', 0);
            $fall           = Yii::app()->request->getPost('fall', array());
            $spring         = Yii::app()->request->getPost('spring', array());
            $fall_display   = Yii::app()->request->getPost('fall_display', 1);
            $spring_display = Yii::app()->request->getPost('spring_display', 1);

            if($fall_display == 2){
                $flip = array_flip($fall);
                if(isset($flip[0])){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t("", '请完成第一学期的所有选项后再上线！'));
                    $this->showMessage();
                }
            }
            if($spring_display == 2){
                $flip = array_flip($spring);
                if(isset($flip[0])){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t("", '请完成第二学期的所有选项后再上线！'));
                    $this->showMessage();
                }
            }
            $criteria = new CDbCriteria();
            $criteria->compare('classid', $this->selectedClassId);
            $criteria->compare('childid', $childid);
            $model = DevData::model()->find($criteria);
            if($model == null)
                $model = new DevData();
            $model->childid = $childid;
            $model->branchid = Yii::app()->request->getParam('branchId', '');
            $model->classid = $this->selectedClassId;
            $model->yid = $classObj->yid;
            $model->class_type = $classObj->classtype;
            $model->development = serialize(array('fall'=>$fall, 'spring'=>$spring));
            $model->display = 1;
            $model->fall_display = $fall_display;
            $model->spring_display = $spring_display;
            $model->timestamp = time();
            $model->userid = Yii::app()->user->id;
            if($model->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('', '保存成功！'));
                $flag = $this->selectedSemester == 1 ? $fall_display : $spring_display;
                $this->addMessage('data', array('childid'=>$childid, 'flag'=>$flag));
                $this->addMessage('callback', 'cb');
            }
            else{
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('', '保存失败！'));
            }
            $this->showMessage();
        }

        $cond = $this->selectedSemester == 1 ? "fall_display=2" : "spring_display=2";
        $condParams = array(
            ':classid'  => $this->selectedClassId,
        );
        $rows = Yii::app()->db->createCommand()
            ->select('childid')
            ->from('ivy_dev_data')
            ->where("classid=:classid and ".$cond, $condParams)
            ->queryAll();
        $onlines = array();
        foreach($rows as $row){
            $onlines[] = $row['childid'];
        }
        $taskData['onlines'] = $onlines;
        $isOld = ($classObj->calendarInfo->startyear > 2017) ? false : true;
        $checkListTemplates = DevTemplate::model()->getTemplatebyClassType($classObj->classtype, $isOld);
        $taskData['checkListTemplates'] = $checkListTemplates;

        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
        $cs->registerScriptFile(Yii::app()->theme->baseUrl.'/js/star-rating.js');
        $cs->registerCssFile( Yii::app()->theme->baseUrl.'/css/star-rating.css');

        return $taskData;
    }

    /**
     * 管理一对一家长会议时间计划
     * @param int $classid
     * @param int $semester
     */
    public function taskPtc(){
        Yii::import('common.models.ptc.*');
        $plan = ParentMeetingPlan::model()->with('items')->findByAttributes(array(
            'classid' => $this->selectedClassId,
            'semester' => $this->selectedSemester
        ));
        Yii::import('common.models.calendar.Calendar');
        $calendarObj = Calendar::model()->findByPk($this->calendarId);
        $default_duration = 30;

        if($this->branchObj->type == 50){
            $default_duration = 90;
            $classModel = IvyClass::model()->findByPk($this->selectedClassId);
            if (in_array($classModel->classtype, array('e6', 'e7', 'e8', 'e9', 'e10', 'e11', 'e12'))){
                $default_duration = 120;
            }elseif (in_array($classModel->classtype, array('mc','mt'))){
                $default_duration = 30;
            }/*elseif (in_array($classModel->classtype, array('mk'))){
                $default_duration = 20;
            }*/

        }
        if(empty($plan)){
            $plan = new ParentMeetingPlan();
            $plan->setAttributes(array(
                'calendar_id' => $this->calendarId,
                'classid' => $this->selectedClassId,
                'semester' => $this->selectedSemester,
                'startyear' => $calendarObj->startyear,
                'school_id' => $this->branchId,
                'default_duration' => $default_duration,
                'creater' => Yii::app()->user->getId(),
                'created' => time()
            ));
            if(!$plan->save()){
                print_r($plan->getErrors());
            }
        }
        foreach($plan->timeslotExplode() as $_time){
            $timeslots[] = array('time'=>$_time);
        }

		$criteria = new CDbCriteria();
        $criteria->compare('planid', $plan->id);
        $criteria->order = "target_date ASC, timeslot ASC, meet_index ASC";
		$item = ParentMeetingItem::model()->findAll($criteria);
		$select_childid = array();
		$item_values = array();
		foreach($item as $k=>$v){
			$timelotarray = explode(",", $v['timeslot']);
			$timestart= $timelotarray[0];
			$timeend = $timelotarray[1];
			$timestrto = strtotime($v['target_date']);
			$time = date('Y-m-d', $timestrto);
			$startIndex = sprintf("%02d",$v->meet_index);
			$endIndex = sprintf("%02d",$v->meet_index);;
			$start = $time.'T'.$timestart;
			$end = $time.'T'.$timeend;
			$item_values[$v->uniKey()] = array(
				'childid' => $v->childid,
				'start' => $start . ":" . $startIndex,
				'end' => $end . ":" . $endIndex,
			);
            if($v->childid)
                $select_childid[$v->childid] = $start.' - '.$timeend;
		}

        $events = array();
        $arr_childid = array_keys($select_childid);
		if($arr_childid){
			$criteria = new CDbCriteria();
			$criteria->compare('childid', $arr_childid);
			$criteria->index = 'childid';
			$items = ChildProfileBasic::model()->findAll($criteria);
		}
		foreach($item_values as $unikey => $value){
			$events[] = array(
				'id' => $unikey,
				'title' => isset($items[$value['childid']])?$items[$value['childid']]->getChildName():'',
				'start' => $value['start'],
				'end' => $value['end'],
				'color' => $value['childid']?'#3a87ad':'#257e4a',
			);
		}
        $timepoints = explode(',', $calendarObj->timepoints);

        $mon = date('Y-m', $this->selectedSemester==1 ? $timepoints[1] : $timepoints[3]);
        $months = $this->getRecentMonth($mon, 7, 0);

        $currentMonth = date('Y-m');
        $selectMonth = ( in_array($currentMonth, $months) ) ? $currentMonth : current($months);

		$criter = new CDbCriteria();
        $criter->compare('planid', $plan->id);
		$item = ParentMeetingItemMemo::model()->findAll($criter);
		$arr_memo = array();
		foreach($item as $k => $v){
			$arr_memo[$v->childid] = $v->memo;
		}

        $taskData['select_childid'] = $select_childid;
        $taskData['arr_childid'] = $arr_childid;
        $taskData['arr_memo'] = $arr_memo;
        $taskData['months'] = $months;
        $taskData['selectMonth'] = $selectMonth;
        $taskData['events'] = $events;
        $taskData['timeslots'] = $timeslots;
        $taskData['plan'] = $plan;
        $taskData['children'] = $this->getNameList($this->selectedClassId,true,'array','ptc');

        $send_time = time() - 86400;
        $criteria = new CDbCriteria();
        $criteria->compare('classid', $this->selectedClassId);
        $criteria->compare('planid', $plan->id);
        $criteria->compare('childid', array_keys($taskData['children']));
        $criteria->compare('send_time', ">={$send_time}");
        $wxnotifPtcModel = WxnotifPtc::model()->findAll($criteria);
        $expired = array();
        if($wxnotifPtcModel){
            foreach ($wxnotifPtcModel as $item){
                $expired[$item->childid] = $item->childid;
            }
        }
        $taskData['expired'] = $expired;
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/phpfunc.js');

        return $taskData;
    }
    /**
     * 管理一对一家长预约 发通知
     * @param int $classid
     * @param int $planid
     * @param int $childid
     */
    public function actionSendMessage()
    {
        $classid = Yii::app()->request->getParam('classid', '');
        $planid = Yii::app()->request->getParam('planid', '');
        $childid = Yii::app()->request->getParam('childid', '');
        $semester = Yii::app()->request->getParam('semester', '');
        Yii::import('common.models.ptc.*');
        Yii::import('common.models.wechat.*');
        if(Yii::app()->request->isAjaxRequest) {
            $this->accessToken = ($this->branchObj->type == 50) ? CommonUtils::getAccessToken('ds'): CommonUtils::getAccessToken('ivy');
            if($this->accessToken) {
                $yid = Branch::model()->getBranchInfo($this->branchId, 'schcalendar');
                $calendarModel = Calendar::model()->findByPk($yid);
                $endYear = $calendarModel->startyear + 1;
                $stratYear = $calendarModel->startyear . ' - ' . $endYear;

                $criteria = new CDbCriteria();
                $criteria->compare('t.calendar_id', $yid);
                $criteria->compare('t.school_id', $this->branchId);
                $criteria->compare('t.classid', $classid);
                $criteria->compare('t.semester', $semester);
                $criteria->compare('t.status', 1);
                $criteria->with = 'items';
                $criteria->order = "items.target_date ASC";
                $planModel = ParentMeetingPlan::model()->find($criteria);
                $message = Yii::t('teaching', 'No available time slot found or PTC not activated.');
                $items = array();
                if ($planModel) {
                    foreach ($planModel->items as $val) {
                        $items[$val->target_date] = date("Y/m/d", strtotime($val->target_date));
                    }
                }

                if ($items) {
                    $message = Yii::t('teaching', 'Notification can be sent only once within 24 hours.');
                    $send_time = time() - 86400;

                    $criteria = new CDbCriteria();
                    $criteria->compare('classid', $classid);
                    $criteria->compare('planid', $planid);
                    $criteria->compare('childid', $childid);
                    $criteria->compare('send_time', ">={$send_time}");
                    $wxnotifPtcModel = WxnotifPtc::model()->count($criteria);

                    if (!$wxnotifPtcModel) {
                        $childModel = ChildProfileBasic::model()->findByPk($childid);
                        $parent = array();
                        if ($childModel->fid) {
                            $parent['fid'] = $childModel->fid;
                        }
                        if ($childModel->mid) {
                            $parent['mid'] = $childModel->mid;
                        }
                        $model = new WxnotifPtc();
                        $model->childid = $childModel->childid;
                        $model->schoolid = $this->branchId;
                        $model->classid = $classid;
                        $model->planid = $planid;
                        $model->send_time = time();
                        $model->updated_at = time();
                        $model->updated_by = Yii::app()->user->id;
                        if ($model->save()) {
                            $message = '成功，家长未绑定微信';
                            $data = array();
                            if ($parent) {
                                $criteria = new CDbCriteria();
                                $criteria->compare('userid', $parent);
                                if ($this->branchObj->type == 50) {
                                    $criteria->compare('account', 'ds');
                                } else {
                                    $criteria->compare('account', 'ivy');
                                }
                                $criteria->compare('valid', 1);
                                $wechatUserModel = WechatUser::model()->findAll($criteria);

                                if ($wechatUserModel) {
                                    $childModel = ChildProfileBasic::model()->findByPk($childid);
                                    $schoolTitle = $this->branchObj->title;
                                    if($this->branchObj->type == 50){
                                        $schoolTitle = BranchInfo::model()->findByPk($childModel->schoolid);
                                    }

                                    $ivyClass = IvyClass::model()->findByPk($classid);

                                    $num = 0;
                                    foreach ($wechatUserModel as $val) {
                                        $modelItme = new Wxnotif();
                                        $modelItme->openid = $val->openid;
                                        $modelItme->wx_account = ($this->branchObj->type == 50) ? 'ds' : 'ivy';
                                        $modelItme->task_type = ($this->branchObj->type == 50) ? 'ds' : 'ivy';
                                        $modelItme->taskid = $model->id;
                                        $modelItme->notify_at = time();
                                        $modelItme->randtag = uniqid();
                                        $modelItme->updated_at = time();
                                        $modelItme->updated_by = Yii::app()->user->id;
                                        $modelItme->save();
                                        $status = $this->sendMessageData($model->childid, $ivyClass,$this->branchObj->type, $schoolTitle, $val->openid, $modelItme, $items, $semester, $stratYear);
                                        $modelItme->notified = $status;
                                        $modelItme->save();
                                        if ($status == 0) {
                                            $num++;
                                            $information = array();
                                            if ($val->info) {
                                                $information = json_decode($val->info->info, true);
                                            }
                                            $data[$childModel->childid][] = array(
                                                'name' => ($information) ? $information['nickname'] : "",
                                                'headimgurl' => ($information) ? $information['headimgurl'] : "",
                                            );
                                        }
                                    }
                                    $message = '微信通知发送完成' . $num . '个';
                                }
                            }

                            $this->addMessage('state', 'success');
                            $this->addMessage('message', $message);
                            $this->addMessage('data', $data);
                            $this->showMessage();
                        }
                    } else {
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', $message);
                        $this->showMessage();
                    }
                } else {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', $message);
                    $this->showMessage();
                }
            }else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', 'token获取失败');
                $this->showMessage();
            }

        }
    }

    // 发送模板消息
    public function sendMessageData($childid,$ivyClass,$schoolid, $school,$openid,$modelItme, $items,$semester,$stratYear)
    {
        $url = 'https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=' . $this->accessToken;
        $season_cn = ($semester == 1) ? '秋季' : '春季' ;
        $season_en = ($semester == 1) ? 'Autumn' : 'Spring' ;
        // 微信模板消息内容

        if ($schoolid == 50) {
            $appid = 'wxb1a42b81111e29f3';
            $state = 'ds';
            $template_id = '2f8ybAHdLRRmzfOwUOSFIDpNTyIcnGBfkuQiPaf28Ao';
            $ceshiOpenid = 'oHBKPwQUsreFHcZkzsq0CdW7VqEQ';
            //$first = '亲爱的家长：您好！'.$stratYear. $season_cn . $school->title_cn.'学生主导的家长会议预约已经开启。Dear parent, the reservation for '.$stratYear. $season_en .' Student Led Conference is now open.';
            $first = '家长会预约已开启。Student Led Conf. Appt. is now open.';

            if (in_array($ivyClass->classtype, array('e6', 'e7', 'e8', 'e9', 'e10', 'e11', 'e12'))){
                $type = '明义多功能厅 Mingyi MPR';
            } elseif (in_array($ivyClass->classtype, array('e1', 'e2', 'e3', 'e4', 'e5'))){
                $type = '请查阅家校联系本 Please refer to Home-School Comm. Book';
            } else{
                $first = '家长会预约已开启。Parent-Teacher Conf. Appt. is now open.';
                $type = '幼儿所在班级 In the classroom';
            }

            $remark = '请点击“详情”进行预约。Please tap on "Details" to schedule your time.';
            $date = 'Daystar (Beigao)';
            $class = (strtotime(reset($items)) != strtotime(end($items))) ? reset($items) . ' - ' . date("d", strtotime(end($items))) : reset($items);
        } else {
            $appid = 'wx903fba9d4709cf10';
            $state = 'ivy';
            $template_id = 'lcF9djd0KumgCKkT2XbFNf0SVs9_bkUsp2GocyMAMnA';
            $ceshiOpenid = 'ouwmTjiT6aIh_TLd6Kldr2Cx4tCs';
            $first = '家长您好，'. $stratYear . $season_cn . $school  .'校园一对一家长会预约已开启。';
            $type = '请咨询班级老师或校方工作人员';
            $remark = '请点击“详情”进行时间段预约。';
            $date = $school;
            $class = (strtotime(reset($items)) != strtotime(end($items))) ? reset($items) . ' - ' . end($items) : reset($items);
        }

        $redirectUrl = "http://www.ivyonline.cn/wechat/reserve/index/randtag/$modelItme->randtag";
        $touser  = (OA::isProduction()) ? $openid  : $ceshiOpenid;

        $data = array(
            'touser' => $touser,
            'template_id' => $template_id,
            'url' => 'https://open.weixin.qq.com/connect/oauth2/authorize?appid='.$appid.'&redirect_uri='.$redirectUrl.'&response_type=code&scope=snsapi_base&state='.$state.'#wechat_redirect',
            'data' => array(
                'first' => array('value' => $first),
                //'first' => array('value' => '家长您好，'. $stratYear . $season . $school  .'校园一对一家长会预约已开启。'),
                'date' => array('value' => $date),
                'class' => array('value' => $class),
                'type' => array('value' => $type),
                'remark' => array('value' => $remark, 'color' => '#ff6726'),
            ),
        );
        $jsonData = json_encode($data);
        $res = CommonUtils::httpGet($url, $jsonData);
        $jsonRes = CJSON::decode($res);

        Yii::log($state.'=>'. $childid .'=>'.$openid.'=>'.$jsonRes['errcode'], CLogger::LEVEL_INFO, 'wechatptc.teacher');
        return $jsonRes['errcode'];
    }

    public function actionGetChildData($id=0, $classid=0, $semester=0)
    {
        if($id && $classid && $semester){
            /*Yii::import('common.models.childdev.*');

            $classObj = IvyClass::model()->findByPk($classid);

            $dev_templates = DevTemplate::model()->findAllByAttributes(array('class_type'=>$classObj->classtype));
            $development = array();
            foreach($dev_templates as $temp){
                $development['fall'][$temp->id] = 0;
                $development['spring'][$temp->id] = 0;
            }

            $criteria = new CDbCriteria();
            $criteria->compare('classid', $classid);
            $criteria->compare('childid', $id);
            $model = DegetChildDatavData::model()->find($criteria);
            if($model === null){
                $model = new DevData();
                $model->development = $development;
            }
            else{
                $dataDev = unserialize($model->development);
                $newDevelopment = array_merge($development, $dataDev);
                $model->development = $newDevelopment;
            }*/
            $model = $this->getChildData($id,$classid,$semester);
            $data = array('model'=>$model);
            $url = $this->createUrl('exportGrowing', array('childid' => $id, 'classid' => $classid, 'semester' => $semester));

            echo CJSON::encode(array('model'=>$model,'url' => $url));
        }
    }

    public function getChildData($id=0, $classid=0, $semester=0)
    {
        if($id && $classid && $semester) {
            Yii::import('common.models.childdev.*');
            $classObj = IvyClass::model()->findByPk($classid);
            $isOld = ($classObj->calendarInfo->startyear > 2017) ? false : true;
            $dev_templates = DevTemplate::model()->getTemplates($classObj->classtype, $isOld);
            $development = array();
            foreach ($dev_templates as $temp) {
                $development['fall'][$temp['id']] = 0;
                $development['spring'][$temp['id']] = 0;
            }

            $criteria = new CDbCriteria();
            $criteria->compare('classid', $classid);
            $criteria->compare('childid', $id);
            $model = DevData::model()->find($criteria);
            if ($model === null) {
                $model = new DevData();
                $model->development = $development;
            } else {
                $dataDev = unserialize($model->development);
                $newDevelopment = array_merge($development, $dataDev);
                $model->development = $newDevelopment;
            }

            return $model;
        }
    }

    //打印成长报告
    public function actionExportGrowing()
    {
        $childid = Yii::app()->request->getParam('childid', "");
        $classid = Yii::app()->request->getParam('classid', "");
        $semester = Yii::app()->request->getParam('semester', "");
        $taskData = array();
        if($classid){
            $taskData = $this->taskSchecklist();
        }
        $childModel = ChildProfileBasic::model()->with('ivyclass')->findByPk($childid);

        $this->layout = '//layouts/print';
        $this->printFW = $this->branchObj->getPrintHeader();
        $childdata = $this->getChildData($childid, $classid, $semester);
        $this->render('export', array(
            'childModel'=>$childModel,
            'taskData'=>$taskData,
            'childdata'=>$childdata,
        ));
    }

    public function actionSaveDuration($classid=0, $semester=0)
    {
        if($this->checkTeachers()){
            $duration = Yii::app()->request->getParam('duration', 0);
            if($classid && $semester && $duration){
                Yii::import('common.models.ptc.*');
                $criter = new CDbCriteria();
                $criter->compare('classid', $classid);
                $criter->compare('semester', $semester);
				$criter->compare('school_id', $this->branchId);
                $item = ParentMeetingPlan::model()->find($criter);
				$item->default_duration = $duration;
				if($item->save()){
					$this->addMessage('state', 'success');
					$this->addMessage('message', Yii::t('message', 'Data Saved!'));
				}
				else{
					$this->addMessage('state', 'fail');
					$err = current($item->getErrors());
					$this->addMessage('message', $err[0]);
				}
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('teaching', 'PTC time cannot be blank'));
            }
        }
        else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', 'System Error!'));
        }
		$this->showMessage();
    }

    public function actionSaveTimeslot($id=0, $classid=0, $semester=0)
    {
        if($this->checkTeachers()){
            $hour = Yii::app()->request->getParam('hour', 0);
            $minute = Yii::app()->request->getParam('minute', 0);
            if($classid && $semester && $hour && $minute){
                Yii::import('common.models.ptc.*');
                $criter = new CDbCriteria();
                $criter->compare('classid', $classid);
                $criter->compare('semester', $semester);
				$criter->compare('school_id', $this->branchId);
                $item = ParentMeetingPlan::model()->find($criter);
                $character = $item->timeslot_starts;
                if($character){
                    $array = explode(",", $character);
                    foreach($array as $k => $v){
                        $arr[$v] = $v;
                    }
                }
                $newtimeslot = $hour.':'.$minute;
                if(!in_array($newtimeslot, $arr)){
                    $arr[$newtimeslot] = $newtimeslot;
                    sort($arr);
                    $time_starts = implode(",", $arr);
                    $item->timeslot_starts = $time_starts;
                    if($item->save()){
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', Yii::t('message', 'Data Saved!'));
                        $this->addMessage('data', array('time'=>$newtimeslot));
                    }
                    else{
                        $this->addMessage('state', 'fail');
                        $err = current($item->getErrors());
                        $this->addMessage('message', $err[0]);
                    }
                }
                else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('message', 'Data existed!'));
                }
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('teaching', 'PTC time cannot be blank'));
            }
        }
        else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', 'System Error!'));
        }
		$this->showMessage();
    }

    public function actionGetTimeslot($id=0, $classid=0, $semester=0)
    {
		if($classid && $semester && $id){
			Yii::import('common.models.ptc.*');
			$item = ParentMeetingPlan::model()->findByPk($id);
			$character = $item->timeslot_starts;
			if($character){
				$array = explode(",", $character);
				$arr = array();
				foreach($array as $k=>$v){
					$arr[] = array('time' => $v);
				}
				echo CJSON::encode($arr);
			}
		}
    }

    public function actionDelTimeslot($id=0, $classid=0, $semester=0)
    {
        if($this->checkTeachers()){
            $time = Yii::app()->request->getParam('time', 0);
            if($classid && $semester && $id){
                Yii::import('common.models.ptc.*');
				$item = ParentMeetingPlan::model()->findByAttributes(array('school_id'=>$this->branchId, 'id'=>$id));
                $character = $item->timeslot_starts;
                $array = explode(",", $character);
                $arr = array();
                foreach($array as $k => $v){
                    $arr[$v] = $v;
                }
                unset($arr[$time]);
                $time_starts = implode(",", $arr);
                $item->timeslot_starts = $time_starts;
                if($item->save()){
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('global', 'Data deleted'));
                }
                else{
                    $this->addMessage('state', 'fail');
                    $err = current($item->getErrors());
                    $this->addMessage('message', $err[0]);
                }
            }
        }
        else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', 'System Error!'));
        }
        $this->showMessage();
    }

    public function actionSaveTimeslotwithDay()
    {
        if($this->checkTeachers()){
            $unikey_del = array();
            $unikey_new = array();
            $planid = Yii::app()->request->getParam('planid', 0);
            $timeslot = Yii::app()->request->getParam('timeslot', array());
            $days = Yii::app()->request->getParam('days', array());
            Yii::import('common.models.ptc.*');
            foreach($days as $k=>$v){
                $arr[] = date('Ymd', $v);
            }
            $criter = new CDbCriteria();
            $criter->compare('planid', $planid);
            $criter->compare('target_date', $arr);
            $des = ParentMeetingItem::model()->findAll($criter);
            foreach($des as $k=>$v){
                if($v->childid == 0){
                    $unikey_del[] = $v->uniKey();
                    $v->delete();
                }
            }

            $success = 0;
            $fail = 0;
            $criter = new CDbCriteria();
            $criter->compare('id', $planid);
            $item = ParentMeetingPlan::model()->find($criter);
            $duration = $item->default_duration;
            foreach($days as $k1 => $v1){
                foreach($timeslot as $k2 => $v2){
                    $timearray = explode(":", $v2);
                    $hour= $timearray[0];
                    $minute = $timearray[1];
                    $newminute = $minute + $duration;
                    if($newminute){
                        $hour = floor($newminute/60) + $hour;
                        if($hour < 10){
                            $hour = '0'.$hour;
                        }
                        $newminute = fmod($newminute, 60);
                        if($newminute == 0){
                            $newminute = '00';
                        }
                        elseif($newminute == 5){
                            $newminute = '05';
                        }
                    }
                    $endtime = $hour.":".$newminute;
                    $times = $v2.','.$endtime;
                    $criter = new CDbCriteria();
                    $criter->compare('planid', $planid);
                    $criter->compare('target_date', date('Ymd', $v1));
                    $criter->compare('timeslot', $times);
                    $model = ParentMeetingItem::model()->find($criter);
                    if($model == null){
                        $model = new ParentMeetingItem;
                    }
                    if($model->isNewRecord){
                        $model->planid = $planid;
                        $model->target_date = date('Ymd', $v1);
                        $model->timeslot = $times;
                        $model->stem = 0;
                        $timelotarray = explode(",", $times);
                        $timestart= $timelotarray[0];
                        $timeend = $timelotarray[1];
                        $time = date('Y-m-d', $v1);
                        $start = $time.'T'.$timestart;
                        $end = $time.'T'.$timeend;
                        if($model->save()){
                            $unikey_new[] = array(
                                'id' => $model->uniKey(),
                                'start' => $start,
                                'end' => $end
                            );
                            $success = $success + 1;
                        }
                        else{
                            $fail = $fail + 1;
                        }
                    }
                }
            }
            if($fail){
                $this->addMessage('state', 'fail');
                $this->addMessage('message',
                    Yii::t('teaching', ':failNum items saving failed', array(':failNum'=>$fail)));
            }
            else{
                $this->addMessage('state', 'success');
                $this->addMessage('message',
                    Yii::t('teaching', ':failNum items saving success', array(':successNum'=>$success)));
                $this->addMessage('data', array('delTimeslot' => $unikey_del, 'newTimeslot' => $unikey_new));
                $this->addMessage('callback', 'cbChooseTimeslot');
            }
        }
        else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', 'System Error!'));
        }
		$this->showMessage();
	}

    protected function getRecentMonth($m='', $c=3, $d=2)
    {
		$con = explode('-', $m);
		$year = $con[0];
		$month = $con[1];

		$day = 1;
		$et[] = $m;
		for($i=1; $i<$c; $i++){
			$month -= 1;
			if($month == 0){
				$month = 12;
				$year -= 1;
				$et[] = date("Y-m", mktime(0,0,0,$month,$day,$year));
			}else{
			$et[] = date("Y-m", mktime(0,0,0,$month,$day,$year));
			}
		}
		$month += $c-1;
		for($i=0; $i<$d; $i++){
			$month += 1;
			if($month == 12){
				$month = 1;
				$year += 1;
				$et[] = date("Y-m", mktime(0,0,0,$month,$day,$year));
			}else{
			$et[] = date("Y-m", mktime(0,0,0,$month,$day,$year));
			}
		}
		sort($et);
		return $et;
	}

    public function actionSaveTimeslotwithItem()
    {
        if($this->checkTeachers()){
            $unikey = Yii::app()->request->getParam('unikey', '');
            $childid = Yii::app()->request->getParam('childid', 0);
            $endtime = Yii::app()->request->getParam('endtime', array());
            $memo = Yii::app()->request->getParam('memo', '');
            $model = ChildProfileBasic::model()->findByPk($childid);

            $op_timestamp = time();
            $arr = explode("_", $unikey);
            $planid = $arr[0];
            $days = $arr[1];
            $times = $arr[2];
            $meet_index = $arr[3];
            $timearr = explode(",", $times);
            $timestart = $timearr[0];
            $timeend = $endtime['hour'].':'.$endtime['minute'];
            if($this->branchObj->type == 50){
                $timeend = $timearr[1];
            }

            $timeslot = $timestart.','.$timeend;
            $ymd = date('Y-m-d', strtotime($days));
            $ymd_times = $ymd.'T'.$timestart.' - '.$timeend;
            Yii::import('common.models.ptc.*');
            $criter = new CDbCriteria();
            $criter->compare('planid', $planid);
            $criter->compare('target_date', $days);
            $criter->compare('timeslot', $times);
            $criter->compare('meet_index', $meet_index);
            $item = ParentMeetingItem::model()->find($criter);
			$model_schoolid = ParentMeetingPlan::model()->findByPk($planid);
			$models = ChildProfileBasic::model()->findByPk($item->childid);
			if($model_schoolid->school_id == $this->branchId){
				if($item->childid ==0 || $item->childid == $childid){
					$item->childid = $childid;
					$item->timeslot = $timeslot;
					$item->op_timestamp = ($childid) ? $op_timestamp : 0;
					$item->op_userid = ($childid) ? Yii::app()->user->getId() : 0;
					$item->stem = ($childid) ? (($item->stem) ? $item->stem : 3): 0;
					$criteria = new CDbCriteria();
					$criteria->compare('planid', $planid);
					$criteria->compare('childid', $childid);
					$itemmemo = ParentMeetingItemMemo::model()->find($criteria);
					if($itemmemo == null){
						$itemmemo = new ParentMeetingItemMemo;
					}
					$itemmemo->planid = $planid;
					$itemmemo->childid = $childid;
					$itemmemo->memo = $memo;
					if($itemmemo->save()){
						if($item->save()){
							$this->addMessage('state', 'success');
							$this->addMessage('message', Yii::t('message', 'Data Saved!'));
							$this->addMessage('data',
                                array('childid' => $childid, 'childName' => ($model) ? $model->getChildName() : "", 'unikey' => $unikey, 'memo' => $memo, 'childTimeslot' => $ymd_times));
							$this->addMessage('callback', 'cbChooseChild');
						}
						else{
							$this->addMessage('state', 'fail');
							$err = current($item->getErrors());
							$this->addMessage('message', $err[0]);
						}
					}
					else{
						$this->addMessage('state', 'fail');
						$err = current($itemmemo->getErrors());
						$this->addMessage('message', $err[0]);
					}
				}else{
					$this->addMessage('state', 'fail');
					$this->addMessage('message', Yii::t('teaching', 'Sorry, this slot is no longer available.'));
					$this->addMessage('data', array('childid' => '', 'childName' => '', 'unikey' => $unikey));
				}
			}
        }
        else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', 'System Error!'));
        }
		$this->showMessage();
    }

    /**
     * @TODO 返回某个时间段的详细信息
     * @params unikey [planid_target_date_timeslot]
     * @return {endtimeh: x, endtimem: x, childid: x, memo: x}
     */
    public function actionGetTimeslotInfo()
    {
		$unikey = Yii::app()->request->getParam('unikey', '');
		$arr = explode("_", $unikey);
		$planid = $arr[0];
		$days = $arr[1];
		$times = $arr[2];
        $meet_index = $arr[3];
		$timearr = explode(",", $times);
		Yii::import('common.models.ptc.*');
		$criter = new CDbCriteria();
		$criter->compare('planid', $planid);
		$criter->compare('target_date', $days);
		$criter->compare('timeslot', $timearr[0].',', true);
		$criter->compare('meet_index', $meet_index);
		$item = ParentMeetingItem::model()->find($criter);
		$criteria = new CDbCriteria();
		$criteria->compare('planid', $planid);
		$criteria->compare('childid', $item->childid);
		$itemmemo = ParentMeetingItemMemo::model()->find($criteria);
		$timearr = explode(",", $item->timeslot);
		$timeend = $timearr[1];
		$endtimeh = explode(":", $timeend);
		$arr_json['endtimeh'] = $endtimeh[0];
		$arr_json['endtimem'] = $endtimeh[1];
		$arr_json['childid'] = $item->childid;
		$arr_json['memo'] = $itemmemo->memo;
		echo CJSON::encode($arr_json);
    }

    /**
     * @TODO 返回某天的预约信息
     * @params planid, target_date
     * @return {timeslot_s: 开始时间点}
     */
    public function actionGetDayInfo()
    {
		$planid = Yii::app()->request->getParam('planid', 0);
		$days = Yii::app()->request->getParam('target_date', 0);
		$arr = explode("/", $days);
		if($arr[1] < 10){
			$arr[1] = '0'.$arr[1];
		}
		if($arr[2] < 10){
			$arr[2] = '0'.$arr[2];
		}
		$target_date = implode("", $arr);
		Yii::import('common.models.ptc.*');
		$criter = new CDbCriteria();
		$criter->compare('planid', $planid);
		$criter->compare('target_date', $target_date);
		$item = ParentMeetingItem::model()->findAll($criter);
		foreach($item as $k => $v){
			$arr_childid[] = $v->childid;
			$timeslot = $v->timeslot;
			$arr_s = explode(",", $timeslot);
			$timeslot_s[] = Array(
			'timeslot_s' => $arr_s[0],
			'childid' => $v->childid
			);
		}
		if($timeslot_s){
			$criteria = new CDbCriteria();
			$criteria->compare('childid', $arr_childid);
			$criteria->index = 'childid';
			$items = ChildProfileBasic::model()->findAll($criteria);
			foreach($timeslot_s as $key => $v1){
				$events[] = array(
					'childid' => $v1['childid'],
					'childName' => isset($items[$v1['childid']])?$items[$v1['childid']]->getChildName():'',
					'timeslot_s' => $v1['timeslot_s']
				);
			}
		}
		echo CJSON::encode($events);
    }

    /**
     * @TODO 返回某天的预约信息 DS
     * @params planid, target_date
     * @return {timeslot_s: 开始时间点}
     */
    public function actionGetDayInfoDS()
    {
        $planid = Yii::app()->request->getParam('planid', 604);
        $days = Yii::app()->request->getParam('target_date', '2018/11/6');
        $arr = explode("/", $days);
        if($arr[1] < 10){
            $arr[1] = '0'.$arr[1];
        }
        if($arr[2] < 10){
            $arr[2] = '0'.$arr[2];
        }
        $target_date = implode("", $arr);
        Yii::import('common.models.ptc.*');
        $criter = new CDbCriteria();
        $criter->compare('planid', $planid);
        $criter->compare('target_date', $target_date);
        $item = ParentMeetingItem::model()->findAll($criter);
        foreach($item as $k => $v){
            $arr_childid[] = $v->childid;
            $timeslot = $v->timeslot;
            $arr_s = explode(",", $timeslot);
            $timeslot_s[] = Array(
                'timeslot_s' => $arr_s[0],
                'childid' => $v->childid,
                'meet_index' => $v->meet_index
            );
        }
        if($timeslot_s){
            $criteria = new CDbCriteria();
            $criteria->compare('childid', $arr_childid);
            $criteria->index = 'childid';
            $items = ChildProfileBasic::model()->findAll($criteria);
            foreach($timeslot_s as $key => $v1){
                $events[$v1['timeslot_s']][] = array(
                    'childid' => $v1['childid'],
                    'childName' => isset($items[$v1['childid']])?$items[$v1['childid']]->getChildName():'',
                    'timeslot_s' => $v1['timeslot_s'],
                    'meet_index' => $v1['meet_index']
                );
            }
        }
        echo CJSON::encode($events);
    }

	public function actionClearChild()
	{
        if($this->checkTeachers()){
            $unikey = Yii::app()->request->getParam('unikey', '');
            $arr = explode("_", $unikey);
            $planid = $arr[0];
            $days = $arr[1];
            $times = $arr[2];
            $meet_index = $arr[3];
            Yii::import('common.models.ptc.*');
			$model = ParentMeetingPlan::model()->findByPk($planid);
			if($model->school_id == $this->branchId){
				$criter = new CDbCriteria();
				$criter->compare('planid', $planid);
				$criter->compare('target_date', $days);
				$criter->compare('timeslot', $times);
				$criter->compare('meet_index', $meet_index);
				$item = ParentMeetingItem::model()->find($criter);
				$childid = $item->childid;
				$item->childid = 0;
				$item->op_userid = 0;
				$item->op_timestamp  = 0;
				if($item->save()){
					$this->addMessage('state', 'success');
					$this->addMessage('message', Yii::t('global', 'Data deleted'));
					$this->addMessage('data', array('unikey' => $unikey, 'childid' => $childid));
					$this->addMessage('callback', 'cbClearChild');
				}
				else{
					$this->addMessage('state', 'fail');
					$this->addMessage('message', Yii::t('message', 'Data Saving Failed!'));
				}
			}
        }
        else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', 'System Error!'));
        }
		$this->showMessage();
	}

	public function actionPrintPtc()
	{
		$this->layout='//layouts/print';
		$this->printFW = $this->branchObj->getPrintHeader();
		Yii::import('common.models.ptc.*');
		$id = Yii::app()->request->getParam('id', 0);

		$planModel = ParentMeetingPlan::model()->findByPk($id);
        $classModel = IvyClass::model()->findByPk($planModel->classid);

		$criteria = new CDbCriteria();
        $criteria->compare('planid', $id);
        $criteria->order  = 'target_date ASC';
		$item = ParentMeetingItem::model()->findAll($criteria);
        $dataArr = array();
        $events = array();
		if($item) {
            $select_childid = array();
            $item_values = array();
            foreach ($item as $k => $v) {
                $timelotarray = explode(",", $v['timeslot']);
                $item_values[$v->target_date][] = array(
                    'childid' => $v->childid,
                    'start' => $timelotarray[0],
                    'end' => $timelotarray[1]
                );
                if ($v->childid)
                    $select_childid[$v->childid] = $v->childid;
            }
            $items = array();
            if($select_childid){
                $criteria = new CDbCriteria();
                $criteria->compare('childid', $select_childid);
                $criteria->index = 'childid';
                $items = ChildProfileBasic::model()->findAll($criteria);
            }

            foreach ($item_values as $unikey => $value) {
                foreach ($value as $childName) {
                    $events[$unikey][] = array(
                        'title' => (isset($items) && isset($items[$childName['childid']])) ? $items[$childName['childid']]->getChildName() : '',
                        'start' => $childName['start'],
                        'end' => $childName['end']
                    );
                }
            }

            if($events) {
                $month = $this->getDateFromRange(reset(array_keys($events)), end(array_keys($events)));

                $array = array();
                $k = 0;
                $oldM = 0;

                // 根据开始时间和结束时间 弄成以月分隔的数组$array[自增下标][时间] = 周几 0-6
                foreach ($month as $val) {
                    $w = date("w", strtotime($val));
                    $m = date("m", strtotime($val));
                    if ($m != $oldM) {
                        $k += 1;
                        $oldM = $m;
                    }
                    $array[$k][$val] = $w;
                }


                $calendar = array();
                $key = 0;
                // 把$array里面每月的数组在以每周7天来分隔数组
                foreach ($array as $val) {
                    $num = 0;
                    foreach ($val as $d => $month) {
                        $keysa = date("Y-m", strtotime($d));
                        $calendar[$keysa][$num][] = $d;
                        if ($month == 6) {
                            $num += 1;
                        }
                    }
                    $key++;
                }

                //  以周的数组判断是否7个  前后补齐
                foreach ($calendar as $key => $val) {
                    foreach ($val as $k => $itemas) {
                        if (count($itemas) < 7) {
                            $star = strtotime(reset($itemas));
                            $end = strtotime(end($itemas));
                            $starmonthDate = date('w', $star);
                            $starmonthDay = date('d', $star);
                            $endmonthDate = date('w', $end);
                            $endmonthDay = date('m', $end);
                            // 补齐前面
                            if ($starmonthDate > 0) {
                                $before = $star - 86400;
                                for ($i = 0; $i < $starmonthDate; $i++) {
                                    if ($starmonthDay < 2) {
                                        array_unshift($itemas, 0);
                                    } else {
                                        $beforeTime = date("Ymd", $before);
                                        array_unshift($itemas, $beforeTime);
                                        $before -= 86400;
                                    }
                                }
                            }

                            // 补齐后面
                            if ($endmonthDate < 6) {
                                $after = $end + 86400;
                                for ($i = $endmonthDate; $i < 6; $i++) {
                                    $NewEndmonthDate = date('m', $after);
                                    if ($endmonthDay != $NewEndmonthDate) {
                                        array_push($itemas, 0);
                                    } else {
                                        $afterTime = date("Ymd", $after);
                                        array_push($itemas, $afterTime);
                                        $after += 86400;
                                    }
                                }
                            }
                        }

                        $dataArr[$key][$k] = $itemas;
                    }
                }
            }
        }

		$this->render('printptc', array('events'=>$events, 'dataArr' => $dataArr, 'classTitle' => $classModel->title));
	}


    function getDateFromRange($startdate, $enddate){
        $stimestamp = strtotime($startdate);
        $etimestamp = strtotime($enddate);
        // 计算日期段内有多少天
        $days = ($etimestamp-$stimestamp)/86400+1;
        // 保存每天日期
        $date = array();
        for($i=0; $i<$days; $i++){
            $date[] = date('Ymd', $stimestamp+(86400*$i));
        }
        return $date;
    }

    public function actionGetBcoverChildren($id=0, $classid=0)
    {
        $category = Yii::app()->request->getParam('category', 'BackCover');
        $childids = array();
        if($id){
            Yii::import('common.models.portfolio.*');

            $criteira = new CDbCriteria();
            $criteira->compare('t.media_id', $id);
            $criteira->compare('t.category', $category);
            $criteira->compare('report.classid', $classid);
            $items = SReportItem::model()->with('report')->findAll($criteira);
            foreach($items as $item){
                $childids[] = $item->report->childid;
            }
        }
        echo CJSON::encode($childids);
    }

    public function actionSaveBcover()
    {
        if(Yii::app()->request->isAjaxRequest && Yii::app()->request->isPostRequest){
            $pid = Yii::app()->request->getPost('pid', 0);
            $classid = Yii::app()->request->getPost('classid', 0);
            $semester = Yii::app()->request->getPost('semester', 0);
            $_childid = Yii::app()->request->getPost('childid', 0);
            $children = Yii::app()->request->getPost('children', array());
            $category = Yii::app()->request->getPost('category', 'BackCover');
            $postedChildren = $children;
            $existChildren = array();
            if($pid && $classid && $semester && $_childid){
                Yii::import('common.models.portfolio.*');
                parent::initExt();
                Yii::import('common.models.calendar.Calendar');
                $calendarObj = Calendar::model()->findByPk($this->calendarId);
                $criteira = new CDbCriteria();
                $criteira->compare('t.media_id', $pid);
                $criteira->compare('t.category', $category);
                $criteira->compare('report.classid', $classid);
                $criteira->compare('report.childid', '<>'.$_childid);
                $items = SReportItem::model()->with('report')->findAll($criteira);
                foreach($items as $item){
                    $existChildren[] = $item->report->childid;
                }

                // 需要删除的孩子ID
                $toRemoveChildren   = array_diff($existChildren, $postedChildren);
                // 需要添加的孩子ID
                $toAddChildren      = array_diff($postedChildren, $existChildren);
                // 未变化的孩子ID， 无需处理
                //$untainedChildren   = array_intersect($postedChildren, $existChildren);

                // 删除
                if($toRemoveChildren){
                    foreach($items as $item){
                        if(in_array($item->report->childid, $toRemoveChildren)){
                            $item->delete();
                        }
                    }
                }

                // 新增
                if($toAddChildren){
                    foreach($children as $childid){
                        $model = SReport::model()->findByAttributes(array('childid'=>$childid, 'semester'=>$semester, 'classid'=>$classid));
                        if($model == null)
                            $model = new SReport();
                        $model->schoolid = $this->branchId;
                        $model->classid = $classid;
                        $model->childid = $childid;
                        $model->pdf_file = 0;
                        $model->yid = $this->calendarId;
                        $model->startyear = $calendarObj->startyear;
                        $model->semester = $semester;
                        $model->template_id	 = $this->getTemplateId($calendarObj->startyear, $classid);
                        $model->timestamp = time();
                        $model->uid = Yii::app()->user->id;
                        if($model->save()){
                            $criteira = new CDbCriteria();
                            $criteira->compare('report_id', $model->id);
                            $criteira->compare('category', $category);
                            $subModel = SReportItem::model()->find($criteira);
                            if($subModel == null)
                                $subModel = new SReportItem();
                            $subModel->report_id = $model->id;
                            $subModel->category = $category;
                            $subModel->subcategory = ($category == 'TeacherSign') ? $category : 'BackCoverPhoto';
                            $subModel->media_id = $pid;
                            $subModel->save();
                        }
                    }
                }

                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('teaching', 'Save succefully!'));
                $this->addMessage('callback', 'cbAssign');
                $this->showMessage();
            }
        }
    }

    /**
     * @return mixed
     * Daystar 学期报告
     */
    public function taskDsreport01()
    {
        Yii::import('common.models.reportCards.*');

        $crit = new CDbCriteria();
        $crit->compare('class_id', $this->selectedClassId);
        $crit->compare('semester', $this->selectedSemester);
        $crit->compare('status', 1);
        $crit->index = 'child_id';

        $taskData['onlined'] = array_keys(ReportsData::model()->findAll($crit));
        $taskData['children'] = $this->getNameList($this->selectedClassId);
        $taskData['calendarData'] = $this->getYidStartYearByClassIdAndBranchId($this->selectedClassId, $this->branchId);
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');

        return $taskData;
    }

    /**
     * 月任务，学习领域报告
     *
     * @return void
     */
    public function taskLearning()
    {
        Yii::import('common.models.reportCards.*');

        $crit = new CDbCriteria();
        $crit->compare('class_id', $this->selectedClassId);
        $crit->compare('semester', $this->selectedSemester);
        $crit->compare('status', 1);
        $crit->index = 'child_id';

        $taskData['onlined'] = array_keys(ReportsData::model()->findAll($crit));
        $taskData['children'] = $this->getNameList($this->selectedClassId);
        $taskData['calendarData'] = $this->getYidStartYearByClassIdAndBranchId($this->selectedClassId, $this->branchId);

        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/cropperv1.5.6/cropper.min.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl.'/base/js/cropperv1.5.6/cropper.min.css');

        return $taskData;
    }

    public function actionGetLearningInfo()
    {
        $classModel = IvyClass::model()->findByPk($this->selectedClassId);
        $learningDomainsType = $classModel->classtype;
        // 查找是否自定义了学习领域标准
        $reportModel = SReport::model()->findByAttributes(array('childid' => $this->selectedChildId,'yid' => $classModel->yid, 'semester' => $this->selectedSemester));
        if ($reportModel && $reportModel->learning_type) {
            $learningDomainsType = $reportModel->learning_type;
        }
        // 获取学习领域及标准
        Yii::import('common.models.learning.*');
        $crit = new CDbCriteria();
        $crit->compare('t.status', 1);
        $crit->compare('t.version', 2);

        if ($this->branchId == "BJ_QFF") {
            $crit->compare('t.school_type', 'qf');
        } else {
            $crit->compare('t.school_type', 'ivy');
        }
        $crit->compare('t.type', array($learningDomainsType, 'ibs'));
        $crit->order = 't.sort ASC';
        $crit->with = 'items';
        $learningModels = LearningDomains::model()->findAll($crit);
        $crit = new CDbCriteria();
        $crit->compare('yid', $classModel->yid);
        $crit->compare('childid', $this->selectedChildId);
        $crit->compare('semester', $this->selectedSemester);
        $crit->compare('classid', $this->selectedClassId);
        $crit->compare('status', 1);
        $crit->index = 'lid';
        $formModels = LearningSemester::model()->findAll($crit);
        $photoIds = array();
        foreach ($formModels as $val) {
            if ($val->intro_img) {
                $photoIds[] = $val->intro_img;
            }
            if ($val->items_img) {
                $photoIds[] = $val->items_img;
            }
        }
        $photosData = array();
        $photoIds = array_unique($photoIds);
        $calendarData = $this->getYidStartYearByClassIdAndBranchId($this->selectedClassId, $this->branchId);
        $startYear = $calendarData['startyear'];
        if($photoIds){
            ChildMedia::setStartYear($startYear);
            $photoObjs = ChildMedia::model()->findAllByPk($photoIds);
            foreach ($photoObjs as $photo) {
                // $photosData[$photo->id] = $photo->getMediaUrl(true);
                $photosData[$photo->id] = $photo->getOriginal();
            }
        }

        $crit = new CDbCriteria();
        $crit->compare('yid', $classModel->yid);
        $crit->compare('semester', $this->selectedSemester);
        $crit->compare('childid', $this->selectedChildId);
        $crit->compare('status', 1);
        $optionModels = LearningSemesterItems::model()->findAll($crit);
        $optionData = array();
        foreach ($optionModels as $optionModel) {
            $optionData[$optionModel->lid][$optionModel->itemid] = $optionModel->option;
        }
        if ($this->branchId == 'BJ_QFF') {
            // 健康，社会，科学，艺术，语言
            $cropperRatio = array(
                array(580 / 378, 580 / 378),
                array(580 / 378, 580 / 378),
                array(580 / 378, 580 / 378),
                array(580 / 378, 580 / 378),
                array(580 / 378, 580 / 378),
                array(580 / 378, 580 / 378),
                array(580 / 378, 580 / 378),
                array(580 / 378, 580 / 378),
                array(580 / 378, 580 / 378),
                array(580 / 378, 580 / 378),
                array(580 / 378, 580 / 378),
                array(580 / 378, 580 / 378),
            );
            $optionTitle = LearningDomains::getQfOptionTitle($this->selectedSemester);
        } else {
            // 健康，社会，科学，艺术，语言
            $cropperRatio = array(
                array(620 / 480, 520 / 330),
                array(620 / 360, 520 / 330),
                array(620 / 320, 520 / 330),
                array(620 / 365, 520 / 330),
                array(620 / 413, 520 / 330),
                array(620 / 413, 520 / 330),
                array(620 / 413, 520 / 330),

            );
            $optionTitle = LearningDomains::getOptionTitle($this->selectedSemester);
        }

        if ($this->branchId == 'BJ_QFF') {
            $learningDomainsTypeList = LearningDomains::getDomainsTypeList(30);
        } else {
            $learningDomainsTypeList = LearningDomains::getDomainsTypeList();
        }

        echo $this->renderPartial('_learninginfo', array(
            'learningDomainsType' => $learningDomainsType,
            'learningDomainsTypeFixed' => $formModels ? 1 : 0,
            'learningDomainsTypeList' => $learningDomainsTypeList,
            'learningModels' => $learningModels,
            'formModels' => $formModels,
            'optionData' => $optionData,
            'optionTitle' => $optionTitle,
            'photosData' => $photosData,
            'cropperRatio' => $cropperRatio,
            'branchId' => $this->branchId,
            'branchGroup' => $this->branchObj->group,
            'flagNum' => ($startYear + $this->selectedSemester),
            'classtype' => $classModel->classtype,
        ));
    }

    /**
     * 修改学生适用的学习领域标准
     * @return void
     */
    public function actionChildLearningTypeSave()
    {
        Yii::import('common.models.learning.*');
        $learning_type = Yii::app()->request->getParam('learning_type');
        $childid = $this->selectedChildId;
        $classid = $this->selectedClassId;
        $semester = $this->selectedSemester;

        // 查找是否已有学习领域的数据
        $crit = new CDbCriteria();
        $crit->compare('semester', $this->selectedSemester);
        $crit->compare('classid', $this->selectedClassId);
        $crit->compare('childid', $this->selectedChildId);
        $count = LearningSemester::model()->count($crit);
        if ($count > 0) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '修改失败，学习领域已有数据。');
            $this->showMessage();
        }

        $crit = new CDbCriteria();
        $crit->compare('semester', $this->selectedSemester);
        $crit->compare('classid', $this->selectedClassId);
        $crit->compare('childid', $this->selectedChildId);
        $crit->compare('schoolid', $this->branchId);
        $reportModel = SReport::model()->find($crit);
        if ($reportModel && $reportModel->stat > SReport::STATUS_OFFLINE) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '修改失败，报告已经提交或者审核，不可修改');
            $this->showMessage();
        }
        if (!$reportModel) {
            $classObj = IvyClass::model()->findByPk($classid);
            $yid = $classObj->yid;
            $calendarObj = Calendar::model()->findByPk($yid);
            $startyear = $calendarObj->startyear;

            $reportModel = new SReport();
            $reportModel->schoolid = $this->branchId;
            $reportModel->classid = $classid;
            $reportModel->childid = $childid;
            $reportModel->pdf_file = 0;
            $reportModel->yid = $yid;
            $reportModel->startyear = $startyear;
            $reportModel->semester = $semester;
            $reportModel->template_id  = $this->getTemplateId($startyear, $classid);
        }
        $reportModel->uid = Yii::app()->user->id;
        $reportModel->timestamp = time();
        $reportModel->learning_type = $learning_type;
        $reportModel->save();
        $this->addMessage('state', 'success');
        $this->addMessage('message', '修改成功');
        $this->addMessage('callback', 'cbSuccess');
        $this->showMessage();
    }

    public function actionResetLearningDomiansData() 
    {
        Yii::import('common.models.learning.*');
        $childid = $this->selectedChildId;
        $classid = $this->selectedClassId;
        $semester = $this->selectedSemester;

        if (!$childid || !$classid || !$semester) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '参数错误');
            $this->showMessage();
        }

        $crit = new CDbCriteria();
        $crit->compare('semester', $this->selectedSemester);
        $crit->compare('classid', $this->selectedClassId);
        $crit->compare('childid', $this->selectedChildId);

        LearningSemester::model()->deleteAll($crit);
        LearningSemesterItems::model()->deleteAll($crit);
        
        $this->addMessage('state', 'success');
        $this->addMessage('message', '清除成功');
        $this->addMessage('callback', 'cbSuccess');
        $this->showMessage();
    }

    public function actionSaveLearningInfo()
    {
        if ($_POST['LearningSemester']) {

            $crit = new CDbCriteria();
            $crit->compare('semester', $this->selectedSemester);
            $crit->compare('classid', $this->selectedClassId);
            $crit->compare('childid', $this->selectedChildId);
            $crit->compare('schoolid', $this->branchId);
            $crit->compare('stat', array(15,16,20));
            $srrportCount = SReport::model()->count($crit);
            if($srrportCount){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '已经提交或者审核，不可修改');
                $this->showMessage();
            }

            Yii::import('common.models.learning.*');
            $id = Yii::app()->request->getParam('id', 0);
            $lid = $_POST['LearningSemester']['lid'];
            $model = new LearningSemester();
            if ($id) {
                if ($temp = LearningSemester::model()->findByPk($id)) {
                    $model = $temp;
                }
            } elseif ($lid) {
                // 查找还在该领域是否有记录
                $crit = new CDbCriteria();
                $crit->compare('semester', $this->selectedSemester);
                $crit->compare('classid', $this->selectedClassId);
                $crit->compare('childid', $this->selectedChildId);
                $crit->compare('lid', $lid);
                $crit->compare('status', 1);
                if ($temp = LearningSemester::model()->find($crit)) {
                    $model = $temp;
                }
            }
            if ($model->isNewRecord) {
                $model->created_at = time();
                $model->created_by = $this->staff->uid;
            }
            $classModel = IvyClass::model()->findByPk($this->selectedClassId);
            $model->setAttributes($_POST['LearningSemester']);
            $model->yid = $classModel->yid;
            $model->semester = $this->selectedSemester;
            $model->childid = $this->selectedChildId;
            $model->classid = $this->selectedClassId;
            $model->status = 1;
            $model->updated_at = time();
            $model->updated_by = $this->staff->uid;;
            if (!$model->save()) {
                $error = current($model->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('message', $error[0]);
                $this->showMessage();
            }
            // 保存评价标准
            $crit = new CDbCriteria();
            $crit->compare('lid', $model->lid);
            $crit->compare('yid', $model->yid);
            $crit->compare('semester', $model->semester);
            $crit->compare('childid', $model->childid);
            $crit->compare('status', 1);
            $crit->index = 'itemid';
            $optionModels = LearningSemesterItems::model()->findAll($crit);
            $options = $_POST['options'];
            foreach ($options as $key => $value) {
                if (!isset($optionModels[$key])) {
                    $optionModel = new LearningSemesterItems();
                    $optionModel->created_at = time();
                    $optionModel->created_by = $this->staff->uid;
                } else {
                    $optionModel = $optionModels[$key];
                }
                $optionModel->semester = $model->semester;
                $optionModel->yid = $model->yid;
                $optionModel->childid = $model->childid;
                $optionModel->classid = $model->classid;
                $optionModel->lid = $model->lid;
                $optionModel->itemid = $key;
                $optionModel->option = $value;
                $optionModel->status = 1;
                $optionModel->updated_at = time();
                $optionModel->updated_by = $this->staff->uid;
                $optionModel->save();
            }
            $this->addMessage('state', 'success');
            $this->addMessage('callback', 'cbSuccess');
            $this->addMessage('data', array('id' => $model->lid));
            $this->addMessage('message', 'Success');
            $this->showMessage();
        }
    }

    // 月任务，孩子生长发育
    public function taskHealth()
    {
        Yii::import('common.models.reportCards.*');

        $crit = new CDbCriteria();
        $crit->compare('class_id', $this->selectedClassId);
        $crit->compare('semester', $this->selectedSemester);
        $crit->compare('status', 1);
        $crit->index = 'child_id';

        $taskData['onlined'] = array_keys(ReportsData::model()->findAll($crit));
        $taskData['children'] = $this->getNameList($this->selectedClassId);


        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/backbone-min.js');

        return $taskData;
    }

    public function actionGetChildSemesterReport01($classid=0, $semester=0){
        Yii::import('common.models.reportCards.*');
        Yii::import('common.models.attendance.*');
        //学校课程
        $cfgCampusProgram = CommonUtils::LoadConfig('CfgCampusProgram');
        $childid = Yii::app()->request->getPost('childid', 0);

        $child = ChildProfileBasic::model()->findByPk($childid);

        $criteria = new CDbCriteria();
        $criteria->compare('class_id', $classid);
        $criteria->compare('child_id', $childid);
        $criteria->compare('semester', $semester);
        $model = ReportsData::model()->with('ext')->find($criteria);

        $classModel = IvyClass::model()->findByPk($classid);
        $calendarModel = Calendar::model()->findByPk($classModel->yid);
        $timepoints = explode(',', $calendarModel->timepoints);

        $start_time = ($semester == 1) ? $timepoints[0] : $timepoints[2] ;
        $end_time = ($semester == 1) ? $timepoints[1] : $timepoints[3] ;
        $criteria = new CDbCriteria();
        $criteria->compare('child_id', $childid);
        $criteria->compare('type', array(10,20,40));
        $criteria->compare('vacation_time_start', ">={$start_time}");
        $criteria->compare('vacation_time_end', "<={$end_time}");
        $childvacation = ChildVacation::model()->findAll($criteria);

        $absent = array(); // 缺勤
        $tardy = array();  // 迟到
        if($childvacation){
            foreach($childvacation as $item){
                if($item->type == ChildVacation::VACATION_SICK_LEAVE || $item->type == ChildVacation::VACATION_AFFAIR_LEAVE){
                    if($item->vacation_time_start == $item->vacation_time_end){
                        $absent[$item->vacation_time_start] = $item->vacation_time_start;
                    }else{
                        $calendarSchlloModel = CalendarSchoolDays::model()->countCalendarSchoolday($calendarModel->yid, $item->vacation_time_start, $item->vacation_time_end, 0);
                        foreach($calendarSchlloModel as $calendarItem){
                            foreach ($calendarItem as $key=>$day) {
                                foreach ($day['day'] as $dayItem) {
                                    $timeDay = $key . $dayItem;
                                    $absent[strtotime($timeDay)] = strtotime($timeDay);
                                }
                            }
                        }
                    }
                }
                if($item->type == ChildVacation::VACATION_LATE){
                    $tardy[$item->vacation_time_start] = $item->id;
                }
            }
        }
        if(empty($model)){
            $model = new ReportsData;
            $model->class_id = $classid;
            $model->child_id = $childid;
            $model->startyear = $calendarModel->startyear;
            $model->semester = $semester;
            $model->attendance_ext = CJSON::encode(array(
                'absent' => count($absent),
                'tardy' => count($tardy),
                'esl' => "",
                'csl' => "",
                'support' => "",
                'leap' => "",
                'other' => "",
                'other_data' => "",
            ));
            $model->status = 0;
            $model->pdf_file = 0;
            $model->updated_user = Yii::app()->user->id;
            $model->updated = time();
            $model->custom_teacher = "";
            $model->save();
        }
        $templateid = 0;

        if($model){
            $attendance = json_decode($model->attendance_ext,true);
            $attendance['absent'] = count($absent);
            $attendance['tardy'] = count($tardy);
            $model->attendance_ext = CJSON::encode($attendance);
            $model->save();
            $cateid = end($model->ext)->category_id;

            $cateModel = ReportsCategory::model()->findByPk($cateid);
            if($cateModel != null){
                $templateid = $cateModel->template_id;
            }
        }

        $calendarData = $this->getYidStartYearByClassIdAndBranchId($classid, $this->branchId);
        $startyear = $calendarData['startyear'];
        $classObj = IvyClass::model()->findByPk($classid);
        $grade = $classObj->classtype;
        $uid = Yii::app()->user->getId();
        $disabledIdList = array();
        if ($startyear < 2023) {
            if(!$templateid){
                $criteria = new CDbCriteria();
                $criteria->compare('t.branch_id', $this->branchId);
                $criteria->compare('t.for_age', $grade);
                $criteria->compare('template.active', 1);
                $template = ReportCardTemplateClass::model()->with('template')->find($criteria);
                $templateid = $template->template_id ? $template->template_id : 0;
            }
            $res = ReportsCategory::model()->getCategoryData($this->branchId, $templateid, $uid);
            $roots = $res['roots'];
            $subs = $res['subs'];
            $items = $res['items'];
        } else {
            $res = ReportsCategory::model()->getCategoryDataNew($this->branchId, $startyear, $grade, $uid, $this->selectedSemester);
            $roots = $res['roots'];
            $subs = $res['subs'];
            $items = $res['items'];
            $disabledIdList = $res['disabledIdList'];
        }

        $criteria = new CDbCriteria();
        $criteria->order = 'weight';
        $criteria->index = 'id';
        $options = ReportsOption::model()->findAll($criteria);
        $optionsList = array();
        if (!empty($options)){
            foreach ($options as $k=>$option){
                if (!in_array($classObj->yid, array(117, 135)) && $option->label == 'NE') {
                    continue;
                }
                $optionsList[$option->group_id][$k] = $option;
            }
        }

        //已经保存数据
        $reportsOption = array();
        if (!empty($model->ext)){
            foreach ($model->ext as $val){
                $reportsOption[$val->category_id]['category_id'] = $val->category_id;
                $reportsOption[$val->category_id]['memo'] = $val->extra_data;
                $reportsOption[$val->category_id]['status'] = $val->status;
                $reportsOption[$val->category_id]['option'] = CJSON::decode($val->item_data);
            }
        }
        // 获取 gradebook 平均分
        $gradebookScore = array();
        if (in_array($this->branchId, array('BJ_DS', 'BJ_SLT'))){
            $requestUrl = 'gradebook/getChildScore';
            $requestData = array('school_id' => $this->branchId, 'startyear' => $startyear, 'semester' => $semester * 10, 'child_id' => $childid);
            $res = CommonUtils::requestDsOnline($requestUrl, $requestData);
            if (isset($res['code']) && $res['code'] == 0) {
                $gradebookScore = $res['data'];
            }
        }
        if (!$model->uptime) {
            $model->uptime = time();
        }

        $html = $this->renderPartial('_dsreportinfo', array(
            'model'=>$model,
            'options'=>$optionsList,
            'child'=>$child,
            'roots'=>$roots,
            'subs'=>$subs,
            'items'=>$items,
            'classid'=>$classid,
            'semester'=>$semester,
            'classObj'=>$classObj,
            'startyear'=>$startyear,
            'reportsOption'=>$reportsOption,
            'disabledIdList'=>$disabledIdList,
            'gradebookScore'=>$gradebookScore,
            'canSave'=>$this->canSave($classModel->yid, $semester) ? 1 : 0,
        ));
        echo $html;
    }

    public function actionSaveClassTeacher() {
        Yii::import('common.models.reportCards.*');
        
        $reportid = Yii::app()->request->getPost('reportid');
        $data = Yii::app()->request->getPost('data', array());

        if (!$reportid) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '参数错误');
            $this->showMessage();
        }

        $model = ReportsData::model()->findByPk($reportid);
        if (!$model) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '参数错误');
            $this->showMessage();
        }
        $customTeacherList = array();
        foreach ($data as $item) {
            $customTeacherList[] = array(
                'teacherid' => $item['uid'],
                'weight' => $item['weight'],
            );
        }
        if ($model->semester == 1) {
            $model->custom_teacher = json_encode($customTeacherList);
            $model->pdf_file = 0;
            $model->save();
        } else {
            $criteria = new CDbCriteria();
            $criteria->compare('class_id', $model->class_id);
            $criteria->compare('child_id', $model->child_id);
            $criteria->compare('semester', 1);
            $firstModel = ReportsData::model()->find($criteria);
            if (!$firstModel) {
                $firstModel = new ReportsData;
                $firstModel->class_id = $model->class_id;
                $firstModel->child_id = $model->child_id;
                $firstModel->startyear = $model->startyear;
                $firstModel->semester = $model->semester;
                $firstModel->attendance_ext = CJSON::encode(array(
                    'absent' => 0,
                    'tardy' => 0,
                    'esl' => "",
                    'csl' => "",
                    'support' => "",
                    'leap' => "",
                    'other' => "",
                    'other_data' => "",
                ));
                $firstModel->status = 0;
                $firstModel->updated_user = Yii::app()->user->id;
                $firstModel->updated = time();
            }
            $firstModel->pdf_file = 0;
            $firstModel->custom_teacher = json_encode($customTeacherList);
            $firstModel->save();
        }

        $this->addMessage('state', 'success');
        $this->addMessage('data', array());
        $this->showMessage();
    }

    public function actionGetClassTeachers($childid, $classid, $semester) {
        Yii::import('common.models.reportCards.*');

        $criteria = new CDbCriteria();
        $criteria->compare('class_id', $classid);
        $criteria->compare('child_id', $childid);
        $criteria->compare('semester', 1);
        $model = ReportsData::model()->find($criteria);
        $customTeacherJson = $model ? $model->custom_teacher : false;
        $childTeacherList = $this->classTeachers($classid, $customTeacherJson);
        $this->addMessage('state', 'success');
        $this->addMessage('data', $childTeacherList);
        $this->showMessage();
    }

    public function classTeachers($classid, $customTeacherJson = false) {
        $classObj = IvyClass::model()->with('schoolInfo')->findByPk($classid);
        $yid = $classObj->yid;
        $teacherList = array();
        $criteria = new CDbCriteria();
        $criteria->compare('classid', $classid);
        $criteria->compare('isheadteacher', 1);//只要主班老师
        $criteria->order = 't.weight asc';
        $classteacher = ClassTeacher::model()->with('userWithProfile')->findAll($criteria);
        foreach($classteacher as $t){
            $cnName = $t->userWithProfile->name;
            $enName = $t->userWithProfile->profile->first_name . " " . $t->userWithProfile->profile->last_name;
            $enName = trim($enName);

            if($enName == ""){
                $_name = isset($forPrint) && $forPrint ? sprintf('<span class="cn">%s</span>', $cnName) : $cnName;
            }elseif($cnName == ""){
                $_name = isset($forPrint) && $forPrint ? sprintf('<span class="en">%s</span>', $enName) : $enName;
            }else{
                $_name = isset($forPrint) && $forPrint ? sprintf('<span class="cn">%s</span> <span class="en">%s</span>',
                    $cnName, $enName) :
                    sprintf('%s %s', $cnName, $enName);
            }
            $teacherList[] = array(
                'name'=>$_name,
                'weight'=>$t->weight,
                'teacherid'=>$t->teacherid,
                'is_custom'=>0,
            );
        }
        $customTeacherList = array();
        if ($customTeacherJson) {
            $customTeacherArray = json_decode($customTeacherJson, true);
            if ($customTeacherArray) {

                $customTeacherIds = array();
                $teacher2weight = array();
                foreach ($customTeacherArray as $customTeacher) {
                    $customTeacherIds[] = $customTeacher['teacherid'];
                    $teacher2weight[$customTeacher['teacherid']] = $customTeacher['weight'];
                }
                $customTeacherModelList = User::model()->with('profile')->findAllByPk($customTeacherIds);
                foreach ($customTeacherModelList as $customTeacherModel) {
                    $customTeacherList[] = array(
                        'name'=>$customTeacherModel->getName(),
                        'weight'=>$teacher2weight[$customTeacherModel->uid],
                        'teacherid'=>$customTeacherModel->uid,
                        'is_custom'=>1,
                    );
                }
            }
        }
        $teacherList = array_merge($teacherList, $customTeacherList);
        // 使用usort函数进行排序
        usort($teacherList, function ($a, $b) {
            if ($a['weight'] == $b['weight']) {
                if ($a['is_custom'] == 1 && $b['is_custom'] == 0) {
                    return 1;
                }
                return 0;
            }
            return ($a['weight'] < $b['weight']) ? -1 : 1;
        });

        return $teacherList;
    }

    public function actionSaveDsreport()
    {
        if(Yii::app()->request->isAjaxRequest && Yii::app()->request->isPostRequest){
            Yii::import('common.models.reportCards.*');

            $childid = Yii::app()->request->getPost('childid', 0);
            $categoryid = Yii::app()->request->getPost('category_id', 0);
            $classid = Yii::app()->request->getPost('classid', 0);
            $semester = Yii::app()->request->getPost('semester', 0);
            $option = Yii::app()->request->getPost('option', array());
            $memo = Yii::app()->request->getPost('memo', '');
            $status = Yii::app()->request->getPost('status', 0);
            $this->checkReportDataEditable($semester);
            if(count($option)){
                $calendarData = $this->getYidStartYearByClassIdAndBranchId($classid, $this->branchId);
                $criteira = new CDbCriteria();
                $criteira->compare('class_id', $classid);
                $criteira->compare('child_id', $childid);
                $criteira->compare('startyear', $calendarData['startyear']);
                $criteira->compare('semester', $semester);
                $model = ReportsData::model()->find($criteira);
                if($model === null){
                    $model = new ReportsData();
                    $model->status = 0;
                }
                $model->class_id = $classid;
                $model->child_id = $childid;
                $model->pdf_file = 0;
                $model->startyear = $calendarData['startyear'];
                $model->semester = $semester;
                $model->updated_user = Yii::app()->user->id;
                $model->updated = time();
                if($model->save()){
                    $model_item = ReportsDataExtra::model()->find('data_id=:data_id and category_id=:category_id',array(':data_id'=>$model->id,':category_id'=>$categoryid));
                    if($model_item === null){
                        $model_item = new ReportsDataExtra();
                    }
                    $model_item->data_id = $model->id;
                    $model_item->category_id = $categoryid;
                    $model_item->item_data = CJSON::encode($option);
                    $model_item->extra_data = $memo;
                    $model_item->status = $status;
                    $model_item->uid = Yii::app()->user->id;
                    $model_item->updated = time();
                    $model_item->save();
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message', 'Data Saved!'));
                    $this->addMessage('callback', 'cbSuccess');
                }
                else{
                    $this->addMessage('state', 'fail');
                    $err = current($model->getErrors());
                    $this->addMessage('message', $err[0]);
                }
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '请选择选项！');
            }
        }
        $this->showMessage();
    }

    //保存分数
    public function actionSaveDsreportOption()
    {
        if(Yii::app()->request->isAjaxRequest && Yii::app()->request->isPostRequest){
            Yii::import('common.models.reportCards.*');

            $childid = Yii::app()->request->getPost('childid', 0);
            $categoryid = Yii::app()->request->getPost('category_id', 0);
            $classid = Yii::app()->request->getPost('classid', 0);
            $semester = Yii::app()->request->getPost('semester', 0);
            $option = Yii::app()->request->getPost('option', array());
            $status = Yii::app()->request->getPost('status', 0);
            $this->checkReportDataEditable($semester);
            if(count($option)){
                $calendarData = $this->getYidStartYearByClassIdAndBranchId($classid, $this->branchId);
                $criteira = new CDbCriteria();
                $criteira->compare('class_id', $classid);
                $criteira->compare('child_id', $childid);
                $criteira->compare('startyear', $calendarData['startyear']);
                $criteira->compare('semester', $semester);
                $model = ReportsData::model()->find($criteira);
                if($model === null){
                    $model = new ReportsData();
                    $model->status = 0;
                }
                $model->class_id = $classid;
                $model->child_id = $childid;
                $model->pdf_file = 0;
                $model->startyear = $calendarData['startyear'];
                $model->semester = $semester;
                $model->updated_user = Yii::app()->user->id;
                $model->updated = time();
                if($model->save()){
                    $model_item = ReportsDataExtra::model()->find('data_id=:data_id and category_id=:category_id',array(':data_id'=>$model->id,':category_id'=>$categoryid));
                    if($model_item === null){
                        $model_item = new ReportsDataExtra();
                    }
                    $model_item->data_id = $model->id;
                    $model_item->category_id = $categoryid;
                    $model_item->item_data = CJSON::encode($option);
                    $model_item->status = $status;
                    $model_item->uid = Yii::app()->user->id;
                    $model_item->updated = time();
                    $model_item->save();
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message', 'Data Saved!'));
                    $this->addMessage('callback', 'cbSuccess');
                } else{
                    $this->addMessage('state', 'fail');
                    $err = current($model->getErrors());
                    $this->addMessage('message', $err[0]);
                }
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '请选择选项！');
            }
        }
        $this->showMessage();
    }

    //保存评语
    public function actionSaveDsreportMemo()
    {
        if(Yii::app()->request->isAjaxRequest && Yii::app()->request->isPostRequest){
            Yii::import('common.models.reportCards.*');

            $childid = Yii::app()->request->getPost('childid', 0);
            $categoryid = Yii::app()->request->getPost('category_id', 0);
            $classid = Yii::app()->request->getPost('classid', 0);
            $semester = Yii::app()->request->getPost('semester', 0);
            $memo = Yii::app()->request->getPost('memo', '');
            $status = Yii::app()->request->getPost('status', 0);
            $this->checkReportDataEditable($semester);
            $calendarData = $this->getYidStartYearByClassIdAndBranchId($classid, $this->branchId);
            $criteira = new CDbCriteria();
            $criteira->compare('class_id', $classid);
            $criteira->compare('child_id', $childid);
            $criteira->compare('startyear', $calendarData['startyear']);
            $criteira->compare('semester', $semester);
            $model = ReportsData::model()->find($criteira);
            if($model === null){
                $model = new ReportsData();
                $model->status = 0;
            }
            $model->class_id = $classid;
            $model->child_id = $childid;
            $model->pdf_file = 0;
            $model->startyear = $calendarData['startyear'];
            $model->semester = $semester;
            $model->updated_user = Yii::app()->user->id;
            $model->updated = time();
            if($model->save()){
                $model_item = ReportsDataExtra::model()->find('data_id=:data_id and category_id=:category_id',array(':data_id'=>$model->id,':category_id'=>$categoryid));
                if($model_item === null){
                    $model_item = new ReportsDataExtra();
                }
                $model_item->data_id = $model->id;
                $model_item->category_id = $categoryid;
                $model_item->item_data = empty($model_item->item_data) ? CJSON::encode(array()) : $model_item->item_data;
                $model_item->extra_data = $memo;
                $model_item->status = $status;
                $model_item->uid = Yii::app()->user->id;
                $model_item->updated = time();
                $model_item->save();
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message', 'Data Saved!'));
                $this->addMessage('callback', 'cbSuccess');
            }else{
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err[0]);
            }
        }
        $this->showMessage();
    }

    public function actionSetDsreportStatus()
    {
        if(Yii::app()->request->isAjaxRequest && Yii::app()->request->isPostRequest){
            Yii::import('common.models.reportCards.*');

            $id = Yii::app()->request->getPost('id', 0);
            $type = Yii::app()->request->getPost('type', '');

            if($id && $type){
                $model = ReportsData::model()->findByPk($id);
                if($model != null){
//                    $count = ReportsDataExtra::model()->count('data_id=:data_id and status=0',array(':data_id'=>$id));
//                    if ($count){
//                        $this->addMessage('state', 'fail');
//                        $this->addMessage('message', Yii::t('teaching', 'Not all subjects are complete yet.'));
//                    }else{
                        $model->status = $type == 'online' ? 1 : 0;
                        if ($type == 'online' && !$model->uptime) {
                            $model->uptime = time();
                        }

                        $classModel = IvyClass::model()->findByPk($model->class_id);

                        if($classModel->classtype == 'mc'){
                            $criteria = new CDbCriteria();
                            $criteria->compare('schoolid', 'BJ_DS');
                            $criteria->compare('classid', $model->class_id);
                            $criteria->compare('childid', $model->child_id);
                            $criteria->compare('startyear', $model->startyear);
                            $criteria->compare('semester', $model->semester);
                            $srModel = SReport::model()->find($criteria);
                            if(!$srModel){
                                $this->addMessage('state', 'fail');
                                $this->addMessage('message',Yii::t("","请先上传DPF"));
                                $this->showMessage();
                            }
                            $srModel->stat = ($model->status == 1) ? 20 : 10;
                            $srModel->save();
                        }

                        if($model->save()){
                            $this->addMessage('state', 'success');
                            $this->addMessage('message', Yii::t('message', 'Data Saved!'));
                        }
                        else{
                            $this->addMessage('state', 'fail');
                            $err = current($model->getErrors());
                            $this->addMessage('message', $err[0]);
                        }
//                    }
                }
                else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message',Yii::t('teaching', 'Please complete the report first.'));
                }
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('teaching', 'Please complete the report first.'));
            }
            $this->showMessage();
        }
    }

    //预览DS学期报告
    public function  actionPreviewReportDs()
    {
        Yii::import('common.models.portfolio.*');
        Yii::import('common.components.teaching.*');
        Yii::import('common.models.reportCards.*');

        $childId = Yii::app()->request->getParam('childid', 0);
        $reportId = Yii::app()->request->getParam('id', 0);
        $classId = Yii::app()->request->getParam('classid', 0);

        if($childId && $classId){
            $params = array(
                'schoolid' => $this->branchId,
                'childid' => $childId,
                'classid' => $classId,
                'id' => $reportId,
//                'flag' => $this->teacherFlagShow(),
            );
            $withToolBar = true;
            $downloadActionUrl = Yii::app()->createAbsoluteUrl(
                '//mteaching/semester/downloadReportDs',
                array('branchId'=>$this->branchId));
            $reportHtml =  ContentFetcher::getSemesterReportOfDs($params,
                $withToolBar,
                $downloadActionUrl
            );
            echo $reportHtml;
        }
    }

    public function actionClearPDF()
    {
        if(Yii::app()->request->isAjaxRequest && Yii::app()->request->isPostRequest){
            $id = Yii::app()->request->getPost('id', 0);
            SReport::model()->updateByPk($id, array('pdf_file'=>0));
            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('global', '清除成功'));
        }
        else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('global', '清除失败'));
        }
        $this->showMessage();
    }

    /**
     * 判断当前用户，如果是老师显示所教的科目，否则显示全部
     */
    public function teacherFlagShow(){
        Yii::import('common.models.grades.TeacherSubjectLink');
        $flag=array();
        $link = TeacherSubjectLink::model()->findAll('schoolid=:schoolid and teacher_uid=:teacher_uid and status=1',
            array(':schoolid'=>$this->branchId,':teacher_uid'=>Yii::app()->user->getId())
        );
        if ($link){
            foreach($link as $tag){
                $flag[] = $tag->subject_flag;
            }
        }
        return $flag;
    }

    public function actionOpenTimeslot()
    {
        $planid = Yii::app()->request->getPost('planid', 0);
        $days = Yii::app()->request->getPost('days', array());
        $timeslot = Yii::app()->request->getPost('timeslot', '');

        Yii::import('common.models.ptc.*');
        if(Yii::app()->request->isAjaxRequest) {
            $times = $this->timeslot($planid, $timeslot);
            foreach ($days as $k1 => $v1) {
                for ($i = 0; $i < 3; $i++) {
                    $model = new ParentMeetingItem;
                    $model->planid = $planid;
                    $model->target_date = date('Ymd', $v1);
                    $model->timeslot = $times;
                    $model->meet_index = $i;
                    $model->stem = 0;
                    $model->save();
                }
            }

            $this->addMessage('state', 'success');
            $this->addMessage('message', '成功');
            $this->addMessage('data', array(0, 1, 2));
            $this->showMessage();
        }
        $this->addMessage('state', 'fail');
        $this->addMessage('message', '非法操作');
        $this->showMessage();
    }

    public function actionCancelTimeslot()
    {
        $planid = Yii::app()->request->getPost('planid', 0);
        $days = Yii::app()->request->getPost('days', array());
        $timeslot = Yii::app()->request->getPost('timeslot', '');
        Yii::import('common.models.ptc.*');

        if(Yii::app()->request->isAjaxRequest) {
            $dataYmd = array();
            foreach ($days as $val){
                $dataYmd[] = date("Ymd", $val);
            }
            $times = $this->timeslot($planid, $timeslot);

            foreach ($dataYmd as $_date) {
                if($planid && $_date && $times) {
                    $sql = "delete from pmeet_plan_item where planid=".$planid." and target_date='".$_date."' and timeslot='".$times."'";
                    $rs = Yii::app()->subdb->createCommand($sql)->query();
                }
            }

            $this->addMessage('state', 'success');
            $this->addMessage('message', '成功');
            $this->addMessage('data', '');
            $this->showMessage();
        }else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '非法操作');
            $this->showMessage();
        }
    }

    public function actionAddNum()
    {
        $planid = Yii::app()->request->getPost('planid', 0);
        $days = Yii::app()->request->getPost('days', array());
        $timeslot = Yii::app()->request->getPost('timeslot', '');
        Yii::import('common.models.ptc.*');

        if(Yii::app()->request->isAjaxRequest) {
            $times = $this->timeslot($planid, $timeslot);
            $data = 0;
            foreach ($days as $k1 => $v1){
                $criter = new CDbCriteria();
                $criter->compare('planid', $planid);
                $criter->compare('target_date', date('Ymd', $v1));
                $criter->compare('timeslot', $times);
                $criter->limit = 1;
                $criter->order = "meet_index DESC";
                $oldModel = ParentMeetingItem::model()->find($criter);
                $meet_index = ($oldModel) ? $oldModel->meet_index + 1 : 0;
                $model = new ParentMeetingItem;
                $model->planid = $planid;
                $model->target_date = date('Ymd', $v1);
                $model->timeslot = $times;
                $model->meet_index = $meet_index;
                $model->stem = 0;
                $model->save();
                $data = $model->meet_index;
            }

            $this->addMessage('state', 'success');
            $this->addMessage('message', '成功');
            $this->addMessage('data', $data);
            $this->showMessage();
        }else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '非法操作');
            $this->showMessage();
        }
    }

    public function actionDelNum()
    {
        $planid = Yii::app()->request->getPost('planid', 0);
        $days = Yii::app()->request->getPost('days', array());
        $timeslot = Yii::app()->request->getPost('timeslot', '');
        $index = Yii::app()->request->getPost('index', '');
        Yii::import('common.models.ptc.*');

        if(Yii::app()->request->isAjaxRequest) {
            $dataYmd = array();
            foreach ($days as $val){
                $dataYmd[] = date("Ymd", $val);
            }
            $times = $this->timeslot($planid, $timeslot);

            $criter = new CDbCriteria();
            $criter->compare('planid', $planid);
            $criter->compare('target_date', $dataYmd);
            $criter->compare('timeslot', $times);
            $criter->compare('meet_index', $index);
            $des = ParentMeetingItem::model()->findAll($criter);
            if($des) {
                foreach ($des as $k => $v) {
                    $v->delete();
                }
            }

            $this->addMessage('state', 'success');
            $this->addMessage('message', '成功');
            $this->addMessage('data', $index);
            $this->showMessage();
        }else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '非法操作');
            $this->showMessage();
        }
    }

    /**
     * 设置幼儿园报告的语言
     *
     * @return void
     */
    public function actionSetReportLang()
    {
        $id = Yii::app()->request->getPost('id');
        $lang = Yii::app()->request->getPost('lang');

        $this->addMessage('state', 'fail');
        if (!$id) {
            $this->addMessage('message', 'ID 不能为空');
            $this->showMessage();
        }
        if (!in_array($lang, array('cn', 'en'))) {
            $this->addMessage('message', '语言参数错误');
            $this->showMessage();
        }
        $report = SReport::model()->findByPk($id);
        if (!$report) {
            $this->addMessage('message', '报告未找到');
            $this->showMessage();
        }
        $report->lang = $lang;
        $report->save();
        $this->addMessage('state', 'success');
        $this->addMessage('message', 'success');
        $this->showMessage();
    }

    public function timeslot($planid,$timeslot)
    {
        $criter = new CDbCriteria();
        $criter->compare('id', $planid);
        $item = ParentMeetingPlan::model()->find($criter);

        $duration = $item->default_duration;
        $timearray = explode(":", $timeslot);
        $hour = $timearray[0];
        $minute = $timearray[1];
        $newminute = $minute + $duration;
        if ($newminute) {
            $hour = floor($newminute / 60) + $hour;
            if ($hour < 10) {
                $hour = '0' . $hour;
            }
            $newminute = fmod($newminute, 60);
            if ($newminute == 0) {
                $newminute = '00';
            } elseif ($newminute == 5) {
                $newminute = '05';
            }
        }
        $endtime = $hour . ":" . $newminute;
        $times = $timeslot . ',' . $endtime;

        return $times;
    }

    public function getTemplateId($startYear, $classId)
    {
        if (in_array($classId, array(1398))) {
            return 'Ivy01';
        }

        if ($startYear >= 2019) {
            return 'Ivy03';
        }
        return 'Ivy01';
    }

    public function actionGetGradebookScoreDetail()
    {
        $startyear = Yii::app()->request->getParam('startyear', 0);
        $category = Yii::app()->request->getParam('category', 0);
        $item = Yii::app()->request->getParam('item', 0);

        if (!$startyear || !$category || !$item) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '参数错误');
            $this->showMessage();
        }

        $requestUrl = 'gradebook/getChildScoreDetail';
        $requestData = array(
            'school_id' => $this->branchId,
            'startyear' => $startyear,
            'semester' => $this->selectedSemester * 10,
            'child_id' => $this->selectedChildId,
            'category_id' => $category,
            'item_id' => $item,
        );
        $res = CommonUtils::requestDsOnline($requestUrl, $requestData);
        if (isset($res['code']) && $res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();

    }
    
}
