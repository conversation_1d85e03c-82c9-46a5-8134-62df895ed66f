<?php

/**
 * 为提高效率，本controller下未做权限验证；只做签名验证
 */
class AllinpayOrderController extends Controller
{
    public function filters()
    {
        return array(
            'accessControl',
        );
    }

    public function accessRules()
    {
        return array(
            array('allow',
                'actions'=>array('amount'),
                'ips'=>array('**************','**************'),
            )
        );
    }

    public function init()
    {
        parent::init();
        Yii::import('common.models.yeepay.*');
    }

    /**
     * 通联POS订单查询
     * @return [type] [description]
     */
    public function actionAmount()
    {
        if(Yii::app()->request->isPostRequest){
            $id     = Yii::app()->request->getParam('id', '');
            $mac    = Yii::app()->request->getParam('mac', '');
            if($mac == md5(sprintf('%s|%s', $id, $this->securityKey))){
                $msg = '订单号有误';
                Yii::import('common.models.invoice.Invoice');
                $model = YeepayOrder::model()->with('ChildProfile')->findByPk($id);

                if($model){
                    if($model->status == 0){
                        $success = true;
                        $items = YeepayOrderItem::model()->with('invoice')->findAllByAttributes(array('it_orderId'=>$model->orderId));
                        if($items){
                            if (current($items)->invoice->childid == 0 && current($items)->invoice->admission_id) {
                                Yii::import('common.models.visit.AdmissionsDs');
                                $admission = AdmissionsDs::model()->findByPk(current($items)->invoice->admission_id);
                                $childName = $admission->getName();
                            } else{
                                $childName = $model->ChildProfile->getChildName();
                            }
                            foreach($items as $item){
                                if($item->status == 1){
                                    $success = false;
                                    $msg = '订单已支付';
                                }
                                if($item->invoice->status != 10 && $item->invoice->status != 30){
                                    $success = false;
                                    $msg = '实际账单状态已变更';
                                }
                            }
                        }
                        if($success){
                            echo CJSON::encode(array(
                                'state' => 'success',
                                'amount' => $model->payable_amount,
                                'childName' => $childName
                            ));
                            Yii::app()->end();
                        }
                    }
                    else{
                        $msg = '订单已支付';
                    }
                }
                echo CJSON::encode(array(
                    'state' => 'fail',
                    'msg' => $msg
                ));
            }
        }
    }

    /**
     * 通联POS订单支付
     * @return [type] [description]
     */
    public function actionPay()
    {
        if(Yii::app()->request->isPostRequest){
            $id         = Yii::app()->request->getParam('id', '');
            $amount     = Yii::app()->request->getParam('amount', '');
            $t          = Yii::app()->request->getParam('t', '');
            $v          = Yii::app()->request->getParam('v', '');
            $mac        = Yii::app()->request->getParam('mac', '');
            if($mac == md5(sprintf('%s|%s|%s|%s', $id, $amount, $t, $this->securityKey))){
                Yii::import('application.components.policy.PolicyApi');
                Yii::import('common.models.yeepay.*');
                Yii::import('common.models.invoice.*');

                $order = YeepayOrder::model()->with('items')->findByPk($id);
                if ($order){
                    if($order->status == 0){
                        $policyApi = new PolicyApi($order->schoolId);
                        $order->fact_amount = $amount;
                        $order->r2_TrxId = 'allinpay';
                        $order->r7_Uid = $v;
                        $order->rp_PayDate = $t;
                        $order->status = 1;
                        $order->updateTime = time();
                        $order->flag = 1;
                        if ($order->save()){
                            foreach ($order->items as $item){
                                $invoiceID[] = $item->invocieId;

                                $item->status = 1;
                                $item->updateTime = time();
                                $item->save();
                            }

                            $criteria = new CDbCriteria;
                            $criteria->compare('invoice_id', $invoiceID);
                            $iModels=Invoice::model()->findAll($criteria);

                            $errCode = $policyApi->pay($iModels, InvoiceTransaction::TYPE_POS_ALLINPAY, $amount, $order->operatorId);
                            if ( !$errCode ) {
                                echo 'success';
                                if (count($iModels)==1 && current($iModels)->payment_type=='registration' && current($iModels)->childid==0) {
                                    PolicyApi::admissionsToEmail(current($iModels)->admission_id);
                                } else{
                                    PolicyApi::posToEmail(array($order->orderId),'parent',true,OA::isProduction());
                                }
                            }
                            else{
                                $order->status = 0;
                                if ( $order->save() ){
                                    $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
                                    $mailer->AddAddress('<EMAIL>');
                                    $mailer->Subject = $errCode . ' 执行Pay失败 ID: '.$id;
                                    $mailer->Body = '执行Pay失败 ID: '.$id.'<br>ErrorCode: '.$errCode.'<br>Time: '.time();
                                    $mailer->iniMail( OA::isProduction() ); // 此行代码要放到AddAddress, AddCC方法下面
                                    $mailer->Send();
                                }
                                else{
                                    $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
                                    $mailer->AddAddress('<EMAIL>');
                                    $mailer->Subject = $errCode . ' 更新ivy_yp_invoice_transaction status失败 ID: '.$id;
                                    $mailer->Body = '更新ivy_yp_invoice_transaction status失败 ID: '.$id.'<br>ErrorCode: '.$errCode.'<br>Time: '.time();
                                    $mailer->iniMail( OA::isProduction() ); // 此行代码要放到AddAddress, AddCC方法下面
                                    $mailer->Send();
                                }
                            }
                        }
                        else{
                            $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
                            $mailer->AddAddress('<EMAIL>');
                            $mailer->Subject = '更新ivy_yp_invoice_transaction失败 ID: '.$id;
                            $mailer->Body = '更新ivy_yp_invoice_transaction失败 ID: '.$id;
                            $mailer->iniMail( OA::isProduction() ); // 此行代码要放到AddAddress, AddCC方法下面
                            $mailer->Send();
                        }
                    }
                    else{
                        echo 'success';
                    }
                }

            }
        }
    }

    /**
     * 通联在线订单支付（异步请求）
     * @return [type] [description]
     */
    public function actionOnlinePay()
    {
        if(Yii::app()->request->isPostRequest){
            Yii::import('common.models.alipay.*');
            Yii::import('common.models.invoice.*');

            $orderNo = Yii::app()->request->getPost('orderNo','');
            $alipayOrder = AlipayOrder::model()->findByPk($orderNo);
            if (isset($alipayOrder)) {
                $schoolid = $alipayOrder->schoolid;
                $allinpayPartnerInfo = CommonUtils::LoadConfig('CfgAllinpayPartnerInfo');
                if (!empty($allinpayPartnerInfo[$schoolid])) {
                    Yii::import('common.extensions.allinpay.*');

                    $allinpay = new Allinpay($schoolid);
                    //验证签名
                    if($allinpay->verifySignMsg($_POST))
                    {   
                        if($allinpay->payResult == 1)
                        {
                            //处理订单相关表状态
                            if ($alipayOrder->status == 0) {
                                //更新订单表
                                $alipayOrder->fact_amount = $allinpay->getRealMoney();
                                $alipayOrder->status = 1;
                                $alipayOrder->update_timestamp = time();
                                if($alipayOrder->save()){
                                    $payinfo = '订单支付成功！此次支付金额为：'.$allinpay->getRealMoney();
                                    //更新账单状态
                                    $criteria=new CDbCriteria;
                                    $criteria->compare('order_id',$allinpay->orderNo);
                                    $alipayOrderDetails = AlipayOrderDetail::model()->findAll($criteria);

                                    $payType = InvoiceTransaction::TYPE_ONLINE_ALLINPAY;
                                    Yii::import('common.components.policy.*');
                                    $policyApi = new PolicyApi($schoolid);
                                    $amount = round($allinpay->getRealMoney(),2);

                                    $invoices = array();
                                    foreach ($alipayOrderDetails as $alipayOrderDetail) {
                                        $invoices[] = $alipayOrderDetail->invoice;
                                    }
                                    $totalDueAmount = round(Invoice::model()->getTotalDueAmount($invoices),2);
                                    //如果与该订单号相关的账单记录只有一条，且$amount不大于$totalDueAmount,则此次支付为分割账单
                                    $rs = 1;
                                    if (count($invoices) == 1 && $totalDueAmount >= $amount) {
                                        $rs = $policyApi->Pay($invoices,$payType,$amount);
                                    }else{
                                        //如果与该订单相关的多个账单总金额等于订单返回金额
                                        if ($totalDueAmount == $amount) {
                                            $rs = $policyApi->Pay($invoices,$payType,$amount); 
                                        }
                                    }
                                    if (!$rs) {
                                        $payinfo .= ',账单状态已更改';
                                        //发送邮件
                                        PolicyApi::wxpayToEmail(array($allinpay->orderNo),'parent',true,0,true);
                                        //更新订单详情表
                                        AlipayOrderDetail::model()->updateAll(array('status'=>1,'update_timestamp'=>time()),'order_id = :order_id',array('order_id'=>$allinpay->orderNo));
                                    }else{
                                        $payinfo .= ',账单状态更新失败';
                                    }
                                }else{
                                    $payinfo = '订单更新失败';
                                }
                            }else{
                                $payinfo = '订单已处理';
                            }
                        }else{
                            $payinfo = '订单支付失败！';
                        } 
                    }else{
                        $payinfo = '报文验签失败!';
                    }
                }else{
                    $payinfo = '学校参数出错!';
                }
            }else{
                $payinfo = '订单号出错!';
            }
            echo $payinfo;
        }
    }
}