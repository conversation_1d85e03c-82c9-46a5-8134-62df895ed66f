<style>
	[v-cloak]{display: none;}
</style>
<div class="container-fluid">
	<ol class="breadcrumb">
		<li><?php echo CHtml::link(Yii::t('site', 'HQ Operations'), array('default/index')); ?></li>
		<li><?php echo CHtml::link(Yii::t('site', 'Support'), array('default/index')); ?></li>
		<li class="active"><?php echo Yii::t('site', '服务报销'); ?></li>
	</ol>
	<div class="row" id='service'  v-cloak>
		<div class="col-md-12">
			<!-- <form action="<?php echo $this->createUrl('print'); ?>" method="post"> -->
			<table class="table table-bordered">
				<thead>
					<tr>
						<!-- <th width="3%" class="nosort">选中打印</th> -->
						<th width="10%" class="nosort">服务标题</th>
						<th width="10%">服务类型</th>
						<th width="10%">发起学校</th>
						<th width="5%">开始时间</th>
						<th width="5%">结束时间</th>
						<th width="5%">去程费用</th>
						<th width="5%">返程费用</th>
						<th width="5%">住宿费用</th>
						<th width="5%" class="nosort">操作</th>
					</tr>
				</thead>
				<tbody>
					<template  v-for="(item, i) in serviceData">
						<tr :class="{success: item.status == 2}">
							<!-- <td>
									<input type="checkbox" class="print_input" name="print_ids[]" :value=item.id>
								</td> -->
							<td>{{item.service_title}}</td>
							<td>{{config[item.service_id].title}}</td>
							<td>{{item.school_title}}</td>
							<td>{{item.start_time}}</td>
							<td>{{item.end_time}}</td>
							<td>{{item.serviceExpense[1]}}</td>
							<td>{{item.serviceExpense[2]}}</td>
							<td>{{item.serviceExpense[3]}}</td>
							<td>
								<button @click="show(i)" type="button" class="btn btn-primary btn-xs">查看</button>
							</td>
						</tr>
					</template>
				</tbody>
				<button @click="print" class="btn btn-primary pull-right">打印</button>
			</table>
			<!-- </form> -->
			<nav aria-label="Page navigation">
				<ul class="pagination">
					<?php
					$prevClass = 'disabled';
					$prevHref = '#';
					if ($page - 1 > 0) {
						$prevClass = '';
						$prevHref = $this->createUrl('index', array('page' => $page - 1));
					}
					echo '<li class="' . $prevClass . '"><a href="' . $prevHref . '" aria-label="Previous"> <span aria-hidden="true">&laquo;</span></a> </li>'
					?>
					<?php
					for ($i = 1; $i <= $totalPages; $i++) {
						$class = ($i == $page) ? 'active' : '';
						$href = $this->createUrl('index', array('page' => $i));
						echo '<li class="' . $class . '"><a href="' . $href . '">' . $i . '</a></li>';
					}
					?>
					<?php
					$prevClass = 'disabled';
					$prevHref = '#';
					if ($totalPages - $page > 0) {
						$prevClass = '';
						$prevHref = $this->createUrl('index', array('page' => $page + 1));
					}
					echo '<li class="' . $prevClass . '"><a href="' . $prevHref . '" aria-label="Next"> <span aria-hidden="true">&raquo;</span></a> </li>'
					?>
				</ul>
			</nav>
		</div>
		<!-- 显示模态框 -->
		<div class="modal fade" id="showmodal" tabindex="-1" role="dialog" aria-labelledby="modal">
			<div v-if="dataId !=0" class="modal-dialog" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" v-if="dataId != 0">{{config[serviceId].title}}</h4>
					</div>
					<div class="modal-body">
						<form v-if="dataId != 0" id="expenseForm" class="form-horizontal" action="<?php echo $this->createUrl('new'); ?>">
							<div class="form-group">
								<label class="col-xs-3 control-label">
									<label for="service_title">服务标题 *</label>
								</label>
								<div class="col-xs-8">
									<input maxlength="255" class="form-control" type="text" :value="serviceData[dataId].service_title" disabled>
								</div>
							</div>
							<div v-for="(field, i) in config[serviceId].requestFields" class="form-group">
								<label class="col-xs-3 control-label">
									<label for="">{{field.title}}{{field.required ? ' *' : ''}}</label>
								</label>
								<div class="col-xs-8">
									<template v-if="field.type == 'text' ">
										<textarea class="form-control" rows="3" :value=serviceData[dataId].serviceField[field.id] disabled></textarea>
									</template>
									<template v-else-if="field.type == 'timestamp' ">
										<input maxlength="255" class="form-control datepicker" type="text" :value=serviceData[dataId].serviceField[field.id] disabled>
									</template>
									<template v-else>
										<input maxlength="255" class="form-control" type="text" :value=serviceData[dataId].serviceField[field.id] disabled>
									</template>
								</div>
							</div>
							<hr>
							<div v-if="JSON.stringify(serviceData[dataId].serviceExpense) !== '{}'" v-for="(field, i) in config[serviceId].expenseFields" class="form-group">
								<label class="col-xs-3 control-label">
									<label for="">{{field.title}}{{field.required ? ' *' : ''}}</label>
								</label>
								<div v-if="serviceData[dataId].serviceExpense" class="col-xs-8">
									<template v-if="field.type == 'text' ">
										<textarea class="form-control" rows="3" :name="'service_data[' +field.id+ ']'" :id=field.id :value=serviceData[dataId].serviceExpense[field.id] :disabled="serviceData[dataId].status == 2"></textarea>
									</template>
									<template v-else-if="field.type == 'timestamp' ">
										<input maxlength="255" class="form-control datepicker" type="text" :name="'service_data[' +field.id+ ']'" :id=field.id :value=serviceData[dataId].serviceExpense[field.id] :disabled="serviceData[dataId].status == 2">
									</template>
									<template v-else-if="field.type == 'float' ">
										<input maxlength="255" class="form-control datepicker" type="number" min="0.00" max="100000.00" step="0.01" :name="'service_data[' +field.id+ ']'" :id=field.id :value=serviceData[dataId].serviceExpense[field.id] :disabled="serviceData[dataId].status == 2">
									</template>
									<template v-else>
										<input maxlength="255" class="form-control" type="text" :value=serviceData[dataId].serviceExpense[field.id] :disabled="serviceData[dataId].status == 2">
									</template>
								</div>
							</div>
							<input type="hidden" name="id" :value=dataId>
						</form>
					</div>
					<div class="modal-footer">
						<button v-if="serviceData[dataId].status != 2" @click="save" type="button" class="btn btn-primary" id='saveBtn'>保存</button>
						<button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
					</div>
				</div>
			</div>
		</div>

		<!-- 选择打印的学校与月份 -->
		<div class="modal fade" id="printmodal" tabindex="-1" role="dialog" aria-labelledby="modal">
			<div class="modal-dialog" role="document">
				<form class=" form-horizontal" action="<?php echo $this->createUrl('printExpenses'); ?>" method="post">
					<div class="modal-content">
						<div class="modal-header">
							<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
							<h4 class="modal-title">选择打印的学校与月份</h4>
						</div>
						<div class="modal-body">
							<div class="form-group">
								<label class="col-xs-3 control-label">
									<label for="service_title">选择学校</label>
								</label>
								<div class="col-xs-6">
									<select name="schoolid" id="schoolid" class="form-control">
										<option v-for="(schoolname, schoolid) in config[1].schoollist" :value="schoolid">{{schoolname}}</option>
									</select>
								</div>
							</div>
							<div class="form-group">
								<label class="col-xs-3 control-label">
									<label for="service_title">选择月份</label>
								</label>
								<div class="col-xs-6">
									<select name="month" id="month" class="form-control">
										<?php
										for ($i = 0; $i < 12; $i++) {
											$date = date('Ym', strtotime("-$i month"));
											if ($date >= 201907) {
												echo '<option value="' . $date . '">' . $date . '</option>';
											}
										}
										?>
									</select>
								</div>
							</div>
						</div>
						<div class="modal-footer">
							<button type="submit" class="btn btn-primary">打印</button>
							<button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
						</div>
				</form>
			</div>
		</div>
	</div>
</div>
</div>
<script>
	var config = <?php echo json_encode($config); ?>;
	var data = <?php echo json_encode($data); ?>;
	var service = new Vue({
		el: "#service",
		data: {
			config: config,
			serviceId: 0,
			isManager: false,
			serviceData: data,
			dataId: 0,
		},
		created: function() {},
		updated: function() {},
		computed: {},
		methods: {
			show(id) {
				this.dataId = id;
				this.serviceId = this.serviceData[id].service_id;
				$('#showmodal').modal('show');
			},
			save: function(e) {
				$('#saveBtn').attr('disabled', 'disabled');
				let form = $('#expenseForm');
				$.ajax({
					url: '<?php echo $this->createUrl("new") ?>',
					type: "post",
					dataType: 'JSON',
					data: form.serialize(),
					success: function(res) {
						if (res.state == 'success') {
							$('#showmodal').modal('hide');
							service.$set(service.serviceData[service.dataId], 'status',2);
							resultTip({
								error: 'success',
								msg: 'success'
							});
						} else {
							let msg = res.message
							resultTip({
								error: 'error',
								msg: msg
							});
						}
					},
					error: function(res) {
						$('#saveBtn').removeAttr('disabled');
						resultTip({
							error: 'error',
							msg: 'error'
						});
					},
				}).done(function() {
					$('#saveBtn').removeAttr('disabled');
				});
			},
			print: function(e) {
				$('#printmodal').modal('show');
			}
		},
	});
</script>
<script>
	$(document).ready(function() {
		var table = $('.table').DataTable({
			aaSorting: [3, 'desc'], // 默认排序
			paging: false,
			info: false,
			searching: false,
			columnDefs: [{
				"targets": 'nosort',
				"orderable": false
			}],
		});
	});
</script>