<?php

/**
 * This is the model class for table "ivy_information_frame".
 *
 * The followings are the available columns in table 'ivy_information_frame':
 * @property integer $id
 * @property string $schoolid
 * @property integer $pid
 * @property string $title_cn
 * @property string $title_en
 * @property integer $updated
 * @property integer $updated_by
 */
class InformationFrame extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_information_frame';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('schoolid, pid, title_cn, title_en, updated, updated_by', 'required'),
			array('pid, updated, updated_by', 'numerical', 'integerOnly'=>true),
			array('schoolid', 'length', 'max'=>30),
			array('title_cn, title_en', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, schoolid, pid, title_cn, title_en, updated, updated_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'schoolid' => 'Schoolid',
			'pid' => 'Pid',
			'title_cn' => 'Title Cn',
			'title_en' => 'Title En',
			'updated' => 'Updated',
			'updated_by' => 'Updated By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('pid',$this->pid);
		$criteria->compare('title_cn',$this->title_cn,true);
		$criteria->compare('title_en',$this->title_en,true);
		$criteria->compare('updated',$this->updated);
		$criteria->compare('updated_by',$this->updated_by);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return InformationFrame the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public function getName()
    {
        return (Yii::app()->language == 'zh_cn') ? $this->title_cn : $this->title_en  ;

    }
}
