<?php

/**
 * This is the model class for table "ivy_dev_data".
 *
 * The followings are the available columns in table 'ivy_dev_data':
 * @property integer $id
 * @property integer $childid
 * @property string $branchid
 * @property integer $classid
 * @property integer $yid
 * @property string $class_type
 * @property string $development
 * @property integer $display
 * @property integer $fall_display
 * @property integer $spring_display
 * @property integer $timestamp
 * @property integer $userid
 */
class DevData extends CActiveRecord
{
    public $url;
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return DevData the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_dev_data';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('childid, branchid, classid, yid, class_type, timestamp, userid', 'required'),
			array('childid, classid, yid, display, fall_display, spring_display, timestamp, userid', 'numerical', 'integerOnly'=>true),
			array('branchid', 'length', 'max'=>100),
			array('class_type', 'length', 'max'=>5),
			array('development', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, childid, branchid, classid, yid, class_type, development, display, fall_display, spring_display, timestamp, userid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'childid' => 'Childid',
			'branchid' => 'Branchid',
			'classid' => 'Classid',
			'yid' => 'Yid',
			'class_type' => 'Class Type',
			'development' => 'Development',
			'display' => 'Display',
			'fall_display' => 'Fall Display',
			'spring_display' => 'Spring Display',
			'timestamp' => 'Timestamp',
			'userid' => 'Userid',
			'url' => 'url',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('branchid',$this->branchid,true);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('class_type',$this->class_type,true);
		$criteria->compare('development',$this->development,true);
		$criteria->compare('display',$this->display);
		$criteria->compare('fall_display',$this->fall_display);
		$criteria->compare('spring_display',$this->spring_display);
		$criteria->compare('timestamp',$this->timestamp);
		$criteria->compare('userid',$this->userid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}