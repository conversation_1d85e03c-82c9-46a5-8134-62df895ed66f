<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yiic message' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE, this file must be saved in UTF-8 encoding.
 *
 * @version $Id: $
 */
return array (
  'Registration' => '注册费',
  'Entrance Fee' => '入学费',
  'Uniform' => '校服',
  'Course Fee' => '课程费',
  'Tuition' => '学费',
  'Meal' => '餐费',
  'School Bus' => '校车费',
  '1st Semester' => '第一学期',
  '2nd Semester' => '第二学期',
  'Other' => '其他',
  'Student' => '学生',
  'sum' => '汇总',
  'Tuition Deposit' => '预交学费',
);
