<style>
	[v-cloak] {
		display: none;
	}
	.nav-wizard > li>a:hover{
	   background:#d5d5d5
	}
	.nav-wizard > li{
	    text-align: center;
	}
	.nav-wizard a{
		color:#ADADAD;
		font-size:14px
	}
	.number{
		display: inline-block;
		width:17px;
		height:17px;
		border:1px solid #ADADAD;
		border-radius:50%;
		text-align: center;
		line-height:17px;
	}
	.nav-wizard > li:not(:first-child) > a:before{
	    border-top: 23px inset transparent;
		border-bottom: 18px inset transparent;
	}
	.nav-wizard > li:not(:last-child) > a:after{
	    border-top: 23px inset transparent;
   		border-bottom: 17px inset transparent;
	}
	.active .number{
		border:1px solid #fff;
	}
</style>
<div class="container-fluid">
	<ol class="breadcrumb">
	    <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
	    <li><?php echo CHtml::link(Yii::t('site', '新生注册'), array('//mcampus/registration/index')) ?></li>
		<li class="active"><?php echo Yii::t('site','注册配置') ?></li>
	</ol>
	<div class="row">
		<div class="col-md-2 col-sm-2">
			<ul class="nav nav-pills nav-stacked text-left background-gray" id="pageCategory">
                <?php foreach ($this->getSubMenu() as $val){ ?>
                    <li class="<?php echo ($this->getAction()->getId() == $val['url']) ? "active " : ""; ?>" ><a href="<?php echo $this->createUrlReg($val['url']) ?>"><?php echo $val['label'] ?></a></li>
                <?php } ?>
            </ul>
		</div>
		<div class="col-md-10 col-sm-10" >
			<div id='registration' v-cloak>
				<div class="row">
					<div class="col-md-2">
						<div class="btn-group mb15 wid"><button type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" class="btn btn-default dropdown-toggle  wid">{{startYearList[yid]}} <span class="caret"></span></button>
							<ul class="dropdown-menu wid">
								<li v-for='(data,index) in YearList'>
									<a :href="'<?php echo $this->createUrl('/mcampus/registration/registconfig', array('branchId' => $this->branchId));?>&yid='+data.id+''">{{data.title}} </a>
								</li>
							</ul>
						</div>
					</div>
				</div>
				<div class="listul">
					<ul class="nav nav-wizard mb15">
						<template  v-for='(list,index) in steps'>
							<li :class="{active:list==current}" >
								<a :href="'<?php echo $this->createUrlReg('/mcampus/registration/registconfig', array('branchId' => $this->branchId)); ?>&step_id='+list+''" >{{items[list]}}</a>
							</li>
						</template>
					</ul>
				</div>
			</div>
            <?php

            if ($stepId == 1){
                $this->renderPartial("configBus", array('model' => $model, 'stepId' => $stepId));
            }else if ($stepId == 2){
                $this->renderPartial("configCard", array('model' => $model, 'stepId' => $stepId));
            }else if ($stepId == 3){
                $this->renderPartial("configUniform", array('model' => $model, 'stepId' => $stepId));
            }else if ($stepId == 4){
                $this->renderPartial("configMedical", array('model' => $model, 'stepId' => $stepId));
            }else if ($stepId == 5){
                $this->renderPartial("configLunch", array('model' => $model, 'stepId' => $stepId));
            }else if ($stepId == 6){
                $this->renderPartial("configLang", array('model' => $model, 'stepId' => $stepId));
            }else if ($stepId == 7){
                $this->renderPartial("configFee", array('model' => $model, 'stepId' => $stepId));
            }else if ($stepId == 8){
                $this->renderPartial("configSafe", array('model' => $model, 'stepId' => $stepId));
            }else if ($stepId == 9){
                $this->renderPartial("configStudent", array('model' => $model, 'stepId' => $stepId));
            }else if ($stepId == 10){
                $this->renderPartial("configParents", array('model' => $model, 'stepId' => $stepId));
            }else if ($stepId == 11){
                $this->renderPartial("configMaterial", array('model' => $model, 'stepId' => $stepId));
            }else{
                $this->renderPartial("welcome", array('model' => $model, 'stepId' => $stepId));
            }
            ?>
            <p>
		</div>
	</div>
</div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
	var config = <?php echo json_encode($config); ?>;
	var steps = <?php echo json_encode($steps); ?>;
	var startYearList = <?php echo json_encode($yearList) ?>;//年月
	var yid = '<?php echo $yid ?>';
	var stepId = '<?php echo $stepId ?>';
	function sortKey(array, key) {
        return array.sort(function(a, b) {
            var x = parseInt(a[key])
            var y = parseInt(b[key]);
            return((x > y) ? -1 : (x < y) ? 1 : 0)
        })
    }
	var  registration = new Vue({
		el: "#registration",
		data: {
			items: config,
			steps:steps,
		    startYearList:startYearList,
		    yid:yid,
		    YearList:'',
		    current:stepId
		},
		created: function() {
			var newcalendarArr = []
            for(key in startYearList) {
                var calendarArrkey = {}
                calendarArrkey.id = parseInt(key)
                calendarArrkey.title = startYearList[key]
                newcalendarArr.push(calendarArrkey)
            }
            this.YearList = sortKey(newcalendarArr, 'id')
		},
		computed: {},
		methods: {
			// tabList(index,list){
			// 	console.log(list)
			// 	this.current=index
			// },
		},
	})
	function keep(){
				alert(2)
			}
    function cbSuccess() {
        location.reload();
    }
</script>
