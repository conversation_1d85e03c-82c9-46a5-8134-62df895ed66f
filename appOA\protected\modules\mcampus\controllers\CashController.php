<?php

class CashController extends BranchBasedController{

    public $dcategory = 'finance';
    public $dsubcategory = 'handover';

    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mcampus/cash/handover');
    }

    public function actionHandover(){
        Yii::import('common.models.invoice.InvoiceTransaction');
        Yii::import('common.models.invoice.ChildCredit');
        Yii::import('common.models.invoice.CashHandoverHistory');
        Yii::import('common.models.invoice.CashHandoverLink');
        $category = Yii::app()->request->getParam('category','current');
        $cs = Yii::app()->clientScript;
		$cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
		$cs->registerCoreScript('jquery.ui');
		$cs->registerScriptFile($cs->getCoreScriptUrl() . '/jui/js/jquery-ui-i18n.min.js');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $this->render('handover',array('category'=>$category));
    }

    //保存交接截止日
    public function actionSaveHandoverDate(){
        $data = Yii::app()->request->getPost('data', 0);
        if (Yii::app()->request->isAjaxRequest && $data){
            $model = BranchVar::model()->find('branchid=:branchid and category=:category and subcategory=:subcategory',array(':branchid'=>$this->branchId,':category'=>$this->dcategory,':subcategory'=>$this->dsubcategory));
            if (empty($model)){
                $model = new BranchVar;
                $model->branchid = $this->branchId;
                $model->category = $this->dcategory;
                $model->subcategory = $this->dsubcategory;
                $model->flag = 1;
            }
            $model->data = strtotime($data);
            $model->user = Yii::app()->user->getId();
            $model->updated = time();
            if ($model->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('refresh',true);
            }else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('classroom', '提交的信息有错误.'));
            }
        }else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '请填写必填项');
        }
        $this->showMessage();
    }

    //获取交接明细数据
    public function actionGetHistoryData(){
        Yii::import('common.models.invoice.InvoiceTransaction');
        Yii::import('common.models.invoice.CashHandoverHistory');
        Yii::import('common.models.invoice.CashHandoverLink');
        $id = Yii::app()->request->getPost('id',0);
        $ret = array();
        if (Yii::app()->request->isAjaxRequest && $id){
            $model = CashHandoverHistory::model()->with('handoverLink')->findByPk($id);
            if (!empty($model)){
                $ret = $model->getAttributes();
                $ret['handover_date'] = OA::formatDateTime($model->handover_date);
                $ret['amount'] = number_format($model->amount,2);
                foreach ($model->handoverLink as $val){
                    $ret['info'][$val->transaction_id] = $val->transaction->getAttributes();
                    $ret['info'][$val->transaction_id]['name'] = $val->transaction->childInfo->getChildName();
                    $ret['info'][$val->transaction_id]['timestampe'] = OA::formatDateTime($val->transaction->timestampe,'medium','short');
                    $ret['info'][$val->transaction_id]['date'] = ($val->transaction->startdate) ? OA::formatDateTime($val->transaction->startdate) : OA::formatDateTime($val->transaction->enddate);
                    $ret['info'][$val->transaction_id]['amount'] = number_format($val->transaction->amount,2);
                }
            }
        }
        echo CJSON::encode($ret);
    }
}

?>
