<?php

/* 
 * 学生升学/转校定时任务
 */
class TransferCommand extends CConsoleCommand
{
    public function actionIndex()
    {
        Yii::import('application.components.policy.PolicyApi');
        Yii::import('common.models.child.TransferSchoolHistory');
        $tranModel = TransferSchoolHistory::model()->findAll('rank=:rank and status=:status and transfer_time<=:transfer_time', array(':rank' => 1, ':status' => TransferSchoolHistory::TRANSFER_STATUS_TWO, ':transfer_time' => time()));
        $errorTxt = '';
        if (!empty($tranModel)) {
            foreach ($tranModel as $model) {
                if (PolicyApi::newTransferSchool($model, false, true)) {
                    $model->status = TransferSchoolHistory::TRANSFER_STATUS_THREE;
                    if ($model->save()) {
                        ChildProfileBasic::setChildSync($model->childid);
                        TransferSchoolHistory::sendEmail($model, true, true);
                        echo $model->id . "\r\n";
                    }
                } else {
                    echo $model->id . "\r\n";
                    $errorTxt .= (empty($errorTxt)) ? $model->id : ',' . $model->id;
                }
            }
        } else {
            echo "\r\n end.";
        }
        if (!empty($errorTxt)) {
            echo "执行失败的ID：" . $errorTxt;
        }
    }

    public function actionTuitionInvoice()
    {
        Yii::import('common.models.invoice.*');
        Yii::import('common.models.calendar.*');
        Yii::import('common.components.policy.*');
        Yii::import('common.components.RC');
        Yii::import('common.models.easyapply.EasyapplyTransfer');
        Yii::import('common.models.easyapply.EasyapplyInvoice');

        // 获取账单列表
        $data = array(
            "tradeList" => array(
                array(
                    "tradeId" => "EA-2021120800004",
                    "invoiceId" => "584",
                    "applyId" => "2021112700024",
                    "amount" => "2400000",
                    "channel" => "WEIXIN_MINI",
                    "pay_succ_time" => "20211208163605",
                )
            ),
            "invoiceList" => array(
                array(
                    "id" => "584",
                    "applyId" => "2021112700024",
                    "amount" => "2400000",
                    "term" => "tuitionBySemester",
                    "title" => "学期学费",
                )
            ),
        );


        $applyIdList = array();
        foreach ($data["invoiceList"] as $item) {
            $applyIdList[] = $item['applyId'];
            $invoiceIdList[] = $item['id'];
        }

        // 查找已转入的信息
        $crit = new CDbCriteria();
        $crit->compare('apply_id', $applyIdList);
        $crit->index = 'apply_id';
        $transModelList = EasyapplyTransfer::model()->findAll($crit);

        $discountIdList = array();
        foreach ($transModelList as $item) {
            $discountIdList['discount_id'] = $item->discount_id;
        }
        $discountModelList = array();
        if ($discountIdList) {
            $crit = new CDbCriteria();
            $crit->compare('id', array_keys($discountIdList));
            $crit->index = "id";
            $discountModelList = DiscountSchool::model()->findAll($crit);
        }

        $crit = new CDbCriteria();
        $crit->compare('invoiceid', $invoiceIdList);
        $crit->index = 'invoiceid';
        $invoiceList = EasyapplyInvoice::model()->findAll($crit);
        // 生成相关账单
        $oaInvoiceModels = array();
        foreach ($data["invoiceList"] as $item) {
            $applyId = $item['applyId'];
            $invoiceId = $item['id'];
            // 检查关联的申请ID是否已转入
            if (!isset($transModelList[$applyId])) {
                echo "$applyId 申请未转入。\r\n";
                continue;
            }

            $transModel = $transModelList[$applyId];
            // 检查账单是否建立
            if (isset($invoiceList[$invoiceId])) {
                $oaInvoiceModel = Invoice::model()->findByPk($invoiceList[$invoiceId]->invoiceid_oa);
                if ($oaInvoiceModel) {
                    $oaInvoiceModels[$invoiceId] = $oaInvoiceModel;
                }
            } else {
                $startyear = $transModel->apply_year;
                $childId = $transModel->child_id;
                $childObj = ChildProfileBasic::model()->findByPk($childId);
                $schoolId = $childObj->schoolid;
                $amount = $item['amount'] / 100;
                // $classObj = IvyClass::model()->findByPk($childObj->classid);
                $policy = new IvyPolicy("pay", $startyear, $schoolId);
                if (!$policy) {
                    echo "$schoolId , $startyear 收退费政策不存在\r\n";
                    continue;
                }
                // 获取收费配置月付金额
                // $feeType = IvyClass::getClassTypeForFee();
                // $monthAmount = $policy->configs['TUITION_CALCULATION_BASE'][$feeType[$classObj->classtype]]['BY_MONTH']['AMOUNT']['FULL5D'];
                // if ($schoolId == "BJ_QFF") {
                //     $monthAmount = $policy->configs['TUITION_CALCULATION_BASE']['BY_MONTH']['AMOUNT']['FULL5D'];
                //     if ($classObj->classtype == "k") {
                //         $monthAmount = $policy->configs['TUITION_CALCULATION_BASE']['BY_MONTH']['AMOUNT']['FULL5D2'];
                //     }
                // }
                // if ($monthAmount <= 0) {
                //     echo "未找到匹配月付金额\r\n";
                //     continue;
                // }

                $schoolyear = CalendarSchool::model()->with('cTemplate')->findByAttributes(array(
                    'startyear' => $startyear,
                    'branchid' => $schoolId,
                ));
                $semester = 0;
                if ($item['term'] == "tuitionBySemester") {
                    $fee_type = 1;
                    if (date("m", $childObj->est_enter_date) < 8) {
                        $semester = 2;
                    } else {
                        $semester = 1;
                    }
                } else {
                    $fee_type = 1;
                }
                // 匹配折扣
                $discount = $transModel->discount_id ? $transModel->discount_id : 0;
                $nodiscountAmount = $amount;
                if (isset($discountModelList[$discount])) {
                    $nodiscountAmount = $amount / ($discountModelList[$discount]->discount / 100);
                }

                $timepoints = explode(',', $schoolyear->cTemplate->timepoints);
                if ($semester == 1) {
                    $byType = 'PAYGROUP_SEMESTER';
                    $startDate = strtotime(date('Y', $timepoints[0]) . '-09-01');
                    $endDate = $timepoints[1];
                } elseif ($semester == 2) {
                    $byType = 'PAYGROUP_SEMESTER';
                    $startDate = $timepoints[2];
                    $endDate = $timepoints[3];
                } else {
                    $byType = 'PAYGROUP_ANNUAL';
                    $startDate = strtotime(date('Y', $timepoints[0]) . '-09-01');
                    $endDate = $timepoints[3];
                }

                // 生成 invoice
                $transaction = Yii::app()->subdb->beginTransaction();
                try {
                    $params = array(
                        'byType' => $byType,
                        'feeType' => 'FEETYPE_TUITION',
                        'startDate' => $startDate,
                        'endDate' => $endDate,
                        'feeProgram' => 'FULL5D',
                    );

                    $invoiceAttr = array(
                        'calendar_id' => $schoolyear->yid,
                        'amount' => $amount,
                        'nodiscount_amount' => $nodiscountAmount,
                        'original_amount' => $amount,
                        'schoolid' => $schoolId,
                        'childid' => $childId,
                        'classid' => $childObj->classid,
                        'payment_type' => 'tuition',
                        'inout' => Invoice::INVOICE_INOUT_IN,
                        'startdate' => $startDate,
                        'enddate' => $endDate,
                        'fee_type' => $fee_type,
                        'fee_program' => 'FULL5D',
                        'duetime' => time() + 7 * 86400,
                        'title' => $policy->genInvoiceTitle($params),
                        'discount_id' => $discount,
                        'userid' => 1,
                        'timestamp' => time(),
                        'status' => Invoice::STATS_UNPAID,
                        'child_service_info' => 'a:5:{s:3:"mon";i:20;s:3:"tue";i:20;s:3:"wed";i:20;s:3:"thu";i:20;s:3:"fri";i:20;}',
                    );
                    $model = new Invoice();
                    $model->setAttributes($invoiceAttr);
                    if (!$model->save()) {
                        $error = current($model->getErrors());
                        throw new Exception('保存 ivy_invoice_invoice 失败' . $error[0]);
                    }
                    $eaInvoice = new EasyapplyInvoice();
                    $eaInvoice->invoiceid = $invoiceId;
                    $eaInvoice->invoiceid_oa = $model->invoice_id;
                    $eaInvoice->trade_ids = json_encode(array());
                    $eaInvoice->apply_id = $applyId;
                    $eaInvoice->apply_year = $startyear;
                    $eaInvoice->child_id = $childId;
                    $eaInvoice->class_id = $model->classid;
                    $eaInvoice->updated_by = 1;
                    $eaInvoice->updated_at = time();
                    if (!$eaInvoice->save()) {
                        $error = current($eaInvoice->getErrors());
                        throw new Exception('保存 ivy_easyapply_invoice 失败' . $error[0]);
                    }
                    $transaction->commit();
                    $oaInvoiceModels[$invoiceId] = $model;
                    $invoiceList[$invoiceId] = $eaInvoice;
                    echo "账单创建成功\r\n";
                } catch (Exception $e) {
                    $transaction->rollBack();
                    echo "eaInvoice: $invoiceId, 创建账单失败：" .  $e->getMessage() . "\r\n";
                }
            }
        }

        // 生成相关订单
        foreach ($data["tradeList"] as $item) {
            $applyId = $item['applyId'];
            $eaInvoiceId = $item['invoiceId'];
            $tradeId = $item['tradeId'];
            // 检查关联的申请ID是否已转入
            if (!isset($transModelList[$applyId])) {
                echo "申请未转入，申请ID：$applyId" . "\r\n";
                continue;
            }
            // 检查关联账单是否创建
            if (!isset($oaInvoiceModels[$eaInvoiceId])) {
                echo "对应账单未生成，账单ID：$eaInvoiceId" . "\r\n";
                continue;
            }

            $oaInvoice = $oaInvoiceModels[$eaInvoiceId];
            $eaInvoice = $invoiceList[$eaInvoiceId];
            // 检查是否已生成微信订单
            $tradeIdList = json_decode($eaInvoice['trade_ids']) ? json_decode($eaInvoice['trade_ids']) : array();

            if (in_array($tradeId, $tradeIdList)) {
                echo "订单：{$tradeId} ，已处理\r\n";;
                continue;
            }
            // 生成微信订单
            Yii::import("common.models.wxpay.WechatPayOrder");
            Yii::import("common.models.wxpay.WechatPayOrderItem");

            try {
                $childid = $oaInvoice->childid;
                $schoolid = $oaInvoice->schoolid;
                $oaInvoice_id = $oaInvoice->invoice_id;
                $amount = $item['amount'] / 100;

                $payTime = strtotime($item['pay_succ_time']);
                $wxpayInfo = CommonUtils::LoadConfig('CfgWxPayGlobal');
                $number_code = $wxpayInfo[$schoolid]['number_code'];
                $wechatPayOrder = new WechatPayOrder();
                $wechatPayOrder->orderid = $wechatPayOrder->genOrderID($number_code . $oaInvoice_id);
                $wechatPayOrder->payable_amount = $amount;
                $wechatPayOrder->fact_amount = 0;
                $wechatPayOrder->schoolid = $schoolid;
                $wechatPayOrder->childid = $childid;
                $wechatPayOrder->type = 'NATIVE';
                $wechatPayOrder->status = 0;
                $wechatPayOrder->settlement_status = 0;
                $wechatPayOrder->order_time = $payTime;
                $wechatPayOrder->update_timestamp = $payTime;
                $wechatPayOrder->uid = 1;
                if (!$wechatPayOrder->save()) {
                    $error = current($wechatPayOrder->getErrors());
                    throw new Exception('保存 wechatPayOrder 失败' . $error[0]);
                }

                $wechatPayOrderItem = new WechatPayOrderItem();
                $wechatPayOrderItem->orderid = $wechatPayOrder->orderid;
                $wechatPayOrderItem->invoice_id = $oaInvoice_id;
                $wechatPayOrderItem->amount = $amount;
                $wechatPayOrderItem->save();
                if (!$wechatPayOrderItem->save()) {
                    $error = current($wechatPayOrderItem->getErrors());
                    throw new Exception('保存 wechatPayOrder 失败' . $error[0]);
                }
                // 付款
                $policyApi = new PolicyApi($schoolId);
                $payType = InvoiceTransaction::TYPE_WX_NATIVE;
                $payResult = $policyApi->Pay(array($oaInvoice), $payType, $amount, 1);
                // 判断账单支付结果
                if ($payResult != 0) {
                    throw new Exception('付款失败');
                }
                $wechatPayOrder->fact_amount = $amount;
                $wechatPayOrder->status = 1;
                $wechatPayOrder->save();

                $tradeIdList[] = $tradeId;
                $eaInvoice->trade_ids = json_encode($tradeIdList);
                if (!$eaInvoice->save()) {
                    $error = current($eaInvoice->getErrors());
                    throw new Exception('保存 EasyapplyInvoice 失败' . $error[0]);
                }

                echo "订单：{$tradeId} ，处理成功\r\n";
            } catch (Exception $e) {
                echo "tradeId: {$tradeId}, 创建订单失败：" .  $e->getMessage() . "\r\n";
            }
        }
    }

}
