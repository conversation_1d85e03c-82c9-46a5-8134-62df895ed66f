<?php $this->beginContent('//layouts/main'); ?>
    <style>
        .pt50 {
            padding-top: 50px;
        }
        .pt100 {
            padding-top: 100px;
        }
        .container {
            padding-bottom: 50px;
        }

        .modal-body {
            max-height: 420px;
            overflow-y: auto;
        }

        #toptitle {
            background-color: #2695D0;
            /*padding: 12px 10px 10px;*/
            padding: 5px 0;
            width: 100%;
            color: #ffffff;
            margin-bottom: 15px;
            margin-top: 45px;
        }

        .htitle {
            font-size: 20px;
            margin: 0;
            padding: 0;
            text-align: center;
        }

        .regfooter {
            height: 35px;
            line-height: 35px;
            margin: 10px 0 0;
            text-align: center;
            color: #fff;
            background-color: #4b4b4b;
        }

        .regfooter a {
            color: #fff;
        }

        .radio, .checkbox {
            padding-left: 20px;
        }

        .radio label, .checkbox label {
            padding-left: 0;
        }

        .waitStrip {
            text-indent: 9999px;
            display: -webkit-box;
            display: -webkit-flex;
            display: flex;
            position: fixed;
            z-index: 500;
            top: 0;
            width: 100%;
            height: 4px;
            background-color: #fcfcfc;
        }

        .waitStrip_done {
            height: 4px;
            background-color: #335AA8;
        }

        .done1 {
            width: 14%;
        }

        .done2 {
            width: 28%;
        }

        .done3 {
            width: 42%;
        }

        .done4 {
            width: 56%;
        }

        .done5 {
            width: 70%;
        }

        .done6 {
            width: 84%;
        }

        .done7 {
            width: 100%;
        }

        .error_content {
            animation: blink 3s;
            -webkit-animation: blink 3s;
            animation-fill-mode: forwards;
            -webkit-animation-fill-mode: forwards;
        }

        @-webkit-keyframes blink {
            0% {
                color: #ffff00;
            }
            100% {
                color: #FA7A17
            }
        }

        @keyframes blink {
            0% {
                color: #ffff00
            }
            100% {
                color: #FA7A17
            }
        }

        .step_icon {
            margin-bottom: 30px;
            text-align: center;
        }
        .step_icon > i {
            font-size: 93px;
            border-radius: 50%;
            display: inline-block;
            vertical-align: middle;
            text-rendering: auto;
            -webkit-font-smoothing: antialiased;
            color:#fff;
        }
        .step_success > i {
            color: #09BB07;
        }
        .step_warning > i {
            color: #FFBE00;
        }
        .radio-inline label {
            font-weight: normal;
        }

        .steps{
            display: -webkit-box;
            display: -webkit-flex;
            display: flex;
            position: fixed;
            z-index: 500;
            top: 0;
            width: 100%;
            height: 45px;
            line-height: 45px;
            background-color: #fcfcfc;
            border-bottom: #2695D0 1px solid;
        }
        .sui-steps{font-size:0px;overflow:hidden;line-height:0px;margin:18px 0}.sui-steps .wrap{display:inline-block}.sui-steps .wrap>div{width:195px;height:28px;display:inline-block;line-height:28px;vertical-align:top;font-size:12px;position:relative}.sui-steps .wrap>div>label{margin-left:24px;cursor:default;min-width:130px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.sui-steps .triangle-right{display:inline-block;width:0px;height:0px;border-style:solid;border-width:14px;position:absolute;right:-27px;z-index:1}.sui-steps .triangle-right-bg{display:inline-block;width:0px;height:0px;border-style:solid;border-width:14px;position:absolute;right:-27px;z-index:1;border-width:18px;right:-36px;border-color:transparent transparent transparent #FFF;top:-4px}.sui-steps .round{display:inline-block;width:16px;height:16px;-webkit-border-radius:8px;-moz-border-radius:8px;border-radius:8px;text-align:center;line-height:16px}.sui-steps .round .sui-icon{vertical-align:-1px}.sui-steps .round+span:before{content:'\00a0'}.sui-steps .finished{background-color:#53b8c0;color:#fff}.sui-steps .finished .triangle-right{border-color:transparent transparent transparent #53b8c0}.sui-steps .finished .round{background-color:#fff;background-color:transparent\9;color:#fff}.sui-steps .finished .round>i{color:#53b8c0;font-size:12px}.sui-steps .current{background-color:#339ba3;color:#fff}.sui-steps .current .triangle-right{border-color:transparent transparent transparent #339ba3}.sui-steps .current .round{background-color:#fff;color:#339ba3;color:#FFF\9;background-color:transparent\9}.sui-steps .todo{background-color:#eee;color:#999}.sui-steps .todo .triangle-right{border-color:transparent transparent transparent #eee}.sui-steps .todo .round{background-color:#fff;background-color:transparent\9}.steps-large .wrap>div{font-size:14px;width:243.75px;height:35px;line-height:35px}.steps-large .wrap>div>label{font-size:14px;margin-left:27.5px}.steps-large .triangle-right{border-width:17.5px;right:-34px}.steps-large .triangle-right-bg{border-width:21.5px;right:-43px}.steps-large .round{width:18px;height:18px;line-height:18px;-webkit-border-radius:9px;-moz-border-radius:9px;border-radius:9px}.steps-auto{display:table;width:100%}.steps-auto .wrap{display:table-cell}.steps-auto .wrap>div{width:100%}.sui-steps-round{font-size:0px;overflow:hidden;line-height:0px;margin:10px 0;padding:0 6px}.sui-steps-round>div{display:inline-block;vertical-align:top}.sui-steps-round>div .wrap{*zoom:1}.sui-steps-round>div .wrap:before,.sui-steps-round>div .wrap:after{display:table;content:"";line-height:0}.sui-steps-round>div .wrap:after{clear:both}.sui-steps-round>div>label{display:inline-block;width:80%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;font-size:12px;line-height:12px;height:12px;margin-left:-6px;margin-top:6px;color:#53b8c0;cursor:default}.sui-steps-round>div .round{width:24px;height:24px;-webkit-border-radius:15px;-moz-border-radius:15px;border-radius:15px;display:inline-block;vertical-align:middle;font-size:12px;color:#FFF;line-height:20px;text-align:center;float:left}.sui-steps-round>div .bar{margin:10px 10px 0px 40px;width:200px;height:6px;vertical-align:middle;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px}.sui-steps-round>div:last-child{width:5%}.sui-steps-round>div:last-child>label{width:auto}.sui-steps-round>.finished .round{border:3px #53b8c0 solid;background-color:#53b8c0}.sui-steps-round>.finished .bar{background-color:#53b8c0}.sui-steps-round>.current .round{border:3px #339ba3 solid;background-color:#339ba3}.sui-steps-round>.current .bar{background-color:#339ba3}.sui-steps-round>.todo>label{color:#666}.sui-steps-round>.todo .round{border:3px #bcbcbc solid;background-color:#FFF;color:#999}.sui-steps-round>.todo .bar{background-color:#d3d3d3}.steps-round-auto{display:table;width:100%}.steps-round-auto>div{display:table-cell}.steps-round-auto>div .bar{width:auto}.steps-3>div{width:50%}.steps-4>div{width:33%}.steps-5>div{width:25%}.steps-6>div{width:20%}.steps-7>div{width:16%}
    </style>
<?php echo $content; ?>
    <script>
        <?php if ($this->showModel == true) :?>
        $('#theModal').modal({
            backdrop: 'static',
            keyboard: false
        });
        <?php endif;?>
        <?php if ($this->isOpen == false) :?>
        $('body input').attr('disabled', 'disabled');
        $('body button').attr('disabled', 'disabled');
        <?php endif;?>        
        <?php if (isset($this->studentSteps[$this->step]) && $this->studentSteps[$this->step] == 1) :?>
        $('body input').attr('disabled', 'disabled');
        $('body button').attr('disabled', 'disabled');
        $('body .examine').html('<?php echo Yii::t("reg", 'No content can be submitted while registration is closed'); ?>');
        <?php endif;?>
    </script>
<?php $this->endContent(); ?>