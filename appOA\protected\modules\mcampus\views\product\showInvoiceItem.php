<?php $config = Products::config(); ?>
<div class="container-fluid">
    <div style="padding: 1em 0;">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
    </button>
    </div>
    <div class="clearfix"></div>
    <div class="form-group">
        <div class="col-xs-12">
            <table class="table table-hover">
                <thead>
                <tr>
                    <th><?php echo Yii::t('product','商品') ?></th>
                    <?php foreach ($config[$productsModel->type]['item'] as $key => $types) { ?>
                        <th><?php echo Yii::t('products', $types['title']); ?></th>
                    <?php } ?>
                    <th><?php echo Yii::t('product','价格') ?></th>
                    <th><?php echo Yii::t('product','数量') ?></th>
                </tr>
                </thead>
                <tbody>
                <?php
                if ($invoiceItemModel):
                    $config = $config[$productsModel->type]['item'];
                    foreach ($invoiceItemModel as $item):
                        $attr = $item->attr;
                        ?>
                        <tr>
                            <td class="col-md-2"><?php echo $item->product->getTitle() ?></td>
                            <?php
                            $i = 1;
                            foreach ($config as $types) {
                                $_attr = "attr" . $i; ?>
                                <th><?php echo Yii::t('products', $types['option'][$attr->$_attr]); ?></th>
                                <?php $i++;
                            } ?>

                            <td class="col-md-2"><?php echo $item->unit_price ?></td>
                            <td class="col-md-2"><?php echo $item->num ?></td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

