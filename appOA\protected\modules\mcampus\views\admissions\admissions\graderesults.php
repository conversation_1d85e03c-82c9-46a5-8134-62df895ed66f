<?php
$form = $this->beginWidget('CActiveForm', array(
    'id' => 'child_interivew',
    'enableAjaxValidation' => false,
    'action' => $this->createUrl('interviewResults', array('childid' => $child->id)),
    'htmlOptions' => array('class' => 'J_ajaxForm', 'role' => 'form'),
));
Yii::import('common.models.visit.*');
$gender = array(1=>'Boy', 2=> 'Girl');
$grade = AdmissionsDs::getConfig();

$countries = Country::model()->getCountryList('','','en');
$Diglossia = Diglossia::model()->getLanguage('true');

$data = array();
if($childinterview->discipline_stat){
    $data = json_decode($childinterview->discipline_stat,true);
    $child->interviewDate = date("Y-m-d", $data['interviewDate']);
    $child->interviewDate_t = date("Y-m-d", $data['interviewDate_t']);
}

$schoolHistoryArr = array();
if($child->school_history){
    $school_history = json_decode($child->school_history,true);
    foreach ($school_history as $val){
        $schoolHistoryArr[] = $val['school'];
    }
    $schoolHistoryArr = implode(',',$schoolHistoryArr);
}

?>
<div class="modal-body">
    <table class="table table-bordered">
        <h3 class="text-center"><?php echo (isset($calender) && isset($calender[$child->start_year])) ? $calender[$child->start_year] : '' ; ?> Admissions Assessment Check List (Grade 2+)</h3><br/>
    </table>
    <div class="table-responsive">
        <h3>Student Information</h3>
        <table class="table table-bordered">
            <tr>
                <td>English Name: <?php echo $child->en_name_last . ' ' .$child->en_name_middle . ' ' .$child->en_name?></td>
                <td>Chinese Name: <?php echo $child->cn_name . ' ' .$child->cn_name_last ?></td>
            </tr>
            <tr>
                <td>Nationality: <?php echo $countries[$child->nationality] ?></td>
                <td>Mother Language: <?php echo $Diglossia[$child->native_lang] ?></td>
            </tr>
            <tr>
                <td>Gender: <?php echo $gender[$child->gender] ?></td>
                <td>DOB: <?php echo date("Y-m-d", $child->birthday) ?></td>
            </tr>
            <tr>
                <td>Starting Year & Grade: <?php echo $grade['grade_en'][$child->start_grade] ?></td>
                <td>Previous School: <?php echo ($schoolHistoryArr) ? $schoolHistoryArr : '' ?></td>
            </tr>
            <tr>
                <td>Father:
                    <?php echo ($child->father_name) ? $child->father_name . '，' : '' ; ?>
                    <?php echo ($child->father_education) ? $grade['education'][$child->father_education]  . '，' : '' ; ?>
                    <?php echo ($child->father_position) ? $child->father_position  . '，' : '' ; ?>
                    <?php echo ($child->father_employer) ? $child->father_employer : '' ; ?>，
                </td>
                <td>
                    Mother:
                    <?php echo ($child->mother_name) ? $child->mother_name  . '，' : '' ?>
                    <?php echo ($child->mother_education) ? $grade['education'][$child->mother_education]  . '，' : '' ?>
                    <?php echo ($child->mother_position) ? $child->mother_position  . '，' : '' ?>
                    <?php echo ($child->mother_employer) ? $child->mother_employer : '' ?>
                </td>
            </tr>
            <tr>
                <td>
                <div class="form-inline">
                  <span>Siblings at Daystar: </span>
                    <?php echo CHTml::dropDownList('interview[siblingsDaystar]', ($data) ? $data['siblingsDaystar'] : 2, array( 1 => 'Yes', 2 => 'No' ), array('class'=>'form-control')) ?>
                    </div>
                </td>
                <td>Staff Child: <?php echo ($child->is_staff == 1) ? "Yes" : "No" ?></td>
            </tr>
        </table>
    </div>
    <div class="table-responsive">
        <h3><?php echo Yii::t('user','Other Materials') ?></h3>
        <table class="table table-bordered">
            <tr>
                <td width="280">学籍Xueji: </td>
                <td><?php echo CHtml::textField("interview[xueji]", ($data) ? $data['xueji'] : "", array('class' => "form-control")) ?></td>
                <td width="200"><?php echo CHtml::textField("interview[xueji2]", ($data) ? $data['xueji2'] : "", array('class' => "form-control")) ?></td>
            </tr>
            <tr>
                <td width="280">School Report: </td>
                <td><?php echo CHtml::textField("interview[schoolReport]", ($data) ? $data['schoolReport'] : "", array('class' => "form-control")) ?></td>
                <td><?php echo CHtml::textField("interview[schoolReport2]", ($data) ? $data['schoolReport2'] : "", array('class' => "form-control")) ?></td>
            </tr>
            <tr>
                <td width="280">Recommendation Letter: </td>
                <td><?php echo CHtml::textField("interview[letter]", ($data) ? $data['letter'] : "", array('class' => "form-control")) ?></td>
                <td><?php echo CHtml::textField("interview[letter2]", ($data) ? $data['letter'] : "", array('class' => "form-control")) ?></td>
            </tr>
        </table>
    </div>
    <div class="table-responsive">
        <div>
            <h3>Written Test Information</h3>
            <div class="pull-right form-inline mb15">
            <span>Date: </span>
                <?php echo ($childinterview) ? date("Y-m-d", $childinterview->interview_time) : "未分配面试时间" ?>
            </div>
        </div>
        <table class="table table-bordered">
            <tr>
                <td>Subject & Score:</td>
                <td>Benchmark:</td>
                <td>Teacher:</td>
                <td>Subject & Score:</td>
                <td>Teacher:</td>
            </tr>
            <tr>
                <td>
                 <div class="form-group">
                    <span class="col-sm-3" style="padding:0">Map Reading:</span>
                    <div class="col-sm-9">
               <?php echo CHtml::textField("interview[mapReadingFraction]", ($data) ? $data['mapReadingFraction'] : "", array('class' => "form-control col-sm-1")) ?>
                </div>
               </div>
               </td>
                <td><?php echo CHtml::textField("interview[mapReading]", ($data) ? $data['mapReading'] : "", array('class' => "form-control")) ?></td>
                <td><?php echo CHtml::textField("interview[mapReadingTeacher]", ($data) ? $data['mapReadingTeacher'] : "", array('class' => "form-control")) ?></td>
                <td>
                  <div class="form-group">
                    <span class="col-sm-3"  style="padding:0">English writing:</span>
                    <div class="col-sm-9">
                
                    <?php echo CHtml::textField("interview[englishWritingFraction]", ($data) ? $data['englishWritingFraction'] : "", array('class' => "form-control")) ?>
                      </div>
               </div>
                </td>
                <td><?php echo CHtml::textField("interview[englishWriting]", ($data) ? $data['englishWriting'] : "", array('class' => "form-control")) ?></td>
            </tr>
            <tr>
                <td>
                <div class="form-group">
                 <span class="col-sm-3" style="padding:0"> Map Language:</span>
                    <div class="col-sm-9">
                    <?php echo CHtml::textField("interview[mapLanguageFraction]", ($data) ? $data['mapLanguageFraction'] : "", array('class' => "form-control")) ?>
                     </div>
                 </div>
                </td>
                <td><?php echo CHtml::textField("interview[mapLanguage]", ($data) ? $data['mapLanguage'] : "", array('class' => "form-control")) ?></td>
                <td><?php echo CHtml::textField("interview[mapLanguageTeacher]", ($data) ? $data['mapLanguageTeacher'] : "", array('class' => "form-control")) ?></td>
                <td>
                  <div class="form-group">
                 <span class="col-sm-3"  style="padding:0">Chinese Literature:</span>
                    <div class="col-sm-9">
               
                    <?php echo CHtml::textField("interview[chineseLiteratureFraction]", ($data) ? $data['chineseLiteratureFraction'] : "", array('class' => "form-control")) ?>
                    </div>
                 </div>
                </td>
                <td><?php echo CHtml::textField("interview[chineseLiterature]", ($data) ? $data['chineseLiterature'] : "", array('class' => "form-control")) ?></td>
            </tr>
            <tr>
                <td>
                  <div class="form-group">
                 <span class="col-sm-3"  style="padding:0">Map Math:</span>
                    <div class="col-sm-9">
                
                    <?php echo CHtml::textField("interview[mapMathFraction]", ($data) ? $data['mapMathFraction'] : "", array('class' => "form-control")) ?>
                    </div>
                 </div>
                </td>
                <td><?php echo CHtml::textField("interview[mapMath]", ($data) ? $data['mapMath'] : "", array('class' => "form-control")) ?></td>
                <td><?php echo CHtml::textField("interview[mapMathTeacher]", ($data) ? $data['mapMathTeacher'] : "", array('class' => "form-control")) ?></td>
                <td>
                   <div class="form-group">
                 <span class="col-sm-3"  style="padding:0">Chinese Math:</span>
                    <div class="col-sm-9">
                
                    <?php echo CHtml::textField("interview[chineseMathFraction]", ($data) ? $data['chineseMathFraction'] : "", array('class' => "form-control")) ?>
                     </div>
                 </div>
                </td>
                <td><?php echo CHtml::textField("interview[chineseMath]", ($data) ? $data['chineseMath'] : "", array('class' => "form-control")) ?></td>
            </tr>
            <tr>
                <td colspan="5">Learning Support：
                    <?php echo CHtml::textArea("interview[recommendationLetter]", ($data) ? $data['recommendationLetter'] : "", array('class' => "form-control")) ?>
                </td>
            </tr>
        </table>
    </div>

    <div class="table-responsive">
        <div>
            <h3>Interview Information</h3>
            <span class="pull-right" ><?php echo Yii::t('user','Date:') ?>
                <?php $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                    'model' => $child,
                    'attribute' => 'interviewDate',
                    'options' => array(
                        'dateFormat' => 'yy-mm-dd',
                    ),
                    'htmlOptions' => array(
                        'class' => 'form-control',
                        'placeholder' => Yii::t('management', '时间'),
                    ),
                )); ?>
            </span>
        </div>
        <table class="table table-bordered">
            <tr>
                <td></td>
                <td>Student Interview:</td>
                <td>Parents Interview:</td>
            </tr>
            <tr>
                <td>Interviewer:</td>
                <td><?php echo CHtml::textField("interview[interviewer1]", ($data) ? $data['interviewer1'] : "", array('class' => "form-control")) ?></td>
                <td><?php echo CHtml::textField("interview[interviewer2]", ($data) ? $data['interviewer2'] : "", array('class' => "form-control")) ?></td>
            </tr>
            <tr>
                <td>Interview Result:</td>
                <td>
                    <?php echo CHTml::radioButtonList('interview[studentInterview]', ($data) ? $data['studentInterview'] : '', array(1=>'A',2=>'B',3=>'C',4=>'D'), array('separator'=>'', 'template'=>'<div class="col-md-2">{input} {label}</div>')) ?>
                </td>
                <td>
                    <?php echo CHTml::radioButtonList('interview[parentsInterview]', ($data) ? $data['parentsInterview'] : '', array(1=>'A',2=>'B',3=>'C',4=>'D'), array('separator'=>'', 'template'=>'<div class="col-md-2">{input} {label}</div>')) ?>
                </td>
            </tr>
            <tr>
                <td colspan="3"><span class="pull-right">A: Outstanding   B: Above Average   C: Average   D: Below Average</span></td>
            </tr>
        </table>
    </div>

    <div class="table-responsive">
        <h3><?php echo Yii::t('user','Person in Charge') ?></h3>
        <table class="table table-bordered">
            <tr>
                <td width="280"><?php echo Yii::t('user', 'Department') ?>:</td>
                <td><?php echo Yii::t('user', 'Accept') ?>:</td>
                <td><?php echo Yii::t('user', 'Reject') ?>:</td>
                <td><?php echo Yii::t('user', 'Note') ?>:</td>
                <td><?php echo Yii::t('user', 'Signature &Date') ?>:</td>
            </tr>
            <tr>
                <td width="280"><?php echo Yii::t('user', 'Admissions Director') ?>:</td>
                <td><?php echo CHtml::textField("interview[admissionsDirector]", ($data) ? $data['admissionsDirector'] : "", array('class' => "form-control")) ?></td>
                <td><?php echo CHtml::textField("interview[admissionsDirector2]", ($data) ? $data['admissionsDirector2'] : "", array('class' => "form-control")) ?></td>
                <td><?php echo CHtml::textField("interview[admissionsDirector3]", ($data) ? $data['admissionsDirector3'] : "", array('class' => "form-control")) ?></td>
                <td><?php echo CHtml::textField("interview[admissionsDirector4]", ($data) ? $data['admissionsDirector4'] : "", array('class' => "form-control")) ?></td>
            </tr>
            <tr>
                <td width="280"><?php echo Yii::t('user', 'Principal') ?>:</td>
                <td><?php echo CHtml::textField("interview[principal]", ($data) ? $data['principal'] : "", array('class' => "form-control")) ?></td>
                <td><?php echo CHtml::textField("interview[principal2]", ($data) ? $data['principal2'] : "", array('class' => "form-control")) ?></td>
                <td><?php echo CHtml::textField("interview[principal3]", ($data) ? $data['principal3'] : "", array('class' => "form-control")) ?></td>
                <td><?php echo CHtml::textField("interview[principal4]", ($data) ? $data['principal4'] : "", array('class' => "form-control")) ?></td>
            </tr>
        </table>
    </div>
    <?php
    if (Yii::app()->user->checkAccess('o_A_Adm_Visits') && $child->status != AdmissionsDs::STATS__TRANSFER_CHILD && $status == 2): ?>
        <div class="form-group"><?php echo Yii::t('interview', 'Whether to pass the interview') ?>:
            <label for="exampleInputEmail1">
                <?php echo CHtml::radioButtonList('stat', $child->status, array('41' => Yii::t('user', 'No'), '40' => Yii::t('user', 'Yes'), '42' => Yii::t('user', '候补')), array('separator' => '&nbsp;&nbsp;')); ?>
        </div>
    <?php endif; ?>
</div>
<?php if($status == 2): ?>
    <div class="clearfix"> </div>
    <div class="modal-footer">
        <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit') ?></button>
        <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel') ?></button>
    </div>
<?php endif; ?>
<?php $this->endWidget(); ?>
