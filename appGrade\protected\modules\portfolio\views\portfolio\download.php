<?php
$this->breadcrumbs=array(
	'Portfolio'=>array('/portfolio/portfolio/classes'),
	Yii::t("navigations",'Download Photos'),
);?>

<div id="left-col" class="no-bg span-6">
    
</div>

<div id="main-col" class="span-18 last">
    <p class="note"><?php echo Yii::t('portfolio','The system generates the photo package overnight and will send you an email when it is ready for download.');?></p>
    <?php foreach ($tds as $year=>$tk):?>
        <h1><label><?php echo $year?> - <?php echo $year+1?> <?php echo Yii::t('portfolio', 'School Year')?></label></h1>
        <?php if($year == 2014):?>
            <div style="padding-left: 40px">
                <div class="flash-info">
                    <?php echo Yii::t('message', 'We are upgrading our systems and photos for the 2014-15 school year are not available for download temporarily. Our apologies for any inconveniences caused!');?>
                </div>
            </div>
        <?php else:?>
    <div class="download-list mb20">
        <ul>
            <?php foreach ($tk as $sw=>$ws):
            $ew = $ws['e'];
            ?>
            <li><span>
                <?php
                    if($sw == $ew){
                        echo Yii::t("portfolio","Week :weeknumber", array(":weeknumber"=>$sw));
                    }else{
                        echo Yii::t("portfolio","Week :weeknumber1 - Week :weeknumber2", array(":weeknumber1"=>$sw,":weeknumber2"=>$ew));
                    }
                ?>
                </span>
                <em>
                <?php if (isset($ws['dlpath'])):?>
					<?php if (Yii::app()->user->checkAccess('fullAccessChild', array("childid" => $this->childid))) :?>
						<?php echo CHtml::link(Yii::t("portfolio", 'Download Photos'), Mims::CreateUploadUrl($ws['dlpath']), array("class"=>"download"))?>
					<?php else: ?>
						<?php echo CHtml::link(Yii::t("portfolio", 'Download Photos'), 'javascript:(void);', array("class"=>"download","onclick"=>'alert("No permission"+"\r\n"+"没有权限");'))?>
					<?php endif; ?>
                <?php else:?>
					<?php if ($ws['packing']):?>
						<?php
							echo CHtml::openTag('span', array("title"=>Yii::t("portfolio","An email notification will be sent to you shortly when they are ready for download.")));
							echo Yii::t("portfolio", ':pnum photos found and being packaged.', array(':pnum'=>$ws['pnum']));
							echo CHtml::closeTag('span');
						?>
					<?php else:?>
						<?php if (Yii::app()->user->checkAccess('adminChild', array("childid" => $this->childid))) :?>
							<?php echo CHtml::openTag('span', array('id'=>$year.'_'.$sw))?>
							<?php echo CHtml::ajaxLink(Yii::t("portfolio", 'Package Photos'), $this->createUrl("//portfolio/portfolio/packpic", array(
							"startyear" => $year,
							"sweek" => $sw,
							"eweek" => $ew,
							"schoolid" => $ws['schoolid'],
							)),
							array(
								"success" => "function(data, textStatus, jqXHR){callback(data,'reload')}",
								"beforeSend" => "function(){ $('#".$year."_".$sw."').html('<img src=\"".Yii::app()->theme->baseUrl."/images/loading.gif\">') }"
							),
							array("class"=>"package")
							)
							?>
							<?php echo CHtml::closeTag('span');?>
						<?php else:?>
							<?php echo CHtml::link(Yii::t("portfolio", 'Package Photos'), 'javascript:(void);', array("class"=>"download","onclick"=>'alert("No permission"+"\r\n"+"没有权限");'))?>
						<?php endif;?>
					<?php endif;?>
                <?php endif;?>
                </em>
            </li>
            <?php endforeach;?>
        </ul>
    </div>
    <?php endif;?>
    <?php endforeach;?>
</div>