<?php
class ChildInfo extends CWidget
{
    public $childid=0;
    public $childObj=null;
    public $displaySearch=true;
    public $selectedPayFlag="";
    
    public function run()
    {
        if($this->childObj){
            $this->render('childinfo', array('child'=>$this->childObj));
        }
        elseif ($this->childid){
            $child=$this->getController()->getChild($this->childid);
            $this->render('childinfo', array('child'=>$child));
        }
    }
}