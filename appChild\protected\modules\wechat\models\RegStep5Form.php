<?php
class RegStep5Form extends CFormModel
{
	public $lunch;
	public $lunchSort;
	public $allergen;


	public function rules()
	{
		return array(
			array('lunch', 'required'),
			array('lunchSort', 'lunchSort'),
            array('allergen', 'safe'),
		);
	}

	public function attributeLabels()
	{
		return array(
			'lunch' => Yii::t("reg", "School Lunch"),
			'lunchSort' => Yii::t("reg", "lunchSort"),
			'allergen' => Yii::t("reg", "Food Allergies (Please List in Detail)"),
		);
	}

    public function lunchSort($attribute, $params)
    {
        if ($this->lunch == 1) {
            if (empty($this->$attribute)) {
                $this->addError($attribute,  Yii::t("reg", 'Meal variety cannot be empty'));
            }
        }
    }

    public function safe($attribute, $params)
    {
        if (empty($this->$attribute)) {
            $this->addError($attribute,  Yii::t("reg", 'Please clarify allergy information, fill NO if it is none.'));
        }
    }
}