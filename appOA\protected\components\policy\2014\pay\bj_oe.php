<?php
if(!defined("IVY_POLICY"))
throw new CException('DO NOT USE THIS FILE DIRECTLY');

return array( 'policy'=>array(

	//是否老生老办法
	ENABLE_CONSTANT_TUITION => true,
	// 按天收费
	FENTAN_FACTOR => DAILY,

	//年保育费
	//ANNUAL_CHILDCARE_AMOUNT => 8000,

	//是否开放课外活动账单
	ENABLE_AFTERSCHOOLS => true,

	//图书馆工本费
	FEETYPE_LIBCARD => 10,

	//学期四个时间点；取前后两端
	TIME_POINTS   => array(201409,201412,201501,201607),

	//寒暑假月份，及其每月计费天数
	//HOLIDAY_MONTH => array(201402=>20,201408=>20),

	//年付开通哪些缴费类型
	PAYGROUP_ANNUAL => array(
		ITEMS => array(
			FEETYPE_TUITION,
			FEETYPE_LUNCH,
			FEETYPE_SCHOOLBUS,
		),
		TUITION_CALC_BASE => BY_MONTH,
	),

	//学期付开通哪些缴费类型
	PAYGROUP_SEMESTER => array(
		ITEMS => array(
			FEETYPE_TUITION,
			FEETYPE_LUNCH,
			FEETYPE_SCHOOLBUS,
		),
		TUITION_CALC_BASE => BY_SEMESTER,
	),

	//月份开通哪些缴费类型
	PAYGROUP_MONTH => array(
		ITEMS => array(
			FEETYPE_TUITION,
			FEETYPE_LUNCH,
			FEETYPE_SCHOOLBUS,
		),
		TUITION_CALC_BASE => BY_MONTH,
	),

	//计算基准：BY_MONTH, 或者 BY_SETTING, 前者表示所有的学费都基于月费用计算，后者表示所有的学费都基于学期或学年
	TUITION_CALCULATION_BASE => array(

		BY_MONTH => array(

			//月收费金额
			AMOUNT => array(
				FULL5D => 8500,
				HALF5D => 6400,
				HALF2D => 3200,
				HALF3D => 4608,
				FULL3D => 6120,
				FULL2D => 5100,
			),

			//收费月份，及其权重
			MONTH_FACTOR => array(
				201409=>1,
				201410=>1,
				201411=>1,
				201412=>.8,
				201501=>1,
				201502=>.8,
				201503=>1,
				201504=>1,
				201505=>1,
				201506=>1,
				201507=>1
			),

			//折扣
			GLOBAL_DISCOUNT => array(
				DISCOUNT_ANNUAL => '0.97:1:2014-12-01'
			)
		),
		BY_SEMESTER1 => array(
		    AMOUNT => array(
		        //FULL5D => 46415,
		        FULL5D => 42107,//2019
		        // HALF2D => 28500,
		        // HALF3D => 41500,
		        // HALF5D => 69664,
		    ),
		),

		BY_SEMESTER2 => array(
		    AMOUNT => array(
		        //FULL5D => 43685,
		        FULL5D => 47993,//2019
		        // HALF2D => 28500,
		        // HALF3D => 41500,
		        // HALF5D => 55322,
		    ),
		),
	),

	//每餐费用
	LUNCH_PERDAY => 28,

	//账单到期日和账单开始日之差
	DUEDAYS_OFFSET => 1,

    //代金卷金额
    VOUCHER_AMOUNT => 500,

));

?>