
<div >
    <div v-if='shareType=="view"' style='margin-bottom:40px'><div class="alert alert-warning p10" role="alert"><?php echo Yii::t("reg", "This Qrcode is for internal preview, DO NOT share to parents.");?></div></div>
    <div class='relative pb20 mt20'>
        <div class='Options'>
            <div><label><?php echo Yii::t("reg", "Options");?></label></div>
            <div class='mb20'>
                <div class="checkbox">
                    <label>
                        <input type="checkbox" id="inlineCheckbox1" v-model='showSummary'> <?php echo Yii::t("reg", "Dispaly abstract");?>
                    </label>
                </div>
                <div class="checkbox">
                    <label>
                        <input type="checkbox" id="inlineCheckbox2" v-model='showPublisher'> <?php echo Yii::t("reg", "Display name and title");?>
                    </label>
                </div>
            </div>
            <p style='color:#F0AD4E' class='font14 flex'><span class='glyphicon glyphicon-info-sign mr5 pt2'></span><span class='flex1'><?php echo Yii::t("reg", "Please use screenshot tool to capture the picture.");?></span></p>
        </div>
        <div  class='Thumbnail'>
            <div :class='htmlContent==1?"checkImg":""' @click='htmlContent=1' class='mb20'>
                <img src="http://m2.files.ivykids.cn/cloud01-file-8025768Fvx8x6eNPaQ0XZLdqNPICkriD4Vd.png"  >
            </div>
            <div :class='htmlContent==2?"checkImg":""' @click='htmlContent=2' class='mb20'>
                <img src="http://m2.files.ivykids.cn/cloud01-file-8025768FkunNlr5VhkAvfwlt9cVFicZDavE.png"  >
            </div>
            <div :class='htmlContent==3?"checkImg":""' @click='htmlContent=3' class='mb20'>
                <img src="http://m2.files.ivykids.cn/cloud01-file-8025768FnacKp-4ia2p4sPlbyDfG5ntgE6R.png"  >
            </div>
        </div>
        <div  class='shareContent' style='background: #428BCA;' ref='shareContent1' v-if='htmlContent==1'>
            <div class='bgPto relative' style='background-image: url("http://m2.files.ivykids.cn/cloud01-file-8025768FsL-G-mcYYSw5mn_EmjZHNBW2XXo.png")' >
                <div class='sharePto text-center'>
                    <img class='logo' src="https://m2.files.ivykids.cn/cloud01-file-8025768FuSnI0PSaaQ94OwfrBy7P4WW9hM3.png" alt="">
                    <div class='title'>{{shareList.category=='2'?"特别沟通 Direct Message":"日志 Journal"}}</div>
                    <div class='schoolTitle'>{{shareList.school_title}}</div>
                    <div class='view' v-if='shareType=="view"'>
                        <div>内部预览，请勿分享给家长</div>
                        <div>Internal preview only, do not share to parents</div>
                    </div>
                </div>
                <div class='contentText p20 ' style='background:#fff'>
                    <div v-if='sharetUser=="teacher"'>
                        <div class='color3 font16 title fontWight' >{{shareList.title}}</div>
                        <div class="media mt10 mb20" v-if='shareList.sign_as_uid && showPublisher'>
                            <div class="media-left pull-left media-middle">
                                <a href="javascript:void(0)">
                                    <img class="media-object img-circle shareAvatar" :src="teacherUser(shareList.sign_as_uid,'photo')" data-holder-rendered="true" >
                                </a>
                            </div>
                            <div class="media-body pt5 media-middle">
                                <h4  class="media-heading font12">{{teacherUser(shareList.sign_as_uid,'name')}}</h4>
                                <div class='text-muted'>
                                    {{teacherUser(shareList.sign_as_uid,'title')}}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-else>
                        <div class='color3 font16 title fontWight' >{{shareList.title}}</div>
                        <div class='color3 font16 title fontWight' >{{shareList.title_en}}</div>
                        <div class="media mt10 mb20 " v-if='shareList.sign_as_uid && showPublisher'>
                            <div class="media-left pull-left media-middle">
                                <a href="javascript:void(0)">
                                    <img class="media-object img-circle shareAvatar" :src="leaderUser(shareList.sign_as_uid,'photo')" data-holder-rendered="true" >
                                </a>
                            </div>
                            <div class="media-body pt5 media-middle">
                                <h4 class="media-heading font12">{{leaderUser(shareList.sign_as_uid,'name')}}</h4>
                                <div class='text-muted'>
                                    {{leaderUser(shareList.sign_as_uid,'title')}}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class='time mt5 color6'>{{shareList.format_publish_at}}</div>
                    <div class='wechat '>
                        <img  :src="shareType=='view'?previewQrcode:shareQrcode" class='wechatImg mt10'>
                        <!-- <div class='wechatTitle color6'>微信长按识别，查看详情</div>
                        <div class='wechatTitle color6'>Wechat long press qrcode for details</div> -->
                        <div class='wechatTitle color6 '>微信长按识别 Wechat long press for details</div>
                    </div>
                </div>
                <div class='bottom'>
                    <img src="http://m2.files.ivykids.cn/cloud01-file-8025768Fq95CMHmofnMY8zDky250kSK0jjw.png" alt="">
                </div>
            </div>
        </div>
        <div  class='shareContent' style='border:1px solid #0710B0'  ref='shareContent2' v-if='htmlContent==2'> 
            <div class='bgPto relative' style='background-image: url("http://m2.files.ivykids.cn/cloud01-file-8025768Fm92t6oYcOnmwk1MbwEGNhQlaQZV.png")' >
                <div class='sharePto text-center'>
                    <img class='logo mb20' src="https://m2.files.ivykids.cn/cloud01-file-8025768FuSnI0PSaaQ94OwfrBy7P4WW9hM3.png" alt="">
                    <div class='title' style='color:#061F9D;margin-top:40px'>{{shareList.category=='2'?"特别沟通 Direct Message":"日志 Journal"}}</div>
                    <div class='schoolTitle1' style='color:#061F9D'>{{shareList.school_title}}</div>
                    <div class='view' v-if='shareType=="view"'>
                        <div>内部预览，请勿分享给家长</div>
                        <div>Internal preview only, do not share to parents</div>
                    </div>
                </div>
                <div class='contentText p20' style='background:#F2F5FF'>
                    <div v-if='sharetUser=="teacher"'>
                        <div class='color3 font16 title fontWight' >{{shareList.title}}</div>
                        <div class="media mt10 mb20" v-if='shareList.sign_as_uid && showPublisher'>
                            <div class="media-left pull-left media-middle">
                                <a href="javascript:void(0)">
                                    <img class="media-object img-circle shareAvatar" :src="teacherUser(shareList.sign_as_uid,'photo')" data-holder-rendered="true" >
                                </a>
                            </div>
                            <div class="media-body pt5 media-middle">
                                <h4  class="media-heading font12">{{teacherUser(shareList.sign_as_uid,'name')}}</h4>
                                <div class='text-muted'>{{teacherUser(shareList.sign_as_uid,'title')}}</div>
                            </div>
                        </div>
                    </div>
                    <div v-else>
                        <div class='color3 font16 title fontWight' >{{shareList.title}}</div>
                        <div class='color3 font16 title fontWight' >{{shareList.title_en}}</div>
                        <div class="media mt10 mb20" v-if='shareList.sign_as_uid && showPublisher'>
                            <div class="media-left pull-left media-middle">
                                <a href="javascript:void(0)">
                                    <img class="media-object img-circle shareAvatar" :src="leaderUser(shareList.sign_as_uid,'photo')" data-holder-rendered="true" >
                                </a>
                            </div>
                            <div class="media-body pt5 media-middle">
                                <h4  class="media-heading font12">{{leaderUser(shareList.sign_as_uid,'name')}}</h4>
                                <div class='text-muted'>{{leaderUser(shareList.sign_as_uid,'title')}}</div>
                            </div>
                        </div>
                    </div>
                    <div class='time mt5 color6'>{{shareList.format_publish_at}}</div>
                </div>
                <div class='wechat text-center p20'>
                    <img :src="shareType=='view'?previewQrcode:shareQrcode" class='wechatImg'>
                    <!-- <div class='wechatTitle color6 mt10'>微信长按识别，查看详情</div>
                    <div class='wechatTitle color6'>Wechat long press qrcode for details</div> -->
                    <div class='wechatTitle color6 '>微信长按识别 Wechat long press for details</div>

                </div>
            </div>
        </div>
        <div  class='shareContent' style='background-image: url("http://m2.files.ivykids.cn/cloud01-file-8025768FsubjqpWc9t2CQXvvEdMV4d9leHW.png");background-size: cover' ref='shareContent3' v-if='htmlContent==3'> 
            <div class='bgPto relative' style='' >
                <div class='sharePto text-center'>
                    <img class='logo' src="https://m2.files.ivykids.cn/cloud01-file-8025768FuSnI0PSaaQ94OwfrBy7P4WW9hM3.png" alt="">
                    <div class='title' >{{shareList.category=='2'?"特别沟通 Direct Message":"日志 Journal"}}</div>
                    <div class='schoolTitle'>{{shareList.school_title}}</div>
                    <div class='view' v-if='shareType=="view"'>
                        <div>内部预览，请勿分享给家长</div>
                        <div>Internal preview only, do not share to parents</div>
                    </div>
                </div>
                <div class='contentText white p8'>
                    <div  v-if='sharetUser=="teacher"'>
                        <div class=' font16 title fontWight' >{{shareList.title}}</div>
                        <div class="media mt10 mb20" v-if='shareList.sign_as_uid && showPublisher'>
                            <div class="media-left pull-left media-middle">
                                <a href="javascript:void(0)">
                                    <img class="media-object img-circle shareAvatar" :src="teacherUser(shareList.sign_as_uid,'photo')" data-holder-rendered="true" >
                                </a>
                            </div>
                            <div class="media-body pt5 media-middle">
                                <h4  class="media-heading font12">{{teacherUser(shareList.sign_as_uid,'name')}}</h4>
                                <div class=''>{{teacherUser(shareList.sign_as_uid,'title')}}</div>
                            </div>
                        </div>
                    </div>
                    <div v-else>
                        <div class=' font16 title fontWight' >{{shareList.title}}</div>
                        <div class=' font16 title fontWight' >{{shareList.title_en}}</div>
                        <div class="media mt10 mb20" v-if='shareList.sign_as_uid && showPublisher'>
                            <div class="media-left pull-left media-middle">
                                <a href="javascript:void(0)">
                                    <img class="media-object img-circle shareAvatar" :src="leaderUser(shareList.sign_as_uid,'photo')" data-holder-rendered="true" >
                                </a>
                            </div>
                            <div class="media-body pt5 media-middle">
                                <h4  class="media-heading font12">{{leaderUser(shareList.sign_as_uid,'name')}}</h4>
                                <div class=''>{{leaderUser(shareList.sign_as_uid,'title')}}</div>
                            </div>
                        </div>
                    </div>
                    <div class='time mt5 white08'>{{shareList.format_publish_at}}</div>
                </div>
                <div class='wechat p24 white flex'>
                    <div class='flex1 white08'>
                        <div class='wechatTitle'>微信长按识别</div>
                        <div class='wechatTitle '>Wechat long press for details</div>
                    </div>
                    <img :src="shareType=='view'?previewQrcode:shareQrcode" class='wechatImg'>                    
                </div>
            </div>
        </div>
    </div>
</div>