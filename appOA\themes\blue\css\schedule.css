#schedule{width: 967px;overflow-x:auto; overflow-y: hidden; min-height: 1600px;}

#schedule #sLeft{width: 60px; float: left;}
#schedule #sRight{}
#schedule #sLeft .tb{height: 20px; position: relative;}
#schedule #sLeft .tb .tbt{z-index: 999;text-align:center;text-shadow:2px 1px 1px rgba(255, 255, 255, 0.6);color:#999;}
.bg1{background-color: 	#CDBFAC;}
.bg2{background-color: #E1D4C0;}
.hli{text-align: center;}
.thx{width: 180px; margin-left: 1px;}
.sT{overflow-y:auto;}
.sT-tit{font-weight: bold;text-align: center;padding: 5px}
.sT-txt{padding: 0 5px;}
.tbend{height: 0 !important; position: /*absolute !important;*/ bottom: 13px;}
.hul{position: relative;}
.hli{height: 25px;line-height: 25px;background-color: #FF7D0D; font-weight: bold;color: #FFF;font-size: 14px;}
#add-sde{
    height: 119px;
    line-height: 119px;
    background-color: rgba(242, 242, 242, 0.49);
    border: 1px dashed #D8D8D8;
    display: block;
    text-align: center;
    font-size: 16px;
}
.timeslot{
    position: relative;
    overflow-y: auto;
    opacity: 0.8;
    padding: 3px;
}
.timeslot .toolbar{
    position: absolute;
    background:#efefef;
    top: 0;
    right: 0;
    visibility: hidden;
    padding: 2px 4px;
    border-left: 1px solid #dedede;
    border-bottom: 1px solid #ddd;
    cursor: pointer;
}
.timeslot:hover .toolbar{
    visibility: visible;
}
.timeslot .text{
    padding: 6px 2px;
}
.daytitle{
    position: relative;
}
.daytitle .toolbar{
    position: absolute;
    color: #F2F2F2;
    right: 3px !important;
    visibility:hidden;
    cursor: pointer;
}
.daytitle:hover .toolbar{
    visibility: visible;
}
.daydata{
    min-height: 800px;
}
li.save-btn{position:relative}
li.save-btn em{position: absolute; width:16px;height:16px;display:block;background:url(../../images/schedule_btn.png) no-repeat 999px 999px transparent;right:0; top:-10px;text-indent:-9999px;overflow:hidden;}
li.save-btn em.pending{background-position:-48px 0}
li.save-btn em.saved{background-position:-64px 0}