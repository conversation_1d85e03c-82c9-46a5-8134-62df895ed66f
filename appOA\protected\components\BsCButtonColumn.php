<?php
class BsCButtonColumn extends CButtonColumn
{
	public $template='{update} {delete}';

    public $updateButtonImageUrl = false;

    public $updateButtonLabel = '<i class="glyphicon glyphicon-pencil"></i>';

    public $updateButtonOptions = array('class'=>'J_dialog btn btn-info btn-xs');

    public $deleteButtonImageUrl = false;

    public $deleteButtonLabel = '<i class="glyphicon glyphicon-remove"></i>';

    public $deleteButtonOptions = array('class'=>'J_ajax_del btn btn-danger btn-xs');

    protected function initDefaultButtons()
    {
        if(!isset($this->updateButtonOptions['title'])){
        $this->updateButtonOptions['title']=Yii::t('zii', 'Update');
        }
        if(!isset($this->deleteButtonOptions['title'])){
            $this->deleteButtonOptions['title']=Yii::t('zii', 'Delete');
        }
        foreach(array('update','delete') as $id)
        {
            $button=array(
                'label'=>$this->{$id.'ButtonLabel'},
                'url'=>$this->{$id.'ButtonUrl'},
                'imageUrl'=>$this->{$id.'ButtonImageUrl'},
                'options'=>$this->{$id.'ButtonOptions'},
            );
            if(isset($this->buttons[$id]))
                $this->buttons[$id]=array_merge($button,$this->buttons[$id]);
            else
                $this->buttons[$id]=$button;
        }
    }
}
