<?php

/**
 * This is the model class for table "ivy_easyapply_transfer".
 *
 * The followings are the available columns in table 'ivy_easyapply_transfer':
 * @property integer $id
 * @property string $apply_id
 * @property string $apply_year
 * @property string $apply_data
 * @property integer $child_id
 * @property integer $class_id
 * @property integer $status
 * @property integer $sibling_id
 * @property integer $updated_by
 * @property integer $updated_at
 */
class EasyapplyTransfer extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_easyapply_transfer';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('apply_id, apply_year, child_id, class_id, updated_by, updated_at', 'required'),
			array('child_id, class_id, status, sibling_id, updated_by, updated_at, discount_id', 'numerical', 'integerOnly' => true),
			array('apply_id, apply_year', 'length', 'max' => 255),
			array('apply_data', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('apply_id, apply_year, apply_data, child_id, class_id, status, sibling_id, updated_by, updated_at', 'safe', 'on' => 'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array();
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'apply_id' => 'Apply',
			'apply_year' => 'Apply Year',
			'apply_data' => 'Apply Data',
			'child_id' => 'Child',
			'class_id' => 'Class',
			'status' => 'Status',
			'sibling_id' => 'Sibling',
			'updated_by' => 'Updated By',
			'updated_at' => 'Updated At',
			'discount_id' => 'Discount Id',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria = new CDbCriteria;

		$criteria->compare('id', $this->id);
		$criteria->compare('apply_id', $this->apply_id, true);
		$criteria->compare('apply_year', $this->apply_year, true);
		$criteria->compare('apply_data', $this->apply_data, true);
		$criteria->compare('child_id', $this->child_id);
		$criteria->compare('class_id', $this->class_id);
		$criteria->compare('status', $this->status);
		$criteria->compare('sibling_id', $this->sibling_id);
		$criteria->compare('updated_by', $this->updated_by);
		$criteria->compare('updated_at', $this->updated_at);

		return new CActiveDataProvider($this, array(
			'criteria' => $criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return EasyapplyTransfer the static model class
	 */
	public static function model($className = __CLASS__)
	{
		return parent::model($className);
	}


	public function generateInvoice($amount, $type, $discountId = 0, $invoiceId = 0)
	{
		$applyId = $this->apply_id;
		$startyear = $this->apply_year;
		$childId = $this->child_id;
		$childObj = ChildProfileBasic::model()->findByPk($childId);
		$schoolId = $childObj->schoolid;
		$amount = $amount / 100;
		$policy = new IvyPolicy("pay", $startyear, $schoolId);
		if (!$policy) {
			return array(
				'state' => 'fail',
				'msg' => "$schoolId , $startyear 收退费政策不存在"
			);
		}

		$schoolyear = CalendarSchool::model()->with('cTemplate')->findByAttributes(array(
			'startyear' => $startyear,
			'branchid' => $schoolId,
		));
		$semester = 0;
		if ($type == "tuitionBySemester") {
			$fee_type = 1;
			if (date("m", $childObj->est_enter_date) < 8) {
				$semester = 2;
			} else {
				$semester = 1;
			}
		} else {
			$fee_type = 1;
		}
		// 匹配折扣
		$nodiscountAmount = $amount;
		if ($discountId > 0) {
			$discount = DiscountSchool::model()->findByPk($discountId);
			if ($discount) {
				$nodiscountAmount = $amount / ($discount->discount / 100);
			}
		}

		$timepoints = explode(',', $schoolyear->cTemplate->timepoints);
		if ($semester == 1) {
			$byType = 'PAYGROUP_SEMESTER';
			$startDate = strtotime(date('Y', $timepoints[0]) . '-09-01');
			$endDate = $timepoints[1];
		} elseif ($semester == 2) {
			$byType = 'PAYGROUP_SEMESTER';
			$startDate = $timepoints[2];
			$endDate = $timepoints[3];
		} else {
			$byType = 'PAYGROUP_ANNUAL';
			$startDate = strtotime(date('Y', $timepoints[0]) . '-09-01');
			$endDate = $timepoints[3];
		}

		// 生成 invoice
		$transaction = Yii::app()->subdb->beginTransaction();
		try {
			$params = array(
				'byType' => $byType,
				'feeType' => 'FEETYPE_TUITION',
				'startDate' => $startDate,
				'endDate' => $endDate,
				'feeProgram' => 'FULL5D',
			);

			$invoiceAttr = array(
				'calendar_id' => $schoolyear->yid,
				'amount' => $amount,
				'nodiscount_amount' => $nodiscountAmount,
				'original_amount' => $amount,
				'schoolid' => $schoolId,
				'childid' => $childId,
				'classid' => $this->class_id,
				'payment_type' => 'tuition',
				'inout' => Invoice::INVOICE_INOUT_IN,
				'startdate' => $startDate,
				'enddate' => $endDate,
				'fee_type' => $fee_type,
				'fee_program' => 'FULL5D',
				'duetime' => time() + 7 * 86400,
				'title' => $policy->genInvoiceTitle($params),
				'discount_id' => $discountId,
				'userid' => 1,
				'timestamp' => time(),
				'status' => Invoice::STATS_UNPAID,
				'child_service_info' => 'a:5:{s:3:"mon";i:20;s:3:"tue";i:20;s:3:"wed";i:20;s:3:"thu";i:20;s:3:"fri";i:20;}',
			);
			$model = new Invoice();
			$model->setAttributes($invoiceAttr);
			if (!$model->save()) {
				$error = current($model->getErrors());
				throw new Exception('保存 ivy_invoice_invoice 失败' . $error[0]);
			}
			$eaInvoice = new EasyapplyInvoice();
			$eaInvoice->invoiceid = $invoiceId;
			$eaInvoice->invoiceid_oa = $model->invoice_id;
			$eaInvoice->trade_ids = json_encode(array());
			$eaInvoice->apply_id = $applyId;
			$eaInvoice->apply_year = $startyear;
			$eaInvoice->child_id = $childId;
			$eaInvoice->class_id = $model->classid;
			$eaInvoice->updated_by = 1;
			$eaInvoice->updated_at = time();
			if (!$eaInvoice->save()) {
				$error = current($eaInvoice->getErrors());
				throw new Exception('保存 ivy_easyapply_invoice 失败' . $error[0]);
			}
			$transaction->commit();
			return array(
				'state' => 'success',
				'data' => array(
					'oaInvoice' => $model,
					'eaInvoice' => $eaInvoice,
				)
			);
		} catch (Exception $e) {
			$transaction->rollBack();
			return array(
				'state' => 'fail',
				'msg' => "eaInvoice: $invoiceId, 创建账单失败：" .  $e->getMessage()
			);
		}
	}

	public function generateOrder($tradeList, $oaInvoice, $eaInvoice, $schoolId)
	{
		$i = 0;
		foreach ($tradeList as $item) {
			$tradeId = $item['tradeId'];
			// 检查是否已生成微信订单
			$tradeIdList = json_decode($eaInvoice['trade_ids']) ? json_decode($eaInvoice['trade_ids']) : array();
			if (in_array($tradeId, $tradeIdList)) {
				continue;
			}
			// 生成微信订单
			Yii::import("common.models.wxpay.WechatPayOrder");
			Yii::import("common.models.wxpay.WechatPayOrderItem");
			Yii::import('common.components.policy.*');
			Yii::import("common.models.invoice.InvoiceTransaction");
			if ($i > 0) {
				// 重新赋值已支付金额
				$oaInvoice->paidSum = InvoiceTransaction::model()->getPaidSum($oaInvoice->invoice_id);
			}
			try {
				$childid = $oaInvoice->childid;
				$oaInvoice_id = $oaInvoice->invoice_id;
				$amount = round($item['amount'] / 100, 2);

				$payTime = strtotime($item['payTime']);
				$wxpayInfo = CommonUtils::LoadConfig('CfgWxPayGlobal');
				$number_code = $wxpayInfo[$schoolId]['number_code'];
				$wechatPayOrder = new WechatPayOrder();
				$wechatPayOrder->orderid = $wechatPayOrder->genOrderID($number_code . $oaInvoice_id);
				$wechatPayOrder->payable_amount = $amount;
				$wechatPayOrder->fact_amount = 0;
				$wechatPayOrder->schoolid = $schoolId;
				$wechatPayOrder->childid = $childid;
				$wechatPayOrder->type = 'NATIVE';
				$wechatPayOrder->status = 0;
				$wechatPayOrder->settlement_status = 0;
				$wechatPayOrder->order_time = $payTime;
				$wechatPayOrder->update_timestamp = $payTime;
				$wechatPayOrder->uid = 1;
				if (!$wechatPayOrder->save()) {
					$error = current($wechatPayOrder->getErrors());
					throw new Exception('保存 wechatPayOrder 失败' . $error[0]);
				}

				$wechatPayOrderItem = new WechatPayOrderItem();
				$wechatPayOrderItem->orderid = $wechatPayOrder->orderid;
				$wechatPayOrderItem->invoice_id = $oaInvoice_id;
				$wechatPayOrderItem->amount = $amount;
				$wechatPayOrderItem->save();
				if (!$wechatPayOrderItem->save()) {
					$error = current($wechatPayOrderItem->getErrors());
					throw new Exception('保存 wechatPayOrder 失败' . $error[0]);
				}
				// 付款
				$policyApi = new PolicyApi($schoolId);
				$payType = InvoiceTransaction::TYPE_WX_NATIVE;
				$payResult = $policyApi->Pay(array($oaInvoice), $payType, $amount, 1);
				// 判断账单支付结果
				if ($payResult != 0) {
					throw new Exception('付款失败');
				}
				$wechatPayOrder->fact_amount = $amount;
				$wechatPayOrder->status = 1;
				$wechatPayOrder->save();

				$tradeIdList[] = $tradeId;
				$eaInvoice->trade_ids = json_encode($tradeIdList);
				if (!$eaInvoice->save()) {
					$error = current($eaInvoice->getErrors());
					throw new Exception('保存 EasyapplyInvoice 失败' . $error[0]);
				}
			} catch (Exception $e) {
				return array(
					'state' => 'fail',
					'msg' => "tradeId: {$tradeId}, 创建订单失败：" .  $e->getMessage()
				);
			}
			$i++;
		}
		return array(
			'state' => 'success',
			'msg' => "处理成功"
		);
	}
}
