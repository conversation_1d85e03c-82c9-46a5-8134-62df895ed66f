<?php


/**
 * This is the model class for table "ivy_timetable_course_data".
 *
 * The followings are the available columns in table 'ivy_timetable_course_data':
 * @property integer $id
 * @property integer school_id
 * @property integer target_day
 * @property string target_weeknum
 * @property integer target_monday
 * @property string source_day
 * @property integer start_year
 * @property integer tid
 * @property integer yid
 * @property integer replace_schedule
 * @property integer replace_day
 * @property integer $created_at
 * @property integer $created_by
 * @property integer $updated_at
 * @property integer $updated_by
 */
class TimetableDailyReplace extends CActiveRecord
{

    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return 'ivy_timetable_daily_replace';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array();
    }

    /**
     * @return array relational rules.
     */
    public function relations()
    {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array();
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return array(
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     *
     * Typical usecase:
     * - Initialize the model fields with values from filter form.
     * - Execute this method to get CActiveDataProvider instance which will filter
     * models according to data in model fields.
     * - Pass data provider to CGridView, CListView or any similar widget.
     *
     * @return CActiveDataProvider the data provider that can return the models
     * based on the search/filter conditions.
     */
    public function search()
    {
        // @todo Please modify the following code to remove attributes that should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('id', $this->id);
        $criteria->compare('school_id', $this->school_id);
        $criteria->compare('target_day', $this->target_day);
        $criteria->compare('target_weeknum', $this->target_weeknum);
        $criteria->compare('target_monday', $this->target_monday);
        $criteria->compare('source_day', $this->source_day);
        $criteria->compare('start_year', $this->start_year);
        $criteria->compare('tid', $this->tid);
        $criteria->compare('yid', $this->yid);
        $criteria->compare('replace_schedule', $this->replace_schedule);
        $criteria->compare('replace_day', $this->replace_day);
        $criteria->compare('created_at', $this->created_at);
        $criteria->compare('created_by', $this->created_by);
        $criteria->compare('updated_at', $this->updated_at);
        $criteria->compare('updated_by', $this->updated_by);

        return new CActiveDataProvider($this, array(
            'criteria' => $criteria,
        ));
    }

    /**
     * @return CDbConnection the database connection used for this class
     */
    public function getDbConnection()
    {
        return Yii::app()->subdb;
    }

    /**
     * Returns the static model of the specified AR class.
     * Please note that you should have this exact method in all your CActiveRecord descendants!
     * @param string $className active record class name.
     * @return TimetableCourseData the static model class
     */
    public static function model($className = __CLASS__)
    {
        return parent::model($className);
    }
}
