<style>
     [v-cloak] {
        display: none;
    }
    .addImg{
        width: 54px;
       height: 54px;
       border-radius: 8px;
       object-fit: cover;
    }
    .avatar{
        width:54px;
        height:54px;
        border-radius:50%;
        object-fit: cover;
    }
    .font18{
        font-size:18px
    }
    .assessment{
        background: #FAFAFA;
        padding:16px;
        line-height: 20px;
    }
    .colorc{
        color:#cccccc
    }
    .score tr td{
        vertical-align: middle !important
    }
    .font16{
        font-size:16px
    }
    .enbreak{
        text-align: justify;
        padding:10px;
        line-height:20px;
        word-break: break-word;
        word-wrap: break-word;
    }
    .yellow{
        color:#F0AD4E
    }
    .red{
        color:#D9534F
    }
    .bg7{
        background-color: #777777 !important
    }
    .classTitle{
        border:1px solid rgba(0, 0, 0, 0.15);
        background:rgba(0, 0, 0, 0.04);
        padding:4px 8px;
        display:inline-block
    }
    .table_wrap {
        width: 100%;
        overflow: auto;
    }
    .table_wrap .table {
        table-layout: fixed;
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        border-left: 1px solid #DDDDDD;
        border-top: 1px solid #DDDDDD;
    }
    .table_wrap .table td,.table_wrap .table th {
        width: 250px;
        border-right: 1px solid #DDDDDD;
        border-bottom: 1px solid #DDDDDD;
        border-top:none !important;
        padding: 16px !important
    }
    /* 表头固定 */
    .table_wrap .table tr th {
        position: sticky;
        top: 0;
        background: #FAFAFA;
        z-index:1
    }
    .scrollbar::-webkit-scrollbar {
        /*滚动条整体样式*/
        width : 10px;  /*高宽分别对应横竖滚动条的尺寸*/
        height:10px;
    }
    .scrollbar::-webkit-scrollbar-thumb {
        border-radius   : 10px;
        background-color: skyblue;
        background-image: -webkit-linear-gradient(
        45deg,
        rgba(255, 255, 255, 0.2) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0.2) 75%,
        transparent 75%,
        transparent
        );
    }
    .scrollbar::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0.2);
        border-radius: 10px;
        background   : #ededed;
    }
    .reserved{
        background: #F6F9FF;
        border-radius: 4px;
        padding:12px
    }
    .timeStart{
        border-bottom: 1px solid #E5E7EB;
    }
    .timeList{
        min-height:100px
    }
    .green{
        color:#1FA11A
    }
    .teacherImg{
        width:24px;
        height:24px;
        border-radius:50%;
        object-fit: cover;
    }
    .subTop{
        top:25px
    }
    .example{
        width: 25px;
        height: 25px;
    }
    .translate{
        position: absolute;
        width: 25px;
        height: 25px;
        bottom: -8px;
        left: 25px;
    }
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Teaching Tasks'), array('//mteaching/default/index')) ?></li>
        <li><?php echo Yii::t('newDS', 'PTC') ?></li>
        <li><?php echo Yii::t("ptc", "My Schedule");?></li>
    </ol>
    <div class="row" id='container' v-cloak>
        <div class='col-md-12 col-sm-12 mb20' v-if='initData.itemData'>
            <p class='font14'><a href="<?php echo $this->createUrl('index', array('branchId' => $this->branchId,'yid'=>Yii::app()->request->getParam('yid', ''),'startyear'=>Yii::app()->request->getParam('startyear', ''),'semester'=>Yii::app()->request->getParam('semester', ''))); ?>"><span class='glyphicon glyphicon-chevron-left font12 mr5'></span> PTC</a> | <span class='ml5'><?php echo Yii::t("labels", "My Schedule");?></span> </p>
            <div class='form-inline mt20'>
                <select class="form-control mr20" v-model='startYear' @change='myptcData()' disabled>
                    <option v-for='(list,key,index) in startYearList' :value='key'>{{list}}</option>
                </select>
                <select class="form-control mr20" v-model='semester'  @change='myptcData()' disabled>
                    <option v-for='(list,key,index) in semesterList' :value='key'>{{list}}</option>
                </select>
            </div>
            <div class='mt20 flex'>
                <div class='flex1'>
                    <span  @click="chooseTeacher()">                
                        <span class="glyphicon glyphicon-user color6 font16 mr5"></span>
                        <span style='color:#4D88D2' class='font16'>{{teacherTitle==''?teacherInfo.name:teacherTitle}}</span>
                        <span style='color:#4D88D2' class="glyphicon glyphicon-chevron-down"></span>
                    </span>
                </div>
               <div>
                <button type="button" class="btn btn-primary" @click='time'><?php echo Yii::t('ptc','Schedulable Time') ?></button>   
               </div>
            </div>
            <div class='flex mt20 font14' v-if='initData.itemData.length!=0'>
                <ul class="nav nav-pills">
                    <li role="presentation" :class='currentDate=="all"?"active":""' @click='showDate("all")'><a href="javascript:;"><?php echo Yii::t("dashboard", "All");?></a></li>
                    <li role="presentation" v-for='(list,key,index) in initData.itemData' :class='currentDate==key?"active":""' @click='showDate(key)'><a href="javascript:;">{{key}}</a></li>
                </ul>
            </div>
            <div class='mt20'>
                <span class='font14 color3'><?php echo Yii::t('ptc','Legend: ') ?></span> 
                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/A-E.png' ?>" class='example'  alt=""> <span class='color6'>{{initData.translationConfig[1].title}}</span> 
                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/E-A.png' ?>" class='example ml20' alt=""> <span class='color6'>{{initData.translationConfig[2].title}}</span>
                
            </div>
            <div class=' scrollbar  mb20 mt20'>
                <div  v-if='initData.itemData.length==0 && initData.noTimeData.length==0'>
                    <div class="alert alert-warning" role="alert" ><?php echo Yii::t("ptc", "No Data");?></div>  
                </div>
                <table class="table table-bordered  score" v-else>
                    <tr>
                        <th width='100' class='text-center'><?php echo Yii::t("labels", "Time");?></th>
                        <th width='200'><?php echo Yii::t("ptc", "Student");?></th>
                        <th width='500'><?php echo Yii::t("ptc", "Score");?></th>
                        <th width='700'><?php echo Yii::t("ptc", "Improvement Plan");?></th>
                    </tr>
                    <template v-if='currentDate=="all"'>
                        <tr  v-for='(item,index) in initData.noTimeData'>
                            <td class='text-center '>
                                <span class='red' ><?php echo Yii::t("ptc", "Not Scheduled");?></span> 
                                <div v-if='item.location!=null' class='color6 mt10 font12'>{{item.location}}</div>
                                <span v-if='item.parents_choose==-1'><span class='glyphicon glyphicon-exclamation-sign red'></span> <?php echo Yii::t("ptc", "Not to attend");?></span> 
                                <span  v-if='item.parents_choose==1'><span class='glyphicon glyphicon-ok-sign green'></span> <?php echo Yii::t("ptc", "Confirmed to attend"); ?></span>
                            </td>
                            <td>
                                <div class="media pb10">
                                    <div class="media-left pull-left media-middle relative">
                                        <img :src="initData.childInfo[item.child_id].avatar"  data-holder-rendered="true" class="avatar">
                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/E-A.png' ?>" class='translate' alt="" v-if='initData.translation[item.child_id]==3'>
                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/A-E.png' ?>" class='translate' alt="" v-if='initData.translation[item.child_id]==2'>
                                    </div> 
                                    <div class="media-body media-middle">
                                        <div class="font14 mt5 color3">{{initData.childInfo[item.child_id].name}}</div> 
                                        <div class=" mt5 color6">{{initData.childInfo[item.child_id].class_name}}</div> 
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div v-if='initData.scoreData[item.child_id] && initData.scoreData[item.child_id][item.subject]' class='assessment'>
                                        <div v-if='initData.scoreData[item.child_id][item.subject].assessment && initData.scoreData[item.child_id][item.subject].assessment.length!=0'>
                                        <div v-for='(_item,idx) in initData.scoreData[item.child_id][item.subject].assessment' class='relative mt5'>
                                            <div v-if='_item.title=="Math" || _item.title=="CLA"' class='relative'>
                                                {{_item.title}} :
                                                <span class='text-primary' data-toggle="popover" data-trigger="focus" title="Dismissible popover" data-content="And here's some amazing content. It's very engaging. Right?" @click='showPopover(index,_item,"noTimeData")'><?php echo Yii::t('ptc','Number of areas for growth') ?></span> 
                                                <div class="popover bottom subTop" :id='"nosub"+index'>
                                                    <div class="arrow"></div>
                                                    <h3 class="popover-title"><?php echo Yii::t('ptc','Number of areas for growth') ?>({{_item.score}})</h3>
                                                    <div class="popover-content" v-if='popoverData.l'>
                                                        <div v-if='popoverData.l.length!=0'>
                                                            <div class='font14 color3 mb10'><strong>L（Learner）：</strong><span class='pointNum'>{{popoverData.l.length}}</span>  </div>
                                                            <div class='flex' v-for='l in popoverData.l'>
                                                                <div><span class='point'></span></div>
                                                                <div class='flex1'>
                                                                    <div >{{l}}</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div v-if='popoverData.n.length!=0'>
                                                            <div class='font14 color3 mb10'><strong>N（Novice）：</strong><span class='pointNum'>{{popoverData.n.length}}</span> </div>
                                                            <div class='flex' v-for='n in popoverData.n'>
                                                                <div><span class='point'></span></div>
                                                                <div class='flex1'>
                                                                    <div >{{n}}</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <span class="label label-default ml5 mt5">{{_item.score}}<span v-if='_item.target!=null && _item.target!=""'>/{{_item.target}}</span></span>   
                                                <span class="label label-default ml10"  v-if='_item.title=="MAP Reading" && _item.standard!=null'>{{_item.standard}}</span>
                                                <span  v-if='_item.target==null || _item.target===""' class='yellow ml20'><span class='glyphicon glyphicon-exclamation-sign'></span> <?php echo Yii::t('ptc','Target score not set') ?></span>
                                            </div>
                                            <div v-else>
                                            {{_item.title}} :
                                            <span class="label label-default ml5 mt5">{{_item.score}}<span v-if='_item.target!=null  && _item.target!=""'>/{{_item.target}}</span></span>   
                                            <span class="label label-default ml10"  v-if='_item.title=="MAP Reading" && _item.standard!=null'>{{_item.standard}}</span>
                                            <span  v-if='_item.target==null || _item.target===""' class='yellow ml20'><span class='glyphicon glyphicon-exclamation-sign'></span> <?php echo Yii::t('ptc','Target score not set') ?></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div v-if='initData.scoreData[item.child_id][item.subject].courses && initData.scoreData[item.child_id][item.subject].courses.length!=0'>
                                        <div v-for='(_item,idx) in initData.scoreData[item.child_id][item.subject].courses' class='relative mt5 ml10'>
                                            {{_item.title}} :
                                            <p v-for='(__item,i) in _item.subdomain' class='ml20 mt5'>{{__item.subdomain}} :
                                                <span class="label label-default ml5">{{__item.score}}<span v-if='__item.target!=null && __item.target!=""'>/{{__item.target}}</span></span>
                                                <span class="label label-default ml10" v-if='__item.standard!=null'>{{__item.standard}}</span>
                                                <span  v-if='__item.target==null || __item.target===""' class='yellow ml20'><span class='glyphicon glyphicon-exclamation-sign'></span> <?php echo Yii::t('ptc','Target score not set') ?></span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div v-else  >
                                    <div class='mt10 colorc text-center'><?php echo Yii::t("ptc", "No scores recorded");?></div>  
                                </div>
                            </td>
                            <td>
                                <div v-if='initData.planData[item.child_id] && initData.planData[item.child_id][item.subject]'>
                                    <div class='mt5 enbreak font14' v-if='initData.planData[item.child_id][item.subject]!="" && initData.planData[item.child_id][item.subject]!=null' v-html='html(initData.planData[item.child_id][item.subject])'></div>
                                    <div v-else class='mt10 colorc text-center'>N/A</div>
                                </div>
                                <div v-else class='mt10 colorc text-center'>N/A</div>
                            </td>
                        </tr>
                    </template>
                    <template v-for='(list,key,index) in itemData'>
                        <tr v-for='(item,i) in list'>
                            <td class='text-center font14'>
                                {{key}}
                                <div class='mt5'> {{item.time}}</div>
                                <div v-if='item.location!=null' class='color6 mt10 font12'>{{item.location}}</div>
                                <span v-if='item.parents_choose==-1'><span class='glyphicon glyphicon-exclamation-sign red'></span> <?php echo Yii::t("ptc", "Not to attend");?></span> 
                                <span  v-if='item.parents_choose==1'><span class='glyphicon glyphicon-ok-sign green'></span> <?php echo Yii::t("ptc", "Confirmed to attend"); ?></span>
                            </td>
                            <td>
                                <div class="media pb10">
                                    <div class="media-left pull-left media-middle relative">
                                        <img :src="initData.childInfo[item.child_id].avatar"  data-holder-rendered="true" class="avatar">
                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/E-A.png' ?>" class='translate' alt="" v-if='initData.translation[item.child_id]==3'>
                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/A-E.png' ?>" class='translate' alt="" v-if='initData.translation[item.child_id]==2'>
                                    </div> 
                                    <div class="media-body media-middle">
                                        <div class="font14 mt5 color3">{{initData.childInfo[item.child_id].name}}</div> 
                                        <div class=" mt5 color6">{{initData.childInfo[item.child_id].class_name}}</div> 
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div v-if='initData.scoreData[item.child_id] && initData.scoreData[item.child_id][item.subject]' class='assessment'>
                                        <div v-if='initData.scoreData[item.child_id][item.subject].assessment && initData.scoreData[item.child_id][item.subject].assessment.length!=0'>
                                        <div v-for='(_item,idx) in initData.scoreData[item.child_id][item.subject].assessment' class='relative mt5'>
                                            <div v-if='_item.title=="Math" || _item.title=="CLA"' class='relative'>
                                                {{_item.title}} :
                                                <span class='text-primary' data-toggle="popover" data-trigger="focus" title="Dismissible popover" data-content="And here's some amazing content. It's very engaging. Right?" @click='showPopover(index,_item,"itemData")'><?php echo Yii::t('ptc','Number of areas for growth') ?></span> 
                                                <div class="popover bottom subTop" :id='"sub"+index'>
                                                    <div class="arrow"></div>
                                                    <h3 class="popover-title"><?php echo Yii::t('ptc','Number of areas for growth') ?>({{_item.score}})</h3>
                                                    <div class="popover-content" v-if='popoverData.l'>
                                                        <div v-if='popoverData.l.length!=0'>
                                                            <div class='font14 color3 mb10'><strong>L（Learner）：</strong><span class='pointNum'>{{popoverData.l.length}}</span>  </div>
                                                            <div class='flex' v-for='l in popoverData.l'>
                                                                <div><span class='point'></span></div>
                                                                <div class='flex1'>
                                                                    <div >{{l}}</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div v-if='popoverData.n.length!=0'>
                                                            <div class='font14 color3 mb10'><strong>N（Novice）：</strong><span class='pointNum'>{{popoverData.n.length}}</span> </div>
                                                            <div class='flex' v-for='n in popoverData.n'>
                                                                <div><span class='point'></span></div>
                                                                <div class='flex1'>
                                                                    <div >{{n}}</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <span class="label label-default ml5 mt5">{{_item.score}}<span v-if='_item.target!=null && _item.target!=""'>/{{_item.target}}</span></span>   
                                                <span class="label label-default ml10"  v-if='_item.title=="MAP Reading" && _item.standard!=null'>{{_item.standard}}</span>
                                                <span  v-if='_item.target==null || _item.target===""' class='yellow ml20'><span class='glyphicon glyphicon-exclamation-sign'></span> <?php echo Yii::t('ptc','Target score not set') ?></span>
                                            </div>
                                            <div v-else>
                                                {{_item.title}} :
                                                <span class="label label-default ml5 mt5">{{_item.score}}<span v-if='_item.target!=null && _item.target!=""'>/{{_item.target}}</span></span>   
                                                <span class="label label-default ml10"  v-if='_item.title=="MAP Reading" && _item.standard!=null'>{{_item.standard}}</span>
                                                <span  v-if='_item.target==null || _item.target===""' class='yellow ml20'><span class='glyphicon glyphicon-exclamation-sign'></span> <?php echo Yii::t('ptc','Target score not set') ?></span>
                                            </div>
                                            
                                        </div>
                                    </div>
                                    <div v-if='initData.scoreData[item.child_id][item.subject].courses && initData.scoreData[item.child_id][item.subject].courses.length!=0'>
                                        <div v-for='(_item,idx) in initData.scoreData[item.child_id][item.subject].courses' class='relative mt5 ml10'>
                                            {{_item.title}} :
                                            <p v-for='(__item,i) in _item.subdomain' class='ml20 mt5'>{{__item.subdomain}} :
                                                <span class="label label-default ml5">{{__item.score}}<span v-if='__item.target!=null && __item.target!=""'>/{{__item.target}}</span></span>
                                                <span class="label label-default ml10" v-if='__item.standard!=null'>{{__item.standard}}</span>
                                                <span  v-if='__item.target==null || __item.target===""' class='yellow ml20'><span class='glyphicon glyphicon-exclamation-sign'></span> <?php echo Yii::t('ptc','Target score not set') ?></span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div v-else  >
                                    <div class='mt10 colorc text-center'><?php echo Yii::t("ptc", "No scores recorded");?></div>  
                                </div>
                            </td>
                            <td>
                                <div v-if='initData.planData[item.child_id] && initData.planData[item.child_id][item.subject]'>
                                    <div class='mt5 enbreak font14' v-if='initData.planData[item.child_id][item.subject]!="" && initData.planData[item.child_id][item.subject]!=null' v-html='html(initData.planData[item.child_id][item.subject])'></div>
                                    <div v-else class='mt10 colorc text-center'>N/A</div>
                                </div>
                                <div v-else class='mt10 colorc text-center'>N/A</div>
                            </td>
                        </tr>
                    </template>
                </table>
                <div class="alert alert-warning" role="alert" v-else><?php echo Yii::t("ptc", "No Data");?></div>                       
            </div>
            <!-- 教师列表 -->
            <div class="modal fade" id="teacherList" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title" ><?php echo Yii::t('attends','Select a Teacher') ?> <span id="teacherList_t"></span></h4>
                        </div>
                        <div class="form-horizontal">
                            <div class="modal-body" v-if='teacherPtcList.teacherData'>
                                <div v-if='teacherPtcList.teacherData.length==0'>
                                    <div class="alert alert-warning" role="alert" ><?php echo Yii::t("ptc", "No Data");?></div>  
                                </div>
                                <div v-else>
                                    <div v-if='teacherPtcList.teacherData[1]'>
                                        <p class='font14 color3'><strong><?php echo Yii::t("ptc", "ES");?></strong></p>
                                        <a v-for='(num,key,index) in teacherPtcList.teacherData[1]'  class='mb10 btn btn-default mr15' href='javascript:;' @click='showTeacher(key)'>
                                            {{teacherPtcList.teachersInfo[key].name}}<span class="badge ml10 bg7">{{num}}</span>
                                        </a>
                                    </div>
                                    <div v-if='teacherPtcList.teacherData[2]'>
                                        <p class='mt20 font14 color3'><strong><?php echo Yii::t("ptc", "SS");?></strong></p>
                                        <a v-for='(num,key,index) in teacherPtcList.teacherData[2]'  class='mb10 btn btn-default mr15' href='javascript:;' @click='showTeacher(key)'>
                                            {{teacherPtcList.teachersInfo[key].name}}<span class="badge ml10 bg7">{{num}}</span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 教师列表 -->
            <div class="modal fade" id="reserve" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title" ><?php echo Yii::t('ptc','Schedulable Time') ?> <span id="teacherList_t"></span></h4>
                        </div>
                        <div class="modal-body">
                            <div class="media pb10">
                                <div class="media-left pull-left media-middle">
                                    <img :src="teacherInfo.photo" data-holder-rendered="true" class="addImg">
                                </div> 
                                <div class="media-body media-middle">
                                    <div class="font18 mt15 color3">{{teacherInfo.name}}</div> 
                                </div>
                            </div>
                            <div class="flex mt20" v-if='reserveData.classData && reserveData.classData.length!=0'>
                                <label class="mt5 text-right"><?php echo Yii::t('ptc','Classes with ownership:') ?></label>
                                <div class="flex1 ml20">
                                    <span class='classTitle mr10' v-for='(list,key,i) in reserveData.classData'>{{list}}</span>
                                </div>
                            </div>
                            <div class='clearfix'></div>
                            <div class='table_wrap scrollbar  mt20'>
                                <table class="table" id='table' v-if='reserveData.scheduleList && reserveData.scheduleList.length!=0'>
                                    <tr>
                                        <th v-for='(list,key,index) in reserveData.scheduleList' class='font16 text-center'>
                                            <strong>{{key}}</strong> 
                                            <div class='font14 mt5 color6'>{{list.week}}</div>
                                        </th>
                                    </tr>
                                    <tr>
                                        <td v-for='(item,index) in reserveData.scheduleList'> 
                                            <div v-for='(_item,idx) in item.items' class='reserved mb20'>
                                                <div class='font14 color3 pb10 timeStart'>
                                                    <span class='glyphicon glyphicon-time mr10'></span> 
                                                    <strong>{{_item.start}}-{{_item.end}}</strong>
                                                </div>
                                                <div class='timeList'>
                                                    <div class='flex mt15 border'>
                                                        <span class='glyphicon glyphicon-ok-sign green pt5'></span>
                                                        <div class='flex1 ml10'>
                                                            <div class='color3 font14'>{{reserveData.classData[_item.class_id]}}</div>
                                                            <div v-for='(tea,keys,id) in reserveData.classTeacher[_item.class_id]' class='mt5'>
                                                                <img :src="reserveData.teachersInfo[tea].photoUrl" class='teacherImg mr10 pull-left' alt="">
                                                            </div>
                                                            <div class='clearfix'></div>
                                                        </div>
                                                    </div>
                                                </div> 
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                                <div v-else>
                                    <div class="alert alert-warning" role="alert"><?php echo Yii::t('ptc','No Data (This function is for ES only)') ?></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    var teacherId = '<?php echo  Yii::app()->request->getParam('teacherId', '')?>'
    var container = new Vue({
        el: "#container",
        data: {
            startYearList: "",
            startYear: "<?php echo  Yii::app()->request->getParam('startyear', '')?>",
            semester: "<?php echo  Yii::app()->request->getParam('semester', '')?>",
            semesterList:'',
            initData:{},
            startdate:'',
            teacherInfo:{},
            normalList:{},
            itemData:{},
            currentDate:'all',
            teacherTitle:'',
            teacherPtcList:{},
            reserveData:{},
            teacher_id:'',
            popoverData:{}
        },
		created(){
            if(teacherId!=''){
            this.myptcData(teacherId)

            }else{
                this.myptcData()
            }
		},
        methods: {
            html(data){
              return  data.replace(/\n/g, '<br/>')
            },
            myptcData(teacher_id){
                let that=this
                var listData={}
                if(teacher_id){
                    this.teacher_id=teacher_id
                    listData={
                        teacher_id:teacher_id,
                        startyear:that.startYear,
                        semester:that.semester,
                    }
                }else{
                    listData={
                        startyear:that.startYear,
                        semester:that.semester,
                    }
                }
				$.ajax({
                    url: '<?php echo $this->createUrl("ptcSchedule/myPtc") ?>',
                    type: "post",
                    dataType: 'json',
                    data:listData,
                    success: function(data) {
                        if(data.state=='success'){  
                            that.teacherInfo=data.data.teacherInfo
						    that.initData=data.data.ptcData
						    that.semesterList=data.data.semesterList
						    that.startYearList=data.data.startYearList
						    that.startYear=data.data.startYear
						    that.semester=data.data.semester
                            that.itemData=JSON.parse(JSON.stringify(data.data.ptcData.itemData))
                            that.$nextTick(()=>{
                                $("#startDate").datepicker({
                                    dateFormat: "yy-mm-dd ",
                                    onClose : function(dateText, inst){//当日期面板关闭后触发此事件（无论是否有选择日期）
                                        that.startdate = dateText
                                        that.myptcData()
                                    }
                                })
                            })
                        }else{ 
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            showDate(key){
                this.currentDate=key
                if(key=='all'){
                    this.itemData=this.initData.itemData
                }else{
                    this.itemData={}
                    this.itemData[key]=this.initData.itemData[key]
                }
            },
            chooseTeacher(){
                let that=this
                if(that.teacherPtcList.teacherData){
                    $('#teacherList').modal('show'); 
                    return
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("ptcSchedule/teacherPtcNum") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        semester:that.semester,
                        start_year:that.startYear,
                        yid:that.yid,
                    },
                    success: function(data) {
                        if(data.state=='success'){  
                            that.teacherPtcList=data.data.teacherPtcNum
                            $('#teacherList').modal('show'); 
                        }else{ 
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            showTeacher(id){
                this.teacherTitle=this.teacherPtcList.teachersInfo[id].name
                this.currentDate='all'
                this.myptcData(id)
                $("#teacherList").modal('hide');

            },
            time(){
                let that=this
                var teacher={}
                if(this.teacher_id!=''){
                    teacher={
                        teacher_id:this.teacher_id
                    }
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("ptcSchedule/teacherClassSchedule") ?>',
                    type: "post",
                    dataType: 'json',
                    data:teacher,
                    success: function(data) {
                        if(data.state=='success'){  
                            console.log(data)
                            that.reserveData=data.data
                            $('#reserve').modal('show'); 
                        }else{ 
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            showPopover(index,list,type){
                // if(this.popoverIndex===index){
                //     this.popoverIndex=''
                //     return
                // }
                if(list.score=='0'){
                    return
                }
                this.popoverIndex=index
                var len=Object.keys(this.initData.itemData).length
                for(var i=0;i<len;i++){
                    $('#sub'+i).hide()
                }
                var noTimeData=Object.keys(this.initData.noTimeData).length
                for(var i=0;i<noTimeData;i++){
                    $('#nosub'+i).hide()
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("ptcSchedule/mathDetail") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        score_ext:list.score_ext
                    },
                    success: function(data) { 
                        if(data.state=='success'){  
                            that.popoverData=data.data
                            if(type=='noTimeData'){
                                $('#nosub'+index).show()
                            }else{
                                $('#sub'+index).show()
                            }
                        }else{ 
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
                
            },
		}
    })
    $(document).click(function(event) {
        if(container.initData.itemData && container.initData.itemData){
            var len=Object.keys(container.initData.itemData).length
            for(var i=0;i<len;i++){
                $('#sub'+i).hide()
            }
        }
        if(container.initData.noTimeData && container.initData.noTimeData){
            var len=Object.keys(container.initData.noTimeData).length
            for(var i=0;i<len;i++){
                $('#nosub'+i).hide()
            }
        }
    });
</script>