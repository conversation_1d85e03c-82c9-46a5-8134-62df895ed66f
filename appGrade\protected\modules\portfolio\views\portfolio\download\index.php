<?php
$this->breadcrumbs=array(
    'Portfolio'=>array('/portfolio/portfolio/classes'),
    Yii::t("navigations",'Download Photos'),
);

$serverConfs = CommonUtils::LoadConfig('CfgMediaServer');
$qiniuConfig = $serverConfs['servers'][20];
?>

<div id="left-col" class="no-bg span-6">

</div>

<div id="main-col" class="span-18 last">
    <p class="note"><?php echo Yii::t('portfolio','Photo package will be generated within one hour and a notification email will be sent to you when it is ready for download.');?></p>
    <?php foreach ($ranges as $year=>$range):?>
        <h1><label>
                <?php echo sprintf('%d ~%d %s', $year, $year+1,
                    Yii::t('portfolio', 'School Year')); ?>
                </label></h1>

        <div class="download-list mb20">
            <ul>
                <?php foreach($packages[$year] as $item): ?>
                    <li>
                        <span>
                            <?php echo $item['label'];?>
                        </span>
                        <em>
                            <?php
                            //已打包
                            if($item['uploaded_at'] && $item['zipurl']): ?>

                                <?php if (Yii::app()->user->checkAccess('fullAccessChild', array("childid" => $this->childid))) :?>
                                    <?php
//                                    echo CHtml::link(Yii::t("portfolio", 'Download Tool (:number)',
//                                            array(':number'=> $item['pcount'])),
//                                            array('getfile', 'dlpath'=>$item['dlpath']),
//                                            array("class"=>"download"));
                                    $downloadUrl = $qiniuConfig['url'].$item['zipurl'];
                                    echo CHtml::link(Yii::t("portfolio", 'Download (:number)', array(':number'=> $item['pcount'])), $downloadUrl, array("class"=>"download"));
                                    ?>
                                <?php else: ?>
                                    <?php echo CHtml::link(Yii::t("portfolio", 'Download (:number)',
                                        array(':number'=> $item['pcount'])),
                                        'javascript:(void);', array("class"=>"download","onclick"=>'alert("No permission"+"\r\n"+"没有权限");'))?>
                                <?php endif; ?>

                            <?php
                            //已提交；尚未运行命令
                            elseif(isset($item['pack_time']) && $item['zipurl'] == ""): ?>
                                <?php
                                echo CHtml::openTag('span', array("title"=>Yii::t("portfolio","An email notification will be sent to you shortly when they are ready for download.")));
                                echo Yii::t("portfolio", 'photo list is being packaged.');
                                echo CHtml::closeTag('span');
                                ?>

                            <?php
                            //未打包
                            else:?>

                                <?php if (Yii::app()->user->checkAccess('adminChild', array("childid" => $this->childid))) :?>
                                    <?php echo CHtml::openTag('span', array('id'=>$year.'_'.$item['s']))?>
                                    <?php echo CHtml::ajaxLink(Yii::t("portfolio", 'Package Photos'), $this->createUrl("//portfolio/portfolio/packpic", array(
                                        "startyear" => $year,
                                        "sweek" => $item['s'],
                                        "eweek" => $item['e'],
                                    )),
                                        array(
                                            "success" => "function(data, textStatus, jqXHR){callback(data,'reload')}",
                                            "beforeSend" => "function(){ $('#".$year."_".$item['s']."').html('<img src=\"".Yii::app()->theme->baseUrl."/images/loading.gif\">') }"
                                        ),
                                        array("class"=>"package")
                                    )
                                    ?>
                                    <?php echo CHtml::closeTag('span');?>
                                <?php else:?>
                                    <?php echo CHtml::link(Yii::t("portfolio", 'Package Photos'), 'javascript:(void);', array("class"=>"download","onclick"=>'alert("No permission"+"\r\n"+"没有权限");'))?>
                                <?php endif;?>

                            <?php endif; ?>
                        </em>
                    </li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endforeach;?>
</div>