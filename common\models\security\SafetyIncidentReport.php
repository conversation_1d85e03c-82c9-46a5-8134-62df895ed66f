<?php

/**
 * This is the model class for table "ivy_safety_incident_report".
 *
 * The followings are the available columns in table 'ivy_safety_incident_report':
 * @property integer $id
 * @property string $school_id
 * @property string $incident_name
 * @property integer $incident_date
 * @property string $incident_place
 * @property integer $reported_by
 * @property integer $reported_position
 * @property integer $reported_date
 * @property integer $level
 * @property integer $incident_type
 * @property string $incident_title
 * @property integer $child_id
 * @property integer $class_id
 * @property string $parent_mood
 * @property integer $involved_teacher
 * @property integer $staff
 * @property integer $staff_position
 * @property string $injured_part
 * @property string $incident_desc
 * @property string $incident_process
 * @property integer $hospital
 * @property integer $media_in
 * @property integer $government_in
 * @property integer $edu_in
 * @property integer $staff_in
 * @property string $immediate_case
 * @property string $primary_case
 * @property string $immediate_action
 * @property string $next_action
 * @property integer $status
 * @property integer $created_at
 * @property integer $created_by
 * @property integer $updated_at
 * @property integer $updated_by
 */
class SafetyIncidentReport extends CActiveRecord
{
    public $injured_part_child;
    public $injured_part_teacher;
    public $minute;
    public $month;

    const STATUS_ACTIVE = 1;
    const STATUS_CHECK = 2;
    const STATUS_INVALID = 9999;
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_safety_incident_report';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('incident_name, incident_date, incident_place, reported_by, reported_position, reported_date, level, incident_type, incident_title, incident_desc, next_action, status, created_at, created_by, updated_at, updated_by', 'required'),
			array('incident_date, reported_by, reported_position, reported_date, level, incident_type, child_id, class_id, involved_teacher, staff, staff_position, hospital, media_in, government_in, edu_in, staff_in, status, created_at, created_by, updated_at, updated_by', 'numerical', 'integerOnly'=>true),
			array('school_id, incident_name, incident_place, incident_title, parent_mood, injured_part, incident_desc, incident_process, immediate_case, primary_case, immediate_action, next_action', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, school_id, incident_name, incident_date, incident_place, reported_by, reported_position, reported_date, level, incident_type, incident_title, child_id, class_id, parent_mood, involved_teacher, staff, staff_position, injured_part, incident_desc, incident_process, hospital, media_in, government_in, edu_in, staff_in, immediate_case, primary_case, immediate_action, next_action, status, created_at, created_by, updated_at, updated_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'class' => array(self::HAS_ONE, 'IvyClass', array('classid' => 'class_id')),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'school_id' => Yii::t('safety','School'),
			'incident_name' => Yii::t('safety','Incident Name'),
			'incident_date' => Yii::t('safety','Incident Date'),
			'incident_place' => Yii::t('safety','Place of the incident'),
			'reported_by' => 'Reported By',
			'reported_position' => 'Reported Position',
			'reported_date' => 'Reported Date',
			'level' => 'Level',
			'incident_type' => 'Incident Type',
			'incident_title' => 'Incident Title',
			'child_id' => 'Child',
			'class_id' => 'Class',
			'parent_mood' => 'Parent Mood',
			'involved_teacher' => 'Involved Teacher',
			'staff' => 'Staff',
			'staff_position' => 'Staff Position',
			'injured_part' => 'Injured Part',
			'incident_desc' => 'Incident Desc',
			'incident_process' => 'Incident Process',
			'hospital' => 'Hospital',
			'media_in' => 'Media In',
			'government_in' => 'Government In',
			'edu_in' => 'Edu In',
			'staff_in' => 'Staff In',
			'immediate_case' => 'Immediate Case',
			'primary_case' => 'Primary Case',
			'immediate_action' => 'Immediate Action',
			'next_action' => 'Next Action',
			'status' => Yii::t('safety','status'),
			'created_at' => 'Created At',
			'created_by' => 'Created By',
			'updated_at' => 'Updated At',
			'updated_by' => 'Updated By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('school_id',$this->school_id,true);
		$criteria->compare('incident_name',$this->incident_name,true);
		$criteria->compare('incident_date',$this->incident_date);
		$criteria->compare('incident_place',$this->incident_place,true);
		$criteria->compare('reported_by',$this->reported_by);
		$criteria->compare('reported_position',$this->reported_position);
		$criteria->compare('reported_date',$this->reported_date);
		$criteria->compare('level',$this->level);
		$criteria->compare('incident_type',$this->incident_type);
		$criteria->compare('incident_title',$this->incident_title,true);
		$criteria->compare('child_id',$this->child_id);
		$criteria->compare('class_id',$this->class_id);
		$criteria->compare('parent_mood',$this->parent_mood,true);
		$criteria->compare('involved_teacher',$this->involved_teacher);
		$criteria->compare('staff',$this->staff);
		$criteria->compare('staff_position',$this->staff_position);
		$criteria->compare('injured_part',$this->injured_part,true);
		$criteria->compare('incident_desc',$this->incident_desc,true);
		$criteria->compare('incident_process',$this->incident_process,true);
		$criteria->compare('hospital',$this->hospital);
		$criteria->compare('media_in',$this->media_in);
		$criteria->compare('government_in',$this->government_in);
		$criteria->compare('edu_in',$this->edu_in);
		$criteria->compare('staff_in',$this->staff_in);
		$criteria->compare('immediate_case',$this->immediate_case,true);
		$criteria->compare('primary_case',$this->primary_case,true);
		$criteria->compare('immediate_action',$this->immediate_action,true);
		$criteria->compare('next_action',$this->next_action,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('created_at',$this->created_at);
		$criteria->compare('created_by',$this->created_by);
		$criteria->compare('updated_at',$this->updated_at);
		$criteria->compare('updated_by',$this->updated_by);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return SafetyIncidentReport the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
