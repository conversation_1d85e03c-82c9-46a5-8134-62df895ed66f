<style>
    .item-count{
        color: #ffffff;
        background-color: #5b5353;
    }
    .active .item-count{
        color: #337ab7;
        background-color: #fff;
    }
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Product list'), array('//mcampus/product/index')) ?></li>
    </ol>

    <div class="row">
        <!-- 左侧菜单 -->
        <div class="col-md-2 col-sm-2 mb10">
            <a class="btn btn-primary J_modal mb5" href="<?php echo $this->createUrl('productsCat'); ?>">
                <span class="glyphicon glyphicon-plus" aria-hidden="true"></span>
                <?php echo Yii::t("site", "Add product classification"); ?>
            </a>
            <div></div>
            <ul class="nav nav-pills nav-stacked background-gray" id="pageCategory">
                <?php
                $title = "";
                $desc = "";
                    if($productsCatObj){
                        foreach ($productsCatObj as $item) {
                            $active = "";
                            if($item->id == $pcid){
                                $title = $item->getName();
                                $desc = $item->getDesc();
                                $school = $item->school_id;
                                $active = "active";
                            }
                            echo "<li class=" . $active . ">" . CHtml::link("<div class='flex align-items'><span class='flex1'>".CHtml::encode($item->getName())."</span>"."<span style='width:40px;text-align:right'><span class='item-count badge'>".$productOrderCount[$item->id]."</span></span></div>", array('index', 'pcid' => $item->id, "branchId"=>Yii::app()->controller->branchId)) . '</li>';

                        }
                    }
                ?>
            </ul>
        </div>

        <?php if($pcid){ ?>
            <div class="col-md-10 col-sm-12">
                <div class="page-header">
                    <h2>
                        <?php echo CHtml::encode($title); ?>
                        <?php
                        if($school == $schoolId): ?>
                        <a class="btn btn-default J_modal" href="<?php echo $this->createUrl('productsCat', array('pcid' => $pcid)); ?>">
                            <span class="glyphicon glyphicon-edit" aria-hidden="true" title="<?php echo Yii::t('site', 'Edit basic information'); ?>"></span>
                            <?php echo Yii::t('site', 'Edit basic information'); ?>
                        </a>
                        <a class="btn btn-default J_modal" href="<?php echo $this->createUrl('schoolShared', array('cid' => $pcid)); ?>">
                            <span class="glyphicon glyphicon-edit" aria-hidden="true" title="<?php echo Yii::t('site', 'Cross-campus sharing'); ?>" ></span>
                            <?php echo Yii::t('site', 'Cross-campus sharing'); ?>
                        </a>
                        <?php endif; ?>
                        <div class="pull-right">
                            <a class="btn btn-primary" target="_blank" href="<?php echo $this->createUrl('product2/showConfirmOrder', array('pcid' => $pcid, 'status' => 20)); ?>">
                                <?php echo Yii::t("site", "订单管理"); ?>
                                <span class="badge"><?php echo ($invoiceCont) ? $invoiceCont : "" ; ?></span>
                            </a>
<!--                            <a class="btn btn-primary" target="_blank" href="--><?php //echo $this->createUrl('showConfirmOrder', array('pcid' => $pcid, 'status' => 20)); ?><!--">-->
<!--                                --><?php //echo Yii::t("site", "订单管理"); ?>
<!--                                <span class="badge">--><?php //echo ($invoiceCont) ? $invoiceCont : "" ; ?><!--</span>-->
<!--                            </a>-->
                        </div>
                    </h2>
                    <div>  <?php echo CHtml::encode($desc); ?></div>
                    <?php if(!empty($catShare)){?>
                    <div class="mt10 mb10">
                        <?php if (!empty($catShare->share_stock)){?>
                            <span class="label bg-success" style="display:inline-block;height:20px;line-height:16px;text-align: center;color: #5CB85C;font-size: 12px;padding:2px 6px;margin-right: 12px;font-weight: unset">共享库存</span>
                        <?php }?>
                        <?php if (!empty($catShare->share_featured)){?>
                            <span class="label bg-success" style="display:inline-block;height:20px;line-height:16px;text-align: center;color: #5CB85C;font-size: 12px;padding:2px 6px;margin-right: 12px;font-weight: unset">共享推荐</span>
                        <?php }?>
                    </div>
                    <?php }?>
                </div>

                <div class="clearfix mb10">
                    <?php if($school == $schoolId): ?>
                    <a class="btn btn-primary J_modal" href="<?php echo $this->createUrl('updateProducts', array('pcid' => $pcid)); ?>">
                        <span class="glyphicon glyphicon-plus" aria-hidden="true"></span>
                        <?php echo Yii::t("site", "Add product"); ?>
                    </a>
                    <?php endif; ?>
                        <a href="javascript:;" class="btn btn-primary  pull-right" style="margin-left: 10px" onclick="exportRefund('<?php echo $this->createUrl('PrintOutOfStock',array('cid' => $pcid)) ?>')">
                            导出缺货登记
                        </a>
<!--                    --><?php //if (empty($catShare->share_stock)){?>
                        <a href="javascript:;" class="btn btn-primary  pull-right" onclick="exportRefund('<?php echo $this->createUrl('PrintStock',array('cid' => $pcid)) ?>')">
                            导出库存
                        </a>
<!--                    --><?php //}?>
                    </div>

                <div class="col-md-12"></div>
                <div class="panel panel-default">
                    <div class="panel-body">
                        <?php
                        $this->widget('ext.ivyCGridView.BsCGridView', array(
                            'id'=>'confirmed-visit-grid',
                            'afterAjaxUpdate'=>'js:head.Util.modal',
                            'dataProvider'=>$dataProvider,
                            'template'=>"{items}{pager}{summary}",
                            'colgroups'=>array(
                                array(
                                    "colwidth"=>array(200,200,100,100,100,100,100,200),
                                )
                            ),
                            'columns'=>array(
                                array(
                                    'name'=> Yii::t('asainvoice','Title Cn'),
                                    'type'=>'raw',
                                    'value'=>'$data->title_cn',
                                ),
                                array(
                                    'name'=> Yii::t('asainvoice','Title En'),
                                    'type'=>'raw',
                                    'value'=>'$data->title_en',
                                ),
                                array(
                                    'name'=> Yii::t('user','Price'),
                                    'type'=>'raw',
                                    'value'=>'$data->unit_price',
                                ),
                                array(
                                    'name'=> Yii::t('user','划线价格'),
                                    'type'=>'raw',
                                    'value'=>'$data->scribing_price',
                                ),
                                array(
                                    'name'=> Yii::t('user','缺货登记'),
                                    'type'=>'raw',
                                    'value'=> array($this, 'getReserveCount'),
                                ),
                                array(
                                    'name'=> Yii::t('curriculum','State'),
                                    'type'=>'raw',
                                    'value'=> array($this, 'getStatus'),
                                ),
                                array(
                                    'name'=> Yii::t('labels','Created Time'),
                                    'type'=>'raw',
                                    'value'=>'date("Y-m-d H:i", $data->updated_time)',
                                ),

                                array(
                                    'name'=> Yii::t('srbac','Operations'),
                                    'value'=> array($this, 'getButton'),
                                ),
                            ),
                        ));
                        ?>
                    </div>
                </div>
            </div>
        <?php } ?>
    </div>
</div>
<div class="modal fade" tabindex="-1" role="dialog" id='sort'>
    <div class="modal-dialog" role="document">
        <div class="modal-content">
        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title"><?php echo Yii::t("campus", "Sort"); ?></h4>
        </div>
        <div class="modal-body">
            <div class='sortImgList ui-sortable' v-if='showFalse'>
                <div v-for="(item,index) in photo"  class="sortList"  :data-id="item.id">
                    <img :src="item.img" class='sortImg'>
                    <a role="button" class="btn btn-default btn-xs sort-header mr5"
                                               style="cursor: move;background: #eee"><span
                                                    class="glyphicon glyphicon-move"></span></a>
                </div>
            </div>
         
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
            <button type="button" class="btn btn-primary" onclick='saveSort()'><?php echo Yii::t("global", "Save"); ?></button>
        </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
<style>
    .sortImg{
        max-height:100px
    }
    .sortList{
        margin:16px 0
    }
</style>
<script>
     $(function(){
        $('#main-edit-zone').find('.edit-title').html($('#user-profile-item li.active a').html());
    })
    $(document).ready(function () {
    // 通过该方法来为每次弹出的模态框设置最新的zIndex值，从而使最新的modal显示在最前面
        $(document).on('shown.bs.modal', '.modal.in', function(event) {
            var zIndex = 1040 + (10 * $('.modal:visible').length);
                $(this).css('z-index', zIndex);
                setTimeout(function() {
                    $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');
                }, 0);
        }).on('hidden.bs.modal', '.modal', function(event) {
            if ($('.modal.in').size() >= 1) {
                $('body').addClass('modal-open')
            }
        })
    });
    var modal = '<div class="modal fade" id="modal" class="1123" tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog  modal-lg" role="document"><div class="modal-content"></div></div></div>';
    $('body').append(modal);

    function cbSuccess() {
        location.reload();
    }
    function exportRefund(url) {
        $.ajax({
            url: url,
            type: 'POST',
            success: function (res) {
                if (res.state == 'success') {
                    var data = res.data.items;
                    const filename = res.data.title;
                    const ws_name = "SheetJS";
                    if(data.length<=1){
                        alert('<?php echo Yii::t('asa','No data found.'); ?>');
                        return false;
                    }
                    const worksheet = XLSX.utils.aoa_to_sheet(data);
                    const workbook = XLSX.utils.book_new();
                    XLSX.utils.book_append_sheet(workbook, worksheet, ws_name);
                    // generate Blob
                    const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                    const blob = new Blob([wbout], {type: 'application/octet-stream'});
                    // save file
                    let link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    link.download = filename;
                    link.click();
                    setTimeout(function() {
                        // 延时释放掉obj
                        URL.revokeObjectURL(link.href);
                        link.remove();
                    }, 500);
                }
            },
            dataType: 'json'
        });
    }
    
     var container = new Vue({
         el: "#sort",
         data:{
             photo:'',
             sort:{},
             showFalse:false
         },
     })
    function saveSort(){
        var pid = $("#photo").attr('data-pid');
        $.ajax({
             url: '<?php echo $this->createUrl("setPhotoSort") ?>',
             type: "post",
             dataType: 'json',
             data: {
                 data: container.sort
             },
             success: function(data) {
                if (data.state == 'success') {
                    resultTip({msg: data.state});
                    $("#photo_"+pid).click();
                    $('#sort').modal('hide')
                    container.showFalse=false
                }else{
                    resultTip({error: 'warning',msg: data.message});
                }
             },
             error:function(data){
                resultTip({error: 'warning',msg: data.message});
             }
         })
    }
     $('#sort').on('show.bs.modal', function (e) {
         var pid = $("#photo").attr('data-pid');
         $.ajax({
             url: '<?php echo $this->createUrl("getPhoto") ?>',
             type: "post",
             dataType: 'json',
             data: {
                 pid:pid
             },
             success: function(data) {
                if (data.state == 'success') {
                    container.photo = data.data
                    container.showFalse=true
                    container.$nextTick(()=>{
                        $( ".sortImgList" ).sortable({
                            placeholder: "ui-state-highlight",
                            handle: ".sort-header",
                            axis: 'y',
                            opacity: 0.8,
                            start: function( event, ui ) {
                                $('.ui-state-highlight').height($(ui['item'][0]).outerHeight()).css("list-style-type", "none");
                            },
                            update: function(event, ui){
                                var sort = [];
                                $('.sortImgList').find('.sortList').each(function () {
                                    sort.push($(this).data('id'))
                                });
                                container.sort = sort
                            }
                        }).disableSelection();
                    })
                }else{
                    resultTip({error: 'warning',msg: data.message});
                }
             },
             error:function(data){
                 resultTip({error: 'warning', msg: data.message});
             }
         })
     })
</script>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>