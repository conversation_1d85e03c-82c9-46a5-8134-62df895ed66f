<style>
    .borderBto{
        border-bottom:1px solid #E8EAED
    }
    .content{
        background: #FFFFFF;
        box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.16);
        border-radius: 4px 4px 0px 0px;
    }
    .bg<PERSON>rey{
        background: #FAFAFA;
        border-radius: 4px;
        padding:10px;
        border: 1px solid #FAFAFA;

    }
    .bgGrey:hover{
        background:rgba(77, 136, 210, 0.1000);
        border: 1px solid #4D88D2;
    }
    .closeFile{
        display:none
    }
    .bgGrey:hover .closeFile{
        display:block
    }
    .blue{
        display: inline-block;
        width: 4px;
        height: 14px;
        background: #4D88D2;
        position: absolute;
        left: 0;
        top: 5px;
    }
    .border{
        border: 1px solid #E8EAED;
        border-radius: 4px;
    }
    .contentAvatar{
        width: 44px;
        height: 44px;
        border-radius: 50%;
        object-fit:cover;
    }
    textarea{
        border:none !important
    }
    .image {
        width: 32px;
        height: 32px;
        object-fit: cover;
    }
    .closeChild {
        width: 16px;
        height: 16px;
        display: inline-block;
        border: 1px solid #999999;
        text-align: center;
        line-height: 15px;
        border-radius: 50%;
        color: #999999;
    }
    .closeChild:hover{
        color:red;
        border: 1px solid red;
    }
    .childList {
        max-height: 250px;
        min-height: 40px;
        padding:14px;
        overflow-y:auto
    }
    .asaGroupList {
        padding-left: 10px;
        padding-right: 10px;
        max-height: 400px;
        min-height: 40px;
        overflow-y:auto
    }
    .addChild {
        font-size: 17px;
        color: #409EFF;
        width: 16px;
        height: 16px;
        display: inline-block;
        border: 1px solid #409EFF;
        text-align: center;
        line-height: 15px;
        border-radius: 50%;
    }
    .allCheck {
        position: absolute;
        right: 10px;
        top: 4px;
    }
    .color606 {
        color: #606266;
        line-height: 30px;
    }
    .lineHeight {
        line-height: 32px;
    }
    .is-checked {
        border-color: #4D88D2;
    }
    .clone{
        padding:2px 12px;
        background: #F0AD4E;
        border-radius:  100px  0px 0px 100px;
        color:#fff;
        position: absolute;
        right: -24px;
    }
    .box{
        padding:0 15px
    }
    .box-active{
        position: fixed;
        top: 50px;
        width: 100%;
        background: #fff;
        z-index: 999;

        padding:20px 15px 0 15px
    }
    .box-shadow{
        box-shadow: 0px 4px 6px 0px rgb(0 0 0 / 10%);
    }
    .classList{
        padding:14px
    }
    .iconImg{
        width: 20px;
        height: 20px;
        float: left;
    }
    .textarea{
        font-family: PingFangSC-Medium, PingFang SC;
        font-size:20px;

    }
    #textarea{
        resize:none;
        padding:24px 0;
        color:#333;
        overflow:hidden
    }
    .yellow{
        color:#F0AD4E
    }
    .uploadImg{
        position: absolute;
        right: 10px;
        z-index: 998;
        top: 15px;
    }
    .tagLabel{
        background:#EBEDF0;
        font-size:100%
    }
    [v-cloak] {
        display: none;
    }
    .progress{
        position: absolute;
        bottom: 0;
        width: 100%;
        margin: 0;
        height: 4px;
        border-radius: 0px 2px 2px 0px;
    }
    .optionSearch{
        height:auto
    }
    .imageSearch{
        width: 44px;
        height: 44px;
        object-fit: cover;
    }
    .text_overflow {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        line-height: 14px;
    }
    .menuLeft{
        left:auto;
        right:0 !important
    }
    #testSpanForCheck{
        font-family: PingFangSC-Medium, PingFang SC;
    }
    .inputSelect{
        width:300px;
    }
    .el-input__inner:focus{
        border-color: #428bca;
        box-shadow: 0px 1px 1px rgba(0, 0, 0, 0.075) inset, 0px 0px 8px rgba(64, 158, 255, 0.5)
    }
    .el-input__inner{
        height:34px;
        line-height:34px;
        padding: 6px 12px;
        font-size: 14px;
        color: #555;
        background-color: #fff;
        background-image: none;
        border: 1px solid #ccc;
        border-radius: 4px;
        transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s,-webkit-box-shadow ease-in-out .15s;
    }
    .listMedia{
        padding:8px;
        border: 1px solid #fff;
        max-height:62px;
        margin:0
    }
    .listMedia:hover{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        border: 1px solid #4D88D2;
        cursor: pointer;
    }
    .image42{
        width: 42px;
        height: 42px;
        object-fit: cover;
    }
    .el-input__prefix, .el-input__suffix{
        top:-2px
    }
    
    .el-scrollbar .el-scrollbar__bar {
    opacity: 1 !important;
    }
    .newIcon{
        background: red !important;
        position: absolute !important;
        right: -16px !important;
        top: -10px !important;
        height: 15px !important;
        line-height: 12px !important;
        border-radius: 40px !important;
        color: #fff !important;
        font-size: 12px !important;
        padding: 0 2px !important;
    }
    .tox .tox-tbtn{
        overflow: inherit !important;
    }
    .tox .borderInsert{
        max-width:100px !important;
    }
    .tox-editor-container .tox-promotion,.tox-statusbar .tox-statusbar__branding ,.tox .tox-toolbar-nav-js{
    display: none !important;
    }
</style>
<div  id='container' v-cloak>
    <div v-if='Object.keys(journalContent).length!=0'>
        <div class='box-active'>
            <p class='font14 borderBto pb24'><a href="<?php echo $this->createUrl('index'); ?>" ><span class="glyphicon glyphicon-chevron-left font12"></span> <?php echo Yii::t('newDS','Direct Message');?></a> | <span><?php echo Yii::t("directMessage", "Create a DM"); ?></span> </p>
            <div class='flex mt12 mb12 align-items'>
                <div class='flex1'><img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/new.png' ?>" class='iconImg mr8' alt="" ><strong class='color3 font16'>{{!journalContent.is_new?'<?php echo Yii::t("directMessage", "DM Editing"); ?>':'<?php echo Yii::t("directMessage", "Create a DM"); ?>'}}</strong> </div>
                <div class=''>
                    <!-- <el-switch
                        v-model="autoSave"
                        @change='autoSaveContent'
                        active-text="自动保存">
                    </el-switch> -->
                    <el-button type="button" v-if='journalContent.status!=1' class='btn btn-primary ml16' size="small"  @click='saveData()'><span class='el-icon-document'></span> <?php echo Yii::t("directMessage", "Save draft"); ?></el-button>
                    <el-button type="button" v-else class='btn btn-primary ml16' size="small"  @click='saveData("update")'><?php echo Yii::t('global','Update');?></el-button>
                    <div class="btn-group ml16"  v-if='journalContent.status==1'>
                        <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <?php echo Yii::t("directMessage", "Make Online"); ?> <span class="caret"></span>
                        </button>
                        <ul class="dropdown-menu menuLeft">
                            <li><a href="javascript:;" @click='assignTime("edit")'><?php echo Yii::t("directMessage", "Modify publish time"); ?></a></li>
                            <li><a href="javascript:;" @click='unpublish("cancel")'><?php echo Yii::t("directMessage", "Make offline"); ?></a></li>
                        </ul>
                    </div>
                    <div class="btn-group ml16" v-else >
                        <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <?php echo Yii::t("directMessage", "Make Online"); ?> <span class="caret"></span>
                        </button>
                        <ul class="dropdown-menu menuLeft">
                            <li><a href="javascript:;" @click='publishConfirm()'><?php echo Yii::t('directMessage','Make online now');?> </a></li>
                            <li><a href="javascript:;" @click='assignTime("new")'><?php echo Yii::t("directMessage", "Specify online time"); ?></a></li>
                        </ul>
                    </div>
                    <el-button size="small" class='ml16' @click='delJournal("del")' v-if='!journalContent.is_new'><?php echo Yii::t('global','Delete');?></el-button>
                </div>
            </div>
        </div>

        <div class='ml15' style='margin-top:120px'>
            <div class="col-md-8 col-sm-12 content p24" >
                <div class='flex relative mb10'>
                    <span class='flex1'>
                    <span  class='label label-default tagLabel  color6'><?php echo Yii::t("newDS", "School Year");?>：{{journalContent.start_year}} - {{parseInt(journalContent.start_year)+1}}</span>
                    </span>
                    <span ><span v-if='isClone==1' class='clone'><?php echo Yii::t("directMessage", "You are cloning a DM"); ?></span> </span>
                </div>
                <div  class=''>
                    <el-input
                        type="textarea"
                        :rows="linesCount"
                        placeholder="<?php echo Yii::t("directMessage", "Input title"); ?>"
                        class='borderBto textarea'
                        id='textarea'
                        v-model="journalContent.title">
                    </el-input>
                    <div id='testSpanForCheck' class='font20' style="line-height:30px;display:none" v-html='textareaHtml'></div>
                </div>
                
                <div  class='borderBto'>
                    <div class='flex align-items mb12 mt12'>
                        <div class='flex1'>
                            <span class='font14 color3'><?php echo Yii::t("newDS", "Attachments");?></span>
                            <el-button type="text" class='text-primary ml24' icon="el-icon-upload2" id='pickfilesPhoto' :disabled='disabledUpload'>{{!disabledUpload?'<?php echo Yii::t("newDS", "Add");?>':'<?php echo Yii::t("newDS", "Uploading...");?>'}}</el-button>
                        </div>
                        <div class='color6'><?php echo Yii::t("directMessage", "Pictures and videos are strongly suggested to be embed instead of listed in attachements"); ?></div>
                    </div>
                    <div>
                        <div class='col-md-4 col-sm-4 mb24' v-for='(list,index) in journalContent.attachments'>
                            <div class="bgGrey flex align-items" >
                                <div class='flex1 nowrap text-primary font14'>
                                    <a target="_blank"  class='flex1' style='line-height:26px' :href='list.file_key' ><span class='glyphicon glyphicon-paperclip'></span><span class='ml4'>{{list.title}}</span></a>
                                </div>
                                <div style='width:20px'><span class="glyphicon glyphicon-trash font16 closeFile cur-p"  @click='delFile(list,index)'></span></div>
                            </div>
                        </div>
                        <div class='col-md-4 col-sm-4 mb24' v-if='progressData.name'>
                            <div class="relative" >
                                <div class="bgGrey flex align-items">
                                    <div class='flex1 nowrap text-primary font14'>
                                        <a target="_blank"  class='flex1' style='line-height:26px' ><span class='glyphicon glyphicon-paperclip'></span><span class='ml4'>{{progressData.name}} </span></a>
                                    </div>
                                    <div style=''><?php echo Yii::t("newDS", "Uploading...");?></div>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar" id='progress' role="progressbar" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100" style="width:0%;">
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class='clearfix'></div>
                    </div>
                </div>
                <div class='mt24 relative'>
                    <el-popover
                        placement="top"
                        width="270"
                        class='uploadImg'
                        trigger="hover">
                            <div >
                                <?php echo sprintf(Yii::t("newDS", "From Media Repository: "), $this->createUrl('/mteaching/weekly/index'));?>
                                <br>
                                <?php echo sprintf(Yii::t("newDS", "Click <a target='_blank'  href='%s'>here</a> to upload photos or videos, then insert files by clicking the gallery icon."), $this->createUrl('/mteaching/weekly/index'));?>
                                <div class='mt16'>
                                    <?php echo Yii::t("newDS", "Direct Upload: ");?>
                                </div>
                                <?php echo Yii::t("newDS", "Just drag images to editor area (Images Only).");?>
                            </div>
                            <span slot="reference" class='yellow font14'><span class='el-icon-info mr8'></span> <?php echo Yii::t("newDS", "How to insert a photo or video?");?> </span>
                    </el-popover>
                    <!-- <input id="tinymceCn" type="textarea" v-model='journalContent.content'> -->
                    <textarea id="tinymceCn"  v-model='journalContent.content'></textarea>
                </div>
            </div>
            <div class="col-md-4 col-sm-12" >
                <div class='border '>
                    <div class='mt24 relative color3 font14'><span class='blue'></span><label class='ml12'><?php echo Yii::t("directMessage", "Signature"); ?></label> </div>
                    <div class='flex font14 color9'>
                        <span class='el-icon-info ml12 mt2'></span>
                        <span class='flex1 ml8'><?php echo Yii::t("newDS", "This photo is for parent view and mangaged by HR. If the photo is not uploaded or mis-uploaded, please contact HR.");?></span>
                    </div>
                    <div>
                        <div class="m24 border" :class='journalContent.sign_as_uid==journalData.userid?"is-checked":""'  >
                            <label class='flex align-items'>
                                <input type="radio" v-model='journalContent.sign_as_uid' class='ml16 mr16' :value="journalData.userid">
                                <div class="media mt12 mb4 flex1">
                                    <div class="media-left pull-left media-middle">
                                        <img :src="journalData.userInfo[journalData.userid].photoUrl" data-holder-rendered="true" class="contentAvatar">
                                    </div>
                                    <div class="media-body media-middle">
                                        <div class="lineHeight20 text-primary">
                                            <span class="font14 color3 nowrap">{{journalData.userInfo[journalData.userid].name}}</span>
                                        </div>
                                        <div class="font12 color6 nowrap">{{journalData.userInfo[journalData.userid].hrPosition}}</div>
                                    </div>
                                </div>
                            </label>
                        </div>
                        <div v-if='journalData.signAsUidList.length!=0'>
                            <div class="m24 border" v-for='(list,index) in journalData.signAsUidList' :class='journalContent.sign_as_uid==list?"is-checked":""'>
                                <label class='flex align-items'>
                                    <input type="radio" v-model='journalContent.sign_as_uid'  class='ml16 mr16' :value="list"  >
                                    <div class="media mt12 mb4 flex1">
                                        <div class="media-left pull-left media-middle">
                                            <img :src="journalData.userInfo[list].photoUrl" data-holder-rendered="true" class="contentAvatar">
                                        </div>
                                        <div class="media-body pt4 media-middle">
                                            <div class="lineHeight20 text-primary">
                                                <span class="font14 color3 nowrap">{{journalData.userInfo[list].name}}</span>
                                            </div>
                                            <div class="font12 color6 nowrap">{{journalData.userInfo[list].hrPosition}}</div>
                                        </div>
                                    </div>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class='border mt24'>
                    <div class='mt24 relative color3 font14'><span class='blue'></span><label class='ml12'><?php echo Yii::t("newDS", "Subscribers");?></label> <span  class='label label-default tagLabel  color6'><?php echo Yii::t("newDS", "Student");?></span></div>
                    <div class='flex font14 color9'>
                        <span class='el-icon-info ml12 mt2'></span>
                        <span class='flex1 ml8'><?php echo Yii::t("directMessage", "Notifications will be pushed to newly added subscribers, and each subscriber will be notified only once."); ?></span>
                    </div>
                    <div class='m24 flex'>
                        <div class='flex1' >
                            <el-button  type="button" class='btn btn-primary' icon="el-icon-plus" size='small' @click='addChild'><?php echo Yii::t("ptc", "Add student");?></el-button>
                        </div>

                        <el-button type="text" @click='batchDel("clear")' class='text-primary' v-if='Object.keys(postObjectList).length!=0'><?php echo Yii::t("directMessage", "Clear All"); ?>（{{Object.keys(postObjectList).length}}）</el-button>
                    </div>
                    <div style='max-height:200px;' class='overflow-y'  v-if='Object.keys(postObjectList).length!=0'>
                        <div class='col-md-6 col-sm-6 mb24' v-for='(list,index) in postObjectList'>
                            <div class="media bgGrey">
                                <div class="media-left pull-left media-middle">
                                    <a href="javascript:void(0)">
                                        <img :src="list.avatar" data-holder-rendered="true" class="contentAvatar">
                                    </a>
                                </div>
                                <div class="media-right pull-right pt12 text-right"><span class="el-icon-circle-close font16" @click='Unassign(list.id,index)'></span></div>
                                <div class="media-body pt4 media-middle">
                                    <div class="lineHeight20 text-primary"><span class="font14 color3 nowrap">{{list.name}}</span></div>
                                    <div class="font12 color6 nowrap">{{list.class_name}}</div>
                                </div>
                            </div>
                        </div>
                        <div class='clearfix'></div>
                    </div>

                </div>
                <div class='border mt24'>
                    <div class='mt24 relative color3 font14'><span class='blue'></span><label class='ml12'><?php echo Yii::t("directMessage", "Parent Feedback Collaborators"); ?></label> <span class='label label-default tagLabel  color6'><?php echo Yii::t("directMessage", "User"); ?></span></div>
                    <div class='flex font14 color9'>
                        <span class='el-icon-info ml12 mt2'></span>
                        <span class='flex1 ml8'><?php echo Yii::t("directMessage", "All collaborators can reply parent feedback"); ?></span>
                    </div>
                    <div class='m24 flex'>
                        <div class='flex1' >
                            <el-button type="button" class='btn btn-primary' icon="el-icon-plus" size='small' @click='addUser'><?php echo Yii::t("directMessage", "Add a member"); ?></el-button>
                        </div>
                        <el-button type="text" @click='clearJoint("clear")'  class='text-primary'  v-if='journalContent.joint_admins.length!=0'><?php echo Yii::t("directMessage", "Clear All"); ?>（{{journalContent.joint_admins.length}}）</el-button>
                    </div>
                    <div class='col-md-6 col-sm-6 mb24' v-for='(list,index) in journalContent.joint_admins'>
                        <div class="media bgGrey">
                            <div class="media-left pull-left media-middle">
                                <a href="javascript:void(0)">
                                    <img :src="journalData.userInfo[list].photoUrl" data-holder-rendered="true" class="contentAvatar">
                                </a>
                            </div>
                            <div class="media-right pull-right pt12 text-right"><span class="el-icon-circle-close font16" @click='delJoint(list)'></span></div>
                            <div class="media-body pt4 media-middle">
                                <div class="lineHeight20 text-primary"><span class="font14 color3 nowrap">{{journalData.userInfo[list].name}}</span></div>
                                <div class="font12 color6 nowrap">{{journalData.userInfo[list].hrPosition}}</div>
                            </div>
                        </div>
                    </div>
                    <div class='clearfix'></div>
                </div>
            </div>
            <div class='clearfix'></div>
        </div>
    </div>
    <div class="modal fade" id="addClassModal" tabindex="-1" role="dialog" data-backdrop="static" data-keyboard="false" >
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t("newDS", "Add subscribers");?></h4>
                </div>
                <div class="modal-body">
                    <div style='max-height:600px;'>
                        <div class='col-md-6 col-sm-6 '>
                            <ul class="nav nav-tabs" role="tablist">
                                <li role="presentation" :class="tabActive=='class'?'active':''"  @click='tabLabel("class")'><a href="javascript:;"><?php echo Yii::t("directMessage", "By Class");?></a></li>
                                <li role="presentation"  :class="tabActive=='profile'?'active':''"   @click='tabLabel("profile")'><a href="javascript:;"><?php echo Yii::t("directMessage", "By Course");?></a></li>
                                <li role="presentation"  :class="tabActive=='search'?'active':''"   @click='tabLabel("search")'><a href="javascript:;"><?php echo Yii::t("directMessage", "By Search");?></a></li>
                                <li role="presentation"  :class="tabActive=='custom'?'active':''"  @click='tabLabel("custom")'><a href="javascript:;"><?php echo Yii::t("directMessage", "By Group");?></a></li>
                                <li role="presentation"  :class="tabActive=='asa'?'active':''"  @click='tabLabel("asa")'><a href="javascript:;"><?php echo Yii::t("directMessage", "By ASA");?></a></li>
                            </ul>
                            <div class="tab-content" >
                                <div role="tabpanel" v-if='tabActive=="class"' class="tab-pane active mt15 scroll-box" id="class"  style='max-height:540px;overflow-y:auto'>
                                    <div v-for='(list,index) in classList' class='relative'>
                                        <p  @click='getChild(list)'>
                                            <span  class='font14 color606 cur-p'>{{list.title}} </span>
                                            <span class='glyphicon glyphicon-chevron-down ml5' v-if='classId!=list.classid'></span>
                                            <span class='glyphicon glyphicon-chevron-up ml5' v-else></span>
                                        </p>
                                        <p  class='allCheck' v-if='classId==list.classid'><button class="btn btn-primary pull-right btn-xs" type="button" @click='selectAll(list,"classList")'  v-if='list.childData && list.childData.length!=0'><?php echo Yii::t("global", "Select All");?></button></p>
                                        <div  class='border scroll-box mr10 childList' v-if='classId==list.classid'>
                                            <div v-if='!childLoading'>
                                                <div class='' v-if='list.childData && list.childData.length!=0'>
                                                    <div class="media listMedia" v-for='(item,index) in list.childData'>
                                                        <div class="media-left pull-left media-middle">
                                                            <a href="javascript:void(0)">
                                                                <img :src="item.photo" data-holder-rendered="true" class="media-object img-circle image42">
                                                            </a>
                                                        </div>
                                                        <div v-if='item.stuLoading'>
                                                            <div v-if='item.disabled' class="media-right pull-right text-muted mt15">
                                                                <span class='cur-p'><?php echo Yii::t("newDS", "Subscribed");?></span>
                                                            </div>
                                                            <div v-else class="media-right pull-right text-muted" @click='assignChildren(item,"one","classList")'>
                                                                <span class='cur-p addChild mt15'>+</span>
                                                            </div>
                                                        </div>
                                                        <div class='childLoading' v-else>
                                                            <span></span>
                                                        </div>
                                                        <div class="media-body media-middle">
                                                            <h4 class="media-heading font12 mt8">{{item.name}}</h4>
                                                            <div class="text-muted"><span>{{item.id}}</span><el-divider direction="vertical"></el-divider>{{list.title}}</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div v-else>
                                                    <div class='font12 text-muted text-center'><?php echo Yii::t("newDS", "no student in this class");?></div>
                                                </div>
                                            </div>
                                            <div class='loading' v-else>
                                                <span></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div v-if='tabActive=="profile"'>
                                    <div v-if='courseList.length!=0'>
                                        <div v-for='(list,index) in courseList' class='relative'>
                                            <p  @click='getCourseChild(list)'>
                                                <span  class='font14 color606 cur-p'>{{list.title}} </span>
                                                <span class='glyphicon glyphicon-chevron-down ml5' v-if='courseId!=list.id'></span>
                                                <span class='glyphicon glyphicon-chevron-up ml5' v-else></span>
                                            </p>
                                            <p  class='allCheck' v-if='courseId==list.id'><button class="btn btn-primary pull-right btn-xs" type="button" @click='selectAll(list,"courseList")'  v-if='list.childData && list.childData.length!=0'><?php echo Yii::t("global", "Select All");?></button></p>
                                            <div  class='border scroll-box mr10 childList' v-if='courseId==list.id'>
                                                <div v-if='!childLoading'>
                                                    <div class='' v-if='list.childData && list.childData.length!=0'>
                                                        <div class="media listMedia" v-for='(item,index) in list.childData'>
                                                            <div class="media-left pull-left media-middle">
                                                                <a href="javascript:void(0)">
                                                                    <img :src="item.photo" data-holder-rendered="true" class="media-object img-circle image42">
                                                                </a>
                                                            </div>
                                                            <div v-if='item.stuLoading'>
                                                                <div v-if='item.disabled' class="media-right pull-right text-muted mt15">
                                                                    <span class='cur-p '><?php echo Yii::t("newDS", "Subscribed");?></span>
                                                                </div>
                                                                <div v-else class="media-right pull-right text-muted" @click='assignChildren(item,"one","courseList")'>
                                                                    <span class='cur-p addChild mt15'>+</span>
                                                                </div>
                                                            </div>
                                                            <div class='childLoading' v-else>
                                                                <span></span>
                                                            </div>
                                                            <div class="media-body media-middle">
                                                                <h4 class="media-heading font12 mt8">{{item.name}}</h4>
                                                                <div class="text-muted"><span>{{item.id}}</span><el-divider direction="vertical"></el-divider>{{item.class_name}}</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div v-else>
                                                        <div class='font12 text-muted text-center'><?php echo Yii::t("newDS", "no student in this class");?></div>
                                                    </div>
                                                </div>
                                                <div class='loading' v-else>
                                                    <span></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div v-else class="alert alert-success mt20" role="alert"><?php echo Yii::t("newDS", "No MS courses assigned to you yet.");?></div>
                                </div>
                                <div v-if='tabActive=="search"'>
                                    <div class='mt10 pr16'>
                                        <el-input
                                            placeholder="搜索学生"
                                            ref="scoreInput"
                                            v-model="searchChild">
                                            <i slot="prefix" class="el-input__icon el-icon-search"></i>
                                        </el-input>
                                    </div>
                                    <div class='scroll-box pt16 pr16 overflow-y relative' style='height:500px'>
                                        <div class='loading' v-if='searchStu' >
                                            <span></span>
                                        </div>
                                        <div v-else-if='searchChildList.length!=0'>
                                            <div class="media listMedia" v-for='(item,index) in searchChildList[0].childData'>
                                                <div class="media-left pull-left media-middle">
                                                    <a href="javascript:void(0)">
                                                        <img :src="item.avatar" data-holder-rendered="true" class="media-object img-circle image42">
                                                    </a>
                                                </div>
                                                <div v-if='item.stuLoading'>
                                                    <div v-if='item.disabled' class="media-right pull-right text-muted mt15">
                                                        <span class='cur-p '><?php echo Yii::t("newDS", "Subscribed");?></span>
                                                    </div>
                                                    <div v-else class="media-right pull-right text-muted" @click='assignChildren(item,"one","searchChildList")'>
                                                        <span class='cur-p addChild mt15'>+</span>
                                                    </div>
                                                </div>
                                                <div class='childLoading' v-else>
                                                    <span></span>
                                                </div>
                                                <div class="media-body media-middle">
                                                    <h4 class="media-heading font12 mt8">{{item.name}}</h4>
                                                    <div class="text-muted"><span>{{item.id}}</span><el-divider direction="vertical"></el-divider>{{item.class_name}}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div role="tabpanel" v-if='tabActive=="custom"' class="tab-pane active mt15" id="class"  >
                                    <div class='scroll-box'  style='height:500px;overflow-y:auto'>
                                        <div v-for='(list,index) in groupList' class='relative'>
                                            <p  @click='getGroupList(list)'>
                                                <span  class='font14 color606 cur-p'>{{list.title}} </span>
                                                <span class='glyphicon glyphicon-chevron-down ml5' v-if='groupId!=list.id'></span>
                                                <span class='glyphicon glyphicon-chevron-up ml5' v-else></span>
                                            </p>
                                            <p  class='allCheck' v-if='groupId==list.id'><button class="btn btn-primary pull-right btn-xs" type="button" @click='selectAll(list,"groupList")'><?php echo Yii::t("global", "Select All");?></button></p>
                                            <div  class='border scroll-box mr10 childList'  v-if='groupId==list.id'>
                                                <div v-if='!childLoading'>
                                                    <div class="media listMedia" v-for='(item,index) in list.childData'>
                                                        <div class="media-left pull-left media-middle">
                                                            <a href="javascript:void(0)">
                                                                <img :src="item.avatar" data-holder-rendered="true" class="media-object img-circle image42">
                                                            </a>
                                                        </div>
                                                        <div v-if='item.stuLoading'>
                                                            <div v-if='item.disabled' class="media-right pull-right text-muted mt15">
                                                                <span class='cur-p '><?php echo Yii::t("newDS", "Subscribed");?></span>
                                                            </div>
                                                            <div v-else class="media-right pull-right text-muted" @click='assignChildren(item,"one","groupList")'>
                                                                <span class='cur-p addChild mt15'>+</span>
                                                            </div>
                                                        </div>
                                                        <div class='childLoading' v-else>
                                                            <span></span>
                                                        </div>
                                                        <div class="media-body media-middle">
                                                            <h4 class="media-heading font12 mt8">{{item.name}}</h4>
                                                            <div class="text-muted"><span>{{item.id}}</span><el-divider direction="vertical"></el-divider>{{item.class_name}}</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class='loading' v-else>
                                                    <span></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <p class='mt15'>
                                        <button type="button" class="btn btn-default  btn-sm"  @click='addGroup("add")'>
                                            <span class="glyphicon glyphicon-plus" aria-hidden="true"></span> <?php echo Yii::t("newDS", "Create a group");?>
                                        </button>
                                        <button type="button" class="btn btn-default  btn-sm"  @click='addGroup("edit")' v-if='groupList.length!=0'>
                                            <span class="glyphicon glyphicon-edit" aria-hidden="true"></span> <?php echo Yii::t("newDS", "Manage groups");?>
                                        </button>
                                    </p>
                                </div>
                                <!--课后课-->
                                <div role="tabpanel" v-if='tabActive=="asa"' class="tab-pane active mt15 scroll-box" style='max-height:540px;overflow-y:auto'>
                                    <div v-if='!asaCourseGroupLoading'>
                                        <div v-if="asaCourseGroupList.length!=0">
                                            <!--学年选择-->
                                            <div class="dropdown">
                                                <button class="btn btn-default dropdown-toggle" type="button" id="dropdownMenu1" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                                                    {{selectYear}}-{{selectYear+1}}<?php echo Yii::t("newDS", " Yr.");?>
                                                    <span class="caret"></span>
                                                </button>
                                                <ul class="dropdown-menu" aria-labelledby="dropdownMenu1">
                                                    <li v-for="(item,index) in asaCourseGroupList" @click="chooseYear(item.startyear)">
                                                        <a href="#">{{item.startyear}}-{{item.startyear+1}}<?php echo Yii::t("newDS", " Yr.");?></a>
                                                    </li>
                                                </ul>
                                            </div>
                                            <!--课程组-->
                                            <div style="margin-top: 15px">
                                                <div class="relative " v-for="(item,index) in selectedAsaCourseGroupList">
                                                    <p  @click='getAsaCourseList(item.id)'>
                                                        <span  class='font14 color606 cur-p'>{{item.title}} </span>
                                                        <span class='glyphicon glyphicon-chevron-down ml5' v-if='selectedAsaCourseGroupId!=item.id'></span>
                                                        <span class='glyphicon glyphicon-chevron-up ml5' v-else></span>
                                                    </p>
                                                    <!--课程列表-->
                                                    <div class="ml15  mr10 relative" v-if='selectedAsaCourseGroupId==item.id'>
                                                        <div class="relative" v-if="!asaCourseLoading" v-for="(item2,index2) in selectedAsaCourseList">
                                                            <p  @click='getAsaCourseChild(item2.id)'>
                                                                <span  class='font14 color606 cur-p'>{{item2.title}} </span>
                                                                <span class='glyphicon glyphicon-chevron-down ml5' v-if='selectedAsaCourseId!=item2.id'></span>
                                                                <span class='glyphicon glyphicon-chevron-up ml5' v-else></span>
                                                            </p>
                                                            <!--选择全部学生-->
                                                            <p  class='allCheck' v-if='selectedAsaCourseId==item2.id'>
                                                                <button class="btn btn-primary pull-right btn-xs" type="button" @click='selectAll(item2,"selectedAsaCourseList")'  v-if='asaCourseChildList.length!=0'><?php echo Yii::t("global", "Select All");?></button>
                                                            </p>
                                                            <!--学生列表-->
                                                            <div  class='border scroll-box mr10 asaGroupList relative' v-if='selectedAsaCourseId==item2.id'>
                                                                <div v-if="!asaCourseChildLoading">
                                                                    <div class='' v-if='asaCourseChildList.length!=0'>
                                                                        <div class="media listMedia" v-for='(item3,index3) in asaCourseChildList'>
                                                                            <div class="media-left pull-left media-middle">
                                                                                <a href="javascript:void(0)">
                                                                                    <img :src="item3.photo" data-holder-rendered="true" class="media-object img-circle image42">
                                                                                </a>
                                                                            </div>
                                                                            <div>
                                                                                <div v-if='item3.disabled' class="media-right pull-right text-muted mt15">
                                                                                    <span class='cur-p'><?php echo Yii::t("newDS", "Subscribed");?></span>
                                                                                </div>
                                                                                <div v-else class="media-right pull-right text-muted" @click='assignChildren(item3,"one","selectedAsaCourseList")'>
                                                                                    <span class='cur-p addChild mt15'>+</span>
                                                                                </div>
                                                                            </div>
                                                                            <div class="media-body media-middle">
                                                                                <h4 class="media-heading font12 mt8">{{item3.name}}</h4>
                                                                                <div class="text-muted"><span>{{item3.id}}</span><el-divider direction="vertical"></el-divider>{{item3.class_name}}</div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div v-else>
                                                                        <div class='font12 text-muted text-center' style="line-height: 40px"><?php echo Yii::t("newDS", "此课程没有学生");?></div>
                                                                    </div>
                                                                </div>
                                                                <div class="loading" v-else>
                                                                    <span></span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="loading" v-else>
                                                            <span></span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div v-else class="alert alert-success" role="alert"><?php echo Yii::t("newDS", "您没有执教的课后课");?></div>
                                    </div>
                                    <div class='loading' v-else>
                                        <span></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class='col-md-6 col-sm-6 borderLeft'>
                            <ul class="nav nav-tabs" role="tablist">
                                <li role="presentation" class="active"><a href="#chosen" aria-controls="home" role="tab" data-toggle="tab"><?php echo Yii::t("newDS", "Subscribed");?></a></li>
                            </ul>
                            <div class="tab-content">
                                <div role="tabpanel" class="tab-pane active" id="chosen">
                                    <p class='mt15 font14 color606'><?php echo Yii::t("newDS", " ");?>{{selected.length}}<?php echo Yii::t("newDS", " student(s) selected");?>
                                        <button class="btn btn-primary pull-right btn-xs" v-if='selected.length!=0' type="button" @click='batchDel("modal")'><?php echo Yii::t("newDS", "Remove All");?></button>
                                    </p>
                                    <div class='border scroll-box p10 overflow-y' style='height:500px'>
                                    <div class="media listMedia" v-for='(list,index) in selected'>
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                                <img :src="list.avatar" data-holder-rendered="true" class="media-object img-circle image42">
                                            </a>
                                        </div>
                                        <div class="media-right pull-right text-muted" v-if='!list.loading' @click='Unassign(list.id,index)'>
                                            <span class='cur-p mt15 font16 el-icon-circle-close'></span>
                                        </div>
                                        <div class='childLoading' v-else>
                                            <span></span>
                                        </div>
                                        <div class="media-body media-middle">
                                            <h4 class="media-heading font12 mt8">{{list.name}}</h4>
                                            <div v-if="list.asa_course_name" class="text-muted">{{list.asa_course_name}}</div>
                                            <div v-else class="text-muted"><span>{{list.id}}</span><el-divider direction="vertical"></el-divider>{{list.class_name}}</div>
                                        </div>
                                    </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class='clearfix'></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="groupModal" tabindex="-1" role="dialog" aria-hidden='true' data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" v-if='groupType=="add"' id="parentCommentsModalLabel"><?php echo Yii::t("newDS", "Create a group");?></h4>
                <h4 class="modal-title" v-else id="parentCommentsModalLabel"><?php echo Yii::t("newDS", "Manage groups");?></h4>
            </div>
            <div class="modal-body">
                <div class='col-md-12 col-sm-12 pb20' style='border-bottom:1px solid #dddddd;' v-if='groupType=="edit"'>
                        <?php echo Yii::t("newDS", "Select a group");?>
                        <select class="form-control select_4  ml10 inline-block"  v-model='editGroupId'  @change='viewGroup()'>
                            <option v-for='(list,index) in groupList' :value='list.id'>{{list.title}}</option>
                        </select>
                </div>
                <div class='clearfix'></div>
                <div style='max-height:600px;'>
                    <div class='col-md-6 col-sm-6 '>
                        <div  class="tab-pane active scroll-box mt10"  style='max-height:600px;overflow-y:auto'>
                            <div v-for='(list,index) in classList' class='relative'>
                                <p @click='getGroupChild(list)'>
                                    <span  class='font14 color606 cur-p' >{{list.title}} </span>
                                    <span class='glyphicon glyphicon-chevron-down ml5' v-if='classId!=list.classid'></span>
                                    <span class='glyphicon glyphicon-chevron-up ml5' v-else></span>
                                </p>
                                <p  class='allCheck' v-if='classId==list.classid'><button class="btn btn-primary pull-right btn-xs" type="button" @click='groupAll(list)' v-if='list.childData.length!=0'><?php echo Yii::t("global", "Select All");?></button></p>
                                <div  class='border scroll-box mr10 classList' v-if='classId==list.classid'>
                                    <div v-if='!childLoading'>
                                        <div class=''  v-if='list.childData.length!=0'>
                                            <div class="media mt10" v-for='(item,index) in list.childData'>
                                                <div class="media-left pull-left media-middle">
                                                    <a href="javascript:void(0)">
                                                        <img :src="item.photo" data-holder-rendered="true" class="media-object img-circle image">
                                                    </a>
                                                </div>
                                                <div v-if='item.groupDisabled' class="media-right pull-right text-muted lineHeight">
                                                    <span class='cur-p mt10'><?php echo Yii::t("newDS", "Subscribed");?></span>
                                                </div>
                                                <div v-else class="media-right pull-right text-muted" @click='addGroupChild(item,"one")'>
                                                    <span class='cur-p addChild mt10'>+</span>
                                                </div>
                                                <div class="media-body media-middle">
                                                    <h4 class="media-heading font12 lineHeight">{{item.name}}</h4>
                                                </div>
                                            </div>
                                        </div>
                                        <div v-else>
                                            <div class='font12 text-muted text-center'><?php echo Yii::t("newDS", "no student in this class");?></div>
                                        </div>
                                    </div>
                                    <div class='loading' v-else>
                                        <span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class='col-md-6 col-sm-6 borderLeft'>
                        <p class='flex mt10'><span class='font14 color606 flexWidth'><?php echo Yii::t("newDS", "Group Title");?></span>
                            <input type="text" v-model='groupTitle' class="form-control flex1 ml10" id="exampleInputEmail1" placeholder="<?php echo Yii::t("newDS", "Group Title");?>">
                        </p>
                        <p class='mt15 font14 color606'><?php echo Yii::t("newDS", " ");?>{{groupSelect.length}}<?php echo Yii::t("newDS", " student(s) selected");?>
                            <button class="btn btn-primary pull-right btn-xs" v-if='groupSelect.length!=0' type="button" @click='groupSelect=[];classId=""'><?php echo Yii::t("newDS", "Remove All");?></button>
                        </p>
                        <div class='border scroll-box' style='height:485px;padding:10px'>
                            <div class="media" v-for='(list,index) in groupSelect'>
                                <div class="media-left pull-left media-middle">
                                    <a href="javascript:void(0)">
                                        <img :src="list.avatar" data-holder-rendered="true" class="media-object img-circle image">
                                    </a>
                                </div>
                                <div class="media-right pull-right text-muted" @click='delGroupChild(list.id,index)'>
                                    <span class='closeChild cur-p mt10 glyphicon glyphicon-minus'></span>
                                </div>

                                <div class="media-body media-middle">
                                    <h4 class="media-heading font12">{{list.name}}</h4>
                                    <div class="text-muted">{{list.class_name}}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class='clearfix'></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal" ><?php echo Yii::t("global", "Close");?></button>
                <button type="button" v-if='groupType=="edit"' class="btn btn-default"  @click='delGroup()'><?php echo Yii::t("global", "Delete");?></button>
                <button type="button" class="btn btn-primary" @click='saveGroup()'><?php echo Yii::t("global", "Save");?></button>
            </div>
            </div>
        </div>
    </div>
    <!-- 协同管理 -->
    <div class="modal fade" id="authorizedModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("directMessage", "Add Reply collaborators"); ?></h4>
                </div>
                <div class="modal-body" >
                    <div class='color6 font14 mt24'><?php echo Yii::t("global", "Search"); ?></div>
                    <div  class='flex mt16'>
                        <el-select
                            v-model="teacherUid"
                            filterable
                            ref="agentSelect"
                            @hook:mounted="cancalReadOnly" 
                            @visible-change="cancalReadOnly" 
                            remote
                            class='inline-input flex1'
                            reserve-keyword
                            placeholder="<?php echo Yii::t("directMessage", "Key words"); ?>"
                            :remote-method="remoteMethod"
                            prefix-icon="el-icon-search"
                            :loading="loading">
                            <el-option
                                v-for="item in options"
                                :key="item.uid"
                                :label="item.name"
                                class='optionSearch mb8'
                                :value="item.uid">
                                <div class="media">
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)">
                                            <img :src="item.photoUrl" data-holder-rendered="true" class="media-object img-circle imageSearch">
                                        </a>
                                    </div>
                                    <div class="media-body mt5 media-middle">
                                        <h4 class="media-heading font14 color3 text_overflow">{{item.name}}</h4>
                                        <div class="text-muted text_overflow font12">{{ item.hrPosition }}</div>
                                    </div>
                                </div>
                            </el-option>
                        </el-select>
                        <button type="button" class="btn btn-primary ml16"  :disabled='teacherUid==""?true:false' @click='confirmAdmin'><?php echo Yii::t("directMessage", "Proceed to add"); ?></button>
                    </div>
                    <div class='color6 font14 mt24 mb16' v-if='journalContent.joint_admins'><span><?php echo Yii::t("directMessage", "Reply collaborators"); ?></span>  <span class="badge">{{journalContent.joint_admins.length}}</span></div>
                    <div class='col-md-4 col-sm-4 mb24' v-for='(list,index) in journalContent.joint_admins'>
                        <div class="media bgGrey" >
                            <div class="media-left pull-left media-middle">
                                <a href="javascript:void(0)"><img :src="journalData.userInfo[list].photoUrl" data-holder-rendered="true" class="contentAvatar"></a>
                            </div>
                            <div class="media-right pull-right pt12 text-right">
                                <span class='el-icon-circle-close font16' @click='delJoint(list)'></span>
                            </div>
                            <div class="media-body pt4 media-middle">
                                <div class="lineHeight20  text-primary">
                                    <span class='font14 color3 nowrap'>{{journalData.userInfo[list].name}}</span>
                                </div>
                                <div class="font12 color6 nowrap">{{journalData.userInfo[list].hrPosition}}</div>
                            </div>
                        </div>
                    </div>
                    <div class='clearfix'></div>
                </div>
            </div>
        </div>
    </div>
    <!-- 指定时间发布 -->
    <div class="modal fade" id="assignTimeModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("directMessage", "publish time"); ?></h4>
                </div>
                <div class="modal-body" >
                    <div class='text-center'>
                        <input type="text" placeholder="<?php echo Yii::t("directMessage", "Publish time"); ?>"  class="form-control select_4" style='margin:0 auto'  v-model="publishDate" readonly="readonly " >
                        <v-date-picker class='mt8' v-model="publishDate" mode="dateTime"  :min-date='new Date()' :model-config="modelConfig"  mode="dateTime" is24hr />
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button" class="btn btn-primary" @click='publishConfirm("assignTime")'><?php echo Yii::t("message", "OK");?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 删除确认框 -->
    <div class="modal fade" id="delModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel">
                       <span  v-if="delType=='del'"><?php echo Yii::t("global", "Cancel"); ?></span>
                       <span v-else><?php echo Yii::t("directMessage", "Clear All"); ?></span>
                    </h4>
                </div>
                <div class="modal-body" >
                    <div v-if="delType=='del'">
                        <div v-if='delStatus==10'><?php echo Yii::t("directMessage", "DM with comments from parents cannot be deleted, make it offline instead?"); ?></div>
                        <div v-else>
                            <div v-if='journalContent.status==1'><?php echo Yii::t("directMessage", "This DM is already online, Proceed to delete?"); ?></div>
                            <div v-else><?php echo Yii::t("directMessage", "Proceed to remove?"); ?></div> </div>
                    </div>
                    <div v-if="delType=='Joint'"><?php echo Yii::t("directMessage", "Empty all collaborators?"); ?></div>
                    <div v-if="delType=='child'"><?php echo Yii::t("directMessage", "Empty all subscribers?"); ?></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button" v-if='delStatus==10' class="btn btn-primary" @click='journalOffline()'><?php echo Yii::t("workflow", "Offline"); ?></button>
                    <button type="button" v-else class="btn btn-primary" @click='delConfirm'><?php echo Yii::t("global", "OK"); ?></button>
                    <!-- <button type="button" class="btn btn-primary" @click='delConfirm'><?php echo Yii::t("message", "OK");?></button> -->
                </div>
            </div>
        </div>
    </div>
    <!-- 取消发布确认框 -->
    <div class="modal fade" id="unpublishModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel">
                        <?php echo Yii::t("directMessage", "Make online"); ?>
                    </h4>
                </div>
                <div class="modal-body" >
                    <div ><?php echo Yii::t("directMessage", "Proceed to make it offline?"); ?></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button" class="btn btn-primary" @click='unpublish()'><?php echo Yii::t("message", "OK");?></button>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    var height=document.documentElement.clientHeight-$('.breadcrumb').outerHeight(true)-140
   
    function newTinymce(){
        var types='<?php echo $this->branchObj->type ?>';
        if(types=='20'){
            var contextmenu=['image','editDay']
            var toolbar='mediaDialog shareDialog bullist numlist dailyCare'
        }else{
            var toolbar='mediaDialog shareDialog bullist numlist'
            var contextmenu=['image']
        }
        const example_image_upload_handler = (blobInfo, progress) => new Promise((resolve, reject) => {
            $.ajax({
                url: '<?php echo $this->createUrl("journals/getQiniuTokenSimple") ?>',
                type: "post",
                dataType: 'json',
                data: {
                    isPrivate: 0,
                    prefix: 'notice',
                },
                success: function(data) {
                    if (data.state == 'success') {
                        var token = data.data.token;
                        var domain = data.data.domain;
                        const xhr = new XMLHttpRequest();
                        xhr.withCredentials = false;
                        xhr.open("post", "https://up-z0.qiniup.com");
                        xhr.upload.onprogress = (e) => {
                            progress(e.loaded / e.total * 100);
                        };

                        xhr.onload = () => {
                            if (xhr.status === 403) {
                            reject({ message: 'HTTP Error: ' + xhr.status, remove: true });
                            return;
                            }

                            if (xhr.status < 200 || xhr.status >= 300) {
                            reject('HTTP Error: ' + xhr.status);
                            return;
                            }

                            const json = JSON.parse(xhr.responseText);
                        
                            resolve( domain + "/" + json.name+container.WatermarkImg);
                        };

                        xhr.onerror = () => {
                            reject('Image upload failed due to a XHR Transport error. Code: ' + xhr.status);
                        };

                        var formData = new FormData();
                        var file = blobInfo.blob();
                        formData.append('file', file, file.name);
                        formData.append('token', token);
                        xhr.send(formData);
                    } else {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                },
                error: function(data) {
                    resultTip({
                        error: 'warning',
                        msg: "上传失败"
                    });
                },
            })
        });
        tinymce.PluginManager.add('my-example-plugin', function (editor) {
            editor.ui.registry.addMenuItem('image', {
                icon: 'image',
                text: '<?php echo Yii::t("newDS", "Add/Remove Watermark");?>',
                onAction: function () {
                    if(container.Watermark.indexOf(container.WatermarkImg) == -1){
                        container.Watermark=container.Watermark+container.WatermarkImg
                    }else{
                        container.Watermark=container.Watermark.replace(container.WatermarkImg,"")
                    }
                    editor.insertContent('<div><img  style="max-width:100%"  src='+container.Watermark+'></div>')
                }
            });
            editor.ui.registry.addContextMenu('image', {
                update: function (element) {
                    container.Watermark=element.src
                    return !container.Watermark ? '' : 'image';
                }
            });
        });
        // tinymce.PluginManager.add('customCommand1', function (editor) {
        //     editor.ui.registry.addMenuItem('editDay', {
        //         icon: 'dailyCare',
        //         text: '编辑每日生活照顾摘要',
        //         onAction: function () {
        //             var selectedNode = editor.selection.getNode();
        //             if (selectedNode.classList.contains('borderInsert')) {
        //                 getDay(editor,'show')
        //             }
        //         }
        //     });
        //     editor.ui.registry.addContextMenu('div>.borderInsert', {
        //         update: function (element) {
        //             return element;
        //         }
        //     });
        // });
        tinymce.init({
            selector: 'textarea#tinymceCn',
            width: "100%",
            height:800,
            resize: false,
            language:'<?php echo Yii::app()->language;?>'=='zh_cn'?'zh_CN':'en',
            plugins: [
                'fullscreen', 'image', 'link', 'media', 'preview', 'table', 'code', 'my-example-plugin', 'lists', 'advlist','my-example-plugin',
            ],
            contextmenu: contextmenu,
            noneditable_class: 'non-editable',
            toolbar:toolbar,
            images_upload_handler:example_image_upload_handler,
            setup: function(editor) {
                // 实时同步编辑器内容到 selector
                editor.on('change', function() {
                    tinymce.triggerSave();
                });
                editor.ui.registry.addIcon('mediaDialog', '<svg width="24" height="24"><path fill-rule="nonzero" d="M5 15.7l2.3-2.2c.3-.3.7-.3 1 0L11 16l5.1-5c.3-.4.8-.4 1 0l2 1.9V8H5v7.7zM5 18V19h3l1.8-1.9-2-2L5 17.9zm14-3l-2.5-2.4-6.4 6.5H19v-4zM4 6h16c.6 0 1 .4 1 1v13c0 .6-.4 1-1 1H4a1 1 0 0 1-1-1V7c0-.6.4-1 1-1zm6 7a2 2 0 1 1 0-4 2 2 0 0 1 0 4zM4.5 4h15a.5.5 0 1 1 0 1h-15a.5.5 0 0 1 0-1zm2-2h11a.5.5 0 1 1 0 1h-11a.5.5 0 0 1 0-1z"></path></svg>');
                editor.ui.registry.addIcon('shareDialog', ' <svg xmlns="http://www.w3.org/2000/svg" version="1.0" width="24" height="24" viewBox="0 0 36.000000 36.000000" preserveAspectRatio="xMidYMid meet"><g transform="translate(0.000000,36.000000) scale(0.100000,-0.100000)" fill="#000000" stroke="none"><path d="M67 324 c-10 -11 -8 -74 2 -74 5 0 11 12 13 28 l3 27 103 3 102 3 0 -126 0 -125 -105 0 -105 0 0 25 c0 14 -5 25 -11 25 -6 0 -9 -16 -7 -37 l3 -38 120 0 120 0 0 145 0 145 -116 3 c-63 1 -118 -1 -122 -4z"/><path d="M161 242 c-12 -23 4 -52 29 -52 26 0 45 36 29 56 -16 19 -46 18 -58 -4z m44 -11 c7 -12 -12 -24 -25 -16 -11 7 -4 25 10 25 5 0 11 -4 15 -9z"/><path d="M40 220 c0 -5 14 -10 30 -10 17 0 30 5 30 10 0 6 -13 10 -30 10 -16 0 -30 -4 -30 -10z"/><path d="M40 180 c0 -5 14 -10 30 -10 17 0 30 5 30 10 0 6 -13 10 -30 10 -16 0 -30 -4 -30 -10z"/><path d="M152 154 c-12 -8 -22 -24 -22 -35 0 -26 13 -24 25 4 7 14 19 22 35 22 16 0 28 -8 35 -22 12 -28 25 -30 25 -4 0 22 -35 51 -60 51 -9 0 -26 -7 -38 -16z"/><path d="M40 140 c0 -5 14 -10 31 -10 17 0 28 4 24 10 -3 6 -17 10 -31 10 -13 0 -24 -4 -24 -10z"/></g></svg>');
                editor.ui.registry.addIcon('dailyCare', '<svg t="1722578783609" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2486" width="24" height="24"><path d="M317.781333 295.992889a173.624889 173.624889 0 0 1 173.397334 173.397333 172.942222 172.942222 0 0 1-46.762667 118.385778c18.773333 10.922667 36.295111 24.234667 51.882667 39.936a250.936889 250.936889 0 0 1 73.784889 178.176c0 40.96-9.955556 69.916444-30.72 88.519111-20.081778 18.318222-46.250667 23.495111-76.572445 23.495111-19.342222 0-40.504889-2.104889-62.805333-4.380444-26.168889-2.844444-53.304889-5.518222-81.806222-5.518222-28.558222 0-55.580444 2.844444-81.806223 5.518222-56.945778 5.802667-106.382222 10.979556-139.491555-19.000889-20.593778-18.773333-30.72-47.729778-30.72-88.519111 0-67.299556 26.339556-130.616889 73.955555-178.176 15.587556-15.644444 32.768-28.899556 51.370667-39.708445a172.828444 172.828444 0 0 1-47.104-118.727111 173.624889 173.624889 0 0 1 173.397333-173.397333z m81.237334 326.599111c-24.291556 12.913778-51.768889 20.195556-81.123556 20.195556-26.794667 0-52.337778-6.087111-74.979555-17.066667a195.754667 195.754667 0 0 0-119.921778 180.167111c0 23.722667 3.982222 39.310222 12.003555 46.535111 14.506667 13.084444 54.044444 8.988444 95.800889 4.778667 26.282667-2.616889 56.092444-5.688889 87.495111-5.688889s61.212444 3.072 87.495111 5.688889c41.813333 4.209778 81.351111 8.305778 95.800889-4.778667 7.964444-7.224889 12.003556-22.926222 12.117334-46.648889a195.754667 195.754667 0 0 0-117.930667-179.256889c1.251556-1.194667 2.275556-2.616889 3.242667-3.925333zM957.383111 108.088889v714.808889h-263.111111v-60.700445h199.452444V168.846222H195.982222v86.869334H132.380444V108.088889H957.44zM317.781333 352.597333a116.963556 116.963556 0 0 0-116.792889 116.792889c0 64.398222 52.394667 116.792889 116.792889 116.792889A116.963556 116.963556 0 0 0 434.631111 469.390222a116.963556 116.963556 0 0 0-116.792889-116.792889z m492.202667-50.005333c1.422222-0.113778 2.844444 2.218667 3.015111 5.12v0.113778l30.378667 119.864889c0.398222 1.422222-1.706667 3.128889-4.494222 3.811555l-44.487112 11.377778c-2.844444 0.796444-5.518222 0.227556-5.802666-1.080889l-9.784889-38.798222-58.197333 81.578667a5.802667 5.802667 0 0 1-1.137778 1.820444l-26.851556 37.319111c-1.820444 2.275556-4.096 3.584-5.233778 2.787556l-57.116444-41.415111-53.191111 74.581333c-0.796444 1.137778-3.413333 0.739556-5.802667-0.967111l-37.489778-26.624c-2.389333-1.706667-3.697778-3.982222-2.844444-5.12l56.32-78.961778 0.341333-0.512 6.485334-8.988444 9.329777-13.027556 0.170667-0.056889 10.922667-15.189333c1.706667-2.503111 3.982222-3.697778 5.12-2.901334l56.945778 41.358223 59.505777-83.399111-45.681777 3.697777c-1.422222 0.113778-2.844444-2.161778-3.015112-5.12l-3.811555-45.738666c-0.170667-2.901333 0.739556-5.404444 2.104889-5.518223z" p-id="2487"></path></svg>');
                if(types=='20'){
                    const isAnchorElement = (node) => {
                        if (node.classList.contains('borderInsert')) {
                            return node.nodeName.toLowerCase() === 'div';
                        }
                    }
                    editor.ui.registry.addContextForm('dayEdit', {
                        predicate: isAnchorElement,
                        position:'node',
                        commands: [
                            {
                                type: 'contextformtogglebutton',
                                icon: 'duplicate',
                                tooltip: '<?php echo Yii::t("newDS", "Edit Daily Care Summary");?>',
                                primary: true,
                                onSetup: (buttonApi) => {
                                    $('.tox-toolbar-nav-js').parent().hide()
                                },
                                onAction: (formApi) => {
                                    var selectedNode = editor.selection.getNode();
                                    if (selectedNode.classList.contains('borderInsert')) {
                                        getDay(editor,'show')
                                        formApi.hide();
                                    }
                                }
                            },
                            {
                                type: 'contextformbutton',
                                icon: 'remove',
                                tooltip: '<?php echo Yii::t("global", "Delete");?>',
                                active: false,
                                onAction: (formApi) => {
                                    var selectedNode = editor.selection.getNode();
                                    const element = editor.dom.get(selectedNode.id);
                                    if(element){
                                        editor.dom.remove(selectedNode);
                                    }
                                }
                            }
                        ]
                    })
                }
                editor.ui.registry.addButton('dailyCare', {
                    icon:'dailyCare',
                    tooltip: '<?php echo Yii::t("newDS", "Daily Care Summary");?>',
                    onAction: function () {
                        getDay(editor,'click')
                    }
                })
                editor.ui.registry.addButton('mediaDialog', {
                    icon:'mediaDialog',
                    tooltip: '<?php echo Yii::t("newDS", "Media Gallery");?>',
                    onAction: function () {
                        container.tinymceList=[]
                        var instanceApi = editor.windowManager.openUrl({
                            onMessage: function (dialogApi, details) {
                                switch (details.mceAction) {
                                    case 'loading':
                                        dialogApi.unblock()
                                        break;
                                    case 'addItem':
                                        container.tinymceList.push(details.content)
                                        break;
                                    case 'removeItem':
                                        for(var i=0;i<container.tinymceList.length;i++){
                                            if(container.tinymceList[i].id==details.content.id){
                                                container.tinymceList.splice(i,1)
                                            }
                                        }
                                    break;
                                }
                            },
                            onAction: function (dialogApi, details) {
                                dialogApi.close()
                                for(var i=0;i<container.tinymceList.length;i++){
                                    if(container.tinymceList[i].type=='photo'){
                                        editor.insertContent('<div><img style="max-width:100%" src='+container.tinymceList[i]._url+'></div>')
                                    }else{
                                        let url=container.tinymceList[i].url.split('!vh120')[0]
                                        editor.insertContent(
                                        '<p><video controls  style="max-width:100%;object-fit:cover"  width="600" height="auto" poster='+url+' >'+
                                            '<source src='+container.tinymceList[i]._url+'  type="video/mp4">'+
                                        '</video></p>')
                                    }
                                }
                            },
                            title: '<?php echo Yii::t("newDS", "Media Gallery"); ?>',
                            url: '<?php echo $this->createUrl('journals/media'); ?>',
                            height: 500,
                            width: 730,
                            buttons: [{
                                type:'custom',
                                text:'Insert',
                                name:'btn-insert',
                                primary: true,
                                align: 'end'
                            },
                                {
                                type:'cancel',
                                text:'Close',
                                name:'btn-close',
                                primary: false,
                                align: 'end'
                            }],

                        });
                        instanceApi.block('loading')
                    }
                })
                editor.ui.registry.addButton('shareDialog', {
                    icon: 'shareDialog',
                    tooltip:'<?php echo Yii::t("directMessage", "Insert Department Contact QrCode"); ?>',
                    onAction: function () {
                        container.tinymceList=[]
                        var instanceApi = editor.windowManager.openUrl({
                            onMessage: function (dialogApi, details) {
                                switch (details.mceAction) {
                                    case 'loading':
                                        dialogApi.unblock()
                                        break;
                                    case 'insertImg':
                                        editor.insertContent('<div style="text-align:center;margin:0 auto"><img src='+details.content+'></div><div></div>')
                                        dialogApi.close()
                                    break;
                                }
                            },        
                            title: '<?php echo Yii::t("directMessage", "Insert Department Contact QrCode"); ?>',
                            url: '<?php echo $this->createUrl('qrcode'); ?>',
                            height: 350,
                            width:400,
                        });
                        instanceApi.block('loading')
                    }
                })
            },
        })
       
        // .tox .tox-toolbar-nav-js
    }
    function getDay(editor,type){
        container.tinymceList={}
        var url=''
        if(type=='click'){
            url='<?php echo $this->createUrl('daily'); ?>'
        }else{
            var selectedNode = editor.selection.getNode();
            const dataDate = selectedNode.querySelector('.dataDate');
            const date = dataDate.dataset.id;
            if(selectedNode.querySelector('.dataLunch')){
                const dataLunch = selectedNode.querySelector('.dataLunch');
                var lunch = dataLunch.dataset.id;
                const dataCup = selectedNode.querySelector('.dataCup');
                var cup = dataCup.dataset.id;
                const dataBed = selectedNode.querySelector('.dataBed');
                var bed = dataBed.dataset.id;
                const dataToilet = selectedNode.querySelector('.dataToilet');
                var toilet = dataToilet.dataset.id;
            }else{
                var lunch=''
                var cup=''
                var bed=''
                var toilet=''
            }
            if(selectedNode.querySelector('.datagoodsList')){
                const datagoodsList = selectedNode.querySelector('.datagoodsList');
                var goodsList = datagoodsList.dataset.id;
            }else{
                var goodsList =''
            }
            if(selectedNode.querySelector('.dataother')){
                const dataother = selectedNode.querySelector('.dataother');
                var other = dataother.dataset.id;
            }else{
                var other =''
            }
            url='<?php echo $this->createUrl('daily'); ?>&date='+date+'&lunch='+lunch+'&cup='+cup+'&bed='+bed+'&toilet='+toilet+'&goodsList='+goodsList+'&other='+other
        }
        var instanceApi = editor.windowManager.openUrl({
            onMessage: function (dialogApi, details) {
                switch (details.mceAction) {
                    case 'loading':
                        dialogApi.unblock()
                        break;
                    case 'cancel':
                        dialogApi.close()
                        break;
                    case 'saveData':
                        container.tinymceList=details.content
                        details.content.goodsList=details.content.goodsList.map(str => parseInt(str))
                        let html="<div   style='border:1px solid #E5E6EB; padding:1em 0;border-radius:0.5em;margin-top:1.5em'>"+
                            "<div style='text-align:center'>"+
                                "<div><img style='width:10.625em;padding-top:0.25em;margin-bottom:0' src='https://m2.files.ivykids.cn/cloud01-file-8025768FnzblWj6uv_Fw2Aw2NjB77KrBWD6.png'></div>"+
                                "<div style='color:#333;font-size:0.88em;font-weight:bold' class='dataDate' data-id="+container.tinymceList.date+">"+container.tinymceList.date+"</div>"+
                            "</div>"
                            if(details.content.record){
                                html+="<div style='display:flex'>"+
                                    "<div style='flex:1;background: linear-gradient( 180deg, #FFEAEA 50%, rgba(255,234,234,0.4) 100%);padding: 0.75em;border-radius: 4px;text-align: left;position: relative;margin: 2.25em 0.375em 2.25em 0.75em;'>"+
                                        "<div style='position: absolute;width:4em;height:4em;left: 50%;margin-left: -2em;top:-1.875em; background: #FFEAEA;border-radius: 50%; text-align: center;'>"+
                                            "<img style='width:4em;' src='https://m2.files.ivykids.cn/cloud01-file-8025768FkUBh84NxEq6rmKthSV4Q7D_u1qB.png' >"+
                                        "</div> "+
                                        "<div style='padding-top:1.125em;font-size:0.88em;color:#333'>今天午餐我吃了...</div>"+
                                        "<div style='font-size:0.75em;color:#666;line-height:1.125em'>Today at lunch I…</div>"+
                                        "<div style='padding-top:0.75em;font-size:0.88em;color:#4D88D2;font-weight:500'  class='dataLunch' data-id="+container.tinymceList.lunch+">"+container.dailyLife[container.tinymceList.lunch]+"</div>"+
                                        "<div style='font-size:0.75em;color:rgba(77, 136, 210, 0.7);font-weight:500;border-bottom: 1px solid rgba(77, 136, 210, 0.5);padding-bottom: 0.75em;line-height:1.125em'>"+container.dailyLifeEn[container.tinymceList.lunch]+"</div>"+
                                    "</div>"+
                                    "<div style='flex:1;background: linear-gradient( 180deg, #EAF2FF 50%, rgba(234,242,255,0.4) 100%);padding: 0.75em;border-radius: 4px;text-align: left;position: relative;margin: 2.25em 0.75em 2.25em 0.375em;'>"+
                                        "<div style='position: absolute;width:4em;height:4em;left: 50%;margin-left: -2em;top:-1.875em; background: #EAF2FF;border-radius: 50%; text-align: center;'>"+
                                            "<img style='width:4em;' src='https://m2.files.ivykids.cn/cloud01-file-8025768FsfXQADFnNLwKsOhmN0_rQXHEw-e.png'>"+
                                        "</div>"+
                                        "<div style='padding-top:1.125em;font-size:0.88em;color:#333'>今天我喝水...</div>"+
                                        "<div style='font-size:0.75em;color:#666;line-height:1.125em'>Today I drink water…</div>"+
                                        "<div style='padding-top:0.75em;font-size:0.88em;color:#4D88D2;font-weight:500' class='dataCup' data-id="+container.tinymceList.cup+">"+container.dailyLife[container.tinymceList.cup]+"</div>"+
                                        "<div style='font-size:0.75em;color:rgba(77, 136, 210, 0.7);border-bottom: 1px solid rgba(77, 136, 210, 0.5);padding-bottom: 0.75em;font-weight:500;line-height:1.125em'>"+container.dailyDrinkEn[container.tinymceList.cup]+"</div>"+
                                    "</div>"+
                                "</div>"+
                                "<div style='display:flex'>"+
                                    "<div style='flex:1;background: linear-gradient( 180deg, #EDFFE9 50%, rgba(237,255,233,0.4) 100%);;padding: 0.75em;border-radius: 4px;text-align: left;position: relative;margin: 0.375em 0.375em 0 0.75em'>"+
                                        "<div style='position: absolute;width:4em;height:4em;left: 50%;margin-left: -2em;top:-1.875em; background: #EDFFE9;border-radius: 50%; text-align: center;'>"+
                                            "<img style='width:4em;' src='https://m2.files.ivykids.cn/cloud01-file-8025768Fv_vBUdhPiVUDmtNxAYcD9AYbnxx.png'>"+
                                        "</div> "+
                                        "<div style='padding-top:1.125em;font-size:0.88em;color:#333'>今天午睡时间我...</div>"+
                                        "<div style='font-size:0.75em;color:#666;line-height:1.125em'>At nap time I…</div>"+
                                        "<div style='padding-top:0.75em;font-size:0.88em;color:#4D88D2;font-weight:500' class='dataBed' data-id="+container.tinymceList.bed+">"+container.dailyLifeTime[container.tinymceList.bed]+"</div>"+
                                        "<div style='font-size:0.75em;color:rgba(77, 136, 210, 0.7);border-bottom: 1px solid rgba(77, 136, 210, 0.5);padding-bottom: 0.75em;font-weight:500;line-height:1.125em'>"+container.dailyLifeTimeEn[container.tinymceList.bed]+"</div>"+
                                    "</div>"+
                                    "<div style='flex:1;background: linear-gradient( 180deg, #FFF5DD 50%, rgba(255,245,221,0.4) 100%);;padding: 0.75em;border-radius: 4px;text-align: left;position: relative;margin: 0.375em 0.75em 0 0.375em'>"+
                                        "<div style='position: absolute;width:4em;height:4em;left: 50%;margin-left: -2em;top:-1.875em; background: #FFF5DD;border-radius: 50%; text-align: center;'>"+
                                            "<img style='width:4em;' src='https://m2.files.ivykids.cn/cloud01-file-8025768FlKIg-MUCx8oNtvpXYZmJhPXmV4U.png'>"+
                                        "</div>"+
                                        "<div style='padding-top:1.125em;font-size:0.88em;color:#333'>我今天大便了吗？</div>"+
                                        "<div style='font-size:0.75em;color:#666;line-height:1.125em'>Did I go poo-poo ?</div>"+
                                        "<div style='padding-top:0.75em;font-size:0.88em;color:#4D88D2;font-weight:500' class='dataToilet' data-id="+container.tinymceList.toilet+">"+container.dailyLifePoo[container.tinymceList.toilet]+"</div>"+
                                        "<div style='font-size:0.75em;color:rgba(77, 136, 210, 0.7);border-bottom: 1px solid rgba(77, 136, 210, 0.5);padding-bottom: 0.75em;font-weight:500;line-height:1.125em'>"+container.dailyLifePooEn[container.tinymceList.toilet]+"</div>"+
                                    "</div>"+
                                "</div>"
                            }
                            if(details.content.goods){
                                html+="<div style='background: linear-gradient( 180deg, #E7FDFF 50%, rgba(234,255,252,0.5) 100%);border-radius: 4px;position: relative;margin:0.75em 0.75em 0;text-align: left'>"+
                                    "<div style='display:flex;'>"+
                                        "<img style='width:4em;height:4em;margin-bottom:0' src='https://m2.files.ivykids.cn/cloud01-file-8025768Fq8sZQgJ8lMCppQnEGholM0ImQa3.png'>"+
                                        "<div style='flex:1'>"+
                                            "<div style='padding-top:1.125em;font-size:0.88em;color:#333'>需要带到学校的物品</div>"+
                                            "<div style='font-size:0.75em;color:#666;line-height:1.125em'>Reminder: Supplies Needed</div>"+
                                        "</div>"+
                                   "</div>"
                                    html+="<div style='padding:0 0.75em 0.75em'><div style='display:flex;' data-id="+details.content.goodsList+" class='datagoodsList'>"+
                                           "<div style='flex:1;background:#FFFFFF;padding:0.25em 0.5em;display:flex;align-items:center'>"
                                                if(details.content.goodsList.indexOf(0)!=-1){
                                                    html+="<div><img style='width:1em;height:1em;margin-right:0.625em;margin-bottom:0' src='https://m2.files.ivykids.cn/cloud01-file-8025768Fo5XcRODiAEGMJ6WkSjrFx9WLm4S.png'></div>"+
                                                        "<div style='flex:1'>"+
                                                            "<div style='font-size:0.88em;color:#4D88D2;font-weight:500'>"+container.goodsListCn[0]+"</div>"+
                                                            "<div style='font-size:0.75em;color:rgba(77, 136, 210, 0.7);font-weight:500;line-height:1.125em'>"+container.goodsListEn[0]+"</div>"+
                                                        "</div>"+
                                                    "</div>"
                                                }else{
                                                    html+="<div><img style='width:1em;height:1em;margin-right:0.625em;margin-bottom:0' src='https://m2.files.ivykids.cn/cloud01-file-8025768Fi6w_SymakhZR4BHUvbCTrp6A3pq.png'></div>"+
                                                        "<div style='flex:1'>"+
                                                            "<div style='font-size:0.88em;color:#999999;font-weight:500'>"+container.goodsListCn[0]+"</div>"+
                                                            "<div style='font-size:0.75em;color:rgba(153, 153, 153, 0.7);font-weight:500;line-height:1.125em'>"+container.goodsListEn[0]+"</div>"+
                                                        "</div>"+
                                                    "</div>"
                                                }
                                                html+="<div style='flex:1;background:#FFFFFF;padding:0.25em 0.5em;display:flex;align-items:center;margin-left:0.5em'>"
                                                if(details.content.goodsList.indexOf(1)!=-1){
                                                    html+="<div><img style='width:1em;height:1em;margin-right:0.625em;margin-bottom:0' src='https://m2.files.ivykids.cn/cloud01-file-8025768Fo5XcRODiAEGMJ6WkSjrFx9WLm4S.png'></div>"+
                                                        "<div style='flex:1'>"+
                                                            "<div style='font-size:0.88em;color:#4D88D2;font-weight:500'>"+container.goodsListCn[1]+"</div>"+
                                                            "<div style='font-size:0.75em;color:rgba(77, 136, 210, 0.7);font-weight:500;line-height:1.125em'>"+container.goodsListEn[1]+"</div>"+
                                                        "</div>"+
                                                    "</div>"
                                                }else{
                                                    html+="<div><img style='width:1em;height:1em;margin-right:0.625em;margin-bottom:0' src='https://m2.files.ivykids.cn/cloud01-file-8025768Fi6w_SymakhZR4BHUvbCTrp6A3pq.png'></div>"+
                                                        "<div style='flex:1'>"+
                                                            "<div style='font-size:0.88em;color:#999999;font-weight:500'>"+container.goodsListCn[1]+"</div>"+
                                                            "<div style='font-size:0.75em;color:rgba(153, 153, 153, 0.7);font-weight:500;line-height:1.125em'>"+container.goodsListEn[0]+"</div>"+
                                                        "</div>"+
                                                    "</div>"
                                                }
                                            html+="</div>"
                                            html+="<div style='display:flex;margin-top:0.5em'>"
                                            html+="<div style='flex:1;background:#FFFFFF;padding:0.25em 0.5em;display:flex;align-items:center'>"
                                                if(details.content.goodsList.indexOf(2)!=-1){
                                                    html+="<div><img style='width:1em;height:1em;margin-right:0.625em;margin-bottom:0' src='https://m2.files.ivykids.cn/cloud01-file-8025768Fo5XcRODiAEGMJ6WkSjrFx9WLm4S.png'></div>"+
                                                        "<div style='flex:1'>"+
                                                            "<div style='font-size:0.88em;color:#4D88D2;font-weight:500'>"+container.goodsListCn[2]+"</div>"+
                                                            "<div style='font-size:0.75em;color:rgba(77, 136, 210, 0.7);font-weight:500;line-height:1.125em'>"+container.goodsListEn[2]+"</div>"+
                                                        "</div>"+
                                                    "</div>"
                                                }else{
                                                    html+="<div><img style='width:1em;height:1em;margin-right:0.625em;margin-bottom:0' src='https://m2.files.ivykids.cn/cloud01-file-8025768Fi6w_SymakhZR4BHUvbCTrp6A3pq.png'></div>"+
                                                        "<div style='flex:1'>"+
                                                            "<div style='font-size:0.88em;color:#999999;font-weight:500'>"+container.goodsListCn[2]+"</div>"+
                                                            "<div style='font-size:0.75em;color:rgba(153, 153, 153, 0.7);font-weight:500;line-height:1.125em'>"+container.goodsListEn[2]+"</div>"+
                                                        "</div>"+
                                                    "</div>"
                                                }
                                                html+="<div style='flex:1;background:#FFFFFF;padding:0.25em 0.5em;display:flex;align-items:center;margin-left:0.5em'>"
                                                if(details.content.goodsList.indexOf(3)!=-1){
                                                    html+="<div><img style='width:1em;height:1em;margin-right:0.625em;margin-bottom:0' src='https://m2.files.ivykids.cn/cloud01-file-8025768Fo5XcRODiAEGMJ6WkSjrFx9WLm4S.png'></div>"+
                                                        "<div style='flex:1'>"+
                                                            "<div style='font-size:0.88em;color:#4D88D2;font-weight:500'>"+container.goodsListCn[3]+"</div>"+
                                                            "<div style='font-size:0.75em;color:rgba(77, 136, 210, 0.7);font-weight:500;line-height:1.125em'>"+container.goodsListEn[3]+"</div>"+
                                                        "</div>"+
                                                    "</div>"
                                                }else{
                                                    html+="<div><img style='width:1em;height:1em;margin-right:0.625em;margin-bottom:0' src='https://m2.files.ivykids.cn/cloud01-file-8025768Fi6w_SymakhZR4BHUvbCTrp6A3pq.png'></div>"+
                                                        "<div style='flex:1'>"+
                                                            "<div style='font-size:0.88em;color:#999999;font-weight:500'>"+container.goodsListCn[3]+"</div>"+
                                                            "<div style='font-size:0.75em;color:rgba(153, 153, 153, 0.7);font-weight:500;line-height:1.125em'>"+container.goodsListEn[3]+"</div>"+
                                                        "</div>"+
                                                    "</div>"
                                                }
                                            html+="</div>"

                                        // }
                                        if(details.content.other!=''){
                                            html+="<div style='border-bottom: 1px solid rgba(77, 136, 210, 0.5);padding-bottom: 0.75em;margin-top:0.5em'>"+
                                                "<div style='background:#FFFFFF;padding:0.5em 0.88em; font-size:0.88em;color:#4D88D2;font-weight:500' data-id="+details.content.other+" class='dataother'><span style='color:#666666'><?php echo Yii::t("newDS", "Other");?>：</span> "+details.content.other+"</div>"+
                                            "</div>"
                                        }
                                "</div></div>"
                            }
                        html+="</div>"
                        if(type=='show'){
                            var selectedNode = editor.selection.getNode();
                            const element = editor.dom.get(selectedNode.id);
                            if (element) {
                                var newNode = editor.dom.doc.createElement('div');
                                newNode.setAttribute('id', selectedNode.id);
                                newNode.setAttribute('class', 'non-editable borderInsert');
                                newNode.innerHTML =html;
                                editor.dom.replace(newNode, element);
                            }
                            editor.setContent(editor.getBody().innerHTML);
                        }else{
                            let elements = Array.from(editor.getBody().querySelectorAll('.borderInsert'));
                            let maxId = 0;
                            elements.forEach(div => {
                                const currentId = parseInt(div.id);
                                if (currentId > maxId) {
                                    maxId = currentId;
                                }
                            });
                            if (elements.length > 0) {
                                var len=maxId+1
                            }else{
                                var len=0
                            }
                            let data='<div id="'+len+'" class="non-editable borderInsert">'+html+'</div>'
                            editor.insertContent(data);
                        }
                        if(container.journalContent.title.trim()==''){
                            container.journalContent.title='每日生活照顾 Daily Care Summary '+container.tinymceList.date
                        }
                        dialogApi.close()
                    break;
                }
            },
            title: '<?php echo Yii::t("newDS", "Daily Care Summary"); ?>',
            url:url,
            height:710,
            width: 500,
            scrollbars:false
        });
        instanceApi.block('loading')
    }
    window.addEventListener('scroll', function(){
        let t = $('body, html').scrollTop();   // 目前监听的是整个body的滚动条距离
        if(t>0){
            $('.box-active').addClass('box-shadow')
        }else{
            $('.box-active').removeClass('box-shadow')
        }
    })
    var journal_id = '<?php echo Yii::app()->request->getParam('journal_id', '')?>';
    var clone = '<?php echo Yii::app()->request->getParam('clone', '')?>';
    var container = new Vue({
        el: "#container",
        data: {
            Watermark:'',
            WatermarkImg:'<?php echo (isset($this->schoolType) && $this->schoolType == 'ds') ? '!v1000' : '!i1000'; ?>',
            isClone:clone,
            journal_id:journal_id,
            autoSave:false, //自动保存
            journalData:{}, //初始化数据
            journalContent:{}, //保存数据
            postObjectList:{}, //发布对象
            // releaseId:'', //授权发布人id
            tabActive:"class",
            classId:'',
            selected:[],
            classData:{},
            groupType:'',
            groupSelect:[],
            groupTitle:'',
            groupList:[],
            groupListChild:[],
            groupId:'',
            editGroupId:'',
            childLoading:false,
            isEdit:false,
            courseList:[],
            courseId:'',
            courseData:{},
            authorizedName:'', //授权搜索
            authorizedList:{}, //授权列表
            teacherUid:'', //授权老师id
            classList:[], //班级列表
            token:'',
            publishDate:'',
            publishTime:'',
            masks: {
                inputDateTime24hr: 'YYYY-MM-DD HH:mm',
            },
            modelConfig: {
                type: 'string',
                mask: 'YYYY-MM-DD HH:mm', // Uses 'iso' if missing
            },
            disabledUpload:false,
            linesCount:0,
            publishType:'',
            textareaHtml:'',
            delType:'',
            progressData:{},
            options: [],
            loading: false,
            asaCourseGroupList:[],//每学年下所有的课程分组
            selectYear:'',//当前选中的学年
            selectedAsaCourseGroupList:[],//选中学年下的课程组
            selectedAsaCourseGroupId:'',//选中的课程组id
            selectedAsaCourseList:[],//选中课程组中的课程
            selectedAsaCourseId:'',//选中的课后课课程id
            asaCourseChildList:[],//参加课后课的学生
            asaCourseGroupLoading:false,//加载课后课分组loding
            asaCourseLoading:false,//加载课后课分组中的课程loding
            asaCourseChildLoading:false,//加载课程中的孩子loding
            searchChild:'',
            searchStu:false,
            searchChildList:[],
            TimeId: -1,
            delStatus:'',
            dailyLife:{1:'很多',2:'合适',3:'很少'},
            dailyLifeEn:{1:'Ate a lot',2:'Ate enough',3:'Ate a little'},
            dailyDrinkEn:{1:'Drink a lot',2:'Drink enough',3:'Drink a little'},
            dailyLifeTime:{1:'1-2 小时',2:'少于1小时',3:'没睡'},
            dailyLifeTimeEn:{1:'Slept 1-2hrs',2:'Slept less than 1 hour',3:"Didn't sleep"},
            dailyLifePoo:{1:'正常',2:'有点干',3:'没有'},
            dailyLifePooEn:{1:"Yes, it's normal",2:"Yes, it's hard",3:'No'},
            goodsListCn:["纸尿裤","湿巾",'防晒霜','衣服'],
            goodsListCn1:{1:"纸尿裤",2:"湿巾",3:'防晒霜',4:'衣服'},
            goodsListEn:["Diapers","Wipes",'Sunscreen','Clothes'],
            goodsListEn1:{1:"Diapers",2:"Wipes",3:'Sunscreen',4:'Clothes'},
        },
        watch: {
            journalContent: {
                deep: true, // deep 为true  意味着开启了深度监听 a对象里面任何数据变化都会触发handler函数,
                immediate:true,
                handler(old,nesw){
                    this.onGetLines()
                    if(this.autoSave){
                        this.saveData()
                    }
                }
            },
            postObjectList:function(newVal,oldVal){
                if(Object.keys(this.postObjectList).length!=0){
                    this.journalContent.is_new=false
                }
            },
            searchChild:function(newVal,oldVal){
                clearTimeout(this.timeout)
                this.timeout = setTimeout(() => {
                    if(newVal!=''){
                        this.getSearchStudent()
                    }
                }, 1000)
            },
        },
        created: function() {
            let that=this
            $.ajax({
                url: '<?php echo $this->createUrl("journals/classList") ?>',
                type: "post",
                dataType: 'json',
                data: {
                },
                success: function(data) {
                    if (data.state == 'success') {
                       data.data.forEach(item => {
                            item.childData=[]
                        })
                       that.classList=data.data
                       that.allChild()
                    }else{
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                },
                error: function(data) {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                },
            })

            // this.allChild()
            this.initData()
           
        },

        mounted() {
        //     window.onbeforeunload = function (e) {
        //         e = e || window.event;
        //         // 兼容ie8和firefox 4之前的版本
        //         if (e) {
        //         e.returnvalue = '关闭提示';
        //         }
        //         // chrome, safari, firefox 4+, opera 12+ , ie 9+
        //         return '关闭提示';
        //     }
        },
        // destroyed() {
        //     window.removeEventListener(‘beforeunload’, () => this.beforeunloadHandler());
        //     window.removeEventListener(‘unload’, () => this.unloadFn());
        // },
        methods: {
            cancalReadOnly(onOff) {
                this.$nextTick(() => {
                    if (!onOff) {
                        const Selects = this.$refs
                        if (Selects.agentSelect) {
                            const input = Selects.agentSelect.$el.querySelector('.el-input__inner')
                            input.removeAttribute('readonly')
                        }
                    }
                })
            },
            getSearchStudent(){
                let that = this
                this.searchStu=true
                var childId=[]
                if(this.selected!=null){
                    for(var i=0;i<this.selected.length;i++){
                        childId.push(+this.selected[i].id)
                    }
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/searchStudent") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        child_name:this.searchChild
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            for(var i=0;i<data.data.length;i++){
                                data.data[i].stuLoading=true
                                if (childId.indexOf(data.data[i].id)!=-1) {
                                    data.data[i].disabled=true
                                }else{
                                    data.data[i].disabled=false
                                }
                            }
                            that.searchChildList=[]
                            that.searchChildList.push({
                                childData:data.data
                            })
                            that.searchStu=false
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.searchStu=false
                        }
                    },
                    error: function(data) {
                        that.searchStu=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })  
            },
            onGetLines() {
                this.$nextTick(() => {
                    if(document.querySelector('#textarea')){
                        var tmp = document.querySelector('#textarea').value;
                        if(tmp==''){
                            this.linesCount=1
                        }else{
                            this.textareaHtml=tmp.split("\n").join("<br/>")
                            this.$nextTick(() => {
                            var rowNum=Math.round($("#testSpanForCheck").height()/parseFloat($("#testSpanForCheck").css('line-height')));
                                this.linesCount=rowNum
                            })
                        }

                    }
                })

            },
            delConfirm(){
                if(this.delType=='child'){
                    this.batchDel()
                }else if(this.delType=='Joint'){
                    this.clearJoint()
                }else if(this.delType=='del'){
                    this.delJournal()
                }
            },
            initData(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("edit") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        journal_id:this.journal_id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                        that.journal_id=data.data.journalId
                        that.journalContent=data.data.journalData
                        that.journalData=data.data
                        if(data.data.signAsUidList.length==0 && that.journalContent.sign_as_uid==0){
                            that.journalContent.sign_as_uid=data.data.userid
                        }
                        that.$nextTick(() => {
                            newTinymce()
                            that.getQiniu()
                        })
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            getQiniu(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/getQiniuToken") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        linkId: this.journal_id,
                        linkType:'journal'
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.token=data.data.data
                            config['token'] = data.data.data;
                            var uploader = new plupload.Uploader(config);
                            uploader.init();
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            allChild(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("subscribers") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        journal_id:this.journal_id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            var list=[]
                            that.classList.forEach((item,index) => {
                                list.push(item.title)
                            })
                            var sortData = Object.values(data.data).sort((a, b) => {
                                return list.indexOf(a.class_name) - list.indexOf(b.class_name)
                            })
                            that.postObjectList=sortData
                            that.selected=sortData
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            delFile(list,index) {
                var that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/deleteAttachment") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        attachmentId:list._id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.journalContent.attachments.forEach((item,index) => {
                                if (item._id==list._id) {
                                    that.journalContent.attachments.splice(index, 1)
                                }
                            })
                            resultTip({
                                msg: data.state
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            autoSaveContent(){

            },
            saveData(type){
                // this.journalContent.sign_as_uid=this.releaseId
                this.journalContent.content= tinymce.get('tinymceCn').getContent()
                if(this.journalContent.content=='' && this.journalContent.title=='' && Object.keys(this.postObjectList).length==0 && Object.keys(this.journalContent.joint_admins).length==0){
                    resultTip({
                        error: 'warning',
                        msg: '<?php echo Yii::t("directMessage", "You cannot save an empty DM"); ?>'
                    });
                    return
                }

                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("save") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        journal_id:this.journal_id,
                        data:that.journalContent
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.journalContent=that.journalContent
                            that.journalContent.is_new=false
                            resultTip({
                                msg: data.state
                            });
                            if(type){
                                window.location.href='<?php echo $this->createUrl('index'); ?>'
                            }
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            addUser(){
                this.teacherUid=''
                this.options=[]
                $('#authorizedModal').modal('show')
            },
            remoteMethod(query) {
                let that=this
                if (query !== '') {
                    this.loading = true;
                    $.ajax({
                        url: '<?php echo $this->createUrl("teacherSearch") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            searchString:query
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.options = Object.values(data.data) ;
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                            that.loading = false;
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.loading = false;
                        },
                    })
                } else {
                    this.options = [];
                }
            },
            confirmAdmin(){
                let that=this
                if(this.journalContent.joint_admins.indexOf(this.teacherUid)!=-1){
                    resultTip({
                        error: 'warning',
                        msg: '<?php echo Yii::t("directMessage", "Already added"); ?>'
                    });
                    return
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("jointAdminsAdd") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        journal_id:this.journal_id,
                        staff_id:this.teacherUid
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           that.journalContent.joint_admins.push(that.teacherUid)
                           that.journalData.userInfo[that.teacherUid]=data.data
                           that.journalContent.is_new=false
                           that.teacherUid=''
                           that.options=[]
                           resultTip({
                                msg:data.state
                            });
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            clearJoint(data){
                if(data){
                    this.delType='Joint'
                    $('#delModal').modal('show')
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("jointAdminsDelAll") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        journal_id:this.journal_id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.journalContent.joint_admins=[]
                            resultTip({
                                msg:data.state
                            });
                            $('#delModal').modal('hide')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            delJoint(id){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("jointAdminsDel") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        journal_id:this.journal_id,
                        staff_id:id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            var index = that.journalContent.joint_admins.indexOf(id);
                            if (index > -1) {
                                that.journalContent.joint_admins.splice(index, 1);
                            }
                            resultTip({
                                msg:data.state
                            });

                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            addChild(){
                $('#addClassModal').modal('show')
            },
            tabLabel(type){
                this.tabActive=type
                var that=this
                this.groupId=''
                this.classId=''
                this.courseId=''
                if(type=='custom'){
                    $.ajax({
                        url: '<?php echo $this->createUrl("journals/getGroupLink") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                for(var i=0;i<data.data.length;i++){
                                    data.data[i].childData=[]
                                }
                                that.groupList=data.data
                            } else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                        },
                        error: function(data) {
                        },
                    })
                }
                if(type=='profile'){
                    $.ajax({
                        url: '<?php echo $this->createUrl("journals/getCourseList") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            type:'course'
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                for(var i=0;i<data.data.list.length;i++){
                                    data.data.list[i].childData=[]
                                }
                                that.courseList=data.data.list
                            } else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                        },
                        error: function(data) {
                        },
                    })
                }
                if(type=='asa'){
                    that.asaCourseGroupLoading = true
                    $.ajax({
                        url: '<?php echo $this->createUrl("journals/asaCourseGroupList") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {},
                        success: function(data) {
                            if (data.state == 'success') {
                                if(data.data.length!=0){
                                    that.asaCourseGroupList = data.data
                                    //默认学年
                                    that.selectYear = data.data[0]['startyear']
                                    that.chooseYear(that.selectYear)
                                }else{

                                }
                                that.asaCourseGroupLoading = false
                            } else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                        },
                        error: function(data) {
                        },
                    })
                }
            },
            //选择学年
            chooseYear(year){
                let that=this
                that.selectYear = year
                //展示学年下的课程组
                // selectedAsaCourseGroupList
                for (var i=0; i<that.asaCourseGroupList.length;i++){
                    if(year == that.asaCourseGroupList[i].startyear){
                        that.selectedAsaCourseGroupList= that.asaCourseGroupList[i].list
                        break;
                    }
                }
            },
            //课程组下的课程信息
            getAsaCourseList(asaCourseGroupId){
                let that=this
                if(that.selectedAsaCourseGroupId==asaCourseGroupId){
                    that.selectedAsaCourseGroupId = ''
                    return
                }
                that.selectedAsaCourseGroupId = asaCourseGroupId
                that.asaCourseLoading = true
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/asaCourseList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        group_id:asaCourseGroupId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            for(var i=0;i<data.data.length;i++){
                                data.data[i].childData = []
                            }
                            that.selectedAsaCourseList = data.data
                            that.asaCourseLoading = false
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                    },
                })
            },
            //获取课程下的孩子
            getAsaCourseChild: function (asaCourseId) {
                let that = this
                if (that.selectedAsaCourseId == asaCourseId) {
                    that.selectedAsaCourseId = ''
                    return;
                }
                that.selectedAsaCourseId = asaCourseId
                that.asaCourseChildLoading = true
                var childId=[]
                if(this.selected!=null){
                    for(var i=0;i<this.selected.length;i++){
                        childId.push(+this.selected[i].id)
                    }
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/asaCourseStudentList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:asaCourseId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.asaCourseChildList=data.data
                            for(var i=0;i<data.data.length;i++){
                                if (childId.indexOf(data.data[i].id)!=-1) {
                                    data.data[i].disabled=true
                                }else{
                                    data.data[i].disabled=false
                                }
                            }
                            for(var i=0;i<that.selectedAsaCourseList.length;i++){
                                if(that.selectedAsaCourseList[i].id == asaCourseId){
                                    that.selectedAsaCourseList[i].childData=data.data
                                }
                            }
                            that.asaCourseChildLoading = false
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            getchildIds(){
                var childId=[]
                if(this.selected!=null){
                    for(var i=0;i<this.selected.length;i++){
                        childId.push(this.selected[i].id)
                    }
                }
                return childId
            },
            getGroupList(list,type){
                let that=this
                if(!type){
                    if(that.groupId==list.id){
                        that.groupId=''
                        return
                    }
                }
                that.groupId=list.id
                that.childLoading=true
                var childId=[]
                if(this.selected!=null){
                    for(var i=0;i<this.selected.length;i++){
                        childId.push(+this.selected[i].id)
                    }
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/getGroupLinkChild") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:list.id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.sortData(data.data)
                            if(type){
                                that.groupSelect=data.data
                            }
                            for(var i=0;i<data.data.length;i++){
                                data.data[i].stuLoading=true
                                data.data[i].classTitle= data.data[i].class_name
                                data.data[i].photo= data.data[i].avatar
                                if (childId.indexOf(data.data[i].id)!=-1) {
                                    data.data[i].disabled=true
                                }else{
                                    data.data[i].disabled=false
                                }
                            }
                            for(var i=0;i<that.groupList.length;i++){
                                if(that.groupList[i].id==list.id){
                                    that.groupList[i].childData=data.data
                                }
                            }
                            that.childLoading=false
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            getChild(list,type){
                var that=this
                if(that.classId==list.classid){
                    that.classId=''
                    return
                }
                that.classId=list.classid
                this.classData=list
                var childId=[]
                if(this.selected!=null){
                    for(var i=0;i<this.selected.length;i++){
                        childId.push(this.selected[i].id+'')
                    }
                }
                for(var i=0;i<that.classList.length;i++){
                    if(that.classList[i].classid==list.classid){
                        if(that.classList[i].childData.length!=0){
                            for(var j=0;j<that.classList[i].childData.length;j++){
                                if (childId.indexOf(that.classList[i].childData[j].id)!=-1) {
                                    that.classList[i].childData[j].disabled=true
                                }else{
                                    that.classList[i].childData[j].disabled=false
                                }
                            }
                            that.$forceUpdate()
                            return
                        }
                    }
                }
                that.childLoading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/childList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        classId:list.classid
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            for(var i=0;i<data.data.length;i++){
                                data.data[i].classTitle=list.title
                                data.data[i].stuLoading=true
                                if(type){
                                    data.data[i].disabled=false
                                }else{
                                    if (childId.indexOf(data.data[i].id)!=-1) {
                                        data.data[i].disabled=true
                                    }else{
                                        data.data[i].disabled=false
                                    }
                                }
                            }
                            that.sortData(data.data)
                            for(var i=0;i<that.classList.length;i++){
                                if(that.classList[i].classid==list.classid){
                                    that.classList[i].childData=data.data
                                }
                            }
                            that.childLoading=false
                            that.$forceUpdate()
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            getCourseChild(list,type){
                var that=this
                if(that.courseId==list.id){
                    that.courseId=''
                    return
                }
                that.courseId=list.id
                this.courseData=list
                var childId=[]
                if(this.selected!=null){
                    for(var i=0;i<this.selected.length;i++){
                        childId.push(this.selected[i].id)
                    }
                }
                for(var i=0;i<that.courseList.length;i++){
                    if(that.courseList[i].id==list.id){
                        if(that.courseList[i].childData.length!=0){
                            for(var j=0;j<that.courseList[i].childData.length;j++){
                                if (childId.indexOf(that.courseList[i].childData[j].id)!=-1) {
                                    that.courseList[i].childData[j].disabled=true
                                }else{
                                    that.courseList[i].childData[j].disabled=false
                                }
                            }
                            that.$forceUpdate()
                            return
                        }
                    }
                }
                that.childLoading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/getCourseChild") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        courseId:list.id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            for(var i=0;i<data.data.length;i++){
                                data.data[i].classTitle=list.title
                                data.data[i].stuLoading=true
                                if(type){
                                    data.data[i].disabled=false
                                }else{
                                    if (childId.indexOf(data.data[i].id)!=-1) {
                                        data.data[i].disabled=true
                                    }else{
                                        data.data[i].disabled=false
                                    }
                                }
                            }
                            that.sortData(data.data)
                            for(var i=0;i<that.courseList.length;i++){
                                if(that.courseList[i].id==list.id){
                                    that.courseList[i].childData=data.data
                                }
                            }
                            that.childLoading=false
                            that.$forceUpdate()
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            selectAll(list,type){
                var targetId=[]
                for(var i=0;i<list.childData.length;i++){
                    if (!list.childData[i].disabled) {
                        targetId.push(list.childData[i].id)
                    }
                }
                if(targetId.length==0){
                    resultTip({
                        error: 'warning',
                        msg:'<?php echo Yii::t("newDS", "All added already.");?>'
                    });
                    return
                }
               this.assignChildren(targetId,'all',type)
            },
            addLoading(list,type,dataType){
                for(var i=0;i<this[dataType].length;i++){
                    for(var j=0;j<this[dataType][i].childData.length;j++){
                        if(type=='all'){
                        if (list.indexOf(this[dataType][i].childData[j].id)!=-1) {
                                Vue.set(this[dataType][i].childData[j], 'stuLoading', false);
                                this.$forceUpdate()
                            }
                        }
                        if(list.id==this[dataType][i].childData[j].id){
                            Vue.set(this[dataType][i].childData[j], 'stuLoading', false);
                            this.$forceUpdate()
                        }
                    }
                }
            },
            assignChildren(list,type,dataType){
                var data=[]
                let that=this
                this.addLoading(list,type,dataType)
                that.$forceUpdate()
                $.ajax({
                    url: '<?php echo $this->createUrl("targetsAdd") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        journal_id:this.journal_id,
                        targets:type=='all'?list:[parseInt(list.id)]
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            if(data.data==1){
                                for(var i=0;i<that[dataType].length;i++){
                                    if(dataType=='classList'){
                                        if(that[dataType][i].classid==that.classId){
                                            that.shuttle(list,type,dataType,i)
                                        }
                                    }else if(dataType=='courseList'){
                                        if(that[dataType][i].id==that.courseId){
                                            that.shuttle(list,type,dataType,i)
                                        }
                                    }else if(dataType=='searchChildList'){
                                        that.shuttle(list,type,dataType,0)
                                    }else if(dataType=='selectedAsaCourseList'){
                                        if(that[dataType][i].id==that.selectedAsaCourseId){
                                            that.shuttle(list,type,dataType,i)
                                        }
                                    }else{
                                        if(that[dataType][i].id==that.groupId){
                                            that.shuttle(list,type,dataType,i)
                                        }
                                    }
                                }
                                that.allChild()
                            }
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            sortData(list){
                <?php if (Yii::app()->language == 'zh_cn') { ?>
                    list.sort((a, b)=> a.name.localeCompare(b.name, 'zh'));
                <?php } else { ?>
                    list.sort((a, b) => a.name.charCodeAt(0) - b.name.charCodeAt(0));
                <?php } ?>
            },
            shuttle(list,type,dataType,i){
                let that=this
                for(var j=0;j<that[dataType][i].childData.length;j++){
                    if(type=='all'){
                        if (list.indexOf(that[dataType][i].childData[j].id)!=-1) {
                            var data = {
                                'avatar':that[dataType][i].childData[j].photo,
                                'class_name':that[dataType][i].childData[j].classTitle,
                                'name':that[dataType][i].childData[j].name,
                                'loading':false,
                                'id':that[dataType][i].childData[j].id,
                                'asa_course_name':that[dataType][i].childData[j].course_name ? that[dataType][i].childData[j].course_name:'',
                            };
                            Vue.set(that[dataType][i].childData[j], 'disabled', true);
                            Vue.set(that[dataType][i].childData[j], 'stuLoading', true);
                            that.selected.push(data)
                            that.sortData(that.selected)

                        }
                    }else{
                        if(that[dataType][i].childData[j].id==list.id){
                            var data ={
                                'avatar':list.photo,
                                'class_name':list.classTitle,
                                'name':list.name,
                                'loading':false,
                                'id':list.id,
                                'asa_course_name':list.course_name ? list.course_name:'',
                            };
                            Vue.set(that[dataType][i].childData[j], 'disabled', true);
                            Vue.set(that[dataType][i].childData[j], 'stuLoading', true);
                            that.selected.push(data)
                            that.sortData(that.selected)
                        }
                    }
                }
                Vue.set(list, 'stuLoading', true);
            },
            Unassign(id,index){
                let that=this
                var dataType=''
                Vue.set(that.selected[index], 'loading', true);
                if(this.tabActive=='custom'){
                    dataType='groupList'
                }else if(this.tabActive=='asa'){
                    dataType='selectedAsaCourseList'
                }else if(this.tabActive=='search'){
                    dataType='searchChildList'
                }else{
                    dataType='classList'
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("targetsDel") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        journal_id:this.journal_id,
                        target_id:id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            if(data.data==1){
                                for(var i=0;i<that[dataType].length;i++){
                                    if(dataType=='classList'){
                                        if(that[dataType][i].classid==that.classId){
                                            that.delChild(dataType,i,id)
                                        }
                                    }else if(dataType=='selectedAsaCourseList'){
                                        if(that[dataType][i].id==that.selectedAsaCourseId){
                                            that.delChild(dataType,i,id)
                                        }
                                    }else if(dataType=='searchChildList'){
                                        that.delChild(dataType,0,id)
                                    }else{
                                        if(that[dataType][i].id==that.groupId){
                                            that.delChild(dataType,i,id)
                                        }
                                    }
                                }
                                that.selected.splice(index,1)
                                that.allChild()
                                that.$forceUpdate()
                                resultTip({
                                    msg: data.state
                                });
                            }
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            delChild(dataType,i,id){
                let that=this
                for(var j=0;j<that[dataType][i].childData.length;j++){
                    if(that[dataType][i].childData[j].id==id){
                        Vue.set(that[dataType][i].childData[j], 'disabled', false);
                    }
                }
            },
            addGroup(type){
                this.groupType=type
                this.groupSelect=[]
                this.groupTitle=''
                this.classId=''
                if(type=='edit'){
                    this.editGroupId=this.groupList[0].id
                    this.groupTitle=this.groupList[0].title
                    this.getGroupList(this.groupList[0],'edit')
                }
                $('#groupModal').modal('show')
            },
            getGroupChild(list,type){
                var that=this
                var groupChildId=[]
                if(that.classId==list.classid){
                    that.classId=''
                    return
                }
                that.classId=list.classid
                if(that.groupType=='edit'){
                    for(var i=0;i<this.groupSelect.length;i++){
                        groupChildId.push(this.groupSelect[i].id+'')
                    }
                }
                for(var i=0;i<that.classList.length;i++){
                    if(that.classList[i].classid==list.classid){
                        if(that.classList[i].childData.length!=0){
                            for(var j=0;j<that.classList[i].childData.length;j++){
                                if(that.groupType=='add'){
                                    that.classList[i].childData[j].groupDisabled=false
                                }else{
                                    if (groupChildId.indexOf(that.classList[i].childData[j].id)!=-1) {
                                        that.classList[i].childData[j].groupDisabled=true
                                    }else{
                                        that.classList[i].childData[j].groupDisabled=false
                                    }
                                }
                            }
                            return
                        }
                    }
                }
                this.childLoading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/childList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        classId:list.classid
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.sortData(data.data)
                            for(var i=0;i<data.data.length;i++){
                                data.data[i].classTitle=list.title
                                if(that.groupType=='add'){
                                    data.data[i].groupDisabled=false
                                }else{
                                    if (groupChildId.indexOf(data.data[i].id)!=-1) {
                                        data.data[i].groupDisabled=true
                                    }else{
                                        data.data[i].groupDisabled=false
                                    }
                                }
                            }
                            for(var i=0;i<that.classList.length;i++){
                                if(that.classList[i].classid==list.classid){
                                    that.classList[i].childData=data.data
                                }
                            }
                            that.childLoading=false
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            groupAll(list){
                var targetId=[]
                for(var i=0;i<list.childData.length;i++){
                    if (!list.childData[i].groupDisabled) {
                        targetId.push(list.childData[i].id)
                    }
                }
               this.addGroupChild(targetId,'all')
            },
            addGroupChild(list,type){
                let that=this
                for(var i=0;i<that.classList.length;i++){
                    if(that.classList[i].classid==that.classId){
                        for(var j=0;j<that.classList[i].childData.length;j++){
                            if(type=='all'){
                                if (list.indexOf(that.classList[i].childData[j].id)!=-1) {
                                    var data ={
                                        'avatar':that.classList[i].childData[j].photo,
                                        'class_name':that.classList[i].childData[j].classTitle,
                                        'name':that.classList[i].childData[j].name,
                                        'id':that.classList[i].childData[j].id
                                    };
                                    Vue.set(that.classList[i].childData[j], 'groupDisabled', true);
                                    that.groupSelect.push(data)
                                    that.sortData(that.groupSelect)
                                }
                            }else{
                                if(that.classList[i].childData[j].id==list.id){
                                    var data ={
                                        'avatar':list.photo,
                                        'class_name':list.classTitle,
                                        'name':list.name,
                                        'id':list.id
                                    };
                                    Vue.set(that.classList[i].childData[j], 'groupDisabled', true);
                                    that.groupSelect.push(data)
                                    that.sortData(that.groupSelect)
                                }
                            }
                        }
                    }
                }
            },
            delGroupChild(id,index){
                let that=this
                for(var i=0;i<that.classList.length;i++){
                    if(that.classList[i].classid==that.classId){
                        for(var j=0;j<that.classList[i].childData.length;j++){
                            if(that.classList[i].childData[j].id==id){
                                Vue.set(that.classList[i].childData[j], 'groupDisabled', false);
                            }
                        }
                    }
                }
                that.groupSelect.splice(index,1)
                that.$forceUpdate()
            },
            saveGroup(){
                var targetId=[]
                for(var i=0;i<this.groupSelect.length;i++){
                    targetId.push(this.groupSelect[i].id)
                }
                if(this.groupTitle==''){
                    resultTip({
                        error: 'warning',
                        msg: '<?php echo Yii::t("newDS", "Please input group title.");?>'
                    });
                    return
                }
                if(targetId.length==0){
                    resultTip({
                        error: 'warning',
                        msg: '请选择分组学生'
                    });
                    return
                }
                let that=this
                if(that.groupType=='edit'){
                    $.ajax({
                        url: '<?php echo $this->createUrl("journals/editGroupLink") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            title:this.groupTitle,
                            childIds:targetId,
                            id:that.editGroupId
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.groupId=''
                                that.tabLabel('custom')
                                $('#groupModal').modal('hide')
                            } else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                        },
                        error: function(data) {

                        },
                    })
                }else{
                    $.ajax({
                        url: '<?php echo $this->createUrl("journals/addGroupLink") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            title:this.groupTitle,
                            childIds:targetId
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.groupId=''
                                that.tabLabel('custom')
                                $('#groupModal').modal('hide')
                            } else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                        },
                        error: function(data) {

                        },
                    })
                }

            },
            viewGroup(list){
                for(var i=0;i<this.groupList.length;i++){
                    if(this.editGroupId==this.groupList[i].id){
                        this.groupTitle=this.groupList[i].title
                        this.getGroupList(this.groupList[i],'edit')
                    }
                }
            },
            delGroup(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/delGroupLink") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:this.editGroupId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.groupId=''
                            that.tabLabel('custom')
                            $('#groupModal').modal('hide')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            batchDel(data){
                if(data){
                    this.delType='child'
                    $('#delModal').modal('show')
                    return
                }
                let that=this
                var targetId=[]
                for(var i=0;i<this.selected.length;i++){
                    targetId.push(this.selected[i].id)
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("targetsDelAll") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        journal_id:this.journal_id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.groupId=''
                            that.selected=[]
                            that.classId=''
                            that.selectedAsaCourseId=''
                            resultTip({
                                msg: data.state
                            });
                            $('#delModal').modal('hide')
                            that.allChild()
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })

            },
            assignTime(type){
                this.publishType=type
                $('#assignTimeModal').modal('show')
            },
            publishConfirm(type){
                this.journalContent.content= tinymce.get('tinymceCn').getContent()
                if(this.journalContent.title==''){
                    resultTip({
                        error: 'warning',
                        msg: '<?php echo Yii::t("directMessage", "Title cannot be empty"); ?>'
                    });
                    return
                }
                if(this.journalContent.content==''){
                    resultTip({
                        error: 'warning',
                        msg: '<?php echo Yii::t("directMessage", "Please input content"); ?>'
                    });
                    return
                }
                if(Object.keys(this.postObjectList).length==0){
                    resultTip({
                        error: 'warning',
                        msg: '<?php echo Yii::t("newDS", "Add subscribers");?>！'
                    });
                    return
                }
                if(this.journalContent.sign_as_uid=='' || this.journalContent.sign_as_uid==0){
                    resultTip({
                        error: 'warning',
                        msg: '<?php echo Yii::t("directMessage", "Please choose a signature"); ?>'
                    });
                    return
                }
                if(type){
                    if(this.publishDate==''){
                        resultTip({
                            error: 'warning',
                            msg: '<?php echo Yii::t("directMessage", "Publish time"); ?>'
                        });
                        return
                    }
                }

                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("save") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        journal_id:this.journal_id,
                        data:that.journalContent
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            if(that.publishType=='edit'){
                                that.editPublish()
                            }else{
                                that.publishData()
                            }
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            editPublish(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("publishDate") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        journal_id:this.journal_id,
                        publishDate:this.publishDate
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg: data.state
                            });
                            window.location.href='<?php echo $this->createUrl('index'); ?>'
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            publishData(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("publish") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        journal_id:this.journal_id,
                        publishDate:this.publishDate
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg: data.state
                            });
                            window.location.href='<?php echo $this->createUrl('index'); ?>'
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            unpublish(list){
                if(list){
                    $('#unpublishModal').modal('show')
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("offline") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        journal_id:this.journal_id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg: data.state
                            });
                            window.location.href='<?php echo $this->createUrl('index'); ?>'
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            delJournal(list){
                if(list){
                    this.delType='del'
                    this.delStatus=1;
                    $('#delModal').modal('show')
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("delete") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        journal_id:this.journal_id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.delStatus=data.data
                            if(data.data==1){
                                window.location.href='<?php echo $this->createUrl('index'); ?>'
                                $('#delModal').modal('hide')
                                resultTip({
                                    msg:data.state
                                });
                            }else if(data.data==10){
                                that.delType='del'
                               $('#delModal').modal('show')
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg:'<?php echo Yii::t("newDS", "Cannot delete due to existence of parent comments."); ?>'
                                })
                            }
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            journalOffline(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("offline") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        journal_id:this.journal_id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            window.location.href='<?php echo $this->createUrl('index'); ?>'
                            $('#delModal').modal('hide')
                            resultTip({
                                msg:data.message
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg:data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg:data.message
                        });
                    },
                })
            },
        }
    })
    var config = {
        runtimes: 'html5,flash,html4', //上传模式,依次退化
        container: 'container', //上传区域DOM ID，默认是browser_button的父元素，
        browse_button: 'pickfilesPhoto', //上传选择的点选按钮，**必需**
        token: '',
        flash_swf_url: '/js/plupload/Moxie.swf', // flash的相对地址
        auto_start: true, //选择文件后自动上传，若关闭需要自己绑定事件触发上传
        multi_selection: false, //不允许多选
        url:'https://upload-z1.qiniup.com',

        init: {
            'FilesAdded': function(up, files) {
                container.disabledUpload=true
                container.progressData=files[0]
                up.start();
            },
            BeforeUpload: function(up, file) {
                //设置参数
                up.setOption({
                    multipart_params:{token:container.token}
                })
            },
            UploadProgress: function(up, file) {
                $('#progress').css('width', file.percent+'%').html(file.percent+'%');
            },
            'FileUploaded': function(up, file, info) {
                var response = eval('('+info.response+')');
                if(info.status == 200){
                    container.progressData={}
                    container.journalContent.attachments.push(response.data)
                    container.disabledUpload=false
                }
            },
            'Error': function(up, err, errTip) {
                if(err.response){
                    var response = eval('('+err.response+')');
                }
                else{
                    var response = {message: err.message};
                }
                if(response.error == 'token not specified'){
                    up.stop();
                    $.ajax({
                        url: '<?php echo $this->createUrl("journals/getQiniuToken") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            journalId: container.journal_id
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                container.uptoken=data.data.data
                                config['uptoken'] = data.data.data;
                                up.setOption("multipart_params", {"token":data.data.token,})
                                up.start();
                            } else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.msg
                                });
                            }
                        },
                        error: function(data) {

                        },
                    })
                }
            },
            'UploadComplete': function(up, file) {
                //队列文件处理完毕后,处理相关的事情
            }
        }
    };
</script>
