<?php $this->beginContent('//layouts/main'); ?>
    <nav class="navbar navbar-inverse navbar-fixed-top">
        <div class="navbar-header">
            <button type="button" class="navbar-toggle">
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>
            <label class="navbar-brand">IvyOnline</label>
            <?php echo CHtml::link('<span class="glyphicon glyphicon-option-vertical"></span>', array('//app/default/settings'), array('class'=>'aj_ajax settings'));?>
        </div>
    </nav>

    <nav class="spmenu-left">
        <div class="spmenu-header">
            <div class="col-xs-6 text-center">
                <?php echo CHtml::link(
                    CHtml::image(
                        CommonUtils::childPhotoUrl($this->myChildObjs[$this->childid]->photo, 'small'),
                        '', array('class'=>'img-circle')),
                    array('//app/default/profile'), array('class'=>'aj_ajax')
                );?>
            </div>
            <div class="col-xs-6">
                <h5><?php echo CHtml::link($this->myChildObjs[$this->childid]->getChildName(), array('//app/default/profile'), array('class'=>'aj_ajax'));?></h5>
                <h5><?php echo CHtml::link('<span class="glyphicon glyphicon-yen"></span> '.$this->myChildObjs[$this->childid]['credit'], array('//app/default/credit'), array('class'=>'aj_ajax'));?></h5>
                <h5><?php echo CHtml::link('<span class="glyphicon glyphicon-star"></span> '.$this->point, array('//app/default/point'), array('class'=>'aj_ajax'));?></h5>
            </div>
        </div>
        <div class="spmenu-body">
            <h6>校园生活</h6>
            <div class="list-group">
                <?php echo CHtml::link('<span class="glyphicon glyphicon-picture"></span> 周报告', array('//app/default/journal'), array('class'=>'list-group-item aj_ajax'));?>
                <?php echo CHtml::link('<span class="glyphicon glyphicon-cutlery"></span> 每周食谱', array('//app/default/lunch'), array('class'=>'list-group-item aj_ajax'));?>
                <?php echo CHtml::link('<span class="glyphicon glyphicon-education"></span> 我的小伙伴', array('//app/default/mates'), array('class'=>'list-group-item aj_ajax'));?>
                <?php echo CHtml::link('<span class="glyphicon glyphicon-ice-lolly-tasted"></span> 取消午餐', array('//app/default/cancelLunch'), array('class'=>'list-group-item aj_ajax'));?>
            </div>
            <hr>
            <h6>财务信息</h6>
            <div class="list-group">
                <?php echo CHtml::link('<span class="glyphicon glyphicon-bell"></span> 未付账单', array('//app/default/unpaid'), array('class'=>'list-group-item aj_ajax'));?>
                <?php echo CHtml::link('<span class="glyphicon glyphicon-piggy-bank"></span> 付款记录', array('//app/default/invoice'), array('class'=>'list-group-item aj_ajax'));?>
                <?php echo CHtml::link('退出', array('//app/user/logout'), array('class'=>'list-group-item'))?>
            </div>
        </div>
    </nav>

    <div id="shade" class="hide"></div>

    <div class="container-fluid" id="content">
        <?php echo $content;?>
    </div>

    <script type="text/javascript">
        $(document).ready(function() {
            var spmenu = {
                open: function(){
                    $('.spmenu-left').toggleClass('spmenu-left-open');
                    $('#shade').toggleClass('hide');
                    $('window, html, body').css({'position': 'fixed'});
                },
                close: function(){
                    $('.spmenu-left').removeClass('spmenu-left-open');
                    $('#shade').addClass('hide');
                    $('window, html, body').css({'position': 'static'});
                },
                menuClose: function(obj){
                    $('.spmenu-body .list-group .list-group-item').removeClass('active');
                    obj.addClass('active');
                    this.close();
                }
            };

            $('.navbar-toggle').click(function() {
                spmenu.open();
            });

            $('#shade').click(function(){
                spmenu.close();
            });

            $('.spmenu-left a.aj_ajax, .settings').click(function(e){
                e.preventDefault();
                spmenu.menuClose( $(this) );
                var url = $(this).attr('href');
                $.ajax({
                    url: url,
                    success: function(data){
                        $('#content').html(data);
                    },
                    error : function() {
                        alert("访问异常！");
                    }
                });
            });
        });
    </script>
<?php $this->endContent(); ?>