<?php

class AccessController extends Controller {

    public $layout = '//layouts/auth.access';

    public function init() {
        parent::init();
        //if (Yii::app()->user->isGuest)
        //$this->redirect(Yii::app()->createUrl('/user/login'));
    }

    public function filters() {
        return array(
            'accessControl',
        );
    }

    public function accessRules() {
        return array(
            array('allow',
            //'actions'=>array('index','page','contact','captcha'),
            //'expression'=>'Yii::app()->user->getId()==2',
            //'ips'=>array('127.0.0.1'),
            //'verbs'=>array('GET'),
            //'users'=>array('@'),
            //'roles'=>array(),
            ),
            array('deny',
                //'actions'=>array('index'),
                'users' => array('*')
            )
        );
    }

    /**
     * Declares class-based actions.
     */
    public function actions() {
        return array(
            // captcha action renders the CAPTCHA image displayed on the contact page
            'captcha' => array(
                'class' => 'CCaptchaAction',
            //'backColor'=>0xFFFFFF,
            ),
            // page action renders "static" pages stored under 'protected/views/site/pages'
            // They can be accessed via: index.php?r=site/page&view=FileName
            'page' => array(
                'class' => 'CViewAction',
            ),
        );
    }

    /**
     * This is the default 'index' action that is invoked
     * when an action is not explicitly requested by users.
     */
    public function actionIndex($id) {
		$this->setSubPageTitle(Yii::t("user","Visitor Authentication"));
        $params['valid'] = false;
        $params['authenticated'] = false;
        if (!empty($id)) {
            $childAccess = ChildAccess::model()->findByAttributes(array('accesskey' => $id));
            if ($childAccess != null) {
				if (in_array($childAccess->childid, Yii::app()->user->getAdminCIds())){
					$params['authenticated'] = true;	//已经以父母身份登录
				}
                elseif (in_array($childAccess->childid, Yii::app()->user->getVisitCIds())) {
                    $this->redirect($this->createUrl("/child/profile/welcome", array("childid" => $childAccess->childid)));
                    Yii::app()->end;
                } else {
                    $params['valid'] = true;
                }
				
				$params['child'] = ChildProfileBasic::model()->findByPk($childAccess->childid);
            }
        }
        

        $model = new VisitorAccessForm;
        $model->accesskey = $id;

        // if it is ajax validation request

        if (isset($_POST['ajax']) && $_POST['ajax'] === 'access-form') {
            echo CActiveForm::validate($model);
            Yii::app()->end();
        }

        // collect user input data
        if (isset($_POST['VisitorAccessForm'])) {
            $model->attributes = $_POST['VisitorAccessForm'];
            // validate user input and redirect to the previous page if valid
            if ($model->validate())
                $this->redirect($this->createUrl('//child/profile/welcome', array('childid' => $childAccess->childid)));
        }

        $this->render('//access/access', array('model' => $model, 'params' => $params));
    }

    public function actionGuest() {
        Yii::app()->user->authVisit(477, 'rambler00');
        Yii::app()->user->authVisit(774, '2005081700');
        echo Yii::app()->user->errorCode;

        Yii::app()->user->setState("__accesschilds", array(3432, 7383));
        $this->render('index');
    }

    /**
     * This is the action to handle external exceptions.
     */
    public function actionError() {
        if ($error = Yii::app()->errorHandler->error) {
            if (Yii::app()->request->isAjaxRequest)
                echo $error['message'];
            else
                $this->render('error', $error);
        }
    }

    /**
     * Displays the contact page
     */
    public function actionContact() {
        $model = new ContactForm;
        if (isset($_POST['ContactForm'])) {
            $model->attributes = $_POST['ContactForm'];
            if ($model->validate()) {
                $headers = "From: {$model->email}\r\nReply-To: {$model->email}";
                mail(Yii::app()->params['adminEmail'], $model->subject, $model->body, $headers);
                Yii::app()->user->setFlash('contact', 'Thank you for contacting us. We will respond to you as soon as possible.');
                $this->refresh();
            }
        }
        $this->render('contact', array('model' => $model));
    }

    /**
     * Displays the login page
     */
    public function actionLogin() {
        $model = new LoginForm;

        // if it is ajax validation request
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'login-form') {
            echo CActiveForm::validate($model);
            Yii::app()->end();
        }

        // collect user input data
        if (isset($_POST['LoginForm'])) {
            $model->attributes = $_POST['LoginForm'];
            // validate user input and redirect to the previous page if valid
            if ($model->validate() && $model->login())
                $this->redirect(Yii::app()->user->returnUrl);
        }
        // display the login form
        $this->render('login', array('model' => $model));
    }

    /**
     * Logs out the current user and redirect to homepage.
     */
    public function actionLogout() {
        Yii::app()->user->logout();
        $this->redirect(Yii::app()->homeUrl);
    }

    public function actionRBAC() {
        $auth = Yii::app()->authManager;
        /*
          $auth->createOperation('readJournal','Read child journal');
          $auth->createOperation('readLunchMenu','Read child lunch menu');
          $auth->createOperation('readClassContact','Read child contact list');
          $auth->createOperation('readClassTeacher','Read class teacher contact list');

          $bizRule='return in_array($params["chilidID"], Yii::app()->user->getVisitCIds() )';

          $task=$auth->createTask('visitChild','access child info by visitor',$bizRule);
          $task->addChild('readJournal');
          $task->addChild('readLunchMenu');

          $role=$auth->createRole('visitor');
          $role->addChild('visitChild');
         */
        $auth->assign('admin', 2);

        //$auth->createOperation('createPost','create a post');
        //$auth->createOperation('readPost','read a post');
        //$auth->createOperation('updatePost','update a post');
        //$auth->createOperation('deletePost','delete a post');
        //
		//$bizRule='return Yii::app()->user->id==$params["post"]->authID;';
        //$task=$auth->createTask('updateOwnPost','update a post by author himself',$bizRule);
        //$task->addChild('updatePost');
        // 
        //$role=$auth->createRole('reader');
        //$role->addChild('readPost');
        // 
        //$role=$auth->createRole('author');
        //$role->addChild('reader');
        //$role->addChild('createPost');
        //$role->addChild('updateOwnPost');
        //
		//$role=$auth->createRole('editor');
        //$role->addChild('reader');
        //$role->addChild('updatePost');
        // 
        //$role=$auth->createRole('admin');
        //$role->addChild('editor');
        //$role->addChild('author');
        //$role->addChild('deletePost');
        // 
        //$auth->assign('reader','readerA');
        //$auth->assign('author','authorB');
        //$auth->assign('editor','editorC');
        //$auth->assign('admin','adminD');
        //
		echo "finished";
    }

}