<?php
return array(
	'Course-Teacher Assignment' => '课程老师分配',
	'Student Schedule' => '学生课表',
	'Schedules Overview' => '课程安排总览',
    'Schedules Type'=>'课表类型',
	'My Courses' => '我的课程',
	'Student List' => '学生列表',
	'Memo' => '备注',
	'Assign Course Primary Teacher' => '分配课程教师',
	'Input name to filter' => '输入老师信息快速过滤',
	'Assign' => '分配',
	'Sure to proceed?' => '确认分配此老师？',
    'Reset assign?' => '确认重置？',
    'Data duplication' => '数据重复',
	'Assign successflly' => '分配成功',
	'Assign failed' => '分配失败',
	'Please select a class' => '请选择班级',
	'Mon' => '星期一',
	'Tue' => '星期二',
	'Wed' => '星期三',
	'Thu' => '星期四',
	'Fri' => '星期五',
    'Sun'=>'星期日',
    'Sat'=>'星期六',
	'Add a Course' => '添加课程',
	'Course' => '课程',
	'Frequence' => '课程时间表',
	'Please select a course' => '请选择课程',
	'Sure to remove this course?' => '确定去掉该课程吗？',
	'Boy' => '男',
	'Girl' => '女',
	'All Present' => '全出勤',
	'All Tardy' => '全迟到',
	'All Leave' => '全请假',
	'All Absent' => '全旷课',
	'Absent' => '旷课',
	'Internal Suspension' => '校内停学',
	'External Suspension' => '校外停学',
	'Leave' => '请假',
	'Personal Leave' => '事假',
	'Sick Leave' => '病假',
	'Tardy' => '迟到',
	'Tardy %s Minutes' => '迟到 %s 分钟',
	'Present' => '出勤',
	'Online Present' => '线上出勤',
	'Minutes' => '分钟',
	'%s Minutes' => '%s 分钟',
	'%s period #%s' => '%s 第 %s 节课',
	'Course has been assigned to ' => '课程已经分配给 ',
	', new assgin will make a replacement.' => '，新的分配将替换原有老师.',
	'No course has been assigned to you.' => '没有分配任何课程。',
	'Change Course Title' => '修改课程标题',
	'English Title:' => '英文标题：',
	'Chinese Title:' => '中文标题：',
	'Course Code:' => '课程代码：',
	'Synchronous modification' => '同步修改以下课程',
	'Course Code' => '课程代码',
	'Course Title' => '课程标题',
	'Action' => '操作',
	'Edit Course Title' => '修改课程标题',
	'Assign Course Teacher' => '分配课程老师',
	'View Schedules of Other Teachers' => '查看其它老师课表',
	'Select a Teacher' => '选择老师',
	'Check students to print schedules' => '请勾选需要打印课表的学生',
	'View Only! You have no attendance permission of this course.' => '您对该课程没有考勤操作权限，当前为浏览状态',
	'Sync attendance to next consecutive period' => '将签到结果同步到下一节课（连续课）',
    'Student Attendance Overview' => '学生签到总览',
    'The teachers can mark/edit the students attendance records within the current school day only' => '只能操作当天的数据',
    'Attendance' => '出勤',
    'Dress Code' => '着装要求',
    'Dress Code Violation' => '违反着装规定',
    'Device Confiscated' => '违规使用电子设备',
    'Yes' => '违反',
    'No' => '未违反',
    'All Yes' => '全违反',
    'All No' => '全未违反',
    'Select Date: ' => '选择日期：',
    'Attendance Report' => '考勤报表',
    'Attendance Submission Report' => '教师考勤提交报表',
    'Daily Attendance Submission By Teacher' => '教师完成考勤提交统计',
    'Quick View of Daily First Peiod Status' => '第一节课考勤报表',
    'Daily Attendance by Period' => '每日课堂考勤报表',
    'Reset' => '重置状态',
    'Uniform Infraction' => '着装违规',
    'Daily Attendance (Based on Period 2）' => '每日考勤（以每日第二节课统计）',
    'Daily Grades Attendance Report' => '年级日考勤报表',
    'Daily Attendance is based on 2nd period attendence.' => '每日考勤以每日第二节课考勤结果统计',
    'Grade Attendance' => '年级考勤统计',
    'Student Attendance Profile' => '学生考勤统计',
    'Abnormal Attendance & Violation' => '考勤异常&日常违规',
    'Attendance Report by Subjects' => '学科考勤统计',
    'Time Range'=>'时间范围',
    'This Week'=>'本周',
    'This Month'=>'本月',
    'This Semester'=>'本学期',
    'This School Year'=>'本学年',
    'Custom'=>'自定义时间',
    'Type'=>'选择类型',
    'Consecutive Days'=>'连续天数',
    'Cumulative Days'=>'累计天数',
    'Sick Leave %'=>'病假比例',
    'Personal Leave %'=>'事假比例',
    'Dress Code Violation %'=>'着装违规比例',
    'Device Confiscated %'=>'违规使用电子设备比例',
    'Period %d' => '第%d节',
    'Alias'=>'别名',
    'Edit Alias'=>'修改别名',
    'Course Name'=>'课程名称',
    'Course Alias'=>'课程别名',
    'Only courses assigned to you could be edited.'=>'只能编辑的自己的课程别名。',
    'No course has been assigned to you'=>'暂无课程',
    'Effective Period'=>'有效时间',
    'All School Year'=>'整学年',
    'Term 1'=>'第一学期',
    'Term 2'=>'第二学期',
    'Cannot span school years'=>'不能跨学年',
    'Not Assigned' => '待分配',
    'Not Assigned Year' => '未指定学年',
    'Assign Teacher' => '分配老师',
    'Assign Students' => '分配学生',
    'Students' => '学生',
    'Teachers' => '老师',
    'All Violated' => '全违反',
    'None violated' => '全未违反',
    'Violated' => '违反',
    'Not violated' => '未违反',
    'Violated: %d ' => '已违反%d次',
    'Record: %d'=>'，记录%d次',
    'Student Search' => '搜索',
    'Add To Course' => '加入到课程',
	'Course-Period Arrangement' => '编辑课程安排',
	'Input classroom NO.' => '请输入教室编号',
	'Display course details' => '显示课程完整信息',
	'No students' => '暂无学生',
	'No courses arranged in this period' => '本时段无可选课程',
	'Only show conflicted courses (conflics of classroom and teacher)' => '仅显示冲突课程（教室冲突、老师冲突）',
	'Edit classroom' => '修改教室',
    'Phone'=>'手机',
    'Other Personal Devices'=>'其他个人设备',
    'Both'=>'全部',
    'Phone&Other'=>'手机&其他设备',
    'Meet'=>'会议详情',
    'Meet Members'=>'添加老师',
    'Filter by name'=>'请输入姓名',
    'Add to Meet'=>'加入会议',
    'Meet Owner'=>'会议发起人',
    'Members'=>'参会成员',
    'Owner' => '分配负责人',
    'Meet Title'=>'会议标题',
    '√T'=>'√迟',
    'T'=>'迟',
    'S'=>'病',
    'P'=>'事',
    'A'=>'旷',
    'I'=>'校内停课',
    'E'=>'校外停课',
    'Student Attendance'=>'学生考勤',
    'Yearly Watermark' => '电子水印',
    'Customize' => '自定义',
    'Text' => "文字",
    'Color' => '颜色',
    'For calculation' => '计算值',
    'For display' => '显示值',
    "Completed" => "考勤完成",
    "Pending" => "待考勤",
);
