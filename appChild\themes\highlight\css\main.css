body
{
	margin: 0;
	padding: 0;
	color: #443B32;
	/*font: normal 10pt "Trebuchet MS","Microsoft Yahei",Arial,Helvetica,sans-serif;*/
	/*background: url("../images/main_bg.png") repeat-y #E8E7DA;*/
	/*background: url("../images/gradient.png") no-repeat scroll 50% 0 #EEECE7;*/
    _background-attachment: fixed;
    _background-image: url("text.txt");
	 -webkit-text-size-adjust:none; 
}
.ui-widget {
	/*font: normal 10pt "Trebuchet MS","Microsoft Yahei",Arial,Helvetica,sans-serif !important;*/
}
.ui-tabs .ui-tabs-nav li a{
	font-size:16px !important;
}
a{
	color:#8787C1;
}
a:hover{
	color:#333;
}
#page
{
	
}
ul,ol
{
	margin: 0;
	padding: 0;
	list-style-type: none;
}
#header
{
	margin: 0;
	padding: 0;
}
#header .nav-box {
	text-align: right;
	margin: 0;
	margin-top: 0;
	height: 35px;
	position: relative;
	z-index: 4;
}
#header .top-bar{
	height: 56px;
	display: block;
	text-align:right;
	position: relative;
	z-index: 0;
}
#header .top-bar .guide-icon{
	position: absolute;
	right: 3px;
	top: 3px;
	background: url("../images/icons/guide.png") no-repeat left top transparent;
	width: 75px;
	height: 50px;
}
#header .top-bar .guide-icon a{
	display: block;
	width: 75px;
	height: 50px;
	text-indent: 9999px;
	white-space: nowrap;
	overflow: hidden;
}
#header .top-bar .guide-icon:hover{
	right: 0px;
	top: -2px;
	background: url("../images/icons/guide_over.png") no-repeat left top transparent;
	width: 90px;
	height: 60px;
}
#header .top-bar .guide-icon:hover a{
	width: 75px;
	height: 50px;
}

#content
{
    padding: 0;
}

#sidebar
{
	padding: 20px 20px 20px 0;
}

div.flash-error, div.flash-info, div.flash-warning, div.flash-success, div.flash-info2
{
	margin:1em 0;
	border: 1px solid;
	padding:15px 10px 15px 50px;
	background-repeat: no-repeat;
	background-position: 10px center;
	border-radius:2px;
}

div.flash-error
{
	background-color:#FBE3E4;
	background-image:url(../images/msgbox_error.png);
	color:#8a1f11;
	border-color:#FBC2C4;
}

div.flash-info
{
	background-color:#CBE2F5;
	background-image:url(../images/msgbox_info.png);
	color:#514721;
	border-color:#58A8E3;
}
div.flash-info2
{
	background-color:#FFE2C0;
	/*background-image:url(../images/msgbox_info.png);*/
	color:#666;
	border-color:#EA7400;
	padding-left: 30px !important;
}
div.flash-warning
{
	background-color:#FFF6BF;
	background-image:url(../images/msgbox_warning.png);
	color:#514721;
	border-color:#FFD324;
}

div.flash-success
{
	background-color:#E6EFC2;
	background-image:url(../images/msgbox_success.png);
	color:#264409;
	border-color:#C6D880;
}

div.flash-error a
{
	color:#8a1f11;
}

div.flash-notice a
{
	color:#514721;
}

div.flash-success a
{
	color:#264409;
}

div.form .rememberMe label
{
	display: inline;
}

div.view
{
	padding: 10px;
	margin: 10px 0;
	border: 1px solid #C9E0ED;
}

div.breadcrumbs
{
	margin-bottom: 20px;
}

div.breadcrumbs span
{
	font-weight: bold;
}

div.search-form
{
	padding: 10px;
	margin: 10px 0;
	background: #eee;
}
.portlet
{

}
.portlet-decoration
{
	padding: 3px 8px;
	background: #B7D6E7;
	border-left: 5px solid #6FACCF;
}
.portlet-title
{
	font-size: 12px;
	font-weight: bold;
	padding: 0;
	margin: 0;
	color: #298dcd;
}
.portlet-content
{
	font-size:0.9em;
	margin: 0 0 15px 0;
	padding: 5px 8px;
	background:#EFFDFF;
}
.portlet-content ul
{
	list-style-image:none;
	list-style-position:outside;
	list-style-type:none;
	margin: 0;
	padding: 0;
}
.portlet-content li
{
	padding: 2px 0 4px 0px;
}
.operations
{
	list-style-type: none;
	margin: 0;
	padding: 0;
}
.operations li
{
	padding-bottom: 2px;
}
.operations li a
{
	font: bold 12px Arial;
	color: #0066A4;
	display: block;
	padding: 2px 0 2px 8px;
	line-height: 15px;
	text-decoration: none;
}
.operations li a:visited
{
	color: #0066A4;
}
.operations li a:hover
{
	background: #80CFFF;
}
/**********RAN XIN***********/

.mb20{
	margin-bottom: 20px;
}
.mt20{
	margin-top: 20px;
}
.mr20{
	margin-right: 20px;
}
.mtb20{
	margin: 20px 0;
}
.fr{float:right}
.alignRight{
	text-align:right !important;
}
.alignLeft{
	text-align:left !important;
}
/**************************/
#topbar{
	height: 4px;
	background:#6CB70C;
}
#header
{
	height:140px;
	/*border-bottom: 4px solid #9EB847;*/
}
#header #logo
{
}
#header a.logo-anonymous{
	background:url("../images/logo_b.png") no-repeat transparent;
	display: block;
	width: 310px;
	height: 60px;
	margin: 40px 0;
	text-indent: -999px;
}
#header a.logo-yzq{
    background:url("../images/logo_yzq.png") no-repeat transparent;
    display: block;
    width: 315px;
    height: 60px;
    margin: 40px 0;
    text-indent: -999px;
}
#header .links
{
	text-align:right;
	height: 22px;
	position: relative;
}

#header .links .weather{
	position: absolute;
	top: 0;
	left: 0;
	z-index: 1;
}
#header .subnav
{
	text-align: right;
}
#describer
{
	padding: 40px 0;
	margin: 40px 0;
	font-size: 12px;
	border-top: 1px solid #E9E8E8;
}
#footer
{
	clear:both;
	padding-top: 40px;
	padding-bottom: 20px;
	margin: 40px 0 0;
	font-size: 12px;
	background: url("../images/pfolio_nav_bg.png") no-repeat scroll top left transparent;
}
#footer .foot-logo p{
	text-align:right;
	padding-right: 20px;
}
#footer .foot-logo a{
	color:#fff;
}
#footer .copyright
{
	float:left;
	width:600px;
}
#footer .links
{
	float:right;
	/*width:200px;*/
	text-align:right;
}
#footer .links span{
	margin-left: 4px;
}
#footer h5.logo-s{
	display: block;
	width: 140px;
	height:55px;
	background: url("../images/logo_s.png") no-repeat top left transparent;
	margin-left: 50px;
	margin-bottom: 0;
	background-size: cover;
}
.seperator{
	height:0px;
	border-top: 1px solid #999;
	margin-bottom: 20px;
}
.thumb-small{
	height: 64px;
}
.thumb-bg{
	padding:4px;
	border:1px solid #E9E8E8;
}
.tac{
	text-align:center;
}



/*** ESSENTIAL STYLES ***/
.sf-menu, .sf-menu * {
	margin:			0;
	padding:		0;
	list-style:		none;
}
.sf-menu {
	line-height:	1.0;
}
.sf-menu ul {
	position:		absolute;
	top:			-999em;
	width:			10em; /* left offset of submenus need to match (see below) */
	width: 			100%;
	display: 		block;
	background: 	#9EB847;
	z-index:		999999;
	*z-index: 999;
}
.sf-menu ul li {
	width:			100%;
}
.sf-menu li:hover {
	visibility:		inherit; /* fixes IE7 'sticky bug' */
}
.sf-menu li {
	float:			left;
	position:		relative;
}
.sf-menu a {
	display:		block;
	position:		relative;
}
.sf-menu li:hover ul,
.sf-menu li.sfHover ul {
	/**left:			0;**/
	right: 0px;
	/**top:			2.5em; /* match top ul list item height */
	top: 34px;
}
ul.sf-menu li:hover li ul,
ul.sf-menu li.sfHover li ul {
	top:			-999em;
}
ul.sf-menu li li:hover ul,
ul.sf-menu li li.sfHover ul {
	left:			10em; /* match ul width */
	top:			0;
}
ul.sf-menu li li:hover li ul,
ul.sf-menu li li.sfHover li ul {
	top:			-999em;
}
ul.sf-menu li li li:hover ul,
ul.sf-menu li li li.sfHover ul {
	left:			10em; /* match ul width */
	top:			0;
}

/*** DEMO SKIN ***/
a.sf-with-ul{
	font-size: 14px;
}
.sf-menu li{
	border-right: 2px solid #E8E7DA;
}
.sf-menu li:hover em{
    background-color: #E8E7DA;
    border-top: 2px solid #E8E7DA;
    height: 3px;
    left: 0px;
    position: absolute;
    top: 32px;
	display: block;
	width: 100%
}

.sf-menu li.active em{
    background-color: #4B4B4B;
    border-top: 2px solid #E8E7DA;
    height: 3px;
    left: 0px;
    position: absolute;
    top: 32px;
	display: block;
	width: 100%
}


.sf-menu li li em{
    border-top: 0;
    height: 0px;
	display: none !important;
}

.sf-menu .mainitem a {
	line-height: 32px;
	height: 32px;
    color: #FFFFFF;
    display: block;
    padding: 0 15px;
    text-decoration: none;
	font-size: 14px;
	padding-left: 1.5em;
}
.sf-menu ul li{
	font-size: 12px;
	height: 28px;
	line-height: 28px;
}
.sf-menu .item-1, .sf-menu .item-1 ul{
	background:#01c2f4;
}
.sf-menu .item-2, .sf-menu .item-2 ul{
	background:#ff0091;
}
.sf-menu .item-3, .sf-menu .item-3 ul{
	background:#90B82F;
}
.sf-menu .item-4, .sf-menu .item-4 ul{
	background:#F36601;
}
.sf-menu .item-5, .sf-menu .item-5 ul{
	background:#92009F;
}
.sf-menu .item-1 ul{
	width:140px
}
.sf-menu .item-2 ul{
	width:130px;
}
.sf-menu .item-3 ul{
	width:150px;
}
.sf-menu .item-4 ul{
	width:180px;
}
.sf-menu .item-5 ul{
	width:200px;
}
.sf-menu a.sf-with-ul {
	/*padding-right: 	2.25em;*/
	min-width:		1px; /* trigger IE7 hasLayout so spans position accurately */
}
.sf-sub-indicator {
	position:		absolute;
	display:		block;
	right:			.75em;
	top:			1.05em; /* IE6 only */
	width:			10px;
	height:			10px;
	text-indent: 	-999em;
	overflow:		hidden;
	background:		url('../images/arrows-ffffff.png') no-repeat -10px -100px; /* 8-bit indexed alpha png. IE6 gets solid image only */
}
a > .sf-sub-indicator {  /* give all except IE6 the correct values */
	top:			.8em;
	background-position: 0 -100px; /* use translucent arrow for modern browsers*/
}
/* apply hovers to modern browsers */
a:focus > .sf-sub-indicator,
a:hover > .sf-sub-indicator,
a:active > .sf-sub-indicator,
li:hover > a > .sf-sub-indicator,
li.sfHover > a > .sf-sub-indicator {
	background-position: -10px -100px; /* arrow hovers for modern browsers*/
}

.sf-menu ul.subitem li a{
	text-align: right;
	font-size: 12px;
}
.sf-menu ul.subitem li:hover a{
	text-decoration: underline;
}

/*************************child selector start*******************************/
#child-selector{
	padding-top:20px;
	position:relative;
	height:120px;
	color:#fff;
}
#child-selector .photobox{
	position: relative;
	height: 120px;
}
#child-selector .photobox .face img{
	position: absolute;
	left:0;
	top:0;
}
#child-selector .photobox:hover a.face img{
	position: absolute;
	left:-16px;
	top:-16px;
	height: 140px;
}
a.face img{
    border: 5px solid #FFFFFF;
    box-shadow: 2px 2px 3px #666666;
    /*float: left;*/
    margin: 0 20px 0 0;
/*    width: 80px;*/
    height: 108px;
}
span.childname{
	display:inline-block;
	padding: 4px 0;
	font-size:14px;
}
#child-selector h2{
    border-bottom: 1px dotted #F2F2F2;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 10px;
	height:30px;
	line-height:30px;
	overflow:hidden;
	color:#fff;
	padding-left: 10px;
	text-shadow: 2px 1px 1px rgba(20, 20, 20, 0.6);
}
#child-selector .cs-edit{
	text-align:left;
	visibility: hidden;
	padding: 2px 0;
    position: absolute;
    background-color: green;
    opacity: 0.9;
    left: 0px;
    bottom: 0px;
	
}
#child-selector .cs-edit a{
	display: inline-block;
	padding: 1px 6px;
	color: #ededed;
	text-decoration: none;
/*	line-height:28px;*/
}
#child-selector .cs-edit a:hover{
	color:#fff;
}

#child-selector:hover .cs-edit, .showOperation{
	visibility: visible !important;
}
#child-selector .cs-edit .switch{
/*	margin-right:20px;*/
}
#child-selector .cs-edit .becenter{
/*	margin-right:48px;*/
}
ul.basic-info li{
	line-height:24px;
}
ul.basic-info li label{
	width: 84px;
	display:inline-block;
	text-align:right;
	padding-right:10px;
	color:#f2f2f2;
	font-size:14px;
}
ul.basic-info li span{
	border-bottom:1px dotted #f2f2f2;
	padding-left:4px;
	padding-right:10px;
	white-space: nowrap;
}

/*************************child selector end*******************************/

.nav-box .unpay-reminder{
	position: absolute;
	top: -30px;
	left: 300px;
	height: 30px;
}
.nav-box .reply-reminder{
	position: absolute;
	top: -30px;
	left: 80px;
	height: 30px;
}

.nav-box .unpay-reminder div{
	position: relative;
}
.nav-box .unpay-reminder div span{
	background: url('../images/unpay_reminder_bg.png') no-repeat right -30px #E8E7DA;
	line-height: 24px;
	height: 30px;
	display: inline-block;
	color:#fff;
	cursor: pointer;
}
.nav-box .reply-reminder div span{
	background: url('../images/reply_reminder_bg.png') no-repeat right -30px #E8E7DA;
	line-height: 24px;
	height: 30px;
	display: inline-block;
	color:#fff;
	cursor: pointer;
}
.nav-box .unpay-reminder div span a, .nav-box .reply-reminder div span a{
	color:#fff;
}
.nav-box .unpay-reminder div span span{
	background: url('../images/unpay_reminder_bg.png') no-repeat left 0 #E8E7DA;
	margin-right: 1em;
	display: inline-block;
	padding-left: 1em;
}
.nav-box .reply-reminder div span span{
	background: url('../images/reply_reminder_bg.png') no-repeat left 0 #E8E7DA;
	margin-right: 1em;
	display: inline-block;
	padding-left: 1em;
}
.nav-box .unpay-reminder div .hover, .nav-box .reply-reminder div .hover{
	visibility: hidden;
	position: absolute;
	left:0;
	top: 0;
	white-space:nowrap;
}

.nav-box .unpay-reminder div:hover .hover, .nav-box .reply-reminder div:hover .hover{
	visibility: visible;
}

#header #navigation{
	float:right;
}
#content-index{
}
#content-index li.active a{
	color: #333;
}
.hidden{
	display:none;
}

.themepage{
    /*background: url("../images/leftbar_bg.png") repeat-y #E8E7DA;*/
    position: relative;
}
.tar{
	text-align:right;
}
#left-col{
	min-height:900px;
	overflow-y:hidden;
	/*background: url("../images/leftbar_sep.png") no-repeat left top;*/
	padding-top: 22px;
}
#main-col{
	padding-top: 22px;
}
#main-col p.desc, #main-col ul.desc{
	color: #666;
}
#maintop{
	background: url("../images/mainnav_bg.png") no-repeat top right #E8E7DA;
}
#mainbox h1{
    border-bottom: 1px solid #c1c1c1;
    font-size: 18px;
    height: 29px;
    position: relative;
	margin-bottom: 10px;
	position:relative;
	*z-index: -3;
}
#mainbox h1 span{
	position: absolute;
	right: 5px;
	bottom: 5px;
	color: #787878;
	font-size: 12px;
}
#mainbox h1 span a{
	text-decoration: none;
}
#mainbox h1 label{
	background-color: #E8E7DA;
    color: #4B4B4B;
    display: inline-block;
    font-weight: normal;
    height: 30px;
	line-height:30px;
    padding-right: 5px;
    position: absolute;
	left: 0;
	top: 0;
	*z-index: -2;
}
#mainbox h2{
	color:#4b4b4b;
	font-size: 16px;
	height: 28px;
	line-height: 28px;
}
.sub-nav {
	text-align:right;
}
.sub-nav li a:link, .sub-nav li a:visited {
    color: #FFFFFF;
    display: block;
    font-size: 14px;
    min-height: 31px;
    padding: 8px 30px 0 0;
    text-decoration: none;
}
.sub-nav li a:hover, .sub-nav li.active a:link, .sub-nav li.active a:visited, .sub-nav li.inside a:hover {
    /*background: url("../images/leftbar_nav_selected.png") no-repeat left top;*/
}

ul.status-filter li{
	display:inline-block;
	display:inline;
}
ul.status-filter li a{
	text-decoration:none;
	padding: 4px 10px;
	font-size:14px;
	height:30px;
	line-height:30px;
	border-left: 1px solid #fff;
}
ul.status-filter li.active a{
	color:#333;
	padding: 4px 10px;
	font-size:14px;
	height:30px;
	line-height:30px;	
}
ul.parent-filter{
	padding-left: 80px;
	margin-top: 20px;
}
ul.parent-filter li{
	display: inline;
	margin-bottom: 4px;
	margin-right: 4px;
}
ul.parent-filter li a{
	display:inline-block;
	height: 30px;
	width: 130px;
	line-height: 30px;
	overflow: hidden;
	text-align:center;
	text-decoration: none;
}
ul.parent-filter li.active a{
	color:#fff;
	background:url("../images/custom_bg1.png") no-repeat left top;
}
.w100{
	padding: 4px 40px !important;
}

.dotted-border-1{
	border: 1px dotted #fff;
}
.contact-campus-bg{
	background:url("../images/contact_bg.png") no-repeat right top transparent;
	min-height: 128px;
}
.email-campus-bg{
	background:url("../images/email_bg.png") no-repeat right top transparent;
}
.profile_bg{
	background:url("../images/profile_profile_bg.png") no-repeat right bottom transparent;
}
.home_bg{
	background:url("../images/profile_home_bg.png") no-repeat right bottom transparent;
}
.misc_bg{
	background:url("../images/profile_misc_bg.png") no-repeat right bottom transparent;
}
.privacy_bg{
	background:url("../images/profile_privacy_bg.png") no-repeat right bottom transparent;
}
.share_bg{
	background:url("../images/profile_share_bg.png") no-repeat right bottom transparent;
}
.bignumber{
	font-size:20px;
}
dl.resource-list{
	margin: 0.5em 0;
	height: 126px;
}
dl.resource-list dt{
	float: left;
	width: 80px;
	text-align:center;
}
dl.resource-list dd{
	margin-left: 0;
	padding-left: 90px;
}
dl.resource-list dd h3{
	font-size: 16px;
	margin-bottom: 0.5em;
}
.inout-icon-in{
	background: url(../images/icons/in.png) no-repeat transparent;
	display: block;
	height: 24px;
	width: 24px;
}
.inout-icon-out{
	background: url(../images/icons/out.png) no-repeat transparent;
	display: block;
	height: 24px;
	width: 24px;
}
.icon-24{
	width: 24px;
}
.zero{
	color:#999;
}
/*button*/
span.btn001,span.btn001 a span{
    margin:0px;
    width:auto;
}
span.btn001 a {
    background-image: url("../images/btn_bg.gif");
    background-position: 0 0;
    background-repeat: no-repeat;
    display: inline-block;
    height: 32px;
    text-decoration: none;
}
span.btn001 a span {
    background-image: url("../images/btn_bg.gif");
    background-position: right -32px;
    background-repeat: no-repeat;
    color: #FFFFFF;
    cursor: pointer;
    display: inline-block;
    font-size: 14px;
    font-weight: bold;
    line-height: 32px;
    padding: 0 20px;
    text-decoration: none;
}
.shortCuts{
    position: fixed;
    bottom: 100px;
    left: 10px;
    _position: absolute;
    z-index: 8888;
    _top: expression(documentElement.scrollTop + documentElement.clientHeight - this.offsetHeight-(parseInt(this.currentStyle.marginTop,10)||0)-(parseInt(this.currentStyle.marginBottom,10)||0));
}
.shortCuts li{
	display: inline-block;
	margin-right: 10px;
	margin-bottom: 4px;
}
.shortCuts li{
	*display: inline;
}
.shortCuts h5{
	position: relative;
	color:#533419;
	padding-left: 10px;
	display: block;
}
.shortCuts h5 em{
	position: absolute;
	top: 4px;
	right: 6px;
	width: 26px;
	height: 26px;
	display: inline-block;
	background:url(../images/icons/quicklinks_hide.png) no-repeat;
	cursor: pointer;
}
.shortCuts #quick_max{
    background: url(../images/gradient.png) repeat #E8E7DA;
    border-radius: 4px;
	border: 1px solid #fff;
	border-right-color: #B3B085;
	border-bottom-color: #B3B085;
    width: 215px;
}
.shortCuts #quick_min{
    display: none;
    width: 42px;
    height: 42px;
    background:url("../images/icons/quicklinks_max.png") no-repeat;
    cursor: pointer;
}

/*input TEXT*/
textarea, select, input[type="text"]{
	font: normal 10pt "Trebuchet MS","Microsoft Yahei",Arial,Helvetica,sans-serif;
	padding: 2px;
}


.credit-balance{
	text-align: right;height: 40px;
	position: relative;
}
.credit-balance .title{
    font-size: 16px;
    font-weight: bold;
	line-height: 40px;
}
.credit-balance:hover .unit{
	color:#333;
}
.credit-balance:hover a{
	color:#333;
}
.credit-balance .desc a{
	color:red;
	padding: 0 4px;
}
.credit-balance .unit{
	font-size:16px;
	color:#999;
	font-weight:bold;
	padding-left:10px;
}
.credit-balance .credit{
	display:inline-block;
	width:70px;
	text-align:left;
	font-size: 18px;
}
.credit-balance .desc{
	position:absolute;
	text-align: left;
	width:450px;
	left: 250px;
	background:#fff;
	border-radius:4px;
	height:80px;
	border-bottom-color:#f2f2f2;
	border-right-color:#f2f2f2;
	z-index:2;
	padding: 4px 6px;
}
.credit-tuition .desc{
	top:0;
}
.credit-general .desc{
	top: -40px;
}
.credit-balance .hover{
	display:none;
}
.credit-balance .info:hover .hover{
	display:block;
}
.credit-balance em{
	position: absolute;
	left: 240px;
	display:inline-block;
	width: 18px;
	background:url(../images/left_focus.png) no-repeat left center transparent;
	height: 40px;
	z-index:1;
}
.credit-balance:hover .info{
	
}
.credit-balance .info{
	width: 240px !important;
	margin-right: 0 !important;
	padding-right: 10px !important;
}
.credit-tuition em{
	top:0;
}
.credit-general em{
	top:0;
}
.credit-tuition{
	color:#889E3D;
}
.credit-general{
	color:#F36601;
}

.ie6-alert{
    width: 100%;
    height: 100px;
    line-height: 100px;
    color: #FFF;
    font-size: 30px;
    background-color: red;
    z-index: 99999;
    position: fixed;
    bottom: 0;
    left: 0;
    _position: absolute;
    _top: expression(documentElement.scrollTop + documentElement.clientHeight - this.offsetHeight-(parseInt(this.currentStyle.marginTop,10)||0)-(parseInt(this.currentStyle.marginBottom,10)||0));
}

.mb-1em{
	margin-bottom: 1em;
}

.weather td.normal{
	white-space: nowrap;
}
.weather td.normal span em, em.info-icon{
	cursor: pointer;
	display: inline-block;
	width: 16px;
	height: 16px;
	background: url("../images/icons/info.png") no-repeat left top transparent;
}
.weather td.normal span em{
	*display:inline;
}
.weather td a:hover {
	color: #AE2D4E;
}
.white{
	color:#fff;
}

#birthday-countdown,#birthday-today,.birthday-reminder{
	height: 146px;
	width: 710px;
	margin-bottom: 10px;
	position: relative;
	border-bottom: 1px dotted #fff;
}
#birthday-today{
	background:url("../images/birthday_bg.png") no-repeat left top transparent;
}
#birthday-countdown .num-days{
    position: absolute;
    top: 12px;
    width: 40px;	
}
.birthday-reminder .note{
	position: absolute;
	right: 40px;
	bottom: 10px;
}
.birthday-reminder .note a{
	text-decoration: none;
	color: #DB6500;
}
.birthday-reminder .note-detail{
	display: none;
	position: absolute;
	right: 40px;
	bottom: 38px;
	padding: 0;
	width: 400px;
	background: #FF7E10;
	color:#fff;
	border-radius: 4px;
	line-height: 22px;
}
.birthday-reminder .note-detail .note-content{
	padding: 4px 8px;
	position: relative;
}
.birthday-reminder .note-detail .note-content em.quote-icon{
	display: block;
	position: absolute;
	width: 25px;
	height: 10px;
	right: 60px;
	bottom: -10px;
	background: url("../images/icons/birthday_quote.png") no-repeat left top transparent;
}
#birthday-today .cong-to{
	font-size: 20px;
	color: #D0652E;
	position: absolute;
	left: 120px;
	top: 30px;
}
#birthday-today .cong-from{
	font-size: 12px;
	color: #488744;
	position: absolute;
	right: 200px;
	top: 80px;
}
.qr-code img{
	margin-top: 0.5em;
}
.popup-box1 {
	margin: 20px;
	text-align: left;
}
ul.withIndent, .popup-box1 ol, .popup-box1 ul,.popup-box1 p {
	margin: 0 0 1em;
}
.popup-box1 li, ul.withIndent li{
	padding-left: 0.5em;
	margin-left: 3em;
}
.popup-box1 ol li{
	list-style-type: decimal;	
}
.popup-box1 ul li, ul.withIndent li{
	list-style-type: disc;	
}
.maintain h2{
	color:#000;
	font-size: 24px;
	margin: 120px 40px 0;
}
.maintain h3{
	color:#333;
	font-size: 20px;
	margin: 60px 40px 80px;
}
p.bm0{
	margin-bottom: 0;
}
.fl{
	float: left;
}
span.btn2 a{
    text-decoration:none;
}
span.btn2 a span{
    display: block;
    text-align: center;
    padding: 20px 0;
    background: #F36601;
    margin-bottom: 6px;
    font-size: 20px;
    color:#fff;
}
ol.number{
	list-style: 
}