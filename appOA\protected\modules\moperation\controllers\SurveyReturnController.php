<?php

class SurveyReturnController extends ProtectedController
{
    public $actionAccessAuths = array(
        'Index'             => 'o_A_Access',
    );

    public $branchArr;

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

        //$this->branchSelectParams['urlArray'] = array('//mcampus/backschool/index');

        foreach ( Branch::model()->getBranchList(null,true, '') as $branch){
            $this->branchArr[$branch['id']] = $branch['title'];
        }


        Yii::import('common.models.surveyReturn.*');
        Yii::import('common.models.calendar.*');
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue2.js');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile($cs->getCoreScriptUrl().'/jui/js/jquery-ui.min.js');
    }


    /**
     * 返校管理主页面
     */
    public function actionIndex()
    {
        $criteria = new CDbCriteria();
        $criteria->compare('status', 1);
        $returnModel = SurveyReturn::model()->findAll($criteria);
        $returnInfo = array();
        if($returnModel){
            foreach ($returnModel as $val){
                $returnInfo[$val->tid][] = array(
                    'id' => $val->school_id,
                    'start_timestamp' => $val->start_timestamp,
                    'end_timestamp' => $val->end_timestamp,
                    'status' => $val->status,
                    'class_type' => explode(',', $val->class_type),
                );
            }
        }

        $criteria = new CDbCriteria();
        $criteria->compare('status', 1);
        $templatModel = SurveyReturnTemplate::model()->findAll($criteria);
        $templateData = array();
        if($templatModel){
            foreach ($templatModel as $val){
                $templateData[] = array(
                    'id' => $val->id,
                    'cn_title' => $val->cn_title,
                    'en_title' => $val->en_title,
                    'schoolInfo' => isset($returnInfo[$val->id]) ? $returnInfo[$val->id] : array(),
                );
            }
        }

        $this->render('index');
    }

    public function actionTemplate()
    {
        $criteria = new CDbCriteria();
        $criteria->compare('status', array(0,1));
        $criteria->order='updated_at desc';
        $templatModel = SurveyReturnTemplate::model()->findAll($criteria);
        $templateData = array();
        if($templatModel){
            foreach ($templatModel as $val){
                $templateData[] = array(
                    'id' => $val->id,
                    'cn_title' => $val->cn_title,
                    'en_title' => $val->en_title,
                    'cn_intro' => $val->cn_intro,
                    'en_intro' => $val->en_intro,
                    'cn_intro2' => $val->cn_intro2,
                    'en_intro2' => $val->en_intro2,
                    'status' => $val->status,
                );
            }
        }

        $this->render('template',array(
            'templateData'=>$templateData,
        ));
    }


    // 增加模板
    public function actionUpdateTemplate()
    {
        if(Yii::app()->request->isPostRequest){
            $id = Yii::app()->request->getParam('id', '');
            $model = SurveyReturnTemplate::model()->findByPk($id);
            if(!$model) {
                $model = new SurveyReturnTemplate();
                $model->created_by = $this->staff->uid;
                $model->created_at = time();
            }

            $model->attributes=$_POST['SurveyReturnTemplate'];
            $model->updated_by = $this->staff->uid;
            $model->updated_at = time();

            if(!$model->save()){
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err[0]);
                $this->showMessage();
            }

            $this->addMessage('state', 'success');
            $this->addMessage('data', $model->attributes);
            $this->addMessage('callback', 'cbSuccess');
            $this->addMessage('message', Yii::t('message', 'Data Saved!'));
            $this->showMessage();
        }
    }

    // 删除返校调查报告模板
    public function actionDeleteTemplate()
    {
        if(Yii::app()->request->isPostRequest) {
            $survey_id = Yii::app()->request->getParam('survey_id', '');

            $model = SurveyReturnTemplate::model()->findByPk($survey_id);
            if(!$model) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '非法 操作');
                $this->showMessage();
            }
            $criteria = new CDbCriteria();
            $criteria->compare('tid', $survey_id);
            $criteria->compare('status', 1);
            $returnCount = SurveyReturn::model()->count($criteria);

            if($returnCount) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '模板已分配了校园不可删除');
                $this->showMessage();
            }

            $model->status = 99;
            $model->updated_by = $this->staff->uid;
            $model->updated_at = time();
            if(!$model->save()){
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err[0]);
                $this->showMessage();
            }
            $this->addMessage('state', 'success');
            $this->addMessage('data', $model->id);
            $this->addMessage('message', Yii::t('message', 'Data Saved!'));
            $this->showMessage();
        }
    }

    // 问题
    public function actionQuestionsList()
    {
        $survey_id = Yii::app()->request->getParam('survey_id', '');

        $question = array();
        if($survey_id){
            $criteria = new CDbCriteria();
            $criteria->compare('survey_id', $survey_id);
            $criteria->compare('status', 1);
            $criteria->order = 'option_sort ASC';
            $templatModel = SurveyReturnQuestions::model()->findAll($criteria);

            if($templatModel) {
                foreach ($templatModel as $val){
                    $question[] = array(
                        'id' => $val->id,
                        'pid' => $val->pid,
                        'survey_id' => $val->survey_id,
                        'option_sort' => $val->option_sort,
                        'type' => $val->type,
                        'titleEn' => $val->en_title,
                        'titleCn' => $val->cn_title,
                        'content_ext' => $val->content_ext,
                        'content_ext_en' => $val->content_ext_en,
                        'is_required' => $val->is_required,
                        'is_multiple' => $val->is_multiple,
                        'is_memo' => $val->is_memo,
                        'is_signature' => $val->is_signature,
                        'is_invoice' => isset($val->is_invoice) ? $val->is_invoice : '',
                        'to_survey' => $val->to_survey,
                        'jump_id' => $val->jump_id,
                    );
                }
            }
        }

        $criteria = new CDbCriteria();
        $criteria->compare('type', array(20,50));
        $criteria->compare('status', 10);
        $criteria->order = 'abb ASC';
        $branchModel = Branch::model()->findAll($criteria);
        $branchInfo = array();
        if($branchModel){
            foreach ($branchModel as $val){
                $branchInfo[] = array(
                    'title' => $val->title,
                    'branchid' => $val->branchid,
                );
            }
        }
        array_unshift($branchInfo, array('title' => '全部校园', 'branchid' => 'all'));

        $this->render('questionsList', array(
            'question' => $question,
            'survey_id' => $survey_id,
            'branchInfo' => $branchInfo,
        ));
    }

    public function getTree($list, $pid = 0)
    {
        $tree = array();
        if (!empty($list)) {
            $newList = array();
            foreach ($list as $k => $v) {
                $newList[$v['id']] = $v;
            }
            foreach ($newList as $value ) {
                if ($pid == $value['pid']) {
                    $tree[] = &$newList[$value['id']];
                } elseif (isset($newList[$value['pid']]))
                {
                    $newList[$value['pid']]['items'][] = &$newList[$value['id']];
                }
            }
        }

        return $tree;
    }

    // 增加问题和选项
    public function actionUpdateQuetion()
    {
        $type = Yii::app()->request->getParam('type', '');
        $survey_id = Yii::app()->request->getParam('survey_id', ''); // 模板ID
        $pid = Yii::app()->request->getParam('pid', ''); // 父类id
        $id = Yii::app()->request->getParam('id', ''); // 当前ID
        $option_ids = Yii::app()->request->getParam('option_ids', ''); // 删除ID
        $option = Yii::app()->request->getParam('option', array());

        if($option_ids){
            $option_ids = explode(',', $option_ids);
            $criteria = new CDbCriteria();
            $criteria->compare('pid', $option_ids);
            $criteria->compare('status', 1);
            $optionCount = SurveyReturnQuestions::model()->count($criteria);
            if($optionCount){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', '删除选项下有下一级题目，不可删除'));
                $this->showMessage();
            }
        }

        $connection = Yii::app()->db->beginTransaction();
        try {
            $model = SurveyReturnQuestions::model()->findByPk($id);
            if(!$model){
                $model = new SurveyReturnQuestions();
                $model->created_by = $this->staff->uid;
                $model->created_at = time();
                $model->option_sort = 0;
            }else{
                if($model->type != $type){
                    $criteria = new CDbCriteria();
                    $criteria->compare('pid', $model->id);
                    $typeCount = SurveyReturnQuestions::model()->count($criteria);
                    if($typeCount){
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('message', '题目下有选项，请先删除选项然后在修改题目类型'));
                        $this->showMessage();
                    }
                }
            }
            $model->attributes = $_POST['SurveyReturnQuestions'];
            $model->survey_id = $survey_id;
            $model->pid = $pid ? $pid : 0;
            $model->status = 1;
            $model->type = $type;
            $model->updated_by = $this->staff->uid;
            $model->updated_at = time();
            if(!$model->save()) {
                $connection->rollBack();
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err[0]);
                $this->showMessage();
            }
            if($option_ids) {
                $oldOptionInfo = SurveyReturnQuestions::model()->findAllByPk($option_ids);
                if($oldOptionInfo) {
                    foreach ($oldOptionInfo as $info) {
                        $info->status = 99;
                        $info->updated_by = $this->staff->uid;
                        $info->updated_at = time();
                        if (!$info->save()) {
                            $connection->rollBack();
                            $this->addMessage('state', 'fail');
                            $err = current($info->getErrors());
                            $this->addMessage('message', $err[0]);
                            $this->showMessage();
                        }
                    }
                }
            }
            if ($model->type == 'question') {
                if ($option) {
                    foreach ($option as $key => $val) {
                        $optionModel = SurveyReturnQuestions::model()->findByPk($val['id']);
                        if(!$optionModel) {
                            $optionModel = new SurveyReturnQuestions();
                            $optionModel->created_by = $this->staff->uid;
                            $optionModel->created_at = time();
                            $optionModel->status = 1;
                        }
                        $optionModel->survey_id = $survey_id;
                        $optionModel->pid = $model->id;
                        $optionModel->type = 'option';
                        $optionModel->cn_title = $val['cn_title'];
                        $optionModel->en_title = $val['en_title'];
                        $optionModel->option_sort = $key;
                        $optionModel->is_invoice = isset($val['is_invoice']) ? $val['is_invoice'] : '' ;
                        $optionModel->updated_by = $this->staff->uid;
                        $optionModel->updated_at = time();
                        if (!$optionModel->save()) {
                            $connection->rollBack();
                            $this->addMessage('state', 'fail');
                            $err = current($optionModel->getErrors());
                            $this->addMessage('message', $err[0]);
                            $this->showMessage();
                        }
                    }
                }
            }
            $connection->commit();
            $this->addMessage('state', 'success');
            $this->addMessage('refresh', true);
            $this->addMessage('message', Yii::t('message', 'success'));
            $this->showMessage();
        } catch (\Exception $e) {
            $connection->rollBack();
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', '异常,请稍后再试!'));
            $this->showMessage();
        }
    }

    // 删除题目
    public function actionDeleteTopic()
    {
        $id = Yii::app()->request->getParam('id', '');

        if(!$id){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '缺少参数');
            $this->showMessage();
        }

        $surveyReturnModel = SurveyReturnQuestions::model()->findByPk($id);

        $criteria = new CDbCriteria();
        $criteria->compare('pid', $surveyReturnModel->id);
        $criteria->compare('status', 1);
        $criteria->index = 'id';
        $model = SurveyReturnQuestions::model()->findAll($criteria);
        $pids = array($id);
        if($model){
            $pids = array_merge($pids, array_keys($model));
        }

        $criteria = new CDbCriteria();
        $criteria->compare('pid', $pids);
        $criteria->compare('status', 1);
        $modelCount = SurveyReturnQuestions::model()->count($criteria);
        if($modelCount){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', '删除选项下有下一级题目，不可删除'));
            $this->showMessage();
        }
        $surveyReturnModel->status = 99;
        if(!$surveyReturnModel->save()){
            $this->addMessage('state', 'fail');
            $err = current($surveyReturnModel->getErrors());
            $this->addMessage('message', $err[0]);
            $this->showMessage();
        }

        $this->addMessage('state', 'success');
        $this->addMessage('message', Yii::t('message', 'success'));
        $this->showMessage();

    }

    public function actionSaveJump()
    {
        $id = Yii::app()->request->getParam('id', '');
        $jumpId = Yii::app()->request->getParam('jumpId', '');

        if(!$id) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '缺少参数');
            $this->showMessage();
        }

        $model = SurveyReturnQuestions::model()->findByPk($id);
        $model->jump_id = $jumpId;
        if ($model->save()) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('message', 'success'));
        }
        else {
            $this->addMessage('state', 'fail');
//            $this->addMessage('message', current($model->getErrors())[0]);
            $this->addMessage('message', current($model->getErrors()));
        }

        $this->showMessage();
    }

    // 获取已经分配给模板校园的数据和学校还有班级数据
    public function actionAssigntemplate()
    {
        $survey_id = Yii::app()->request->getParam('survey_id', '');

        $criteria = new CDbCriteria();
        $criteria->compare('tid', $survey_id);
        $criteria->compare('status', 1);
        $surveyModel = SurveyReturn::model()->findAll($criteria);
        $data = $this->branchClassInfo();
        if($surveyModel){
            foreach ($surveyModel as $val){
                $data['branchid'][] = $val->school_id;
                $data['class_type'] = explode(',', $val->class_type);
            }
        }

        $this->addMessage('state', 'success');
        $this->addMessage('data', $data);
        $this->addMessage('message', Yii::t('message', 'success'));
        $this->showMessage();
    }


    // 获取校园和班级
    public static function branchClassInfo()
    {
        $data['classInfo'] = array(IvyClass::getClassTypes(), IvyClass::getClassTypes(true,50));
        $criteria = new CDbCriteria();
        $criteria->compare('type', array(20,50));
        $criteria->compare('status', 10);
        $criteria->order = 'abb ASC';
        $branchModel = Branch::model()->findAll($criteria);

        foreach ($branchModel as $branch){
            $data['branchInfo'][] = array(
                'branchid' => $branch->branchid,
                'type' => $branch->type,
                'title' => $branch->title
            );
        }
        return $data;
    }

    // 给模板分配到校园
    public function actionAddreturn()
    {
        if(Yii::app()->request->isPostRequest){
            $tid = Yii::app()->request->getParam('tid', '');
            $branchids = Yii::app()->request->getParam('branchids', array());
            $classids = Yii::app()->request->getParam('classids', array());
            $start_timestamp = Yii::app()->request->getParam('start_timestamp', array());
            $end_timestamp = Yii::app()->request->getParam('end_timestamp', array());

            $criteria = new CDbCriteria();
            $criteria->compare('tid', $tid);
            $returnChildModel = SurveyReturn::model()->findAll($criteria);
            $school = array();
            if($returnChildModel) {
                foreach ($returnChildModel as $item) {
                    $school[] = $item->school_id;
                }
            }
            $delReg = array();
            $connection = Yii::app()->db->beginTransaction();
            try {
                $delResultSchool = array_diff($school,$branchids); // 要删除的学校数组
                if($delResultSchool) {
                    $data = array(
                        'tid' => $tid,
                        'delResultSchool' => $delResultSchool,
                    );
                    $delReg = $this->delReturn($data);
                    if ($delReg['state'] == 'fail') {
                        $connection->rollBack();
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', $delReg['message']);
                        $this->showMessage();
                    }
                }

                $saveResultSchool = array_diff($branchids,$school); // 要增加学校的数组
                if($saveResultSchool) {
                    $data = array(
                        'tid' => $tid,
                        'saveResultSchool' => $saveResultSchool,
                        'classids' => $classids,
                        'start_timestamp' => $start_timestamp,
                        'end_timestamp' => $end_timestamp,
                    );
                    $saveReg = $this->saveReturn($data);
                    if ($saveReg['state'] == 'fail') {
                        $connection->rollBack();
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', $saveReg['message']);
                        $this->showMessage();
                    }
                }

                $message = isset($delReg) && $delReg['message'] ? $delReg['message'] : Yii::t('message', 'success');
                $connection->commit();
                $this->addMessage('state', 'success');
                $this->addMessage('message', $message);
                $this->showMessage();
            } catch (\Exception $e) {
                $connection->rollBack();
                $this->addMessage('state', 'fail');
                $this->addMessage('message', $e->getMessage());
                $this->showMessage();
            }
        }
    }

    public function delReturn($data)
    {
        $criteria = new CDbCriteria();
        $criteria->compare('tid', $data['tid']);
        $criteria->compare('school_id', $data['delResultSchool']);
        $returnModel = SurveyReturn::model()->findAll($criteria);
        $schoolid = array();
        if($returnModel){
            foreach ($returnModel as $item){
                $criteria = new CDbCriteria();
                $criteria->compare('survey_id', $item->id);
                $criteria->compare('template_id', $data['tid']);
                $criteria->compare('is_answer', 1);
                $count = SurveyReturnChild::model()->count($criteria);
                if(!$count){
                    SurveyReturnChild::model()->deleteAll('survey_id=:survey_id',array(':survey_id'=> $item->id));
                    $item->delete();
                }else{
                    $schoolid[] = $item->school_id;
                }
            }
        }

        $message = $schoolid ? implode(',',$schoolid) . ' 未取消' : Yii::t('message', 'success');
        return array('state' => 'success', 'message' => $message);
    }

    /*
        $data = array(
            'tid' => $tid, 模板ID
            'saveResultSchool' => $saveResultSchool,  增加的学校
            'classids' => $classids, 班级
            'start_timestamp' => $start_timestamp, 开始时间
            'end_timestamp' => $end_timestamp, 结束时间
        );
     */
    public function saveReturn($data)
    {
        $criteria = new CDbCriteria();
        $criteria->compare('branchid', $data['saveResultSchool']);
        $criteria->compare('is_selected', 1);
        $criteria->index = 'branchid';
        $calendarInfo = CalendarSchool::model()->findAll($criteria);
        $yid = array();
        foreach ($calendarInfo as $val){
            $yid[] = $val->yid;
        }
        $childInfo = array();
        // 根据学校id和班级类型，拿到当前所有有效的班级id
        if($data['classids']) {
            $criteria = new CDbCriteria();
            $criteria->compare('schoolid', $data['saveResultSchool']);
            $criteria->compare('yid', $yid);
            $criteria->compare('stat', 10);
            $criteria->compare('classtype', $data['classids']);
            $criteria->index = 'classid';
            $classModel = IvyClass::model()->findAll($criteria);


            if($classModel) {
                // 拿到所有有效班级里的有效学生数据
                $criteria = new CDbCriteria();
                $criteria->compare('classid', array_keys($classModel));
                $criteria->compare('status', array(ChildProfileBasic::STATS_ACTIVE_WAITING, ChildProfileBasic::STATS_ACTIVE));
                $childModel = ChildProfileBasic::model()->findAll($criteria);
                if ($childModel) {
                    foreach ($childModel as $val) {
                        $childInfo[$val->schoolid][] = array(
                            'classid' => $val->classid,
                            'childid' => $val->childid,
                        );
                    }
                }
            }
        }

        // 循环学校增加surveyReturn 表数据
        foreach ($data['saveResultSchool'] as $val){
            $model = new SurveyReturn();
            $model->start_timestamp = strtotime($data['start_timestamp']);
            $model->end_timestamp = strtotime($data['end_timestamp']);
            $model->school_id = $val;
            $model->class_type = $data['classids'] ? implode(',', $data['classids']) : '';
            $model->tid = $data['tid'];
            $model->yid = $calendarInfo[$val]->yid;
            $model->updated_by = $this->staff->uid;
            $model->updated_at = time();
            $model->status = 1;
            $model->created_by = $this->staff->uid;
            $model->created_at = time();
            if(!$model->save()){
                $err = current($model->getErrors());
                return array('state' => 'fail', 'message' => $err[0]);
            }
            if($childInfo[$val]) {
                foreach ($childInfo[$val] as $child) {
                    $returnChildModel = new SurveyReturnChild();
                    $returnChildModel->school_id = $val;
                    $returnChildModel->yid = $model->yid;
                    $returnChildModel->classid = $child['classid'];
                    $returnChildModel->classType = $classModel[$child['classid']]->classtype;
                    $returnChildModel->childid = $child['childid'];
                    $returnChildModel->survey_id = $model->id;
                    $returnChildModel->template_id = $data['tid'];
                    $returnChildModel->is_answer = 0;
                    $returnChildModel->status = 1;
                    $returnChildModel->updated_by = $this->staff->uid;
                    $returnChildModel->updated_at = time();
                    $returnChildModel->created_by = $this->staff->uid;
                    $returnChildModel->created_at = time();
                    if (!$returnChildModel->save()) {
                        $err = current($returnChildModel->getErrors());
                        return array('state' => 'fail', 'message' => $err[0]);
                    }
                }
            }
        }
        return array('state' => 'success', 'message' => Yii::t('message', 'success'));

    }
}
