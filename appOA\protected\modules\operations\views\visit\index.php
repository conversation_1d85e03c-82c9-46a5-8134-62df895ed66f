<div class="table_c1">
    <table width="100%">
        <colgroup>
            <col class="th" width="280">
            <col>
        </colgroup>
        <tr>
            <td>
                <div class="table_list">
                    <table width="100%">
                        <thead>
                            <tr>
                                <td>需要审核</td>
                            </tr>
                        </thead>
                        <tr>
                            <td><div id="pending"></div></td>
                        </tr>
                    </table>
                </div>
                <?php
                 $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                    'name'=>'pending',
                    'options'=>array(
                       'showOtherMonths'=>true,
                       'selectOtherMonths'=>true,
                       'onSelect'=>'js:onSelect',
                       'beforeShowDay'=>'js:beforeShowDay',
                    ),
                    'htmlOptions'=>array(
                        'class'=>'dn',
                    ),
                ));
                ?>
                <div class="tips_light">红圈标记的日期表示当天有预约参观申请，点击可查看。请在一个工作日内联络家长并处理新申请。</div>
                <div class="tac"><a href="<?php echo Yii::app()->params['OABaseUrl']?>/modules/visits/index.php" target="_blank" class="btn">去来访记录</a></div>
            </td>
            <td>
                <div>
                    <?php
                    $this->widget('ext.ivyCGridView.IvyCGridView', array(
                    'id'=>'pending-visit-grid',
                    'afterAjaxUpdate'=>'js:aft',
                    'dataProvider'=>$mdataProvider,
                    'template'=>"{items}{pager}",
                    'enableSorting'=>false,
                    'colgroups'=>array(
                        array(
                            "colwidth"=>array(160,200,200),
                        )
                    ),
                    'columns'=>array(
                        array(
                            'name' => 'visit_date',
                            'type'=>'raw',
                            'value' => 'OA::formatDateTime($data->visit_date)."<br><span>".$data->visit_time."</span>"'
                        ),
                        'child_name',
                        array(
                            'name'=>'家长信息',
                            'type'=>'raw',
                            'value'=>'$data->parent_name."<br>".$data->phone."<br>".$data->email',
                        ),
                        array(
                            'class' => 'CButtonColumn',
                            'template' => '{update} {review}',
                            'updateButtonLabel' => Yii::t('global', 'Edit'),
                            'updateButtonUrl' => 'Yii::app()->controller->createUrl("/operations/visit/update",array("id"=>$data->id, "branchId"=>Yii::app()->controller->branchId))',
                            'updateButtonImageUrl' => false,
                            'updateButtonOptions' => array('class'=>'J_dialog'),
                            'buttons' => array(
                                'review' => array(
                                    'label' => Yii::t('visit', '审核'),
                                    'imageUrl' => false,
                                    'url' => 'Yii::app()->controller->createUrl("/operations/visit/aview",array("id"=>$data->id, "branchId"=>Yii::app()->controller->branchId))',
                                    'options' => array('class'=>'J_dialog'),
                                ),
                            ),
                        ),
                    ),
                ));
                    ?>
                </div>
            </td>
        </tr>
    </table>
</div>
<div class="table_c1">
    <table width="100%">
        <colgroup>
            <col class="th" width="280">
            <col>
        </colgroup>
        <tr>
            <td>
                <div class="table_list">
                    <table width="100%">
                        <thead>
                            <tr>
                                <td>需要接待</td>
                            </tr>
                        </thead>
                        <tr>
                            <td><div id="confirmed"></div></td>
                        </tr>
                    </table>
                </div>
                <?php
                 $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                    'name'=>'confirmed',
                    'options'=>array(
                       'showOtherMonths'=>true,
                       'selectOtherMonths'=>true,
                        'minDate'=>-60,
                       'onSelect'=>'js:onSelect',
                       'beforeShowDay'=>'js:beforeShowDay',
                    ),
                    'htmlOptions'=>array(
                        'class'=>'dn',
                    ),
                ));
                ?>
                <div class="tips_light">红圈标记的日期表示当天有确认的预约参观，这里可查阅当前及60日内的历史数据</div>
            </td>
            <td>
                <div>
                    <?php
                    $this->widget('ext.ivyCGridView.IvyCGridView', array(
                    'id'=>'confirmed-visit-grid',
                    'afterAjaxUpdate'=>'js:aft',
                    'dataProvider'=>$dataProvider,
                    'enableSorting'=>false,
                    'template'=>"{items}{pager}",
                    'colgroups'=>array(
                        array(
                            "colwidth"=>array(160,200,200),
                        )
                    ),
                    'columns'=>array(
                        array(
                            'name' => 'appointment_date',
                            'type'=>'raw',
                            'value' => 'OA::formatDateTime($data->appointment_date)."<br><span>".$data->appointment_time."</span>"'
                        ),
                        array(
                            'name'=>'孩子姓名',
                            'value'=>'$data->basic->child_name',
                        ),
                        array(
                            'name'=>'家长信息',
                            'type'=>'raw',
                            'value'=>'$data->basic->parent_name."<br>".$data->basic->tel."<br>".$data->basic->email',
                        ),
                        array(
                            'class' => 'CButtonColumn',
                            'template' => '{update} {changedate}',
                            'updateButtonLabel' => Yii::t('visit', '添加跟踪记录'),
                            'updateButtonImageUrl' => false,
                            'updateButtonOptions' => array('target'=>'_blank'),
                            'updateButtonUrl' => 'CHtml::normalizeUrl("'.Yii::app()->params['OABaseUrl'].'/modules/visits/index.php?id=$data->basic_id")',
                            'buttons' => array(
                                'changedate' => array(
                                    'label' => Yii::t('visit', '重新安排时间'),
                                    'imageUrl' => false,
                                    'url' => 'Yii::app()->controller->createUrl("/operations/visit/changedate",array("id"=>$data->id, "branchId"=>Yii::app()->controller->branchId))',
                                    'options' => array('class'=>'J_dialog'),
                                ),
                            ),
                        ),
                    ),
                ));
                ?>
                </div>
            </td>
        </tr>
    </table>
</div>

<script type="text/javascript">
var aUpdate = true;
var pendingdate = {};
var confirmeddate = {};

function upPending()
{
    $.ajax({
        async: false,
        type: 'get',
        url: '<?php echo $this->createUrl('targetDate');?>',
        dataType: 'json',
        data: {type: 'pending', branchId:'<?php echo $this->branchId?>'}
    }).done(function(data){
        pendingdate = data;
    });
}
upPending();
function upConfirmed()
{
    $.ajax({
        async: false,
        type: 'get',
        url: '<?php echo $this->createUrl('targetDate');?>',
        dataType: 'json',
        data: {type: 'confirmed', branchId:'<?php echo $this->branchId?>'}
    }).done(function(data){
        confirmeddate = data;
    });
}
upConfirmed();
function beforeShowDay(date) {
    var rData = (this.id === 'pending') ? pendingdate : confirmeddate;
    var cssClass = '';
    var d = date.getFullYear().toString()+(date.getMonth()+1).toString()+date.getDate().toString();
    if(rData[d])
        cssClass = 'date-active';
    return [true, cssClass];
}
function onSelect(dateText, inst) {
    if (aUpdate){
        aUpdate = false;
        var dt = inst.currentYear+'-'+(inst.currentMonth+1)+'-'+inst.currentDay;
        var d = inst.selectedYear.toString()+(inst.selectedMonth+1).toString()+inst.selectedDay.toString();
        if (this.id === 'pending'){
            var rData = pendingdate;
            if (rData[d]){
                var filterHtml = ' <a href="javascript:;" class="dfilter" onclick=showAll("pending-visit-grid")><strong>'+dt+'</strong><b></b></a>';
                $.fn.yiiGridView.update("pending-visit-grid", { data:{ "IvyschoolsVisit[visit_date]":dateText}, complete: function(){
                    $('#pending-visit-grid_c0').append(filterHtml);
                    aUpdate = true;
                }});
            }
            else{
                resultTip({error:true, msg: dt+' 没有记录'});
                aUpdate = true;
            }

        }
        else{
            var rData = confirmeddate;
            if (rData[d]){
                var filterHtml = ' <a href="javascript:;" class="dfilter" onclick=showAll("confirmed-visit-grid")><strong>'+dt+'</strong><b></b></a>';
                $.fn.yiiGridView.update("confirmed-visit-grid", {data:{"IvyVisitsRecord[appointment_date]":dateText}, complete: function(){
                    $('#confirmed-visit-grid_c0').append(filterHtml);
                    aUpdate = true;
                }});
            }
            else {
                resultTip({error:true, msg: dt+' 没有记录'});
                aUpdate = true;
            }
        }
    }
    else{
        resultTip({error:true, msg: '正在请求，请不要过快点击！'});
    }
}

function aft(id, data)
{
    if( $('a.J_dialog').length ) {
		head.use('dialog',function() {
			$('#'+id+' .J_dialog').on( 'click',function(e) {
				e.preventDefault();
				var _this = $(this);
				head.dialog.open( $(this).prop('href') ,{
					onClose : function() {
						_this.focus();//关闭时让触发弹窗的元素获取焦点
					},
					title:_this.prop('title')
				});
			}).attr('role','button');

		});
	}
}
function showAll(id)
{
    $.fn.yiiGridView.update(id, {data:{"IvyVisitsRecord[appointment_date]":"", "IvyschoolsVisit[visit_date]":""}});
    if(id == 'pending-visit-grid'){
        upPending();
        $( "#pending" ).datepicker( "refresh" );
    }
    else{
        upConfirmed();
        $( "#confirmed" ).datepicker( "refresh" );
    }
}
$(document).ready(function(){
    var schoolsCount = <?php echo $schoolsCount;?>;
    for(var schoolid in schoolsCount){
        $('#branch-selector div.list a#school_'+schoolid).append('<i class="extra-number">'+schoolsCount[schoolid]+'</i>');
    }
})
</script>
<style>
    .ui-datepicker{
        width: auto;
    }
</style>