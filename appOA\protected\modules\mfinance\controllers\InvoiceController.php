<?php

class InvoiceController extends BranchBasedController
{
    public $actionAccessAuths = array(
        'index'                     => 'o_A_Access',
        'GetGroupInvoice'           => 'o_A_Access',
        'Manage'                    => 'o_A_Adm_Finance',
        'InvalidInvoice'            => 'o_A_Adm_Finance',
        'CreateInvoice'             => 'o_A_Adm_Finance',
        'SaveEdit'                  => 'o_A_Adm_Finance',
        'RefundInvoice'             => 'o_A_Adm_Finance',
    );
    public $invoiceEdit;
    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'main';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Finance Mgt');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mfinance/invoice/index');
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/xlsx.full.min.js');
        Yii::import('common.models.invoice.*');
        $this->invoiceEdit = array(
            Invoice::STATS_UNPAID => array('title','amount','startdate','enddate','duetime','memo'),
            Invoice::STATS_PAID => array('title','startdate','enddate','memo'),
            Invoice::STATS_PARTIALLY_PAID => array('title','amount','startdate','enddate'),
        );
    }

	public function actionIndex()
	{
        $category = Yii::app()->request->getParam('category','current');
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/excellentexport.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/jquery.jPrintArea.js');
        $this->render('index',array('category'=>$category));
	}

    public function actionGetGroupInvoice(){
        $classid = Yii::app()->request->getPost('classid',0);
        $calendarid = Yii::app()->request->getPost('calendar',0);
        $result = array();
        if (Yii::app()->request->isAjaxRequest && $calendarid){
            Yii::import('common.models.calendar.*');
//            $classObj = IvyClass::model()->findByAttributes(array('classid'=>$classid, 'schoolid'=>$this->branchId));
            $calendarObj = Calendar::model()->findByPk($calendarid);
            $timePoints = explode(',', $calendarObj->timepoints);

            $criteria = new CDbCriteria();
            $criteria->compare('yid', $calendarid);
            $criteria->compare('branchid', $this->branchId);
            $criteria->compare('is_selected', 1);
            $count = CalendarSchool::model()->count($criteria);

//            if($classObj){
            if(1){
                $cModel = ChildProfileBasic::model();
                $cModel->getMetaData()->addRelation(
                    '_studentsInvoices',
                    array(
                        CActiveRecord::HAS_MANY,
                        'Invoice',
                        'childid',
                        'on' => '_studentsInvoices.calendar_id=:calendar_id and _studentsInvoices.status<:status and `inout`=:inout',
                        'params' => array(':calendar_id'=>$calendarid, ':inout'=>'in', ':status'=>Invoice::STATS_CHANGE_ARCHIVED)
                    )
                );
                if(!$count && $classid){
                    $crit = new CDbCriteria;
                    $crit->compare('nextYear.schoolid', $this->branchId);
                    $crit->compare('nextYear.classid', $classid);
                    $crit->with = 'nextYear';
                    $crit->addCondition('nextYear.stat<100 or nextYear.stat=888');
                }
                else{
                    $crit = new CDbCriteria;
                    $crit->compare('t.schoolid', $this->branchId);
                    $crit->compare('t.classid', $classid);
                    //$crit->compare('t.status', '<100');
                    $crit->addCondition('t.status<100 or t.status=888');
                }
                $models = $cModel->with('_studentsInvoices')->findAll($crit);
                $result['calendars'][$calendarid] = $timePoints;
                foreach($models as $_model){
                    $result['students'][$_model->childid] = array(
                        'dob' => $_model->birthday_search,
                        'name' => $_model->getChildName(),
                        'photo' => $_model->photo,
                        'id' => $_model->childid
                    );
                    foreach($_model->_studentsInvoices as $_invoice){
                        $result['invoices'][$_invoice->invoice_id] = array(
                            'childid' => $_invoice->childid,
                            'invoice_id' => $_invoice->invoice_id,
                            'amount' => number_format($_invoice->amount, 2),
                            'original_amount' => $_invoice->original_amount,
                            'title' => $_invoice->title,
                            'memo' => $_invoice->memo,
                            'payment_type' => $_invoice->payment_type,
                            'startdate' => $_invoice->startdate,
                            'enddate' => $_invoice->enddate,
                            'status' => $_invoice->status,
                            'semester' => $_invoice->enddate == 0 ? 0 : ($_invoice->enddate <= $timePoints[2]) ? 1 : 2,
                        );
                    }
                }
            }
        }
        echo CJSON::encode(array('data'=>$result,'state'=>'success'));
        Yii::app()->end();
    }

    public function actionManage($invoiceid){
        Yii::import('common.models.calendar.*');
        Yii::import('application.components.policy.PolicyApi');
        $invoice = Invoice::model()->with(array('invoiceTransaction', 'childprofile', 'classInfo','calendar','workflowRefund'))->findByPk($invoiceid);
        if ($this->branchId == $invoice->schoolid){
            if (!empty($invoice)){
                $policyApi = new PolicyApi($invoice->schoolid);
                $userIds[] = $invoice->userid;
                if(!is_null($invoice->invoiceTransaction)){
                    foreach($invoice->invoiceTransaction as $t){
                        $userIds[] = $t->operator_uid;
                    }
                }
                $userList = User::model()->findAllByPk($userIds);
                foreach($userList as $u){
                    $userNames[$u->uid] = $u->getName();
                }
                $invoice->depositAmount = OA::formatMoney($invoice->original_amount - $invoice->amount);
                $invoice->startdate = ($invoice->startdate) ? OA::formatDateTime($invoice->startdate) : "" ;
                $invoice->enddate = ($invoice->enddate) ?  OA::formatDateTime($invoice->enddate) : "";
                $invoice->duetime =  ($invoice->duetime) ?  OA::formatDateTime($invoice->duetime) : "";
            }
            $cs = Yii::app()->clientScript;
            $cs->registerCoreScript('jquery.ui');
            $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
            $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
            $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
            $this->render('manage', array('invoice'=>$invoice, "userNames"=>$userNames, 'policyApi'=>$policyApi));
        }
    }

    /*
     * 作废账单
     */
    public function actionInvalidInvoice(){
        $invoiceId = Yii::app()->request->getPost('invoiceId',0);
        $branchId = Yii::app()->request->getPost('branchId',null);
        if ($invoiceId){
            $model = Invoice::model()->findByPk($invoiceId);
            if ($model->schoolid == $branchId && $model->status == Invoice::STATS_UNPAID){
                $model->status = Invoice::STATS_CANCELLED;
                $model->send_timestamp = time();
                if ($model->save()){
                    TemporaryDeposit::model()->deleteByPk($invoiceId);
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message','success'));
                    $this->addMessage('refresh', true);

                }else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '失败');
                }
            }else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '错误的账单');
            }
        }
        $this->showMessage();
    }

    public function actionCreateInvoice()
    {
        if(isset($_POST['postData'])){
            $postData = $_POST['postData'];
            if($postData['students']){
                Yii::import('common.models.calendar.*');
                Yii::import('common.models.invoice.Invoice');
                $calendarObj = Calendar::model()->findByPk($postData['calendarid']);
                $timePoints = explode(',', $calendarObj->timepoints);
                // 判断所选时间跟校历区间是否相符
                $startTimepoint = $timePoints[0];
                $endTimepoint = $timePoints[3];

                if ($postData['startdate'] && $postData['enddate']) {
                    if ($postData['startdate'] > $postData['enddate']) {
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', '账单开始日期不能大于结束日期');
                        $this->showMessage();
                    }
                }

                if ($postData['startdate'] && strtotime($postData['startdate']) < $startTimepoint) {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '账单开始日期不在该学年范围内');
                    $this->showMessage();
                }

                if ($postData['enddate'] && strtotime($postData['enddate']) > $endTimepoint) {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '账单结束日期不在该学年范围内');
                    $this->showMessage();
                }

                foreach($postData['students'] as $childid){
                    $model = new Invoice();
                    if( in_array($postData['payment_type'], array('registration', 'entrance', 'deposit')) ){
                        $model->setScenario('nodate');
                    }
                    else{
                        $model->setScenario('daystar');
                    }
                    $model->calendar_id = $postData['calendarid'];
                    $model->amount = $postData['amount'];
                    $model->original_amount = $postData['amount'];
                    $model->schoolid = $this->branchId;
                    $model->classid = $postData['classid'];
                    $model->childid = $childid;
                    $model->payment_type = $postData['payment_type'];
                    $model->inout = 'in';
                    if(in_array($model->payment_type, array('tuition', 'lunch', 'bus'))){
                        $model->fee_type = 2;
                    }
                    else{
                        $model->fee_type = 0;
                    }
                    $model->startdate = strtotime($postData['startdate']);
                    $model->enddate = strtotime($postData['enddate']);
                    $model->duetime = strtotime($postData['duetime']);
                    $model->title = $postData['title'];
                    $model->memo = $postData['memo'];
                    $model->userid = Yii::app()->user->id;
                    $model->timestamp = time();
                    $model->status = 10;
                    $service  = array(
                        'mon' => '10',
                        'tue' => '10',
                        'wed' => '10',
                        'thu' => '10',
                        'fri' => '10',
                    );
                    $model->child_service_info = serialize($service);
                    if(!$model->save()){
                        $this->addMessage('state', 'fail');
                        $err = current($model->getErrors());
                        $this->addMessage('message', $err[0]);
                        $this->showMessage();
                    }
                    $result[$model->invoice_id] = array(
                        'childid' => $model->childid,
                        'invoice_id' => $model->invoice_id,
                        'amount' => number_format($model->amount, 2),
                        'original_amount' => $model->original_amount,
                        'title' => $model->title,
                        'memo' => $model->memo,
                        'payment_type' => $model->payment_type,
                        'startdate' => $model->startdate,
                        'enddate' => $model->enddate,
                        'status' => $model->status,
                        'semester' => $model->enddate == 0 ? 0 : (($model->enddate <= $timePoints[2]) ? 1 : 2),
                    );
                }
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('callback', 'cb');
                $this->addMessage('data', $result);
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '请选择孩子');
            }
        }
        $this->showMessage();
    }

    //编辑账单
    public function actionSaveEdit(){
        $invoiceId = Yii::app()->request->getPost('invoiceId',0);
        if ($invoiceId){
            $model = Invoice::model()->findByPk($invoiceId);
            $amount = $model->amount;
            if (!empty($model) && $model->schoolid = $this->branchId){
                $model->attributes = $_POST['Invoice'];
                $model->original_amount = $model->amount;
                $model->startdate = (!empty($_POST['Invoice']['startdate'])) ? strtotime($_POST['Invoice']['startdate']) : 0;
                $model->enddate = (!empty($_POST['Invoice']['enddate'])) ? strtotime($_POST['Invoice']['enddate']) : 0;
                if (isset($_POST['Invoice']['duetime'])) {
                    $model->duetime = strtotime($_POST['Invoice']['duetime']);
                }
                if ($model->enddate < $model->startdate){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '结束日期不能小于开始日期.');
                    $this->showMessage();
                }
                switch ($model->status){
                    case Invoice::STATS_UNPAID:
                    case Invoice::STATS_PARTIALLY_PAID:
                        //针对部分付清更改金额判断
                        if ($model->status == Invoice::STATS_PARTIALLY_PAID){
                            $sumAmount = InvoiceTransaction::model()->findAllBySql('select sum(amount) as amount from '.InvoiceTransaction::model()->tableName().' where invoice_id=:invoice_id', array(':invoice_id'=>$invoiceId));
                            $balance = $_POST['Invoice']['amount']-$sumAmount[0]->amount;
                            if ($balance == 0){
                                //更新帐单
                                Yii::import('application.components.policy.PolicyApi');
                                $policyApi = new PolicyApi($model->schoolid);
                                $policyApi->Pay(array($model),InvoiceTransaction::TYPE_CASH);
                            }elseif($balance<0.001){
                                $this->addMessage('state', 'fail');
                                $this->addMessage('message', '更改后的金额不能小于已付款.');
                                $this->showMessage();
                            }
                        }
                        break;
                    case Invoice::STATS_PAID:
                        $model->amount = $amount;
                        $tModels = InvoiceTransaction::model()->findAll('invoice_id=:invoice_id',array(':invoice_id'=>$invoiceId));
                        foreach ($tModels as $val){
                            $tAmount = $val->amount;
                            $val->attributes = $_POST['Invoice'];
                            if (($model->startdate) && ($model->enddate)){
                                $val->startdate = $model->startdate;
                                $val->enddate = $model->enddate;
                            }
                            $val->amount = $tAmount;
                            $val->save();
                        }
                        break;
                }
                if ($model->save()){
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message','success'));
                    $this->addMessage('refresh', true);
                }else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '失败');
                }
            }
            $this->showMessage();
        }
    }

    //账单退费
    public function actionRefundInvoice(){
        Yii::import('application.components.policy.PolicyApi');
        $invoiceId = Yii::app()->request->getPost('invoiceId',0);
        if ($invoiceId){
            $model = Invoice::model()->findByPk($invoiceId);
            if (!empty($model) && ($model->schoolid = $this->branchId) && ($model->status == Invoice::STATS_PAID)){
                $refundModel = new InvoiceChildRefund();
                $refundModel->attributes = $_POST['InvoiceChildRefund'];
                $refundModel->transaction_id = 0;
                $refundModel->invoice_id = 0;
                $refundModel->on_invoice_id = $invoiceId;
                $refundModel->childid = $model->childid;
                $refundModel->yid = $model->calendar_id;
                $refundModel->schoolid = $model->schoolid;
                $refundModel->classid = $model->classid;
                $refundModel->status = InvoiceChildRefund::STATS_AWAITING;
                $refundModel->refund_type = 10;
                $refundModel->payment_type = $model->payment_type;
                $refundModel->userid = Yii::app()->user->getId();
                $refundModel->startdate = strtotime($refundModel->startdate);
                $refundModel->enddate = strtotime($refundModel->enddate);
                $refundModel->timestamp = strtotime($refundModel->timestamp);
                $refundModel->setScenario('refund');
                if ($refundModel->validate()){
                    if ($refundModel->amount<=0){
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', '退费金额必须大于零.');
                        $this->showMessage();
                    }
                    if (($refundModel->startdate < $model->startdate) || ($refundModel->enddate > $model->enddate)  || ($refundModel->startdate > $refundModel->enddate)){
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', '退费期间必须在原账单期间内.');
                        $this->showMessage();
                    }
                    //查询账单已退费金额
                    $command = Yii::app()->db->createCommand();
                    $command->from(InvoiceChildRefund::model()->tableName());
                    $command->where('on_invoice_id=:on_invoice_id',array(':on_invoice_id'=>$invoiceId));
                    $command->andWhere(array('in','status',array(10,20,77)));
                    $amountd = $command->select('sum(amount) as amount')->queryRow();
                    $sumAmount = 0;
                    if (!empty($amountd['amount'])){
                        $sumAmount = $amountd['amount'];
                    }
                    if ($model->amount - $sumAmount < $refundModel->amount){
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', '退费金额大于账单金额.');
                        $this->showMessage();
                    }
                    //向退费表保存
                    if ($refundModel->save()){
                        //向账单表保存数据
                        $transaction = Yii::app()->db->beginTransaction();
                        $data = array(
                             'calendar_id'=>$model->calendar_id,
                             'schoolid'=>$model->schoolid,
                             'classid'=>$model->classid,
                             'childid'=>$model->childid,
                             'payment_type'=>$model->payment_type,
                             'inout'=>'out',
                             'startdate'=>$refundModel->startdate,
                             'enddate'=>$refundModel->enddate,
                             'title'=>$refundModel->title,
                             'amount'=>  $refundModel->amount,
                        );
                        $newInvoiceModel = new Invoice();
                        $newInvoiceModel->setAttributes($data);
                        $newInvoiceModel->original_amount = $refundModel->amount;
                        $newInvoiceModel->fee_type = 0;
                        $newInvoiceModel->duetime = 0;
                        $newInvoiceModel->child_service_info = $model->child_service_info;
                        $newInvoiceModel->discount_id = $model->discount_id;
                        $newInvoiceModel->fee_program = $model->fee_program;
                        $newInvoiceModel->userid = Yii::app()->user->getId();
                        $newInvoiceModel->timestamp = time();
                        $newInvoiceModel->status = Invoice::STATS_PAID;
                        if (!$newInvoiceModel->save()) {
                            $transaction->rollBack();
                            $this->addMessage('state', 'fail');
                            $this->addMessage('message', '失败');
                            $this->showMessage();
                        }
                        $tranModel = new InvoiceTransaction;
                        $tranModel->setAttributes($data);
                        $tranModel->invoice_id = $newInvoiceModel->invoice_id;
                        $tranModel->timestampe = time();
                        $tranModel->fee_type = 0;
                        $tranModel->operator_uid = Yii::app()->user->getId();
                        $tranModel->transactiontype = InvoiceTransaction::TYPE_TOCREDIT;
                        if (!$tranModel->save()) {
                            $transaction->rollBack();
                            $this->addMessage('state', 'fail');
                            $this->addMessage('message', '失败');
                            $this->showMessage();
                        }
                        //向个户表保存
                        $balanceAmount = PolicyApi::getCreditAmount($data['childid'])+$data['amount'];
                        $credit = new ChildCredit;
                        $credit->classid = $data['classid'];
                        $credit->schoolid = $data['schoolid'];
                        $credit->yid = $data['calendar_id'];
                        $credit->childid = $data['childid'];
                        $credit->amount = $data['amount'];
                        $credit->inout = 'in';
                        $credit->itemname = $data['payment_type'];
                        $credit->invoice_id = $newInvoiceModel->invoice_id;
                        $credit->transaction_id = $tranModel->id;
                        $credit->userid = Yii::app()->user->getId();
                        $credit->updated_timestamp = time();
                        $credit->balance = $balanceAmount;
                        if (!$credit->save()){
                            $transaction->rollBack();
                            $this->addMessage('state', 'fail');
                            $this->addMessage('message', '失败');
                            $this->showMessage();
                        }
                        $childModel = ChildProfileBasic::model()->findByPk($data['childid']);
                        $childModel->credit = $balanceAmount;
                        if (!$childModel->save()){
                            $transaction->rollBack();
                            $this->addMessage('state', 'fail');
                            $this->addMessage('message', '失败');
                            $this->showMessage();
                        }
                        $transaction->commit();
                        //更新帐单退费表
                        $refundModel->invoice_id = $credit->invoice_id;
                        $refundModel->transaction_id = $credit->transaction_id;
                        $refundModel->status = Invoice::STATS_PAID;
                        if ($refundModel->save()){
                            $this->addMessage('state', 'success');
                            $this->addMessage('message', Yii::t('message','success'));
                            $this->addMessage('refresh', true);
                        }else{
                            $this->addMessage('state', 'fail');
                            $this->addMessage('message', '失败');
                            $this->showMessage();
                        }
                    }

                }else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('error', $refundModel->getErrors());
                    $this->addMessage('message', '请填写必选项.');
                }
            }
            $this->showMessage();
        }
    }

    // 显示作废账单
    public function actionCanceledInvoices()
    {
        $this->branchSelectParams['urlArray'] = array('//mfinance/invoice/canceledInvoices');
        $this->render('canceledInvoices');
    }
	// 查询某个时间段的作废账单
	public function actionInsertCanceledInvoices(){
		$startdate = Yii::app()->request->getParam('startdate', '');
		$enddate = Yii::app()->request->getParam('enddate', '');
		$schoolid = $_GET['branchId'];
		$startdate = strtotime($startdate);
		$enddate = strtotime($enddate.' 23:59:59');
		if($startdate && $enddate){
			if( $enddate-$startdate < 5184000){//查询期限不得大于60天
				$founds = $this->getCancelInvoice($schoolid, $startdate, $enddate);
				$data = $this->renderPartial('_canceledInvoices',array('invoice' => $founds), true);
				$this->addMessage('state', 'success');
				$this->addMessage('callback', 'cbInvoice');
				$this->addMessage('data', $data);
				$this->addMessage('message', Yii::t('message','Data saved!'));
			}else{
				$this->addMessage('state', 'fail');
				$this->addMessage('message', '查看作废账单，开始和结束日期不能超过60天');
				$this->showMessage();
			}
		}else{
			$this->addMessage('state', 'fail');
            $this->addMessage('message', '请先选择开始日期、结束日期！');
		}
		$this->showMessage();
	}

    // 导出作废账单
    public function actionExportCanceledInvoices()
    {
		Yii::import('application.components.policy.PolicyApi');
		$policyApi = new PolicyApi($invoice->schoolid);
		$startdate =strtotime($_GET['startdate']);
		$enddate = strtotime($_GET['enddate']);
		$schoolid =  $_GET['branchId'];
		$founds = $this->getCancelInvoice($schoolid, $startdate, $enddate);
		Yii::import('common.models.invoice.*');
        Yii::import('common.models.portfolio.ChildStudyHistory');
		$title = $_GET['branchId'] .'院校' . $startdate . '到'.$enddate .'作废单';
		header("Content-type:application/vnd.ms-excel");
        header("Content-Disposition:attachment;filename=".$title.".xls");
        echo $this->_t("班级ID \t 班级 \t 学生ID \t 学生姓名 \t 标题  \t  类型 \t 开始时间 \t 结束时间 \t 作废时间 \t 金额 \t 状态 \n");
        foreach($founds as $v){
			$v['startdate'] = date("Y-m-d",$v['startdate']);
			$v['enddate'] = date("Y-m-d",$v['enddate']);
			$v['send_timestamp'] = date("Y-m-d H:i:s",$v['send_timestamp']);
            echo $this->_t($v['classid']."\t");
            echo $this->_t($v->classInfo->title."\t");
            echo $this->_t($v['childid']."\t");
            echo $this->_t($v['schoolid']."\t");
            echo $this->_t($v->childprofile->getChildName()."\t");
            echo $this->_t($policyApi->renderFeeType($v['payment_type'])."\t");
            echo $this->_t($v['startdate']."\t");
            echo $this->_t($v['enddate']."\t");
            echo $this->_t($v['send_timestamp']."\t");
            echo $this->_t($v['amount']."\t");
			if($v['apportion_status']){
            echo $this->_t('已导入'."\n");
			}else{
			 echo $this->_t('未导入'."\n");
			}
        }
    }

	public function _t($str)
    {
        return iconv('utf-8', 'GBK', $str);
    }

	//作废账单查找
	public function getCancelInvoice($branchId,$startdate,$enddate){
			Yii::import('common.models.invoice.Invoice');

			$params['schoolid'] = $branchId;
			$status = Invoice::STATS_CANCELLED;
			$criteria = new CDbCriteria;
			$criteria->compare('status',$status);
			$criteria->compare('schoolid',$params);
			$criteria->compare('send_timestamp','>='.$startdate);
			$criteria->compare('send_timestamp','<='.$enddate);
			$founds = Invoice::model()->findAll($criteria);
			return $founds;
	}
}
