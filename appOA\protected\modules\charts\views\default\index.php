<script type="text/javascript">
    $(function() {
        var quarter = [<?php echo $quarter?>];
        var chart = {
            chart: {
                type: 'line'
            },
            title: {
                text: ''
            },
            subtitle: {
                text: 'Source: ivyonline.cn'
            },
            xAxis: {
                categories: quarter
            },
            yAxis: {
                title: {
                    text: 'Values'
                }
            },
            plotOptions: {
                line: {
                    dataLabels: {
                        enabled: true
                    }
                }
            },
            series: []
        };

    <?php foreach ($testData as $key=>$data):?>
        chart.chart.type = 'line';
        chart.title.text = '<?php echo $key?> Campus';
        chart.series = [{
                name: 'Budgeted Enrollment number',
                data: [<?php echo implode(',', $data['budgeted_number'])?>]
            }, {
                name: 'Autual No.',
                data: [<?php echo implode(',', $data['autual_number'])?>]
            }];
        $('#container_<?php echo $key?>_1').highcharts(chart);

        chart.chart.type = 'bar';
        chart.title.text = '<?php echo $key?> Campus';
        chart.series = [{
                name: 'Budgeted Revenue',
                data: [<?php echo implode(',', $data['budgeted_revenue'])?>]
            }, {
                name: 'Autual Revenue',
                data: [<?php echo implode(',', $data['autual_revenue'])?>]
            }];
        $('#container_<?php echo $key?>_2').highcharts(chart);
    <?php endforeach; ?>
    });
</script>

<?php foreach ($testData as $key=>$data):?>
<div class="row">
    <div class="col-sm-12 col-md-6 col-lg-6" id="container_<?php echo $key?>_1"></div>
    <div class="col-sm-12 col-md-6 col-lg-6" id="container_<?php echo $key?>_2"></div>
</div>
<hr>
<?php endforeach; ?>