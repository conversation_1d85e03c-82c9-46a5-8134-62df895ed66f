<?php

class CategoryController extends ProtectedController
{
	/**
	 * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
	 * using two-column layout. See 'protected/views/layouts/column2.php'.
	 */
	public $layout='//layouts/column2';

	/**
	 * Creates a new model.
	 * If creation is successful, the browser will be redirected to the 'view' page.
	 */
	public function actionCreate()
	{
		$id = Yii::app()->request->getParam('id',null);
		$categoryList = IvyPCategory::model()->categoryList();
		if (!empty($id))
		{
			$model = $this->loadModel($id);
			$model->fullId = $model->id;
			$model->abbId = IvyPCategory::getAbbId($model->fullId);
			$model->parentCategory = IvyPCategory::getParentCategory($model->fullId);
			unset($categoryList[$model->fullId]);
		}
		else
		{
			$model = new IvyPCategory;
		}
		// Uncomment the following line if AJAX validation is needed
		// $this->performAjaxValidation($model);
	
		if(isset($_POST['IvyPCategory']))
		{
			$model->attributes=$_POST['IvyPCategory'];
			$model->create_userid = Yii::app()->user->id;
			$model->update_timestamp = time();
			$model->id = empty($_POST['IvyPCategory']['parentCategory']) ? $model->abbId : $_POST['IvyPCategory']['parentCategory'].'_'.$model->abbId;
			if($model->save())
			{
				$model->refresh();
                $this->redirect(array('admin'));
			}
				
		}
		$this->render('create',array(
			'model'=>$model,'categoryList'=>$categoryList
		));
	}
	

	/**
	 * Deletes a particular model.
	 * If deletion is successful, the browser will be redirected to the 'index' page.
	 * @param integer $id the ID of the model to be deleted
	 */
	public function actionDelete($id)
	{
		if(Yii::app()->request->isPostRequest)
		{
			// we only allow deletion via POST request
			if (IvyPCategory::model()->existsChildCategory($id) === true)
			{
				throw new CHttpException(403, '此类别下已经存在子类');
			}
			elseif (IvyPCategory::model()->existsProduct($id) === true)
			{
				throw new CHttpException(403, '产品库已经使用此类别 ');
			}
			else
			{
				$this->loadModel($id)->delete();
				// if AJAX request (triggered by deletion via admin grid view), we should not redirect the browser
				if(!isset($_GET['ajax']))
					$this->redirect(isset($_POST['returnUrl']) ? $_POST['returnUrl'] : array('admin'));
			}
		}
		else
			throw new CHttpException(400,'Invalid request. Please do not repeat this request again.');
	}

	/**
	 * Manages all models.
	 */
	public function actionAdmin()
	{
		$model=new IvyPCategory('search');
		$model->unsetAttributes();  // clear any default values
		if(isset($_GET['IvyPCategory']))
			$model->attributes=$_GET['IvyPCategory'];

		$this->render('admin',array(
			'model'=>$model,
		));
	}

	/**
	 * Returns the data model based on the primary key given in the GET variable.
	 * If the data model is not found, an HTTP exception will be raised.
	 * @param integer the ID of the model to be loaded
	 */
	public function loadModel($id)
	{
		$model=IvyPCategory::model()->findByPk($id);
		if($model===null)
			throw new CHttpException(404,'The requested page does not exist.');
		return $model;
	}

	/**
	 * Performs the AJAX validation.
	 * @param CModel the model to be validated
	 */
	protected function performAjaxValidation($model)
	{
		if(isset($_POST['ajax']) && $_POST['ajax']==='ivy-pcategory-form')
		{
			echo CActiveForm::validate($model);
			Yii::app()->end();
		}
	}
}
