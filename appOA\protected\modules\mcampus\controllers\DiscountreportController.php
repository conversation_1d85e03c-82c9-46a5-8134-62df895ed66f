<?php

class DiscountreportController extends BranchBasedController{

    public function createUrl($route, $params = array(), $ampersand = '&', $parentOnly = false)
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";
       
        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = false;
        $this->branchSelectParams['urlArray'] = array('//mcampus/discountreport/index');
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue2.js');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile($cs->getCoreScriptUrl().'/jui/js/jquery-ui.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/xlsx.full.min.js');
    }

    public function actionIndex()
    {
        $this->checkAccess();
        $this->render('index');
    }

    public function actionGetData()
    {
        $this->checkAccess();
        $date = Yii::app()->request->getParam('date');
        $timestamp = strtotime($date) ? strtotime($date) : time();
        Yii::import('common.models.invoice.*');
        // 查找所选日期有效期内的账单ID
        $crit = new CDbCriteria();
        $crit->compare('schoolid', $this->branchId);
        $crit->compare('startdate', '<='.$timestamp);
        $crit->compare('payment_type', 'tuition');
        $crit->compare('enddate', '>='.$timestamp);
        $crit->select = 'invoice_id';
        $crit->index = 'invoice_id';
        $services = ChildServiceInfo::model()->findAll($crit);
        // 查找账单详情
        $invoiceIds = array_keys($services);
        // 查找未付的折扣账单
        $crit = new CDbCriteria();
        $crit->compare('schoolid', $this->branchId);
        $crit->compare('payment_type', 'tuition');
        $crit->compare('`inout`', 'in');
        $crit->compare('status', array(Invoice::STATS_UNPAID, Invoice::STATS_PARTIALLY_PAID));
        $crit->compare('startdate', '<='.$timestamp);
        $crit->compare('enddate', '>='.$timestamp);
        $crit->select = 'invoice_id';
        $crit->index = 'invoice_id';
        $unpaidInvoiceModels = Invoice::model()->findAll($crit);
        $unpaidInvoiceIds = array_keys($unpaidInvoiceModels);

        unset($services);
        unset($unpaidInvoiceModels);
        
        $allInvoiceIds = array_merge($invoiceIds, $unpaidInvoiceIds);
        $data = array(
            'total_invoice_num' => count($allInvoiceIds),
            'discount_invoice_num' => 0,
            'total_invoice_amount' => 0,
            'discount_invoice_amount' => 0,
            'nodiscount_invoice_amount' => 0,
            'items'=>array(),
            'discount_info'=>array(),
        );
        if ($allInvoiceIds) {
            $crit = new CDbCriteria();
            $crit->compare('t.invoice_id', $allInvoiceIds);
            $crit->compare('t.discount_id', '>0');
            $crit->order = 't.status DESC';
            $crit->with = array('childprofile', 'classInfo');
            $invoices = Invoice::model()->findAll($crit);

            
            $childIds = array();
            $flagChildIds = array();
            foreach ($invoices as $i=>$invoice) {
                if (in_array($invoice->childid, $childIds)) {
                        $flagChildIds[] = $invoice->childid;
                }
                if ($invoice->discount) {
                    $childIds[] = $invoice->childid;
                }
            }
            foreach ($invoices as $i=>$invoice) {
                $data['total_invoice_amount'] += $invoice->original_amount;
                if ($invoice->discount) {
                    $data['discount_invoice_num']++;
                    $data['discount_invoice_amount'] += $invoice->original_amount;
                    $data['nodiscount_invoice_amount'] += $invoice->nodiscount_amount;
                    if (!isset($data['discount_info'][$invoice->discount_id])) {
                        $data['discount_info'][$invoice->discount_id]['invoice_amount'] = 0;
                        $data['discount_info'][$invoice->discount_id]['invoice_num'] = 0;
                        $data['discount_info'][$invoice->discount_id]['discount_id'] = $invoice->discount_id;
                    }
                    $flag = false;
                    if (in_array($invoice->childid, $flagChildIds)) {
                        $flag = true;
                    }
                    $data['discount_info'][$invoice->discount_id]['invoice_num'] ++;
                    $data['discount_info'][$invoice->discount_id]['invoice_amount'] += $invoice->original_amount;
                    $data['items'][$invoice->discount_id][$invoice->invoice_id]['childid'] = $invoice->childid;
                    $data['items'][$invoice->discount_id][$invoice->invoice_id]['childeduid'] = $invoice->childprofile->educational_id;
                    $data['items'][$invoice->discount_id][$invoice->invoice_id]['child_name'] = $invoice->childprofile->getChildName();
                    $data['items'][$invoice->discount_id][$invoice->invoice_id]['class_title'] = $invoice->classInfo->title;
                    $data['items'][$invoice->discount_id][$invoice->invoice_id]['invoive_title'] = $invoice->title;
                    $data['items'][$invoice->discount_id][$invoice->invoice_id]['invoice_id'] = $invoice->invoice_id;
                    $data['items'][$invoice->discount_id][$invoice->invoice_id]['nodiscount_amount'] = $invoice->nodiscount_amount;
                    $data['items'][$invoice->discount_id][$invoice->invoice_id]['amount'] = $invoice->original_amount;
                    $data['items'][$invoice->discount_id][$invoice->invoice_id]['invoive_title'] = $invoice->title;
                    $data['items'][$invoice->discount_id][$invoice->invoice_id]['discount_id'] = $invoice->discount_id;
                    $data['items'][$invoice->discount_id][$invoice->invoice_id]['flag'] = $flag;
                    // 1 未付 2 部分付 3 已付
                    $data['items'][$invoice->discount_id][$invoice->invoice_id]['status'] = $invoice->status == Invoice::STATS_UNPAID ? 1 : ($invoice->status == Invoice::STATS_PARTIALLY_PAID ? 2 : 3);
                    $monthNum = (date("Y", $invoice->enddate)-date("Y", $invoice->startdate))*12+date("m", $invoice->enddate)-date("m", $invoice->startdate) + 1;
                    $data['items'][$invoice->discount_id][$invoice->invoice_id]['monthly'] = ($invoice->nodiscount_amount-$invoice->original_amount)/$monthNum;
                }
                unset($invoices[$i]);
            }
        }
        // 查找折扣详情
        $discountIds = array_keys($data['items']);
        if ($discountIds) {
            $discounts = DiscountSchool::model()->findAllByPk($discountIds, array('with'=>'discountTitle'));
            foreach ($discounts as $discount) {
                $data['discount_info'][$discount->id]['discount_name'] = $discount->discountTitle->title_cn;
                $data['discount_info'][$discount->id]['discount_discount'] = round($discount->discount,2);
                $data['discount_info'][$discount->id]['discount_name'] = $discount->discountTitle->title_cn;
            }
        }

        $data['total_invoice_amount_str'] = number_format($data['total_invoice_amount'], 2);
        $data['total_invoice_num_str'] = number_format($data['total_invoice_num']);
        $data['discount_invoice_num_str'] = number_format($data['discount_invoice_num']);
        $data['discount_invoice_amount_str'] = number_format($data['discount_invoice_amount'], 2);
        $data['nodiscount_invoice_amount_str'] = number_format($data['nodiscount_invoice_amount'], 2);
        $data['invoice_discount_amount_str'] = number_format($data['nodiscount_invoice_amount'] - $data['discount_invoice_amount'], 2);

        $data['invoice_percent'] =  round($data['discount_invoice_num'] / $data['total_invoice_num'] * 100, 2);
        $data['invoice_discount_percent'] = number_format($data['discount_invoice_amount'] / $data['nodiscount_invoice_amount'] * 10, 2);

        $this->addMessage('state', 'success');
        $this->addMessage('data', $data);
        $this->showMessage();
    }

    public function checkAccess()
    {
        Yii::import('common.models.invoice.DiscountSchool');
        if (!DiscountSchool::checkView($this->branchId, Yii::app()->user->roles, Yii::app()->user->id)) {
            $this->render('//denied/index');
            Yii::app()->end();
        }
    }
}
