<?php

class HandoverController extends BranchBasedController
{
    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

        $this->branchSelectParams['urlArray'] = array('//masa/handover/index');

        // jquery ui
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue2.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/jquery.printThis.js');
    }

	public function actionIndex($groupId = 0)
    {
        $courseGroup = AsaCourseGroup::getGroup($this->branchId, false, false, false);
        
        $newCourseGroup = array();
        $courseGroupArr = json_decode(CJSON::encode($courseGroup), TRUE);
        foreach ($courseGroupArr as $v) {
            $newCourseGroup[$v['startyear']][] = $v;
        }

        // 获取共享课程
        $criteria=new CDbCriteria;
        $criteria->compare('site_id', $this->branchId);
        $criteria->index = "group_id";
        $share = AsaCourseGroupShare::model()->findAll($criteria);
        if($share){
            $criteria=new CDbCriteria;
            $criteria->compare('id', array_keys($share));
            $criteria->index = "id";
            $sharegroup = AsaCourseGroup::model()->findAll($criteria);
        }
        foreach ($sharegroup as $item) {
            $newCourseGroup[$item->startyear][] = $item->attributes;
        }
        
        $startyearArr = array_keys($newCourseGroup);

        $crit = new CDbCriteria;
        $crit->compare('program_id', $groupId);
        $crit->compare('site_id', $this->branchId);
        $cashHandover = new CActiveDataProvider('AsaCashHandover', array(
            'criteria' => $crit,
            'sort' => array(
                'defaultOrder' => 'created DESC',
            ),
            'pagination'=>array(
                'pageSize'=>50,
            ),
        ));

        // 当前课程组下的所有课程
        $this->render('index', array(
//            'courseGroup' => $courseGroup,
            'newCourseGroup' => $newCourseGroup,
            'startyearArr' => $startyearArr,
            'groupId' => $groupId,
            'cashHandover' => $cashHandover,
        ));
	}

    public  function actionHandoverList($groupId)
    {
        if($groupId){
            //当前课程组下的所有课程
            $criteria = new CDbCriteria;
            $criteria->compare('t.gid', $groupId);
            // $criteria->compare('t.schoolid', $this->branchId);
            $criteria->index = 'id';
            $courseObjs = AsaCourse::model()->findAll($criteria);
            if($courseObjs){
                $courseData = array();
                foreach($courseObjs as $courseId => $course){
                    $courseData[$courseId]  = array(
                        'title' => $course->getTitle(),
                        'classCount' => $course->default_count,
                        'amount' => $course->unit_price,
                    );
                }
                //未做现金交接的账单
                $criteria = new CDbCriteria;
                $criteria->compare('schoolid', $this->branchId);
                $criteria->compare('course_group_id', $groupId);
                $invoiceItemObjs = AsaInvoiceItem::model()->findAll($criteria);
                $courseItem = array();
                if($invoiceItemObjs){
                    foreach($invoiceItemObjs as $k=>$course){
                        $courseItem[$course->order_id][$course->course_id] = array(
                            'title' =>$courseData[$course->course_id]['title'],
                            'classCount' =>$courseData[$course->course_id]['classCount'],
                            'amount' =>$courseData[$course->course_id]['amount'],
                        );
                    }
                    $criteria = new CDbCriteria;
                    $criteria->compare('t.id', array_keys($courseItem));
                    $criteria->compare('t.schoolid', $this->branchId);
                    $criteria->compare('t.pay_type', AsaInvoice::CASH);
                    $criteria->compare('t.cash_handover_id', 0);
                    $criteria->order = 'updated ASC' ;//排序条件
                    $invoiceObjs = AsaInvoice::model()->findAll($criteria);
                    if($invoiceObjs){
                        $teacherList = array();
                        foreach($invoiceObjs as $_teacher){
                            $teacherList[] = $_teacher->updated_userid;
                        }
                        $teacherNames = User::model()->findAllByPk($teacherList);
                        $teacherListName = array();
                        foreach($teacherNames as $_teache)
                        {
                            $teacherListName[$_teache->uid] = $_teache->getName();
                        }

                        $handovers = array();
                        foreach($invoiceObjs as $k=>$invoice){
                            $handovers[$k]  =  array(
                                'incoiceid' => $invoice->id,
                                'title' => $invoice->title,
                                'amount' => $invoice->amount_actual,
                                'updated' => date("Y-m-d",$invoice->updated),
                                'updated_by' => $teacherListName[$invoice->updated_userid],
                                'courseItems' => $courseItem[$invoice->id],
                            );
                        }
                        $this->addMessage('state', 'success');
                        $this->addMessage('data', $handovers);
                        $this->showMessage();
                    }else{
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('global', "没有账单"));
                        $this->showMessage();
                    }

                }else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('global', "没有课程账单"));
                    $this->showMessage();
                }
            }else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('global', "没有课程"));
                $this->showMessage();
            }
        }else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('global', "课程组不能为空"));
            $this->showMessage();
        }
    }

    public function actionUpdateHandover()
    {
        $groupId = Yii::app()->request->getParam('groupId', '');
        $invoices = Yii::app()->request->getParam('invoices', array());
        $createrMemo = Yii::app()->request->getParam('creater_memo', '');
        if($invoices){
            $invoicesId = array_keys($invoices);
            $invoiceList = AsaInvoice::model()->findAllByPk($invoicesId);
            if($invoiceList){
                $numInvoiceId = "";
                foreach($invoiceList as $invoice){
                    $numInvoiceId += $invoice->amount_actual;
                }
                $model = new AsaCashHandover;
                $model->setAttributes(array(
                    'site_id' => $this->branchId,
                    'program_id' => $groupId,
                    'amount' => $numInvoiceId,
                    'item_number' => count($invoiceList),
                    'invoice_ids' => implode(',',$invoicesId),
                    'status' => 0,
                    'creater'=> Yii::app()->user->id,
                    'created'=> time(),
                    'handover_type'=> 1,
                    'creater_memo'=> $createrMemo,
                    ));
                if($model->save()){
                    $criteria = new CDbCriteria;
                    $criteria->compare('id', $invoicesId);
                    AsaInvoice::model()->updateAll(array('cash_handover_id'=>$model->id), $criteria);

                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message','success'));
                    $this->addMessage('callback', 'cbUpdateCash');
                    $this->showMessage();
                }else{
                    $error = current($model->getErrors());
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('message', $error[0]));
                    $this->showMessage();
                }
            }else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('global', "没有所选的账单"));
                $this->showMessage();
            }
        }else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('global', "请选择账单"));
            $this->showMessage();
        }
    }

    public function actionDelHandover()
    {
        $cashHandoverId = Yii::app()->request->getParam('id','');

        if (Yii::app()->request->isPostRequest) {
            if($cashHandoverId){
                $cashHandover = AsaCashHandover::model()->findByPk($cashHandoverId);
                if($cashHandover->site_id == $this->branchId){
                    if(in_array($cashHandover->status, array( AsaCashHandover::STATUS_DRAFT,AsaCashHandover::STATUS_REJECTED))){
                        $invoices = explode(',',$cashHandover->invoice_ids);
                        if(is_array($invoices) && count($invoices)){
                            $criteria = new CDbCriteria;
                            $criteria->compare('id', $invoices);
                            AsaInvoice::model()->updateAll(array('cash_handover_id'=>0), $criteria);
                            if($cashHandover->delete()){
                                $this->addMessage('state', 'success');
                                $this->addMessage('message', Yii::t('message','success'));
                                $this->addMessage('callback', 'cbUpdateCash');
                                $this->showMessage();
                            }else{
                                $error = current($cashHandover->getErrors());
                                $this->addMessage('state', 'fail');
                                $this->addMessage('message', Yii::t('message', $error[0]));
                                $this->showMessage();
                            }
                        }
                    }else{
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('message', '现金交接已完成,不可删除!'));
                        $this->showMessage();
                    }
                }else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('message', '请选择所在校园的项目'));
                    $this->showMessage();
                }
            }
        }
    }

    public function actionDownloads()
    {
        ob_end_clean();
        $id = Yii::app()->request->getParam('id', 0);
        $model= AsaCashHandover::model()->findByPk($id);
        $fileNames = $model->bank_ref_url;
        $file_dir = Yii::app()->params['xoopsVarPath'] . '/asa/handover/';
        $fileres = file_get_contents($file_dir . $fileNames);
        header('Content-type: image/jpeg');
        echo $fileres;
    }

    public function actionBankRef()
    {
        $cashHandoverId = Yii::app()->request->getParam('id','');
        if($cashHandoverId){
            $model = AsaCashHandover::model()->findByPk($cashHandoverId);
            //转换时间格式
            if($model->bank_deposit_time){
                $model->bank_deposit_time=date('Y-m-d',$model->bank_deposit_time);
            }

            $this->renderPartial('receipt', array(
                'model' => $model,
            ));
        }
    }
    
    public function actionUpdateHandovers($id=0){
        //接收请求参数
        $attributes = $_POST['AsaCashHandover'];
        //查询其他参数
        //$cashModel=AsaCashHandover::model()->find($attributes['id']);

        $model= $this->loadModel($id);
        if(in_array($model->status, array( AsaCashHandover::STATUS_DRAFT,AsaCashHandover::STATUS_REJECTED))){
            $model->setAttributes(array(
                'bank_ref' => $attributes['bank_ref'],
                'bank_deposit_time' => strtotime($attributes['bank_deposit_time']),
                'creater_memo' => $attributes['creater_memo'],
                'status' => $attributes['status'],
                'creater' => Yii::app()->user->id,
            ));
            $model->bank_ref_file=CUploadedFile::getInstance($model,'bank_ref_file');
            $this->getAllBranch();
            if($model->save()){
                if($model->status == 1){
                    $teacher = User::model()->findByPk($model->creater);
                    $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
                    $mailer->Subject = '[' . $this->allBranch[$model->site_id]['abb'] . ']有新的课后课现金交接，请注意处理';
                    $mailer->AddAddress('<EMAIL>');
                    $mailer->AddCC('<EMAIL>');
                    $mailer->AddCC($teacher->email);
                    $mailer->getView('handover', array('model' => $model,'teacher' => $teacher->getName()), 'main');
                    $mailer->iniMail( OA::isProduction()); // 此行代码要放到AddAddress, AddCC方法下面
                    $mailer->Send();
                }
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','提交成功!'));
                $this->addMessage('callback', 'cbUpdateCash');
                $this->showMessage();
            }else{
                $error = current($model->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', $error[0]));
                $this->showMessage();
            }
        }else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', '已经提交到财务不可修该'));
            $this->showMessage();
        }
    }

    public function getButton($data)
    {
        if(in_array($data->status,array(0,99))){
            echo CHtml::link('提交', array('bankRef', 'id' => $data->id, "branchId"=>Yii::app()->controller->branchId), array('class' => 'J_modal btn btn-xs btn-info')) . ' ';
            echo CHtml::link('删除', array('delHandover', 'id' => $data->id, "branchId"=>Yii::app()->controller->branchId), array('class' => 'J_ajax_del btn btn-xs btn-danger'));
        }else{
            echo CHtml::link('查看', array('/mfinance/asa/showHandover', 'id' => $data->id, "branchId"=>Yii::app()->controller->branchId), array('class' => 'J_modal btn btn-xs btn-info drop')) . ' ';
        }
    }

    public function getStatus($data)
    {
        $status = AsaCashHandover::getConfig();
        return $status[$data->status];
    }

    public function loadModel($id)
    {
        $model=AsaCashHandover::model()->findByPk($id);
        if($model===null)
            $model = new AsaCashHandover;
        return $model;
    }

}