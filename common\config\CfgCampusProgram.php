<?php
/**
 * 1-12年级开设的课程
 */

return array(
    Branch::TYPE_CAMPUS_DAYSTAR => array(
        'programs' => array(
            'EN' => '英文 English Language Arts',
            'CH' => '语文 Chinese Language Arts',
            'QKB' => '全科班 QKB',
            'MA' => '数学 Math',
            'LIB' => '图书馆 LIB',
            'PE' => '体育 Physical Education',
            'ART' => '美术 Art',
            'MU' => '音乐 Music',
            'PA' => '表演艺术 Performing Arts',
            'CTE' => '品德教育 Character Education',
            'SN'  => '加餐 Snack',
            'BK' => '课间活动 Recess',
            'SNBK' => '课间活动/加餐 Recess/Snack',
            'LU' => '午餐 Lunch',
            'HR' => '班级时间 Homeroom',
            'ASA' => '社团活动 ASA',
            'LE' => '放学 Dismissal',

            'EM' => 'English Math/英文数学',
            'SS' => 'Social Studies/社会',
            'SCI' => 'Science/科学',
            'RE' => 'Read/阅读',
            'CE' => 'Class meeting/班会',
            'ESL' => 'LEAP',
            'CSL' => '鱼跃 YuYue',
            'PYP' => 'PYP/PYP',
            'ICT' => 'ICT/信息技术',
            'MO' => 'Morning Check/晨检',
            'HW' => 'HomeWork/作业',
            'UN' => 'Unity Time/集会',

            'SCISS' => '科学 Science / 社会 Social Studies',
            'UNIT' => '探究单元 Unit of Inquiry',

            'CALLIGRAPHY' => '书法 Calligraphy',
            'GERMAN' => '德语 German',

            'DRAMA' => '戏剧 Drama',
            'ENRICHMENT' => '增益 Enrichment',
        ),

        //时间段配置必须是11位长；注意前导0
        'schedules' => array(
//            'A' => array(
//                '08:10-08:15',
//                '08:15-08:50',
//                '08:55-09:30',
//                '09:40-10:15',
//                '10:20-10:55',
//                '11:00-11:30',
//                '12:25-13:00',
//                '13:05-13:40',
//                '13:50-14:25',
//                '14:30-15:05',
//                '15:10-15:40'
//            ),
//            'A' => array(
//                '08:15-08:30',
//                '08:30-09:15',
//                '09:25-10:10',
//                '10:20-11:05',
//                '11:05-11:25',
//                '11:25-12:10',
//                '12:10-13:00',
//                '13:00-13:45',
//                '13:55-14:40',
//                '14:40-14:55',
//                '14:55-15:35',
//                '15:45-16:40',
//                '16:50-16:50',
//            ),
//            'B' => array(
//                '08:15-08:30',
//                '08:30-09:15',
//                '09:15-10:00',
//                '10:00-10:10',
//                '10:10-10:30',
//                '10:35-11:20',
//                '11:20-12:05',
//                '12:10-12:55',
//                '13:00-13:45',
//                '13:45-14:30',
//                '14:30-15:15',
//                '15:15-15:35',
//                '15:50-15:50',
//                '16:50-16:50',
//            ),
        //2019-2020
//            'A' => array(
//                '08:15-08:30',
//                '08:30-09:15',
//                '09:15-10:00',
//                '10:00-10:10',
//                '10:10-10:30',
//                '10:35-11:20',
//                '11:20-12:05',
//                '12:10-12:55',
//                '13:00-13:45',
//                '13:45-14:30',
//                '14:30-15:15',
//                '15:15-15:40',
//                '15:45-15:45',
//                '17:00-17:00',
//            ),
//            'B' => array(
//                '08:15-08:30',
//                '08:30-09:15',
//                '09:15-10:00',
//                '10:00-10:10',
//                '10:10-10:30',
//                '10:35-11:20',
//                '11:20-12:05',
//                '12:10-12:55',
//                '13:00-13:45',
//                '13:45-14:30',
//                '14:30-15:15',
//                '15:15-15:40',
//                '15:45-15:45',
//                '17:00-17:00',
//            ),
//          2021年1月19号注释
            /*'A' => array(
                '08:10-08:50',
                '08:50-09:30',
                '09:30-10:10',
                '10:10-10:50',
                '10:50-11:30',
                '11:30-11:55',
                '12:00-12:20',
                '12:20-12:50',
                '12:50-13:30',
                '13:30-14:10',
                '14:10-14:50',
                '14:50-15:30',
                '15:30-15:45',
            ),
            'B' => array(
                '08:10-08:50',
                '08:50-09:30',
                '09:30-10:10',
                '10:10-10:50',
                '10:50-11:30',
                '11:30-12:10',
                '12:10-12:35',
                '12:35-13:00',
                '13:00-13:30',
                '13:30-14:10',
                '14:10-14:50',
                '14:50-15:30',
                '15:30-15:45',
            ),*/
            // 2021年1月19号修改
            'A' => array(
                '08:00-08:10',
                '08:10-08:30',
                '08:30-09:10',
                '09:10-09:50',
                '09:50-10:10',
                '10:10-10:50',
                '10:50-11:30',
                '11:30-11:55',
                '12:00-12:20',
                '12:20-12:40',
                '12:40-13:20',
                '13:20-14:00',
                '14:00-14:10',
                '14:10-14:50',
                '14:50-15:30',
                '15:30-15:45',
            ),
            'B' => array(
                '08:00-08:10',
                '08:10-08:30',
                '08:30-09:10',
                '09:10-09:50',
                '09:50-10:30',
                '10:30-10:50',
                '10:50-11:30',
                '11:30-12:10',
                '12:10-12:35',
                '12:40-13:00',
                '13:00-13:30',
                '13:30-14:10',
                '14:10-14:50',
                '14:50-15:30',
                '15:30-15:45',
            ),
            'C' => array(
                '08:00-08:10',
                '08:10-08:30',
                '08:30-09:10',
                '09:10-09:50',
                '09:50-10:10',
                '10:10-10:50',
                '10:50-11:30',
                '11:30-12:10',
                '12:10-12:35',
                '12:35-13:00',
                '13:00-13:30',
                '13:30-14:10',
                '14:10-14:50',
                '14:50-15:30',
                '15:30-15:45',
            ),
        ),
        //评分和评语可分开保存的programs
        'score_comment_separate' => array(
            'BJ_DS'=>array('CTE',),
        ),
    )
);
