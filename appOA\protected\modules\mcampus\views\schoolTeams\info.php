<div class="container-fluid" id='box'>
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('schoolTeams','Phoenix Athletic Program');?></li>
    </ol>
    <div >
        <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="<?php echo Yii::t("schoolTeams", "Homepage"); ?>" name="home"></el-tab-pane>
            <el-tab-pane label="<?php echo Yii::t("schoolTeams", "Teams"); ?>" name="list"></el-tab-pane>
        </el-tabs>
        <div class='row ' v-if='infoList'>
            <div class='col-md-8'>
                <div class='mt24'>
                    <button type="button" class="btn btn-primary" @click='editContent()'><span class='el-icon-edit'></span> <?php echo Yii::t("schoolTeams", "Edit Homepage Content"); ?></button>
                </div>
                <div class='flex mt24 align-items'>
                    <div class='flex1 font14 color3 fontBold'><?php echo Yii::t('schoolTeams','Important Information');?></div>
                    <button type="button" class="btn btn-primary" @click='addInfo()'><span class='el-icon-circle-plus-outline'></span> <?php echo Yii::t('global','Add');?></button>
                </div>
                <div class='mt20 overflow-y scroll-box' :style="'height:'+(height-342)+'px;overflow-x: hidden;'">
                    <table class='table table-hover tableElent'>
                        <thead>
                            <tr>
                                <th ><?php echo Yii::t('labels','Title');?></th>
                                <th width="120"><?php echo Yii::t('newDS','Applicable to');?></th>
                                <th  width="100"><?php echo Yii::t('curriculum','Attachments');?></th>
                                <th width="230"><?php echo Yii::t("newDS", "Actions"); ?></th>
                            </tr>
                        </thead>
                        <tbody class='table_count' > 
                            <tr v-for='(list,index) in infoList'>
                                <td>{{list.title}} <span class='el-icon-edit ml8 blueColor cur-p'  @click='editInfoList(list)'></span></td>
                                <td> <span v-for='(item,idx) in list.extsList'>{{item}} <span v-if='idx+1<list.extsList.length'>、</span></span></td>
                                <td>
                                    <el-popover
                                        v-if='list.file_list.length>0'
                                        :ref="`popover-${index}`"
                                        placement="bottom"
                                        width="300"
                                        trigger="click"
                                        popper-class='popoverImg'
                                        v-model="tipVisibles[index]"
                                    >
                                    <div>
                                        <div class='mb16 flex align-items'>
                                            <span class='flex1 color3 font14'> {{translate("<?php echo Yii::t('schoolTeams', '%s attachment(s)'); ?>", list.file_list.length)}} </span>
                                            <span class='el-icon-close font16 color6 cur-p' @click='popoverHide(index)'></span>
                                        </div>
                                        <div class='flex flexList' v-for='(list,id) in list.file_list' >
                                            <span class='el-icon-paperclip mr8 font16' ></span>
                                            <a class='flex1' target='_blank' :href='list.file_key'>{{list.title}}</a>
                                            <span class='el-icon-delete ml10 cur-p' @click='delFile(list._id)'></span>
                                        </div>
                                    </div>
                                        <span slot="reference" size='small' class='popoverBtn blueColor cur-p'>{{list.file_list.length}}</span>
                                    </el-popover>
                                    <span slot="reference" size='small' class='popoverBtn' v-else >{{list.file_list.length}}</span>

                                </td>
                                <td class=''>
                                    <button type="button" class="btn btn-link p6" @click='editInfoContent(list)'><?php echo Yii::t('schoolTeams','Edit');?></button>
                                    <el-upload
                                        action="https://up-z1.qiniup.com"
                                        :show-file-list="false"
                                        :on-success="handleAvatarSuccess"
                                        :data="uploadToken"
                                        :before-upload="beforeAvatarUpload"
                                        class="upload-demo">
                                        <button type="button" class="btn btn-link p6" @click='uploadFile(list.id)'><?php echo Yii::t("schoolTeams", "Attachments"); ?></button>
                                        </el-upload>
                                    <button type="button" class="btn btn-link p6" @click='delList(list.id)'><?php echo Yii::t("newDS", "Delete");?></button>
                                    <span class='el-icon-rank font14 ml10 handle cur-p'></span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class='col-md-4'>
                <div style='background:#F2F3F5'  :style="'height:'+(height-212)+'px;overflow-x: hidden;'">
                    <div class='iphone'>                   
                    <div class='pt24 flex align-items'>
                        <span class='flex1 color3 font14 fontBold'><?php echo Yii::t('schoolTeams','Preview');?></span> 
                        <el-popover
                            placement="bottom"
                            width="200"
                            trigger="click"
                            v-model="visible">
                            <div class='text-center p12'>
                                <div class='color3 font14 mb16 fontBold'><?php echo Yii::t('schoolTeams','QRcode for Preview');?></div>
                                <div id='wechatQrcode' class='wechatQrcode'></div>
                            </div>
                            <span></span>
                            <span slot="reference" class='font14 flex align-items cur-p reference' @click="previewHomeUrl"><span class='glyphicon glyphicon-qrcode font14' ></span><span class='ml8'><?php echo Yii::t('schoolTeams','QRcode for Preview');?></span></span>
                        </el-popover>
                    </div>
                    <div class='mobile ' style='scale:0.8'>
                        <div class=''>
                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/phoenix_top.png' ?>" alt="">
                        </div>
                        <div class='overflow-y scroll-box' style='height: 623px;'>
                            <div class='font18 text-center pb24 pt12'><?php echo Yii::t('schoolTeams','Phoenix Athletic Program');?></div>
                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/phoenix.png' ?>" alt="">
                            <div v-html='content' class='contentHtml'></div>
                            <div class='important'>
                                <div class="title_1 mt10">
                                    <div class="title_text" style='color:#111265'><?php echo Yii::t('schoolTeams','Important Information');?></div>
                                    <span class="bg">
                                        <span class="yellow"></span>
                                        <span class="blue"></span>
                                    </span>
                                </div>
                                <div class="listInfo flex" v-for="(list, index) in preview_list" >
                                    <div class="color3 font14 flex1">{{ list.title }}</div>
                                    <span class='el-icon-right blueColor font16'></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 编辑重要信息 -->
    <div class="modal fade" id="editListModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static" >
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t('global','Add');?></h4>
                </div>
                <div class="modal-body p24 overflow-y scroll-box" :style="'max-height:'+(height-180)+'px;overflow-x: hidden;'">
                    <div class='font14 color6 mb12'><?php echo Yii::t("event", "Title Cn"); ?></div>
                    <div class='relative'>
                        <input type="text" class="form-control" v-model='listInfoData.title_cn' placeholder="<?php echo Yii::t("leave", "Input"); ?>">
                    </div>
                    <div class='font14 color6 mb12 mt24'><?php echo Yii::t("event", "Title En"); ?></div>
                    <div class='relative'>
                        <input type="text" class="form-control" v-model='listInfoData.title_en' placeholder="<?php echo Yii::t("leave", "Input"); ?>">
                    </div>
                    <div class='font14 color6 mb12 mt24'><?php echo Yii::t('newDS','Applicable to');?></div>
                    <div>
                        <label class="checkbox-inline mr16" v-for='(list,index) in school_list'>
                            <input type="checkbox" :value="list.key"  v-model='listInfoData.ext'> {{list.value}}
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" :disabled='btnDisanled' @click='savelList()'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 编辑重要信息内容 -->
    <div class="modal fade" id="editListInfoModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static" >
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t('schoolTeams','Edit');?></h4>
                </div>
                <div class="modal-body p24 overflow-y scroll-box" :style="'max-height:'+(height-180)+'px;overflow-x: hidden;'">
                    <div class='font14 color6 mb12'><?php echo Yii::t("teaching", "Chinese"); ?></div>
                    <div>
                        <div class='relative'>
                            <el-popover
                                placement="top"
                                width="270"
                                class='uploadImg'
                                trigger="hover">
                                    <div >
                                        <?php echo sprintf(Yii::t("newDS", "From Media Repository: "), $this->createUrl('/mteaching/weekly/index'));?>
                                        <br>
                                        <?php echo sprintf(Yii::t("newDS", "Click <a target='_blank'  href='%s'>here</a> to upload photos or videos, then insert files by clicking the gallery icon."), $this->createUrl('/mteaching/weekly/index'));?>
                                        <div class='mt16'>
                                            <?php echo Yii::t("newDS", "Direct Upload: ");?>
                                        </div>
                                        <?php echo Yii::t("newDS", "Just drag images to editor area (Images Only).");?>
                                    </div>
                                    <span slot="reference" class='yellow font14'><span class='el-icon-info mr8'></span> <?php echo Yii::t("newDS", "How to insert a photo or video?");?> </span>
                            </el-popover>
                            <textarea id="tinymceContentCn" class='tinymceText' v-model='editListInfo.content_cn'></textarea>
                        </div>
                    </div>
                    <div class='font14 color6 mb12 mt24'><?php echo Yii::t("teaching", "English"); ?></div>
                    <div>
                        <div class='relative'>
                            <el-popover
                                placement="top"
                                width="270"
                                class='uploadImg'
                                trigger="hover">
                                    <div >
                                        <?php echo sprintf(Yii::t("newDS", "From Media Repository: "), $this->createUrl('/mteaching/weekly/index'));?>
                                        <br>
                                        <?php echo sprintf(Yii::t("newDS", "Click <a target='_blank'  href='%s'>here</a> to upload photos or videos, then insert files by clicking the gallery icon."), $this->createUrl('/mteaching/weekly/index'));?>
                                        <div class='mt16'>
                                            <?php echo Yii::t("newDS", "Direct Upload: ");?>
                                        </div>
                                        <?php echo Yii::t("newDS", "Just drag images to editor area (Images Only).");?>
                                    </div>
                                    <span slot="reference" class='yellow font14'><span class='el-icon-info mr8'></span> <?php echo Yii::t("newDS", "How to insert a photo or video?");?> </span>
                            </el-popover>
                            <textarea id="tinymceContentEn" class='tinymceText' v-model='editListInfo.content_en'></textarea>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" :disabled='btnDisanled' @click='saveInfoContent()'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 编辑首页内容 -->
    <div class="modal fade" id="editInfoModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static" >
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("schoolTeams", "Edit Homepage Content"); ?></h4>
                </div>
                <div class="modal-body p24 overflow-y scroll-box" :style="'max-height:'+(height-180)+'px;overflow-x: hidden;'">
                    <div class='font14 color6 mb16'><?php echo Yii::t("teaching", "Chinese"); ?></div>
                    <div class='relative'>
                        <el-popover
                            placement="top"
                            width="270"
                            class='uploadImg'
                            trigger="hover">
                                <div >
                                    <?php echo sprintf(Yii::t("newDS", "From Media Repository: "), $this->createUrl('/mteaching/weekly/index'));?>
                                    <br>
                                    <?php echo sprintf(Yii::t("newDS", "Click <a target='_blank'  href='%s'>here</a> to upload photos or videos, then insert files by clicking the gallery icon."), $this->createUrl('/mteaching/weekly/index'));?>
                                    <div class='mt16'>
                                        <?php echo Yii::t("newDS", "Direct Upload: ");?>
                                    </div>
                                    <?php echo Yii::t("newDS", "Just drag images to editor area (Images Only).");?>
                                </div>
                                <span slot="reference" class='yellow font14'><span class='el-icon-info mr8'></span> <?php echo Yii::t("newDS", "How to insert a photo or video?");?> </span>
                        </el-popover>
                        <textarea id="tinymceCn" class='tinymceText' v-model='content_cn'></textarea>
                    </div>
                    <div class='font14 color6 mb16 mt24'><?php echo Yii::t("teaching", "English"); ?></div>
                    <div class='relative'>
                        <el-popover
                            placement="top"
                            width="270"
                            class='uploadImg'
                            trigger="hover">
                                <div >
                                    <?php echo sprintf(Yii::t("newDS", "From Media Repository: "), $this->createUrl('/mteaching/weekly/index'));?>
                                    <br>
                                    <?php echo sprintf(Yii::t("newDS", "Click <a target='_blank'  href='%s'>here</a> to upload photos or videos, then insert files by clicking the gallery icon."), $this->createUrl('/mteaching/weekly/index'));?>
                                    <div class='mt16'>
                                        <?php echo Yii::t("newDS", "Direct Upload: ");?>
                                    </div>
                                    <?php echo Yii::t("newDS", "Just drag images to editor area (Images Only).");?>
                                </div>
                                <span slot="reference" class='yellow font14'><span class='el-icon-info mr8'></span> <?php echo Yii::t("newDS", "How to insert a photo or video?");?> </span>
                        </el-popover>
                        <textarea id="tinymceEn" class='tinymceText' v-model='content_en'></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" :disabled='btnDisanled' @click='savelContent()'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 删除 -->
    <div class="modal fade" id="delModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t('newDS', 'Warning') ?></h4>
                </div>
                <div class="modal-body p24" >
                    <?php echo Yii::t('directMessage', 'Proceed to remove?') ?>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" v-if='delType=="list"' :disabled='btnDisanled' @click='delList()'><?php echo Yii::t("global", "OK"); ?></button>
                    <button type="button" class="btn btn-primary" v-if='delType=="file"' :disabled='btnDisanled' @click='delFile()'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    var height=document.documentElement.clientHeight;
    $(document).ready(function () {
        // 通过该方法来为每次弹出的模态框设置最新的zIndex值，从而使最新的modal显示在最前面
        $(document).on('show.bs.modal', '.modal', function (event) {
            var zIndex = 1040 + (10 * $('.modal:visible').length);
            $(this).css('z-index', zIndex);
            setTimeout(function() {
                $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');
            }, 0);
        });
    });
    var height=document.documentElement.clientHeight;

    var container=new Vue({
        el: "#box",
        data: {
            height:height,
            uploadToken:{},
            WatermarkImg:'<?php echo (isset($this->schoolType) && $this->schoolType == 'ds') ? '!v1000' : '!i1000'; ?>',
            imageUrl: '',
            domain:'',
            token:'',
            btnDisanled:false,
            delId:'',
            delType:'',
            tipVisibles:[],
            dataLoading:false,
            activeName:'home',
            content_en:'',
            content_cn:'',
            content:'',
            infoList:[],
            listInfoData:{},
            editListInfo:{},
            school_list:[],
            preview_list:[],
            height:height,
            visible:false
        },
        created: function() {
            this.getInit()
            this.getContent()
        },
        beforeUnmount() {
        },
        watch:{
        },
        methods: {
            translate(text, ...args) {
                return text.replace(/%s/g, () => args.shift());
            },
            popoverHide(index){
               Vue.set(this.tipVisibles, index, false);
            },
            previewHomeUrl(){
                let that=this
                $('.wechatQrcode').html('')
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "get",
                    dataType: 'json',
                    data:{
                        url:'previewHomeUrl',
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.visible=true
                            that.$nextTick(() => {
                                $('.wechatQrcode').qrcode({
                                    width:120,
                                    height:120,
                                    text:data.data,
                                });
                            })
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            getInit(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        url:'importantDataList',
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            data.data.list.forEach(info => {
                            let list=['ext1', 'ext2'].filter(extName => info[extName] === '1');
                                if(list.length>0){
                                    let school=list.map(key => {
                                        const item = data.data.school_list.find(item => item.key === key);
                                        return item ? item.value : null;
                                    });
                                    info.extsList=school
                                }
                            });
                            that.infoList=data.data.list
                            that.school_list=data.data.school_list
                            that.preview_list=data.data.preview_list
                            that.$nextTick(function () {
                                const el = document.querySelector('.table_count')
                                new Sortable(el, {
                                    animation: 150,
                                    handle: '.handle',
                                    ghostClass: 'blue-background-class',
                                    onEnd: function ({ newIndex, oldIndex }) { //拖拽完毕之后发生该事件
                                        var list=JSON.parse( JSON.stringify (that.infoList))
                                        list.splice(newIndex, 0, list.splice(oldIndex, 1)[0])
                                        var newArray = list.slice(0)
                                        that.sortTable(newArray)
                                        that.infoList = []
                                        that.$nextTick(function () {
                                            that.infoList = newArray
                                        })
                                    }
                                });
                            })
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            sortTable(list){
                var ids=[]
                list.forEach(item => {
                    ids.push(item.id)
                });
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        url:'sortImportantData',
                        ids:ids
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg: data.message
                            });
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            getContent(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        url:'homeData',
                        startYear:this.startYear,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            const tempDiv = document.createElement('div');
                            tempDiv.innerHTML = data.data.content;
                            const h1Tags = tempDiv.querySelectorAll('h1');
                            h1Tags.forEach(h1 => {
                                const textContent = h1.textContent.trim();
                                const newHtml = `
                                    <div class="title_1 mt15">
                                        <div class="title_text">${textContent}</div>
                                        <span class="bg">
                                            <span class="yellow"></span>
                                            <span class="blue"></span>
                                        </span>
                                    </div>
                                `;
                                h1.outerHTML = newHtml;
                            });
                            that.content = tempDiv.innerHTML;
                            that.content_en=data.data.content_en
                            that.content_cn=data.data.content_cn
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            editContent(){
                let that=this
                tinymce.remove('#tinymceCn');
                tinymce.remove('#tinymceEn');
                $("#editInfoModal").modal('show')
                that.$nextTick(() => {
                    newTinymce()
                    that.getQiniu()
                })
            },
            getQiniu(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        url: 'token',
                        startYear:this.startYear
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.token = data.data.token;
                            that.domain = data.data.domain;
                            that.uploadToken = {'token':data.data.token};
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            savelContent(){
                let content_cn= tinymce.get('tinymceCn').getContent()
                let content_en= tinymce.get('tinymceEn').getContent()
                if(content_cn==''){
                    resultTip({
                        error: 'warning',
                        msg:'<?php echo Yii::t("leave", "Input"); ?> <?php echo Yii::t("teaching", "Chinese"); ?>'
                    });
                    return
                }
                if(content_en==''){
                    resultTip({
                        error: 'warning',
                        msg:'<?php echo Yii::t("leave", "Input"); ?> <?php echo Yii::t("teaching", "English"); ?>'
                    });
                    return
                }
                let that=this
                this.btnDisanled=true
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        url:'updateHomeData',
                        data:{
                            content_cn:content_cn,
                            content_en:content_en
                        }
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            $("#editInfoModal").modal('hide')
                            that.getContent()
                            resultTip({
                                msg: data.message
                            });
                            that.btnDisanled=false
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.btnDisanled=false
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.btnDisanled=false
                    },
                })
            },
            addInfo(){
                this.listInfoData={
                    title_cn:'',
                    title_en:'',
                    ext:[],
                    url:'addImportantData'
                }
                $("#editListModal").modal('show')
            },
            editInfoList(info){
                const ext = ['ext1', 'ext2'].filter(extName => info[extName] === '1');
                this.listInfoData={
                    title_cn:info.title_cn,
                    title_en:info.title_en,
                    id:info.id,
                    ext:ext,
                    url:'updateImportantData'
                }
                $("#editListModal").modal('show')
            },
            savelList(){
                let that=this
                if(this.listInfoData.title_cn==''){
                    resultTip({
                        error: 'warning',
                        msg:'<?php echo Yii::t("leave", "Input"); ?> <?php echo Yii::t("event", "Title Cn"); ?>'
                    });
                    return
                }
                var exts={ 
                    title_cn:this.listInfoData.title_cn,
                    title_en:this.listInfoData.title_en,
                    id:this.listInfoData.id,
                  }
                this.listInfoData.ext.forEach(item => {
                    exts[item]='1'
                });
                this.btnDisanled=true
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        url:this.listInfoData.url,
                        data:exts
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.getInit()
                            $("#editListModal").modal('hide')
                            resultTip({
                                msg: data.message
                            });
                            that.btnDisanled=false
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.btnDisanled=false
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.btnDisanled=false
                    },
                })
            },
            uploadFile(id){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        url:'fileUploadToken',
                        linkId:id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.uploadToken = {'token':data.data};
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            handleAvatarSuccess(res, file) {
                resultTip({
                    msg: '上传成功'
                });
                this.getInit()
            },
            beforeAvatarUpload(file) {
                let fileType=file.type.split('/')
                const isJPG = true;
                const isLt2M = file.size / 1024 / 1024 < 2;
                if(fileType[0]=="image"){
                    if (!isLt2M) {
                        resultTip({
                            error: 'warning',
                            msg:'上传图片大小不能超过 2MB!'
                        });
                    }
                    return isJPG && isLt2M;
                }
            },
            delFile(id){
                if(id){
                    this.delId=id
                    this.delType='file'
                    $("#delModal").modal('show')
                    return
                }
                let that=this
                that.btnDisanled=true
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        url:'deleteFile',
                        id:this.delId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.getInit()
                            resultTip({
                                msg: data.message
                            });
                            $("#delModal").modal('hide')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.btnDisanled=false
                    },
                    error: function(data) {
                        that.btnDisanled=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            delList(id){
                if(id){
                    this.delId=id
                    this.delType='list'
                    $("#delModal").modal('show')
                    return
                }
                let that=this
                that.btnDisanled=true
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        url:'deleteImportantData',
                        id:this.delId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.getInit()
                            resultTip({
                                msg: data.message
                            });
                            $("#delModal").modal('hide')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.btnDisanled=false
                    },
                    error: function(data) {
                        that.btnDisanled=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            editInfoContent(list){
                this.editListInfo={
                    content_cn:list.content_cn,
                    content_en:list.content_en,
                    id:list.id
                }
                tinymce.remove('#tinymceContentCn');
                tinymce.remove('#tinymceContentEn');
                this.$nextTick(() => {
                    newTinymce()
                    this.getQiniu()
                })
                $("#editListInfoModal").modal('show')
            },
            saveInfoContent(){
                let that=this
                let content_cn= tinymce.get('tinymceContentCn').getContent()
                let content_en= tinymce.get('tinymceContentEn').getContent()
                if(content_cn==''){
                    resultTip({
                        error: 'warning',
                        msg:'请填写中文'
                    });
                    return
                }
                that.btnDisanled=true
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        url:'updateImportantContent',
                        data:{
                            id:this.editListInfo.id,
                            content_cn:content_cn,
                            content_en:content_en
                        }
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.getInit()
                            resultTip({
                                msg: data.message
                            });
                            $("#editListInfoModal").modal('hide')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.btnDisanled=false
                    },
                    error: function(data) {
                        that.btnDisanled=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            handleClick(){
                if(this.activeName=='list'){
                    window.location.href="<?php echo $this->createUrl('index', array('branchId' => $this->branchId)); ?>"
                }
            }
        }
    })
    function newTinymce(){
        const example_image_upload_handler = (blobInfo, progress) => new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();
            xhr.withCredentials = false;
            xhr.open("post", "https://up-z0.qiniup.com");
            xhr.upload.onprogress = (e) => {
                progress(e.loaded / e.total * 100);
            };

            xhr.onload = () => {
                if (xhr.status === 403) {
                reject({ message: 'HTTP Error: ' + xhr.status, remove: true });
                return;
                }

                if (xhr.status < 200 || xhr.status >= 300) {
                reject('HTTP Error: ' + xhr.status);
                return;
                }

                const json = JSON.parse(xhr.responseText);
            
                resolve( container.domain + "/" + json.name);
            };

            xhr.onerror = () => {
                reject('Image upload failed due to a XHR Transport error. Code: ' + xhr.status);
            };

            var formData = new FormData();
            var file = blobInfo.blob();
            formData.append('file', file, file.name);
            formData.append('token', container.token);
            xhr.send(formData);
        });
        tinymce.PluginManager.add('my-example-plugin', function (editor) {
            editor.ui.registry.addMenuItem('image', {
                icon: 'image',
                text: '<?php echo Yii::t("newDS", "Add/Remove Watermark");?>',
                onAction: function () {
                    editor.insertContent('<div><img  style="max-width:100%"  src='+container.Watermark+'></div>')
                }
            });
            editor.ui.registry.addContextMenu('image', {
                update: function (element) {
                    container.Watermark=element.src
                    return !container.Watermark ? '' : 'image';
                }
            });
        });
        tinymce.init({
            content_style: "img{max-width:100%}",
            selector: 'textarea.tinymceText',
            width: "100%",
            height:800,
            resize: false,
            language:'<?php echo Yii::app()->language;?>'=='zh_cn'?'zh_CN':'en',
            plugins: [
                'fullscreen', 'image', 'link', 'media', 'preview', 'table', 'my-example-plugin', 'lists', 'advlist','my-example-plugin',
            ],
            contextmenu: ['image'],
            noneditable_class: 'non-editable',
            toolbar:'mediaDialog bullist numlist',
            images_upload_handler:example_image_upload_handler,
            setup: function(editor) {
                // 实时同步编辑器内容到 selector
                editor.on('change', function() {
                    tinymce.triggerSave();
                });
                editor.ui.registry.addIcon('mediaDialog', '<svg width="24" height="24"><path fill-rule="nonzero" d="M5 15.7l2.3-2.2c.3-.3.7-.3 1 0L11 16l5.1-5c.3-.4.8-.4 1 0l2 1.9V8H5v7.7zM5 18V19h3l1.8-1.9-2-2L5 17.9zm14-3l-2.5-2.4-6.4 6.5H19v-4zM4 6h16c.6 0 1 .4 1 1v13c0 .6-.4 1-1 1H4a1 1 0 0 1-1-1V7c0-.6.4-1 1-1zm6 7a2 2 0 1 1 0-4 2 2 0 0 1 0 4zM4.5 4h15a.5.5 0 1 1 0 1h-15a.5.5 0 0 1 0-1zm2-2h11a.5.5 0 1 1 0 1h-11a.5.5 0 0 1 0-1z"></path></svg>');
                editor.ui.registry.addButton('mediaDialog', {
                    icon:'mediaDialog',
                    tooltip: '<?php echo Yii::t("newDS", "Media Gallery");?>',
                    onAction: function () {
                        container.tinymceList=[]
                        var instanceApi = editor.windowManager.openUrl({
                            onMessage: function (dialogApi, details) {
                                switch (details.mceAction) {
                                    case 'loading':
                                        dialogApi.unblock()
                                        break;
                                    case 'addItem':
                                        container.tinymceList.push(details.content)
                                        break;
                                    case 'removeItem':
                                        for(var i=0;i<container.tinymceList.length;i++){
                                            if(container.tinymceList[i].id==details.content.id){
                                                container.tinymceList.splice(i,1)
                                            }
                                        }
                                    break;
                                }
                            },
                            onAction: function (dialogApi, details) {
                                dialogApi.close()
                                for(var i=0;i<container.tinymceList.length;i++){
                                    if(container.tinymceList[i].type=='photo'){
                                        editor.insertContent('<div><img style="max-width:100%" src='+container.tinymceList[i]._url+'></div>')
                                    }else{
                                        let url=container.tinymceList[i].url.split('!vh120')[0]
                                        editor.insertContent(
                                        '<p><video controls  style="max-width:100%;object-fit:cover"  width="600" height="auto" poster='+url+' >'+
                                            '<source src='+container.tinymceList[i]._url+'  type="video/mp4">'+
                                        '</video></p>')
                                    }
                                }
                            },
                            title: '<?php echo Yii::t("newDS", "Media Gallery"); ?>',
                            url: '<?php echo $this->createUrl('/mteaching/journals/media'); ?>',
                            height: 500,
                            width: 730,
                            buttons: [{
                                type:'custom',
                                text:'Insert',
                                name:'btn-insert',
                                primary: true,
                                align: 'end'
                            },
                                {
                                type:'cancel',
                                text:'Close',
                                name:'btn-close',
                                primary: false,
                                align: 'end'
                            }],

                        });
                        instanceApi.block('loading')
                    }
                })
            },
        })
    }
</script>
<?php
    $this->renderPartial('//layouts/common/branchSelectBottom');
?>
<style> 
    .scroll-box::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius   : 10px;
        background-color: skyblue;
        background-color: #D8D8D8;
        background-image: none
    }
    .scroll-box::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0);
        background   : #fff;
        border-radius: 10px;
    }
    .filterList{
        padding:10px;
        font-size: 14px;
        color: #333333;
        line-height:20px
    }
    .filterList:hover{
        background: #F7F7F8;
        border-radius: 4px;
        cursor: pointer;
    }
    .filterList.active{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        color: #4D88D2;
    }
    .detailList{
        padding:16px;
        border-radius: 4px;
        border: 1px solid #E5E6EB;
        margin-bottom:20px
    }
    .tagLabel{
        font-size: 12px;
        color: #333333;
        line-height: 12px;
        background: #F2F3F5;
        border-radius: 2px;
        padding:2px 4px;
        margin-right:6px
    }
    .redBg{
        color: #D9534F;
        background: #FCF1F1;
    }
    .blueColor{
        color: #4D88D2;
    }
    .yellowBg{
        color: #F0AD4E;
        background: #FDF8F1;
    }
    .yellow{
        color: #F0AD4E;
    }
    .tox .tox-tbtn{
        overflow: inherit !important;
    }
    .tox .borderInsert{
        max-width:100px !important;
    }
    .tox-editor-container .tox-promotion,.tox-statusbar .tox-statusbar__branding ,.tox .tox-toolbar-nav-js{
    display: none !important;
    }
    .uploadImg{
        position: absolute;
        right: 10px;
        z-index: 998;
        top: 15px;
    }
    .tox-tinymce{
        height:500px !important
    }
    .avatar-uploader .el-upload {
    border: 1px dashed #4D88D2;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .blueColor{
    color: #4D88D2;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #4D88D2;
    width:250px;
    height: 150px;
    line-height: 150px;
    text-align: center;
  }
  .avatar {
    width: 250px;
    height: 150px;
    display: block;
  }
  .el-upload__input{
    display:none !important
  }
  .img42{
    width: 42px;
    height: 42px;
    object-fit: cover;
    border-radius: 50%;
  }
  .listMedia{
    padding:8px;
    border: 1px solid #fff;
    margin:0
    }
    .listMedia:hover{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        border: 1px solid #428bca;
        cursor: pointer;
    }
    .lineHeight {
        line-height:42px;
    }
    .childList {
        max-height: 250px;
        min-height: 40px;
        padding: 14px;
        overflow-y: auto;
    }
    .border {
        border: 1px solid #E8EAED;
        border-radius: 4px;
    }
    .allCheck {
        position: absolute;
        right: 10px;
        top: 4px;
    }
    .borderLeft{
        border-left: 1px solid #EBEDF0;
    }
    #wechatQrcode{
        width: 120px;
        height: 120px;
        margin: 0 auto;
    }
    .p12{
        padding:12px
    }
    span.col-sm-2 {
        padding-top:7px
    }
    .el-tabs__item.is-active{
        color:#4D88D2
    }
    .el-tabs__active-bar{
        background-color:#4D88D2
    }
    .el-tabs__item:hover{
        color:#4D88D2
    }
    .el-tabs__header{
        margin:0
    }
    .upload-demo{
        display:inline-block
    }
    .tableElent > thead > tr > th {
        border-bottom: 1px solid #dddddd;
        background: rgb(247, 247, 248);
        color: #666666;
        padding: 12px 8px;
    }
    .tableElent td,.tableElent th{
        vertical-align: middle !important;
    }
    .iphone{
        width: 375px;
        
        margin: 0 auto;  
    }
    .mobile{
        background: #111265;
        border-radius: 8px;
        color: #fff;
        padding:0 0 16px 0;
        margin-top: -40px;
        height:667px;
    }
    .mobile img{
        width: 100%;
        height:auto
    }
    .flexList{
        padding: 5px 12px;
        background: #F7F7F8;
        font-size: 14px;
        align-items: center;
        margin-top:8px
    }
    .flexList .flex1{
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .popoverImg{
        z-index:999 !important
    }
    .bg {
        position: absolute;
        left: 50%;
        margin-left: -8px;
        height: 10px;
        bottom: -10px;
        z-index: 9;
    }
    .bg .yellow {
        width: 10px;
        height: 10px;
        background: #ffc53c;
        border-radius: 50%;
        display: inline-block;
        position: absolute;
        top: 0;
        z-index: 99;
    }

    .bg .blue {
        width: 10px;
        height: 10px;
        background: #476bf5;
        border-radius: 50%;
        display: inline-block;
        position: absolute;
        left: 6px;
    }
    .title_1 {
        text-align: center;
        font-weight: bold;
        font-size: 16px;
        line-height: 22px;
        position: relative;
        margin-bottom: 24px;
        font-size: 22px;   
        color:#111265         
    }
    .title_text {
        position: relative;
        z-index: 99;
        color:#fff
    }
    .important {
        background: #fff;
        padding: 24px 12px;
    }
    .contentHtml{
        padding:16px;
        font-size:14px
    }
    .listInfo {
        padding:12px 16px;
        background: #edf1ff;
        border-radius: 5px;
        margin: 0 6px 12px;
        display: flex;
        align-items: center;
    }
    .handle{
        margin-top:7px
    }
    .tox-tinymce img{
        max-width: 100%;
    }
    .reference{
        color:#428bca
    }
    .reference:hover{
        color: #044985
    }
    .p6{
        padding:6px 8px
    }
</style>