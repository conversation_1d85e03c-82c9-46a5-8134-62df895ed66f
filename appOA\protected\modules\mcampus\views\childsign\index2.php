<style>
    .attendColor{
        background: #5CB85C;
    }
    .lateColor{
        background: #D9534F;
    }
    .leaveColor{
        background: #F0AD4E;
    }
    .progressHeight{
        /* height:8px; */
        margin-bottom:0px
    }
    .p0{
        padding:0px !important
    }
    .notAttend{
       border-left:6px solid #ccc;
    }
    .lateBox{
        border-left:6px solid #D9534F;
        background:rgba(217, 83, 79, 0.08)
    }
    .attended{
        border-left:6px solid #5CB85C;
        background:rgba(92, 184, 92, 0.08)
    }
    .leaveIcon{
        width: 16px;
    }
    .lateBg{
        position: absolute;
        bottom: 0;
        width: 100%;
        text-align: center;
        color: #fff;
        font-size: 12px;
        border-radius: 0 0 6px 6px;
    }
    .img-rounded{
        width:80px;
        height:80px
    }
    .remark{
        position: absolute;
        right:-100px;
        bottom: 0;
        width: 100px;
        padding-left:5px
    }
    .absentColor{
        background:#999
    }
    .reset{
        color:#D9534F !important
    }
    .iconColor{
        color:#5CB85C
    }
    .loading{
        width:98%;
        height:95%;
        background:#fff;
        position: absolute;
        opacity: 0.5;
        z-index: 99
    }
    .loading span{
        width:100%;
        height:20%;
        display:block;
        background: url("<?php echo Yii::app()->theme->baseUrl?>/images/loading.gif")no-repeat center center ;
    }
    [v-cloak] {
        display: none;
    }
    .mb70{
        margin-bottom:200px
    }
    .student_num{
        padding:10px 16px;
        border-radius: 4px;
        width: 140px;
        background:#F2F3F5
    }
    .pending{
        background: #FCF1F1;
    }
    .completed{
        background: #F2F9F2;
    }
    .leave{
        background:#FDF8F1
    }
    .tardy{
        padding:1px 4px;
        color:#fff;
        border-radius: 2px;
        font-size:12px;
        margin-left:12px;
        background:#999999
    }
    .font500{
        font-weight:bold
    }
    .font600{
        font-weight:600
    }
    .ml1{
        margin-left:1px
    }
    
</style>
<div class="container-fluid"  id='container' v-cloak>
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('default/index')); ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Routines'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Student Attendance Management'), array('//mcampus/childsign/index2')) ?></li>
        <li class="active"><?php echo Yii::t('campus', 'Student Attendance'); ?></li>
    </ol>
    <div class="row">
        <?php
        $this->renderPartial('/childsign/left');
        ?>
        <div class="col-md-10">
            <div class="flex mb24 align-items" v-if='classData.student_num'>
                <v-date-picker name="calendar"  @dayclick="showDay"  class="  pull-left mr10 ml0"  :masks="masks"  :max-date='new Date()'  v-model='datepicker' ref='calendar'>
                    <template v-slot="{ inputValue, togglePopover,hidePopover  }">
                        <input
                            class="form-control select_3"
                            :value="inputValue"
                            style='width:251px'
                            @click="togglePopover"
                            placeholder="<?php echo Yii::t("directMessage", "By publish date"); ?>"
                        />
                    </template>
                </v-date-picker>
                <!-- <div class='flex1'>
                    <span class='mr16'><?php echo Yii::t('reg', 'Completed: '); ?>{{classData.attendance_num}}/{{classData.student_num}}</span>
                    <span class='mr16'><?php echo Yii::t('attends', 'Present'); ?>：<span class="badge attendColor">{{classData.sign_num}}</span></span>
                    <span class='mr16'><?php echo Yii::t('attends', 'Tardy'); ?>：<span class="badge lateColor">{{classData.tardy_num}}</span></span>
                    <span class='mr16'><?php echo Yii::t('attends', 'Leave'); ?>：<span class="badge leaveColor">{{classData.vacation_num}}</span></span>
                    <span class='mr16'><?php echo Yii::t('attends', 'Absent'); ?>：<span class="badge">{{classData.truancy_num}}</span></span>
                </div> -->
            </div>
            <div class='mb15 mt20 flex' v-if='classData.student_num'>
                <div class='mr16 student_num completed' v-if='classData.attendance_num==classData.student_num'>
                    <div class='font12 iconColor font500'><span class='glyphicon glyphicon-ok-sign mr4'></span><?php echo Yii::t('attends', 'Completed'); ?></div>
                    <div class='font14 ml16 iconColor mt4'><span class='font600'>{{classData.student_num}}</div>
                </div>
                <div class='mr16 student_num pending' v-else>
                    <div class='font12 reset font500'><span class='glyphicon glyphicon-question-sign mr4'></span><?php echo Yii::t('attends', 'Pending'); ?></div>
                    <div class='font14 ml16 mt4'><span class='font600 reset'>{{classData.student_num-classData.attendance_num}}</span> / <span class='color6'>{{classData.student_num}}</span> </div>
                </div>
                <div class='mr16 student_num '> 
                    <div class='font12 color3 font500'><?php echo Yii::t('attends', 'Present'); ?></div>    
                    <div class='mt4 ml1'>
                        <span class="color3 font14 font600">{{classData.sign_num}}</span>
                        <span class='tardy' v-if='classData.tardy_num!=0'><?php echo Yii::t('attends', 'Tardy'); ?>：{{classData.tardy_num}}</span>
                    </div>
                </div>
                <div class='mr16 student_num '>
                    <div class='font12 color3 font500'><?php echo Yii::t('attends', 'Leave'); ?></div>
                    <div class='font14 ml1 color3 mt4 font600'> {{classData.vacation_num}}</div>
                </div>
            </div>
            <div class='loading'  v-if='showLoading'>
                <span></span>
            </div>
            <div class='mb70'>
            <div class="panel panel-default " v-for='(list,index) in classData.class_list'>
                <div class="panel-heading" @click='classShow(index)' >
                    <h3 class="panel-title flex align-items" style='height:26px'>
                        <span class='flex1'>{{list.title}}</span>
                        <div v-if='classData.sign_childids[list.classid] && Object.keys(classData.sign_childids[list.classid]).length==Object.keys(classData.class_child[list.classid]).length' class='color6 font12'>
                           <span class='glyphicon glyphicon-ok-sign iconColor'></span> <?php echo Yii::t('reg', 'Completed'); ?> （{{Object.keys(classData.sign_childids[list.classid]).length}}）
                        </div>
                        <div class='length_4' v-else>
                            <div class="progress progressHeight"  v-if='classData.sign_childids[list.classid]'>
                                <div class="progress-bar progress-bar-success" role="progressbar" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100" :style="numberPeople(list.classid)" style="min-width:30px;line-height:18px">
                                    {{Object.keys(classData.sign_childids[list.classid]).length}}/{{Object.keys(classData.class_child[list.classid]).length}}
                                </div>
                            </div>
                            <div class="progress progressHeight"  v-else style='background: #dfdede;'>
                                <div class="progress-bar" role="progressbar" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100" :style="numberPeople(list.classid)" style="line-height:18px;color: #333;background: #dfdede;margin-left: 10px;">
                                    0/{{Object.keys(classData.class_child[list.classid]).length}}
                                </div>
                            </div>
                        </div>
                    </h3>
                </div>
                <div class="panel-body p0" :class='show==index?"show":"hide"'>
                    <div class='notAttend '  v-if='classData.no_sign_childids[list.classid]'>
                        <div class='mb16 mt16 col-lg-12 col-md-12 col-sm-12 col-xs-12'><strong class='color3 font14'><?php echo Yii::t('reg', 'Absent'); ?><span class="badge ml8">{{classData.no_sign_childids[list.classid].length}}</span></strong></div>
                        <div class="btn-group col-lg-12 col-md-12 col-sm-12 col-xs-12 mb24" v-if='!classData.is_history'>
                            <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <?php echo Yii::t('campus', 'Mark All Present'); ?> <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a href="javascript:;" @click='batchCheck("1",list)'><?php echo Yii::t('campus', 'Present'); ?></a></li>
                                <li><a href="javascript:;" @click='batchCheck("11",list)'><?php echo Yii::t('attends', 'Online Present'); ?></a></li>
                            </ul>
                        </div>
                        <div>
                            <div class="col-lg-2 col-md-2 col-sm-3 col-xs-6 mb20" v-for='(item,idx) in classData.no_sign_childids[list.classid]'>
                                <a style="display: inline-block;position: relative" href="javascript:" >
                                    <img class="img-responsive img-rounded" :src="classData.class_child[list.classid][item].photo">
                                    <div class='remark'>
                                        <!--当日违规--->
                                        <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/stu_sign_in2.png' ?>" class='leaveIcon' alt=""
                                             v-if='dressCode_violation_num[item] && (child_sign_status[list.classid] && child_sign_status[list.classid][item] && child_sign_status[list.classid][item].other)'
                                             onMouseOver="$(this).tooltip('fixTitle').tooltip('show')"  data-toggle="tooltip"
                                             :title="`<?php echo Yii::t('reg', 'DressCode Violation (');?>${dressCode_violation_num[item]}<?php echo Yii::t('reg', 'times)');?>`" data-placement="top" data-html="true" data-container='body'>
                                        <!--今天没有违规 但之前有违规-->
                                        <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/stu_sign_in3.png' ?>" class="leaveIcon" alt=""
                                             v-if="!(child_sign_status[list.classid] && child_sign_status[list.classid][item] && child_sign_status[list.classid][item].other) && (dressCode_violation_num[item] && dressCode_violation_num[item] != undefined && dressCode_violation_num[item]>0)"
                                             onMouseOver="$(this).tooltip('fixTitle').tooltip('show')" data-toggle="tooltip"
                                             :title="`<?php echo Yii::t('reg', 'DressCode Violation (');?>${dressCode_violation_num[item]}<?php echo Yii::t('reg', 'times)');?>`" data-placement="top" data-html="true" data-container='body'>
                                    </div>
                                </a>

                                <div class="dropdown">
                                    <button class="btn btn-default dropdown-toggle thebtn" type="button" id="dropdownMenu-19368" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        {{classData.class_child[list.classid][item].name}} <span class="caret"></span>
                                    </button>
                                    <ul class="dropdown-menu"   v-if='!classData.is_history'>
                                        <li><a href="javascript:;" @click='studentSignIn("1",item)'><?php echo Yii::t('campus', 'Present'); ?></a></li>
                                        <li><a href="javascript:;" @click='studentSignIn("11",item)'><?php echo Yii::t('attends', 'Online Present'); ?></a></li>
                                        <li><a href="javascript:" @click='leaveEvent("10",item,list.classid)'><?php echo Yii::t('attends', 'Sick Leave'); ?></a></li>
                                        <li><a href="javascript:" @click='leaveEvent("20",item,list.classid)'><?php echo Yii::t('attends', 'Personal Leave'); ?></a></li>
                                        <li><a href="javascript:"  @click='lateEvent(item,list.classid)'><?php echo Yii::t('attends', 'Tardy'); ?></a></li>
                                        <!-- <li><a href="javascript:"  @click='leaveEvent("60",item,list.classid)'><?php echo Yii::t('attends', 'Absent'); ?></a></li> -->
                                        <li role="separator" class="divider"></li>
                                        <li>
                                            <a href="javascript:;"  @click='cancelViolation(item,list.classid)' v-if='child_sign_status[list.classid] && child_sign_status[list.classid][item] && child_sign_status[list.classid][item].other'><?php echo Yii::t('reg', 'Revoke DressCode Violation'); ?></a>
                                            <a href="javascript:;"  @click='violationEvent(item,list.classid)' v-else><?php echo Yii::t('reg', 'DressCode Violated'); ?></a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class='clearfix'></div>
                    </div>
                    <div class='lateBox' v-if='classData.late_list[list.classid]'>
                        <div class='mb16 mt16 col-lg-12 col-md-12 col-sm-12 col-xs-12 font14'>
                            <strong class='color3 '><?php echo Yii::t('attends', 'Tardy'); ?> <?php echo Yii::t('campus', '(Not arrived yet)'); ?><span class="badge ml8">{{classData.late_list[list.classid].length}}</span></strong>
                        <!-- <span class='color6 ml20'>点击头像取消考勤</span> -->
                        </div>
                        <div>
                            <div class="col-lg-2 col-md-2 col-sm-3 col-xs-6 mb20" v-for='(lateItem,_index) in classData.late_list[list.classid]'>
                                <div style="display: inline-block;position: relative" href="javascript:" >
                                    <img class="img-responsive img-rounded" :src="classData.class_child[list.classid][lateItem.childid].photo">
                                    <span class='lateColor lateBg' ><?php echo Yii::t('attends', 'Tardy'); ?></span>
                                    <div class='remark'>
                                        <div>
                                            <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/stu_sign_in2.png' ?>"
                                                 class='leaveIcon' alt=""
                                                 v-if='child_sign_status[list.classid] && child_sign_status[list.classid][lateItem.childid] && child_sign_status[list.classid][lateItem.childid].other'
                                                 onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" title="<?php echo Yii::t('reg', 'DressCode Violated'); ?>" data-placement="top">

                                            <!--当日违规--->
                                            <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/stu_sign_in2.png' ?>"
                                                 class='leaveIcon' alt=""
                                                 v-if='dressCode_violation_num[lateItem.childid] && (child_sign_status[list.classid] && child_sign_status[list.classid][lateItem.childid] && child_sign_status[list.classid][lateItem.childid].other)'
                                                 onMouseOver="$(this).tooltip('fixTitle').tooltip('show')"  data-toggle="tooltip"
                                                 :title="`<?php echo Yii::t('reg', 'DressCode Violation (');?>${dressCode_violation_num[lateItem.childid]}<?php echo Yii::t('reg', 'times)');?>`" data-placement="top" data-html="true" data-container='body'>
                                            <!--今天没有违规 但之前有违规-->
                                            <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/stu_sign_in3.png' ?>" class="leaveIcon" alt=""
                                                 v-if="!(child_sign_status[list.classid] && child_sign_status[list.classid][lateItem.childid] && child_sign_status[list.classid][lateItem.childid].other) && (dressCode_violation_num[lateItem.childid] && dressCode_violation_num[lateItem.childid] != undefined && dressCode_violation_num[lateItem.childid]>0)"
                                                 onMouseOver="$(this).tooltip('fixTitle').tooltip('show')" data-toggle="tooltip"
                                                 :title="`<?php echo Yii::t('reg', 'DressCode Violation (');?>${dressCode_violation_num[lateItem.childid]}<?php echo Yii::t('reg', 'times)');?>`" data-placement="top" data-html="true" data-container='body'>
                                        </div>
                                        <div>
                                            <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/stu_sign_in1.png' ?>"
                                                 class='leaveIcon' alt=""
                                                 v-if='child_reason[lateItem.childid]' @click='showRemark(lateItem.childid,list.classid)'
                                                 onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" title="<?php echo Yii::t('labels','Memo') ?>" data-placement="top">
                                        </div>
                                    </div>
                                </div>
                                <div class="dropdown">
                                    <button class="btn btn-default dropdown-toggle thebtn" type="button" id="dropdownMenu-19368" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    {{classData.class_child[list.classid][lateItem.childid].name}} <span class="caret"></span>
                                    </button>
                                    <ul class="dropdown-menu"   v-if='!classData.is_history'>
                                        <li><a href="javascript:;" @click='lateSignIn("1",lateItem.childid,list.classid)'><?php echo Yii::t('campus', 'Present'); ?></a></li>
                                        <li><a href="javascript:;" @click='lateSignIn("11",lateItem.childid,list.classid)'><?php echo Yii::t('attends', 'Online Present'); ?></a></li>
                                        <li><a href="javascript:" @click='leaveEvent("10",lateItem.childid,list.classid)'><?php echo Yii::t('attends', 'Sick Leave'); ?></a></li>
                                        <li><a href="javascript:" @click='leaveEvent("20",lateItem.childid,list.classid)'><?php echo Yii::t('attends', 'Personal Leave'); ?></a></li>
                                        <li><a href="javascript:"  @click='lateEvent(lateItem.childid,list.classid)'><?php echo Yii::t('attends', 'Tardy'); ?></a></li>
                                        <!-- <li><a href="javascript:"  @click='leaveEvent("60",lateItem.childid,list.classid)'><?php echo Yii::t('attends', 'Absent'); ?></a></li> -->
                                        <li role="separator" class="divider"></li>
                                        <li>
                                            <a href="javascript:;"  @click='cancelViolation(lateItem.childid,list.classid)' v-if='child_sign_status[list.classid] && child_sign_status[list.classid][lateItem.childid] && child_sign_status[list.classid][lateItem.childid].other'><?php echo Yii::t('reg', 'Revoke DressCode Violation'); ?></a>
                                            <a href="javascript:;"  @click='violationEvent(lateItem.childid,list.classid)'  v-else><?php echo Yii::t('reg', 'DressCode Violated'); ?></a>
                                        </li>
                                        <li><a href="javascript:;" class='reset' @click='resetAttend(lateItem.childid,list.classid)'><?php echo Yii::t('campus', 'Reset'); ?></a></li>
                                    </ul>
                                </div>
                                <div class='color6 mt4'>{{teacher_name_list[lateItem.teacher_id]}}</div>
                            </div>
                        </div>
                        <div class='clearfix'></div>
                    </div>
                    <div class='attended'   v-if='classData.sign_childids[list.classid]'>
                        <div class='mb16 mt16 col-lg-12 col-md-12 col-sm-12 col-xs-12 font14'>
                            <strong class='color3 '><?php echo Yii::t('reg', 'Present'); ?><span class="badge ml8">{{classData.sign_childids[list.classid].length}}</span></strong>
                        <!-- <span class='color6 ml20'>点击头像取消考勤</span> -->
                        </div>
                        <div>
                            <div class="col-lg-2 col-md-2 col-sm-3 col-xs-6 mb20" v-for='(signItem,idx) in classData.sign_childids[list.classid]'>
                                <a style="display: inline-block;position: relative" href="javascript:" >
                                    <img class="img-responsive img-rounded" :src="classData.class_child[list.classid][signItem.childid].photo">
                                    <span v-if='child_sign_status[list.classid][signItem.childid]'>
                                        <div class='attendColor lateBg' v-if='child_sign_status[list.classid][signItem.childid].main==1 || child_sign_status[list.classid][signItem.childid].main==11'>{{main_status_map[child_sign_status[list.classid][signItem.childid].main]}}</div>
                                        <div class='absentColor lateBg' v-if='child_sign_status[list.classid][signItem.childid].main==60'>{{main_status_map[child_sign_status[list.classid][signItem.childid].main]}}</div>
                                        <div class='lateColor lateBg' v-if='child_sign_status[list.classid][signItem.childid].main==40'>{{main_status_map[child_sign_status[list.classid][signItem.childid].main]}}</div>
                                        <div class='leaveColor lateBg' v-if='child_sign_status[list.classid][signItem.childid].main==10 || child_sign_status[list.classid][signItem.childid].main==20'>{{main_status_map[child_sign_status[list.classid][signItem.childid].main]}}</div>
                                    </span>
                                    <div class='remark'>
                                        <div>
                                            <!--当日违规-->
                                            <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/stu_sign_in2.png' ?>" class='leaveIcon' alt=""
                                                 v-if='dressCode_violation_num[signItem.childid] && (child_sign_status[list.classid] && child_sign_status[list.classid][signItem.childid] && child_sign_status[list.classid][signItem.childid].other)'
                                                 onMouseOver="$(this).tooltip('fixTitle').tooltip('show')"  data-toggle="tooltip"
                                                 :title="`<?php echo Yii::t('reg', 'DressCode Violation (');?>${dressCode_violation_num[signItem.childid]}<?php echo Yii::t('reg', 'times)');?>`" data-placement="top" data-html="true" data-container='body' data-sanitize="false">
                                            <!--今天没有违规 但之前有违规-->
                                            <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/stu_sign_in3.png' ?>" class="leaveIcon" alt=""
                                                 v-if="!(child_sign_status[list.classid] && child_sign_status[list.classid][signItem.childid] && child_sign_status[list.classid][signItem.childid].other) && (dressCode_violation_num[signItem.childid] && dressCode_violation_num[signItem.childid] != undefined && dressCode_violation_num[signItem.childid]>0)"
                                                 onMouseOver="$(this).tooltip('fixTitle').tooltip('show')" data-toggle="tooltip"
                                                 :title="`<?php echo Yii::t('reg', 'DressCode Violation (');?>${dressCode_violation_num[signItem.childid]}<?php echo Yii::t('reg', 'times)');?>`" data-placement="top" data-html="true" data-container='body' data-sanitize="false">
                                        </div>
                                        <div>
                                            <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/stu_sign_in1.png' ?>"
                                                 class='leaveIcon' alt=""
                                                 v-if='child_reason[signItem.childid]' @click='showRemark(signItem.childid,list.classid)'
                                                 onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" title="<?php echo Yii::t('labels','Memo') ?>" data-placement="top">
                                        </div>
                                    </div>
                                </a>
                                <div class="dropdown">
                                    <button class="btn btn-default dropdown-toggle thebtn" type="button" id="dropdownMenu-19368" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" >
                                    {{classData.class_child[list.classid][signItem.childid].name}} <span class="caret"></span>
                                    </button>
                                    <ul class="dropdown-menu"  v-if='!classData.is_history'>
                                        <li><a href="javascript:;" @click='studentSignIn("1",signItem.childid)'><?php echo Yii::t('campus', 'Present'); ?></a></li>
                                        <li><a href="javascript:;" @click='studentSignIn("11",signItem.childid)'><?php echo Yii::t('attends', 'Online Present'); ?></a></li>
                                        <li><a href="javascript:" @click='leaveEvent("10",signItem.childid,list.classid)'><?php echo Yii::t('attends', 'Sick Leave'); ?></a></li>
                                        <li><a href="javascript:" @click='leaveEvent("20",signItem.childid,list.classid)'><?php echo Yii::t('attends', 'Personal Leave'); ?></a></li>
                                        <li><a href="javascript:"  @click='lateEvent(signItem.childid,list.classid)'><?php echo Yii::t('attends', 'Tardy'); ?></a></li>
                                        <!-- <li><a href="javascript:"  @click='leaveEvent("60",signItem.childid,list.classid)'><?php echo Yii::t('attends', 'Absent'); ?></a></li> -->
                                        <li role="separator" class="divider"></li>
                                        <li>
                                            <a href="javascript:;"  @click='cancelViolation(signItem.childid,list.classid)' v-if='child_sign_status[list.classid] && child_sign_status[list.classid][signItem.childid] && child_sign_status[list.classid][signItem.childid].other'><?php echo Yii::t('reg', 'Revoke DressCode Violation'); ?></a>
                                            <a href="javascript:;" @click='violationEvent(signItem.childid,list.classid)' v-else><?php echo Yii::t('reg', 'DressCode Violated'); ?></a>
                                        </li>
                                        <li><a href="javascript:;"  class='reset'  @click='resetAttend(signItem.childid,list.classid)'><?php echo Yii::t('campus', 'Reset'); ?></a></li>

                                    </ul>
                                </div>
                                <div class='color6 mt4'>{{teacher_name_list[signItem.teacher_id]}}</div>
                            </div>
                        </div>
                        <div class='clearfix'></div>
                    </div>
                </div>
            </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="leaveModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel"  data-backdrop="static">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel">
                        <span v-if="leaveType=='10'"><?php echo Yii::t('attends', 'Sick Leave'); ?></span>
                        <span v-if="leaveType=='20'"><?php echo Yii::t('attends', 'Personal Leave'); ?></span>
                        <span v-if="leaveType=='60'"><?php echo Yii::t('attends', 'Absent'); ?></span>
                    </h4>
                </div>
                <div class="modal-body">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <label for="inputEmail3" class="col-sm-2 control-label"><?php echo Yii::t('labels','Student') ?></label>
                            <div class="col-sm-10" v-if='leaveClassid!=""'>
                            <input type="text" class="form-control" :value='classData.class_child[leaveClassid][leaveChildid].name' disabled='disabled'>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="inputPassword3" class="col-sm-2 control-label"><?php echo Yii::t('labels','Date') ?></label>
                            <div class="col-sm-10">
                            <input type="text" class="form-control" disabled='disabled' :value='datepicker'>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="inputPassword3" class="col-sm-2 control-label"><?php echo Yii::t('labels','Type') ?></label>
                            <div class="col-sm-10 pt8">
                                <span v-if="leaveType=='10'"><?php echo Yii::t('attends', 'Sick Leave'); ?></span>
                                <span v-if="leaveType=='20'"><?php echo Yii::t('attends', 'Personal Leave'); ?></span>
                                <span v-if="leaveType=='60'"><?php echo Yii::t('attends', 'Absent'); ?></span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="inputPassword3" class="col-sm-2 control-label"><?php echo Yii::t('labels','Memo') ?></label>
                            <div class="col-sm-10">
                                <textarea class="form-control" rows="3" v-model='leaveRemark'></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close"); ?></button>
                    <button type="button"  class="btn btn-primary" @click='saveLeave()' :disabled='btnDisanled'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="lateModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel"  data-backdrop="static">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t('attends', 'Tardy'); ?></h4>
                </div>
                <div class="modal-body">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <label for="inputEmail3" class="col-sm-2 control-label"><?php echo Yii::t('labels','Student') ?></label>
                            <div class="col-sm-10" v-if='lateClassid!=""'>
                            <input type="text" class="form-control" :value='classData.class_child[lateClassid][lateChildid].name' disabled='disabled'>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="inputPassword3" class="col-sm-2 control-label"><?php echo Yii::t('labels','Date') ?></label>
                            <div class="col-sm-10">
                                <input type="text" class="form-control" disabled='disabled' :value='datepicker'>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="inputPassword3" class="col-sm-2 control-label"><?php echo Yii::t('labels','Type') ?></label>
                            <div class="col-sm-10 pt8">
                               <div><?php echo Yii::t('attends', 'Tardy'); ?></div>
                                <div>
                                    <label class="radio-inline" >
                                        <input type="radio"  value="1" v-model='lateType'> <?php echo Yii::t('campus','Not arrived yet') ?>
                                    </label>
                                    <label class="radio-inline">
                                        <input type="radio"  value="2" v-model='lateType'> <?php echo Yii::t('campus','Already Arrived') ?>
                                    </label>
                                </div>
                                <div>
                                    <v-date-picker v-model="lateTime" mode="time" is24hr  v-if='lateType==1'>
                                        <template v-slot="{ inputValue, inputEvents }">
                                            <input class="form-control length_3 mt8 pull-left" :value="inputValue" v-on="inputEvents" placeholder="<?php echo Yii::t('reg','ETA') ?>"/>
                                        </template>
                                    </v-date-picker>
                                    <v-date-picker v-model="lateTime" mode="time" is24hr  v-if='lateType==2'>
                                        <template v-slot="{ inputValue, inputEvents }">
                                            <input class="form-control length_3 mt8 pull-left" :value="inputValue" v-on="inputEvents" placeholder="<?php echo Yii::t('reg','ATA') ?>"/>
                                        </template>
                                    </v-date-picker>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="inputPassword3" class="col-sm-2 control-label"><?php echo Yii::t('labels','Memo') ?></label>
                            <div class="col-sm-10">
                                <textarea class="form-control" rows="3" v-model='lateRemark'></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close"); ?></button>
                    <button type="button"  class="btn btn-primary" @click='saveLate()' :disabled='btnDisanled'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
    <div id="tardyDefine" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel"  data-backdrop="static">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
                    <h4 class="modal-title"><?php echo Yii::t('campus','Add leave record') ?></h4>
                </div>
                <div class="modal-body" v-if='lateSignInId!=""'>
                    <div class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-2 control-label"><?php echo Yii::t('labels','Student') ?></label>
                            <div class="col-sm-10">
                            <input type="text" class="form-control" :value='classData.class_child[lateSignInClassid][lateSignInId].name' disabled='disabled'>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label"><?php echo Yii::t('campus','EsteEstimated Time of Arrival') ?></label>
                            <div class="col-sm-10">
                                <input class="form-control" disabled="disabled" type="text" :value="child_est_begin_time[lateSignInId]" >
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label"><?php echo Yii::t('labels','Type') ?></label>
                            <div class="col-sm-10">
                                <input class="form-control" id="type" disabled="disabled" type="text" value="<?php echo Yii::t('attends', 'Tardy'); ?>" name="type">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label"><?php echo Yii::t('campus','Actual Time of Arrival') ?> <span class="required">*</span></label>
                            <div class="col-sm-10">
                            <v-date-picker v-model="lateSignInTime" mode="time" is24hr >
                                <template v-slot="{ inputValue, inputEvents }">
                                    <input class="form-control length_3 mt8 pull-left" :value="inputValue" v-on="inputEvents" placeholder="<?php echo Yii::t('reg','ATA') ?>"/>
                                </template>
                            </v-date-picker>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close"); ?></button>
                    <button type="button"  class="btn btn-primary" @click='saveSignIn()' :disabled='btnDisanled'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div>
    <div class="modal fade" id="remarkModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t('labels','Memo') ?></h4>
                </div>
                <div class="modal-body" v-if='remarkChildid!=""'>
                    <div class="form-horizontal">
                        <div class="form-group">
                            <label for="inputEmail3" class="col-xs-3 control-label"><?php echo Yii::t('labels','Student') ?></label>
                            <div class="col-xs-9">
                            <input type="text" class="form-control" :value='classData.class_child[remarkClassid][remarkChildid].name' disabled='disabled'>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="inputPassword3" class="col-xs-3 control-label"><?php echo Yii::t('labels','Date') ?></label>
                            <div class="col-xs-9">
                            <input type="text" class="form-control" disabled='disabled' :value='datepicker'>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="inputPassword3" class="col-xs-3 control-label"><?php echo Yii::t('labels','Type') ?></label>
                            <div class="col-xs-9 pt8"  v-if='child_sign_status[remarkClassid] && child_sign_status[remarkClassid][remarkChildid]'>
                                {{main_status_map[child_sign_status[remarkClassid][remarkChildid].main]}}
                            </div>
                        </div>
                        <div class="form-group" v-if='child_begin_time[remarkChildid]'>
                            <label for="inputPassword3" class="col-xs-3 control-label"><?php echo Yii::t('campus','ATA') ?></label>
                            <div class="col-xs-9 pt8">
                                {{child_begin_time[remarkChildid]}}
                            </div>
                        </div>
                        <div class="form-group" v-if='child_est_begin_time[remarkChildid]'>
                            <label for="inputPassword3" class="col-xs-3 control-label"><?php echo Yii::t('campus','ETA') ?></label>
                            <div class="col-xs-9 pt8">
                            {{child_est_begin_time[remarkChildid]}}
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="inputPassword3" class="col-xs-3 control-label"><?php echo Yii::t('labels','Memo') ?></label>
                            <div class="col-xs-9 pt8">
                                {{child_reason[remarkChildid]}}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close"); ?></button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- 显示底部学校选择栏 -->
<?php $this->renderPartial('//layouts/common/branchSelectBottom');?>

    <script>
        $(function () {
            $('[data-toggle="tooltip"]').tooltip()
        })
    var container = new Vue({
        el: "#container",
        data: {
            show:-1,
            leaveType:'',
            leaveChildid:'',
            leaveClassid:'',
            lateChildid:'',
            lateClassid:'',
            leaveRemark:'',
            lateRemark:'',
            lateType:'',
            lateTime:'',
            classData:{},
            main_status_map:{},
            child_reason:{},
            datepicker:'',
            child_sign_status:{},
            child_est_begin_time:{},
            child_begin_time:{},
            remarkChildid:'',
            remarkClassid:'',
            masks: {
                input: 'YYYY-MM-DD',
            },
            lateSignInType:'',
            lateSignInId:'',
            lateSignInClassid:'',
            lateSignInTime:'',
            teacher_name_list:'',
            dressCode_violation_num:'',//着装违规的次数
            showLoading:false,
            btnDisanled:false,
        },
        created() {
            var myDate = new Date;
            var year = myDate.getFullYear(); //获取当前年
            var mon = myDate.getMonth() + 1; //获取当前月
            var date = myDate.getDate(); //获取当前日
            this.datepicker=year + "-" + mon + "-" + date;
            this.initData()
        },
        methods: {
            classShow(index){
                if(this.show==index){
                    this.show=-1
                }else{
                    this.show=index
                }
            },
            numberPeople(classid){
                let num=this.classData.sign_childids[classid]?Object.keys(this.classData.sign_childids[classid]).length:0
                let total=Object.keys(this.classData.class_child[classid]).length
                var progress=num <= 0? "0%" : Math.round((num / total) * 10000) / 100.0 + "%";
                return 'width:'+progress
            },
            showDay(day){
                this.datepicker=day.id;
                this.initData()
            },
            initData(){
                let that=this
                that.showLoading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("allClasses") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        date:this.datepicker
                    },
                    success: function(data) {
                        if(data.state=='success'){
                            that.classData=data.data
                            that.child_sign_status=data.data.child_sign_status
                            that.main_status_map=data.data.main_status_map
                            that.child_reason=data.data.child_reason
                            that.child_est_begin_time=data.data.child_est_begin_time
                            that.child_begin_time=data.data.child_begin_time
                            that.teacher_name_list=data.data.teacher_name_list
                            that.dressCode_violation_num=data.data.dressCode_violation_num
                            if(data.data.class_list.length==1){
                                that.show=0
                            }
                        }else{
                            resultTip({error: 'warning', msg:data.message});
                        }
                        that.showLoading=false
                    },
                    error:function(data){
                        that.showLoading=false
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            batchCheck(type,list){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("classSignIn") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        type:type,
                        date:this.datepicker,
                        childData:this.classData.no_sign_childids[list.classid]
                    },
                    success: function(data) {
                        if(data.state=='success'){
                            that.initData()
                            resultTip({ msg:data.message});
                        }else{
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            studentSignIn(type,childid){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("studentSignIn") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        type:type,
                        date:this.datepicker,
                        child_id:childid
                    },
                    success: function(data) {
                        if(data.state=='success'){
                            that.initData()
                            resultTip({ msg:data.message});
                        }else{
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            violationEvent(childid,classid){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("setViolation") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        date:this.datepicker,
                        child_id:childid,
                        class_id:classid,
                    },
                    success: function(data) {
                        if(data.state=='success'){
                            that.initData()
                            resultTip({ msg:data.message});
                        }else{
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            cancelViolation(childid,classid){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("cancelViolation") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        date:this.datepicker,
                        child_id:childid,
                        class_id:classid,
                    },
                    success: function(data) {
                        if(data.state=='success'){
                            that.initData()
                            resultTip({ msg:data.message});
                        }else{
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            resetAttend(childid,classid){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("delSign2") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        date:this.datepicker,
                        child_id:childid,
                        class_id:classid,
                    },
                    success: function(data) {
                        if(data.state=='success'){
                            that.initData()
                            resultTip({ msg:data.message});
                        }else{
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            leaveEvent(type,childid,classid){
                this.leaveType=type
                this.leaveChildid=childid
                this.leaveClassid=classid
                this.leaveRemark=''
                $('#leaveModal').modal('show')
            },
            saveLeave(){
                let that=this
                that.btnDisanled=true
                if(this.leaveRemark==''){
                    resultTip({error: 'warning', msg:'请填写备注'});
                    return
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("SetVacation") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        date:this.datepicker,
                        child_id:this.leaveChildid,
                        type:this.leaveType,
                        memo:this.leaveRemark,
                    },
                    success: function(data) {
                        if(data.state=='success'){
                            that.initData()
                            resultTip({ msg:data.message});
                            $('#leaveModal').modal('hide')
                        }else{
                            resultTip({error: 'warning', msg:data.message});
                        }
                        that.btnDisanled=false
                    },
                    error:function(data){
                        that.btnDisanled=false
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            lateEvent(childid,classid){
                this.lateChildid=childid
                this.lateClassid=classid
                this.lateRemark=''
                this.lateType=''
                this.lateTime=''
                $('#lateModal').modal('show')
            },
            saveLate(){
                if(this.lateType==''){
                    resultTip({error: 'warning', msg:'请选择迟到类型'});
                    return
                }
                if(this.lateTime==''){
                    resultTip({error: 'warning', msg:'请选择时间'});
                    return
                }
                if(this.lateRemark==''){
                    resultTip({error: 'warning', msg:'请填写备注'});
                    return
                }
                let date=new Date(this.lateTime);
                var hh =date.getHours() < 10 ? '0' + date.getHours() : date.getHours();
                var mm =date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
                var time=hh+':'+mm
                let that=this
                that.btnDisanled=true
                $.ajax({
                    url: '<?php echo $this->createUrl("setLate") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        date:this.datepicker,
                        child_id:this.lateChildid,
                        type:this.lateType,
                        memo:this.lateRemark,
                        time:time,
                    },
                    success: function(data) {
                        if(data.state=='success'){
                            that.initData()
                            resultTip({ msg:data.message});
                            $('#lateModal').modal('hide')
                        }else{
                            resultTip({error: 'warning', msg:data.message});
                        }
                        that.btnDisanled=false
                    },
                    error:function(data){
                        that.btnDisanled=false
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            showRemark(childid,classid){
                this.remarkChildid=childid
                this.remarkClassid=classid
                $('#remarkModal').modal('show')
            },
            lateSignIn(type,childid,classid){
                this.lateSignInType=type
                this.lateSignInId=childid
                this.lateSignInClassid=classid
                $('#tardyDefine').modal('show')
            },
            saveSignIn(){
                if(this.lateSignInTime==''){
                    resultTip({error: 'warning', msg:'请选择时间'});
                    return
                }
                let date=new Date(this.lateSignInTime);
                var hh =date.getHours() < 10 ? '0' + date.getHours() : date.getHours();
                var mm =date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
                var time=hh+':'+mm
                let that=this
                that.btnDisanled=true
                $.ajax({
                    url: '<?php echo $this->createUrl("studentSignIn") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        type:this.lateSignInType,
                        date:this.datepicker,
                        child_id:this.lateSignInId,
                        time:time
                    },
                    success: function(data) {
                        if(data.state=='success'){
                            that.initData()
                            resultTip({ msg:data.message});
                            $('#tardyDefine').modal('hide')

                        }else{
                            resultTip({error: 'warning', msg:data.message});
                        }
                        that.btnDisanled=false
                    },
                    error:function(data){
                        that.btnDisanled=false
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            }
        }
    })

    </script>
