<h1><label><?php echo Yii::t("auth", "What is my IvyOnline account?"); ?></label></h1>
<p>
<?php echo Yii::t("auth", "We understand you're having problems logging on to IvyOnline. Please fill in the form below and our campus staff will follow up after you submit it.")?>
</p>
<p>
<?php echo Yii::t("auth", "Please kindly note that our campus staff may contact you when verifying your information. Thank you!");?>
</p>
<div>
    <?php
    foreach (Yii::app()->user->getFlashes() as $key => $message) {
        echo '<div class="span-18 flash-' . $key . '">' . $message . "</div>\n";
    }
    echo '<div class="clear"></div>';
    ?>
    <?php if ($model->isNewRecord):?>
    <div class="form">
    <?php
    $form = $this->beginWidget('CActiveForm', array(
        'id' => 'lost-account',
        'enableClientValidation' => true,
        'clientOptions' => array(
            'validateOnSubmit' => true,
        ),
    ));
    ?>
        <dl class="row">
            <dt><?php echo CHtml::activeLabelEx($model, 'schoolid'); ?></dt>
            <dd>
                <?php echo CHtml::activeDropDownList($model, 'schoolid', $schoolsel) ?>
            </dd>
        </dl>
        <dl class="row">
            <dt><?php echo CHtml::activeLabelEx($model, 'childname'); ?></dt>
            <dd>
                <?php echo CHtml::activeTextField($model, 'childname') ?>
            </dd>
        </dl>
        <dl class="row">
            <dt><?php echo CHtml::activeLabelEx($model, 'birthday'); ?></dt>
            <dd>
                <?php
                    $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                        "model"=>$model,
                        "attribute"=>"birthday",
                        "options"=>array(
                            'changeMonth'=>true,
                            'changeYear'=>true,
                            'dateFormat'=>'yy-mm-dd',
                        ),
                        'htmlOptions'=>array(
                            'autocomplete'=>'off',
                        ),
                    ));
                ?>
            </dd>
        </dl>
        <dl class="row">
            <dt><?php echo CHtml::activeLabelEx($model, 'parentname'); ?></dt>
            <dd>
                <?php echo CHtml::activeTextField($model, 'parentname') ?>
            </dd>
        </dl>
        <dl class="row">
            <dt><?php echo CHtml::activeLabelEx($model, 'email'); ?></dt>
            <dd>
                <?php echo CHtml::activeTextField($model, 'email') ?>
            </dd>
        </dl>
        <dl class="row">
            <dt><?php echo CHtml::activeLabelEx($model, 'phone'); ?></dt>
            <dd>
                <?php echo CHtml::activeTextField($model, 'phone') ?>
            </dd>
        </dl>
        <dl class="row">
            <dt><?php echo CHtml::activeLabelEx($model, 'memo'); ?></dt>
            <dd>
                <?php echo CHtml::activeTextArea($model, 'memo') ?>
            </dd>
        </dl>
        
        <?php if(CCaptcha::checkRequirements()): ?>
        <div class="row">
            <dt><?php echo $form->labelEx($model,'verifyCode'); ?></dt>
            <dd>
                <div><?php $this->widget('CCaptcha'); ?></div>
                <div><?php echo $form->textField($model,'verifyCode'); ?></div>
            </dd>
        </div>
        <?php endif; ?>
        
        <dl class="row submit">
            <dd>
            <?php echo CHtml::submitButton(Yii::t("global", "Submit"), array("class"=>"w100 bigger"));?>
            </dd>
        </dl>
    <?php $this->endWidget(); ?>
    </div>
    <?php else:?>
        <dl class="row">
            <dd>
            <?php echo CHtml::htmlButton(Yii::t("global", "Back"), array("class"=>"w100 bigger", "onclick"=>"location.href='".Yii::app()->createUrl('//user/lostAccount')."'"));?>
            </dd>
        </dl>
    <?php endif;?>
</div>
<style>
div.form{
	margin-top: 10px;
}
#login h1{
	margin-bottom: 20px;
}
</style>