<div class="container-fluid">
    <!-- 面包屑导航 -->
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Teaching Tasks'), array('//mteaching/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Curriculum'), array('//mteaching/curriculum/project', 'official'=>'1'))?></li>
        <li class="active"><?php echo Yii::t('curriculum','Activity Candidates');?></li>
    </ol>
    <div class="row">
        <!-- 左侧菜单栏 -->
        <div class="col-md-2">
            <div class="list-group" id="classroom-status-list">
                <?php foreach ($this->leftMenu as $key => $value) { ?>
                <a href="<?php echo ($value[0] == 'project') ? $this->createUrl($value[0], array('official' =>1 ))  : $this->createUrl($value[0]);?>" class="list-group-item status-filter <?php echo $this->getAction()->getId()==$value[0]?'active':''; ?>"><?php echo $value[1]; ?></a></li>
                <?php } ?>
            </div>
        </div>
        <div class="col-md-10">
            <div class="btn-group mb10">
                <a class="btn btn-primary"  href="<?php echo $this->createUrl('editActivity'); ?>"><span class="glyphicon glyphicon-plus"></span> <?php echo Yii::t('curriculum', 'Add a New Activity');?></a>
            </div>
            <div class="panel panel-default">
            <div class="panel-body">
                <?php $this->widget('ext.ivyCGridView.BsCGridView', array(
                    'id'=>'points-product-grid',
                    'dataProvider'=>$activity,
                    'colgroups'=>array(
                        array(
                            "colwidth"=>array(50,50,50,80),
                        )
                    ),
                    'columns'=>array(
                      array(
                        'name'=>'cn_title',
                        'value'=>array($this,'getActivityName'),
                       ),
                      array(
                        'name'=>'en_title',
                        'value'=>array($this,'getEnActivityName'),
                       ),
                      array(
                        'name'=>Yii::t('curriculum','Public'),
                        'value'=>array($this,'getUserName'),
                       ),
                      array(
                        'name'=>Yii::t('curriculum','Operation'),
                        'value'=>array($this,'getActButton'),
                       ),
                    ),
                    )); 
                ?>
            </div>
          </div>
        </div>
    </div>
</div>


<script>
    //删除活动
    function deleteAct (deletebtn,aid) {
        if (confirm('确定要删除吗？')) {
            $.ajax( {    
                url:'<?php echo $this->createUrl('deleteActivity'); ?>',    
                data:{aid : aid},    
                type:'post',    
                dataType:'json',    
                success:function(data) {    
                    if(data.msg == 'success'){    
                        $(deletebtn).parent().parent().remove();
                        // window.location.reload();    
                    }else{    
                        alert(data.msg);
                        console.log(data);    
                    }    
                 },    
                error : function() {    
                    console.log("异常！");    
                }    
            });  
        }
    }
    //活动状态控制
    function toggle (togglebtn,aid) {
        $.ajax( {
            url:'<?php echo $this->createUrl('state'); ?>',
            data:{aid : aid},
            type:'post',
            dataType:'json',
            success:function(data){
                console.log(data);
                if (data.msg == "success") {
                    $(togglebtn).text(data.btnvalue);
                    $(togglebtn).removeClass();
                    $(togglebtn).addClass(data.btnclass);
                }else{
                    console.log(data.msg);
                }
            },
            error:function(data){
                console.log(data.error);
            }
        });
    }
</script>