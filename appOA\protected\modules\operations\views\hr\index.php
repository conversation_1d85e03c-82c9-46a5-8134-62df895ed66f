<?php if ($type == 1 && Yii::app()->user->checkAccess("oSuperAdmin")):?>
<div class="mb10">
    <a href="<?php echo $this->createUrl("/operations/hr/update");?>" class="btn"><span class="add"></span>添加员工</a>
</div>
<?php endif;?>
<?php if ($type == -1 && Yii::app()->user->checkAccess("oSuperAdmin")):?>
<div class="mb10">
    <a href="<?php echo $this->createUrl("/operations/hr/addStaff");?>" class="btn"><span class="add"></span>添加新员工资料</a>
</div>
<?php endif;?>

<div class="h_a">搜索</div>
<div class="search_type cc mb10">
    <form action="" method="post">
        <input type="hidden" name="action" value="search">
        <label>中文姓名：<input name="name" type="text" class="input length_2 mr10" value="<?php echo $_name?>"></label>
        <label>电子邮箱：<input name="email" type="text" class="input mr10" value="<?php echo $_email?>"></label>
        <button type="submit" class="btn">搜索</button>
    </form>
</div>

<?php
$this->widget('ext.ivyCGridView.IvyCGridView', array(
	'id'=>'points-product-grid',
	'dataProvider'=>$dataProvider,
    'colgroups'=>array(
        array(
            "colwidth"=>array(100,100,80,80),
        )
    ),
	'columns'=>array(
		array(
            'name'=>'user_avatar',
            'value'=>'CHtml::image(Yii::app()->params["OAUploadBaseUrl"]."/users/thumbs/".$data->user_avatar, "", array("style"=>"width:46px;"))',
            'type'=>'raw',
            'sortable'=>false,
        ),
        array(
            'name'=>'profile.first_name',
            'value'=>'$data->profile->first_name." ".$data->profile->last_name',
        ),
        array(
            'name'=>'name',
            'sortable'=>false,
        ),
        array(
            'name'=>'profile.user_gender',
            'value'=>'$data->profile->getGender()',
        ),
        array(
            'name'=>'email',
            'sortable'=>false,
        ),
        array(
            'name'=>'profile.nationality',
            'value'=>'Yii::app()->controller->cfg["country"][$data->profile->nationality]',
        ),
        array(
            'name'=>'工作地点',
            'value'=>'Yii::app()->controller->cfs["branch"][$data->profile->branch]',
        ),
        array(
            'name'=>'职位',
            'value'=>'$data->profile->occupation ? $data->profile->occupation->getName() : ""',
        ),
        array(
			'class'=>'CButtonColumn',
            'template' => '{update}',
            'updateButtonUrl' => 'Yii::app()->controller->createUrl("/operations/hr/update", array("id"=>$data->uid, "type"=>$_GET["type"]))',
            'updateButtonImageUrl'=>false,
            'visible'=>Yii::app()->user->checkAccess("oSuperAdmin"),
		),
	),
)); ?>