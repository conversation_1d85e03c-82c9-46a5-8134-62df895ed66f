<div class="row J_cat" id="lea-view" style="display: none;">
    <div class="col-md-6 col-sm-12">
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4><?php echo Yii::t('teaching','Chinese Teaching Plan');?>
                <button class="pull-right btn btn-primary btn-xs" onclick="learntarget.edit_cn();">
                    <?php echo Yii::t('global','Edit');?></button>
                <button class="pull-right btn btn-info btn-xs mr10" onclick="learntarget.print('cn');">
                    <?php echo Yii::t('global','Print');?></button>
                </h4>
            </div>
            <div class="panel-body lea-view-content" id="lea-view-cn"></div>
        </div>
    </div>

    <div class="col-md-6 col-sm-12">
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4><?php echo Yii::t('teaching','English Teaching Plan');?>
                <button class="pull-right btn btn-primary btn-xs" onclick="learntarget.edit_en();">
                    <?php echo Yii::t('global','Edit');?></button>
                <button class="pull-right btn btn-info btn-xs mr10" onclick="learntarget.print('en');">
                    <?php echo Yii::t('global','Print');?></button>
                </h4>
            </div>
            <div class="panel-body lea-view-content" id="lea-view-en"></div>
        </div>
    </div>
</div>

<div class="row J_cat" id="lea-edit-cn" style="display: none;">
    <div class="col-md-12">
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4><?php echo Yii::t('teaching','Chinese Teaching Plan');?>
                <button class="pull-right btn btn-default btn-xs" onclick="learntarget.init();">
                    <?php echo Yii::t('global','Back');?></button></h4></div>
            <div class="panel-body"></div>
        </div>
    </div>
</div>

<div class="row J_cat" id="lea-edit-en" style="display: none;">
    <div class="col-md-12">
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4><?php echo Yii::t('teaching','English Teaching Plan');?>
                <button class="pull-right btn btn-default btn-xs" onclick="learntarget.init();">
                    <?php echo Yii::t('global','Back');?></button></h4></div>
            <div class="panel-body"></div>
        </div>
    </div>
</div>

<script>
    var learntarget = {
        init: function(){
            $('.J_cat').hide();
            $.getJSON('<?php echo $this->createUrl('getLeaView');?>', {classid: classid, month: month}, function(data){
                var tpl = _.template($('#lea-view-tpl').html());
                $('#lea-view-cn').html( tpl(data.cn) );
                if(_.isUndefined(data.mik)){
                    $('#lea-view-en').html( tpl(data.en) );
                }
                else{
                    $('#lea-view-en').html(_.template($('#mik-view-tpl').html(), data.mik) );
                }
                $('.lea-view-content dt').prepend('<span class="glyphicon glyphicon-chevron-right"></span> ')
                $('#lea-view').slideDown();
            });
        },
        edit_cn: function(){
            $('.J_cat').hide();
            $.getJSON('<?php echo $this->createUrl('getLeaView');?>', {classid: classid, month: month}, function(data){
                var tpl = _.template($('#edit-tpl').html());
                $('#lea-edit-cn .panel-body').html( tpl(data.cn) );
                head.Util.ajaxForm( $('#lea-edit-cn') );
                $('#lea-edit-cn').slideDown();
            });
        },
        edit_en: function(){
            $('.J_cat').hide();
            $.getJSON('<?php echo $this->createUrl('getLeaView');?>', {classid: classid, month: month}, function(data){
                if(_.isUndefined(data.mik)){
                    var tpl = _.template($('#edit-tpl').html());
                    $('#lea-edit-en .panel-body').html( tpl(data.en) );
                }
                else{
                    var tpl = _.template($('#mik-edit-tpl').html());
                    $('#lea-edit-en .panel-body').html( tpl(data.mik) );
                }
                head.Util.ajaxForm( $('#lea-edit-en') );
                $('#lea-edit-en').slideDown();
            });
        },
        print: function(lang){
            window.open('<?php echo $this->createUrl('printLea',
            array('classid'=>$this->selectedClassId, 'month'=>$this->selectedMonth));?>&lang='+lang);
        }
    };

    learntarget.init();

    function callback()
    {
        window.setTimeout(function(){
            learntarget.init();
        }, 1000);
    }
</script>

<script type="text/template" id="lea-view-tpl">
    <dl>
        <dt><?php echo Yii::t('teaching', 'Unit Theme');?></dt>
        <dd><%= nl2br(title)%></dd>
    </dl>
    <dl>
        <dt><?php echo Yii::t('teaching', 'Big Idea');?></dt>
        <dd><%= nl2br(newIdea)%></dd>
    </dl>
    <dl>
        <dt><?php echo Yii::t('teaching', 'Key Questions');?></dt>
        <dd><%= nl2br(questions)%></dd>
    </dl>
    <dl>
        <dt><?php echo Yii::t('teaching', 'Activities');?></dt>
        <dd><%= nl2br(activities)%></dd>
    </dl>
        <dl>
        <dt><?php echo Yii::t('teaching', 'Children\'s Song/Rhymes/Stories');?></dt>
        <dd><%= nl2br(song)%></dd>
    </dl>
    <dl>
        <dt><?php echo Yii::t('teaching', 'Special Festivals');?></dt>
        <dd><%= nl2br(festivals)%></dd>
    </dl>
    <dl>
        <dt><?php echo Yii::t('teaching', 'Field Trips');?></dt>
        <dd><%= nl2br(trips)%></dd>
    </dl>
    <dl>
        <dt><?php echo Yii::t('teaching', 'Physical & Health');?></dt>
        <dd><%= nl2br(newPhysical)%></dd>
    </dl>
    <dl>
        <dt><?php echo Yii::t('teaching', 'Language');?></dt>
        <dd><%= nl2br(newLanguage)%></dd>
    </dl>
    <dl>
        <dt><?php echo Yii::t('teaching', 'Social');?></dt>
        <dd><%= nl2br(newSocial)%></dd>
    </dl>
    <dl>
        <dt><?php echo Yii::t('teaching', 'Science & Math');?></dt>
        <dd><%= nl2br(newScience)%></dd>
    </dl>
    <dl>
        <dt><?php echo Yii::t('teaching', 'Art');?></dt>
        <dd><%= nl2br(newArt)%></dd>
    </dl>
</script>

<script type="text/template" id="mik-view-tpl">
    <dl>
        <dt><?php echo Yii::t('teaching', 'Words');?></dt>
        <dd><%= nl2br(words)%></dd>
    </dl>
    <dl>
        <dt><?php echo Yii::t('teaching', 'Sentences');?></dt>
        <dd><%= nl2br(sentences)%></dd>
    </dl>
    <dl>
        <dt><?php echo Yii::t('teaching', 'Letters');?></dt>
        <dd><%= nl2br(letters)%></dd>
    </dl>
    <dl>
        <dt><?php echo Yii::t('teaching', 'Songs');?></dt>
        <dd><%= nl2br(songs)%></dd>
    </dl>
    <dl>
        <dt><?php echo Yii::t('teaching', 'Stories');?></dt>
        <dd><%= nl2br(stories)%></dd>
    </dl>
    <dl>
        <dt><?php echo Yii::t('teaching', 'Functional Language');?></dt>
        <dd><%= nl2br(functional)%></dd>
    </dl>
</script>

<script type="text/template" id="edit-tpl">
    <?php echo CHtml::form('', 'post', array('class'=>'form-horizontal J_ajaxForm', 'role'=>'form'));?>
        <div class="form-group">
            <label for="title" class="col-sm-2 control-label"><?php echo Yii::t('teaching', 'Unit Theme');?></label>
            <div class="col-sm-10">
                <input type="text" class="form-control" id="title" name="content[title]" value="<%= title%>">
            </div>
        </div>
        <div class="form-group">
            <label for="newIdea" class="col-sm-2 control-label"><?php echo Yii::t('teaching', 'Big Idea');?></label>
            <div class="col-sm-10">
                <input type="text" class="form-control" id="newIdea" name="content[newIdea]" value="<%= newIdea%>">
            </div>
        </div>
        <div class="form-group">
            <label for="questions" class="col-sm-2 control-label"><?php echo Yii::t('teaching', 'Key Questions');?></label>
            <div class="col-sm-10">
                <input type="text" class="form-control" id="questions" name="content[questions]" value="<%= questions%>">
            </div>
        </div>
        <div class="form-group">
            <label for="activities" class="col-sm-2 control-label"><?php echo Yii::t('teaching', 'Activities');?></label>
            <div class="col-sm-10">
                <textarea class="form-control" name="content[activities]" id="activities"><%= activities%></textarea>
            </div>
        </div>
        <div class="form-group">
            <label for="song" class="col-sm-2 control-label"><?php echo Yii::t('teaching', 'Children\'s Song/Rhymes/Stories');?></label>
            <div class="col-sm-10">
                <textarea class="form-control" name="content[song]" id="song"><%= song%></textarea>
            </div>
        </div>
        <div class="form-group">
            <label for="festivals" class="col-sm-2 control-label"><?php echo Yii::t('teaching', 'Special Festivals');?></label>
            <div class="col-sm-10">
                <textarea class="form-control" name="content[festivals]" id="festivals"><%= festivals%></textarea>
            </div>
        </div>
        <div class="form-group">
            <label for="trips" class="col-sm-2 control-label"><?php echo Yii::t('teaching', 'Field Trips');?></label>
            <div class="col-sm-10">
                <textarea class="form-control" name="content[trips]" id="trips"><%= trips%></textarea>
            </div>
        </div>
    <div class="form-group">
        <label for="newPhysical" class="col-sm-2 control-label"><?php echo Yii::t('teaching', 'Physical & Health');?></label>
        <div class="col-sm-10">
            <textarea class="form-control" name="objectives[newPhysical]" id="newPhysical"><%= newPhysical%></textarea>
        </div>
    </div>
    <div class="form-group">
        <label for="newLanguage" class="col-sm-2 control-label"><?php echo Yii::t('teaching', 'Language');?></label>
        <div class="col-sm-10">
            <textarea class="form-control" name="objectives[newLanguage]" id="newLanguage"><%= newLanguage%></textarea>
        </div>
    </div>
    <div class="form-group">
        <label for="newSocial" class="col-sm-2 control-label"><?php echo Yii::t('teaching', 'Social');?></label>
        <div class="col-sm-10">
            <textarea class="form-control" name="objectives[newSocial]" id="newSocial"><%= newSocial%></textarea>
        </div>
    </div>
    <div class="form-group">
        <label for="newScience" class="col-sm-2 control-label"><?php echo Yii::t('teaching', 'Science & Math');?></label>
        <div class="col-sm-10">
            <textarea class="form-control" name="objectives[newScience]" id="newScience"><%= newScience%></textarea>
        </div>
    </div>
    <div class="form-group">
        <label for="newArt" class="col-sm-2 control-label"><?php echo Yii::t('teaching', 'Art');?></label>
        <div class="col-sm-10">
            <textarea class="form-control" name="objectives[newArt]" id="newArt"><%= newArt%></textarea>
        </div>
    </div>

<!--        <div class="form-group">-->
<!--            <label for="language" class="col-sm-2 control-label"><?php /*//echo Yii::t('teaching', 'Language and Literacy');*/?><!--</label>-->
<!--            <div class="col-sm-10">-->
<!--                <textarea class="form-control" name="objectives[language]" id="language"><%= language%></textarea>-->
<!--            </div>-->
<!--        </div>-->
<!--        <div class="form-group">-->
<!--            <label for="mathematical" class="col-sm-2 control-label">--><?php /*//echo Yii::t('teaching', 'Mathematical Thinking');*/?><!--</label>-->
<!--            <div class="col-sm-10">-->
<!--                <textarea class="form-control" name="objectives[mathematical]" id="mathematical"><%= mathematical%></textarea>-->
<!--            </div>-->
<!--        </div>-->
<!--        <div class="form-group">-->
<!--            <label for="science" class="col-sm-2 control-label">--><?php /*//echo Yii::t('teaching', 'Science Thinking');*/?><!--</label>-->
<!--            <div class="col-sm-10">-->
<!--                <textarea class="form-control" name="objectives[science]" id="science"><%= science%></textarea>-->
<!--            </div>-->
<!--        </div>-->
<!--        <div class="form-group">-->
<!--            <label for="physical" class="col-sm-2 control-label">--><?php /*//echo Yii::t('teaching', 'Physical/Health');*/?><!--</label>-->
<!--            <div class="col-sm-10">-->
<!--                <textarea class="form-control" name="objectives[physical]" id="physical"><%= physical%></textarea>-->
<!--            </div>-->
<!--        </div>-->
<!--        <div class="form-group">-->
<!--            <label for="social" class="col-sm-2 control-label">--><?php /*//echo Yii::t('teaching', 'Social/Personal');*/?><!--</label>-->
<!--            <div class="col-sm-10">-->
<!--                <textarea class="form-control" name="objectives[social]" id="social"><%= social%></textarea>-->
<!--            </div>-->
<!--        </div>-->
<!--        <div class="form-group">-->
<!--            <label for="art" class="col-sm-2 control-label">--><?php /*//echo Yii::t('teaching', 'Art');*/?><!--</label>-->
<!--            <div class="col-sm-10">-->
<!--                <textarea class="form-control" name="objectives[art]" id="art"><%= art%></textarea>-->
<!--            </div>-->
<!--        </div>-->
<!--        <div class="form-group">-->
<!--            <label for="social_studies" class="col-sm-2 control-label">--><?php /*//echo Yii::t('teaching', 'Social Studies');*/?><!--</label>-->
<!--            <div class="col-sm-10">-->
<!--                <textarea class="form-control" name="objectives[social_studies]" id="social_studies"><%= social_studies%></textarea>-->
<!--            </div>-->
<!--        </div>-->-->
        <div class="form-group">
            <div class="col-sm-2"></div>
            <div class="col-sm-10 checkbox">
                <label><input type="checkbox" name="stat" value="20"
                <% if(stat == 20){%>checked<% }%>> <?php echo Yii::t('teaching','Make Online');?></label>
            </div>
        </div>
        <div class="form-group">
            <div class="col-sm-offset-2 col-sm-10">
                <button type="submit" class="btn btn-primary J_ajax_submit_btn">Submit</button>
            </div>
        </div>
        <input type="hidden" name="flag" value="<%= flag%>">
    <?php echo CHtml::endform();?>
</script>

<script type="text/template" id="mik-edit-tpl">
    <?php echo CHtml::form('', 'post', array('class'=>'form-horizontal J_ajaxForm', 'role'=>'form'));?>
        <div class="form-group">
            <label for="words" class="col-sm-2 control-label"><?php echo Yii::t('teaching', 'Words');?></label>
            <div class="col-sm-10">
                <textarea class="form-control" name="content[words]" id="activities"><%= words%></textarea>
            </div>
        </div>
        <div class="form-group">
            <label for="sentences" class="col-sm-2 control-label"><?php echo Yii::t('teaching', 'Sentences');?></label>
            <div class="col-sm-10">
                <textarea class="form-control" name="content[sentences]" id="song"><%= sentences%></textarea>
            </div>
        </div>
        <div class="form-group">
            <label for="letters" class="col-sm-2 control-label"><?php echo Yii::t('teaching', 'Letters');?></label>
            <div class="col-sm-10">
                <textarea class="form-control" name="content[letters]" id="festivals"><%= letters%></textarea>
            </div>
        </div>
        <div class="form-group">
            <label for="songs" class="col-sm-2 control-label"><?php echo Yii::t('teaching', 'Songs');?></label>
            <div class="col-sm-10">
                <textarea class="form-control" name="content[songs]" id="trips"><%= songs %></textarea>
            </div>
        </div>
        <div class="form-group">
            <label for="stories" class="col-sm-2 control-label"><?php echo Yii::t('teaching', 'Stories');?></label>
            <div class="col-sm-10">
                <textarea class="form-control" name="content[stories]" id="trips"><%= stories %></textarea>
            </div>
        </div>
        <div class="form-group">
            <label for="functional" class="col-sm-2 control-label"><?php echo Yii::t('teaching', 'Functional Language');?></label>
            <div class="col-sm-10">
                <textarea class="form-control" name="content[functional]" id="trips"><%= functional%></textarea>
            </div>
        </div>
        <div class="form-group">
            <div class="col-sm-2"></div>
            <div class="col-sm-10 checkbox">
                <label><input type="checkbox" name="stat" value="20"
                <% if(stat == 20){%>checked<% }%>> <?php echo Yii::t('teaching','Make Online');?></label>
            </div>
        </div>
        <div class="form-group">
            <div class="col-sm-offset-2 col-sm-10">
                <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
            </div>
        </div>
        <input type="hidden" name="flag" value="<%= flag%>">
    <?php echo CHtml::endform();?>
</script>

<style>
    textarea{height: 92px !important;}
    .lea-view-content dt{font-size:120%; margin-bottom: 4px;}
    .lea-view-content dd{padding-left: 1em; border-left: 1px solid #efefef; margin-left: 1em; font-size:110%;}
</style>