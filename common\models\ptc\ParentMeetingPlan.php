<?php

/**
 * This is the model class for table "pmeet_plan".
 *
 * The followings are the available columns in table 'pmeet_plan':
 * @property integer $id
 * @property integer $startyear
 * @property integer $calendar_id
 * @property string $school_id
 * @property integer $semester
 * @property integer $classid
 * @property integer $default_duration
 * @property string $timeslot_starts
 * @property integer $status
 * @property string $children_ids
 * @property string $extra
 * @property integer $creater
 * @property integer $created
 */
class ParentMeetingPlan extends CActiveRecord
{
    const STATUS_ONLINE = 1; //开放预约
    const STATUS_OFFLINE = 0; //未开放预约
    public $title_cn;
    public $title_en;
    public $starYear;
    public $classData;
    public $start_date;
    public $end_date;
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'pmeet_plan';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('timeslot_starts, children_ids, extra', 'safe'),
			array('startyear, calendar_id, semester, classid, default_duration, status, creater, created', 'numerical', 'integerOnly'=>true),
			array('school_id', 'length', 'max'=>32),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, startyear, calendar_id, school_id, extra, semester, classid, default_duration, timeslot_starts, status, children_ids, creater, created', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'items' => array(self::HAS_MANY, 'ParentMeetingItem', 'planid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'startyear' => 'Startyear',
			'calendar_id' => 'Calendar',
			'school_id' => 'School',
			'semester' => 'Semester',
			'classid' => 'Classid',
			'default_duration' => Yii::t('labels','Default Duration'),
			'timeslot_starts' => Yii::t('labels','Timeslot Starts'),
			'status' => 'Status',
			'children_ids' => 'Children Ids',
			'extra' => 'extra',
			'creater' => 'Creater',
			'created' => 'Created',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('startyear',$this->startyear);
		$criteria->compare('calendar_id',$this->calendar_id);
		$criteria->compare('school_id',$this->school_id,true);
		$criteria->compare('semester',$this->semester);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('default_duration',$this->default_duration);
		$criteria->compare('timeslot_starts',$this->timeslot_starts,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('children_ids',$this->children_ids,true);
		$criteria->compare('extra',$this->extra,true);
		$criteria->compare('creater',$this->creater);
		$criteria->compare('created',$this->created);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return ParentMeetingPlan the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    /**
     * @return CDbConnection the database connection used for this class
     */
    public function getDbConnection()
    {
        return Yii::app()->subdb;
    }

    public function timeslotExplode()
    {
        $timeslots = array();
        if($this->timeslot_starts)
            $timeslots = explode(',', $this->timeslot_starts);
        return $timeslots;
    }
}
