<div class="h_a">添加管理校园</div>

<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'points-product-form',
	'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm'),
)); ?>
<div class="table_full">
    <table width="100%" class="" style="margin-bottom:0;">
        <col width="150" />
        <col width="400" />
        <col />
        <tbody>
            <tr>
                <th><?php echo $form->labelEx($model,'type'); ?></th>
                <td>
                    <?php echo $form->dropDownList($model, 'type', OA::getMultiAdmType());?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'type'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'uid'); ?></th>
                <td>
                    <?php $this->widget('ext.search.StaffSearchBox', array(
                        'acInputCSS' => 'length_4',
                        'data' => array(),
                        'useModel' => true,
                        'model' => $model,
                        'attribute' => 'uid',
                        'allowMultiple' => false,
                        'withAlumni' => false,
                    )) ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'uid'); ?></div></td>
            </tr>
            <tr>
                <th>
                    <?php echo $form->labelEx($model,'schoolid'); ?>
                </th>
                <td>
                    <?php echo $form->listBox($model, 'schoolid', CHtml::listData($branch, 'branchid', 'title'), array('multiple'=>'multiple', 'size'=>8));?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'schoolid'); ?></div></td>
            </tr>
        </tbody>
    </table>
</div>
<div class="btn_wrap">
    <div class="btn_wrap_pd">
        <button class="btn btn_submit mr10 J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
    </div>
</div>
<?php $this->endWidget(); ?>