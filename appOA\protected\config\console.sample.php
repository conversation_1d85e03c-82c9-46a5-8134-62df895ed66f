<?php

// This is the configuration for yiic console application.
// Any writable CConsoleApplication properties can be configured here.
Yii::setPathOfAlias('common', dirname(__FILE__) . '/../../../common');
Yii::setPathOfAlias('xoopsVarPath', 'D:/php/mims/trunk/oasrc/XOOPS-data');
$baseUrl = "http://apps.mims.cn";
return array(
	'basePath'=>dirname(__FILE__).DIRECTORY_SEPARATOR.'..',
	'name'=>'My Console Application',
	// autoloading model and component classes
	'import'=>array(
		//'common.models.*',
		'application.components.*',
		'application.modules.srbac.controllers.SBaseController',
		'application.modules.srbac.models.*'
	),
	
	// application components
	'components'=>array(
		// uncomment the following to use a MySQL database
		'db'=>array(
			'connectionString' => 'mysql:host=localhost;dbname=mims2',
			'emulatePrepare' => true,
			'username' => 'root',
			'password' => '',
			'charset' => 'utf8',
		),
        'cache' => array(
            'class' => 'CFileCache',
        ),
		'authManager'=>array(
			'class'=>'application.modules.srbac.components.SDbAuthManager',
			'connectionID'=>'db',
			'itemTable'=>'auth_oa_items',
			'assignmentTable'=>'auth_oa_assignments',
			'itemChildTable'=>'auth_oa_itemchildren',
		),		
	),
    'params' => array(
        // this is used in contact page
    	'baseUrl'=>$baseUrl,
    ),
);