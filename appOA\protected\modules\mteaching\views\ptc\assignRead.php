<style>
    [v-cloak] {
        display: none;
    }
    .classlist{
        border: 1px solid #DBDBDB;
        border-radius: 4px;
        padding:16px 16px 0 16px; 
        margin-bottom:16px;
        background:#fff;
        margin-right: 10px;
    }
    .font16{
        font-size:16px
    }
    .pt5{
        padding-top:5px
    }
    .media{
        padding-bottom:16px
    }
    .childImg{
        width:44px;
        height:44px;
        border-radius:50%;
        object-fit: cover;
    }
    .reserveImg{
        width:32px;
        height:32px;
        border-radius:50%;
        object-fit: cover;
    }
    .lineHeight{
        line-height:13px;
        font-weight: 400;
        white-space: nowrap;
        overflow: hidden;
    }
    .table_wrap {
        width: 100%;
        overflow: auto;
    }
    .table {
        table-layout: fixed;
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        border-left: 1px solid #DDDDDD;
        border-top: 1px solid #DDDDDD;
    }
    td,th {
        width:100px;
        text-align:center;
        border-right: 1px solid #DDDDDD;
        border-bottom: 1px solid #DDDDDD;
        border-top:none !important;

    }
    .filter{
        vertical-align: middle !important
    }
        /* 表头固定 */
    thead tr th {
        position: sticky;
        top: 0;
        background: #F4F4F5;
        z-index: 1;

    }

    /* 首列固定/最后一列固定*/
    th:first-child,
    td:first-child{
        position: sticky;
        left: 0;
        background: #F4F4F5;
        text-align: center;
        right: 0px;
        width: 90px;
        z-index: 1;

    }

    /* 表头首列和最后一列强制最顶层 */
    th:first-child {
        z-index: 1;
        /*左上角单元格z-index，切记要设置，不然表格纵向横向滚动时会被该单元格右方或者下方的单元格遮挡*/
        background: #F4F4F5;
    }
    .scrollbar::-webkit-scrollbar {
    /*滚动条整体样式*/
    width : 10px;  /*高宽分别对应横竖滚动条的尺寸*/
    height:10px;
    }
    .scrollbar::-webkit-scrollbar-thumb {
        border-radius   : 10px;
        background-color: skyblue;
        background-image: -webkit-linear-gradient(
        45deg,
        rgba(255, 255, 255, 0.2) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0.2) 75%,
        transparent 75%,
        transparent
        );
    }
    .scrollbar::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    background   : #ededed;
    }
    .delChild {
        position: absolute;
        right: 0px;
        top: 0px;
        width: 24px;
        height: 24px;
        background: rgba(0, 0, 0, 0.5);
        color: #fff;
        text-align: center;
        line-height: 23px;
        border-bottom-left-radius: 2em;
        display:none
    }
    .delTd:hover .delChild{
        display:block
    }
    .red{
        color:#F0AD4E
    }
    .switch {
        position: relative;
        display: inline-block;
    }
    .switch input {display:none;}
    .slider {
        margin: 0;
        display: inline-block;
        position: relative;
        width: 40px;
        height: 20px;
        border: 1px solid #dcdfe6;
        outline: none;
        border-radius: 10px;
        box-sizing: border-box;
        background: #dcdfe6;
        cursor: pointer;
        transition: border-color .31s,background-color .3s;
        vertical-align: middle;
    }
    .slider:before {
        content: "";
        position: absolute;
        top: 1px;
        left: 1px;
        border-radius: 50%;
        transition: all .3s;
        width: 16px;
        height: 16px;
        background-color: #fff;
    }
    input:checked + .slider {
        background-color: #2196F3;
    }
    input:checked + .slider:before {
        left: 100%;
        margin-left: -17px;
    }
    .image{
        width: 54px;
       height: 54px;
       border-radius: 8px;
       object-fit: cover;
    }
    .font18{
        font-size:18px
    }
    .assessment{
        background: #FAFAFA;
        padding:16px;
        line-height: 20px;
    }
    .yellow{
        color:#F0AD4E
    }
    .blue{
        background:rgba(77, 136, 210, 0.15);
    }
    .blueColor{
        color:#DFF0D8 !important
    }
    .borderBlue{
        border:1px solid #4D88D2
    }
    #classlist{
        overflow-y: auto;
    }
    .green{
        color:#5CB85C
    }
    .piont{
        width: 8px;
        height: 8px;
        border-radius:50%;
        display:inline-block;
        background:#5CB85C;
        margin-right:3px
    }
    .grey{
        background:#cccccc
    }
    .bgRed{
        background:#F0AD4E
    }
    .bgGreen{
        background:#5CB85C
    }
    .loading{
        width:98%;
        height:95%;
        background:#fff;
        position: absolute; 
        opacity: 0.5;
        z-index: 99
    }
    .loading span{
        width:100%;
        height:100%;
        display:block;
        background: url("<?php echo Yii::app()->theme->baseUrl?>/images/loading.gif")no-repeat center center ;
    }
    #ui-datepicker-div{
        z-index:9 !important
    }
    .translate{
        position: absolute;
        width: 25px;
        height: 25px;
        bottom: -8px;
        left: 25px;
    }
    .example{
        width: 25px;
        height: 25px;
    }
    .posRight{
        left:50%;
        margin-left:10px
    }
    .bluemanyChildren{
        position: absolute;
        left: 0;
        top: 0;
        padding: 2px 15px;
        background: #4D88D2;
        color: #fff;
        border-radius: 0 81px 81px 0;
    }
    .manyChildren{
        position: absolute;
        left: 0;
        top: 0;
        padding: 2px 15px;
        background: #E5E7EB;
        color: #333;
        border-radius: 0 81px 81px 0;
    }
    .maxWidth{
        width:326px
    }
    .bg6{
        background:#666666 !important
    }
    .noPtc{
        width: 44px;
       height: 44px;
       border-radius: 50%;
       object-fit: cover;
    }
    .border{
        border: 1px solid #E5E7EB;
        border-radius: 5px;
        padding:10px
    }
    .exportHide{
        display:none;
        margin-left:-15px
    }
    .bgFA{
        background: #FAFAFA;
    }
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Teaching Tasks'), array('//mteaching/default/index')) ?></li>
        <li><?php echo Yii::t('site', 'G6-12 PTC Management') ?></li>
    </ol>
    <div class="row" id='container' v-cloak>
        <div class='col-md-12 col-sm-12 mb20 flex'>
            <div class='form-inline flex1'>
                <select class="form-control" id="startyear" onchange="change()">
                    <?php foreach ($this->startYearList as $startYear => $text): ?>
                        <option <?php echo $startYear == $this->startYear ? 'selected' : ''; ?> value='<?php echo $startYear; ?>'><?php echo $text; ?></option>
                    <?php endforeach?>
                </select>
                <select class="form-control" id="semester" onchange="change()">
                    <?php foreach ($this->semesterList as $semester => $text): ?>
                        <option <?php echo $semester == $this->semester ? 'selected' : ''; ?> value='<?php echo $semester; ?>'><?php echo $text; ?></option>
                    <?php endforeach?>
                </select>
            </div>
        </div>
        <div v-if='indexData.childInfo.length==0'  class='col-md-12 col-sm-12'>
        <div class="alert alert-warning" role="alert"><?php echo Yii::t('ptc','No Data') ?></div>
        </div>
        <div v-else>
            <div class='col-md-12 col-sm-12 mb20 flex'>
                <div class='flex1'>
                    <span class='font14 color3'><?php echo Yii::t('ptc','Legend: ') ?></span> 
                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/A-E.png' ?>" class='example'  alt=""> <span class='color6'>{{indexData.translationConfig[1].title}}</span> 
                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/E-A.png' ?>" class='example ml20' alt=""> <span class='color6'>{{indexData.translationConfig[2].title}}</span>
                </div>
                <div class='form-inline' style='line-height:30px'>
                    
                    <div class="form-group">
                        <label for="exampleInputName2"><?php echo Yii::t('ptc','Freeze student list at:') ?></label>
                        <!-- <input type="text" class="form-control select_2  ml10" id="endDate" v-model='enddate'  placeholder="<?php echo Yii::t("newDS", "Select a date"); ?>"  :value='enddate'> -->
                        {{enddate}}
                    </div>
                    <div class="form-group ml20">
                        <label for="exampleInputName2"><?php echo Yii::t('ptc','When parents can view:') ?></label>
                        <!-- <input disabled="disabled" type="text" class="form-control select_2  ml0" id="startDate" v-model='startdate'  placeholder="<?php echo Yii::t("newDS", "Select a date"); ?>"  :value='startdate'> -->
                        {{startdate}}
                    </div>
                </div>
            </div>
            <div class='clearfix'></div>
            <div class='flex'>
                <div class='maxWidth col-md-4 col-sm-4'>
                    <p class='color3 font14 mb15'><strong><?php echo Yii::t('ptc','Student list with PTC') ?>（{{Object.keys(indexData.allChild).length}}）</strong> </p>
                    <p class='color6 font12 mb20' v-if='indexData.hasPlan && !preview'>
                        <span class=' mr10 cur-p' :class='unconfirmed==1?"text-primary":"color6"' @click='filterType(1)'><span class='piont bgGreen'></span><?php echo Yii::t('ptc','Attend') ?> ({{filterTypeUnm(1)}})</span>
                        <span class=' mr10 cur-p' :class='unconfirmed==-1?"text-primary":"color6"'  @click='filterType(-1)'><span class='piont bgRed'></span><?php echo Yii::t('ptc','Cannot attend') ?> ({{filterTypeUnm(-1)}})</span>
                        <span class=' mr10 cur-p' :class='unconfirmed==0?"text-primary":"color6"'  @click='filterType(0)'><span class='piont grey'></span><?php echo Yii::t('ptc','Not Confirmed') ?> ({{filterTypeUnm(0)}})</span>
                    </p>
                    <div class='mb15 mr15'>
                        <input type="text" class="form-control" placeholder="<?php echo Yii::t("ptc", "Input name to filter"); ?>" v-model="searchStu">
                    </div>
                    <div id='classlist' class='scroll-box'  :style='"max-height:"+maxHeight'>
                        <div class='classlist drag relative'  v-for='(list,key,index) in searchData'  :class='key==family_id?"blue borderBlue":""'  @click='showChild(key)'>
                            <div v-if='list.length>1'   :class='key==family_id?"bluemanyChildren":"manyChildren"'><?php echo Yii::t('ptc', 'Sibling Family'); ?></div>
                            <template v-if='indexData.hasPlan  && !preview'>
                                <div class='color9 flex mb10' :class='list.length>1?"mt20":""' v-if='indexData.anotherFamilyDay[key]'>
                                    <span class='glyphicon glyphicon-exclamation-sign'></span>
                                    <span class='flex1 ml4'><?php echo Yii::t('ptc', 'Other Scheduled Date: '); ?><span v-for='(dates,datesi) in indexData.anotherFamilyDay[key]' class='mr10'>{{indexData.dayList[dates]}}</span> </span> 
                                </div>
                            </template>
                            <div v-for='(item,idx) in list' :class='list.length>1 && idx==0 && !indexData.anotherFamilyDay[key]?"mt20":""'>
                                <template v-if='indexData.hasPlan  && !preview'>
                                    
                                    <div class='flex' >
                                        <div class='flex1'>
                                            <template v-if='indexData.parentsChoose[item]'>
                                                <span class='piont ' v-for='(id,i) in indexData.parentsChoose[item]' :class='id==-1?"bgRed":id==1?"bgGreen":"grey"'></span>  
                                            </template>
                                        </div>
                                        <div v-if='opAuth'>
                                            <span class='ml10 text-primary cur-p' @click='reset(item)' v-if='showReset(item)'><?php echo Yii::t('ptc', 'Reset'); ?> </span>
                                        </div>
                                    </div>
                                </template>
                                <div class="media">
                                    <div class="media-left pull-left media-middle relative">
                                        <a href="javascript:void(0)">
                                            <img :src="childName(item,'avatar')" data-holder-rendered="true" class="childImg">
                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/E-A.png' ?>" class='translate' alt="" v-if='indexData.translation[item]==3'>
                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/A-E.png' ?>" class='translate' alt="" v-if='indexData.translation[item]==2'>
                                        </a>
                                    </div>
                                    <div class="media-right pull-right">
                                        <span class="badge mt10"  onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" :title="`<?php echo Yii::t("ptc", "Total"); ?>${indexData.allChild[item]}<?php echo Yii::t("ptc", "meetings"); ?>`"  data-placement="left">{{indexData.allChild[item]}}</span>
                                    </div>
                                    <div class="media-body pt5 media-middle">
                                        <h4 class="media-heading font14 color3">{{ childName(item,'name')}}</h4>
                                        <div class="color6 font12 mt5 ">{{childName(item,'className')}}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-if='unconfirmed==null'>
                            <div class='classlist drag relative bgFA'  v-for='(list,key,index) in another' >
                                <div v-if='list.length>1'   :class='key==family_id?"bluemanyChildren":"manyChildren"'><?php echo Yii::t('ptc', 'Sibling Family'); ?></div>
                                <template v-if='indexData.hasPlan  && !preview'>
                                    <div class='color9 flex mb10' :class='list.length>1?"mt20":""' v-if='indexData.anotherFamilyDay[key]' v-if='indexData.anotherFamilyDay[key]'>
                                        <span class='glyphicon glyphicon-exclamation-sign'></span>
                                        <span class='flex1 ml4'><?php echo Yii::t('ptc', 'Scheduled Date: '); ?><span v-for='(dates,datesi) in indexData.anotherFamilyDay[key]' class='mr10'>{{indexData.dayList[dates]}}</span></span> 
                                    </div>
                                </template>
                                <div v-for='(item,idx) in list' :class='list.length>1 && idx==0 && !indexData.anotherFamilyDay[key]?"mt20":""'>
                                    <!-- <template v-if='indexData.hasPlan  && !preview'>
                                        <div class='color9 flex'><span class='glyphicon glyphicon-exclamation-sign'></span><span class='flex1 ml4'>请在 {{ indexData.dayList[indexData.anotherFamilyDay[key]]}} 的安排中查看</span> </div>
                                    </template> -->
                                    <div class="media">
                                        <div class="media-left pull-left media-middle relative">
                                            <a href="javascript:void(0)">
                                                <img :src="childName(item,'avatar')" data-holder-rendered="true" class="childImg">
                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/E-A.png' ?>" class='translate' alt="" v-if='indexData.translation[item]==3'>
                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/A-E.png' ?>" class='translate' alt="" v-if='indexData.translation[item]==2'>
                                            </a>
                                        </div>
                                        <div class="media-right pull-right">
                                            <span class="badge mt10"  onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" :title="`<?php echo Yii::t("ptc", "Total"); ?>${indexData.allChild[item]}<?php echo Yii::t("ptc", "meetings"); ?>`"  data-placement="left">{{indexData.allChild[item]}}</span>
                                        </div>
                                        <div class="media-body pt5 media-middle">
                                            <h4 class="media-heading font14 color3">{{ childName(item,'name')}}</h4>
                                            <div class="color6 font12 mt5 ">{{childName(item,'className')}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class='classlist drag relative bgFA'  v-for='(list,key,index) in unassigned' >
                                <div v-if='list.length>1'   :class='key==family_id?"bluemanyChildren":"manyChildren"'><?php echo Yii::t('ptc', 'Sibling Family'); ?></div>
                                <div v-for='(item,idx) in list' :class='list.length>1 && idx==0?"mt20":""'>
                                    <template v-if='indexData.hasPlan  && !preview'>
                                    <div class='color9 flex'><span class='glyphicon glyphicon-exclamation-sign'></span><span class='flex1 ml4'>无面谈日期</span> </div>
                                    </template>
                                    <div class="media">
                                        <div class="media-left pull-left media-middle relative">
                                            <a href="javascript:void(0)">
                                                <img :src="childName(item,'avatar')" data-holder-rendered="true" class="childImg">
                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/E-A.png' ?>" class='translate' alt="" v-if='indexData.translation[item]==3'>
                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/A-E.png' ?>" class='translate' alt="" v-if='indexData.translation[item]==2'>
                                            </a>
                                        </div>
                                        <div class="media-right pull-right">
                                            <span class="badge mt10"  onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" :title="`<?php echo Yii::t("ptc", "Total"); ?>${indexData.allChild[item]}<?php echo Yii::t("ptc", "meetings"); ?>`"  data-placement="left">{{indexData.allChild[item]}}</span>
                                        </div>
                                        <div class="media-body pt5 media-middle">
                                            <h4 class="media-heading font14 color3">{{ childName(item,'name')}}</h4>
                                            <div class="color6 font12 mt5 ">{{childName(item,'className')}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class='col-md-8 col-sm-8 flex1'>
                    <!-- <div class='assessment flex'>
                        <div class='flex1'>
                            <div v-if='!indexData.hasPlan'>
                                <?php echo Yii::t('ptc','Schedule Plans: ') ?>
                                <label class="radio-inline" v-for='(list,index) in indexData.planList'>
                                    <input type="radio" name="inlineRadioOptions" id="inlineRadio1" :value="index" @click='showPlan(index)'> {{list.title}}
                                </label>
                                <span v-if='preview' class='yellow'><span class='glyphicon glyphicon-exclamation-sign ml20'></span> <?php echo Yii::t('ptc','Schedule not saved, preview only.') ?></span>
                            </div>
                        </div>
                        <div>
                            <div v-if='!indexData.hasPlan'>
                                <button type="button" class="btn btn-primary" :disabled='!preview || Loading' @click='savePlan'><?php echo Yii::t('ptc','Save as final schedules') ?></button>
                            </div>
                            <div v-else>
                                <button type="button" class="btn btn-default" @click='cleanplan' v-if='opAuth'><?php echo Yii::t('ptc','Reset current schedules') ?></button>
                            </div>
                        </div>
                    </div> -->
                    <div class='color3 font14 mb24 mt20 flex'>
                        <div class='flex1 '>
                            <button type="button" class="btn mr10 btn-sm" :class='key==indexData.targetDay?"btn-primary":"btn-default"' v-for='(list,key,index) in indexData.dayList' @click='hrefDay(key)'>{{list}}</button>  
                        </div>
                        <span class='mr20'>
                            <label class="switch">
                                <input type="checkbox" :checked='isTabShow' @change='tabShow()'>
                                <span class="slider"></span>
                            </label>
                            <span class='ml5' style='padding-top:3px'><?php echo Yii::t('ptc', 'Hide others when a student is selected'); ?></span>
                        </span>
                        <span >
                            <label class="switch">
                                <input type="checkbox" :checked='isAvatar' @change='showAvatar()'>
                                <span class="slider"></span>
                            </label>
                            <span class='ml5' style='padding-top:3px'><?php echo Yii::t('ptc', 'Display Photo'); ?></span>
                        </span>
                    </div>
                    <div class='table_wrap scrollbar  mb20'  :style='"max-height:"+maxHeight'>
                        <div class='loading ' v-if='Loading'>
                            <span></span>
                        </div>
                        <table class="table" id='table' >
                            <thead>
                                <tr>
                                    <th width="90" style='z-index:2;vertical-align: middle !important; width: 90px;' class='font14 text-center'><strong><?php echo Yii::t("ptc", "Time Slot");?></strong> </th>
                                    <th v-for='(list,key,index) in indexData.teachersInfo'  class="text-center">
                                        <div class='cur-p'>
                                            <div>
                                                <img :src="list.photoUrl" alt="" class="reserveImg">
                                            </div>
                                            <div class="color3 mt5 lineHeight">{{list.name}}</div>
                                        </div>
                                        <!-- <div class="text-primary mt10" @click='setLocation(list,"unified")'  v-if='!preview && indexData.hasPlan && opAuth'><?php echo Yii::t("ptc", "Set location");?> </div> -->
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr  v-for='(list,index) in indexData.scheduleList' >
                                    <td  class='text-center filter'>
                                        <div>#{{index+1}}</div>
                                        <strong>{{list.start}}-{{list.end}}</strong>
                                    </td>
                                    <template  v-for='(item,key,idx) in indexData.teachersInfo' >
                                        <template v-if='indexData.hasPlan && assigned.length!=0'>
                                            <td v-if='assigned[key] && assigned[key][index]' :class='childName(assigned[key][index],"family_id")==family_id?"blue":""' @click='showChild( childName(assigned[key][index],"family_id") )'>
                                                <div v-if='isAvatar'class='relative'>
                                                    <span  v-if='indexData.parentsChoose[assigned[key][index]] && indexData.parentsChoose[assigned[key][index]][index]'>
                                                        <span class='glyphicon glyphicon-ok-sign green' v-if='indexData.parentsChoose[assigned[key][index]][index]==1' onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" title="<?php echo Yii::t("ptc", "Confirmed to attend"); ?>" data-placement="top"></span>
                                                        <span class='glyphicon glyphicon-exclamation-sign red' v-if='indexData.parentsChoose[assigned[key][index]][index]==-1'  onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" title="<?php echo Yii::t("ptc", "Not to attend"); ?>" data-placement="top"></span>
                                                    </span>
                                                    <img :src="childName(assigned[key][index],'avatar')" alt="" class="reserveImg">
                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/E-A.png' ?>" class='translate posRight' alt="" v-if='indexData.translation[assigned[key][index]]==3'>
                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/A-E.png' ?>" class='translate posRight' alt="" v-if='indexData.translation[assigned[key][index]]==2'>

                                                    </div>
                                                <div class="mt5" v-else>
                                                    <span  v-if='indexData.parentsChoose[assigned[key][index]] && indexData.parentsChoose[assigned[key][index]][index]'>
                                                        <span class='glyphicon glyphicon-ok-sign green' v-if='indexData.parentsChoose[assigned[key][index]][index]==1' onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" title="<?php echo Yii::t("ptc", "Confirmed to attend"); ?>" data-placement="top"></span>
                                                        <span class='glyphicon glyphicon-exclamation-sign red' v-if='indexData.parentsChoose[assigned[key][index]][index]==-1'  onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" title="<?php echo Yii::t("ptc", "Not to attend"); ?>" data-placement="top"></span>
                                                    </span>
                                                    <span class='color3 '>{{childName(assigned[key][index],'name')}} </span>
                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/E-A.png' ?>" class='example' alt="" v-if='indexData.translation[assigned[key][index]]==3'>
                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/A-E.png' ?>" class='example' alt="" v-if='indexData.translation[assigned[key][index]]==2'>
                                                </div>
                                                <div  v-if='!preview'>
                                                    <div class='text-primary mt10' v-if='indexData.locationList[key] && indexData.locationList[key][list.key]'><span style="cursor: pointer;">{{indexData.locationList[key][list.key].location}}</span></div>
                                                    <!-- <div class='text-primary mt10' v-else><span class='glyphicon glyphicon-edit' @click.stop='setLocation(list,"alone",key,index)' style="cursor: pointer;"></span></div> -->
                                                </div>
                                                <!-- <span class='glyphicon glyphicon-trash delChild' @click='deleteSchedule()'></span> -->
                                            
                                            </td>
                                            <td v-else></td>
                                        </template>
                                        <template v-else-if='preview && assigned.length!=0'>
                                            <td v-if='assigned[key] && assigned[key][index+1]' :class='childName(assigned[key][index+1],"family_id")==family_id?"blue":""' @click='showChild( childName(assigned[key][index+1],"family_id") )'>
                                                <div v-if='isAvatar'>
                                                    <img :src="childName(assigned[key][index+1],'avatar')" alt="" class="reserveImg">
                                                    </div>
                                                <div class="mt5" v-else>
                                                    <span class='color3  '>{{childName(assigned[key][index+1],'name')}}</span>
                                                </div>
                                            </td>
                                            <td v-else></td>
                                        </template>
                                        <template v-else>
                                        <td></td>
                                        </template>
                                    </template>
                                </tr>
                            </tbody>

                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal fade" id="interviewLocation" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false"  data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog " role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" ><?php echo Yii::t('ptc', 'Set location') ?> <span id="remarks_t"></span></h4>
                    </div>
                    <div class="modal-body" >
                        <div class="media " v-if='teacherLon!=""'>
                            <div class="media-left pull-left media-middle">
                                <a href="javascript:void(0)"><img :src="indexData.teachersInfo[teacherLon].photoUrl" data-holder-rendered="true" class="image"></a>
                            </div> 
                            <div class="media-body mt15 media-middle">
                                <h4 class="media-heading  color3"><span class='font18 mr20'>{{indexData.teachersInfo[teacherLon].name}}</span></h4>
                            </div>
                        </div>
                        <div class="form-group mt20" v-if='locationType=="unified"'>
                            <label for="exampleInputEmail1"><?php echo Yii::t('ptc', 'Save location for all PTC meetings:') ?></label>
                            <input type="text" class="form-control" placeholder="<?php echo Yii::t('ptc', 'Input meeting location') ?>" v-model='teacherAddress'>
                        </div>
                        <div class="form-horizontal mt20" v-else-if='Object.keys(aloneData).length!=0'>
                            <div class="form-group">
                                <label for="inputEmail3" class="col-sm-2 control-label"><?php echo Yii::t("ptc", "Time Slot");?></label>
                                <div class="col-sm-10 mt10">
                                <span>{{aloneData.start}}-{{aloneData.end}}</span> 
                                </div>
                            </div>
                            <div class="form-group" v-if='assigned[teacherLon] && assigned[teacherLon][setChildIndex]'>
                                <label for="inputPassword3" class="col-sm-2 control-label"><?php echo Yii::t("ptc", "Student");?></label>
                                <div class="col-sm-10">
                                    <div class="media">
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                                <img :src="childName(assigned[teacherLon][setChildIndex],'avatar')" data-holder-rendered="true" class="reserveImg">
                                            </a>
                                        </div>
                                        <div class="media-body pt5 media-middle">
                                            <h4 class="media-heading font14 color3">{{childName(assigned[teacherLon][setChildIndex],'name')}} </h4>
                                            <div class="color6 font12 mt5 ">{{childName(assigned[teacherLon][setChildIndex],'className')}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inputEmail3" class="col-sm-2 control-label"><?php echo Yii::t('ptc', 'Set location') ?></label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" placeholder="<?php echo Yii::t('ptc', 'Input meeting location') ?>"  v-model='aloneAddress'>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" @click='assignLocation'><?php echo Yii::t('global','Save') ?></button>
                        <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal fade" id="delModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
            <div class="modal-dialog modal-sm" role="document">
                <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("ptc", "Reset current schedules"); ?></h4>
                </div>
                <div class="modal-body">
                    <div ><?php echo Yii::t("ptc", "Proceed to reset?"); ?></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close"); ?></button>

                    <button type="button"  class="btn btn-primary" @click='cleanplan("model")'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
                </div>
            </div>
        </div>
        <div class="modal fade" id="resetModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
            <div class="modal-dialog modal-sm" role="document">
                <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("ptc", "Reset parent confirm result");?></h4>
                </div>
                <div class="modal-body">      
                    <div class='mb10 mt10 color3 font12'><?php echo Yii::t("ptc", "This action will cancel the confirmation result of whether the parent is present or not, and parents need to confirm again, procced ?");?></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>

                    <button type="button"  class="btn btn-primary" @click='reset("model")'><?php echo Yii::t("global", "OK");?></button>
                </div>
                </div>
            </div>
        </div>
        <div class="modal fade" id="withoutPtc" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false" >
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" ><?php echo Yii::t('ptc', 'Students without PTC') ?> <span id="remarks_t"></span></h4>
                    </div>
                    <div class="modal-body" >
                        <div v-if='noInterview.students && Object.keys(noInterview.students).length!=0'>    
                            <div class="panel panel-default"  v-for='(list,index) in noInterview.students'>
                                <div class="panel-heading">
                                    <h3 class="panel-title">{{list.class_title}} <span class='badge ml10 bg6'>{{list.child_ids.length}}</span></h3>
                                </div>
                                <div class="panel-body">
                                    <div class="col-md-3 col-sm-3 mb10" v-for='(child,idx) in list.child_ids'>
                                        <div class="media">
                                            <div class="media-left pull-left media-middle">
                                                <a href="javascript:void(0)">
                                                    <img :src="noInterview.childInfo[child].avatar" data-holder-rendered="true" class="noPtc">
                                                </a>
                                            </div> 
                                            <div class="media-body pt10 media-middle">
                                                <h4 class="media-heading font14 color3 mt5">{{noInterview.childInfo[child].name}}</h4> 
                                                <!-- <div class="color6 font12 mt5 ">{{list.class_title}} </div> -->
                                            </div>
                                        </div>
                                    </div>
                                    <div class='clearfix'></div>
                                </div>
                            </div>      
                        </div>
                        <div class="alert alert-warning" role="alert" v-else><?php echo Yii::t("ptc", "No Data");?></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal fade" id="exportModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("ptc", "Export Daily Visitors List");?></h4>
                </div>
                <div class="modal-body">      
                    <div class='mb10 color6 font14'><?php echo Yii::t("ptc", "请选择要导出的名单");?></div>
                    <div class='col-md-3 col-sm-3 mb10 ' v-for='(list,index) in exportDays'>
                        <div :id='"export"+index' class='loading exportHide'>
                            <span></span>
                        </div>
                        <div class='border text-center cur-p font14' @click='exportData(list.day,index)'>
                            <div class='color3'><label>{{list.day}}</label></div>
                            <div class='color6'>{{list.weekday}}</div>
                        </div>
                    </div>
                    <div class='clearfix'></div>
                </div>
                </div>
            </div>
        </div>
    </div>

</div>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    function change() {
        var startyear = $('#startyear').val();
        var semester = $('#semester').val();
        var url = '<?php echo $this->createUrl('assign'); ?>&startyear=' + startyear + '&semester=' + semester;
        window.location.href = url;
    }
    var container = new Vue({
        el: "#container",
        data: {
            opAuth:<?php echo json_encode($opAuth); ?>,
            yid: "<?php echo $this->yid; ?>",
            startYearList: "<?php echo $this->startYearList; ?>",
            startYear: "<?php echo $this->startYear; ?>",
            semester: "<?php echo $this->semester; ?>",
            semesterList: <?php echo json_encode($this->semesterList); ?>,
            indexData: <?php echo json_encode($data); ?>,
            startdate:'',
            enddate:'',
            isAvatar:false,
            locationType:'',
            teacherLon:'',
            teacherAddress:'',
            aloneData:'',
            aloneAddress:'',
            assigned:{},
            preview:false,
            family_id:'',
            setChildIndex:'',
            planId:'',
            maxHeight:'',
            resetId:'',
            Loading:false,
            searchStu:'',
            noInterview:{},
            exportDays:[],
            unconfirmed:null,
            unconfirmedData:{},
            isTabShow:true,
        },
        computed: {
            searchData: function() {
                var search = this.searchStu.trim();
                var family={}
                let that=this
                if(this.unconfirmed==null){
                    if(search) {
                        that.indexData.childInfo.filter(function(product) {
                            if(that.indexData.familyInfo[product['family_id']]){
                                if(String(product['name'].toUpperCase()).indexOf(search.toUpperCase()) !== -1){
                                    family[product['family_id']]=that.indexData.familyInfo[product['family_id']]
                                }
                            }
                            
                        })
                        return family
                    }
                    return that.indexData.familyInfo;
                }else{
                    return this.unconfirmedData
                }
            },
            another: function() {
                var search = this.searchStu.trim();
                var family={}
                let that=this
                if(search) {
                    that.indexData.childInfo.filter(function(product) {
                        if(that.indexData.anotherFamilyInfo[product['family_id']]){
                            if(String(product['name'].toUpperCase()).indexOf(search.toUpperCase()) !== -1){
                                family[product['family_id']]=that.indexData.anotherFamilyInfo[product['family_id']]
                            }
                        }
                    })
                    return family
                }
                return that.indexData.anotherFamilyInfo;
            },
            unassigned: function() {
                var search = this.searchStu;
                var family={}
                let that=this
                if(search) {
                    that.indexData.childInfo.filter(function(product) {
                        if(that.indexData.unassignedFamilyInfo[product['family_id']]){
                            if(String(product['name'].toUpperCase()).indexOf(search.toUpperCase()) !== -1){
                                family[product['family_id']]=that.indexData.unassignedFamilyInfo[product['family_id']]
                            }
                        }
                    })
                    return family
                }
                return that.indexData.unassignedFamilyInfo;
            }
        },
        watch: {
            searchStu(){
                this.unconfirmed=null
            },
            family_id() {
                this.$nextTick(() => {
                    if(this.isTabShow){
                        this.cellLess()
                    }else{
                        $('#table').css({width: '100%'})
                        $('#table thead tr th, #table tbody tr td').show() 
                    }
                })
            },
            isTabShow(){
                this.$nextTick(() => {
                    if(this.isTabShow){
                        this.cellLess()
                    }else{
                        $('#table').css({width: '100%'})
                        $('#table thead tr th, #table tbody tr td').show() 
                    }
                })
            },
            planId(val) {
                if (val) {
                    this.rowLess()
                }
            }
        },
        mounted(){
            this.maxHeight=$(window).height()-150+'px'
            let that=this
            this.startdate=this.indexData.ptcInfo.p_view_from
            this.enddate=this.indexData.ptcInfo.froze_student_from
            setTimeout(function() {
                that.$nextTick(()=>{
                    $("#startDate").datepicker({
                        dateFormat: "yy-mm-dd ",
                    });
                    $("#endDate").datepicker({
                        dateFormat: "yy-mm-dd ",
                    });
                })
            }, 500);
            this.assigned=JSON.parse(JSON.stringify(this.indexData.assigned))
            if(!this.preview && this.indexData.hasPlan){
                this.rowLess()
            }
        },
        methods: {
            hrefDay(key){
                if(key=='<?php echo  Yii::app()->request->getParam('day', '')?>'){
                    return
                }
                var startyear = $('#startyear').val();
                var semester = $('#semester').val();
                var url = '<?php echo $this->createUrl('assign'); ?>&startyear=' + startyear + '&semester=' + semester+ '&day=' + key;
                window.location.href = url;
            },
            filterType(type){
                let that=this
                if(this.unconfirmed==type){
                    this.unconfirmed=null
                    that.unconfirmedData=that.indexData.familyInfo
                    return
                }
                this.unconfirmed=type
                var family={}
                that.indexData.childInfo.filter(function(product) {
                    if(that.indexData.parentsChoose[product['id']] && that.indexData.familyInfo[product['family_id']]){
                        let len=Object.values(that.indexData.parentsChoose[product['id']])
                        if(len.indexOf(type) !== -1){
                            family[product['family_id']]=that.indexData.familyInfo[product['family_id']]
                        }
                    }
                })
                that.unconfirmedData=family
            },
            filterTypeUnm(type){
                var lens=[]
                let that=this
                for(var key in that.indexData.parentsChoose){
                    let len=Object.values(that.indexData.parentsChoose[key])
                    if(len.indexOf(type) !== -1){
                        lens.push(1)
                    }
                }
                return lens.length
            },
            childName(id,type){
                var data
                this.indexData.childInfo.forEach(parent => {
                    if(parent.id==id){
                        data=parent[type]
                    }
                })
                return data
            },
            showChild(id){
                if(!this.preview && !this.indexData.hasPlan){
                    return
                }
                if(id==this.family_id){
                    this.family_id=''
                }else{
                    this.family_id=id
                }
            },
            showPlan(index){
                this.family_id=''
                this.planId=this.indexData.planList[index].id
                this.assigned=this.indexData.planList[index].content==null?[]:this.indexData.planList[index].content
                this.preview=true
                // this.indexData.hasPlan=true
            },
            showAvatar(){
                this.isAvatar=!this.isAvatar
            },
            tabShow(){
                this.isTabShow=!this.isTabShow
            },
           setLocation(data,type,key,index){
               if(!this.opAuth){
                   return
               }
               if(type=='unified'){
                   this.teacherLon=data.uid
               }else{
                   this.teacherLon=key
                   this.aloneData=data
                   this.setChildIndex=index
                    if(this.indexData.locationList[key] && this.indexData.locationList[key][data.key]){
                        this.aloneAddress=this.indexData.locationList[key][data.key].location
                    }else{
                        this.aloneAddress=''
                    }
               }
             this.locationType=type
             $('#interviewLocation').modal('show')

            },
           assignLocation(){
                let that=this
                var datas={}
                if(this.locationType=='unified'){
                    var ids=[]
                    for(var i=0;i<this.indexData.scheduleList.length;i++){
                        ids.push(this.indexData.scheduleList[i].id)
                    }
                    datas={
                        location:that.teacherAddress,
                        teacher_id:that.teacherLon,
                        schedule_ids:ids,
                        online_link:''
                    }
                }else{
                    datas={
                        location:that.aloneAddress,
                        teacher_id:that.teacherLon,
                        schedule_ids:[this.aloneData.id],
                        online_link:''
                    }
                }
                $.ajax({
                    url: '<?php echo $this->createUrlPtc("assignTeacherLocation") ?>',
                    type: "post",
                    dataType: 'json',
                    data: datas,
                    success: function(data) {
                        if(data.state=='success'){
                            resultTip({
                                msg: data.state
                            });
                            if(that.locationType=='unified'){
                                if(that.indexData.locationList[that.teacherLon]!=undefined){
                                    for(var i=0;i<that.indexData.scheduleList.length;i++){
                                        that.$set(that.indexData.locationList[that.teacherLon],that.indexData.scheduleList[i].key,{location:that.teacherAddress,online_link:''})
                                    }
                                }else{
                                    that.indexData.locationList[that.teacherLon]={}
                                    for(var i=0;i<that.indexData.scheduleList.length;i++){
                                        that.$set(that.indexData.locationList[that.teacherLon],that.indexData.scheduleList[i].key,{location:that.teacherAddress,online_link:''})
                                    }
                                }
                               
                            }else{
                                if(that.indexData.locationList[that.teacherLon]!=undefined){
                                    that.$set(that.indexData.locationList[that.teacherLon],that.aloneData.key, {location:that.aloneAddress,online_link:''})
                                }else{
                                    that.indexData.locationList[that.teacherLon]={}
                                    that.$set(that.indexData.locationList[that.teacherLon],that.aloneData.key, {location:that.aloneAddress,online_link:''})
                                }
                            }
                            $('#interviewLocation').modal('hide')
                        }else{
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
           },
           saveTime(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrlPtc("ptcInit") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        dept:'2',
                        "p_view_from":$('#startDate').val(),
                        // "froze_student_from":$('#endDate').val()

                    },
                    success: function(data) {
                        if(data.state=='success'){  
                            resultTip({
                                msg: data.state
                            });
                        }else{ 
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            savePlan(){
                let that=this
                this.Loading=true
                $.ajax({
                    url: '<?php echo $this->createUrlPtc("schedulePlanSave") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        plan_id:this.planId
                    },
                    success: function(data) {
                        if(data.state=='success'){  
                            resultTip({
                                msg: data.state
                            });
                            that.$set(that.indexData,'hasPlan', true)
                            that.$set(that,'preview', false)
                            that.$set(that,'assigned', data.data.assigned)
                            that.Loading=false
                            that.family_id = ''
                            location.reload()
                        }else{ 
                            resultTip({error: 'warning', msg:data.message});
                            that.Loading=false
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                        that.Loading=false
                    }
                })
            },
            cleanplan(data){
                if(data!='model'){
                    $('#delModal').modal('show')
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrlPtc("schedulePlanClean") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        day:this.indexData.targetDay
                    },
                    success: function(data) {
                        if(data.state=='success'){
                            resultTip({
                                msg: data.state
                            });
                            that.$set(that,'preview', false)
                            that.$set(that.indexData,'hasPlan', false)
                            $('#delModal').modal('hide')
                            that.family_id = ''
                            location.reload()
                        }else{ 
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            showReset(id){
                var show=false
                var child=this.indexData.parentsChoose[id]
                if(child!=undefined){
                    Object.keys(child).forEach(function(key){
                        if(child[key]==1 || child[key]==-1){
                            show=true
                            return
                        }
                    });
                }
                
                return show
            },
            reset(data){
                if(data!='model'){
                    this.resetId=data
                    $('#resetModal').modal('show')
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrlPtc("resetStudentPtc") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        dept: 2, // 部门
                        child_id: this.resetId // 孩子ID
                    },
                    success: function(data) {
                        if(data.state=='success'){  
                            Object.keys(that.indexData.parentsChoose[that.resetId]).forEach(function(key){
                                that.indexData.parentsChoose[that.resetId][key]=0
                            });
                            resultTip({
                                msg: data.state
                            });
                            $('#resetModal').modal('hide')
                        }else{ 
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            cellLess() {
                if (this.family_id) {
                    $('#table').css({width: 'auto'})
                    $('#table thead tr th:not(".font14")').hide()
                    $('#table tbody tr td:not(".filter")').hide()
                    $('#table tbody tr').find('td.blue').map((index, item) => {
                        $($('#table thead tr th')[item.cellIndex]).show()
                        $('#table tbody tr').map((_index, _item) => {
                            $($(_item).find('td')[item.cellIndex]).show()
                        })
                    })
                }
                else {
                    $('#table').css({width: '100%'})
                    $('#table thead tr th, #table tbody tr td').show()
                }
            },
            rowLess() {
                if(this.assigned.length!=0){
                    let maxLine=0
                    for(var key in this.assigned){
                        if(Object.values(this.assigned[key]).length>maxLine){
                            maxLine=Object.values(this.assigned[key]).length
                        }
                    }
                    $('#table tbody tr').map((index, item) => {
                        if (index >= maxLine) {
                            $(item).hide()
                        }
                    })
                }
            },
            noPtc(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrlPtc("studentsWithoutPtc") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        dept: 2, // 部门
                    },
                    success: function(data) {
                        if(data.state=='success'){  
                           that.noInterview=data.data
                            $('#withoutPtc').modal('show')
                        }else{ 
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            exportTable(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrlPtc("getPtcScheduleDay") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        dept: 2, // 部门
                    },
                    success: function(data) {
                        if(data.state=='success'){  
                           that.exportDays=data.data
                            $('#exportModal').modal('show')
                        }else{ 
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            exportData(day,index){
                let that=this
                $('#export'+index).show()
                $.ajax({
                    url: '<?php echo $this->createUrlPtc("getStudentsByDate") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        dept: 2, // 部门
                        date:day
                    },
                    success: function(data) {
                        if(data.state=='success'){  
                            let dataList=data.data
                            const filename =day+'.xlsx';
                            const ws_name = "SheetJS";
                            var exportDatas = [];
                            for(var i=0;i<dataList.ptcData.length;i++){
                                let childId=parseInt(dataList.ptcData[i].child_id)
                                if(dataList.childInfo[childId]){
                                    if(dataList.parentsData[dataList.childInfo[childId].mid]){
                                        exportDatas.push(
                                        {
                                        '日期':day,
                                        "会谈时间":dataList.ptcData[i].time,
                                        "学生姓名":dataList.childInfo[childId].name,
                                        "学生ID":childId,
                                        "班级":dataList.childInfo[childId].className,
                                        "参会人员":'母亲',
                                        "姓名":dataList.parentsData[dataList.childInfo[childId].mid].name,
                                        "电话":dataList.parentsData[dataList.childInfo[childId].mid].mphone,
                                        })
                                        if(dataList.parentsData[dataList.childInfo[childId].fid]){
                                            exportDatas.push(
                                            {
                                            '日期':'',
                                            "会谈时间":'',
                                            "学生姓名":'',
                                            "学生ID":'',
                                            "参会人员":'父亲',
                                            "姓名":dataList.parentsData[dataList.childInfo[childId].fid].name,
                                            "电话":dataList.parentsData[dataList.childInfo[childId].fid].mphone,
                                            })
                                        }
                                    }else{
                                        exportDatas.push(
                                            {
                                            '日期':day,
                                            "会谈时间":dataList.ptcData[i].time,
                                            "学生姓名":dataList.childInfo[childId].name,
                                            "学生ID":childId,
                                            "班级":dataList.childInfo[childId].className,
                                            "参会人员":'母亲',
                                            "姓名":dataList.parentsData[dataList.childInfo[childId].fid].name,
                                            "电话":dataList.parentsData[dataList.childInfo[childId].fid].mphone,
                                        })
                                        if(dataList.parentsData[dataList.childInfo[childId].mid]){
                                            exportDatas.push(
                                                {
                                                '日期':'',
                                                "会谈时间":'',
                                                "学生姓名":'',
                                                "学生ID":'',
                                                "参会人员":'父亲',
                                                "姓名":dataList.parentsData[dataList.childInfo[childId].mid].name,
                                                "电话":dataList.parentsData[dataList.childInfo[childId].mid].mphone,
                                            })
                                        }
                                    }
                                }
                            }
                            var wb=XLSX.utils.json_to_sheet(exportDatas,{
                                origin:'A1',// 从A1开始增加内容
                                header: ['日期', '会谈时间', '学生姓名','学生ID','班级','参会人员','姓名','电话'],
                            });
                            const workbook = XLSX.utils.book_new();
                            XLSX.utils.book_append_sheet(workbook, wb, ws_name);
                            const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                            const blob = new Blob([wbout], {type: 'application/octet-stream'});
                            let link = document.createElement('a');
                            link.href = URL.createObjectURL(blob);
                            link.download = filename;
                            link.click();
                            setTimeout(function() {
                                // 延时释放掉obj
                                URL.revokeObjectURL(link.href);
                                link.remove();
                                $('#export'+index).hide()
                            }, 500);
                        }else{ 
                            $('#export'+index).hide()
                            resultTip({error: 'warning', msg:data.message});
                        }
                    },
                    error:function(data){
                        $('#export'+index).hide()
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            myPTC(id){
                let url='<?php echo $this->createUrl('ptc/index', array('branchId' => $this->branchId)); ?>&showTab=MyPTC&my_ptc_teacher_id='+id
                window.open(url,'_blank');
            }
        }
    })
</script>