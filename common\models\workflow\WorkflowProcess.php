<?php

/**
 * This is the model class for table "ivy_workflow_process".
 *
 * The followings are the available columns in table 'ivy_workflow_process':
 * @property integer $process_id
 * @property integer $defination_id
 * @property integer $operation_id
 * @property string $process_desc
 * @property integer $current_node_index
 * @property integer $start_time
 * @property integer $finish_time
 * @property integer $state
 * @property integer $start_user
 * @property integer $updatetime
 */
class WorkflowProcess extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return WorkflowProcess the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_workflow_process';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('process_desc, start_time', 'required'),
			array('defination_id, operation_id, current_node_index, start_time, finish_time, state, start_user, updatetime', 'numerical', 'integerOnly'=>true),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('process_id, defination_id, operation_id, process_desc, current_node_index, start_time, finish_time, state, start_user, updatetime', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'user'=>array(self::BELONGS_TO,'User','start_user'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'process_id' => 'Process',
			'defination_id' => 'Defination',
			'operation_id' => 'Operation',
			'process_desc' => 'Process Desc',
			'current_node_index' => 'Current Node Index',
			'start_time' => 'Start Time',
			'finish_time' => 'Finish Time',
			'state' => 'State',
			'start_user' => 'Start User',
			'updatetime' => 'Updatetime',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('process_id',$this->process_id);
		$criteria->compare('defination_id',$this->defination_id);
		$criteria->compare('operation_id',$this->operation_id);
		$criteria->compare('process_desc',$this->process_desc,true);
		$criteria->compare('current_node_index',$this->current_node_index);
		$criteria->compare('start_time',$this->start_time);
		$criteria->compare('finish_time',$this->finish_time);
		$criteria->compare('state',$this->state);
		$criteria->compare('start_user',$this->start_user);
		$criteria->compare('updatetime',$this->updatetime);

		return new CActiveDataProvider(get_class($this), array(
			'criteria'=>$criteria,
		));
	}
}