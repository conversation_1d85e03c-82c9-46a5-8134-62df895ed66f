<?php
$crit = new CDbCriteria();
$crit->compare('schoolid', $this->branchId);
$crit->compare('classid', 0);
$crit->compare('status', '<100');
$noclassCount = ChildProfileBasic::model()->count($crit);

Yii::import('common.models.calendar.*');
Yii::import('application.components.policy.*');
$policyApi = new PolicyApi($this->branchId);
$availableYears = $policyApi->getAvailableYears($this->branchId);

if(isset($_GET['startyear']) && $_GET['startyear']){
    $startyear = $_GET['startyear'];
    $sModel = CalendarSchool::model()->findByAttributes(array('branchid'=>$this->branchId, 'startyear'=>$startyear));
}
else{
    $sModel = CalendarSchool::model()->findByAttributes(array('branchid'=>$this->branchId, 'is_selected'=>1));
    $startyear = $sModel->startyear;
}
$yid = $sModel->yid;
$relation = $sModel->is_selected ? 'studentCount' : 'reserveCount';
$classList = IvyClass::model()->with($relation)->getClassList($this->branchId, $yid);
global $paymentMappings;

if($policyApi->constantFlag == 'daystar'){
    $invoiceTypes = array(
        'reg' => array(
            $paymentMappings['dbFeeType'][FEETYPE_REGISTER],
            $paymentMappings['dbFeeType'][FEETYPE_ENTRANCE],
            $paymentMappings['dbFeeType'][FEETYPE_DEPOSIT]),
        'main' => array(
            $paymentMappings['dbFeeType'][FEETYPE_TUITION],
            $paymentMappings['dbFeeType'][FEETYPE_LUNCH],
            $paymentMappings['dbFeeType'][FEETYPE_SCHOOLBUS]),
        'other' => array(
            $paymentMappings['dbFeeType'][FEETYPE_UNIFORM],
            $paymentMappings['dbFeeType'][FEETYPE_ACCOUNTBALANCE],
            $paymentMappings['dbFeeType'][FEETYPE_SOFTDYNED]),
    );
}else{
    $invoiceTypes = array(
        'reg' => array(
            $paymentMappings['dbFeeType'][FEETYPE_MATERIAL],
            $paymentMappings['dbFeeType'][FEETYPE_DEPOSIT]),
        'main' => array(
            $paymentMappings['dbFeeType'][FEETYPE_TUITION],
            $paymentMappings['dbFeeType'][FEETYPE_LUNCH],
            $paymentMappings['dbFeeType'][FEETYPE_SCHOOLBUS]),
        'other' => array(
            $paymentMappings['dbFeeType'][FEETYPE_UNIFORM],
            $paymentMappings['dbFeeType'][FEETYPE_LIBCARD]),
            $paymentMappings['dbFeeType'][FEETYPE_ACCOUNTBALANCE],
    );
}

$langFile = 'payment_'.$policyApi->constantFlag;
$paymentKeyFlip = array_flip($paymentMappings['dbFeeType']);
foreach($paymentKeyFlip as $_key=>$_flag){
    $invoiceTitles[$_key] = Yii::t($langFile, $paymentMappings['titleFeeType'][$_flag]);
}
$export_title = array();
$export_title_by_type = array();
foreach ($invoiceTypes['reg'] as $_ptype){
    $export_title_type[] =  $_ptype;
    $export_title_by_type[$_ptype] =  Yii::t($langFile, $paymentMappings['titleFeeType'][$paymentKeyFlip[$_ptype]] );
    $export_title[] =  Yii::t($langFile, $paymentMappings['titleFeeType'][$paymentKeyFlip[$_ptype]] );
}
foreach ($invoiceTypes['main'] as $_ptype){
    $export_title_type[] =  $_ptype;
    $export_title_by_type[$_ptype] =  Yii::t($langFile, $paymentMappings['titleFeeType'][$paymentKeyFlip[$_ptype]] );
    $export_title[] =  Yii::t($langFile, $paymentMappings['titleFeeType'][$paymentKeyFlip[$_ptype]] );
}
$export_title_type[] = 'other';
$export_title[] = 'other';
$export_title_by_type['other'] =  Yii::t($langFile,'Other');;

?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-2">
            <div class="panel panel-default">
                <div class="panel-heading"><?php echo Yii::t('campus','Students List');?></div>
                <div class="list-group class-list-group">
                    <?php foreach($classList as $_class):?>
                        <a href="javascript:;" data-classid="<?php echo $_class->classid?>" onclick="loadGroupInvoice(this)" class="list-group-item"><?php echo $_class->title;?> <span class="badge"><?php echo $_class->$relation;?></span></a>
                    <?php endforeach;?>
                    <a href="javascript:;" data-classid="0" onclick="loadGroupInvoice(this)" class="list-group-item">
                        <?php echo Yii::t('message','No class assigned');?> <span class="badge"><?php echo $noclassCount;?></span></a>
                </div>
            </div>
        </div>
        <div class="col-md-10">
            <ul class="nav nav-pills mb10">
                <li class="active dropdown">
                    <a data-toggle="dropdown" href="javascript:;"><?php echo $startyear.' - '.($startyear+1)?> <span class="caret"></span></a>
                    <?php if(count($availableYears) > 1):?>
                    <ul class="dropdown-menu">
                        <?php foreach($availableYears as $year):?>
                        <li><a href="<?php echo $this->createUrl('index', array('startyear'=>$year))?>"><?php echo $year.' - '.($year+1)?></a></li>
                        <?php endforeach;?>
                    </ul>
                    <?php endif;?>
                </li>
                <li class='showBtn' style="display: none;">
                    <a class="btn btn-default btn-lg excell" download="'+text+'.xls" style="padding:8px 14px;border-radius:4px;" role="button" href="#"><span class="glyphicon glyphicon-log-out"></span> <?php echo Yii::t($langFile, '导出EXCEL');?></a>
                </li>
                <li class='showBtn' style="display: none;">
                    <button type="button" class="btn btn-default btn-lg excell1" download="'+text+'.xls" style="padding:8px 14px;border-radius:4px;" role="button" href="#"><span class="glyphicon glyphicon-log-out"></span> <?php echo Yii::t($langFile, '导出EXCEL');?>-Beta</button>
                </li>
            </ul>

            <div class="well well-sm">
                <span class="mr10">
                    <?php echo Yii::t('payment', $paymentMappings['invoiceStatus'][Invoice::STATS_PAID]);?>
                    <span class="label label-info">&nbsp;</span></span>
                <span class="mr10">
                    <?php echo Yii::t('payment', $paymentMappings['invoiceStatus'][Invoice::STATS_PARTIALLY_PAID]);?>
                    <span class="label label-warning">&nbsp;</span></span>
                <span class="mr10">
                    <?php echo Yii::t('payment', $paymentMappings['invoiceStatus'][Invoice::STATS_UNPAID]);?>
                    <span class="label label-danger">&nbsp;</span></span>
            </div>
            <div class="table-responsive">
                <table class="table table-bordered" id="main-zone" style="display: none;">
                    <thead>
                    <tr class="active">
                        <th width="12.7%"><h4><span class="glyphicon glyphicon-user"></span> <?php echo Yii::t($langFile,'Student');?></h4></th>
                        <?php foreach($invoiceTypes['reg'] as $_ptype):?>
                            <th width="9.7%" data-batchptype="<?php echo $_ptype?>">
                                <h5><?php echo Yii::t($langFile, $paymentMappings['titleFeeType'][$paymentKeyFlip[$_ptype]] );?></h5>
                                <p class="text-muted" id="support_<?php echo $_ptype;?>"></p>
                            </th>
                        <?php endforeach;?>
                        <?php foreach($invoiceTypes['main'] as $_ptype):?>
                            <th width="9.7%" data-batchptype="<?php echo $_ptype;?>">
                                <h5><?php echo Yii::t($langFile, $paymentMappings['titleFeeType'][$paymentKeyFlip[$_ptype]]);?></h5>
                                <p class="text-muted" id="support_<?php echo $_ptype;?>"></p>
                            </th>
                        <?php endforeach;?>
                        <th width="9.7%" data-batchptype="other"><h5><?php echo Yii::t($langFile,'Other');?></h5></th>
                    </tr>
                    </thead>
                    <tbody id="class-child-invoices"></tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script type="text/template" id="student-invoices-row-template">
    <tr data-studentid="<%= id %>">
        <th class="active">
            <h4>
            <a href="/child/invoice/unpaid?t=cash&childid=<%= id %>" target="_blank">
            <%- name %></a>
            </h4>
            <h5 title="<?php echo Yii::t('child','Date of Birth');?>" class="text-muted"><span class="glyphicon glyphicon-calendar"></span> <%- dob %></h5>
        </th>
        <?php foreach($invoiceTypes['reg'] as $_ptype):?>
            <td data-ptype="<?php echo $_ptype;?>">
            </td>
        <?php endforeach;?>
        <?php foreach($invoiceTypes['main'] as $_ptype):?>
            <td data-ptype="<?php echo $_ptype;?>">
            </td>
            <?php endforeach;?>
        <td data-ptype="other"></td>
    </tr>
</script>

<?php  $this->renderPartial('_batchInvoices');?>

<div id="add-invoice-template" style="display: none">
    <a href="javascript:;" onclick="addInvoice(this)" title="<?php echo Yii::t('invoice','create an invoice');?>">
        <span class="glyphicon glyphicon-plus-sign"></span>
    </a>
    <a href="javascript:;" onclick="showBlockTitle(this)" title="<?php echo Yii::t('invoice',
        'show invoice title');?>"><span class="glyphicon glyphicon-info-sign"></span></a>
</div>
<div id="batchadd-invoice-template" style="display: none">
    <a href="javascript:;" onclick="batchAdd(this)" title="<?php echo Yii::t('invoice',
        'create batch invoices');?>"><span class="glyphicon glyphicon-plus"></span></a>
    <a class="pull-right" href="javascript:;" onclick="showTitle(this)" title="<?php echo Yii::t('invoice',
        'show invoice title');?>"><span class="glyphicon glyphicon-info-sign"></span></a>
</div>
<script type="text/template" id="invoices-item-template">
    <div class="dropdown mb5 excelHidden1" data-invoiceid=<%= invoice_id %> data-info="<%- title%> <%- amount%>">
        <a title="<%- title %>" data-toggle="dropdown" href="javascript:;">
            <span class="label <% print(invoiceCss[status]); %>">
            <span class="extra-title" style="font-weight: normal"><small><%- title %></small></span>
            <%- amount %> <span class="caret"></span></span>
<!--            <span style="display: none" id="<%= payment_type %>_<%= invoice_id %>"><%- title%> <%- amount%></span>-->
        </a>
        <div class="dropdown-menu excelHidden">
            <div class="p10">
                <h5><%- title %></h5>
                <p class="text-muted"><%- memo %></p>
                <p><a class="btn btn-primary" href="javascript:;" data-invoiceid="<%= invoice_id %>" onclick="invoiceManage(this)"><?php echo Yii::t('global', 'View Detail');?></a></p>
            </div>
        </div>
    </div>
</script>

<script>


// 导出
    $(".excell").click(function () {
         if(!$("#main-zone").is(":hidden")) {
             $(".excelHidden").remove()
             return ExcellentExport.excel(this, 'main-zone', 'Sheet Name Here');
         } else {
             return false;
         }
    });

    $(".excell1").click(function(){
        var export_title = <?php echo CJSON::encode($export_title);?>;
        var export_title_type = <?php echo CJSON::encode($export_title_type);?>;
        var export_title_by_type = <?php echo CJSON::encode($export_title_by_type);?>;
        const filename = $(".excell").attr('download');
        const ws_name = "SheetJS";
        var exportDatas = [];
        var merges = [{
            s: {r: 0, c: 0},
            e: {r: 1, c: 0}
        }];//需要合并的单元格
        //表头下的付款未付款数量
        var header_data = {'Student':''};
        var header = ['Student'];
        var cols = [{wpx:200}];//列宽
        var rows = [{hpt:25},{hpt:25},{hpt:25}];//行高
        var hearder_text = {'Student':'学生'};
        for (k in export_title_type){
            var d = export_title_type[k];
            hearder_text[d] = export_title_by_type[d]
        }
        exportDatas.push(hearder_text)
        for (k in export_title_type){
            var d = export_title_type[k];
            if(d != 'other'){
                header_data[d] = $("#support_"+export_title_type[k]).html();
            }else{
                header_data['other'] = '';
            }
            header.push(d);
            cols.push({wpx:200})
        }
        exportDatas.push(header_data)

        //表头处理 合并 共2行
        for (var i=0; i<export_title.length;i++){
            if(!$("#support_"+export_title_type[i]).html()){
                merges.push({
                    s: {r: 0, c: i+1},
                    e: {r: 1, c: i+1}
                })
            }
        }
        var child_all_data = [];
        var child_num = 0
            for (child_id in childInvoicesStudents){
                var child_base_data = [];
                child_base_data[child_id] = [
                    {'Student':childInvoicesStudents[child_id]['name']}, [], [], [], [], [], [], [], [], [], [],
                ]
            for (k in export_title_type){
                var ptype = export_title_type[k]
                var ptype_num = 0
                d1 = $("tr[data-studentid='"+child_id+"'] td[data-ptype='"+ptype+"']").find('.excelHidden1').each(function(i){
                    if(ptype_num === 0 || ptype_num < i){
                        ptype_num++;
                    }
                    child_base_data[child_id][i][ptype] =  $(this).attr('data-info');
                })
            }
            child_all_data.push(child_base_data);
            child_num++;
        }
        console.log(child_all_data)
        for (k in child_all_data){
            for (const child_id in child_all_data[k]) {
                for (const child_key in child_all_data[k][child_id]) {
                    if(Object.keys(child_all_data[k][child_id][child_key]).length > 0){
                        child_all_data[k][child_id][child_key]['Student'] = childInvoicesStudents[child_id]['name']
                        exportDatas.push(child_all_data[k][child_id][child_key])
                        rows.push({hpt:25})
                    }
                }
            }
        }
        console.log(exportDatas)
        var wb = XLSX.utils.json_to_sheet(exportDatas,{
            origin:'A1',// 从A1开始增加内容
            header: header,
        });
        // wb['!merges'] = merges;//需要合并的单元格
        wb['!rows'] = rows;
        wb['!cols'] = cols;
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, wb, ws_name);
        const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
        const blob = new Blob([wbout], {type: 'application/octet-stream'});
        let link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        link.click();
        setTimeout(function() {
            // 延时释放掉obj
            URL.revokeObjectURL(link.href);
            link.remove();
        }, 500);
    });
    var loadGroupInvoice;
    var renderTableSchema = null;
    var renderTableInvoices = null;
    var showSupport = null;
    var childInvoices = null;
    var childInvoicesExport = null;
    var studentRow = _.template($('#student-invoices-row-template').html());
    var invoiceRow = _.template($('#invoices-item-template').html());
    var invoiceTypes = <?php echo CJSON::encode($invoiceTypes);?>;
    var invoiceTypeTitles = <?php echo CJSON::encode($invoiceTitles);?>;
    var busy = 0;

    var currentCalendarId = <?php echo $yid;?>;
    var currentClassId = null;

    var addInvoice = null; //添加单个孩子账单
    var invoiceManage = null;

    var invoiceCss = {
        10: 'label-danger',
        20: 'label-info',
        30: 'label-warning',
        40: 'label-default',
        77: 'label-default'
    };

    var supportTips;

    var batchAdd = null;
    $(function(){
        $.datepicker.setDefaults($.datepicker.regional['<?php echo (Yii::app()->language == 'en_us')? '': 'zh-CN'?>']);

        $('table#main-zone thead').find('th[data-batchptype] h4, th[data-batchptype] h5').append($('#batchadd-invoice-template').html());

        loadGroupInvoice = function(obj){
            if(busy) return;
            $('.showBtn').show();
            busy = 1;
            $('#main-zone').hide();
            var _classid = $(obj).data('classid');
            currentClassId = _classid;
            $(obj).siblings().removeClass('active');
            $(obj).addClass('active');

            $.post('<?php echo $this->createUrl('getGroupInvoice')?>', {classid: _classid, calendar: currentCalendarId}, function(data){
                if (data.state === 'success'){
                    $('#main-zone').show();
                    childInvoices = data.data;
                    childInvoicesStudents = data.data.students;
                    renderTableSchema();
                    renderTableInvoices(childInvoices.invoices);
                }
                else {
                    alert(data.message);
                }
                busy = 0;
            }, 'json');
            $('.excell').attr('download',$(obj).text()+'.xls')
        };

        //画表体
        renderTableSchema = function(){
            $('#class-child-invoices').empty();
            var _trs = '';
            _.each(childInvoices.students, function(item,index){
                _trs += studentRow(item);
            });
            $('#class-child-invoices').append(_trs);

            $('#class-child-invoices').find('td[data-ptype]').append( $('#add-invoice-template').html() );
        }

        //画表格内容
        renderTableInvoices = function(invoices){
            supportTips = {
                registration: {10:0, 20:0, 30:0},
                entrance: {10:0, 20:0, 30:0},
                deposit: {10:0, 20:0, 30:0},
                tuition: {10:0, 20:0, 30:0},
                lunch: {10:0, 20:0, 30:0},
                bus: {10:0, 20:0, 30:0},
                uniform: {10:0, 20:0, 30:0},
                softdyned: {10:0, 20:0, 30:0}
            };

            _.each(invoices, function(_invoice,index){
                if (supportTips[_invoice.payment_type] == undefined) {
                    return;
                }
                var _tr = $('#class-child-invoices').find('tr[data-studentid|="'+_invoice.childid+'"]');
                var _row = invoiceRow(_invoice);
                if(_.contains(invoiceTypes.reg,_invoice.payment_type)){
                    _tr.find('td[data-ptype|="'+_invoice.payment_type+'"]').append(_row);
                    supportTips[_invoice.payment_type][_invoice.status] += 1;
                }else if(_.contains(invoiceTypes.other,_invoice.payment_type)){
                    _tr.find('td[data-ptype|="other"]').append(_row);
                }else{
                    var _td = _tr.find('td[data-ptype|="'+_invoice.payment_type+'"]');
                    _td.append(_row);
                    supportTips[_invoice.payment_type][_invoice.status] += 1;
                }
            })

            showSupport();
        }

        showSupport = function() {
            _.each(supportTips, function (value, key) {
                var tips = '';
                if(value[10] > 0){
                    tips += '未付款: '+value[10]+'; ';
                }
                if(value[20] > 0){
                    tips += '已付款: '+value[20]+'; ';
                }
                if(value[30] > 0){
                    tips += '部分付: '+value[30]+'; ';
                }

                if(tips) {
                    $('#support_'+key).text(tips);
                }
            })
        }

        //批量开账单
        batchAdd = function(obj){
            $('#J_fail_info').remove();
            var i_payment_type = $(obj).parents('th').data('batchptype');
            // if (i_payment_type == 'tuition') {
            //     alert('批量开学费暂不支持');
            //     return;
            // }
            // if (i_payment_type == 'lunch') {
            //     alert('批量餐费暂不支持');
            //     return;
            // }
            // if (i_payment_type == 'bus') {
            //     alert('批量校车费暂不支持');
            //     return;
            // }
            if(_.contains(invoiceTypes['main'], i_payment_type) && !currentClassId){
                alert('<?php echo Yii::t('message', 'Invoice of tuition, lunch and schoolbus cannot be issued for students not assigned to a class.')?>');
                return;
            }
            $('#batchInvoiceModal').modal({backdrop: 'static'});
            $('.J_check_all').attr('checked', false);
//            var i_semster = $(obj).parents('th').data('semester');
//            if(_.isUndefined(i_semster)) i_semster = null;
            iniForm(i_payment_type, null);
            head.Util.checkAll();
        }

        //单个孩子开账单
        addInvoice = function(obj){
            $('#J_fail_info').remove();
            var i_payment_type = $(obj).parents('td').data('ptype');
            var i_childid = $(obj).parents('tr').data('studentid');
//            var i_semster = $(obj).parents('td').data('semester');
//            if(_.isUndefined(i_semster)) i_semster = null;

            if(_.contains(invoiceTypes['main'], i_payment_type) && !currentClassId){
                alert('<?php echo Yii::t('message', 'Invoice of tuition, lunch and schoolbus cannot be issued for students not assigned to a class.')?>');
                return;
            }
            $('#batchInvoiceModal').modal({backdrop: 'static'});
            iniForm(i_payment_type, i_childid);
        }

        invoiceManage = function(obj){
            var url = '<?php echo $this->createUrl('//mfinance/invoice/manage', array('invoiceid'=>'__invoiceid'));?>';
            url = url.replace('__invoiceid', parseInt($(obj).data('invoiceid')));
            window.open(url, '_blank');
        }

        //添加账单成功的回调
        cb = function(data){
            renderTableInvoices(data);
            window.setTimeout(function(){
                $('#batchInvoiceModal').modal('hide');
            }, 1000);
        }
    })
    function showTitle(obj){
        var i_payment_type = $(obj).parents('th').data('batchptype');
        $('td[data-ptype|="'+i_payment_type+'"]').find('span.extra-title').toggle();
    }
    function showBlockTitle(obj){
        var td = $(obj).parents('td');
        var i_payment_type = td.data('ptype');
        td.find('span.extra-title').toggle();
    }
</script>

<style>
    .extra-title{
        display: none;
    }
    #class-child-invoices td .label{
        font-size: 100%;
        line-height: 2;
    }
</style>
