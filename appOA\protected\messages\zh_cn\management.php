<?php
return array (
  'Unpaid bills' => '存在未付款账单',
  'Email has been sent' => '邮件已发送',
  "The email can't be sent" => '邮件发送失败',
  'The bill has been voided' => '账单已作废',
  'Payment failure' => '付款失败',
  'Payment success' => '付款成功',
  'Confirm payment' => '确认已支付',
  'Please scan the authorization code first ' => '请先扫描授权码',
  'Creating order' => '正在生成订单',
  'Date of received payment' => '到帐日期',
  'Payment and payment options' => '付款及付款方式',
  'Arrange interview' => '安排面试',
  'Workflow' => '面试结果',
  'The student has been admitted' => '转为正式学生',
  'The student has been admitteds' => '学生已经转移到正式学生中',
  'Admission in progress' => '转移',
  'Interview passed' => '通过面试',
  'Application Fee' => '申请费',
  'Status of Application Fee Payment' => '申请费缴费状态',
  'Acceptance' => '录取',


  'Interview failed' => '面试未通过',
  'Check the interview result' => '查看结果',
  'Interview result' => '面试结果',
  'Assessment Result' => '面试结果',
  'No interview results yet' => '未有面试结果',
  'No interview arrangement yet' => '未安排面试',
  'Curriculum ' => '课程',
  'Management' => '管理',
  'No billing' => '账单未开',
  'Bill payment' => '账单缴费',
  'Please make payment' => '缴费',
  'WeChat credit card payment' => '微信刷卡支付',
  'POS payment' => '刷卡支付',
  'Bank transfer' => '银行转账',
  'Generate POS order ' => '生成POS订单',
  'Transaction sequence number' => '交易流水号',
  'Bank receipt number' => '银行回单号',
  'Please scan the authorization code' => '扫描授权码',
  'Abnormal recording' => '异常记录',
  'View all interviews this month' => '查看本月所有面试情况',
  'Student information' => '学生基本信息',
  'Bill error' => '账单错误',
  'Payment failed' => '支付失败',
  'Bill is not legal' => '账单不合法',
  'No bill' => '暂无可支付账单',
  'Application Fee' => '申请费',
  'The teacher you chose does not work for this campus' => '您选的老师有不属于本校园',
  'The interview teacher is not available' => '面试老师不能为空',
  'Date of preferred Admission not yet available' => '入学日期暂无校历',
  //'Workflow' => '工作流',
);
