<?php
Yii::import('zii.widgets.grid.CGridView');
class BsCGridView extends CGridView
{
    public $cssFile = false;
    public $htmlOptions=array('class'=>'table-responsive');
    public $template="{items}\n{pager}";
    public $rowCssClassExpression = "";
    public $colgroups = array();
    public $itemsCssClass = 'table table-hover';
    public $pagerCssClass = 'bs-pager';
    public $pager = array('class'=>'BsCLinkPager');

    public function init()
    {
        parent::init();

        if(!$this->afterAjaxUpdate){
            $this->afterAjaxUpdate='js:function(){head.Util.aDialog();}';
        }
    }

    public function renderItems()
	{
		if($this->dataProvider->getItemCount()>0 || $this->showTableOnEmpty)
		{
			echo "<table valign=\"middle\" class=\"{$this->itemsCssClass}\" >\n";
            $this->renderColgroup();
			$this->renderTableHeader();
			ob_start();
			$this->renderTableBody();
			$body=ob_get_clean();
			$this->renderTableFooter();
			echo $body; // TFOOT must appear before TBODY according to the standard.
			echo "</table>";
		}
		else
			$this->renderEmptyText();
	}
       
    public function renderColgroup()
    {
        if ( !empty($this->colgroups) ){
            foreach ($this->colgroups as $colg){
                echo CHtml::openTag('colgroup', isset($colg['htmlOptions'])?$colg['htmlOptions']:array());
                foreach ($colg['colwidth'] as $col){
                    if ($col){
                        $colOptions['width'] = $col;
                    }
                    echo CHtml::tag('col', $colOptions);
                }
            }
            echo CHtml::closeTag('colgroup');
        }
    }
}