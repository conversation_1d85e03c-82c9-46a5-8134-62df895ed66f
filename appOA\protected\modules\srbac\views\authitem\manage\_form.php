<?php
/**
 * _form.php
 *
 * <AUTHOR> <<EMAIL>>
 * @link http://code.google.com/p/srbac/
 */

/**
 * The create new auth item form.
 *
 * <AUTHOR> Soldatos <<EMAIL>>
 * @package srbac.views.authitem.manage
 * @since 1.0.0
 */
?>

<?php echo SHtml::beginForm('', 'post', array('class'=>'J_ajaxForm form-horizontal')); ?>
<div class="pop_cont">
    <div class="form-group">
        <?php echo SHtml::activeLabelEx($model,'name', array('class'=>'col-xs-2 control-label')); ?>
        <div class="col-xs-10">
            <?php echo SHtml::activeTextField($model,'name',
                $model->name == Helper::findModule('srbac')->superUser ?
                    array('size'=>20,'disabled'=>"disabled", 'class'=>'form-control'): array('size'=>20, 'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <?php echo SHtml::activeLabelEx($model,'type', array('class'=>'col-xs-2 control-label')); ?>
        <div class="col-xs-10">
            <?php echo SHtml::activeDropDownList($model,'type',AuthItem::$TYPES,
                $model->name == Helper::findModule('srbac')->superUser || $update
                    ? array('disabled'=>"disabled", 'class'=>'form-control'): array( 'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <?php echo SHtml::activeLabelEx($model,'description', array('class'=>'col-xs-2 control-label')); ?>
        <div class="col-xs-10">
            <?php echo SHtml::activeTextArea($model,'description',array('rows'=>3, 'cols'=>20, 'class'=>'form-control')); ?>
        </div>
    </div>
    <?php
    if(Yii::app()->user->hasFlash('updateSuccess')):
        $tip = Yii::app()->user->getFlash('updateSuccess');
        echo '<script>resultTip({msg:"'.$tip.'"})</script>';
    elseif(Yii::app()->user->hasFlash('updateError')):
        $tip = Yii::app()->user->getFlash('updateError');
        echo '<script>resultTip({error:1, msg:"'.$tip.'"})</script>';
    endif;
    ?>
    <div class="form-group">
        <?php echo SHtml::activeLabelEx($model,'bizrule', array('class'=>'col-xs-2 control-label')); ?>
        <div class="col-xs-10">
            <?php echo SHtml::activeTextArea($model,'bizrule',
                $model->name == Helper::findModule('srbac')->superUser ?
                    array('rows'=>3, 'cols'=>20, 'disabled'=>'disabled', 'class'=>'form-control'):array('rows'=>3, 'cols'=>20, 'class'=>'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <?php echo SHtml::activeLabelEx($model,'data', array('class'=>'col-xs-2 control-label')); ?>
        <div class="col-xs-10">
            <?php echo SHtml::activeTextField($model,'data',
                $model->name == Helper::findModule('srbac')->superUser ?
                    array('disabled'=>'disabled','size'=>30, 'class'=>'form-control') : array('size'=>30, 'class'=>'form-control')); ?>
        </div>
    </div>
    <?php echo SHtml::hiddenField("oldName",$model->name); ?>
</div>
<div class="pop_bottom">
    <button id="J_dialog_close" type="button" class="btn btn-default pull-right"><?php echo Yii::t('global','Cancel');?></button>
    <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global','Submit');?></button>
</div>
<?php echo SHtml::endForm(); ?>
