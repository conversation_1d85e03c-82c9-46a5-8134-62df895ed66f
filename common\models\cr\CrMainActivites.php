<?php

/**
 * This is the model class for table "ivy_cr_main_activites".
 *
 * The followings are the available columns in table 'ivy_cr_main_activites':
 * @property integer $id
 * @property integer $yid
 * @property string $schoolid
 * @property integer $weeknumber
 * @property integer $totaldeposits
 * @property integer $inquiries
 * @property integer $track
 * @property string $accontent
 * @property string $comments
 * @property integer $status
 * @property integer $time
 */
class CrMainActivites extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_cr_main_activites';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('yid, schoolid, weeknumber', 'required'),
			array('yid, weeknumber, totaldeposits, inquiries, track, status, time', 'numerical', 'integerOnly'=>true),
			array('schoolid', 'length', 'max'=>255),
			array('accontent, comments', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, yid, schoolid, weeknumber, totaldeposits, inquiries, track, accontent, comments, status, time', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'yid' => 'Yid',
			'schoolid' => 'Schoolid',
			'weeknumber' => 'Weeknumber',
			'totaldeposits' => 'Totaldeposits',
			'inquiries' => 'Inquiries',
			'track' => 'Track',
			'accontent' => Yii::t("campusreport",'accontent'),
			'comments' => Yii::t('campusreport','comments'),
			'status' => 'Status',
			'time' => 'Time',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('weeknumber',$this->weeknumber);
		$criteria->compare('totaldeposits',$this->totaldeposits);
		$criteria->compare('inquiries',$this->inquiries);
		$criteria->compare('track',$this->track);
		$criteria->compare('accontent',$this->accontent,true);
		$criteria->compare('comments',$this->comments,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('time',$this->time);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return CrMainActivites the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
