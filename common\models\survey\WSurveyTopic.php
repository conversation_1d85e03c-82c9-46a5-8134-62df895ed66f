<?php

/**
 * This is the model class for table "ivy_survey_topic".
 *
 * The followings are the available columns in table 'ivy_survey_topic':
 * @property integer $id
 * @property string $title_cn
 * @property string $title_en
 * @property integer $template_id
 * @property integer $option_group
 * @property integer $display_order
 * @property string $binary_flag
 * @property string $schoolid
 * @property integer $status
 * @property integer $update_user
 * @property integer $update_time
 */
class WSurveyTopic extends CActiveRecord
{
	public $topicNum = 0;
	/**
	 * Returns the static model of the specified AR class.
	 * @return SurveyTopic the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_survey_topic';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
				array('template_id, option_group, display_order, status, update_user, update_time', 'numerical', 'integerOnly'=>true),
				array('title_cn, title_en', 'length', 'max'=>1024),
				array('binary_flag', 'length', 'max'=>25),
				array('schoolid', 'length', 'max'=>255),
				// The following rule is used by search().
				// Please remove those attributes that should not be searched.
				array('id, title_cn, title_en, template_id, option_group, display_order, binary_flag, schoolid, status, update_user, update_time', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
				'optionGroup'=>array(self::BELONGS_TO,'WSurveyOptionGroup','option_group'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
				'id' => 'ID',
				'title_cn' => 'Title Cn',
				'title_en' => 'Title En',
				'template_id' => 'Template',
				'option_group' => 'Option Group',
				'display_order' => 'Display Order',
				'binary_flag' => 'Binary Flag',
				'schoolid' => 'Schoolid',
				'status' => 'Status',
				'update_user' => 'Update User',
				'update_time' => 'Update Time',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('title_cn',$this->title_cn,true);
		$criteria->compare('title_en',$this->title_en,true);
		$criteria->compare('template_id',$this->template_id);
		$criteria->compare('option_group',$this->option_group);
		$criteria->compare('display_order',$this->display_order);
		$criteria->compare('binary_flag',$this->binary_flag,true);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('update_user',$this->update_user);
		$criteria->compare('update_time',$this->update_time);

		return new CActiveDataProvider($this, array(
				'criteria'=>$criteria,
		));
	}

    /**
     * @param $templateId
     * @return array
     * 去某一个调查问卷的题目数据
     */
    public function getSurveyTopics($templateId) {
        $templateId = intval($templateId);
        $cacheId = sprintf('survey-topics-data-%d', $templateId);

        $topicData = Yii::app()->cache->get($cacheId);

        if($topicData === false) {
            $crit = new CDbCriteria();
            $crit->compare('template_id', $templateId);
            $crit->compare('status', 0);
            $crit->order = 'display_order ASC';
            $topics = WSurveyTopic::model()->findAll($crit);
            foreach($topics as $topic){
				$option_cat = isset($topic->optionGroup) && is_object($topic->optionGroup) ? $topic->optionGroup->option_cat : 0;
                $topicData[] = array_merge($topic->getAttributes(), array('cat'=>$option_cat));
            }

            Yii::app()->cache->set($cacheId, $topicData, 0,
                new CDbCacheDependency("SELECT MAX(update_time) FROM ".
                    $this->tableName().
                    " where template_id=".$templateId) );
        }

        return $topicData;
    }

    public function getTopicOptions($templateId) {

        $groupIds = array();
        $rows = Yii::app()->db->createCommand()
            ->selectDistinct('option_group')
            ->from($this->tableName())
            ->where('template_id=:template_id', array(':template_id'=>$templateId))
            ->queryAll();

        foreach($rows as $row) {
            $groupIds[$row['option_group']] = $row['option_group'];
        }
        unset($groupIds[0]);

        if(!empty($groupIds)) {
            $options = array();
            $crit = new CDbCriteria();
            $crit->compare('t.id', array_keys($groupIds));
            $optionGroupModels = WSurveyOptionGroup::model()->with('optionsWithKey')->findAll($crit);
            foreach($optionGroupModels as $group) {
                $_orders = unserialize($group->option_order);
                $options[$group->id] = array();
                foreach($_orders as $_id) {
                    $options[$group->id][] = $group->optionsWithKey[$_id]->getAttributes(
                        array('title_cn','title_en', 'option_value')
                    );
                }
            }
            return $options;
        }
    }

    public function getTitle($status = false)
    {
        if($status){
            $title = ( Yii::app()->language == "zh_cn" ) ? $this->title_cn . ' ' . $this->title_en : $this->title_en . ' ' . $this->title_cn;
        }else{
            $title = ( Yii::app()->language == "zh_cn" ) ? $this->title_cn : $this->title_en;
        }
        return $title;
    }

    public function getNumberconfig($survey_id)
    {
        $config = array(
            47 => array(1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,'a','b','c','d','e','f','g','h','i','j',28,'a','b','c','d','e','f','g','h','i','j','k','l','m',29,'a','b','c','d','e','f','g','h','i','j','k','l','m',30,'a','b','c','d','e','f','g','h','i','j','k',31),
            48 => array(1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,'a','b','c','d','e','f','g','h','i','j',28,'a','b','c','d','e','f','g','h','i','j','k','l','m',29,'a','b','c','d','e','f','g','h','i','j','k','l','m',30,'a','b','c','d','e','f','g','h','i','j','k',31),
            49 => array(1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,'a','b','c','d','e','f','g','h','i','j',28,'a','b','c','d','e','f','g','h','i','j','k','l','m',29,'a','b','c','d','e','f','g','h','i','j','k','l','m',30,'a','b','c','d','e','f','g','h','i','j','k',31),
        );
        if (!isset($config[$survey_id])) {
            return array();
        }
        return $config[$survey_id];
    }

}