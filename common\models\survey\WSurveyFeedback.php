<?php

/**
 * This is the model class for table "ivy_survey_feedback".
 *
 * The followings are the available columns in table 'ivy_survey_feedback':
 * @property integer $id
 * @property integer $survey_id
 * @property integer $classid
 * @property integer $childid
 * @property string $schoolid
 * @property integer $is_fb
 * @property integer $followup
 * @property integer $open2school
 * @property integer $status
 * @property integer $fb_user
 * @property integer $fb_time
 * @property integer $update_user
 * @property integer $update_time
 */
class WSurveyFeedback extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return WSurveyFeedback the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_survey_feedback';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('survey_id, classid, childid, is_fb, followup, open2school, status, fb_user, fb_time, update_user, update_time', 'numerical', 'integerOnly'=>true),
			array('schoolid', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, survey_id, classid, childid, schoolid, is_fb, followup, open2school, status, fb_user, fb_time, update_user, update_time', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'survey_id' => 'Survey',
			'classid' => 'Classid',
			'childid' => 'Childid',
			'schoolid' => 'Schoolid',
			'is_fb' => 'Is Fb',
			'followup' => 'Followup',
			'open2school' => 'Open2school',
			'status' => 'Status',
			'fb_user' => 'Fb User',
			'fb_time' => 'Fb Time',
			'update_user' => 'Update User',
			'update_time' => 'Update Time',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('survey_id',$this->survey_id);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('is_fb',$this->is_fb);
		$criteria->compare('followup',$this->followup);
		$criteria->compare('open2school',$this->open2school);
		$criteria->compare('status',$this->status);
		$criteria->compare('fb_user',$this->fb_user);
		$criteria->compare('fb_time',$this->fb_time);
		$criteria->compare('update_user',$this->update_user);
		$criteria->compare('update_time',$this->update_time);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

    public function getFeedBackStat($surveyId=0, $branchId=null)
    {
        $crit = new CDbCriteria();
        $crit->compare('survey_id', $surveyId);
        $crit->compare('schoolid', $branchId);
        $num1 = self::model()->count($crit);
        $crit->compare('is_fb', 1);
        $num2 = self::model()->count($crit);
        return array($num1, $num2);
    }
}