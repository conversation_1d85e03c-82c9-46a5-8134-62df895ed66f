<?php

/**
 * This is the model class for table "ivy_child_profile_basic".
 *
 * The followings are the available columns in table 'ivy_child_profile_basic':
 * @property integer $childid
 * @property string $barcode
 * @property integer $birthday
 * @property string $birthday_search
 * @property string $is_legel_cn_name
 * @property string $name_cn
 * @property string $first_name_en
 * @property string $middle_name_en
 * @property string $last_name_en
 * @property string $nick
 * @property integer $gender
 * @property string $country
 * @property string $lang
 * @property string $photo
 * @property string $schoolid
 * @property string $next_schoolid
 * @property integer $next_yid
 * @property integer $classid
 * @property integer $fid
 * @property integer $mid
 * @property integer $updated_timestamp
 * @property integer $created_timestamp
 * @property integer $create_uid
 * @property string $access_pass
 * @property double $credit
 * @property string $identity
 * @property string $est_enter_date
 * @property string $enter_date
 * @property string $compound
 * @property integer $seniority
 * @property string $family_id
 * @property string $admission_id
 * @property string $first_startyear
 * @property integer $label
 */
class ChildProfileBasic extends CActiveRecord {

    public $uploadPhoto;
    public $statusList;
    public $exportReports;

    public $englishName;
    public $fatherName;
    public $fatherEmail;
    public $fatherMobile;
    public $motherName;
    public $motherEmail;
    public $motherMobile;

    const STATS_REGISTERED = 0;        	//注册完成的默认值
    const STATS_ACTIVE_WAITING = 10;    //有效状态
    const STATS_ACTIVE = 20;            //有效状态
    const STATS_GRADUATED = 100;        //已毕业
    const STATS_DROPPINGOUT = 888;      //退学处理中
    const STATS_DROPOUT = 999;          //已退学

    /**
     * Returns the static model of the specified AR class.
     * @return ChildProfileBasic the static model class
     */
    public static function model($className = __CLASS__) {
        return parent::model($className);
    }

    /**
     * @return string the associated database table name
     */
    public function tableName() {
        return 'ivy_child_profile_basic';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules() {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('birthday_search, country, schoolid, updated_timestamp, created_timestamp, create_uid', 'required'),
            array('birthday, gender, next_yid, classid, fid, mid, updated_timestamp, created_timestamp, create_uid, est_enter_date, enter_date, seniority', 'numerical', 'integerOnly' => true),
            array('credit, birthday', 'numerical'),
            array('barcode, name_cn, is_legel_cn_name, first_name_en, middle_name_en, last_name_en, nick, country, lang, photo, schoolid, identity, invoice_title, compound, educational_id', 'length', 'max' => 255),
            array('access_pass', 'length', 'max' => 50),
            array('family_id', 'length', 'max' => 32),
            // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            array('childid, barcode, first_startyear, birthday, birthday_search, name_cn, first_name_en, last_name_en, nick, gender, country, lang, photo, schoolid, next_schoolid, next_yid, classid, fid, mid, updated_timestamp, created_timestamp, create_uid, access_pass, credit, identity, educational_id', 'safe', 'on' => 'search'),
            array("uploadPhoto", "file", "types" => "jpg, gif, png, jpeg", "allowEmpty" => true ,'except'=>'wechat'),
            array("birthday_search", "checkDate", "on" => "update"),
            array("first_name_en", "allEmpty", "on" => "update"),

            array('first_name_en, gender, lang, schoolid, est_enter_date', 'required', 'on'=>'regChild'),
            array('barcode','unique'),
            array('barcode', 'length', 'max' => 32, 'min' => 16, 'on'=>'update'),

            array('first_name_en, lang, country', 'required', 'on'=>'wechat'),
        );
    }

    /**
     * 检查日期是否合法
     * @return	Object		Description
     */
    public function checkDate() {
        if (!$this->hasErrors()) {
            if (!strtotime($this->birthday_search)) {
                $this->addError("birthday_search", Yii::t("userinfo", "Please input a valid birthday."));
            }
        }
    }

    public function allEmpty($attribute, $params) {
        if (!$this->hasErrors()) {
            if (trim($this->name_cn) == "" && trim($this->first_name_en) == "") {
                $this->addError("first_name_en", Yii::t("userinfo", "Please input either Chinese Name or First Name"));
            }
        }
    }

    /**
     * @return array relational rules.
     */
    public function relations() {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
            'access' => array(self::HAS_ONE, "ChildAccess", "childid"),
            'misc' => array(self::HAS_ONE, "ChildMisc", "childid"),
            'homeaddr' => array(self::HAS_ONE, 'HomeAddress', 'childid'),

            //下面这个名字是谁加的？辞不达意，把我要用的主键都给用了
            'stat' => array(self::BELONGS_TO, 'ChildClassLink', 'childid'),
            'nextYear' => array(self::HAS_ONE, 'ChildReserve', array('childid'=>'childid')),
            'invoices' => array(self::HAS_MANY, 'Invoice', array('childid'=>'childid')),
            'paid_tuition' => array(self::HAS_ONE, 'Invoice', array('childid'=>'childid'), 'condition'=>'paid_tuition.status=20 and paid_tuition.schoolid=t.schoolid and paid_tuition.payment_type="tuition"'),
            'constant' => array(self::HAS_ONE, 'ChildConstantTuition', array('childid'=>'childid')),
            'bindDiscount' => array(self::HAS_ONE, 'ChildDiscountLink', array('childid'=>'childid'), 'with'=>'DiscountSchool'),
            'ivyclass' => array(self::BELONGS_TO, 'IvyClass', 'classid'),
            'school' => array(self::BELONGS_TO, 'Branch', 'schoolid'),
            'creditList' => array(self::HAS_MANY, 'ChildCredit', 'childid'),
            'childLabel'=>array(self::HAS_ONE,'ChildLabel','childid'),
            'invoiceTransaction' => array(self::HAS_MANY, 'InvoiceTransaction', 'childid'),
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels() {
        return array(
            'childid' => 'Childid',
            'barcode' => Yii::t("labels", 'Barcode'),
            'birthday' => Yii::t("labels", 'Date of Birth'),
            'birthday_search' => Yii::t("labels", 'Date of Birth'),
            'name_cn' => Yii::t("labels", 'Chinese Name'),
            'first_name_en' => Yii::t("labels", 'First Name'),
            'middle_name_en' => Yii::t("labels", 'Middle Name'),
            'last_name_en' => Yii::t("labels", 'Last Name'),
            'nick' => Yii::t("labels", 'Preferred Name'),
            'gender' => Yii::t("labels", 'Gender'),
            'country' => Yii::t("labels", 'Nationality'),
            'lang' => Yii::t("labels", 'Language'),
            'photo' => Yii::t("labels", 'Photo'),
            'schoolid' => Yii::t("labels", 'Campus'),
            'next_schoolid' => 'Next Schoolid',
            'next_yid' => 'Next Yid',
            'classid' => 'Classid',
            'status' => Yii::t("labels", 'Status'),
            'fid' => 'Fid',
            'mid' => 'Mid',
            'updated_timestamp' => 'Updated Timestamp',
            'created_timestamp' => 'Created Timestamp',
            'create_uid' => 'Create Uid',
            'access_pass' => 'Access Pass',
            'privacy' => Yii::t("labels", 'Privacy'),
            'credit' => Yii::t("labels", 'Credit'),
            'identity' => Yii::t("labels", 'Identity'),
            'invoice_title' => Yii::t("labels", 'Fapiao Title'),
            'englishName' => Yii::t("labels", 'English Name'),
            'fatherName' => Yii::t("labels", 'Father Name'),
            'fatherEmail' => Yii::t("labels", 'Father Email'),
            'motherName' => Yii::t("labels", 'Mother Name'),
            'motherEmail' => Yii::t("labels", 'Mother Email'),
            'fatherMobile' => Yii::t("labels", 'Father Mobile'),
            'motherMobile' => Yii::t("labels", 'Mother Mobile'),
            'est_enter_date' => Yii::t("labels", 'Expected Date of Attendance'),
            'enter_date' => Yii::t("labels", 'Actual Date of Attendance'),
            'admission_id' => 'admission_id',
            'first_startyear' => 'first_startyear',
            'educational_id' => Yii::t("labels", 'Educational Student ID'),
        );
    }

    public function scopes() {
        return array(
            'auth' => array(
                'select' => 'childid, access_pass, fid, mid, schoolid'
            )
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search() {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('childid', $this->childid);
        $criteria->compare('barcode', $this->barcode, true);
        $criteria->compare('birthday', $this->birthday);
        $criteria->compare('birthday_search', $this->birthday_search, true);
        $criteria->compare('name_cn', $this->name_cn, true);
        $criteria->compare('first_name_en', $this->first_name_en, true);
        $criteria->compare('middle_name_en', $this->middle_name_en, true);
        $criteria->compare('last_name_en', $this->last_name_en, true);
        $criteria->compare('nick', $this->nick, true);
        $criteria->compare('gender', $this->gender);
        $criteria->compare('country', $this->country, true);
        $criteria->compare('lang', $this->lang, true);
        $criteria->compare('is_legel_cn_name', $this->is_legel_cn_name, true);
        $criteria->compare('photo', $this->photo, true);
        $criteria->compare('schoolid', $this->schoolid, true);
        $criteria->compare('next_schoolid', $this->next_schoolid, true);
        $criteria->compare('next_yid', $this->next_yid);
        $criteria->compare('classid', $this->classid);
        $criteria->compare('status', $this->status);
        $criteria->compare('fid', $this->fid);
        $criteria->compare('mid', $this->mid);
        $criteria->compare('updated_timestamp', $this->updated_timestamp);
        $criteria->compare('created_timestamp', $this->created_timestamp);
        $criteria->compare('create_uid', $this->create_uid);
        $criteria->compare('access_pass', $this->access_pass, true);
        $criteria->compare('credit', $this->credit);
        $criteria->compare('identity', $this->identity, true);
        $criteria->compare('admission_id', $this->admission_id, true);
        $criteria->compare('first_startyear', $this->first_startyear, true);
        $criteria->compare('educational_id', $this->educational_id, true);

        return new CActiveDataProvider($this, array(
                    'criteria' => $criteria,
                ));
    }

    public function getChildsByParentId($parentUid) {
        $criteria = new CDbCriteria;
        $parentUid = intval($parentUid);
        if ($parentUid) {
            $criteria->condition = 'fid=:parentUid';
            $criteria->addCondition('mid=:parentUid', 'OR');
            $criteria->params = array(':parentUid' => $parentUid);
            //$childs = self::model()->auth()->findAll($criteria);
            $childs = self::model()->findAll($criteria);
            $ret = array();
            foreach($childs as $child){
                $ret[] = $child;
            }
            return $ret;
        }
    }

    public function getChildsByParentIdForDs($parentUid) {
        $criteria = new CDbCriteria;
        $parentUid = intval($parentUid);
        if ($parentUid) {
            $criteria->condition = 'fid=:parentUid';
            $criteria->addCondition('mid=:parentUid', 'OR');
            $criteria->params = array(':parentUid' => $parentUid);
            $childs = self::model()->findAll($criteria);
            $ret = array();
            foreach($childs as $child){
                $ret[] = $child;
            }
            return $ret;
        }
    }

    public function getDsChildName($showall=false,$language=false) {
        $eName1 = trim(sprintf("%s %s", trim($this->nick), trim($this->last_name_en)));
        $eName2 = trim(sprintf("%s %s", trim($this->first_name_en), trim($this->last_name_en)));
        $cName = trim($this->name_cn);
        if(empty($language)){
            $language = Yii::app()->language;
        }
        if($showall){
            if(isset($this->is_legel_cn_name) || $this->is_legel_cn_name == null ||  $this->is_legel_cn_name == 1){
                if ($language == 'zh_cn'){
                    return trim(sprintf("%s (%s)", $cName, $eName1));
                }else{
                    return trim(sprintf("%s (%s)", $eName1, $cName));
                }
            }else{
                return  $eName2;
            }
        }else{
            if($language == 'zh_cn'){
                if(isset($this->is_legel_cn_name) || $this->is_legel_cn_name == null ||  $this->is_legel_cn_name == 1){ // 中文
                    $fullName  = ($cName != "") ? $cName : $eName1;
                }else{
                    $fullName = $eName2;
                }
            }else{
                if(isset($this->is_legel_cn_name) || $this->is_legel_cn_name == null ||  $this->is_legel_cn_name == 1){ // 中文
                    $fullName = ($eName1 != "") ? $eName1 : $cName;
                }else{
                    $fullName = $eName2;
                }
            }
        }
        return $fullName;
    }

    public function getReportChildName($showall=false, $lang = NULL) {
        if(!$lang){
            $lang = Yii::app()->language;
        }
        $eName1 = trim(sprintf("%s %s", trim($this->nick), trim($this->last_name_en)));
        $eName2 = trim(sprintf("%s %s", trim($this->first_name_en), trim($this->last_name_en)));
        if ($this->is_legel_cn_name == 2 && $this->middle_name_en) {
            $eName2 = trim(sprintf("%s %s %s", trim($this->first_name_en), trim($this->middle_name_en), trim($this->last_name_en)));
        }
        $cName = trim($this->name_cn);
        if($showall){
            if(isset($this->is_legel_cn_name) || $this->is_legel_cn_name == null ||  $this->is_legel_cn_name == 1){
                if ($lang == 'zh_cn'){
                    return trim(sprintf("%s (%s)", $cName, $eName2));
                }else{
                    return trim(sprintf("%s (%s)", $eName2, $cName));
                }
            }else{
                return  $eName2;
            }
        }else{
            if ($lang == 'zh_cn') {
                if(isset($this->is_legel_cn_name) || $this->is_legel_cn_name == null ||  $this->is_legel_cn_name == 1){ // 中文
                    $fullName  = ($cName != "") ? $cName : $eName2;
                }else{
                    $fullName = $eName2;
                }
            } else {
                if(isset($this->is_legel_cn_name) || $this->is_legel_cn_name == null ||  $this->is_legel_cn_name == 1){ // 中文
                    $fullName = ($eName2 != "") ? $eName2 : $cName;
                }else{
                    $fullName = $eName2;
                }
            }
        }
        return $fullName;
    }
    public function getReportChildNameByLang($lang = NULL) {
        if(!$lang){
            $lang = Yii::app()->language;
        }
        $eName2 = trim(sprintf("%s %s", trim($this->first_name_en), trim($this->last_name_en)));
        if ($this->is_legel_cn_name == 2 && $this->middle_name_en) {
            $eName2 = trim(sprintf("%s %s %s", trim($this->first_name_en), trim($this->middle_name_en), trim($this->last_name_en)));
        }
        $cName = trim($this->name_cn);

        if ($lang == 'zh_cn') {
            $fullName  = $cName;
        } else {
            $fullName = $eName2;
        }
  
        return $fullName;
    }

    public function getNameForReport()
    {
        if (Yii::app()->language == 'en_us' && $this->is_legel_cn_name == 1) {
            return $this->first_name_en . ' ' . $this->last_name_en;
        }
        else {
            return $this->getChildName();
        }
    }

    public function getChildName($nick = false, $showall=false, $bilingual=false,$lang=false) {
        if(in_array($this->schoolid, array("BJ_DS","BJ_SLT"))){
            return $this->getDsChildName($showall);
        }
        if (trim($this->last_name_en) != "")
            $eName = trim(sprintf("%s %s", trim($this->first_name_en), trim($this->last_name_en)));
        else
            $eName = trim($this->first_name_en);
        $cName = trim($this->name_cn);

        if($bilingual) {
            if (Yii::app()->language == 'zh_cn')
                return trim(sprintf("%s %s", $cName, $eName));
            else
                return trim(sprintf("%s %s", $eName, $cName));
        }

        if ($nick) {
            $nickName = "";
            if ($this->nick != "") {
                $nickName = $this->nick;
            }else{
                if(!$showall){
                    if (!in_array($this->country, array(36, 175))) {
                        if ($this->first_name_en != "") {
                            $nickName = $this->first_name_en;
                        }
                    }
                }
            }
            if(!$showall){
                return $nickName; //1+0
            }
        }

        // if (in_array($this->country, array(36, 175))) {
        if (Yii::app()->language == 'zh_cn') {
            $fullName = ($cName != "") ? $cName : $eName;
        } else {
            $fullName = ($eName != "") ? $eName : $cName;
        }

        if(!$nick && !$showall) return $fullName; // 0 + 0

        if(isset($nickName) && $nickName != "") return addBrackets($nickName, true, 'append') . $fullName; // 1 + 1
        else return $fullName;
    }

    public function getUnpaidCount($childid = 0) {
        $childid = ($childid) ? $childid : $this->childid;
        if ($childid) {
            Yii::import('common.models.invoice.Invoice');

            $up_cri = new CDbCriteria();
            $up_cri->compare('t.childid', $childid);
            // 不显示学前教育津贴
            $up_cri->addNotInCondition('t.payment_type', array('preschool_subsidy'));
            $up_cri->addInCondition('t.status', array(10, 30));

//            $ktype = IvyClass::getClassTypes(false, Branch::TYPE_CAMPUS_ELEMENTARY_SCHOOL);
//            $up_cri->addNotInCondition('classInfo.classtype', array_keys($ktype));

            $up_count = Invoice::model()->count($up_cri);
            return $up_count;
        }
        return null;
    }

    public function getReplyCount($childid = 0) {
        $childid = ($childid) ? $childid : $this->childid;
        if ($childid) {
            Yii::import('common.models.feedback.Comments');

            $readf = array(0);
            $currController = Yii::app()->getController();
            $fid = $currController->myChildObjs[$currController->getChildid()]->fid;
            $mid = $currController->myChildObjs[$currController->getChildid()]->mid;
            if (Yii::app()->user->id == $fid) {
                array_push($readf, 2);
            } elseif (Yii::app()->user->id == $mid) {
                array_push($readf, 1);
            }
            $r_cri = new CDbCriteria();
            $r_cri->compare('com_child_id', $childid);
            $r_cri->compare('com_ifread', $readf);
            $r_cri->compare('com_user_type', 1);

            $r_count = Comments::model()->count($r_cri);
            return $r_count;
        }
        return null;
    }

    /**
     * 在使用学年更替功能时显示孩子的状态;已毕业、已退学？或者已分配到下年某班
     * @return	Object		Description
     */
    public function getSwitchStatus($withNext=true){
        if(in_array($this->status, array(self::STATS_GRADUATED, self::STATS_DROPOUT, self::STATS_DROPPINGOUT))){
            $ret['txt'] = $this->getStatus();
        }

        if($withNext && $this->nextYear && $this->nextYear->classid){
            $ret['classid'] = $this->nextYear->classid;
        }
        return $ret;
    }

    /**
     * 返回以 father, mother 为主键的对象数组
     * @param	String	$with		array()
     * @param	Boolean	$createNew	boolean
     * @return	Object				array()
     */
    public function getParents($with=array('parent'), $createNew=false){
        $crit = new CDbCriteria;
        $pids = array();
        if($this->fid) $pids[] = $this->fid;
        if($this->mid) $pids[] = $this->mid;
        $crit->index = 'uid';
        if(empty($with)){
        $parents = User::model()->findAllByPk($pids, $crit);

        }else{
        $parents = User::model()->with($with)->findAllByPk($pids, $crit);
        }

        $models = array();

        $default = ($createNew) ? new User : null;
        if(!is_null($default)) $default->level = 0;

        $models['father'] = isset($parents[$this->fid]) ? $parents[$this->fid] : $default;
        $models['mother'] = isset($parents[$this->mid]) ? $parents[$this->mid] : $default;

        return $models;
    }

    public function getStatusList(){
        return array(
            self::STATS_REGISTERED => Yii::t('child', 'Newly Registered'),
            //self::STATS_ACTIVE_WAITING => Yii::t('child', '有效'),
            self::STATS_ACTIVE => Yii::t('child', 'Active'),
            self::STATS_DROPPINGOUT => Yii::t('child', 'Dropping Out'),
            self::STATS_DROPOUT => Yii::t('child', 'Dropped Out'),
            self::STATS_GRADUATED => Yii::t('child', 'Graduated'),
        );
    }

    public function getStatus(){
        if(empty($this->statusList)){
            $this->statusList = $this->getStatusList();
        }

        return $this->statusList[$this->status];
    }

    /**
     *
     * @param type $childId
     * @param type $flag
     * @return boolean
     */
    public static function  setChildSync($childId, $flag=1)
    {
        Yii::import('common.models.child.ChildSync');

        $model = ChildSync::model()->findByPk($childId);
        if ($model === null)
            $model = new ChildSync;
        $model->childid = $childId;
        $model->flag = $flag;
        $model->timestamp = time();
        if ( $model->save() )
            return true;
        else
            return false;
    }

    public function save($runValidation=true, $attributes=null)
    {
        $ret = parent::save($runValidation, $attributes);
        $this->setChildSync($this->childid, 1);
        return $ret;
    }

    public function getSeniority($family_id='')
    {
        if($family_id){
            $seniority = $this->countByAttributes(array('family_id'=>$family_id));
            return $seniority+1;
        }
        return false;
    }

    /**
     * 这个小孩是否上过幼儿园
     */
    public function kindergarten($childid = 0)
    {
        if($childid){
            $model = $this->findByPk($childid);
            $schoolid = $model->schoolid;
        }
        else{
            $childid = $this->childid;
            $schoolid = $this->schoolid;
        }
        if($schoolid != 'BJ_DS'){
            return true;
        }
        $kindergartenType = array('b', 'c', 'k', 'n', 'p', 'mc', 'mk', 'mt');
        Yii::import('common.models.portfolio.ChildStudyHistory');
        $criteria = new CDbCriteria();
        $criteria->compare('t.childid', $childid);
        $criteria->compare('classInfo.classtype', $kindergartenType);
        $count = ChildStudyHistory::model()->with('classInfo')->count($criteria);
        if($count)
            return true;
        else
            return false;
    }

    /**
     * 获取孩子当前学年所退学费次数
     * @return [type] [description]
     */
    public function refundfeeNum()
    {
        $childid = $this->childid;
        if ($childid) {
            Yii::import('common.models.invoice.InvoiceChildRefund');

            $criteria = new CDbCriteria();
            $criteria->compare('t.childid', $childid);
            $criteria->compare('t.payment_type', 'tuition');
            $criteria->compare('t.yid', $this->school->schcalendar);
            $criteria->compare('t.status', array(InvoiceChildRefund::STATS_COMPLETED,InvoiceChildRefund::STATS_AWAITING));

            $count = InvoiceChildRefund::model()->count($criteria);
            return $count;
        }
        return null;
    }

    public function processAvatar($new, $old)
    {
        if (class_exists('OA')) {
            $rootUploadPath = Yii::app()->params['OAUploadBasePath'];
        } else {
            $rootUploadPath = Yii::app()->params['uploadPath'];
        }
        $aliyunPath = date('Ym') . '/' . $new;
        $localPath = $rootUploadPath . '/childmgt/' . $new;
        if (!CommonUtils::isProduction()) {
            $aliyunPath = 'test/' . date('Ym') . '/' . $new;
        }
        // 旧头像存档
        if ($old && $old != 'blank.gif') {
            $data = array(
                'school_id' => $this->schoolid,
                'class_id' => $this->classid,
                'child_id' => $this->childid,
                'photo' => $aliyunPath,
                'updated_by' => Yii::app()->user->getId(),
                'created_by' => Yii::app()->user->getId(),
            );
            CommonUtils::requestDsOnline('child/childPhotoRepo', $data);
        }
        // 新头像上传到阿里云
        if ($new && $new != 'blank.gif') {
            $bucketFlag = 'public';
            $ossSDK = CommonUtils::initOSS($bucketFlag);
            if ($ossSDK->uploadFile('childmgt/' . $aliyunPath, $localPath)) {
                // 删除原有文件
                @unlink($localPath);
                $this->photo = $aliyunPath . '!w200';
            }
        }
    }

    /**给指定位置设置 0 / 1
     * @param $n int 要设置的位置 从1开始
     * @param $set int 设置的数 0 / 1
     */
    public function setLabel($n, $set)
    {
        $label_num = (int)ceil($n / ChildLabelGroup::MAXLABEL);
        $label = 'label' . $label_num;
        $n = ChildLabelGroup::MAXLABEL - (($label_num * ChildLabelGroup::MAXLABEL) - $n);//重新计算对应字段的设置位置
        if (!$this->childLabel) {
            // 创建一个新的关联模型对象
            $this->childLabel = new ChildLabel;
            $this->childLabel->childid = $this->childid;
            $this->childLabel->$label = 0;
        }

        if ($set === 1) {
            $toBinary = $this->toBinary($this->childLabel->$label);
            $setBitTo1 = $this->setBitTo1($toBinary,$n-1);
            return $this->childLabel->$label = $this->binaryToDecimal($setBitTo1);
        } else {
            $toBinary = $this->toBinary($this->childLabel->$label);
            $setBitTo0 = $this->setBitTo0($toBinary, $n-1);
            return $this->childLabel->$label = $this->binaryToDecimal($setBitTo0);;
        }
    }

    /**
     * 所有标签
     * @return array
     */
    public function getLabel(){
        $label = array();
        if ($this->childLabel) {
            foreach ($this->childLabel as $k => $item) {
                if (strpos($k, 'label') !== false) {
                    $label[] = $item;
                }
            }
        } else {
            return array();
        }
        $flagConfig = $this->labelConfig();//获取分组配置
        $AllFlag = array();
        $returnData = array();
        foreach ($label as $k => $item) {
            $binaryFlag = $this->toBinary($item);//十进制转二进制
            //获取 从右到左 1的位置
            $BinaryFlagArr = str_split($binaryFlag);
            krsort($BinaryFlagArr);
            $BinaryFlagArr = array_values($BinaryFlagArr);
            //1的位置  只展示配置上有的标签
            $flag1 = array_keys(array_filter($BinaryFlagArr));
            foreach ($flag1 as $value) {
                $AllFlag[] = ($k * ChildLabelGroup::MAXLABEL) + $value + 1;
            }
        }
        foreach ($AllFlag as $value) {
            if (!empty($flagConfig[$value])) {
                $returnData[] = $flagConfig[$value];
            }
        }
        return $returnData;
    }

    //十进制转二进制
    public function toBinary($decimal){
        $binary = '';
        while (bccomp($decimal, '0') > 0) {
            $binary = bcmod($decimal, '2') . $binary;
            $decimal = bcdiv($decimal, '2');
        }
        return $binary;
    }

    // 将二进制字符串转换为十进制数
    function binaryToDecimal($binary) {
        $decimal = '0';
        $length = strlen($binary);
        for ($i = 0; $i < $length; $i++) {
            $bit = $binary[$i];
            $decimal = bcmul($decimal, '2');
            if ($bit === '1') {
                $decimal = bcadd($decimal, '1');
            }
        }
        return $decimal;
    }

    //将制定位置设置成1
    function setBitTo1($binary, $position) {
        $binary = strrev(str_pad($binary, $position + 1, '0', STR_PAD_LEFT));
        $binary[$position] = '1';
        return strrev($binary);
    }
    //将制定位置设置成0
    function setBitTo0($binary, $position) {
        $binary = strrev(str_pad($binary, $position + 1, '0', STR_PAD_LEFT));
        $binary[$position] = '0';
        return strrev($binary);
    }



    /**获取指定位置的值 0/1
     * @param $n
     * @return int
     */
    public function getPositionLabel($n)
    {
        $label_num = (int)ceil($n / ChildLabelGroup::MAXLABEL);
        $label = 'label' . $label_num;
        if (!$this->childLabel) {
            return 0;
        }
        $num = $this->childLabel->$label;
        $n = ChildLabelGroup::MAXLABEL - (($label_num * ChildLabelGroup::MAXLABEL) - $n);//重新计算对应字段的设置位置
        $digit = $n - 1;
        return ($num & (1 << $digit)) >> $digit;
    }

    /**可设置的标签
     * @return array
     */
    public function labelConfig()
    {
        $criteria = new CDbCriteria();
        $models =  ChildLabelGroupItem::model()->findAll($criteria);
        $data = array();
        foreach ($models as $model) {
            if($model->hide_desc == 1){
                $model->desc =  CommonUtils::autoLang('受保护的标签信息', 'Protected Tag Info.');
            }
            $data[$model->flag] = $model;
        }
        return  $data;
    }

}
