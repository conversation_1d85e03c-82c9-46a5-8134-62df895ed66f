<?php
if(!defined("IVY_POLICY"))
throw new CException('DO NOT USE THIS FILE DIRECTLY');

return array( 'policy'=>array(

	//是否老生老办法
	ENABLE_CONSTANT_TUITION => true,

	//年保育费
	//ANNUAL_CHILDCARE_AMOUNT => 8000,

	//是否开放课外活动账单
	ENABLE_AFTERSCHOOLS => true,

	//图书馆工本费
	FEETYPE_LIBCARD => 10,

	//学期四个时间点；取前后两端
	TIME_POINTS   => array(201609,201701,201703,201707),

	//寒暑假月份，及其每月计费天数
//	HOLIDAY_MONTH => array(201702=>20,201708=>20),

	//年付开通哪些缴费类型
//	PAYGROUP_ANNUAL => array(
//		ITEMS => array(
//			FEETYPE_TUITION,
//			FEETYPE_LUNCH,
//			FEETYPE_SCHOOLBUS,
//		),
//		TUITION_CALC_BASE => BY_MONTH,
//	),
//
//	//学期付开通哪些缴费类型
	PAYGROUP_SEMESTER => array(
		ITEMS => array(
			FEETYPE_TUITION,
			FEETYPE_LUNCH,
			FEETYPE_SCHOOLBUS,
		),
		TUITION_CALC_BASE => BY_MONTH,
	),
//
//	//月份开通哪些缴费类型
//	PAYGROUP_MONTH => array(
//		ITEMS => array(
//			FEETYPE_TUITION,
//			FEETYPE_LUNCH,
//			FEETYPE_SCHOOLBUS,
//		),
//		TUITION_CALC_BASE => BY_MONTH,
//	),

	//计算基准：BY_MONTH, 或者 BY_SETTING, 前者表示所有的学费都基于月费用计算，后者表示所有的学费都基于学期或学年
	TUITION_CALCULATION_BASE => array(

		BY_MONTH => array(

			//月收费金额
			AMOUNT => array(
				FULL5D => 3800,
			),

			//收费月份，及其权重
			MONTH_FACTOR => array(
				201609=>1,
				201610=>1,
				201611=>1,
				201612=>1,
				201701=>1,
				201703=>1,
				201704=>1,
				201705=>1,
				201706=>1,
				201707=>1
			),

			//折扣
			GLOBAL_DISCOUNT => array(
				DISCOUNT_ANNUAL => '1:1:2016-12-02',
				DISCOUNT_SEMESTER1 => '1:1:2016-11-02',
				DISCOUNT_SEMESTER2 => '1:1:2017-06-02'
			)

		),

	),

	//每餐费用
	LUNCH_PERDAY => 32,

	//账单到期日和账单开始日之差
	DUEDAYS_OFFSET => 1,

    //代金卷金额
    //VOUCHER_AMOUNT => 500,

));