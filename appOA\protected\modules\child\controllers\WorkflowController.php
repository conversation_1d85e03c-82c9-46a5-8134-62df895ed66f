<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
class WorkflowController extends ChildBasedController
{
    public $dialogWidth=500;
    public function actionCashout()
    {
        $t = strtolower(Yii::app()->request->getParam('t', 'initadmin'));
        Yii::import('application.components.workflow.*');
        Yii::import('application.components.policy.PolicyApi');
        Yii::import('common.models.invoice.*');
        Yii::import('common.models.child.*');
        Yii::import('common.models.workflow.*');
        new PolicyApi($this->childObj->schoolid);
        $this->layout = '//layouts/dialog';
        $deModel = WorkflowDefination::model()->find('defination_handle=:defination_handle',array(':defination_handle'=>'invoice_cashout'));
        $nodeId = current(explode(',', $deModel->node_order));
        $workflow = new WorkflowApi($deModel->defination_id,$nodeId,0,$this->childObj->schoolid);
        switch ($t){
            case 'initadmin':
                $model = new ChildCredit;
                $creditModel = new ChildCreditWorkflow;
                $this->render('cashout', array('model' => $model,'creditModel'=>$creditModel,'t'=>'saveadmin'));
                break;
            case 'saveadmin':
                //判断提现金额是否足够
                if ($cid = PolicyApi::childOutCash($this->childId,$_POST['ChildCredit']['amount'])){
                    //插入工作流数据
                    $opModel = new WorkflowOperation();
                    $opModel->operation_type=0;
                    $opModel->operation_object_id = $this->childObj->childid;
                    $opModel->defination_id = $deModel->defination_id;
                    $opModel->branchid = $this->childObj->schoolid;
                    $opModel->state = WorkflowOperation::WORKFLOW_STATS_UNOP;
                    $opModel->current_node_index = $nodeId;
                    $opModel->start_user = Yii::app()->user->id;
                    $opModel->start_time = time();
                    if ($opModel->save()){
                        //变量
                        $workflow->operationId = $opModel->id;
                        $workflow->getOperationObj();
                        //业务中间表（ivy_child_credit_workflow）
                        $creditFlowModel = new ChildCreditWorkflow();
                        $creditFlowModel->credit_id = $cid;
                        $creditFlowModel->status = WorkflowOperation::WORKFLOW_STATS_UNOP;
                        $creditFlowModel->memo = $_POST['ChildCreditWorkflow']['memo'];
                        $creditFlowModel->userid = Yii::app()->user->id;
                        $creditFlowModel->create_timestamp = time();
                        $creditFlowModel->save();
                        //工作流业务中间表
                        $detailModel = new WorkflowOperationDetail();
                        $detailModel->operation_id = $opModel->id;
                        $detailModel->operation_object_id = $creditFlowModel->id;
                        $detailModel->save();
                        //工作流权限创建
                        $workflow->createRole($nodeId,$opModel->id);
                        //更新工作流低层流程
                        $workflow->saveWorkflow($_POST['ChildCreditWorkflow']['memo'],WorkflowOperation::WORKFLOW_STATS_OPED);
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', '成功');
                        $this->addMessage('refresh', true);
                    }else{
                        $this->addMessage('state', 'fail');
                        $errs = current($model->getErrors());
                        if ($errs)
                            $this->addMessage('message', $errs[0]);
                    }
                    
                }else{
                     $this->addMessage('state', 'fail');
                     $this->addMessage('message', '失败');
                }
                $this->showMessage();
                break;
            case 'transitadmin':
                break;
        }
        /*
        if (isset($_POST['ChildCreditMemo']) && Yii::app()->request->isPostRequest) {
            $model->attributes = $_POST['ChildCreditMemo'];
            if ($model->validate()) {
                if (PolicyApi::childOutCash($this->childId, $model->amount)) {
                    if ($model->memo)
                        $model->save();
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', '成功');
                    $this->addMessage('refresh', true);
                }
                else {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '失败');
                }
            } else {
                $this->addMessage('state', 'fail');
                $errs = current($model->getErrors());
                if ($errs)
                    $this->addMessage('message', $errs[0]);
            }
            $this->showMessage();
        }
        */
       
    }
}
?>
