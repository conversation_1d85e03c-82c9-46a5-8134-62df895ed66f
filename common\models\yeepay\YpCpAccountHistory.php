<?php

/**
 * This is the model class for table "ivy_yp_cp_account_history".
 *
 * The followings are the available columns in table 'ivy_yp_cp_account_history':
 * @property integer $id
 * @property string $orderIds
 * @property string $title
 * @property double $settlement_amount
 * @property double $settlement_actual_amount
 * @property string $bank_title
 * @property double $bank_amount
 * @property double $bank_actual_amount
 * @property integer $date_timestamp
 * @property integer $bank_userid
 * @property integer $status
 * @property integer $userid
 * @property integer $update_timestamp
 * @property integer $yeepay_timestamp
 * @property integer $bank_timestamp
 * @property string $p1_MerId
 * @property integer $handover_status
 */
class YpCpAccountHistory extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_yp_cp_account_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('orderIds, title', 'required'),
			array('date_timestamp, bank_userid, status, userid, update_timestamp, yeepay_timestamp, bank_timestamp, handover_status, bank_id', 'numerical', 'integerOnly'=>true),
			array('settlement_amount, settlement_actual_amount, bank_amount, bank_actual_amount', 'numerical'),
			array('title, bank_title', 'length', 'max'=>500),
			array('p1_MerId', 'length', 'max'=>20),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, orderIds, title, settlement_amount, settlement_actual_amount, bank_title, bank_amount, bank_actual_amount, date_timestamp, bank_userid, status, userid, update_timestamp, yeepay_timestamp, bank_timestamp, p1_MerId, handover_status', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'detail' => array(self::HAS_MANY, 'YpCpAccountDetail', 'settlementId'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'orderIds' => 'Order Ids',
			'title' => 'Title',
			'settlement_amount' => 'Settlement Amount',
			'settlement_actual_amount' => 'Settlement Actual Amount',
			'bank_title' => 'Bank Title',
			'bank_amount' => 'Bank Amount',
			'bank_actual_amount' => 'Bank Actual Amount',
			'date_timestamp' => 'Date Timestamp',
			'bank_userid' => 'Bank Userid',
			'status' => 'Status',
			'userid' => 'Userid',
			'update_timestamp' => 'Update Timestamp',
			'yeepay_timestamp' => 'Yeepay Timestamp',
			'bank_timestamp' => 'Bank Timestamp',
			'p1_MerId' => 'P1 Mer',
			'handover_status' => 'Handover Status',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('orderIds',$this->orderIds,true);
		$criteria->compare('title',$this->title,true);
		$criteria->compare('settlement_amount',$this->settlement_amount);
		$criteria->compare('settlement_actual_amount',$this->settlement_actual_amount);
		$criteria->compare('bank_title',$this->bank_title,true);
		$criteria->compare('bank_amount',$this->bank_amount);
		$criteria->compare('bank_actual_amount',$this->bank_actual_amount);
		$criteria->compare('date_timestamp',$this->date_timestamp);
		$criteria->compare('bank_userid',$this->bank_userid);
		$criteria->compare('status',$this->status);
		$criteria->compare('userid',$this->userid);
		$criteria->compare('update_timestamp',$this->update_timestamp);
		$criteria->compare('yeepay_timestamp',$this->yeepay_timestamp);
		$criteria->compare('bank_timestamp',$this->bank_timestamp);
		$criteria->compare('p1_MerId',$this->p1_MerId,true);
		$criteria->compare('handover_status',$this->handover_status);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return YpCpAccountHistory the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
