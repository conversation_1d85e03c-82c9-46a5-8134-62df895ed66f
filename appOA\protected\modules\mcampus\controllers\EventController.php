<?php

class EventController extends BranchBasedController
{
    public $actionAccessAuths = array(
        'Index'             => 'o_A_Access',
        'update'            => array('access'=>'o_A_Access', 'admin'=>'o_A_Adm_Visits'),
        'viewMember'        => 'o_A_Access',
        'viewMemberivy'     => 'o_A_Access',
        'deleteMember'      => 'o_A_Adm_Visits',
        'updateMember'      => array('access'=>'o_A_Access', 'admin'=>'o_A_Adm_Visits'),
        'Import'            => array('access'=>'o_A_Access', 'admin'=>'o_A_Adm_Visits'),
        'Export'            => 'o_A_Adm_Visits',
        'deleteImport'      => 'o_A_Adm_Visits',
        'tocrm'             => 'o_A_Adm_Visits',
        'updatememberivy'   => array('access'=>'o_A_Access', 'admin'=>'o_A_Adm_Visits'),
        'deletememberivy'   => 'o_A_Adm_Visits',
        'exportivy'         => 'o_A_Adm_Visits',
    );

    public $branchArr;
    public $dialogWidth = 600;
    public $cfg = array();


    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";
        Yii::import('common.models.visit.*');
        $this->cfg = CommonUtils::LoadConfig('CfgVisit');
        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

        $this->branchSelectParams['urlArray'] = array('//mcampus/event/index');

        foreach ( Branch::model()->getBranchList(null,true, '') as $branch){
            $this->branchArr[$branch['id']] = $branch['title'];
        }
    }


    /**
     * 活动管理主页面
     */
    public function actionIndex()
    {

        $criteria = new CDbCriteria();
        $criteria->compare('school_id', $this->branchId);
        $criteria->order='event_date desc';
        $dataProvider = new CActiveDataProvider('SchoolEvents', array(
            'criteria'=>$criteria,
        ));

        $this->render('index',array(
            'model'=>$dataProvider,
        ));
    }

    /**
     * 添加修改页面及保存处理
     * @param int $id
     */
    public function actionUpdate($id=0)
    {
        $this->layout='//layouts/dialog';
        $model = SchoolEvents::model()->findByPk($id);

        if ($model === null)
            $model = new SchoolEvents;
            $model->code = uniqid();

        if (isset( $_POST['SchoolEvents'] )){
            $model->attributes = $_POST['SchoolEvents'];
            $model->school_id = $this->branchId;
            $model->event_date=strtotime($_POST['SchoolEvents']['event_date']);
            $model->apply_duedate=strtotime($_POST['SchoolEvents']['apply_duedate']);
            $model->userid=Yii::app()->user->id;
            $model->update_timestamp=time();

            $model->poster_en = CUploadedFile::getInstance($model, 'poster_en');
            $model->poster_cn = CUploadedFile::getInstance($model, 'poster_cn');

            $subDir = 'event/';
            $uploadRootPath = rtrim(Yii::app()->params['OAUploadBasePath'], '/') . '/';

            //删除旧图
            if(!$model->isNewRecord) {
                if(!empty($model->new_poster_en)){
                    Yii::log('post en exists');
                    @unlink($uploadRootPath . $subDir . $model->new_poster_en);
                    $aliYunOss['del'][] = $subDir . $model->new_poster_en;
                }
                if(!empty($model->new_poster_cn)){
                    Yii::log('post cn exists');
                    @unlink($uploadRootPath . $subDir . $model->new_poster_cn);
                    $aliYunOss['del'][] = $subDir . $model->new_poster_cn;
                }
            }

            if ($model->poster_en){
                $fileNameEn = 'poster_'.uniqid().'.'.$model->poster_en->getExtensionName();
                $model->poster_en->saveAs($uploadRootPath . $subDir. $fileNameEn );
                $aliYunOss['new'][] = $subDir . $fileNameEn;
                $model->new_poster_en = $fileNameEn;
            }

            if ($model->poster_cn){
                $fileNameCn = 'poster_'.uniqid().'.'.$model->poster_cn->getExtensionName();
                $model->poster_cn->saveAs($uploadRootPath . $subDir. $fileNameCn );
                $aliYunOss['new'][] = $subDir . $fileNameCn;
                $model->new_poster_cn = $fileNameCn;
            }

            if ($model->save()){

                if ($model->id)
                    SchoolEventsShow::model()->deleteAllByAttributes(array('eventid'=>$model->id));
                if (isset($_POST['SchoolEventsShow']) && $_POST['SchoolEventsShow']['schoolid'] && $model->school_id == 'BJ_TYG'){
                    foreach ($_POST['SchoolEventsShow']['schoolid'] as $schid){
                        $_model = new SchoolEventsShow;
                        $_model->eventid = $model->id;
                        $_model->schoolid=$schid;
                        $_model->save();
                    }
                }
                $this->addMessage('state', 'success');
                $this->addMessage('refresh', true);
                $this->addMessage('message', Yii::t("global","Data Saved!"));

                CommonUtils::processAliYunOSS($aliYunOss, $uploadRootPath);
            }
            else{
                $this->addMessage('state', 'fail');
                $errs = current($model->getErrors());
                $this->addMessage('message', $errs?$errs[0]:Yii::t('message','Failed!'));
            }
            $this->showMessage();
        }

        $sArray = array();
        $modelS = SchoolEventsShow::model()->findAllByAttributes(array('eventid'=>$id));
        if ($modelS){
            foreach($modelS as $ms){
                $sArray[$ms->schoolid]=$ms->schoolid;
            }
        }

        $model->apply_duedate = OA::formatDateTime($model->apply_duedate);
        $model->event_date = OA::formatDateTime($model->event_date);

        $this->render('edit', array('model'=>$model, 'sArray'=>$sArray));
    }

    /**
     * 查看活动报名名单（非艾毅家长）
     * @param int $id
     */
    public function actionViewMember($id=0)
    {
        if ($id){
            $pageSubMenu = array(
                array(
                    'label' => '非艾毅家长',
                    'url' => array('//mcampus/event/viewMember', 'id'=>$id)
                ),
                array(
                    'label' => '艾毅家长',
                    'url' => array('//mcampus/event/viewMemberivy', 'id'=>$id)
                ),
            );

            $criteria = new CDbCriteria();
            $criteria->compare('event_id', $id);
            $criteria->order='timestamp desc';
            $dataProvider = new CActiveDataProvider('EventMember', array(
                'criteria'=>$criteria,
            ));

            $model = SchoolEvents::model()->findByPk($id);

            $this->render('viewmember', array('dataProvider'=>$dataProvider, 'model'=>$model, "pageSubMenu"=>$pageSubMenu));
        }
    }

    /**
     * 查看活动报名名单（艾毅家长）
     * @param int $id
     */
    public function actionViewMemberivy($id=0)
    {
        if ($id){
            $pageSubMenu = array(
                array(
                    'label' => '非艾毅家长',
                    'url' => array('//mcampus/event/viewMember', 'id'=>$id)
                ),
                array(
                    'label' => '艾毅家长',
                    'url' => array('//mcampus/event/viewMemberivy', 'id'=>$id)
                ),
            );

            $criteria = new CDbCriteria();
            $criteria->compare('event_id', $id);
            $criteria->order='timestamp desc';
            $dataProvider = new CActiveDataProvider('EventMemberIvy', array(
                'criteria'=>$criteria,
            ));

            $model = SchoolEvents::model()->findByPk($id);

            $this->render('viewmemberivy', array('dataProvider'=>$dataProvider, 'model'=>$model, "pageSubMenu"=>$pageSubMenu));
        }
    }

    /**
     * 删除报名人员
     */
    public function actionDeleteMember()
    {
        $id=Yii::app()->request->getParam('id', 0);
        if ($id){
            if(EventMember::model()->deleteByPk($id)){
                $this->addMessage('state', 'success');
                $this->addMessage('refresh', true);
                $this->addMessage('message', '删除成功！');
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '删除失败！');
            }
            $this->showMessage();
        }
    }

    /**
     * 更新报名人员信息页面及保存处理
     * @param int $id
     * @param int $event_id
     */
    public function actionUpdateMember($id=0, $event_id=0)
    {
        if ($event_id){
            $this->layout='//layouts/dialog';

            $model = EventMember::model()->findByPk($id);
            if ($model === null)
                $model = new EventMember;

            if (isset($_POST['EventMember'])){
                $model->attributes = $_POST['EventMember'];
                $model->event_id = $event_id;
                $model->child_birthday = strtotime($_POST['EventMember']['child_birthday']);
                $model->timestamp=time();

                $count = 0;
                if($model->isNewRecord){
                    if ($model->tel || $model->email){
                        $criteria = new CDbCriteria;
                        $criteria->compare('event_id', $event_id);
                        $criteria->compare('tel', $model->tel);
                        $criteria->compare('email', $model->email, false, 'or');
                        $count = EventMember::model()->count($criteria);
                    }
                }

                if (!$count){
                    if ($model->save()){
                        $this->addMessage('state', 'success');
                        $this->addMessage('refresh', true);
                        $this->addMessage('message', Yii::t("global","Data Saved!"));
                    }
                    else {
                        $this->addMessage('state', 'fail');
                        $errs = current($model->getErrors());
                        $this->addMessage('message', $errs?$errs[0]:Yii::t('message','Failed!'));
                    }
                }
                else {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '电话或邮件已经存在！');
                }
                $this->showMessage();
            }

            $model->child_birthday = OA::formatDateTime($model->child_birthday);
            $this->render('updatemember', array('model'=>$model));
        }
    }

    /**
     * 导入活动名单（csv文件）页面及导入处理
     * @param int $id
     */
    public function actionImport($id=0)
    {
        $this->layout='//layouts/dialog';
        $this->dialogWidth = 600;

        $model = new EventImportForm;

        if (isset($_POST['EventImportForm'])){
            $model->attributes = $_POST['EventImportForm'];

            if ($model->validate()){
                $i=0;$j=0;
                $model->csv = CUploadedFile::getInstance($model, 'csv');
                if ($model->csv){
                    $fileName = uniqid().'.'.$model->csv->getExtensionName();
                    $model->csv->saveAs(Yii::app()->params['OAUploadBasePath'].'/'.$fileName);

                    if (is_file(Yii::app()->params['OAUploadBasePath'].'/'.$fileName)){

                        $flag = md5(uniqid());

                        $fileArray = file(Yii::app()->params['OAUploadBasePath'].'/'.$fileName);
                        @unlink(Yii::app()->params['OAUploadBasePath'].'/'.$fileName);

                        $items = EventMember::model()->findAllByAttributes(array('event_id'=>$id));
                        $theemail = array();
                        $thetel = array();
                        foreach ($items as $item){
                            $theemail[] = $item['email'];
                            $thetel[] = $item['tel'];
                        }

                        foreach($fileArray as $val){
                            $val = iconv('GB2312', 'UTF-8', $val);
                            $valArray = explode(',', $val);

                            if ($valArray[0] && $valArray[1] && $valArray[2] && !in_array($valArray[1], $thetel) && !in_array($valArray[5], $theemail)){
                                $mdl = new EventMember;
                                $mdl->event_id          = $id;
                                $mdl->parent_name       = $valArray[0];
                                $mdl->tel               = $valArray[1];
                                $mdl->child_birthday    = strtotime($valArray[2]);
                                $mdl->child_name        = $valArray[3];
                                $mdl->email             = $valArray[4];
                                $mdl->adult_number      = $valArray[5];
                                $mdl->kid_number        = $valArray[6];
                                $mdl->address           = $valArray[7];
                                $mdl->from              = $valArray[8];
                                $mdl->timestamp         = time();
                                $mdl->flag              = $flag;
                                if ($mdl->save()){
                                    $i++;
                                    if ($mdl->email)
                                        array_push($theemail, $mdl->email);
                                    if ($mdl->tel)
                                        array_push($thetel, $mdl->tel);
                                }
                                else {
                                    $j++;
                                }
                            }
                            else {
                                $j++;
                            }
                        }

                        $total = count($fileArray);
                        $eventImport = new EventMemberImport;
                        $eventImport->event_id=$id;
                        $eventImport->flag=$flag;
                        $eventImport->total=$total;
                        $eventImport->success=$i;
                        $eventImport->fail=$j;
                        $eventImport->timestamp=time();
                        $eventImport->uid=Yii::app()->user->id;
                        $eventImport->save();
                    }
                }

                $this->addMessage('state', 'success');
                $this->addMessage('refresh', true);
                $this->addMessage('message', '共 '.$total.'; 成功 '. $i . '; 失败 '.$j);
            }
            else {
                $this->addMessage('state', 'fail');
                $errs = current($model->getErrors());
                $this->addMessage('message', $errs?$errs[0]:Yii::t('message','Failed!'));
            }
            $this->showMessage();
        }

        $criteria = new CDbCriteria();
        $criteria->compare('event_id', $id);
        $dataProvider = new CActiveDataProvider('EventMemberImport', array(
            'criteria'=>$criteria,
        ));

        $this->render('import', array('model'=>$model, 'dataProvider'=>$dataProvider));
    }

    /**
     * 导出活动名单
     * @param int $id
     */
    public function actionExport($id=0)
    {
        $model = SchoolEvents::model()->findByPk($id);

        $criteria = new CDbCriteria();
        $criteria->compare('event_id', $id);
        $items = EventMember::model()->findAll($criteria);

        $knowus = array();
        foreach($this->cfg['knowus'] as $key=>$val){
            $knowus[$key]=  CommonUtils::autoLang($val['cn'], $val['en']);
        }
        ob_end_clean();
        header("Content-type:application/vnd.ms-excel");
        header("Content-Disposition:filename=".$model->code.".xls");

        echo _t('家长姓名')."\t";
        echo _t("电话")."\t";
        echo _t("邮件")."\t";
        echo _t("孩子姓名")."\t";
        echo _t("孩子生日")."\t";
        echo _t("成人")."\t";
        echo _t("孩子")."\t";
        echo _t("地址")."\t";
        echo _t("录入时间")."\t";
        echo _t("来源")."\n";
        foreach ($items as $item){
            echo _t($item->parent_name)."\t";
            echo _t($item->tel)."\t";
            echo _t($item->email)."\t";
            echo _t($item->child_name)."\t";
            echo _t(OA::formatDateTime($item->child_birthday))."\t";
            echo _t($item->adult_number)."\t";
            echo _t($item->kid_number)."\t";
            echo _t($item->address)."\t";
            echo _t(OA::formatDateTime($item->timestamp, 'medium', 'short'))."\t";
            echo _t(isset($item->knowus) ? $knowus[$item->knowus] : '' )."\n";
        }
    }

    /**
     * 删除整个导入（包括导入人员）
     */
    public function actionDeleteImport()
    {
        $id = Yii::app()->request->getParam('id', 0);
        if ($id){
            $model = EventMemberImport::model()->findByPk($id);
            if ($model->flag)
                EventMember::model()->deleteAllByAttributes(array('flag'=>$model->flag));
            if($model->delete()){
                $this->addMessage('state', 'success');
                $this->addMessage('refresh', true);
                $this->addMessage('message', Yii::t('message','success'));
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message','Failed!'));
            }
            $this->showMessage();
        }
    }

    /**
     * 导入活动名单到来访记录
     * @param int $id
     */
    public function actionToCrm($id=0)
    {
        $this->layout='//layouts/dialog';
        $this->dialogWidth = 600;

        if (!$id){
            die;
        }
        @set_time_limit(300);

        $event = SchoolEvents::model()->findByPk($id);

        $criteria = new CDbCriteria();
        $criteria->compare('event_id', $id);

        $cc = EventMember::model()->count($criteria);
        $rStr = '';
        $batchNum=100;
        $cycle=ceil($cc/$batchNum);
        for($i=0; $i<$cycle; $i++){
            $criteria->limit=$batchNum;
            $criteria->offset=$i*$batchNum;
            $items = EventMember::model()->findAll($criteria);
            foreach($items as $item){
                $crit = new CDbCriteria();
                $crit->compare('tel', $item->tel);
                if ($item->email)
                    $crit->compare('email', $item->email, false, 'or');
                $exist=IvyVisitsBasicInfo::model()->find($crit);
                if(!is_object($exist)){
                    $model = new IvyVisitsBasicInfo;
                    $model->child_name = $item->child_name;
                    $model->parent_name = $item->parent_name;
                    $model->birth_timestamp = $item->child_birthday;
                    $model->tel = $item->tel;
                    $model->email = $item->email;
                    $model->address = $item->address;
                    $model->knowus = $item->knowus;
                    $model->register_timestamp = time();
                    $model->state_timestamp = time();
                    $model->recent_log = time();
                    $model->status = 10;
                    $model->update_timestamp = time();
                    $model->update_user = Yii::app()->user->id;
                    if(!$model->save()){
                        $err = current($model->getErrors());
                    }

                    $modelR = new IvyVisitsRecord;
                    $modelR->basic_id = $model->id;
                    $modelR->event_id = $id;
                    $modelR->category = 'event';
                    $modelR->schoolid = $event->school_id;
                    $modelR->visit_timestamp = $event->event_date;
                    $modelR->parent_num = $item->adult_number;
                    $modelR->child_num = $item->kid_number;
                    $modelR->update_timestamp = time();
                    $modelR->update_user = Yii::app()->user->id;
                    $modelR->ip = $item->ip;
//                    $modelR->save();
                    if(!$modelR->save()){
                        $err = current($modelR->getErrors());
                    }

                    $criteria =new CDbCriteria;
                    $criteria->compare('basic_id',$model->id);
                    $criteria->compare('school_id',$modelR->schoolid);
                    $modelL = VisitsBasicLink::model()->find($criteria);
                    if(!$modelL){
                        $modelL = new VisitsBasicLink();
                        $modelL->basic_id = $model->id;
                        $modelL->school_id = $modelR->schoolid;
                    }
                    $modelL->status = $model->status;
                    $modelL->visit_id = $modelR->id;
                    $modelL->updated_at = time();
                    $modelL->updated_by = Yii::app()->user->id;
                    $modelL->save();

                    $rStr .= '<li class="text-success"><span class="glyphicon glyphicon-ok-circle"></span> '.$item->tel.' '.$item->parent_name.'，导入成功</li><ivy>';
                }
                else {
                    $criteria = new CDbCriteria();
                    $criteria->compare('event_id', $id);
                    $criteria->compare('basic_id', $exist->id);
                    $count = IvyVisitsRecord::model()->count($criteria);

                    if ($count){
                        $rStr .= '<li class="text-warning"><span class="glyphicon glyphicon-remove-circle"></span> '.$item->tel.' '.$item->parent_name.'，已经存在</li><ivy>';
                    }
                    else{

                        $existBak = new IvyVisitsBasicInfoBak();
                        $existBak->child_name = $exist->child_name;
                        $existBak->parent_name = $exist->parent_name;
                        $existBak->birth_timestamp = $exist->birth_timestamp;
                        $existBak->tel = $exist->tel;
                        $existBak->email = $exist->email;
                        $existBak->address = $exist->address;
                        $existBak->communication = $exist->communication;
                        $existBak->child_enroll = $exist->child_enroll;
                        $existBak->country = $exist->country;
                        $existBak->knowus = $exist->knowus;
                        $existBak->concerns = $exist->concerns;
                        $existBak->memo = $exist->memo;
                        $existBak->register_timestamp = $exist->register_timestamp;
                        $existBak->status = $exist->status;
                        $existBak->childid = $exist->childid;
                        $existBak->update_timestamp = $exist->update_timestamp;
                        $existBak->update_user = $exist->update_user;
                        $existBak->save();

                        $exist->child_name = $item->child_name;
                        $exist->parent_name = $item->parent_name;
                        $exist->birth_timestamp = $item->child_birthday;
                        $exist->tel = $item->tel;
                        $exist->email = $item->email;
                        $exist->address = $item->address;
                        $exist->knowus = $item->knowus;
                        $exist->register_timestamp = time();
                        $exist->state_timestamp = time();
                        $exist->recent_log = time();
                        $exist->status = 10;
                        $exist->update_timestamp = time();
                        $exist->update_user = Yii::app()->user->id;
                        $exist->save();

                        $modelR = new IvyVisitsRecord;
                        $modelR->basic_id = $exist->id;
                        $modelR->event_id = $id;
                        $modelR->category = 'event';
                        $modelR->schoolid = $event->school_id;
                        $modelR->visit_timestamp = $event->event_date;
                        $modelR->parent_num = $item->adult_number;
                        $modelR->child_num = $item->kid_number;
                        $modelR->update_timestamp = time();
                        $modelR->update_user = Yii::app()->user->id;
                        $modelR->ip = $item->ip;
                        $modelR->save();

                        $modelTraceLog = new VisitsTraceLog();
                        $modelTraceLog->basic_id = $modelR->basic->id;
                        $modelTraceLog->visit_id = $modelR->id;
                        $modelTraceLog->type = 10;
                        $modelTraceLog->content = '活动导入';
                        $modelTraceLog->schoolid = $this->branchId;
                        $modelTraceLog->update_timestamp = time();
                        $modelTraceLog->update_user = Yii::app()->user->id;
                        $modelTraceLog->save();


                        $rStr .= '<li class="text-success"><span class="glyphicon glyphicon-ok-circle"></span> '.$item->tel.' '.$item->parent_name.'，导入成功</li><ivy>';
                    }
                }
            }
        }

        $this->render('tocrm', array('rStr'=>$rStr));
    }

    /**
     * 更新艾毅家长报名人员信息页面及保存处理
     * @param int $id
     * @param int $event_id
     */
    public function actionUpdateMemberivy($id=0, $event_id=0)
    {
        if ($event_id){
            $this->layout='//layouts/dialog';
            $this->dialogWidth = 600;

            $model = EventMemberIvy::model()->findByPk($id);
            if ($model === null)
                $model = new EventMemberIvy;

            if (isset($_POST['EventMemberIvy'])){
                $model->attributes = $_POST['EventMemberIvy'];
                $model->event_id = $event_id;
                $model->timestamp=time();

                $count = 0;
                if($model->isNewRecord){
                    if ($model->telphone){
                        $criteria = new CDbCriteria;
                        $criteria->compare('event_id', $event_id);
                        $criteria->compare('telphone', $model->telphone);
                        $count = EventMemberIvy::model()->count($criteria);
                    }
                }

                if ($count){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '电话已经存在！');
                }
                else {
                    if ($model->save()){
                        $this->addMessage('state', 'success');
                        $this->addMessage('refresh', true);
                        $this->addMessage('message', Yii::t("global","Data Saved!"));
                    }
                    else {
                        $this->addMessage('state', 'fail');
                        $errs = current($model->getErrors());
                        $this->addMessage('message', $errs?$errs[0]:Yii::t('message','Failed!'));
                    }
                }
                $this->showMessage();
            }

            $this->render('updatememberivy', array('model'=>$model));
        }
    }

    /**
     * 删除艾毅家长报名人员
     */
    public function actionDeleteMemberivy()
    {
        $id = Yii::app()->request->getParam('id', 0);
        if ($id){
            if(EventMemberIvy::model()->deleteByPk($id)){
                $this->addMessage('state', 'success');
                $this->addMessage('refresh', true);
                $this->addMessage('message', '删除成功！');
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '删除失败！');
            }
            $this->showMessage();
        }
    }

    /**
     * 导出艾毅家长报名名单
     * @param int $id
     */
    public function actionExportivy($id=0)
    {
        $model = SchoolEvents::model()->findByPk($id);

        $criteria = new CDbCriteria();
        $criteria->compare('event_id', $id);
        $items = EventMemberivy::model()->findAll($criteria);

        header("Content-type:application/vnd.ms-excel");
        header("Content-Disposition:filename=".$model->code.".xls");

        echo _t('家长姓名')."\t";
        echo _t("电话")."\t";
        echo _t("邮箱")."\t";
        echo _t("孩子姓名")."\t";
        echo _t("成人")."\t";
        echo _t("孩子")."\t";
        echo _t("录入时间")."\n";
        foreach ($items as $item){
            echo _t($item->parent_name)."\t";
            echo _t($item->telphone)."\t";
            echo _t($item->email)."\t";
            echo _t($item->child_name)."\t";
            echo _t($item->adult_number)."\t";
            echo _t($item->kid_number)."\t";
            echo _t(OA::formatDateTime($item->timestamp, 'medium', 'short'))."\n";
        }
    }

    public function getButtons($data)
    {
        echo '<a class="btn btn-info btn-xs" title="查看报名名单" href="' . $this->createUrl("viewMember", array("id" => $data->id)) . '"><i class="glyphicon glyphicon-user"></i></a>' . " ";
        echo '<a class="J_dialog btn btn-info btn-xs" title="更新" href="' . $this->createUrl("update", array("id" => $data->id)) . '"><i class="glyphicon glyphicon-pencil"></i></a>' . " ";
        echo '<a class="J_ajax_del btn btn-danger btn-xs" title="删除" href="' . $this->createUrl("delete", array("id" => $data->id)) . '"><i class="glyphicon glyphicon-remove"></i></a>' . " ";
    }

    public function getViewButtons($data)
    {
        echo '<a class="J_dialog btn btn-info btn-xs" title="更新" href="' . $this->createUrl("updateMember", array("id" => $data->id, "event_id"=> $data->event_id)) . '"><i class="glyphicon glyphicon-pencil"></i></a>' . " ";
        echo '<a class="J_ajax_del btn btn-danger btn-xs" title="删除" href="' . $this->createUrl("deleteMember", array("id" => $data->id)) . '"><i class="glyphicon glyphicon-remove"></i></a>' . " ";
    }

    public function getViewIvyButtons($data)
    {
        echo '<a class="J_dialog btn btn-info btn-xs" title="更新" href="' . $this->createUrl("updateMemberivy", array("id" => $data->id, "event_id"=> $data->event_id)) . '"><i class="glyphicon glyphicon-pencil"></i></a>' . " ";
        echo '<a class="J_ajax_del btn btn-danger btn-xs" title="删除" href="' . $this->createUrl("deleteMemberivy", array("id" => $data->id)) . '"><i class="glyphicon glyphicon-remove"></i></a>' . " ";
    }

}



function _t($str='')
{
    return iconv("UTF-8", "GBK", $str);
}
