<style>
    <!--
    .photos {
        margin-top: 20px;
    }
    ul, ol {
        list-style-type: none;
        margin: 0;
        padding: 0;
    }
    .photos ul li a {
        display: block;
        height: 130px;
        margin: 4px;
        width: 180px;
    }
    .photos ul li {
        border: 1px solid #D8D8D8;
        float: left;
        margin-bottom: 20px;
        margin-right: 20px;
        position: relative;
    }
    .photos ul li:hover {
        border-color: #9A9FA4;
        box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.85);
    }
    .photos ul li:hover a.btn {
        display: block;
    }
    .photos ul li:hover a.btn:hover {
        background-position: 147px 25px;
    }
    .photos ul li a.btn:first-of-type {
        background-position: 30px 0;
        left: 0;
        right: auto;
    }
    .photos ul li a.btn:first-of-type:hover {
        background-position: 30px 25px;
    }
    .photos ul li a.cover {
        background-position: 30px 25px !important;
        display: block;
    }
    .photos ul li a span.thumb {
        display: block;
        height: 100%;
        width: 100%;
        background-position: center center;
        background-repeat: no-repeat;
    }
    .photos ul li a span.title {
        background: none repeat scroll 0 0 #FAFAFA;
        bottom: 0;
        display: block;
        height: 25px;
        line-height: 25px;
        overflow: hidden;
        position: absolute;
        text-indent: 3px;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 100%;
    }
    .photos ul li a.btn {
        background-color: transparent;
        background-image: url("/images/controls.png");
        background-position: 147px 0;
        border: 0 none;
        cursor: pointer;
        display: none;
        height: 24px;
        position: absolute;
        right: 0;
        top: 0;
        width: 24px;
        padding:0;
    }
    .photos ul li a.cover {
        background-position: 30px 25px !important;
        display: block;
    }
    .plupload_filelist_footer{
        height: auto !important;
    }
    .plupload_scroll .plupload_filelist{
        height: 100px !important;
    }
    -->
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','HQ Operations'), array('default/index'));?></li>
        <li><?php echo CHtml::link(Yii::t('site','Support'), array('default/index'));?></li>
        <li><?php echo CHtml::link(Yii::t('site','Exchange Items'), array('admin'));?></li>
        <li class="active"><?php echo $model->cn_title ;?></li>
        <?php if(!isset($_GET['id'])) echo '新增商品' ?>
    </ol>
    <?php $form=$this->beginWidget('CActiveForm', array(
        'id'=>'points-product-form',
        'enableAjaxValidation'=>false,
        'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
    )); ?>
    <div class="pop_cont">
        <div class="form-group">
            <label class="col-md-2 col-xs-4 control-label"><?php echo $form->labelEx($model,'en_title'); ?></label>
            <div class="col-md-4 col-xs-8">
                <?php echo $form->textField($model,'en_title',array('maxlength'=>255,'class'=>'form-control')); ?>
            </div>
        </div>
        <div class="form-group">
            <label class="col-md-2 col-xs-4 control-label"><?php echo $form->labelEx($model,'cn_title'); ?></label>
            <div class="col-md-4 col-xs-8">
                <?php echo $form->textField($model,'cn_title',array('maxlength'=>255,'class'=>'form-control')); ?>
            </div>
        </div>
        <div class="form-group">
            <label class="col-md-2 col-xs-4 control-label"><?php echo $form->labelEx($model,'credits'); ?></label>
            <div class="col-md-4 col-xs-8">
                <?php echo $form->textField($model,'credits',array('maxlength'=>255,'class'=>'form-control')); ?>
            </div>
        </div>
        <div class="form-group">
            <label class="col-md-2 col-xs-4 control-label"><?php echo $form->labelEx($model,'en_memo'); ?></label>
            <div class="col-md-4 col-xs-8">
                <?php echo $form->textArea($model,'en_memo',array('class'=>'form-control', 'rows'=>5)); ?>
            </div>
        </div>
        <div class="form-group">
            <label class="col-md-2 col-xs-4 control-label"><?php echo $form->labelEx($model,'cn_memo'); ?></label>
            <div class="col-md-4 col-xs-8">
                <?php echo $form->textArea($model,'cn_memo',array('class'=>'form-control', 'rows'=>5)); ?>
            </div>
        </div>
        <div class="form-group">
            <label class="col-md-2 col-xs-4 control-label"><?php echo $form->labelEx($model,'p_price'); ?></label>
            <div class="col-md-4 col-xs-8">
                <?php echo $form->textField($model,'p_price',array('maxlength'=>255,'class'=>'form-control')); ?>
            </div>
        </div>
        <div class="form-group">
            <label class="col-md-2 col-xs-4 control-label"><?php echo $form->labelEx($model,'m_price'); ?></label>
            <div class="col-md-4 col-xs-8">
                <?php echo $form->textField($model,'m_price',array('maxlength'=>255,'class'=>'form-control')); ?>
            </div>
        </div>
        <div class="form-group">
            <label class="col-md-2 col-xs-4 control-label"><?php echo $form->labelEx($model,'status'); ?></label>
            <div class="col-md-4 col-xs-8">
                <?php echo $form->dropDownList($model,'status', array(Yii::t('points', '隐藏'),Yii::t('points', '显示')), array('class'=>'select_2 form-control', 'empty'=>Yii::t("global", 'Please Select'))); ?>
            </div>
        </div>
        <!-- 照片管理 -->
        <?php  if (isset($_GET['id'])) {
            ?>
            <div class="form-group">
                <label class="col-md-2 col-xs-4 control-label"></label>
                <div class="col-md-4 col-xs-8">
                    <button id="photoup" type="button" class="btn btn-primary"><?php echo Yii::t('points','上传照片');?></button>
                    <div id="photos" class="photos">
                        <ul>
                            <?php foreach ($imageModel as $item):?>
                                <li id="pic_<?php echo $item->id?>">
                                    <?php
                                    $hrefto=$this->createUrl('/operations/product/pic/', array('id'=>$item->id)); // 图片排序功能 暂时停用
                                    ?>
                                    <a class="btn <?php if ($model->cover == $item->id):?>cover<?php endif;?>" href="javascript:;" onclick="cover(<?php echo $item->id?>)" title="<?php if ($model->cover == $item->id):echo Yii::t("points", "封面");else:echo Yii::t("points", "设为封面");endif;?>" id="cover_<?php echo $item->id?>"></a>
                                    <a class="btn" href="javascript:;" onclick="delpic(<?php echo $item->id?>)" title="<?php echo Yii::t("points", "删除")?>"></a>
                                    <a href="javascript:;" class="" title="<?php echo $model->getContent()?>">
                                        <span class="thumb" style="background-image: url('<?php echo $imageUrl.$item->image?>');"></span>
                                    </a>
                                </li>
                            <?php endforeach;?>
                        </ul>
                    </div>
                </div>
            </div>
        <?php
        } ?>
    </div>
    <div class="pop_bottom">
        <label class="col-md-2 col-xs-4 control-label"></label>
        <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
    </div>
    <?php $this->endWidget(); ?>
</div>
<!-- JS代码 -->
<script type="text/javascript">

    function delpic(id)
    {
        $.post('<?php echo $this->createUrl("/moperation/product/delpic")?>', {id:id}, function(data){
            if(data.status == 'success'){
                $('#pic_'+id).remove();
            }
            else {
                alert(data.message)
            }
        }, 'json');
    }
    function cover(id)
    {
        $.post('<?php echo $this->createUrl("/moperation/product/cover")?>', {id:id}, function(data){
            if(data.status == 'success'){
                $('.photos ul a.btn').removeClass('cover');
                $('#cover_'+id).addClass('cover');
            }
        }, 'json');
    }
</script>
<script type="text/javascript">
    var uploader = new plupload.Uploader({//创建实例的构造方法
        runtimes: 'html5,flash,silverlight,html4', //上传插件初始化选用那种方式的优先级顺序
        browse_button: 'photoup', // 上传按钮
        url: '<?php echo $this->createUrl('Upload', array('productid'=>$productid)); ?>', //远程上传地址
        flash_swf_url: 'plupload/Moxie.swf', //flash文件地址
        silverlight_xap_url: 'plupload/Moxie.xap', //silverlight文件地址
        filters: {
            max_file_size: '5000kb', //最大上传文件大小（格式100b, 10kb, 10mb, 1gb）
            mime_types: [//允许文件上传类型
                {title: "files", extensions: "jpeg,jpg,png,gif"}
            ]
        },
        multi_selection: true, //true:ctrl多文件上传, false 单文件上传
        init: {
            FilesAdded: function(up, files) { //文件上传前
                uploader.start();
            },
            UploadProgress: function(up, file) { //上传中，显示进度条
            },
            FileUploaded: function(up, file, response) { //文件上传成功的时候触发
                var obj = jQuery.parseJSON(response.response);
                console.log(obj);
                if(obj.result == 1){
                    var html = "<li id='pic_"+obj.id+"'>";
                    html+= '<a class="btn" href="javascript:;" onclick="cover('+obj.id+')" id="cover_'+obj.id+'"></a>';
                    html+= '<a class="btn" href="javascript:;" onclick="delpic('+obj.id+')"></a>';
                    html += "<a style='background-image: url("+obj.pic+")' class='J_dialog' href='<?php echo $hrefto=$this->createUrl('/operations/product/pic');?>/id/"+obj.id+"'>";
                    html += "</a></li>";
                    $('#photos ul').prepend(html);
                    //$('#pic_'+obj.id+' a.dialogform').formDialog({'onSuccess':function(data, e){alert(data.message)}});
                }
            },
            Error: function(up, err) { //上传出错的时候触发
                alert(err.message);
            }
        }
    });
    uploader.init();
</script>