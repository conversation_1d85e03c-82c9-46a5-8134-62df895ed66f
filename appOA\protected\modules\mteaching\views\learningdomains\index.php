<div class="container-fluid">
    <!-- 面包屑导航 -->
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Teaching Tasks'), array('//mteaching/learningdomains/index')) ?></li>
        <li class="active"><?php echo Yii::t('curriculum','教学领域');?></li>

    </ol>
    <div class="row">
        <!-- 左侧菜单栏 -->
        <div class="col-md-2">
            <div class="list-group">
                <?php
                foreach ($this->typeList as $key => $value) {
                    $active = ($key == $_GET['type']) ? 'active' : '';
                    echo CHtml::link(Yii::t('curriculum', $value), array('index', 'type' => $key), array('class' => 'list-group-item status-filter ' . $active));
                }
                ?>
            </div>
        </div>
        <div class="col-md-10">
            <ul class="nav nav-tabs">
                <li role="ivy" class="<?php echo ('ivy' == $this->schoolType) ? 'active' : '' ?>">
                    <a href="<?php echo $this->createUrl('index', array('school_type'=>'ivy')); ?>">艾毅</a>
                </li>
                <li role="qf" class="<?php echo ('qf' == $this->schoolType) ? 'active' : '' ?>">
                    <a href="<?php echo $this->createUrl('index', array('school_type'=>'qf')); ?>">泉发</a>
                </li>
            </ul>
            <br>
            <?php echo CHtml::link(Yii::t('curriculum', '新建领域'), array('updateLearning', 'type' => $_GET['type']), array('class' => 'btn btn-primary J_modal')); ?>
            <?php $this->widget('ext.ivyCGridView.BsCGridView', array(
                'id' => 'learning-grid',
                'dataProvider' => $models,
                'afterAjaxUpdate' => 'js:head.Util.modal',
                'colgroups' => array(
                    array(
                        "colwidth" => array(50, 50, 50, 50, 80),
                    )
                ),
                'columns' => array(
                    'title_cn',
                    'title_en',
                    array(
                        'name' => 'type',
                        'value' => array($this, 'getTypeName'),
                    ),
                    array(
                        'name' => 'updated_at',
                        'value' => 'date("Y-m-d H:i:s", $data->updated_at)',
                    ),
                    array(
                        'name' => Yii::t('curriculum', 'Operation'),
                        'value' => array($this, 'getButton'),
                    ),
                ),
            ));
            ?>
        </div>
    </div>
</div>

<script>
    var modal = '<div class="modal fade" id="modal" tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog modal-lg" role="document"><div class="modal-content"></div></div></div>';
    $('body').append(modal);

    function cb_modal() {
        $('#modal').modal('hide');
        $.fn.yiiGridView.update('learning-grid', {
            complete: function() {
                head.Util.modal()
                head.Util.ajaxDel()
            }
        });
    }

    function cb_del(params) {
        $.fn.yiiGridView.update('learning-grid', {
            complete: function() {
                head.Util.modal()
                head.Util.ajaxDel()
            }
        });
    }
</script>