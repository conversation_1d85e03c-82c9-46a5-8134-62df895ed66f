<?php

class LearningdomainsController extends ProtectedController
{
    public $actionAccessAuths = array(
        'index'           => 'o_E_Edu_Common',
    );

    public $leftMenu;
    public $typeList;
    public $version = 2;
    public $schoolType = 'ivy';

    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'teaching';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Campus Workspace');

        //初始化选择校园页面
        // $this->branchSelectParams['hideOffice'] = true;
        // $this->branchSelectParams['urlArray'] = array('//mcampus/curriculum/project');

        //左侧菜单栏
        $this->leftMenu = array(
            array('project', Yii::t('curriculum', 'Theme List')),
            array('activity', Yii::t('curriculum', 'Activity List')),
        );

        $this->typeList = IvyClass::getClassTypes();
        if (isset($this->typeList['c'])) {
            unset($this->typeList['c']);
        }

        if ($_GET['school_type']) {
            $this->schoolType = $_GET['school_type'];
            if ($this->schoolType == 'qf') {
                $this->typeList = IvyClass::getClassTypes(true, 30);
            }
        }

        Yii::import('common.models.learning.*');
    }

    public function createUrl($route, $params = array(), $ampersand = '&', $parentOnly = false)
    {
        if (empty($params['school_type'])) {
            $params['school_type'] = $this->schoolType;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    // 显示所有领域
    public function actionIndex()
    {
        $type = Yii::app()->request->getParam('type');
        $criteria = new CDbCriteria;
        if ($type) {
            $criteria->compare('type', $type);
        }
        $criteria->compare('status', 1);
        $criteria->compare('version', $this->version);
        $criteria->compare('school_type', $this->schoolType);
        $criteria->order = 'sort ASC';
        $models = new CActiveDataProvider('LearningDomains', array('criteria' => $criteria, 'pagination' => array("pagesize" => 20),));
        $this->render('index', array('models' => $models));
    }

    // 新建更新领域
    public function actionUpdateLearning()
    {
        $id = Yii::app()->request->getParam('id');
        $type = Yii::app()->request->getParam('type');

        $model = '';
        if ($id) {
            $model = LearningDomains::model()->findByPk($id);
        }
        if (!$model) {
            $model = new LearningDomains();
            $model->type = $type;
            $model->status = 1;
        }
        if ($_POST['LearningDomains']) {
            $model->attributes = $_POST['LearningDomains'];
            $model->version = $this->version;
            $model->school_type = $this->schoolType;
            $model->updated_by = $this->staff->uid;
            $model->updated_at = time();
            if (!$model->save()) {
                $error = current($model->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('message', $error[0]);
                $this->showMessage();
            }
            $this->addMessage('state', 'success');
            $this->addMessage('callback', 'cb_modal');
            $this->addMessage('message', 'success');
            $this->showMessage();
        }
        $this->renderPartial('update', array('model' => $model, 'typeList' => $this->typeList));
    }

    // 领域排序
    public function actionSortLearning()
    { }

    // 删除领域
    public function actionDelLearning()
    {
        $id = Yii::app()->request->getParam('id');
        $model = '';
        if ($id) {
            $model = LearningDomains::model()->findByPk($id);
        }
        if (!$model) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'fail');
            $this->showMessage();
        }
        $model->status = 0;
        $model->updated_at = time();
        $model->updated_by = $this->staff->uid;
        $model->save();
        $this->addMessage('state', 'success');
        $this->addMessage('message', 'success');
        $this->addMessage('callback', 'cb_del');
        $this->showMessage();
    }

    // 指定领域下面的所有子项
    public function actionItems($pid)
    {
        if (!$pid) {
            return false;
        }
        $model = LearningDomains::model()->findByPk($pid);
        if (!$model) {
            return false;
        }
        $criteria = new CDbCriteria();
        $criteria->compare('pid', $pid);
        $criteria->compare('status', 1);
        $criteria->order = 'sort ASC';
        $dataProvieder = new CActiveDataProvider('LearningDomainsItems', array('criteria' => $criteria, 'pagination' => array("pagesize" => 20)));
        $this->render('items', array('dataProvieder' => $dataProvieder, 'model' => $model));
    }

    // 新建更新子项
    public function actionUpdateItems()
    {
        $pid = Yii::app()->request->getParam('pid');
        $id = Yii::app()->request->getParam('id');

        if (!$pid) {
            return false;
        }
        $model = '';
        if ($id) {
            $model = LearningDomainsItems::model()->findByPk($id);
        }
        if (!$model) {
            $model = new LearningDomainsItems();
            $model->status = 1;
        }
        if ($_POST['LearningDomainsItems']) {
            $model->attributes = $_POST['LearningDomainsItems'];
            $model->pid = $pid;
            $model->updated_by = $this->staff->uid;
            $model->updated_at = time();
            if (!$model->save()) {
                $error = current($model->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('message', $error[0]);
                $this->showMessage();
            }
            $this->addMessage('state', 'success');
            $this->addMessage('callback', 'cb_modal');
            $this->addMessage('message', 'success');
            $this->showMessage();
        }
        $this->renderPartial('updateItem', array('model' => $model, 'typeList' => $this->typeList));
    }

    // 子项排序
    public function actionSortItems()
    {
        # code...
    }

    // 删除子项
    public function actionDelItems()
    {
        $id = Yii::app()->request->getParam('id');
        $model = '';
        if ($id) {
            $model = LearningDomainsItems::model()->findByPk($id);
        }
        if (!$model) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'fail');
            $this->showMessage();
        }
        $model->status = 0;
        $model->updated_at = time();
        $model->updated_by = $this->staff->uid;
        $model->save();
        $this->addMessage('state', 'success');
        $this->addMessage('message', 'success');
        $this->addMessage('callback', 'cb_del');
        $this->showMessage();
    }

    public function getButton($data)
    {
        // 查看按钮
        echo CHtml::link(Yii::t('curriculum', 'View'), array('items', 'pid' => $data->id, 'type' => $data->type), array('class' => 'btn btn-primary btn-xs')) . ' ';
        // 编辑按钮
        echo CHtml::link(Yii::t('curriculum', 'Edit'), array('updateLearning', 'id' => $data->id), array('class' => 'btn btn-primary btn-xs J_modal')) . ' ';
        // 删除按钮
        echo CHtml::link(Yii::t('curriculum', 'Delete'), array('delLearning', 'id' => $data->id), array('class' => 'btn btn-danger btn-xs J_ajax_del')) . ' ';
    }

    public function getItemButton($data)
    {
        // 编辑按钮
        echo CHtml::link(Yii::t('curriculum', 'Edit'), array('updateItems', 'pid' => $data->pid , 'id' => $data->id), array('class' => 'btn btn-primary btn-xs J_modal')) . ' ';
        // 删除按钮
        echo CHtml::link(Yii::t('curriculum', 'Delete'), array('delItems', 'id' => $data->id), array('class' => 'btn btn-danger btn-xs J_ajax_del')) . ' ';
    }


    public function getTypeName($data)
    {
        if (isset($this->typeList[$data->type])) {
            echo $this->typeList[$data->type];
        }
    }
}
