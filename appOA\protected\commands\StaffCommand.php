<?php

class StaffCommand extends CConsoleCommand {

    public function init()
    {
    	Yii::import('common.models.staff.*');
    	Yii::import('common.models.hr.*');
    }

    // 获取某天变更职位的人，给指定的人发送邮件
    //  $time 0点的时间戳
    public function actionPositionChange($time=0)
    {
        $time = (!$time) ? time() : $time;
        $time = $time  - 86400 * 3;
        Yii::app()->language = "zh_cn";
        $criteria = new CDbCriteria();
        $criteria->compare('operating_day', ">=$time");
        $criteria->compare('status', 0);
        $model = PositionChangeRecord::model()->findAll($criteria);
        if($model) {
            $positionChangeInfo = array();
            $userId = array();
            $positionId = array();
            foreach ($model as $val) {
                if($val->old_position != $val->new_position) {
                    $positionChangeInfo[$val->schoolid][$val->uid] = $val;
                    $userId[$val->uid] = $val->uid;
                    $userId[$val->updated_by] = $val->updated_by;
                    $positionId[$val->old_position] = $val->old_position;
                    $positionId[$val->new_position] = $val->new_position;
                }
                $val->status = 1;
                $val->updated_at = time();
                $val->save();
            }

            // 所有变更的职位名称
            $criteria = new CDbCriteria();
            $criteria->compare('id', $positionId);
            $criteria->index = 'id';
            $data['hrModel'] = HrPosition::model()->findAll($criteria);

            // 所有变更人员的名字
            $criteria = new CDbCriteria();
            $criteria->compare('uid', $userId);
            $criteria->index = 'uid';
            $data['userModel'] = User::model()->findAll($criteria);

            $branchData = Branch::model()->getTitleEmail();
            if($positionChangeInfo) {
                foreach ($positionChangeInfo as $schoolid => $val) {
                    $data['campus'] = $branchData[$schoolid]['title'];
                    $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
                    $mailer->Subject = $data['campus'] . "职位变更人员名单";
                    $mailer->pushTo('position', $schoolid);
                    $data['info'] = $val;
                    $mailer->getCommandView('positionChange', array(
                        'data' => $data,
                    ), 'main');
                    $isProduction = defined("IS_PRODUCTION") ? IS_PRODUCTION : false;
                    $mailer->iniMail($isProduction);
                    if ($mailer->Send()) {
                        echo $schoolid . " - success \n";
                    } else {
                        echo $schoolid . " - error \n";
                        //echo $mailer->ErrorInfo;
                    }
                }
            }
        }
    }
}
