<?php

/**
 * This is the model class for table "ivy_asa_schedule".
 *
 * The followings are the available columns in table 'ivy_asa_schedule':
 * @property integer $id
 * @property string $title
 * @property string $schoolid
 * @property integer $program_id
 * @property integer $startyear
 * @property integer $week_day
 * @property string $default_time_start
 * @property string $default_time_end
 * @property integer $total_count
 * @property integer $state
 * @property integer $updated
 * @property integer $updated_by
 * @property string $week_multiple
 */
class AsaSchedule extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_asa_schedule';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('title, schoolid, program_id, week_day, total_count, state, updated, updated_by', 'required'),
			array('program_id, week_day, total_count, state, updated, updated_by, startyear', 'numerical', 'integerOnly'=>true),
			array('title, schoolid, week_multiple', 'length', 'max'=>255),
			array('default_time_start, default_time_end', 'length', 'max'=>5),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, title, schoolid, program_id, week_day, default_time_start, default_time_end, total_count, state, updated, updated_by, week_multiple, startyear', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'courses' => array(self::HAS_MANY, 'AsaCourse', array('schedule_id'=>'id')),
		    'asaScheduleItem' => array(self::HAS_MANY, 'AsaScheduleItem', array('schedule_id'=>'id')),
			'items' => array(self::HAS_MANY, 'AsaScheduleItem', array('schedule_id'=>'id')),
		);

	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'title' => Yii::t('asainvoice', '标题'),
			'schoolid' => 'Schoolid',
			'program_id' => 'Program',
			'week_day' => Yii::t('asainvoice', '周几'),
			'default_time_start' => Yii::t('asainvoice', '默认开始时间'),
			'default_time_end' => Yii::t('asainvoice', '默认结束时间'),
			'total_count' => Yii::t('asainvoice', '总课时'),
			'state' => 'State',
			'updated' => 'Updated',
			'updated_by' => 'Updated By',
			'week_multiple' => 'Week Multiple',
			'startyear' => 'Share Year',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('title',$this->title,true);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('program_id',$this->program_id);
		$criteria->compare('week_day',$this->week_day);
		$criteria->compare('default_time_start',$this->default_time_start,true);
		$criteria->compare('default_time_end',$this->default_time_end,true);
		$criteria->compare('total_count',$this->total_count);
		$criteria->compare('state',$this->state);
		$criteria->compare('updated',$this->updated);
		$criteria->compare('updated_by',$this->updated_by);
		$criteria->compare('week_multiple',$this->week_multiple);
		$criteria->compare('sahre_year',$this->startyear);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AsaSchedule the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
