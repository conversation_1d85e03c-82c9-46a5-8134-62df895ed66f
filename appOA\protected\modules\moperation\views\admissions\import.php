<?php
$this->widget('ext.ivyCGridView.BsCGridView', array(
	'id'=>'import-member-grid',
	'dataProvider'=>$dataProvider,
    'hideHeader'=>true,
    'colgroups'=>array(
        array(
            "colwidth"=>array(),
        )
    ),
	'columns'=>array(
        array(
            'name'=>'timestamp',
            'value'=>'OA::formatDateTime($data->timestamp, "medium", "short")',
        ),
        'total',
        'success',
        'fail',
        array(
            'class'=>'BsCButtonColumn',
            'template' => '{delete}',
            'deleteButtonUrl'=>'Yii::app()->createUrl("/moperation/admissions/deleteImport", array("id" => $data->id))',
        ),
	),
)); ?>

<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'log-form',
	'enableAjaxValidation'=>false,
	'htmlOptions'=>array('class'=>'J_ajaxForm'),
));
?>
<div class="pop_cont" style="height:auto;">
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'csv'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->fileField($model,'csv'); ?>
        </div>
    </div>
</div>
<div class="pop_bottom">
    <button id="J_dialog_close" type="button" class="btn btn-default pull-right"><?php echo Yii::t('global','Cancel');?></button>
    <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global','Submit');?></button>
</div>
<?php $this->endWidget(); ?>