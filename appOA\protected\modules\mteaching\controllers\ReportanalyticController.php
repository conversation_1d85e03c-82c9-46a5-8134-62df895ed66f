<?php

class ReportanalyticController extends BranchBasedController
{
	public $printFW = array();
	public $type = '';

    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    // 访问action的初级权限
    public $actionAccessAuths = array(
        'index'              => 'o_MS_Rrport',
    );

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'teaching';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Teaching Tasks');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mteaching/reportanalytic/index');
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile($cs->getCoreScriptUrl().'/jui/js/jquery-ui-i18n.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue2.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/xlsx.full.min.js');

        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/excellentexport.js');
         $cs->registerScriptFile('https://cdn.datatables.net/1.10.19/js/jquery.dataTables.min.js');
        $cs->registerScriptFile('https://cdn.datatables.net/1.10.19/js/dataTables.bootstrap.min.js');
        $cs->registerCssFile('https://cdn.datatables.net/1.10.19/css/dataTables.bootstrap.min.css');

        Yii::import('common.models.portfolio.*');
        Yii::import('common.models.secondary.*');
        Yii::import('common.models.timetable.*');
        Yii::import('common.models.calendar.*');
    }

    /*
     * $tid
     *  array(
     *      'id' => 01
     *      'title' => Language & Literature
     *      'items' => array(
     *          '01xx1' => array(
     *              [0] => Array
                        (
                            [course_code] => 01061
                            [course_title] => English Language and Literature
                            [course_title_en] => English Language and Literature
                        )

                    [1] => Array
                        (
                            [course_code] => 01071
                            [course_title] => English Language and Literature
                            [course_title_en] => English Language and Literature
                        )
     *          )
     *      )
     *  )
     *
     */
    public function actionIndex()
    {
        $this->getCalendars();
        $type = Yii::app()->request->getParam('type', ''); //tid
        $yid = Yii::app()->request->getParam('yid', $this->calendarYids['currentYid']);

        $calenderList = $this->getCalendarList();

        // 获取课程分类
        $courseType = TimetableCourses::getCourseType();
        $courseModel = array();

        if(!Yii::app()->user->checkAccess('o_MS_RrportMgt') && !Yii::app()->user->checkAccess('ivystaff_cd')) {
            $criteria = new CDbCriteria;
            $criteria->compare('yid', $yid);
            $criteria->compare('schoolid', $this->branchId);
            $criteria->compare('status', 1);
            $timeTableModel = Timetable::model()->find($criteria);

            if ($timeTableModel) {
                $criteria = new CDbCriteria;
                $criteria->compare('tid', $timeTableModel->id);
                $criteria->compare('status', 1);
                $criteria->compare('teacher_id', Yii::app()->user->id);
                $criteria->index = 'course_id';
                $teacherModel = TimetableCourseTeacher::model()->findAll($criteria);

                $courseTypeNew = array();
                $courseListId = array();
                if ($teacherModel) {
                    foreach ($teacherModel as $val) {
                        $courses = substr($val->course->program, 0, 2);
                        $courseTypeNew[$courses] = $courseType[$courses];
                        $courseListId[$val->course_id] = $val->course_id;
                    }
                    ksort($courseTypeNew);
                    $courseType = $courseTypeNew;
                    $courseModel = TimetableCourses::model()->findAllByPk($courseListId);
                }else{
                    $courseType = array();
                }
            }
        }else{
            $criteria = new CDbCriteria;
            $criteria->compare('status', 1);
            $criteria->index = 'program';
            $courseModel = TimetableCourses::model()->findAll($criteria);
        }

        $data = array();
        if($courseType && $courseModel) {
            foreach ($courseType as $key => $item) {
                $items = array();
                $i = 0;
                foreach ($courseModel as $k => $course) {
                    $typeid = substr($course->program, 0, 2);
                    $endid = substr($course->program, 4, 2);
                    $typeData = $typeid . 'xx' . $endid;
                    if (!$items[$typeData]) {
                        $i = 0;
                    }
                    if ($key == $typeid) {
                        $items[$typeData][$i]['course_code'] = $course->program;
                        $items[$typeData][$i]['course_title'] = $course->program . ' ' . $course->getTitle();
                        $i++;
                    }
                }
                $data[] = array(
                    'id' => $key,
                    'title' => $item,
                    'items' => $items,
                );
            }
        }
        $criteria = new CDbCriteria;
        $criteria->compare('school_id', $this->branchId);
        $criteria->compare('yid', $yid);
        $criteria->compare('status', 1);
        $criteria->compare('created_by', Yii::app()->user->id);
        $reportColormodel = AchievementReportColor::model()->find($criteria);
        $fractionColor = array();
        if($reportColormodel){
            $fractionColor = array(
                'id' => $reportColormodel->id,
                'title' => $reportColormodel->title_cn,
                'status' => $reportColormodel->status,
                'data' => ($reportColormodel->data) ? json_decode($reportColormodel->data, true) : array(),
            );
        }

        $this->render('index', array(
            'courseType' => $courseType,
            'data' => $data,
            'type' => $type,
            'calenderList' => $calenderList,
            'yid' => $yid,
            'branchId' => $this->branchId,
            'fractionColor' => $fractionColor,
        ));
    }

    // 学生报告数据
    public function actionStudentRoster()
    {
        $this->getCalendars();
        $code = Yii::app()->request->getParam('code', array(''));
        $yid = Yii::app()->request->getParam('yid', $this->calendarYids['currentYid']);

        // 根据yid 查询tid的值
        $criteria = new CDbCriteria;
        $criteria->compare('yid', $yid);
        $criteria->compare('schoolid', $this->branchId);
        $model = Timetable::model()->find($criteria);
        $tips = array();
        $classes = array();
        $dataArr = array();
        $courseCodeArr = array();
        $state = 'fail';
        $message = '暂无数据';
        if($model){
            $courseModel = array();
            if(!Yii::app()->user->checkAccess('ivystaff_counselor') && !Yii::app()->user->checkAccess('ivystaff_counselor')){
                $criteria = new CDbCriteria;
                $criteria->compare('yid', $yid);
                $criteria->compare('schoolid', $this->branchId);
                $criteria->compare('status', 1);
                $timeTableModel = Timetable::model()->find($criteria);

                if ($timeTableModel) {
                    $criteria = new CDbCriteria;
                    $criteria->compare('tid', $timeTableModel->id);
                    $criteria->compare('status', 1);
                    $criteria->compare('teacher_id', Yii::app()->user->id);
                    $criteria->index = 'course_id';
                    $teacherModel = TimetableCourseTeacher::model()->findAll($criteria);

                    $courseListId = array();
                    if ($teacherModel) {
                        foreach ($teacherModel as $val) {
                            $courseListId[$val->course_id] = $val->course_id;
                        }
                    }
                }
            }

            // 根据code 查询课程
            $criteria = new CDbCriteria;
            if(!Yii::app()->user->checkAccess('ivystaff_counselor') && !Yii::app()->user->checkAccess('ivystaff_counselor')){
                $criteria->compare('id', $courseListId);
            }
            $criteria->compare('program', $code);
            $courseModel = TimetableCourses::model()->findAll($criteria);
            $courseData = array();

            if($courseModel){
                $courseIds = array();          // 新课程id
                $report_course_id = array();   // 关联标准ID
                foreach ($courseModel as $item) {
                    $courseData[$item->program] = array(
                        'code' => $item->program,
                        'title' => $item->getTitle(),
                        'grade' => substr($item->program, 3, 1),
                        'tips' => array(),
                        'report_course_id' => $item->report_course_id,
                        'classes' => array()
                    );

                    $report_course_id[$item->report_course_id] = $item->report_course_id;
                    $courseIds[$item->id] = $item->id;
                }

                // 根据课程ID查询需要的标准
                $criteria = new CDbCriteria;
                $criteria->compare('courseid', $report_course_id);
                $standardModel = AchievementReportCourseStandard::model()->findAll($criteria);


                // $grading = array('评分标准id' => '分数')
                $grading = array();
                if($standardModel){
                    foreach ($standardModel as $val){
                        foreach ($val->items as $item) {
                            $fraction[$item->kid][$item->fraction] = nl2br($item->getName());
                            $grading[$item->id] = $item->fraction;
                        }
                        $tips[$val->courseid][$val->title_cn]['tip'] = nl2br($val->getName());
                        $tips[$val->courseid][$val->title_cn]['items'] = $fraction[$val->id];
                    }
                }

                // 根据校历ID 获取当前校历的报告模板ID
                $criteria = new CDbCriteria;
                $criteria->compare('calendar', $yid);
                $criteria->index = 'id';
                $reportModel = AchievementReport::model()->findAll($criteria);
                $dataArr = array();
                if($reportModel){
                    // 根据课程和报告ID 去拿到所有符合条件的所有孩子填写报告的数据
                    // ----------------- Start -----------------//
                    $criteria = new CDbCriteria;
                    $criteria->compare('timetable_records_id', $courseIds);
                    $criteria->compare('report_id', array_keys($reportModel));
                    $childFractionModel = AchievementReportChildFraction::model()->findAll($criteria);
                    $childFraction = array();
                    if($childFractionModel) {
                        foreach ($childFractionModel as $val) {
                            if($val->courseCourse){
                                $childFraction[$val->timetable_records_code][$val->childid][$val->report->cycle][] = isset($val->courseScoresid) ? $val->courseScoresid->fraction : '-';
                            }
                        }
                    }
                    // ----------------- End -----------------//

                    // 拿总分数
                    $total = array();
                    $criteria = new CDbCriteria;
                    $criteria->compare('calender_id', $yid);
                    $criteria->compare('timetable_records_id', $courseIds);
                    $totalModel = AchievementReportChildTotal::model()->findAll($criteria);
                    if($totalModel) {
                        foreach ($totalModel as $val) {
                            $total[$val->timetable_records_code][$val->childid] = ($val->oprion_value) ? $val->oprion_value : '-';
                        }
                    }

                    // 根据课程查询 上课的孩子
                    $criteria = new CDbCriteria;
                    $criteria->compare('course_id', $courseIds);
                    $criteria->compare('tid', $model->id);
                    $courseDataModel = TimetableStudentData::model()->findAll($criteria);

                    if($courseDataModel){
                        // 获取所有孩子的姓名------- Start -----------------//
                        $childsArr = array();
                        foreach ($courseDataModel as $val) {
                            $childsArr[$val->child_id] = $val->child_id;
                        }


                        $criteria = new CDbCriteria;
                        $criteria->compare('childid', $childsArr);
                        $criteria->index = 'childid';
                        $childModel = ChildProfileBasic::model()->findAll($criteria);

                        //--------------- End -----------------//

                        foreach ($courseDataModel as $val) {
                            $courseCodeArr[$val->course_code] = $val->course_code;
                            $preoid = ($childFraction && $childFraction[$val->course_code]) ? $childFraction[$val->course_code] : array();
                            $yearend = ($total && $total[$val->course_code]) ? $total[$val->course_code] : array();
                            if ($model->id == 1) {
                                $courseCode = substr($val->course_code, 0, 5);
                            } else {
                                $courseCode = substr($val->course_code, 0, 6);
                            }
                            $classes[$courseCode][$val->course_code][] = array(
                                'childid' => $val->child_id,
                                'name' => ($childModel && $childModel[$val->child_id]) ? $childModel[$val->child_id]->getChildName() : $val->child_id,
                                'peroid1' => ($preoid && $preoid[$val->child_id] & $preoid[$val->child_id][1]) ? $preoid[$val->child_id][1] : array('-','-','-','-'),
                                'peroid2' => ($preoid && $preoid[$val->child_id] & $preoid[$val->child_id][2]) ? $preoid[$val->child_id][2] : array('-','-','-','-'),
                                'peroid3' => ($preoid && $preoid[$val->child_id] & $preoid[$val->child_id][3]) ? $preoid[$val->child_id][3] : array('-','-','-','-'),
                                'peroid4' => ($preoid && $preoid[$val->child_id] & $preoid[$val->child_id][4]) ? $preoid[$val->child_id][4] : array('-','-','-','-'),
                                'yearend' => ($yearend && $yearend[$val->child_id]) ? $yearend[$val->child_id] : '-',
                            );
                        }
                    }
                }
                $state = 'success';
                $message = '成功';
            }
        }
        $alias = array();
        if($courseCodeArr && $model){
            $criteria = new CDbCriteria;
            $criteria->compare('course_code', $courseCodeArr);
            $criteria->compare('tid', $model->id);
            $criteria->compare('created_by', Yii::app()->user->id);
            $timetableCoursesAliasModel = TimetableCoursesAlias::model()->findAll($criteria);
            if($timetableCoursesAliasModel){
                foreach ($timetableCoursesAliasModel as $val){
                    $alias[] = array(
                        'course_code' => $val->course_code,
                        'alias' => $val->alias_cn,
                    );
                }
            }
        }

        // 整合数组发送前台
        foreach ($courseData as $val) {
            $dataArr[] = array(
                'code' => $val['code'],
                'title' => $val['title'],
                'grade' => $val['grade'],
                'tips' => ($tips && $tips[$val['report_course_id']]) ? $tips[$val['report_course_id']] : array(),
                'classes' => ($classes && $classes[$val['code']]) ? $classes[$val['code']] : array(),
                'alias' => $alias
            );
        }

        $this->addMessage('state', $state);
        $this->addMessage('message', $message);
        $this->addMessage('data', $dataArr);
        $this->showMessage();
    }

    // 拿到所有的课程 老师 班级 的3个数组
    public function actionCampusSummaryList()
    {
        $this->getCalendars();
        $yid = Yii::app()->request->getParam('yid', $this->calendarYids['currentYid']);

        $data['class'] = array();
        $data['course'] = array();
        $data['teacher'] = array();

        $courseModel = TimetableCourses::model()->findAll();
        $data['class'] = array();
        $data['course'] = array();
        $data['teacher'] = array();
        foreach ($courseModel as $val){
            $data['course'][$val->program] = $val->program . '-' . $val->getTitle();
            $data['class'][$val->course_code] = $val->course_code . ' - ' . $val->getTitle();
        }

        // g根据校历ID 和学校查询tid  在去查询当前TID下的老师
        $criteria = new CDbCriteria;
        $criteria->compare('yid', $yid);
        $criteria->compare('schoolid', $this->branchId);
        $timeTableModel = Timetable::model()->find($criteria);
        if($timeTableModel){
            $criteria = new CDbCriteria;
            $criteria->compare('schoolid', $this->branchId);
            $criteria->compare('tid', $timeTableModel->id);
            $teacherModel = TimetableCourseTeacher::model()->findAll();
            if($teacherModel) {
                $teacherids = array();
                foreach ($teacherModel as $val) {
                    $teacherids[$val->teacher_id] = $val->teacher_id;
                }
                $userModel = User::model()->findAllByPk($teacherids);
                foreach ($userModel as $val) {
                    $data['teacher'][$val->uid] = $val->getName();
                }
            }
        }

        $data['type'] = TimetableCourses::getCourseType();

        $this->addMessage('state', 'success');
        $this->addMessage('message', '成功');
        $this->addMessage('data', $data);
        $this->showMessage();
    }


    public function actionCampusSummary()
    {
        $this->getCalendars();
        $yid = Yii::app()->request->getParam('yid', $this->calendarYids['currentYid']);
        $this->getCalendars();
        $calenderList = $this->getCalendarList();
        $status = 3;
        $criteria = new CDbCriteria;
        $criteria->compare('school_id', $this->branchId);
        $criteria->compare('yid', $yid);
        $model = StateManage::model()->find($criteria);
        $time = 0;
        if($model){
            if($model->status == 1){
                $criteria = new CDbCriteria;
                $criteria->compare('calendar', $yid);
                $criteria->compare('schoolid', $this->branchId);
                $criteria->index = 'id';
                $reportModel = AchievementReport::model()->findAll($criteria);

                $criteria = new CDbCriteria;
                $criteria->compare('report_id', array_keys($reportModel));
                $criteria->limit = '1';
                $criteria->group = 'update_time desc';
                $fractionModel = AchievementReportChildFraction::model()->find($criteria);
                if($fractionModel){
                    if($fractionModel->update_time >= $model->updated_at){
                        $status = 2;
                        $model->status = 2;
                        $model->save();
                    }else{
                        $status = $model->status;
                    }
                }
            }else{
                $status = $model->status;
            }
            $time = date("Y-m-d H:i:s", $model->updated_at);
        }
        $language = Yii::app()->language;

        $permissionStatus = 0;
        $id = 0;
        if(!Yii::app()->user->checkAccess('o_MS_RrportMgt') && !Yii::app()->user->checkAccess('ivystaff_cd')) {
            $permissionStatus = 1;
            $id = Yii::app()->user->id;
        }
        $this->render('campusSummary', array(
            'calenderList' => $calenderList,
            'yid' => $yid,
            'status' => $status,
            'language' => $language,
            'time' => $time,
            'permissionStatus' => $permissionStatus,
            'id' => $id,
        ));
    }


    /*
     *  type  course || course_code ||  teacher
     *  data   前端点击传过的来 课程ID 或者 课程code 或者老师ID
     */
    public function actionCampusSummaryData()
    {
        $this->getCalendars();
        $type =  Yii::app()->request->getParam('type', ''); //tid
        $id =  Yii::app()->request->getParam('id', ''); //tid
        $yid =  Yii::app()->request->getParam('yid', $this->calendarYids['currentYid']); //tid

        $average_data = array();
        $frequency_data = array();
        $model = array();
        $alias = array();
        if($type && $id) {
            $criteria = new CDbCriteria;
            if($type == 'course'){
                $criteria->compare('program', $id);
                $criteria->compare('yid', $yid);
                $model = AchievementReportCache::model()->findAll($criteria);
            }
            if($type == 'class'){
                $criteria->compare('course_code', $id);
                $criteria->compare('yid', $yid);
                $model = AchievementReportCache::model()->findAll($criteria);
            }
            if($type == 'teacher'){
                $crita = new CDbCriteria;
                $crita->compare('yid', $yid);
                $crita->compare('schoolid', $this->branchId);
                $crita->compare('status', 1);
                $modelTimeTableModel = Timetable::model()->find($crita);

                if($modelTimeTableModel) {
                    $crita = new CDbCriteria;
                    $crita->compare('teacher_id', $id);
                    $crita->compare('status', 1);
                    $crita->compare('tid', $modelTimeTableModel->id);
                    $crita->index = 'course_id';
                    $teacherCourseIdModel = TimetableCourseTeacher::model()->findAll($crita);
                    if ($teacherCourseIdModel) {
                        $crita = new CDbCriteria;
                        $crita->compare('id', array_keys($teacherCourseIdModel));
                        $crita->index = 'course_code';
                        $courseModel = TimetableCourses::model()->findAll($crita);
                        if ($courseModel) {
                            $criteria->compare('course_code', array_keys($courseModel));
                            $criteria->compare('yid', $yid);
                            $model = AchievementReportCache::model()->findAll($criteria);
                        }
                        if($type == 'teacher' && Yii::app()->user->id == $id){
                            $criteria = new CDbCriteria;
                            $criteria->compare('course_code', array_keys($courseModel));
                            $criteria->compare('tid', $modelTimeTableModel->id);
                            $criteria->compare('created_by', $id);
                            $timetableCoursesAliasModel = TimetableCoursesAlias::model()->findAll($criteria);
                            if($timetableCoursesAliasModel){
                                foreach ($timetableCoursesAliasModel as $value){
                                    $alias[$value->course_code] = $value->alias_cn;
                                }
                            }
                        }
                    }
                }
            }

            if ($model) {
                foreach ($model as $val) {
                    if($alias && $alias[$val->course_code]){
                        $averageData = json_decode($val->average_data, true);
                        $averageData['name_cn'] = $averageData['name_cn'] . "（" . $alias[$val->course_code] ."）";
                        $averageData['name_en'] = $averageData['name_en'] . "(" . $alias[$val->course_code] .")";
                        $average_data[] = $averageData;
                        $frequencyData = json_decode($val->frequency_data, true);
                        $frequencyData['name_cn'] = $frequencyData['name_cn'] . "（" . $alias[$val->course_code] ."）";
                        $frequencyData['name_en'] = $frequencyData['name_en'] . "(" . $alias[$val->course_code] .")";
                        $frequency_data[] = $frequencyData;
                    }else{
                        $average_data[] = json_decode($val->average_data, true);
                        $frequency_data[] = json_decode($val->frequency_data, true);
                    }
                }
                //$data = $this->SpliceData($average_data, $frequency_data, $id);
            }

            /*if($type == 'teacher'){
                if($average_data){
                    Yii::msg($average_data);
                }
                if($average_data){

                }
            }*/
        }

        $data['average'] = $average_data;
        $data['frequency'] = $frequency_data;
        $this->addMessage('state', 'success');
        $this->addMessage('message', '成功');
        $this->addMessage('data', $data);
        $this->showMessage();
    }


    // 生成缓存表数据
    public function actionGenerateData()
    {
        $this->getCalendars();
        $yid =  Yii::app()->request->getParam('yid', $this->calendarYids['currentYid']); //tid
        $criteria = new CDbCriteria;
        $criteria->compare('school_id', $this->branchId);
        $criteria->compare('yid', $yid);
        $model = StateManage::model()->find($criteria);
        if(!$model){
            $model = new StateManage();
            $model->yid = $yid;
            $model->school_id = $this->branchId;
            $model->type = 'report';
            $model->created_at = time();
            $model->created_by = Yii::app()->user->id;
        }
        $model->status = 0;
        $model->updated_at = time();
        $model->updated_by = Yii::app()->user->id;
        $model->save();
        // 调用命令行
        $data['status'] = 0;
        $shell = "php " . Yii::app()->basePath . "/yiic.php achievementreportcache saveCache --yid=". $yid ." --schoolid=". $this->branchId . ' > /dev/null &';
        shell_exec($shell);
        $this->addMessage('state', 'success');
        $this->addMessage('message', '成功');
        $this->addMessage('data', $data);
        $this->showMessage();
    }

    // 表的状态 是否生成完数据
    public function actionStateManageStatus()
    {
        $this->getCalendars();
        $yid =  Yii::app()->request->getParam('yid', $this->calendarYids['currentYid']);
        $criteria = new CDbCriteria;
        $criteria->compare('school_id', $this->branchId);
        $criteria->compare('yid', $yid);
        $model = StateManage::model()->find($criteria);
        $data['status'] = 0;
        if($model && $model->status != 0){
            $data['status'] = 1;
        }
        $this->addMessage('state', 'success');
        $this->addMessage('message', '成功');
        $this->addMessage('data', $data);
        $this->showMessage();
    }

    public function actionShowFractionColor()
    {
        $yid =  Yii::app()->request->getParam('yid', $this->calendarYids['currentYid']);
        $data = array();
        $criteria = new CDbCriteria;
        $criteria->compare('school_id', $this->branchId);
        $criteria->compare('yid', $yid);
        $criteria->compare('created_by', Yii::app()->user->id);
        $model = AchievementReportColor::model()->findAll($criteria);
        if($model){
            foreach ($model as $val){
                $data[] = array(
                    'id' => $val->id,
                    'title' => $val->title_cn,
                    'status' => $val->status,
                    'data' => ($val->data) ? json_decode($val->data,true) : array(),
                );
            }
        }
        echo json_encode($data);
    }

    /*
     * $fractionColor  選中的配置
     * showFractionColor 獲取所有的配置項
     * yid  校曆ID
     *
     * updateColor  增加配置或者增加颜色
     * 增加配置参数
     * 增加 title 标题   yid  校历ID
     * 修改 title 标题   color_id  ID
     *
     * 增加颜色参数
     * color_id 配置参数id   data  颜色参数   selected  启用那个配置
     *
     */

    // 增加颜色配置分类 和 增加颜色
    public function actionUpdateColor()
    {
        $color_id =  Yii::app()->request->getParam('color_id', '');
        $title =  Yii::app()->request->getParam('title', '');
        $data =  Yii::app()->request->getParam('data', array());
        $status =  Yii::app()->request->getParam('status', 0);
        $yid =  Yii::app()->request->getParam('yid', '');
        $model = array();
        if(!$color_id){
            $model = new AchievementReportColor();
            $model->status = $status;
            $model->yid = $yid;
            $model->school_id = $this->branchId;
            $model->created_at = time();
            $model->created_by = Yii::app()->user->id;
            $model->title_cn = $title;
            $model->title_en = $title;
            $model->updated_at = time();
            $model->updated_by = Yii::app()->user->id;
            if(!$model->save()){
                $err = current($model->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('message', $err[0]);
                $this->showMessage();
            }
        }

        if($data && $color_id){
            $model = AchievementReportColor::model()->findByPk($color_id);
            if($status == 1) {
                $criteria = new CDbCriteria;
                $criteria->compare('school_id', $this->branchId);
                $criteria->compare('yid', $model->yid);
                $criteria->compare('status', $status);
                $modelColor = AchievementReportColor::model()->find($criteria);
                if ($modelColor) {
                    $modelColor->status = 0;
                    $modelColor->save();
                }
            }
            $model->data = json_encode($data);
            $model->title_cn = $title;
            $model->title_en = $title;
            $model->status = $status;
            $model->updated_at = time();
            $model->updated_by = Yii::app()->user->id;
            if(!$model->save()){
                $err = current($model->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('message', $err[0]);
                $this->showMessage();
            }

        }

        if($model){
            $fractionColor = array(
                'id' => $model->id,
                'title' => $model->title_cn,
                'status' => $model->status,
                'data' => ($model->data) ? json_decode($model->data,true) : array(),
            );
        }
        $this->addMessage('state', 'success');
        $this->addMessage('message', '成功');
        $this->addMessage('data', $fractionColor);
        $this->addMessage('callback', 'cbSuccess');
        $this->showMessage();
    }

    // 修改选中用那一个配置
    public function createdStatua($color_id, $yid, $status)
    {
        if($status == 1){
            $criteria = new CDbCriteria;
            $criteria->compare('school_id', $this->branchId);
            $criteria->compare('yid', $yid);
            $criteria->compare('status', $status);
            $model = AchievementReportColor::model()->find($criteria);
            if($model){
                $model->status = 0;
                $model->save();
            }
        }
        $colorModel = AchievementReportColor::model()->findByPk($color_id);
        if($colorModel){
            $colorModel->status = $status;
            $colorModel->save();
        }
        return true;
    }

    // 拼接数组传给前台  暂时无用 留着
    public function SpliceData($average_data,$frequency_data,$id)
    {
        $averageData = array(
            'peroid1' => array('-','-','-','-'),
            'peroid2' => array('-','-','-','-'),
            'peroid3' => array('-','-','-','-'),
            'peroid4' => array('-','-','-','-'),
            'yearend' => '-',
        );
        $total_students = 0;
        $yearend = '-';
        foreach ($average_data as $k => $val) {
            $total_students += $val['total_students'];
            foreach ($val['peroid1'] as $key=>$item) {
                if(is_numeric($item)){
                    $averageData['peroid1'][$key] += $item;
                }
            }
            foreach ($val['peroid2'] as $key=>$item) {
                if(is_numeric($item)){
                    $averageData['peroid2'][$key] += $item;
                }
            }
            foreach ($val['peroid3'] as $key=>$item) {
                if(is_numeric($item)){
                    $averageData['peroid3'][$key] += $item;
                }
            }
            foreach ($val['peroid4'] as $key=>$item) {
                if(is_numeric($item)){
                    $averageData['peroid4'][$key] += $item;
                }
            }
            if(is_numeric($val['yearend'])){
                $yearend += $val['yearend'];
            }
        }

        $average = array();
        foreach ($averageData as $k=>$val){
            foreach ($val as $key => $item) {
                if(is_numeric($item)){
                    $average[$k][$key] = sprintf('%.2f', $item / count($average_data));
                }else{
                    $average[$k][$key] = '-';
                }
            }
        }

        if(is_numeric($yearend)){
            $average['yearend'] = sprintf('%.2f', $yearend / count($average_data));
        }else{
            $average['yearend'] = '-';
        }

        $frequencyData = array();
        foreach ($frequency_data as $val){
            foreach ($val['score1'] as $key=>$item) {
                foreach ($item as $k=>$i) {
                    $frequencyData['score1'][$key][$k] += $i;
                }
            }
            $frequencyData['score1']['yearend'] += $val['score1']['yearend'];
            foreach ($val['score2'] as $key=>$item) {
                foreach ($item as $k=>$i) {
                    $frequencyData['score2'][$key][$k] += $i;
                }
            }
            $frequencyData['score2']['yearend'] += $val['score2']['yearend'];
            foreach ($val['score3'] as $key=>$item) {
                foreach ($item as $k=>$i) {
                    $frequencyData['score3'][$key][$k] += $i;
                }
            }
            $frequencyData['score3']['yearend'] += $val['score3']['yearend'];
            foreach ($val['score4'] as $key=>$item) {
                foreach ($item as $k=>$i) {
                    $frequencyData['score4'][$key][$k] += $i;
                }
            }
            $frequencyData['score4']['yearend'] += $val['score4']['yearend'];
            foreach ($val['score5'] as $key=>$item) {
                foreach ($item as $k=>$i) {
                    $frequencyData['score5'][$key][$k] += $i;
                }
            }
            $frequencyData['score5']['yearend'] += $val['score5']['yearend'];
        }

        $data['average']['total_students'] = $total_students;
        $data['average']['name'] = $id;
        $data['average']['peroid1'] = $average['peroid1'];
        $data['average']['peroid2'] = $average['peroid2'];
        $data['average']['peroid3'] = $average['peroid3'];
        $data['average']['peroid4'] = $average['peroid4'];
        $data['average']['yearend'] = $average['yearend'];
        $data['frequency']['total_students'] = $total_students;
        $data['frequency']['name'] = $id;
        $data['frequency']['score1'] = $frequencyData['score1'];
        $data['frequency']['score2'] = $frequencyData['score2'];
        $data['frequency']['score3'] = $frequencyData['score3'];
        $data['frequency']['score4'] = $frequencyData['score4'];
        $data['frequency']['score5'] = $frequencyData['score5'];

        return $data;
    }

    public function getCalendarList()
    {
        $calenderList = array();
        $criteria = new CDbCriteria();
        $criteria->compare('branchid', $this->branchId);
        $criteria->order='startyear asc';
        $years = CalendarSchool::model()->findAll($criteria);
        foreach($years as $year){
            if ($year->startyear >= 2018) {
                $calenderList[$year->yid] = $year->startyear ."-". ($year->startyear + 1);
            }
        }
        return $calenderList;
    }
}
