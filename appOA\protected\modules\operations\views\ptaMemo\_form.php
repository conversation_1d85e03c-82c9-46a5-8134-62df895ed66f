<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'pta-memo-form',
	'enableAjaxValidation'=>false,
)); ?>
<div class="h_a"><?php echo $model->isNewRecord ? '添加会议记录' : '修改会议记录';?></div>
<div class="table_full">
    <table width="100%">
        <col width="150" />
        <col width="600" />
        <col />
        <tbody>
            <tr>
                <th><?php echo $form->labelEx($model,'title'); ?></th>
                <td>
                    <?php echo $form->textField($model,'title',array('maxlength'=>255,'class'=>'input length_5')); ?>
                </td>
                <td><div class="fun_tips">中文标题{{lang-tag}}English Title</div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'schoolid'); ?></th>
                <td>
                    <?php echo $form->dropDownList($model, 'schoolid', $schools, array('empty'=>Yii::t("global", 'Please Select')));?>
                </td>
                <td></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'memo_date'); ?></th>
                <td>
                    <?php
                    $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                        "model"=>$model,
                        "attribute"=>"memo_date",
                        "options"=>array(
                            'changeMonth'=>true,
                            'changeYear'=>true,
                            'dateFormat'=>'yy-mm-dd',
                        ),
                        'htmlOptions'=>array(
                            'autocomplete'=>'off',
                            'class'=>'input'
                        ),
                    ));
                    ?>
                </td>
                <td></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'pta_members'); ?></th>
                <td>
                    <div id="ptamembers">
                        <?php foreach($model->renderMembers() as $user):?>
                            <?php echo CHtml::image('http://oa.ivyonline.cn/uploads/users/thumbs/'.$user->user_avatar, $user->name, array('style'=>'width: 30px;', 'title'=>$user->name));?>
                        <?php endforeach;?>
                    </div>
                    <?php
                    echo CHtml::link(Yii::t("global", 'Please Select'), array('parentmember', 'id'=>$model->id, 'schoolid'=>$model->schoolid), array('class'=>'J_dialog btn', 'title'=>'请选择家长参会人员', 'id'=>'pmember'));
                    echo $form->hiddenField($model, 'pta_members');
                    ?>
                </td>
                <td></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'pta_admins'); ?></th>
                <td>
                    <div id="aptamembers">
                        <?php foreach($model->renderAdmins() as $user):?>
                            <?php echo CHtml::image('http://oa.ivyonline.cn/uploads/users/thumbs/'.$user->user_avatar, $user->name, array('style'=>'width: 30px;', 'title'=>$user->name));?>
                        <?php endforeach;?>
                    </div>
                    <?php
                    echo CHtml::link(Yii::t("global", 'Please Select'), array('adminmember', 'id'=>$model->id, 'schoolid'=>$model->schoolid), array('class'=>'J_dialog btn', 'title'=>'请选择校园参会人员', 'id'=>'padmin'));
                    echo $form->hiddenField($model, 'pta_admins');
                    ?>
                </td>
                <td></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model1,'ext_members'); ?></th>
                <td>
                    <?php echo $form->textArea($model1,'ext_members',array('rows'=>6, 'cols'=>50)); ?>
                </td>
                <td></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model1,'ext_admins'); ?></th>
                <td>
                    <?php echo $form->textArea($model1,'ext_admins',array('rows'=>6, 'cols'=>50)); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model1,'ext_admins'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model1,'content'); ?></th>
                <td>
                    <?php
                    /*$this->widget('common.extensions.xheditor.XHEditor', array(
                        'model' => $model1,
                        'modelAttribute' => 'content',
                        'language' => Yii::app()->language,
                        'config' => array(
                            'tools' => 'mini',
                        )
                    ));*/
                    $this->widget('common.extensions.ueditor.ueditor',array(
                        'model' => $model1,
                        'attribute' => 'content',
                        'language' =>Yii::app()->language == 'en_us' ? 'en' : 'zh-cn',
                        'width' =>'100%',
                    ));
                    ?>
                </td>
                <td></td>
            </tr>
        </tbody>
    </table>
</div>
<div class="btn_wrap">
    <div class="btn_wrap_pd">
        <button class="btn btn_submit mr10 J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
    </div>
</div>

<?php $this->endWidget(); ?>

<script>
    $('#PtaMemo_schoolid').change(function(){
        var schoolid = $(this).val();
        $('#pmember').attr('href', '<?php echo $this->createUrl("parentmember", array('id'=>$model->id));?>&schoolid='+schoolid);
        $('#padmin').attr('href', '<?php echo $this->createUrl("adminmember", array('id'=>$model->id));?>&schoolid='+schoolid);
    });
</script>