<?php
	foreach(Yii::app()->user->getFlashes() as $key => $message) {
        echo '<div class="flash-' . $key . '">' . $message . "</div>\n";
    }
?>
<style>
    .warningContent{
        color: #8a6d3b;
        background-color: #fcf8e3;
        border-color: #faebcc;
        padding: 15px;
        font-size: 12px;
        margin-bottom: 20px;
        border: 1px solid transparent;
        border-radius: 4px;
    }
</style>
<div class="form profile_bg">
<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'child-form',
	'enableClientValidation'=>true,
	'clientOptions'=>array(
		'validateOnSubmit'=>true,
	),
	'htmlOptions' => array('enctype' => 'multipart/form-data'),
)); ?>

	<?php //echo CHtml::errorSummary($model); ?>
    <div  class="warningContent">
        <?php
            if ($this->getChildInfo()['schoolid'] == 'BJ_DS') {
                echo Yii::t("labels", 'If any of the information above is inaccurate, <NAME_EMAIL> to update them.');
            }
            else if ($this->getChildInfo()['schoolid'] == 'BJ_SLT') {
                echo Yii::t("labels", 'If any of the information above is inaccurate, <NAME_EMAIL> to update them.');
            }else{
                echo Yii::t("labels", 'If any of the information above is inaccurate, <NAME_EMAIL> to update them.');

            }
        ?>
    </div>
	<dl class="row">
		<dt>
		<?php $_child = $this->myChildObjs[$this->childid];?>
		<?php echo CHtml::image(CommonUtils::childPhotoUrl($_child->photo, 'small'), "", array("class"=>"thumb-small") );?>
		</dt>
		<dd>&nbsp;</dd>
	</dl>

<!--	<dl class="row">-->
<!--		<dt>-->
<!--		--><?php //echo CHtml::activeLabelEx($model,'photo'); ?>
<!--		</dt>-->
<!--		<dd>-->
<!--		--><?php //echo CHtml::activeFileField($model,'uploadPhoto') ?>
<!--		--><?php //echo CHtml::error($model,'uploadPhoto'); ?>
<!--		</dd>-->
<!--	</dl>-->

	<dl class="row">
		<dt>
		<?php echo CHtml::activeLabelEx($model,'birthday_search'); ?>
		</dt>
		<dd>
		<?php
			$this->widget('zii.widgets.jui.CJuiDatePicker', array(
				"model"=>$model,
				"attribute"=>"birthday_search",
				"options"=>array(
					'changeMonth'=>true,
					'changeYear'=>true,
					'dateFormat'=>'yy-mm-dd', //很重要，不要随便修改 (RAN)
				),
				"htmlOptions"=>array(
					'disabled'=>'disabled'
				)
			));
		?>
		<?php echo CHtml::error($model,'birthday_search'); ?>
		</dd>
	</dl>

	<dl class="row">
		<dt>
		<?php echo CHtml::activeLabelEx($model,'educational_id'); ?>
		</dt>
		<dd>
		<?php echo CHtml::activeTextField($model,'educational_id',array('disabled'=>'disabled')) ?>
		<?php echo CHtml::error($model,'educational_id'); ?>
		</dd>
	</dl>

    <?php if($model->is_legel_cn_name == 1){ ?>
        <dl class="row">
            <dt>
                <label for="ChildProfileBasic_name_cn">法定中文名字</label>
            </dt>
            <dd>
                <?php echo CHtml::activeTextField($model,'name_cn',array('disabled'=>'disabled'))?>
                <?php echo CHtml::error($model,'name_cn'); ?>
            </dd>
        </dl>

        <dl class="row">
            <dt>
                <label for="ChildProfileBasic_last_name_en">法定姓（拼音）</label>
            </dt>
            <dd>
                <?php echo CHtml::activeTextField($model,'last_name_en',array('disabled'=>'disabled')) ?>
                <?php echo CHtml::error($model,'last_name_en'); ?>
            </dd>
        </dl>

        <dl class="row">
            <dt>
                <label for="ChildProfileBasic_first_name_en">法定名（拼音）</label>
            </dt>
            <dd>
                <?php echo CHtml::activeTextField($model,'first_name_en',array('disabled'=>'disabled')) ?>
                <?php echo CHtml::error($model,'first_name_en'); ?>
            </dd>
        </dl>
        <dl class="row">
            <dt>
                <label for="ChildProfileBasic_nick">昵称/英文名</label>
            </dt>
            <dd>
                <?php echo CHtml::activeTextField($model,'nick',array('disabled'=>'disabled')) ?>
                <?php echo CHtml::error($model,'nick'); ?>
            </dd>
        </dl>
    <?php }else{ ?>
        <dl class="row">
            <dt>
                <label for="ChildProfileBasic_last_name_en_en">Legal Last Name</label>
            </dt>
            <dd>
                <?php echo CHtml::activeTextField($model,'last_name_en',array("id" => "ChildProfileBasic_last_name_en_en", 'disabled'=>'disabled')) ?>
                <?php echo CHtml::error($model,'last_name_en'); ?>
            </dd>
        </dl>

        <dl class="row">
            <dt>
                <label for="ChildProfileBasic_first_name_en_en">Legal First Name</label>
            </dt>
            <dd>
                <?php echo CHtml::activeTextField($model,'first_name_en',array("id" => "ChildProfileBasic_first_name_en_en", 'disabled'=>'disabled')) ?>
                <?php echo CHtml::error($model,'first_name_en'); ?>
            </dd>
        </dl>

        <dl class="row">
            <dt>
                <label for="ChildProfileBasic_middle_name_en_en">Legal Middle Name</label>
            </dt>
            <dd>
                <?php echo CHtml::activeTextField($model,'middle_name_en',array("id" => "ChildProfileBasic_middle_name_en_en", 'disabled'=>'disabled')) ?>
                <?php echo CHtml::error($model,'middle_name_en'); ?>&nbsp; <span class="btn-default"></span>
            </dd>
        </dl>
        <dl class="row">
            <dt>
                <label for="ChildProfileBasic_nick_en">Preferred Name</label>
            </dt>
            <dd>
                <?php echo CHtml::activeTextField($model,'nick',array("id" => "ChildProfileBasic_nick_en", 'disabled'=>'disabled')) ?>
                <?php echo CHtml::error($model,'nick'); ?>
            </dd>
        </dl>
        <dl class="row">
            <dt>
                <label for="ChildProfileBasic_last_name_cn_en">Chinese Name</label>
            </dt>
            <dd>
                <?php echo CHtml::activeTextField($model,'name_cn',array("id" => "ChildProfileBasic_last_name_cn_en", 'disabled'=>'disabled')) ?>
                <?php echo CHtml::error($model,'name_cn'); ?>
            </dd>
        </dl>
    <?php } ?>
	<dl class="row">
		<dt>
		<?php echo CHtml::activeLabelEx($model,'gender'); ?>
		</dt>
		<dd>
		<?php echo CHtml::activeDropDownList($model,'gender',$cfgs["gender"], array('disabled'=>'disabled')); ?>
		<?php echo CHtml::error($model,'gender'); ?>
		</dd>
	</dl>


	<dl class="row">
		<dt>
		<?php echo CHtml::activeLabelEx($model,'country'); ?>
		</dt>
		<dd>
		<?php echo CHtml::activeDropDownList($model,'country', Country::model()->getData(), array('empty'=>Yii::t("global", 'Please Select'), 'disabled'=>'disabled')) ?>
		<?php echo CHtml::error($model,'country'); ?>
		</dd>
	</dl>


	<dl class="row">
		<dt>
		<?php echo CHtml::activeLabelEx($model,'lang'); ?>
		</dt>
		<dd>
		<?php echo CHtml::activeDropDownList($model,'lang', Term::model()->getLangList(), array('empty'=>Yii::t("global", 'Please Select'), 'disabled'=>'disabled')) ?>
		<?php echo CHtml::error($model,'lang'); ?>
		</dd>
	</dl>

<!--	<dl class="row submit">-->
<!--		<dd>-->
<!--		--><?php //if(Yii::app()->user->checkAccess('editProfile',array("childid"=>$this->getChildId()))){
//			if ($model->status >= ChildProfileBasic::STATS_GRADUATED) {
//				echo CHtml::submitButton(Yii::t("global", "Save"), array("class"=>"w100 bigger","disabled"=>"disabled"));
//			}else{
//				echo CHtml::submitButton(Yii::t("global", "Save"), array("class"=>"w100 bigger","disabled"=>"disabled"));
//			}
//		}else{
//			echo CHtml::submitButton(Yii::t("global", "Save"), array("class"=>"w100 bigger","disabled"=>"disabled"));
//		}
//		?>
<!--		</dd>-->
<!--	</dl>-->


<?php $this->endWidget(); ?>
</div><!-- form -->