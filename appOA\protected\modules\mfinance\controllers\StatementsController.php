<?php

class StatementsController extends ProtectedController
{
    public $actionAccessAuths = array(
        'index'            => 'o_F_Access',
        'query'            => 'o_F_Access',
        'done'             => 'o_F_checkAccount',
        'vieworder'        => 'o_F_Access',
        'viewstatement'    => 'o_F_Access',
    );

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/jquery.printThis.js');
        $this->modernMenuFlag = 'main';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Finance Mgt');
    }

	public function actionIndex()
	{
        $accountSel = $this->getAccountSchool();
        $this->render('index', array('accountSel'=>$accountSel, 'branch'=>$this->allBranch));
	}

    public function actionQuery()
    {
        Yii::import('common.models.bank.*');
        $account = Yii::app()->request->getParam('account', '');
        $startdate = Yii::app()->request->getParam('startdate', '');
        $enddate = Yii::app()->request->getParam('enddate', '');
        $type = Yii::app()->request->getParam('type', 1);
        if($account){
            $items = BankSchool::model()->findAllByAttributes(array('accountid'=>$account));
            foreach($items as $item){
                $schoolids[] = $item->schoolid;
            }

            if($type == 1){
                $posData = $this->getPosData($schoolids, $startdate, $enddate);
                $wechatData = $this->getWechatData($schoolids, $startdate, $enddate);               
                $onlineData = $this->getOnlineData($schoolids, $startdate, $enddate);
                $data['data'] = $posData+$onlineData+$wechatData;
                if (in_array('BJ_DS', $schoolids)) {
                    $busWechatData = $this->getBusWechatData($startdate, $enddate);
                    $busPosData = $this->getBusPosData($startdate, $enddate);
                    $uniformWechatData = $this->getUniformWechatData($startdate, $enddate);
                    $data['data'] = $posData+$onlineData+$wechatData+$busWechatData+$busPosData+$uniformWechatData;
                }
                $data['bankid'] = $account;
                $this->addMessage('callback', 'callback');
            }
            else{
                $timestamps = strtotime($startdate);
                $timestampe = strtotime($enddate);
                if($timestampe-$timestamps>5184000){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '查看已到帐，开始和结束日期不能超过60天');
                    $this->showMessage();
                }
                $data = $this->getHistory($account, $timestamps, $timestampe);
                $this->addMessage('callback', 'history');
            }
            $this->addMessage('data', $data);
            $this->addMessage('state', 'success');
            $this->addMessage('message', '查询成功');
        }
        else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '请选择账户');
        }
        $this->showMessage();
    }

    public function getHistory($bankid=0, $startdate='', $enddate='')
    {
        Yii::import('common.models.yeepay.*');
        Yii::import('common.models.alipay.*');
        Yii::import('common.models.wxpay.*');

        $criteria = new CDbCriteria;
        $criteria->compare('bank_id', $bankid);
        if($startdate)
            $criteria->compare('bank_timestamp', '>='.$startdate);
        if($enddate)
            $criteria->compare('bank_timestamp', '<='.$enddate);

        $yeepayModels = YpCpAccountHistory::model()->findAll($criteria);

        $criteria = new CDbCriteria;
        $criteria->compare('bank_id', $bankid);
        if($startdate)
            $criteria->compare('bank_toaccount_time', '>='.$startdate);
        if($enddate)
            $criteria->compare('bank_toaccount_time', '<='.$enddate);
        $criteria->compare('type', 'alipay');
        $alipayModels = PmStatement::model()->findAll($criteria);

        $criteria = new CDbCriteria;
        $criteria->compare('bank_id', $bankid);
        if($startdate)
            $criteria->compare('bank_toaccount_time', '>='.$startdate);
        if($enddate)
            $criteria->compare('bank_toaccount_time', '<='.$enddate);
        $criteria->compare('type', 'allinpayOnline');
        $allinpayOnlineModels = PmStatement::model()->findAll($criteria);

        $criteria = new CDbCriteria;
        $criteria->compare('bank_id', $bankid);
        if($startdate)
            $criteria->compare('bank_toaccount_time', '>='.$startdate);
        if($enddate)
            $criteria->compare('bank_toaccount_time', '<='.$enddate);
        $criteria->compare('type', 'allinpay');
        $allinpayModels = PmStatement::model()->findAll($criteria);

        $criteria = new CDbCriteria;
        $criteria->compare('bank_id', $bankid);
        if($startdate)
            $criteria->compare('bank_toaccount_time', '>='.$startdate);
        if($enddate)
            $criteria->compare('bank_toaccount_time', '<='.$enddate);
        $criteria->compare('type', 'wechat');
        $wechatPayOrderModels = PmStatement::model()->findAll($criteria);

        return $this->renderPartial('history', array(
            'yeepayModels' => $yeepayModels,
            'alipayModels' => $alipayModels,
            'allinpayModels' => $allinpayModels,
            'wechatPayOrderModels' => $wechatPayOrderModels,
            'allinpayOnlineModels' => $allinpayOnlineModels,
        ), true);
    }

    public function getAccountSchool()
    {
        Yii::import('common.models.bank.*');
        $adminBranch = AdmBranchLink::model()->getMultipleBranches(Yii::app()->user->getId());
        $accountSchool = array();
        $branchs = $this->getAllBranch();
        $criteria = new CDbCriteria();
        $criteria->compare('inactive', 0);
        $items = BankAccount::model()->with('school')->findAll($criteria);
        foreach($items as $item){
            if($item->school){
                $label = array();
                $canAccess = false;
                foreach($item->school as $school){
                    $label[$school->schoolid] = $branchs[$school->schoolid]['abb'];
                    if(in_array($school->schoolid, $adminBranch)){
                        $canAccess = true;
                    }
                }
                if($this->staff->profile->branch == 'BJ_TYG' || $canAccess)
                    $accountSchool[$item->id] = implode(', ', $label).' '.$item->account;
            }
        }
        return $accountSchool;
    }

    public function getPosData($schoolids=array(), $startdate='', $enddate='')
    {
        if (in_array('BJ_QFF', $schoolids)) {
            $busLinkIds = $this->getBusLinkIds('pos', $startdate, $enddate);
        }
        $yeePay = array('total'=>0);
        $allinPay = array('total'=>0);
        Yii::import('common.models.yeepay.*');
        $criteria = new CDbCriteria();
        $criteria->compare('status', 1);
        $criteria->compare('schoolId', $schoolids);
        if($startdate)
            $criteria->compare('updateTime', '>='.strtotime($startdate));
        if($enddate)
            $criteria->compare('updateTime', '<='.strtotime($enddate.' 23:59:59'));
        $criteria->order='updateTime desc';
        $criteria->compare('payed_status', 0);
        $items = YeepayOrder::model()->findAll($criteria);
        foreach($items as $item){
            if (isset($busLinkIds) && in_array($item->orderId, $busLinkIds)) {
                continue;
            }
            if($item->r2_TrxId == 'allinpay'){
                $allinPay['items'][] = array(
                    'orderId' => $item->orderId,
                    'amount' => $item->fact_amount,
                    'payDate' => date('Y/m/d H:i', strtotime($item->rp_PayDate)),
                    'schoolid' => $item->schoolId,
                );
                $allinPay['total'] += $item->fact_amount;
            }
            else{
                $yeePay['items'][] = array(
                    'orderId' => $item->orderId,
                    'amount' => $item->fact_amount,
                    'payDate' => date('Y/m/d H:i', strtotime($item->rp_PayDate)),
                    'schoolid' => $item->schoolId,
                );
                $yeePay['total'] += $item->fact_amount;
            }
        }
        return array('allinpay'=>$allinPay, 'yeepay'=>$yeePay);
    }

    public function getOnlineData($schoolids=array(), $startdate='', $enddate='')
    {
        $alipay = array('total'=>0);
        $allinpayOnline = array('total'=>0);
        Yii::import('common.models.alipay.*');
        Yii::import('common.models.invoice.InvoiceTransaction');
        $criteria = new CDbCriteria();
        $criteria->compare('status', 1);
        $criteria->compare('settlement_status', 0);
        if($startdate)
            $criteria->compare('update_timestamp', '>='.strtotime($startdate));
        if($enddate)
            $criteria->compare('update_timestamp', '<='.strtotime($enddate.' 23:59:59'));
        $criteria->compare('schoolid', $schoolids);
        // $criteria->compare('payment_method', InvoiceTransaction::TYPE_ONLINEPAYMENT);
        $criteria->order='update_timestamp desc';
        $items = AlipayOrder::model()->findAll($criteria);
        foreach($items as $item){
            if ($item->payment_method == InvoiceTransaction::TYPE_ONLINEPAYMENT) {
                $alipay['items'][] = array(
                    'orderId' => $item->id,
                    'amount' => $item->fact_amount,
                    'payDate' => date('Y/m/d H:i', $item->update_timestamp),
                    'schoolid' => $item->schoolid,
                );
                $alipay['total'] += $item->fact_amount;
            }else{
                $allinpayOnline['items'][] = array(
                    'orderId' => $item->id,
                    'amount' => $item->fact_amount,
                    'payDate' => date('Y/m/d H:i', $item->update_timestamp),
                    'schoolid' => $item->schoolid,
                );
                $allinpayOnline['total'] += $item->fact_amount;
            }
        }
        return array('allinpayOnline'=>$allinpayOnline,'alipay'=>$alipay,);
    }

    //获取微信到帐情况
    public function getWechatData($schoolids=array(), $startdate='', $enddate='')
    {
        if (in_array('BJ_QFF', $schoolids)) {
            $busLinkIds = $this->getBusLinkIds('wechat', $startdate, $enddate);
            $uniformLInkIds = $this->getUniformLinkIds('wechat', $startdate, $enddate);
        }
        $wechat = array('total'=>0);
        Yii::import('common.models.wxpay.*');
        $criteria = new CDbCriteria();
        $criteria->compare('status', 1);
        $criteria->compare('settlement_status', 0);
        if($startdate)
            $criteria->compare('update_timestamp', '>='.strtotime($startdate));
        if($enddate)
            $criteria->compare('update_timestamp', '<='.strtotime($enddate.' 23:59:59'));
        $criteria->compare('schoolid', $schoolids);
        $criteria->order='update_timestamp desc';
        $items = WechatPayOrder::model()->findAll($criteria);
        foreach($items as $item){
            // 过滤未到账的校服费用
            if ($item->ext == '{"merchantId":"1653216826"}') {
                continue;
            }
            if (isset($busLinkIds) && in_array($item->orderid, $busLinkIds)) {
                continue;
            }
            if (isset($uniformLInkIds) && in_array($item->orderid, $uniformLInkIds)) {
                continue;
            }
            $handling_amount = round($item->fact_amount * 0.003, 2);
            $wechat['items'][] = array(
                'orderId' => $item->orderid,
                'amount' => $item->fact_amount,
                'handling_amount' => $handling_amount,
                'payDate' => date('Y/m/d H:i', $item->update_timestamp),
                'schoolid' => $item->schoolid,
            );
            $wechat['total'] += $item->fact_amount;
            $wechat['handling_amount'] += $handling_amount;
        }
        return array('wechat'=>$wechat);
    }

    public function getBusWechatData($startdate='', $enddate='') {
        $ids = $this->getBusLinkIds('wechat', $startdate, $enddate);
        if (!$ids) {
            return array('wechatBus'=>array());
        }
        $wechat = array('total'=>0);
        Yii::import('common.models.wxpay.*');
        $criteria = new CDbCriteria();
        $criteria->compare('orderid', $ids);
        $criteria->compare('status', 1);
        $criteria->compare('settlement_status', 0);
        if($startdate)
            $criteria->compare('update_timestamp', '>='.strtotime($startdate));
        if($enddate)
            $criteria->compare('update_timestamp', '<='.strtotime($enddate.' 23:59:59'));
        $criteria->order='update_timestamp desc';
        $items = WechatPayOrder::model()->findAll($criteria);
        foreach($items as $item){
            $handling_amount = round($item->fact_amount * 0.003, 2);
            $wechat['items'][] = array(
                'orderId' => $item->orderid,
                'amount' => $item->fact_amount,
                'handling_amount' => $handling_amount,
                'payDate' => date('Y/m/d H:i', $item->update_timestamp),
                'schoolid' => $item->schoolid,
            );
            $wechat['total'] += $item->fact_amount;
            $wechat['handling_amount'] += $handling_amount;
        }
        return array('wechatBus'=>$wechat);
    }

    public function getUniformWechatData($startdate='', $enddate='') {
        $ids = $this->getUniformLinkIds('wechat', $startdate, $enddate);
        if (!$ids) {
            return array('wechatUniform'=>array());
        }
        $wechat = array('total'=>0);
        Yii::import('common.models.wxpay.*');
        $criteria = new CDbCriteria();
        $criteria->compare('orderid', $ids);
        $criteria->compare('status', 1);
        $criteria->compare('settlement_status', 0);
        if($startdate)
            $criteria->compare('update_timestamp', '>='.strtotime($startdate));
        if($enddate)
            $criteria->compare('update_timestamp', '<='.strtotime($enddate.' 23:59:59'));
        $criteria->order='update_timestamp desc';
        $items = WechatPayOrder::model()->findAll($criteria);
        foreach($items as $item){
            $handling_amount = round($item->fact_amount * 0.003, 2);
            $wechat['items'][] = array(
                'orderId' => $item->orderid,
                'amount' => $item->fact_amount,
                'handling_amount' => $handling_amount,
                'payDate' => date('Y/m/d H:i', $item->update_timestamp),
                'schoolid' => $item->schoolid,
            );
            $wechat['total'] += $item->fact_amount;
            $wechat['handling_amount'] += $handling_amount;
        }
        return array('wechatUniform'=>$wechat);
    }

    public function getBusPosData($startdate='', $enddate='') {
        $ids = $this->getBusLinkIds('pos', $startdate, $enddate);
        if (!$ids) {
            return array('allinpayBus'=>array(), 'yeepayBus'=>array());
        }
        $yeePay = array('total'=>0);
        $allinPay = array('total'=>0);
        Yii::import('common.models.yeepay.*');
        $criteria = new CDbCriteria();
        $criteria->compare('status', 1);
        $criteria->compare('orderId', $ids);
        if($startdate)
            $criteria->compare('updateTime', '>='.strtotime($startdate));
        if($enddate)
            $criteria->compare('updateTime', '<='.strtotime($enddate.' 23:59:59'));
        $criteria->order='updateTime desc';
        $criteria->compare('payed_status', 0);
        $items = YeepayOrder::model()->findAll($criteria);
        foreach($items as $item){
            if($item->r2_TrxId == 'allinpay'){
                $allinPay['items'][] = array(
                    'orderId' => $item->orderId,
                    'amount' => $item->fact_amount,
                    'payDate' => date('Y/m/d H:i', strtotime($item->rp_PayDate)),
                    'schoolid' => $item->schoolId,
                );
                $allinPay['total'] += $item->fact_amount;
            }
            else{
                $yeePay['items'][] = array(
                    'orderId' => $item->orderId,
                    'amount' => $item->fact_amount,
                    'payDate' => date('Y/m/d H:i', strtotime($item->rp_PayDate)),
                    'schoolid' => $item->schoolId,
                );
                $yeePay['total'] += $item->fact_amount;
            }
        }
        return array('allinpayBus'=>$allinPay, 'yeepayBus'=>$yeePay);
    }

    /**
     * 获取泉发校车费相关订单ID
     *
     * @param [type] $type
     * @param [type] $startdate
     * @param [type] $enddate
     * @return array
     */
    public function getBusLinkIds($type, $startdate, $enddate)
    {
        $ids = array();
        Yii::import('common.models.invoice.*');
        $crit = new CDbCriteria;
        if ($startdate) {
            $crit->compare('timestampe', '>='.strtotime($startdate));
        }
        if ($enddate) {
            $crit->compare('timestampe', '<='.strtotime($enddate.' 23:59:59'));
        }
        $crit->compare('calendar_id', '>186');
        $crit->compare('amount', '>0');
        $crit->compare('schoolid', 'BJ_QFF');
        $crit->compare('payment_type', 'bus');
        if ($type == 'wechat') {
            $crit->compare('transactiontype', array(InvoiceTransaction::TYPE_WX_MICROPAY, InvoiceTransaction::TYPE_WX_NATIVE));
        } elseif ($type == 'pos') {
            $crit->compare('transactiontype',InvoiceTransaction::TYPE_POS_ALLINPAY);
        }
        $crit->compare('`inout`', Invoice::INVOICE_INOUT_IN);
        $transModelList = InvoiceTransaction::model()->findAll($crit);
        $invoiceIdList = array();
        foreach ($transModelList as $transModel) {
            $invoiceIdList[] = $transModel->invoice_id;
        }

        if (!$invoiceIdList) {
            return $ids;
        }
        if ($type == 'wechat') {
            Yii::import('common.models.wxpay.*');
            $crit = new CDbCriteria();
            $crit->compare('invoice_id', $invoiceIdList);
            $wechatPayItemModelList = WechatPayOrderItem::model()->findAll($crit);
            foreach ($wechatPayItemModelList as $wechatPayItemModel) {
                $ids[] = $wechatPayItemModel->orderid;
            }
            return $ids;
        } elseif ($type == 'pos') {
            Yii::import('common.models.yeepay.*');
            $crit = new CDbCriteria();
            $crit->compare('invocieId', $invoiceIdList);
            $yeePayItemModelList = YeepayOrderItem::model()->findAll($crit);
            foreach ($yeePayItemModelList as $yeePayItemModel) {
                $ids[] = $yeePayItemModel->it_orderId;
            }
            return $ids;
        } else {
            return $ids;
        }
    }

    /**
     * 获取泉发校服费相关订单ID
     *
     * @param [type] $type
     * @param [type] $startdate
     * @param [type] $enddate
     * @return array
     */
    public function getUniformLinkIds($type, $startdate, $enddate)
    {
        $ids = array();
        Yii::import('common.models.invoice.*');
        $crit = new CDbCriteria;
        if ($startdate) {
            $crit->compare('timestampe', '>='.strtotime($startdate));
        }
        if ($enddate) {
            $crit->compare('timestampe', '<='.strtotime($enddate.' 23:59:59'));
        }
        $crit->compare('calendar_id', '>188');
        $crit->compare('amount', '>0');
        $crit->compare('schoolid', 'BJ_QFF');
        $crit->compare('payment_type', 'uniform');
        if ($type == 'wechat') {
            $crit->compare('transactiontype', array(InvoiceTransaction::TYPE_WX_MICROPAY, InvoiceTransaction::TYPE_WX_NATIVE));
        } elseif ($type == 'pos') {
            $crit->compare('transactiontype',InvoiceTransaction::TYPE_POS_ALLINPAY);
        }
        $crit->compare('`inout`', Invoice::INVOICE_INOUT_IN);
        $transModelList = InvoiceTransaction::model()->findAll($crit);
        $invoiceIdList = array();
        foreach ($transModelList as $transModel) {
            $invoiceIdList[] = $transModel->invoice_id;
        }

        if (!$invoiceIdList) {
            return $ids;
        }
        if ($type == 'wechat') {
            Yii::import('common.models.wxpay.*');
            $crit = new CDbCriteria();
            $crit->compare('invoice_id', $invoiceIdList);
            $wechatPayItemModelList = WechatPayOrderItem::model()->findAll($crit);
            foreach ($wechatPayItemModelList as $wechatPayItemModel) {
                $ids[] = $wechatPayItemModel->orderid;
            }
            return $ids;
        } elseif ($type == 'pos') {
            Yii::import('common.models.yeepay.*');
            $crit = new CDbCriteria();
            $crit->compare('invocieId', $invoiceIdList);
            $yeePayItemModelList = YeepayOrderItem::model()->findAll($crit);
            foreach ($yeePayItemModelList as $yeePayItemModel) {
                $ids[] = $yeePayItemModel->it_orderId;
            }
            return $ids;
        } else {
            return $ids;
        }
    }

    public function actionDone()
    {
        Yii::import('common.models.yeepay.*');
        Yii::import('common.models.alipay.*');
        Yii::import('common.models.wxpay.*');
        Yii::import('common.models.bank.*');
        $type = Yii::app()->request->getParam('type', '');
        $thetime = Yii::app()->request->getParam('thetime', '');
        $selected = Yii::app()->request->getParam('selected', array());
        $bankid = Yii::app()->request->getParam('bankid', 0);
        $actual = Yii::app()->request->getParam('actual_amount', 0);
        $handling = Yii::app()->request->getParam('handling_amount', 0);
        $thetimestamp = strtotime($thetime);
        if($type && $selected && $thetime && $thetimestamp>0){
            if ($type == 'yeepayBus') {
                $type = 'yeepay';
            }
            if ($type == 'allinpayBus') {
                $type = 'allinpay';
            }
            if ($type == 'wechatBus') {
                $type = 'wechat';
            }
            if ($type == 'wechatUniform') {
                $type = 'wechat';
            }
            $mBranchs = AdmBranchLink::model()->getMultipleBranches(Yii::app()->user->id);
            if($type == 'alipay'){
                $criteira = new CDbCriteria();
                $criteira->compare('status', 1);
                $criteira->compare('settlement_status', 0);
                $criteira->compare('id', $selected);
                if($this->staff->profile->branch != 'BJ_TYG'){
                    $criteira->compare('schoolid', $mBranchs);
                }
                $items = AlipayOrder::model()->findAll($criteira);
                $total = 0;
                foreach($items as $item){
                    $total += $item->fact_amount;
                }

                $statement = new PmStatement();
                $statement->type = $type;
                $statement->partner_id = 'new account checking';
                $statement->settlement_title = $thetime.' 支付宝结算';
                $statement->xpay_settlement_amount = $total;
                $statement->ivy_settlement_amount = $total;
                $statement->xpay_settlement_time = $thetimestamp;
                $statement->toaccount_title = $thetime.' 支付宝到账';
                $statement->bank_toaccount_amount = $total;
                $statement->ivy_toaccount_amount = $total;
                $statement->bank_toaccount_time = $thetimestamp;
                $statement->status = 1;
                $statement->settlement_update_user = Yii::app()->user->id;
                $statement->toaccount_update_user = Yii::app()->user->id;
                $statement->bank_id = $bankid;
                $statement->updated = time();
                if($statement->save()){
                    foreach($items as $item){
                        $statement_link = new PmStatementOrderLink();
                        $statement_link->statement_id = $statement->id;
                        $statement_link->order_id = $item->id;
                        $statement_link->save();

                        $item->settlement_status = 1;
                        $item->save();
                    }
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', '成功');
                }
                else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '保存错误！');
                }
            }
            elseif($type == 'allinpayOnline'){
                $criteira = new CDbCriteria();
                $criteira->compare('status', 1);
                $criteira->compare('settlement_status', 0);
                $criteira->compare('id', $selected);
                if($this->staff->profile->branch != 'BJ_TYG'){
                    $criteira->compare('schoolid', $mBranchs);
                }
                $items = AlipayOrder::model()->findAll($criteira);
                $total = 0;
                foreach($items as $item){
                    $total += $item->fact_amount;
                }

                $statement = new PmStatement();
                $statement->type = $type;
                $statement->partner_id = 'new account checking';
                $statement->settlement_title = $thetime.' 通联在线结算';
                $statement->xpay_settlement_amount = $total;
                $statement->ivy_settlement_amount = $total;
                $statement->xpay_settlement_time = $thetimestamp;
                $statement->toaccount_title = $thetime.' 通联在线到账';
                $statement->bank_toaccount_amount = $total;
                $statement->ivy_toaccount_amount = $total;
                $statement->bank_toaccount_time = $thetimestamp;
                $statement->status = 1;
                $statement->settlement_update_user = Yii::app()->user->id;
                $statement->toaccount_update_user = Yii::app()->user->id;
                $statement->bank_id = $bankid;
                $statement->updated = time();
                if($statement->save()){
                    foreach($items as $item){
                        $statement_link = new PmStatementOrderLink();
                        $statement_link->statement_id = $statement->id;
                        $statement_link->order_id = $item->id;
                        $statement_link->save();

                        $item->settlement_status = 1;
                        $item->save();
                    }
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', '成功');
                }
                else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '保存错误！');
                }
            }
            elseif($type == 'yeepay'){
                $criteira = new CDbCriteria();
                $criteira->compare('status', 1);
                $criteira->compare('payed_status', 0);
                $criteira->compare('orderId', $selected);
                if($this->staff->profile->branch != 'BJ_TYG'){
                    $criteira->compare('schoolId', $mBranchs);
                }
                $items = YeepayOrder::model()->findAll($criteira);
                $total = 0;
                $orderIds = array();
                foreach($items as $item){
                    $total += $item->fact_amount;
                    $orderIds[] = $item->orderId;
                }

                $account = new YpCpAccountHistory;
                $account->orderIds = implode(',', $orderIds);
                $account->title = $thetime.' 易宝到账';
                $account->settlement_amount = $total;
                $account->settlement_actual_amount = $total;
                $account->bank_title = $account->title;
                $account->bank_amount = $total;
                $account->bank_actual_amount = $total;
                $account->date_timestamp = $thetimestamp;
                $account->bank_userid = Yii::app()->user->id;
                $account->status = 20;
                $account->userid = Yii::app()->user->id;
                $account->update_timestamp = time();
                $account->yeepay_timestamp = $thetimestamp;
                $account->bank_timestamp = $thetimestamp;
                $account->p1_MerId = 'new account checking';
                $account->bank_id = $bankid;
                if($account->save()){
                    foreach($items as $item){
                        $account_link = new YpCpAccountDetail();
                        $account_link->settlementId = $account->id;
                        $account_link->orderId = $item->orderId;
                        $account_link->save();

                        $item->payed_status = 20;
                        $item->save();
                    }
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', '成功');
                }
                else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '保存错误！');
                }
            }
            elseif($type == 'allinpay'){
                $criteira = new CDbCriteria();
                $criteira->compare('status', 1);
                $criteira->compare('payed_status', 0);
                $criteira->compare('orderId', $selected);
                if($this->staff->profile->branch != 'BJ_TYG'){
                    $criteira->compare('schoolId', $mBranchs);
                }
                $items = YeepayOrder::model()->findAll($criteira);
                $total = 0;
                foreach($items as $item){
                    $total += $item->fact_amount;
                }

                $statement = new PmStatement();
                $statement->type = $type;
                $statement->partner_id = 'new account checking';
                $statement->settlement_title = $thetime.' 通联结算';
                $statement->xpay_settlement_amount = $total;
                $statement->ivy_settlement_amount = $total;
                $statement->xpay_settlement_time = $thetimestamp;
                $statement->toaccount_title = $thetime.' 通联到账';
                $statement->bank_toaccount_amount = $total;
                $statement->ivy_toaccount_amount = $total;
                $statement->bank_toaccount_time = $thetimestamp;
                $statement->status = 1;
                $statement->settlement_update_user = Yii::app()->user->id;
                $statement->toaccount_update_user = Yii::app()->user->id;
                $statement->bank_id = $bankid;
                $statement->updated = time();
                if($statement->save()){
                    foreach($items as $item){
                        $statement_link = new PmStatementOrderLink();
                        $statement_link->statement_id = $statement->id;
                        $statement_link->order_id = $item->orderId;
                        $statement_link->save();

                        $item->payed_status = 20;
                        $item->save();
                    }
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', '成功');
                }
                else{
                    $this->addMessage('state', 'fail');
                    $errs = current($statement->getErrors());
                    $this->addMessage('message', $errs[0]);
                }
            }
            elseif($type == 'wechat'){
                $criteira = new CDbCriteria();
                $criteira->compare('status', 1);
                $criteira->compare('settlement_status', 0);
                $criteira->compare('orderid', $selected);
                if($this->staff->profile->branch != 'BJ_TYG'){
                    $criteira->compare('schoolid', $mBranchs);
                }
                $items = WechatPayOrder::model()->findAll($criteira);
                $total = 0;
                $handling_amount = 0;
                foreach($items as $item){
                    $total += $item->fact_amount;
                    $handling_amount += round($item->fact_amount * 0.003, 2);
                }
                $actual_amount = $total - $handling_amount;
                if (abs($actual - $actual_amount) >= 0.01) {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '到账总额不符');
                    $this->showMessage();
                }
                if (abs($handling - $handling_amount) >= 0.01) {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '到账手续费不符');
                    $this->showMessage();
                }
                // 判断金额的正确性
                if (abs($total - $actual_amount - $handling_amount) >= 0.01) {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '金额错误');
                    $this->showMessage();
                }

                $statement = new PmStatement();
                $statement->type = $type;
                $statement->partner_id = 'new account checking';
                $statement->settlement_title = $thetime.' 微信结算';
                $statement->xpay_settlement_amount = $total;
                $statement->ivy_settlement_amount = $total;
                $statement->xpay_settlement_time = $thetimestamp;
                $statement->toaccount_title = $thetime.' 微信到账';
                $statement->bank_toaccount_amount = $total;
                $statement->ivy_toaccount_amount = $total;
                $statement->actual_toaccount_amount = $actual_amount;
                $statement->handling_amount = $handling_amount;
                $statement->bank_toaccount_time = $thetimestamp;
                $statement->status = 1;
                $statement->settlement_update_user = Yii::app()->user->id;
                $statement->toaccount_update_user = Yii::app()->user->id;
                $statement->bank_id = $bankid;
                $statement->updated = time();
                if($statement->save()){
                    foreach($items as $item){
                        $statement_link = new PmStatementOrderLink();
                        $statement_link->statement_id = $statement->id;
                        $statement_link->order_id = $item->orderid;
                        $statement_link->save();

                        $item->settlement_status = 1;
                        $item->handling_amount = round($item->fact_amount * 0.003, 2);
                        $item->save();
                    }
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', '成功');
                }
                else{
                    $this->addMessage('state', 'fail');
                    $errs = current($statement->getErrors());
                    $this->addMessage('message', $errs[0]);
                }
            }
        }
        else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '系统错误！');
        }
        $this->showMessage();
    }

    public function actionViewOrder($orderid='', $type='')
    {
        Yii::import('common.models.visit.*');
        Yii::import('common.models.invoice.*');
        $child_name = "";
        if($type == 'alipay'||$type == 'allinpayOnline'){
            Yii::import('common.models.alipay.*');
            $model = AlipayOrder::model()->findByPk($orderid);
        }
        elseif ($type == 'wechat' || $type == 'wechatBus' || $type == 'wechatUniform') {
            $type = 'wechat';
            Yii::import('common.models.wxpay.*');
            $model = WechatPayOrder::model()->findByPk($orderid);
        }
        else{
            if ($type == 'allinpayBus') {
                $type = 'allinpay';
            }
            if ($type == 'yeepayBus') {
                $type = 'yeepay';
            }
            Yii::import('common.models.yeepay.*');
            $model = YeepayOrder::model()->findByPk($orderid);
        }
        $admission_id = "";
        if(empty($model->ChildProfile)){
            if(count($model->items) == 1){
                $admission_id  = $model->items[0]->invoice->admission_id;
            }
            $child_name = AdmissionsDs::model()->findByPk($admission_id);
        }

        $this->renderPartial('vieworder', array('type'=>$type, 'model'=>$model, 'child_name'=>$child_name));
    }

    public function actionViewStatement($id=0, $type='')
    {
        Yii::import('common.models.alipay.*');
        Yii::import('common.models.yeepay.*');
        Yii::import('common.models.wxpay.*');
        Yii::import('common.models.invoice.*');
        Yii::import('common.models.visit.*');
        $this->getAllBranch();
        if($type == 'yeepay'){
            $items = YpCpAccountDetail::model()->findAllByAttributes(array('settlementId'=>$id));
        }
        else{
            $items = PmStatementOrderLink::model()->findAllByAttributes(array('statement_id'=>$id));
        }
        $data = array();
        $admission_id_t = array();
        $child_name_t = array();
        foreach($items as $item) {
            if ($type == 'yeepay') {
                $orderid = $item->orderId;
            } else {
                $orderid = $item->order_id;
            }
            if ($type == 'allinpay') {
                $order = $item->allinpayorder;
            } elseif ($type == 'wechatpay') {
                $order = $item->wxpayorder;
            } else {
                $order = $item->order;
            }

            $schoolid = $type == 'alipay' || $type == 'wechatpay' || $type == 'allinpayOnline' ? $order->schoolid : $order->schoolId;

            $data[$schoolid]['orderid'][] = $orderid;
            $data[$schoolid]['order'][] = $order;
            if ($type == 'wechatpay') {
                $data[$schoolid]['actual'] += ($order->fact_amount - $order->handling_amount);
                $data[$schoolid]['handling_amount'] += $order->handling_amount;
            }

            if (!$order->ChildProfile) {
                if (count($order->items) == 1) {
                    $admission_id_t[$order->items[0]->invoice->admission_id] = $order->items[0]->invoice->admission_id;
                }
            }

        }

        $wechatInfo = array();
        if($type == 'wechatpay'){
            $pmStatementModel = PmStatement::model()->findByPk($id);
            $wechatInfo['handling_amount'] = $pmStatementModel->handling_amount;
            $wechatInfo['actual_toaccount_amount'] = $pmStatementModel->actual_toaccount_amount;

        }

        $criteria = new CDbCriteria();
        $criteria->compare('id', $admission_id_t);
        $adissionModel = AdmissionsDs::model()->findAll($criteria);
        foreach($adissionModel as $model){
            $child_name_t[$model->id] = $model->getName();
        }

        $this->renderPartial('viewstatement', array('wechatInfo' => $wechatInfo, 'type'=>$type, 'child_name_t'=> $child_name_t ,'id'=>$id, 'data'=>$data, 'schools'=>$this->allBranch));
    }

    public function actionExport($id=0, $type='', $schoolid='')
    {
        Yii::import('common.models.alipay.*');
        Yii::import('common.models.yeepay.*');
        Yii::import('common.models.wxpay.*');
        Yii::import('common.models.invoice.*');
        Yii::import('application.components.policy.*');
        Yii::import('common.models.visit.*');
        $policyApi = new PolicyApi($schoolid);
        $schoolobj = Branch::model()->findByPk($schoolid);
        $orderids = array();
        if($type == 'yeepay'){
            $model = YpCpAccountHistory::model()->findByPk($id);
            $orderids = explode(',', $model->orderIds);
            $filename = $schoolobj->abb.' '.$type.' '.$model->bank_title;
        }
        else{
            $model = PmStatement::model()->with('items')->findByPk($id);
            foreach($model->items as $item){
                $orderids[] = $item->order_id;
            }
            $filename = $schoolobj->abb.' '.$type.' '.$model->toaccount_title;
        }

        if($type == 'alipay' || $type == 'allinpayOnline'){
            $criteria = new CDbCriteria();
            $criteria->compare('order_id', $orderids);
            $criteria->compare('schoolid', $schoolid);
            $items = AlipayOrder::model()->with('items')->findAll($criteria);
        }
        elseif ($type == 'wechatpay') {
            $criteria = new CDbCriteria();
            $criteria->compare('orderid', $orderids);
            $criteria->compare('schoolid', $schoolid);
            $items = WechatPayOrder::model()->findAll($criteria);
        }
        else{
            $criteria = new CDbCriteria();
            $criteria->compare('orderId', $orderids);
            $criteria->compare('schoolId', $schoolid);
            $items = YeepayOrder::model()->with('items')->findAll($criteria);
        }

        header("Content-type:application/vnd.ms-excel");
        header("Content-Disposition:attachment;filename=$filename.xls");
        echo $this->_t("订单号")."\t";
        echo $this->_t("孩子ID")."\t";
        echo $this->_t("孩子信息")."\t";
        echo $this->_t("订单总额")."\t";
        echo $this->_t("刷卡成功时间")."\t";
        echo $this->_t("账单标题")."\t";
        echo $this->_t("收费所属区间")."\t";
        echo $this->_t("账单金额")."\t";
        echo $this->_t("POS/网银金额/微信")."\t";
        echo $this->_t("发票号")."\t";
        echo $this->_t("其他金额")."\t";
        echo $this->_t("学校")."\t";
        echo $this->_t("班级")."\t";
        echo $this->_t("费用类型")."\t";
        echo $this->_t("折扣")."\t";
        echo "\n";
        $total = 0;
        foreach($items as $_item){
            foreach($_item->items as $item){
                echo '#'.(($type == 'alipay' || $type == 'allinpayOnline') ? $_item->id :  ($type == 'wechatpay' ? $_item->orderid : $_item->orderId))."\t";
                echo $item->invoice->childid."\t";
                if(!$item->invoice->childid && $item->invoice->admission_id){
                    $amodel = AdmissionsDs::model()->findByPk($item->invoice->admission_id);
                    echo $this->_t($amodel->getName())."\t";
                }
                else{
                    echo $this->_t($item->invoice->childprofile->getChildName())."\t";
                }
                echo $_item->fact_amount."\t";
                echo (($type == 'alipay' || $type == 'wechatpay' || $type == 'allinpayOnline') ? date('Y-m-d H:i:s', $_item->update_timestamp) : date('Y-m-d H:i:s', $_item->updateTime))."\t";
                echo $this->_t($item->invoice->title)."\t";
                if($item->invoice->startdate && $item->invoice->enddate){
                    echo date('Y/m/d', $item->invoice->startdate).' - '.date('Y/m/d', $item->invoice->enddate)."\t";
                }
                else{
                    echo "\t";
                }
                echo $item->invoice->amount."\t";
                $tAmount = 0;
                $extAmount = '';
                $fapiaoNum = array();
                foreach($item->invoice->invoiceTransaction as $tran){
                    if($type == 'alipay' || $type == 'allinpayOnline'){
                        if($tran->transactiontype == InvoiceTransaction::TYPE_ONLINEPAYMENT || $tran->transactiontype == InvoiceTransaction::TYPE_ONLINE_ALLINPAY){
                            // $tAmount += $tran->amount;
                            $tAmount = $item->amount;
                            $fapiaoNum[$tran->invoice_number] = '#'.$tran->invoice_number;
                        }
                        else{
                            $extAmount .= $policyApi->renderPaymentType($tran->transactiontype).": ".$tran->amount." ";
                        }
                    }
                    elseif($type == 'allinpay'){
                        if($tran->transactiontype == InvoiceTransaction::TYPE_POS_ALLINPAY){
                            // $tAmount += $tran->amount;
                            $tAmount = $item->payable_amount;
                            $fapiaoNum[$tran->invoice_number] = '#'.$tran->invoice_number;
                        }
                        else{
                            $extAmount .= $policyApi->renderPaymentType($tran->transactiontype).": ".$tran->amount." ";
                        }
                    }
                    elseif($type == 'yeepay'){
                        if($tran->transactiontype == InvoiceTransaction::TYPE_POS){
                            // $tAmount += $tran->amount;
                            $tAmount = $item->payable_amount;
                            $fapiaoNum[$tran->invoice_number] = '#'.$tran->invoice_number;
                        }
                        else{
                            $extAmount .= $policyApi->renderPaymentType($tran->transactiontype).": ".$tran->amount." ";
                        }
                    }
                    elseif($type == 'wechatpay'){
                        if($tran->transactiontype == InvoiceTransaction::TYPE_WX_MICROPAY || $tran->transactiontype == InvoiceTransaction::TYPE_WX_NATIVE){
                            // $tAmount += $tran->amount;
                            $tAmount = $item->amount;
                            $fapiaoNum[$tran->invoice_number] = '#'.$tran->invoice_number;
                        }
                        else{
                            $extAmount .= $policyApi->renderPaymentType($tran->transactiontype).": ".$tran->amount." ";
                        }
                    }
                }
                echo $tAmount."\t";
                echo implode(', ', $fapiaoNum)."\t";
                $total += $tAmount;
                echo $this->_t($extAmount)."\t";
                echo $schoolobj->abb."\t";
                echo $item->invoice->classInfo->title."\t";
                echo $this->_t($policyApi->renderFeeType($item->invoice->payment_type))."\t";
                if($item->invoice->discount_id){
                    echo $this->_t($item->invoice->discount->discountTitle->title_cn)."\t";
                }
                echo "\n";
            }
        }
        echo "\t";
        echo "\t";
        echo "\t";
        echo "\t";
        echo "\t";
        echo "\t";
        echo "\t";
        echo $total."\t";
        echo "\t";
        echo "\t";
        echo "\t";
        echo "\t";
        echo "\t";
        echo "\t";
    }

    public function _t($str='')
    {
        return iconv('utf-8', 'gbk', $str);
    }
}
