<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'crm-record-form',
	'enableAjaxValidation'=>false,
	'htmlOptions'=>array('class'=>'J_ajaxForm'),
));
?>
<div class="pop_cont pop_table" style="height:300px;">
    <table width="100%">
        <colgroup>
            <col class="th" />
            <col />
        </colgroup>
        <tbody>
            <tr class="J_unique">
                <th><?php echo $form->labelEx($model,'category'); ?></th>
                <td>
                    <?php echo $form->radioButtonList($model,'category',$this->cfg['category'],array('class'=>'J_cate'));?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'category'); ?></div></td>
            </tr>
            <tr class="J_unique J_visit<?php if ($model->category == 'appointment'):?> dn<?php endif;?>">
                <th><?php echo $form->labelEx($model,'visit_timestamp'); ?></th>
                <td>
                    <?php
                    $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                        "model"=>$model,
                        "attribute"=>"visit_timestamp",
                        "options"=>array(
                            'changeMonth'=>true,
                            'changeYear'=>true,
                            'dateFormat'=>'yy-mm-dd',
                        ),
                        'htmlOptions'=>array(
                            'class'=>'input'
                        ),
                    ));
                    ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'visit_timestamp'); ?></div></td>
            </tr>
            <tr class="J_unique J_appointment<?php if ($model->category == 'visit'):?> dn<?php endif;?>">
                <th><?php echo $form->labelEx($model,'appointment_date'); ?></th>
                <td>
                    <?php
                    $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                        "model"=>$model,
                        "attribute"=>"appointment_date",
                        "options"=>array(
                            'changeMonth'=>true,
                            'changeYear'=>true,
                            'dateFormat'=>'yy-mm-dd',
                        ),
                        'htmlOptions'=>array(
                            'class'=>'input'
                        ),
                    ));
                    ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'appointment_date'); ?></div></td>
            </tr>
            <tr class="J_unique J_appointment<?php if ($model->category == 'visit'):?> dn<?php endif;?>">
                <th><?php echo $form->labelEx($model,'appointment_time'); ?></th>
                <td>
                    <?php echo $form->textField($model,'appointment_time',array('maxlength'=>255,'class'=>'input length_4')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'appointment_time'); ?></div></td>
            </tr>
            <tr class="J_unique">
                <th><?php echo $form->labelEx($model,'schoolid'); ?></th>
                <td>
                    <?php echo $form->dropDownList($model,'schoolid',$adBranch,array('class'=>'select_4','empty'=>Yii::t('global','Please Select')));?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'schoolid'); ?></div></td>
            </tr>
        </tbody>
    </table>
</div>
<div class="pop_bottom">
    <button type="button" class="btn fr" id="J_dialog_close"><?php echo Yii::t('global','Cancel');?></button>
    <button class="btn fr btn_submit mr10 J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
</div>
<?php $this->endWidget(); ?>


<script type="text/javascript">
$('.J_cate').change(function(){
    if ( $(this).val() == 'visit' ){
        $('.J_visit').show();
        $('.J_appointment').hide();
    }
    else {
        $('.J_visit').hide();
        $('.J_appointment').show();
    }
});
</script>