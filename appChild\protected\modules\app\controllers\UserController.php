<?php

class UserController extends Controller
{
	public function actionLogin()
	{
		if(Yii::app()->user->isGuest){
			$this->layout = '//layouts/column2';
			$this->render('login');
		}
		else{
			$this->redirect('/app/user/home');
		}
	}

	public function actionHome()
	{
		if ( Yii::app()->user->getId() ){
			$childids=Yii::app()->user->getAdminCids();
			if(count($childids) > 1){
				$this->redirect('/app/user/select');
			}
			else{
				$this->redirect(array("//app/default/index", "childid"=>$childids[0]));
			}
		}
	}

	public function actionLogout()
	{
		Yii::app()->user->logout();
		$this->redirect('/app');
	}
}