<?php 
/*
 * <PERSON><PERSON>
* 2012-7-10
*
*/

?>

<?php 

if(!empty($vote_infos)):

Mims::<PERSON><PERSON>Helper('HtoolKits');
$tk = new HtoolKits();

$operate_id = 0;

?>
<div id="vote-box" class="votebox">
	<h4>
		<span><?php echo Yii::t("vote", 'Please Vote');?></span>
	</h4>
	<div id="vote_info" class="voteinfo">
		<?php foreach ($vote_infos as $vote_info):
		
		if($vote_info->vote_model){
			$operate_id = Yii::app()->user->id;
		}else{
			$operate_id = $this->childId;
		}
		
		?>
		<div class="poll pd10 prebg">
			<h5 class="vtitle">
				<a
					href="<?php echo $this->controller->createUrl('vote/index',array('id'=>$vote_info->id));?>"
					target="_blank"> <?php echo $tk->getContentByLang($vote_info->title_cn, $vote_info->title_en);?>
				</a>
			</h5>
			<div class="polldesc">
				<?php echo $tk->getContentByLang($vote_info->detail_description_cn, $vote_info->detail_description_en);?>
			</div>
			<ul class="optpreview fl">
				<?php 
				if(!empty($vote_info->options)):
				foreach ($vote_info->options as $index => $option):
				if($index> 1){
					break;
				}
				?>
				<li class="vopt">
				
				<?php if($vote_info->option_number > 1):;?>
				
				<span class="operate_input"> 
				<input type="checkbox" disabled="true">
				</span>
				<?php else:;?>
				<span class="operate_input"> <input type="radio"
						disabled="true">
				</span>
				<?php endif;?>
				<?php echo $tk->getContentByLang($option->title_cn, $option->title_en);?>
				</li>
				<?php endforeach;?>
				<?php endif;?>

				<li class="vopt">&nbsp;<span class="shenglue"> <a
						href="<?php echo $this->controller->createUrl('vote/index',array('id'=>$vote_info->id));?>" target="_blank"> . . . </a>
				</span>
				</li>
			</ul>
			<div class="pollact">
				<span class="fr btn001">
				<?php if(WVoteResult::model()->checkVoted($vote_info->id,$operate_id)):?>
				<a
					href="<?php echo $this->controller->createUrl('vote/index',array('id'=>$vote_info->id));?>" target="_blank"><span><?php echo Yii::t("vote", 'View Result');?></span>
				</a> 
				<?php else:?>
				<a href="<?php echo $this->controller->createUrl('vote/index',array('id'=>$vote_info->id));?>" target="_blank"><span><?php echo Yii::t("vote", 'I want to vote');?></span>
				</a>
				<?php endif;?>
				</span>
			</div>
			<div style="clear: both;"></div>
			<div class="pollother">
				<span class="fr txt"><em><?php echo Yii::t("vote", 'End Date');?></em><span>
				<?php 
				if(!empty($vote_info->end_time)){
					echo Mims::formatDateTime($vote_info->end_time);
				}
				?>
				</span>
				</span> <span class="fr txt"><em><?php echo Yii::t("vote", 'Start Date');?></em><span>
				<?php 
				if(!empty($vote_info->start_time)){
					echo Mims::formatDateTime($vote_info->start_time);
				}
				?>
				</span>
				</span>
			</div>
			<div style="clear: both;"></div>
		</div>
		<?php endforeach;?>
	</div>
	<div class="c"></div>
</div>
<?php endif;?>
