<?php
return array(
    'Uniform Violation' => '校服违规',
    'Student Tardys' => '学生迟到',
    'All' => '全部',
    'Please select'=>'请选择',
    'Consequence Pending' => '待处理',
    'Record Consequence' => '标记处理',
    '%s Selected' => '已选择%s个',
    'Record' => '标记次数',
    'Consequence' => '处理结果',
    "By" => '处理人',
    'Time' => '处理时间',
    'Number' =>'序号',
    'Course' => '课程',
    'Date' => '日期',
    'Comment' => '备注',
    'Last Monday' => '上周一',
    'This Monday' => '本周一',
    'Next Monday' => '下周一',
    'Tardy times exclude Homeroom period.' => '迟到只统计1-7节课，Homeroom不做统计。',
    'When the unprocessed tardy numbers accumulate to a multiple of 5, they need to be handled.' => '当迟到次数累计到5的整数倍时，需要进行处理。',
    'When the unprocessed violation numbers accumulate to a multiple of 5, they need to be handled.' => '当违规次数累计到5的整数倍时，需要进行处理。',
    'Period' => '时段',
    'Refresh' => '刷新数据'
);
