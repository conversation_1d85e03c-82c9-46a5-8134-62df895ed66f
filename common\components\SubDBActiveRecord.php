<?php
/**
 * 链接存储在SubDB中的Model
 */

class SubDBActiveRecord extends CActiveRecord
{
    public static $subdb = null;

    protected static function getSubDbConnection()
    {
        if (self::$subdb !== null)
            return self::$subdb;
        else
        {
            self::$subdb = Yii::app()->subdb;
            if (self::$subdb instanceof CDbConnection)
            {
                self::$subdb->setActive(true);
                return self::$subdb;
            }
            else
                throw new CDbException(Yii::t('yii','Active Record requires a "db" CDbConnection application component.'));
        }
    }

    public function getDbConnection()
    {
        return self::getSubDbConnection();
    }
}