<?php

/**
 * This is the model class for table "ivy_achievement_report_child_course".
 *
 * The followings are the available columns in table 'ivy_achievement_report_child_course':
 * @property integer $id
 * @property integer $timetable_records_id
 * @property integer $timetable_records_code
 * @property integer $calender
 * @property integer $childid
 * @property integer $reportid
 * @property integer $teacherid
 * @property integer $courseid
 * @property integer $sort
 * @property string $total_id
 * @property string $myp
 * @property string $teacher_message_cn
 * @property string $teacher_message_en
 * @property string $student_message_cn
 * @property string $student_message_en
 * @property string $template_data
 * @property integer $status
 * @property integer $create_id
 * @property integer $update_id
 * @property integer $create_time
 * @property integer $update_time
 */
class AchievementReportChildCourse extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_achievement_report_child_course';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('childid, timetable_records_id, timetable_records_code, teacherid, courseid, status, create_id, update_id, create_time, update_time', 'required'),
			array('childid, reportid, calender, timetable_records_id, teacherid, courseid, sort, status, create_id, update_id, create_time, update_time', 'numerical', 'integerOnly'=>true),
			array('total_id, timetable_records_code, myp', 'length', 'max'=>255),
//			array('teacher_message_en , student_message_en', 'length', 'max'=>600),
			array('teacher_message_cn, student_message_cn ', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, childid, reportid, calender, timetable_records_id, timetable_records_code, teacherid, courseid, sort, total_id, myp, teacher_message_cn, teacher_message_en, student_message_cn, student_message_en, status, create_id, update_id, create_time, update_time', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			"reportCourse" => array(self::BELONGS_TO, 'AchievementReportCourse', array('courseid' => "id")),
			"childFraction" => array(self::HAS_MANY, 'AchievementReportChildFraction', array('cid' => "id", 'report_id' => 'reportid'), 'with' => 'courseStandard', 'order' => 'courseStandard.title_cn'),
			"childTotal" => array(self::BELONGS_TO, 'AchievementReportChildTotal', array('total_id' => "id")),
			"teacherName" => array(self::BELONGS_TO, 'User', array('teacherid' => "uid")),
			"timetableCourse" => array(self::HAS_ONE, 'TimetableCourses', array('id' => "timetable_records_id")),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'timetable_records_id' => 'Timetable Records Id',
			'timetable_records_code' => 'timetable_records_code',
			'calender' => 'Calender',
			'childid' => 'Childid',
			'reportid' => 'Reportid',
			'teacherid' => 'Teacherid',
			'courseid' => 'Courseid',
			'sort' => 'Sort',
			'total_id' => 'Total',
			'myp' => 'Myp',
			'teacher_message_cn' => 'Teacher Message Cn',
			'teacher_message_en' => 'Teacher Message En',
			'student_message_cn' => 'Student Message Cn',
			'student_message_en' => 'Student Message En',
			'status' => 'Status',
			'create_id' => 'Create',
			'update_id' => 'Update',
			'create_time' => 'Create Time',
			'update_time' => 'Update Time',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('timetable_records_id',$this->timetable_records_id);
		$criteria->compare('timetable_records_code',$this->timetable_records_code);
		$criteria->compare('calender',$this->calender);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('reportid',$this->reportid);
		$criteria->compare('teacherid',$this->teacherid);
		$criteria->compare('courseid',$this->courseid);
		$criteria->compare('sort',$this->sort);
		$criteria->compare('total_id',$this->total_id,true);
		$criteria->compare('myp',$this->myp,true);
		$criteria->compare('teacher_message_cn',$this->teacher_message_cn,true);
		$criteria->compare('teacher_message_en',$this->teacher_message_en,true);
		$criteria->compare('student_message_cn',$this->student_message_cn,true);
		$criteria->compare('student_message_en',$this->student_message_en,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('create_id',$this->create_id);
		$criteria->compare('update_id',$this->update_id);
		$criteria->compare('create_time',$this->create_time);
		$criteria->compare('update_time',$this->update_time);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AchievementReportChildCourse the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
