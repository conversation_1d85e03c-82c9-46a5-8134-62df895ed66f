<h1>
<?php echo Yii::t("guide","Payment Items") ?>
</h1>
<?php if (Yii::app()->language == 'en_us'): ?>
<p>
	Below explains what each sub navigation means:
</p>

<p>
	<strong><?php echo Yii::t("navigations","Summary") ?>:&nbsp;</strong>This page displays all the payment history as well as the balance.
</p>

<p>
	<strong><?php echo Yii::t("navigations","Payment History") ?>:&nbsp;</strong>You can check the details of each payment that has been made to the school and print out the receipts easily.
</p>

<p>
	<strong><?php echo Yii::t("navigations","Tuition Deposit") ?>:&nbsp;</strong>This credit is the balance of the deposit that you have paid to guarantee a seat in advance and will be automatically applied to your outstanding tuition invoices.
</p>

<p>
	<strong><?php echo Yii::t("navigations","General Credit") ?>:&nbsp;</strong>This credit is generated from tuition, lunch and other refunds.  It can be applied to any invoice, or you can also request for a cash refund.  To use, please <a href="<?php echo Yii::app()->getController()->createUrl('//child/support/email');?>">inform</a> our campus support staff of this request.
</p>

<p>
	<strong><?php echo Yii::t("navigations","Make Payment") ?>:&nbsp;</strong>You can process unpaid invoices either through online banking or bank transfer.
</p>
<?php else: ?>

<p>
	<strong><?php echo Yii::t("navigations","Summary") ?>：&nbsp;</strong>该页面显示历史付款记录以及账户余额信息等。
</p>

<p>
	<strong><?php echo Yii::t("navigations","Payment History") ?>：&nbsp;</strong>您可以查看付给学校的每笔款项记录，并且轻松打印收据。
</p>

<p>
	<strong><?php echo Yii::t("navigations","Tuition Deposit") ?>：&nbsp;</strong>预缴学费是为了保证学位而提前缴纳的学费押金，该部分金额将会自动转入您的未付学费账单中。
</p>

<p>
	<strong><?php echo Yii::t("navigations","General Credit") ?>：&nbsp;</strong>个人账户中的余额是从学费、餐费以及其它类型的退费中产生。这部分金额可以转入任何费用账单中，或者您也可以申请现金退款。如果您需要使用个人账户余额，请<a href="<?php echo Yii::app()->getController()->createUrl('//child/support/email');?>">通知</a>我们的校园支持团队。
</p>

<p>
	<strong><?php echo Yii::t("navigations","Make Payment") ?>:&nbsp;</strong>您可以通过网银或银行转账支付未付账单。
</p>
<?php endif ?>