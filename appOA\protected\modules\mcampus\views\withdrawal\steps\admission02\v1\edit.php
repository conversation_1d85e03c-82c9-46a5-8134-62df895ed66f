<div>
    <div class='admissionData'>
        <form class="J_ajaxForm formStu" action="<?php echo $this->createUrl("saveNode");?>" method="post" onsubmit="return check(this)">
            <div class="panel panel-default">
                <div class="panel-heading"><?php echo Yii::t('withdrawal','Admissions confirmation');?></div>
                <div class="panel-body">
                    <div class='ml8'>
                        <div class=' form-horizontal font14'>
                            <div class='notTalkData'>
                                <div class='flex flexStart mb20'>
                                    <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Final student status');?></div>
                                    <div class='flex1 color3'>
                                        <label class="checkbox-inline pt0 lableHeight">
                                            <input type="checkbox" class='notTalk' name="student_status"  value="1"> <?php echo Yii::t('withdrawal','Completed');?>
                                        </label>
                                        <span class='bluebg ml20 font12'><a href="" class='childStatus' target="_blank">需手动处理</a> <span class='el-icon-arrow-right'></span></span>
                                    </div>
                                </div>
                                <div class='flex mb20 flexStart'>
                                    <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Final withdrawal date');?></div>
                                    <div class='flex1'>
                                        <input type="text" class="form-control length_3 pull-left mr20" name='withdrawal_date'   id="dropout_date"   placeholder="<?php echo Yii::t("newDS", "Select a date");?>"  >
                                    </div>
                                </div>
                                <div class='flex mb20 flexStart'>
                                    <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Ultimate withdrawal reason');?></div>
                                    <div class='flex1'>
                                        <div class='mb12' id='withdrawal_reason'></div>
                                        <input type="text" class="form-control"  name='withdrawal_reason_memo' placeholder="<?php echo Yii::t('withdrawal','Comment');?>">
                                    </div>
                                </div>
                                <div class='flex  flexStart'>
                                    <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Comment');?></div>
                                    <div class='flex1'>
                                        <textarea class="form-control" name='memo' rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <input type="hidden" name="type" id="type" value='admission02'>
            <input type="hidden" name="applicationId" id="applicationId">
            <input type="hidden" name="node" id="node">
            <div class='modal-footer borderTopNone'>
                <label class="checkbox-inline">
                    <input type="checkbox" name="status" value="1"> <?php echo Yii::t('withdrawal', 'Mark this step as completed'); ?>
                </label>
                <button type="submit" class="btn btn-primary ml24 submitSend"><?php echo Yii::t('global','Submit');?></button>
                <button type="button" class="btn btn-primary J_ajax_submit_btn ml24"><?php echo Yii::t('global','Submit');?></button>
            </div>
        </form>
    </div>
</div>
<script>
    // 第一步
    initDate()
    function initDate() {
        $('input[name="applicationId"]').val(container.applicationId)
        $('input[name="node"]').val(container.stepName)
        let indexData=container.indexDataList
        for(let key in indexData.reasonList){
            let option='<label class="radio-inline lableHeight mr10">'+
                '<input type="radio" name="withdrawal_reason" value="'+key+'"> '+indexData.reasonList[key]+''+
            '</label>'
            $('#withdrawal_reason').append(option)
        }
        $('#dropout_date').datepicker({
            dateFormat: 'yy-mm-dd',
            selectOtherMonths: false,
            showOtherMonths: false,
            changeMonth: true,
            changeYear: true,
            firstDay: 0,
            onSelect: function (date,str) {
                $('#dropout_date').val(date)
            }
        });
       let hrefUrl="<?php echo $this->createUrl('/child/index/status'); ?>&childid="+childDetails.info.baseInfo.childid
        $('.childStatus').attr('href',hrefUrl)
        $('.submitSend').show()
        $('.J_ajax_submit_btn').hide()
        let data=childDetails.forms[0].data
        if(data){
            $('input[name="student_status"][value='+data.student_status+']').prop('checked', true);
            $('input[name="withdrawal_reason"][value='+data.withdrawal_reason+']').prop('checked', true);
            $('input[name="withdrawal_date"]').val(data.withdrawal_date)
            $('input[name="withdrawal_reason_memo"]').val(data.withdrawal_reason_memo)
            $('textarea[name="memo"]').val(data.memo)
        }
    }
    function check(_this){
        let no_talk=$('input[name="no_talk"]:checked').val()
        $('#J_fail_info').remove();
        var button = $(_this).find('button[type="submit"]');
        var flag = true, msg=[];
      
        if($('input[name="withdrawal_date"]').val()==''){
            flag = false;
            msg.push('<?php echo Yii::t("teaching","请选择退学日期");?>');
        }
        if($('input[name="withdrawal_reason"]:checked').length == 0){
            flag = false;
            msg.push('<?php echo Yii::t("teaching","请选择退学原因");?>');
        }
        if(flag){
            $('.submitSend').hide()
            $('.J_ajax_submit_btn').show()
            $('.formStu .J_ajax_submit_btn').click();
            return false;
        } else{
            $( '<span id="J_fail_info" class="text-warning"><i class="glyphicon glyphicon-remove text-warning"></i> ' + msg.join('、') + '</span>' ).appendTo(button.parent()).fadeIn( 'fast' );
            return false;
        }
    }
    
</script>
<style>
    .avatar20{
        width: 20px;
        height: 20px;
        border-radius: 50%;
        object-fit: cover;
    }
    .addTalk{
        padding:4px;
        display:inline-flex;
        background: #F2F3F5;
        border-radius: 2px;
        margin-left:16px;
        margin-bottom:6px
    }
    .pt0{
        padding-top:0 !important
    }
</style>