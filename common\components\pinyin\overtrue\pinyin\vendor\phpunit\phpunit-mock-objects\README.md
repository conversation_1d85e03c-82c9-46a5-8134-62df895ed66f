[![Build Status](https://img.shields.io/travis/se<PERSON><PERSON><PERSON><PERSON>/phpunit-mock-objects/2.3.svg?style=flat-square)](https://travis-ci.org/sebastian<PERSON>mann/phpunit-mock-objects)
[![Latest Stable Version](https://img.shields.io/packagist/v/phpunit/phpunit-mock-objects.svg?style=flat-square)](https://packagist.org/packages/phpunit/phpunit-mock-objects)

# PHPUnit_MockObject

**PHPUnit_MockObject** is the default mock object library for PHPUnit.

## Requirements

* PHP 5.3.3 is required but using the latest version of PHP is highly recommended

## Installation

To add PHPUnit_MockObject as a local, per-project dependency to your project, simply add a dependency on `phpunit/phpunit-mock-objects` to your project's `composer.json` file. Here is a minimal example of a `composer.json` file that just defines a dependency on PHPUnit_MockObject 2.3:

    {
        "require": {
            "phpunit/phpunit-mock-objects": "2.3.*"
        }
    }

