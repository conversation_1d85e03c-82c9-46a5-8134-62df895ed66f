<?php

$disabled = array('class'=>"form-control", 'empty' => Yii::t('global','Please Select'));
$disabledHour = array('class' => 'form-control', 'style'=>'width:50%', 'empty'=>Yii::t('teaching', 'Hour'));
$disabledMinute = array('class' => 'form-control', 'style'=>'width:50%', 'empty'=>Yii::t('teaching', 'Minute'));
// if($this->branchObj->type == 50){
//     $disabled = array('class'=>"form-control", 'disabled' => 'disabled');
//     $disabledHour = array('class' => 'form-control', 'style'=>'width:50%', 'empty'=>Yii::t('teaching', 'Hour'), 'disabled' => 'disabled');
//     $disabledMinute = array('class' => 'form-control', 'style'=>'width:50%', 'empty'=>Yii::t('teaching', 'Minute'), 'disabled' => 'disabled');
// }

?>
<div class="col-lg-3 col-md-3 col-sm-4 col-xs-6">
    <div class="panel panel-default">
        <div class="panel-heading">
            <?php echo Yii::t('teaching','1. Basic Settings');?>
        </div>
        <div class="panel-body">
            <div class="form-group">
                <?php echo CHtml::label($taskData['plan']->getAttributeLabel('default_duration'),
                    CHtml::getIdByName('ParentMeetingPlan[default_duration]'),array('class'=>"control-label")) ?>
                <div class="input-group">
                    <?php for($i=5;$i<121;$i+=5){$durations[$i]=$i.' '.Yii::t('global', 'min');};
                        echo CHtml::dropDownList('ParentMeetingPlan[default_duration]',
                            $taskData['plan']->default_duration, $durations, $disabled) ?>
                        <span class="input-group-btn">
                        <button class="btn btn-primary" type="button" onclick="saveDuration()"><?php echo Yii::t('global','Save');?></button>
                           <!-- <?php if($this->branchObj->type == 50){ ?>
                                <button class="btn btn-primary disabled"><?php echo Yii::t('global','Save');?></button>
                            <?php }else{ ?>
                                <button class="btn btn-primary" type="button" onclick="saveDuration()"><?php echo Yii::t('global','Save');?></button>
                            <?php } ?> -->
                        </span>
                </div><!-- /input-group -->
            </div>
            <div class="form-group">
                <?php echo CHtml::label($taskData['plan']->getAttributeLabel('timeslot_starts'), false); ?>
                <ul class="list-group" id="timeslot-starts"></ul>

                <div class="input-group">
                    <?php for($i=8;$i<21;$i++){$t=sprintf('%02d', $i);$hours[$t]=$t;};
                        echo CHtml::dropDownList('timeslot_starts[hour]', '', $hours,
                            array('class'=>"form-control", 'style'=>'width:50%', 'empty'=>Yii::t('teaching', 'Hour'))) ?>
                    <?php for($i=0;$i<60;$i+=5){$t=sprintf('%02d', $i);$minutes[$t]=$t;};
                        echo CHtml::dropDownList('timeslot_starts[minute]', '', $minutes,
                            array('class'=>"form-control", 'style'=>'width:50%', 'empty'=>Yii::t('teaching', 'Minute'))) ?>
                    <div class="input-group-btn">
                        <button class="btn btn-primary" type="button" onclick="saveTimeslot()"><?php echo Yii::t('global','Add');?></button>
                    </div>
                </div><!-- /input-group -->
            </div>
        </div>
    </div>
    <div class="panel panel-default">
        <div class="panel-heading">
            <?php echo Yii::t('teaching','2. Timeslot configuration');?>
        </div>
        <div class="panel-body">
            <?php echo Yii::t('teaching','Please set slots in right side calendar.');?>
        </div>
    </div>
    <div class="panel panel-default">
        <div class="panel-heading">
            <?php echo Yii::t('teaching','3. Open to parents');?>
            <?php if($taskData['plan_start']) echo sprintf(" ( %s - %s )", $taskData['plan_start'], $taskData['plan_end']); ?>
        </div>
        <div class="panel-body" id="status-wrapper">

        </div>
    </div>

</div>


<div class="col-lg-9 col-md-9 col-sm-8 col-xs-6">
    <div class="panel panel-default">
        <div class="panel-heading">
            <?php echo Yii::t('user','Time slot configuration & schedule result');?>
            <a href="<?php echo $this->createUrl('printPtc', array('id'=>$taskData['plan']->id));?>"
               class="btn btn-primary" target="_blank"><span class="glyphicon glyphicon-print"></span> <?php echo Yii::t('global', 'Print')?></a>
        </div>
        <div class="panel-body">

            <div class="row">
                <div class="col-md-9  col-sm-12">
                    <div id='progress'>

                    </div>

                    <?php
                    $this->widget('common.extensions.EFullCalendar.EFullCalendar', array(
                        'id' => 'ptcFull-calendar',
//                        'themeCssFile'=>'cupertino/jquery-ui.min.css',

                        // raw html tags
                        'htmlOptions'=>array(
                            // you can scale it down as well, try 80%
                            'style'=>'width:100%'
                        ),
                        // FullCalendar's options.
                        'options'=>array(
                            'header'=>array('right'=>'prev,next'),
                            'firstDay' => 0,
                            'timezone' => 'local',
                            'defaultDate' => $taskData['selectMonth'],
//                            'editable' => true,

                            'selectable' => true,
//                          'selectHelper' => true,
                            'select' => "js:function(start,end){dayClick(start,end)}",
                            'eventClick' => "js:function(event, element){timeSlotClick(event, element)}",

                            //示例数据
                            'events' => $taskData['events'],
                            'timeFormat' => 'HH:mm',
                            'displayEventEnd' => true,
                        )
                    ));
                    ?>

                </div>
                <div class="col-md-3">
                    <button class="btn btn-primary btn-sm pull-left" onclick='addClassData()'><?php echo Yii::t("teaching", 'Add Students');?></button>
                    <button class="btn btn-default btn-sm pull-right" onclick='clearChildData()'><?php echo Yii::t("teaching", '清空');?></button>
                    <div id="planClass">
                        <?php $plan =  $taskData['plan']; ?>
                        <?php
                        if($plan->status){ ?>
                            <!-- <button class="btn btn-primary btn-sm pull-right" onclick='allSend()'><?php echo Yii::t("teaching", 'Send Reminder');?></button> -->
                        <?php } ?>
                    </div>
                    <div class="clearfix"></div>
                    <ul class="media-list mt15" id="ptc-child-list"></ul>
                </div>
            </div>


        </div>
    </div>

</div>

<div id="timeslotwithday" class="modal fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true"  onclick="closeds()">&times;</span><span class="sr-only">Close</span></button>
                <h4 class="modal-title"><?php echo Yii::t('teaching','选择预约有效时间');?> <small></small></h4>
            </div>
            <?php echo CHtml::form($this->createUrl('saveTimeslotwithDay', array('classid'=>$this->selectedClassId)), 'post', array('class'=>'J_ajaxForm'));?>
            <?php echo CHtml::hiddenField('planid', $taskData['plan']->id)?>
            <div class="modal-body">
                <div class="J_check_wrap p20" id="timeslot-options">
                    <div class="checkbox">
                        <label><input type="checkbox" class="J_check_all" data-checklist="J_check_c1" data-direction="y"> <?php echo Yii::t("global", 'Select All');?></label>
                    </div>
                </div>
            </div>
            <div class="modal-footer pop_bottom">
                <button type="button" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Save')?></button>
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Close')?></button>
            </div>
            <?php echo CHtml::endForm();?>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
<!--启明星ds-->
<div id="Dstimeslotwithday" class="modal fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true"  onclick="closeds()">&times;</span><span class="sr-only">Close</span></button>
                <h4 class="modal-title"><?php echo Yii::t('teaching','选择预约有效时间');?> <small></small></h4>
            </div>
            <div class="modal-body">
                <div class="J_check_wrap" id="Dstimeslot-options">
                    <div class="alert alert-info" role="alert"><?php echo Yii::t('global', 'Choose the start times for all the timeslots on that date. Each timeslot will open up one default spots.')?></div>
                </div>
            </div>
            <div class="modal-footer pop_bottom">
                <button type="button" class="btn btn-default" data-dismiss="modal" onclick="closeds()"><?php echo Yii::t('global', 'Close')?></button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<div id="timeslotwithitem" class="modal fade">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                <h4 class="modal-title"></h4>
            </div>
            <?php echo CHtml::form($this->createUrl('saveTimeslotwithItem', array('classid'=>$this->selectedClassId)),
                'post', array('class'=>'J_ajaxForm form-horizontal'));?>
            <?php echo CHtml::hiddenField('unikey')?>
            <div class="modal-body">
                <div class="row" id="un-child-list"></div>
                <hr />
                <div class="form-group">
                    <label for="inputEmail3" class="col-sm-2 control-label"><?php echo Yii::t('teaching', 'Starting Time')?></label>
                    <div class="col-sm-10">
                        <p class="form-control-static" id="starttime"></p>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label"><?php echo Yii::t('teaching', 'Ending Time')?></label>
                    <div class="col-sm-10">
                        <div class="input-group">
                            <?php
                            $hours['21']='21';echo CHtml::dropDownList('endtime[hour]', '', $hours, $disabledHour) ?>
                            <?php echo CHtml::dropDownList('endtime[minute]', '',
                                $minutes, $disabledMinute) ?>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label for="inputEmail3" class="col-sm-2 control-label"><?php echo Yii::t('labels', 'Memo')?></label>
                    <div class="col-sm-10">
                        <?php echo CHtml::textArea('memo', '', array('class'=>'form-control', 'rows'=>3));?>
                    </div>
                </div>
            </div>
            <div class="modal-footer pop_bottom">
                <a class="btn btn-danger J_ajax_del" id="delchild" href="#"><?php echo Yii::t('teaching', 'Remove Appointment')?></a>
                <button type="button" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Save')?></button>
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Close')?></button>
            </div>
            <?php echo CHtml::endForm();?>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
<div class="sendcontent"> </div>
<div class="addChildData"></div>
<style>
    .img-item{background: #f2f2f2;border: 1px solid #d5d5d5}
    .child-face{max-width: 40px;}
    #ptc-child-list .media{padding-left: 22px; position: relative;padding-right:24px}
    #ptc-child-list h5.flag{position: absolute;top: 2px; left: 0px; font-size:16px;color: #f2f2f2}
    #ptc-child-list .online h5.flag{color: #008000}
    th.fc-day-header{vertical-align: bottom;padding: 8px;}
    .fc-day-grid-event .fc-time{font-weight: normal !important;}
    /*.fc-time,.fc-title{color: #fff}*/
    .fc-event {font-size: 1em;}
    .pot .media{padding-left: 22px; position: relative}
    .pot h4.flag{position: absolute;top: 2px; left: 0px; font-size:16px;color: #f2f2f2}
    .pot .online h4.flag{color: #008000}
    .table tr td{
        vertical-align:middle !important;
    }
    .checkbox-custom {
        position: relative;
        margin-top: 0;
        display: inline-block;
    }
    /*
    将初始的checkbox的样式改变
    */
    .checkbox-custom input[type="checkbox"] {
        opacity: 0;/*将初始的checkbox隐藏起来*/
        position: absolute;
        cursor: pointer;
        z-index: 2;
        margin: -6px 0 0 0;
        top: 50%;
        left: 3px;
    }
    /*
    设计新的checkbox，位置
    */
    .checkbox-custom label:before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        margin-top: -9px;
        width:16px;
        height:16px;
        display: inline-block;
        border-radius: 2px;
        border: 1px solid #bbb;
        background: #fff;
    }
    /*
    点击初始的checkbox，将新的checkbox关联起来
    */
    .checkbox-custom input[type="checkbox"]:checked +label:after {
        position: absolute;
        display: inline-block;
        font-family: 'Glyphicons Halflings';
        content: "\e013";
        top:34%;
        left:0px;
        margin-top: -5px;
        font-size: 11px;
        line-height: 1;
        /*width: 14px;
        height: 13px;*/
        color: #333;
        width: 16px;
        height:16px;
        padding-top: 1px;
        padding-left: 2px;
        border-radius: 2px;
    }
    .mb0{
        margin-bottom: 0 !important
    }
    .bodbotnone{
        border-top: none;
        padding: 0;
        padding-bottom: 15px
    }
    .addchild tr td,.childname tr td{
        word-wrap:break-word;word-break:break-all
    }
    .mt5{

        margin-bottom: 0
    }
    .imgbig{
        width:2rem;
        height:2rem;
    }
    .sendlist{
        position: relative;
    }
    .textsend{
        position: absolute;
        right:10px;
        top: 50%;
        margin-top: -8px;
    }
    .pr0{
        padding-right: 0 !important
    }
    .pl20{
        padding:10px 0;
    }
    .dashed{
        border: 1px dashed #ddd;
        width: 50px;
        height:50px;
        line-height:50px;
        text-align: center;
        top: 0;
        border-radius: 5px;
    }
    .childrenName{
        height:22px;
        overflow: hidden;
        line-height: 22px;
        text-align: center;
    }
    .childrenNum{
        padding-top: 10px;
        width: 50px;
        position: relative;
        padding-left: 0 !important;
        margin-right:10px;
        height: 90px;
    }
    .closeimg{
        position: absolute;
        right: -5px;
        top: 5px;
        display: inline-block;
        width: 15px;
        height: 15px;
        border: 1px solid #989898;
        line-height: 13px;
        text-align: center;
        border-radius: 50%;
        color: #989898;
        background: #fff;
        font-size: 16px;
    }
    .childTime{
        padding-left:20px
    }
    .delright{
        position: absolute;
        top: 2px;
        right: 0px;
        /* display: none; */
    }
    .avatar42 {
        width: 42px;
        height: 42px;
        border-radius: 50%;
        object-fit: cover;
    }
    .lineHeight {
        line-height:42px;
    }
    .childList {
        max-height: 250px;
        min-height: 40px;
        padding: 14px;
        overflow-y: auto;
    }
    .border {
        border: 1px solid #E8EAED;
        border-radius: 4px;
    }
    .allCheck {
        position: absolute;
        right: 10px;
        top: 4px;
    }
    .borderLeft{
        border-left: 1px solid #EBEDF0;
    }
    .scroll-box::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius   : 10px;
        background-color: #ccc;
        background-image: none
    }
    .scroll-box::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0);
        background   : #fff;
        border-radius: 10px;
        border:none
    }
    .bluebg{
        color: #428bca;
    }
</style>

<script type="text/template" id="child-li-item-template">
    <li class="media class-child<%= online?' online':''%>" childid=<%- id %> <%if(online){%>onclick="childPopover(this)"<%}%> data-toggle="popover" data-placement="left" data-trigger="focus" data-content="" data-html="true">
        <img class="media-object child-face img-thumbnail pull-left" src="<?php echo Yii::app()->params['OAUploadBaseUrl']?>/childmgt/<%= photo %>" title="<%- name %>">
        <div class="media-body">
            <h5 class="media-heading pt12"><%- name %>

            </h5>
        </div>
        <h5 class="flag"><span class="glyphicon glyphicon-ok-sign"></span></h5>
        <h5 class="delright">
            <%if(!online){%>
                <button type="button" class="btn btn-danger btn-xs" onclick="delChildNoMark(<%- id %>,this)" >
                <a href='javascript:;' class=""></a>
                <span class="glyphicon glyphicon-trash"></span>
                </button>
            <%}%>
        </h5>
    </li>
</script>

<script type="text/template" id="child-radio-item-template">
    <div class="col-sm-2 text-center">
        <label>
            <div>
                <img style="width: 80px;" class="img-thumbnail" src="<?php echo Yii::app()
                ->params['OAUploadBaseUrl']?>/childmgt/<%= photo %>" title="<%- name %>">
            </div>
            <div style="height: 22px;overflow: hidden;font-weight: normal;">
                <input type="radio" name="childid" value="<%= id%>" onchange="changeChild(this)"> <%- name%>
            </div>
        </label>
    </div>
</script>

<script type="text/template" id="ptc-plan-setstatus-template">
    <% if(parseInt(status) == 1){ %>
        <div class="alert alert-success text-center">
            <div class="p20"> <span class="glyphicon glyphicon-ok-sign"></span> <?php echo Yii::t('teaching', 'Opening for parents to make appointment')?></div>
            <div id="qrcodeView"></div>
            <button type="button" class="btn btn-danger btn-lg" onclick="setPlanStatus('offline', <%- id %>, this)"><?php echo Yii::t('teaching', 'Close Appointment')?></button>
        </div>
    <% }else{ %>
        <div class="alert alert-danger text-center">
            <div class="p20"> <span class="glyphicon glyphicon-exclamation-sign"></span> <?php echo Yii::t('teaching', 'Closed for parents to make appointment')?></div>
            <button type="button" class="btn btn-primary btn-lg" onclick="setPlanStatus('online', <%- id %>, this)"><?php echo Yii::t('teaching', 'Open Appointment')?></button>
        </div>
    <% } %>
</script>

<script type="text/template" id="send-plan-setstatus-template">
    <div id="addChildList" class="modal fade" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog  modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" ><span onclick="closesend()">&times;</span><span class="sr-only">Close</span></button>
                    <h4 class="modal-title"><?php echo Yii::t('teaching', 'Send wechat notification to parents')?></h4>
                </div>
                <div class="modal-body">
                    <div class="col-md-6">
                        <div class="panel panel-info">
                            <div class="panel-heading"><?php echo Yii::t('teaching', 'Available to send')?></div>
                            <div class="J_check_wrap">
                                <table class="table  table-striped mb0">
                                    <thead>
                                        <tr style="height: 33px">
                                            <th width='40' class="textcen">
                                                <% if(sendlist.length!=''){%>
                                                <div class="checkbox-custom checkbox-default">
                                                    <input type="checkbox" class="J_check_all " data-checklist="J_check_c1" data-direction="y" checked>
                                                    <label></label>
                                                </div>
                                                <%}%>
                                            </th>
                                            <th width="150"><?php echo Yii::t('global', 'Name')?></th>
                                            <th><img style="width: 20px" src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/weixin.png' ?>" alt=""></th>
                                        </tr>
                                    </thead>
                                </table>
                                <div class="pre-scrollable" style="height:340px;margin-top:-1px">
                                    <table class="table mb0">
                                        <tbody class="childname">
                                            <% for(var i=0;i< sendlist.length;i++){%>
                                            <tr id='tr<%=sendlist[i].id%>'>
                                                <td width='40' id='check<%=sendlist[i].id%>' class="textcen">
                                                    <div class="checkbox-custom checkbox-default">
                                                        <input class="J_check" data-yid="J_check_c1" type="checkbox" name='send' value='<%=sendlist[i].id%>' checked>
                                                        <label></label>
                                                    </div>
                                                </td>
                                                <td width="40" class="pr0">
                                                      <img class="media-object child-face img-thumbnail pull-left" src="<?php echo Yii::app()->params['OAUploadBaseUrl']?>/childmgt/<%= sendlist[i].photo %>" title="<%- sendlist[i].name %>">
                                                </td>
                                                <td width="100" >
                                                    <span> <%= sendlist[i].name %></span>
                                                </td>
                                                <td id='<%=sendlist[i].id%>' class='sendlist'>
                                                    <p class="mt5">
                                                        <% for(key in sendlist[i].parent){%>
                                                        <img class="img-circle imgbig" src="<%= sendlist[i].parent[key].headimgurl %>" alt="<%= sendlist[i].parent[key].headimgurl %>" title="<%= sendlist[i].parent[key].name %>">
                                                        <%}%>
                                                    </p>
                                                    <p class="mt5 textsend" id="text<%=sendlist[i].id%>"></p>
                                                </td>

                                            </tr>
                                            <%}%>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer bodbotnone">
                            <button type="submit" class="btn btn-primary sendbtn" onclick="send()"><?php echo Yii::t('global', 'Send')?></button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="panel panel-default">
                            <div class="panel-heading"><?php echo Yii::t('teaching', 'Unavailable to send')?></div>
                            <table class="table table-striped mb0">
                                <thead>
                                    <tr>
                                        <th  width="150"><?php echo Yii::t('global', 'Name')?></th>
                                        <th><img style="width: 20px" src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/weixin.png' ?>" alt=""></th>
                                        <th width="78"><?php echo Yii::t('admissions', 'Status')?></th>
                                    </tr>
                                </thead>
                            </table>
                            <div class="pre-scrollable" style="height:340px;margin-top:-1px">
                                <table class="table mb0">
                                    <tbody class="addchild">
                                        <% for(var i=0;i< limit.length;i++){%>
                                        <tr>
                                            <td width="40" class="pr0">
                                                <img class="media-object child-face img-thumbnail pull-left" src="<?php echo Yii::app()->params['OAUploadBaseUrl']?>/childmgt/<%= limit[i].photo %>" title="<%- limit[i].name %>">
                                            </td>
                                            <td width="100">
                                               <span><%= limit[i].name %></span></td>
                                            <td>
                                                <% if(limit[i].parent.length!=''){%>
                                                <p class="mt5">
                                                <% for(key in limit[i].parent){%>
                                                <img class="img-circle imgbig" src="<%= limit[i].parent[key].headimgurl %>" alt="<%= limit[i].parent[key].headimgurl %>" title="<%= limit[i].parent[key].name %>">
                                                <%}%>
                                                </p>
                                                <%}%>
                                                <div class="clearfix"></div>
                                            </td>
                                            <td  width="60"><?php echo Yii::t('teaching', 'Sent')?></td>
                                        </tr>
                                        <%}%>
                                        <% for(var i=0;i< unbound.length;i++){%>
                                        <tr>
                                         <td width="40" class="pr0">
                                             <img class="media-object child-face img-thumbnail pull-left" src="<?php echo Yii::app()->params['OAUploadBaseUrl']?>/childmgt/<%= unbound[i].photo %>" title="<%- unbound[i].name %>">
                                         </td>
                                            <td width="100">
                                                <span><%= unbound[i].name %></span>
                                            </td>
                                            <td><?php echo Yii::t('teaching', 'No wechat account linked')?></td>
                                            <td  width="50"></td>
                                        </tr>
                                        <%}%>
                                        <% for(var i=0;i< booked.length;i++){%>
                                        <tr>
                                            <td width="40" class="pr0">
                                                <img class="media-object child-face img-thumbnail pull-left" src="<?php echo Yii::app()->params['OAUploadBaseUrl']?>/childmgt/<%= booked[i].photo %>" title="<%- booked[i].name %>">
                                            </td>
                                            <td width="100">
                                                 <span><%= booked[i].name %></span>
                                            </td>
                                            <td>
                                                <p class="mt5">
                                                 <% for(key in booked[i].parent){%>
                                                <img class="img-circle imgbig" src="<%= booked[i].parent[key].headimgurl %>" alt="<%= booked[i].parent[key].headimgurl %>" title="<%= booked[i].parent[key].name %>">
                                                <%}%>
                                                </p>
                                            </td>
                                            <td  width="60"><?php echo Yii::t('teaching', 'Already subscribed')?></td>
                                        </tr>
                                        <%}%>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="modal-footer bodbotnone">
                             <span><?php echo Yii::t('teaching', 'Notification can be sent only once within 24 hours.')?></span>
                        </div>
                    </div>

                </div>
                <div class="clearfix"></div>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
    </div>
</script>

<div class="modal fade" id="addClassModal" tabindex="-1" role="dialog" data-backdrop="static" data-keyboard="false" >
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t("referral", "Student"); ?></h4>
            </div>
            <div class="modal-body p24 relative">
                <span class='borderLeftpos'></span>
                <div style='max-height:600px;' class='row'>
                    <div class='col-md-6 col-sm-6'>
                        <div>
                            <el-input
                            placeholder="<?php echo Yii::t("global", "Search"); ?>"
                            v-model='searchText' 
                            size='small'
                            clearable>
                            </el-input>
                        </div>
                        <div  class="tab-pane active mt15 scroll-box" id="class" v-if='searchText==""'  style='max-height:500px;overflow-y:auto'>
                            <div v-for='(list,index) in classList' class='relative mb16'>
                                <p  @click='getChild(list)'>
                                    <span  class='font14 color606 cur-p'>{{list.title}} </span>
                                    <span class='el-icon-arrow-down ml5' v-if='classId!=list.classid'></span>
                                    <span class='el-icon-arrow-up ml5' v-else></span>
                                </p>
                                <p  class='allCheck' v-if='classId==list.classid'><button class="btn btn-default pull-right btn-xs" type="button" @click='selectAll(list,index)'  v-if='list.childData && list.childData.length!=0'><?php echo Yii::t("global", "Select All");?></button></p>
                                <div  class='border scroll-box mr10 childList' v-if='classId==list.classid'>
                                    <div v-if='!childLoading'>
                                        <div class='' v-if='list.childData && list.childData.length!=0'>
                                            <div class="media mb10 listMedia" v-for='(item,idx) in list.childData'>
                                                <div class="media-left pull-left media-middle">
                                                    <a href="javascript:void(0)">
                                                        <img :src="item.avatar" data-holder-rendered="true" class="media-object img-circle avatar42">
                                                    </a>
                                                </div>
                                                <div v-if='item.stuLoading'>
                                                    <div v-if='item.disabled' class="media-right pull-right text-muted lineHeight">
                                                        <span class='cur-p mt10 color9'><?php echo Yii::t("newDS", "Subscribed");?></span>
                                                    </div>
                                                    <div v-else class="media-right pull-right text-muted" @click='assignChildren(item,index,idx)'>
                                                        <span class='cur-p font16 bluebg mt15 el-icon-circle-plus-outline'></span>
                                                    </div>
                                                </div>
                                                <div class='childLoading' v-else>
                                                    <span></span>
                                                </div>
                                                <div class="media-body media-middle">
                                                    <h4 class="media-heading font14 lineHeight">{{item.name}}</h4>
                                                </div>
                                            </div>
                                        </div>
                                        <div v-else>
                                            <div class='font12 text-muted text-center'><?php echo Yii::t("newDS", "no student in this class");?></div>
                                        </div>
                                    </div>
                                    <div class='loading' v-else>
                                        <span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-else>
                            <div v-if='searchChildList.length!=0'  class='scroll-box'   style='max-height:500px;overflow-y:auto'>                               
                                <div class="media mt10 listMedia" v-for='(item,idx) in searchChildList'>
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)">
                                            <img :src="item.avatar" data-holder-rendered="true" class="media-object img-circle avatar42">
                                        </a>
                                    </div>
                                    <div v-if='item.disabled' class="media-right pull-right text-muted lineHeight">
                                        <span class='cur-p mt10'><?php echo Yii::t("newDS", "Subscribed");?></span>
                                    </div>
                                    <div v-else class="media-right pull-right text-muted" @click='assignChildren(item,idx,"search")'>
                                        <span class='cur-p font16 bluebg mt15 el-icon-circle-plus-outline'></span>
                                    </div>
                                    <div class="media-body media-middle">
                                        <h4 class="media-heading font14 color3 mt5">{{item.name}}</h4>
                                        <div class="text-muted color6">{{item.className}}</div>
                                    </div>
                                </div>
                            </div>
                            <div v-else-if='searchText!=""'>
                                    <div class='font14 color6 text-center mt20'><?php echo Yii::t("ptc", "No Data"); ?></div>    
                            </div>
                        </div>
                    </div>
                    <div class='col-md-6 col-sm-6 borderLeft'>
                        <p class='mt10 font14 color606'>
                        <?php echo Yii::t("newDS", " ");?>{{childSelected.length}}<?php echo Yii::t("newDS", " student(s) selected");?>
                            <button class="btn btn-link pull-right btn-xs font14" v-if='childSelected.length!=0' type="button" @click='batchDel("modal")'><?php echo Yii::t("directMessage", "Clear All");?></button>
                        </p>
                        <div class='scroll-box p10 overflow-y' style='height:500px'>
                            <div class="media m0 listMedia" v-for='(list,index) in childSelected'>
                                <div class="media-left pull-left media-middle">
                                    <a href="javascript:void(0)">
                                        <img :src="list.avatar" data-holder-rendered="true" class="media-object img-circle avatar42">
                                    </a>
                                </div>
                                <div class="media-right pull-right text-muted" @click='Unassign(list,index)'>
                                    <span class='closeChild cur-p mt15 font16 el-icon-circle-close'></span>
                                </div>
                                <div class="media-body media-middle">
                                    <h4 class="media-heading font14 color3 mt5">{{list.name}} 
                                        
                                    </h4>
                                    <div class="text-muted color6">{{list.class_name}}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class='clearfix'></div>
                </div>
            </div> 
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                <button type="button" class="btn btn-primary" onclick='addChildKeep()' :disabled='btnDisanled'><?php echo Yii::t("global", "OK"); ?></button>
            </div>
        </div>
    </div>
</div>
<script type="text/template" id="progress-template">
    <div class="progress">
        <div class="progress-bar" role="progressbar" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100" style="min-width:6em;width:<%= data.percentage %>;">
          <%= data.percentage %>   (<%= data.complete %>/<%= data.total %>)
        </div>
    </div>
</script>
<div class="modal fade" tabindex="-1" role="dialog" id='delChildModal'>
  <div class="modal-dialog modal-sm" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h4 class="modal-title">删除</h4>
      </div>
      <div class="modal-body">
        <p class="delModal"></p>
        <span id='delChildid' class="hidden" ;></span>
      </div>
      <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
          <button type="button" class="btn btn-primary" onclick='delConfirm()'>确认</button>
      </div>
    </div>
  </div>
</div>
<div class="modal fade" tabindex="-1" role="dialog" id='clearChildModal'>
  <div class="modal-dialog modal-sm" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h4 class="modal-title">清空</h4>
      </div>
      <div class="modal-body">
        <div>确认全部清空未预约的学生吗？</div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" onclick='clearConfirm()'>确认</button>
      </div>
    </div>
  </div>
</div>
<script>
    var canSubmit = true;
    var dayClick = null;
    var timeSlotClick = null;
    var setPlanStatus = null;
    var currentData = {};
    var schoolData = '<?php echo Yii::app()->request->getParam('branchId', '')?>';
    var classid = '<?php echo Yii::app()->request->getParam('classid', '')?>';

    var memos = <?php echo CJSON::encode($taskData['arr_memo'])?>;
    var select_childid = <?php echo CJSON::encode($taskData['select_childid']);?>;
    var childids = <?php echo CJSON::encode($taskData['arr_childid']);?>;
    var cdata = <?php echo CJSON::encode($taskData['timeslots']); ?>;
    var childDataRaw = <?php echo CJSON::encode($taskData['children']); ?>;
    var expired = <?php echo CJSON::encode($taskData['expired']); ?>;
    var childData = _.sortBy(childDataRaw, 'name');
    var reviewStatus = <?php echo CJSON::encode($taskData['completeConfig'])?>;
    var backChildData = _.sortBy(childDataRaw, 'name');
    var childItemTemplate = _.template( $('#child-li-item-template').html() );
    var ptcStatusTemplate = _.template( $('#ptc-plan-setstatus-template').html() );
    var sendStatusTemplate = _.template( $('#send-plan-setstatus-template').html() );
    var progressTemplate = _.template( $('#progress-template').html() );
    var TimeslotList = Backbone.Collection.extend({
        comparator: 'time'
    });
    var Timeslots = new TimeslotList;
    currentData.plan = <?php echo CJSON::encode($taskData['plan']);?>;
    // var types = '<?php echo $this->branchObj->type ?>';
    var types = 50;
    $('#ptc-child-list').html('');
    $(function () {
        var itemView = Backbone.View.extend({
            tagName: 'li',
            className: 'list-group-item',
            events: {
                'click .J_remove': 'delItem'
            },
            initialize: function() {
                this.listenTo(this.model, 'change', this.render);
                this.listenTo(this.model, 'destroy', this.remove);
            },
            delItem: function(){
                var model = this.model;
                $.post(
                    '<?php echo $this->createUrl('delTimeslot', array('id'=>$taskData['plan']->id, 'classid'=>$this->selectedClassId,'semester'=>$this->selectedSemester))?>',
                    {time: this.model.get('time'),plan_id:currentData.plan.id},
                    function(data){
                        if(data.state == 'success'){
                            resultTip({msg: data.message});
                            model.destroy();
                        }
                        else{
                            resultTip({msg: data.message, error: 1});
                        }
                    },
                    'json'
                );
            },
            render: function(){
                this.$el.html( _.template('<span class="glyphicon glyphicon-time"></span><span class="pull-right"><a href="javascript:void(0);"><span class="glyphicon glyphicon-remove J_remove"></span></a></span> <%= time%>', this.model.attributes) );
                return this;
            }
        });
        var optionView = Backbone.View.extend({
            tagName: 'div',
            className: 'checkbox',
            initialize: function() {
                this.listenTo(this.model, 'destroy', this.remove);
            },
            render: function(){
                if(types=='50'){
                    this.$el.html( _.template(' <div class="panel panel-default"><div class="panel-body"><label><input class="J_check" data-yid="J_check_c1" type="checkbox" value="<%= time%>" onclick="add(this)"> <%= time%><span class="classTime"></span></label><div class="childTime"></div></div></div>', this.model.attributes) );
                 }else{
                    this.$el.html( _.template('<label><input class="J_check" data-yid="J_check_c1" type="checkbox" name="timeslot[]" value="<%= time%>"> <%= time%> <span></span></label>', this.model.attributes) );
                 }
                return this;
            }
        });
        var AppView = Backbone.View.extend({
            //el: $("#timeslot-starts"),
            initialize: function () {
                this.listenTo(Timeslots, 'reset', this.addAll);
            },
            addAll: function(){
                Timeslots.each(this.addOne,this);
            },
            addOne: function(model) {
                var item = new itemView({model: model});
                $("#timeslot-starts").append(item.render().el);

                var option = new optionView({model: model});
                if(types=='50'){
                    $('#Dstimeslot-options').append(option.render().el);
                    return
                }
                $('#timeslot-options').append(option.render().el);


            }
        });
        new AppView;
        Timeslots.reset(cdata);

        $('#ptc-month-list li').click(function(){
            $(this).siblings().removeClass('active');
            $(this).addClass('active');
            var _date = $(this).data('month');
            $('#ptcFull-calendar').fullCalendar('gotoDate', _date);
        });

        renderChildList = function(){
            $('#ptc-child-list').html('');
            _.each(childData, function(_data){
                _data.online = childids.indexOf(Number(_data.id)) == -1 ? false : true;
                _data.schoolData = schoolData;
                _data.classid = classid;

                var _item = childItemTemplate(_data);
                var _child = _.first($(_item));
                $('#ptc-child-list').append( $(_child) );
            });
        }
        renderChildList();

        $('#status-wrapper').html( ptcStatusTemplate(currentData.plan) );

        setPlanStatus = function(status, id, obj){
            $(obj).attr('disabled', 'disabled');
            $.ajax({
                type: 'post',
                url: '<?php echo $this->createUrl('//mteaching/parentReservation/setPlanStatus');?>',
                dataType: 'json',
                data: {status:status,plan_id:currentData.plan.id}
            }).done(function(data){
                if(data.state == 'success'){
                    currentData.plan.status = data.data.status;
                    $('#status-wrapper').html( ptcStatusTemplate(currentData.plan) );
                    if(currentData.plan.status == 1){
                        // var as = "<div><button class='btn btn-primary btn-sm pull-right' onclick='allSend()'><?php echo Yii::t('teaching', 'Send Reminder');?></button></div>";
                        // $('#planClass').append(as);
                        showQrCode()
                    }else{
                        $('#planClass button').remove();
                    }
                    resultTip({msg: data.message});
                }else{
                    resultTip({msg: data.message, error: 1});
                }
                $(obj).removeAttr('disabled');
            });
        }
        Date.prototype.format = function() {
              var s = '';
              var mouth = (this.getMonth() + 1)>=10?(this.getMonth() + 1):('0'+(this.getMonth() + 1));
              var day = this.getDate()>=10?this.getDate():('0'+this.getDate());
              s += this.getFullYear() + '-'; // 获取年份。
              s += mouth + "-"; // 获取月份。
              s += day; // 获取日。
              return (s); // 返回日期。
        };
        var canClick = true;
        dayClick = function(start,end){
            if(types=='50'){
                if (canClick === false) {
                    return;
                }
                canClick = false;
                if($('#ParentMeetingPlan_default_duration').val()==''){
                    alert('<?php echo Yii::t('teaching', '请设置并保存默认时长')?>');
                    return;
                }
                if(Timeslots.length<1){
                    alert('<?php echo Yii::t('teaching', 'Please set time slots first.')?>');
                    return;
                }

                $('#Dstimeslotwithday form input[name="timeslot[]"]').attr('checked', false);
                var c=0;
                Dsdays=[]
                for(var i=start['_d'].getTime(); i<end['_d'].getTime(); i+=86400000){
                    Dsdays.push((i/1000))
                    c++;
                }
                if(c == 1){
                    var dateT = start['_d'].getFullYear()+'/'+(start['_d'].getMonth()+1)+'/'+start['_d'].getDate();
                    $('#Dstimeslotwithday .modal-title small').text(dateT);
                    $.ajax({
                        url : '<?php echo $this->createUrl('getDayInfoDS');?>',
                        data: {planid: '<?php echo $taskData['plan']->id?>', target_date: dateT},
                        type: 'post',
                        dataType: 'json'
                    }).done(function(data){
                        $('#Dstimeslot-options input[type="checkbox"]').attr('checked', false).attr('disabled', false)
                        for(var st in data){
                            var obj = $('#Dstimeslot-options input[value="'+st+'"]').attr('checked', true);
                            for(var i=0;i<data[st].length;i++){
                                var add='<div class="childrenNum pull-left" onclick="addone(this,\''+data[st][i].timeslot_s+'\')"><div class="dashed"><span class="glyphicon glyphicon-plus" aria-hidden="true" ></span></div><div class="childrenName"></div></div><div class="clearfix"></div>'
                                if(data[st][i].childid != 0){
                                    obj.attr('disabled', true);
                                    for(var j=0;j<childData.length;j++){
                                        if(childData[j].id==data[st][i].childid){
                                            var htmlname='<div class="childrenNum pull-left"><div><img style="width:50px;" class="img-thumbnail" src="<?php echo Yii::app()->params['OAUploadBaseUrl']?>/childmgt/'+childData[j].photo+'" title='+data[st][i].childName+'></div><div class="childrenName " >'+data[st][i].childName+'</div></div>'
                                            obj.parent().next().append(htmlname)
                                        }
                                    }
                                }
                                else{
                                    var html='<div class="childrenNum pull-left"><div><img style="width:50px;" class="img-thumbnail" src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/childwho.jpg' ?>"><span class="closeimg" aria-hidden="true" onclick="Dsdel(this,\''+data[st][i].timeslot_s+'\','+data[st][i].meet_index+')">&times;</span></div><div class="childrenName name'+data[st][i].meet_index+'" ></div></div>'
                                    obj.parent().next().append(html)
                                }
                            }
                            obj.parent().next().append(add)
                        }

                        $('.classTime').html('（ <?php echo Yii::t('global', 'Duration %d mins'); ?>'.replace(/%d/g,$('#ParentMeetingPlan_default_duration').val())+'）')
                        
                        $('#Dstimeslotwithday').modal({
                            backdrop: false
                        });
                        canClick = true;

                    });
                }else{
                    var datalist=$('#ptcFull-calendar').fullCalendar('clientEvents')
                    var theend = new Date(end['_d'].getTime() - 86400000);
                    var startTime = start['_d'].getTime();
                    var dateArrthis=[]
                    for (var k = startTime; k <= theend;) {
                        dateArrthis.push((new Date(parseInt(k))).format());
                        k = k + 24 * 60 * 60 * 1000;
                    }
                    thisData=[]
                    for(var i=0;i<datalist.length;i++){
                        for(var j=0;j<dateArrthis.length;j++){
                            var eventStart = datalist[i].start.format('YYYY-MM-DD');
                            var theDate = dateArrthis[j];
                            if(theDate==eventStart){
                                thisData.push(datalist[i])
                            }
                        }
                    }
                    if(thisData.length>0){
                        resultTip({error: 'warning', msg: '<?php echo Yii::t('global', '禁止连续选择'); ?>'});
                        canClick = true;
                        return
                    }
                    $('#Dstimeslotwithday .modal-title small').text(start['_d'].getFullYear()+'/'+(start['_d'].getMonth()+1)+'/'+start['_d'].getDate()+' - '+theend.getFullYear()+'/'+(theend.getMonth()+1)+'/'+theend.getDate());
                    $('#Dstimeslot-options input[type="checkbox"]').attr('checked', false).attr('disabled', false)
                    $('#Dstimeslotwithday').modal({
                        backdrop: false
                    });
                 }
                return
            }
            if(Timeslots.length<1){
                alert('<?php echo Yii::t('teaching', 'Please set time slots first.')?>');
                return;
            }
            $('#timeslotwithday form input[name="days[]"]').remove();
            $('#timeslotwithday form input[name="timeslot[]"]').attr('checked', false);
            var c=0;
            for(var i=start['_d'].getTime(); i<end['_d'].getTime(); i+=86400000){
                $('#timeslotwithday form').append('<input type="hidden" name="days[]" value="'+(i/1000)+'">');
                c++;
            }
            if(c == 1){
                var dateT = start['_d'].getFullYear()+'/'+(start['_d'].getMonth()+1)+'/'+start['_d'].getDate();
                $('#timeslotwithday .modal-title small').text(dateT);

                $.ajax({
                    url : '<?php echo $this->createUrl('getDayInfo');?>',
                    data: {planid: '<?php echo $taskData['plan']->id?>', target_date: dateT},
                    async: false,
                    dataType: 'json'
                }).done(function(data){
                    $('#timeslot-options input[type="checkbox"]').attr('checked', false).attr('disabled', false).nextAll('span').html('');
                    for(var st in data){
                        var obj = $('#timeslot-options input[value="'+data[st].timeslot_s+'"]').attr('checked', true);
                        if(data[st].childid != 0){
                            obj.attr('disabled', true);
                            obj.next('span').html(data[st].childName);
                        }
                        else{
                            obj.attr('disabled', false);
                            obj.next('span').html('');
                        }
                    }
                    $('#timeslotwithday').modal({
                        backdrop: false
                    });
                });
            }
            else{
                var theend = new Date(end['_d'].getTime() - 86400000);
                $('#timeslotwithday .modal-title small').text(start['_d'].getFullYear()+'/'+(start['_d'].getMonth()+1)+'/'+start['_d'].getDate()+' - '+theend.getFullYear()+'/'+(theend.getMonth()+1)+'/'+theend.getDate());
                $('#timeslot-options input[type="checkbox"]').attr('checked', false).attr('disabled', false).nextAll('span').html('');
                $('#timeslotwithday').modal({
                    backdrop: false
                });
            }
            head.Util.checkAll( $('#timeslotwithday') );
        }
        add = function(obj){
            var checked=$(obj).parent().find('input')
            var timeslotsTime=$(obj).val()
            if(checked.is(':checked')){
                $.ajax({
                    url : '<?php echo $this->createUrl('openTimeslot');?>',
                    data: {planid: '<?php echo $taskData['plan']->id?>', days: Dsdays,'timeslot':timeslotsTime},
                    type: 'post',
                    dataType: 'json',
                    success:function(res){
                        if(res.state=='success'){
                            var add='<div class="childrenNum pull-left" onclick="addone(this,\''+timeslotsTime+'\')"><div class="dashed"><span class="glyphicon glyphicon-plus" aria-hidden="true" ></span></div><div class="childrenName"></div></div><div class="clearfix"></div>'
                            var html=''
                            for(var i=0;i<res.data.length;i++){
                                html+='<div class="childrenNum  pull-left"><div><img style="width:50px;" class="img-thumbnail" src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/childwho.jpg' ?>"><span class="closeimg" aria-hidden="true" onclick="Dsdel(this,\''+timeslotsTime+'\','+res.data[i]+')">&times;</span></div><div class="childrenName"></div></div>'
                            }
                            $(obj).parent().next().append(html)
                            $(obj).parent().next().append(add)
                        }else{
                            resultTip({msg: res.message, error: 1});
                            $('#Dstimeslotwithday').modal('hide')
                        }
                    }
                })
            }else{
                 $.ajax({
                    url : '<?php echo $this->createUrl('cancelTimeslot');?>',
                    data: {planid: '<?php echo $taskData['plan']->id?>', days: Dsdays,'timeslot':timeslotsTime},
                    type: 'post',
                    dataType: 'json',
                    success:function(data){
                       if(data.state=='success'){
                            $(obj).parent().next().html('')
                       }
                    }
                })
            }
        }
        addone = function(obj,timeslotsTime){
            $.ajax({
                url : '<?php echo $this->createUrl('addNum');?>',
                data: {planid: '<?php echo $taskData['plan']->id?>', days: Dsdays,'timeslot':timeslotsTime},
                type: 'post',
                dataType: 'json',
                success:function(data){
                    if(data.state=='success'){
                        var html='<div  class="childrenNum pull-left"><div><img style="width:50px;" class="img-thumbnail" src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/childwho.jpg' ?>"><span class="closeimg" aria-hidden="true" onclick="Dsdel(this,\''+timeslotsTime+'\','+data.data+')">&times;</span></div><div class="childrenName"></div></div>'
                        $(obj).before(html)
                    }
                }
            })
        }
        Dsdel=function(obj,timeslotsTime,index){
            $.ajax({
                url : '<?php echo $this->createUrl('delNum');?>',
                data: {planid: '<?php echo $taskData['plan']->id?>', days: Dsdays,'timeslot':timeslotsTime,index:index},
                type: 'post',
                dataType: 'json',
                success:function(data){
                    if(data.state=='success'){
                        $(obj).parent().parent().remove()
                    }
                }
            })
        }
        closeds=function(){
            if(types=='50'){
                window.location.href='<?php echo $this->createUrl("edit")?>&plan_id=<?php echo $taskData['plan']->id?>&selectMonth='+timetrans(Dsdays)+''
            }
        }
        function timetrans(timestamp){
            if (Array.isArray(timestamp)) {
                timestamp = timestamp[0];
            }
            var date = new Date(timestamp*1000);//如果date为13位不需要乘1000
            var Y = date.getFullYear() + '-';
            var M = (date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1) : date.getMonth()+1);
            return Y+M
        }
        var canClick = true;
        timeSlotClick = function(event, element){
            console.log(canClick)
            if (canClick === false) {
                return;
            }
            // 禁止元素点击
            canClick = false;
            var html = '';
            var startObj = event['start']['_i'].split('T');
            var starttime = startObj[1].substring(0,5);
            var endtime = event['end']['_i'].split('T')[1];
            $('#timeslotwithitem .modal-title').text(startObj[0]+' '+starttime+' - '+endtime);
            $('#unikey').val(event.id);
            $('#starttime').text(starttime);

            $.getJSON(
                '<?php echo $this->createUrl('getTimeslotInfo');?>',
                {unikey: event.id},
                function(data){
                    if(data.childid != 0){
                        $('#delchild').attr('href', '<?php echo $this->createUrl('clearChild', array('classid'=>$this->selectedClassId));?>&unikey='+event.id).show();
                        $('#un-child-list').html( _.template($('#child-radio-item-template').html(), {id: data.childid, name: childDataRaw[data.childid].name, photo: childDataRaw[data.childid].photo}) );
                    }
                    else{
                        _.each(childData, function(_data){
                            if(childids.indexOf(Number(_data.id)) == -1){
                                var _item = _.template($('#child-radio-item-template').html(), _data);
                                html += _item;
                            }
                        });
                        $('#timeslotwithitem #un-child-list').html( html );

                        $('#delchild').hide();
                    }

                    $('#timeslotwithitem input[value='+data.childid+']').attr('checked', true);
                    $('#endtime_hour').val(data.endtimeh);
                    $('#endtime_minute').val(data.endtimem);
                    $('#memo').val(data.memo);

                    $('#timeslotwithitem').modal({
                        backdrop: false
                    });
                }
            ).done(function(response) {
                canClick = true;
                console.log('数据加载成功:', response);
            });
        }

        saveDuration = function()
        {
            if(canSubmit){
                canSubmit = false;
                $.post(
                    '<?php echo $this->createUrl('saveDuration', array('classid'=>$this->selectedClassId, 'semester'=>$this->selectedSemester))?>',
                    {duration: $('#ParentMeetingPlan_default_duration').val(),plan_id:currentData.plan.id},
                    function(data){
                        canSubmit = true;
                        if(data.state == 'success'){
                            resultTip({msg: data.message});
                        }
                        else{
                            resultTip({msg: data.message, error: 1});
                        }
                    },
                    'json'
                );
            }
        }

        saveTimeslot = function()
        {
            var th_obj = $('#timeslot_starts_hour');
            var tm_obj = $('#timeslot_starts_minute');
            if(th_obj.val() == ''){
                return false;
            }
            if(tm_obj.val() == ''){
                return false;
            }
            if(canSubmit){
                canSubmit = false;
                $.post(
                    '<?php echo $this->createUrl('saveTimeslot', array('id'=>$taskData['plan']->id, 'classid'=>$this->selectedClassId,'semester'=>$this->selectedSemester))?>',
                    {hour: th_obj.val(), minute: tm_obj.val(),plan_id:currentData.plan.id},
                    function(data){
                        canSubmit = true;
                        if(data.state == 'success'){
                            th_obj.val('');
                            tm_obj.val('');
                            Timeslots.reset(data.data);
                            //Timeslots.create(data.data);
                            resultTip({msg: data.message});
                        }
                        else{
                            resultTip({msg: data.message, error: 1});
                        }
                    },
                    'json'
                );
            }
        }

        // 选孩子回调函数
        cbChooseChild = function(data)
        {
            window.location.href='<?php echo $this->createUrl("edit")?>&plan_id=<?php echo $taskData['plan']->id?>&selectMonth='+data.childTimeslot.substring(0,7)+''
            var event = $("#ptcFull-calendar").fullCalendar('clientEvents', data.unikey)[0];
            event.title = data.childName;
            event.color = '#3a87ad';
            $("#ptcFull-calendar").fullCalendar('updateEvent', event);
            childids.push(Number(data.childid));
            select_childid[data.childid] = data.childTimeslot;
            memos[data.childid] = data.memo;
            renderChildList();
            $('#timeslotwithitem').modal('hide');
        }

        // 选时间点回调函数
        cbChooseTimeslot = function(data)
        {
            var delTimeslot = data.delTimeslot;
            var newTimeslot = data.newTimeslot;
            for(var i in delTimeslot){
                $("#ptcFull-calendar").fullCalendar('removeEvents', delTimeslot[i]);
            }
            for(var i in newTimeslot){
                var eventData = {
                    id: newTimeslot[i].id,
                    title: '',
                    start: newTimeslot[i].start,
                    end: newTimeslot[i].end,
                    color: '#257e4a'
                };
                $("#ptcFull-calendar").fullCalendar('renderEvent', eventData, true);
            }
            $('#timeslotwithday').modal('hide');
        }

        // 删除预约成功的孩子回调函数
        cbClearChild = function(data)
        {
            var event = $("#ptcFull-calendar").fullCalendar('clientEvents', data.unikey)[0];
            event.title = '';
            event.color = '#257e4a';
            $("#ptcFull-calendar").fullCalendar('updateEvent', event);
            delete childids[childids.indexOf(Number(data.childid))];
            renderChildList();
            progress();
            $('#timeslotwithitem').modal('hide');
        }

        changeChild = function(_this)
        {
            var childid = $(_this).val();
            $('#memo').val(memos[childid]);
        }

        childPopover = function(_this)
        {
            var childid = $(_this).attr('childid');
            $(_this).attr('title', select_childid[childid]);
            $(_this).attr('data-content', memos[childid]==''?'<?php echo Yii::t('teaching', 'N/A')?>':nl2br(_.escape(memos[childid])));
            $(_this).popover('toggle');
        }

        showQrCode = function() {
            $('#qrcodeView').qrcode({
            width:180,
            height:180,
            text:'<?= $qrCodeUrl; ?>',
            })
        }

        showQrCode()

    });

    function allSend(){
       var booked=[]//已预约
       var unbound=[]//未绑定微信
       var limit=[]//24小时
       var sendlist=[]//可发送

        for(var i=0;i<childData.length;i++){
            if(childData[i].online){
                booked.push(childData[i])
            }
            if(!childData[i].online){
                if(childData[i].bindingStatus=='0'){
                    unbound.push(childData[i])
                }else{
                    if(expired[childData[i].id]){
                        limit.push(childData[i])
                    }else{
                        sendlist.push(childData[i])
                    }
                }
            }
        }
        var datas={}
        datas.booked=booked
        datas.unbound=unbound
        datas.limit=limit
        datas.sendlist=sendlist
        $('.sendcontent').html( sendStatusTemplate(datas) );
        $('#addChildList').modal('show');
         head.Util.checkAll($('#addChildList'));
    }

    closeindex=0
    read=''
    function send(){
        read=''
        var chaildfiler=[]
        $.each($('input[name="send"]:checked'),function(){
            chaildfiler.push($(this).val());
        })
        if(chaildfiler.length==''){
             resultTip({msg:'<?php echo Yii::t('teaching', 'Please select')?>', error: 1});
        }else{
            sendAjaxS(chaildfiler);
        }
    }

    function sendAjaxS(id) {
        var index = 0;
        var result = [];
        var childindex='';
        var sendsucc=[]
        var successData=''
        sendAjax(index);
        function sendAjax(index) {

            closeindex=1
            $('.sendbtn').attr("disabled", true);
            $('#text'+id[index]).css('visibility','visible')
            $('#text'+id[index]).html('<?php echo Yii::t('teaching', 'Sending...')?>')
            if(childindex!=''){
                var html=''
                for(var i=0;i<childData.length;i++){
                  if(childindex==childData[i].id){
                    $('#tr'+childindex).fadeToggle(1000)
                    html+='<tr id=add'+childData[i].id+' style="display:none"> <td width="40" class="pr0"> <img class="media-object child-face img-thumbnail pull-left" src="<?php echo Yii::app()->params['OAUploadBaseUrl']?>/childmgt/'+childData[i].photo+'" title='+childData[i].name+'></td><td width:100"><span>'+childData[i].name+'</span></td><td><p class="mt5">'
                        for(key in successData.data[childindex]){
                            if(successData.data[childindex][key].name!=''){
                                html+=' <img class="img-circle imgbig" src='+successData.data[childindex][key].headimgurl+' alt='+successData.data[childindex][key].name+' title='+successData.data[childindex][key].name+'>'
                            }
                        }
                    html+='</p></td><td width="60"><?php echo Yii::t('teaching', 'Sent')?></td></tr>'
                    }
                }
                $('.addchild').prepend(html)
                $('#add'+childindex).fadeIn(1000)
            }
            if(index >= id.length) {
                doSomething(id,result);
                return;
            }
            if(read=='close'){
                colsesend(sendsucc)
                return;
            }
            $.ajax({
                url: '<?php echo $this->createUrl("SendMessage")?>',
                type: "POST",
                async: true,
                dataType: 'json',
                data: {
                    'planid': '<?php echo $taskData['plan']->id?>',
                    'childid': id[index],
                    'classid':classid,
                    'semester':<?php echo $this->selectedSemester ?>
                },
                success: function(data) {
                    if(data.state == 'success') {
                        result.push(data);
                        sendsucc.push(id[index])
                        $('#check'+id[index]).find('div').remove()
                        $('#text'+id[index]).html(' <span class="glyphicon glyphicon-ok" aria-hidden="true"></span> <?php echo Yii::t('teaching', 'Sent')?>')
                        childindex=id[index]
                        successData=data
                        setTimeout(function(){
                            index++;
                            sendAjax(index);
                        }, 1000);
                    } else {
                        $('#'+id[index]).html(data.message)
                        closeindex=0
                        $('.sendbtn').attr("disabled", true);
                        resultTip({msg: data.message, error: 1});
                    }
                },
                error: function() {
                    alert("请求错误")
                }
            });
         }
    }
    function doSomething(id,data) {
        closeindex=0
        $('.sendbtn').attr("disabled", false);
        for(var i=0;i<id.length;i++){
             expired[id[i]]=id[i]
        }
    }
    function colsesend(sendsucc){
        closeindex=0
        $('.sendbtn').attr("disabled", false);
        for(var i=0;i<sendsucc.length;i++){
             expired[sendsucc[i]]=sendsucc[i]
        }
    }
    function closesend(){
        if(closeindex==0){
            $('#addChildList').modal('hide');
        }else{
            var con;
            con=confirm("<?php echo Yii::t('teaching', 'Sending task is not completed, are you sure to stop it?')?>"); //在页面上弹出对话框
            if(con){
                $('#addChildList').modal('hide');
                read='close'
            }

        }
    }
    //添加学生
    var container=new Vue({
        el: "#addClassModal",
        data: {
            classList:[],
            classId:'',
            childSelected:[],
            searchText:'',
            btnDisanled:false
        },
        created: function() {
        },
        watch:{
            searchText(old,newValue){
                if(old!=''){
                    this.searchChild()
                }
            },
        },
        methods: {
            addChild(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("/mteaching/journals/classList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            data.data.forEach(item => {
                                item.childData=[]
                            })
                            that.classList=data.data
                            that.searchChildList=[]
                            that.childSelected=[]
                            that.searchText=''
                            that.classId=''
                            that.btnDisanled=false
                            $('#addClassModal').modal('show')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            getChild(list){
                let that=this
                if(that.classId==list.classid){
                    that.classId=''
                    return
                }
                that.classId=list.classid
                var childId=[]
                if(this.childSelected!=null){
                    for(let i=0;i<this.childSelected.length;i++){
                        childId.push(this.childSelected[i].id)
                    }
                }
                for(let i=0;i<childData.length;i++){
                    childId.push(parseInt(childData[i].id))
                }
                that.childLoading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("/mteaching/student/childList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        classId:list.classid
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            for(var i=0;i<data.data.length;i++){
                                data.data[i].classTitle=list.title
                                data.data[i].stuLoading=true
                                data.data[i].loading=true
                                if (childId.indexOf(data.data[i].id)!=-1) {
                                    data.data[i].disabled=true
                                }else{
                                    data.data[i].disabled=false
                                }
                            }
                            that.sortData(data.data)
                            for(var i=0;i<that.classList.length;i++){
                                if(that.classList[i].classid==list.classid){
                                    that.classList[i].childData=data.data
                                }
                            }
                            that.childLoading=false
                            that.$forceUpdate()
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            searchChild(){
                var childId=[]
                if(this.childSelected!=null){
                    for(var i=0;i<this.childSelected.length;i++){
                        childId.push(this.childSelected[i].id+'')
                    }
                }
                for(let i=0;i<childData.length;i++){
                    childId.push(parseInt(childData[i].id))
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("/mteaching/journals/searchStudent") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        child_name:this.searchText
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            for(var i=0;i<data.data.length;i++){
                                data.data[i].loading=true
                                if (childId.indexOf(data.data[i].id)!=-1) {
                                    data.data[i].disabled=true
                                }else{
                                    data.data[i].disabled=false
                                }
                            }
                            that.searchChildList=data.data
                            that.$forceUpdate()
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            sortData(list){
                <?php if (Yii::app()->language == 'zh_cn') { ?>
                    list.sort((a, b)=> a.name.localeCompare(b.name, 'zh'));
                <?php } else { ?>
                    list.sort((a, b) => a.name.charCodeAt(0) - b.name.charCodeAt(0));
                <?php } ?>
            },
            selectAll(list,index){
                for(var i=0;i<list.childData.length;i++){
                    if (!list.childData[i].disabled) {
                        
                        this.childSelected.push(list.childData[i])
                        Vue.set(this.classList[index].childData[i], 'disabled', true);
                    }
                }
                this.$forceUpdate()
            },
            assignChildren(list,index,idx){
                if(idx=='search'){
                    this.searchChildList[index].disabled=true
                }else{
                    Vue.set(this.classList[index].childData[idx], 'disabled', true);
                }
                this.$forceUpdate()
                list.class_name=this.classList[index].title
                this.childSelected.push(list)
            },
            Unassign(data,index,type){
                for(var i=0;i<this.classList.length;i++){
                    for(var j=0;j<this.classList[i].childData.length;j++){
                        if(data.id==this.classList[i].childData[j].id){
                            Vue.set(this.classList[i].childData[j], 'disabled', false);
                        }
                    }
                }
                for(var i=0;i<this.searchChildList.length;i++){
                    if(data.id==this.searchChildList[i].id){
                        Vue.set(this.searchChildList[i], 'disabled', false);
                    }
                }
                this.$forceUpdate()
                this.childSelected.splice(index,1)
                if(type){
                    this.getTeacher()
                }
            },
            batchDel(){
                this.childSelected=[]
                for(var i=0;i<this.classList.length;i++){
                    for(var j=0;j<this.classList[i].childData.length;j++){
                        this.classList[i].childData[j].disabled=false
                    }                        
                }                
                this.$forceUpdate() 
            },
        },
    })
    function addClassData(){
        container.addChild()
    }
    function clearChildData(){
        $('#clearChildModal').modal('show');
    }
    function clearConfirm(){
        $.ajax({
            url: '<?php echo $this->createUrl("delAllChild")?>',
            type: "POST",
            async: true,
            dataType: 'json',
            data: {
                'plan_id':'<?php echo $taskData['plan']->id?>',
            },
            success: function(data) {
                if(data.state=='success'){
                    
                    for(var i=0;i<childData.length;i++){
                        if(data.data.indexOf(childData[i].id)!=-1){
                            delete childData[i];
                        }
                    }
                    for(var i=0;i<childData.length;i++){
                        if(childData[i] == undefined) {
                          childData.splice(i,1);
                          i = i - 1;
                        }
                    }
                    resultTip({
                        msg: data.message
                    });
                    renderChildList();
                    progress();
                    $('#clearChildModal').modal('hide')
                }
            },
            error: function() {
                alert("请求错误")
            }
        });
    }
    function addChildKeep(){
        $('#addChildDis').attr('disabled','true')
        var chaildfiler=[]
        if(container.childSelected.length==0){
            resultTip({
                error: 'warning',
                msg: '请选择学生'
            });
            return
        }
        for(var i=0;i<container.childSelected.length;i++){
            chaildfiler.push(container.childSelected[i].id)
        }
        $.ajax({
            url: '<?php echo $this->createUrl("updateChild")?>',
            type: "POST",
            async: true,
            dataType: 'json',
            data: {
                'plan_id':'<?php echo $taskData['plan']->id?>',
                'childids':chaildfiler
            },
            success: function(data) {
                if(data.state=='success'){
                    var childKeep = _.sortBy(data.data, 'name');
                    for(key in data.data){
                        childData.push(data.data[key])
                    }
                    renderChildList();
                    progress()
                    $('#addClassModal').modal('hide');
                    resultTip({msg:'添加成功'});
                }else{
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                }
            },
            error: function() {
                resultTip({
                    error: 'warning',
                    msg: data.message
                });
            }
        });
    }
    //进度
    progress()
    function progress(){
        for(var i=0;i<childids.length;i++){
            if(childids[i] == undefined) {
              childids.splice(i,1);
              i = i - 1;
            }
        }
        for(var i=0;i<childData.length;i++){
            if(childData[i] == undefined) {
              childData.splice(i,1);
              i = i - 1;
            }
        }
        var progressDataList={}
        progressDataList.data={}
        progressDataList.data.total=childData.length
        progressDataList.data.complete=childids.length
        if(childData.length==0 && childids.length==0){
            progressDataList.data.percentage='0%'
        }else{
            progressDataList.data.percentage=Math.round(childids.length / childData.length * 10000) / 100.00 + "%"
        }
        $('#progress').html( progressTemplate(progressDataList) );

    }
    //删除学生
    function delChildNoMark(id,obj,e){
        // $.ajax({
        //     url: '<?php echo $this->createUrl("inquireWechat")?>',
        //     type: "POST",
        //     async: true,
        //     dataType: 'json',
        //     data: {
        //         'plan_id':'<?php echo $taskData['plan']->id?>',
        //         'childid':id
        //     },
        //     success: function(data) {
        //         if(data.state=='success'){
                    $('#delChildid').html(id)
        //             if(data.data>0){
        //                 $('.delModal').html("已发送通知"+data.data+"遍,确认删除吗")
        //             }else{
                        $('.delModal').html("确认删除吗")
        //             }

                    $('#delChildModal').modal('show')
        //         }
        //     },
        //     error: function() {
        //         alert("请求错误")
        //     }
        // });
    }
    function delConfirm(){
        var id=$('#delChildid').html()
        $.ajax({
            url: '<?php echo $this->createUrl("delChild")?>',
            type: "POST",
            async: true,
            dataType: 'json',
            data: {
                'plan_id':'<?php echo $taskData['plan']->id?>',
                'childid':id
            },
            success: function(data) {
                if(data.state=='success'){
                    for(var i=0;i<childData.length;i++){
                        if(childData[i] == undefined) {
                          childData.splice(i,1);
                          i = i - 1;
                        }

                    }
                    for(var i=0;i<childData.length;i++){
                        if(childData[i].id==id){
                            delete childData[i];
                        }
                    }
                    renderChildList();
                    progress();
                    $('#delChildModal').modal('hide')
                }
            },
            error: function() {
                alert("请求错误")
            }
        });
    }
</script>
