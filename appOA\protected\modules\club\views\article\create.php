<?php
    foreach (Yii::app()->user->getFlashes() as $key => $message) {
        echo '<div class="flash-' . $key . '" style="background: #E6EFC2;color:#264409;border-color:#C6D880;padding:8px;border:1px solid #ddd;margin-bottom:5px;">' . $message . "</div>\n";
    }
?>
<div class="h_a">基本信息</div>
<div class="form">
<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'club-article-form',
	'enableAjaxValidation'=>false,
)); 
$branchList = Branch::model()->getBranchList();
?>
<div class="table_full">
    <table width="100%">
            <col width="150" />
            <col width="400" />
            <col />
            <tbody>
                <tr>
                    <th><?php echo $form->labelEx($model,'branch_id'); ?></th>
                    <td><?php echo $form->dropDownList($model, 'branch_id', array('' => Yii::t('club', '请选择')) + $branchList); ?></td>
                    <td><div class="fun_tips"><?php echo $form->error($model,'branch_id'); ?></div></td>
                </tr>
                <tr>
                    <th><?php echo $form->labelEx($model,'author_email'); ?></th>
                    <td>
                        <?php echo $form->textField($model,'author_email', array('size'=>30,'maxlength'=>100)); ?>
                        <?php echo $form->hiddenField($model,'author_uid'); ?>
                        <?php echo CHtml::tag('span',array('id'=>'userName'),($model->isNewRecord)?'':$model->user->getName());?>
                    </td>
                    <td>
                        <div class="fun_tips"><?php echo $form->error($model,'author_email'); ?></div>
                    </td>
                </tr>
                <tr>
                    <th><?php echo $form->labelEx($model,'author_text'); ?></th>
                    <td><?php echo $form->textField($model,'author_text',array('size'=>30,'maxlength'=>255)); ?></td>
                    <td><div class="fun_tips"><?php echo $form->error($model,'author_text'); ?></div></td>
                </tr>
               
                <tr>
                    <th><?php echo $form->labelEx($model,'category'); ?></th>
                    <td><?php echo $form->dropDownList($model, 'category', array('' => Yii::t('club', '请选择')) + ClubArticle::getClubCategory()); ?></td>
                    <td><?php echo $form->error($model,'category'); ?></div></td>
                </tr>
                
                <tr>
                    <th><?php echo $form->labelEx($model,'language'); ?></th>
                    <td><?php echo $form->dropDownList($model, 'language', array('' => Yii::t('club', '请选择')) + ClubArticle::getClubLanguage()); ?></td>
                    <td><div class="fun_tips"><?php echo $form->error($model,'language'); ?></div></td>
                </tr>

                <tr>
                    <th><?php echo $form->labelEx($model,'title'); ?></th>
                    <td><?php echo $form->textField($model,'title',array('size'=>30)); ?></td>
                    <td><div class="fun_tips"><?php echo $form->error($model,'title'); ?></div></td>
                </tr>

                <tr>
                    <th><?php echo $form->labelEx($model,'album'); ?></th>
                    <td><?php echo $form->dropDownList($model, 'album', array('' => Yii::t('club', '请选择')) + ClubArticle::getClubAlbum()); ?></td>
                    <td><div class="fun_tips"><?php echo $form->error($model,'album'); ?></div></td>
                </tr>

                <tr>
                    <th><?php echo $form->labelEx($model,'status'); ?></th>
                    <td><?php echo $form->radioButtonList($model,'status',ClubArticle::getClubStatus(), array('separator'=>'&nbsp;&nbsp;')); ?></td>
                    <td><div class="fun_tips"><?php echo $form->error($model,'status'); ?></div></td>
                </tr>

                <tr>
                    <th><?php echo $form->labelEx($model,'privacy'); ?></th>
                    <td><?php echo $form->radioButtonList($model,'privacy',ClubArticle::getClubPrivacy(), array('separator'=>'&nbsp;&nbsp;')); ?></td>
                    <td><div class="fun_tips"><?php echo $form->error($model,'privacy'); ?></div></td>
                </tr>
        </tbody>
    </table>
</div>
<div id="articlecontent" <?php if ($model->isNewRecord):?>style="display: none;" <?php endif;?>>
    <div class="h_a">文章内容</div>
    <div class="table_full">
         <table width="100%">
                <col width="150" />
                <col width="900" />
                <col />
                <tbody>
                    <tr>
                        <th><?php echo $form->labelEx($model->content, 'content'); ?></th>
                        <td style="height: 500px;">
                            <?php
                                /*$this->widget('common.extensions.xheditor.XHEditor', array(
                                    'model' => $model->content,
                                    'modelAttribute' => 'content',
                                    'language' => Yii::app()->language,
                                    'config' => array(
                                        'tools' => 'Bold,Italic,Underline,Strikethrough,Align,List,Link,Source,Fullscreen',
                                    )
                                ));*/
                            $this->widget('common.extensions.ueditor.ueditor',array(
                                'model' => $model->content,
                                'attribute' => 'content',
                                'language' =>Yii::app()->language == 'en_us' ? 'en' : 'zh-cn',
                                'width' =>'100%',
                                'upImageUrl' => $this->createUrl('/club/article/upimage', array('id'=>$model->id)),
                                'imageMgt' => $this->createUrl('/club/article/imageMgt', array('id'=>$model->id)),
                            ));
                            ?>
                        </td>
                        <td><div class="fun_tips"><?php echo $form->error($model->content, 'content'); ?></div></td>
                    </tr>
                    <tr>
                        <th><?php echo $form->labelEx($model,'publish'); ?></th>
                        <td><?php echo $form->radioButtonList($model,'publish',array('否','是'), array('separator'=>'&nbsp;&nbsp;')); ?></td>
                        <td><div class="fun_tips"><?php echo $form->error($model,'publish'); ?></div></td>
                    </tr>
                </tbody>
        </table>
    </div>
</div>
<div class="btn_wrap">
    <div class="btn_wrap_pd">
        <?php echo CHtml::hiddenField('id', $model->id);?>
        <button class="btn btn_submit mr10 J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
    </div>
</div>
<?php $this->endWidget(); ?>
</div>

<!-- form -->
<script>
$(document).ready(function(){
    //根据EMAIL地址查询用户的ID
    $("#ClubArticle_author_email").blur(function(){
        var email = $("#ClubArticle_author_email").val();
        if (email){
            $.ajax({
                type: "POST",
                url: '<?php echo $this->createUrl('//club/article/searchUser') ?>',
                data: "email=" + email,
                dataType: 'json',
                success: function(data) {
                    if(data.state=='success'){
                        $("#ClubArticle_author_uid").attr('value',data.data.uid);
                        $("#userName").html(data.data.name);
                    }
                    else{
                        $("#ClubArticle_author_uid").attr('value','');
                        $("#userName").html(data.message);
                    }
                }
            });
        }
    });
});
</script>