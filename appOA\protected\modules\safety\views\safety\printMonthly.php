<?php
$securityType = CommonUtils::LoadConfig('CfgSecurity');

$s = array();
if($safetyData){
    foreach ($safetyData as $k=>$item) {
        $s[$item['type']][$k] = $item['name'];
    }
}
?>

    <table class="table table-bordered">
        <thead>
        <tr>
            <th colspan="4" class="text-center"><?php echo Yii::t('safety','Summary of monthly self-inspection results') ?><span  style="font-weight:lighter" class="pull-right text-muted"><?php echo $yearMonth; ?></span></th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td><?php echo Yii::t('safety','Inspection category') ?></td>
            <td><?php echo Yii::t('safety','Inspection item') ?></td>
            <td><?php echo Yii::t('safety','Inspection department/person') ?></td>
            <td><?php echo Yii::t('safety','Score') ?></td>
        </tr>
        <?php
        foreach ($securityType['type'] as $k=>$item):
            ?>
            <tr>
                <td  class="col-md-3" rowspan="<?php echo isset($s[$k]) ? count($s[$k]) + 1 : 2 ; ?>"><?php echo Yii::app()->language == 'zh_cn' ? $item['cn'] : $item['en'] ; ?></td>
            </tr>
            <?php if($s[$k]){?>
            <?php foreach($s[$k] as $key => $val): ?>
                <tr>
                    <td><?php echo $val ?></td>
                    <td>
                        <?php
                        if(isset($user) && isset($user[$key])){
                            foreach ($user[$key] as $userName) {
                                echo $userName . ' ';
                            }
                        }
                        ?>
                    </td>
                    <td><?php echo (isset($scoreArr) && isset($scoreArr[$key])) ? $scoreArr[$key] : 0 ; ?></td>
                </tr>
            <?php endforeach; ?>
        <?php }else{ ?>
            <tr>
                <td>无</td>
                <td></td>
                <td></td>
            </tr>
        <?php } ?>
        <?php  endforeach; ?>
        <tr>
            <td colspan="2"><?php echo Yii::t('safety','Overall score of self-inspection for safety in the month') ?></td>
            <td></td>
            <td></td>
        </tr>
        </tbody>
    </table>

    <table class="table table-bordered">
        <tbody>
        <tr>
            <td rowspan="7" class="text-center" width="100"><?php echo Yii::t('safety','Monthly self-inspection table') ?></td>
        </tr>
        <tr>
            <td rowspan="7" class="text-center" width="100"><?php echo Yii::t('safety','Description of scoring:') ?></td>
        </tr>
        <tr>
            <td><?php echo Yii::t('safety','5 points  good state') ?></td>
        </tr>
        <tr>
            <td><?php echo Yii::t('safety','4 points  rectify within 2 weeks') ?></td>
        </tr>
        <tr>
            <td><?php echo Yii::t('safety','3 points   rectify within 1 week') ?></td>
        </tr>
        <tr>
            <td><?php echo Yii::t('safety','Below 3 points  rectify immediately') ?></td>
        </tr>
        <tr>
            <td><?php echo Yii::t('safety','N/A  the item is not applicable for scoring') ?></td>
        </tr>
        </tbody>
    </table>