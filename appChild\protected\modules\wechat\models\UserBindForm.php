<?php

/**
 * 设置分享报告
 */
class UserBindForm extends CFormModel
{
	public $username;
	public $password;
	public $wechatUname;
	public $userid;
	
	public function rules()
	{
		return array(
			array('username,password,wechatUname', 'required'),
			array('password', 'userAuth')
		);
	}
	
	public function attributeLabels()
	{
		return array(
			'username'=>Yii::t("wechat", "Account"),
			'password'=>Yii::t("wechat", "Password"),
		);
	}
	
	public function userAuth($attribute,$params)
	{
		if(!$this->hasErrors())  // we only want to authenticate when no input errors
		{
			$identity=new UserIdentity($this->username,$this->password);
			$identity->authenticate();
			switch($identity->errorCode)
			{
				case UserIdentity::ERROR_NONE:
					$duration= 3600*24*300; // 30 days
					$login = Yii::app()->user->login($identity,$duration);
					if(!$login){
						$this->addError("status",Yii::t("user", "You account has no permission set."));
					}else{
						$this->userid = Yii::app()->user->id;
					}
					break;
				case UserIdentity::ERROR_EMAIL_INVALID:
				case UserIdentity::ERROR_USERNAME_INVALID:
					$this->addError("username",Yii::t("user", "Email does not exist."));
					break;
				case UserIdentity::ERROR_STATUS_NOTACTIV:
					$this->addError("status",Yii::t("user", "You account is not activated."));
					break;
				case UserIdentity::ERROR_IS_STAFF:
					// $this->redirectToOA = true;
					$this->addError("status",Yii::t("user", "You account is for staff."));
					break;
				case UserIdentity::ERROR_PASSWORD_INVALID:
					$this->addError("password",Yii::t("user", "Password is incorrect."));
					break;
			}
		}
	}	
	
}