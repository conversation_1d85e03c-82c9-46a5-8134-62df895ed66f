<?php
$childInterview = new InterviewTeacher();
?>
	<div class="container-fluid">
		<ol class="breadcrumb">
			<li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('//mcampus/default/index'))?></li>
			<li><?php echo CHtml::link(Yii::t('site','Basic'), array('//mcampus/default/index'))?></li>
			<li class="active">招生管理</li>
			<li class="active">面试老师</li>
		</ol>

		<div class="row">
			<!-- 左侧菜单 -->
			<div class="col-md-2 col-sm-2">
				<?php
				$this->widget('zii.widgets.CMenu',array(
					'items'=> $this->getMenu(),
					'id'=>'visitConfiguration',
					'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked background-gray mb10'),
					'activeCssClass'=>'active',
					'itemCssClass'=>''
				));
				?>
			</div>

			<div class="col-md-10 col-sm-12">
				<div class="mb10">
					<button class="btn btn-primary" onclick="vacationAdd()">
						<?php echo Yii::t('campus', '添加面试老师'); ?>
					</button>
					<!--<a class="J_modal btn btn-primary" href="#" data-toggle="modal" data-target="#model"><span class="glyphicon glyphicon-plus"></span> 新建记录</a>-->
				</div>
				<div class="panel panel-default">
					<div class="panel-body">

						<?php
						$this->widget('ext.ivyCGridView.BsCGridView', array(
							'id'=>'teacher-form',
							'dataProvider'=>$dataProvider,
							'afterAjaxUpdate'=>'js:function(){head.Util.ajaxDel()}',
							'colgroups'=>array(
								array(
									"colwidth" => array(100, 100, 100, 100, 100, 100, 100, 100),
								)
							),
							'columns' => array(
								array(
									'name' => Yii::t('ivyer', 'school'),
									'value' => array($this, 'getSchool'),
									//'value' => '$data->school_id',
								),
								array(
									'name' => Yii::t('ivyer', 'teacher'),
									'value' => array($this, 'getTeacher'),
								),
								array(
									'name' => Yii::t('ivyer', 'updated_time'),
									'value' => 'date("Y-m-d", $data->updated_time)',
								),
								array(
									'name' => Yii::t('ivyer', 'uid'),
									'value' => array($this, 'getUid'),

								),
								array(
									'name' => Yii::t('global', 'Action'),
									'type' => 'raw',
									'value' => array($this, 'getButtons'),
								),
							),
						));
						?>
					</div>
				</div>
			</div>
		</div>
	</div>

<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
						aria-hidden="true">×</span></button>
				<h4 class="modal-title"><?php echo Yii::t('campus', '添加面试老师'); ?></h4>
			</div>
			<?php
			$form = $this->beginWidget('CActiveForm', array(
				'id' => 'interviewTeacher-form',
				'enableAjaxValidation' => false,
				'action' => $this->createUrl('interviewTeacherAdd'),
				'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form'),
			));
			?>
			<div class="modal-body">
				<div class="form-group">
					<label class="col-xs-3 control-label"><?php echo Yii::t('campus','面试老师') ?></label>
					<div class="col-xs-9">
						<?php $this->widget('ext.search.StaffSearchBox', array(
							'acInputCSS' => 'form-control',
							'htmlOptions' => array('class' => 'form-control'),
							'data' => $teacher_name,
							'useModel' => true,
							'model' => $childInterview,
							'attribute' => 'teacher_id',
							'allowMultiple' => 8,
							'withAlumni' => false,
						)) ?>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<button type="submit"
						class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
				<button type="button" class="btn btn-default"
						data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
			</div>
			<?php $this->endWidget(); ?>
		</div>
	</div>
</div>








<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>

<script>

    function vacationAdd() {
        $('#myModal').modal({backdrop: 'static'});
    }

    // 回调：添加成功
    function cbAddTeacher() {
        $('#myModal').modal('hide');
        $.fn.yiiGridView.update('teacher-form');
    }


</script>


