<?php

/**
 * This is the model class for table "ivy_notes_mii".
 *
 * The followings are the available columns in table 'ivy_notes_mii':
 * @property integer $id
 * @property integer $childid
 * @property string $schoolid
 * @property integer $classid
 * @property integer $pid
 * @property integer $aid
 * @property integer $yid
 * @property integer $weeknum
 * @property integer $stat
 * @property string $en_title
 * @property string $cn_title
 * @property string $en_content
 * @property string $cn_content
 * @property string $ext1
 * @property integer $updated_timestamp
 * @property integer $userid
 */
class NotesMii extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return NotesMii the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_notes_mii';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('schoolid, classid, pid, aid, yid, weeknum, stat, en_title, cn_title, ext1, updated_timestamp, userid', 'required'),
			array('childid, classid, pid, aid, yid, weeknum, stat, updated_timestamp, userid', 'numerical', 'integerOnly'=>true),
			array('schoolid', 'length', 'max'=>15),
			array('en_title, cn_title', 'length', 'max'=>255),
			array('en_content, cn_content', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, childid, schoolid, classid, pid, aid, yid, weeknum, stat, en_title, cn_title, en_content, cn_content, ext1, updated_timestamp, userid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'projectInfo'=>array(self::BELONGS_TO, 'CurProjects', 'pid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'childid' => 'Childid',
			'schoolid' => 'Schoolid',
			'classid' => 'Classid',
			'pid' => 'Pid',
			'aid' => 'Aid',
			'yid' => 'Yid',
			'weeknum' => 'Weeknum',
			'stat' => 'Stat',
			'en_title' => 'En Title',
			'cn_title' => 'Cn Title',
			'en_content' => 'En Content',
			'cn_content' => 'Cn Content',
			'ext1' => 'Ext1',
			'updated_timestamp' => 'Updated Timestamp',
			'userid' => 'Userid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('pid',$this->pid);
		$criteria->compare('aid',$this->aid);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('weeknum',$this->weeknum);
		$criteria->compare('stat',$this->stat);
		$criteria->compare('en_title',$this->en_title,true);
		$criteria->compare('cn_title',$this->cn_title,true);
		$criteria->compare('en_content',$this->en_content,true);
		$criteria->compare('cn_content',$this->cn_content,true);
		$criteria->compare('ext1',$this->ext1,true);
		$criteria->compare('updated_timestamp',$this->updated_timestamp);
		$criteria->compare('userid',$this->userid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}