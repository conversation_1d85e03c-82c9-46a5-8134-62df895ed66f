<?php
$level = array(
        1 => '一级',
        2 => '二级',
        3 => '三级',
);

?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Routines'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('site', '安全事故管理') ?></li>
    </ol>

    <div class="row">
        <div class="col-md-12 mb10">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->module->getMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-tabs'),
                'activeCssClass' => 'active',
            ));
            ?>
        </div>
        <div class="col-md-12">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <tr>
                        <th class="text-center bg-success" colspan="9"><?php echo Yii::t('safety','Incident Reporting Process - Campus permissions') ?></th>
                    </tr>
                    <tr>
                        <th width="150"><?php echo Yii::t('safety','Incident Date') ?></th>
                        <th><?php echo Yii::t('safety','Incident Time') ?></th>
                        <th><?php echo Yii::t('safety','Incident Name') ?></th>
                        <th colspan="2"><?php echo Yii::t('safety','Parental Mood') ?></th>
                        <th><?php echo Yii::t('safety','Media Intervention') ?></th>
                        <th><?php echo Yii::t('safety','Government Intervention') ?></th>
                        <th><?php echo Yii::t('safety','员工介入') ?> </th>
                        <th><?php echo Yii::t('safety','教委介入') ?></th>
                    </tr>
                    <tr>
                        <td>
                            <?php echo date("Y-m-d" , $model->incident_date);?>
                        </td>
                        <td>
                            <?php echo date("H:i:s" , $model->incident_date);?>
                        </td>
                        <td>
                            <?php echo $model->incident_name;?>
                        </td>
                        <td colspan="2">
                            <?php echo $model->parent_mood;?>
                        </td>
                        <td>
                            <?php echo $model->media_in == 1 ? Yii::t('safety','YES') : Yii::t('safety','NO') ; ?>
                        </td>
                        <td>
                            <?php echo $model->government_in == 1 ? Yii::t('safety','YES') : Yii::t('safety','NO') ; ?>
                        </td>
                        <td>
                            <?php echo $model->staff_in == 1 ? Yii::t('safety','YES') : Yii::t('safety','NO') ; ?>
                        </td>
                        <td>
                            <?php echo $model->edu_in == 1 ? Yii::t('safety','YES') : Yii::t('safety','NO') ; ?>
                        </td>
                    </tr>
                    <tr>
                        <th class="text-center" colspan="9"><?php echo Yii::t('safety','Campus Incident Roport') ?></th>
                    </tr>
                    <tr>
                        <th><?php echo Yii::t('safety','Date of the incident') ?></th>
                        <td colspan="4">
                            <?php echo date("Y-m-d H:i:s" , $model->incident_date);?>
                        </td>
                        <th><?php echo Yii::t('safety','Reported by') ?> </th>
                        <td>
                            <?php
                            echo isset($userModel[$model->reported_by]) ? $userModel[$model->reported_by]->getName() : ""; ?>
                        </td>
                        <th><?php echo Yii::t('safety','Reporter Position') ?></th>
                        <td>
                            <?php echo (isset($hrModel[$model->reported_position]) ? $hrModel[$model->reported_position]->getName() : "");?>
                        </td>
                    </tr>
                    <tr>
                        <th><?php echo Yii::t('safety','Place of the incident') ?></th>
                        <td colspan="4">
                            <?php echo $model->incident_place;?>
                        </td>
                        <th><?php echo Yii::t('safety','Report Date') ?> </th>
                        <td>
                            <?php echo date("Y-m-d H:i:s" , $model->reported_date);?>
                        </td>
                        <th><?php echo Yii::t('safety','Level of Risk') ?></th>
                        <td>
                            <?php echo $level[$model->level];?>
                        </td>
                    </tr>
                    <tr>
                        <th><?php echo Yii::t('safety','Incident') ?></th>
                        <td colspan="8">
                            <?php echo $model->incident_title;?>
                        </td>
                    </tr>
                    <tr>
                        <th><?php echo Yii::t('safety','选择当事人') ?></th>
                        <td colspan="8">
                            <div class="col-xs-9">
                                <?php echo $model->incident_type == 1 ? Yii::t('safety','Student') : Yii::t('safety','Teacher') ; ?>
                            </div>
                        </td>
                    </tr>
                    <tr id = "status1_th" style="<?php echo ($model->incident_type == 1) ? "" : "display:none;" ; ?>">
                        <th><?php echo Yii::t('safety','Situation of the person(s) injured in the incident') ?></th>
                        <th><?php echo Yii::t('safety','Student Name') ?></th>
                        <td>
                            <?php
                            echo ($childModel) ? $childModel->getChildName() : "";?>
                        </td>
                        <th><?php echo Yii::t('safety','Class') ?></th>
                        <td>
                            <?php
                            echo (isset($model->class)) ? $model->class->title : "" ;?>
                        </td>
                        <th><?php echo Yii::t('safety','Injured Part') ?></th>
                        <td>
                            <?php echo $model->injured_part;?>
                        </td>
                        <th><?php echo Yii::t('safety','Involved Teacher Name') ?> </th>
                        <td>
                            <?php
                            echo (isset($userModel[$model->involved_teacher]) ? $userModel[$model->involved_teacher]->getName() : "");?>
                        </td>
                    </tr>
                    <tr id = "status2_th" style="<?php echo ($model->incident_type == 2) ? "" : "display:none;" ; ?>">
                        <th><?php echo Yii::t('safety','Situation of the person(s) injured in the incident') ?></th>
                        <th><?php echo Yii::t('safety','Staff Name') ?></th>
                        <td>
                            <?php echo (isset($userModel[$model->staff]) ? $userModel[$model->staff]->getName() : "");?>
                        </td>
                        <th><?php echo Yii::t('safety','Position') ?></th>
                        <td>
                            <?php echo (isset($hrModel[$model->staff_position]) ? $hrModel[$model->staff_position]->getName() : "");?>
                        </td>
                        <th><?php echo Yii::t('safety','Injured Part') ?></th>
                        <td  colspan="3">
                            <?php echo $model->injured_part;?>
                        </td>
                    </tr>
                    <tr>
                        <th><?php echo Yii::t('safety','Description of the incident') ?></th>
                        <td colspan="8">
                            <?php echo $model->incident_desc;?>
                        </td>
                    </tr>
                    <tr>
                        <th rowspan="2"><?php echo Yii::t('safety','Process and current status description') ?> </th>
                        <th colspan="2"><?php echo Yii::t('safety','是否去医院') ?></th>
                        <td colspan="6">
                            <div class="col-xs-9">
                                <?php echo $model->hospital == 1 ? '是' : '否' ; ?>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="8">
                            <?php echo $model->incident_process;?>
                        </td>
                    </tr>
                    <tr>
                        <th colspan="9"><?php echo Yii::t('safety','Brife analysis of the incident') ?></th>
                    </tr>
                    <tr>
                        <th><?php echo Yii::t('safety','Immediate Cause') ?> </th>
                        <td colspan="8">
                            <?php echo $model->immediate_case;?>
                        </td>
                    </tr>
                    <tr>
                        <th><?php echo Yii::t('safety','Primary Cause') ?></th>
                        <td colspan="8">
                            <?php echo $model->primary_case;?>
                        </td>
                    </tr>
                    <tr>
                        <th><?php echo Yii::t('safety','Immediate corrective action') ?></th>
                        <td colspan="8">
                            <?php echo $model->immediate_action;?>
                        </td>
                    </tr>
                    <tr>
                        <th><?php echo Yii::t('safety','Next step measure and speculation on the evolution direction of the incident') ?> </th>
                        <td colspan="8">
                            <?php echo $model->next_action;?>
                        </td>
                    </tr>
                </table>
                <?php if($trackModel->id): ?>
                <table class="table table-bordered">
                    <tr>
                        <td class="text-center bg-success" colspan="16"><?php echo Yii::t('safety','Incident Development Rank Evaluation') ?></td>
                    </tr>
                    <tr>
                        <td class="text-center" rowspan="6" width="80"><?php echo Yii::t('safety','Risk Event Tracking')?> </td>
                        <td class="text-center" colspan="4" rowspan="2"><?php echo Yii::t('safety','The Basical Information of Incident')?></td>
                        <td class="text-center"><?php echo Yii::t('safety','Participant:CD')?></td>
                        <td class="text-center" colspan="8"><?php echo Yii::t('safety','Participant：PD、QS、Revelent Leader from HQ')?></td>
                        <td class="text-center"><?php echo Yii::t('safety','Participant：PD、QS、Revelent Leader from HQ')?></td>
                        <td class="text-center" style="width: 6%" class="text-center" rowspan="5"><?php echo Yii::t('safety','完结')?></td>
                    </tr>
                    <tr>
                        <td class="text-center"><?php echo Yii::t('safety','Accident Report 30分')?></td>
                        <td class="text-center" colspan="8"><?php echo Yii::t('safety','Event Development 50分')?></td>
                        <td class="text-center" rowspan="3"><?php echo Yii::t('safety','The Solving Methods and The Results of Process 20分')?></td>
                    </tr>
                    <tr>
                        <td class="text-center" width="80"><?php echo Yii::t('safety','Campus')?></td>
                        <td class="text-center" width="80"><?php echo Yii::t('safety','Time')?></td>
                        <td class="text-center" width="80"><?php echo Yii::t('safety','Name of accident')?></td>
                        <td class="text-center" width="80"><?php echo Yii::t('safety','Event Level')?></td>
                        <td class="text-center" width="80"><?php echo Yii::t('safety','character of accident')?> </td>
                        <td class="text-center" colspan="7"></td>
                        <td class="text-center"><?php echo Yii::t('safety','Final score of event development')?></td>
                    </tr>
                    <tr>
                        <td class="text-center" rowspan="3"><?php echo $schoolid ?></td>
                        <td class="text-center" rowspan="3"><?php echo date("Y-m-d H:i:s" ,$model->incident_date) ?></td>
                        <td class="text-center" rowspan="3"><?php echo $model->incident_name ?></td>
                        <td class="text-center" rowspan="3"><?php echo $level[$model->level] ?></td>
                        <td class="text-center" rowspan="2"><?php echo Yii::t('safety','negligence 30分')?><br>
                            <?php echo Yii::t('safety','Accident-Preventable 20分')?><br>
                            <?php echo Yii::t('safety','Accident-Unpreventable 10分')?></td>
                        <td class="text-center"><?php echo Yii::t('safety','Report Accident according to the process 5分')?></td>
                        <td class="text-center"><?php echo Yii::t('safety','The Authenticity of The Accident(Video、Hospital Report) 5分')?></td>
                        <td class="text-center"><?php echo Yii::t('safety','Processing by CD 20分')?></td>
                        <td class="text-center" colspan="4"><?php echo Yii::t('safety','The rate of regional expansion 20分')?></td>
                        <td class="text-center" rowspan="2"><?php echo Yii::t('safety','Grading()')?></td>
                    </tr>
                    <tr>
                        <td class="text-center"><?php echo Yii::t('safety','NO')?> 5分 <br>
                            <?php echo Yii::t('safety','YES')?> 0分</td>
                        <td class="text-center"><?php echo Yii::t('safety','Untrue 5分')?><br>
                            <?php echo Yii::t('safety','True 0分')?></td>
                        <td class="text-center"><?php echo Yii::t('safety','Have Problem 20分')?><br>
                            <?php echo Yii::t('safety','No Problem 0分')?></td>
                        <td class="text-center"><?php echo Yii::t('safety','parent involvement 5分')?><br>
                            <?php echo Yii::t('safety','No parent involvement 0分')?></td>
                        <td class="text-center"><?php echo Yii::t('safety','Staff involvement 5分')?><br>
                            <?php echo Yii::t('safety','No Staff involvement 0分')?></td>
                        <td class="text-center"><?php echo Yii::t('safety','Media involvement 5分')?><br>
                            <?php echo Yii::t('safety','No Media involvement 0分')?></td>
                        <td class="text-center"><?php echo Yii::t('safety','Jiaowei involvement 5分')?><br>
                            <?php echo Yii::t('safety','No jiaowei involvement 0分')?></td>
                        <td class="text-center"><?php echo Yii::t('safety','Have been Solved in a week  5分')?><br>
                            <?php echo Yii::t('safety','Have been Expanded within a week 10分')?><br>
                            <?php echo Yii::t('safety','Have been Expanded More than a week 20分')?></td>
                    </tr>
                    <tr>
                        <td class="text-center">
                            <?php echo $trackModel->accident_type; ?>
                        </td>
                        <td class="text-center">
                            <?php
                            $array = array(0=> 0, 5 => 5);
                            echo $array[$trackModel->invidet_report] ?>
                        </td>
                        <td class="text-center">
                            <?php
                            echo $array[$trackModel->incidet_truth] ?>
                        </td>
                        <td class="text-center">
                            <?php
                            $array = array(0=> 0, 20 => 20);
                            echo $array[$trackModel->process_cd] ?>
                        </td>

                        <td class="text-center" id="parent_mood_num"><?php echo ($model->parent_mood) ? 5 : 0; ?></td>
                        <td class="text-center" id="staff_in_num"><?php echo ($model->staff_in == 1) ? 5 : 0; ?></td>
                        <td class="text-center" id="media_in_num"><?php echo ($model->media_in == 1) ? 5 : 0; ?></td>
                        <td class="text-center" id="edu_in_num"><?php echo ($model->edu_in == 1) ? 5 : 0; ?></td>

                        <td class="text-center" id="score"><?php echo ($trackModel->development_score) ? $trackModel->development_score : ""; ?></td>
                        <td class="text-center">
                            <?php echo $trackModel->solving ?>
                        </td>
                        <td></td>
                    </tr>
                </table>

                <table class="table table-bordered">
                    <tr>
                        <th class="text-center bg-success" colspan="5"><?php echo Yii::t('safety','Final Grade Confirm Form') ?></th>
                    </tr>
                    <tr>
                        <th class="text-center" colspan="3" rowspan="2"><?php echo Yii::t('safety','The Basical Information of Incident') ?></th>
                        <th class="text-center" colspan="2"><?php echo Yii::t('safety','Participant：PD、QS、Revelent Leader from HQ') ?></th>
                    </tr>
                    <th class="text-center" colspan="2"><?php echo Yii::t('safety','Final Grand Confirm') ?></th>
                    </tr>
                    <tr>
                        <th class="text-center"><?php echo Yii::t('safety','Campus') ?></th>
                        <th class="text-center"><?php echo Yii::t('safety','Time') ?></th>
                        <th class="text-center"><?php echo Yii::t('safety','Name of accident') ?></th>
                        <th class="text-center"><?php echo Yii::t('safety','Score') ?></th>
                        <th class="text-center"><?php echo Yii::t('safety','Final Grand Comfirm/Treatment Efficiency') ?></th>
                    </tr>
                    <tr>
                        <td class="text-center" rowspan="2"><?php echo $schoolid ?></td>
                        <td class="text-center" rowspan="2"><?php echo date("Y-m-d H:i:s" ,$model->incident_date) ?></td>
                        <th class="text-center" rowspan="2"><?php echo $model->incident_name ?></th>
                        <th class="text-center"><?php echo Yii::t('safety','Total score = Accident Nature + Event Development + Event Result') ?></th>
                        <th class="text-center">81-100 一级 <?php echo Yii::t('safety','Poor') ?> <br>
                            61-80  二级 <?php echo Yii::t('safety','Fair') ?> <br>
                            41-60  三级 <?php echo Yii::t('safety','Normal') ?> <br>
                            0-40  四级 <?php echo Yii::t('safety','Good') ?></th>
                    </tr>
                    <tr>
                        <th><?php $num =  $trackModel->accident_type + $trackModel->development_score + $trackModel->solving;
                            echo $num;
                            ?></th>
                        <th><?php
                            if($num > 0 && $num < 41){
                                echo '四级';
                            }elseif ($num > 40 && $num < 61) {
                                echo '三级';
                            }elseif ($num > 60 && $num < 81) {
                                echo '二级';
                            }else{
                                echo '一级';
                            }

                            ?></th>
                    <tr>
                    </tr>
                </table>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>