body
{
	margin: 0;
	padding: 0;
	color: #443B32;
	font: normal 10pt "Microsoft Yahei",Arial,Helvetica,sans-serif;
	background: url("../images/gradient.png") repeat !important;
	/*background: url("../images/gradient.png") no-repeat scroll 50% 0 #EEECE7;*/
}

#describer{
	background: url("../images/bg/green/main_bg.png") repeat !important;
}

.select-lang{
	margin: 0 auto;
	width: 360px;
}
.select-lang li{
	float: left;
	display: block;
	width: 180px;
	text-align: center;	
}
.select-lang li p{
	margin-bottom: 0.4em;
	font-weight: bold;
	margin-top: 0.5em;
}
.select-lang li.opt-cn{
	font-size: 18px;
	color:#333;
	margin-top: 0.4em;
}
.select-lang li.opt-en{
	font-size: 18px;
	color:#333;
	font-family: "Trebuchet MS";
	margin-top: 0.4em;
}
.select-lang li a.flag{
	display: block;
	height: 140px;
	opacity: 0.3;
}
.select-lang li a span{
	display: block;
	padding-top: 146px;
	font-size: 18px;
}
.select-lang li a:hover{
/*	opacity: 0;*/
}

.select-lang li.opt-cn{
	background: url("../images/lang/zh_cn.png") no-repeat center center;
}
.select-lang li.opt-en{
	background: url("../images/lang/en_us.png") no-repeat center center;
}

ul.testimonials li{
	float: left;
	width: 206px;
	margin-right: 17px;
	position: relative;
	color:#593E1A;
}
ul.testimonials li{
	/**display: inline;*/
}
ul.testimonials li .text{
    z-index: 0;
}
ul.testimonials li .text p{
    height: 72px;
	overflow: hidden;
}
ul.testimonials li .text, ul.testimonials li .text .full-text{
	text-indent: 30px;
	background:#DEDDD0;
	border-radius: 4px;
	padding: 2px 6px;
	height: 72px;
	margin-bottom: 12px;
	position: relative;
    line-height: 18px;
}
ul.testimonials li .text .full-text{
	width: 400px;
	height: auto;
	position: absolute;
	left: 0;
	bottom: -12px;
	background:#fff;
	display: none;
	z-index: 999999;
	padding: 20px;
}
ul.testimonials li:hover .full-text{
	display: block;
}
ul.testimonials li .text .full-text p{
	height: auto;
	max-height: 396px;
	overflow-y: auto;
    position: relative;
    padding-top: 8px;
}
ul.testimonials li .full-text img.photo{
	float: left;
    border: 5px solid #FFFFFF;
    box-shadow: 2px 2px 3px #666666;
    width: 100px;
    margin: 0 20px 0 0;	
}
ul.testimonials li .text:after{
	/*content:'...'*/
}
ul.testimonials li .text em.quo{
	position: absolute;
	display: block;
	left: 4px;
	top: -10px;
	font-size: 40px;
	width: 33px;
	height: 30px;
	background: url("../images/icons/quote_s.png") no-repeat left top transparent;
}
ul.testimonials li .text .full-text em.quo{
    left: 130px;
    top: 0px;
}
ul.testimonials li .text em.who{
	position: absolute;
	display: block;
	left: 16px;
	bottom: -10px;
	width: 20px;
	height: 12px;
	background: url("../images/icons/quote_bg.png") no-repeat left top transparent;
}
ul.testimonials li .full-text em.who, ul.testimonials li:hover .text em.who{
	background: url("../images/icons/quote_bg.png") no-repeat left -12px transparent;
}
ul.testimonials li dl.title {
	width: 206px;
	margin-bottom: 4px;
}
ul.testimonials li dl.title dt {
	width: 50px;
	height: 50px;
	float: left;
}
ul.testimonials li dl.title dd {
	margin-left: 56px;
	height: 50px;
	vertical-align: middle;
	width: 150px;
}
ul.testimonials li dl.title dt img{
    border: 2px solid #ccc;
    box-shadow: 2px 2px 3px #666666;
    width: 42px;
}
ul.testimonials li:hover dl.title dt img{
	border: 2px solid #FFFFFF;
	box-shadow: 2px 2px 3px #fff;
}
ul.testimonials li:hover dl.title dd{
	color:#000;
	text-shadow: 2px 1px 1px rgba(255, 255, 255, 0.9);
}
.testimonials-more{
	text-align: right;
	margin-right: 17px;
}
.testimonials-more a{
	color: #fff;
}