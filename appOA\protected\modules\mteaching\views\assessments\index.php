<style>
    [v-cloak] {
        display: none;
    }
    .table th {
        vertical-align: middle!important;
        text-align:center
    }
    .switch {
        position: relative;
        display: inline-block;
    }
    .switch input {display:none;}
    .slider {
        margin: 0;
        display: inline-block;
        position: relative;
        width: 40px;
        height: 20px;
        border: 1px solid #dcdfe6;
        outline: none;
        border-radius: 10px;
        box-sizing: border-box;
        background: #dcdfe6;
        cursor: pointer;
        transition: border-color .3s,background-color .3s;
        vertical-align: middle;
    }
    .slider:before {
        content: "";
        position: absolute;
        top: 1px;
        left: 1px;
        border-radius: 50%;
        transition: all .3s;
        width: 16px;
        height: 16px;
        background-color: #fff; 
    }
    input:checked + .slider {
        background-color: #2196F3;
    }
    input:checked + .slider:before {
        left: 100%;
        margin-left: -17px;
    }
    .textOVerThree {
        display: -webkit-box;
        overflow: hidden;
        white-space: normal !important;
        text-overflow: ellipsis;
        word-wrap: break-word;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        max-height:50px;
        position: relative;
    }
    .showText{
        position: absolute;
        top: 35px;
        right: 0;
        width: 56px;
        background-color: #Fff;
        text-align: center;
        height: 20px;
    }
    .image{
        width:70px;
        height:70px;
        margin:0 auto; 
    }
    .alert{
        padding:8px
    }
    .point{
        width: 6px;
        height: 6px;
        background: #999999;
        border-radius: 50%;
        display: inline-block;
        position: absolute;
        left: 0;
        top: 5px;
    }
    .dragStatus{
        position: absolute;
        width: 100%;
        left: 0;
        height: 100%;
        padding-right: 50px;
    }
    .dragCss{
        border: 1px solid #428bca;
    }
    .imgBg{
        background: #F7F7F8;
    padding: 5px;
    border-radius: 5px;
    }
    .loading{
        width:98%;
        height:100%;
        background:#fff;
        position: absolute; 
        opacity: 0.5;
        z-index: 99
    }
    .loading span{
        width:100%;
        height:100%;
        display:block;
        background: url("<?php echo Yii::app()->theme->baseUrl?>/images/loading.gif")no-repeat center center ;
    }
    .titleText{
        font-weight:600;
        font-size:16px
    }
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Teaching Tasks'), array('//mteaching/default/index')) ?></li>
        <li><?php echo Yii::t('newDS', 'Assessments') ?></li>
    </ol>
    <div class="row" id='container' v-cloak>
        <div class=' col-md-12 col-sm-12' v-if='isShow && classList.length==0'>
            <div class='alert alert-warning'><?php echo Yii::t('newDS', 'None class has been assigned to you.') ?></div>
        </div>
        <div v-else>
            <div class="col-md-12 col-sm-12 mb10 titleText"><?php echo Yii::t('newDS', 'Assessments') ?><?php echo Yii::t('newDS', ' Batch upload') ?></div>
            <div class="col-md-2 col-sm-12">
                <div class="list-group" id="classroom-status-list">
                    <a href="javascript:;" class="list-group-item status-filter" :class='list.id==classId?"active":""' v-for='(list,i) in classList' @click='classActive(list)'>{{list.title}}</a>
                </div>
            </div>
            <div class="col-md-10 col-sm-10">
                <div>
                    <button type="button" class="btn btn-default btn-sm mr20"  v-for='(item,key,index) in config.types' v-if="item.hide!=true" :class='key==type?"btn-primary":""'  @click='typeActive(key)' >{{item.title}}</button>
                </div>
                <p class='mt20'>
                    <label class="radio-inline" v-for='(list,key,id) in config.periods' @change='getTable(false)'>
                        <input type="radio" name="inlineRadioOptions" :value="key" v-model='period'>{{list.title}}
                    </label>
                </p>
                <div>
                <div class='mt20' v-if='classList.length!=0'>
                    <div class="alert alert-info col-md-5 col-sm-5" role="alert" v-if='tableList.length!=0'><?php echo Yii::t('newDS', 'Current Assessment Data:') ?> {{classTitle}} {{config.types[type].title}} {{config.periods[period].title}}</div>
                    <div class="alert alert-warning col-md-5 col-sm-5" role="alert" v-else><?php echo Yii::t("newDS", "Please select a class, Assessment type and period.");?></div>
                    <div class='col-md-7 col-sm-7 mt10' v-if='tableList.length!=0'>
                        <div class='pull-right'>
                            <span><?php echo Yii::t("newDS", "Batch Online");?></span>
                            <label class="switch ml5">
                                <input type="checkbox" :checked='allCheck?true:false' @change='batOnline("all",$event)'>
                                <span class="slider"></span>
                            </label>
                        </div>
                        
                    </div>
                </div>
                <div class='loading' v-if='initLoading'>
                    <span></span>
                </div>
                <table class="table table-bordered mt20"  v-if='tableList.length!=0'>
                    <thead>
                        <tr>
                            <th width='80'><?php echo Yii::t('newDS', 'Student') ?></th>
                            <th width='250'><?php echo Yii::t('newDS', 'Attachments') ?>
                            <div class='alert alert-warning flex mt10' v-if='PDF!=""'> 
                                <span class='glyphicon glyphicon-exclamation-sign' style='width:30px'></span> 
                                <div class='flex1 text-left' v-html='PDF'></div> 
                            </div></th>
                            <th  width='400'><?php echo Yii::t('newDS', 'Comment') ?></th>
                            <th  width='80'><?php echo Yii::t('global', 'Online') ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for='(list,index) in tableList'>
                            <th style='vertical-align: middle!important;'>
                                <img :src="list.photo"
                                class='media-object img-circle image' alt="">
                                <p class='mt10'>{{list.name}}</p>
                                <div class='text-muted font12'>{{list.childId}}</div>
                            </th>
                            <td class='text-center'  style='word-break:break-all;' >
                                <button type="button" :id="['pickfilesPhoto'+list.childId]" class="btn  btn-default  btn-sm" :disabled='list.disabledUpload' @click='getQiniuId(list,index)'>
                                <span class='glyphicon glyphicon-plus'></span> {{list.disabledUpload?'<?php echo Yii::t("newDS", "Uploading...");?>':'<?php echo Yii::t("newDS", "Add");?>'}}
                                </button>
                                <div class='flex mt10 imgBg' v-for='(item,idx) in list.attachments' :class='item.dragStatus?"dragCss":""'>
                                    <a style='width:20px;padding-top:2px'  class='glyphicon glyphicon-paperclip inline-block'> </a>
                                    <a target="_blank" class='flex1 text-left ml5' :href='item.url' v-if='!item.isEdit'>{{item.title}}</a>
                                    <span class='flex1' v-else><input type="input" class="form-control" v-model='editFile' :value='item.title'  @keyup.enter="saveFile(item,list)"  v-model='attachmentName' ></span>
                                    <span style='width:85px' class='text-right inline-block mt5 relative' v-if='!item.isEdit'>
                                        <span  class='dragStatus'
                                            draggable="true"
                                            @dragstart="handleDragStart($event, item, list)" 
                                            @dragover.prevent="handleDragOver($event, item, list)" 
                                            @dragenter="handleDragEnter($event, item, list)" 
                                            @dragend="handleDragEnd($event, item, list, index)" >
                                            <span 
                                                class='text-primary cur-p'
                                                onMouseOver="$(this).tooltip('show')" 
                                                data-toggle="tooltip" 
                                                data-placement="left"  
                                                title="<?php echo Yii::t("newDS", "可上下拖动");?>">
                                                <span class="glyphicon glyphicon-move"></span>
                                            </span>
                                        </span>
                                        <a href="javascript:;"  class='glyphicon glyphicon-edit ml10' @click='editFiles(item,list)'></a>
                                        <span class='glyphicon glyphicon-trash ml10' @click='delModel(item,list)'></span>
                                    </span>
                                    <span style='width:80px'  class='text-right inline-block mt5' v-else>
                                        <button type="button" class="btn btn-primary btn-xs" @click='saveFile(item,list)'><?php echo Yii::t("global", "Save");?></button>
                                        <button type="button" class="btn btn-default btn-xs" @click='item.isEdit=false'><?php echo Yii::t("global", "Cancel");?></button>
                                    </span>
                                </div>
                            </td>
                            <td class='text-center'>
                                <button type="button" class="btn btn-default mb10 btn-sm" aria-label="Left Align" @click='editComment(list)'> <?php echo Yii::t("newDS", "Edit Comment");?>  </button>
                                <p class='text-left mt5 relative '><span class='point'></span> <strong class='ml10'><?php echo Yii::t("newDS", "Visible to Staff Only");?></strong></p>
                                <div  class='mb10 ml10 text-left'  :class="list.staffshowTotal ? '' : 'textOVerThree'":ref='list.refStaff'  v-if='list.comment_to_staff!=""'>
                                    <div class='text-muted' v-html='list.comment_to_staff'></div>
                                    <a href='javascript:;' :class='list.staffexchangeButton?"showText":""' @click="showTotalIntro(list,'staff')" v-if="list.staffshowExchangeButton">
                                        {{list.staffexchangeButton ? '<?php echo Yii::t("newDS", "Expand");?>' : '<?php echo Yii::t("newDS", "Collapse");?>'}}
                                        <span class='glyphicon glyphicon-chevron-up' v-if='!list.staffexchangeButton'></span>
                                        <span class='glyphicon glyphicon-chevron-down' v-else></span>
                                    </a>
                                </div>      
                                <p class="text-muted text-left ml10" v-else>N/A</p>           
                                <p class='text-left mt15 relative'><span class='point'></span> <strong class='ml10'><?php echo Yii::t("newDS", "Visible to Parent");?></strong> </p>
                                <div class='mb10 ml10 text-left' :class="list.parentshowTotal ? '' : 'textOVerThree'" :ref='list.refParent' v-if='list.comment_to_parent!=""'>
                                
                                    <div class='text-muted' v-html='list.comment_to_parent'></div>

                                    <a href='javascript:;' :class='list.parentexchangeButton?"showText":""' @click="showTotalIntro(list,'parent')" v-if="list.parentshowExchangeButton">
                                        {{list.parentexchangeButton ? '<?php echo Yii::t("newDS", "Expand");?>' : '<?php echo Yii::t("newDS", "Collapse");?>'}}
                                        <span class='glyphicon glyphicon-chevron-up' v-if='!list.parentexchangeButton'></span>
                                        <span class='glyphicon glyphicon-chevron-down' v-else></span>
                                    </a>
                                </div>   
                                <p  class="text-muted text-left ml10"  v-else>N/A</p>           
                            </td>
                            <td class='text-center' style='vertical-align: middle!important;'>
                                <label class="switch" v-if='list.isNew==0'>
                                    <input type="checkbox" :checked='list.online==10?true:false' @change='batOnline(list,$event)'>
                                    <div class="slider"></div>
                                </label>
                            </td>
                        </tr>
                    
                    </tbody>
                </table>
                </div>
            </div>
        </div>
        <div class="modal fade" tabindex="-1" role="dialog" id='editCommentModal'>
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title"><?php echo Yii::t("newDS", "Edit Comment");?></h4>
                    </div>
                    <div class="modal-body">
                        <div class="media mb20">
                            <div class="pull-left">
                                <a href="#">
                                <img class="media-object img-circle image" :src="childData.photo" alt="...">
                                </a>
                            </div>
                            <div class="media-body mt15">
                                <h4 class="media-heading">{{childData.name}}</h4>
                                <div class='mt10'>{{classTitle}}</div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="exampleInputEmail1"><?php echo Yii::t("newDS", "Visible to Staff Only");?></label>
                            <textarea class="form-control" rows="5" :value='teacher'  v-model='teacher'></textarea>
                        </div>
                        <div class="form-group">
                            <label for="exampleInputPassword1"><?php echo Yii::t("newDS", "Visible to Parent");?></label>
                            <textarea  class="form-control" rows="5" :value='parent'  v-model='parent'></textarea>
                        </div>
                        <div class='clearfix'></div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                        <button type="button" class="btn btn-primary" @click='saveInfo()'><?php echo Yii::t("global", "Save");?></button>
                    </div>
                </div><!-- /.modal-content -->
            </div><!-- /.modal-dialog -->
        </div><!-- /.modal -->
        <div class="modal fade" id="deleteModal" tabindex="-1" role="dialog">
        <div class="modal-dialog  modal-sm" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t("newDS", "Delete");?></h4>
            </div>
            <div class="modal-body">     
                <?php echo Yii::t("newDS", "Confirm to delete this item?");?>    
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                <button type="button" class="btn btn-primary" @click='delFile()'><?php echo Yii::t("newDS", "Delete");?></button>
            </div>
            </div>
        </div>
    </div>
    </div>
</div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    
    var container = new Vue({
        el: "#container",
        data: {
            token:'',
            classList:[],
            config:{},
            classId:'',
            classTitle:'',
            copytableList:[],
            period:'',
            type:'',
            tableList:[],
            childData:{},
            teacher: '',
            parent: '',
            allCheck:false,
            editFile:'',
            qiniuUpId:'',
            qiniuList:{},
            uploader:[],
            initLoading:false,
            delItem:{},
            delList:[],
            dragItems:[],
            isShow:false,
            PDF:''
        },
        watch:{},
        created:function(){
            this.init()
        },
        methods: {
            typeActive(list){
                this.type=list
                this.PDF=this.config.types[list].guide
                this.getTable(false)
            },
            classActive(list){
                this.classId=list.id
                this.classTitle=list.title
                this.getTable(false)
            },
            init() {
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("yearClass") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {},
                    success: function(data) {
                        if (data.state == 'success') {
                            that.isShow=true
                            that.classList=data.data.classList
                            that.config=data.data.config
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    }
                })
            },
            getTable(load){
                if(this.classId=='' || this.type=='' || this.period==''){
                    return
                }
                let that=this
                if(!load){
                    this.initLoading=true
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("getAssessmentByClass") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        classId: this.classId,
                        type: this.type,
                        period: this.period
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            for(var i=0;i<data.data.length;i++){
                                data.data[i].refParent='parent'+i
                                data.data[i].refStaff='staff'+i
                                data.data[i].staffshowExchangeButton=false
                                data.data[i].staffexchangeButton=true
                                data.data[i].staffshowTotal=true
                                data.data[i].parentshowExchangeButton=false
                                data.data[i].parentexchangeButton=true
                                data.data[i].parentshowTotal=true
                                data.data[i].disabledUpload=false
                                if(data.data[i].attachments.length!=0){
                                    for(var j=0;j<data.data[i].attachments.length;j++){
                                        data.data[i].attachments[j].isEdit=false
                                        data.data[i].attachments[j].dragStatus=false
                                    }
                                }
                            }
                            that.tableList=data.data
                            that.$nextTick(function () {
                                that.showData()
                                if(!load){
                                    that.getQiniu()  
                                }
                                that.allOnline()  
                            })
                            that.initLoading=false
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.initLoading=false
                        }
                    }
                })
            },
            allOnline(){
                var list=[]
                for(var i=0;i<this.tableList.length;i++){
                    if(this.tableList[i].isNew==0){
                        list.push(this.tableList[i].online)
                    }
                }
                if(list.length!=0){
                    let xso=list.every(item=>item==10)
                    if(xso){
                        this.allCheck=true
                    }else{
                        this.allCheck=false
                    }
                }else{
                    this.allCheck=false
                }
                
            },
            showTotalIntro (list,type) {
                list[type+'showTotal'] = !list[type+'showTotal'];
                list[type+'exchangeButton']= !list[type+'exchangeButton'];
            },
            showData(){
                this.$nextTick(function () {
                    for(var j=0;j<this.tableList.length;j++){

                        let refParent=this.tableList[j].refParent
                        let refStaff=this.tableList[j].refStaff
                        if (this.$refs[refStaff]!=undefined && this.$refs[refStaff].length!=0) {
                            let descHeight = window.getComputedStyle(this.$refs[refStaff][0]).height.replace('px', '');
                            if (descHeight >60) {
                                Vue.set( this.tableList[j], 'staffshowExchangeButton', true);
                                Vue.set( this.tableList[j], 'staffexchangeButton', true);
                                Vue.set( this.tableList[j], 'staffshowTotal', false);
                            } else {
                                Vue.set( this.tableList[j], 'showTotal', true);
                                Vue.set( this.tableList[j], 'showExchangeButton', false);
                            }
                        }
                        if (this.$refs[refParent]!=undefined && this.$refs[refParent].length!=0) {
                            let descHeight = window.getComputedStyle(this.$refs[refParent][0]).height.replace('px', '');
                            if (descHeight >60) {
                                Vue.set( this.tableList[j], 'parentshowExchangeButton', true);
                                Vue.set( this.tableList[j], 'parentexchangeButton', true);
                                Vue.set( this.tableList[j], 'parentshowTotal', false);
                            } else {
                                Vue.set( this.tableList[j], 'parentshowTotal', true);
                                Vue.set( this.tableList[j], 'parentshowExchangeButton', false);
                            }
                        }
                    }
                })
            },
            getQiniu(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("uploadToken") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        classId:this.classId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            if(that.uploader) {
                                for(var i=0;i<that.uploader.length;i++){
                                that.uploader[i].destroy();
                                }
                            }
                            that.token=data.data
                            that.initQiniu()
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            getQiniuId(list,index){
                this.qiniuUpId=list.id
                this.qiniuList=list
            },
            initQiniu(){
                for(var i=0;i<this.tableList.length;i++){
                    config['token'] =this.token;
                    config['browse_button'] = 'pickfilesPhoto'+this.tableList[i].childId;
                    config['multipart_params'] = {'token':this.token,'x:id':this.tableList[i].id,'x:childId':this.tableList[i].childId,'x:period':this.period,'x:type':this.type};
                    var uploader=new plupload.Uploader(config);
                    this.uploader.push(uploader);
                    uploader.init();
                }
            },
            editComment(data){
                this.childData=data
                var reg = new RegExp("<br />", "g");
                this.teacher=data.comment_to_staff.replace(reg, '');
                this.parent=data.comment_to_parent.replace(reg, '');
                $('#editCommentModal').modal('show') 
            },
            saveInfo(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("saveComment") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        classId: this.classId,
                        type: this.type,
                        period: this.period,
                        id:this.childData.id,
                        childId: this.childData.childId,
                        teacher: this.teacher,
                        parent: this.parent
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            $('#editCommentModal').modal('hide') 
                            resultTip({
                                msg: data.state
                            });
                            that.getTable(true)
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    }
                })
            },
            batOnline(data,e){
                var datas;
                if(data=='all'){
                    var ids=[]
                    for(var i=0;i<this.tableList.length;i++){
                        if(this.tableList[i].isNew==0){
                            ids.push(this.tableList[i].id)
                        }
                    }
                    datas={
                        id:ids,
                        status:this.allCheck?20:10
                    }
                }else{
                    datas={
                        id:[data.id],
                        status:data.online==10?20:10
                    }
                    if(data.comment_to_parent=='' && data.comment_to_staff=='' && data.attachments.length==0){
                        resultTip({
                            error: 'warning',
                            msg: '<?php echo Yii::t("newDS", "Attachment or parent comment is required！");?>'
                        });
                        e.target.checked=false
                        return
                    }
                }
                if(datas.id.length==0){
                    resultTip({
                        error: 'warning',
                        msg: '<?php echo Yii::t("newDS", "No available entries found.");?>'
                    });
                    e.target.checked=false
                    this.allCheck=false
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("batOnline") ?>',
                    type: "post",
                    dataType: 'json',
                    data: datas,
                    success: function(data) {
                        if (data.state == 'success') {
                            if(datas.status==10){
                                resultTip({
                                    msg: data.data.success_num+'<?php echo Yii::t("newDS", "assessments have been online successfully.");?>'
                                });
                            }else{
                                resultTip({
                                    msg: data.state
                                });
                            }
                            that.getTable(true)
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    }
                })
            },
            saveFile(item,list){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("renameAttachment") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:list.id,
                        aid:item.aid,
                        title:this.editFile
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            Vue.set(item, 'title', that.editFile);
                            Vue.set(item, 'isEdit', false);
                            resultTip({
                                msg: data.state
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            editFiles(item,list){
                list.attachments.forEach((items,index) => {
                    items.isEdit=false;
                })
                item.isEdit=true;
                this.editFile=item.title
            },
            delModel(item,list){
                this.delItem=item
                this.delList=list
                $('#deleteModal').modal('show')
            },
            delFile(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("deleteAttachment") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:this.delList.id,
                        aid:this.delItem.aid
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.delList.attachments.forEach((items,index) => {
                                if (that.delItem.aid==items.aid) {
                                    that.delList.attachments.splice(index, 1)
                                } 
                            })
                            $('#deleteModal').modal('hide')
                            that.getTable(true)
                            resultTip({
                                msg: data.state
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            handleDragStart(e, item, list) {
                this.dragging = item;
                this.dragItems =JSON.parse(JSON.stringify(list.attachments)) 
            },
            handleDragEnd(e, item, list, index) {
                this.dragging = null
                var fileList=[];
                var prev=[];
                list.attachments.forEach((item,index) => {
                    fileList.push(item.aid)
                })
                this.dragItems.forEach((item,index) => {
                    prev.push(item.aid)
                })
                if(prev.toString() == fileList.toString()){
                    list.attachments.forEach((item,index) => {
                        Vue.set(item, 'dragStatus', false);
                    })
                    return
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("sortAttachments") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        sort:fileList,
                        id:list.id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg: data.state
                            });
                            list.attachments.forEach((item,index) => {
                                Vue.set(item, 'dragStatus', false);
                            })
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            handleDragOver(e,item,list) {
                e.dataTransfer.dropEffect = 'move' // e.dataTransfer.dropEffect="move";//在dragenter中针对放置目标来设置!
                
            },
            handleDragEnter(e, item, list) {
                e.dataTransfer.effectAllowed = "move" //为需要移动的元素设置dragstart事件
                if(item === this.dragging) {
                    return
                }
                
                const newItems = [...list.attachments]
                const src = newItems.indexOf(this.dragging)
                const dst = newItems.indexOf(item)
                newItems.splice(dst, 0, ...newItems.splice(src, 1))
                list.attachments = newItems
                list.attachments.forEach((item,index) => {
                    Vue.set(item, 'dragStatus', false);
                })
                
                Vue.set(list.attachments[dst], 'dragStatus', true);
            },
        }
    })
    var config = {
        runtimes: 'html5,flash,html4', //上传模式,依次退化
        container: 'container', //上传区域DOM ID，默认是browser_button的父元素，
        flash_swf_url: '/js/plupload/Moxie.swf', // flash的相对地址
        auto_start: true, //选择文件后自动上传，若关闭需要自己绑定事件触发上传
        multi_selection: false, //不允许多选
        url:'https://upload-z1.qiniup.com',
        
        init: {
            'FilesAdded': function(up, files) {
                up.start();
                container.qiniuList.disabledUpload=true
            },
            BeforeUpload: function(up, file) {
            },
            'FileUploaded': function(up, file, info) {
                var response = eval('('+info.response+')');
                if(info.status == 200){   
                    container.qiniuList.disabledUpload=false
                    container.getTable(true)
                }
            },
            'Error': function(up, err, errTip) {
                resultTip({msg: err.message, error: 1});
                container.qiniuList.disabledUpload=false

            },
            'UploadComplete': function(up, file) {
                //队列文件处理完毕后,处理相关的事情
                container.qiniuList.disabledUpload=false

            }
        }
    };
</script>