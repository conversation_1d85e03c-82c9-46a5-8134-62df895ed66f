<?php

// uncomment the following to define a path alias
// Yii::setPathOfAlias('local','path/to/local-folder');

// This is the main Web application configuration. Any writable
// CWebApplication properties can be configured here.
return array(
	'sourcePath'=>dirname(__FILE__).DIRECTORY_SEPARATOR.'..'.DIRECTORY_SEPARATOR.'..',
	'messagePath'=>dirname(__FILE__).DIRECTORY_SEPARATOR.'..'.DIRECTORY_SEPARATOR.'messages',
    'sourceLanguage'=>'en_us',
	'languages'=>array('zh_cn'),
	'fileTypes'=>array('php'),
    'overwrite'=>true,
    'translator'=>'Yii::t',
	'exclude'=>array(
		'.svn',
		'yiilite.php',
		'yiit.php',
        'yiic.php',
        'yiic.sample.php',
        '/config',
		'/i18n/data',
		'/messages',
		'/vendors',
		'/web/js',
		'/tests',
		'/runtime',
		'/data',
	),
);