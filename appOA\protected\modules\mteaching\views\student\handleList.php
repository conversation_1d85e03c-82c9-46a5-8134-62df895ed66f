<style>
    .listCom{
        border-radius: 8px;
        border: 1px solid #E8EAED;
        padding:24px 0 24px 24px
    }
    .labelbg{
        padding:2px;
        background: #F7F7F8;
        border-radius: 2px;
        border: 1px solid #D9D9D9;
    }
    .supportPlan{
        background: #FFFFFF;
        border: 1px solid #E8EAED;
        border-radius:4px;
    }
    
    .handlingResults{
        background: rgba(77,136,210,0.06);
        border-radius: 4px;
        border: 1px solid #E8EAED;
    }
    .handlingTitle{
        background: linear-gradient(90deg, #84BDEB 0%, #4D88D2 100%);
        border-radius: 4px 4px 0px 0px;
        border: 1px solid #E8EAED;
        padding:16px 0;
        color: #FFFFFF;
        text-align:center
    }
    .labelTag{
        width: 18px;
        height: 18px;
        background: #666666;
        border-radius: 9px;
        border: 1px solid #FFFFFF;
        display:inline-block;
        text-align: center;
        line-height: 17px;
        color: #fff;
        font-size: 12px;
    }
    .labelTagRed{
        background:#D9534F
    }
    .plr0{
        padding-left:0;
        padding-right:0
    }    
</style>
<div class="" id='container' v-cloak >
    <div v-if='typeData.typeList'>
        <div>
            <span class="mr20 cur-p" v-for='(list,index) in typeData.typeList' :class='showType==list.type?"btn btn-primary":""' @click='getData(list.type)'>
                <span>{{list.title}} </span>
                <span class="labelTag labelTagRed" :class='showType==list.type?"labelTagRed":""'>{{list.num}} </span>
            </span>
        </div>
        <div class='bgGrey mb20 mt16 plr0'>
            <div class='col-lg-3 col-md-4 col-sm-12' v-for='(list,index) in typeData.staff[showType]'>
                <div class="flex mt5 mb5">
                    <div class="">
                        <img :src="typeData.staff_info[list].photoUrl" data-holder-rendered="true" class="avatar">
                    </div> 
                    <div class="flex1 ml10 text_overflow">
                        <div class="mt8 font14 color3 text_overflow"> {{typeData.staff_info[list].name}} <span class='labelbg' v-if='list==config.user_data.uid'>我</span></div> 
                        <div class="color6 mt4 text_overflow">{{typeData.staff_info[list].hrPosition}}</div>
                    </div>
                </div>
            </div>
            <div class='clearfix'></div>
        </div>
    </div>
    <div v-else-if='typeData.staff_info'>
    <div class="alert alert-warning" role="alert"><?php echo Yii::t("ptc", "No Data"); ?></div>
    </div>
    <div v-if='dataList.list'>
        <div class='loading'  v-if='indexLoading'>
            <span></span>
        </div>
        <el-table
            :header-cell-style="{background:'#fafafa',color:'#333'}"
            :data="dataList.list"
            style="width: 100%"
            :default-sort = "{prop: 'created_at', order: 'descending'}"
            @sort-change="sort_change" 
            >
            <el-table-column
            prop="child"
            label="<?php echo Yii::t("ptc", "Student"); ?>"
            min-width="250">
                <template slot-scope="scope">
                    <div v-if='scope.row.child.length==1'>
                        <div class="media" v-for='(list,index) in scope.row.child'>
                            <div class="media-left pull-left media-middle relative">
                                <img :src="dataList.child_list[list.id].avatar" data-holder-rendered="true" class="avatar">
                            </div> 
                            <div class="pull-left media-middle">
                                <div class="font14 color3"> {{dataList.child_list[list.id].name}}
                                    <span  :class='list.tag==2?"tagLabelYellow":list.tag==3?"tagLabel":""'>{{showTitle(list.tag,'ISP_type')}}</span>
                                </div> 
                                <div class="color6 font12">{{dataList.child_list[list.id].className}}</div>
                            </div>
                        </div>
                    </div>
                    <div v-else>
                        <img :src="dataList.child_list[list.id].avatar" v-for='(list,index) in scope.row.child' data-holder-rendered="true" class="avatar32 mb8 mr4">
                        <el-popover
                            placement="bottom"
                            trigger="click">
                            <div class="media" v-for='(list,index) in scope.row.child'>
                                <div class="media-left pull-left media-middle relative">
                                    <img :src="dataList.child_list[list.id].avatar" data-holder-rendered="true" class="avatar">
                                </div> 
                                <div class="pull-left media-middle">
                                    <div class="font12 color6"> {{dataList.child_list[list.id].name}}
                                        <span  :class='list.tag==2?"tagLabelYellow":list.tag==3?"tagLabel":""'>{{showTitle(list.tag,'ISP_type')}}</span>
                                    </div> 
                                    <div class="color6 font12">{{dataList.child_list[list.id].className}}</div>
                                </div>
                            </div>
                            <span class='bluebg font14 cur-p' slot="reference">共{{scope.row.child.length}}人</span>
                        </el-popover>
                    </div>
                </template>
            </el-table-column>
            <el-table-column
            prop="created_by"
            min-width="150"
            label="提请人">
            <template slot-scope="scope">
                <span class='color3'>{{dataList.staff_info[scope.row.created_by].name}}</span>
            </template>
            </el-table-column>
            <el-table-column
            prop="created_at"
            min-width="150"
            sortable='custom'
            label="提交时间">
                <template slot-scope="scope">
                    <el-tooltip class="item" effect="dark" :content="scope.row.created_date" placement="top">
                        <span class='color3 font14'>{{scope.row.diff_date}}</span>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column
            prop="office_user"
            label="处理状态"
            min-width="300">
                <template slot-scope="scope">
                    <div class='flex'>
                        <div class='text-center minWidth mr50' v-for='(list,index) in scope.row.nodes_order'>
                            <div v-if='scope.row.nodes_by_node[list].status==1'>
                                <div class='relative'>
                                    <div class='firstBorder' v-if='scope.row.nodes_order.length>1 && index+1<scope.row.nodes_order.length'></div>
                                    <img :src="dataList.staff_info[scope.row.nodes_by_node[list].updated_by].photoUrl" alt="" class='avatar32'>
                                </div>
                                <div class='color3 font14'>{{scope.row.nodes_order_title[index]}}</div>
                                <div class='color6 font12'>
                                <el-tooltip class="item" effect="dark"  placement="top">
                                <div slot="content">由{{dataList.staff_info[scope.row.nodes_by_node[list].updated_by].name}}处理<br/>{{scope.row.nodes_by_node[list].updated_at}}</div>
                                <div class='color6'>{{scope.row.nodes_by_node[list].diff_date}}</div>
                                </el-tooltip>
                                </div>
                            </div>
                            <div v-else>
                                <div>
                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/pending.png' ?>" alt="" class='avatar32'>
                                </div>
                                <div class='color3 font14'>{{scope.row.nodes_order_title[index]}}</div>
                                <div class='font12 waitingColor'>等待处理</div>
                            </div>
                        </div>
                    </div>
                </template>     
            </el-table-column>
            <el-table-column
            prop="office_user"
            label="<?php echo Yii::t("newDS", "Actions"); ?>"
            width="150">
                <template slot-scope="scope">
                    <el-button type="text" @click='showDetails(scope.row._id)' class='font14 bluebg'>去处理</el-button>
                </template>
            </el-table-column>
        </el-table>
        <nav aria-label="Page navigation" v-if='itemList.count_page &&  itemList.count_page>1'  class="text-left ml10">
            <ul class="pagination">
                <li v-if='itemList.current_page >1'>
                    <a href="javascript:void(0)" @click="prev(itemList.current_page)" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
                <li v-else class="disabled">
                    <a aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
                <li v-for='(data,index) in itemList.count_page' :class="{ active:index+1==itemList.current_page }">
                    <a href="javascript:void(0)" @click="plus(index)">{{index+1}}</a>
                </li>
                <li v-if='itemList.current_page <itemList.count_page'>
                    <a href="javascript:void(0)" @click="plus(itemList.current_page)" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
                <li v-else class="disabled">
                    <a aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
            </ul>
        </nav>
    </div>
     <!-- 详情 -->
     <div class="modal fade" id="contentModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static" style="overflow-y: auto;">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close" ><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "Detail"); ?></h4>
                </div>
                <div class="modal-body p0 relative" v-if='contentList.behavior'>
                    <div><?php $this->renderPartial("detail", array('type' => 'processing'));?></div>
                    <div class='clearfix'></div>
                </div>
            </div>
        </div>
    </div>
    <!-- 认定非行为问题和最终处理 -->
    <div class="modal fade"  id="finalResultModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel"  aria-labelledby="wantSayModalLabel"  data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">{{resultType=="isp" || resultType=="grade"?"认定非行为问题":"填写最终认定结果"}}</h4>
            </div>
            <div class="modal-body p24">
                <div class='font14'>
                    <div v-if='resultType=="office"'>                       
                        <div class='flex align-items'>
                            <span class='fontBold color3 flex1'>认定学生</span> 
                            <template>
                                <el-select
                                        v-model="stuVal"
                                        filterable
                                        remote
                                        clearable
                                        class='inline-input flex1 formControl'
                                        reserve-keyword
                                        placeholder="<?php echo Yii::t("directMessage", "添加其他学生"); ?>"
                                        :remote-method="stuRemoteMethod"
                                        prefix-icon="el-icon-search"
                                        @change='addStu'
                                        :loading="loading">
                                    <el-option
                                            v-for="item in stuOption"
                                            :key="item.id"
                                            :label="item.name"
                                            class='optionSearch mb8'
                                            :value="item.id">
                                        <div class="media">
                                            <div class="media-left pull-left media-middle">
                                                <a href="javascript:void(0)">
                                                    <img :src="item.avatar" data-holder-rendered="true" class="media-object img-circle avatar">
                                                </a>
                                            </div>
                                            <div class="media-body mt5 media-middle">
                                                <h4 class="media-heading font14 color3 text_overflow">{{item.name}}</h4>
                                                <div class="text-muted text_overflow font12">{{ item.class_name }}</div>
                                            </div>
                                        </div>
                                    </el-option>
                                </el-select>
                            </template>
                        </div>
                        <div class='flex align-items waitingColor'><span class='glyphicon glyphicon-info-sign'></span> <span class='ml5'>若学生的处理结果不同，需要对学生单个认定</span> </div>
                        <div  class='mt8'>
                            <div class='row' v-if='contentList.behavior'>
                                <div class='col-md-6 mt10' v-for='(list,index) in contentList.behavior.child'>
                                    <div class='bgGrey p8 height65'>
                                        <label class="checkbox-inline flex">
                                            <input type="checkbox"  :value="list.id" class='mt16' v-model='resultData.student_ids'> 
                                            <div class="media flex1 m0 authorizedMedia" >
                                                <div class="media-left pull-left media-middle">
                                                    <a href="javascript:void(0)">
                                                        <img :src="contentList.student_info_list[list.id].avatar" data-holder-rendered="true" class="avatar">
                                                    </a>
                                                </div>
                                                <div class="media-body media-middle">
                                                    <div class="lineHeight20  text-primary">
                                                        <span class='font14 color3 nowrap'>{{contentList.student_info_list[list.id].name}} </span>
                                                        <span :class='list.tag==2?"tagLabelYellow":list.tag==3?"tagLabel":""'>{{showTitle(list.tag,'ISP_type')}}</span>
                                                    </div>
                                                    <div class="font12 color6 nowrap">{{contentList.student_info_list[list.id].className}}</div>
                                                </div>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                                <div class='col-md-6 mt10' v-for='(list,index) in addChildList' :key='list.id'>
                                    <div class='bgGrey p8 height65'>
                                        <label class="checkbox-inline flex">
                                            <input type="checkbox"  :value="list.id" class='mt16' v-model='resultData.student_ids'> 
                                            <div class="media flex1 m0 authorizedMedia" >
                                                <div class="media-left pull-left media-middle">
                                                    <a href="javascript:void(0)">
                                                        <img :src="list.avatar" data-holder-rendered="true" class="avatar">
                                                    </a>
                                                </div>
                                                <div class="media-right pull-right mt15 text-right">
                                                    <span class='el-icon-circle-close font16' @click='delChild(index,list.id)'></span>
                                                </div>
                                                <div class="media-body media-middle">
                                                    <div class="lineHeight20  text-primary">
                                                        <span class='font14 color3 nowrap'>{{list.name}} </span>
                                                        <span v-for='(item,ind) in list.label' >
                                                            <span v-if='item.flag==2 || item.flag==3' :class='item.flag==2?"tagLabelYellow":item.flag==3?"tagLabel":""'>{{item.name}}</span>
                                                        </span>
                                                    </div>
                                                    <div class="font12 color6 nowrap">{{list.className}}</div>
                                                </div>
                                                
                                            </div>
                                        </label>
                                    </div>
                                </div>
                                
                            </div>
                        </div>
                        <div class='fontBold color3 mt24'>认定结果</div>
                        <div  class='mt8'>
                            <label class="radio-inline" v-for='(list,index) in config.fact'>
                                <input type="radio" name="inlineRadioOptions" v-model='resultData.identified' :value="list.value"> {{list.title}}
                            </label>
                        </div>
                        <div v-if='resultData.identified==1'>   
                            <div class='fontBold color3 mt24'>最终决定</div>
                            <div class='row'>
                                <div class='col-md-3 col-sm-12'  v-for='(list,index) in config.actions'>
                                    <div class="checkbox m0 mt5" >
                                        <label>
                                            <input type="checkbox" v-model='resultData.actions'  :value="list.value" >{{list.title}}
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div  v-if='resultType=="isp" || resultType=="grade"'>
                        <div class='flex align-items'>
                            <span class='fontBold color3 flex1'>认定学生</span> 
                        </div>
                        <div class='row'>
                        <div class='col-md-6 mt10' v-for='(list,index) in contentList.behavior.child'>
                            <div class='height65'>
                                <div class="media flex1 m0 authorizedMedia" >
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)">
                                            <img :src="contentList.student_info_list[list.id].avatar" data-holder-rendered="true" class="avatar">
                                        </a>
                                    </div>
                                    <div class="media-body media-middle">
                                        <div class="lineHeight20  text-primary">
                                            <span class='font14 color3 nowrap'>{{contentList.student_info_list[list.id].name}} </span>
                                            <span :class='list.tag==2?"tagLabelYellow":list.tag==3?"tagLabel":""'>{{showTitle(list.tag,'ISP_type')}}</span>
                                        </div>
                                        <div class="font12 color6 nowrap">{{contentList.student_info_list[list.id].className}}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        </div>
                        <div class='fontBold color3'>认定结果</div>
                        <div  class='mt8'>
                            <div class='flex1 color3'><span class="label label-success" >不属于行为问题</span></div>
                        </div>
                    </div>
                    <div class='fontBold color3 mt24'>联系家长</div>
                    <div class='row'>
                        <div class='col-md-3 col-sm-12'  v-for='(list,index) in config.contact_parents'>
                            <div class="checkbox m0 mt5" >
                                <label>
                                    <input type="checkbox" v-model='resultData.contact_parents'  :value="list.value" >{{list.title}}
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class='otherBg mt10' v-if='showIndexOf("actions")'>
                        <input type="text" class="form-control" placeholder="其他"  v-model='resultData.actions_other' >
                    </div>
                    <div class='fontBold color3 mt24'>面谈时长</div>
                    <div class='mt8'>
                        <el-time-picker
                            v-model="resultData.duration"
                            value-format="HH:mm"
                            format="H 小时 m 分"
                            placement="bottom-start"
                            placeholder="面谈时长">
                        </el-time-picker>
                    </div>
                    <div class='fontBold color3 mt24'>处理意见</div>
                    <div  class='mt8'><textarea class="form-control" placeholder="请输入" v-model='resultData.remark' rows="3"></textarea></div>
                    <div class='flex mb10 align-items mt24 '>
                        <div class='fontBold color3 font14'><?php echo Yii::t("curriculum", "Attachments"); ?></div>
                        <div class='flex1 ml10'>
                            <el-button type="text" icon="el-icon-circle-plus-outline" id='finalResultAddFile' class='font14 bluebg'>上传</el-button>
                        </div>
                    </div>
                    <div>
                        <div class='' v-if='attachments.img.length>0'>
                            <div class='imgData mr8'  v-for='(list,i) in attachments.img'>
                                <div v-if="list.types=='1'">
                                    <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Uploading");?></div>
                                </div>
                                <div v-else>
                                    <img class='imgList' @click='uploadShowImg(list.file_key)'  :src="list.file_key" alt="">
                                    <span aria-hidden="true" class='closeImg'  @click.stop='delImg("img",list,i)'>×</span>
                                </div>
                            </div>
                            <template v-if='loadingType==1'>
                                <div class='imgData mr8'  v-for='(list,i) in loadingList'>
                                    <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Upload queue");?></div>
                                </div>
                            </template>
                        </div>
                        <div>
                            <div class='mt16' v-if='attachments.other.length>0'>
                                <div class='flex uploadFile' v-for='(list,index) in attachments.other'>
                                    <span class='glyphicon glyphicon-paperclip mr8'></span>
                                    <a target="_blank" class='flex1'  style='line-height:26px' :href='list.file_key' v-if='!list.isEdit'>{{list.title}}</a>
                                    <span class='flex1' v-else><input type="input" class='inputStyle'  @keyup.enter="saveFile(list,index)"  v-model='attachmentName' ></span>
                                    <span v-if="list.types=='1'"></span>
                                    <template v-else>
                                        <template v-if='!list.isEdit'>
                                            <span class='glyphicon glyphicon-edit icon mr16' v-if='list.file_key!=""' @click.stop='list.isEdit=true,attachmentName=list.title'></span>
                                            <span class='glyphicon glyphicon-trash icon' v-if='list.file_key!=""' @click.stop='delImg("link",list,index)'></span>
                                        </template>
                                        <span style='width:90px'  class='text-right inline-block' v-else>
                                            <button type="button" class="btn btn-primary btn-xs" @click='saveFile(list,index)'><?php echo Yii::t("global", "Save");?></button>
                                            <button type="button" class="btn btn-default btn-xs" @click='list.isEdit=false'><?php echo Yii::t("global", "Cancel");?></button>
                                        </span>
                                    </template>
                                </div>
                            </div>
                            <template v-if='loadingType==2'>
                                <div class='flex uploadFile'  v-for='(list,i) in loadingList'>
                                    <span class='glyphicon glyphicon-paperclip mr8'></span>
                                    <a  class='flex1'  style='line-height:26px' > <?php echo Yii::t("directMessage", "Upload queue");?></a>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                <button type="button" class="btn btn-primary"  v-if='resultType=="office"' @click='saveOffice("result")'><?php echo Yii::t("global", "OK"); ?></button>
                <button type="button" class="btn btn-primary" v-if='resultType=="isp"' @click='saveTeacher("result")'><?php echo Yii::t("global", "OK"); ?></button>
                <button type="button" class="btn btn-primary" v-if='resultType=="grade"' @click='saveGrade("result")'><?php echo Yii::t("global", "OK"); ?></button>
            </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
    <!-- 重新认定 和 认定其他学生-->
    <div class="modal fade"  id="afreshResultModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel"  aria-labelledby="wantSayModalLabel"  data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">{{editStatus==2?"认定其他学生":"重新认定"}}</h4>
            </div>
            <div class="modal-body p24 font14">
                <div v-if='editStatus!=0'>
                    <div v-if='editStatus==2'>
                        <div class='flex align-items'>
                            <span class='fontBold color3 flex1'>认定学生</span> 
                            <template>
                                <el-select
                                        v-model="stuVal"
                                        filterable
                                        remote
                                        clearable
                                        class='inline-input flex1 formControl'
                                        reserve-keyword
                                        placeholder="<?php echo Yii::t("directMessage", "学生姓名"); ?>"
                                        :remote-method="stuRemoteMethod"
                                        prefix-icon="el-icon-search"
                                        @change='addStu'
                                        :loading="loading">
                                    <el-option
                                            v-for="item in stuOption"
                                            :key="item.id"
                                            :label="item.name"
                                            class='optionSearch mb8'
                                            :value="item.id">
                                        <div class="media">
                                            <div class="media-left pull-left media-middle">
                                                <a href="javascript:void(0)">
                                                    <img :src="item.avatar" data-holder-rendered="true" class="media-object img-circle avatar">
                                                </a>
                                            </div>
                                            <div class="media-body mt5 media-middle">
                                                <h4 class="media-heading font14 color3 text_overflow">{{item.name}}</h4>
                                                <div class="text-muted text_overflow font12">{{ item.class_name }}</div>
                                            </div>
                                        </div>
                                    </el-option>
                                </el-select>
                            </template>
                        </div>
                        <div class='flex align-items waitingColor'><span class='glyphicon glyphicon-info-sign'></span> <span class='ml5'>若学生的处理结果不同，需要对学生单个认定</span> </div>
                        <div  class='mt8'>
                            <div class='row' v-if='contentList.behavior'>
                                <div class='col-md-6 mt10' v-for='(list,index) in contentList.behavior.child'>
                                    <div class='bgGrey p8 height65'>
                                        <label class="checkbox-inline flex">
                                            <input type="checkbox"   class='mt16' v-if='showDisabled(list.id)' disabled='true' > 
                                            <input type="checkbox"  :value="list.id" class='mt16' v-if='!showDisabled(list.id)' v-model='resultData.student_ids'> 
                                            <div class="media flex1 m0 authorizedMedia" >
                                                <div class="media-left pull-left media-middle">
                                                    <a href="javascript:void(0)">
                                                        <img :src="contentList.student_info_list[list.id].avatar" data-holder-rendered="true" class="avatar">
                                                    </a>
                                                </div>
                                                <div class="media-right pull-right mt15 text-right">
                                                    <span class='color6' v-if='showDisabled(list.id)'>已认定</span>
                                                </div>
                                                <div class="media-body media-middle">
                                                    <div class="lineHeight20  text-primary">
                                                        <span class='font14 color3 nowrap'>{{contentList.student_info_list[list.id].name}} </span>
                                                        <span :class='list.tag==2?"tagLabelYellow":list.tag==3?"tagLabel":""'>{{showTitle(list.tag,'ISP_type')}}</span>
                                                    </div>
                                                    <div class="font12 color6 nowrap">{{contentList.student_info_list[list.id].className}}</div>
                                                </div>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                                <div class='col-md-6 mt10' v-for='(list,index) in addChildList' :key='list.id'>
                                    <div class='bgGrey p8 height65'>
                                        <label class="checkbox-inline flex">
                                            <input type="checkbox"  :value="list.id" class='mt16' v-model='resultData.student_ids'> 
                                            <div class="media flex1 m0 authorizedMedia" >
                                                <div class="media-left pull-left media-middle">
                                                    <a href="javascript:void(0)">
                                                        <img :src="list.avatar" data-holder-rendered="true" class="avatar">
                                                    </a>
                                                </div>
                                                <div class="media-right pull-right mt15 text-right">
                                                    <span class='el-icon-circle-close font16' @click='delChild(index,list.id)'></span>
                                                </div>
                                                <div class="media-body media-middle">
                                                    <div class="lineHeight20  text-primary">
                                                        <span class='font14 color3 nowrap'>{{list.name}} </span>
                                                        <span v-for='(item,ind) in list.label' >
                                                            <span v-if='item.flag==2 || item.flag==3' :class='item.flag==2?"tagLabelYellow":item.flag==3?"tagLabel":""'>{{item.name}}</span>
                                                        </span>
                                                    </div>
                                                    <div class="font12 color6 nowrap">{{list.className}}</div>
                                                </div>
                                                
                                            </div>
                                        </label>
                                    </div>
                                </div>
                                
                            </div>
                        </div>
                    </div>
                    <div  v-if='editStatus==1'>
                        <div class='row' v-if='reaffData.child'>
                            <div class='col-md-6 mt10'>
                                <div class="media flex1 m0" >
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)">
                                            <img :src="contentList.student_info_list[reaffData.child.id].avatar" data-holder-rendered="true" class="avatar">
                                        </a>
                                    </div>
                                    <div class="media-body pt4 media-middle">
                                        <div class="lineHeight20  text-primary">
                                            <span class='font14 color3 nowrap'>{{contentList.student_info_list[reaffData.child.id].name}} </span>
                                        </div>
                                        <div class="font12 color6 nowrap">{{contentList.student_info_list[reaffData.child.id].className}}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class='fontBold color3 mt24'>认定结果</div>
                    <div  class='mt8'>
                        <div  >
                        <label class="radio-inline" v-for='(list,index) in config.fact'>
                            <input type="radio" name="inlineRadioOptions" v-model='resultData.identified' :value="list.value"> {{list.title}}
                        </label>
                        </div>
                    </div>
                    <div v-if='resultData.identified==1'>                    
                        <div class='fontBold color3 mt24'>最终决定</div>
                        <div class='row'>
                            <div class='col-md-3 col-sm-12'  v-for='(list,index) in config.actions'>
                                <div class="checkbox m0 mt5" >
                                    <label>
                                        <input type="checkbox" v-model='resultData.actions'  :value="list.value" >{{list.title}}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class='fontBold color3 mt24'>联系家长</div>
                    <div class='row'>
                        <div class='col-md-3 col-sm-12'  v-for='(list,index) in config.contact_parents'>
                            <div class="checkbox m0 mt5" >
                                <label>
                                    <input type="checkbox" v-model='resultData.contact_parents'  :value="list.value" >{{list.title}}
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class='otherBg mt10' v-if='showIndexOf("actions")'>
                        <input type="text" class="form-control" placeholder="其他"  v-model='resultData.actions_other' >
                    </div>
                    <div class='fontBold color3 mt24'>面谈时长</div>
                    <div class='mt8'>
                        <el-time-picker
                            v-model="resultData.duration"
                            value-format="HH:mm"
                            format="H 小时 m 分"
                            placement="bottom-start"
                            placeholder="面谈时长">
                        </el-time-picker>
                    </div>
                    <div class='fontBold color3 mt24'>处理意见</div>
                    <div  class='mt8'><textarea class="form-control" placeholder="请输入" v-model='resultData.remark' rows="3"></textarea></div>
                    <div class='flex mb10 align-items mt24'>
                        <div class='fontBold color3 font14'><?php echo Yii::t("curriculum", "Attachments"); ?></div>
                        <div class='flex1 ml10'>
                            <el-button type="text" icon="el-icon-circle-plus-outline" id='afreshAddFile' class='font14 bluebg'>上传</el-button>

                        </div>
                    </div>
                    <div>
                        <div class='' v-if='attachments.img && attachments.img.length>0'>
                            <div class='imgData mr8'  v-for='(list,i) in attachments.img'>
                                <div v-if="list.types=='1'">
                                    <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Uploading");?></div>
                                </div>
                                <div v-else>
                                    <img class='imgList' @click='uploadShowImg(list.file_key)'  :src="list.file_key" alt="">
                                    <span aria-hidden="true" class='closeImg' @click.stop='delImg("img",list,i)'>×</span>
                                </div>
                            </div>
                            <template v-if='loadingType==1'>
                                <div class='imgData mr8'  v-for='(list,i) in loadingList'>
                                    <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Upload queue");?></div>
                                </div>
                            </template>
                        </div>
                        <div>
                            <div class='mt16' v-if='attachments.other && attachments.other.length>0'>
                                <div class='flex uploadFile' v-for='(list,index) in attachments.other'>
                                    <span class='glyphicon glyphicon-paperclip mr8'></span>
                                    <a target="_blank" class='flex1'  style='line-height:26px' :href='list.file_key' v-if='!list.isEdit'>{{list.title}}</a>
                                    <span class='flex1' v-else><input type="input" class='inputStyle'  @keyup.enter="saveFile(list,index)"  v-model='attachmentName' ></span>
                                    <span v-if="list.types=='1'"></span>
                                    <template v-else>
                                        <template v-if='!list.isEdit'>
                                            <span class='glyphicon glyphicon-edit icon mr16' v-if='list.file_key!=""' @click.stop='list.isEdit=true,attachmentName=list.title'></span>
                                            <span class='glyphicon glyphicon-trash icon' v-if='list.file_key!=""' @click.stop='delImg("link",list,index)'></span>
                                        </template>
                                        <span style='width:90px'  class='text-right inline-block' v-else>
                                            <button type="button" class="btn btn-primary btn-xs" @click='saveFile(list,index)'><?php echo Yii::t("global", "Save");?></button>
                                            <button type="button" class="btn btn-default btn-xs" @click='list.isEdit=false'><?php echo Yii::t("global", "Cancel");?></button>
                                        </span>
                                    </template>
                                </div>
                            </div>
                            <template v-if='loadingType==2'>
                                <div class='flex uploadFile'  v-for='(list,i) in loadingList'>
                                    <span class='glyphicon glyphicon-paperclip mr8'></span>
                                    <a  class='flex1'  style='line-height:26px' > <?php echo Yii::t("directMessage", "Upload queue");?></a>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                <button type="button" class="btn btn-primary" @click='afreshOffice()' v-if='editStatus==2'><?php echo Yii::t("global", "OK"); ?></button>
                <button type="button" class="btn btn-primary" @click='afreshReaff()' v-if='editStatus==1'><?php echo Yii::t("global", "OK"); ?></button>
            </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
    <div><?php $this->renderPartial("sendModal");?></div>
    <div><?php $this->renderPartial("transferModal");?></div>
</div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    var config = <?php echo CJSON::encode($config)?>;
    var height=document.documentElement.clientHeight;
    console.log(<?php echo CJSON::encode($config)?>)
    console.log(height)
    var container = new Vue({
        el: "#container",
        data: {
            search:'',
            height:height-220,
            config:config,
            teacherUid:'',
            studyTeacherUid:'',
            options:[],
            studyOptions:[],
            loading:false,
            dataList:{},
            contentList:{},
            time:'',
            principal_processing:'',
            remark:'',
            duration:'00:00',
            help:false,
            officeData:{},
            gradeData:{},
            resultData:{},
            ispData:{},
            planData:{},
            viewType:'',
            sendType:'',
            appendComment:'',
            appendText:false,
            refill:false,
            showType:'',
            typeData:{},
            detail:{},
            order: "descending",
            orderProp:"created_at",
            pageNum:1,
            itemList:{},
            qiniuToken:'',
            loadingType:0,
            loadingList:[],
            handId:'',
            uploader:[],
            stuVal:'',
            stuOption:[],
            showBehavior:true,
            showStudy:true,
            attachments:{
                img:[],
                other:[]
            },
            addChildList:[],
            resultType:"",
            editStatus:0,
            reaffData:{},
            followItem:{},
            followData:{
                remark:'',
                date:'',
                time:''
            },
            followType:'',
            sendItem:{},
            classTeacher:{},
            teacher_ids:[],
            sendRemark:'',
            checkall:false,
            sendLogList:[],
            affirmRecordList:{},
            affirmData:{},
            forwardType:'',
            selectOptionWidth: null,
            ispForwardType:'',
            GradeForwardType:''
        },
        watch:{
        },
        created: function() {
            this.initData()
            this.getSupport()
            // this.showDetails('6486f05cd6c8493a5978ed38')
        },
        computed: {},
        methods: {
            htmlBr(content){
                if(content!=null){
                    var reg=new RegExp("\n","g");
                    content= content.replace(reg,"<br/>");
                }
                return content
            },
            setOptionWidth(event){
                this.$nextTick(() => {
                    this.selectOptionWidth = event.srcElement.offsetWidth;
                    this.studyOptions=[]
                    this.options=[]
                }); 
            },
            showTitle(data,type){
                for(var i=0;i<this.config[type].length;i++){
                    if(this.config[type][i].value==data){
                        return this.config[type][i].title
                    }
                }
            },
            sort_change(column){
                this.order=column.order
                this.orderProp=column.prop
                this.getData(this.showType)
            },
            plus(index) {
                this.pageNum = Number(index) + 1
                this.getData(this.showType)
            },
            prev(index) {
                this.pageNum = Number(index) - 1
                this.getData(this.showType)
            },
            initData(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("showTab") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            if(data.data.typeList){
                                that.showType=data.data.typeList[0].type
                                that.getData(that.showType)
                            }
                            that.typeData=data.data
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            getData(type){
                let that=this
                this.showType=type
                that.indexLoading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("getHandleList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        type:type,
                        page:this.pageNum,
                        order: this.order,
                        orderProp:this.orderProp
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.dataList=data.data
                            that.itemList=data.data.meta
                            that.indexLoading=false
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.indexLoading=false

                        }
                    },
                    error: function(data) {
                        that.indexLoading=false

                    },
                })
            },
            getQiniu(id){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getQiniuToken") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        linkId:id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.qiniuToken=data.data
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            showDetails(id){
                let that=this
                this.sendLogList=[]
                this.affirmData={}
                that.getQiniu(id)
                if(that.uploader.length!=0) {
                    for(var i=0;i<that.uploader.length;i++){
                    that.uploader[i].destroy();
                    }
                }
                this.handId=id
                this.appendText=false
                this.refill=false
                this.help=''
                this.officeData={
                    to_admin: "", //是否需要校长处理 1是 0否
                    remark: "",
                    duration: "00:00",
                    attachments:{
                        img:[],
                        other:[]
                    }
                }
                this.resultData={
                    identified:'',
                    actions_other:'',
                    duration: "00:00",
                    remark:'',
                    actions:[],
                    attachments:{
                        img:[],
                        other:[]
                    },
                    student_ids:[],
                    contact_parents:[]
                }
                this.ispData={
                    help_type:'',
                    support:'',
                    remark:'',
                    attachments:{
                        img:[],
                        other:[]
                    }
                }
                this.gradeData={
                    remark:'',
                    attachments:{
                        img:[],
                        other:[]
                    }
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("getBehaviorDetail") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.contentList=data.data
                            that.detail=data.data.behavior.detail
                            $('#contentModal').modal('show')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.loading = false;
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.loading = false;
                    },
                })
            },
            getSupport(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getSupport") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.planData=data.data
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            addTeacher(viewType,sendType){
                this.viewType=viewType
                this.sendType=sendType
                $('#addTeacherModal').modal('show')
            },
            confirmTeacher(type){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("setSupportTeacher") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        teacher_id:type=='3'?this.teacherUid:this.studyTeacherUid,
                        type:type,//2-学习计划支持 3-行为计划支持
                        status: "1",//1-设置 2取消
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.options=[]
                            that.studyOptions=[]
                            that.teacherUid=''
                            that.studyTeacherUid=''
                            resultTip({
                                msg: data.message
                            });
                            that.getSupport()
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            delTeacher(show,list,index,type){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("setSupportTeacher") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        teacher_id:list,
                        type:type,//2-学习计划支持 3-行为计划支持
                        status: "2",//1-设置 2取消
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.planData[show].teacher.splice(index,1)
                            resultTip({
                                msg: data.message
                            });
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            
            studyRemoteMethod(query){
                let that=this
                if (query !== '') {
                    this.loading = true;
                    $.ajax({
                        url: '<?php echo $this->createUrl("teacherSearch") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            teacher_name:query
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.studyOptions = Object.values(data.data) ;
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                            that.loading = false;
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.loading = false;
                        },
                    })
                } else {
                    this.options = [];
                }
            },
            remoteMethod(query) {
                let that=this
                if (query !== '') {
                    this.loading = true;
                    $.ajax({
                        url: '<?php echo $this->createUrl("teacherSearch") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            teacher_name:query
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.options = Object.values(data.data) ;
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                            that.loading = false;
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.loading = false;
                        },
                    })
                } else {
                    this.options = [];
                }
            },
            showIndexOf(type){
                if(this.resultData[type] && this.resultData[type].indexOf("OTHER")!=-1){
                    return true
                }else{
                    return false
                }
            },
            saveGrade(type){
                let that=this
                var data={id:this.contentList.behavior._id}
                if(type=='isp'){
                    for(var i=0;i<this.attachments.img.length;i++){
                        this.ispData.attachments.img.push(this.attachments.img[i]._id)
                    }
                    for(var i=0;i<this.attachments.other.length;i++){
                        this.ispData.attachments.other.push(this.attachments.other[i]._id)
                    }
                    this.ispData.to_isp=1
                    data=Object.assign(data,{data:this.ispData})
                }
                if(type=='office'){
                    for(var i=0;i<this.attachments.img.length;i++){
                        this.officeData.attachments.img.push(this.attachments.img[i]._id)
                    }
                    for(var i=0;i<this.attachments.other.length;i++){
                        this.officeData.attachments.other.push(this.attachments.other[i]._id)
                    }
                    data=Object.assign(data,{data:{remark:this.officeData.remark,attachments:this.officeData.attachments,to_admin:1}})
                }
                if(type=='result'){
                    this.resultData.attachments.img=[]
                    this.resultData.attachments.other=[]
                    for(var i=0;i<this.attachments.img.length;i++){
                        this.resultData.attachments.img.push(this.attachments.img[i]._id)
                    }
                    for(var i=0;i<this.attachments.other.length;i++){
                        this.resultData.attachments.other.push(this.attachments.other[i]._id)
                    }
                    for(var i=0;i<this.contentList.behavior.child.length;i++){
                        this.resultData.student_ids.push(this.contentList.behavior.child[i].id)
                    }
                    this.resultData.identified=2
                    data=Object.assign(data,{data:this.resultData})
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("gradeleadProcess") ?>',
                    type: "post",
                    dataType: 'json',
                    data:data,
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg: data.message
                            });
                            that.getData(that.showType)
                            that.showDetails(that.contentList.behavior._id)
                            if(type=='result'){
                                $('#finalResultModal').modal('hide')
                            }else if(type=="office"){
                                $('#officeModal').modal('hide')
                            }else{
                                $('#ispModal').modal('hide')
                            }
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            saveTeacher(type){
                let that=this
                var data={id:this.contentList.behavior._id}
                if(type=='result'){
                    this.resultData.attachments.img=[]
                    this.resultData.attachments.other=[]
                    for(var i=0;i<this.attachments.img.length;i++){
                        this.resultData.attachments.img.push(this.attachments.img[i]._id)
                    }
                    for(var i=0;i<this.attachments.other.length;i++){
                        this.resultData.attachments.other.push(this.attachments.other[i]._id)
                    }
                    for(var i=0;i<this.contentList.behavior.child.length;i++){
                        this.resultData.student_ids.push(this.contentList.behavior.child[i].id)
                    }
                    this.resultData.identified=2
                    data=Object.assign(data,{data:this.resultData})
                }
                if(type=="office"){
                    for(var i=0;i<this.attachments.img.length;i++){
                        this.officeData.attachments.img.push(this.attachments.img[i]._id)
                    }
                    for(var i=0;i<this.attachments.other.length;i++){
                        this.officeData.attachments.other.push(this.attachments.other[i]._id)
                    }
                    data=Object.assign(data,{data:{remark:this.officeData.remark,attachments:this.officeData.attachments,to_admin:1}})
                }
                if(type=="grade"){
                    for(var i=0;i<this.attachments.img.length;i++){
                        this.gradeData.attachments.img.push(this.attachments.img[i]._id)
                    }
                    for(var i=0;i<this.attachments.other.length;i++){
                        this.gradeData.attachments.other.push(this.attachments.other[i]._id)
                    }
                    this.gradeData.to_gradelead=1
                    data=Object.assign(data,{data:this.gradeData})
                }
                console.log(data)
                $.ajax({
                    url: '<?php echo $this->createUrl("ISPProcess") ?>',
                    type: "post",
                    dataType: 'json',
                    data:data,
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg: data.message
                            });
                            that.getData(that.showType)
                            that.showDetails(that.contentList.behavior._id)
                            if(type=='result'){
                                $('#finalResultModal').modal('hide')
                            }else if(type=="grade"){
                                $('#gradeModal').modal('hide')
                            }else{
                                $('#officeModal').modal('hide')
                            }
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            saveOffice(type){
                let that=this
                var data={id:this.contentList.behavior._id}
                this.resultData.attachments.img=[]
                this.resultData.attachments.other=[]
                this.ispData.attachments.img=[]
                this.ispData.attachments.other=[]
                this.gradeData.attachments.img=[]
                this.gradeData.attachments.other=[]
                if(type=='isp'){
                    for(var i=0;i<this.attachments.img.length;i++){
                        this.ispData.attachments.img.push(this.attachments.img[i]._id)
                    }
                    for(var i=0;i<this.attachments.other.length;i++){
                        this.ispData.attachments.other.push(this.attachments.other[i]._id)
                    }
                    this.ispData.to_isp=1
                    data=Object.assign(data,{data:this.ispData})
                }
                if(type=="grade"){
                    for(var i=0;i<this.attachments.img.length;i++){
                        this.gradeData.attachments.img.push(this.attachments.img[i]._id)
                    }
                    for(var i=0;i<this.attachments.other.length;i++){
                        this.gradeData.attachments.other.push(this.attachments.other[i]._id)
                    }
                    this.gradeData.to_gradelead=1
                    data=Object.assign(data,{data:this.gradeData})
                }
                if(type=="result"){
                    this.resultData.attachments.img=[]
                    this.resultData.attachments.other=[]
                    for(var i=0;i<this.attachments.img.length;i++){
                        this.resultData.attachments.img.push(this.attachments.img[i]._id)
                    }
                    for(var i=0;i<this.attachments.other.length;i++){
                        this.resultData.attachments.other.push(this.attachments.other[i]._id)
                    }
                    if(this.resultData.student_ids.length==0){
                        resultTip({
                            error: 'warning',
                            msg: '请选择孩子'
                        });
                        return
                    }
                    data=Object.assign(data,{data:this.resultData,help:0})
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("officeProcess") ?>',
                    type: "post",
                    dataType: 'json',
                    data:data,
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg: data.message
                            });
                            if(type=='result'){
                                $('#finalResultModal').modal('hide')
                            }else{
                                $('#gradeModal').modal('hide')
                                $('#ispModal').modal('hide')
                            }
                            that.showDetails(that.contentList.behavior._id)
                            that.getData(that.showType)
                            // $('#contentModal').modal('hide')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            saveComment(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("officeProcessAddComment") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        id:this.contentList.behavior._id,
                        comment:this.appendComment
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.appendText=false
                            resultTip({
                                msg: data.message
                            });
                            that.appendComment=''
                            that.showDetails(that.contentList.behavior._id)
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            showImg(id,list){
                var viewer = new Viewer(document.getElementById(id),{
                    fullscreen: false,
                    title:false,
                    scalable:false,
                    show:function(){ 
                        if(list.length==1){
                            $('.viewer-prev').hide()
                            $('.viewer-next').hide()
                        }
                    },
                    hide:function(){ 
                        viewer.destroy()
                    }
                });
                $("#"+id).click();
            },
            stuRemoteMethod(query) {
                let that=this
                if (query !== '') {
                    this.loading = true;
                    $.ajax({
                        url: '<?php echo $this->createUrl("studentSearch") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            child_name:query
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.stuOption = Object.values(data.data) ;
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                            that.loading = false;
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.loading = false;
                        },
                    })
                } else {
                    this.options = [];
                }
            },
            addStu(id){
                for(var i=0;i<this.addChildList.length;i++){
                    if(this.addChildList[i].id==this.stuVal){
                        resultTip({
                            error: 'warning',
                            msg: '不能重复添加'
                        });
                        return
                    }
                }
                this.stuOption.forEach(item => {
                    if(item.id==this.stuVal){
                        this.addChildList.push(item)
                    }
                })
                this.resultData.student_ids.push(this.stuVal)
                this.stuOption=[]
                this.stuVal=''
            },
            delChild(index,id){
                this.addChildList.splice(index, 1)
                this.resultData.student_ids=this.resultData.student_ids.filter((item)=>{return item!=id});
            },
            finalResult(type){
                this.resultType=type
                if(this.uploader.length!=0) {
                    for(var i=0;i<this.uploader.length;i++){
                    this.uploader[i].destroy();
                    }
                }
                this.attachments={
                    img:[],
                    other:[]
                }
                $('#finalResultModal').modal('show')
                this.$nextTick(()=>{
                    config['token'] = this.qiniuToken;
                    config['browse_button'] ='finalResultAddFile';
                    var uploader=new plupload.Uploader(config);
                    this.uploader.push(uploader);
                    uploader.init();
                })
            },
            forwardOffice(type){
                this.forwardType=type
                if(this.uploader.length!=0) {
                    for(var i=0;i<this.uploader.length;i++){
                    this.uploader[i].destroy();
                    }
                }
                this.attachments={
                    img:[],
                    other:[]
                }
                $('#officeModal').modal('show')
                this.$nextTick(()=>{
                    config['token'] = this.qiniuToken;
                    config['browse_button'] ='officeAddFile';
                    var uploader=new plupload.Uploader(config);
                    this.uploader.push(uploader);
                    uploader.init();
                })
            },
            forwardGrade(type){
                this.GradeForwardType=type
                if(this.uploader.length!=0) {
                    for(var i=0;i<this.uploader.length;i++){
                    this.uploader[i].destroy();
                    }
                }
                this.attachments={
                    img:[],
                    other:[]
                }
                $('#gradeModal').modal('show')
                this.$nextTick(()=>{
                    config['token'] = this.qiniuToken;
                    config['browse_button'] ='gradeAddFile';
                    var uploader=new plupload.Uploader(config);
                    this.uploader.push(uploader);
                    uploader.init();
                })
            },
            forwardISP(type){
                this.ispForwardType=type
                if(this.uploader.length!=0) {
                    for(var i=0;i<this.uploader.length;i++){
                    this.uploader[i].destroy();
                    }
                }
                this.attachments={
                    img:[],
                    other:[]
                }
                $('#ispModal').modal('show')
                this.$nextTick(()=>{
                        config['token'] = this.qiniuToken;
                        config['browse_button'] ='ispAddFile';
                        var uploader=new plupload.Uploader(config);
                        this.uploader.push(uploader);
                        uploader.init();
                    })
            },
            delImg(type,list,index){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("deleteAttachment") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        attachmentId:list._id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            if(type=='img'){
                                that.attachments.img.forEach((item,index) => {
                                    if (item._id==list._id) {
                                        that.attachments.img.splice(index, 1)
                                    } 
                                })
                            }else{
                                that.attachments.other.forEach((item,index) => {
                                    if (item._id==list._id) {
                                        that.attachments.other.splice(index, 1)
                                    } 
                                })
                            }
                            resultTip({
                                msg:data.state
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            saveFile(list,index) {
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("updateAttachmentName") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        attachmentId:list._id,
                        attachmentName:this.attachmentName

                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.attachments.other.forEach((item,index) => {
                                if (item._id==list._id) {
                                    Vue.set(that.attachments.other[index], 'title',data.data.title);
                                    Vue.set(that.attachments.other[index], 'file_key', data.data.url);
                                    Vue.set(that.attachments.other[index], 'isEdit', false);
                                } 
                            })
                            resultTip({
                                msg:'<?php echo Yii::t("newDS", "Data Saved!");?>'
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            markEnd(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("setOver") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:this.contentList.behavior._id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.getData(that.showType)
                            $('#contentModal').modal('hide')
                            resultTip({
                                msg:data.state
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            addReaff(){
                this.resultData={
                    identified:'',
                    actions_other:'',
                    duration:"00:00",
                    remark:'',
                    actions:[],
                    attachments:{
                        img:[],
                        other:[]
                    },
                    student_ids:[],
                    contact_parents:[]
                }
                this.addChildList=[]
                this.attachments={
                    img:[],
                    other:[]
                }
                this.editStatus=2
                
                $('#afreshResultModal').modal('show')
            },
            showDisabled(id){
                var finallyId=[]
                this.contentList.finally_data.forEach(item => {
                    finallyId.push(item.child.id)
                });
                if(finallyId.indexOf(id)!=-1){
                    return true
                }else{
                    return false
                }
            },
            reaffirmation(data,type){
                this.resultData={
                    identified:data.result.identified,
                    actions_other:data.result.actions_other,
                    duration:data.result.duration,
                    remark:data.result.remark,
                    actions:data.result.actions,
                    attachments:{ 
                        img:[],
                        other:[]
                    },
                    contact_parents:data.result.contact_parents
                }
                var imgs=[]
                var others=[]
                if(data.result.attachments.img){
                    data.result.attachments.img.forEach(item => {
                        imgs.push(this.contentList.attachmentList[item]) 
                    });
                }
                if(data.result.attachments.other){
                    data.result.attachments.other.forEach(item => {
                        others.push(this.contentList.attachmentList[item]) 
                    });
                }
                this.attachments={
                    img:imgs,
                    other:others
                }
                this.editStatus=1
                this.reaffData=data
                if(this.uploader.length!=0) {
                    for(var i=0;i<this.uploader.length;i++){
                    this.uploader[i].destroy();
                    }
                }
                $('#afreshResultModal').modal('show')
                this.$nextTick(()=>{
                    console.log(1)
                    config['token'] = this.qiniuToken;
                    config['browse_button'] ='afreshAddFile';
                    var uploader=new plupload.Uploader(config);
                    this.uploader.push(uploader);
                    uploader.init();
                    console.log(this.uploader)
                })
            },
            afreshOffice(type){
                let that=this
                if(this.resultData.student_ids.length==0){
                    resultTip({
                        error: 'warning',
                        msg: '请选择学生'
                    });
                    return
                }
                for(var i=0;i<this.attachments.img.length;i++){
                    this.resultData.attachments.img.push(this.attachments.img[i]._id)
                }
                for(var i=0;i<this.attachments.other.length;i++){
                    this.resultData.attachments.other.push(this.attachments.other[i]._id)
                }
                // for(var i=0;i<this.addChildList.length;i++){
                //     this.resultData.student_ids.push(this.addChildList[i].id)
                // }
                var data={id:this.contentList.behavior._id,data:this.resultData,help:0}
                $.ajax({
                    url: '<?php echo $this->createUrl("officeProcess") ?>',
                    type: "post",
                    dataType: 'json',
                    data:data,
                    success: function(data) {
                        if (data.state == 'success') {
                            $('#afreshResultModal').modal('hide')
                            resultTip({
                                msg: data.message
                            });
                            that.showDetails(that.contentList.behavior._id)
                            
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            afreshReaff(){
                let that=this
                for(var i=0;i<this.attachments.img.length;i++){
                    this.resultData.attachments.img.push(this.attachments.img[i]._id)
                }
                for(var i=0;i<this.attachments.other.length;i++){
                    this.resultData.attachments.other.push(this.attachments.other[i]._id)
                }
                var data={id:this.reaffData._id,data:this.resultData}
                $.ajax({
                    url: '<?php echo $this->createUrl("afreshAffirm") ?>',
                    type: "post",
                    dataType: 'json',
                    data:data,
                    success: function(data) {
                        if (data.state == 'success') {
                            $('#afreshResultModal').modal('hide')
                            resultTip({
                                msg: data.message
                            });
                            that.showDetails(that.contentList.behavior._id)
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            showFollow(list){
                if(list.indexOf("OUTSCHOOLSUSPENSION")!=-1 || list.indexOf("INSCHOOLSUSPENSION")!=-1 ){
                    return true
                }else{
                    return false
                }
            },
            follow(data,type){
                this.followItem=data
                this.followType=type
                this.followData={
                    remark:this.followItem.followup.remark?this.followItem.followup.remark:'',
                    date:this.followItem.followup.date?this.followItem.followup.date:'',
                    time:this.followItem.followup.time?this.followItem.followup.time:''
                }
                $('#followModal').modal('show')
            },
            saveFollow(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("saveFollowup") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:this.followItem._id,
                        data:this.followData

                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.showDetails(that.contentList.behavior._id)
                            resultTip({
                                msg:data.message
                            });
                             $('#followModal').modal('hide')

                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            sendMessage(item){
                this.sendItem=item
                let that=this
                this.teacher_ids=[]
                this.sendRemark=''
                $.ajax({
                    url: '<?php echo $this->createUrl("getClassTeacherList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        class_id:[item.child.class_id]
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.classTeacher=data.data[item.child.class_id]
                            $('#sendTeacherModal').modal('show')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
                
            },
            allTeacher(){
                this.teacher_ids=[]
                if(this.checkall){
                    for(var keys in this.classTeacher){
                        this.teacher_ids.push(parseInt(keys))                         
                    }
                }
            },
            selectSendTeacher(){
                if(this.teacher_ids.length==Object.values(this.classTeacher).length){
                    this.checkall=true
                }else{
                    this.checkall=false
                }
            },
            saveSend(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("sendBehaviorMsgToTeacher") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        class_id:[this.sendItem.child.class_id],
                        id: this.sendItem._id,
                        remark: this.sendRemark,
                        "assist[class_id]":this.sendItem.child.class_id,
                        "assist[teacher_ids]":this.teacher_ids,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg: data.message
                            });
                            that.showDetails(that.contentList.behavior._id)
                            $('#sendTeacherModal').modal('hide')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            viewSendMessage(item){
                this.sendLogList=item
                $('#viewSendLogModal').modal('show')
            },
            viewAffirm(list){
                let that=this
                if(list.affirm_num==0){
                    return
                }
                this.affirmData=list
                $.ajax({
                    url: '<?php echo $this->createUrl("affirmRecordList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        child_id:list.id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.affirmRecordList=data.data
                            $('#affirmRecordListModal').modal('show')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            }
        }
    })
   
    var config = {
        runtimes: 'html5,flash,html4', //上传模式,依次退化
        container: 'container', //上传区域DOM ID，默认是browser_button的父元素，
        browse_button: '', //上传选择的点选按钮，**必需**
        token: '',
        flash_swf_url: '/js/plupload/Moxie.swf', // flash的相对地址
        auto_start: true, //选择文件后自动上传，若关闭需要自己绑定事件触发上传
        // multi_selection: false, //不允许多选
        url:'https://upload-z1.qiniup.com',
        init: {
            'FilesAdded': function(up, files) {
                container.disabledUpload=true
                let fileType=files[0].type.split('/')
                if(fileType[0]=="image"){
                  container.loadingType=1
                }else{
                  container.loadingType=2
                }
                container.loadingList=files
                up.start();
            },
            BeforeUpload: function(up, file) {
                //设置参数
                up.setOption({
                    multipart_params:{token:container.qiniuToken}
                })
                let fileType=file.type.split('/')
                if(fileType[0]=="image"){
                    container.attachments.img.push({types:'1'})
                }else{
                    container.attachments.other.push({types:'1',title:' <?php echo Yii::t("directMessage", "Uploading");?>'})  
                }
                container.loadingList.splice(0,1)
            },
            UploadProgress: function(up, file) {
                $('#progress').css('width', file.percent+'%').html(file.percent+'%');
            },
            'FileUploaded': function(up, file, info) {
                var response = eval('('+info.response+')');
                if(info.status == 200){
                    let fileType=response.data.mimetype.split('/')
                    if(fileType[0]=="image"){
                        container.attachments.img.splice(container.attachments.img.length-1,1)
                        container.attachments.img.push(response.data)
                    }else{
                        response.data.isEdit=false
                        container.attachments.other.splice(container.attachments.other.length-1,1)
                        container.attachments.other.push(response.data)
                    }
                }
            },
            'Error': function(up, err, errTip) {
                if(err.response){
                    var response = eval('('+err.response+')');
                }
                else{
                    var response = {message: err.message};
                }
                if(response.error == 'token not specified'){
                    up.stop();
                    $.ajax({
                        url: '<?php echo $this->createUrl("getQiniuToken") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            linkId: container.handId
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                container.qiniuToken=data.data
                                config['uptoken'] = data.data;
                                up.setOption("multipart_params", {"token":data.data,})
                                up.start();
                            } else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.msg
                                });
                            }
                        },
                        error: function(data) {

                        },
                    })
                }
            },
            'UploadComplete': function(up, file) {
                //队列文件处理完毕后,处理相关的事情
            }
        }
    };
</script>
