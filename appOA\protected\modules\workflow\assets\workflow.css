    .user-information h2 span{display: inline-block;float:right;font-size: 12px;font-weight: normal;margin-right: 4px;}
    #create-task{text-align: right;height:40px; position: relative;}
    #create-task .orange-box{border: 3px solid #FF9900; position: absolute; background-color: #FFFFCC;width: 280px; height: 100px;right: 0;top: 31px; text-align: left;}
    #create-task .orange-box h4{margin: 5px;font-size:14px;border-bottom:1px dotted #999;color:#666}
    .fs16{font-size:16px;}
    .gray{color:#999;}
    .pt20{padding-top:20px;}
    .tac{text-align:center;}
    #create-task .orange-box .acts{margin-left: 15px;}
    #create-task .orange-box .acts a{margin: 0 2px; padding:2px 6px; display: inline-block;line-height: 20px;text-decoration:none;}
    #create-task .orange-box .acts a:hover{text-decoration:none;background:#FF9900;color:#cc0001}
    #create-task a span, .tasks-list h3 span{padding:4px 16px;background:#FF9900;display: inline-block;font-size: 16px;color:#FFF5D5;font-weight: bold;}
    #create-task a:hover span{background:#FFC100;color:#fff;text-decoration:none;}
    
    #view-all-task{height:50px;}
    #view-all-task a span{height:40px;display:block;background:#cc0001;color:#fff;text-align:center;font-size:16px;line-height:40px;font-weight:bold;border-color: #FF2426 #A70001 #A70001 #FF2426;border-style: solid;border-width: 2px;}
    #view-all-task a:hover span{background:#EE4401;}
    .mb30{margin-bottom: 30px;}
    .tasks-list h3{height:40px; border-bottom: 1px solid #f2f2f2;color:#333;font-size:16px;}
    .tasks-list h3 span{margin-right:10px;min-width:28px;text-align: center;}
    #tasks-related h3 span{background: #747E80;}
    
    .workflow-box{margin:10px 0;}
    .workflow-box h4{display:block;float:left;width:60px;margin-right:10px;}
    .workflow-box h4,.workflow-box em{background: url("../../images/backend/wf_bg.png")  no-repeat scroll 0 0 transparent }
    #tasks-related .workflow-box h4{background: url("../../images/backend/wf_bg.png")  no-repeat scroll 0 -60px transparent }
    .workflow-box em.approved{display: block;height:32px;width:60px;background-position: 0 -120px;}
    .workflow-box em.rejected{display: block;height:32px;width:60px;background-position: 0 -152px;}
    .workflow-box h4 span{display:block;width:60px;height:60px;overflow:hidden;text-align:center;font-size:16px;line-height:60px;}
    .workflow-box .task-data{float:left;width:615px;padding-bottom:10px;border-bottom:2px dotted #f2f2f2;}
    ul.task-info li{float:left;width:250px;display:block;color:#333;overflow:hidden;border-bottom:1px dotted #fafafa;height:22px;}
    ul.task-info li label{margin-right:6px;}
    ul.workflow-chart li{float:left;}
    ul.workflow-chart li span{display:block;padding: 4px 12px;font-size:14px;line-height:22px;margin-top:10px;}
    ul.workflow-chart li.done span {background:#FFFFCC;border-bottom:5px solid #FF9900;}
    ul.workflow-chart li.todo span {background:#CC0001;border-bottom:5px solid #cc0001;margin-top:4px;line-height:28px;padding:4px 10px;font-size:18px;font-weight:bold;}
    ul.workflow-chart li.rejected span {background:#000000;border-bottom:5px solid #000000;margin-top:4px;line-height:28px;padding:4px 10px;font-size:18px;font-weight:bold;}
    ul.workflow-chart li.current span {background:#FFCC99;border-bottom:5px solid #cc0001;color:#cc0001}
    ul.workflow-chart li.waiting span {background:#FFFFCC;border-bottom:5px solid #ccc;font-size:12px;color:#b6b6b6;}
    ul.workflow-chart li.done span a{color:#333;}
    ul.workflow-chart li.todo span a{color:#fff;}
    ul.workflow-chart li.rejected span a{color:#fff;}
table#invoice-list{
    background-color: #000;
}
table#invoice-list th, table#invoice-list td{
    background-color: #fff;
    padding: 6px;
}
table#invoice-list th{
    text-align: center;
}
.towitem td{
     background-color:#999 !important;
     color:#fff;
}
.rafsbox{
    padding: 0 10px; margin-bottom: 10px;
}
.smes{padding:6px 0;line-height:22px;}
.smes label, .upload_label{display:inline-block;width:100px; text-align:right;margin-right:20px;font-size:12px;color:#666}
.oldpayment{
    color: #aaa;
}
.retshow{
    margin-bottom:10px;background:#fff;line-height:24px;padding: 6px;
}
.retshow h3{
    border-bottom: 2px solid #666;
    font-size:14px;
}
.retshow h4{
    clear:both;
    font-size:12px;
    color:#333;
}
.retshow h4 em{
    float:right;
    width: 140px;
    font-weight:normal;
    font-style:normal;
}
.retshow h4 span{
    width: 140px;
    float:left;
}
.retshow .comment label{
    display:inline-block;
    width:120px;
    text-align:right;
    color:#666;
    float:left;
}
.retshow .comment p{
    display:inline-block;
    /*float:left;*/
    width:400px;
    padding-left:20px;
    padding-right:20px;
    border-bottom: 1px dotted #999;
    color:#cc0001;
}
.hr{
    height: 3px;
    background-color: #000;
    margin-bottom: 10px;
}
table#invoice-list th, table#invoice-list td{
    padding: 6px;
}
table#invoice-list th{
    background:#f2f2f2;
}
textarea[name=process_desc]{
    width: 100%;
    height: 90px;
}
.winth500{
    min-width:500px;_width:500px;
}
#tasks-todo{
    min-height: 140px;
}

.file_box ul li.file{position:relative;padding: 4px 4px; border:1px solid #f2f2f2;margin:4px 0;min-height:80px}
.file_box ul li.file:hover{border:1px solid #d5d5d5;}
.file_box ul li.file span{display:inline-block;position:absolute;top:4px;left:100px;color:#666}
.file_box ul li.file button{position:absolute;top:56px;left:100px;color:#666}
.file_box ul li.file span input{border:1px solid #efefef;padding:2px;}
.file_box ul li.file:hover input{border:1px solid #999}
.upload_button{padding:10px 0 10px 200px;}

.upload_button span a span{display:inline-block;padding:4px 10px;background:#ff9900;color:#fff;font-size:14px;border-color: #FFB444 #DB8300 #DB8300 #FFB444;border-style: solid;border-width: 1px;}
.upload_button span a:hover span{background:#FFB444;color:#CC0001;text-decoration:none;_cursor:hand}

#attachmentList{width: 555px;}
#attachmentList ul li{float: left;width: 270px;margin: 10px 0;}
.mw500{min-width:500px;_width:500px;}
.mw600{min-width:600px;_width:600px}
.w600{width:600px}
.w680{width:680px}
.w500{width:500px}


/** 
*   过滤工作流
*/

.workflow-filter{
    height: 46px;
    border-bottom: 1px solid #F2F2F2;
    margin: 10px;
}
.workflow-filter li{
    float: left;
    margin: 10px 20px;
}
.workflow-filter li span.label{
    display: inline-block;
    width: 60px;
}