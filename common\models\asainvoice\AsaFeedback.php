<?php

/**
 * This is the model class for table "ivy_asa_feedback".
 *
 * The followings are the available columns in table 'ivy_asa_feedback':
 * @property integer $id
 * @property integer $childid
 * @property integer $group_id
 * @property integer $course_id
 * @property string $schoolid
 * @property integer $type
 * @property string $text
 * @property string $openid
 * @property integer $updated
 * @property integer $updated_by
 */
class AsaFeedback extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_asa_feedback';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('childid, group_id, course_id, type, updated, updated_by', 'numerical', 'integerOnly'=>true),
			array('schoolid, openid', 'length', 'max'=>255),
			array('text', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, childid, group_id, course_id, schoolid, type, text, openid, updated, updated_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'childid' => Yii::t('campusreport','childid'),
			'group_id' => 'Group',
			'course_id' => 'Course',
			'schoolid' => Yii::t('campusreport','schoolid'),
			'type' => Yii::t('asa','type'),
			'text' => Yii::t('asa','text'),
			'openid' => Yii::t('campusreport','childid'),
			'updated' => Yii::t('curriculum','Updated_time'),
			'updated_by' => Yii::t('asa','Parent Name'),
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('group_id',$this->group_id);
		$criteria->compare('course_id',$this->course_id);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('type',$this->type);
		$criteria->compare('text',$this->text,true);
		$criteria->compare('openid',$this->openid,true);
		$criteria->compare('updated',$this->updated);
		$criteria->compare('updated_by',$this->updated_by);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AsaFeedback the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
