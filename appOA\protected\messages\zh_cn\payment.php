<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yiic message' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE, this file must be saved in UTF-8 encoding.
 *
 * @version $Id: $
 */
return array (
  '2nd Month (:date)' => '',
  'Are you sure to cancel?' => '',
  'Are you sure to recover?' => '',
  'Awaiting' => '',
  'Cancel' => '作废',
  'Cancel Date' => '',
  'Day selections do not match tuition day type.' => '',
  'Deposit Balance' => '预缴学费余额',
  'End date should bigger than starting date' => '',
  'Fee Type' => '',
  'Final Amount: ' => '最终金额：',
  'Final Amount' => '需支付金额',
  'In/Out' => '',
  'Invoice Detail' => '',
  'Invoice Status' => '账单状态',
  'Invoice Amount' => '账单金额',  
  'Issue Date' => '生成时间',
  'Make Payment' => '付款',
  'New school year (:date)' => '',
  'Next Monday (:date)' => '',
  'Next Month (:date)' => '',
  'Paid Amount' => '已付金额',
  'Part of :amount has been frozen.' => '其中 :amount 冻结在学费账单中',
  'Payment Type' => '账单类型',
  'Payment List' => '付款记录',
  'Personal Account' => '个人账户',
  'Please input the correct number of afterschool class' => '',
  'Please select at date within this schoolyear' => '',
  'Please select at least one invoice' => '',
  'Refund' => '退款信息',
  'Make Refund' => '退款',
  'Refund Found' => '有退费信息',
  'Refund Information' => '退费信息',
  'Refunded' => '',
  'School/Class' => '',
  'Status' => '',
  'Target Date' => '',
  'Today (:date)' => '',
  'Week Day' => '',
  '付款' => '',
  '余额转移' => '',
  '作废' => '',
  'Print' => '打印',
  '提现申请' => '',
  '详细' => '',
  '2 Half Days' => '两半天',
  '2 Full Days' => '两全天',
  '3 Half Days' => '三半天',
  '5 Full Days' => '五全天',
  '3 Full Days' => '三全天',
  '5 Half Days' => '五半天',
  ':beginTime - :endTime :feeProgram :feeType' => ':beginTime-:endTime :feeProgram :feeType',
  ':singleTime :feeProgram :feeType' => ':singleTime :feeProgram :feeType',
  'After School' => '课后课',
  'CaRousel' => '亲子班',
  'Chinese Class' => '中文班',
  'Delivered to campus' => '校园已接收',
  'Due Date' => '到期日期',
  'Gift delivered' => '礼品已发放',
  'Library Card Cost' => '图书卡工本费',
  'Lunch' => '餐费',
  'M d, Y' => 'Y/m/d',
  'M, Y' => 'Y/m',
  'MIK Class' => 'MIK班',
  'Material Fee' => '入园材料费',
  'On route to campus' => '已经发往校园',
  'Order confirmed' => '订单已确认',
  'Order submitted' => '订单已提交',
  'Preparing to ship' => '准备发往校园',
  'School Bus' => '校车费',
  'Tuition' => '学费',
  'Tuition Deposit' => '预交学费',
  'Use Credit' => '使用个人余额',
  '代收代缴' => '代收代缴',
  '保育费' => '保育费',
  '寒暑假' => '寒暑假',
  '政府补贴' => '政府补贴',
  '进/出' => 'In/Out',
  'Extended Care' => '延时服务费',
  ':feeType Yearly Plan' => '【:feeType】年度月计划',
  'Partially Pay Amount' => '拆分金额',
  'Credits :credit' => '个人余额 :credit',
  ':selectedCount invoice(s) selected, total amount :amount.' => '选中 :selectedCount 笔账单，需支付总额 :amount',
  'Page expired, please reload.' => '页面已经过期，请刷新后再试',
  'POS Payment' => '订单POS机付款',
  'Cash Payment' => '现金付款',
  'Beginning of school year (:date)' => '学年起始（:date）',
  '2nd Month (:date)' => '第二个月起始（:date）',
  'Today (:date)' => '当日（:date）',
  'This Week End (:date)' => '本周末（:date）',
  'This Month End (:date)' => '本月末（:date）',
  'Next Monday (:date)' => '下周一（:date）',
  'Next Month (:date)' => '下个月（:date）',
  'Invoice Total Amount' => '账单总额',
  ':amount1 / :amount2' => ':amount1 / :amount2',
  'Memo' => '备注',
  'Start Date' => '开始时间',
  'End Date' => '结束时间',
  'Invoice Management' => '账单管理',
  'Cash Handover' => '现金交接',

  'Pay-by-cash' => '现金支付',

  'Paid' => '款付清',
  'Partially Paid' => '部分付清',
  'UnPaid' => '未付款',

  'Send Reminder Email' => '发送提醒邮件',
  'Last Reminder Date' => '上次提醒发送时间',
  'Last sent' => '上次发送',
  'Invoice' => '账单',
  'General Credit' => '个人账户',
    'Balance' => '余额',
    'Registration' => '注册费',
    'Entrance Fee' => '入学费',
    'Uniform' => '校服',
    'Course Fee' => '课程费',
    'Meal' => '餐费',
    '1st Semester' => '第一学期',
    '2nd Semester' => '第二学期',
    'Other' => '其他',
    'Student' => '学生',
    'Duration' => '账单区间',
    'Amount' => '金额',
    'Subtotal' => '小计',
    'Total' => '总计',
    'Office Signature' => '校园行政签名',
    'Parent Signature' => '家长签名',

  'Receipt' => '收据',
  'Amount after discount' => '折后价格',
  'Awaiting for Change Approval' => '更改中等待审核',

  'Wechat Payment' => '微信支付',
  'Annual' => '学年',

  'Invoice Period' => '付款区间',
);
