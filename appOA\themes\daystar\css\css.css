.main-menu-toggle{width: 550px;left: -80px}
.main-menu-toggle dl{margin-bottom: 0.4em;}
.main-menu-toggle dt{color: #999}
.main-menu-toggle dt b.caret{display:none}
.main-menu-toggle dd a{margin-right: 0.8em}

/*
===================
全局弹窗
*pop_top 头
	**strong 标题
	**pop_close 关闭
*pop_cont 中
	**pop_table 表格
*pop_bottom 尾
*pop_tips 提示
*pop_loading 加载状态
===================
*/

.core_pop_wrap{
    background-color: #fff;
    border: 1px solid #ccc;
    box-shadow:0 0 5px rgba(0,0,0,0.2);
    border-radius: 3px;
    position:fixed;
    _position:absolute;/*ie6*/
    z-index:10;
    color:#333;
    outline:none;
}
.core_pop_wrap a{
    color:#336699;
}
.pop_top{
    line-height:18px;
    padding:9px 0 8px;
    border-top:1px solid #fff;
    border-bottom:1px solid #e7e7e7;
    background:#f6f6f6;
    zoom:1;
    /*margin-bottom:5px;*/
}
.pop_top:after{
    content:'\20';
    display:block;
    height:0;
    clear:both;
    visibility: hidden;
    width:1px;
}
.pop_top strong{
    text-indent:15px;
    font-size:14px;
    color:#333;
    font-weight:700;
    white-space:nowrap;
    margin-right:10px;
    float:left;
}
.pop_top select{
    float:left;
    padding:1px;
    line-height:22px;
    height:22px;
}
.pop_top ul{
    border-bottom:1px solid #ccc;
    height:25px;
}
.pop_top li{
    float:left;
    display:block;
    line-height:25px;
    height:25px;
    padding:0 15px;
    cursor:pointer;
}
.pop_top li.current{
    float:left;
    border:1px solid #ccc;
    background:#fff;
    border-bottom:0 none;
}
.pop_top .pop_close{
    margin-right:15px;
}
.pop_bottom{
    padding:10px 15px;
    border-top:1px solid #e9e9e9;
    height:50px;
    line-height: 30px;
    /*white-space:nowrap;*/
}
.pop_bottom label{
    display:inline-block;
    padding-top:3px;
}
.pop_bottom .btn{
    padding-left:20px;
    padding-right:20px;
}
.pop_bottom .tips_error,
.pop_bottom .tips_success{
    max-width:200px;
    float:left;
}
.founder_pop .pop_bottom .tips_error{
    width:150px;
}
.pop_cont{
    background:#fff;
    color:#333;
    padding:10px 15px 10px;
}
.pop_table{
    height:365px;
    overflow-y:auto;
    position:relative;
}
.pop_table th,
.pop_table td{
    padding:6px 0 6px;
}
.pop_table th{
    height:26px;
    line-height:26px;
    vertical-align:top;
    white-space:nowrap;
    padding-right:20px;
}
.pop_table tr:hover th{
    color:#000;
}
.pop_tips{
    background:#f7f7f7;
    line-height:24px;
    padding:0 10px;
    margin:0 10px 0;
    color:#666;
}
/*关闭*/
.pop_close{
    margin-top:5px;
    float:right;
    width:9px;
    height:8px;
    overflow:hidden;
    text-indent:-2000em;
    background:url(../images/close.png) no-repeat;
    -webkit-transition: all 0.2s ease-out;
    margin-left:10px;
}
.pop_close:hover{
    background-position:0 -8px;
}
/*读取中*/
.pop_loading{
    background:url(../images/loading.gif) center center no-repeat;
    height:80px;
    width:80px;
    margin:auto;
}

/*
===================
非操作型提示
>>	作用与操作成功等，自动关闭效果的弹窗
===================
*/
.pop_showmsg_wrap:focus{
    outline:0 none;
}
.pop_showmsg_wrap,
.pop_showmsg{
    background:#f6fbfe url(../images/pop_showmsg.png) no-repeat;
}
.pop_showmsg_wrap{
    position:fixed;
    _position:absolute;
    z-index:11;
    height:55px;
    padding-right:9px;
    background-position:right 0;
    border-radius: 8px;
    box-shadow:0 0 10px #e1e1e1;
}
.pop_showmsg{
    height:55px;
    font-size:14px;
    background-position:left 0;
    border-top-left-radius:8px;
    border-bottom-left-radius:8px;
    display:inline-block;
}
.pop_showmsg span{
    padding:10px 10px 10px 68px;
    display:inline-block;
    line-height:36px;
    /*height:35px;*/
    text-shadow: 0 1px 1px #eee;
    color:#333;
}
.pop_showmsg span.tip-success{
    background:url(../images/success.gif) 20px center no-repeat;
}
.pop_showmsg span.tip-warning{
    background:url(../images/warning.gif) 20px center no-repeat;
}

/*
===================
间距
===================
*/
.mb5{
    margin-bottom:5px !important;
}
.mb10{
    margin-bottom:10px !important;
}
.mb15{
    margin-bottom:15px !important;
}
.mb20{
    margin-bottom:20px !important;
}
.mr5{
    margin-right:5px !important;
}
.mr10{
    margin-right:10px !important;
}
.mr15{
    margin-right:15px !important;
}
.mr20{
    margin-right:20px !important;
}
.ml5{
    margin-left:5px !important;
}
.ml10{
    margin-left:10px !important;
}
.ml15{
    margin-left:15px !important;
}
.ml20{
    margin-left:20px !important;
}
.mt20{
    margin-top:20px !important;
}
.p2{
    padding:2px;
}
.p5{
    padding:5px;
}
.p8{
    padding: 8px;
}
.p10{
    padding:10px;
}
.p15{
    padding:15px;
}
.p20{
    padding:20px;
}
.pt10{
    padding-top: 10px;
}
.background-gray{
    background: #f7f7f9;
}
/*
===================
输入框文本框长度
===================
*/
.length_0{
    width:20px;
}
.length_1{
    width:50px;
}
.length_2{
    width:110px;
}
.length_3{
    width:170px;
}
.length_4{
    width:230px;
}
.length_5{
    width:290px;
}
.length_6{
    width:350px;
}

/*
===================
选择select专属长度
===================
*/
.select_1{
    width:60px;
}
.select_2{
    width:120px;
}
.select_3{
    width:180px;
}
.select_4{
    width:240px;
}
.select_5{
    width:300px;
}
.select_6{
    width:360px;
}
.program30{
    color: dodgerblue;
}
.program20{
    color: orangered;
}
.program10{
    color: #008000;
}
.program0{
    color: #000;
}
.w400{
    width: 400px !important;
}
.nav-pills>li.warning>a, .nav-pills>li.warning>a:hover, .nav-pills>li.warning>a:focus {
    color: #fff;
    background-color: #ed9c28;
}
.date-active a {
    background: url("../images/circle.png") no-repeat right -2px !important;
}
.grid-view-loading{
    background: url("../../base/images/loading.gif") no-repeat center 38.2%;
}

.ui-datepicker-calendar tbody td a, .ui-datepicker-calendar tbody td span{text-align: center;font-family:arial !important;}
.ui-datepicker-week-end a.ui-state-default{color:#c1c1c1 !important;}
.ui-widget{font-weight: inherit !important;font-family: inherit !important}
.ui-widget input,.ui-widget select,.ui-widget textarea,.ui-widget button{font-family:inherit !important}
.ui-datepicker select.ui-datepicker-month, .ui-datepicker select.ui-datepicker-year {width:39% !important;}
