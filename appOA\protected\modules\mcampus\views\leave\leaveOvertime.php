<div id='containerData'  v-cloak>
    <div v-if='configPage.yearList && configPage.yearList.length==0'>
        <div class="alert alert-warning" role="alert"><?php echo Yii::t('leave', 'The number of leave days has not been assigned yet, please kindly contact HR to update.') ?></div>
    </div>
    <div v-else>
        <div v-if='configPage.role'>
            <div class='flex mb24'>
                <div class='flex1'>
                    <el-dropdown  v-if='configPage.showJiaban'> 
                        <el-button size="small"  type="primary" class='btn btn-primary'>
                        <?php echo Yii::t('leave', 'New Application') ?><i class="el-icon-arrow-down el-icon--right"></i>
                        </el-button>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item @click.native='newLeave()'><?php echo Yii::t('leave', 'Leave Application/Out of Office') ?></el-dropdown-item>
                            <el-dropdown-item  @click.native='newOvertime()'><?php echo Yii::t('leave', 'OverTime Application') ?></el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                    <button type="button" v-else class="btn btn-primary" @click='newLeave()'><?php echo Yii::t('leave', 'Leave Application/Out of Office') ?></button>
                </div>
                <div>
                    <el-select v-model="year" size="small" @change='tableLists(tableType,"init")' placeholder="<?php echo Yii::t('teaching', 'Please select') ?>">
                        <el-option
                        v-for="(item,index) in yearList"
                        :key="item.key"
                        :label="item.value"
                        :value="item.key">
                        </el-option>
                    </el-select>
                </div>
            </div>
            <ul class="nav nav-tabs" role="tablist">
                <li role="presentation" :class="tableType=='processList'?'active':''" v-if='configPage.isApprover'><a href="#home" aria-controls="home" role="tab" data-toggle="tab"  @click='tableLists("processList","init")'><?php echo Yii::t('leave', 'Pending') ?></a></li>
                <li role="presentation" :class="tableType=='finishedList'?'active':''" v-if='configPage.isApprover'><a href="#user" aria-controls="profile" role="tab" data-toggle="tab" @click='tableLists("finishedList","init")'><?php echo Yii::t('leave', 'Completed') ?></a></li>
                <li role="presentation" :class="tableType=='staffList'?'active':''" ><a href="#user" aria-controls="profile" role="tab" data-toggle="tab" @click='tableLists("staffList","init")'><?php echo Yii::t('leave', 'Mine') ?></a></li>
                <li role="presentation" :class="tableType=='ccList'?'active':''" ><a href="#user" aria-controls="profile" role="tab" data-toggle="tab" @click='tableLists("ccList","init")'><?php echo Yii::t('leave', 'CC me') ?></a></li>
            </ul>
        </div>
        <div class='mt24' v-if='tablePage.itemList'>
            <el-table
            :data="tableData"
            :header-cell-style="{background:'#fafafa',color:'#333'}"
            style="width: 100%">
            <template slot="empty">
                <?php echo Yii::t("ptc", "No Data"); ?>
            </template>
            <el-table-column
                prop="date"
                width='250'
                label="<?php echo Yii::t('leave', 'Applicant') ?>">
                <template slot-scope="scope">
                    <div class='flex'>
                        <img :src="tablePage.staffInfo[scope.row.leave_staff].photoUrl" class='img42 img-circle' alt="">
                        <div class='flex1 ml10'>
                            <div class='font14 color3'>{{tablePage.staffInfo[scope.row.leave_staff].name}}</div>
                            <div class='font12 color6'>{{tablePage.staffInfo[scope.row.leave_staff].hrPosition}}</div>
                        </div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column
                prop="name"
                label="<?php echo Yii::t('leave', 'Department') ?>">
                <template slot-scope="scope">
                    <span>{{tablePage.posData[tablePage.staffInfo[scope.row.leave_staff].posId]}}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="type"
                label="<?php echo Yii::t('newDS', 'Type') ?>">
                <template slot-scope="scope">
                    <span v-if='configPage.leaveConfig[scope.row.type]'>{{configPage.leaveConfig[scope.row.type].title}}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="address"
                width='200'
                label="<?php echo Yii::t('labels', 'Time') ?>">
                <template slot-scope="scope">
                    <div v-if='tablePage.itemDateList[scope.row.id] && scope.row.start==null'>
                        <div v-for='(list,index) in tablePage.itemDateList[scope.row.id]'>{{formatDate(list.date)}}  <span v-if='list.date_end!=null'> - {{formatDate(list.date_end)}}</span> 
                        </div>
                    </div>
                    <div v-else>{{formatDate(scope.row.start)}} - {{formatDate(scope.row.end)}}</div>
                </template>
            </el-table-column>
            <el-table-column
                prop="duration_format"
                min-width='100'
                label="<?php echo Yii::t('leave', 'Total Time') ?>">
                
            </el-table-column>
            <el-table-column
                prop="created_at"
                min-width='150'
                label="<?php echo Yii::t('leave', 'Submit Time') ?>">
            </el-table-column>
            <el-table-column
                prop="address"
                min-width='100'
                label="<?php echo Yii::t('user', 'Status') ?>">
                <template slot-scope="scope">
                <span class="label labelWarning" :class='scope.row.state==1?"label-warning":scope.row.state==0?"label-danger":scope.row.state==2?"label-success":"label-default"'>{{configPage.stateList[scope.row.state]}}</span>
                </template>
            </el-table-column>
            <el-table-column
                prop="address"
                width='140'
                fixed="right"
                label="<?php echo Yii::t('global', 'Action') ?>">
                <template slot-scope="scope">
                    <span v-if='tableType=="processList"'><el-button type="text" class='text-primary mr20' @click='approval(scope.row,"shenpi")'><?php echo Yii::t('leave', 'Process') ?></el-button></span>
                    <span v-else><el-button type="text" class='text-primary mr20' @click='approval(scope.row,"detail")'><?php echo Yii::t('newDS', 'Detail') ?></el-button></span>
                    <span v-if='tableType!="processList" && scope.row.state!=-1'>
                        <el-dropdown v-if='((configPage.userInfo.uid==scope.row.leave_staff || configPage.userInfo.uid==scope.row.created_by) && (scope.row.state==1 || scope.row.state==2)) || configPage.role=="hr"'>
                            <span class="el-dropdown-link text-primary"  >
                            <?php echo Yii::t('referral', 'More') ?><i class="el-icon-arrow-down el-icon--right"></i>
                            </span>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item @click.native='addFile(scope.row)' v-if='scope.row.state!=0 ' ><?php echo Yii::t('leave', 'Material Attachments') ?></el-dropdown-item>
                                <el-dropdown-item v-if='scope.row.state==1 || configPage.role=="hr"' @click.native='cancelList(scope.row.id)'><?php echo Yii::t('leave', 'Revoke Application') ?></el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </span>
                </template>
            </el-table-column>
            </el-table>
            <nav aria-label="Page navigation" v-if='CopyPages.count && CopyPages.count>1'  class="text-left ml10">
                <ul class="pagination">
                    <li v-if='pageNum >1'>
                        <a href="javascript:void(0)" @click="plus(1)" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    <li v-else class="disabled">
                        <a aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    <li class="previous" v-if='pageNum >1'>
                        <a href="javascript:void(0)" @click="prev(pageNum)">‹</a>
                    </li>
                    <li class="disabled" v-else>
                        <a href="javascript:void(0)">‹</a>
                    </li>
                    <li v-for='(data,index) in CopyPages.count' :class="{ active:data==pageNum }">
                        <a href="javascript:void(0)" @click="plus(data)">{{data}}</a>
                    </li>
                    <li class="previous" v-if='pageNum <CopyPages.count'>
                        <a href="javascript:void(0)" @click="next(pageNum)">›</a>
                    </li>
                    <li class="previous disabled" v-else>
                        <a href="javascript:void(0)">›</a>
                    </li>
                    <li v-if='pageNum <CopyPages.count'>
                        <a href="javascript:void(0)" @click="plus(CopyPages.count)" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li v-else class="disabled">
                        <a aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                </ul>
                <div class='summary mb10'>共 {{CopyPages.total}} 条</div>
            </nav>
        </div>
        <div v-else-if='tablePage.itemList'>
            <el-empty description="<?php echo Yii::t('ptc', 'No Data') ?>"></el-empty>
        </div>
    </div>
    <!-- 请假 -->
    <div class="modal fade" id="addLeaveModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t('leave', 'Leave Application/Out of Office') ?></h4>
            </div>
            <div class="modal-body p24 overflow-y scroll-box font14" :style="'max-height:'+(modalHeight)+'px;overflow-x: hidden;'" v-if='balanceData.approver'>
                <div class='row'>
                    <div class='col-md-6'>
                        <div class='flex relative'>
                            <span class='borderLine'></span>
                            <div class='width20'>
                                <span class='dot'></span>
                            </div>
                            <div class='flex1'>
                                <div class='titleLeave mt0'><?php echo Yii::t('leave', 'Applicant') ?> <span class='text-primary cur-p'  v-if='configPage.requestOthers' @click='cutover'><?php echo Yii::t('leave', 'Request for others') ?></span>
                                </div>
                                <div class="flex mt8 mb20">
                                    <div class="">
                                        <img :src="currentStaff.photoUrl" class="img-circle img42">
                                    </div>
                                    <div class="flex1 ml15">
                                        <h4 class="list-group-item-heading mt5 color3 font14">{{currentStaff.name}}</h4>
                                        <div class="color6 font12">{{currentStaff.hrPosition}}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class='flex relative'>
                            <div class='width20'>
                                <span class='dot'></span>
                            </div>
                            <div class='flex1'>
                                <div class='titleLeave mt0'><?php echo Yii::t('leave', 'Approver') ?></div>
                                <div class="flex mt8" v-if='balanceData.approver.approver_first && balanceData.approver.approver_first!="" && balanceData.approver.staff_info[balanceData.approver.approver_first]'>
                                    <div class="">
                                        <img :src="balanceData.approver.staff_info[balanceData.approver.approver_first].photoUrl" class="img-circle img42">
                                    </div>
                                    <div class="flex1 ml15">
                                        <h4 class="list-group-item-heading mt5 color3 font14">{{balanceData.approver.staff_info[balanceData.approver.approver_first].name}}</h4>
                                        <div class="color6 font12">{{balanceData.approver.staff_info[balanceData.approver.approver_first].hrPosition}}</div>
                                    </div>
                                </div>
                                <div v-else style='color:#F0AD4E' class='font14 mt16'><span class='glyphicon glyphicon-info-sign'></span> <?php echo Yii::t('leave', 'No approver configured, please contact HR.') ?></div>
                            </div>
                        </div>
                        <div class='titleLeave'><?php echo Yii::t('leave', 'Leave Type') ?></div>
                        <div class='mt8' v-if='configPage.leaveType'>
                            <el-select v-model="typeLeave" placeholder="<?php echo Yii::t('global', 'Please Select') ?>" size='small' style='width:100%'>
                                <el-option
                                v-for='(list,index) in configPage.leaveTypeShort'
                                :key="index"
                                :label="configPage.leaveConfig[list].title"
                                :value="list"
                                v-if="!(balanceData.balance[list]==0 && list=='tiaoxiu')"
                                :disabled="!balanceData.balance.start_date || (balanceData.balance[list]==0 && list!='bingjia')?true:false">
                                <span v-if='balanceData.balance.start_date'>
                                    <span>
                                        <span >{{configPage.leaveConfig[list].title}} 
                                            <span v-if='balanceData.balance[list] && balanceData.balance[list]!=0'>（{{translate("<?php echo Yii::t('leave', '%s Left'); ?>", countHour(balanceData.balance[list]))}}）</span>  
                                            <span v-if='balanceData.balance[list]==0'>（{{translate("<?php echo Yii::t('leave', '%s Left'); ?>","0 <?php echo Yii::t('site', 'Days') ?>")}}）</span>
                                        </span>
                                    </span>
                                </span>
                                <span class='color9' v-else>{{configPage.leaveConfig[list].title}} 
                                        <span>（<?php echo Yii::t('leave', 'No quota configured, please contact HR.') ?>）</span>  
                                    </span> 
                                </el-option>
                            </el-select>
                        </div>
                        <div class='titleLeave'><span v-if='typeLeave=="waichu"'><?php echo Yii::t('leave', 'Reason For Out of Office') ?></span><span v-else><?php echo Yii::t('leave', 'Reason For Leave Application') ?></span> </div>
                        <div class='mt8'>
                            <el-input
                                type="textarea"
                                :rows="2"
                                placeholder="<?php echo Yii::t('leave', 'Input') ?>"
                                v-model="memoLeave">
                            </el-input>
                        </div>
                        <div class='titleLeave'><?php echo Yii::t('leave', 'Upload Attachment') ?></div>
                        <div class='font12 color6 mt5'>
				<ul style="padding-left: 16px;">
					<li><?php echo Yii::t('leave', 'For sick leave of 2 consecutive days or more, you need to upload an official doctor note.') ?></li>
					<li><?php echo Yii::t('leave', 'If the application is for PD training, please upload the approved PD application form.') ?></li>
				</ul>
			</div>
                        <div class='mt12 mb12' id="leaveBox">
                            <div id='leaveFile' class='leaveFile'>
                                <span class='el-icon-upload2'></span> <?php echo Yii::t('referral', 'Upload') ?>
                            </div>
                        </div>
                        <div>
                            <div class='' v-if='attachments.img && attachments.img.length>0'>
                                <div class='imgData mr8'  v-for='(list,i) in attachments.img'>
                                    <div v-if="list.types=='1'">
                                        <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Uploading");?></div>
                                    </div>
                                    <div v-else>
                                        <img class='imgList'  :src="list.url" alt="">
                                        <span aria-hidden="true" class='closeImg' @click.stop='delImg("img",list,i)'>×</span>
                                    </div>
                                </div>
                                <template v-if='loadingType==1'>
                                    <div class='imgData mr8'  v-for='(list,i) in loadingList'>
                                        <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Upload queue");?></div>
                                    </div>
                                </template>
                            </div>
                            <div>
                                <div class='mt16' v-if='attachments.other && attachments.other.length>0'>
                                    <div class='flex uploadFile' v-for='(list,index) in attachments.other'>
                                        <span class='glyphicon glyphicon-paperclip mr8'></span>
                                        <span v-if="list.types=='1'"><?php echo Yii::t("directMessage", "Uploading");?></span>
                                        <template v-else>
                                            <a target="_blank" class='flex1'  style='line-height:26px' :href='list.url' v-if='!list.isEdit'>{{list.name}}</a>
                                            <span class='flex1' v-else><input type="input" class='inputStyle'  @keyup.enter="saveFile(list,index)"  v-model='attachmentName' ></span>
                                            <template v-if='!list.isEdit'>
                                                <span class='glyphicon glyphicon-edit icon mr16' v-if='list.url!=""' @click.stop='list.isEdit=true,attachmentName=list.name'></span>
                                                <span class='glyphicon glyphicon-trash icon' v-if='list.url!=""' @click.stop='delImg("link",list,index)'></span>
                                            </template>
                                            <span style='width:90px'  class='text-right inline-block' v-else>
                                                <button type="button" class="btn btn-primary btn-xs" @click='saveFile(list,index)'><?php echo Yii::t("global", "Save");?></button>
                                                <button type="button" class="btn btn-default btn-xs" @click='list.isEdit=false'><?php echo Yii::t("global", "Cancel");?></button>
                                            </span>
                                        </template>
                                    </div>
                                </div>
                                <template v-if='loadingType==2'>
                                    <div class='flex uploadFile'  v-for='(list,i) in loadingList'>
                                        <span class='glyphicon glyphicon-paperclip mr8'></span>
                                        <a  class='flex1'  style='line-height:26px' > <?php echo Yii::t("directMessage", "Upload queue");?></a>
                                    </div>
                                </template>
                            </div>
                        </div>  
                        <div class="panel panel-default mt24" v-if='balanceData.substitute'>
                            <div class="panel-heading"><?php echo Yii::t('leave', 'Substitute Info') ?></div>
                            <div class="panel-body">
                                <div class='titleLeave mt0'><?php echo Yii::t('leave', 'Course Substitute Required?') ?></div>
                                <div class='mt8 font14'>
                                    <label class="radio-inline">
                                        <input type="radio" v-model='substitute' value="1"> <?php echo Yii::t('leave', 'Yes') ?>
                                    </label>
                                    <label class="radio-inline">
                                        <input type="radio" v-model='substitute' value="2"> <?php echo Yii::t('leave', 'No') ?>
                                    </label>
                                </div>
                                <div class='font14' v-if='substitute=="1"'>
                                    <div class='' v-for='(list,index) in configPage.substitute_type'>
                                        <label class="checkbox-inline mt12">
                                            <input type="checkbox" v-model="selectedItems" :value="list.value">{{list.title}}
                                        </label>
                                        <div v-if="selectedItems.includes(list.value)" class='mb12'>
                                            <div class='color6 font12 mt12'>{{list.desc}}</div>
                                            <div>
                                                <textarea class="form-control" placeholder= "<?php echo Yii::t('leave', 'Input') ?>" rows="2" v-model="list.text"></textarea>
                                            </div>
                                            <!-- <el-input 
                                                type="textarea"
                                                :rows="2"
                                                placeholder="<?php echo Yii::t('leave', 'Input') ?>" 
                                                v-model="list.text">
                                            </el-input> -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>                      
                    </div>
                    <div class='col-md-6'>
                        <div class='titleLeave mt0'><?php echo Yii::t('leave', 'Please select the date & time of leave application on the calendar and the dates respectively if it is more than one day.') ?></div>
                        <div class=' mt8'  v-if='calendarDate'>
                            <div class=''>
                                <v-date-picker  :locale="localeType" :attributes='addAttrsLeave' style='width:100%' :min-date='minData' :max-date='maxData'  :available-dates='availableDate' @dayclick="pickDateLeave"  v-model='selectedDate'/>
                            </div>
                            <div class='mt8'>
                                <div class='dateList font14 color3 flex mb8' v-for='(item,index) in addTimeListLeave'>
                                    <span class='flex1 pt8'>{{item.day}} {{item.text}} </span>
                                    <div>
                                        <el-select v-model="item.value" size='small' placeholder="<?php echo Yii::t('teaching', 'Please select') ?>" style='width:150px' @change='countDuration("leave")'>
                                            <el-option
                                            v-for="(item,index) in configPage.dateType"
                                            :key="item.key"
                                            :label="item.value"
                                            :value="item.key">
                                            </el-option>
                                        </el-select>
                                        <div class='mt8' v-if='choose_time.indexOf(item.value)!=-1'>
                                            <el-time-picker
                                                style='width:150px' 
                                                size='small' 
                                                
                                                v-model="item.time"
                                                value-format="HH:mm"
                                                format="HH:mm"
                                                placement="bottom-start"
                                                placeholder="<?php echo Yii::t('leave', 'Start Time');?>">
                                            </el-time-picker>
                                        </div>
                                    </div>
                                    <span class='el-icon-error font16 ml12 color9 pt8' @click='delLeaveTime(index,item,"leave")'></span>
                                </div>
                            </div>
                        </div>
                        <div v-if='totalDurationLeave!=""'>
                            <div class='titleLeave'><?php echo Yii::t('leave', 'Total Time') ?> <span class='color6 ml12 font12 fontNormal'><?php echo Yii::t('leave', 'One working day = 8 hours') ?></span></div>
                            <div  class='mt20'><span class='dateList font14 color3'>{{totalDurationLeave}}</span></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer"  v-if='balanceData.approver'>
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                <button type="button" class="btn btn-primary" @click="saveLeave('leave')" v-if='balanceData.approver.approver_first && balanceData.approver.approver_first!="" && balanceData.approver.staff_info[balanceData.approver.approver_first]' :disabled='subBtn'><?php echo Yii::t("reg", "Submit");?></button>
                <button type="button" class="btn btn-primary"  v-else disabled='true'><?php echo Yii::t("reg", "Submit");?></button>
            </div>
            </div>
        </div>
    </div>
    <!-- 加班 -->
    <div class="modal fade" id="overtimeModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t('leave', 'OverTime Application') ?></h4>
            </div>
            <div class="modal-body p24 overflow-y scroll-box font14" :style="'max-height:'+(modalHeight)+'px;overflow-x: hidden;'" v-if='balanceData.approver'>
                <div class='titleLeave mt4'>
                <?php echo Yii::t('leave', 'Applicant') ?> <span class='text-primary cur-p'  v-if='configPage.requestOthers' @click='cutover'><?php echo Yii::t('leave', 'Request for others') ?></span>
                </div>
                <div class="flex mt8">
                    <div class="">
                        <img :src="currentStaff.photoUrl" class="img-circle img42">
                    </div>
                    <div class="flex1 ml15">
                        <h4 class="list-group-item-heading mt5 color3 font14">{{currentStaff.name}}</h4>
                        <p class="color6 font12">{{currentStaff.hrPosition}}</p>
                    </div>
                </div>
                <div class='titleLeave'><?php echo Yii::t('leave', 'Date / Time') ?></div>
                <div class='mt8'>
                    <div class='text-center mb16'>
                        <v-date-picker :attributes='addAttrsOvertime' :available-dates='availableDate' @dayclick="pickDateOvertime"  v-model='selectedDate' />
                    </div>
                    <div >
                        <div class='dateList font14 color3 flex mb8' v-for='(item,index) in addTimeListOvertime'>
                            <span class='flex1'>{{item.day}} {{item.text}}</span>
                            <el-select v-model="item.value" size='small' placeholder="<?php echo Yii::t('teaching', 'Please select') ?>" style='width:100px'  @change='countDuration("overtime")'>
                                <el-option
                                v-for="(item,i) in configPage.dateType"
                                :key="item.key"
                                :label="item.value"
                                :value="item.key">
                                </el-option>
                            </el-select>
                            <el-select v-model="item.overtime_type" size='small' class='ml12' placeholder="<?php echo Yii::t('teaching', 'Please select') ?>" style='width:100px'>
                                <el-option
                                v-for="(item,index) in configPage.overtimeType"
                                :key="item.key"
                                :label="item.value"
                                :value="item.key">
                                </el-option>
                            </el-select>
                            <span class='el-icon-error font16 ml12 color9'  @click='delLeaveTime(index,item,"overtime")'></span>
                        </div>
                    </div>
                </div>
                <div v-if='totalDurationOvertime!=""'>
                    <div class='titleLeave'><?php echo Yii::t('leave', 'Total Time') ?> <span class='color6 ml12 font12'><?php echo Yii::t('leave', 'One working day = 8 hours') ?></span></div>
                    <div  class='mt20'><span class='dateList font14 color3'> {{totalDurationOvertime}}</span></div>
                </div>
                <div class='titleLeave'><?php echo Yii::t('newDS', 'Upload attachments') ?></div>
                <div class='mt8' id='overtimeBox'><el-button type="text" icon="el-icon-circle-plus-outline" id='overtimeFile' class='font14 bluebg'><?php echo Yii::t('referral', 'Upload') ?></el-button></div>
                <div>
                    <div class='' v-if='attachments.img && attachments.img.length>0'>
                        <div class='imgData mr8'  v-for='(list,i) in attachments.img'>
                            <div v-if="list.types=='1'">
                                <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Uploading");?></div>
                            </div>
                            <div v-else>
                                <img class='imgList' :src="list.url" alt="">
                                <span aria-hidden="true" class='closeImg' @click.stop='delImg("img",list,i)'>×</span>
                            </div>
                        </div>
                        <template v-if='loadingType==1'>
                            <div class='imgData mr8'  v-for='(list,i) in loadingList'>
                                <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Upload queue");?></div>
                            </div>
                        </template>
                    </div>
                    <div>
                        <div class='mt16' v-if='attachments.other && attachments.other.length>0'>
                            <div class='flex uploadFile' v-for='(list,index) in attachments.other'>
                                <span class='glyphicon glyphicon-paperclip mr8'></span>
                                <span v-if="list.types=='1'"><?php echo Yii::t("directMessage", "Uploading");?></span>
                                <template v-else>
                                    <a target="_blank" class='flex1'  style='line-height:26px' :href='list.url' v-if='!list.isEdit'>{{list.name}}</a>
                                    <span class='flex1' v-else><input type="input" class='inputStyle'  @keyup.enter="saveFile(list,index)"  v-model='attachmentName' ></span>
                                    <template v-if='!list.isEdit'>
                                        <span class='glyphicon glyphicon-edit icon mr16' v-if='list.url!=""' @click.stop='list.isEdit=true,attachmentName=list.name'></span>
                                        <span class='glyphicon glyphicon-trash icon' v-if='list.url!=""' @click.stop='delImg("link",list,index)'></span>
                                    </template>
                                    <span style='width:90px'  class='text-right inline-block' v-else>
                                        <button type="button" class="btn btn-primary btn-xs" @click='saveFile(list,index)'><?php echo Yii::t("global", "Save");?></button>
                                        <button type="button" class="btn btn-default btn-xs" @click='list.isEdit=false'><?php echo Yii::t("global", "Cancel");?></button>
                                    </span>
                                </template>
                            </div>
                        </div>
                        <template v-if='loadingType==2'>
                            <div class='flex uploadFile'  v-for='(list,i) in loadingList'>
                                <span class='glyphicon glyphicon-paperclip mr8'></span>
                                <a  class='flex1'  style='line-height:26px' > <?php echo Yii::t("directMessage", "Upload queue");?></a>
                            </div>
                        </template>
                    </div>
                </div>
                <div class='titleLeave'><?php echo Yii::t('leave', 'Reason(s) for OT') ?></div>
                <div class='mt8'>
                    <el-input
                        type="textarea"
                        :rows="2"
                        placeholder="<?php echo Yii::t('leave', 'Input') ?>"
                        v-model="memoOvertime">
                    </el-input>
                </div>
                <div class='titleLeave'><?php echo Yii::t('leave', 'Approve') ?></div>
                <div class="flex mt8" v-if='balanceData.approver.Approver && balanceData.approver.approver_first!="" && balanceData.approver.staff_info[balanceData.approver.approver_first]'>
                    <div class="">
                        <img :src="balanceData.approver.staff_info[balanceData.approver.approver_first].photoUrl" class="img-circle img42">
                    </div>
                    <div class="flex1 ml15">
                        <h4 class="list-group-item-heading mt5 color3 font14">{{balanceData.approver.staff_info[balanceData.approver.approver_first].name}}</h4>
                        <p class="color6 font12">{{balanceData.approver.staff_info[balanceData.approver.approver_first].hrPosition}}</p>
                    </div>
                </div>
                <div v-else  style='color:#F0AD4E' class='font14 mt16'><span class='glyphicon glyphicon-info-sign'></span> <?php echo Yii::t('leave', 'No approver configured, please contact HR.') ?></div>
            </div>
            <div class="modal-footer" v-if='balanceData.approver'>
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                <button type="button" class="btn btn-primary" @click="saveLeave('overtime')" v-if='balanceData.approver.approver_first && balanceData.approver.approver_first!="" && balanceData.approver.staff_info[balanceData.approver.approver_first]' :disabled='subBtn'><?php echo Yii::t("reg", "Submit");?></button>
                <button type="button" class="btn btn-primary"  v-else disabled='true'><?php echo Yii::t("reg", "Submit");?></button>
            </div>
            </div>
        </div>
    </div>
    <!-- 选择成员 -->
    <div class="modal fade" id="addStaffModal" tabindex="-1" role="dialog" data-backdrop="static" data-keyboard="false" data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t('leave', 'Applicant') ?></h4>
                </div>
                <div class="modal-body relative" style='padding:0'>
                    <span class='borderLeftpos'></span>
                    <div style='max-height:600px;' class='p24 row'>
                        <div class='col-md-6 col-sm-6'>
                            <!-- <div>
                                <el-select v-model="schoolId" class='width100' placeholder="<?php echo Yii::t('teaching', 'Please select') ?>" @change='getSchoolData'>
                                    <el-option
                                        v-for="(item,key,index) in schoolList"
                                        :key="key"
                                        :label="item.title"
                                        :value="key">
                                    </el-option>
                                </el-select>
                            </div> -->
                            <div class='mt10'>
                                <el-input
                                placeholder="<?php echo Yii::t('global', 'Search') ?>"
                                v-model='searchText' 
                                clearable>
                                </el-input>
                            </div>
                            <div  class="tab-pane active mt15 scroll-box" id="class" v-if='searchText==""'  style='max-height:460px;overflow-y:auto'>
                                <div v-for='(list,index) in currentDept.list' class='relative mb16'>
                                    <p  @click='showDepName(list)'>
                                        <span  class='font14 color606 cur-p'>{{list.dep_name}} </span>
                                        <span class='el-icon-arrow-down ml5' v-if='dep_name!=list.dep_name'></span>
                                        <span class='el-icon-arrow-up ml5' v-else></span>
                                    </p>
                                    <div  class='border scroll-box mr10 childList' v-if='dep_name==list.dep_name'>
                                        <div class="flex align-items listMedia" v-for='(item,key,idx) in list.user'>
                                            <div class='flex flex1' v-if='currentDept.user_info[item.uid]'>
                                                <img :src="currentDept.user_info[item.uid].photoUrl" data-holder-rendered="true" class="media-object img-circle img42">
                                                <div class="flex1 ml10 flex1Text">
                                                    <div class=" font14 mt4 color3 text_overflow">{{currentDept.user_info[item.uid].name}}</div>
                                                    <div class="font12 color6 text_overflow">{{currentDept.user_info[item.uid].hrPosition}}</div>
                                                </div>
                                            </div>
                                            <div >
                                                <span class='cur-p font16 text-primary  el-icon-circle-plus-outline' v-if='!currentDept.user_info[item.uid].disabled' @click='assignStaff(item,index,idx)'></span>
                                                <span v-else><?php echo Yii::t('directMessage', 'selected') ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else>
                                <div v-if='searchStaffList.length!=0' class='mt24 scroll-box'  style='max-height:460px;overflow-y:auto'>  
                                    <div class="flex align-items listMedia" v-for='(item,idx) in searchStaffList'>
                                        <div class='flex flex1' >
                                            <img :src="item.photoUrl" data-holder-rendered="true" class="media-object img-circle img42">
                                            <div class="flex1 ml10 flex1Text">
                                                <div class=" font14 mt4 color3 text_overflow">{{item.name}}</div>
                                                <div class="font12 color6 text_overflow">{{item.hrPosition}}</div>
                                            </div>
                                        </div>
                                        <div >
                                            <span class='cur-p font16 text-primary  el-icon-circle-plus-outline' v-if='!currentDept.user_info[item.uid].disabled' @click='assignStaff(item,idx,"search")'></span>
                                            <span v-else><?php echo Yii::t('directMessage', 'selected') ?></span>
                                        </div>
                                    </div> 
                                </div>
                                <div v-else-if='searchText!=""'>
                                        <div class='font14 color6 text-center mt20'><?php echo Yii::t('ptc', 'No Data)') ?></div>    
                                </div>
                            </div>
                        </div>
                        <div class='col-md-6 col-sm-6 borderLeft'>
                            <p class='mt10 font14 color6'>
                            <?php echo Yii::t("newDS", " ");?>{{staffSelected.length}} <?php echo Yii::t('leave', 'Applicant') ?>
                                <!-- <button class="btn btn-link pull-right btn-xs font14" v-if='staffSelected.length!=0' type="button" @click='batchDel("modal")'><?php echo Yii::t("newDS", "Clear All");?></button> -->
                            </p>
                            <div class='scroll-box p10 overflow-y' style='height:500px'>
                                <div class="flex align-items listMedia" v-for='(item,idx) in staffSelected'>
                                    <div class='flex flex1' v-if='allDept[item]'>
                                        <img :src="allDept[item].photoUrl" data-holder-rendered="true" class="media-object img-circle img42">
                                        <div class="flex1 ml10 flex1Text">
                                            <div class=" font14 mt4 color3 text_overflow">{{allDept[item].name}}</div>
                                            <div class="font12 color6 text_overflow">{{allDept[item].hrPosition}}</div>
                                        </div>
                                    </div>
                                    <div @click='Unassign(item,idx)'>
                                        <span class='closeChild cur-p mt10 font16 el-icon-circle-close'></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class='clearfix'></div>
                    </div>
                </div> 
                <div class="modal-footer">
                <span class='text-left colorRed pr16'><span class='glyphicon glyphicon-exclamation-sign'></span> <?php echo Yii::t('leave', 'You can only select one staff.') ?></span>
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel");?></button>
                    <button type="button" class="btn btn-primary" :disabled="staffSelected.length==1?false:true" @click='confirmSatff()'><?php echo Yii::t("global", "OK");?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 审批请假 -->
    <div class="modal fade" id="approvalModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t('leave', 'Approver') ?></h4>
            </div>
            <div class="modal-body p24 overflow-y scroll-box font14" :style="'max-height:'+(modalHeight)+'px;overflow-x: hidden;'" v-if='detailData.applyData'>
                <div class='titleLeave mt4'>
                    {{configPage.leaveConfig[detailData.applyData.type].title}} 
                    <span class="label labelWarning" :class='detailData.applyData.state==1?"label-warning":detailData.applyData.state==0?"label-danger":detailData.applyData.state==2?"label-success":"label-default"'>{{configPage.stateList[detailData.applyData.state]}}</span>
                </div>
                <div class='color6 font12 mt12 mb20' v-if='shenpiState.type!="jiaban"'>
                    {{translate("<?php echo Yii::t('leave', 'Total %s Taken %s'); ?>", configPage.leaveConfig[shenpiState.type].title, detailData.applyData.count==0? 0 + ' <?php echo Yii::t('site', 'Days') ?>':countHour(detailData.applyData.count) )}}
                </div>
                <div v-if='detailData.applyData.leave_staff==detailData.applyData.created_by'>
                    <div class="flex mt8">
                        <div class="">
                            <img :src="detailData.staffInfo[detailData.applyData.leave_staff].photoUrl" class="img-circle img42">
                        </div>
                        <div class='flex flex1 ml15 align-items'>
                            <div class="flex1 ">
                                <h4 class="list-group-item-heading mt5 color3 font14">{{detailData.staffInfo[detailData.applyData.leave_staff].name}}</h4>
                                <div class="color6 font12">{{detailData.staffInfo[detailData.applyData.leave_staff].hrPosition}}</div>
                            </div>
                            <div><?php echo Yii::t('leave', 'Submit Time') ?>：{{detailData.applyData.created_at}}</div>
                        </div>
                    </div>
                </div>
                <div v-else>
                    <div class="flex mt8">
                        <div class="">
                            <img :src="detailData.staffInfo[detailData.applyData.leave_staff].photoUrl" class="img-circle img42">
                        </div>
                        <div class="flex1 ml15">
                            <h4 class="list-group-item-heading mt5 color3 font14">{{detailData.staffInfo[detailData.applyData.leave_staff].name}}</h4>
                            <p class="color6 font12">{{detailData.staffInfo[detailData.applyData.leave_staff].hrPosition}}</p>
                        </div>
                    </div>
                    <div class='flex color6 font12'>
                        <div class='flex1'>{{translate("<?php echo Yii::t('leave', 'Requested by %s'); ?>", detailData.staffInfo[detailData.applyData.created_by].name)}}</div>
                        <div><?php echo Yii::t('leave', 'Submit Time') ?>：{{detailData.applyData.created_at}}</div>
                    </div>
                </div>
                <hr>
                <div v-if='shenpiState.type=="jiaban"'>                
                    <div class='flex align-items approvalTitle'>
                        <div class='line'></div>
                        <div class='flex1 ml8'><?php echo Yii::t('leave', 'OverTime Information') ?></div>
                    </div>
                    <div class='mt20'>
                        <div class='detailWidth'><?php echo Yii::t('leave', 'OverTime Information') ?></div>
                        <div class=''>
                            <el-table
                                ref="table"
                                class='summaryTables setCell'
                                :header-cell-style="{background:'#fafafa',color:'#333'}"
                                show-summary
                                sum-text='<?php echo Yii::t('leave', 'Total Time') ?>'
                                :summary-method="getSummaries"
                                :data="detailData.itemData"
                                border
                                style="width: 100%">
                                <el-table-column
                                prop="date"
                                label="<?php echo Yii::t('labels', 'Time') ?>">
                                <template  slot-scope="scope">
                                    {{formatDate(scope.row.date)}}
                                </template>
                                </el-table-column>
                                <el-table-column
                                prop="duration_format"
                                label="<?php echo Yii::t('teaching', 'Duration') ?>">
                                </el-table-column>
                                <el-table-column
                                prop="overtime_type"
                                label="<?php echo Yii::t('leave', 'OverTime Type') ?>">
                                <template  slot-scope="scope">
                                    {{overtimeValue(scope.row.overtime_type,'overtimeType')}}
                                </template>
                                </el-table-column>
                                <el-table-column
                                prop="duration_format"
                                width='150'
                                label="<?php echo Yii::t('leave', 'Payment Option') ?>">
                                <template  slot-scope="scope">
                                    <el-dropdown>
                                        <span class="el-dropdown-link">
                                            <span v-if='scope.row.settlement.value'>{{scope.row.settlement.value}}</span>
                                            <span v-else class='colorRed'><?php echo Yii::t('teaching', 'Please select') ?></span><i class="el-icon-arrow-down el-icon--right"></i>
                                        </span>
                                        <el-dropdown-menu slot="dropdown">
                                            <el-dropdown-item v-for='(item,index) in configPage.overtimeSettlement' :key='index' @click.native='scope.row.settlement=item'>{{item.value}}</el-dropdown-item>
                                        </el-dropdown-menu>
                                    </el-dropdown>
                                </template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </div>
                </div>
                <div v-else>                
                    <div class='flex align-items approvalTitle'>
                        <div class='line'></div>
                        <div class='flex1 ml8'><span v-if='shenpiState.type=="waichu"'><?php echo Yii::t('leave', 'Out of Office Information') ?></span><span v-else> <?php echo Yii::t('leave', 'Leave Information') ?></span></div>
                    </div>
                    <div class=' mt20'>
                        <div class='detailWidth'><span v-if='shenpiState.type=="waichu"'><?php echo Yii::t('leave', 'Out of Office Information') ?></span><span v-else> <?php echo Yii::t('leave', 'Leave Information') ?></span></div>
                        <div class=''>
                            <el-table
                                class='setCell'
                                :header-cell-style="{background:'#fafafa',color:'#333'}"
                                show-summary
                                sum-text='<?php echo Yii::t('leave', 'Total Time') ?>'
                                :summary-method="getSummaries"
                                :data="detailData.itemData"
                                border
                                style="width: 100%">
                                <el-table-column
                                prop="date"
                                label="<?php echo Yii::t('labels', 'Time') ?>">
                                <template  slot-scope="scope">
                                    {{formatDate(scope.row.date)}}
                                </template>
                                </el-table-column>
                                <el-table-column
                                prop="duration_format"
                                label="<?php echo Yii::t('teaching', 'Duration') ?>">
                                    <template  slot-scope="scope">
                                        {{scope.row.duration_format}}
                                        <span v-if='scope.row.duration_start!=null && scope.row.duration_start!=""' class=''>（<?php echo Yii::t('leave', 'From ') ?>{{scope.row.duration_start}}）</span>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </div>
                </div>
                <div class=' mt20'  v-if='detailData.fileData.img.length!=0 || detailData.fileData.other.length!=0'>
                    <div class='detailWidth'><?php echo Yii::t('curriculum', 'Attachments') ?></div>
                    <div class=''>
                        <div class=''>
                            <ul class='mb12 imgLi'  id='record'  v-if='detailData.fileData.img.length!=0'>
                                <li v-for='(list,i) in detailData.fileData.img'>
                                    <img :src="list.fileUrl_thumb" :data-original="list.fileUrl" class='imgList mb8 mr8' @click='showImg("record",detailData.fileData.img)' alt=""  >
                                </li>
                            </ul>
                        </div>
                        <div class='color3' v-if='detailData.fileData.other.length!=0'>
                            <div v-for='(list,j) in detailData.fileData.other'>
                                <div class='flex fileLink' >
                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg"/> 
                                    <a class='flex1 ml5' target= "_blank" :href="list.fileUrl">{{list.name}}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class=' mt20'>
                    <div class='detailWidth'>
                        <span v-if='shenpiState.type=="jiaban"'><?php echo Yii::t('leave', 'Reason(s) for OT') ?></span>
                        <span v-else-if='shenpiState.type=="waichu"'><?php echo Yii::t('leave', 'Reason For Out of Office') ?></span>
                        <span v-else><?php echo Yii::t('leave', 'Reason For Leave Application') ?></span>
                    </div>
                    <div class='  font14' v-html='detailData.applyData.leave_memo'> </div>
                </div>
                <div v-if='detailData.applyData.substitute'>
                    <div class='flex align-items approvalTitle mt24 mb8'>
                        <div class='line'></div>
                        <div class='flex1 ml8'><?php echo Yii::t('leave', 'Substitute Info') ?></div>
                    </div>
                    <div class='font14 color6 mt16' v-if='detailData.applyData.substitute_info.need=="2" || detailData.applyData.substitute_info.list.length==0'><?php echo Yii::t('leave', 'N/A') ?></div>
                    <div  v-if='detailData.applyData.substitute_info.need=="1" && detailData.applyData.substitute_info.list.length!=0'>
                        <div class='flex font14 mt16' v-for='(list,index) in detailData.applyData.substitute_info.list'>
                            <div class='substituteInfo'>
                                <span class='color6'>{{detailData.applyData.substitute_type[list.value].title}}</span>
                            </div>
                            <div class='flex1 color3'> {{list.desc}}</div>
                        </div>
                    </div>
                </div>
                <div >                
                    <div class='flex align-items approvalTitle mt24'>
                        <div class='line'></div>
                        <div class='flex1 ml8'><?php echo Yii::t('leave', 'Approver') ?></div>
                    </div>
                    <div class='mt20'>
                        <div class="flex mt8">
                            <div class="">
                                <img :src="configPage.userInfo.photoUrl" class="img-circle img42">
                            </div>
                            <div class="flex1 ml15">
                                <h4 class="list-group-item-heading mt5 color3 font14">{{configPage.userInfo.name}}</h4>
                                <p class="color6 font12">{{configPage.userInfo.hrPosition}}</p>
                                <div class='approvalData mt12'>
                                    <div class="font14">
                                        <label class="radio-inline">
                                            <input type="radio" v-model='approverModel' value="1"> <?php echo Yii::t('leave', 'Approved') ?>
                                        </label>
                                    </div>
                                    <div class='ml16' v-if='approverModel=="1"'>
                                        <el-input
                                            type="textarea"
                                            :rows="2"
                                            placeholder="<?php echo Yii::t('leave', 'Input') ?>"
                                            v-model="approverMemo">
                                        </el-input>
                                        <div class='flex mt12 font14' v-if='detailData.applyData.cc.length!=0'>
                                            <div class='color6'><?php echo Yii::t('leave', 'Copy to:') ?></div>
                                            <div class='flex1  color3 ml10'>
                                                <span v-for='(item,i) in detailData.applyData.cc'><span v-if='detailData.staffInfo[item]'>{{detailData.staffInfo[item].name}}</span>  <span v-if='i+1<detailData.applyData.cc.length'>、</span></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="font14 mt10">
                                        <label class="radio-inline">
                                            <input type="radio" v-model='approverModel' value="2"> <?php echo Yii::t('leave', 'Rejected') ?>
                                        </label>
                                    </div>
                                    <div class='ml16' v-if='approverModel=="2"'>
                                        <el-input
                                            type="textarea"
                                            :rows="2"
                                            placeholder="<?php echo Yii::t('leave', 'Input') ?>"
                                            v-model="approverMemo">
                                        </el-input>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer" >
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                <button type="button" class="btn btn-primary" @click="saveApproval()"><?php echo Yii::t("global", "OK");?></button>
            </div>
            </div>
        </div>
    </div>
    <!-- 详情 -->
    <div class="modal fade" id="detailModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog" role="document" v-if='detailData.applyData'>
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t('newDS', 'Detail');?></h4>
            </div>
            <div class="modal-body p24 overflow-y scroll-box font14" :style="'max-height:'+(modalHeight)+'px;overflow-x: hidden;'"  v-if='detailData.applyData'>
                <div class='titleLeave mt4 mb12'>
                    {{configPage.leaveConfig[detailData.applyData.type].title}} 
                    <span class="label font12 labelWarning" :class='detailData.applyData.state==1?"label-warning":detailData.applyData.state==0?"label-danger":detailData.applyData.state==2?"label-success":"label-default"'>{{configPage.stateList[detailData.applyData.state]}}</span>
                </div>
                <div v-if='detailData.applyData.leave_staff==detailData.applyData.created_by'>
                    <div class="flex mt8">
                        <div class="">
                            <img :src="detailData.staffInfo[detailData.applyData.leave_staff].photoUrl" class="img-circle img42">
                        </div>
                        <div class='flex flex1 ml15 align-items'>
                            <div class="flex1 ">
                                <h4 class="list-group-item-heading mt5 color3 font14">{{detailData.staffInfo[detailData.applyData.leave_staff].name}}</h4>
                                <div class="color6 font12">{{detailData.staffInfo[detailData.applyData.leave_staff].hrPosition}}</div>
                            </div>
                            <div><?php echo Yii::t('leave', 'Submit Time') ?>：{{detailData.applyData.created_at}}</div>
                        </div>
                    </div>
                </div>
                <div v-else>
                    <div class="flex mt8">
                        <div class="">
                            <img :src="detailData.staffInfo[detailData.applyData.leave_staff].photoUrl" class="img-circle img42">
                        </div>
                        <div class="flex1 ml15">
                            <h4 class="list-group-item-heading mt5 color3 font14">{{detailData.staffInfo[detailData.applyData.leave_staff].name}}</h4>
                            <p class="color6 font12">{{detailData.staffInfo[detailData.applyData.leave_staff].hrPosition}}</p>
                        </div>
                    </div>
                    <div class='flex color6 font12'>
                        <div class='flex1'>{{translate("<?php echo Yii::t('leave', 'Requested by %s'); ?>", detailData.staffInfo[detailData.applyData.created_by].name)}}</div>
                        <div><?php echo Yii::t('leave', 'Submit Time') ?>：{{detailData.applyData.created_at}}</div>
                    </div>
                </div>
                <hr>
                <div v-if='detailData.applyData.type=="jiaban"'>                
                    <div class='flex align-items approvalTitle'>
                        <div class='line'></div>
                        <div class='flex1 ml8'><?php echo Yii::t('leave', 'OverTime Information') ?></div>
                    </div>
                    <div class='mt20'>
                        <div class=' mt20'>
                            <div class='detailWidth'><?php echo Yii::t('leave', 'OverTime Information') ?></div>
                            <div class=''>
                                <el-table
                                    ref="table"
                                    class='summaryTable setCell'
                                    :header-cell-style="{background:'#fafafa',color:'#333'}"
                                    show-summary
                                    sum-text='<?php echo Yii::t('leave', 'Total Time') ?>'
                                    :summary-method="getSummaries"
                                    :data="detailData.itemData"
                                    border
                                    style="width: 100%">
                                    <el-table-column
                                    prop="date"
                                    label="<?php echo Yii::t('labels', 'Time') ?>">
                                    <template  slot-scope="scope">
                                        {{formatDate(scope.row.date)}}
                                    </template>
                                    </el-table-column>
                                    <el-table-column
                                    prop="duration_format"
                                    label="<?php echo Yii::t('teaching', 'Duration') ?>">
                                    </el-table-column>
                                    <el-table-column
                                    prop="duration_format"
                                    label="<?php echo Yii::t('leave', 'OverTime Type') ?>">
                                    <template  slot-scope="scope">
                                        {{overtimeValue(scope.row.overtime_type,'overtimeType')}}
                                    </template>
                                    </el-table-column>
                                    <el-table-column
                                    prop="duration_format"
                                    label="<?php echo Yii::t('leave', 'Payment Option') ?>">
                                    <template  slot-scope="scope">
                                        {{scope.row.overtime_settlement==null?'':overtimeValue(scope.row.overtime_settlement,'overtimeSettlement')}}
                                    </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                        </div>
                        <div class=' mt20'  v-if='detailData.fileData.img.length!=0 || detailData.fileData.other.length!=0'>
                            <div class='detailWidth'><?php echo Yii::t('curriculum', 'Attachments') ?></div>
                            <div class=''>
                                <div class=''>
                                    <ul class='mb12 imgLi'  id='detail'  v-if='detailData.fileData.img.length!=0'>
                                        <li v-for='(list,i) in detailData.fileData.img'>
                                            <img :src="list.fileUrl_thumb" :data-original="list.fileUrl" class='imgList mb8 mr8' @click='showImg("detail",detailData.fileData.img)' alt=""  >
                                        </li>
                                    </ul>
                                </div>
                                <div class='color3' v-if='detailData.fileData.other.length!=0'>
                                    <div v-for='(list,j) in detailData.fileData.other'>
                                        <div class='flex fileLink' >
                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" /> 
                                            <a class='flex1 ml5' target= "_blank" :href="list.fileUrl">{{list.name}}
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class=' mt20'>
                            <div class='detailWidth'><?php echo Yii::t('leave', 'Reason(s) for OT') ?></div>
                            <div class=' font14' v-html='detailData.applyData.leave_memo'></div>
                        </div>
                    </div>
                </div>
                <div v-else>                
                    <div class='flex align-items approvalTitle'>
                        <div class='line'></div>
                        <div class='flex1 ml8'> <span v-if='detailData.applyData.type=="waichu"'><?php echo Yii::t('leave', 'Out of Office Information') ?></span><span v-else> <?php echo Yii::t('leave', 'Leave Information') ?></span></div>
                    </div>
                    <div class='mt20'>
                        <div class=' mt20'>
                            <div class='detailWidth mb10'><span v-if='detailData.applyData.type=="waichu"'><?php echo Yii::t('leave', 'Out of Office Information') ?></span><span v-else> <?php echo Yii::t('leave', 'Leave Information') ?></span></div>
                            <div v-if='detailData.modified.modified_uid' style='color:#F0AD4E' class='font12 mb10'><span class='el-icon-info font14 mr4'></span> {{translate("<?php echo Yii::t('leave', 'This entry was last modified by %s %s'); ?>", detailData.staffInfo[detailData.modified.modified_uid].name, detailData.modified.modified_time)}}</div>
                            <div class=''>
                                <el-table
                                    class='setCell'
                                    :header-cell-style="{background:'#fafafa',color:'#333'}"
                                    show-summary
                                    sum-text='<?php echo Yii::t('leave', 'Total Time') ?>'
                                    :summary-method="getSummariesDetail"
                                    :data="detailData.itemData"
                                    border
                                    style="width: 100%">
                                    <el-table-column
                                    prop="date"
                                    label="<?php echo Yii::t('labels', 'Time') ?>">
                                    <template  slot="header">
                                        <span class='color6'><?php echo Yii::t('labels', 'Time') ?></span>
                                    </template>
                                    <template  slot-scope="scope">
                                        {{formatDate(scope.row.date)}}
                                    </template>
                                    </el-table-column>
                                    <el-table-column
                                    prop="duration_format"
                                    label="<?php echo Yii::t('teaching', 'Duration') ?>">
                                    <template  slot="header">
                                        <span class='color6'><?php echo Yii::t('teaching', 'Duration') ?></span>
                                    </template>
                                    <template  slot-scope="scope">
                                        {{scope.row.duration_format}}
                                        <span v-if='scope.row.duration_start!=null && scope.row.duration_start!=""' class=''>（<?php echo Yii::t('leave', 'From ') ?>{{scope.row.duration_start}}）</span>
                                    </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                            <div class='history' v-if='detailData.modified.modified_uid'>
                                <span class='historyTitle'><?php echo Yii::t('leave', 'Original Leave Information') ?></span>
                                <div>
                                    <span class='color6 font12'><?php echo Yii::t('leave', 'Type') ?>：</span><span class='color3 font12'>{{configPage.leaveConfig[detailData.oldDate[0].type].title}}</span>
                                </div>
                                <div class='mt10 flex'><span class='color6 font12' style='margin-top:2px'><?php echo Yii::t('labels', 'Time') ?>：</span><span class='flex1'><span v-for='(list,index) in detailData.oldDate' class='historyDate'>{{formatDate(list.date)}} <span class='historyBorder'>{{list.duration_format}} <span v-if='list.duration_start'>（<?php echo Yii::t('leave', 'From ') ?>{{list.duration_start}}）</span></span></span></span></div>
                               <div class='flex mt5'><span class='color6 font12'><?php echo Yii::t('leave', 'Modification Reason (By HR)') ?>：</span><span class='color3 font12 flex1' v-html='detailData.modified.modified_memo'></span></div>
                            </div>
                        </div>
                        <div class=' mt20'  v-if='detailData.fileData.img.length!=0 || detailData.fileData.other.length!=0'>
                            <div class='detailWidth'><?php echo Yii::t('curriculum', 'Attachments') ?></div>
                            <div class=''>
                                <div class=''>
                                    <ul class='mb12 imgLi'  id='detail'  v-if='detailData.fileData.img.length!=0'>
                                        <li v-for='(list,i) in detailData.fileData.img'>
                                            <img :src="list.fileUrl_thumb" :data-original="list.fileUrl" class='imgList mb8 mr8' @click='showImg("detail",detailData.fileData.img)' alt=""  >
                                        </li>
                                    </ul>
                                </div>
                                <div class='color3' v-if='detailData.fileData.other.length!=0'>
                                    <div v-for='(list,j) in detailData.fileData.other'>
                                        <div class='flex fileLink' >
                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" /> 
                                            <a class='flex1 ml5' target= "_blank" :href="list.fileUrl">{{list.name}}
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class=' mt20'>
                            <div class='detailWidth'><span v-if='detailData.applyData.type=="waichu"'><?php echo Yii::t('leave', 'Reason For Out of Office') ?></span><span v-else><?php echo Yii::t('leave', 'Reason For Leave Application') ?></span></div>
                            <div class=' font14' v-html='detailData.applyData.leave_memo'></div>
                        </div>
                    </div>
                </div>
                <div v-if='detailData.applyData.substitute'>
                    <div class='flex align-items approvalTitle mt24 mb8'>
                        <div class='line'></div>
                        <div class='flex1 ml8'><?php echo Yii::t('leave', 'Substitute Info') ?></div>
                    </div>
                    <div class='font14 color6 mt16' v-if='detailData.applyData.substitute_info.need=="2" || detailData.applyData.substitute_info.list.length==0'><?php echo Yii::t('leave', 'N/A') ?></div>
                    <div  v-if='detailData.applyData.substitute_info.need=="1" && detailData.applyData.substitute_info.list.length!=0'>
                        <div class='flex font14 mt16' v-for='(list,index) in detailData.applyData.substitute_info.list'>
                            <div class='substituteInfo'>
                                <span class='color6'>{{detailData.applyData.substitute_type[list.value].title}}</span>
                            </div>
                            <div class='flex1 color3'> {{list.desc}}</div>
                        </div>
                    </div>
                </div>
                <div v-if='detailData.applyData.state!=-1'>                
                    <div class='flex align-items approvalTitle mt24'>
                        <div class='line'></div>
                        <div class='flex1 ml8'><?php echo Yii::t('leave', 'Approver') ?></div>
                    </div>
                    <div class='mt20' v-if='detailData.applyData.approver!=null'>
                        <div class="flex mt8">
                            <div class="">
                                <img :src="detailData.staffInfo[detailData.applyData.approver].photoUrl" class="img-circle img42">
                            </div>
                            <div class="flex1 ml15">
                                <div class='flex align-items'>
                                    <div class='flex1'>
                                        <h4 class="list-group-item-heading mt5 color3 font14 flex">{{detailData.staffInfo[detailData.applyData.approver].name}} </span></h4>
                                        <div class="color6 font12">{{detailData.staffInfo[detailData.applyData.approver].hrPosition}}</div>
                                    </div>
                                    <span>
                                        <span class="label font12 labelWarning" :class='detailData.applyData.state==1?"label-warning":detailData.applyData.state==0?"label-danger":detailData.applyData.state==2?"label-success":"label-default"'>{{configPage.stateList[detailData.applyData.state]}}</span>
                                        <span class='font12 color6 ml16' v-if='detailData.applyData.state!=1'>{{detailData.applyData.updated_at}}</span>
                                    </span>
                                </div>
                                <div class='approvalData mt12' v-if='detailData.applyData.approver_memo!=null'>
                                    <div class='flex font14'>
                                        <div  class='color6'><?php echo Yii::t('labels', 'Memo') ?>：</div>
                                        <div class='flex1  color3 ml10' v-html='detailData.applyData.approver_memo'></div>
                                    </div> 
                                    <div class='flex mt12 font14' v-if='detailData.applyData.state==2'>
                                        <div class='color6'><?php echo Yii::t('leave', 'Copy to:') ?></div>
                                        <div class='flex1  color3 ml10'>
                                            <span v-for='(item,i) in detailData.applyData.cc'>{{detailData.staffInfo[item].name}} <span v-if='i+1<detailData.applyData.cc.length'>、</span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="p15" style='border-top:1px solid #EBEEF5'>
                <div class='flex'>
                    <div class='flex1'>
                        <div  v-if='configPage.role=="hr" && detailData.applyData.state!=-1'>
                            <button type="button" class="btn btn-default" data-dismiss="modal"  @click='addFile(detailData.applyData)'><?php echo Yii::t('leave', 'Material Attachments') ?></button>
                            <button type="button" class="btn btn-default" data-dismiss="modal" @click='cancelList(detailData.applyData.id)'><?php echo Yii::t('leave', 'Revoke Application') ?></button>
                        </div>
                        <div v-else-if='(configPage.userInfo.uid==shenpiState.leave_staff || configPage.userInfo.uid==shenpiState.created_by) && (detailData.applyData.state==1 || detailData.applyData.state==2)'>
                            <button type="button" class="btn btn-default" data-dismiss="modal" v-if='detailData.applyData.state!=0'  @click='addFile(detailData.applyData)'><?php echo Yii::t('leave', 'Material Attachments') ?></button>
                            <button type="button" class="btn btn-default ml16" data-dismiss="modal" v-if='detailData.applyData && detailData.applyData.state==1'  @click='cancelList(detailData.applyData.id)'><?php echo Yii::t('leave', 'Revoke Application') ?></button>
                        </div>
                    </div>
                    <button type="button" class="btn btn-primary pull-right" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                </div>
            </div>
            </div>
        </div>
    </div>
    <!-- 撤销 -->
    <div class="modal fade" id="delModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t('leave', 'Revoke Application') ?></h4>
                </div>
                <div class="modal-body p24" >
                <?php echo Yii::t('leave', 'Proceed to revoke this application?') ?>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" @click='cancelList("del")'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 补充材料 -->
    <div class="modal fade" id="addFileModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t('leave', 'Material Attachments') ?></h4>
                </div>
                <div class="modal-body p24" >
                    <div class='' id='addFileBox'><el-button type="text" icon="el-icon-circle-plus-outline" id='addFile' class='font14 bluebg'><?php echo Yii::t('referral', 'Upload') ?></el-button></div>
                    <div>
                        <div class='' v-if='attachments.img && attachments.img.length>0'>
                            <div class='imgData mr8'  v-for='(list,i) in attachments.img'>
                                <div v-if="list.types=='1'">
                                    <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Uploading");?></div>
                                </div>
                                <div v-else>
                                    <img class='imgList' @click='uploadShowImg(list.url)'  :src="list.url" alt="">
                                    <span aria-hidden="true" class='closeImg' @click.stop='delImg("img",list,i)'>×</span>
                                </div>
                            </div>
                            <template v-if='loadingType==1'>
                                <div class='imgData mr8'  v-for='(list,i) in loadingList'>
                                    <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Upload queue");?></div>
                                </div>
                            </template>
                        </div>
                        <div>
                            <div class='mt16' v-if='attachments.other && attachments.other.length>0'>
                                <div class='flex uploadFile' v-for='(list,index) in attachments.other'>
                                    <span class='glyphicon glyphicon-paperclip mr8'></span>
                                    <span v-if="list.types=='1'"><?php echo Yii::t("directMessage", "Uploading");?></span>
                                    <template v-else>
                                        <a target="_blank" class='flex1'  style='line-height:26px' :href='list.url' v-if='!list.isEdit'>{{list.name}}</a>
                                        <span class='flex1' v-else><input type="input" class='inputStyle'  @keyup.enter="saveFile(list,index)"  v-model='attachmentName' ></span>
                                        <template v-if='!list.isEdit'>
                                            <span class='glyphicon glyphicon-edit icon mr16' v-if='list.url!=""' @click.stop='list.isEdit=true,attachmentName=list.name'></span>
                                            <span class='glyphicon glyphicon-trash icon' v-if='list.url!=""' @click.stop='delImg("link",list,index)'></span>
                                        </template>
                                        <span style='width:90px'  class='text-right inline-block' v-else>
                                            <button type="button" class="btn btn-primary btn-xs" @click='saveFile(list,index)'><?php echo Yii::t("global", "Save");?></button>
                                            <button type="button" class="btn btn-default btn-xs" @click='list.isEdit=false'><?php echo Yii::t("global", "Cancel");?></button>
                                        </span>
                                    </template>
                                </div>
                            </div>
                            <template v-if='loadingType==2'>
                                <div class='flex uploadFile'  v-for='(list,i) in loadingList'>
                                    <span class='glyphicon glyphicon-paperclip mr8'></span>
                                    <a  class='flex1'  style='line-height:26px' > <?php echo Yii::t("directMessage", "Upload queue");?></a>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" @click='confirmAddFile()'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
var config = {
    runtimes: 'html5,flash,html4', //上传模式,依次退化
    container: '', //上传区域DOM ID，默认是browser_button的父元素，
    browse_button: '', //上传选择的点选按钮，**必需**
    token: '',
    flash_swf_url: '/js/plupload/Moxie.swf', // flash的相对地址
    auto_start: true, //选择文件后自动上传，若关闭需要自己绑定事件触发上传
    // multi_selection: false, //不允许多选
    url:'https://upload-z1.qiniup.com',
    init: {
        'FilesAdded': function(up, files) {
            container.disabledUpload=true
            let fileType=files[0].type.split('/')
            if(fileType[0]=="image"){
                container.loadingType=1
            }else{
                container.loadingType=2
            }
            container.loadingList=files
            up.start();
        },
        BeforeUpload: function(up, file) {
            //设置参数
            up.setOption({
                multipart_params:{token:container.token}
            })
            let fileType=file.type.split('/')
            if(fileType[0]=="image"){
                container.attachments.img.push({types:'1'})
            }else{
                container.attachments.other.push({types:'1',title:' <?php echo Yii::t("directMessage", "Uploading");?>'})  
            }
            container.loadingList.splice(0,1)
        },
        UploadProgress: function(up, file) {
            $('#progress').css('width', file.percent+'%').html(file.percent+'%');
        },
        'FileUploaded': function(up, file, info) {
            var response = eval('('+info.response+')');
            if(info.status == 200){
                let fileType=response.data.mimeType.split('/')
                if(fileType[0]=="image"){
                    container.attachments.img.splice(container.attachments.img.length-1,1)
                    container.attachments.img.push(response.data)
                }else{
                    response.data.isEdit=false
                    container.attachments.other.splice(container.attachments.other.length-1,1)
                    container.attachments.other.push(response.data)
                }
            }
        },
        'Error': function(up, err, errTip) {
            if(err.response){
                var response = eval('('+err.response+')');
            }
            else{
                var response = {message: err.message};
            }
            if(response.error == 'token not specified'){
                up.stop();
                // $.ajax({
                //     url: '<?php echo $this->createUrl("getQiniuToken") ?>',
                //     type: "post",
                //     dataType: 'json',
                //     data: {
                //         linkId: container.handId
                //     },
                //     success: function(data) {
                //         if (data.state == 'success') {
                //             container.qiniuToken=data.data
                //             config['uptoken'] = data.data;
                //             up.setOption("multipart_params", {"token":data.data,})
                //             up.start();
                //         } else {
                //             resultTip({
                //                 error: 'warning',
                //                 msg: data.msg
                //             });
                //         }
                //     },
                //     error: function(data) {

                //     },
                // })
            }
        },
        'UploadComplete': function(up, file) {
            //队列文件处理完毕后,处理相关的事情
        }
    }
};
var height=document.documentElement.clientHeight;
 var container=new Vue({
        el: "#containerData",
        data: {
            localeType:'',
            modalHeight:height-220,
            year:'',
            tableType:'',
            tableData:[],
            CopyPages:{},
            pageNum:'1',
            tablePage:{},
            yearList:[],
            configPage:{},
            addAttrsLeave:[],
            availableDate:{},
            selectedDate: null,
            value:'',
            textarea:'',
            currentStaff:{},
            addTimeListLeave:[],
            balanceData:{},
            typeLeave:'',
            totalDurationLeave:'',
            memoLeave:'',
            searchText:'',
            addAttrsOvertime:[],
            addTimeListOvertime:[],
            memoOvertime:'',
            totalDurationOvertime:'',
            schoolList:[],
            schoolId:'',
            currentDept:{},
            allDept:{},
            dep_name:'',
            staffSelected:[],
            detailData:{},
            addBalanceType:'',
            token:'',
            uploader:[],
            attachments:{
                img:[],
                other:[]
            },
            loadingList:[],
            loadingType:0,
            attachmentName:'',
            shenpiType:'',
            shenpiState:{},
            approverModel:'',
            approverMemo:'',
            delId:'',
            addFileList:{},
            minData:'',
            maxData:'',
            subBtn:false,
            substitute:null,
            choose_time:[],
            calendarDate:false,
            selectedItems:[]
        },
        created: function() {
            this.localeType='<?php echo Yii::app()->language;?>'=='zh_cn'?"zh-cn":"en-us"
            this.getConfig()
        },
        watch:{
        },
        computed: {
            searchStaffList: function() {
                var search = this.searchText;
                var searchVal = ''; //搜索后的数据
                if(search) {
                    searchVal =Object.values(this.currentDept.user_info).filter(function(product) {
                        return Object.keys(product).some(function(key) {
                            return String(product['name'].toLowerCase()).indexOf(search.toLowerCase()) !== -1;
                        })
                    })
                    return searchVal;
                }
                return this.searchStaffList;
            },
        },
        methods: {
            translate(text, ...args) {
                return text.replace(/%s/g, () => args.shift());
            },
            countHour(num){
                if(!num){
                    return
                }
                let b = num / 8
                let c = num % 8
                var text=''
                if(parseInt(b)==0){
                    text=c + ' <?php echo Yii::t('referral', 'Hour') ?>'
                }else if(c==0){
                    text= parseInt(b) + ' <?php echo Yii::t('site', 'Days') ?>'
                }else{
                    text=parseInt(b) + ' <?php echo Yii::t('site', 'Days') ?> ' + c + ' <?php echo Yii::t('referral', 'Hour') ?>'
                }
                return text
            },
            formatDate(dateString){
                const date=dateString+''
                const year = date.slice(0, 4);
                const month = date.slice(4, 6);
                const day = date.slice(6, 8);
                return year+'.'+month+'.'+ day
            },
            getweekday(date){
                var weekArray = new Array("<?php echo Yii::t('attends', 'Sun') ?>", "<?php echo Yii::t('attends', 'Mon') ?>", "<?php echo Yii::t('attends', 'Tue') ?>", "<?php echo Yii::t('attends', 'Wed') ?>", "<?php echo Yii::t('attends', 'Thu') ?>", "<?php echo Yii::t('attends', 'Fri') ?>", "<?php echo Yii::t('attends', 'Sat') ?>");
                var week = weekArray[new Date(date).getDay()];
                return week;
            },
            overtimeValue(id,list){
                if(this.configPage[list]){
                    var list=this.configPage[list].find((item,index,arr)=>{
                        return item.key == id
                    })
                    return list?list.value:''
                } 
            },
            getConfig(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        url:'config',
                        startyear:this.year,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.configPage=data.data
                            if(data.data.yearList.length!=0){
                                data.data.substitute_type.forEach(item => {
                                    item.show=false
                                    item.text=''
                                });
                                that.yearList=data.data.yearList
                                that.choose_time=[]
                                data.data.dateType.forEach(item => {
                                    if(item.choose_time){
                                        that.choose_time.push(item.key)
                                    }
                                });
                                that.year=data.data.startyear?data.data.startyear:data.data.yearList[0].key
                                if(data.data.isApprover){
                                    that.tableType='processList'
                                }else{
                                    that.tableType='staffList'
                                }
                                that.tableLists(that.tableType,'init')
                            }
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            next(index){
                this.pageNum = Number(index) + 1
                this.tableLists(this.tableType)
            },
            prev(index) {
                this.pageNum = Number(index) - 1
                this.tableLists(this.tableType)
            },
            plus(index) { 
                this.pageNum = Number(index)
                this.tableLists(this.tableType)
            },
            tableLists(type,init){
                let that=this
                this.tableType=type
                if(init){
                    this.pageNum='1' 
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        url:type,
                        startyear:this.year,
                        page:this.pageNum
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            if(data.data.itemList){
                                that.tableData=data.data.itemList
                                that.tablePage=data.data
                                that.CopyPages={
                                    count:data.data.last_page,
                                    current_page:data.data.current_page,
                                    total:data.data.total
                                }
                            }else{
                                that.tableData=[]
                                that.tablePage={itemList:[]}
                                that.CopyPages={}
                            }
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            getBalance(id,init){
                this.addBalanceType=init
                this.calendarDate=false
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        url:'balance',
                        startyear:this.year,
                        staff_id:id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.balanceData=data.data
                            that.token=data.data.uploadToken
                            if(data.data.balance.start_date){
                                const startDate=data.data.balance.start_date+''
                                const year = startDate.slice(0, 4);
                                const month = startDate.slice(4, 6);
                                const day = startDate.slice(6, 8);
                                const endDate=data.data.balance.end_date+''
                                const endyear = endDate.slice(0, 4);
                                const endmonth = endDate.slice(4, 6);
                                const endday = endDate.slice(6, 8);
                                that.availableDate={
                                    start: new Date(year, parseInt(month)-1, day),
                                    end: new Date(endyear, parseInt(endmonth)-1, endday)
                                }
                                that.minData=new Date(year, parseInt(month)-1, day)
                                that.maxData=new Date(endyear, parseInt(endmonth)-1, endday)
                            }else{
                                that.availableDate={}
                                that.minData=''
                                that.maxData=''
                            }
                     
                            if(that.uploader.length!=0) {
                                for(var i=0;i<that.uploader.length;i++){
                                that.uploader[i].destroy();
                                }
                            }
                            that.attachments={
                                img:[],
                                other:[]
                            }
                            
                            var u = navigator.userAgent,isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
                            if(init=='leave'){
                                that.typeLeave=''
                               
                                that.calendarDate=true
                                $("#addLeaveModal").modal('show')  
                                that.$nextTick(()=>{
                                    config['container'] = 'leaveBox';
                                    config['token'] = that.token;
                                    config['browse_button'] ='leaveFile';
                                    var uploader=new plupload.Uploader(config);
                                    that.uploader.push(uploader);
                                    uploader.init();
                                    $("#leaveFile").click(function () {
                                        if(isiOS){
                                            $(this).siblings("div").children("input").trigger("click");
                                            return false;
                                        }
                                    });
                                })
                            }
                            if(init=='overtime'){
                                $("#overtimeModal").modal('show')  
                                that.$nextTick(()=>{
                                    config['container'] = 'overtimeBox';
                                    config['token'] = that.token;
                                    config['browse_button'] ='overtimeFile';
                                    var uploader=new plupload.Uploader(config);
                                    that.uploader.push(uploader);
                                    uploader.init();
                                    $("#overtimeFile").click(function () {
                                        if(isiOS){
                                            $(this).siblings("div").children("input").trigger("click");
                                            return false;
                                        }
                                    });
                                })
                            }
                            
                            
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            newLeave(){
                this.addTimeListLeave=[]
                this.addTimeListOvertime=[]
                this.addAttrsLeave=[]
                this.addAttrsOvertime=[]
                this.memoOvertime=''
                this.memoLeave=''
                this.substitute=null
                this.selectedDate=null
                this.totalDurationLeave=''
                this.totalDurationOvertime=''
                this.attachments={
                    img:[],
                    other:[]
                }
                this.selectedItems=[]
                this.configPage.substitute_type.forEach(item => {
                    item.show=false
                    item.text=''
                });
                this.currentStaff=this.configPage.userInfo
                this.getBalance(this.configPage.userInfo.uid,'leave')
            },
            newOvertime(){
                this.addTimeListLeave=[]
                this.addTimeListOvertime=[]
                this.addAttrsLeave=[]
                this.addAttrsOvertime=[]
                this.memoOvertime=''
                this.memoLeave=''
                this.substitute=null
                this.selectedDate=null
                this.totalDurationLeave=''
                this.totalDurationOvertime=''
                this.attachments={
                    img:[],
                    other:[]
                }
                this.selectedItems=[]
                this.configPage.substitute_type.forEach(item => {
                    item.show=false
                    item.text=''
                });
                this.currentStaff=this.configPage.userInfo
                this.getBalance(this.configPage.userInfo.uid,'overtime')
            },
            pickDateLeave(day) {
                if (day.date < this.minData || day.date > this.maxData) {
                    return
                }
                const idx = this.addAttrsLeave.findIndex(d => d.id === day.id);
                if (idx >= 0) {
                    const listidx = this.addTimeListLeave.findIndex(d => d.day === day.id);
                    this.addAttrsLeave.splice(idx, 1);
                    this.addTimeListLeave.splice(listidx, 1);
                    this.countDuration('leave')
                    this.selectedDate=null
                } else {
                    let startDate=new Date(this.selectedDate)
                    var yearStart = startDate.getFullYear();
                    var monthStart =startDate.getMonth();
                    var dayStart = startDate.getDate();
                    this.addTimeListLeave.push({text:this.getweekday(day.id),value:'',day:day.id,time:''})
                    this.addTimeListLeave=this.sortDownDate(this.addTimeListLeave)
                    this.addAttrsLeave.push({
                        highlight: {
                            fillMode: 'solid',
                        },
                        id:day.id,
                        dates: new Date(yearStart, monthStart, dayStart),
                    })
                }
              
            },
            sortDownDate(arr) {
                return arr.sort((a, b) => a.day.localeCompare(b.day)); 
            },
            countDuration(type){
                if(type=='leave'){
                    const arr = this.addTimeListLeave.map(item1 => this.configPage.dateType.find((item2) => {return item1.value!='' && item1.value === item2.key})).filter(item2 => item2 !== undefined);;
                    const result = arr.reduce(function(prev, cur) {
                        return cur.duration + prev;
                    }, 0);
                    if(result==0){
                        this.totalDurationLeave=''
                    }else{
                        this.totalDurationLeave=this.countHour(result)
                    }
                }else{
                    const arr = this.addTimeListOvertime.map(item1 => this.configPage.dateType.find((item2) => {return item1.value!='' && item1.value === item2.key})).filter(item2 => item2 !== undefined);;
                    const result = arr.reduce(function(prev, cur) {
                        return cur.duration + prev;
                    }, 0);
                    if(result==0){
                        this.totalDurationOvertime=''
                    }else{
                        this.totalDurationOvertime=this.countHour(result)
                    }
                }
               
            },
            pickDateOvertime(day){
                if (day.date < this.minData || day.date > this.maxData) {
                    return
                }
                const idx = this.addAttrsOvertime.findIndex(d => d.id === day.id);
                if (idx >= 0) {
                    const listidx = this.addTimeListOvertime.findIndex(d => d.day === day.id);
                    this.addTimeListOvertime.splice(listidx, 1);
                    this.addAttrsOvertime.splice(idx, 1);
                    this.selectedDate=null
                    this.countDuration('overtime')
                }else{
                    let startDate=new Date(this.selectedDate)
                    var yearStart = startDate.getFullYear();
                    var monthStart =startDate.getMonth();
                    var dayStart = startDate.getDate();
                    this.addTimeListOvertime.push({text:this.getweekday(day.id),value:'',day:day.id,overtime_type:''})
                    this.addTimeListOvertime=this.sortDownDate(this.addTimeListOvertime)
                    this.addAttrsOvertime.push({
                        highlight: {
                            fillMode: 'solid',
                        },
                        id:day.id,
                        dates: new Date(yearStart, monthStart, dayStart),
                    })
                }
                
            },
            delLeaveTime(index,item,type){
                if(type=='leave'){
                    const idx = this.addAttrsLeave.findIndex(d => d.id === item.day);
                    this.addTimeListLeave.splice(index, 1); 
                    this.addAttrsLeave.splice(idx, 1); 
                    this.countDuration('leave')
                }else{
                    const idx = this.addAttrsOvertime.findIndex(d => d.id === item.day);
                    this.addTimeListOvertime.splice(index, 1); 
                    this.addAttrsOvertime.splice(idx, 1); 
                    this.countDuration('overtime')
                }
                this.selectedDate=null
            },
            saveLeave(type){
                let that=this
                that.subBtn=true
                var dateList=[]
                var fileList=[]
                this.attachments.img.forEach(list => {
                    fileList.push({file:list.key,type:'img',name:list.name})
                });
                this.attachments.other.forEach(list => {
                    fileList.push({file:list.key,type:'link',name:list.name})
                });
                var data={}
                if(type=='leave'){
                    if(this.memoLeave==''){
                        that.subBtn=false
                        resultTip({
                            error: 'warning',
                            msg: '<?php echo Yii::t('leave', 'Input') ?> <?php echo Yii::t('leave', 'Reason For Leave Application') ?>'
                        });
                        return
                    }
                    this.addTimeListLeave.forEach(list => {
                        dateList.push({type:list.value,date:list.day,duration_start:list.time})
                    });
                    let substitute_info={}
                    if(this.balanceData.substitute){
                        substitute_info={
                            need:this.substitute,
                            list:[]
                        }
                        if(this.substitute==null){
                            that.subBtn=false
                            resultTip({
                                error: 'warning',
                                msg: '<?php echo Yii::t('leave', 'Course Substitute Required?') ?>'
                            });
                            return
                        }
                        for(var i=0;i<this.configPage.substitute_type.length;i++){
                            let list=this.configPage.substitute_type[i]
                            if(this.selectedItems.includes(list.value)){
                                if(list.text.trim()==''){
                                    that.subBtn=false
                                    resultTip({
                                        error: 'warning',
                                        msg: '<?php echo Yii::t('leave', 'Input') ?> '+list.title
                                    });
                                    return
                                }
                                substitute_info.list.push({
                                    value:list.value,
                                    desc:list.text
                                })
                            }
                        }
                        data={
                            url:'apply',
                            startyear:this.year,
                            staff_id:this.currentStaff.uid,
                            type:this.typeLeave,
                            memo:this.memoLeave,
                            dateList:dateList,
                            fileList:fileList,
                            substitute_info:substitute_info
                        }
                    }else{
                        data={
                            url:'apply',
                            startyear:this.year,
                            staff_id:this.currentStaff.uid,
                            type:this.typeLeave,
                            memo:this.memoLeave,
                            dateList:dateList,
                            fileList:fileList,
                        }
                    }
                    
                }else{
                    this.addTimeListOvertime.forEach(list => {
                        dateList.push({overtime_type:list.overtime_type,date:list.day,type:list.value})
                    });
                    data={
                        url:'apply',
                        startyear:this.year,
                        staff_id:this.currentStaff.uid,
                        type:'jiaban',
                        memo:this.memoOvertime,
                        dateList:dateList,
                        fileList:fileList
                    }
                    if(this.memoOvertime==''){
                        that.subBtn=false
                        resultTip({
                            error: 'warning',
                            msg: '<?php echo Yii::t('leave', 'Input') ?> <?php echo Yii::t('leave', 'Reason(s) for OT') ?>'
                        });
                        return
                    }
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data: data,
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg: data.state
                            });
                            that.addTimeListLeave=[]
                            that.addTimeListOvertime=[]
                            that.addAttrsLeave=[]
                            that.addAttrsOvertime=[]
                            that.memoOvertime=''
                            that.memoLeave=''
                            that.substitute=null
                            that.selectedDate=null
                            that.totalDurationLeave=''
                            that.totalDurationOvertime=''
                            that.attachments={
                                img:[],
                                other:[]
                            }
                            that.configPage.substitute_type.forEach(item => {
                                item.show=false
                                item.text=''
                            });
                            that.typeLeave=''
                            if(type=='leave'){
                                that.calendarDate=false
                                $("#addLeaveModal").modal('hide')
                            }else if(type=='overtime'){
                                $("#overtimeModal").modal('hide')
                            }
                            that.tableLists(that.tableType)
                            that.subBtn=false
                        }else{
                            that.subBtn=false
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        that.subBtn=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            getSchoolData(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getAllDepartment") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        school_id:this.schoolId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.currentDept=data.data
                            that.allDept=Object.assign(that.allDept, data.data.user_info)
                            
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            cutover(){
                let that=this
                if(Object.keys(that.schoolList).length!=0){
                    $("#addStaffModal").modal('show')  
                    return
                }
                that.getSchoolData()
                $.ajax({
                    url: '<?php echo $this->createUrl("schoolList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.schoolList=data.data.list  
                            that.schoolId=data.data.current_school 
                            
                            $("#addStaffModal").modal('show')  
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            showDepName(list){
                if(this.dep_name==list.dep_name){
                    this.dep_name=''
                    return
                }
                this.dep_name=list.dep_name
            },
            assignStaff(list,index,idx){
                if(this.staffSelected.length==1){
                    resultTip({
                        error: 'warning',
                        msg: '<?php echo Yii::t('leave', 'You can only select one staff.') ?>'
                    });
                    return
                }
                if(idx=='search'){
                    this.searchStaffList[index].disabled=true
                }else{
                    Vue.set(this.currentDept.user_info[list.uid], 'disabled', true);
                }
                this.staffSelected.push(list.uid)
            },
            Unassign(list,index){
                if(this.currentDept.user_info[list]){
                    Vue.set(this.currentDept.user_info[list], 'disabled', false);
                }
                this.staffSelected.splice(index,1)
            },
            confirmSatff(){
                this.currentStaff=this.allDept[this.staffSelected[0]]
                this.getBalance(this.staffSelected[0],this.addBalanceType)
                $("#addStaffModal").modal('hide')  
            },
            approval(row,type){
                let that=this
                this.shenpiType=type
                this.shenpiState=row
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        url:'detail',
                        id:row.id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            data.data.itemData.forEach(item => {
                                item.settlement={}
                            });
                            data.data.fileData={
                                img:[],
                                other:[]
                            }
                            if(data.data.fileList.length!=0){
                                data.data.fileList.forEach(item => {
                                   if(item.type=='img'){
                                        data.data.fileData.img.push(item)
                                   }else{
                                        data.data.fileData.other.push(item)
                                   }
                                });
                            }
                           
                            that.detailData=data.data
                            if(type=='shenpi'){
                                $("#approvalModal").modal('show')  
                                that.approverModel=''
                                that.approverMemo=''
                            }else{
                                $("#detailModal").modal('show')  
                            }
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            getSummariesDetail(){
                this.$nextTick(() => {
                    const tds = document.querySelectorAll('.summaryTable .el-table__footer tr>td');
                    const summaryTables = document.querySelectorAll('.summaryTables .el-table__footer tr>td');
                    if(tds.length>0){
                        tds[1].colSpan=3;
                        tds[2].style.display='none'
                        tds[3].style.display='none'
                    }
                    if(summaryTables.length>0){
                        summaryTables[1].colSpan=3;
                        summaryTables[2].style.display='none'
                        summaryTables[3].style.display='none'
                    }
                }, 1000)
                if(this.detailData.modified.modified_uid){
                    return ['<?php echo Yii::t('leave', 'Total Time') ?> （'+this.configPage.leaveConfig[this.detailData.applyData.type].title+'）',this.detailData.applyData.duration_format]
                }else{
                    return ['<?php echo Yii::t('leave', 'Total Time') ?>',this.detailData.applyData.duration_format]
                }
            },
            getSummaries(param){
                this.$nextTick(() => {
                    const tds = document.querySelectorAll('.summaryTable .el-table__footer tr>td');
                    const summaryTables = document.querySelectorAll('.summaryTables .el-table__footer tr>td');
                    if(tds.length>0){
                        tds[1].colSpan=3;
                        tds[2].style.display='none'
                        tds[3].style.display='none'
                    }
                    if(summaryTables.length>0){
                        summaryTables[1].colSpan=3;
                        summaryTables[2].style.display='none'
                        summaryTables[3].style.display='none'
                    }
                }, 1000)
                return ['<?php echo Yii::t('leave', 'Total Time') ?>',this.detailData.applyData.duration_format]
            },
            saveApproval(){
                if(this.approverModel==''){
                    resultTip({
                        error: 'warning',
                        msg: '<?php echo Yii::t('reg', 'Please select') ?> <?php echo Yii::t('leave', 'Approver') ?>'
                    });
                    return
                }
                var data={}
                if(this.shenpiState.type=="jiaban"){
                    const itemDateList=[]
                    for(let i=0;i<this.detailData.itemData.length;i++){
                        if(!this.detailData.itemData[i].settlement.key){
                            if(this.approverModel=='1'){                           
                                resultTip({
                                    error: 'warning',
                                    msg: '<?php echo Yii::t('reg', 'Please select') ?> <?php echo Yii::t('leave', 'Payment Option') ?>'
                                });
                                return
                            }
                        }
                        itemDateList.push({id:this.detailData.itemData[i].id,overtime_settlement:this.detailData.itemData[i].settlement.key})
                    }
                    data={
                        url:this.approverModel=='1'?'pass':this.approverModel=='2'?"refuse":'',
                        id:this.shenpiState.id,
                        memo:this.approverMemo,
                        itemDateList:itemDateList
                    }
                }else{
                    data={
                        url:this.approverModel=='1'?'pass':this.approverModel=='2'?"refuse":'',
                        id:this.shenpiState.id,
                        memo:this.approverMemo
                    }
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data:data,
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg: data.state
                            });
                            that.tableLists(that.tableType)
                            $("#approvalModal").modal('hide')  
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            cancelList(id){
                if(id!='del'){
                    this.delId=id
                    $("#delModal").modal('show')  
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        url:'cancel',
                        id:this.delId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg: data.state
                            });
                            that.tableLists(that.tableType)
                            $("#delModal").modal('hide')  
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            addFile(list){
                this.addFileList=list
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        url:'balance',
                        startyear:this.year,
                        staff_id:list.leave_staff
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            $("#addFileModal").modal('show')  
                            that.token=data.data.uploadToken
                            if(that.uploader.length!=0) {
                                for(var i=0;i<that.uploader.length;i++){
                                that.uploader[i].destroy();
                                }
                            }
                            that.attachments={
                                img:[],
                                other:[]
                            }
                            var u = navigator.userAgent,isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
                            that.$nextTick(()=>{
                                config['container'] = 'addFileBox';
                                config['token'] = that.token;
                                config['browse_button'] ='addFile';
                                var uploader=new plupload.Uploader(config);
                                that.uploader.push(uploader);
                                uploader.init();
                                $("#addFile").click(function () {
                                    if(isiOS){
                                        $(this).siblings("div").children("input").trigger("click");
                                        return false;
                                    }
                                });
                            })
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            confirmAddFile(){
                let that=this
                var fileList=[]
                this.attachments.img.forEach(list => {
                    fileList.push({file:list.key,type:'img',name:list.name})
                });
                this.attachments.other.forEach(list => {
                    fileList.push({file:list.key,type:'link',name:list.name})
                });
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        url:'addFile',
                        id:this.addFileList.id,
                        fileList:fileList
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            $("#addFileModal").modal('hide')  
                            resultTip({
                                error: 'warning',
                                msg: data.state
                            });
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            showImg(id,list){
                var viewer = new Viewer(document.getElementById(id),{
                    fullscreen: false,
                    title:false,
                    scalable:false,
                    url: 'data-original',
                    show:function(){ 
                        if(list.length==1){
                            $('.viewer-prev').hide()
                            $('.viewer-next').hide()
                        }
                    },
                    hide:function(){ 
                        viewer.destroy()
                    }
                });
                $("#"+id).click();
            },
            delImg(type,list,index){
                if(type=='img'){
                    this.attachments.img.forEach((item,index) => {
                        if (item._id==list._id) {
                            this.attachments.img.splice(index, 1)
                        } 
                    })
                }else{
                    this.attachments.other.forEach((item,index) => {
                        if (item._id==list._id) {
                            this.attachments.other.splice(index, 1)
                        } 
                    })
                }
            },
            saveFile(list,index) {
                Vue.set(this.attachments.other[index], 'name',this.attachmentName);
                Vue.set(this.attachments.other[index], 'isEdit', false);
            },
        },
       
    })

    
</script>
<style>
    .fontNormal{
        font-weight:normal !important
    }
    .groupList{
        padding:10px;
        align-items:center;
        border: 1px solid #fff;
    }
    .groupList:hover,.currentGroup{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        border: 1px solid #4D88D2;
        cursor: pointer;
    }
    .titleGroup{
        font-size:14px;
        font-weight: 500;
        color: #333333;
        margin-bottom:20px
    }
    .img42{
        width: 42px;
        height: 42px;
        object-fit: cover;
    }
    .text_overflow {
        overflow: hidden; 
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .flex1Text{
        overflow: hidden; 
        text-overflow: ellipsis;
        white-space: nowrap;
        width:0
    }
    .width100{
        width:100%
    }
    .staffData{
        width:100%;
        background-color: #FFF;
        border-radius: 4px;
        border: 1px solid #DCDFE6;
        box-sizing: border-box;
        color: #606266;
        font-size: inherit;
        min-height: 40px;
        padding: 0 15px;
        line-height: 40px;
        color: #C0C4CC;
        font-size: 14px;
        cursor: pointer;
    }
    .staff_ids{
        border-radius: 4px;
        border: 1px solid #DCDFE6;
        cursor: pointer;
        min-height: 40px;
    }
    .staff_ids .flex1{
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
        display: flex;
    }
    .downIcon{
        width: 25px;
        color: #C0C4CC;
        font-size: 14px;
    }
    .borderLeftpos {
        display: inline-block;
        border-left: 1px solid #E4E7ED;
        height: 100%;
        width: 1px;
        position: absolute;
        left: 50%;
    }
    .allCheck{
        position: absolute;
        right: 10px;
        top: 0;
    }
    .border{
        border: 1px solid #DCDFE6;
        padding: 16px;
        border-radius: 4px;
    }
    .listMedia{
        padding:8px;
        border: 1px solid #fff;
    }
    .listMedia:hover{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        border: 1px solid #4D88D2;
        cursor: pointer;
    }
    .titleLeave{
        font-size:14px;
        color:#333333;
        margin-top:24px;
        font-weight:bold
    }
    .dateList{
        background: #F7F7F8;
        padding:8px 12px;
        border-radius: 4px;
        align-items:flex-start
    }
    .approvalTitle .line{
        width: 6px;
        height: 14px;
        background: #4D88D2;
        border-radius: 1px;
    }
    .approvalTitle .flex1{
        font-size:14px;
        color:#333333;
        font-weight:bold
    }
    .approvalData{
        padding:16px;
        background: #FAFAFA;
        border-radius: 2px;
    }
    .detailWidth{
        color:#666666;
        font-size:14px;
        margin-bottom:12px
    }
    .labelWarning{
        padding:2px 5px;
        margin-left:10px
    }
    .imgList{
        width: 72px;
        height: 72px;
        border-radius: 4px;
        object-fit: cover;
        margin-bottom:8px
    }
    .uploadLoading{
        width: 72px;
        height: 72px;
        background: #D9D9D9;
        border-radius: 4px;
        line-height: 72px;
        text-align: center;
    }
    .imgData{
        position: relative;
        display: inline-block;
    }
    .imgData span{    
        position: absolute;
        right: 3px;
        top: 3px;
        background: #333333;
        opacity: 1;
        color: #fff;
        width: 17px;
        height: 17px;
        border-radius: 50%;
        font-weight: 200;
        text-align: center;
        line-height: 15px;
        font-size: 19px;
    }
    .uploadFile {
        font-size:14px;
        align-items: center;
        padding:5px
    }
    .uploadFile .icon{
        display:none
    }
    .uploadFile:hover{
        background: #F7F7F8;

    }
    .uploadFile:hover .icon{
        display:block
    }
    .uploadFile a{
        color:#4D88D2
    }
    .colorRed{
        color:rgb(217, 83, 79)
    }
    .radio-inline, .checkbox-inline{
        line-height:1.5 !important
    }
    .mt0{
        margin-top:0
    }
    .dot{
        position: absolute;
        width: 8px;
        height: 8px;
        background: #4D88D2;
        border-radius: 50%;
        top: 5px;
    }
    .borderLine{
        border-left: 1px dashed #ccc;
        position: absolute;
        height: 100%;
        left: 3px;
        top: 10px;
    }
    .width20{
        position: relative;
        width:20px
    }
    .leaveFile{
        padding:10px;
        border-radius: 4px;
        border: 1px dashed #4D88D2;
        color:#4D88D2;
        text-align:center;
        font-size:14px;
        cursor: pointer;
    }
    .substituteInfo{
        width:115px
    }
    .setCell .el-table__cell{
        padding:8px 0
    }
    .history{
        padding:32px 16px 10px;
        background: #F7F7F8;
        border-radius: 4px;
        margin-top:20px;
        background: rgba(240,173,78,0.1);
        border: 1px solid #F0AD4E;
        position: relative;
    }
    .historyTitle{
        position: absolute;
        left: 0;
        top: 0;
        padding: 3px 12px;
        background: #F5A429;
        color: #fff;
        border-radius: 2px 0px 4px 0px;
    }
    .historyDate{
        padding:4px 6px;
        font-size: 12px;
        color: #333333;
        line-height: 12px;
        margin-right:12px;
        background: #FFFFFF;
        border-radius: 2px;
        display: inline-block;
        margin-bottom: 6px;
    }
    .historyBorder{
        border-left: 1px solid #999999;
        margin-left:5px;
        padding-left:5px
    }
</style>
