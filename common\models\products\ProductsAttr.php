<?php

/**
 * This is the model class for table "ivy_products_attr".
 *
 * The followings are the available columns in table 'ivy_products_attr':
 * @property integer $id
 * @property integer $pid
 * @property string $type
 * @property string $attr1
 * @property string $attr2
 * @property string $attr3
 * @property string $attr4
 * @property string $attr5
 * @property double $unit_price
 * @property double $scribing_price
 * @property double $status
 */
class ProductsAttr extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_products_attr';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('pid, type', 'required'),
			array('pid', 'numerical', 'integerOnly'=>true),
			array('unit_price', 'numerical'),
			array('scribing_price', 'numerical'),
			array('type, attr1, attr2, attr3, attr4, attr5, status', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, pid, type, attr1, attr2, attr3, attr4, attr5, unit_price, scribing_price, status', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'products' => array(self::BELONGS_TO, 'Products', array('pid'=>'id')),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'pid' => 'Pid',
			'type' => 'Type',
			'attr1' => 'Attr1',
			'attr2' => 'Attr2',
			'attr3' => 'Attr3',
			'attr4' => 'Attr4',
			'attr5' => 'Attr5',
			'unit_price' => 'Unit Price',
			'scribing_price' => 'Scribing Price',
			'status' => 'Status',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('pid',$this->pid);
		$criteria->compare('type',$this->type,true);
		$criteria->compare('attr1',$this->attr1,true);
		$criteria->compare('attr2',$this->attr2,true);
		$criteria->compare('attr3',$this->attr3,true);
		$criteria->compare('attr4',$this->attr4,true);
		$criteria->compare('attr5',$this->attr5,true);
		$criteria->compare('unit_price',$this->unit_price);
		$criteria->compare('scribing_price',$this->scribing_price);
		$criteria->compare('status',$this->status,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return ProductsAttr the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
