<?php

/**
 * This is the model class for table "ivy_child_daily_sign_log".
 *
 * The followings are the available columns in table 'ivy_child_daily_sign_log':
 * @property integer $id
 * @property string $schoolid
 * @property integer $classid
 * @property integer $childid
 * @property integer $sign_status
 * @property integer $sign_start
 * @property integer $sign_end
 * @property integer $update_by
 * @property integer $update_at
 */
class ChildDailySignLog extends CActiveRecord
{
	const STATUS_SIGN = 9;
	const STATUS_SICK = 10;
	const STATUS_OTHER = 30;
	const STATUS_LATE = 40;

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_child_daily_sign_log';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('schoolid, classid, childid, sign_status, sign_start, sign_end, update_by, update_at', 'required'),
			array('classid, childid, sign_status, sign_start, sign_end, update_by, update_at', 'numerical', 'integerOnly'=>true),
			array('schoolid', 'length', 'max'=>10),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, schoolid, classid, childid, sign_status, sign_start, sign_end, update_by, update_at', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'child'=>array(self::HAS_ONE, 'ChildProfileBasic', array('childid'=>'childid')),
			'user' => array(self::BELONGS_TO, 'User', array('update_by'=>'uid')),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'schoolid' => 'Schoolid',
			'classid' => 'Classid',
			'childid' => 'Childid',
			'sign_status' => 'Sign Status',
			'sign_start' => 'Sign Start',
			'sign_end' => 'Sign End',
			'update_by' => 'Update By',
			'update_at' => 'Update Timestamp',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('sign_status',$this->sign_status);
		$criteria->compare('sign_start',$this->sign_start);
		$criteria->compare('sign_end',$this->sign_end);
		$criteria->compare('update_by',$this->update_by);
		$criteria->compare('update_at',$this->update_at);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return ChildDailySignLog the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * 签到日志
	 *
	 * @param [type] $schoolid
	 * @param [type] $classid
	 * @param [type] $childid
	 * @param [type] $status
	 * @param [int] $start
	 * @param [int] $end
	 * @param [type] $uid
	 * @return void
	 */
	public static function signLog($schoolid, $classid, $childid, $status, $start, $end, $uid)
	{
		$model = new ChildDailySignLog();
		$model->schoolid = $schoolid;
		$model->classid = $classid;
		$model->childid = $childid;
		$model->sign_status = $status;
		$model->sign_start = date('Ymd', $start);
		$model->sign_end = date('Ymd', $end);
		$model->update_by = $uid;
		$model->update_at = time();
		if ($model->save()) {
			return true;
		}
		return false;
	}

	public function statusText()
	{
		$signStatus = array(
            '10' => Yii::t('campus', 'mark sick leave'),
            '11' => Yii::t('campus', '标记线上出勤'),
            '12' => Yii::t('campus', '取消线上出勤'),
            '20' => Yii::t('campus', 'mark other leave'),
            '30' => '其它',
            '40' => Yii::t('campus', 'mark tardy'),
            '50' => '早退',
            '60' => Yii::t('campus', 'mark present'),
            '61' => Yii::t('campus', '标记旷课'),
            '62' => Yii::t('campus', '撤销旷课'),
            '19' => Yii::t('campus', 'cancel sick leave'),
            '29' => Yii::t('campus', 'cancel other leave'),
            '39' => '取消其它',
            '49' => Yii::t('campus', 'cancel tardy'),
            '59' => '取消早退',
            '69' => Yii::t('campus', 'cancel present'),
		);
		if (isset($signStatus[$this->sign_status])) {
			return $signStatus[$this->sign_status];
		} else {
			return '';
		}
	}
}
