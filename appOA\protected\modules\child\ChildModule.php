<?php

class ChildModule extends CWebModule
{
	public $defaultController = 'child/list';
	public function init()
	{
		// this method is called when the module is being created
		// you may place code here to customize the module or the application

		// import the module-level models and components
		$this->setImport(array(
			'child.models.*',
			'child.components.*',
		));
	}

	public function beforeControllerAction($controller, $action)
	{
		if(parent::beforeControllerAction($controller, $action))
		{
			// this method is called before any module controller action is performed
			// you may place customized code here
			if(strtolower($controller->getId().'/'.$action->getId())!='message/error'){
				if(!Yii::app()->user->checkAccess('oChildGeneralView')){
					$controller->forward('/child/message/error');
					Yii::app()->end();
				}
			}
			return true;
		}
		else
			return false;
	}
}
