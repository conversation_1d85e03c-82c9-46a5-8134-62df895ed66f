<?php

/**
 * This is the model class for table "ivy_user_notification".
 *
 * The followings are the available columns in table 'ivy_user_notification':
 * @property integer $id
 * @property integer $staff_uid
 * @property integer $createdflagid
 * @property string $title
 * @property integer $type
 * @property integer $status
 * @property string $data
 * @property integer $created
 */
class UserNotification extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return UserNotification the static model class
	 */
    const WAITING_STATUS = 0;
    const END_STATUS = 1;
    public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_user_notification';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('staff_uid, flagid, type, created', 'required'),
			array('staff_uid, flagid, status, created', 'numerical', 'integerOnly'=>true),
			array('title', 'length', 'max'=>255),
			array('type', 'length', 'max'=>50),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, staff_uid, flagid, title, type, status, data, created', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'staff_uid' => 'Staff Uid',
			'flagid' => 'Flagid',
			'title' => 'Title',
			'type' => 'Type',
			'status' => 'Status',
			'data' => 'Data',
			'created' => 'Created',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('staff_uid',$this->staff_uid);
		$criteria->compare('flagid',$this->flagid);
		$criteria->compare('title',$this->title,true);
		$criteria->compare('type',$this->type);
		$criteria->compare('status',$this->status);
		$criteria->compare('data',$this->data,true);
		$criteria->compare('created',$this->created);

		return new CActiveDataProvider(get_class($this), array(
			'criteria'=>$criteria,
		));
	}
    
     /**
     * 取得某个用户按类型统计的条数
     * @param int  $staffUid 员工ID
     * @return array e.g. array('hr_leave'=>2);
     */
    static public function getDataByUserCountType($userId){
        $data = array();
        $result = Yii::app()->db->createCommand()
                ->select()
                ->from('ivy_user_notification')
                ->where('staff_uid=:staff_uid and status=:status', array(':staff_uid' => $userId, ':status' => self::WAITING_STATUS))
                ->queryAll();
        if (count($result)) {
            foreach ($result as $val) {
                $data[$val['type']]['count'] = (isset($data[$val['type']]['count'])) ? $data[$val['type']]['count']+1 :1;
            }
        }
        return $data;
    }
    
    /**
     * 取消某个用户某种类型的业务通知
     * @param int $staffUid 需要取消的用户ID
     * @param int $flagId   相关联的业务ID
     * @param int $type     通知类型
     * @return boolean
     */
    static public function deleteDataByUser($staffUid,$flagId,$type){
        $ruselt = FALSE;
        $crite = new CDbCriteria;
        $crite->compare('staff_uid', $staffUid);
        $crite->compare('flagid', $flagId);
        $crite->compare('type', $type);
        if (self::model()->deleteAll($crite))
        {
             $ruselt = TRUE;
        }
        return $ruselt;
    }
}