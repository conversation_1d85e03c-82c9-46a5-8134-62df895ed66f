<style>
    <?php if(Yii::app()->language == 'en_us'):?>
    /*@font-face {*/
    /*    font-family: 'Myriad Pro';*/
    /*    src: url('*/<?php //echo Yii::app()->themeManager->baseUrl; ?>/*/base/fonts/MyriadPro-Regular.woff2') format('woff2');*/
    /*    font-weight: normal;*/
    /*    font-style: normal;*/
    /*    font-display: swap;*/
    /*}*/
    body {
        margin: 0;
        padding: 0;
        font-family: 'Myriad Pro';
        font-weight: normal;
        font-style: normal;
    }
    <?php else:?>
    /*@font-face {*/
    /*    font-family: 'SourceHanSans';*/
    /*    src: url('*/<?php //echo Yii::app()->themeManager->baseUrl; ?>/*/base/fonts/SourceHanSansCN-VF.otf.woff2') format('woff2');*/
    /*    font-weight: normal;*/
    /*    font-style: normal;*/
    /*    font-display: swap;*/
    /*}*/
    body {
        margin: 0;
        padding: 0;
        font-family: '思源黑体 CN';
        font-weight: normal;
        font-style: normal;
    }
    <?php endif;?>
    .view {
        margin: 0 auto;
    }
    .text-center {
        text-align: center;
    }
    .page {
        width: 210mm;
        min-height: 297mm;
        margin: 10mm auto;
        background: white;
        box-shadow: 0 0 0.5cm rgba(0,0,0,0.5);
    }
    .split{
        page-break-after: always;
    }
    .clearfix:before {
        content: " ";
        display: table;
    }
    .clearfix:after {
        clear: both;
    }
    .card-box {
        width: 142mm;
        height: 106mm;
        margin: 0 auto;
        position: relative;
    }
    .card-bg {
        width: 142mm;
        height: 106mm;
        position: absolute;
        left: 0;
        top: 0;
    }
    .card-box .card-content {
        width: 136mm;
        height: 101mm;
        position: absolute;
        left: 3mm;
        top: 3mm;
        display: flex;
        overflow: hidden;
    }
    .box-half {
        width: 68mm;
    }
    .box-left {
        padding-left: 6mm;
        padding-top: 9mm;
    }
    .child-avatar {
        width: 25mm;
        height: 25mm;
        margin-bottom: 3mm;
        border-radius: 1mm;
    }
    .text-1 {
        font-size: 3mm;
        font-weight: bold;
        margin-bottom: 2mm;
    }
    .text-2 {
        font-size: 2.4mm;
        width: 25mm;
        height: 4mm;
        margin-bottom: 6mm;
    }
    .text-3 {
        font-size: 3.5mm;
        font-weight: bold;
        margin-bottom: 2mm;
    }
    .sub-box-1 {
        float: left;
        margin-right: 6mm;
    }
    .sub-box-2 {
        margin-left: 18mm;
    }
    .sub-box-3 {
        color: rgb(255, 255, 255);
        font-size: 4.2mm;
        position: absolute;
        left: 72mm;
        bottom: 4mm;
        height: 4.2mm;
    }
    .parent-avatar {
        margin: 25mm 18mm 3mm 0;
        width: 31mm;
        height: 31mm;
        border-radius: 1mm;
        object-fit: cover;
    }
    .card-box-2 {
        width: 74mm;
        height: 106mm;
        position: relative;
        margin:0 auto
    }
    .card-2-bg {
        width: 74mm;
        height: 106mm;
    }
    .card-2-content {
        position: absolute;
        left: 3mm;
        top: 3mm;
        width: 68mm;
        height: 101mm;
    }
    .card-2-content-1 {
        position: absolute;
        left: 9mm;
        top: 9mm;
    }
    .warp-2 {
        width: 160mm;
        margin: 0 auto;
    }
    .page-1 {
        display: flex;
        flex-wrap: wrap;
        /*justify-content: space-between;*/
    }
    .sub-box-4 {
        color: rgb(255, 255, 255);
        font-size: 4.2mm;
        position: absolute;
        left: 4mm;
        bottom: 4mm;
        height: 4.7mm;
    }
    .tips-block {
        text-align: center;
        font-size: 14px;
        background-color: #ffecba;
        top: 0;
        left: 0;
        width: 100%;
        height: 44px;
        line-height: 44px;
        z-index: 9;
    }
    .mb16 {
        margin-bottom: 16px;
    }
    .mt-16 {
        margin-top: -16px;
    }
    .web-only input {
        padding: 4px;
        width: 66px;
    }
    .web-only span{
        font-size: 12px;
        color: #666;
        margin-left: 16px;
    }
    @media print {
        .web-only {
            display: none;
        }
    }
</style>
<?php 
    function schoolYearLess($schoolYear)
    {
        if ($schoolYear) {
            $schoolYear = trim($schoolYear);
            return substr($schoolYear, 0, 5) . substr($schoolYear, 7);
        }
        return $schoolYear;
    }
?>

<div class="container-fluid">
<div class="tips-block mb16 web-only">使用本打印功能，请确保您的电脑已安装下列字体 <a href="http://mega.ivymik.cn/fonts%2FMyriadPro-Regular.otf">MyriadPro</a>&nbsp;&nbsp;<a href="http://mega.ivymik.cn/fonts%2FSourceHanSansCN-Regular_0.otf">思源黑体</a> （下载后双击打开进行安装）</div>
    <div class="row">
        <div id='print' class=" pb5">
            <?php foreach ($data as $item) : ?>
                <div class="card-box-2">
                    <img src="http://m2.files.ivykids.cn/cloud01-file-5FpwBl_RlIsRfqfGEK9uo9k8dF3Al.jpg" class="card-2-bg" alt="">
                    <div class="card-2-content">
                        <div class="sub-box-2">
                            <img src="<?php echo $item['photo'];?>" alt="" class="parent-avatar">
                            <?php if(Yii::app()->language == 'en_us'):?>
                                <div class="text-3"><?php echo Yii::t('global','Name') . Yii::t('global',': ') ;?><?php echo $item['nick'] . ' ' . $item['last_name_en']; ?></div>
                            <?php else:?>
                                <div class="text-3"><?php echo Yii::t('global','Name') . Yii::t('global',': ') ;?><?php echo $item['cn_name'] . ' ' . $item['nick']; ?></div>
                            <?php endif;?>
                            <div class="text-3"><?php echo Yii::t('child','Class') . Yii::t('global',': ');?><?php echo schoolYearLess($item['class']); ?></div>
                            <div class="text-3"><?php echo Yii::t('site','ID') . Yii::t('global',': ') ;?><?php echo $item['id']; ?></div>
                        </div>
                        <svg class="sub-box-4">
                            <text font-size="4.2mm" y="14" x="0" fill="#ffffff">
                             <?php echo $item['grade_group_cn']; ?> <?php echo $item['grade_group_en']; ?>                   </text>
                        </svg>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>
<script>

</script>