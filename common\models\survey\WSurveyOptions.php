<?php

/**
 * This is the model class for table "ivy_survey_options".
 *
 * The followings are the available columns in table 'ivy_survey_options':
 * @property integer $id
 * @property integer $group_id
 * @property string $title_cn
 * @property string $title_en
 * @property string $option_value
 * @property string $schoolid
 * @property integer $weight
 * @property integer $status
 * @property integer $update_user
 * @property integer $update_time
 */
class WSurveyOptions extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return SurveyOptions the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_survey_options';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('group_id, weight, status, update_user, update_time', 'numerical', 'integerOnly'=>true),
			array('title_cn, title_en, schoolid', 'length', 'max'=>255),
			array('option_value', 'length', 'max'=>25),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, group_id, title_cn, title_en, option_value, schoolid, weight, status, update_user, update_time', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'group_id' => 'Group',
			'title_cn' => 'Title Cn',
			'title_en' => 'Title En',
			'option_value' => 'Option Value',
			'schoolid' => 'Schoolid',
			'weight' => 'Weight',
			'status' => 'Status',
			'update_user' => 'Update User',
			'update_time' => 'Update Time',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('group_id',$this->group_id);
		$criteria->compare('title_cn',$this->title_cn,true);
		$criteria->compare('title_en',$this->title_en,true);
		$criteria->compare('option_value',$this->option_value,true);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('weight',$this->weight);
		$criteria->compare('status',$this->status);
		$criteria->compare('update_user',$this->update_user);
		$criteria->compare('update_time',$this->update_time);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

    public function getTitle($status = false)
    {
        if($status){
            $title = ( Yii::app()->language == "zh_cn" ) ? $this->title_cn . ' ' . $this->title_en : $this->title_en . ' ' . $this->title_cn;
        }else{
            $title = ( Yii::app()->language == "zh_cn" ) ? $this->title_cn : $this->title_en;
        }
        return $title;
    }
}