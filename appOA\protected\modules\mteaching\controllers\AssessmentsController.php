<?php

class AssessmentsController extends TeachBasedController
{

    // 访问action的初级权限
    public $actionAccessAuths = array(
        'index' => 'o_T_Access',
        'yearClass'  => 'o_T_Access',
        'getAssessmentByClass'  => 'o_T_Access',
        'saveComment'  => 'o_T_Access',
        'batOnline'      => 'o_T_Access',
        'uploadToken'      => 'o_T_Access',
        'renameAttachment'      => 'o_T_Access',
        'sortAttachments'      => 'o_T_Access',
        'deleteAttachment'      => 'o_T_Access',
    );

    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'teaching';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Teaching Tasks');

        //初始化选择校园页面
//        $this->branchSelectParams['hideKG'] = true;
        $this->branchSelectParams['urlArray'] = array('//mteaching/assessments/index', 'type' => $_GET['type']);

        $mapCampus = array('BJ_DS', 'BJ_SLT', 'BJ_QFF', 'BJ_IASLT', 'BJ_OE', 'TJ_EB', 'NB_FJ');
        foreach ($this->accessBranch as $key => $item) {
            if (!in_array($item, $mapCampus)) {
                unset($this->accessBranch[$key]);
            }
        }

        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl() . '/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/vue2.js');
        // 七牛上传所需文件
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/plupload/plupload.full.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/qiniu.min.js');
        // 引入图表所需文件
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/echarts.min.js');
    }

    public function actionIndex()
    {
        $this->render('index');
    }

    public function actionYearClass()
    {
        $calendarYids = $this->getCalendars();
        $startyear = $this->calendarStartYear[$calendarYids['currentYid']];
        $schoolId = $this->branchId;
        $res = CommonUtils::requestDsOnline('assessments/indexData/' . $schoolId . '/' . $startyear);
        if ($res['code'] == 0) {
            // 获取管理的校园
            parent::initExt();
            if ($this->myClasses) {
                $newClassList = array();
                foreach ($res['data']['classList'] as $item) {
                    if (in_array($item['id'], $this->myClasses)) {
                        $newClassList[] = $item;
                    }
                }
                $res['data']['classList'] = $newClassList;
            }
            $this->addMessage("state", "success");
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionGetAssessmentByClass()
    {
        $calendarYids = $this->getCalendars();
        $startyear = $this->calendarStartYear[$calendarYids['currentYid']];
        $schoolId = $this->branchId;
        $classId = Yii::app()->request->getParam('classId');
        $type = Yii::app()->request->getParam('type');
        $period = Yii::app()->request->getParam('period');
        $data = array(
            "type" => $type,
            "period" => $period,
            "schoolId" => $schoolId,
            "classId" => $classId,
            "year" => $startyear
        );
        $res = CommonUtils::requestDsOnline('assessments/getAssessmentByClass', $data);
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }



    public function actionSaveComment()
    {
        $calendarYids = $this->getCalendars();
        $startyear = $this->calendarStartYear[$calendarYids['currentYid']];
        $schoolId = $this->branchId;
        $id = Yii::app()->request->getParam('id');
        $classId = Yii::app()->request->getParam('classId');
        $type = Yii::app()->request->getParam('type');
        $period = Yii::app()->request->getParam('period');
        $teacher = Yii::app()->request->getParam('teacher');
        $parent = Yii::app()->request->getParam('parent');
        $childId = Yii::app()->request->getParam('childId');
        $data = array(
            "id" => $id,
            "type" => $type,
            "period" => $period,
            "schoolId" => $schoolId,
            "classId" => $classId,
            "childId" => $childId,
            "year" => $startyear,
            "teacher" => $teacher,
            "parent" => $parent,
        );
        $res = CommonUtils::requestDsOnline('assessments/saveComment/' . $id, $data);
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }


    public function actionBatOnline()
    {
        $id = Yii::app()->request->getParam('id');
        $status = Yii::app()->request->getParam('status');
        $this->addMessage("state", "fail");
        if (count($id) == 0) {
            $this->addMessage('message', "id error");
            $this->showMessage();
        }
        if (!in_array($status, array(10, 20))) {
            $this->addMessage('message', "status error");
            $this->showMessage();
        }
        $data = array(
            "id" => $id,
            "status" => $status,
        );
        $res = CommonUtils::requestDsOnline('assessments/onlineBatch', $data);
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    // 获取七牛上传 token
    public function actionUploadToken()
    {
        $classId = Yii::app()->request->getParam('classId');
        if (!$classId) {
            $this->addMessage("state", "fail");
            $this->addMessage('message', "params error");
            $this->showMessage();
        }
        $calendarYids = $this->getCalendars();
        $startyear = $this->calendarStartYear[$calendarYids['currentYid']];
        $data = array(
            'classId' => $classId,
            'year' => $startyear,
        );
        $res = CommonUtils::requestDsOnline('assessments/getQiniuToken/' . $this->branchId, $data);
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionRenameAttachment()
    {
        $id = Yii::app()->request->getParam('id');
        $aid = Yii::app()->request->getParam('aid');
        $title = Yii::app()->request->getParam('title');
        $this->addMessage("state", "fail");
        if (count($id) == 0) {
            $this->addMessage('message', "id error");
            $this->showMessage();
        }

        $data = array(
            "id" => $id,
            "aid" => $aid,
            "title" => $title,
        );
        $res = CommonUtils::requestDsOnline('assessments/renameAttachment', $data);
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionSortAttachments()
    {
        $id = Yii::app()->request->getParam('id');
        $sort = Yii::app()->request->getParam('sort');
        $this->addMessage("state", "fail");
        if (count($id) == 0) {
            $this->addMessage('message', "id error");
            $this->showMessage();
        }

        $data = array(
            "id" => $id,
            "sort" => $sort
        );
        $res = CommonUtils::requestDsOnline('assessments/sortAttachments', $data);
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionDeleteAttachment()
    {
        $id = Yii::app()->request->getParam('id');
        $aid = Yii::app()->request->getParam('aid');
        $this->addMessage("state", "fail");
        if (count($id) == 0) {
            $this->addMessage('message', "id error");
            $this->showMessage();
        }

        $data = array(
            "id" => $id,
            "aid" => $aid
        );
        $res = CommonUtils::requestDsOnline('assessments/deleteAttachment', $data);
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

}
