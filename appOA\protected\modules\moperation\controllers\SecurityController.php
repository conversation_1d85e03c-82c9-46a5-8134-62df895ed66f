<?php

class SecurityController extends ProtectedController
{

    public $schoolList = array();
    public function init(){
        parent::init();
        Yii::import('common.models.content.MailImportInfo');
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'hqOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','HQ Operations');
        Yii::import('common.models.security.*');
    }

    public function actionIndex()
    {
        $this->render('index');
    }

    public function actionUpdateConfiguration()
    {
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue.js');
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');

        $criteria = new CDbCriteria();
        $criteria->order = 'status DESC';
        $dataProvider=new CActiveDataProvider('SafetySelf', array(
            'criteria'=>$criteria,
        ));

        $this->render('_updateConfiguration',array(
            'dataProvider'=>$dataProvider,
        ));
    }

    public function actionShowSafety()
    {
        $year = Yii::app()->request->getParam('year', 0);
        $month = Yii::app()->request->getParam('month', 0);
        $schoolid = Yii::app()->request->getParam('schoolid', 0);

        $scoreArr = array();
        $monthModle = array();
        $safetyData = array();
        $data = array();
        $cName = array();
        $rName = array();
        $user = array();
        if($year && $month && $schoolid){
            $timeDate  = $year . $month;
            $criteria = new CDbCriteria();
            $criteria->compare('schoold_id', $schoolid);
            $criteria->compare('month', $timeDate);
            $monthModle = SafetySelfMonth::model()->findAll($criteria);

            $criteria = new CDbCriteria();
            $criteria->compare('schoold_id', $schoolid);
            $criteria->compare('month', $timeDate);
            $safetySelfMonthModel = SafetySelfMonth::model()->findAll($criteria);


            $midArr = array();
            $userModel = array();
            if($safetySelfMonthModel){
                foreach($safetySelfMonthModel as $item){
                    $safetyData[$item->sid] = array(
                        'status' => $item->status,
                        'name' => $item->self->getName(),
                        'mid' => $item->id,
                        'type' => $item->self->type,
                    );
                    if($item->status == 1){
                        $midArr[] = $item->id;
                    }
                }
                ;

                if($midArr){
                    $criteria = new CDbCriteria();
                    $criteria->compare('mid', $midArr);
                    $criteria->compare('status', 1);
                    $criteria->order = 'sid desc';
                    $itemModel = SafetySelfItem::model()->findAll($criteria);

                    foreach ($itemModel as $item) {
                        $data[$item->sid][$item->cid][$item->rid] = array(
                            'principal' => $item->principal,
                            'document' => isset($item->rulesName) ? $item->rulesName->getDocName() : "",
                            'record' => $item->record,
                            'impore' => $item->impore,
                            'score' => $item->score,
                        );
                        $principal[$item->principal] = $item->principal;
                        $cName[$item->cid] = $item->cateName->getName();
                        $rName[$item->rid] = $item->rulesName->getName();
                    }


                    if($principal){
                        $criteria = new CDbCriteria();
                        $criteria->compare('uid', $principal);
                        $criteria->index = 'uid';
                        $userModel = User::model()->findAll($criteria);
                    }
                }
            }

            $criteria = new CDbCriteria();
            $criteria->compare('month', $timeDate);
            $criteria->compare('school_id', $schoolid);
            $criteria->compare('status', 1);
            $itemModel = SafetySelfItem::model()->findAll($criteria);
            if($itemModel){
                foreach ($itemModel as $item) {
                    $scoreArr[$item->sid] += $item->score;
                    $user[$item->sid][$item->principal] = $item->userName->getName();
                }
            }
        }

        $criteria = new CDbCriteria();
        $criteria->compare('type', array(Branch::PROGRAM_IBS,Branch::PROGRAM_DAYSTAR));
        $criteria->compare('status', Branch::STATUS_ACTIVE);
        $criteria->compare('branchid', "<>BJ_IA");
        $branchModel = Branch::model()->findAll($criteria);
        $school = array();
        foreach ($branchModel as $item){
            $school[$item->branchid] = $item->title;
        }


        $this->render('_showSafety', array(
            'school' => $school,
            'yearData' => $year,
            'monthData' => $month,
            'schoolid' => $schoolid,
            'monthModle' => $monthModle,
            'scoreArr' => $scoreArr,
            'safetyData' => $safetyData,
            'data' => $data,
            'cName' => $cName,
            'rName' => $rName,
            'timeDate' => $timeDate,
            'userModel' => $userModel,
            'user' => $user,
        ));
    }

    public function actionUndo()
    {
        $sid = Yii::app()->request->getParam('sid', "");
        $schoolid = Yii::app()->request->getParam('schoolid', "");
        $dataTime = Yii::app()->request->getParam('dateTime', "");
        $data = array(
            'state' => 'fail',
            'message' => '参数错误',
        );

        if(Yii::app()->request->isAjaxRequest){
            if($sid && $schoolid && $dataTime){
                $criteria = new CDbCriteria();
                $criteria->compare('sid', $sid);
                $criteria->compare('schoold_id', $schoolid);
                $criteria->compare('month', $dataTime);
                $model = SafetySelfMonth::model()->find($criteria);
                if($model){
                    $model->status = 0;
                    $model->updated_at = time();
                    $model->updated_by = Yii::app()->user->id;
                    if($model->save()){
                        $data = array(
                            'state' => 'success',
                            'message' => '成功',
                        );
                    }else{
                        $error = current($model->getErrors());
                        $data = array(
                            'state' => 'fail',
                            'message' => $error,
                        );
                    }
                }
            }
            echo json_encode($data);
        }
    }

    public function actionAddSafetySelf()
    {
        $safetyid = Yii::app()->request->getParam('id', 0);

        $model = SafetySelf::model()->findByPk($safetyid);
        if(!$model){
            $model = New SafetySelf();
        }

        if(Yii::app()->request->isPostRequest){
            $model->attributes = $_POST['SafetySelf'];
            $model->created_at = time();
            $model->created_by = Yii::app()->user->id;
            $model->updated_at = time();
            $model->updated_by = Yii::app()->user->id;

            if($model->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('callback', 'cbSecurity');
            } else {
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err ? $err[0] : '失败');
            }
            $this->showMessage();
        }
        $this->renderpartial('_update', array(
            'model' => $model,
        ));
    }

    public  function  actionUpdatedRule()
    {
        $srid = Yii::app()->request->getParam('srid', 0);
        $year = Yii::app()->request->getParam('year', "");
        $month = Yii::app()->request->getParam('month', "");
        $model = SafetySelfRules::model()->findByPk($srid);

        if(Yii::app()->request->isPostRequest){
            if(empty($year) && empty($month)){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '增加年月不能为空');
                $this->showMessage();
            }
            $model->attributes = $_POST['SafetySelfRules'];
            $model->start_time = $year . sprintf("%02d", $month);
            $model->created_at = time();
            $model->created_by = Yii::app()->user->id;
            $model->updated_at = time();
            $model->updated_by = Yii::app()->user->id;
            if($model->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('callback', 'cbSecurityRule');
            } else {
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err ? $err[0] : '失败');
            }
            $this->showMessage();
        }
        $this->renderpartial('_updateRule', array(
            'model' => $model,
        ));
    }

    public function actionCreatedRule()
    {
        $title_en = Yii::app()->request->getParam('title_en', array());
        $title_cn = Yii::app()->request->getParam('title_cn', array());
        $documentCn = Yii::app()->request->getParam('document_cn', array());
        $documentEn = Yii::app()->request->getParam('document_en', array());
        $year = Yii::app()->request->getParam('year', array());
        $month = Yii::app()->request->getParam('month', array());

        $sid = Yii::app()->request->getParam('sid', '');
        $scid = Yii::app()->request->getParam('scid', '');
        if($sid && $scid){
            foreach ($title_en as $key => $title) {
                if($title !=='' && $title_cn[$key] !== ''){
                    $model = new SafetySelfRules();
                    $model->title_cn = $title;
                    $model->title_en = $title_cn[$key];
                    $model->document_cn = $documentCn[$key];
                    $model->document_en = $documentEn[$key];
                    $model->sid = $sid;
                    $model->cid = $scid;
                    $model->status = 1;
                    $model->start_time = $year[$key] . $month[$key];
                    $model->created_at = time();
                    $model->created_by = Yii::app()->user->id;
                    $model->updated_at = time();
                    $model->updated_by = Yii::app()->user->id;
                    $model->save();
                }
            }
            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('message','success'));
            $this->addMessage('callback', 'cbSuccess');
            $this->showMessage();
        }
    }

    //修改或者增加类别
    public function actionSaveCateGory()
    {
        $tid = Yii::app()->request->getParam('tid', 0);
        $new = Yii::app()->request->getParam('new', array());
        $old = Yii::app()->request->getParam('old', array());
        $program = Yii::app()->request->getPost('program', array());
        $weight = Yii::app()->request->getPost('sub_weight', array());

        if($tid) {
            $newroot = isset($new['root']) ? $new['root'] : array();
            if(!empty($newroot)){
                foreach ($newroot as $_newroot) {
                    if($program[$_newroot]['cn'] || $program[$_newroot]['en']){
                        $model = New SafetySelfCategory;
                        $model->sid = $tid;
                        $model->sort = $weight[$_newroot];
                        $model->title_cn = trim($program[$_newroot]['cn']);
                        $model->title_en = trim($program[$_newroot]['en']);
                        $model->status = 0;
                        $model->created_at = time();
                        $model->created_by = Yii::app()->user->id;
                        $model->updated_at = time();
                        $model->updated_by = Yii::app()->user->id;
                        $model->save();
                    }
                }
            }
            $oldroot = isset($old['root']) ? $old['root'] : array();
            if(!empty($oldroot)){
                foreach ($oldroot as $_oldroot) {
                    $model = SafetySelfCategory::model()->findByPk($_oldroot);
                    $model->sid = $tid;
                    $model->sort = $weight[$_oldroot];
                    $model->title_cn = trim($program[$_oldroot]['cn']);
                    $model->title_en = trim($program[$_oldroot]['en']);
                    $model->status = 0;
                    $model->updated_at = time();
                    $model->updated_by = Yii::app()->user->id;
                    $model->save();
                }
            }
            $this->addMessage('state', 'success');
            $this->addMessage('message', '成功');
            $this->addMessage('refresh', true);
        }else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '失败');
        }

        $this->showMessage();
    }

    // 删除分类
    public function actionDelCateGory()
    {
        $id = Yii::app()->request->getParam('id');
        if($id){
            $model = SafetySelfCategory::model()->findByPk($id);
            if($model){
                $model->status = 9999;
                if($model->save()){
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('global', 'Data deleted'));
                }
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('global', 'Deletion failed'));
            }
            $this->showMessage();
        }
    }

    public function actionGetCateGory()
    {
        $id = Yii::app()->request->getParam('id', 0);
        if($id){
            $ret = array();
            $criteria = new CDbCriteria();
            $criteria->compare('sid', $id);
            $criteria->addNotInCondition('status', array(9999));
            $criteria->order='sort';
            $items = SafetySelfCategory::model()->findAll($criteria);
            if($items){
                foreach($items as $item){
                    $ret[] = array(
                        'id' => $item->id,
                        'ctitle' => $item->title_cn,
                        'etitle' => $item->title_en,
                        'weight' => intval($item->sort),
                    );
                }
            }
            echo CJSON::encode($ret);
        }
    }

    // 显示分类 给分类下增加选项
    public function actionEditItem($sid=0, $scid=0)
    {
        if($sid){
            $safetyModel = SafetySelf::model()->findByPk($sid);
            $criteria = new CDbCriteria();
            $criteria->compare('sid', $sid);
            $criteria->addNotInCondition('status', array(9999));
            $criteria->order = 'sort';
            $model = SafetySelfCategory::model()->findAll($criteria);
            $subs = "";
            if($scid){
                $criteria = new CDbCriteria();
                $criteria->compare('t.cid', $scid);
                $criteria->compare('t.sid', $sid);
                $criteria->compare('t.status', "<>9999");
                $criteria->order= 'sort';
                $subs = SafetySelfRules::model()->findAll($criteria);
            }
            $cs = Yii::app()->clientScript;
            $cs->registerCoreScript('jquery.ui');
            $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');

            $ruleModel = new SafetySelfRules();

            $this->render('edititem', array(
                'model' => $model,
                'ruleModel' => $ruleModel,
                'subs' => $subs,
                'scid' => $scid,
                'sid' => $sid,
                'safetyModel' => $safetyModel,
            ));
        }
    }

    //校园事故上报列表
    public function actionAccidentTrack()
    {
        $dataProvider = array();
        $critre = new CDbCriteria;
        $critre->compare('status', array(SafetyIncidentReport::STATUS_ACTIVE, SafetyIncidentReport::STATUS_CHECK));
        $dataProvider = new CActiveDataProvider('SafetyIncidentReport', array(
            'criteria' => $critre,
            'pagination'=>array(
                'pageSize'=>50,
            ),
            'sort' => array(
                'defaultOrder' => 't.incident_date DESC',
            ),
        ));


        $criteria = new CDbCriteria();
        $criteria->compare('type', array(Branch::PROGRAM_IBS,Branch::PROGRAM_DAYSTAR));
        $criteria->compare('status', Branch::STATUS_ACTIVE);
        $criteria->compare('branchid', "<>BJ_IA");
        $branchModel = Branch::model()->findAll($criteria);

        foreach ($branchModel as $item) {
            $this->schoolList[$item->branchid] = $item->title;
        }

        $this->render('accidentTrack', array(
            'dataProvider' => $dataProvider,
        ));
    }

    public function actionComparisonAccident()
    {
        $schoolid = Yii::app()->request->getParam('schoolid', "");
        $comparisonAccidentsNew = array();
        $comparisonAccidentsOld = array();
        $reportNew = array();
        $reportOld = array();
        $typeNew = array();
        $typeOld = array();
        if($schoolid){
            $time = date("Y",time());
            if(date("m",time()) > 9){
                $strDate = $time-1 . "09";
            }else{
                $strDate = $time-2 . "09";
            }
            $incident_date = $strDate;
            $month = array();
            while ($strDate <= date("Ym",time())){
                $month[$strDate] =  $strDate;
                $strDate = date('Ym',strtotime($strDate . "01 +1 month"));
            }
            foreach ($month as $item) {
                $tmp_statr = strtotime($item . '01');
                $tmp_year=substr($item,0,4);
                $tmp_mon =substr($item,4,2);
                $tmp_nextmonth=mktime(0,0,0,$tmp_mon+1,1,$tmp_year);

                $sql = "SELECT period_timestamp , (num1 + num2) as totle FROM ivy_stats_child_count WHERE schoolid = '" . $schoolid ."' and classid = 0 and period_timestamp >= ". $tmp_statr ."  and period_timestamp < ". $tmp_nextmonth ." order by num1 desc limit 1";
                $ret = Yii::app()->db->createCommand($sql)->queryRow();
                if($ret){
                    if(date("m",time()) > 9){
                        if($time . "09"  <= date("Ym", $ret['period_timestamp'])){
                            $comparisonAccidentsNew[date("m", $ret['period_timestamp'])] = $ret['totle'];
                        }else{
                            $comparisonAccidentsOld[date("m", $ret['period_timestamp'])] = $ret['totle'];
                        }
                    }else{
                        if($time-1 . "09"  <= date("Ym", $ret['period_timestamp'])){
                            $comparisonAccidentsNew[date("m", $ret['period_timestamp'])] = $ret['totle'];
                        }else{
                            $comparisonAccidentsOld[date("m", $ret['period_timestamp'])] = $ret['totle'];
                        }
                    }
                }
            }

            $incident_date_statr = strtotime($incident_date . '01');
            $incident_date_end = time();
            $criteria = new CDbCriteria();
            $criteria->compare('school_id', $schoolid);
            $criteria->compare('status', SafetyIncidentReport::STATUS_CHECK);
            $criteria->compare('incident_date', ">={$incident_date_statr}");
            $criteria->compare('incident_date', "<={$incident_date_end}");
            $criteria->index = 'id';
            $reportModel = SafetyIncidentReport::model()->findAll($criteria);
            if($reportModel){
                $criteria = new CDbCriteria();
                $criteria->compare('school_id', $schoolid);
                $criteria->compare('report', array_keys($reportModel));
                $criteria->compare('status', SafetyIncidentReport::STATUS_ACTIVE);
                $track = SafetyIncidentTrack::model()->findAll($criteria);
                $trackArr = array();
                if($track){
                    foreach ($track as $item) {
                        if($item->accident_type > 20){
                            $trackArr[$item->report][1][$item->id] = $item->id;
                        }else{
                            $trackArr[$item->report][2][$item->id] = $item->id;
                        }
                    }
                }


                foreach($reportModel as $key => $item){
                    $month = date("m", $item->incident_date);
                    $yearTime = date("Ym", $item->incident_date);
                    $monthTime = date("m", time());
                    if($monthTime > 9){
                        if($time . "09"  <= $yearTime){
                            $reportNew[$month][$item->level][$item->id] = $item->level;
                            if(isset($trackArr[$item->id][1])){
                                $typeNew[$month]['responsibility'][$key] = $trackArr[$item->id][1];
                            }
                            if(isset($trackArr[$item->id][2])){
                                $typeNew[$month]['accident'][$key] = $trackArr[$item->id][2];
                            }
                        }else{
                            $reportOld[$month][$item->level][$item->id] = $item->level;
                            if(isset($trackArr[$item->id][1])){
                                $typeOld[$month]['responsibility'][$key] = $trackArr[$item->id][1];
                            }
                            if(isset($trackArr[$item->id][2])){
                                $typeOld[$month]['accident'][$key] = $trackArr[$item->id][2];
                            }
                        }
                    }else{
                        if($time-1 . "09"  <= $yearTime){
                            $reportNew[$month][$item->level][$item->id] = $item->level;
                            if(isset($trackArr[$item->id][1])){
                                $typeNew[$month]['responsibility'][$key] = $trackArr[$item->id][1];
                            }
                            if(isset($trackArr[$item->id][2])){
                                $typeNew[$month]['accident'][$key] = $trackArr[$item->id][2];
                            }

                        }else{
                            $reportOld[$month][$item->level][$item->id] = $item->level;
                            if(isset($trackArr[$item->id][1])){
                                $typeOld[$month]['responsibility'][$key] = $trackArr[$item->id][1];
                            }
                            if(isset($trackArr[$item->id][2])){
                                $typeOld[$month]['accident'][$key] = $trackArr[$item->id][2];
                            }
                        }
                    }
                }
            }
        }


        $criteria = new CDbCriteria();
        $criteria->compare('type', array(Branch::PROGRAM_IBS,Branch::PROGRAM_DAYSTAR));
        $criteria->compare('status', Branch::STATUS_ACTIVE);
        $criteria->compare('branchid', "<>BJ_IA");
        $branchModel = Branch::model()->findAll($criteria);
        $school = array();
        foreach ($branchModel as $item){
            $school[$item->branchid] = $item->title;
        }


        $this->render('comparison', array(
            'school' => $school,
            'schoolid' => $schoolid,
            'comparisonAccidentsOld' => $comparisonAccidentsOld,
            'comparisonAccidentsNew' => $comparisonAccidentsNew,
            'reportNew' => $reportNew,
            'reportOld' => $reportOld,
            'typeNew' => $typeNew,
            'typeOld' => $typeOld,
            'time' => $time,
        ));
    }

    // 跟进事故列表
    public function actionShowReport()
    {
        Yii::import('common.models.child.*');
        Yii::import('common.models.hr.*');
        $id = Yii::app()->request->getParam('id', "");
        $model = SafetyIncidentReport::model()->findByPk($id);

        $criteria = new CDbCriteria();
        $criteria->compare('report', $model->id);
        $criteria->compare('school_id', $model->school_id);
        $criteria->compare('status', SafetyIncidentReport::STATUS_ACTIVE);
        $trackModel = SafetyIncidentTrack::model()->find($criteria);
        if(!$trackModel){
            $trackModel = new SafetyIncidentTrack();
        }

        if(Yii::app()->request->isPostRequest){
            $trackModel->attributes = $_POST['SafetyIncidentTrack'];
            $trackModel->status = 1;
            $trackModel->created_at = time();
            $trackModel->created_by = Yii::app()->user->id;
            $trackModel->updated_at = time();
            $trackModel->updated_by = Yii::app()->user->id;
            if($trackModel->save()){
                $model->status = SafetyIncidentReport::STATUS_CHECK;
                $model->save();
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('callback', 'cbIncidentTrack');
            } else {
                $this->addMessage('state', 'fail');
                $err = current($trackModel->getErrors());
                $this->addMessage('message', $err ? $err[0] : '失败');
            }
            $this->showMessage();
        }

        $userArr = array($model->reported_by => $model->reported_by);
        $position = array($model->reported_position => $model->reported_position);
        $childModel = array();
        if($model->incident_type == 1){
            $criteria = new CDbCriteria();
            $criteria->compare('childid', $model->child_id);
            $criteria->index = "childid";
            $childModel = ChildProfileBasic::model()->find($criteria);
            $userArr[$model->involved_teacher] = $model->involved_teacher;
        }

        if($model->incident_type == 2){
            $userArr[$model->staff] = $model->staff;
            $position[$model->staff_position] = $model->staff_position;
        }

        $criteria = new CDbCriteria();
        $criteria->compare('uid', $userArr);
        $criteria->index = "uid";
        $userModel = User::model()->findAll($criteria);

        $criteria = new CDbCriteria();
        $criteria->compare('id', $position);
        $criteria->index = "id";
        $hrModel = HrPosition::model()->findAll($criteria);

        if($model){
            $branchModel = Branch::model()->findByPk($model->school_id);
            $this->render('follow', array(
                'model' => $model,
                'trackModel' => $trackModel,
                'userModel' => $userModel,
                'childModel' => $childModel,
                'hrModel' => $hrModel,
                'branchModel' => $branchModel,
            ));
        }
    }

    //  修改排序
    public function actionUpdateRuleSort()
    {
        if(Yii::app()->request->isPostRequest){
            $sort = Yii::app()->request->getPost('sort', array());

            foreach($sort as $key=>$val){
                SafetySelfRules::model()->updateByPk($key, array('sort'=>$val));
            }
        }
    }

    // 作废
    public function actionInvalidRule()
    {
        $id = Yii::app()->request->getPost('id', "");
        if($id){
            $model = SafetySelfRules::model()->findByPk($id);
            $model->status = 9999;
            if($model->save()) {
                $this->addMessage('state', 'success');
                $this->addMessage('message', '删除成功');
                $this->addMessage('refresh', true);
            }else{
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err ? $err[0] : '删除失败');
            }
            $this->showMessage();

        }
    }

    public function actionDeleteTrack()
    {
        $id = Yii::app()->request->getPost('id', "");
        $type = Yii::app()->request->getPost('type', "");
        if($id){
            $model = SafetyIncidentReport::model()->findByPk($id);
            if($type == 'recall'){
                $model->status = 0;
                $message = '撤回成功';
            }else{
                $model->status = 9999;
                $message = '删除成功';
            }

            if($model->save()) {
                $criteria = new CDbCriteria();
                $criteria->compare('report', $model->id);
                $criteria->compare('status', SafetyIncidentReport::STATUS_ACTIVE);
                $trackModel = SafetyIncidentTrack::model()->findAll($criteria);
                if ($trackModel) {
                    foreach ($trackModel as $item){
                        $item->status = 9999;
                        $item->save();
                    }
                }
                $this->addMessage('state', 'success');
                $this->addMessage('message', $message);
                $this->addMessage('refresh', true);
            }else{
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err ? $err[0] : '删除失败');
            }
            $this->showMessage();
        }
    }

    public function getStatus($data)
    {
        if($data->status){
            echo '有效';
        }else{
            echo '无效';
        }
    }

    public function getButton($data)
    {
        echo '<a href='. $this->createUrl('addSafetySelf',array("id"=>$data->id)) . ' type="button" class="J_modal btn btn-primary btn-xs"><span class="glyphicon glyphicon-wrench"></span></a> ';
        echo sprintf('<button type="button" class="btn btn-primary btn-xs" onclick="editCategory('.$data->id.')"
        title="%s"><span class="glyphicon glyphicon-th-list"></span></button> ', Yii::t('report', 'Categories Edit'));
        echo sprintf('<a role="button" class="btn btn-primary btn-xs" title="%s" target=_blank href="%s"><span
        class="glyphicon glyphicon-pencil"></span></a> ',
            Yii::t('report', 'Items Edit'),
            $this->createUrl('editItem',array('sid'=>$data->id)));
    }

    public function getButtonReport($data)
    {
        $message = ($data->status == SafetyIncidentReport::STATUS_CHECK) ? '已经写过评估表,确认撤回提交吗?' : "确认撤回提交吗?";
        echo CHtml::link('查看', array('showReport', 'id' => $data->id), array('class' => 'btn btn-xs btn-info')) . ' ';
        echo CHtml::link('撤回', array('deleteTrack', 'id' => $data->id, 'type' => 'recall'), array('class' => 'J_ajax_del btn btn-xs btn-primary', 'data-msg' => " $message ")) . ' ';
        //echo CHtml::link('删除', array('deleteTrack', 'id' => $data->id, 'type' => 'del'), array('class' => 'J_ajax_del btn btn-xs btn-danger'));
    }

    public function getType($data)
    {
        $SecurityType = CommonUtils::LoadConfig('CfgSecurity');
        $type = array();
        foreach ($SecurityType['type'] as $k=>$item) {
            $type[$k] = (Yii::app()->language == 'zh_cn') ? $item['cn'] : $item['en'] ;
        }
        echo $type[$data->type];
    }

    public function getSchool($data)
    {
        echo $this->schoolList[$data->school_id];
    }

    public function getReportStatus($data)
    {
        if($data->status == 1){
            echo '未审核';
        }else{
            echo '已审核';
        }
    }
}