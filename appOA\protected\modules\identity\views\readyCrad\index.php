<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Routines'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('site', 'Device list') ?></li>
    </ol>

    <div class="row">
        <div class="col-md-2 col-sm-2 mb10">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->module->getMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                'activeCssClass' => 'active',
            ));
            ?>
        </div>
        <div class="col-md-10 col-sm-10">
                <a href="javascript:;" class="btn btn-primary " onclick="lateAdd()">
                    <?php echo Yii::t("site", "Add new card"); ?>
            </a>
            <br>
            <br>
            <?php
            $this->widget('ext.ivyCGridView.BsCGridView', array(
                'id' => 'cradList',
                'afterAjaxUpdate' => 'js:head.Util.modal',
                'dataProvider' => $cardsObj,
                'template' => "{items}{pager}{summary}",
                //状态为无效时标红
                //'rowCssClassExpression' => '( $data->active == 0 ? "active" : "" )',
                'colgroups' => array(
                    array(
                        "colwidth" => array(200, 200, 200, 200, 200),
                    )
                ),
                'columns' => array(
                    array(
                        'class' => 'CCheckBoxColumn',
                        'selectableRows' => 2,
                        'checkBoxHtmlOptions' => array(
                            'class' => 'chose',
                            'value' => '$data->id',
                        ),
                    ),
                    array(
                        'name' => 'childid',
                        'value' => array($this, 'getChildName'),
                    ),
                    array(
                        'name' => Yii::t('site','Parent name'),
                        'value' => array($this, 'getParentName'),
                    ),
                    array(
                        'name' => Yii::t('site','Contact number'),
                        'value' => array($this, 'getParentTel'),
                    ),
                    array(
                        'name' => Yii::t('site','Relationship'),
                        'value' => array($this, 'getParentRelation'),
                    ),
                    array(
                        'name' => Yii::t('site','Photo'),
                        'value' => array($this, 'GetPhoto'),
                    ),
                    array(
                        'name' => Yii::t('invoice','Status'),
                        'value' => array($this, 'getSign'),
                    ),
                    array(
                        'name' => 'updated',
                        'value' => 'date("Y-m-d", $data->updated)',
                    ),
                    array(
                        'name' => Yii::t('global', 'ACtion'),
                        'value' => array($this, 'getButton'),
                    ),
                ),

            ));
            ?>
            <hr/>
            <div class="col-md-12">
                <button class="btn btn-primary  pull-left" onclick="check(this)">通过</button>
                <span class="text-warning ml10" id="failInfo" style="display: none;line-height:2.6em;display: inline-block"></span>
            </div>
            <div class="col-md-12">
                <br>
                &nbsp;
                <span id="J_fail_info" class="text-warning"></span>
            </div>
        </div>
    </div>
</div>

<!-- 发信弹框 -->
<div id="pro-modal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" onclick="cbCrads()" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">×</span></button>
                <h4 class="modal-title"><?php echo Yii::t('campus', 'Cards to be made'); ?></h4>
            </div>
            <div class="modal-body">
                <div class="col-md-12">制卡资料显示 (在输入框点击,然后Ctrl+A 全选)</div>
                <div id="waitingProductionList"></div>
                <div style="clear: both"></div>
            </div>
        </div>
    </div>
</div>


<div class="modal fade" id="myModal_late" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">×</span></button>
                <h4 class="modal-title"><?php echo Yii::t('site', 'Add new card'); ?></h4>
            </div>
            <?php
            $form = $this->beginWidget('CActiveForm', array(
                'id' => 'visits-form',
                'enableAjaxValidation' => false,
                'action' => $this->createUrl('create'),
                'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form'),
            ));
            ?>
            <div class="modal-body">
                <div class="form-group">
                    <label class="col-xs-2 control-label"><?php echo Yii::t('campus','Students on leave/tardy') ?></label>

                    <div class="col-xs-8">
                        <?php
                        $this->widget('ext.search.ChildSearchBox', array(
                            'acInputCSS' => 'form-control',
                            'acName' => 'searchChild_late',
                            'allowMultiple' => false,
                            'allowMultipleSchool' => false,
                            'simpleDisplay' => false,
                            'extendCss' => false,
                            'useModel' => true,
                            //'allowMultiple' => 8,
                            'model' => $model,
                            'attribute' => 'childid',
                            'htmlOptions' => array('class' => 'form-control')
                        )) ?>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'name'); ?></label>
                    <div class="col-xs-8">
                        <?php echo $form->textField($model, 'name', array('class' => 'form-control')); ?>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'relation'); ?></label>
                    <div class="col-xs-8">
                        <?php echo $form->textField($model, 'relation', array('class' => 'form-control')); ?>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'tel'); ?></label>
                    <div class="col-xs-8">
                        <?php echo $form->textField($model, 'tel', array('class' => 'form-control')); ?>
                    </div>
                </div>
                <!-- 备注 -->
                <div class="form-group">
                    <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'uploadFile'); ?></label>
                    <div class="col-xs-8">
                        <?php echo $form->fileField($model, 'uploadFile'); ?>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit"
                        class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
                <button type="button" class="btn btn-default"
                        data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
            </div>
            <?php $this->endWidget(); ?>
        </div>
    </div>
</div>

<script>
    var modal = '<div class="modal fade" id="modal" class="1123" tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog modal-lg" role="document"><div class="modal-content"></div></div></div>';
    $('body').append(modal);


    function lateAdd() {
        $('#myModal_late').modal({backdrop: 'static'});
    }


    function cbUpdateDevices() {
        location.reload();
    }

    function check(btn) {
        var id = [];
        $('.chose').each(function (i, val) {
            if (val.checked) {
                id[i] = $(val).val();
            }
        });

        if (!id) {
            $('#J_fail_info').text('至少选择一个记录');
            return;
        }
        // show madal
        $(btn).prop("disabled", "disabled");
        // ajax request
        $.ajax({
            url: '<?php echo $this->createUrl("systemCard", array('branchId'=>$this->branchId));?>',
            data: {status: status, id: id},
            type: 'post',
            timeout: 5000,
            dataType: 'json',
            global: false
        }).done(function (data, status, xhr) {
            $('#waitingProductionList').empty();
            $(btn).removeProp("disabled");
            if (data.state == 'success') {
                $('#failInfo').hide();
                $('#pro-modal').modal({
                    show: true,
                    backdrop: 'static'
                });
                var tr = '<textarea  rows="20"  class="col-md-12" style="overflow-x:auto;,overflow-y:auto;">';
                $.each(data.data, function (i, _item) {
                    tr += _item['childid'] + ',' + _item['childName'] + ',' + _item['campus'] + ',' + _item['class'] + ',' + _item['name'] + ',' + _item['tel'] + ',' + _item['relation'] + "," + _item['photo'] + "," + '\n'
                });
                tr += '</textarea >';
                $('#waitingProductionList').html(tr);
            } else {
                $('#failInfo').html(data.message).show()
            }

        }).fail(function (xml, status, text) {
            alert(status)
            $(btn).removeProp("disabled");
        });
    }

    function cbCrads() {
        $('#modal').modal('hide');
        $.fn.yiiGridView.update('cradList');
    }
</script>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>