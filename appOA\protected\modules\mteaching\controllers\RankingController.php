<?php

class RankingController extends TeachBasedController
{
    // 访问action的初级权限
    public $actionAccessAuths = array(
        'index' => 'o_T_Access'
    );

    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'teaching';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Teaching Tasks');

        //初始化选择校园页面
        $this->branchSelectParams['hideKG'] = true;
        $this->branchSelectParams['urlArray'] = array('//mteaching/ranking/index');
        $cs = Yii::app()->clientScript;
//        $cs->registerCoreScript('jquery.ui');
//        $cs->registerCssFile($cs->getCoreScriptUrl() . '/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/vue2.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/xlsx.full.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/echarts.5.4.3.min.js');
    }

    public function actionIndex()
    {
        $this->setPageTitle('Daystar - G6-12 Internal Grade Analytics');
        $show = false;

        if (Yii::app()->user->checkAccess('ivystaff_it')) {
            $show = true;
        }
        elseif (Yii::app()->user->checkAccess('ivystaff_cd')) {
            $model = DepPosLink::model()->findByPk($this->staff->profile->occupation_en);
            if(in_array($model->department_id, array(137, 147))) {
                $show = true;
            }
        }
        elseif (in_array(Yii::app()->user->id, array(8032242, 8017612,8030635,8037254,8034219,8038173,8038172,8034217,8011391,8014971,8027100, 8030837))) {
            $show = true;
        }

        $this->render('index', array('show' => $show));
    }

    public function actionSchoolyear()
    {
        $res = CommonUtils::requestDsOnline('ranking/schoolyear/'.$this->branchId, array(), 'get');
        $this->addMessage('state', 'success');
        $this->addMessage('message', Yii::t('message','success'));
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    public function actionMainData()
    {
        $schoolYear = Yii::app()->request->getParam('schoolYear', '');
        $grade = Yii::app()->request->getParam('grade', '');

        $res = CommonUtils::requestDsOnline('ranking/score/'.$this->branchId.'/'.$schoolYear.'/'.$grade, array(), 'get');
        $this->addMessage('state', 'success');
        $this->addMessage('message', Yii::t('message','success'));
        $this->addMessage('data', $res['data']);
        $this->showMessage();
    }

    //设置星标
    public function actionSaveStarFlag()
    {
        $grade = Yii::app()->request->getParam('grade');
        $clear_child_ids = Yii::app()->request->getParam('clear_child_ids');//去掉标签的学生
        $child_ids = Yii::app()->request->getParam('child_ids');//需要标签的学生
        $requestUrl = 'child/saveLabel';
        $this->remote($requestUrl, array(
            'school_id' => $this->branchId,
            'clear_child_ids' => $clear_child_ids,
            'child_ids' => $child_ids,
        ));
    }
    public function remote($requestUrl, $requestData = array())
    {
        $res = CommonUtils::requestDsOnline($requestUrl, $requestData);
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        }
        $this->showMessage();
    }
}
