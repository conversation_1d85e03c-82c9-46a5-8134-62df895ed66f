<?php

/**
 * This is the model class for table "ivy_child_active_status".
 *
 * The followings are the available columns in table 'ivy_child_active_status':
 * @property integer $id
 * @property integer $transaction_id
 * @property integer $childid
 * @property integer $classid
 * @property integer $yid
 * @property integer $category
 * @property string $itemname
 * @property string $itemvalue
 * @property string $branchid
 * @property integer $updatetime
 */
class ChildActiveStatus extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ChildActiveStatus the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_child_active_status';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('transaction_id, childid, classid, yid, category, itemname, itemvalue, branchid, updatetime', 'required'),
			array('transaction_id, childid, classid, yid, category, updatetime', 'numerical', 'integerOnly'=>true),
			array('itemname, itemvalue', 'length', 'max'=>100),
			array('branchid', 'length', 'max'=>10),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, transaction_id, childid, classid, yid, category, itemname, itemvalue, branchid, updatetime', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'transaction_id' => 'Transaction',
			'childid' => 'Childid',
			'classid' => 'Classid',
			'yid' => 'Yid',
			'category' => 'Category',
			'itemname' => 'Itemname',
			'itemvalue' => 'Itemvalue',
			'branchid' => 'Branchid',
			'updatetime' => 'Updatetime',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('transaction_id',$this->transaction_id);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('category',$this->category);
		$criteria->compare('itemname',$this->itemname,true);
		$criteria->compare('itemvalue',$this->itemvalue,true);
		$criteria->compare('branchid',$this->branchid,true);
		$criteria->compare('updatetime',$this->updatetime);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}