<?php
/*
 * <PERSON><PERSON>
 * 2012-11-19
 *
 */
?>

<div id="invoiceinfo" class="confirminfo">
    <?php
    if (!empty($dataProvider)) {

        Mims::LoadHelper('HtoolKits');
        $tk = new HtoolKits();
        $nf = new CNumberFormatter('zh-cn');
        $beforePaidInfoForm = $this->beginWidget('CActiveForm', array(
            'id' => 'custom_yeepay_online',
            'enableAjaxValidation' => false,
                ));
        ?>
        <div class="paylist">
            <?php
            $invoice_model = Invoice::model();
            $this->widget('zii.widgets.grid.CGridView', array(
                'cssFile' => Yii::app()->theme->baseUrl . '/widgets/gridview/styles.css',
                'dataProvider' => $dataProvider,
                'enablePagination' => false,
                'enableSorting' => false,
                'columns' => array(
                    array(
                        'name' => 'title',
                        'type' => 'raw',
                        'value' => 'CHtml::link($data->title,Yii::app()->getController()->createUrl("//child/payment/viewInvoice", array("invoice_id"=>$data->invoice_id)),array("class"=>"colorbox"))',
                    ),
                    'amount',
                    array(
                        'name' => Yii::t("payment", 'Amount Paid'),
                        'value' => array($invoice_model, 'renderPaidAmountF'),
                    ),
                    array(
                        'header' => Yii::t("payment", 'Amount Due'),
                        'type' => 'raw',
                        'value' => array($invoice_model, 'renderDueAmountHtml'),
                    ),
                ),
                'template' => '{items}',
            ));
            ?>
        </div>

        <?php echo $beforePaidInfoForm->hiddenField($beforePaidInfoModel, 'childId'); ?>
        <?php echo $beforePaidInfoForm->hiddenField($beforePaidInfoModel, 'schoolId'); ?>
        <?php echo $beforePaidInfoForm->hiddenField($beforePaidInfoModel, 'bankId'); ?>
        <?php echo $beforePaidInfoForm->hiddenField($beforePaidInfoModel, 'autoOrderId'); ?>
        <?php echo $beforePaidInfoForm->hiddenField($beforePaidInfoModel, 'totalDueAmount'); ?>
        <?php echo $beforePaidInfoForm->hiddenField($beforePaidInfoModel, 'reqURL_onLine'); ?>
        <?php echo $beforePaidInfoForm->hiddenField($beforePaidInfoModel, 'customHmac'); ?>
        <?php echo $beforePaidInfoForm->hiddenField($beforePaidInfoModel, 'invoiceId'); ?>
        <?php echo CHtml::hiddenField('payment_mothod', 'yeepay'); ?>
        <?php $this->endWidget(); ?>

        <div id="confirm-info">
            <div class="fr big-size">
                <?php echo Yii::t("payment", 'Total'); ?>
                <span id="should_pay"><?php echo $nf->format('#,##0.00', $beforePaidInfoModel->totalDueAmount); ?>
                </span>
            </div>
            <div class="normal-size">
                <p>
                    <?php echo addColon(Yii::t("payment", 'Selected Bank')); ?>
                </p>
                <?php
                $bankName = $tk->getContentByLang($selectedBank['cn_name'], $selectedBank['en_name']);
                echo CHtml::image(Yii::app()->getThemeManager()->getBaseUrl() . '/base/images/bankLogo4Yeepay/' . $selectedBank['logoname'], $bankName, array('title' => $bankName))
                ?>
            </div>
        </div>
        <div class="clear"></div>
        <div id="operatecol">

            <?php echo CHtml::link('<span>' . Yii::t("global", 'Back') . '</span>', $this->createUrl('payment/online'), array("class" => "back")); ?>
            <div class="fr">
                <?php
                if (Yii::app()->user->checkAccess('makePayment', array("childid" => $this->getChildId()))):
                    $ajaxOptions = array(
                        'type' => 'POST',
                        'dataType' => 'JSON',
                        'data' => 'js:jQuery("#custom_yeepay_online").serialize()',
                        'beforeSend' => 'showRequest',
                        'success' => 'showResponse',
                    );
                    echo CHtml::openTag('span', array('class' => 'btn001'));
                    echo CHtml::ajaxLink(CHtml::tag('span') . Yii::t("payment", 'Make Payment') . CHtml::closeTag('span'), $this->createUrl('//child/payment/onlineBeforePaid'), $ajaxOptions, array('class' => 'operatecol', 'id' => 'pay_money'));
                    echo CHtml::closeTag('span');
                else:
                    echo CHtml::button(Yii::t("payment", 'Make Payment'), array("class" => "bigger w100", "disabled" => "disabled"));
                endif;
                ?>
                <form id='frmOnlinePaySubmit' name='frmOnlinePaySubmit'
                      action="<?php echo $beforePaidInfoModel->reqURL_onLine; ?>" method='post'>
                          <?php
                          foreach ($requestInfo as $key => $value) {
                              echo CHtml::hiddenField($key, $value);
                          }
                          ?>
                </form>
            </div>

        </div>
        <div align=center>
            <span id="show_paymoney_info"></span>
        </div>
    <?php } ?>
</div>
