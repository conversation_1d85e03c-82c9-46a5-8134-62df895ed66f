<!--=======================孩子媒体搜索===========================-->
<div class="core_pop_wrap" id="J_mediaSearch_pop" style="display:none;">
	<div class="core_pop">
		<div style="width:800px;">
			<?php echo CHtml::beginForm($this->createUrl('//ivyMediaResponder/mediaSearch'),'GET',array('class'=>'J_ajaxForm'));?>
			<div class="pop_top">
				<a href="" id="J_mediaSearch_pop_x" class="pop_close"><?php echo Yii::t('global','Close');?></a>
				<strong id='d_tit'><?php echo Yii::t('teaching', '每周分配照片'); ?></strong>
			</div>
			<div class="pop_cont">
				<div class="pop_table weeklyMediaByWeek" style="height:500px;">
					<table width="100%">
						<colgroup>
							<col width="630">
						</colgroup>
						<tbody>
							<tr>
								<th>
									<?php echo CHtml::dropDownList('MediaSearch[ldid]',null, $learningDomains,
										array('class'=>'select_3','empty'=>Yii::t('learning', 'All Learning Domain')));?>
										
									<?php echo CHtml::dropDownList('MediaSearch[childid]',null, null,
										array('class'=>'select_2','empty'=>Yii::t('learning', 'All Child')));?>
										
									<?php echo CHtml::dropDownList('MediaSearch[weeknum]',null, null,
										array('class'=>'select_2','empty'=>Yii::t('learning', 'All Week')));?>
										
									<?php echo CHtml::textField('MediaSearch[freetag]','',
										array('class'=>'length_3 input','placeholder'=>Yii::t('learning', 'Free tag')));?>
								</th>
								<th>
									<button class="J_ajax_submit_btn btn" id="J_mediaSearch_pop_sub"><?php echo Yii::t('global','Filter');?></button>
								</th></tr>
						</tbody>
					</table>
									
					<div id="media-pager" class="page-container fr"></div>
					<div class="clear"></div>
					<ul id="media-list">
					
					</ul>
				</div>
			</div>
			<div class="pop_bottom tac">
                <button id="J_mediaSearch_pop_close" class="btn fr"><?php echo Yii::t('global','Cancel');?></button>
				<button type="button" onclick=""  class="btn btn_submit mr10 fr"><?php echo Yii::t('global','OK');?></button>
			</div>
			<?php echo CHtml::hiddenField('classid', $this->currentClassId);?>
			<?php echo CHtml::hiddenField('weeknum', $this->currentWeeknum);?>
			<?php echo CHtml::hiddenField('startyear', $this->currentStartYear);?>
			<?php echo CHtml::hiddenField('yid', $this->currentCalendarId);?>
			<?php echo CHtml::hiddenField('securityCode', md5(sprintf('%s&%s&%s&%s&%s', $this->currentClassId,$this->currentWeeknum,$this->currentStartYear,Yii::app()->user->getId(),$this->securityKey)));?>
			<?php echo CHtml::endForm();?>
		</div>
	</div>
</div>
<!--===========================结束==========================-->
<button class="btn" id="media-search-btn" onclick="openD()">Media Search</button>

<script>
head.use('dialog', 'draggable', function() {
    region_pop = $('#J_mediaSearch_pop');
    region_pop.draggable( { handle : '.pop_top'} );
    
    $('#J_mediaSearch_pop_x, #J_mediaSearch_pop_close').on('click', function(e){
		e.preventDefault();
		region_pop.hide();
	});
});

function openD(){
		$('#J_mediaSearch_pop').show().css({
			left : ($(window).width() - region_pop.outerWidth())/2,
			top : ($(window).height() - region_pop.outerHeight())/2 //+ $(document).scrollTop()
		});
}

var OAUploadBaseUrl = '<?php echo Yii::app()->params['OAUploadBaseUrl'];?>';
var nameList = <?php echo CJSON::encode($nameList);?> ;
var maxweek = <?php echo $maxweek;?>;
head.js('<?php echo Yii::app()->themeManager->baseUrl . '/base/js/teaching/semesterrpt.js?t='.time(); ?>');

function getMediaList(obj){
	$.ajax({
		url: '<?php echo Yii::app()->request->hostInfo;?>'+ $(obj).attr('href'),
		dataType: 'json',
		type: 'GET',
	}).done(function(data){
		postMediaSearch(data.data);
	});
}

function renderMediaList(mediaList){
	console.log(mediaList);
	var ul = $('#media-list');
	ul.html('');
	if(mediaList.length==0){
		
	}else{
		for(var i in mediaList){
			picAssigns = new Array;
			var item = $('<li></li>')
				.attr('id', 'mc'+i)
				.attr('year', mediaList[i].year)
				.attr('sid', mediaList[i].sid)
				.attr('mediaid', i)
				.html('<img class="preview" src="'+mediaList[i].thumb+'">');
			ul.append(item);
		}
	}	
}

function postMediaSearch(data){
	$('#media-pager').hide();
	$('#media-pager').html(data.pages);
	$('#media-pager a').unbind('click');
	
	renderMediaList(data.mediaData);
	
	$('#media-pager a').click(function(){
		getMediaList(this);
		return false;
	});
	$('#media-pager').show();
	
}
</script>