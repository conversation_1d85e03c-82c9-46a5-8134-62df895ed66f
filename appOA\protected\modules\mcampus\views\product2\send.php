<style>
    .tableShow  td, .tableShow  th {
        border-bottom: 1px solid #DDDDDD;
        border-top:none !important;
        background:#fff;
        width: 135px;
        vertical-align: middle !important;
    }
     .tableShow    thead tr th {
        position: sticky;
        top: 0;
        background: #FAFAFA;
        z-index: 1;
    }
     .tableShow  th:first-child,
     .tableShow   td:first-child{
        position: sticky;
        left: 0;
        background: #FAFAFA;
        right: 0px;
        border-left: 1px solid #DDDDDD ;
        border-right:none ;
        width: 140px;
        box-shadow: 4px 0px 6px 0px rgba(0,0,0,0.0600);
    }
    .tableShow  th:last-child,
    .tableShow  td:last-child {
        position: sticky;
        left: 0;
        background: #FAFAFA;
        right: 0px;
        border-right:none ;
        width: 100px;
        text-align:center;
        box-shadow: -4px 0px 6px 0px rgba(0,0,0,0.0600);
        border-right: 1px solid #DDDDDD;
     }
    .tableShow  th:last-child,
    .tableShow  th:first-child {
        z-index: 3;
        /* background: #FAFAFA; */
    }
</style>
<div id='container' v-cloak>
    <div class="" v-if='dataList.products_cat'>
        <div class="mb20 flex">
            <div>
                <img :src="dataList.products_cat.img" class='image' alt="">
            </div>
            <div class='flex1 ml10'>
                <div class='font18 mt15'><label>{{dataList.products_cat.title}}</label> </div>
                <div class='mt5'><span class="label label-default">已经发放：{{dataList.total_count}}</span></div>
            </div>
        </div>
       <div class='grey mb5'>
            <div class='form-inline'>
                <div class="form-group mr10">
                    <input type="text" class="form-control"  placeholder="孩子姓名" v-model='child_name' @keyup.enter='searchInit()'>
                </div>
                <div class="form-group mr10">
                    <select class="form-control" v-model='class_id'>
                        <option value="" selected>全部班级</option>
                        <option v-for='(list,index) in class_list' :value='list.classid'>{{list.title}}</option>
                    </select>
                </div>
                <div class="form-group mr10">
                    <input type="text" class="form-control datepicker" id="startdate"
                                   placeholder="<?php echo Yii::t('user', '开始日期') ?>"
                                    >
                </div>
                <div class="form-group mr10">
                <input type="text" class="form-control datepicker" id="enddate"
                                   placeholder="<?php echo Yii::t('user', '结束日期') ?>"
                                    >
                </div>
                <button type="submit" class="btn btn-primary pull-right ml15" @click='searchInit()' :disabled='disabled'>搜索</button>
                <button type="submit" class="btn btn-default pull-right" @click='reset()'  :disabled='disabled'>重置</button>
            </div>
        </div>
        <div class="tab-content" v-if='dataList.list.length!=0'>
            <div role="tabpanel" class="tab-pane fade in active" id="confirmation">
                <div class='flex mt20 pt10'>
                    <div class='flex1'>
                    </div>
                    <div>
                        <button type="submit" class="btn btn-default mr10" @click='exportData' :disabled='disabled'>导出</button>
                    </div>
                </div>
                <div class='table_wrap scrollbar mt20  mb20' >
                    <table class="table tableShow" id='table' >
                        <thead>
                            <tr>
                                <th width="140" style='z-index:2;vertical-align: middle !important; width:100px;' class='font14 '>
                                    <!-- <input type="checkbox" v-model='checkAll' @click='allChecked($event)'> -->
                                    <strong class='ml10'><?php echo Yii::t("ptc", "订单号");?></strong>
                                </th>

                                <th  class="">
                                    <div class='relative'>
                                        <span class='mr10'>学生</span>
                                        <span class='top' :class='sort=="childid.asc"?"topActive":""' @click='sort=="childid.asc"?sort="":sort="childid.asc";init()'></span>
                                        <span class='bottom' :class='sort=="childid.desc"?"bottomActive":""'  @click='sort=="childid.desc"?sort="":sort="childid.desc";init()'></span>
                                    </div>
                                </th>
                                <th  class="">
                                    <div class='relative'>
                                        <span class='mr10'>账单班级</span>
                                    </div>
                                </th>
                                <th  class=""  style=' width:290px;'>
                                    <div class='relative'>
                                        <span class='mr10'>商品</span>
                                        <span class='top' :class='sort=="pid.asc"?"topActive":""' @click='sort=="pid.asc"?sort="":sort="pid.asc";init()'></span>
                                        <span class='bottom' :class='sort=="pid.desc"?"bottomActive":""'  @click='sort=="pid.desc"?sort="":sort="pid.desc";init()'></span>
                                    </div>
                                </th>
                                <th  class="" style=' width:66px;'>
                                    <div class='relative'>
                                        <span class='mr10'>金额</span>
                                        <span class='top' :class='sort=="sum_price.asc"?"topActive":""' @click='sort=="sum_price.asc"?sort="":sort="sum_price.asc";init()'></span>
                                        <span class='bottom' :class='sort=="sum_price.desc"?"bottomActive":""'  @click='sort=="sum_price.desc"?sort="":sort="sum_price.desc";init()'></span>
                                    </div>
                                </th>
                                <th  class="" style=' width:76px;'>
                                    <div class='relative'>
                                        <span class='mr10'>商品数量</span>
                                        <span class='top' :class='sort=="num.asc"?"topActive":""' @click='sort=="num.asc"?sort="":sort="num.asc";init()'></span>
                                        <span class='bottom' :class='sort=="num.desc"?"bottomActive":""'  @click='sort=="num.desc"?sort="":sort="num.desc";init()'></span>
                                    </div>
                                </th>
                                <th  class=""  style=' width:93px;'>
                                    下单时间
                                </th>
                                <th  class="" style=' width:93px;'>
                                    <div class='relative'>
                                        <span class='mr10'>操作时间</span>
                                        <span class='top' :class='sort=="updated_time.asc"?"topActive":""' @click='sort=="updated_time.asc"?sort="":sort="updated_time.asc";init()'></span>
                                        <span class='bottom' :class='sort=="updated_time.desc"?"bottomActive":""'  @click='sort=="updated_time.desc"?sort="":sort="updated_time.desc";init()'></span>
                                    </div>
                                </th>

                                <th  class="" style='z-index:2;vertical-align: middle !important; width:80px;' class='font14 '>
                                    操作
                                </th>
                            </tr>
                        </thead>
                        <tbody v-if='dataList.list.length!=0'>
                            <tr  v-for='(list,index) in dataList.list'>
                                <td  class=''>
                                    <!-- <input type="checkbox"  :value="list.id" v-model='checkList' @click='Checked()'>  -->
                                    <span class='ml10'>{{list.order_id}}</span>
                                </td>
                                <td  class="">
                                    <div>{{list.child_name}}</div>
                                    <div>{{list.child_class}}</div>
                                </td>
                                <td  class="">
                                    {{list.class_name}}
                                </td>
                                <td  class="">
                                    <p>{{list.product_title}}</p>
                                    <div><span class='change' v-if='list.exchange_flag==1'>换</span> {{list.attr1_title}} {{list.attr2_title}}</div>
                                </td>
                                <td  class="">
                                    {{list.sum_price}}
                                </td>
                                <td  class="">
                                    {{list.num}}
                                </td>
                                <td  class="">
                                    <div>{{list.order_date}}</div>
                                    <div>{{list.order_time}}</div>
                                </td>
                                <td  class="">
                                    <div>{{list.updated_date}}</div>
                                    <div>{{list.updated_time}}</div>
                                </td>
                                <td  class="last">
                                    <div class="btn-group pull-right">
                                        <button type="button" class="btn btn-link dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" @click='showBox(list,index)'>
                                            更多 <span class="caret"></span>
                                        </button>
                                        <!-- <ul class="dropdown-menu">
                                            <li><a href="javascript:;"  @click='exchangeDetails(list)' v-if='list.exchange_flag==1'>换货详情</a></li>
                                            <li><a href="javascript:;" @click='exchangeClick(list)'>换货</a></li>
                                            <li><a href="javascript:;"  @click='RefundClick(list)'>退款</a></li>
                                        </ul> -->
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                        <tbody  v-else>
                            <tr >
                                <td colspan="9" class='noBg text-center'><span class="empty">没有找到数据.</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <ul class="tip-box" id='tip-box'>
                    <li><a href="javascript:;"  @click='exchangeDetails(popupList)' v-if='popupList.exchange_flag==1'>换货详情</a></li>
                    <li v-if="is_cat_share==false"><a href="javascript:;" @click='exchangeClick(popupList)'>换货</a></li>
                    <li><a href="javascript:;"  @click='RefundClick(popupList)'>退款</a></li>
                </ul>
                <div class='flex' v-if='dataList.list.length!=0'>
                    <div  class='flex1'>
                        <span>共 {{dataList.total_count}} 项</span>
                        <span>总金额：{{dataList.total_amount}}</span>
                    </div>
                    <div>
                        <nav aria-label="Page navigation"   class="text-left ml10" v-if='dataList.page_count>1'>
                            <ul class="pagination">
                                <li v-if='currentPage >1'>
                                    <a href="javascript:void(0)" @click="plus(1)" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                <li v-else class="disabled">
                                    <a aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                <li class="previous" v-if='currentPage >1'>
                                    <a href="javascript:void(0)" @click="prev">‹</a>
                                </li>
                                <li class="disabled" v-else>
                                    <a href="javascript:void(0)">‹</a>
                                </li>
                                <li v-for='(data,index) in page_count' :class="{ active:data==currentPage }">
                                    <a href="javascript:void(0)" @click="plus(data)">{{data}}</a>
                                </li>
                                <li class="previous" v-if='currentPage <CopyPage'>
                                    <a href="javascript:void(0)" @click="next">›</a>
                                </li>
                                <li class="previous disabled" v-else>
                                    <a href="javascript:void(0)">›</a>
                                </li>
                                <li v-if='currentPage < CopyPage'>
                                    <a href="javascript:void(0)" @click="plus(CopyPage)" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                <li v-else class="disabled">
                                    <a aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
        <div v-else>
            <el-empty description="暂无数据"></el-empty>
        </div>
    </div>
    <div class="modal fade" id="refundModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" >
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "退款");?></h4>
                </div>
                <div class="modal-body" v-if='Object.keys(refundData).length!=0'>
                    <div class="media" v-if='dataList.child_list[refundData.childid]'>
                        <div class="media-left pull-left media-middle">
                            <a href="javascript:void(0)">
                                <img :src="dataList.child_list[refundData.childid].avatar" data-holder-rendered="true" class="avatar">
                            </a>
                        </div>
                        <div class="media-body media-middle">
                            <h4 class="media-heading font14 mt5 color3">{{dataList.child_list[refundData.childid].name}}</h4>
                            <h4 class="media-heading font12 mt5 color6">{{dataList.child_list[refundData.childid].className}}</h4>
                        </div>
                    </div>
                    <div class="panel panel-default mt20">
                        <div class="panel-heading flex"><strong class='flex1 color3'>订单信息</strong><span class='color6'>下单时间：{{refundData.order_time}}</span></div>
                        <div class="panel-body">
                            <div class="col-md-12 font14">
                                <div class="col-md-6 mb10 flex"><span class='color6'>订单号：</span><span class='color3 flex1'>{{refundData.order_id}}</span></div>
                                <div class="col-md-6 mb10 flex"><span class='color6'>Pid：</span><span class='color3 flex1'>{{refundData.title}}</span></div>
                                <div class="col-md-6 mb10 flex"><span class='color6'>规格：</span><span class='color3 flex1'>{{refundData.attr1_title}} {{refundData.attr2_title}}</span></div>
                                <div class="col-md-6 mb10 flex"><span class='color6'>数量：</span><span class='color3 flex1'>{{refundData.num}}</span></div>
                                <div class="col-md-6 mb10 flex"><span class='color6'>付款金额：</span><span class='color3 flex1'>{{refundData.sum_price}}</span></div>
                            </div>
                        </div>
                    </div>
                    <div class='text-right'>退款总额：<span class='colorRed'>{{refundData.sum_price}}</span> </div>
                    <div>
                        <p>退款原因</p>
                        <textarea class="form-control" rows="3" v-model='reason'></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button" class="btn btn-primary" @click='confirmRefund()'  :disabled='disabled'><?php echo Yii::t("newDS", "确认退款");?></button>
                </div>
                <div class='clearfix'></div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="exchangeModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "换货");?></h4>
                </div>
                <div class="modal-body"  v-if=' Object.keys(exchangeList.list).length!=0' >
                    <div class="media" v-if='dataList.child_list[exchangeList.child.childid]'>
                        <div class="media-left pull-left media-middle">
                            <a href="javascript:void(0)">
                                <img :src="dataList.child_list[exchangeList.child.childid].avatar" data-holder-rendered="true" class="avatar">
                            </a>
                        </div>
                        <div class="media-body media-middle">
                            <h4 class="media-heading font14 mt5 color3">{{dataList.child_list[exchangeList.child.childid].name}}</h4>
                            <h4 class="media-heading font12 mt5 color6">{{dataList.child_list[exchangeList.child.childid].className}}</h4>
                        </div>
                    </div>
                    <div class=' mt20'><strong class='color3 font14'>{{exchangeList.child.product_title}}</strong><span class="label label-default ml10 color3 labelBg">订单号：{{exchangeList.child.order_id}}</span></div>
                    <div class='font12 mt10'><span class='color6'>下单时间：{{exchangeList.child.order_date}} {{exchangeList.child.order_time}}</span></div>
                    <div class="panel panel-default mt20" v-for='(list,index) in exchangeList.list'>
                        <div class="panel-body">
                            <div class="col-md-12 font14">
                                <div class="col-md-6 mb10"><span class='color6'>单价：</span><span class='color3'>{{list.unit_price}}</span></div>
                                <div class="col-md-6 mb10"><span class='color6'>数量：</span><span class='color3'>{{list.num}}</span></div>
                                <div class="col-md-6 mb10 flex">
                                    <span class='color6 lineHeight'>颜色：{{list.copyAttr1_title}}</span>
                                    <span class='color3 flex1 ml10'>
                                        <select class="form-control" disabled  v-model='list.attr1_title'  v-if='exchangeList.attr.length==0'>
                                            <option value="" disabled selected hidden>暂无库存</option>
                                        </select>
                                        <select class="form-control"  v-model='list.attr1_title' @change='filterSize()' v-else>
                                            <option v-for="(item,index) in exchangeList.attr" :value='item.color'>{{item.color}}</option>
                                        </select>
                                    </span>
                                </div>
                                <div class="col-md-6 mb10 flex">
                                    <span class='color6 lineHeight'>尺寸：{{list.copyAttr2_title}}</span>
                                    <span class='color3 flex1 ml10'>
                                        <select class="form-control" disabled  v-if='exchangeList.attr.length==0'>
                                            <option value="" disabled selected hidden>暂无库存</option>
                                        </select>
                                        <select class="form-control"  v-model='list.paid' v-else>
                                            <option value="" disabled selected hidden>请选择</option>
                                            <option v-for="(item,index) in filterSize(list.attr1_title)" :value='item.value'>{{item.size}}</option>
                                        </select>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <p><label>操作</label></p>
                    <div class='flex'>
                        <div class='flex1'>学生是否需要退回校服</div>
                        <div>
                            <label class="radio-inline">
                                <input type="radio" name="inlineRadioOptions" id="inlineRadio1" value="1" v-model='sendback'> 是
                            </label>
                            <label class="radio-inline">
                                <input type="radio" name="inlineRadioOptions" id="inlineRadio2" value="2" v-model='sendback'> 否
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button" class="btn btn-primary" @click='confirmExchange()' v-if='exchangeList.attr.length!=0' :disabled='disabled'><?php echo Yii::t("newDS", "确认换货");?></button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="exchangeDetailModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content modal-lg">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "换货详情");?></h4>
                </div>
                <div class="modal-body"  v-if='exchangeDetailList.original' >
                    <div class="media" v-if='dataList.child_list[exchangeDetailList.original.childid]'>
                        <div class="media-left pull-left media-middle">
                            <a href="javascript:void(0)">
                                <img :src="dataList.child_list[exchangeDetailList.original.childid].avatar" data-holder-rendered="true" class="avatar">
                            </a>
                        </div>
                        <div class="media-body media-middle">
                            <h4 class="media-heading font14 mt5 color3">{{dataList.child_list[exchangeDetailList.original.childid].name}}</h4>
                            <h4 class="media-heading font12 mt5 color6">{{dataList.child_list[exchangeDetailList.original.childid].className}}</h4>
                        </div>
                    </div>
                    <div class="mt20 mb15"><span class='flex1'>订单信息 <span class="label label-default">原订单</span></span></div>
                    <div>
                        <table class='table table-bordered text-center'>
                            <colgroup>
                                <col width="100">
                                <col width="200">
                                <col width="100">
                                <col width="200">
                            </colgroup>
                            <tbody>
                                <tr>
                                    <td class='color6 bgfa'>订单号</td>
                                    <td class='color3'>{{exchangeDetailList.original.order_id}}</td>
                                    <td class='color6 bgfa'>下单时间</td>
                                    <td class='color3'>{{exchangeDetailList.original.order_time}}</td>
                                </tr>
                                <tr>
                                    <td class='color6 bgfa'>商品名称</td>
                                    <td class='color3'>{{exchangeDetailList.original.product_title}}</td>
                                    <td class='color6 bgfa'>规格</td>
                                    <td class='color3'>{{exchangeDetailList.original.attr1_title}} {{exchangeDetailList.original.attr2_title}}</td>
                                </tr>
                                <tr>
                                    <td class='color6 bgfa'>数量</td>
                                    <td class='color3'>{{exchangeDetailList.original.num}}</td>
                                    <td class='color6 bgfa'>付款金额</td>
                                    <td class='color3'>{{exchangeDetailList.original.sum_price}}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class='mt20'>
                        <table class="table mt20" id='stocking' >
                            <thead>
                                <tr>
                                    <th  class="" style='width:50px'>
                                        序号
                                    </th>
                                    <th  class="">
                                        更换颜色
                                    </th>
                                    <th  class="">
                                        更换尺寸
                                    </th>
                                    <th  class="">
                                        更换数量
                                    </th>
                                    <th  class="">
                                        单价
                                    </th>
                                    <th  class="" style='width:80px'>
                                        状态
                                    </th>
                                    <th  class="" style='width:300px'>
                                        是否需要商品退回
                                    </th>
                                    <th  class="" style='width:130px'>
                                        时间
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr  v-for='(list,index) in exchangeDetailList.exchange' :class='list.status==10?"danger":""'>
                                    <td  class="">
                                        {{index+1}}
                                    </td>
                                    <td  class="">
                                        {{list.attr1_title}}
                                    </td>
                                    <td  class="">
                                        {{list.attr2_title}}
                                    </td>
                                    <td  class="">
                                        {{list.num}}
                                    </td>
                                    <td  class="">
                                        {{list.unit_price}}
                                    </td>
                                    <td  class="">
                                        <span class='relative'>
                                            <span class='point' :class='list.status==10?"red":list.status==20?"green":"grey"'></span>
                                            <span class='ml10'>{{list.status==10?"等待换货":list.status==20?"换货完成":'取消换货'}}</span>
                                        </span>
                                    </td>
                                    <td  class="">
                                        {{list.sendback==1?'是':list.sendback==2?'否':''}}
                                        <div  v-if='list.is_sendback!=0' class='mt5 flex'> <span>{{list.is_sendback==1?'未退回':list.is_sendback==2?'已退回':''}} 备注：</span> <span  class='flex1' v-html='list.remark'></span></div>
                                    </td>
                                    <td  class="">
                                        {{list.updated_at}}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="noRefundModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "提示");?></h4>
                </div>
                <div class="modal-body">
                    <span>暂未开通</span>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                </div>
            </div>
        </div>
    </div>
</div>
    <script>
        var childBaseUrl = '<?php echo $this->createUrl('//child/index/index')."&childid=";?>';
         var container = new Vue({
        el: "#container",
        data: {
            dataList:{},
            class_list:[],
            child_name:'',
            class_id:'',
            startdate:'',
            enddate:'',
            stockingList:{},
            checkAll:false,
            checkList:[],
            refundData:{},
            currentPage:1,
            confirmList:{},
            exchangeList:{
                child:{},
                list:{},
                attr:{}
            },
            CopyPage:0,
            page_count:[],
            disabled:false,
            exchangeDetailList:{},
            sendback:'',
            sort:'',
            popupList:{},
            reason:'',
            is_cat_share:false
        },
        watch:{},
        created:function(){
            this.init('page')
        },
        mounted(){
             $('.datepicker').datepicker({
                 changeMonth: true,
                 changeYear: true,
                 dateFormat: 'yy-mm-dd'
             });
         },
        methods:{
            showBox(list,index){
                this.popupList=list
                let box = document.querySelector('#tip-box');
                box.style.top =event.pageY-100 + 'px';
                box.style.display = 'block';
            },
            filterSize(list){
                var data=[]
                this.exchangeList.attr.forEach((item,index) => {
                   if(item.color==list){
                        data=item.attr
                   }
                })
                return data
            },
            pagesSize(type){
                var _this = this;
                if(_this.currentPage<10){
                    if(_this.CopyPage>=10){
                        _this.page_count=[1,2,3,4,5,6,7,8,9,10]
                    }else{
                        var numPage=[]
                        for(var i=1;i<=_this.CopyPage;i++){
                            numPage.push(i)
                        }
                        _this.page_count=numPage
                    }
                }else if(_this.currentPage<=_this.CopyPage){
                    if(_this.CopyPage-_this.currentPage>=4){
                        var minPage=_this.currentPage-5
                        var maxPage=_this.currentPage+4
                    }else{
                        var minPage=_this.currentPage-(9-((_this.CopyPage-_this.currentPage)))
                        var maxPage=_this.currentPage+(_this.CopyPage-_this.currentPage)
                    }
                    var numPage=[]
                    for(var i=minPage;i<=maxPage;i++){
                        numPage.push(i)
                    }
                    _this.page_count=numPage
                }
            },
            next(){
                var _this = this;
                _this.currentPage = Number(this.currentPage) + 1
                this.init()
                this.pagesSize()
            },
            prev() {
                var _this = this;
                _this.currentPage = Number(this.currentPage) - 1
                this.init()
                this.pagesSize()
            },
            plus(index) {
                var _this = this;
                _this.currentPage = Number(index)
                this.init()
                this.pagesSize()

            },
            initPage(){
                var _this = this;
                _this.CopyPage=JSON.parse(JSON.stringify(_this.dataList.page_count))
                if(_this.CopyPage>=10){
                    _this.page_count=[1,2,3,4,5,6,7,8,9,10]
               }else{
                    var numPage=[]
                    for(var i=1;i<=_this.CopyPage;i++){
                        numPage.push(i)
                    }
                    _this.page_count=numPage
               }
            },
            reset(){
                this.child_name=''
                this.class_id=''
                this.startdate=''
                this.enddate=''
                $('#startdate').val('')
                $('#enddate').val('')
                this.currentPage=1
                this.init('page')
            },
            pageSize(index){
                this.currentPage=index
                this.init()
            },
            searchInit(){
                this.currentPage=1
                this.init('page')
            },
            init(page){
                var that=this
                that.disabled=true
                $.ajax({
                    url: '<?php echo $this->createUrl("getProductOrder") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        status:40,
                        "search_input[child_name]":this.child_name,
                        "search_input[class_id]": this.class_id,
                        "search_input[startdate]":this.startdate,
                        "search_input[enddate]": this.enddate,
                        page:this.currentPage,
                        sort:this.sort
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.dataList=data.data
                            that.class_list=data.data.class_list
                            that.is_cat_share=data.data.is_cat_share
                            if(page){
                                that.initPage()
                            }
                            that.$nextTick(()=>{
                                $("#startdate").datepicker({
                                    dateFormat: "yy-mm-dd ",
                                });
                                $( "#startdate").on('change', function () {
                                    container.startdate = $('#startdate').val();
                                });
                                $("#enddate").datepicker({
                                    dateFormat: "yy-mm-dd ",
                                });
                                $( "#enddate").on('change', function () {
                                    container.enddate = $('#enddate').val();
                                });
                            })
                            that.disabled=false
                        } else {
                            head.dialog.alert(data.message);
                        }
                        that.disabled=false

                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.disabled=false
                    }
                })
            },
            exportData(){
                var that=this
                this.disabled=true
                $.ajax({
                    url: '<?php echo $this->createUrl("exportProductOrder") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        status:40,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            const filename ='已经发放.xlsx';
                            const ws_name = "SheetJS";
                            var exportDatas = [];
                            for(var i=0;i<data.data.list.length;i++){
                                exportDatas.push({
                                    "订单号":data.data.list[i].order_id,
                                    "学生id":data.data.list[i].childid,
                                    "姓名":data.data.list[i].child_name_cn+"\r\n"+data.data.list[i].child_name_en,
                                    "学生班级":data.data.list[i].child_class,
                                    "账单班级":data.data.list[i].class_name,
                                    "类型":data.data.list[i].export_product_title,
                                    "规格":data.data.list[i].attr1_title_cn +' '+ data.data.list[i].attr2_title_cn+"\n"+data.data.list[i].attr1_title_en + ' ' + data.data.list[i].attr2_title_en,
                                    "数量":data.data.list[i].num,
                                    "金额":data.data.list[i].sum_price,
                                    "下单时间":data.data.list[i].order_time,
                                    "操作时间":data.data.list[i].updated_time,
                                })
                            }
                            var wb=XLSX.utils.json_to_sheet(exportDatas,{
                                origin:'A1',// 从A1开始增加内容
                                header: ['订单号',"学生id",'姓名','学生班级','账单班级','类型','规格','数量','金额','下单时间','操作时间']
                            });
                            for (let key in wb){
                                wb[key]['s'] = {
                                    font: {
                                        sz: 10,
                                        bold: false,//设置标题是否加粗
                                    },
                                    alignment: {
                                        horizontal: 'center',
                                        vertical: 'center',
                                        wrapText: true
                                    },//设置标题水平竖直方向居中，并自动换行展示
                                    fill: {
                                        fgColor: { rgb: 'ebebeb' }//设置标题单元格的背景颜色
                                    },
                                    border: {//添加边框
                                        bottom: {
                                            style: 'thin',
                                            color: '000000'
                                        },
                                        left: {
                                            style: 'thin',
                                            color: '000000'
                                        },
                                        right: {
                                            style: 'thin',
                                            color: '000000'
                                        },
                                        top: {
                                            style: 'thin',
                                            color: '000000'
                                        }
                                    }
                                };
                            }
                            wb['!cols'] = [{wpx:100},{wpx:100},{wpx:120},{wpx:120},{wpx:200},{wpx:200}];
                            openDownload(sheet2blob(wb,ws_name), filename);
                            that.disabled=false
                        } else {
                            head.dialog.alert(data.message);
                            that.disabled=false

                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.disabled=false

                    }
                })

            },
            RefundClick(list){
                var that=this
                // $('#noRefundModal').modal('show')
                // return
                $.ajax({
                    url: '<?php echo $this->createUrl("showConfirmRefundDetail") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        status:20,
                        id:list.id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                          $('#refundModal').modal('show')
                         that.refundData=data.data
                        } else {
                            head.dialog.alert(data.message);
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                })
            },
            confirmRefund(){
                var that=this
                that.disabled=true
                $.ajax({
                    url: '<?php echo $this->createUrl("confirmRefund") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        status:30,
                        ids:[this.refundData.id],
                        reason:this.reason
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                             $('#refundModal').modal('hide')
                             that.refundData={}
                            resultTip({
                                msg: data.state
                            });
                            that.init()
                        } else {
                            head.dialog.alert(data.message);
                            $('#refundModal').modal('hide')
                        }
                        that.disabled=false
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.disabled=false
                    }
                })
            },
            exchangeClick(list){
                this.exchangeList.child=list
                this.sendback=''
                var that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("showConfirmExchangeDetail") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        status:30,
                        id:list.id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.exchangeList.list=JSON.parse(JSON.stringify(data.data.product_list))
                            that.exchangeList.attr=data.data.attr
                            that.exchangeList.list.forEach((item,index) => {
                                item.copyAttr1_title=JSON.parse(JSON.stringify(item.attr1_title))
                                item.copyAttr2_title=JSON.parse(JSON.stringify(item.attr2_title))
                                item.copyPaid=JSON.parse(JSON.stringify(item.paid))
                                item.paid=''
                                if(data.data.attr.length==0){
                                    item.attr1_title=''
                                }
                            })
                           $('#exchangeModal').modal('show')
                        } else {
                            head.dialog.alert(data.message);

                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                })

            },
            confirmExchange(){
                var that=this
                let ids=[]
                this.exchangeList.list.forEach((item,index) => {
                    if(item.paid!='' && item.paid!=item.copyPaid){
                        ids.push(item.paid)
                    }
                })
                if(ids.length==0){
                    resultTip({
                        error: 'warning',
                        msg: '请选择颜色或尺码'
                    });
                    return
                }
                that.disabled=true
                $.ajax({
                    url: '<?php echo $this->createUrl("commitExchange") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        status:30,
                        id:this.exchangeList.child.id,
                        paid:ids,
                        sendback:this.sendback
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg: data.state
                            });
                            that.init()
                           $('#exchangeModal').modal('hide')
                        } else {
                           $('#exchangeModal').modal('hide')
                            head.dialog.alert(data.message);
                        }
                        that.disabled=false
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.disabled=false
                    }
                })
            },
            exchangeDetails(data){
                this.exchangeDetailsList=data
                var that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("showExchangeDetail") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        status:20,
                        id:this.exchangeDetailsList.id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                          $('#exchangeDetailModal').modal('show')
                         that.exchangeDetailList=data.data
                        } else {
                            head.dialog.alert(data.message);

                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                })
            },
        }
    })
    </script>
