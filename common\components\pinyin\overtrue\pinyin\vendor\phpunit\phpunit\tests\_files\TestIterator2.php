<?php
class TestIterator2 implements Iterator
{
    protected $data;

    public function __construct(array $array)
    {
        $this->data = $array;
    }

    public function current()
    {
        return current($this->data);
    }

    public function next()
    {
        next($this->data);
    }

    public function key()
    {
        return key($this->data);
    }

    public function valid()
    {
        return key($this->data) !== null;
    }

    public function rewind()
    {
        reset($this->data);
    }
}
