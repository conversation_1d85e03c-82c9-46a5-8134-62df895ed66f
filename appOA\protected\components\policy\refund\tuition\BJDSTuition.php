<?php
class BJDSTuition implements RefundInterface
{
    public $invoiceObj;
    public $config;
    public $workflow;
    public function __construct($invoiceObj,$config,$workflow) {
        $this->invoiceObj = $invoiceObj;
        $this->config = $config->configs;
        $this->workflow = $workflow;
    }

    public function fee(){
        $ret = 0;
        $rate = $this->getRate();
        //订位金
        $deposit = isset($this->config['REFUND_FEE_RATE']['deposit']) ? $this->config['REFUND_FEE_RATE']['deposit'] : 0;
        //判断是否年付
        if ($this->isAnnualPay() === true){
            //上学期退学
            if ($rate['n'] === false){
                $ret = round(($this->invoiceObj->original_amount - $this->getHalfYearAmount() - $deposit) * $rate['r'] + $this->getHalfYearAmount());
            }else{
                $ret = round(($this->invoiceObj->original_amount - $this->getHalfYearAmount() - $deposit) * $rate['r']);
            }
        }else{
            if ($this->getDiscout() === true){
                $ret = round(($this->invoiceObj->original_amount - ($this->invoiceObj->nodiscount_amount-$this->invoiceObj->original_amount) - $deposit) * $rate['r']);
            }else{
                $ret = round(($this->invoiceObj->original_amount - $deposit)*$rate['r']);
            }
        }
        return ($ret<0) ? 0 :$ret;
    }
    
  
    /**
     * 费率
     */
    public function getRate(){
        $ret = array('n'=>false,'r'=>1);
        $refundDate = $this->workflow->opertationObj->exte1;
        $dates = $this->config['REFUND_FEE_RATE']['tuition'];
        foreach ($dates as $key=>$val){
            foreach ($val as $k=>$v){
                if ($key){
                    $ret['n'] = true;
                }
                if ($refundDate <  strtotime($k)){
                    $ret['r'] = $v;
                    break 2;
                }
            }
        }
        return $ret;
    }
    
    /**
     * 判断是否年付
     */
    public function isAnnualPay(){
        $day = ($this->invoiceObj->enddate - $this->invoiceObj->startdate)/86400;
        return (($day/30)>5) ? true :false;
    }
    
    /**
     * 获取折扣 
     */
    public function getDiscout(){
        return ($this->invoiceObj->discount_id) ? true :false;
    }
    
    /**
     * 计算半年费用
     */
    public function getHalfYearAmount(){
        Yii::import('common.models.calendar.CalendarSchoolDays');
        $schoolDay = CalendarSchoolDays::model()->getCalendarSchoolDay($this->invoiceObj->calendar_id, $this->invoiceObj->startdate, $this->invoiceObj->enddate);
        $schoolDay = (count($schoolDay) > 10) ? 10 :count($schoolDay) > 10;
        return ($this->invoiceObj->original_amount/$schoolDay)*5;
    }
}
