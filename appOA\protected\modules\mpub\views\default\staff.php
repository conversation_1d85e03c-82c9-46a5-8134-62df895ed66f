<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo Yii::t('site','Ivy Family')?></li>
        <li><?php echo Yii::t('site','Ivyers')?></li>
        <li class="active"><?php echo $staff->getName();?></li>
    </ol>
    <div class="row">
        <div class="col-md-2">
            <div class="panel panel-default">
                <div class="panel-body">
                    <?php if($staff->level == 0):?>
                    <button type="button" class="btn btn-danger btn-lg btn-block disabled">已离职</button>
                    <?php endif;?>

                    <h4 class="text-center">
                        <span class="glyphicon glyphicon-user"></span>
                        <strong><?php echo $staff->getName();?></strong>
                    </h4>
                    <p class="text-center">
                        <?php echo $staff->profile->occupation->getName();?>
                    </p>
                    <?php
                    if($staff->user_avatar):
                        echo CHtml::image(OA::CreateOAUploadUrl('users', $staff->user_avatar),'',array("class"=>"text-center img-rounded img-responsive mb10"));
                    endif;
                    ?>
                    <div class="caption">
                        <p class="text-center"><a href="mailto:<?php echo $staff->email;?>"><?php echo $staff->email;?></a></p>
                        <p class="text-center"><a href="<?php echo $this->createUrl('//mpub/default/ivyers', array('branchId'=>$staff->profile->branch));?>">
                                <?php echo $branches[$staff->profile->branch]->title;?>
                            </a></p>
                    </div>
                </div>
            </div>

            <?php if(!is_null($approver)):?>
            <div class="panel panel-default">
                <div class="panel-body">
                    <h4>
                        <span class="label label-primary pull-left mr5">
                            <span class="glyphicon glyphicon-user"></span> 上级主管</span>
                    </h4>
                    <a href="<?php echo $this->createUrl('//mpub/default/staff',array('id'=>$approver->uid));?>" target="_blank">
                        <?php echo $approver->getName();?>
                    </a>
                </div>
            </div>
            <?php endif;?>
        </div>

        <?php if($staff->staffInfo): ?>
        <div class="col-md-3">
            <div class="panel panel-default">
                <div class="panel-heading"><?php echo Yii::t('ivyer','Public Bio');?></div>
                <div class="panel-body">
                    <p>
                        <?php echo CHtml::image(OA::CreateOAUploadUrl('infopub/staff', $staff->staffInfo->staff_photo),'',array("class"=>"img-rounded pull-left mr10 mb5"));?>
                        <?php echo $staff->staffInfo->intro_cn;?>
                    </p>
                    <?php if($staff->staffInfo->intro_en):?>
                    <p>
                        <?php echo $staff->staffInfo->intro_en;?>
                    </p>
                    <?php endif;?>
                </div>
            </div>

            <div class="panel panel-default">
                <div class="panel-heading"><?php echo Yii::t('ivyer','Extra Info');?></div>
                <div class="panel-body">
                    <h4><span class="label label-primary pull-left mr5"><?php echo Yii::t('labels','Nationality');?></span><?php echo $staff->profile->country->getName();?></h4>
                    <?php if($staff->staff->startdate):?>
                        <h4><span class="label label-primary pull-left mr5"><?php echo Yii::t('ivyer','Start Date');?></span><?php echo OA::formatDateTime($staff->staff->startdate);?></h4>
                    <?php endif;?>
                    <?php if($staff->staff->leavedate && $staff->level == 0):?>
                        <h4><span class="label label-primary pull-left mr5"><?php echo Yii::t('ivyer','Exit Date');?></span><?php echo OA::formatDateTime($staff->staff->leavedate);?></h4>
                    <?php endif;?>
                    <?php if($staff->user_icq):?>
                        <h4><span class="label label-primary pull-left mr5"><?php echo Yii::t('labels','QQ Num#');?></span><?php echo $staff->user_icq;?></h4>
                    <?php endif;?>
                    <?php if($staff->user_intrest):?>
                        <h4><span class="label label-primary pull-left mr5"><?php echo Yii::t('labels','Interest');?></span><?php echo $staff->user_intrest;?></h4>
                    <?php endif;?>
                    <?php if($staff->staff->mobile_telephone):?>
                        <h4><span class="label label-primary pull-left mr5"><?php echo Yii::t('labels','Cellphone');?></span><?php echo $staff->staff->mobile_telephone;?></h4>
                    <?php endif;?>
                    <?php if($staff->bio):?>
                        <p><h4><span class="label label-primary pull-left mr5"><?php echo Yii::t('ivyer','Personal Bio');?></span></h4><?php echo $staff->bio;?></p>
                    <?php endif;?>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <?php if($branchData):?>
        <div class="col-md-2">
            <div class="panel panel-default">
                <div class="panel-heading">管理多校园</div>
                <div class="panel-body">
                    <?php $admTypes = OA::getMultiAdmType();?>
                    <?php foreach(array_keys($branchData) as $key):?>
                    <p>
                    <h4><span class="label label-primary pull-left mr5"><?php echo $admTypes[$key];?></span></h4>
                    <?php foreach($branchData[$key] as $campus):?>
                        <span class="text-info mr5"><?php echo $campus;?></span>
                    <?php endforeach;?>
                    <p>
                        <?php endforeach;?>
                </div>
            </div>
        </div>
        <?php endif;?>

    </div>
</div>
