<style>
    #user-leave-info{
        padding-left: 20px;
    }
    #user-leave-info h5{
        padding: 10px 0px;
    }
    .table-b td{
        border-top: 8px solid #d36b68;  
        border-right: 1px solid #e1e1e1;   
        border-bottom: 1px solid #e1e1e1;   
        border-left: 1px solid #e1e1e1;  
        width:500px;
        height:170px;
        background-color:#fff;
    }
    .table-b td:hover{
        border-top: 8px solid #d36b68;
        border-top: 8px solid #00a353;   
        background-color:#eef9fd;
    }
    
    .user-leave-header{
        border-bottom: 1px solid #e1e1e1;
        //height:120px;
    }
    .user-leave-foot{
        height:50px;
        line-height: 50px;
    }
    .user-leave-header .fl{
        width: 115px;
    }
    .user-leave-header .u-type{

    }
    .user-leave-header .u-type div{
        line-height: 90px;
        width: 90px;
        height: 90px;
        margin: 10px auto;
        border-radius: 50%;
        border: 1px solid #D37470;
        color: #D37470;
        font-size: 26px;
    }
    .user-leave-header .u-count{
        margin: 10px 0;
    }
    .user-leave-header .u-count div{
        //height: 30px;
        line-height: 30px;
    }
</style>
<?php $type = OTItem::getOType();?>
<div id="user-leave-info">
    <h5>个人当前加班统计（2013.09.01 - 2014.09.30）</h5>
    <table class="table-b">
        <tbody>
            <tr>
                <td>
                    <div class="user-leave-header">
                        <div class="fl u-type" style="width:150px;">
                            <div class="tac"><?php echo Yii::t('user', '加班');?></div>
                        </div>
                        <div class="fl u-count" style="width:290px;">
                            <div>【总共加班】：<?php echo isset($data['useOTItem']['sum']['agree']['hours']) ? UserLeave::getLeaveFormat($data['useOTItem']['sum']['agree']['hours']) : '0天';?></div>
                            <hr>
                            <div>
                                <ul>
                                    <?php foreach ($type as $key=>$val):?>
                                    <li class="fl" style="width:130px;"><?php echo $val; ?>：<?php echo isset($data['useOTItem']['sub'][$key]['agree']['hours']) ? UserLeave::getLeaveFormat($data['useOTItem']['sub'][$key]['agree']['hours']) : '0天'?></li>
                                    <?php endforeach;?>
                                    <li class="c"></li>
                                </ul>
                            </div>
                            <div>【总共待审】：<?php echo isset($data['useOTItem']['sum']['waiting']['hours']) ?  UserLeave::getLeaveFormat($data['useOTItem']['sum']['waiting']['hours']) : '0天';?></div>
                            <hr>
                            <div>
                                <ul>
                                    <?php foreach ($type as $key=>$val):?>
                                    <li class="fl" style="width:130px;"><?php echo $val; ?>：<?php echo isset($data['useOTItem']['sub'][$key]['waiting']['hours']) ? UserLeave::getLeaveFormat($data['useOTItem']['sub'][$key]['waiting']['hours']) : '0天'?></li>
                                    <?php endforeach;?>
                                </ul>
                            </div>
                        </div>
                        <div class="c"></div>
                    </div>
                    <div class="user-leave-foot tac">
                        <a class="btn btn_submit J_dialog" href="<?php echo $this->createUrl('/user/hr/index',array('t'=>'ot'));?>" title="<?php echo $val;?>申请">申请</a>
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
<br>
<div class="h_a">待审核</div>
<?php
$this->widget('ext.ivyCGridView.IvyCGridView', array(
	'id'=>'checking_for_me',
	'dataProvider'=>$data['dataProvider'],
    'htmlOptions'=>array(
        'class'=>'table_full',
    ),
	'template'=>"<div class='user_group' style='border:none;'>{items}</div><div class='table_full'>{summary}</div><div class='table_full'>{pager}</div>",
    'colgroups'=>array(
        array(
            "colwidth"=>array(150,90,90, 120, 90,150),
        )
    ),
	'columns'=>array(
		'type'=>array(
			'name'=>'type',
            'value'=>'OTItem::getOType($data->type)',
        ),
		'startdate'=>array(
            'name'=>'startdate',
            'value'=>'OA::formatDateTime($data->startdate)',
        ),
        'enddate'=>array(
            'name'=>'startdate',
            'value'=>'OA::formatDateTime($data->enddate)',
        ),
		'hours'=>array(
            'name'=>'hours',
            'value'=>'UserLeave::getLeaveFormat($data->hours)',
        ),
		'created'=>array(
            'name'=>'created',
            'value'=>'OA::formatDateTime($data->created)',
        ),
        'memo'=>array(
            'name'=>'memo',
            'value'=>'$data->memo',
        ),
        array(
            'class'=>'CButtonColumn',
            'template' => '{delete}',
            'deleteButtonUrl'=>'Yii::app()->createUrl("/user/hr/delete", array("id" =>$data->id,"t"=>ot))',
        ),
	)
)); ?>
<div class="h_a">加班历史</div>
<?php
$this->widget('ext.ivyCGridView.IvyCGridView', array(
	'id'=>'checked_for_me',
	'dataProvider'=>$data['dataProvidered'],
    'htmlOptions'=>array(
        'class'=>'table_full',
    ),
	'template'=>"<div class='user_group' style='border:none;'>{items}</div><div class='table_list'>{summary}</div><div class='table_list'>{pager}</div>",
    'colgroups'=>array(
        array(
            "colwidth"=>array(150,90,90, 120, 90,150),
        )
    ),
	'columns'=>array(
		'type'=>array(
			'name'=>'type',
            'value'=>'OTItem::getOType($data->type)',
        ),
		'startdate'=>array(
            'name'=>'startdate',
            'value'=>'OA::formatDateTime($data->startdate)',
        ),
        'enddate'=>array(
            'name'=>'startdate',
            'value'=>'OA::formatDateTime($data->enddate)',
        ),
		'hours'=>array(
            'name'=>'hours',
            'value'=>'UserLeave::getLeaveFormat($data->hours)',
        ),
		'created'=>array(
            'name'=>'created',
            'value'=>'OA::formatDateTime($data->created)',
        ),
        'memo'=>array(
            'name'=>'memo',
            'value'=>'$data->memo',
        )
	)
)); ?>
</div>