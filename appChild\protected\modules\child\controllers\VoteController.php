<?php

class VoteController extends ProtectedController {

    public function init() {
        parent::init();
        Mims::<PERSON><PERSON><PERSON><PERSON><PERSON>('HtoolKits');
    }

    /*
     * @see CController::actions()
     */

    public function actions() {
        return array(
            'voting' => 'ext.vote.actions.votingAction',
        );
    }

    public function actionIndex($id = 0) {
        Mims::<PERSON><PERSON><PERSON><PERSON><PERSON>('HtoolKits');
        Yii::import('common.models.vote.WVoteBasicInfo');
        $vote_info = WVoteBasicInfo::model()->findByPk($id);
        $vote_title = "";
        if (!empty($vote_info)) {
            $vote_title = HtoolKits::getContentByLang($vote_info->title_cn, $vote_info->title_en);
        }
        $this->render('index', array('voteId' => $id, 'childId' => $this->childid, 'vote_title' => $vote_title));
    }

}