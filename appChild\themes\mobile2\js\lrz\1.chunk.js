webpackJsonp([1],{6:function(t,e,a){var r,n;!function(){function a(t){var e=t.naturalWidth,a=t.naturalHeight;if(e*a>1048576){var r=document.createElement("canvas");r.width=r.height=1;var n=r.getContext("2d");return n.drawImage(t,-e+1,0),0===n.getImageData(0,0,1,1).data[3]}return!1}function i(t,e,a){var r=document.createElement("canvas");r.width=1,r.height=a;var n=r.getContext("2d");n.drawImage(t,0,0);for(var i=n.getImageData(0,0,1,a).data,o=0,c=a,s=a;s>o;){var h=i[4*(s-1)+3];0===h?c=s:o=s,s=c+o>>1}var d=s/a;return 0===d?1:d}function o(t,e,a){var r=document.createElement("canvas");return c(t,r,e,a),r.toDataURL("image/jpeg",e.quality||.8)}function c(t,e,r,n){var o=t.naturalWidth,c=t.naturalHeight,h=r.width,d=r.height,g=e.getContext("2d");g.save(),s(e,g,h,d,r.orientation);var l=a(t);l&&(o/=2,c/=2);var u=1024,w=document.createElement("canvas");w.width=w.height=u;for(var f=w.getContext("2d"),v=n?i(t,o,c):1,m=Math.ceil(u*h/o),b=Math.ceil(u*d/c/v),L=0,I=0;c>L;){for(var p=0,R=0;o>p;)f.clearRect(0,0,u,u),f.drawImage(t,-p,-L),g.drawImage(w,0,0,u,u,R,I,m,b),p+=u,R+=m;L+=u,I+=b}g.restore(),w=f=null}function s(t,e,a,r,n){switch(n){case 5:case 6:case 7:case 8:t.width=r,t.height=a;break;default:t.width=a,t.height=r}switch(n){case 2:e.translate(a,0),e.scale(-1,1);break;case 3:e.translate(a,r),e.rotate(Math.PI);break;case 4:e.translate(0,r),e.scale(1,-1);break;case 5:e.rotate(.5*Math.PI),e.scale(1,-1);break;case 6:e.rotate(.5*Math.PI),e.translate(0,-r);break;case 7:e.rotate(.5*Math.PI),e.translate(a,-r),e.scale(-1,1);break;case 8:e.rotate(-.5*Math.PI),e.translate(-a,0)}}function h(t){if(window.Blob&&t instanceof Blob){var e=new Image,a=window.URL&&window.URL.createObjectURL?window.URL:window.webkitURL&&window.webkitURL.createObjectURL?window.webkitURL:null;if(!a)throw Error("No createObjectURL function found to create blob url");e.src=a.createObjectURL(t),this.blob=t,t=e}if(!t.naturalWidth&&!t.naturalHeight){var r=this;t.onload=function(){var t=r.imageLoadListeners;if(t){r.imageLoadListeners=null;for(var e=0,a=t.length;a>e;e++)t[e]()}},this.imageLoadListeners=[]}this.srcImage=t}h.prototype.render=function(t,e,a){if(this.imageLoadListeners){var r=this;return void this.imageLoadListeners.push(function(){r.render(t,e,a)})}e=e||{};var n=this.srcImage,i=n.src,s=i.length,h=n.naturalWidth,d=n.naturalHeight,g=e.width,l=e.height,u=e.maxWidth,w=e.maxHeight,f=this.blob&&"image/jpeg"===this.blob.type||0===i.indexOf("data:image/jpeg")||i.indexOf(".jpg")===s-4||i.indexOf(".jpeg")===s-5;g&&!l?l=d*g/h<<0:l&&!g?g=h*l/d<<0:(g=h,l=d),u&&g>u&&(g=u,l=d*g/h<<0),w&&l>w&&(l=w,g=h*l/d<<0);var v={width:g,height:l};for(var m in e)v[m]=e[m];var b=t.tagName.toLowerCase();"img"===b?t.src=o(this.srcImage,v,f):"canvas"===b&&c(this.srcImage,t,v,f),"function"==typeof this.onrender&&this.onrender(t),a&&a()},r=[],n=function(){return h}.apply(e,r),!(void 0!==n&&(t.exports=n))}()}});
//# sourceMappingURL=1.chunk.js.map