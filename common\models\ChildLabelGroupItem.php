<?php

/**
 * This is the model class for table "ivy_child_label_group_item".
 *
 * The followings are the available columns in table 'ivy_child_label_group_item':
 * @property integer id
 * @property integer group_id
 * @property string school_id
 * @property integer flag
 * @property string name
 * @property string desc
 * @property integer hide_desc
 * @property bool editable_by_staff
 * @property string color
 * @property string teacher_ids
 * @property integer created_by
 * @property integer updated_by
 * @property integer created_at
 * @property integer updated_at
 */
class ChildLabelGroupItem extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ChildClassLink the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_child_label_group_item';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(

		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'group_id' => 'Group Id',
			'school_id' => 'School Id',
			'flag' => 'Flag',
			'name' => 'Name',
			'editable_by_staff' => 'Editable By Staff',
			'desc' => 'Desc',
			'color' => 'Color',
			'teacher_ids' => 'Teacher Ids',
			'created_by' => 'Created By',
			'updated_by' => 'Updated By',
			'created_at' => 'Created At',
			'updated_at' => 'Updated At',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('group_id',$this->group_id);
		$criteria->compare('school_id',$this->school_id);
		$criteria->compare('flag',$this->flag);
		$criteria->compare('name',$this->name);
		$criteria->compare('desc',$this->desc);
		$criteria->compare('hide_desc',$this->hide_desc);
		$criteria->compare('editable_by_staff',$this->editable_by_staff);
		$criteria->compare('color',$this->color);
		$criteria->compare('teacher_ids',$this->teacher_ids);
		$criteria->compare('created_by',$this->created_by);
		$criteria->compare('updated_by',$this->updated_by);
		$criteria->compare('created_at',$this->created_at);
		$criteria->compare('updated_at',$this->updated_at);
		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}