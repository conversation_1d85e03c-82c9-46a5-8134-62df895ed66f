<?php

class DefaultController extends ProtectedController
{
    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'main';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Ivy Family');
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/mark/mark.min.js');
    }

    /*
     * 校园列表
     */
    public function actionCampus(){
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
        $this->render('campus');
    }

    /*
     * 员工名录
     */
    public function actionIvyers(){
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');

        $initBranchId = Yii::app()->request->getParam('branchId', '');
        $initBranchId =  empty($initBranchId) ? $this->staff->profile->branch : $initBranchId;

        $this->render('ivyers', array('initBranchId'=>$initBranchId));
    }

    /*
     * 员工名录，切换校园时的请求
     */
    public function actionGetStaff(){
        $branchId =  Yii::app()->request->getParam('branchid');
        $userData = $this->getStaffByBranch($branchId);
        echo CJSON::encode($userData);
    }

    /**
     * 匹配2020.8新架构一人存在多个组织
     */
    public function staffMap($branchId)
    {
        $map = array(
            'BJ_TYG' => array(
                8032988 => 472,
                8017111 => 489,
                8020511 => 490,
//                8019827 => 92,
//                8039375 => 563,
            ),
            'BJ_BU' => array(
                // k-12业务
//                8027099 => 386,
//                8026998 => 387,
//                8024244 => 389,
//                8020769 => 390,
//                8030633 => 393,
                8027100 => 340,
//                8018600 => 245,
//                8001508 => 495,
                8036662 => 281,
                8020811 => 588,
                8037496 => 509,
                8041641 => 592,
                8042712 => 607,
            ),
            
            'BJ_SS' => array(
                8020811 => 434,
                // 关键服务
                // 8027099 => 398,
//                8020769 => 403,
//                8023007 => 433,
                // 教学
//                8032446 => 404,
                // 8027099 => 405,
//                8030646 => 415,
//                8028384 => 416,
//                8030642 => 417,
//                8025193 => 419,
//                8020989 => 424,
//                8017616 => 407,
//                8014971 => 409,
//                8017510 => 410,
//                8017870 => 412,
//                8030645 => 414,
//                8030643 => 418,
            ),
            'BJ_SLT' => array(
//                8020811 => 165,
//                8034854 => 337,
            ),
            'BJ_DS' => array(
//                8023007 => 156,
//                8037343 => 510
//                8033421 => 499
            ),
            'BJ_QFF' => array(
//                8001618 => 514,
            ),
            'BJ_OE' => array(
//                8003231 => 208
            ),
            'NB_FJ' => array(
                8014504 => 54
            )
        );

        return isset($map[$branchId]) ? $map[$branchId] : array();
    }

    /*
     * 按校园查询所有员工
     */
    public function getStaffByBranch($branchId){
        Yii::import('common.components.general.CommonContentFetcher');
        Yii::import('common.models.classTeacher.InfopubStaffExtend');
        $staffMap = $this->staffMap($branchId);
        $userData = CommonContentFetcher::getStaffByBranch($branchId, true, false, false, $staffMap);
        foreach ($userData as $k => $v) {
            if ($v['photo'] == 'blank.gif' && $v['pubPhoto'] != 'blank.jpg') {
                $userData[$k]['photo'] = '/infopub/staff/' . $v['pubPhoto'];
                $userData[$k]['photo_thumb'] = '/infopub/staff/' . $v['pubPhoto'];
            } else {
                $userData[$k]['photo'] = '/users/' . $v['photo'];
                $userData[$k]['photo_thumb'] = '/users/thumbs/' . $v['photo'];
            }
        }

        return $userData;
    }

    /*
     * 获取用户弹窗信息
     */
    public function actionStaffInfo(){
        $staffId = Yii::app()->request->getPost('staffid', 0);

        if( Yii::app()->request->isPostRequest && $staffId ){
            Yii::import('common.models.classTeacher.*');
            $infoModel = InfopubStaffExtend::model()->findByPk($staffId);
            $infoData = array();
            if($infoModel){
                $infoData = $infoModel->getAttributes(array(
                    'userid', 'intro_cn', 'intro_en', 'staff_photo'
                ));

            }

            $sql = "select count(*) as count from ivy_docs_center_read_task where user_id=".$staffId." and status=0";
            $count = Yii::app()->db->createCommand($sql)->queryRow();
            $infoData['readtask'] = $count['count'];

            echo CJSON::encode(array(
                'state'=>'success',
                'data' => array('info'=>$infoData)
            ));
        }
    }

    /*
     * 员工详细信息页面
     */
    public function actionStaff(){
        $id = intval( Yii::app()->request->getParam('id',0) );
        Yii::import('common.models.classTeacher.*');
        Yii::import('common.models.staff.*');
        Yii::import('common.models.hr.*');

        $staff = User::model()->with(array('profile', 'staff', 'staffInfo', 'staffApprover'))->findByPk($id);

        if($staff->isstaff == 1){
            $crit = new CDbCriteria();
            $crit->index = 'branchid';
            $branches = Branch::model()->findAll($crit);
            $approver = null;

            if($staff->isstaff && $staff->level){

                if( $staff->staffApprover ){
                    if( Yii::app()->user->id != $staff->staffApprover->approver ){
                        $approver = User::model()->with(array('profile'))->findByPk($staff->staffApprover->approver);
                    }else{
                        $approver = $this->staff;
                    }
                }

                $crit = new CDbCriteria();
                $crit->compare('uid',$id);
                $crit->compare('type','<>adm_school');
                $branchAdms = AdmBranchLink::model()->findAll($crit);
                foreach($branchAdms as $adm){
                    $branchData[$adm->type][] = $branches[$adm->schoolid]->title;
                }
            }

            $this->render('staff', array(
                'staff' => $staff,
                'branches' => $branches,
                'branchData' => $branchData,
                'approver' => $approver
            ));
        }
        else{
            echo '非法ID！';
        }
    }

    public function actionWechatMsg()
    {
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/vue2.js');
        $this->render('wechatMsg');
    }

    public function actionWechatMsgData($type = '')
    {
        $data = CommonUtils::requestDsOnline('wechat/statistics/'.$type, array(), 'get');
        $this->addMessage('state', 'success');
        $this->addMessage('message', '');
        $this->addMessage('data', $data['data']);
        $this->showMessage();
    }

    public function actionWechatMsgListData($type = '')
    {
        $openid = Yii::app()->request->getParam('openid',0);
        if(empty($openid)){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'openid error');
            $this->showMessage();
        }
        $data = CommonUtils::requestDsOnline('wechat/messageList/'.$type, array('openid'=>$openid));
        $this->addMessage('state', 'success');
        $this->addMessage('message', '');
        $this->addMessage('data', $data['data']);
        $this->showMessage();
    }
}
