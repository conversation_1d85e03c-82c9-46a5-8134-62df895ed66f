<style>
    body{
        display:block;
        margin:0;
        padding:0;
    }
    table{
        border: 0 !important;
        margin: 0;
        padding: 0;
    }
    .borderBto {
        border-top: 1px solid #ddd;
        padding: 2px 0;
    }
</style>

<div>
    <h3><?php echo $title; ?></h3>
    <?php
    if($dataArr){
    foreach ($dataArr as $mon=>$item){ ?>
        <h4><?php echo $mon ?></h4>
        <table class='table table-bordered'>
            <tr>
                <td><?php echo Yii::t('attends','Sun'); ?></td>
                <td><?php echo Yii::t('attends','Mon'); ?></td>
                <td><?php echo Yii::t('attends','Tue'); ?></td>
                <td><?php echo Yii::t('attends','Wed'); ?></td>
                <td><?php echo Yii::t('attends','Thu'); ?></td>
                <td><?php echo Yii::t('attends','Fri'); ?></td>
                <td><?php echo Yii::t('attends','Sat'); ?></td>
            </tr>
                <?php foreach ($item as $val) {?>
                    <tr>
                        <?php foreach ($val as $v) { ?>
                            <td>
                                <?php
                                $dayTime = ($v > 0) ? date("m-d", strtotime($v)) : "";
                                echo '<p class="text-right">'.$dayTime.'</p>';

                                if($events && $events[$v]){ ?>
                                    <?php foreach ($events[$v] as $times){
                                        $title = ($times['title']) ? ' ' . $times['title']: "" ;
                                        echo '<div class="borderBto">'.$times['start'] . '-' . $times['end'] . $title .'</div>';
                                    } ?>
                                <?php }?>
                            </td>
                        <?php } ?>
                    </tr>
                <?php } ?>
        </table>
        <div style="PAGE-BREAK-AFTER:always;"></div>
    <?php }
    }else{ ?>
        <div class="alert alert-danger" role="alert">
            <span class="glyphicon glyphicon-exclamation-sign" aria-hidden="true"></span>
            <span class="sr-only">Error:</span>
            暂无安排数据
        </div>
    <?php } ?>
</div>
