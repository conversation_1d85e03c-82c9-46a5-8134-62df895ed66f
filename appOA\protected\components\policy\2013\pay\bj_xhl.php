<?php
if(!defined("IVY_POLICY"))
throw new CException('DO NOT USE THIS FILE DIRECTLY');

return array( 'policy'=>array(

	//是否老生老办法
	ENABLE_CONSTANT_TUITION => true,
	
	//年保育费
	//ANNUAL_CHILDCARE_AMOUNT => 8000,
	
	//是否开放课外活动账单
	ENABLE_AFTERSCHOOLS => false,
	
	//图书馆工本费
	FEETYPE_LIBCARD => 10,
		
	//学期四个时间点；取前后两端
	TIME_POINTS   => array(201309,201312,201401,201408),

	//寒暑假月份，及其每月计费天数
	HOLIDAY_MONTH => array(
        MONTH=>array(201402=>20,201408=>20),
        ITEMS => array(
            FEETYPE_TUITION,
            FEETYPE_LUNCH,
            FEETYPE_SCHOOLBUS,
		),
        TUITION_CALC_BASE => BY_MONTH,
    ),
	
	//年付开通哪些缴费类型
	/*
	PAYGROUP_ANNUAL => array(
		FEETYPE_TUITION,
		FEETYPE_LUNCH,
		FEETYPE_SCHOOLBUS,
	),
	*/
		
	//学期付开通哪些缴费类型
    /*
	PAYGROUP_SEMESTER => array(
		ITEMS => array(
			FEETYPE_TUITION,
			FEETYPE_LUNCH,
			FEETYPE_SCHOOLBUS,		
		),
		TUITION_CALC_BASE => BY_MONTH,
	),
     * 
     */
	
	//月份开通哪些缴费类型
	PAYGROUP_MONTH => array(
		ITEMS => array(
			FEETYPE_TUITION,
			FEETYPE_LUNCH,
			FEETYPE_SCHOOLBUS,		
		),
		TUITION_CALC_BASE => BY_MONTH,
	),
	
	//计算基准：BY_MONTH, 或者 BY_SETTING, 前者表示所有的学费都基于月费用计算，后者表示所有的学费都基于学期或学年
	TUITION_CALCULATION_BASE => array(
	
		BY_MONTH => array(
			
			//月收费金额
			AMOUNT => array(
				FULL5D => 4200,
			),
			
			//收费月份，及其权重
			MONTH_FACTOR => array(
				201309=>1,
				201310=>1,
				201311=>1,
				201312=>1,
				201401=>1,
				201403=>1,
				201404=>1,
				201405=>1,
				201406=>1,
				201407=>1
			),
			
			//折扣
			/*
			GLOBAL_DISCOUNT => array(
				DISCOUNT_ANNUAL => '0.97:10:2013-12-01'
			)
			*/
		),
		
		//东湖配置实例
		/*
		BY_ANNUAL => array(
			AMOUNT => array(
				FULL5D => 146030,
				HALF2D => 52570,
				HALF3D => 75700,
				HALF5D => 105140,
			),		
		),
		
		BY_SEMESTER1 => array(
			AMOUNT => array(
				FULL5D => 60220,
				HALF2D => 21680,
				HALF3D => 31220,
				HALF5D => 43360,
			),		
		),
		
		BY_SEMESTER2 => array(
			AMOUNT => array(
				FULL5D => 90330,
				HALF2D => 32520,
				HALF3D => 46830,
				HALF5D => 65040,
			),		
		),
		*/
	),
	
	//每餐费用
	LUNCH_PERDAY => 25,
	
	//账单到期日和账单开始日之差
	DUEDAYS_OFFSET => 0,
    
    //代金卷金额
//    VOUCHER_AMOUNT => 500,

));

?>