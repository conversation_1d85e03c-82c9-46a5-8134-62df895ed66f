<?php
if(!defined("IVY_POLICY"))
throw new CException('DO NOT USE THIS FILE DIRECTLY');

return array( 'policy'=>array(

	//是否老生老办法
	ENABLE_CONSTANT_TUITION => true,

	//年保育费
	//ANNUAL_CHILDCARE_AMOUNT => 8000,

	//是否开放课外活动账单
	ENABLE_AFTERSCHOOLS => true,

	//图书馆工本费
	FEETYPE_LIBCARD => 10,

	//学期四个时间点；取前后两端
	TIME_POINTS   => array(201909,202002,202003,202008),

	//寒暑假月份，及其每月计费天数
	HOLIDAY_MONTH => array(
		MONTH=>array(202001=>0, 202008=>0),
		ITEMS => array(
			FEETYPE_TUITION,
			FEETYPE_LUNCH,
			FEETYPE_SCHOOLBUS,
		),
		TUITION_CALC_BASE => BY_MONTH,
	),

	//年付开通哪些缴费类型
//	PAYGROUP_ANNUAL => array(
//		ITEMS => array(
//			FEETYPE_TUITION,
//			FEETYPE_LUNCH,
//			FEETYPE_SCHOOLBUS,
//		),
//        EXCEPTS => array(
//            OTHER_PROGRAM_CHN
//        ),
//		TUITION_CALC_BASE => BY_MONTH,
//	),
//
	//学期付开通哪些缴费类型
	PAYGROUP_SEMESTER => array(
		ITEMS => array(
			FEETYPE_TUITION,
			FEETYPE_LUNCH,
			FEETYPE_SCHOOLBUS,
		),
		TUITION_CALC_BASE => BY_MONTH,
	),
////
	//月份开通哪些缴费类型
	PAYGROUP_MONTH => array(
		ITEMS => array(
			// FEETYPE_TUITION,
			FEETYPE_LUNCH,
			// FEETYPE_SCHOOLBUS,
		),
		TUITION_CALC_BASE => BY_MONTH,
	),

	//计算基准：BY_MONTH, 或者 BY_SETTING, 前者表示所有的学费都基于月费用计算，后者表示所有的学费都基于学期或学年
	TUITION_CALCULATION_BASE => array(

		BY_MONTH => array(

			//月收费金额
			AMOUNT => array(
				FULL5D => 4300,
                PROGRAM_MIK => 2500,
			),

			//收费月份，及其权重
			MONTH_FACTOR => array(
				201909=>1,
				201910=>1,
				201911=>1,
				201912=>1,
				202001=>0,
				202002=>1,
				202003=>1,
				202004=>1,
				202005=>1,
				202006=>1,
				202007=>1
			),

		),

		BY_SEMESTER1 => array(
			ENDDATE => 1582905600 //2020-2-29
		),

		BY_SEMESTER2 => array(
			ENDDATE => 1596124800 //2020-7-31
		),

	),

	//每餐费用
	LUNCH_PERDAY => array(
        LUNCHA => 32,
        LUNCHB => 32,
    ),

	//账单到期日和账单开始日之差
	DUEDAYS_OFFSET => 1,

    //代金卷金额
    //VOUCHER_AMOUNT => 500,

));