<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
    "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="language" content="en"/>
    <!--<title></title>-->
    <style>
        * {
            margin: 0;
            padding: 0
        }

        body {
            font-family: Utopia, "Trebuchet MS", <PERSON><PERSON>, "Microsoft Yahei", "微软雅黑", <PERSON><PERSON><PERSON><PERSON>, "华文细黑", sans-serif;
            font-size: 10px;
            line-height: 1.42857143;
            color: #333333;
            margin: 0;
            padding: 0;
            background-color: #efefef;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        body * {
            box-sizing: border-box;
            -moz-box-sizing: border-box;
            -webkit-box-sizing: border-box;
        }

        .page-break-after {
            page-break-after: always;
        }

        .navbar-fixed-top {
            top: 0;
            position: fixed;
            right: 0;
            left: 0;
            z-index: 1030;
            -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
        }

        .navbar-inverse {
            background-color: #333333;
            border-color: #efefef;
        }

        .navbar-inverse form {
            line-height: 50px;
            height: 50px;
            text-align: center;
        }

        .navbar-inverse form input {
            margin-top: 13px;
        }

        .paddinglr6 {
            padding: 0 6px;
        }

        .text-right {
            text-align: right;
        }

        .text-center {
            text-align: center;
        }

        .text-left {
            text-align: left;
        }

        .clear {
            clear: both;
        }

        .fl {
            float: left;
        }

        .fr {
            float: right;
        }

        .report-page {
            min-height: 877px;
            position: relative;
            background-color: #fff;
        }

        .dshr {
            width: 100%;
            border-bottom: 1px solid #92a2af;
        }

        .high_title {
            width: 440px;
            border-top: 2px solid #4f91ce;
        }

        .line_height18 {
            line-height: 18px;
        }

        .text_blue {
            color: #71aeca
        }

        .text_white {
            color: white;
        }

        .report-wrapper {
            width: 620px;
            margin: 0 auto;
            padding: 0;
        }

        .pull-content {
            padding: 40px 30px 0;
        }

        .title_header {
            color: #626e60;
            line-height: 24px;
            margin: 0;
            text-align: right
        }

        .left_title {
            line-height: 35px;
            font-weight: bold;
        }

        .bg_blue {
            background: #2374b3;
        }

        .bg_gray {
            background: #dfe9f5;
        }

        .bg_yellow {
            background: #fde69a;
        }

        .bg_gold {
            background: #fcc10f;
        }
        .logo-top-center{
            width: 110px;
            height: 120px;
            background: url('https://m2.files.ivykids.cn/cloud01-file-8035133FueFp-X_Mzk0IJPxInAZ_vikTFWW.png') no-repeat;
            margin-bottom : 16px;
            background-size: 100%;
        }
    </style>
</head>

<body>
<!--下载pdf 按键-->
<!--<div class="navbar navbar-fixed-top navbar-inverse" role="navigation">-->
<!--    <form action="-" method="post"><input type="hidden" value="BJ_DS" name="schoolid" id="schoolid">-->
<!--        <input type="hidden" value="10864" name="childid" id="childid">-->
<!--        <input type="hidden" value="691" name="classid" id="classid"><input type="hidden" value="0" name="id" id="id">-->
<!--        <input style="font-family: Microsoft Yahei" type="submit" name="yt0" value="Download PDF / 下载 PDF">-->
<!--    </form>-->
<!--</div>-->
<?php

$MYP = array(
    Yii::t('principal','Self-Manage. Skills'),
    Yii::t('principal','Research Skills'),
    Yii::t('principal','Social Skills'),
    Yii::t('principal','Thinking Skills'),
    Yii::t('principal','Communication Skills'),
);

?>
<div class="report-wrapper web-view">
    <!--page one-->
    <div id="page-1" class="report-page page-break-after">
        <div class="pull-content">

           <div class="fl logo-top-center"></div>

            <div class="high_title fr">
                <h5 class="title_header"><?php echo Yii::t('principal','Student Achievement Report') ?></h5>
                <h5 class="title_header"><?php echo $report->getTitle() ?></h5>

                <?php
                    foreach($reportchild as $chidNameclass){
                        if($chidNameclass->id == $courseid){
                            echo "<p class='text-right' style='text-indent: 16px'>" . Yii::t('principal','Student Name:') . "<span>" . $chidNameclass->childName->getChildName() ."</span></p>";
                            echo "<p class='text-indent' style='text-indent: 18px'>" . Yii::t('principal','Grade Level:') . "<span>" . $chidNameclass->childClass->title ."</span></p>";
                        }
                    }
                ?>

                <p style="text-indent: 16px" class="line_height18"><?php echo Yii::t('principal','Reporting Period:') ?><?php echo $report->cycle ?></p>

                <p style="text-indent: 16px" class="line_height18"><?php echo Yii::t('principal','Reporting Date:') ?><span
                        class="text_blue"><?php echo date("Y-m-d", $report->end_time) ?></span></p>
            </div>
            <div class="clear"></div>
            <div class="dshr" style="margin-bottom: 28px"></div>
            <h3 class="text-center" style="line-height: 34px;font-size: 14px"><?php echo Yii::t('principal','A Message from the Principals') ?>启明星宗旨</h3>
            <p class="text-center" style="font-style: italic"><?php echo Yii::t('principal','Daystar Academy develops world citizens by embracing Chinese and Western culture through its integrated education model. Daystar students strive for distinction in Chinese and English studies, creative thinking and character development for the purpose of serving the community at large.') ?>
                <br/>
                <br/>
            </p>

            <h3 class="text-center" style="line-height: 34px;font-size: 14px"><?php echo Yii::t('principal','A Message from the Principals') ?></h3>

            <p class="text-center" style="font-style: italic"><?php echo nl2br($report->getPresident()); ?><br/>
                <!--<span class="fr"><php /*echo Yii::t('principal','Sincerely,') */?></span><br/><br/>
                <em class="fr"><p /*echo $report->principalID->getName() */?></em><br/><br/>
                <span class="fr">< /*echo Yii::t('principal','MS Principal') */?></span>-->
            </p>

            <div class="clear"></div>
            <div class="left_title"><?php echo Yii::t('principal','Attendance Record') ?></div>
            <table border="1" class="text-center" style="margin-bottom: 48px">
                <tr class="bg_blue text_white" style="height: 38px">
                    <?php
                    $num = count($report_list);
                    foreach ($report_list as $k => $_report_list) {
                        echo "<th colspan='2' class='paddinglr6' width='200'>第" . ($k+1) . "个评估报告周期</th>";
                    }
                    for($num; $num<4; $num++){
                        echo "<th colspan='2' class='paddinglr6' width='200'>第" . ($num+1) . "个评估报告周期</th>";
                    }?>
                    <th colspan="2" class="bg_gold paddinglr6" width="200"><?php echo Yii::t('principal','Year-to-Date') ?></th>
                </tr>
                <tr class="bg_gray" style="height:55px">
                    <?php
                    foreach ($report_list as $k => $_report_list) {
                        echo "<td width='100'>" .  Yii::t('principal','Days in the Reporting Period') . "</td>";
                        echo "<td width='100'>" . Yii::t('principal','Days Absent') . "</td>";
                    }
                    $num = count($report_list);
                    for($num; $num<4; $num++){
                        echo "<td width='100'>" . Yii::t('principal','Days in the Reporting Period') . "</td>";
                        echo "<td width='100'>" . Yii::t('principal','Days Absent') . "</td>";
                    }?>
                    <td class="bg_yellow" width="100"><?php echo Yii::t('principal','Year-End Score') ?></td>
                    <td class="bg_yellow" width="100"><?php echo Yii::t('principal','Days Absent') ?></td>
                </tr>

                <tr style="height: 24px">
                    <?php
                    $munber = "";
                    $days = "";
                    foreach ($report_list as $k => $_report_list) {
                        foreach ($reportchild as $_reportchild) {
                            if ($_report_list->id == $_reportchild->report_id) {
                                echo "<td>" . $_reportchild->evaluate_number . "</td>";
                                echo "<td>" . $_reportchild->absent_days . "</td>";
                                $number += $_reportchild->evaluate_number;
                                $days += $_reportchild->absent_days;
                            }

                            if (count($reportchild) == 1 && $k < 1) {
                                echo "<td>" . "--" . "</td>";
                                echo "<td>" . "--" . "</td>";
                            }
                        }
                    }
                    $num = count($report_list);
                    for($num; $num<4; $num++){
                        echo "<td>" . "--" . "</td>";
                        echo "<td>" . "--" . "</td>";
                    }
                    ?>
                    <td><?php echo $number ?></td>
                    <td><?php echo $days ?></td>
                </tr>
            </table>
            <div class="dshr"></div>
            <div style="line-height: 26px"><span class="text_blue fl"><?php echo $teacherName['8018280'] ?></span><span
                    class="text_blue fr"><?php echo $teacherName['8014497'] ?></span></div>
            <div class="clear"></div>
            <div style="line-height: 26px"><span class=" fl"><?php echo Yii::t('principal','MS Principal') ?></span><span class=" fr"><?php echo Yii::t('principal','Head of School') ?></span></div>
            <div class="clear"></div>
            <br/>
            <br/>

            <div style="line-height: 26px"><span class="text_blue fl"><?php echo $teacherName['8011431'] ?></span><span class="text_blue fr"><?php echo $teacherName['5']  ?></span>
            </div>
            <div class="clear"></div>
            <div style="line-height: 26px"><span class=" fl"><?php echo Yii::t('principal','MS Vice Principal') ?></span><span class=" fr"><?php echo Yii::t('principal','MYP Coordinator') ?></span></div>
            <div class="clear"></div>
        </div>
    </div>

    <!--page2-->

    <div id="page-2" class="report-page page-break-after">
        <div class="pull-content">
            <div class="left_title"><?php echo Yii::t('principal','Quantitative Achievement Summary') ?></div>
            <table border="1" class="text-center" style="margin-bottom: 48px">
                <tr class="bg_blue text_white" style="height: 38px">
                    <th rowspan="2" class="paddinglr6" width="100"><?php echo Yii::t('principal','Course Title') ?><br/><?php echo Yii::t('principal','Teacher Name') ?></th>
                    <th rowspan="2" class="paddinglr6" width="50"><?php echo Yii::t('principal','Criteria') ?></th>
                    <th colspan="4" class="paddinglr6" width="350"><?php echo Yii::t('principal','Reporting Period') ?></th>
                    <th rowspan="2" class="paddinglr6 bg_gold" width="50"><?php echo Yii::t('principal','Reporting Period') ?></th>
                    <th rowspan="2" class="paddinglr6 bg_gold" width="50"><?php echo Yii::t('principal','Year-End Score') ?></th>
                </tr>
                <tr class="bg_gray">
                    <?php
                        foreach ($report_list as $k => $_report_list) {
                            echo "<td>" . ($k+1) . "</td>";
                        }
                        $num = count($report_list);
                        for($num; $num<4; $num++){
                            echo "<td>" . ($num+1) . "</td>";
                        }
                    ?>
                </tr>
                <?php
                foreach ($childcourse as $_childcourse) {
                    $frecyion_list = json_decode($_childcourse->childTotal()->frecyion_total, TRUE);
                    ?>
                    <tr>
                        <td rowspan="<?php echo count($_childcourse->reportCourse->courseScores)+1  ?>">
                            <?php
                                echo $_childcourse->reportCourse->getName();
                                echo "<br>";
                                echo $_childcourse->teacherName->getName();
                            ?>
                        </td>
                    </tr>
                    <?php
                    foreach ($_childcourse->reportCourse->courseScores as $k=>$val) {
                        $array = array();
                        foreach ($val->items as $_item) {
                            $array[$_item->id] = $_item->fraction;
                        }
                        ?>
                        <tr>
                            <td>
                                <?php echo $val->title_cn; ?>
                            </td>
                            <?php
                                foreach ($report_list as $_reportlist){
                                    $status = ($array[$options[$_childcourse->courseid][$val->id][$_reportlist->id]]) ? $array[$options[$_childcourse->courseid][$val->id][$_reportlist->id]] : "-";
                                    echo "<td class='bg_gray'>".  $status ."</td>";
                                }
                                $num = count($report_list);
                                for($num; $num<4; $num++){
                                    echo "<td class='bg_gray'>--</td>";
                                }
                            $numb = ($array[$frecyion_list[$val->id]]) ? $array[$frecyion_list[$val->id]] : "-";
                                echo "<td class='bg_gray'>" .  $numb . "</td>";
                                if($k < 1 ){
                                    $sta = array("1" => 0, 1, 2, 3, 4, 5, 6, 7);
                                    echo "<td rowspan='". count($_childcourse->reportCourse->courseScores) ."' class='paddinglr6 bg_gold'>" . $sta[$_childcourse->childTotal->oprion_value] . "</td>";
                                }
                            ?>

                        </tr>
                    <?php }
                }?>
            </table>
        </div>
    </div>

    <!--page3-->
    <div id="page-3" class="report-page page-break-after">

        <div class="pull-content">
            <div class="left_title"><?php echo Yii::t('principal','Qualitative Achievement Summary') ?></div>
            <table border="1" class="" style="margin-bottom: 48px">
                <tr class="bg_blue text_white text-center" style="height: 38px">
                    <th class="paddinglr6" width="100"><?php echo Yii::t('principal','Course Name') ?><br/><?php echo Yii::t('principal','Teacher Name') ?></th>
                    <th class="paddinglr6" width="200"><?php echo Yii::t('principal','Teacher Comments') ?></th>
                    <!-- <th class="paddinglr6 bg_gold" width="200"><?php echo Yii::t('principal','Student Comments') ?></th> -->
                </tr>
                <?php  foreach ($childcourse as $_childcourse) { ?>
                    <tr>
                        <td style="padding: 10px">
                            <?php
                                echo $_childcourse->reportCourse->getName();
                                echo "<br>";
                                echo $_childcourse->teacherName->getName();
                            ?>
                        </td>
                        <td class="bg_gray" style="padding: 10px">
                            <?php echo nl2br($_childcourse->teacher_message_en); ?>
                        </td>
                        <!-- <td class="bg_yellow" style="padding: 10px">
                            <?php echo nl2br($_childcourse->student_message_en); ?>
                        </td> -->
                    </tr>
                <?php } ?>
            </table>
        </div>
    </div>

    <!--page4-->
    <div id="page-4" class="report-page page-break-after">
        <div class="pull-content">
            <div class="left_title"><?php echo Yii::t('principal','MYP Approaches to Learning Student Progress Summary') ?></div>
            <table border="1" class="text-center" style="margin-bottom: 48px">
                <tr class="bg_blue text_white" style="height: 38px">
                    <th rowspan="2" class="paddinglr6" width="100"><?php echo Yii::t('principal','Course Name') ?></th>
                    <th colspan="5" class="paddinglr6 bg_gold" width="200"><?php echo Yii::t('principal','Approaches to Learning Categories') ?></th>
                </tr>
                <tr>
                    <?php foreach ($MYP as $k => $_myp) {
                        echo "<td class='bg_yellow' width='70'>" . $_myp . "</td>";
                    }?>
                </tr>

                <?php
                $MYP_value = array('E', 'P', 'L', 'N');
                foreach ($childcourse as $_childcourse) {
                    $myp_list = json_decode($_childcourse->myp);
                    ?>
                    <tr>
                        <td><?php echo $_childcourse->reportCourse->getName(); ?></td>
                        <?php foreach ($MYP as $k => $_myp) {
                            $re = ($MYP_value[$myp_list[$k]]) ? $MYP_value[$myp_list[$k]] : "-";
                            echo "<td>" . $re . "</td>";
                        }?>
                    </tr>
                <?php } ?>

            </table>
            <br/>
            <br/>
            <br/>
            <br/>

            <div class="left_title"><?php echo Yii::t('principal','Description of Symbols & Abbreviations') ?></div>
            <table border="1" class="text-center" style="margin-bottom: 48px">
                <tr class="bg_blue text_white" style="height: 38px">
                    <th class="paddinglr6" width="100"><?php echo Yii::t('principal','Abbreviation') ?></th>
                    <th class="paddinglr6 bg_gold" width="300"><?php echo Yii::t('principal','Description') ?></th>
                </tr>
                <tr>
                    <td>--</td>
                    <td>不适用于本评估报告周期</td>
                </tr>
                <tr>
                    <td>E</td>
                    <td>优秀 - 学生可以向他人展示如何运用技能以及正确的评估技能使用的有效</td>
                </tr>
                <tr>
                    <td>P</td>
                    <td>高阶 - 学生自信和有效地运用技能（展示）</td>
                </tr>
                <tr>
                    <td>L</td>
                    <td>进阶 - 学生模仿运用技能的其他人，使用技能时需要一步步的指导（效仿）</td>
                </tr>
                <tr>
                    <td>N</td>
                    <td>入门 - 向学生介绍了技能，可以观看他人执行这个技能（观察）</td>
                </tr>
            </table>


            <br/>
            <br/>
            <br/>
            <br/>
            <br/>
            <div class="left_title"><?php echo Yii::t('principal','Advisor Comments') ?></div>
            <div>
                <?php
                    foreach($reportchild as $v){
                        if($v->id = $courseid){
                            echo nl2br($v->counselor_message_en);
                        }
                    }
                ?>
            </div>
            <br/>
            <br/>
            <br/>
            <br/>
    </div>
</div>


</body>
</html>
