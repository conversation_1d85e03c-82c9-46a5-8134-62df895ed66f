<?php

class CalendarNewController extends ProtectedController
{
    public $actionAccessAuths = array(
        'Index'              => 'o_X_Access',
        'SaveCalendar'       => 'o_X_Adm_Common',
        'TemplateSaveAs'     => 'o_X_Adm_Common',
        'SaveCalendarDays'   => 'o_X_Adm_Common',
        //        'GetCalendarDays'   => 'o_X_Access',
        'DelCalendar'        => 'o_X_Adm_Common',
        'DelDay'             => 'o_X_Adm_Common',
        'AssignCalendar'     => 'o_X_Adm_Common',
        'UnassignCalendar'   => 'o_X_Adm_Common',
        'SetCurrentCalendar' => 'o_X_Adm_Common',
    );

    public function createUrlReg($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
//            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function beforeAction($action)
    {
        parent::beforeAction($action);
        return true;
    }

    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'hqOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'HQ Operations');
        $cs = Yii::app()->clientScript;
        // $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/v-calendar/vue.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/element/vue.global.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl.'/base/js/element/index.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/element/index.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/v-calendar/v-calendar.umd.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/plupload/plupload.full.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/qiniu.min.js');

        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile($cs->getCoreScriptUrl().'/jui/js/jquery-ui-i18n.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/phpfunc.js');
        $cs->registerCssFile(Yii::app()->theme->baseUrl.'/css/calendar.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/xlsx.full.min.js');
    }


    public function actionIndex()
    {
        $this->render('index');
    }

    public function actionGetHomePageDataByHQ()
    {
        $res = CommonUtils::requestDsOnline('calendar/getHomePageDataByHQ');
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }


    public function actionCreateNewCalendar()
    {
        $school_year = Yii::app()->request->getParam("school_year");
        $title = Yii::app()->request->getParam("title");
        $first_semester1 = Yii::app()->request->getParam("first_semester1");
        $first_semester2 = Yii::app()->request->getParam("first_semester2");
        $second_semester1 = Yii::app()->request->getParam("second_semester1");
        $second_semester2 = Yii::app()->request->getParam("second_semester2");
        $remark_cn = Yii::app()->request->getParam("remark_cn");
        $remark_en = Yii::app()->request->getParam("remark_en");
        $stat = Yii::app()->request->getParam("stat");

        $res = CommonUtils::requestDsOnline('calendar/createNewCalendar', array(
            'school_year'      => $school_year,
            'title'            => $title,
            'first_semester1'  => $first_semester1,
            'first_semester2'  => $first_semester2,
            'second_semester1' => $second_semester1,
            'second_semester2' => $second_semester2,
            'remark_cn'        => $remark_cn,
            'remark_en'        => $remark_en,
            'stat'             => $stat,#家长是否能查看校历10可以 20不可以
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionSaveCalendarYearlyStat()
    {
        $yid = Yii::app()->request->getParam("yid");
        $res = CommonUtils::requestDsOnline('calendar/saveCalendarYearlyStat', array(
            'yid' => $yid,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }


    public function actionGetCalendarData()
    {
        $yid = Yii::app()->request->getParam("yid");
        $school_id = Yii::app()->request->getParam("branchId", 0);
        $res = CommonUtils::requestDsOnline('calendar/getCalendarData', array(
            'yid'       => $yid,
            'school_id' => $school_id,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionAssignCalendarSchool()
    {
        $yid = Yii::app()->request->getParam("yid");
        $school_id = Yii::app()->request->getParam("school_id", 0);
        $res = CommonUtils::requestDsOnline('calendar/assignCalendarSchool', array(
            'yid'       => $yid,
            'school_id' => $school_id,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionRemoveCalendarSchool()
    {
        $yid = Yii::app()->request->getParam("yid");
        $school_id = Yii::app()->request->getParam("school_id", 0);
        $res = CommonUtils::requestDsOnline('calendar/removeCalendarSchool', array(
            'yid'       => $yid,
            'school_id' => $school_id,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionSaveCalendarSchoolSelect()
    {
        $yid = Yii::app()->request->getParam("yid");
        $school_id = Yii::app()->request->getParam("school_id", 0);
        $res = CommonUtils::requestDsOnline('calendar/saveCalendarSchoolSelect', array(
            'yid'       => $yid,
            'school_id' => $school_id,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionRemoveCalendar()
    {
        $yid = Yii::app()->request->getParam("yid");
        $res = CommonUtils::requestDsOnline('calendar/removeCalendar', array(
            'yid' => $yid,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionSaveCalendar()
    {
        $yid = Yii::app()->request->getParam("yid");
        $title = Yii::app()->request->getParam("title");
        $first_semester1 = Yii::app()->request->getParam("first_semester1");
        $first_semester2 = Yii::app()->request->getParam("first_semester2");
        $second_semester1 = Yii::app()->request->getParam("second_semester1");
        $second_semester2 = Yii::app()->request->getParam("second_semester2");
        $remark_cn = Yii::app()->request->getParam("remark_cn");
        $remark_en = Yii::app()->request->getParam("remark_en");
        $stat = Yii::app()->request->getParam("stat");
        $res = CommonUtils::requestDsOnline('calendar/saveCalendar', array(
            'yid'              => $yid,
            'title'            => $title,
            'first_semester1'  => $first_semester1,
            'first_semester2'  => $first_semester2,
            'second_semester1' => $second_semester1,
            'second_semester2' => $second_semester2,
            'remark_cn'        => $remark_cn,
            'remark_en'        => $remark_en,
            'stat'             => $stat,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionCreateCalendarDay()
    {
        $yid = Yii::app()->request->getParam("yid");
        $date_list = Yii::app()->request->getParam("date_list");
        $day_type = Yii::app()->request->getParam("day_type");
        $event = Yii::app()->request->getParam("event");
        $title_cn = Yii::app()->request->getParam("title_cn");
        $title_en = Yii::app()->request->getParam("title_en");
        $memo_cn = Yii::app()->request->getParam("memo_cn");
        $memo_en = Yii::app()->request->getParam("memo_en");
        $privacy = Yii::app()->request->getParam("privacy");
        $school_id = Yii::app()->request->getParam("branchId");
        $event_type = Yii::app()->request->getParam("event_type");
        $res = CommonUtils::requestDsOnline('calendar/createCalendarDay', array(
            'yid'        => $yid,
            'date_list'  => $date_list,
            'day_type'   => $day_type,
            'event'      => $event,
            'title_cn'   => $title_cn,
            'title_en'   => $title_en,
            'memo_cn'    => $memo_cn,
            'memo_en'    => $memo_en,
            'privacy'    => $privacy,
            'school_id'  => $school_id,
            'event_type' => $event_type,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionDelCalendarDay()
    {
        $yid = Yii::app()->request->getParam("yid");
        $bindend_id = Yii::app()->request->getParam("bindend_id");
        $res = CommonUtils::requestDsOnline('calendar/delCalendarDay', array(
            'yid'        => $yid,
            'bindend_id' => $bindend_id,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionSaveCalendarDay()
    {
        $yid = Yii::app()->request->getParam("yid");
        $title_cn = Yii::app()->request->getParam("title_cn");
        $title_en = Yii::app()->request->getParam("title_en");
        $edit_calendar_start_day = Yii::app()->request->getParam("edit_calendar_start_day");
        $edit_calendar_end_day = Yii::app()->request->getParam("edit_calendar_end_day");
        $bindend_id = Yii::app()->request->getParam("bindend_id");
        $event = Yii::app()->request->getParam("event");
        $day_type = Yii::app()->request->getParam("day_type");
        $memo_cn = Yii::app()->request->getParam("memo_cn", '');
        $memo_en = Yii::app()->request->getParam("memo_en", '');
        $privacy = Yii::app()->request->getParam("privacy", 0);
        $school_id = Yii::app()->request->getParam("school_id", 0);
        $event_type = Yii::app()->request->getParam("event_type", 0);
        $time = Yii::app()->request->getParam("time");
        $res = CommonUtils::requestDsOnline('calendar/saveCalendarDay', array(
            'yid'                     => $yid,
            'bindend_id'              => $bindend_id,
            'title_cn'                => $title_cn,
            'title_en'                => $title_en,
            'edit_calendar_start_day' => $edit_calendar_start_day,
            'edit_calendar_end_day'   => $edit_calendar_end_day,
            'event'                   => $event,
            'day_type'                => $day_type,
            'memo_cn'                 => $memo_cn,
            'memo_en'                 => $memo_en,
            'privacy'                 => $privacy,
            'school_id'               => $school_id,
            'event_type'              => $event_type,
            'time'                    => $time,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }


    public function actionGetHomePageDataBySchool()
    {
        $yid = Yii::app()->request->getParam("yid");
        $school_id = Yii::app()->request->getParam("branchId");
        $res = CommonUtils::requestDsOnline('calendar/getHomePageDataBySchool', array(
            'yid'       => $yid,
            'school_id' => $school_id,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    /**
     * 导出校历的假期 事件 调休
     */
    public function actionExportEvent()
    {
        $yid = Yii::app()->request->getParam("yid");
        $school_id = Yii::app()->request->getParam("branchId");
        $res = CommonUtils::requestDsOnline('calendar/exportEvent', array(
            'yid'       => $yid,
            'school_id' => $school_id,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    /***
     * 学校 导入校历的活动事件 前端读取数据
     */
    public function actionImportEvent2()
    {
        $yid = Yii::app()->request->getPost("yid");
        $data = Yii::app()->request->getPost("data");
        if (empty($yid)) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'parameter error');
            $this->showMessage();
        }
        foreach ($data as $item) {
            $start_day1 = trim($item['StartDateAndTime']);
            $end_day = trim($item['EndDateAndTime']);
            $event = trim($item['Event']);
            $day_type = trim($item['FullOrHalfDay']);
            $title_cn = trim($item['TitleCn']);
            $title_en = trim($item['TitleEn']);
            //开始时间
            if (empty($start_day1)) {
                $this->addMessage('message', '开始时间不能为空');
                $this->showMessage();
            }
            $start_day = strtotime(date($start_day1));
            if (!$start_day) {
                $this->addMessage('message', $start_day1.'开始时间格式不正确');
                $this->showMessage();
            }
            //结束时间
            if (empty($end_day)) {
                $this->addMessage('message', '结束时间不能为空');
                $this->showMessage();
            }
            $end_day = strtotime(date($end_day));
            if (!$end_day) {
                $this->addMessage('message', '结束时间格式不正确');
                $this->showMessage();
            }
            if (empty($event)) {
                $this->addMessage('message', '校例日类型不能为空');
                $this->showMessage();
            }
            if (empty($day_type)) {
                $this->addMessage('message', '时间类型不能为空');
                $this->showMessage();
            }
            if (!in_array(strtolower($day_type), array('full', 'half', '全天', '半天'))) {
                $this->addMessage('message', '时间类型不正确');
                $this->showMessage();
            }
            if (empty($title_cn) || empty($title_en)) {
                $this->addMessage('message', '中英文标题不能为空');
                $this->showMessage();
            }
        }
        $input_data = array();
        foreach ($data as $key => $val) {
            if (in_array(strtolower(trim($val['Event'])), array('holiday', '假期'))) {
                $event = 10;
            } elseif (in_array(strtolower(trim($val['Event'])), array('event', '事件'))) {
                $event = 20;
            } else {
                $event = 30;
            }
            if (in_array(trim($val['FullOrHalfDay']), array('full', '全天'))) {
                $day_type = 10;
            } else {
                $day_type = 20;
            }
            $privacy = trim($val['OnlyStaffSee']);
            $input_data[$key] = array(
                'date_list'  => date('Y.m.d', strtotime(trim($val['StartDateAndTime']))).'-'.date('Y.m.d', strtotime(trim($val['EndDateAndTime']))),
                #开始时间分时
                'start_time' => $this->hasTime($val['StartDateAndTime']) ? date('H:i', strtotime(trim($val['StartDateAndTime']))) : '',
                #结束时间分时
                'end_time'   => $this->hasTime($val['EndDateAndTime']) ? date('H:i', strtotime(trim($val['EndDateAndTime']))) : '',
                'yid'        => $yid,
                'day_type'   => $day_type,
                'event'      => $event,//学校只能上传事件不能传假期，调休等
                'title_cn'   => trim($val['TitleCn']),
                'title_en'   => trim($val['TitleEn']),
                'memo_cn'    => trim($val['DescriptionCn']),
                'memo_en'    => trim($val['DescriptionEn']),
                'privacy'    => !empty($privacy) && $privacy === 'Y' ? 1 : 0,
                'event_type' => trim($val['Categories']),
                'locations'  => trim($val['Location']),
                'school_id'  => 0,
                'user_id'    => Yii::app()->user->getId(),
            );
        }
        //提交导入数据
        $res = CommonUtils::requestDsOnline2('calendar/importEvent', array('data' => $input_data), 'post');
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    //判断是否输入了时分
    function hasTime($dateTimeString)
    {
        $pattern = '/^\d{1,2}\/\d{1,2}\/\d{1,2} \d{1,2}:\d{2}$/';
        return (bool) preg_match($pattern, $dateTimeString);
    }

    /***
     * 导入校历的假期 事件 调休
     */
    public function actionImportEvent()
    {
        $yid = Yii::app()->request->getParam("yid");
        if (empty($yid)) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'parameter error');
            $this->showMessage();
        }
        Yii::import('common.models.visit.*');
        $model = new EventImportForm;
        if (isset($_POST['EventImportForm'])) {
            $model->attributes = $_POST['EventImportForm'];
            if ($model->validate()) {
                $model->csv = CUploadedFile::getInstance($model, 'csv');
                if ($model->csv) {
                    $fileName = uniqid('', true).'.'.$model->csv->getExtensionName();
                    $model->csv->saveAs(Yii::app()->params['xoopsVarPath'].'/'.$fileName);
                    if (is_file(Yii::app()->params['xoopsVarPath'].'/'.$fileName)) {
                        $fileArray = file(Yii::app()->params['xoopsVarPath'].'/'.$fileName);
                        @unlink(Yii::app()->params['xoopsVarPath'].'/'.$fileName);
                        foreach ($fileArray as $key => $val) {
                            if ($key > 0) {
                                $val = mb_convert_encoding($val, 'UTF-8', 'UTF-8,GBK,GB2312,BIG5,ASCII');//iconv('GB2312', 'UTF-8', $val);
                                $val = trim(str_replace(';', ',', $val));
                                $explode_data = explode(',', $val);
                                $unique_data = array_unique(array_values($explode_data));
                                if (!empty($unique_data[0])) {
                                    $valArray[] = explode(',', $val);
                                }
                            }
                        }
                        //数据检查
                        //0 开始时间 1结束时间 2校例日类型 3时间类型all全天 Half半天 4标题中文 5标题英文 6描述中文 7描述英文 8仅员工可见
                        $this->addMessage('state', 'fail');
                        foreach ($valArray as $item) {
                            $start_day = trim($item[0]);
                            $end_day = trim($item[1]);
                            $event = trim($item[2]);
                            $day_type = trim($item[3]);
                            $title_cn = trim($item[4]);
                            $title_en = trim($item[5]);
                            //开始时间
                            if (empty($start_day)) {
                                $this->addMessage('message', '开始时间不能为空');
                                $this->showMessage();
                            }
                            $start_day = strtotime(date($start_day));
                            if (!$start_day) {
                                $this->addMessage('message', '开始时间格式不正确');
                                $this->showMessage();
                            }
                            //结束时间
                            if (empty($end_day)) {
                                $this->addMessage('message', '结束时间不能为空');
                                $this->showMessage();
                            }
                            $end_day = strtotime(date($end_day));
                            if (!$end_day) {
                                $this->addMessage('message', '结束时间格式不正确');
                                $this->showMessage();
                            }

                            if (empty($event)) {
                                $this->addMessage('message', '校例日类型不能为空');
                                $this->showMessage();
                            }
                            if (!in_array(strtolower($event), array(
                                'holiday', 'event', 'schooldaysmake-up', '假期', '事件', '周末调休'
                            ))) {
                                $this->addMessage('message', '校例日类型不正确');
                                $this->showMessage();
                            }

                            if (empty($day_type)) {
                                $this->addMessage('message', '时间类型不能为空');
                                $this->showMessage();
                            }
                            if (!in_array(strtolower($day_type), array('full', 'half', '全天', '半天'))) {
                                $this->addMessage('message', '时间类型不正确');
                                $this->showMessage();
                            }

                            if (empty($title_cn) || empty($title_en)) {
                                $this->addMessage('message', '中英文标题不能为空');
                                $this->showMessage();
                            }
                        }
                        //重新赋值数据
                        //0 开始时间 1结束时间 2校例日类型 3时间类型all全天 Half半天 4标题中文 5标题英文 6描述中文 7描述英文 8仅员工可见
                        $input_data = array();
                        foreach ($valArray as $key => $val) {
                            if (in_array(strtolower(trim($val[2])), array('holiday', '假期'))) {
                                $event = 10;
                            } elseif (in_array(strtolower(trim($val[2])), array('event', '事件'))) {
                                $event = 20;
                            } else {
                                $event = 30;
                            }

                            if (in_array($val[3], array('full', '全天'))) {
                                $day_type = 10;
                            } else {
                                $day_type = 20;
                            }
                            $privacy = trim($val[8]);
                            $input_data[$key] = array(
                                'date_list' => date('Y.m.d', strtotime(trim($val[0]))).'-'.date('Y.m.d', strtotime(trim($val[1]))),
                                'yid'       => $yid,
                                'day_type'  => $day_type,
                                'event'     => $event,
                                'title_cn'  => trim($val[4]),
                                'title_en'  => trim($val[5]),
                                'memo_cn'   => trim($val[6]),
                                'memo_en'   => trim($val[7]),
                                'privacy'   => !empty($privacy) && $privacy === 'Y' ? 1 : 0,
                                'school_id' => 0,
                                'user_id'   => Yii::app()->user->getId(),
                            );
                        }
                        //提交导入数据
                        $res = CommonUtils::requestDsOnline('calendar/importEvent', array('data' => $input_data));
                        if ($res['code'] == 0) {
                            $this->addMessage('state', 'success');
                            $this->addMessage('data', $res);
                            $this->showMessage();
                        } else {
                            $this->addMessage('state', 'fail');
                            $this->addMessage('message', $res['msg']);
                            $this->showMessage();
                        }
                    } else {
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', '读取文件失败');
                    }
                } else {
                    $this->addMessage('state', 'fail');
                    $errs = current($model->getErrors());
                    $this->addMessage('message', $errs ? $errs[0] : Yii::t('message', 'Failed!'));
                }
            } else {
                $this->addMessage('state', 'fail');
                $errs = current($model->getErrors());
                $this->addMessage('message', $errs ? $errs[0] : Yii::t('message', 'Failed!'));
            }
            $this->showMessage();
        }
    }

    //清空所有事件
    public function actionClearAllEvent()
    {
        $yid = Yii::app()->request->getParam("yid");
        $res = CommonUtils::requestDsOnline('calendar/clearAllEvent', array(
                'yid'      => $yid,
                'schoolid' => '0',
            )
        );
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }


}