<?php
$statusList = array(
    1=>'信息准确，人准备来参观',
    2=>'信息准确，人不来',
    3=>'过期、虚假或重复信息'
);

 ?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('//mcampus/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Basic'), array('//mcampus/default/index'))?></li>
        <li class="active"><?php echo Yii::t('admissions', 'Admissions management'); ?></li>
    </ol>

    <div class="row">
        <div class="col-md-2 col-sm-2">
            <?php
            $this->widget('zii.widgets.CMenu',array(
                'items'=> $this->getMenu(),
                'id'=>'pageCategory',
                'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked background-gray'),
                'activeCssClass'=>'active',
                'itemCssClass'=>''
            ));
            ?>
        </div>
        <div class="col-md-10 col-sm-10">
            <div class="row">
                <h4 class="col-md-12"><?php echo Yii::t('admissions', 'For review'); ?></h4>
                <div class="col-md-3">
                    <div id="pending" class="mb15"></div>
                    <?php
                    $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                        'name'=>'pending',
                        'options'=>array(
                            'showOtherMonths'=>true,
                            'selectOtherMonths'=>true,
                            'onSelect'=>'js:onSelect',
                            'beforeShowDay'=>'js:beforeShowDay',
                        ),
                        'htmlOptions'=>array(
                            'class'=>'hidden',
                        ),
                    ));
                    ?>
                    <div class="alert alert-success">
                    <?php echo Yii::t('admissions', 'Dates circled in red have scheduled visits. Please contact the parents within one working day'); ?></div>
                    <select class="form-control mb5" id="time" name="time" onchange = "dataUpdate('pending')" >
                        <?php foreach($configs as $k=>$v){ ?>
                            <option value ="<?php echo $k ?>" ><?php echo $v ?></option>
                        <?php } ?>
                    </select>
                </div>
                <div class="col-md-9">
                    <?php
                    $this->widget('ext.ivyCGridView.BsCGridView', array(
                        'id'=>'pending-visit-grid',
                        'afterAjaxUpdate'=>'js:head.Util.modal',
                        'dataProvider'=>$mdataProvider,
                        'template'=>"{items}{pager}{summary}",
                        'enableSorting'=>false,
                        'colgroups'=>array(
                            array(
                                "colwidth"=>array(10,100,100,100,200),
                            )
                        ),
                        'columns'=>array(
                            array(
                                'class' => 'CCheckBoxColumn',
                                'selectableRows' => 2,
                                'checkBoxHtmlOptions' => array(
                                    'class' => 'chose',
                                    'value' => '$data->id',
                                ),
                            ),
                            array(
                                'name' => 'visit_date',
                                'type'=>'raw',
                                'value' => 'OA::formatDateTime($data->visit_date)."<br><span>".$data->visit_time."</span>"'
                            ),
                            'child_name',
                            array(
                                'name'=>Yii::t('admissions', 'Parent contact'),
                                'type'=>'raw',
                                'value'=>'$data->parent_name."<br>".$data->phone."<br>".$data->email',
                            ),
                            array(
                                'class' => 'CButtonColumn',
                                'template' => '{update}',
                                'updateButtonLabel' => Yii::t('global', 'Edit'),
                                'updateButtonUrl' => 'Yii::app()->controller->createUrl("visitUpdate",array("id"=>$data->id, "branchId"=>Yii::app()->controller->branchId))',
                                'updateButtonImageUrl' => false,
                                'updateButtonOptions' => array('class'=>'J_modal btn btn-info btn-xs'),
                                'buttons' => array(
                                    'review' => array(
                                        'label' => Yii::t('admissions', 'check'),
                                        'imageUrl' => false,
                                        'url' => 'Yii::app()->controller->createUrl("visitAview",array("id"=>$data->id, "branchId"=>Yii::app()->controller->branchId))',
                                        'options' => array('class'=>'J_modal btn btn-info btn-xs'),
                                    ),
                                ),
                            ),
                        ),
                    ));
                    ?>
                    <?php if($mdataProvider->getData()): ?>
                    <div class="row">
                        <div class="col-md-3">
                            <?php echo Chtml::dropDownList('status', 1, $statusList, array('class'=>'form-control')); ?>
                        </div>
                        <!-- 无论艾毅或是起名均不显示发送邮件按钮 -->
                        <?php if(0&&$this->branchId != 'BJ_DS'): ?>
                        <div class="col-md-12">
                            <br>
                            &nbsp;
                            <label>
                                <input type="checkbox" id="send" checked="checked"> 发送邮件
                            </label>
                        </div>
                        <?php endif; ?>
                        <div class="col-md-12">
                            <br>
                            &nbsp;
                            <button class="btn btn-primary btn-xs" onclick="check(this)"><?php echo Yii::t('admissions', 'check'); ?></button>
                            <?php echo CHtml::link(Yii::t('admissions', 'print'), array('prints'), array('id'=>'prints', 'class'=>'btn btn-primary btn-xs', 'target'=>'_blank'));?>
                            <span id="exportExport"><a href="javascript:void(0);"  class="btn btn-primary btn-xs" onclick="exportParent('<?php echo $this->createUrl("exportClvs")?>')"><?php echo Yii::t('admissions', 'export'); ?></a></span>
                        </div>
                        <div class="col-md-12">
                            <br>
                            &nbsp;
                            <span id="J_fail_info" class="text-warning"></span>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            <div class="row">
                <h4 class="col-md-12">需要接待</h4>
                <div class="col-md-3">
                    <div id="confirmed" class="mb15"></div>
                    <?php
                    $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                        'name'=>'confirmed',
                        'options'=>array(
                            'showOtherMonths'=>true,
                            'selectOtherMonths'=>true,
                            'minDate'=>-90,
                            'onSelect'=>'js:onSelect',
                            'beforeShowDay'=>'js:beforeShowDay',
                        ),
                        'htmlOptions'=>array(
                            'class'=>'hidden',
                        ),
                    ));
                    ?>
                    <div class="alert alert-success">红圈标记的日期表示当天有确认的预约参观，这里可查阅当前及60日内的历史数据</div>
                    <select class="form-control mb5" id="times" name="times" onchange = "dataUpdate('confirmeddate')" >

                        <?php foreach($configs as $k=>$v){ ?>
                            <option value ="<?php echo $k ?>" ><?php echo $v ?></option>
                        <?php } ?>
                    </select>
                </div>
                <div class="col-md-9">
                    <?php
                    $this->widget('ext.ivyCGridView.BsCGridView', array(
                        'id'=>'confirmed-visit-grid',
                        'afterAjaxUpdate'=>'js:head.Util.modal',
                        'dataProvider'=>$dataProvider,
                        'enableSorting'=>false,
                        'template'=>"{items}{pager}{summary}",
                        'colgroups'=>array(
                            array(
                                "colwidth"=>array(10,100,100,100,200),
                            )
                        ),
                        'columns'=>array(
                            array(
                                'class' => 'CCheckBoxColumn',
                                'selectableRows' => 2,
                                'checkBoxHtmlOptions' => array(
                                    'class' => 'visitchose',
                                    'value' => '$data->id',
                                ),
                                'checked' => '($data->category == "visit") ? "true":""',
                                'disabled' => '($data->category == "visit") ? "true":""',
                            ),
                            array(
                                'name' => 'appointment_date',
                                'type'=>'raw',
                                'value' => 'OA::formatDateTime($data->appointment_date)."<br><span>".$data->appointment_time."</span>"'
                            ),
                            array(
                                'name'=>'孩子姓名',
                                'value'=>'$data->basic->child_name',
                            ),
                            array(
                                'name'=>'家长信息',
                                'type'=>'raw',
                                'value'=>'$data->basic->parent_name."<br>".$data->basic->tel."<br>".$data->basic->email',
                            ),
                            array(
                                'class' => 'CButtonColumn',
                                'template' => '{update} {changedate}',
                                'updateButtonLabel' => Yii::t('visit', '添加跟踪记录'),
                                'updateButtonImageUrl' => false,
                                'updateButtonUrl' => 'Yii::app()->controller->createUrl("tracelog",array("vid"=>$data->id,"bid"=>$data->basic->id))',
                                'updateButtonOptions' => array('class'=>'J_modal btn btn-info btn-xs'),
                                'buttons' => array(
                                    'changedate' => array(
                                        'label' => Yii::t('visit', '重新安排时间'),
                                        'imageUrl' => false,
                                        'visible' => '$data->category == "appointment"',
                                        'url' => 'Yii::app()->controller->createUrl("visitChangeDate",array("id"=>$data->id, "branchId"=>Yii::app()->controller->branchId))',
                                        'options' => array('class'=>'J_modal btn btn-info btn-xs'),
                                    ),
                                ),
                            ),
                        ),
                    ));
                    ?>
                    <div class="confirm row <?php if(!$dataProvider->getData()) echo 'hide'; ?>">
                        <div class="col-md-12">
                            &nbsp;
                            <label>
                                    <input type="checkbox" checked="checked" id="visit-send"> 发送邮件
                            </label>
                        </div>
                        <div class="col-md-12">
                            <br>
                            &nbsp;
                            <button class="btn btn-primary btn-xs" onclick="visitcheck(this)">确认到场</button>
                            <?php echo CHtml::link('打印', array('print'), array('id'=>'print', 'class'=>'btn btn-primary btn-xs', 'target'=>'_blank'));?>
                            <span id="exportExport_2">
                                <a class="btn btn-primary btn-xs" onclick="exportParent('<?php echo $this->createUrl("exportClv")?>')"><?php echo Yii::t('admissions', 'export'); ?></a>
                            </span>
                        </div>
                        <div class="col-md-12">
                            <br>
                            &nbsp;
                            <span id="visit-error" class="text-warning"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- 发信弹框 -->
<div id="pro-modal" class="modal fade bs-example-modal-sm" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
                <h4 class="modal-title" id="mySmallModalLabel">正在处理...</h4>
            </div>
            <div class="modal-body">
                请勿操作。
            </div>
        </div>
    </div>
</div>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>

<script type="text/javascript">
    var aUpdate = true;
    var pendingdate = {};
    var confirmeddate = {};
    var str = "";
    var targetDate = {
        d: '',
        dt: '',
        dateText: ''
    };

    function upPending()
    {
        $.ajax({
            async: false,
            type: 'get',
            url: '<?php echo $this->createUrl('targetDate');?>',
            dataType: 'json',
            data: {type: 'pending', branchId:'<?php echo $this->branchId?>'}
        }).done(function(data){
            pendingdate = data;
        });
    }
    upPending();
    function upConfirmed()
    {
        $.ajax({
            async: false,
            type: 'get',
            url: '<?php echo $this->createUrl('targetDate');?>',
            dataType: 'json',
            data: {type: 'confirmed', branchId:'<?php echo $this->branchId?>'}
        }).done(function(data){
            confirmeddate = data;
        });
    }
    upConfirmed();
    function beforeShowDay(date) {
        var rData = (this.id === 'pending') ? pendingdate : confirmeddate;
        var cssClass = '';
        var d = date.getFullYear().toString()+(date.getMonth()+1).toString()+date.getDate().toString();
        if(rData[d])
            cssClass = 'date-active';
        return [true, cssClass];
    }
    function onSelect(dateText, inst) {
        if (aUpdate){
            aUpdate = false;
            var dt = inst.currentYear+'-'+(inst.currentMonth+1)+'-'+inst.currentDay;
            var d = inst.selectedYear.toString()+(inst.selectedMonth+1).toString()+inst.selectedDay.toString();
            targetDate.d = d;
            targetDate.dt = dt;
            targetDate.dateText = dateText;
            if (this.id === 'pending'){
                dataUpdate('pending');

            }
            else{
                dataUpdate('confirmed');
            }
        }
        else{
            resultTip({error:true, msg: '正在请求，请不要过快点击！'});
        }
    }

    function dataUpdate(type)
    {
        var filterHtml = "";
        if(type == 'pending'){
            var rData = pendingdate;
            var time = $('#time').val();
            if(time == 0){
                time = "";
            }
            if (rData[targetDate.d] || $('#time').val()){
                if(rData[targetDate.d]){
                    filterHtml += '<a href="javascript:;" class="btn btn-danger btn-xs" onclick=showAll("pending-visit-grid")>'+targetDate.dt+' <b class="glyphicon glyphicon-remove"></b></a> ';
                }
                if($('#time').val()  != 0 ){
                    filterHtml += '<a href="javascript:;" class="btn btn-danger btn-xs" onclick=showAlla("pending-visit-grid")>'+ time +' <b class="glyphicon glyphicon-remove"></b></a>';
                }
                $.fn.yiiGridView.update("pending-visit-grid", { data:{"IvyschoolsVisit[visit_date]":targetDate.dateText, "IvyschoolsVisit[visit_time]":time}, complete: function(){
                    $('#pending-visit-grid').prepend(filterHtml);
                    var href = '<?php echo $this->createUrl('exportClvs');?>';
                    var href = href + '&IvyschoolsVisit[visit_date]=' + targetDate.dateText + '&IvyschoolsVisit[visit_time]=' + time;                    

                    var exporthref = '<?php echo $this->createUrl('exportClvs');?>';
                    var exporthref = exporthref + '&IvyschoolsVisit[visit_date]=' + targetDate.dateText + '&IvyschoolsVisit[visit_time]=' + time;

                    $('#exportExport a').empty();
                    var html = '<a  href="javascript:void(0);" class="btn btn-primary btn-xs" onclick="exportParent(\'' + exporthref + '\')"><?php echo Yii::t("admissions", "export"); ?></a>';
                    $('#exportExport').html(html);


                    $('#prints').attr('href', href);

                    aUpdate = true;
                }});
            }
            else{
                resultTip({error:true, msg: targetDate.dt+' 没有记录'});
                aUpdate = true;
            }
        }
        else{
            var rData = confirmeddate;
            var times = $('#times').val();
            if(times == 0){
                times = "";
            }
            if (rData[targetDate.d] || $('#times').val()){
                if(rData[targetDate.d]){
                    filterHtml += '<a href="javascript:;" class="btn btn-danger btn-xs" onclick=showAll("confirmed-visit-grid")>'+targetDate.dt+' <b class="glyphicon glyphicon-remove"></b></a> ';
                }
                if($('#times').val()  != 0 ){
                    filterHtml += '<a href="javascript:;" class="btn btn-danger btn-xs" onclick=showAlla("confirmed-visit-grid")>' + times +' <b class="glyphicon glyphicon-remove"></b></a>';
                }
                $.fn.yiiGridView.update("confirmed-visit-grid", {data:{"IvyVisitsRecord[appointment_date]":targetDate.dateText, "IvyVisitsRecord[appointment_time]": times }, complete: function(){
                    $('#confirmed-visit-grid').prepend(filterHtml);
                    var href = '<?php echo $this->createUrl('print');?>';
                    var href = href + '&IvyVisitsRecord[appointment_date]=' + targetDate.dateText + '&IvyVisitsRecord[appointment_time]=' + times;
                    $('#print').attr('href', href);
                    var exporthref = '<?php echo $this->createUrl('exportClv');?>';
                    var exporthref = exporthref + '&IvyVisitsRecord[appointment_date]=' + targetDate.dateText + '&IvyVisitsRecord[appointment_time]=' + times;

                    $('#exportExport_2').empty();
                    var html = '<a href="javascript:void(0);" class="btn btn-primary btn-xs" onclick="exportParent(\'' + exporthref + '\')"><?php echo Yii::t("admissions", "export"); ?></a>';
                    $('#exportExport_2').html(html);


                    $('.confirm').removeClass('hide');
                    aUpdate = true;
                }});
            }
            else {
                resultTip({error:true, msg: targetDate.dt+' 没有记录'});
                aUpdate = true;
            }
        }
    }

    function showAll(id)
    {
        $.fn.yiiGridView.update(id, {data:{"IvyVisitsRecord[appointment_date]":"", "IvyschoolsVisit[visit_date]":"", "IvyVisitsRecord[appointment_time]":$('#times').val(), "IvyschoolsVisit[visit_time]":$('#time').val()}});
        if(id == 'pending-visit-grid'){

            if($('#time').val() != 0){
                targetDate = {
                    d: '',
                    dt: '',
                    dateText: ''
                };

                dataUpdate('pending');
            }else{
                upPending();
            }
            $( "#pending" ).datepicker( "refresh" );
            var hrefs = '<?php echo $this->createUrl('exportClvs');?>';
            var hrefs = hrefs + '&IvyschoolsVisit[visit_date]=';
            $('#prints').attr('href', hrefs);
            var exporthref = '<?php echo $this->createUrl('exportClv');?>'
            $('#exportExport a').empty();
            var html = '<a href="javascript:void(0);" class="btn btn-primary btn-xs" onclick="exportParent(\'' + exporthref + '\')"><?php echo Yii::t("admissions", "export"); ?></a>';
            $('#exportExport').html(html);

            $('.confirm').removeClass('hide');
        }
        else{
            if($('#times').val() != 0){
                targetDate = {
                    d: '',
                    dt: '',
                    dateText: ''
                };

                dataUpdate('confirmed');
            }else{
                upConfirmed();
            }
            var exporthref = '<?php echo $this->createUrl('exportClv');?>'
            $('#exportExport_2 a').empty();
            var html = '<a href="javascript:void(0);" class="btn btn-primary btn-xs" onclick="exportParent(\'' + exporthref + '\')"><?php echo Yii::t("admissions", "export"); ?></a>';
            $('#exportExport_2').html(html);

            $( "#confirmed" ).datepicker( "refresh" );
            var href = '<?php echo $this->createUrl('print');?>';
            var href = href + '&IvyVisitsRecord[appointment_date]=';
            $('#print').attr('href', href);
            $('#export').attr('href', '<?php echo $this->createUrl('exportClv');?>');
            $('.confirm').removeClass('hide');
        }
    }

    function showAlla(id)
    {
        $.fn.yiiGridView.update(id, {data:{"IvyVisitsRecord[appointment_date]":targetDate.dateText, "IvyschoolsVisit[visit_date]":targetDate.dateText, "IvyVisitsRecord[appointment_time]":"", "IvyschoolsVisit[visit_time]":""}});
        if(id == 'pending-visit-grid'){
            $("#time").val("0");
            if(targetDate.dateText){
                dataUpdate('pending');
            }else{
                upPending();
            }
            //
            var exporthref = '<?php echo $this->createUrl('exportClv');?>'
            $('#exportExport a').empty();
            var html = '<a href="javascript:void(0);" class="btn btn-primary btn-xs" onclick="exportParent(\'' + exporthref + '\')"><?php echo Yii::t("admissions", "export"); ?></a>';
            $('#exportExport').html(html);

        }
        else{
            $("#times").val("0");
            if(targetDate.dateText){
                dataUpdate('confirmed');
            }else{
                upConfirmed();
            }
            var exporthref = '<?php echo $this->createUrl('exportClv');?>'
            $('#exportExport_2 a').empty();
            var html = '<a href="javascript:void(0);" class="btn btn-primary btn-xs" onclick="exportParent(\'' + exporthref + '\')"><?php echo Yii::t("admissions", "export"); ?></a>';
            $('#exportExport_2').html(html);
        }
    }

    function check(btn) {
        var status = $('#status').val();
        var send = $('#send').prop('checked');
        var id = [];
        $('.chose').each(function(i, val){
            if (val.checked) {
                id[i] = $(val).val();
            }
        });
        if (!id.length || !status) {
            $('#J_fail_info').text('至少选择一个记录');
            return;
        }
        // show madal
        $(btn).prop("disabled","disabled");
        $('#pro-modal').modal({
          show: true,
          backdrop: 'static'
        });
        // ajax request
        $.ajax({
            type: 'post',
            url: '<?php echo $this->createUrl("visitAview", array('branchId'=>$this->branchId));?>',
            data: {status: status, id: id, send: send},
            success: function (data) {
                var data = $.parseJSON(data);
                if (data.state == 'success') {
                    $.fn.yiiGridView.update('pending-visit-grid');
                    $.fn.yiiGridView.update('confirmed-visit-grid');
                    $('.confirm').removeClass('hide');
                }
                if (data.state == 'fail') {
                    $('#J_fail_info').text(data.message);
                }
            }
        }).done(function () {
            $(btn).removeAttr('disabled');
            $('#pro-modal').modal('hide');
        });
    }

    function checkAll(btn) {
        if ($('.chose').length == $('.chose:checked').length) {
            $('.chose').attr('checked', false);
        } else{
            $('.chose').attr('checked', true);
        }
    }

    function visitcheck(btn) {
        var send = $("#visit-send").prop('checked');
        var id = [];
        $('.visitchose').each(function(i, val){
            if (val.checked && !val.disabled) {
                id[i] = $(val).val();
            }
        });
        if (id.length == 0) {
            $('#visit-error').text('至少选择一个记录');
            return;
        }

        // show modal
        $(btn).prop("disabled","disabled");
        $('#pro-modal').modal({
          show: true,
          backdrop: 'static'
        });

        // ajax request
        $.ajax({
            type: 'post',
            url: '<?php echo $this->createUrl("confirmVisit", array('branchId'=>$this->branchId));?>',
            data: {ids: id, send: send},
            success: function (data) {
                var data = $.parseJSON(data);
                if (data.state == 'success') {
                    $.fn.yiiGridView.update('confirmed-visit-grid');
                }
                if (data.state == 'fail') {
                    $('#visit-error').text(data.message);
                }
            }
        }).done(function () {
            $(btn).removeAttr('disabled');
            $('#pro-modal').modal('hide');
        });
    }

    function visitcheckAll(btn) {
        if ($('.visitchose').length == $('.visitchose:checked').length) {
            $('.visitchose').each(function(i, val){
                if (!val.disabled){
                    $(val).attr('checked', false);
                }
            });
        } else{
            $('.visitchose').attr('checked', true);
        }
    }
    $(document).ready(function(){
        var schoolsCount = <?php echo $schoolsCount;?>;
        for(var schoolid in schoolsCount){
            $('#branch-selector div.list a#school_'+schoolid).append('<i class="extra-number">'+schoolsCount[schoolid]+'</i>');
        }
        $('#status').bind('change', function (e) {
            if ($(this).val() == 1) {
                $('#send').parent().show();
                $('#send').attr('checked', 'checked');
            } else{
                $('#send').parent().hide();
            }
        });
    });
    var modal = '<div class="modal fade" id="modal" tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog" role="document"><div class="modal-content"></div></div></div>';
    $('body').append(modal);

    function exportParent(url) {
        $.ajax({
            url: url,
            type: 'POST',
            success: function (res) {
                if (res.state == 'success') {
                    var data = res.data.items;
                    const filename = res.data.title;
                    const ws_name = "SheetJS";

                    const worksheet = XLSX.utils.aoa_to_sheet(data);
                    const workbook = XLSX.utils.book_new();
                    XLSX.utils.book_append_sheet(workbook, worksheet, ws_name);
                    // generate Blob
                    const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                    const blob = new Blob([wbout], {type: 'application/octet-stream'});
                    // save file
                    let link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    link.download = filename;
                    link.click();
                    setTimeout(function() {
                        // 延时释放掉obj
                        URL.revokeObjectURL(link.href);
                        link.remove();
                    }, 500);
                }
            },
            dataType: 'json'
        });
    }

</script>
<style>
    .ui-datepicker{
        width: auto;
    }
    .date-active a {
        background-position: center -2px !important;
    }
</style>