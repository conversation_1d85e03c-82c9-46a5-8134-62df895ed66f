<?php

class StudentController extends BranchBasedController
{
    public $actionAccessAuths = array(
        'index'                     => 'o_A_Access',
        'invoices'                  => 'o_A_Access',
        'SibingAllSearch'           => 'o_A_Adm_Student',
        'GetSiblings'               => 'o_A_Adm_Student',
        'Register'                  => 'o_A_Adm_Student',
        'CheckChild'                => 'o_A_Adm_Student',
        'ExporRegister'             => 'o_A_Adm_Student',
        'CheckChildtChildInfo'      => 'o_A_Adm_Student',
        'ExportChildInfo'           => 'o_A_Adm_Student',
    );

    public $branchObj=null;
    public $constantSchoolList = array('BJ_IA','BJ_CP', 'BJ_IASLT'); //老生老办法特殊学校

    public $printFW = array();
    protected $securityKey = 'A23sW343eL8934ov2332E';

    public function createUrl($route, $params = array(), $ampersand = '&', $parentOnly = false)
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Campus Operations');



        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mcampus/student/index','category'=>'current');
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/vue2.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/vue.global.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/xlsx.full.min.js');

        //字体
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/label/iconfont.css?v=20231124');

    }

    public function actionSelect(){
        $this->render('branchSelect');
    }

    /**
     * 学生管理基本页面
     */
    public function actionIndex()
    {
        Yii::import('common.models.calendar.*');
        Yii::import('common.models.classTeacher.*');

        $category = Yii::app()->request->getParam('category',null);
        if (!empty($category)){
            switch (strtolower($category)){
                case 'newreg':
                case 'switch':
                    if (isset($_POST['AssignForm'])){
                        $model = new AssignForm();
                        $model->attributes = $_POST['AssignForm'];
                        if ($model->validate()){
                            $sClassList = explode('-', $model->classid);
                            $resutlList = array();
                            $criter = new CDbCriteria;
                            $criter->compare('childid', $model->childid);
                            $criter->compare('schoolid', $this->branchId);
                            $criter->index = 'childid';
                            $childModel = ChildProfileBasic::model()->findAll($criter);
                            if (empty($childModel)){
                                $this->addMessage('state','fail');
                                $this->addMessage('message', Yii::t('student', '提交的信息有错误.'));
                                $this->showMessage();
                            }
                            if (count($sClassList) == 2){
                                $classModel = IvyClass::model()->find('classid=:classid and schoolid=:schoolid',array(':classid'=>$sClassList[1],':schoolid'=>$this->branchId));
                                if (empty($classModel)){
                                    $this->addMessage('state','fail');
                                    $this->addMessage('message', Yii::t('student', '提交的信息有错误.'));
                                    $this->showMessage();
                                }
                                $resutlList = $this->batchAssignClass($childModel,$classModel,$sClassList[0]);
                            }else{#已退学+已毕业
                                $status = ($sClassList[0]=='setGraduated') ? ChildProfileBasic::STATS_GRADUATED : ChildProfileBasic::STATS_DROPOUT;
                                $resutlList = $this->setChildStatus($childModel, NULL, $status);
                            }
                            $this->addMessage('data',$resutlList);
                            $this->addMessage('state','success');
                            $this->addMessage('message',Yii::t('student','保存成功'));
                            $this->addMessage('callback','setReturnStatus');
                        }else{
                            $this->addMessage('state','fail');
                            $errs = current($model->getErrors());
                            $this->addMessage('message',$errs[0]);
                        }
                        $this->showMessage();
                    }
                    break;
            }
        }
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/jquery.number.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/xlsx.full.min.js');
        $this->render('index');
    }

    /**
     * 查看学生每月的交费信息
     */
    public function actionInvoices(){
        $return = array();
        Yii::import('common.models.calendar.*');
        $calendar = CalendarSchool::model()->findByAttributes(array('branchid'=>$this->branchId, 'yid'=>$_POST['selectedYid']));
        if(!is_null($calendar)){
            for($i=0; $i<12; $i++){
                $return['ym'][] = date('Ym', mktime(0,0,0,9+$i, 1, $calendar->startyear));
            }

            $command = Yii::app()->db->createCommand();
            $command->select(array('invoice_id', 'childid', 'payment_type', 'startdate', 'enddate', 'status'));
            $command->from('ivy_invoice_invoice');
            $command->where('schoolid=:schoolid and calendar_id=:calendar_id and `inout`=:inout and status <=:status', array(
                ':schoolid'=>$this->branchId,
                ':calendar_id'=>$_POST['selectedYid'],
                ':inout'=>'in',
                ':status'=> 77
            ));
            $command->andWhere(array('in', 'payment_type', array('tuition','bus','lunch','deposit','registration')));

            if($_POST['rangeAll'] == 0 && !empty($_POST['childIds'])){
                $childIds = explode(',', $_POST['childIds']);
                foreach($childIds as $i=>$v){
                    $childIds[$i] = intval($v);
                }
                $command->andWhere(array('in', 'childid', $childIds));
            }
            $rows = $command->queryAll();

            foreach($rows as $row){

                if(in_array($row['payment_type'], array('deposit','registration'))){
                    $return['invoices'][$row['childid']][$row['payment_type']] = array('id'=>$row['invoice_id'], 'status'=> $row['status'], 'type'=>$row['payment_type'],'childid'=>$row['childid']);
                }else{
                    $ymBegin = date('Ym', $row['startdate']);
                    $ymEnd = date('Ym', $row['enddate']);
                    if($ymBegin != $ymEnd){
                        list($startY, $startM) = array( date('Y', $row['startdate']), date('m', $row['startdate']));
                        for($i=0; $i<12; $i++){
                            $theym = date('Ym', mktime(1,1,1,$startM+$i, 1, $startY));
                            if($theym <= $ymEnd){
                                $return['invoices'][$row['childid']][$row['payment_type']][$theym][] = array('id'=>$row['invoice_id'], 'status'=> $row['status'], 'type'=>$row['payment_type'],'childid'=>$row['childid']);
                            }else{
                                break;
                            }
                        }
                    }else{
                        $return['invoices'][$row['childid']][$row['payment_type']][$ymBegin][] = array('id'=>$row['invoice_id'], 'status'=> $row['status'], 'type'=>$row['payment_type'],'childid'=>$row['childid']);
                    }
                }
            }

            $this->addMessage('state', 'success');
            $this->addMessage('data', $return);

        }else{
            $this->addMessage('state', 'fail');
            $this->addMessage('data', 'Invalid POST data');
        }

        $this->showMessage();
    }

//    public function getCampusStuduents($branchid,$status='<100'){
//        $crit = new CDbCriteria();
//        $crit->compare('schoolid', $branchid);
//        $crit->compare('status', $status);
//        $childrenObjs = ChildProfileBasic::model()->findAll($crit);
//        foreach($childrenObjs as $child){
//            $childData[] = array(
//                'id'=>$child->childid,
//                'birth'=>$child->birthday_search,
//                'name'=>$child->getChildName(),
//                'classid'=>intval($child->classid),
//                'status'=>$child->status,
//                'age'=> CommonUtils::getAge($child->birthday)
//            );
//        }
//        return $childData;
//    }

    public function getStuduentsByClass($branchId, $classIds,$status='<100', &$redalertCount=null, &$droppingCount=null, $year = null){
        Yii::import('common.models.child.StatsChildCount');

        $criteria = new CDbCriteria();
        $criteria->compare('classid', 0);
        $criteria->compare('schoolid', $branchId);
        $criteria->compare('period_timestamp', strtotime('today'));
        $countModel = StatsChildCount::model()->find($criteria);
        $countArr = array();
        if ($countModel->childids1 || $countModel->childids2) {
            $countArr = explode(',', $countModel->childids1.','.$countModel->childids2);
        }

        $crit = new CDbCriteria();
        $crit->compare('schoolid', $branchId);
        $crit->compare('classid', $classIds);
        $crit->compare('status', $status);
        $crit->order = 'birthday ASC';
        $childrenObjs = ChildProfileBasic::model()->findAll($crit);
        $cnt = 0;
        $cnt2 = 0;
        //查询有星标的学生
        $res = CommonUtils::requestDsOnline2('label/child/getChildInfo', array(
            'school_id' => $this->branchId,
            'flag'=>1,
        ),'get');
        $label_child = array();
        if ($res['code'] == 0) {
            foreach ($res['data']['list'] as $type => $item){
                foreach ($item as $class_name =>$child_info){
                    foreach ($child_info as $value){
                        $label_child[$value['id']] = array(array('flag'=>1));
                    }
                }
            }
        }
        $withDrawaStudentIds = array();
        if ($year) {
            $withDrawaStudentIds = $this->getWithdrawalStudentListByYear($year);
        }
        foreach($childrenObjs as $child){
            $withDrawalStyle = '';
            if (isset($withDrawaStudentIds[$child->childid])) {
                $withDrawalStyle =  $withDrawaStudentIds[$child->childid]['type'] == 10 ? 'highlight4' : 'highlight5';
            }

            $issafe = in_array($child->childid, $countArr);
            $childData[] = array(
                'id'=>$child->childid,
                'birth'=>$child->birthday_search,
                'name'=>CHtml::encode($child->getChildName()),
                'classid'=>intval($child->classid),
                'status'=>$child->status,
                'gender'=>$child->gender,
                'nick'=>CHtml::encode($child->nick),
                'age'=> CommonUtils::getAge($child->birthday),
                'country' => $child->country,
                'redalert' => $issafe ? 0 : 1,
                'label'=>$label_child[$child->childid] ? $label_child[$child->childid] : array(),//高亮星标的学生
                'isWithDrawal' => isset($withDrawaStudentIds[$child->childid]) ? 1 : 0,
                'withDrawalStyle' => $withDrawalStyle,
            );
            $cnt += $issafe ? 0 : 1;
            $cnt2 += $child->status == 888 ? 1 : 0;
        }
        if ($redalertCount != null) $redalertCount = $cnt;
        if ($droppingCount != null) $droppingCount = $cnt2;
        return $childData;
    }

    public function getNextStudentsByClass($branchId, $classIds,$status='<100', $year = null){
        Yii::import('common.models.invoice.*');
        $crit = new CDbCriteria();
        $crit->compare('t.schoolid',$branchId);
        $crit->compare('t.classid',$classIds);
        $crit->order = 'childProfile.birthday ASC';
        $reserveObjs = ChildReserve::model()->with(array('childProfile'=>array(
            'condition'=>'t.stat' . $status
        )))->findAll($crit);
        $withDrawaStudentIds = array();
        if ($year) {
            $withDrawaStudentIds = $this->getWithdrawalStudentListByYear($year);
        }
        $childData = array();
        foreach($reserveObjs as $reserve){
            $withDrawalStyle = '';
            if (isset($withDrawaStudentIds[$reserve->childid])) {
                $withDrawalStyle =  $withDrawaStudentIds[$reserve->childid]['type'] == 10 ? 'highlight4' : 'highlight5';
            }
            $childData[] = array(
                'id'=>$reserve->childid,
                'birth'=>$reserve->childProfile->birthday_search,
                'name'=>CHtml::encode($reserve->childProfile->getChildName()),
                'classid'=>intval($reserve->classid),
                'gender'=>$reserve->childProfile->gender,
                'nick'=>CHtml::encode($reserve->childProfile->nick),
                'status'=>$reserve->childProfile->status,
                'age'=> CommonUtils::getAge($reserve->childProfile->birthday),
                'country' => $reserve->childProfile->country,
                'isWithDrawal' =>  isset($withDrawaStudentIds[$reserve->childid]) ? 1 : 0,
                'withDrawalStyle' => $withDrawalStyle,
            );
        }
        return $childData;

    }

    function getWithdrawalStudentIdsByYear($year)
    {
        if (!in_array($this->branchId, CommonUtils::dsSchoolList())) {
            return array();
        }
        $requestUrl = 'withdrawal/studentIds';
        $requestData['school_id'] = $this->branchId;
        $requestData['year'] = $year;

        $res = CommonUtils::requestDsOnline($requestUrl, $requestData);
        if ($res['code'] == 0) {
            return $res['data'];
        } else {
            return array();
        }
    }

    function getWithdrawalStudentListByYear($year)
    {
        if (!in_array($this->branchId, CommonUtils::dsSchoolList())) {
            return array();
        }
        $requestUrl = 'withdrawal/studentList';
        $requestData['school_id'] = $this->branchId;
        $requestData['year'] = $year;

        $res = CommonUtils::requestDsOnline($requestUrl, $requestData);
        if ($res['code'] == 0) {
            return $res['data'];
        } else {
            return array();
        }
    }

    //$status = array(10,20);
    //$status = '<100';
    //$status = 0;
    public function getStudentsWithNextYearInfo($branchId, $currentClassIds=null, $nextYid, $status=null){
        Yii::import('common.models.invoice.*');
        Yii::import('common.models.visit.*');
        $crit = new CDbCriteria();
        $crit->compare('schoolid', $branchId);
        if( !is_null($currentClassIds) ){
            $crit->compare('classid', $currentClassIds);
        }
        if(!is_null($status))
            $crit->compare('status', $status);

        $crit->order = 'status ASC';
        if($currentClassIds == 0)
            $crit->order = 'created_timestamp DESC';

        $childrenObjs = ChildProfileBasic::model()->findAll($crit);

        $childid = array();
        foreach($childrenObjs as $child){
            if($child->admission_id){
                $childid[] = $child->admission_id;
            }
        }


        $child_class = AdmissionsDs::model()->findAllByPk($childid);

        $crit = new CDbCriteria();
        $crit->compare('schoolid', $branchId);
        $crit->compare('calendar', $nextYid);
        $crit->compare('stat', 20);
        $crit->index = 'childid';
        $reserves = ChildReserve::model()->findAll($crit);


        foreach($childrenObjs as $child){
            $admission_class = "";
            foreach($child_class as $_child_class){
                if($child->admission_id == $_child_class->id){
                    $admission_class = $_child_class->start_grade;
                }
            }
            $childData[] = array(
                'id'=>$child->childid,
                'birth'=>$child->birthday_search,
                'name'=>$child->getChildName(),
                'classid'=>($admission_class) ? $admission_class : intval($child->classid),
                'status'=> $child->status,
                'statusText'=>($child->status>=100)?OA::getChildStatus($child->status):'',
                'age'=> CommonUtils::getAge($child->birthday),
                'nextClassId' => is_null($reserves[$child->childid]) ? 0 : $reserves[$child->childid]->classid,
                'created' => date('Y-m-d', $child->created_timestamp),
                'gender' => $child->gender,
                'country' => $child->country,
            );
        }
        return $childData;
    }

    // 新注册及过期数据
    public function getNewregStudents(){
        Yii::import('common.models.invoice.ChildReserve');
        $branchId = $this->branchId;
        $yids = $this->getCalendars();
        $yid = isset($yids['currentYid']) ? $yids['currentYid'] : 0;
        $nextYid = isset($yids['nextYid']) ? $yids['nextYid'] : 0;

        $status = '<100';
        $crit = new CDbCriteria();
        $crit->compare('schoolid', $branchId);
        $crit->compare('status', $status);

        $crit->order = 'status ASC';

        $childrenObjs = ChildProfileBasic::model()->findAll($crit);

        // 查找新注册学生
        $crit = new CDbCriteria();
        $crit->compare('schoolid', $branchId);
        $crit->compare('classid', 0);
        $crit->compare('status', '<100');
        $crit->index = 'childid';
        $newregChildModels = ChildProfileBasic::model()->findAll($crit);
        
        // 查找过期数据
        $crit = new CDbCriteria();
        $crit->compare('t.schoolid', $branchId);
        $crit->compare('t.status', '<100');
        $crit->compare('ivyclass.yid', "<$yid");
        $crit->with = array('ivyclass');
        $crit->index = 'childid';
        $expiredChildModels = ChildProfileBasic::model()->findAll($crit);

        // 查找下学年分班
        $reserves = array();
        if ($nextYid) {
            $childIds = array_keys($newregChildModels) + array_keys($expiredChildModels);
            $crit = new CDbCriteria();
            $crit->compare('schoolid', $branchId);
            $crit->compare('calendar', $nextYid);
            $crit->compare('childid', $childIds);
            $crit->compare('stat', 20);
            $crit->index = 'childid';
            $reserves = ChildReserve::model()->findAll($crit);
        }

        $childrenObjs = array_merge($newregChildModels, $expiredChildModels);

        foreach($childrenObjs as $child){
            $childData[] = array(
                'id'=>$child->childid,
                'birth'=>$child->birthday_search,
                'name'=>$child->getChildName(),
                'classid'=>isset($expiredChildModels[$child->childid]) ? -1 : 0,
                'status'=> $child->status,
                'statusText'=>($child->status>=100)?OA::getChildStatus($child->status):'',
                'age'=> CommonUtils::getAge($child->birthday),
                'nextClassId' => isset($reserves[$child->childid]) ? $reserves[$child->childid]->classid : 0,
                'created' => date('Y-m-d', $child->created_timestamp),
                'gender' => $child->gender,
                'country' => $child->country,
            );
        }

        return $childData;
    }

    public function actionCancelNextClass()
    {
        $childId = Yii::app()->request->getPost('childid');
        if (!$childId) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'childId not null');
            $this->showMessage();
        }
        Yii::import('common.models.invoice.*');
        Yii::import('common.models.portfolio.ChildStudyHistory');
        Yii::import('common.models.*');
        $model = ChildReserve::model()->findByAttributes(array('childid' => $childId));
        if (!$model) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message','下学年分班未找到');
            $this->showMessage();
        }
        if ($model->stat == 999) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message','下学年分班状态错误');
            $this->showMessage();
        }

        $model->stat = 999;
        $model->save();

        $ChildStudyHistoryModel = new ChildStudyHistory();
        $ChildStudyHistoryModel->childid = $model->childid;
        $ChildStudyHistoryModel->schoolid = $model->schoolid;
        $ChildStudyHistoryModel->classid = $model->classid;
        $ChildStudyHistoryModel->calendar = $model->calendar;
        $ChildStudyHistoryModel->semester = 0;
        $ChildStudyHistoryModel->stat = 50;
        $ChildStudyHistoryModel->timestamp = time();
        $ChildStudyHistoryModel->userid = Yii::app()->user->id;
        $ChildStudyHistoryModel->from_classid = 0;
        $ChildStudyHistoryModel->save();

        $this->addMessage('state', 'success');
        $this->addMessage('message','success');
        $this->showMessage();
    }

    //old 获取学生的信息
    public function getStudentsWithSwitchInfo($branchId, $currentClassIds=null, $nextYid, $status=null){
        Yii::import('common.models.invoice.*');
        Yii::import('common.models.visit.*');
        $crit = new CDbCriteria();
        $crit->compare('schoolid', $branchId);
        if( !is_null($currentClassIds) ){
            $crit->compare('classid', $currentClassIds);
        }
        if(!is_null($status))
            $crit->compare('status', $status);

        $crit->order = 'status ASC';
        if($currentClassIds == 0)
            $crit->order = 'created_timestamp DESC';

        $childrenObjs = ChildProfileBasic::model()->findAll($crit);
//var_dump($childrenObjs);die;
        $crit = new CDbCriteria();
        $crit->compare('schoolid', $branchId);
        $crit->compare('calendar', $nextYid);
        $crit->index = 'childid';
        $reserves = ChildReserve::model()->findAll($crit);

        //获取学生是否缴纳了定位金 学费
        $crit = new CDbCriteria();
        $crit->compare('schoolid', $branchId);
        $crit->compare('calendar_id', $nextYid);
        $crit->compare('status', Invoice::STATS_PAID);#已付款
        $crit->compare('payment_type', array('deposit','tuition'));
        $invoice =Invoice::model()->findAll($crit);
        //初始化定位金和学费数据
        $child_amount = array();
        foreach ($childrenObjs as $item){
            $child_amount[$item->childid]['deposit'] = 0.00;
            $child_amount[$item->childid]['tuition'] = 0.00;
        }
        foreach ($invoice as $invoice_item){
            #入账
            if($invoice_item->inout === 'in'){
                if($invoice_item->payment_type === 'deposit'){
                    $child_amount[$invoice_item->childid]['deposit'] = bcadd($child_amount[$invoice_item->childid]['deposit'],$invoice_item->amount,2);
                }elseif($invoice_item->payment_type === 'tuition'){
                    $child_amount[$invoice_item->childid]['tuition'] = bcadd($child_amount[$invoice_item->childid]['tuition'],$invoice_item->amount,2);
                }
            }
            #出账
            if($invoice_item->inout === 'out'){
                if($invoice_item->payment_type === 'deposit'){
                    $child_amount[$invoice_item->childid]['deposit'] = bcsub($child_amount[$invoice_item->childid]['deposit'],$invoice_item->amount,2);
                }elseif ($invoice_item->payment_type === 'tuition'){
                    $child_amount[$invoice_item->childid]['tuition'] = bcsub($child_amount[$invoice_item->childid]['tuition'],$invoice_item->amount,2);
                }
            }
        }
        foreach($childrenObjs as $child){
            $child_amount[$child->childid]['deposit'] =(float) $child_amount[$child->childid]['deposit'];
            $child_amount[$child->childid]['tuition'] =(float) $child_amount[$child->childid]['tuition'];
            $childData[] = array(
                'id'=>$child->childid,
                'gender'=>$child->gender,
                'birth'=>$child->birthday_search,
                'name'=>$child->getChildName(),
                'classid'=> (int) $child->classid,
                'status'=> $child->status,
                'statusText'=>($child->status>=100)?OA::getChildStatus($child->status):'',
                'age'=> CommonUtils::getAge($child->birthday),
                'nextClassId' => is_null($reserves[$child->childid]) ? 0 : $reserves[$child->childid]->classid,
                'created' => date('Y-m-d', $child->created_timestamp),
                'deposit' => empty($child_amount[$child->childid]['deposit']) ? 0 :1,
                'tuition' => empty($child_amount[$child->childid]['tuition']) ? 0 : 1,
            );
        }
//        var_dump($childData);die;
        return $childData;
    }
    //new 1.1 ajax获取学生的信息
    public function actionGetStudentsWithSwitchInfo(){
        $res = CommonUtils::requestDsOnline('student/getStudentsWithSwitchInfo', array(
            'school_id' => $this->branchId,
        ));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    public function actionGetTransitionData(){
        $startyear = Yii::app()->request->getParam('startyear');
        $res = CommonUtils::requestDsOnline('student/getDsSwitchInfo', array(
            'school_id' => $this->branchId,
            'startyear' => $startyear,
        ));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }


    public function actionGetDsTransitionComparisonStatus(){
        $days = Yii::app()->request->getParam('days',array());
        $res = CommonUtils::requestDsOnline('student/getDsTransitionComparisonStatus', array(
            'school_id' => $this->branchId,
            'days' => $days,
        ));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    public function actionSetTransitionStatus(){
        $startyear = Yii::app()->request->getParam('startyear');
        $childid = Yii::app()->request->getParam('childid');
        $status = Yii::app()->request->getParam('status');
        $memo = Yii::app()->request->getParam('memo');
        $skip = Yii::app()->request->getParam('skip');
        $res = CommonUtils::requestDsOnline('student/setTransitionStatus', array(
            'school_id' => $this->branchId,
            'startyear' => $startyear,
            'childid' => $childid,
            'status' => $status,
            'skip' => $skip,
            'action' => 'status_change',
        ));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    public function actionCancelTransitionStatus(){
        $startyear = Yii::app()->request->getParam('startyear');
        $childid = Yii::app()->request->getParam('childid');
        $memo = Yii::app()->request->getParam('memo');
        $res = CommonUtils::requestDsOnline('student/setTransitionStatus', array(
            'school_id' => $this->branchId,
            'startyear' => $startyear,
            'childid' => $childid,
            'status' => 0,
            'memo' => $memo,
            'action' => 'status_cancel',
        ));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    public function actionConfirmTransitionStatus(){
        $startyear = Yii::app()->request->getParam('startyear');
        $childid = Yii::app()->request->getParam('childid');
        $status = Yii::app()->request->getParam('status');
        $memo = Yii::app()->request->getParam('memo');
        $skip = Yii::app()->request->getParam('skip');
        $res = CommonUtils::requestDsOnline('student/setTransitionStatus', array(
            'school_id' => $this->branchId,
            'startyear' => $startyear,
            'childid' => $childid,
            'status' => $status,
            'skip' => $skip,
            'memo' => $memo,
            'action' => 'status_confirm',
        ));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();

    }

    public function actionSyncSurveyResult(){
        $startyear = Yii::app()->request->getParam('startyear');
        $res = CommonUtils::requestDsOnline('student/syncSurveyResult', array(
            'school_id' => $this->branchId,
            'startyear' => $startyear,
        ));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();

    }

    //分配学生班级
    public function actionAssignClasses()
    {
        //学生id
        //分配的classid    setGraduated毕业  setDroppedout退学
        $model =  new AssignForm();
        $model->attributes = $_POST['assignData'];
        if ($model->validate()){
            $sClassList = explode('-', $model->classid);
            $criter = new CDbCriteria;
            $criter->compare('childid', $model->childid);
            $criter->compare('schoolid', $this->branchId);
            $criter->index = 'childid';
            $childModel = ChildProfileBasic::model()->findAll($criter);
            if (empty($childModel)){
                $this->addMessage('state','fail');
                $this->addMessage('message', Yii::t('student', '提交的信息有错误.'));
                $this->showMessage();
            }
            if (count($sClassList) == 2){
                $classModel = IvyClass::model()->find('classid=:classid and schoolid=:schoolid',array(':classid'=>$sClassList[1],':schoolid'=>$this->branchId));
                if (empty($classModel)){
                    $this->addMessage('state','fail');
                    $this->addMessage('message', Yii::t('student', '提交的信息有错误.'));
                    $this->showMessage();
                }
                $resutlList = $this->batchAssignClass($childModel,$classModel,$sClassList[0]);
            }else{#已退学+已毕业
                $status = ($sClassList[0]=='setGraduated') ? ChildProfileBasic::STATS_GRADUATED : ChildProfileBasic::STATS_DROPOUT;
                $resutlList = $this->setChildStatus($childModel, '', $status);
            }
            $this->addMessage('data',$resutlList);
            $this->addMessage('state','success');
            $this->addMessage('message',Yii::t('student','保存成功'));
        }else{
            $this->addMessage('state','fail');
            $errs = current($model->getErrors());
            $this->addMessage('message',$errs[0]);
        }
        $this->showMessage();
    }

    //展示预计流失操作记录
    public function actionGetOutflowLog()
    {
        $child_id = Yii::app()->request->getParam('child_id');
        $res = CommonUtils::requestDsOnline('student/getOutflowLog', array(
            'school_id' => $this->branchId,
            'child_id' => $child_id,
        ));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    //操作预计流失
    public function actionSaveOutflow()
    {
        $child_id = Yii::app()->request->getParam('child_id');
        $state = Yii::app()->request->getParam('state');
        $submit_data = Yii::app()->request->getParam('submit_data');
        $res = CommonUtils::requestDsOnline('student/saveOutflow', array(
            'school_id' => $this->branchId,
            'child_id' => $child_id,
            'state' => $state,
            'submit_data' => $submit_data,
        ));

        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }



    /**
     * 批量分班、操作孩子状态
     * @param array $childId    孩子主键        e.g. array(1392)
     * @param int $classId        班级主键        e.g. 100
     * @param string $flag        操作        e.g. currentYid,nextYid
     * @param int $status        状态            e.g. ChildProfileBasic::STATS_ACTIVE,ChildProfileBasic::STATS_GRADUATED,ChildProfileBasic::STATS_DROPOUT
     */
    public function batchAssignClass($childModel, $classModel,$flag=null){
        if (in_array($flag, array('currentYid','nextYid')))
        {
            Yii::import('common.models.portfolio.ChildStudyHistory');
            Yii::import('common.models.invoice.ChildReserve');
            $resultList = array();
            $values = array
                (
                'schoolid' => $classModel->schoolid,
                'classid' => $classModel->classid,
                'calendar' => $classModel->yid,
                'semester' => 0,
                'stat' => ChildProfileBasic::STATS_ACTIVE,
                'userid' => Yii::app()->user->id,
            );
            if ($flag == 'currentYid'){
                foreach ($childModel as $key=>$val) {
                    $historyModel = new ChildStudyHistory();
                    $historyModel->setAttributes($values);
                    $historyModel->childid = $key;
                    $historyModel->timestamp = time();
                    $historyModel->from_classid = 0;
                    if ($historyModel->save()) {
                        $schoolClassModel = ChildClassLink::model()->findByPk($key);
                        if (empty($schoolClassModel)) {
                            $schoolClassModel = new ChildClassLink();
                            $schoolClassModel->childid = $key;
                        }
                        $schoolClassModel->setAttributes($values);
                        $schoolClassModel->hid = $historyModel->hid;
                        $schoolClassModel->updated_timestamp = time();
                        if ($schoolClassModel->save()) {
                            ChildProfileBasic::model()->updateByPk($key, array('classid' => $classModel->classid, 'status' => ChildProfileBasic::STATS_ACTIVE));
                            $resultList[] = array(
                                'childid' => $key,
                                'title' => $classModel->title
                            );
                        } else {
                            $errs = current($schoolClassModel->getErrors());
                            $resultList[] = array(
                                'childid' => $key,
                                'title' => $errs ? $errs[0] : Yii::t('student', '保存信息失败')
                            );
                        }
                    }
                }
            } else {
                ChildReserve::model()->deleteAllByAttributes(array('childid' => array_keys($childModel)));
                foreach ($childModel as $key=>$val) {
                    $historyModel = new ChildStudyHistory();
                    $historyModel->setAttributes($values);
                    $historyModel->childid = $key;
                    $historyModel->timestamp = time();
                    $historyModel->from_classid = 0;
                    if ($historyModel->save()) {
                        $reserveModel = new ChildReserve();
                        $reserveModel->setAttributes($values);
                        $reserveModel->childid = $key;
                        $reserveModel->hid = $historyModel->hid;
                        $reserveModel->updated_timestamp = time();
                        if ($reserveModel->save()) {
                            $resultList[] = array(
                                'childid' => $key,
                                'title' => $classModel->title
                            );
                        } else {
                            $errs = current($reserveModel->getErrors());
                            $resultList[] = array(
                                'childid' => $key,
                                'title' => $errs ? $errs[0] : Yii::t('student', '保存信息失败')
                            );
                        }
                    }
                }
            }
            return $resultList;
        }
    }

    /*
     * 批量设置孩子状态;
     */
    public function setChildStatus($childModel, $classModel, $status){
        $yids = $this->getCalendars();
        $resultList = array();
        $childIds = array_keys($childModel);
        Yii::import('common.models.invoice.Invoice');
        Yii::import('common.models.invoice.InvoiceChildRefund');
        Yii::import('common.models.invoice.InvoiceTransaction');
        Yii::import('application.components.policy.PolicyApi');
        Yii::import('common.models.portfolio.ChildStudyHistory');
        Yii::import('common.models.invoice.ChildConstantTuition');
        Yii::import('common.models.invoice.ChildReserve');
        Yii::import('common.models.invoice.ChildInactiveUnpaid');
        $policyApi = new PolicyApi($this->branchId);
        $unInvoice = $policyApi->getChildUnInvoices($childIds,0,array(FEETYPE_TUITION,FEETYPE_LUNCH,FEETYPE_CHILDCARE,FEETYPE_SCHOOLBUS,FEETYPE_COLLECTION,FEETYPE_AFTERSCHOOL));
        if (!empty($unInvoice)) {
            foreach ($unInvoice as $key => $val) {
//                $resultList[] = array(
//                    'childid' => $key,
//                    'disable' => 0,
//                    'title' => Yii::t('student', '有未付帐单')
//                );
                $unpaidModel = ChildInactiveUnpaid::model()->findByPk($key);
                if (empty($unpaidModel)){
                    $unpaidModel = new ChildInactiveUnpaid();
                    $unpaidModel->childid = $key;
                }
                $unpaidModel->schoolid = $val->schoolid;
                $unpaidModel->created = time();
                $unpaidModel->updated = time();
                $unpaidModel->save();
            }
        }
        //老生老办法表删除
        // ChildConstantTuition::model()->deleteAllByAttributes(array('childid' => $childIds));
        // ChildReserve::model()->deleteAllByAttributes(array('childid' => $childIds));
        if (count($childModel)) {
            foreach ($childModel as $key=>$val) {
                if ($val->status > 0) {
                    $historyModel = new ChildStudyHistory;
                    $historyModel->childid = $key;
                    $historyModel->schoolid = $val->schoolid;
                    $historyModel->classid = $val->classid;
                    $historyModel->calendar = $yids['currentYid'];
                    $historyModel->semester = 0;
                    $historyModel->stat = $status;
                    $historyModel->timestamp = time();
                    $historyModel->userid = Yii::app()->user->id;
                    $historyModel->save();
                    $linkModel = ChildClassLink::model()->findByPk($key);
                    if (!empty($linkModel)) {
                        $linkModel->stat = $status;
                        $linkModel->updated_timestamp = time();
                        $linkModel->userid = Yii::app()->user->id;
                        $linkModel->hid = $historyModel->hid;
                        $linkModel->save();
                    }
                }
                $val->status = $status;
                if ($val->save()) {
                    $resultList[] = array(
                        'childid' => $key,
                        'disable' => 1,
                        'title' => $val->getStatus()
                    );
                } else {
                    $errs = current($val->getErrors());
                    $resultList[] = array(
                        'childid' => $key,
                        'disable' => 0,
                        'title' => $errs ? $errs[0] : Yii::t('student', '保存信息失败')
                    );
                }
            }
        }
        return $resultList;
    }

    //添加兄弟姐们时的查询
    public function actionSibingAllSearch(){
        //todo: 检查权限先
        if(Yii::app()->request->isAjaxRequest && Yii::app()->request->isPostRequest){
            if(!empty($_POST['targetBranchId']) && !empty($_POST['queryText'])){
                $crit = new CDbCriteria();
                $crit->compare('schoolid', $_POST['targetBranchId']);
                $crit->addSearchCondition('tdata',$_POST['queryText']);
                $crit->order = 'id ASC';
                if($this->branchId != $_POST['targetBranchId']){
                    $crit->limit = 5;
                }
                $children = FtsChild::model()->findAll($crit);
                $cData = array();
                if(count($children)){
                    foreach($children as $child){
                        preg_match('/{(.*)}\n*(.*)/s', $child->tdata, $matches);
                        $cData[] = array(
                            'id' => $child->id,
                            'cinfo' => substr($matches['1'],strpos($matches['1'],')')+2),
                            'pinfo' => $matches['2']
                        );
                    }
                }
                $this->addMessage('state','success');
                $this->addMessage('data', $cData);
                $this->showMessage();
            }
        }
        $this->addMessage('state','fail');
        $this->addMessage('message', 'invalid request');
        $this->showMessage();
    }

    /**
     * 添加兄弟姐们时选择孩子返回孩子的信息
     */
    public function actionGetSiblings(){
        if(Yii::app()->request->isAjaxRequest && Yii::app()->request->isPostRequest){
            if(!empty($_POST['childid'])){
                $child = ChildProfileBasic::model()->findByPk(intval($_POST['childid']));
                $pid = ($child->fid) ? $child->fid : $child->mid;
                $parent = IvyParent::model()->findByPk($pid);
                $childIds = unserialize($parent->childs);

                if(count($childIds)>1){
                    $children = ChildProfileBasic::model()->findAllByPk($childIds);
                }else{
                    $children[] = $child;
                }
                $cData = array();
                $cData['fid'] = $child->fid;
                $cData['mid'] = $child->mid;
                foreach($children as $_child){
                    $cData['children'][] = array(
                        'childid' => $_child->childid,
                        'photo' => $_child->photo,
                        'name' => $_child->getChildName(true, true),
                        'dob' => $_child->birthday_search,
                        'gender' => ($_child->gender == 1) ? Yii::t('child','Male') : Yii::t('child','Female')
                    );
                }
                $this->addMessage('state','success');
                $this->addMessage('data', $cData);
                $this->showMessage();
            }
        }
    }

    /**
     * 添加新学生
     */
    public function actionRegister()
    {
        if(Yii::app()->request->isPostRequest){

            $model = new ChildProfileBasic();
            $model->setScenario('regChild');
            $model->attributes = $_POST['ChildProfileBasic'];
            foreach(array('first_name_en', 'middle_name_en', 'last_name_en') as $tag){
                $model->$tag = ucwords($model->$tag);
            }
            $model->gender = isset($_POST['ChildProfileBasic']['gender']) ? $_POST['ChildProfileBasic']['gender'] : '';
            $model->birthday = strtotime($model->birthday_search);
            $model->est_enter_date = strtotime($model->est_enter_date);
            $model->updated_timestamp = time();
            $model->created_timestamp = time();
            $model->create_uid = Yii::app()->user->id;
            //$model->barcode = time().rand(100, 999);

            if(!$model->validate()){
                $this->addMessage('state', 'fail');
                $this->addMessage('error', $model->getErrors());
                $this->addMessage('message', '请填写必填项');
                $this->showMessage();
            }

            $siblings = isset($_POST['siblings']) ? $_POST['siblings'] : 0;
            $_fid = 0;
            $_mid = 0;
            if($siblings){
                $model->save();
                $sibModel = ChildProfileBasic::model()->findByPk($siblings);
                $_fid = $sibModel->fid;
                $_mid = $sibModel->mid;

                if($_fid){
                    $fModel = IvyParent::model()->findByPk($_fid);
                    if($fModel != null && is_object($fModel)){
                        $childrenArr = unserialize($fModel->childs);
                        if(!in_array($model->childid, $childrenArr)){
                            $childrenArr[] = $model->childid;
                            $fModel->childs = serialize($childrenArr);
                            $fModel->save(false);
                        }
                    }
                }

                if($_mid){
                    $mModel = IvyParent::model()->findByPk($_mid);
                    if($mModel != null && is_object($mModel)){
                        $childrenArr = unserialize($mModel->childs);
                        if(!in_array($model->childid, $childrenArr)){
                            $childrenArr[] = $model->childid;
                            $mModel->childs = serialize($childrenArr);
                            $mModel->save(false);
                        }
                    }
                }

                $model->fid = $_fid;
                $model->mid = $_mid;
                $model->family_id = md5($sibModel->childid);
                $model->seniority = $model->getSeniority($model->family_id);
                $model->save();
            }
            else{
                $fatherName = Yii::app()->request->getParam('fatherName', '');
                $fatherEmail = Yii::app()->request->getParam('fatherEmail', '');
                $fatherMobile = Yii::app()->request->getParam('fatherMobile', '');
                $motherName = Yii::app()->request->getParam('motherName', '');
                $motherEmail = Yii::app()->request->getParam('motherEmail', '');
                $motherMobile = Yii::app()->request->getParam('motherMobile', '');

                if(!$fatherName && !$motherName){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '父母的名字至少填一个');
                    $this->showMessage();
                }

                if($fatherEmail || $fatherMobile || $motherEmail || $motherMobile){
                    Yii::import('application.components.user.UserApi');
                    $valid = new CEmailValidator;
                    if($fatherEmail || $fatherMobile || $fatherName){
                        $default_password = CommonUtils::randPass();
                        if(!$fatherEmail && $fatherMobile){
                            $fatherEmail = $fatherMobile;
                            $flagF = 1;
                        }
                        if($fatherEmail){
                            if($flagF != 1 && !$valid->validateValue($fatherEmail)){
                                $this->addMessage('state', 'fail');
                                $this->addMessage('message', '失败');
                                $this->addMessage('callback', 'cbFail');
                                $this->addMessage('data', array('key'=>'fatherEmail', 'info'=>$fatherEmail.' 不是有效的电子邮件地址'));
                                $this->showMessage();
                            }
                            $count = User::model()->countByAttributes(array('email'=>$fatherEmail));
                            if($count){
                                $this->addMessage('state', 'fail');
                                $this->addMessage('message', '失败');
                                $this->addMessage('callback', 'cbFail');
                                $this->addMessage('data', array('key'=>$flagF?'fatherMobile':'fatherEmail', 'info'=>$fatherEmail.' 已经存在 若该家庭不是首次注册，请选择”曾注册过兄弟姐妹“'));
                                $this->showMessage();
                            }
                        }

                        if($fatherMobile){
                            if(!CommonUtils::checkMobile($fatherMobile)){
                                $this->addMessage('state', 'fail');
                                $this->addMessage('message', '失败');
                                $this->addMessage('callback', 'cbFail');
                                $this->addMessage('data', array('key'=>'fatherMobile', 'info'=>$fatherMobile.' 不是有效的手机号码'));
                                $this->showMessage();
                            }
                            $count = IvyParent::model()->countByAttributes(array('mphone'=>$fatherMobile));
                            if($count){
                                $this->addMessage('state', 'fail');
                                $this->addMessage('message', '失败');
                                $this->addMessage('callback', 'cbFail');
                                $this->addMessage('data', array('key'=>'fatherMobile', 'info'=>$fatherMobile.' 已经存在 若该家庭不是首次注册，请选择”曾注册过兄弟姐妹“'));
                                $this->showMessage();
                            }
                        }

                        $model->save();

                        $model->family_id = md5($model->childid);
                        $model->seniority = $model->getSeniority($model->family_id);
                        $model->save();

                        $pModel = new User();
                        $pModel->email = $fatherEmail;
                        $pModel->name = $fatherName;

                        $pModel->iniPassword = $pModel->pass = $default_password;
                        $pModel->mphone = $fatherMobile;
                        $fModel = UserApi::createParent($model->childid, $pModel, 1);
                        $_fid = $fModel->parent->pid;

                        if(isset($_POST['sendmail']) && $_POST['sendmail'] && $fatherEmail){
                            # 发送帐号信息给爸爸
                        }
                    }

                    if($motherEmail || $motherMobile || $motherName){
                        $default_password = CommonUtils::randPass();
                        if(!$motherEmail && $motherMobile){
                            $motherEmail = $motherMobile;
                            $flagM = 1;
                        }

                        if($motherEmail){
                            if($flagM != 1 && !$valid->validateValue($motherEmail)){
                                $this->addMessage('state', 'fail');
                                $this->addMessage('message', '失败');
                                $this->addMessage('callback', 'cbFail');
                                $this->addMessage('data', array('key'=>'fatherEmail', 'info'=>$motherEmail.' 不是有效的电子邮件地址'));
                                $this->showMessage();
                            }
                            $count = User::model()->countByAttributes(array('email'=>$motherEmail));
                            if($count){
                                $this->addMessage('state', 'fail');
                                $this->addMessage('message', '失败');
                                $this->addMessage('callback', 'cbFail');
                                $this->addMessage('data', array('key'=>$flagM?'motherMobile':'motherEmail', 'info'=>$motherEmail.' 已经存在 若该家庭不是首次注册，请选择”曾注册过兄弟姐妹“'));
                                $this->showMessage();
                            }
                        }

                        if($motherMobile){
                            if(!CommonUtils::checkMobile($motherMobile)){
                                $this->addMessage('state', 'fail');
                                $this->addMessage('message', '失败');
                                $this->addMessage('callback', 'cbFail');
                                $this->addMessage('data', array('key'=>'motherMobile', 'info'=>$motherMobile.' 不是有效的电子邮件地址'));
                                $this->showMessage();
                            }
                            $count = IvyParent::model()->countByAttributes(array('mphone'=>$motherMobile));
                            if($count){
                                $this->addMessage('state', 'fail');
                                $this->addMessage('message', '失败');
                                $this->addMessage('callback', 'cbFail');
                                $this->addMessage('data', array('key'=>'motherMobile', 'info'=>$motherMobile.' 已经存在 若该家庭不是首次注册，请选择”曾注册过兄弟姐妹“'));
                                $this->showMessage();
                            }
                        }

                        if($model->isNewRecord){
                            $model->save();

                            $model->family_id = md5($model->childid);
                            $model->seniority = $model->getSeniority($model->family_id);
                            $model->save();
                        }
                        $pModel = new User();
                        $pModel->name = $motherName;
                        $pModel->email = $motherEmail;
                        $pModel->iniPassword = $pModel->pass = $default_password;
                        $pModel->mphone = $motherMobile;
                        $mModel = UserApi::createParent($model->childid, $pModel, 2);
                        $_mid = $mModel->parent->pid;

                        if(isset($_POST['sendmail']) && $_POST['sendmail'] && $motherEmail){
                            # 发送帐号信息给妈妈
                        }
                    }
                }
                else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '父母的邮箱手机四项中至少填一个');
                    $this->showMessage();
                }
            }

            Yii::import('common.models.child.HomeAddress');
            $homeModel = new HomeAddress;
            $homeModel->childid = $model->childid;
            $homeModel->fid = $_fid;
            $homeModel->mid = $_mid;
            $homeModel->family_id = $model->family_id;
            $homeModel->save(false);

            $this->addMessage('state', 'success');
            $this->addMessage('message', '成功!');
            $this->addMessage('callback', 'cbSuccess');
            $this->addMessage('data', array('childid'=>$model->childid, 'name'=>$model->getChildName()));
            $this->showMessage();
        }
    }

    /**
     * 查找同名孩子
     * @param string $name
     */
    public function actionCheckChild($name='')
    {
        if($name){
            $criteria = new CDbCriteria();
            $criteria->compare('name_cn', $name);
            $items = ChildProfileBasic::model()->findAll($criteria);
            $ret = array();
            foreach($items as $item){
                $ret[$item->childid] = array(
                    'name' => $item->name_cn,
                    'schoolabb' => $item->school->abb,
                    'birthday' => $item->birthday_search,
                );
            }
            echo CJSON::encode($ret);
        }
    }

    /**
     * 导出孩子的信息
     */
    public function actionExportChildInfo($category=''){
        Yii::import('common.models.child.*');
        Yii::import('common.models.schoolbus.*');
        Yii::import('common.models.calendar.*');
        Yii::import('common.models.invoice.ChildReserve');
        Yii::import('common.models.invoice.Invoice');
        Yii::import('common.models.invoice.ChildServiceInfo');
        Yii::import('common.models.invoice.DepositHistory');
        Yii::import('common.models.term.*');
        //调取孩子所在国家（全部国家信息有缓存）

        $countryList = Country::model()->getCountryList();
        $calendar = Calendar::model()->findByPk($this->branchObj->schcalendar);

        $getLangList = Term::model()->lang()->findAll();
		$langList = array();
		foreach($getLangList as $v){
            $langList[$v->diglossia_id] = $v->getContent();
		}
        $classids = array();
        $startyear = $calendar->startyear;
        $calendar_id = $calendar->yid;
        $criteria = new CDbCriteria();
        if($category == 'next'){
            $title = Yii::t('site', 'Students of next Year');
            $nextCalendar = CalendarSchool::model()->findByAttributes(array('startyear'=>($calendar->startyear+1), 'branchid'=>$this->branchId));
            if($nextCalendar){
                $startyear = $nextCalendar->startyear;
                $calendar_id = $nextCalendar->yid;
                $models = IvyClass::model()->findAllByAttributes(array('schoolid'=>$this->branchId, 'yid'=>$nextCalendar->yid));
                $classids = CHtml::listData($models, 'classid', 'classid');

                $crit = new CDbCriteria();
                $crit->compare('schoolid', $this->branchId);
                $crit->compare('classid', $classids);
                $crit->addCondition('stat<100 or stat=888');
                $reserveObjs = ChildReserve::model()->findAll($crit);
                $childids = CHtml::listData($reserveObjs, 'childid', 'childid');
                $criteria->compare('t.childid', $childids);
            }
        } elseif ($category == 'newreg') {
            $title = Yii::t('site', 'Newly Registered');
            $models = IvyClass::model()->findAllByAttributes(array('schoolid'=>$this->branchId, 'yid'=>$calendar->yid));
            $classids = CHtml::listData($models, 'classid', 'classid');

            $criteria->compare('t.status', ChildProfileBasic::STATS_REGISTERED);
            $criteria->compare('t.schoolid', $this->branchId);
            $classidsStr = implode(',', $classids);
            $branchId = $this->branchId;
            // 查找过期数据
            $criteria->addCondition("t.status In (10,20) AND t.schoolid = '{$branchId}' AND t.classid NOT IN ($classidsStr)", 'OR');
        } elseif ($category == 'quit'){
            $title = Yii::t('site', '在校生退学');
            $invoiceType = array('tuition');
            $models = IvyClass::model()->findAllByAttributes(array('schoolid'=>$this->branchId, 'yid'=>$calendar->yid));
            $classids = CHtml::listData($models, 'classid', 'classid');

            $criteria->with = 'paid_tuition';
            $criteria->compare('t.status', '>'.ChildProfileBasic::STATS_GRADUATED);
            $criteria->compare('t.schoolid', $this->branchId);
            $criteria->group = 't.childid desc';
        }elseif ($category == 'droppingOut'){
            $title = Yii::t('site', '在校生退学处理中');
            $models = IvyClass::model()->findAllByAttributes(array('schoolid'=>$this->branchId, 'yid'=>$calendar->yid));
            $classids = CHtml::listData($models, 'classid', 'classid');
            $criteria->with = 'paid_tuition';
            $criteria->compare('t.status', '='.ChildProfileBasic::STATS_DROPPINGOUT);
            $criteria->compare('t.schoolid', $this->branchId);
            $criteria->group = 't.childid desc';
        } else{
            $title = Yii::t('site', 'Current Students');
            $models = IvyClass::model()->findAllByAttributes(array('schoolid'=>$this->branchId, 'yid'=>$calendar->yid));
            $classids = CHtml::listData($models, 'classid', 'classid');

//            $criteria->compare('t.status', '<'.ChildProfileBasic::STATS_GRADUATED);
            $criteria->compare('t.status', array(10,20,888));
            $criteria->compare('t.schoolid', $this->branchId);
            $criteria->compare('t.classid', $classids);
        }

        $total = ChildProfileBasic::model()->with('ivyclass','misc','homeaddr')->count($criteria);
        $batchNum = 50;
        $cycle = ceil($total/$batchNum);
        // header("Content-type:application/vnd.ms-excel");
        // header("Content-Disposition:filename=".$this->branchId."_".$title.".xls");

        $data = array();
        $data['title'] = $this->branchId."_". $title . ".xlsx";
        $data['items'][0] = array(
            Yii::t("labels","ID"),
            Yii::t("labels",'Barcode'),
            Yii::t("labels",'Educational Student ID'),
            Yii::t("labels",'中文法定名'),
            Yii::t("labels",'Chinese Name'),
            in_array($this->branchId, array("BJ_DS", "BJ_SLT", "BJ_QFF")) ? Yii::t("labels", 'Legal First Name') : Yii::t("labels",'First Name'),
            in_array($this->branchId, array("BJ_DS", "BJ_SLT", "BJ_QFF")) ? Yii::t("labels", 'Legal Middle Name') : Yii::t("labels",'Middle Name'),
            in_array($this->branchId, array("BJ_DS", "BJ_SLT", "BJ_QFF")) ? Yii::t("labels", 'Legal Last Name') : Yii::t("labels",'Last Name'),
            Yii::t("labels",'Date of Birth'),
            Yii::t("labels",'Preferred Name'),
            Yii::t("labels",'Gender'),
            Yii::t("labels",'Nationality'),
            Yii::t("labels",'Language'),
            Yii::t("labels",'Class'),
            Yii::t("child", 'Balance'),
            Yii::t("labels",'Fapiao Title'),
            Yii::t("labels",'注册时间'),
            Yii::t("labels",'Photo Authorization'),
            Yii::t("labels",'Alternative Lunch Menu'),
            Yii::t("labels",'Vaccination'),
            Yii::t("labels",'Physical Examination'),
            Yii::t("labels",'Residence Location'),
            Yii::t("labels",'Father Name'),
            Yii::t("labels",'Father Email'),
            Yii::t("labels",'Father Mobile'),
            Yii::t("labels",'Primary Contact'),
            Yii::t("labels",'联系语言'),
            Yii::t("labels",'Father Company'),
            Yii::t("labels",'Father Position'),
            Yii::t("labels",'Father') . Yii::t("labels",'ID_card'),
            Yii::t("user",'Father Nation'),
            Yii::t("labels",'Mother Name'),
            Yii::t("labels",'Mother Email'),
            Yii::t("labels",'Mother Mobile'),
            Yii::t("labels",'Primary Contact'),
            Yii::t("labels",'联系语言'),
            Yii::t("labels",'Mother Company'),
            Yii::t("labels",'Mother Position'),
            Yii::t("labels",'Mother') . Yii::t("labels",'ID_card'),
            Yii::t("user",'Mother Nation'),
            Yii::t("campus",'Student start date'),
            Yii::t("labels",'Bus NO.'),
            Yii::t("reg", 'Allergies(food,medications,etc.)'),
            Yii::t("labels",'Address'),
            Yii::t("asa", 'Identity Number'), '入学学年', '已交学费', '已交预交学费',
        );
        if (in_array($this->branchId, array('BJ_DS', 'BJ_SLT'))) {
            $data['items'][0][] = '银行所在城市';
            $data['items'][0][] = '银行名称';
            $data['items'][0][] = '帐户名';
            $data['items'][0][] = '帐户号码';
            $data['items'][0][] = Yii::t("labels", '食物过敏');
            $data['items'][0][] = Yii::t("labels", '药品过敏');
            $data['items'][0][] = Yii::t("labels", '其他过敏');
            $data['items'][0][] = Yii::t("labels", '既往病史');
            $data['items'][0][] = Yii::t("labels", '用药');
            $data['items'][0][] = Yii::t("labels", '医疗免体');
        }
        if($category == 'quit'){
            $data['items'][0] = '退学操作日期';
        }
        $index = 1;
        for ($i=0; $i < $cycle; $i++) {
            $fids = array();
            $mids = array();
            $childids = array();
            $temp = array();

            $criteria->limit = $batchNum;
            $criteria->offset = $i*$batchNum;
            $basicModel = ChildProfileBasic::model()->with('ivyclass','misc','homeaddr')->findAll($criteria);
            if (!empty($basicModel)){
                foreach($basicModel as $model){
                    $fids[$model->fid] = $model->fid;
                    $mids[$model->mid] = $model->mid;
                    $childids[$model->childid] = $model->childid;
                }
                // 查找孩子在当前学年的 service_info 判断孩子有没有交学费
                $crit = new CDbCriteria();
                $crit->compare('childid', $childids);
                $crit->compare('yid', $calendar_id);
                $crit->compare('payment_type', 'tuition');
                $crit->select = 'childid,yid,payment_type';
                $crit->index = 'childid';
                $serviceModels = ChildServiceInfo::model()->findAll($crit);
                $unServiceChildids = array_diff($childids, array_keys($serviceModels));
                // 查找孩子在当前学年的预交学费
                $crit = new CDbCriteria();
                $crit->compare('childid', $unServiceChildids);
                $crit->compare('yid', $calendar_id);
                $crit->select = 'childid,yid,amount,balance,`inout`';
                $depositModels = DepositHistory::model()->findAll($crit);
                $depositArr = array();
                foreach ($depositModels as $depositModel) {
                    if ($depositModel->inout == 'in') {
                        $depositArr[$depositModel->childid] += $depositModel->amount;
                    } else {
                        $depositArr[$depositModel->childid] -= $depositModel->amount;
                    }
                }


                $crite = new CDbCriteria();
                $crite->compare('childid', $childids);
                $crite->compare('startyear', $startyear);
                $buses = SchoolBusChild::model()->with('businfo')->findAll($crite);

                $crite = new CDbCriteria();
                $crite->compare('childid', $childids);
                $crite->compare('calendar_id', $calendar_id);
                $crite->compare('t.schoolid', $this->branchId);
                $crite->compare('t.payment_type', 'bus');
                $crite->compare('t.inout', 'in');
                $crite->compare('t.status', array(Invoice::STATS_PAID,Invoice::STATS_UNPAID,Invoice::STATS_PARTIALLY_PAID));
                $crite->index = 'childid';
                $busInvoices = Invoice::model()->findAll($crite);

                $sbus = array();
                foreach($buses as $bus){
                    if (isset($busInvoices[$bus->childid])) {
                        $sbus[$bus->childid][] = $bus->businfo->bus_title;
                    }
                    $sbus[$bus->childid][] = '';
                }

                //父亲信息
                //$fids = CHtml::listData($basicModel, 'fid', 'fid');
                $crite = new CDbCriteria;
                $crite->select = 't.cn_name,t.en_firstname,t.en_lastname,t.mphone,t.company,t.job,t.country,t.flag,t.language,t.ID_card';
                $crite->compare('pid', $fids);
                $crite->index = 'pid';
                $fInfo = IvyParent::model()->with('ivyUser')->findAll($crite);
                //母亲信息
                //$mids = CHtml::listData($basicModel, 'mid', 'mid');
                $crite = new CDbCriteria;
                $crite->select = 't.cn_name,t.en_firstname,t.en_lastname,t.mphone,t.company,t.job,t.country,t.flag,t.language,t.ID_card';
                $crite->index = 'pid';
                $crite->compare('pid', $mids);
                $mInfo = IvyParent::model()->with('ivyUser')->findAll($crite);

                if (in_array($this->branchId, array('BJ_DS', 'BJ_SLT'))) {
                    // 查找银行信息
                    $crit = new CDbCriteria();
                    $crit->compare('childid', $childids);
                    $crit->index = 'childid';
                    $childBankInfo = ChildBankAccount::model()->findAll($crit);
                    //查询孩子医疗信息
                    $medicalInfo = $this->getChildMedicalInfo(array_values($childids));
                    $childMedicalInfo = array();
                    if($medicalInfo){
                        $childMedicalInfo = $medicalInfo['data'];
                    }
                }
                foreach ($basicModel as $val){
                    $gender = $val->gender == 1 ? Yii::t("safety","M") : Yii::t('safety','F');
                    $photo = $val->misc->agree_photo_open == 1 ? Yii::t('safety','YES') : Yii::t('safety','NO');
                    $allergy = $val->misc->sign_allergy == 1 ?  Yii::t('safety','YES') : Yii::t('safety','NO');
                    $vaccine = $val->misc->vaccine == 1 ?  Yii::t('safety','YES') : Yii::t('safety','NO');
                    $physical = $val->misc->physical == 1 ?  Yii::t('safety','YES') : Yii::t('safety','NO');
                    if(Yii::app()->language == 'zh_cn'){
                        $birthday_search = $val->birthday_search;
                        $est_enter_date = $val->est_enter_date ? date('Y-m-d', $val->est_enter_date) : '';
                        $created_timestamp = date("Y-m-d", $val->created_timestamp);
                    }else{
//                        $birthday_search = OA::formatDateTime($val->birthday,'medium');
                        $birthday_search = $val->birthday_search;
                        $est_enter_date = $val->est_enter_date ? OA::formatDateTime($val->est_enter_date,'medium') : "";
                        $created_timestamp = OA::formatDateTime($val->created_timestamp,'medium');
                    }
                    $temp = array();
                    $temp[] = $val->childid;
                    $temp[] = $val->barcode;
                    $temp[] = $val->educational_id;
                    if ($val->is_legel_cn_name == 1) {
                        $temp[] = Yii::t('safety','YES');
                    }
                    else if ($val->is_legel_cn_name == 2) {
                        $temp[] = Yii::t('safety','NO');
                    }
                    else {
                        $temp[] = '';
                    }
                    $temp[] = trim($val->name_cn);
                    $temp[] = trim($val->first_name_en);
                    $temp[] = trim($val->middle_name_en);
                    $temp[] = trim($val->last_name_en);
                    $temp[] = $birthday_search;
                    $temp[] = $val->nick;
                    $temp[] = $gender;
                    $temp[] = $countryList[$val->country];
                    $temp[] = $langList[$val->lang];
                    if ($category == 'next') {
                        $temp[] = $val->nextYear->ivyclass->title;
                    }else{
                        $temp[] = $val->ivyclass->title;
                    }
                    $temp[] = $val->credit;
                    $temp[] = $val->invoice_title;
                    $temp[] = $created_timestamp;
                    $temp[] = $photo;
                    $temp[] = $allergy;
                    $temp[] = $vaccine;
                    $temp[] = $physical;
                    $temp[] = $val->misc->residence;
                    if (isset($fInfo[$val->fid])  && $val->fid != 0){
                        $temp[] = $fInfo[$val->fid]->getName();
                        $temp[] = " ".$fInfo[$val->fid]->ivyUser->email;
                        $temp[] = $fInfo[$val->fid]->mphone;
                        $temp[] = $fInfo[$val->fid]->getPositionFlag(1) ? Yii::t('safety','YES') : '';
                        $temp[] = $fInfo[$val->fid]->language ? $langList[$fInfo[$val->fid]->language] : '';
                        $temp[] = $fInfo[$val->fid]->company;
                        $temp[] = $fInfo[$val->fid]->job;
                        $temp[] = $fInfo[$val->fid]->ID_card;
                        $temp[] = $countryList[$fInfo[$val->fid]->country];
                    }else{
                        $temp[] = " ";
                        $temp[] = " ";
                        $temp[] = " ";
                        $temp[] = " ";
                        $temp[] = " ";
                        $temp[] = " ";
                        $temp[] = " ";
                        $temp[] = " ";
                        $temp[] = " ";
                    }
                    if (isset($mInfo[$val->mid])  && $val->mid != 0){
                        $temp[] = $mInfo[$val->mid]->getName();
                        $temp[] = " ".$mInfo[$val->mid]->ivyUser->email;
                        $temp[] = $mInfo[$val->mid]->mphone;
                        $temp[] = $mInfo[$val->mid]->getPositionFlag(1) ? Yii::t('safety','YES') : '';
                        $temp[] = $mInfo[$val->mid]->language ? $langList[$mInfo[$val->mid]->language] : '';
                        $temp[] = $mInfo[$val->mid]->company;
                        $temp[] = $mInfo[$val->mid]->job;
                        $temp[] = $mInfo[$val->mid]->ID_card;
                        $temp[] = $countryList[$mInfo[$val->mid]->country];
                    }else{
                        $temp[] = " ";
                        $temp[] = " ";
                        $temp[] = " ";
                        $temp[] = " ";
                        $temp[] = " ";
                        $temp[] = " ";
                        $temp[] = " ";
                        $temp[] = " ";
                        $temp[] = " ";
                    }
                    $temp[] = $est_enter_date;
                    $temp[] = (isset($sbus[$val->childid]) ? implode(',', $sbus[$val->childid]) : "N/A");
                    $temp[] = $val->misc->allergy;

                    $temp[] = $val->homeaddr->en_address;
                    if($val->identity){
                        $temp[] = $val->identity;
                    }else{
                        $temp[] = '';
                    }
                    $temp[] = $val->first_startyear ?  $val->first_startyear .'—'. ($val->first_startyear+1) : '';
                    if (isset($serviceModels[$val->childid])) {
                        $temp[] = Yii::t('safety','YES') ;
                        $temp[] = " ";
                    } elseif (isset($depositArr[$val->childid])) {
                        $temp[] = Yii::t('safety','NO');
                        if ($depositArr[$val->childid] > 0) {
                            $temp[] = Yii::t('safety','YES') ;
                        } else {
                            $temp[] = Yii::t('safety','NO');
                        }
                    } else {
                        $temp[] = Yii::t('safety','NO');
                        $temp[] = Yii::t('safety','NO');
                    }
                    if (in_array($this->branchId, array('BJ_DS', 'BJ_SLT'))) {
                        if (isset($childBankInfo[$val->childid])) {
                            $temp[] = $childBankInfo[$val->childid]->bank_city;
                            $temp[] = $childBankInfo[$val->childid]->bank_name;
                            $temp[] = $childBankInfo[$val->childid]->bank_user;
                            $temp[] = $childBankInfo[$val->childid]->bank_account;
                        } else {
                            $temp[] = "";
                            $temp[] = "";
                            $temp[] = "";
                            $temp[] = "";
                        }
                        //医疗信息
                        $foodAllergyText = '';
                        $medAllergyText = '';
                        $otherAllergyText = '';
                        $medicalHistoryText = '';
                        $special_useText = '';
                        $PEText = '';
                        if(!empty($childMedicalInfo[$val->childid])){
                            $allergy = isset($childMedicalInfo[$val->childid][15]) ? $childMedicalInfo[$val->childid][15] : array();
                            if(!empty($allergy['data']['foodAllergy'])){
                                $foodAllergyText = implode('-',$allergy['data']['foodAllergyDate']).' '.$allergy['data']['foodAllergyDetailCn'].' '.$allergy['data']['foodAllergyDetailEn'];
                            }

                            if(!empty($allergy['data']['medAllergy'])){
                                $medAllergyText = implode('-',$allergy['data']['medAllergyDate']).' '.$allergy['data']['medAllergyDetailCn'].' '.$allergy['data']['medAllergyDetailEn'];
                            }
                            if(!empty($allergy['data']['otherAllergy'])){
                                $otherAllergyText = implode('-',$allergy['data']['otherAllergyDate']).' '.$allergy['data']['otherAllergyDetailCn'].' '.$allergy['data']['otherAllergyDetailEn'];
                            }

                            $medicalHistory =  isset($childMedicalInfo[$val->childid][13]) ? $childMedicalInfo[$val->childid][13] : array();
                            if($medicalHistory){
                                $medicalHistoryText = implode('-',$medicalHistory['data']['medicalHistoryDate']).' '.$medicalHistory['data']['medicalHistoryDetailCn'].' '.$medicalHistory['data']['medicalHistoryDetailEn'];
                            }

                            $special_use = isset($childMedicalInfo[$val->childid][12]) ? $childMedicalInfo[$val->childid][12] : array();
                            if($special_use){
                                $special_useText = implode('-',$special_use['data']['use_days']).' '.$special_use['data']['use_time_cn'].' '.$special_use['data']['use_time_en'];
                            }
                            $PE = isset($childMedicalInfo[$val->childid][14]) ? $childMedicalInfo[$val->childid][14]: array();
                            if($PE){
                                $PEText = implode('-',$PE['data']['exemptionDate']).' '.$PE['data']['reasons_cn'].' '.$PE['data']['reasons_en'];
                            }

                        }

                        $temp[] = $foodAllergyText;
                        $temp[] = $medAllergyText;
                        $temp[] = $otherAllergyText;
                        $temp[] = $medicalHistoryText;
                        $temp[] = $special_useText;
                        $temp[] = $PEText;
                    }
                    if($category == 'quit'){
                        $temp[] = OA::formatDateTime($val->stat->updated_timestamp);
                    }
                    $data['items'][$index] = $temp;
                    $index ++;
                }
            }
        }
        $this->addMessage('state', 'success');
        $this->addMessage('data', $data);
        $this->showMessage();
    }

    public function _t($str='')
    {
        $str = "\"".$str."\"";
        return iconv("UTF-8", "GBK//IGNORE", $str);
    }

    //获取家长和孩子信息
    public function actionGetParent(){
        $childid = Yii::app()->request->getPost('childid');
        $model = ChildProfileBasic::model()->findByPk($childid);
        $countryList = Country::model()->getData();
        $parent = $model->getParents();
        $list['childid'] = $model->childid;
        $list['name'] = $model->getChildName(false,true,true);
        $list['birthday'] = $model->birthday_search;
        $list['gender'] = OA::renderChildGender($model->gender);
        $list['photo'] = CHtml::image(CommonUtils::childPhotoUrl($model->photo), '', array("width"=>80));
        $list['mother']['name'] = isset($parent['mother']) ? $parent['mother']->name : '';
        $list['mother']['email'] = isset($parent['mother']) ? $parent['mother']->email : '';
        $list['mother']['mphone'] = isset($parent['mother']) ? $parent['mother']['parent']->mphone : '';
        $list['father']['name'] = isset($parent['father']) ? $parent['father']->name : '';
        $list['father']['email'] = isset($parent['father']) ?$parent['father']->email : '';
        $list['father']['mphone'] = isset($parent['father']) ?$parent['father']['parent']->mphone : '';
        $this->addMessage('state','success');
        $this->addMessage('message','获取数据成功！');
        $this->addMessage('data',$list);
        $this->showMessage();
    }

    //家长账户
    public function actionGetParentAccount()
    {
        $childIds = Yii::app()->request->getParam('childIds', '');
        $data = array();
        if($childIds){
            $data = $this->GetParent($childIds);
            $this->addMessage('state','success');
            $this->addMessage('message','获取数据成功！');
            $this->addMessage('data',$data);
            $this->showMessage();
        }else{
            $this->addMessage('state','fail');
            $this->addMessage('message','无数据。');
            $this->addMessage('data',$data);
            $this->showMessage();
        }
    }

    //制卡资料
    public function actionCrads()
    {
        Yii::import('common.models.identity.*');
        $childIds = Yii::app()->request->getParam('childIds', '');
        $data = array();
        $dataMother = array();
        $dataFather = array();
        if($childIds){
            $data = $this->GetParent($childIds);
            $waitCrad = array_keys($data);

            $crite = new CDbCriteria;
            $crite->compare('childid', $waitCrad);
            $crite->index = 'childid';
            $childCradArr = Cards::model()->findAll($crite);
            $completeCrad = array_keys($childCradArr);
            $difference = array_diff($waitCrad, $completeCrad);
            $cradData = array();
            if($difference){
                foreach ($difference as $childid) {
                    if (isset($data[$childid])) {
                        $ins = $data[$childid];
                        if($ins['mother']) {
                            $model = new Cards();
                            $model->childid = $ins['childid'];
                            $model->sign = 4;
                            $model->data = CJSON::encode(array('child'=>"", 'campus'=>"", 'class'=>"", 'name'=>"", 'tel'=>"", 'relation'=>"", 'expire'=>"", 'photo'=>"",));
                            $model->updated = time();
                            $model->schoolid = $ins['schooldid'];
                            if ($model->save()) {
                                $dataMother = array(
                                    'child' => $ins['name'],
                                    'campus' => $ins['schooldid'],
                                    'class' => $ins['class'],
                                    'name' => $ins['mother']['name'],
                                    'tel' => substr_replace($ins['mother']['mphone'],'****',3,4),
                                    'relation' => "妈妈",
                                    'expire' => '',
                                    'photo' => $model->id . ".png",
                                );
                                $model->data = CJSON::encode($dataMother);
                                if(!$model->save()) {
                                    Yii::msg(current($model->getErrors()));
                                }
                            }else{
                                Yii::msg(current($model->getErrors()));
                            }
                        }

                        if($ins['father']){
                            $model = new Cards();
                            $model->childid = $ins['childid'];
                            $model->data = CJSON::encode(array('child'=>"", 'campus'=>"", 'class'=>"", 'name'=>"", 'tel'=>"", 'relation'=>"", 'expire'=>"", 'photo'=>"",));
                            $model->sign = 4;
                            $model->updated = time();
                            $model->schoolid = $ins['schooldid'];
                            if($model->save()){
                                $dataFather = array(
                                    'child' => $ins['name'],
                                    'campus' => $ins['schooldid'],
                                    'class' => $ins['class'],
                                    'name' => $ins['father']['name'],
                                    'tel' => substr_replace($ins['father']['mphone'],'****',3,4),
                                    'relation' => "爸爸",
                                    'expire' => '',
                                    'photo' => $model->id . ".png",
                                );
                                $model->data = CJSON::encode($dataFather);
                                if(!$model->save()) {
                                    Yii::msg(current($model->getErrors()));
                                }
                            }else{
                                Yii::msg(current($model->getErrors()));
                            }
                        }
                    }
                }
            }

            $dataA = array();
            $crite = new CDbCriteria;
            $crite->compare('childid', $waitCrad);
            $cradObj = Cards::model()->findAll($crite);
            foreach($cradObj as $item){
                $dataA = CJSON::decode($item->data);
                $cradData[] = array(
                    'childid' => $item->childid,
                    'childName' => $dataA['child'],
                    'campus' => $dataA['campus'],
                    'class' => $dataA['class'],
                    'name' => $dataA['name'],
                    'tel' => substr_replace($dataA['tel'],'****',3,4),
                    'relation' => $dataA['relation'],
                    'photo' => $item->id
                );
            }


            $this->addMessage('state','success');
            $this->addMessage('message','获取数据成功！');
            $this->addMessage('data',$cradData);
            $this->showMessage();
        }else{
            $this->addMessage('state','fail');
            $this->addMessage('message','无数据。');
            $this->addMessage('data',$data);
            $this->showMessage();
        }
    }

    public function GetParent($childIds)
    {
        Yii::import('common.models.wechat.*');
        $data = array();
        //$childIds = Yii::app()->request->getParam('childIds', '');
        if ($childIds) {
            // 获取开发者的 openIds
            $res = CommonUtils::requestDsOnline('devOpenIds/' . 'all');
            $openIds = array();
            if (isset($res['code']) && $res['code'] == 0) {
                $openIds = is_array($res['data']) ? $res['data'] : array() ;
            }
            $childIds = explode(',', $childIds);
            foreach ($childIds as $childId) {
                $model = ChildProfileBasic::model()->findByPk($childId);
                $parent = $model->getParents();
                $crite = new CDbCriteria();
                $crite->compare('userid', array($model->fid,$model->mid));
                $crite->compare('valid', 1);
                $wechatUserObj = WechatUser::model()->with('info')->findAll($crite);

                $wechatUser = array();
                $wechatUserInfo = array();
                foreach($wechatUserObj as $item){
                    $isDev = 0;
                    $nickname = '未授权';
                    $headimgurl = 'https://ea03.joy61.com/non-auth.png';
                    $wechatUser[$item->userid][$item->account][] = $item->account;
                    if ($item->info) {
                        $info = json_decode($item->info->info, true);
                        if (isset($info['nickname']) && $info['nickname']) {
                            $nickname = $info['nickname'];
                        }
                        if (isset($info['headimgurl']) && $info['headimgurl']) {
                            $headimgurl = $info['headimgurl'];
                        }
                    }
                    if (isset($openIds[$item->account])) {
                        if (in_array($item->openid, $openIds[$item->account])) {
                            $isDev = 1;
                        }
                    }
                    $wechatUserInfo[$item->userid][$item->account][] = array(
                        'isDev' => $isDev,
                        'nickname' => $nickname,
                        'headimgurl' => $headimgurl,
                        'openid' => $item->openid,
                    );
                }
                $fmmx = "";
                $fivy = "";
                $fDs = "";
                if(isset($wechatUser) && isset($wechatUser[$model->fid])){
                    if($wechatUser[$model->fid]['mmx']){
                        $fmmx = "ASA:" . count($wechatUser[$model->fid]['mmx']);
                    }
                    if($wechatUser[$model->fid]['ivy']){
                        $fivy = "IVY:" . count($wechatUser[$model->fid]['ivy']);
                    }
                    if($wechatUser[$model->fid]['ds']){
                        $fDs = "DSO:" . count($wechatUser[$model->fid]['ds']);
                    }
                }
                $mmmx = "";
                $mivy = "";
                $mDs = "";
                if(isset($wechatUser) && isset($wechatUser[$model->mid])){
                    if($wechatUser[$model->mid]['mmx']){
                        $mmmx = "ASA: " . count($wechatUser[$model->mid]['mmx']);
                    }
                    if($wechatUser[$model->mid]['ivy']){
                        $mivy = "IVY: " . count($wechatUser[$model->mid]['ivy']);
                    }
                    if($wechatUser[$model->mid]['ds']){
                        $mDs = "DSO: " . count($wechatUser[$model->mid]['ds']);
                    }
                }


                $data[$childId]['father']['fWechat'] = array();
                $data[$childId]['mother']['mWechat'] = array();
                if (isset($wechatUserInfo[$model->fid])) {
                    $data[$childId]['father']['fWechat'] = $wechatUserInfo[$model->fid];
                }
                if (isset($wechatUserInfo[$model->mid])) {
                    $data[$childId]['mother']['mWechat'] = $wechatUserInfo[$model->mid];
                }

                $data[$childId]['childid'] = $model->childid;
                $data[$childId]['name'] = $model->getChildName(false,true,true);
                $data[$childId]['schooldid'] = $this->branchObj->abb;
                $data[$childId]['class'] = $model->ivyclass->title;
                $data[$childId]['gender'] = $model->gender;
                $data[$childId]['photo'] = CHtml::image(CommonUtils::childPhotoUrl($model->photo), '', array("width"=>80,'class'=>'img-circle'));
                $data[$childId]['mother']['pid'] = isset($parent['mother']) ? $parent['mother']['uid'] : '';
                $data[$childId]['mother']['name'] = isset($parent['mother']) ? $parent['mother']['parent']->getName() : '';
                $data[$childId]['mother']['email'] = isset($parent['mother']) ? $parent['mother']->email : '';
                $data[$childId]['mother']['asd'] = $mmmx . " " .$mivy . " ". $mDs;
                $data[$childId]['mother']['userAvatar'] = isset($parent['mother']) ? Yii::app()->urlManager->baseUrl ."/static/userPhoto?uid=".$parent['mother']->uid  : '';
                $data[$childId]['mother']['mphone'] = isset($parent['mother']) ? $parent['mother']['parent']->mphone : '';
                $data[$childId]['mother']['cardmphone'] = (isset($parent['mother']) && count($parent['mother']) > 7) ? substr_replace($parent['mother']['parent']->mphone,'****',3,4) : '';
                $data[$childId]['mother']['lastlogin'] = isset($parent['mother']) ? $parent['mother']->last_login : '';
                $data[$childId]['mother']['regdate'] = isset($parent['mother']) ? $parent['mother']->user_regdate : '';
                $data[$childId]['mother']['primary_contact'] = isset($parent['mother']) ? $parent['mother']->parent->getPositionFlag(1) : '';//位置1是主要联系人
                if (!$data[$childId]['mother']['lastlogin'] || ($data[$childId]['mother']['lastlogin'] - $data[$childId]['mother']['regdate'] == 0)) {
                    $data[$childId]['mother']['loginInfo'] = '<p><span class="glyphicon glyphicon-time"> </span> <span class="text-danger">'.Yii::t('site', 'Never login').'</span></p>';
                } elseif(time()- $data[$childId]['mother']['lastlogin'] <= 3600*30){
                    $data[$childId]['mother']['loginInfo'] = '<p><span class="glyphicon glyphicon-time"> </span> <span>'.Yii::t('site','Last login:').date('Y/m/d H:i', $data[$childId]['mother']['lastlogin']). '</span></p>';
                } else{
                    $data[$childId]['mother']['loginInfo'] = '<p><span class="glyphicon glyphicon-time"> </span> <span class="text-danger">'.Yii::t('site','Last login:').date('Y/m/d H:i', $data[$childId]['mother']['lastlogin']) .'</span></p>';
                }
                $data[$childId]['mother']['avatar'] = isset($parent['mother']) ? CHtml::image(OA::CreateOAUploadUrl('users', $parent['mother']->user_avatar), '', array("width"=>40)) : '';
                $data[$childId]['mother']['pass'] = isset($parent['mother']) ? $parent['mother']->pass : '';
                $data[$childId]['mother']['passwordText'] = isset($parent['mother']) ? $parent['mother']['parent']->password_text : '';
                $data[$childId]['mother']['passChanged'] = $data[$childId]['mother']['pass'] == md5($data[$childId]['mother']['passwordText']) ? '':Yii::t('site', '(Password Changed)');

                $data[$childId]['father']['pid'] = isset($parent['father']) ? $parent['father']['uid'] : '';
                $data[$childId]['father']['name'] = isset($parent['father']) ? $parent['father']['parent']->getName(): '';
                $data[$childId]['father']['email'] = isset($parent['father']) ?$parent['father']->email : '';
                $data[$childId]['father']['asd'] = $fmmx . " " .$fivy . " ". $fDs;
                $data[$childId]['father']['userAvatar'] = isset($parent['father']) ? Yii::app()->urlManager->baseUrl ."/static/userPhoto?uid=".$parent['father']->uid : '';
                $data[$childId]['father']['mphone'] = isset($parent['father']) ?$parent['father']['parent']->mphone : '';
                $data[$childId]['father']['cardmphone'] = (isset($parent['father']) && count($parent['father']) > 7) ? substr_replace($parent['father']['parent']->mphone,'****',3,4) : '';
                $data[$childId]['father']['lastlogin'] = isset($parent['father']) ? $parent['father']->last_login : '';
                $data[$childId]['father']['regdate'] = isset($parent['father']) ? $parent['father']->user_regdate : '';
                $data[$childId]['father']['primary_contact'] = isset($parent['father']) ? $parent['father']->parent->getPositionFlag(1) : '';
                if (!$data[$childId]['father']['lastlogin'] || ($data[$childId]['father']['lastlogin'] - $data[$childId]['father']['regdate'] == 0)) {
                    $data[$childId]['father']['loginInfo'] = '<p><span class="glyphicon glyphicon-time"> </span> <span class="text-danger">'.Yii::t('site', 'Never login').'</span></p>';
                } elseif(time()- $data[$childId]['father']['lastlogin'] <= 3600*30){
                    $data[$childId]['father']['loginInfo'] = '<p><span class="glyphicon glyphicon-time"> </span> <span>'.Yii::t('site','Last login:').date('Y/m/d H:i', $data[$childId]['father']['lastlogin']). '</span></p>';
                } else{
                    $data[$childId]['father']['loginInfo'] = '<p><span class="glyphicon glyphicon-time"> </span> <span class="text-danger">'.Yii::t('site','Last login:').date('Y/m/d H:i', $data[$childId]['father']['lastlogin']) .'</span></p>';
                }
                $data[$childId]['father']['avatar'] = isset($parent['father']) ? CHtml::image(OA::CreateOAUploadUrl('users', $parent['father']->user_avatar), '', array("width"=>40)) : '';
                $data[$childId]['father']['pass'] = isset($parent['father']) ? $parent['father']->pass : '';
                $data[$childId]['father']['passwordText'] = isset($parent['father']) ? $parent['father']['parent']->password_text : '';
                $data[$childId]['father']['passChanged'] = $data[$childId]['father']['pass'] == md5($data[$childId]['father']['passwordText']) ? '':Yii::t('site', '(Password Changed)');
            }
            return $data;
            /*$this->addMessage('state','success');
            $this->addMessage('message','获取数据成功！');
            $this->addMessage('data',$data);
            $this->showMessage();*/
        } else{

            return false;
            /*$this->addMessage('state','fail');
            $this->addMessage('message','无数据。');
            $this->addMessage('data',$data);
            $this->showMessage();*/
        }
    }



    public function actionExportUser(){
        $this->layout = '//layouts/print';
        $this->printFW = $this->branchObj->getPrintHeader();
        $childsClass = Yii::app()->request->getParam('childsClass', 0);

        Yii::import('common.models.wechat.*');
        $status = 100;
        $crite = new CDbCriteria();
        $crite->compare('classid', $childsClass);
        $crite->compare('status', "<{$status}");
        $crite->compare('schoolid', $this->branchId);
        $crite->order = "birthday ASC";
        $childProfileBasicObj = ChildProfileBasic::model()->findAll($crite);

        if($childProfileBasicObj){
            $data = array();
            foreach ($childProfileBasicObj as $childId) {
                $parent = $childId->getParents();
                $crite = new CDbCriteria();
                $crite->compare('userid', array($childId->fid,$childId->mid));
                $crite->compare('valid', 1);
                $wechatUserObj = WechatUser::model()->findAll($crite);

                $wechatUser = array();
                foreach($wechatUserObj as $item){
                    $wechatUser[$item->userid][$item->account][] = $item->account;
                }
                $fmmx = "";
                $fivy = "";
                $fDs = "";
                if(isset($wechatUser) && isset($wechatUser[$childId->fid])){
                    if($wechatUser[$childId->fid]['mmx']){
                        $fmmx = "ASA:" . count($wechatUser[$childId->fid]['mmx']);
                    }
                    if($wechatUser[$childId->fid]['ivy']){
                        $fivy = "IVY:" . count($wechatUser[$childId->fid]['ivy']);
                    }
                    if($wechatUser[$childId->fid]['ds']){
                        $fDs = "DSO:" . count($wechatUser[$childId->fid]['ds']);
                    }
                }
                $mmmx = "";
                $mivy = "";
                $mDs = "";
                if(isset($wechatUser) && isset($wechatUser[$childId->mid])){
                    if($wechatUser[$childId->mid]['mmx']){
                        $mmmx = "ASA: " . count($wechatUser[$childId->mid]['mmx']);
                    }
                    if($wechatUser[$childId->mid]['ivy']){
                        $mivy = "IVY: " . count($wechatUser[$childId->mid]['ivy']);
                    }
                    if($wechatUser[$childId->mid]['ds']){
                        $mDs = "DSO: " . count($wechatUser[$childId->mid]['ds']);
                    }
                }
                $data[$childId->childid]['childid'] = $childId->childid;
                $data[$childId->childid]['name'] = $childId->getChildName(false,true,true);

                $data[$childId->childid]['mother']['name'] = isset($parent['mother']) ? $parent['mother']['parent']->getName() : '';
                $data[$childId->childid]['mother']['email'] = isset($parent['mother']) ? $parent['mother']->email : '';
                $data[$childId->childid]['mother']['asd'] = $mmmx . " " .$mivy . " ". $mDs;
                $data[$childId->childid]['mother']['mphone'] = isset($parent['mother']) ? $parent['mother']['parent']->mphone : '';
                $data[$childId->childid]['mother']['lastlogin'] = isset($parent['mother']) ? $parent['mother']->last_login : '';
                $data[$childId->childid]['mother']['regdate'] = isset($parent['mother']) ? $parent['mother']->user_regdate : '';
                if (!$data[$childId->childid]['mother']['lastlogin'] || ($data[$childId->childid]['mother']['lastlogin'] - $data[$childId->childid]['mother']['regdate'] == 0)) {
                    $data[$childId->childid]['mother']['loginInfo'] = '<p><span class="text-danger">'.Yii::t('site', 'Never login').'</span></p>';
                } elseif(time()- $data[$childId->childid]['mother']['lastlogin'] <= 3600*30){
                    $data[$childId->childid]['mother']['loginInfo'] = '<p><span>'.Yii::t('site','Last login:').date('Y/m/d H:i', $data[$childId->childid]['mother']['lastlogin']). '</span></p>';
                } else{
                    $data[$childId->childid]['mother']['loginInfo'] = '<p><span class="text-danger">'.Yii::t('site','Last login:').date('Y/m/d H:i', $data[$childId->childid]['mother']['lastlogin']) .'</span></p>';
                }
               $data[$childId->childid]['mother']['pass'] = isset($parent['mother']) ? $parent['mother']->pass : '';
                $data[$childId->childid]['mother']['passwordText'] = isset($parent['mother']) ? $parent['mother']['parent']->password_text : '';
                $data[$childId->childid]['mother']['passChanged'] = $data[$childId->childid]['mother']['pass'] == md5($data[$childId->childid]['mother']['passwordText']) ? '':Yii::t('site', '(Password Changed)');

                $data[$childId->childid]['father']['name'] = isset($parent['father']) ? $parent['father']['parent']->getName(): '';
                $data[$childId->childid]['father']['email'] = isset($parent['father']) ?$parent['father']->email : '';
                $data[$childId->childid]['father']['asd'] = $fmmx . " " . $fivy . " ". $fDs;
                $data[$childId->childid]['father']['mphone'] = isset($parent['father']) ?$parent['father']['parent']->mphone : '';
                $data[$childId->childid]['father']['lastlogin'] = isset($parent['father']) ? $parent['father']->last_login : '';
                $data[$childId->childid]['father']['regdate'] = isset($parent['father']) ? $parent['father']->user_regdate : '';
                if (!$data[$childId->childid]['father']['lastlogin'] || ($data[$childId->childid]['father']['lastlogin'] - $data[$childId->childid]['father']['regdate'] == 0)) {
                    $data[$childId->childid]['father']['loginInfo'] = '<p><span class="text-danger">'.Yii::t('site', 'Never login').'</span></p>';
                } elseif(time()- $data[$childId->childid]['father']['lastlogin'] <= 3600*30){
                    $data[$childId->childid]['father']['loginInfo'] = '<p><span>'.Yii::t('site','Last login:').date('Y/m/d H:i', $data[$childId->childid]['father']['lastlogin']). '</span></p>';
                } else{
                    $data[$childId->childid]['father']['loginInfo'] = '<p><span class="text-danger">'.Yii::t('site','Last login:').date('Y/m/d H:i', $data[$childId->childid]['father']['lastlogin']) .'</span></p>';
                }
               $data[$childId->childid]['father']['pass'] = isset($parent['father']) ? $parent['father']->pass : '';
                $data[$childId->childid]['father']['passwordText'] = isset($parent['father']) ? $parent['father']['parent']->password_text : '';
                $data[$childId->childid]['father']['passChanged'] = $data[$childId->childid]['father']['pass'] == md5($data[$childId->childid]['father']['passwordText']) ? '':Yii::t('site', '(Password Changed)');
            }
            $classeTitle = IvyClass::model()->findByPk($childsClass);
            $this->render('student/exportParent',array('date' => $data, 'classeTitle' => $classeTitle));
        }
    }

    // 导出家长信息
    public function actionExportParent()
    {
        $childIds = Yii::app()->request->getParam('childIds', '');
        $data = array();
        if ($childIds) {
            Yii::import('common.models.calendar.*');
            Yii::import('common.models.schoolbus.*');
            // header("Content-type:application/vnd.ms-excel");
            // header("Content-Disposition:filename=".$this->branchId."_家长信息.xls");
            $data['title'] = $this->branchId . "_孩子名单.xlsx";
            if(in_array($this->branchId, array("BJ_DS", "BJ_SLT", "BJ_QFF"))) {
                $data['items'][0] = array(
                    Yii::t("labels", "ID"),
                    Yii::t("labels", 'Chinese Name'),
                    Yii::t("labels", 'Legal First Name'),
                    Yii::t("labels", 'Legal Middle Name'),
                    Yii::t("labels", 'Legal Last Name'),
                    Yii::t("labels", 'Preferred Name'),
                    Yii::t("labels", 'Nationality'),
                    Yii::t("labels", 'Date of Birth'),
                    Yii::t("labels", 'Gender'),
                    Yii::t("labels", 'Bus NO.'),
                    Yii::t("labels", 'Bus Notes'),
                    Yii::t("labels", 'Father Name'),
                    Yii::t("labels", 'Father Email'),
                    Yii::t("labels", 'Father Mobile'),
                    Yii::t("labels", '联系语言'),
                    Yii::t("labels", 'Primary Contact'),
                    Yii::t("labels", 'Mother Name'),
                    Yii::t("labels", 'Mother Email'),
                    Yii::t("labels", 'Mother Mobile'),
                    Yii::t("labels", '联系语言'),
                    Yii::t("labels", 'Primary Contact'),
                );
                if(in_array($this->branchId, array("BJ_DS", "BJ_SLT"))){
                    $medical_fields = array(
                        Yii::t("labels", '食物过敏'),
                        Yii::t("labels", '药品过敏'),
                        Yii::t("labels", '其他过敏'),
                        Yii::t("labels", '既往病史'),
                        Yii::t("labels", '用药'),
                        Yii::t("labels", '医疗免体')
                    );
                    $data['items'][0] = array_merge($data['items'][0],$medical_fields);
                }

            }else {
                $data['items'][0] = array(
                    Yii::t("labels", "ID"),
                    Yii::t("labels", 'Chinese Name'),
                    Yii::t("labels", 'First Name'),
                    Yii::t("labels", 'Middle Name'),
                    Yii::t("labels", 'Last Name'),
                    Yii::t("labels", 'Date of Birth'),
                    Yii::t("labels", 'Gender'),
                    Yii::t("labels", 'Bus NO.'),
                    Yii::t("labels", 'Bus Notes'),
                    Yii::t("labels", 'Father Name'),
                    Yii::t("labels", 'Father Email'),
                    Yii::t("labels", 'Father Mobile'),
                    Yii::t("labels", '联系语言'),
                    Yii::t("labels", 'Primary Contact'),
                    Yii::t("labels", 'Mother Name'),
                    Yii::t("labels", 'Mother Email'),
                    Yii::t("labels", 'Mother Mobile'),
                    Yii::t("labels", '联系语言'),
                    Yii::t("labels", 'Primary Contact'),
                );
            }

            $childIds = explode(',', $childIds);
            $childModel = ChildProfileBasic::model()->findAllByPk($childIds);

            $country = Country::model()->getCountryList();
            $crite = new CDbCriteria();
            $crite->compare('t.is_selected', 1);
            $crite->compare('t.branchid', $this->branchId);
            $calendarModel = CalendarSchool::model()->find($crite);

            $crite = new CDbCriteria();
            $crite->compare('t.startyear', $calendarModel->startyear);
            $crite->compare('t.childid', $childIds);
            $crite->compare('t.schoolid', $this->branchId);
            $crite->index = 'childid';
            $schoolBusList = SchoolBusChild::model()->findAll($crite);
            if(in_array($this->branchId, array("BJ_DS", "BJ_SLT"))){
                $medicalInfo = $this->getChildMedicalInfo($childIds);
                $childMedicalInfo = array();
                if($medicalInfo){
                    $childMedicalInfo = $medicalInfo['data'];
                }
            }
            if ($childModel[0]->ivyclass->title) {
                $classeTitle = $childModel[0]->ivyclass->title;
                $data['title'] = $this->branchId .'_'. $classeTitle . "_孩子名单.xlsx";
            }
            $gender = array(1 => Yii::t("safety","M"), 2 => Yii::t("safety","F"));
            $langList = Term::model()->getLangList();
            if ($childModel) {
                foreach ($childModel as $i => $model) {
                    if(Yii::app()->language == 'zh_cn'){
                        $birthday = $model->birthday_search;
                    }else{
//                        $birthday = OA::formatDateTime($model->birthday,'medium');
                        $birthday = $model->birthday_search;
                    }
                    $parent = $model->getParents();
                    $array = array();
                    $array[] = $model->childid;
                    $array[] = trim($model->name_cn);
                    $array[] = trim($model->first_name_en);
                    $array[] = trim($model->middle_name_en);
                    $array[] = trim($model->last_name_en);
                    if(in_array($this->branchId, array("BJ_DS", "BJ_SLT", "BJ_QFF"))){
                        $array[] = $model->nick;
                        $array[] = $country[$model->country];
                    }
                    $array[] = $birthday;
                    $array[] = $gender[$model->gender];
                    $array[] = ($schoolBusList && $schoolBusList[$model->childid]) ? $schoolBusList[$model->childid]->businfo->bus_title  : '' ;
                    $array[] = ($schoolBusList && $schoolBusList[$model->childid]) ? nl2br($schoolBusList[$model->childid]->remark)  : '' ;

                    $array[] = isset($parent['father']) ? $parent['father']['parent']->getName() : '';
                    $array[] = isset($parent['father']) ? $parent['father']->email : '';
                    $array[] = isset($parent['father']) ? $parent['father']['parent']->mphone : '';
                    $array[] = isset($parent['father']) ? $langList[$parent['father']['parent']->language] : '';
                    $array[] = isset($parent['father']) ? ($parent['father']['parent']->getPositionFlag(1) == 1 ? Yii::t('safety','YES') : '' ) : '';

                    $array[] = isset($parent['mother']) ? $parent['mother']['parent']->getName() : '';
                    $array[] = isset($parent['mother']) ? $parent['mother']->email : '';
                    $array[] = isset($parent['mother']) ? $parent['mother']['parent']->mphone : '';
                    $array[] = isset($parent['mother']) ? $langList[$parent['mother']['parent']->language] : '';
                    $array[] = isset($parent['mother']) ? ($parent['mother']['parent']->getPositionFlag(1) == 1 ? Yii::t('safety','YES') : '') : '';

                    if(in_array($this->branchId, array("BJ_DS", "BJ_SLT"))){
                        $foodAllergyText = '';
                        $medAllergyText = '';
                        $otherAllergyText = '';
                        $medicalHistoryText = '';
                        $special_useText = '';
                        $PEText = '';
                        if(!empty($childMedicalInfo[$model->childid])){
                            $allergy = isset($childMedicalInfo[$model->childid][15]) ? $childMedicalInfo[$model->childid][15] : array();
                            if(!empty($allergy['data']['foodAllergy'])){
                                $foodAllergyText = implode('-',$allergy['data']['foodAllergyDate']).' '.$allergy['data']['foodAllergyDetailCn'].' '.$allergy['data']['foodAllergyDetailEn'];
                            }
                            if(!empty($allergy['data']['medAllergy'])){
                                $medAllergyText = implode('-',$allergy['data']['medAllergyDate']).' '.$allergy['data']['medAllergyDetailCn'].' '.$allergy['data']['medAllergyDetailEn'];
                            }
                            if(!empty($allergy['data']['otherAllergy'])){
                                $otherAllergyText = implode('-',$allergy['data']['otherAllergyDate']).' '.$allergy['data']['otherAllergyDetailCn'].' '.$allergy['data']['otherAllergyDetailEn'];
                            }
                            $medicalHistory =  isset($childMedicalInfo[$model->childid][13]) ? $childMedicalInfo[$model->childid][13] : array();
                            if($medicalHistory){
                                $medicalHistoryText = implode('-',$medicalHistory['data']['medicalHistoryDate']).' '.$medicalHistory['data']['medicalHistoryDetailCn'].' '.$medicalHistory['data']['medicalHistoryDetailEn'];
                            }
                            $special_use = isset($childMedicalInfo[$model->childid][12]) ? $childMedicalInfo[$model->childid][12] : array();
                            if($special_use){
                                $special_useText = implode('-',$special_use['data']['use_days']).' '.$special_use['data']['use_time_cn'].' '.$special_use['data']['use_time_en'];
                            }
                            $PE = isset($childMedicalInfo[$model->childid][14]) ? $childMedicalInfo[$model->childid][14]: array();
                            if($PE){
                                $PEText = implode('-',$PE['data']['exemptionDate']).' '.$PE['data']['reasons_cn'].' '.$PE['data']['reasons_en'];
                            }
                        }
                        $array[] = $foodAllergyText;//食物过敏 15 foodAllergy ==true foodAllergyDate foodAllergyDetailCn foodAllergyDetailEn
                        $array[] = $medAllergyText;//药品过敏 15 medAllergy==true medAllergyDate  medAllergyDetailCn  medAllergyDetailEn
                        $array[] = $otherAllergyText;//其他过敏 15  otherAllergy==true otherAllergyDate  otherAllergyDetailCn otherAllergyDetailEn
                        $array[] = $medicalHistoryText;//既往病史 13  medicalHistory==2  medicalHistoryDate medicalHistoryDetailCn medicalHistoryDetailEn
                        $array[] = $special_useText;//用药 12  use_days use_time_cn use_time_en
                        $array[] = $PEText;//医疗免体 14  exemptionDate reasons_cn reasons_en
                    }

                    $data['items'][$i + 1] = $array;
                }
            }
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
            $this->showMessage();
        }
    }

    public function actionChildInfo()
    {
        $childInfo = $this->getChildInfo2();
        $this->addMessage('state', 'success');
        $this->addMessage('data', $childInfo);
        $this->showMessage();
    }

    //老师权限查看学生信息
    public function actionChildWindowInfo()
    {
        $childInfo = $this->getChildInfo2();
        //将医疗相关标签分到一组
        $childInfo['labelGroup'] = array();
        $nurseMedicalFlag = array(12, 13, 14, 15);
        foreach ($childInfo['label'] as $k=>$item){
            if(in_array($item['flag'],$nurseMedicalFlag)){
                $childInfo['labelGroup']['nurseMedical'][] = $item;
                unset($childInfo['label'][$k]);
            }
        }
        $this->renderPartial('student/childWindowInfo',array('data' => $childInfo,));
    }


    public function getChildInfo2(){
        Yii::import('common.models.calendar.*');
        Yii::import('common.models.schoolbus.*');
        Yii::import('common.models.timetable.*');
        $childid = Yii::app()->request->getParam('childid', '');

        if(!$childid){
            $this->addMessage('state', 'fail');
            $this->addMessage('data', 'fail');
            $this->showMessage();
        }
        $childModel = ChildProfileBasic::model()->findByPk($childid);

        $criteria =new CDbCriteria;
        $criteria->compare('branchid',$this->branchId);
        $criteria->compare('is_selected',1);
        $calendarModel = CalendarSchool::model()->find($criteria);

        $crite = new CDbCriteria;
        $crite->compare('t.startyear', $calendarModel->startyear);
        $crite->compare('t.schoolid', $this->branchId);
        $crite->compare('t.childid', $childid);
        $crite->order = 't.time1 ASC';
        $childByschoolbus = SchoolBusChild::model()->find($crite);
        $parentInfo = $childModel->getParents();
        $countryList = Country::model()->getCountryList();

        $crite = new CDbCriteria;
        $crite->compare('status',1);
        $timetableInfo = Timetable::model()->find($crite);
        $tid = $timetableInfo->id;
        $res = CommonUtils::requestDsOnline('child/wx/receivers?child_id='.$childid);
        $childInfo = array(
            "id" => $childModel->childid,
            "name" => $childModel->name_cn,
            "photo" => CommonUtils::childPhotoUrl($childModel->photo),
            "first_name_en" => $childModel->first_name_en,
            "middle_name_en" => $childModel->middle_name_en,
            "last_name_en" => $childModel->last_name_en,
            "nick" => $childModel->nick,
            "country" => $countryList[$childModel->country],
            "birthday_search" => $childModel->birthday_search,
            "gender" => $childModel->gender,
            "label"=>$childModel->getLabel(),
            "busId" => $childByschoolbus ? $childByschoolbus->businfo->bus_title : "",
            "BusContent" => $childByschoolbus ? $childByschoolbus->businfo->bus_type : "",
            "fName" => isset($parentInfo["father"]) ? $parentInfo["father"]->getName() : '',
            "fTel" => isset($parentInfo["father"]) ? $parentInfo["father"]->parent->mphone : '',
            "fEmail" => isset($parentInfo["father"]) ? $parentInfo["father"]->email : '',
            "fFlag" =>isset($parentInfo["father"]) && !empty($parentInfo["father"]->parent) ? $parentInfo["father"]->parent->getParentFlag() : array(),
            "mName" => isset($parentInfo["mother"]) ? $parentInfo["mother"]->getName() : '',
            "mTel" => isset($parentInfo["mother"]) ? $parentInfo["mother"]->parent->mphone : '',
            "mEmail" => isset($parentInfo["mother"]) ? $parentInfo["mother"]->email : '',
            "mFlag" =>isset($parentInfo["mother"])&& !empty($parentInfo["mother"]->parent) ? $parentInfo["mother"]->parent->getParentFlag() : array(),
            "wxReceivers" => $res['code'] == 0 ? $res['data'] : array(),
            "homeUrl" => OA::genCCUrlHome($childModel->childid),
            "academicCenterUrl" => $this->createUrl('/mteaching/learnInfo/index',array('childid'=>$childModel->childid)),
            "tid"=>$tid,
            'isMiddleSchool' => CommonUtils::isMiddleSchool($childModel->classid)
        );
        return $childInfo;
    }

    public function actionExportReports()
    {
        $this->layout = '//layouts/print';
        $this->printFW = $this->branchObj->getPrintHeader();
        $childIds = Yii::app()->request->getParam('childIds', '');

        if($childIds){
            $childIds = explode(',', $childIds);
            $criteria =new CDbCriteria;
            $criteria->compare('childid',$childIds);
            $criteria->order = 'birthday ASC';
            $childModel = ChildProfileBasic::model()->findAll($criteria);

            $child = array();
            foreach($childModel as $k=>$item){
                $child[$k]['id'] = $item->classid;
                $child[$k]['name'] = $item->getChildName();
                $child[$k]['childids'] = sprintf("%06d", $item->childid);
            }
            if($child){
                $classID = IvyClass::model()->findByPk($child[0]['id']);
            }

            $this->render('student/print', array(
                'child' => $child,
                'classID' => $classID,
            ));
        }

    }

    public function actionChildsData()
    {
        $this->layout = '//layouts/print';
        $this->printFW = $this->branchObj->getPrintHeader();
        $childIds = Yii::app()->request->getParam('childIds', '');
        print_r($childIds);exit;
    }

    public function actionChildCards()
    {
        header("content-type:text/html;charset=utf-8");
        $this->layout = '//layouts/column3';
        $this->printFW = $this->branchObj->getPrintHeader();
        $childIds = Yii::app()->request->getParam('childIds', '');
        $gradeGroupList = IvyClass::getClassToGroup();
        $gradeGroupTitle = array(
            'KG' => array('cn' => '幼儿园部', 'en' => 'Kindergarten School'),
            'ES' => array('cn' => '小学部', 'en' => 'Elementary School'),
            'MS' => array('cn' => '初中部', 'en' => 'Middle School'),
            'HS' => array('cn' => '高中部', 'en' => 'High School'),
        );
        $data = array();
        if($childIds){
            $childIds = explode(',', $childIds);
            $criteria =new CDbCriteria;
            $criteria->compare('childid',$childIds);
            $criteria->order = 'birthday ASC';
            $childModel = ChildProfileBasic::model()->findAll($criteria);

            foreach($childModel as $k=>$item){
                $data[] = array(
                    'id' => $item->childid,
                    'class' => $item->ivyclass->title,
                    'grade_group_cn' => $gradeGroupTitle[$gradeGroupList[$item->ivyclass->classtype]]['cn'],
                    'grade_group_en' => $gradeGroupTitle[$gradeGroupList[$item->ivyclass->classtype]]['en'],
                    'cn_name' => $item->name_cn,
                    'nick' => $item->nick,
                    'last_name_en' => $item->last_name_en,
                    'en_name' => trim(sprintf("%s %s", trim($item->first_name_en), trim($item->last_name_en))),
                    'photo' => CommonUtils::childPhotoUrl($item->photo, 'big'),
                );
            }

            $this->render('student/childcards', array('data' => $data));
        }
    }

    public function actionFamilyInfo()
    {
        Yii::import('common.models.child.*');
        Yii::import('common.models.calendar.*');
        $calendar = Calendar::model()->findByPk($this->branchObj->schcalendar);
        $criteria = new CDbCriteria();
        $classModels = IvyClass::model()->findAllByAttributes(array('schoolid' => $this->branchId, 'yid' => $calendar->yid));
        $classData = array();
        $classids = array();
        foreach ($classModels as $classModel) {
            $classId = $classModel->classid;
            $classids[$classId] = $classId;
            $classData[$classId] = array(
                'title' => $classModel->title,
                'gradeGroup' => $this->getClassGradeGroup($classModel->classtype),
            );
        }

        $criteria->compare('t.status', array(10, 20, 888));
        $criteria->compare('t.schoolid', $this->branchId);
        $criteria->compare('t.classid', $classids);
        $childModels = ChildProfileBasic::model()->findAll($criteria);

        $childids = array();
        $childData = array();
        foreach ($childModels as $childModel) {
            $fid = $childModel->fid;
            $mid = $childModel->mid;
            $childId = $childModel->childid;
            $classId = $childModel->classid;
            $fids[$fid] = $fid;
            $mids[$mid] = $mid;
            $familyKey = md5($fid . $mid);
            $childids[$childId] = $childId;
            if (!isset($childData[$familyKey])) {
                $childData[$familyKey] = array(
                    'fid' => $fid,
                    'mid' => $mid,
                    'children' => array(),
                );
            }
            $class = $classData[$classId];
            $childData[$familyKey]['children'][] = array(
                'id' => $childModel->childid,
                'photo' => CommonUtils::childPhotoUrl($childModel->photo, 'small'),
                'birth' => $childModel->birthday_search,
                'name' => CHtml::encode($childModel->getChildName()),
                'classTitle' => $class['title'],
                'gradeGroup' => $class['gradeGroup'],
                'status' => $childModel->status,
                'gender' => $childModel->gender,
                'eduId' => $childModel->educational_id,
                'school_id' => $childModel->schoolid,
                'school_other' => 0,
                'school_abb' => $this->branchObj->abb,
            );
        }
        // 根据 fids mids 查找其它学校的兄弟姐妹
        $pids = array_unique($fids + $mids);
        $parentModels = IvyParent::model()->findAllByPk($pids);
        $otherSchoolChildIds = array();
        foreach ($parentModels as $parentModel) {
            $parentChildids = unserialize($parentModel->childs);
            if ($parentChildids && count($parentChildids) > 0) {
                foreach ($parentChildids as $parentChildid) {
                    if (!in_array($parentChildid, $childids)) {
                        $otherSchoolChildIds[$parentChildid] = $parentChildid;
                    }
                }
            }
        }
        if ($otherSchoolChildIds) {
            $otherSchoolChildIds = array_unique($otherSchoolChildIds);
            $criteria = new CDbCriteria();
            $criteria->compare('t.status', array(10, 20, 888));
            $criteria->compare('t.childid', $otherSchoolChildIds);
            $criteria->with = array('ivyclass', 'school');
            $otherSchoolChildModels = ChildProfileBasic::model()->findAll($criteria);
            foreach ($otherSchoolChildModels as $otherSchoolChildModel) {
                if ($otherSchoolChildModel->ivyclass) {
                    $familyKey = md5($otherSchoolChildModel->fid . $otherSchoolChildModel->mid);
                    $childData[$familyKey]['children'][] = array(
                        'id' => $otherSchoolChildModel->childid,
                        'photo' => CommonUtils::childPhotoUrl($otherSchoolChildModel->photo, 'small'),
                        'birth' => $otherSchoolChildModel->birthday_search,
                        'name' => CHtml::encode($otherSchoolChildModel->getChildName()),
                        'classTitle' => $otherSchoolChildModel->ivyclass->title,
                        'gradeGroup' => $this->getClassGradeGroup($otherSchoolChildModel->ivyclass->classtype),
                        'status' => $otherSchoolChildModel->status,
                        'gender' => $otherSchoolChildModel->gender,
                        'eduId' => $otherSchoolChildModel->educational_id,
                        'school_id' => $otherSchoolChildModel->schoolid,
                        'school_other' => $otherSchoolChildModel->schoolid != $this->branchId,
                        'school_abb' => $otherSchoolChildModel->school->abb,
                    );
                }
            }
        }

        //父亲信息
        $crite = new CDbCriteria;
        $crite->select = 't.cn_name,t.en_firstname,t.en_lastname,t.mphone,t.company,t.job';
        $crite->compare('pid', $fids);
        $crite->index = 'pid';
        $fInfo = IvyParent::model()->with('ivyUser')->findAll($crite);
        //母亲信息
        $crite = new CDbCriteria;
        $crite->select = 't.cn_name,t.en_firstname,t.en_lastname,t.mphone,t.company,t.job';
        $crite->index = 'pid';
        $crite->compare('pid', $mids);
        $mInfo = IvyParent::model()->with('ivyUser')->findAll($crite);
        // 处理数据
        $esIds = array();
        $ssIds = array();
        $otherIds = array();
        $childNum = 0;
        $esChildNum = 0;
        $ssChildNum = 0;
        $otherChildNum = 0;
        $otherSchoolChildNum = 0;
        $otherSchoolIds = array();
        // 全部在本校就读
        $thisSchoolChildNum = 0;
        $thisSchoolIds = array();
        $idData = array();
        $i = 0;
        foreach ($childData as $k => $v) {
            if (isset($fInfo[$v['fid']])) {
                $childData[$k]['fData'] = array(
                    'id' => $v['fid'],
                    'name' => $fInfo[$v['fid']]->getName(),
                    'email' => " ".$fInfo[$v['fid']]->ivyUser->email,
                    'mphone' => $fInfo[$v['fid']]->mphone,
                );
            }
            if (isset($mInfo[$v['mid']])) {
                $childData[$k]['mData'] = array(
                    'id' => $v['mid'],
                    'name' => $mInfo[$v['mid']]->getName(),
                    'email' => " ".$mInfo[$v['mid']]->ivyUser->email,
                    'mphone' => $mInfo[$v['mid']]->mphone,
                );
            }
            // 只在ES或者只在SS的数据
            $total = count($v['children']);
            if ($total <= 1) {
                unset($childData[$k]);
                continue;
            }
            $esNum = 0;
            $ssNum = 0;
            $isOtherSchool = false;
            foreach ($v['children'] as $child) {
                $idData[$i][] = (int)$child['id'];
                $childNum++;
                if ($child['gradeGroup'] == 'ES') {
                    $esNum++;
                }
                if ($child['gradeGroup'] == 'SS') {
                    $ssNum++;
                }
                // 跨学校就读
                if ($child['school_id'] != $this->branchId) {
                    $isOtherSchool = true;
                }
            }
            if ($isOtherSchool) {
                $otherSchoolIds[] = $k;
                $otherSchoolChildNum += $total;
            } else {
                $thisSchoolIds[] = $k;
                $thisSchoolChildNum += $total;
            }
            if ($total == $esNum) {
                $esIds[] = $k;
                $esChildNum = $esChildNum + $esNum;
            } elseif ($total == $ssNum) {
                $ssIds[] = $k;
                $ssChildNum = $ssChildNum + $ssNum;
            } else {
                $otherIds[] = $k;
                $otherChildNum = $otherChildNum + $esNum + $ssNum;
            }
            $i++;
        }

        $this->renderPartial('familyInfo', array(
            'childData' => $childData,
            'familyNum' => count($childData),
            'childNum' => $childNum,
            'esData' => $esIds,
            'ssData' => $ssIds,
            'otherData' => $otherIds,
            'esFamilyNum' => count($esIds),
            'ssFamilyNum' =>  count($ssIds),
            'otherFamilyNum' =>  count($otherIds),
            'esChildNum' => $esChildNum,
            'ssChildNum' => $ssChildNum,
            'otherChildNum' => $otherChildNum,
            'idData' => $idData,
            'ohterSchoolData' => $otherSchoolIds,
            'otherSchoolFamilyNum' => count($otherSchoolIds),
            'otherSchoolChildNum' => $otherSchoolChildNum,
            'thisSchoolFamilyNum' => count($thisSchoolIds),
            'thisSchoolChildNum' => $thisSchoolChildNum,
            'thisSchoolIds' => $thisSchoolIds,
        ));
    }

    function getClassGradeGroup($classType) {
        if (in_array($classType, array('mk', 'e1', 'e2', 'e3', 'e4', 'e5',))) {
            $gradeGroup = 'ES';
        } elseif (in_array($classType, array('e6', 'e7', 'e8', 'e9', 'e10', 'e11', 'e12',))) {
            $gradeGroup = 'SS';
        } else {
            $gradeGroup = 'KG';
        }
        return $gradeGroup;
    }

    //获取给家长发送的微信消息
    public function actionGetPushMsg()
    {
        $openid = Yii::app()->request->getParam("openid");
        $data = $this->renderPartial('/student/student/WechatMsg',array('openid'=>$openid),true);
        $this->addMessage('state', 'success');
        $this->addMessage('data', $data);
        $this->showMessage();
    }

    public function actionGetWechatPushMsg()
    {
        $openid = Yii::app()->request->getParam("openid");
        //获取推送的信息
        $res = CommonUtils::requestDsOnline('student/getWechatPushMsg', array(
            'openid' => $openid,
        ));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    public function actionGetBindQrcode()
    {
        Yii::import('common.models.wechat.WechatQRScene');
        $account = Yii::app()->request->getParam('account');
        $pid = Yii::app()->request->getParam('pid');
        if (!$account || !$pid) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'params error');
            $this->showMessage();
        }

        $crit = new CDbCriteria;
        $crit->compare('account', $account);
        $crit->compare('userid', $pid);
        $crit->compare('flag', 1);
        $crit->order = 'created DESC';
        $scene = WechatQRScene::model()->find($crit);
        $baseUrl = 'https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=';
        $time = time();
        if(!empty($scene) && ($scene->updated + $scene->expire ) > $time ){
            $qrcode = $baseUrl . $scene->ticket;
            $this->addMessage('state', 'success');
            $this->addMessage('message', 'from Cache..');
            $this->addMessage('data', $qrcode);
            $this->showMessage();
        }

        if(empty($scene)) {
            $scene = new WechatQRScene;
            $scene->requested = 0;
            $scene->flag = 1;
            $scene->account = $account;
            $scene->created = time();
            $scene->userid = $pid;
        }
        $scene->expire = 0;
        $scene->updated = time();
        $scene->ticket = 'tmp';
        $scene->requested++;
        if (!$scene->save()) {
            $error = current($scene->getErrors());
            $this->addMessage('state', 'fail');
            $this->addMessage('message', current($error));
            $this->showMessage();
        }

        $token = CommonUtils::getAccessToken($account);
        $data = $this->getTicket($scene->id, $token);

        if(isset($data['ticket'])){
            $qrcode = $baseUrl .$data['ticket'];
            $result = array(
                'state' => 'success',
                'message' => 'success',
                'data'=> $qrcode
            );
            $scene->ticket = $data['ticket'];
            $scene->expire = $data['expire_seconds'];
            $scene->updated = time();
            $scene->save();
        }else{
            $result = array(
                'state' => 'fail',
                'message' => 'fail',
                'data'=> $data
            );
        }
        $this->addMessage('state', $result['state']);
        $this->addMessage('message', $result['message']);
        $this->addMessage('data', $result['data']);
        $this->showMessage();
    }

    public function getTicket($sceneId, $token)
    {
        $ch = curl_init();
        $data = array(
            "expire_seconds" => 600,
            "action_name" => "QR_SCENE",
            "action_info" => array(
                'scene' => array(
                    'scene_id' => $sceneId
                )
            )
        );
        curl_setopt($ch, CURLOPT_URL,"https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token=" . $token);
        curl_setopt($ch, CURLOPT_POSTFIELDS, CJSON::encode($data));
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $result = curl_exec($ch);
        curl_close($ch);
        return json_decode($result, true);
    }

    /**
     * 获取标记的学生 获取本学年和下学年的数据
     */
    public function actionGetMarkUpStudent()
    {
        $yid= Yii::app()->request->getParam("yid",0);
        $res = CommonUtils::requestDsOnline('child/getMarkUpStudent', array(
            'school_id' => $this->branchId,
            'yid' => $yid,
        ));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    /**
     * 修改标记状态 学生信息传递数组
     */
    public function actionReviseMarkUpStudent()
    {
        $yid = Yii::app()->request->getParam("yid");
        $mark = Yii::app()->request->getParam("mark");
        $child_data= Yii::app()->request->getParam("child_data");
        $fromschool= Yii::app()->request->getParam("fromschool",'');
        $res = CommonUtils::requestDsOnline('child/reviseMarkUpStudent', array(
            'school_id' => $this->branchId,
            'yid' => $yid,
            'child_data' => $child_data,
            'mark'=>$mark,
            'fromschool'=>$fromschool,
        ));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    public function actionGetChildMarkLog()
    {
        $yid = Yii::app()->request->getParam("yid");
        $res = CommonUtils::requestDsOnline('child/getChildMarkLog', array(
            'school_id' => $this->branchId,
            'yid'       => $yid,
        ));
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    //获取学生标签组的信息
    public function actionGetLabelExtraInfo()
    {
        $child_id = Yii::app()->request->getParam("child_id");
        $label = Yii::app()->request->getParam("label",array());
        $res = CommonUtils::requestDsOnline2('child/getLabelExtraInfo', array(
            'child_id' => $child_id,
            'label' => $label,
        ),'get');
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    //获取学生医疗信息
    public function getChildMedicalInfo($child_ids){
        $res = CommonUtils::requestDsOnline2('child/medicalInfo', array(
            'child_ids' => $child_ids,
        ),'get');
        return $res;
    }


    // 判断是否有 ivy_teacher 的权限
    public function isTeacher()
    {
        if (in_array('ivystaff_opschool', Yii::app()->user->roles)) {
            return false;
        } elseif (in_array('ivystaff_teacher', Yii::app()->user->roles)) {
            return true;
        } else{
            return false;
        }
    }

}
