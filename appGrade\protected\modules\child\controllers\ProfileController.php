<?php

class ProfileController extends ProtectedController {

    public $defaultAction = 'profile';
    public $noServiceAction = null;
    private $photoUploading;
    //需要验证的action名称 及 验证的权限
    // action => validateName
    protected $needValidateAction = array(
        'profile' => 'isGuest',
        'misc' => 'isGuest',
        'privacy' => 'isGuest',
    );

    public function init() {
        Yii::app()->errorHandler->errorAction = '/child/profile/error';
        parent::init();
    }

    protected function beforeAction(CAction $action) {
        parent::beforeAction($action);
        if (!empty($this->needValidateAction)) {
            $actionId = $action->getId();
            if (!empty($this->needValidateAction[$actionId])) {

                if (Yii::app()->user->getIsGuest()) {
                    if (Yii::app()->request->isAjaxRequest) {
                        $this->renderPartial('noservice_ajax');
                        return false;
                    } else {
                        $this->noServiceAction = $actionId;
                        $this->render("noservice");
                        Yii::app()->end();
                    }
                }
            }
        }

        return true;
    }

    public function actions() {
        return array(
            'weather' => 'ext.weather.actions.weatherAction',
        );
    }

    /**
     * ala - Alternative Lunch Agreement  特殊餐食用协议
     */
    public function actionAla() {
        Mims::LoadHelper('HtoolKits');
        $view = HtoolKits::getContentByLang('ala_cn', 'ala_en');
        $this->renderPartial($view);
    }

    public function actionMisc() {
        $child = $this->myChildObjs[$this->getChildId()];
        $model = ChildMisc::model()->findByPk($this->childid);
        if ($model == null) {
            $model = new ChildMisc;
            $model->childid = $this->childid;
        }
        $allergy = $model->allergy;
        if (isset($_POST['ChildMisc'])) {
			if ($child->status < ChildProfileBasic::STATS_GRADUATED){
				$model->attributes = $_POST['ChildMisc'];
				$model->updated_timestamp = time();
				$model->create_uid = Yii::app()->user->id;
				if ($model->save()) {
					if ($model->allergy != $allergy) {

						$child_name = $this->myChildObjs[$this->getChildId()]->getChildName();
						$class_info = IvyClass::model()->findByPk($child->classid);

						$branchInfo = BranchInfo::model()->findByPk($this->getSchoolId());
						$support_email = null;
						if (!empty($branchInfo)) {
							$support_email = $branchInfo->support_email;
						}

						// 修改过敏症状  发送邮件
						//邮件发送给校园support，同时抄送父母双方
						$this->widget('ext.sendEmail.Send', array(
							'viewName' => 'updateAllergy',
							'toParty' => false, //发送至 当事人 -参数 party_email
							'toParent' => false, //发送至 父母 -
							'toSupport' => true, //发送至 校园支持 -参数 support_email
							'ccSpouse' => false, //抄送至 配偶 -
							'ccParent' => true, //抄送至 父母 -
							'ccSupport' => false, //抄送至 校园支持 -参数 support_email
							'bccItDev' => true, // 密抄至 ItDev -
							'replyToSupport' => false, // 回复至 校园支持 -参数 support_email
							'params' => array(
								'support_email' => $support_email,
								'class_name' => $class_info->title,
								'child_name' => $child_name,
								'new_allergy' => $model->allergy,
								'original_allergy' => $allergy,
								'date_time' => time(),
							),
						));
					}
					Yii::app()->user->setFlash('success', Yii::t("message", "Data Saved!"));
				} else {
					print_r($model->getErrors());
				}
			}
        }

        $this->render('misc', array("model" => $model,'child'=>$child));
    }

    public function actionPrivacy() {
        $model = new ProfilePrivacyForm;
        $child = $this->myChildObjs[$this->childid];

        $model->iniData($child);
        if (isset($_POST['ProfilePrivacyForm'])) {

            if (Yii::app()->user->checkAccess('adminChild', array("childid" => $this->childid))) {
                $model->attributes = $_POST['ProfilePrivacyForm'];
                $child->misc->privacy = $model->genData();
                if (!$child->misc->save()) {
                    print_r($child->getErrors());
                } else {
                    Yii::app()->user->setFlash('success', Yii::t("message", "Data Saved!"));
                }
            } else {
                throw new CHttpException(500, 'No permission');
            }
        }
        $childInfo = $this->getChildInfo();
        $currentClassId = $childInfo['classid'];
        Yii::import('common.models.portfolio.NotesChild');
        $nc = new NotesChild();
        $classIds = $nc->getClassIdList($this->getChildId());
        $classIds[$currentClassId] = $currentClassId;
        $this->render('privacy', array('model' => $model, 'currentClassId' => $currentClassId, 'classIdList' => $classIds));
    }

    public function processAvatarUpload($uploadedInst, $params) {
        $subPath = trim($params['subDir'], '/') . '/';

        $normalDir = rtrim(Yii::app()->params['uploadPath'], '/') . '/' . $subPath;

        $fileName = $params['filePrefix'] . uniqid() . '.' . strtolower($uploadedInst->getExtensionName());

        $filePath = $normalDir . $fileName;

        $uploadedInst->saveAs($filePath);

        register_shutdown_function(array($this, 'photoUploadShutdown_handler'), $filePath);
        $this->photoUploading = true;

        Yii::import('application.extensions.image.Image');
        $image = new Image($filePath);
        if (isset($params['forceSquare']) && $params['forceSquare']) {
            $min = min($image->__get('width'), $image->__get('height'));
            $image->crop($min, $min)->save();
        }
        $image->resize($params['sizeNormal'], $params['sizeNormal'])->save();

        $this->photoUploading = false;

        return $fileName;
    }

    public function processUpload($uploadedInst, $params, $oldFile = "", $deleteOld = true) {
        $subPath = trim($params['subDir'], '/') . '/';
        $subThumbPath = trim($params['subDir'], '/') . '/thumbs/';

        $normalDir = rtrim(Yii::app()->params['uploadPath'], '/') . '/' . $subPath;
        $thumbDir = rtrim(Yii::app()->params['uploadPath'], '/') . '/' . $subThumbPath;

        $fileName = $params['filePrefix'] . uniqid() . '.' . strtolower($uploadedInst->getExtensionName());

        $filePath = $normalDir . $fileName;
        $fileThumbPath = $thumbDir . $fileName;

        $uploadedInst->saveAs($filePath);

        register_shutdown_function(array($this, 'photoUploadShutdown_handler'), $filePath);
        $this->photoUploading = true;

        Yii::import('application.extensions.image.Image');
        $image = new Image($filePath);
        if (isset($params['forceSquare']) && $params['forceSquare']) {
            $min = min($image->__get('width'), $image->__get('height'));
            $image->crop($min, $min)->save();
        }
        $image->resize($params['sizeNormal'], $params['sizeNormal'])->save();
        $image->resize($params['sizeThumb'], $params['sizeThumb'])->save($fileThumbPath);

        $this->photoUploading = false;
        $aliYunOss['new'][] = $subPath.$fileName;
        $aliYunOss['new'][] = $subThumbPath.$fileName;

        if ($deleteOld && !empty($oldFile)) {
            @unlink($normalDir . $oldFile);
            @unlink($thumbDir . $oldFile);
            $aliYunOss['del'][] = $subPath.$oldFile;
            $aliYunOss['del'][] = $subThumbPath.$oldFile;
        }

        CommonUtils::processAliYunOSS($aliYunOss);

        return $fileName;
    }

    public function photoUploadShutdown_handler($file) {
        if ($this->photoUploading) {
            @unlink($file);
            echo '<hr><h1>Photo is too big</h1></hr>';
        }
    }

    public function actionProfile() {
        $cfgs = Mims::LoadConfig('CfgProfile');
        $extra = array();
        $t = strtolower(Yii::app()->request->getParam("t", "child"));

        switch ($t) {
            case "child":
                $model = $this->myChildObjs[$this->childid];
                // if it is ajax validation request
                if (isset($_POST['ajax']) && $_POST['ajax'] === 'child-form') {
                    echo CActiveForm::validate($model);
                    Yii::app()->end();
                }

                $model->setScenario('update');

                if (isset($_POST['ChildProfileBasic'])) {
                    if (Yii::app()->user->checkAccess('adminChild', array("childid" => $this->childid)) && $model->status < ChildProfileBasic::STATS_GRADUATED) {
                        $oldPhoto = $model->photo;
                        unset($_POST['ChildProfileBasic']['educational_id']);
                        $model->attributes = $_POST['ChildProfileBasic'];
//                        $model->first_name_en = ucwords($_POST['ChildProfileBasic']['first_name_en']);
//                        $model->last_name_en = ucwords($_POST['ChildProfileBasic']['last_name_en']);
                        $model->uploadPhoto = CUploadedFile::getInstance($model, 'uploadPhoto');
                        //$model->birthday = strtotime($model->birthday_search);
                        //$model->birthday = ($model->birthday) ? $model->birthday : 0; //如果输入 2012-19-90 之类的错误日期格式，此行代码可以避免一个多于的错误提示
                        if ($model->validate()) {
                            if ($model->uploadPhoto) {
                                $params = $cfgs["childPhoto"];
                                $newPhoto = $this->processAvatarUpload($model->uploadPhoto, $params);
                                $model->processAvatar($newPhoto, $oldPhoto);
                            }

                            if ($model->save()) {
                                ChildProfileBasic::setChildSync($this->childid, 1);
                                Yii::app()->user->setFlash('success', Yii::t("message", "Data Saved!"));
                            } else {
                                print_r($model->getErrors());
                            }
                        }
                    }
                }

                break;
            case "parent":
                $pids['f'] = $this->myChildObjs[$this->childid]->fid;
                $pids['m'] = $this->myChildObjs[$this->childid]->mid;
                if (!in_array(Yii::app()->user->id, $pids) && !Yii::app()->user->isStaff()) {
                    throw new CHttpException(500, Yii::t("userinfo", "User does not match parents IDs"));
                }

                $mine = (Yii::app()->user->id == $pids["f"]) ? "f" : "m";
                $male = Yii::app()->request->getParam("p", $mine);
                $pid = $pids[$male];


                $ini = Yii::app()->request->getParam("p", "");

                $extra['class']['m'] = "";
                $extra['class']['f'] = "";
                if ($ini == "") {
                    $extra['class'][$mine] = "active";
                }

                if ($pid) {
                    $model = User::model()->with('parent', 'profile')->findByPk($pid);
                    $model->setScenario('update');
                } else {
                    $model = new User;
                    $model->email = "";
                    $model->parent = new IvyParent;
                    $model->profile = new UserProfile;
                    $model->setScenario('create');
                }
                $model->parent->gender = $male == 'f' ? 1 : 2;

                if (isset($_POST['ajax']) && $_POST['ajax'] === 'parent-form') {
                    echo CActiveForm::validate($model);
                    Yii::app()->end();
                }
                $creatingAccount = false;
                $updateAccount = false;

                if (isset($_POST['User'])) {
                    if (Yii::app()->user->checkAccess('adminChild', array("childid" => $this->childid))) {
                        $oldPhoto = $model->user_avatar;
                        $parent = $model->parent;
                        $userProfile = $model->profile;
                        $emailChanged = false;
                        if (isset($_POST["User"]["changeEmail"])) {
                            if (strtolower($_POST["User"]["changeEmail"]) != strtolower($model->email)) {
                                $emailChanged = true;
                            }
                        }

                        $model->changePassword = $_POST['User']['changePassword'];
                        $model->verifyPassword = $_POST['User']['verifyPassword'];
                        // $parent->attributes = $_POST['IvyParent'];

                        // $userProfile->first_name = ucwords($_POST['IvyParent']['en_firstname']);
                        // $userProfile->last_name = ucwords($_POST['IvyParent']['en_lastname']);
                        // $parent->en_firstname = ucwords($_POST['IvyParent']['en_firstname']);
                        // $parent->en_lastname = ucwords($_POST['IvyParent']['en_lastname']);

                        if (!$emailChanged) {
                            $model->changeEmail = "";
                        }

                        $model->uploadPhoto = CUploadedFile::getInstance($model, 'uploadPhoto');
                        $isNew = false;
                        if ($model->validate() && $parent->validate()) {
                            if ($model->isNewRecord) {
                                $isNew = true;
                                $model->user_regdate = time();
                                $model->pass = md5($model->changePassword);
//                                $parent->password_text = $model->changePassword;
                                $parent->childs = serialize(array_keys($this->myChildObjs));
                                $model->timezone_offset = 8.0;
                                $model->child_theme = "green";
                                $userProfileSite = new UserProfileSite;
                                // 发送创建账号的邮件
                                $creatingAccount = true;
                            } else {
                                if ($model->changePassword != "") {
                                    $model->pass = md5($model->changePassword);
//                                    $parent->password_text = $model->changePassword;
                                }
                                if ($model->changeEmail != "" && $model->changeEmail != $model->email) {
                                    $model->email = $model->changeEmail;
                                    // 发送修改账号的邮件
                                    $updateAccount = true;
                                }
                            }
                            if ($model->uploadPhoto) {
                                $params = $cfgs["userPhoto"];
                                $delold = $oldPhoto == 'blank.gif' ? false : true;
                                $model->user_avatar = $this->processUpload($model->uploadPhoto, $params, $oldPhoto, $delold);
                            }
                            $model->name = $parent->cn_name;
                            if ($model->save()) {
                                if ($parent->validate()) {
                                    $parent->pid = $model->uid;
                                    if ($isNew) {
                                        $userProfileSite->uid = $model->uid;
                                        $userProfile->uid = $model->uid;
                                    }
                                    //为了维持数据统一，不得不做这些额外工作
                                    $keymaps = array(
                                        "first_name" => "en_firstname",
                                        "last_name" => "en_lastname",
                                        "user_gender" => "gender",
                                        "nationality" => "country",
                                        "location" => "province",
                                    );
                                    if ($parent->save()) {
                                        foreach ($keymaps as $k => $v) {
                                            $userProfile->setAttribute($k, $parent->getAttribute($v));
                                        }
                                        if (!$userProfile->save()) {
                                            print_r($userProfile->getErrors());
                                        }
                                        if ($isNew) {
                                            //这个表没什么用，但为了维持MIMS数据y一致性，也插入一条数据
                                            if (!$userProfileSite->save()) {
                                                print_r($userProfileSite->getErrors());
                                            }

                                            //更新孩子父母ID信息
                                            //$this->myChildObjs[$this->childid]->fid
                                            $attr = strtolower($male . "id");
                                            if (in_array($attr, array("fid", "mid"))) {
                                                $this->myChildObjs[$this->childid]->setAttribute($attr, $model->uid);
                                                $this->myChildObjs[$this->childid]->save();
                                            }
                                            //创建用户权限
                                            $model->createParentRole();
                                        }

                                        $branchInfo = BranchInfo::model()->findByPk($this->getSchoolId());
                                        $support_email = null;
                                        if (!empty($branchInfo)) {
                                            $support_email = $branchInfo->support_email;
                                        }

                                        if (true == $creatingAccount) {
                                            // 发送创建账号的邮件
                                            //邮件发送给新建帐号的email，抄送至创建帐号的一方，若家长回复则回复至校园support
                                            $this->widget('ext.sendEmail.Send', array(
                                                'viewName' => 'creatingAccount',
                                                'toParty' => true, //发送至 当事人 -参数 party_email
                                                'toParent' => false, //发送至 父母 -
                                                'toSupport' => false, //发送至 校园支持 -参数 support_email
                                                'ccSpouse' => true, //抄送至 配偶 -
                                                'ccParent' => false, //抄送至 父母 -
                                                'ccSupport' => false, //抄送至 校园支持 -参数 support_email
                                                'bccItDev' => true, // 密抄至 ItDev -
                                                'replyToSupport' => true, // 回复至 校园支持 -参数 support_email
                                                'params' => array(
                                                    'party_email' => $model->email,
                                                    'support_email' => $support_email,
                                                    'account' => $model->email,
                                                    'password' => $model->changePassword,
                                                    'site_name' => Yii::app()->urlManager->baseUrl,
                                                    'date_time' => time(),
                                                ),
                                            ));
                                        }

                                        if (true == $updateAccount) {
                                            // 发送修改账号的邮件
                                            //邮件发送给要当前新修改的帐号EMAIL，若家长回复则回复至校园support
                                            $this->widget('ext.sendEmail.Send', array(
                                                'viewName' => 'updateAccount',
                                                'toParty' => true, //发送至 当事人 -参数 party_email
                                                'toParent' => false, //发送至 父母 -
                                                'toSupport' => false, //发送至 校园支持 -参数 support_email
                                                'ccSpouse' => false, //抄送至 配偶 -
                                                'ccParent' => false, //抄送至 父母 -
                                                'ccSupport' => false, //抄送至 校园支持 -参数 support_email
                                                'bccItDev' => true, // 密抄至 ItDev -
                                                'replyToSupport' => true, // 回复至 校园支持 -参数 support_email
                                                'params' => array(
                                                    'party_email' => $model->email,
                                                    'support_email' => $support_email,
                                                    'account' => $model->email,
                                                    'site_name' => Yii::app()->urlManager->baseUrl,
                                                    'date_time' => time(),
                                                ),
                                            ));
                                        }
                                        //如果修改的是当前用户的信息，更新session里的username;
                                        if ($model->uid == Yii::app()->user->id) {
                                            Yii::app()->user->setName($model->getName());
                                        }
                                        ChildProfileBasic::setChildSync($this->childid, 1);
                                        Yii::app()->user->setFlash('success', Yii::t("message", "Data Saved!"));
                                    } else {
                                        print_r($parent->getErrors());
                                    }
                                }
                            } else {
                                print_r($model->getErrors());
                            }
                        }
                    }
                }

                break;
            case "address":
                $model = HomeAddress::model()->findByPk($this->childid);

                // if it is ajax validation request
                if (isset($_POST['ajax']) && $_POST['ajax'] === 'address-form') {
                    echo CActiveForm::validate($model);
                    Yii::app()->end();
                }

                if (isset($_POST['HomeAddress'])) {
                    if (Yii::app()->user->checkAccess('adminChild', array("childid" => $this->childid))) {
                        $address = trim($model->en_address);
                        $model->attributes = $_POST['HomeAddress'];
                        if ($model->validate()) {
                            if ($model->save()) {
                                if (trim($model->en_address) != $address) {
                                    $child = $this->myChildObjs[$this->getChildId()];
                                    $child_name = $child->getChildName();
                                    $class_info = IvyClass::model()->findByPk($child->classid);

                                    $branchInfo = BranchInfo::model()->findByPk($this->getSchoolId());
                                    $support_email = null;
                                    if (!empty($branchInfo)) {
                                        $support_email = $branchInfo->support_email;
                                    }

                                    // 修改家庭地址  发送邮件
                                    //邮件发送给校园support，同时抄送父母双方
                                    $this->widget('ext.sendEmail.Send', array(
                                        'viewName' => 'updateAddress',
                                        'toParty' => false, //发送至 当事人 -参数 party_email
                                        'toParent' => false, //发送至 父母 -
                                        'toSupport' => true, //发送至 校园支持 -参数 support_email
                                        'ccSpouse' => false, //抄送至 配偶 -
                                        'ccParent' => true, //抄送至 父母 -
                                        'ccSupport' => false, //抄送至 校园支持 -参数 support_email
                                        'bccItDev' => true, // 密抄至 ItDev -
                                        'replyToSupport' => false, // 回复至 校园支持 -参数 support_email
                                        'params' => array(
                                            'support_email' => $support_email,
                                            'class_name' => $class_info->title,
                                            'child_name' => $child_name,
                                            'new_allergy' => $model->en_address,
                                            'original_allergy' => $address,
                                            'date_time' => time(),
                                        ),
                                    ));
                                }
                                Yii::app()->user->setFlash('success', Yii::t("message", "Data Saved!"));
                            } else {
                                print_r($model->getErrors());
                            }
                        }
                    }
                }

                break;
            case "password":
                $pids['f'] = $this->myChildObjs[$this->childid]->fid;
                $pids['m'] = $this->myChildObjs[$this->childid]->mid;
                if (!in_array(Yii::app()->user->id, $pids) && !Yii::app()->user->isStaff()) {
                    throw new CHttpException(500, Yii::t("userinfo", "User does not match parents IDs"));
                }
                $model = User::model()->with('parent', 'profile')->findByPk(Yii::app()->user->id);
                $model->setScenario('update');
                if (isset($_POST['User'])) {
                    $model->changePassword = $_POST['User']['changePassword'];
                    $model->verifyPassword = $_POST['User']['verifyPassword'];
                    if ($model->changePassword != "") {
                        $model->pass = md5($model->changePassword);
                    }
                    if ($model->validate()) {
                        if ($model->save()) {
                            Yii::app()->user->setFlash('success', Yii::t("message", "Data Saved!"));
                        } else {
                            print_r($model->getErrors());
                        }
                    }
                }
                break;
        }
        $this->render('profile', array("model" => $model, "t" => $t, "cfgs" => $cfgs, "extra" => $extra));
    }

    public function actionShare() {
        $this->layout = "//layouts/column2_child_portfolio";
        $cs = Yii::app()->clientScript;
//		$cs->registerCssFile(Yii::app()->theme->baseUrl."/css/portfolio.css");
        $cs->registerCssFile(Yii::app()->theme->baseUrl . "/css/portfolio.css?t=" . Yii::app()->params['refreshAssets']);

        //if (!Yii::app()->user->checkAccess('viewProfilePrivate', array("childid" => $this->childid))) {
        //    $this->render("noservice");
        //    Yii::app()->end();
        //}
        $model = new ProfileShareForm;

        // if it is ajax validation request
        if (isset($_POST['ajax']) && $_POST['ajax'] === 'share-form') {
            echo CActiveForm::validate($model);
            Yii::app()->end();
        }

        $child = $this->myChildObjs[$this->childid]; //ChildProfileBasic::model()->with('access')->findByPk($this->childid);

        if (isset($_POST['ProfileShareForm'])) {

            if (Yii::app()->user->checkAccess('adminChild', array("childid" => $this->childid))) {
                $model->accessCode = $_POST['ProfileShareForm']['accessCode'];
                //$model->attributes=$_POST['ProfileShareForm'];
                //删掉access key中对应的数据，并清空孩子的access pass字段
                if (isset($_POST['disableBtn'])) {
                    $_code = $model->genAccessLink($child->childid);
                    ChildAccess::model()->deleteByPk($_code);
                    $child->access_pass = "";
                    $model->accessCode = $child->access_pass;
                    if (!$child->save()) {
                        print_r($child->getErrors());
                    }
                } else {
                    $model->setScenario("create");
                    if ($model->validate()) {
                        $access = ChildAccess::model()->find('childid=:childid',array(':childid'=>$child->childid));
                        if (empty($access)){
                            $access = new ChildAccess;
                        }
                        $_code = $model->genAccessLink($child->childid);
                        $access->accesskey = $_code;
                        $access->childid = $child->childid;
                        $access->save();
                        $child->access_pass = $model->accessCode;
                        if (!$child->save()) {
                            print_r($child->getErrors());
                        } else {
                            $model->enabled = 1;
                            Yii::app()->user->setFlash('success', Yii::t("message", "Data Saved!"));
                        }
                    }
                }
            } else {
                throw new CHttpException(500, 'No permission');
            }
        } else {
            $model->accessCode = $child->access_pass;
            $model->enabled = (!empty($child->access) && trim($child->access_pass) != "" ) ? 1 : 0;
        }

        $model->getPreviewText($child);
        $this->render('share', array("model" => $model));
    }

    public function actionWelcome() {
        Yii::import('common.models.calendar.*');
        Yii::import('common.models.portfolio.*');
        Yii::import('common.models.classTeacher.InfopubStaffExtend');

        $childId = $this->getChildId();
        $childclass = ChildClassLink::model()->findByPk($childId);
        if ($childclass) {
            $classId = $childclass->classid;
            $schoolId = $childclass->schoolid;
            $yId = $childclass->calendar;
        } else {
            $classId = 0;
            $schoolId = $this->myChildObjs[$childId]->schoolid;
            $bobj = Branch::model()->findByPk($schoolId);
            $yId = $bobj->schcalendar;
        }

        Yii::app()->clientScript->registerCssFile(Yii::app()->theme->baseUrl . '/css/welcome.css?t=' . Yii::app()->params['refreshAssets']);
        $this->render('welcome', array(
            'schoolId' => $schoolId,
            'classId' => $classId,
            'yId' => $yId,
        ));
    }

    public function actionNumpic($num = 0, $size = 38) {
        $img = imagecreatetruecolor(30, 46);
        $color = imagecolorallocate($img, 232, 231, 218);
        imagecolortransparent($img, $color);
        imagefill($img, 0, 0, $color);
        $textcolor = imagecolorallocate($img, 208, 101, 46);
        $font = Yii::getPathOfAlias('common') . '/fonts/boopee.ttf'; //echo $font; die;
        imagettftext($img, $size, 0, 0, 40, $textcolor, $font, strval($num));
        //imagepng($img,"aaa.png");
        //imagedestroy($img);
        Header("Content-type: image/png");
        imagePNG($img);
        imagedestroy($img);
    }

    public function actionError() {
        if ($error = Yii::app()->errorHandler->error) {
            if (Yii::app()->request->isAjaxRequest)
                echo $error['message'];
            else
                $this->render('error', $error);
        }
    }

    public function actionCloseGuide() {
        if (Yii::app()->request->isAjaxRequest) {
            Yii::app()->user->hideGuide();
            echo Yii::app()->user->getHideGuide();
        }
    }

    // Uncomment the following methods and override them if needed
    /*
      public function filters()
      {
      // return the filter configuration for this controller, e.g.:
      return array(
      'inlineFilterName',
      array(
      'class'=>'path.to.FilterClass',
      'propertyName'=>'propertyValue',
      ),
      );
      }

      public function actions()
      {
      // return external action classes, e.g.:
      return array(
      'action1'=>'path.to.ActionClass',
      'action2'=>array(
      'class'=>'path.to.AnotherActionClass',
      'propertyName'=>'propertyValue',
      ),
      );
      }
     */
}
