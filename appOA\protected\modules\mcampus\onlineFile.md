# surveyReturn 返校调查文旦
```

域名: http://apps.mims.cn/mcampus/surveyReturn/index?branchId=CD_FC

```

[TOC] 

## 一、网络文件

### 1、文件或者文件夹列表

**请求URI**
```
/onlineFile/index
```

**请求方式**
```
POST
```

**请求参数**

| 参数     | 是否必选 | 类型   | 示例                     | 说明     |
| :------- | :------: | :----- | :----------------------- | :------- |

**返回说明**

| 参数  | 说明      |
| :---- | :-------- |
| $templateData | 模板数据 |


**返回示例**

```javascript
{
    Array
(
    [0] => Array
        (
            [id] => 3
            [display] => display
            [type] => file
            [path_original] => 
            [path_processed] => 
            [handle] => 
            [handle_status] => 0
            [status] => 1
            [created_at] => 1581561007
            [updated_at] => 1581561007
        )

    [1] => Array
        (
            [id] => 2
            [display] => asda
            [type] => 123
            [path_original] => 
            [path_processed] => 
            [handle] => 
            [handle_status] => 0
            [status] => 1
            [created_at] => 1581560298
            [updated_at] => 1581560298
        )

)
}

```
### 2、增加文件或者文件夹列表

**请求URI**
```
/onlineFile/saveOnline
```

**请求方式**
```
POST
```

**请求参数**

| 参数     | 是否必选 | 类型   | 示例                     | 说明     |
| :------- | :------: | :----- | :----------------------- | :------- |
| id | N | INT | 1 | id |
| pid | N | INT | 1 | pid |
| OnlineFile[type] | Y | staring | file | file 文件夹 photo 图片 video 视频 |
| OnlineFile[display] | Y | staring | 测试 | 显示名称 |
| OnlineFile[status] | Y | int | 0 | 0关闭 1开放 99删除|
| OnlineFile[introduction_cn] | N | staring | staring | 中文简介 |
| OnlineFile[introduction_en] | N | staring | staring | 英文简介 |
| OnlineFile[path_original] | N | staring | staring | 原地址 |
| OnlineFile[path_processed] | N | staring | staring | 处理后的地址 |
| OnlineFile[handle] | N | staring | staring | 源文件处理方式 |
| OnlineFile[handle_status] | N | int | 0 | 0处理中 1处理完成 |

**返回说明**

| 参数  | 说明      |
| :---- | :-------- |
| $templateData | 模板数据 |


**返回示例**

```javascript
{
    	<pre>Array
(
    [school_id] => CD_FC
    [pid] => 0
    [display] => display
    [type] => file
    [path_original] => 
    [path_processed] => 
    [handle] => 
    [handle_status] => 0
    [status] => 1
    [created_at] => 1581562269
    [created_by] => 8016536
    [updated_at] => 1581562269
    [updated_by] => 8016536
    [id] => 5
)
</pre>
}

```
### 4、根据PID查询文件夹下的数据

**请求URI**
```
/onlineFile/onlineList
```

**请求方式**
```
POST
```

**请求参数**

| 参数     | 是否必选 | 类型   | 示例                     | 说明     |
| :------- | :------: | :----- | :----------------------- | :------- |
| pid | Y | int | 1 | 主键ID |

**返回说明**

| 参数  | 说明      |
| :---- | :-------- |
| $templateData | 模板数据 |


**返回示例**

```javascript
{
  {"state":"success","data":"2","message":"success"}
}

```
### 3、删除文件或者文件夹

**请求URI**
```
/onlineFile/delOnline
```

**请求方式**
```
POST
```

**请求参数**

| 参数     | 是否必选 | 类型   | 示例                     | 说明     |
| :------- | :------: | :----- | :----------------------- | :------- |
| id | Y | int | 1 | 主键ID |

**返回说明**

| 参数  | 说明      |
| :---- | :-------- |
| $templateData | 模板数据 |


**返回示例**

```javascript
{

}

```

### 5、删除文件或者文件夹

**请求URI**
```
/onlineFile/path
```

**请求方式**
```
POST
```

**请求参数**

| 参数     | 是否必选 | 类型   | 示例                     | 说明     |
| :------- | :------: | :----- | :----------------------- | :------- |
| id | Y | int | 1 | 主键ID |

**返回说明**

| 参数  | 说明      |
| :---- | :-------- |
| $templateData | 模板数据 |


**返回示例**

```javascript
{
    
}

```

