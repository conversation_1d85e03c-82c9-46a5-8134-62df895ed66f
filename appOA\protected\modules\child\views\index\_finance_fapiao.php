<div class="table_full">
<table width="100%">
	<colgroup>
		<col width="220">
		<col width="120">
		<col width="100">
		<col width="120">
	</colgroup>
	<thead>
		<tr>
			<th><?php echo Yii::t("invoice", "Invoice Title");?></th>
			<th><?php echo Yii::t("invoice", "Fapiao Amount");?></th>
			<th><?php echo Yii::t("invoice", "付款日期");?></th>
			<th><?php echo Yii::t("payment", "Payment Type");?></th>
			<th><?php echo Yii::t("invoice", "Fapiao Info");?></th>
		</tr>
	</thead>
	<?php
			
		$lv = $this->widget('zii.widgets.CListView', array(
			'id'=>'child-invoice',
			'dataProvider'=>$dataProvider,
			'itemsTagName' => 'tbody',
			'template'=>"{items}",
			'ajaxUpdate'=>false,
			'itemView'=>'_fapiao_item',
		));							
	
	?>

</table>
</div>

<div class="table_full">
<?php echo $lv->renderSummary();?>
</div>

<div>
<?php $this->widget('CLinkPager', array(
    'pages' => $dataProvider->pagination,
)) ?>
</div>

<style>
td.fapiao-info span.hover{visibility:hidden;}
td.fapiao-info:hover span.hover{visibility:visible;}
</style>

<script type="text/javascript">

var invoiceTitle = '<?php echo $this->childObj->invoice_title?>';
var _tmpHtml = '';
$('td.fapiao-info').bind('dblclick',function(){
    if ($(this).html().substring(0, 6) === '<input'){
        return false;
    }
    var number = $(this).find('span.red').text();
    var tit = $(this).find('span.org').text();
    var title = tit ? tit : invoiceTitle;
    title = title.replace('"', '');
    title = title.replace("'", '');
    var id = $(this).attr('id');
	html = '<input type="text" id="n'+id+'" class="input length_2 mb5" name="fapiao-number" value="'+number+'" placeholder="发票号" /><br />';
	html+= '<input type="text" id="t'+id+'" class="input length_4 mb5" name="fapiao-title" value="'+title+'" placeholder="发票抬头" />';
    html+= '<label class="mr10"><input id="s'+id+'" type="checkbox" name="savetobasic" /> 设置为默认发票抬头</label>';
	html+= '<br /><button class="btn ml15 mr10" onclick="save('+id+');"><?php echo Yii::t("message", "Save");?></button>';
	html+= '<button class="btn ml15" onclick="cancel('+id+');"><?php echo Yii::t("message", "Cancel");?></button>';
    _tmpHtml = $(this).html();
	$(this).html(html);
    $('#n'+id).focus();
});
function save(id)
{
    if ($('#n'+id).val() === '' && $('#t'+id).val() === ''){
        $('#n'+id).focus();
        return false;
    }
    $.post('<?php echo $this->createUrl('/child/index/processSavefapiao')?>', {id: id, number: $('#n'+id).val(), title: $('#t'+id).val(), savetobasic: $('#s'+id).attr('checked')}, function(data){
        if (data.state === 'success'){
            var html = '<span class="red mr15">'+data.data.number+'</span>';
            html += '<span class="org mr15">'+data.data.title+'</span>';
            html += '<span class="hover"><?php echo Yii::t("message", "双击编辑");?></span>';
            invoiceTitle = data.data.title;
            $('#'+id).html(html);
        }
        else {
            alert(data.message);
        }
    }, 'json');
}
function cancel(id)
{
    $('#'+id).html(_tmpHtml);
}
</script>