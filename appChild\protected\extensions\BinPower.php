<?php

/* 二进制权限控制PHP类
 * 请到网上搜索更多相关知识
* 作    者: 多菜鸟
* 邮    箱: kingerq AT QQ DOT com
* 来    源：http://blog.csdn.net/kingerq/archive/2009/10/22/4714223.aspx
*
*/
class BinPower {

	private $power = "";
	//权限存贮变量,十进制整数

	public function __construct($power = 0) {
		$this->setPower($power);
	}
	public function setPower($power) {
		$this->power = intval($power);
	}

	/* 添加指定权限
	 * $bit 代表权限序号
	*/
	public function addPower($bit) {
		//利用逻辑或添加权限
		$right = $this->returnPower($bit);
		$this->power = $this->power | intval($right);
	}

	/* 删除指定权限
	 * $bit 代表权限序号
	*/
	public function delPower($bit) {
		//删除权限，先将预删除的权限取反，再进行与操作
		$right = $this->returnPower($bit);
		$this->power = $this->power & ~intval($right);
	}

	/* 判断是否存在指定权限
	 * $bit 代表权限序号
	*/
	public function isPower($bit) {
		//权限比较时，进行与操作，得到0的话，表示没有权限
		$right = $this->returnPower($bit);
		if (($this->power & intval($right)) == 0)
			return false;
		return true;
	}

	/*
	 * 返回操作后的权限
	*/
	public function getPower() {
		//为了减少存贮位数，返回也可以转化为十六进制
		return $this->power;
	}

	public function returnPower($bit) {
		return pow(2, $bit);
	}

}

?>
