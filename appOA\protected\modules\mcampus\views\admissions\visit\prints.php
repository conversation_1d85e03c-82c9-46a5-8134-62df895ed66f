<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <?php
            $this->widget('ext.ivyCGridView.BsCGridView', array(
                'id'=>'confirmed-visit-grid',
                'afterAjaxUpdate'=>'js:head.Util.modal',
                'dataProvider'=>$dataProvider,
                'enableSorting'=>false,
                'template'=>"{items}{pager}",
                'colgroups'=>array(
                    array(
                        "colwidth"=>array(150,150,150,150,150),
                    )
                ),
                'columns'=>array(
                    array(
                        'name' => 'visit_date',
                        'type'=>'raw',
                        'value' => 'OA::formatDateTime($data->visit_date)."<br><span>".$data->visit_time."</span>"'
                    ),
                    array(
                        'name'=>'孩子姓名',
                        'value'=>'$data->child_name',
                    ),
                    array(
                        'name'=>'孩子年龄',
                        'type'=>'raw',
                        'value'=>'CommonUtils::getAge($data->birthdate)',
                    ), 
                    array(
                        'name'=>'家长信息',
                        'type'=>'raw',
                        'value'=>'$data->parent_name."<br>".$data->phone."<br>".$data->email',
                    ),
                ),
            ));
            ?>
        </div>
    </div>
</div>
<div>预约总人数: <?php echo $count ?></div>