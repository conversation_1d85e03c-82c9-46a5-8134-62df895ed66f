
$(function(){
    showSearch = function(){
        $('#register-new #regform-content').hide();
        var _qText = $('#register-new #siblingSearch_queryText').val();
        if(!_.isEmpty(_qText)){
            $.ajax({
                async: false,
                type: 'post',
                url: postSearchUrl,
                dataType: 'json',
                data: {targetBranchId: selectedBranchId, queryText:_qText}
            }).done(function(data){
                $('#register-new #search-box .panel-body').html('');
                $('#register-new #search-box .panel-heading').html('查询结果');
                if(data.state=='success'){
                    if(data.data.length>0){
                        $('#register-new #search-box .panel-body').html('<div id="sibling-list" class="list-group"></div>');
                        var _tmpl = _.template('<a href="javascript:void(0)" childid="<%= id %>" class="list-group-item"><%= cinfo %></a>');
                        $.each(data.data, function(index,item){
                            var _view = _tmpl(item);
                            $('#register-new #sibling-list').append(_view);
                        });

                        $('#register-new #sibling-list a').click(function(){
                            chooseChild(this);
                        });

                    }else{
                        $('#register-new #search-box .panel-body').html('<div class="alert alert-warning">没有找到相关信息</div>');
                    }
                }else{
                    $('#register-new #search-box .panel-body').html('<div class="alert alert-danger">请求错误</div>');
                }
                $('#register-new #search-box').show('blind',{},200)
            });
        }
    }

    $('#register-new #siblingSearch_queryText').bind('keyup', function(event){
        if (event.keyCode=="13"){
            showSearch();
        }
    });

    chooseChild = function(obj){
        var childId = $(obj).attr('childid');
        $.ajax({
            async: false,
            type: 'post',
            url: getSibingsUrl,
            dataType: 'json',
            data: {childid: childId}
        }).done(function(data){
            if(data.state=='success'){
                $('#register-new #search-box .panel-body').html('');
                $('#register-new #search-box .panel-heading').html('兄弟姐妹信息');
                var _tmple = _.template($('#sibling-item-template').html());
                $.each(data.data.children,function(_i,_d){
                    var _view = _tmple(_d);
                    $('#register-new #search-box .panel-body').append(_view);
                });
                $('#register-new #regform-content').show('blind',{},200);
                $('#register-new #regform-content').append('<input type="hidden" name="siblings" value="'+childId+'">');
            }else{

            }
        });
    }

    SSCampusChange = function(obj){
        selectedBranchId = $(obj).children('option:selected').val();
        if(selectedBranchId != myBranchId){
            $('#register-new #searchAlert').show();
        }else{
            $('#register-new #searchAlert').hide();
        }
        $('#register-new #search-box').hide();
    }
})
