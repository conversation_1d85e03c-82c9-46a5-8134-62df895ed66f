<?php

/**
 * This is the model class for table "ivy_child_media_2012".
 *
 * The followings are the available columns in table 'ivy_child_media_2012':
 * @property integer $id
 * @property string $schoolid
 * @property integer $classid
 * @property integer $yid
 * @property integer $weeknum
 * @property string $tag
 * @property string $type
 * @property string $filename
 * @property integer $server
 * @property integer $timestamp
 * @property integer $uid
 */
class ChildMedia extends CActiveRecord
{
    public static $StartYear = 2009;
    
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ChildMedia the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_child_media_'.self::$StartYear;
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('schoolid, classid, yid, weeknum, filename, server, timestamp, uid', 'required'),
			array('classid, yid, weeknum, server, timestamp, uid', 'numerical', 'integerOnly'=>true),
			array('schoolid', 'length', 'max'=>30),
			array('type', 'length', 'max'=>5),
			array('tag', 'safe'),
			array('filename', 'length', 'max'=>255),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, schoolid, classid, yid, weeknum, tag, type, filename, server, timestamp, uid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'weeklyLinks' => array(self::HAS_MANY, 'ChildMediaLinks', array('pid'=>'id'))
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'schoolid' => 'Schoolid',
			'classid' => 'Classid',
			'yid' => 'Yid',
			'weeknum' => 'Weeknum',
			'tag' => 'Tag',
			'type' => 'Type',
			'filename' => 'Filename',
			'server' => 'Server',
			'timestamp' => 'Timestamp',
			'uid' => 'Uid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('weeknum',$this->weeknum);
		$criteria->compare('tag',$this->tag,true);
		$criteria->compare('type',$this->type,true);
		$criteria->compare('filename',$this->filename,true);
		$criteria->compare('server',$this->server);
		$criteria->compare('timestamp',$this->timestamp);
		$criteria->compare('uid',$this->uid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
	
	public function getSubPath(){
		$subPath = sprintf('s_%s/c_%s/w_%s/', $this->schoolid, $this->classid, $this->weeknum );
		return $subPath;
	}
    
    public function getMediaUrl($thumb=false)
    {
        $mediaUrl = CommonUtils::getMediaUrl($thumb, $this->server, $this->type, $this->filename, $this->schoolid, $this->classid, $this->weeknum);

        return $mediaUrl;
    }

	/**
	 * 获取图片地址
	 *
	 * @param boolean $removeRoute 是否去掉旋转参数
	 * @return void
	 */
    public function getOriginal($removeRoute = false)
    {
        $styleFlag = '!w600';
        $styleFlagA = '!w600A';
        $styleFlagB = '!w600B';
        $styleFlagC = '!w600C';

        $mediaUrl = $this->getMediaUrl();

		if (strpos($mediaUrl, $styleFlagA) > -1) {
			$mediaUrl = str_replace($styleFlagA, '!AA', $mediaUrl);
		} elseif (strpos($mediaUrl, $styleFlagB) > -1) {
			$mediaUrl = str_replace($styleFlagB, '!BB', $mediaUrl);
		} elseif (strpos($mediaUrl, $styleFlagC) > -1) {
			$mediaUrl = str_replace($styleFlagC, '!CC', $mediaUrl);
		} elseif (strpos($mediaUrl, $styleFlag) > -1) {
			$mediaUrl = str_replace($styleFlag, '', $mediaUrl);
		}

		$styleFlag = '!ww600';
        $styleFlagA = '!ww600A';
        $styleFlagB = '!ww600B';
        $styleFlagC = '!ww600C';

		if (strpos($mediaUrl, $styleFlagA) > -1) {
			$mediaUrl = str_replace($styleFlagA, '!AA', $mediaUrl);
		} elseif (strpos($mediaUrl, $styleFlagB) > -1) {
			$mediaUrl = str_replace($styleFlagB, '!BB', $mediaUrl);
		} elseif (strpos($mediaUrl, $styleFlagC) > -1) {
			$mediaUrl = str_replace($styleFlagC, '!CC', $mediaUrl);
		} elseif (strpos($mediaUrl, $styleFlag) > -1) {
			$mediaUrl = str_replace($styleFlag, '', $mediaUrl);
		}

		if ($removeRoute) {
			$mediaUrl = str_replace(array('!AA', '!BB', '!CC'), '', $mediaUrl);
		}

        return $mediaUrl;
    }
    
    public function renderMedia($thumb=false, $alt='', $htmlOptions=array(), $hasPermission=false)
    {
        $url = $this->getMediaUrl($thumb);
        
        $html = '';
        if ($thumb){
            $html = CHtml::image($url);
        }
        else {
            if ($this->type == 'video'){
                $html = $this->server == 1 ? MCHtml::flv($url) : MCHtml::mp4($url);
            }
            else {
                $htmlOptions['init']=$url;
                $html = MCHtml::weiboImage(Yii::app()->theme->baseUrl.'/images/pic_loading.gif', $alt, $htmlOptions, $hasPermission);
                
                $cs = Yii::app()->getClientScript();
                $js =  'function loadimage(){
                    var imgs = $("img[init]");
                    if(imgs.length<1) return;
                    for(var i=0;i<imgs.length;i++) {
                        if(imgs.eq(i).offset().top < ($(window).scrollTop()+document.documentElement.clientHeight) ) {
                            $(imgs.eq(i)).attr("src",imgs.eq(i).attr("init")).removeAttr("init");
                        }
                    }
                }
                $(window).load(function() {
                    loadimage();
                })
                $(window).scroll(function(){
                    loadimage();
                });';

                $cs->registerScript('Yii.ImgInit', $js);
            
            }
        }
        return $html;
    }

    public static function setStartYear($startYear)
    {
        self::$StartYear = $startYear;
    }

    public function delQiniuMedia($securityKey)
    {
        if($this->server == 20){
            if(OA::isProduction()){
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_POST, 1);
//                curl_setopt($ch, CURLOPT_URL, 'http://**************/qiniuNotify/delMedia');
                curl_setopt($ch, CURLOPT_URL, 'https://transfer.api.ivykids.cn/api/qiniu/delmedia');
                curl_setopt($ch, CURLOPT_POSTFIELDS, 'id='.$this->id.'&t='.self::$StartYear.'&sign='.md5($this->id.self::$StartYear.$securityKey));
                curl_setopt($ch, CURLOPT_RETURNTRANSFER , 1);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                $result = curl_exec($ch);
                return $result;
            }
            else{
                return 'success';
            }
        }
        return false;
    }
}