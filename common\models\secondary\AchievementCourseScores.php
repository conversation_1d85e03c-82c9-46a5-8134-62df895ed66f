<?php

/**
 * This is the model class for table "ivy_achievement_course_scores".
 *
 * The followings are the available columns in table 'ivy_achievement_course_scores':
 * @property integer $id
 * @property integer $kid
 * @property integer $weight
 * @property string $fraction
 * @property string $introduction_cn
 * @property string $introduction_en
 * @property integer $uid
 * @property integer $create_time
 * @property integer $update_time
 */
class AchievementCourseScores extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_achievement_course_scores';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			/*array('introduction_cn, introduction_en', 'required'),*/
			array('kid, weight, uid, create_time, update_time', 'numerical', 'integerOnly'=>true),
			array('fraction', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, kid, weight, fraction, introduction_cn, introduction_en, uid, create_time, update_time', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'kid' => 'Kid',
			'weight' => 'Weight',
			'fraction' => Yii::t('curriculum','分数'),
			'introduction_cn' => Yii::t('curriculum','Cn Desc'),
			'introduction_en' => Yii::t('curriculum','En Desc'),
			'uid' => 'Uid',
			'create_time' => 'Create Time',
			'update_time' => 'Update Time',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('kid',$this->kid);
		$criteria->compare('weight',$this->weight);
		$criteria->compare('fraction',$this->fraction,true);
		$criteria->compare('introduction_cn',$this->introduction_cn,true);
		$criteria->compare('introduction_en',$this->introduction_en,true);
		$criteria->compare('uid',$this->uid);
		$criteria->compare('create_time',$this->create_time);
		$criteria->compare('update_time',$this->update_time);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AchievementCourseScores the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	public function getName()
	{
		switch (Yii::app()->language) {
			case "zh_cn":
				return  $this->introduction_cn ;
				break;
			case "en_us":
				return  $this->introduction_en;
				break;
		}
	}
}
