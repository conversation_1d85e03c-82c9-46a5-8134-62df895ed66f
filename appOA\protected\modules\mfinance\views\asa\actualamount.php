<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo Yii::t('site','Finance Management');?></li>
        <li class="active">课后课财务</li>
    </ol>

    <div class="row">
        <!-- 左侧菜单 -->
        <div class="col-md-2 col-sm-2 mb10">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->getMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                'activeCssClass' => 'active',
                'itemCssClass' => ''
            ));
            ?>
        </div>

        <div class="col-md-10 col-sm-12">
            <form action="<?php echo $this->createUrl('queryAmount')?>" class="form-inline mb15 J_ajaxForm" method="post">
                <?php echo CHtml::dropDownList('schoolId', '', CHtml::listData($this->getAllBranch(), 'id', 'title'), array('class'=>'form-control form-group', 'empty'=>Yii::t('global', 'Please Select')));?>
                <?php echo CHtml::dropDownList('groupId', '', array(), array('class'=>'form-control form-group', 'empty'=>Yii::t('global', 'Please Select')));?>
                <button class="btn btn-primary J_ajax_submit_btn">查询</button>
                <div id='dao' class='pull-right'></div>
                <!-- <button onclick="daochu()" class="btn btn-primary btn-sm pull-right">导出表格</button> -->
            </form>
            <div class="row J_check_wrap" id="viewData"></div>
        </div>
    </div>
</div>
<script>
    var groups = <?php echo CJSON::encode($groups)?>;
    $('#schoolId').change(function(){
        $('#groupId').html('<option value=""><?php echo Yii::t('global', 'Please Select')?></option>');
        var options = groups[$(this).val()];
        if (options) {
            for(var id in options){
                $('<option></option>').val(options[id]['id']).text(options[id]['name']).appendTo( $('#groupId') );
            }
        }
    });

    function callback(data) {
        var html = '';
        var btn=''
        btn+='<button onclick="daochu()" type="button" class="btn btn-primary"><?php echo Yii::t('user','Excel Export'); ?></button>'
        html += '<div class="col-md-12" id="block">';
        html += '<div class="table-responsive">';
        html += '<table class="table table-bordered table-hover">';
        html += '<thead>';
        html += '<tr>';
        html += '<th class="text-center">ID</th>';
        html += '<th class="text-center"><?php echo Yii::t("principal","Course Title"); ?></th>';
        html += '<th class="text-center"><?php echo Yii::t("asa","Purchase"); ?></th>';
        html += '<th class="text-center"><?php echo Yii::t("asa","Refund"); ?></th>'; //退款金额
        html += '<th class="text-center"><?php echo Yii::t("asa","Revenue"); ?></th>';//有效收入
        html += '<th class="text-center"><?php echo Yii::t("asa","VAT (6%)"); ?></th>';//收入税款
        html += '<th class="text-center"><?php echo Yii::t("asa","Wechat Fee (0.6%)"); ?></th>';//微信税款
        html += '<th class="text-center"><?php echo Yii::t("asa","Staff Salary"); ?></th>';//考勤支出
        html += '<th class="text-center"><?php echo Yii::t("asa","Adjustiment"); ?></th>';//调整金额
        html += '<th class="text-center"><?php echo Yii::t("asa","Vendor Payment"); ?></th>';//付款金额
        html += '<th class="text-center"><?php echo Yii::t("labels","Reimbursement"); ?></th>';//报销金额
        html += '<th class="text-center"><?php echo Yii::t("asa","Profit"); ?></th>';//实际金额
        html += '<th class="text-center"><?php echo Yii::t("asa","Profit Margin"); ?></th>';//利润率
        html += '</tr>';
        html += '</thead>';
        html += '<tbody>';

        for (var key in data['data']) {
            html += '<tr>';
            html += '<td>' + key + '</td>';
            html += '<td>' + data['data'][key].title_cn + '<br>' + data['data'][key].title_en + '</td>';
            html += '<td class="text-right">' + data['data'][key].amount + '</td>';
            html += '<td class="text-right">' + data['data'][key].refund + '</td>';
            html += '<td class="text-right">' + data['data'][key].income + '</td>';
            html += '<td class="text-right">' + data['data'][key].wage + '</td>';
            html += '<td class="text-right">' + data['data'][key].wechat + '</td>';
            html += '<td class="text-right">' + data['data'][key].teacher + '</td>';
            html += '<td class="text-right">' + data['data'][key].variation + '</td>';
            html += '<td class="text-right">' + data['data'][key].vendor + '</td>';
            html += '<td class="text-right">' + data['data'][key].reimbursement + '</td>';
            html += '<td class="text-right">' + data['data'][key].actual + '</td>';
            html += '<td class="text-right">' + data['data'][key].margin + '</td>';
            html += '</tr>';
        }

        html += '</tbody>';
        html += '<tfoot>';
        html += '<tr>';
        html += '<th colspan="2"><?php echo Yii::t("asa","Total"); ?></th>';
        html += '<th class="text-right">' + data['total'][0] + '</th>';
        html += '<th class="text-right">' + data['total'][1] + '</th>';
        html += '<th class="text-right">' + data['total'][2] + '</th>';
        html += '<th class="text-right">' + data['total'][8] + '</th>';
        html += '<th class="text-right">' + data['total'][9] + '</th>';
        html += '<th class="text-right">' + data['total'][3] + '</th>';
        html += '<th class="text-right">' + data['total'][4] + '</th>';
        html += '<th class="text-right">' + data['total'][5] + '</th>';
        html += '<th class="text-right">' + data['total'][6] + '</th>';
        html += '<th class="text-right">' + data['total'][7] + '</th>';
        html += '<th class="text-right">' + data['total'][10] + '</th>';
        html += '</tr>';
        html += '</tfoot>';
        html += '</table>';
        html += '</div>';

        $('#viewData').html(html);
        $('#dao').html(btn);
    }
    function daochu(){
        var elt = document.getElementById('block');
        var wb = XLSX.utils.table_to_book(elt, {sheet:"课程收入"});
        return XLSX.writeFile(wb, '课程收入.xlsx');
    }
</script>