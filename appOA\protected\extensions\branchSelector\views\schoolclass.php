<div class="h_a"><?php echo Yii::t('teaching', 'Teaching Management');?></div>
<div class="table_full">
    <table width="100%">
        <col class="th" />
        <col />
		<?php if($this->controller->showHistory):?>
		<tr>
			<th><?php echo Yii::t('teaching','School Year');?></th>
			<td>
				<?php
					$endPoint = mktime(0,0,0,9,1,date('Y'));
					$time = time();
					$defaultStartYear = date('Y');
					
					if($time < $endPoint){
						$defaultStartYear = $defaultStartYear -1;
					}
					
					$history = 3;
					
					for($i=1; $i<=$history; $i++){
						$_i = $defaultStartYear - $i;
						$startYears[$defaultStartYear - $i] = sprintf('%s - %s', $_i, $_i + 1);
					}
					
					echo CHtml::dropDownList('historystartyear', $this->controller->historyStartYear, $startYears,
						array(
							'empty'=>'Select a School Year',
							'onchange'=>'javascript:window.location.href="'.$this->controller->createUrl($this->linkRoute, array('showHistory'=>1)).'"+"&historyStartYear="+$(this).val()',
							'class'=> 'mr15'
						)
					);
					
					echo CHtml::link(Yii::t('teaching','Back to Current'), array('//teaching/default/index'));
					
				?>
			</td>
		</tr>
		<?php endif;?>
		
		<?php
		//如果不显示历史数据，显示下面内容；
		//如果显示历史数据，并且指定了历史数据的年份，显示下面内容
		if( (!$this->controller->showHistory) || (($this->controller->showHistory) && ($this->controller->historyStartYear)) ):
		?>
			<tr>
				<th><?php echo Yii::t('teaching', 'Class and Week');?></th>
				<td>
					<div class="yarnball mr20 fl">
						<ul class="cc">
							<li id="J_yarnball_province" class="first" style="<?php echo $route['school']['display'];?>"><a class="J_yarnball" href="" data-type="province" data-id="<?php echo $route['school']['areaid']?>"><?php echo $route['school']['name']?></a></li>
							<li id="J_yarnball_city" class="middle" style="<?php echo $route['class']['display'];?>"><a class="J_yarnball" href="" data-id="<?php echo $route['class']['areaid']?>"><?php echo $route['class']['name']?></a></li>
							<li id="J_yarnball_week" class="last" style="<?php echo $route['week']['display'];?>"><a class="J_yarnball" href="" data-id="<?php echo $route['week']['areaid']?>"><?php echo $route['week']['name']?></a></li>
						</ul>
					</div>
					<?php if ($hasLevel):?>
					<a href="#" id="J_region_set" data-change="change" style="float:left;margin-top:7px;"><?php echo Yii::t('global', 'Re-Select>>');?></a>
					<?php else:?>
					<a href="#" id="J_region_set" style="float:left;margin-top:7px;"><?php echo Yii::t('global', 'Please Select>>');?></a>
					<?php endif;?>
				</td>
			</tr>
			<?php if(!empty($schoolid) && !empty($classid) && !empty($weeknum)):?>
			<tr>
				<th><?php echo Yii::t('teaching', 'Teaching Tasks');?></th>
				<td>
					<?php
						$items = array();
						foreach($this->controller->teachingTasks as $k=>$v){
							$items[$k] = array(
								'label' => $v,
								'url' => array(
									$this->linkRoute,
									'task'=>$k,
									'schoolid'=>$schoolid,
									'classid'=>$classid,
									'weeknum'=>$weeknum
								),
								'linkOptions' => array(
									'id' => 'lnk-' . $k
								)
							);
							if($showHistory) $items[$k]['url']['showHistory'] = $showHistory;
							if($historyStartYear) $items[$k]['url']['historyStartYear'] = $historyStartYear;
						}
						$this->widget('zii.widgets.CMenu', array(
							'id'=>'t-tasks',
							'items' => $items
						));
					?>
				</td>
			</tr>
			<?php endif;?>
		<?php endif;?>
    </table>
</div>

<!--=========================弹窗============================-->
<div class="core_pop_wrap" id="J_region_pop" style="display:none;">
	<div class="core_pop">
		<form action="<?php if(!empty($this->linkRoute))echo $this->controller->createUrl($this->linkRoute);?>" method="get">
		<div style="width:600px;">
			<div class="pop_top">
				<a href="" id="J_region_pop_x" class="pop_close"><?php echo Yii::t('global','Close');?></a>
				<strong><?php ;?><?php echo Yii::t('teaching','Class and Week');?></strong>
			</div>
			<div class="pop_cont">
				<div class="pop_table" style="height:auto;">
					<table width="100%">
						<tr>
							<th>
								<?php echo Yii::t('teaching','Campus');?>
							</th>
							<td>
								<div class="pop_region_list">
									<ul id="J_region_pop_province" class="cc"></ul>
								</div>
							</td>
						</tr>
						<tr>
							<th>
								<?php echo Yii::t('teaching','Class');?>
							</th>
							<td>
								<div class="pop_region_list">
									<ul id="J_region_pop_city" class="cc">
										<li><em class="gray"><?php echo Yii::t('teaching','No class found.');?></em></li>
									</ul>
								</div>
							</td>
						</tr>
						<tr>
							<th>
								<?php echo Yii::t('teaching','Week');?>
							</th>
							<td>
								<ul><div id="week"></div></ul>
							</td>
						</tr>
						<tr>
							<th>
								<?php echo Yii::t('teaching',"Tasks");?>
							</th>
							<td>
								<div class="pop_region_list">
									<ul class="cc">
									<?php
									echo CHtml::radioButtonList("task", $task, $this->controller->teachingTasks,
										array(
											'separator'=> ' ',
											'template' => '<li><span>{input}{label}</span></li>'
										)
									);
									?>
									</ul>
								</div>
							</td>
						</tr>
						<tr>
							<th></th>
							<td><?php echo CHtml::link(Yii::t('teaching', 'View historical data.'), array('//teaching/default/index', 'showHistory'=>1), array('target'=>'_blank'));?></td>
						</tr>
					</table>
				</div>
			</div>
			<div class="pop_bottom tac">
                <?php echo CHtml::hiddenField('schoolid');?>
                <?php echo CHtml::hiddenField('classid');?>
                <?php echo CHtml::hiddenField('weeknum');?>
                <?php if($showHistory) echo CHtml::hiddenField('showHistory', $showHistory);?>
                <?php if($historyStartYear) echo CHtml::hiddenField('historyStartYear', $historyStartYear);?>
				
                <button id="J_region_pop_close" type="submit" class="btn fr"><?php echo Yii::t('global','Cancel');?></button>
				<button type="submit" class="btn btn_submit mr10 fr" id="J_region_pop_sub"><?php echo Yii::t('global','OK');?></button>
                <div class="tips_error" style="display:none;" id="J_etip"><?php echo Yii::t('teaching','Select both campus and class.');?></div>
			</div>
		</div>
		</form>
	</div>
</div>
<!--===========================结束==========================-->
<?php
 $this->widget('zii.widgets.jui.CJuiDatePicker', array(
     'name'=>'week',
     // additional javascript options for the date picker plugin
     'options'=>array(
		'showWeek'=>true,
		'showOtherMonths'=>true,
		'selectOtherMonths'=>true,
		'calculateWeek'=>'js:fisc',
		'onSelect'=>'js:onSelect',
		'beforeShowDay'=>'js:beforeShowDay',
        'numberOfMonths'=>2,
     ),
     'htmlOptions'=>array(
		 'class'=>'dn',
     ),
 ));
 ?>

<script>
head.use('dialog', 'draggable', function() {

    var REGION_CONFIG = <?php echo CJSON::encode($schoolclass)?>;
    var myClass = <?php echo CJSON::encode($myClasses);?>;
    var calendarIds = <?php echo CJSON::encode($calendarIds);?>;
    var calendarse = <?php echo CJSON::encode($calendarse);?>;
	
	var region_set = $('#J_region_set'),
			region_pop = $('#J_region_pop'),
			region_pop_province = $('#J_region_pop_province'),
			region_pop_city = $('#J_region_pop_city'),
			region_list = $('#J_region_list'),
			parentid = $('#J_parentid');
	
	//引入弹窗拖动组件
	region_pop.draggable( { handle : '.pop_top'} );
	
	var yarnball_all = $('#J_yarnball_all'),
			yarnball_province = $('#J_yarnball_province'),
			yarnball_city = $('#J_yarnball_city'),
			yarnball_week = $('#J_yarnball_week');
	
	$('#J_yarnball_list').children().hover(function(){
		$(this).addClass('hover');
	}, function(){
		$(this).removeClass('hover');
	});
	
	region_set.on('click', function(e){
		e.preventDefault();
		var $this = $(this), province_arr = [];
		
		if(!REGION_CONFIG) {
			return false;
		}
		$.each(REGION_CONFIG, function(i, o){
			province_arr.push('<li id="J_p_'+ i +'" data-id="'+ i +'"><a href="#" class="J_province">'+ o.title +'</a></li>');
		});
		
		region_pop_province.html(province_arr.join(''));
		
        if (!$.isEmptyObject(myClass)){
            region_pop_city.html('<li><span class="org"><?php echo Yii::t('teaching','My Class');?></span></li>');
        }
        else {
//            region_pop_city.html('<li><span><?php //echo Yii::t('teaching','Classes');?></span></li>');
        }
		
        <?php if($historyStartYear):?>
        var cDate = new Date();
        cDate.setMonth(mm);
        cDate.setDate(dd);
        cDate.setYear(yy);
        $( "div#week" ).datepicker('setDate', cDate);
        <?php endif;?>
		
		if($this.data('change')) {
			var current_p_id = yarnball_province.children('a.J_yarnball').data('id'),
				current_p_name = yarnball_province.children('a.J_yarnball').text(),
				current_c_id = yarnball_city.children('a.J_yarnball').data('id'),
				weeknum = yarnball_week.children('a.J_yarnball').data('id'),
				current_city_arr = [],
				current_my_arr = [];

			//当前省状态
			$('#J_p_'+current_p_id).addClass('current').siblings('li.current').removeClass('current');

			var items = REGION_CONFIG[current_p_id]['items'];
			if(!items) {
				region_pop_city.html('<li><em class="gray"><?php echo Yii::t('teaching','No class found.');?></em></li>');
			}else{
				$.each(items, function(i, o){
                    if(typeof(myClass[i]) === 'undefined'){
                        current_city_arr.push('<li id="J_c_'+ i +'" data-id="'+ i +'"><a href="#" class="J_city">'+ o +'</a></li>');
                    }
                    else {
                        current_my_arr.push('<li id="J_c_'+ i +'" data-id="'+ i +'"><a href="#" class="J_city">'+ o +'</a></li>');
                    }
				});
                if (!$.isEmptyObject(myClass)){
                    region_pop_city.html('<li data-cname=""><span class="org"><?php echo Yii::t('teaching','My Class');?></span></li>'+ current_my_arr.join('')+'<li class="c"></li><li data-cname=""><span class="org"><?php echo Yii::t('teaching','Other Classes');?></span></li>'+current_city_arr.join(''));
                }
                else {
                    region_pop_city.html(current_city_arr.join(''));
                }
			}

			if(current_c_id) {
				$('#J_c_'+current_c_id).addClass('current').siblings('li.current').removeClass('current');
			}
			
            var cal = calendarse[calendarIds[current_p_id]];
            $( "div#week" ).datepicker( "option", "minDate", cal['s'] );
            $( "div#week" ).datepicker( "option", "maxDate", cal['e'] );
            mm = cal['s'].substring(0, 2)-1;
            dd = cal['s'].substring(3, 5);
		
            $( "div#week" ).datepicker('refresh');
            $( "div#week" ).datepicker('setDate', fisc1(cal['s'], weeknum));
            
//            $('td.ui-datepicker-week-col:contains('+weeknum+')').parent('tr').find('td a').addClass('ui-state-active');
            $('td.ui-datepicker-week-col:contains('+weeknum+')').parent('tr').find('td a').click();
		}
		
		
		region_pop.show().css({
			left : ($(window).width() - region_pop.outerWidth())/2,
			top : ($(window).height() - region_pop.outerHeight())/2 + $(document).scrollTop()
		});
        $('#week').find('.ui-datepicker-today').siblings('td').find('a').click();
        $('#J_etip').hide();
	});
	
	
	region_pop_province.on('click', 'a.J_province', function(e){
		e.preventDefault();
		var city_arr = [],
            my_arr = [],
			$this = $(this),
			$li = $this.parent(),
			id = $li.data('id'),
			itmes = REGION_CONFIG[id]['items'];
		
		$li.addClass('current').siblings('li.current').removeClass('current');
        
		if(!itmes) {
			region_pop_city.html('<li><em class="gray"><?php echo Yii::t('teaching','No class found.');?></em></li>');
			return;
		}

		$.each(itmes, function(i, o){
            if(typeof(myClass[i]) === 'undefined'){
                city_arr.push('<li id="J_c_'+ i +'" data-id="'+ i +'"><a href="#" class="J_city">'+ o +'</a></li>');
            }
            else{
                my_arr.push('<li id="J_c_'+ i +'" data-id="'+ i +'"><a href="#" class="J_city">'+ o +'</a></li>');
            }
		});
			
        if (!$.isEmptyObject(myClass)){
            region_pop_city.html('<li data-cname=""><span class="org"><?php echo Yii::t('teaching','My Class');?></span></li>'+ my_arr.join('')+'<li class="c"></li><li data-cname=""><span class="org"><?php echo Yii::t('teaching','Other Classes');?></span></li>'+city_arr.join(''));
        }
        else {
            region_pop_city.html(city_arr.join(''));
        }

        var cal = calendarse[calendarIds[id]];
        $( "div#week" ).datepicker( "option", "minDate", cal['s'] );
        $( "div#week" ).datepicker( "option", "maxDate", cal['e'] );
        mm = cal['s'].substring(0, 2)-1;
        dd = cal['s'].substring(3, 5);
        $( "div#week" ).datepicker('refresh');
        $('td.ui-datepicker-today a').click();
        $('#J_etip').hide();
	});
	
	
	region_pop_city.on('click', 'a.J_city', function(e){
		e.preventDefault();
		var $li = $(this).parent();
		
		//切换点击状态
		$li.addClass('current').siblings('li.current').removeClass('current');
        $('#J_etip').hide();
		
	});
	
	$('#J_region_pop_sub').on('click', function(e){
		e.preventDefault();
        
		var p_li = $('#J_region_pop_province > li.current'),
			p_name = p_li.data('pname'),
			p_id = p_li.data('id'),
			c_li = $('#J_region_pop_city > li.current'),
			c_name = c_li.data('cname'),
			c_id = c_li.data('id'),
			region_arr = [],
			p_c_set = false,
            weeknum = $('input#week').val();
			
		if(!p_id || !c_id){
            $('#J_etip').show();
            return false;
        }
        
        $("#schoolid").val(p_id);
        $("#classid").val(c_id);
        $("#weeknum").val(weeknum);
        this.form.submit();
        return;
        
        if(c_id) {
			var items = REGION_CONFIG[p_id]['items'][c_id]['items'];

			if(!items) {
				region_arr = [];
			}else{
				$.each(items, function(i, o){
					//region_arr.push( tr_district.replace(/_ID/g, i).replace(/_TEXT/, o) );
				});
			}
				
			yarnball_province.show().removeClass('li_disabled').find('a.J_yarnball').text(p_li.text()).data('id', p_id);
			yarnball_city.show().find('a.J_yarnball').text(c_li.text()).data('id', c_id);
			parentid.val(c_id);
		}else{
			var items = REGION_CONFIG[p_id]['items'];

			if(!items) {
				region_arr = [];
			}else{
				$.each(items, function(i, o){
					//region_arr.push( tr_city.replace(/_ID/g, i).replace(/_TEXT|_CNAME/g, o) );
				});
			}
			
				
			yarnball_province.show().addClass('li_disabled').find('a.J_yarnball').text(p_li.text()).data('id', p_id);
			yarnball_city.hide().children('a.J_yarnball').removeData('id');
			parentid.val(p_id);
		}
        
        if (weeknum){
            yarnball_city.removeClass('li_disabled');
            $('#J_yarnball_week').show().addClass('li_disabled').find('a.J_yarnball').text('Week '+weeknum).data('id', weeknum);
        }
		
		yarnball_all.removeClass('li_disabled');
		p_c_set = true;

		
		//写入列表
		region_list.html(region_arr.join(''));
		
		region_pop.hide(0, function(){
			if(p_c_set) {
				region_set.data('change', 'change').text('<?php echo Yii::t('global', 'Re-Select>>');?>');
			}else{
				region_set.removeData('change').text('<?php echo Yii::t('global', 'Please Select>>');?>');
			}
		});
		
	});
	
	
	$('#J_region_pop_x, #J_region_pop_close').on('click', function(e){
		e.preventDefault();
        $( "div#week" ).datepicker('refresh');
		region_pop.hide();
	});

	
	$('a.J_yarnball').on('click', function(e){
		e.preventDefault();
	});

	region_list.on('click', 'a.J_region_next', function(e){
		e.preventDefault();
	});

});

var startDate;
var endDate;
var mm = 7;
var dd = 31;
<?php if($historyStartYear):?>
var yy = <?php echo $historyStartYear;?>;
<?php endif;?>
var selectCurrentWeek = function() {
    window.setTimeout(function () {
        $('#week').find('.ui-datepicker-current-day a').addClass('ui-state-active')
    }, 1);
}
function fisc(date) {
    var checkDate = new Date(date.getTime());
	//以周四为计算基准
    checkDate.setDate(checkDate.getDate() + 4 - (checkDate.getDay() || 7));
    var time = checkDate.getTime();
    var yearStart = new Date();
    yearStart.setMonth(mm); //0-11表示1-12
    yearStart.setDate(dd);
	<?php if($historyStartYear):?>
    yearStart.setYear(yy); //0-11表示1-12
	<?php endif;?>
    var week = (Math.floor(Math.round((time - yearStart) / 86400000) / 7) + 2);
    if (week < 1) {
        week = 52 + week;
    }
    return week;
}
function fisc1(st, weeknum) {
    var sTime = new Date(st);
    var sday = 6-sTime.getDay();
    if (sday !== 2){
        sTime.setDate(parseInt(sTime.getDate())+parseInt(sday-2));
    }
    sTime.setTime(sTime.getTime() + 86400000 * 7 * (weeknum-1));
    
    return sTime;
}
function onSelect(dateText, inst) {
    var date = $(this).datepicker('getDate');
    startDate = new Date(date.getFullYear(), date.getMonth(), date.getDate() - date.getDay());
    endDate = new Date(date.getFullYear(), date.getMonth(), date.getDate() - date.getDay() + 6);
    var dateFormat = inst.settings.dateFormat || $.datepicker._defaults.dateFormat;
//    $('#startDate').text($.datepicker.formatDate( dateFormat, startDate, inst.settings ));
//    $('#endDate').text($.datepicker.formatDate( dateFormat, endDate, inst.settings ));

    selectCurrentWeek();
    
    var weeknum = fisc(startDate);
    $('input#week').val(weeknum);
    $('input#week').attr('startDate', $.datepicker.formatDate( dateFormat, startDate, inst.settings ));
}
function beforeShowDay(date) {
    var cssClass = '';
    if(date >= startDate && date <= endDate)
        cssClass = 'ui-datepicker-current-day';
    return [true, cssClass];
}
$('.ui-datepicker-calendar tr').live('mousemove', function() { $(this).find('td a').addClass('ui-state-hover'); });
$('.ui-datepicker-calendar tr').live('mouseleave', function() { $(this).find('td a').removeClass('ui-state-hover'); });

</script>
<style type="text/css">
.yarnball ul li.first a{
    background-position:right 0px;
}
.yarnball ul li.middle a{
    background-position:right -30px;
}
.yarnball ul li.last a{
    background-position:right -60px;
}
#t-tasks li{
	float: left;
	margin-right: 4px;
}
#t-tasks li a{
	padding: 2px 6px;
	border-radius: 2px;
}
#t-tasks li.active a{
	background: #333;
	color:#fff;
}
</style>