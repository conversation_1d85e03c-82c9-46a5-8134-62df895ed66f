<?php

/**
 * This is the model class for table "ivy_child_bank_account".
 *
 * The followings are the available columns in table 'ivy_child_bank_account':
 * @property integer $childid
 * @property string $bank_city
 * @property string $bank_name
 * @property string $bank_account
 * @property string $bank_user
 * @property string $bank_exta
 * @property integer $updated_at
 * @property integer $updated_by
 */
class ChildBankAccount extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_child_bank_account';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('childid, bank_city, bank_name, bank_account, bank_user, updated_at, updated_by', 'required'),
			array('childid, updated_at, updated_by', 'numerical', 'integerOnly'=>true),
			array('bank_city, bank_name, bank_account, bank_user, bank_exta', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('childid, bank_city, bank_name, bank_account, bank_user, bank_exta, updated_at, updated_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'childid' => 'Childid',
			'bank_city' => 'Bank City',
			'bank_name' => 'Bank Name',
			'bank_account' => 'Bank Account',
			'bank_user' => 'Bank User',
			'bank_exta' => 'Bank Exta',
			'updated_at' => 'Updated At',
			'updated_by' => 'Updated By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('childid',$this->childid);
		$criteria->compare('bank_city',$this->bank_city,true);
		$criteria->compare('bank_name',$this->bank_name,true);
		$criteria->compare('bank_account',$this->bank_account,true);
		$criteria->compare('bank_user',$this->bank_user,true);
		$criteria->compare('bank_exta',$this->bank_exta,true);
		$criteria->compare('updated_at',$this->updated_at);
		$criteria->compare('updated_by',$this->updated_by);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return ChildBankAccount the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
