function sheet2blob(sheet, sheetName) {
    let wb = XLSX.utils.book_new()
    wb.SheetNames.push(sheetName)
    wb.Sheets[sheetName] = sheet
    var wbout = xlsxStyle.write(wb, { bookType: '', bookSST: false, type: 'binary' })
    var blob = new Blob([s2ab(wbout)], { type: "" },sheetName)
    // 字符串转ArrayBuffer
    function s2ab(s) {
        var buf = new ArrayBuffer(s.length)
        var view = new Uint8Array(buf)
        for (var i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xff
        return buf
    }
    return blob
}
function openDownload(url, saveName) {
    if (typeof url == "object" && url instanceof Blob) {
        url = URL.createObjectURL(url) // 创建blob地址
    }
    var aLink = document.createElement("a")
    aLink.href = url
    aLink.download = saveName || ""; // HTML5新增的属性，指定保存文件名，可以不要后缀，注意，file:///模式下不会生效
    var event
    if (window.MouseEvent) event = new MouseEvent("click")
    else {
        event = document.createEvent("MouseEvents")
        event.initMouseEvent("click",true,false,window,0,0,0,0,0,false,false,false,false,0,null)
    }
    aLink.dispatchEvent(event);
}
