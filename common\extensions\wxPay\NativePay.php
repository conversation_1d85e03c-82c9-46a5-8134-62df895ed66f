<?php
class NativePay extends CWidget {

    public $cfg = array();

    public $values = array();

    private $input;

    public function init()
    {
        Yii::import('common.extensions.wxPay.lib.*');

        $this->input = new WxPayMicroPay();
        if (!isset($this->values['trade_type'])) {
            $this->values['trade_type'] = 'NATIVE';
        }

        $this->input->cfg = $this->cfg;
        $this->input->values = $this->values;
    }

    public function run()
    {
        return $this;
    }

    /**
     * 生成二维码
     * @param string $value [description]
     */
    public function createQRcode()
    {
        WxPayApi::unifiedOrder($this->input);
        return $this->input->values;
    }

    /**
     * 货币转换
     * @param  integer $money [description]
     * @return [type]         [description]
     */
    public function transformMoney($money = 0)
    {
        $money = intval($money*100);
        if ($money != 0) {
            return $money;
        }
        return false;
    }

    /**
     * XML转换
     * @param [type] $xml [description]
     */
    public function fromXml($xml)
    {
        $this->values = $this->input->FromXml($xml);
        return $this->values;
    }

    /**
     * 验证签名
     */
    public function checkSign()
    {
        return $this->input->CheckSign();
    }

    /**
     * 模拟微信的异步通知
     * @param [type] $arr [description]
     */
    public function simulation($arr)
    {
        $url = "http://apps.mims.cn/wechatPay/wechatpay";
        $xml = "<xml>";
        foreach ($arr as $key=>$val)
        {
            if (is_numeric($val)){
                $xml.="<".$key.">".$val."</".$key.">";
            }else{
                $xml.="<".$key."><![CDATA[".$val."]]></".$key.">";
            }
        }
        $xml.="</xml>";
        // //初始化curl        
        // $ch = curl_init();
        // //设置超时
        // curl_setopt($ch, CURLOPT_TIMEOUT, 8);
        // curl_setopt($ch,CURLOPT_URL, $url);
        // curl_setopt($ch,CURLOPT_SSL_VERIFYPEER,FALSE);
        // curl_setopt($ch,CURLOPT_SSL_VERIFYHOST,FALSE);
        // //设置header
        // curl_setopt($ch, CURLOPT_HEADER, FALSE);
        // //要求结果为字符串且输出到屏幕上
        // curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);

        // //post提交方式
        // curl_setopt($ch, CURLOPT_POST, TRUE);
        // curl_setopt($ch, CURLOPT_POSTFIELDS, $xml);
        // //运行curl
        // $data = curl_exec($ch);
        // //返回结果
        // var_dump($xml);die();
        // if($data){
        //     curl_close($ch);
        //     return $data;
        // } else { 
        //     $error = curl_errno($ch);
        //     curl_close($ch);
        //     return false;
        // }
        $header = "Content-type: text/xml";//定义content-type为xml
        $ch=curl_init();
        curl_setopt($ch, CURLOPT_TIMEOUT, 6);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch,CURLOPT_URL,$url);
        curl_setopt($ch,CURLOPT_POST,TRUE);
        curl_setopt($ch,CURLOPT_POSTFIELDS,$xml);
        ob_start();
        curl_exec($ch);
        $result=ob_get_contents();
        ob_end_clean();
        curl_close($ch);
        return $result;
    }
    
    /**
     * 数组转换为xml
     * @param  [type] $arr [array]
     * @return [type] $xml [string]
     */
    public function toXml($arr)
    {
        $xml = "<xml>";
        foreach ($arr as $key=>$val)
        {
            if (is_numeric($val)){
                $xml.="<".$key.">".$val."</".$key.">";
            }else{
                $xml.="<".$key."><![CDATA[".$val."]]></".$key.">";
            }
        }
        $xml.="</xml>";
        echo $xml; 
    }
}