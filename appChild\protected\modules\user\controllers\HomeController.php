<?php

class HomeController extends Controller
{
	public $defaultAction = 'index';
	public $layout = '//layouts/auth.access';

	/**
	 * Displays the multiple children selection page
	 */
	public function actionIndex()
	{
		if ( UserModule::checkLogin() ){
			$children = ChildProfileBasic::model()->findAllByPk(Yii::app()->user->getAdminCids());
			//判断只有小学的孩子才能登陆
			if ( count($children) === 1){
				$flag = CommonUtils::isGradeSchool($children[0]->classid, true);
				if ($flag === true){
					$this->redirect(Yii::app()->params['gradeUrl']."/".$children[0]->childid);
				}else{
					if ($children[0]->schoolid == 'BJ_DS' && $flag === false){
						$this->redirect(Yii::app()->params['casaUrl']."/".$children[0]->childid);
					}else{
						$this->redirect(array("//child/profile/welcome", "childid"=>$children[0]->childid));
					}
				}
			}elseif( count($children) > 1){
				$ivy = true;
				foreach ($children as $cn){
					if ($cn->schoolid == 'BJ_DS'){
						$ivy = false;
						break;
					}
				}
				if ($ivy){
					$this->render('/user/select',array('children'=>$children));
				}else{
					$this->redirect(Yii::app()->params['gradeUrl']."/user/home");
				}
			}else{
				Yii::app()->user->logout();
				$this->redirect(Yii::app()->controller->module->returnLogoutUrl);
			}
		}
	}

}