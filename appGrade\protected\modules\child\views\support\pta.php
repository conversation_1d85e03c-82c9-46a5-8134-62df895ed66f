<?php
$this->breadcrumbs = array(
    Yii::t("navigations", 'Support') => array('/child/support'),
    Yii::t("navigations", 'Parent Teacher Association'),
);

$colorbox = $this->widget('application.extensions.colorbox.JColorBox');
$colorbox->addInstance('.colorbox', array('iframe' => false, 'width' => '800', 'height' => '80%'));
?>

<h1><label><?php echo Yii::t("navigations", "Parent Teacher Association") ?></label></h1>

<div class="span-12 colborder">
    
        <?php if (Yii::app()->language == "en_us"): ?>
            <p class="desc">
            The PTA in Schools is an important organization whose aim is improve the overall educational and service quality at Ivy Schools. The PTA will work with the school management team, representing the interests and wills of Ivy families.</p>
            <p class="desc bm0">The PTA operates on the following key principles:
            <ul class="desc withIndent">
                <li>Transparency. All decisions and processes shall be documented and open for public review</li>
                <li>Everything should revolve around what is best for the children</li>
                <li>Be compliant with governmental regulations and make recommendations in line with our mission and goals</li>
            </ul>

        <?php else: ?>
            <p class="desc">
            幼儿园家长教师协会是以促进幼儿良好发展为主要目的，在幼儿园园务委员会领导下，代表广大家长意愿，配合幼儿园开展教育活动，提高办园质量的群众性组织机构。</p>
            <p class="desc bm0">家委会的主要原则包括：</p>
            <ul class="desc withIndent">
                <li>透明化，一切决定和过程都应该有记录，并接受广大家长的监督</li>
                <li>家长教师协会的一切决策活动会紧密围绕幼儿园的办园目标，以有利于幼儿教育、有利于幼儿发展为基本原则</li>
                <li>家长教师协会工作应符合政府规定及幼儿园办学宗旨</li>
            </ul>
        <?php endif; ?>
    
    <h2><?php echo Yii::t("support", "PTA Committee Members");?></h2>
    <?php
    if (false === $noservice) {
        Mims::LoadHelper('HtoolKits');
        if (!empty($pta_info)):
            foreach ($class_info as $_class):
                foreach($pta_info[$_class->classid] as $pta):
                ?>
                <dl class="pta-list">
                    <dt>
                    <img class="face" src="<?php echo $parent_info[$pta->parent_id]->user->getPhotoSubUrl(); ?>" />
                    </dt>
                    <dd class="content">
                        <h3><span>
                                <?php
                                $class_id = $pta->classLink->classid;
                                if (isset($class_info[$class_id])) {
                                    echo $class_info[$class_id]->title;
                                }
                                ?>
                            </span></h3>
                        <ul class="info">

                            <?php
                            $parent = null;
                            if (isset($parent_info[$pta->parent_id])):
                                $parent = $parent_info[$pta->parent_id];
                                ?>

                                <li><label><?php echo addColon(Yii::t("auth", "Name of Child")); ?></label><?php echo $pta->childInfo->getChildName(true, true);?> </li>
                                <li><label><?php echo addColon(Yii::t("labels", "Name")); ?></label> <?php echo HtoolKits::getContentByLang($parent->cn_name, $parent->en_firstname . ' ' . $parent->en_lastname) ?></li>
                                <li><label><?php echo addColon(Yii::t("labels", "Gender")); ?></label>

                                    <?php
                                    if ($parent->gender == 2) {
                                        echo Yii::t("userinfo", "Female");
                                    } else {
                                        echo Yii::t("userinfo", "Male");
                                    }
                                    ?></li>
                                <li><label><?php echo addColon(Yii::t("labels", "Nationality")); ?></label>
                                    <?php
                                    if (isset($parent->countryInfo)) {
                                        echo $parent->countryInfo->getCountryName();
                                    }
                                    ?>
                                </li>
                                <li><label><?php echo addColon(Yii::t("labels", "Email")); ?></label>
                                    <?php
                                    echo strtolower($parent->user->email);
                                    ?>
                                </li>
                                <li><label><?php echo addColon(Yii::t("labels", "Mobile Phone")); ?></label> <?php echo HtoolKits::getContentByLang($parent->mphone, $parent->tel, 'cn'); ?></li>

                            <?php else: ?>

                                <li><label><?php echo addColon(Yii::t("labels", "Name")); ?></label> </li>
                                <li><label><?php echo addColon(Yii::t("labels", "Gender")); ?></label>   </li>
                                <li><label><?php echo addColon(Yii::t("labels", "Nationality")); ?></label>  </li>
                                <li><label><?php echo addColon(Yii::t("labels", "Email")); ?></label>  </li>
                                <li><label><?php echo addColon(Yii::t("labels", "Mobile Phone")); ?></label>  </li>

                            <?php
                            endif;
                            ?>

                        </ul>
                    </dd>
                    <dd class="clear"></dd>
                </dl>
                <?php
            endforeach;
            endforeach;
        else:
            ?>

            <div class="flash-warning">
            <?php
        $cst_link = CHtml::link(Yii::t("userinfo", "contact"), $this->createUrl('//child/support/email'));
        echo Yii::t("support", "Parent Teacher Association is not yet established at your campus, or it has not been set up on System. Please :contact campus support team for more details.", array(':contact' => $cst_link));
            ?>
            </div>

        <?php
        endif;
    }else {
        echo '<ul><li class="noservice">';
        echo Yii::t("support", 'PTA list is not visible to visitors.');
        echo '</li></ul>';
    }
    ?>
</div>

<div class="span-5 last">
    <?php if(!Mims::unIvy()):?>
    <ul class="pta-items">
        <li>
            <span><a href="<?php echo $this->createUrl('//child/support/ptaGuidelines'); ?>" class="colorbox"><span><?php echo Yii::t("support", 'PTA Guidelines'); ?></span></a></span>
        </li>
    </ul>
    <?php endif;?>
    <?php if ($memos):?>
    <h4 class="bom-x"><?php echo Yii::t("support", 'PTA Meeting Minutes')?></h4>
    <ul class="mome-items">
        <?php foreach($memos as $memo):?>
        <li><a href="<?php echo $this->createUrl('//child/support/ptaMemoview', array('id'=>$memo->id)); ?>" class="colorbox group1"><?php echo $memo->renderTitle();?></a> <span><?php echo Mims::formatDateTime($memo->memo_date)?></span></li>
        <?php endforeach;?>
    </ul>
    <script>$(".group1").colorbox({rel:'group1', current:'{current} / {total}'});</script>
    <?php endif;?>
</div>

<style>
    img.face{
        border: 5px solid #FFFFFF;
        box-shadow: 2px 2px 3px #666666;
        width: 80px;
        margin: 0 20px 0 0;
    }
    .pta-list dl{
        clear:both;
        width: 100%;
        margin-bottom: 1em;
    }
    .pta-list dt{
        float: left;
        width: 110px;
    }
    .pta-list dd.content{
        margin: 0 0 0 110px;
        position: relative;
        border-top: 1px dotted #ccc;
        padding: 4px;
    }
    .pta-list dd h3{
        position: absolute;
        top: -20px;
        right: 0;
        text-align:right;
        background: #fff;
        color:#333;
        padding: 4px;
        font-size: 14px;
    }
    .pta-list dl:hover{
        background:#fff;
    }
    .pta-list dd:hover{
        border-top: 1px dotted #F36601;
    }
    .pta-list dd:hover h3{
        background: #F36601;
        color:#fff;
    }
    ul.info li{
        line-height: 24px;
    }
    .pta-list dd:hover ul.info li{
        color:#000;
    }
    ul.info li label{
        color:#999;
        font-weight: bold;
        margin-right: 4px;
    }
    .pta-items li span a{
        text-decoration:none;
    }
    .pta-items li span a span{
        display: block;
        text-align: center;
        padding: 20px 0;
        background: #F36601;
        margin-bottom: 6px;
        font-size: 20px;
        color:#fff;
    }
    .pta-items{
        margin-bottom: 50px;
    }
    .mome-items{
        
    }
    .mome-items li{
        text-overflow: ellipsis;
        width: 190px;
        line-height: 25px;
    }
    h4.bom-x{
        margin-bottom: 5px;
    }
    .sub-desc{
        border-bottom: 1px solid #dedede;
        text-align: right;
        color: #999;
    }
</style>