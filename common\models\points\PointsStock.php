<?php

/**
 * This is the model class for table "ivy_points_stock".
 *
 * The followings are the available columns in table 'ivy_points_stock':
 * @property integer $id
 * @property integer $product_id
 * @property integer $num
 * @property string $memo
 * @property integer $userid
 * @property integer $update_timestamp
 */
class PointsStock extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return PointsStock the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_points_stock';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('num, product_id', 'required'),
			array('product_id, num, userid, update_timestamp', 'numerical', 'integerOnly'=>true),
			array('memo', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, product_id, num, memo, userid, update_timestamp', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'Product'=>array(self::BELONGS_TO,'PointsProduct','product_id','select'=>'en_title,cn_title,cover'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => Yii::t('operations', '标识'),
			'product_id' => Yii::t('operations', '产品'),
			'num' => Yii::t('operations', '数量'),
			'memo' => Yii::t('operations', '备注'),
			'userid' => Yii::t('operations', '操作人'),
			'update_timestamp' => Yii::t('operations', '日期'),
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('product_id',$this->product_id);
		$criteria->compare('num',$this->num);
		$criteria->compare('memo',$this->memo,true);
		$criteria->compare('t.userid',$this->userid);
		$criteria->compare('update_timestamp',$this->update_timestamp);
		$criteria->with = 'Product';
        $criteria->order='update_timestamp DESC';
		return new CActiveDataProvider(get_class($this), array(
			'criteria'=>$criteria,
		));
	}
	
	/**
	 * 统计某产品当前库存数
	 * @param int $productId
	 * @return number
	 */
	public function countStock($productId)
	{
		$model = $this->findAllBySql('select sum(num) as num from '.$this->tableName().' where product_id=:product_id', array(':product_id'=>$productId));
		$products = PointsOrder::model()->getProductOrder($productId);
		return (!empty($model[0]->num)) ? $model[0]->num - $products : 0;
	}
}