<style>
    .wechat,.wechatBind{
        text-align:center;
        padding-top:92px;
        display:none
    }
    strong{
        font-size:20px
    }
    .avatarWechat{
        width:86px;
        height:86px;
        border-radius:50%
    }
    .wechatIcon{
        height:16px;
        position: absolute;
        bottom: 0;
        right: 0;
        width: 16px;
    }
    .relative{
        position: relative;
    }
</style>
<?php
$photoUrl = ($this->staff->user_avatar && $this->staff->user_avatar !== 'blank.gif') ? $this->staff->user_avatar : 'blank.jpg';
$photoPath = Yii::app()->params['OAUploadBaseUrl'].'/users/'.$photoUrl;
?>
<div class='wechatBind'>
    <div><strong class='color3'><?php echo Yii::t("directMessage", "Wechat Notification"); ?></strong> </div>
    <div class='mt8 mb24'><?php echo Yii::t("directMessage", "Response parent feedback on wechat!"); ?></div>
    <div id="wechatQrcodeBind" alt="" class="qrcodeWechat mt24"></div>
    <div class='color6 mt24'>
    <?php echo Yii::t("directMessage", "Bind Wechat to account of :feeType", array(":feeType"=>Yii::t("directMessage",$this->staff->getName())) ); ?></div>
</div>
<div class='wechat'>
    <div><strong class='color3'><?php echo Yii::t("directMessage", "Wechat notification enabled"); ?></strong> </div>
    <div class='mt8 mb24 pb8'><?php echo Yii::t("directMessage", "Response parent feedback on wechat!"); ?></div>
    <div class='relative inline-block'>
        <img  alt="" src='' class='avatarWechat'>
        <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/wechatDM.png' ?>" alt="" class='wechatIcon'> 
    </div>
    <div class='font12 mt16 color6 ' id='nickname'><?php echo $this->staff->getName() ?></div>
    <div class='font12 mt24 mt16 color6 '>
        <button class="btn btn-default" type="button" id='unbind'><?php echo Yii::t("directMessage", "Unbind your account"); ?></button>
    </div>
</div>
<script>
    getQrcode()
    init()
    
    function getQrcode(){
        $.ajax({
            url: '<?php echo $this->createUrl("teacherBindQrcode") ?>',
            type: "post",
            dataType: 'json',
            data: {
              
            },
            success: function(data) {
                if (data.state == 'success') {
                    $('#wechatQrcodeBind').qrcode({width: 144,height: 144,text:data.data});
                }
            },
            error: function(data) {

            },
        })
    }
    var interval = null;
    function start(){
        if(interval!=null){
            clearInterval(interval);
            interval=null;
        }
        interval = setInterval(init,5000);
    }
    function init(){
        $.ajax({
            url: '<?php echo $this->createUrl("teacherBindInfo") ?>',
            type: "post",
            dataType: 'json',
            data: {},
            success: function(data) {
                if (data.state == 'success') {
                   if(data.data.state==0){
                     $('.wechatBind').show()
                     $('.wechat').hide()
                     start()
                   }else{
                        clearInterval(interval);
                        interval=null;
                        $('.wechatBind').hide()
                        $('.wechat').show()
                        $('#nickname').html(data.data.nickname)
                        if(data.data.headimgurl=='' || data.data.headimgurl==null){
                            $(".avatarWechat").attr("src",'<?php echo Yii::app()->themeManager->baseUrl.'/base/images/default.png' ?>');
                        }else{
                            $(".avatarWechat").attr("src",data.data.headimgurl);
                        }
                   }
                }else{
                    clearInterval(interval);
                    interval = null;
                    resultTip({
                        error: 'warning',
                        msg:data.message
                    })
                }
            },
            error: function(data) {

            },
        })
    }
    $('#unbind').click(function () { 
        $.ajax({
            url: '<?php echo $this->createUrl("teacherUnbind") ?>',
            type: "post",
            dataType: 'json',
            data: {},
            success: function(data) {
                if (data.state == 'success') {
                    resultTip({
                        msg:data.message
                    })
                    init()
                }else{
                    resultTip({
                        error: 'warning',
                        msg:data.message
                    })
                }
            },
            error: function(data) {

            },
        })
    });
</script>