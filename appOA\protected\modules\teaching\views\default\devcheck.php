<script>
var currentChildId = 0;
var currentChildData =  {
	fall: <?php echo CJSON::encode($checkListTemplates['checkIds']);?>,
	spring: <?php echo CJSON::encode($checkListTemplates['checkIds']);?>
};
var initialChildData;
var nameListColl;
</script>
<div class="h_a"><?php echo Yii::t('teaching', 'Development Checklist');?></div>
<div class="prompt_text">
	<ul>
		<li><?php echo Yii::t('teaching', 'Instructions');?></li>
	</ul>
</div>

<div class="table_full-">
	<table width="100%">
		<colgroup>
			<col width=180></col>
		</colgroup>
		<tbody>
			<tr>
				<th valign=top>
					<div id="list-box">
						<?php
						?>
						
						<ul id="child-list" class="child-list child-checklist"> <!--place holder-->
						</ul>
					</div>
				</th>
				<td valign="top">
					<div id="checklist-zone" class="table_list" style="display:none;"> <!--place holder-->
						
						<table width="100%" class="mb10 checklist">
						<colgroup>
							<col width=420>
							<col width=100>
						</colgroup>
						<tbody>
					
						<?php
						function renderCheckListItem($dataArray){
							foreach($dataArray as $id=>$item){
								echo CHtml::openTag('tr');
								echo CHtml::openTag('td', array('class'=>'item'));
								echo CHtml::openTag('div');
								echo sprintf('%s) %s', $item['weight'], CommonUtils::autoLang($item['cn_content'],$item['en_content']) );// . $id );
								echo CHtml::closeTag('div');
								echo CHtml::closeTag('td');
								echo CHtml::openTag('td');
								echo sprintf('<div class="basic" semester="fall" itemid="%d" data-average=0 data-id="fall-%d"></div>', $id, $id);
								echo CHtml::closeTag('td');
								echo CHtml::openTag('td');
								echo sprintf('<div class="basic" semester="spring" itemid="%d" data-average=0 data-id="spring-%d"></div>', $id, $id);
								echo CHtml::closeTag('td');
								echo CHtml::closeTag('tr');
							}						
						}

						foreach($checkListTemplates['category'] as $cat_0=>$category){
							echo CHtml::openTag('tr');
							echo CHtml::openTag('td', array('catid'=>$cat_0, 'colspan'=>3, 'class'=>'level0'));
							echo CommonUtils::autoLang(
								$checkListTemplates['catList'][$cat_0]['cn_title'],
								$checkListTemplates['catList'][$cat_0]['en_title']);//. $cat_0;
							echo CHtml::closeTag('td');
							echo CHtml::closeTag('tr');

							
							foreach( $category as $cat_1 => $data){
								echo CHtml::openTag('tr');
								echo CHtml::openTag('td', array('catid'=>$cat_1, 'class'=>'level1'));
								echo CommonUtils::autoLang(
									$checkListTemplates['catList'][$cat_1]['cn_title'],
									$checkListTemplates['catList'][$cat_1]['en_title']);//. $cat_1;
								echo CHtml::closeTag('td');
								echo CHtml::openTag('td');
								echo '1st Semester';
								echo CHtml::closeTag('td');
								echo CHtml::openTag('td');
								echo '2nd Semester';
								echo CHtml::closeTag('td');
								echo CHtml::closeTag('tr');
								
								if(count($checkListTemplates['checkList'][$cat_1])){
									renderCheckListItem( $checkListTemplates['checkList'][$cat_1] );
								}
								foreach( $data as $cat_2){
									echo CHtml::openTag('tr');
									echo CHtml::openTag('td', array('colspan'=>3, 'class'=>'level2'));
									echo CommonUtils::autoLang(
										$checkListTemplates['catList'][$cat_2]['cn_title'],
										$checkListTemplates['catList'][$cat_2]['en_title']) ;//. $cat_2;
									echo CHtml::closeTag('td');
									echo CHtml::closeTag('tr');
									
									if(count( $checkListTemplates['checkList'][$cat_2] )){
										renderCheckListItem( $checkListTemplates['checkList'][$cat_2] );
									}
								}
							}
						};
						
						$nameListStr = implode(',', $nameList);
						?>
						</tbody>
						</table>
						<div class="btn_wrap">						
							<div class="btn_wrap_pd">
								<label class="mr15">
								<?php echo CHtml::checkBox("finish[fall]", false, array('semester'=>'fall', 'class'=>'markFinish')); ?>
								第一学期完成标记
								</label>
								<label class="mr15">
								<?php echo CHtml::checkBox("finish[spring]", false, array('semester'=>'spring', 'class'=>'markFinish')); ?>
								第二学期完成标记
								</label>
								<button id="checkList-save" class="btn btn_submit"><?php echo Yii::t('global','Save');?></button>
								<div id="J_message_send_tip"></div>
							</div>
						</div>
					</div>
					<div id="loading" style="display:none;"><?php echo Yii::t('global','Loading Data...');?></div>
					&nbsp;
				</td>
			</tr>
		</tbody>
	</table>
</div>

<style>
.btn_wrap{left: 215px !important;border-left: #dedede;}
</style>

<script>
var OAUploadBaseUrl = '<?php echo Yii::app()->params['OAUploadBaseUrl'];?>';
var nameList;

$(function(){
	initialChildData = JSON.parse(JSON.stringify(currentChildData));
	
	$.each(initialChildData.fall, function(index, content){
		initialChildData.fall[index] = 0;
	});
	$.each(initialChildData.spring, function(index, content){
		initialChildData.spring[index] = 0;
	});

	iniSingleChildData = function(data){
		currentChildData = JSON.parse(JSON.stringify(initialChildData));;
		if(data.devdata){
			if(data.devdata.fall)
			$.each(data.devdata.fall, function(index, content){
				currentChildData.fall[index] = content;
			});
			if(data.devdata.spring)
			$.each(data.devdata.spring, function(index, content){
				currentChildData.spring[index] = content;
			});
		}
		$.each(currentChildData.fall, function(index, content){
			$('div.basic[data-id|=fall-'+index+']').attr('data-average', content);
		});
		$.each(currentChildData.spring, function(index, content){
			$('div.basic[data-id|=spring-'+index+']').attr('data-average', content);
		});
		if(data.fall_display == 2){
			$('#finish_fall').attr('checked', 'checked');
			$('#child-list li[childid|='+currentChildId+']').children('em.flag-1').addClass('finished');
		}else{
			$('#finish_fall').removeAttr('checked');
			$('#child-list li[childid|='+currentChildId+']').children('em.flag-1').removeClass('finished');
		}
		if(data.spring_display == 2){
			$('#finish_spring').attr('checked', 'checked');
			$('#child-list li[childid|='+currentChildId+']').children('em.flag-2').addClass('finished');
		}else{
			$('#finish_spring').removeAttr('checked');
			$('#child-list li[childid|='+currentChildId+']').children('em.flag-2').removeClass('finished');
		}
	}
	
	//保存
	$('#checkList-save').click(function(){
		$(this).attr('disabled','disabled');
		$(this).addClass('disabled');
		$.ajax({
			url: '<?php echo $this->createUrl('//teaching/default/childDevCheckListSave');?>',
			dataType: 'json',
			type: 'POST',
			data: {
				'classid': <?php echo $this->currentClassId;?>,
				'childid': currentChildId,
				'userid': <?php echo Yii::app()->user->getId();?>,
				'development': JSON.stringify(currentChildData),
				'startyear': <?php echo $this->currentStartYear;?>,
				'fall_display': $('#finish_fall').is(':checked')? 2: 1,
				'spring_display': $('#finish_spring').is(':checked')? 2: 1,
				'securityCode': '<?php echo $securityCode;?>'
			}
		}).done(function(data){
			if(data.state == 'success'){
				iniSingleChildData(data.data);
				iniRating();
			}else{
				alert(data.message);
			}
			$('#J_message_send_tip').removeClass('tips_success tips_faild').addClass('tips_'+data.state).
				html(data.message).
				css({opacity: 1.0, visibility: "visible"});
			$('#checkList-save').removeClass('disabled');
			$('#checkList-save').removeAttr('disabled');
			$('#J_message_send_tip').css({opacity: 1.0, visibility: "visible"}).animate({opacity: 0.0}, 2000);
		});
	});

	iniRating = function(){
		$(".basic").html("");
		$(".basic").jRating({
			sendRequest: false,
			length: 3,
			step: true,
			rateMax: 3,
			canRateAgain: true,
			nbRates: 999999,
			showRateInfo: false,
			bigStarsPath: '<?php echo Yii::app()->theme->baseUrl.'/images/icons/stars.png'?>',
			onClick: function(element,rate){
				var semester = $(element).attr('semester');
				var itemid = $(element).attr('itemid');
				currentChildData[semester][itemid] = rate;
			}
		});	
	};
	
	$('.markFinish').click(function(){
		var semester = $(this).attr('semester');
		var cango = true;
		if($(this).is(':checked')){
			$.each(currentChildData[semester], function(index, content){
				if(currentChildData[semester][index] == 0){
					cango = false;
					return false;
				}
			});
			if(cango == false){
				alert('本学期的数据尚未全部完成标记，请检查后重试');
				$(this).removeAttr('checked');
			}
		}
		
	});
	

	iniChildClick = function(){ 
		$('#child-list li').click(function(){
			$('#checklist-zone').hide();
			$('#J_message_send_tip').css('visibility','hidden');
			$('#loading').show();
			var li=$(this);
			currentChildId = 0;
			var selectedChildId = $(this).attr('childid');
			$.ajax({
				url: '<?php echo $this->createUrl('//teaching/default/childDevCheckList');?>',
				dataType: 'json',
				type: 'POST',
				data: {
					'classid': <?php echo $this->currentClassId;?>,
					'childid': selectedChildId,
					'userid': <?php echo Yii::app()->user->getId();?>,
					'development': JSON.stringify(currentChildData),
					'securityCode': '<?php echo $securityCode;?>'
				}
			}).done(function(data){
				currentChildId = selectedChildId;
				li.siblings().removeClass('current');
				li.addClass('current');
				if(data.data.hasdata){
					iniSingleChildData(data.data);
				}else{
					$.each(currentChildData.fall, function(index, content){
						currentChildData.fall[index] = 0;
						$('div.basic[data-id|=fall-'+index+']').attr('data-average', 0);
					});
					$.each(currentChildData.spring, function(index, content){
						currentChildData.spring[index] = 0;
						$('div.basic[data-id|=spring-'+index+']').attr('data-average', 0);
					});
					$('#finish_fall').removeAttr('checked');
					$('#finish_spring').removeAttr('checked');
				}
				iniRating();
				$('#loading').hide();
				$('#checklist-zone').show();
			});
		});
	}
	
	nameList = [<?php echo $nameListStr;?>];
	nameListColl = new Backbone.Collection;
	
	var singleChildView = Backbone.View.extend({
		tagName: 'li',
		initialize: function(){
		
		},
		render: function(){
			this.$el.attr('childid', this.model.get('id'));
			this.$el.html( _.template('<em title="1st Semester" class="devflag flag-1"></em><em title="2nd Semester" class="devflag flag-2"></em><img class="face" src="<% print(OAUploadBaseUrl) %>/childmgt/<%= photo %>"><%= name %>', this.model.attributes) );
			if(this.model.get("fall")==2){this.$el.children("em.flag-1").addClass('finished');}
			if(this.model.get("spring")==2){this.$el.children("em.flag-2").addClass('finished');}
			return this;
		}
	});
	
	var ChildListView = Backbone.View.extend({
		el: $('#child-list'),
		initialize: function(){
			this.listenTo(nameListColl, 'reset', this.addAll);
			nameListColl.reset(nameList);
		},
		addAll: function(){
			this.$el.html("");
			nameListColl.each(this.addOne, this);
			iniChildClick();
		},
		addOne: function(childModel){
			var item = new singleChildView({model:childModel});
			this.$el.append(item.render().el);
		}
	});
	
	var pageChildList = new ChildListView;	
});

</script>