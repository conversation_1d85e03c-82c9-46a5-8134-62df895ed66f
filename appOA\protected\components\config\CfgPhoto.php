<?php
/**
 * 
 */
return array(
	"defaultSizes"=>array(
		"normalW"=>400,
		"normalH"=>300,
		"thumbW"=>200,
	),
	"operations"=>array(
		"filePrefix"=>"op_",
		"subDir"=>Yii::app()->params['OAUploadBasePath']."/operations/",
		"uploadUrl"=>Yii::app()->params['OAUploadBaseUrl']."/operations/",
		"sizes"=>array(
			"normalW"=>350,
			"normalH"=>350,
			"thumbW"=>200,
		),
	),
	"points"=>array(
		"subDir"=>Yii::app()->params['OAUploadBasePath']."/points/",
		"uploadUrl"=>Yii::app()->params['OAUploadBaseUrl']."/points/",
		"sizes"=>array(
			"normalW"=>800,
			"normalH"=>800,
			"thumbW"=>200,
		),
	),
	"childPhoto"=>array(
        "filePrefix"=>"child_",
		"subDir"=>Yii::app()->params['OAUploadBasePath']."/childmgt/",
		"uploadUrl"=>Yii::app()->params['OAUploadBaseUrl']."/childmgt/",
		"sizes"=>array(
			"normalW"=>800,
			"normalH"=>800,
			"thumbW"=>80,
		),
	),
	"userPhoto"=>array(
        "filePrefix"=>"p_",
		"subDir"=>Yii::app()->params['OAUploadBasePath']."/users/",
		"uploadUrl"=>Yii::app()->params['OAUploadBaseUrl']."/users/",
		"sizes"=>array(
			"normalW"=>200,
			"normalH"=>200,
			"thumbW"=>80,
		),
	),
    "classPhoto"=>array(
        "filePrefix"=>"class_",
		"subDir"=>Yii::app()->params['OAUploadBasePath']."/classmgt/",
		"uploadUrl"=>Yii::app()->params['OAUploadBaseUrl']."/classmgt/",
		"sizes"=>array(
			"normalW"=>400,
			"normalH"=>400,
			"thumbW"=>160,
		),
	),
    "articlePhoto"=>array(
        "filePrefix"=>"article_",
        "subDir"=>Yii::app()->params['OAUploadBasePath']."/article/".date('Ym').'/',
        "uploadUrl"=>Yii::app()->params['OAUploadBaseUrl']."/article/",
        "sizes"=>array(
            "normalW"=>1600,
            "thumbW"=>800,
        ),
    ),
    "branchPhoto"=>array(
        "filePrefix"=>"logo_",
        "subDir"=>Yii::app()->params['OAUploadBasePath']."/branch/",
        "uploadUrl"=>Yii::app()->params['OAUploadBaseUrl']."/branch/",
        "sizes"=>array(
            "normalW"=>1600,
            "thumbW"=>800,
        ),
    ),
);

?>