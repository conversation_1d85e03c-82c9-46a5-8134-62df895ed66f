
<style>
	.border {
		border: 1px solid #ccc;
		border-radius: 5px
	}
	
	.pb5 {
		padding-bottom: 15px
	}
	
	.ptintImg {
		height: 140px;
    border-radius: 4px;
	}
	.datalist{
		float: left;
		width: 50%;
		margin-top:10px;
	}
	.datalist div{
		font-size:12px
	}
	.list{
		width:140px;
		margin:0 auto;
	}
	.borderNone {
		border: 1px solid #ccc;
	}
	
	.borderRight {
		border-right: 1px solid #ccc;
	}
	
	.padd0 {
		padding: 0 !important
	}
	
	[v-cloak] {
		display: none;
	}
	
	.mauto {
		height: 100%
	}
	
	.h100 {
		height: 100%
	}
	
	.chilidPto {
		max-height: 40%;
    border-radius: 4px;
	}
	
	.pt10 {
		padding: 10px 0 20px 10px;

	}
	
	.col-md-6 {
		float: left;
		width: 50%;
        /* padding: 0 10px 0 0; */
		/*padding:0;
		padding-left:10px*/
	}
	
	.p10 {
		padding-left: 10px;
		padding-right: 10px
	}
	
	.view {
		width: 800px;
		margin: 0 auto;
		/*float: left;*/
		margin-top: 10px;
		padding-right: 10px
	}
	
	.mt15 {
		margin-top: 15px
	}

    .mt25 {
        margin-top: 25px
    }

    .mt40 {
        margin-top: 40px
    }
	
	.borderNone {
		height: 440px
	}

    .mb5 {
        margin-bottom: 5px;
    }
    .mb10 {
        margin-bottom: 10px;
    }
		.relative{
		position: relative;
	}
	.absolute{
		position: absolute;
    bottom: 15px;
    left: 15px;
    font-size: 16px;
    color: #fff;
	}
	.pt30{
		padding-top:30px
	}
	.fontSize{
		font-size:18px
	}
	.keepAll{
		word-wrap:break-word;
		word-break:keep-all;
	}
	.flex{
		display: flex
	}
	.flexW{
		width:39px
	}
	.flex1{
	   flex:1
	}
	.imgCss{
		width:150px
	}
	.qf{
		width:180px
	}
	.ivyimgCss{
		width:107px
	}
	<?php
 if ($this->branchObj->type == 50 || $this->branchId == 'BJ_QFF') {
 $imgSrc='http://m2.files.ivykids.cn/cloud01-file-8025768FtLJ88p3PQvUtBNeUSKGQPXQBynO.jpg';
 $logoWdith='imgCss';

} else {
	$imgSrc='http://m2.files.ivykids.cn/cloud01-file-8025768FqoRUgyX5mkN_M9pWPUH9fEAKZvK.jpg';
	$logoWdith='ivyimgCss';
} 	
?>
</style>
<style media="print">

	.view:nth-child(even) {
		page-break-after: always
	}
	.datalist{
		height:auto !important;
		padding-left:13px !important
	}
	.view {
		width: 510px;
		float: none;
		margin: 0 auto;
		padding-right: 0px;
	}
	.list{
		width:auto !important;
		margin:0 auto;
	}
	.ptintImg {
		height: auto !important;
		width: 100px !important;
	}
	
	.borderNone {
		height:350px !important;
	}
	
	p {
		margin-bottom: 5px;
	}
	.fontSize{
		font-size:15px
	}
	.absolute{
    bottom:10px !important;
    left: 10px !important;
    font-size: 15px !important;
    color: rgb(255 255 255) !important;
	}
	.absolute div{
		color: rgb(255 255 255) !important;
	}
	.imgCss{
		width:130px !important;
	}
	.ivyimgCss{
		height:60px !important;
	}
	<?php
 if ($this->branchObj->type == 50 || $this->branchId == 'BJ_QFF') {
 $logoWdith='imgCss';

} else {
	$logoWdith='ivyimgCss';

} 
?>
</style>
<div class="container-fluid">
	<div class="row">
		<div id='print' v-cloak>
			<div class="view pb5" v-for='(list,key,index) in data'>
				<div class="borderNone col-md-12 padd0 relative">
				
				  <img src="<?php echo $imgSrc; ?>" style='width:100%;height:100%;position: absolute;'>
					<div class="col-md-6 borderRight padd0 h100">
							<div class="datalist" v-for='(parentlist,idx) in list.pickups'>
								<div class='list'>
									<p><img :src="parentlist.head_url" alt="" class="ptintImg"></p>
									<div class="keepAll flex" >
									 <span class='flexW'> <?php echo Yii::t('global','Name') ?><?php echo Yii::t('global',': ') ?></span>
									 <span class='flex1'>{{parentlist.name}}</span>
									</div>
									<div class=""><?php echo Yii::t('asa','Phone') ?><?php echo Yii::t('global',': ') ?>{{trim(parentlist.phone)}}</div>
									<div class=""><?php echo Yii::t('site','Relationship') ?><?php echo Yii::t('global',': ') ?>{{parentlist.relation}}</div>
								</div>
							</div>
                  <div style="clear: both;"></div>
						</div>
					<div class="col-md-6 pt10 h100 relative">
						<div class="mauto text-center">
							<p class="pt30"><img :src="logo" alt=""  class="<?php echo $logoWdith;?> "></p>
							<img :src="list.child_avatar" class="mt10 chilidPto " alt="">
							<div class="text-center pt10 fontSize">{{list.child_name_cn}}<br>{{list.child_name_en}}</div>
						</div>
						<div class='text-left absolute'>
									<div>{{branchTitle}}</div>
							</div>
					</div>
				</div>
				<div class="clearfix"></div>
			</div>
		</div>
	</div>
</div>
<script>
	var data = <?php echo json_encode($data) ?>; //班级 孩子数据
	var logo = '<?php echo $logo ?>';
	var year = <?php echo json_encode($year) ?>;//班级
	var branchTitle = <?php echo json_encode($branchTitle) ?>;//班级

	var print = new Vue({
		el: "#print",
		data: {
			data: data,
			logo: logo,
			yearData:year,
			branchTitle:branchTitle
		},
		created: function() {},
		computed: {},
		methods: {
			trim(str){
                if(str){
                    var result;
                    var is_global='g'
                    result = str.replace(/(^\s+)|(\s+$)/g,"");
                    if(is_global.toLowerCase()=="g")
                    {
                        result = result.replace(/\s/g,"");
                    }
                    var tel=result.replace("+86",'')
                    return tel;
                }
			}
		},
	})
</script>