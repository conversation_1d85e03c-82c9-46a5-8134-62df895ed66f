<?php
class latestReport extends CWidget{
	
	public function init(){
        
	}
	
	public function run(){
        Yii::import('common.models.portfolio.*');

        $childid = $this->getController()->getChildId();
        
        $criteria = new CDbCriteria();
        $criteria->compare('t.childid', $childid);
        $criteria->compare('t.stat', SReport::STATUS_ONLINE);
        $criteria->compare('t.custom', 0);
        $criteria->order='t.timestamp desc';
        $criteria->limit=1;
        $report=SReport::model()->with('items')->find($criteria);

        if ($report){
            $pids = array();
            foreach ($report->items as $extfo){
                if ($extfo->media_id)
                    $pids[$extfo->media_id]=$extfo->media_id;
            }
            if ($pids){

                ChildMediaAlt::setStartYear($report->startyear);

                if (count($pids)>3)
                    $pids=  array_rand($pids, 3);

                $criteria = new CDbCriteria();
                $criteria->compare('id', $pids);
                $med=ChildMediaAlt::model()->findAll($criteria);

                $this->render('latestreport',array(
                    'report'=>$report,
                    'med'=>$med,
                ));
            }
        }
	}
	
}