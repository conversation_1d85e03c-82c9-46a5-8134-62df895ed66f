<?php
/**
 * Locale data for 'en_US'.
 *
 * This file is automatically generated by yiic cldr command.
 *
 * Copyright © 1991-2007 Unicode, Inc. All rights reserved.
 * Distributed under the Terms of Use in http://www.unicode.org/copyright.html.
 *
 * Copyright © 2008-2011 Yii Software LLC (http://www.yiiframework.com/license/)
 */
return array (
  'version' => '4123',
  'numberSymbols' => 
  array (
    'decimal' => '.',
    'group' => ',',
    'list' => ';',
    'percentSign' => '%',
    'plusSign' => '+',
    'minusSign' => '-',
    'exponential' => 'E',
    'perMille' => '‰',
    'infinity' => '∞',
    'nan' => 'NaN',
    'alias' => '',
  ),
  'decimalFormat' => '#,##0.###',
  'scientificFormat' => '#E0',
  'percentFormat' => '#,##0%',
  'currencyFormat' => '¤#,##0.00;(¤#,##0.00)',
  'currencySymbols' => 
  array (
    'AUD' => 'AU$',
    'BRL' => 'BR$',
    'CAD' => 'CA$',
    'CNY' => 'CN¥',
    'EUR' => '€',
    'GBP' => '£',
    'HKD' => 'HK$',
    'ILS' => '₪',
    'INR' => '₹',
    'JPY' => '¥',
    'KRW' => '₩',
    'MXN' => 'MX$',
    'NZD' => 'NZ$',
    'THB' => '฿',
    'TWD' => 'NT$',
    'USD' => '$',
    'VND' => '₫',
    'XAF' => 'FCFA',
    'XCD' => 'EC$',
    'XOF' => 'CFA',
    'XPF' => 'CFPF',
  ),
  'monthNames' => 
  array (
    'wide' => 
    array (
      1 => 'January',
      2 => 'February',
      3 => 'March',
      4 => 'April',
      5 => 'May',
      6 => 'June',
      7 => 'July',
      8 => 'August',
      9 => 'September',
      10 => 'October',
      11 => 'November',
      12 => 'December',
    ),
    'abbreviated' => 
    array (
      1 => 'Jan',
      2 => 'Feb',
      3 => 'Mar',
      4 => 'Apr',
      5 => 'May',
      6 => 'Jun',
      7 => 'Jul',
      8 => 'Aug',
      9 => 'Sep',
      10 => 'Oct',
      11 => 'Nov',
      12 => 'Dec',
    ),
  ),
  'monthNamesSA' => 
  array (
    'narrow' => 
    array (
      1 => 'J',
      2 => 'F',
      3 => 'M',
      4 => 'A',
      5 => 'M',
      6 => 'J',
      7 => 'J',
      8 => 'A',
      9 => 'S',
      10 => 'O',
      11 => 'N',
      12 => 'D',
    ),
  ),
  'weekDayNames' => 
  array (
    'wide' => 
    array (
      0 => 'Sunday',
      1 => 'Monday',
      2 => 'Tuesday',
      3 => 'Wednesday',
      4 => 'Thursday',
      5 => 'Friday',
      6 => 'Saturday',
    ),
    'abbreviated' => 
    array (
      0 => 'Sun',
      1 => 'Mon',
      2 => 'Tue',
      3 => 'Wed',
      4 => 'Thu',
      5 => 'Fri',
      6 => 'Sat',
    ),
  ),
  'weekDayNamesSA' => 
  array (
    'narrow' => 
    array (
      0 => 'S',
      1 => 'M',
      2 => 'T',
      3 => 'W',
      4 => 'T',
      5 => 'F',
      6 => 'S',
    ),
  ),
  'eraNames' => 
  array (
    'abbreviated' => 
    array (
      0 => 'BC',
      1 => 'AD',
    ),
    'wide' => 
    array (
      0 => 'Before Christ',
      1 => 'Anno Domini',
    ),
    'narrow' => 
    array (
      0 => 'B',
      1 => 'A',
    ),
  ),
  'dateFormats' => 
  array (
    'full' => 'EEEE, MMMM d, y',
    'long' => 'MMMM d, y',
    'medium' => 'MMM dd, y',
    'short' => 'M/d/yy',
  ),
  'timeFormats' => 
  array (
    'full' => 'h:mm:ss a zzzz',
    'long' => 'h:mm:ss a z',
    'medium' => 'h:mm:ss a',
    'short' => 'HH:mm',
  ),
  'dateTimeFormat' => '{1} {0}',
  'amName' => 'AM',
  'pmName' => 'PM',
  'orientation' => 'ltr',
  'languages' => 
  array (
    'aa' => 'Afar',
    'ab' => 'Abkhazian',
    'ace' => 'Achinese',
    'ach' => 'Acoli',
    'ada' => 'Adangme',
    'ady' => 'Adyghe',
    'ae' => 'Avestan',
    'af' => 'Afrikaans',
    'afa' => 'Afro-Asiatic Language',
    'afh' => 'Afrihili',
    'agq' => 'Aghem',
    'ain' => 'Ainu',
    'ak' => 'Akan',
    'akk' => 'Akkadian',
    'ale' => 'Aleut',
    'alg' => 'Algonquian Language',
    'alt' => 'Southern Altai',
    'am' => 'Amharic',
    'an' => 'Aragonese',
    'ang' => 'Old English',
    'anp' => 'Angika',
    'apa' => 'Apache Language',
    'ar' => 'Arabic',
    'arc' => 'Aramaic',
    'arn' => 'Araucanian',
    'arp' => 'Arapaho',
    'art' => 'Artificial Language',
    'arw' => 'Arawak',
    'as' => 'Assamese',
    'asa' => 'Asu',
    'ast' => 'Asturian',
    'ath' => 'Athapascan Language',
    'aus' => 'Australian Language',
    'av' => 'Avaric',
    'awa' => 'Awadhi',
    'ay' => 'Aymara',
    'az' => 'Azeri',
    'ba' => 'Bashkir',
    'bad' => 'Banda',
    'bai' => 'Bamileke Language',
    'bal' => 'Baluchi',
    'ban' => 'Balinese',
    'bas' => 'Basaa',
    'bat' => 'Baltic Language',
    'be' => 'Belarusian',
    'bej' => 'Beja',
    'bem' => 'Bemba',
    'ber' => 'Berber',
    'bez' => 'Bena',
    'bg' => 'Bulgarian',
    'bh' => 'Bihari',
    'bho' => 'Bhojpuri',
    'bi' => 'Bislama',
    'bik' => 'Bikol',
    'bin' => 'Bini',
    'bla' => 'Siksika',
    'bm' => 'Bambara',
    'bn' => 'Bengali',
    'bnt' => 'Bantu',
    'bo' => 'Tibetan',
    'br' => 'Breton',
    'bra' => 'Braj',
    'brx' => 'Bodo',
    'bs' => 'Bosnian',
    'btk' => 'Batak',
    'bua' => 'Buriat',
    'bug' => 'Buginese',
    'byn' => 'Blin',
    'ca' => 'Catalan',
    'cad' => 'Caddo',
    'cai' => 'Central American Indian Language',
    'car' => 'Carib',
    'cau' => 'Caucasian Language',
    'cay' => 'Cayuga',
    'cch' => 'Atsam',
    'ce' => 'Chechen',
    'ceb' => 'Cebuano',
    'cel' => 'Celtic Language',
    'cgg' => 'Chiga',
    'ch' => 'Chamorro',
    'chb' => 'Chibcha',
    'chg' => 'Chagatai',
    'chk' => 'Chuukese',
    'chm' => 'Mari',
    'chn' => 'Chinook Jargon',
    'cho' => 'Choctaw',
    'chp' => 'Chipewyan',
    'chr' => 'Cherokee',
    'chy' => 'Cheyenne',
    'cmc' => 'Chamic Language',
    'co' => 'Corsican',
    'cop' => 'Coptic',
    'cpe' => 'English-based Creole or Pidgin',
    'cpf' => 'French-based Creole or Pidgin',
    'cpp' => 'Portuguese-based Creole or Pidgin',
    'cr' => 'Cree',
    'crh' => 'Crimean Turkish',
    'crp' => 'Creole or Pidgin',
    'cs' => 'Czech',
    'csb' => 'Kashubian',
    'cu' => 'Church Slavic',
    'cus' => 'Cushitic Language',
    'cv' => 'Chuvash',
    'cy' => 'Welsh',
    'da' => 'Danish',
    'dak' => 'Dakota',
    'dar' => 'Dargwa',
    'dav' => 'Taita',
    'day' => 'Dayak',
    'de' => 'German',
    'de_at' => 'Austrian German',
    'de_ch' => 'Swiss High German',
    'del' => 'Delaware',
    'den' => 'Slave',
    'dgr' => 'Dogrib',
    'din' => 'Dinka',
    'dje' => 'Zarma',
    'doi' => 'Dogri',
    'dra' => 'Dravidian Language',
    'dsb' => 'Lower Sorbian',
    'dua' => 'Duala',
    'dum' => 'Middle Dutch',
    'dv' => 'Divehi',
    'dyo' => 'Jola-Fonyi',
    'dyu' => 'Dyula',
    'dz' => 'Dzongkha',
    'ebu' => 'Embu',
    'ee' => 'Ewe',
    'efi' => 'Efik',
    'egy' => 'Ancient Egyptian',
    'eka' => 'Ekajuk',
    'el' => 'Greek',
    'elx' => 'Elamite',
    'en' => 'English',
    'en_au' => 'Australian English',
    'en_ca' => 'Canadian English',
    'en_gb' => 'British English',
    'en_us' => 'U.S. English',
    'enm' => 'Middle English',
    'eo' => 'Esperanto',
    'es' => 'Spanish',
    'es_419' => 'Latin American Spanish',
    'es_es' => 'Iberian Spanish',
    'et' => 'Estonian',
    'eu' => 'Basque',
    'ewo' => 'Ewondo',
    'fa' => 'Persian',
    'fan' => 'Fang',
    'fat' => 'Fanti',
    'ff' => 'Fulah',
    'fi' => 'Finnish',
    'fil' => 'Filipino',
    'fiu' => 'Finno-Ugrian Language',
    'fj' => 'Fijian',
    'fo' => 'Faroese',
    'fon' => 'Fon',
    'fr' => 'French',
    'fr_ca' => 'Canadian French',
    'fr_ch' => 'Swiss French',
    'frm' => 'Middle French',
    'fro' => 'Old French',
    'frr' => 'Northern Frisian',
    'frs' => 'Eastern Frisian',
    'fur' => 'Friulian',
    'fy' => 'Western Frisian',
    'ga' => 'Irish',
    'gaa' => 'Ga',
    'gay' => 'Gayo',
    'gba' => 'Gbaya',
    'gd' => 'Scottish Gaelic',
    'gem' => 'Germanic Language',
    'gez' => 'Geez',
    'gil' => 'Gilbertese',
    'gl' => 'Galician',
    'gmh' => 'Middle High German',
    'gn' => 'Guarani',
    'goh' => 'Old High German',
    'gon' => 'Gondi',
    'gor' => 'Gorontalo',
    'got' => 'Gothic',
    'grb' => 'Grebo',
    'grc' => 'Ancient Greek',
    'gsw' => 'Swiss German',
    'gu' => 'Gujarati',
    'guz' => 'Gusii',
    'gv' => 'Manx',
    'gwi' => 'Gwichʼin',
    'ha' => 'Hausa',
    'hai' => 'Haida',
    'haw' => 'Hawaiian',
    'he' => 'Hebrew',
    'hi' => 'Hindi',
    'hil' => 'Hiligaynon',
    'him' => 'Himachali',
    'hit' => 'Hittite',
    'hmn' => 'Hmong',
    'ho' => 'Hiri Motu',
    'hr' => 'Croatian',
    'hsb' => 'Upper Sorbian',
    'ht' => 'Haitian',
    'hu' => 'Hungarian',
    'hup' => 'Hupa',
    'hy' => 'Armenian',
    'hz' => 'Herero',
    'ia' => 'Interlingua',
    'iba' => 'Iban',
    'id' => 'Indonesian',
    'ie' => 'Interlingue',
    'ig' => 'Igbo',
    'ii' => 'Sichuan Yi',
    'ijo' => 'Ijo',
    'ik' => 'Inupiaq',
    'ilo' => 'Iloko',
    'inc' => 'Indic Language',
    'ine' => 'Indo-European Language',
    'inh' => 'Ingush',
    'io' => 'Ido',
    'ira' => 'Iranian Language',
    'iro' => 'Iroquoian Language',
    'is' => 'Icelandic',
    'it' => 'Italian',
    'iu' => 'Inuktitut',
    'ja' => 'Japanese',
    'jbo' => 'Lojban',
    'jmc' => 'Machame',
    'jpr' => 'Judeo-Persian',
    'jrb' => 'Judeo-Arabic',
    'jv' => 'Javanese',
    'ka' => 'Georgian',
    'kaa' => 'Kara-Kalpak',
    'kab' => 'Kabyle',
    'kac' => 'Kachin',
    'kaj' => 'Jju',
    'kam' => 'Kamba',
    'kar' => 'Karen',
    'kaw' => 'Kawi',
    'kbd' => 'Kabardian',
    'kcg' => 'Tyap',
    'kde' => 'Makonde',
    'kea' => 'Kabuverdianu',
    'kfo' => 'Koro',
    'kg' => 'Kongo',
    'kha' => 'Khasi',
    'khi' => 'Khoisan Language',
    'kho' => 'Khotanese',
    'khq' => 'Koyra Chiini',
    'ki' => 'Kikuyu',
    'kj' => 'Kuanyama',
    'kk' => 'Kazakh',
    'kl' => 'Kalaallisut',
    'kln' => 'Kalenjin',
    'km' => 'Khmer',
    'kmb' => 'Kimbundu',
    'kn' => 'Kannada',
    'ko' => 'Korean',
    'kok' => 'Konkani',
    'kos' => 'Kosraean',
    'kpe' => 'Kpelle',
    'kr' => 'Kanuri',
    'krc' => 'Karachay-Balkar',
    'krl' => 'Karelian',
    'kro' => 'Kru',
    'kru' => 'Kurukh',
    'ks' => 'Kashmiri',
    'ksb' => 'Shambala',
    'ksf' => 'Bafia',
    'ksh' => 'Colognian',
    'ku' => 'Kurdish',
    'kum' => 'Kumyk',
    'kut' => 'Kutenai',
    'kv' => 'Komi',
    'kw' => 'Cornish',
    'ky' => 'Kirghiz',
    'la' => 'Latin',
    'lad' => 'Ladino',
    'lag' => 'Langi',
    'lah' => 'Lahnda',
    'lam' => 'Lamba',
    'lb' => 'Luxembourgish',
    'lez' => 'Lezghian',
    'lg' => 'Ganda',
    'li' => 'Limburgish',
    'ln' => 'Lingala',
    'lo' => 'Lao',
    'lol' => 'Mongo',
    'loz' => 'Lozi',
    'lt' => 'Lithuanian',
    'lu' => 'Luba-Katanga',
    'lua' => 'Luba-Lulua',
    'lui' => 'Luiseno',
    'lun' => 'Lunda',
    'luo' => 'Luo',
    'lus' => 'Lushai',
    'luy' => 'Luyia',
    'lv' => 'Latvian',
    'mad' => 'Madurese',
    'mag' => 'Magahi',
    'mai' => 'Maithili',
    'mak' => 'Makasar',
    'man' => 'Mandingo',
    'map' => 'Austronesian Language',
    'mas' => 'Masai',
    'mdf' => 'Moksha',
    'mdr' => 'Mandar',
    'men' => 'Mende',
    'mer' => 'Meru',
    'mfe' => 'Morisyen',
    'mg' => 'Malagasy',
    'mga' => 'Middle Irish',
    'mgh' => 'Makhuwa-Meetto',
    'mh' => 'Marshallese',
    'mi' => 'Maori',
    'mic' => 'Micmac',
    'min' => 'Minangkabau',
    'mis' => 'Miscellaneous Language',
    'mk' => 'Macedonian',
    'mkh' => 'Mon-Khmer Language',
    'ml' => 'Malayalam',
    'mn' => 'Mongolian',
    'mnc' => 'Manchu',
    'mni' => 'Manipuri',
    'mno' => 'Manobo Language',
    'mo' => 'Moldavian',
    'moh' => 'Mohawk',
    'mos' => 'Mossi',
    'mr' => 'Marathi',
    'ms' => 'Malay',
    'mt' => 'Maltese',
    'mua' => 'Mundang',
    'mul' => 'Multiple Languages',
    'mun' => 'Munda Language',
    'mus' => 'Creek',
    'mwl' => 'Mirandese',
    'mwr' => 'Marwari',
    'my' => 'Burmese',
    'myn' => 'Mayan Language',
    'myv' => 'Erzya',
    'na' => 'Nauru',
    'nah' => 'Nahuatl',
    'nai' => 'North American Indian Language',
    'nap' => 'Neapolitan',
    'naq' => 'Nama',
    'nb' => 'Norwegian Bokmål',
    'nd' => 'North Ndebele',
    'nds' => 'Low German',
    'ne' => 'Nepali',
    'new' => 'Newari',
    'ng' => 'Ndonga',
    'nia' => 'Nias',
    'nic' => 'Niger-Kordofanian Language',
    'niu' => 'Niuean',
    'nl' => 'Dutch',
    'nl_be' => 'Flemish',
    'nmg' => 'Kwasio',
    'nn' => 'Norwegian Nynorsk',
    'no' => 'Norwegian',
    'nog' => 'Nogai',
    'non' => 'Old Norse',
    'nqo' => 'N’Ko',
    'nr' => 'South Ndebele',
    'nso' => 'Northern Sotho',
    'nub' => 'Nubian Language',
    'nus' => 'Nuer',
    'nv' => 'Navajo',
    'nwc' => 'Classical Newari',
    'ny' => 'Nyanja',
    'nym' => 'Nyamwezi',
    'nyn' => 'Nyankole',
    'nyo' => 'Nyoro',
    'nzi' => 'Nzima',
    'oc' => 'Occitan',
    'oj' => 'Ojibwa',
    'om' => 'Oromo',
    'or' => 'Oriya',
    'os' => 'Ossetic',
    'osa' => 'Osage',
    'ota' => 'Ottoman Turkish',
    'oto' => 'Otomian Language',
    'pa' => 'Punjabi',
    'paa' => 'Papuan Language',
    'pag' => 'Pangasinan',
    'pal' => 'Pahlavi',
    'pam' => 'Pampanga',
    'pap' => 'Papiamento',
    'pau' => 'Palauan',
    'peo' => 'Old Persian',
    'phi' => 'Philippine Language',
    'phn' => 'Phoenician',
    'pi' => 'Pali',
    'pl' => 'Polish',
    'pon' => 'Pohnpeian',
    'pra' => 'Prakrit Language',
    'pro' => 'Old Provençal',
    'ps' => 'Pushto',
    'pt' => 'Portuguese',
    'pt_br' => 'Brazilian Portuguese',
    'pt_pt' => 'Iberian Portuguese',
    'qu' => 'Quechua',
    'raj' => 'Rajasthani',
    'rap' => 'Rapanui',
    'rar' => 'Rarotongan',
    'rm' => 'Romansh',
    'rn' => 'Rundi',
    'ro' => 'Romanian',
    'roa' => 'Romance Language',
    'rof' => 'Rombo',
    'rom' => 'Romany',
    'root' => 'Root',
    'ru' => 'Russian',
    'rup' => 'Aromanian',
    'rw' => 'Kinyarwanda',
    'rwk' => 'Rwa',
    'sa' => 'Sanskrit',
    'sad' => 'Sandawe',
    'sah' => 'Sakha',
    'sai' => 'South American Indian Language',
    'sal' => 'Salishan Language',
    'sam' => 'Samaritan Aramaic',
    'saq' => 'Samburu',
    'sas' => 'Sasak',
    'sat' => 'Santali',
    'sbp' => 'Sangu',
    'sc' => 'Sardinian',
    'scn' => 'Sicilian',
    'sco' => 'Scots',
    'sd' => 'Sindhi',
    'se' => 'Northern Sami',
    'see' => 'Seneca',
    'seh' => 'Sena',
    'sel' => 'Selkup',
    'sem' => 'Semitic Language',
    'ses' => 'Koyraboro Senni',
    'sg' => 'Sango',
    'sga' => 'Old Irish',
    'sgn' => 'Sign Language',
    'sh' => 'Serbo-Croatian',
    'shi' => 'Tachelhit',
    'shn' => 'Shan',
    'si' => 'Sinhala',
    'sid' => 'Sidamo',
    'sio' => 'Siouan Language',
    'sit' => 'Sino-Tibetan Language',
    'sk' => 'Slovak',
    'sl' => 'Slovenian',
    'sla' => 'Slavic Language',
    'sm' => 'Samoan',
    'sma' => 'Southern Sami',
    'smi' => 'Sami Language',
    'smj' => 'Lule Sami',
    'smn' => 'Inari Sami',
    'sms' => 'Skolt Sami',
    'sn' => 'Shona',
    'snk' => 'Soninke',
    'so' => 'Somali',
    'sog' => 'Sogdien',
    'son' => 'Songhai',
    'sq' => 'Albanian',
    'sr' => 'Serbian',
    'srn' => 'Sranan Tongo',
    'srr' => 'Serer',
    'ss' => 'Swati',
    'ssa' => 'Nilo-Saharan Language',
    'ssy' => 'Saho',
    'st' => 'Southern Sotho',
    'su' => 'Sundanese',
    'suk' => 'Sukuma',
    'sus' => 'Susu',
    'sux' => 'Sumerian',
    'sv' => 'Swedish',
    'sw' => 'Swahili',
    'swb' => 'Comorian',
    'swc' => 'Congo Swahili',
    'syc' => 'Classical Syriac',
    'syr' => 'Syriac',
    'ta' => 'Tamil',
    'tai' => 'Tai Language',
    'te' => 'Telugu',
    'tem' => 'Timne',
    'teo' => 'Teso',
    'ter' => 'Tereno',
    'tet' => 'Tetum',
    'tg' => 'Tajik',
    'th' => 'Thai',
    'ti' => 'Tigrinya',
    'tig' => 'Tigre',
    'tiv' => 'Tiv',
    'tk' => 'Turkmen',
    'tkl' => 'Tokelau',
    'tl' => 'Tagalog',
    'tlh' => 'Klingon',
    'tli' => 'Tlingit',
    'tmh' => 'Tamashek',
    'tn' => 'Tswana',
    'to' => 'Tongan',
    'tog' => 'Nyasa Tonga',
    'tpi' => 'Tok Pisin',
    'tr' => 'Turkish',
    'trv' => 'Taroko',
    'ts' => 'Tsonga',
    'tsi' => 'Tsimshian',
    'tt' => 'Tatar',
    'tum' => 'Tumbuka',
    'tup' => 'Tupi Language',
    'tut' => 'Altaic Language',
    'tvl' => 'Tuvalu',
    'tw' => 'Twi',
    'twq' => 'Tasawaq',
    'ty' => 'Tahitian',
    'tyv' => 'Tuvinian',
    'tzm' => 'Central Morocco Tamazight',
    'udm' => 'Udmurt',
    'ug' => 'Uyghur',
    'uga' => 'Ugaritic',
    'uk' => 'Ukrainian',
    'umb' => 'Umbundu',
    'und' => 'Unknown Language',
    'ur' => 'Urdu',
    'uz' => 'Uzbek',
    'vai' => 'Vai',
    've' => 'Venda',
    'vi' => 'Vietnamese',
    'vo' => 'Volapük',
    'vot' => 'Votic',
    'vun' => 'Vunjo',
    'wa' => 'Walloon',
    'wae' => 'Walser',
    'wak' => 'Wakashan Language',
    'wal' => 'Walamo',
    'war' => 'Waray',
    'was' => 'Washo',
    'wen' => 'Sorbian Language',
    'wo' => 'Wolof',
    'xal' => 'Kalmyk',
    'xh' => 'Xhosa',
    'xog' => 'Soga',
    'yao' => 'Yao',
    'yap' => 'Yapese',
    'yav' => 'Yangben',
    'yi' => 'Yiddish',
    'yo' => 'Yoruba',
    'ypk' => 'Yupik Language',
    'yue' => 'Cantonese',
    'za' => 'Zhuang',
    'zap' => 'Zapotec',
    'zbl' => 'Blissymbols',
    'zen' => 'Zenaga',
    'zh' => 'Chinese',
    'zh_hans' => 'Simplified Chinese',
    'zh_hant' => 'Traditional Chinese',
    'znd' => 'Zande',
    'zu' => 'Zulu',
    'zun' => 'Zuni',
    'zxx' => 'No linguistic content',
    'zza' => 'Zaza',
  ),
  'scripts' => 
  array (
    'arab' => 'Perso-Arabic',
    'armi' => 'Imperial Aramaic',
    'armn' => 'Armenian',
    'avst' => 'Avestan',
    'bali' => 'Balinese',
    'bamu' => 'Bamum',
    'batk' => 'Batak',
    'beng' => 'Bengali',
    'blis' => 'Blissymbols',
    'bopo' => 'Bopomofo',
    'brah' => 'Brahmi',
    'brai' => 'Braille',
    'bugi' => 'Buginese',
    'buhd' => 'Buhid',
    'cakm' => 'Chakma',
    'cans' => 'Unified Canadian Aboriginal Syllabics',
    'cari' => 'Carian',
    'cham' => 'Cham',
    'cher' => 'Cherokee',
    'cirt' => 'Cirth',
    'copt' => 'Coptic',
    'cprt' => 'Cypriot',
    'cyrl' => 'Cyrillic',
    'cyrs' => 'Old Church Slavonic Cyrillic',
    'deva' => 'Devanagari',
    'dsrt' => 'Deseret',
    'egyd' => 'Egyptian demotic',
    'egyh' => 'Egyptian hieratic',
    'egyp' => 'Egyptian hieroglyphs',
    'ethi' => 'Ethiopic',
    'geok' => 'Georgian Khutsuri',
    'geor' => 'Georgian',
    'glag' => 'Glagolitic',
    'goth' => 'Gothic',
    'gran' => 'Grantha',
    'grek' => 'Greek',
    'gujr' => 'Gujarati',
    'guru' => 'Gurmukhi',
    'hang' => 'Hangul',
    'hani' => 'Han',
    'hano' => 'Hanunoo',
    'hans' => 'Simplified Han',
    'hant' => 'Traditional Han',
    'hebr' => 'Hebrew',
    'hira' => 'Hiragana',
    'hmng' => 'Pahawh Hmong',
    'hrkt' => 'Katakana or Hiragana',
    'hung' => 'Old Hungarian',
    'inds' => 'Indus',
    'ital' => 'Old Italic',
    'java' => 'Javanese',
    'jpan' => 'Japanese',
    'kali' => 'Kayah Li',
    'kana' => 'Katakana',
    'khar' => 'Kharoshthi',
    'khmr' => 'Khmer',
    'knda' => 'Kannada',
    'kore' => 'Korean',
    'kthi' => 'Kaithi',
    'lana' => 'Lanna',
    'laoo' => 'Lao',
    'latf' => 'Fraktur Latin',
    'latg' => 'Gaelic Latin',
    'latn' => 'Latin',
    'lepc' => 'Lepcha',
    'limb' => 'Limbu',
    'lina' => 'Linear A',
    'linb' => 'Linear B',
    'lisu' => 'Fraser',
    'lyci' => 'Lycian',
    'lydi' => 'Lydian',
    'mand' => 'Mandaean',
    'mani' => 'Manichaean',
    'maya' => 'Mayan hieroglyphs',
    'merc' => 'Meroitic Cursive',
    'mero' => 'Meroitic',
    'mlym' => 'Malayalam',
    'mong' => 'Mongolian',
    'moon' => 'Moon',
    'mtei' => 'Meitei Mayek',
    'mymr' => 'Myanmar',
    'nkgb' => 'Naxi Geba',
    'nkoo' => 'N’Ko',
    'ogam' => 'Ogham',
    'olck' => 'Ol Chiki',
    'orkh' => 'Orkhon',
    'orya' => 'Oriya',
    'osma' => 'Osmanya',
    'perm' => 'Old Permic',
    'phag' => 'Phags-pa',
    'phli' => 'Inscriptional Pahlavi',
    'phlp' => 'Psalter Pahlavi',
    'phlv' => 'Book Pahlavi',
    'phnx' => 'Phoenician',
    'plrd' => 'Pollard Phonetic',
    'prti' => 'Inscriptional Parthian',
    'rjng' => 'Rejang',
    'roro' => 'Rongorongo',
    'runr' => 'Runic',
    'samr' => 'Samaritan',
    'sara' => 'Sarati',
    'sarb' => 'Old South Arabian',
    'saur' => 'Saurashtra',
    'sgnw' => 'SignWriting',
    'shaw' => 'Shavian',
    'sinh' => 'Sinhala',
    'sund' => 'Sundanese',
    'sylo' => 'Syloti Nagri',
    'syrc' => 'Syriac',
    'syre' => 'Estrangelo Syriac',
    'syrj' => 'Western Syriac',
    'syrn' => 'Eastern Syriac',
    'tagb' => 'Tagbanwa',
    'tale' => 'Tai Le',
    'talu' => 'New Tai Lue',
    'taml' => 'Tamil',
    'tavt' => 'Tai Viet',
    'telu' => 'Telugu',
    'teng' => 'Tengwar',
    'tfng' => 'Tifinagh',
    'tglg' => 'Tagalog',
    'thaa' => 'Thaana',
    'thai' => 'Thai',
    'tibt' => 'Tibetan',
    'ugar' => 'Ugaritic',
    'vaii' => 'Vai',
    'visp' => 'Visible Speech',
    'wara' => 'Varang Kshiti',
    'xpeo' => 'Old Persian',
    'xsux' => 'Sumero-Akkadian Cuneiform',
    'yiii' => 'Yi',
    'zinh' => 'Inherited',
    'zmth' => 'Mathematical Notation',
    'zsym' => 'Symbols',
    'zxxx' => 'Unwritten',
    'zyyy' => 'Common',
    'zzzz' => 'Unknown Script',
  ),
  'territories' => 
  array (
    '001' => 'World',
    '002' => 'Africa',
    '003' => 'North America',
    '005' => 'South America',
    '009' => 'Oceania',
    '011' => 'Western Africa',
    '013' => 'Central America',
    '014' => 'Eastern Africa',
    '015' => 'Northern Africa',
    '017' => 'Middle Africa',
    '018' => 'Southern Africa',
    '019' => 'Americas',
    '021' => 'Northern America',
    '029' => 'Caribbean',
    '030' => 'Eastern Asia',
    '034' => 'Southern Asia',
    '035' => 'South-Eastern Asia',
    '039' => 'Southern Europe',
    '053' => 'Australia and New Zealand',
    '054' => 'Melanesia',
    '057' => 'Micronesian Region',
    '061' => 'Polynesia',
    '062' => 'South-Central Asia',
    142 => 'Asia',
    143 => 'Central Asia',
    145 => 'Western Asia',
    150 => 'Europe',
    151 => 'Eastern Europe',
    154 => 'Northern Europe',
    155 => 'Western Europe',
    172 => 'Commonwealth of Independent States',
    200 => 'Czechoslovakia',
    419 => 'Latin America',
    830 => 'Channel Islands',
    'ac' => 'Ascension Island',
    'ad' => 'Andorra',
    'ae' => 'United Arab Emirates',
    'af' => 'Afghanistan',
    'ag' => 'Antigua and Barbuda',
    'ai' => 'Anguilla',
    'al' => 'Albania',
    'am' => 'Armenia',
    'an' => 'Netherlands Antilles',
    'ao' => 'Angola',
    'aq' => 'Antarctica',
    'ar' => 'Argentina',
    'as' => 'American Samoa',
    'at' => 'Austria',
    'au' => 'Australia',
    'aw' => 'Aruba',
    'ax' => 'Åland Islands',
    'az' => 'Azerbaijan',
    'ba' => 'Bosnia and Herzegovina',
    'bb' => 'Barbados',
    'bd' => 'Bangladesh',
    'be' => 'Belgium',
    'bf' => 'Burkina Faso',
    'bg' => 'Bulgaria',
    'bh' => 'Bahrain',
    'bi' => 'Burundi',
    'bj' => 'Benin',
    'bl' => 'Saint Barthélemy',
    'bm' => 'Bermuda',
    'bn' => 'Brunei',
    'bo' => 'Bolivia',
    'bq' => 'Bonaire, Saint Eustatius, and Saba',
    'br' => 'Brazil',
    'bs' => 'Bahamas',
    'bt' => 'Bhutan',
    'bv' => 'Bouvet Island',
    'bw' => 'Botswana',
    'by' => 'Belarus',
    'bz' => 'Belize',
    'ca' => 'Canada',
    'cc' => 'Cocos [Keeling] Islands',
    'cd' => 'Congo [DRC]',
    'cf' => 'Central African Republic',
    'cg' => 'Congo [Republic]',
    'ch' => 'Switzerland',
    'ci' => 'Ivory Coast',
    'ck' => 'Cook Islands',
    'cl' => 'Chile',
    'cm' => 'Cameroon',
    'cn' => 'China',
    'co' => 'Colombia',
    'cp' => 'Clipperton Island',
    'cr' => 'Costa Rica',
    'cs' => 'Serbia and Montenegro',
    'ct' => 'Canton and Enderbury Islands',
    'cu' => 'Cuba',
    'cv' => 'Cape Verde',
    'cw' => 'Curaçao',
    'cx' => 'Christmas Island',
    'cy' => 'Cyprus',
    'cz' => 'Czech Republic',
    'dd' => 'East Germany',
    'de' => 'Germany',
    'dg' => 'Diego Garcia',
    'dj' => 'Djibouti',
    'dk' => 'Denmark',
    'dm' => 'Dominica',
    'do' => 'Dominican Republic',
    'dz' => 'Algeria',
    'ea' => 'Ceuta and Melilla',
    'ec' => 'Ecuador',
    'ee' => 'Estonia',
    'eg' => 'Egypt',
    'eh' => 'Western Sahara',
    'er' => 'Eritrea',
    'es' => 'Spain',
    'et' => 'Ethiopia',
    'eu' => 'European Union',
    'fi' => 'Finland',
    'fj' => 'Fiji',
    'fk' => 'Falkland Islands [Islas Malvinas]',
    'fm' => 'Micronesia',
    'fo' => 'Faroe Islands',
    'fq' => 'French Southern and Antarctic Territories',
    'fr' => 'France',
    'fx' => 'Metropolitan France',
    'ga' => 'Gabon',
    'gb' => 'United Kingdom',
    'gd' => 'Grenada',
    'ge' => 'Georgia',
    'gf' => 'French Guiana',
    'gg' => 'Guernsey',
    'gh' => 'Ghana',
    'gi' => 'Gibraltar',
    'gl' => 'Greenland',
    'gm' => 'Gambia',
    'gn' => 'Guinea',
    'gp' => 'Guadeloupe',
    'gq' => 'Equatorial Guinea',
    'gr' => 'Greece',
    'gs' => 'South Georgia and the South Sandwich Islands',
    'gt' => 'Guatemala',
    'gu' => 'Guam',
    'gw' => 'Guinea-Bissau',
    'gy' => 'Guyana',
    'hk' => 'Hong Kong',
    'hm' => 'Heard Island and McDonald Islands',
    'hn' => 'Honduras',
    'hr' => 'Croatia',
    'ht' => 'Haiti',
    'hu' => 'Hungary',
    'ic' => 'Canary Islands',
    'id' => 'Indonesia',
    'ie' => 'Ireland',
    'il' => 'Israel',
    'im' => 'Isle of Man',
    'in' => 'India',
    'io' => 'British Indian Ocean Territory',
    'iq' => 'Iraq',
    'ir' => 'Iran',
    'is' => 'Iceland',
    'it' => 'Italy',
    'je' => 'Jersey',
    'jm' => 'Jamaica',
    'jo' => 'Jordan',
    'jp' => 'Japan',
    'jt' => 'Johnston Island',
    'ke' => 'Kenya',
    'kg' => 'Kyrgyzstan',
    'kh' => 'Cambodia',
    'ki' => 'Kiribati',
    'km' => 'Comoros',
    'kn' => 'Saint Kitts and Nevis',
    'kp' => 'North Korea',
    'kr' => 'South Korea',
    'kw' => 'Kuwait',
    'ky' => 'Cayman Islands',
    'kz' => 'Kazakhstan',
    'la' => 'Laos',
    'lb' => 'Lebanon',
    'lc' => 'Saint Lucia',
    'li' => 'Liechtenstein',
    'lk' => 'Sri Lanka',
    'lr' => 'Liberia',
    'ls' => 'Lesotho',
    'lt' => 'Lithuania',
    'lu' => 'Luxembourg',
    'lv' => 'Latvia',
    'ly' => 'Libya',
    'ma' => 'Morocco',
    'mc' => 'Monaco',
    'md' => 'Moldova',
    'me' => 'Montenegro',
    'mf' => 'Saint Martin',
    'mg' => 'Madagascar',
    'mh' => 'Marshall Islands',
    'mi' => 'Midway Islands',
    'mk' => 'Macedonia [FYROM]',
    'ml' => 'Mali',
    'mm' => 'Myanmar [Burma]',
    'mn' => 'Mongolia',
    'mo' => 'Macau',
    'mp' => 'Northern Mariana Islands',
    'mq' => 'Martinique',
    'mr' => 'Mauritania',
    'ms' => 'Montserrat',
    'mt' => 'Malta',
    'mu' => 'Mauritius',
    'mv' => 'Maldives',
    'mw' => 'Malawi',
    'mx' => 'Mexico',
    'my' => 'Malaysia',
    'mz' => 'Mozambique',
    'na' => 'Namibia',
    'nc' => 'New Caledonia',
    'ne' => 'Niger',
    'nf' => 'Norfolk Island',
    'ng' => 'Nigeria',
    'ni' => 'Nicaragua',
    'nl' => 'Netherlands',
    'no' => 'Norway',
    'np' => 'Nepal',
    'nq' => 'Dronning Maud Land',
    'nr' => 'Nauru',
    'nt' => 'Neutral Zone',
    'nu' => 'Niue',
    'nz' => 'New Zealand',
    'om' => 'Oman',
    'pa' => 'Panama',
    'pc' => 'Pacific Islands Trust Territory',
    'pe' => 'Peru',
    'pf' => 'French Polynesia',
    'pg' => 'Papua New Guinea',
    'ph' => 'Philippines',
    'pk' => 'Pakistan',
    'pl' => 'Poland',
    'pm' => 'Saint Pierre and Miquelon',
    'pn' => 'Pitcairn Islands',
    'pr' => 'Puerto Rico',
    'ps' => 'Palestinian Territories',
    'pt' => 'Portugal',
    'pu' => 'U.S. Miscellaneous Pacific Islands',
    'pw' => 'Palau',
    'py' => 'Paraguay',
    'pz' => 'Panama Canal Zone',
    'qa' => 'Qatar',
    'qo' => 'Outlying Oceania',
    're' => 'Réunion',
    'ro' => 'Romania',
    'rs' => 'Serbia',
    'ru' => 'Russia',
    'rw' => 'Rwanda',
    'sa' => 'Saudi Arabia',
    'sb' => 'Solomon Islands',
    'sc' => 'Seychelles',
    'sd' => 'Sudan',
    'se' => 'Sweden',
    'sg' => 'Singapore',
    'sh' => 'Saint Helena',
    'si' => 'Slovenia',
    'sj' => 'Svalbard and Jan Mayen',
    'sk' => 'Slovakia',
    'sl' => 'Sierra Leone',
    'sm' => 'San Marino',
    'sn' => 'Senegal',
    'so' => 'Somalia',
    'sr' => 'Suriname',
    'st' => 'São Tomé and Príncipe',
    'su' => 'Union of Soviet Socialist Republics',
    'sv' => 'El Salvador',
    'sx' => 'Sint Maarten',
    'sy' => 'Syria',
    'sz' => 'Swaziland',
    'ta' => 'Tristan da Cunha',
    'tc' => 'Turks and Caicos Islands',
    'td' => 'Chad',
    'tf' => 'French Southern Territories',
    'tg' => 'Togo',
    'th' => 'Thailand',
    'tj' => 'Tajikistan',
    'tk' => 'Tokelau',
    'tl' => 'East Timor',
    'tm' => 'Turkmenistan',
    'tn' => 'Tunisia',
    'to' => 'Tonga',
    'tr' => 'Turkey',
    'tt' => 'Trinidad and Tobago',
    'tv' => 'Tuvalu',
    'tw' => 'Taiwan',
    'tz' => 'Tanzania',
    'ua' => 'Ukraine',
    'ug' => 'Uganda',
    'um' => 'U.S. Minor Outlying Islands',
    'us' => 'United States',
    'uy' => 'Uruguay',
    'uz' => 'Uzbekistan',
    'va' => 'Vatican City',
    'vc' => 'Saint Vincent and the Grenadines',
    'vd' => 'North Vietnam',
    've' => 'Venezuela',
    'vg' => 'British Virgin Islands',
    'vi' => 'U.S. Virgin Islands',
    'vn' => 'Vietnam',
    'vu' => 'Vanuatu',
    'wf' => 'Wallis and Futuna',
    'wk' => 'Wake Island',
    'ws' => 'Samoa',
    'yd' => 'People’s Democratic Republic of Yemen',
    'ye' => 'Yemen',
    'yt' => 'Mayotte',
    'za' => 'South Africa',
    'zm' => 'Zambia',
    'zw' => 'Zimbabwe',
    'zz' => 'Unknown Region',
  ),
  'pluralRules' => 
  array (
    0 => 'n==1',
    1 => 'true',
  ),
);
