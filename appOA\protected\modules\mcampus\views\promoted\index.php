<style>
.labels span{
    padding: 1px 4px;
    font-weight: 100;
}
.ui-datepicker-trigger{
    margin-left: -27px;
    border: none;
    background: #fff;
    height: 27px;
    margin-right:15px
}
.glyphicon {
    top:2px
}
[v-cloak] {
    display: none;
}
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 25px;
    margin-bottom:0px;
    float:right
}

.switch input {display:none;}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0px;
    left: 0;
    right: 0;
    bottom:0px;
    background-color: #ccc;
    -webkit-transition: .4s;
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 21px;
    width: 21px;
    left: 1px;
    bottom: 2px;
    background-color: white;
    -webkit-transition: .4s;
    transition: .4s;
}

input:checked + .slider {
    background-color: #2196F3;
}

input:focus + .slider {
    box-shadow: 0 0 1px #2196F3;
}

input:checked + .slider:before {
    -webkit-transform: translateX(26px);
    -ms-transform: translateX(26px);
    transform: translateX(26px);
}

/* Rounded sliders */
.slider.round {
    border-radius: 34px;
}

.slider.round:before {
    border-radius: 50%;
}
.mt-20{
    margin-top:20px
}
.checkbox-inline{
    margin-top: 6px !important;
    margin-bottom: 6px;
}
.checkbox-inline:first-child{
    margin-left:10px
}
.classFilter{
    font-size: 12px;
    line-height: 25px;
    margin-right: 5px;

}
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Advanced'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('site', '学生留存报告'); ?></li>
    </ol>
    <div class="row">
        <div class="col-md-12">
            <div class="form-inline mb15">
                <div class="well well-sm">
                    <p>学生在读状态采样时间点</p>
                    <p>1. 为提高精确度，请在开始时间和结束时间各选择两个采样点</p>
                    <p>2. 日期选择请避开假期</p>
                    <p>3. 数据来源为学生学费账单，非出勤数据</p>
                    <p>4. 流失人数包含了正常毕业的人数</p>
                </div>
                开始日期：<input type="text" class="form-control form-group datepicker" id="pre_start" placeholder="采样点1">
                <input type="text" class="form-control form-group datepicker" id="pre_end" placeholder="采样点2">
                结束日期：<input type="text" class="form-control form-group datepicker" id="cur_start" placeholder="采样点1">
                <input type="text" class="form-control form-group datepicker" id="cur_end" placeholder="采样点2">
                <select class="form-control form-group" id="next_school">
                    <?php foreach ($branckInfo as $key=>$val): ?>
                        <option value ="<?php echo $key ?>"><?php echo $val ?></option>
                    <?php  endforeach;?>
                </select>
                <button type="button" class="btn btn-primary btn-sm ml15" id="search" onclick='search()'>查询</button>
                <a href="<?php echo $this->createUrl("promoted") ?>" class='pull-right'>基础查询
                <span aria-hidden="true">&rarr;</span></a>

            </div>
        </div>
        <div id='container' v-cloak>
            <div class="col-md-6" v-if='data.previous'>
                <div class="panel panel-default">
                    <div class="panel-body">
                        <h3 class="">{{data.previous.pre_name}}
                            <span class="label label-success">留存：{{data.previous.pre_up}}</span>
                            <span class="label label-danger">流失：{{data.previous.pre_down}}</span>
                            <span class="label label-info">留存率：{{data.previous.pre_percent}}%</span>
                            <label class="switch" @change="keepNum">
                                <input type="checkbox" v-model="keepCheck">
                                <div class="slider round"></div>
                            </label>
                            <span class='pull-right classFilter'>按班级过滤</span>
                        </h3>
                        <p class='mt-20 alert alert-info' v-if='keepCheck'>
                            <label class="checkbox-inline"  v-for='item in cloneprevious'>
                                <input type="checkbox" :value="item.class_name"  v-model="keepCheckedClass">{{item.class_name}}
                            </label>
                        </p>
                        <hr>
                        <template v-for='item in data.previous.items'>
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    {{item.class_name}}
                                    <span class="label label-success">留存：{{item.class_up}}</span>
                                    <span class="label label-danger">流失：{{item.class_down}}</span>
                                    <span class="pull-right">留存率</span>
                                </div>
                                <div class="panel-body row">
                                    <div class="col-md-10">
                                        <ul class="nav nav-pills">
                                            <li v-for="child in item.children">
                                                <a target="_blank" class='labels' :href="'<?php echo $this->createUrl('/child/index/index'); ?>&childid='+child.childid">{{child.name}} <span :class="status_color[child.status]">{{status_text[child.status]}}</span></a>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="col-md-2 text-right">
                                        <h2 ><span :class="[item.pre_percent>60?'label label-success':'label label-warning']">{{item.pre_percent}}%</span></h2>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
            <div class="col-md-6"  v-if='data.current'>
                <div class="panel panel-default">
                    <div class="panel-body">
                        <h3>{{data.current.cur_name}}
                            <span class="label label-success">留存：{{data.current.cur_up}}</span>
                            <span class="label label-primary">新生：{{data.current.cur_new}}</span>
                            <span class="label label-info">新生率：{{data.current.cur_percent}}%</span>
                            <label class="switch" @change="newborn">
                                <input type="checkbox" v-model="knewbornCheck">
                                <div class="slider round"></div>
                            </label>
                            <span class='pull-right classFilter'>按班级过滤</span>
                        </h3>
                        <p class='mt-20 alert alert-info' v-if='knewbornCheck'>
                            <label class="checkbox-inline"  v-for='item in clonecurrent'>
                                <input type="checkbox" :value="item.class_name"  v-model="knewbornCheckedClass">{{item.class_name}}
                            </label>
                        </p>
                        <hr>
                        <template v-for='item in data.current.items'>
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    {{item.class_name}}
                                    <span class="label label-success">留存：{{item.class_up}}</span>
                                    <span class="label label-primary">新生：{{item.class_new}}</span>
                                    <span class="pull-right">新生率</span>
                                </div>
                                <div class="panel-body row">
                                    <div class="col-md-10">
                                        <ul class="nav nav-pills">
                                            <li v-for="child in item.children">
                                                <a target="_blank" class='labels'  :href="'<?php echo $this->createUrl('/child/index/index'); ?>&childid='+child.childid">{{child.name}} <span :class="status_color[child.status]">{{status_text[child.status]}}</span></a>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="col-md-2 text-right">
                                    <h2 ><span class='label label-primary'>{{item.cur_percent}}%</span></h2>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    $("#next_school option[value='<?php echo $schoolid ?>']").prop("selected", true);   
   if(getCookie('pre_start')!=null){
    $('#pre_start').val(getCookie('pre_start'))
    $('#pre_end').val(getCookie('pre_end'))
    $('#cur_start').val(getCookie('cur_start'))
    $('#cur_end').val(getCookie('cur_end'))
    search() 
   }
    $(function() {
        $(".datepicker").datepicker({
            showOn: 'button',
            dateFormat: "yy-mm-dd",
            changeMonth: true,
            changeYear: true,
            buttonClass:'texts',
            buttonText: "<i class='glyphicon glyphicon-calendar'></i>"
        });
    });
    function search() {
        var pre_start = $('#pre_start').val();
        var pre_end = $('#pre_end').val();
        var cur_start = $('#cur_start').val();
        var cur_end = $('#cur_end').val();
        var next_school = $('#next_school').val();
        if (!pre_start || !pre_end || !cur_start || !cur_end) {
            resultTip({
                error: 'warning',
                msg: '请选择日期'
            });
            return
        }
        $('#search').addClass('disabled').attr('disabled', 'disabled');
        $('#search').html('查询中...');
        $.ajax({
            url: '<?php echo $this->createUrl("promotedData") ?>',
            type: "post",
            dataType: 'json',
            data: {
                pre_start: pre_start,
                pre_end: pre_end,
                cur_start: cur_start,
                cur_end: cur_end,
                next_school: next_school
            },
            success: function(res) {
                $('#search').removeClass('disabled').removeAttr('disabled');
                $('#search').html('查询');
                if (res.state == "success") {
                    container.data={}
                    container.cloneprevious=[]
                    container.clonecurrent=[]
                    container.knewbornCheckedClass=[]
                    container.keepCheckedClass=[]
                    container.data = res.data
                    if(res.data.previous.items){
                        container.cloneprevious=deepClone2(res.data.previous.items);
                        for(var i=0;i<res.data.previous.items.length;i++){
                            container.keepCheckedClass.push(res.data.previous.items[i].class_name)
                        }
                    }
                    if(res.data.current.items){
                        container.clonecurrent=deepClone2(res.data.current.items);
                        for(var i=0;i<res.data.current.items.length;i++){
                            container.knewbornCheckedClass.push(res.data.current.items[i].class_name)
                        }
                    }
                   
                    setCookie('pre_start',  $('#pre_start').val())
                    setCookie('pre_end',  $('#pre_end').val())
                    setCookie('cur_start',  $('#cur_start').val())
                    setCookie('cur_end',  $('#cur_end').val())
                } else {
                    resultTip({
                        error: 'warning',
                        msg: res.message
                    });
                    $('#search').removeClass('disabled').removeAttr('disabled');
                }
            },
            error: function() {
                $('#search').removeClass('disabled').removeAttr('disabled');
                resultTip({
                    error: 'warning',
                    msg: '请求错误'
                });
            }
        });
    }
    function setCookie(name, value) {
        document.cookie = name + "=" + escape(value) + ";path=/"; 
    } 
    function getCookie(name) { 
        var arr, reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)"); 
        if (arr = document.cookie.match(reg)) 
            return unescape(arr[2]); 
        else 
            return null; 
    } 
    function deepClone2(obj){
        return JSON.parse(JSON.stringify(obj))
    }
    var container = new Vue({
        el: "#container",
        data: {
            data: {},
            status_color: {
                'up': ' label  label-success',
                'down': ' label  label-danger',
                'new': 'label  label-primary'
            },
            status_text: {
                'up': '留',
                'down': '失',
                'new': '新'
            },
            keepCheck:false,
            keepCheckedClass:[],
            cloneprevious:[],
            knewbornCheck:false,
            clonecurrent:[],
            knewbornCheckedClass:[]
        },
        watch:{
            keepCheckedClass(){
                if(!this.data.previous.items){
                    return
                }
                var item=[]
                let previousData=this.cloneprevious
                for(var i=0;i<previousData.length;i++){
                    for(var j=0;j<this.keepCheckedClass.length;j++){
                        if(previousData[i].class_name==this.keepCheckedClass[j]){
                            item.push(previousData[i])
                        }
                    }
                }
                this.data.previous.items=item
                var Keep=0
                var Churn=0
                for(var i=0;i<item.length;i++){
                    Keep+=parseInt(item[i].class_down)
                    Churn+=parseInt(item[i].class_up)
                }
                this.data.previous.pre_up=Churn
                this.data.previous.pre_down=Keep
                this.data.previous.pre_total=Keep+Churn
                if(Keep==0 || Churn==0){
                    this.data.previous.pre_percent=0.00.toFixed(2)
                }else{
                this.data.previous.pre_percent=(Churn/(Keep+Churn)* 100).toFixed(2)
                }
            },
            knewbornCheckedClass(){
                if(!this.data.current.items){
                    return
                }
                var item=[]
                let currentData=this.clonecurrent
                for(var i=0;i<currentData.length;i++){
                    for(var j=0;j<this.knewbornCheckedClass.length;j++){
                        if(currentData[i].class_name==this.knewbornCheckedClass[j]){
                            item.push(currentData[i])
                        }
                    }
                }
                this.data.current.items=item
                var Keep=0
                var Churn=0
                for(var i=0;i<item.length;i++){
                    Keep+=parseInt(item[i].class_new)
                    Churn+=parseInt(item[i].class_up)
                }
                this.data.current.cur_up=Churn
                this.data.current.cur_new=Keep
                this.data.current.cur_total=Keep+Churn
                if(Keep==0 || Churn==0){
                    this.data.current.cur_percent=0.00.toFixed(2)
                }else{
                    this.data.current.cur_percent=(Keep/(Keep+Churn)* 100).toFixed(2)
                }
            }
        },
        updated: function() {
        },
        methods: {
            keepNum(){
            },
            newborn(){

            }
        }
    })
</script>
<!-- 显示底部学校选择栏 -->
<?php $this->renderPartial('//layouts/common/branchSelectBottom'); ?>
