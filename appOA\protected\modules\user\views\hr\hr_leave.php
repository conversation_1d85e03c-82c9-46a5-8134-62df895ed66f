
<?php 
$form=$this->beginWidget('CActiveForm', array(
	'id'=>'user-form',
	'enableAjaxValidation'=>false,
        'htmlOptions'=>array('class'=>'J_ajaxForm'),
)); 
$typeList = UserLeave::getLeaveType();
?>
    <?php if ($data['leaveList']['valid'] === TRUE):?>
    <div class="table_full">
        <table style="width:550px;" class="table_list">
            <tbody>
                <colgroup>
                    <col width="200">
                </colgroup>
                <?php if (in_array($model->type, array(UserLeave::TYPE_ANNUAL_LEAVE,UserLeave::TYPE_SICK_LEAVE,UserLeave::TYPE_DAYS_OFF))):?>
                <tr>
                    <th><?php echo Yii::t('hr', 'type'); ?></th>
                    <td><?php echo $typeList[$model->type];?></td>
                </tr>
                <tr>
                    <td></td>
                    <td>
                        <?php 
                            foreach ($data['leaveList']['info'] as $k=>$val):
                                if ($val['hours']):
                                    $leave[$k] = Yii::t('hr', '剩余').$typeList[$model->type].UserLeave::getLeaveFormat($val['hours']).'('.OA::formatDateTime($val['startdate']).'/'.OA::formatDateTime($val['enddate']).')';
                                endif;
                            endforeach;
                            echo $form->radioButtonList($model, 'leave_id', $leave, array('template' => '<li style="float:left;padding-right:10px;">{input}{label}</li>', 'separator' => ''));
                        ?>
                    </td>
                </tr>
                <?php else:?>
                <tr>
                    <th><?php echo Yii::t('hr', 'type'); ?></th>
                    <td>
                        <?php echo $typeList[$model->type];?>
                        <?php echo $form->hiddenField($model, 'leave_id');?>
                    </td>
                </tr>
                <?php endif;?>
                <tr>
                    <th><?php echo Yii::t("hr", "请假天数");?></th>
                    <?php
                        for ($i=0;$i<21;$i++){
                            $day[$i] = $i.Yii::t('hr', '天');
                        }
                        for ($i=0;$i<8;$i++){
                            $hour[$i] = $i.Yii::t('hr', '小时');
                        }
                    ?>
                    <td>
                        <?php echo $form->dropDownList($model,'leave_num',$day);?>
                        <?php echo $form->dropDownList($model,'leave_hour',$hour);?>
                    </td>
                </tr>
                <tr>
                    <th><?php echo $form->labelEx($model, 'startdate'); ?></th>
                    <td>
                        <?php
                            $this->widget('common.extensions.datePicker.JMy97DatePicker', array(
                                    'name'=>CHtml::activeName($model,'startdate'),
                                    'value'=>$model->startdate,
                                    'options'=>array('dateFmt'=>'yyyy-MM-dd','opposite'=>true),
                                    'htmlOptions'=>array('readonly'=>'readonly', 'class'=>'input'),
                            ));
                        ?>
                    </td>
                </tr>
                <tr>
                    <th><?php echo $form->labelEx($model, 'enddate'); ?></th>
                        <td>
                            <?php
                                $this->widget('common.extensions.datePicker.JMy97DatePicker', array(
                                      'name'=>CHtml::activeName($model,'enddate'),
                                      'value'=>$model->enddate,
                                      'options'=>array('dateFmt'=>'yyyy-MM-dd','opposite'=>true),
                                      'htmlOptions'=>array('readonly'=>'readonly', 'class'=>'input'),
                                ));
                            ?>
                        </td>
                </tr>
               
            </tbody>
            </table>
     </div>
    <?php
        echo $form->hiddenField($model, 'type');
        echo $form->hiddenField($model, 'staff_uid');
    ?>
    <div id="J_submit_tips"></div>
    <div class="pop_bottom tar">
        <button type="submit" class="btn btn_submit mr10 J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
        <button id="J_dialog_close" type="button" class="btn btn_cancel"><?php echo Yii::t('global','Cancel');?></button>
    </div>
    <?php else: ?>
    <div class="not_content_mini">
        <i></i><?php echo sprintf(Yii::t('hr','您的%s已经用完了，请选择其它类型假期！'),$typeList[$model->type]);?>
    </div>
    <?php endif;?>
<?php $this->endWidget(); ?>
<!-- form -->
