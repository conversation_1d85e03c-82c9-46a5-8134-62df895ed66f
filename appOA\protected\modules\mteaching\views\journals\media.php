<style>
    [v-cloak] {
        display: none;
    }

    .mt10{
        margin-top:10px
    }
    img{
        height: 100%;
        max-width: 100%;
    }
    .mr10{
        margin-right:10px
    }
    .imageCss{
        border:1px solid #DCDFE6;
        padding:4px;
        border-radius: 4px;
        width:120px;
        height:100px;
        text-align:center
    }
    .relative{
        position: relative;
        padding:0px !important
    }
    .absolute{
        position: absolute !important;
        left: 25px;
    }
</style>
<link href="<?php echo Yii::app()->theme->baseUrl; ?>/css/bootstrap.css" rel="stylesheet">
    <link href="<?php echo Yii::app()->theme->baseUrl; ?>/css/css.css" rel="stylesheet">
<div class="container-fluid" id='container'>
    <div class='mt10'>
        <div class='col-sm-4 col-xs-4'>
            <select class="form-control" v-model='yid' @change='getClass()'>
                <option v-for='(list,index) in yearList' :value='list.yid'>{{list.title}}</option>
            </select>
        </div>
        <div class='col-sm-4 col-xs-4'>
            <select class="form-control" v-model='classId' @change='getWeek()'>
                <option value="" disabled selected><?php echo Yii::t("newDS", "Select a class");?></option>  
                <option v-for='(list,index) in classList' :value='list.classid'>{{list.title}}</option>
            </select>
        </div>
        <div class='col-sm-4 col-xs-4' >
            <select class="form-control" v-model='weekId' @change='getData()'>
                <option value="" disabled selected><?php echo Yii::t("newDS", "Select a week");?></option>  
                <option v-for='(list,index) in weekList' :value='list.weeknumber'>{{list.title}}</option>
            </select>
        </div>
    </div>
    <div class='clearfix'></div>
    <div class='mt10 col-sm-12 col-xs-12' >
        <div v-if='dataList.length!=0'>
            <div class='mr10 mt10 pull-left imageCss ' v-for='(list,index) in  dataList'>
                <label class="checkbox-inline relative">
                    <input type="checkbox" class='absolute' id="inlineCheckbox1" value="option1" v-model='list.checked' @click='checked(list.checked,list)'>
                    <img :src="list.url"  alt="">

                        <!-- <video v-else width="auto" height="100" :poster="list._url" >
                            <source :src="list._url" type="video/mp4">
                        </video> -->
                </label>
            </div>
        </div>
        <div class='clearfix'></div>
        <div>
            <nav aria-label="Page navigation pull-right" v-if='page.pageCount && page.pageCount>1'>
                <ul class="pagination">
                    <li v-for='(list,index) in page.pageCount' :class="{ active:index+1==page.pageSize }"><a href="javascript:;" @click='getData(list)'>{{list}}</a></li>
                </ul>
            </nav>
        </div>
    </div>
</div>
<script src="<?php echo Yii::app()->themeManager->baseUrl; ?>/base/js/vue2.js"></script>
<script>
    var calendarInfo = <?php echo json_encode($calendarInfo); ?>;
    var yid = '<?php echo $yid; ?>';
   
     var container = new Vue({
        el: "#container",
        data: {
            yearList:calendarInfo,
            yid:yid,
            classList:[],
            classId:'',
            weekList:[],
            weekId:'',
            dataList:[],
            checkData:{},
            checkList:[],
            page:{}
        },
        watch: {
           
        },
        created: function() {
            window.parent.postMessage({
                mceAction: 'loading',
                content: {}
            }, '*');
            this.getClass()
        },
        methods: {
            getClass(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getClass")?>',
                    type: "post",
                    dataType: 'json',
                    data: {yid:that.yid},
                    success: function(data) {
                        if(data.state=='success'){  
                          that.classList=data.data
                          that.weekId=''
                          that.classId=''
                          that.dataList=[]
                          that.page={}
                        }
                    },
                    error: function(data) {
                        
                    },
                })
            },
            getWeek(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("fetchWeekly")?>',
                    type: "post",
                    dataType: 'json',
                    data: {yid:that.yid,classId:that.classId},
                    success: function(data) {
                        if(data.state=='success'){  
                          that.weekList=data.data
                          that.weekId=''
                          that.dataList=[]
                          that.page={}
                        }
                    },
                    error: function(data) {
                        
                    },
                })
            },
            getData(page){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("fetchWeekly")?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        yid:that.yid,
                        classId:that.classId,
                        weekNum:that.weekId,
                        pageSize:page?page:1
                    },
                    success: function(data) {
                        if(data.state=='success'){  
                          for(var i=0;i<data.data.data.length;i++){
                            data.data[i].checked=false
                          }
                          that.dataList=data.data.data
                          that.page=data.data.pageData
                        }
                    },
                    error: function(data) {
                        
                    },
                })
            },
            checked(checked,list){
                this.checkData=list
                if(checked){
                    this.checkList.push(this.checkData)
                    this.addImg()
                }else{
                    this.removeImg() 
                    this.checkList.splice(0,1)
                }
            },
            addImg(){
                let that=this
                if((/Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent))){
                    window.parent.postMessage({
                        mceAction: 'removeItem',
                        content:that.checkData
                    }, '*');
                }else{
                    window.parent.postMessage({
                        mceAction: 'addItem',
                        content: that.checkData
                    }, '*');
                }
            },
            removeImg(){
                let that=this
                if((/Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent))){
                    window.parent.postMessage({
                        mceAction: 'addItem',
                        content: that.checkData
                    }, '*');
                }else{
                    window.parent.postMessage({
                        mceAction: 'removeItem',
                        content:that.checkData
                    }, '*');
                }
                
            }
        }
    })
</script>
