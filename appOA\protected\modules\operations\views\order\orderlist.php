<div class="table_list" id="order_list" style="padding: 0 10px;">
    <h3 style="font-family: 'Microsoft Yahei'">#<?php echo $model->id?> 积分兑换礼品清单</h3>
    <h5 style="font-family: 'Microsoft Yahei'">打包人：<?php echo $model->updateUser->getName();?> 打包时间：<?php echo OA::formatDateTime($model->update_timestamp)?></h5>
	<table width="100%" style="table-layout:fixed;">
        <colgroup>
            <col width="200">
            <col width="80">
            <col width="50">
            <col width="100">
            <col width="100">
            <col width="100">
            <col width="200">
        </colgroup>
        <thead>
            <tr>
                <th>产品</th>
                <th>积分</th>
                <th>数量</th>
                <th>孩子</th>
                <th>家长</th>
                <th>学校</th>
                <th><?php echo Yii::t('labels', 'Created Time');?></th>
            </tr>
        </thead>
		<tbody>
            <?php foreach ($orders as $order):?>
            <tr>
                <td><?php echo $order->Product->getContent();?></td>
                <td><?php echo $order->credits;?></td>
                <td><?php echo $order->quantity;?></td>
                <td><?php echo $order->ChildProfile->getChildName();?></td>
                <td><?php echo $order->orderUser->getName();?></td>
                <td><?php echo $order->school->title;?></td>
                <td><?php echo OA::formatDateTime($order->created_timestamp, "medium", "short");?></td>
            </tr>
            <?php endforeach;?>    
		</tbody>

    </table>
</div>
<div style="text-align: center;">
    <?php echo CHtml::link(CHtml::image(Yii::app()->theme->baseUrl.'/images/printer32.png'), '#', array('onclick'=>'_print();'));?>
</div>
<script type="text/javascript">
function _print()
{
    if ($.browser.msie) {
        $("#order_list").printArea({
            mode: 'popup', popClose: true
        });
    }
    else {
        $("#order_list").printArea();
    }
    return false;
}
</script>