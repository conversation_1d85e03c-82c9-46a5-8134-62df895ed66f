<div style="color:#274257;">
    <div style="font-family: 'Microsoft Yahei'">
        <?php echo $mailDesc;?>
        <h2 style="font-size:14px;">
            <?php echo $branch->title; ?>
        </h2>
        <table border='1' cellpadding='2' cellspacing='0' width="90%">
            <tr>
                <th width="100" style="font-family: 'Microsoft Yahei';font-size:12px;">孩子姓名</th>
                <th width="100" style="font-family: 'Microsoft Yahei';font-size:12px;">年龄</th>
                <th width="100" style="font-family: 'Microsoft Yahei';font-size:12px;">当前状态</th>
                <th width="120" style="font-family: 'Microsoft Yahei';font-size:12px;">学年</th>
                <th style="font-family: 'Microsoft Yahei';font-size:12px;">押金余额</th>
            </tr>
            <?php
            foreach(array_keys($result) as $childid):
                $countbyChild =  count($result[$childid]);
                $keysbyChild = array_keys($result[$childid]);
            ?>
            <tr>
                <td style="font-family: 'Microsoft Yahei';font-size:12px;" 
                <?php if($countbyChild>1) echo sprintf(" rowspan=%s", $countbyChild);?>
                >
                    <?php echo $children[$childid]->getChildName() . ' ' . $childid;?>
                </td>
                
                <td style="font-family: 'Microsoft Yahei';font-size:12px;">
                    <?php echo OA::getAge($children[$childid]->birthday);?>
                </td>
                <td style="font-family: 'Microsoft Yahei';font-size:12px;">
                    <?php echo OA::getChildStatus($children[$childid]->status);?>
                </td>
                <td style="font-family: 'Microsoft Yahei';font-size:12px;">
                    <?php echo sprintf('%s - %s', $keysbyChild[0], $keysbyChild[0] + 1);?>
                </td>
                <td style="font-family: 'Microsoft Yahei';font-size:12px;">
                    <?php echo $result[$childid][$keysbyChild[0]];?>
                </td>
            </tr>
            
            <?php
            if($countbyChild > 1):
            
            for($i=1; $i<$countbyChild; $i++){
            ?>
            <tr>                
                <td style="font-family: 'Microsoft Yahei';font-size:12px;">
                    <?php echo sprintf('%s - %s', $keysbyChild[$i], $keysbyChild[$i] + 1);?>
                </td>
                <td style="font-family: 'Microsoft Yahei';font-size:12px;">
                    <?php echo $result[$childid][$keysbyChild[$i]];?>
                </td>
            </tr>
            
            <?php
            }
            endif;
            ?>
            
            
            <?php endforeach;?>
        </table>
    </div>
</div>