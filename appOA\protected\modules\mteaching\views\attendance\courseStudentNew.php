<!-- 加载底部导航栏 -->
<?php
$this->branchSelectParams['extraUrlArray'] = array('//mgrades/attendance/index');

$timeData = array(5=>5, 10=>10, 15=>15, 20=>20);
?>

<div class="row modal-text">

    <div class="col-md-12">
        <?php
        $form=$this->beginWidget('CActiveForm', array(
            'id'=>'visits-form',
            'enableAjaxValidation'=>false,
            'action' => $this->createUrl('updateRecords',array('tid' => $tid, 'datatime'=>$datatime,'weekday'=>$weekday,'course_code'=>$course_code, 'teacherId' => $uid,)),
            'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
        ));
        ?>
        <div class="row">
            <div class="col-md-8">
                <h4>
                    <span><?php echo isset($courseModel) ? $courseModel->getTitle() : "" ; ?></span>
                </h4>
                <p style="display: inline-block; position: relative;">
                    <span class="glyphicon glyphicon-time"></span>
                    <?php
                        $week = substr( $weekday, 0, 1 );
                        $period = substr($weekday,-1,1);
                        $weekArr = array(
                            1=> Yii::t('attends','Mon'),
                            2=> Yii::t('attends','Tue'),
                            3=> Yii::t('attends','Wed'),
                            4=> Yii::t('attends','Thu'),
                            5=> Yii::t('attends','Fri'),
                            );
                        $getTime = TimetableCourses::getTime();
                        echo sprintf(Yii::t('attends', '%s period #%s'), $weekArr[$week], $period);
                    ?>
                    <span class="label label-info"><?php echo $getTime[$period]; ?></span>
                    <?php if($next_same){?>
<!--                    <div style="display: inline-block; position: relative;">-->
                        <input class="checkbox-inline ml20" type="checkbox" value="1" name="nextPeriod" checked>
                        <span><?php echo Yii::t('attends','Sync attendance to next consecutive period');?></span>
                        <span class="newIcon">new</span>
<!--                    </div>-->
                    <?php } ?>
                </p>
            </div>
            <div class="col-md-4 text-right">
                <a class="btn btn-primary" href="<?php echo $this->createUrl('courseStudentPrint', array('uid'=>$uid, 'tid'=>$tid, 'weekday'=>$weekday, 'course_code'=>$course_code)); ?>" target="_blank">
                <span class="glyphicon glyphicon-print" aria-hidden="true"> </span> <?php echo Yii::t('global', 'Print') ?></a>
            </div>
        </div>
        <?php if($status < 1):?>
            <div class="alert alert-danger"><?php echo Yii::t('attends','View Only! You have no attendance permission of this course.');?></div>
        <?php endif;?>
        <hr>
            <input type="hidden" value='<?php echo $status ?>' id='status'>
        <table class="table">
            <thead>
            <tr>
                <th><?php echo Yii::t('attends','Student List') ?></th>
                <th><?php echo Yii::t('attends','Attendance') ?></th>
                <th><?php echo Yii::t('attends','Dress Code Violation') ?></th>
<!--                <th>--><?php //echo Yii::t('attends','Device Confiscated') ?><!--</th>-->
                <th><?php echo Yii::t('attends','Memo') ?></th>
            </tr>
            <tr>
                <th width="50"></th>
                <th width="400">
                    <div class="btn-group buttons attendance"role="group" style="margin: 5px 0;">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <?php echo Yii::t('attends','All Present') ?>
                                <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a href="javascript:" onclick="allSet(10)"><?php echo Yii::t('attends', 'Present');?></a></li>
                                <li><a href="javascript:" onclick="allSet(11)"><?php echo Yii::t('attends', 'Online Present');?></a></li>
                            </ul>
                        </div>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <?php echo Yii::t('attends','All Tardy') ?>
                                <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu">
                                <?php foreach ($timeData as $tda):?>
                                    <li><a href="javascript:" onclick="allSet(20, <?php echo $tda;?>)"><?php echo sprintf(Yii::t('attends','%s Minutes'), $tda);?></a></li>
                                <?php endforeach;?>
                            </ul>
                        </div>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <?php echo Yii::t('attends','All Leave') ?>
                                <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a href="javascript:" onclick="allSet(30)"><?php echo Yii::t('campus', 'Personal Leave');?></a></li>
                                <li><a href="javascript:" onclick="allSet(31)"><?php echo Yii::t('campus', 'Sick Leave');?></a></li>
                            </ul>
                        </div>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" ">
                            <?php echo Yii::t('attends','All Absent') ?>
                            <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a href="javascript:" onclick="allSet(40)"><?php echo Yii::t('attends', 'Absent');?></a></li>
                                <li><a href="javascript:" onclick="allSet(41)"><?php echo Yii::t('attends', 'Internal Suspension');?></a></li>
                                <li><a href="javascript:" onclick="allSet(42)"><?php echo Yii::t('attends', 'External Suspension');?></a></li>
                            </ul>
                        </div>
                    </div>
                </th>
                <th width="204">
                    <div class="btn-group buttons">
                        <label class="btn btn-default" onclick="allSets(1, 0)"><?php echo Yii::t('attends','All Violated') ?></label>
                        <label class="btn btn-default" onclick="allSets(0, 0)"><?php echo Yii::t('attends','None violated') ?></label>
                    </div>
                </th>
<!--                <th width="204">-->
<!--                    <div class="btn-group buttons attendance"role="group" style="margin: 5px 0;">-->
<!--                        <div class="btn-group" role="group">-->
<!--                            <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">-->
<!--                                --><?php //echo Yii::t('attends','Violated') ?>
<!--                                <span class="caret"></span>-->
<!--                            </button>-->
<!--                            <ul class="dropdown-menu">-->
<!--                                <li><a href="javascript:" onclick="allSet(61)">--><?php //echo Yii::t('attends', 'Phone');?><!--</a></li>-->
<!--                                <li><a href="javascript:" onclick="allSet(62)">--><?php //echo Yii::t('attends', 'Other Personal Devices');?><!--</a></li>-->
<!--                                <li><a href="javascript:" onclick="allSet(63)">--><?php //echo Yii::t('attends', 'Both');?><!--</a></li>-->
<!--                            </ul>-->
<!--                        </div>-->
<!--                        <button type="button" class="btn btn-default" onclick="allSets(60, 0)">--><?php //echo Yii::t('attends','Not violated') ?><!--</button>-->
<!---->
<!--                    </div>-->
<!--                </th>-->
                <th></th>
            </tr>
            </thead>
            <tbody>
            <?php foreach ($studentList as $key => $val){ ?>
                <tr id="child_<?php echo $key;?>">
                    <td width="90">
                        <div class="text-center mt8">
                            <img class="img-circle" src="<?php echo CommonUtils::childPhotoUrl($val['photo'], 'small') ?>" style="width: 48px;">
                        </div>

                        <div class="text-center mt2 mb2">
                            <span><?php echo $val['childName'] ?></span>
                            <p style="display: none;"><span class="badge"><?php echo ($val['gender'] == 1) ? Yii::t('attends','Boy') : Yii::t('attends','Girl') ; ?></span></p>
                        </div>

                        <div class="text-center mb8">
                            <?php foreach ($val['childLabel'] as $k=>$item){?>
                                <span title="<?php echo $item['desc']?>" class="flag_span hover-cursor" style="color: rgb(<?php echo $item['color']?>);">
                                    <?php echo $item['name']?>
                                    <span class=""><?php echo $item['flag_text']?></span>
                                </span>
                            <?php }?>
                        </div>
                    </td>
                    <?php $is_admin = (isset($recordsArr) && isset($recordsArr[$key])) ? $recordsArr[$key]['is_admin'] : 0; ?>
                    <td class='text-center' width="300" style="line-height: 70px;text-align: center; vertical-align: middle;">
                        <div id="action_<?php echo $key?>" class="attendance btn-group buttons <?php echo ($is_admin) ? '' : 'act'; ?>" data-toggle="buttons" <?php echo ($is_admin) ? '' : 'act'; ?>>
                            <?php $active = (isset($recordsArr) && isset($recordsArr[$key])) ? $recordsArr[$key]['type'] : 0; ?>
                            <div class="btn-group" role="group">
                                <button class="btn btn-default btn_label_10 <?php echo in_array($active, array(10,11)) ? (($is_admin) ? 'active disabled' : 'active') : (($is_admin) ? 'disabled' : '')?>" data-toggle="dropdown" aria-haspopup="true" style="margin-left: -1px;"  onclick='typeSel()'>
                                    <input type="radio" autocomplete="off" <?php echo $active == 10 ? 'checked' : '';?> name="type[<?php echo $key; ?>]" id="hospital_10_<?php echo $key; ?>" value="10" style="display: none">
                                    <input type="radio" autocomplete="off" <?php echo $active == 11 ? 'checked' : '';?> name="type[<?php echo $key; ?>]" id="hospital_11_<?php echo $key; ?>" value="11" style="display: none">
                                    <span class="tflag"><?php echo $active == 10 ? Yii::t('attends', 'Present') : (($active == 11) ? Yii::t('attends', 'Online Present') :  Yii::t('attends','Present')) ?></span>
                                    <span class="caret"></span>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a href="javascript:" onclick="types1(this, <?php echo $key;?>, 10,0)"><?php echo Yii::t('attends', 'Present');?></a></li>
                                    <li><a href="javascript:" onclick="types1(this, <?php echo $key;?>, 11,0)"><?php echo Yii::t('attends', 'Online Present');?></a></li>
                                </ul>
                            </div>
                            <div class="btn-group" role="group">
                                <button class="btn btn-default btn_label_20 <?php echo $active == 20 ? (($is_admin) ? 'active disabled' : 'active') : (($is_admin) ? 'disabled' : '')?>" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"  onclick='typeSel()'>
                                    <input type="radio" autocomplete="off" <?php echo $active == 20 ? 'checked' : '';?> name="type[<?php echo $key; ?>]" id="hospital_2_<?php echo $key; ?>" value="20" style="display: none;">
                                    <span class="tflag"><?php echo $active == 20 ? $recordsArr[$key]['late_time'].' '. Yii::t('attends','Minutes') : Yii::t('attends','Tardy') ?></span>
                                    <span class="caret"></span>
                                    <input type="hidden" name="select[<?php echo $key?>]" id="late_<?php echo $key?>" value="<?php echo $recordsArr[$key]['late_time'];?>">
                                </button>
                                <ul class="dropdown-menu">
                                    <?php foreach ($timeData as $tda):?>
                                        <li><a href="javascript:" onclick="types(this, <?php echo $key.', '.$tda;?>)"><?php echo sprintf(Yii::t('attends','%s Minutes'), $tda);?></a></li>
                                    <?php endforeach;?>
                                </ul>
                            </div>
                            <div class="btn-group" role="group">
                                <button class="btn btn-default btn_label_30 <?php echo in_array($active, array(30,31)) ? (($is_admin) ? 'active disabled' : 'active') : (($is_admin) ? 'disabled' : '')?>" data-toggle="dropdown" aria-haspopup="true" style="margin-left: -1px;"  onclick='typeSel()'>
                                    <input type="radio" autocomplete="off" <?php echo $active == 30 ? 'checked' : '';?> name="type[<?php echo $key; ?>]" id="hospital_30_<?php echo $key; ?>" value="30" style="display: none">
                                    <input type="radio" autocomplete="off" <?php echo $active == 31 ? 'checked' : '';?> name="type[<?php echo $key; ?>]" id="hospital_31_<?php echo $key; ?>" value="31" style="display: none">
                                    <span class="tflag"><?php echo $active == 30 ? Yii::t('campus', 'Personal Leave') : (($active == 31) ? Yii::t('campus', 'Sick Leave') :  Yii::t('attends','Leave')) ?></span>
                                    <span class="caret"></span>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a href="javascript:" onclick="types1(this, <?php echo $key;?>, 30,2)"><?php echo Yii::t('campus', 'Personal Leave');?></a></li>
                                    <li><a href="javascript:" onclick="types1(this, <?php echo $key;?>, 31,2)"><?php echo Yii::t('campus', 'Sick Leave');?></a></li>
                                </ul>
                            </div>
                            <div class="btn-group" role="group">
                                <button class="btn btn-default btn_label_40 <?php echo in_array($active, array(40,41,42)) ? (($is_admin) ? 'active disabled' : 'active') : (($is_admin) ? 'disabled' : '')?>"  data-toggle="dropdown" aria-haspopup="true" style="margin-left: -1px;border-radius:0 4px 4px 0" onclick='typeSel()'>
                                    <input type="radio" autocomplete="off" <?php echo $active == 40 ? 'checked' : '';?> name="type[<?php echo $key; ?>]" id="hospital_40_<?php echo $key; ?>" value="40" style="display: none">
                                    <input type="radio" autocomplete="off" <?php echo $active == 41 ? 'checked' : '';?> name="type[<?php echo $key; ?>]" id="hospital_41_<?php echo $key; ?>" value="41" style="display: none">
                                    <input type="radio" autocomplete="off" <?php echo $active == 42 ? 'checked' : '';?> name="type[<?php echo $key; ?>]" id="hospital_42_<?php echo $key; ?>" value="42" style="display: none">
                                    <span class="tflag">
                                        <?php if($active == 40){
                                            echo Yii::t('attends', 'Absent');
                                        }elseif ($active == 41){
                                            echo Yii::t('attends', 'Internal Suspension');
                                        }elseif ($active == 42){
                                            echo Yii::t('attends', 'External Suspension');
                                        }else{
                                            echo Yii::t('attends', 'Absent');
                                        }
                                      ?>
                                    </span>
                                    <span class="caret"></span>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a href="javascript:" onclick="types1(this, <?php echo $key;?>, 40,3)"><?php echo Yii::t('attends', 'Absent');?></a></li>
                                    <li><a href="javascript:" onclick="types1(this, <?php echo $key;?>, 41,3)"><?php echo Yii::t('attends', 'Internal Suspension');?></a></li>
                                    <li><a href="javascript:" onclick="types1(this, <?php echo $key;?>, 42,3)"><?php echo Yii::t('attends', 'External Suspension');?></a></li>
                                </ul>
                            </div>
                        </div>
                    </td>

                    <td class='text-center' width="160" style="line-height: 70px;position:relative;text-align: center; vertical-align: middle;">
                    <div class='relative'>
                        <div id="action_<?php echo $key?>" class="btn-group buttons" data-toggle="buttons">
                            <?php $active = (isset($recordsArr) && isset($recordsArr[$key])) ? $recordsArr[$key]['dress'] : 0; ?>
                            <label class="btn btn-default btn_label_1 <?php echo $active == 1 ? 'active' : ''?>" onclick='typeSel()'>
                                <input type="radio" autocomplete="off" <?php echo $active == 1 ? 'checked' : "";?>  name="dress[<?php echo $key; ?>]" id="hospital_1_<?php echo $key; ?>" value="1">
                                <span><?php echo Yii::t('attends','Violated') ?></span>
                            </label>
                            <label class="btn btn-default btn_label_0 <?php echo $active == 0 ? 'active' : ''?>" style="margin-left: -1px;" onclick='typeSel()'>
                                <input type="radio" autocomplete="off" <?php echo $active == 0 ? 'checked' : "";?> name="dress[<?php echo $key; ?>]" id="hospital_3_<?php echo $key; ?>" value="0">
                                <span><?php echo Yii::t('attends','Not violated') ?></span>
                            </label>
                        </div>
                        <?php if(!empty($recordsArr[$key]['dress_num'])){?>
                        <div class="font13" style="position: absolute;left: 48px;top: 60px;height:16px;line-height: 0">
                            <span class="glyphicon glyphicon-info-sign " style="color: #f0ad4e;"></span>
                            <span class="font15" style="color: #f0ad4e;">
<!--                                 已违反？次，记录？次-->
                                <?php echo sprintf(Yii::t('attends', 'Violated: %d '), $recordsArr[$key]['dress_modulo'])?>

                                <?php if($recordsArr[$key]['dress_trunc']>0){echo sprintf(Yii::t('attends', 'Record: %d'), $recordsArr[$key]['dress_trunc']);}?>
                            </span>
                            <span class="font15" style="color: #f0ad4e;display: none"><?php echo  $recordsArr[$key]['dress_num']?></span>
                            <span class="font15" style="color: #f0ad4e;display: none"><?php echo  $recordsArr[$key]['dress_modulo']?></span>
                            <span class="font15" style="color: #f0ad4e;display: none"><?php echo $recordsArr[$key]['dress_trunc']?></span>
                        </div>
                        <?php }?>
                        </div>
                    </td>
<!---
                    <td class='text-center' width="50" style="line-height: 70px;text-align: center; vertical-align: middle;">
                        <div id="action_electronic_<?php echo $key?>" class="attendance btn-group buttons " data-toggle="buttons" >
                            <?php $active = (isset($recordsArr) && isset($recordsArr[$key])) ? $recordsArr[$key]['electronic'] : 60; ?>
                            <div class="btn-group" role="group">
                                <button class="btn btn-default btn_label_61 <?php echo in_array($active, array(61,62,63)) ? 'active' :  ''?>"
                                        data-toggle="dropdown" aria-haspopup="true" style="margin-left: -1px;"
                                        onclick='typeSel()'>
                                    <input type="radio" autocomplete="off" <?php echo $active == 61 ? 'checked' : '';?> name="electronic[<?php echo $key; ?>]" id="electronic_61_<?php echo $key; ?>" value="61" style="display: none">
                                    <input type="radio" autocomplete="off" <?php echo $active == 62 ? 'checked' : '';?> name="electronic[<?php echo $key; ?>]" id="electronic_62_<?php echo $key; ?>" value="62" style="display: none">
                                    <input type="radio" autocomplete="off" <?php echo $active == 63 ? 'checked' : '';?> name="electronic[<?php echo $key; ?>]" id="electronic_63_<?php echo $key; ?>" value="63" style="display: none">
                                    <span class="tflag">
                                        <?php if($active == 61){
                                            echo Yii::t('attends', 'Phone');
                                        }elseif ($active == 62){
                                            echo Yii::t('attends', 'Other Personal Devices');
                                        }elseif($active == 63){
                                            echo Yii::t('attends', 'Both');
                                        }else{
                                            echo Yii::t('attends', 'Phone');
                                        }?>
                                    </span>
                                    <span class="caret"></span>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a href="javascript:" onclick="types3(this, <?php echo $key;?>, 61)"><?php echo Yii::t('attends', 'Phone');?></a></li>
                                    <li><a href="javascript:" onclick="types3(this, <?php echo $key;?>, 62)"><?php echo Yii::t('attends', 'Other Personal Devices');?></a></li>
                                    <li><a href="javascript:" onclick="types3(this, <?php echo $key;?>, 63)"><?php echo Yii::t('attends', 'Both');?></a></li>
                                </ul>
                            </div>
                            <button class="btn btn-default  btn_label_60 <?php echo in_array($active,array(60,0,null,'')) ? 'active' : '' ?>"  onclick='typeSel()'>
                                <input type="radio" autocomplete="off" <?php echo in_array($active,array(60,0,null,'')) ? 'checked' : '';?> name="electronic[<?php echo $key; ?>]" id="electronic_0_<?php echo $key; ?>" value="60">
                                <span><?php echo Yii::t('attends','Not violated') ?></span>
                            </button>
                        </div>
                    </td>
                    -->
                    <td style="text-align: center; vertical-align: middle;">
                        <textarea class="form-control" name="content[<?php echo $key; ?>]" style="height: 70px;"><?php if(isset($recordsArr) && isset($recordsArr[$key])){echo $recordsArr[$key]['memo'];} ?></textarea>
                    </td>
                </tr>
            <?php } ?>
            </tbody>
            <tfoot>
                <tr>
                    <th></th>
                    <th style="text-align: center; vertical-align: middle;">
                        <div class="btn-group buttons">
                            <label class="label-1"><span id="current_10"><?php echo $numberTypes[10];?></span>/<?php echo count($studentList) ?></label>
                            <label class="label-1"><span id="current_20"><?php echo $numberTypes[20];?></span>/<?php echo count($studentList) ?></label>
                            <label class="label-1"><span id="current_30"><?php echo $numberTypes[30];?></span>/<?php echo count($studentList) ?></label>
                            <label class="label-1"><span id="current_40"><?php echo $numberTypes[40];?></span>/<?php echo count($studentList) ?></label>
                        </div>
                    </th>
                    <th style="text-align: center; vertical-align: middle;">
                        <div class="btn-group buttons">
                            <label class="label-1"><span id="current_1"><?php echo $numberTypes[1];?></span>/<?php echo count($studentList) ?></label>
                            <label class="label-1"><span id="current_0"><?php echo $numberTypes[0];?></span>/<?php echo count($studentList) ?></label>
                        </div>
                    </th>
                    <th style="text-align: center; vertical-align: middle;">
                        <div class="btn-group buttons">
                            <label class="label-1"><span id="current_61"><?php echo $numberTypes[61];?></span>/<?php echo count($studentList) ?></label>
                            <label class="label-1"><span id="current_60"><?php echo $numberTypes[60];?></span>/<?php echo count($studentList) ?></label>
                        </div>
                    </th>
                    <th></th>
                </tr>
            </tfoot>
        </table>
        <!-- <div class="modal-footer">
            <button type="button" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t("global", 'Save');?></button>
        </div> -->
        <?php $this->endWidget(); ?>
    </div>
</div>
<style>
    .flag_span{
        display: inline-block;
        padding-left:4px ;
        padding-right:4px ;
    }
    .flag_span .iconfont {
        font-size:14px
    }
    .hover-cursor {
        cursor: pointer; /* 设置悬停时的光标为箭头 */
    }
    .modal-text .active, .modal-text .btn-default.active{
        color: #ffffff;
        background-color: #428bca;
        border-color: #357ebd;
    }
    .modal-text .label-1 {
        border: 0;
        padding: 6px 12px;
    }
    .btn-group > .btn:hover {
        z-index: 99;
    }
    .bootstrap-select .btn{
        border-radius: 4px 0 0 4px
    }

    .line-btn-group{
        display: table;
        border-collapse: separate;
        margin: 21px 0;
    }
    .newIcon{
        background: red !important;
        position: absolute !important;
        right: -20px !important;
        top: -10px !important;
        height: 15px !important;
        line-height: 12px !important;
        border-radius: 40px !important;
        color: #fff !important;
        font-size: 12px !important;
        padding: 0 2px !important;
    }
</style>
