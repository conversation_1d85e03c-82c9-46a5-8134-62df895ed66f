<?php

/**
 * Journal 周报告
 *
 * <AUTHOR>
 * @date 2013-01-11
 */
class Journal {

    /**
     * 补全周信息
     * @param Array $weekInfo
     * @param Number $weekNumber
     * @return Array
     */
    public function perfectWeekInfo($weekInfo = null, $weekNumber = 0) {

        $emptyWeekNumber = null;
        $tmpWeekInfo = null;
        for ($i = 1; $i <= $weekNumber; $i++) {
            if (empty($weekInfo[$i])) {
                if (empty($tmpWeekInfo)) {
                    $emptyWeekNumber[$i] = $i;
                } else {
                    $weekInfo[$i] = $tmpWeekInfo;
                }
            } else {
                $tmpWeekInfo = $weekInfo[$i];
                if (!empty($emptyWeekNumber)) {
                    foreach ($emptyWeekNumber as $wn) {
                        $weekInfo[$wn] = $tmpWeekInfo;
                    }
                    unset($emptyWeekNumber);
                }
            }
        }
        unset($emptyWeekNumber);
        unset($tmpWeekInfo);
        ksort($weekInfo);
        return $weekInfo;
    }

    /**
     * 去掉重复的数组
     * @param Array $info 二维数组
     * @return Array
     */
    public function removeRepetitive($info = null) {

        $distinctInfo = null;
        if (!empty($info)) {
            $vk = null;
            foreach ($info as $v) {
                ksort($v);
                $vk = implode('_', $v);
                $distinctInfo[$vk] = $v;
            }
        }
        return $distinctInfo;
    }

}
