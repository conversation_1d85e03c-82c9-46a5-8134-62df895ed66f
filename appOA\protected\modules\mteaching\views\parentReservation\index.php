<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Teaching Tasks'), array('//mteaching/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','家长预约'), array('//mteaching/parentReservation/index'))?></li>
        <li><?php echo Yii::t('site','预约配置')?></li>
    </ol>
    <div class="row">
        <div class="col-md-12 col-sm-12">
                <h3>
                    <small><?php echo Yii::t("site","预约配置列表");?></small>
                    <a class="pull-right btn btn-primary J_modal"
                       href="<?php echo $this->createUrl('update'); ?>">
                            <span class="glyphicon glyphicon-plus" aria-hidden="true"></span> <?php echo Yii::t("user", "Add"); ?>
                    </a>
                </h3>
            <?php
            $this->widget('ext.ivyCGridView.BsCGridView', array(
                'id' => 'wida-data',
                'afterAjaxUpdate' => 'js:head.Util.modal',
                'dataProvider' => $plansModel,
                'template' => "{items}{pager}",
                //状态为无效时标红
                //'rowCssClassExpression' => '( $data->active == 0 ? "active" : "" )',
                'colgroups' => array(
                    array(
                        "colwidth" => array(100, 200, 100, 100, 100, 100, 100),
                    )
                ),
                'columns' => array(
                    array(
                        'name' => Yii::t('payment', 'Annual'),
                        'value' => array($this, 'getStarYear'),
                    ),
                    array(
                        'name' => Yii::t('global', 'Title'),
                        'value' => array($this, 'getTitle'),
                    ),
                    array(
                        'name' => Yii::t('global', '状态'),
                        'value' => array($this, 'getStatus'),
                    ),
                    array(
                        'name' => Yii::t('global', '开始时间'),
                        'value' => array($this, 'getStartDate'),
                    ),
                    array(
                        'name' => Yii::t('global', '结束时间'),
                        'value' => array($this, 'getEndDate'),
                    ),
                    array(
                        'name' => Yii::t('asa', 'Students Count'),
                        'value' => array($this, 'getChildCount'),
                    ),
                    array(
                        'name' => Yii::t('global', 'Action'),
                        'value' => array($this, 'getButton'),
                    ),
                ),

            ));
            ?>
        </div>
    </div>
</div>

<script>
    var modal = '<div class="modal fade" id="modal" tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog" role="document"><div class="modal-content"></div></div></div>';
    $('body').append(modal);
    function cbWida() {
        location.reload()
        /*$('#modal').modal('hide');
        $.fn.yiiGridView.update('wida-data');*/
    }
</script>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>