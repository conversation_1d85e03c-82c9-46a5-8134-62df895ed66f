<?php

/**
 * This is the model class for table "ivy_asa_schedule_item_revise".
 *
 * The followings are the available columns in table 'ivy_asa_schedule_item_revise':
 * @property integer $id
 * @property integer $schedule_id
 * @property integer $schedule_item_id
 * @property integer $gid
 * @property integer $cid
 * @property integer $type
 * @property integer $schedule_date
 * @property string $time_start
 * @property string $time_end
 * @property integer $course_index
 * @property integer $updated
 * @property integer $updated_by
 */
class AsaScheduleItemRevise extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_asa_schedule_item_revise';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('schedule_id, schedule_item_id, gid, cid, schedule_date, time_start, time_end, course_index, updated, updated_by', 'required'),
			array('schedule_id, schedule_item_id, gid, cid, type, schedule_date, course_index, updated, updated_by', 'numerical', 'integerOnly'=>true),
			array('time_start, time_end', 'length', 'max'=>5),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, schedule_id, schedule_item_id, gid, cid, type, schedule_date, time_start, time_end, course_index, updated, updated_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'schedule_id' => 'Schedule',
			'schedule_item_id' => 'Schedule Item',
			'gid' => 'gid',
			'cid' => 'Cid',
			'type' => 'Type',
			'schedule_date' => 'Schedule Date',
			'time_start' => 'Time Start',
			'time_end' => 'Time End',
			'course_index' => 'Course Index',
			'updated' => 'Updated',
			'updated_by' => 'Updated By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('schedule_id',$this->schedule_id);
		$criteria->compare('schedule_item_id',$this->schedule_item_id);
		$criteria->compare('gid',$this->gid);
		$criteria->compare('cid',$this->cid);
		$criteria->compare('type',$this->type);
		$criteria->compare('schedule_date',$this->schedule_date);
		$criteria->compare('time_start',$this->time_start,true);
		$criteria->compare('time_end',$this->time_end,true);
		$criteria->compare('course_index',$this->course_index);
		$criteria->compare('updated',$this->updated);
		$criteria->compare('updated_by',$this->updated_by);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AsaScheduleItemRevise the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
