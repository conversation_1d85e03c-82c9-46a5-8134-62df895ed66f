<?php

/**
 * 园长报告
 */
class CampusreportController extends BranchBasedController
{
    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mcampus/campusreport/index');

        // 引入JS文件
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
    }
  
    public function actionIndex()
    {	
		Yii::import('common.models.ivyClass.*');
		Yii::import('common.models.invoice.*');
		Yii::import('common.models.calendar.*');
		Yii::import('common.models.cr.*');
		Yii::import('common.models.Branch.*');
		Yii::import('common.models.visit.*');
		$schoolid = Yii::app()->request->getParam('branchId');
		$yid = Yii::app()->request->getParam('found', '');
		$weeknumber = Yii::app()->request->getParam('weeknumber', '');
		//校历ID
		$criteria = new CDbCriteria;
		$criteria->select = 'schid, yid';
		$criteria->compare('branchId',$schoolid);
		$criteria->compare('is_selected',1);
		$found = CalendarSchool::model()->find($criteria);//校历ID
		$weekid = Yii::app()->request->getParam('weekid','');
		$weeks = CalendarWeek::weeks($found->yid);//全部周数
		krsort($weeks);
		if(!$weekid){
			$weekid = key($weeks);
		}
		if($weekid){
			$criteria = new CDbCriteria;
			$criteria->compare('yid',$found->yid);
			$criteria->compare('schoolid',$schoolid);
			$criteria->compare('weeknumber',$weekid);
			$criteria->compare('status',1);
			$report = CrCampusReport::model()->count($criteria);//是否有报告
			$criteria = new CDbCriteria;
			$criteria->compare('branchId',$schoolid);
			$schoolidtitle = Branch::model()->findAll($criteria);//那个校园
		}else{
			$weekid = CalendarWeek::weeks($found->yid);
			$criteria = new CDbCriteria;
			$criteria->compare('branchId',$schoolid);
			$schoolidtitle = Branch::model()->findAll($criteria);
		}

		$this->render('index',array(
			'found' => $found->yid,
			'schoolidtitle' => $schoolidtitle,
			'report' => $report,
			'weekids' => $weekid,
			'week' => $weeks,
			'activity' =>$models,
		));
    }
	
	//受伤报告
	public function actionUpdata()
	{
		Yii::import('common.models.ivyClass.*');
		Yii::import('common.models.cr.*');
		$id = Yii::app()->request->getParam('id', '');
		$style = Yii::app()->request->getParam('app', '');
		$yid = Yii::app()->request->getParam('found', '');
		$schoolid = Yii::app()->request->getParam('branchId', '');
		$weeknumber = Yii::app()->request->getParam('weeknumber', '');
		$model= CrInjuriesReport::model()->findByPk($id);
		if($style){
				if($model){
					$founds = ivyClass::getClassList($model['schoolid'], $model['yid']);
						foreach($founds as $k=>$v){
							$classname[$v['classid']] = $v['title'];
						}				
				}else{
					$founds = ivyClass::getClassList($schoolid, $yid);
						foreach($founds as $k=>$v){
							$classname[$v['classid']] = $v['title'];//班级ID
						}
					$model = new CrInjuriesReport();
				
				}
				$data = $this->renderPartial('_injured',array('model'=>$model,'classname'=>$classname,'idt'=>$id,'yid'=>$yid,'weeknumber'=>$weeknumber),true);
				$this->addMessage('state', 'success');
				$this->addMessage('callback', 'cbInvoice');
				$this->addMessage('data', $data);
				$this->showMessage();
		}else{
			if($model){
				$model->injuryreport =  $_POST['CrInjuriesReport']['injuryreport'];
				$model->parentback = $_POST['CrInjuriesReport']['parentback'];
			}else{
				$model = new CrInjuriesReport();
				$model->yid = $_POST['yid'];
				$model->schoolid = $schoolid;
				$model->classid = $_POST['CrInjuriesReport']['classid'];
				$model->weeknumber = $_POST['weeknumber'];
				$model->childid = $_POST['CrInjuriesReport']['childid'];
				$model->injuryreport = $_POST['CrInjuriesReport']['injuryreport'];
				$model->parentback = $_POST['CrInjuriesReport']['parentback'];
				$model->time = time();
			}
			if($model->save()){
				$this -> addMessage('state', 'success');
				$this -> addMessage('callback', 'cbinjured');
				$this -> addMessage('message', Yii::t('message','Data saved!'));
			}else{				
				$this->addMessage('state', 'fail');
				$errs = current($model->getErrors());
				$this->addMessage('message', $errs?$errs[0]:Yii::t('message','Saving Failed!'));
			}
			$this->showMessage();
		}
		
	}
	
	
	//显示主要活动和建议
	public function actionActivites()
	{		
		Yii::import('common.models.cr.*');
		Yii::import('common.models.ivyClass.*');
		Yii::import('common.models.invoice.*');
		Yii::import('common.models.calendar.*');
		Yii::import('common.models.visit.*');
		$schoolid = Yii::app()->request->getParam('branchId');
		$yid = Yii::app()->request->getParam('found', '');
		$weeknumber = Yii::app()->request->getParam('weeknumber', '');
		if($_POST['CrMainActivites']){
			$criteria = new CDbCriteria;
			$criteria->select = 'monday_timestamp';
			$criteria->compare('yid',$yid);
			$criteria->compare('weeknumber',$weeknumber);
			$timelast = CalendarWeek::model()->find($criteria);
			//每周结束的时间
			$endtime = $timelast['monday_timestamp'] + 86400*5;
			$criteria = new CDbCriteria;
			$criteria->compare('timestampe','>='.$timelast['monday_timestamp']);
			$criteria->compare('timestampe','<='.$endtime);
			$criteria->compare('schoolid',$schoolid);
			$criteria->compare('payment_type','deposit');
			$criteria->compare('`inout`','in');
			$deposit = InvoiceTransaction::model()->count($criteria);
			//本周来访人数
			$criteria = new CDbCriteria;
			$criteria->compare('visit_timestamp','>='.$timelast['monday_timestamp']);
			$criteria->compare('visit_timestamp','<'.$endtime);
			$criteria->compare('schoolid',$schoolid);
			$visit = IvyVisitsRecord::model()->count($criteria);
			//本周跟踪人数
			$criteria = new CDbCriteria;
			$criteria->compare('update_timestamp','>='.$timelast['monday_timestamp']);
			$criteria->compare('update_timestamp','<'.$endtime);
			$criteria->compare('schoolid',$schoolid);
			$tracelog = VisitsTraceLog::model()->count($criteria);
			
			
			$criteria = new CDbCriteria;
			$criteria->compare('yid',$yid);
			$criteria->compare('schoolid',$schoolid);
			$criteria->compare('weeknumber',$weeknumber);
			$model = CrMainActivites::model()->find($criteria);
			//判断是修改还是增加
			if($model){
				$model->comments =  $_POST['CrMainActivites']['comments'];
				$model->accontent = $_POST['CrMainActivites']['accontent'];
				$model->totaldeposits = $deposit;
				$model->inquiries = $visit;
				$model->track = $tracelog;
			}else{
				$model = new CrMainActivites();
				$model->yid = $yid;
				$model->schoolid = $schoolid;
				$model->comments =  $_POST['CrMainActivites']['comments'];
				$model->accontent = $_POST['CrMainActivites']['accontent'];
				$model->weeknumber = $weeknumber;
				$model->totaldeposits = $deposit;
				$model->inquiries = $visit;
				$model->track = $tracelog;
				$model->time = time();
			}
			
			if($model->save()){
				$this->addMessage('state', 'success');
				$this->addMessage('callback', 'cbActivitys');
				$this->addMessage('message', Yii::t('message','Data saved!'));
			}else{
				$this->addMessage('state', 'fail');
				$errs = current($model->getErrors());
				$this->addMessage('message', $errs?$errs[0]:Yii::t('message','Saving Failed!'));
			}
			$this->showMessage();
		}else{
			$criteria = new CDbCriteria;
			$criteria->compare('yid',$yid);
			$criteria->compare('schoolid',$schoolid);
			$criteria->compare('weeknumber',$weeknumber);
			$models = CrMainActivites::model()->find($criteria);
			if(empty($models)){
				$models = new CrMainActivites();
			}
			$data = $this->renderPartial('activity',array('model'=>$models,'weeknumber'=>$weeknumber,'found'=>$yid),true);
			$this->addMessage('state', 'success');
			$this->addMessage('data', $data);
			$this->showMessage();
		}
	}

	//受伤报告列表
	public function actionInjuries()
	{
		$schoolid = Yii::app()->request->getParam('branchId','');
		$yid = Yii::app()->request->getParam('found','');
		$id = Yii::app()->request->getParam('id','');
		$weeknumber = Yii::app()->request->getParam('weeknumber','');		
		Yii::import('common.models.cr.*');
		//显示孩子受伤报告列表
		$criteria = new CDbCriteria;
		$criteria->compare('schoolid',$schoolid);
		$criteria->compare('weeknumber',$weeknumber);
		$criteria->order = 'time DESC' ;//排序条件 
		//$CrInjuriesReport = CrInjuriesReport::model()->findAll($criteria);
		$dataProvider=new CActiveDataProvider('CrInjuriesReport', array(
			'criteria'=>$criteria,
			'pagination'=>array(
				'pageSize'=>9999,
			),
		));
		$data = $this->render('injured',array('dataProvider'=>$dataProvider,'weeknumber'=>$weeknumber,'yid'=>$yid),true);
		$this->addMessage('state', 'success');
		$this->addMessage('data', $data);
		$this->showMessage();
	}

	//学生
	public function actionGetShops() 
	{
		$schoolid = Yii::app()->request->getParam('branchId', '');
		$classid = Yii::app()->request->getParam('classid', '');
		Yii::import('common.models.ChildProfilebasic.*');
		$criteria = new CDbCriteria;
		$criteria->select = 'childid,name_cn,first_name_en,last_name_en';
		//$criteria->compare('schoolid',$schoolid);
		$criteria->compare('classid',$classid);
		$week= ChildProfilebasic::model()->findAll($criteria);
		$htmlContent = "<option value=''>全部</option>";
		foreach($week as $k=>$v) {
			if($v['name_cn']){
				$htmlContent .= "<option value='{$v["childid"]}'> {$v['name_cn']}</option>";
			}else{
				$htmlContent .= "<option value='{$v["childid"]}'> {$v['first_name_en']}{$v['last_name_en']}</option>";
			}
		}
		echo $htmlContent;
	}
	
	public function actionPreview()
	{
		Yii::import('common.models.ivyClass.*');
		Yii::import('common.models.cr.*');
		Yii::import('common.models.calendar.CalendarWeek');
		Yii::import('common.models.ChildClassLink.*');
		Yii::import('common.models.invoice.InvoiceTransaction');
		Yii::import('common.models.visit.*');
		Yii::import('common.models.ChildProfileBasic.*');
		Yii::import('common.models.child.StatsChildCount');
		
		$schoolid = $this->branchId;//学校
		$user = $this->staff->uid;
		$yid = Yii::app()->request->getParam('found','');//校历
		$style = Yii::app()->request->getParam('style','');//z状态
		$status = Yii::app()->request->getParam('status','');//z状态
		$weeknumber = Yii::app()->request->getParam('weeknumber','');//周数

		//每周开始的时间
		$criteria = new CDbCriteria;
		$criteria->select = 'monday_timestamp';
		$criteria->compare('yid',$yid);
		$criteria->compare('weeknumber',$weeknumber);
		$timelast = CalendarWeek::model()->find($criteria);
		//每周结束的时间
		$endtime = $timelast['monday_timestamp'] + 86400*5;
		//周统计数据
		$class_capacity = ivyClass::getClassList($schoolid, $yid);//班级和班级指定人数
		$class_id = array();
		$people = 0;
		foreach($class_capacity as $k => $v){
			$class_id[$k] = $v->classid;
			$people += $v->capacity;
		}
		//实际付款人数
		$criteria = new CDbCriteria;
		$criteria->compare('period_timestamp','>='.$timelast['monday_timestamp']);
		$criteria->compare('period_timestamp','<'.$endtime);
		$criteria->compare('schoolid',$schoolid);
		$criteria->compare('classid',$class_id);
		$criteria->order = 'period_timestamp' ;
		$class_shou_two = StatsChildCount::model()->findAll($criteria);
		//var_dump($class_shou_two);exit;
		$childArr = array();
		$i = 0;
		foreach($class_shou_two as $v){
			$childArr[$v->classid][$i]['sum'] = $v->num1 + $v->num2 + $v->num3 + $v->num4 + $v->num5;//班级实际人数
			$childArr[$v->classid][$i]['paid'] = $v->num1;
			$i++;
		}
		$array = array();
		foreach($class_shou_two as $value){
			$array[$value->classid][date('w', $value->period_timestamp)] = $value->num1 + $value->num2 + $value->num3 + $value->num4 + $value->num5 ; 
			$array[$value->classid]['right'][date('w', $value->period_timestamp)] = $value->num1 ; 
		}
		$criteria = new CDbCriteria;
		$criteria->compare('updated_timestamp','>='.$timelast['monday_timestamp']);
		$criteria->compare('updated_timestamp','<'.$endtime);
		$criteria->compare('schoolid',$schoolid);
		$criteria->compare('stat',ChildClassLink::STATS_DROPOUT);
		$exitschool = ChildClassLink::model()->count($criteria);
		//交付定金总人数
		$criteria = new CDbCriteria;
		$criteria->compare('timestampe','>='.$timelast['monday_timestamp']);
		$criteria->compare('timestampe','<='.$endtime);
		$criteria->compare('schoolid',$schoolid);
		$criteria->compare('payment_type','deposit');
		$criteria->compare('`inout`','in');
		$deposit = InvoiceTransaction::model()->count($criteria);
		//本周来访人数
		$criteria = new CDbCriteria;
		$criteria->compare('visit_timestamp','>='.$timelast['monday_timestamp']);
		$criteria->compare('visit_timestamp','<'.$endtime);
		$criteria->compare('schoolid',$schoolid);
		$visit = IvyVisitsRecord::model()->count($criteria);
		//本周跟踪人数
		$criteria = new CDbCriteria;
		$criteria->compare('update_timestamp','>='.$timelast['monday_timestamp']);
		$criteria->compare('update_timestamp','<'.$endtime);
		$criteria->compare('schoolid',$schoolid);
		$tracelog = VisitsTraceLog::model()->count($criteria);
		// 受伤列表
		$criteria = new CDbCriteria;
		$criteria->compare('schoolid',$schoolid);
		$criteria->compare('weeknumber',$weeknumber);
		$criteria->order = 'time DESC' ;//排序条件 
		$dataProvider=new CActiveDataProvider('CrInjuriesReport', array(
			'criteria'=>$criteria,
			'pagination'=>array(
				'pageSize'=>9999,
			),
		));
		$weeks = CalendarWeek::weeks($yid);//全部周数
		krsort($weeks);
		//主要活动和建议
		$criteria = new CDbCriteria;
		$criteria->compare('yid',$yid);
		$criteria->compare('schoolid',$schoolid);
		$criteria->compare('weeknumber',$weeknumber);
		$models = CrMainActivites::model()->find($criteria);
		//数据存在时候的状态
		$criteria = new CDbCriteria;
		$criteria->compare('yid',$yid);
		$criteria->compare('schoolid',$schoolid);
		$criteria->compare('weeknumber',$weeknumber);
		$criteria->compare('status',1);
		$crcampuscount = CrCampusReport::model()->count($criteria);
		//数据库里是否有数据		
		$criteria = new CDbCriteria;
		$criteria->compare('yid',$yid);
		$criteria->compare('schoolid',$schoolid);
		$criteria->compare('weeknumber',$weeknumber);
		$crcampusreportnum = CrCampusReport::model()->findAll($criteria);

		if($style == 1){		
			switch($status){
			  case 1://变为生成报告模式
				foreach($crcampusreportnum as $value){
					$value->status = 1;
					$value->save();
					$this->addMessage('state', 'updata');
				}
				break;
			  case 2://删除报告重新写入
				foreach($crcampusreportnum as $value){
					$result = $value->deleteByPK($value['id']);
				}
				$this->addMessage('state', 'delete');
				break;
			  default://写入报告且未生成状态
				if($crcampusreportnum){//没写入报告前 如果有就修改最新的数据
					foreach($crcampusreportnum as $value){
						$value->yid = $yid;
						$value->schoolid = $schoolid;
						$value->weeknumber = $weeknumber;
						$value->monday_timestamp = $timelast['monday_timestamp'];
						$value->classid = $value->classid;
						//$childArr[$value->classid];
						$value->capacity = $value->capacity;
						$arr = array();
						$arr2 = array();
						if($childArr){
							foreach($childArr[$value->classid] as $v3){
									$arr[] = $v3['sum'];
									if($value->capacity != 0){
										$arr2[] = number_format($v3['sum']/$value->capacity * 100,2);
									}else{
										$arr2[] = 0;
									}		
							}
						}else{
							$arr = array(0,0,0,0,0);
							$arr2 = array(0,0,0,0,0);
						}
						$value->mon_person = $arr[0];
						$value->tue_person = $arr[1];
						$value->wed_person = $arr[2];
						$value->thu_person = $arr[3];
						$value->fri_person = $arr[4];
						$value->mon_fte_ratio = $arr2[0];
						$value->tue_fte_ratio = $arr2[1];
						$value->wed_fte_ratio = $arr2[2];
						$value->thu_fte_ratio = $arr2[3];
						$value->fri_fte_ratio = $arr2[4];
						$value->uid = 1;
						$value->status = 0;
						$value->time = time();
						$value->save();
						$this->addMessage('state', 'success');
					}
				}else{ //如果没有就增加
					foreach($class_capacity as $value){
						$model = new CrCampusReport();
						$model->yid = $yid;
						$model->schoolid = $schoolid;
						$model->weeknumber = $weeknumber;
						$model->monday_timestamp = $timelast['monday_timestamp'];
						$model->classid = $value->classid;
						$childArr[$value->classid];
						$model->capacity = $value->capacity;
						$arr = array();
						$arr2 = array();
						if($childArr){
							foreach($childArr[$value->classid] as $v3){
									$arr[] = $v3['sum'];
									if($value->capacity != 0){
										$arr2[] = number_format($v3['sum']/$value->capacity * 100,2);
									}else{
										$arr2[] = 0;
									}		
							}
						}else{
							$arr = array(0,0,0,0,0);
							$arr2 = array(0,0,0,0,0);
						}
						$model->mon_person = $arr[0];
						$model->tue_person = $arr[1];
						$model->wed_person = $arr[2];
						$model->thu_person = $arr[3];
						$model->fri_person = $arr[4];
						$model->mon_fte_ratio = $arr2[0];
						$model->tue_fte_ratio = $arr2[1];
						$model->wed_fte_ratio = $arr2[2];
						$model->thu_fte_ratio = $arr2[3];
						$model->fri_fte_ratio = $arr2[4];
						$model->uid = $user;
						$model->status = 0;
						$model->time = time();
						$model->save();
						$this->addMessage('state', 'success');
					}
				}
			}
		}		
		$datas = $this->renderPartial('preview',array(
			'weeknumber'=>$weeknumber,
			'tracelog'=>$tracelog,
			'visit'=>$visit,
			'deposit'=>$deposit,
			'exitschool'=>$exitschool,
			'weeks'=>$weeks,
			'class_capacity'=>$class_capacity,
			'dataProvider'=>$dataProvider,
			'class_shou_two'=>$class_shou_two,
			'activit'=>$models,
			'yid'=>$yid,
			'childArr'=>$childArr,
			'people' => $people,
			'crcampuscount'=>$crcampuscount,
			'timelast'=>$timelast,
			'array' =>$array,
		),true);
		
		$this->addMessage('data', $datas);
		$this->showMessage();
	}
	
	// 删除受伤报告
	public function actionDelete()
	{	
		$id = Yii::app()->request->getParam('id', '');
		Yii::import('common.models.cr.CrInjuriesReport');
		
		$model = new CrInjuriesReport();
		$result = $model->deleteByPK($id);
		if($result){
			$this -> addMessage('state', 'success');
			$this -> addMessage('callback', 'cbinjured');
			$this -> addMessage('message', Yii::t('message','Data saved!'));
		}else{
			$this->addMessage('state', 'fail');
			$this->addMessage('message', '');
			}
		$this->showMessage();
	}
	
	public function showBtn($data)
	{
		
		echo '<button class="btn btn-info btn-xs" type="button" onclick="updates('.$data->id.')" title="更新"><span class="glyphicon glyphicon-pencil"></span></button>  ';
		

		echo '<a href="'.$this->createUrl('delete', array('id'=>$data->id)).'" class="btn btn-danger btn-xs J_ajax_del" ><span class="glyphicon glyphicon-trash"></span></a>';
	}
}
