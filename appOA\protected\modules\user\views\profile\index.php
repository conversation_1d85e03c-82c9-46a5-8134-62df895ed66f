<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','User Center'), array('/backend/apps'));?></li>
        <li class="active"><?php echo Yii::t('site','My Profile');?></li>
    </ol>
    <div class="row">
        <div class="col-md-2">
            <?php
                $mainMenu = array(
                    array('label'=>Yii::t('user','Basic Information'), 'url'=>array("//user/profile/index","category"=>"basic")),
                    array('label'=>Yii::t('user','Detailed Information'), 'url'=>array("//user/profile/index","category"=>"expan")),
                    array('label'=>Yii::t('user','Upload Photo'), 'url'=>array("//user/profile/index","category"=>"upload")),
                    array('label'=>Yii::t('user','Change Password'), 'url'=>array("//user/profile/index","category"=>"passwd")),
                    array('label'=>Yii::t('user','Wechat Binding'), 'url'=>array("//user/profile/index","category"=>"wechat")),
                );
                $this->widget('zii.widgets.CMenu',array(
                    'id' => 'user-profile-item',
                    'items'=> $mainMenu,
                    'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked text-right background-gray'),
                    'activeCssClass'=>'active',
                    'itemCssClass'=>''
                ));
            ?>
        </div>
        <div class="col-md-10" id="main-edit-zone">
            <?php
                $this->renderPartial('profile/'.$category);
            ?>
        </div>
    </div>
</div>

<script>
    $(function(){
        $('#main-edit-zone').find('.edit-title').html($('#user-profile-item li.active a').html());
    })
</script>