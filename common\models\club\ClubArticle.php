<?php

/**
 * This is the model class for table "ivy_club_article".
 *
 * The followings are the available columns in table 'ivy_club_article':
 * @property integer $id
 * @property string $branch_id
 * @property integer $author_uid
 * @property string $author_text
 * @property integer $op_uid
 * @property string $category
 * @property string $sub_category
 * @property integer $language
 * @property integer $updated
 * @property string $title
 * @property integer $created
 * @property string $cover
 * @property integer $album
 * @property integer $status
 * @property integer $privacy
 * @property integer $comments
 * @property integer $hits
 */
class ClubArticle extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return ClubArticle the static model class
	 */
    //语言
    const CLUB_LANGUAGE_IBS=0; //双语
    const CLUB_LANGUAGE_CHA=1; //中文
    const CLUB_LANGUAGE_ENG=2; //英文
    //分类
    const CLUB_CATEGORG_TRAVEL = 'travel';
    const CLUB_CATEGORG_CHEF = 'chef';
    public $author_email = null;
    public $publish = 0; //文章发布开关
    public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_club_article';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('branch_id, author_email,author_text,op_uid, category, language, updated, title, created, album, status, privacy', 'required'),
			array('author_uid, op_uid, language, updated, created, album, status, privacy, comments, hits, publish_date, cover,publish', 'numerical', 'integerOnly'=>true),
            array('author_email','email'),
            array('author_email','authorEmail'),
            array('author_uid','required'),
			array('branch_id', 'length', 'max'=>12),
			array('author_text, page_num', 'length', 'max'=>255),
			array('category, sub_category', 'length', 'max'=>64),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, branch_id, author_uid, author_text, op_uid, category, sub_category, language, updated, title, created, cover, album, status, privacy, comments, hits, publish_date,page_title,digest', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'content'=>array(self::HAS_ONE, 'ClubArticleContent', 'id'),
            'user'=>array(self::BELONGS_TO, 'User', 'author_uid'),
            'clubUname'=>array(self::HAS_ONE, 'ClubUsers', array('uid'=>'author_uid')),
            'branchinfo'=>array(self::BELONGS_TO, 'BranchInfo', 'branch_id'),
            'branch'=>array(self::BELONGS_TO, 'Branch', 'branch_id'),
            'coverImg'=>array(self::HAS_ONE, 'ClubUploads', array('id'=>'cover')),
		);
	}
    
    public function authorEmail(){
        if (!$this->hasErrors()) {
            $count = User::model()->count('email=:email and isstaff=:isstaff',array(':email'=>$this->author_email,':isstaff'=>0));
            if (!$count){
                $this->addError('author_email', Yii::t('t','无效邮箱地址！'));
            }
        }
    }

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'branch_id' => 'Branch',
			'author_uid' => 'Author Uid',
			'author_text' => 'Author Text',
			'op_uid' => 'Op Uid',
			'category' => 'Category',
			'sub_category' => 'Sub Category',
			'language' => 'Language',
			'updated' => 'Updated',
			'title' => 'Title',
			'created' => 'Created',
			'cover' => 'Cover',
			'album' => 'Album',
			'status' => 'Status',
			'privacy' => 'Privacy',
			'comments' => 'Comments',
			'hits' => 'Hits',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('t.id',$this->id);
		$criteria->compare('t.branch_id',$this->branch_id,true);
		$criteria->compare('t.author_uid',$this->author_uid);
		$criteria->compare('t.author_text',$this->author_text,true);
		$criteria->compare('t.op_uid',$this->op_uid);
		$criteria->compare('t.category',$this->category,true);
		$criteria->compare('t.sub_category',$this->sub_category,true);
		$criteria->compare('t.language',$this->language);
		$criteria->compare('t.updated',$this->updated);
		$criteria->compare('t.title',$this->title,true);
		$criteria->compare('t.album',$this->album);
		$criteria->compare('t.status',$this->status);
		$criteria->compare('t.privacy',$this->privacy);
        $criteria->with = array('user','branch');
		return new CActiveDataProvider(get_class($this), array(
			'criteria'=>$criteria,
            'pagination' => array(
                'pageSize' => 40,
            ),
		));
	}
    
    /*
     * 取大分类列表
     */
    static public function getClubCategory($id=''){
        $rel = array(
            self::CLUB_CATEGORG_TRAVEL=>  Yii::t('club', '宝贝去哪里'),
            self::CLUB_CATEGORG_CHEF=>  Yii::t('club', '厨神在这里'),
        );
        return ($id == '') ? $rel :$rel[$id] ;
    }
    
    /*
     * 文章语言
     */
    static public function getClubLanguage($id=''){
        $rel = array(
            self::CLUB_LANGUAGE_IBS=>  Yii::t('club', '双语'),
            self::CLUB_LANGUAGE_CHA=>  Yii::t('club', '中文'),
            self::CLUB_LANGUAGE_ENG=>  Yii::t('club', '英文'),
        );
        return ($id=='') ? $rel : $rel[$id];
    }
    
    /*
     * 专辑
     */
    static public function getClubAlbum(){
        $result = array();
        $items = DiglossiaCategory::model()->with('diglossia')->findAll('t.category_sign=:category_sign',array(':category_sign'=>'clubAlbum'));
        if (!empty($items)){
            foreach ($items[0]->diglossia as $val){
                $result[$val->diglossia_id] = (Yii::app()->language == 'zh_cn') ? $val->cntitle : $val->entitle;
            }
        }
        return $result;
    }
    /*
     * 文章状态
     */
    static public function getClubStatus($id=''){
        $ret = array(Yii::t('club','隐藏'),Yii::t('club','显示'));
        return ($id=='') ? $ret : $ret[$id];
    }

    public function getTitle($type='title'){
        $langTag = '{{lang-tag}}';
        if(strpos($this->$type, $langTag)!==false){
            $tmp = explode($langTag, $this->$type);
            return Yii::app()->language == 'zh_cn' ? $tmp[0] : $tmp[1];
        }
        else{
            return $this->$type;
        }
    }
    
    /*
     * 文章隐私
     */
    static public function getClubPrivacy($id=''){
        $ret = array(Yii::t('club','隐藏'),Yii::t('club','显示'));
        return ($id=='') ? $ret : $ret[$id];
    }

    public function genArticleCache($id=0)
    {
        $articleKey = 'article-%d';
        $cacheKey = 'article-%d-%s-%s';
        $model = $this->model()->with('content')->findByPk($id);
        $articleKeyArray = array();
        $articlePageArray = array();

        if($model != null){
            $langTag = '{{lang-tag}}';
            $pageTag = '{{page-tag}}';

            $filekey = sprintf($articleKey, $model->id);
            $cachefiles = Yii::app()->cache->get($filekey);
            foreach($cachefiles as $fkey){
                Yii::app()->cache->delete($fkey);
            }
            Yii::app()->cache->delete($filekey);

            switch($model->language){
                case self::CLUB_LANGUAGE_IBS:
                    $langs = array('zh_cn', 'en_us');
                    break;
                case self::CLUB_LANGUAGE_CHA:
                    $langs = array('zh_cn');
                    break;
                case self::CLUB_LANGUAGE_ENG:
                    $langs = array('en_us');
                    break;
            }

            $ctmp = array();
            foreach($langs as $lkey=>$lang){

                $content = '';
                $branch = '';
                if($model->language == 0){
                    if(strpos($model->content->content, $langTag) !== false){
                        $ctmp = explode($langTag, $model->content->content);
                        $content = $ctmp[$lkey];
                    }
                    else{
                        $content = $model->content->content;
                    }
                }
                else{
                    $content = $model->content->content;
                }
                $pagesContent = explode($pageTag, $content);
                $pagecount = count($pagesContent);
                foreach($pagesContent as $pageKey=>$pageContent){
                    $_pageKey=$pageKey+1;
                    $cacheId = sprintf($cacheKey, $model->id, $lang, $_pageKey);
                    $articleKeyArray[]=$cacheId;
                    Yii::app()->cache->set($cacheId, $pageContent);
                }
                $articlePageArray[$lang] = $pagecount;
            }
            Yii::app()->cache->set($filekey, $articleKeyArray);
            $model->page_num = serialize($articlePageArray);
            $model->save(false);
        }
    }
}