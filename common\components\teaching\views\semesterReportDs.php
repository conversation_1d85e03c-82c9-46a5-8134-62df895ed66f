<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="language" content="en" />
    <title><?php echo CHtml::encode($this->pageTitle); ?></title>
</head>

<body>

    <?php if($withToolBar):?>
        <div class="navbar navbar-fixed-top navbar-inverse" role="navigation">
            <?php
            echo CHtml::form( $downloadActionUrl );
            echo CHtml::hiddenField('schoolid',     $params['schoolid']);
            echo CHtml::hiddenField('childid',      $params['childid']);
            echo CHtml::hiddenField('classid',      $params['classid']);
            echo CHtml::hiddenField('id',           $params['id']);
            echo CHtml::submitButton('Download PDF / 下载 PDF', array('style'=>'font-family: Microsoft Yahei'));
            echo CHtml::endForm();
            ?>
        </div>
    <?php endif;?>

    <div class="report-wrapper web-view">
    <!--page one-->
    <div id="page-1" class="report-page page-break-after">
        <!-- page content-->
        <div class="page-content">

            <?php
            if($data['child']->photo){
                echo CHtml::image(CommonUtils::childPhotoUrl($data['child']->photo),
                    null,
                    array('class' => 'profile-photo')
                );
            }
            ?>

            <div class="profile-box">
                <dl>
                    <dt><span class="cn">姓名</span> <span class="en">Name</span></dt>
                    <dd><?php
                        $childName = $data['child']->getChildName();
                        $hasCNChars = CommonUtils::hasCNChars( $childName );

                        if( $hasCNChars > 0){
                            echo '<span class="cn">';
                        }else{
                            echo '<span class="en">';
                        }

                        echo $childName . '</span>';

                        ?></dd>

                    <dt><span class="cn">出生日期</span> <span class="en">DOB</span></dt>
                    <dd><?php echo $data['child']->birthday_search?></dd>

                    <dt><span class="cn">校园</span> <span class="en">Campus</span></dt>
                    <dd><?php
                        $title = $data['campusTitle'];
                        $title = str_replace('(', '(<span class="cn">', $title);
                        $title = str_replace(')', '</span>)', $title);
                        echo $title;
                        ?></dd>

                    <dt><span class="cn">学期</span> <span class="en">Semester</span></dt>
                    <dd>
                        <?php echo IvyClass::formatSchoolYear($data['report']['startyear'])?>
                        <?php echo ($data['report']['semester'] == 1) ?
                            '<span class="cn">第一学期</span> <span class="en">First semester</span>' :
                            '<span class="cn">第二学期</span> <span class="en">Second semester</span>'
                        ?>
                    </dd>

                    <dt><span class="cn">班级</span> <span class="en">Class</span></dt>
                    <dd><?php echo $data['classTitle'];?></dd>

                    <dt><span class="cn">班级教师</span> <br/><span class="en">Class Teachers</span></dt>
                    <dd>
                        <?php
                        $listClass = (count($data['teachers']) < 9 ) ? 'a' :(
                        (count($data['teachers']) < 16 ) ? 'b' : 'c'
                        );
                        ?>
                        <div class="teacher-list teacher-list-<?php echo $listClass;?>">
                            <?php
                            $idx = 0;
                            if ($data['teachers']):?>
                                <?php foreach ($data['teachers'] as $tName):?>
                                    <span class="item"><?php echo $tName['name'];?><?php if ($data['tearcherFlag'][$tName['teacherid']]){ echo ' - '.$data['tearcherFlag'][$tName['teacherid']];};?></span>
                                <?php endforeach;?>
                            <?php endif;?>
                        </div>
                        <div style="clear: both"></div>
                    </dd>
                </dl>
            </div>

            <div class="cover-bottom">

            </div>


        </div><!-- page content -->

        <div class="top-title"><div class="cn">评估报告</div><div class="en">Progress Report</div></div>
        <div class="top-school"><div class="cn">启明星学校</div><div class="en">Daystar Academy</div></div>
        <div class="logo-bottom-center"></div>

    </div><!--page one-->

    <!--page 2-->
    <div id="page-2" class="report-page page-break-after">

        <!-- page content -->
        <div class="page-content">
            <h3><span class="cn">概述</span> <span class="en">General Comments</span></h3>
            <table class="table">
                <thead>
                    <tr>
                        <th colspan="3">孩子作为一名学习者</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="3">
                            启明星学校笃行中西合璧之教育模式，博采东西方文化之长，秉承服务全社会之宗旨，致力于培养中英文学业卓越，具有创造性思维及高贵品格的世界公民。启明星学校的学生努力发展成为:
                        </td>
                    </tr>
                    <tr>
                        <td style="border-top:0;">双语卓越的人</td>
                        <td style="border-top:0;">心胸宽广的人</td>
                        <td style="border-top:0;">有原则的人</td>
                    </tr>
                    <tr>
                        <td style="border-top:0;">具有全球视野的人</td>
                        <td style="border-top:0;">有爱心的人</td>
                        <td style="border-top:0;">善于反思的人</td>
                    </tr>
                    <tr>
                        <td style="border-top:0;">全面发展的人</td>
                        <td style="border-top:0;">知识渊博的人</td>
                        <td style="border-top:0;">交流者</td>
                    </tr>
                    <tr>
                        <td style="border-top:0;">思考者</td>
                        <td style="border-top:0;">探究者</td>
                        <td style="border-top:0;">勇于尝试的人</td>
                    </tr>
                </tbody>
            </table>
            <table class="table">
                <thead>
                    <tr>
                        <th>年级评定标准</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    foreach ($optionsList as $group):?>
                    <tr>
                        <td align="center" style="border-top:0;">
                            <?php echo $group['title'];?>
                            <table class="table" style="margin-bottom:2px;">
                                <tr>
                                <?php foreach ($group['options'] as $option):?>
                                    <td style="border-top:0;text-align: center;"><strong><?php echo $option['label'];?></strong><br/><?php echo CommonUtils::autoLang($option['title_cn'],$option['title_en']);?></td>
                                <?php endforeach;?>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <?php endforeach;?>
                </tbody>
            </table>
        </div>
<!--        <div class="logo-bottom-center"></div>-->
        <div class="top-title"><div class="cn">评估报告</div><div class="en">Progress Report</div></div>

    </div>

    <!--page 3-->
    <div id="page-3" class="report-page page-break-after">

        <!-- page content -->
        <div class="page-content">
            <h3><span class="cn">学生成绩</span> <span class="en">Student Achievement</span></h3>
            <?php
                ksort($optionsList);
                foreach ($optionsList as $optionKey => $options):
            ?>
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th width="200"><?php echo $options['title'];?></th>
                        <?php
                        foreach ($optionsList[$optionKey]['options'] as $option):?>
                        <th><?php echo $option['label'];?></th>
                        <?php endforeach;?>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($data['root'] as $root):?>
                    <?php if ($optionKey == $root['option_groupid']):?>
                    <tr>
                        <td><?php echo $root['title'];?></td>
                        <?php
                        foreach ($optionsList[$optionKey]['options'] as $option):?>
                        <td align="center">
                            <?php
                                if (isset($data['report'][$root['id']]['option'][$root['id']]) && $data['report'][$root['id']]['option'][$root['id']] == $option['id'] ):
                                    echo CHtml::image('http://m1.files.ivykids.cn/images/sreport/booked.png','',array('width'=>18,'height'=>18));
                                else:
                                    echo '';
                                endif;
                            ?>
                        </td>
                        <?php endforeach;?>
                    </tr>
                    <?php endif;?>
                    <?php endforeach;?>
                </tbody>
            </table>
            <?php endforeach;?>
        </div>
        <div class="top-title"><div class="cn">评估报告</div><div class="en">Progress Report</div></div>

    </div>

    <!--page 4-->
    <?php foreach ($data['root'] as $root):?>
        <?php if (count($data['subs'][$root['id']])>2):?>
            <?php
            $i=0;
            foreach ($data['subs'][$root['id']] as $subs):
            $i++;
            ?>
            <div id="page-4-<?php echo $root['id']?>" class="report-page page-break-after">
            <!-- page content -->
                <div class="page-content">
                    <h3><span class="cn"><?php echo $root['title']?></span></h3>
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="200"><?php echo $subs['title'];?></th>
                                <?php foreach ($optionsList[$subs['option_groupid']]['options'] as $option):?>
                                <th><?php echo $option['label'];?></th>
                                <?php endforeach;?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            foreach ($data['items'][$subs['id']] as $keyItem=>$items):?>
                            <tr>
                                <td><?php echo $items;?></td>
                                <?php
                                foreach ($optionsList[$subs['option_groupid']]['options'] as $option):?>
                                <td align="center">
                                    <?php
                                        if (isset($data['report'][$root['id']]['option'][$subs['id']][$keyItem]) && $data['report'][$root['id']]['option'][$subs['id']][$keyItem] == $option['id'] ):
                                            echo CHtml::image('http://m1.files.ivykids.cn/images/sreport/booked.png','',array('width'=>18,'height'=>18));
                                        else:
                                            echo '';
                                        endif;
                                    ?>
                                </td>
                                <?php endforeach;?>
                            </tr>
                            <?php endforeach;?>
                        </tbody>
                    </table>
                    <?php if (isset($data['report'][$root['id']]) && $data['report'][$root['id']]['memo'] && count($data['subs'][$root['id']])==$i):?>
                    <table class="table">
                        <thead>
                            <tr>
                                <th align="left"><?php echo Yii::t('teaching', 'Teacher Comments');?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><?php echo $data['report'][$root['id']]['memo'];?></td>
                            </tr>
                        </tbody>
                    </table>
                    <?php endif;?>
                </div>
                <div class="top-title"><div class="cn">评估报告</div><div class="en">Progress Report</div></div>
            </div>
            <?php
            endforeach;?>
        <?php else:?>
            <div id="page-4-<?php echo $root['id']?>" class="report-page page-break-after">
            <!-- page content -->
                <div class="page-content">
                    <h3><span class="cn"><?php echo $root['title']?></span></h3>
                    <?php foreach ($data['subs'][$root['id']] as $subs):?>
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="200"><?php echo $subs['title'];?></th>
                                <?php foreach ($optionsList[$subs['option_groupid']]['options'] as $option):?>
                                <th><?php echo $option['label'];?></th>
                                <?php endforeach;?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            foreach ($data['items'][$subs['id']] as $keyItem=>$items):?>
                            <tr>
                                <td><?php echo $items;?></td>
                                <?php
                                foreach ($optionsList[$subs['option_groupid']]['options'] as $option):?>
                                <td align="center">
                                    <?php
                                        if (isset($data['report'][$root['id']]['option'][$subs['id']][$keyItem]) && $data['report'][$root['id']]['option'][$subs['id']][$keyItem] == $option['id'] ):
                                            echo CHtml::image('http://m1.files.ivykids.cn/images/sreport/booked.png','',array('width'=>18,'height'=>18));
                                        else:
                                            echo '';
                                        endif;
                                    ?>
                                </td>
                                <?php endforeach;?>
                            </tr>
                            <?php endforeach;?>
                        </tbody>
                    </table>
                    <?php endforeach;?>
                    <?php if (isset($data['report'][$root['id']]) && $data['report'][$root['id']]['memo']):?>
                    <table class="table">
                        <thead>
                            <tr>
                                <th align="left"><?php echo Yii::t('teaching', 'Teacher Comments');?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><?php echo $data['report'][$root['id']]['memo'];?></td>
                            </tr>
                        </tbody>
                    </table>
                    <?php endif;?>
                </div>
                <div class="top-title"><div class="cn">评估报告</div><div class="en">Progress Report</div></div>
            </div>
        <?php endif;?>
    <?php endforeach;?>
        <!--page 4-->
        <div id="page-5" class="report-page">
            <!-- page content -->
            <div class="page-content">
                <h3><span class="cn"> 评估报告注释</span> <span class="en"> </span></h3>
                <div class="cn">
                    <p>启明星学校首要目标是让每位学生通过学习并取得学业上的成功，发展人际交往能力。评估报告的目的是根据年级评定标准，反映学生进步的信息。在语言艺术、社会学、科学等学科上，使用专业标准来评估每位学生。</p>
                </div>
                <div class="cn">
                    <h3>学业</h3>
                    <h5>处于年级水平起步阶段:</h5>
                    <p>学生取得一些进步，但还低于年级期望水平。学生还没有完全掌握应有知识和技能。学生的表现低于应有的年级水平，可能需要更多的支持和努力。</p>
                </div>
                <div class="cn">
                    <h5>处于年级水平发展阶段:</h5>
                    <p>表明学生正在取得进步，学期末有足够的能力达到年级水平。处于该阶段学生已经达到年级水平的所有标准。学生已经取得了预期的进步，但还未达到学年末水平。和您的孩子沟通，说明D表示他/她正在朝学年末达标标准进步，这很重要。</p>
                </div>
                <div class="cn">
                    <h5>处于年级水平达标阶段:</h5>
                    <p>表明学生已经达到本年级学年末标准。目标是让所有学生在年末达到该标准。</p>
                </div>
                <div class="cn">
                    <h5>超出年级水平阶段:</h5>
                    <p>表明学生已经超过了该年级应有标准。这个阶段的学生能够将所学知识运用到新的领域，并且独立使用知识技能，完整更高年级的工作。</p>
                </div>
            </div><!-- page content -->
            <div class="top-title"><div class="cn">评估报告</div><div class="en">Progress Report</div></div>

        </div>
        <!--page 4-->
        <div id="page-6" class="report-page">
            <!-- page content -->
            <div class="page-content">
                <h3><span class="cn"> 评估报告注释</span> <span class="en"> </span></h3>
                <div class="cn">
                    <p>启明星学校首要目标是让每位学生通过学习并取得学业上的成功，发展人际交往能力。评估报告的目的是根据年级评定标准，反映学生进步的信息。在语言艺术、社会学、科学等学科上，使用专业标准来评估每位学生。</p>
                </div>
                <div class="cn">
                    <h3>非学业:</h3>
                    <p>学生表现是基于老师观察发现的行为频率来测评。需要注意的是，非学业分级评定参照的是行为数量，而不是质量。老师只汇报观察到的行为出现频率，而非对此行为作出评价。</p>
                </div>
                <div class="cn">
                    <h5>有待提高</h5>
                    <p>表明学生很少或有时表现良好</p>
                </div>
                <div class="cn">
                    <h5>经常</h5>
                    <p>表明学生大部分时间表示良好</p>
                </div>
                <div class="cn">
                    <h5>一贯</h5>
                    <p>表明学生一如既往表现良好</p>
                </div>
            </div><!-- page content -->

            <div class="logo-contact-left">
                <span class="phone">010-6433-7366</span>
                <span class="url">www.daystarchina.cn</span>
            </div>
            <div class="logo-bottom-right"></div>
            <div class="top-title"><div class="cn">评估报告</div><div class="en">Progress Report</div></div>

        </div>
    </div>


    <style>
        .en{font-family: Tahoma,'Trebuchet','Utopia';}
        .cn{font-family:'Microsoft Yahei'}
        .report-page{
            font-size: 9px;
            padding:0;
            height: 877px;
            position: relative;
            background: url('http://m1.files.ivykids.cn/images/sreport/header.gif') no-repeat;
            background-size: 100%;
            background-color: #ffffff;
        }
        .report-wrapper{width:620px; margin: 0px auto; padding: 0; font-size:10px;}
        .logo-bottom-center{
            width: 160px;
            height: 158px;
            background: url('https://m2.files.ivykids.cn/cloud01-file-8035133FueFp-X_Mzk0IJPxInAZ_vikTFWW.png') no-repeat;
            background-size: 100%;
            position: absolute;
            left: 230px;
            bottom: 0;
        }
        .logo-bottom-right{
            width: 160px;
            height: 158px;
            background: url('https://m2.files.ivykids.cn/cloud01-file-8035133FueFp-X_Mzk0IJPxInAZ_vikTFWW.png') no-repeat;
            background-size: 100%;
            position: absolute;
            right: 20px;
            bottom: 0;
        }
        .top-title{
            width: 160px;
            height: 60px;
            position: absolute;
            top: 20px;
            right: 50px;
            color: #ffffff;
            font-size: 20px;
            text-align: center;
        }
        .top-school{
            width: 220px;
            height: 60px;
            position: absolute;
            top: 80px;
            left: 80px;
            font-size: 22px;
            text-align: center;
        }
        .logo-contact-left{
            width: 160px;
            height: 58px;
            position: absolute;
            left: 20px;
            bottom: 0;
        }
        .cd-signature{
            position: absolute;
            right: 60px;
            bottom: 120px;
            text-align: right;
            font-size: 12px;
        }
        .logo-contact-left .phone{font-size:18px;display:block;}
        .logo-contact-left .url{font-size:12px;}
        .page-content{
            margin: 0 60px;position: absolute;top: 90px; width: 500px;
            position: relative;
        }
        .page-content .profile-photo{
            position: absolute;
            right: 0;
            top: 56px;
            width: 150px;
        }

        .page-content .profile-box{
            padding-top: 50px;
        }

        .page-content .profile-box dl{line-height: 25px;}
        .page-content .profile-box dt{float: left; width: 90px; clear: left;color: #999;text-align: right}
        .page-content .profile-box dt:after{content: ': '}
        .page-content .profile-box dd{margin-left: 100px;border-bottom: 1px dotted #efefef; width: 220px;}
        .page-content .profile-box dd:before{display: table}
        .page-content .profile-box dd:after{clear: both;}
        .teacher-list span.item{display: inline-block; text-align: left;color: #666666;}
        .teacher-list-a span.item{width: 310px;display: block; }
        .teacher-list-b span.item{width: 110px;float: left; height: 25px;}

        .page-content .cover-bottom{
            position: absolute;
            width: 100%;
            top: 395px;
        }

        span.ld{
            color: #000;
            font-size: 110%;
            padding: 1px 3px;
            margin-right: 4px;
            background: #efefef;
        }

        span.ld:after{
        }

        hr.sep{
            margin: 5em 0 3em;
            border: 0;
            height: 1px;
            background: #333;
            background-image: -webkit-linear-gradient(left, #efefef, #ccc, #efefef);
            background-image:    -moz-linear-gradient(left, #efefef, #999, #ccc);
            background-image:     -ms-linear-gradient(left, #efefef, #999, #ccc);
            background-image:      -o-linear-gradient(left, #efefef, #999, #ccc);
        }

        ul.mission li{line-height: 1.5em;}
        /*.web-view .report-page{margin: 1em 0;}*/

        .tac{text-align: center}
        .page-break-after{page-break-after: always;}
        img.middle-size{
            max-width: 360px;
            max-height: 272px;
            margin: 10px auto;
            display: block;
            border: 4px solid #efefef;
        }
        body {
            font-family: Utopia, "Trebuchet MS", Arial, "Microsoft Yahei", "微软雅黑", STXihei, "华文细黑", sans-serif;
            font-size: 12px;
            line-height: 1.42857143;
            color: #333333;
            margin: 0;
            padding: 0;
            background-color: #efefef;
        <?php if($withToolBar):?>
            padding-top: 70px;
            padding-bottom: 20px;
        <?php endif;?>
        }

        @media print {
            /**.web-view .report-page {background: none;}**/
            /*.web-view .report-page{margin: 0;}*/
        }
        .navbar-inverse {
            background-color: #333333;
            border-color: #efefef;
        }
        .navbar-inverse form {
            line-height: 50px;
            height: 50px;
            text-align: center;
        }
        .navbar-inverse form input {
            margin-top: 13px;
        }
        .navbar-fixed-top {
            top: 0;
            position: fixed;
            right: 0;
            left: 0;
            z-index: 1030;
            -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
        }
        .navbar-inverse a{
            color: #f2f2f2;
        }

        .table {
            width: 100%;
            max-width: 100%;
            margin-bottom: 17px;
        }
        .table > thead > tr > th,
        .table > tbody > tr > th,
        .table > tfoot > tr > th,
        .table > thead > tr > td,
        .table > tbody > tr > td,
        .table > tfoot > tr > td {
          padding: 8px;
          line-height: 1.42857143;
          vertical-align: top;
          border-top: 1px solid #dddddd;
        }
        .table > thead > tr:first-child > th,
        .table > tbody > tr:first-child > th,
        .table > tfoot > tr:first-child > th,
        .table > thead > tr:first-child > td,
        .table > tbody > tr:first-child > td,
        .table > tfoot > tr:first-child > td{
            border-top: 0;
        }
        .table > thead > tr > th {
          vertical-align: bottom;
          border-bottom: 2px solid #dddddd;
        }
        .table > caption + thead > tr:first-child > th,
        .table > colgroup + thead > tr:first-child > th,
        .table > thead:first-child > tr:first-child > th,
        .table > caption + thead > tr:first-child > td,
        .table > colgroup + thead > tr:first-child > td,
        .table > thead:first-child > tr:first-child > td {
          border-top: 0;
        }
        .table > tbody + tbody {
          border-top: 2px solid #dddddd;
        }
        .table .table {
          background-color: #ffffff;
        }

        .table-hover > tbody > tr:hover > td,
        .table-hover > tbody > tr:hover > th {
          background-color: #f5f5f5;
        }
        table col[class*="col-"] {
          position: static;
          float: none;
          display: table-column;
        }
        table td[class*="col-"],
        table th[class*="col-"] {
          position: static;
          float: none;
          display: table-cell;
        }
        .table > thead > tr > td.active,
        .table > tbody > tr > td.active,
        .table > tfoot > tr > td.active,
        .table > thead > tr > th.active,
        .table > tbody > tr > th.active,
        .table > tfoot > tr > th.active,
        .table > thead > tr.active > td,
        .table > tbody > tr.active > td,
        .table > tfoot > tr.active > td,
        .table > thead > tr.active > th,
        .table > tbody > tr.active > th,
        .table > tfoot > tr.active > th {
          background-color: #f5f5f5;
        }
        .table-hover > tbody > tr > td.active:hover,
        .table-hover > tbody > tr > th.active:hover,
        .table-hover > tbody > tr.active:hover > td,
        .table-hover > tbody > tr:hover > .active,
        .table-hover > tbody > tr.active:hover > th {
          background-color: #e8e8e8;
        }
        .table > thead > tr > td.success,
        .table > tbody > tr > td.success,
        .table > tfoot > tr > td.success,
        .table > thead > tr > th.success,
        .table > tbody > tr > th.success,
        .table > tfoot > tr > th.success,
        .table > thead > tr.success > td,
        .table > tbody > tr.success > td,
        .table > tfoot > tr.success > td,
        .table > thead > tr.success > th,
        .table > tbody > tr.success > th,
        .table > tfoot > tr.success > th {
          background-color: #dff0d8;
        }
        .table-hover > tbody > tr > td.success:hover,
        .table-hover > tbody > tr > th.success:hover,
        .table-hover > tbody > tr.success:hover > td,
        .table-hover > tbody > tr:hover > .success,
        .table-hover > tbody > tr.success:hover > th {
          background-color: #d0e9c6;
        }
        .table > thead > tr > td.info,
        .table > tbody > tr > td.info,
        .table > tfoot > tr > td.info,
        .table > thead > tr > th.info,
        .table > tbody > tr > th.info,
        .table > tfoot > tr > th.info,
        .table > thead > tr.info > td,
        .table > tbody > tr.info > td,
        .table > tfoot > tr.info > td,
        .table > thead > tr.info > th,
        .table > tbody > tr.info > th,
        .table > tfoot > tr.info > th {
          background-color: #d9edf7;
        }
        .table-hover > tbody > tr > td.info:hover,
        .table-hover > tbody > tr > th.info:hover,
        .table-hover > tbody > tr.info:hover > td,
        .table-hover > tbody > tr:hover > .info,
        .table-hover > tbody > tr.info:hover > th {
          background-color: #c4e3f3;
        }
        .table > thead > tr > td.warning,
        .table > tbody > tr > td.warning,
        .table > tfoot > tr > td.warning,
        .table > thead > tr > th.warning,
        .table > tbody > tr > th.warning,
        .table > tfoot > tr > th.warning,
        .table > thead > tr.warning > td,
        .table > tbody > tr.warning > td,
        .table > tfoot > tr.warning > td,
        .table > thead > tr.warning > th,
        .table > tbody > tr.warning > th,
        .table > tfoot > tr.warning > th {
          background-color: #fcf8e3;
        }
        .table-hover > tbody > tr > td.warning:hover,
        .table-hover > tbody > tr > th.warning:hover,
        .table-hover > tbody > tr.warning:hover > td,
        .table-hover > tbody > tr:hover > .warning,
        .table-hover > tbody > tr.warning:hover > th {
          background-color: #faf2cc;
        }
        .table > thead > tr > td.danger,
        .table > tbody > tr > td.danger,
        .table > tfoot > tr > td.danger,
        .table > thead > tr > th.danger,
        .table > tbody > tr > th.danger,
        .table > tfoot > tr > th.danger,
        .table > thead > tr.danger > td,
        .table > tbody > tr.danger > td,
        .table > tfoot > tr.danger > td,
        .table > thead > tr.danger > th,
        .table > tbody > tr.danger > th,
        .table > tfoot > tr.danger > th {
          background-color: #f2dede;
        }
        .table-hover > tbody > tr > td.danger:hover,
        .table-hover > tbody > tr > th.danger:hover,
        .table-hover > tbody > tr.danger:hover > td,
        .table-hover > tbody > tr:hover > .danger,
        .table-hover > tbody > tr.danger:hover > th {
          background-color: #ebcccc;
        }
-->
    </style>
</body>
</html>
