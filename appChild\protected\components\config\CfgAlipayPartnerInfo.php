<?php

/*
 * <PERSON><PERSON>
 * 2012-9-19
 *
 */
// 配置 各校园的 合作身份者id,安全检验码,以及签约支付宝账号
return array(
    "test" => array(
        "partner" => "2088201564704294",
        "key" => "kh2i8hnd4euxubf80zp64vld4807i5b3",
        "seller_email" => "<EMAIL>",
        "number_code" => "001",
    ),
    "BJ_IA" => array(
        "partner" => "2088801537344641",
        "key" => "hbqfhmjtbb88wkm7a3sahuzzjh59918i",
        "seller_email" => "<EMAIL>",
        "number_code" => "001",
    ),
    "BJ_OE" => array(
        "partner" => "2088801537344641",
        "key" => "hbqfhmjtbb88wkm7a3sahuzzjh59918i",
        "seller_email" => "<EMAIL>",
        "number_code" => "002",
    ),
    "BJ_OG" => array(
        "partner" => "2088801537344641",
        "key" => "hbqfhmjtbb88wkm7a3sahuzzjh59918i",
        "seller_email" => "<EMAIL>",
        "number_code" => "003",
    ),
    "BJ_XHL" => array(
        "partner" => "2088801537344641",
        "key" => "hbqfhmjtbb88wkm7a3sahuzzjh59918i",
        "seller_email" => "<EMAIL>",
        "number_code" => "005",
    ),
    "BJ_TS" => array(
        "partner" => "2088801537344641",
        "key" => "hbqfhmjtbb88wkm7a3sahuzzjh59918i",
        "seller_email" => "<EMAIL>",
        "number_code" => "006",
    ),
    "NB_HH" => array(
        "partner" => "2088801537344641",
        "key" => "hbqfhmjtbb88wkm7a3sahuzzjh59918i",
        "seller_email" => "<EMAIL>",
        "number_code" => "007",
    ),
    "CD_FC" => array(
        "partner" => "2088801537344641",
        "key" => "hbqfhmjtbb88wkm7a3sahuzzjh59918i",
        "seller_email" => "<EMAIL>",
        "number_code" => "008",
    ),
    "CD_LH" => array(
        "partner" => "2088801537344641",
        "key" => "hbqfhmjtbb88wkm7a3sahuzzjh59918i",
        "seller_email" => "<EMAIL>",
        "number_code" => "009",
    ),
    "BJ_TT" => array(
        "partner" => "2088801537344641",
        "key" => "hbqfhmjtbb88wkm7a3sahuzzjh59918i",
        "seller_email" => "<EMAIL>",
        "number_code" => "010",
    ),
//    "BJ_TT" => array(
//        "partner" => "2088021640589337",
//        "key" => "ap0k9zx4jld9sk8zhchxqufjr36c5uxs",
//        "seller_email" => "<EMAIL>",
//        "number_code" => "010",
//    ),
    "TJ_SA" => array(
        "partner" => "2088801537344641",
        "key" => "hbqfhmjtbb88wkm7a3sahuzzjh59918i",
        "seller_email" => "<EMAIL>",
        "number_code" => "011",
    ),
    "TJ_EC" => array(
        "partner" => "2088801537344641",
        "key" => "hbqfhmjtbb88wkm7a3sahuzzjh59918i",
        "seller_email" => "<EMAIL>",
        "number_code" => "012",
    ),
    "XA_LB" => array(
        "partner" => "2088801537344641",
        "key" => "hbqfhmjtbb88wkm7a3sahuzzjh59918i",
        "seller_email" => "<EMAIL>",
        "number_code" => "015",
    ),
    "XA_GJ" => array(
        "partner" => "2088801537344641",
        "key" => "hbqfhmjtbb88wkm7a3sahuzzjh59918i",
        "seller_email" => "<EMAIL>",
        "number_code" => "016",
    ),
    "TJ_ES" => array(
        "partner" => "2088801537344641",
        "key" => "hbqfhmjtbb88wkm7a3sahuzzjh59918i",
        "seller_email" => "<EMAIL>",
        "number_code" => "017",
    ),
);