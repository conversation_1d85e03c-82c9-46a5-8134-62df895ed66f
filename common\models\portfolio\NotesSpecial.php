<?php

/**
 * This is the model class for table "ivy_notes_special".
 *
 * The followings are the available columns in table 'ivy_notes_special':
 * @property integer $id
 * @property string $schoolid
 * @property integer $classid
 * @property integer $yid
 * @property integer $childid
 * @property integer $weeknum
 * @property integer $stat
 * @property integer $timestamp
 * @property integer $uid
 */
class NotesSpecial extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return NotesSpecial the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_notes_special';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('classid, yid, childid, weeknum, stat, timestamp, uid', 'numerical', 'integerOnly'=>true),
			array('schoolid', 'length', 'max'=>30),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, schoolid, classid, yid, childid, weeknum, stat, timestamp, uid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'schoolid' => 'Schoolid',
			'classid' => 'Classid',
			'yid' => 'Yid',
			'childid' => 'Childid',
			'weeknum' => 'Weeknum',
			'stat' => 'Stat',
			'timestamp' => 'Timestamp',
			'uid' => 'Uid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('weeknum',$this->weeknum);
		$criteria->compare('stat',$this->stat);
		$criteria->compare('timestamp',$this->timestamp);
		$criteria->compare('uid',$this->uid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}