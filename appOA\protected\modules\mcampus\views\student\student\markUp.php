<style>
    [v-cloak] {
		display: none;
	}
    .font20{
        font-size:24px
    }
    .fontWeight{
        font-weight: 600 !important;
    }
    .menuList{
        position: absolute;
        right: 10px;
        top: 0;
    }
   .menuList .dropdown-menu{
       min-width: 0px !important;
       left:-60px !important;
   }
    .border10{
        border-radius: 10px;
        display: inline-block;
        min-width: 10px;
        padding: 3px 7px;
        font-size: 11px;
        font-weight: bold;
        color: #ffffff;
   }
   .stu{
        padding:2px 30px 3px 10px;
        border-radius: 2px;
        border: 1px solid #5CB85C;
        display: inline-block;
        margin-left:10px;
        font-weight: 600;
        position: relative;
   }
   .closeStu{
        display: inline-block;
        width: 15px;
        height: 15px;
        line-height: 14px;
        text-align: center;
        color: #fff;
        background: #999999;
        border-radius: 50%;
        font-size: 18px;
        position: absolute;
        right: 10px;
        top: 3px;
   }
   .newStu{
        background: #DFF0D8;
        border: 1px solid #5CB85C;
        color: #5CB85C;
   }
   .newStu .closeStu{
       background:#5CB85C;
   }
   .backStu{
        background: #D9EDF7;
        border-radius: 2px;
        border: 1px solid #5BC0DE;
        color: #5BC0DE;
   }
   .backStu .closeStu{
       background:#5BC0DE;
   }
   .pendStu{
        background: #FCF8E3;
        border-radius: 2px;
        border: 1px solid #F0AD4E;
        color: #F0AD4E;
   }
   .pendStu .closeStu{
       background:#F0AD4E;
   }
    .notReturnStu{
        background: #DDE9FB;
        border-radius: 2px;
        border: 1px solid #428bca;
        color: #428bca;
    }
    .notReturnStu .closeStu{
        background:#428bca;
    }
   .p0{
       padding:0
   }
   .m0{
       margin:0
   }
   .height{
       height:50px
   }
</style>
    <div class="col-md-10" id='container' v-cloak>
        <div class="row mb15">
            <div class="col-md-12 flex mt20" v-if='dataList.mark_total_list'>
                <div class='flex align-items'>
                    <span class='color3 font20 fontWeight pull-left'>标记学生</span>
                    <span class="label label-default ml10 pull-left  font16">{{dataList.mark_total_list.all}}</span>
                </div>
                
            </div>
        </div>
        <div class='relative' v-if='dataList.mark_total_list'>
            <div class=''>
                <select class="form-control select_2" v-model='yid' @change='getInfo'>
                    <option v-for='(list,id) in dataList.year_list.yearListArr' :value='list.yid'>{{list.year}}</option>
                </select>
            </div>
            <!--导出按钮-->
            <div class="btn-group"  style="position: absolute;right: 112px;top: 0px;">
                <button type="button" class="btn btn-default ml16" @click='exportExcel'>导出标记记录</button>
            </div>
            <div class="btn-group menuList">
                <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    批量标记 <span class="caret"></span>
                </button>
                <ul class="dropdown-menu">
                    <li><a href="javascript:;" @click='markTag("1")'>标记为新生</a></li>
                    <li><a href="javascript:;" @click='markTag("2")'>标记为返校</a></li>
<!--                    <li><a href="javascript:;" @click='markTag("3")'>标记为待定</a></li>-->
<!--                    <li><a href="javascript:;" @click='markTag("4",item)'>标记为不返校</a></li>-->
<!--                    <li v-for='(list,index) in fromSchoolMapList'>-->
<!--                        <a href="javascript:;" @click='markTag("5",item,index)'>标记来自{{list}}</a>-->
<!--                    </li>-->
                </ul>
            </div>

        </div>
        <div v-if='dataList.mark_total_list'>
            <div class='relative'>
                <ul class="nav nav-tabs mt20 tabList"  role="tablist">
                    <li role="presentation" class="active"><a href="#home" @click='showData'  aria-controls="home" role="tab" data-toggle="tab" >全部 <span class="label label-default border10 ml5">{{dataList.mark_total_list.all}}</span></a></li>
                    <li role="presentation"><a href="#new" @click='showData'  aria-controls="new" role="tab" data-toggle="tab" >新生 <span class="label label-success border10 ml5">{{dataList.mark_total_list.newStudent}}</span></a></li>
                    <li role="presentation"><a href="#back" @click='showData'  aria-controls="back" role="tab" data-toggle="tab">返校 <span class="label label-info border10 ml5">{{dataList.mark_total_list.backSchool}}</span></a></li>
<!--                    <li role="presentation"><a href="#pending" @click='showData'  aria-controls="pending" role="tab" data-toggle="tab">待定 <span class="label label-warning border10 ml5">{{dataList.mark_total_list.pending}}</span></a></li>-->
<!--                    <li role="presentation"><a href="#notReturn" @click='showData'  aria-controls="notReturn" role="tab" data-toggle="tab">不返校 <span class="label label-primary border10 ml5">{{dataList.mark_total_list.notReturn}}</span></a></li>-->
<!--                    <li role="presentation"><a href="#fromOther" @click='showData'  aria-controls="fromOther" role="tab" data-toggle="tab">来自其他学校 <span class="label label-warning border10 ml5">{{dataList.mark_total_list.fromOther}}</span></a></li>-->
                    <li role="presentation"><a href="#unmarked" @click='showData'  aria-controls="unmarked" role="tab" data-toggle="tab">未标记 <span class="label label-danger border10 ml5">{{dataList.mark_total_list.unmarked}}</span></a></li>
                </ul>
                
            </div>
            <div class="tab-content">
                <div role="tabpanel" class="tab-pane active" id="home">
                    <div class="panel panel-default mt20" v-for='(list,index) in dataList.class_list'>
                        <div class="panel-heading">
                            <div class="checkbox m0 " > 
                                <label class='fontWeight'>
                                    <input type="checkbox" v-model='list.checked'  class="emailStu"  :value="list.id" @change='allChecked($event,list,dataList.child_by_class[list.classid])'>{{list.title}}
                                </label>
                            </div>
                        </div>
                        <div class="panel-body">
                            <div class="col-xs-3 col-sm-3 col-md-3 col-lg-3 height" v-for='(item,idx) in dataList.child_by_class[list.classid]'>
                                <div class="checkbox"> 
                                    <label >
                                        <input type="checkbox" class="emailStu" v-model='list.checkedList' :value="item.childid" @change='childChecked($event,list,dataList.child_by_class[list.classid])'>{{item.child_name}} 
                                    </label>
                                    <div class="btn-group" v-if='item.mark==0'>
                                        <button type="button" class="btn btn-link dropdown-toggle p0" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            标记为 <span class="caret"></span>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a href="javascript:;" @click='markTag("1",item)'>标记为新生</a></li>
                                            <li><a href="javascript:;" @click='markTag("2",item)'>标记为返校</a></li>
<!--                                            <li><a href="javascript:;" @click='markTag("3",item)'>标记为待定</a></li>-->
<!--                                            <li><a href="javascript:;" @click='markTag("4",item)'>标记为不返校</a></li>-->
<!--                                            <li v-for='(list,index) in fromSchoolMapList'>-->
<!--                                                <a href="javascript:;" @click='markTag("5",item,index)'>标记来自{{list}}</a>-->
<!--                                            </li>-->
                                        </ul>
                                    </div>
                                    <div class="newStu stu" v-if='item.mark==1' @click='markTag("0",item)'><span>新生</span>  <span class='closeStu cur-p'>×</span></div>
                                    <div class="backStu stu" v-if='item.mark==2' @click='markTag("0",item)'><span>返校</span>  <span class='closeStu cur-p'>×</span></div>
                                    <div class="pendStu stu" v-if='item.mark==3' @click='markTag("0",item)'><span>待定</span>  <span class='closeStu cur-p'>×</span></div>
                                    <div class="notReturnStu stu" v-if='item.mark==4' @click='markTag("0",item)'><span>不返校</span>  <span class='closeStu cur-p'>×</span></div>
                                    <div class="pendStu stu" v-if='item.mark==5' @click='markTag("0",item)'><span>来自{{fromSchoolMapList[item.from_school]}}</span>  <span class='closeStu cur-p'>×</span></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane " id="new">
                    <div v-if='Object.keys(newChildList).length==0'>
                        <div class="alert alert-warning mt20" role="alert"><?php echo Yii::t("ptc", "No Data");?></div>                       
                    </div>
                    <div v-else>         
                        <template v-for='(list,index) in dataList.class_list'>
                            <div class="panel panel-default mt20" v-if='newChildList[list.classid]'>
                                <div class="panel-heading">
                                    <div class="checkbox m0"> 
                                        <label class='fontWeight'>
                                            <input type="checkbox" v-model='list.checked'  class="emailStu"  :value="list.id" @change='allChecked($event,list,newChildList[list.classid])'>{{list.title}}
                                        </label>
                                    </div>
                                </div>
                                <div class="panel-body">
                                    <div class="col-xs-3 col-sm-3 col-md-3 col-lg-3 height" v-for='(item,idx) in newChildList[list.classid]'>
                                        <div class="checkbox"> 
                                            <label >
                                                <input type="checkbox" class="emailStu" v-model='list.checkedList' :value="item.childid"  @change='childChecked($event,list,newChildList[list.classid])'>{{item.child_name}} 
                                            </label>
                                            <div class="newStu stu" @click='markTag("0",item)'><span>新生</span>  <span class='closeStu cur-p'>×</span></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane " id="back">    
                    <div v-if='Object.keys(backChildList).length==0'>
                        <div class="alert alert-warning mt20" role="alert"><?php echo Yii::t("ptc", "No Data");?></div>                       
                    </div>
                    <div v-else>                    
                        <template v-for='(list,index) in dataList.class_list'>
                            <div class="panel panel-default mt20" v-if='backChildList[list.classid]'>
                                <div class="panel-heading">
                                    <div class="checkbox m0"> 
                                        <label class='fontWeight'>
                                            <input type="checkbox" v-model='list.checked'  class="emailStu"  :value="list.id" @change='allChecked($event,list,backChildList[list.classid])'>{{list.title}}
                                        </label>
                                    </div>
                                </div>
                                <div class="panel-body">
                                    <div class="col-xs-3 col-sm-3 col-md-3 col-lg-3 height" v-for='(item,idx) in backChildList[list.classid]'>
                                        <div class="checkbox"> 
                                            <label >
                                                <input type="checkbox" class="emailStu" v-model='list.checkedList' :value="item.childid"  @change='childChecked($event,list,backChildList[list.classid])'>{{item.child_name}} 
                                            </label>
                                            <div class="backStu stu" @click='markTag("0",item)'><span>返校</span>  <span class='closeStu cur-p'>×</span></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane " id="pending">    
                    <div v-if='Object.keys(pendChildList).length==0'>
                        <div class="alert alert-warning mt20" role="alert"><?php echo Yii::t("ptc", "No Data");?></div>                       
                    </div>
                    <div v-else>
                        <template v-for='(list,index) in dataList.class_list'>
                            <div class="panel panel-default mt20" v-if='pendChildList[list.classid]'>
                                <div class="panel-heading">
                                    <div class="checkbox m0"> 
                                        <label class='fontWeight'>
                                            <input type="checkbox" v-model='list.checked'  class="emailStu"  :value="list.id" @change='allChecked($event,list,pendChildList[list.classid])'>{{list.title}}
                                        </label>
                                    </div>
                                </div>
                                <div class="panel-body">
                                    <div class="col-xs-3 col-sm-3 col-md-3 col-lg-3 height" v-for='(item,idx) in pendChildList[list.classid]'>
                                        <div class="checkbox"> 
                                            <label >
                                                <input type="checkbox" class="emailStu" v-model='list.checkedList' :value="item.childid" @change='childChecked($event,list,pendChildList[list.classid])'>{{item.child_name}} 
                                            </label>
                                            <div class="pendStu stu"  @click='markTag("0",item)'><span>待定</span>  <span class='closeStu cur-p'>×</span></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane " id="notReturn">
                    <div v-if='Object.keys(notReturnChildList).length==0'>
                        <div class="alert alert-warning mt20" role="alert"><?php echo Yii::t("ptc", "No Data");?></div>
                    </div>
                    <div v-else>
                        <template v-for='(list,index) in dataList.class_list'>
                            <div class="panel panel-default mt20" v-if='notReturnChildList[list.classid]'>
                                <div class="panel-heading">
                                    <div class="checkbox m0">
                                        <label class='fontWeight'>
                                            <input type="checkbox" v-model='list.checked'  class="emailStu"  :value="list.id" @change='allChecked($event,list,notReturnChildList[list.classid])'>{{list.title}}
                                        </label>
                                    </div>
                                </div>
                                <div class="panel-body">
                                    <div class="col-xs-3 col-sm-3 col-md-3 col-lg-3 height" v-for='(item,idx) in notReturnChildList[list.classid]'>
                                        <div class="checkbox">
                                            <label >
                                                <input type="checkbox" class="emailStu" v-model='list.checkedList' :value="item.childid" @change='childChecked($event,list,notReturnChildList[list.classid])'>{{item.child_name}}
                                            </label>
                                            <div class="notReturnStu stu"  @click='markTag("0",item)'><span>不返校</span>  <span class='closeStu cur-p'>×</span></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>

                <div role="tabpanel" class="tab-pane " id="fromOther">
                    <div v-if='Object.keys(fromOtherChildList).length==0'>
                        <div class="alert alert-warning mt20" role="alert"><?php echo Yii::t("ptc", "No Data");?></div>
                    </div>
                    <div v-else>
                        <template v-for='(list,index) in dataList.class_list'>
                            <div class="panel panel-default mt20" v-if='fromOtherChildList[list.classid]'>
                                <div class="panel-heading">
                                    <div class="checkbox m0">
                                        <label class='fontWeight'>
                                            <input type="checkbox" v-model='list.checked'  class="emailStu"  :value="list.id" @change='allChecked($event,list,fromOtherChildList[list.classid])'>{{list.title}}
                                        </label>
                                    </div>
                                </div>
                                <div class="panel-body">
                                    <div class="col-xs-3 col-sm-3 col-md-3 col-lg-3 height" v-for='(item,idx) in fromOtherChildList[list.classid]'>
                                        <div class="checkbox">
                                            <label >
                                                <input type="checkbox" class="emailStu" v-model='list.checkedList' :value="item.childid" @change='childChecked($event,list,fromOtherChildList[list.classid])'>{{item.child_name}}
                                            </label>
                                            <div class="pendStu stu"  @click='markTag("0",item)'><span>来自{{fromSchoolMapList[item.from_school]}}</span>  <span class='closeStu cur-p'>×</span></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
                <div role="tabpanel" class="tab-pane " id="unmarked">    
                    <div v-if='Object.keys(UnmarkedChildList).length==0'>
                        <div class="alert alert-warning mt20" role="alert"><?php echo Yii::t("ptc", "No Data");?></div>                       
                    </div>
                    <div v-else>
                        <template v-for='(list,index) in dataList.class_list'>
                            <div class="panel panel-default mt20" v-if='UnmarkedChildList[list.classid]'>
                                <div class="panel-heading">
                                    <div class="checkbox m0"> 
                                        <label class='fontWeight'>
                                            <input type="checkbox"   class="emailStu" v-model='list.checked'   :value="list.classid" @change='allChecked($event,list,UnmarkedChildList[list.classid])'>{{list.title}}
                                        </label>
                                    </div>
                                </div>
                                <div class="panel-body">
                                    <div class="col-xs-3 col-sm-3 col-md-3 col-lg-3 height" v-for='(item,idx) in UnmarkedChildList[list.classid]'>
                                        <div class="checkbox"> 
                                            <label >
                                                <input type="checkbox" class="emailStu" v-model='list.checkedList' :value="item.childid" @change='childChecked($event,list,UnmarkedChildList[list.classid])'>{{item.child_name}} 
                                            </label>
                                            <div class="btn-group">
                                                <button type="button" class="btn btn-link dropdown-toggle p0" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    标记为 <span class="caret"></span>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a href="javascript:;" @click='markTag("1",item)'>标记为新生</a></li>
                                                    <li><a href="javascript:;" @click='markTag("2",item)'>标记为返校</a></li>
<!--                                                    <li><a href="javascript:;" @click='markTag("3",item)'>标记为待定</a></li>-->
<!--                                                    <li><a href="javascript:;" @click='markTag("4",item)'>标记为不返校</a></li>-->
<!--                                                    <li v-for='(list,index) in fromSchoolMapList'>-->
<!--                                                        <a href="javascript:;" @click='markTag("5",item,index)'>标记来自{{list}}</a>-->
<!--                                                    </li>-->
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
     var  registrations = new Vue({
        el: "#container",
        data: {
            dataList:{},
            newChildList:{},
            backChildList:{},
            pendChildList:{},
            notReturnChildList:{},
            fromOtherChildList:{},
            UnmarkedChildList:{},
            yid:'',
            fromSchoolMapList:{},
        },
        created: function() {
            this.getInfo()
        },
        watch: {
        },
        methods: {
            getInfo(){
                var data={}
                if(this.yid!=''){
                    data={
                        yid:this.yid
                    }
                }else{
                    data={}
                }
                let that = this
                $.ajax({
                    url:'<?php echo $this->createUrl('getMarkUpStudent'); ?>',
                    type: 'get',
                    dataType:'json',
                    data:data,
                    success:function(data){
                        if(data.state=="success"){
                            for(var i=0;i<data.data.class_list.length;i++){
                                data.data.class_list[i].checked=false
                                data.data.class_list[i].checkedList=[]
                            }
                            that.dataList=data.data
                            that.yid=data.data.select_yid
                            that.fromSchoolMapList = data.data.fromSchool
                            that.newChildList=that.newChild(1)
                            that.backChildList=that.newChild(2)
                            that.pendChildList=that.newChild(3)
                            that.notReturnChildList=that.newChild(4)
                            that.fromOtherChildList=that.newChild(5)
                            that.UnmarkedChildList=that.newChild(0)
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            newChild(type){
                var list={}
                for(var key in this.dataList.child_by_class){
                    list[key]=[]
                    for(var i=0;i<this.dataList.child_by_class[key].length;i++){
                        if(this.dataList.child_by_class[key][i].mark==type){
                            list[key].push(this.dataList.child_by_class[key][i])
                        }
                    }
                }
                for(var key in list){
                    if(list[key].length==0){
                        delete list[key]
                    }
                }
                return list
            },
            markTag(type,item,fromschool){
                var childid=[]
                for(var i=0;i<this.dataList.class_list.length;i++){
                    childid.push.apply(childid,this.dataList.class_list[i].checkedList)
                }
                if(!item && childid.length==0){
                    resultTip({error: 'warning', msg: '请勾选学生'});
                    return
                }
                let that = this
                $.ajax({
                    url:'<?php echo $this->createUrl('reviseMarkUpStudent'); ?>',
                    type: 'get',
                    dataType:'json',
                    data:{
                        "mark":type,
                        "yid": this.yid,
                        child_data:item?[item.childid]:childid,
                        fromschool:fromschool ? fromschool : '',//返回的学校
                    },
                    success:function(data){
                        if(data.state=="success"){
                            that.getInfo()
                            resultTip({ msg: data.state});
                        }else{
                        resultTip({error: 'warning', msg: data.message});

                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            exportExcel(){
                let that = this
                $.ajax({
                    url:'<?php echo $this->createUrl('getChildMarkLog'); ?>',
                    type: 'get',
                    dataType:'json',
                    data:{
                        "yid": this.yid,
                    },
                    success:function(data){
                        if(data.state=="success"){
                            let list = data.data
                            if(list.length == 0){
                                resultTip({error: 'warning', msg: '暂无记录'});
                                return ;
                            }
                           let newData = [];
                           $.each(list,function(index, item){
                              newData.push({
                                  "学生ID":item.child_id,
                                  "中文姓名":item.cn_name,
                                  "英文姓名":item.en_name,
                                  "新学年班级":item.reserveCLassName,
                                  "入学学年":item.first_startyear,
                                  "预计上学日期":item.est_enter_date,
                                  "实际上学日期":item.enter_date,
                                  "转出校园":item.beforeSchoolabb,
                                  "招生标注动作":item.action,
                                  "标注日期":item.action_date,
                                  "标注时间":item.action_time,
                                  "操作人":item.action_user,
                                  "招生备注":'',
                                  "运营备注":'',
                                  "财务备注":'',
                              })
                           })
                            var title='标记记录-<?php echo $this->branchId?>'
                            const worksheet = XLSX.utils.json_to_sheet(newData);
                            const workbook = XLSX.utils.book_new();
                            XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');
                            that.downloadExcel(workbook,title);
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            downloadExcel(workbook,title) {
                XLSX.writeFile(workbook, title + '.xls', {
                    bookType: 'xls',
                    bookVBA: true
                });
            },
            allChecked(e,item,list){
                if(e.target.checked){
                    item.checked=true
                    item.checkedList=[]
                    for(var i=0;i<list.length;i++){
                        item.checkedList.push(list[i].childid)  
                    }
                }else{
                    item.checked=false
                    item.checkedList=[]
                }
            },
            childChecked(e,list,item){
                if(e.target.checked){
                   if(list.checkedList.length!=item.length){
                    list.checked=false
                   }else{
                    list.checked=true
                   }
                }else{
                    list.checked=false
                }                
            },
            showData(){
                 for(var i=0;i<this.dataList.class_list.length;i++){
                   this.dataList.class_list[i].checked=false
                   this.dataList.class_list[i].checkedList=[]
                }
            }
        }
    })
    </script>