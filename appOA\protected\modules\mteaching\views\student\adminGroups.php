<style>
    .list{
        list-style: none;
        padding: 0;
    }
    .groupList{
        padding:10px;
        align-items:center;
        font-size:14px
    }
    .groupList a{
        color:#333;
    }
    .groupList:hover,.currentGroup{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        cursor: pointer;
        color: #4D88D2;
    }
    .groupList:hover a,.currentGroup a{
        color: #4D88D2;
    }
</style>


<div class="container-fluid" id='page'>
    <div class='row mb20'>
        <div class="col-md-12">
            <ul class="nav nav-tabs" id="tablist">
                <li role="presentation" class="active"><a href="#home" aria-controls="home" id='MS'>Primary School</a></li>
                <li role="presentation"><a href="#profile" aria-controls="profile" id='ES'>Secondary School</a></li>
            </ul>
        </div>
    </div>
    <div class="row">
        <div class="col-md-2">
        <?php
            $mainMenu = array(
                array('label'=>Yii::t('user','Support Groups'), 'url'=>array("//mteaching/student/index","category"=>"adminGroups","category2"=>"supportList")),
                array('label'=>Yii::t('user','Head of Grade'), 'url'=>array("//mteaching/student/index","category"=>"adminGroups","category2"=>"headGroup")),
                array('label'=>Yii::t('user','Principal Office'), 'url'=>array("//mteaching/student/index","category"=>"adminGroups","category2"=>"office")),
                array('label'=>Yii::t('referral','Head of Department'), 'url'=>array("//mteaching/student/index","category"=>"adminGroups","category2"=>"subject")),

            );
            $this->widget('zii.widgets.CMenu',array(
                'id' => 'user-profile-item',
                'items'=> $mainMenu,
                'htmlOptions'=>array('class'=>'list'),
                'activeCssClass'=>'currentGroup',
                'itemCssClass'=>'groupList'
            ));
        ?>
        </div>
        <div class="col-md-10" id="main-edit-zone">
            <?php
            $this->renderPartial($category2,array('config'=>$config,'department'=>$department));
            ?>
        </div>
    </div>
</div>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    var department= <?php echo CJSON::encode($department)?>;
    var groupType=''
    $('#tablist a').click(function (e) {
        groupType=e.target.id
        e.preventDefault()
        $(this).tab('show')
    })
</script>