<?php
class Promotion extends CWidget{
    public $startDate = '2013/05/01';
    public $endDate = '2013/05/21';
	public $view = 'default';
    	
	public function run(){
        $theTime = time();
        $startTime = strtotime($this->startDate);
        $endTime = strtotime($this->endDate);
        if($startTime <= $theTime && $endTime >= $theTime) {
            Yii::app()->clientScript->registerCssFile(Yii::app()->theme->baseUrl . "/css/notice.css?t=" . Yii::app()
                    ->params['refreshAssets'] . $this->view);

            $this->render($this->view);
        }

		return;
		
		/*
		$startTime = strtotime($this->startDate);
		$endTime = strtotime($this->endDate) + 24 * 60 * 60;
		$time = time();
		$showNotice = is_null(Yii::app()->request->getParam('showNotice')) ? false : true;
		if( $showNotice || ($time >= $startTime && $time <= $endTime ) ){
			$alipayPartnerInfo = Mims::LoadConfig('CfgAlipayPartnerInfo');
			
			if(!isset($alipayPartnerInfo[$this->getController()->getSchoolId()])){return false;}
			Yii::app()->clientScript->registerCssFile(Yii::app()->theme->baseUrl . "/css/notice.css?t=" . Yii::app()->params['refreshAssets']);
			$this->render($this->view);
		}else{
			return false;
		}
		*/
	}
}