<style>
    .allCheck {
        position: absolute;
        right: 10px;
        top: 4px;
    }
    .childHover{
        padding:8px;
        border: 1px solid #fff;
    }
    .childHover:hover{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        border: 1px solid #4D88D2;
        cursor: pointer;
    }
    .border {
        border: 1px solid #E8EAED;
        border-radius: 4px;
    }
    .childList {
        max-height: 250px;
        min-height: 40px;
        padding: 14px;
        overflow-y: auto;
        border: 1px solid #E8EAED;
        border-radius: 4px;
    }
    .lineHeight {
        line-height: 32px;
    }
    .presenter{
        border-radius: 4px;
    }
    .replyColor {
        color: #F0AD4E;
    }
    .delSearchText{
        position: absolute;
        left: -25px;
        top: 10px;
        z-index: 99;
        color: #ccc;
    }
    .processed{
        background: #5CB85C;
        border-radius: 2px;
        padding:2px 4px;
        color:#fff
    }
    .pendingProcessed{
        background: #F0AD4E;
        border-radius: 2px;
        padding:2px 4px;
        color:#fff
    }
    .labelbg{
       padding:2px;
        background: #F7F7F8;
        border-radius: 2px;
        border: 1px solid #D9D9D9;
    }
    .bluebg{
        color: #4D88D2;
    }
    .el-input__icon{
        line-height:100%
    }
    .plus{
        width: 20px;
        height: 20px;
        border-radius: 50%;
        border: 1px solid #4D88D2;
        display: inline-block;
        font-size: 12px;
        text-align: center;
        line-height: 17px;
        margin-right: 4px;
    }
    .addPlus{
        font-size:32px;
        color:#4D88D2
    }
    .importantRed{
        color:#D9534F;
        padding-top:3px;
        margin-left:5px
    }
    .uploadImg{
        width: 72px;
        height: 72px;
        background: #FAFAFA;
        border-radius: 4px;
        border: 1px dashed #E8EAED;
        text-align:center;
        line-height:70px;
        display:inline-block;
    }
     
    .text {
        position: relative;
        overflow: hidden;
        line-height:20px;
        text-align: justify;
        text-overflow: ellipsis;
        word-break: break-all;
    }
    .text::before {
        float: right;
        height: calc(100% - 20px);
        content: '';
    }
    .expand {
        position: relative;
        float: right;
        clear: both;
        margin-left: 20px;
        font-size: 14px;
        padding: 0 0 0 8px;
        color: #4D88D2;
        line-height: 20px;
        border-radius: 4px;
        cursor: pointer;
        z-index: 10;
    }
    
    .expand::before {
        position: absolute;
        left: 1px;
        color: #333;
        transform: translateX(-100%);
        content: '...';
    }
    .classMore{
        height: 50px;
        background: #FFFFFF;
        border-radius: 4px;
        border: 1px solid #E8EAED;
        padding:16px;
        margin-bottom:16px;
        align-items:center
    }
    .classMore:hover{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        border: 1px solid #4D88D2;
        cursor: pointer;
    }
    .dp-text-ellipsis-wrapper {
        display: flex;
        overflow: hidden;
        font-size: 12px;
        line-height: 25px;
    }
    .text {
        position: relative;
        overflow: hidden;
        line-height:20px;
        text-align: justify;
        text-overflow: ellipsis;
        word-break: break-all;
    }
    .text::before {
        float: right;
        height: calc(100% - 20px);
        content: '';
    }
    .expand {
        position: relative;
        float: right;
        clear: both;
        margin-left: 20px;
        font-size: 14px;
        padding: 0 0 0 8px;
        color: #4D88D2;
        line-height: 20px;
        border-radius: 4px;
        cursor: pointer;
        z-index: 10;
    }
    
    .expand::before {
        position: absolute;
        left: 1px;
        color: #333;
        transform: translateX(-100%);
        content: '...';
    }
    .classMore{
        height: 50px;
        background: #FFFFFF;
        border-radius: 4px;
        border: 1px solid #E8EAED;
        padding:16px;
        margin-bottom:16px;
        align-items:center
    }
    .classMore:hover{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        border: 1px solid #4D88D2;
        cursor: pointer;
    }
    .warning_outline{
        padding:6px 8px;
        background:#F7F7F8;
        margin-left:15px;
        border-radius:2px;
        color:#666666;
        font-size:12px
    }
    .praise{
        width: 19px;
        height: 19px;
        margin-right:6px;
    }
    .lineHeight{
        line-height:1
    }
    .word_break{
        word-break: break-word;
    }
    .colorWait{
        color:#F0AD4E
    }
</style>
<div class="row" id='container' v-cloak >
    
    <div  class="col-md-12 col-lg-12 col-sm-12" v-if='dataList.list'>
        <div class='row'>
            <div class="col-md-6 mb16 text-left">
                <button type="button" class="btn btn-primary" @click='addApply()'><?php echo Yii::t("referral", "New Entry"); ?></button>
                <el-popover
                    placement="bottom"
                    width="200"
                    trigger="hover"
                    >
                    <div>
                        <div class='color3 font14 text-center fontBold'>Ivy Workspace</div>
                        <div class='text-center'>
                            <img v-if='branchId=="BJ_DS"' src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/stubehavior_ds.png' ?>" width='160' alt="">
                            <img v-if='branchId=="BJ_SLT"' src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/stubehavior_slt.png' ?>" width='160' alt="">
                        </div>
                        <div class='color6 font12 text-center'><?php echo Yii::t("referral", "Wechat scan"); ?></div>
                    </div>
                    <span class='ml24 cur-p' slot="reference"><span class='glyphicon glyphicon-qrcode'></span><span class='ml8'><?php echo Yii::t("referral", "Submit on Wechat"); ?></span> </span>
                </el-popover>
            </div>
            <div class="col-md-6  mb16 text-right">
                <div class="form-group has-feedback ml16"  style="width: 250px;float: right;">
                    <div class="input-group">
                        <input type="text" class="form-control" v-model='searchList' placeholder='<?php echo Yii::t("referral", "Input name");?>'  @keyup.enter='getAll()'>
                        <a href='javascript:;' class="input-group-addon relative" @click='getAll()'><span class='glyphicon glyphicon-search'></span> <?php echo Yii::t("global", "Search");?>
                        <span class="glyphicon glyphicon-remove-circle font12 delSearchText"  v-if='searchList!=""'  @click='searchList="";getAll()'></span>   
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class='clearfix'></div>
        <div >
            <div class='loading'  v-if='indexLoading'>
                <span></span>
            </div>
            <el-table
                :header-cell-style="{background:'#fafafa',color:'#333'}"
                :data="dataList.list"
                style="width: 100%;font-size:12px"
                :default-sort = "{prop: 'created_at', order: 'descending'}"
                @sort-change="sort_change" 
                >
                <template slot="empty">
                    <?php echo Yii::t("ptc", "No Data"); ?>
                </template>
                <el-table-column
                prop="child"
                label="<?php echo Yii::t("admissions", "Student name");?>"
                min-width="200">
                    <template slot-scope="scope">
                        <div v-if='scope.row.child.length==1'>
                            <div class="media flex  align-items" v-for='(list,index) in scope.row.child'>
                                <div class="media-left pull-left media-middle relative">
                                    <img :src="dataList.child_list[list.id].avatar" data-holder-rendered="true" class="avatar">
                                </div> 
                                <div class="pull-left media-middle">
                                    <div class="lineHeight  color3"> {{dataList.child_list[list.id].name}}
                                        <span v-if='list.tag==2 || list.tag==3' :class='list.tag==2?"waitingColor":list.tag==3?"colorRed":""'  class='glyphicon glyphicon-bookmark font12'></span>
                                    </div> 
                                    <div class="color6 font12 lineHeight mt8">{{dataList.child_list[list.id].className}}</div>
                                </div>
                            </div>
                        </div>
                        <div v-else>
                            <img :src="dataList.child_list[list.id].avatar" v-for='(list,index) in scope.row.child' data-holder-rendered="true" class="avatar32 mb8 mr4">
                            <el-popover
                                placement="bottom"
                                trigger="click">
                                <div class="media flex  align-items" v-for='(list,index) in scope.row.child'>
                                    <div class="media-left pull-left media-middle relative">
                                        <img :src="dataList.child_list[list.id].avatar" data-holder-rendered="true" class="avatar">
                                    </div> 
                                    <div class="pull-left media-middle">
                                        <div class="font12 color6 lineHeight"> {{dataList.child_list[list.id].name}}
                                            <span v-if='list.tag==2 || list.tag==3'  :class='list.tag==2?"waitingColor":list.tag==3?"colorRed":""'  class='glyphicon glyphicon-bookmark font12'></span>
                                        </div> 
                                        <div class="color6 font12 lineHeight mt8">{{dataList.child_list[list.id].className}}</div>
                                    </div>
                                </div>
                                <span class='bluebg  cur-p' slot="reference"><?php echo Yii::t("referral", "Total "); ?>{{scope.row.child.length}}<?php echo Yii::t("referral", " "); ?></span>
                            </el-popover>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                prop="id"
                label="<?php echo Yii::t("labels", "ID"); ?>"
                min-width="150">
                    <template slot-scope="scope">
                       <div class='color6'>No.{{scope.row.id}}</div>
                       <div class='color3'>{{dataList.staff_info[scope.row.created_by].name}}</div>
                       <el-tooltip class="item" effect="dark" :content="scope.row.created_date" placement="top">
                            <span class='color6 cur-p'>{{scope.row.diff_date}}</span>
                        </el-tooltip>
                    </template>
                </el-table-column>
                <el-table-column
                prop="id"
                label="<?php echo Yii::t("referral", "Event Description"); ?>"
                min-width="300">
                    <template slot-scope="scope">
                        <div class="dp-text-ellipsis-wrapper" :class='scope.row._id' v-if='showHtml'>
                            <div class="text cur-p" :style="scope.row.more?textStyleObject:''" ref="text" @click='scope.row.more=!scope.row.more' v-if='shouldShowExpandButton[scope.row._id]' >
                                <span class="expand" v-if='scope.row.more' ></span>
                                <el-tooltip class="item" effect="dark" content="<?php echo Yii::t("behavior", "Positive Behavior"); ?>" placement="top">
                                <img  class='praise'  v-if='scope.row.detail.subtype=="POSITIVEBEHAVIOR"' src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/praise.png' ?>" alt=""></el-tooltip><span :class="'span_'+scope.row._id" class='word_break'>{{scope.row.detail.desc}}</span>
                            </div>
                            <div v-else-if='!shouldShowExpandButton[scope.row._id]' style='line-height:20px'>
                                <el-tooltip class="item" effect="dark" content="<?php echo Yii::t("behavior", "Positive Behavior"); ?>" placement="top">
                                <img  class='praise'  v-if='scope.row.detail.subtype=="POSITIVEBEHAVIOR"' src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/praise.png' ?>" alt=""></el-tooltip><span :class="'span_'+scope.row._id" class='word_break'>{{scope.row.detail.desc}}</span>
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                prop="office_user"
                label="<?php echo Yii::t("referral", "Status"); ?>"
                :min-width="showMinWidth">
                    <template slot-scope="scope">
                        <div class='flex' v-if='scope.row.just_record==0'>
                            <div class='text-center minWidth mr50' v-for='(list,index) in scope.row.nodes_order'>
                                <div v-if='scope.row.nodes_by_node[list].status==1'>
                                    <div class='relative'>
                                        <div class='firstBorder' v-if='scope.row.nodes_order.length>1 && index+1<scope.row.nodes_order.length'></div>
                                        <div class='relative inline-block'>
                                            <img :src="dataList.staff_info[scope.row.nodes_by_node[list].updated_by].photoUrl" alt="" class='avatar32'>
                                            <span class='el-icon-success iconAbsolute'></span>
                                        </div>
                                    </div>
                                    <div class='color3 '>{{scope.row.nodes_order_title[index]}}</div>
                                    <div class='color6 font12'>
                                    <el-tooltip class="item" effect="dark"  placement="top">
                                    <div slot="content">{{dataList.staff_info[scope.row.nodes_by_node[list].updated_by].name}}<br/><?php echo Yii::t("leave", "Completed"); ?> {{scope.row.nodes_by_node[list].updated_at}}</div>
                                    <div class='color6'>{{scope.row.nodes_by_node[list].diff_date}}</div>
                                    </el-tooltip>
                                    </div>
                                </div>
                                <div v-else>
                                    <div>
                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/pending.png' ?>" alt="" class='avatar32'>
                                        <!-- <span class='waiting'>?</span> -->
                                    </div>
                                    <div class='color3 '>{{scope.row.nodes_order_title[index]}}</div>
                                    <div class='font12 waitingColor'><?php echo Yii::t("referral", "In Process"); ?></div>
                                </div>
                            </div>
                        </div>
                        <div v-if='scope.row.just_record==1' class='text-center minWidth'>
                           <?php echo Yii::t("referral", "Record only"); ?>
                        </div>
                    </template>     
                </el-table-column>
                <el-table-column
                prop="office_user"
                fixed="right"
                label="<?php echo Yii::t("newDS", "Actions"); ?>"
                width="150">
                    <template slot-scope="scope">
                        <el-button type="text" @click='showDetails(scope.row)' class=' bluebg mr20 font12'><?php echo Yii::t("global", "View Detail"); ?></el-button>
                        <el-dropdown trigger="click" v-if='scope.row.can_edit==1'>
                            <span class="el-dropdown-link bluebg cur-p font12"><?php echo Yii::t("referral", "More"); ?><i class="el-icon-arrow-down el-icon--right"></i>
                            </span>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item @click.native='editList(scope.row)'><?php echo Yii::t("global", "Edit"); ?></el-dropdown-item>
                                <el-dropdown-item @click.native='delList(scope.row)'><?php echo Yii::t("global", "Delete"); ?></el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </template>
                </el-table-column>
            </el-table>
            <nav aria-label="Page navigation" v-if='CopyPages.count_page>1'  class="text-left ml10">
                <ul class="pagination">
                    <li v-if='pageNum >1'>
                        <a href="javascript:void(0)" @click="plus(1)" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    <li v-else class="disabled">
                        <a aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    <li class="previous" v-if='pageNum >1'>
                        <a href="javascript:void(0)" @click="prev(pageNum)">‹</a>
                    </li>
                    <li class="disabled" v-else>
                        <a href="javascript:void(0)">‹</a>
                    </li>
                    <li v-for='(data,index) in pages.count' :class="{ active:data==pageNum }">
                        <a href="javascript:void(0)" @click="plus(data)">{{data}}</a>
                    </li>
                    <li class="previous" v-if='pageNum <CopyPages.count_page'>
                        <a href="javascript:void(0)" @click="next(pageNum)">›</a>
                    </li>
                    <li class="previous disabled" v-else>
                        <a href="javascript:void(0)">›</a>
                    </li>
                    <li v-if='pageNum <CopyPages.count_page'>
                        <a href="javascript:void(0)" @click="plus(CopyPages.count_page)" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li v-else class="disabled">
                        <a aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                </ul>
                <div class='summary mb10'>第 <span v-if='pageNum*CopyPages.per_page-CopyPages.per_page==0'>1</span><span v-else>{{pageNum*CopyPages.per_page-CopyPages.per_page}}</span>-{{pageNum*CopyPages.per_page}} 条, 共 {{CopyPages.total}} 条.</div>
            </nav>
        </div>
    </div>
     <!-- 详情 -->
    <div class="modal fade" id="contentModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" >
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "Detail"); ?></h4>
                </div>
                <div class="modal-body p0 relative" v-if='contentList.behavior'>
                    <div><?php $this->renderPartial("detail", array('type' => 'myList'));?></div>
                    <div class='clearfix'></div>
                </div>
            </div>
        </div>
    </div>
    <!-- 增加申请单 -->
    <div class="modal fade" id="newApplyModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel"  data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close"  @click='closeForm()' aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel">{{idEdit?'<?php echo Yii::t("global", "Edit"); ?>':'<?php echo Yii::t("referral", "New Entry"); ?>'}}</h4>
                </div>
                <div class="modal-body font14 p0">
                    <div class='overflow-y scroll-box p24 font14' :style="'max-height:'+(height)+'px;overflow-x: hidden;'">
                        <div class='flex align-items bgGrey mb24'>
                            <span><?php echo Yii::t("referral", "Submitter"); ?>：</span>
                            <img :src="config.user_data.photoUrl" alt="" class='avatar24 ml8'>
                            <span class='flex1 color3 ml8'>{{ config.user_data.name }}</span>
                        </div>
                        <div>
                            <div class='flex font14'>
                                <div class='flex fontBold'><span class='color3'><?php echo Yii::t("attends", "Students"); ?></span><span class='colorRed'>*</span></div> 
                            </div>
                            <div class='mt5'> 
                                <el-button type="text" icon="el-icon-circle-plus-outline" class='font14 bluebg' @click='addChild("multiple")'><?php echo Yii::t("ptc", "Add student"); ?></el-button>
                            </div>
                            <div class='row'>
                                <div class='col-md-4' v-for='(list,index) in confirmChild'>
                                <div class="media listMedia mt10 pb10 ">
                                    <div class="media-left pull-left media-middle relative">
                                        <img :src="list.avatar" data-holder-rendered="true" class="avatar">
                                    </div> 
                                    <div class="media-right pull-right mt15 text-right">
                                        <span class='el-icon-circle-close font16 closeIcon' @click='Unassign(list,index,"del")'></span>
                                    </div>
                                    <div class="pull-left media-middle">
                                        <div class="mt4 color3 font14">{{list.name}}
                                            <span v-for='(item,ind) in list.label' >
                                                <span v-if='item.flag==2 || item.flag==3' class='glyphicon glyphicon-bookmark font12' :class='item.flag==2?"waitingColor":item.flag==3?"colorRed":""'></span>
                                            </span>
                                        </div> 
                                        <div class="color6 font12">{{list.class_name}}</div>
                                    </div>
                                </div>
                                </div>
                            </div>
                        </div>
                        <div class='mt24' v-if='Object.keys(classTeacher).length>0 && confirmChild.length>0'>
                            <div> 
                                <span class='mr10 color3 fontBold el-switch__label el-switch__label--left'><?php echo Yii::t("referral", "Collaborators"); ?></span>   
                                <el-switch
                                    v-model="showClassTeacher">
                                </el-switch>
                            </div>
                            <div v-if='showClassTeacher' class='bgGrey mt15'>
                                <div v-for='(data,key,index) in classTeacher' class='flex align-items' :class='index!=0?"mt15":""'>
                                    <span class='color6 mr20'>{{data.title}}</span>
                                    <el-select v-model="data.value" class='flex1' multiple placeholder="<?php echo Yii::t("global", "Please Select"); ?>" @change="selectTeacher(key)">
                                        <el-checkbox
                                            v-model="data.checkall"
                                            class="ml20"
                                            @change="allTeacher(key)"
                                        >
                                        <?php echo Yii::t("global", "Select All"); ?>
                                        </el-checkbox>
                                        <el-option
                                        v-for="item in data.data"
                                        :key="item.uid"
                                        :label="item.name"
                                        :value="item.uid">
                                        </el-option>
                                    </el-select>
                                </div>
                            </div>
                            <div class='mt32' v-if='subtypeList.length>0'>
                                <div class='color3 fontBold mb10 flex'><span class=''><?php echo Yii::t("referral", "Type"); ?></span><span class='importantRed'>*</span></div>
                                <div>
                                    <label class="radio-inline"  v-for='(list,index) in subtypeList'>
                                        <input type="radio" @change='behavioralSubtype()'  :value="list.value" v-model='saveSubmit.subtype'> {{ list.title }}
                                    </label>
                                </div>
                            </div>
                            <div class='mt32' v-if='behavioral_sub.length>0 && saveSubmit.subtype=="BEHAVIORAL"'>
                                <div  class='mb10'>
                                    <span  class='color3 fontBold'><?php echo Yii::t("referral", "Special Tracking"); ?></span>  
                                    <span class='colorWait font12 ml5'><span class='el-icon-info'></span> <?php echo Yii::t("referral", "This entry will inform SMT when checkbox below is checked"); ?></span>
                                </div>
                                <div class='mb10'>
                                    <label class="checkbox-inline" v-for='(list,index) in behavioral_sub' >
                                        <input type="checkbox" @change='behavioralSub(list)' :disabled='list.disabled' :value="list.value" v-model='saveSubmit.behavioral_sub'> {{list.title}}
                                    </label>
                                </div>
                            </div>
                            <div class='mt32' v-if='subjectList.length>0 && saveSubmit.subtype=="ACADEMIC"'>
                                <div  class='color3 fontBold mb10'><?php echo Yii::t("application", "Subject"); ?></div>
                                <div class='mb10'>
                                    <label class="radio-inline" v-for='(list,index) in subjectList' >
                                        <input type="radio" :value="list.value" v-model='saveSubmit.subject'> {{list.title}}
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class='mt32'>
                            <div  class='color3 fontBold mb10 flex'>
                                <span v-if="saveSubmit.subtype=='POSITIVEBEHAVIOR'"><?php echo Yii::t("behavior", "Time"); ?></span>
                                <span v-else><?php echo Yii::t("referral", "Time"); ?></span>
                                <span class='importantRed'>*</span>
                            </div>
                            <div>
                                <el-date-picker
                                    v-model="saveSubmit.date"
                                    type="date"
                                    format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd"
                                    placement="bottom-start"
                                    placeholder="<?php echo Yii::t("ptc", "Select a date"); ?>">
                                </el-date-picker>
                                <el-time-picker
                                    class='ml20'
                                    v-model="saveSubmit.time"
                                    value-format="HH:mm"
                                    format="HH:mm"
                                    placement="bottom-start"
                                    placeholder="<?php echo Yii::t("labels", "Time"); ?>">
                                </el-time-picker>
                            </div>
                        </div>
                        <div class='mt32' v-if='locationList.length>0 && confirmChild.length>0'>
                            <div  class='color3 fontBold mb10'><?php echo Yii::t("referral", "Location"); ?></div>
                            <div class='mb10'>
                                <label class="checkbox-inline" v-for='(list,index) in locationList' >
                                    <input type="checkbox" :value="list.value" v-model='saveSubmit.location'> {{list.title}}
                                </label>
                            </div>
                            <div class='flex'>
                                <div>
                                    <label class="checkbox-inline mr10" >
                                        <input type="checkbox" v-model='location_other'><?php echo Yii::t("report", "Other"); ?>
                                    </label>
                                </div>
                                <input type="text" class="form-control flex1 ml5" v-if='location_other' placeholder="<?php echo Yii::t("report", "Other"); ?>" v-model='saveSubmit.location_other'>
                            </div>
                        </div>
                        <div class='mt32'>
                            <div class='color3 fontBold mb10 flex'><span><?php echo Yii::t("referral", "Event Description"); ?></span><span class='importantRed'>*</span></div>
                            <div>
                                <textarea class="form-control" rows="4" v-model='saveSubmit.desc'></textarea>
                            </div>
                        </div>
                        <div  class='color3 fontBold mb10 mt32'><?php echo Yii::t("curriculum", "Attachments"); ?></div>
                        <div>
                            <div class='mb12'>
                                <el-button type="text" icon="el-icon-circle-plus-outline" id='addFile' class='font14 bluebg'><?php echo Yii::t("referral", "Upload"); ?></el-button>
                            </div>
                            <div>
                                <div class='' v-if='attachments.img.length>0'>
                                    <div class='imgData mr8 mb12'  v-for='(list,i) in attachments.img'>
                                        <div v-if="list.types=='1'">
                                            <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Uploading");?></div>
                                        </div>
                                        <div v-else>
                                            <img class='imgList' @click='uploadShowImg(list.file_key)'  :src="list.file_key" alt="">
                                            <span aria-hidden="true" class='closeImg' @click.stop='delImg("img",list,i)'>×</span>
                                        </div>
                                    </div>
                                    <template v-if='loadingType==1'>
                                        <div class='imgData mr8 mb12'  v-for='(list,i) in loadingList'>
                                            <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Upload queue");?></div>
                                        </div>
                                    </template>
                                </div>
                            </div>
                            <div>
                                <div class='mt12' v-if='attachments.other.length>0'>
                                    <div class='flex uploadFile' v-for='(list,index) in attachments.other'>
                                        <span class='glyphicon glyphicon-paperclip mr8'></span>
                                        <a target="_blank" class='flex1'  style='line-height:26px' :href='list.file_key' v-if='!list.isEdit'>{{list.title}}</a>
                                        <span class='flex1' v-else><input type="input" class='inputStyle'  @keyup.enter="saveFile(list,index)"  v-model='attachmentName' ></span>
                                        <span v-if="list.types=='1'"></span>
                                        <template v-else>
                                            <template v-if='!list.isEdit'>
                                                <span class='glyphicon glyphicon-edit icon mr16' v-if='list.file_key!=""' @click.stop='list.isEdit=true,attachmentName=list.title'></span>
                                                <span class='glyphicon glyphicon-trash icon' v-if='list.file_key!=""' @click.stop='delImg("link",list,index)'></span>
                                            </template>
                                            <span style='width:90px'  class='text-right inline-block' v-else>
                                                <button type="button" class="btn btn-primary btn-xs" @click='saveFile(list,index)'><?php echo Yii::t("global", "Save");?></button>
                                                <button type="button" class="btn btn-default btn-xs" @click='list.isEdit=false'><?php echo Yii::t("global", "Cancel");?></button>
                                            </span>
                                        </template>
                                    </div>
                                </div>
                                <template v-if='loadingType==2'>
                                    <div class='flex uploadFile'  v-for='(list,i) in loadingList'>
                                        <span class='glyphicon glyphicon-paperclip mr8'></span>
                                        <a  class='flex1'  style='line-height:26px' > <?php echo Yii::t("directMessage", "Upload queue");?></a>
                                    </div>
                                </template>
                            </div>
                        </div>
                        <div v-if="saveSubmit.subtype!='POSITIVEBEHAVIOR'">
                        <div class='mt32' v-if='purposeList.length>0 && confirmChild.length>0'>
                            <div class='color3 fontBold mb10 flex'><span class=''><?php echo Yii::t("referral", "Follow up actions"); ?></span><span class='importantRed'>*</span></div>
                            <div>
                                <label class="radio-inline"  v-for='(list,index) in purposeList'>
                                    <input type="radio"  :value="list.value" :disabled='list.disabled' v-model='saveSubmit.purpose'> {{ list.title }}
                                </label>
                            </div>
                        </div>
                        <div class='mt32'>
                            <div  class='color3 fontBold mb10'><?php echo Yii::t("referral", "Core value that was not met or violated"); ?></div>
                            <div>
                                <label class="checkbox-inline" v-for='(list,index) in config.violated'>
                                    <input type="checkbox" :value="list.value" v-model='saveSubmit.violated'> {{list.title}}
                                </label>
                            </div>
                        </div>
                        <div class='mt32'>
                            <div  class='color3 fontBold mb10'><?php echo Yii::t("referral", "Problem Behavior"); ?></div>
                            <div>
                                <div class="checkbox"  v-for='(list,index) in config.problem_behavior'>
                                    <label>
                                        <input type="checkbox"  :value="list.value" v-model='saveSubmit.problem_behavior'>  {{list.title}}
                                    </label>
                                </div>
                            </div>
                            <div class='flex '>
                                <div>
                                    <label class="checkbox-inline mr10" >
                                        <input type="checkbox" v-model='problem_behavior_other'><?php echo Yii::t("report", "Other"); ?>
                                    </label>
                                </div>
                                <input type="text" class="form-control flex1 ml5" v-if='problem_behavior_other' placeholder="<?php echo Yii::t("report", "Other"); ?>" v-model='saveSubmit.problem_behavior_other'>
                            </div>
                        </div>
                        <div class='mt32'>
                            <div  class='color3 fontBold mb10'><?php echo Yii::t("referral", "What previous steps have you taken to address this behavior"); ?></div>
                            <div>
                                <div class="checkbox"  v-for='(list,index) in config.intervene'>
                                    <label>
                                        <input type="checkbox"  :value="list.value" v-model='saveSubmit.intervene'>  {{list.title}}
                                    </label>
                                </div>
                            </div>
                            <div class='flex '>
                                <div>
                                    <label class="checkbox-inline mr10" >
                                        <input type="checkbox" v-model='intervene_other'><?php echo Yii::t("report", "Other"); ?>
                                    </label>
                                </div>
                                <input type="text" class="form-control flex1 ml5" v-if='intervene_other' placeholder="<?php echo Yii::t("report", "Other"); ?>" v-model='saveSubmit.intervene_other'>
                            </div>
                        </div>
                        <div class='mt32'>
                            <div  class='color3 fontBold mb10'><?php echo Yii::t("referral", "Possible Motivation"); ?></div>
                            <div>
                                <div class="checkbox"  v-for='(list,index) in config.motivation'>
                                    <label>
                                        <input type="checkbox"  :value="list.value" v-model='saveSubmit.motivation'>  {{list.title}}
                                    </label>
                                </div>
                            </div>
                            <div class='flex '>
                                <div>
                                    <label class="checkbox-inline mr10" >
                                        <input type="checkbox" v-model='motivation_other'><?php echo Yii::t("report", "Other"); ?>
                                    </label>
                                </div>
                                <input type="text" class="form-control flex1 ml5" v-if='motivation_other' placeholder="<?php echo Yii::t("report", "Other"); ?>" v-model='saveSubmit.motivation_other'>
                            </div>
                        </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" @click='closeForm()'><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" @click='saveForm("modal")'><?php echo Yii::t("global", "Submit"); ?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 选择学生 -->
    <div class="modal fade" id="addClassModal" tabindex="-1" role="dialog" data-backdrop="static" data-keyboard="false" >
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t("referral", "Student"); ?></h4>
                </div>
                <div class="modal-body p0 relative">
                    <span class='borderLeftpos'></span>
                    <div style='max-height:600px;' class='p24 row' v-if='addChildType=="multiple"'>
                        <div class='col-md-6 col-sm-6'>
                            <div>
                                <el-input
                                placeholder="<?php echo Yii::t("global", "Search"); ?>"
                                v-model='searchText' 
                                clearable>
                                </el-input>
                            </div>
                            <div  class="tab-pane active mt15 scroll-box" id="class" v-if='searchText==""'  style='max-height:500px;overflow-y:auto'>
                                <div v-for='(list,index) in classList' class='relative mb16'>
                                    <p  @click='getChild(list)'>
                                        <span  class='font14 color606 cur-p'>{{list.title}} </span>
                                        <span class='el-icon-arrow-down ml5' v-if='classId!=list.classid'></span>
                                        <span class='el-icon-arrow-up ml5' v-else></span>
                                    </p>
                                    <p  class='allCheck' v-if='classId==list.classid'><button class="btn btn-default pull-right btn-xs" type="button" @click='selectAll(list,index)'  v-if='list.childData && list.childData.length!=0'><?php echo Yii::t("global", "Select All");?></button></p>
                                    <div  class='border scroll-box mr10 childList' v-if='classId==list.classid'>
                                        <div v-if='!childLoading'>
                                            <div class='' v-if='list.childData && list.childData.length!=0'>
                                                <div class="media mt10 flex align-items listMedia" v-for='(item,idx) in list.childData'>
                                                    <div class="media-left pull-left media-middle">
                                                        <a href="javascript:void(0)">
                                                            <img :src="item.avatar" data-holder-rendered="true" class="media-object img-circle avatar32">
                                                        </a>
                                                    </div>
                                                    <div class="media-body media-middle flex1">
                                                        <div class="font14 lineHeight">{{item.name}}</div>
                                                    </div>
                                                    <div v-if='item.stuLoading'>
                                                        <div v-if='item.disabled' class="media-right pull-right text-muted lineHeight">
                                                            <span class='cur-p mt10 color9'><?php echo Yii::t("newDS", "Subscribed");?></span>
                                                        </div>
                                                        <div v-else class="media-right pull-right text-muted" @click='assignChildren(item,index,idx)'>
                                                            <span class='cur-p font16 bluebg  el-icon-circle-plus-outline'></span>
                                                        </div>
                                                    </div>
                                                    <div class='childLoading' v-else>
                                                        <span></span>
                                                    </div>
                                                   
                                                </div>
                                            </div>
                                            <div v-else>
                                                <div class='font12 text-muted text-center'><?php echo Yii::t("newDS", "no student in this class");?></div>
                                            </div>
                                        </div>
                                        <div class='loading' v-else>
                                            <span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else>
                                <div v-if='searchChildList.length!=0'  class='scroll-box'   style='max-height:500px;overflow-y:auto'>                               
                                    <div class="media mt10 flex align-items listMedia" v-for='(item,idx) in searchChildList'>
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                                <img :src="item.avatar" data-holder-rendered="true" class="media-object img-circle avatar32">
                                            </a>
                                        </div>
                                        <div class="media-body media-middle flex1">
                                            <div class=" font12 color3">{{item.name}}</div>
                                            <div class="color6 lineHeight mt5">{{item.className}}</div>
                                        </div>
                                        <div v-if='item.disabled' class="media-right pull-right text-muted lineHeight">
                                            <span class='cur-p mt10'><?php echo Yii::t("newDS", "Subscribed");?></span>
                                        </div>
                                        <div v-else class="media-right pull-right text-muted" @click='assignChildren(item,idx,"search")'>
                                            <span class='cur-p font16 bluebg  el-icon-circle-plus-outline'></span>
                                        </div>
                                       
                                    </div>
                                </div>
                                <div v-else-if='searchText!=""'>
                                        <div class='font14 color6 text-center mt20'><?php echo Yii::t("ptc", "No Data"); ?></div>    
                                </div>
                            </div>
                        </div>
                        <div class='col-md-6 col-sm-6 borderLeft'>
                            <p class='mt10 font14 color606'>
                            <?php echo Yii::t("newDS", " ");?>{{childSelected.length}}<?php echo Yii::t("newDS", " student(s) selected");?>
                                <button class="btn btn-link pull-right btn-xs font14" v-if='childSelected.length!=0' type="button" @click='batchDel("modal")'><?php echo Yii::t("directMessage", "Clear All");?></button>
                            </p>
                            <div class='scroll-box p10 overflow-y' style='height:500px'>
                                <div class="media m0 flex align-items listMedia" v-for='(list,index) in childSelected'>
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)">
                                            <img :src="list.avatar" data-holder-rendered="true" class="media-object img-circle avatar32">
                                        </a>
                                    </div>
                                    
                                    <div class="media-body media-middle flex1">
                                        <div class=" font12 color3">{{list.name}}
                                            <span v-for='(item,ind) in list.label' >
                                                <span v-if='item.flag==2 || item.flag==3' class='glyphicon glyphicon-bookmark font12' :class='item.flag==2?"waitingColor":item.flag==3?"colorRed":""'></span>
                                            </span>
                                        </div>
                                        <div class="color6 lineHeight mt5">{{list.classTitle}}</div>
                                    </div>
                                    <div class="media-right pull-right text-muted" @click='Unassign(list,index)'>
                                        <span class='closeChild cur-p  font16 el-icon-circle-close'></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class='clearfix'></div>
                    </div>
                </div> 
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" @click='getTeacher()'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade"  id="followModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel"  aria-labelledby="wantSayModalLabel"  data-backdrop="static">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title"><?php echo Yii::t("referral", "Follow up actions"); ?></h4>
            </div>
            <div class="modal-body">
               <div class='flex align-items' v-if='followItem.child'>
                    <div class="media flex1" > 
                        <div class="media-left pull-left media-middle">
                            <a href="javascript:void(0)">
                                <img :src="contentList.student_info_list[followItem.child.id].avatar" data-holder-rendered="true" class="avatar">
                            </a>
                        </div>
                        <div class="media-body pt4 media-middle">
                            <div class="lineHeight20  text-primary">
                                <span class='font14 color3 nowrap'>{{contentList.student_info_list[followItem.child.id].name}} </span>
                            </div>
                            <div class="font12 color6 nowrap">{{contentList.student_info_list[followItem.child.id].className}}</div>
                        </div>
                    </div>
                </div>
                <div class='mt24 color3 font14'><?php echo Yii::t("referral", "Suspention Schedule"); ?></div>
                <div class='flex mt12 align-items'>
                    <span class='mr10 color6 font14'><?php echo Yii::t("labels", "Date"); ?></span>
                    <el-date-picker
                        :disabled='followType=="edit"?false:true'
                        v-model="followData.date"
                        type="daterange"
                        placement="bottom-start"
                        range-separator="至"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                        start-placeholder="<?php echo Yii::t("campus", "Start Date"); ?>"
                        end-placeholder="<?php echo Yii::t("campus", "End Date"); ?>">
                    </el-date-picker>
                </div>
                <div class='flex mt12 align-items'>
                    <span class='mr10 color6 font14'><?php echo Yii::t("labels", "Time"); ?></span>
                    <el-time-picker
                        is-range
                        :disabled='followType=="edit"?false:true'

                        v-model="followData.time"
                        range-separator="至"
                        value-format="HH:mm"
                        format="HH:mm"
                        placement="bottom-start"
                        start-placeholder="<?php echo Yii::t("campus", "Start Date"); ?>"
                        end-placeholder="<?php echo Yii::t("campus", "End Date"); ?>">
                    </el-time-picker>
                </div>
                <div class='mt24 color3 font14'><?php echo Yii::t("attends", "Memo"); ?></div>
                <div  class='mt8'><textarea class="form-control" placeholder="<?php echo Yii::t("attends", "Memo"); ?>" v-model='followData.remark' rows="3"  :disabled='followType=="edit"?false:true'></textarea></div>

            </div>
            <div class="modal-footer" v-if='followType=="edit"'>
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                <button type="button" class="btn btn-primary" @click='saveFollow()'><?php echo Yii::t("global", "Save"); ?></button>
            </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
    <!-- 认定记录 -->
    <div class="modal fade" id="affirmRecordListModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("referral", "Behavior Submission Entries"); ?></h4>
                </div>
                <div class="modal-body p24" >
                    <div class="media pb10" v-if='affirmData.id'>
                        <div class="media-left pull-left media-middle relative">
                            <img :src="contentList.student_info_list[affirmData.id].avatar" data-holder-rendered="true" class="avatar">
                        </div> 
                        <div class="pull-left media-middle">
                            <div class="mt4 color3">{{contentList.student_info_list[affirmData.id].name}} 
                                <span v-if='affirmData.tag==2 || affirmData.tag==3' class='glyphicon glyphicon-bookmark font12' :class='affirmData.tag==2?"waitingColor":affirmData.tag==3?"colorRed":""'></span>
                            </div> 
                            <div class="color6 font12">{{contentList.student_info_list[affirmData.id].className}}</div>
                        </div>
                    </div>
                    <div v-for='(item,idx) in affirmRecordList.list' class='bgGrey presenter mt12 relative font14'>
                        <span class='NOID'>No.{{item.id}}</span>
                        
                        <div class='flex mt12'>
                            <span class='color6 font14'><?php echo Yii::t("referral", "Decision"); ?>：</span>
                            <div class='flex1 color3'><span class="label " :class='item.result.identified==1?"label-danger":"label-success"'>{{showTitle(item.result.identified,'fact')}}</span></div>
                        </div>
                        <div class='flex mt12'>
                            <span class='color6 font14'><?php echo Yii::t("referral", "Time in Office"); ?>：</span>
                            <div class='flex1 color3'>{{item.result.duration}}</div>
                        </div>
                        <div class='flex mt12'>
                            <span class='color6 font14'><?php echo Yii::t("referral", "Final Decision"); ?>：</span>
                            <div class='flex1 color3'>
                                <span class='mr10' v-for='(list,index) in item.result.actions'>{{showTitle(list,'actions')}}</span>
                                <span class='btn-link' v-if='item.followup.date'>{{item.followup.date[0]}} <span class='el-icon-arrow-right'></span></span>
                            </div>
                        </div>
                        <div class='flex mt12'>
                            <span class='color6 font14'><?php echo Yii::t("referral", "Contact parents"); ?>：</span>
                            <div class='flex1 color3'><span class='mr10' v-for='(list,index) in item.result.contact_parents'>{{showTitle(list,'contact_parents')}}</span></div>
                        </div>
                        <div class='flex mt12'>
                            <span class='color6 font14'><?php echo Yii::t("referral", "Comment"); ?>：</span>
                            <div class='flex1 color3' v-html='htmlBr(item.result.remark)'></div>
                        </div>
                        <div class='flex mt12' v-if='item.result.attachments.img || item.result.attachments.other'>
                            <span class='color6 font14'><?php echo Yii::t("curriculum", "Attachments"); ?>：</span>
                            <div class='flex1 color3'>
                                <div class=''>
                                    <ul class='mb12 imgLi'  id='record'  v-if='item.result.attachments.img'>
                                        <li v-for='(list,i) in item.result.attachments.img'>
                                            <img :src="affirmRecordList.attachmentList[list].file_key" class='imgList mb8 mr8' @click='showImg("record",item.result.attachments.img)' alt=""  >
                                        </li>
                                    </ul>
                                </div>
                                <div class='mt12 color3' v-if='item.result.attachments.other'>
                                    <div v-for='(list,j) in item.result.attachments.other'>
                                        <div class='flex fileLink' >
                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/PDF1.png' ?>" alt="" class="pdfImg" v-if='affirmRecordList.attachmentList[list].mime_type=="application/pdf"' />
                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/excel.png' ?>" alt="" class="pdfImg" v-else-if='affirmRecordList.attachmentList[list].mime_type=="application/vnd.ms-excel" || affirmRecordList.attachmentList[list].mime_type=="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"' />
                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/word1.png' ?>" alt="" class="pdfImg" v-else-if='affirmRecordList.attachmentList[list].mime_type=="application/msword" || affirmRecordList.attachmentList[list].mime_type=="application/vnd.openxmlformats-officedocument.wordprocessingml.document"' />
                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/zip.png' ?>" alt="" class="pdfImg" v-else-if='affirmRecordList.attachmentList[list].mime_type=="application/x-zip-compressed"' />
                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" v-else /> 
                                            <a class='flex1 ml5' target= "_blank" :href="list.url">{{affirmRecordList.attachmentList[list].title}}
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class='flex mt12 align-items '>
                            <span class='color6 font14'><?php echo Yii::t("referral", "By"); ?>：</span>
                            <div class='flex1 color3'>
                                <div class='flex align-items '>
                                    <img :src="affirmRecordList.staff_info[item.result.created_by].photoUrl" data-holder-rendered="true" class="avatar32">
                                    <span class='ml10'>{{affirmRecordList.staff_info[item.result.created_by].name}}</span>
                                </div>
                            </div>
                        </div>
                        <div class='flex mt12'>
                            <span class='color6 font14'><?php echo Yii::t("ptc", "Time"); ?>：</span>
                            <div class='flex1 color3'>{{item.result.created_at}}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="delModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("global", "Delete"); ?></h4>
                </div>
                <div class="modal-body p24" >
                  <?php echo Yii::t("directMessage", "Proceed to remove?"); ?>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" @click='delList("del")'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="closeModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "Warning"); ?></h4>
                </div>
                <div class="modal-body p24" >
                <?php echo Yii::t("referral", "Discard unsaved data?"); ?>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" @click='closeForm("close")'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 发通知记录 -->
    <div class="modal fade"  id="allSendLogModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel"  >
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title"><?php echo Yii::t("referral", "Share To"); ?></h4>
                </div>
                <div class="modal-body p24" v-if='sendLogList' style='min-height:400px'>
                    <div>
                        <div class="alert alert-warning" role="alert" style='padding:10 15px'><span class='glyphicon glyphicon-exclamation-sign mr5'></span><?php echo Yii::t("referral", "Staff whom shared to will be added as collaborator"); ?></div>
                        <div  class='flex mt16 mb16'>
                            <el-select
                                    class='flex1'
                                    v-model="sendTeacherUid"
                                    filterable
                                    remote
                                    multiple 
                                    size='small'
                                    clearable
                                    @visible-change="loseFocus"
                                    class='inline-input flex1 formControl'
                                    placeholder="<?php echo Yii::t("directMessage", "Staff Name"); ?>"
                                    :remote-method="sendRemoteMethod"
                                    prefix-icon="el-icon-search"
                                    :reserve-keyword='false'
                                    @change='senTeacherOption'
                                    :loading="loading">
                                <el-option
                                        v-for="item in sendTecherOptions"
                                        :key="item.uid"
                                        :label="item.name"
                                        class='optionSearch mb8'
                                        :value="item.uid">
                                    <div class="media">
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                                <img :src="item.photoUrl" data-holder-rendered="true" class="media-object img-circle avatar">
                                            </a>
                                        </div>
                                        <div class="media-body mt5 media-middle">
                                            <h4 class="media-heading font14 color3 text_overflow">{{item.name}}</h4>
                                            <div class="text-muted text_overflow font12">{{ item.hrPosition }}</div>
                                        </div>
                                    </div>
                                </el-option>
                            </el-select>
                            <button type="button" class="btn btn-primary ml16"  :disabled='sendTeacherUid.length==0?true:false' @click='sendConfirmTeacher'><?php echo Yii::t("global", "Send"); ?></button>
                        </div>
                    </div>
                    <div class='mt24' v-if='contentList.behavior && contentList.behavior.send_log.length!=0'>
                        <div class='font14 color3 mb8 fontBold'><?php echo Yii::t("referral", "Share Logs"); ?></div>
                        <div class='pb24 relative overflow' v-for='(item,id) in contentList.behavior.send_log'>
                            <div class='mb16'><span class='round'></span><span class='font14 color3 ml8'>{{item.created_at}}</span></div>
                            <span class='roundSolid'></span>
                            <div style='margin-left:28px'>
                                <div class='bgGrey '>
                                    <span class='mr12 mb8 mt8' v-for='(list,index) in item.teacher_ids'><span class='color6 font14'>{{contentList.staff_info_list[list].name}}</span></span>
                                </div>
                            </div>
                        </div>
                    </div>
                
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
    <!-- 发通知记录 -->
    <div class="modal fade"  id="sendConfirmModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel"  >
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title"><?php echo Yii::t("ptc", "Warning"); ?></h4>
                </div>
                <div class="modal-body p24" v-if='sendLogList'>
                <div v-if='saveSubmit.purpose==1'>
                    <div class='font14 color3 mb16'><?php echo Yii::t("referral", "Notification will be sending to:"); ?></div> 
                    <div v-if='saveSubmit.teacher_ids && saveSubmit.teacher_ids.length>0'>
                        <div class='mt20 font14 color6 fontBold mb10'><?php echo Yii::t("referral", "Collaborators"); ?></div>
                        <div v-for='(list,key,index) in classTeacher' >
                            <div v-if='list.value.length>0' class='mb5 color6'>
                                <div>{{list.title}}：</div>
                                <div><span v-for='(item,idx) in list.value'>{{list.data[item].name}} <span v-if='idx+1<list.value.length'>、</span></span></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-else>                
                    <div class='font14 color3 mb16'><?php echo Yii::t("referral", "Notification will be sending to:"); ?></div> 
                    <div v-if='saveSubmit.teacher_ids && saveSubmit.teacher_ids.length>0'>
                        <div class='mt20 font14 color6 fontBold mb10'><?php echo Yii::t("referral", "Collaborators"); ?></div>
                        <div v-for='(list,key,index) in classTeacher' >
                            <div v-if='list.value.length>0' class='mb5 color6'>
                                <div>{{list.title}}：</div>
                                <div><span v-for='(item,idx) in list.value'>{{list.data[item].name}} <span v-if='idx+1<list.value.length'>、</span></span></div>
                            </div>
                        </div>
                    </div>
                    <div class='mt16 color6' v-if='Object.values(nextNode).length>0'>
                        <div class='mt20 font14 color6 fontBold mb5'><?php echo Yii::t("referral", "Follow up actions"); ?></div>
                        <span v-for='(list,key,index) in nextNode'>
                            <span>{{list.name}} <span v-if='index+1<Object.values(nextNode).length'>、</span></span>
                        </span>
                    </div>
                </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" @click='saveForm()'><?php echo Yii::t("global", "Submit"); ?></button>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
</div>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    var height=document.documentElement.clientHeight;
    var config = <?php echo CJSON::encode($config)?>;   
    var id_num = <?php echo CJSON::encode($id_num)?>;
    var branchId = '<?php echo $_GET['branchId'] ?>';
    var container = new Vue({
        el: "#container",
        data: {
            branchId:branchId,
            search:'',
            height:height-220,
            config:config,
            addChildType:'',
            itemList:[],
            dataList:{},
            classList:[],
            classId:'',
            childSelected:[],
            applyChild:{},
            searchText:'',
            teacherUid:{},
            teacherSelected:[],
            options:[],
            loading:false,
            searchChildList:[],
            location_other:false,
            problem_behavior_other:false,
            motivation_other:false,
            intervene_other:false,
            saveSubmit:{
                child_id:[],
                purpose:'',
                desc:'',
                date: "",
	            time: "",
                location:[],
                location_other:'',
                violated:[],
                problem_behavior:[],
                problem_behavior_other:'',
                motivation:[],
                motivation_other:'',
                intervene:[],
                intervene_other:'',
                involved:{
                    peers:[],
                    staff:[],
                    other:''
                },
                attachments:{
                    img:[],
                    other:[]
                },
                subtype:'',
                subject:''
            },
            attachments:{
                img:[],
                other:[]
            },
            contentList:{},
            token:'',
            detail:{},
            order: "descending",
            orderProp:"created_at",
            pageNum:1,
            editId:'',
            token:'',
            loadingType:0,
            loadingList:[],
            uploader:[],
            attachmentName:'',
            classTeacher:{},
            checkall:false,
            allTeahcerList:[],
            appendComment:'',
            showClassTeacher:false,
            followItem:{},
            followType:'',
            followData:{},
            affirmData:{},
            affirmRecordList:{},
            purposeList:[],
            delData:{},
            confirmChild:[],
            searchChildList:[],
            searchList:id_num,
            indexLoading:false,
            locationList:[],
            sendTecherOptions:[],
            sendTeacherUid:[],
            sendLogList:{},
            idEdit:false,
            subjectList:[],
            subtypeList:[],
            purposeListCopy:[],
            nextNode:{},
            CopyPages:{},
            showMinWidth:'200',
            shouldShowExpandButton:{},
            showHtml:true,
            behavioral_sub:[]
        },
        watch:{
            searchText(old,newValue){
                if(old!=''){
                    this.searchChild()
                }
            },
            "saveSubmit.subtype":{
                deep:true,
                handler(newVal){
                    this.getSubtype(newVal)
                }
            },
            'dataList': {
                handler(newVal) {
                if (newVal && newVal.list) {
                    this.$nextTick(() => {
                        newVal.list.forEach(key => {
                            this.getHeight(key._id);
                        });
                    });
                }
                },
                deep: true
            }
        },
        created: function() {
            this.getClass()
            this.getAll('0')
        },
        computed: {
            textStyleObject () {
                return {
                    'max-height': '6.7em'
                }
            }
        },
        methods: {
            getSubtype(newVal){
                if(newVal=='BEHAVIORAL'){
                    if(this.purposeListCopy.findIndex(item=>item.value===5)!=-1){
                        this.purposeList.splice(this.purposeListCopy.findIndex(item=>item.value===5),1)
                        if(this.saveSubmit.purpose==5){
                            this.saveSubmit.purpose=''
                        }
                    }
                }else{
                    this.purposeList=JSON.parse(JSON.stringify(this.purposeListCopy))
                }
            },
            getHeight(id) {
                return new Promise((resolve) => {
                    this.$nextTick(() => {
                        const element = $(`.${id}`);
                        const span = $(`.span_${id}`);
                        const flag = element.height() >= 80 && span.height()>80;
                        resolve(flag);
                    });
                    }).then(flag => {
                    this.showHtml=false
                    this.$nextTick(() => { 
                        this.showHtml=true
                        this.shouldShowExpandButton[id] = flag;
                    });
                });
            },
            htmlBr(content){
                if(content!=null){
                    var reg=new RegExp("\n","g");
                    content= content.replace(reg,"<br/>");
                }
                return content
            },
            showImg(id,list){
                var viewer = new Viewer(document.getElementById(id),{
                    fullscreen: false,
                    title:false,
                    scalable:false,
                    show:function(){ 
                        if(list.length==1){
                            $('.viewer-prev').hide()
                            $('.viewer-next').hide()
                        }
                    },
                    hide:function(){ 
                        viewer.destroy()
                    }
                });
                $("#"+id).click();
            },
            selectTeacher(id){
                var len=[]
                for(var keys in this.classTeacher[id].data){
                    len.push(parseInt(keys))                         
                }
                if(len.length>this.classTeacher[id].value.length){
                    this.classTeacher[id].checkall=false
                }
                if(len.length==this.classTeacher[id].value.length){
                    this.classTeacher[id].checkall=true
                }
            },
            allTeacher(id){
                this.classTeacher[id].value=[]
                if(this.classTeacher[id].checkall){
                    for(var keys in this.classTeacher[id].data){
                        this.classTeacher[id].value.push(parseInt(keys))                         
                    }
                }
            },
            followupNode(gradeList,child_id){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("followupNode") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        group:gradeList,
                        child_id:child_id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            data.data.node.forEach(item => {
                                item.disabled=false
                            });
                            that.purposeList=data.data.node
                            that.purposeListCopy = JSON.parse(JSON.stringify(data.data.node)); // 拷贝原数据, 深拷贝
                            that.locationList=data.data.locations
                            that.subjectList=data.data.subject
                            that.subtypeList=data.data.subtype
                            that.behavioral_sub=data.data.behavioral_sub
                            if(that.behavioral_sub.length && that.saveSubmit.behavioral_sub && that.saveSubmit.behavioral_sub!='' && that.saveSubmit.behavioral_sub!=null){
                                let sub = that.behavioral_sub.filter((i) => i.value == that.saveSubmit.behavioral_sub)
                                that.behavioralSub(sub[0])
                            }
                            if(that.saveSubmit.subtype && that.saveSubmit.subtype!=''){
                                that.getSubtype(that.saveSubmit.subtype)
                            }
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            behavioralSubtype(){
                this.purposeList.forEach(item => {
                    item.disabled=false
                });
                this.behavioral_sub.forEach(item => {
                    item.disabled=false
                });
                this.saveSubmit.purpose=''
                this.saveSubmit.subject=''
                if(this.behavioral_sub.length>0){
                    this.saveSubmit.behavioral_sub=[]
                }
            },
            behavioralSub(list){
                if(this.saveSubmit.behavioral_sub.length==0){
                    this.purposeList.forEach(item => {
                        item.disabled=false
                    });
                    this.behavioral_sub.forEach(item => {
                        item.disabled=false
                    });
                }else{
                    this.purposeList.forEach(item => {
                        if(item.name==list.node){
                            this.saveSubmit.purpose=item.value
                        }else{
                            item.disabled=true
                        }
                    });
                }
                if(this.saveSubmit.behavioral_sub.length==1){
                    this.behavioral_sub.forEach(item => {
                        if(item.value!=this.saveSubmit.behavioral_sub[0]){
                            item.disabled=true
                        }
                    });
                }
            },
            getTeacher(teacherList){
                let that=this
                var classids=[]
                var gradeList=[]
                var childId=[]
                if(!teacherList){
                    this.confirmChild=this.childSelected
                }
                if(this.confirmChild.length==0){
                    return
                }
                this.confirmChild.forEach(item => {
                    classids.push(item.class_id)
                    gradeList.push(item.grade)
                    childId.push(item.id)
                });
            
                gradeList=Array.from(new Set(gradeList));
                if(gradeList.length>1){
                    resultTip({
                        error: 'warning',
                        msg: '<?php echo Yii::t("referral", "Students from different divisions should be submitted in different entries."); ?>'
                    });
                    return
                }
                this.followupNode(gradeList[0],childId)
                classids=Array.from(new Set(classids));
                if(classids.length==0){
                    return
                }
                that.showClassTeacher=false
                $.ajax({
                    url: '<?php echo $this->createUrl("getClassTeacherList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        class_id:classids
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            var list={}
                            for(var key in data.data){
                                list[key]={}
                                list[key].data=data.data[key]
                                delete list[key].data[that.config.user_data.uid]
                                if(teacherList && teacherList.length!=0){
                                    teacherList.forEach(item => {
                                        if(item.class_id === key) list[key].value =item.teacher_ids
                                    });
                                    that.showClassTeacher=true
                                }else{
                                    that.showClassTeacher=false
                                    list[key].value=[]
                                }
                                list[key].checkall=false
                                list[key].title=that.classList.find(item2 =>item2.classid === key).title                               
                            }
                            that.classTeacher=list
                            $('#addClassModal').modal('hide')

                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            showTitle(data,type){
                for(var i=0;i<this.config[type].length;i++){
                    if(this.config[type][i].value==data){
                        return this.config[type][i].title
                    }
                }
            },
            sort_change(column){
                this.order=column.order
                this.orderProp=column.prop
                this.getAll()
            },
            initPage(){
                var _this = this;
                _this.CopyPages=JSON.parse(JSON.stringify(_this.pages))
                if(_this.CopyPages.count_page>=10){
                    _this.pages.count=[1,2,3,4,5,6,7,8,9,10]
               }else{
                    var numPage=[]
                    for(var i=1;i<=_this.CopyPages.count_page;i++){
                        numPage.push(i)
                    }
                    _this.pages.count=numPage
               }
            },
            pagesSize(){
                var _this = this;
                if(_this.pageNum<10){
                    if(_this.CopyPages.count_page>=10){
                        _this.pages.count=[1,2,3,4,5,6,7,8,9,10]
                    }else{
                        var numPage=[]
                        for(var i=1;i<=_this.CopyPages.count_page;i++){
                            numPage.push(i)
                        }
                        _this.pages.count=numPage
                    }
                }else if(_this.pageNum<=_this.CopyPages.count_page){
                    if(_this.CopyPages.count_page-_this.pageNum>=4){
                        var minPage=_this.pageNum-5
                        var maxPage=_this.pageNum+4
                    }else{
                        var minPage=_this.pageNum-(9-((_this.CopyPages.count_page-_this.pageNum)))
                        var maxPage=_this.pageNum+(_this.CopyPages.count_page-_this.pageNum)
                    }
                    var numPage=[]
                    for(var i=minPage;i<=maxPage;i++){
                        numPage.push(i)
                    }
                    _this.pages.count=numPage
                }
            },
            prev(index) {
                this.pageNum = Number(index) - 1
                this.pagesSize()
                this.getAll()
            },
            plus(index) {
                this.pageNum = Number(index)
                this.pagesSize()
                this.getAll()
            },
            next(index){
                this.pageNum = Number(index) + 1
                this.pagesSize()
                this.getAll()
            },
            getAll(num){
                if(num=='-1'){
                    this.dataList.list=[]
                    this.indexLoading=false
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getMyList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        page:this.pageNum,
                        order: this.order,
                        orderProp:this.orderProp,
                        child_ids:this.searchChildList,
                        name:this.searchList,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            let maxNodesOrderLength = 0;
                            data.data.list.forEach(item => {
                                item.more=true
                                if (item.nodes_order.length > maxNodesOrderLength) {
                                    maxNodesOrderLength = item.nodes_order.length;
                                }
                            });
                            that.showMinWidth=maxNodesOrderLength*110
                            that.dataList=data.data
                            that.itemList=data.data.meta
                            that.indexLoading=false
                            if(num){
                                that.pages=data.data.meta
                                that.CopyPages=data.data.meta
                                that.initPage()
                            }
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            searchChild(){
                var childId=[]
                if(this.childSelected!=null){
                    for(var i=0;i<this.childSelected.length;i++){
                        childId.push(this.childSelected[i].id+'')
                    }
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("studentSearch") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        child_name:this.searchText
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            for(var i=0;i<data.data.length;i++){
                                data.data[i].loading=true
                                if (childId.indexOf(data.data[i].id+'')!=-1) {
                                    data.data[i].disabled=true
                                }else{
                                    data.data[i].disabled=false
                                }
                            }
                            that.searchChildList=data.data
                            that.$forceUpdate()
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            getClass(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("classList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            data.data.forEach(item => {
                                item.childData=[]
                            })
                            that.classList=data.data

                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            getQiniu(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getQiniuToken") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        linkId:that.editId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.token=data.data
                            that.$nextTick(()=>{
                                config['token'] = data.data;
                                config['browse_button'] ='addFile'
                                var uploader = new plupload.Uploader(config);
                                that.uploader.push(uploader);
                                uploader.init();
                            })
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            addApply(){
                let that=this
                this.location_other=false
                this.problem_behavior_other=false
                this.motivation_other=false
                this.intervene_other=false
                this.searchChildList=[]
                this.classTeacher={}
                this.classList.forEach(item => {
                    item.childData=[]
                })
                this.classId=''
                var data = new Date()
                var Da = new Date(data.getTime() + 24 * 60 * 60 * 1000 * 30)
                var y = Da.getFullYear()
                var m = Da.getMonth()
                var d = Da.getDate()
                var H = Da.getHours()
                var mm = Da.getMinutes()
                var ss = Da.getSeconds()
                m = m < 10 ? "0" + m : m
                d = d < 10 ? "0" + d : d
                H = H < 10 ? "0" + H : H
                this.saveSubmit={
                    child_id:[],
                    purpose:'',
                    desc:'',
                    date:y + "-" + m + "-" + d ,
                    time: H + ":" + mm ,
                    location:[],
                    location_other:'',
                    violated:[],
                    problem_behavior:[],
                    problem_behavior_other:'',
                    motivation:[],
                    motivation_other:'',
                    intervene:[],
                    intervene_other:'',
                    attachments:{
                        img:[],
                        other:[]
                    },
                    teacher_ids:[],
                    subtype:'',
                    subject:'',
                    behavioral_sub:[]
                }
                this.attachments={
                    img:[],
                    other:[]
                }
                this.applyChild={}
                this.childSelected=[]
                this.confirmChild=[]
                this.teacherSelected=[]
                this.editId=''
                this.idEdit=false
                if(that.uploader.length!=0) {
                    for(var i=0;i<that.uploader.length;i++){
                    that.uploader[i].destroy();
                    }
                }                
                $.ajax({
                    url: '<?php echo $this->createUrl("getBehaviorNewId") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.editId=data.data
                            that.getQiniu()
                            $('#newApplyModal').modal('show')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            showDetails(list){
                this.affirmData={}
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getBehaviorDetail") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:list._id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.contentList=data.data
                            that.detail=data.data.behavior.detail
                            $('#contentModal').modal('show')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.loading = false;
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.loading = false;
                    },
                })
            },
            confirmTeacher(){
                this.options.forEach(item => {
                    if(item.uid==this.teacherUid){
                        this.teacherSelected.push(item)
                    }
                });
            },
            delManager(list,index){
                this.teacherSelected.splice(index,1)
            },
            remoteMethod(query) {
                let that=this
                if (query !== '') {
                    this.loading = true;
                    $.ajax({
                        url: '<?php echo $this->createUrl("teacherSearch") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            teacher_name:query
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.options = Object.values(data.data) ;
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                            that.loading = false;
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.loading = false;
                        },
                    })
                } else {
                    this.options = [];
                }
            },
            addChild(type){
                this.addChildType=type
                this.searchChildList=[]
                this.searchText=''
                this.classId=''
                this.childSelected=JSON.parse(JSON.stringify(this.confirmChild));
                $('#addClassModal').modal('show')
            },
            selectChild(list){
                this.applyChild=list
                $('#addClassModal').modal('hide')
            },
            getChild(list){
                let that=this
                if(that.classId==list.classid){
                    that.classId=''
                    return
                }
                that.classId=list.classid
                var childId=[]
                if(this.childSelected!=null){
                    for(var i=0;i<this.childSelected.length;i++){
                        childId.push(this.childSelected[i].id)
                    }
                }
                for(var i=0;i<that.classList.length;i++){
                    if(that.classList[i].classid==list.classid){
                        if(that.classList[i].childData.length!=0){
                            for(var j=0;j<that.classList[i].childData.length;j++){
                                if (childId.indexOf(that.classList[i].childData[j].id)!=-1) {
                                    that.classList[i].childData[j].disabled=true
                                }else{
                                    that.classList[i].childData[j].disabled=false
                                }
                            }
                            that.$forceUpdate()
                            return
                        }
                    }
                }
                that.childLoading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("childList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        classId:list.classid
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            for(var i=0;i<data.data.length;i++){
                                data.data[i].classTitle=list.title
                                data.data[i].stuLoading=true
                                data.data[i].loading=true
                                if (childId.indexOf(data.data[i].id)!=-1) {
                                    data.data[i].disabled=true
                                }else{
                                    data.data[i].disabled=false
                                }
                            }
                            that.sortData(data.data)
                            for(var i=0;i<that.classList.length;i++){
                                if(that.classList[i].classid==list.classid){
                                    that.classList[i].childData=data.data
                                }
                            }
                            that.childLoading=false
                            that.$forceUpdate()
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            sortData(list){
                <?php if (Yii::app()->language == 'zh_cn') { ?>
                    list.sort((a, b)=> a.name.localeCompare(b.name, 'zh'));
                <?php } else { ?>
                    list.sort((a, b) => a.name.charCodeAt(0) - b.name.charCodeAt(0));
                <?php } ?>
            },
            selectAll(list,index){
                for(var i=0;i<list.childData.length;i++){
                    if (!list.childData[i].disabled) {
                        this.childSelected.push(list.childData[i])
                        Vue.set(this.classList[index].childData[i], 'disabled', true);
                    }
                }
                this.$forceUpdate()
            },
            assignChildren(list,index,idx){
                if(idx=='search'){
                    this.searchChildList[index].disabled=true
                }else{
                    Vue.set(this.classList[index].childData[idx], 'disabled', true);
                }
                this.$forceUpdate()
                this.childSelected.push(list)
            },
            Unassign(data,index,type){
                for(var i=0;i<this.classList.length;i++){
                    for(var j=0;j<this.classList[i].childData.length;j++){
                        if(data.id==this.classList[i].childData[j].id){
                            Vue.set(this.classList[i].childData[j], 'disabled', false);
                        }
                    }
                }
                for(var i=0;i<this.searchChildList.length;i++){
                    if(data.id==this.searchChildList[i].id){
                        Vue.set(this.searchChildList[i], 'disabled', false);
                    }
                }
                this.$forceUpdate()
                this.childSelected.splice(index,1)
                if(type){
                    this.getTeacher()
                }
                
            },
            batchDel(){
                this.childSelected=[]
                for(var i=0;i<this.classList.length;i++){
                    for(var j=0;j<this.classList[i].childData.length;j++){
                        this.classList[i].childData[j].disabled=false
                    }                        
                }                
                this.$forceUpdate() 
            },
            showIndexOf(type){
                if(this.saveSubmit[type]){
                    if(this.saveSubmit[type].indexOf("OTHER")!=-1){
                        return true
                    }else{
                        return false
                    }
                }else{
                    return false
                }
               
            },
            closeForm(type){
                if(this.editId!=''){
                    $('#newApplyModal').modal('hide')
                    return
                }
                if(type){
                    $('#closeModal').modal('hide')
                    $('#newApplyModal').modal('hide')
                    return
                }
                if(this.confirmChild.length>0 || this.saveSubmit.purpose!='' || this.saveSubmit.location.length>0 || this.saveSubmit.desc!='' || this.saveSubmit.violated.length>0 || this.saveSubmit.problem_behavior.length>0 || this.saveSubmit.intervene.length>0 || this.saveSubmit.motivation.length>0){
                    $('#closeModal').modal('show')
                }else{
                    $('#newApplyModal').modal('hide')
                }
            },
            saveForm(type){
                if(this.saveSubmit.subtype =='POSITIVEBEHAVIOR'){
                    this.saveSubmit.purpose = 1;
                }
                if(this.confirmChild.length==0){
                    resultTip({
                        error: 'warning',
                        msg:'<?php echo Yii::t("referral", "Student"); ?>'
                    });
                    return
                }
                if(this.subtypeList.length>0){
                    if(this.saveSubmit.subtype!="ACADEMIC"){
                        this.saveSubmit.subject=''
                    }
                    if(this.config.subtype && (!this.saveSubmit.subtype || this.saveSubmit.subtype=='')){
                        resultTip({
                            error: 'warning',
                            msg:'<?php echo Yii::t("newDS", "Please select"); ?> <?php echo Yii::t("referral", "Type"); ?>'
                        });
                        return
                    }
                }
                // if(this.behavioral_sub.length>0 && this.saveSubmit.subtype=="BEHAVIORAL"){
                //     if(this.saveSubmit.behavioral_sub==''){
                //         resultTip({
                //             error: 'warning',
                //             msg:'<?php echo Yii::t("newDS", "Please select"); ?> <?php echo Yii::t("referral", "专项整治"); ?>'
                //         });
                //         return
                //     }
                // }
                if(this.saveSubmit.purpose==5 && this.saveSubmit.subject==''){
                    resultTip({
                        error: 'warning',
                        msg:'<?php echo Yii::t("newDS", "Please select"); ?> <?php echo Yii::t("application", "Subject"); ?>'
                    });
                    return
                }
                if(this.saveSubmit.purpose==''){
                    resultTip({
                        error: 'warning',
                        msg:'<?php echo Yii::t("newDS", "Please select"); ?> <?php echo Yii::t("referral", "Follow up actions"); ?>'
                    });
                    return
                }
                if(this.saveSubmit.desc==''){
                    resultTip({
                        error: 'warning',
                        msg:'<?php echo Yii::t("teaching", "Input"); ?> <?php echo Yii::t("referral", "Event Description"); ?>'
                    });
                    return
                }
                
                this.saveSubmit.child_id=[]
                this.saveSubmit.attachments={}
                this.saveSubmit.attachments.img=[]
                this.saveSubmit.attachments.other=[]
                this.saveSubmit.teacher_ids=[]
                for(var i=0;i<this.confirmChild.length;i++){
                    this.saveSubmit.child_id.push(this.confirmChild[i].id+'')
                }
                for(var i=0;i<this.attachments.img.length;i++){
                    this.saveSubmit.attachments.img.push(this.attachments.img[i]._id)
                }
                for(var i=0;i<this.attachments.other.length;i++){
                    this.saveSubmit.attachments.other.push(this.attachments.other[i]._id)
                }
                this.saveSubmit.assist=[]
                if(this.showClassTeacher){  
                    for(var key in this.classTeacher){
                        this.saveSubmit.assist.push({class_id:key,teacher_ids:this.classTeacher[key].value})
                        this.saveSubmit.teacher_ids.push(...this.classTeacher[key].value)
                    }
                }else{
                    delete this.saveSubmit.assist
                    this.saveSubmit.teacher_ids=[]
                }
                console.log(this.saveSubmit.behavioral_sub)
                if(type && this.saveSubmit.behavioral_sub.length>0){
                    this.saveSubmit.behavioral_sub=this.saveSubmit.behavioral_sub[0]
                }
                let that=this
                var data={}
                if(this.editId!=''){
                    data={
                        data:this.saveSubmit,
                        id:this.editId
                    }
                }else{
                    data={
                        data:this.saveSubmit,
                    }
                }
                if(type){
                    if(!(this.saveSubmit.purpose==1 && this.saveSubmit.teacher_ids.length==0)){
                       if(this.saveSubmit.purpose==1){
                            $('#sendConfirmModal').modal('show')
                        }else{
                            $.ajax({
                                url: '<?php echo $this->createUrl("getNodeTeacher") ?>',
                                type: "post",
                                dataType: 'json',
                                data:{
                                    "subject":this.saveSubmit.subject,
                                    "node":this.saveSubmit.purpose,
                                    "child_id": this.saveSubmit.child_id
                                } ,
                                success: function(data) {
                                    if (data.state == 'success') {
                                        that.nextNode=data.data
                                        $('#sendConfirmModal').modal('show')
                                    }else{
                                        resultTip({
                                            error: 'warning',
                                            msg: data.message
                                        });
                                    }
                                },
                                error: function(data) {
                                    resultTip({
                                        error: 'warning',
                                        msg: data.message
                                    });
                                },
                            })
                        }
                        return
                    }
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("saveBehaviorForm") ?>',
                    type: "post",
                    dataType: 'json',
                    data:data ,
                    success: function(data) {
                        if (data.state == 'success') {
                            $('#sendConfirmModal').modal('hide')
                            $('#newApplyModal').modal('hide')
                            that.getAll()
                            resultTip({
                                msg: data.message
                            });
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            editList(list){
                this.editId=list._id
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getBehaviorDetail") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:list._id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            if(data.data.behavior.behavioral_sub!=''){
                                data.data.behavior.detail.behavioral_sub=data.data.behavior.detail.behavioral_sub.split(" ")
                            }
                            that.saveSubmit=data.data.behavior.detail
                            that.confirmChild=[]
                            that.attachments.other=[]
                            that.attachments.img=[]
                            if(data.data.behavior.child.length!=0){
                                let peer=data.data.behavior.child
                                for(var i=0;i<peer.length;i++){
                                    that.confirmChild.push(data.data.student_info_list[peer[i].id])
                                }
                            }
                            var detailData=data.data.behavior.detail
                            if(detailData.attachments.img){
                                for(var i=0;i<detailData.attachments.img.length;i++){
                                    that.attachments.img.push(data.data.attachmentList[detailData.attachments.img[i]])
                                }
                            }
                            if(detailData.attachments.other){
                                for(var i=0;i<detailData.attachments.other.length;i++){
                                    that.attachments.other.push(data.data.attachmentList[detailData.attachments.other[i]])
                                }
                            }
                            if(data.data.behavior.detail.location_other!=null && data.data.behavior.detail.location_other!=''){
                                that.location_other=true
                            }
                            if(data.data.behavior.detail.problem_behavior_other!=null && data.data.behavior.detail.problem_behavior_other!=''){
                                that.problem_behavior_other=true
                            }
                            if(data.data.behavior.detail.intervene_other!=null && data.data.behavior.detail.intervene_other!=''){
                                that.intervene_other=true
                            }
                            if(data.data.behavior.detail.motivation_other!=null && data.data.behavior.detail.motivation_other!=''){
                                that.motivation_other=true
                            }
                            if(that.uploader.length!=0) {
                                for(var i=0;i<that.uploader.length;i++){
                                that.uploader[i].destroy();
                                }
                            }    
                            that.getQiniu()
                            that.idEdit=true
                            that.getTeacher(data.data.behavior.detail.assist)
                            $('#newApplyModal').modal('show')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.loading = false;
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.loading = false;
                    },
                })
            },
            saveComment(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("officeProcessAddComment") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        id:this.contentList.behavior._id,
                        comment:this.appendComment
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.appendText=false
                            resultTip({
                                msg: data.message
                            });
                            that.appendComment=''
                            that.showDetails(that.contentList.behavior)
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            delImg(type,list,index){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("deleteAttachment") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        attachmentId:list._id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            if(type=='img'){
                                that.attachments.img.forEach((item,index) => {
                                    if (item._id==list._id) {
                                        that.attachments.img.splice(index, 1)
                                    } 
                                })
                            }else{
                                that.attachments.other.forEach((item,index) => {
                                    if (item._id==list._id) {
                                        that.attachments.other.splice(index, 1)
                                    } 
                                })
                            }
                            resultTip({
                                msg:data.state
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            saveFile(list,index) {
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("updateAttachmentName") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        attachmentId:list._id,
                        attachmentName:this.attachmentName

                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.attachments.other.forEach((item,index) => {
                                if (item._id==list._id) {
                                    Vue.set(that.attachments.other[index], 'title',data.data.title);
                                    Vue.set(that.attachments.other[index], 'file_key', data.data.url);
                                    Vue.set(that.attachments.other[index], 'isEdit', false);
                                } 
                            })
                            resultTip({
                                msg:'<?php echo Yii::t("newDS", "Data Saved!");?>'
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            delList(list){
                if(list!='del'){
                    this.delData=list
                    $('#delModal').modal('show')
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("delBehavior") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        id:this.delData._id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            $('#delModal').modal('hide')
                            resultTip({
                                msg: data.message
                            });
                            that.getAll()
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            follow(data,type){
                this.followItem=data
                this.followType=type
                this.followData={
                    remark:this.followItem.followup.remark?this.followItem.followup.remark:'',
                    date:this.followItem.followup.date?this.followItem.followup.date:'',
                    time:this.followItem.followup.time?this.followItem.followup.time:''
                }
                $('#followModal').modal('show')
            },
            viewAffirm(list){
                let that=this
                if(list.affirm_num==0){
                    return
                }
                this.affirmData=list
                $.ajax({
                    url: '<?php echo $this->createUrl("affirmRecordList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        child_id:list.id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.affirmRecordList=data.data
                            $('#affirmRecordListModal').modal('show')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            allSendMessage(item){
                this.sendTecherOptions=[]
                $('#allSendLogModal').modal('show')
            },
            senTeacherOption(){
                this.sendTecherOptions=[]
            },
            sendConfirmTeacher(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("sendBehaviorMsgToTeacher") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        teacher_ids:this.sendTeacherUid,
                        id: this.contentList.behavior._id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.sendTecherOptions=[]
                            resultTip({
                                msg: data.message
                            });
                            that.showDetails(that.contentList.behavior)
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            loseFocus(val) {
                // 下拉框隐藏时
                if (!val) {
                this.sendTecherOptions=[]
                }
            },
            sendRemoteMethod(query) {
                let that=this
                if (query !== '') {
                    this.loading = true;
                    $.ajax({
                        url: '<?php echo $this->createUrl("teacherSearch") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            teacher_name:query,
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.sendTecherOptions = Object.values(data.data) ;
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                            that.loading = false;
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.loading = false;
                        },
                    })
                } else {
                    this.options = [];
                }
            },
        }
    })
    var config = {
        runtimes: 'html5,flash,html4', //上传模式,依次退化
        container: 'container', //上传区域DOM ID，默认是browser_button的父元素，
        browse_button: 'pickfilesPhoto', //上传选择的点选按钮，**必需**
        token: '',
        flash_swf_url: '/js/plupload/Moxie.swf', // flash的相对地址
        auto_start: true, //选择文件后自动上传，若关闭需要自己绑定事件触发上传
        // multi_selection: false, //不允许多选
        url:'https://upload-z1.qiniup.com',
        init: {
            'FilesAdded': function(up, files) {
                container.disabledUpload=true
                let fileType=files[0].type.split('/')
                if(fileType[0]=="image"){
                  container.loadingType=1
                }else{
                  container.loadingType=2
                }
                container.loadingList=files
                up.start();
            },
            BeforeUpload: function(up, file) {
                //设置参数
                up.setOption({
                    multipart_params:{token:container.token}
                })
                let fileType=file.type.split('/')
                if(fileType[0]=="image"){
                    container.attachments.img.push({types:'1'})
                }else{
                    container.attachments.other.push({types:'1',title:' <?php echo Yii::t("directMessage", "Uploading");?>'})  
                }
                container.loadingList.splice(0,1)
            },
            UploadProgress: function(up, file) {
                $('#progress').css('width', file.percent+'%').html(file.percent+'%');
            },
            'FileUploaded': function(up, file, info) {
                var response = eval('('+info.response+')');
                if(info.status == 200){
                    let fileType=response.data.mimetype.split('/')
                    if(fileType[0]=="image"){
                        container.attachments.img.splice(container.attachments.img.length-1,1)
                        container.attachments.img.push(response.data)
                    }else{
                        response.data.isEdit=false
                        container.attachments.other.splice(container.attachments.other.length-1,1)
                        container.attachments.other.push(response.data)
                    }
                }
            },
            'Error': function(up, err, errTip) {
                if(err.response){
                    var response = eval('('+err.response+')');
                }
                else{
                    var response = {message: err.message};
                }
                if(response.error == 'token not specified'){
                    up.stop();
                    $.ajax({
                        url: '<?php echo $this->createUrl("getQiniuToken") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            linkId: container.editId
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                container.token=data.data
                                config['uptoken'] = data.data;
                                up.setOption("multipart_params", {"token":data.data,})
                                up.start();
                            } else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.msg
                                });
                            }
                        },
                        error: function(data) {

                        },
                    })
                }
            },
            'UploadComplete': function(up, file) {
                //队列文件处理完毕后,处理相关的事情
            }
        }
    };
</script>
