<div id="itemOp-template" style="display: none;">
	<a href="javascript:void(0);" onclick="quickLink(this)" class="icon-tag">Q</a>
	<a href="javascript:void(0);" onclick="item_rotate(this)" class="icon-rotate">R</a>
	<a href="javascript:void(0);" onclick="childAvatar(this)" class="icon-avatar">P</a>
	<a href="javascript:void(0);" onclick="item_delete(this)" class="icon-delete">D</a>
</div>

<div id="upload-preview">
	<ul id="media-list"> <!--place holder-->
	</ul>
	<div class="cc"></div>
</div>
<div class="page-container">
	<?php $this->widget('CLinkPager', array('pages' => $pages, 'id'=>'media-pager')); ?>
</div>
<script>
mediaList = <?php echo CJSON::encode($mediaList);?>;
head.js('<?php echo Yii::app()->themeManager->baseUrl . '/base/js/teaching/mediamgt.js?t='.time(); ?>', function(){displayMediaList();});

$('ul.yiiPager > li > a').click(function(){
	$.ajax({
		type: 'GET',
		url: $(this).attr('href')
	}).done(function(html){
		$('#media-zone').html(html);
		return false;
	});
	return false;
});
</script>