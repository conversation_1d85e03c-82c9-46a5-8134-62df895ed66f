<?php

/**
 * This is the model class for table "ivy_teaching_monthly".
 *
 * The followings are the available columns in table 'ivy_teaching_monthly':
 * @property integer $id
 * @property string $schoolid
 * @property integer $classid
 * @property integer $yid
 * @property string $month
 * @property string $unit_type
 * @property integer $stat_cn
 * @property integer $stat_en
 * @property integer $timestamp
 * @property integer $uid
 */
class TeachingMonthly extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_teaching_monthly';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('schoolid, classid, yid, timestamp, uid', 'required'),
			array('classid, yid, stat_cn, stat_en, unit_type, timestamp, uid', 'numerical', 'integerOnly'=>true),
			array('schoolid, month', 'length', 'max'=>30),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, schoolid, classid, yid, unit_type, month, stat_cn, stat_en, timestamp, uid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'content' => array(self::HAS_ONE, 'TeachingMonthlyContent', 'id'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'schoolid' => 'Schoolid',
			'classid' => 'Classid',
			'yid' => 'Yid',
			'month' => 'Month',
			'unit_type' => 'unit Type',
			'stat_cn' => 'Stat Cn',
			'stat_en' => 'Stat En',
			'timestamp' => 'Timestamp',
			'uid' => 'Uid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('month',$this->month,true);
		$criteria->compare('unit_type',$this->unit_type);
		$criteria->compare('stat_cn',$this->stat_cn);
		$criteria->compare('stat_en',$this->stat_en);
		$criteria->compare('timestamp',$this->timestamp);
		$criteria->compare('uid',$this->uid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return TeachingMonthly the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public static function configType()
    {
        return array(
            1 => Yii::t("labels", "Unit 1 All About Me"),
            2 => Yii::t("labels", "Unit 2 Community Helpers"),
            3 => Yii::t("labels", "Unit 3 So Many Buildings"),
            4 => Yii::t("labels", "Unit 4 Stories Around the World"),
            5 => Yii::t("labels", "Unit 5 The World We Share"),
            6 => Yii::t("labels", "Unit 6 Frontiers"),
        );
    }
}
