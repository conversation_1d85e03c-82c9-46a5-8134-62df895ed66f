(function ($) {
	$.fn.formDialog=function(options){
        var link=$(this);
		link.live('click', function(e){
            var url = $(this).attr('href')
			e.preventDefault();
			$.ajax({
				'url': url,
				'dataType': 'json',
				'success': function(data){
					var dialog=$('<div style="display:none;"><div class="forView"></div></div>');			
					$('body').append(dialog);
					dialog.find('.forView').html(data.view || data.form);
                    dialog.dialog({ width:'auto', show:{effect:'blind',duration:500},hide:{effect:'blind',duration:500}  });
					
					dialog.delegate('form', 'submit', function(e){
						e.preventDefault();
//						$.ajax({
//							'url': url,
//							'type': 'post',
//							'data': $(this).serialize(),
//							'dataType': 'json',
//							'success': function(data){
//								if (data.status=='failure')
//									dialog.find('.forView').html(data.view || data.form);
//								else if (data.status=='success'){
//									dialog.dialog('close').detach();
//									options['onSuccess'](data, e);
//								}
//							}
//						});
                        $(this).ajaxSubmit({
                            dataType: 'json',
                            success: function(data) {
                                if (data.status=='failure')
									dialog.find('.forView').html(data.view || data.form);
								else if (data.status=='success'){
									dialog.dialog('close').detach();
									options['onSuccess'](data, e);
								}
                            }
                        });

					});
				}
			});
		});
	}
})(jQuery);	