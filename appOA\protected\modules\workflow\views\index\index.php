<link rel="stylesheet" type="text/css" href="<?php echo Yii::app()->themeManager->baseUrl.'/modern/css/wizard/bootstrap-nav-wizard.css' ?>" />
<?php
    $allDefind = WorkflowNode::model()->getAllNodesList();
?>
<script>
    var postUrl = '<?php echo Yii::app()->createUrl('//mcampus/classes/getChildrenList');?>';
</script>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Workspace'), array('//mcampus/default/index'))?></li>
        <li class="active"><?php echo Yii::t('site','Workflow Workspace')?></li>
    </ol>
    <div class="row">
        <div class="col-md-12">
            <!-- Split button -->
            <div class="btn-group">
                <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <?php echo Yii::t('workflow','Create Workflow'); ?> <span class="caret"></span>
                </button>
                <ul class="dropdown-menu">
                    <?php
                        if (!empty($allDefind)):
                           foreach ($allDefind as $defind):
                               if ($defind['obj']->display == 0) {
                                    continue;
                               }
                               $memo = $defind['obj']->defination_memo;
                               if ($memo && isset($branchList[$memo]) && $memo != $this->branchObj->branchid) {
                                    continue;
                               }
                               if ($this->branchObj->branchid != 'BJ_QFF' && $defind['obj']->defination_id == 2010) {
                                    continue;
                               }
                               if ($this->branchObj->type == $defind['obj']->branch_type && empty($defind['obj']->start_user_role) && $defind['obj']->status == 1):
                    ?>
                    <li><a href="<?php echo $this->createUrl('//workflow/entrance/index',array('definationId'=>$defind['obj']->defination_id,'branchId'=>$this->branchObj->branchid,'action'=>'initi'));?>" class="J_ajax" ><?php echo $defind['name'];?></a></li>
                    <?php
                                endif;
                            endforeach;
                        endif;
                    ?>
                </ul>
            </div>
            <div class="pull-right btn-group">
                <?php echo CHtml::link(Yii::t('payment', '工作流统计页面'), array('workflowCount', "branchId"=>Yii::app()->controller->branchId), array('class' => 'btn btn-success dropdown-toggle', "target" => "_black"));?>
            </div>
        </div>
    </div>
    <div class="row mt20">
        <div class="col-md-8">
            <?php $this->renderPartial('workflow.views.entrance.waitchecklist',array('currentCheckUser'=>$currentCheckUser,'nodesList'=>$nodesList,'branchList'=>$branchList,'usersList'=>$usersList));?>

            <?php $this->renderPartial('workflow.views.entrance.historychecklist',array('historyCheckUser'=>$historyCheckUser,'nodesList'=>$nodesList,'branchList'=>$branchList,'usersList'=>$usersList));?>

            <?php $this->renderPartial('workflow.views.entrance.processingList',array('processingList'=>$processingList,'nodesList'=>$nodesList,'branchList'=>$branchList,'usersList'=>$usersList));?>
        </div>
        <div class="col-md-4">
            <?php $this->renderPartial('workflow.views.entrance.selfworkflow',array('selfWorkflow'=>$selfWorkflow,'nodesList'=>$nodesList));?>

            <?php $this->renderPartial('workflow.views.entrance.endworkflow',array('endWorkflow'=>$endWorkflow,'nodesList'=>$nodesList));?>

            <?php $this->renderPartial('workflow.views.entrance.rejectworkflow',array('rejectWorkflow'=>$rejectWorkflow,'nodesList'=>$nodesList));?>
        </div>
    </div>
</div>
<?php
    $this->renderPartial('//layouts/common/branchSelectBottom');
?>
<div id="workflow-modal-template"></div>
<div id="workflow-modal-template2"></div>

<style>
    .table-contract th:first-child {
        width: 160px;
        text-align: right;
    }
    .table-contract td:first-child {
        width: 160px;
        text-align: right;
    }
</style>