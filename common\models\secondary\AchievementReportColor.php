<?php

/**
 * This is the model class for table "ivy_achievement_report_color".
 *
 * The followings are the available columns in table 'ivy_achievement_report_color':
 * @property integer $id
 * @property string $school_id
 * @property integer $yid
 * @property string $title_cn
 * @property string $title_en
 * @property string $data
 * @property integer $status
 * @property string $created_at
 * @property string $created_by
 * @property string $updated_at
 * @property string $updated_by
 */
class AchievementReportColor extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_achievement_report_color';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
            array('yid, school_id, title_cn, title_en, status, created_at, created_by, updated_at, updated_by', 'required'),
			array('yid, status', 'numerical', 'integerOnly'=>true),
			array('school_id, title_cn, title_en, created_at, created_by, updated_at, updated_by', 'length', 'max'=>255),
			array('data', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, school_id, yid, title_cn, title_en, data, status, created_at, created_by, updated_at, updated_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'school_id' => 'School',
			'yid' => 'Yid',
			'title_cn' => 'Title Cn',
			'title_en' => 'Title En',
			'data' => 'Data',
			'status' => 'Status',
			'created_at' => 'Created At',
			'created_by' => 'Created By',
			'updated_at' => 'Updated At',
			'updated_by' => 'Updated By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('school_id',$this->school_id,true);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('title_cn',$this->title_cn,true);
		$criteria->compare('title_en',$this->title_en,true);
		$criteria->compare('data',$this->data,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('created_at',$this->created_at,true);
		$criteria->compare('created_by',$this->created_by,true);
		$criteria->compare('updated_at',$this->updated_at,true);
		$criteria->compare('updated_by',$this->updated_by,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AchievementReportColor the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public static function getConfig()
    {
        return array(
            //颜色
            'coloe' => array(
                '#FF0000' => Yii::t('user', '红色'),
                '#FF7F00' => Yii::t('user', '橙色'),
                '#FFFF00' => Yii::t('user', '黄色'),
                '#00FF00' => Yii::t('user', '绿色'),
                '#00FFFF' => Yii::t('user', '青色'),
                '#0000FF' => Yii::t('user', '蓝色'),
                '#8B00FF' => Yii::t('user', '紫色'),
                '#000000' => Yii::t('user', '黑色'),
                '#FFFFFF' => Yii::t('user', '白色'),
            ),
        );
    }
}
