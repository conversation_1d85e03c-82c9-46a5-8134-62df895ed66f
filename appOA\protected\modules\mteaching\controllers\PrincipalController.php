<?php

class PrincipalController extends TeachBasedController
{
    public $selectedClassId=0;
    public $selectedChildId=0;

	public $printFW = array();

    // 访问action的初级权限
    public $actionAccessAuths = array(
        'updatereport'              => array('access'=>'o_MS_Rrport', 'admin'=>'o_MS_RrportMgt'),
        'delereport'                => 'o_MS_RrportMgt',
    );

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'teaching';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Teaching Tasks');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mteaching/principal/index');
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile($cs->getCoreScriptUrl().'/jui/js/jquery-ui-i18n.min.js');


        Yii::import('common.models.portfolio.*');
        Yii::import('common.models.secondary.*');
        Yii::import('common.models.calendar.*');
    }

    public function beforeAction($action){
        if(!parent::beforeAction($action)){
            return false;
        }

        $this->selectedClassId = Yii::app()->request->getParam('classid', 0);
        $this->selectedChildId = Yii::app()->request->getParam('childid', 0);

        return true;
    }

	public function actionIndex()
	{
        $schoolYear = CalendarSchool::model()->getSchoolCalender($this->branchId, 'desc');


        $model = New AchievementReport;

        $criteria = new CDbCriteria();
        $criteria->compare('schoolid', $this->branchId);
        $criteria->order = "start_time ASC";
        $items = AchievementReport::model()->findAll($criteria);

        $this->render('index', array(
            'model'=>$model,
            'items'=>$items,
            'schoolYear'=>$schoolYear,
        ));
	}

    /*
     * 修改一条校长的评语 根据周期ID
     */
    public  function actionUpdatereport()
    {
        $t = time();
        $id = Yii::app()->request->getParam('id', 0);

        $model = AchievementReport::model()->findByPk($id);
        if(!$model){
            $model = new AchievementReport;
        }

        if($_POST['AchievementReport']) {
//            var_dump($_POST['AchievementReport']);die;
            if(!$_POST['AchievementReport']['ms_principal']){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '校长不能为空');
                $this->showMessage();
            }
            if(!$_POST['AchievementReport']['msv_principal']){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '协调员不能为空');
                $this->showMessage();
            }
            if(!$_POST['AchievementReport']['fill_in_end_time'] || !$_POST['AchievementReport']['fill_in_end_time_h'] ||!$_POST['AchievementReport']['fill_in_end_time_i']){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '报告填写截止不能为空');
                $this->showMessage();
            }
            $teacher = array(
                "ms_principal" => $_POST['AchievementReport']['ms_principal'],
                "msv_principal" => $_POST['AchievementReport']['msv_principal'],
                /*"head_school" =>'8011431',
                "msv_principal" => '8014497',
                "myp_coordinator" => '8017616'*/
            );
            $_POST['AchievementReport']['fill_in_end_time'] = $_POST['AchievementReport']['fill_in_end_time']. ' '. $_POST['AchievementReport']['fill_in_end_time_h'].":".$_POST['AchievementReport']['fill_in_end_time_i'];
            $model->attributes = $_POST['AchievementReport'];
            $model->start_time = strtotime($model->start_time);
            $model->end_time = strtotime($model->end_time);
            $model->fill_in_end_time = strtotime($model->fill_in_end_time);
            $model->schoolid = $this->branchId;
            $model->leaders = json_encode($teacher);
            $model->status = 1;
            $model->uid = Yii::app()->user->id;
            $model->update_time = $t;
            $calendarModel = Calendar::model()->findByPk($model->calendar);
            if ($calendarModel) {
                $model->startyear = $calendarModel->startyear;
            }
            if($model->isNewRecord){
                $model->create_time = $t;
            }

            if ($model->save()) {
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message', 'success'));
                $this->addMessage('refresh', true);
            } else {
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err ? $err[0] : '失败');
            }
            $this->showMessage();
        }
        $fill_in_end_time = array('h'=>'','i'=>'');
        if(!$model->isNewRecord){
            $model->start_time = date('Y-m-d', $model->start_time);
            $model->end_time = date('Y-m-d', $model->end_time);
            if(!empty($model->fill_in_end_time)){
                $fill_in_end_time['h'] = date('H', $model->fill_in_end_time);
                $fill_in_end_time['i'] = date('i', $model->fill_in_end_time);
                $model->fill_in_end_time = date('Y-m-d', $model->fill_in_end_time);
            }else{
                $model->fill_in_end_time = '';
            }

        }

        if($model->leaders){
            $leader = json_decode($model->leaders,true);

            $model->ms_principal = $leader['ms_principal'];
            $model->msv_principal = $leader['msv_principal'];
        }

        /*
         * 226 中学校长
         * 151 总校长
         * 162 公共关系协调员
         * 172 英文协调员
         * 235 K-K-12体育协调员
         * 253 PYP 协调员
         * 254 中文协调员
         * 269 LEAP协调员
         * 323 MYP协调员
         * 459 MYP/DP协调员
         * 551 中学副校长及IBDP协调员
         */

        $userArr = array();
        $ms_principal = array();
        $msv_principal = array();
        $occupation_en = array(51,226,151,162,172,235,254,269,323,340,459,551,602,603,560,595,599,497,474,567,528,532,552,172,538);

        $criteria = new CDbCriteria();
        $criteria->compare('occupation_en', $occupation_en);
        //$criteria->compare('branch', array('BJ_DS','BJ_TYG'));
        $criteria->index = 'uid';
        $userProfileModel = UserProfile::model()->findAll($criteria);
        if($userProfileModel){
            $criteria = new CDbCriteria();
            $criteria->compare('uid', array_keys($userProfileModel));
            $criteria1 = new CDbCriteria();
            $criteria1->compare('level', 1);
            $criteria1->compare('uid', $model->msv_principal, false, 'OR');
            $criteria->mergeWith($criteria1);
            $userModel = User::model()->findAll($criteria);
            if($userModel){
                foreach ($userModel as $val){
                    if(in_array($val->profile->occupation_en,array(51,226,151,340,551))){
                        $ms_principal[$val->uid] = $val->getName();
                    }else{
                        $msv_principal[$val->uid] = $val->getName();
                    }

                }
            }
        }
        $this->renderPartial('_update', array(
            'model' => $model,
            'ms_principal' => $ms_principal,
            'msv_principal' => $msv_principal,
            'fill_in_end_time'=>$fill_in_end_time,
        ));

    }

    /**
     * 删除一条校长的评语  根据周期ID
     */
    public  function actionDelereport()
    {
        if(Yii::app()->request->isPostRequest){
            $id = Yii::app()->request->getParam('id', 0);
            if($id){
                $model = AchievementReport::model()->findByPk($id);
                if($model->delete()){
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('global', 'Data deleted'));
                    $this->addMessage('refresh', true);
                }
                else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('global', 'Deletion failed'));
                }
            }
        }
        $this->showMessage();
    }


    /**
     * 判断当前用户，如果是老师显示所教的科目，否则显示全部
     */
    public function teacherFlagShow(){
        Yii::import('common.models.grades.TeacherSubjectLink');
        $flag=array();
        $link = TeacherSubjectLink::model()->findAll('schoolid=:schoolid and teacher_uid=:teacher_uid and status=1',
            array(':schoolid'=>$this->branchId,':teacher_uid'=>Yii::app()->user->getId())
        );
        if ($link){
            foreach($link as $tag){
                $flag[] = $tag->subject_flag;
            }
        }
        return $flag;
    }
}
