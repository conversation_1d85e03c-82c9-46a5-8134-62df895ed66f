<?php if(!$params['valid'] && !$params['authenticated']): //错误的URL?>
	<h1>
	<?php echo Yii::t("user", 'Invalid access link'); ?>
	</h1>
<?php else: ?>
	<h1><?php echo Yii::t("user", 'Visitor Authentication');?></h1>
	<?php if($params['authenticated']): ?>
		<p><?php echo Yii::t("user", 'Already Authenticated.'); ?></p>
	<?php else: ?>
		<p><?php echo Yii::t("user", "Please enter the access code.");?></p>
	<?php endif;?>

		<div id="access-auth" class="form">
		<?php $form=$this->beginWidget('CActiveForm', array(
			'id'=>'access-form',
			'enableClientValidation'=>true,
			'clientOptions'=>array(
				'validateOnSubmit'=>true,
			),
		)); ?>
		
			<div class="tac span-2 colborder">
				<img class="thumb-small thumb-bg" src="<?php echo CommonUtils::childPhotoUrl($params['child']->photo, 'small'); ?>" />
			</div>
			<div class="span-8">
				<?php if($params['authenticated']):?>
					<p><?php echo Yii::t("user", 'You already logged in as parent');?></p>
				<?php elseif($params['valid']): //认证FORM ?>
					<?php echo $form->hiddenField($model, 'accesskey'); ?>
					<div class="row">
						<?php echo $form->labelEx($model,'passcode'); ?>
						<?php echo $form->passwordField($model,'passcode', array("size"=>"30")); ?>
						<?php echo $form->error($model,'passcode'); ?>
					</div>
				
					<div class="row buttons">
						<?php echo CHtml::submitButton(Yii::t("global", 'Submit'), array("class"=>"bigger wider")); ?>
					</div>
				<?php endif;?>
			</div>
		
		<?php $this->endWidget(); ?>
		</div><!-- form -->

<?php endif;?>


