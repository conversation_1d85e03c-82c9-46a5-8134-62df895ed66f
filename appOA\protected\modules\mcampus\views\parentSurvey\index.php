<?php if ($show): ?>
<div class="container-fluid"  id='container' v-cloak>
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Advanced'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('teaching','Survey-Teacher Feedback');?></li>
    </ol>
    <div id='container' v-cloak>
        <div class='borderBto flex pb24 align-items'>
            <div class='flex1'>
                <el-select v-model="startYear" placeholder="<?php echo Yii::t('global', 'Please Select'); ?>" size='small'  @change='getTeacher'>
                    <el-option
                    v-for="(item,index) in initData.yearList"
                    :key="item.key"
                    :label="item.value"
                    :value="item.key">
                    </el-option>
                </el-select>
                <el-select v-model="grade" placeholder="<?php echo Yii::t('global', 'Please Select'); ?>"  size='small' @change='getTeacher'>
                    <el-option
                    v-for="(item,index) in initData.gradeList"
                    :key="item.key"
                    :label="item.value"
                    :value="item.key">
                    </el-option>
                </el-select>
            </div>
            <div  v-if='teacherData.answer && teacherData.answer.length'>
                <label class="checkbox-inline" >
                     <input type="checkbox" v-model='unRemove' @change='changeRemove'> <?php echo Yii::t('teaching','Exclude option of <strong>not familiar</strong>');?>
                </label>
                <button type="button" class="btn btn-primary ml16" @click='exportTable' :disabled='exportBtn'>{{exportBtn?"<?php echo Yii::t('global', 'Exporting'); ?>":"<?php echo Yii::t('labels', 'Export'); ?>"}}</button>
            </div>
        </div>
        <div class='row' v-if='teacherData.answer && teacherData.answer.length'>
            <div class='col-md-3 borderRight ' style='padding: 0 8px 0 15px;'>
                <div  class='pt24 overflow-y scroll-box '  :style="'height:'+(height-275)+'px;overflow-x: hidden;padding-right:7px'">                
                <div class='flex mb12 align-items'>
                    <div class='flex1 font14 color3 font500'><?php echo Yii::t('teaching','Teachers List');?></div>
                    <div>
                        <el-dropdown>
                            <span class="el-dropdown-link colorBlue cur-p">
                            “{{showOption(key)}}” <?php echo Yii::t('teaching','Ratio');?><i class="el-icon-arrow-down el-icon--right"></i>
                            </span>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item @click.native='key=list.key;getTeacher()' :key='index' v-for='(list,index) in initData.optionList'>{{list.value}}</el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                        <span class="caret-wrapper">
                            <el-tooltip class="item" effect="dark" content="<?php echo Yii::t('teaching','ascending order');?>" placement="top">
                                <i class="sort-caret " :class='sortType=="ascending"?"ascendingColor":"ascending"' @click='sortTeacher("ascending")'></i>
                            </el-tooltip>
                            <el-tooltip class="item" effect="dark" content="<?php echo Yii::t('teaching','descending order');?>" placement="top">
                                <i class="sort-caret " :class='sortType=="descending"?"descendingColor":"descending"' @click='sortTeacher("descending")'></i>
                            </el-tooltip>
                        </span>
                    </div>
                </div>
                <div >
                    <div class='teacherList' v-for='(list,index) in teacherData.answer' :class='teacherId==list.teacherId?"currentTeacher":""' @click='getDetail(list)'>
                        <div class='font12 color6'>{{list.sort}}</div>
                        <div class='flex1 flex ml12 align-items'>
                            <img :src="teacherData.teacherInfo[list.teacherId].photoUrl" alt="" class='img42'>
                            <div class='flex1 flexText ml5'>
                                <div class='font14 color3 word-break'>{{teacherData.teacherInfo[list.teacherId].name}}</div>
                                <div class='font12 color6 word-break'>{{teacherData.teacherInfo[list.teacherId].hrPosition}}</div>
                            </div>
                        </div> 
                        <div class='font12 colorBlue ml10'>{{formatPercentage(list.percent)}}</div>
                        <div class='ranking ml4'>{{list.num}}/{{list.total}}</div>
                    </div>
                </div>
                </div>
            </div>
            <div class='col-md-9 overflow-y scroll-box' v-if='teacherId!=""' :style="'height:'+(height-250)+'px;overflow-x: hidden;'">
                <div v-loading="loading" >
              
                <div class='flex align-items pt24 mb24' >
                    <div class='flex1 flex ml5 align-items' >
                        <img :src="teacherData.teacherInfo[teacherId].photoUrl" alt="" class='img42'>
                        <div class='flex1 flexText ml5'>
                            <div class='font14 color3 word-break'>{{teacherData.teacherInfo[teacherId].name}}</div>
                            <div class='font12 color6 word-break'>{{teacherData.teacherInfo[teacherId].hrPosition}}</div>
                        </div>
                    </div> 
                    <div>
                        <span class='rankingTotal'  v-for='(list,index) in initData.optionList'  :style="'background:'+commentColorlist[index]+';color:'+echatsColorlist[index]">{{list.value}}：{{getPercentage(list)}}</span>
                    </div>
                </div>
                <!-- style='height:600px;width:100%' -->
                <div id='echart'  :style="'height:'+(classLen*80)+'px;width:100%'" ></div>
                <div class='mt20 flex'>
                    <div class='flex1'>                    
                        <el-select v-model="valueClass" placeholder="<?php echo Yii::t('global', 'Please Select'); ?>" size='small' class='mr24' @change='filterTable'>
                            <el-option
                            v-for="(item,index) in filterClass"
                            :key="item.classid"
                            :label="item.title"
                            :value="item.classid">
                            </el-option>
                        </el-select>
                        <label class="checkbox-inline" v-for='(list,index) in initData.optionList'>
                            <input type="checkbox" :value="list" v-model='filterOption' @change='selectedOptions'> {{list.value}}
                        </label>
                    </div>
                    
                </div>
                <div>
                    <table class="table mt16">
                        <tbody>
                            <tr >
                                <!-- <th width='180'><?php echo Yii::t('child', 'Class'); ?></th> -->
                                <th width='180'>
                                    <!-- <span class='font14 color6'>评价</span> -->
                                </th>
                                <th>
                                    <div class='flex align-items'>
                                        <div class='flex1'>
                                            <!-- <span class='font14 color6'><?php echo Yii::t('global', 'Comment'); ?></span>  -->
                                        </div>
                                        <el-switch
                                            style="display: block"
                                            v-model="showUser"
                                            @change='getDetail(teacherDetail,"switch")'
                                            active-color="#D9534F">
                                        </el-switch>
                                        <span class='colorRed ml5 font14' v-if='initData.superAuth'><?php echo Yii::t('teaching','Show submitter (super admin)');?></span>
                                    </div>
                                </th>
                            </tr>
                            </tbody>
                            <template  v-for="(item,index) in classInfo" >
                                <tr>
                                    <td colspan=2 style='background:#F7F7F8;border-bottom:none'><span class='font14'> {{item.title}}</span></td>
                                </tr>
                                <template  v-for="(list,id) in filterOption" >
                                    <tr  v-if='getMemos(item,list).length'>
                                        <td  :class='id+1==filterOption.length?"borderBtoNone":""'><span :style="'background:'+commentColorlist[id]+''" class='label_tag'> {{list.value}}</span></td>
                                        <td  :class='id+1==filterOption.length?"borderBtoNone":""'>
                                            <div  v-for='(comment,i) in getMemos(item,list)'>
                                                <div class='comment' :style="'background:'+commentColorlist[id]+''">
                                                    <span v-if='comment.memo!=""'>{{comment.memo}}</span> 
                                                    <span v-else>N/A</span>
                                                    <span class='color9 ml5' v-if='showUser'><span v-if='detailData.childInfo[comment.childid]'>— {{detailData.childInfo[comment.childid].name}}</span></span>
                                                </div>  
                                            </div>
                                        </td>
                                    </tr>
                                </template>
                            </template>
                       
                    </table>
                </div>
                </div>
            </div>
        </div>
        <el-empty v-else-if='teacherData.answer' description="<?php echo Yii::t('ptc', 'No Data') ?>"></el-empty>
    </div>
</div>
<?php
    $this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
$(document).ready(function () {
// 通过该方法来为每次弹出的模态框设置最新的zIndex值，从而使最新的modal显示在最前面
    $(document).on('shown.bs.modal', '.modal.in', function(event) {
        var zIndex = 1040 + (10 * $('.modal:visible').length);
            $(this).css('z-index', zIndex);
            setTimeout(function() {
                $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');
            }, 0);
    }).on('hidden.bs.modal', '.modal', function(event) {
        if ($('.modal.in').size() >= 1) {
            $('body').addClass('modal-open')
        }
    })
});
var height=document.documentElement.clientHeight;
var container=new Vue({
    el: "#container",
    data: {
        height:height,
        initData:{},
        startYear:'',
        grade:'',
        key:'A',
        sortType:'ascending',
        teacherData:{},
        teacherId:'',
        detailData:{},
        showUser:false,
        value:'',
        seriesData:[],
        option:[],
        optionValue:[],
        yAxisData: [],
        filterOption:[],
        filterClass:[],
        classInfo:[],
        valueClass:0,
        teacherDetail:{},
        classLen:0,
        unRemove:true,
        copyOptionList:[],
        loading:false,
        tabLoading:false,
        commentColorlist : ["rgba(66, 126, 248, 0.08)", "rgba(90, 216, 166, 0.08)", "rgba(246, 189, 22, 0.08)", "rgba(109, 200, 236, 0.08)","rgba(66, 126, 248, 0.08)", "rgba(90, 216, 166, 0.08)", ],
        echatsColorlist : ["rgba(66, 126, 248, 0.8)", "rgba(90, 216, 166, 0.8)", "rgba(246, 189, 22, 0.8)", "rgba(109, 200, 236, 0.8)","rgba(66, 126, 248, 0.8)", "rgba(90, 216, 166, 0.8)",],
        exportBtn:false,
        unOption:'Z'
    },
    created: function() {
        this.getInit()
    },
    computed: {
    },
    methods: {
        OptionListOrder(a, b) {
            const optionListIndexA = this.initData.optionList.findIndex(option => option.key === a.key);
            const optionListIndexB = this.initData.optionList.findIndex(option => option.key === b.key);
            return optionListIndexA - optionListIndexB;
        },
        selectedOptions() {
           this.filterOption.sort(this.OptionListOrder)
        },
        filterTable(){
            if(this.valueClass==0){
                this.classInfo=this.detailData.classInfo
            }else{
                this.classInfo=this.detailData.classInfo.filter((i) => i.classid === this.valueClass)
            }
        },
        getMemos(list,item){
            let option=this.detailData.answer[list.classid].options
            let memos=option.filter((i) => i.option === item.key)
            return memos.length?memos[0].memos:[]
        },
        formatPercentage(number) {
            return `${parseFloat((number * 100).toFixed(2))}%`;
        },
        getPercentage(item){
            var total=0
            var num=0
            for(var key in this.detailData.answer){
                total+=this.detailData.answer[key].total
                this.detailData.answer[key].options.forEach((list,i) => {
                    if(item.key==list.option){
                        num+=list.count
                    }
                })
            }
            return `${parseFloat(((num / total) * 100).toFixed(2))}%`;
        },
        showOption(key){
            var time = this.initData.optionList.filter(function(element,index){
                return element.key === key;
            });
            return time[0].value
        },
        getInit(){
            let that=this
            $.ajax({
                url: '<?php echo $this->createUrl("api") ?>',
                type: "post",
                dataType: 'json',
                data: {
                    url:'config',
                },
                success: function(data) {
                    if (data.state == 'success') {
                        that.initData=data.data
                        that.copyOptionList=JSON.parse(JSON.stringify(data.data.optionList))
                        if(that.unRemove){
                            that.initData.optionList= that.copyOptionList.filter(option => option.key !== that.unOption);
                        }
                        that.filterOption=data.data.optionList
                        that.option=[]
                        for(var i=0;i<that.initData.optionList.length;i++){
                            that.option.push(that.initData.optionList[i].value)
                            that.optionValue.push(that.initData.optionList[i].key)
                        }
                        that.startYear=data.data.yearList[0].key
                        that.grade=data.data.gradeList[0].key
                        that.getTeacher()
                    }else{
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                },
                error: function(data) {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                },
            })
        },
        getTeacher(){
            let that=this
            if(this.startYear=='' || this.grade==''){
                return
            }
            this.teacherId=''
            $.ajax({
                url: '<?php echo $this->createUrl("api") ?>',
                type: "post",
                dataType: 'json',
                data: {
                    url:'overview',
                    startYear:this.startYear,
                    grade:this.grade,
                    key:this.key,
                    showD:this.unRemove?0:1
                },
                success: function(data) {
                    if (data.state == 'success') {
                        that.sortType='ascending'
                        if(data.data.answer){
                            for(var i=0;i<data.data.answer.length;i++){
                                data.data.answer[i].sort=i+1
                            }
                        }
                        that.teacherData=data.data
                    }else{
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                },
                error: function(data) {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                },
            })
        },
        sortTeacher(sortType){
            this.sortType=sortType
            if (sortType === "ascending") {
                this.teacherData.answer = this.teacherData.answer.sort((a, b) => b.percent - a.percent);
            } else if (sortType === "descending") {
                this.teacherData.answer = this.teacherData.answer.sort((a, b) => a.percent - b.percent);
            }
        },
        changeRemove(){
            if(this.unRemove){
                this.initData.optionList= this.copyOptionList.filter(option => option.key !== this.unOption);
            }else{
                this.initData.optionList=this.copyOptionList
            }
            this.option=[]
                        for(var i=0;i<this.initData.optionList.length;i++){
                            this.option.push(this.initData.optionList[i].value)
                            this.optionValue.push(this.initData.optionList[i].key)
                        }
            this.getTeacher()
            if(this.teacherDetail.teacherId){
                this.getDetail(this.teacherDetail)
            }
        },
        getDetail(list,type){
            this.teacherId=list.teacherId
            this.teacherDetail=list
            let that=this
            if(this.startYear=='' || this.grade==''){
                return
            }
            if(type){
                this.tabLoading=true
            }else{
                this.showUser=false
                this.loading=true
            }
            $.ajax({
                url: '<?php echo $this->createUrl("api") ?>',
                type: "post",
                dataType: 'json',
                data: {
                    url:'detail',
                    startYear:this.startYear,
                    grade:this.grade,
                    key:this.key,
                    teacherId:this.teacherId,
                    showUser:this.showUser,
                    showD:this.unRemove?0:1
                },
                success: function(data) {
                    if (data.state == 'success') {
                        
                        that.detailData=data.data
                        let negative=['C','D']
                        that.classLen=data.data.classInfo.length+1
                        that.seriesData=[]
                        var seriesDataVal=[]
                        that.initData.optionList.forEach((item,index) => {
                            seriesDataVal[index]=[]
                            for(var key in data.data.answer){
                                let filterOption= data.data.answer[key].options.filter(option => option.option == item.key);
                                if(filterOption.length==0){
                                    seriesDataVal[index].push(0)
                                }else{
                                    if(negative.indexOf(filterOption[0].option)!=-1){
                                        seriesDataVal[index].push(-filterOption[0].count)
                                    }else{
                                        seriesDataVal[index].push(filterOption[0].count)
                                    }
                                }
                            }
                            that.seriesData.push({
                                itemStyle: {//自定义颜色
                                    normal: { color:that.echatsColorlist[index] },
                                },
                                name: that.option[index],
                                type: 'bar',
                                label: {
                                    show: true,
                                    position: 'inside',
                                    formatter: function(params) {
                                        return Math.abs(params.value)
                                    },
                                    textStyle:{
                                        color:"#fff",//文字颜色
                                    },      
                                },
                                // stack: 'Total',
                                emphasis: {
                                    focus: 'series'
                                },
                                data:seriesDataVal[index]
                                
                            })
                        });
                        
                        that.yAxisData=[]
                        data.data.classInfo.forEach((item,index) => {
                            that.yAxisData.push(item.title + '\n' + + data.data.answer[item.classid].total+' '+'<?php echo Yii::t('teaching','participants');?>')
                        })
                        let info=[{title:'<?php echo Yii::t('campus', 'All classes'); ?>',classid:0}]
                        that.classInfo=JSON.parse(JSON.stringify(data.data.classInfo))
                        
                        that.filterClass=info.concat(JSON.parse(JSON.stringify(data.data.classInfo)))
                        that.$nextTick(() => {
                            var myCharts = echarts.init(document.getElementById('echart'))
                            var option = that.optionData(myCharts)
                            myCharts.setOption(option,true);
                            myCharts.resize()
                        })
                        that.loading=false
                        that.tabLoading=false
                    }else{
                        that.loading=false
                        that.tabLoading=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                },
                error: function(data) {
                    that.loading=false
                    that.tabLoading=false
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                },
            })
        },
        optionData(myCharts){
            var option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                    type: 'shadow'
                    }
                },
                legend: {
                    itemWidth:12,
                    itemHeight:12,
                    textStyle:{
                        fontSize:12,
                        color:'#333',
                    },
                    x:"left",
                    icon: 'rect',
                    data:this.option
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: [
                    {
                    type: 'value'
                    }
                ],
                yAxis: [
                    {
                    position: 'top',
                    inverse: true,
                    axisTick: {
                        show: false
                    },
                    data:this.yAxisData
                    }
                ],
                series:this.seriesData
            };
            return option
        },
        exportTable(){
            let that=this
            this.exportBtn=true
            var exportDatas = [];
            $.ajax({
                url: '<?php echo $this->createUrl("api") ?>',
                type: "post",
                dataType: 'json',
                data: {
                    url:'export',
                    startYear:this.startYear,
                    grade:this.grade,
                },
                success: function(data) {
                    if (data.state == 'success') {
                        for(var i=0;i<data.data.surveyData.length;i++){
                            let child=data.data.surveyData[i]
                            let childInfo=data.data.childInfo[child.childid]
                            exportDatas.push({
                            'ID':child.childid,
                            '姓名':childInfo.childName,
                            '入学学年':childInfo.firstStartyear,
                            '语言':data.data.langList[childInfo.lang],
                            '生日':childInfo.birthday,
                            '老师':data.data.teacherInfo[child.teacherid],
                            '校园':data.data.schoolAbb,
                            '班级':childInfo.className,
                            '父亲邮件':data.data.parentInfo[childInfo.fid],
                            '母亲邮件':data.data.parentInfo[childInfo.mid],
                            '选项':data.data.optionConfig[child.option],
                            '评价':child.memo,
                            });
                        }
                        var myDate=new Date;
                        var year=myDate.getFullYear(); //获取当前年
                        var mon=myDate.getMonth()+1<10?"0"+(myDate.getMonth()+1):myDate.getMonth()+1; //获取当前月
                        var date=myDate.getDate()<10?"0"+myDate.getDate():myDate.getDate(); //获取当前日
                        let nowDate=year+'-'+mon+'-'+date
                        var title='教师满意度评价'+nowDate;
                        if(title.length>31){
                            title=title.substr(13,title.length)
                        }
                        const filename =title+'.xlsx';
                        const ws_name = "SheetJS";
                        var wb=XLSX.utils.json_to_sheet(exportDatas,{
                            origin:'A1',// 从A1开始增加内容
                            header: ['ID','姓名','入学学年','语言','生日','老师','校园','班级','父亲邮件','母亲邮件','选项','评价'],
                        });
                        const workbook = XLSX.utils.book_new();
                        XLSX.utils.book_append_sheet(workbook, wb, ws_name);
                        const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                        const blob = new Blob([wbout], {type: 'application/octet-stream'});
                        let link = document.createElement('a');
                        link.href = URL.createObjectURL(blob);
                        link.download = filename;
                        link.click();
                        setTimeout(function() {
                            // 延时释放掉obj
                            that.exportBtn=false
                            URL.revokeObjectURL(link.href);
                            link.remove();
                        }, 500);
                    }else{
                        that.exportBtn=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                },
                error: function(data) {
                    that.exportBtn=false
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                },
            })
       
        }
    }
})
</script>
<style>
    .img42{
        width: 42px;
        height: 42px;
        object-fit: cover;
        border-radius:50%
    }
    .font500{
        font-weight:500
    }
    [v-cloak]{
        display:none;
    }
    .scroll-box::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius   : 10px;
        background-color: #ccc;
        background-image: none
    }
    .scroll-box::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0);
        background   : #fff;
        border-radius: 10px;
        border:none
    }
    .teacherList{
        background: #FFFFFF;
        border-radius: 4px;
        border: 1px solid #E5E6EB;
        padding:12px;
        display:flex;
        align-items:center;
        margin-top:12px;
    }
    .teacherList:hover,.currentTeacher{
        cursor: pointer;
        background: rgba(77,136,210,0.08);
        border-radius: 4px;
        border: 1px solid #4D88D2;
    }
    .colorBlue{
        color:#4D88D2
    }
    .colorRed{
        color:#D9534F
    }
    .ranking{
        padding:2px 6px;
        background: #F2F3F5;
        border-radius: 2px;
        font-size:12px;
        color:#333
    }
    .flexText{
      width:0
    }
    .word-break{
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .rankingTotal{
        height: 12px;
        font-weight: 400;
        font-size: 12px;
        line-height: 12px;
        border-radius: 2px;
        padding:2px 6px;
        margin-left:16px
    }
    .comment{
        padding:12px;
        background: #F7F7F8;
        border-radius: 4px;
        color:#333;
        font-size:12px;
        margin-bottom:12px
    }
    .comment:hover{
        background: rgba(77,136,210,0.08);
    }
    th,td{
        padding:12px 24px !important;
        border-top:none !important
    }
    td{
        border-bottom:1px solid #E5E6EB;
        vertical-align: top;
    }
    .caret-wrapper {
        display: -webkit-inline-box;
        display: -ms-inline-flexbox;
        display: inline-flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        width: 24px;
        vertical-align: middle;
        cursor: pointer;
        overflow: initial;
        position: relative;
    }
    .sort-caret {
        width: 0;
        height: 0;
        border: 5px solid transparent;
        position: absolute;
        left: 7px;
    }
    .descendingColor{
        border-top-color: #4D88D2;
        bottom: -9px;

    }
    .ascendingColor{
        border-bottom-color: #4D88D2 ;
        top: -13px;

    }
    .ascending {
        border-bottom-color: #C0C4CC;
        top: -13px;
    }
    .descending {
        border-top-color: #C0C4CC;
        bottom: -9px;
    }
   
    .borderRight{
        border-right:1px solid #E5E6EB
    }
    .borderBto{
        border-bottom:1px solid #E5E6EB
    }
    .scroll-box::-webkit-scrollbar{
      background-color: #ccc;
      /* height:6px; */
      width: 5px;
    } 
    .scroll-box::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius   : 10px;
        background-color: skyblue;
        background-color: #D8D8D8;
        background-image: none
    }
    .label_tag{
        display:inline-block;
        padding:4px 8px;
        line-height:1;
        border-radius:4px;
        font-size:14px
    }
    .borderBtoNone{
        border-bottom:none !important
    }
</style>
<?php endif; ?>
