<?php
$this->menu=array(
	array('label'=>Yii::t('operations', '新增商品'), 'url'=>array('update'), 'role'=>'add'),
);
$this->widget('ext.ivyCGridView.IvyCGridView', array(
	'id'=>'points-product-grid',
	'dataProvider'=>$model->search(),
//	'filter'=>$model,
    'colgroups'=>array(
        array(
            "colwidth"=>array(200,80,80,80,80,80,100,200),
//            "htmlOptions"=>array("align"=>"left", "span"=>2)
        )
    ),
	'columns'=>array(
		'title'=>array(
            'name'=>'商品名称',
            'type'=>'raw',
            'value'=>'$data->en_title."<br/>".$data->cn_title.$data->showCover("div", "", array("style"=>"height:80px"))',
        ),
		'credits',
		'p_price',
		'm_price',
		'stock',
		array(
			'name' => 'status',
            'value' => '$data->showStatus()',
		),
        array(
            'name'=>'updated',
            'value'=>'OA::formatDateTime($data->updated)',
        ),
		array(
			'class'=>'CButtonColumn',
            'template' => '{gallery} {stock} {update} {delete}',
            'updateButtonImageUrl'=>false,
            'deleteButtonImageUrl'=>false,
            'afterDelete'=>'function(link,success,data){ if(success && data) alert(data);}',
            'buttons'=>array(
                'stock'=>array(
                    'label'=>'添加库存',
                    'url'=>'Yii::app()->createUrl("/operations/stock/create", array("productid" => $data->id))',
                    'options'=>array('class'=>'J_dialog'),
                ),
                'gallery'=>array(
                    'label'=>'上传照片',
                    'url'=>'Yii::app()->createUrl("/operations/product/gallery", array("id" => $data->id))',
                ),
            ),
		),
        array('value'=>''),
	),
)); ?>
