<div v-if='feedback.items && feedback.items.length==0'><div class="alert alert-warning" role="alert"><?php echo Yii::t("newDS", "No feedback found!");?></div></div>
<div v-for="(item, i) in feedback.items" :key="i">
    <?php if($type == 'allUnreplied'):?>
        <div class="panel panel-default" @click.stop="open(item.id)">
            <div class="panel-body bg-hover" :class="{'bg-info': item.id==opened}"  v-if='item.teacherInfo'>
                <div class="media flex mb10" style='line-height:20px' >
                    <div class="media-body flex1  pull-left">
                        <div class="list-group-item-heading text-primary font14" >
                            <span class='cur-p' @click.stop='viewContent(item.id)'>{{ item.title }}</span>
                        </div>
                    </div>
                    <div class="media-right pull-right text-muted text-right select_4 relative" style='line-height:20px'>
                        <span class="badge Unreplied">{{ item.count }}</span>
                        <div class="font12 cur-p" v-if='item.id==opened'>
                            <?php echo Yii::t("newDS", "Collapse");?> 
                            <span class='glyphicon glyphicon-chevron-up'></span>
                        </div>
                        <div v-else class="font12 cur-p" >
                            <?php echo Yii::t("newDS", "Expand");?> 
                            <span class='glyphicon glyphicon-chevron-down' ></span>
                        </div>
                    </div>
                </div>
                <div style='line-height:32px'>
                    <div class='pull-left' >
                        <a href="javascript:void(0)"  class='pull-left' >
                            <img class="media-object img-circle image" style='width:32px;height:32px' :src="item.teacherInfo.photoUrl" data-holder-rendered="true" >
                        </a>
                        <span class='ml10'>
                        <span class='font14 color3'>{{item.teacherInfo.name}}</span>丨
                        <span class='font14 color6'>{{item.sign_as_title}}</span>
                        </span>                         
                    </div>
                    <span style='line-height:32px' class="list-group-item-text color6 pull-right">{{ item.updated_at }}</span>
                </div>
                <div class='clearfix'></div>
            </div>
        </div>
    <?php else:?>
        <div class="panel panel-default" @click.stop="open(item.id)">
            <div class="panel-body bg-hover" :class="{'bg-info': item.id==opened}">
                <span class="badge" :class='tabType=="replied"?"gray":""'>{{ item.count }}</span>
                <?php if($type == 'unreply'):?>
                    <h4 class="list-group-item-heading" style='margin-right:64px'>
                        <div class='flex align-items'>
                            <div>{{ item.title }}</div>
                            <span class='btn-link font14 ml16' v-if='item.joint_admins && item.joint_admins.length>0' @click.stop='viewContent(item.id)'><?php echo Yii::t('newDS', 'Detail');?></span>
                        </div>
                    </h4>
                    <div class='flex align-items mt10'>
                        <div class="list-group-item-text text-muted pull-left mt5">{{ item.updated_at }}</div>
                        <div class='flex1 ml16 relative' v-if='item.joint_admins && item.joint_admins.length>0'>
                            <img :src="feedback.teacherInfo[list].photoUrl" alt="" class='img24 ' :class='index!=0?"jointImg":""' v-for='(list,index) in item.joint_admins'>
                            <span class='font12 color9 ml10 absoluteFlex'><?php echo Yii::t("newDS", "Multiple collaborators");?></span>
                        </div>
                    </div>
                <?php else:?>
                    <h4 class="list-group-item-heading" style='margin-right:64px'>{{ item.title }}</h4>
                    <p class="list-group-item-text text-muted">{{ item.updated_at }}</p>
                <?php endif;?>
            </div>
        </div>
    <?php endif;?>
    
    <div v-if="opened == item.id" class="comment-box">
        <div class="row" style="margin-right: 0;">
            <div class="col-md-3">
                <div class="list-group maxHeight">
                    <a href="javascript:;" class="list-group-item" :class="{'active-1': childid==_childid}" v-for="_childid in item.childids" :key="_childid" @click.stop="itemChild(_childid)">
                        <?php if($type == 'unreply'):?>
                        <span class="badge dot" v-if="feedbackNonAlert.indexOf(_childid) < 0">&nbsp;</span>
                        <?php endif;?>
                        <div class="flex">
                            <div class="">
                                <img :src="feedback.childs[_childid].avatar" class="img-circle" width="50">
                            </div>
                            <div class="flex1 ml15" style="margin-top: 10px;">
                                <h4 class="list-group-item-heading">{{feedback.childs[_childid].name}}</h4>
                                <p class="list-group-item-text">{{feedback.childs[_childid].class_name}}</p>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            <div class="col-md-9 active-1" v-if="childid && Object.keys(feedbackItem).length!=0" style="padding: 15px; border-radius: 4px;">
                <div class='scroll-box'  ref='scrollHeight' style="max-height: 500px;overflow-y: auto; overflow-x: hidden; margin-bottom: 15px;">
                    <div style="margin-bottom: 15px;" v-for="(fItem, i) in feedbackItem.items" :key="i">
                        <div v-if='fItem.mark_as_staff==0'>
                            <div class="flex" v-if="fItem.creator_type == 'parent'">
                                <div class="text-center">
                                    <img :src="feedback.childs[childid].avatar" class="img-circle" width="50"  height='50' style='object-fit: cover;'>
                                </div>
                                <div class="flex1 ml15">
                                    <div class="name-2">{{feedback.childs[childid].name}}</div>
                                    <div class="content-2" v-html='html(fItem.content)'></div>
                                    <div>
                                        <ul class='mb12 imgLi' :id='"<?php echo $type ?>"+"_"+fItem.id'  v-if='fItem.imgUrl.length!=0'>
                                            <li v-for='(list,j) in fItem.imgUrl'>
                                                <img :src="list.url" class='fileImg mr8' @click='showImg(fItem)' alt=""  >
                                            </li>
                                        </ul>
                                        <div  v-if='fItem.videoUrl.length!=0'>
                                            <div v-for="(list, j) in fItem.videoUrl" :key="list.id">
                                                <div v-if="list.video_convert_status == 0" class="flex fileLink">
                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" /> 
                                                    <a class='flex1 ml5' target= "_blank" :href="list.url"><?php echo Yii::t('message', "Convert") ?></a>
                                                </div>
                                                <video v-else :src="list.url" :poster="list.video_cover"  controls muted preload="auto" style="width: 100%; height: 100%; fit: fill"></video>
                                            </div>
                                        </div> 
                                        <div >
                                            <div class='flex fileLink' v-for='(list,j) in fItem.pdfUrl'>
                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/PDF1.png' ?>" alt="" class="pdfImg" v-if='list.mimetype=="application/pdf"' />
                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/excel.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/vnd.ms-excel" || list.mimetype=="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"' />
                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/word1.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/msword" || list.mimetype=="application/vnd.openxmlformats-officedocument.wordprocessingml.document"' />
                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/zip.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/x-zip-compressed"' />
                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" v-else /> 
                                                <a class='flex1 ml5' target= "_blank" :href="list.url">{{list.title}}
                                                </a>
                                            </div>
                                        </div>
                                        
                                    </div>
                                    <div class="text-muted" v-if="feedback.childs[childid][`p_${fItem.created_by}`] == 'f'"><?php echo Yii::t("newDS", "From student's Dad");?> {{ fItem.updated_at }}</div>
                                    <div class="text-muted font12" v-else><?php echo Yii::t("newDS", "From student's Mom");?> {{ fItem.updated_at }}</div>
                                </div>
                            </div>
                            <div class="flex" v-if="fItem.creator_type == 'staff'">
                                <div class="text-center">
                                    <img :src="feedbackItem.staff[fItem.created_by].avatar" class="img-circle" width="50" height='50' style='object-fit: cover;'>
                                </div>
                                <div class="flex1 ml15">
                                    <div class="name-2">{{feedbackItem.staff[fItem.created_by].name}}</div>
                                    <div class="content-2" v-html='html(fItem.content)'></div>
                                    <div>
                                        <ul class='mb12 imgLi' :id='"<?php echo $type ?>"+"_"+fItem.id'  v-if='fItem.imgUrl.length!=0'>
                                            <li v-for='(list,j) in fItem.imgUrl'>
                                                <img :src="list.url" class='fileImg mr8' @click='showImg(fItem)' alt=""  >
                                            </li>
                                        </ul>
                                        <div  v-if='fItem.videoUrl.length!=0'>
                                            <div v-for="(list, j) in fItem.videoUrl" :key="list.id">
                                                <div v-if="list.video_convert_status == 0" class="flex fileLink">
                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" /> 
                                                    <a class='flex1 ml5' target= "_blank" :href="list.url"><?php echo Yii::t('message', "Convert") ?></a>
                                                </div>
                                                <video v-else :src="list.url" :poster="list.video_cover"  controls muted preload="auto" style="width: 100%; height: 100%; fit: fill"></video>
                                            </div>
                                        </div> 
                                        <div >
                                            <div class='flex fileLink' v-for='(list,j) in fItem.pdfUrl'>
                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/PDF1.png' ?>" alt="" class="pdfImg" v-if='list.mimetype=="application/pdf"' />
                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/excel.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/vnd.ms-excel" || list.mimetype=="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"' />
                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/word1.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/msword" || list.mimetype=="application/vnd.openxmlformats-officedocument.wordprocessingml.document"' />
                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/zip.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/x-zip-compressed"' />
                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" v-else /> 
                                                <a class='flex1 ml5' target= "_blank" :href="list.url">{{list.title}}
                                                </a>
                                            </div>
                                        </div>
                                        
                                    </div>
                                    <div class="text-muted font12">{{ fItem.updated_at }}</div>
                                </div>
                            </div>
                        </div>
                        <div v-else class='p20 mt20 text-center borderTop ' :class='i!=feedbackItem.items.length-1?"borderBto":""'>
                            <p class='font14 color3'>
                                <span class='glyphicon glyphicon-pushpin mr10' style='color:#EC971FFF'></span>{{feedbackItem.staff[fItem.created_by].name}} <?php echo Yii::t("newDS", "marked No Reply Needed");?> <span class='font14 color6 ml10'>{{fItem.updated_at}}</span>
                            </p>
                            <div  class='font12 color9'>
                                <?php echo Yii::t("newDS", "(This message is invisible to parents)");?>
                            </div>
                        </div>
                    </div>
                </div>
                <div class='borderTop'>
                    <div class="form-group" v-if='item.canReply'>
                        <div class='uploadImg' v-if='uploadImgList.length>0'>
                            <div class='imgData mr8'  v-for='(list,i) in uploadImgList'>
                                <div v-if="list.types=='1'">
                                    <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Uploading");?></div>
                                </div>
                                <div v-else>
                                    <img class='fileImgs' @click='uploadShowImg(list.file_key)'  :src="list.file_key" alt="">
                                    <span aria-hidden="true"  @click.stop='delImg("img",list,i)'>×</span>
                                </div>
                            </div>
                            <template v-if='loadingType==1'>
                                <div class='imgData mr8'  v-for='(list,i) in loadingList'>
                                        <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Upload queue");?></div>
                                </div>
                            </template>
                        </div>
                        <div class='mt16' v-if='uploadLinkList.length>0'>
                            <div class='flex uploadFile' v-for='(list,index) in uploadLinkList'>
                                <span class='glyphicon glyphicon-paperclip mr8'></span>
                                <a target="_blank" class='flex1'  style='line-height:26px' :href='list.file_key' v-if='!list.isEdit'>{{list.title}}</a>
                                <span class='flex1' v-else><input type="input" class='inputStyle'  @keyup.enter="saveFile(list,index)"  v-model='attachmentName' ></span>
                                <template v-if='!list.isEdit'>
                                    <span class='glyphicon glyphicon-edit icon mr16' v-if='list.file_key!=""' @click.stop='list.isEdit=true,attachmentName=list.title'></span>
                                    <span class='glyphicon glyphicon-trash icon' v-if='list.file_key!=""' @click.stop='delImg("link",list,index)'></span>
                                </template>
                                <span style='width:90px'  class='text-right inline-block' v-else>
                                    <button type="button" class="btn btn-primary btn-xs" @click='saveFile(list,index)'><?php echo Yii::t("global", "Save");?></button>
                                    <button type="button" class="btn btn-default btn-xs" @click='list.isEdit=false'><?php echo Yii::t("global", "Cancel");?></button>
                                </span>
                            </div>
                        </div>
                        <template v-if='loadingType==2'>
                            <div class='flex uploadFile'  v-for='(list,i) in loadingList'>
                                <span class='glyphicon glyphicon-paperclip mr8'></span>
                                <a  class='flex1'  style='line-height:26px' > <?php echo Yii::t("directMessage", "Upload queue");?></a>
                            </div>
                        </template>
                        <div class='uploadIcon'>
                            <?php if($type == 'replied'):?>
                                <div class='cur-p inline-block' id='pickfilesPhoto1'>
                                    <span class='glyphicon glyphicon-picture pull-left' style='margin-top:2px'></span>
                                    <span class='ml5 font14 color6'><?php echo Yii::t("directMessage", "Photo");?></span> 
                                </div>
                                <div class='cur-p inline-block ml10' id='pickfilesVideo1'>
                                    <span class='glyphicon glyphicon-facetime-video pull-left' style='margin-top:3px'></span>
                                    <span class='ml5 font14 color6'><?php echo Yii::t("directMessage", "Video");?></span> 
                                </div>
                                <div class='cur-p inline-block ml10' id='pickfilesPDF1'>
                                    <span class='glyphicon glyphicon-paperclip pull-left' style='margin-top:3px'></span>
                                    <span class='ml5 font14 color6'><?php echo Yii::t("curriculum", "Attachments");?></span> 
                                </div>
                            <?php endif;?>
                            <?php if($type == 'unreply'):?>
                                <div class='cur-p inline-block' id='pickfilesPhoto'>
                                    <span class='glyphicon glyphicon-picture pull-left' style='margin-top:2px'></span>
                                    <span class='ml5 font14 color6'><?php echo Yii::t("directMessage", "Photo");?></span> 
                                </div>
                                <div class='cur-p inline-block ml10' id='pickfilesVideo'>
                                    <span class='glyphicon glyphicon-facetime-video pull-left font18' style='margin-top:1px'></span>
                                    <span class='ml5 font14 color6'><?php echo Yii::t("directMessage", "Video");?></span> 
                                </div>
                                <div class='cur-p inline-block ml10' id='pickfilesPDF'>
                                    <span class='glyphicon glyphicon-paperclip pull-left' style='margin-top:2px'></span>
                                    <span class='ml5 font14 color6'><?php echo Yii::t("curriculum", "Attachments");?></span> 
                                </div>
                            <?php endif;?>
                        </div>
                        <textarea v-model="content" class="form-control textareaCss" rows="5" placeholder='<?php echo Yii::t("newDS", "Please input your reply");?>'></textarea>
                    </div>
                    <div class="text-right" v-if='item.canReply'>
                        <button type="button" class="btn btn-link mr20"  @click.stop="markNoReply(item)"><?php echo Yii::t("newDS", "No Reply Needed");?></button>
                        <button class="btn btn-primary" @click.stop="reply('0')" :disabled="loading"  ><?php echo Yii::t("newDS", "Reply");?></button>
                    </div>
                    <div class="text-right" v-if='!item.canReply'>
                        <span class='color6 mr20'><?php echo Yii::t("newDS", "Notice is offline, feedback reply is disabled.");?></span>
                        <button class="btn btn-primary"  @click.stop="markNoReply(item)" :disabled="loading"  ><?php echo Yii::t("newDS", "No Reply Needed");?></button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>