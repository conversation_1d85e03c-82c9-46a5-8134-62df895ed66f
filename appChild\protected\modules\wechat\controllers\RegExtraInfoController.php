<?php

class RegExtraInfoController extends WeChatVisualController
{
	public $layout = '//layouts/column4';

	public $showModel;

	public $step;

	public $childid;

	public $childObj;

	public $reserveObj;

	public $steps;

	public $studentSteps = array();

	public $regStudent = array();

	public $parent;

	public $config;

	public $showNumber;

	// 当前学生注册是否在开放时间内
	public $isOpen = false;

	public function init()
	{
		parent::init();
		// 关闭注册
//		$this->layout = '//layouts/mainv2';
//		$this->render("//../modules/wechat/views/bind/nowechat", array('regTips'=>true));
//		Yii::app()->end();

		Yii::app()->theme = 'app';
		Yii::import('common.models.regextrainfo.*');
		Yii::import('common.models.invoice.ChildReserve');
	}

	public function beforeAction($action)
	{

		if ($this->action->id == 'index') {
			return true;
		}

		$this->childid = (string)Yii::app()->request->cookies['childid'];
		if (Yii::app()->request->getParam('childid')) {
			$this->childid = Yii::app()->request->getParam('childid');
			Yii::app()->request->cookies['childid'] = new CHttpCookie('childid', $this->childid);
		}
		if ($this->childid && isset($this->myChildObjs[$this->childid])) {
		    $this->childObj = $this->myChildObjs[$this->childid];
		} else {
		    $this->childObj = end($this->myChildObjs);
		    $this->childid = $this->childObj->childid;
		}

		// regStudent
		$this->regStudent = RegStudents::getActive(null, $this->childid);
		$this->config = RegExtraInfo::getStepConfig();
		$regInfo = array();
		if (!$this->regStudent) {
			return false;
		}

		if (($this->regStudent->start_time <= time() && $this->regStudent->end_time >= time())) {
			$this->isOpen = true;
		}
		// 查找孩子正在进行的步骤
		$regInfo = RegExtraInfo::getChildReginfo($this->regStudent->reg_id, $this->regStudent->childid);
		foreach ($regInfo as $item) {
			$this->studentSteps[$item->step] = $item->complete;
		}



		$criteria = new CDbCriteria();
		$criteria->compare('childid', $this->childid);
		$criteria->compare('stat', 20);
		$this->reserveObj = ChildReserve::model()->find($criteria);
        $this->steps = RegExtraInfo::getStepsBySchool($this->regStudent->schoolid);

		$this->parent = IvyParent::model()->findByPk($this->wechatUser->userid);
		$this->showNumber = array_search($this->step, $this->steps) + 1;
		return true;
	}

	public function actionIndex()
	{
		$this->render('selectchild', array('childObjs' => $this->myChildObjs));
	}

	public function actionStep1()
	{
		$cs = Yii::app()->clientScript;
		$cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/app/js/signature_pad.min.js');

		$this->subPageTitle = $this->config[1];
		$this->showModel = false;
		$this->step = 1;

		$oldModel = $this->getOldModel();

		$form = new RegStep1Form();
		if ($oldModel) {
			$form->attributes = CJSON::decode($oldModel->data);
		}
		if (isset($_POST['RegStep1Form'])) {
			$form->attributes = $_POST['RegStep1Form'];

			if ($form->validate()) {
				// 上传签名
				if ($form->uploadFile()) {
					$_POST['RegStep1Form']['filedata'] = $form->filedata;
					if($this->saveModel($_POST['RegStep1Form'], $oldModel)){
						$nextStep = $this->getSteps(RegExtraInfo::PROCESS_BUS);
						$this->redirect($this->createUrl($nextStep));
					}
				} else {
					Yii::app()->user->setFlash('err', array(array(Yii::t("reg", "Signature save failed"))));
				}
			}else{
				Yii::app()->user->setFlash('err', $form->getErrors());
			}
		}

        // $criteria = new CDbCriteria();
        // $criteria->compare('reg_id', $this->regStudent->reg_id);
        // $criteria->compare('schoolid', $this->regStudent->schoolid);
        // $regCarSite = RegCarSite::model()->findAll($criteria);
        $regCarSite = RegCarSite::getCarsite($this->regStudent->reg_id, $this->regStudent->schoolid);
        $carSiteList = array();
        if($regCarSite){
            foreach ($regCarSite as $item){
                $carSiteList[] = array(
                    'id' => $item->id,
                    'name_cn' => $item->name_cn,
                    'name_en' => $item->name_en,
                    'park_address_cn' => $item->park_address_cn,
                    'park_address_en' => $item->park_address_en,
                    'site_name' => $item->site_name,
                    'longitude' => $item->longitude,
                    'latitude' => $item->latitude,
                    'created_at' => date("Y-m-d", $item->created_at),
                );
            }
        }

        $this->render('step1', array('formModel' => $form, 'carSiteList' => $carSiteList));
	}

	public function actionStep2()
	{
		$cs = Yii::app()->clientScript;
		$cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/app/js/cropper.min.js?20190531');
		$cs->registerCssFile(Yii::app()->themeManager->baseUrl.'/app/css/cropper.min.css');

        $this->subPageTitle = $this->config[2];
		$this->step = 2;
		$oldModel = $this->getOldModel();

		$form = new RegStep2Form();
		$form1 = new RegStep2Form();
		$form2 = new RegStep2Form();
		$form3 = new RegStep2Form();

		$carInfo = '';
		if ($oldModel) {
			$data = CJSON::decode($oldModel->data);
			$carInfo = $oldModel->memo;
			$form->attributes = $data[0];
			$form1->attributes = $data[1];
			$form2->attributes = $data[2];
			$form3->attributes = $data[3];
		}
		$formArray = array($form, $form1, $form2, $form3);

		if(isset($_POST['RegStep2Form'])){
		    $photo = false;
		    if($this->childObj->schoolid == "BJ_DS" || $this->childObj->schoolid == "BJ_SLT"){
                $photo = true;
            }else {
                if ($this->childObj->photo && $this->childObj->photo != "blank.gif") {
                    $photo = true;
                }
            }
		   
		    if($photo ) {
                $form->attributes = $_POST['RegStep2Form'][0];
                // $form->photoFile = $form->photo;

                $form1->attributes = $_POST['RegStep2Form'][1];
                // $form1->photoFile = $form1->photo;

                $form2->attributes = $_POST['RegStep2Form'][2];
                // $form2->photoFile = $form2->photo;

                $form3->attributes = $_POST['RegStep2Form'][3];
                // $form3->photoFile = $form3->photo;

                if (!$form->name && !$form->relation && !$form->tel && !$form->photoFile) {
                    $valid = false;
                    $form->addError($form->name, Yii::t('reg', 'Minimum of 1 person is needed'));
                } else {
                    $valid = $form->validate();
                    $valid = $form1->validate() && $valid;
                    $valid = $form2->validate() && $valid;
                    $valid = $form3->validate() && $valid;
                }

                if ($valid) {
                    $oss = CommonUtils::initOSS('private');
                    if (strpos($form->photo, 'base64') !== false) {
                        $form->photoFile = $form->photo;
                        $form->photo = 'picker_' . time() . '_' . uniqid() . '.jpg';
                        $form->upload($oss);
                        $_POST['RegStep2Form'][0]['photo'] = $form->photo;
                    }
                    if (strpos($form1->photo, 'base64') !== false) {
                        $form1->photoFile = $form1->photo;
                        $form1->photo = 'picker_' . time() . '_' . uniqid() . '.jpg';
                        $form1->upload($oss);
                        $_POST['RegStep2Form'][1]['photo'] = $form1->photo;
                    }
                    if (strpos($form2->photo, 'base64') !== false) {
                        $form2->photoFile = $form2->photo;
                        $form2->photo = 'picker_' . time() . '_' . uniqid() . '.jpg';
                        $form2->upload($oss);
                        $_POST['RegStep2Form'][2]['photo'] = $form2->photo;
                    }
                    if (strpos($form3->photo, 'base64') !== false) {
                        $form3->photoFile = $form3->photo;
                        $form3->photo = 'picker_' . time() . '_' . uniqid() . '.jpg';
                        $form3->upload($oss);
                        $_POST['RegStep2Form'][3]['photo'] = $form3->photo;
                    }
                    unset($_POST['RegStep2Form'][0]['photoFile']);
                    unset($_POST['RegStep2Form'][1]['photoFile']);
                    unset($_POST['RegStep2Form'][2]['photoFile']);
                    unset($_POST['RegStep2Form'][3]['photoFile']);

                    if ($this->saveModel($_POST['RegStep2Form'], $oldModel)) {
                        $nextStep = $this->getSteps(RegExtraInfo::PROCESS_CARD);
                        $this->redirect($this->createUrl($nextStep));
                    }
                } else {
                    $errs = current($form->getErrors());
                    $errs = !$errs ? current($form1->getErrors()) : $errs;
                    $errs = !$errs ? current($form2->getErrors()) : $errs;
                    $errs = !$errs ? current($form3->getErrors()) : $errs;
                    Yii::app()->user->setFlash('err', array($errs));
                }
            }else{
                Yii::app()->user->setFlash('err', array(array("请先上传孩子头像")));
            }
		}

        $this->render('step2', array('formArray' => $formArray, 'carInfo' => $carInfo));
	}

	public function actionStep3()
	{
        $this->subPageTitle = $this->config[3];
		$this->step = 3;

		$oldModel = $this->getOldModel();

		$form = new RegStep3Form();

		if ($oldModel) {
			$form->attributes = CJSON::decode($oldModel->data);
		}

		if($_POST){
			$form->attributes = $_POST['RegStep3Form'];
			$form->gender = $this->childObj->gender;
			if ($form->validate()) {
				if($this->saveModel($_POST['RegStep3Form'], $oldModel)){
					$nextStep = $this->getSteps(RegExtraInfo::PROCESS_UNIFORM);
					$this->redirect($this->createUrl($nextStep));
				}
			}else{
				Yii::app()->user->setFlash('err', $form->getErrors());
			}
		}
		$form->gender = $this->childObj->gender;
        $this->render('step3', array('formModel3' => $form, 'childObj' => $this->reserveObj));
	}

	public function actionStep4()
	{
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/app/js/signature_pad.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/app/js/exif.js');
		Yii::import('common.models.visit.AdmissionsDs');
        $this->subPageTitle = $this->config[4];
		$this->showModel = true;
		$this->step = 4;

		$config = RegStep4Form::StudentHealthHistory();
		$oldModel = $this->getOldModel();

		$form = new RegStep4Form();
		$form->setScenario('submit');
		if ($oldModel) {
			$form->attributes = CJSON::decode($oldModel->data);
		}

		if(isset($this->childObj->admission_id)){
			$adminssionObj = AdmissionsDs::model()->findByPk($this->childObj->admission_id);
			if(empty($form)){
				$form->oneEmergencyName = $adminssionObj->emergency_contact_name;
				$form->oneEmergencyPhone = $adminssionObj->emergency_contact_phone;
			}
		}
		if (isset($_POST['RegStep4Form'])) {
			$form->attributes = $_POST['RegStep4Form'];
			if ($form->validate()) {
                // if (!isset($_POST['RegStep4Form']['medicalReport'])) {
                // 	Yii::app()->user->setFlash('err', array(array(Yii::t("reg", "Physical examination report must be uploaded"))));
                // 	$this->render('step4', array('formModel4' => $form, 'config' => $config));
                // 	Yii::app()->end();
                // }
//                if (!isset($_POST['RegStep4Form']['vaccineReport'])) {
//                	Yii::app()->user->setFlash('err', array(array(Yii::t("reg", "Vaccine report must be uploaded"))));
//                	$this->render('step4', array('formModel4' => $form, 'config' => $config));
//                	Yii::app()->end();
//                }
                if ($form->updataSignature()) {
                    $_POST['RegStep4Form']['filedata'] = $form->filedata;
                    if ($this->saveModel($_POST['RegStep4Form'], $oldModel)) {
                        $nextStep = $this->getSteps(RegExtraInfo::PROCESS_MEDICAL);
                        $this->redirect($this->createUrl($nextStep));
                    }
                }else {
                    Yii::app()->user->setFlash('err', array(array(Yii::t("reg", "Signature save failed"))));
                }
			}else{
				Yii::app()->user->setFlash('err', $form->getErrors());
			}
		}

        $this->render('step4', array('formModel4' => $form, 'config' => $config));
	}

	public function actionStep5()
	{
        $this->subPageTitle = $this->config[5];
		$this->showModel = true;
		$this->step = 5;

		$oldModel = $this->getOldModel();
		$form = new RegStep5Form();
		if ($oldModel) {
			$form->attributes = CJSON::decode($oldModel->data);
		}

		if($_POST){
			$form->attributes = $_POST['RegStep5Form'];
			if ($form->validate()) {
				if($this->saveModel($_POST['RegStep5Form'], $oldModel)){
					$nextStep = $this->getSteps(RegExtraInfo::PROCESS_LUNCH);
					$this->redirect($this->createUrl($nextStep));
				}
			}else{
				Yii::app()->user->setFlash('err', $form->getErrors());
			}
		}

        $this->render('step5', array('formModel5' => $form));
	}

	public function actionStep6()
	{
		Yii::import('common.models.visit.AdmissionsDs');
        $this->subPageTitle = $this->config[6];
		$this->showModel = true;
		$this->step = 6;

		$oldModel = $this->getOldModel();
		$form = new RegStep6Form();
		if ($oldModel) {
			$this->showModel = false;
			$form->attributes = CJSON::decode($oldModel->data);
		}

		if(isset($this->childObj->admission_id)){
			if(empty($form)){
				$adminssionObj = AdmissionsDs::model()->findByPk($this->childObj->admission_id);
				$form->firstLanguage = $adminssionObj->native_lang;
			}
		}

		if($_POST){
			$form->attributes = $_POST['RegStep6Form'];
			if ($form->validate()) {
				if($this->saveModel($_POST['RegStep6Form'], $oldModel)){
					$nextStep = $this->getSteps(RegExtraInfo::PROCESS_LANG);
					$this->redirect($this->createUrl($nextStep));
				}
			}else{
				Yii::app()->user->setFlash('err', $form->getErrors());
			}
		}

		$this->render('step6', array('formModel6' => $form));
	}

	public function actionStep7()
	{
		$cs = Yii::app()->clientScript;
		$cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/app/js/signature_pad.min.js');
        $this->subPageTitle = $this->config[7];
		$this->showModel = true;
		$this->step = 7;

		$oldModel = $this->getOldModel();

		$form = new RegStep7Form();
		if ($oldModel) {
			$form->attributes = CJSON::decode($oldModel->data);
		}


		if($_POST){
			$form->attributes = $_POST['RegStep7Form'];
            if ($form->validate()) {
                // 上传签名
                if ($form->uploadFile()) {
                    $_POST['RegStep7Form']['filedata'] = $form->filedata;
                    if($this->saveModel($_POST['RegStep7Form'], $oldModel)){
                        $nextStep = $this->getSteps(RegExtraInfo::PROCESS_FEE);
                        $this->redirect($this->createUrl($nextStep));
                    }
                } else {
                    Yii::app()->user->setFlash('err', array(array(Yii::t("reg", "Signature save failed"))));
                }
            }else{
                Yii::app()->user->setFlash('err', $form->getErrors());
            }
		}

		$this->render('step7', array('formModel7' => $form));
	}

    public function actionStep8()
    {
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/app/js/signature_pad.min.js');
        $this->subPageTitle = $this->config[8];
        $this->showModel = true;
        $this->step = 8;

        $oldModel = $this->getOldModel();

        $form = new RegStep8Form();
        if ($oldModel) {
            $form->attributes = CJSON::decode($oldModel->data);
        }

        if($_POST){
            $form->attributes = $_POST['RegStep8Form'];
            if ($form->validate()) {
                // 上传签名
                if ($form->uploadFile()) {
                    $_POST['RegStep8Form']['filedata'] = $form->filedata;
                    if($this->saveModel($_POST['RegStep8Form'], $oldModel)){
                        $nextStep = $this->getSteps(RegExtraInfo::PROCESS_SAFE);
                        $this->redirect($this->createUrl($nextStep));
                    }
                } else {
                    Yii::app()->user->setFlash('err', array(array(Yii::t("reg", "Signature save failed"))));
                }
            }else{
                Yii::app()->user->setFlash('err', $form->getErrors());
            }
        }

        $this->render('step8', array('formModel8' => $form));
    }

    public function actionStep9()
    {
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/app/js/signature_pad.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/app/js/exif.js');
        Yii::import('common.models.visit.AdmissionsDs');
        $this->subPageTitle = $this->config[9];
        $this->showModel = true;
        $this->step = 9;
        //$config = RegStep9Form::StudentHealthHistory();
        $oldModel = $this->getOldModel();

        $form = new RegStep9Form();
        $form->setScenario('submit');

        if (isset($_POST['RegStep9Form'])) {
            $form->attributes = $_POST['RegStep9Form'];
            if ($form->validate()) {
                if($this->saveModel($_POST['RegStep9Form'], $oldModel)){
                    $nextStep = $this->getSteps(RegExtraInfo::PROCESS_FAMILYID);

                    $this->redirect($this->createUrl($nextStep));
                }
            }else{
                Yii::app()->user->setFlash('err', $form->getErrors());
            }
        }
        if ($oldModel) {
            $form->attributes = CJSON::decode($oldModel->data);
        }

        $this->render('step9', array('formModel9' => $form));
    }

    public function actionStep10()
    {
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/app/js/signature_pad.min.js');
        $this->subPageTitle = $this->config[10];
        $this->showModel = true;
        $this->step = 10;

        $oldModel = $this->getOldModel();
        $form = new RegStep10Form();

        if ($oldModel) {
            $form->attributes = CJSON::decode($oldModel->data);
        }

        if($_POST){
            if(Yii::app()->language == 'zh_cn'){
            //if($this->childObj->country == 36 || $this->childObj->country = 175){
                $form->setScenario("regChildCn");
            }else{
                $form->setScenario("regChildEn");
            }
            $person1 = 1;
            $person2 = 1;
            $form->attributes = $_POST['RegStep10Form'];
            if(!$form->personName1 || !$form->relationship1 || !$form->contactNumber1){
                $person1 = 0;
            }

            if(!$form->personName2 || !$form->relationship2 || !$form->contactNumber2){
                $person2 = 0;
            }

            if($person2 || $person1) {
                if ($form->validate()) {
                    // 上传签名
                    if ($form->uploadFile()) {
                        $_POST['RegStep10Form']['filedata'] = $form->filedata;
                        if ($this->saveModel($_POST['RegStep10Form'], $oldModel)) {
                            $nextStep = $this->getSteps(RegExtraInfo::PROCESS_INFORMATION);
                            $this->redirect($this->createUrl($nextStep));
                        }
                    } else {
                        Yii::app()->user->setFlash('err', array(array(Yii::t("reg", "Signature save failed"))));
                    }
                } else {
                    Yii::app()->user->setFlash('err', $form->getErrors());
                }
            }else{
                Yii::app()->user->setFlash('err', array(array(Yii::t("reg", "紧急联系人最少写全一个"))));
            }
        }

        $this->render('step10', array('formModel10' => $form, 'country' => $this->childObj->country));
    }

    public function actionStep11()
    {
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/app/js/signature_pad.min.js');
        $this->subPageTitle = $this->config[11];
        $this->showModel = true;
        $this->step = 11;

        $oldModel = $this->getOldModel();

        $form = new RegStep11Form();
        if ($oldModel) {
            $form->attributes = CJSON::decode($oldModel->data);
        }

        if($_POST){
            $form->attributes = $_POST['RegStep11Form'];
            if ($form->validate()) {
                if($this->saveModel($_POST['RegStep11Form'], $oldModel)){
                    $nextStep = $this->getSteps(RegExtraInfo::PROCESS_MATERIAL);
                    $this->redirect($this->createUrl($nextStep));
                }
            }else{
                Yii::app()->user->setFlash('err', $form->getErrors());
            }
        }

        $this->render('step11', array('formModel11' => $form));
    }

    public function actionUploadFile()
    {
    	$fileName = Yii::app()->request->getParam('fileName');
    	$this->addMessage('state', 'fail');
    	if (!in_array($fileName, array('medicalReportFile', 'vaccineReportFile', 'healthReportFile'))) {
    		$this->addMessage('message', Yii::t("reg", "File name error"));
    		$this->showMessage();
    	}
    	$form = new RegStep4Form();
    	$form->setScenario('uploadFile');
    	$form->$fileName = CUploadedFile::getInstance($form, $fileName);
    	if (!$form->$fileName) {
    		$this->addMessage('message',Yii::t("reg", "File cannot be empty"));
    		$this->showMessage();
    	}

    	if (!$form->validate()) {
    		$error = current($form->getErrors());
    		$this->addMessage('message', $error[0]);
    		$this->showMessage();
    	}
    	// 上传文件
    	$oss = CommonUtils::initOSSCS('private');
		$saveName = $fileName . '_' . time() . '_' .uniqid() . '.' . $form->$fileName->getExtensionName();
		$res = $oss->uploadFile('regextrainfo/' . $saveName, $form->$fileName->getTempName());
		if ($res === false) {
			$this->addMessage('message', Yii::t("reg", "File upload failed"));
			$this->showMessage();
		}
		$data = array('fileName' => $saveName, 'fileUrl' => $form->getOssImageUrl($saveName));
		$this->addMessage('state', 'success');
		$this->addMessage('message', 'success');
		$this->addMessage('data', $data);
		$this->showMessage();
    }

    public function actionUploadFileIvy()
    {
        $fileName = Yii::app()->request->getParam('fileName');
        $this->addMessage('state', 'fail');

        if (!in_array($fileName, array('studentdataFile', 'parentsdataFile', 'vaccinedataFile', 'insurancedataFile'))) {
            $this->addMessage('message', Yii::t("reg", "File name error"));
            $this->showMessage();
        }

        $form = new RegStep9Form();
        $form->setScenario('uploadFile');
        $form->$fileName = CUploadedFile::getInstance($form, $fileName);
        if (!$form->$fileName) {
            $this->addMessage('message',Yii::t("reg", "File cannot be empty"));
            $this->showMessage();
        }

        if (!$form->validate()) {
            $error = current($form->getErrors());
            $this->addMessage('message', $error[0]);
            $this->showMessage();
        }
        // 上传文件
        $oss = CommonUtils::initOSSCS('private');
        $saveName = $fileName . '_' . time() . '_' .uniqid() . '.' . $form->$fileName->getExtensionName();
        $res = $oss->uploadFile('regextrainfo/' . $saveName, $form->$fileName->getTempName());
        if ($res === false) {
            $this->addMessage('message', Yii::t("reg", "File upload failed"));
            $this->showMessage();
        }
        $data = array('fileName' => $saveName, 'fileUrl' => $form->getOssImageUrl($saveName));
        $this->addMessage('state', 'success');
        $this->addMessage('message', 'success');
        $this->addMessage('data', $data);
        $this->showMessage();
    }

    public function getStepTitle($step)
    {
    	$config = RegExtraInfo::getStepConfig();
    	return isset($config[$step]) ? $config[$step] : '';
    }

	public function saveModel($data, $oldModel)
	{
		// 验证是否已过填写时间
		if (!$this->isOpen) {
			Yii::app()->user->setFlash('err', array(array(Yii::t("reg", "Registration closed"))));
			return false;
		}
		$complete = 0;
		// 查找验证老记录
		if ($oldModel) {
			if (!$oldModel->checkStatus()) {
				Yii::app()->user->setFlash('err', $oldModel->getErrors());
				return false;
			}
			$oldModel->status = 0;
			$oldModel->save();

			$complete = $oldModel->complete;
			if ($oldModel->complete == -1) {
				$complete = 2;
			}
		}
		// 保存新纪录
		$model = new RegExtraInfo();
		$model->reg_id = $this->regStudent->reg_id;
		$model->childid = $this->childid;
		$model->schoolid = $this->regStudent->schoolid;
		$model->openid = $this->openid ? $this->openid : '';
		$model->parent_id = $this->parent->pid;
		$model->parent_phone = $this->parent->mphone ? $this->parent->mphone : '';
		$model->step = $this->step;
		$model->data = CJSON::encode($data);
		$model->status = 1;
		$model->complete = $complete;
		$model->link_id = $oldModel ? $oldModel->id : 0;
		$model->memo = $_POST['memo'];
		$model->updated = time();
		if ($model->save()) {
			$this->regStudent->updateStatus();
			return true;
		}else{
			Yii::app()->user->setFlash('err', $model->getErrors());
			return false;
		}
	}

	public function getOldModel()
	{
		Yii::app()->user->setFlash('err', null);
		$criteria = new CDbCriteria();
		$criteria->compare('childid', $this->childid);
		$criteria->compare('step', $this->step);
		$criteria->compare('status', 1);
		$criteria->compare('reg_id', $this->regStudent->reg_id);
		$criteria->order = 'updated desc';
		$oldModel = RegExtraInfo::model()->find($criteria);
		if ($oldModel) {
			if ($oldModel->complete == -1 && $oldModel->reject_memo) {
				Yii::app()->user->setFlash('err', array(array(Yii::t("reg", "Reject reason: "). $oldModel->reject_memo)));
			}
		}
		return $oldModel;
	}

	public function actionComplete()
	{
		$this->subPageTitle = Yii::t('reg', 'Complete');

		$this->render('complete');
	}

	public function getSteps($step, $flag = 'next', $schoolid = false)
	{
		$steps = $this->steps;
		if ($schoolid) {
			$steps = RegExtraInfo::getStepsBySchool($schoolid);;
		}
		if (!$steps) {
			return 'index';
		}
		if ($flag == 'first') {
			return 'step'. $steps[0];
		}
		if ($flag == 'last') {
			return end($steps);
		}
		if ($flag == 'next') {
			$ckey = array_search($step, $steps);
			$action = 'step1';
			if ($ckey !== false) {
				if (count($steps) == $ckey + 1) {
					$action = 'complete';
				} else {
					$action = 'step' . $steps[$ckey + 1];
				}
			}
		} elseif ($flag == 'pre') {
			$ckey = array_search($step, $steps);
			$action = 'step1';
			if ($ckey !== false) {
				if ($ckey == 0) {
					$action = 'index';
				} else {
					$action = 'step' . $steps[$ckey-1];
				}
			}
		}
		return $action;
	}
}
