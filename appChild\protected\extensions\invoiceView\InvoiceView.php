<?php
class InvoiceView extends CWidget
{
	public $invoiceId;
	public $childId;
	public $childName;
	
	public function init()
	{
		Yii::import('common.models.Invoice.*');
	}
	
	public function run()
	{
		$invoice = null;
		$transation = null;
		$childName = null;
		$schoolName = null;
		$className = null;
		$depositAmount = 0;
		$discountName = '';
		$lunchStr = '';
		$userName='';
		$childServiceInfo ='';
		if ($this->invoiceId)
		{
			$HtoolKits = Mims::LoadHelper('HtoolKits');
			$invoice = Invoice::model()->findByPk($this->invoiceId);
			if ($invoice->childid != $this->childId) return;
			if ($invoice->status == 20 || $invoice->status == 30)
			{
				$transation = InvoiceTransaction::model()->findAll('invoice_id=:invoice_id',array('invoice_id'=>$this->invoiceId));
				if (is_array($transation) && count($transation))
				{
					foreach ($transation as $v)
					{
						$transationId[] = $v->id;
					}
				}
				$criteria = new CDbCriteria;
				$criteria->compare('`inout`', 'out');
				$criteria->compare('childid', $invoice->childid);
				$criteria->compare('tran_id', $transationId);
				$depositHisObj = DepositHistory::model()->find($criteria);
				$depositAmount = (is_object($depositHisObj) && count($depositHisObj)) ? $depositHisObj->amount : 0;

				if ($invoice->startdate ==0 && $invoice->enddate==0 && $invoice->payment_type == 'lunch' && $invoice->inout == 'out')
				{
					$criteria = new CDbCriteria;
					$criteria->compare('transaction_id',$transationId);
					$criteria->compare('`inout`', 'in');
					$lunch = ChildCredit::model()->with('refundlunch')->lunch()->findAll($criteria);
					if (is_array($lunch) && count($lunch))
					{
						foreach ($lunch as $v)
						{
							if (empty($lunchStr))
							{
								$lunchStr .= addColon(Yii::t("payment", 'Refund Lunch')).date('Y-m-d',$v->refundlunch[0]->target_timestamp);
							}
							else
							{
								$lunchStr .= ','.date('Y-m-d',$v->refundlunch[0]->target_timestamp);
							}
						}
					}
				}
			}
			elseif ($invoice->status == 10)
			{
				$depositAmount = TemporaryDeposit::model()->getDeposit($this->invoiceId, 'amount');
			}
			$schoolName = Branch::model()->getBranchInfo($invoice->getAttribute("schoolid"), 'title');
			$className = IvyClass::model()->getClassInfo($invoice->getAttribute("classid"), 'title');
			
			//折扣
			if ($invoice->discount_id)
			{
				$criteria = new CDbCriteria;
				$dicount = DiscountSchool::model()->with('discountTitle')->dis()->findByPk($invoice->discount_id);
				$discountName = $HtoolKits->getContentByLang($dicount->discountTitle->title_cn,$dicount->discountTitle->title_en);
			}
			$schoolYear = '';
			$latepayRate = '';
			if ( $invoice->payment_type == 'deposit' ||   $invoice->payment_type == 'registration')
			{
				Yii::import('common.models.calendar.Calendar');
				$startYear = Calendar::model()->getCalendarInfo($invoice->calendar_id,'startyear');
				$schoolYear = $startYear ."-".($startYear+1);
			}
			if ($invoice->userid)
			{
				$userInfo = User::model()->active()->findByPk($invoice->userid);
				$userName = $userInfo->uname.'('.$userInfo->name.')';
			}
			if ($invoice->child_service_info)
			{
				$childServiceInfo = unserialize($invoice->child_service_info);
			}
			$previousInvoice = null;
			if ($invoice->payment_type == 'latepayment')
			{
				$previousInvoice = Invoice::model()->findByPk($invoice->latepay_id);
				//GET SCHOOL LATE FEE CONFIG
				$criteria = new CDbCriteria;
				$criteria->compare('schoolid',$invoice->schoolid);
				$criteria->compare('feemgt_type','no_yid');
				$criteria->select = 'latepay_rate';
				$latepayRateObj = FeemgtSchoolConfig::model()->find($criteria);
				$latepayRate = $latepayRateObj->latepay_rate * 100;
			}
		}
		
		$this->render('invoiceView', array('invoModel'=>$invoice,
				'tranModel'=>$transation,
				'childName'=>$this->childName,
				'schoolName'=>$schoolName,
				'depositAmount'=>$depositAmount,
				'discountName'=>$discountName,
				'lunchStr'=>$lunchStr,
				'schoolYear'=>$schoolYear,
				'userName'=>$userName,
				'childServiceInfo'=>$childServiceInfo,
				'latepayRate'=>$latepayRate,
				'previousInvoice'=>$previousInvoice,
				'className'=>$className)
		);
	}
}