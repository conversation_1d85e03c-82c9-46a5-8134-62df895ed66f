<?php

class LoginPage extends CWidget {
    /*
     * 初始化 设定属性的默认值
     * 此方法会被 CController::beginWidget() 调用
     */

    public function init() {
        Yii::import('common.models.resource.Testimonials');
    }

    /*
     * 此方法会被 CController::endWidget() 调用
     */

    public function run() {
        $display_palce = Yii::app()->language == 'zh_cn' ? 1 : 2;

        $tes_cri = new CDbCriteria();
        $tes_cri->compare('status', 0);
        $tes_cri->compare('place', $display_palce);
        $tes_cri->order = 'id DESC';
        $tes_cri->limit = 3;
        $testimonialList = Testimonials::model()->findAll($tes_cri);
        $this->render('loginPage', array('testimonialList' => $testimonialList));
    }

}