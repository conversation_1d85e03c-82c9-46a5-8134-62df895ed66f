<a name="anchor_<?php echo $data->id?>"></a>
<div class="product">
    <?php
    $colorbox->addInstance('.img'.$data->id,array('rel'=>'img'.$data->id, 'iframe'=>false, 'height'=>'90%'));
    $imagesUrl = null;
    if (!empty($data->cover)):
        $imgUrl = Yii::app()->params['uploadBaseUrl'] . 'points/thumb/' . $data->Images[$data->cover]->image;
		$imagesUrl = Yii::app()->params['uploadBaseUrl'] . 'points/' . $data->Images[$data->cover]->image;
        $img = CHtml::image($imgUrl, '', array('class' => 'fl cover', 'width' => 120));
        echo CHtml::link($img, $imagesUrl,array('class'=>'img'.$data->id));
    else:
        $imgUrl = Yii::app()->theme->baseUrl.'\images\pic_loading_s.gif';
        echo CHtml::image($imgUrl, '', array('class' => 'fl cover', 'width' => 120));
    endif;
    ?>
    <h5>
        <?php
        echo CHtml::encode($data->getContent("title"));
        ?>
    </h5>
    
    <div style="display:none;">
    <?php  
    if (!empty($data->Images)):
   		foreach ($data->Images as $imgs):
    		if ($imgs->id != $data->cover):
    ?>
    			<a class="img<?php echo $data->id;?>" href="<?php echo Yii::app()->params['uploadBaseUrl'] . 'points/' . $imgs->image;?>"></a>
    <?php
    		endif;
     	endforeach;
     endif;
     ?>
    </div>
    <p>
        <?php echo Yii::t('payment', ':points points', array(':points'=> '<span class="pt" id="itempoint_'.$data->id.'">'.$data->credits.'</span>' )) ?> ×
        <input type="number" value="1" id="orderNumber_<?php echo $data->id;?>" class="p-number" />
        <span id="e_<?php echo $data->id?>">
            <?php
			$_t = ($credits < $data->credits) ? Yii::t("payment", 'Insufficient Ivy Points') : '';
			$_t = ($_t == "") ? ( ($data->stock < 1)? Yii::t("payment", "Temporarily out of stock. We will replenish as soon as possible!") : $_t ) : $_t;
            echo CHtml::Button(Yii::t('payment', 'Order'), array( 'title'=> $_t , 'disabled' => ($credits < $data->credits || !$data->stock) ? 'disabled' : '', 'class'=>'e-exch', 'id'=>$data->id, 'name'=>'orderBtn'.$data->id)) ?>
        </span>
    </p>
	<p class="stock">
		<?php echo ( $data->stock ) ? Yii::t("payment", "In Stock: :stock", array(":stock"=>'<span>'.$data->stock.'</span>')) : Yii::t('payment','Temporarily out of stock. We will replenish as soon as possible!');?>
	</p>
    <p class="desc">
        <?php echo CHtml::encode($data->getContent("memo")); ?>
    </p>
    <div class="sep"></div>
</div>
