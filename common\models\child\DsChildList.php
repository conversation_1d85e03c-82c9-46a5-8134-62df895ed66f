<?php

/**
 * This is the model class for table "ds_child_list".
 *
 * The followings are the available columns in table 'ds_child_list':
 * @property integer $id
 * @property integer $student_number
 * @property string $first_name
 * @property string $middle_name
 * @property string $last_name
 * @property string $nickname
 * @property string $name_cn
 * @property string $gender
 * @property string $birthday
 * @property string $native_language
 * @property string $nationality
 * @property string $grade
 * @property string $mother_first_name
 * @property string $mother_last_name
 * @property string $mother_chinese_name
 * @property string $mother_email
 * @property string $mother_phone
 * @property string $father_first_name
 * @property string $father_last_name
 * @property string $father_email
 * @property string $father_phone
 * @property string $home_no
 * @property string $address_english
 * @property string $address_chinese
 * @property string $bus_route
 * @property string $allergies
 * @property string $healthcheck
 * @property string $emergency_contact
 * @property string $other_emergency_contact
 * @property integer $status
 */
class DsChildList extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return DsChildList the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ds_child_list';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('student_number, first_name, gender, birthday, native_language, nationality, mother_email, mother_phone, father_email, father_phone', 'required'),
			array('student_number, status,childid', 'numerical', 'integerOnly'=>true),
			array('first_name, middle_name, last_name, nickname, name_cn, grade, address_chinese, bus_route, allergies, emergency_contact, other_emergency_contact', 'length', 'max'=>255),
			array('gender, mother_chinese_name, healthcheck,father_chinese_name', 'length', 'max'=>10),
			array('birthday', 'length', 'max'=>20),
			array('native_language, nationality, mother_last_name, father_first_name, father_last_name', 'length', 'max'=>50),
			array('mother_first_name, mother_phone, father_phone, home_no, address_english', 'length', 'max'=>30),
			array('mother_email, father_email', 'length', 'max'=>100),
            array('error,childid,schoolid','safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, student_number, first_name, middle_name, last_name, nickname, name_cn, gender, birthday, native_language, nationality, grade, mother_first_name, mother_last_name, mother_chinese_name, father_chinese_name, mother_email, mother_phone, father_first_name, father_last_name, father_email, father_phone, home_no, address_english, address_chinese, bus_route, allergies, healthcheck, emergency_contact, other_emergency_contact, status, error,childid,schoolid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'student_number' => 'Student Number',
			'first_name' => 'First Name',
			'middle_name' => 'Middle Name',
			'last_name' => 'Last Name',
			'nickname' => 'Nickname',
			'name_cn' => 'Name Cn',
			'gender' => 'Gender',
			'birthday' => 'Birthday',
			'native_language' => 'Native Language',
			'nationality' => 'Nationality',
			'grade' => 'Grade',
			'mother_first_name' => 'Mother First Name',
			'mother_last_name' => 'Mother Last Name',
			'mother_chinese_name' => 'Mother Chinese Name',
			'mother_email' => 'Mother Email',
			'mother_phone' => 'Mother Phone',
			'father_first_name' => 'Father First Name',
			'father_last_name' => 'Father Last Name',
			'father_email' => 'Father Email',
			'father_phone' => 'Father Phone',
			'home_no' => 'Home No',
			'address_english' => 'Address English',
			'address_chinese' => 'Address Chinese',
			'bus_route' => 'Bus Route',
			'allergies' => 'Allergies',
			'healthcheck' => 'Healthcheck',
			'emergency_contact' => 'Emergency Contact',
			'other_emergency_contact' => 'Other Emergency Contact',
			'status' => 'Status',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('student_number',$this->student_number);
		$criteria->compare('first_name',$this->first_name,true);
		$criteria->compare('middle_name',$this->middle_name,true);
		$criteria->compare('last_name',$this->last_name,true);
		$criteria->compare('nickname',$this->nickname,true);
		$criteria->compare('name_cn',$this->name_cn,true);
		$criteria->compare('gender',$this->gender,true);
		$criteria->compare('birthday',$this->birthday,true);
		$criteria->compare('native_language',$this->native_language,true);
		$criteria->compare('nationality',$this->nationality,true);
		$criteria->compare('grade',$this->grade,true);
		$criteria->compare('mother_first_name',$this->mother_first_name,true);
		$criteria->compare('mother_last_name',$this->mother_last_name,true);
		$criteria->compare('mother_chinese_name',$this->mother_chinese_name,true);
		$criteria->compare('mother_email',$this->mother_email,true);
		$criteria->compare('mother_phone',$this->mother_phone,true);
		$criteria->compare('father_first_name',$this->father_first_name,true);
		$criteria->compare('father_last_name',$this->father_last_name,true);
		$criteria->compare('father_email',$this->father_email,true);
		$criteria->compare('father_phone',$this->father_phone,true);
		$criteria->compare('home_no',$this->home_no,true);
		$criteria->compare('address_english',$this->address_english,true);
		$criteria->compare('address_chinese',$this->address_chinese,true);
		$criteria->compare('bus_route',$this->bus_route,true);
		$criteria->compare('allergies',$this->allergies,true);
		$criteria->compare('healthcheck',$this->healthcheck,true);
		$criteria->compare('emergency_contact',$this->emergency_contact,true);
		$criteria->compare('other_emergency_contact',$this->other_emergency_contact,true);
		$criteria->compare('status',$this->status);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}