<?php

/*
 * 校园升学控制器
 */
class TransferController extends BranchBasedController{
    public $actionAccessAuths = array(
        'index'             => 'o_A_Access',
        'appsave'           => 'o_A_Adm_Student',
        'getclass'          => 'o_A_Adm_Student',
        'delete'            => 'o_A_Adm_Student',
        'confirm'           => 'o_A_Adm_Student',
    );

    public $type;
    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";
        Yii::import('common.models.child.TransferSchoolHistory');

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mcampus/transfer/index');

        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile($cs->getCoreScriptUrl().'/jui/js/jquery-ui.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/jquery.jPrintArea.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/xlsx.full.min.js');
    }

    /*
     * 转校或升学列表
     */
    public function actionIndex(){
        Yii::import('common.models.calendar.CalendarSchool');
        Yii::import('common.models.calendar.Calendar');
        $type = Yii::app()->request->getParam('type','in');
        $startTime = Yii::app()->request->getParam('startTime','');
        $endTime = Yii::app()->request->getParam('endTime','');
        $this->type = $type;

        $status = 1;
        if($startTime || $endTime){
            $status = 2;
            if($startTime && $endTime){
                $status = 1;
                $statrTime = strtotime($startTime);
                $endTime = strtotime($endTime) + 86400;
                if($statrTime > $endTime){
                    $status = 3;
                }

                $gapTime = $endTime - $statrTime;
                if($gapTime > 86400*60){
                    $status = 3;
                }
            }
        }
        $dataProvider = '';
        if($status == 1) {
            $criteria = new CDbCriteria();
            if ($type == 'in') {
                $criteria->compare('t.to_schoolid', $this->branchId);
            } else {
                $criteria->compare('t.from_schoolid', $this->branchId);
            }

            $criteria->compare('t.transfer_time', ">=$statrTime");
            $criteria->compare('t.transfer_time', "<=$endTime");

            $criteria->with = array('child', 'class');
            $dataProvider = new CActiveDataProvider('TransferSchoolHistory', array(
                'criteria' => $criteria,
                'sort' => array(
                    'defaultOrder' => 't.status asc,t.transfer_time Desc'
                ),
                'pagination' => array(
                    'pageSize' => 15,
                ),
            ));
        }
        Yii::app()->clientScript->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');

        $this->render('index',array(
            'type'=>$type,
            'transModel'=>$dataProvider,
            'status'=>$status,
        ));
    }

    /*
     * 转校或升学申请保存
     */
    public function actionAppSave(){
        if (Yii::app()->request->isAjaxRequest && isset($_POST['TransferSchoolHistory'])){
            $model = new TransferSchoolHistory();
            $model->unsetAttributes();
            $model->attributes = $_POST['TransferSchoolHistory'];
            if ($model->childid){
                $childModel = ChildProfileBasic::model()->findByPk($model->childid);
                $model->from_schoolid = $childModel->schoolid;
                $credit = (float) $childModel->credit;
                if($childModel->schoolid == 'BJ_CP' && !empty($credit)){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '个人账户余额不为空，不能转出！（CP）');
                    $this->showMessage();
                }
            }
            $model->transfer_time = ($model->transfer_time) ? strtotime($model->transfer_time) : time();
            $model->to_schoolid = $this->branchId;
            $model->timestamp = time();
            $model->status = TransferSchoolHistory::TRANSFER_STATUS_ONE;
            $model->setScenario('app');
            if ($model->from_schoolid == $model->to_schoolid){
                $this->addMessage('state', 'fail');
                $this->addMessage('message','相同的学校，不能互转！');
                $this->showMessage();
            }
            // 如果是转入当前学年，则做账单限制
            Yii::import('common.models.calendar.CalendarSchool');
            Yii::import('common.models.calendar.CalendarSemester');
            $currentYid = CalendarSchool::model()->getCurrentSchoolYearCalendar($model->to_schoolid);
            if ($currentYid ==  $model->yid) {
                //孩子在当前学校有预交学费,不让转
                Yii::import('common.models.invoice.DepositHistory');
                $amount = DepositHistory::model()->getChildDepositBalanceBySchool($model->childid, $model->from_schoolid);
                if ($amount){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message','预交学费未使用，请先退费！');
                    $this->showMessage();
                }
                //学费未收与部分付清的情况不允许转校
                Yii::import('common.models.invoice.Invoice');
                $invoiceCount = Invoice::model()->getInvoiceCountByStatus($model->childid, array(10,30));
                if ($invoiceCount){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message','有未付账单或部分付清账单，不能转校！');
                    $this->showMessage();
                }
                // 有未审核的餐费不允许转
                Yii::import('common.models.invoice.RefundLunch');
                $crit = new CDbCriteria();
                $crit->compare('childid', $model->childid);
                $crit->compare('operator_uid', 0);
                $count = RefundLunch::model()->count($crit);
                if ($count > 0) {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '存在未审核的餐费退费！');
                    $this->showMessage();
                }
            }
            //过滤重复转校
            $cri = new CDbCriteria();
            $cri->compare('childid', $model->childid);
            $cri->compare('to_schoolid', $model->to_schoolid);
            $cri->addCondition('transfer_time>0 and transfer_time >= '.$model->transfer_time);
            $count = TransferSchoolHistory::model()->count($cri);
            if ($count){
                $this->addMessage('state', 'fail');
                $this->addMessage('message','学生已经转入本校，请不要重复转入！');
                $this->showMessage();
            }
            //下学年已分班不允许转校
            Yii::import('common.models.invoice.ChildReserve');
            $cri = new CDbCriteria();
            $cri->compare('childid', $model->childid);
            $cri->compare('stat', 20);
            $childReserve = ChildReserve::model()->exists($cri);
            if ($childReserve) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message','学生下一学年已分班，不能转校！');
                $this->showMessage();
            }
            //转校日期如果不是当前学年（下学年），转校日期赋值下学年校历开始日期
            if ($currentYid !=  $model->yid){
                $semesterList = CalendarSemester::model()->getSemesterTimeStamp($model->yid);
                $model->transfer_time = $semesterList['fall_start'];
                $model->rank = 2;
            }
            //end
            if ($model->validate()){
                if ($model->rank && $model->transfer_time<= strtotime('today')){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '指定转校的日期不能小于当天！');
                    $this->showMessage();
                }
                if ($model->save(false)){
                    //发送邮件
                    TransferSchoolHistory::sendEmail($model,false,true);
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message','Data Saved!'));
                    $this->addMessage('refresh',true);
                    $this->addMessage('referer',$this->createUrl('//mcampus/transfer/index',array('branchId'=>$this->branchId)));
                    $this->showMessage();
                }
            }else{
                $error = current($model->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('message',$error ? $error[0] : '填写必填项！');
                $this->showMessage();
            }
        }
        $this->addMessage('state', 'fail');
        $this->addMessage('message', Yii::t('message','Data Saving Failed!'));
        $this->showMessage();
    }

    public function actionGetClass(){
        $yid = Yii::app()->request->getPost('yid',0);
        $branchId = Yii::app()->request->getParam('branchId', null);
        $option = '<option>'.Yii::t('global', 'Please Select').'</option>';
        if ($yid && $branchId !== null){
            $classModels = IvyClass::model()->getClassList($branchId,$yid);
            if (!empty($classModels)){
                foreach ($classModels as $val){
                    $option .= '<option value="'.$val->classid.'">'.$val->title.'</option>';
                }
            }
        }
        echo CJSON::encode($option);
    }

    public function renderSchool($data){
        $allBranch = $this->getAllBranch();
        if ($this->type === 'in'){
            echo $allBranch[$data->from_schoolid]['title'];
        }else{
            echo $allBranch[$data->to_schoolid]['title'];
        }
    }

    /*
     * 删除转校申请
     */
    public function actionDelete(){
        $request = Yii::app()->request;
        $id = $request->getPost('id',0);
        if ($id && $request->isAjaxRequest){
            $model = TransferSchoolHistory::model()->findByPk($id);
            if ($model->status == TransferSchoolHistory::TRANSFER_STATUS_ONE && $this->checkBranchAccess($model->to_schoolid)){
                if ($model->delete()){
                    TransferSchoolHistory::sendEmail($model,false,false);
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message','Delete successful!'));
                    $this->addMessage('callback','callbackRefresh');
                    $this->showMessage();
                }
            }
        }
        $this->addMessage('state', 'fail');
        $this->addMessage('message', Yii::t('message','Delete failed!'));
        $this->showMessage();
    }

    /*
     * 转校确认
     */
    public function actionConfirm(){
        $request = Yii::app()->request;
        $id = $request->getPost('id',0);
        if ($id && $request->isAjaxRequest){
            Yii::import('common.models.calendar.CalendarSchool');
            Yii::import('application.components.policy.PolicyApi');
            $model = TransferSchoolHistory::model()->findByPk($id);
            if ($model->status == TransferSchoolHistory::TRANSFER_STATUS_ONE && ($this->checkBranchAccess($model->from_schoolid) || $model->from_schoolid == $this->branchId)){
                ChildProfileBasic::setChildSync($model->childid);
                $currentYid = CalendarSchool::model()->getCurrentSchoolYearCalendar($model->to_schoolid);
                if ($model->yid == $currentYid){
                    //立即转校（指定日期使用commands执行）
                    if ($model->rank == 0){
                        if (PolicyApi::newTransferSchool($model)){
                            $model->status = TransferSchoolHistory::TRANSFER_STATUS_THREE;
                            $model->save();
                            TransferSchoolHistory::sendEmail($model,false,true);
                            $this->addMessage('state', 'success');
                            $this->addMessage('message', Yii::t('message','Confirm successful!'));
                            $this->addMessage('callback','callbackRefresh');
                            $this->showMessage();
                        }
                    }else{
                        if ($model->transfer_time <= time()){
                            if (PolicyApi::newTransferSchool($model)){
                                $model->status = TransferSchoolHistory::TRANSFER_STATUS_THREE;
                                $model->save();
                                TransferSchoolHistory::sendEmail($model,false,true);
                                $this->addMessage('state', 'success');
                                $this->addMessage('message', Yii::t('message','Confirm successful!'));
                                $this->addMessage('callback','callbackRefresh');
                                $this->showMessage();
                            }
                        }else{
                            $model->status = TransferSchoolHistory::TRANSFER_STATUS_TWO;
                            $model->timestamp = time();
                            if ($model->save()){
                                TransferSchoolHistory::sendEmail($model,false,true);
                            }
                            $this->addMessage('state', 'success');
                            $this->addMessage('message', Yii::t('message','Confirm successful!'));
                            $this->addMessage('callback','callbackRefresh');
                            $this->showMessage();
                        }
                    }
                }else{
                    if (PolicyApi::newTransferSchool($model,true)){
                        $model->status = TransferSchoolHistory::TRANSFER_STATUS_TWO;
                        $model->save();
                        TransferSchoolHistory::sendEmail($model,false,true);
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', Yii::t('message','Confirm successful!'));
                        $this->addMessage('callback','callbackRefresh');
                        $this->showMessage();
                    }
                }
            }
        }
        $this->addMessage('state', 'fail');
        $this->addMessage('message', Yii::t('message','Confirm failed!'));
        $this->showMessage();
    }


    public function getBranchId(){
        return $this->staff->profile->branch;
    }

    public function timestampBalance($data)
    {
        $statrTime = strtotime(date("Y-m-d", $data->transfer_time));
        $endTime = $statrTime + 86400;
        Yii::import('common.models.child.*');
        $criteria = new CDbCriteria;
        $criteria->compare('childid', $data->childid);
        $criteria->compare('updated_timestamp', "<$endTime");
        $criteria->order = 'updated_timestamp DESC';
        $now = ChildCreditNow::model()->find($criteria);

        return ($now) ? $now->surplus : 0;

    }

    //渲染孩子名字
    public function renderChildName($data){
        if ($this->type === 'in'){
            if ($data->status > TransferSchoolHistory::TRANSFER_STATUS_ONE){
                echo CHtml::link($data->child->getChildName(), $this->createUrl('/child/invoice/index',array('childid'=>$data->childid)), array('target'=>'_blank'));
            }else{
                echo $data->child->getChildName();
            }
        }else{
            echo $data->child->getChildName();
        }
    }

    public function renderParent($data){
        echo '<a href="javascript:void(0)" onclick="viewParent(this);" data-childid='.$data->childid.'><span class="glyphicon glyphicon-search"></span></a>';
    }

    // 导出数据
    public function actionExportClv()
    {
        $type = Yii::app()->request->getParam('type','in');
        $startTime = Yii::app()->request->getParam('startTime','');
        $endTime = Yii::app()->request->getParam('endTime','');
        Yii::import('common.models.child.TransferSchoolHistory');
        $criteria = new CDbCriteria();
        if($type == 'in'){
            $filename = '转入学生列表';
            $schoolName = '转出学校';
            $criteria->compare('t.to_schoolid', $this->branchId);
        }
        else{
            $filename = '转出学生列表';
            $schoolName = '转入学校';
            $criteria->compare('t.from_schoolid', $this->branchId);
        }

        $statrTime = strtotime($startTime);
        $endTime = strtotime($endTime) + 86400;

        $criteria->compare('t.transfer_time', ">=$statrTime");
        $criteria->compare('t.transfer_time', "<=$endTime");
        $criteria->order = 't.status asc, t.transfer_time desc';

        $model = TransferSchoolHistory::model()->findAll($criteria);
        $datas = array();
        $datas['title'] = date('Y-m-d', $statrTime)  . '至' . date("Y-m-d", $endTime) . $this->branchId . $filename . ".xlsx";
        $datas['items'][0] = array('孩子姓名', $schoolName, '转入班级', '入学日期','操作时间','状态','个人账户（入学日期）','个人账户（操作时间）');
        if($model){
            $allBranch = $this->getAllBranch();
            foreach ($model as $i => $item) {
                if ($type === 'in'){
                    $school = $allBranch[$item->from_schoolid]['title'];
                }else{
                    $school = $allBranch[$item->to_schoolid]['title'];
                }

                $timestampBalance = $this->timestampBalance($item);
                $array = array();
                $array[] = $item->child->getChildName();;
                $array[] = $school;
                $array[] = $item->class->title;
                $array[] = date("Y-m-d", $item->transfer_time);
                $array[] = date("Y-m-d", $item->timestamp);
                $array[] = TransferSchoolHistory::renderStatus($item->status,1);
                $array[] = $timestampBalance;
                $array[] = $item->balance;
                $datas['items'][$i + 1] = $array;
            }
        }

        $this->addMessage('state', 'success');
        $this->addMessage('data', $datas);
        $this->showMessage();
    }

}

