<?php

/**
 * This is the model class for table "ivy_asa_invoice".
 *
 * The followings are the available columns in table 'ivy_asa_invoice':
 * @property integer $id
 * @property string $title
 * @property double $amount_original
 * @property double $amount_actual
 * @property integer $pay_type
 * @property integer $status
 * @property string $schoolid
 * @property integer $updated
 * @property integer $updated_userid
 */
class AsaInvoice extends CActiveRecord
{
	const CASH = 1;
	const WECHAT = 2;
	const CREDIT = 3;

	const STATS_UNPAID = 10;            //未付款
	const STATS_PAID = 20;				//款付清
	const STATS_EXPIRED = 89;			//自动作废
	const STATS_CANCELLED = 99;         //作废

	const CREATED_FROM_STAFF = 1;
	const CREATED_FROM_USER = 2;

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_asa_invoice';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('title, amount_original, amount_actual, status, schoolid, updated, updated_userid', 'required'),
			array('pay_type, status, updated, updated_userid', 'numerical', 'integerOnly'=>true),
			array('amount_original, amount_actual', 'numerical'),
			array('title, schoolid', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, title, amount_original, amount_actual, pay_type, status, schoolid, updated, updated_userid, created_from', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'item' => array(self::HAS_MANY, 'AsaInvoiceItem', array('order_id'=>'id')),
			'itemExtra' => array(self::HAS_MANY, 'AsaInvoiceItemExtra', array('invoice_id'=>'id')),
			'childInfo' => array(self::HAS_ONE, 'ChildProfileBasic', array('childid'=>'childid')),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'title' => 'Title',
			'amount_original' => 'Amount Original',
			'amount_actual' => 'Amount Actual',
			'pay_type' => 'Pay Type',
			'status' => 'Status',
			'schoolid' => 'Schoolid',
			'updated' => 'Updated',
			'updated_userid' => 'Updated Userid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('title',$this->title,true);
		$criteria->compare('amount_original',$this->amount_original);
		$criteria->compare('amount_actual',$this->amount_actual);
		$criteria->compare('pay_type',$this->pay_type);
		$criteria->compare('status',$this->status);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('updated',$this->updated);
		$criteria->compare('updated_userid',$this->updated_userid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AsaInvoice the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
