<?php
class SI extends CWidget
{
    protected $scriptUrl;

    protected $scriptFile;

    public $cssFile;

    public function init()
    {
        $this->scriptUrl = Yii::app()->getAssetManager()->publish(dirname(__FILE__).'/assets');

        $this->scriptFile = '/script.js?v20241107';

        $this->cssFile = '/style.css?v20241107';

        $this->registerClientScript();
    }

    public function registerClientScript()
    {
        $cs = Yii::app()->clientScript;

        $cs->registerScriptFile($this->scriptUrl.$this->scriptFile);

        $cs->registerCssFile($this->scriptUrl.$this->cssFile);
    }

    public function run()
    {
        $res = CommonUtils::requestDsOnline('quote/getHomePageSiData', array(),'get');
        if(!empty($res['data'])){
            $this->render('si',array('tips'=>json_encode($res['data'])));
        }
    }
}
