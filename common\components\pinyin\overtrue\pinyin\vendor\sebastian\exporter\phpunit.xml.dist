<?xml version="1.0" encoding="UTF-8"?>
<phpunit backupGlobals="false"
         bootstrap="vendor/autoload.php"
         beStrictAboutTestsThatDoNotTestAnything="true"
         beStrictAboutOutputDuringTests="true"
         beStrictAboutTodoAnnotatedTests="true"
         checkForUnintentionallyCoveredCode="true"
         forceCoversAnnotation="true"
         verbose="true">
 <testsuites>
  <testsuite name="exporter">
   <directory>tests</directory>
  </testsuite>
 </testsuites>
 <filter>
  <whitelist addUncoveredFilesFromWhitelist="true" processUncoveredFilesFromWhitelist="true">
   <directory>src</directory>
  </whitelist>
 </filter>
</phpunit>

