<style>
    .addChildBill{
        background: #FFFFFF;
        border-radius: 4px;
        padding:8px 16px;
        align-items:center
    }                                                       
    .invalidBill{
        padding:8px 16px;
        background: #F7F7F8;
        border-radius: 4px;
        align-items:center
    }
    .highlight-row {
        background-color: #F0F5FB !important; /* 浅绿色背景 */
    }
    .zindex{
        z-index:1000 !important
    }
    
    .bill > li > a{
        padding: 2px 8px !important;
        color:#666
    }
    .firstBorder{
        border-top: 1px dashed #D9D9D9;
        position: absolute;
        left: 50%;
        top: 50%;
        width:100px;
    }
    .avatar32{
        width:32px;
        height: 32px;
        border-radius: 50%;
        object-fit: cover;
    }
    .avatar40{
        width:40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
    }
    .iconAbsolute{
        height: 14px;
        position: absolute;
        bottom: 0px;
        right: -5px;
        width: 14px;
        color: #5CB85C;
        background: #fff;
        border-radius: 50%;
        font-size:13px
    }
    .iconAbsoluteNot{
        height: 14px;
        position: absolute;
        bottom: 0px;
        right: -5px;
        width: 14px;
        color: #F0AD4E;
        background: #fff;
        border-radius: 50%;
        font-size:13px
    }
    .minWidth {
        width: 100px;
    }
    .statusWidth{
        width:80px;
    }
    .colorBlue{
        color: #4D88D2;
    }
    .colorYellow{
        color:#F0AD4E
    }
    .colorC{
        color:#ccc
    }
    .dot{
        width: 6px;
        height: 6px;
        background: #D9534F;
        border-radius:50%;
        display: inline-flex
    }
    .dotGreen{
        width: 6px;
        height: 6px;
        background: #5CB85C;
        border-radius:50%;
        display: inline-flex
    }
    .colorRed{
        color:#D9534F
    }
    .colorGreen{
        color:#5CB85C
    }
    .text_overflow{
        overflow: hidden; /* 必须 */
        text-overflow: ellipsis; /* 当文本溢出时显示省略号 */
        white-space: nowrap; /* 强制文本在一行内显示 */
    }
    .widthFlex{
        width: 0;
    }
    .tag{
        padding:0px 6px;
        background: #E5E6EB;
        border-radius: 2px;
        color:#333;
        font-size:12px;
        height: 20px;
        line-height: 20px;
        white-space: nowrap;
        display: inline-block;
    }
    .tips{
        padding:10px 16px;
        background: #FDF8F1;
        border-radius: 4px;
    }
    .warning{
        padding:6px 8px;
        background: #FCF1F1;
        border-radius: 3px 3px 0px 0px;
        color: #D9534F;
        margin-bottom:10px;
        text-align:left;
        position: absolute;
        top:0;
        left:0
    }
    .greenTag{
        padding: 0 6px;
        font-size: 12px;
        color: #5CB85C;
        background: #F2F9F2;
        border-radius: 2px;
        white-space: nowrap;
        display: inline-block;
        height: 20px;
        line-height: 20px;
    }
    .redTag{
        padding:4px 6px;
        font-size: 12px;
        color: #D9534F;
        background: #FCF1F1;
        border-radius: 2px;
        white-space: nowrap;
    }
    .scroll-box::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius   : 10px;
        background-color: skyblue;
        background-color: #D8D8D8;
        background-image: none
    }
    .scroll-box::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0);
        background   : #fff;
        border-radius: 10px;
    }
    .tableInvoice{
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        border-left: 1px solid #DDDDDD;
        margin-bottom:1px
    }
    .tableInvoice tr td,.tableInvoice tr th{
        padding:10px 16px !important;
        color:#333;
        vertical-align: middle !important;
        box-sizing: border-box;
        border-right: 1px solid #DDDDDD;
        border-bottom: 1px solid #DDDDDD;
        border-bottom-width: 1px;
    }
    .tableInvoice tr td{
        border-top:none !important
    }
    .table > tbody + tbody{
        border-top: 1px solid #dddddd !important;
    }
    .sticky-header tr{
        position: sticky;
        top:129px;
        background-color: #fafafa;
        z-index: 10;
    }
    .sticky-header th {
        position: sticky;
        top:-1px;
        background-color: #fafafa;
        z-index: 11;
        border-bottom:1px solid #ddd !important;
        border-top:1px solid #ddd !important;
    }
    .labelTag{
        background: #F2F3F5;
        border-radius: 2px;
        text-align: center;
        padding: 0 6px;
        font-size: 12px;
        border-radius: 2px;
        white-space: nowrap;
        display: inline-block;
        height: 20px;
        line-height: 20px;
    }
    .el-dropdown-menu__item:focus,.el-dropdown-menu__item:not(.is-disabled):hover {
        background-color: #ecf5ff;
        color: #4D88D2
    }
    .line{
        border-top: 1px dashed #D9D9D9;
        display:inline-block;
        height:1px
    }
    .inline_flex{
        display:inline-flex
    }
    .fontWeight{
        font-weight:normal
    }
    .el-dropdown-menu__item,.el-select-dropdown__item,.el-input__inner{
        font-size:12px
    }
    .sticky{
        position: sticky;
        top: 50px;
        background-color: #fff;
        z-index: 10;
        padding: 24px 0;
    }
</style>
<div class="container-fluid" id='box' v-cloak>
    <ol class="breadcrumb"> 
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('schoolBus', '批量账单'); ?></li>
    </ol>
    <div class='loadingData' >
        <div class='mb16'>
            <el-select v-model="startYear" size='small' @change='switchYear("tab")' placeholder="请选择">
                <el-option
                v-for="item in startYearList"
                :key="item.key"
                :label="item.value"
                :value="item.key">
                </el-option>
            </el-select>
        </div>
        <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="学费" name="tuition"></el-tab-pane>
            <el-tab-pane label="餐费" name="food"></el-tab-pane>
            <el-tab-pane label="校车费" name="bus"></el-tab-pane>
        </el-tabs>
        <div v-if='activeName=="tuition" || activeName=="food"'>
            <el-empty description="<?php echo Yii::t('ptc', 'No Data') ?>"></el-empty>
        </div>
        <div v-if='activeName=="bus"'>
            <div class=''>
                <el-input
                    placeholder="搜索学生"
                    class='select_3'
                     size='small'
                    prefix-icon="el-icon-search"
                    clearable
                    v-model="searchChild">
                </el-input>
                
                <el-select v-model="levelFilter" clearable  class='ml16 select_2' size='small' placeholder="档位">
                    <el-option
                    v-for="item in configData.fee_level"
                    :key="item.fee_level"
                    :label="item.title"
                    :value="item.fee_level">
                    </el-option>
                </el-select>   
                <el-select v-model="schoolFilter" clearable  class='ml16 select_2' size='small' placeholder="学费折扣">
                    <el-option
                    v-for="item in tuition_discount"
                    :key="item.id"
                    :label="item.title"
                    :value="item.id">
                    </el-option>
                </el-select>  
                <el-select v-model="busFilter" clearable  class='ml16 select_2' size='small' placeholder="校车费折扣">
                    <el-option
                        v-for="item in configData.discount"
                        :key="item.id"
                        :label="item.title"
                        :value="item.id">
                    </el-option>
                </el-select>  
                <el-select v-model="statusFilter" clearable  class='ml16 length_3' size='small' placeholder="确认状态">
                    <el-option
                        v-for="item in confirmList"
                        :key="item.value"
                        :label="item.title"
                        :value="item.value">
                    </el-option>
                </el-select>  
                <el-select v-model="billFilter" clearable  class='ml16 select_2' size='small' placeholder="账单状态">
                    <el-option
                    v-for="item in billList"
                    :key="item.value"
                    :label="item.title"
                    :value="item.value">
                    </el-option>
                </el-select>   
            </div>
            <div class='flex align-items sticky' v-if='tableInvoice.sort_family'>
                <div class='flex1'>
                    <label class="checkbox-inline">
                        <input type="checkbox" v-model='moreFamily'> 仅查看多子女家庭 ({{tableInvoice.many_children_total}})
                    </label>
                </div>
                <div v-if='batchType==""'>
                    <button type="button" class="btn btn-primary ml16" @click='batchHandle("coordinator")'>批量校车协调员确认</button>
                    <button type="button" class="btn btn-primary ml16" @click='batchHandle("finance")'>批量第二审批人确认</button>
                    <button type="button" class="btn btn-primary ml16" @click='batchHandle("bill")'>批量生成账单</button>
                    <button type="button" class="btn btn-default ml16" @click='exportData()'>导出</button>
                </div>
                <div v-else>
                    <span v-if='batchType=="coordinator"' class='font14'><span class='fontBold'>批量校车协调员确认：</span> 已选择 {{childInvoice.length}} 位学生</span>
                    <span v-if='batchType=="finance"' class='font14'><span class='fontBold'>批量第二审批确认：</span>已选择 {{childInvoice.length}} 位学生</span>
                    <span v-if='batchType=="bill"' class='font14'><span class='fontBold'>批量生成账单：</span>已选择 {{childInvoice.length}} 位学生</span>
                    <button type="button" class="btn btn-primary ml16" @click='batchConfirm()'>确定</button>
                    <button type="button" class="btn btn-default ml16" @click='cancelConfirm()'>取消</button>
                </div>
            </div>
            <div v-loading="tableLoading">
                <div class='tableList scroll-box'  >
                    <table class='table tableInvoice ' id='table'  v-if='tableInvoice.sort_family'  style="table-layout: fixed;">
                        <thead class="sticky-header">
                            <tr>
                                <th v-if='batchType!=""'  width='40' class='text-center'>
                                    <input type="checkbox" v-if='batchType=="coordinator"' v-model='childInvoiceAll' @change='onChildCheckAll("bus")'>
                                    <input type="checkbox" v-if='batchType=="finance"' v-model='childInvoiceAll' @change='onChildCheckAll("finance")'>
                                    <input type="checkbox" v-if='batchType=="bill"' v-model='childInvoiceAll' @change='onChildCheckAll("invoice")'>
                                </th>
                                <th style="width:70px;" class='text-center'>多子女</th>
                                <th class='text-center' style="width:200px;">学生</th>
                                <th class='text-center' style="width:120px;">档位</th>
                                <th class='text-center' style="width:120px;">学费折扣</th>
                                <th class='text-center' style="width:110px;">推荐折扣</th>
                                <th class='text-center' style="width:150px;">校车费折扣</th>
                                <th style="width:90px;" class='text-center'>乘坐方式</th>
                                <th style="width:200px;" >
                                    <div class='text-center'>确认状态</div>
                                    <div class='flex mt12 align-items'>
                                        <span class='tag fontWeight'>校车协调员</span>
                                        <span  class='flex1 line'></span>
                                        <span class='tag fontWeight'>第二审批</span>
                                    </div> 
                                </th>
                                <th>
                                    <div class='flex align-items'>
                                        <span class='flex1'>账单</span>
                                        <ul class="nav nav-pills bill">
                                            <li role="presentation"  :class='rangeFilter==list.value?"active":""'  v-for='(list,index) in configData.invoice_range'>
                                                <a href="#" @click='filterRange(list.value)'>{{list.title}}</a>
                                            </li>
                                            <li role="presentation" :class='rangeFilter=="all"?"active":""'><a href="#" @click='filterRange("all")'>全部</a></li>
                                        </ul>
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <template v-if='searchData && searchData.length'>
                            <tbody v-for='(list,index) in searchData' :style="index % 2 === 0 ? { backgroundColor: '#F2F9F2' } : {}">
                                <tr v-for='(item,idx) in tableInvoiceData[list]' :class='style === "batch" ? (childInvoice.indexOf(item.child_id) !== -1 ? "highlight-row" : "") : ""'>
                                    <td v-if='batchType!=""'>
                                        <input type="checkbox" v-if='batchType=="coordinator"' v-model='childInvoice' @change='onChildCheck("bus")'  :value='item.child_id' :disabled='item.can_batch_bus?false:true'>
                                        <input type="checkbox" v-if='batchType=="finance"' v-model='childInvoice' @change='onChildCheck("finance")'  :value='item.child_id' :disabled='item.can_batch_finance?false:true'>
                                        <input type="checkbox" v-if='batchType=="bill"' v-model='childInvoice' @change='onChildCheck("invoice")'  :value='item.child_id' :disabled='item.can_batch_invoice?false:true'>
                                    </td>
                                    <td class='text-center' v-if='tableInvoiceData[list].length>1 && idx==0' :rowspan='tableInvoiceData[list].length>1?tableInvoiceData[list].length:""'>是</td>
                                    <td class='text-center' v-else-if='tableInvoiceData[list].length==1'>否</td>
                                    <td>
                                        <div class='colorBlue font12 cur-p' @click='studentbill(item)'>{{item.child_name}}</div>
                                        <div class='font12 color6'><span :class='item.in_school?"greenTag":"tag"'>{{item.school_abb}}</span> ｜{{item.child_id}}｜{{item.class_name}}</div>
                                    </td>
                                    <td class='text-center'>
                                        <div class='color3 font12' v-if='configData.fee_level[item.fee_level]'>{{configData.fee_level[item.fee_level].abb}} ￥{{item.price}}</div>
                                        <div class='mt4 color3 font12'>{{item.site_name}}</div>
                                        <!-- <div class='font12 mt4 color6'>原价：{{item.price}}</div> -->
                                    </td>
                                    <td class='text-center'>
                                        <div class='color3 font12' v-for='(tuition,t) in item.tuition_discount'>{{tuitionTitle(tuition)}}</div>
                                    </td>
                                    <td class='text-center'>
                                    <div class='color3 font12'>{{discountTitle(item.referral_discount)}}</div></td>
                                    <td class='text-center'>
                                        <div v-if='item.finance!=null && item.coordinator!=null' class='font12'>
                                            <div class='color3'>{{discountTitle(item.discount)}}<span v-if='item.discount==99'>优惠{{item.off_ratio}}%</span></div>
                                            <div class='colorBlue mt4 '><span class='el-icon-edit cur-p' @click='editDiscount(idx,list)'>修改</span> </div>
                                        </div>
                                        <el-dropdown v-else>
                                            <span class="el-dropdown-link colorBlue font12 cur-p" style='white-space: nowrap;'>
                                                <span v-if='item.discount==item.referral_discount'>使用推荐折扣</span>
                                                <span v-else> {{item.discount!=null?discountTitle(item.discount):'请选择'}}</span>
                                                <span v-if='item.discount==99'>优惠{{item.off_ratio}}%</span>
                                                <i class="el-icon-arrow-down el-icon--right"></i>
                                            </span>
                                            <el-dropdown-menu slot="dropdown">
                                                <template  v-for='(discount,d) in configData.discount' >
                                                    <el-dropdown-item v-if='discount.id!=99' :key='d' :disabled='item.discount==discount.id?true:false' @click.native='customizeDiscount(item,discount)'>
                                                        {{discount.title}}
                                                    </el-dropdown-item>
                                                    <el-dropdown-item divided v-if='discount.id==99' :key='d'  @click.native='customizeDiscount(item,discount)'>
                                                    <span class='el-icon-plus'></span> {{discount.title}}
                                                    </el-dropdown-item>
                                                </template>
                                            </el-dropdown-menu>
                                        </el-dropdown>
                                        <div v-if='item.discount!=1' class='mt4 color3'>折扣价格：{{item.fee_level_price}}</div>
                                        <div v-if='item.discount_remark!=null'  class='mt4 color3'>备注：{{item.discount_remark}}</div>
                                    </td>
                                    <td class='text-center'><span class='font12'>{{item.journey==1?"上学":item.journey==2?"放学":"双程"}}</span></td>
                                    <td class='text-center relative' >
                                        <div v-if='item.again_confirm.discount' class='flex warning'>
                                            <span class='el-icon-warning font12'></span>
                                            <div class='ml4'>
                                                <div>和上次信息有变化需重新确认！上次信息：{{configData.fee_level[item.again_confirm.fee_level].title}}、{{discountTitle(item.again_confirm.discount)}}<span v-if='item.again_confirm.discount==99'>优惠{{item.again_confirm.off_ratio}}%</span>、{{item.again_confirm.journey==1?"上学乘坐":item.again_confirm.journey==2?"放学乘坐":item.again_confirm.journey==3?"双程乘坐":""}}</div>
                                            </div>    
                                        </div>
                                        <div class='inline_flex' :style='item.again_confirm.discount?"margin-top:60px":""' v-if='item.discount!=null'>
                                            <div class='text-center statusWidth mr16'>
                                                <div >
                                                    <div class='relative'>
                                                        <div class='firstBorder'></div>
                                                        <div class='relative inline-block' v-if='item.coordinator!="" && item.coordinator!=null'>
                                                            <img  :src="tableInvoice.staff_info[item.coordinator].photoUrl" alt="" class='avatar32'>
                                                            <span class='el-icon-success iconAbsolute'></span>
                                                        </div>
                                                        <div class='relative inline-block' v-else>
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/default.png' ?>" alt="" class='avatar32'>
                                                            <span class='el-icon-question iconAbsoluteNot'></span>
                                                        </div>
                                                    </div>
                                                
                                                    <div  class='mt5 font12' v-if='item.coordinator!="" && item.coordinator!=null'>
                                                        <el-tooltip class="item" effect="dark" :content="tableInvoice.staff_info[item.coordinator].name" placement="top">
                                                            <div class='color3 text_overflow' >{{tableInvoice.staff_info[item.coordinator].name}}</div>
                                                        </el-tooltip>
                                                        <div class='color6' >{{item.coordinator_date}}</div>
                                                    </div>
                                                    <div v-else class='mt5 font12'>
                                                        <div class='color3 '>校车协调员</div>
                                                        <div class='colorBlue' ><span class='cur-p' @click='singleConfirm(item.child_id,"coordinator")'>确认</span>  </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class='text-center statusWidth'>
                                                <div >
                                                    <div class='relative'>
                                                        <div class='relative inline-block'  v-if='item.finance!="" && item.finance!=null'>
                                                            <img :src="tableInvoice.staff_info[item.finance].photoUrl" alt="" class='avatar32'>
                                                            <span class='el-icon-success iconAbsolute'></span>
                                                        </div>
                                                        <div class='relative inline-block' v-else>
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/default.png' ?>" alt="" class='avatar32'>
                                                            <span class='el-icon-question iconAbsoluteNot'></span>
                                                        </div>
                                                    </div>
                                                    <div class='font12 mt5' v-if='item.finance!="" && item.finance!=null'>
                                                        <el-tooltip class="item" effect="dark" :content="tableInvoice.staff_info[item.finance].name" placement="top">
                                                            <div class='color3 text_overflow' >{{tableInvoice.staff_info[item.finance].name}}</div>
                                                        </el-tooltip>
                                                        <div class='color6'>{{item.finance_date}}</div>
                                                    </div>
                                                    <div class='font12 mt5' v-else>
                                                        <div class='color3 '>第二审批</div>
                                                        <div class='colorBlue'  v-if='item.coordinator!=null'><span class=' cur-p' @click='singleConfirm(item.child_id,"finance")'>确认</span></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div v-else class='font14 colorC text-center'>—</div>
                                    </td>
                                    <td  style='padding: 0px !important; vertical-align: top !important;'>
                                        <div>                            
                                            <div style='border-bottom:1px solid #E5E6EB;padding:12px 16px'>
                                                <div class='flex align-items'>
                                                    <div class=' flex1 '><span class='font12 color6 fontBold'>当前关联账单：</span> <span v-if='item.invoice_list.master.length==0' class='colorC'>无</span></div>
                                                    <div class='colorBlue font12 cur-p' v-if='item.finance!=null && item.coordinator!=null && item.in_school' @click='tableCreateBill(item)'><span class='el-icon-plus'></span> 添加账单</div>
                                                </div>
                                                <div  v-for='(_item,i) in item.invoice_list.master'>
                                                    <div class='flex font12 invalidBill mt8' v-if="rangeFilter !== 'all' ? _item.invoice_range_value.indexOf(rangeFilter) !== -1 : true">
                                                        <div class='flex1 widthFlex text_overflow  mr10'>
                                                            <span class='color3'> {{_item.amount}}元</span> 
                                                            <span class='color6'>｜ {{_item.title}}</span>
                                                        </div>
                                                        <span class='colorRed mr12 ' v-if='_item.status==10'><span class='dot'></span> 未付</span> 
                                                        <span class='colorGreen mr12 ' v-if='_item.status==20'><span class='dotGreen'></span> 已付</span> 
                                                        <span class='colorBlue cur-p mr12' style='width:24px'  @click='cancelBill(_item.invoice_id)'>{{_item.status==10?"作废":""}}</span>
                                                        <a class=" J_dialog colorBlue" non-resize="1" role="button" :href="'<?php echo $this->createUrl("/child/invoice/viewInvoice"); ?>&childid='+item.child_id+'&invoiceid='+_item.invoice_id">详情</a>
                                                    </div>
                                                </div>    
                                            </div>
                                            <div style='padding:12px 16px'>
                                                <div>
                                                    <span class='font12 color6 fontBold'>其他账单：</span>
                                                    <span v-if='item.invoice_list.other.length==0' class='colorC'>无</span>
                                                </div>
                                                <div class='' v-for='(_item,i) in item.invoice_list.other'>
                                                    <div class='flex font12 invalidBill mt8' v-if="rangeFilter !== 'all' ? _item.invoice_range_value.indexOf(rangeFilter) !== -1 : true">
                                                        <div class='flex1 widthFlex text_overflow ' :class='_item.status==20?"mr12":""'>
                                                            <span class='color3'> {{_item.amount}}元</span> 
                                                            <span class='color6'>｜ {{_item.title}}</span>
                                                        </div>
                                                        <el-tooltip class="item" effect="dark" content="和上次信息有变化，此账单可作废" placement="top">
                                                            <span class='el-icon-warning-outline colorRed font12 mr12' v-if='_item.status==10'></span>
                                                        </el-tooltip>
                                                        <span class='colorRed mr12 ' v-if='_item.status==10'><span class='dot'></span> 未付</span> 
                                                        <span class='colorGreen mr12 ' v-if='_item.status==20'><span class='dotGreen'></span> 已付</span> 
                                                        <span class='colorBlue cur-p mr12' style='width:24px'  @click='cancelBill(_item.invoice_id)'>{{_item.status==10?"作废":""}}</span>
                                                        <a class=" J_dialog colorBlue" non-resize="1" role="button" :href="'<?php echo $this->createUrl("/child/invoice/viewInvoice"); ?>&childid='+item.child_id+'&invoiceid='+_item.invoice_id">详情</a>
                                                    </div>
                                                </div>   
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </template>
                        <template v-else-if='tableInvoice.list_by_family'>
                            <tr>
                                <td colspan='9'><div class='text-center color9 pt10 pb10'>暂无数据</div> </td>
                            </tr>
                        </template>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <!-- 删除 -->
    <div class="modal fade" id="delModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel">
                        <span v-if='delType=="cancel"'><?php echo Yii::t('newDS', '作废') ?></span>
                        <span v-if='delType=="coordinator"'><?php echo Yii::t('newDS', '校车协调员确认') ?></span>
                        <span v-if='delType=="finance"'><?php echo Yii::t('newDS', '第二审批确认') ?></span>
                    </h4>
                </div>
                <div class="modal-body p24 font14" >
                    <div v-if='delType=="cancel"'>
                        确认作废账单吗？
                    </div>
                    <div v-if='delType=="coordinator" || delType=="finance"'>
                        确认无误
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" v-if='delType=="cancel"' :disabled='btnDisanled' @click='cancelBill()'><?php echo Yii::t("global", "OK"); ?></button>
                    <button type="button" class="btn btn-primary" v-if='delType=="coordinator" || delType=="finance"' :disabled='btnDisanled' @click='batchSave()'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 生成账单 -->
    <div class="modal fade" id="createBillModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel"  data-backdrop="static">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel">生成账单</h4>
                </div>
                <div class="modal-body  p24 overflow-y scroll-box" :style="'max-height:'+(height-200)+'px;overflow-x: hidden;'">
                    <div class='font14 color3 fontBold mb16'>已选择{{childInvoice.length}}项</div>
                    <div class='font14 color6 mb12'>账单标题</div>
                    <div class='mb24'><el-input v-model="createBill.title" size="small" placeholder="请输入内容"></el-input></div>
                    <div class='font14 color6 mb12'>账单区间</div>
                    <div class='mb24'>
                        <el-date-picker
                            v-model="createBill.billDate"
                            type="daterange"
                            placement="bottom-start"
                            range-separator="至"
                            format="yyyy-MM-dd"
                            size='small' 
                            @change='getSimilarOrder'
                            style='width:100%'
                            :picker-options="pickerOptions"
                            value-format="yyyy-MM-dd"
                            start-placeholder="<?php echo Yii::t("campus", "Start Date"); ?>"
                            end-placeholder="<?php echo Yii::t("campus", "End Date"); ?>">
                        </el-date-picker>
                        <div class='tips mt12' v-if='tips.state=="fail"'>
                            <div class='colorYellow font14'><span class='el-icon-warning'></span><span class='ml4 '>{{tips.message}}</span></div>
                            <div class='color3 font12'>{{tips.data}}</div>
                        </div>
                    </div>
                    <div class='font14 color6 mb12'>账单到期时间</div>
                    <div class=''>
                        <el-date-picker
                            size='small' 
                            style='width:100%'
                            v-model="createBill.billTime"
                            type="date"
                            placement="bottom-start"
                            value-format="yyyy-MM-dd"
                            placeholder="选择日期">
                        </el-date-picker>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" :disabled='btnDisanled' @click='batchCreateInvoice()'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 自定义折扣 -->
    <div class="modal fade" id="discountModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel" v-if='customize.list && customize.item.id==99'><?php echo Yii::t('newDS', '特殊折扣') ?></h4>
                    <h4 class="modal-title" id="parentReplyLabel" v-else><?php echo Yii::t('newDS', '设置折扣') ?></h4>
                </div>
                <div class="modal-body p24" >
                    <div class='mb24' v-if='customize.list && customize.item.id==99'>
                        <div class='font14 color6'>请输入特殊折扣</div>
                        <div class='flex align-items mt12'>
                            <span class='font14 color3 fontBold'>优惠：</span>
                            <el-input
                                size="small"
                                placeholder="请输入特殊折扣"
                                type='number'
                                class='flex1'
                                v-model="ratio"
                                @input="calculateDiscount(ratio)" >
                            </el-input>
                            <span class='font14 color3 fontBold ml16'>%</span>
                            <span class='font14 color3 fontBold ml16' style="width: 10%;">
                                <span v-if="ratio!==''">{{discountValue}}</span>
                            </span>
                        </div>
                    </div>
                    <div>
                        <div class='font14 mb24' v-if='customize.list && customize.item.id!=99'>
                            <span class='color6'>当前选择折扣：</span> <span class='color3'>{{discountTitle(customize.item.id)}}</span>
                        </div>
                        <div class='font14 color6'>备注</div>
                        <textarea class="form-control mt8" rows="2"  v-model='remarks' placeholder="请输入备注"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" :disabled='btnDisanled' @click='discountSave()'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 修改折扣 -->
    <div class="modal fade" id="editDiscountModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel"  data-backdrop="static">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel">修改折扣</h4>
                </div>
                <div class="modal-body  p24 overflow-y scroll-box" :style="'max-height:'+(height-200)+'px;overflow-x: hidden;'" v-if='editDiscountData.school_id'>
                    <div class='flex align-items'>
                        <img :src="editDiscountData.avatar" class='avatar40' alt="">
                        <div class='flex1 ml8'>
                            <div class='color3 font14'>{{editDiscountData.child_name}}</div>
                            <div class='font12 color6'><span :class='editDiscountData.in_school?"greenTag":"tag"'>{{editDiscountData.school_abb}}</span>｜{{editDiscountData.child_id}}｜{{editDiscountData.class_name}}</div>
                        </div>
                    </div>
                    <div class='tips mt20' v-if='editDiscountData.hasStatus'>
                        <div class='colorYellow font14 flex mb16'>
                            <span class='el-icon-warning font14 mt4'></span>
                            <span class='ml4 flex1'>当前校车费折扣“{{discountTitle(editDiscountData.discount)}}”下已有关联的未付账单，如修改折扣可作废未付账单。</span>
                        </div>
                        <div  class='' v-for='(_item,index) in editDiscountData.invoice_list.master'>
                            <div class='flex font12 addChildBill mt8' v-if='_item.status==10'>
                                <div class='flex1 widthFlex text_overflow  mr10'>
                                    <span class='color3'> {{_item.amount}}元</span> 
                                    <span class='color6'>｜ {{_item.title}}</span>
                                </div>
                                <span class='colorBlue cur-p' style='width:24px'  @click='cancelBill(_item.invoice_id,index,"edit")'>作废</span>
                            </div>
                        </div>   
                    </div>
                    <div class='mt24'>
                        <div class='font14 color6'>修改折扣</div>
                        <div class='row'>
                            <div class='col-md-6' v-for='(item,index) in configData.discount'>
                                <div class="radio font14 color3" style='margin-bottom:0'>
                                    <label style='line-height:1.5'>
                                        <input type="radio" v-model='discountRatio' :value="item.id" :disabled='editDiscountData.discount!=99 && editDiscountData.discount==item.id?true:false'>
                                        {{item.title}} <span class='greenTag ml4' v-if='editDiscountData.discount==item.id'>当前折扣</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-if='discountRatio==99' class='invalidBill mt8'>
                        <div class='flex align-items '>
                            <span class='font14 color3 fontBold'>优惠：</span>
                            <el-input
                                size="small"
                                placeholder="请输入特殊折扣"
                                type='number'
                                class='flex1'
                                v-model="ratio"
                                @input="calculateDiscount(ratio)" >
                            </el-input>
                            <span class='font14 color3 fontBold ml16'>%</span>
                            <span class='font14 color3 fontBold ml16' style="width: 10%;">
                                <span v-if="ratio!==''">{{discountValue}}</span>
                            </span>
                        </div>
                    </div> 
                    <div class='mt24'>
                        <div class='font14 color6'>备注</div>
                        <textarea class="form-control mt12" rows="2" v-model='remarks' placeholder="请输入备注"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" :disabled='btnDisanled' @click='editDiscountSave()'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    var height=document.documentElement.clientHeight;
    $(document).ready(function () {
        // 通过该方法来为每次弹出的模态框设置最新的zIndex值，从而使最新的modal显示在最前面
        $(document).on('show.bs.modal', '.modal', function (event) {
            var zIndex = 1040 + (10 * $('.modal:visible').length);
            $(this).css('z-index', zIndex);
            setTimeout(function() { 
                $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');
            }, 0);
        });
    });
    var branchAbb = '<?php echo $this->branchObj->abb;?>';
    var container=new Vue({
        el: "#box",
        data: {
            branchAbb:branchAbb,
            height:height,
            activeName:'bus',
            startYearList: [],
            configData:{},
            tuition_discount:{},
            startYear:'',
            searchChild:'',
            schoolFilter:'',
            levelFilter:'',
            statusFilter:'',
            busFilter:'',
            billFilter:'',
            moreFamily:false,
            confirmList:[{title:'待校车协调员确认',value:1},{title:'待第二审批确认',value:2}],
            billList:[{title:'已付',value:1},{title:'未付',value:2}],
            rangeFilter:'all',
            tableInvoice:{},
            tableInvoiceData:{},
            tableInvoiceSort_family:[],
            tableInvoiceSortDataCopy:[],
            createBill:{
                title:'',
                billDate:'',
                billTime:''
            },
            childInvoice:[],
            childInvoiceAll:false,
            invoiceCheck:[],
            recordData:[],
            brotherChild:[],
            childSiteId:[],
            childSiteIndex:null,
            tipVisibles:{},
            childBilllink_info:{},
            childBillinvoice_info:{},
            delType:'',
            btnDisanled:false,
            detailData:{},
            batchType:'',
            dateRange:{},
            shortcutsList:[],
            busCheck:[],
            financeCheck:[],
            style:'',
            tips:{},
            ratio:'',
            customize:{},
            editDiscountData:{},
            discountRatio:'',
            editDiscountDataIndex:'',
            editDiscountDataList:{},
            tableLoading:true,
            remarks:'',
            discountValue:'',
        },
        created: function() {
			this.getInit()
        },
        updated: function () {
            head.Util.aDialog();
        },
        computed: {
            searchData: function() {
                var search = this.searchChild.trim().toUpperCase();
                var family = [];
                let that = this;
                if (!that.tableInvoiceData || !that.tableInvoice || !that.tableInvoice.list_by_family) {
                    that.tableLoading=false
                    return [];  // 或者其他合适的默认返回值
                }
                try {
                    that.tableLoading=true
                    if (search) {
                        for (let key in that.tableInvoiceData) {
                            let group = that.tableInvoice.list_by_family[key];
                            group.forEach(item => {
                                if (item.child_name.toUpperCase().includes(search)) {
                                    family.push(item.family_id);
                                }
                            });
                        }
                        family = [...new Set(family)];
                    } else {
                        family = that.tableInvoice.sort_family;
                    }
                   
                    return family.filter(familyId => {
                        let items = that.tableInvoice.list_by_family[familyId];
                        if (!items || !Array.isArray(items)) {
                            return false;
                        }
                        
                        return items.some(item => 
                            (that.levelFilter === '' || item.fee_level == that.levelFilter) &&
                            (that.schoolFilter === '' || (item.tuition_discount!=null && item.tuition_discount.indexOf(that.schoolFilter) !== -1)) &&
                            (that.busFilter === '' || item.discount == that.busFilter) &&
                            (that.statusFilter === '' || 
                                (that.statusFilter == 1 && item.can_batch_bus) ||
                                (that.statusFilter == 2 && item.can_batch_finance)
                            ) &&
                            (that.billFilter === '' || 
                                (that.billFilter == 1 && !item.billStatus) ||
                                (that.billFilter == 2 && item.billStatus)
                            ) &&
                            (!that.moreFamily || items.length>1)
                        );
                    });
                } finally {
                    that.$nextTick(() => {
                        setTimeout(() => {
                            that.tableLoading = false;
                        },200);
                    })
                }
            },
            pickerOptions() {
                const self = this; 
                return {
                    disabledDate(time) {
                        const { firstDay, lastDay } = self.dateRange;
                        if (!firstDay || !lastDay) return false;
                        return time < firstDay || time > lastDay;
                    },
                    shortcuts:self.shortcutsList
                };
            }
        },
        methods: {
            handleClick(){

            },
            switchYear(){
                this.getInit('year')
            },
            getInit(type){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        url:'getConfig',
                        start_year:that.startYear
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.configData=data.data.config
                            that.startYearList=data.data.startYearList
                            that.startYear=data.data.startYear
                            that.getInvoiceList(type)
                            that.configData.invoice_range=that.generateDates(that.configData.invoice_range, that.startYear)
                            that.dateRange = that.getFirstAndLastDay(that.configData.invoice_range[0].endpoint[0], that.configData.invoice_range[1].endpoint[1]);
                            that.shortcutsList=[]
                            that.configData.invoice_range.forEach(item => {
                                that.shortcutsList.push({
                                    text:item.title,
                                    onClick(picker) {
                                        const { firstDay, lastDay } =that.getFirstAndLastDay(item.endpoint[0], item.endpoint[1]);
                                        picker.$emit('pick', [firstDay, lastDay]);
                                    }
                                })
                            });
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.tableLoading=false
                            that.tabLoading=false
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.tabLoading=false
                    },
                })  
            },
            getFirstAndLastDay(yearMonthStr1, yearMonthStr2) {
                let year1 = parseInt(yearMonthStr1.substring(0, 4), 10);
                let month1 = parseInt(yearMonthStr1.substring(4, 6), 10) - 1;
                let year2 = parseInt(yearMonthStr2.substring(0, 4), 10);
                let month2 = parseInt(yearMonthStr2.substring(4, 6), 10) - 1;
                let firstDay = new Date(year1, month1, 1);
                let lastDay = new Date(year2, month2 + 1, 0);
                return { firstDay, lastDay };
            },
            generateDates(data, startyear) {
                return data.map(item => {
                    const months = item.endpoint;
                    let year = parseInt(startyear);
                    if (months[0] === '01') {
                        year = parseInt(startyear) + 1;
                    }
                    const dates = months.map(month => {
                        const monthNum = parseInt(month);
                        const start = `${year}${String(monthNum).padStart(2, '0')}`;
                        const end = `${year}${String(monthNum).padStart(2, '0')}`;
                        return { start, end };
                    });
                    const startDate = dates[0].start;
                    const endDate = dates[dates.length - 1].end;
                    return {
                    ...item,
                    endpoint: [startDate, endDate],
                    };
                });
            },
            discountTitle(id){
                return this.configData.discount.find(i => i.id === parseInt(id))?.title || '';
            },
            tuitionTitle(id){
                return this.tuition_discount.find(i => i.id === parseInt(id))?.title || '';
            },
            getInvoiceList(type){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        url:'advanceInvoiceList',
                        start_year:this.startYear,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.tuition_discount=data.data.tuition_discount
                            that.childInvoice=[]
                            that.invoiceCheck = []
                            that.busCheck = []
                            that.financeCheck = []
                            that.tableInvoice=data.data
                            that.tableInvoiceData= that.addBillStatusFlag(data.data.list_by_family)
                            that.tableInvoiceSort_family=data.data.sort_family
                            that.tableInvoiceSortDataCopy=JSON.parse(JSON.stringify(data.data.sort_family))
                            if(type){
                                that.searchChild=''
                                that.schoolFilter=''
                                that.levelFilter=''
                                that.statusFilter=''
                                that.busFilter=''
                                that.billFilter=''
                                that.moreFamily=false
                            }
                            that.tableLoading=false
                        }else{
                            that.tableLoading=false
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })  
            },
            addBillStatusFlag(data) {
                for (let familyId in data) {
                    const familyItems = data[familyId];
                    familyItems.forEach(student => {
                        let hasUnpaid = false;
                        const invoiceList = student.invoice_list || {};
                        if (!hasUnpaid && Array.isArray(invoiceList.master)) {
                            hasUnpaid = invoiceList.master.some(item => item.status === 10);
                        }
                        if (!hasUnpaid && Array.isArray(invoiceList.other)) {
                            hasUnpaid = invoiceList.other.some(item => item.status === 10);
                        }
                        Vue.set(student,'can_batch_bus', student.discount !== null && student.coordinator == null)
                        Vue.set(student,'can_batch_finance', student.discount !== null && student.finance == null && student.coordinator != null)
                        Vue.set(student,'billStatus',hasUnpaid)
                        if(student.can_batch_invoice){
                            this.invoiceCheck.push(student)
                        }
                        if(student.can_batch_bus){
                            this.busCheck.push(student)
                        }
                        if(student.can_batch_finance){
                            this.financeCheck.push(student)
                        }
                    });
                }
                return data;
            },
            filterRange(type){
                this.rangeFilter=type
            },
            masterStatus(list){
                return !list.some(item => item.status ===20);
            },
            customizeDiscount(list,item){
                this.customize={
                    list:list,
                    item:item
                }
                if(list.discount==99){
                    this.ratio=list.off_ratio
                    this.calculateDiscount(this.ratio)
                }else{
                    this.ratio=''
                }
                this.remarks=''
                $("#discountModal").modal('show')
            },
            discountSave(){
                if(this.customize.item.id==99 && this.ratio==''){
                    resultTip({
                        error: 'warning',
                        msg:'请输入特殊折扣'
                    });
                    return
                }
                if(this.remarks.trim()==''){
                    resultTip({
                        error: 'warning',
                        msg:'请输入备注'
                    });
                    return
                }
                this.setDiscountTitle(this.customize.list,this.customize.item)
            },
            editDiscount(index,list){
                this.editDiscountData=this.tableInvoiceData[list][index]
                this.editDiscountDataIndex=index
                this.editDiscountDataList=list
                this.editDiscountData.hasStatus = this.editDiscountData.invoice_list.master.some(item => item.status === 10);
                this.discountRatio=''
                this.ratio=''
                this.remarks=''
                $("#editDiscountModal").modal('show')
            },
            editDiscountSave(){
                if(this.discountRatio==''){
                    resultTip({
                        error: 'warning',
                        msg:'请选择折扣'
                    });
                    return
                }
                if(this.discountRatio==99){
                    if(this.ratio==''){
                        resultTip({
                            error: 'warning',
                            msg:'请输入特殊折扣'
                        });
                        return
                    }
                }
                if(this.remarks.trim()==''){
                    resultTip({
                        error: 'warning',
                        msg:'请输入备注'
                    });
                    return
                }
                this.setDiscountTitle( this.editDiscountData,'edit')
            },
            setDiscountTitle(list,item){
                let that=this
                this.btnDisanled=true
                let dataList={}
                if(item=='edit'){
                    let ratioData=''
                    if(this.discountRatio!=99){
                        ratioData=this.configData.discount.find(i => i.id === parseInt(this.discountRatio))?.ratio;
                    }
                    dataList= {
                        url:'setDiscount',
                        start_year:this.startYear,
                        child_id:list.child_id,
	                    discount:this.discountRatio,
                        ratio:this.discountRatio==99?this.ratio:ratioData,
                        remark:this.remarks
                    }
                }else{
                    dataList= {
                        url:'setDiscount',
                        start_year:this.startYear,
                        child_id:list.child_id,
	                    discount:item.id,
                        ratio:item.id==99?this.ratio:item.ratio,
                        remark:this.remarks
                    } 
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data: dataList,
                    success: function(data) {
                        if (data.state == 'success') {
                            that.getInvoiceList()
                            resultTip({
                                msg: data.message
                            });
                            $("#discountModal").modal('hide')
                            $("#editDiscountModal").modal('hide')
                            that.btnDisanled=false
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.btnDisanled=false
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.btnDisanled=false
                    },
                })  
            },
            onChildCheck(type) {
                const checkList = this[`${type}Check`];
                checkList.length = 0;
                const canBatchKey = `can_batch_${type}`;
                this.searchData.forEach(list => {
                    (this.tableInvoiceData[list] || []).forEach(item => {
                        if (item[canBatchKey]) {
                            checkList.push(item);
                        }
                    });
                });
                if (checkList.length === this.childInvoice.length) {
                    this.childInvoiceAll = true;
                } else {
                    this.childInvoiceAll = false;
                }
            },
            onChildCheckAll(type) {
                const checkList = this[`${type}Check`];
                const canBatchKey = `can_batch_${type}`;
                
                if (this.tableInvoiceSort_family.length) {
                    this.childInvoice = [];
                    checkList.length = 0;
                    if (this.childInvoiceAll) {
                        this.searchData.forEach(list => {
                            (this.tableInvoiceData[list] || []).forEach(item => {
                                if (item[canBatchKey]) {
                                    checkList.push(item);
                                    this.childInvoice.push(item.child_id);
                                }
                            });
                        });
                    }
                }
            },
            cancelBill(id,index,type){
                if(id){
                    this.delId={id:id,type:type,index:index}
                    this.delType='cancel'
                    $("#delModal").modal('show')
                    return
                }
                let that=this
                that.btnDisanled=true
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        url:'invalidInvoice',
                        start_year:this.startYear,
                        invoice_id:this.delId.id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.getInvoiceList()
                            resultTip({
                                msg: data.message
                            });
                            that.btnDisanled=false
                            $("#delModal").modal('hide')
                            if(that.delId.type){
                                that.editDiscountData.invoice_list.master.splice(that.delId.index,1)
                                that.editDiscountData.hasStatus = that.editDiscountData.invoice_list.master.some(item => item.status === 10);
                            }
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.btnDisanled=false
                        }
                    },
                    error: function(data) {
                        that.btnDisanled=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            tableCreateBill(list){
                if(list=='batch'){
                    if(this.childInvoice.length==0){
                        resultTip({
                            error: 'warning',
                            msg:'请选择生成的账单列表'
                        });
                        return
                    }
                    this.style='batch'
                }else{
                    this.childInvoice=[list.child_id]
                    this.style=''
                }
                this.createBill={
                    title:'',
                    billDate:'',
                    billTime:''
                }
                this.tips={}
                $('#createBillModal').modal('show')
            },
            
            getSimilarOrder(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        url:'getSimilarOrder',
                        start_year:this.startYear,
                        child_ids:this.childInvoice,
                        startdate:this.createBill.billDate[0],
                        enddate:this.createBill.billDate[1]
                    },
                    success: function(data) {
                        that.tips=data
                    },
                    error: function(data) {
                        
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            batchCreateInvoice(){
                let that=this
                if(this.createBill.title==''){
                    resultTip({
                        error: 'warning',
                        msg: '请输入标题'
                    });
                    return
                }
                if(this.createBill.billDate==''){
                    resultTip({
                        error: 'warning',
                        msg: '请选择账单区间'
                    });
                    return
                }
                if(this.createBill.billTime==''){
                    resultTip({
                        error: 'warning',
                        msg: '请选择账单到期时间'
                    });
                    return
                }
                that.btnDisanled=true
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        url:'batchCreateInvoice',
                        start_year:this.startYear,
                        child_ids:this.childInvoice,
                        title:this.createBill.title,
                        startdate:this.createBill.billDate[0],
                        enddate:this.createBill.billDate[1],
                        duetime:this.createBill.billTime
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.getInvoiceList()
                            that.childInvoice=[]
                            that.childInvoiceAll=false
                            resultTip({
                                msg: data.message
                            });
                            that.btnDisanled=false
                           $('#createBillModal').modal('hide')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.btnDisanled=false
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.btnDisanled=false
                    },
                })  
            },
            batchHandle(type){
                this.batchType=type
                this.childInvoice=[]
                this.childInvoiceAll=false
                this.style='batch'
            },
            batchConfirm(){
                if(this.batchType=='bill'){
                    this.tableCreateBill('batch')
                }else{
                    this.delType=this.batchType
                    if(this.childInvoice.length==0){
                        resultTip({
                            error: 'warning',
                            msg: '请选择需要确认的学生'
                        });
                        return
                    }
                    $("#delModal").modal('show')
                }
            },
            singleConfirm(id,type){
                this.delType=type
                this.childInvoice=[id]
                $("#delModal").modal('show')
                this.style=''
            },
            cancelConfirm(){
                this.batchType=''
                this.childInvoice=[]
                this.childInvoiceAll=false
            },
            getDiscountByChildId(data, childIds) {
                const childMap = {};
                for (const key in data) {
                    const children = data[key];
                    children.forEach(child => {
                     childMap[child.child_id] = child.discount;
                    });
                }
                return childIds.map(childid => ({
                    childid,
                    discount: childMap[childid] || null // 如果找不到可以设为 null、0 或其他默认值
                }));
            },
            batchSave(){
                let that=this
                this.btnDisanled=true
                let child_data=this.getDiscountByChildId(this.tableInvoice.list_by_family,this.childInvoice)
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        url:'confirmDiscount',
                        child_data:child_data,
                        type:this.delType,
                        start_year:this.startYear,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.getInvoiceList()
                            that.batchType=''
                            that.childInvoice=[]
                            that.childInvoiceAll=false
                            that.btnDisanled=false
                            resultTip({
                                msg: data.message
                            });
                            $("#delModal").modal('hide')

                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.btnDisanled=false
                        }
                        
                    },
                    error: function(data) {
                        that.btnDisanled=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            studentbill(item){
                window.open('<?php echo $this->createUrl('/child/invoice/index'); ?>&childid='+item.child_id+'&schoolyear='+this.startYear,'_blank')
            },
            exportData(){
                let year=this.startYearList.find(item => item.key==this.startYear);
                var title=year.value+"学年_"+this.branchAbb+'_校车费.xlsx'
                const ws_name = "SheetJS";
                let exportDatas=[]

                for (let familyId in this.tableInvoiceData) {
                    const familyItems = this.tableInvoiceData[familyId];
                    familyItems.forEach(item => {
                        let tuition_discount=''
                        if(item.tuition_discount !=null && item.tuition_discount.length!=0){
                            item.tuition_discount.forEach(data => {
                                tuition_discount+=this.tuitionTitle(data)+ ";"
                            });
                        }else{
                            tuition_discount=''
                        }
                        // let billData=''
                        // if(item.invoice_list.master.length){
                        //     item.invoice_list.master.forEach(data => {
                        //         let status=data.status==10?"未付":"已付"
                        //         billData+=data.amount+"|"+data.title+"|"+status+ "; "
                        //     });
                        // }else{
                        //     billData='无'
                        // }
                        exportDatas.push(
                            {
                            'ID':item.child_id,
                            '<?php echo Yii::t('global', '姓名') ?>':item.child_name,
                            "<?php echo Yii::t('labels', '学校') ?>":item.school_id,
                            "<?php echo Yii::t('labels', '班级') ?>":item.class_name,
                            "<?php echo Yii::t('labels', '档位') ?>":this.configData.fee_level[item.fee_level].title,
                            "<?php echo Yii::t('labels', '学费折扣') ?>":tuition_discount,
                            "<?php echo Yii::t('labels', '校车折扣') ?>":this.discountTitle(item.discount),
                            "<?php echo Yii::t('labels', '乘坐方式') ?>":item.journey==1?"上学":item.journey==2?"放学":"双程",
                            "<?php echo Yii::t('labels', '协调员确认状态') ?>":item.coordinator!=null?"已确认":"未确认",
                            "<?php echo Yii::t('labels', '第二审批确认状态') ?>":item.finance!=null?"已确认":"未确认",
                            "<?php echo Yii::t('labels', '当前账单') ?>":item.invoice_list.master.length==0?"无":'有',
                        });
                    })
                }
                var wb=XLSX.utils.json_to_sheet(exportDatas,{
                    origin:'A1',// 从A1开始增加内容
                    header: ["ID", "姓名" ,"学校", "班级", "档位", "学费折扣", "校车折扣", "乘坐方式", "协调员确认状态", "第二审批确认状态", "当前账单"],
                });
                const workbook = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(workbook, wb, ws_name);
                const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                const blob = new Blob([wbout], {type: 'application/octet-stream'});
                let link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = title;
                link.click();
                setTimeout(function() {
                    // 延时释放掉obj
                    URL.revokeObjectURL(link.href);
                    link.remove();
                }, 500);
            },
            calculateDiscount(ratio) {
                if (ratio >= 0 && ratio <= 100) {
                    let discountValue = (100 - parseFloat(ratio)) / 10;
                    this.discountValue = discountValue== 0 ? '免费' : discountValue + '折'
                } else {
                    resultTip({
                        error: 'warning',
                        msg: '请输入有效的百分比（0-100）'
                    });
                    this.ratio = 100;
                    this.discountValue = '免费';
                }
            },
        }
    })
</script>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
