<?php

class CashController extends BranchBasedController
{
    public $dcategory = 'finance';
    public $dsubcategory = 'handover';
    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'main';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Finance Mgt');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mfinance/cash/index');
    }

	public function actionIndex()
	{
        Yii::import('common.models.invoice.*');
        Yii::import('common.models.invoice.ChildCredit');
        Yii::import('common.models.invoice.CashHandoverHistory');
        Yii::import('common.models.invoice.CashHandoverLink');
        $category = Yii::app()->request->getParam('category','current');
        $cs = Yii::app()->clientScript;
		$cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/jquery.printThis.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/xlsx.full.min.js');
        $this->render('index',array('category'=>$category));
	}

    //保存现金交接信息
    public function actionSaveHandover(){
        Yii::import('common.models.invoice.InvoiceTransaction');
        Yii::import('common.models.invoice.ChildCredit');
        Yii::import('common.models.invoice.CashHandoverHistory');
        Yii::import('common.models.invoice.CashHandoverLink');
        $this->addMessage('state', 'fail');
        $this->addMessage('message', '失败');
        if (Yii::app()->request->isAjaxRequest){
            $transactionIds = Yii::app()->request->getPost('transactionIds','');
            $creditIds = Yii::app()->request->getPost('creditIds','');
            $fTransactionIds = Yii::app()->request->getPost('fTransactionIds');
            $fCreditIds = Yii::app()->request->getPost('fCreditIds');
            $mdTransactionIds = md5(implode(",", $transactionIds));
            $mdFCreditIds = md5(implode(",", $creditIds));
            $uid = Yii::app()->user->getId();
            // if (($fTransactionIds == $mdTransactionIds) && ($fCreditIds == $mdFCreditIds)){
                $amount = 0;
                $invoiceIdStr = '';
                $transactionIdStr = '';
                $tranDataList = array();
                $creditDataList = array();
                //查流水
                if (!empty($transactionIds)){
                    $command = Yii::app()->db->createCommand();
                    $command->from(InvoiceTransaction::model()->tableName());
                    $command->where(array('in','id',$transactionIds));
                    $command->andWhere("transfer_to_uid=:transfer_to_uid", array(':transfer_to_uid'=>0));
                    $tranDataList = $command->queryAll();
                    foreach ($tranDataList as $val){
                        $amount += $val['amount'];
                        $invoiceIdStr .= $val['invoice_id'] . ',';
                        $transactionIdStr .= $val['id'] . ',';
                    }
                    $criter = new CDbCriteria;
                    $criter->compare('id', $transactionIds);
                    $criter->compare('transfer_to_uid', 0);
                    InvoiceTransaction::model()->updateAll(array('transfer_to_uid'=>$uid,'transfer_timestamp'=>time()), $criter);
                }
                //查个户
                if (!empty($creditIds)){
                    $command = Yii::app()->db->createCommand();
                    $command->from(ChildCredit::model()->tableName());
                    $command->where(array('in','cid',$creditIds));
                    $command->andWhere("transfer_to_uid=:transfer_to_uid", array(':transfer_to_uid'=>0));
                    $creditDataList = $command->queryAll();
                    foreach ($creditDataList as $val){
                        $amount += $val['amount'];
                    }
                    $criter = new CDbCriteria;
                    $criter->compare('cid', $creditIds);
                    $criter->compare('transfer_to_uid', 0);
                    ChildCredit::model()->updateAll(array('transfer_to_uid'=>$uid,'transfer_timestamp'=>  time()), $criter);
                }
                if ($amount){
                    $invoiceIdStr = trim($invoiceIdStr, ',');
                    $transactionIdStr = trim($transactionIdStr, ',');
                    $cashHistory = new CashHandoverHistory();
                    $cashHistory->handover_date = time();
                    $cashHistory->count = count($tranDataList);
                    $cashHistory->userid = $uid;
                    $cashHistory->schoolid = $this->branchId;
                    $cashHistory->amount = $amount;
                    $cashHistory->timestmpe = time();
                    $cashHistory->status = 0;
                    $cashHistory->invoice_id = $invoiceIdStr;
                    $cashHistory->transaction_id = $transactionIdStr;
                    if ($cashHistory->save()){
                        foreach ($tranDataList as $val){
                            $link = new CashHandoverLink();
                            $link->handover_id = $cashHistory->id;
                            $link->invoice_id = $val['invoice_id'];
                            $link->transaction_id = $val['id'];
                            $link->save();
                        }
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', Yii::t('message','success'));
                        $this->addMessage('refresh',true);
                    }
                }
            // }
        }
        $this->showMessage();
    }

}