<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
class Notification {
    public $configs;
    public function __construct(){
        if(!defined("NOTIFICATION_CONFIG"))
		define("NOTIFICATION_CONFIG", true);
        $configs = CommonUtils::getConfig("application.components.user.NotificationConfig");
        $this->configs = $configs;
    }
    
    /*
     * 保存通知信息
     * @param array $data e.g. array('staff_uid'=>$staffUid,'flagid'=>$flagId,'title'=>$title,'type'=>'type')
     * @staff_uid 需要通知人的ID
     * @flagid    关联业务的主键
     * @type      通知的类型 e.g. 请假审请、加班审请
     * @return boolean
     */
    public function save($data){
        $model = new UserNotification();
        $model->setAttributes($data);
        $model->status = UserNotification::WAITING_STATUS;
        $model->created = time();
        if ($model->save()){
            return TRUE;
        }else{
            return FALSE;
        }
    }
    
    /**
     * 更新通知状态
     * @param int $flagId   业务关联表主键
     * @param int $type     通知分类表主键
     * @param int $staffId  通知人的主键
     * return boolean
     */
    public function update($staffUid,$flagId,$type){
        $result = false;
        if ($staffUid && $flagId && $type) {
            $crite = new CDbCriteria;
            $crite->compare('staff_uid', $staffUid);
            $crite->compare('flagid', $flagId);
            $crite->compare('type', $type);
            if (UserNotification::model()->updateAll(array('status'=>UserNotification::END_STATUS), $crite)){
                $result = TRUE;
            }
        }
        return $result;
    }
    
    /**
     * 取得某个用户按类型统计的条数
     * @param int  $staffUid 员工ID
     * @return array e.g. array('hr_leave'=>2);
     */
    public function getData($staffUid){
        return UserNotification::getDataByUserCountType($staffUid);
    }
    
    /**
     * 取消某个用户某种类型的业务通知
     * @param int $staffUid 需要取消的用户ID
     * @param int $flagId   相关联的业务ID
     * @param int $type     通知类型
     * @return boolean
     */
    public function deleteData($staffUid,$flagId,$type){
        if ($staffUid && $flagId && $type){
            return UserNotification::deleteDataByUser($staffUid,$flagId,$type); 
        }else
            return FALSE;
    }
    
    /**
     * 过滤通知配置信息
     * @param int $id 通右配置ID(配置唯一)
     * @return array $config
     */
    public function filterConfigs($id){
        $config = array();
        if ($id){
            $config = $this->configs[$id];
        }
        return $config;
    }
}
?>
