<style type="text/css">
    .mask{
        position: fixed;
        left: 0px;
        top: 0px;
        background: #000;
        width: 100%;
        height: 100%;
        opacity: 0.5;
    }
    .tankuang{
        position: fixed;
        background: #fff;
        width: 50%;
        border-radius: 5px;
        left:25%
    }
    #header{
        height: 40px;
    }
    #header-right{
        position: absolute;
        width: 25px;
        height: 25px;
        border-radius: 5px;
        background: red;
        color: #fff;
        right: 5px;
        top: 5px;
        text-align: center;
    }
    .time{
        margin: auto 25px;
    }
    .btn-group{
        padding: 25px;
    }
</style>
<div id="childinfo">
    <?php $this->widget('ext.childInfo.ChildInfo', array('childObj' => $this->childObj)) ?>
</div>
<div class="mask" style="display: none"></div>
<div class="tankuang" style="display: none">
    <div id="header">
        <div id="header-right" onclick="cancel()">x</div>
    </div>
    <div class="content">
        <div class="time">
            <?php echo Yii::t('child', 'Estimated Leave Date')?>
            <?php
            $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                "model"=>$model,
                "attribute"=>"est_quit_date",
                "options"=>array(
                    'changeMonth'=>true,
                    'changeYear'=>true,
                    'dateFormat'=>'yy-mm-dd', //很重要，不要随便修改 (RAN)
                ),
                "htmlOptions"=>array(
                    'class'=>'input'
                )
            ));
            ?>
        </div>
        <div class="btn-group">
            <button type="button" onclick="cancel()">取消</button>
            <button type="button" onclick="saveState()">保存</button>
        </div>
    </div>
</div>
<?php if($hasChangeAccess):?>
	<script type="text/javascript">
        function dianwo(){

        }
        function cancel(){
            $(".mask").hide();
            $(".tankuang").hide();
        }

		function assignClass(e)
		{
			var confirm_info = '<?php echo Yii::t("child", 'Are you sure to assign class?') ?>';
			var currentClass = $("#" + e).val();
			if (currentClass)
			{
				if (window.confirm(confirm_info))
				{
					$.ajax({
						type: "POST",
						url: '<?php echo $this->createUrl('//child/index/processAssignClass') ?>',
						data: "classId=" + currentClass + '&type=' + e,
						dataType: 'json',
						success: function(data) {
							if(data.state=='success'){
								window.location.reload();
							}
							else{
								alert(data.message);
								window.location.reload();
							}
						}
					});
				}
			}
		}

		function changeState(_this)
		{
            var state = $(_this).val()
            if(state == 888){
               $("#changeState1").click();
            }else if(state == 999){
                $("#changeState2").click();
            }else{
                if (confirm('<?php echo Yii::t("child", 'Change Status?') ?>')){
                    $.ajax({
                        type: 'post',
                        data: {state: $(_this).val()},
                        dataType: 'json',
                        success: function(data){
                            if (data.state == 'success'){
                                window.location.reload();
                            } else{
                                alert(data.message);
                                window.location.reload();
                            }
                        }
                    });
                }
            }

		}

        function saveState()
        {
            var est_quit_date = $('#ChildProfileBasic_est_quit_date').val();
            $.ajax({
                type: 'post',
                data: {
                    state: 888,
                    est_quit_date:est_quit_date,
                },
                dataType: 'json',
                success: function(data){
                    if (data.state == 'success'){
                        window.location.reload();
                    } else{
                        alert(data.message);
                    }
                }
            });
        }
	</script>
<?php endif;?>
<div class="table_full">
    <table width="100%">
        <colgroup>
            <col width="200">
            <col>
        </colgroup>
        <tbody>
            <tr>
                <th><?php echo Yii::t("child", "Current Status"); ?></th>
                <td>
                    <?php echo $this->childObj->getStatus(); ?>
                    <?php
					
					if($hasChangeAccess)
					echo CHtml::link(Yii::t('child', 'Change Status'), 'javascript:void(0);',array('onClick'=>'$("#child_state").toggle();'));?>
					
					<?php if($hasChangeAccess):?>
                    <div id="child_state" style="display: none;">
                        <ul class="single_list">
                            <?php
                            $stateArr = OA::getChildStatus();
                            if ($this->childObj->status != ChildProfileBasic::STATS_ACTIVE){
                                unset($stateArr[ChildProfileBasic::STATS_ACTIVE]);
                            }
                            //退学处理中的单独
                            echo CHtml::radioButtonList('changeStatus', $this->childObj->status, $stateArr,
                                array(
                                'template'=>'<li>{input} {label}</li>',
                                'separator'=>'',
                                'onchange'=>'changeState(this);')
                            );
                            echo CHtml::link(
                                "<?php echo Yii::t('child','Dropping Out')?>",
                                array('/child/index/droppingOut'),
                                array(
                                    'id'=>'changeState1',
                                    'class'=>'mr15 J_dialog',
                                    'style'=>'display:none',
                                    'title'=>"退学处理中"
                                )
                            );
                            echo CHtml::link(
                                "<?php echo Yii::t('child','Dropping Out')?>",
                                array('/child/index/alreadyDroppingOut'),
                                array(
                                    'id'=>'changeState2',
                                    'class'=>'mr15 J_dialog',
                                    'style'=>'display:none',
                                    'title'=>"已退学"
                                )
                            );

                            ?>
                        </ul>
                    </div>
					<?php endif;?>
                </td>
            </tr>
            <?php if (true || $this->childObj->status < ChildProfileBasic::STATS_GRADUATED):?>
                <tr>
                    <th rowspan="2"><?php echo Yii::t("child", "Class Assignments"); ?></th>
                    <td>
                        <?php
							$createClassLink = Yii::app()->params['OABaseUrl'] . '/modules/classmgt/admin.php?action=add';
							//if ($this->childObj->status == 0 || $this->childObj->stat->calendar == $branch->schcalendar): 
							if ($this->childObj->status < ChildProfileBasic::STATS_GRADUATED): ?>
								<?php
									if(empty($branch->schcalendar)):
										echo Yii::t("message", "No school calendar assigned this year.");
									else:
								?>
										<?php
										$calendarC = Calendar::model()->with(
														array('currentClasses' => array('order'=>'child_age asc','params' => array(':schoolid' => $this->childObj->schoolid)))
												)->findByPk($branch->schcalendar);
										if(empty($calendarC)):
											echo Yii::t("message", "No school calendar assigned this year.");
										else:
										?>
												<p><?php echo Yii::t('child', 'Current School Year (:startYear - :endYear)', array(':startYear' => $calendarC->startyear, ':endYear' => $calendarC->startyear + 1)); ?></p>
												<?php
												$currentClasses = CHtml::listData($calendarC->currentClasses, 'classid', 'title');
					
												if (empty($currentClasses)) {
													echo '<br />';
													echo Yii::t("child", "No class found, :createLink", array(":createLink"=>CHtml::link(Yii::t("child", "Create Class?"), $createClassLink, array("target"=>"_blank"))));
												} else {
													if(isset($currentClasses[$this->childObj->stat->classid])):
														echo CHtml::openTag('span', array('class'=>'mr15'));
														echo $currentClasses[$this->childObj->stat->classid];
														echo CHtml::closeTag('span');
														$txt = Yii::t('child','Re-assign');
													else:
														$txt = Yii::t('child','Assign');
													endif;
													
													if($hasChangeAccess):
														echo CHtml::link($txt, 'javascript:void(0);', array('onClick'=>'$("#current-class-list").toggle();'));
														echo '<br />';
														
														echo CHtml::listBox("currentClass", $this->childObj->stat->classid, $currentClasses, array('class' => 'mb5', 'size' => 8, 'onchange' => 'assignClass("current-class-list");','id'=>'current-class-list', 'style'=>'display:none'));
													endif;
												}
										endif;
									endif;
								?>
                        <?php endif; ?>
                    </td>
                </tr>
                <tr>
                    <td>
                        <?php
                        $nextCid = $branch->getNextCalendarId();
                        $nextSid = $this->childObj->schoolid;
                        if ($this->childObj->nextYear) {
                        	$nextCid = $this->childObj->nextYear->calendar;
                        	$nextSid = $this->childObj->nextYear->schoolid;
                        	if ($this->childObj->nextYear->stat == 20) {
	                        	$nextCLassId = $this->childObj->nextYear->classid;
                        	} else{
                        		$nextCLassId = 0;
                        	}
                        }
						if(!empty($nextCid)):
							$calendarN = Calendar::model()->with(
                                        array('nextClasses' => array('params' => array(':schoolid' => $nextSid)))
									)->findByPk($nextCid);
                        ?>
                        <p><?php echo Yii::t('child', 'Next School Year (:startYear - :endYear)', array(':startYear' => $calendarN->startyear, ':endYear' => $calendarN->startyear + 1)); ?></p>
							<?php
							$nextClasses = CHtml::listData($calendarN->nextClasses, 'classid', 'title');

							if (empty($nextClasses)) {
								echo '<br/>';
								echo Yii::t("child", "No class found, :createLink", array(":createLink"=>CHtml::link(Yii::t("child", "Create Class?"), $createClassLink, array("target"=>"_blank"))));
							} else {
								if(isset($nextClasses[$nextCLassId])):
									$nextClasses[0] = '取消分班';
									echo CHtml::openTag('span', array('class'=>'mr15'));
									echo $nextClasses[$nextCLassId];
									echo CHtml::closeTag('span');
									$txt = Yii::t('child','Re-assign');
								else:
									$txt = Yii::t('child','Assign');
								endif;
								
								if($hasChangeAccess):
									echo CHtml::link($txt, 'javascript:void(0);', array('onClick'=>'$("#next-class-list").toggle();'));
									echo '<br />';
								
									echo CHtml::listBox("nextClass", $nextCLassId, $nextClasses, array('class' => 'mb5', 'size' => 8, 'onchange' => 'assignClass("next-class-list");','id'=>'next-class-list', 'style'=>'display:none'));
								endif;
							}
						else:
							echo CHtml::openTag("p");
							echo Yii::t("campus", "No calendar of next year assigned.");
							echo CHtml::closeTag("p");
						endif;
						?>
                    </td>
                </tr>

            <?php endif; ?>
            <tr>
                <th><?php echo Yii::t("child", "All Classes"); ?></th>
                <td>
                    <?php
                    global $allB;
                    $allB = $allBranch;

                    function getCampus($dbval) {
                        global $allB;
                        return isset($allB[$dbval]) ? $allB[$dbval]['title'] : null;
                    }

                    $this->widget('ext.ivyCGridView.IvyCGridView', array(
                        'id' => 'child-class',
                        'dataProvider' => $dataProvider,
                        'template' => "<div class='table_list'>{items}</div><div class='table_list'>{summary}</div><div class='table_list'>{pager}</div>",
                        'colgroups' => array(
                            array(
                                "colwidth" => array(140, 120, 300, 120, 200),
                            )
                        ),
                        'ajaxUpdate' => false,
                        'columns' => array(
                            'campus' => array(
                                'header' => Yii::t('child', 'Campus'),
                                'value' => 'getCampus($data->schoolid)'
                            ),
                            'calendar' => array(
                                'header' => Yii::t('child', 'Calendar'),
                                'value' => '$data->calendarNonTogether->startyear . " ~ " . intval($data->calendarNonTogether->startyear + 1)',
                            ),
                            'class' => array(
                                'header' => Yii::t('child', 'Class'),
                                'value' => '$data->classNonTogether->title'
                            ),
                            'status' => array(
                                'header' => Yii::t('payment', 'Status'),
                                'value' => 'OA::getChildClassStatus($data->stat)',
                            ),
							array(
								'header' => Yii::t('payment', 'User'),
								'value' => 'isset($data->userInfo)?$data->userInfo->getName():""',
							),
                            'timestamp' => array(
                                'header' => Yii::t('child', 'Updated Date'),
                                'value' => 'OA::formatDateTime($data->timestamp)',
                            )
                        ),
                    ));
                    ?>
                </td>
            </tr>
        </tbody>
    </table>
</div>