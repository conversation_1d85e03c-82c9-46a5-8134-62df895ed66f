<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo Yii::t('site','Finance Management');?></li>
        <li class="active">付款查询</li>
    </ol>
    <form action="<?php echo $this->createUrl('query')?>" class="form-inline mb15 J_ajaxForm" method="post">
        <?php echo CHtml::dropDownList('invoicetype', '', $invoiceTypes, array('class'=>'form-control form-group', 'empty'=>Yii::t('global', 'All')));?>
        <?php
        $this->widget('zii.widgets.jui.CJuiDatePicker',array(
            'name'=>'startdate',
            'id'=>'startdate',
            'options'=>array(
                'dateFormat'=>'yy-mm-dd',
            ),
            'htmlOptions'=>array(
                'class'=>'form-control form-group',
                'placeholder'=>'开始日期',
            ),
        ));
        ?>
        <?php
        $this->widget('zii.widgets.jui.CJuiDatePicker',array(
            'name'=>'enddate',
            'id'=>'enddate',
            'options'=>array(
                'dateFormat'=>'yy-mm-dd',
            ),
            'htmlOptions'=>array(
                'class'=>'form-control form-group',
                'placeholder'=>'结束日期',
            ),
        ));
        ?>
        <button class="btn btn-primary J_ajax_submit_btn">查询</button>
    </form>
    <div class="row hide" id="viewData">
        <div class="col-md-12">
            <table class="table table-bordered table-hover">
                <thead>
                <tr>
                    <th style="width: 10%;">学生ID</th>
                    <th style="width: 10%;">学生</th>
                    <th style="width: 5%;">生日</th>
                    <th style="width: 5%;">入学时间</th>
                    <th style="width: 10%;">入学班级/年级</th>
                    <th style="width: 10%;">账单班级</th>
                    <th style="width: 5%;">金额</th>
                    <th style="width: 10%;">支付类型</th>
                    <th style="width: 10%;">付款时间</th>
                    <th style="width: 5%;">费用类型</th>
                </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
            <div>
                <form action="<?php echo $this->createUrl('expotData')?>" method="post" >
                    <input type="hidden" name="data" id="expotData">
                    <input type="hidden" name="startdate" id="start">
                    <input type="hidden" name="enddate" id="end">
                    <input type="submit" class="btn btn-primary" value="导出">
                </form>
            </div>
        </div>
    </div>
</div>

<?php $this->renderPartial('//layouts/common/branchSelectBottom');?>

<script type="text/template" id="feerate-template">
    <tr>
        <td><%= childid%></td>
        <td><%= childname%></td>
        <td class="text-right"><%= childbirthday%></td>
        <td class="text-right"><%= classname%></td>
        <td><%= dataname%></td>
        <td class="text-right"><%= invoiceClass%></td>
        <td class="text-right"><%= amount%></td>
        <td><%= transactiontype %></td>
        <td class="text-right"><%= paytime%></td>
        <td><%= invoicetype%></td>
    </tr>
</script>

<script>
    function callback(data)
    {
        var html = '';
        $('#viewData table tbody').html(html);
        var feerateTemplate = $('#feerate-template').html();
        for(var datum in data){
            html += _.template(feerateTemplate, data[datum]);
        }
        
        $("#expotData").val(JSON.stringify(data));
        $("#start").val($("#startdate").val());
        $("#end").val($("#enddate").val());
        $('#viewData table tbody').html(html);
        $('#viewData').removeClass('hide');
    }
</script>