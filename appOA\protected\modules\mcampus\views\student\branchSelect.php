<div class="container-fluid">

    <p class="text-center p20">
        <a style="" id="branch-selector" class="btn btn-primary">
            <?php echo Yii::t('campus','Select a Campus');?>
        </a>
    </p>
    <div id="branch-list" class="hidden">
        <ul class="nav nav-pills">
            <?php
            $allbranches = $this->getAllBranch();

            foreach($this->accessBranch as $_branch):
                if(isset($allbranches[$_branch])):
                    if(!$this->branchSelectParams['hideOffice'] || $allbranches[$_branch]['type'] != 10):
                        $text = '<span class="glyphicon glyphicon-tags program'.$allbranches[$_branch]['group'].'"></span> '.$allbranches[$_branch]['title'];
                        ?>
                        <li class="branchItem">
                            <?php
                            if(isset($this->branchSelectParams['branchCount']) && $this->branchSelectParams['branchCount'][$_branch] && in_array($_branch, array_keys($this->branchSelectParams['branchCount']))){
                                $text .= ' <span class="badge">'.$this->branchSelectParams['branchCount'][$_branch].'</span>';
                            }
                            ?>
                            <?php echo CHtml::link($text, array_merge($this->branchSelectParams['urlArray'], array($this->branchSelectParams['keyId']=>$_branch)));?>
                        </li>
                    <?php
                    endif;
                endif;
            endforeach;
            ?>
        </ul>
    </div>
    <div class="searchbox">
        <?php $this->widget('ext.search.ChildSearchHandy', array(
            'inputCSS' => 'form-control input-lg',
            'displayLabel'=>'',
            'popPosition' => array('collision'=>"none"),
        )) ?>
    </div>

</div>
<style>
    .popover{max-width: 45%}
    .branchItem{height: 37px;width: 170px;}
    .searchbox{margin-top: 300px;}
    .searchbox input{margin: 0 auto;width: 750px;}
</style>
<script>
    $(function() {
        var _opts = {placement:'bottom',html:'true',content:$('#branch-list').html()};
        $('a#branch-selector').popover(_opts);
        $('a#branch-selector').popover('show');
    });
</script>