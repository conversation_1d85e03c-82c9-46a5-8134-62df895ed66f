/*********************/
/** jRating CSS **/
/*********************/

/**Div containing the color of the stars */
.jRatingAverage {
	background-color:#f62929;
	position:relative;
	top:0;
	left:0;
	z-index:2;
	height:100%;
}
.jRatingColor {
	background-color:#f4c239; /* bgcolor of the stars*/
	position:relative;
	top:0;
	left:0;
	z-index:2;
	height:100%;
}

/** Div containing the stars **/
.jStar {
	position:relative;
	left:0;
	z-index:3;
}

/** P containing the rate informations **/
p.jRatingInfos {
	position:		absolute;
	z-index:9999;
	background:	transparent url('../images/icons/bg_jRatingInfos.png') no-repeat;
	color:			#FFF;
	display:		none;
	width:			91px;
	height:			29px;	
	font-size:16px;
	text-align:center;
	padding-top:5px;
}
	p.jRatingInfos span.maxRate {
		color:#c9c9c9;
		font-size:14px;
	}