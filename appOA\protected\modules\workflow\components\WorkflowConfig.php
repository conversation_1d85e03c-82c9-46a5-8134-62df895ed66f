<?php

/* 
 * 工作流配置文件
 */
class WorkflowConfig{
    const WORKFLOW_TYPE_REFUND = 3;
    const WORKFLOW_TYPE_DISCOUNT = 5;
    const WORKFLOW_TYPE_CHANGE = 2;
    const WORKFLOW_TYPE_CASH = 6;
    const WORKFLOW_TYPE_PAYMENT = 7;
    const WORKFLOW_TYPE_CONTRACT = 8;
    static private $childsInfo;
    public static function getType($typeId,$id=0){
        if (empty($typeId)){
            return '';
        }
        $ret = array(
            self::WORKFLOW_TYPE_REFUND =>array(
                'title' => '退费审批流程',
                'function' => self::getChildName($id),
                'href' => Yii::app()->controller->createUrl('/child/invoice/finance',array('t'=>'invoice','childid'=>$id)),
            ),
            self::WORKFLOW_TYPE_DISCOUNT =>array(
                'title' => '绑定折扣审批流程',
                'function' => '',
                'href'=>''
            ),
            self::WORKFLOW_TYPE_CHANGE => array(
                'title' => '帐单更改审批流程',
                'function' => self::getChildName($id),
                'href' => Yii::app()->controller->createUrl('/child/invoice/finance',array('t'=>'invoice','childid'=>$id)),
            ),
            self::WORKFLOW_TYPE_CASH => array(
                'title' => '提现工作流',
                'function' => self::getChildName($id),
                'href' => Yii::app()->controller->createUrl('/child/invoice/finance',array('t'=>'balance','childid'=>$id)),
            )
        );
        return isset($ret[$typeId]) ? $ret[$typeId] : '';
    }
    
    /**
     * 获取孩子名字
     */
    public static function getChildName($id=0){
        if (!$id){
            return '';
        }
        if (!isset(self::$childsInfo[$id])){
            self::$childsInfo[$id] = ChildProfileBasic::model()->findByPk($id);
        }
        if (!self::$childsInfo[$id]) {
            return '';
        }
        return self::$childsInfo[$id]->getChildName();
    }
}
