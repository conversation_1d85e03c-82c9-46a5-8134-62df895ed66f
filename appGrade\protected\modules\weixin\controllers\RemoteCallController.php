<?php

class RemoteCallController extends Controller
{
    private $AppId = 'wxb1a42b81111e29f3';
    private $AppSecret = '7fec437b1b5cbbb64b32e170e6857a7f';

	private $securityKey = 'A23sW343eL8934ov2332E';
	private $remoteToken = false;
	private $cacheIdToken = '';
	private $cacheIdGroups = '';
	
	public $remoteGroups = false;
	
	public function init(){
		$this->cacheIdToken = md5('weixin-token-ds');
		$this->cacheIdGroups = md5('weixin-groups-ds');
	}
	
	public function beforeAction($action){
		if(Yii::app()->request->isPostRequest && isset($_POST['postTime']) && isset($_POST['command'])){
			$myCode = md5(sprintf("%s&%s&%s", $_POST['postTime'], $_POST['command'], $this->securityKey));
			$time = time();
			if($myCode == $_POST['postKey'] && abs($time - $_POST['postTime'] ) < 300 ){
				
				$this->getAccessToken();
				if(!$this->remoteToken){
					return false;
				}
			
				return true;
			}
		}
		return false;
	}
	
	
	//同一时间只能有一个token，如果测试系统生成了一个token，将使得正式系统里已经缓存过的token过期，通过下面的函数，测试系统不再单独生成token，只是去IvyOnline上取
    public function getAccessToken()
    {
		//Yii::app()->cache->delete($this->cacheIdToken);
		$this->remoteToken = Yii::app()->cache->get($this->cacheIdToken);
		
		if(Mims::isProduction()){
			if($this->remoteToken === false){
				$ch = curl_init();
				$data = array(
					"grant_type" => "client_credential",
					"appid" => $this->AppId,
					"secret" => $this->AppSecret
					);
				curl_setopt($ch, CURLOPT_URL,"https://api.weixin.qq.com/cgi-bin/token");
				curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
				curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
				curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
				$return = curl_exec($ch);
				curl_close($ch);
	
				if($return){
					$json = CJSON::decode($return);
					if(isset($json['access_token']) && isset($json['expires_in'])){
						$expire = $json['expires_in'] - 200;
						//Yii::app()->cache->set($this->cacheIdToken, $json['access_token'], $expire);
						Yii::app()->cache->set($this->cacheIdToken, $json['access_token'], 30);
						$this->remoteToken = $json['access_token'];
					}
				}
			}
		}else{
			if($this->remoteToken === false){
				$ch = curl_init();
				$time = time();
				$command = 'doToken';
				$data = array(
					"postTime" => $time,
					"command" => $command,
					"postKey" => md5(sprintf("%s&%s&%s", $time, $command, $this->securityKey))
					);
				curl_setopt($ch, CURLOPT_URL,"http://www.ivyonline.cn/weixin/remoteCall/getWCToken");
				curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
				curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
				curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
				$return = curl_exec($ch);
				curl_close($ch);
	
				if($return){
					$json = CJSON::decode($return);
					if(isset($json['state']) && isset($json['token'])){
						$expire = 60;
						Yii::app()->cache->set($this->cacheIdToken, $json['token'], $expire);
						$this->remoteToken = $json['token'];
					}
				}
			}
		}
    }
	
	public function actionGetWCToken(){
		$result = array(
			'token' => $this->remoteToken,
			'state' => 'success'
		);
		echo CJSON::encode($result);
	}
	
	public function getRemoteGroups(){
		
		$this->remoteGroups = Yii::app()->cache->get($this->cacheIdGroups);
		if($this->remoteGroups === false){
			$ch = curl_init($url) ;
			curl_setopt($ch, CURLOPT_URL,"https://api.weixin.qq.com/cgi-bin/groups/get?access_token=" . $this->remoteToken); 
			//curl_setopt($ch, CURLOPT_POST, 1);
			//curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
			//curl_setopt($ch, CURLOPT_POSTFIELDS,$menuStr);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
			curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
			
			$result = curl_exec($ch);
			curl_close($ch);
			
			if($result){
				$json = CJSON::decode($result);
				if(!isset($json['errcode'])){
					foreach($json['groups'] as $group){
						if($group['id']>=100){
							$this->remoteGroups[$group['name']] = $group['id'];
						}
					}
					Yii::app()->cache->set($this->cacheIdGroups, $this->remoteGroups, 72000);
				}
			}
		}
		
		return $result;
	}
	
	public function actionDoGroup(){
		if($_POST['command'] == 'doGroup'){
			$groups = $this->getRemoteGroups();
			$params = unserialize(base64_decode($_POST['params']));
			if( isset( $params['groupKey'] ) && isset($this->remoteGroups[$params['groupKey']]) ){
				$ch = curl_init();
				$data = CJSON::encode(
					array(
						'openid' => $params['openid'],
						'to_groupid' => $this->remoteGroups[$params['groupKey']],
					)
				);
				curl_setopt($ch, CURLOPT_URL,"https://api.weixin.qq.com/cgi-bin/groups/members/update?access_token=" . $this->remoteToken); 
				curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
				curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
				//curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
				$return = curl_exec($ch);
				curl_close($ch);
			}
		}
	}
	
	public function actionDoQrCode(){
		if($_POST['command'] == 'doQrCode'){

			$ch = curl_init();
			$data = array(
				"expire_seconds" => 600,
				"action_name" => "QR_SCENE",
				"action_info" => array(
					'scene' => array(
						'scene_id' => $_POST['scene_id']
					)
			    )
			);
			curl_setopt($ch, CURLOPT_URL,"https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token=" . $this->remoteToken);
			curl_setopt($ch, CURLOPT_POSTFIELDS, CJSON::encode($data));
			curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

			$result = curl_exec($ch);
			echo $result;
			curl_close($ch);

		}
	}
	
	public function actionDoUserInfo(){
		if($_POST['command'] == 'doUserInfo'){
			$ch = curl_init();
 			curl_setopt($ch, CURLOPT_URL, 'https://api.weixin.qq.com/cgi-bin/user/info?access_token='.$this->remoteToken.'&openid='.$_POST['openid']);
			curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

			$result = curl_exec($ch);
			echo $result;
			curl_close($ch);
		}
	}
	
	public function actionDoCustomSendText(){
		$data = unserialize(base64_decode($_POST['data']));
		if($_POST['command'] == 'doCustomSendText' && isset($data['touser']) && isset($data['content']) ){
			$ch = curl_init();
			$postData = array(
				"touser" => $data['touser'],
				"msgtype" => "text",
				"text" => array(
					'content' => $data['content']
			    )
			);
			
			curl_setopt($ch, CURLOPT_URL,"https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token=" . $this->remoteToken);
			curl_setopt($ch, CURLOPT_POSTFIELDS, CJSON::encode($postData));
			curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

			$result = curl_exec($ch);
			echo $result;
			curl_close($ch);		
		}
	}
}
