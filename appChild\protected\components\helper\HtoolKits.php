<?php

/**
 * 通用小工具
 * <AUTHOR>
 *
 */
class HtoolKits {

    /**
     * 根据语言返回对应的字段值 中英文
     * @param string $cn_value
     * @param string $en_value
     * @param boolean $assign_lang 指定语言--- false 不指定语言环境是什么就是什么  true 中英文 cn 中文 en 英文
     * <AUTHOR>
     * @time 2012-6-26
     */
    public static function getContentByLang($cn_value, $en_value, $assign_lang = false) {
        $lang_key = Yii::app()->language;
        $cn_value = trim($cn_value);
        $en_value = trim($en_value);
        if (strtolower($assign_lang) == "cn") {
            $lang_key = "zh_cn";
        } else if (strtolower($assign_lang) == "en") {
            $lang_key = "en_us";
        }

        if ($lang_key == "zh_cn") {
            if ($assign_lang === true) {
                if (!empty($cn_value) && !empty($en_value)) {
                    return $cn_value . " (" . $en_value . ")";
                }
            }
            if (empty($cn_value))
                return $en_value;
            else
                return $cn_value;
        }else {
            if ($assign_lang === true) {
                if (!empty($cn_value) && !empty($en_value)) {
                    return $en_value . "（" . $cn_value . "）";
                }
            }
            if (empty($en_value))
                return $cn_value;
            else
                return $en_value;
        }
    }

    public static function autoLang($cn_value, $en_value, $assign_lang = false) {
        $lang_key = Yii::app()->language;
        if (strtolower($assign_lang) == "cn") {
            $lang_key = "zh_cn";
        } else if (strtolower($assign_lang) == "en") {
            $lang_key = "en_us";
        }

        if ($lang_key == "zh_cn") {
            if ($assign_lang === true) {
                if (!empty($cn_value) && !empty($en_value)) {
                    return $cn_value . " (" . $en_value . ")";
                }
            }
            if (empty($cn_value))
                return $en_value;
            else
                return $cn_value;
        }else {
            if ($assign_lang === true) {
                if (!empty($cn_value) && !empty($en_value)) {
                    return $en_value . "（" . $cn_value . "）";
                }
            }
            if (empty($en_value))
                return $cn_value;
            else
                return $en_value;
        }
    }

    /**
     * 一个一维数组 按照一个 一维数组来排序
     * @param array $arr1d 一维数组
     * @param array $order1d 一维数组
     * @return array
     * <AUTHOR>
     */
    public function orderSort($arr1d, $order1d) {

        if (!is_array($order1d)) {
            return $arr1d;
        }

        if (!is_array($arr1d)) {
            return null;
        }

        $sort_arr = null;
        if (count($order1d)) {
            foreach ($order1d as $s) {
                if (isset($arr1d[$s])) {
                    $sort_arr[$s] = $arr1d[$s];
                    unset($arr1d[$s]);
                }
            }
            if (count($arr1d)) {
                foreach ($arr1d as $k => $v) {
                    $sort_arr[$k] = $v;
                }
            }
        } else {
            return $arr1d;
        }
        return $sort_arr;
    }

    /**
     * 格式化数字
     * @param string $pattern
     * @param mixed $value
     * @param string $currency
     * @return Ambigous <string, mixed>
     * <AUTHOR>
     * @time 2012-7-24
     */
    public static function formatNumber(string $pattern, mixed $value, string $currency = null) {
        $nf = new CNumberFormatter(Yii::app()->language);
        return $nf->format($pattern, $value, $currency);
    }

    /**
     * 递归创建目录
     * @param type $path
     * <AUTHOR>
     */
    public static function createDir($path) {
        if (!file_exists($path)) {
            self::createDir(dirname($path));
            mkdir($path, 0777);
        }
    }

    /**
     * 计算周一
     * @param type $dIn
     * @return type
     * <AUTHOR> Chen <<EMAIL>>
     */
    public static function getMonday($dIn) {
        if ($dIn == 0) {
            $getMonday = date("d") + (-6);
        } else {
            $getMonday = date("d") + (($dIn - 1) * -1);
        }
        return $getMonday;
    }
    public static function getIpAddress() {
        foreach (array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 'HTTP_X_CLUSTER_CLIENT_IP', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 'REMOTE_ADDR') as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4 | FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
    }

}

?>