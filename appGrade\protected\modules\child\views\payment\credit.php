<?php
$this->breadcrumbs=array(
	Yii::t("navigations","Payment")=>array('/child/payment/summary'),
	Yii::t("navigations","General Credit"),
);?>

<h1><label><?php echo Yii::t("payment","General Credit History"); ?></label></h1>
<script type="text/javascript">
function creditType(id)
{
	window.location = "<?php echo Yii::app()->getController()->createUrl('//child/payment/credit')?>?type="+id;
}
</script>
<?php
	$typeSelect = array(
		"none" => Yii::t('payment', 'All'),
		"in" => Yii::t('payment', 'All Refund'),
		"out" => Yii::t('payment', 'All Expense'),
		"lunchrefund" => Yii::t('payment', 'Lunch Refund Only'),
	);
	//参数 filter = none, in, out, lunchrefund
	echo CHtml::dropDownList("type", $type, $typeSelect,array('onchange'=>'creditType(this.options[this.options.selectedIndex].value);'));
?>

<?php
		$this->widget('zii.widgets.grid.CGridView', array(
			'cssFile'=>Yii::app()->theme->baseUrl.'/widgets/gridview/styles.css',
			'dataProvider'=> $creditList,
			'enableSorting'=>false,
			'pager'=>array(
				'class'=>'CLinkPager',
				'cssFile'=>Yii::app()->theme->baseUrl.'/css/pager.css',
			),
			'columns'=>array(
				array(
					'headerHtmlOptions'=>array('class'=>'icon-24'),
					'htmlOptions' => array('class'=>'icon-column'),
					'name' => 'inout',
					'header' => '',
					'type'=>'raw',
					'value' => '$data->renderInout()'
				),
				array(
        			'name' =>'itemname',
					'value'  => 'Invoice::feeType($data->itemname)',
				),
				array(
        			'name'   =>'amount',
					'value' => 'Invoice::formatAmount($data->inout,$data->amount)'
				),
				array(
        			'name'=>'updated_timestamp',
					'value' => 'Mims::formatDateTime($data->updated_timestamp,"medium","short")'
				),
			),
			'ajaxUpdate'=>false,
			'template'=>"{summary}{pager}{items}{summary}{pager}"
		));
?>
