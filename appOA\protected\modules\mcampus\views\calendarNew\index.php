<div class="container-fluid" id='container' v-cloak @click="qrcodeBox=false">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('//mcampus/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Basic'), array('//mcampus/default/index'))?></li>
        <li class="active"><?php echo Yii::t('site','School Calendar');?></li>
    </ol>
    <div class="row" v-if='calendarData.calendar_yearly_data'>
        <div class="col-md-12 col-sm-12 mb20">
            <div class='bgGrey'>
                <button type="button" class="btn  mr10" :class='currentCalendar==index?"btn-primary":"btn-default"' v-for='(list,index) in initData.yearly_list' @click='showCalendar(index,list.yid)'>{{list.year_title}}</button>
            </div>
        </div>
        <div class="col-md-12 col-sm-12">
            <div class="alert alert-warning" role="alert" v-if='(currentCalendar==null || currentCalendar=="") && schoolIndex!=null'>请选择校历查看相应信息</div>
            <div v-else-if='!isNewCalendarDay'>
                <div class='calendarDetails' v-if='calendarData.calendar_yearly_data'>
                    <div class='flex'>
                        <div class='flex1 font18'>{{calendarData.calendar_yearly_data.title}}</div>
                        <div>
                            <span class='relative cur-p' @click.stop='showQrcode'>
                                <span class='glyphicon glyphicon-qrcode'></span>
                                <span class='ml5 text-primary'><?php echo Yii::t('teaching','Preview');?></span>
                                <div class='qrcodeBox' v-show='qrcodeBox'>
                                    <div  class='mt16 color3 fontBold'>{{calendarData.calendar_yearly_data.school_title}} {{year_title}}</div>
                                    <div id='qrcodeBox' class='mt12'></div>
                                    <div class='mt12'><?php echo Yii::t('calendar','Wechat Preview');?></div>
                                    <!-- <div>查看移动端效果</div> -->
                                </div> 
                            </span>
                            <button type="button" class="btn btn-default mr10 ml20" :disabled='exportBtn' @click='exportCsv'>
                                <span class="glyphicon glyphicon-log-out" aria-hidden="true"></span>  <?php echo Yii::t('user','Export');?>
                            </button>
                            <button type="button" class="btn btn-primary mr10 " @click='uploadPDF'><?php echo Yii::t('calendar','PDF Version Upload');?></button>
                            <button type="button" class="btn btn-primary" @click='newCalendarDay'><?php echo Yii::t('site','Edit Calendar');?></button>
                        </div>
                    </div>
                    <div class='font14 color3 mt10'><?php echo Yii::t('labels','1st Semester');?>：{{calendarData.calendar_yearly_data.first_semester}}<span class='ml20'><?php echo Yii::t('labels','2nd Semester');?>：{{calendarData.calendar_yearly_data.second_semester}}</span></div>
                    <div class='flex alignCenter'>
                        <div class='calendarNum flex1 flex'>
                            <div class='flex1 text-center'>
                                <div class='color3 font18'>{{calendarData.calendar_yearly_data.all_total}}</div>
                                <div class='font14 color6 mt10'><?php echo Yii::t('global','Total School Days');?></div>
                            </div>
                            <div class='flex1 text-center'>
                                <div class='color3 font18'>{{calendarData.calendar_yearly_data.first_total}}</div>
                                <div class='font14 color6 mt10'><?php echo Yii::t('global','Fall Semester School Days');?></div>
                            </div>
                            <div class='flex1 text-center'>
                                <div class='color3 font18'>{{calendarData.calendar_yearly_data.second_total}}</div>
                                <div class='font14 color6 mt10'><?php echo Yii::t('global','Spring Semester School Days');?></div>
                            </div>
                        </div>
                        <div class='ml20 pl20'>
                            <span class='font14'><?php echo Yii::t('calendar','Open to parents');?></span>
                            <label class="switch mt10 ml10">
                                <input type="checkbox" :checked='calendarData.calendar_yearly_data.stat==10?true:false' @change='tabSimple()'>
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                </div>
                <div class='flex' v-if='calendarData.calendar_day_list_by_event'>
                    <div>
                        <div class='mb15'>
                            <v-date-picker :attributes='attrs' name="calendar" :available-dates='availableDate' :min-date='minData' :max-date='maxData'   @dayclick="showHoliday" v-model='clickDays' @transition-end='PrevMonth' ref='calendar' class='calendar'/>
                        </div>
                        <div class='text-center color3 font14 mb10'><?php echo Yii::t('global','School Days');?>：{{teachingDays}}<?php echo Yii::t('site','Days');?></div>
                        <div>
                            <span class='mr15'><span class='point holidayColor'></span> <?php echo Yii::t('site','Holiday');?></span>
                            <span class='mr15'><span class='point eventColor'></span> <?php echo Yii::t('site','Event');?></span>
                            <span><span class='point weekColor'></span> <?php echo Yii::t('site','Schooldays Make-up');?></span>
                        </div>
                        <div class='mt5'>
                            <span class='mr15'><span class='point fullDay'></span> <?php echo Yii::t('site','Full Day');?></span>
                            <span class='mr15'><span class='halfDay'></span>  <?php echo Yii::t('site','Half Day');?></span>
                        </div>
                        <div class='daysDetails mt20' v-if='clickDays!=null' :style='currentDaysList.length==0?"background: #F7F7F8;":"background: rgba(77, 136, 210, 0.10);"'>
                            <div class='font16 mb20'>
                                <!-- <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/calendar.png' ?>" alt="" style='width:28px' v-if='currentDaysList.length!=0'>
                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/noCalendar.png' ?>" alt="" style='width:28px' v-else> -->
                                <span class='fontBold' :style='currentDaysList.length==0?"color: #333;":"color: #4D88D2;"'>{{currentDay}}</span> 
                            </div>
                            <div v-if='currentDaysList.length!=0'>
                                <div v-for='(list,index) in currentDaysList'>
                                    <div class='flex font14 mb10'>
                                        <div class='mr10'><span v-if='list.event==10' class='eventType' style='background:#F0AD4E'>H</span> <span v-if='list.event==20' class='eventType' style='background:#55C1EE'>E</span> <span v-if='list.event==30'class='eventType'  style='background:#D9534F'>W</span></div>
                                        <div>
                                            <div class='color3 font14'>{{list.title}} </div>
                                            <div class='flex align-items font12'>
                                                <span class='dayType mt5' v-if='list.day_type==20'><?php echo Yii::t('site','Half Day');?></span>
                                                <span class='color6 font12 mt5  flex1' v-if="list.time_str[0]!='' && list.time_str[0]!=null">{{list.time_str[0]}} — {{list.time_str[1]}}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else class='color3 font14'>
                                无日程安排
                            </div>
                        </div>
                    </div>
                    <div class='flex1 ml20' >
                        <div class="flex">
                            <div class='flex1'>
                                <div class="panel panel-default">
                                    <div class="panel-heading"><span><?php echo Yii::t('site','Holiday List');?></span><span class='color6 font12'>（<?php echo Yii::t('site','Please contact IT-Dev for holiday changes');?>）</span></div>
                                    <div class="panel-body">
                                        <template v-if='calendarData.calendar_day_list_by_event[10] && calendarData.calendar_day_list_by_event[10].length!=0'>
                                            <div class='flex mb15 alignCenter' v-for='(item,index) in calendarData.calendar_day_list_by_event[10]'>
                                                <div class='text-center mr10 time font14'>
                                                    <div v-if='item.days.length!=1'>
                                                    <span class='color3'>{{item.start}}</span><br>
                                                    <span class='colorC mt5'>|</span><br>
                                                    <span class='color3'>{{item.end}}</span>
                                                    </div>
                                                    <span class='color3' v-else>{{item.start}}</span>
                                                </div>
                                                <div class='flex1 holidayDetails holidayColor'  :style='item.memo=="" && (item.time_str[0]=="" || item.time_str[0]==null)?"padding:22px 16px;":"padding:8px 16px"'>
                                                    <div class='color3 font14'>{{item.title}}</div>
                                                    <div class='color6 font12 mt5' v-if='item.memo!=""'><?php echo Yii::t('principal','Description');?>：{{item.memo}}</div>
                                                    <div class='flex align-items '>
                                                        <span class='dayType mt5' v-if='item.day_type==20'> <?php echo Yii::t('site','Half Day');?></span>
                                                        <span class='color6 font12 mt5  flex1' v-if="item.time_str[0]!='' && item.time_str[0]!=null">{{item.time_str[0]}} — {{item.time_str[1]}}</span>
                                                    </div>
                                                    <span v-if='item.privacy==1' class='privacy'><?php echo Yii::t('labels', 'Staff Only');?></span>
                                                </div>
                                            </div>
                                        </template>
                                        <div v-else class='color3 font14 color9'>
                                            无日程安排
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div  class='flex1 m15'>
                                <div class="panel panel-default">
                                    <div class="panel-heading flex">
                                        <span class='flex1'><?php echo Yii::t('site','Event List');?></span>
                                        <div style='height:16px;line-height:16px'>
                                            <span type="button" class="btn btn-link p0 mr16" @click='newCalendarDay' >
                                               <span class='glyphicon glyphicon-plus mr4'></span><?php echo Yii::t('site','New Event');?>
                                            </span>
                                            <div class="btn-group">
                                                <button type="button" class="btn btn btn-link p0 dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    高级 <span class="caret"></span>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <el-tooltip class="item" effect="dark" content="将清空“事件”所有自定义数据" placement="top">
                                                        <a href="javascript:;"  @click='clearEvent("modal")'>
                                                               <?php echo Yii::t('directMessage','Clear All');?>
                                                            </a>
                                                        </el-tooltip>
                                                    </li>
                                                    <li>
                                                        <a href="javascript:;"  @click='showTip'>
                                                            <span class="glyphicon glyphicon-log-in" aria-hidden="true"></span>  <?php echo Yii::t('teaching','Import');?>
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                            <!-- <el-tooltip class="item" effect="dark" content="将清空“事件”所有自定义数据" placement="top">
                                                <button type="button" class="btn btn-link p0 mr16" @click='clearEvent("modal")'><?php echo Yii::t('directMessage','Clear All');?></button>
                                            </el-tooltip> -->
                                            <!-- <input ref="img-upload-input" class="img-upload-input hide" type="file" accept=".xlsx" @change="submitUpload">
                                            <button type="button" class="btn btn-link p0" @click='handleSelectedImg' id='filesPhoto'>
                                                <span class="glyphicon glyphicon-log-in" aria-hidden="true"></span>  <?php echo Yii::t('teaching','Import');?>
                                            </button> -->

                                            <!-- <button type="button" class="btn btn-link p0" @click='showTip' >
                                                <span class="glyphicon glyphicon-log-in" aria-hidden="true"></span>  <?php echo Yii::t('teaching','Import');?>
                                            </button> -->
                                        </div>
                                    </div>
                                    <div class="panel-body">
                                        <div class='mb15' v-if='calendarData.day_event_type_list.length!=0'>
                                            <el-select v-model="filter_event_type" size='small' clearable collapse-tags  multiple placeholder="筛选事件类型" @change='filterType()' class='multipleSelect'>
                                                <el-option
                                                v-for="(list,key,index) in calendarData.day_event_type_list"
                                                :key="index"
                                                :label="list.name"
                                                :value="list.type">
                                                </el-option>
                                            </el-select>
                                        </div>
                                        <template v-if='calendarData.calendar_day_list_by_event[20] && calendarData.calendar_day_list_by_event[20].length!=0'>
                                            <div class='flex mb15 alignCenter' v-for='(item,index) in calendarData.calendar_day_list_by_event[20]'>
                                                <div class='text-center mr10 time font14'>
                                                    <div v-if='item.days.length!=1'>
                                                    <span class='color3'>{{item.start}}</span><br>
                                                    <span class='colorC mt5'>|</span><br>
                                                    <span class='color3'>{{item.end}}</span>
                                                    </div>
                                                    <span class='color3' v-else>{{item.start}}</span>
                                                </div>
                                                <div class='flex1 flex align-items holidayDetails eventColor' :style='item.memo=="" && (item.time_str[0]=="" || item.time_str[0]==null)?"padding:22px 16px;":"padding:8px 16px"'>
                                                    <div class='flex1'>
                                                        <div class='color3 font14'>{{item.title}}</div>
                                                        <div class='color6 font12 mt5' v-if='item.memo!=""'><?php echo Yii::t('principal','Description');?>：{{item.memo}}</div>
                                                        <div class='flex align-items'>
                                                            <span class='dayType mt5' v-if='item.day_type==20'><?php echo Yii::t('site','Half Day');?></span>
                                                            <span class='color6 font12  mt5 flex1' v-if="item.time_str[0]!='' && item.time_str[0]!=null">{{item.time_str[0]}} — {{item.time_str[1]}}</span>
                                                        </div>
                                                    </div>
                                                    <el-dropdown v-if='item.operation_auth==1'>
                                                        <span class="el-dropdown-link">
                                                            <span class='el-icon-more cur-p eventEdit'></span>
                                                        </span>
                                                        <el-dropdown-menu slot="dropdown">
                                                            <el-dropdown-item @click.native='editHoliday(item,index,"20")'><?php echo Yii::t('global','Edit');?></el-dropdown-item>
                                                            <el-dropdown-item @click.native='delHoliday(item)'><?php echo Yii::t('global','Delete');?></el-dropdown-item>
                                                        </el-dropdown-menu>
                                                    </el-dropdown>
                                                    <span v-if='item.privacy==1' class='privacy'><?php echo Yii::t('labels', 'Staff Only');?></span>
                                                </div>
                                                <!-- <div v-if='item.operation_auth==1'>
                                                    
                                                    <div>
                                                        <button type="button" class="btn btn-link" @click='editHoliday(item,index,"20")'><?php echo Yii::t('global','Edit');?></button>
                                                    </div>
                                                    <button type="button" class="btn btn-link"  @click='delHoliday(item)'><?php echo Yii::t('global','Delete');?></button>
                                                </div> -->
                                            </div>
                                        </template>
                                        <div v-else class='color3 font14 color9'>
                                            无日程安排
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div  class='flex1'>
                            <div class="panel panel-default">
                                    <div class="panel-heading"><?php echo Yii::t('site','Schooldays Make-up');?></div>
                                    <div class="panel-body">
                                        <template v-if='calendarData.calendar_day_list_by_event[30] && calendarData.calendar_day_list_by_event[30].length!=0'>
                                            <div class='flex mb15 alignCenter' v-for='(item,index) in calendarData.calendar_day_list_by_event[30]'>
                                                <div class='text-center mr10 time font14'>
                                                    <div v-if='item.days.length!=1'>
                                                    <span class='color3'>{{item.start}}</span><br>
                                                    <span class='colorC mt5'>|</span><br>
                                                    <span class='color3'>{{item.end}}</span>
                                                    </div>
                                                    <span class='color3' v-else>{{item.start}}</span>
                                                </div>
                                                <div class='flex1 holidayDetails weekColor' :style='item.memo=="" && (item.time_str[0]=="" || item.time_str[0]==null)?"padding:22px 16px;":"padding:8px 16px"'>
                                                    <div class='color3 font14'>{{item.title}}</div>
                                                    <div class='color6 font12 mt5' v-if='item.memo!=""'><?php echo Yii::t('principal','Description');?>：{{item.memo}}</div>
                                                    <div class='flex align-items'>
                                                        <span class='dayType mt5' v-if='item.day_type==20'><?php echo Yii::t('site','Half Day');?></span>
                                                        <span class='color6 font12  mt5 flex1' v-if="item.time_str[0]!='' && item.time_str[0]!=null">{{item.time_str[0]}} — {{item.time_str[1]}}</span>
                                                    </div>
                                                    <span v-if='item.privacy==1' class='privacy'><?php echo Yii::t('labels', 'Staff Only');?></span>
                                                </div>
                                            </div>
                                        </template>
                                        <div v-else class='color3 font14 color9'>
                                            无日程安排
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div v-else-if='isNewCalendarDay'>
                <p class='font14'><a href="javascript:;" @click='isNewCalendarDay=false'><span class="glyphicon glyphicon-chevron-left font12"></span><?php echo Yii::t('site','School Calendar Management');?></a> | <span><?php echo Yii::t('site','Edit Calendar');?></span> </p>
                <div class='text-center mb20'>
                     <v-date-picker  :columns="$screens({ default: 1, lg: 3 })" :attributes='addAttrs' :available-dates='availableDate' @dayclick="pickDate"  v-model='selectedDate' :min-date='minData' :max-date='maxData' class='calendar'  is-range></v-date-picker>
                    <div class='mt20'>
                        <span class='mr15'><span class='point holidayColor'></span> <?php echo Yii::t('asa','Holiday');?></span>
                        <span class='mr15'><span class='point eventColor'></span> <?php echo Yii::t('asa','Event');?></span>
                        <span class='mr15'><span class='point weekColor'></span> <?php echo Yii::t('site','Schooldays Make-up');?></span>
                        <span><span class='point currentColor'></span> 当前已选日期</span>
                    </div>
                </div>
                <!-- <div class='mb20 mt20'>
                    <span>选择日期：</span>
                    <input type="text" class="form-control select_3" id="addStart" v-model='addStart'  placeholder="请选择开始时间">
                    -
                    <input type="text" class="form-control select_3" id="addEnd" placeholder="请选择结束时间" v-model='addEnd'>
                    <button type="button" class="btn btn-primary" @click='addTime'>保存</button>
                </div> -->
                <div class='col-md-3 col-sm-3 p0 font14'>
                    <div class="panel panel-default">
                        <div class="panel-heading">已选日期</div>
                        <div class="panel-body">
                            <div v-for='(list,index) in addTimeList' class='mb10 timeData flex'>
                                <div class='flex1'>
                                    <div class='mb8'><span class='font14 color3'>{{list.date}}</span></div>
                                    <el-time-picker
                                    class='time-picker'
                                        is-range
                                        size="small"
                                        v-model="list.time"
                                        range-separator="至"
                                        placement="bottom-start"
                                        value-format="HH:mm"
                                        format="HH:mm"
                                        start-placeholder="开始时间"
                                        start-placeholder="<?php echo Yii::t("campus", "Start Date"); ?>"
                                        end-placeholder="<?php echo Yii::t("campus", "End Date"); ?>">>
                                    </el-time-picker>
                                </div>
                                <span class='el-icon-error cur-p  timeClose' @click='closeTime(index)'></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class='col-md-9 col-sm-9 font14'>
                    <div class="panel panel-default">
                        <div class="panel-heading">编辑日程内容</div>
                        <div class="panel-body">
                            <div class='col-md-6 col-sm-6 '>
                                <div class="form-group">
                                    <label class="labelCss"><?php echo Yii::t('labels','Calendar Day Type');?></label>
                                    <select class="form-control" v-model='addTimeData.event'>
                                        <option value=''><?php echo Yii::t('global','Please Select');?></option>
                                        <option value='20'><?php echo Yii::t('site','Event');?></option>
                                    </select>
                                </div>
                            </div>
                            <div class='col-md-6 col-sm-6 '>
                                <div class="form-group">
                                    <label class="labelCss"><?php echo Yii::t('labels','Calendar Time');?></label>
                                    <select class="form-control" v-model='addTimeData.day_type' >
                                        <option value=''><?php echo Yii::t('global','Please Select');?></option>
                                        <option value='10'><?php echo Yii::t('site','Full Day');?></option>
                                        <option value='20'><?php echo Yii::t('site','Half Day');?></option>
                                    </select>
                                </div>
                            </div>
                            <div class='col-md-12 col-sm-12 ' v-if='addTimeData.event=="20" && calendarData.day_event_type_list.length!=0'>
                                <div class='flex eventCheckType mb15 mt0'>
                                    <span class='color3 mr10'>事件类型：</span>
                                    <div class='flex1 row'>
                                        <span  v-for='(list,key,index) in calendarData.day_event_type_list' class='col-md-4'>
                                            <label class="radio-inline">
                                                <input type="radio"  :value="list.type" v-model='addTimeData.event_type'> {{list.name}}
                                            </label>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class='col-md-6 col-sm-6'>
                                <div class="form-group">
                                    <label class="labelCss">日程标题（中文）</label>
                                    <input type="text" class="form-control" v-model='addTimeData.title_cn' placeholder="日程标题（中文）">
                                </div>
                                <div class="form-group">
                                    <label class="labelCss">日程描述（中文）</label>
                                    <input type="text" class="form-control" v-model='addTimeData.memo_cn' placeholder="日程描述（中文）">
                                </div>
                                <div class="form-group"  v-if='currentBranchId=="BJ_DS"'>
                                    <label class="labelCss">学部类型</label>
                                    <!-- <div>                                    
                                        <label class="radio-inline" v-for='(list,index) in calendarData.dept_type'>
                                            <input type="radio"  :value="index" v-model='addTimeData.dept'> {{list}}
                                        </label>
                                    </div> -->
                                    <select class="form-control" v-model='addTimeData.dept' >
                                        <option :value='index' v-for='(list,index) in calendarData.dept_type'>{{list}}</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <div class="checkbox">
                                        <label>
                                        <input type="checkbox" value='1' v-model='addTimeData.privacy'> <?php echo Yii::t('newDS', 'Visible to Staff Only') ?>
                                        </label>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <button type="button"  class="btn btn-primary mr10" @click='saveNewTime()'><?php echo Yii::t("newDS", "保存"); ?></button>
                                    <!-- <button type="button" class="btn btn-default"><?php echo Yii::t('global', 'Cancel') ?></button> -->
                                </div>
                            </div>
                            <div class='col-md-6 col-sm-6 '>
                                <div class="form-group">
                                    <label class="labelCss">日程标题（英文）</label>
                                    <input type="text" class="form-control" v-model='addTimeData.title_en' placeholder="日程标题（英文）">
                                </div>
                                <div class="form-group">
                                    <label class="labelCss">日程描述（英文）</label>
                                    <input type="text" class="form-control" v-model='addTimeData.memo_en' placeholder="日程描述（英文）">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="editHoliday" tabindex="-1" role="dialog" >
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" ><?php echo Yii::t('attends', '新建校历模板') ?> <span id="remarks_t"></span></h4>
                </div>
                <div class="modal-body color6" >
                    <div class="form-horizontal">
                        <div class="form-group">
                            <label for="inputPassword3" class="col-sm-2 control-label">日期</label>
                            <div class="col-sm-10">
                                <input type="text" class="form-control select_3" id='start_date' v-model='editHolidayData.start_date'  placeholder="<?php echo Yii::t('global','Please Select');?>">
                                -
                                <input type="text" class="form-control select_3" id='end_date'  v-model='editHolidayData.end_date'    placeholder="<?php echo Yii::t('global','Please Select');?>" v-model='first_semester2'>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="inputPassword3" class="col-sm-2 control-label">起止时间</label>
                            <div class="col-sm-10">
                                <el-time-picker
                                    class='time-picker'
                                    is-range
                                    size="small"
                                    v-model="editHolidayData.time"
                                    range-separator="至"
                                    placement="bottom-start"
                                    value-format="HH:mm"
                                    format="HH:mm"
                                    start-placeholder="开始时间"
                                    start-placeholder="<?php echo Yii::t("campus", "Start Date"); ?>"
                                    end-placeholder="<?php echo Yii::t("campus", "End Date"); ?>">>
                                </el-time-picker>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="inputEmail3" class="col-sm-2 control-label"><?php echo Yii::t('labels','Calendar Day Type');?></label>
                            <div class="col-sm-10">
                                <select class="form-control" v-model='editHolidayData.event'>
                                    <option value=''><?php echo Yii::t('global','Please Select');?></option>
                                    <option value='20'><?php echo Yii::t('site','Event');?></option>
                                </select>
                                <div class='flex eventCheckType' v-if='editHolidayData.event==20 && calendarData.day_event_type_list && calendarData.day_event_type_list.length!=0'>
                                    <span class='color3 mr10 fontBold'>事件类型：</span>
                                    <div class='flex1 row'>
                                        <span  v-for='(list,key,index) in calendarData.day_event_type_list' class='col-md-6'>
                                            <label class="radio-inline">
                                                <input type="radio"  :value="list.type" v-model='editHolidayData.event_type'> {{list.name}}
                                            </label>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="inputEmail3" class="col-sm-2 control-label"><?php echo Yii::t('labels','Calendar Time');?></label>
                            <div class="col-sm-10">
                                <select class="form-control" v-model='editHolidayData.day_type' >
                                    <option value=''><?php echo Yii::t('global','Please Select');?></option>
                                    <option value='10'><?php echo Yii::t('site','Full Day');?></option>
                                    <option value='20'><?php echo Yii::t('site','Half Day');?></option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group" v-if='currentBranchId=="BJ_DS"'>
                            <label for="inputEmail3" class="col-sm-2 control-label"><?php echo Yii::t('labels','学部类型');?></label>
                            <div class="col-sm-10">
                                <div>                                    
                                    <label class="radio-inline" v-for='(list,index) in calendarData.dept_type'>
                                        <input type="radio"  :value="index" v-model='editHolidayData.dept'> {{list}}
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="inputPassword3" class="col-sm-2 control-label">日程标题（中文）</label>
                            <div class="col-sm-10">
                            <input type="text" class="form-control" v-model='editHolidayData.title_cn' placeholder="日程标题（中文）">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="inputPassword3" class="col-sm-2 control-label">日程标题（英文）</label>
                            <div class="col-sm-10">
                            <input type="text" class="form-control" v-model='editHolidayData.title_en' placeholder="日程标题（中文）">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="inputPassword3" class="col-sm-2 control-label">日程描述（中文）</label>
                            <div class="col-sm-10">
                            <input type="text" class="form-control" v-model='editHolidayData.memo_cn' placeholder="日程标题（中文）">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="inputPassword3" class="col-sm-2 control-label">日程描述（英文）</label>
                            <div class="col-sm-10">
                            <input type="text" class="form-control" v-model='editHolidayData.memo_en' placeholder="日程标题（中文）">
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="col-sm-offset-2 col-sm-10">
                                <div class="checkbox">
                                    <label>
                                    <input type="checkbox" v-model='editHolidayData.privacy' :checked='editHolidayData.privacy'><?php echo Yii::t('newDS', 'Visible to Staff Only') ?> 
                                    </label>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel') ?></button>
                    <button type="button"  class="btn btn-primary pull-right ml10" @click='saveCalendarDay()'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="delModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "Delete");?></h4>
            </div>
            <div class="modal-body">      
                <div><?php echo Yii::t("newDS", "Confirm to delete this item?");?></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                <button type="button" class="btn btn-primary" @click='delHoliday("hide")'><?php echo Yii::t("newDS", "Delete");?></button>
            </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="upload" tabindex="-1" role="dialog" >
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" ><?php echo Yii::t('attends', '上传PDF') ?> <span id="remarks_t"></span></h4>
                </div>
                <div class="modal-body color6" v-if='initUpload'>
                    <div class='mb20'>
                        <div><label>中文校历PDF</label> </div>
                        <p>
                            <button type="button" class="btn btn-primary"  id="PDF_cn" :disabled='disabledUploadCn'>
                            <span class="glyphicon glyphicon-upload" aria-hidden="true"></span> {{pdf_cn_url==''?'上传PDF':'重新上传'}}
                            </button>
                            <span class='ml5'>只能上传一个中文校历PDF</span> 
                        </p>
                        <div class='flex pdf mt5' v-if='pdf_cn_url!=""'>
                            <div class='flex1'>
                                <a :href="pdf_cn_url"  target='_blank'><span class='glyphicon glyphicon-paperclip mr5'></span> {{pdf_cn_name}} </a>
                            </div>
                            <span class='glyphicon glyphicon-trash'  @click='delPDF("cn")'></span>
                        </div>
                    </div>
                    <div><label>英文校历PDF</label>    </div>
                    <p>
                        <button type="button" class="btn btn-primary" id='PDF_en' :disabled='disabledUploadEn'>
                        <span class="glyphicon glyphicon-upload" aria-hidden="true"></span> {{pdf_en_url==''?'上传PDF':'重新上传'}}
                        </button>
                         <span class='ml5'>只能上传一个英文校历PDF</span> 
                    </p>
                    <div class='flex pdf mt5' v-if='pdf_en_url!=""'>
                        <div class='flex1'>
                            <a :href="pdf_en_url" target='_blank'><span class='glyphicon glyphicon-paperclip mr5'></span> {{pdf_en_name}} </a>
                        </div>
                        <span class='glyphicon glyphicon-trash' @click='delPDF("en")'></span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel') ?></button>
                <button type="button" class="btn btn-primary" @click='saveCalendarPDF()'><?php echo Yii::t("newDS", "保存");?></button>

                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="fileprogress" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" @click='unUploadFiles()'><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "导入");?></h4>
            </div>
            <div class="modal-body">  
                <p class='color3 font14'><label>将文档中“事件”数据导入到校历中</label> </p>  
                <!-- <input ref="img-upload-input" class="img-upload-input hide" type="file" accept=".xlsx" @change="submitUpload">
                <button type="button" class="btn btn-link p0" @click='handleSelectedImg' id='filesPhoto'>
                    <span class="glyphicon glyphicon-log-in" aria-hidden="true"></span>  <?php echo Yii::t('teaching','Import');?>
                </button>   -->
                <!-- <div class='color3 font14' v-if='Object.keys(files).length!=0'><span class='glyphicon glyphicon-paperclip text-primary mr8'></span> {{files[0].name}}</div> -->
                <input ref="img-upload-input" class="img-upload-input hide" type="file" accept=".xlsx" @change="submitUpload">
                <button type="button" class="btn btn-default mr10 " @click='handleSelectedImg' id='filesPhoto' :disabled='importBtn'>
                    <span class="glyphicon glyphicon-log-in" aria-hidden="true"></span> 
                    <span v-if='importBtn==false'><?php echo Yii::t('site','导入');?></span> 
                    <span v-else><?php echo Yii::t('site','正在导入中');?></span> 
                </button>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal" @click='unUploadFiles()'><?php echo Yii::t("global", "Close");?></button>
                <!-- <button type="button" class="btn btn-primary" @click='uploadFiles()'><?php echo Yii::t("global", "OK");?></button> -->
            </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="clearEventModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" @click='unUploadFiles()'><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "清空");?></h4>
            </div>
            <div class="modal-body">  
                <p class='color3 font14'><label>将清空“事件”所有自定义数据</label> </p>    
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                <button type="button" class="btn btn-primary" @click='clearEvent()'><?php echo Yii::t("global", "OK");?></button>
            </div>
            </div>
        </div>
    </div>
</div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    var currentBranchId = '<?php echo $this->branchId;?>';
    var container = new Vue({
        el: "#container",
        data: {
            currentBranchId:currentBranchId,
            isTab:false,
            addTab:false,
            calendarMenu:null,
            scoolMenu:null,
            addSchoolMenu:null,
            TemplateData:{
                "school_year": "",
                "title": "",
                "first_semester1": "",
                "first_semester2": "",
                "second_semester1": "",
                "second_semester2": "",
                "stat": ""
            },
            addTemplateData:{},
            currentYid:'',
            currentCalendar:null,
            selectedDate: {},
            attrs:[],
            addAttrs:[],
            initData:{},
            schoolIndex:null,
            calendarList:{},
            calendarData:{},
            isNewCalendarDay:false,
            addStart:'',
            addEnd:'',
            addTimeList:[],
            addAttrsTime:[],
            addTimeData:{
                "title_cn": "",
                "title_en": "",
                "memo_cn": "",
                "memo_en": "",
                "event": "",
                "day_type": "",
                "privacy": "",
                'event_type':'',
                'dept':'0'
            },
            editHolidayData:{},
            isEditCalendar:false,
            delList:{},
            availableDate:{},
            fromData:{},
            days:[],
            currentDaysList:[],
            currentDay:'',
            clickDays:null,
            disabledUpload:false,
            token:'',
            disabledUploadCn:false,
            disabledUploadEn:false,
            uploadId:'',
            pdf_cn_url:'',
            pdf_en_url:'',
            pdf_cn_name:'',
            pdf_en_name:'',
            pdf_cn_id:'',
            pdf_en_id:'',
            initUpload:false,
            teachingDays:0,
            qrcodeBox:false,
            files:{},
            exportBtn:false,
            fliterEventType:0,
            calendar_day_list_by_event_copy:{},
            minData:'',
            maxData:'',
            filter_event_type:[],
            importBtn:false,
            year_title:''
        },
        watch:{},
        created:function(){
           this.initList()
        },
        methods:{
            showQrcode(){
                $('#qrcodeBox').html('')
                this.qrcodeBox=!this.qrcodeBox
                $('#qrcodeBox').qrcode({
                    width:124,
                    height:124,
                    text: this.calendarData.preview_qrcode,
                    // background: "#efeeee",//二维码的后景色
                    // foreground: "#2f2c2c"//二维码的前景色
                });
                
            },
            initList(){
                let that = this;
                $.ajax({
                    url:'<?php echo $this->createUrlReg('getHomePageDataBySchool'); ?>',
                    type: 'get',
                    dataType:'json',
                    data:{},
                    success:function(data){
                        if(data.state=="success"){
                            container.initData=data.data
                            container.currentYid=data.data.select_yid
                            data.data.yearly_list.forEach((item,index) => {
                                if(item.yid==data.data.select_yid){
                                    container.showCalendar(index,data.data.select_yid)
                                }
                            })
                            if(container.schoolIndex!=null){
                                container.showList(data.data.yearly_list[container.schoolIndex],container.schoolIndex)
                            }
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            tabSimple(){
                $.ajax({
                    url:'<?php echo $this->createUrlReg('saveCalendarYearlyStat'); ?>',
                    type: 'post',
                    dataType:'json',
                    data:{
                        yid:this.currentYid
                    },
                    success:function(data){
                        if(data.state=="success"){
                            resultTip({msg: data.state});
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            newCalendarDay(){
                this.isNewCalendarDay=true
                let that=this
                this.addTimeList=[]
                this.addAttrsTime=[]
                this.addAttrs=this.attrs
                this.selectedDate={}
                this.addTimeData={
                    "title_cn": "",
                    "title_en": "",
                    "memo_cn": "",
                    "memo_en": "",
                    "event": "",
                    "day_type": "",
                    "privacy": "",
                    'event_type':'',
                    'dept':'0'
                }
            },
            pickDate(day) {
               if(this.selectedDate!=null && this.selectedDate.start){
                    this.addTime(this.selectedDate.start,this.selectedDate.end)
               }
            },
            addTime(start,end){
                let startDate=new Date(start)
                let endDate=new Date(end)
                var yearStart = startDate.getFullYear();
                var monthStart =startDate.getMonth();
                var dayStart = startDate.getDate();
                var yearEnd = endDate.getFullYear();
                var monthEnd =endDate.getMonth();
                var dayEnd = endDate.getDate();
                this.addTimeList.push({
                    date:yearStart + '.' + (monthStart + 1) + '.' + dayStart+' - '+yearEnd + '.' + (monthEnd + 1) + '.' + dayEnd,
                    time:['','']}
                    )
                if(startDate==endDate){
                    this.addAttrsTime.push({
                        highlight: {
                            fillMode: 'solid',
                        },
                        dates: new Date(yearStart, monthStart, dayStart),
                    })
                }else{
                    this.addAttrsTime.push({
                        highlight: {
                            start: { fillMode: 'solid', 
                            },
                            base: { fillMode: 'light' ,
                            },
                            end: { fillMode: 'solid',
                            },
                        },
                        dates: { start: new Date(yearStart, monthStart, dayStart), end: new Date(yearEnd, monthEnd, dayEnd) },
                    })
                }
                this.selectedDate={}
                this.addAttrs=this.addAttrs.concat(this.addAttrsTime)
            },
            closeTime(index){
                this.addTimeList.splice(index, 1); 
                this.addAttrsTime.splice(index, 1); 
                this.addAttrs=this.attrs.concat(this.addAttrsTime)
                this.selectedDate={}
            },
            saveNewTime(){
                this.addTimeData.yid=this.currentYid
                this.addTimeData.date_list=this.addTimeList
                this.addTimeData.privacy=this.addTimeData.privacy?1:0
                $.ajax({
                    url:'<?php echo $this->createUrlReg('createCalendarDay'); ?>',
                    type: 'post',
                    dataType:'json',
                    data:this.addTimeData,
                    success:function(data){
                        if(data.state=="success"){
                            resultTip({ msg: data.state});
                            container.newCalendarDay()
                            // container.isNewCalendarDay=false
                            container.showCalendar(container.currentCalendar,container.currentYid)
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            showCalendar(index,yid){
                this.currentYid=yid
                this.clickDays=null
                this.initUpload=false
                this.selectedDate={}
                this.filter_event_type=[]
                let start=this.initData.yearly_list[index].startyear
                let end=parseInt(this.initData.yearly_list[index].startyear)+1
                this.year_title=this.initData.yearly_list[index].year_title
                let that=this
                $.ajax({
                    url:'<?php echo $this->createUrlReg('getCalendarData'); ?>',
                    type: 'post',
                    dataType:'json',
                    data:{
                        yid:yid
                    },
                    success:function(data){
                        if(data.state=="success"){
                            that.pdf_cn_url=data.data.calendar_yearly_data.pdf_url_cn
                            that.pdf_cn_name=data.data.calendar_yearly_data.pdf_url_title_cn
                            that.pdf_en_name=data.data.calendar_yearly_data.pdf_url_title_en
                            that.pdf_en_url=data.data.calendar_yearly_data.pdf_url_en
                            that.pdf_cn_id=data.data.calendar_yearly_data.pdf_url_id_cn
                            that.pdf_en_id=data.data.calendar_yearly_data.pdf_url_id_en           
                            that.token = data.data.calendar_yearly_data.token;
                            that.initUpload=true
                            that.$nextTick(function(){
                                let btn=[{id:'PDF_cn',type:'pdf'},{id:'PDF_en',type:'pdf'}]
                                for(var i in btn){
                                    config['token'] = data.data.calendar_yearly_data.token;
                                    config['browse_button'] =btn[i].id;
                                    var uploader = new plupload.Uploader(config);
                                    uploader.init();
                                }
                            })
                            
                            var calendarHolidy=[]
                            var eventColorlist=[]
                            for(var key in data.data.calendar_day_list_by_event){
                                var item=data.data.calendar_day_list_by_event[key]
                                
                                for(var i=0;i<item.length;i++){
                                    var year = item[i].start_date.substring(0,4);
                                    var month =item[i].start_date.substring(5,7)-1;
                                    var day = item[i].start_date.substring(8,10);
                                    item[i].start=item[i].start_date.substring(5,10)
                                    var endYear = item[i].end_date.substring(0,4);
                                    var endMonth =item[i].end_date.substring(5,7)-1;
                                    var endDay = item[i].end_date.substring(8,10);
                                    item[i].end=item[i].end_date.substring(5,10)
                                    if(key ==10){
                                        if(item[i].days.length!=1){
                                            calendarHolidy.push({
                                                highlight: {
                                                    start: { fillMode: 'solid', 
                                                        style: {
                                                            backgroundColor: '#F0AD4E',
                                                        } 
                                                    },
                                                    base: { fillMode: 'light' ,
                                                        style: {
                                                            backgroundColor: 'rgba(240, 173, 78, 0.2)',
                                                        } ,
                                                    },
                                                    end: { fillMode: 'solid',
                                                        style: {
                                                            backgroundColor: '#F0AD4E',
                                                        }  
                                                    },
                                                },
                                                dates: { start: new Date(year, month, day), end: new Date(endYear, endMonth, endDay) },
                                            })
                                        }else{
                                            if(item[i].day_type==10){
                                                calendarHolidy.push({
                                                    highlight: {
                                                        fillMode: 'solid',
                                                        style: {
                                                            backgroundColor: '#F0AD4E',
                                                        },
                                                    },
                                                    dates: new Date(year, month, day),
                                                })
                                            }else{
                                                calendarHolidy.push({
                                                    highlight: {
                                                        fillMode: 'outline',
                                                        color: '#F0AD4E',
                                                        style: {
                                                            color: '#F0AD4E',
                                                        },
                                                    },
                                                    dates: new Date(year, month, day),
                                                })
                                            }
                                            
                                        }
                                    }
                                    if(key ==20){
                                        if(item[i].days.length!=1){
                                            let dateList=[]
                                            for(var j=0;j<item[i].days.length;j++){
                                                let daysOne=item[i].days[j]+''
                                                var year = daysOne.slice(0,4);
                                                var month =daysOne.slice(4,6)-1;
                                                var day = daysOne.slice(6,8);
                                                dateList.push(new Date(year, month, day))
                                                eventColorlist.push(year+'-'+month+'-'+day)
                                            }
                                        }else{
                                            eventColorlist.push(year+'-'+month+'-'+day)
                                        }
                                    }
                                    if(key ==30){
                                        if(item[i].days.length!=1){
                                            calendarHolidy.push({
                                                highlight: {
                                                    start: { fillMode: 'solid', 
                                                        style: {
                                                            backgroundColor: '#D9534F',
                                                        } 
                                                    },
                                                    base: { fillMode: 'light' ,
                                                        style: {
                                                            backgroundColor: 'rgba(217, 83, 79, 0.2)',
                                                        } ,
                                                    },
                                                    end: { fillMode: 'solid',
                                                        style: {
                                                            backgroundColor: '#D9534F',
                                                        }  
                                                    },
                                                },
                                                dates: { start: new Date(year, month, day), end: new Date(endYear, endMonth, endDay) },
                                            })
                                        }else{
                                            if(item[i].day_type==10){
                                                calendarHolidy.push({
                                                    highlight: {
                                                        fillMode: 'solid',
                                                        style: {
                                                            backgroundColor: '#D9534F',
                                                        },
                                                    },
                                                    dates: new Date(year, month, day),
                                                })
                                            }else{
                                                calendarHolidy.push({
                                                    highlight: {
                                                        fillMode: 'outline',
                                                        color: '#D9534F',
                                                        style: {
                                                            color: '#D9534F',
                                                        },
                                                    },
                                                    dates: new Date(year, month, day),
                                                })
                                            }
                                        }
                                    }
                                }
                            }
                            const uniqueTimestamps = [...new Set(eventColorlist)];
                            for(let i=0;i<uniqueTimestamps.length;i++){
                                const [year, month, day] = uniqueTimestamps[i].split('-');
                                let months = month.padStart(2, '0');
                                let days = day.padStart(2, '0');
                                calendarHolidy.push({
                                    dot: {
                                        style: {
                                            backgroundColor: '#55C1EE',
                                        },
                                    },
                                    dates:new Date(year, months, days),
                                })
                            }
                            that.calendar_day_list_by_event_copy=JSON.parse(JSON.stringify(data.data.calendar_day_list_by_event))
                            container.addAttrs=calendarHolidy
                            container.attrs=JSON.parse(JSON.stringify(calendarHolidy))
                            container.calendarData=data.data
                            container.currentCalendar=index
                            let startYearDate=container.calendarData.calendar_yearly_data.first_semester1.substring(5,7)
                            let endYearDate=container.calendarData.calendar_yearly_data.second_semester2.substring(5,7)
                            startYearDate = parseInt(startYearDate) -2
                            endYearDate = parseInt(endYearDate) + 2
                            container.availableDate={
                                start: new Date(start, startYearDate-1, 1),
                                end: new Date(end, endYearDate-1, 31)
                            }
                            container.range={
                                start: new Date(start,startYearDate-1, 1),
                                end: new Date(end, endYearDate-1, 31)
                            }
                            that.minData= new Date(start,startYearDate-1, 1),
                            that.maxData=new Date(end, endYearDate-1, 31)
                            const today = new Date();
                            const todayYear = today.getFullYear();
                            const todayMonth = String(today.getMonth() + 1).padStart(2, '0');
                            that.teachingDays=data.data.schooldays_by_month[todayYear+todayMonth]
                            // container.fromData={month:parseInt(startYearDate),year:parseInt(start)}
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            editHoliday(item,index,event){
                let items=JSON.parse(JSON.stringify(item))
                this.editHolidayData={
                    "event": event,
                    "day_type":items.day_type,
                    "privacy":'',
                    "bindend_id": items.bindend_id,
                    "start_date": items.start_date,
                    "end_date": items.end_date,
                    "title_cn": items.title_cn,
                    "title_en": items.title_en,
                    "memo_cn": items.memo_cn,
                    "memo_en": items.memo_en,
                    "time": items.time,
                    'event_type':items.event_type,
                    'dept':items.dept
                }
                this.editHolidayData.privacy=items.privacy==1?true:false
                let that=this
                $('#start_date').datepicker({
                        dateFormat: 'yy.mm.dd',
                        selectOtherMonths: false,
                        showOtherMonths: false,
                        changeMonth: true,
                        changeYear: true,
                        firstDay: 0,
                        onSelect: function (date,str) {
                            that.editHolidayData.start_date=date
                        }
                    });
                    $('#end_date').datepicker({
                        dateFormat: 'yy.mm.dd',
                        selectOtherMonths: false,
                        showOtherMonths: false,
                        changeMonth: true,
                        changeYear: true,
                        firstDay: 0,
                        onSelect: function (date,str) {
                            that.editHolidayData.end_date=date
                        }
                    });
                $('#editHoliday').modal('show')
            },
            saveCalendarDay(){
                $.ajax({
                    url:'<?php echo $this->createUrlReg('saveCalendarDay'); ?>',
                    type: 'post',
                    dataType:'json',
                    data:{
                        "yid": this.currentYid,
                        "event": this.editHolidayData.event,
                        "day_type":this.editHolidayData.day_type,
                        "privacy": this.editHolidayData.privacy?1:0,
                        "bindend_id": this.editHolidayData.bindend_id,
                        "edit_calendar_start_day": this.editHolidayData.start_date,
                        "edit_calendar_end_day": this.editHolidayData.end_date,
                        "title_cn": this.editHolidayData.title_cn,
                        "title_en": this.editHolidayData.title_en,
                        "memo_cn": this.editHolidayData.memo_cn,
                        "memo_en": this.editHolidayData.memo_en,
                        "time": this.editHolidayData.time,
                        'event_type':this.editHolidayData.event_type,
                        'dept':this.editHolidayData.dept
                    },
                    success:function(data){
                        if(data.state=="success"){
                            $('#editHoliday').modal('hide')
                            resultTip({ msg: data.state});
                            container.showCalendar(container.currentCalendar,container.currentYid)
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            delHoliday(item){
                if(item!='hide'){
                    this.delList=item
                    $('#delModal').modal('show')
                    return
                }
                let that=this
                $.ajax({
					url: '<?php echo $this->createUrlReg('delCalendarDay'); ?>',
					type: 'post',
					dataType: 'json',
					data: {
                        bindend_id:this.delList.bindend_id,
                        yid:this.currentYid
					},
					success: function(data) {
						if(data.state == 'success') {
                            resultTip({
								msg: data.state
							});
                            $('#delModal').modal('hide')
                            container.showCalendar(container.currentCalendar,container.currentYid)

						} else {
							resultTip({
								error: 'warning',
								msg: data.message
							});
						}
					},
					error: function(data) {
						resultTip({
							error: 'warning',
							msg: '请求错误'
						});
					}
				})
            },
            showHoliday(day){
                let days=day.year+day.month.toString().padStart(2,'0')+day.day.toString().padStart(2,'0')
                // this.currentDay=day.year+'年'+day.month.toString().padStart(2,'0')+'月'+day.day.toString().padStart(2,'0')+'日'
                this.currentDay=day.ariaLabel
                this.currentDaysList=[]
                this.calendarData.calendar_day_list.forEach((item,index) => {
                    if(item.date==days){
                        this.currentDaysList.push(item)
                    }
                })
            },
            PrevMonth(day){
                let month=this.$refs.calendar.$children[0].pages[0].month
                let year=this.$refs.calendar.$children[0].pages[0].year
                if(month<10){
                    month='0'+month
                }else{
                    month=month+''   
                }
                if(this.calendarData.schooldays_by_month[year+month]){
                    this.teachingDays=this.calendarData.schooldays_by_month[year+month]
                }else{
                    this.teachingDays=0
                }
            },
            uploadPDF(){
                $('#upload').modal('show')
            },
            delPDF(type){
                if(type=='cn'){
                    this.pdf_cn_url=''
                    this.pdf_cn_name=''
                    this.pdf_cn_id=''
                }else{
                    this.pdf_en_name=''
                    this.pdf_en_id=''
                    this.pdf_en_url=''
                }
            },
            saveCalendarPDF(){
                $.ajax({
                    url:'<?php echo $this->createUrlReg('saveCalendarPDF'); ?>',
                    type: 'post',
                    dataType:'json',
                    data:{
                        "yid": this.currentYid,
                        "pdf_url_cn":this.pdf_cn_id,
                        "pdf_url_en":this.pdf_en_id, 
                    },
                    success:function(data){
                        if(data.state=="success"){
                            $('#upload').modal('hide')
                            resultTip({ msg: data.state});
                            container.showCalendar(container.currentCalendar,container.currentYid)
                        }else{
                            resultTip({error: 'warning', msg: data.message});
                        }
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            exportCsv(){
                let that=this
                that.exportBtn=true
                $.ajax({
					url: '<?php echo $this->createUrlReg('exportEvent'); ?>',
					type: 'post',
					dataType: 'json',
					data: {
                        bindend_id:this.delList.bindend_id,
                        yid:this.currentYid
					},
					success: function(data) {
						if(data.state == 'success') {
                            const filename =that.calendarData.calendar_yearly_data.title+'.xlsx';
                            const ws_name = "SheetJS";
                            var exportDatas = [];
                            for(var i=0;i<data.data.length;i++){
                                exportDatas.push(
                                    {
                                    'StartDateAndTime':data.data[i].start_date,
                                    'EndDateAndTime':data.data[i].end_date,
                                    "Event":data.data[i].event,
                                    "FullOrHalfDay":data.data[i].day_type,
                                    "TitleCn":data.data[i].title_cn,
                                    "TitleEn":data.data[i].title_en,
                                    "DescriptionCn":data.data[i].memo_cn,
                                    "DescriptionEn":data.data[i].memo_en,
                                    "OnlyStaffSee":data.data[i].privacy,
                                    "Categories":data.data[i].event_type,
                                    "Location":data.data[i].locations,
                                    "Division":data.data[i].dept,
                                    "Duration":data.data[i].duration,
                                    });
                            }
                            var wb=XLSX.utils.json_to_sheet(exportDatas,{
                                origin:'A1',// 从A1开始增加内容
                                header: ['StartDateAndTime','EndDateAndTime', 'Event', 'FullOrHalfDay','TitleCn','TitleEn','DescriptionCn','DescriptionEn','OnlyStaffSee','Categories','Location','Division','Duration'],
                            });
                            const workbook = XLSX.utils.book_new();
                            XLSX.utils.book_append_sheet(workbook, wb, ws_name);
                            const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                            const blob = new Blob([wbout], {type: 'application/octet-stream'});
                            let link = document.createElement('a');
                            link.href = URL.createObjectURL(blob);
                            link.download = filename;
                            link.click();
                            setTimeout(function() {
                                // 延时释放掉obj
                                URL.revokeObjectURL(link.href);
                                link.remove();
                                that.exportBtn=false
                            }, 500);
						} else {
							resultTip({
								error: 'warning',
								msg: data.message
							});
                            that.exportBtn=false
						}
					},
					error: function(data) {
						resultTip({
							error: 'warning',
							msg: '请求错误'
						});
					}
				})
            },
            showTip(){
                $('#fileprogress').modal('show')
            },
            handleSelectedImg() {
                this.$refs['img-upload-input'].click()
            },
            //选好图片之后点击打开按钮
            submitUpload(e) {
                this.files = e.target.files
                const rawFile = this.files[0] // only use files[0]
                // if (!rawFile) return
                this.uploadSectionFile(rawFile)
                
                
            },
            unUploadFiles(){
                this.$refs['img-upload-input'].value = null 
            },
            uploadFiles(){
                this.uploadSectionFile(this.files[0])
            },
            uploadSectionFile(file) {
                let that=this
                this.importBtn=true
                const reader = new FileReader();
                reader.readAsBinaryString(file);
                reader.onload = (e) => {
                    const data = e.target.result;
                    const my_excel = window.XLS.read(data, {
                        type: 'binary'
                    });
                    const sheet2JSONOpts = {
                        defval: ''//给defval赋值为空的字符串
                    }
                    let xlx_json = window.XLS.utils.sheet_to_json(my_excel.Sheets[my_excel.SheetNames[0]],sheet2JSONOpts);
                    let importData = {
                        yid:this.currentYid,
                        data:JSON.stringify(xlx_json)
                    };
                    console.log(importData)
                    $.post(
                        '<?php echo $this->createUrlReg('importEvent2'); ?>',
                        importData,
                        function (res){
                            if(res.state == 'success') {
                                that.importBtn=false
                                resultTip({
                                    msg: res.data.msg
                                });
                                that.$refs['img-upload-input'].value = null
                                $('#fileprogress').modal('hide')
                                container.showCalendar(container.currentCalendar,container.currentYid)
                            } else {
                                resultTip({
                                    error: 'warning',
                                    msg: res.message
                                });
                                that.importBtn=false
                            }
                        },
                        'json'
                    )
                }
            },
            clearEvent(modal){
                if(modal){
                    $('#clearEventModal').modal('show')
                    return
                }
                
                let that=this
                $.ajax({
					url: '<?php echo $this->createUrlReg('clearAllEvent'); ?>',
					type: 'post',
					dataType: 'json',
					data: {
                        yid:this.currentYid,
                        event:10
					},
					success: function(data) {
						if(data.state == 'success') {
                            resultTip({
								msg: data.state
							});
                            $('#clearEventModal').modal('hide')
                            container.showCalendar(container.currentCalendar,container.currentYid)
						} else {
							resultTip({
								error: 'warning',
								msg: data.message
							});
						}
					},
					error: function(data) {
						resultTip({
							error: 'warning',
							msg: '请求错误'
						});
					}
				})
            },
            filterType(){
                if(this.filter_event_type.length==0){
                    this.calendarData.calendar_day_list_by_event[20]=this.calendar_day_list_by_event_copy[20]
                }else{
                    this.calendarData.calendar_day_list_by_event[20] =this.calendar_day_list_by_event_copy[20].filter((a,i)=>{ 
                        return  this.filter_event_type.some(f=>(parseInt(f) === a.event_type)) 
                    })                  
                }
            }
        }
    })
    var config = {
        runtimes: 'html5,flash,html4', //上传模式,依次退化
        container: 'container', //上传区域DOM ID，默认是browser_button的父元素，
        token: '',
        flash_swf_url: '/js/plupload/Moxie.swf', // flash的相对地址
        auto_start: true, //选择文件后自动上传，若关闭需要自己绑定事件触发上传
        multi_selection: false, //不允许多选
        url:'https://upload-z1.qiniup.com',
        filters:{ 
            mime_types: [ {title : "<?php echo Yii::t('teaching', 'pdf files');?>", extensions : "pdf,mp3,zip"}]
        },
        init: {
            'FilesAdded': function(up, files) {
                container.uploadId=up.getOption("browse_button")[0].id
                if(container.uploadId=='PDF_cn'){
                    container.disabledUploadCn=true
                }else if(container.uploadId=='PDF_en'){
                    container.disabledUploadEn=true
                }
                up.start();
            },
            BeforeUpload: function(up, file) {
                //设置参数
                up.setOption({
                    multipart_params:{token:container.token},
					headers: {
						Authorization: "UpToken " + container.token
					}
				});
            },
            'FileUploaded': function(up, file, info) {       
                var response = eval('('+info.response+')');
                if(info.status == 200){
                    if(container.uploadId=='PDF_cn'){
                        container.disabledUploadCn=false
                        container.pdf_cn_url=response.data.file_key
                        container.pdf_cn_name=response.data.title
                        container.pdf_cn_id=response.data._id
                    }else{
                        container.disabledUploadEn=false
                        container.pdf_en_url=response.data.file_key
                        container.pdf_en_name=response.data.title
                        container.pdf_en_id=response.data._id
                    }
                }
            },
            'Error': function(up, err, errTip) {
                if(err.response){
                    var response = eval('('+err.response+')');
                }
                else{
                    var response = {message: err.message};
                }
                if(response.error == 'token not specified'){
                    up.stop();
                    $.ajax({
                        url: '<?php echo $this->createUrl("getHomePageDataBySchool") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {},
                        success: function(data) {
                            if (data.state == 'success') {
                                container.token = data.data.calendar_yearly_data.token;
                                config['token'] = data.data.calendar_yearly_data.token;
                                up.setOption("multipart_params", {"token":data.data.calendar_yearly_data.token,})
                                up.start();
                            } else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.msg
                                });
                            }
                        },
                        error: function(data) {

                        },
                    })
                }
            },
            'UploadComplete': function(up, file) {
                //队列文件处理完毕后,处理相关的事情
            }
        }
    };
</script>
<style>
    [v-cloak] {
		display: none;
	}
    .pdf{
        border:1px solid #E8EAED;
        border-radius:4px;
        padding:10px
    }
    .p0{
        padding:0 !important
    }
    .bgGrey{
        background: #FAFAFA;
        border-radius: 8px;
        padding:16px 24px
    }
    .schoolList{
        background: #FFFFFF;
        border-radius: 4px;
        border: 1px solid #E8EAED;
        padding:16px
    }
    .borderIcon{
        border: 1px solid #428bca;
        border-radius: 50%;
        font-size: 12px;
        text-align: center;
        line-height: 15px;
        display: inline-block;
        width: 17px;
        height: 17px;

    }
    .borderTop{
        border-top: 1px solid #E8EAED;
    }
    .colorC{
        color:#ccc
    }
    .select_3{
        display:inline-block
    }
    .switch {
        position: relative;
        display: inline-block;
    }
    .switch input {display:none;}
    .slider {
        margin: 0;
        display: inline-block;
        position: relative;
        width: 40px;
        height: 20px;
        border: 1px solid #dcdfe6;
        outline: none;
        border-radius: 10px;
        box-sizing: border-box;
        background: #dcdfe6;
        cursor: pointer;
        transition: border-color .31s,background-color .3s;
        vertical-align: middle;
    }
    .slider:before {
        content: "";
        position: absolute;
        top: 1px;
        left: 1px;
        border-radius: 50%;
        transition: all .3s;
        width: 16px;
        height: 16px;
        background-color: #fff;
    }
    input:checked + .slider {
        background-color: #2196F3;
    }
    input:checked + .slider:before {
        left: 100%;
        margin-left: -17px;
    }
    .checkedCalendar{
        background: rgba(77, 136, 210, 0.1);
        border-radius: 4px;
        border: 1px solid #4D88D2;
    }
    .calendarDetails{
        background: #FAFAFA;
        border-radius: 4px;
        border: 1px solid #E8EAED;
        padding:16px 24px;
        margin-bottom:20px
    }
    .font20{
        font-size:20px
    }
    .font18{
        font-size:18px
    }
    .calendarNum{
        background: #FFFFFF;
        border-radius: 12px;
        border: 1px solid #E8EAED;
        padding:16px;
        margin:16px 50px 0 0;
        margin-bottom:16px
    }
    .calendarNum .text-center{
        border-right:1px solid #E8EAED
    }
    .calendarNum .text-center:last-child {
        border-right:none
    }
    .point{
        width: 10px;
        height: 10px;
        border-radius:50%;
        display: inline-block;
    }
    .holidayColor{
        background: #F0AD4E;
        border-left:4px solid #F0AD4E;

    }
    .eventColor{
        background: #55C1EE;
        border-left:4px solid #55C1EE;

    }
    .weekColor{
        background: #D9534F;
        border-left:4px solid #D9534F;
    }
    .currentColor{
        background: #4D88D2;
    }
    .holidayDetails{
        background: #FAFAFA;
        border-radius: 4px;
        position: relative;
    }
    .privacy{
        position: absolute;
        right:0;
        top:0;
        background:#F2F3F5;
        padding:2px 4px;
        border-radius:2px;
        font-size:12px;
        color: #333333;
    }
    .time{
        width:40px
    }
    .alignCenter{
        align-items: center;
    }
    .timeData{
        background: #F7F7F8;
        padding:12px 16px;
        align-items:center
    }
    .time-picker{
        width:100% !important
    }
    .el-date-editor .el-range-separator{
        width:10%
    }
    .timeClose{
        color: #999999;
        margin-left: 20px;
    }
    .fullDay{
        background:#666;
    }
    .halfDay{
        width: 10px;
        height: 10px;
        background: #FFFFFF;
        border: 1px solid #666666;
        display:inline-block;
        border-radius:50%
    }
    .dayType{
        padding: 2px 6px;
        background: rgba(77, 136, 210, 0.10);
        color: #4D88D2;
        border-radius: 2px;
        margin-right:6px
    }
    .m15{
        margin:0 20px;
    }
    .daysDetails{
        background:#4D88D2;
        border-radius:8px;
        padding:16px;
        color:#fff;
        width:250px
    }
    .font16{
        font-size:16px
    }
    .eventType{
        width: 22px;
        height: 22px;
        background: #ccc;
        border-radius: 14px;
        text-align:center;
        line-height:22px;
        font-size:14px;
        display: inline-block;
    }
    .qrcodeBox{
        position: absolute;
        right: 55px;
        top: 15px;
        width: 240px;
        height: 228px;
        border: 1px solid #ddd;
        background: #fff;
        border-radius: 4px;
        text-align:center;
        font-size:14px;
        color:#333;
        box-shadow: 0px 2px 8px 0px rgb(0 0 0 / 20%);
        z-index:9
    }
    .qrcodeBox img{
        width:124px;
        height:124px;
        margin-top:24px
    }
    .eventCheckType{
        border-radius: 4px;
        background: #F7F7F8;
        padding: 16px;
        align-items: flex-start;;
        font-size: 14px;
        margin-top: 15px;
    }
    .eventCheckType label{
        line-height: 1.5;
        padding-top: 0 !important;
        margin-left: 0 !important;
        margin-right: 10px;
    }
    .mt0{
        margin-top:0px !important
    }
    .eventEdit{
        width: 20px;
        height: 20px;
        background: #F2F3F5;
        text-align: center;
        line-height: 20px;
        border-radius: 3px;
    }
    .labelCss{
        font-weight:normal
    }
    .multipleSelect{
        width:100%
    }
    .calendar .vc-day{
        min-height: 40px;
    }
</style>