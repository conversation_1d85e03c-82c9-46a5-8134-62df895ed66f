<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('//mcampus/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Basic'), array('//mcampus/default/index'))?></li>
        <li class="active">招生管理</li>
    </ol>

    <div class="row">
        <!-- 左侧菜单 -->
        <div class="col-md-2 col-sm-2">
            <?php
            $this->widget('zii.widgets.CMenu',array(
                'items'=> $this->getMenu(),
                'id'=>'visitConfiguration',
                'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked background-gray mb10'),
                'activeCssClass'=>'active',
                'itemCssClass'=>''
            ));
            ?>
        </div>
        <div class="col-md-10 col-sm-10">
            <div  data-backdrop='static'>
                <?php
                $form=$this->beginWidget('CActiveForm', array(
                    'id'=>'newstaff-forms',
                    'action'=>$this->createUrl('VisitsConfig'),
                    'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form','enctype'=>"multipart/form-data"),
                ));
                ?>
                <div class="modal-body_two">
                    <div class="form-group">
                        <label class="col-xs-3 control-label">预约状态</label>
                        <div class="col-xs-9">
                            <span id="text1">
                                <input type="radio" name="status" id="status1" <?php if($model['status'] == 1){echo 'checked="checked"';}?> value="1"><label for="status1" >开启</label>
                            </span>
                            <span id="text2">
                                <input type="radio" name="status" id="status2" <?php if($model['status'] == 0){echo 'checked="checked"';} ?>value="0"><label for="status2" >关闭</label>
                            </span>
                        </div>
                    </div>
                    <div class="form-group" id="shut_down_1" style="display: none">
                        <label class="col-xs-3 control-label">关闭提示文字(中文)</label>
                        <div class="col-xs-3">
                            <input type="type" class="form-control" name="status_text_cn" value="<?php echo ($model['status_text_cn']) ? $model['status_text_cn'] : "" ?>"  />
                        </div>
                    </div>
                    <div class="form-group" id="shut_down_2" style="display: none">
                        <label class="col-xs-3 control-label">关闭提示文字(英文)</label>
                        <div class="col-xs-3">
                            <input type="type" class="form-control" name="status_text_en" value="<?php echo ($model['status_text_en']) ? $model['status_text_en'] : "" ?>" />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-xs-3 control-label">可预约日期</label>
                        <div class="col-xs-8">
                            <?php foreach($array as $k=>$v) {?>
                                <label class="">
                                    <input type="checkbox" name="week[<?php echo $k; ?>]" <?php if(isset($model['week'][$k]) && $model['week'][$k] != null){echo 'checked="checked"';}?> value="<?php echo $k; ?>"> <?php echo $v; ?>
                                </label>
                            <?php } ?>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-xs-3 control-label">开放日</label>
                        <div class="col-xs-5" >
                            <div id="pending" class="mb15"></div>
                            <?php
                            $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                                'name'=>'pending',
                                'options'=>array(
                                    'showOtherMonths'=>true,
                                    'selectOtherMonths'=>true,
                                    'minDate'=>'d',
                                    'onSelect'=>'js:onSelect',
                                ),
                                'htmlOptions'=>array(
                                    'class'=>'hidden',
                                ),
                            ));
                            ?>
                        </div>
                    </div>
                    <!-- 常规设置 -->
                    <div class="form-group">
                        <label class="col-xs-3 control-label">参观时间</label>
                        <div class="col-xs-5" id="demo">
                            <?php
                            if($model['time']){
                                $q = 1;
                                foreach($model['time'] as $v){
                                    echo '<div><a href="#" class="removeclass">×</a>
								<div class="form-inline">
									<input class="form-control mb5"  name="time['. $q .'][time]"  value="'. $v['time'] . '"   value=""placeholder="参观的时间段">
									<input id="top_style" class="form-control mb5" name="time['. $q .'][man]"   value="'. $v['man'] . '" placeholder="时间段内接待人数上限">
									</div></div>';
                                    $q++;
                                }
                            }else{
                                echo '<div class="form-inline">
									<input class="form-control mb5"  name="time[99999][time]"   value="" placeholder="参观的时间段">
									<input id="top_style" class="form-control mb5" name="time[99999][man]"   value="" placeholder="时间段内接待人数上限">
								</div>';
                            }

                            ?>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-xs-5 col-xs-offset-3">
                            <a href="JavaScript:void(0)" id="AddTime" class="btn btn-info">增加时间段</a>
                        </div>
                    </div>
                    <!-- 额外日期设置 -->
                    <div class="form-group">
                        <label class="col-xs-3 control-label">特殊日期</label>
                        <div class="col-md-8 col-xs-8" id="Tday">
                            <?php foreach ($days as $date => $day) : ?>
                                <?php foreach ($day as $time => $v) : ?>
                                <div id="day_<?php echo $v['id']; ?>">
                                <a href="<?php echo $this->createUrl('deleteDay', array('id'=>$v['id'])); ?>" class="J_ajax_del">×</a>
                                <div class=" form-inline" >
                                    <input class="form-control mb5" name="cdate[]" type="text" value="<?php echo $date; ?>" readonly ="readonly"> 
                                    <input class="form-control mb5" name="ctime[]" type="text" value="<?php echo $time; ?>" > 
                                    <input class="form-control mb5" name="cnum[]" type="text" value="<?php echo $v['num']; ?>" >
                                </div>
                                </div>
                                <?php endforeach; ?>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t("global", 'Submit');?></button>
                </div>
                <?php
                $this->endWidget();
                ?>
            </div>
        </div>
    </div>
</div>
<script>
    function cbAddVisits()
    {
        location= '<?php echo $this->createUrl('visitsConfig')?>';
    }

    function onSelect(dateText, inst) {
        var dt = inst.currentYear+'-'+(inst.currentMonth+1)+'-'+inst.currentDay;
        var a = 1;
        $("#Tday input").each(function(i, e){
            // if(dt == $(e).val()){
            //     a = 2;
            //     resultTip({error:true, msg: '日期不能重复'});
            // }
            // console.log($(e).val());
        });
        if(a != 2){
            $("#Tday").append('<div><a href="#" class="removeclass">×</a><div class=" form-inline"><input class="form-control mb5" name="cdate[]" type="text" value="'+ dt +'" readonly="readonly"> <input class="form-control mb5" name="ctime[]" type="text" placeholder="参观的时间段"> <input class="form-control mb5" name="cnum[]" type="text" placeholder="时间段内接待人数上限"></div></div>');
        }
    }


    $("#AddDay").click(function (a)
    {
        $("#Tday").append('<input class="form-control" name="day"  id="day" type="text"   placeholder="">');
    });

    var i = 100000;

    $("#AddTime").click(function (e)
    {
        $("#demo").append('<div><a href="#" class="removeclass">×</a><div class="form-inline"><input class="form-control mb5"  name="time['+ i +'][time]"   value="" placeholder="参观的时间段"> <input id="top_style" class="form-control mb5" name="time['+ i +'][man]"   value="" placeholder="时间段内接待人数上限"></div></div>');
        i++;
    });
    $("body").on("click",".removeclass", function(e){
        $(this).parent('div').remove();
    })

    $("#text1").bind("click",function(){
        $("#shut_down_1").css('display','none')
        $("#shut_down_2").css('display','none')
    });

    $("#text2").bind("click",function(){
        $("#shut_down_1").css('display','block')
        $("#shut_down_2").css('display','block')
    });

    var status_text = "<?php echo ($model['status']) ? $model['status'] : 0 ?>"
    if(status_text == 1){
        $("#shut_down_1").css('display','none')
        $("#shut_down_2").css('display','none')
    }else{
        $("#shut_down_1").css('display','block')
        $("#shut_down_2").css('display','block')
    }

    function deleteSuccess(id) {
        $('#day_' + id).remove();
    }


</script>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>