<?php
//$classType = $this->childObj->ivyclass->classtype;
//foreach ($dataProvider->getData() as $invoiceItem) {
//    if ($invoiceItem->payment_type == 'tuition') {
//        if ($invoiceItem->classInfo->classtype == 'k') {
//            $classType = 'k';
//        }
//    }
//}

if($this->childObj->schoolid == $this->specialSchool){
    $payType = $this->getDsPayType();
}
?>

<div id="childinfo">
    <?php $this->widget('ext.childInfo.ChildInfo', array('childObj'=>$this->childObj,'selectedPayFlag'=>$t))?>
</div>

<?php if(!$POSenabled && $t === 'pos'):?>
    <div class="tips_light tips_block">
        <span>
            本校园暂未开通订单POS机刷卡支付业务（订单POS机是与本系统集成的第三方支付方式，不含校园自行办理的POS机）
        </span>
    </div>
<?php elseif(!$wxPayenabled && $t === 'micropay'):?>
    <div class="tips_light tips_block">
        <span>
            本校园暂未开通微信支付业务
        </span>
    </div>
<?php else:?>

<script>
var o = true;
function usePartialPay(value){
	var Turl;
    var nextSchool = $("input[name='nextSchool']").is(':checked');

    if (nextSchool) {
        nextSchool = 'nextSchool';
    }else{
        nextSchool = '';
    }

	if(value == undefined){
		Turl = "<?php echo $this->createUrl('//child/invoice/unpaid', array('t'=>$t));?>";
	}
	else{
		Turl = "<?php echo $this->createUrl('//child/invoice/unpaid', array('t'=>$t, 'usePartial'=>'usePartial'));?>" + '&nextSchool=' + nextSchool;
	}
	location.href = Turl;
}

function changeSchool(value) {
    var Turl;
    var usePartial = $("input[name='usePartial']").is(':checked');

    if (usePartial) {
        usePartial = 'usePartial';
    }else{
        usePartial = '';
    }

    if(value == undefined){
        Turl = "<?php echo $this->createUrl('//child/invoice/unpaid', array('t'=>$t));?>";
    }
    else{
        Turl = "<?php echo $this->createUrl('//child/invoice/unpaid', array('t'=>$t, 'nextSchool'=>'nextSchool'));?>" + '&usePartial=' + usePartial;
    }
    location.href = Turl;
}


function useCredit(id)
{
    if (o){
        o = false;
        var credit = parseFloat('<?php echo $this->childObj->credit; ?>')
        var use = parseFloat($('#'+id+'_credit').val())
        if (parseFloat($('#finalAmount_'+id).val()) == 0){
            alert('此账单需支付金额为0，请点击现金付款按钮完成操作。');
            o = true;
            return false;
        }

        if ((use - credit) > 0.001) {
            alert('使用金额不能大于个人账户余额');
            return false;
        }
        if (confirm('确定使用个人余额支付？')){
            $.ajax({
                type: 'post',
                url: '<?php echo $this->createUrl('//child/invoice/processUseCredit');?>',
                data: {id: id, amount: use},
                dataType: 'json',
                success: function(data){
                    if (data.state === 'success'){
                        reloadPage(window);
                    }
                    else {
                        alert(data.message);
                    }
                },
                complete: function(){
                    o = true;
                }
            });
        }
        else {
            o = true;
        }
    }
}

function pPay(id)
{
    if (o){
        var obj = $('#finalAmount_'+id);
        var amount = obj.val();
        o = false;
        if (parseFloat(amount) < 0.001 || parseFloat(amount) >= parseFloat(obj.attr('fullamount'))){
            alert('<?php echo Yii::t("payment","Please check the amount.");?>');
            obj.select();
            o = true;
            return false;
        }
        if (confirm("本次支付："+amount+"\n确定付款？")){
            $.ajax({
                type: 'post',
                url: '<?php echo $this->createUrl('//child/invoice/processPartialCashPay');?>',
                data: {id: id, amount: amount, transactiontype: $('#p_'+id).find('select').val(), paydate: $('#paydate_'+id).val()},
                dataType: 'json',
                success: function(data){
                    if (data.state === 'success'){
                        reloadPage(window);
                    }
                    else {
                        alert(data.message);
                    }
                },
                complete: function(){
                    o = true;
                }
            });
        }
        else {
            o = true;
        }
    }
}
$(function() {
	var numObj = $('#pay_summary .num');
	var amountObj = $('#pay_summary .amount');
	var invoiceCountObj = $('#invoiceCount');
	var invoiceTotalAmountObj = $('#invoiceTotalAmount');
	
	var num = 0;
	var amount = 0.00;
	
	$('.checkbox-column input:checkbox').on('change',function(){
		num=0;
		amount=0;
        var usePartialPay = <?php echo ($usePartial)? 1 : 0;?>;
        var isRowEle = $(this).attr("name").indexOf("[]");
        if(usePartialPay != 1){
            if(isRowEle == -1){
                if( $(this).attr("checked") == 'checked' ){
                    $(".checkbox-column input:checkbox").each(function(){
                            var _id = "#finalAmount_"+$(this).val();
                            var _v = parseFloat($(_id).val());
                            if(!isNaN(_v)){
                                amount = parseFloat(amount) + parseFloat(_v);
                                num += 1;
                            }
                    })
                }
            }else{
                $(".checkbox-column input:checkbox[checked]").each(function(){
                        var _id = "#finalAmount_"+$(this).val();
                        var _v = parseFloat($(_id).val());
                        if(!isNaN(_v)){
                            amount = parseFloat(amount) + parseFloat(_v);
                            num += 1;
                        }
                })
            }
        }else{
            if( $(this).attr("checked") == 'checked' ){
                var _id = "#finalAmount_"+$(this).val();
                var _v = parseFloat($(_id).val());
                $('input.final-amount').attr("disabled","disabled");
                $(_id).removeAttr('disabled');
                $(_id).focus();

                $('.J_pay').hide();
                <?php if($t === 'cash'):?>
                var _obj = $('#p_'+$(this).val());
                <?php if($this->childObj->schoolid == $this->specialSchool):?>
                if(!_obj.find('select').length){
                    var selectHTML = '<select class="mr10" name="transactiontype">';
                    selectHTML += '<option value=""><?php echo Yii::t('global', 'Please Select')?></option>'
                    <?php foreach($payType as $pkey=>$pt):?>
                    selectHTML += '<option value="<?php echo $pkey?>"><?php echo $pt;?></option>';
                    <?php endforeach;?>
                    selectHTML += '</select>';
                    <?php if($this->payDateToggle == true):?>
                    selectHTML += '<input id="paydate_'+$(this).val()+'" name="paydate" type="text" class="input length_2 mr10" placeholder="<?php echo Yii::t('payment', '付款日期');?>">';
                    <?php endif;?>
                    _obj.prepend(selectHTML);
                    <?php if($this->payDateToggle == true):?>
                    $('#paydate_'+$(this).val()).datepicker({'changeMonth':true,'changeYear':true,'dateFormat':'yy-mm-dd'});
                    <?php endif;?>
                }
                <?php endif;?>
                _obj.show();
                <?php endif;?>
                amount = _v;
                num = 1;
            }else{
                $('input.final-amount').attr("disabled","disabled");
                $('.J_pay').hide();
                amount = 0;
                num = 0;
            }
        }
        amount = amount.toFixed(2);
        numObj.html(num);invoiceCountObj.val(num);
        amountObj.html(amount);invoiceTotalAmountObj.val(amount);
	});
	
	<?php if($usePartial):?>
	$('input.final-amount').bind("keyup",function(){
		if(isNaN($(this).val()))
			$(this).val(0);
		amountObj.html($(this).val());invoiceTotalAmountObj.val($(this).val());
	});
	<?php endif;?>

    $('#auth_code').bind('keypress',function(event){
        if(event.keyCode == "13")
        {
            if($(this).val() == ''){
                resultTip({msg: '请先扫描授权码', error: 1});
                $(this).focus();
            }
            else{
                $.ajax({
                    type: 'post',
                    beforeSend: function(){return submitCheck()},
                    dataType: 'json',
                    url: '<?php echo $this->createUrl('wxmicropay')?>',
                    data: $(this).parents("form").serialize(),
                    success: function(data){
                        if(data.return_code == 'SUCCESS'){
                            if(data.result_code == 'SUCCESS'){
                                resultTip({msg: '付款成功', callback: function(){
                                    reloadPage(window);
                                }});
                            }
                            else{
                                if(data.err_code == 'AUTHCODEEXPIRE'){
                                    resultTip({msg: data.err_code_des, error: 1});
                                }
                                else if(data.err_code == 'USERPAYING'){
                                    showMicropay(data.out_trade_no);
                                }
                            }
                        }
                    },
                    complete: function(){
                        o=true;
                    }
                });
            }
            return false;
        }
    });
});

function submitCheck(){
    if (!o){
        return false;
    }
    o = false;
	var found = $(".checkbox-column input:checkbox[checked]");
	<?php if($usePartial):?>
		if(found.length != 1){alert("<?php echo Yii::t("payment","Please select only one invoice");?>");o = true;return false;}
		input = $('#finalAmount_'+found.val());
		if (isNaN(input.attr('fullamount')) || isNaN(input.val()) || parseFloat(input.attr('fullamount')) <= parseFloat(input.val())){
			alert("<?php echo Yii::t("payment","Please check the amount.");?>");
			input.focus();
            o = true;
			return false;
		}
    <?php else:?>
        if(found.length < 1){alert("<?php echo Yii::t("payment","Please select at least one invoice");?>");o = true;return false;}
    <?php endif;?>
}
function showOrderID(id)
{
    <?php if(1):?>
    head.dialog.open('<?php echo $this->createUrl("postYeePay", array('allinpay'=>1))?>&orderId='+id, {title: '订单POS支付：'+id});
    <?php else:?>
    window.open('<?php echo $this->createUrl("postYeePay")?>&orderId='+id);
    <?php endif;?>
}

function showMicropay(id)
{
    head.dialog.open('<?php echo $this->createUrl("showmicropay", array('wx'=>1))?>&id='+id, {title: '微信刷卡支付'});
}

</script>

<div class="form">
		<?php $form=$this->beginWidget('CActiveForm', array(
			'id'=>'child-form',
			'enableClientValidation'=>true,
			'clientOptions'=>array(
				'validateOnSubmit'=>true,
			),
		)); ?>	
		
			<div class="tips_light tips_block">
				<span>
					如果账单金额过大，家长需要使用组合付款方式进行拆分付款，请使用此功能，组合付款时每次只能支付一张账单。
				</span>
				<span class="red">
					<?php
						echo CHtml::checkBox('usePartial', $usePartial, array('onClick'=>'js:usePartialPay($(this).attr("checked"))'));
						echo CHtml::label(Yii::t('payment','组合付款'), 'usePartial');
					?>
				</span>
				<?php if (!$usePartial && ($this->childObj->credit > 0.01) ):?>
					<?php if ($t === 'pos'):?>
					<br />
					　如果要使用个人账户，请在要付款的账单后点击“使用个账余额”先进行付款，完成后再生成POS机订单。
					<?php endif;?>
				<br />
				　使用个人账户时，须全额使用个人账户的余额，系统不再询问使用的金额数量，如果余额大于账单金额，将只使用账单金额部分，剩余部分将继续留在个人账户中。
				<?php endif;?>
			</div>
            <?php if ($transferSchool === true): ?>
            <div class="tips_light tips_block">
                <span>
                    当前孩子下学年将转走，查看下学年账单请选择。
                </span>
                <span class="red">
                    <?php
                        echo CHtml::checkBox('nextSchool', $nextSchool, array('onClick'=>'js:changeSchool($(this).attr("checked"))'));
                        echo CHtml::label(Yii::t('payment','下学年'), 'nextSchool');
                    ?>
                </span>
            </div>
            <?php endif; ?>
			
		<?php
            // 计算需要使用的个人账户金额
             round($data->amount - $data->paidSum, 2);
			function renderInvoiceLink($invoice){
				return CHtml::link($invoice->title, array("//child/invoice/viewInvoice", 'invoiceid'=>$invoice->invoice_id), array('class'=>'mr5 J_dialog'));
			}
			$this->widget('ext.ivyCGridView.IvyCGridView', array(
				'id'=>'posInvoiceId',
				'dataProvider'=>$dataProvider,
				//'template'=>"<div class='table_list'>{items}</div><div class='table_list'>{summary}</div><div class='table_list'>{pager}</div>",
				'template'=>"<div class='table_list'>{items}</div>",
				'colgroups'=>array(
					array(
						"colwidth"=>array(40,180,120,100,100,100,100,400),
					)
				),
				'ajaxUpdate'=>false,
				'columns'=>array(
					'id'=>array(
					   'value'=>'$data->invoice_id',
					   'class'=>'CCheckBoxColumn',
					   'selectableRows'=>($usePartial)? 1 : '2 or more',
					   'checkBoxHtmlOptions'=>array(
					   )
					),
			'title'=>array(
				'name'=>'title',
				'type'=>'raw',
				'value'=>'renderInvoiceLink($data)',
			),
			'payment_type'=>array(
				'name'=>'payment_type',
				'value'=>'Yii::app()->controller->policyApi->renderFeeType($data->payment_type)',
			),
			'createTime'=>array(
				'name'=>'timestamp',
				'header'=>Yii::t("payment", "Issue Date"),
				'value'=>'OA::formatDateTime($data->timestamp)'
			),
			'amount'=>array(
				'name'=>'amount',
			),
			'paidamount'=>array(
				'header'=>Yii::t('payment','Paid Amount'),
				'value'=>'$data->paidSum'
			),			
			'finalamount'=>array(
				'header'=>Yii::t('payment','Final Amount'),
				'value'=>'round($data->amount - $data->paidSum, 2)'
			),
			'partially'=>array(
				'header'=>($usePartial) ? Yii::t("payment", "Partially Pay Amount") : (( ($this->childObj->credit > 0.01) ? Yii::t("payment","Credits :credit", array(":credit"=>$this->childObj->credit)) : '' )),
				'type'=>'raw',
				'value'=> ($usePartial) ? 'CHtml::textField("finalAmount[$data->invoice_id]", round($data->amount - $data->paidSum, 2), array("class"=>"input final-amount", "fullamount"=>round($data->amount - $data->paidSum,2), "invoiceid"=>$data->invoice_id, "disabled"=>"disabled")).CHtml::openTag("span", array("class"=>"ml10 J_pay", "id"=>"p_$data->invoice_id", "style"=>"display:none;")).CHtml::link(Yii::t("payment","付款"), "#", array("class"=>"btn", "onclick"=>"pPay($data->invoice_id);return false;")).CHtml::closeTag("span")' :
					( ( $this->childObj->credit > 0.001 ) ? 'CHtml::link(Yii::t("payment","Use Credit"), "#", array("class"=>"btn", "onclick"=>"useCredit($data->invoice_id);return false;")) .CHtml::textField($data->invoice_id."_credit", ($data->amount - $data->paidSum - '.$this->childObj->credit.') > 0 ? '.$this->childObj->credit.' : round($data->amount - $data->paidSum,2) , array("class"=>"input ml10")). CHtml::hiddenField("finalAmount[$data->invoice_id]", round($data->amount - $data->paidSum,2))' : 'CHtml::hiddenField("finalAmount[$data->invoice_id]", round($data->amount - $data->paidSum,2))')
			),
            'voucher'=>array(
                'header'=>($usePartial) ? '' : (Yii::t("payment", "现金抵用卷")),
                'type'=>'raw',
                'value'=>($usePartial) ? '' : 'Invoice::showVoucher($data,$data->invoice_id) ? CHtml::link(Yii::t("payment","使用现金抵用卷"), array("//child/invoice/processVoucher", "id"=>$data->invoice_id), array("class"=>"btn J_dialog", "title"=>"使用现金抵用卷")) : ""'
            )
		),
	));

?>

	<?php if($dataProvider->getData()):$ss = $usePartial && $t == 'cash' ? 'none' : 'block';?>
		<div id="pay_summary" class="pay-summary mb10" style="display:<?php echo $ss?>">
			<?php
            echo Yii::t('payment',':selectedCount invoice(s) selected, total amount :amount.', array(
				':selectedCount' => CHtml::openTag('span',array('class'=>'num org')) . '0' . CHtml::closeTag('span'),
				':amount' => CHtml::openTag('span',array('class'=>'amount red')) . '0' . CHtml::closeTag('span'),
			));
			echo CHtml::hiddenField("invoiceCount", 0);
			echo CHtml::hiddenField("invoiceTotalAmount", 0);

            if($t == 'micropay'):
                echo CHtml::openTag('label', array('class'=>'ml20')). '扫描授权码';
                echo CHtml::textField('auth_code', '', array('class'=>'input ml10')).CHtml::closeTag('label');
            elseif (!$usePartial && $t == 'cash'):
                echo CHtml::openTag('label', array('class'=>'ml20')). '实际支付';
                echo CHtml::textField('amount', '', array('class'=>'input length_2 ml10')) . CHtml::closeTag('label');
                if($this->childObj->schoolid == $this->specialSchool){
                    echo CHtml::dropDownList('transactiontype', '', $payType, array('class'=>'ml10', 'empty'=>Yii::t('global', 'Please Select')));
                    if($this->payDateToggle == true){
                        $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                            "name"=>'paydate',
                            "options"=>array(
                                'changeMonth'=>true,
                                'changeYear'=>true,
                                'dateFormat'=>'yy-mm-dd',
                            ),
                            'htmlOptions'=>array('class'=>'input length_2 ml10', 'readonly'=>true, 'placeholder'=>Yii::t('payment', '付款日期')),
                        ));
                    }
                }
            endif;
			?>
		</div>
	
		<div class="btn_wrap_pd">
			<?php
            if ($t == 'pos'):
				echo CHtml::ajaxButton(Yii::t("payment", "生成POS订单"), array('//child/invoice/processYeePayOrderCreation'),
					array(
                        'type'=>'post',
						'beforeSend'=>'js:function(){return submitCheck()}',
						'dataType'=>'json',
                        'success'=>'js:function(data){if (data.state === "success"){showOrderID(data.data)}else{alert(data.message)}}',
						'complete'=>"js:function(){o=true;}"
					),
					array('class'=>"btn btn_submit") );
            elseif (!$usePartial && $t == 'cash'):
                echo CHtml::ajaxButton(Yii::t("payment", "现金付款"), array('//child/invoice/processCashPay'),
                    array(
                        'type'=>'post',
                        'beforeSend'=>'js:function(){return submitCheck()}',
                        'dataType'=>'json',
                        'success'=>'js:function(data){if (data.state === "success"){alert("支付成功");reloadPage(window);}else{alert(data.message)}}',
                        'complete'=>"js:function(){o=true;}"
                    ),
                    array('class'=>"btn btn_submit")
                );
            endif;
			?>
		</div>
	<?php endif;?>
	
<?php $this->endWidget(); ?>
</div><!-- form -->

    <?php if($t == 'micropay' && $items):?>
        <h3>异常记录</h3>
        <div class="table_list">
            <table class="items" width="100%">
            <?php foreach($items as $item):?>
                <tr>
                    <td><?php echo CHtml::link($item->orderid, array('showmicropay', 'id'=>$item->orderid), array('class'=>'J_dialog', 'title'=>'微信刷卡支付'))?></td>
                    <td><?php echo CommonUtils::formatDateTime($item->order_time, 'medium', 'medium')?></td>
                </tr>
            <?php endforeach;?>
            </table>
        </div>
    <?php endif;?>
<?php endif;?>