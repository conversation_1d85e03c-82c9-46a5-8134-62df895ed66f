<?php if ($selfWorkflow) : ?>
    <div class="panel panel-default">
        <div class="panel-heading" data-branchfilter="dailyBulletin"><span class="glyphicon glyphicon-bullhorn"></span> <?php echo Yii::t('workflow', 'My Requests'); ?></div>
        <div class="panel-body">
            <a href="<?php echo $this->createUrl('contract/index'); ?>" target="_blank" class="btn btn-primary btn-xs"><?php echo Yii::t('workflow', 'Contract List'); ?></a>
            <?php
            if ($selfWorkflow)
                $this->widget('ext.ivyCGridView.BsCGridView', array(
                    'id' => 'selfWorkflow',
                    'afterAjaxUpdate' => 'js:function(){head.Util.ajax($("#selfWorkflow"))}',
                    'dataProvider' => $selfWorkflow,
                    'pager' => array(
                        'class' => 'BsCLinkPager',
                        'maxButtonCount' => 6,
                        'pageSize' => 10,
                        'firstPageLabel' => '',
                        'lastPageLabel' => '',
                    ),
                    'colgroups' => array(
                        array(
                            "colwidth" => array(100),
                        )
                    ),
                    'columns' => array(
                        array(
                            'name' => '',
                            'value' => array($this, 'showWorkflow'),
                        ),
                    ),
                ));
            ?>
        </div>
    </div>
<?php endif; ?>

<style>
    #endWorkflow thead {
        display: none
    }


    li.first {
        display: none;
    }

    li.first {
        display: none;
    }

    .first a {
        display: none;
    }

    .last a {
        display: none;
    }
</style>