<?php

class ueditorfull extends CInputWidget {
    
    /**
	 * Editor language
	 * Supports: zh-cn  or en
	 */
	public $language = 'zh-cn';
    
    /**
	 * Editor toolbars
	 * Supports: 
	 */
	public $toolbars = '';
    
    /**
	 * Html options that will be assigned to the text area
	 */
	public $htmlOptions = array();
    
	/**
	 * Editor options that will be passed to the editor
	 */
	public $editorOptions = array();
    
	/**
	 * Debug mode
	 * Used to publish full js file instead of min version
	 */
    public $debug = false;
    
    /**
	 * Editor theme
     * Supports: default
	 */
	public $theme = 'default';

    public $configFile = '';

    /**
	 * Display editor
	 */
    public function run() {
	
		// Resolve name and id
		list($name, $id) = $this->resolveNameID();

		// Get assets dir
        $baseDir = dirname(__FILE__);
        $assets = Yii::app()->getAssetManager()->publish($baseDir.DIRECTORY_SEPARATOR.'ueditor_full', true, -1);

		// Publish required assets
		$cs = Yii::app()->getClientScript();

        if($this->configFile){
            $cs->registerScriptFile($assets.'/'.$this->configFile.'.js');
        }
        else{
            $cs->registerScriptFile($assets.'/ueditor.config.js');
        }

		$jsFile = $this->debug ? 'ueditor.all.js' : 'ueditor.all.min.js';
		$cs->registerScriptFile($assets.'/' . $jsFile);

        $this->htmlOptions['id'] = $id;

        if($this->toolbars){
            $this->editorOptions['toolbars'][] = $this->toolbars;
        }

		$options = CJSON::encode(array_merge(array('lang' => $this->language),$this->editorOptions));
		// Register js code
//		$cs->registerScript('Yii.'.get_class($this).'#'.$id, $js, CClientScript::POS_READY);
	
		// Do we have a model
		if($this->hasModel()) {
            $html = CHtml::activeTextArea($this->model, $this->attribute, $this->htmlOptions);
        } else {
            $html = CHtml::textArea($name, $this->value, $this->htmlOptions);
        }

		echo $html;

        $js =<<<EOP
        <script>
var ue{$id}=UE.getEditor('$id',$options);
</script>
EOP;
        echo $js;
    }
    
}