<?php

class ReadyCradController extends BranchBasedController
{

	public function createUrl($route, $params = array(), $ampersand = '&')
	{
		if (empty($params['branchId'])) {
			$params['branchId'] = $this->branchId;
		}
		return parent::createUrl($route, $params, $ampersand);
	}

	public $schoolsName = array();
    public $childsName = array();

	public function init(){
		parent::init();
		Yii::app()->theme = 'blue';
		//$this->layout = "//layouts/column1";
		Yii::import('common.models.identity.*');
		$this->modernMenuFlag = 'campusOp';
		$this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
		$this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

		$this->branchSelectParams['urlArray'] = array('//identity/readyCrad/index');

		// jquery ui
		$cs = Yii::app()->clientScript;
		$cs->registerCoreScript('jquery.ui');
		$cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
		$cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue2.js');
	}

	// 显示待制作卡列表
    public function actionIndex()
    {
        $critre = new CDbCriteria;
        $critre->compare('schoolid', $this->branchObj->abb);
        $critre->compare('sign', Cards::SIGN_WAIT);
        $cardsObj = new CActiveDataProvider('Cards', array(
            'criteria' => $critre,
            'sort' => array(
                /*'defaultOrder'=>array(
                    'status'=>CSort::SORT_DESC,
                    'updated'=>CSort::SORT_DESC,
                )*/
            ),
            'pagination'=>array(
                'pageSize'=>30,
            ),
        ));

        foreach($cardsObj->getData() as $child){
            $childId[$child->childid] = $child->childid;
        }

        if($childId){
            $childModel = ChildProfileBasic::model()->findAllByPk($childId);

            foreach($childModel as $child){
                $this->childsName[$child->childid] = $child->getChildName();
            }
        }

        $model = new Cards();

        $this->schoolsName = $this->getAllBranch();
        $this->render('index', array(
            'cardsObj' => $cardsObj,
            'model' => $model,
        ));
    }

    //编辑资料显示
    public function actionUpdateUser()
    {
        $cardid = intval(Yii::app()->request->getParam('cardid',0));
        if($cardid){
            $model = Cards::model()->findByPk($cardid);
            if (Yii::app()->request->isPostRequest){
                $model->uploadFile = CUploadedFile::getInstance($model, 'uploadFile');
                if($model->save()){
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message','success'));
                    $this->addMessage('callback', 'cbCrads');
                }else{
                    $this->addMessage('state', 'fail');
                    $errs = current($model->getErrors());
                    $this->addMessage('message', $errs[0]);
                }
                $this->showMessage();
            }

            $data = CJSON::decode($model->data);
            $model->child = $data['child'];
            $model->campus = $data['campus'];
            $model->class = $data['class'];
            $model->name = $data['name'];
            $model->tel = $data['tel'];
            $model->relation = $data['relation'];
            $model->expire = date("Y-m-d", strtotime($data['expire']));
            $model->showPhoto = Yii::app()->params['OAUploadBaseUrl'] . '/crads/' . $data['photo'] . "?" . time();;
            $this->renderPartial('_update', array(
                'model' => $model,
            ));
        }
    }

    public function actionCreate()
    {
        $data = array();
        if (Yii::app()->request->isPostRequest){
            if(empty($_POST['Cards']['childid'])){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', "添加孩子不能为空");
                $this->showMessage();
            }
            if(empty($_POST['Cards']['name'])){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', "家长姓名不能为空");
                $this->showMessage();
            }

            if(empty($_POST['Cards']['tel'])){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', "家长电话不能为空");
                $this->showMessage();
            }

            if(!preg_match("/^1[34578]{1}\d{9}$/",$_POST['Cards']['tel'])){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', "请输入正确的电话格式");
                $this->showMessage();
            }

            if(empty($_POST['Cards']['relation'])){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', "家长关系不能为空");
                $this->showMessage();
            }

            $childObj = ChildProfileBasic::model()->findByPk($_POST['Cards']['childid']);
            $data = array(
                'child' => $childObj->getChildName(),
                'campus' => $this->branchObj->abb,
                'class' => $childObj->ivyclass->title,
                'name' => $_POST['Cards']['name'],
                'tel' => $_POST['Cards']['tel'],
                'relation' => $_POST['Cards']['relation'],
                'expire' => "",
            );
            $model = new Cards();
            $model->childid = $_POST['Cards']['childid'];
            $model->data = json_encode($data);
            $model->sign = 4;
            $model->updated = time();
            $model->schoolid = $this->branchObj->abb;
            if($model->save()){
                $model->uploadFile = CUploadedFile::getInstance($model, 'uploadFile');
                if($model->save()) {
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', '增加成功！');
                    $this->addMessage('callback', "cbCrads");
                }
            }else{
                $error = current($model->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', $error[0]));
            }
            $this->showMessage();

        }
    }

    //制卡
    public function actionSystemCard()
    {
        $cardid = Yii::app()->request->getParam('id', array());
        $cradData = array();
        if($cardid){
            $cradObj = Cards::model()->findAllByPk($cardid);
            foreach($cradObj as $item){
                $dataA = CJSON::decode($item->data);
                $cradData[$item->id] = array(
                    'childid' => $item->childid,
                    'childName' => $dataA['child'],
                    'campus' => $dataA['campus'],
                    'class' => $dataA['class'],
                    'name' => $dataA['name'],
                    'tel' => substr_replace($dataA['tel'],'****',3,4),
                    'relation' => $dataA['relation'],
                    'photo' => $item->id
                );
            }

            $this->addMessage('state','success');
            $this->addMessage('message','获取数据成功！');
            $this->addMessage('data',$cradData);
            $this->showMessage();
        }else{
            $this->addMessage('state','fail');
            $this->addMessage('message','至少勾选一条数据');
            $this->addMessage('data',$cradData);
            $this->showMessage();
        }
    }


    public function getButton($data)
    {
        echo CHtml::link(Yii::t('global', 'Edit'), array('updateUser', 'cardid' => $data->id, "branchId"=>Yii::app()->controller->branchId), array('class' => 'J_modal btn btn-xs btn-info')) . ' ';
    }

    public function getChildName($data)
    {
        echo $this->childsName[$data->childid];
    }

    public function getParentName($data)
    {
        $parentName = CJSON::decode($data->data);
        echo $parentName['name'];
    }

    public function getParentTel($data)
    {
        $parentName = CJSON::decode($data->data);
        echo $parentName['tel'];
    }

    public function getParentRelation($data)
    {
        $parentName = CJSON::decode($data->data);
        echo $parentName['relation'];
    }

    public function getSchoolName($data)
    {
        $schoolName = $this->schoolsName[$data->schoolid];
        echo $schoolName['title'];
    }

    public function getSign($data)
    {
        $array = Cards::getConfig();
        echo $array[$data->sign];
    }

    public function getPhoto($data)
    {
        $info = CJSON::decode($data->data);
        $src = Yii::app()->params['OAUploadBaseUrl'] . '/crads/' . $info['photo'] . "?" . time();
        echo '<img style="width:50px;" src="' .$src. '">';
    }
}