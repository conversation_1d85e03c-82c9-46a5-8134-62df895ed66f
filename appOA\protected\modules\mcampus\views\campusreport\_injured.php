<div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h4 class="modal-title" id="myModalLabel"><?php if($idt): ?>修改孩子受伤报告<?php else: ?> 增加孩子受伤报告<?php endif; ?></h4>
      </div>
       <div class="modal-body">
        <?php $form=$this->beginWidget('CActiveForm', array(
			'id'=>'injured',
			'enableAjaxValidation'=>false,
			'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
		)); ?>			  
			<div class="form-group">
				<label for="inputEmail3" class="col-sm-2 control-label">班级</label>
				<div class="col-sm-5">
					<?php echo $form->dropDownList($model,'classid',$classname,array('empty'=>Yii::t("global", 'Please Select'),'id' => 'task', 'readonly'=>'readonly')); ?>
					<?php echo CHtml::hiddenField('weeknumber',$weeknumber);; ?>
					<?php echo CHtml::hiddenField('yid',$yid);; ?>
				</div>
			</div>
			<div class="form-group">
				<label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'childid'); ?></label>
					<div class="col-xs-8">
						<?php echo $form->dropDownList($model,'childid',array('empty'=>Yii::t("global", 'Please Select'), 'Enabled'=>"false")); ?>
					</div>
			</div>
			
				<div class="form-group">
					<label for="inputEmail3" class="col-sm-2 control-label"><?php echo $form->labelEx($model,'parentback'); ?></label>
						<div class="col-sm-5">
							<?php echo CHtml::hiddenField('id',$idt);; ?>
							<?php echo $form->textArea($model,'parentback',array('maxlength'=>255,'class'=>'form-control')); ?>
						</div>
				</div>
				<div class="form-group">
					<label for="inputEmail3" class="col-sm-2 control-label"><?php echo $form->labelEx($model,'injuryreport'); ?></label>
						<div class="col-sm-5">
							<?php echo $form->textArea($model,'injuryreport',array('maxlength'=>255,'class'=>'form-control')); ?>
						</div>
					</div>
				
					<div class="modal-footer">
						<button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?>
						<button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
					</div>
				</div>
			</div>
	  <?php $this->endWidget(); ?>

<script>
	<?php if($id): ?>
	  $('#myModal').modal('show');
	<?php endif; ?>
</script>
<script>
$(function() {
	$("#task").change(function(){
		getclasschild()
	});
});

function getclasschild(){
		$.get('<?php echo $this->createUrl('getShops')?>', {
			'classid': $('#task').val()
		}, function(htmlcontent) {
			$('#CrInjuriesReport_childid').html(htmlcontent)
			.find('option[value=<?php echo $model->childid?>]')
			.selected()
		});
}
<?php if(!$model->isNewRecord): ?>
		getclasschild()
<?php endif;?>

</script>



