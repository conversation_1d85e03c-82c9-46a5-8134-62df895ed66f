<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
    <h4 class="modal-title"><?php echo $modalTitle;?></h4>
</div>
<?php echo CHtml::form($this->createUrl('linkChild'), 'post', array('class'=>'J_ajaxForm')); ?>
<div class="modal-body">
    <div class="form-group">
        <?php echo CHtml::hiddenField('id', $basicId); ?>
        <label for="searchChild">查询孩子</label>
        <?php 
            $this->widget('ext.search.ChildSearchBox', array(
                'acInputCSS' => 'form-control',
                'allowMultiple' => false,
                'allowMultipleSchool' => false,
                'simpleDisplay' => true,
                'extendCss' => false,
                'useModel' => false,
                'withAlumni' => true,
                'name' => 'linkChild',
                'htmlOptions' => array('class'=>'form-control'),
                'select'=>$select,
                'data'=>$tdata,
            ));
        ?>
    </div>
</div>
<div class="modal-footer">
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
    <button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
</div>
    <?php echo CHtml::endForm(); ?>
<script>
    $(function () {
        // 搜索框
        jQuery('#searchChild').autocomplete({'minLength':'1','delay':1000,'position':{'my':'left top','at':'left bottom','collision':'none'},'source':'/backend/search/childoutput?displayType=simpleDisplay&withAlumni=0&allowMultipleSchool=0'});
        jQuery('#searchChild').data( 'autocomplete' )._renderItem = function( ul, item ) {
                return $( '<li onclick=\'mySelect(this);return false;\'></li>' )
                    .attr('uid',item.value)
                    .data( 'item.autocomplete', item )
                    .append( item.label)
                    .appendTo( ul );            
        };
        $("#searchChild").keydown(function(event){ 
            if(event.which == 13){
                return false;
            }
        });
    });
    function mySelect(elem){
        var v=$(elem).attr('uid');
        jQuery('#searchChild').data('autocomplete').close();
        $('#searchChild').val("");
        if(v>0)
        $('#linkChild').append('<option value="'+v+'" selected="selected">'+$(elem).html()+'</option>');
    }
    // 回调：添加成功
    function cbVisit() {
        $('#modal').modal('hide');
        $.fn.yiiGridView.update('confirmed-visit-grid');
    }
</script>