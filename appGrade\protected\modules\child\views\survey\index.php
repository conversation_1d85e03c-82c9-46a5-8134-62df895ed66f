<?php
Mims::LoadHelper('HtoolKits');
$this->breadcrumbs = array(
    Yii::t("navigations", "Survey") . ' - ' .
    HtoolKits::getContentByLang($survey_obj->title_cn, $survey_obj->title_en),
);
?>
<style type="text/css">
    div.survey_wrapper {
        margin: 0 20px;
        width: 630px;
        background: #F2F2F2;
        font-family: "Microsoft Yahei", Tahoma;
    }

    .survey_wrapper .summary {
        background: #DCCCAA;
        color: #48110C;
        padding: 10px;
    }

    .survey_wrapper .summary p {
        margin-bottom: 0.6em;
        font-size: 14px;
    }

    .survey_wrapper .summary p span {
        color: #E15119;
    }

    .survey_wrapper .summary h3 {
        line-height: 30px;
        font-size: 16px;
        margin: 0 !important;
        padding: 0 0 10px;
        height: 30px;
    }

    .survey_wrapper .summary .tips {
        font-weight: normal;
        color: #f2f2f2;
        font-size: 14px;
    }

    .survey_wrapper .summary .tips span {
        font-size: 12px;
        text-decoration: underline;
    }

    .cn {
        font-family: "Microsoft Yahei";
    }

    .en {
        font-family: 'Trebuchet MS';
    }
    /*
             * 方形 按钮
    */
    .sq-btn a span {
        display: inline-block;
        padding: 6px 8px;
        background: none repeat scroll 0 0 #59C30A;
        color: #FFFFFF;
    }

    .sq-btn a {
        margin-left: 20px;
    }

    .sq-btn a:hover {
        text-decoration: none;
    }

    .sq-btn a:hover span {
        background: none repeat scroll 0 0 #059B01;
    }

    .actions li {
        float: right;
        margin: 20px;
        margin-right: 60px;
    }
</style>

<div class="votebox">
    <h4 class="survey">
        <span><b><?php echo Yii::t("survey", 'Parent Survey'); ?> </b> </span><span
            class="alignRight"><b></b> </span>
    </h4>

    <div class="voteinfo">
        <div class="desc">
            <?php if ($survey_obj->survey_type == Survey::SURVEY_TYPE_ONE){
                if($survey_obj->id == 58){
                    $this->renderPartial(Yii::t("survey", '_en_us_message_entrepreneurship'),
                        array("endTime" => $survey_obj->surveyDetail->end_time,'param'=>$param));
                }
                else{
                    $this->renderPartial(Yii::t("survey", '_en_us_message'), array("endTime" => $survey_obj->surveyDetail->end_time,'param'=>$param));
                }
             }else if($survey_obj->survey_type == Survey::SURVEY_TYPE_THIRD){
                $this->renderPartial(Yii::t("survey", '_en_us_third_message'), array("endTime" => $survey_obj->surveyDetail->end_time,'param'=>$param));
             }else{
                 $this->renderPartial(Yii::t("survey", '_en_us_return_message'), array("endTime" => $survey_obj->surveyDetail->end_time,'param'=>$param));
             }?>
        </div>
        <?php
        $tk = new HtoolKits();
        ?>

        <div class="poll pd10 prebg">
            <h5 class="vtitle">
                <a href="javascript:;">
                    <?php echo $tk->getContentByLang($survey_obj->title_cn, $survey_obj->title_en); ?>
                </a>
            </h5>
            <?php
            $this->widget('ext.survey.TopicListWidget', array(
                'surveyId' => $survey_id,
                'topicDiglossia' => false,
                'allowFeedback' => true,
                'scriptFile' => true,
                'view' => 'topicListModelView',
                    //'normalizeUrl'=>CHtml::normalizeUrl($this->createUrl('/child/survey/feedbackModel')),
            ));
            ?>
        </div>
    </div>
</div>
