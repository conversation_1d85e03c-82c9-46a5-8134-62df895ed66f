<?php
$this->breadcrumbs = array(
    'Portfolio' => array('/portfolio/portfolio/classes'),
    Yii::t("navigations", 'All Feedback & Replies'),
);
?>

<div id="left-col" class="no-bg span-6">
    <ul class="sub-nav" id="subnav">

        <?php
        if (!empty($calendarStartYear)) {
            foreach ($calendarStartYear as $year) {
                ?>
                <li <?php if ($year == $startYear) { ?> class="active" <?php } ?>><a
                        href="<?php echo $this->createUrl('/portfolio/portfolio/journal', array('type' => 'feedback', 'startyear' => $year)); ?>">
                            <?php echo IvyClass::formatSchoolYear($year) ?> <?php echo Yii::t("portfolio", 'School Year') ?>
                    </a></li>

                <?php
            }
        }
        ?>
    </ul>
</div>
<?php
$this->widget('application.extensions.dit.Dit', array(
    'bId' => 'subnav',
    'cId' => 'mainbox',
        )
);
?>

<div id="main-col" class="span-18 last">

    <h1>
        <label><?php echo Yii::t("portfolio", 'Journal Feedback & Response'); ?>
        </label>
    </h1>

    <ul class="yearly-feedback-list">
        <?php
        if (!empty($comment_list)) {
            foreach ($comment_list as $comment) :
                ?>
                <li id="feedback_info_<?php echo $comment->id; ?>"><?php if ($comment->showWeekInfo) { ?>
                        <div class="week-number">
                            <h5>
                                <?php
                                echo Yii::t("portfolio", "Week :weeknumber", array(':weeknumber' => $comment->com_week_num));
                                ?>
                            </h5>
                            <h6>
                                <?php
                                $sy = $classes[$comment->com_class_id]->calendarInfo->startyear;
                                echo sprintf("%d-%d", $sy, $sy + 1);
                                ?>
                            </h6>
                        </div>
                        <h4 class="class-info">

                            <?php
                            echo CHtml::link($week_time_slot[$comment->com_yid][$comment->com_week_num], $this->createUrl(
                                            '/portfolio/portfolio/journal', array(
                                        'classid' => $comment->com_class_id,
                                        'weeknum' => $comment->com_week_num
                                            )
                                    ), array('target' => '_blank',
                                'title' => Yii::t("portfolio", "Go to journal of this week.")));
                            ?>

                            <span>
                                <?php echo $classes[$comment->com_class_id]->title; ?>
                            </span>
                        </h4> <?php } ?>
                    <dl>
                        <dt>
                        <?php echo CHtml::image($comment->userPhoto, $comment->userName, array("class" => "face")) ?>
                        </dt>
                        <dd>
                            <h5>
                                <?php echo $comment->userName; ?>
                                <?php echo addColon(Yii::t("portfolio", "said")); ?>
                            </h5>
                            <p>
                                <?php echo Yii::app()->format->ntext($comment->com_content); ?>
                                <span><?php echo Mims::formatDateTime($comment->com_created_time, 'medium', 'short'); ?> </span>
                            </p>
                        </dd>
                    </dl>
                    <?php
                    if (isset($comment->replies)) {
                        $comment_model = new Comments();
                        foreach ($comment->replies as $reply) {
                            ?>
                            <dl class="indent fb">
                                <dt>
                                <?php
                                $staff_photo = Mims::CreateUploadUrl('infopub/staff/blank.gif');
                                if (isset($reply->staffInfo->staff_photo) && $reply->staffInfo->staff_photo != "") {
                                    $staff_photo = Mims::CreateUploadUrl('infopub/staff/' . $reply->staffInfo->staff_photo);
                                }
                                if ($reply->com_user_type != 1) {
                                    $staff_photo = Mims::CreateUploadUrl('users/thumbs/' . $reply->userWithProfile->user_avatar);
                                }
                                echo CHtml::image($staff_photo, $reply->userWithProfile->getName(), array("class" => "face"));
                                ?>
                                </dt>
                                <dd>
                                    <h5>
                                        <?php echo $reply->userWithProfile->getName(); ?>
                                        <?php echo addColon(Yii::t("portfolio", "said")); ?>
                                    </h5>
                                    <p>
                                        <?php echo Yii::app()->format->ntext($reply->com_content); ?>
                                        <span><?php echo Mims::formatDateTime($reply->com_created_time, 'medium', 'short'); ?> </span>
                                    </p>
                                </dd>
                            </dl>
                            <?php
                        }
                        ?>

                        <?php if (Yii::app()->user->checkAccess('adminChild', array("childid" => $this->getChildId()))): ?>
                            <dl class="indent">
                                <div id = "cinput_<?php echo $comment->id; ?>" class = "textinput" onclick = "showArea(<?php echo $comment->id; ?>);"><?php echo Yii::t("portfolio", 'Make a comment')
                            ?></div>
                                <div id="carea_<?php echo $comment->id; ?>" style="display: none;">
                                    <?php
                                    $form = $this->beginWidget('CActiveForm', array(
                                        'id' => "reply_form_" . $comment->id,
                                        'enableAjaxValidation' => false,
                                            ));
                                    ?>
                                    <?php echo $form->textArea($comment_model, 'com_content', array('class' => 'reply-com', 'onblur' => 'showInput(' . $comment->id . ')')); ?>
                                    <?php echo $form->hiddenField($comment_model, 'com_class_id', array('value' => $comment->com_class_id)); ?>
                                    <?php echo $form->hiddenField($comment_model, 'com_week_num', array('value' => $comment->com_week_num)); ?>
                                    <?php echo $form->hiddenField($comment_model, 'com_root_id', array('value' => $comment->id)); ?>
                                    <div class="rbtn">
                                        <?php
                                        echo CHtml::ajaxSubmitButton(Yii::t("portfolio", 'Submit'), $this->createUrl("//portfolio/portfolio/savefeedback"), array('type' => 'post', 'dataType' => 'json', 'data' => "js:$('#reply_form_" . $comment->id . "').serialize()", 'success' => "replenish"));
                                        ?>

                                    </div>
                                    <?php $this->endWidget(); ?>
                                </div>

                            </dl>
                            <?php
                        endif;
                    }
                    ?>
                    <div class="clear"></div>
                </li>
                <?php
            endforeach;


            //将提示数置零
            if (Yii::app()->user->checkAccess('sendFeedback', array("childid" => $this->getChildId()))) {
                echo CHtml::image($this->createUrl('//portfolio/portfolio/zerorizeReminder', array('startYear' => $startYear)), "", array("class" => "hidden"));
            }
        } else {
            ?>

            <li>
                <div  class=flash-warning>
                    <?php echo Yii::t("portfolio", "Current School Year No Feedback & Response!"); ?>
                </div>
            </li>



        <?php } ?>
    </ul>

</div>

<div class="clear"></div>
<script type="text/javascript">
    function showArea(com_id)
    {
        $('#cinput_'+com_id).hide();
        $('#carea_'+com_id).show();
        $('#carea_'+com_id+' textarea').focus();
    }
    function showInput(com_id)
    {
        if ($('#carea_'+com_id+' textarea').val() == ''){
            $('#carea_'+com_id).hide();
            $('#cinput_'+com_id).show();
        }
    }

    function replenish(data, textStatus, jqXHR){
        var l = $('#feedback_info_'+data.com_id+' dl.fb').length;
        if(l> 0){
            $('#feedback_info_'+data.com_id+' dl.fb:last').after(data.fb_info);
        }else{
            $('#feedback_info_'+data.com_id+' dl:first').after(data.fb_info);
        }
        $('#reply_form_'+data.com_id+' textarea').val('');
        showInput(data.com_id);
    }
</script>
<style>
    img.face {
        border: 2px solid white;
        box-shadow: 2px 2px 3px #666;
        margin: 0 20px 10px 0;
    }

    .yearly-feedback-list li {
        position: relative;
    }

    .yearly-feedback-list li .week-number{
        position: absolute;
        width: 66px;
        top: 10px;
    }

    .yearly-feedback-list li .week-number h5{
        text-align: center;
        background-color: rgb(255, 0, 145);
        background-color: rgba(255, 0, 145, 0.5);
        color:#fff;
        line-height: 50px;
        height: 50px;
    }
    .yearly-feedback-list li .week-number h6{
        font-size: 10px;
        color:#999;
        font-weight: normal;
        text-align: center;
    }
    .yearly-feedback-list h4.class-info{
        margin-left: 10px;
        padding-left: 70px;
        font-size: 12px;
        line-height: 28px;
        background:#d5d5d5;
        border-bottom: 1px solid #c1c1c1;
        padding-right: 6px;
    }
    .yearly-feedback-list h4.class-info span{
        float: right;
    }
    .yearly-feedback-list li dl {
        clear: both;
        margin-left: 80px;
        min-height: 70px;
        padding-top: 0.4em;
        margin-bottom: 0.5em;
    }

    .yearly-feedback-list li dl.indent {
        margin-left: 120px;
        min-height: 60px;
        border-top: 2px dotted #d5d5d5;
    }

    .yearly-feedback-list li dl.indent dt img.face {
        height: 50px;
    }

    .yearly-feedback-list li dd h5{
        font-weight: normal;
    }
    .yearly-feedback-list li dt {
        padding-right: 0px;
        float: left;
    }

    .yearly-feedback-list li dd p span{
        font-size: 10px;
        color:#999;
        font-style: italic;
    }
    .yearly-feedback-list li dt img.face {
        height: 64px;
    }

    /**
    *   补充一句相关的样式
    */
    .textinput {
        background-color: #FFFFFF;
        border: 1px solid #EAE2C6;
        color: #A8A8A8;
        cursor: text;
        line-height: 20px;
        min-height: 21px;
        padding-left: 5px;
        width: 586px;
    }
    .reply-com {
        border: 1px solid #d5d5d5;
        font-size: 12px;
        height: 40px;
        width: 586px;
    }
    .rbtn {
        text-align: right;
        margin: 5px;
    }
</style>
