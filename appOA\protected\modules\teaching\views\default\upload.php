<?php
$hasError = (empty($mediaServer) || empty($options['notify-url']));

$policy = base64_encode(json_encode($options));
$time = time(); 
$sign = md5($policy.'&'.$mediaServer['securityKey'] . '&' . $time); /// 表单 API 功能的密匙（请访问又拍云管理后台的空间管理页面获取）

if($hasError){ ?>
	<div class="red p20">
		<?php echo Yii::t('teaching','Media Server or Notify Url missing.');?>
	</div>
<?php
}else{

?>

    <script type="text/javascript">
    
		var swfu;

		window.onload = function() {
			var settings = {
				flash_url : "<?php echo Yii::app()->theme->baseUrl;?>/swfupload/swfupload.swf",
				flash9_url : "<?php echo Yii::app()->theme->baseUrl;?>/swfupload/swfupload_FP9.swf",
                upload_url: "<?php echo $mediaServer['uploadUrl'];?>",
				post_params: {"policy": "<?php echo $policy;?>","signature":"<?php echo $sign?>","time":"<?php echo $time;?>"},

                file_post_name: "file",
				file_size_limit : "100 MB",
				file_types : "*.*",
				file_types_description : "All Files",
				file_upload_limit : 100,
				file_queue_limit : 0,
				custom_settings : {
					progressTarget : "fsUploadProgress",
					cancelButtonId : "btnCancel"
				},
				//debug: true,

				// Button settings
				button_image_url: "<?php echo Yii::app()->theme->baseUrl;?>/images/swfupload_btn.png?",
				button_width: 160,
				button_height: 27,
				button_placeholder_id: "spanButtonPlaceHolder",
				button_action : SWFUpload.BUTTON_ACTION.SELECT_FILES,
				button_cursor : SWFUpload.CURSOR.ARROW,
				
				// The event handler functions are defined in handlers.js
				swfupload_preload_handler : preLoad,
				swfupload_load_failed_handler : loadFailed,
				file_queued_handler : fileQueued,
				file_queue_error_handler : fileQueueError,
				file_dialog_complete_handler : fileDialogComplete,
				upload_start_handler : uploadStart,
				upload_progress_handler : uploadProgress,
				upload_error_handler : uploadError,
				upload_success_handler : uploadSuccess,
				upload_complete_handler : uploadComplete,
				queue_complete_handler : queueComplete	// Queue plugin event
			};

			swfu = new SWFUpload(settings);
	     };        
        
		function item_rotate(obj){
			var item = $(obj).parents('ul#media-list li');
			$.ajax({
				url: "<?php echo $this->createUrl('/IvyMediaResponder/modify');?>",
				dataType: 'json',
				type: 'POST',
				data:{op:'rotate',mkey:item.attr("mkey"), sid:item.attr("sid"), securityCode: item.attr("securityCode")}
			}).done(function(data){
				if(data.status == 'success'){
					if(data.op == 'rotate'){
						var img = item.children('img');
						if ( img.attr("src").indexOf("?") < 0){
							img.attr("src", img.attr("src") + "?t=" + new Date().getTime() );
						}else{
							img.attr("src", img.attr("src") + "&t=" + new Date().getTime() );
						}
					}
				}else{
					alert(data.message);
					return false;
				}
			});
		}
		
		function item_delete(obj){
			var proceed = confirm('<?php echo Yii::t("teaching", "Sure to delete?");?>');
			if(proceed){
				var item = $(obj).parents('ul#media-list li');
				$.ajax({
					url: "<?php echo $this->createUrl('/IvyMediaResponder/modify');?>",
					dataType: 'json',
					type: 'POST',
					data:{op:'delete',mkey:item.attr("mkey"), sid:item.attr("sid"), securityCode: item.attr("securityCode")}
				}).done(function(data){
					if(data.status == 'success'){
						if(data.op == 'delete'){
							item.hide(400,function(){item.remove();});
						}
					}else{
						alert(data.message);
						return false;
					}
				});			
			}
		}
		
	</script>

	<div class="h_a"><?php echo Yii::t('teaching', 'Media Upload Utils');?></div>
	<div class="prompt_text">
		<ul>
			<li><?php echo Yii::t('teaching', 'Only image and flv files will be processed by Ivy Media Server.');?></li>
		</ul>
	</div>
	
	<div class="table_full">
		<table width="100%">
			<colgroup>
				<col width=400></col>
			</colgroup>
			<tbody>
				<tr>
					<th>
						<form id="flash-upload-form" action="" method="post" enctype="multipart/form-data">
							<div class="p20">
								<span id="spanButtonPlaceHolder"></span>
								<input id="btnCancel" type="button" value="Cancel All Uploads" onclick="swfu.cancelQueue();" disabled="disabled" style="margin-left: 2px; font-size: 12px; height: 29px;" />
							</div>
							
							<div id="divStatus">0 Files Uploaded</div>
							
							<div>Upload Queue</div>
							<div class="fieldset flash" id="fsUploadProgress">
							</div>
						</form>				
					</th>
					<td>
						<div id="upload-preview">
							<ul id="media-list"></ul>
						</div>
					</td>
				</tr>
			</tbody>
		</table>
	</div>

<?php } ?>


<style>
#media-list li{
	float: left;
	width: 120px;
	height: 120px;
}
#media-list li img.preview{
	width: 100px;
}
#media-list li:hover div.itemOp{
	visibility: visible;
}
#media-list li div.itemOp{
	visibility: hidden;
}
</style>


<div id="itemOp-template" style="display: none;">
	<a href="javascript:void(0);" class="icon-rotate" title="<?php echo Yii::t('global', 'Rotate');?>" onclick="item_rotate(this);">R</a>
	<a href="javascript:void(0);" class="icon-delete" title="<?php echo Yii::t('global', 'Delete');?>" onclick="item_delete(this);">D</a>
</div>