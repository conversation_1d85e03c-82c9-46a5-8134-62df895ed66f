<div id='container' v-cloak>
    <?php if ($this->checkRole()): ?>
    <div>
        <button type="button" class="btn btn-primary" @click='setLeave'>假期配额管理</button>
    </div>
    <?php endif; ?>
    <div class='flex mt12'>
        <div class='flex1'>
            <el-select v-model="startYear" size="small" placeholder="请选择"  @change='getLeave'>
                <el-option
                v-for="(item,index) in yearList"
                :key="item.key"
                :label="item.title"
                :value="item.key">
                </el-option>
            </el-select>
            <?php if ($this->checkRole()): ?>
            <el-select v-model="schoolId" size="small" placeholder="请选择" @change='getGroup()'>
                <el-option
                v-for="(item,index) in schoolList"
                :key="item.branchid"
                :label="item.title"
                :value="item.branchid">
                </el-option>
            </el-select>
            <?php endif; ?>
            <el-select v-model="user_ids" size="small" placeholder="请选择" @change='getLeave'>
                <el-option
                v-for="(item,index) in groupData.list"
                :key="index"
                :label="item.dep_name"
                :value="item.user_ids">
                </el-option>
            </el-select>
            <el-select v-model="month" size="small" placeholder="请选择" @change='getLeave'>
                <el-option
                v-for="(item,index) in monthList"
                :key="item.value"
                :label="item.value"
                :value="item.key">
                </el-option>
            </el-select>
        </div>
        <div>
            <el-input v-model="searchText" size="small" style='width:200px' placeholder="搜索"></el-input>
            <el-button  size="small" @click='exportTab'>导出</el-button>
        </div>
    </div>
    <div class='mt20' v-if='showTable'>
        <el-table
            :data="tableList"
            :max-height="modalHeight"
            style="width: 100%">
            <el-table-column
            prop="date"
            label="员工"
            width='200'
            fixed
            >
            <template slot="header" slot-scope="scope">
                员工（{{tableList.length}}人）
            </template>
            <template slot-scope="scope">
                <div class='flex'>
                    <img :src="scope.row.photoUrl" class='img42 img-circle' alt="">
                    <div class='flex1 flexText ml10'>
                        <div class='font14 color3 word-break'>{{scope.row.staff_name}}</div>
                        <div class='font12 color6 word-break'>{{scope.row.hrPosition}}</div>
                    </div>
                </div>
            </template>
            </el-table-column>
            <el-table-column label="年假（天）">
                <el-table-column
                prop="nianjia"
                label="年假本月已休"
            >
                    <template slot-scope="scope">
                        <el-popover
                            v-if='scope.row.nianjia!=0'
                            placement="bottom"
                            width="400"
                            trigger="click"
                            >
                            <div v-if='popoverData.length!=0'>
                                <div class='font14 color3'>累计已休</div>
                                <div class='popoverList flex' v-for='(list,index) in popoverData'>
                                    <div class='flex1'>
                                        <div class='font14 color3'><span v-for='(item,i) in list.days'>{{item}} <span v-if='i+1<list.days.length'>、</span></span></div>
                                        <div class='font12 color6 mt8'>{{list.total}}</div>
                                    </div>
                                    <div class='el-icon-arrow-right'></div>
                                </div>
                            </div>
                            <div class='text-primary cur-p' @click='getDetail(scope.row,"nianjia")'  slot="reference">{{scope.row.nianjia}}</div>
                        </el-popover>
                        <span v-else>{{scope.row.nianjia}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                prop="nianjia_year"
                label="当前学年可休年假"
            >
                    <template slot-scope="scope">
                    <span>{{scope.row.nianjia_year}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                prop="nianjia_last"
                label="以往学年顺延年假"
            >
                    <template slot-scope="scope">
                    <span>{{scope.row.nianjia_last}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                prop="nianjia_to_last"
                label="累计至上月已休年假"
            >
                    <template slot-scope="scope">
                    <span>{{scope.row.nianjia_to_last}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="nianjia_total"
                    label="当年累计已休年假"
                >
                    <template slot-scope="scope">
                        <el-popover
                            v-if='scope.row.nianjia_total!=0'
                            placement="bottom"
                            width="400"
                            trigger="click"
                            >
                            <div v-if='popoverData.length!=0'>
                                <div class='font14 color3'>累计已休</div>
                                <div class='popoverList flex' v-for='(list,index) in popoverData'>
                                    <div class='flex1'>
                                        <div class='font14 color3'><span v-for='(item,i) in list.days'>{{item}} <span v-if='i+1<list.days.length'>、</span></span></div>
                                        <div class='font12 color6 mt8'>{{list.total}}</div>
                                    </div>
                                    <div class='el-icon-arrow-right'></div>
                                </div>
                            </div>
                            <div class='text-primary cur-p' @click='getDetail(scope.row,"nianjia_total")'  slot="reference">{{scope.row.nianjia_total}}</div>
                        </el-popover>
                        <span v-else>{{scope.row.nianjia_total}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                prop="nianjia_balance"
                label="当前学年剩余年假"
            >
                    <template slot-scope="scope">
                    <span>{{scope.row.nianjia_balance}}</span>
                    </template>
                </el-table-column>
                
            </el-table-column>
            <el-table-column label="病假（天）">
                <el-table-column
                prop="bingjia"
                label="病假本月已休"
            >
                    <template slot-scope="scope">
                        <el-popover
                            v-if='scope.row.bingjia!=0'
                            placement="bottom"
                            width="400"
                            trigger="click"
                            >
                            <div v-if='popoverData.length!=0'>
                                <div class='font14 color3'>累计已休</div>
                                <div class='popoverList flex' v-for='(list,index) in popoverData'>
                                    <div class='flex1'>
                                        <div class='font14 color3'><span v-for='(item,i) in list.days'>{{item}} <span v-if='i+1<list.days.length'>、</span></span></div>
                                        <div class='font12 color6 mt8'>{{list.total}}</div>
                                    </div>
                                    <div class='el-icon-arrow-right'></div>
                                </div>
                            </div>
                            <div class='text-primary cur-p' @click='getDetail(scope.row,"bingjia")'  slot="reference">{{scope.row.bingjia}}</div>
                        </el-popover>
                        <span v-else>{{scope.row.bingjia}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                prop="bingjia_year"
                label="当前学年可休病假"
            >
                    <template slot-scope="scope">
                    <span>{{scope.row.bingjia_year}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                prop="bingjia_to_last"
                label="累计至上月已休病假"
            >
                    <template slot-scope="scope">
                    <span>{{scope.row.bingjia_to_last}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                prop="bingjia_total"
                label="病假累计已休"
            >
                    <template slot-scope="scope">
                        <el-popover
                            v-if='scope.row.bingjia_total!=0'
                            placement="bottom"
                            width="400"
                            trigger="click"
                            >
                            <div v-if='popoverData.length!=0'>
                                <div class='font14 color3'>累计已休</div>
                                <div class='popoverList flex' v-for='(list,index) in popoverData'>
                                    <div class='flex1'>
                                        <div class='font14 color3'><span v-for='(item,i) in list.days'>{{item}} <span v-if='i+1<list.days.length'>、</span></span></div>
                                        <div class='font12 color6 mt8'>{{list.total}}</div>
                                    </div>
                                    <div class='el-icon-arrow-right'></div>
                                </div>
                            </div>
                            <div class='text-primary cur-p' @click='getDetail(scope.row,"bingjia_total")'  slot="reference">{{scope.row.bingjia_total}}</div>
                        </el-popover>
                        <span v-else>{{scope.row.bingjia_total}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                prop="bingjia_balance"
                label="当前学年剩余病假"
            >
                    <template slot-scope="scope">
                        <span>{{scope.row.bingjia_balance}}</span>
                    </template>
                </el-table-column>
            </el-table-column>
            <el-table-column label="事假（天）">
                <el-table-column
                prop="shijia"
                label="事假本月已休"
            >
                    <template slot-scope="scope">
                        <el-popover
                            v-if='scope.row.shijia!=0'
                            placement="bottom"
                            width="400"
                            trigger="click"
                            >
                            <div v-if='popoverData.length!=0'>
                                <div class='font14 color3'>累计已休</div>
                                <div class='popoverList flex' v-for='(list,index) in popoverData'>
                                    <div class='flex1'>
                                        <div class='font14 color3'><span v-for='(item,i) in list.days'>{{item}} <span v-if='i+1<list.days.length'>、</span></span></div>
                                        <div class='font12 color6 mt8'>{{list.total}}</div>
                                    </div>
                                    <div class='el-icon-arrow-right'></div>
                                </div>
                            </div>
                            <div class='text-primary cur-p' @click='getDetail(scope.row,"shijia")'  slot="reference">{{scope.row.shijia}}</div>
                        </el-popover>
                        <span v-else>{{scope.row.shijia}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                prop="shijia"
                label="累计至上月已休事假"
            >
                    <template slot-scope="scope">
                        <span>{{scope.row.shijia_to_last}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                prop="shijia_total"
                label="累计已休事假"
            >
                    <template slot-scope="scope">
                    <el-popover
                            v-if='scope.row.shijia_total!=0'
                            placement="bottom"
                            width="400"
                            trigger="click"
                            >
                            <div v-if='popoverData.length!=0'>
                                <div class='font14 color3'>累计已休</div>
                                <div class='popoverList flex' v-for='(list,index) in popoverData'>
                                    <div class='flex1'>
                                        <div class='font14 color3'><span v-for='(item,i) in list.days'>{{item}} <span v-if='i+1<list.days.length'>、</span></span></div>
                                        <div class='font12 color6 mt8'>{{list.total}}</div>
                                    </div>
                                    <div class='el-icon-arrow-right'></div>
                                </div>
                            </div>
                            <div class='text-primary cur-p' @click='getDetail(scope.row,"shijia_total")'  slot="reference">{{scope.row.shijia_total}}</div>
                        </el-popover>
                        <span v-else>{{scope.row.shijia_total}}</span>
                    </template>
                </el-table-column>
            </el-table-column>
            <el-table-column label="调休（小时）">
                <el-table-column
                        prop="tiaoxiu_current_hour"
                        label="当前学年可休调休"
                >
                    <template slot-scope="scope">
                        <span>{{scope.row.tiaoxiu_current_hour}}</span>
                    </template>
                </el-table-column>

                <!--<el-table-column
                prop="tiaoxiu"
                label="当前学年可休调休小时"
            >
                    <template slot-scope="scope">
                        <el-popover
                            v-if='scope.row.tiaoxiu!=0'
                            placement="bottom"
                            width="400"
                            trigger="click"
                            >
                            <div v-if='popoverData.length!=0'>
                                <div class='font14 color3'>累计已休</div>
                                <div class='popoverList flex' v-for='(list,index) in popoverData'>
                                    <div class='flex1'>
                                        <div class='font14 color3'><span v-for='(item,i) in list.days'>{{item}} <span v-if='i+1<list.days.length'>、</span></span></div>
                                        <div class='font12 color6 mt8'>{{list.total}}</div>
                                    </div>
                                    <div class='el-icon-arrow-right'></div>
                                </div>
                            </div>
                            <div class='text-primary cur-p' @click='getDetail(scope.row,"tiaoxiu")'  slot="reference">{{scope.row.tiaoxiu}}</div>
                        </el-popover>
                        <span v-else>{{scope.row.tiaoxiu}}</span>
                    </template>
                </el-table-column>-->
                <el-table-column
                prop="tiaoxiu_total_hour"
                label="当前学年已休调休"
            >
                    <template slot-scope="scope">
                        <el-popover
                            v-if='scope.row.tiaoxiu_total_hour!=0'
                            placement="bottom"
                            width="400"
                            trigger="click"
                            >
                            <div v-if='popoverData.length!=0'>
                                <div class='font14 color3'>累计已休</div>
                                <div class='popoverList flex' v-for='(list,index) in popoverData'>
                                    <div class='flex1'>
                                        <div class='font14 color3'><span v-for='(item,i) in list.days'>{{item}} <span v-if='i+1<list.days.length'>、</span></span></div>
                                        <div class='font12 color6 mt8'>{{list.total}}</div>
                                    </div>
                                    <div class='el-icon-arrow-right'></div>
                                </div>
                            </div>
                            <div class='text-primary cur-p' @click='getDetail(scope.row,"tiaoxiu_total")'  slot="reference">{{scope.row.tiaoxiu_total_hour}}</div>
                        </el-popover>
                        <span v-else>{{scope.row.tiaoxiu_total_hour}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                prop="tiaoxiu_balance"
                label="当前学年剩余调休"
            >
                    <template slot-scope="scope">
                        <span>{{scope.row.tiaoxiu_balance_hour}}</span>
                    </template>
                </el-table-column>
            </el-table-column>
            <el-table-column label="外出（天）">
                <el-table-column prop="waichu_total" label="外出">
                    <template slot-scope="scope">
                        <el-popover v-if='scope.row.waichu_total!=0' placement="bottom" width="400" trigger="click">
                            <div v-if='popoverData.length!=0'>
                                <div class='font14 color3'>累计外出</div>
                                <div class='popoverList flex' v-for='(list,index) in popoverData'>
                                    <div class='flex1'>
                                        <div class='font14 color3'><span v-for='(item,i) in list.days'>{{item}} <span v-if='i+1<list.days.length'>、</span></span></div>
                                        <div class='font12 color6 mt8'>{{list.total}}</div>
                                    </div>
                                    <div class='el-icon-arrow-right'></div>
                                </div>
                            </div>
                            <div class='text-primary cur-p' @click='getDetail(scope.row,"waichu_total")'  slot="reference">{{scope.row.waichu_total}}</div>
                        </el-popover>
                        <span v-else>{{scope.row.waichu_total}}</span>
                    </template>
                </el-table-column>
            </el-table-column>
            <!-- <el-table-column
            prop="shijia"
            label="事假（天）"
            width="150">
            </el-table-column>
            <el-table-column
            prop="jiaban"
            label="加班（时）"
            width="150">
            </el-table-column> -->
        </el-table>
    </div>
    <div class="modal fade" id="getLeaveModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="parentReplyLabel">假期配额管理</h4>
            </div>
            <div class="p24 overflow-y scroll-box font14" :style="'max-height:'+(modalHeight+30)+'px;overflow-x: hidden;'"  >
                <div>
                    <el-select v-model="startYear" size="small" placeholder="请选择" @change='getLeaveDay()'>
                        <el-option
                        v-for="(item,index) in yearList"
                        :key="item.key"
                        :label="item.title"
                        :value="item.key">
                        </el-option>
                    </el-select>
                    <el-select v-model="setSchoolId" size="small" placeholder="请选择" @change='getGroup("set")'>
                        <el-option
                        v-for="(item,index) in schoolList"
                        :key="item.branchid"
                        :label="item.title"
                        :value="item.branchid">
                        </el-option>
                    </el-select>
                    <el-select v-model="setUser_ids" size="small" placeholder="请选择" @change='getLeaveDay()'>
                        <el-option
                        v-for="(item,index) in addGroupData.list"
                        :key="index"
                        :label="item.dep_name"
                        :value="item.user_ids">
                        </el-option>
                    </el-select>
                </div>
                <div v-if='leaveData.list'>
                <el-table
                    :data="leaveData.list"
                    :max-height="modalHeight-50"
                    :header-cell-style="{background:'#fafafa',color:'#333'}"
                    style="width: 100%">
                    <el-table-column
                        prop="date"
                        width="150"
                        label="员工">
                        <template slot-scope="scope">

                            <div class='flex'>
                                <img :src="leaveData.staff_info[scope.row.staff_id].photoUrl" class='img42 img-circle' alt="">
                                <div class='flex1 ml10'>
                                    <div class='font14 color3'>{{leaveData.staff_info[scope.row.staff_id].name}}</div>
                                    <div class='font12 color6'>{{leaveData.staff_info[scope.row.staff_id].hrPosition}}</div>
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="name"
                        label="当年度可休年假（天）"
                        width="90">
                        <template slot-scope="scope">
                        <el-input v-model='scope.row.nianjia.days'  size='small'></el-input>
                    </template>
                    </el-table-column>
                    <el-table-column
                        prop="address"
                        width="90"
                        label="上年度顺延年假（天）">
                        <template slot-scope="scope">
                            <el-input v-model='scope.row.nianjia_last.days' size='small'></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="address"
                        width="90"
                        label="当年度可休病假（天）">
                        <template slot-scope="scope">
                            <el-input  v-model='scope.row.bingjia.days' size='small'></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="address"
                        width="90"
                        label="当年度调休(小时)">
                        <template slot-scope="scope">
                            <el-input  v-model='scope.row.tiaoxiu.hours' size='small'></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="address"
                        width="90"
                        label="上年度延休调休(小时)">
                        <template slot-scope="scope">
                            <el-input  v-model='scope.row.tiaoxiu_last.hours' size='small'></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="name"
                        label="当年度可休寒暑假（天）"
                        width="90">
                        <template slot-scope="scope">
                        <el-input v-model='scope.row.hanshujia.days'  size='small'></el-input>
                    </template>
                    </el-table-column>

                    <el-table-column
                        prop="address"
                        width="90"
                        label="上年度顺延寒暑假（天）">
                        <template slot-scope="scope">
                            <el-input v-model='scope.row.hanshujia_last.days' size='small'></el-input>
                        </template>
                    </el-table-column>

                    <el-table-column
                            prop="address"
                            width="150"
                            label="备注">
                        <template slot-scope="scope">
                            <el-input type="textarea" v-model='scope.row.remark' size='small'></el-input>
                        </template>
                    </el-table-column>

                    </el-table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close"); ?></button>
                <button type="button" class="btn btn-primary" @click="saveLeaveDay()"><?php echo Yii::t("newDS", "保存"); ?></button>
            </div>
            </div>
        </div>
    </div>
</div>
<script>
var itRole = <?php echo $this->checkRole() ? 1 : 0; ?>;
var yearData = <?php echo CJSON::encode($yearList) ?>;
var yearList = yearData.list.map((val,index,arr)=>{
	let json = {}
	json.title= val.value
	json.key = val.key
	return json
})
var height=document.documentElement.clientHeight;
 var container=new Vue({
        el: "#container",
        data: {
            height:height,
            modalHeight:height-220,
            yearList:yearList,
            groupData:{},
            schoolList:[],
            schoolId:'',
            monthList:[
                {value:'1月',key:1,},
                {value:'2月',key:2,},
                {value:'3月',key:3,},
                {value:'4月',key:4,},
                {value:'5月',key:5,},
                {value:'6月',key:6,},
                {value:'7月',key:7,},
                {value:'8月',key:8,},
                {value:'9月',key:9,},
                {value:'10月',key:10,},
                {value:'11月',key:11,},
                {value:'12月',key:12,},
            ],
            month:'',
            user_ids:'',
            leaveData:{},
            startYear:yearData.current,
            addGroupData:{},
            setUser_ids:'',
            setSchoolId:'',
            searchText:'',
            tableData:[],
            popoverData:[],
            showTable:false
        },
        created: function() {
            this.getPage()
            var myDate = new Date();
            this.month= myDate.getMonth()+1;
        },
        watch:{
        },
        computed: {
            tableList: function() {
                var search = this.searchText;
                var searchVal = ''; //搜索后的数据
                if(search) {
                    searchVal =this.tableData.filter(function(product) {
                        return Object.keys(product).some(function(key) {
                            return String(product['staff_name'].toLowerCase()).indexOf(search.toLowerCase()) !== -1;
                        })
                    })
                    return searchVal;
                }
                return this.tableData;
            },
        },
        methods: {
            getPage(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("schoolList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            if (itRole == 1) {                                
                                that.schoolList=data.data.list
                            } else {
                                that.schoolList=data.data.list[data.data.current_school]
                            }
                            that.schoolId=data.data.current_school
                            that.setSchoolId=data.data.current_school
                            that.getGroup()
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            getGroup(type){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getAllDepartment") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        school_id:type?this.setSchoolId:this.schoolId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            if(type){
                                that.addGroupData=data.data
                                that.setUser_ids=''
                            }else{
                                that.addGroupData=data.data
                                that.groupData=JSON.parse(JSON.stringify(data.data));
                                that.groupData.list.unshift({
                                    dep_name: "全部",
                                    user_ids:''
                                })
                                that.user_ids=''
                                that.setUser_ids=''
                            }
                            that.getLeave()
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            getLeave(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getLeaveList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        startyear:this.startYear,
                        user_ids:this.user_ids==''?[-1]:this.user_ids,
	                    month: this.month,
                        school_id:this.schoolId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.showTable=true
                            that.tableData=that.sortTea(data.data.list)
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.showTable=true
                    },
                    error: function(data) {
                        that.showTable=true
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            sortTea(list){
                list.sort((x,y)=>{
                    return x['staff_name'].localeCompare(y['staff_name'])
                })
                return list
            },
            setLeave(){
                this.setSchoolId=this.schoolId
                // this.setUser_ids=this.user_ids
                if(this.setUser_ids!=''){
                    this.getLeaveDay()
                }
                this.leaveData={}
                $("#getLeaveModal").modal('show')
            },
            getLeaveDay(){
                let that=this
                if(this.setUser_ids==''){
                    return
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("getLeaveQuotaDays") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        startyear:this.startYear,
                        user_ids:this.setUser_ids,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.leaveData=data.data
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            saveLeaveDay(){
                let that=this
                var user_data={}
                this.leaveData.list.forEach(item => {
                    user_data[item.staff_id]={
                        nianjia:item.nianjia.days,
                        tiaoxiu:item.tiaoxiu.hours,
                        bingjia:item.bingjia.days,
                        hanshujia:item.hanshujia.days,
                        nianjia_last:item.nianjia_last.days,
                        tiaoxiu_last:item.tiaoxiu_last.hours,
                        hanshujia_last:item.hanshujia_last.days,
                        remark:item.remark,
                    }
                });
                $.ajax({
                    url: '<?php echo $this->createUrl("setLeaveQuotaDays") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        startyear:this.startYear,
                        user_data:user_data,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            $("#getLeaveModal").modal('hide')
                            that.getLeave()
                            resultTip({
                                msg: data.message
                            });
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            getDetail(list,type){
                let that=this
                that.popoverData=[]
                $.ajax({
                    url: '<?php echo $this->createUrl("getLeaveDays") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        startyear:this.startYear,
                        month:this.month,
                        user_id:list.staff_id,
                        type:type
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                          that.popoverData=data.data
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            exportTab() {
                var schoolTitle=''
                if (itRole == 1) {                       
                    var schoolTitle= this.schoolList[this.schoolId].title
                } else {
                    var schoolTitle= this.schoolList.title
                }
                let August=['BJ_DS','BJ_SLT','NB_FJ','NB_HH','BJ_QFF','BJ_IASLT','BJ_OE']
                var year=''
                if(August.indexOf(this.schoolId)==-1){
                    year=this.month>8?this.startYear:parseInt(this.startYear)+1
                }else{
                    year=this.month>7?this.startYear:parseInt(this.startYear)+1
                }
                var title= year+'-'+this.month+'月考勤'+schoolTitle+'.xlsx'
                const ws_name = "SheetJS";
                var exportDatas = [];
                for(var i=0;i<this.tableList.length;i++){
                    exportDatas.push(
                        {
                        '<?php echo Yii::t('global', '员工') ?>':this.tableList[i].staff_name,
                        '<?php echo Yii::t('global', '身份证号') ?>':this.tableList[i].card_id,
                        '<?php echo Yii::t('global', '单位') ?>':schoolTitle,
                        '<?php echo Yii::t('global', '部门') ?>':this.tableList[i].pos_name,
                        '<?php echo Yii::t('global', '职位') ?>':this.tableList[i].hrPosition,
                        "<?php echo Yii::t('labels', '年假本月已休') ?>":this.tableList[i].nianjia,
                        "<?php echo Yii::t('labels', '当前学年可休年假') ?>":this.tableList[i].nianjia_year,
                        "<?php echo Yii::t('labels', '累计至上月已休年假') ?>":this.tableList[i].nianjia_to_last,
                        "<?php echo Yii::t('labels', '当前学年剩余年假') ?>":this.tableList[i].nianjia_balance,
                        "<?php echo Yii::t('labels', '病假本月已休') ?>":this.tableList[i].bingjia,
                        "<?php echo Yii::t('labels', '当前学年可休病假') ?>":this.tableList[i].bingjia_year,
                        "<?php echo Yii::t('labels', '累计至上月已休病假') ?>":this.tableList[i].bingjia_to_last,
                        "<?php echo Yii::t('labels', '当前学年剩余病假') ?>":this.tableList[i].bingjia_balance,
                        "<?php echo Yii::t('labels', '事假本月已休') ?>":this.tableList[i].shijia,
                        "<?php echo Yii::t('labels', '累计至上月已休事假') ?>":this.tableList[i].shijia_to_last,
                        //"<?php //echo Yii::t('labels', '调休本月已休') ?>//":this.tableList[i].tiaoxiu,
                        //"<?php //echo Yii::t('labels', '调休累计已休') ?>//":this.tableList[i].tiaoxiu_total,

                        "<?php echo Yii::t('labels', '当前学年可休调休小时') ?>":this.tableList[i].tiaoxiu_current_hour,
                        "<?php echo Yii::t('labels', '当前学年已休调休小时') ?>":this.tableList[i].tiaoxiu_total_hour,
                        "<?php echo Yii::t('labels', '当前学年剩余调休小时') ?>":this.tableList[i].tiaoxiu_balance_hour,

                        "<?php echo Yii::t('labels', '外出') ?>":this.tableList[i].waichu_total,

                        '<?php echo Yii::t('labels','工作日加班（小时）')?>':"",
                        '<?php echo Yii::t('labels','周末加班（小时）')?>':"",
                        '<?php echo Yii::t('labels','法定假日加班（小时）')?>':"",
                        '<?php echo Yii::t('labels','当月旷工天数')?>':"",
                        '<?php echo Yii::t('labels','累计至上月旷工天数')?>':"",
                        '<?php echo Yii::t('labels','备注')?>':this.tableList[i].remark,
                        });
                }
                var wb=XLSX.utils.json_to_sheet(exportDatas,{
                    origin:'A1',// 从A1开始增加内容
                    header: [
                    '<?php echo Yii::t('global', '身份证号') ?>',
                    '<?php echo Yii::t('global', '员工') ?>',
                    '<?php echo Yii::t('global', '单位') ?>',
                    '<?php echo Yii::t('global', '部门') ?>',
                    '<?php echo Yii::t('global', '职位') ?>',
                    '<?php echo Yii::t('labels', '年假本月已休') ?>',
                    '<?php echo Yii::t('labels', '当前学年可休年假') ?>',
                    '<?php echo Yii::t('labels', '累计至上月已休年假') ?>',
                    '<?php echo Yii::t('labels', '当前学年剩余年假') ?>',
                    '<?php echo Yii::t('labels','病假本月已休')?>',
                    '<?php echo Yii::t('labels','当前学年可休病假')?>',
                    '<?php echo Yii::t('labels','累计至上月已休病假')?>',
                    '<?php echo Yii::t('labels','当前学年剩余病假')?>',
                    '<?php echo Yii::t('labels','事假本月已休')?>',
                    '<?php echo Yii::t('labels','累计至上月已休事假')?>',
                    //'<?php //echo Yii::t('labels','调休本月已休')?>//',
                    //'<?php //echo Yii::t('labels','调休累计已休')?>//',
                        "<?php echo Yii::t('labels', '当前学年可休调休小时') ?>",
                    "<?php echo Yii::t('labels', '当前学年已休调休小时') ?>",
                    "<?php echo Yii::t('labels', '当前学年剩余调休小时') ?>",
                    "<?php echo Yii::t('labels', '外出') ?>",
                    '<?php echo Yii::t('labels','工作日加班（小时）')?>',
                    '<?php echo Yii::t('labels','周末加班（小时）')?>',
                    '<?php echo Yii::t('labels','法定假日加班（小时）')?>',
                    '<?php echo Yii::t('labels','当月旷工天数')?>',
                    '<?php echo Yii::t('labels','累计至上月旷工天数')?>',
                    '<?php echo Yii::t('labels','备注')?>',
                    ]
                });
                const workbook = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(workbook, wb, ws_name);
                const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                const blob = new Blob([wbout], {type: 'application/octet-stream'});
                let link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = title;
                link.click();
                setTimeout(function() {
                    // 延时释放掉obj
                    URL.revokeObjectURL(link.href);
                    link.remove();
                }, 500);
            },
        }
    })
</script>
<style>
    .img42{
        width: 42px;
        height: 42px;
        object-fit: cover;
    }
    .popoverList{
        padding:8px;
        border-bottom:1px solid #E8E8E8;
        align-items:center
    }
    .word-break{
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        
    }
    .flexText{
        width:0
    }
    .modal-lg {
        width: 990px;
    }
</style>