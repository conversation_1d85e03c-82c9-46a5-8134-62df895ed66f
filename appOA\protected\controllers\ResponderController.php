<?php

class ResponderController extends Controller
{   
	public function actionYeepayResponder()
	{
        $this->layout='//layouts/blank';
        
        if (1 || Yii::app()->request->isPostRequest){ // 易宝这个贱人，get过来的
            Yii::import('application.components.policy.PolicyApi');
            Yii::import('common.components.yeepay.*');
            Yii::import('common.models.yeepay.*');
            Yii::import('common.models.invoice.*');
        
            $yeePay = new YeepayCommon;
            
            $postStr = file_get_contents("php://input").'|'.Yii::app()->request->url;
            $yeePay->logCustomStr(null, $postStr);
            
            $yeePay->getCallBackValue($p1_MerId,$r0_Cmd, $r1_Code, $r2_TrxId, $r3_Amt, $r4_Cur, $r5_Pid, $r6_Order, $r7_Uid, $r8_MP, $r9_BType,$rb_BankId, $ro_BankOrderId, $rp_PayDate, $rq_CardNo,$ru_Trxtime, $hmac);
            
            if (!$r6_Order)
                die;
            
            $order = YeepayOrder::model()->with('items')->findByPk($r6_Order);
            $yeePay->init($order->schoolId);
            $policyApi = new PolicyApi($order->schoolId);
            
            $bRet = $yeePay->CheckHmac($p1_MerId,$r0_Cmd,$r1_Code,$r2_TrxId,$r3_Amt,$r4_Cur,$r5_Pid,$r6_Order,$r7_Uid,$r8_MP,$r9_BType,$hmac);
            
            if($bRet && $r3_Amt == $order->payable_amount){
                if($r1_Code=="1"){
                    if ($order->status == 0){
                        $order->fact_amount = $r3_Amt;
                        $order->r2_TrxId = $r2_TrxId;
                        $order->r7_Uid = $r7_Uid;
                        $order->ro_BankOrderId = $ro_BankOrderId;
                        $order->rp_PayDate = $rp_PayDate;
                        $order->status = 1;
                        $order->updateTime = time();
                        $order->flag = 1;
                        if ($order->save()){
                            foreach ($order->items as $item){
                                $invoiceID[] = $item->invocieId;

                                $item->status = 1;
                                $item->updateTime = time();
                                $item->save();
                            }

                            $criteria = new CDbCriteria;
                            $criteria->compare('invoice_id', $invoiceID);
                            $iModels=Invoice::model()->findAll($criteria);

                            $errCode = $policyApi->pay($iModels, InvoiceTransaction::TYPE_POS, $r3_Amt, $order->operatorId);
                            if ( !$errCode ) {
                                if($r9_BType=="1"){

                                }elseif($r9_BType=="2"){
                                    echo "success";
                                }
                                PolicyApi::posToEmail(array($order->orderId),'parent',true,OA::isProduction());
                            }
                            else{
                                $order->status = 0;
                                if ( $order->save() ){
                                    $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
                                    $mailer->AddAddress('<EMAIL>');
                                    $mailer->Subject = $errCode . ' 执行Pay失败 ID: '.$r6_Order;
                                    $mailer->Body = '执行Pay失败 ID: '.$r6_Order.'<br>ErrorCode: '.$errCode.'<br>Time: '.time();
                                    $mailer->iniMail( OA::isProduction() ); // 此行代码要放到AddAddress, AddCC方法下面
                                    $mailer->Send();
                                }
                                else{
                                    $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
                                    $mailer->AddAddress('<EMAIL>');
                                    $mailer->Subject = $errCode . ' 更新ivy_yp_invoice_transaction status失败 ID: '.$r6_Order;
                                    $mailer->Body = '更新ivy_yp_invoice_transaction status失败 ID: '.$r6_Order.'<br>ErrorCode: '.$errCode.'<br>Time: '.time();
                                    $mailer->iniMail( OA::isProduction() ); // 此行代码要放到AddAddress, AddCC方法下面
                                    $mailer->Send();
                                }
                            }
                        }
                        else{
                            $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
                            $mailer->AddAddress('<EMAIL>');
                            $mailer->Subject = '更新ivy_yp_invoice_transaction失败 ID: '.$r6_Order;
                            $mailer->Body = '更新ivy_yp_invoice_transaction失败 ID: '.$r6_Order;
                            $mailer->iniMail( OA::isProduction() ); // 此行代码要放到AddAddress, AddCC方法下面
                            $mailer->Send();
                        }
                    }
                    else{
                        echo "success";
                    }
                }else{
                    echo "success";
                }
            }
            else {
                echo "交易信息被篡改";
            }
            
        }
	}
}