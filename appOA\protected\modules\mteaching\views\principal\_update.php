<div class="form-group">
    <label class="col-xs-2 control-label"><?php echo CHtml::activeLabelEx($model, 'cycle'); ?></label>
    <div class="col-xs-4">
        <?php echo CHtml::activeDropDownList($model, 'cycle', array(1 => 1,2 => 2,3 => 3,4 => 4), array('maxlength'=>255,'class'=>'form-control', 'empty' => Yii::t('global','Please Select'))); ?>
    </div>
    <label class="col-xs-2 control-label"><?php echo CHtml::activeLabelEx($model, 'type'); ?></label>
    <div class="col-xs-4">
        <?php echo CHtml::activeDropDownList($model, 'type', array(1 => Yii::t('principal','Mid-Term Report (Full)'), 2 => Yii::t('principal','End-Term Report (Short)')), array('maxlength'=>255,'class'=>'form-control', 'empty' => Yii::t('global','Please Select'))); ?>
    </div>
</div>
<div class="form-group">
    <label class="col-xs-2 control-label"><?php echo CHtml::activeLabelEx($model, 'ms_principal'); ?> *</label>
    <div class="col-xs-4">
        <?php echo CHtml::activeDropDownList($model, 'ms_principal', $ms_principal, array('maxlength'=>255,'class'=>'form-control', 'empty' => Yii::t('global','Please Select'))); ?>
    </div>
    <label class="col-xs-2 control-label"><?php echo CHtml::activeLabelEx($model, 'msv_principal'); ?> *</label>
    <div class="col-xs-4">
        <?php echo CHtml::activeDropDownList($model, 'msv_principal', $msv_principal, array('maxlength'=>255,'class'=>'form-control', 'empty' => Yii::t('global','Please Select'))); ?>
    </div>
</div>
<div class="form-group">
    <label class="col-xs-2 control-label"><?php echo CHtml::activeLabelEx($model,'report_name_cn'); ?></label>
    <div class="col-xs-10">
        <?php echo CHtml::activeTextField($model,'report_name_cn',array('maxlength'=>255,'class'=>'form-control')); ?>
    </div>
</div>
<div class="form-group">
    <label class="col-xs-2 control-label"><?php echo CHtml::activeLabelEx($model,'report_name_en'); ?></label>
    <div class="col-xs-10">
        <?php echo CHtml::activeTextField($model,'report_name_en',array('maxlength'=>255,'class'=>'form-control')); ?>
    </div>
</div>
<div class="form-group">
    <label class="col-xs-2 control-label"><?php echo CHtml::activeLabelEx($model,'start_time'); ?></label>
    <div class="col-xs-4">
        <?php echo CHtml::activeTextField($model,'start_time',array('class'=>'form-control inputDate','placeholder' => Yii::t('newDS','Select a date'))); ?>
    </div>
    <label class="col-xs-2 control-label"><?php echo CHtml::activeLabelEx($model,'end_time'); ?></label>
    <div class="col-xs-4">
        <?php echo CHtml::activeTextField($model,'end_time',array('class'=>'form-control inputDate' ,'placeholder' => Yii::t('newDS','Select a date'))); ?>
    </div>
</div>
<!-- <div class="form-group">
    <label class="col-xs-2 control-label"><?php echo CHtml::activeLabelEx($model,'end_time'); ?></label>
    <div class="col-xs-3">
        <?php echo CHtml::activeTextField($model,'end_time',array('class'=>'form-control inputDate' ,'placeholder' => Yii::t('newDS','Select a date'))); ?>
    </div>
</div> -->
<div class="form-group">
    <label class="col-xs-2 control-label"><?php echo CHtml::activeLabelEx($model,'fill_in_end_time'); ?></label>
    <div class="col-xs-4">
        <?php echo CHtml::activeTextField($model,'fill_in_end_time',array('class'=>'form-control inputDate', 'placeholder' => Yii::t('newDS','Select a date')) ); ?>
    </div>
    <div class="flex align-items">
        <div >
            <select name="AchievementReport[fill_in_end_time_h]" id="" class="form-control input_1">
                <option value=""></option>
                <?php
                for ($i=0;$i<=23;$i++){
                    $i_str = str_pad($i, 2, "0", STR_PAD_LEFT);
                    if($fill_in_end_time['h']==$i_str){
                        echo "<option value='".$i_str."' selected>$i_str</option>";
                    }else{
                        echo "<option value='".$i_str."'>$i_str</option>";
                    }
                }
                ?>
            </select>
        </div>
        <div class='ml10 mr10'> :</div>
        <div>
            <select name="AchievementReport[fill_in_end_time_i]" id="" class="form-control input_1" >
                <option value=""></option>
                <?php
                for ($i=0;$i<=59;$i++){
                    $i_str = str_pad($i, 2, "0", STR_PAD_LEFT);
                    if($fill_in_end_time['i']==$i_str){
                        echo "<option value='".$i_str."' selected >$i_str</option>";
                    }else{
                        echo "<option value='".$i_str."'>$i_str</option>";
                    }
                }
                ?>
            </select>
        </div>
    </div>
</div>
<div class="form-group">
    <label class="col-xs-2 control-label"><?php echo CHtml::activeLabelEx($model,'president_meassge_cn'); ?></label>
    <div class="col-xs-10">
        <?php echo CHtml::activeTextArea($model,'president_meassge_cn',array('rows' => 6,'class'=>'form-control status')); ?>
        <div class="counters counters1" style="visibility: hidden"><span class="counter"></span>/1000 </div>
    </div>
</div>
<div class="form-group">
    <label class="col-xs-2 control-label"><?php echo CHtml::activeLabelEx($model,'president_meassge_en'); ?></label>
    <div class="col-xs-10">
        <?php echo CHtml::activeTextArea($model,'president_meassge_en',array('rows' => 6,'class'=>'form-control status')); ?>
        <div class="counters counters2" style="visibility: hidden"><span class="counter"></span>/1000 </div>
    </div>
</div>

<?php
echo CHtml::hiddenField("id", $model->id);
echo CHtml::activeHiddenField($model, 'calendar');
?>

<script>
    $( "#AchievementReport_start_time" ).datepicker({'dateFormat':'yy-mm-dd'});
    $( "#AchievementReport_end_time" ).datepicker({'dateFormat':'yy-mm-dd'});
    $( "#AchievementReport_fill_in_end_time" ).datepicker({'dateFormat':'yy-mm-dd'});
    function countChar(spanName) {
        $('.' + spanName).css('visibility','inherit');
        let textlength = $('.' + spanName).prev('.status').val().length;
        $('.' + spanName).children('.counter').html(function () {
            if (textlength >= 1000) {
                $(this).parent().css('color','red');
                return textlength;
            } else {
                $(this).parent().css('color','#b2b2b2');
                return textlength;
            }
        });
    }
</script>
