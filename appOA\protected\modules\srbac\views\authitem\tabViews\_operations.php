<div id="task_operations" class="panel panel-primary">
    <div class="panel-body">
        <label>Operations</label>
        <div class="row">
            <?php foreach($taskData as $t=>$ops):?>
                <div class="col-md-4">
                    <div class="panel panel-success">
                        <div class="panel-heading">
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" data-checklist="J_check_c<?php echo $t?>" class="J_check_all" data-direction="x" name="tauth[<?php echo $t?>]" value="<?php echo $t?>">
                                    <?php echo $descriptions[$t].' '.$t;?>
                                </label>
                            </div>
                        </div>
                        <?php if($ops):?>
                            <div class="J_check_wrap panel-body">
                                <?php foreach($ops as $p):?>
                                    <div class="checkbox">
                                        <label>
                                            <input type="checkbox" data-xid="J_check_c<?php echo $t?>" class="J_check" data-direction="x" name="oauth[<?php echo $p?>]" value="<?php echo $p?>">
                                            <?php echo $descriptions[$p].' '.$p;?>
                                        </label>
                                    </div>
                                <?php endforeach;?>
                            </div>
                        <?php endif;?>
                    </div>
                </div>
            <?php endforeach;?>
        </div>
        <button type="submit" class="btn btn-default J_ajax_submit_btn">Submit</button>
    </div>
</div>