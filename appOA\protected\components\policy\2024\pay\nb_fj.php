<?php
if(!defined("IVY_POLICY"))
throw new CException('DO NOT USE THIS FILE DIRECTLY');

return array( 'policy'=>array(

    //是否老生老办法
    ENABLE_CONSTANT_TUITION => true,

    //年保育费
    //ANNUAL_CHILDCARE_AMOUNT => 8000,

    //是否开放课外活动账单
    ENABLE_AFTERSCHOOLS => true,

    //图书馆工本费
    FEETYPE_LIBCARD => 10,

    //学期四个时间点；取前后两端
    TIME_POINTS   => array(202409,202501,202502,202507),

    //年付开通哪些缴费类型
   PAYGROUP_ANNUAL => array(
       ITEMS => array(
           FEETYPE_TUITION,
           FEETYPE_LUNCH,
        //    FEETYPE_SCHOOLBUS,
       ),
       EXCEPTS => array(
           OTHER_PROGRAM_CHN,
           OTHER_PROGRAM_CHN_1
       ),
       TUITION_CALC_BASE => BY_MONTH,
   ),

//    //学期付开通哪些缴费类型
   PAYGROUP_SEMESTER => array(
       ITEMS => array(
           FEETYPE_TUITION,
           FEETYPE_LUNCH,
        //    FEETYPE_SCHOOLBUS,
       ),
       EXCEPTS => array(
           OTHER_PROGRAM_CHN,
           OTHER_PROGRAM_CHN_1
       ),
       TUITION_CALC_BASE => BY_MONTH,
   ),

//    //月份开通哪些缴费类型
   PAYGROUP_MONTH => array(
       ITEMS => array(
           FEETYPE_TUITION,
           FEETYPE_LUNCH,
        //    FEETYPE_SCHOOLBUS,
       ),
       TUITION_CALC_BASE => BY_MONTH,
   ),

    //计算基准：BY_MONTH, 或者 BY_SETTING, 前者表示所有的学费都基于月费用计算，后者表示所有的学费都基于学期或学年
    TUITION_CALCULATION_BASE => array(

        BY_MONTH => array(

            //月收费金额
            AMOUNT => array(
                FULL5D => 7000,
                FULL5D1 => 5000,
                // FULL5D2 => 1010,
//                OTHER_PROGRAM_CHILDCARE => 3800,
            //    OTHER_PROGRAM_CHN => 1320,
                // OTHER_PROGRAM_CHN_1 => 1010,
//                FIX => 24,
            ),

            //收费月份，及其权重
            MONTH_FACTOR => array(
                202409=>1,
                202410=>1,
                202411=>1,
                202412=>1,
                202501=>.8,
                202502=>.8,
                202503=>1,
                202504=>1,
                202505=>1,
                202506=>1,
                202507=>0.9685
            ),

            //折扣
            GLOBAL_DISCOUNT => array(
                DISCOUNT_ANNUAL => '1:1:2024-12-01:round',
                DISCOUNT_SEMESTER1 => '1:1:2024-12-30:round',
                DISCOUNT_SEMESTER2 => '1:1:2025-07-31:round'
            )

        ),

    ),

    //每餐费用
    LUNCH_PERDAY => array(
        LUNCHA => 25,
        LUNCHB => 18,
    ),

    //账单到期日和账单开始日之差
    DUEDAYS_OFFSET => 1,

));