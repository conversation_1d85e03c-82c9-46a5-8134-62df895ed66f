<div class="row">
    <div class="col-md-3">
        <div class="input-group mb10">
            <input type="text" class="form-control" id="searchInput" placeholder="<?php echo Yii::t('message',
                'Input partial email to search');?>" value="<?php echo Yii::app()->request->getParam('email', '');?>">
            <span class="input-group-btn">
                <button type="button" class="btn btn-primary" onclick="searchStaff()">
                    <?php echo Yii::t('global', 'Search');?>
                </button>
            </span>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-md-12">
        <table class="table">
            <thead>
            <tr>
                <th><?php echo Yii::t('ivyer','English Name');?></th>
                <th><?php echo Yii::t('labels','Chinese Name');?></th>
                <th><?php echo Yii::t('labels','Gender');?></th>
                <th><?php echo Yii::t('labels','Email');?></th>
                <th><?php echo Yii::t('labels', 'Position');?></th>
                <th></th>
            </tr>
            </thead>
            <tbody id="cbody">

            </tbody>
        </table>
        <?php if ($taskData['count']>20):?>
        <nav aria-label="Page navigation">
            <ul class="pagination">
                <?php for($i=1; $i<ceil($taskData['count']/20); $i++):?>
                <li><a href="<?php echo $this->createUrl('index', array('task' => 'alumni', 'page' => $i));?>"><?php echo $i;?></a></li>
                <?php endfor;?>
            </ul>
        </nav>
        <?php endif;?>
    </div>
</div>

<?php
$this->branchSelectParams['extraUrlArray'] = array('//mcampus/staffProfile/index', 'task'=>'alumni');
$this->renderPartial('//layouts/common/branchSelectBottom');
?>

<div class="modal fade" id="reentry">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title"><?php echo Yii::t('ivyer','Re-register');?></h4>
            </div>
            <?php
            $form=$this->beginWidget('CActiveForm', array(
                'id'=>'reentry-form',
                'action'=>$this->createUrl('reentry'),
                'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
            ));
            ?>
            <div class="modal-body">

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", 'Cancel');?></button>
                <button type="button" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t("global", 'Submit');?></button>
            </div>
            <?php
            $this->endWidget();
            ?>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<script type="text/template" id="item-template">
    <td><%= name_en%></td>
    <td><%= name_cn%></td>
    <td><%= gender%></td>
    <td><%= email%></td>
    <td><%= occupation%></td>
    <td><button class="btn btn-info btn-xs" role="button"><?php echo Yii::t('ivyer','Re-register');?></td>
</script>

<script type="text/template" id="entry-template">
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo Yii::t('labels','Email');?></label>
        <div class="col-xs-9">
            <input type="text" class="form-control" value="<%= email%>" disabled>
            <input type="hidden" name="uid" value="<%= uid%>">
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label" for="entryDate">
            <?php echo Yii::t('labels','Start Date');?>
        </label>
        <div class="col-xs-9">
            <input type="text" name="entryDate" id="entryDate" class="form-control">
        </div>
    </div>
</script>

<script>
    var cdata = <?php echo CJSON::encode($taskData['cdata'])?>;

    var userList = Backbone.Collection.extend({
        comparator: 'name_en',
        search : function(letters){
            if(letters == "") return this;

            var pattern = new RegExp(letters,"gi");
            return _(this.filter(function(data) {
                return pattern.test(data.get("email"));
            }));
        }
    });
    var users = new userList;

    var itemView = Backbone.View.extend({
        tagName: 'tr',
        events: {
            "click .btn-info"   : "reEntry"
        },
        render: function(){
            this.$el.html( _.template($('#item-template').html(), this.model.attributes) );
            return this;
        },
        reEntry: function() {
            var entry = new reEntryView({model: this.model});
            $('#reentry .modal-body').html(entry.render().el);
            $('#reentry').modal();
            $('#reentry .modal-body #entryDate').datepicker({'dateFormat':'yy-mm-dd'});
        }
    });

    var reEntryView = Backbone.View.extend({
        tagName: 'p',
        render: function(){
            this.$el.html(_.template($('#entry-template').html(), this.model.attributes));
            return this;
        }
    });

    var AppView = Backbone.View.extend({
        el: $("body"),
        initialize: function () {
            this.listenTo(users, 'reset', this.renderList);
        },
        renderList : function(users){
            this.$('#cbody').html('');

            users.each(function(user){
                var item = new itemView({
                    model: user,
                    collection: this.collection
                });
                this.$('#cbody').append(item.render().el);
            });
            return this;
        }
    });
    var app = new AppView;
    users.reset(cdata);

    function searchStaff()
    {
        var letters = $('#searchInput').val();
        location.href = '<?php echo $this->createUrl('index', array('task' => 'alumni'));?>&email='+letters;
        return;
        app.renderList(users.search(letters));
    }

    function cbreentry()
    {
        window.setTimeout(function(){
            $('#reentry').modal('hide');
        }, 500)
    }

    $('#searchInput').keydown(function(event){
        if(event.keyCode == 13){
            searchStaff();
        }
    })
</script>