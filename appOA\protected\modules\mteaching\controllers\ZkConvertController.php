<?php

class ZkConvertController extends TeachBasedController
{
    public $actionAccessAuths = array(
        'index' => array('ivystaff_it', 'ivystaff_cd', 'ivystaff_dsedu_viewall', 'ivystaff_hos_office'),
    );

    public function createUrl($route, $params = array(), $ampersand = '&', $parentOnly = false)
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";
        $this->modernMenuFlag = 'teaching';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Teaching Tasks');
        $this->pageTitle =  CommonUtils::autoLang('DS-中考成绩转化','DS-Zhongkao Score Conversion');
        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = false;
        $this->branchSelectParams['urlArray'] = array('//mteaching/ZkConvert/index');

        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/vue.global.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/xlsx.full.min.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/label/iconfont.css?v=20230227');
    }

    public function actionIndex()
    {
        $this->render('index');
    }

    //中考成绩转换首页数据
    public function actionIndexData()
    {
        $requestUrl = 'zk/indexData';
        $date = array();
        $this->remote($requestUrl, array(
            'date' => $date,
        ), 'GET');
    }


    //中考成绩转换后分数
    public function actionScore()
    {
        $requestUrl = 'zk/score';
        $start_year = Yii::app()->request->getParam('start_year');
        $grade = Yii::app()->request->getParam('grade');
        $term = Yii::app()->request->getParam('term');
        $this->remote($requestUrl, array(
            'start_year' => $start_year,
            'grade'      => $grade,
            'term'       => $term,
        ), 'GET');
    }

    //保存转换分数
    public function actionScoreStore()
    {
        $requestUrl = 'zk/scoreStore';
        $start_year = Yii::app()->request->getParam('start_year');
        $grade = Yii::app()->request->getParam('grade');
        $term = Yii::app()->request->getParam('term');
        $zk_course = Yii::app()->request->getParam('zk_course',array());
        $this->remote($requestUrl, array(
            'start_year' => $start_year,
            'grade'      => $grade,
            'term'       => $term,
            'zk_course'  => $zk_course,
        ), 'POST');
    }

    //转换规则
    public function actionRuleData()
    {
        $requestUrl = 'zk/ruleData';
        $start_year = Yii::app()->request->getParam('start_year');
        $grade = Yii::app()->request->getParam('grade');
        $term = Yii::app()->request->getParam('term');
        $zk_course = Yii::app()->request->getParam('zk_course');
        $this->remote($requestUrl, array(
            'start_year' => $start_year,
            'grade'      => $grade,
            'term'       => $term,
            'zk_course'  => $zk_course,
        ), 'GET');
    }

    //检查转换规则的人数
    public function actionRuleCheck()
    {
        $requestUrl = 'zk/ruleCheck';
        $start_year = Yii::app()->request->getParam('start_year');
        $grade = Yii::app()->request->getParam('grade');
        $first_rule = Yii::app()->request->getParam('first_rule',array());
        $second_rule = Yii::app()->request->getParam('second_rule',array());
        $this->remote($requestUrl, array(
            'start_year'  => $start_year,
            'grade'       => $grade,
            'first_rule'  => $first_rule,
            'second_rule' => $second_rule,
        ), 'GET');
    }

    //保存转换规则
    public function actionRuleStore()
    {
        $requestUrl = 'zk/ruleStore';
        $start_year = Yii::app()->request->getParam('start_year');
        $grade = Yii::app()->request->getParam('grade');
        $term = Yii::app()->request->getParam('term');
        $zk_course = Yii::app()->request->getParam('zk_course');
        $first_rule = Yii::app()->request->getParam('first_rule');
        $second_rule = Yii::app()->request->getParam('second_rule',array());
        $this->remote($requestUrl, array(
            'start_year'  => $start_year,
            'grade'       => $grade,
            'term'        => $term,
            'zk_course'   => $zk_course,
            'first_rule'  => $first_rule,
            'second_rule' => $second_rule,
        ), 'POST');
    }

    //保存一个中考课程的规则和分数
    public function actionRuleScoreStore()
    {
        $requestUrl = 'zk/ruleScoreStore';
        $start_year = Yii::app()->request->getParam('start_year');
        $grade = Yii::app()->request->getParam('grade');
        $term = Yii::app()->request->getParam('term');
        $zk_course = Yii::app()->request->getParam('zk_course');
        $first_rule = Yii::app()->request->getParam('first_rule');
        $second_rule = Yii::app()->request->getParam('second_rule',array());
        $this->remote($requestUrl, array(
            'start_year'  => $start_year,
            'grade'       => $grade,
            'term'        => $term,
            'zk_course'   => $zk_course,
            'first_rule'  => $first_rule,
            'second_rule' => $second_rule,
        ), 'POST');
    }


    public function remote($requestUrl, $requestData = array(), $method)
    {
        if (empty($requestData['school_id'])) {
            $requestData['school_id'] = $this->branchId;
        }
        $res = CommonUtils::requestDsOnline2($requestUrl, $requestData, $method);
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        }
        $this->showMessage();
    }


}