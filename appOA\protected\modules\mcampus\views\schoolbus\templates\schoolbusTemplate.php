<?php
$labels = SchoolBus::attributeLabels();
$stateList = array(
            10 => '有效',
            20 => '暂停',
        );
Yii::import('common.models.operations.IvyVendorCitylink');
Yii::import('common.models.operations.IvyVendor');
$cityModel = Term::model()->city()->findAll();
$comment = Yii::app()->db->createCommand();
$comment->from(Term::model()->tableName());
$comment->where('category_id=:category_id', array(':category_id'=>7));
$comment->order('weight ASC');
$city = $comment->queryAll();
$cityList = array();
if (count($city)){
    foreach ($city as $val){
        $cityList[$val['diglossia_id']] =  CommonUtils::autoLang($val['cntitle'], $val['entitle']);
    }
}
unset($city);
$criteria = new CDbCriteria();
$criteria->compare('vendor.vendor_type_id', array(2, 15));//2:租赁公司15:校园车辆
$items = IvyVendorCitylink::model()->with('vendor')->findAll($criteria);
$vendorList = array();
if ($items){
    foreach ($items as $item) {
        $vendorList[$item->diglossia_id][$item->vendor_id] = CommonUtils::autoLang($item->vendor->cn_title, $item->vendor->en_title);
    }
}
?>
<script>
var vendorList = <?php echo CJSON::encode($vendorList);?>
</script>
<script type="text/template" id="schoolbus-item-template">
<div class="form-group">
    <?php echo CHtml::label($labels['bus_title'], CHtml::getIdByName('SchoolBus[bus_title]'), array('class'=>'col-sm-3 control-label'));?>
    <div class="col-sm-9">
        <?php echo CHtml::textField('SchoolBus[bus_title]', '<%= bus_title %>', array('class'=>'form-control','encode'=>false));?>
    </div>
</div>
<div class="form-group">
    <?php echo CHtml::label($labels['bus_code'], CHtml::getIdByName('SchoolBus[bus_code]'), array('class'=>'col-sm-3 control-label'));?>
    <div class="col-sm-9">
        <?php echo CHtml::textField('SchoolBus[bus_code]', '<%= bus_code %>', array('class'=>'form-control','encode'=>false));?>
    </div>
</div>
<div class="form-group">
    <?php echo CHtml::label($labels['bus_type'], CHtml::getIdByName('SchoolBus[bus_type]'), array('class'=>'col-sm-3 control-label'));?>
    <div class="col-sm-9">
        <?php echo CHtml::textField('SchoolBus[bus_type]', '<%= bus_code %>', array('class'=>'form-control','encode'=>false));?>
    </div>
</div>
<div class="form-group">
    <?php echo CHtml::label($labels['acctual_children'], CHtml::getIdByName('SchoolBus[acctual_children]'), array('class'=>'col-sm-3 control-label'));?>
    <div class="col-sm-9">
        <?php echo CHtml::textField('SchoolBus[acctual_children]', '<%= acctual_children %>', array('class'=>'form-control','encode'=>false));?>
    </div>
</div>
<div class="form-group">
    <?php echo CHtml::label($labels['driver_name'], CHtml::getIdByName('SchoolBus[driver_name]'), array('class'=>'col-sm-3 control-label'));?>
    <div class="col-sm-9">
        <?php echo CHtml::textField('SchoolBus[driver_name]', '<%= driver_name %>', array('class'=>'form-control','encode'=>false));?>
    </div>
</div>
<div class="form-group">
    <?php echo CHtml::label($labels['driver_mobile'], CHtml::getIdByName('SchoolBus[driver_mobile]'), array('class'=>'col-sm-3 control-label'));?>
    <div class="col-sm-9">
        <?php echo CHtml::textField('SchoolBus[driver_mobile]', '<%= driver_mobile %>', array('class'=>'form-control','encode'=>false));?>
    </div>
</div>
<div class="form-group">
    <?php echo CHtml::label($labels['aunt_name'], CHtml::getIdByName('SchoolBus[aunt_name]'), array('class'=>'col-sm-3 control-label'));?>
    <div class="col-sm-9">
        <?php echo CHtml::textField('SchoolBus[aunt_name]', '<%= aunt_name %>', array('class'=>'form-control','encode'=>false));?>
    </div>
</div>
<div class="form-group">
    <?php echo CHtml::label($labels['aunt_mobile'], CHtml::getIdByName('SchoolBus[aunt_mobile]'), array('class'=>'col-sm-3 control-label'));?>
    <div class="col-sm-9">
        <?php echo CHtml::textField('SchoolBus[aunt_mobile]', '<%= aunt_mobile %>', array('class'=>'form-control','encode'=>false));?>
    </div>
</div>
<div class="form-group">
    <?php echo CHtml::label($labels['state'], CHtml::getIdByName('SchoolBus[state]'), array('class'=>'col-sm-3 control-label'));?>
    <div class="col-sm-9">
        <?php echo CHtml::dropDownList('SchoolBus[state]','<%= state %>',$stateList, array('class'=>'form-control',
            'encode'=>false,
            'empty'=>Yii::t('global','Please Select')));?>
    </div>
</div>
<div class="form-group">
    <?php echo CHtml::label($labels['city_id'], CHtml::getIdByName('SchoolBus[city_id]'), array('class'=>'col-sm-3 control-label'));?>
    <div class="col-sm-9">
        <?php echo CHtml::dropDownList('SchoolBus[city_id]','<%= city_id %>',$cityList, array('class'=>'form-control',
            'encode'=>false,
            'empty'=>Yii::t('global','Please Select'),
            'onchange'=>'selectVendor(this,0);'));?>
    </div>
</div>
<div class="form-group">
    <?php echo CHtml::hiddenField('bid', '<%= bid %>',array('encode'=>false));?>
    <?php echo CHtml::label($labels['vendor_id'], CHtml::getIdByName('SchoolBus[vendor_id]'), array('class'=>'col-sm-3 control-label'));?>
    <div class="col-sm-9">
        <?php echo CHtml::dropDownList('SchoolBus[vendor_id]','<%= vendor_id %>',array(), array('class'=>'form-control',
            'encode'=>false,
            'empty'=>Yii::t('global','Please Select')));?>
    </div>
</div>
</script>
