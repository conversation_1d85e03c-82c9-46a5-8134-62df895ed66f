<style>
    .table {
    font-family: verdana,arial,sans-serif;
    font-size:11px;
    color:#333333;
    border-width: 1px;
    border-color: #666666;
    border-collapse: collapse;
    width: 100%;
}
.table th {
    border-width: 1px;
    padding: 8px;
    border-style: solid;
    border-color: #666666;
    background-color: #dedede;
}
.table td {
    border-width: 1px;
    padding: 8px;
    border-style: solid;
    border-color: #666666;
    background-color: #ffffff;
}
</style>
<div>
    <div style="font-size:15px;margin-bottom:10px"><?php
        $star = date("Y-m-d", $weeklinkModel->monday_timestamp);
        $endstr = $weeklinkModel->monday_timestamp + 86400 * 4 ;
        $end = date("Y-m-d", $endstr);
        echo $branchId . ' - ' . $weeklinkModel->week_num . ' 周 ' . $star . ' - ' . $end;
        ?>
    </div>
    <?php
    foreach ($exitData as $key=>$val){
        echo '<div style="font-size:14px;background: #f0f0f0;height: 30px;line-height: 30px;margin-bottom:10px;padding-left: 5px;margin-top: 20px;
    border-top: 1px solid #ddd;">'. $lunch[$key] .'</div>';
        echo  '<table class="table">
                    <thead>
                        <tr>
                            <th width="20%"></th>
                            <th width="40%">之前数据</th>
                            <th width="40%">修改数据</th>
                        </tr>
                    </thead>
                    <tbody>';
        foreach ($val as $week=>$item){
           echo '<tr>
                    <td>' . $weekData[$week] . '</td>
                    <td>' . $item['old'] . '</td>
                    <td>' . $item['new'] . '</td>
                </tr>';
        }
        echo  '</tbody>
                </table>';
    }  ?>
</div>