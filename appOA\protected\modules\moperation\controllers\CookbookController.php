<?php

class CookbookController extends ProtectedController
{
    public $batchNum = 30;
    public $actionAccessAuths = array(
    	'index'=>'oOperationsView',
    );

    public function init() {
        parent::init();
        Yii::import('common.models.calendar.*');
        Yii::import('common.models.lunchmenu.*');

        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'hqOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','HQ Operations');
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue2.js');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile($cs->getCoreScriptUrl().'/jui/js/jquery-ui.min.js');
    }

	public function actionIndex()
	{
        $weekTime = Yii::app()->request->getParam('weekTime', '');

        $nowtime = ($weekTime) ? strtotime($weekTime) : strtotime(date("Y-m-d",time()));

        $lunchModels = Term::model()->lunch()->findAll();
        foreach($lunchModels as $lunch){
            $lunchs[$lunch->diglossia_id] = $lunch->getContent();
        }

        $model = new Branch();
        $branch = $model->getBranchList();
        /*unset($branch['BJ_DS']);
        unset($branch['BJ_SLT']);*/
        // 根据时间取数据  $nowtime 为任意一天的时间戳
        $monthData = $this->getCookBookData($nowtime);

		$this->render('index', array(
		    'lunchs' => $lunchs,
		    'monthData' => $monthData,
		    'branch' => $branch,
		    'weekTime' => $weekTime,
        ));
	}



	// 根据任意一天的时间戳，来获取那一周开始的时间戳，  开始安周一算
	public function getCookBookData($weekTime)
    {
        //  根据时间获取当前周一的时间戳
        $dateN = date('N', $weekTime);
        $monday = $weekTime - ($dateN-1)*3600*24;
        //$school = array('BJ_DS','BJ_SLT');
        $criteria = new CDbCriteria();
        $criteria->compare('monday_timestamp', $monday);
        //$criteria->addNotInCondition('schoolid', $school);
        $criteria->addCondition("menu_id>0 OR allergy_id>0");
        $weeklinkModel = CateringMenuWeeklink::model()->findAll($criteria);
        $monthData = array();
        $commonWeekCate = array();
        $allergyWeekCate = array();
        if($weeklinkModel) {
            foreach ($weeklinkModel as $val) {
                $commonData = array();
                if($val->common) {
                    $commonWeekCate = ($val->common->week_cate) ? unserialize($val->common->week_cate) : array('mon', 'tue', 'wed', 'thu', 'fri');
                    foreach ($val->common->dailyMenus as $item) {
                        $commonData[$item->category][$item->weekday] = array(
                            'title' => nl2br($item->food_list),
                            'photo' => ($item->photo) ? $photoPath = Yii::app()->params['OAUploadBaseUrl']. '/lunch/' . $item->photo : "",
                        );
                    }
                }

                $allergyData = array();
                $allergyWeekCate = array();
                if($val->allergy) {
                    $allergyWeekCate = ($val->allergy->week_cate) ? unserialize($val->allergy->week_cate) : array('mon', 'tue', 'wed', 'thu', 'fri');
                    foreach ($val->allergy->dailyMenus as $item) {
                        $allergyData[$item->category][$item->weekday] = array(
                            'title' => nl2br($item->food_list),
                            'photo' => ($item->photo) ? $photoPath = Yii::app()->params['OAUploadBaseUrl']. '/lunch/' . $item->photo : "",
                        );
                    }
                }

                $monthData[$val->schoolid] = array(
                    'id' => $val->id,
                    'weekNum' => $val->week_num,
                    'title' => $val->common->title_cn,
                    'status' => $val->status,
                    'menu_cate' => ($val->common->menu_cate) ? unserialize($val->common->menu_cate) : array(),
                    'menu_cate_allergy' => ($val->allergy->menu_cate) ? unserialize($val->allergy->menu_cate) : array(),
                    'commonWeekCate' => $commonWeekCate,
                    'allergyWeekCate' => $allergyWeekCate,
                    'commonData' => $commonData,
                    'allergyData' => $allergyData,

                );
            }
        }

        return $monthData;
    }

    public function actionEdit()
    {
        $id = Yii::app()->request->getParam('id', '');

        if($id && Yii::app()->user->id == 8020811){
            $model = CateringMenuWeeklink::model()->findByPk($id);
            if($model){
                $model->status = ($model->status == 1) ? 0 : 1;
                $model->review_by = Yii::app()->user->id;
                $model->review_at = time();
                if($model->save()){
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message','success'));
                    $this->addMessage('callback', 'cbVisit');
                } else {
                    $this->addMessage('state', 'fail');
                    $err = current($model->getErrors());
                    $this->addMessage('message', $err ? $err[0] : '失败');
                }
                $this->showMessage();
            }
        }
        $this->addMessage('state', 'fail');
        $this->addMessage('message', '错误');
        $this->showMessage();
    }
}
