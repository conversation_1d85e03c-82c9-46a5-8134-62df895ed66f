<div style="color:#274257;">

    <div style="font-family: 'Microsoft Yahei'">

        <p>亲爱的家长，</p>

        <p>此邮件确认 [<?php echo $child->stat->classInfo->title; ?>] 班的 [<?php echo $child->getChildName(); ?>] 修改了午餐信息：</p>

        <table cellspacing="0" cellpadding="0" border="1" align="center" style="border-collapse:collapse;width:320px;">
            <tr>
                <th width="50%" style="color:#000000;font-family:'Microsoft Yahei';font-size: 12px;" bgcolor="#f2f2f2">操作
                </th>
                <th width="50%" style="color:#999999;font-family:'Microsoft Yahei';font-size: 12px;" bgcolor="#dedede">目标日期
                </th>
            </tr>
            <tr>
                <td style="color:#000000;font-size:12px;padding:4px;font-family:'Microsoft Yahei'" bgcolor="#f2f2f2">
                    <?php
                    if ('cancel' == $action) {
                        echo '取消午餐';
                    } else if ('recover' == $action) {
                        echo '恢复午餐';
                    }
                    ?>
                </td>
                <td style="color:#999999;font-size:12px;padding:4px;font-family:'Microsoft Yahei'" bgcolor="#dedede">
                    <?php echo $targetDate; ?>
                </td>
            </tr>
        </table>

        <p>如遇到问题，请联系校园 <?php echo CHtml::link($supportEmail, 'mailto:' . $supportEmail) ?>。</p>

        <p>
            <?php echo CommonUtils::formatDateTime(time(), 'medium', 'short'); ?>
        </p>

        <p>该邮件为系统自动发送。<?php echo $otherinfo;?></p>

    </div>


</div>
<div style="height:0; border-bottom: 1px solid #f2f2f2; margin: 0.2em 0;"></div>


<div style="color:#644436;">

    <div style="font-family: 'Trebuchet MS','Microsoft Yahei',Arial,Helvetica,sans-serif">

        <p>Dear Parents,</p>

        <p>This confirms that [<?php echo $child->getChildName(); ?>] at class [<?php echo $child->stat->classInfo->title; ?>] has made the following change to his/her lunch:</p>

        <table cellspacing="0" cellpadding="0" border="1" align="center" style="border-collapse:collapse;width:320px;">
            <tr>
                <th width="50%" style="color:#000000;font-family:'Trebuchet MS';font-size: 12px;" bgcolor="#f2f2f2">Operation
                </th>
                <th width="50%" style="color:#999999;font-family:'Trebuchet MS';font-size: 12px;" bgcolor="#dedede">Date
                </th>
            </tr>
            <tr>
                <td style="color:#000000;font-size:12px;padding:4px;font-family:'Trebuchet MS'" bgcolor="#f2f2f2">
                    <?php
                    if ('cancel' == $action) {
                        echo 'Cancel Lunch';
                    } else if ('recover' == $action) {
                        echo 'Resume Lunch';
                    }
                    ?>
                </td>
                <td style="color:#999999;font-size:12px;padding:4px;font-family:'Trebuchet MS'" bgcolor="#dedede">
                    <?php echo $targetDate; ?>
                </td>
            </tr>
        </table>

        <p>If you have any questions, please feel free to contact the campus <?php echo CHtml::link($supportEmail, 'mailto:' . $supportEmail) ?>.</p>

        <p>
            <?php echo CommonUtils::formatDateTime(time(), 'medium', 'short'); ?>
        </p>

        <p>This is an automatic email.</p>

    </div>

</div>
<div style="height:0; border-bottom: 1px solid #f2f2f2; margin: 0.2em 0;"></div>