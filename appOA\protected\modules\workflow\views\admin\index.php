<?php 

$clabels = $definationModel->attributeLabels();
$definationList = WorkflowDefination::model()->findAll();
if (!empty($definationList)){
    foreach ($definationList as $val){
        $workflowDefinationData[$val->defination_id] = $val->getAttributes();
        $workflowDefinationData[$val->defination_id]['isNew'] = false;
    }
}
?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','HQ Operations'), array('/moperation'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Basic Configurations'), array('/moperation'))?></li>
        <li class="active"><?php echo Yii::t('site','Workflow Management');?></li>
    </ol>
    <div class="row"> 
        <div class="col-sm-12">
            <button type="button" class="btn btn-primary mb10" onclick="newWorkflowTemplate(0)"><span class="glyphicon glyphicon-plus"></span> 新建工作流程</button>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-2">
            <div class="list-group" id="workflow-status-list">
                <a data-vfilter="-1" href="javascript:void(0)" class="list-group-item status-filter" onclick="filterStatus(-1);"><?php echo Yii::t('global', 'All');?> <span class="badge"></span></a>
                <?php foreach (WorkflowDefination::getStatus() as $key => $val):?>
                    <a data-vfilter="<?php echo $key; ?>" href="javascript:void(0)" class="list-group-item status-filter" onclick="filterStatus(<?php echo $key; ?>);"><?php echo $val;?> <span class="badge"></span></a>
                <?php endforeach;?>
            </div>
        </div>
        <div class="col-md-10">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                    <tr class="active">
                        <th width="180"><?php echo $clabels['defination_name_cn'];?></th>
                        <th width="180"><?php echo $clabels['defination_name_en'];?></th>
                        <th width="140" class="text-center"><?php echo $clabels['defination_type'];?></th>
                        <th width="140" class="text-center"><?php echo $clabels['branch_type'];?></th>
                        <th width="140" class="text-center"><?php echo $clabels['defination_handle'];?></th>
                        <th width="150" class=""><?php echo $clabels['status'];?></th>
                        <th width="150" class=""><?php echo $clabels['display'];?></th>
                        <th width="100"><?php echo Yii::t('global','Action');?></th>
                    </tr>
                    </thead>
                    <tbody id="workflow_list">

                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- 工作流程模板 -->
<div class="modal fade" id="newWorkflowModal" tabindex="-1" role="dialog" aria-labelledby="newWorkflowModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel"><?php echo '工作流程';?> <small></small></h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="well">
                            说明
                        </div>
                        <form class="form-horizontal J_ajaxForm" action="<?php echo $this->createUrl('//workflow/admin/saveDefination');?>" method="POST">
                            <div id="form-data">
                                <!--place holder-->
                            </div>

                            <div class="pop_bottom">
                                <button onclick="$('#newWorkflowModal').modal('hide')" type="button" class="btn btn-default pull-right"><?php echo Yii::t('global','Cancel');?></button>
                                <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global','Submit');?></button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $this->renderPartial('templates/backboneTemplate',array('clabels'=>$clabels,'rolesArray'=>$rolesArray));?>
<script type="text/javascript">
    var basicEditWorkflowTemplate = _.template($('#workflow-basicEdit-template').html());
    var workflowDefinationData = <?php echo CJSON::encode($workflowDefinationData);?>;
    var workflowListTemplate = _.template($('#workflow-list-template').html());
    var statsList = <?php echo CJSON::encode( WorkflowDefination::getStatus());?>;
    var displayList = <?php echo CJSON::encode( WorkflowDefination::getDisplay());?>;
    var oStat = 1; //当前过滤状态
    $(function(){
        //初始化教室显示
        var _container = $('#workflow_list');
        displayWorkflowList = function(){
            _container.empty();
            if (!_.isEmpty(workflowDefinationData)){
                statusWorkflowDefinationData = _.sortBy(workflowDefinationData,function(num){return - num.status; });
                $.each(statusWorkflowDefinationData, function(m, item){
                    _container.append(workflowListTemplate(item));
                })
            }
            _container.find('tr[data-status|="0"]').addClass('warning');
            renderStatsBadge();
            filterStatus(oStat)
        }
        
        //根据工作流状态过滤
        filterStatus = function(stat){
            oStat = stat;
            $('#workflow-status-list a').removeClass('active');
            $('#workflow-status-list a[data-vfilter|="'+oStat+'"]').addClass('active');
            if(oStat == -1){
                $('#workflow_list').find('tr').show();
            }else{
                $('#workflow_list').find('tr').hide();
                $('#workflow_list').find('tr[data-status|="'+oStat+'"]').show();
            }
        }
        
        //渲染工作流统计
        renderStatsBadge = function(){
            if (!_.isEmpty(workflowDefinationData)){
                workflowDefinationKeys = _.keys(workflowDefinationData);
                workflowNum = workflowDefinationKeys.length;
            }else{
               workflowNum = 0;
            }
            
            $('#workflow-status-list').find("a[data-vfilter|=-1]").find('span.badge').html(workflowNum);
            statusGroupedWorkflow = _.groupBy(workflowDefinationData, 'status');
            _.each(statsList, function(item,index){
                $('#workflow-status-list').find('a[data-vfilter|="'+index+'"]').find('span.badge').html(_.isUndefined(statusGroupedWorkflow[index]) ? 0 : statusGroupedWorkflow[index].length);
            })
        }
        
        //新建工作流程
        newWorkflowTemplate = function(workflowId){
            $('#J_fail_info').remove();
            $('#newWorkflowModal').modal();
            displayWorkflowEditForm(workflowId,'#newWorkflowModal #form-data');
        }
    
        displayWorkflowEditForm = function(workflowId, domWrapperId){
            if (workflowId == 0){
                editWorkflowDefinationData = {'defination_id':0,'defination_name_cn':'','defination_name_en':'','defination_type':'','defination_handle':'','node_order':'','start_user_role':'','status':'1','display':'1','branch_type':'','defination_memo':'','isNew':true};
            }else{
                editWorkflowDefinationData = workflowDefinationData;
                editWorkflowDefinationData = editWorkflowDefinationData[workflowId];
            }
            var _view = basicEditWorkflowTemplate(editWorkflowDefinationData);
            $(domWrapperId).html(_view);
            if(editWorkflowDefinationData.isNew === false){
                    $('#form-data #id').val(workflowId);
                    if (editWorkflowDefinationData.status ==1){
                        $('#form-data #WorkflowDefination_status_0').attr("checked",true);
                    }else{
                        $('#form-data #WorkflowDefination_status_1').attr("checked",true);
                    }
                    if (editWorkflowDefinationData.display ==1){
                        $('#form-data #WorkflowDefination_display_0').attr("checked",true);
                    }else{
                        $('#form-data #WorkflowDefination_display_1').attr("checked",true);
                    }
                    $("#form-data #WorkflowDefination_defination_type").attr("value",editWorkflowDefinationData.defination_type);
                    $("#form-data #WorkflowDefination_branch_type").attr("value",editWorkflowDefinationData.branch_type);
            }else{
                $(domWrapperId + ' ' + 'a[enableEdit]').hide();
            }
        }
        
        //更工作流信息回调函数
        updateCallback = function(data){
            $('#newWorkflowModal').modal('hide');
            workflowDefinationData[data.defination_id] = data;
            displayWorkflowList();
            head.Util.ajaxDel();
        }
        
        deleteCallback = function(data){
            delete workflowDefinationData[data.defination_id];
            displayWorkflowList();
            head.Util.ajaxDel();
        }
        
        //初始化
        displayWorkflowList();
        head.Util.ajaxDel();
    })
</script>