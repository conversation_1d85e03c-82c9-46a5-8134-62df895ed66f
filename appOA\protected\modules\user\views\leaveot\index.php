<?php
$label = LeaveItem::attributeLabels();
?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo Yii::t('site','User Center');?></li>
        <li class="active">休假及加班</li>
    </ol>

    <div class="row">
        <div class="col-md-12">
            <div class="mb10">
                <span class="dropdown">
                    <a href="javascript:void(0)" class="btn btn-success dropdown-toggle" role="button" data-toggle="dropdown" onclick="getBehalfApplicationData(this);">
                        休假加班代申请
                        <span class="caret"></span>
                    </a>
                    <div class="dropdown-menu p20" id="staff-list">
                        <!-- place holder -->
                    </div>
                </span>
                <a href="<?php echo $this->createUrl('//user/leaveot/checkList');?>" target="_blank" class="btn btn-danger" role="button">休假加班审核</a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-2 col-sm-4 hidden-xs">
            <div class="thumbnail">
				<?php
					if($this->userInfo->user_avatar):
						echo CHtml::image(OA::CreateOAUploadUrl('users', $this->userInfo->user_avatar),'',array("class"=>"img-rounded"));
                    endif;
                    ?>
                <div class="caption">
                    <h4 class="text-center"><strong><?php echo $this->userInfo->getName();?></strong></h4>
                    <h4 class="text-center"><?php echo $this->userInfo->email;?></h4>
                    <p class="text-center"><a href="javascript:void(0)" class="btn btn-primary" role="button">Edit Profile</a></p>
                </div>
            </div>
        </div>
        <div class="col-md-2 col-sm-4">
            <ul class="list-group">
				<?php foreach ($appLabel as $labKey=>$labVal):
                    $remain = '';
                    ?>
                <li href="javascript:void(0);" class="list-group-item">
                    <h3 class="list-group-item-heading">
						<?php echo $labVal['label'];?>
                        <?php if (($labKey<=UserLeave::USER_TYPE_OVERTIME) && isset($leaveCountList[$labKey])):?>
                            <a title="查看详细信息" href="javascript:void(0);" onclick="leaveView(<?php echo $labKey;?>);"><span class="glyphicon glyphicon-info-sign"></span></a>
                        <?php endif;?>
                        <?php
                            if( isset( $leaveList[$labKey] ) && isset( $leaveList[$labKey]['sum'] ) && $leaveList[$labKey]['sum'] > 0 ):
                            $remain = sprintf(Yii::t('user','共余%s'),UserLeave::getLeaveFormat($leaveList[$labKey]['sum']));
                        ?>
                            <span title="<?php echo $remain;?>" class="label label-info"><?php echo ceil( $leaveList[$labKey]['sum'] / UserLeave::DAY ) ;?></span>
                        <?php
                            endif;
                        ?>
					</h3>
					<?php if (isset($labVal['subtitle'])):?>
						<p class="list-group-item-text text-muted">
							<?php
								$strVar ="";
                                if ($labKey > UserLeave::USER_TYPE_OVERTIME):
                                    foreach ($labVal['subtitle'] as $val){
                                        $strVar.= ($strVar) ?  ' , '.$val : $val;
                                    }
                                    echo $strVar;
                                endif;
							?>
						</p>
					<?php endif;?>
					<?php if (isset($leaveList[$labKey])):?>
                        <?php foreach ($leaveList[$labKey]['list'] as $val):?>
                            <p class="list-group-item-text text-muted">
                                <?php echo sprintf(Yii::t('user','%s有效期至%s'),UserLeave::getLeaveFormat($val['hours']),  OA::formatDateTime($val['enddate']));?><br/>
                            </p>
                        <?php endforeach;?>
					<?php endif;?>
                    <?php if (isset($leaveCountList[$labKey])):?>
                        <?php foreach ($leaveCountList[$labKey] as $key=>$val):?>
                            <?php if (isset($leaveType[$key])):?>
                                <p class="list-group-item-text text-muted">
                                    <?php echo $leaveType[$key].' : '.$val['sumTxt'];?>
                                </p>
                            <?php endif;?>
                        <?php endforeach;?>
                    <?php endif;?>
					<?php if (isset($labVal['appButton']) && $labVal['appButton'] ===true):?>
						<div class="text-right">
							<div class="btn-group">
								<?php if (isset($labVal['menu']) && count($labVal['menu'])):?>
                                <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown"
                                    <?php if (in_array($labKey, array(UserLeave::TYPE_ANNUAL_LEAVE,UserLeave::TYPE_DAYS_OFF)) && $leaveList[$labKey]['sum']== 0)
                                    {
                                        echo "disabled";
                                    }
                                    ?>
                                >
										<span class="glyphicon glyphicon-plus"></span> <?php echo Yii::t('hr', '申请');?>
									</button>
									<ul class="dropdown-menu pull-right" role="menu" aria-labelledby="dLabel">
										<?php foreach ($labVal['menu'] as $val):?>
										<?php
											$htmlOption = '';
											if (isset($val['htmlOption'])){
												foreach ($val['htmlOption'] as $k=>$v){
													$htmlOption .=  $k.'="'.$v.'"';
												}
											}
										?>
										<li role="presentation"><a <?php echo $htmlOption;?>><?php echo $val['label'];?></a></li>
										<?php endforeach;?>
									</ul>
								<?php else:?>
										<?php
											$htmlOption = '';
											if (isset($labVal['htmlOption'])){
												foreach ($labVal['htmlOption'] as $k=>$v){
													$htmlOption .=  $k.'="'.$v.'"';
												}
											}
										?>
									<a <?php echo $htmlOption;?>>
									<button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
										 <span class="glyphicon glyphicon-plus"></span><?php echo Yii::t('hr', '申请');?>
									</button>
									</a>
								<?php endif;?>
							</div>
						</div>
					<?php endif;?>
                </li>
				<?php endforeach;?>
            </ul>
        </div>
        <div class="col-md-8 col-sm-4">
			<?php echo CHtml::form($this->createUrl('/user/leaveot/saveLeave'), 'post', array('class'=>'J_ajaxForm form-horizontal','role'=>'form','id'=>'leave-form'));?>
                <div id="form-list">

                </div>
			<?php CHtml::endForm(); ?>
            <?php if (count($historyData->getData())):?>
            <div class="panel panel-default">
                <div class="panel-heading"><?php echo Yii::t('hr', '待审批申请');?></div>
                <div class="panel-body">
                    <?php
                    $this->widget('ext.ivyCGridView.BsCGridView', array(
                        'id' => 'child-list',
                        'dataProvider' => $historyData,
                        'template' => "{items}{pager}",
                        'enableSorting'=>false,
                        'colgroups' => array(
                            array(
                                "colwidth" => array(120, 180, 120, 120),
                            )
                        ),
                        'columns' => array(
                            'type' => array(
                                'name' => 'type',
                                'value' => 'UserLeave::getLeaveType($data->type)',
                            ),
                            'startdate' => array(
//                                'name' => 'startdate',
                                'header' => Yii::t('hr', 'Date'),
                                'type' => 'raw',
                                'value' => 'UserLeave::showLeavePeriod($data->getattributes())'
                            ),
                            'hours' => array(
                                'name' => 'hours',
                                'header' => 'Period',
                                'value'=>'UserLeave::getLeaveFormat($data->hours)',
                            ),
                            'status' => array(
                                'name' => 'status',
                                'value' => 'LeaveItem::getStatus($data->status)',
                            ),
                            'creator' => array(
                                'name' => 'creator',
                                'value' => '$data->creatorUser->getName()',
                            ),
                            'created' => array(
                                'name' => 'created',
                                'value'=>'OA::formatDateTime($data->created)',
                            ),
                            array(
                                'class' => 'CButtonColumn',
                                'template' => '{update}',
                                'updateButtonUrl' => 'Yii::app()->controller->createUrl("/user/leaveot/delete", array("id" =>$data->id))',
                                'updateButtonImageUrl'=>false,
                                'updateButtonLabel'=>'<span class="glyphicon glyphicon-trash"></span>',
                                'updateButtonOptions'=>array('class'=>'J_ajax_del btn btn-danger btn-xs','title'=>'撤销'),
                            ),
                        )
                    ));
                    ?>
                </div>
            </div>
            <?php endif;?>
            <?php if (count($rejectData->getData())):?>
            <div class="panel panel-default">
                <div class="panel-heading"><?php echo Yii::t('hr', '被拒申请');?></div>
                <div class="panel-body">
                    <?php
                    $this->widget('ext.ivyCGridView.BsCGridView', array(
                        'id' => 'child-list',
                        'dataProvider' => $rejectData,
                        'template' => "{items}{pager}",
                        'enableSorting'=>false,
                        'colgroups' => array(
                            array(
                                "colwidth" => array(120, 180, 120, 250),
                            )
                        ),
                        'columns' => array(
                            'type' => array(
                                'name' => 'type',
                                'value' => 'UserLeave::getLeaveType($data->type)',
                            ),
                            'startdate' => array(
//                                'name' => 'startdate',
                                'header' => Yii::t('hr', 'Date'),
                                'type' => 'raw',
                                'value' => 'UserLeave::showLeavePeriod($data->getattributes())'
                            ),
                            'hours' => array(
                                'name' => 'hours',
                                'header' => 'Period',
                                'value'=>'UserLeave::getLeaveFormat($data->hours)',
                            ),
                            'check_desc' => array(
                                'name' => 'check_desc',
                                'value' => '$data->check_desc',
                            ),
                            'approver' => array(
                                'name' => 'approver',
                                'value' => '$data->checkUser->getName()',
                            ),
                            'approvedate' => array(
                                'name' => 'approvedate',
                                'value'=>'OA::formatDateTime($data->approvedate)',
                            ),
                            'created' => array(
                                'name' => 'created',
                                'value'=>'OA::formatDateTime($data->created)',
                            )

                        )
                    ));
                    ?>
                </div>
            </div>
            <?php endif;?>
        </div>
    </div>
</div>
<!-- Modal -->
<div class="modal" id="leaveViewModal" tabindex="-1" role="dialog" aria-labelledby="leaveViewModal" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel"></h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th width="150"><h4><?php echo $label['type'];?></h4></th>
                                    <th width="200"><h4><?php echo Yii::t('hr', 'Date');?></h4></th>
                                    <th width="80" class="text-center"><h4><?php echo Yii::t('hr', 'Period');?></h4></th>
                                    <th width="150" class="text-center"><h4><?php echo $label['created'];?></h4></th>
                                </tr>
                            </thead>
                            <tbody id="leave-list-item">

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo $this->renderPartial('user.views.leaveot.templates._form');?>
<script>
    var lang = <?php echo CJSON::encode(strtolower(Yii::app()->language));?>;
	var appLabel = <?php echo CJSON::encode($appLabel);?>;
	var leaveList = <?php echo CJSON::encode($leaveList);?>;
	var leaveCountList = <?php echo CJSON::encode($leaveCountList);?>;
    var startDate = '';
    var staffList = '';
    var template = _.template($('#form-template').html());
    var staffItemTemplate = _.template($('#staff-item-template').html());
    var leaveCountListTemplate = _.template($('#leave-item-template').html());
    lang = (lang == "zh_cn") ? lang : "";
	function showUserForm(type,methods){
		$.datepicker.setDefaults($.datepicker.regional[lang]);
        var view = template({type:type, appLabel:appLabel[type],leaveList:leaveList,methods:methods});
        $("#form-list").html(view);
		if (methods == 'hour'){
			$('#form-list #LeaveItem_startdate').datepicker({'changeMonth':true,'changeYear':true,'dateFormat':'yy-mm-dd'});
		}else if(methods == 'more'){
			$('#form-list #LeaveItem_startdate').datepicker({'changeMonth':true,'changeYear':true,'dateFormat':'yy-mm-dd'});
			$('#form-list #LeaveItem_enddate').datepicker({'changeMonth':true,'changeYear':true,'dateFormat':'yy-mm-dd'});
		}else if (methods == 'day'){
			$('#form-list #LeaveItem_enddate_container').datepicker({
				numberOfMonths: 2,
				onSelect: onSelect,
				beforeShowDay: beforeShowDay,
			});
            removeCss();
		}
        head.Util.ajaxForm();
	}
	function resetForm(_this){
        removeCss();
		document.getElementById('leave-form').reset();
	}

	function onSelect(dateText, inst){
		if (dateText){
			var d = inst.selectedYear.toString()+'-'+(inst.selectedMonth+1).toString()+'-'+inst.selectedDay.toString();
			if (startDate){
				var startDateList = startDate.split(",");
				_exist = $.inArray(d,startDateList);
				if (_exist>=0){
					startDateList.splice(_exist,1);
					startDate = startDateList.toString();
				}else{
					startDate = startDate+','+d;
				}
			}else{
				startDate = d;
			}
            var selectedDayNumber = startDate.split(",").length;
            if (selectedDayNumber > 0){
                $("#selected-day").removeClass('hide');
                $("#selected-day span.selected-day-number").html(selectedDayNumber);
            }
		}
		$("#LeaveItem_startdate").val(startDate);
	}

    function emptySelectedDay(){
        $("#selected-day").addClass('hide');
        $("#selected-day span.selected-day-number").html(0);
        removeCss();
    }

	function beforeShowDay(date){
		var d = date.getFullYear().toString()+'-'+(date.getMonth()+1).toString()+'-'+date.getDate().toString();
		var cssClass = 'date-active';
		if (startDate){
			var startDateList = startDate.split(",");
			_exist = $.inArray(d,startDateList);
			if (_exist>=0){
				return [true, cssClass];
			}else{
				return [true];
			}
		}
		return [true];
	}

    //清除默认选中的CSS样式
    function removeCss(){
        var obj = $("#LeaveItem_enddate_container table.ui-datepicker-calendar tbody tr td");
        $.each(obj,function(i,item){
            $(item).removeClass('date-active');
        });
        $("#LeaveItem_startdate").val('');
        startDate = $('#LeaveItem_startdate').val();
    }

    //代申请业务
    function getBehalfApplicationData(obj){
        var dataFlag = $(obj).attr('data-init');
        if (_.isUndefined(dataFlag)){
            if (!_.isObject(staffList['data'])){
                $('#staff-list').html('Loading...');
                loadData();
                $('#staff-list').empty();
                if (!_.isEmpty(staffList['data'])){
                    _.each(staffList['data'],function(item,i){
                        var _staff = _.first( $(staffItemTemplate(item) ));
                        var a = $(_staff).find('a[basehref]');
                        var href = a.attr('basehref');
                        href = ( href.indexOf('?') < 0 ) ? href + '?userid=' + a.attr('userid') : href + '&userid=' + a.attr('userid');
                        a.attr('href', href );
                        $('#staff-list').append(_staff);
                    })
                    $('#staff-list').css('width','600px').css('margin-top','10px');

                }else{
                    $('#staff-list').html('没权限');
                }
            }
            $(obj).attr('data-init', 'on')
        }

    }

    //AJAX取代申请数据
    function loadData(){
        $.ajax({
            type: "Post",
            dataType: "json",
            async: false,
            url: "<?php echo $this->createUrl('//user/leaveot/getBehalfApplicationData'); ?>"
        }).done(function(data) {
            staffList = data;
        });
    }

    function leaveView(id){
        $('#leaveViewModal').modal();
        $('#myModalLabel').html(appLabel[id].label);
        var _container = $("#leave-list-item");
        _container.empty();
        $.each(leaveCountList[id].list,function(i,item){
            _container.append(leaveCountListTemplate(item));
        });
    }

</script>

<style>
    p.list-group-item-text{margin-bottom: 0.4em; padding-left: .5em;}
    h3.list-group-item-heading {margin-bottom: 8.5px}
    h3.list-group-item-heading .label{}
</style>