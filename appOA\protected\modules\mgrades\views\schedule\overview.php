
<?php
$programs = CommonUtils::LoadConfig('CfgCampusProgram');
$program = isset( $programs[$this->branchObj->type] ) ? $programs[$this->branchObj->type] : null;
$program['programs']['ALL'] = 'All/全部';

$dayLabels = array(
    '1' => 'Monday/周一',
    '2' => 'Tuesday/周二',
    '3' => 'Wednesday/周三',
    '4' => 'Thursday/周四',
    '5' => 'Friday/周五',
);
?>

<div class="modal" id="overall-progress">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-body">
                <div class="progress">
                    <div class="progress-bar" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%;">
                        0%
                    </div>
                </div>
                <div class="text-center hint">Loading Data...</div>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<div class="container-fluid">
    <div class="row mb10">
        <div class="col-md-12">
            <div class="btn-group" data-toggle="buttons">
                <label class="btn btn-primary action-view-by active" data-viewby="classes">
                    <input type="radio" name="viewbyoptions"> <?php echo Yii::t('campus','By Classes');?>
                </label>
                <label class="btn btn-primary action-view-by" data-viewby="schedules">
                    <input type="radio" name="viewbyoptions"> <?php echo Yii::t('campus','By Schedules');?>
                </label>
                <label class="btn btn-primary action-view-by" data-viewby="teachers">
                    <input type="radio" name="viewbyoptions"> <?php echo Yii::t('campus','By Teachers');?>
                </label>
            </div>

            <a href="<?php echo $this->createUrl('//mgrades/schedule/index');?>" class="btn btn-default pull-right" target="_blank">
                <span class="glyphicon glyphicon-link"></span> <?php echo Yii::t('global', 'Schedule Arrangements');?></a>
        </div>
    </div>

    <div class="row view-zone" data-viewid="classes" style="display: none">
        <?php
        $this->renderPartial('views/_viewByClasses');
        ?>
    </div>
    <div class="row view-zone" data-viewid="schedules" style="display: none">
        <?php
        $this->renderPartial('views/_viewBySchedules');
        ?>
    </div>
    <div class="row view-zone" data-viewid="teachers" style="display: none">
        <?php
        $this->renderPartial('views/_viewByTeachers');
        ?>
    </div>

</div>

<script>
    var pageData={};
    pageData.dayLabels = <?php echo CJSON::encode($dayLabels);?>;
    var currentViewby='classes';
    var programCfg = <?php echo CJSON::encode($program); ?>;
    var scheduleCode = <?php echo CJSON::encode($schedule->schedule_code); ?>;
    var wrapper = null;
    //默认页面
    function getData(){

        var progress = 0;
        var step = ( 100 *( 1 / getDataRequest.length) ).toFixed(2);
        var complete = 0;
        var container;
        $('#overall-progress .progress-bar').css('width','0').html('0%');
        $('#overall-progress').modal({keyboard: false,backdrop:'static'});

        for(var i=0; i<getDataRequest.length;i++){
            var varName = getDataRequest[i].varName;
            $.ajax({
                type: "POST",
                url: getDataRequest[i].url,
                data: getDataRequest[i].data,
                dataType: 'json',
                async: true,
                success: function(data) {
                    progress = parseInt(progress) + parseInt(step);
                    complete +=1;
                    if(progress > 100) progress = 100;
                    if(complete == getDataRequest.length) progress = 100;

                    if(data.type == 'replace'){
                        container = $('#'+data.replaceholder);
                        container.html(data.data);
                        container.hide();
                    }else{
                        eval(data.cmd);
                    }

                    $('#overall-progress .progress-bar').attr("aria-valuenow", progress).css('width',progress+'%').html(progress+'%');
                    if(progress == 100){
                        $('#overall-progress .hint').html('Preparing Data...');
                        showData();
                        if(data.type == 'replace'){
                            container.show();
                        }
                    }
                }
            });
        }
    }

    function viewBy(viewid){
        $('div.view-zone').hide();
        $('div.view-zone[data-viewid|="'+viewid+'"]').show('blind',{},500);

        eval('renderData'+viewid+'("'+viewid+'");');
    }

    function showData(){
        $('.action-view-by').click(function(){
            if( currentViewby == $(this).data('viewby') ){

            }else{
                currentViewby = $(this).data('viewby');
                viewBy( currentViewby );
            }

        });

        viewBy( currentViewby );
        setTimeout("js:$('#overall-progress').modal('hide')", 500);
    }

    function loadGenealInfo(){
        //抓取所有课表信息
        //所有班级+老师关系
        //所有老师信息

        getDataRequest = [
            {
                url:"<?php echo $this->createUrl('//mgrades/schedule/getOverviewData') ;?>",
                data: 'type=classes'
            },
            {
                url:"<?php echo $this->createUrl('//mgrades/schedule/getOverviewData') ;?>",
                data: 'type=teachers'
            },
            {
                url:"<?php echo $this->createUrl('//mgrades/schedule/getOverviewData') ;?>",
                data: 'type=schedules&scheduleid=<?php echo $scheduleid;?>'
            }
        ];

        getData();
    }
    loadGenealInfo();
</script>