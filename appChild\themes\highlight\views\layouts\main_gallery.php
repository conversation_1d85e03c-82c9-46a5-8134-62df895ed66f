<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta name="language" content="en" />

	<?php
		$bgset = isset($_COOKIE['child_mims_bgset'])?$_COOKIE['child_mims_bgset']:'body-green';
	?>
	<!-- blueprint CSS framework -->
	<link rel="stylesheet" type="text/css" href="<?php echo Yii::app()->theme->baseUrl; ?>/css/screen.css" media="screen, projection" />
	<link rel="stylesheet" type="text/css" href="<?php echo Yii::app()->theme->baseUrl; ?>/css/print.css" media="print" />
	<!--[if lt IE 8]>
	<link rel="stylesheet" type="text/css" href="<?php echo Yii::app()->themeManager->baseUrl; ?>/base/css/ie.css" media="screen, projection" />
	<![endif]-->
	<link rel="stylesheet" type="text/css" href="<?php echo Yii::app()->theme->baseUrl; ?>/css/form.css?t=<?php echo Yii::app()->params['refreshAssets'];?>" />
	<link rel="stylesheet" type="text/css" href="<?php echo Yii::app()->theme->baseUrl; ?>/css/main.css?t=<?php echo Yii::app()->params['refreshAssets'];?>" />
	<link rel="stylesheet" type="text/css" href="<?php echo Yii::app()->theme->baseUrl; ?>/css/bg.css?t=<?php echo Yii::app()->params['refreshAssets'];?>" />
    <link rel="stylesheet" type="text/css" href="<?php echo Yii::app()->theme->baseUrl; ?>/css/<?php echo Yii::app()->getLanguage()?>.css?t=<?php echo Yii::app()->params['refreshAssets'];?>" />
	<link rel="SHORTCUT ICON" href="/favicon.ico" />
	
    <script type="text/javascript">var yiiLang='<?php echo Yii::app()->getLanguage()?>',theme='<?php echo Yii::app()->theme->baseUrl?>';</script>
	
	<script type="text/javascript" src="<?php echo Yii::app()->themeManager->baseUrl; ?>/base/js/callback.js"></script>

	<?php
	if(Yii::app()->params['baiduStats'])
		$this->renderPartial('//statsScripts/baiduStats_head');
	?>
	
	<title><?php echo CHtml::encode($this->pageTitle); ?></title>
</head>

<body class="gallery">

<div class="themepage container" id="page">
	
	<div class="clear"></div>

	
	<div id="mainbox">
		<?php echo $content; ?>
	</div>
	<div class="clear"></div>
	
	
</div><!-- page -->

<?php
if(Yii::app()->params['baiduStats'])
	$this->renderPartial('//statsScripts/baiduStats_common');
?>
</body>
</html>