<?php
if(!defined("IVY_POLICY"))
throw new CException('DO NOT USE THIS FILE DIRECTLY');

return array( 'policy'=>array(

    //是否老生老办法
    ENABLE_CONSTANT_TUITION => true,
    FENTAN_FACTOR => DAILY,

    //年保育费
    //ANNUAL_CHILDCARE_AMOUNT => 8000,

    //是否开放课外活动账单
    ENABLE_AFTERSCHOOLS => true,

    //图书馆工本费
    FEETYPE_LIBCARD => 10,

    //学期四个时间点；取前后两端
    TIME_POINTS   => array(202308,202402,202402,202406),

    //年付开通哪些缴费类型
    // PAYGROUP_ANNUAL => array(
    //     ITEMS => array(
    //         FEETYPE_TUITION,
    //         FEETYPE_LUNCH,
    //         FEETYPE_SCHOOLBUS,
    //     ),
    //     TUITION_CALC_BASE => BY_ANNUAL,
    // ),

    //学期付开通哪些缴费类型
   PAYGROUP_SEMESTER => array(
       ITEMS => array(
           FEETYPE_TUITION,
           FEETYPE_LUNCH,
           FEETYPE_SCHOOLBUS,
       ),
       TUITION_CALC_BASE => BY_SEMESTER,
   ),

 // //月份开通哪些缴费类型
 //    PAYGROUP_MONTH => array(
 //        ITEMS => array(
 //            FEETYPE_TUITION,
 //            FEETYPE_LUNCH,
 //            // FEETYPE_SCHOOLBUS,
 //        ),
 //        TUITION_CALC_BASE => BY_MONTH,
 //    ),

    //计算基准：BY_MONTH, 或者 BY_SETTING, 前者表示所有的学费都基于月费用计算，后者表示所有的学费都基于学期或学年
    TUITION_CALCULATION_BASE => array(

        BY_MONTH => array(

            // //月收费金额
            // AMOUNT => array(
            //     FULL5D => 173195,
            //     HALF5D => 121236,
            //     // HALF3D => 8710,
            //     // HALF2D => 6100
            // ),

            //收费月份，及其权重
            MONTH_FACTOR => array(
                202309=>1,
                202310=>1,
                202311=>1,
                202312=>1,
                202401=>1,
                202402=>1,
                202403=>1,
                202404=>1,
                202405=>1,
                202406=>1,
                202407=>0,
            ),

            //折扣

            GLOBAL_DISCOUNT => array(
                DISCOUNT_ANNUAL => '1:1:2023-12-01'
            )


        ),

        //东湖配置实例

        BY_ANNUAL => array(
            AMOUNT => array(
                FULL5D => 210000,
                FULL5D2 => 196410,
                FULL5D3 => 178552,
                // HALF5D => 147000,
            ),
        ),

        BY_SEMESTER1 => array(
            AMOUNT => array(
                FULL5D => 112459,
                FULL5D2 => 105181,
                FULL5D3 => 95618,
                // HALF5D => 68654,
                // HALF5D2 => 66932,
            ),
        ),

        BY_SEMESTER2 => array(
            AMOUNT => array(
                FULL5D => 97541,
                FULL5D2 => 91229,
                FULL5D3 => 82934,
                // HALF5D => 78346,
                //  HALF5D2 => 58054,
            ),
        ),

    ),

    //每餐费用
    LUNCH_PERDAY => 45,

    //账单到期日和账单开始日之差
    DUEDAYS_OFFSET => 0,

));