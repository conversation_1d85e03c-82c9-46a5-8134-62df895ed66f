<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <?php
            $this->widget('ext.ivyCGridView.BsCGridView', array(
                'id'=>'confirmed-visit-grid',
                'afterAjaxUpdate'=>'js:head.Util.modal',
                'dataProvider'=>$dataProvider,
                'enableSorting'=>false,
                'template'=>"{items}{pager}",
                'colgroups'=>array(
                    array(
                        "colwidth"=>array(150,150,150,150,150,150),
                    )
                ),
                'columns'=>array(
                    array(
                        'name' => 'appointment_date',
                        'type'=>'raw',
                        'value' => 'OA::formatDateTime($data->appointment_date)."<br><span>".$data->appointment_time."</span>"'
                    ),
                    array(
                        'name'=>'孩子姓名',
                        'value'=>'$data->basic->child_name',
                    ),
                    array(
                        'name'=>'孩子年龄',
                        'type'=>'raw',
                        'value'=>'CommonUtils::getAge($data->basic->birth_timestamp)',
                    ),                     
                    array(
                        'name'=>'孩子生日',
                        'type'=>'raw',
                        'value'=>'date("Y-m-d" ,$data->basic->birth_timestamp)',
                    ), 
                    array(
                        'name'=>'家长信息',
                        'type'=>'raw',
                        'value'=>'$data->basic->parent_name."<br>".$data->basic->tel."<br>".$data->basic->email',
                    ),                    
                    array(
                        'name'=>'是否已到',
                        'type'=>'raw',
                        'value'=>'$data->category == "visit" ? "已到" : "未到"',
                    ),
                ),
            ));
            ?>
        </div>
    </div>
</div>