<?php
$crit = new CDbCriteria;
$crit->index = 'branchid';
$crit->compare('type', '<>'.Branch::TYPE_OFFICE);
$crit->compare('status', Branch::STATUS_ACTIVE);
$crit->order = '`group` ASC';
$branches = Branch::model()->findAll($crit);

$clabels = MailRecipient::attributeLabels();
?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','HQ Operations'), array('/moperation'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Basic Configurations'), array('/moperation'))?></li> 
        <li class="active"><?php echo Yii::t('site','Mailer Sender');?></li>
    </ol>

    <div class="row">
        <div class="col-md-1 col-sm-2">
            <div class="list-group" id="campus-list">
                <?php
                echo CHtml::link('<span class="glyphicon glyphicon-tags"></span> All', 'javascript:void(0)', array('branchid'=>'all','class'=>'list-group-item'));
                foreach($branches as $branch):
                    $_title = '<span class="glyphicon glyphicon-tags program'.$branch->group.'"></span> ';
                    $_title .= $branch->abb;
                    echo CHtml::link($_title,'javascript:void(0)', array('branchid'=>$branch->branchid,'class'=>'list-group-item'));
                endforeach;
                ?>
            </div>
        </div>
        <div class="col-md-11 col-sm-10">
            <div id="mail-edit-zone" style="display: none">
                <?php foreach($mailIds as $mailKey => $mailTitle): ?>
                    <div class="panel panel-primary">
                        <div class="panel-heading">
                            <span class="glyphicon glyphicon-file"></span>
                            <?php echo $mailTitle; ?>
                            <span class="glyphicon glyphicon-pencil" onclick="editMail('<?php echo $mailKey; ?>')"
                                  style="cursor:pointer"></span>
                        </div>
                        <div class="panel-body mail-zone" data-mail-key="<?php echo $mailKey; ?>">
                            <!-- place holder -->
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</div>

<!--内容显示-->
<script type="text/template" id="mail-recipient-display-template">
<dl class="dl-horizontal">
    <dt><?php echo $clabels['mail_reply_to'];?></dt>
    <dd><%- mail_reply_to %></dd>
</dl>
<dl class="dl-horizontal">
    <dt><?php echo $clabels['send_to'];?></dt>
    <dd><%- send_to %></dd>
</dl>
<dl class="dl-horizontal">
    <dt><?php echo $clabels['cc_to'];?></dt>
    <dd><%- cc_to %></dd>
</dl>
<dl class="dl-horizontal">
    <dt><?php echo $clabels['bcc_to'];?></dt>
    <dd><%- bcc_to %></dd>
</dl>
</script>


<!--表单模版-->
<script type="text/template" id="mail-recipient-form-template">

        <?php echo CHtml::hiddenField('MailRecipient[flag]', '<%- flag %>', array('encode'=>false)); ?>
        <?php echo CHtml::hiddenField('MailRecipient[branch_id]', '<%- branch_id %>', array('encode'=>false)); ?>
        <?php echo CHtml::hiddenField('MailRecipient[id]', '<%- id %>', array('encode'=>false)); ?>

        <div class="form-group" model-attribute="mail_reply_to">
            <?php echo CHtml::label($clabels['mail_reply_to'], CHtml::getIdByName('MailRecipient[mail_reply_to]'), array('class'=>'col-sm-2 control-label'));?>
            <div class="col-sm-10">
                <?php echo CHtml::textField(
                    'MailRecipient[mail_reply_to]',
                    '<%= mail_reply_to %>',
                    array('class'=>'form-control','encode'=>false));?>
                <div class="checkbox"><label><?php echo CHtml::checkbox('support_email[reply_to]');?> 加入学校support邮箱</label></div>
            </div>
        </div>
        <div class="form-group" model-attribute="send_to">
            <?php echo CHtml::label($clabels['send_to'], CHtml::getIdByName('MailRecipient[send_to]'), array('class'=>'col-sm-2 control-label'));?>
            <div class="col-sm-10">
                <?php echo CHtml::textField(
                    'MailRecipient[send_to]',
                    '<%= send_to %>',
                    array('class'=>'form-control','encode'=>false));?>
                <div class="checkbox"><label><?php echo CHtml::checkbox('support_email[to]');?> 加入学校support邮箱</label></div>
            </div>
        </div>
        <div class="form-group" model-attribute="cc_to">
            <?php echo CHtml::label($clabels['cc_to'], CHtml::getIdByName('MailRecipient[cc_to]'), array('class'=>'col-sm-2 control-label'));?>
            <div class="col-sm-10">
                <?php echo CHtml::textField(
                    'MailRecipient[cc_to]',
                    '<%= cc_to %>',
                    array('class'=>'form-control','encode'=>false));?>
                <div class="checkbox"><label><?php echo CHtml::checkbox('support_email[cc]');?> 加入学校support邮箱</label></div>
            </div>
        </div>
        <div class="form-group" model-attribute="bcc_to">
            <?php echo CHtml::label($clabels['bcc_to'], CHtml::getIdByName('MailRecipient[bcc_to]'), array('class'=>'col-sm-2 control-label'));?>
            <div class="col-sm-10">
                <?php echo CHtml::textField(
                    'MailRecipient[bcc_to]',
                    '<%= bcc_to %>',
                    array('class'=>'form-control','encode'=>false));?>
                <div class="checkbox"><label><?php echo CHtml::checkbox('support_email[bcc]');?> 加入学校support邮箱</label></div>
            </div>
        </div>
</script>

<!-- Calendar Template Modal -->
<div class="modal fade" id="editMailModal" tabindex="-1" role="dialog" aria-labelledby="editMailModalLabel"
     aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel"><?php echo Yii::t('site','School Calendar Template');?> <small></small></h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="well">
                            多个电子邮件请用分号隔开
                        </div>
                        <form class="form-horizontal J_ajaxForm" action="<?php echo $this->createUrl('//moperation/misc/mailsender');?>" method="POST">
                            <?php
                            echo CHtml::hiddenField('postMailForm',1);
                            ?>
                            <div id="form-data">
                                <!--place holder-->
                            </div>

                            <div class="pop_bottom">
                                <button onclick="$('#editMailModal').modal('hide')" type="button" class="btn btn-default pull-right"><?php echo Yii::t('global','Cancel');?></button>
                                <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global','Submit');?></button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    var currentBranchId = null;
    var showBranchMailSender = null;
    var isAjaxing = false;
    var mailIds = <?php echo CJSON::encode($mailIds)?>;
    var campusMailData = {};
    var formTemplate = $('#mail-recipient-form-template').html();
    var displayTemplate = $('#mail-recipient-display-template').html();

    console.log(mailIds);
    $(function(){
        $('#campus-list a').click(function(){showBranchMailSender(this)});

        showBranchMailSender = function(obj){
            if (isAjaxing) return false;
            $('#mail-edit-zone').hide();
            $('.mail-zone').empty();
            var branchId = $(obj).attr('branchid');
            currentBranchId = branchId;
            $.ajax({
                type: 'post',
                url:'<?php echo $this->createUrl('//moperation/misc/fetchMailByCampusId')?>',
                dataType: 'json',
                data: {branchId: currentBranchId}
            }).done(function(data){
                if(data.state == 'success'){
                    campusMailData[currentBranchId] = data.data.mails;
                    _.each(campusMailData[currentBranchId], function(data, key){
                        var _wrapper = $('div.mail-zone[data-mail-key|="'+key+'"]');
                        _wrapper.empty();
                        var _mail = _.template(displayTemplate, data);
                        _wrapper.html(_mail);
                    })
                }
                $('#mail-edit-zone').show();
            })
        }
    })

    var cbSaveMail = function(data) {
        var mailData = data.maildata;
        if ( _.isEmpty(campusMailData[mailData.branch_id]) ){
            campusMailData[mailData.branch_id] = {};
        }
        campusMailData[mailData.branch_id][mailData.flag] = mailData;

        var _wrapper = $('div.mail-zone[data-mail-key|="'+mailData.flag+'"]');
        _wrapper.empty();
        var _mail = _.template(displayTemplate, mailData);
        _wrapper.html(_mail);

        $('#editMailModal').modal('hide');
    }

    var editMail = function(mailId) {
        var model = {id: 0, mail_reply_to:'',send_to:'',cc_to:'',bcc_to:'', flag:mailId, branch_id: currentBranchId};
        if(!_.isEmpty(campusMailData[currentBranchId]) && !_.isEmpty(campusMailData[currentBranchId][mailId])) {
            model = campusMailData[currentBranchId][mailId]
        }
        var _form = _.template(formTemplate, model);
        $('#form-data').html(_form);
        $('#editMailModal').modal();
        for(var support in model.support_email){
            $('#support_email_'+support).attr('checked', true);
        }
    }
</script>