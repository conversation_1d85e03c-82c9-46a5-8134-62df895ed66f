<?php

/**
 * This is the model class for table "ivy_asa_month_report_item".
 *
 * The followings are the available columns in table 'ivy_asa_month_report_item':
 * @property integer $id
 * @property integer $report_id
 * @property integer $vendor_id
 * @property string $vendor_name
 * @property string $position
 * @property integer $group_id
 * @property integer $course_id
 * @property string $identity
 * @property double $unit_price
 * @property integer $course_count
 * @property string $bank_title
 * @property string $bank_account
 * @property integer $fee_merge
 */
class AsaMonthReportItem extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_asa_month_report_item';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('report_id, vendor_id, vendor_name, position, group_id, course_id, unit_price, course_count', 'required'),
			array('report_id, vendor_id, group_id, course_id, course_count, fee_merge', 'numerical', 'integerOnly'=>true),
			array('unit_price', 'numerical'),
			array('vendor_name, position, identity, bank_title, bank_account', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, report_id, vendor_id, vendor_name, position, group_id, course_id, identity, unit_price, course_count, bank_title, bank_account,fee_merge', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'course' => array(self::HAS_ONE, 'AsaCourse', array('id'=>'course_id')),
			'group' => array(self::HAS_ONE, 'AsaCourseGroup', array('id'=>'group_id')),
			'vendor' => array(self::HAS_ONE, 'AsaVendor', array('vendor_id'=>'vendor_id')),
			'report' => array(self::HAS_ONE, 'AsaMonthReport', array('id'=>'report_id')),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'report_id' => 'Report',
			'vendor_id' => 'Vendor',
			'vendor_name' => 'Vendor Name',
			'position' => 'Position',
			'group_id' => 'Group',
			'course_id' => 'Course',
			'identity' => 'Identity',
			'unit_price' => 'Unit Price',
			'course_count' => 'Course Count',
			'bank_title' => 'Bank Title',
			'bank_account' => 'Bank Account',
			'fee_merge' => 'Fee Merge',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('report_id',$this->report_id);
		$criteria->compare('vendor_id',$this->vendor_id);
		$criteria->compare('vendor_name',$this->vendor_name,true);
		$criteria->compare('position',$this->position,true);
		$criteria->compare('group_id',$this->group_id);
		$criteria->compare('course_id',$this->course_id);
		$criteria->compare('identity',$this->identity,true);
		$criteria->compare('unit_price',$this->unit_price);
		$criteria->compare('course_count',$this->course_count);
		$criteria->compare('bank_title',$this->bank_title,true);
		$criteria->compare('bank_account',$this->bank_account,true);
		$criteria->compare('fee_merge',$this->fee_merge,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AsaMonthReportItem the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
