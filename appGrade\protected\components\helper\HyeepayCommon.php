<?php

/**
 * 根据易宝接口提供的方法改写成的类
 *
 * <AUTHOR>
 *
 */
class HyeepayCommon{
	/**
	 * 备注
	 * 请求的信息中
	 *  pa_MP  商户扩展信息  被用来存储 ” 账单ID “了,方便支付成功后 更新账单信息
	 *
	 */

	/*
	 * 学校ID
	*/
	private $schoolId = null;
	/*
	 *商户编号
	*/
	private $p1_MerId = "";
	/*
	 * 商户密钥
	*/
	private $merchantKey = "";
	/*
	 * 日志文件的地址
	*/
	private $logPath = "";
	/*
	 * 日志文件的名称
	*/
	private $logName = "";
	/*
	 *正式请求地址
	*/
	private $reqURL_onLine = "";
	/*
	 *业务类型 固定值“Buy”
	*/
	private $p0_Cmd = "Buy";
	/*
	 *交易币种 固定值 ”CNY”.
	*/
	private $p4_Cur ="CNY";
	/*
	 *送货地址 “0”: 不需要，默认为 ”0”.
	*/
	private $p9_SAF = "0";
	/*
	 *商户接收支付成功数据的地址
	*/
	private $p8_Url = "";
	/*
	 *应答机制 固定值为“1”: 需要应答机制
	*/
	private $pr_NeedResponse = 1;


	private $xoops_var_path = "";
	private $xoops_url = "";

	function __construct($schoolid = null){

		$this->xoops_var_path = Yii::app()->params['xoopsVarPath'];
		$this->xoops_url = Yii::app()->params['YeepayRespondeUrl'];
	}

	/**
	 * 初始化
	 * @param unknown_type $schoolid
	 * <AUTHOR>
	 * @time 2012-7-18
	 */
	public function init($schoolid = null){

		$this->schoolId = $schoolid;
		$yeepayMeridInfo = Mims::LoadConfig('CfgYeepayMeridInfo');

		if(isset($yeepayMeridInfo[$this->schoolId])){
			$this->p1_MerId = $yeepayMeridInfo[$this->schoolId]['p1_MerId'];
			$this->merchantKey = $yeepayMeridInfo[$this->schoolId]['merchantKey'];
		}
		$this->logPath = $this->xoops_var_path."/logs/payment/yeepay/";
		$this->logName = $this->logPath."YeePay_".date("Ymd",time()).".log";
		$this->reqURL_onLine = "https://www.yeepay.com/app-merchant-proxy/node";
		$langKey = Yii::app()->language == 'en_us' ? 'english' : 'schinese_utf8';
		$this->p8_Url = $this->xoops_url."?payment=yeepay&schoolid=".$this->schoolId."&lang=".$langKey;

	}

	/*
	 * 测试
	*/
	public function initTest($schoolid=null){

		$this->schoolId = $schoolid;
		$this->p1_MerId = "10001126856";
		$this->merchantKey = "69cl522AV6q613Ii4W6u8K6XuW8vM1N6bFgyv769220IuYe9u37N4y7rI4Pl";
		$this->logPath = $this->xoops_var_path."/logs/payment/yeepay/";
		$this->logName = $this->logPath."YeePay_".date("Ymd",time()).".log";
		$this->reqURL_onLine = "http://tech.yeepay.com:8080/robot/debug.action";
		$langKey = Yii::app()->language == 'en_us' ? 'english' : 'schinese_utf8';
		$this->p8_Url = $this->xoops_url."?payment=yeepay&schoolid=".$this->schoolId."&lang=".$langKey;

	}
	/**
	 * 随机码生成
	 * @param int $length
	 * @return string code
	 */
	public function customRandom($length = 6){
		//	$hash = 'CR-';
		$hash = '';
		//	$chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyz';
		$chars = '012356789';
		$max = strlen($chars) - 1;
		mt_srand((double)microtime() * 1000000);
		for($i = 0; $i < $length; $i++){
			$hash .= $chars[mt_rand(0, $max)];
		}
		return $hash;
	}

	/**
	 * 根据学校id 时间 随机码 生成 订单号
	 * @param string $schoolid
	 * @return string
	 */
	public function generateOrderId($number_code="004",$one_invoice_id=0,$length = 6) {
		$invoice_id = sprintf("%0".$length."s\n",$one_invoice_id);
		if(empty($invoice_id)){
			$invoice_id = $this->customRandom($length);
		}
		return trim($number_code.$invoice_id);
	}
	#签名函数生成签名串

	public function getReqHmacString($p2_Order,$p3_Amt,$p5_Pid,$p6_Pcat,$p7_Pdesc,$pa_MP,$pd_FrpId)
	{

		#进行签名处理，一定按照文档中标明的签名顺序进行
		$sbOld = "";
	#加入业务类型
	$sbOld = $sbOld.$this->p0_Cmd;
	#加入商户编号
	$sbOld = $sbOld.$this->p1_MerId;
	#加入商户订单号
	$sbOld = $sbOld.$p2_Order;
	#加入支付金额
	$sbOld = $sbOld.$p3_Amt;
	#加入交易币种
	$sbOld = $sbOld.$this->p4_Cur;
	#加入商品名称
	$sbOld = $sbOld.$p5_Pid;
	#加入商品分类
	$sbOld = $sbOld.$p6_Pcat;
	#加入商品描述
	$sbOld = $sbOld.$p7_Pdesc;
	#加入商户接收支付成功数据的地址
	$sbOld = $sbOld.$this->p8_Url;
	#加入送货地址标识
	$sbOld = $sbOld.$this->p9_SAF;
	#加入商户扩展信息
	$sbOld = $sbOld.$pa_MP;
	#加入支付通道编码
	$sbOld = $sbOld.$pd_FrpId;
	#加入是否需要应答机制
	$sbOld = $sbOld.$this->pr_NeedResponse;

	$hmac = $this->HmacMd5($sbOld,$this->merchantKey);
	$request_info = array(
			"schoolid"=>$this->schoolId,
			"p0_Cmd"=>$this->p0_Cmd,
			"p1_MerId"=>$this->p1_MerId,
			"merchantKey"=>$this->merchantKey,
			"p2_Order"=>$p2_Order,
			"p3_Amt"=>$p3_Amt,
			"p4_Cur"=>$this->p4_Cur,
			"p5_Pid"=>$p5_Pid,
			"p6_Pcat"=>$p6_Pcat,
			"p7_Pdesc"=>$p7_Pdesc,
			"p8_Url"=>$this->p8_Url,
			"pa_MP"=>$pa_MP,
			"pd_FrpId"=>$pd_FrpId,
			"pr_NeedResponse"=>$this->pr_NeedResponse,
			"hmac"=>$hmac
	);
	$this->logstr($this->logName,$request_info,$type="request");
	return $hmac;

	}



	/**
	 * 签名函数
	 * @param $data
	 * @param $key
	 * @return string
	 * <AUTHOR>
	 * @time 2012-7-18
	 */
	public function HmacMd5($data,$key)
	{
		// RFC 2104 HMAC implementation for php.
		// Creates an md5 HMAC.
		// Eliminates the need to install mhash to compute a HMAC
		// Hacked by Lance Rushing(NOTE: Hacked means written)

		//需要配置环境支持iconv，否则中文参数不能正常处理
		$key = iconv("GB2312","UTF-8",$key);
		$data = iconv("GB2312","UTF-8",$data);

		$b = 64; // byte length for md5
		if (strlen($key) > $b) {
			$key = pack("H*",md5($key));
		}
		$key = str_pad($key, $b, chr(0x00));
		$ipad = str_pad('', $b, chr(0x36));
		$opad = str_pad('', $b, chr(0x5c));
		$k_ipad = $key ^ $ipad ;
		$k_opad = $key ^ $opad;

		return md5($k_opad . pack("H*",md5($k_ipad . $data)));
	}
	/**
	 * 日志记录函数
	 * @param  $logName
	 * @param  $info
	 * @param  $type
	 * <AUTHOR>
	 * @time 2012-7-18
	 */
	public function logstr($logName,$info,$type="response1")
	{
		$james=fopen($logName,"a+");
		if("response1" == $type || "response2" == $type ){
			$orderid = $info['r6_Order'];
		}else{
			$orderid = $info['p2_Order'];
		}
		$writeStr = date("Y-m-d H:i:s")."|type[".$type."]|orderid[".$orderid."]";
		if(is_array($info)){
			foreach ($info as $key => $value) {
				$writeStr.="|".$key."[".$value."]";
			}
		}
		$writeStr .= "\r\n";

		fwrite($james,$writeStr);
		fclose($james);
	}

	public function getP0Cmd(){
		return $this->p0_Cmd;
	}
	public function getP1MerId(){
		return $this->p1_MerId;
	}
	public function getP4Cur(){
		return $this->p4_Cur;
	}
	public function getP8Url(){
		return $this->p8_Url;
	}
	public function getP9Saf(){
		return $this->p9_SAF;
	}
	public function getPrNeedResponse(){
		return $this->pr_NeedResponse;
	}
	public function getReqUrlOnline(){
		return $this->reqURL_onLine;
	}



}


?>