<?php

/**
 * This is the model class for table "ivy_refund_lunch".
 *
 * The followings are the available columns in table 'ivy_refund_lunch':
 * @property integer $id
 * @property integer $yid
 * @property string $schoolid
 * @property integer $classid
 * @property integer $childid
 * @property integer $target_date
 * @property integer $target_timestamp
 * @property integer $operator_uid
 * @property integer $operate_timestamp
 * @property double $amount
 * @property integer $child_credit_id
 * @property string $userid
 * @property string $updated_timestamp
 */
class RefundLunch extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return RefundLunch the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_refund_lunch';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('yid, schoolid, classid, childid, target_date, target_timestamp, operator_uid, operate_timestamp, amount, userid, updated_timestamp', 'required'),
			array('yid, classid, childid, target_date, target_timestamp, operator_uid, operate_timestamp, child_credit_id', 'numerical', 'integerOnly'=>true),
			array('amount', 'numerical'),
			array('schoolid', 'length', 'max'=>10),
			array('userid, updated_timestamp', 'length', 'max'=>11),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, yid, schoolid, classid, childid, target_date, target_timestamp, operator_uid, operate_timestamp, amount, child_credit_id, userid, updated_timestamp', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'childInfo' => array(self::BELONGS_TO, 'ChildProfileBasic','childid'),
			'classInfo' => array(self::BELONGS_TO, 'IvyClass', array('classid'=>'classid'), 'together'=>false),
			'schoolInfo' => array(self::BELONGS_TO, 'Branch', array('schoolid'=>'branchid'), 'together'=>false)
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'yid' => 'Yid',
			'schoolid' => 'Schoolid',
			'classid' => 'Classid',
			'childid' => 'Childid',
			'target_date' => 'Target Date',
			'target_timestamp' => 'Target Timestamp',
			'operator_uid' => 'Operator Uid',
			'operate_timestamp' => 'Operate Timestamp',
			'amount' => 'Amount',
			'child_credit_id' => 'Child Credit',
			'userid' => 'Userid',
			'updated_timestamp' => 'Updated Timestamp',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('target_date',$this->target_date);
		$criteria->compare('target_timestamp',$this->target_timestamp);
		$criteria->compare('operator_uid',$this->operator_uid);
		$criteria->compare('operate_timestamp',$this->operate_timestamp);
		$criteria->compare('amount',$this->amount);
		$criteria->compare('child_credit_id',$this->child_credit_id);
		$criteria->compare('userid',$this->userid,true);
		$criteria->compare('updated_timestamp',$this->updated_timestamp,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 *取得某一个孩子已经取消的餐午列表
	 * @param int $childId
	 * @param int $startdate
	 * @param int $enddate
	 * @return Ambigous <mixed, Ambigous <NULL, multitype:>, multitype:, NULL, unknown>
	 * <AUTHOR>
	 */
	public function getRefundLunch($childId,$startdate,$enddate)
	{
		$criteria = new CDbCriteria;
		$criteria->select = 'id,operator_uid,target_date' ;
		$criteria->compare('childid', $childId);
		$criteria->addCondition('target_timestamp>='.$startdate);
		$criteria->addCondition('target_timestamp<='.$enddate);
		return $this->findAll($criteria);
	}

	/**
	 * 判断孩子某天午餐是否存在
	 * @param int $childId
	 * @param int $day
	 * @return boolean
	 * <AUTHOR>
	 */
	public function refundExists($childId,$day)
	{
		$criteria = new CDbCriteria;
		$criteria->compare('childid', $childId);
		$criteria->compare('target_timestamp', $day);
		return $this->exists($criteria);
	}

	/**
	 * 取某天餐费的属性
	 * @param int $childId
	 * @param int $day
	 * @return Array
	 * <AUTHOR>
	 */
	public function getChildLunch($childId,$day)
	{
		$criteria = new CDbCriteria;
		$criteria->compare('childid', $childId);
		$criteria->compare('target_timestamp', $day);
		return $this->find($criteria);
	}
}