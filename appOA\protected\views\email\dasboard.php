<table width=100%  border="1">
    <thead>
        <tr class="active">
            <th class="success" colspan="5">周报告完成信息</th>
        </tr>
        <tr>
            <td>学校报告</td>
            <td colspan="4" id="school_report">
                <span><?php echo $weeklyReport['schoolreport'] == 1 ? '√' : 'X' ; ?></span>
            </td>
        </tr>
        <tr class="active">
            <th>班级名称</th>
            <th>班级课表</th>
            <th>班级报告</th>
            <th>孩子报告完成数/班级总人数</th>
            <th>周报告不合格</th>
        </tr>
    </thead>
    <tbody>
        <?php
        if(isset($weeklyReport['classtitle'])):
        foreach ($weeklyReport['classtitle'] as $classid => $item): ?>
            <tr>
                <td><?php echo $item; ?></td>
                <td><?php echo isset($weeklyReport['schedule']) ? ((isset($weeklyReport['schedule'][$classid])) ? "√" : "X") : 'X'; ?></td>
                <td><?php echo isset($weeklyReport['classreport']) ? ((isset($weeklyReport['classreport'][$classid])) ? "√" : "X") : 'X'; ?></td>
                <td>
                    <?php echo isset($weeklyReport['childreport']) ? ((isset($weeklyReport['childreport'][$classid])) ? count($weeklyReport['childreport'][$classid]) : 0) : 0; ?>
                    /<?php echo isset($weeklyReport['countchild']) ? $weeklyReport['countchild'][$classid] : 0; ?></td>
                <td><?php echo isset($weeklyReport['childreport_invalid']) ? ((isset($weeklyReport['childreport_invalid'][$classid])) ? count($weeklyReport['childreport_invalid'][$classid]) : 0) : 0; ?></td>
            </tr>
        <?php endforeach;
        endif;
        ?>
    </tbody>
</table>

<table width=100% border="1">
    <thead>
    <tr class="active">
        <th class="success" colspan="5">餐谱信息</th>
    </tr>
    </thead>
    <tbody>
    <tr>
        <td width=20%>正常餐谱</td>
        <td><?php
            echo ($lunch['general_lunch'] == 1) ? '√' : 'X' ; ?></td>
    </tr>
    <tr>
        <td>特殊餐</td>
        <td><?php echo ($lunch['special_lunch'] == 1) ? '√' : 'X' ; ?></td>
    </tr>
    </tbody>
</table>