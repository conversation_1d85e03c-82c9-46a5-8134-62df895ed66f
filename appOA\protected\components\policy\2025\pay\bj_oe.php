<?php
if(!defined("IVY_POLICY"))
throw new CException('DO NOT USE THIS FILE DIRECTLY');

return array( 'policy'=>array(

	//是否老生老办法
	ENABLE_CONSTANT_TUITION => true,
    // 按天收费
    FENTAN_FACTOR => DAILY,

	//年保育费
	//ANNUAL_CHILDCARE_AMOUNT => 8000,

	//是否开放课外活动账单
	ENABLE_AFTERSCHOOLS => true,

	//图书馆工本费
	FEETYPE_LIBCARD => 10,

	//学期四个时间点；取前后两端
	TIME_POINTS   => array(202509,202602,202602,202607),

	//年付开通哪些缴费类型
//	PAYGROUP_ANNUAL => array(
//		ITEMS => array(
//			FEETYPE_TUITION,
//			FEETYPE_LUNCH,
//			FEETYPE_SCHOOLBUS,
//		),
//		TUITION_CALC_BASE => BY_MONTH,
//	),
//
//	学期付开通哪些缴费类型
	 PAYGROUP_SEMESTER => array(
	 	ITEMS => array(
	 		FEETYPE_TUITION,
	 		FEETYPE_LUNCH,
            FEETYPE_SCHOOLBUS,
	 	),
	 	TUITION_CALC_BASE => BY_SEMESTER,
	 ),

//    PAYGROUP_MONTH => array(
//        ITEMS => array(
//            FEETYPE_TUITION,
//            FEETYPE_LUNCH,
//            FEETYPE_SCHOOLBUS,
//        ),
//        TUITION_CALC_BASE => BY_MONTH,
//    ),

	//计算基准：BY_MONTH, 或者 BY_SETTING, 前者表示所有的学费都基于月费用计算，后者表示所有的学费都基于学期或学年
	TUITION_CALCULATION_BASE => array(


        BY_MONTH => array(

            //月收费金额
            // AMOUNT => array(
            //    FULL5D => 10500,
            //    HALF5D => 7560,
                // FULL3D => 6840,
                // HALF3D => 5050,
                // FULL2D => 4750,
                // HALF2D => 4321,
            // ),

            //收费月份，及其权重
            MONTH_FACTOR => array(
                202509=>1,
                202510=>1,
                202511=>1,
                202512=>1,
                202601=>1,
                202602=>0.25,
                202603=>1,
                202604=>1,
                202605=>1,
                202606=>1,
                202607=>0.13
            ),

            //折扣
            GLOBAL_DISCOUNT => array(
                DISCOUNT_ANNUAL => '1:1:2026-06-01:round',
                DISCOUNT_SEMESTER1 => '1:1:2026-07-02:round',
                DISCOUNT_SEMESTER2 => '1:1:2026-07-31:round'
            )
        ),

        BY_SEMESTER1 => array(
            AMOUNT => array(
                FULL5D => 96400,
                PROGRAM_IBS => 44974,
//                FULL5D2 => 40143,
                // HALF2D => 28500,
                // HALF3D => 41500,
                // HALF5D => 67480,
            ),
        ),

        BY_SEMESTER2 => array(
            AMOUNT => array(
                FULL5D => 88610,
                PROGRAM_IBS => 50426,
//                FULL5D2 => 55256,
                // HALF2D => 28500,
                // HALF3D => 41500,
                // HALF5D => 55322,
            ),
            ENDDATE => array(
                FULL5D => 1783008000,
                // RS_FULL5D => 1751558400,
                PROGRAM_IBS => 1785427200,
                LUNCHA => 1783008000,
                LUNCHB => 1785427200,

                // 国际班校车费
                41903 => 1783008000,
                41904 => 1783008000,
                41905 => 1783008000,
                41906 => 1783008000,
                41907 => 1783008000,
                41908 => 1783008000,

                // 双语班校车费
                41897 => 1785427200,
                41898 => 1785427200,
                41899 => 1785427200,
                41900 => 1785427200,
                41901 => 1785427200,
                41902 => 1785427200,

            )
        ),
	),

	//每餐费用
    LUNCH_PERDAY => array(
        LUNCHA => 45,
        LUNCHB => 45,
    ),

	//账单到期日和账单开始日之差
	DUEDAYS_OFFSET => 1,

));