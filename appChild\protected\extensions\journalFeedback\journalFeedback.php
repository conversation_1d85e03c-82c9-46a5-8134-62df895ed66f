<?php
class journalFeedback extends CWidget{
	public $classId;
    public $yId;
    public $schoolId;
    public $childId;
	
	public function init(){
        
	}
	
	public function run(){

        Yii::import('common.models.portfolio.*');
        Yii::import('common.models.feedback.Comments');
        
        $criteria = new CDbCriteria();
        $criteria->compare('com_child_id', $this->childId);
//        $criteria->compare('com_yid', $this->yId);
//        $criteria->compare('com_class_id', $this->classId);
        $criteria->select='max(`com_created_time`) as com_created_time';
        $criteria->limit=5;
        $criteria->group='com_week_num';
        $mfeed = Comments::model()->findAll($criteria);
        $feids=array();
        foreach ($mfeed as $mf){
            $feids[$mf->com_created_time] = $mf->com_created_time;
        }
        $criteria = new CDbCriteria();
        $criteria->compare('com_child_id', $this->childId);
//        $criteria->compare('com_yid', $this->yId);
//        $criteria->compare('com_class_id', $this->classId);
        $criteria->compare('com_created_time', $feids);
        $criteria->order='com_created_time desc';
        $feedback = Comments::model()->with('userWithProfile', 'yInfo')->findAll($criteria);
        
        $this->render('journalfeedback',array(
            'feedback'=>$feedback,
        ));
	}
	
}