<?php
//查询有剩余年假的员工信息
$criter = new CDbCriteria;
$criter->compare('t.type', array(UserLeave::TYPE_ANNUAL_LEAVE,UserLeave::TYPE_DAYS_OFF));
$criter->compare('t.branchid', $this->branchId);
$criter->addCondition('t.balance>0');
$criter->with='appUser';
$balanceData = UserLeave::model()->findAll($criter);
$balanceList = array();
if (!empty($balanceData)){
    foreach ($balanceData as $val){
        $balanceList[$val->id] = $val->getAttributes();
        $balanceList[$val->id]['name'] = $val->appUser->getName();
        $balanceList[$val->id]['startdate'] = OA::formatDateTime($val->startdate);
        $balanceList[$val->id]['enddate'] = OA::formatDateTime($val->enddate);
        $balanceList[$val->id]['hoursTxt'] = UserLeave::getLeaveFormat($val->hours);
        $balanceList[$val->id]['extensionDate'] = ($extensionDate === null) ? '' : sprintf($extensionDate,$val->startyear+1);
    }
    unset($balanceData);
}
//查询正在审核中假期信息
$command = Yii::app()->db->createCommand();
$command->from(LeaveItem::model()->tableName());
$command->where('branchid=:branchid and type=:type and status=:status', array(':branchid'=>$this->branchId,':type'=>UserLeave::TYPE_ANNUAL_LEAVE,':status'=>LeaveItem::STATUS_WAITING));
$waitDataArr = $command->queryAll();
$waitDataList = array();
if (count($waitDataArr)){
    foreach ($waitDataArr as $val){
        $waitDataList[$val['leave_id']]['sum'] = isset($waitDataList[$val['leave_id']]['sum']) ? ($waitDataList[$val['leave_id']]['sum']+$val['hours']) : $val['hours'];
        $waitDataList[$val['leave_id']]['sumTxt'] = UserLeave::getLeaveFormat($waitDataList[$val['leave_id']]['sum']);
    }
    unset($waitDataArr);
}
?>
<div id="leave-base-list">
    <div class="row">
        <div class="col-md-12">
            <div id="occupation-list">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h3 class="panel-title">年假折现</h3>
                        </div>
                        <?php echo CHtml::form($this->createUrl('//mhr/default/saveStaffDiscounted'), 'Post', array('role'=>'form','class'=>'J_ajaxForm','id'=>'extension-form')); ?>
                        <div class="panel-body">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th width="60"><label><?php echo CHtml::checkBox('all',null,array('id'=>'checkall'));?><span class="text"><?php echo Yii::t('mhr','全选');?></span></label></th>
                                        <th width="180"><?php echo Yii::t('mhr','用户姓名');?></th>
                                        <th width="150"><?php echo Yii::t('mhr','剩余假期');?></th>
                                        <th width="200"><?php echo Yii::t('mhr','年假有效期');?></th>
                                        <th width="200"><?php echo Yii::t('mhr','折现备注');?></th>
                                        <th></th>
                                    </tr>
                                </thead>
                                <tbody id="occupation-item">

                                </tbody>
                            </table>
                        </div>
                        <div class="panel-footer">
                            <button type="button" class="btn btn-primary J_ajax_submit_btn" data-subcheck="1"><?php echo Yii::t('mhr', '折现');?></button>
                            <button type="reset" class="btn btn-default J_newBlock_edit"><?php echo Yii::t('mhr', '重置');?></button>
                        </div>
                        <?php echo CHtml::endForm(); ?>
                    </div>
            </div>
        </div>
    </div>
</div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
$this->renderPartial('templates/_staffList');
?>

<script>
var balanceList = <?php echo CJSON::encode($balanceList);?>;
var waitDataList = <?php echo CJSON::encode($waitDataList);?>;
var balanceTemplate = _.template($('#staff-discounted').html());
$(function(){
    renderTemplate = function(){
        _.each(balanceList,function(attr,key){
            var view = balanceTemplate(attr);
            var box = $('#occupation-item');
            box.append(view);
        })
    }
    renderTemplate();
    //全选
    var total_check_all = $("#extension-form").find('input.J_check');
    $("#checkall").click(
        function(){
            if(this.checked){
                $(total_check_all).each(function(k,_this){
                    if (_this.disabled == false){
                        _this.checked = true;
                        $("#LeaveItem_desc_"+_this.value).removeAttr('disabled');
                        $("#LeaveItem_staff_uid_"+_this.value).removeAttr('disabled');
                    }
                });
            }else{
                $(total_check_all).each(function(k,_this){
                    _this.checked = false;
                    $("#LeaveItem_desc_"+_this.value).attr('disabled',true);
                    $("#LeaveItem_staff_uid_"+_this.value).attr('disabled',true);
                });
            }
        }
    );

    //单选
    $('#extension-form').on('click', 'input.J_check',function(e){
        var total_check_all = $("#extension-form").find('input.J_check');
        if(this.checked){
            $("#LeaveItem_desc_"+this.value).removeAttr('disabled');
            $("#LeaveItem_staff_uid_"+this.value).removeAttr('disabled');
            if(total_check_all.filter(':checked').length === total_check_all.length) {
                $("#checkall").attr('checked', true);
            }
        }else{
            $("#LeaveItem_desc_"+this.value).attr('disabled',true);
            $("#LeaveItem_staff_uid_"+this.value).attr('disabled',true);
            $("#checkall").removeAttr('checked');
        }
    })

    succCallback = function(data){
        $.each(data,function(m,attr){
            $("#check-list-"+m).html('已处理');
            $("#check-list-"+m).addClass('text-success');
        })
    }
})
</script>
