<div class="form">
<?php
	foreach (Yii::app()->user->getFlashes() as $key => $message) {
		echo '<div class="flash-' . $key . '">' . $message . "</div>\n";
	}
?>
<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'ivy-pproduct-form',
	'enableAjaxValidation'=>false,
	'htmlOptions' => array('enctype' => 'multipart/form-data'),
)); 
?>

	<p class="note">Fields with <span class="required">*</span> are required.</p>


	<div class="row">
		<?php echo $form->labelEx($model,'title'); ?>
		<?php echo $form->textField($model,'title',array('size'=>60,'maxlength'=>255)); ?>
		<?php echo $form->error($model,'title'); ?>
	</div>

	<div class="row">
		<?php echo $form->labelEx($model,'spec'); ?>
		<?php echo $form->textField($model,'spec',array('size'=>60,'maxlength'=>255)); ?>
		<?php echo $form->error($model,'spec'); ?>
	</div>

	<div class="row">
		<?php echo $form->labelEx($model,'price'); ?>
		<?php echo $form->textField($model,'price'); ?>
		<?php echo $form->error($model,'price'); ?>
	</div>
	
	<div class="row">
		<?php echo $form->labelEx($model,'image'); ?>
		<?php echo CHtml::activeFileField($model, 'image') ?>
		<?php echo $form->error($model,'image'); ?>
	</div>
	
	<div class="row">
		 <?php echo $model->imageUrl; ?>
	</div>
	
	<div class="row">
		<?php echo $form->labelEx($model,'region'); ?>
		<?php echo $form->dropDownList($model, 'region', IvyPRegion::model()->getPegionTitle(),array('multiple'=>true, 'empty'=>Yii::t("global", 'Please Select'))); ?>
		<?php echo $form->error($model,'region'); ?>
	</div>
	
	<div class="row">
		<?php echo $form->labelEx($model,'account_code'); ?>
		<?php echo $form->dropDownList($model, 'account_code', IvyPProduct::model()->showAccount(), array('empty'=>Yii::t("global", 'Please Select'))); ?>
		<?php echo $form->error($model,'account_code'); ?>
	</div>

	<div class="row">
		<?php echo $form->labelEx($model,'category_id'); ?>
		<?php echo $form->dropDownList($model, 'category_id', IvyPProduct::model()->showCategory(), array('empty'=>Yii::t("global", 'Please Select'))); ?>
		<?php echo $form->error($model,'category_id'); ?>
	</div>

	<div class="row">
		<?php echo $form->labelEx($model,'dept_id'); ?>
		<?php echo $form->dropDownList($model, 'dept_id', IvyPProduct::model()->showDept(1), array('empty'=>Yii::t("global", 'Please Select'))); ?>
		<?php echo $form->error($model,'dept_id'); ?>
	</div>

	<div class="row">
		<?php echo $form->labelEx($model,'supplier_id'); ?>
		<?php echo $form->dropDownList($model, 'supplier_id', IvyPProduct::model()->showSupplier()); ?>
		<?php echo $form->error($model,'supplier_id'); ?>
	</div>

	<div class="row">
		<?php echo $form->labelEx($model,'p_period'); ?>
		<?php echo $form->textField($model,'p_period'); ?>
		<?php echo $form->error($model,'p_period'); ?>
	</div>

	<div class="row">
		<?php echo $form->labelEx($model,'status'); ?>
		<?php echo $form->dropDownList($model, 'status', array(Yii::t('operations', '显示'),Yii::t('operations', '隐藏')), array('empty'=>Yii::t("global", 'Please Select'))); ?>
		<?php echo $form->error($model,'status'); ?>
	</div>

	<div class="row">
		<?php echo $form->labelEx($model,'memo'); ?>
		<?php echo $form->textArea($model,'memo',array('rows'=>6, 'cols'=>50)); ?>
		<?php echo $form->error($model,'memo'); ?>
	</div>
	
	<div class="row buttons">
		<?php echo CHtml::submitButton($model->isNewRecord ? 'Create' : 'Save'); ?>
	</div>

<?php $this->endWidget(); ?>

</div><!-- form -->