<?php $form=$this->beginWidget('CActiveForm', array(
    'id'=>'draw-form',
    'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm'),
));
?>

    <div class="pop_cont pop_table" style="height:auto;">
        <div class="tips_light">
            <p>
                <?php echo Yii::t('child','请输入退学日期')?>
            </p>
            <p>
            </p>
        </div>
        <table width="100%" style="margin-bottom: 180px">
            <colgroup>
                <col class="th" />
                <col />
            </colgroup>
            <tbody>
            <tr>
                <th style="width: 100px"><?php echo Yii::t('child', '退学日期')?></th>
                <td>
                    <?php
                    $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                        "name"=>'quit_date',
                        "value"=>$model->quit_date,
                        "model"=>$model,
                        "options"=>array(
                            'changeMonth'=>true,
                            'changeYear'=>true,
                            'dateFormat'=>'yy-mm-dd', //很重要，不要随便修改 (RAN)
                        ),
                        "htmlOptions"=>array(
                            'class'=>'input'
                        )
                    ));
                    ?>
                </td>
            </tr>
            </tbody>
        </table>
    </div>

    <div class="pop_bottom">
        <button type="button" class="btn fr" id="J_dialog_close"><?php echo Yii::t('global','Cancel');?></button>
        <button class="btn fr btn_submit mr10 J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
    </div>

<?php $this->endWidget(); ?>
<style type="text/css">
    #ui-datepicker-div{
        /*top:140px !important;*/
    }
</style>
