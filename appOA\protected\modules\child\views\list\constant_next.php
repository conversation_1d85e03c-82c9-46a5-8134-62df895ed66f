
<?php
$this->renderPartial('header');
	$this->widget('zii.widgets.CMenu',array(
		'items'=> $pageSubMenu,
		'activeCssClass'=>'current',
		'id'=>'page-subMenu',
		'htmlOptions'=>array('class'=>'pageSubMenu')
	));
	echo CHtml::openTag("div", array("class"=>"c"));
	echo CHtml::closeTag("div");
?>


<?php if ($calendar):?>
	<div class="h_a"><?php echo Yii::t("campus", "School Year");?></div>
	<div class="prompt_text">
		<ul>
			<li><?php echo $calendar->startyear . ' ~ ' . intval($calendar->startyear + 1);?></li>
		</ul>
	</div>	
	
	<div class="h_a"><?php echo Yii::t("campus", "Class List");?></div>
	<div class="prompt_text">
		<ul>
			<?php if ($useStartYearOfSchool === true):?>
			<li>校园人员请认真核对双轨制开始年，该生的学费将按照所设置的年份为基准，若开始年有误，请联系IT部门，校园人员没有权限直接进行修改</li>
			<li>若您有权限修改开始年，请双击开始年进行修改</li>
			<li>开始年为0，表示该学生的费用将以标准金额进行计算</li>
			<?php else:?>
			<li>校园人员请认真核对双轨制金额，若金额有误，请联系IT部门，校园人员没有权限直接进行修改</li>
			<li>若您有权限修改金额，请双击金额进行修改</li>
			<li>金额为0，表示该学生的费用将以标准金额进行计算</li>
			<?php endif;?>
		</ul>
	</div>
	<?php if(!$enabledConstantTuition):?>
	<div class="tips_light tips_block">
		<span>本校园本学年未开启双轨制价格（老生老办法）</span>
	</div>
	<?php endif;?>
	
	<div class="table_full">
		<table width="100%">
			<colgroup>
				<col width=240>
			</colgroup>
			<?php foreach ($calendar->nextClasses as $class):?>
				<tr>
					<th id="classid-<?php echo $class->classid;?>">
						<?php echo $class->title . ' ('.count($class->reserveChildren).')';?></th>
						<td class="user_group">
						<?php if($class->reserveChildren):?>
							<ul class="constant-list">
							<?php foreach ($class->reserveChildren as $child):
								$this->renderPartial('_constant_single_row', array("child"=>$child->childProfile, "enabledConstantTuition"=>$enabledConstantTuition,"useStartYearOfSchool"=>$useStartYearOfSchool));
							endforeach;?>
							</ul>
						<?php endif;?>
						</td>
				</tr>
			<?php endforeach; ?>
		</table>
	</div>
<?php
$this->renderPartial('_constant_js');
endif;
?>