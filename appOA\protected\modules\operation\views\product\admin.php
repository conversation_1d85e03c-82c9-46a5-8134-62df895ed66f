<?php
$this->breadcrumbs=array(
	'Ivy Pproducts'=>array('admin'),
	'Manage',
);

$this->menu=array(
	array('label'=>'Create Product', 'url'=>array('create')),
);
$colorbox = $this->widget('common.extensions.colorbox.JColorBox');
$colorbox->addInstance('.colorbox', array('iframe' => false, 'width' => '650', 'height' => '545'));
?>

<h1>Manage Ivy Pproducts</h1>
<?php $this->widget('zii.widgets.grid.CGridView', array(
	'id'=>'ivy-pproduct-grid',
	'dataProvider'=>$model->search(),
	'filter'=>$model,
	'columns'=>array(
		'id',
		'title',
		array(
            'name' => 'category_id',
         	'value' => '$data->Category->title',
         	'filter'=>CHtml::activeDropDownList($model, 'category_id',array(''=>Yii::t("operations", Yii::t('global','Please Select')))+ IvyPProduct::model()->showCategory(2)),
        ),
 		array(
            'name' => 'dept_id',
         	'value' => '$data->Dept->getName()',
         	'filter'=>CHtml::activeDropDownList($model, 'dept_id',array(''=>Yii::t("operations", Yii::t('global','Please Select')))+ IvyPProduct::model()->showDept(2)),
        ),
        array(
            'name' => 'supplier_id',
         	'value' => '$data->Supplier->getName()',
        	'filter'=>CHtml::activeDropDownList($model, 'supplier_id',array(''=>Yii::t("operations", Yii::t('global','Please Select')))+ IvyPProduct::model()->showSupplier(2)),
        ),
        array(
            'name' => 'status',
         	'value' => '$data->showStatus()',
         	'filter'=>CHtml::activeDropDownList($model, 'status',array(''=>Yii::t("operations", Yii::t('global','Please Select')))+array(Yii::t('operations', '显示'),Yii::t('operations', '隐藏'))),
        ),
		/*
		'p_period',
		'memo',
		'user_id',
		'create_timestamp',
		'update_timestamp',
		*/
		array(
			'class'=>'CButtonColumn',
            'template' => '{view} {update} {delete}',
		 	'updateButtonUrl' => 'Yii::app()->createUrl("/operations/product/create", array("id" => $data->id))',
			'viewButtonUrl' => 'Yii::app()->createUrl("/operations/product/promot", array("id" => $data->id))',
			'viewButtonOptions'=> array('class'=>'colorbox'),
		),
	),
)); ?>
