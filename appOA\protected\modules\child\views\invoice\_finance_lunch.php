<?php

	function renderLunchCancelStatus($refund){
		if($refund->child_credit_id && $refund->operate_timestamp){
			echo Yii::t("payment", "Refunded");
		}else{
			echo CHtml::openTag('span', array('class'=>'red')) . Yii::t("payment", "Awaiting") . CHtml::closeTag("span");
		}
	}

	$this->widget('ext.ivyCGridView.IvyCGridView', array(
		'id'=>'child-lunch',
		'dataProvider'=>$dataProvider,
		'template'=>"<div class='table_list'>{items}</div><div class='table_list'>{summary}</div><div class='table_list'>{pager}</div>",
		'colgroups'=>array(
			array(
				"colwidth"=>array(120,100,300,100),
			)
		),
		'ajaxUpdate'=>false,
		'columns'=>array(
			'target_date'=>array(
				'header' => Yii::t('payment','Target Date'),
				'value' => 'OA::formatDateTime($data->target_timestamp)'
			),
			'weekday'=>array(
				'header' => Yii::t('payment','Week Day'),
				'value' => 'Yii::t("lang", date("l", $data->target_timestamp))'
			),
			'class'=>array(
				'header' => Yii::t('payment','School/Class'),
				'value' => '$data->schoolInfo->abb . " / " . $data->classInfo->title',
			),
			'applyDate'=>array(
				'header' => Yii::t('payment','Cancel Date'),
				'value' => 'OA::formatDateTime($data->updated_timestamp)',
			),
			'status'=>array(
				'header' => Yii::t('payment','Status'),
				'value' => 'renderLunchCancelStatus($data)',
			)
		),
	));

?>