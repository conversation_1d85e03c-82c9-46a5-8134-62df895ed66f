<?php
$this->widget('zii.widgets.CMenu',array(
    'items'=> array(
        array('label'=>Yii::t('srbac','Managing auth items'), 'url'=>array("//srbac/authitem/manage")),
        array('label'=>Yii::t('srbac','Assign to users') . ' <b class="caret"></b>', 'url'=>'javascript:void(0)',
            'items'=>array(
                array('label'=>Yii::t('srbac','Assign Auth to Role'), 'url'=>array("//srbac/authitem/assignRole")),
                array('label'=>Yii::t('srbac','Assign Operation to Task'), 'url'=>array("//srbac/authitem/assignTask"))
            ),
            'linkOptions'=>array(
                'id'=>'auth-assign',
                'data-toggle'=>'dropdown'
            ),
            'itemOptions'=>array(
                'class'=>'dropdown',
                'id'=>'assign-dropdown',
            ),
            'submenuOptions'=>array(
                'class'=>'dropdown-menu'
            ),
        ),
        array('label'=>Yii::t('srbac','User\'s assignments'), 'url'=>array("//srbac/authitem/assignments")),
        array('label'=>Yii::t('srbac','Roles member'), 'url'=>array("//srbac/authitem/rolemember")),
    ),
    'htmlOptions'=>array('class'=>'nav nav-pills'),
    'activeCssClass'=>'active',
    'encodeLabel'=>false,
    'id'=>'auth-submenu',
));
?>