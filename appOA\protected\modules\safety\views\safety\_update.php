<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Routines'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('site', '安全事故管理') ?></li>
    </ol>

    <div class="row">
        <div class="col-md-12 mb10">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->module->getMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-tabs'),
                'activeCssClass' => 'active',
            ));
            ?>
        </div>
        <div class="col-md-12">
        <?php
        $form=$this->beginWidget('CActiveForm', array(
            'id'=>'visits-form',
            'enableAjaxValidation'=>false,
            'action' => $this->createUrl('update',array("id"=>$model->id)),
            'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
        ));
        ?>
            <div class="table-responsive">
                <table class="table table-bordered">
                    <tr>
                        <th class="text-center bg-success" colspan="9"><?php echo Yii::t('safety','Incident Reporting Process - Campus permissions') ?></th>
                    </tr>
                    <tr>
                        <th width="12.5%"><?php echo Yii::t('safety','Incident Date') ?></th>
                        <th width="12.5%"><?php echo Yii::t('safety','Incident Time') ?></th>
                        <th width="12.5%"><?php echo Yii::t('safety','Incident Name') ?></th>
                        <th width="12.5%" colspan="2"><?php echo Yii::t('safety','Parental Mood') ?></th>
                        <th width="12.5%"><?php echo Yii::t('safety','Media Intervention') ?></th>
                        <th width="12.5%"><?php echo Yii::t('safety','Government Intervention') ?></th>
                        <th width="12.5%"><?php echo Yii::t('safety','员工介入') ?></th>
                        <th width="12.5%"><?php echo Yii::t('safety','教委介入') ?></th>
                    </tr>
                    <tr>
                        <th>
                            <?php $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                                'model' => $model,
                                'attribute' => 'month',
                                'options' => array(
                                    'dateFormat' => 'yy-mm-dd',
                                ),
                                'htmlOptions' => array(
                                    'class' => 'form-control',
                                    'onchange' => 'timeData()',
                                    'placeholder' => Yii::t('management', '日期'),
                                ),
                            )); ?>
                        </th>
                        <th><?php echo $form->timeField($model,'minute',array('class'=>'form-control')); ?> </th>
                        <th><?php echo $form->textField($model,'incident_name',array('class'=>'form-control')); ?> </th>

                        <th colspan="2"><?php echo $form->textField($model,'parent_mood',array('class'=>'form-control')); ?> </th>
                        <th><?php echo $form->dropDownList($model,'media_in', array(1=> Yii::t('safety','YES'), 2 => Yii::t('safety','NO')), array('class'=>'form-control')); ?></th>
                        <th><?php echo $form->dropDownList($model,'government_in', array(1=> Yii::t('safety','YES'), 2 => Yii::t('safety','NO')), array('class'=>'form-control')); ?></th>
                        <th><?php echo $form->dropDownList($model,'staff_in', array(1=> Yii::t('safety','YES'), 2 => Yii::t('safety','NO')), array('class'=>'form-control')); ?></th>
                        <th><?php echo $form->dropDownList($model,'edu_in', array(1=> Yii::t('safety','YES'), 2 => Yii::t('safety','NO')), array('class'=>'form-control')); ?></th>
                    </tr>
                    <tr>
                        <th class="text-center bg-success" colspan="9"><?php echo Yii::t('safety','Campus Incident Roport') ?></th>
                    </tr>
                    <tr>
                        <th><?php echo Yii::t('safety','Date of the incident') ?></th>
                        <th colspan="4">
                            <?php echo $form->textField($model,'incident_date',array('class'=>'form-control','disabled' => 'disabled')); ?>
                        </th>
                        <th><?php echo Yii::t('safety','Reported by') ?></th>
                        <th><?php echo $model->reported_by ?> </th>
                        <th><?php echo Yii::t('safety','Reporter Position') ?></th>
                        <th><?php echo $model->reported_position ?> </th>
                    </tr>
                    <tr>
                        <th><?php echo Yii::t('safety','Place of the incident') ?></th>
                        <th colspan="4"><?php echo $form->textField($model,'incident_place',array('class'=>'form-control')); ?> </th>
                        <th><?php echo Yii::t('safety','Report Date') ?></th>
                        <th><?php
                            echo date("Y-m-d" , time());?>
                        </th>
                        <th><?php echo Yii::t('safety','Level of Risk') ?></th>
                        <th> <?php echo $form->dropDownList($model,'level', array(1=> "一级", 2 => "二级", 3 => "三级"),array('class'=>'form-control')); ?></th>
                    </tr>
                    <tr>
                        <th><?php echo Yii::t('safety','Incident') ?></th>
                        <th colspan="8"><?php echo $form->textArea($model,'incident_title',array('class'=>'form-control')); ?>  </th>
                    </tr>
                    <tr>
                        <th><?php echo Yii::t('safety','选择当事人') ?></th>
                        <th colspan="8">
                            <div class="col-xs-9">
                                <input type="radio" name="SafetyIncidentReport[incident_type]" <?php if($model['incident_type'] == 1){echo 'checked="checked"';} ?> id="status1" value="1"><label for="status1"><?php echo Yii::t('safety','Student')?></label>
                                <input type="radio" name="SafetyIncidentReport[incident_type]" <?php if($model['incident_type'] == 2){echo 'checked="checked"';} ?> id="status2" value="2"><label for="status2"><?php echo Yii::t('safety','Teacher')?></label>
                            </div>
                        </th>
                    </tr>
                    <tr id = "status1_th" style="<?php echo ($model->incident_type == 1) ? "" : "display:none;" ; ?>">
                        <th><?php echo Yii::t('safety','Situation of the person(s) injured in the incident') ?></th>
                        <th><?php echo Yii::t('safety','Student Name') ?> </th>
                        <th colspan="2"><?php
                            if($model->child_id){
                                echo $childBasicModel->getChildName();
                            }
                            $this->widget('ext.search.ChildSearchBox', array(
                                'acInputCSS' => 'form-control',
                                'acName' => 'searchChild_late',
                                'allowMultiple' => false,
                                'allowMultipleSchool' => false,
                                'simpleDisplay' => false,
                                'extendCss' => false,
                                'useModel' => true,
                                //'allowMultiple' => 8,
                                'model' => $model,
                                'attribute' => 'child_id',
                                'htmlOptions' => array('class' => 'form-control')
                            ))
                            ?> </th>
                        <th><?php echo Yii::t('safety','Injured Part') ?></th>
                        <th><?php echo $form->textField($model,'injured_part_child',array('class'=>'form-control')); ?>  </th>
                        <th><?php echo Yii::t('safety','Involved Teacher Name') ?></th>
                        <th colspan="2">
                            <?php
                            if($model->involved_teacher){
                                echo $user[$model->involved_teacher]->getName();
                            }
                            $this->widget('ext.search.StaffSearchBox', array(
                                'acInputCSS' => 'form-control',
                                'acName' => 'searchStaff_involved_teacher',
                                'htmlOptions' => array('class'=>'form-control'),
                                'data' => array(),
                                'useModel' => true,
                                'model' => $model,
                                'attribute' => 'involved_teacher',
                                'allowMultiple' => false,
                                'withAlumni' => false,
                            )) ?>
                        </th>
                    </tr>
                    <tr id = "status2_th" style="<?php echo ($model->incident_type == 2) ? "" : "display:none;" ; ?>">
                        <th><?php echo Yii::t('safety','Situation of the person(s) injured in the incident') ?></th>
                        <th><?php echo Yii::t('safety','Staff Name') ?></th>
                        <th colspan="3">
                            <?php
                            if($model->staff){
                                echo $user[$model->staff]->getName();
                            }
                            $this->widget('ext.search.StaffSearchBox', array(
                                'acInputCSS' => 'form-control',
                                'acName' => 'searchStaff_staff',
                                'htmlOptions' => array('class'=>'form-control'),
                                'data' => array(),
                                'useModel' => true,
                                'model' => $model,
                                'attribute' => 'staff',
                                'allowMultiple' => false,
                                'withAlumni' => false,
                            ))
                             ?>
                        </th>
                        <th><?php echo Yii::t('safety','Injured Part') ?></th>
                        <th  colspan="3"><?php echo $form->textArea($model,'injured_part_teacher',array('class'=>'form-control')); ?> </th>
                    </tr>
                    <tr>
                        <th><?php echo Yii::t('safety','Description of the incident') ?></th>
                        <th colspan="8"><?php echo $form->textArea($model,'incident_desc',array('class'=>'form-control')); ?>  </th>
                    </tr>
                    <tr>
                        <th rowspan="2"><?php echo Yii::t('safety','Process and current status description') ?></th>
                        <th colspan="2"><?php echo Yii::t('safety','是否去医院') ?></th>
                        <th colspan="6">
                            <div class="col-xs-9">
                                <input type="radio" name="SafetyIncidentReport[hospital]" id="hospital1" <?php if($model['hospital'] == 1){echo 'checked="checked"';} ?> value="1"><label for="hospital1"><?php echo Yii::t('safety','YES')?></label>
                                <input type="radio" name="SafetyIncidentReport[hospital]" id="hospital2" <?php if($model['hospital'] == 2){echo 'checked="checked"';} ?> value="2"><label for="hospital2"><?php echo Yii::t('safety','NO')?></label>
                            </div>
                        </th>
                    </tr>
                    <tr>
                        <th colspan="8"><?php echo $form->textArea($model,'incident_process',array('class'=>'form-control')); ?> </th>
                    </tr>
                    <tr>
                        <th colspan="9"><?php echo Yii::t('safety','Brife analysis of the incident') ?></th>
                    </tr>
                    <tr>
                        <th><?php echo Yii::t('safety','Immediate Cause') ?></th>
                        <th colspan="8"><?php echo $form->textArea($model,'immediate_case',array('class'=>'form-control')); ?> </th>
                    </tr>
                    <tr>
                        <th><?php echo Yii::t('safety','Primary Cause') ?></th>
                        <th colspan="8"><?php echo $form->textArea($model,'primary_case',array('class'=>'form-control')); ?> </th>
                    </tr>
                    <tr>
                        <th colspan="9"><?php echo Yii::t('safety','Immediate corrective action') ?></th>
                    </tr>
                    <tr>
                        <th colspan="9"><?php echo $form->textArea($model,'immediate_action',array('class'=>'form-control')); ?> </th>
                    </tr>
                    <tr>
                        <th colspan="9"><?php echo Yii::t('safety','Next step measure and speculation on the evolution direction of the incident') ?></th>
                    </tr>
                    <tr>
                        <th colspan="9"><?php echo $form->textArea($model,'next_action',array('class'=>'form-control')); ?> </th>
                    </tr>
                </table>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t("global", 'Submit');?></button>
            </div>
            <?php $this->endWidget(); ?>
        </div>
    </div>
</div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    $("#status1").click(function(){
        $("#status1_th").show();
        $("#status2_th").hide();
    });

    $("#status2").click(function(){
        $("#status2_th").show();
        $("#status1_th").hide();
    });

    $('.datepicker').datepicker({
        changeMonth: true,
        changeYear: true,
        dateFormat:'yy-mm-dd'
    });

    function timeData() {
        var month = $("#SafetyIncidentReport_month").val();
        //var minute = $("#SafetyIncidentReport_minute").val()
        $('#SafetyIncidentReport_incident_date').val(month)
    }

    function cbReport(data) {
        resultTip({msg: '添加成功', callback: function(){
            window.location.href = "<?php echo $this->createUrl('accident') ?>";
        }});
    }

</script>
