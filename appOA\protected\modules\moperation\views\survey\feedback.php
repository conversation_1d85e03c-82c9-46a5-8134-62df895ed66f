<style>
    [v-cloak] {
        display: none;
    }
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li>
            <?php echo CHtml::link(Yii::t('site','HQ Operations'), array('/moperation'))?>
        </li>
        <li>
            <?php echo CHtml::link(Yii::t('site','问卷管理'), array('index'))?>
        </li>
        <li class="active">
            <?php echo Yii::t('site','模版管理') ?>
        </li>
    </ol>

    <div class="row">
        <div class="col-md-2 col-sm-2">
            <?php

            $this->widget('zii.widgets.CMenu',array(
                'items'=> $this->getSubMenu(),
                'id'=>'pageCategory',
                'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked text-left background-gray'),
                'activeCssClass'=>'active',
                'itemCssClass'=>''
            ));
            ?>
        </div>
        <div class="col-md-10 col-sm-10" id='survey' v-cloak>
            <ul class="nav nav-pills col-md-12 mb15">
                <li :class="schoolid==''?'active':''">
                    <a :href="'<?php echo $this->createUrl('feedbackList') ?>?surveyId='+surveyid+'&isFb='+isFb+'&offset=1'" >全部</a>
                </li>
                <li v-for='(list,key,index) in schoolList'  :class="schoolid == key ? 'active':''" >
                    <a :href="'<?php echo $this->createUrl('feedbackList') ?>?surveyId='+surveyid+'&schoolId='+key+'&isFb='+isFb+'&offset=1'" >{{list}}</a>
                </li>
            </ul>
            <p class="pull-right"><button type="button" class="btn btn-primary">对校园开放</button></p>
            <div class="clearfix"></div>

            <div class="panel panel-default">
                <div class="panel-body">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>问卷标题</th>
                                <th>孩子姓名</th>
                                <th>反馈时间</th>
                                <th>校园</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody v-if='data.length!=0'>
                            <tr v-for='(data,id) in data'>
                                <td width="30%">{{data.surveyTitle}}</td>
                                <td  width="20%">{{data.childName}}</td>
                                <td  width="20%">{{data.fbTime}}</td>
                                <td  width="10%">{{schoolList[data.school]}}</td>
                                <td  width="10%">
                                    <a :href="'<?php echo $this->createUrl('previewFeedback') ?>?surveyId='+surveyid+'&schoolid='+schoolid+'&status=1&childId='+data.childid+''"  target="_blank" class="btn btn-xs btn-default mr5" >编辑</a>
                                    <a :href="'<?php echo $this->createUrl('previewFeedback') ?>?surveyId='+surveyid+'&schoolid='+schoolid+'&status=2&childId='+data.childid+''"  target="_blank" class="btn btn-xs btn-default mr5" >查看</a>
                                </td>
                            </tr>
                        </tbody>
                        <tbody v-else>
                            <tr>
                                <td colspan="5">暂无信息</td>
                            </tr>
                        </tbody>
                    </table>
                    <nav aria-label="Page navigation" class="text-left"  v-if='data.length!=0'>
                        <ul class="pagination">
                            <li v-if='currentPage>1'>
                                <a :href="'<?php echo $this->createUrl('feedbackList') ?>?surveyId='+surveyid+'&schoolId='+schoolid+'&isFb='+isFb+'&offset='+prev(currentPage)+''" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            <li v-else  class="disabled">
                                <a  aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            <li v-for='(data,index) in totalPage' :class="{ active:index+1==currentPage }">
                                <a  :href="'<?php echo $this->createUrl('feedbackList') ?>?surveyId='+surveyid+'&schoolId='+schoolid+'&isFb='+isFb+'&offset='+plus(index)+''">{{index+1}}</a>
                            </li>
                            <li  v-if='currentPage<totalPage'  >
                                <a :href="'<?php echo $this->createUrl('feedbackList') ?>?surveyId='+surveyid+'&schoolId='+schoolid+'&isFb='+isFb+'&offset='+plus(currentPage)+''" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li  v-else  class="disabled">
                                <a  aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
            
        </div>
    </div>
    <script>
        var datas = <?php echo json_encode($data) ?>;
        var schoolList   = <?php echo json_encode($schoolList) ?>;
        var surveyid = <?php echo json_encode($surveyid) ?>;
        var schoolid = <?php echo json_encode($schoolid) ?>;
        var isFb = <?php echo json_encode($isFb) ?>;
        var totalPages = <?php echo $totalPages ?>;
        console.log(totalPages)
        var offset = '<?php echo Yii::app()->request->getParam('offset', ''); ?>';
        var survey = new Vue({
            el: "#survey",
            data: {
                data: datas,
                schoolList:schoolList,
                schoolid:schoolid,
                surveyid:surveyid,
                isFb:isFb,
                totalPage:totalPages,
                currentPage:offset,
            },
            created: function() {},
            methods: {
                plus(index){
                    return Number(index)+1
                },
                prev(index){
                    return  Number(index)-1
                }
            }
        })
    </script>