<?php $form=$this->beginWidget('CActiveForm', array(
    'id'=>'staff-form',
    'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'enctype' => 'multipart/form-data'),
));
?>
<ol class="breadcrumb">
    <li class="text-info">常规信息</li>
</ol>
<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            <?php echo $form->labelEx($model->profile,'first_name',array('class'=>"col-sm-2 control-label")); ?>
            <div class="col-sm-10">
                <?php echo $form->textField($model->profile,'first_name',array('maxlength'=>255,'class'=>'form-control')); ?>
            </div>
        </div>
        <div class="form-group">
            <?php echo $form->labelEx($model->profile,'last_name',array('class'=>"col-sm-2 control-label")); ?>
            <div class="col-sm-10">
                <?php echo $form->textField($model->profile,'last_name',array('maxlength'=>255,'class'=>'form-control')); ?>
            </div>
        </div>
        <div class="form-group">
            <?php echo $form->labelEx($model,'name',array('class'=>"col-sm-2 control-label")); ?>
            <div class="col-sm-10">
                <?php echo $form->textField($model,'name',array('maxlength'=>255,'class'=>'form-control')); ?>
            </div>
        </div>
        <div class="form-group">
            <?php echo $form->labelEx($model,'email',array('class'=>"col-sm-2 control-label")); ?>
            <div class="col-sm-10">
                <?php echo $form->textField($model,'email',array('maxlength'=>255,'class'=>'form-control')); ?>
            </div>
        </div>
        <?php if (Yii::app()->user->checkAccess("o_StaffCreate")): ?>
        <div class="form-group">
            <?php echo $form->labelEx($model->staff, 'emailPass', array('class' => "col-sm-2 control-label")); ?>
            <div class="col-sm-10">
                <?php echo $form->textField($model->staff, 'emailPass', array('maxlength' => 255, 'class' => 'form-control')); ?>
            </div>
        </div>
        <div class="form-group">
            <?php echo $form->labelEx($model, 'iniPassword', array('class' => "col-sm-2 control-label")); ?>
            <div class="col-sm-8">
                <?php echo $form->textField($model, 'iniPassword', array('maxlength' => 255, 'class' => 'form-control')); ?>
            </div>
            <div class="col-sm-2">
                <?php echo CHtml::link(Yii::t('child','Generate'), 'javascript:void(0)', array('class'=>'setPass btn btn-default', 'rel'=>'User_iniPassword','onclick'=>'rp(this);'));?>
            </div>
        </div>
        <?php endif;?>
        <div class="form-group">
            <?php echo $form->labelEx($model->profile,'user_gender',array('class'=>"col-sm-2 control-label")); ?>
            <div class="col-sm-10">
                <?php echo $form->dropDownList($model->profile,'user_gender',OA::getChildGenderList(),array('maxlength'=>255,'class'=>'form-control','empty'=>Yii::t('global', 'Please Select'))); ?>
            </div>
        </div>
        <div class="form-group">
            <?php echo $form->labelEx($model->profile,'nationality',array('class'=>"col-sm-2 control-label")); ?>
            <div class="col-sm-10">
                <?php echo $form->dropDownList($model->profile,'nationality',Country::model()->getData(),array('maxlength'=>255,'class'=>'form-control','empty'=>Yii::t('global', 'Please Select'))); ?>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="form-group">
            <?php echo $form->labelEx($model->staff,'pemail',array('class'=>"col-sm-2 control-label")); ?>
            <div class="col-sm-10">
                <?php echo $form->textField($model->staff,'pemail',array('maxlength'=>255,'class'=>'form-control')); ?>
            </div>
        </div>
        <div class="form-group">
            <?php echo $form->labelEx($model->profile,'branch',array('class'=>"col-sm-2 control-label")); ?>
            <div class="col-sm-10">
                <?php echo $form->dropDownList($model->profile,'branch',Yii::app()->controller->cfg["branch"],array('maxlength'=>255,'class'=>'form-control','empty'=>Yii::t('global', 'Please Select'))); ?>
            </div>
        </div>
        <div class="form-group">
            <?php echo $form->labelEx($model->profile,'occupation_en',array('class'=>"col-sm-2 control-label")); ?>
            <div class="col-sm-10">
                <?php echo $form->dropDownList($model->profile,'occupation_en',DepPosLink::model()->getDepPos(),array('maxlength'=>255,'class'=>'form-control','empty'=>Yii::t('global', 'Please Select'))); ?>
            </div>
        </div>
        <div class="form-group">
            <?php echo $form->labelEx($model->staffApprover,'approver',array('class'=>"col-sm-2 control-label")); ?>
            <div class="col-sm-10">
                <?php echo $form->dropDownList($model->staffApprover,'approver',$model->staffApprover->approvers,array('maxlength'=>255,'class'=>'form-control','empty'=>Yii::t('global', 'Please Select'))); ?>
            </div>
        </div>
        <?php if ($model->user_avatar && $model->user_avatar != 'blank.gif'):?>
            <div class="form-group">
                <?php echo $form->labelEx($model,'user_avatar',array('class'=>"col-sm-2 control-label")); ?>
                <div class="col-sm-10">
                     <?php echo CHtml::image(Yii::app()->params["OAUploadBaseUrl"]."/users/thumbs/".$model->user_avatar,$model->name); ?>
                </div>
            </div>
        <?php endif;?>
        <div class="form-group">
            <?php echo $form->labelEx($model,'uploadPhoto',array('class'=>"col-sm-2 control-label")); ?>
            <div class="col-sm-10">
                <?php echo $form->fileField($model,'uploadPhoto'); ?>
            </div>
        </div>
    </div>
</div>
<?php if (Yii::app()->user->checkAccess("o_StaffApprove",null,null)): ?>
<ol class="breadcrumb">
    <li class="text-info">扩展信息</li>
</ol>
<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            <?php echo $form->labelEx($model->staff, 'mobile_telephone', array('class' => "col-sm-2 control-label")); ?>
            <div class="col-sm-10">
                <?php echo $form->textField($model->staff, 'mobile_telephone', array('maxlength' => 255, 'class' => 'form-control')); ?>
            </div>
        </div>
        <div class="form-group">
            <?php echo $form->labelEx($model->staff, 'card_id', array('class' => "col-sm-2 control-label")); ?>
            <div class="col-sm-10">
                <?php echo $form->textField($model->staff, 'card_id', array('maxlength' => 255, 'class' => 'form-control')); ?>
            </div>
        </div>
        <div class="form-group">
            <?php echo $form->labelEx($model->staff, 'card_id_due', array('class' => "col-sm-2 control-label")); ?>
            <div class="col-sm-10">
                <?php
                $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                    "model" => $model->staff,
                    "attribute" => "card_id_due",
                    "options" => array(
                        'changeMonth' => true,
                        'changeYear' => true,
                        'dateFormat' => 'yy-mm-dd',
                    ),
                    "htmlOptions" => array(
                        'class' => 'form-control'
                    )
                ));
                ?>
            </div>
        </div>
        <div class="form-group">
            <?php echo $form->labelEx($model->staff, 'startdate', array('class' => "col-sm-2 control-label")); ?>
            <div class="col-sm-10">
                <?php
                $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                    "model" => $model->staff,
                    "attribute" => "startdate",
                    "options" => array(
                        'changeMonth' => true,
                        'changeYear' => true,
                        'dateFormat' => 'yy-mm-dd',
                    ),
                    "htmlOptions" => array(
                        'class' => 'form-control'
                    )
                ));
                ?>
            </div>
        </div>
        <div class="form-group">
            <?php echo $form->labelEx($model->staff, 'leavedate', array('class' => "col-sm-2 control-label")); ?>
            <div class="col-sm-10">
                <?php
                $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                    "model" => $model->staff,
                    "attribute" => "leavedate",
                    "options" => array(
                        'changeMonth' => true,
                        'changeYear' => true,
                        'dateFormat' => 'yy-mm-dd',
                    ),
                    "htmlOptions" => array(
                        'class' => 'form-control'
                    )
                ));
                ?>
            </div>
        </div>
        <div class="form-group">
            <?php echo $form->labelEx($model->staff, 'contract_type', array('class' => "col-sm-2 control-label")); ?>
            <div class="col-sm-10">
                <?php echo $form->textField($model->staff, 'contract_type', array('maxlength' => 255, 'class' => 'form-control')); ?>
            </div>
        </div>
        <div class="form-group">
            <?php echo $form->labelEx($model->staff, 'contract_period', array('class' => "col-sm-2 control-label")); ?>
            <div class="col-sm-10">
                <?php
                $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                    "model" => $model->staff,
                    "attribute" => "contract_period",
                    "options" => array(
                        'changeMonth' => true,
                        'changeYear' => true,
                        'dateFormat' => 'yy-mm-dd',
                    ),
                    "htmlOptions" => array(
                        'class' => 'form-control'
                    )
                ));
                ?>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            <?php echo $form->labelEx($model->staff, 'social_insurance', array('class' => "col-sm-2 control-label")); ?>
            <div class="col-sm-10">
                <?php echo $form->textField($model->staff, 'social_insurance', array('maxlength' => 255, 'class' => 'form-control')); ?>
            </div>
        </div>
        <div class="form-group">
            <?php echo $form->labelEx($model->staff, 'emergency_person', array('class' => "col-sm-2 control-label")); ?>
            <div class="col-sm-10">
                <?php echo $form->textField($model->staff, 'emergency_person', array('maxlength' => 255, 'class' => 'form-control')); ?>
            </div>
        </div>
        <div class="form-group">
            <?php echo $form->labelEx($model->staff, 'emergency_contact', array('class' => "col-sm-2 control-label")); ?>
            <div class="col-sm-10">
                <?php echo $form->textField($model->staff, 'emergency_contact', array('maxlength' => 255, 'class' => 'form-control')); ?>
            </div>
        </div>
        <div class="form-group">
            <?php echo $form->labelEx($model->staff, 'dwelling_place', array('class' => "col-sm-2 control-label")); ?>
            <div class="col-sm-10">
                <?php echo $form->textField($model->staff, 'dwelling_place', array('maxlength' => 255, 'class' => 'form-control')); ?>
            </div>
        </div>
        <div class="form-group">
            <?php echo $form->labelEx($model->staff, 'native_place', array('class' => "col-sm-2 control-label")); ?>
            <div class="col-sm-10">
                <?php echo $form->textField($model->staff, 'native_place', array('maxlength' => 255, 'class' => 'form-control')); ?>
            </div>
        </div>
        <div class="form-group">
            <?php echo $form->labelEx($model->staff, 'education_degree', array('class' => "col-sm-2 control-label")); ?>
            <div class="col-sm-10">
                <?php echo $form->textField($model->staff, 'education_degree', array('maxlength' => 255, 'class' => 'form-control')); ?>
            </div>
        </div>
        <div class="form-group">
            <?php echo $form->labelEx($model->staff, 'archive_site', array('class' => "col-sm-2 control-label")); ?>
            <div class="col-sm-10">
                <?php echo $form->textField($model->staff, 'archive_site', array('maxlength' => 255, 'class' => 'form-control')); ?>
            </div>
        </div>
    </div>
</div>
<?php endif;?>
<div class="row">
    <div class="col-md-6">
        <div class="form-group">
            <?php echo $form->labelEx($model, 'chechOpUser', array('class' => "col-sm-2 control-label")); ?>
            <div class="col-sm-10">
                <?php echo $form->radioButtonList($model, 'chechOpUser', array('1'=>'是','0'=>'否'),array('maxlength' => 255, 'class' => 'checkbox-inline','')); ?>
            </div>
        </div>
    </div>
    <div class="col-md-6"></div>
</div>
<div class="row" id="submit-box">
    <div class="col-md-12">
        <div class="form-group">
            <div class="col-sm-1"></div>
            <div class="col-sm-11">
                <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
            </div>
        </div>
    </div>
</div>
<?php $this->endWidget(); ?>
<script type="text/javascript">
    function getRandomNum() {
        // between 0 - 1
        var rndNum = Math.random()
        // rndNum from 0 - 1000
        rndNum = parseInt(rndNum * 1000);
        // rndNum from 33 - 127
        rndNum = (rndNum % 94) + 33;
        return rndNum;
    }
    function checkPunc(num) {
        if ((num >=33) && (num <=47)) { return true; }
        if ((num >=58) && (num <=64)) { return true; }
        if ((num >=91) && (num <=96)) { return true; }
        if ((num >=123) && (num <=126)) { return true; }
        return false;
    }
    function randPassword()
    {
        var sPassword = "";
        for (i=0; i < 8; i++) {
            numI = getRandomNum();
            while (checkPunc(numI)) { numI = getRandomNum(); }
            sPassword = sPassword + String.fromCharCode(numI);
        }
        return sPassword;
    }
    $('a.setPass').bind('click',function(){
        var t='#'+$(this).attr('rel');
        $(t).val(randPassword());
    })
    $('#UserProfile_branch').bind('change',function(){
        var branchid = $(this).val();
        $("#StaffApprover_approver").empty();
        $("#StaffApprover_approver").append($('<option>', {
            value: '',
            text : '<?php echo Yii::t('global', 'Please Select');?>'
        }));
        if (branchid){
            $.ajax({
                type: "POST",
                url: "<?php echo $this->createUrl('//operations/hr/getUser'); ?>",
                data: "branchid="+branchid,
                dataType: "json",
                success: function(data){
                   $.each(data, function (i, item) {
                       $("#StaffApprover_approver").append($('<option>', {
                            value: i,
                            text : item
                       }));
                    });
                }
        });
        }
    })
</script>