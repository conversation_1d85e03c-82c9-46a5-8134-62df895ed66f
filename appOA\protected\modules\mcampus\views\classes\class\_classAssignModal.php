<!-- Modal -->
<div class="modal" id="classStaffModal" tabindex="-1" role="dialog" aria-labelledby="classStaffModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel"><?php echo Yii::t('campus','Assign Class Teachers');?> <small></small></h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="well"><?php echo Yii::t('campus','Click right arrows to assign');?></div>
                        <div>
                            <ul class="list-group" id="teacher-candidates">
                            </ul>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <div class="well">
                            <p><?php echo Yii::t('campus','Small sort number displays first');?></p>
                            <p><?php echo Yii::t('campus','Only selected Head Teachers will be displayed in Progress Report');?></p>
                        </div>
                        <form class="form J_ajaxForm" action="<?php echo $this->createUrl('//mcampus/classes/assignStaff');?>" method="POST">
                            <input type="hidden" name="postData[classid]" id="postData_classid" value="0" />
                            <input type="hidden" name="postData[calendarid]" id="postData_calendarid" value="<?php echo $calendarId;?>" />
                            <table class="table table-hover">
                                <thead>
                                <tr>
                                    <th width="40"></th>
                                    <th><?php echo Yii::t('campus','Teachers');?></th>
                                    <th class="text-center" width="100"><?php echo Yii::t('campus','Head Teacher');?></th>
                                    <?php if($this->branchObj->type == 50): ?>
                                    <th class="text-center" width="100"><?php echo Yii::t('campus','PTC');?></th>
                                    <?php endif; ?>
                                    <th class="text-center" width="100"><?php echo Yii::t('campus','Sort');?></th>
                                    <th class="text-center" width="40"></th>
                                </tr>
                                </thead>
                                <tbody id="edit-class-teachers">

                                </tbody>
                            </table>
                            <div class="pop_bottom">
                                <button onclick="$('#classStaffModal').modal('hide')" type="button" class="btn btn-default pull-right">
                                    <?php echo Yii::t('global','Cancel');?></button>
                                <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10">
                                    <?php echo Yii::t('global','Submit');?></button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/template" id="candidate-item-template">
    <li class="list-group-item teacher-candidate" teacherid=<%= id %> >
        <img src="<%= teacherPhotoBaseUrl %><% print(teacherData[id].photo); %>" height="20">
        <% print(teacherData[id].name); %> <a class="pull-right" href="#" onclick="assignThisOne(this)" teacherid="<%= id %>">
        <span class="glyphicon glyphicon-chevron-right"></span></a></li>
</script>

<script type="text/template" id="selected-item-template">
    <td><img src="<%= teacherPhotoBaseUrl %><% print(teacherData[teacherId].photo); %>" height="31"></td>
    <td><%= teacherData[teacherId].name %></td>
    <td class="text-center">
        <input name="teacherPost[<%= teacherId%>][isHead]" type="checkbox" <% if(isHead=="1"){print('checked="checked"');}%> >
    </td>
    <% if(isDaystar){ %>
    <td class="text-center">
        <input name="teacherPost[<%= teacherId%>][isHelp]" type="checkbox" <% if(isHelp=="1"){print('checked="checked"');}%> >
    </td>
    <%}%>
    <td class="text-center">
        <input name="teacherPost[<%= teacherId%>][weight]" type="text" class="text-center form-control" value="<%= weight %>">
    </td>
    <td class="text-center">
        <a class="destroy" href="#"><span class="glyphicon glyphicon-remove"></span></a>
    </td>
</script>

<script>
    var assignThisOne;
    var bbClassTeacher = Backbone.Model.extend({});
    var bbClassTeacherColl = Backbone.Collection.extend({});
    var theClassTeachers = new bbClassTeacherColl;
    var candiTemplate = _.template($('#candidate-item-template').html());

    $(function(){
        assignThisOne = function(obj){
            var teacherId = $(obj).attr('teacherId');
            if(!_.isUndefined(teacherData[teacherId])){
                teacherId = parseInt(teacherId);
                var _teacher = new bbClassTeacher;
                _teacher = _.clone({isHead: 0, isHelp: 0, teacherId: teacherId, weight: 9});
                theClassTeachers.add(_teacher);
            }
            $(obj).parents('.teacher-candidate').hide();
        };

        classStaff = function(obj){
            $('#classStaffModal h4.modal-title small').html( $(obj).parents('h4').find('label').html() );
            theClassTeachers = new bbClassTeacherColl;
            $('#edit-class-teachers').empty();
            $('#teacher-candidates').empty();
            assignedTeachers = [];
            teacherCandidates= [];
            var _classId = $(obj).parents('.list-group-item').attr('classId');

            $('#postData_classid').val(_classId);

//            $('#classStaffModal').modal({backdrop:'static'});
            $('#classStaffModal').modal();
            $('img.face').each(function(index,obj){
                assignedTeachers.push($(obj).attr('teacherId'));
            });
            teacherCandidates = _.uniq(
                _.union(
                    assignManyList,
                    _.difference(_.keys(teacherDatas),assignedTeachers)
                )
            );

            var sortTmpData = [];
            teacherCandidates.forEach(function(id){
                sortTmpData.push( {id:id,name:teacherData[id]['name']} );
            });
            if(!_.isEmpty(sortTmpData)){
                sortTmpData = _.sortBy(sortTmpData, 'name');
                _.each(sortTmpData, function(_d,_i){
                    var _view = candiTemplate(teacherData[_d.id]);
                    $('#teacher-candidates').append(_view);
                })
            };

            var OneTeacherView = Backbone.View.extend({
                tagName: 'tr',
                template: _.template($('#selected-item-template').html()),
                events:{
                    "click a.destroy"   :   "clear"
                },
                initialize: function(){
                    this.listenTo(this.model, 'change', this.render);
                    this.listenTo(this.model, 'destroy', this.remove);
                },
                render: function(){
                    this.$el.html(this.template(this.model.toJSON()));
                    return this;
                },
                clear: function(){
                    var _tid = this.model.get('teacherId');
                    var find = $('#teacher-candidates li.teacher-candidate[teacherId="'+ _tid +'"]');
                    if(find.length>0){
                        find.show();
                    }else{
                        var _view = candiTemplate(teacherData[_tid]);
                        $('#teacher-candidates').append(_view);
                    }
                    this.model.destroy();
                }
            });

            var PageTbodyView = Backbone.View.extend({
                el: $('#edit-class-teachers'),
                initialize: function(){
                    this.listenTo(theClassTeachers, 'add', this.addOne);
                    this.listenTo(theClassTeachers, 'reset', this.addAll);
                    this.listenTo(theClassTeachers, 'all', this.render);
                },
                render: function(){

                },
                addOne: function(teacher){
                    var view = new OneTeacherView({model:teacher});
                    this.$el.append(view.render().el);
                },
                addAll: function(){
                    theClassTeachers.each(this.addOne, this);
                }
            });
            var TbodyView = new PageTbodyView;
            if(!_.isNull(classTeacherData) && !_.isUndefined(classTeacherData[_classId])){
                classTeacherData[_classId].forEach(function(item){
                    if(!_.isUndefined(teacherData[item.teacherId])){
                        $('#teacher-candidates li.teacher-candidate[teacherId="'+ item.teacherId +'"]').hide();
                        var _teacher = new bbClassTeacher;
                        _teacher = _.clone(item);
                        theClassTeachers.add(_teacher);
                    }
                });
            }
        }
    });
    function assignedCallBack(data)
    {
        var classid = data.classid;
        classTeacherData[classid] = data.data;
        $('#classStaffModal').modal('hide');
        showTeachersInfo(classid);
    }

</script>