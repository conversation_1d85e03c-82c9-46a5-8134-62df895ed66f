<?php
if (true === $this->lang || 'cn' == $this->lang) {
    $df_cn = new CDateFormatter('zh-cn');
    $this->render('mailHeader_lang', array('lang' => 'cn'));
    ?>

    <div style="font-family: 'Microsoft Yahei'">


        <p>致校园支持团队，</p>
        <p>[<?php echo $class_name; ?>]班的[<?php echo $child_name; ?>]的家庭地址已修改：</p>

        <table cellspacing="0" cellpadding="0" border="1" align="center" style="border-collapse:collapse;">
            <tr>
                <th width="50%" style="color:#000000;font-family:'Microsoft Yahei'" bgcolor="#f2f2f2">当前家庭地址
                </th>
                <th width="50%" style="color:#999999;font-family:'Microsoft Yahei'" bgcolor="#dedede">修改之前的家庭地址
                </th>
            </tr>
            <tr>
                <td style="color:#000000;font-size:12px;padding:4px;font-family:'Microsoft Yahei'" bgcolor="#f2f2f2">
                    <?php echo $new_allergy; ?>
                </td>
                <td style="color:#999999;font-size:12px;padding:4px;font-family:'Microsoft Yahei'" bgcolor="#dedede">
                    <?php echo $original_allergy; ?>
                </td>
            </tr>
        </table>

        <p>如有疑问请及时与家长进行确认。</p>

        <p>
            <?php echo $df_cn->formatDateTime($date_time, 'medium', 'short'); ?>
        </p>

        <p>该邮件为系统自动发送。</p>

    </div>

    <?php
    $this->render('mailFooter_lang', array('lang' => 'cn'));
}
?>


<?php
if (true === $this->lang || 'en' == $this->lang) {
    $df_en = new CDateFormatter('en-us');
    $this->render('mailHeader_lang', array('lang' => 'en'));
    ?>

    <div style="font-family: 'Trebuchet MS','Microsoft Yahei',Arial,Helvetica,sans-serif">

        <p>Dear Campus Support Team,</p>
        <p>[<?php echo $child_name; ?>] at class [<?php echo $class_name; ?>] has Family Address needs:</p>

        <table cellspacing="0" cellpadding="0" border="1" align="center" style="border-collapse:collapse;">
            <tr>
                <th width="50%" style="color:#000000;font-family:'Trebuchet MS'" bgcolor="#f2f2f2">Current Family Address
                </th>
                <th width="50%" style="color:#999999;font-family:'Trebuchet MS'" bgcolor="#dedede">Original Family Address
                </th>
            </tr>
            <tr>
                <td style="color:#000000;font-size:12px;padding:4px;font-family:'Trebuchet MS'" bgcolor="#f2f2f2">
                    <?php echo $new_allergy; ?>
                </td>
                <td style="color:#999999;font-size:12px;padding:4px;font-family:'Trebuchet MS'" bgcolor="#dedede">
                    <?php echo $original_allergy; ?>
                </td>
            </tr>
        </table>

        <p>If you have any questions, please confirm with the parents as soon as possible.</p>

        <p>
            <?php echo $df_en->formatDateTime($date_time, 'medium', 'short'); ?>
        </p>

        <p>This is an automatic email.</p>

    </div>


    <?php
    $this->render('mailFooter_lang', array('lang' => 'en'));
}
?>
