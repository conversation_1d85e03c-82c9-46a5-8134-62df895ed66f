<div class="modal-header">
	<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
	<h4 class="modal-title" id="exampleModalLabel">添加到标配：<?php echo $pName; ?></h4>
</div>
<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'purchase-products-form',
	'enableAjaxValidation'=>false,
	'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
)); ?>

	<div class="modal-body" >
		<div class="form-group">
		    <label class="col-xs-4 control-label"><?php echo '选择要添加的标配:'; ?></label>
		    <div class="col-xs-4">
		    	<select class="select_3 form-control" name="PurchaseProductsItem[sid]">
		    		<?php foreach ($sdModels as $sdModel) { ?>
		    		<option value="<?php echo $sdModel->id ?>"><?php echo $sdModel->cn_title; ?></option>
		    		<?php } ?>
		    	</select>
		    </div>
		</div>

		<div class="form-group">
		    <label class="col-xs-4 control-label"><?php echo '选择要添加的数量:'; ?></label>
		    <div class="col-xs-4">
		    	<input name="PurchaseProductsItem[num]" class="form-control length_2" type="number" value="1">
		    	<input name="PurchaseProductsItem[pid]" type="hidden" value="<?php echo $pid; ?>">
		    </div>
		</div>
	</div>
	<div class="modal-footer">
		<button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
		<button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
	</div>

<?php $this->endWidget(); ?>

