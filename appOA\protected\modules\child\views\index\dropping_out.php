<?php $form=$this->beginWidget('CActiveForm', array(
    'id'=>'draw-form',
    'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm'),
));
?>

    <div class="pop_cont pop_table" style="height:auto;">
        <div class="tips_light">
            <p>
                <?php echo Yii::t('child','Please fill in the estimated leave date')?>
            </p>
            <p>
                该操作只是针对有明确退学意向的学生临时做标记用，并不会进行任何退学相关的实质性操作。请在学生确定退学时间之后进行退学处理。
            </p>
        </div>
        <table width="100%" style="margin-bottom: 180px">
            <colgroup>
                <col class="th" />
                <col />
            </colgroup>
            <tbody>
            <tr>
                <th style="width: 100px"><?php echo Yii::t('child', 'Estimated Leave Date')?></th>
                <td>
                    <?php
                    $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                        "name"=>'est_quit_date',
                        "value"=>$model->est_quit_date,
                        "model"=>$model,
//                        "attribute"=>"est_quit_date",
                        "options"=>array(
                            'changeMonth'=>true,
                            'changeYear'=>true,
                            'dateFormat'=>'yy-mm-dd', //很重要，不要随便修改 (RAN)
                        ),
                        "htmlOptions"=>array(
                            'class'=>'input'
                        )
                    ));
                    ?>
                </td>
            </tr>
            </tbody>
        </table>
    </div>

    <div class="pop_bottom">
        <button type="button" class="btn fr" id="J_dialog_close"><?php echo Yii::t('global','Cancel');?></button>
        <button class="btn fr btn_submit mr10 J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
    </div>

<?php $this->endWidget(); ?>
<style type="text/css">
    #ui-datepicker-div{
        /*top:140px !important;*/
    }
</style>
