<h1>
<?php echo Yii::t("navigations","Portfolio"); ?>
</h1>
<?php if (Yii::app()->language == 'en_us'): ?>
<p>
	You can read your child's class information and schedules, weekly journals, and semester reports under this category.
</p>
<p>
	Please click "Next" to see sub-navigations of this category.
</p>
<p>
	Or jump to the guide of other categories by clicking the links below:
</p>
<?php else: ?>
<p>
	您可以在此浏览孩子的班级信息、课程表、周报告以及学期报告等。
</p>
<p>
	请点击“下一步”来进一步了解成长实录的具体项目。
</p>
<p>
	或者点击以下链接跳转到其他页面的向导：
</p>
<?php endif ?>
<ul class="list">
<?php foreach(Mims::Nav() as $key=>$nav):?>
	<li>
		<?php
		if($key == "portfolio"){
			$nav['url'] = array('//portfolio/portfolio/classes');
		}
		$url = array_merge($nav['url'], array("demo"=>"guide"));
		echo CHtml::link($nav['label'], $url);
		?>
	</li>
<?php endforeach;?>
</ul>