<?php

/**
 * This is the model class for table "ivy_task_packpic".
 *
 * The followings are the available columns in table 'ivy_task_packpic':
 * @property integer $id
 * @property integer $childid
 * @property integer $startyear
 * @property integer $sweek
 * @property integer $eweek
 * @property integer $pid
 * @property string $pemail
 * @property integer $create_time
 * @property string $filelist
 * @property integer $pcount
 * @property string $dlpath
 * @property integer $pack_time
 * @property string $schoolid
 * @property integer $run_next
 */
class TaskPackpic extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return TaskPackpic the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_task_packpic';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
//			array('schoolid', 'required'),
			array('childid, startyear, sweek, eweek, pid, create_time, pcount, pack_time, run_next', 'numerical', 'integerOnly'=>true),
			array('pemail', 'length', 'max'=>64),
			array('filelist, dlpath', 'length', 'max'=>255),
			array('schoolid', 'length', 'max'=>20),
            array('schoolid', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, childid, startyear, sweek, eweek, pid, pemail, create_time, filelist, pcount, dlpath, pack_time, schoolid, run_next', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'childid' => 'Childid',
			'startyear' => 'Startyear',
			'sweek' => 'Sweek',
			'eweek' => 'Eweek',
			'pid' => 'Pid',
			'pemail' => 'Pemail',
			'create_time' => 'Create Time',
			'filelist' => 'Filelist',
			'pcount' => 'Pcount',
			'dlpath' => 'Dlpath',
			'pack_time' => 'Pack Time',
			'schoolid' => 'Schoolid',
			'run_next' => 'Run Next',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('startyear',$this->startyear);
		$criteria->compare('sweek',$this->sweek);
		$criteria->compare('eweek',$this->eweek);
		$criteria->compare('pid',$this->pid);
		$criteria->compare('pemail',$this->pemail,true);
		$criteria->compare('create_time',$this->create_time);
		$criteria->compare('filelist',$this->filelist,true);
		$criteria->compare('pcount',$this->pcount);
		$criteria->compare('dlpath',$this->dlpath,true);
		$criteria->compare('pack_time',$this->pack_time);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('run_next',$this->run_next);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}