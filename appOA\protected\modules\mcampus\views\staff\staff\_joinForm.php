<?php
$id = Yii::app()->request->getParam('id',0);
if ($id){
    $model = User::model()->findByPk($id);
    $userData['basic'] = $model->getAttributes();
    $userData['profile'] = $model->profile->getAttributes();
    $userData['staff'] = $model->staff->getAttributes();
    $userData['staff']['contract_period'] = OA::formatDateTime($model->staff->contract_period);
    $userData['approver'] = $model->staffApprover->getAttributes();
}else{
    $model = new User;
    $model->profile = new UserProfile;
    $model->staff = new Staff;
    $model->staffApprover = new StaffApprover;
    $model->profile->branch = $this->branchId;
    $userData['basic'] = $model->getAttributes();
    $userData['profile'] = $model->profile->getAttributes();
    $userData['staff'] = $model->staff->getAttributes();
    $userData['approver'] = $model->staffApprover->getAttributes();
}
$model->staffApprover->approvers = StaffApprover::getApproverUserBySystem($model->profile->branch);
$branchList = array();
$allBranch = $this->getAllBranch();
foreach ($this->accessBranch as $val){
    $branchList[$val] = $allBranch[$val]['title'];
}
?>
<script type="text/template" id="user-form-template">
    <?php echo CHtml::beginForm('', 'post', array('class'=>'J_ajaxForm form-horizontal','enctype' => 'multipart/form-data')); ?>
    <ol class="breadcrumb">
        <li class="text-info">常规信息</li>
    </ol>
    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <?php echo CHtml::label($model->profile->getAttributeLabel('first_name'), CHtml::getIdByName('UserProfile[first_name]'),array('class'=>"col-sm-2 control-label")); ?>
                <div class="col-sm-10">
                    <?php echo CHtml::textField('UserProfile[first_name]', '<%= profile.first_name %>', array('class' => "form-control length_5","encode"=>false)); ?>
                </div>
            </div>
            <div class="form-group">
                <?php echo CHtml::label($model->profile->getAttributeLabel('last_name'), CHtml::getIdByName('UserProfile[last_name]'),array('class'=>"col-sm-2 control-label")); ?>
                <div class="col-sm-10">
                    <?php echo CHtml::textField('UserProfile[last_name]', '<%= profile.last_name %>', array('class' => "form-control length_5","encode"=>false)); ?>
                </div>
            </div>
            <div class="form-group">
                <?php echo CHtml::label($model->getAttributeLabel('name'), CHtml::getIdByName('User[name]'),array('class'=>"col-sm-2 control-label")); ?>
                <div class="col-sm-10">
                    <?php echo CHtml::textField('User[name]', '<%= basic.name %>', array('class' => "form-control length_5",'encode'=>false)); ?>
                </div>
            </div>
            <div class="form-group">
                <?php echo CHtml::label($model->getAttributeLabel('email'), CHtml::getIdByName('User[email]'),array('class'=>"col-sm-2 control-label")); ?>
                <div class="col-sm-10">
                    <?php echo CHtml::textField('User[email]', '<%= basic.email %>', array('class' => "form-control length_5","encode"=>false)); ?>
                </div>
            </div>
             <?php if ($model->getScenario() == 'addStaff'):?>
            <div class="form-group">
                <?php echo CHtml::label($model->staff->getAttributeLabel('emailPass'), CHtml::getIdByName('Staff[emailPass]'),array('class'=>"col-sm-2 control-label")); ?>
                <div class="col-sm-10">
                    <?php echo CHtml::textField('Staff[emailPass]', '<%= staff.emailPass %>', array('class' => "form-control length_5","encode"=>false)); ?>
                </div>
            </div>
             <?php endif;?>
            <div class="form-group">
                <?php echo CHtml::label($model->getAttributeLabel('iniPassword'), CHtml::getIdByName('User[iniPassword]'),array('class'=>"col-sm-2 control-label")); ?>
                <div class="col-sm-2">
                    <?php echo CHtml::textField('User[iniPassword]', '', array('class' => "form-control length_2",'encode'=>false)); ?>
                </div>
                <div class="col-sm-8">
                    <?php echo CHtml::link(Yii::t('child','Generate'), 'javascript:void(0)', array('class'=>'setPass btn btn-default', 'rel'=>'User_iniPassword','onclick'=>'rp(this);'));?>
                </div>
            </div>

             <?php if ($model->user_avatar && $model->user_avatar != 'blank.gif'):?>
            <div class="form-group">
                <?php echo CHtml::label($model->getAttributeLabel('user_avatar'), CHtml::getIdByName('User[user_avatar]'),array('class'=>"col-sm-2 control-label")); ?>
                <div class="col-sm-10">
                     <?php echo CHtml::image(Yii::app()->params["OAUploadBaseUrl"]."/users/thumbs/<%= basic.user_avatar %>",'<%= basic.name %>',array("encode"=>false)); ?>
                </div>
            </div>
            <?php endif;?>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <?php echo CHtml::label($model->staff->getAttributeLabel('pemail'), CHtml::getIdByName('Staff[email]'),array('class'=>"col-sm-2 control-label")); ?>
                <div class="col-sm-10">
                    <?php echo CHtml::textField('Staff[pemail]', '<%= staff.pemail %>', array('class' => "form-control length_5","encode"=>false)); ?>
                </div>
            </div>
            <div class="form-group">
                <?php echo CHtml::label($model->profile->getAttributeLabel('user_gender'), CHtml::getIdByName('UserProfile[user_gender]'),array('class'=>"col-sm-2 control-label")); ?>
                <div class="col-sm-10">
                    <?php echo CHtml::dropDownList('UserProfile[user_gender]','<%= profile.user_gender %>',OA::getChildGenderList(),array('class' => "form-control length_5",'empty'=>Yii::t('global', 'Please Select'))); ?>
                </div>
            </div>
            <div class="form-group">
                <?php echo CHtml::label($model->profile->getAttributeLabel('nationality'), CHtml::getIdByName('UserProfile[nationality]'),array('class'=>"col-sm-2 control-label")); ?>
                <div class="col-sm-10">
                    <?php echo CHtml::dropDownList('UserProfile[nationality]', '<%= profile.nationality %>', Country::model()->getData(), array('class' => "form-control length_5",'empty'=>Yii::t('global', 'Please Select'),"encode"=>false)); ?>
                </div>
            </div>
            <div class="form-group">
                <?php echo CHtml::label($model->profile->getAttributeLabel('branch'), CHtml::getIdByName('UserProfile[branch]'),array('class'=>"col-sm-2 control-label")); ?>
                <div class="col-sm-10">
                    <?php echo CHtml::dropDownList('UserProfile[branch]', '<%= profile.branch %>', $branchList, array('class' => "form-control length_5",'empty'=>Yii::t('global', 'Please Select'),"onchange"=>"updateApprover(this);")); ?>
                </div>
            </div>
            <div class="form-group">
                <?php echo CHtml::label($model->profile->getAttributeLabel('occupation_en'), CHtml::getIdByName('UserProfile[occupation_en]'),array('class'=>"col-sm-2 control-label")); ?>
                <div class="col-sm-10">
                    <?php echo CHtml::dropDownList('UserProfile[occupation_en]', '<%= profile.occupation_en %>',DepPosLink::model()->getDepPos(), array('class' => "form-control length_5",'empty'=>Yii::t('global', 'Please Select'),"encode"=>false)); ?>
                </div>
            </div>
            <div class="form-group">
                <?php echo CHtml::label($model->staffApprover->getAttributeLabel('approver'), CHtml::getIdByName('StaffApprover[approver]'),array('class'=>"col-sm-2 control-label")); ?>
                <div class="col-sm-10">
                    <?php echo CHtml::dropDownList('StaffApprover[approver]', '<%= approver.approver %>', $model->staffApprover->approvers, array('class' => "form-control length_5",'empty'=>Yii::t('global', 'Please Select'),"encode"=>false)); ?>
                </div>
            </div>
            <div class="form-group">
                <?php echo CHtml::label($model->getAttributeLabel('uploadPhoto'), CHtml::getIdByName('User[uploadPhoto]'),array('class'=>"col-sm-2 control-label")); ?>
                <div class="col-sm-10">
                    <?php echo CHtml::fileField('User[uploadPhoto]'); ?>
                </div>
            </div>
        </div>
    </div>
    <ol class="breadcrumb">
        <li class="text-info">扩展信息</li>
    </ol>
    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <?php echo CHtml::label($model->staff->getAttributeLabel('mobile_telephone'), CHtml::getIdByName('Staff[mobile_telephone]'),array('class'=>"col-sm-2 control-label")); ?>
                <div class="col-sm-10">
                    <?php echo CHtml::textField('Staff[mobile_telephone]', '<%= staff.mobile_telephone %>', array('class' => "form-control length_5","encode"=>false)); ?>
                </div>
            </div>
            <div class="form-group">
                <?php echo CHtml::label($model->staff->getAttributeLabel('card_id'), CHtml::getIdByName('Staff[card_id]'),array('class'=>"col-sm-2 control-label")); ?>
                <div class="col-sm-10">
                    <?php echo CHtml::textField('Staff[card_id]', '<%= staff.card_id %>', array('class' => "form-control length_5","encode"=>false)); ?>
                </div>
            </div>
            <div class="form-group">
                <?php echo CHtml::label($model->staff->getAttributeLabel('card_id_due'), CHtml::getIdByName('Staff[card_id_due]'),array('class'=>"col-sm-2 control-label")); ?>
                <div class="col-sm-10">
                    <?php echo CHtml::textField('Staff[card_id_due]', '<%= staff.card_id_due %>', array('class' => "form-control length_5","encode"=>false)); ?>
                </div>
            </div>
            <div class="form-group">
                <?php echo CHtml::label($model->staff->getAttributeLabel('startdate'), CHtml::getIdByName('Staff[startdate]'),array('class'=>"col-sm-2 control-label")); ?>
                <div class="col-sm-10">
                    <?php echo CHtml::textField('Staff[startdate]', '<%= staff.startdate %>', array('class' => "form-control length_5","encode"=>false)); ?>
                </div>
            </div>
            <div class="form-group">
                <?php echo CHtml::label($model->staff->getAttributeLabel('leavedate'), CHtml::getIdByName('Staff[leavedate]'),array('class'=>"col-sm-2 control-label")); ?>
                <div class="col-sm-10">
                    <?php echo CHtml::textField('Staff[leavedate]', '<%= staff.leavedate %>', array('class' => "form-control length_5","encode"=>false)); ?>
                </div>
            </div>
            <div class="form-group">
                <?php echo CHtml::label($model->staff->getAttributeLabel('contract_type'), CHtml::getIdByName('Staff[contract_type]'),array('class'=>"col-sm-2 control-label")); ?>
                <div class="col-sm-10">
                    <?php echo CHtml::textField('Staff[contract_type]', '<%= staff.contract_type %>', array('class' => "form-control length_5","encode"=>false)); ?>
                </div>
            </div>
            <div class="form-group">
                <?php echo CHtml::label($model->staff->getAttributeLabel('contract_period'), CHtml::getIdByName('Staff[contract_period]'),array('class'=>"col-sm-2 control-label")); ?>
                <div class="col-sm-10">
                    <?php echo CHtml::textField('Staff[contract_period]', '<%= staff.contract_period %>', array('class' => "form-control length_5","encode"=>false)); ?>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <?php echo CHtml::label($model->staff->getAttributeLabel('social_insurance'), CHtml::getIdByName('Staff[social_insurance]'),array('class'=>"col-sm-2 control-label")); ?>
                <div class="col-sm-10">
                    <?php echo CHtml::textField('Staff[social_insurance]', '<%= staff.social_insurance %>', array('class' => "form-control length_5","encode"=>false)); ?>
                </div>
            </div>
            <div class="form-group">
                <?php echo CHtml::label($model->staff->getAttributeLabel('emergency_person'), CHtml::getIdByName('Staff[emergency_person]'),array('class'=>"col-sm-2 control-label")); ?>
                <div class="col-sm-10">
                    <?php echo CHtml::textField('Staff[emergency_person]', '<%= staff.emergency_person %>', array('class' => "form-control length_5","encode"=>false)); ?>
                </div>
            </div>
            <div class="form-group">
                <?php echo CHtml::label($model->staff->getAttributeLabel('emergency_contact'), CHtml::getIdByName('Staff[emergency_contact]'),array('class'=>"col-sm-2 control-label")); ?>
                <div class="col-sm-10">
                    <?php echo CHtml::textField('Staff[emergency_contact]', '<%= staff.emergency_contact %>', array('class' => "form-control length_5","encode"=>false)); ?>
                </div>
            </div>
            <div class="form-group">
                <?php echo CHtml::label($model->staff->getAttributeLabel('dwelling_place'), CHtml::getIdByName('Staff[dwelling_place]'),array('class'=>"col-sm-2 control-label")); ?>
                <div class="col-sm-10">
                    <?php echo CHtml::textField('Staff[dwelling_place]', '<%= staff.dwelling_place %>', array('class' => "form-control length_5","encode"=>false)); ?>
                </div>
            </div>
            <div class="form-group">
                <?php echo CHtml::label($model->staff->getAttributeLabel('native_place'), CHtml::getIdByName('Staff[native_place]'),array('class'=>"col-sm-2 control-label")); ?>
                <div class="col-sm-10">
                    <?php echo CHtml::textField('Staff[native_place]', '<%= staff.native_place %>', array('class' => "form-control length_5","encode"=>false)); ?>
                </div>
            </div>
            <div class="form-group">
                <?php echo CHtml::label($model->staff->getAttributeLabel('education_degree'), CHtml::getIdByName('Staff[education_degree]'),array('class'=>"col-sm-2 control-label")); ?>
                <div class="col-sm-10">
                    <?php echo CHtml::textField('Staff[education_degree]', '<%= staff.education_degree %>', array('class' => "form-control length_5","encode"=>false)); ?>
                </div>
            </div>
            <div class="form-group">
                <?php echo CHtml::label($model->staff->getAttributeLabel('archive_site'), CHtml::getIdByName('Staff[archive_site]'),array('class'=>"col-sm-2 control-label")); ?>
                <div class="col-sm-10">
                    <?php echo CHtml::textField('Staff[archive_site]', '<%= staff.archive_site %>', array('class' => "form-control length_5","encode"=>false)); ?>
                </div>
            </div>
        </div>
    </div>
    <div class="row" id="submit-box">
        <div class="col-md-12">
            <div class="form-group">
                <div class="col-sm-1"></div>
                <div class="col-sm-11">
                    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
                </div>
            </div>
        </div>
    </div>
    <?php echo CHtml::endForm(); ?>
</script>
<script type="text/javascript">
    var userData = <?php echo CJSON::encode($userData)?>;
    var template = _.template($('#user-form-template').html());
    $(function(){
        var view = template(userData);
            $("#form-content").html(view);
            $('.J_ajaxForm #UserProfile_user_gender option[value="'+userData.profile.user_gender+'"]').attr('selected', true);
            $('.J_ajaxForm #UserProfile_nationality option[value="'+userData.profile.nationality+'"]').attr('selected', true);
            $('.J_ajaxForm #UserProfile_branch option[value="'+userData.profile.branch+'"]').attr('selected', true);
            $('.J_ajaxForm #UserProfile_occupation_en option[value="'+userData.profile.occupation_en+'"]').attr('selected', true);
            $('.J_ajaxForm #StaffApprover_approver option[value="'+userData.approver.approver+'"]').attr('selected', true);
            $('.J_ajaxForm #Staff_card_id_due').datepicker({'changeMonth':true,'changeYear':true,'dateFormat':'yy-mm-dd'});
            $('.J_ajaxForm #Staff_startdate').datepicker({'changeMonth':true,'changeYear':true,'dateFormat':'yy-mm-dd'});
            $('.J_ajaxForm #Staff_leavedate').datepicker({'changeMonth':true,'changeYear':true,'dateFormat':'yy-mm-dd'});
            head.Util.aDialog();
            head.Util.ajaxForm();
    });
    function updateApprover(_this){
        var branchid = $(_this).val();
        $("#StaffApprover_approver").empty();
        $("#StaffApprover_approver").append($('<option>', {
        value: '',
        text : '<?php echo Yii::t('global', 'Please Select');?>'
        }));
        if (branchid){
            $.ajax({
                type: "POST",
                url: "<?php echo $this->createUrl('//operations/hr/getUser'); ?>",
                data: "branchid="+branchid,
                dataType: "json",
                success: function(data){
                   $.each(data, function (i, item) {
                       $("#StaffApprover_approver").append($('<option>', {
                            value: i,
                            text : item
                       }));
                    });
                }
            });
        }
    }
    function getRandomNum() {
        // between 0 - 1
        var rndNum = Math.random()
        // rndNum from 0 - 1000
        rndNum = parseInt(rndNum * 1000);
        // rndNum from 33 - 127
        rndNum = (rndNum % 94) + 33;
        return rndNum;
    }
    function checkPunc(num) {
        if ((num >=33) && (num <=47)) { return true; }
        if ((num >=58) && (num <=64)) { return true; }
        if ((num >=91) && (num <=96)) { return true; }
        if ((num >=123) && (num <=126)) { return true; }
        return false;
    }
    function randPassword()
    {
        var sPassword = "";
        for (i=0; i < 8; i++) {
            numI = getRandomNum();
            while (checkPunc(numI)) { numI = getRandomNum(); }
            sPassword = sPassword + String.fromCharCode(numI);
        }
        return sPassword;
    }

    function rp(_this){
        var t='#'+$(_this).attr('rel');
        $(t).val(randPassword());
    }
</script>

