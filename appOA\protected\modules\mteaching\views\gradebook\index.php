<style>
    
    [v-cloak] {
        display: none;
    }
    .borderTop{
        border-top: 1px solid #D9D9D9;
    }
    .p016{
        padding:0 16px
    }
    .group-p-box{
        border-radius: 4px;
        width:320px;
        transition: all 0.2s ease 0s;
    }
    .g-not-fold {
      width: 320px;
    }

    .g-is-fold {
      width: calc(320px - 300px);
      transition: all 0.2s;
    }

    .colorBlue{
        color:#4D88D2
    }
    .line40{
        padding: 16px 10px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 400;
        color: #333333;
        margin-bottom: 8px;
        position: relative;
    }
    .bgNum{
        background:rgba(77, 136, 210, 0.06)
    }
    .noNum{
        background:rgba(250, 250, 250, 1)
    }
    .evidenceNum{
        position: absolute;
        right: 0;
        top: 0;
        background: rgba(77,136,210,0.1);
        font-size: 12px;
        font-weight: 400;
        color: #4D88D2;
        line-height: 12px;
        padding: 2px 4px;
        border-radius: 0px 0px 0px 4px;
    }
    .bgLabel{
        background: #F2F3F5 !important;
    }
    .tabLabelTop{
        position: absolute;
        left: 0;
        top: 0;
        background: rgba(77,136,210,0.1);
        border-radius: 0px 0px 4px 0px;
        font-size: 12px;
        font-weight: 400;
        color: #4D88D2;
        line-height: 12px;
        padding: 2px 4px;
    }
    /* .line40:hover{
        background: #EBEDF0;
        cursor: pointer;
    } */
    .table thead tr th{
        text-align:center
    }
    .avatar{
        width:36px;
        height:36px;
        border-radius: 50%;
        object-fit: cover;
    }
    .itemCss{
        padding:6px 10px;
        cursor: pointer;
    }
    .itemCss:hover{
       background:#E8EAED
    }
    /* .modal{
        z-index:9999
    }
    .modal-backdrop{
        z-index:9998
    } */
    .borderRight{
        border-right: 1px solid #D9D9D9;
    }
    .maxWidth{
        max-width:320px;
    }
    .iconShow{
        position: absolute;
        right: -12px;
        cursor: pointer;
        top:-14px;
        z-index: 9;
    }
    .iconShow:hover{
        transform: scale(1.2)
    }
    .iconShow span{
        height: 24px;
        background: #FFFFFF;
        box-shadow:0px 2px 6px 0px rgba(0,0,0,0.3);
        border: 1px solid #E5E6EB;
        border-radius: 50%;
        text-align: center;
        line-height: 23px;
        font-size: 16px;
        display: inline-block;
        width: 24px;
    }
    .el-input__inner, .form-control {
        height: 34px;
        box-shadow: none;
        line-height:34px
    }
    .el-input__icon {
        line-height: 100%;
    }
    .selectTeacher {
        width: auto;
        max-height: 300px;
        position: absolute;
        background: #fff;
        border: 1px solid #E8EAED;
        border-radius: 3px;
        top: 35px;
        overflow-y: auto;
        z-index: 1;
        left: 50%;
        padding: 8px 8px 16px 8px;
        box-shadow: 0px 0px 6px 0px rgba(0,0,0,0.04), 0px 2px 4px 0px rgba(0,0,0,0.12);
        margin-left: -175px;
        text-align: left;
    }
    .selectName {
        height: 30px;
        line-height: 30px;
        padding-left: 10px;
    }
    .selectName:hover{
        background:#EEF4FB
    }
    .el-table__body-wrapper::-webkit-scrollbar ,.bgGray::-webkit-scrollbar ,.selectTeacher::-webkit-scrollbar,.tablescroll::-webkit-scrollbar,.popper_class::-webkit-scrollbar{
        width: 4px; /*滚动条宽度*/
        height: 4px; /*滚动条高度*/
    }
    .el-table__body-wrapper::-webkit-scrollbar-track , .bgGray::-webkit-scrollbar-track ,.selectTeacher::-webkit-scrollbar-track,.tablescroll::-webkit-scrollbar-track,.popper_class::-webkit-scrollbar-track {
        border-radius: 10px; /*滚动条的背景区域的圆角*/
        -webkit-box-shadow: inset 0 0 6px rgba(238,238,238, 0.1);
        background-color: #F5F7FA; /*滚动条的背景颜色*/
    }
    .el-table__body-wrapper::-webkit-scrollbar-thumb,.bgGray::-webkit-scrollbar-thumb ,.selectTeacher::-webkit-scrollbar-thumb,.tablescroll::-webkit-scrollbar-thumb,.popper_class::-webkit-scrollbar-thumb {
        border-radius: 10px; /*滚动条的圆角*/
        -webkit-box-shadow: inset 0 0 6px rgba(145, 143, 0143, 0.1);
        background-color: #ccc; /*滚动条的背景颜色*/
    }
    .el-table .el-table__fixed{
        height:auto !important;
        bottom:8px !important;
    }
    .el-table__body-wrapper {
        z-index:2;
    }
    .loading{
        width:98%;
        height:98%
    }
    .popoverEdit {
        z-index:99 !important
    }
    .sortList{
        padding:5px;
    }
    .sortList:hover{
        background:#EEF4FB;
        cursor: pointer;
    }
    .form-control{
        box-shadow: none;
        border: 1px solid #dcdfe6;
        box-sizing: border-box;
    }
    .form-control::-webkit-input-placeholder { /* Chrome/Opera/Safari */
        color: #ccc;
    }
    .el-step__head.is-process{
        color: #666666;
        border-color: #666666;
        font-size: 14px;
    }
    .el-step__title.is-process {
        color: #666666;
        font-size: 14px;
    }
    .el-step__title.is-finish{
        font-weight:bold;
        font-size: 14px;
    }
    .font500{
        font-weight:400
    }

    .tabLabel {
        background: #EBEDF0;
        border-radius: 2px;
        border: 1px solid #D9D9D9;
        padding: 1px 5px;
        font-size: 12px;
        color: #333;
        display: inline-block;
        line-height: 14px;
        height: 18px;
    }
    .text_overflow {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        line-height: 14px;
    }
    .el-table .el-table__cell,.cell{
        padding: 0 !important
    }
    .tooltipWidth{
        max-width:350px
    }
    .semester1{
        background: #D9EDF7;
        border-radius: 2px;
        padding:1px 5px;
        font-size:12px;
        color:#5BC0DE;
        display: inline-block;
    }
    .semester2{
        background: #DFF0D8;
        border-radius: 2px;
        padding:1px 5px;
        font-size:12px;
        color:#5CB85C;
        display: inline-block;
    }
    .el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf,.el-table--border .el-table__cell, .el-table__body-wrapper .el-table--border.is-scrolling-left~.el-table__fixed{
        border-bottom: 1px solid #E8EAED;
    }
    .inputSelect{
        margin:0 auto
    }
    .warningText{
        color:#F0AD4E
    }
    .pl30{
        padding-left:30px
    }
    .cupDis{
        cursor: not-allowed;
    }
    .borderBto{
        border-bottom:1px solid #EBEDF0
    }
    .el-divider{
        background-color:#999999
    }
    .el-table__body,
    .el-table__header {
    width: 100%;
    table-layout: fixed !important;
    font-size:12px
    }
    .el-table th.gutter {
    display: table-cell !important;
    }
    .el-table colgroup.gutter {
    display: table-cell !important;
    }
    #table {
        table-layout: fixed;
        width: 100%;
        border-top:none
    }
    #table tr td,#table tr th {
        padding:0;
        width:300px;
        border-top:1px solid #ddd;
    }
    .tablePadding{
        padding:8px
    }
    .tableMb{
        margin-bottom:0;
        font-weight:400
    }
    #table tr th {
        border-bottom:none;
        border-top:none;
    }
    #table  .thClass {
        position: sticky;
        top: 0;
        background: #FAFAFA;
        z-index: 1;
        border-bottom:1px solid #ddd;
        border-top:1px solid #ddd;
    }
    .moreClass{
        background: #F7F7F8;
        border-radius: 4px;
        padding:16px;
        margin-top:16px
    }
    .warning_outline{
        padding:6px 8px;
        background:#F7F7F8;
        margin-left:15px;
        border-radius:2px;
        color:#666666;
        font-size:12px
    }
    .custom-tree-node {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 12px;
        padding-right: 8px;
        padding:5px;
        border-radius:4px
    }
    .modal-backdrop{
        z-index:1900
    }
    .modal{
        z-index:2000 !important

    }
    .el-table__body .el-table__row.hover-row td{
        background-color: transparent !important;
        cursor: pointer;
     }
     .el-tree-node__content{
        height:auto
     }
     .el-tree-node{
        white-space: normal;
        margin: 5px 0;
     }
     .popper_class{
        max-height:500px;
        overflow-y:auto ;
        z-index:999 !important
     }
     .addIcon:hover{
        color:#4D88D2 !important;
        cursor: pointer;
     }
    .name_overflow{
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
     }
     .warning-row{
        background:#fafafa !important
     }
     .labelTag{
        /* font-size: 12px;
        color: #666666;
        padding:2px 4px;
        background: #E5E6EB;
        border-radius:2px;
        display:inline */
            font-size: 12px;
        color: #666666;
        padding: 0px 4px;
        background: #E5E6EB;
        border-radius: 2px;
        margin-top: 2px;
        height:16px;
        line-height: 16px;
     }
</style>
<div class="container-fluid"  id='container' v-cloak>
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Teaching Tasks'), array('//mteaching/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('site', 'K-G5 GradeBook'); ?></li>
    </ol>
    <div v-if='Object.keys(school_year).length!=0'>
        <div class='flex row borderTop' >
            <div class='relative borderRight'  :style="'height:'+(height-141)+'px;'">
                <div class='iconShow' @click="toggleSidebar">
                    <el-tooltip class="item" effect="dark"  placement="top">
                        <div slot="content">{{isOpen?"<?php echo Yii::t("newDS", "Collapse"); ?>":"<?php echo Yii::t("newDS", "Expand"); ?>"}}</div>
                        <span :class='isOpen?"el-icon-arrow-left":"el-icon-arrow-right"' class='color6'></span>
                    </el-tooltip>
                </div>
                <div class='bgGray' style='min-width:15px' :style="'height:'+(height-141)+'px;overflow-x: hidden;'" :class="['group-p-box', isOpen ? 'g-not-fold' : 'g-is-fold']">
                    <div class='p016 pt24' v-if='isOpen'>
                        <div class='mb20'>
                            <el-select v-model="currentyear" placeholder="<?php echo Yii::t("labels", "School Year"); ?>"  @change='getInit(currentTeacher)' style='width:100%'>
                                <el-option
                                v-for="item in school_year"
                                :key="item.startyear"
                                :label="item.title"
                                :value="item.startyear">
                                </el-option>
                            </el-select>
                        </div>
                        <div  v-if='initData.isSuper'>
                            <div class='flex align-items cur-p'  @click='otherTeacher' >
                                <span class='el-icon-user-solid font20 colorBlue mr10'></span>
                                <span class='font18 fontBold colorBlue name_overflow'>{{initData.teacher_name}}</span>
                                <span  class='el-icon-arrow-down font20 colorBlue ml5' ></span>
                            </div>
                        </div>
                        <div class='flex align-items' v-else>
                            <span class='el-icon-user-solid font20 colorBlue mr10'></span>
                            <span class='font18 fontBold colorBlue flex1 name_overflow'>{{initData.teacher_name}}</span>
                        </div>
                        <div v-if='class_list.length!=0 && subjectList.length!=0'>
                            <div class='mt16'>
                                <div class='color3 font14 mb8'><?php echo Yii::t("report", "Class"); ?>:</div>
                                <el-select v-model="classid" placeholder="<?php echo Yii::t("attends", "Select a class"); ?>" class='optionSearch ' @change='getTree()' style='width:100%'>
                                    <el-option
                                        v-for="item in class_list"
                                        :key="item.classid"
                                        :label="item.title"
                                        :value="item.classid">
                                    </el-option>
                                </el-select>
                            </div>
                            <div class='mt16'>
                                <div class='color3 font14 mb8'><?php echo Yii::t("ptc", "Subject"); ?>:</div>
                                <el-select v-model="program" placeholder="<?php echo Yii::t("ptc", "Subject"); ?>" @change='showTree()' style='width:100%'>
                                    <el-option
                                    v-for="item in subjectList"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                    </el-option>
                                </el-select>
                            </div>
                            <div class='mt16'>
                                <div class='color3 font14 mb8'><?php echo Yii::t("teaching", "Academic Standards"); ?>:</div>
                                <el-input v-model="searchInput" placeholder="<?php echo Yii::t("teaching", "Input keywords to filter"); ?>" style='width:100%' prefix-icon="el-icon-search" clearable></el-input>
                            </div>
                        </div>
                        <div v-else class='color9 mt16 font14'>
                            <?php echo Yii::t("teaching", "No class or subject assigned"); ?>
                        </div>
                        <div class='mt12' v-if='SearchTreeData.length!=0'>
                            <!-- <div class='color6 font14'><?php echo Yii::t("teaching", "Academic Standards"); ?></div> -->
                            <div v-for='(list,index) in SearchTreeData'>
                                <div class='color6 font12 mb8 mt12' v-if='searchInput==""'>{{list.title}}</div>
                                <div v-if='list.children.length!=0' :class='searchInput!=""?"mt12":""'>
                                    <div v-for='(item,idx) in list.children'>
                                        <div  class='line40 flex align-items' :class='item.evidenceNum?"bgNum":"noNum"'>
                                            <span class="tabLabelTop colorc" :class='item.evidenceNum?"":"color6 bgLabel"'>{{item.standard_code}} </span>
                                            <el-tooltip class="item" effect="dark" content="<?php echo Yii::t("teaching", "Add Evidence"); ?>" placement="top">
                                                <div slot="content">{{translate("<?php echo Yii::t('teaching', 'Total %s times added'); ?>", item.evidenceNum)}}</div>
                                                <span class="evidenceNum" v-if='item.evidenceNum'><span class='el-icon-check'></span> {{item.evidenceNum}} </span>
                                            </el-tooltip>
                                            <span class='flex1'>
                                                 {{item.title}}
                                            </span>
                                            <el-tooltip class="item" effect="dark" content="<?php echo Yii::t("teaching", "Add Evidence"); ?>" placement="top">
                                                <span class='el-icon-circle-plus-outline color3 addIcon font16 ml16' v-if='initData.editall' @click='addInfo(item)'></span>
                                                <span v-else>
                                                    <span class='el-icon-circle-plus-outline color3 addIcon font16 ml16' v-if='currentTeacher==initData.teacher_info.uid' @click='addInfo(item)'></span>
                                                </span>
                                            </el-tooltip>
                                            <!-- <div v-if='initData.editall'>
                                                <el-dropdown >
                                                    <span class='el-icon-circle-plus-outline colorBlue font16 ml16'  ></span>
                                                    <el-dropdown-menu slot="dropdown">
                                                        <el-dropdown-item @click.native='addInfo(item)'><?php echo Yii::t("teaching", "Add Evidence"); ?></el-dropdown-item>
                                                        <el-dropdown-item @click.native='addInfo(item,1)' :disabled='item.finalExam && item.finalExam.indexOf(10)!=-1?true:false'><?php echo Yii::t("teaching", "Progress Report S1"); ?></el-dropdown-item>
                                                        <el-dropdown-item @click.native='addInfo(item,2)' :disabled='item.finalExam && item.finalExam.indexOf(20)!=-1?true:false'><?php echo Yii::t("teaching", "Progress Report S2"); ?></el-dropdown-item>
                                                    </el-dropdown-menu>
                                                </el-dropdown>
                                            </div> -->
                                            <span v-else-if='currentTeacher==initData.teacher_info.uid'>
                                                <el-dropdown>
                                                    <span class='el-icon-circle-plus-outline colorBlue font16 ml16'  ></span>
                                                    <el-dropdown-menu slot="dropdown">
                                                        <el-dropdown-item @click.native='addInfo(item)'><?php echo Yii::t("teaching", "Add Evidence"); ?></el-dropdown-item>
                                                        <el-dropdown-item @click.native='addInfo(item,1)' :disabled='item.finalExam && item.finalExam.indexOf(10)!=-1?true:false'><?php echo Yii::t("teaching", "Progress Report S1"); ?><</el-dropdown-item>
                                                        <el-dropdown-item @click.native='addInfo(item,2)' :disabled='item.finalExam && item.finalExam.indexOf(20)!=-1?true:false'><?php echo Yii::t("teaching", "Progress Report S2"); ?></el-dropdown-item>
                                                    </el-dropdown-menu>
                                                </el-dropdown>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div v-else-if='searchInput==""' class='color9'>
                                    <?php echo Yii::t("teaching", "No standard item"); ?>
                                </div>
                            </div>
                        </div>
                        <div v-else-if='categoryList' class='color9 mt20'>
                            <?php echo Yii::t("teaching", "No standard item"); ?>
                        </div>
                    </div>
                </div>
            </div>

            <div class='col-md-8 pt24  col-sm-8 flex1'>
                <div class='loading'  v-if='!showTableScore'>
                    <span></span>
                </div>
                <div v-if='class_list.length!=0 && subjectList.length!=0'>
                    <div v-if='columns.length>0'>
                        <div class='flex align-items'>
                            <div class='flex1'>
                                <el-popover
                                    placement="bottom-start"
                                    width='600'
                                    popper-class='popper_class'
                                    trigger="click">
                                    <el-checkbox v-model="checkedAll" @change='checkedChange($event)'><?php echo Yii::t("global", "Select All"); ?></el-checkbox>
                                    <el-tree
                                    :data="treeData"
                                        show-checkbox
                                        default-expand-all
                                        node-key="id"
                                        ref="tree"
                                        highlight-current
                                        @check="handleCheck"
                                        icon-class='icon_class'
                                        :props="defaultProps">
                                        <span class="custom-tree-node" slot-scope="{ node, data }" :class='data.evidenceNum?"bgNum":""'>
                                            <span>{{data.standard_code}} {{ data.title }}</span>
                                            <span v-if='data.standard_code'>
                                                <el-dropdown >
                                                    <span class='el-icon-circle-plus-outline color3 addIcon font16 ml16'  ></span>
                                                    <el-dropdown-menu slot="dropdown">
                                                        <el-dropdown-item @click.native='addInfo(data)'><?php echo Yii::t("teaching", "Add Evidence"); ?></el-dropdown-item>
                                                        <el-dropdown-item @click.native='addInfo(data,1)' :disabled='data.finalExam && data.finalExam.indexOf(10)!=-1?true:false'><?php echo Yii::t("teaching", "Progress Report S1"); ?></el-dropdown-item>
                                                        <el-dropdown-item @click.native='addInfo(data,2)' :disabled='data.finalExam && data.finalExam.indexOf(20)!=-1?true:false'><?php echo Yii::t("teaching", "Progress Report S2"); ?></el-dropdown-item>
                                                    </el-dropdown-menu>
                                                </el-dropdown>
                                                <!-- <el-tooltip class="item" effect="dark" content="<?php echo Yii::t("teaching", "Add Evidence"); ?>" placement="top">
                                                    <span class='el-icon-circle-plus-outline color3 addIcon font16 ml16' v-if='initData.editall' @click='addInfo(data)'></span>
                                                    <span v-else>
                                                        <span class='el-icon-circle-plus-outline color3 addIcon font16 ml16' v-if='currentTeacher==initData.teacher_info.uid' @click='addInfo(data)'></span>
                                                    </span>
                                                </el-tooltip> -->
                                            </span>
                                        </span>
                                    </el-tree>
                                    <span class="el-dropdown-link btn-link dropdown-toggle font14" slot="reference" >
                                    <span class='glyphicon glyphicon-filter'></span> <?php echo Yii::t("teaching", "Columns"); ?> <i class="el-icon-arrow-down el-icon--right btn-link"></i>
                                    </span>
                                </el-popover>

                                <el-dropdown trigger="click">
                                    <span class="el-dropdown-link ml24  btn-link">
                                        {{semesterData[filterTerm]}}<i class="el-icon-arrow-down el-icon--right"></i>
                                    </span>
                                    <el-dropdown-menu slot="dropdown">
                                        <el-dropdown-item @click.native='filterSemester(item.value)' v-for='(item,index) in semester'>{{item.label}}</el-dropdown-item>
                                    </el-dropdown-menu>
                                </el-dropdown>
                                <span class='warning_outline'><span class='el-icon-info mr5 font14'></span><?php echo Yii::t("referral", "Double-click grid to edit the score"); ?></span>
                            </div>
                            <div>
                                <button type="button" class="btn btn-primary" @click='exportTab()'><?php echo Yii::t("user", "Export"); ?></button>
                            </div>
                        </div>
                        <div class='mt15'>
                            <div v-if='tableColumns.length>0'>
                            <el-table
                                :data="tableDataList"
                                id='exportTable'
                                :row-class-name="tableRowClassName"
                                :row-style="selectedRowStyle"
                                :cell-class-name="tableCellClassName"
                                :cell-style="selectedCellStyle"
                                @cell-dblclick="tabClick"
                                :header-cell-style="headerStyleEvent"
                                row-key="id"
                                :height="''+(height-211)+''"
                                ref='table'
                                :key='itemKey'>
                                <el-table-column
                                    fixed
                                    align="center"
                                    header-align='center'
                                    min-width='150'
                                    width='200'
                                    prop="student"
                                    >
                                    <template slot-scope="scope">
                                        <div v-if='scope.row.child_id==-1' class='color3 ml8 p10'><?php echo Yii::t("teaching", "Full Score"); ?></div>
                                        <div v-else-if='scope.row.child_id==-2' class='color3 ml8 p10'><?php echo Yii::t("application", "Average Score"); ?></div>
                                        <div class='flex align-items p10' v-else-if='tableChild[scope.row.child_id]'  @click='cellClick(scope.row,scope.column)'>
                                            <img :src="tableChild[scope.row.child_id].avatar" alt="" class='avatar'>
                                            <div class='font12 color3 ml8 text-left'>
                                               <div style='line-height:16px'> {{tableChild[scope.row.child_id].name}} </div>
                                               <div v-if='tableChild[scope.row.child_id].status==999' class='labelTag'><?php echo Yii::t("teaching", "Dropped out"); ?></div>
                                               <div v-if='tableChild[scope.row.child_id].status==20 && tableChild[scope.row.child_id].classid!=classid'  class='labelTag'><?php echo Yii::t("teaching", "Transferred"); ?></div>
                                            </div>
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column v-for="(item, index) in tableColumns" :key="index" :label="item.title" min-width='180'  align="center">
                                    <template  #header>
                                        <div class='color3 font500 p10'> {{item.title}}</div>
                                    </template>
                                    <template slot-scope="scope">
                                    <div class='color3 font500 p10'>{{scope.row[item.prop]}} </div>
                                    </template>
                                    <template>
                                    <el-table-column v-for="(subItem, subIndex) in item.children" :key="subIndex" :label="subItem.title" min-width='150'  align="center">
                                        <template  #header>
                                            <el-tooltip class="item" effect="dark" :content="subItem.title" placement="top"  popper-class='tooltipWidth'>
                                                <div class='color3 font500 text_overflow p10'> <span class="tabLabel colorc" >{{subItem.standard_code}}</span> {{subItem.title}}</div>
                                            </el-tooltip>
                                        </template>
                                        <template slot-scope="scope">
                                            {{ scope.row[subItem.prop] }}
                                        </template>
                                        <template >
                                        <el-table-column v-for="(threeItem, threeIndex) in subItem.children" prop="itemScore" :key="threeIndex" :label="threeItem.label" min-width='150'  align="center">
                                            <template  #header>
                                                <div class='flex align-items p10'>
                                                    <el-popover
                                                        placement="bottom"
                                                        min-width='150'
                                                        popper-class='popoverEdit'
                                                        trigger="click">
                                                        <div v-if='threeItem.type==1'>
                                                            <div class='color6 font12 itemCss' @click='editInfo(item,subItem,threeItem,"info")'> <?php echo Yii::t("teaching", "Edit Evidence"); ?></div>
                                                            <div class='color6 font12 itemCss'  @click='editInfo(item,subItem,threeItem,"score")'> <?php echo Yii::t("teaching", "Edit Score"); ?></div>
                                                            <div class='color6 font12 itemCss' @click='delTable(threeItem,"score")'> <?php echo Yii::t("asa", "Delete"); ?></div>
                                                        </div>
                                                        <div v-if='threeItem.type==2'>
                                                            <div class='color6 font12 itemCss'  @click='editInfo(item,subItem,threeItem,"score","end")'> <?php echo Yii::t("teaching", "Progress Report"); ?></div>
                                                            <div class='color6 font12 itemCss' @click='delTable(threeItem,"end")'> <?php echo Yii::t("asa", "Delete"); ?></div>
                                                        </div>
                                                        <span slot="reference" >
                                                            <span class='el-icon-edit btn-link'  v-if='initData.editall'></span>
                                                            <span v-else>
                                                                <span class='el-icon-edit btn-link'   v-if='currentTeacher==initData.teacher_info.uid'  ></span>
                                                            </span>
                                                        </span>
                                                    </el-popover>
                                                    <el-tooltip class="item" effect="dark"  placement="top"  popper-class='tooltipWidth'>
                                                        <div slot="content"><span>{{threeItem.semester==10?"<?php echo Yii::t("report", "Term 1"); ?>":"<?php echo Yii::t("report", "Term 2"); ?>"}}</span><br/>{{threeItem.title}}<br/><div v-html='threeItem.desc'></div> </div>
                                                        <span class='color3 flex1 font500 text_overflow ml4'><span class="semester1 mr5" v-if='threeItem.semester==10' >T1</span><span class="semester2 mr5" v-if='threeItem.semester==20' >T2</span> {{threeItem.title}}</span>
                                                    </el-tooltip>
                                                </div>
                                            </template>
                                            <template slot-scope="scope">
                                                <!-- {{scope.row.child_id}} {{tableChild[scope.row.child_id].status}} -->
                                                <span v-if="scope.row.index === tabClickIndex && scope.column.id==tabClickLabel" >
                                                    <el-input v-model="scope.row[threeItem.id]"
                                                        maxlength="300"
                                                        class='p10'
                                                        :class="scope.column.id+'_'+scope.row.index"
                                                        placeholder="<?php echo Yii::t("ptc", "Score"); ?>"
                                                        size="mini" @keyup.enter.native="$event.target.blur()" @blur='inputBlur(threeItem.id,scope.row.child_id,scope.row[threeItem.id],scope.row)'/>
                                                </span>
                                                <div v-else @click='cellClick(scope.row,scope.column)' class='pt10 pb10'>{{ scope.row[threeItem.id] }}</div>
                                            </template>
                                        </el-table-column>
                                        </template>
                                    </el-table-column>
                                    </template>

                                </el-table-column>
                            </el-table>
                            </div>
                            <el-empty v-else description="<?php echo Yii::t('ptc', 'No Data'); ?>"></el-empty>
                        </div>
                    </div>
                    <div v-else-if='showTableScore && classid!="" && program!=""'>
                        <el-empty description="<?php echo Yii::t('teaching', 'No Evidence Added');?>"></el-empty>
                    </div>
                </div>
                <div v-else>
                    <el-empty description="<?php echo Yii::t('teaching', 'No Data');?>"></el-empty>

                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="delModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("global", "Delete"); ?></h4>
                </div>
                <div class="modal-body p24">
                    <div class='color3 font14'><?php echo Yii::t("directMessage", "Are you sure to delete this evidence?"); ?></div>
                    <div class='flex mt12'><span class='color6'><?php echo Yii::t("asa", "Class:"); ?></span> <span class='color6 flex1'> {{showClass(classid)}}</span></div>
                    <div class='flex mt5'><span class='color6'><?php echo Yii::t("directMessage", "Evidence:"); ?></span> <span class='flex1 color6'> {{delData.title}}</span></div>
                    <div class='moreClass font14' v-if='delClassList.length>0'>
                        <div  class='color3 font14'><?php echo Yii::t("directMessage", "This evidence is added in batches, delete the evidence of other classes in the same batch?"); ?></div>
                        <div class='color6 mt8'><?php echo Yii::t("global", "Please Select"); ?></div>
                        <div>
                            <div class="checkbox" v-for='(list,index) in delClassList'>
                                <label>
                                    <input type="checkbox" :value="list.evidence_id" v-model='del_evidence_id'>{{list.class_name}}
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" @click='delTable("del",delIsEnd)'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="scoreModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 v-if='isEndScore || delIsEnd'><?php echo Yii::t('teaching', 'Progress Report');?></h4>
                    <div v-else>
                        <h4 class="modal-title" id="parentReplyLabel" v-if='active==1 || editType=="info"'><?php echo Yii::t('teaching', 'Evidence');?></h4>
                        <h4 class="modal-title" id="parentReplyLabel" v-if='active==2  || editType=="score"'><?php echo Yii::t("report", "Evidence Score"); ?></h4>
                    </div>
                </div>
                <div class="modal-body p24" >
                    <div class='font14 borderBto pb16'>
                        <!-- <div class='fontBold'>基本信息</div> -->
                        <div class=''>
                            <span>{{showYear(currentyear)}} {{showClass(classid)}}</span>
                            <el-divider direction="vertical" class='color9'></el-divider>
                            <span>{{programConfig[program]}}</span>
                        </div>
                        <div class='mt8 fontBold'>{{showList.title}}</div>
                        <div class='mt8 flex '>
                            <span class='el-icon-arrow-right ml5 color9 mt4'></span>
                            <span class="tabLabel colorc ml5 mt2">{{showItem.standard_code}} </span>
                            <span class='flex1 ml5'> {{showItem.title}}</span>
                        </div>
                    </div>
                    <div class='mt24'>
                        <el-steps :active="active" align-center  style='width:500px;margin:0 auto' v-if='showType=="new"'>
                            <el-step title="<?php echo Yii::t('teaching', 'Evidence Information');?>"></el-step>
                            <el-step title="<?php echo Yii::t('teaching', 'Evidence Score');?>"></el-step>
                        </el-steps>
                        <div class='mt24' v-if='active==1 || editType=="info"'  style='width:550px;margin:0 auto'>
                            <div class="form-horizontal">
                                <div class="form-group">
                                <span class="col-sm-3 font14 color6 control-label"><?php echo Yii::t("report", "Class"); ?></span>
                                    <div class="col-sm-9 mt5" v-if='filterClass.length>0'>
                                        <el-checkbox :indeterminate="isIndeterminate" v-model="classCheckAll" @change="handleClassAll" :disabled='editInfoDis'><?php echo Yii::t('teaching', 'Add this evidence to your other classes.');?></el-checkbox>
                                        <el-checkbox-group v-model="stepOne.class_ids" @change="handleClass">
                                            <el-checkbox v-for="(list,index) in filterClass" :label="list.classid" :key="list.classid" :disabled='disabledData(list)'>{{list.title}}</el-checkbox>
                                        </el-checkbox-group>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <span class="col-sm-3 font14 color6 control-label"><?php echo Yii::t('teaching', 'Term');?></span>
                                    <div class="col-sm-9">
                                        <div v-if='modalSemester==30'>
                                            <div v-if='isEndScore' class='mt8 font14'>
                                                {{stepOne.semester==10?'<?php echo Yii::t('report', 'Term 1');?>':'<?php echo Yii::t('report', 'Term 2');?>'}}
                                            </div>
                                            <div v-else>
                                                <label class="radio-inline">
                                                    <input type="radio"  value="10" v-model='stepOne.semester' :disabled='isEndScore'> <?php echo Yii::t('report', 'Term 1');?>
                                                </label>
                                                <label class="radio-inline">
                                                    <input type="radio" value="20" v-model='stepOne.semester'  :disabled='isEndScore'> <?php echo Yii::t('report', 'Term 2');?>
                                                </label>
                                            </div>

                                        </div>
                                        <div v-else-if='initData.semester' class='mt10'>{{initData.semester.list[modalSemester]}}</div>
                                    </div>
                                </div>
                                <div v-if='!isEndScore'>
                                    <div class="form-group">
                                        <span class="col-sm-3 font14 color6 control-label"><?php echo Yii::t("teaching", "Evidence"); ?></span>
                                        <div class="col-sm-9">
                                        <input type="text" class="form-control" v-model='stepOne.title' placeholder="<?php echo Yii::t("teaching", "Input"); ?>">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <span class="col-sm-3 font14 color6 control-label"><?php echo Yii::t('labels', 'Description');?></span>
                                        <div class="col-sm-9">
                                            <textarea class="form-control" rows="3" v-model='stepOne.desc' placeholder="<?php echo Yii::t("teaching", "Input"); ?>"></textarea>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <span  class="col-sm-3 font14 color6 control-label"><?php echo Yii::t("teaching", "Full Score"); ?></span>
                                        <div class="col-sm-9">
                                        <input type="text" class="form-control" v-model='stepOne.full' placeholder="<?php echo Yii::t("teaching", "Input"); ?>">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <span  class="col-sm-3 font14 color6 control-label"><?php echo Yii::t("labels", "Date"); ?></span>
                                        <div class="col-sm-9">
                                            <el-date-picker
                                                v-model="stepOne.exam_time"
                                                type="date"
                                                placeholder="<?php echo Yii::t("teaching", "Date of Evidence"); ?>"
                                                format="yyyy-MM-dd"
                                                value-format="yyyy-MM-dd">
                                            </el-date-picker>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group"  v-if='showType=="new"'>
                                    <div class="col-sm-offset-2 col-sm-10">
                                    <button type="submit" class="btn btn-primary pull-right" :disabled='nextBtn' @click='saveNext()'><?php echo Yii::t('teaching', 'Next Step');?></button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class='mt24 font14' v-if='active==2  || editType=="score"'>
                           <div class='mt16 mb20 flex'>
                                <div class='flex1'>
                                    <span class='color6'><?php echo Yii::t("teaching", "Full Score"); ?>：</span><span class='color3'>{{stepOne.full}}</span>
                                </div>
                                <div>
                                    <el-popover
                                        placement="bottom-end"
                                        width="200"
                                        trigger="hover"
                                       >
                                        <div @click='sortChild("id",childScore)' class='sortList'><?php echo Yii::t("teaching", "Sort by ID"); ?></div>
                                        <div @click='sortChild("child_cn_name",childScore)' class='sortList'><?php echo Yii::t("teaching", "Sort by Cn name"); ?></div>
                                        <div @click='sortChild("child_en_name",childScore)' class='sortList'><?php echo Yii::t("teaching", "Sort by En name"); ?></div>
                                        <span slot="reference" class='cur-p'><?php echo Yii::t("teaching", "Sort Students"); ?><span class='el-icon-arrow-down'></span></span>
                                    </el-popover>
                                </div>
                            </div>
                            <div :style="'max-height:'+(height-420)+'px;overflow: auto;'" class='tablescroll'>
                                <table id='table' class="table table-bordered ">
                                    <tbody>
                                        <tr>
                                            <th v-for='(list,key,index) in childScore'>
                                                <div class='flex tablePadding thClass'>
                                                    <span class='flex1 color3'>{{showClass(key)}}</span>
                                                    <el-popover
                                                        placement="bottom-end"
                                                        trigger="click"
                                                        :ref="`popover-${index}`"
                                                        v-model="tipVisibles[index]"
                                                        width="250">
                                                        <p class='color3 text-center'><?php echo Yii::t("teaching", "Enter the same score for the entire class"); ?></p>
                                                        <div  @click='inputFocus()'><input type="text" class="form-control mt8 scoreInput" v-model='allScoreEnd'   placeholder="<?php echo Yii::t("ptc", "Score"); ?>"></div>
                                                        <div style="text-align: right; margin-top:8px">
                                                            <button type="button" class="btn btn-default" @click="popoverHide(index)"><?php echo Yii::t("global", "Cancel"); ?></button>
                                                            <button type="button" class="btn btn-primary"  @click="getScoreAll(key,index)"><?php echo Yii::t("global", "OK"); ?></button>
                                                        </div>
                                                        <span slot="reference"><span class='cur-p text-primary'><span class='el-icon-edit'> <?php echo Yii::t("teaching", "Set Batch Scores"); ?></span></span></span>
                                                    </el-popover>
                                                </div>
                                                <table class='table tableMb'>
                                                    <tbody>
                                                        <tr v-for='(item,i) in list'>
                                                            <td>
                                                                <div class='flex tablePadding'>
                                                                    <div class='flex align-items flex1'>
                                                                        <img :src="childInfo.child_info[item.child_id].avatar" alt="" class='avatar'>
                                                                        <div class='flex1'>
                                                                            <div class='font14 color3 mt5  ml8' style='line-height:1'>{{childInfo.child_info[item.child_id].name}}</div>
                                                                            <div class='font12 color6 ml8 mt5'>{{item.child_id}}</div>
                                                                        </div>
                                                                    </div>
                                                                    <div>
                                                                        <input type="text" class="form-control length_2 pull-right"  v-model='item.score' placeholder="<?php echo Yii::t("ptc", "Score"); ?>">
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </th>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer" v-if='active!=1'>
                    <div v-if='active==2'>
                        <button type="button" class="btn btn-default"  @click='prev()'><?php echo Yii::t("teaching", "Previous Step"); ?></button>
                        <button type="button" class="btn btn-primary" :disabled='nextBtn' @click='saveScore()'><?php echo Yii::t("reg", "Complete"); ?></button>
                    </div>
                    <div v-if='showType=="edit"'>
                        <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                        <button type="button" class="btn btn-primary" v-if='editType=="info"'  :disabled='nextBtn'  @click='saveNext()'><?php echo Yii::t("global", "Save"); ?></button>
                        <button type="button" class="btn btn-primary"  :disabled='nextBtn'  v-if='editType=="score"' @click='saveScore()'><?php echo Yii::t("global", "Save"); ?></button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="teacherTabModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" >
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content" @click='showSelect=false' >
                <div class="modal-header"  >
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("teaching", "Teacher"); ?></h4>
                </div>
                <div class="modal-body p24" style='min-height:200px'>
                    <div class='font14 warningText mb16'><span class='glyphicon glyphicon-info-sign mr8'></span><?php echo Yii::t("teaching", "Teachers with classes are displayed"); ?></div>
                    <div v-if='notassignedList.length==0 && assignedList.length==0'>
                    <el-empty description="<?php echo Yii::t("teaching", "No teachers"); ?>"></el-empty>
                    </div>
                    <div v-else>
                        <div class="relative text-center">
                            <div class="length_6 el-input el-input--prefix" >
                                <input type="text" class="form-control pl30  "  placeholder="<?php echo Yii::t("attends", "Input name to filter"); ?>" v-model="searchTeacher" @click.stop="showFocus" autocomplete="off">
                                <span class="el-input__prefix"><i class="el-input__icon el-icon-search"></i></span>
                            </div>
                            <div class='selectTeacher' v-if='showSelect'>
                                <div v-if='teacherSearchData.length==0'>
                                <p class='font14 color3 text-center mt20 color9'><?php echo Yii::t('ptc', 'No Data'); ?></p>
                                </div>
                                <div v-else>
                                    <div  v-for='(list,index) in teacherSearchData'>
                                        <div class="selectName font14 color3 " >
                                            <div class="color3 cur-p" v-if='allTeacherList.assigned[list.uid]' @click.stop="getInit(list.uid)">{{ list.name }}</div>
                                            <div class="color9 flex cupDis" v-if='allTeacherList.not_assigned[list.uid]'><span class='flex1'> {{ list.name }}</span> <span class='ml24 font12'><?php echo Yii::t("teaching", "Subject not assigned"); ?></span> </div>
                                    </div>
                                </div>
                            </div>
                            </div>
                        </div>
                        <div class='mt16' v-if='assignedList.length>0'>
                            <div class='font14 color3'><?php echo Yii::t("teaching", "Subject assigned"); ?></div>
                            <div >
                                <a v-for='(list,index) in assignedList' class='mt16 btn btn-default mr16'  href='javascript:;' @click.stop='getInit(list.uid)'>
                                    {{list.name}}
                                </a>
                            </div>
                        </div>
                        <div class='mt24 mb24' v-if='notassignedList.length>0'>
                            <div class='font14 color3 flex'>
                                    <span class='flex1'><?php echo Yii::t("teaching", "Subject not assigned"); ?></span>
                                    <a  class='btn-link' target="_blank" :href="initData.go_assign_url" ><?php echo Yii::t("teaching", "Go assign subject"); ?></a>
                                </div>
                                <div >
                                    <span v-for='(list,index) in notassignedList' class='cupDis mt16 mr16 inline-block'>
                                        <button type="button"  class="btn btn-default " disabled="disabled">{{list.name}}</button>
                                    </span>
                            </div>
                        </div>
                    </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
var height=document.documentElement.clientHeight;
var navHeight=$('.navbar-fixed-bottom ').height()
var container = new Vue({
        el: "#container",
       data:{
        height:height,
        navHeight:navHeight,
        showDate:false,
        date:'',
        checkedAll:false,
        treeData: [],
        defaultProps: {
          children: 'children',
          label: 'label'
        },
        columns:[],
        header_list:[],
        tableDataList:[],
        tableDataListCopy:[],
        tableChild:{},
        active:1,
          showType:'',
          editType:'',
          tableColumns:[],
          newTable:[],
          showTableNum:[],

          initData:{},
          programConfig:{},
          class_list:[],
          semester:[
              {"label":"<?php echo Yii::t("teaching", "All Terms"); ?>", "value":30},
              {"label":"<?php echo Yii::t('report', 'Term 1');?>", "value":10},
              {"label":"<?php echo Yii::t('report', 'Term 2');?>", "value":20}
          ],
          semesterData:{
            "30":"<?php echo Yii::t("teaching", "All Terms"); ?>",
            "10":"<?php echo Yii::t('report', 'Term 1');?>",
            "20":"<?php echo Yii::t('report', 'Term 2');?>"}
          ,
          school_year:{},
          currentyear:'',
          currentSemester:'',
          showSelect:false,
          teacherList:[],
          searchTeacher:'',
          classid:'',
          program:'',
          subjectList:[],
          allTreeList:{},
          stepOne:{
            title:'',
            desc:'',
            full:'',
            category_id:'',
            item_id:'',
            type:'',
            exam_time:'',
            class_ids:[],
          },
          childList:[],
          copyChildList:[],
          showList:{},
          showItem:{},
          delData:{},
          categoryList:false,
          itemKey:'',
          showTableScore:true,
          filterTerm:30,
          modalSemester:'',
          currentTeacher:'',
          assignedList:[],
          notassignedList:[],
          allTeacherList:{},
          copyTableColumns:[],
          showChildType:'',
          allScoreEnd:'',
          tabClickIndex: null, // 双击的单元格
          tabClickLabel: '', // 当前双击的列名
          classCheckAll: false,
          isIndeterminate: false,
          visibles:false,
          evidence_ids:[],
          filterClass:[],
          childScore:{},
          childInfo:{},
          scorevisibles:false,
          tipVisibles:[],
          copyChildScore:{},
          nextBtn:false,
          disabledClass:[],
          isEndScore:false,
          delClassList:[],
          del_evidence_id:[],
          delIsEnd:false,
          editInfoDis:false,
          searchInput:'',
          isOpen: true,
          leftTreeData:[],
          getRowIndex: null,
                // 列的索引值
            getCellIndex: null
        },
        watch: {
            tableColumns(val) {
                this.doLayout();
            },
        },
        created: function() {
            this.getInit('')
            this.height=this.height-this.navHeight
            let that = this
            window.onresize = function () {
                that.doLayout();
            };
        },
        computed: {
            teacherSearchData: function() {
                var search = this.searchTeacher;
                var searchVal = ''; //搜索后的数据
                if(search) {
                    searchVal =Object.values(this.teacherList).filter(function(product) {
                        return Object.keys(product).some(function(key) {
                            return String(product['name'].toLowerCase()).indexOf(search.toLowerCase()) !== -1;
                        })
                    })
                    return searchVal;
                }
                return this.teacherList;
            },
            SearchTreeData: function() {
                var searchInput = this.searchInput;
                var searchTree =[]; //搜索后的数据
                let copyTreeData=JSON.parse( JSON.stringify ( this.leftTreeData ) )
                if(searchInput) {
                    copyTreeData.forEach((item,i)=>{
                        searchTree[i]=item
                        searchTree[i].children=item.children.filter(f=>((f.title+f.standard_code).toLowerCase().indexOf(searchInput.toLowerCase())!==-1))
                    })
                    return searchTree;
                }
                return this.leftTreeData;
            }
        },
        methods: {
            translate(text, ...args) {
                return text.replace(/%s/g, () => args.shift());
            },
            toggleSidebar() {
                this.isOpen = !this.isOpen;
            },
            sortChild(type,data) {
                this.showChildType=type
                if(data){
                    if(type=='id'){
                        this.childScore=JSON.parse( JSON.stringify (this.copyChildScore ) )
                    }else{
                        for(let key in data){
                            this.sortTea(data[key],type)
                        }
                    }
                }else{
                    if(type=='id'){
                        this.childList=JSON.parse( JSON.stringify (this.copyChildList ) )
                    }else{
                        this.childList=this.sortTea(this.childList,type)
                    }
                }
            },
            sortTea(list,type){
                list.sort((x,y)=>{
                    return x[type].localeCompare(y[type])
                })
                return list
            },
            doLayout(){
                let that = this
                this.$nextTick(() => {
                  if(that.tableColumns.length!=0){
                      that.$refs.table.doLayout()
                      that.itemKey = Math.random();
                  }
                })
            },
            showYear(id){
                if(this.school_year.list){
                    return this.school_year.list.find(item2 =>item2.startyear === id).title
                }
            },
            showClass(id){
                if(this.class_list.length!=0 && this.classid!='' && this.class_list.find(item2 =>item2.classid === parseInt(id))){
                    return this.class_list.find(item2 =>item2.classid === parseInt(id)).title
                }
            },
            getInit(id){
                let that=this
                this.classid=''
                this.program=''
                this.treeData=[]
                this.tableColumns=[]
                this.columns=[]
                this.currentTeacher=id
                this.categoryList=false
                this.childScore={}
                $.ajax({
                    url: '<?php echo $this->createUrl("indexData") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        teacher_id: id,
	                    startyear:  that.currentyear,
                        semester:that.currentSemester
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            if(id==''){
                                that.currentTeacher=data.data.teacher_info.uid
                            }
                           that.initData=data.data
                           that.class_list=data.data.class_list
                           that.programConfig=data.data.programConfig
                           that.school_year=data.data.school_year.list
                           that.currentyear=data.data.school_year.current+''
                           that.date=data.data.lock_from_time
                           if(that.date==''){
                               that.showDate=true
                           }
                           that.filterTerm=data.data.semester.current+''
                           that.subjectList=[]
                           data.data.subject.forEach(item => {
                            that.subjectList.push({
                                label:data.data.programConfig[item],
                                value:item
                            })
                           });
                           if(that.class_list.length!=0 && data.data.subject.length!=0){
                               that.classid=that.class_list[0].classid
                               that.program=data.data.subject[0]
                               that.getTree()
                            }
                           $('#teacherTabModal').modal('hide')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            saveDate(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("saveLockTime") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        startyear:that.currentyear,
                        lock_from_time: this.date
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.showDate=false
                            resultTip({
                                msg: data.message
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            showFocus(){
                this.showSelect=!this.showSelect
            },

            otherTeacher(){
                let that=this
                this.searchTeacher=''
                this.showSelect=false
                $.ajax({
                    url: '<?php echo $this->createUrl("teacherList") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        startyear:that.currentyear,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           that.allTeacherList=data.data
                           that.teacherList=that.sortTea(Object.values(data.data.all_teacher),'name')
                            that.assignedList=that.sortTea(Object.values(data.data.assigned),'name')
                            that.notassignedList=that.sortTea(Object.values(data.data.not_assigned),'name')
                            $('#teacherTabModal').modal('show')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            getChild(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("studentList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        class_id:this.classid,
                        startyear:that.currentyear,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.childList=data.data
                            that.childList.forEach(item => {
                                Vue.set(item, 'score','');
                            });
                            that.copyChildList=JSON.parse( JSON.stringify ( that.childList ) )
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            getTree(){
                let that=this
                that.categoryList=false
                $.ajax({
                    url: '<?php echo $this->createUrl("getCategoryList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        class_id:this.classid,
	                    startyear:  that.currentyear,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                          that.allTreeList=data.data
                          that.categoryList=true
                          that.showTree()
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            showTree(){
               if(this.class_list.length!=0 && this.classid!=''){
                    if(this.allTreeList.list && this.allTreeList.list[this.program]){
                        this.treeData=this.allTreeList.list[this.program]
                        this.leftTreeData=this.allTreeList.list[this.program]
                        this.getScore()
                    }else{
                        this.leftTreeData=[]
                        this.treeData=[]
                        this.tableColumns=[]
                    }
                }
            },
            addInfo(item,type){
                this.showList={
                    title:item.category_title
                }
                this.modalSemester=item.semester
                this.showItem=item
                this.editType=''
                this.active=1
                this.showType='new'
                this.evidence_ids=''
                this.classCheckAll=false
                this.isIndeterminate = true
                this.filterClassList(this.classid)
                let that=this
                this.stepOne={
                    category_id:item.category_id,
                    item_id:item.id,
                    title:'',
                    desc:'',
                    full:'',
                    exam_time:'',
                    class_ids:[],
                    startyear:this.currentyear,
                    semester:'',
                    class_id:this.classid,
                    type:''
                }
                this.stepOne.class_ids=[this.classid]
                if(type){
                    this.isEndScore=true
                    this.stepOne.type=2
                    if(type==1){
                        this.stepOne.semester=10
                    }
                    if(type==2){
                        this.stepOne.semester=20
                    }
                    $.ajax({
                        url: '<?php echo $this->createUrl("getSyncClass") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            class_id:this.classid,
                            startyear:  that.currentyear,
                            semester:this.stepOne.semester,
                            teacher_id:this.currentTeacher,
                            type: "2",
                            item_id: item.id
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                let classId=[]
                                for(let i=0;i<data.data.list.length;i++){
                                    classId.push(data.data.list[i].classid)
                                }
                                if(classId.length==that.filterClass.length){
                                    that.classCheckAll=true
                                    that.isIndeterminate = true
                                }
                                that.editInfoDis=true
                                that.disabledClass=classId.map(Number)
                                $('#scoreModal').modal('show')
                            } else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                        },
                        error: function(data) {

                        },
                    })
                }else{
                    this.stepOne.type=1
                    this.disabledClass=[]
                    this.isEndScore=false
                    this.editInfoDis=false
                    $('#scoreModal').modal('show')
                }


            },
            filterClassList(classid){
                let classType=this.class_list.find(item2 =>item2.classid === parseInt(classid)).classtype
                this.filterClass=this.class_list.filter(function(item) {
                    return item.classtype === classType;
                });
            },
            handleClassAll(val) {
                let ids=[]
                this.filterClass.forEach(item => {
                    ids.push(item.classid)
                });
                this.stepOne.class_ids = val ? ids : [];
                this.isIndeterminate = false;
            },
            handleClass(value) {
                let checkedCount = value.length;
                this.classCheckAll = checkedCount === this.filterClass.length;
                this.isIndeterminate = checkedCount > 0 && checkedCount < this.filterClass.length;
            },
            saveNext(){
                let that=this
                this.stepOne.startyear=that.currentyear
                this.stepOne.class_id= this.classid
                if(this.modalSemester==20 || this.modalSemester==10){
                    this.stepOne.semester=this.modalSemester
                }
                if(!this.isEndScore){
                    if(this.stepOne.class_ids.length==0){
                        resultTip({
                            error: 'warning',
                            msg: '<?php echo Yii::t('global', 'Please Select');?> <?php echo Yii::t("report", "Class"); ?>'
                        });
                        return
                    }
                    if(this.stepOne.semester==''){
                        resultTip({
                            error: 'warning',
                            msg: '<?php echo Yii::t('global', 'Please Select');?> <?php echo Yii::t('teaching', 'Term');?>'
                        });
                        return
                    }
                    if(this.stepOne.title.trim()==''){
                        resultTip({
                            error: 'warning',
                            msg: '<?php echo Yii::t('teaching', 'Input');?> <?php echo Yii::t("teaching", "Evidence"); ?>'
                        });
                        return
                    }
                    if(this.stepOne.desc.trim()==''){
                        resultTip({
                            error: 'warning',
                            msg: '<?php echo Yii::t('teaching', 'Input');?> <?php echo Yii::t('labels', 'Description');?>'
                        });
                        return
                    }
                    if(this.stepOne.full.trim()==''){
                        resultTip({
                            error: 'warning',
                            msg: '<?php echo Yii::t('teaching', 'Input');?> <?php echo Yii::t('teaching', 'Full Score');?>'
                        });
                        return
                    }
                }
                this.stepOne.evidence_ids=this.evidence_ids
                that.nextBtn=true
                $.ajax({
                    url: '<?php echo $this->createUrl("saveEvidence") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{data:this.stepOne,teacher_id:this.currentTeacher},
                    success: function(data) {
                        if (data.state == 'success') {
                            that.evidence_ids=data.data.evidence_ids
                            if(that.showType=="new"){
                                that.stepOne.id=data.data.evidence_ids
                                that.editGetScore(data.data.evidence_ids,'new')
                            }else{
                                resultTip({
                                    msg: data.message
                                });
                                that.getScore()
                                that.getTree()
                                $('#scoreModal').modal('hide')
                                that.nextBtn=false
                            }
                        } else {
                            that.nextBtn=false
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        that.nextBtn=false
                    },
                })

            },
            prev(){
                this.active=1
            },
            saveScore(type){
                let that=this
                let list={}
                for(let key in this.childScore){
                    list[key]=[]
                    for(var i=0;i<this.childScore[key].length;i++){
                        list[key].push({
                            class_id:this.childScore[key][i].class_id,
                            class_name:this.childScore[key][i].class_name,
                            child_id:this.childScore[key][i].child_id,
                            score:this.childScore[key][i].score,
                            evidence_id:this.childScore[key][i].evidence_id,
                        })
                    }
                }
                that.nextBtn=true
                $.ajax({
                    url: '<?php echo $this->createUrl("saveScore2") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
	                    data:list,
                        type:this.isEndScore?2:1,
                        teacher_id:this.currentTeacher
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           $('#scoreModal').modal('hide')
                            that.getScore()
                            that.getTree()
                            resultTip({
                                msg: data.message
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.nextBtn=false
                    },
                    error: function(data) {
                        that.nextBtn=false
                    },
                })
            },
            tabClick(row, column, cell, event) {
                if(column.property=='itemScore' && row.index!=0 && row.index!=1){
                    this.tabClickIndex = row.index
                    this.tabClickLabel = column.id
                    this.$nextTick(() => {
                        let promptInput=column.id+'_'+row.index
                        $('.'+promptInput).find('input')[0].focus();
                    })
                }
            },
            tableRowClassName({row, rowIndex}) {
                row.index = rowIndex
                if(this.tableChild[row.child_id]){
                    if(this.tableChild[row.child_id].status==999 || (this.tableChild[row.child_id].status==20 && this.tableChild[row.child_id].classid!=this.classid)){
                        return 'warning-row';
                    }
                }
                return '';
            },
            rowClick(row) {
                this.tabClickIndex = null
                this.tabClickLabel = null
                if(row.index==0 || row.index==1){
                    this.getRowIndex=null
                }else{
                    if(this.getCellIndex == null && this.getRowIndex ==null){
                        this.getCellIndex=null
                        this.getRowIndex =null;
                    }else{
                        this.getRowIndex = row.index;
                    }
                }
            },
            selectedRowStyle({ row, rowIndex }) {
                if (this.getRowIndex === rowIndex) {
                    return {
                        "background-color": "#F4F8FC  !important"
                    };
                }
            },
            tableCellClassName({ column, columnIndex }) {
                column.columnIndex = columnIndex;
            },
            cellClick(row, column) {
                this.tabClickIndex = null
                this.tabClickLabel = null
                if(column.columnIndex==0){
                    this.getCellIndex=null
                    this.getRowIndex = row.index;
                }else{
                    if(this.getCellIndex === column.columnIndex && this.getRowIndex === row.index){
                        this.getCellIndex=null
                        this.getRowIndex=null
                    }else{
                        this.getRowIndex = row.index;
                        this.getCellIndex = column.columnIndex;
                    }
                }
                if(row.index==0 || row.index==1){
                    this.getRowIndex=null
                }
            },
            selectedCellStyle({ column, columnIndex, row, rowIndex }) {
                if (this.getCellIndex === columnIndex) {
                    if(this.getRowIndex == rowIndex) {
                        return {
                            "background-color": "#  !important",
                        };
                    } else {
                        return {
                            "background-color": "#F4F8FC !important"
                        };
                    }
                }
            },
            inputBlur(id,child_id,score,row) {
                this.tabClickIndex = null
                this.tabClickLabel = ''
                this.stepOne.id=id
                let list={}
                list[child_id]=score
                if(this.tableDataListCopy[row.index][id]==='' && score===''){
                    return
                }
                if(parseInt(this.tableDataListCopy[row.index][id])===parseInt(score)){
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("saveScore") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        evidence_id:that.stepOne.id,
	                    data:list,
                        teacher_id:this.currentTeacher
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.getScore()
                            resultTip({
                                msg: data.message
                            });
                        } else {
                            that.tableDataList[row.index][id]=that.tableDataListCopy[row.index][id]
                            head.dialog.alert(data.message);
                        }
                    },
                    error: function(data) {
                        resultTip({
                            msg: data.message
                        });
                    },
                })
            },
            disabledData(list){
                if(this.disabledClass.indexOf(list.classid)!=-1){
                    return this.isEndScore?false:true
                }else{
                    return this.isEndScore?true:false
                }
            },
            editInfo(item,list,row,info,end){
                let that=this
                this.showType='edit'
                this.active=0
                this.editType=info
                this.showList=item
                this.showItem=list
                this.editInfoDis=true
                that.stepOne.id=row.id
                if(end){
                    this.delIsEnd=true
                }else{
                    this.delIsEnd=false
                }
                if(info=="info"){
                    $.ajax({
                        url: '<?php echo $this->createUrl("getEvidence") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            evidence_id:row.id,
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.modalSemester=data.data.apply_semester
                                that.filterClassList(Object.keys(data.data.class_evidence)[0])
                                if(Object.keys(data.data.class_evidence).length==that.filterClass.length){
                                    that.classCheckAll=true
                                    that.isIndeterminate = true
                                }
                                that.disabledClass=Object.keys(data.data.class_evidence).map(Number)
                                that.evidence_ids=data.data.evidence_ids
                                that.stepOne={
                                    category_id:data.data.category_id,
                                    item_id:data.data.item_id,
                                    title:data.data.title,
                                    desc:data.data.desc,
                                    full:data.data.full,
                                    id:row.id,
                                    startyear:that.currentyear,
                                    semester:data.data.semester,
                                    class_ids:Object.keys(data.data.class_evidence).map(Number),
                                    exam_time:data.data.exam_time,
                                    evidence_ids:data.data.evidence_ids
                                }
                                $('#scoreModal').modal('show')
                            } else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                        },
                        error: function(data) {

                        },
                    })
                }else{
                   this.editGetScore([that.stepOne.id],'edit')
                }
            },
            editGetScore(id,type){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getScore") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        evidence_id:id,
                        teacher_id:this.currentTeacher
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                        that.stepOne.full=data.data.evidence.full
                        that.childScore=data.data.list
                        that.childInfo=data.data
                        that.copyChildScore=JSON.parse( JSON.stringify (data.data.list ) )
                        if(type=='new'){
                            that.active=2
                            that.nextBtn=false
                        }
                        if(type=='edit'){
                            $('#scoreModal').modal('show')
                        }
                        } else {
                            that.nextBtn=false
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        that.nextBtn=false
                    },
                })
            },
            filterSemester(semester){
                this.filterTerm=semester
                this.showTable(this.treeCheck)
            },
            filterBySemester(data, semester) {
                let filteredData = [];
                data.forEach(item => {
                    if (item.semester == semester) {
                        filteredData.push(item);
                    }
                    if (item.children) {
                        const childData = this.filterBySemester(item.children, semester);
                        if (childData.length > 0) {
                            item.children = childData;
                            filteredData.push(item);
                        }
                    }
                });
                return filteredData;
            },
            getScore(){
                let that=this
                if(this.classid=="" || that.program==''){
                    return
                }
                if(this.classid!=''){
                    this.getChild()
                }
                that.showTableScore=false
                $.ajax({
                    url: '<?php echo $this->createUrl("getAllScore") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        class_id:this.classid,
	                    startyear:  that.currentyear,
                        semester:30,
                        program:that.program
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.tableColumns=[]
                            that.getCellIndex=null
                            that.getRowIndex =null;
                            that.tableChild=data.data.child_data
                            that.tableDataList=that.sortData(data.data.child_score_list)
                            that.tableDataListCopy=JSON.parse( JSON.stringify ( data.data.child_score_list ) )
                            that.columns=data.data.header_list
                            that.itemKey = Math.random();
                            that.$nextTick(() => {
                                if(data.data.header_list.length!=0){
                                    that.$refs.table.doLayout()
                                }
                            })
                            that.tableColumns=JSON.parse( JSON.stringify ( that.columns ) )

                            that.setCheckedKey()
                            that.filterSemester(that.filterTerm)
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.showTableScore=true
                    },
                    error: function(data) {
                        that.showTableScore=true
                    },
                })
            },
            sortData(list){
                <?php if (Yii::app()->language == 'zh_cn') { ?>
                    list.sort((a, b) => {
                        const nameA = this.tableChild[a.child_id]?.name || '';
                        const nameB = this.tableChild[b.child_id]?.name || '';
                        return nameA.localeCompare(nameB, 'zh');
                    });
                  
                <?php } else { ?>
                    list.sort((a, b) => {
                        const nameA = this.tableChild[a.child_id]?.name || '';
                        const nameB = this.tableChild[b.child_id]?.name || '';
                        return nameA.charCodeAt(0) - nameB.charCodeAt(0);
                    });
                <?php } ?>
                return list
            },
            headerStyleEvent({ row, column, rowIndex, columnIndex }) {
                if (columnIndex != 0) {
                    if (rowIndex === 0) {
                        if((columnIndex%2)===0){//判定条件余数为0时为偶数
                            return { background: '#EEF9FF'}
                        }else{
                            return { background: '#FEFCEE'}
                        }
                    }else{
                        return {background:'#F7F7F8'}
                    }
                } else{
                    return {background:'#F7F7F8'}
                }
            },
            setCheckedKey() {
                var ids=[]
                 var treeCheck=[]
                for(var i=0;i<this.tableColumns.length;i++){
                    treeCheck.push(this.tableColumns[i].id)
                    for(var j=0;j<this.tableColumns[i].children.length;j++){
                        ids.push(this.tableColumns[i].children[j].id)
                    }
                }
                this.treeCheck=treeCheck.concat(ids)
                if(ids.length>0){
                    this.$nextTick(() => {
                        this.$refs.tree.setCheckedKeys(ids);
                    })
                }
            },
            treeCheckList(){
                let that=this
                var columnsId=[]
                var childrenId=[]
                var semesterList={}
                that.columns.forEach(item => {
                    columnsId.push(item.id)
                    item.children.forEach(list => {
                        childrenId.push(list.id)
                        semesterList[list.id]=[]
                        list.children.forEach(child => {
                            semesterList[list.id].push(child.semester)
                        });
                    });
                });
                that.showTableNum=columnsId.concat(childrenId)
                for(var i=0;i<that.treeData.length;i++){
                    if(columnsId.indexOf(that.treeData[i].id)==-1){
                        Vue.set(that.treeData[i], 'disabled',true);
                    }else{
                        Vue.set(that.treeData[i], 'disabled',false);
                    }
                    for(var j=0;j<that.treeData[i].children.length;j++){
                        if(childrenId.indexOf(that.treeData[i].children[j].id)==-1){
                            Vue.set(that.treeData[i].children[j], 'disabled',true);
                        }else if(this.filterTerm!=30){
                            if(semesterList[that.treeData[i].children[j].id].indexOf(this.filterTerm)==-1){
                                Vue.set(that.treeData[i].children[j], 'disabled',true);
                            }else{
                                Vue.set(that.treeData[i].children[j], 'disabled',false);
                            }
                        }else{
                            Vue.set(that.treeData[i].children[j], 'disabled',false);
                        }
                    }
                }
            },

            checkedChange(event){
                var ids=[]
                for(var i=0;i<this.columns.length;i++){
                    for(var j=0;j<this.columns[i].children.length;j++){
                        ids.push(this.columns[i].children[j].id)
                    }
                }
                this.treeCheckList()
                if(event){
                    this.$refs.tree.setCheckedKeys(ids);
                    this.showTable(this.showTableNum)

                }else{
                    this.$refs.tree.setCheckedKeys([]);
                    this.tableColumns=[]
                }
            },
            handleCheck(data, checked) {
                var checkedArray = checked.halfCheckedKeys.concat(checked.checkedKeys);
                this.treeCheck=checkedArray
                this.treeCheckList()
                if(checkedArray.length==0){
                    this.tableColumns=[]
                    this.checkedAll=false
                }else{
                    if(checkedArray.length==this.showTableNum.length){
                        this.checkedAll=true
                    }else{
                        this.checkedAll=false
                    }
                    this.showTable(checkedArray)
                }
            },
            showTable(checkedArray){
                this.treeCheckList()
                let newColumns=JSON.parse( JSON.stringify ( this.columns ) )
                var newData=[]
                for(var i=0;i<newColumns.length;i++){
                    if(checkedArray.indexOf(newColumns[i].id) !=-1){
                        newData[i]={
                            id:newColumns[i].id,
                            title:newColumns[i].title,
                            children:[]
                        }
                    }
                    for(var j=0;j<newColumns[i].children.length;j++){
                        if(checkedArray.indexOf(newColumns[i].children[j].id) !=-1){
                            newData[i].children.push(newColumns[i].children[j])
                        }
                    }
                }
                if(this.filterTerm < 30){
                    newData.forEach((data,index)=>{
                        newData[index]['children'] = this.filterBySemester(data.children, this.filterTerm)
                    })
                }
                var list=[]
                newData.forEach((data,index)=>{
                    if(data.children.length != 0){
                        list.push(data)
                    }
                })
                this.tableColumns=list
                this.copyTableColumns=list
            },
            delTable(row,type){
                if(type=='end'){
                    this.delIsEnd=true
                }else{
                    this.delIsEnd=false
                }
                let that=this
                if(row!='del'){

                    this.delData=row
                    this.del_evidence_id=[]
                    $.ajax({
                        url: '<?php echo $this->createUrl("getBatchClass") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            evidence_id:this.delData.id,
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.delClassList=data.data.filter(function(item) {
                                    return item.class_id != that.classid;
                                });
                                $('#delModal').modal('show')
                            } else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                        },
                        error: function(data) {

                        },
                    })
                    return
                }
                this.del_evidence_id.push(this.delData.id)
                $.ajax({
                    url: '<?php echo $this->createUrl("delEvidence") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        evidence_id:this.del_evidence_id,
                        teacher_id:this.currentTeacher
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                        that.getScore()
                        that.getTree()
                        resultTip({
                                msg: data.message
                            });
                            $('#delModal').modal('hide')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            exportTab(){
                var myDate=new Date;
                var year=myDate.getFullYear(); //获取当前年
                var mon=myDate.getMonth()+1<10?"0"+(myDate.getMonth()+1):myDate.getMonth()+1; //获取当前月
                var date=myDate.getDate()<10?"0"+myDate.getDate():myDate.getDate(); //获取当前日
                let nowDate=mon+date
                let programname=this.programConfig[this.program].split(' ')
                let subTitle=programname.splice(1,programname.length).join('')
                let classname=this.showClass(this.classid).substr(9,this.showClass(this.classid).length)
                let classTitle=classname.split(/[\t\r\f\n\s]*/g).join('')
                if(classTitle.indexOf('KinderClass')!=-1){
                    classTitle='K'+classTitle.substr(11,classTitle.length)
                }
                if(classTitle.indexOf('Grade')!=-1){
                    classTitle=classTitle.substr(5,classTitle.length)
                }
                var title='GradeBook'+'_'+classTitle+'_'+subTitle+'_'+nowDate;
                if(title.length>31){
                    title=title.substr(13,title.length)
                }
                var table = document.getElementById('exportTable');
                if (table.querySelector('.el-table__fixed')) {
                    table.removeChild(table.querySelector('.el-table__fixed'));
                }
                if (table.querySelector('.el-table__fixed-right')) {
                    table.removeChild(table.querySelector('.el-table__fixed-right'));
                }
                $(".itemCss").each(function(){
                    $(this).html('')
                });
                var wb = XLSX.utils.table_to_book(table, {sheet:title});
                this.itemKey = Math.random();
                return XLSX.writeFile(wb, title+'.xlsx');
            },
            popoverHide(index){
               Vue.set(this.tipVisibles, index, false);
            },
            getScoreAll(key,index){
                Vue.set(this.tipVisibles, index, false);
                this.childScore[key].forEach(item => {
                    Vue.set(item, 'score',this.allScoreEnd);
                });
                this.allScoreEnd=''
                this.scorevisibles=false
            },
            inputFocus(){
                $('.scoreInput').focus()
            },
        }
    })
    // $(document).click(function(event) {
    //     container.tabClickIndex = null
    //     container.tabClickLabel = null
    // });
</script>