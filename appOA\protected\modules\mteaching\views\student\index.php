<style>
     [v-cloak] {
        display: none;
    }
    .line20{
        line-height:20px
    }
    .radio-inline, .checkbox-inline{
        position:relative;
        margin-right:20px;
        color:#666;
        line-height:1.5
    }
    .radio, .checkbox{
        color:#666
    }
    .mt32{
        margin-top:32px
    }
    .avatar{
        width: 42px;
        height: 42px;
        border-radius: 50%;
        object-fit: cover;
    }
    .avatar32{
        width:32px;
        height: 32px;
        border-radius: 50%;
        object-fit: cover;
    }
    .avatar24{
        width: 24px;
        height: 24px;
        border-radius: 50%;
        object-fit: cover;
    }
    .tagLabel{
        font-size: 12px;
        color: #D9534F;
        line-height: 20px;
        padding: 1px 5px;
        background: #F2DEDE;
    }
    .tagLabelYellow{
        font-size: 12px;
        color: #F0AD4E;
        line-height: 20px;
        padding: 1px 5px;
        background: #FCF8E3;
    }
    .bluebg{
        color: #4D88D2;
    }
    .colorRed{
        color:#D9534F 
    }
    .p0{
        padding:0px
    }
    .borderTop{
        border-top:1px solid #E8EAED;
    }
    .borderLeftpos {
        display: inline-block;
        border-left: 1px solid #E4E7ED;
        height: 100%;
        width: 1px;
        position: absolute;
        left: 50%;
        top:-12px
    }
    .bgGrey{
        background: #F7F7F8;
        padding:16px;
        border-radius: 4px;
    }
    .presenter{
        border-radius: 4px;
        border:1px solid #E8EAED;
    }
    
    .processing{
        height: 20px;
        background: #5CB85C;
        border-radius: 100px 0px 0px 100px;
        color: #fff;
        font-size: 12px;
        padding: 2px 10px;
        text-align: center;
        display: inline-block;
        margin-right: -15px;
    }
    .listMedia{
        padding:8px;
        border: 1px solid #fff;
        max-height:62px
    }
    .listMedia:hover{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        border: 1px solid #4D88D2;
        cursor: pointer;
    }
    .closeIcon{
        display:none
    }
    .listMedia:hover .closeIcon{
        display:block
    }
    .optionSearch {
        height: auto;
        padding:0 10px
    }
    .text_overflow {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        line-height: 14px;
    }
    .waiting{
        width: 32px;
        height: 32px;
        background: #D9D9D9;
        border-radius: 50%;
        color: #fff;
        display: inline-block;
        line-height: 32px;
        text-align: center;
        font-size: 20px;
    }
    .waitingColor{
        color:#F0AD4E
    }
    .firstBorder{
        border-top: 1px dashed #D9D9D9;
        position: absolute;
        left: 60px;
        top: 50%;
        width:90px;
    }
    .mr50{
        /* margin-right:50px */
    }
    .stepLeft{
        border-left: 1px solid #E8EAED;
        position: absolute;
        left: 12px;
        height: 100%;
        top: 30px;
    }
    .overflow{
        overflow:hidden
    }
    .otherBg{
        padding:16px;
        background: #EBEDF0;
        border-radius: 4px;
    }
    .imgIcon{
        width:16px;
       position:absolute;
       top:2px
    }
    .iconWidth{
        position: relative;
        width:32px;
        height:16px
    }
    .minWidth{
        width:110px
    }
    .imgList{
        width: 72px;
        height: 72px;
        border-radius: 4px;
        object-fit: cover;
        margin-bottom:8px
    }
    .uploadLoading{
        width: 72px;
        height: 72px;
        background: #D9D9D9;
        border-radius: 4px;
        line-height: 72px;
        text-align: center;
    }
    .imgData{
        position: relative;
        display: inline-block;
    }
    .imgData span{    
        position: absolute;
        right: 3px;
        top: 3px;
        background: #333333;
        opacity: 1;
        color: #fff;
        width: 17px;
        height: 17px;
        border-radius: 50%;
        font-weight: 200;
        text-align: center;
        line-height: 15px;
        font-size: 19px;
    }
    .uploadFile {
        font-size:14px;
        align-items: center;
        padding:5px
    }
    .uploadFile .icon{
        display:none
    }
    .uploadFile:hover{
        background: #F7F7F8;

    }
    .uploadFile:hover .icon{
        display:block
    }
    .uploadFile a{
        color:#4D88D2
    }
    .fileLink{
        background: #F7F7F8;
        border-radius: 4px;
        border: 1px solid #EBEDF0;
        padding: 6px 12px;
        font-size: 14px;
        margin-right: 16px;
        margin-bottom:8px;
        line-height:20px
    }
    .fileLink img{
        width:20px;
        height:20px
    }
    .imgLi{
        list-style: none;
        padding-left: 0;
    }
    .imgLi li{
        display:inline-block
    }
    .el-input__inner,.form-control{
        height:34px;
        box-shadow:none
    }
    .m0{
        margin:0px
    }
    .height65{
        height:65px
    }
    .handle{
        background: #FFAC37;
        border-radius: 100px 0 0 100px;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #FFFFFF;
        padding:2px 8px;
        position: absolute;
        right: -24px;
    }
    .scroll-box::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius   : 10px;
        background-color: #ccc;
        background-image: none
    }
    .scroll-box::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0);
        background   : #fff;
        border-radius: 10px;
        border:none
    }
    .NOID{
        position: absolute;
        left: 0;
        top: 0;
        background: #666666;
        border-radius: 2px;
        color: #fff;
        padding: 0px 6px;
    }
    .closeImg{
        cursor: pointer;
    }
    a{
        text-decoration: none !important;
    }
    .sendImg{
        border-radius: 20px;
        border: 1px solid #D9534F;
        background: #FEFAFA;
    }
    .sendImg .sendTitle{
        background: linear-gradient(180deg, #EE8B87 0%, #D9534F 100%);
        border-radius: 20px 20px 50% 50%;
        padding: 12px;
        color: #fff;
    }
    .greenSendImg{
        border-radius: 20px;
        border: 1px solid #5CB85C;
        background: #F8FDF6;
    }
    .greenSendImg .sendTitle{  
        background: linear-gradient(180deg, #95DD95 0%, #5CB85C 100%);
        border-radius: 20px 20px 50% 50%;
        padding: 12px;
        color: #fff;
    }
    .qrcode{
        width:88px;
        height:88px
    }
    .round{
        width: 10px;
        height: 10px;
        background: #FFFFFF;
        border: 2px solid #4D88D2;
        display: inline-block;
        border-radius: 50%;
        margin-left:15px
    }
    .roundSolid{
        border-left: 1px dashed #D9D9D9;
        position: absolute;
        left:19px;
        top:15px;
        height: 100%;
    }
    .dropdownLeft{
        right: 0;
        left: auto;
    }
    .flieEllipsis{
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        width: 0;
    }
    .loading {
        width: 98%;
    }
    .line0{
        line-height:0
    }
    .borderLeft{
        border-left: 1px solid #EBEDF0;
    }
    .inputStyle{
        width:100%;
        border:none;
        border-bottom:1px solid #ccc
    }
    .wechatQrcode{
        display: inline-block;
        padding: 6px 10px;
        color: #50B674;
        background: #FFFFFF;
        border-radius: 4px;
        border: 1px solid #50B674;
        cursor: pointer;
    }
    .wechatQrcode img{
        width:16px;
        float:left;
        border-radius: 50%;
    }
    .qrcodeBox{
        position: absolute;
        left: 0;
        /* top: 15px; */
        width: 290px;
        /* height: 274px; */
        border: 1px solid #ddd;
        background: #fff;
        border-radius: 4px;
        text-align:center;
        font-size:14px;
        color:#333;
        box-shadow: 0px 2px 8px 0px rgb(0 0 0 / 20%);
        z-index:9;
        padding:20px 10px
    }
    .qrcodeBox .qrcodeWechat{
        width:124px;
        height:124px;
        margin:0 auto
    }
    .img24{
        width: 24px;
        height: 24px;
        border-radius: 12px;
        border: 1px solid #FFFFFF;
        object-fit: cover;
    }
    .jointImg{
        margin-left:-5px
    }
    .absoluteFlex{
        position: absolute;
        top: 5px;
    }
    .wechatIcon{
        height:16px;
        position: absolute;
        bottom: 0;
        right: 0;
        width: 16px;
    }
    .avatarWechat{
        width:60px;
        height:60px;
        border-radius:50%
    }
    .iconAbsolute{
        height: 14px;
        position: absolute;
        bottom: 0px;
        right: -5px;
        width: 14px;
        color: #5CB85C;
        background: #fff;
        border-radius: 50%;
        font-size:13px
    }
    .praise1Png{
        width: 24px;
        height: 24px;
        position: absolute;
        left: 0;
        top: 0;
        z-index: 99
    }
    .PositiveBehavior{
        background: rgba(77, 136, 210, 0.14);
        padding: 6px 12px 6px 22px;
        border-radius: 4px;
        line-height: 12px;
        font-weight: bold;
        position: absolute;
        left: 12px;
        top: 0;
    }
</style>
<?php
$category = $_GET['category'];
if(empty($category)){
    $category = 'myList';
}
$category2 = $_GET['category2'];
if(empty($category2) && $category=='adminGroups'){
    $category2 = 'supportList';
}
?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Teaching Tasks'), array('//mteaching/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('referral','Behavior Management');?></li>
    </ol>
    <div class="row">
        <div class="col-md-2">
            <?php
            $this->widget('zii.widgets.CMenu',array(
                'id' => 'user-profile-item',
                'items'=> $this->getMenuItems(),
                'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked text-right background-gray'),
                'activeCssClass'=>'active',
                'itemCssClass'=>''
            ));
            ?>
             <!-- <div class='relative mt20' id='qrcode' v-cloak>            
                <div class='wechatQrcode mb15' @click.stop='showUnWechatQrcode' v-if='wechatData.state==1'>
                    <div>
                        <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/wechatDM.png' ?>" alt="">
                        <span class='ml4'><?php echo Yii::t("directMessage", "Wechat Notification"); ?></span>
                    </div>
                </div>
                <div class="qrcodeBox"  v-if='scanQrcodeBox'>
                    <div class='font14 color3'><strong><?php echo Yii::t("directMessage", "Wechat Notification"); ?></strong></div>
                    <div class='mt8 font12 color6'><?php echo Yii::t("referral", "Get handling progress notifications."); ?></div>
                    <div>
                        <div id='wechatQrcodeBind' alt="" class='qrcodeWechat mt16'></div>
                        <div class='font12 mt16 color6 '><?php echo Yii::t("directMessage", "Wechat scan to bind your account."); ?></div>
                    </div>
                </div>
                <div class='wechatQrcode mb15' @click.stop='qrcodeBox=true'  v-if='wechatData.state==0'>
                    <div>
                        <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/wechatDM.png' ?>" alt="" v-if='wechatData.headimgurl=="" || wechatData.headimgurl==null'>
                        <img :src="wechatData.headimgurl" alt="" v-if='wechatData.headimgurl!=""'>
                        <span class='ml4'><?php echo Yii::t("directMessage", "Wechat Enabled"); ?></span>
                    </div>
                </div>
                <div  class="qrcodeBox"  v-if='qrcodeBox'>
                    <div class='font14 color3'><strong><?php echo Yii::t("directMessage", "Wechat Enabled"); ?></strong></div>
                    <div class='mt8 font12 color6'><?php echo Yii::t("referral", "Get handling progress notifications."); ?></div>
                    <div class='relative inline-block' v-if='wechatData.headimgurl!=""'>
                        <img :src="wechatData.headimgurl" alt="" class='avatarWechat mt16'>
                        <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/wechatDM.png' ?>" alt="" class='wechatIcon'>
                    </div>
                    <div class='font12 mt16 color6 '  v-if='wechatData.nickname!=""'>{{wechatData.nickname}}</div>
                    <div class='font12 mt24 mt16 color6 '><button class="btn btn-default" type="button" @click.stop='unbind()'><?php echo Yii::t("directMessage", "Unbind your account"); ?></button></div>
                </div>
            </div> -->
        </div>
        <div class="col-md-10" id="main-edit-zone">
            <?php
            if(!empty($category2)){
                $category = $category2;
            }
            $this->renderPartial($category,array('config'=>$config,'department'=>$department,'id_num'=>$id_num));
            ?>
           
        </div>
    </div>
</div>

<script>
    $(function(){
        $('#main-edit-zone').find('.edit-title').html($('#user-profile-item li.active a').html());
    })
    $(document).ready(function () {
    // 通过该方法来为每次弹出的模态框设置最新的zIndex值，从而使最新的modal显示在最前面
        $(document).on('shown.bs.modal', '.modal.in', function(event) {
            var zIndex = 1040 + (10 * $('.modal:visible').length);
                $(this).css('z-index', zIndex);
                setTimeout(function() {
                    $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');
                }, 0);
        }).on('hidden.bs.modal', '.modal', function(event) {
            if ($('.modal.in').size() >= 1) {
                $('body').addClass('modal-open')
            }
        })
    });
    $(document).click(function(event) {
        container.qrcodeBox=false;
        container.scanQrcodeBox=false;
        clearInterval(container.timer);
    });
    // var container = new Vue({
    //     el: "#qrcode",
    //     data: {
    //         qrcode:'',
    //         wechatData:{},
    //         scanQrcodeBox:false,
    //         qrcodeBox:false,
    //     },
    //     watch:{
    //     },
    //     created: function() {
    //         this.showWechatQrcode()
    //     },
    //     computed: {},
    //     methods: {
    //         showUnWechatQrcode(){
    //             let that=this
    //             if(this.scanQrcodeBox){
    //                 clearInterval(this.timer);
    //                 that.scanQrcodeBox=!that.scanQrcodeBox
    //                 return
    //             }else{
    //                 this.timer =setInterval(this.showWechatQrcode, 5000);   
    //             }
    //             $.ajax({
    //                 url: '<?php echo $this->createUrl("directMessage/teacherBindQrcode") ?>',
    //                 type: "post",
    //                 dataType: 'json',
    //                 data: {},
    //                 success: function(data) {
    //                     if (data.state == 'success') {
    //                         that.scanQrcodeBox=!that.scanQrcodeBox
    //                         that.$nextTick(() => {
    //                             $('#wechatQrcodeBind').qrcode({width: 124,height: 124,text:data.data});
    //                         })                            
    //                     }
    //                 },
    //                 error: function(data) {
                        
    //                 },
    //             })
    //         },
    //         hideQrcode(){
    //             this.qrcodeBox=false;
    //             this.scanQrcodeBox=false;
    //             clearInterval(this.timer);
    //         },
    //         showWechatQrcode(){
    //             let that=this
    //             $.ajax({
    //                 url: '<?php echo $this->createUrl("directMessage/teacherBindInfo") ?>',
    //                 type: "post",
    //                 dataType: 'json',
    //                 data: {},
    //                 success: function(data) {
    //                     if (data.state == 'success') {
    //                         that.wechatData=data.data
    //                         if(data.data.state==1){
    //                             if(that.scanQrcodeBox){
    //                                 that.scanQrcodeBox=false
    //                                 that.qrcodeBox=true
    //                             }
    //                             clearInterval(that.timer);
    //                         }
    //                     }
    //                 },
    //                 error: function(data) {
                        
    //                 },
    //             })
    //         },
    //         unbind(){
    //             let that=this
    //             $.ajax({
    //                 url: '<?php echo $this->createUrl("directMessage/teacherUnbind") ?>',
    //                 type: "post",
    //                 dataType: 'json',
    //                 data: {},
    //                 success: function(data) {
    //                     if (data.state == 'success') {
    //                         resultTip({
    //                             msg:data.message
    //                         })
    //                         that.qrcodeBox=false
    //                         that.showWechatQrcode()
    //                     }else{
    //                         resultTip({
    //                                 error: 'warning',
    //                                 msg:data.message
    //                             })
    //                     }
    //                 },
    //                 error: function(data) {
                        
    //                 },
    //             })
    //         },
    //     }
    // })
</script>
