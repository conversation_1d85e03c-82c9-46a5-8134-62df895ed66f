<?php
/**
 * 折扣审核
 */
class Discount implements WorkflowTemplate
{
    private $template;
    public $controller;
    public $workflow;
    
    public function __construct() {
        Yii::import('common.models.invoice.DiscountSchool');
        Yii::import('common.models.invoice.DiscountCategory');
        Yii::import('common.models.invoice.ChildDiscountLink');
    }

    public function initi(){
        $this->setTemplate('initi');
        $data['jsFunction'] = 'WorkflowModalShow';
        $data['modalNameId'] = 'workflow-discount-modal';
        $data['workflow'] = $this->workflow;
        //查询学校需要审核的折扣
        $currentTimestamp = time();
        $criteria = new CDbCriteria;
        $criteria->compare('t.schoolid', $this->workflow->schoolId);
        $criteria->compare('t.allow_bind2child', DiscountSchool::DISCOUNT_ALLOW_BIND);
        $criteria->compare('t.stat', DiscountSchool::DISCOUNT_STATUS_VALID);
        $criteria->compare('t.mailto', '>0');
        $criteria->order = "t.discount DESC";
        $criteria->addCondition('t.expire_date>'.$currentTimestamp);
        $discountList = DiscountSchool::model()->with('discountTitle')->findAll($criteria);
        if (!empty($discountList)){
            foreach ($discountList as $discount){
                $selectDisount[$discount->id] = $discount->discount * 100 / 100 . '% ' . $discount->discountTitle->title_cn;
            }
        }
        $data['discount'] = $selectDisount;
        //班级列表
        $commond = Yii::app()->db->createCommand()->from(IvyClass::model()->tableName())->where('schoolid=:schoolid and stat<=:stat',array(':schoolid'=>$this->workflow->schoolId,":stat"=>IvyClass::STATS_OPEN))->order('classid asc')->queryAll();
        if (is_array($commond) && count($commond)){
            foreach ($commond as $class){
                $classList[$class['classid']] = $class['title'];
            }
        }
        $data['classList'] = $classList;

        return $data;
    }
    
    //申请节点保存
    public function save(){
        if (isset($_POST['ChildDiscountLink'])){
            $model = new ChildDiscountLink();
            $model->unsetAttributes();
            $model->attributes = $_POST['ChildDiscountLink'];
            $model->setScenario('workflow');
            if(is_array($model->childid) && count($model->childid)){
                $childList = $model->childid;
                $model->childid = current($model->childid);
            }
            if ($model->validate()){
                $transaction = Yii::app()->db->beginTransaction();
                try{
                    $discountSchool = DiscountSchool::model()->findByPk($model->discount_id);
                    foreach ($childList as $child){
                        $link = new ChildDiscountLink();
                        $link->attributes = $model->getAttributes();
                        $link->childid = $child;
                        $link->operation_id = 0;
                        $link->status = ChildDiscountLink::DISCOUNT_STATUS_INVALID;
                        $link->update_user = Yii::app()->user->id;
                        $link->update_timestamp = time();
                        if (!$link->save()){
                            $transaction->rollback();
                            $this->controller->addMessage('state', 'fail');
                            $this->controller->addMessage('message', '保存失败！原因:向折扣绑定表添加数据失败！');
                            $this->controller->showMessage();
                        }else{
                            $linkList[] = $link->id;
                        }
                    }
                    //保存工作流业务流程
                    $operationData = array('operation_type'=>0,'operation_object_id'=>$model->discount_id,'defination_id'=>$this->workflow->getWorkflowId(),'branchid'=>$this->workflow->schoolId,'state'=>WorkflowOperation::WORKFLOW_STATS_UNOP,'current_node_index'=>$this->workflow->getNextNodeId(),'exte1'=>implode(',', $childList));
                    $operationRet = $this->workflow->saveFirstNodeForWorkflow($operationData,$linkList,$link->memo,$transaction,array($discountSchool->mailto=>0));
                    if ($operationRet === false){
                        $this->controller->addMessage('state', 'fail');
                        $this->controller->addMessage('message', '保存失败！原因：工作流底层保存失败！');
                        $this->controller->showMessage();
                    }
                    if (!ChildDiscountLink::model()->updateByPk($linkList, array('operation_id'=>$operationRet))){
                        $this->controller->addMessage('state', 'fail');
                        $this->controller->addMessage('message', '保存失败！原因：更新折扣绑定表失败！');
                        $this->controller->showMessage();
                    }
                    // 更新相关附件的 link_id
                    if ($files = $_POST['files']) {
                        foreach ($files as $file) {
                            $uploadModel = Uploads::model()->findByPk($file);
                            if (!$uploadModel) {
                                continue;
                            }
                            $uploadModel->link_id = $this->workflow->opertationObj->id;
                            $uploadModel->save();
                        }
                    }
                    $transaction->commit();
                    //发邮件
                    $this->jumpMail();
                    $this->controller->addMessage('state', 'success');
                    $this->controller->addMessage('message', '成功');
                    $this->controller->addMessage('data',array('modalNameId'=>'workflow-discount-modal','operatorId'=>$this->workflow->getOperateId(),'html'=>$this->workflow->getNodeListUseHtml()));
                    $this->controller->addMessage('callback', 'saveWorkflowOperator');
                    $this->controller->showMessage();
                } catch (Exception $ex) {
                    $this->controller->addMessage('state', 'fail');
                    $this->controller->addMessage('message', $ex->getMessage());
                    $transaction->rollBack();
                    $this->controller->showMessage();
                }
            }else{
                $this->controller->addMessage('state', 'fail');
                $errs = current($model->getErrors());
                if ($errs)
                    $this->controller->addMessage('message', $errs[0]);
                $this->controller->showMessage();
            }
        }
    }

    public function show(){
        //Definition template
        $this->setTemplate('show');
        //Definition modal's id and create js function
        $data['jsFunction'] = 'WorkflowModalShow';
        $data['modalNameId'] = 'workflow-discount-modal';
        //业务
        $discountSchool = DiscountSchool::model()->with('discountTitle','approver')->findByPk($this->workflow->getOperateObjId());
        $childIds = ($this->workflow->getOperateValue('exte1')) ? explode(',', $this->workflow->getOperateValue('exte1')) : array();
        if (is_array($childIds) && !count($childIds)){
            throw new Exception('工作流程业务参数错误！',500);
        }
        $criter = new CDbCriteria();
        $criter->compare('childid', $childIds);
        $childModel = ChildProfileBasic::model()->findAll($criter);
        //处理POST请求
        if (Yii::app()->request->isPostRequest){
            $this->handle();
        }
        $data['workflow'] = $this->workflow;
        $data['discountSchool'] = $discountSchool;
        $data['childModel'] = $childModel;
        return $data;
    }
    
    public function handle(){
        $buttonCategory = Yii::app()->request->getPost('buttonCategory',0);
        $memo = Yii::app()->request->getPost('memo','');
        $workflowStatus = WorkflowOperation::WORKFLOW_STATS_UNOP;
        if (empty($memo)){
            $this->controller->addMessage('state', 'fail');
            $this->controller->addMessage('message', '保存失败，原因：意见必填！');
            $this->controller->showMessage();
        }
        $transaction = Yii::app()->db->beginTransaction();
        try{
            $discountLink = ChildDiscountLink::model()->findAll('operation_id=:operation_id',array(':operation_id'=>$this->workflow->getOperateId()));
            //被拒绝
            if ($buttonCategory == WorkflowOperation::WORKFLOW_STATS_OPDENY){
                foreach ($discountLink as $link){
                    if (!$link->delete()){
                        $transaction->rollback();
                        $this->controller->addMessage('state', 'fail');
                        $this->controller->addMessage('message', '保存失败，原因：删除折扣绑定表数据失败。');
                        $this->controller->showMessage();
                    }
                }
                $workflowStatus = WorkflowOperation::WORKFLOW_STATS_OPDENY;
            }else{
                foreach ($discountLink as $link){
                    $link->status = ChildDiscountLink::DISCOUNT_STATUS_VALID;
                    if (!$link->save()){
                        $transaction->rollback();
                        $this->controller->addMessage('state', 'fail');
                        $this->controller->addMessage('message', '保存失败，原因：更新折扣绑定表数据失败。');
                        $this->controller->showMessage();
                    }
                }
                $workflowStatus = WorkflowOperation::WORKFLOW_STATS_OPED;
            }
            if ($this->workflow->saveWorkflow($memo,$workflowStatus,$transaction) === false){
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '保存失败，原因：工作流底层更新失败！');
                $this->controller->showMessage();
            }
            $transaction->commit();
            //发邮件
            $this->jumpMail();
            $this->controller->addMessage('state', 'success');
            $this->controller->addMessage('message', '保存成功');
            $this->controller->addMessage('data',array('modalNameId'=>'workflow-discount-modal','operatorId'=>$this->workflow->getOperateId(),'html'=>$this->workflow->getNodeListUseHtml()));
            $this->controller->addMessage('callback', 'saveWorkflowOperator');
            $this->controller->showMessage();
        } catch (Exception $ex) {
            $transaction->rollBack();
            $this->controller->addMessage('state', 'fail');
            $this->controller->addMessage('message', $ex->getMessage());
            $this->controller->showMessage();
        }
    }
    
    public function jumpMail() {
        $branchList = Branch::model()->getBranchList();
        $branchTitle = $branchList[$this->workflow->getOperateValue('branchid')];
        $workflowTitle = CommonUtils::autoLang($this->workflow->definationObj->defination_name_cn,$this->workflow->definationObj->defination_name_en);
        $nodesTitle = CommonUtils::autoLang($this->workflow->nodesList[$this->workflow->getNextNodeId()]->node_name_cn,$this->workflow->nodesList[$this->workflow->getNextNodeId()]->node_name_en);
        if ($this->workflow->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_OPDENY){
            $subject = sprintf('%s 校园提出 %s申请被拒绝',$branchTitle,$workflowTitle);
        }else{
            if ($this->workflow->isEndNode()){
                $subject = sprintf('%s 校园提出 %s申请已完成',$branchTitle,$workflowTitle);
            }else{
                $subject = sprintf('%s 校园提出 %s申请需要您处理',$branchTitle,$workflowTitle);
            }
        }
        // 微信推送
        $this->workflow->wechatPush();
        $link = $this->controller->createUrl('/workflow/index', array('branchId'=>$this->workflow->schoolId));
        $body = $this->controller->renderPartial('/mail/templater',array('branchTitle'=>$branchTitle,'childName'=>'','workflowTitle'=>$workflowTitle,'nodes'=>$nodesTitle,'link'=>$link,'workflow'=>$this->workflow),true);
        return $this->workflow->sendEmail($subject,$body);
    }
    
    public function delete() {
        
    }
    
     //实现接口方法
    public function setTemplate($name){
        $this->template = $name;
    }
    
    //实现接口方法
    public function getTemplate(){
        return $this->template;
    }

    /**
     * 保存上传的附件
     * @return [type] [description]
     */
    public function saveUploadFile()
    {
        if ($file = CUploadedFile::getInstanceByName('file')) {
            if ($file->size > 10 * 1024 * 1024) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', $file->name . '上传失败，最大10m。');
                $this->controller->showMessage();
            }
            $ext = strtolower($file->getExtensionName());
            $needType = array('jpg', 'png', 'pdf', 'gif', 'jpeg', 'doc', 'docx', 'xls', 'xlsx', 'tif', 'tiff');
            if (!in_array($ext, $needType)) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', $file->name . '上传失败，资料类型出错。');
                $this->controller->showMessage();
            }
            $datePath = date('Y') . '/' . date('m') . '/';
            $filePath = Yii::app()->params['xoopsVarPath'] . '/uploads/' . $datePath;
            if (!is_dir($filePath)) {
                mkdir($filePath, 0777, true);
            }
            $baseUrl = Yii::app()->params['OABaseUrl'] . "/modules/myspace/task/getthumb.php?img=";
            $fileName = uniqid() . '.' . $ext;
            if (!$file->saveAs($filePath . $fileName)) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message',  $file->name . '资料保存失败。');
                $this->controller->showMessage();
            }
            //保存表ivy_uploads
            $uploadModel = new Uploads();
            $uploadModel->link_id = $this->workflow->opertationObj->id;
            $uploadModel->mod_name = 'workflow';
            $uploadModel->func_name = 'discount';
            $uploadModel->file_name = $datePath . $fileName;

            $uploadModel->notes = pathinfo($file->name, PATHINFO_FILENAME);
            $uploadModel->update_time = time();
            $uploadModel->update_user = Yii::app()->user->id;
            if (!$uploadModel->save()) {
                unlink($filePath . $fileName);
                $error = current($uploadModel->getErrors());
                $this->controller->addMessage('message', $file->name . '保存失败: ' . $error[0]);
                $this->controller->showMessage();
            }
            $this->controller->addMessage('data', array('fileId' => $uploadModel->id, 'fileName' => $uploadModel->notes, 'url' => $baseUrl . $uploadModel->file_name));
            $this->controller->addMessage('state', 'success');
            $this->controller->addMessage('message', '资料上传成功。');
            $this->controller->showMessage();
        }
        Yii::app()->end();
    }

    /**
     * 删除附件
     * @return [type] [description]
     */
    public function deleteUploadFile()
    {
        if (Yii::app()->request->isPostRequest) {
            $id = Yii::app()->request->getPost('id', 0);
            if ($uploadModel = Uploads::model()->findByPk($id)) {
                //删除资料
                $fileName = $uploadModel->file_name;
                $filePath = Yii::app()->params['xoopsVarPath'] . '/uploads/';
                if ($uploadModel->delete()) {
                    unlink($filePath . $fileName);
                    $this->controller->addMessage('state', 'success');
                    $this->controller->addMessage('message', '资料删除成功。');
                    $this->controller->showMessage();
                }
            }
        }
        $this->controller->addMessage('state', 'fail');
        $this->controller->addMessage('message', '资料删除失败。');
        $this->controller->showMessage();
    }

    /**
     * 修改附件显示名
     * @param string $value [description]
     */
    public function changeFileName()
    {
        if (Yii::app()->request->isPostRequest) {
            $id = Yii::app()->request->getPost('id', 0);
            $notes = Yii::app()->request->getPost('notes', 0);
            if ($notes && $uploadModel = Uploads::model()->findByPk($id)) {
                $uploadModel->notes = $notes;
                if ($uploadModel->save()) {
                    $this->controller->addMessage('state', 'success');
                    $this->controller->addMessage('message', '资料修改成功。');
                    $this->controller->showMessage();
                }
            }
        }
        $this->controller->addMessage('state', 'fail');
        $this->controller->addMessage('message', '资料修改失败。');
        $this->controller->showMessage();
    }
}

