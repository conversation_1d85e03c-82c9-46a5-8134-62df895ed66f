<?php

// uncomment the following to define a path alias
// Yii::setPathOfAlias('local','path/to/local-folder');

// This is the main Web application configuration. Any writable
// CWebApplication properties can be configured here.

$dbhost = "rds3ab3mvfvjbnu.mysql.rds.aliyuncs.com";
$dbname = "mims";
$subdbname = 'mimssub';
$dbuser = "ivydbuser_a";
$dbpass = "ivyDBtyg2006";
$oaRedirect = true;

$cookieDomain = ".ivyonline.cn"; //孩子前台专用，无需共享
$oaUrl = "http://oa.ivyonline.cn"; //OA 地址

$ivyschoolsUrl = 'http://www.ivyschools.com';
$useIS = false;
$uploadBaseUrl = $oaUrl . "/uploads/"; //开发时为显示图片可以设置为实际OA的upload URL地址
$uploadPath = '/hdc/www/ivysites/oa.ivyonline.cn/trunk/oasrc/htdocs/uploads/';
$xoopsVarPath = '/hdc/www/ivysites/oa.ivyonline.cn/trunk/oasrc/XOOPS-data';
$onlineBanking = "production"; //sandbox, production 
$YeepayRespondeUrl= $oaUrl . '/responder/responder_v2.php';
$AlipayResponse = array(
    "return" => $oaUrl . '/responder/alipay_sync_notify.php',
    "notify" => $oaUrl . '/responder/alipay_async_notify.php',
);
$sessionName = "MIMSCSID";  //sessionId的名字，别跟XOOPS那边的一样，可随意设定
$basePath = dirname(__FILE__) . DIRECTORY_SEPARATOR . '..';
$themeBasePath = dirname(__FILE__) . DIRECTORY_SEPARATOR . '..' . DIRECTORY_SEPARATOR . '..' . DIRECTORY_SEPARATOR . 'themes';
$themesUrl = "http://www.ivyonline.cn/themes"; //真正孩子前台的theme地址，可以设置成单独的一个域名

$sessionTableName ="ivy_session_cc"; //为孩子前台分离出的session新表，与ivy_session表结构完全一样

$sessionKeyPrefix = md5('Yii.CXoWebUser.appChild');

require_once $basePath . DIRECTORY_SEPARATOR . 'components' . DIRECTORY_SEPARATOR . 'utils.php';

$appsUrl = 'http://apps.ivyonline.cn';
$enableAliYunOSS = true;

$baseUrl = "http://dsk" . $cookieDomain;

$productionDomain = 'dsk.ivyonline.cn';

$commonUploadUrl = 'http://apps.ivyonline.cn/uploads/';

$ownerConfig = array(
    'key' => 'BJ_DS',
    'name_en' => 'DayStar',
    'name_cn' => '启明星',
    'logo' => 'http://dse.ivyonline.cn/themes/highlight/images/logo_ds_v2.png', #300x60
    'wechatWelcomeTitle' => '本号为御之桥微信服务号，旨在为御之桥的家长提供更好的服务，请点击绑定系统账户',
    'wechatWelcomePic' => 'http://www.ivyonline.cn/themes/mobile/images/welcome_yzq.jpg',
    'loginPage' => array(
        array(
            'image' => true,
            'file' => 'http://apps.ivyonline.cn/uploads/dsk/slider2.jpg',
            //'url'=>'javascript:(void);',
            'style' => 'background-color:white;',
            //'caption'=>'Demo caption, this is demo caption',
        ),
        array(
            'image' => true,
            'file' => 'http://apps.ivyonline.cn/uploads/dsk/slider3.jpg',
            //'url'=>'javascript:(void);',
            'style' => 'background-color:white;display:none;',
            //'caption'=>'Demo caption, this is demo caption',
        ),
        array(
            'image' => true,
            'file' => 'http://apps.ivyonline.cn/uploads/dsk/slider4.jpg',
            //'url'=>'javascript:(void);',
            'style' => 'background-color:white;display:none;',
            //'caption'=>'Demo caption, this is demo caption',
        ),
        array(
            'image' => true,
            'file' => 'http://apps.ivyonline.cn/uploads/dsk/slider5.jpg',
            //'url'=>'javascript:(void);',
            'style' => 'background-color:white;display:none;',
            //'caption'=>'Demo caption, this is demo caption',
        ),
	array(
            'image' => true,
            'file' => 'http://apps.ivyonline.cn/uploads/dsk/slider6.jpg',
            //'url'=>'javascript:(void);',
            'style' => 'background-color:white;display:none;',
            //'caption'=>'Demo caption, this is demo caption',
        ),

    )
);

return array(
	'basePath'=>$basePath,
	'name'=>'DayStar',
	'homeUrl'=>$baseUrl,

	// preloading 'log' component
	'preload'=>array('log'),
    'theme'=>'highlight',
    'defaultController'=>'user/login',

	// autoloading model and component classes
	'import'=>array(
		'common.models.*',
		'application.components.*',
        'common.components.*',
	),

    'sourceLanguage'=>'en_us',
    'language'=>'zh_cn',
	'localeDataPath'=>dirname(__FILE__).DIRECTORY_SEPARATOR.'..'.'/components/locale_data/',
	'modules'=>array(
		// uncomment the following to enable the Gii tool
	/*	
		'gii'=>array(
			'class'=>'system.gii.GiiModule',
			'password'=>'test!',
		 	// If removed, Gii defaults to localhost only. Edit carefully to taste.
			'ipFilters'=>array('127.0.0.1','::1'),
		),
        */
		'user'=>array(
			"OAUrl" => $oaUrl,
			"OALoginUrl" => $oaUrl . "/login.php"
		),
        'child'=>array(),
        'portfolio'=>array(),
		'weixin',
        'club',
	),

	// application components
	'components'=>array(
	
        'session'=>array(
            'autoCreateSessionTable'=>false,
            'class'=>'application.components.CXoDbHttpSession',
            'cookieParams'=>array('domain'=>$cookieDomain,'lifetime'=>0),
            'connectionID' => 'db',
            'timeout' => 28800,
            'sessionTableName' =>$sessionTableName,
            'sessionName' => $sessionName,
        ),
		'user'=>array(
			// enable cookie-based authentication
            'class'=>'application.components.CXoWebUser',
            'allowAutoLogin'=>true,
			'_keyPrefix' => $sessionKeyPrefix,
		),
        'authManager'=>array(
            'class'=>'CDbAuthManager',
            'connectionID' => 'db',
            'defaultRoles'=>array('visitor')
        ),

		// uncomment the following to enable URLs in path-format
		'urlManager'=>array(
            'baseUrl'=>$baseUrl,
			'urlFormat'=>'path',
            //'urlSuffix'=>'.ivy',
			'rules'=>array(
				//'<controller:\w+>/<id:\d+>'=>'<controller>/view',
                'v/<id:\w+>'=>'access/index',
				'lostpass/<activkey:\w+>/<email:.*>'=>'user/recovery/recovery',
                '<childid:\d+>' => 'child/profile/welcome',
                '<childid:\d+>/portfolio/portfolio/journal/<startyear:\d+>-<classid:\d+>-<weeknum:\d+>' => 'portfolio/portfolio/journal',
                '<childid:\d+>/portfolio/portfolio/semester/<startyear:\d+>-<semester:\d+>' => 'portfolio/portfolio/semester',
                '<childid:\d+>/child/resource/content/<category:\w+>' => 'child/resource/content',
                '<childid:\d+>/child/profile/profile/<t:\w+>' => 'child/profile/profile',
				
                '<childid:\d+>/<module:\\w+>/<controller:\w+>/<action:\w+>' => '<module>/<controller>/<action>',
                '<childid:\d+>/<module:\\w+>/<controller:\w+>/<action:\w+>/<classid:\d+>' => '<module>/<controller>/<action>',
				'<childid:\d+>/<controller:\w+>/<action:\w+>/<id:\d+>'=>'<controller>/<action>',
				'<childid:\d+>/<controller:\w+>/<action:\w+>'=>'<controller>/<action>',
			),
		),


        'themeManager'=>array(
            'basePath'=>$themeBasePath,
            'baseUrl'=>$themesUrl
        ),

		// uncomment the following to use a MySQL database
		'db'=>array(
			'connectionString' => sprintf('mysql:host=%s;dbname=%s', $dbhost, $dbname),
			'emulatePrepare' => true,
			'username' => $dbuser,
			'password' => $dbpass,
			'charset' => 'utf8',
			'schemaCachingDuration' => 300,
		),
        'subdb'=>array(
            'class'=>'CDbConnection',
	'connectionString' => sprintf('mysql:host=%s;dbname=%s', $dbhost, $subdbname),
            'emulatePrepare' => true,
            'username' => $dbuser,
            'password' => $dbpass,
            'charset' => 'utf8',
        ),
		'cache'=>array(
				'class'=>'CFileCache',
		),
		'weatherCache'=>array(
				'class'=>'CDbCache',
				'connectionID'=>'db',
				'autoCreateCacheTable'=>false,
				'keyPrefix'=>'weather_',
				'cacheTableName'=>'ivy_yii_dbcache',
		),		
        'request'=>array(
            'enableCookieValidation'=>true,  
        ),
		'errorHandler'=>array(
			// use 'site/error' action to display errors
            'errorAction'=>'site/error',
        ),
		'log'=>array(
			'class'=>'CLogRouter',
			'routes'=>array(
				array(
					'class'=>'CFileLogRoute',
					'levels'=>'error, warning',
				),
				// uncomment the following to show log messages on web pages
				/*
				array(
					'class'=>'CWebLogRoute',
				),
				*/
			),
		),
	),

	// application-level parameters that can be accessed
	// using Yii::app()->params['paramName']
	'params'=>array(
		'baiduStats'=>false,
		// this is used in contact page
		'adminEmail'=>'<EMAIL>',
		'uploadBaseUrl'=>$uploadBaseUrl,
		'uploadPath'=>$uploadPath,
		// xoops data 目录 用于存放 在线支付的日志文件
		'xoopsVarPath'=>$xoopsVarPath,
		// 商户接收支付成功数据的地址
		'YeepayRespondeUrl'=>$YeepayRespondeUrl,
		'AlipayResponds'=>array(
			"return"=>$AlipayResponse['return'],
			"notify"=>$AlipayResponse['notify'],
		),
		'onlineBanking' => $onlineBanking, //sandbox, production 
		'refreshAssets' => 'refreshAssets' ,
		'oaRedirect' => $oaRedirect,
        'ivyschoolsUrl' => $ivyschoolsUrl,
        'useIS' => $useIS,
        'commonUploadUrl' => $commonUploadUrl,
        'appsUrl' => $appsUrl,
		'unIvy' => true,
        'ownerConfig' => $ownerConfig,
	'hideShortCuts' => true,
	'enableAliYunOSS' => $enableAliYunOSS,
	'productionDomain' => $productionDomain,
	'gradeUrl'=>'http://dse.ivyonline.cn',
        'casaUrl'=>'http://dsk.ivyonline.cn',
	),
);
