<?php

/**
 * This is the model class for table "ivy_user_leave_application".
 *
 * The followings are the available columns in table 'ivy_user_leave_application':
 * @property integer $uid
 * @property integer $application_date
 * @property integer $leave_date
 * @property string $memo
 * @property integer $status
 * @property string $cause
 * @property integer $creator
 * @property integer $created
 */
class UserLeaveApplication extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_user_leave_application';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('uid, application_date, leave_date, status, creator, created', 'required'),
			array('uid, application_date, leave_date, status, creator, created', 'numerical', 'integerOnly'=>true),
			array('memo', 'length', 'max'=>1000),
			array('cause', 'length', 'max'=>20),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('uid, application_date, leave_date, memo, status, cause, creator, created', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'uid' => 'Uid',
			'application_date' => 'Application Date',
			'leave_date' => 'Leave Date',
			'memo' => 'Memo',
			'status' => 'Status',
			'cause' => 'Cause',
			'creator' => 'Creator',
			'created' => 'Created',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('uid',$this->uid);
		$criteria->compare('application_date',$this->application_date);
		$criteria->compare('leave_date',$this->leave_date);
		$criteria->compare('memo',$this->memo,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('cause',$this->cause,true);
		$criteria->compare('creator',$this->creator);
		$criteria->compare('created',$this->created);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return UserLeaveApplication the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
