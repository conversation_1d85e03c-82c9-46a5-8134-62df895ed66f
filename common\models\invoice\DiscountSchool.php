<?php

/**
 * This is the model class for table "ivy_discount_school".
 *
 * The followings are the available columns in table 'ivy_discount_school':
 * @property integer $id
 * @property integer $category_id
 * @property string $schoolid
 * @property double $discount
 * @property integer $stat
 * @property integer $expire_date
 * @property integer $mailto
 * @property integer $is_sentmail
 * @property integer $allow_bind2child
 * @property integer $updated_timestamp
 * @property integer $userid
 */
class DiscountSchool extends CActiveRecord
{
    const DISCOUNT_ALLOW_BIND = 1;
    const DISCOUNT_NOT_ALLOW_BIND = 0;
    const DISCOUNT_STATUS_VALID = 10;
    const DISCOUNT_STATUS_INVALID = 90;
    /**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return DiscountSchool the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_discount_school';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('category_id, schoolid, discount, stat, expire_date, updated_timestamp, userid', 'required'),
			array('category_id, stat, expire_date, mailto, is_sentmail, allow_bind2child, updated_timestamp, userid, flag', 'numerical', 'integerOnly'=>true),
			array('discount', 'numerical'),
			array('schoolid', 'length', 'max'=>10),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, category_id, schoolid, discount, stat, expire_date, mailto, is_sentmail, allow_bind2child, updated_timestamp, userid, flag', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'discountTitle'=> array(self::BELONGS_TO, 'DiscountCategory', 'category_id','select'=>'title_en,title_cn'),
            'approver'=>array(self::BELONGS_TO, 'User', 'mailto', 'with'=>'profile', 'together'=>false),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'category_id' => 'Category',
			'schoolid' => 'Schoolid',
			'discount' => 'Discount',
			'stat' => 'Stat',
			'expire_date' => 'Expire Date',
			'mailto' => 'Mailto',
			'is_sentmail' => 'Is Sentmail',
			'allow_bind2child' => 'Allow Bind2child',
			'updated_timestamp' => 'Updated Timestamp',
			'userid' => 'Userid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('category_id',$this->category_id);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('discount',$this->discount);
		$criteria->compare('stat',$this->stat);
		$criteria->compare('expire_date',$this->expire_date);
		$criteria->compare('mailto',$this->mailto);
		$criteria->compare('is_sentmail',$this->is_sentmail);
		$criteria->compare('allow_bind2child',$this->allow_bind2child);
		$criteria->compare('updated_timestamp',$this->updated_timestamp);
		$criteria->compare('userid',$this->userid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
	
	public function scopes()
	{
		return array(
			'dis' => array(
				'select' =>'discount,id'
			)
		);
	}

	public static function getDiscountList($schoolId, $is_sentmail = 1) {
		$discount = 90;
		if (in_arraY($schoolId, array('BJ_DS', 'BJ_SLT', 'BJ_QFF'))) {
			$discount = 85;
		}
		$currentTimestamp = time();
        $criteria = new CDbCriteria;
        $criteria->compare('t.schoolid', $schoolId);
        $criteria->compare('t.allow_bind2child', DiscountSchool::DISCOUNT_ALLOW_BIND);
        $criteria->compare('t.stat', DiscountSchool::DISCOUNT_STATUS_VALID);
        $criteria->compare('t.discount', '>=' . $discount);
		if ($is_sentmail) {
			$criteria->compare('t.is_sentmail', $is_sentmail);
		}
		$criteria->order = "t.discount DESC";
        $criteria->addCondition('t.expire_date>'.$currentTimestamp);
        $discountList = DiscountSchool::model()->with('discountTitle')->findAll($criteria);
		$list = array(array('id' => 0, 'title' => '无折扣'));
        if (!empty($discountList)){
            foreach ($discountList as $discount){
                $list[] = array(
					'id' => $discount->id,
					'title' => $discount->discount * 100 / 100 . '% ' . $discount->discountTitle->title_cn
				);
            }
        }
		return $list;
	}

	public static function checkView($schoolId, $roles, $uid)
	{
		if (!in_array($schoolId, array('BJ_DS', 'BJ_SLT', 'BJ_QFF'))) {
			return true;
		}
		
		if (in_array('ivystaff_it', $roles)) {
			return true;
		}
		
		if (empty($uid)) {
			return false;
		}
		return in_array($uid, self::getViewUid());
	}

	public static function getViewUid()
	{
		return array(
			8011391,	// Heather
			8016254,	// 新苗
			8028205,	// Vicky
			8018135,	// cynthia luo
			8024244,	//  michelle tian
			8012417,	//  sophia tian
			8017912,	//  quinta li
			8019827,	//  vivienne wen
			8011402,	//  hui yang
			8020989,    //  李姝 (Suly Li)
		);
	}
}