<IfModule mod_rewrite.c>
RewriteEngine On
RewriteBase /

RewriteRule ^modules/(.*) http://oa.ivyonline.cn/modules/$1 [R=permanent,L]
RewriteRule ^newsletter/cancel_autosend_email.php(.*) http://oa.ivyonline.cn/newsletter/cancel_autosend_email.php$1 [R=permanent,L]
RewriteRule ^newsletter/ http://oa.ivyonline.cn/newsletter/ [R=permanent,L]
RewriteRule ^newsletter/(.*) http://oa.ivyonline.cn/newsletter/$1 [R=permanent,L]
RewriteRule ^login.php(.*) http://www.ivyonline.cn/user/login/login/$1 [R=permanent,L]
RewriteRule ^lostpass.php(.*) http://www.ivyonline.cn/user/recovery/recovery/$1 [R=permanent,L]


RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !\.(css|gif|ico|jpg|js|png|swf|txt)$
RewriteRule ^(.*)$ index.php/$1 [L]

rewritecond %{http_host} ^ivyonline.cn [nc]
rewriterule ^(.*)$ http://www.ivyonline.cn/$1 [r=301,nc]
</IfModule>