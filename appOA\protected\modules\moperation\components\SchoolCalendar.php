<?php
class SchoolCalendar{

	function __construct() {
		Yii::import('common.models.calendar.*');
	}
	/*
	 * 生成校历内所有的周
	 * @param int $calendarId;		校历
	 * @param int $startDate		开始日期
	 * @param int $endDate			结束日期
	 * @return boolean;
	 */
	public function generateCalendarWeek($calendarId,$startDate,$endDate){
		CalendarWeek::model()->deleteAllByAttributes(array('yid'=>$calendarId));
		$weekDay = date('w',$startDate);
		$flag = 604800; // 7*24*60*60
		$monTimeStamp = ($weekDay==0 || $weekDay==6) ? strtotime("next monday",$startDate) : strtotime("Monday this week",$startDate);
		$week = 1;
		$sql = 'INSERT INTO `'.CalendarWeek::model()->tableName().'` (`wid`, `yid`, `sid`, `monday`, `monday_timestamp`, `weeknumber`) VALUES ';
		for($i=$monTimeStamp;$i<=$endDate;$i+=$flag){
			//Splice SQL
			$fmtDay = date('Ymd',$i);
			$sql .= "(0, {$calendarId}, 0, {$fmtDay}, {$i}, {$week}),";
			$week++;
		}
		$sql = rtrim($sql,',').";";
		$ret = Yii::app()->db->createCommand($sql)->execute();
		return $ret;
	}

	/*
	 * 修改生成校历内的教学天数
	 * @param int $calendarId;		校历
	 * @param int $startDate		开始日期
	 * @param int $endDate			结束日期
	 * @param boolean $repeat		是否重新生成数据
	 * @return boolean;
	 */

	public function generateSchoolDay($calendarId,$dayList){
		CalendarSchoolDays::model()->deleteAllByAttributes(array('yid'=>$calendarId));
		foreach ($dayList as $val){
			$fmtDay[] = date('Y-m',$val);
		}

		$this->generateSchoolDayT($calendarId,$dayList);

        $time1 = strtotime($fmtDay[0]); // 自动为00:00:00 时分秒
        $time2 = strtotime($fmtDay[3]);
        $monarr = array(date("Ym", strtotime($fmtDay[0])));
        while( ($time1 = strtotime('+1 month', $time1)) <= $time2){
            $monarr[] = date("Ym", $time1); // 取得递增月;
        }

        foreach ($monarr as $val){
            $this->updateSchoolDayForMonth($calendarId,$val);
        }
	}

    /*
     * 新增生成校历内的教学天数
     * @param int $calendarId;		校历
     * @param int $startDate		开始日期
     * @param int $endDate			结束日期
     * @param boolean $repeat		是否重新生成数据
     * @return boolean;
     */

    public function generateSchoolDayT($calendarId,$dayList){
        CalendarSchoolDays::model()->deleteAllByAttributes(array('yid'=>$calendarId));
        foreach ($dayList as $val){
            $fmtDay[] = date('Y-m',$val);
        }

        $sql = "INSERT INTO `".CalendarSchoolDays::model()->tableName()."` (`id`, `yid`, `semester_flag`, `total_day`, `schoolday`, `month_label`, `month`, `schoolday_array`, `half_schoolday_array`) VALUES";
        for($i=$fmtDay[0];$i<=$fmtDay[3];$i=date('Y-m',strtotime($i.'next month'))){
            $month = str_replace('-', '', $i);
            $fstMonthStem = strtotime($i);
            $totalDay = date('t',  $fstMonthStem);
            switch ($i){
                case $fmtDay[0]:
                    $semesterFlag = 10;
                    $schooldayArray = $this->getSchoolDaysWithinMonth($dayList[0],  strtotime($fmtDay[0].'-'.$totalDay));
                    $schoolDay = count($schooldayArray);
                    $schooldayArray = implode(',',$schooldayArray);
                    break;
                case $fmtDay[1]:
                    if($fmtDay[1] == $fmtDay[2]){
                        $semesterFlag = 20;
                        $schooldayArray1 = $this->getSchoolDaysWithinMonth($fstMonthStem, $dayList[1]);
                        $schooldayArray2 = $this->getSchoolDaysWithinMonth($dayList[2], strtotime($fmtDay[2].'-'.$totalDay));
                        $schooldayArray = $schooldayArray1+$schooldayArray2;
                    }else{
                        $schooldayArray = $this->getSchoolDaysWithinMonth($fstMonthStem, $dayList[1]);
                    }
                    $schoolDay = count($schooldayArray);
                    $schooldayArray = implode(',', $schooldayArray);
                    break;
                case $fmtDay[2]:
                    $semesterFlag = 20;
                    $schooldayArray = $this->getSchoolDaysWithinMonth($dayList[2], strtotime($fmtDay[2].'-'.$totalDay));
                    $schoolDay = count($schooldayArray);
                    $schooldayArray = implode(',',$schooldayArray);
                    break;
                case $fmtDay[3]:
                    $schooldayArray = $this->getSchoolDaysWithinMonth($fstMonthStem, $dayList[3]);
                    $schoolDay = count($schooldayArray);
                    $schooldayArray = implode(',',$schooldayArray);
                    break;
                default :
                    $schooldayArray = $this->getSchoolDaysWithinMonth($fstMonthStem,  strtotime($i.'-'.$totalDay));
                    $schoolDay = count($schooldayArray);
                    $schooldayArray = implode(',',$schooldayArray);
                    break;
            }
            $sql .= "(0, {$calendarId}, {$semesterFlag}, {$totalDay}, '{$schoolDay}', '{$i}', {$month}, '{$schooldayArray}', ''),";
        }
        $sql = rtrim($sql,',').";";
        $ret = Yii::app()->db->createCommand($sql)->execute();
        return $ret;
    }

	/*
	 * 取一段日期内的工作日
	 * @return array
	 */
	public function getSchoolDaysWithinMonth($startDate, $endDate) {
		$ret = array();
		$flag = 86400;
		for($i=$startDate;$i<=$endDate;$i+=$flag){
			$dayList = getdate($i);
			if (!in_array($dayList['wday'], array(0,6))){
				$day = sprintf('%02d', $dayList['mday']);
				$ret[$day] = $day;
			}
		}
		return $ret;
	}

	/*
	 * 另存为校历模板
	 */
	public function SaveCalendarAs($calendarId,$title,$stat){
		$calendarModel = Calendar::model()->findByPk($calendarId);
		if (!empty($calendarModel)){
			$model = new Calendar();
			$model->setAttributes($calendarModel->getAttributes());
			$model->title = $title;
			$model->stat = ($stat) ? 10 : 20;
			if ($model->save()){
				//week
				$weekList = CalendarWeek::model()->findAll('yid=:calendarId',array(':calendarId'=>$calendarId));
				if (!empty($weekList)){
					$sql = 'INSERT INTO `'.CalendarWeek::model()->tableName().'` (`wid`, `yid`, `sid`, `monday`, `monday_timestamp`, `weeknumber`) VALUES ';
					foreach ($weekList as $val){
						$sql .= "(0, {$model->yid}, {$val->sid}, {$val->monday}, {$val->monday_timestamp}, {$val->weeknumber}),";
					}
					$sql = rtrim($sql,',').";";
					$command = Yii::app()->db->createCommand($sql);
					$command->execute();
				}
				unset($weekList);
				//school day
				$schoolDayList = CalendarSchoolDays::model()->findAll('yid=:calendarId',array(':calendarId'=>$calendarId));
				if (!empty($schoolDayList)){
					$sql = "INSERT INTO `".CalendarSchoolDays::model()->tableName()."` (`id`, `yid`, `semester_flag`, `total_day`, `schoolday`, `month_label`, `month`, `schoolday_array`, `half_schoolday_array`) VALUES";
					foreach ($schoolDayList as $val){
						$sql .= "(0, {$model->yid}, {$val->semester_flag}, {$val->total_day}, '{$val->schoolday}', '{$val->month_label}', {$val->month}, '{$val->schoolday_array}', '{$val->half_schoolday_array}'),";
					}
					$sql = rtrim($sql,',').";";
					$command = Yii::app()->db->createCommand($sql);
					$command->execute();
				}
				//event
				$dayList = CalendarDay::model()->findAll('yid=:calendarId and schoolid=:schoolid',array(':calendarId'=>$calendarId,':schoolid'=>'0'));
				if (!empty($dayList)){
					$sql = 'INSERT INTO `'.CalendarDay::model()->tableName().'` (`did`, `date`, `yid`, `sid`, `date_timestamp`, `day_type`, `event`, `title_cn`, `title_en`, `memo_cn`, `memo_en`, `privacy`, `schoolid`, `updated_timestamp`, `userid`) VALUES ';
					$nbValues = count($dayList);
					for($i=0;$i<$nbValues;$i++){
						$sql .= '(:did_'.$i.', :date_'.$i.', :yid_'.$i.', :sid_'.$i.', :date_timestamp_'.$i.', :day_type_'.$i.', :event_'.$i.', :title_cn_'.$i.', :title_en_'.$i.', :memo_cn_'.$i.', :memo_en_'.$i.', :privacy_'.$i.', :schoolid_'.$i.', :updated_timestamp_'.$i.', :userid_'.$i.')';
						if ($i !== ($nbValues-1))
							$sql .= ',';
					}
					$command = Yii::app()->db->createCommand($sql);
					$did = 0;
					for ($i=0; $i < $nbValues; $i++) {
						$command->bindParam(':did_'.$i, $did, PDO::PARAM_INT);
						$command->bindParam(':date_'.$i, $dayList[$i]->date, PDO::PARAM_INT);
						$command->bindParam(':yid_'.$i, $model->yid, PDO::PARAM_INT);
						$command->bindParam(':sid_'.$i, $dayList[$i]->sid, PDO::PARAM_INT);
						$command->bindParam(':date_timestamp_'.$i, $dayList[$i]->date_timestamp, PDO::PARAM_INT);
						$command->bindParam(':day_type_'.$i, $dayList[$i]->day_type, PDO::PARAM_INT);
						$command->bindParam(':event_'.$i, $dayList[$i]->event, PDO::PARAM_INT);
						$command->bindParam(':title_cn_'.$i, $dayList[$i]->title_cn, PDO::PARAM_STR);
						$command->bindParam(':title_en_'.$i, $dayList[$i]->title_en, PDO::PARAM_STR);
						$command->bindParam(':memo_cn_'.$i, $dayList[$i]->memo_cn, PDO::PARAM_STR);
						$command->bindParam(':memo_en_'.$i, $dayList[$i]->memo_en, PDO::PARAM_STR);
						$command->bindParam(':privacy_'.$i, $dayList[$i]->privacy, PDO::PARAM_INT);
						$command->bindParam(':schoolid_'.$i, $dayList[$i]->schoolid, PDO::PARAM_STR);
						$command->bindParam(':updated_timestamp_'.$i, $dayList[$i]->updated_timestamp, PDO::PARAM_INT);
						$command->bindParam(':userid_'.$i, $dayList[$i]->userid, PDO::PARAM_INT);
					}
					$command->execute();
				}
				return $model;
			}
		}
		return false;
	}

	/*
	 * 更新教学天数（编辑月历）
	 * @param	int	 $calendarId	校历ID
	 * @param	int  $month			月份 e.g. 201406
	 */
	public function updateSchoolDayForMonth($calendarId,$month){

		$halfSchoolDay = array();
		$fullSchoolDay = array();
		$update = false;

		$calendarModel = Calendar::model()->findByPk($calendarId);
        $calenderEnddate = 0;
        $calenderStartdate = 0;
		if($calendarModel){
            $timepoints = explode(',', $calendarModel->timepoints);
            $calenderStartdate = date("Ym",$timepoints[0]);
            $calenderEnddate = date("Ym",$timepoints[3]);
        }

        $startDate = strtotime($month.'01');

        if($calenderStartdate == $month){
            $startDate = $timepoints[0];
        }
        $endDate = strtotime($month.date('t',$startDate));
        if($calenderEnddate == $month){
            $endDate = $timepoints[3];
        }

		$flag = 86400;
		$ret = CalendarDay::model()->getHolidayAndSchoolDay($calendarId, $startDate, $endDate);
		for($i=$startDate;$i<=$endDate;$i+=$flag){
			$dayList = getdate($i);
			$day = sprintf('%02d', $dayList['mday']);
			if (isset($ret[CalendarDay::DAY_TOSCHOOLDAY][CalendarDay::DAY_TYPE_FULL][$i])){
				$fullSchoolDay[$day] = $day;
			}elseif(isset($ret[CalendarDay::DAY_TOSCHOOLDAY][CalendarDay::DAY_TYPE_HALF][$i])){
				$halfSchoolDay[$day] = $day;
				$fullSchoolDay[$day] = $day;
			}else{
				if ($dayList['wday']>0 && $dayList['wday']<6 && !isset($ret[CalendarDay::DAY_HOLIDAY][CalendarDay::DAY_TYPE_FULL][$i])){
					if (isset($ret[CalendarDay::DAY_HOLIDAY][CalendarDay::DAY_TYPE_HALF][$i])){
						$halfSchoolDay[$day] = $day;
						$fullSchoolDay[$day] = $day;
					}else{
						$fullSchoolDay[$day] = $day;
					}
				}
			}
		}
		$count = count($fullSchoolDay) - (count($halfSchoolDay)/2);
		$model = CalendarSchoolDays::model()->find('yid=:yid and month=:month',array(':yid'=>$calendarId,':month'=>$month));
		$model->schoolday = $count;
		$model->schoolday_array = implode(',', $fullSchoolDay);
		$model->half_schoolday_array = count($halfSchoolDay) ? implode(',',$halfSchoolDay) : null;

		if ($model->save()){
			$update = true;
		}
		return $update;
	}

	public function showSchoolDayForMonth($calendarBigMap){
        $crit = new CDbCriteria();
        $crit->compare('yid',$calendarBigMap);
        $schoolDaysModel = CalendarSchoolDays::model()->findAll($crit);
        $schoolDayList = array();
        $schoolyear = array();
        if($schoolDaysModel){
            foreach ($schoolDaysModel as $val){
                $schoolDayList[$val->yid]['month'][$val->month] = $val->schoolday;
                $schoolDayList[$val->yid]['semester'][$val->semester_flag] += $val->schoolday;
                $schoolDayList[$val->yid]['year'] += $val->schoolday;
            }
        }

        return $schoolDayList;
    }
}
