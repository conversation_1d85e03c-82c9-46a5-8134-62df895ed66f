<?php
/**
 * 必须要指定一所校园的Controller, 默认为当前用户所在的校园，若当前用户有权限管理多所校园，则出现选择校园的界面
 */
class BranchBasedController extends ProtectedController
{
	public $branchId = '';
    public $defaultToUserBranch = null; //如果用户可以管理多校园；可以通过此参数指定默认的校园
    public $branchObj = null;
	public $selectBranchActionId = 'select'; //选择校园的ActionId,
	//public $layout = '//layouts/branchBased';
	//public $layout = '//layouts/branchBased';
	public $branchSelectParams = array();
    public $calendarYids = array(); //e.g. array('currentYid'=>23, 'nextYid'=> 24)
    public $calendarStartYear = array(); //e.g. array('23'=>2014, '24'=> 2015)
	
	public function init(){
		parent::init();
		$this->branchBased = true;
		$this->multipleBranch = $this->accessMultipleBranches();
		
		$this->branchSelectParams = array(
		    'hideKG' => false,
			'hideOffice' => true,
			'showList' => false,
			'selectedBid' => '',
			'selectLabel' => '',
			'urlArray' => array('/settings/demo', 'test1'=>'test'),
            'extraUrlArray' => null,
			'keyId' => 'branchId',
		);
	}

	public function beforeAction($action){
        if(!parent::beforeAction($action)){
            return false;
        }

        $this->branchId = null;
		if($this->multipleBranch){
			$branchId = Yii::app()->request->getParam('branchId',null);
			$route = '/'.$this->module->getId().'/'.$this->getId().'/'.$this->selectBranchActionId;
			if( empty($branchId) && $this->selectBranchActionId != $action->getId() ){
                if(empty($this->defaultToUserBranch)){
					$route = '/'.$this->module->getId().'/'.$this->getId().'/'.$this->selectBranchActionId.'?redirectAction='.$action->getId();
                    $this->redirect($route);
                }else{
                    if(!empty(Yii::app()->params['defaultCampusId']) && in_array(Yii::app()->params['defaultCampusId'], $this->adminBranch ) ){
                        $this->branchId = Yii::app()->params['defaultCampusId'];
                    }else{
                        $this->branchId = $this->staff->profile->branch;
                    }
                }
			}
			if( !empty($branchId) && !$this->checkBranchAccess($branchId) ){
				$this->redirect($route);
			}else{
                if(empty($this->branchId)){
                    $this->branchId = $branchId;
                }
            }

		}else{
			$this->branchId = $this->staff->profile->branch;
		}
		$getParam = Yii::app()->request->getParam('branchId',null);
		if( !empty($getParam) && $this->branchId != $getParam ){
			throw new CException('YOU DO NOT HAVE THE PERMISSION');
		}

        $this->branchObj = Branch::model()->findByPk($this->branchId);
		
		return true;
	}
	
	public function checkBranchAccess($branchId){
		//代码
		$resultAccess = false;
		if (!empty($branchId))
		{
			if(in_array($branchId, $this->accessBranch)){
				$resultAccess = true;
			}
		}
		return $resultAccess;
	}
	
	public function accessMultipleBranches(){
        $officeBranch = CommonUtils::LoadConfig('CfgOffice');

		//TODO: 需要加入判断该用户是否在可以管理多所校园的用户名单里
		$myBranch = $this->staff->profile->branch;
		if( in_array($myBranch, $officeBranch) ||  $this->staff->admtype )
		{
			$adminBranch = AdmBranchLink::model()->getMultipleBranches(Yii::app()->user->getId());
			if (in_array($myBranch, $officeBranch))
			{
				$this->accessBranch = array_keys($this->getAllBranch());
				$this->adminBranch = $adminBranch;
			}
			else 
			{
				$this->accessBranch = $adminBranch;
				$this->adminBranch = $adminBranch;
			}
			return true;
		}
		else
			return false;
	}

    public function getCalendars(){
        if(empty($this->calendarYids)){
            $this->calendarYids = array('currentYid'=>null,'nextYid'=>null);
            Yii::import('common.models.calendar.*');
            $crit = new CDbCriteria();
            $crit->compare('branchid', $this->branchId);
            $crit->index = 'startyear';
            $crit->order = 'is_selected DESC';
            $calendars = CalendarSchool::model()->findAll($crit);
            foreach($calendars as $startyear=>$calendar){
                if($calendar->is_selected){
                    $this->calendarYids['currentYid'] = $calendar->yid;
                    $this->calendarStartYear[$calendar->yid] = $startyear;
                    break;
                }
            }
            if( !empty($this->calendarYids['currentYid']) && !empty($calendars[$startyear+1]) ){
                $this->calendarYids['nextYid'] = $calendars[$startyear+1]->yid;
                $this->calendarStartYear[$calendars[$startyear+1]->yid] = $startyear+1;
            }
        }

        return $this->calendarYids;
    }

    public function actionSelect(){
        $this->render('//layouts/common/branchSelect');
    }
}