<?php

class IndexController extends BranchBasedController
{
    public $childName;
    //重写createUrl
    public function createUrl($route, $params = array(), $ampersand = '&', $parentOnly = false)
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    //初始化
    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";
        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Campus Workspace');
        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = false;
        $this->branchSelectParams['urlArray'] = array('//workflow/index/index');
        $cs = Yii::app()->clientScript;
        // $cs->registerCoreScript('jquery.ui');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/xlsx.full.min.js');
        Yii::import('common.models.workflow.*');
    }

    public function actionSelect()
    {
        $criter = new CDbCriteria;
        $criter->compare('t.assign_user', Yii::app()->user->getId());
        $criter->compare('t.current_operator', WorkflowRole::WORKFLOW_ROLE_CURRENT_CHECK_USER);
        $waits = WorkflowRole::model()->with('operation')->findAll($criter);

        foreach ($waits as $wait) {
            if (isset($this->branchSelectParams['branchCount'][$wait->operation->branchid])) {
                $this->branchSelectParams['branchCount'][$wait->operation->branchid]++;
            } else {
                $this->branchSelectParams['branchCount'][$wait->operation->branchid] = 1;
            }
        }
        $this->render('//layouts/common/branchSelect');
    }

    public function actionIndex()
    {
        $access = false;
        foreach (Yii::app()->user->roles as $role) {
            if (in_array($role, array('ivystaff_it', 'ivystaff_cd', 'ivystaff_opschool', 'ivystaff_hr', 'ivystaff_opgeneral', 'ivystaff_tygmgt', 'ivystaff_finance'))) {
                $access = true;
                break;
            }
        }

        $userBranch = $this->staff->profile->branch;
        // 总部报销付款权限
        if (in_array($userBranch, (array)CommonUtils::LoadConfig('CfgOffice'))) {
            $access = true;
        }

        if ($access === false) {
            $this->render('//denied/index');
            Yii::app()->end();
        }


        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/jquery.printThis.js');
        $localPathJs = dirname(__FILE__) . "/../assets/workflow.js";
        $localPathCss = dirname(__FILE__) . "/../assets/workflow.css";
        $publicPathJs = Yii::app()->getAssetManager()->publish($localPathJs, false, -1, YII_DEBUG);
        $publicPathCss = Yii::app()->getAssetManager()->publish($localPathCss, false, -1, YII_DEBUG);
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/underscore-min.js');
        $cs->registerScriptFile($publicPathJs,  CClientScript::POS_END);
        $cs->registerCssFile($publicPathCss);
        if (!Yii::app()->user->checkAccess('ivystaff_it')) {
            $cs->registerCoreScript('jquery.ui');
        }
        $cs->registerCssFile($cs->getCoreScriptUrl() . '/jui/css/base/jquery-ui.css');

        Yii::import('common.models.workflow.*');
        $userIds = array();
        $definationId = 10;
        //等待审核
        $criter = new CDbCriteria;
        $criter->compare('t.assign_user', Yii::app()->user->getId());
        $criter->compare('t.current_operator', WorkflowRole::WORKFLOW_ROLE_CURRENT_CHECK_USER);
        $criter->compare('operation.branchid', $this->branchId);
        $criter->addCondition('operation.defination_id>=' . $definationId);
        $criter->order = 'operation.start_time desc';
        $currentCheckUser = WorkflowRole::model()->with('operation')->findAll($criter);
        if (!empty($currentCheckUser)) {
            foreach ($currentCheckUser as $user) {
                $userIds[$user->operation->start_user] = $user->operation->start_user;
            }
        }
        //与我有关
        $criter = new CDbCriteria;
        $criter->compare('t.assign_user', Yii::app()->user->getId());
        $criter->compare('t.current_operator', WorkflowRole::WORKFLOW_ROLE_CURRENT_CHECKED_USER);
        $criter->compare('operation.state', WorkflowOperation::WORKFLOW_STATS_UNOP);
        $criter->compare('operation.branchid', $this->branchId);
        $criter->addCondition('operation.defination_id>=' . $definationId);
        $criter->order = 'operation.start_time desc';
        $historyCheckUser = WorkflowRole::model()->with('operation')->findAll($criter);
        if (!empty($historyCheckUser)) {
            //去除重复的工作流
            $historyCheck = array();
            foreach ($historyCheckUser as $key => $user) {
                if (in_array($user->operation_id, $historyCheck)) {
                    unset($historyCheckUser[$key]);
                }
                $historyCheck[] = $user->operation_id;
                $userIds[$user->operation->start_user] = $user->operation->start_user;
            }
        }

        $user = Yii::app()->user;
        $uid = Yii::app()->user->getId();
        $allowDefinationId = array();
        $isTYG = in_array($this->branchId, (array)CommonUtils::LoadConfig('CfgOffice'));

        if ($isTYG) {
            // 获取用户有权限的审批付款工作流
            $crit = new CDbCriteria();
            $crit->compare('t.defination_handle', array('PaymentHq', 'ContractHq'));
            $crit->compare('t.status', 1);
            $crit->with = array('workflowNode');
            $definationModelList = WorkflowDefination::model()->findAll($crit);
            // 当前用户管理多校园
            $criter = new CDbCriteria;
            $criter->compare('schoolid', $this->branchId);
            $criter->compare('uid', $uid);
            $AdmModels = AdmBranchLink::model()->findAll($criter);
            $admType = array();
            foreach ($AdmModels as $admModel) {
                $admType[] = $admModel->type;
            }
            foreach ($definationModelList as $definationModel) {
                $userProfile = UserProfile::model()->findByPk($uid);
                $nodeObjList = $definationModel->workflowNode;
                foreach ($nodeObjList as $nodeObj) {
                    $executor = ($nodeObj->executor) ? unserialize($nodeObj->executor) : array();
                    $arrKey = key($executor);
                    $arrVal = current($executor);
                    // 职位
                    if ($arrKey == WorkflowDefination::WORKFLOW_AUTH_SYSTEM_POSITION) {
                        if (in_array($userProfile->occupation_en, explode(',', $arrVal))) {
                            $allowDefinationId[] = $definationModel->defination_id;
                            continue;
                        }
                    } elseif ($arrKey == WorkflowDefination::WORKFLOW_AUTH_SYSTEM_GROUP) {
                        if (in_array($arrVal, $admType)) {
                            $allowDefinationId[] = $definationModel->defination_id;
                            continue;
                        }
                    }
                }
            }
        }

        //审核通过
        $criteria = new CDbCriteria;
        $criteria->compare('branchid', $this->branchId);
        $criteria->compare('state', WorkflowOperation::WORKFLOW_STATS_OPED);
        $criteria->compare('defination_id', '>=' . $definationId);
        if ($isTYG) {
            if ($allowDefinationId) {
                $allowDefinationIdStr = implode(',', $allowDefinationId);
                $criteria->addCondition("start_user <> $uid and defination_id in ($allowDefinationIdStr)");
            } else {
                $criteria->compare('defination_id', 0);
            }
        }
        $endWorkflow = new CActiveDataProvider(
            new WorkflowOperation(),
            array(
                'criteria' => $criteria,
                'sort' => array('defaultOrder' => 't.end_time DESC'),
            )
        );

        $nodesList = WorkflowNode::model()->getAllNodesList();
        //拒绝
        $criteria = new CDbCriteria;
        $criteria->compare('branchid', $this->branchId);
        $criteria->compare('state', WorkflowOperation::WORKFLOW_STATS_OPDENY);
        $criteria->compare('defination_id', '>=' . $definationId);
        if ($isTYG) {
            if ($allowDefinationId) {
                $allowDefinationIdStr = implode(',', $allowDefinationId);
                $criteria->addCondition("start_user = $uid or defination_id in ($allowDefinationIdStr)");
            } else {
                $criteria->compare('start_user', $uid);
            }
        }

        $rejectWorkflow = new CActiveDataProvider(
            new WorkflowOperation(),
            array(
                'criteria' => $criteria,
                'sort' => array('defaultOrder' => 't.end_time DESC'),
            )
        );
        // 我申请的工作流
        $selfWorkflow = NULL;
        if ($isTYG) {
            $criteria = new CDbCriteria;
            $criteria->compare('branchid', $this->branchId);
            $criteria->compare('state', WorkflowOperation::WORKFLOW_STATS_OPED);
            $criteria->compare('defination_id', '>=' . $definationId);
            $criteria->compare('start_user', $uid);
            $selfWorkflow = new CActiveDataProvider(
                new WorkflowOperation(),
                array(
                    'criteria' => $criteria,
                    'sort' => array('defaultOrder' => 't.end_time DESC'),
                )
            );
        }
        // 进行中的所有工作流
        $criteria = new CDbCriteria;
        $criteria->compare('branchid', $this->branchId);
        $criteria->compare('state', WorkflowOperation::WORKFLOW_STATS_UNOP);
        $criteria->compare('defination_id', '>=' . $definationId);
        $criteria->group = 'start_time DESC';
        if ($isTYG) {
            if ($allowDefinationId) {
                $allowDefinationIdStr = implode(',', $allowDefinationId);
                $criteria->addCondition("start_user = $uid or defination_id in ($allowDefinationIdStr)");
            } else {
                $criteria->compare('start_user', $uid);
            }
        }
        $processingList = WorkflowOperation::model()->findAll($criteria);

        //config
        $branchList = Branch::model()->getBranchList();

        $criter = new CDbCriteria;
        $criter->compare('t.assign_user', Yii::app()->user->getId());
        $criter->compare('t.current_operator', WorkflowRole::WORKFLOW_ROLE_CURRENT_CHECK_USER);
        $waits = WorkflowRole::model()->with('operation')->findAll($criter);
        foreach ($waits as $wait) {
            if (!isset($wait->operation)) {
                continue;
            }
            $userIds[$wait->operation->start_user] = $wait->operation->start_user;
            if (isset($this->branchSelectParams['branchCount'][$wait->operation->branchid])) {
                $this->branchSelectParams['branchCount'][$wait->operation->branchid]++;
            } else {
                $this->branchSelectParams['branchCount'][$wait->operation->branchid] = 1;
            }
        }
        $usersList = array();
        if (count($userIds)) {
            $userListModel = User::model()->with('profile')->findAllByPk($userIds);
            foreach ($userListModel as $value) {
                $usersList[$value->uid] = $value->getName();
            }
            unset($userIds);
            unset($userListModel);
        }
        $this->render('index', array(
            'currentCheckUser' => $currentCheckUser,
            'nodesList' => $nodesList,
            'historyCheckUser' => $historyCheckUser,
            'endWorkflow' => $endWorkflow,
            'selfWorkflow' => $selfWorkflow,
            'branchList' => $branchList,
            'rejectWorkflow' => $rejectWorkflow,
            'usersList' => $usersList,
            'processingList' => $processingList,
        ));
    }

    public function showWorkflow($data)
    {
        $nodesList = WorkflowNode::model()->getAllNodesList();
        $config = WorkflowConfig::getType($data->operation_type, $data->operation_object_id);
        $title = $nodesList[$data->defination_id]['name'];
        if (in_array($nodesList[$data->defination_id]['obj']->defination_handle, array('Payment', 'Contract'))) {
            $title = sprintf("%s#%s", $title, $data->id);
        }

        if (in_array($nodesList[$data->defination_id]['obj']->defination_handle, array('PaymentHq', 'ContractHq'))) {
            $title = sprintf("%s / %s / %s", $title, $data->userInfo->getNameLang(), date('Y-m-d', $data->start_time));
        }

        echo
        CHtml::link(
            (isset($config['function']) ? $config['function'] : '') . $title,
            Yii::app()->createUrl("//workflow/entrance/index", array(
                "definationId" => $data->defination_id,
                "nodeId" => $data->current_node_index,
                "operationId" => $data->id,
                "branchId" => $data->branchid,
                "action" => "show",
            )),
            array("class" => "J_ajax", "data-method" => "get")
        );
    }

    public function actionWorkflowCount()
    {
        Yii::import('common.models.workflow.payment.PaymentApply');
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/jquery.printThis.js');
        $localPathJs = dirname(__FILE__) . "/../assets/workflow.js";
        $localPathCss = dirname(__FILE__) . "/../assets/workflow.css";
        $publicPathJs = Yii::app()->getAssetManager()->publish($localPathJs, false, -1, YII_DEBUG);
        $publicPathCss = Yii::app()->getAssetManager()->publish($localPathCss, false, -1, YII_DEBUG);

        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/underscore-min.js');
        $cs->registerScriptFile($publicPathJs,  CClientScript::POS_END);
        $cs->registerCssFile($publicPathCss);
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl() . '/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/jquery.printThis.js');

        $this->branchSelectParams['urlArray'] = array('//workflow/index/workflowCount');

        $wordDlowObj = Null;
        $definationModel = Null;
        $definationId = Yii::app()->request->getParam('definationId', '');
        $isTYG = in_array($this->branchId, (array)CommonUtils::LoadConfig('CfgOffice'));
        if (!$isTYG || Yii::app()->user->checkAccess('ivystaff_finance')) {
            $criter = new CDbCriteria;
            $criter->compare('branch_type', $this->branchObj->type);
            $criter->compare('defination_id', ">10");
            $criter->compare('status', 1);
            // 非泉发时，过滤掉泉发专用退费流程
            if ($this->branchId != 'BJ_QFF') {
                $criter->compare('defination_id', '<>2010');
            }
            $wordDlowObj = WorkflowDefination::model()->findAll($criter);
            foreach ($wordDlowObj as $key => $model) {
                if ($isTYG && in_array($model->defination_handle, array('PaymentHq', 'ContractHq')) &&  $model->display == 1) {
                    unset($wordDlowObj[$key]);
                }
                if ($model->defination_id == $definationId) {
                    $definationModel = $model;
                }
            }
        }

        $url = '';

        $state = Yii::app()->request->getParam('state', '');
        $exte3 = Yii::app()->request->getParam('exte3', '');
        $userName = Yii::app()->request->getParam('userName', '');
        $start_time = Yii::app()->request->getParam('start_time', '');
        $end_time = Yii::app()->request->getParam('end_time', '');
        $workList = array(
            'definationId' => $definationModel ? $definationId : 0,
            'state' => $state,
            'exte3' => $exte3,
            'userName' => $userName,
            'start_time' => $start_time,
            'end_time' => $end_time,
        );
        if (in_array($definationId, array(12, 1800))) {
            $dataProvider = $this->workflow($workList);
            $childid = array();
            foreach ($dataProvider->getData() as $val) {

                $childid[] = $val->operation_object_id;
            }

            $childObj = ChildProfileBasic::model()->findAllByPk($childid);

            if ($childObj) {
                foreach ($childObj as $item) {
                    $this->childName[$item->childid] = $item->getChildName();
                }
            }
            $url = $this->createUrl('exportRefund', array(
                'state' => $state,
                'exte3' => $exte3,
                'userName' => $userName,
                'start_time' => $start_time,
                'end_time' => $end_time,
                'definationId' => $definationId,
            ));

            $this->render('refundWorkflow', array(
                'wordDlowObj' => $wordDlowObj,
                'dataProvider' => $dataProvider,
                'branchId' => $this->branchId,
                'definationId' => $definationId,
                'url' => $url,
            ));
        } elseif ($definationModel && in_array($definationModel->defination_handle, array('Payment', 'PaymentHq'))) {
            $stateList = array(1 => '待申请',  2 => '待付款', 3 => '已付款');
            $dateList = array( 1 => '当天付款', 2 => '本周付款', 3 => '当月付款');

            $currentWorkflowId = Yii::app()->request->getParam('currentWorkflowId', '');
            $currentState = Yii::app()->request->getParam('currentState', '');
            $currentDate = Yii::app()->request->getParam('currentDate', '');
            if ($currentDate >= 1) {
                $currentState = '';
            }
            // 查找当前学校所有的付款类型
            $paymentWorkflowList = array();
            foreach ($wordDlowObj as $model) {
                if (in_array($model->defination_handle, array('Payment', 'PaymentHq'))) {
                    $paymentWorkflowList[$model->defination_id] = $model->defination_name_cn;
                }
            }

            $paymentWorkflowIds = array_keys($paymentWorkflowList);
            $workList['title'] = Yii::app()->request->getParam('title', '');
            $dataProvider = $this->paymentList($workList, $paymentWorkflowIds, $currentWorkflowId, $currentState, $currentDate);
            $this->render('paymentList', array(
                'wordDlowObj' => $wordDlowObj,
                'dataProvider' => $dataProvider,
                'definationId' => $definationId,
                'currentWorkflowId' => $currentWorkflowId,
                'paymentWorkflowList' => $paymentWorkflowList,
                'branchId' => $this->branchId,
                'stateList' =>$stateList,
                'dateList' =>$dateList,
                'currentState' =>$currentState,
                'currentDate' =>$currentDate,
            ));

        } elseif ($definationModel && in_array($definationModel->defination_handle, array('Contract', 'ContractHq'))){
            $dataProvider = $this->contractList($workList);
            $this->render('contractList', array(
                'wordDlowObj' => $wordDlowObj,
                'dataProvider' => $dataProvider,
                'definationId' => $definationId,
                'branchId' => $this->branchId,
                'categoryList' => ContractApply::getCategoryList(),
                'cycleList' => ContractApply::getCycleList(),
            ));
        } else {
            $dataProvider = $this->workflow($workList);
            $this->render('workflowList', array(
                'wordDlowObj' => $wordDlowObj,
                'dataProvider' => $dataProvider,
                'definationId' => $definationId,
                'branchId' => $this->branchId,
            ));
        }
    }

    public function contractList($workList) {
        Yii::import('common.models.workflow.contract.ContractApply');
        $model = new WorkflowOperation();
        $crit = new CDbCriteria;
        $crit->compare('defination_id', $workList['definationId']);
        $crit->compare('branchid', $this->branchId);
        $crit->compare('t.state', WorkflowOperation::WORKFLOW_STATS_OPED);
        $crit->with = array('contract');
        $dataProvider = new CActiveDataProvider($model, array(
            'criteria' => $crit,
            'sort' => array(
                'defaultOrder' => 'start_time DESC',
            ),
            'pagination' => array(
                'pageSize' => 30,
            ),
        ));

        return $dataProvider;
    }

    public function showContractTitle($data)
    {        
        $title = $data->contract->title;
        echo CHtml::link(
            $title,
            Yii::app()->createUrl("//workflow/entrance/index", array(
                "definationId" => $data->defination_id,
                "nodeId" => $data->current_node_index,
                "operationId" => $data->id,
                "branchId" => $data->branchid,
                "action" => "show",
            )),
            array("class" => "J_ajax", "data-method" => "get")
        );
    }

    public function showContractPaymentPrice($data) {
        // 查找已付金额
        $paidData = ContractApply::getPaymentList($data->contract->id);
        echo number_format($paidData['total'], 2);
    }

    
    public function showContractButton($data)
    {
        if ($data->contract->state == ContractApply::CANCEL) {
            echo "<label class='label label-danger'>已作废</label>";
            return;
        }
        $paidData = ContractApply::getPaymentList($data->contract->id);
        if ($data->contract->category == 2 || $data->contract->price_total > bcmul($paidData['total'], 100)) {
            echo CHtml::link(
                '发起付款',
                Yii::app()->createUrl("//workflow/entrance/index", array(
                    "definationId" => $data->contract->payment_flow_id,
                    "contractId" => $data->contract->id,
                    "branchId" => $data->branchid,
                    "action" => "initi",
                )),
                array("class" => "btn btn-primary btn-xs J_ajax", "data-method" => "get")
            );
        }
    }

    public function workflow($workList)
    {
        $model = new WorkflowOperation();

        $crit = new CDbCriteria;
        $childObj = array();

        if ($workList['userName']) {
            $criteria = new CDbCriteria;
            $criteria->addCondition("concat_ws('',name_cn,first_name_en,middle_name_en,last_name_en) like '%{$workList['userName']}%' ");
            $criteria->index = "childid";
            $childObj = ChildProfileBasic::model()->findAll($criteria);
        }

        if ($childObj) {
            $crit->compare('operation_object_id', array_keys($childObj));
        }
        if ($workList['state']) {
            $crit->compare('state', $workList['state']);
        }
        if ($workList['exte3']) {
            $crit->compare('exte3', $workList['exte3']);
        }
        if ($workList['start_time']) {
            $statr = strtotime($workList['start_time']);
            $end = strtotime($workList['start_time']) + 86400;
            $crit->compare('start_time', ">={$statr}");
            if ($workList['start_time']) {
                $end = (strtotime($workList['end_time']) == $statr) ? strtotime($workList['end_time']) + 86400 : strtotime($workList['end_time']);
            }
            $crit->compare('start_time', "<{$end}");
        }
        $crit->compare('defination_id', $workList['definationId']);
        $crit->compare('branchid', $this->branchId);
        $crit->compare('state', array(WorkflowOperation::WORKFLOW_STATS_UNOP, WorkflowOperation::WORKFLOW_STATS_OPDENY, WorkflowOperation::WORKFLOW_STATS_OPED));
        $dataProvider = new CActiveDataProvider($model, array(
            'criteria' => $crit,
            'sort' => array(
                'defaultOrder' => 'start_time DESC',
            ),
            'pagination' => array(
                'pageSize' => 20,
            ),
        ));

        return $dataProvider;
    }

    public function paymentList($workList, $paymentWorkflowIds, $currentWorkflowId, $currentState, $currentDate)
    {
        Yii::import('common.models.workflow.payment.PaymentApply');
        $model = new WorkflowOperation();
        $crit = new CDbCriteria;
        if ($workList['state']) {
            $crit->compare('state', $workList['state']);
        }
        if ($workList['exte3']) {
            $crit->compare('exte3', $workList['exte3']);
        }
        if ($workList['title']) {
            if (strpos($workList['title'], '#') === 0) {
                $id = ltrim($workList['title'], '#');
                $crit->compare('t.id', $id);
            } else {
                $crit->compare('payment.title', $workList['title'], true);
            }
        }
        if ($workList['start_time']) {
            $statr = strtotime($workList['start_time']);
            $end = strtotime($workList['start_time']) + 86400;
            $crit->compare('start_time', ">={$statr}");
            if ($workList['start_time']) {
                $end = (strtotime($workList['end_time']) == $statr) ? strtotime($workList['end_time']) + 86400 : strtotime($workList['end_time']);
            }
            $crit->compare('start_time', "<{$end}");
        }

        if ($currentWorkflowId) {
            $crit->compare('defination_id', $currentWorkflowId);
        } else {
            $crit->compare('defination_id', $paymentWorkflowIds);
        }
        if ($currentState) {
            if ($currentState == 1) {
                $crit->compare('payment.apply_state', 0);
                $crit->addCondition('payment.payment_user IS NULL');
            }
            if ($currentState == 2) {
                $crit->compare('payment.apply_state', 1);
                $crit->addCondition('payment.payment_user IS NULL');
            }
            if ($currentState == 3) {
                // $crit->compare('payment.apply_state', 1);
                $crit->compare('payment.payment_user', '>0');
            }
        }

        if ($currentDate) {
            $crit->compare('payment.apply_state', 1);
            $crit->addCondition('payment.payment_user IS NULL');
            // 获取当前日期
            $dateTime = new DateTime();
            if ($currentDate == 1) {
                // 获取今天开始的时间戳（00:00:00）
                $dateTime->setTime(0, 0, 0);
                $startTimestamp = $dateTime->getTimestamp();
                // 获取今天结束的时间戳（23:59:59）
                $dateTime->setTime(23, 59, 59);
                $endTimestamp = $dateTime->getTimestamp();
            }
            if ($currentDate == 2) {
                // 获取当周开始的时间戳（周一 00:00:00）
                $dateTime->modify('monday this week');
                $dateTime->setTime(0, 0, 0);
                $startTimestamp = $dateTime->getTimestamp();
                // 获取当周结束的时间戳（周日 23:59:59）
                $dateTime->modify('sunday this week');
                $dateTime->setTime(23, 59, 59);
                $endTimestamp = $dateTime->getTimestamp();
            }
            if ($currentDate == 3) {
                // 获取当月开始的时间戳（1号 00:00:00）
                $dateTime->modify('first day of this month');
                $dateTime->setTime(0, 0, 0);
                $startTimestamp = $dateTime->getTimestamp();
                // 获取当月结束的时间戳（最后一天 23:59:59）
                $dateTime->modify('last day of this month');
                $dateTime->setTime(23, 59, 59);
                $endTimestamp = $dateTime->getTimestamp();
            }
            $crit->compare('payment.apply_time', '>=' . $startTimestamp);
            $crit->compare('payment.apply_time', '<=' . $endTimestamp);
        }

        $crit->compare('branchid', $this->branchId);
        $crit->compare('t.state', WorkflowOperation::WORKFLOW_STATS_OPED);
        $crit->with = array('payment');
        $dataProvider = new CActiveDataProvider($model, array(
            'criteria' => $crit,
            'sort' => array(
                'defaultOrder' => 'start_time DESC',
            ),
            'pagination' => array(
                'pageSize' => 30,
            ),
        ));

        return $dataProvider;
    }

    public function getButton($data)
    {
        $uid = Yii::app()->user->getId();
        $confirmBatchId = $data->payment->confirm_batch_id;
        if ($data->payment->payment_user > 0 && $confirmBatchId > 0) {
            echo " <button class='btn btn-xs btn-primary' onclick='viewBatch(" . $confirmBatchId . ")'>查看</button>";
        }
        if ($data->payment->apply_state == 1 && $data->payment->apply_user == $uid && $data->payment->payment_user <= 0) {
            echo " <button class='btn btn-xs btn-primary' onclick='cancelApply(" . $data->operation_object_id . ")'>取消</button>";
        }
        // if ($data->payment->apply_state == 1 && $data->payment->apply_user == $uid && $data->payment->payment_user <= 0 && $data->payment->apply_batch_id > 0) {
        //     echo " <button class='btn btn-xs btn-primary' onclick='viewApply(" . $data->payment->apply_batch_id . ")'>取消</button>";
        // }
        if ($this->confirmPermission() && $data->payment->payment_user > 0) {
            echo " <button class='btn btn-xs btn-primary' onclick='showNote(" . $data->operation_object_id . ")'>备注</button>";
        }
    }

    // 导出退费
    public function actionExportRefund()
    {
        $state = Yii::app()->request->getParam('state', '');
        $exte3 = Yii::app()->request->getParam('exte3', '');
        $userName = Yii::app()->request->getParam('userName', '');
        $start_time = Yii::app()->request->getParam('start_time', '');
        $end_time = Yii::app()->request->getParam('end_time', '');
        $definationId = Yii::app()->request->getParam('definationId', '');


        Yii::import('common.models.invoice.InvoiceChildRefund');
        Yii::import('common.models.invoice.Invoice');

        $crit = new CDbCriteria;
        $childObj = array();

        if ($userName) {
            $criteria = new CDbCriteria;
            $criteria->addCondition("concat_ws('',name_cn,first_name_en,middle_name_en,last_name_en) like '%{$userName}%' ");
            $criteria->index = "childid";
            $childObj = ChildProfileBasic::model()->findAll($criteria);
        }

        if ($childObj) {
            $crit->compare('operation_object_id', array_keys($childObj));
        }
        if ($state) {
            $crit->compare('state', $state);
        }
        if ($exte3) {
            $crit->compare('exte3', $exte3);
        }
        if ($start_time) {
            $statr = strtotime($start_time);
            $end = strtotime($start_time) + 86400;
            $crit->compare('start_time', ">={$statr}");
            if ($end_time) {
                $end = (strtotime($end_time) == $statr) ? strtotime($end_time) + 86400 : strtotime($end_time);
            }
            $crit->compare('start_time', "<{$end}");
        }
        $crit->compare('defination_id', $definationId);
        $crit->compare('branchid', $this->branchId);
        $crit->compare('state', array(WorkflowOperation::WORKFLOW_STATS_UNOP, WorkflowOperation::WORKFLOW_STATS_OPDENY, WorkflowOperation::WORKFLOW_STATS_OPED));
        $model = WorkflowOperation::model()->findAll($crit);

        $filename = $start_time . ' - ' . $end_time . '导出退费、退学名单';
        $data = array();
        $data['title'] = $filename . ".xlsx";
        $data['items'][0] = array('孩子ID', '孩子姓名', '生日', '提交说明', '退费方式', '退费状态', '增加退费时间', '总钱数', '退费开始时间', '退费结束时间');
        if ($model) {
            foreach ($model as $i => $visit) {
                // 提交人描述
                $criter = new CDbCriteria;
                $criter->compare('operation_id', $visit->id);
                $criter->limit = 1;
                $criter->order = 'start_time ASC';
                $process = WorkflowProcess::model()->find($criter);

                $childName = ChildProfileBasic::model()->findByPk($visit->operation_object_id);

                $criter = new CDbCriteria;
                $criter->compare('operation_id', $visit->id);
                $criter->index = 'operation_object_id';
                $operationDetail = WorkflowOperationDetail::model()->findAll($criter);
                $refundList = InvoiceChildRefund::model()->with('Invoice')->findAllByPk(array_keys($operationDetail));

                $amount = array();
                $amountNum = 0;
                $startdate = array();
                $enddate = array();
                foreach ($refundList as $item) {
                    $startdate[] = date("Y-m-d", $item->startdate);
                    $enddate[] = date("Y-m-d", $item->enddate);
                    $amount[] = $item->amount;
                    $amountNum += $item->amount;
                }

                if (count($amount) == 1) {
                    $price = implode(' + ', $amount);
                } else {
                    $price = implode(' + ', $amount) . ' = ' . $amountNum;
                }

                $configWay = WorkflowOperation::getWay();
                $configStatus = WorkflowOperation::getConfig();
                $array = array();
                $array[] = $visit->operation_object_id;
                $array[] = $childName->getChildName();
                $array[] = $childName->birthday_search;
                $array[] = ($process->process_desc) ? nl2br($process->process_desc) : "";
                $array[] = $configWay[$visit->exte3];
                $array[] = $configStatus[$visit->state];
                $array[] = date("Y-m-d", $visit->start_time);
                $array[] = $price;
                $array[] = implode('/', $startdate);
                $array[] = implode('/', $enddate);
                $data['items'][$i + 1] = $array;
            }
        }
        $this->addMessage('state', 'success');
        $this->addMessage('data', $data);
        $this->showMessage();
    }

    public function getAmount($data)
    {
        $action = "show";
        Yii::import('application.components.workflow.*');
        $workflow = new WorkflowApi($data->defination_id, $data->current_node_index, $data->id, $data->branchid);

        $plugClass = ucfirst($workflow->definationObj->defination_handle);
        Yii::import('application.modules.workflow.plug.' . $plugClass);


        $plug  = new $plugClass();
        $plug->workflow = $workflow;
        $plug->controller = $this;
        $ret = $plug->$action();
        $amount = array();
        $amountNum = 0;
        foreach ($ret['refundList'] as $item) {
            $amount[] = $item->amount;
            $amountNum += $item->amount;
        }
        if (count($amount) == 1) {
            $price = implode(' + ', $amount);
        } else {
            $price = implode(' + ', $amount) . ' = ' . $amountNum;
        }

        return $price;
    }

    public function getChildName($data)
    {
        return $this->childName[$data->operation_object_id];
    }

    public function getWays($data)
    {
        $config = WorkflowOperation::getConfig();
        return $config[$data->state];
    }

    public function getExte3($data)
    {
        $config = WorkflowOperation::getWay();
        return $config[$data->exte3];
    }

    /**
     * 为工作流当前审核节点添加审核人
     *
     * @return void
     */
    public function actionCurrentUser()
    {
        $operationid = Yii::app()->request->getParam('operationid');
        if (!$operationid) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '参数错误');
            $this->showMessage();
        }
        // 查找最后催办的时间
        $operationModel = WorkflowOperation::model()->findByPk($operationid);
        if (!$operationModel) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '工作流未找到');
            $this->showMessage();
        }
        $uid = Yii::app()->user->getId();

        $notifyTime = $operationModel->notify_time;
        if (!$notifyTime) {
            $crit = new CDbCriteria();
            $crit->compare('operation_id', $operationid);
            $crit->order = 'updatetime DESC';
            $processModel = WorkflowProcess::model()->find($crit);
            if ($processModel) {
                $notifyTime = $processModel->updatetime;
            }
        }

        $canNotify = $uid == $operationModel->start_user && $notifyTime > 0 && $notifyTime < time() - 1 * 43200;
        // 查找当前审批人
        $userList = WorkflowOperation::getCurrentUser($operationid);

        if (Yii::app()->request->isPostRequest) {
            if ($uid != $operationModel->start_user) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '无权限');
                $this->showMessage();
            }
            if ($operationModel->state != WorkflowOperation::WORKFLOW_STATS_UNOP) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '工作流状态错误');
                $this->showMessage();
            }
            if ($notifyTime > 0 && $notifyTime > time() - 1 * 43200) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '催办间隔时间太短');
                $this->showMessage();
            }
            Yii::import('application.components.workflow.*');
            $res = WorkflowApi::notify($operationid, $operationModel->current_node_index);
            if ($res['code'] == 0) {
                $operationModel->notify_time = time();
                $operationModel->save();
                $this->addMessage('state', 'success');
                $this->addMessage('message', $res['msg']);
                $this->addMessage('data', $res['data']);
            } else {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', $res['msg']);
                $this->addMessage('data', $res['data']);
            }
        }

        $this->addMessage('state', 'success');
        $this->addMessage('data', array(
            'userList' => $userList,
            'canNotify' => $canNotify,
            'notifyDate' => date('Y-m-d H:i:s', $notifyTime),
        ));
        $this->addMessage('message', 'success');
        $this->showMessage();
    }

    /**
     * 为工作流当前审核节点添加审核人
     *
     * @return void
     */
    public function actionAddUser()
    {
        $this->checkActionAccess('ivystaff_it');

        $uid = Yii::app()->request->getParam('uid');
        $operationid = Yii::app()->request->getParam('operationid');
        if (!$uid || !$operationid) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '参数错误');
            $this->showMessage();
        }
        $crit = new CDbCriteria();
        $crit->compare('operation_id', $operationid);
        $crit->compare('current_operator', 1);
        $models = WorkflowRole::model()->findAll($crit);
        if (!$models) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '参数错误');
            $this->showMessage();
        }
        foreach ($models as $item) {
            if ($item->assign_user == $uid) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '添加用户已存在');
                $this->showMessage();
            }
        }
        $model = $models[0];
        $newModel = new WorkflowRole();
        $newModel->setAttributes($model->getAttributes());
        $newModel->assign_user = $uid;
        $newModel->timestamp = time();
        $newModel->save();
        $this->addMessage('state', 'success');
        $this->addMessage('message', '添加成功');
        $this->showMessage();
    }

    function actionSearchVendor($term)
    {
        Yii::import('common.models.workflow.payment.PaymentVendor');
        // $term = Yii::app()->request->getPost('term');
        $schoolId = Yii::app()->request->getPost('branchId');
        $sep = '%';
        $limit = 10;
        $source = array();
        $criteria = new CDbCriteria;
        $criteria->limit = $limit;
        $criteria->compare("bank_user", $term, true);
        $founds = PaymentVendor::model()->findAll($criteria);

        $i = 0;

        if ($founds) {
            foreach ($founds as $found) {
                $source[] = array(
                    'label' => $found->bank_user . ' ' . $found->bank_account,
                    'value' => $found->bank_user,
                    'vendor_id' => $found->id,
                    'bank_user' => $found->bank_user,
                    'bank_name' => $found->bank_name,
                    'bank_account' => $found->bank_account,
                );
                $i++;
            }
        } else {
            $source[] = array(
                'label' => Yii::t('message', 'Sorry, no match found!'),
                'value' => 0,
            );
        }
        if ($limit == $i) {
            $source[] = array(
                'label' => Yii::t('message', '查询结果只显示:limit条信息，其余已被隐藏，请精确条件后再查询。', array(':limit' => $limit)),
                'value' => -2,
            );
        }

        echo CJSON::encode($source);
    }

    function disabled($data)
    {
        $applyPermission = $this->applyPermission();
        $confirmPermission = $this->confirmPermission();

        // 没有申请和确认的权限
        if (!$applyPermission && !$confirmPermission) {
            return true;
        }

        // 已付款
        if ($data->payment->payment_user > 0) {
            return true;
        }

        if ($confirmPermission) {
            return false;
        }

        // 已锁定
        if ($data->payment->apply_state > 1) {
            return true;
        }

        if ($applyPermission) {
            return false;
        }
        return true;
    }

    function applyPermission()
    {
        return Yii::app()->user->checkAccess('ivystaff_finance');
    }

    function confirmPermission()
    {
        return Yii::app()->user->checkAccess('ivystaff_finance') && in_array($this->staff->profile->branch, (array)CommonUtils::LoadConfig('CfgOffice'));
    }

    function superPermission()
    {
        return Yii::app()->user->checkAccess('ivystaff_it') || Yii::app()->user->getId() == 8001787;
    }

    // 付款申请
    function actionPaymentApply()
    {
        if ($this->applyPermission() == false) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '没有权限');
            $this->showMessage();
        }
        $operationIdList = Yii::app()->request->getParam('operationIdList');
        if (!is_array($operationIdList) || !$operationIdList) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '参数错误');
            $this->showMessage();
        }
        $applyTime = Yii::app()->request->getParam('applyTime');
        $applyNote = Yii::app()->request->getParam('applyNote');
        if ($applyTime == "" || !strtotime($applyTime)) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '到账时间参数错误');
            $this->showMessage();
        }
        $uid = Yii::app()->user->getId();
        // 查找对应的工作流
        $crit = new CDbCriteria();
        $crit->compare('id', $operationIdList);
        $crit->compare('state', WorkflowOperation::WORKFLOW_STATS_OPED);
        $operationModelList = WorkflowOperation::model()->findAll($crit);
        if (count($operationIdList) != count($operationModelList)) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '选中工作流有的状态未完成');
            $this->showMessage();
        }
        $paymentIdList = array();
        foreach ($operationModelList as $operationModel) {
            $paymentIdList[] = $operationModel->operation_object_id;
            $paymentInfo[$operationModel->operation_object_id] = array(
                'operationId' => $operationModel->id,
                'start_user' => $operationModel->start_user,
            );
        }
        Yii::import('common.models.workflow.payment.PaymentApply');
        $crit = new CDbCriteria();
        $crit->compare('id', $paymentIdList);
        $paymentModelList = PaymentApply::model()->findAll($crit);
        foreach ($paymentModelList as $item) {
            if ($item->apply_state == 1) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '数据发生变化');
                $this->showMessage();
            }
        }
        $maxId = 0;
        foreach ($paymentModelList as $paymentModel) {
            if ($paymentModel->id > $maxId) {
                $maxId = $paymentModel->id;
            }
        }
        foreach ($paymentModelList as $paymentModel) {
            if ($paymentModel->id > $maxId) {
                $maxId = $paymentModel->id;
            }
            $paymentModel->apply_user = $uid;
            $paymentModel->apply_time = strtotime($applyTime);
            $paymentModel->apply_note = $applyNote;
            $paymentModel->apply_state = 1;
            $paymentModel->apply_batch_id = $maxId;
            $paymentModel->save();
        }

        $this->addMessage('state', 'success');
        $this->addMessage('message', 'success');
        $this->showMessage();
    }

    function actionCancelApply()
    {
        if ($this->applyPermission() == false) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '没有权限');
            $this->showMessage();
        }
        $paymentIdList = Yii::app()->request->getParam('paymentIdList');
        if (!is_array($paymentIdList) || !$paymentIdList) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '参数错误');
            $this->showMessage();
        }

        $uid = Yii::app()->user->getId();

        Yii::import('common.models.workflow.payment.PaymentApply');
        $crit = new CDbCriteria();
        $crit->compare('id', $paymentIdList);
        $crit->compare('apply_state', 1);
        $crit->compare('apply_user', $uid);
        $paymentModelList = PaymentApply::model()->findAll($crit);
        if (count($paymentIdList) != count($paymentModelList)) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '数据发生变化');
            $this->showMessage();
        }

        foreach ($paymentModelList as $paymentModel) {
            $paymentModel->apply_user = NULL;
            $paymentModel->apply_state = 0;
            $paymentModel->apply_time = NULL;
            $paymentModel->apply_note = NULL;
            $paymentModel->apply_batch_id = NULL;
            $paymentModel->save();
        }

        $this->addMessage('state', 'success');
        $this->addMessage('message', 'success');
        $this->showMessage();
    }

    function actionConfirmBatchData()
    {
        if ($this->confirmPermission() == false) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '没有权限');
            $this->showMessage();
        }
        $comfirmBatchId = Yii::app()->request->getParam('confirm_batch_id');
        if (!$comfirmBatchId) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '参数错误');
            $this->showMessage();
        }

        Yii::import('common.models.workflow.payment.PaymentApply');
        $crit = new CDbCriteria();
        $crit->compare('confirm_batch_id', $comfirmBatchId);
        $paymentModelList = PaymentApply::model()->findAll($crit);
        if (!$paymentModelList) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '数据错误');
            $this->showMessage();
        }

        $itemData = array();
        $totalAmount = 0;
        foreach ($paymentModelList as $paymentModel) {
            $amount = round($paymentModel->price_total / 100, 2);
            $totalAmount += $amount;
            $itemData[] = array(
                'title' => trim($paymentModel->title),
                'bank_name' => trim($paymentModel->bank_name),
                'bank_account' => trim($paymentModel->bank_account),
                'bank_user' => trim($paymentModel->bank_user),
                'amount' => $amount,
            );
        }
        
        $paymentDate = $paymentModelList[0]->payment_time;
        $paymentUser = User::model()->findByPk($paymentModelList[0]->apply_user);
        $returnData = array(
            'payment_total' => $totalAmount,
            'payment_date' => date('Y-m-d', $paymentDate),
            'payment_user' => $paymentUser->getName(),
            'items' => $itemData,
        );

        $this->addMessage('state', 'success');
        $this->addMessage('data', $returnData);
        $this->addMessage('message', 'success');
        $this->showMessage();
    }

    function actionApplyBatchData()
    {
        if ($this->applyPermission() == false) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '没有权限');
            $this->showMessage();
        }
        $applyBatchId = Yii::app()->request->getParam('apply_batch_id');
        if (!$applyBatchId) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '参数错误');
            $this->showMessage();
        }

        Yii::import('common.models.workflow.payment.PaymentApply');
        $crit = new CDbCriteria();
        $crit->compare('apply_batch_id', $applyBatchId);
        $paymentModelList = PaymentApply::model()->findAll($crit);
        if (!$paymentModelList) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '数据错误');
            $this->showMessage();
        }

        $itemData = array();
        $totalAmount = 0;
        foreach ($paymentModelList as $paymentModel) {
            $amount = round($paymentModel->price_total / 100, 2);
            $totalAmount += $amount;
            $itemData[] = array(
                'title' => trim($paymentModel->title),
                'bank_name' => trim($paymentModel->bank_name),
                'bank_account' => trim($paymentModel->bank_account),
                'bank_user' => trim($paymentModel->bank_user),
                'amount' => $amount,
            );
        }
        
        $applyDate = $paymentModelList[0]->apply_time;
        $applyUser = User::model()->findByPk($paymentModelList[0]->apply_user);
        $returnData = array(
            'apply_total' => $totalAmount,
            'apply_date' => date('Y-m-d', $applyDate),
            'apply_user' => $applyUser->getName(),
            'items' => $itemData,
        );

        $this->addMessage('state', 'success');
        $this->addMessage('data', $returnData);
        $this->addMessage('message', 'success');
        $this->showMessage();
    }

    function actionPaymentConfirm()
    {
        if ($this->confirmPermission() == false) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '没有权限');
            $this->showMessage();
        }
        $operationIdList = Yii::app()->request->getParam('operationIdList');
        if (!is_array($operationIdList) || !$operationIdList) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '参数错误');
            $this->showMessage();
        }
        $paymentTime = Yii::app()->request->getParam('paymentTime');
        $paymentNote = Yii::app()->request->getParam('paymentNote');
        if ($paymentTime == "" || !strtotime($paymentTime)) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '到账时间参数错误');
            $this->showMessage();
        }
        $uid = Yii::app()->user->getId();
        // 查找对应的工作流
        $crit = new CDbCriteria();
        $crit->compare('id', $operationIdList);
        $crit->compare('state', WorkflowOperation::WORKFLOW_STATS_OPED);
        $operationModelList = WorkflowOperation::model()->findAll($crit);
        if (count($operationIdList) != count($operationModelList)) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '选中工作流有的状态未完成');
            $this->showMessage();
        }
        $paymentIdList = array();
        $paymentInfo = array();
        foreach ($operationModelList as $operationModel) {
            $paymentIdList[] = $operationModel->operation_object_id;
            $paymentInfo[$operationModel->operation_object_id] = array(
                'operationId' => $operationModel->id,
                'start_user' => $operationModel->start_user,
            );
        }
        Yii::import('common.models.workflow.payment.PaymentApply');
        $crit = new CDbCriteria();
        $crit->compare('id', $paymentIdList);
        $crit->compare('apply_state', 1);
        $paymentModelList = PaymentApply::model()->findAll($crit);
        if (count($paymentIdList) != count($paymentModelList)) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '数据发生变化');
            $this->showMessage();
        }
        $maxId = 0;
        foreach ($paymentModelList as $paymentModel) {
            if ($paymentModel->id > $maxId) {
                $maxId = $paymentModel->id;
            }
        }
        foreach ($paymentModelList as $paymentModel) {
            $paymentModel->payment_user = $uid;
            $paymentModel->payment_time = strtotime($paymentTime);
            $paymentModel->payment_note = $paymentNote;
            $paymentModel->confirm_batch_id = $maxId;
            $paymentModel->updated_by = $uid;
            $paymentModel->updated_at = time();
            $paymentModel->save();
            // 发送消息
            $requestUrl = "workflow/paymentPush";
            $requestData = array(
                'operationId' =>  $paymentInfo[$paymentModel->id]['operationId'],
                'startUser' =>  $paymentInfo[$paymentModel->id]['start_user'],
            );
            CommonUtils::requestDsOnline($requestUrl, $requestData);
        }

        $this->addMessage('state', 'success');
        $this->addMessage('data', $maxId);
        $this->addMessage('message', 'success');
        $this->showMessage();
    }

    function actionCancelConfirm()
    {
        if ($this->superPermission() == false) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '没有权限');
            $this->showMessage();
        }
        $comfirmBatchId = Yii::app()->request->getParam('confirm_batch_id');
        if (!$comfirmBatchId) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '参数错误');
            $this->showMessage();
        }
        $uid = Yii::app()->user->getId();

        Yii::import('common.models.workflow.payment.PaymentApply');
        $crit = new CDbCriteria();
        $crit->compare('confirm_batch_id', $comfirmBatchId);
        $paymentModelList = PaymentApply::model()->findAll($crit);

        foreach ($paymentModelList as $paymentModel) {
            if ($paymentModel->payment_user > 0) {
                $paymentModel->payment_user = NULL;
                $paymentModel->payment_time = NULL;
                $paymentModel->confirm_batch_id = NULL;
                $paymentModel->updated_by = $uid;
                $paymentModel->updated_at = time();
                $paymentModel->save();
            }
        }

        $this->addMessage('state', 'success');
        $this->addMessage('message', 'success');
        $this->showMessage();
    }

    function actionCancelApplyBatch()
    {
        if ($this->applyPermission() == false) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '没有权限');
            $this->showMessage();
        }
        $applyBatchId = Yii::app()->request->getParam('apply_batch_id');
        if (!$applyBatchId) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '参数错误');
            $this->showMessage();
        }
        $uid = Yii::app()->user->getId();

        Yii::import('common.models.workflow.payment.PaymentApply');
        $crit = new CDbCriteria();
        $crit->compare('apply_batch_id', $applyBatchId);
        $crit->compare('apply_state', 1);
        $paymentModelList = PaymentApply::model()->findAll($crit);

        foreach ($paymentModelList as $paymentModel) {
            if ($paymentModel->payment_user > 0) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '数据发生变化');
                $this->showMessage();
            }
        }

        foreach ($paymentModelList as $paymentModel) {
            if ($paymentModel->payment_user <= 0) {
                $paymentModel->apply_state = NULL;
                $paymentModel->apply_user = NULL;
                $paymentModel->apply_time = NULL;
                $paymentModel->apply_time = NULL;
                $paymentModel->apply_batch_id = NULL;
                $paymentModel->updated_by = $uid;
                $paymentModel->updated_at = time();
                $paymentModel->save();
            }
        }

        $this->addMessage('state', 'success');
        $this->addMessage('message', 'success');
        $this->showMessage();
    }

    // 付款的备注信息
    public function actionGetPaymentNote()
    {
        if ($this->confirmPermission() == false) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '没有权限');
            $this->showMessage();
        }
        $id = Yii::app()->request->getParam('id');
        if (!$id) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'id 不能为空');
            $this->showMessage();
        }
        Yii::import('common.models.workflow.payment.PaymentApply');
        $model = PaymentApply::model()->findByPk($id);
        if (!$model) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '付款未找到');
            $this->showMessage();
        }
        if (!$model->payment_user > 0) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '付款未确认');
            $this->showMessage();
        }
        $noteList = array();
        if ($model->add_note) {
            $addNoteArray = json_decode($model->add_note, true);
        } else {
            $addNoteArray = array(
                array(
                    'uid' => $model->payment_user,
                    'time' => $model->updated_at,
                    'note' => $model->payment_note,
                )
            );
        }
        $uidList = array_column($addNoteArray, 'uid');
        $userModelList = User::model()->findAllByPk($uidList);
        $userList = array();
        foreach ($userModelList as $userModel) {
            $userList[$userModel->uid] = $userModel;
        }

        foreach ($addNoteArray as $addNote) {
            $userModel = $userList[$addNote['uid']];
            $noteList[] = array(
                'uid' => $addNote['uid'],
                'name' => $userModel ? $userModel->getNameLang() : '',
                'time' => date('Y-m-d H:i:s', $addNote['time']),
                'note' => $addNote['note'],
            );
        }
        $this->addMessage('state', 'success');
        $this->addMessage('data', $noteList);
        $this->addMessage('message', 'success');
        $this->showMessage();
    }

    // 增加备注
    function actionPaymentAdd()
    {
        if ($this->confirmPermission() == false) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '没有权限');
            $this->showMessage();
        }
        $paymentIdList = Yii::app()->request->getParam('paymentIdList');
        if (!is_array($paymentIdList) || !$paymentIdList) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '参数错误');
            $this->showMessage();
        }
        $addNote = Yii::app()->request->getParam('addNote');
        if (!$addNote) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '追加备注不能为空');
            $this->showMessage();
        }
        $uid = Yii::app()->user->getId();

        Yii::import('common.models.workflow.payment.PaymentApply');
        $crit = new CDbCriteria();
        $crit->compare('id', $paymentIdList);
        $crit->compare('payment_user', '>0');
        $paymentModelList = PaymentApply::model()->findAll($crit);
        if (!$paymentModelList) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'payment 未找到');
            $this->showMessage();
        }
        foreach ($paymentModelList as $paymentModel) {
            $addNoteArray = array();
            if ($paymentModel->add_note) {
                $addNoteArray = json_decode($paymentModel->add_note, true);
            } else {
                $addNoteArray =  array(
                    array(
                        'uid' => $paymentModel->payment_user,
                        'time' => $paymentModel->updated_at,
                        'note' => $paymentModel->payment_note,
                    )
                );
            }
            $addNoteArray[] = array(
                'uid' => $uid,
                'time' => time(),
                'note' => $addNote,
            );
            $paymentModel->payment_note = $addNote;
            $paymentModel->add_note = json_encode($addNoteArray);
            $paymentModel->save();
        }

        $this->addMessage('state', 'success');
        $this->addMessage('message', 'success');
        $this->showMessage();
    }

    function getPaymentUser($data)
    {
        if ($data->payment->payment_user > 0) {
            $user = User::model()->findByPk($data->payment->payment_user);
            return $user->getNameLang();
        } else {
            return "";
        }
    }
    function getTitle($data)
    {
        $title = "#" . $data->id . " " . $data->payment->title;
        $link = CHtml::link(
            $title,
            Yii::app()->createUrl("//workflow/entrance/index", array(
                "definationId" => $data->defination_id,
                "nodeId" => $data->current_node_index,
                "operationId" => $data->id,
                "branchId" => $data->branchid,
                "action" => "show",
            )),
            array("class" => "J_ajax", "data-method" => "get")
        );
        if ($data->payment->budget == 0) {
            $link .= ' <span class="label label-danger">预算外</span>';
        }
        return $link;
    }
    function getState($data)
    {
        $payment = $data->payment;
        if ($payment->payment_time > 0) {
            return "已付款";
        } elseif ($payment->apply_state == 2) {
            return '已锁定';
        } elseif ($payment->apply_state == 1) {
            return '待付款';
        } else {
            return '待申请';
        }
    }
    function getWorkflowUser($data)
    {
        $date = $this->formatDate($data->start_time);
        return $data->userInfo->getNameLang() . '-' . $date;
    }
    function getApplyUser($data)
    {
        if ($data->payment->apply_user > 0) {
            $user = User::model()->findByPk($data->payment->apply_user);
            return $user->getNameLang();
        } else {
            return "";
        }
    }
    function getConfirmUser($data)
    {
        if ($data->payment->payment_user > 0) {
            $user = User::model()->findByPk($data->payment->payment_user);
            return $user->getNameLang();
        } else {
            return "";
        }
    }

    function getApplyDate($data)
    {
        if ($data->payment->apply_time > 0) {
            return $this->formatDate($data->payment->apply_time);
        } else {
            return "";
        }
    }

    function getPaymentDate($data)
    {
        if ($data->payment->payment_time > 0) {
            return $this->formatDate($data->payment->payment_time);
        } else {
            return "";
        }
    }

    function formatDate($time)
    {
        $year = date("Y", $time);
        if ($year == date("Y")) {
            $date = date("m-d", $time);
        } else {
            $date = date("Y-m-d", $time);
        }
        return $date;
    }
}
