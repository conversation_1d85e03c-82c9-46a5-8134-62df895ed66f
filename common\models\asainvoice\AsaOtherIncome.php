<?php

/**
 * This is the model class for table "ivy_asa_other_income".
 *
 * The followings are the available columns in table 'ivy_asa_other_income':
 * @property integer $id
 * @property string $school_id
 * @property integer $group_id
 * @property integer $category
 * @property string $contract
 * @property integer $start_time
 * @property integer $end_time
 * @property double $fee
 * @property integer $status
 * @property integer $updated_by
 * @property integer $updated_at
 * @property integer $created_by
 * @property integer $created_at
 */
class AsaOtherIncome extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_asa_other_income';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('school_id, group_id, category, contract, start_time, end_time, fee, status, updated_by, updated_at, created_by, created_at', 'required'),
			array('group_id, category, start_time, end_time, status, updated_by, updated_at, created_by, created_at', 'numerical', 'integerOnly'=>true),
			array('fee', 'numerical'),
			array('school_id', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, school_id, group_id, category, contract, start_time, end_time, fee, status, updated_by, updated_at, created_by, created_at', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'school_id' => 'School',
			'group_id' => 'Group',
			'category' => Yii::t('asa','类型'),
			'contract' => Yii::t('asa','合同图片'),
			'start_time' => Yii::t('asa','开始时间'),
			'end_time' => Yii::t('asa','结束时间'),
			'fee' => Yii::t('asa','合同金额'),
			'status' => 'Status',
			'updated_by' => 'Updated By',
			'updated_at' => 'Updated At',
			'created_by' => 'Created By',
			'created_at' => 'Created At',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('school_id',$this->school_id,true);
		$criteria->compare('group_id',$this->group_id);
		$criteria->compare('category',$this->category);
		$criteria->compare('contract',$this->contract,true);
		$criteria->compare('start_time',$this->start_time);
		$criteria->compare('end_time',$this->end_time);
		$criteria->compare('fee',$this->fee);
		$criteria->compare('status',$this->status);
		$criteria->compare('updated_by',$this->updated_by);
		$criteria->compare('updated_at',$this->updated_at);
		$criteria->compare('created_by',$this->created_by);
		$criteria->compare('created_at',$this->created_at);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AsaOtherIncome the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	public static function category()
    {
        $data = array(
            1 => '场租',
        );

        return $data;
    }
}
