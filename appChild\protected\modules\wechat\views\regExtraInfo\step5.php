<?php echo $this->renderPartial('steps_header', array('currentStep'=>5)); ?>
<style>
    .hidden{
        display: none;
    }
</style>
<div class="container">
    <h5 class="htitle"><span class="fas fa-utensils"></span> <?php echo $this->getStepTitle($this->step); ?></h5>
    <?php echo $this->renderPartial('_nouser_header'); ?>
    <div class="edit-content">
    <?php 
        $content = RegConfig::getContent($this->regStudent->reg_id, $this->step);
        if ($content) {
            echo CommonUtils::autoLang(base64_decode($content['cn']['lunch']), base64_decode($content['en']['lunch']));
        }
     ?>
    </div>
    <div>
        <!-- 英文名 -->
        <?php $form = $this->beginWidget('CActiveForm', array(
            'id' => 'sep1-form',
            'enableAjaxValidation' => false,
            'htmlOptions' => array(
                // 'class'=>'J_ajaxForm form-horizontal',
                'role' => 'form',
            ),
        )); ?>
        <div class="form-group">
            <?php echo $form->radioButtonList($formModel5, 'lunch', array(
                '1' => Yii::t('reg', 'Yes, my child will eat school lunch.'),
                '0' => Yii::t('reg', 'No, my child will bring his/her own lunch from home.')
            ), array(
                'template' => '<div class="form-check">{input} {label}</div>',
                'separator' => '',
                'class'=>'form-check-input',
            ));
            ?>
        </div>
        <div class="form-group hidden" id='edible'>
        <h5 class="sub-title">
                <span><?php echo Yii::t('reg', "Remarks") ?></span>
            </h5>
            <?php echo $form->radioButtonList($formModel5, 'lunchSort', array(
                '1' => Yii::t('reg', 'Normal Lunch at School'),
                '2' => Yii::t('reg', 'Special Pork-free Lunch at School'),
                '3' => Yii::t('reg', 'Special Vegan Lunch at School'),
            ), array(
                'template' => '<div class="form-check">{input} {label}</div>',
                'separator' => '',
                'class'=>'form-check-input'
            ));
            ?>
        </div>
        <div class="form-group" id='band'>
        <h5 class="sub-title">
                <span><?php echo Yii::t('reg', "Allergy") ?></span>
            </h5>
            <div class="form-group">
                <?php echo $form->labelEx($formModel5, 'allergen'); ?>
                <?php echo $form->textArea($formModel5, 'allergen', array('class' => 'form-control')); ?>
            </div>
        </div>
        <div class="mt-3">
            <button type="submit" class="btn btn-primary btn-lg btn-block examine"><?php echo Yii::t('reg', 'Submit') ?></button>
            <p class="text-black-50 mt-3 text-center  examine_status">

            </p>
        </div>
        <?php $this->endWidget(); ?>
    </div>
</div>
<script>
    if($('#RegStep5Form_lunch_0').attr("checked") == 'checked') {
        $('#edible').removeClass('hidden');
    }else if($('#RegStep5Form_lunch_1').attr("checked") == 'checked') {
        $('#band').removeClass('hidden');
    }
    $('input[name="RegStep5Form[lunch]"]').click(function() {
        if($(this).val() == 1) {
            $('#edible').removeClass('hidden');
            // $('#band').addClass('hidden');
        }else{
            // $('#band').removeClass('hidden');
            $('#edible').addClass('hidden');
        }
    });
</script>