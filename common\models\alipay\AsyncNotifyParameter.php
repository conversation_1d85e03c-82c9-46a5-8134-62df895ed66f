<?php

/**
 * This is the model class for table "ivy_alipay_async_notify_parameter".
 *
 * The followings are the available columns in table 'ivy_alipay_async_notify_parameter':
 * @property integer $id
 * @property string $notify_time
 * @property string $notify_type
 * @property string $notify_id
 * @property string $sign_type
 * @property string $sign
 * @property string $out_trade_no
 * @property string $subject
 * @property string $payment_type
 * @property string $trade_no
 * @property string $trade_status
 * @property string $gmt_create
 * @property string $gmt_payment
 * @property string $gmt_close
 * @property string $refund_status
 * @property string $gmt_refund
 * @property string $seller_email
 * @property string $buyer_email
 * @property string $seller_id
 * @property string $buyer_id
 * @property double $price
 * @property double $total_fee
 * @property integer $quantity
 * @property string $body
 * @property double $discount
 * @property string $is_total_fee_adjust
 * @property string $use_coupon
 * @property string $error_code
 * @property string $bank_seq_no
 * @property string $extra_common_param
 * @property string $out_channel_type
 * @property string $out_channel_amount
 * @property string $out_channel_inst
 * @property integer $update_timestamp
 */
class AsyncNotifyParameter extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return AsyncNotifyParameter the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_alipay_async_notify_parameter';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('notify_time, notify_type, notify_id, sign_type, sign', 'required'),
			array('quantity, update_timestamp', 'numerical', 'integerOnly'=>true),
			array('price, total_fee, discount', 'numerical'),
			array('notify_time, notify_type, sign_type, payment_type, trade_status, gmt_create, gmt_payment, gmt_close, refund_status, gmt_refund, is_total_fee_adjust, use_coupon', 'length', 'max'=>25),
			array('notify_id, sign, subject, extra_common_param, out_channel_type, out_channel_amount', 'length', 'max'=>255),
			array('out_trade_no, trade_no, seller_email, buyer_email, error_code, bank_seq_no, out_channel_inst', 'length', 'max'=>125),
			array('seller_id, buyer_id', 'length', 'max'=>50),
			array('body', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, notify_time, notify_type, notify_id, sign_type, sign, out_trade_no, subject, payment_type, trade_no, trade_status, gmt_create, gmt_payment, gmt_close, refund_status, gmt_refund, seller_email, buyer_email, seller_id, buyer_id, price, total_fee, quantity, body, discount, is_total_fee_adjust, use_coupon, error_code, bank_seq_no, extra_common_param, out_channel_type, out_channel_amount, out_channel_inst, update_timestamp', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'notify_time' => 'Notify Time',
			'notify_type' => 'Notify Type',
			'notify_id' => 'Notify',
			'sign_type' => 'Sign Type',
			'sign' => 'Sign',
			'out_trade_no' => 'Out Trade No',
			'subject' => 'Subject',
			'payment_type' => 'Payment Type',
			'trade_no' => 'Trade No',
			'trade_status' => 'Trade Status',
			'gmt_create' => 'Gmt Create',
			'gmt_payment' => 'Gmt Payment',
			'gmt_close' => 'Gmt Close',
			'refund_status' => 'Refund Status',
			'gmt_refund' => 'Gmt Refund',
			'seller_email' => 'Seller Email',
			'buyer_email' => 'Buyer Email',
			'seller_id' => 'Seller',
			'buyer_id' => 'Buyer',
			'price' => 'Price',
			'total_fee' => 'Total Fee',
			'quantity' => 'Quantity',
			'body' => 'Body',
			'discount' => 'Discount',
			'is_total_fee_adjust' => 'Is Total Fee Adjust',
			'use_coupon' => 'Use Coupon',
			'error_code' => 'Error Code',
			'bank_seq_no' => 'Bank Seq No',
			'extra_common_param' => 'Extra Common Param',
			'out_channel_type' => 'Out Channel Type',
			'out_channel_amount' => 'Out Channel Amount',
			'out_channel_inst' => 'Out Channel Inst',
			'update_timestamp' => 'Update Timestamp',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('notify_time',$this->notify_time,true);
		$criteria->compare('notify_type',$this->notify_type,true);
		$criteria->compare('notify_id',$this->notify_id,true);
		$criteria->compare('sign_type',$this->sign_type,true);
		$criteria->compare('sign',$this->sign,true);
		$criteria->compare('out_trade_no',$this->out_trade_no,true);
		$criteria->compare('subject',$this->subject,true);
		$criteria->compare('payment_type',$this->payment_type,true);
		$criteria->compare('trade_no',$this->trade_no,true);
		$criteria->compare('trade_status',$this->trade_status,true);
		$criteria->compare('gmt_create',$this->gmt_create,true);
		$criteria->compare('gmt_payment',$this->gmt_payment,true);
		$criteria->compare('gmt_close',$this->gmt_close,true);
		$criteria->compare('refund_status',$this->refund_status,true);
		$criteria->compare('gmt_refund',$this->gmt_refund,true);
		$criteria->compare('seller_email',$this->seller_email,true);
		$criteria->compare('buyer_email',$this->buyer_email,true);
		$criteria->compare('seller_id',$this->seller_id,true);
		$criteria->compare('buyer_id',$this->buyer_id,true);
		$criteria->compare('price',$this->price);
		$criteria->compare('total_fee',$this->total_fee);
		$criteria->compare('quantity',$this->quantity);
		$criteria->compare('body',$this->body,true);
		$criteria->compare('discount',$this->discount);
		$criteria->compare('is_total_fee_adjust',$this->is_total_fee_adjust,true);
		$criteria->compare('use_coupon',$this->use_coupon,true);
		$criteria->compare('error_code',$this->error_code,true);
		$criteria->compare('bank_seq_no',$this->bank_seq_no,true);
		$criteria->compare('extra_common_param',$this->extra_common_param,true);
		$criteria->compare('out_channel_type',$this->out_channel_type,true);
		$criteria->compare('out_channel_amount',$this->out_channel_amount,true);
		$criteria->compare('out_channel_inst',$this->out_channel_inst,true);
		$criteria->compare('update_timestamp',$this->update_timestamp);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}