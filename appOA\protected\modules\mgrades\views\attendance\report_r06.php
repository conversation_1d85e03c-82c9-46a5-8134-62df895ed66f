
<style type="text/css">
 [v-cloak] {
	display: none;
}
.filter{
    background:#FAFAFA;
    padding:34px 24px 10px;
}
.loading{
	width:98%;
	height:90%;
	background:#fff;
	position: absolute;
	opacity: 0.5;
	z-index: 99
}
.loading span{
	width:100%;
	height:60%;
	display:block;
	background: url("<?php echo Yii::app()->theme->baseUrl?>/images/loading.gif")no-repeat center center ;
}
.piont{
	width: 6px;
	height: 6px;
	background: #666666;
	border-radius: 50%;
	display:inline-block
}
.flexWidth{
    width:100px;
    line-height:30px
}
</style>
<div id='container'  v-cloak>
	<h3 class='color3 font14'><strong style='font-size:18px' class='mr20'><?php echo Yii::t('attends', 'Abnormal Attendance & Violation'); ?></strong></h3>
    <div class='filter mt20'>
        <div class='form-inline'>
            <div class='flex mb10'>
                <span class='color6 font14 flexWidth' ><?php echo Yii::t('attends', 'Time Range'); ?>：</span>
                <div class='flex1'>
                    <label class="radio-inline" v-for='(list,index) in timeList' >
                        <input type="radio" name="inlineRadioOptions":value="list.key" v-model='time_limit'> {{list.title}}
                    </label>
                    <span v-show='time_limit=="time"'>
                    <input type="text" class="form-control form-group ml10" id="start_date" placeholder="<?php echo Yii::t("newDS", "Select a date");?>" :value='start_date'>
                    -
                    <input type="text" class="form-control form-group ml10" id="end_date" placeholder="<?php echo Yii::t("newDS", "Select a date");?>" :value='end_date'>
                    </span>
                </div>
            </div>
            <div  class='flex mb10'>
                <span class='color6 font14 flexWidth'><?php echo Yii::t('attends', 'Type'); ?>：</span>
                <div class='flex1'>
                    <label class="checkbox-inline" v-for='(list,key,index) in attendanceList'>
                        <input type="checkbox" id="inlineCheckbox1" :value="key"  v-model='type'> {{list}}
                    </label>
                </div>
            </div>
            <div class='flex'>
                <span class='color6 font14 flexWidth'><?php echo Yii::t('attends', 'Cumulative Days'); ?>≧：</span>
                <div class='flex1'>
                    <input type="text" class="form-control select_2"  placeholder="累计天数" v-model='day_number'>
                    <p class='mt20'>
                        <button type="button" class="btn btn-primary mr10" @click='searchData()'><?php echo Yii::t("global", 'Search') ?></button>
                        <button type="button" class="btn btn-default" @click='reset()'><?php echo Yii::t("reg", 'Reset') ?></button>
                    </p>
                </div>
            </div>

        </div>

    </div>
    <div class='loading'  v-if='showLoading'>
		<span></span>
	</div>
    <div v-if='showData'>
        <div v-if='tableList.length!=0'>
            <p class='mt20 mb20 color3 font14'><strong>{{count}}<?php echo Yii::t("newDS", " student(s) selected");?></strong>
            <button type="button" class="btn btn-primary pull-right" @click='exportTab()'><?php echo Yii::t("user", 'Export') ?></button>
            </p>
            <table class="table table-hover" id='table'>
                <tr>
                    <th>#</th>
                    <th><?php echo Yii::t("report", 'Name') ?></th>
                    <th><?php echo Yii::t("report", 'Class') ?></th>
                    <th v-for='(typeOne,index) in searchType'>
                        {{attendanceProportionList[typeOne]}}
                    </th>
                    <th><?php echo Yii::t("labels", 'Type') ?></th>
                </tr>
                <tr v-for='(list,index) in tableList'>
                    <td>{{index+1}}</td>
                    <td>{{list.name}}</td>
                    <td>{{list.class_name}}</td>
                    <th v-for='(typeOne,index) in searchType'>
                        <a href="javascript:;"  @click='childType=typeMap[typeOne];showStu(list)'>{{list.scale[typeOne]}}</a>
                    </th>
                    <td>
                        <span v-for='(item,key,idx) in list.type'>{{attendanceSelectList[item]}}<span  v-if='idx!=Object.keys(list.type).length-1'>、</span></span>
                    </td>
                </tr>
            </table>
            <nav aria-label="Page navigation " v-if='current_page>1'>
                <ul class="pagination pull-right">
                    <li :class='current_page==1?"disabled":""'>
                        <a href="javascript:;" aria-label="Previous" @click='searchData(current_page-1)'>
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    <li v-for='(list,index) in pageList' :class='current_page==list?"active":""'><a href="javascript:;" @click='searchData(list)'>{{list}}</a></li>
                    <li  :class='current_page==pageList?"disabled":""'>
                        <a href="javascript:;" aria-label="Next"  @click='searchData(current_page+1)'>
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
        <div class="alert alert-danger mt20" role="alert" v-else><?php echo Yii::t('asa','No data found.') ?></div>
    </div>
    <div class="modal fade" id="childModel" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false">
		<div class="modal-dialog modal-lg" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" ><?php echo Yii::t('attends','详情') ?> <span id="remarks_t"></span></h4>
				</div>
				<div class="form-horizontal">
					<div class="modal-body">
						<div class='p10' v-if='Object.keys(childData).length!=0'>
							<ul class="nav nav-pills mb20" role="tablist">
								<li role="presentation"  :class='key==childType?"active":""'  v-for='(list,key,index) in childData.total' @click='childType=key'><a href="javascript:;">{{childData.map[key]}} <span class="badge">{{list}}</span></a></li>
							</ul>
							<div class="alert alert-danger mt20"  v-if='childData.list[childType] && childData.list[childType].length==0' role="alert"><?php echo Yii::t('asa','No data found.') ?></div>
							<div style='max-height:400px;overflow-y:auto' v-else class='scroll-box'>
								<div class='col-md-3' v-for='(list,index) in childData.list[childType]' >
									<div class='flex'>
										<div style='width:16px'>
											<span class='piont'></span>
										</div>
										<div class='flex1'>
											<div class='color3 font14 mb5'>{{list.day}} [{{list.week}}]</div>
										</div>
									</div>
								</div>
							</div>
							<div class='clearfix'></div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<script>
    $(function() {
		$("#start_date").datepicker({
			dateFormat: "yy-mm-dd ",
		});
        $("#end_date").datepicker({
			dateFormat: "yy-mm-dd ",
		});
		$( "#start_date").on('change', function () {
			container.start_date = $('#start_date').val();
		});
        $( "#end_date").on('change', function () {
			container.end_date = $('#end_date').val();
		});
	});
	var container = new Vue({
        el: "#container",
        data: {
            timeList:[
                {
                    key:'week',
                    title:'<?php echo Yii::t("attends", 'This Week') ?>'
                },
                {
                    key:'month',
                    title:'<?php echo Yii::t("attends", 'This Month') ?>'
                },
                {
                    key:'semester',
                    title:'<?php echo Yii::t("attends", 'This Semester') ?>'
                },
                {
                    key:'year',
                    title:'<?php echo Yii::t("attends", 'This School Year') ?>'
                },
                {
                    key:'time',
                    title:'<?php echo Yii::t("attends", 'Custom') ?>'
                },
            ],
            //查询出来存在的违规类型
            attendanceSelectList:{
                '10':'<?php echo Yii::t("attends", 'Sick Leave') ?>',
                '20':'<?php echo Yii::t("attends", 'Personal Leave') ?>',
                '64':'<?php echo Yii::t("attends", 'Dress Code Violation') ?>',
                '61':'<?php echo Yii::t("attends", 'Phone') ?>',
                '62':'<?php echo Yii::t("attends", 'Other Personal Devices') ?>',
                '63':'<?php echo Yii::t("attends", 'Phone&Other') ?>',
                // '40':'迟到'
            },
            attendanceList:{
                '10':'<?php echo Yii::t("attends", 'Sick Leave') ?>',
                '20':'<?php echo Yii::t("attends", 'Personal Leave') ?>',
                '64':'<?php echo Yii::t("attends", 'Dress Code Violation') ?>',
                //'61':'<?php //echo Yii::t("attends", 'Device Confiscated') ?>//',
            },
            attendanceProportionList:{
                '10':'<?php echo Yii::t("attends", 'Sick Leave %') ?>',
                '20':'<?php echo Yii::t("attends", 'Personal Leave %') ?>',
                '64':'<?php echo Yii::t("attends", 'Dress Code Violation %') ?>',
                //'61':'<?php //echo Yii::t("attends", 'Device Confiscated %') ?>//',
                // '40':'迟到'
            },
            typeMap:{
                10:'SickLeave',
                20:'PersonalLeave',
                64:'UniformInfraction',
                61:'Phone',
                62:'Other Personal Devices',
                63:'Both',
                // 40:'Tardy'
            },
            type:[10,20,64],
            time_limit:'semester',
            day_number:'2',
            start_date:'',
            end_date:'',
            tableList:[],
            count:'',
            pageSize:20,
            current_page:1,
            pageList:'',
			showLoading:false,
            showData:false,
            childData:{},
            childType:'',
            searchType:[],
        },
		created(){
            var curDate = new Date();
            var nextDate = new Date(curDate.getTime() + 24*60*60*1000); //后一天
            this.start_date=this.formatDate(curDate)
            this.end_date=this.formatDate(nextDate)
            this.searchData()
		},
        methods: {
            formatDate(date) {
                var year = date.getFullYear();
                var month = date.getMonth() + 1;
                var day = date.getDate();
                return year + "-" + this.formatTen(month) + "-" + this.formatTen(day);
            },
            formatTen(num) {
                return num > 9 ? (num + "") : ("0" + num);
            } ,
            reset(){
                this.type=[]
                this.time_limit=''
                this.day_number=''
                this.start_date=''
                this.end_date=''
                this.current_page=1
                this.tableList=[]
                this.pageList=''
				this.showData=false
            },
            searchData(page){
                console.log(this.type)
                console.log(this.time_limit)
                console.log(this.day_number)
                console.log(this.start_date)
                console.log(this.end_date)
                if(page){
                    this.current_page=page
                }
                this.searchType = this.type
                if(this.time_limit==""){
                    resultTip({
                        error: 'warning',
                        msg:'请选择时间范围'
                    });
                    return
                }
                if(this.time_limit=="time"){
                    if(this.start_date==''){
                        resultTip({
                            error: 'warning',
                            msg:'请选择开始时间'
                        });
                        return
                    }
                    if(this.end_date==''){
                        resultTip({
                            error: 'warning',
                            msg:'请选择结束时间'
                        });
                        return
                    }
                }
                if(this.type.length==0){
                    resultTip({
                            error: 'warning',
                            msg:'请选择类型'
                        });
                        return
                }
                if(this.day_number==''){
                    resultTip({
                        error: 'warning',
                        msg:'请填写累计天数'
                    });
                    return
                }
                let that=this
				this.showLoading=true
				this.showData=false
				$.ajax({
                    url: '<?php echo $this->createUrl("UnusualAttendanceList") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
						type:this.type,
                        time_limit:this.time_limit,
                        day_number:this.day_number,
                        start_date:this.start_date,
                        end_date:this.end_date,
                        pageSize:this.pageSize,
                        page:this.current_page
                    },
                    success: function(data) {
                        if(data.state=='success'){
							that.tableList=data.data.list
							that.count=data.data.count
                            that.pageList=data.data.totalPage
                            that.current_page=data.data.page
				            that.showLoading=false
                            that.showData=true
                        }else{
                            that.tableList=[];
                            that.count=0
                            that.pageList=0
                            that.current_page=0
                            that.showLoading=false
                            that.showData=true
                            resultTip({error: 'warning', msg: data.message});
                        }
                    }
                })
            },
            showStu(list,type){
				let that=this
                console.log(type)
				$.ajax({
                    url: '<?php echo $this->createUrl("continuousDetail") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
						child_id:list.child_id,
                        type:this.type,
                        time_limit:this.time_limit,
                        day_number:this.day_number,
                        start_date:this.start_date,
                        end_date:this.end_date,
                    },
                    success: function(data) {
                        if(data.state=='success'){
							that.childData=data.data
							// that.childType=data.data.select
                        	$('#childModel').modal('show');
                        }else{
                        }
                    }
                })
			},
            exportTab(){
                var title='异常考勤&着装违规'
                var elt = document.getElementById('table');
                var wb = XLSX.utils.table_to_book(elt, { raw: true });
                var wbout = XLSX.write(wb, { bookType: 'xlsx', bookSST: true, type: 'array' })
                try {
                    saveAs(new Blob([wbout], { type: 'application/octet-stream' }), title+'.xlsx')
                } catch (e) {
                    if (typeof console !== 'undefined') {
                        console.log(e, wbout)
                    }
                }
                return wbout
            }
		}
    })
</script>
