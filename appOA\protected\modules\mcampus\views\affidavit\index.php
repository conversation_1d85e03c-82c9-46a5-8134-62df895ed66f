<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('/mcampus/default/index'))?></li>
        <li class="active"><?php echo Yii::t('site','防疫承诺书列表') ?></li>
    </ol>

    <div class="row">
        <div class="col-md-12 col-sm-12">
            <div class="mb10 row">
                <!-- 搜索框 -->
                <form  class="" style="float: left;width: 100%" action="<?php echo $this->createUrl('index'); ?>" method="get">
                    <?php echo Chtml::hiddenField('branchId',$this->branchId); ?>
                    <!-- 状态 -->
                    <div class="col-sm-2 form-group">
                        <?php echo Chtml::dropDownList('type', $type, array(1=>'未出城',2=>'出城'),array('class'=>'form-control',  'empty' => Yii::t('teaching', '全部状态'))); ?>
                    </div>
                    <!-- 内容匹配 -->
                    <div class="">
                        <button class="btn btn-default ml5" type="submit"><span class="glyphicon glyphicon-search"> </span> </button>
                        <a href="javascript:void(0);" onclick="exportParent()" class="btn btn-info" target="_blank"><?php echo Yii::t('user', 'Export');?></a>
                    </div>
                </form>
            </div>
        <div class="panel-body">
            <?php
            $this->widget('ext.ivyCGridView.BsCGridView', array(
                'id'=>'covid19Affidavit',
                'afterAjaxUpdate'=>'js:head.Util.modal',
                'dataProvider'=>$dataProvider,
                'template'=>"{items}{pager}{summary}",
                'colgroups'=>array(
                    array(
                        //"colwidth"=>array(100,100,100,100,100,100,100,100,100,100),
                    )
                ),
                'columns'=>array(
                    array(
                        'name'=> 'childid',
                        'type'=>'raw',
                        'value' => array($this, "getUsersName"),
                    ),
                    array(
                        'name'=> 'classid',
                        'type'=>'raw',
                        'value' => array($this, "getClassTitle"),
                    ),
                    array(
                        'name'=> Yii::t('global','家长姓名 / 电话 / 关系'),
                        'type'=>'raw',
                        'value' => array($this, "getParent"),
                    ),
                    array(
                        'name'=> Yii::t('global','是否出城'),
                        'type'=>'raw',
                        'value' => array($this, "getType"),
                    ),
                    array(
                        'name'=> Yii::t('global','目的地'),
                        'type'=>'raw',
                        'value' => array($this, "getDestination"),
                    ),
                    array(
                        'name'=> Yii::t('global','返回日期'),
                        'type'=>'raw',
                        'value' => array($this, "getReturnDate"),
                    ),
                    array(
                        'name'=> 'signature',
                        'type'=>'raw',
                        'value' => array($this, "getSignature"),
                    ),
                    array(
                        'name' => Yii::t('global','Action'),
                        'value' => array($this, "getButton"),
                    ),
                ),
            ));
            ?>
        </div>
    </div>

<script>
    function cbCovid19Affidavit() {
        $.fn.yiiGridView.update('covid19Affidavit', {
            complete: function () {
                head.Util.ajaxDel()
            }
        });
    }
    function exportParent() {
        var type = '<?php echo $type ?>';
        var url = '<?php echo $this->createUrl('export') ?>';

        $.ajax({
            url: url,
            type: 'POST',
            data: {type:type},
            success: function (res) {
                if (res.state == 'success') {
                    var data = res.data.items;
                    const filename = res.data.title;
                    const ws_name = "SheetJS";

                    const worksheet = XLSX.utils.aoa_to_sheet(data);
                    const workbook = XLSX.utils.book_new();
                    XLSX.utils.book_append_sheet(workbook, worksheet, ws_name);
                    // generate Blob
                    const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                    const blob = new Blob([wbout], {type: 'application/octet-stream'});
                    // save file
                    let link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    link.download = filename;
                    link.click();
                    setTimeout(function() {
                        // 延时释放掉obj
                        URL.revokeObjectURL(link.href);
                        link.remove();
                    }, 500);
                }
            },
            dataType: 'json'
        });
    }
</script>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
