<?php

/**
 * This is the model class for table "ivy_purchase_products".
 *
 * The followings are the available columns in table 'ivy_purchase_products':
 * @property integer $id
 * @property integer $cid
 * @property integer $commodity_code
 * @property integer $aid
 * @property string $tid
 * @property string $cn_name
 * @property string $model
 * @property double $price
 * @property string $pic
 * @property string $tip
 * @property string $cagegorys_id
 * @property integer $add_timestamp
 * @property integer $add_user
 * @property integer $update_timestamp
 */
class PurchaseProducts extends CActiveRecord
{

	public $uploadedFile;

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_purchase_products';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('cid, aid, cn_name, price, add_user', 'required'),
			array('cid, aid, cagegorys_id, add_timestamp, add_user, update_timestamp', 'numerical', 'integerOnly'=>true),
			array('price', 'numerical'),
			array('cn_name, tid, commodity_code, model, pic', 'length', 'max'=>255),
			array('tip', 'safe'),
			array("uploadedFile", "file", "types" => "jpg, gif, png, jpeg", "maxSize" => 1024*1024*2, "allowEmpty" => true),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, cid, aid, cagegorys_id, tid, commodity_code, cn_name, model, price, pic, tip, add_timestamp, add_user, update_timestamp', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'productItem'=>array(self::HAS_MANY, 'PurchaseProductsItem', 'pid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'cid' => '商品分类',
			'commodity_code' => '商品编码',
			'aid' => '摆放区域',
			'tid' => '商品类型',
			'cn_name' => '商品标题',
			'model' => '商品型号',
			'price' => '商品单价',
			'pic' => '商品图片',
			'tip' => '商品备注',
			'cagegorys_id' => '主营业务',
			'add_timestamp' => '添加时间',
			'add_user' => '操作用户',
			'update_timestamp' => '最后更新',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('cid',$this->cid);
		$criteria->compare('commodity_code',$this->commodity_code);
		$criteria->compare('aid',$this->aid);
		$criteria->compare('tid',$this->tid,true);
		$criteria->compare('cn_name',$this->cn_name,true);
		$criteria->compare('model',$this->model,true);
		$criteria->compare('price',$this->price);
		$criteria->compare('pic',$this->pic,true);
		$criteria->compare('tip',$this->tip,true);
		$criteria->compare('cagegorys_id',$this->cagegorys_id,true);
		$criteria->compare('add_timestamp',$this->add_timestamp);
		$criteria->compare('add_user',$this->add_user);
		$criteria->compare('update_timestamp',$this->update_timestamp);
        $criteria->order = 'commodity_code DESC';
		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return PurchaseProducts the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	protected function beforeSave()
	{
		if (parent::beforeSave()) {
			if($file = CUploadedFile::getInstance($this,'uploadedFile'))
			{
			   $file_ext = $file->getExtensionName();
			   	//处理图片
			   	Yii::import('application.extensions.image.Image');
	         	$picPath = Yii::app()->params['OAUploadBasePath'] . '/purchase/';
	         	$picName = '5_' . uniqid() . '.' . $file_ext;
	         	if ($file->saveAs($picPath . $picName)){
		   	      	$image = new Image($picPath . $picName);
		   	      	$image->quality(100);
		   	      	$image->resize(800,600);
		   	      	$image->save( $picPath . $picName);
		   	      	//保存缩略图
		   	      	$image = new Image($picPath . $picName);
		   	      	$image->quality(100);
		   	      	$image->resize(100,100);
		   	      	$image->save( $picPath . 'thumbs/' . $picName);

		   	      	$this->pic = $picName;
		   	      	
			   	}
			}
   	      	return true;
		}
		return false;
	}

	protected function afterSave()
	{
		if (!$this->isNewRecord){
			//采购商品更新后，同时更新标配表的update_timestamp
			foreach ($this->productItem as $productItem) {
				$time = time();
				$productItem->standard->update_timestamp = $time;
				$productItem->standard->save();
			}
			//修改相关的申请表状态
			$this->changeApplicationStatus();
		}
		parent::afterSave();
	}

	protected function afterDelete()
	{
		//删除商品关联的图片
		$picPath = Yii::app()->params['OAUploadBasePath'] . '/purchase/';
		$picName = $this->pic;
		unlink($picPath . $picName);
		unlink($picPath . 'thumbs/' . $picName);
		//更新标配表，删除purchaseProductsItem相关的记录
		foreach ($this->productItem as $productItem) {
			$time = time();
			$productItem->delete();
		}
		//修改相关的申请表状态
		$this->changeApplicationStatus();

		parent::afterDelete();
	}

	/**
	 * 修改相关的申请表状态
	 * @return [type] [description]
	 */
	public function changeApplicationStatus()
	{
		$criteria = new CDbCriteria;
		$criteria->compare('type',PurchaseApplicationItem::PURCHASE_PRODUCT);
		$criteria->compare('pid',$this->id);
		$paiModels = PurchaseApplicationItem::model()->findAll($criteria);
		foreach ($paiModels as $paiModel) {
			//如果是正在进行中的申请
			if ($paiModel->application->status == PurchaseApplication::INPROGRESS) {
				$paiModel->application->status = PurchaseApplication::CHANGED;
				$paiModel->application->save();
			}
		}
	}
}
