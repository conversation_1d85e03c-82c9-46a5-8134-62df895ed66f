<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
    </button>
    <h4 class="modal-title" id="exampleModalLabel">设备信息</h4>
</div>
<?php
$form = $this->beginWidget('CActiveForm', array(
    'id' => 'updatedCards',
    'enableAjaxValidation' => false,
    'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form', 'enctype'=>"multipart/form-data"),
)); ?>
<div class="modal-body">
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'class'); ?></label>
        <div class="col-xs-8">
            <?php echo $model['child']?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'class'); ?></label>
        <div class="col-xs-8">
            <?php echo $model->class ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'name'); ?></label>
        <div class="col-xs-8">
            <?php echo $model->name ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'tel'); ?></label>
        <div class="col-xs-8">
            <?php echo $model->tel ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'relation'); ?></label>
        <div class="col-xs-8">
            <?php echo $model->relation ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'uploadFile'); ?></label>
        <div class="col-xs-8">
            <?php echo $form->fileField($model, 'uploadFile'); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"></label>
        <div class="col-xs-8">
            <img style="width:200px;" src="<?php echo $model->showPhoto; ?>">
        </div>
    </div>
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
</div>
<?php $this->endWidget(); ?>
