<style>
    [v-cloak] {
		display: none;
	}
    .colorBlue{
        color:#4D88D2
    }
    .title{
        display: flex;
        margin-top:24px;
        align-items: center;
        background:#F0F5FB;
        padding:8px 24px;
        border-radius:2px
    }
    .lineHeight{
        line-height:15px
    }
    .blueLine{
        width: 4px;
        height: 14px;
        background: #4D88D2;
    }
    .maxWidth{
        max-width:750px
    }
    .status{
        border-radius: 4px;
        border: 1px solid #E5E6EB;
        padding:16px 18px
    }
    .colorRed{
        color:#D9534F
    }
    .colorWait{
        color:#F0AD4E
    }
    .colorGreen{
        color:#5CB85C
    }
    .tag{
        font-size: 12px;
        color: #333333;
        line-height: 12px;
        padding:4px 6px;
        background: #F2F3F5;
        border-radius: 2px;
    }
    .verticalTop{
        vertical-align: top !important;
    }
    .bgGreen{
        background:#5CB85C
    }
    .bgRed{
        background:#D9534F
    }
    .result{
        padding:4px 6px;
        font-size:12px;
        color:#fff;
        border-radius:3px
    }
    .linkFile{
        padding:6px 8px;
    }
    .linkFile:hover{
        background: #F7F7F8;
        border-radius: 4px;
        cursor: pointer;
    }
    .linkImg{
        width: 72px;
        height:72px;
        position: relative;
        display: inline-block;
        border-radius: 4px;
        margin-right: 8px;
        margin-bottom: 5px;
        object-fit: cover;
    }
    .linkImg span{
        position: absolute;
        right:5px;
        top:5px;
        font-size:16px
    }
    .linkImg img{
        width: 72px;
        height:72px;
        border-radius: 4px;
        object-fit: cover;
    }
    .scroll-box::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius   : 10px;
        background-color: #ccc;
        background-image: none
    }
    .scroll-box::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0);
        background   : #fff;
        border-radius: 10px;
        border:none
    }
    .img32{
        width:32px;
        height:32px;
        border-radius:50%;
        object-fit: fill;
    }
    .link{
        background: #F7F7F8;
        border-radius: 4px;
        cursor: pointer;
        margin-right:16px
    }
    .p0{
        padding:0
    }
    .yellowColor{
        background-color:#FDF8F1
    }
    .imgLi{
        list-style: none;
        padding-left: 0;
    }
    .imgLi li{
        display:inline-block
    }
    .viewer-prev::before, .viewer-next::before{
        background-image: none !important;
    }
</style>
<div class="container-fluid"  id='view' v-cloak>
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site','质检系统'), array('indexSchool'))?></li>
        <li class="active">质检总览</li>
    </ol>
    <div class="row" v-if='recordList.template'>
        <div class='col-md-12 col-sm-12' >
            <div v-for='(list,index) in recordList.template'>
                <div class='title'>
                    <div class='blueLine'></div>
                    <span class='font14 colorBlue fontBold ml8 lineHeight'>{{list.title}}</span> 
                </div>
                <div class='mt24'>
                    <el-table
                        :header-cell-style="renderHeader"
                        :data="list.standards"
                        header-align='center'
                        style="width: 100%">
                        <el-table-column
                        type="index"
                        label="序号"
                        header-align='center'
                        fixed
                        width="50">
                        </el-table-column>
                        <el-table-column
                        prop="number"
                        fixed
                        header-align='center'
                        label="整改项"
                        width="80">
                        </el-table-column>
                        <el-table-column label="检查纪实" header-align='center' class='yellowColor'>
                            <el-table-column
                                prop="name"
                                header-class-name="yellowColor"
                                label="描述"
                                header-align='center'
                                width="300">
                                <template  slot-scope="scope">
                                    <div v-html='html(record[scope.row.sub_attach_id].record)'></div>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="name"
                                label="附件"
                                header-align='center'
                                width="300">
                                <template  slot-scope="scope">
                                    <ul class='imgLi'  :id='"recordimg_"+scope.row.sub_attach_id'  v-if='record[scope.row.sub_attach_id].attach.img'>
                                        <li v-for='(item,idx) in record[scope.row.sub_attach_id].attach.img'>
                                            <img :src="item.url" alt="" class='mr8 mb8 linkImg' @click='showImg(scope.row.sub_attach_id,record[scope.row.sub_attach_id].attach.img,"record")' alt=""  >
                                        </li>
                                    </ul>
                                    <div  v-if='record[scope.row.sub_attach_id].attach.link' class='mt16'>
                                        <div class='link linkFile mb10' v-for='(item,idx) in record[scope.row.sub_attach_id].attach.link'>
                                            <span class='el-icon-paperclip font16'></span>
                                            <a class='flex1 colorBlue ml5' :href='item.url' target="_blank">{{item.name}}</a>
                                        </div>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column
                                prop="name"
                                header-align='center'
                                label="预计完成时间"
                                width="110">
                                <template  slot-scope="scope">
                                    {{record[scope.row.sub_attach_id].envision_day}}
                                </template>
                            </el-table-column>
                        </el-table-column>
                        <el-table-column label="校园整改" header-align='center'  :header-style="{ backgroundColor: '#F2F9F2' }">
                            <el-table-column
                            prop="province"
                            label="整改措施"
                            header-align='center'
                            width="300">
                                <template  slot-scope="scope">
                                    <div v-html='html(rectify[scope.row.sub_attach_id].school_action)'></div>
                                </template>
                            </el-table-column>
                            <el-table-column
                            prop="city"
                            header-align='center'
                            label="整改后资料"
                            width="300">
                                <template  slot-scope="scope">
                                    <ul class='imgLi'  :id='"rectifyimg_"+scope.row.sub_attach_id'  v-if='rectify[scope.row.sub_attach_id].attach.img'>
                                        <li v-for='(item,idx) in rectify[scope.row.sub_attach_id].attach.img'>
                                            <img :src="item.url" alt="" class='mr8 mb8 linkImg' @click='showImg(scope.row.sub_attach_id,rectify[scope.row.sub_attach_id].attach.img,"rectify")' alt=""  >
                                        </li>
                                    </ul>
                                    <div  v-if='rectify[scope.row.sub_attach_id].attach.link' class='mt16'>
                                        <div class='link linkFile mb10' v-for='(item,idx) in rectify[scope.row.sub_attach_id].attach.link'>
                                            <span class='el-icon-paperclip font16'></span>
                                            <a class='flex1 colorBlue ml5' :href='item.url' target="_blank">{{item.name}}</a>
                                        </div>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column
                            prop="address"
                            header-align='center'
                            label="完成时间"
                            width="100">
                                <template  slot-scope="scope">
                                    {{rectify[scope.row.sub_attach_id].school_done_date}}
                                </template>
                            </el-table-column>
                            <el-table-column
                            prop="zip"
                            label="审核确认"
                            header-align='center'
                            width="250">
                                <template slot-scope="scope">
                                    <div v-if='rectify[scope.row.sub_attach_id].leader_status!=99'>
                                        <div class='flex' v-if='rectify[scope.row.sub_attach_id].leader_status==1'>
                                            <img class='img32' :src="recordList.teacher_info[rectify[scope.row.sub_attach_id].school_leader].photoUrl" alt="">
                                            <div class='flex1 ml8'>
                                                <div class='font14 color3 mt5'>{{recordList.teacher_info[rectify[scope.row.sub_attach_id].school_leader].name}}</div>
                                                <el-tag size="mini">园长确认</el-tag>
                                            </div>
                                        </div>
                                        <div v-else><el-tag size="mini">园长待确认</el-tag></div>
                                    </div>

                                    <div v-if='rectify[scope.row.sub_attach_id].PD_status!=99'>
                                        <div class='flex mt20' v-if='rectify[scope.row.sub_attach_id].PD_status==1'>
                                            <img class='img32' :src="recordList.teacher_info[rectify[scope.row.sub_attach_id].PD_user].photoUrl" alt="">
                                            <div class='flex1 ml8'>
                                                <div class='font14 color3  mt5'>{{recordList.teacher_info[rectify[scope.row.sub_attach_id].PD_user].name}}</div>
                                                <el-tag size="mini">DP确认</el-tag>
                                            </div>
                                        </div>
                                        <div v-else class='mt20'><el-tag size="mini">DP待确认</el-tag></div>
                                    </div>
                                </template>
                            </el-table-column>
                        </el-table-column>
                        <el-table-column label="总部审核" header-align='center'  :header-style="{ backgroundColor: '#F0F5FB' }">
                            <el-table-column
                            prop="address"
                            header-align='center'
                            label="复检情况"
                            width="100">
                                <template  slot-scope="scope">
                                    <div v-if='rectify[scope.row.sub_attach_id].department_status==1 && rectify[scope.row.sub_attach_id].KMG_status==1'>合格</div>
                                    <div v-else-if='rectify[scope.row.sub_attach_id].department_status==1 && rectify[scope.row.sub_attach_id].KMG_status==99'>合格</div>
                                    <div v-else-if='rectify[scope.row.sub_attach_id].department_status==99 && rectify[scope.row.sub_attach_id].KMG_status==1'>合格</div>
                                    <div v-else>待全部确认</div>
                                </template>
                            </el-table-column>
                            <el-table-column
                            prop="zip"
                            header-align='center'
                            label="审核确认"
                            width="250">
                                <template slot-scope="scope">
                                    <div v-if='rectify[scope.row.sub_attach_id].department_status!=99'>
                                        <div class='flex' v-if='rectify[scope.row.sub_attach_id].department_status==1'>
                                            <img class='img32' :src="recordList.teacher_info[rectify[scope.row.sub_attach_id].department_user].photoUrl" alt="">
                                            <div class='flex1 ml8'>
                                                <div class='font14 color3  mt5'>{{recordList.teacher_info[rectify[scope.row.sub_attach_id].department_user].name}}</div>
                                                <el-tag size="mini">部门确认</el-tag>
                                            </div>
                                        </div>
                                        <div v-else><el-tag size="mini">部门待确认</el-tag></div>
                                    </div>
                                    <div v-if="rectify[scope.row.sub_attach_id].KMG_status != 99">
                                        <div class='flex mt20' v-if='rectify[scope.row.sub_attach_id].KMG_status==1'>
                                            <img class='img32' :src="recordList.teacher_info[rectify[scope.row.sub_attach_id].KMG_user].photoUrl" alt="">
                                            <div class='flex1 ml8'>
                                                <div class='font14 color3 mt5'>{{recordList.teacher_info[rectify[scope.row.sub_attach_id].KMG_user].name}}</div>
                                                <el-tag size="mini">KMG确认</el-tag>
                                            </div>
                                        </div>
                                        <div v-else class='mt20'><el-tag size="mini">KMG待确认</el-tag></div>
                                    </div>
                                </template>
                            </el-table-column>
                            </el-table-column>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </div>
    </div>
    <div style='position: fixed; z-index: 10000;display:none' class='nextImg'>
        <div id='prev' class='viewer-prev'></div>
        <div id='next' class='viewer-next'></div>
    </div>
</div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    
    var isHQstaff = '<?php echo $isHQstaff ?>';
    var id = '<?php echo $_GET['id']?>';
    var  container = new Vue({
        el: "#view",
        data: {
            id:id,
            recordList:[],
            rectify:{},
            record:{}
        },
        created: function() {
            this.initData()
        },
        computed: {
            
        },
        methods: {
            html(data){
                if(data!='' && data!=null){
                    return  data.replace(/\n/g, '<br/>')
                }
            },
            initData(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getQualityInspectSummary") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        tasks_id:this.id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            console.log(data)
                            that.recordList=data.data
                            that.rectify=data.data.rectify
                            that.record=data.data.record
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })  
            },
            showImg(id,list,type){
                var element=type+'img_'+id
                var viewer = new Viewer(document.getElementById(element),{
                    fullscreen: false,
                    title:false,
                    show:function(){
                        $('.nextImg').show()
                        if(list.length==1){
                            $('.viewer-prev').hide()
                            $('.viewer-next').hide()
                        }
                    },
                    hide:function(){ 
                        viewer.destroy()
                        $('.nextImg').hide()

                    }
                });
                document.getElementById('next').onclick = function() {
                    viewer.next();
                }
                document.getElementById('prev').onclick = function() {
                    viewer.prev();
                }
                $("#"+id).click();
            },
            renderHeader({ row, column, rowIndex, columnIndex }) { 
                
                if (rowIndex === 0) {
                    if (columnIndex === 0 || columnIndex === 1) {
                    return 'background-color: #F7F7F8; color: #333;'
                    } else if (columnIndex === 2) {
                    return 'background-color: #FDF8F1; color: #333;'
                    } else if (columnIndex === 3) {
                    return 'background-color: #F2F9F2; color: #333;'
                    } else if (columnIndex === 4) {
                    return 'background-color: #F0F5FB; color: #333;'
                    }
                } 
                if (rowIndex === 1) {
                    if (columnIndex === 0 || columnIndex === 1 || columnIndex === 2 ) {
                    return 'background-color: #FDF8F1; color: #333;'
                    } else if (columnIndex === 3 || columnIndex === 4 || columnIndex === 5 || columnIndex === 6) {
                    return 'background-color: #F2F9F2; color: #333;'
                    } else if (columnIndex === 7 || columnIndex === 8) {
                    return 'background-color: #F0F5FB; color: #333;'
                    }
                }
            },
        },
    })
    
</script>
