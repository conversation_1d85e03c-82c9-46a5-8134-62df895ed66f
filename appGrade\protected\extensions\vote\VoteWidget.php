<?php
/*
 * 结构参见 http://www.cnblogs.com/davidhhuan/archive/2012/01/09/2316851.html
*/
class VoteWidget extends CWidget {

	/*
	 * vote ID
	*/
	public $voteId = 0;
	/*
	 * 孩子ID
	*/
	public $childId = 0;

	/*
	 * 资源(images css js)的主目录
	*/
	public $assetsHomeUrl = null;
	/*
	 * 脚本文件 true 为默认 null 为不加载脚本文件
	*/
	public $scriptFile = true;
	/*
	 * css 文件 true 为默认 null 为不加载css文件
	*/
	public $cssFile = true;
	/*
	 * 视图文件名称
	*/
	public $view = 'vote';
	/*
	 * 初始化 设定属性的默认值
	* 此方法会被 CController::beginWidget() 调用
	*/
	public function init() {

		Yii::import('common.models.vote.*');
		Mims::LoadHelper('HtoolKits');
		if(empty($this->childId)){
			$this->childId  = $this->getController()->getChildId();
		}
		$cs = Yii::app()->getClientScript();
		if(empty($this->assetsHomeUrl)){
			$this->assetsHomeUrl = Yii::app()->getAssetManager()->publish(dirname(__FILE__).'/assets',false, -1, YII_DEBUG);
		}
		if($this->scriptFile == true){
			$this->scriptFile = '';
		}
		if($this->cssFile == true){
			$this->cssFile = 'votebox.css';
		}
		if(!empty($this->scriptFile)){
			$cs->registerScriptFile($this->assetsHomeUrl.'/js/'.$this->scriptFile,CClientScript::POS_HEAD);
		}
		// 这俩 js 投票时（未投票） 和 查看投票时（已投票）都需要
		$cs->registerScript('boxover_image_url',"var boxover_image_url = '".$this->assetsHomeUrl."'",CClientScript::POS_HEAD);
		$cs->registerScriptFile($this->assetsHomeUrl.'/js/boxover.js',CClientScript::POS_HEAD);
		$cs->registerScriptFile($this->assetsHomeUrl.'/js/horizontal_histogram.js',CClientScript::POS_HEAD);
		if(!empty($this->cssFile)){
			//$cs->registerCssFile($this->assetsHomeUrl.'/css/'.$this->cssFile);
//			$cs->registerCssFile(Yii::app()->theme->baseUrl.'/css/vote_survey.css');
			$cs->registerCssFile(Yii::app()->theme->baseUrl.'/css/vote_survey.css?t='.Yii::app()->params['refreshAssets']);
		}

	}

	/*
	 * 此方法会被 CController::endWidget() 调用
	*/
	public function run() {

		$vi_cri = new CDbCriteria();
		$vi_cri->compare('id', $this->voteId);
		$vi_cri->compare('whether_show', 0);
		$vi_cri->limit = 1;
		// 投票的基本信息
		$vote_info = WVoteBasicInfo::model()->with('options')->find($vi_cri);
		$isCheckBox = false;
		if(!empty($vote_info)){
			$vr_model = WVoteResult::model();
			if($vote_info->vote_model == 1){
				$operate_id = Yii::app()->user->id;
			}else{
				$operate_id = $this->childId;
			}
			$vote_info->operate_id = $operate_id;
			if($vote_info->option_number > 1){
				$isCheckBox = true;
			}
			// 总票数
			$vote_info->votesCasted =$vr_model->count('vote_id='.$this->voteId);
			// 投票总人数 注意此sql语句 用 dao 是不是应该更合适一些
			$tv_cri = new CDbCriteria();
			$tv_cri->compare('t.vote_id', $this->voteId);
			$tv_cri->select = 'DISTINCT(operate_id)';
			$tv_cri->group = 'operate_id';
			$vote_info->totalVoters =$vr_model->count($tv_cri);

			//选项 json 格式
			$vo_list_json = array();
			if(!empty($vote_info->options)){
				$tk = new HtoolKits();
				$option_ids = null;
				foreach ($vote_info->options as $index => $option) {
					$vo_list_json[$index]['id'] = $option->id;
					$vo_list_json[$index]['header_title'] = Yii::t("vote", 'Detail Info');
					$vo_list_json[$index]['option_colon'] = $tk->getContentByLang('：',':');
					$vo_list_json[$index]['title'] = $tk->getContentByLang($option->title_cn, $option->title_en);
					$vo_list_json[$index]['desc'] = $tk->getContentByLang($option->detail_description_cn, $option->detail_description_en);
					if(empty($vo_list_json[$index]['desc'])){
						$vo_list_json[$index]['desc']  = $vo_list_json[$index]['title'];
					}
					$option_ids[] = $option->id;
				}
				$optionCounts = $vr_model->getOptionCounts($option_ids);
			}

			$wv_cri = new CDbCriteria();
			$wv_cri->compare('t.vote_id', $this->voteId);
			$wv_cri->compare('t.operate_id', $operate_id);
			// 投票结果
			$result_info = $vr_model->findAll($wv_cri);
			//时间是否符合
			$is_voted = false;
			$voted_time = null;
			$voted_result = array();
			$invalid_time = true;
			$operate_disabled_text = '';
			// 如果为空 就是还没有投票
			if(empty($result_info)){
				$current_time = time();
				//当前时间大于开始时间    当前时间小于截止时间
				$end_time = $vote_info->end_time + (3600*24); // 加一天
				if($current_time>$vote_info->start_time && $current_time<$end_time){
					$invalid_time = false;
				}else{
					//时间无效 不能操作
					$operate_disabled_text = "disabled='disabled'";
				}
			}else{
				// 已经投票
				$is_voted = true;
				$operate_disabled_text = "disabled='disabled'";

				foreach ($result_info as $result) {
					$voted_result[] = $result->result;
					if(empty($voted_time) && !empty($result->vote_time)){
						$voted_time = Mims::formatDateTime($result->vote_time,'medium','short');
					}
				}
			}

			if(!empty($vo_list_json)){
				$is_checked_html = '';
				foreach ($vo_list_json as $index => $info) {
					if(isset($optionCounts[$info['id']])){
						$vo_list_json[$index]['result_count'] = $optionCounts[$info['id']];
					}else{
						$vo_list_json[$index]['result_count'] = 0;
					}
					if(in_array($info['id'], $voted_result)){
						$is_checked_html = ' checked="checked" ';
					}else{
						$is_checked_html = '';
					}
					// 准备操作按钮
					if(true == $isCheckBox){
						$vo_list_json[$index]['operate_input_html'] = '<input id="vote_option_'.$info['id'].'" type="checkbox" onclick="javascript:validateCheckedNum(this,'.$vote_info->option_number.');" value="'.$info['id'].'" name="vote_option[]" '.$operate_disabled_text.$is_checked_html.'>';
					}else{
						if($index == 0 && empty($operate_disabled_text)){
							$vo_list_json[$index]['operate_input_html'] = '<input id="vote_option_'.$info['id'].'" type="radio" value="'.$info['id'].'" name="vote_option" checked="checked">';
						}else{
							$vo_list_json[$index]['operate_input_html'] = '<input id="vote_option_'.$info['id'].'" type="radio" value="'.$info['id'].'" name="vote_option" '.$operate_disabled_text.$is_checked_html.'>';
						}
					}
				}
			}
		}

		// 输出 变量到 视图文件
		$view_data = array(
				'vote_info'=>$vote_info,
				'option_count'=>count($vo_list_json),
				'vo_list_json'=>CJSON::encode($vo_list_json),
				'is_voted'=>$is_voted,
				'voted_time'=>$voted_time,
				'invalid_time'=>$invalid_time,
		);

		$this->render($this->view,$view_data);
	}

}