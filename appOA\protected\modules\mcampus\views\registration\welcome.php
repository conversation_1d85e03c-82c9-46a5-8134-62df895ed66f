<?php
$baseContentCn = ($model->content_cn) ? json_decode($model->content_cn) : '' ;
$model->welcome_cn = ($baseContentCn) ? nl2br(base64_decode($baseContentCn->welcome)) : '' ;

$baseContentEn = ($model->content_en) ? json_decode($model->content_en) : '' ;
$model->welcome_en = ($baseContentEn) ? nl2br(base64_decode($baseContentEn->welcome)) : '' ;
$form=$this->beginWidget('CActiveForm', array(
    'id'=>'courseGroup-form',
    'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
    'action' => $this->createUrlReg('addConfig', array('step' => $stepId)),
)); ?>
<div class="form-group">
    <label class="col-xs-1 control-label"><?php echo $form->labelEx($model,'welcome_cn'); ?></label>
    <div class="col-xs-9">
        <?php echo $form->hiddenField($model,'id'); ?>
        <?php
        $this->widget('common.extensions.ueditor.ueditorfull',array(
            'model' => $model,
            'attribute' => 'welcome_cn',
            'configFile' => 'ueditor.regist.config',
            'language' => 'zh-cn',
            'editorOptions' => array('initialFrameHeight'=>200),
        ));
        ?>
    </div>
</div>
<div class="form-group">
    <label class="col-xs-1 control-label"><?php echo $form->labelEx($model,'welcome_en'); ?></label>
    <div class="col-xs-9">
        <?php
        $this->widget('common.extensions.ueditor.ueditorfull',array(
            'model' => $model,
            'attribute' => 'welcome_en',
            'configFile' => 'ueditor.regist.config',
            'language' => 'en',
            'editorOptions' => array('initialFrameHeight'=>200),
        ));
        ?>
    </div>
</div>
<div class="modal-footer col-xs-10" >
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
</div>
<?php $this->endWidget(); ?>
