<?php
class OverdueEmailController extends TeachBasedController
{
    public function createUrlReg($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $schoolList = array('BJ_DS','BJ_SLT','BJ_QFF');
        foreach ($this->accessBranch as $key => $item) {
            if (!in_array($item, $schoolList)) {
                unset($this->accessBranch[$key]);
            }
        }
        //初始化选择校园页面
        $this->branchSelectParams['urlArray'] = array('//superman/overdueEmail/index','type'=>'not_overdue');
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl() . '/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/vue.global.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/tinymce/tinymce.min.js');
    }

    //展示页面
    public function actionIndex()
    {
        $res = CommonUtils::requestDsOnline('billings/getOverdueEmailTip',array(
            'school_id'=>$this->branchId
        ));
        $bankinfo = BranchVar::model()->bankInfo($this->branchId);
        $this->render('index', array(
            'data' => $res['data'],
            'bankinfo'=>$bankinfo,
        ));
    }

    public function actionSetOverdueEmailTip()
    {
        $email_view = Yii::app()->request->getParam("email_view");
        $comment_type = Yii::app()->request->getParam("comment_type");
        $res = CommonUtils::requestDsOnline('billings/setOverdueEmailTip',array(
            'school_id'=>$this->branchId,
            'comment_type'=>$comment_type,
            'email_view'=>$email_view
        ));
        if($res){
            $this->addMessage('state', 'success');
            $this->addMessage('message', '成功');
            $this->addMessage('data', $res);
            $this->showMessage();
        }
        $this->addMessage('state', 'fail');
        $this->addMessage('message', '失败');
        $this->addMessage('data', $res);
        $this->showMessage();
    }

}