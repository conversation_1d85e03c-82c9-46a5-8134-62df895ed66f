<div class="table_list">
	<table width="100%">
		<colgroup>
			<col width="300">
			<col width="100">
			<col width="100">
			<col width="100">
			<col width="200">
		</colgroup>
		<thead>
			<tr>
				<td>账单标题</td>
				<td>需支付金额</td>
				<td>将使用押金</td>
				<td>折前金额</td>
				<td>关联账单</td>
				<td>操作</td>
			</tr>
		</thead>
		<tbody>
			<?php foreach($invoices as $invoice):?>
			<tr id="invoice_<?php echo $invoice->invoice_id?>">
				<td><?php echo $invoice->title;?></td>
				<td><?php echo $invoice->amount;?></td>
				<td><?php echo ($invoice->original_amount - $invoice->amount < 0.001)? 'N/A': $invoice->original_amount - $invoice->amount;?></td>
				<td><?php echo ($invoice->nodiscount_amount < 0.001) ? 'N/A' : $invoice->nodiscount_amount;?></td>
				<td></td>
				<td>
					<?php echo CHtml::link(Yii::t("payment","详细"), array('/child/invoice/viewInvoice', 'invoiceid'=>$invoice->invoice_id),array('class'=>'mr5', 'target'=>'_blank'));?>
					<?php echo CHtml::link(Yii::t("payment","作废"), 'javascript:;', array('class'=>'mr5', 'onclick'=>'cancelInvoice('.$invoice->invoice_id.')'));?>
					<?php echo CHtml::link(Yii::t("payment","打印"), array('/child/invoice/printInvoice', 'invoice_id'=>$invoice->invoice_id), array('class'=>'mr5', 'target'=>'_blank'));?>
				</td>
			</tr>			
			<?php endforeach;?>
		</tbody>
	</table>
</div>