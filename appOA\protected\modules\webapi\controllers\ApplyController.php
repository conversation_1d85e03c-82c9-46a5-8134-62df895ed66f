<?php
/**
 * easyapply 远程调用
 */
class ApplyController extends Controller
{
    protected $securityKey = 'b09f412314bab7c88cb853004b564651';
    protected $tenantId = 0;

    public function __construct($id, $module = null)
    {
        parent::__construct($id, $module);
        $this->tenantId = Yii::app()->request->getParam('tenantId');
        $token = Yii::app()->request->getParam('token');

        if (!$this->tenantId || !$token) {
            $this->module->_sendResponse(403);
        }
        if ($token != md5($this->tenantId . $this->securityKey)) {
            $this->module->_sendResponse(403);
        }
    }

    public function actionPaidApplyIds()
    {
        $startYear = Yii::app()->request->getParam('startYear');
        $this->addMessage('state', 'fail');
        if (!$startYear) {
            $this->addMessage('message', 'startYear not null');
            $this->showMessage();
        }
        $schoolIds = $this->getSchoolIdsByTenantId($this->tenantId);
        $data = array();
        if (!$schoolIds) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
            $this->showMessage();
        }
        // 获取校历列表
        Yii::import('common.models.calendar.CalendarSchool');
        $crit = new CDbCriteria();
        $crit->compare('branchid', $schoolIds);
        $crit->compare('startyear', $startYear);
        $crit->index = 'yid';
        $calendarModel = CalendarSchool::model()->findAll($crit);
        $calendarIdList = array_keys($calendarModel);
        if (!$calendarIdList) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
            $this->showMessage();
        }
        Yii::import('common.models.easyapply.EasyapplyTransfer');
        $crit = new CDbCriteria();
        $crit->compare('apply_year', $startYear);
        $crit->compare('status', 1);
        $transferModel = EasyapplyTransfer::model()->findAll($crit);

        $applyIds2childIds = array();
        foreach ($transferModel as $model) {
            $applyData = json_decode($model->apply_data, TRUE);
            if (isset($applyData['schoolid']) && in_array($applyData['schoolid'], $schoolIds)) {
                $applyIds2childIds[$model->apply_id] = $model->child_id;
            }
        }
        $childIds = array_values($applyIds2childIds);
        // 查找已支付学费的孩子ID
        Yii::import('common.models.invoice.Invoice');
        $crit = new CDbCriteria();
        $crit->compare('status', Invoice::STATS_PAID);
        $crit->compare('calendar_id', $calendarIdList);
        $crit->compare('childid', $childIds);
        $crit->index = 'childid';
        $invoiceModel = Invoice::model()->findAll($crit);
        $paidChildIds = array_keys($invoiceModel);
        // 已支付的申请ID
        $paidApplyIds = array();
        foreach ($applyIds2childIds as $applyId => $childId) {
            if (in_array($childId, $paidChildIds)) {
                $paidApplyIds[] = $applyId;
            }
        }

        $this->addMessage('state', 'success');
        $this->addMessage('data', $paidApplyIds);
        $this->showMessage();
    }

    public function getSchoolIdsByTenantId($tenantId)
    {
        $array = array(
            '1' => array('BJ_DS', 'BJ_SLT', 'BJ_QFF'),
            '21991' => array('BJ_DS', 'BJ_SLT', 'BJ_QFF',),
            '21990' => array('BJ_CP','BJ_IASLT','BJ_OE','BJ_OG-PH','TJ_EB','CD_LT','CD_LH','NB_HH','NB_FJ','XA_GJ','XA_LB',),
        );
        return isset($array[$tenantId]) ? $array[$tenantId] : array();
    }
}
