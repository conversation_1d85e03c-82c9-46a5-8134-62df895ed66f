<!-- Modal -->
<?php 
    $labels = Invoice::model()->attributeLabels();
?>
<?php echo CHtml::form('/workflow/entrance/index', 'post',array('class'=>'J_ajaxForm'));?>
<div class="modal fade" id="<?php echo $data['modalNameId']?>" tabindex="-1" role="dialog" aria-labelledby="workflowModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel"><?php echo CommonUtils::autoLang($data['workflow']->definationObj->defination_name_cn,$data['workflow']->definationObj->defination_name_en);?> - <?php echo CommonUtils::autoLang($data['workflow']->nodeObj->node_name_cn,$data['workflow']->nodeObj->node_name_cn);?><a href="javascript:void(0)" id="print"> [打印]</a></h4>
            </div>
            <div class="modal-body" id="workflow-modal-body">
                <div class="alert alert-success" role="alert">
                    <span class="sr-only">Refund:</span>
                    [ <?php echo CHtml::link($data['profile']->getChildName(), $this->createUrl('//child/invoice/finance',array('t'=>'invoice','childid'=>$data['workflow']->getOperateObjId())),array('target'=>'_blank'));?> ] <?php echo $data['type'][$data['workflow']->getOperateValue('exte3')];?>
                </div>
                <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <td><?php echo $labels['title'];?></td>
                            <td width="100"><?php echo $labels['payment_type'];?></td>
                            <td><?php echo $labels['amount'];?></td>
                            <td><?php echo $labels['startdate'];?></td>
                            <td><?php echo $labels['enddate'];?></td>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($data['refundList'] as $invoice):?>
                        <tr>
                            <td><?php echo $invoice->Invoice->title;?></td>
                            <td><?php echo $invoice->Invoice->payment_type;?></td>
                            <td><?php echo $invoice->Invoice->amount;?></td>
                            <td><?php echo OA::formatDateTime($invoice->Invoice->startdate);?></td>
                            <td><?php echo OA::formatDateTime($invoice->Invoice->enddate);?></td>
                        </tr>
                        <tr class="active">
                            <td colspan="2">
                                <div class="form-group" style="width: 250px;">
                                    退费标题：<?php echo $invoice->title;?>
                                </div>
                                <div class="form-group" style="width: 250px;">
                                    退费金额：<span style="color:#cc0001"><?php echo number_format($invoice->amount,2);?></span>
                                </div>
                            </td>
                            <td colspan="3">
                                <div class="form-group" style="width: 250px;">
                                    开始日期：<?php echo OA::formatDateTime($invoice->startdate);?>
                                </div>
                                <div class="form-group" style="width: 250px;">
                                    开始日期：<?php echo OA::formatDateTime($invoice->enddate);?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach;?>
                    </tbody>
                </table>
                </div>
                <div class="retshow table-responsive">
                    <h3><?php echo Yii::t('workflow', 'Comments:'); ?></h3>
                     <?php 
                        $processList = $data['workflow']->getWorkflowProcess();
                        foreach ($processList as $process):?>
                    <h4>
                        <em><?php echo Yii::t('workflow', 'Comments:');?>： <?php echo OA::formatDateTime($process->start_time);?></em>
                        <span><?php echo $process->user->getName();?></span>
                    </h4>
                    <div class="clearfix"></div>
                    <div class="comment">
                        <label>意见</label>
                        <p><?php echo nl2br(htmlspecialchars($process->process_desc));?></p>
                    </div>
                    <?php endforeach;?>
                </div>
                <?php if ($data['workflow']->useRole && $data['workflow']->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_UNOP):?>
                <div class="form-group" style="padding-top:50px;">
                    <?php echo CHtml::textArea('memo','',array('class'=>'form-control','row'=>3));?>
                </div>
                <?php endif;?>
            </div>
            <?php 
            if ($data['workflow']->useRole && $data['workflow']->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_UNOP && !$data['workflow']->isFristNode()):?>
            <div class="modal-footer">
                <?php echo CHtml::hiddenField('definationId', $data['workflow']->getWorkflowId());?>
                <?php echo CHtml::hiddenField('nodeId', $data['workflow']->getCurrentNodeId());?>
                <?php echo CHtml::hiddenField('branchId', $data['workflow']->schoolId);?>
                <?php echo CHtml::hiddenField('operationId', $data['workflow']->getOperateId());?>
                <?php echo CHtml::hiddenField('action', 'show');?>
                <?php echo CHtml::hiddenField('buttonCategory','');?>
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('workflow', 'Cancel');?></button>
                <button type="button" class="btn btn-danger J_ajax_submit_btn" data-category="<?php echo WorkflowOperation::WORKFLOW_STATS_OPDENY;?>"><?php echo Yii::t('workflow', 'No');?></button>
                <button type="button" class="btn btn-primary J_ajax_submit_btn" data-category="<?php echo WorkflowOperation::WORKFLOW_STATS_OPED;?>"><?php echo Yii::t('workflow', 'Yes');?></button>
            </div>
            <?php endif;?>
        </div>
    </div>
</div>
<?php CHtml::endForm();?>
<script type="text/javascript">
    //打印
    $(function(){  
         $("#print").click(function(){
            $("#workflow-modal-body a").removeAttr('href');
            $("#workflow-modal-body").printThis();
         })    
    }); 
</script>