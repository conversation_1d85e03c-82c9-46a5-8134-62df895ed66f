<?php

class IndexController extends ProtectedController
{
	public $layout="//layouts/main";
	public function actionIndex()
	{
		$this->render('index');
	}
	
	public function actionIndex1()
	{
		Mims::Nav();
		$criteria=new CDbCriteria;
		
		$criteria->compare('childid', $this->childid);
		$criteria->compare('status', '<>99');
		//$criteria->order = 'timestamp DESC';
		$provider=new CActiveDataProvider('invoice',array(
				'criteria'   => $criteria,
				'pagination' => array('pageSize'=>5)
		));
		
		$this->render('index',array('provider'=>$provider));
	}	
	
	public function actionIndex2()
	{
		
		$criteria=new CDbCriteria;
		
		$criteria->compare('childid', $this->childid);
		$criteria->compare('status', '<>99');
		//$criteria->order = 'timestamp DESC';
		$provider=new CActiveDataProvider('invoice',array(
				'criteria'   => $criteria,
				'pagination' => array('pageSize'=>5)
		));
		
		$this->render('index',array('provider'=>$provider));
	}	

	public function actionIndex3()
	{
		
		$criteria=new CDbCriteria;
		
		$criteria->compare('childid', $this->childid);
		$criteria->compare('status', '<>99');
		//$criteria->order = 'timestamp DESC';
		$provider=new CActiveDataProvider('invoice',array(
				'criteria'   => $criteria,
				'pagination' => array('pageSize'=>5)
		));
		
		$this->render('index',array('provider'=>$provider));
	}	
	
	public function actionRoletest($role)
	{
		$params["childid"] = $this->childid;
		if(Yii::app()->user->checkAccess($role,$params))
		{
			echo CJSON::encode(array("result"=>"success","ret"=>"You have the privilege"));
			Yii::app()->end();		
		}else{
			echo CJSON::encode(array("result"=>"success","ret"=>"Illegal request!"));
			Yii::app()->end();					
		}
	}

	
	public function actionChangetheme($theme)
	{
		$theme = strtolower(trim($theme));
		$cookiename = "child_mims_theme";
		if(in_array($theme, array("green","classic"))){
			unset(Yii::app()->request->cookies[$cookiename]);
			$cookie = new CHttpCookie($cookiename, $theme);
			Yii::app()->request->cookies[$cookiename]=$cookie;			
			
			echo CJSON::encode(array("result"=>"success","ret"=>$theme));
			Yii::app()->end();		
		}
	}
	
}