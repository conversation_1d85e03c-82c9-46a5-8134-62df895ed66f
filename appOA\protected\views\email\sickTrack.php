<?php $style = ' style="font-family: \'Microsoft Yahei\';font-size:12px;"'; ?>
<div <?php echo $style; ?>>
    <table border='1' cellpadding='2' cellspacing='0' width="100%">
        <tr>
            <th width="100" <?php echo $style; ?>></th>
            <th width="500" <?php echo $style; ?>>老师追踪记录</th>
            <th width="500" <?php echo $style; ?>>保健医追踪记录</th>
        </tr>
        <?php
        foreach($classInfo as $resign): ?>
            <tr>
                <td  colspan="3" <?php echo $style; ?>><?php echo $resign['title']; ?></td>
            </tr>
            <?php foreach ($item[$resign['classid']] as $val): ?>
                <tr>
                    <td><?php
                        echo $val['childName'] ?></td>
                    <td><?php
                        if(isset($val['track']) && isset($val['track'][1])){
                            echo "<br>";
                            foreach ($val['track'][1] as $memo){
                                echo nl2br($memo['memo']) . ' - <span style="color:#909090">' . $memo['userName']  . ' ' . $memo['time'] .'</span><br><br>';
                            }
                        }
                        ?>
                    </td>
                    <td><?php
                        if(isset($val['track']) && isset($val['track'][2])){
                            echo "<br>";
                            foreach ($val['track'][2] as $memo){
                                echo nl2br($memo['memo']) . ' - <span style="color:#909090">' . $memo['userName']  . ' ' . $memo['time'] .'</span><br><br>';
                            }
                        }
                        ?>
                    </td>
                </tr>
            <?php endforeach;  ?>
        <?php
        endforeach; ?>
    </table>
</div>
