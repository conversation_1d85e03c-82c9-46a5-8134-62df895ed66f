<?php $this->renderPartial('tabViews/_operations', array('taskData'=>$taskData, 'descriptions'=>$descriptions));?>
<script>
    var roleAssignedTasks = <?php echo $roleAssignedTasks;?>;
    var separator = ',';
    var authsStr = roleAssignedTasks.join(separator)+separator;
    $('#task_operations input[type="checkbox"]').each(function(){
        if( authsStr.indexOf($(this).val()+separator) > -1 ){
            $(this).attr('checked', true);
        }
    });
    $('.J_check_all[name^="tauth"]').on('change', function(){
        var clist = $(this).attr('data-checklist');
        if($(this).attr('checked') == 'checked'){
            $('.J_check_wrap .J_check[data-xid="'+clist+'"]').attr('disabled', true);
        }
        else{
            $('.J_check_wrap .J_check[data-xid="'+clist+'"]').attr('disabled', false);
        }
    });
    $('.J_check_wrap .J_check').on('change', function(){
        var clist = $(this).attr('data-xid');
        var check_d = $('.J_check[data-xid="'+clist+'"]');
        var check_ed = $('.J_check[data-xid="'+clist+'"]:checked');
        if(check_d.length === check_ed.length){
            $('.J_check_wrap .J_check[data-xid="'+clist+'"]').attr('disabled', true);
        }
        else{
            $('.J_check_wrap .J_check[data-xid="'+clist+'"]').attr('disabled', false);
        }
    });
</script>