<div class="background-gray">
    <div class="btn-group p8">
        <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
            入离职登记<span class="caret"></span>
        </button>
        <ul class="dropdown-menu" role="menu">
            <li><a href="<?php echo Yii::app()->controller->createUrl("//mcampus/staff/index",array('category'=>'update')); ?>">入职登记</a></li>
            <li class="divider"></li>
            <li><a href="javascript:void(0);" onclick="leaveApplication();">离职登记</a></li>
        </ul>
    </div>
</div>
<?php
$this->widget('ext.ivyCGridView.BsCGridView', array(
    'id' => 'points-product-grid',
    'dataProvider' => $dataProvider,
    'colgroups' => array(
        array(
            "colwidth" => array(100, 100, 80, 80),
        )
    ),
    'columns' => array(
        array(
            'name' => 'user_avatar',
            'value' => 'CHtml::image(Yii::app()->params["OAUploadBaseUrl"]."/users/thumbs/".$data->user_avatar, "", array("style"=>"width:46px;"))',
            'type' => 'raw',
            'sortable' => false,
        ),
        array(
            'name' => 'profile.first_name',
            'value' => '$data->profile->first_name." ".$data->profile->last_name',
        ),
        array(
            'name' => 'name',
            'sortable' => false,
        ),
        array(
            'name' => 'profile.user_gender',
            'value' => '$data->profile->getGender()',
        ),
        array(
            'name' => 'email',
            'sortable' => false,
        ),
        array(
            'name' => 'profile.nationality',
            'value' => 'Yii::app()->controller->cfg["country"][$data->profile->nationality]',
        ),
        array(
            'name' => Yii::t('labels', 'Position'),
            'value' => '$data->profile->occupation->getName()',
        ),
        array(
            'name' => 'profile.branch',
            'value' => 'Yii::app()->controller->cfg["branch"][$data->profile->branch]',
        ),
        array(
            'name' => '状态',
            'sortable' => false,
            'value' => 'Yii::app()->controller->cfg["level"][$data->level]',
        ),
        array(
            'class' => 'CButtonColumn',
            'template' => '{up} {check}',
            'buttons'=>array(
                'up'=>array(
                    'label'=>'<span class="glyphicon glyphicon-pencil"></span>',
                    'url' => 'Yii::app()->controller->createUrl("/mcampus/staff/index", array("category"=>"update","id"=>$data->uid))',
                    'imageUrl'=>false,
                    'options'=>array('class'=>'btn btn-primary btn-xs','title'=>Yii::t('global','Edit')),
                    'visible'=>'Yii::app()->user->checkAccess("o_StaffRegister",null,null)',
                ),
                'check'=>array(
                    'label'=>'<span class="glyphicon glyphicon-check"></span>',
                    'url'=>'Yii::app()->controller->createUrl("/mcampus/staff/check", array("id"=>$data->uid))',
                    'imageUrl'=>false,
                    'options'=>array('class'=>'btn btn-danger btn-xs','title'=>'审核'),
                    'visible'=>'Yii::app()->user->checkAccess("o_StaffApprove") || Yii::app()->user->checkAccess("o_StaffCreate")',
                ),
            ),
        )
    ),
));
?>

