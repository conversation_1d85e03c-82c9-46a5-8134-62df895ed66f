<?php

class MiscController extends ProtectedController
{
    public $actionAccessAuths = array(
        'admin'     => 'o_X_Access',
        'Confirm'   => 'o_X_Adm_Common',
        'Pack'      => 'o_X_Adm_Common',
        'Orderlist' => 'o_X_Access',
        'Send'      => 'o_X_Adm_Common',
        'Split'     => 'o_X_Adm_Common',
        'Godone'    => 'o_X_Adm_Common',
    );
    
    public $cAdmin = false;	//是否有校园管理权限
    public $oAdmin = false; //是否总部后勤部权限
    public $defaultAction = 'admin';
    public $dialogWidth=500;
    
    public function init() {
        parent::init();

        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'hqOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','HQ Operations');

        $this->oAdmin = Yii::app()->user->checkAccess('o_X_Adm_Common');
    }
    
    public function actionSelect()
	{
        $this->render('//layouts/common/branchSelect');
	}

    public function actionMailSender()
    {
        Yii::import('common.models.mailer.*');

        if ( isset($_POST['postMailForm']) && Yii::app()->request->isAjaxRequest ) {
            $postData = $_POST['MailRecipient'];
            if($postData['id']) {
                $modal = MailRecipient::model()->findByAttributes(
                    array(
                        'id'=>intval($postData['id']),
                        'branch_id'=>$postData['branch_id'],
                        'flag'=>$postData['flag']
                    ));
            }
            if(empty($modal)){
                $modal = new MailRecipient();
            }
            unset($postData['id']);
            $modal->setAttributes($postData);
            $modal->support_email = CJSON::encode($_POST['support_email']);
            if($modal->save()){
                $data['maildata'] = $modal->getAttributes();
                $data['maildata']['support_email'] = CJSON::decode($modal->support_email);
                $this->addMessage('state','success');
                $this->addMessage('message',Yii::t('message', 'Data Saved!'));
                $this->addMessage('callback', 'cbSaveMail');
                $this->addMessage('data', $data);
            }else{
                $this->addMessage('state','fail');
                $this->addMessage('message', 'Save failed.');
            }
            $this->showMessage();
        }

        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
        $mailIds = CommonUtils::LoadConfig('CfgMailId');

        $this->render('mailsender', array("mailIds"=>$mailIds));
    }

    public function actionFetchMailByCampusId()
    {
        if ( Yii::app()->request->isAjaxRequest && Yii::app()->request->isPostRequest ) {
            $branchId = $_POST['branchId'];
            if($branchId) {
                $data = array();
                $data['mails'] = array();
                Yii::import('common.models.mailer.*');
                $crit = new CDbCriteria();
                $crit->compare('branch_id', $branchId);
                $mails = MailRecipient::model()->findAll($crit);
                foreach($mails as $mail){
                    $data['mails'][$mail->flag] = $mail->getAttributes();
                    $data['mails'][$mail->flag]['support_email'] = CJSON::decode($mail->support_email);
                }

                $this->addMessage('state','success');
                $this->addMessage('data', $data);
                $this->showMessage();
            }
        }

        $this->addMessage('state','fail');
        $this->showMessage();
    }
}
