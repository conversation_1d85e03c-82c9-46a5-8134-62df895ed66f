<?php

/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
class VoucherController extends ProtectedController{
    /*
    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }
     * 
     */
     public function init(){
        if(!Yii::app()->user->checkAccess("oVisitAppAdmin")){
			throw new CException('Permission Denied');
		}		
        parent::init();
    }
    
    public function actionIndex(){
        Yii::import('common.models.invoice.InvoiceTransaction');
        Yii::import('common.models.invoice.Invoice');
        
        $_time = time();
        $startdate = Yii::app()->request->getPost('startdate',  date('Y-m-d',mktime(0,0,0,date('m',$_time),1,date('Y',$_time))));
        $enddate = Yii::app()->request->getPost('enddate',  date('Y-m-d',mktime(0,0,0,date('m',$_time),date('t',$_time),date('Y',$_time))));
        //获取各学校使用代金卷数据
        /*
        $crite = new CDbCriteria();
        $crite->compare('t.transactiontype',InvoiceTransaction::TYPE_VOUCHER);
        $crite->addBetweenCondition('timestampe', strtotime($startdate), strtotime($enddate));
        $crite->order = 't.timestampe ASC';
        InvoiceTransaction::model()->with(array('classInfo','childInfo','branchInfo'))->findAll($crite);
        */
        $tranList = Branch::model()->with(array('voucherInvoice'=>array('params'=>array(':transactiontype'=>InvoiceTransaction::TYPE_VOUCHER,':startdate'=>  strtotime($startdate),':enddate'=>  strtotime($enddate)))))->findAll();
        $this->render('index',array(
            'startdate'=>$startdate,
            'enddate'=>$enddate,
            'tranList'=>$tranList,
        ));
    }
    /*
    public function actionSelect()
	{
		$this->branchSelectParams["showList"] = true;
		$this->render('select');
	}
     * 
     */
}
?>
