<div class="container-fluid">
	 <ol class="breadcrumb">
	     <li><?php echo CHtml::link(Yii::t('site','HQ Operations'), array('default/index'));?></li>
	     <li><?php echo CHtml::link(Yii::t('site','Support'), array('default/index'));?></li>
	     <li class="active"><?php echo Yii::t('site','生成订单') ;?></li>
	 </ol>
	 <div class="row">
	    <div class="col-md-2">
	        <div class="list-group" id="classroom-status-list">
	            <a href="<?php echo $this->createUrl('purchaseProducts/index');?>" class="list-group-item status-filter ">商品管理</a>
	            <a href="<?php echo $this->createUrl('purchaseStandard/index');?>" class="list-group-item status-filter">标配管理</a>
	            <a href="<?php echo $this->createUrl('purchaseOrder/index');?>" class="list-group-item status-filter active">生成订单</a>
	            <a href="<?php echo $this->createUrl('purchaseOrder/orders');?>" class="list-group-item status-filter">订单管理</a>
	        </div>
	    </div>
	    <div class="col-md-10">
	        <div class="panel panel-default">
	            <div class="panel-body">
					<?php
                    $this->widget('ext.ivyCGridView.BsCGridView',array(
							'id'=>'purchase-order',
							'dataProvider'=>$application,
							'colgroups'=>array(
							    array(
							        "colwidth"=>array(100,100,100,100),
							    )
							),
							'columns'=>array(
								array(
									'name'=>'申请标题',
									'value'=>'$data->title',
									),
								array(
									'name'=>'申请学校',
                                    'value' => array($this, "getSchool"),
									//'value'=>'User::model()->findByPk($data->add_user)->profile->branch',
									),
								array(
									'name'=>'审批时间',
									'value'=>'date("Y-m-d", $data->update_timestamp)',
									),
								array(
									'name'=>'操作',
									'type'=>'raw',
									'value'=>'CHtml::Link("生成订单","",array("class"=>"J-ajax btn btn-xs btn-default","onclick"=>"getProducts($data->id)"))',
									),
								)
						));
					 ?>
	            </div>
	        </div>
	    </div>
	</div>
</div>
<!-- 模态框 -->
<div class="modal fade" id="modal" tabindex="-1" role="dialog" aria-labelledby="modal">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">

	        <div class="modal-footer">
	        	<button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
	        	<button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
	        </div>
        </div>
    </div>
</div>

<script>
	function getProducts (aid) {
	    $('#modal .modal-content').load('<?php echo $this->createUrl('getProducts') ?>'+'?aid='+aid,function () {
	        $('#modal').modal({
	          show: true,
	          backdrop: 'static'
	        });
	        head.Util.ajaxForm( $('#modal') );
	    });
	}
	// 回调：添加成功
	function cbOrder() {
	    $('#modal').modal('hide');
	    $.fn.yiiGridView.update('purchase-order-form');
	}
</script>