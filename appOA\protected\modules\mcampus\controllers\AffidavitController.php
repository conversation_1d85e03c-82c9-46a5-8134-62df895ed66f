<?php

class AffidavitController extends BranchBasedController
{
    public $childArr = array();
    public $classArr = array();
    public $covid19Info = array();
    public $printFW = array();
    public $batchNum = 100;
    public $actionAccessAuths = array(
        'Index'             => 'o_A_Access',
    );

    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";
        Yii::import('common.models.support.*');
        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/jquery.jPrintArea.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/xlsx.full.min.js');
        $this->branchSelectParams['urlArray'] = array('//mcampus/Affidavit/index');
    }

    public function actionIndex()
    {
        $type = Yii::app()->request->getParam('type', '');

        $criteria = new CDbCriteria();
        $criteria->compare('t.schoolid', $this->branchId);
        $criteria->compare('t.status', 10);
        if($type){
            $criteria->compare('covid19.type', $type);
            $criteria->with = 'covid19';
        }
        $criteria->order='t.updated_at desc';
        $dataProvider = new CActiveDataProvider('Covid19AffidavitChild', array(
            'criteria'=>$criteria,
        ));
        if($dataProvider->getData()){
            foreach ($dataProvider->getData() as $val){
                $childData[$val->childid] = $val->childid;
                $classData[$val->classid] = $val->classid;
                $covid19Data[$val->aid] = $val->aid;
            }

            $criteria = new CDbCriteria();
            $criteria->compare('childid', $childData);
            $criteria->index = 'childid';
            $childModel = ChildProfileBasic::model()->findAll($criteria);

            foreach ($childModel as $child){
                $this->childArr[$child->childid] =  $child->getChildName();
            }
            $criteria = new CDbCriteria();
            $criteria->compare('classid', $classData);
            $criteria->index = 'classid';
            $classModel = IvyClass::model()->findAll();

            foreach ($classModel as $child){
                $this->classArr[$child->classid] =  $child->title;
            }

            $covid19Model = Covid19Affidavit::model()->findAllByPk($covid19Data);

            $relationship = Covid19Affidavit::getRelationship();
            foreach ($covid19Model as $val){
                $this->covid19Info[$val->id] = array(
                    'parent_name' => trim($val->parent_name),
                    'parent_relationship' => $relationship[$val->parent_relationship],
                    'parent_tel' => trim($val->parent_tel),
                    'type' => ($val->type == 1) ? Yii::t('safety','NO') : Yii::t('safety','YES'),
                    'destination' => trim($val->destination),
                    'return_date' => ($val->return_date) ? date("Y-m-d", $val->return_date) : '',
                    'signature' => $val->signature,
                );
            }

        }
        $this->render('index',array(
            'dataProvider' => $dataProvider,
            'type' => $type,
        ));
    }

    public function actionDel()
    {
        $id = Yii::app()->request->getParam('id', '');
        if($id){
            $model = Covid19AffidavitChild::model()->findByPk($id);
            if(!$model || $model->schoolid != $this->branchId){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '非法操作');
                $this->showMessage();
            }
            $model->status = 99;
            if($model->save(false)){
                $this->addMessage('state', Yii::t('event','success'));
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('callback', 'cbCovid19Affidavit');
            } else {
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err ? $err[0] : Yii::t('event','fail'));
            }
            $this->showMessage();
        }
    }

    public function getUsersName($data)
    {
        echo ($data->childid) ? $this->childArr[$data->childid] : '';
    }

    public function getClassTitle($data)
    {
        echo ($data->classid) ? $this->classArr[$data->classid] : '';
    }

    public function getParent($data)
    {
        $info = $this->covid19Info[$data->aid]['parent_name'] . ' / ' . $this->covid19Info[$data->aid]['parent_tel']. ' / ' . $this->covid19Info[$data->aid]['parent_relationship'];
        echo $info;
    }
    public function getType($data)
    {
        echo ($this->covid19Info[$data->aid]) ? $this->covid19Info[$data->aid]['type'] : '';
    }
    public function getDestination($data)
    {
        echo ($this->covid19Info[$data->aid]) ? $this->covid19Info[$data->aid]['destination'] : '';
    }
    public function getReturnDate($data)
    {
        echo ($this->covid19Info[$data->aid]) ? $this->covid19Info[$data->aid]['return_date'] : '';
    }

    public function getButton($data)
    {
        echo CHtml::link(Yii::t('global','Print'), array('print', 'id' => $data->id,"branchId"=>Yii::app()->controller->branchId), array('class' => 'btn btn-xs btn-info', 'target' => "_blank")) . ' ';
        echo CHtml::link(Yii::t('global','Delete'), array('del', 'id' => $data->id,"branchId"=>Yii::app()->controller->branchId), array('class' => 'J_ajax_del btn btn-xs btn-danger')) . ' ';
    }

    public function getSignature($data)
    {
        $img = '';
        if($this->covid19Info[$data->aid]){
            $scr = $this->getThumbOssImageUrl($this->covid19Info[$data->aid]['signature']);
            $img = '<img class="img-thumbnail" style="width: 50px" src="'.$scr.'">';
        }

        echo $img;
    }

    // OSS 文件跳转链接
    public function actionOssfileRedit($filePath)
    {
        if ($filePath) {
            $oss = CommonUtils::initOSS('private');
            $filePath = 'covid19/' . $filePath;
            $url = $oss->get_sign_url($filePath);
            Yii::app()->request->redirect($url);
        }
        return false;
    }

    public function getThumbOssImageUrl($fileName)
    {
        $fileName = 'covid19/' . $fileName;
        // 获取文件临时地址
        $osscs = CommonUtils::initOSSCS('private');

        $object = $fileName;
        $style = 'style/w200';
        $imageUrl = $osscs->getImageUrl($object, $style);
        return $imageUrl;
    }

    public function actionPrint()
    {
        $id = Yii::app()->request->getParam('id', '');
        $this->layout = '//layouts/print';
        $this->printFW = $this->branchObj->getPrintHeader();
        $model = Covid19AffidavitChild::model()->findByPk($id);
        $img = '';
        $childModel = '';
        $classModel = '';
        $covid19Model = '';
        if($model){
            $covid19Model = Covid19Affidavit::model()->findByPk($model->aid);
            $scr = $this->getThumbOssImageUrl($covid19Model->signature);
            $img = '<img style="width: 50px" src="'.$scr.'">';

            $childModel = ChildProfileBasic::model()->findByPk($model->childid);
            $classModel = IvyClass::model()->findByPk($model->classid);
            $config = Covid19Affidavit::getRelationship();

        }
        $this->render('print',array(
            'model' => $model,
            'childModel' => $childModel,
            'classModel' => $classModel,
            'covid19Model' => $covid19Model,
            'img' => $img,
            'config' => $config,
            'school' => $this->branchObj->title,
        ));
    }

    public function actionExport()
    {
        $type = Yii::app()->request->getParam('type', '');
        $criteria = new CDbCriteria();
        $criteria->compare('t.schoolid', $this->branchId);
        $criteria->compare('t.status', 10);
        if($type){
            $criteria->compare('covid19.type', $type);
            $criteria->with = 'covid19';
        }
        $total = Covid19AffidavitChild::model()->count($criteria);

        $info = array();
        if($total) {
            $cycle = ceil($total / $this->batchNum);
            for ($i = 0; $i < $cycle; $i++) {
                $criteria->limit = $this->batchNum;
                $criteria->offset = 0;
                $model = Covid19AffidavitChild::model()->findAll($criteria);
                foreach ($model as $val) {
                    $info[] = $val;
                }
            }
        }
        $data = array();
        $data['title'] = $this->branchId . "_疫情保证书.xlsx";
        $data['items'][0] = array('孩子姓名', '班级', '家长姓名', '电话', '关系', '是否出城', '目的地', '返回日期');
        if($info) {
            $childInfo = array();
            $classInfo = array();
            $covid19Info = array();
            foreach ($model as $datas){
                $childInfo[$datas['childid']] = $datas['childid'];
                $classInfo[$datas['classid']] = $datas['classid'];
                $covid19Info[$datas['aid']] = $datas['aid'];
            }

            $criteria = new CDbCriteria();
            $criteria->compare('childid', $childInfo);
            $criteria->index = 'childid';
            $childModel = ChildProfileBasic::model()->findAll($criteria);

            $criteria = new CDbCriteria();
            $criteria->compare('classid', $classInfo);
            $criteria->index = 'classid';
            $classModel = IvyClass::model()->findAll($criteria);

            $criteria = new CDbCriteria();
            $criteria->compare('id', $covid19Info);
            $criteria->index = 'id';
            $covid19AffidavitModel = Covid19Affidavit::model()->findAll($criteria);

            $relationship = Covid19Affidavit::getRelationship();
            foreach ($model as $i=>$val) {
                $array = array();
                $array[] = isset($childModel) && isset($childModel[$val['childid']]) ? $childModel[$val['childid']]->getChildName() : $val['childid'];
                $array[] = isset($classModel) && isset($classModel[$val['classid']]) ? $classModel[$val['classid']]->title : $val['classid'];
                $array[] = isset($covid19AffidavitModel) && isset($covid19AffidavitModel[$val['aid']]) ? $covid19AffidavitModel[$val['aid']]->parent_name : '';
                $array[] = isset($covid19AffidavitModel) && isset($covid19AffidavitModel[$val['aid']]) ? $covid19AffidavitModel[$val['aid']]->parent_tel : '';
                $array[] = isset($covid19AffidavitModel) && isset($covid19AffidavitModel[$val['aid']]) ? $relationship[$covid19AffidavitModel[$val['aid']]->parent_relationship] : '';
                $array[] = isset($covid19AffidavitModel) && isset($covid19AffidavitModel[$val['aid']]) ? (($covid19AffidavitModel[$val['aid']]->type == 1)  ? '否' : '是') : '';
                $array[] = isset($covid19AffidavitModel) && isset($covid19AffidavitModel[$val['aid']]) ? $covid19AffidavitModel[$val['aid']]->destination: '';
                $array[] = isset($covid19AffidavitModel) && isset($covid19AffidavitModel[$val['aid']]) ? ($covid19AffidavitModel[$val['aid']]->return_date ? date("Y-m-d", $val->return_date) : ''): '';
                $data['items'][$i + 1] = $array;
            }
        }

        $this->addMessage('state', Yii::t('event','success'));
        $this->addMessage('data', $data);
        $this->showMessage();
    }
}

