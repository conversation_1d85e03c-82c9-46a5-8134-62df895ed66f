<?php $typeList = CommonUtils::LoadConfig('CfgASA');
$jobType = array();
$staffJobType = array();
foreach ($typeList['job_type'] as $k => $job_type) {
    if ($k < 10) {
        $jobType[$k] = (Yii::app()->language == 'zh_cn') ? $job_type['cn'] : $job_type['en'];
    } else {
        $staffJobType[$k] = (Yii::app()->language == 'zh_cn') ? $job_type['cn'] : $job_type['en'];
    }
}
$reset = reset($typeList['unit_type']);
$ends = end($typeList['unit_type']);
$unitType = array(1 => (Yii::app()->language == 'zh_cn') ? $reset['cn'] : $reset['en'], 99 => (Yii::app()->language == 'zh_cn') ? $ends['cn'] : $ends['en']);

$type = array();
foreach ($typeList['type'] as $k => $job_type) {
    $type[$k] = (Yii::app()->language == 'zh_cn') ? $job_type['cn'] : $job_type['en'];
}
?>
    <style>
        .list-group-item {
            display: table-row;
        }
        .modal{
            z-index: 1050 !important
        }
        .edui-default{
            z-index: 1060 !important
        }
        .edui-default .edui-dialog{
            z-index: 1062 !important
        }
    </style>
    <div class="container-fluid">
        <ol class="breadcrumb">
            <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
            <li><?php echo CHtml::link(Yii::t('site', 'Routines'), array('//mcampus/default/index')) ?></li>
            <li class="active"><?php echo Yii::t('site', '课后课管理') ?></li>
        </ol>

        <div class="row">
            <!-- 左侧菜单 -->
            <div class="col-md-2 col-sm-2 mb10">
                <?php
                $this->widget('zii.widgets.CMenu', array(
                    'items' => $this->module->getMenu(),
                    'id' => 'pageCategory',
                    'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                    'activeCssClass' => 'active',
                ));
                ?>
            </div>

            <!-- <div class="col-md-10 col-sm-12"> -->
            <div class="col-md-2" id="startyear">
                <a class="btn btn-primary J_modal" href="<?php echo $this->createUrl('updateGroup'); ?>">
                    <span class="glyphicon glyphicon-plus"
                          aria-hidden="true"></span><?php echo Yii::t("asa", "Create an ASA Project"); ?></a>
                <br>
                <br>
                <select autocomplete="off" class="form-control" id="selectstartyear">
                    <option value="">请选择学年</option>
                    <?php
                        if(empty($_GET['year'])){
                            //默认最新的学年
                            $getUrlYear = $startyearArr[0];
                        }else{
                            $getUrlYear = $_GET['year'];
                        }
                        foreach ($startyearArr as $value){
                            if(Yii::app()->language == 'zh_cn'){
                                $msg = ' 学年';
                            }else{
                                $msg =' School Year';
                            }
//                            $schoolYear = ($value-1).'-'.$value.$msg;
                            $schoolYear = $value.'-'.($value+1).$msg;
                            if($value == $getUrlYear){
                                echo "<option value='$value' selected>$schoolYear</option>";
                            }else{
                                echo "<option value='$value'>$schoolYear</option>";
                            }
                        }
                    ?>
                </select>
                <br>
                <br>
                <?php
                echo "<span class=span_year$year>";
                    $selected_title = "";
                    $program_type = "";
                    $groupStartTime = "";
                    $groupEndTime = "";
                    foreach ($newCourseGroup as $year => $courseGroup){
                        if($getUrlYear == $year){
                            echo "<ul class='nav nav-pills nav-stacked background-gray year$year'>";
                        }else{
                            echo "<ul class='nav nav-pills nav-stacked background-gray year$year' style='display: none'>";
                        }
                        foreach ($courseGroup as $v) {
                            $href = $this->createUrl('index', array('groupId' => $v['id'],'year'=>$year));
                            $active = '';
                            $title = Yii::app()->language == "zh_cn" ?
                                empty($v['title_cn']) ? $v['title_en'] : $v['title_cn']
                                :
                                empty($v['title_en']) ? $v['title_cn'] : $v['title_en'];
                            if ($v['id'] == $groupId) {
                                $active = 'active';
                                $selected_title = $title;
                                $program_type = $v['program_type'];
                                $groupStartTime = $v['buy_from'];
                                $groupEndTime = $v['buy_end'];
                            }
                            echo "<li class='{$active}'><a href='{$href}'>";
                            echo $title;
                            echo '</a></li>';
                        }
                        echo '</ul>';
                    }
                echo '</span>';
                ?>
                <ul>

                </ul>

                <br>
                <div  class="text-center">
                    <img src="http://ivy-www-uploads.oss-cn-beijing.aliyuncs.com/asa/perview/<?php echo $schoolid . ".png"; ?>"  width="150" />
                    <p><?php echo Yii::t('asa','此二维码只作为内部预览用，无购买功能。购买请通过微信公众号“萌犸象科技”进入')?></p>
                </div>

            </div>

            <div class="col-md-8">

                <?php if ($groupId): ?>
                    <div class="page-header">
                        <h2>
                            <?php echo $selected_title; ?>
                            <a class="btn btn-default J_modal"
                               href="<?php echo $this->createUrl('updateGroup', array('groupId' => $groupId)); ?>">
                                    <span class="glyphicon glyphicon-edit" aria-hidden="true"
                                          title="<?php echo Yii::t('asa', '编辑基本信息'); ?>"></span> <?php echo Yii::t('asa', '编辑基本信息'); ?></a>
                            <a class="btn btn-default J_modal"
                               href="<?php echo $this->createUrl('refundProtocol', array('groupId' => $groupId)); ?>">
                                    <span class="glyphicon glyphicon-file" aria-hidden="true"
                                          title="<?php echo Yii::t('asa', '编辑详细介绍'); ?>"></span> <?php echo Yii::t('asa', '编辑详细介绍'); ?></a>
                            <?php if(Yii::app()->user->checkAccess('ivystaff_it')){ ?>
                            <a class="btn btn-default J_modal"
                               href="<?php echo $this->createUrl('updateCampus', array('groupId' => $groupId)); ?>">
                                    <span class="glyphicon glyphicon-edit" aria-hidden="true"
                                          title="<?php echo Yii::t('asa', '跨校园课后课'); ?>" ></span>  <?php echo Yii::t('asa', '跨校园课后课'); ?></a>
                            <?php  }?>
<!--                            <a class="btn btn-default" title="本项目下所有报名信息"-->
<!--                               href="--><?php //echo $this->createUrl('exportGroup', array('groupId' => $groupId)); ?><!--">-->
<!--                                <span class="glyphicon glyphicon-export"-->
<!--                                      aria-hidden="true"></span> --><?php //echo Yii::t("asa", "导出名录"); ?>
<!--                            </a>-->

                            <a class="btn btn-default exportGroup" title="本项目下所有报名信息"
                               href="javascript:;" onclick="exportGroup(<?php echo $groupId?>)">
                                <span class="glyphicon glyphicon-export"
                                      aria-hidden="true"></span> <?php echo Yii::t("asa", "导出名录"); ?>
                            </a>

                            <a class="btn btn-danger J_ajax_del" href="<?php echo $this->createUrl('deletegroup', array('groupId' => $groupId)); ?>">
                                <span class="glyphicon glyphicon-remove" aria-hidden="true" title="<?php echo Yii::t('asa', '编辑基本信息'); ?>"></span>
                            </a>
                        </h2>
                    </div>
                    <div class="page-header">
                        <p class="text-info"><?php echo Yii::t('asa', '可购买时间：'); ?><?php echo date("Y-m-d", $groupStartTime) . '-' . date("Y-m-d", $groupEndTime);
                            if (time() > $groupEndTime) {
                                echo "<span class='text-danger'>（当前不可购买）</span>";
                            } ?></p>

                        <h3><?php echo Yii::t('asa', '课程时间显示列表'); ?>
                            <small><?php echo Yii::t("asa", "具体时间"); ?></small>
                            <a class="pull-right btn btn-primary J_modal"
                               href="<?php echo $this->createUrl('schedule', array('groupId' => $groupId)); ?>">
                                <span class="glyphicon glyphicon-plus"
                                      aria-hidden="true"></span> <?php echo Yii::t("asa", "添加时间"); ?>
                            </a>
                        </h3>
                    </div>

                    <?php
                    $this->widget('ext.ivyCGridView.BsCGridView', array(
                        'id' => 'schedule-staff',
                        'afterAjaxUpdate' => 'js:head.Util.modal',
                        'dataProvider' => $schedule,
                        'template' => "{items}{pager}",
                        //状态为无效时标红
                        'rowCssClassExpression' => '( $data->state == 0 ? "active" : "" )',
                        'colgroups' => array(
                            array(
//                                "colwidth" => array(150, 150, 150, 150, 100, 100, 100, 100, 100),
                            )
                        ),
                        'columns' => array(
                            array(
                                'name' => 'title',
                                'value' => '$data->title',
                            ),
                            array(
                                'name' => 'week_day',
                                'value' => '($data->week_day == 10) ? "延时服务" : $data->week_day ',
                            ),
                            array(
                                'name' => 'default_time_start',
                                'value' => array($this, "getDefaultTime"),
                            ),
                            array(
                                'name' => 'total_count',
                                'value' => '$data->total_count',
                            ),
                            array(
                                'name' => '操作',
                                'value' => array($this, "getScheduleButton"),
                            ),
                        ),
                    ));
                    ?>
                <?php endif; ?>


                <?php if ($groupId): ?>
                    <div class="page-header">
                        <h3><?php echo Yii::t('asa', 'Staffs'); ?>
                            <small><?php echo Yii::t("asa", "Mgt team and operation team"); ?></small>
                            <a class="pull-right btn btn-primary J_modal"
                               href="<?php echo $this->createUrl('updataStaffCourse', array('groupId' => $groupId)); ?>">
                                <span class="glyphicon glyphicon-plus"
                                      aria-hidden="true"></span> <?php echo Yii::t("asa", "Add a staff"); ?>
                            </a>
                        </h3>
                    </div>

                    <?php
                    $this->widget('ext.ivyCGridView.BsCGridView', array(
                        'id' => 'course-staff',
                        'afterAjaxUpdate' => 'js:head.Util.modal',
                        'dataProvider' => $staffCourseModel,
                        'template' => "{items}{pager}",
                        //状态为无效时标红
                        'rowCssClassExpression' => '( $data->status == 0 ? "active" : "" )',
                        'colgroups' => array(
                            array(
//                                "colwidth" => array(100, 100, 100, 100, 100, 100, 100, 100, 200),
                            )
                        ),
                        'columns' => array(
                            array(
                                'name' => '姓名',
                                'value' => '$data->vendor->getName()',
                            ),
                            array(
                                'name' => 'job_type',
                                'value' => array($this, "getJobType"),
                            ),
                            'unit_salary',
                            array(
                                'name' => 'unit_type',
                                'value' => array($this, "getUnitType"),
                            ),
                            array(
                                'name' => 'status',
                                'value' => '($data->status == 1) ? "双方已确定" : "未确定"',
                            ),
                            array(
                                'name' => 'updated',
                                'value' => 'date("Y-m-d", $data->updated)',
                            ),
                            array(
                                'name' => '操作',
                                'value' => array($this, "getButton"),
                            ),
                        ),
                    ));
                    ?>
                <?php endif; ?>
                <?php if ($groupId): ?>
                    <div class="page-header">
                        <h3><?php echo Yii::t('asa', 'Course List'); ?>
                            <small><?php echo Yii::t("asa", "ASA courses"); ?></small>

                            <?php if($program_type != AsaCourseGroup::PROGRAM_DELAYCARE){ ?>
                                <a class="pull-right btn btn-primary J_modal"
                                   href="<?php echo $this->createUrl('importCourse', array('groupId' => $groupId)); ?>">

                                    <span class="glyphicon glyphicon-import"
                                          aria-hidden="true"></span> <?php echo Yii::t("asa", "导入课程"); ?>
                                </a>
                            <?php } ?>
                            <a class="pull-right btn btn-primary J_modal mr5"
                               href="<?php echo $this->createUrl('updateCourse', array('groupId' => $groupId)); ?>">

                                <span class="glyphicon glyphicon-plus"
                                      aria-hidden="true"></span> <?php echo Yii::t("asa", "Create a course"); ?>
                            </a>
                        </h3>
                    </div>


                    <div class="table-responsive" id="course-grid">
                        <table valign="middle" class="table table-hover">
                            <colgroup>
                                <col width="100">
                                <col width="100">
                                <col width="50">
                                <col width="50">
                                <col width="100">
                                <col width="100">
                                <col width="100">
                                <col width="100">
                            </colgroup>
                            <thead>
                            <tr>
                                <th id="course-grid_c1">课程名（中文）</th>
                                <th id="course-grid_c2">课程名（英文）</th>
                                <th id="course-grid_c2">家长权限</th>
                                <th id="course-grid_c4">课程信息</th>
                                <th id="course-grid_c3">总课时数 * 课时单价 </th>
                                <th id="course-grid_c4">安排时间标题</th>
                                <th id="course-grid_c4">已报名/容量</th>
                                <th id="course-grid_c5">操作</th>
                            </tr>
                            </thead>
                            <tbody class="list-group ui-sortable">
                            <?php if ($courseModel) { ?>
                                <?php foreach ($courseModel as $course) { ?>
                                    <tr class="list-group-item" data-id="<?php echo $course->id ?>">
                                        <td class="<?php echo $course->id ?>cname"><?php echo $course->title_cn ?></td>
                                        <td class="<?php echo $course->id ?>ename"><?php echo $course->title_en ?></td>
                                        <td>
                                            <?php if ($course->status == 1 || $course->status == 2) { ?>
                                                <i class="glyphicon glyphicon-eye-open" title="家长可见"></i>
                                            <?php } ?>

                                            <?php if ($course->status == 1) { ?>
                                                <i class="glyphicon glyphicon-shopping-cart" title="家长可购买"></i>
                                            <?php } ?>
                                        </td>
                                        <td>
                                            <?php if ($course->course_provider == 10) { ?>
                                                <i class="glyphicon glyphicon-book" title="自有课程"></i>
                                            <?php } ?>
                                            <?php if ($course->assistant_fee == 10) { ?>
                                                <i class="glyphicon glyphicon-usd" title="包含助教薪资"></i>
                                            <?php } ?>
                                        </td>
                                        <td><?php echo $course->default_count    . ' * ' . $course->unit_price ?></td>
                                        <td><?php echo isset($course->schedule) ? $course->schedule->title : "未安排时间" ?></td>
                                        <td><?php
                                            $surplus = (isset($courseNumberS) && isset($courseNumberS[$course->id])) ? $courseNumberS[$course->id] : 0;
                                            echo $surplus . "/" . $course->capacity ?></td>
                                        <td>
                                            <a role="button" class="btn btn-default btn-xs sort-header mr5"
                                               style="cursor: move;background: #eee"><span
                                                    class="glyphicon glyphicon-move"></span></a>

                                            <div class="dropdown" style="display: inline-block">
                                                <a data-toggle="dropdown" class="btn btn-default btn-xs"
                                                   href="#"><?php echo Yii::t("asa", "action"); ?> <span
                                                        class="caret"></span></a>
                                                <ul class="dropdown-menu" style="z-index: 5000" role="menu"
                                                    aria-labelledby="dLabel">
                                                    <li><?php echo CHtml::link(Yii::t('asa','Edit Description'), array('updateCourseDesc', 'id' => $course->id, "branchId" => Yii::app()->controller->branchId), array('class' => 'desc_J_modal')) . ' '; ?></li>
                                                    <li><?php echo CHtml::link(Yii::t('asa','Edit Basic Info'), array('updateCourse', 'id' => $course->id, "groupId" => $course->gid, "branchId" => Yii::app()->controller->branchId), array('class' => 'J_modal')) . ' '; ?></li>
                                                    <li><a href="javascript:teacherModal('<?php echo $course->id ?>')"
                                                           class="teachermodal_open"><?php echo Yii::t('asa','Assign Teachers'); ?></a></li>
                                                    <li><?php echo CHtml::link(Yii::t('asa','NameList'), array('showCourseInvoice', 'id' => $course->id, "branchId" => Yii::app()->controller->branchId), array('class' => 'J_modal')) . ' '; ?></li>
                                                    <li><?php echo CHtml::link(Yii::t('asa','WishList'), array('showFollower', 'id' => $course->id, "branchId" => Yii::app()->controller->branchId), array('class' => 'J_modal')) . ' '; ?></li>
                                                    <li><?php echo CHtml::link(Yii::t('asa','调整课程时间'), array('scheduleRevise', 'id' => $course->id, "branchId" => Yii::app()->controller->branchId), array('class' => 'J_modal')) . ' '; ?></li>
                                                    <li><?php echo CHtml::link(Yii::t('asa','Update NameList'), array('calculation', 'id' => $course->id, "branchId" => Yii::app()->controller->branchId), array('class' => 'J_ajax_del', 'data-msg' => "是否更新人数")); ?></li>
                                                    <li class="divider"></li>
                                                    <li><?php echo CHtml::link(Yii::t('asa', 'Delete'), array('deleteCourse', 'id' => $course->id, "branchId" => Yii::app()->controller->branchId), array('class' => 'J_ajax_del')); ?></li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                <?php } ?>
                            <?php } ?>
                            </tbody>
                        </table>
                    </div>

                    <?php
                    /*$this->widget('ext.ivyCGridView.BsCGridView', array(
                        'id' => 'course-grid',
                        'afterAjaxUpdate' => 'js:head.Util.modal',
                        'dataProvider' => $courseModel,
                        'template' => "{items}{pager}",
                        //状态为无效时标红
                        'rowCssClassExpression' => '( $data->status == 0 ? "active" : "" )',
                        'colgroups' => array(
                            array(
                                "colwidth" => array(80, 100, 50, 50, 100, 100, 100, 100, 200),
                            )
                        ),
                        'columns' => array(
                            array(
                                'class' => 'CCheckBoxColumn',
                                'selectableRows' => 2,
                                'checkBoxHtmlOptions' => array(
                                    'class' => 'chose',
                                    'value' => '$data->id',
                                ),
                            ),
                            'title_cn',
                            'title_en',
                            // 'vendor',
                            'unit_price',
                            'default_count',
                            array(
                                'name'=>'updated',
                                'value'=>array($this, "getCourseButton"),
                            ),
                        ),
                    ));*/
                    ?>
                <?php endif; ?>
            </div>
            <!-- </div> -->
        </div>
    </div>
    <div class="modal fade" id="teachermodal" data-backdrop="static">
        <div class="modal-dialog" role="document">
            <form action="<?php echo $this->createUrl('addteacher') ?>" method="post" class="J_ajaxForm">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                aria-hidden="true">×</span></button>
                        <h4 class="modal-title"><span class="className"></span>
                            <small>分配老师</small>
                        </h4>
                    </div>
                    <div class="modal-body" id="teacherVueModal">
                        <table class="table table-hover" v-show="!teacherdata.length <= 0">
                            <thead>
                            <tr>
                                <th>老师姓名</th>
                                <th>老师来源</th>
                                <th>联系方式</th>
                                <th>计费类型</th>
                                <th>金额</th>
                                <th>职位</th>
                                <th></th>
                            </tr>
                            </thead>
                            <tbody>
                            <p class="p10" v-show="teacherdata.length <= 0">暂未分配老师</p>
                            <tr v-for="data in teacherdata">
                                <td>{{ data.tacherName }}</td>
                                <td>{{ data.type }}</td>
                                <td>{{ data.phone }}</td>
                                <td>{{ data.unitType }}</td>
                                <td>{{ data.teacherPrice }}</td>
                                <td>{{ data.job_type }}</td>
                                <td><a class="J_ajax_del btn btn-danger btn-xs"
                                       :href="'<?php echo $this->createUrl('Delcourse') ?>&id='+data.staffId"><span
                                            class="glyphicon glyphicon-remove"></span></a></td>
                            </tr>
                            </tbody>
                        </table>
                        <hr/>
                        <div>
                            <!-- Nav tabs -->
                            <ul class="nav nav-tabs" role="tablist">
                                <li :class="{'active':index == 1}" v-for="(type,index) in types">
                                    <a :href="'#tab'+index" aria-controls="home" role="tab" :checktab="'tab'+index"
                                       :checknum="index"
                                       data-toggle="tab">{{type}}</a>
                                </li>
                            </ul>
                            <!-- Tab panes -->
                            <div class="row">
                                <input type="hidden" name="courseId" id="courseId"/>

                                <div class="tab-content" style="padding: 14px">
                                    <div v-for="(type,index) in types" :id="'tab' + index"
                                         :class="{'active':index == 1}" class="tab-pane"> <!--内部个人-->
                                        <div role="tabpanel" class="col-md-12">
                                            <div class="form-group">
                                                <select name="teacher" class="form-control project"
                                                        :class="'project' + index" v-bind="{ disabled: index != 1}"
                                                        @change="selectTeacher(index)">
                                                    <option value=""><?php echo Yii::t('asa', '请选择老师或者机构') ?></option>
                                                    <option v-for="slist in selectTeacherList[index]"
                                                            :value="slist.teacherId" :phone="slist.teacherPhone">
                                                        {{slist.value}}
                                                    </option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <input type="text" class="form-control" name="teacherPhone" disabled
                                                       :id="'teacherPhone' + index"
                                                       placeholder="<?php echo Yii::t('asa', '联系方式') ?>">
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <input type="text" class="form-control teacherPrice"
                                                       :id="'teacherPrice' + index" name="teacherPrice" disabled
                                                       placeholder="<?php echo Yii::t('asa', '填写课时费金额') ?>">
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <select class="form-control teacher_job form-group"
                                                    :id="'teacher_unit' + index" name="unitType" disabled>
                                                <option value=""><?php echo Yii::t('asa', '请选择计费类型') ?></option>
                                                <option v-for="(unitType,index) in unitType" :value="index">
                                                    {{unitType}}
                                                </option>
                                            </select>
                                        </div>
                                        <div class="col-md-4">
                                            <select class="form-control teacher_job" :id="'teacher_job' + index"
                                                    name="teacher_job" disabled>
                                                <option value=""><?php echo Yii::t('asa', '请选择职位') ?></option>
                                                <option v-for="(jobtype,index) in jobtypes" :value="index">
                                                    {{jobtype}}
                                                </option>
                                            </select>
                                        </div>
                                        <input type="hidden" name="teacherType" :id="'teacherType' + index"
                                               :value="index" v-bind="{ disabled: index != 1}"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <label id="J_fail_info" class="text-warning" style="display: none;"><i
                                class="glyphicon glyphicon-remove text-warning"></i><span></span></label>
                        <button type="button" class="btn btn-primary J_ajax_submit_btn">添加老师</button>
                        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                        <input type="reset" class="_reset" style="display: none"/>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <script>

        var modal = '<div class="modal fade" id="modal" role="dialog" aria-labelledby="modal"><div class="modal-dialog modal-lg" role="document"><div class="modal-content"></div></div></div>';
        var modal1 = '<div class="modal fade" id="modal1" role="dialog" aria-labelledby="modal"><div class="modal-dialog modal-lg" role="document"><div class="modal-content"></div></div></div>';
        $('body').append(modal);
        $('body').append(modal1);

        $('.desc_J_modal').on('click', function (e) {
            e.preventDefault();
            $('#modal1 .modal-content').load($(this).attr('href'), function () {
                $('#modal1').modal({
                    show: true,
                    backdrop: 'static',
                    keyboard: false
                });
                head.Util.ajaxForm($('#modal1'));
            });
        });
        function cbCourse() {
            location.reload();
            /*$('#modal').modal('hide');
             $.fn.yiiGridView.update('course-grid');*/
        }
        function cbCourseStaff() {
            location.reload()
            /*$('#modal').modal('hide');
             $.fn.yiiGridView.update('course-staff');*/
        }
        function cbDelStaff() {
            $.fn.yiiGridView.update('course-staff', {
                complete: function () {
                    head.Util.ajaxDel()
                }
            });

        }
        function cbDelCourse() {
            location.reload();
            //$.fn.yiiGridView.update('course-grid');
        }
        var teacherflag = false;
        var types = <?php echo json_encode($type)?>;
        var jobtypes = <?php echo json_encode($jobType)?>;
        var unitType = <?php echo json_encode($unitType)?>;
        var groupId = <?php echo $groupId?>;
        var teacherdata = [];
        var teachersearch = [], selectTeacherList = {};
        var startyearArr = <?php echo json_encode($startyearArr)?>;
        var newCourseGroup = <?php echo json_encode($newCourseGroup)?>;


        function teacherModal(id) {
            $('#J_fail_info').hide();
            $('._reset').click();
            teacherdata.splice(0, teacherdata.length);
            $('.teacher_job').attr('disabled', 'disabled');
            $('.teacherPrice').attr('disabled', 'disabled');
            $('#courseId').val(id);
            $('.tab-content').find('input').val('');
            if (GV.lang == 'zh_cn') {
                var _name = $('.' + id + 'cname').text();
                $('.className').text(_name);
            } else {
                var _name = $('.' + id + 'ename').text();
                $('.className').text(_name);
            }
            $.getJSON('<?php echo $this->createUrl('teacherlist') ?>', {courseid: id}, function (data) {
                $.each(data, function (i, list) {
                    teacherdata.push(list);
                });
            });
            $('#teachermodal').modal();
            if (!teacherflag) {
                $.ajax({
                    type: "POST",
                    data:{groupId:groupId},
                    url: "<?php echo $this->createUrl('schoolteacherlist') ?>",
                    dataType: "json",
                    success: function (data) {
                        teacherVue.selectTeacherList = data;

                    }
                });
                teacherflag = true;
            }
        }
        $("#selectstartyear").change(function (){
            var id = $(this).val();
            $(".year"+id).show().siblings().hide();
        })


        var teacherVue = new Vue({
            el: "#teacherVueModal",
            data: {
                selectTeacherList:selectTeacherList,
                teachersearch:teachersearch,
                teacherdata:teacherdata,
                types:types,
                unitType:unitType,
                jobtypes:jobtypes

            },
            created: function () {
            },
            updated: function () {
                head.Util.ajaxDel();
            },
            methods: {
                removeTodo: function (id) {
                    for (var index in this.teacherdata) {
                        if (this.teacherdata[index].staffId == id) {
                            this.teacherdata.splice(index, 1)
                        }
                    }
                },
                selectTeacher: function (index) {
                    var phoneNum = $(".project" + index + " option:selected").attr("phone");
                    $("#teacherPhone" + index).val(phoneNum).attr('readonly', 'readonly').removeAttr('disabled');
                    $("#teacherPrice" + index).removeAttr('disabled');
                    $("#teacher_job" + index).removeAttr('disabled');
                    $("#teacher_unit" + index).removeAttr('disabled');
                }
            }
        });

        function _pushteacher(data) {    //添加老师成功回调
            teacherVue.teacherdata.push(data);
            $('#J_fail_info').hide();
            $('._reset').click();
            $('.tab-content').find('input').val('');
            resultTip({msg: '添加成功'})
        }     //自动完成
        var projects = [];
        var removeTeacher = function (id) {
            teacherVue.removeTodo(id);
        };
        $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
            $('#J_fail_info').hide();
            $('._reset').click();
            var checktabname = $(e.target).attr('checktab');// 激活的标签页
            $('#' + checktabname).find('.project').removeAttr('disabled');

            var checkdtabname = $(e.relatedTarget).attr('checktab');// 前一个激活的标签页
            $('#' + checkdtabname + ' :input').attr('disabled', 'disabled');
        });
        //拖动排序
        $(".list-group").sortable({
            placeholder: "ui-state-highlight",
            handle: ".sort-header",
            axis: 'y',
            opacity: 0.8,
            start: function (event, ui) {
                $('.ui-state-highlight').height($(ui['item'][0]).outerHeight()).css("list-style-type", "none");
            },
            update: function (event, ui) {
                var sort = {};
                var cc = 1;
                $(ui['item'][0]).parents('.list-group').find('.list-group-item').each(function () {
                    sort[$(this).data('id')] = cc++;
                });
                $.post('<?php echo $this->createUrl('updateSort');?>', {sort: sort}, function (data) {
                    if (data.state == 'fail') {
                        resultTip({error: 'warning', msg: data.message});
                    }
                }, 'json');
            }
        })
        function exportGroup(groupId)
        {
            const aTag = $('.exportGroup');
            aTag.removeAttr('onclick');
            $.get('<?php echo $this->createUrl('ajaxExportGroup');?>', {groupId: groupId}, function (data) {
                if (data.state == 'fail') {
                    resultTip({error: 'warning', msg: data.message});
                }
                const filename = data.data.filename;
                const ws_name = "SheetJS";
                data.data.list.push(
                    {
                        "WeekDay":"总人次",
                        "childid":data.data.total_people,
                    },
                    {
                        "WeekDay":"总人数",
                        "childid":data.data.total_number,
                    }
                    );

                var wb=XLSX.utils.json_to_sheet(data.data.list,{
                    origin:'A1',// 从A1开始增加内容
                    header: data.data.title,
                });
                const workbook = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(workbook, wb, ws_name);
                const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                const blob = new Blob([wbout], {type: 'application/octet-stream'});
                let link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = filename;
                link.click();
                setTimeout(function() {
                    // 延时释放掉obj
                    URL.revokeObjectURL(link.href);
                    link.remove();
                }, 500);
                aTag.attr('onclick',"exportGroup("+groupId+")");
            }, 'json');
        }
    </script>    

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>