<?php

/**
 * UserIdentity represents the data needed to identity a user.
 * It contains the authentication method that checks if the provided
 * data can identity the user.
 */
class UserIdentity extends CUserIdentity
{
	
	private $_id;
	const ERROR_EMAIL_INVALID=3;
	const ERROR_STATUS_NOTACTIV=4;
	const ERROR_STATUS_BAN=5;
	const ERROR_IS_STAFF=8;
	const ERROR_UNIVY=9;
	/**
	 * Authenticates a user.
	 * The example implementation makes sure if the username and password
	 * are both 'demo'.
	 * In practical applications, this should be changed to authenticate
	 * against some persistent user identity storage (e.g. database).
	 * @return boolean whether authentication succeeds.
	 */
	public function authenticate()
	{
		$user=User::model()->notsafe()->findByAttributes(array('email'=>$this->username));
		if($user===null)
			if (strpos($this->username,"@")) {
				$this->errorCode=self::ERROR_EMAIL_INVALID;
			} else {
				$this->errorCode=self::ERROR_USERNAME_INVALID;
			}
		else if($user->level==0)
			$this->errorCode=self::ERROR_STATUS_NOTACTIV;
		else if($user->isstaff==1)
			$this->errorCode=self::ERROR_IS_STAFF;		
		else if(Yii::app()->getModule('user')->encrypting($this->password)!==$user->pass)
			$this->errorCode=self::ERROR_PASSWORD_INVALID;
		else {
			$this->_id=$user->uid;
			$this->username=$user->getName();
			$this->errorCode=self::ERROR_NONE;
		}
		return !$this->errorCode;
	}
    
    /**
    * @return integer the ID of the user record
    */
	public function getId()
	{
		return $this->_id;
	}
}