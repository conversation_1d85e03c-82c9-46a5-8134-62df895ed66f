<?php

class AsaController extends Controller
{
	public $layout = '//layouts/main';

	public function init()
	{
		parent::init();
		Yii::app()->theme = 'app';
	}

	public function actionIndex()
	{
		$this->siteTitle = '课后课微信平台';
		$this->render('index');
	}
    public function actionAsaindex()
    {
        $this->siteTitle = '课后课微信平台';
        $this->render('asaIndex');
    }
	public function actionBaseball2017()
	{
		$this->siteTitle = 'Baseball Information for the 2017-18 School Year';
		$this->render('baseball2017');
	}

	public function actionMs2017()
	{
		$this->siteTitle = 'Middle School Athletics 2017-18 GENERAL INFORMATION FOR ALL SPORTS';
		$this->render('ms2017');
	}

	public function actionChangeLanguage()
	{
		$lang = (Yii::app()->language == 'en_us') ? 'zh_cn' : 'en_us' ;
		Mims::setLangCookie($lang);
		// echo Yii::app()->language;
	}

}
