<?php
class RegStep1Form extends CFormModel
{
	public $address;
	public $needBus;
	public $startingFrom;
	public $parking;
	public $journey;
	public $themselves;
	public $filedata;
	public $customize;

	public function rules()
	{
		return array(
			array('needBus', 'required'),
			array('journey, filedata, parking', 'sometimes'),
			array('themselves, customize', 'safe'),
		);
	}
	
	public function attributeLabels()
	{
		return array(
			'phone' => Yii::t("wechat", "Phone"),
			'code' => Yii::t("wechat", "Code"),
			'address' => Yii::t('reg', 'Address (compound name, street and district in Beijing)'),
			'startingFrom' => Yii::t('reg', 'Starting from'),
			'themselves' => Yii::t('reg', 'Walk home from bus stop by themselves'),
			'needBus' => Yii::t('reg', 'needBus'),
			'parking' => Yii::t('reg', '站点'),
			'journey' => Yii::t('reg', '乘坐方式'),
			'filedata' => Yii::t('reg', '签名'),
		);
	}

	public function sometimes($attribute, $params)
	{
		if ($this->needBus == 1) {
			if (empty($this->$attribute)) {
				$atattributeLabels = $this->attributeLabels();
                $this->addError($attribute, $atattributeLabels[$attribute].'不能空');
            }
		}
	}

	public function uploadFile()
	{
		// 判断是否为 base64 图片
		if (strpos($this->filedata, 'base64') === false) {
			return true;
		}
		$data = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $this->filedata));

		$normalDir = rtrim(Yii::app()->params['xoopsVarPath'], '/') .'/';

		$fileName = uniqid() . '.png';

		$filePath = $normalDir . $fileName;
		if (file_put_contents($filePath, $data)) {
			$this->filedata = $fileName;
            if ($this->filedata) {
                // 上传到 OSS
                $objectPath = 'regextrainfo/';
                $oss = CommonUtils::initOSS('private');
                if ($oss->uploadFile($objectPath . $this->filedata, $filePath)) {
                    unlink($filePath);
                    return true;
                }
            }
		}
		return false;
	}


}