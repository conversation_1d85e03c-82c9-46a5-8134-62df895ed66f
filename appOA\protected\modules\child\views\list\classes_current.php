<?php
$this->renderPartial('header');

	$this->widget('zii.widgets.CMenu',array(
		'items'=> $pageSubMenu,
		'activeCssClass'=>'current',
		'id'=>'page-subMenu',
		'htmlOptions'=>array('class'=>'pageSubMenu')
	));
	echo CHtml::openTag("div", array("class"=>"c"));
	echo CHtml::closeTag("div");
?>


<?php if ($calendar):?>
	<div class="h_a"><?php echo Yii::t("campus", "School Year");?></div>
	<div class="prompt_text">
		<ul>
			<li><?php echo $calendar->startyear . ' ~ ' . intval($calendar->startyear + 1);?></li>
		</ul>
	</div>
	<div class="mb10">
        <a href="<?php echo $this->createUrl('/child/list/exportChildInfo');?>" class="btn">
            <?php echo CHtml::openTag('span',array('class'=>'add'));?>
            <?php echo CHtml::closeTag('span');?>
            <?php echo Yii::t('site', 'Export Name List');?>
        </a>
    </div>
	<div class="h_a"><?php echo Yii::t("campus", "Class List");?></div>
	<div class="table_full">
		<table width="100%">
			<colgroup>
				<col width=240>
			</colgroup>
			<?php foreach ($calendar->currentClasses as $class):?>
				<tr>
					<th id="classid-<?php echo $class->classid;?>">
						<?php //echo $class->title . ' <span id="childCount-'.$class->classid.'">'.count($class->children).'</span>';?>
						<?php echo $class->title . ' <span class="stats" id="childCount-'.$class->classid.'"></span>';?>
						</th>
						<td class="user_group" id="childlist-<?php echo $class->classid;?>">
						<?php
							$_totalCount = count($class->children);
							$_activeCount = 0;
						?>
						<?php if($class->children):?>
							<?php
								foreach ($class->children as $child):
								echo CHtml::openTag("span", array("class"=>"mr15"));
								$sts = $child->getSwitchStatus();
								echo CHtml::link($child->getChildName(), array("/child/index/index", "childid"=>$child->childid), array("target"=>"_blank"));
								if(isset($sts['txt'])){
									echo CHtml::openTag("i", array("class"=>"stats mr15"));
									echo $sts['txt'];
									echo CHtml::closeTag("i");		
								}								
								echo CHtml::closeTag("span");
								if($child->status < ChildProfileBasic::STATS_GRADUATED){
									$_activeCount++;
								}
							endforeach;
							?>
						<?php endif;?>
						<?php
							$classChildCounts[$class->classid]['count'] = ($_activeCount == $_totalCount) ? $_totalCount : sprintf('%d/%d', $_activeCount, $_totalCount);
						?>
						
						</td>
				</tr>
				<tr>
					<td colspan="2">
					<div id="invoice-box-<?php echo $class->classid;?>"></div>
					</td>
				</tr>
			<?php endforeach; ?>
			
			
		</table>
	</div>

	<?php if(count($classChildCounts)):?>
		<script>
			var classCounts = <?php echo CJSON::encode($classChildCounts);?>;
			$(function(){
				for(var i in classCounts){
					var span = $('#childCount-'+i);
					span.html('(' + classCounts[i].count + ')');
				}
			});
		</script>
	<?php endif;?>
	
	
<?php endif;?>