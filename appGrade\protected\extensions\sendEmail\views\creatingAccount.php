<?php
if (true === $this->lang || 'cn' == $this->lang) {
    $df_cn = new CDateFormatter('zh-cn');
    $this->render('mailHeader_lang', array('lang' => 'cn'));
    ?>

    <div style="font-family: 'Microsoft Yahei'">


        <p>亲爱的家长，</p>

        <p>您在<?php echo Mims::unIvy()?'幼儿园管理系统':'艾毅在线'; ?>的账号已经建立。</p>
        <p>您现在可以登录阅读孩子报告，检查或在线支付学费，以及享用很多其他的功能！</p>
        <p>账号的信息如下：</p>

        <p>
            <span>登陆账号：</span>
            <?php echo $account; ?>
        </p>
        <p>
            <span>初始密码：</span>
            <?php echo $password; ?>
        </p>

        <p>
            请及时登陆<a href="<?php echo $site_name; ?>">系统</a>并修改初始密码。
        </p>

        <p>如遇到问题，请联系校园 <?php echo CHtml::link($support_email, 'mailto:' . $support_email) ?>。</p>

        <p>
            <?php echo $df_cn->formatDateTime($date_time, 'medium', 'short'); ?>
        </p>

        <p>该邮件为系统自动发送。</p>

    </div>

    <?php
    $this->render('mailFooter_lang', array('lang' => 'cn'));
}
?>


<?php
if (true === $this->lang || 'en' == $this->lang) {
    $df_en = new CDateFormatter('en-us');
    $this->render('mailHeader_lang', array('lang' => 'en'));
    ?>
    <div style="font-family: 'Trebuchet MS','Microsoft Yahei',Arial,Helvetica,sans-serif">

        <p>Dear Parents,</p>

        <p>Your account has been successfully created and you can now login to read reports about your child, check or make online payments, and do many others things!</p>
        <p>Please find the login details below:</p>

        <p>
            <span>Account Name:</span>
            <?php echo $account; ?>
        </p>
        <p>
            <span>Initial Password:</span>
            <?php echo $password; ?>
        </p>

        <p>
            Please sign in to <a href="<?php echo $site_name; ?>"><?php echo Mims::unIvy()?'the system':'IvyOnline'; ?></a> at your earliest convenience and change your password accordingly.
        </p>

        <p>If you have any questions, please feel free to contact the campus <?php echo CHtml::link($support_email, 'mailto:' . $support_email) ?>.</p>

        <p>
            <?php echo $df_en->formatDateTime($date_time, 'medium', 'short'); ?>
        </p>

        <p>This is an automatic email.</p>

    </div>
    <?php
    $this->render('mailFooter_lang', array('lang' => 'en'));
}
?>

