<?php

/**
 * This is the model class for table "ivy_survey_feedback_text".
 *
 * The followings are the available columns in table 'ivy_survey_feedback_text':
 * @property integer $id
 * @property integer $fb_id
 * @property integer $survey_id
 * @property integer $topic_id
 * @property integer $classid
 * @property integer $childid
 * @property integer $userid
 * @property integer $ic_id
 * @property string $schoolid
 * @property integer $rating
 * @property integer $hide2school
 * @property integer $followup
 * @property string $fb_text
 */
class WSurveyFeedbackText extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return WSurveyFeedbackText the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_survey_feedback_text';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('fb_id, survey_id, topic_id, classid, childid, userid, ic_id, rating, hide2school, followup', 'numerical', 'integerOnly'=>true),
			array('schoolid', 'length', 'max'=>255),
			array('fb_text', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, fb_id, survey_id, topic_id, classid, childid, userid, ic_id, schoolid, rating, hide2school, followup, fb_text', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'fb_id' => 'Fb',
			'survey_id' => 'Survey',
			'topic_id' => 'Topic',
			'classid' => 'Classid',
			'childid' => 'Childid',
			'userid' => 'Userid',
			'ic_id' => 'Ic',
			'schoolid' => 'Schoolid',
			'rating' => 'Rating',
			'hide2school' => 'Hide2school',
			'followup' => 'Followup',
			'fb_text' => 'Fb Text',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('fb_id',$this->fb_id);
		$criteria->compare('survey_id',$this->survey_id);
		$criteria->compare('topic_id',$this->topic_id);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('userid',$this->userid);
		$criteria->compare('ic_id',$this->ic_id);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('rating',$this->rating);
		$criteria->compare('hide2school',$this->hide2school);
		$criteria->compare('followup',$this->followup);
		$criteria->compare('fb_text',$this->fb_text,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

    public function getStaffSurveyText($surveyId=0, $branchId=null)
    {
        $texts = array();
        if($surveyId && $branchId) {
            $crit = new CDbCriteria();
            $crit->compare('survey_id', $surveyId);
            $crit->compare('schoolid', $branchId);
            $textModels = self::model()->findAll($crit);
            foreach($textModels as $model) {
                $texts[$model->topic_id][] = $model->fb_text;
            }
        }
        return $texts;
    }
}