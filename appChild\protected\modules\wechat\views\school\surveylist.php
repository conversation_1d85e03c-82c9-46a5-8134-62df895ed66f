<style>
    .js_grid{
        color: #000
    }
    .word{
        font-weight: 400;
        font-size: 17px;
        width: auto;
        word-wrap:break-word !important;
        word-break:keep-all !important;
        text-overflow: none;
        margin-bottom: 8px
    }
    .mt0{
        margin-top: 0
    }
    .display{
        display: block;
      
    }
    .display:before{
        border-top: 1px solid #E5E5E5;
    }
</style>
<div class="cell">
    <div class="bd">
        <div class="weui_cells_title"><?php echo Yii::t('support', 'Parent survey list'); ?></div>
        <div class="weui_cells weui_cells_access">
            <?php 
            //选择孩子
            $this->widget('ext.wechat.ChildSelector',array(
            'childObj'=>$childObj,
            'myChildObjs'=>$this->myChildObjs,
            'jumpUrl'=>'surveyList',
            ));
            ?>
            </div>
            <div class="weui_panel weui_panel_access mt0">
            <?php if(count($surveyInfo) > 0): ?>
                <?php foreach ($surveyInfo as $info): ?>
                    <div class="weui_media_box weui_media_text ">
                        <a href="<?php echo $this->createUrl('/wechat/user/survey', array('id'=>$info['survey_id'], 'lang'=>Yii::app()->language)); ?>" class=" js_grid display">
                            <h4 class=" word">
                                <?php echo $info['title']; ?>
                            </h4>
                            <p class="weui_media_desc"><?php echo Yii::t("vote","End Date") ?><?php echo '：' . date('Y-m-d', $info['end_time']); ?></p>
                        </a>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
               <?php echo $this->renderPartial('../bind/_null', array('text' => Yii::t("support","Currently there is no parent survey"), 'title' => Yii::t("survey", "No Data") )); ?>
            <?php endif; ?>
            </div>
        </div>
    </div>
</div>