<div class="form">

<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'ivy-ppackage-form',
	'enableAjaxValidation'=>false,
)); ?>

	<p class="note">Fields with <span class="required">*</span> are required.</p>

	<div class="row">
		<?php echo $form->labelEx($model,'id'); ?>
		<?php echo $form->textField($model,'id',array('size'=>60,'maxlength'=>10)); ?>
		<?php echo $form->error($model,'id'); ?>
	</div>
	<div class="row">
		<?php echo $form->labelEx($model,'cn_title'); ?>
		<?php echo $form->textField($model,'cn_title',array('size'=>60,'maxlength'=>255)); ?>
		<?php echo $form->error($model,'cn_title'); ?>
	</div>

	<div class="row">
		<?php echo $form->labelEx($model,'en_title'); ?>
		<?php echo $form->textField($model,'en_title',array('size'=>60,'maxlength'=>255)); ?>
		<?php echo $form->error($model,'en_title'); ?>
	</div>

	<div class="row">
		<?php echo $form->labelEx($model,'status'); ?>
		<?php echo $form->dropDownList($model, 'status', array(Yii::t('operations', '显示'),Yii::t('operations', '隐藏')), array('empty'=>Yii::t("global", 'Please Select'))); ?>
		<?php echo $form->error($model,'status'); ?>
	</div>

	<div class="row">
		<?php echo $form->labelEx($model,'weight'); ?>
		<?php echo $form->textField($model,'weight'); ?>
		<?php echo $form->error($model,'weight'); ?>
	</div>

	<div class="row buttons">
		<?php echo CHtml::submitButton($model->isNewRecord ? 'Create' : 'Save'); ?>
	</div>

<?php $this->endWidget(); ?>

</div><!-- form -->