<?php
/*
 * <PERSON><PERSON>
 * 2012-11-19
 *
 */
?>
<?php
$this->breadcrumbs = array(
    Yii::t("navigations", "Payment") => array('/child/payment/summary'),
    Yii::t("navigations", "Make Payment"),
);
?>
<h1>
    <label><?php echo Yii::t('payment', 'Confirm Payment'); ?> </label>
</h1>
<?php
$this->beginWidget('zii.widgets.jui.CJuiDialog', array(
    'id' => 'mydialog',
    // additional javascript options for the dialog plugin
    'options' => array(
        // 提示
        'title' => Yii::t("payment", 'Tips'),
        'autoOpen' => false,
    ),
));

$this->endWidget('zii.widgets.jui.CJuiDialog');


$colorbox = $this->widget('application.extensions.colorbox.JColorBox');
$colorbox->addInstance('.colorbox', array('iframe' => false, 'width' => '800', 'height' => '80%'));

if ('yeepay' == $paymentMethod) {
    $this->renderPartial(
            "/yeepay/onlineConfirm", array(
        'dataProvider' => $dataProvider,
        'selectedBank' => $selectedBank,
        'requestInfo' => $requestInfo,
        'beforePaidInfoModel' => $beforePaidInfoModel,
            )
    );
} else if ('alipay' == $paymentMethod) {
    $this->renderPartial("/alipay/onlineConfirm", array(
        'dataProvider' => $dataProvider,
        'selectedBank' => $selectedBank,
        'mySign' => $mySign,
        'requestParameter' => $requestParameter,
        'requestParameterAttributes' => $requestParameterAttributes,
        'alipaySubmitHtml' => $alipaySubmitHtml
            )
    );
} elseif ('allinpay' == $paymentMethod) {
    $this->renderPartial("/allinpay/onlineConfirm", array(
        'dataProvider' => $dataProvider,
        'selectedBank' => $selectedBank,
        'mySign' => $mySign,
        'orderDatetime' => $orderDatetime,
        'requestParameter' => $requestParameter,
        'requestParameterAttributes' => $requestParameterAttributes,
        'allinpayForm' => $allinpayForm
            )
    );
} elseif ('wxpay' == $paymentMethod) {
    $this->renderPartial("/wxpay/onlineConfirm", array(
        'dataProvider' => $dataProvider,
        'url' => $url,
        'totalDueAmount' => $totalDueAmount,
        'orderid' => $orderid,
        )
    );
}
?>


<script type="text/javascript">
    // pre-submit callback
    function showRequest(formData, jqForm, options){

        //var queryString = $.param(formData);
        //alert('About to submit: \n\n' + queryString);
        $("#show_paymoney_info").html("<img class='load' style='margin: 0px 2px;width:24px;height:24px;' src='<?php echo Yii::app()->theme->baseUrl . '/images/loading.gif'; ?>'><br><?php echo Yii::t("payment", 'Redirecting, please wait.'); ?>");

        return true;
    }

    function showResponse(rt, statusText, xhr, $form){

        $('#show_paymoney_info').html('');
        if (rt.flag == "success") {
            $("#frmOnlinePaySubmit").submit();
        }
        else if (rt.flag == "differentHmac") {
            $("#mydialog").html("<div><?php echo Yii::t("payment", 'System error, please retry.'); ?></div>");
            $("#mydialog").dialog("open");
        }
        else {
            $("#mydialog").dialog("open");
            $("#mydialog").html("<div><?php echo Yii::t("payment", 'System error, please retry.'); ?></div>");
        }
    }
</script>

<style>
    .normal-size {
        font-size: 12px;
    }

    .big-size {
        font-size: 16px;
        padding-top: 40px;
    }

    .big-size span,.back {
        color: #FF6200;
        font-size: 18px;
        line-height: 32px;
    }

    .back {
        margin-right: 20px;
        color: #666;
    }

    #operatecol {
        margin: 1em 0;
        text-align: right;
        border-top: 1px solid #fff;
        padding-top: 0.5em;
    }
</style>
