<?php

/**
 * UserIdentity represents the data needed to identity a user.
 * It contains the authentication method that checks if the provided
 * data can identity the user.
 */
class UserIdentity extends CUserIdentity
{
	public $staff;
	
	private $_id;
	const ERROR_EMAIL_INVALID=3;
	const ERROR_STATUS_NOTACTIV=4;
	const ERROR_STATUS_BAN=5;
	const ERROR_IS_NOT_STAFF=8;
	const ERROR_USER_INVALID=10;
	const ERROR_UNKNOWN=99;
	
	public function __construct($staff)
	{
		$this->staff=$staff;
	}
	/**
	 * Authenticates a user.
	 * The example implementation makes sure if the username and password
	 * are both 'demo'.
	 * In practical applications, this should be changed to authenticate
	 * against some persistent user identity storage (e.g. database).
	 * @return boolean whether authentication succeeds.
	 */
	public function authenticate()
	{
		$this->errorCode = self::ERROR_NONE;
		if($this->staff->level < 1){
			$this->errorCode = self::ERROR_STATUS_BAN;
		}
		elseif($this->staff->isstaff < 1){
			$this->errorCode = self::ERROR_IS_NOT_STAFF;
		}
		return !$this->errorCode;
	}

	public function getName()
	{
		return $this->staff->getName();
	}
	
    /**
    * @return integer the ID of the user record
    */
	public function getId()
	{
		return $this->staff->uid;
	}
}