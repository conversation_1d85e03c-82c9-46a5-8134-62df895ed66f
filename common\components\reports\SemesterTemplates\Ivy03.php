<?php

/**
 * IVY MIK/IBS的学期报告结构模版
 */


$tree = array(

    //封面图
    array(
        'sign' => 'CoverWork',
        'title' => Yii::t('teaching', 'Cover'),
        'items' => array(
            array(
                'sign' => 'CoverWork',
                'title' => Yii::t('teaching', 'Cover'),
                'desc' => array(Yii::t('teaching', 'Please attach a photo of cover')),
                'content' => 'photo'
            )
        )
    ),
    //孩子展示头像
    array(
        'sign' => 'Avatar',
        'title' => Yii::t('teaching', 'Avatar'),
        'items' => array(
            array(
                'sign' => 'Avatar',
                'title' => Yii::t('teaching', 'Avatar'),
                'desc' => array(Yii::t('teaching', 'Please attach a avatar of child')),
                'content' => 'photo'
            )
        )
    ),
    //第一页；孩子作品+评语
    array(
        'sign' => 'FrontCover',
        'title' => Yii::t('teaching', 'Creation'),
        'items' => array(
            array(
                'sign' => 'FrontCoverWork',
                'title' => Yii::t('teaching', 'Creation'),
                'desc' => array(Yii::t('teaching', 'Please attach a photo of a creation completed by the child')),
                'content' => 'photo+text'
            )
        )
    ),

    // 教师寄语
    array(
        'sign' => 'TeacherMessage',
        'title' => Yii::t('teaching', 'A Message from Teachers'),
        'items' => array(
            array(
                'title' => Yii::t('teaching', 'A Message from Teachers'),
                'desc' =>  array(Yii::t('teaching', 'Please attach a photo of all class teachers with this child')),
                'sign' => 'TeacherMessage',
                'content' => 'photo+text',
            )
        )
    ),

    // 教师签名
    // array(
    //     'sign' => 'TeacherSign',
    //     'title' => Yii::t('teaching', '教师签名'),
    //     'items' => array(
    //         array(
    //             'title' => Yii::t('teaching', '教师签名'),
    //             'desc' =>  array(Yii::t('teaching', 'Please attach a photo of all class teachers with this child')),
    //             'sign' => 'TeacherSign',
    //             'content' => 'photo+sighPhoto',
    //         )
    //     )
    // ),

    // 班级合影
    array(
        'sign' => 'BackCover',
        'title' => Yii::t('teaching', 'Class Photo'),
        'items' => array(
            array(
                'title' => Yii::t('teaching', 'Class Photo'),
                'desc' =>  array(Yii::t('teaching', 'Please attach a class photo')),
                'sign' => 'BackCoverPhoto',
                'content' => 'photo+batchPhoto',
            )
        )
    ),
);


$learningDomains = array(
    1 => 'Physical & Health',
    2 => '身体发展与健康',
    3 => 'Personal',
    4 => '个性',
    5 => 'Language and Literacy',
    6 => '语言文字',
    7 => 'Art',
    8 => '艺术',
    9 => 'Social',
    10 => '社会性',
    11 => 'Social Studies',
    12 => '社会学习',
    13 => 'Mathematical Thinking',
    14 => '数学思维',
    15 => 'Scientific Thinking',
    16 => '科学思维',
);

return array(
    'tree' => $tree,
    'learningDomains' => $learningDomains
);
