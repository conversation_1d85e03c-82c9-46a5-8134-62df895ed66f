<?php $form=$this->beginWidget('CActiveForm', array(
    'id'=>'changedate-form',
    'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm'),
));
?>

<div class="pop_cont pop_table" style="height:auto;">
    <table width="100%" class="">
        <col width="150" />
        <col width="400" />
        <col />
        <tbody>
        <tr>
            <th><?php echo $form->labelEx($model,'appointment_date'); ?></th>
            <td>
                <?php
                $this->widget('common.extensions.datePicker.JMy97DatePicker', array(
                    'name'=>CHtml::activeName($model,'appointment_date'),
                    'value'=>$model->appointment_date,
                    'options'=>array('dateFmt'=>'yyyy-MM-dd','opposite'=>true),
                    'htmlOptions'=>array('readonly'=>'readonly', 'class'=>'input'),
                ));
                ?>
            </td>
            <td><div class="fun_tips"><?php echo $form->error($model,'appointment_date'); ?></div></td>
        </tr>
        <tr>
            <th><?php echo $form->labelEx($model,'appointment_time'); ?></th>
            <td>
                <?php echo $form->textField($model,'appointment_time',array('maxlength'=>255,'class'=>'input')); ?>
            </td>
            <td><div class="fun_tips"><?php echo $form->error($model,'appointment_time'); ?></div></td>
        </tr>
        </tbody>
    </table>
</div>
<div class="pop_bottom">
    <button type="button" class="btn fr" id="J_dialog_close"><?php echo Yii::t('global','Cancel');?></button>
    <button class="btn fr btn_submit mr10 J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
</div>

<?php $this->endWidget(); ?>
<script>
    function updateCB(data)
    {
        window.parent.showAll('confirmed-visit-grid');
    }
</script>