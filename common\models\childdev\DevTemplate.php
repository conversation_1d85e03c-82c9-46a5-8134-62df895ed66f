<?php

/**
 * This is the model class for table "ivy_dev_templates".
 *
 * The followings are the available columns in table 'ivy_dev_templates':
 * @property integer $id
 * @property string $class_type
 * @property integer $cat_id
 * @property integer $sub_id
 * @property string $en_content
 * @property string $cn_content
 * @property string $weight
 * @property integer $ver
 */
class DevTemplate extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return DevTemplate the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_dev_templates';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('class_type, cat_id, sub_id, weight', 'required'),
			array('cat_id, sub_id, ver', 'numerical', 'integerOnly'=>true),
			array('class_type, weight', 'length', 'max'=>5),
			array('en_content, cn_content', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, class_type, cat_id, sub_id, en_content, cn_content, weight, ver', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'class_type' => 'Class Type',
			'cat_id' => 'Cat',
			'sub_id' => 'Sub',
			'en_content' => 'En Content',
			'cn_content' => 'Cn Content',
			'weight' => 'Weight',
			'ver' => 'Ver',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('class_type',$this->class_type,true);
		$criteria->compare('cat_id',$this->cat_id);
		$criteria->compare('sub_id',$this->sub_id);
		$criteria->compare('en_content',$this->en_content,true);
		$criteria->compare('cn_content',$this->cn_content,true);
		$criteria->compare('weight',$this->weight,true);
		$criteria->compare('ver',$this->ver);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
	
	public function getTemplatebyClassType($type='', $isOld = false){
		$categoryName = 'ivy_dev_category';
		$templatesName = 'ivy_dev_templates';
		if ($isOld) {
			$categoryName = 'ivy_dev_category_old';
			$templatesName = 'ivy_dev_templates_old';
		}
		if($type != ''){
			$result = array();
			$catList = Yii::app()->db->createCommand()
						->from($categoryName)
						->where('class_type=:type',array(':type'=>$type))
						->order('pid ASC, parent_id ASC, weight ASC')
						->queryAll();
						
			$checkList = Yii::app()->db->createCommand()
						->from($templatesName)
						->where('class_type=:type',array(':type'=>$type))
						->order('cat_id ASC, sub_id	ASC, weight ASC')
						->queryAll();
						
			$categorys = array();
			
			foreach($catList as $cat){
				if( !($cat['pid'] || $cat['parent_id']) && !isset($categorys[$cat['id']]))
					$categorys[$cat['id']] = array();
				elseif(!$cat['parent_id'] && !isset( $categorys[$cat['id']]['pid'] )){
					$categorys[$cat['pid']][$cat['id']] = array();
				}else{
					$categorys[$cat['pid']][$cat['parent_id']][] = $cat['id'];
				}
			}
			
			$result['category'] = $categorys;
			
			foreach($catList as $cat){
				$result['catList'][$cat['id']] = $cat;
			}
			
			foreach($checkList as $check){
				$result['checkList'][$check['sub_id']][$check['id']] = $check;
				$result['checkIds'][$check['id']]=0;
			}
			
			return $result;
		}
	}

	public function getTemplates($type='', $isOld = false)
	{
		$templatesName = 'ivy_dev_templates';
		if ($isOld) {
			$templatesName = 'ivy_dev_templates_old';
		}
		$templates = Yii::app()->db->createCommand()
					->from($templatesName)
					->where('class_type=:type',array(':type'=>$type))
					->queryAll();
		return $templates;
	}
}