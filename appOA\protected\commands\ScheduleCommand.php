<?php
class ScheduleCommand extends CConsoleCommand {
    
    public $total = 60;
    public $num = 2;
    
    function microtime_float()
    {
        list($usec, $sec) = explode(" ", microtime());
        return ((float)$usec + (float)$sec);
    }    
    
    
    public function run($args) {
        Yii::import('common.models.schedule.*');
        //过滤表(ivy_class_schedule)班级ID
        
        /*
        $time_start = $this->microtime_float();
        $crite = new CDbCriteria;
        $crite->distinct = true;
        $crite->select = 'classid';
        $classList = ClassSchedule::model()->findAll($crite);
        echo ($this->microtime_float() - $time_start) . "\r\n";
        */
        
        $time_start = $this->microtime_float();
        $classList = Yii::app()->db->createCommand()
                    ->selectDistinct('classid')
                    ->from('ivy_class_schedule')
                    //->where('classid=:classid',array(':classid'=>104))
                    ->queryAll();

        echo ($this->microtime_float() - $time_start) . "\r\n";
         
        if (!empty($classList)){
            foreach ($classList as $val){
                $classid = $val['classid'];
                echo "\r\n" . 'classId: ' . $classid . "\r\n";
                
                for ($i=1;$i<$this->total;$i+=$this->num){
                    $crite = new CDbCriteria;
                    $scheduleList =ClassSchedule::model()->findAll('classid=:classid and weeknumber>=:weeknumber and weeknumber<:more order by weeknumber ASC,weekday ASC,weight ASC',
                            array(':classid'=>$classid,':weeknumber'=>$i,':more'=>$i+$this->num));
                    if (!empty($scheduleList)){
                        $scheduleArr = array();
                        foreach ($scheduleList as $val){                            
                            $scheduleArr[$val->weeknumber]['data'][$val->weekday-1][] = CJSON::encode(
                                array(
                                    'activityId' => $val->activity_id,
                                    'content' => $val->content,
                                    'period' => $val->minutes,
                                    'title' => $val->title,
                                    'color'=>$val->color,
                                )
                            );
                            
                            $scheduleArr[$val->weeknumber]['activity'][$val->activity_id] = $val->activity_id;
                            $scheduleArr[$val->weeknumber]['yid'] = $val->yid;
                            $scheduleArr[$val->weeknumber]['timestamp'] = $val->updated_timestamp;
                            $scheduleArr[$val->weeknumber]['uid'] = $val->userid;
                        }
                        if (count($scheduleArr)){
                            foreach ($scheduleArr as $sk =>$sVal){
                            
                                foreach($sVal['data'] as $key => $val){
                                    $dataStr[$key] = sprintf('[%s]', implode(',', $val));
                                }
                            
                                echo 'w' . $sk . '.';
                                $v2Model = new ClassScheduleV2();
                                $v2Model->classid = $classid;
                                $v2Model->yid = $sVal['yid'];
                                $v2Model->weeknumber = $sk;
                                $v2Model->activitys = implode(',', $sVal['activity']);
                                $v2Model->timestamp = $sVal['timestamp'];
                                $v2Model->uid = $sVal['uid'];
                                if ($v2Model->save()){
                                    $dataModel = new ClassScheduleData();
                                    $dataModel->id = $v2Model->id;
                                    //$dataModel->data = base64_encode(CJSON::encode($sVal['data']));
                                    $dataModel->data = base64_encode(CJSON::encode($dataStr));
                                    $dataModel->save();
                                }
                            }
                        }
                        unset($scheduleList);
                    }
                }
            }
        }
    }
}
?>
