<?php if (!empty($allinpayModels)) {?>
<div class="col-lg-4 col-md-12">
    <h4>POS刷卡（通联）</h4>
    <div class="table-responsive">
        <table class="table table-bordered">
            <thead>
            <tr>
                <th>到账金额</th>
                <th>到账日期</th>
                <th>操作时间</th>
                <th>操作</th>
            </tr>
            </thead>
            <tbody>
            <?php $total=0;foreach($allinpayModels as $allinpay):?>
            <tr>
                <td class="text-right"><a href="javascript:;" onclick="viewStatement(<?php echo $allinpay->id?>, 'allinpay')"><?php echo $allinpay->bank_toaccount_amount?></a></td>
                <td><?php echo date('Y-m-d', $allinpay->bank_toaccount_time)?></td>
                <td><?php echo $allinpay->updated ? date('Y-m-d H:i', $allinpay->updated) : '';?></td>
                <td><a target="__blank" href="<?php echo $this->createUrl('export', array('id' => $allinpay->id, 'type' => 'allinpay')); ?>">导出</a></td>
            </tr>
            <?php $total+=$allinpay->bank_toaccount_amount;endforeach;?>
            </tbody>
            <tfoot>
            <tr>
                <td class="text-right"><h4 class="text-info"><?php echo number_format($total, 2);?></h4></td>
                <td></td>
                <td></td>
                <td></td>
            </tr>
            </tfoot>
        </table>
    </div>
</div>
<?php } if (!empty($yeepayModels)) {?>
<div class="col-lg-4 col-md-12">
    <h4>POS刷卡（易宝）</h4>
    <div class="table-responsive">
        <table class="table table-bordered">
            <thead>
            <tr>
                <th>到账金额</th>
                <th>到账日期</th>
                <th>操作时间</th>
            </tr>
            </thead>
            <tbody>
            <?php $total=0;foreach($yeepayModels as $yeepay):?>
                <tr>
                    <td class="text-right"><a href="javascript:;" onclick="viewStatement(<?php echo $yeepay->id?>, 'yeepay')"><?php echo $yeepay->bank_amount?></a></td>
                    <td><?php echo date('Y-m-d', $yeepay->date_timestamp)?></td>
                    <td><?php echo $yeepay->update_timestamp ? date('Y-m-d H:i', $yeepay->update_timestamp) : '';?></td>
                </tr>
                <?php $total+=$yeepay->bank_amount;endforeach;?>
            </tbody>
            <tfoot>
            <tr>
                <td class="text-right"><h4 class="text-info"><?php echo number_format($total, 2);?></h4></td>
                <td></td>
                <td></td>
            </tr>
            </tfoot>
        </table>
    </div>
</div>
<?php } if (!empty($allinpayOnlineModels)) {?>
<div class="col-lg-4 col-md-12">
    <h4>在线支付（通联）</h4>
    <div class="table-responsive">
        <table class="table table-bordered">
            <thead>
            <tr>
                <th>到账金额</th>
                <th>到账日期</th>
                <th>操作时间</th>
            </tr>
            </thead>
            <tbody>
            <?php $total=0;foreach($allinpayOnlineModels as $allinpayOnline):?>
                <tr>
                    <td class="text-right"><a href="javascript:;" onclick="viewStatement(<?php echo $allinpayOnline->id?>, 'allinpayOnline')"><?php echo $allinpayOnline->bank_toaccount_amount?></a></td>
                    <td><?php echo date('Y-m-d', $allinpayOnline->bank_toaccount_time)?></td>
                    <td><?php echo $allinpayOnline->updated ? date('Y-m-d H:i', $allinpayOnline->updated) : '';?></td>
                </tr>
                <?php $total+=$allinpayOnline->bank_toaccount_amount;endforeach;?>
            </tbody>
            <tfoot>
            <tr>
                <td class="text-right"><h4 class="text-info"><?php echo number_format($total, 2);?></h4></td>
                <td></td>
                <td></td>
            </tr>
            </tfoot>
        </table>
    </div>
</div>
<?php } if (!empty($alipayModels)) {?>
<div class="col-lg-4 col-md-12">
    <h4>在线支付（支付宝）</h4>
    <div class="table-responsive">
        <table class="table table-bordered">
            <thead>
            <tr>
                <th>到账金额</th>
                <th>到账日期</th>
                <th>操作时间</th>
            </tr>
            </thead>
            <tbody>
            <?php $total=0;foreach($alipayModels as $alipay):?>
                <tr>
                    <td class="text-right"><a href="javascript:;" onclick="viewStatement(<?php echo $alipay->id?>, 'alipay')"><?php echo $alipay->bank_toaccount_amount?></a></td>
                    <td><?php echo date('Y-m-d', $alipay->bank_toaccount_time)?></td>
                    <td><?php echo $alipay->updated ? date('Y-m-d H:i', $alipay->updated) : '';?></td>
                </tr>
                <?php $total+=$alipay->bank_toaccount_amount;endforeach;?>
            </tbody>
            <tfoot>
            <tr>
                <td class="text-right"><h4 class="text-info"><?php echo number_format($total, 2);?></h4></td>
                <td></td>
                <td></td>
            </tr>
            </tfoot>
        </table>
    </div>
</div>
<?php } if (!empty($wechatPayOrderModels)) {?>
<div class="col-lg-4 col-md-12">
    <h4>微信支付</h4>
    <div class="table-responsive">
        <table class="table table-bordered">
            <thead>
            <tr>
                <th>到账金额</th>
                <th>手续费</th>
                <th>到账日期</th>
                <th>操作时间</th>
                <th>操作</th>
            </tr>
            </thead>
            <tbody>
            <?php 
                $total=0;foreach($wechatPayOrderModels as $wechatpay):
                $amount = $wechatpay->bank_toaccount_amount;
                $handling = 0;
                if ($wechatpay->handling_amount > 0) {
                    $amount = $wechatpay->actual_toaccount_amount;
                    $handling = $wechatpay->handling_amount;
                }
            ?>
                <tr>
                    <td class="text-right"><a href="javascript:;" onclick="viewStatement(<?php echo $wechatpay->id?>, 'wechatpay')"><?php echo $amount?></a></td>
                    <td class="text-right"><?php echo $handling?></td>
                    <td><?php echo date('Y-m-d', $wechatpay->bank_toaccount_time)?></td>
                    <td><?php echo $wechatpay->updated ? date('Y-m-d H:i', $wechatpay->updated) : '';?></td>
                    <td><a target="__blank" href="<?php echo $this->createUrl('export', array('id' => $wechatpay->id, 'type' => 'wechatpay')); ?>">导出</a></td>
                </tr>
                <?php $total_handling += $handling; $total+=$amount;endforeach;?>
            </tbody>
            <tfoot>
            <tr>
                <td class="text-right"><h4 class="text-info"><?php echo number_format($total, 2);?></h4></td>
                <td class="text-right"><h4 class="text-info"><?php echo number_format($total_handling, 2);?></h4></td>
                <td></td>
                <td></td>
                <td></td>
            </tr>
            </tfoot>
        </table>
    </div>
</div>
<?php } ?>
<div class="modal fade" id="viewStatement">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">到账详情</h4>
            </div>
            <div class="modal-body"></div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<script>
    function viewStatement(id, type)
    {
        $.get('<?php echo $this->createUrl('viewStatement')?>', {id: id, type: type}, function(data){
            $('#viewStatement .modal-body').html(data);
            $('#viewStatement').modal();
        });
    }
</script>