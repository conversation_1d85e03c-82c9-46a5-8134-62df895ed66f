<?php

class InvoiceController extends BranchBasedController
{
    public $printFW = array();

    public function createUrl($route, $params = array(), $ampersand = '&', $parentOnly = false)
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

        $this->branchSelectParams['urlArray'] = array('//masa/invoice/index');

        // jquery ui
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue2.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/jquery.jPrintArea.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/xlsx.full.min.js');
    }

	public function actionIndex()
    {
        Yii::import('common.models.invoice.ChildReserve');
        $status = Yii::app()->request->getParam('status', "unOn");
        $childid = Yii::app()->request->getParam('childid', 0);
        $classid = Yii::app()->request->getParam('classid', 0);
        // 获取所有班级
        $criteria = new cDbCriteria;
        $criteria->compare('stat', IvyClass::STATS_OPEN);
        $criteria->compare('schoolid', $this->branchId);
        $criteria->order = 'child_age asc,title asc';
        $classes = IvyClass::model()->findAll($criteria);

        // 获取符合要求的课程组
        $group = AsaCourseGroup::getGroup($this->branchId);
        $group = array_merge($group, AsaCourseGroup::model()->getShareGroup($this->branchId));

        // 获取新注册学生
        $criteria = new CDbCriteria;
        $criteria->compare('status', ChildProfileBasic::STATS_REGISTERED);
        $criteria->compare('schoolid', $this->branchId);
        $newChildObj = ChildProfileBasic::model()->findAll($criteria);

        $this->render('index', array(
            'classes'=>$classes,
            'group'=>$group,
            'status'=>$status,
            'childid'=>$childid,
            'classid'=>$classid,
            'newChildObj'=>$newChildObj,
        ));
	}


    // 保存账单
    public function actionSaveInvoice()
    {
        if (Yii::app()->request->isPostRequest) {
            $childid = Yii::app()->request->getParam('childid', 0);
            $classid = Yii::app()->request->getParam('classid', -1);
            $group = Yii::app()->request->getParam('group', 0);
            $items = Yii::app()->request->getParam('itemId', array());
            $title = Yii::app()->request->getParam('title', 0);
            $groupDiscount = Yii::app()->request->getParam('discount', 0);
            $courseDiscount = Yii::app()->request->getParam('course-discount', array());

            if ($classid < 0 || !$childid) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', '请先选择班级和学生'));
                $this->showMessage();
            }
            if (!$group || !$items) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', '课程组与课程不能为空'));
                $this->showMessage();
            }
            if (!$title) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', '账单标题不能为空'));
                $this->showMessage();
            }
            $groupObj = AsaCourseGroup::model()->findByPk($group);
            if($groupObj->program_type != AsaCourseGroup::PROGRAM_DELAYCARE){
                $creater = new CDbCriteria;
                $creater->compare('id', $items);
                $creater->index = 'id';
                $coursesObj = AsaCourse::model()->findAll($creater);

                $creater = new CDbCriteria;
                $creater->compare('course_id', $items);
                $creater->compare('group_id', $group);
                $coursesNumObj = AsaCourseNumber::model()->findAll($creater);

                if($coursesNumObj){
                    foreach($coursesNumObj as $courseNum){
                        if($coursesObj[$courseNum->course_id]){
                            $courseN = $courseNum->number_taken + $courseNum->number_hold;
                            if($coursesObj[$courseNum->course_id]->capacity - $courseN < 1){
                                $this->addMessage('state', 'fail');
                                $this->addMessage('message', Yii::t('message', '课程名额不足'));
                                $this->showMessage();
                            }
                        }
                    }
                }

                $creater = new CDbCriteria;
                $creater->compare('childid', $childid);
                $creater->compare('status', array(AsaInvoice::STATS_UNPAID, AsaInvoice::STATS_PAID));
                $creater->compare('schoolid', $this->branchId);
                $invoicesObj = AsaInvoice::model()->findAll($creater);

                if($invoicesObj){
                    $creater = new CDbCriteria;
                    $creater->compare('childid', $childid);
                    $creater->compare('site_id', $this->branchId);
                    $creater->compare('status', 5);
                    $creater->index = "invoice_item_id";
                    $refundObj = AsaRefund::model()->findAll($creater);
                    $status = 0;
                    foreach($invoicesObj as $invoice){
                        foreach($invoice->item as $item){
                            if(in_array($item->course_id, $items)){
                                $status = 1;
                            }
                            if($refundObj && $refundObj[$item->id]){
                                if($refundObj[$item->id]->status = 5){
                                    $status = 0;
                                }
                            }
                        }
                    }

                    if($status){
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('message', '所选课程中已有买过课程'));
                        $this->showMessage();
                    }
                }

                // 计算所需价格
                $amount_original = 0;
                $price = array();
                $courseAmount = array();
                foreach ($items as $item) {
                    $count = $_POST["itemCount_{$item}"];
                    $course = AsaCourse::model()->findByPk($item);
                    $price[$item] = $course->unit_price;
                    if ($courseDiscount[$item]) {
                        if ($groupDiscount) {
                            $this->addMessage('state', 'fail');
                            $this->addMessage('message', Yii::t('message', '课程优惠与课程组优惠不能同时存在。'));
                            $this->showMessage();
                        }
                        //计算课程折扣
                        $discountModel = AsaDiscount::model()->findByPk($courseDiscount[$item]);
                        if (!$discountModel) {
                            $this->addMessage('state', 'fail');
                            $this->addMessage('message', Yii::t('message', '未找到相关折扣'));
                            $this->showMessage();
                        }
                        $courseAmount[$item]['original'] = round($count * $course->unit_price);
                        $courseAmount[$item]['discount'] = $discountModel->calDiscount($courseAmount[$item]['original']);
                        if ($courseAmount[$item]['discount'] < 0) {
                            $this->addMessage('state', 'fail');
                            $this->addMessage('message', Yii::t('message', '折扣价格大于课程价格'));
                            $this->showMessage();
                        }
                        $amount_original += $courseAmount[$item]['discount'];
                    }else{
                        $amount_original += $count * $course->unit_price;
                    }
                }
                // 计算课程组折扣
                $isDiscount = 0;
                $discountModel = AsaDiscount::model()->findByPk($groupDiscount);
                if ($discountModel) {
                    $amount_pre_discount = $amount_original;
                    $amount_original = $discountModel->calDiscount($amount_original);
                    if ($amount_original < 0) {
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('message', '折扣价格大于课程价格'));
                        $this->showMessage();
                    }
                    $isDiscount = 1;
                }
                // 开启事物
                $transaction = Yii::app()->subdb->beginTransaction();
                try {
                    // 保存订单表
                    $invoice = new AsaInvoice;
                    $invoice->title = $title;
                    $invoice->amount_original = round($amount_original, 2);
                    $invoice->amount_actual = 0;
                    $invoice->status = AsaInvoice::STATS_UNPAID;
                    $invoice->childid = $childid;
                    $invoice->classid = $classid;
                    $invoice->schoolid = $this->branchId;
                    $invoice->created_from = 0 ;
                    $invoice->updated = time();
                    $invoice->updated_userid = $this->staff->uid;
                    if (!$invoice->save()) {
                        $error = current($invoice->getErrors());
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('message', $error[0]));
                        $this->showMessage();
                    }
                    // 保存折扣关系表
                    if ($isDiscount) {
                        $discountLinkModel = new AsaInvoiceDiscountLink();
                        $discountLinkModel->invoice_id = $invoice->id;
                        $discountLinkModel->discount_id = $discountModel->id;
                        $discountLinkModel->amount_pre_discount = $amount_pre_discount;
                        $discountLinkModel->amount_final = $amount_original;
                        $discountLinkModel->save();
                    }
                    $coursesId = array();
                    $occupied = 0;
                    foreach ($items as $item) {
                        $count = $_POST["itemCount_{$item}"];
                        $total = $count * $price[$item];
                        $actual = $total;
                        // 使用单个课程优惠时，计算单个课程折扣后价格
                        if (isset($courseAmount[$item]['discount'])) {
                            $actual = $courseAmount[$item]['discount'];
                        }
                        // 使用课程组优惠时，计算单个课程折扣后价格
                        if ($isDiscount) {
                            $actual = round($total * ($amount_original/$amount_pre_discount));
                            if ($item == end($items)) {
                                $actual = $amount_original - $occupied;
                            }
                            $occupied += $actual;
                        }

                        $invoiceItem = new AsaInvoiceItem;
                        $invoiceItem->course_group_id = $group;
                        $invoiceItem->course_id = $item;
                        $invoiceItem->order_id = $invoice->id;
                        $invoiceItem->class_count = $count;
                        $invoiceItem->unit_price = round($actual/$count, 2);
                        $invoiceItem->actual_total = $actual;
                        $invoiceItem->status = AsaInvoice::STATS_UNPAID;
                        $invoiceItem->schoolid = $this->branchId;
                        $invoiceItem->spot_hold = AsaInvoiceItem::SPOT_HOLD_ACTIVE;
                        $invoiceItem->updated = time();
                        $invoiceItem->updated_userid = $this->staff->uid;
                        if (!$invoiceItem->save()) {
                            $error = current($invoiceItem->getErrors());
                            $this->addMessage('state', 'fail');
                            $this->addMessage('message', Yii::t('message', $error[0]));
                            $this->showMessage();
                        }
                        // 保存课程折扣关联表
                        if ($courseDiscount[$item]) {
                            $discountLinkModel = new AsaInvoiceDiscountLink();
                            $discountLinkModel->invoice_id = 'item_' . $invoiceItem->id;
                            $discountLinkModel->discount_id = $courseDiscount[$item];
                            $discountLinkModel->amount_pre_discount = $courseAmount[$item]['original'];
                            $discountLinkModel->amount_final = $courseAmount[$item]['discount'];
                            if(!$discountLinkModel->save()){
                                $error = current($discountLinkModel->getErrors());
                                $this->addMessage('state', 'fail');
                                $this->addMessage('message', Yii::t('message', $error[0]));
                                $this->showMessage();
                            }
                        }
                        $coursesId[] = $item;
                    }
                    $transaction->commit();
                } catch (Exception $e) {
                    $transaction->rollBack();
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', $e->getMessage());
                    $this->showMessage();
                }

            }else{
                $creater = new CDbCriteria;
                $creater->compare('id', $items);
                $creater->index = 'id';
                $courseObj = AsaCourse::model()->findAll($creater);
                $countUnitPrice = 0;
                $status = 1;
                foreach($courseObj as $itm){
                    $countUnitPrice += $itm->unit_price;
                    if($_POST["itemCount_{$itm->id}"]){
                        $date[] = date("Ymd", strtotime($_POST["itemCount_{$itm->id}"]));
                        $status = 0;
                    }
                }

                if(count($items) != count($date)){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('message', '课程缺少时间'));
                    $this->showMessage();
                }

                /*foreach($date as $dateItem){
                    if($dateItem < $groupObj->buy_from || $dateItem > $groupObj->buy_end){
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('message', '选择日期不在课程组日期之内'));
                        $this->showMessage();
                    }
                }

                if(time() < $groupObj->buy_from || time() > $groupObj->buy_end){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('message', '当前课程时间已过购买时间'));
                    $this->showMessage();
                }*/


                if($status){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('message', '时间不能为空'));
                    $this->showMessage();
                }

                $creater = new CDbCriteria;
                $creater->compare('schoolid', $this->branchId);
                $creater->compare('course_id', array_keys($courseObj));
                $creater->compare('course_group_id', $group);
                $creater->compare('extra_date', $date);
                $creater->index = "invoice_item_id";
                $invoiceItemExtra = AsaInvoiceItemExtra::model()->findAll($creater);

                $extra = 0;
                if($invoiceItemExtra){
                    $creater = new CDbCriteria;
                    $creater->compare('site_id', $this->branchId);
                    $creater->compare('course_id', array_keys($courseObj));
                    $creater->compare('childid', $childid);
                    $creater->compare('program_id', $group);
                    $creater->compare('status', "5");
                    $refundObj = AsaRefund::model()->findAll($creater);

                    if($refundObj){
                        $refind = array();
                        foreach($refundObj as $refund){
                            $refind[$refund->invoice_item_id] = 1;
                        }
                    }

                    foreach($invoiceItemExtra as $itema){
                        if($itema->asainvoice->childid == $childid){
                            $extra = 1;
                        }
                        if($itema->asainvoiceitem->status == 99){
                            $extra = 0;
                        }
                        if($refind[$itema->invoice_item_id]){
                            $extra = 0;
                        }

                    }
                }
                if($extra){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('message', '所选时间已经出过账单'));
                    $this->showMessage();
                }

                $invoice = new AsaInvoice;
                $invoice->title = $title;
                $invoice->amount_original = round($countUnitPrice, 2);
                $invoice->amount_actual = 0;
                $invoice->status = AsaInvoice::STATS_UNPAID;
                $invoice->childid = $childid;
                $invoice->classid = $classid;
                $invoice->schoolid = $this->branchId;
                $invoice->created_from = 0 ;
                $invoice->updated = time();
                $invoice->updated_userid = $this->staff->uid;
                if(!$invoice->save()){
                    $error = current($invoice->getErrors());
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('message', $error[0]));
                    $this->showMessage();
                }
                $invoiceTitle = array();
                foreach($courseObj as $item){
                    $invoiceItem = new AsaInvoiceItem;
                    $invoiceItem->course_group_id = $group;
                    $invoiceItem->course_id = $item->id;
                    $invoiceItem->order_id = $invoice->id;
                    $invoiceItem->class_count = 1;
                    $invoiceItem->unit_price = round($item->unit_price, 2);
                    $invoiceItem->actual_total = $invoiceItem->unit_price * $invoiceItem->class_count;
                    $invoiceItem->status = AsaInvoice::STATS_UNPAID;
                    $invoiceItem->schoolid = $this->branchId;
                    $invoiceItem->spot_hold = AsaInvoiceItem::SPOT_HOLD_ACTIVE;
                    $invoiceItem->updated = time();
                    $invoiceItem->updated_userid = $this->staff->uid;

                    if (!$invoiceItem->save()) {
                        $error = current($invoiceItem->getErrors());
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('message', $error[0]));
                        $this->showMessage();
                    }

                    $invoiceExtra = new AsaInvoiceItemExtra();
                    $invoiceExtra->invoice_id = $invoice->id;
                    $invoiceExtra->course_id = $item->id;
                    $invoiceExtra->invoice_item_id = $invoiceItem->id;
                    $invoiceExtra->course_group_id = $groupObj->id;
                    $invoiceExtra->extra_date = date("Ymd", strtotime($_POST["itemCount_{$item->id}"]));
                    $invoiceExtra->status = 10;
                    $invoiceExtra->schoolid = $this->branchId;
                    $invoiceExtra->update = time();
                    $invoiceExtra->update_by = $this->staff->uid;
                    $invoiceTitle[] = $_POST["itemCount_{$item->id}"];
                    if(!$invoiceExtra->save()){
                        $error = current($invoiceExtra->getErrors());
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('message', $error[0]));
                        $this->showMessage();
                    }
                    $coursesId[] =  $item->id;
                }

                $invoiceTitlenum = array_count_values($invoiceTitle);
                foreach($invoiceTitlenum as $k=>$numItem){
                    if($numItem == 1 ){
                        $invoice->title .= ' ' . $k .' , ';
                    }else{
                        $invoice->title .= ' ' . $k .' * '.$numItem . ' , ';
                    }

                }
                $invoice->title = " ( " . substr($invoice->title, 0, -3) . " ) ";
                $invoice->save();
            }

            Yii::import('common.components.AliYun.MQ.MQProducer');
            CommonUtils::addProducer(MQProducer::TAG_ASA, "Invoice.releaseHoldSpot", CJSON::encode($coursesId), 0);

            $this->addMessage('state', 'success');
            $this->addMessage('data', array('childid'=>$childid));
            $this->addMessage('callback', 'cbShow');
            $this->addMessage('message', Yii::t('message', '保存成功'));
            $this->showMessage();
        }
    }

    // 获取未支付的账单
    public function actionGetInvoice()
    {
        $childid = Yii::app()->request->getParam('childid', 0);
        if (Yii::app()->request->isAjaxRequest && $childid) {
            Yii::import('common.models.asainvoice.*');

            $invoice = $this->getInvoice($childid, AsaInvoice::STATS_UNPAID);
            $data = array();
            foreach ($invoice as $v) {
                $data[$v->id]['title'] = $v->title;
                $data[$v->id]['amount'] = $v->amount_original;
                $data[$v->id]['info'] = '';
                foreach ($v->item as $item) {
                    $course = AsaCourse::model()->findByPk($item->course_id);
                    $data[$v->id]['info'] .= $course->title_cn .'(' . $item->unit_price .' × '. $item->class_count .')，';
                }
                $data[$v->id]['info'] = rtrim($data[$v->id]['info'], '，');

            }
            echo json_encode($data);
        }
    }

    // 获取已支付的账单
    public function actionPaidInvoice()
    {
        $childid = Yii::app()->request->getParam('childid', 0);
        if (Yii::app()->request->isAjaxRequest && $childid) {
            Yii::import('common.models.asainvoice.*');

            $invoice = $this->getInvoice($childid, AsaInvoice::STATS_PAID);
            $data = array();
            foreach ($invoice as $k=>$v) {
                // $userName = User::model()->findByPk($v->updated_userid);
                $data[$k]['title'] = $v->title;
                $data[$k]['id'] = $v->id;
                $data[$k]['amount'] = $v->amount_original;
                $data[$k]['info'] = '';
                $data[$k]['date'] = date('Y-m-d', $v->updated);
                $data[$k]['user'] = User::model()->findByPk($v->updated_userid)->getName();

                $flag = false;
                foreach ($v->item as $item) {
                    // if (in_array($item->course_group_id, AsaCourseGroup::getNoRefundGroupIds())) {
                    //     $flag = true;
                    // }
                    $course = AsaCourse::model()->findByPk($item->course_id);
                    $data[$k]['info'] .= $course->title_cn .'(' . $item->unit_price .' × '. $item->class_count .')，';
                }
                $data[$k]['info'] = rtrim($data[$k]['info'], '，');
                $data[$k]['exchange'] = ($v->created_from == 4) ? 1 : 0;
                if ($flag) {
                    $data[$k]['exchange'] = 1;
                }
            }

            echo json_encode($data);
        }
    }

    // 现金付款
    public function actionCashPay()
    {
        if (Yii::app()->request->isPostRequest) {
            Yii::import('common.models.asainvoice.*');
            $invoiceId = Yii::app()->request->getParam('id', 0);
            $amount = Yii::app()->request->getParam('amount', 0);
            if (!$invoiceId || !$amount) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', 'fail');
                $this->showMessage();
            }

            $invoice = AsaInvoice::model()->findByPk($invoiceId);
            $payResult = $this->payInvoice($invoice, AsaInvoice::CASH);
            if ($payResult) {
                Yii::import('common.components.AliYun.MQ.MQProducer');
                CommonUtils::addProducer(MQProducer::TAG_ASA, "Invoice.releaseHoldSpot", CJSON::encode($payResult), 0);

                $this->addMessage('state', 'success');
                $this->addMessage('data', array('childid'=>$invoice->childid));
                $this->addMessage('message', '付款成功');
                $this->showMessage();
            } else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', $payResult);
                $this->showMessage();
            }
        }
    }

    // 微信支付
    public function actionWechatPay()
    {
        if (Yii::app()->request->isPostRequest) {
            Yii::import('common.models.asainvoice.*');
            Yii::import('common.models.wxpay.*');

            $invoiceId = Yii::app()->request->getParam('invoiceId', 0);
            $authCode = Yii::app()->request->getParam('authCode', 0);
            if (!$invoiceId || !$authCode) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '支付失败');
                $this->showMessage();
            }
            $invoice = AsaInvoice::model()->findByPk($invoiceId);
            if (!$invoice) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '账单不存在');
                $this->showMessage();
            }
            if ($invoice->status != AsaInvoice::STATS_UNPAID) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '账单已付清');
                $this->showMessage();
            }

            // 生成微信账单
            $wxPayCfg = CommonUtils::LoadConfig('CfgWxPayGlobal');
            $cfg = $wxPayCfg['MMX'];
            $schoolid = $this->branchId;
            $totalAmount = $invoice->amount_original;
            $orderPrefix = strval($cfg['number_code']) . sprintf('%03s', $wxPayCfg[$schoolid]['number_code']) . sprintf('%06s', $invoiceId);

            $model = new AsaWechatpayOrder;
            $model->orderid = $model->genOrderID($orderPrefix);
            $model->payable_amount = $totalAmount;
            $model->schoolid = $schoolid;
            $model->invoice_id = $invoice->id;
            $model->childid = $invoice->childid;
            $model->type = 'MICROPAY';
            $model->order_time = time();
            $model->updated = time();
            $model->updated_userid = $this->staff->uid;
            if($model->save()){
                $pay = $this->beginWidget('common.extensions.wxPay.MicroPay', array(
                    'auth_code' => $authCode,
                    'body' => $invoice->title,
                    'detail' => $invoice->title,
                    'amount' => $totalAmount*100,
                    'orderid' => $model->orderid,
                    'device_info' => $this->branchObj->abb,
                    'cfg' => $cfg,
                ));
                $data = $pay->pay();
                $data = $pay->hideAppid($data);
                if($data['return_code'] == 'SUCCESS'){
                    if($data['result_code'] == 'SUCCESS'){
                        if($model->orderid == $data['out_trade_no']){
                            $model = AsaWechatpayOrder::model()->findByPk($data['out_trade_no']);
                            $model->status = 1;
                            $model->fact_amount = $data['total_fee']/100;
                            $model->save();
                            // 支付订单
                            $payResult = $this->payInvoice($invoice, AsaInvoice::WECHAT);
                        }
                    }
                    else{
                        $data['out_trade_no'] = $model->orderid;
                    }
                    echo CJSON::encode($data);
                }
            }
        }
    }

    //微信支付结果查询
    public function actionQueryWechat()
    {
        Yii::import('common.models.asainvoice.*');
        Yii::import('common.models.wxpay.*');

        $orderid = Yii::app()->request->getParam('orderid', '');
        $wxPayCfg = CommonUtils::LoadConfig('CfgWxPayGlobal');

        $micropay = $this->beginWidget('common.extensions.wxPay.MicroPay', array(
            'orderid' => $orderid,
            'cfg' => $wxPayCfg['MMX'],
        ));
        $data = $micropay->query();
        $data = $micropay->hideAppid($data);
        if($data['return_code'] == 'SUCCESS' && $data['result_code'] == 'SUCCESS' && $data['trade_state'] == 'SUCCESS'){
            if($orderid == $data['out_trade_no']){
                $model = AsaWechatpayOrder::model()->findByPk($data['out_trade_no']);
                $model->status = 1;
                $model->fact_amount = $data['total_fee']/100;
                $model->updated = time();
                $model->updated_userid = $this->staff->uid;
                $model->save();
                // 支付订单
                $invoice = AsaInvoice::model()->findByPk($model->invoice_id);
                $payResult = $this->payInvoice($invoice, AsaInvoice::WECHAT);
                $this->addMessage('state', 'success');
                $this->showMessage();
            }
        }
        $this->addMessage('state', 'fail');
        $this->showMessage();
    }

    // 查询异常记录
    public function actionUnpayWechat()
    {
        Yii::import('common.models.asainvoice.*');

        $criteria = new cDbCriteria;
        $invoiceId = Yii::app()->request->getParam('invoiceId');
        $criteria->compare('invoice_id', $invoiceId);
        $criteria->compare('status', 0);
        $wechatPay = AsaWechatpayOrder::model()->findAll($criteria);
        $order = array();
        foreach ($wechatPay as $v) {
            $order[] = $v->orderid;
        }
        echo CJSON::encode($order);
    }

    // 作废账单
    public function actionDelInvoice()
    {
        if (Yii::app()->request->isPostRequest) {
            Yii::import('common.models.asainvoice.*');

            $invoiceId = Yii::app()->request->getParam('invoiceId');
            $invoice = AsaInvoice::model()->findByPk($invoiceId);
            $invoice->status = AsaInvoice::STATS_CANCELLED;
            $invoice->save();
            $courseArr = array();
            foreach ($invoice->item as $asainvoiceitem) {
                $asainvoiceitem->status = AsaInvoice::STATS_CANCELLED;
                $asainvoiceitem->save();
                $courseArr[] = $asainvoiceitem->course_id;
            }
            if($courseArr){
                Yii::import('common.components.AliYun.MQ.MQProducer');
                CommonUtils::addProducer(MQProducer::TAG_ASA, "Invoice.releaseHoldSpot", CJSON::encode($courseArr));
            }
            $this->addMessage('state', 'success');
            $this->addMessage('data', array('childid'=>$invoice->childid));
            $this->showMessage();
        }
    }

    public function getInvoice($childid, $status)
    {
        $criteria = new CDbCriteria;
        // $criteria->compare('schoolid', $this->branchId);
        $criteria->compare('childid', $childid);
        $criteria->compare('status', $status);
        $criteria->order = 'updated DESC';
        $invoice = AsaInvoice::model()->findAll($criteria);

        return $invoice;
    }

    public function actionSummary()
    {
        $this->branchSelectParams['urlArray'] = array('//masa/invoice/summary');
        Yii::import('common.models.asainvoice.*');
        $groupId = Yii::app()->request->getParam('group', 0);
        $courseId = Yii::app()->request->getParam('course', 0);

        $paytype = Yii::app()->request->getParam('paytype', '');
        $invoiceStatus = Yii::app()->request->getParam('invoiceStatus', '');
        $times = Yii::app()->request->getParam('times', '');
        $username = Yii::app()->request->getParam('username', '');
        //--------------- 所有课程组 stat ------------------//

        //---------------- 所有课程组 endd -----------------//

        // 最近三年的课程组
        $currentYear = date('Y');
        if (date('n' <= 9)) {
            $startYear = $currentYear - 4;
        } else {
            $startYear = $currentYear - 3;
        }
        $criteria = new CDbCriteria();
        $criteria->compare('startyear', '>=' . $startYear);
        $criteria->compare('schoolid', $this->branchId);
        $criteria->order = "id DESC";
        $groupModels = AsaCourseGroup::model()->findAll($criteria);

        $groupData = array(0 => '全部课程组');
        $courseData = array(0 => array(array('id' => 0, 'title' => '全部课程')));
        $currentCourseData = array( 0 => '全部课程');
        foreach ($groupModels as $groupModel) {
            $groupData[$groupModel->id] = $groupModel->getName();

            foreach ($groupModel->course as $item) {
                $courseData[$groupModel->id][] = array('id' => $item->id, 'title' => $item->getTitle());
            }
        }
        
        //根据孩子名字查询孩子ID //
        $childId = array();
        if($username){
            $criteria = new CDbCriteria;
            $criteria->addCondition("concat_ws('*',name_cn,first_name_en,middle_name_en,last_name_en) like '%{$username}%' ");
            $criteria->compare('schoolid', $this->branchId);
            $model = ChildProfileBasic::model()->findAll($criteria);
            if($model){
                foreach($model as $_model){
                    $childId[] = $_model->childid;
                }
            }
        }
        $courseInvoice = array();
        if($courseId){
            if($courseId == 999999){
                $criteria = new CDbCriteria;
                $criteria->compare('schoolid', $this->branchId);;
                $criteria->compare('status','1');
                $courseLists = AsaCourse::model()->findAll($criteria);

                foreach($courseLists as $_course){
                    foreach($_course->asainvoiceitem as $_invoice){
                        $courseInvoice[$_invoice->order_id] = $_invoice->order_id;
                    }
                }
            }else{
                $courseList = AsaCourse::model()->findByPk($courseId);
                if($courseList){
                    foreach($courseList->asainvoiceitem as $_course){
                        $courseInvoice[$_course->order_id] = $_course->order_id;
                    }
                }
            }
        }

        $invoiceID = ($courseInvoice) ? $courseInvoice : 0;
        $model = new AsaInvoice;
        $crit = new CDbCriteria;
        $crit->compare('t.schoolid', $this->branchId);
        if($paytype)$crit->compare('t.pay_type', $paytype);
        if($courseId){$crit->compare('t.id', $invoiceID);}
        if($childId) {
            $crit->compare('t.childid', $childId);
        }
        if($invoiceStatus){
            $crit->compare('t.status', $invoiceStatus);
        }else{
            $crit->compare('t.status', array(10,20));
        }
        if($times){
            $stat = strtotime($times);
            $end = strtotime($times) + 84600;
            $crit->compare('t.updated', ">={$stat}");
            $crit->compare('t.updated', "<{$end}");
        }
        //$crit->with = array('asacourse','asainvoice');
        //$model = AsaInvoiceItem::model()->findAll($crit);

        $dataProvider = new CActiveDataProvider($model, array(
            'criteria'=>$crit,
            'sort' => array(
                'defaultOrder' => 'updated DESC',
            ),
            'pagination'=>array(
                'pageSize'=>20,
            ),
        ));

        $this->render('summary', array(
            'dataProvider' => $dataProvider,
            'groupData' => $groupData,
            'courseData' => $courseData,
            'groupId' => $groupId,
            'courseId' => $courseId,
            'currentCourseData' => $currentCourseData,
        ));
    }

    public function actionShowCourse()
    {
        $id = Yii::app()->request->getParam('id', '');
        $courseList = array();
        $courseNumber = array();
        $invoiceObj = AsaInvoice::model()->findByPk($id);
        if($invoiceObj){
            $crit = new CDbCriteria;
            $crit->compare('order_id', $invoiceObj->id);
            $invoiceItme = AsaInvoiceItem::model()->findAll($crit);
            if($invoiceItme){
                $classObj = IvyClass::model()->findByPk($invoiceItme[0]->asainvoice->classid);
                foreach($invoiceItme as $item){
                    $courseList[$item->course_id] = array(
                        'title'  => $item->asacourse->getTitle(),
                        'schedule'  => $item->asacourse->schedule->title,
                        'default_count'  => $item->asacourse->default_count,
                        'unit_price'  => $item->asacourse->unit_price,
                        'capacity'  => $item->asacourse->capacity,
                        'actual_total'  => $item->asacourse->default_count * $item->asacourse->unit_price,
                    );
                }
                $crit = new CDbCriteria;
                $crit->compare('course_id', array_keys($courseList));
                $courseNumberObj = AsaCourseNumber::model()->findAll($crit);
                if($courseNumberObj){
                    foreach($courseNumberObj as $items){
                        $courseNumber[$items->course_id] = $items->number_taken + $items->number_hold;
                    }
                }
            }

            $this->renderPartial('showinvoice', array(
                'courseList' => $courseList,
                'invoiceTitle' => $invoiceObj->title,
                'courseNumber' => $courseNumber,
                'classTitle' => $classObj->title,
            ));
        }

    }

    public function getTeacher($data)
    {
        $statuslist  =  array(
            '10' => "未付款",
            '20' => "款付清",
            '99' => "作废"
        );
        echo $statuslist[$data->status];
    }

    public function getPaytype($data)
    {
        if($data->pay_type){
            echo ($data->pay_type == 1) ? "现金" : "微信";
        }else{
            echo '';
        }

    }

    /**
     * [payInvoice 账单付款]
     * @param  [type] $invoice [账单对象]
     * @param  [type] $payType [支付方式]
     * @return [type]          [支付结果]
     */
    public function payInvoice($invoice, $payType)
    {
        if (!$invoice) {
            return '账单不存在';
        }
        if ($invoice->status != AsaInvoice::STATS_UNPAID) {
            return '账单已付清';
        }
        $courses = array();
        $invoice->amount_actual = $invoice->amount_original;
        $invoice->pay_type = $payType;
        $invoice->status = AsaInvoice::STATS_PAID;
        $invoice->updated = time();
        $invoice->cash_handover_id = 0 ;
        $invoice->updated_userid = $this->staff->uid;
        if (!$invoice->save()) {
            return $courses;
        }
        foreach ($invoice->item as $item) {
            $courses[] = $item->course_id;
            $item->status = AsaInvoice::STATS_PAID;
            $item->updated = time();
            $item->updated_userid = $this->staff->uid;
            $item->save();
        }
        if ($invoice->created_from == 4) {
            // 发送处理 MQ
            Yii::import('common.components.AliYun.MQ.MQProducer');
            CommonUtils::addProducer(MQProducer::TAG_ASA, "Invoice.payBackInvoice", CJSON::encode($invoice->id), 0);
        }
        return $courses;
    }

    public function getButton($data)
    {
        echo CHtml::link('查看', array('showCourse', 'id' => $data->id, "branchId"=>Yii::app()->controller->branchId), array('class' => 'J_modal btn btn-info btn-xs drop')) . " ";
        if($data->status == AsaInvoice::STATS_UNPAID){
            echo CHtml::link('打印', array('printInvoice', 'invoiceId' => $data->id, "branchId"=>Yii::app()->controller->branchId), array('class' => 'btn btn-success btn-xs',  "target" => "_blank"));
        }
    }

    //打印未付账单
    public function actionPrintInvoice($invoiceId = 0)
    {
        $this->layout = '//layouts/print';
        $this->printFW = $this->branchObj->getPrintHeader();

        if($invoiceId){
            $invoiceObj = AsaInvoice::model()->findByPk($invoiceId);
            if($invoiceObj){
                $child = ChildProfileBasic::model()->findByPk($invoiceObj->childid);
                $crit = new CDbCriteria;
                $crit->compare('groupid', $invoiceObj->item[0]->course_group_id);
                $groupAgreement = AsaCourseGroupAgreement::model()->find($crit);
                if($child){
                    $this->render('print_invoice', array(
                        'invoice' => $invoiceObj,
                        'child' => $child,
                        'groupAgreement' => $groupAgreement,
                    ));
                }
            }
        }
    }

    public function actionExport()
    {
        $course = Yii::app()->request->getParam('course', '');
        $paytype = Yii::app()->request->getParam('paytype', '');
        $invoiceStatus = Yii::app()->request->getParam('invoiceStatus', '');
        $times = Yii::app()->request->getParam('times', '');
        $username = Yii::app()->request->getParam('username', '');

        $childId = array();
        if($username){
            $criteria = new CDbCriteria;
            $criteria->addCondition("concat_ws('*',name_cn,first_name_en,middle_name_en,last_name_en) like '%{$username}%' ");
            $criteria->compare('schoolid', $this->branchId);
            $model = ChildProfileBasic::model()->findAll($criteria);
            if($model){
                foreach($model as $_model){
                    $childId[] = $_model->childid;
                }
            }
        }
        $courseInvoice = array();
        if($course){
            if($course == 999999){
                $criteria = new CDbCriteria;
                $criteria->compare('schoolid', $this->branchId);;
                $criteria->compare('status','1');
                $courseLists = AsaCourse::model()->findAll($criteria);

                foreach($courseLists as $_course){
                    foreach($_course->asainvoiceitem as $_invoice){
                        $courseInvoice[$_invoice->order_id] = $_invoice->order_id;
                    }
                }
            }else{
                $courseList = AsaCourse::model()->findByPk($course);
                if($courseList){
                    foreach($courseList->asainvoiceitem as $_course){
                        $courseInvoice[$_course->order_id] = $_course->order_id;
                    }
                }
            }
        }

        $invoiceID = ($courseInvoice) ? $courseInvoice : 0;

        $data = array();
        $data['title'] = "账单汇总.xlsx";
        $data['items'][0] = array('孩子ID', '孩子名字', '账单名称', '原始金额','实付金额','支付方式','支付状态');

        $crit = new CDbCriteria;
        $crit->compare('t.schoolid', $this->branchId);
        if($paytype)$crit->compare('pay_type', $paytype);
        if($course){$crit->compare('id', $invoiceID);}
        if($childId) {
            $crit->compare('childid', $childId);
        }
        if($invoiceStatus){
            $crit->compare('status', $invoiceStatus);
        }else{
            $crit->compare('status', array(10,20));
        }
        if($times){
            $stat = strtotime($times);
            $end = strtotime($times) + 84600;
            $crit->compare('updated', ">={$stat}");
            $crit->compare('updated', "<{$end}");
        }

        $invoiceModel = AsaInvoice::model()->findAll($crit);
        $statuslist  =  array(
            '10' => "未付款",
            '20' => "款付清",
            '99' => "作废"
        );

        foreach ($invoiceModel as $i => $invoice) {
            $array = array();
            $array[] = $invoice->childid;
            $array[] = $invoice->childInfo->getChildName();
            $array[] = $invoice->title;
            $array[] = $invoice->amount_original;
            $array[] = $invoice->amount_actual;
            if($invoice->pay_type){
                $array[] = $invoice->pay_type == 1 ?  "现金" : "微信";
            }else{
                $array[] = '';
            }

            $array[] = $statuslist[$invoice->status];
            $data['items'][$i + 1] = $array;
        }

        $this->addMessage('state', 'success');
        $this->addMessage('data', $data);
        $this->showMessage();
    }

}
