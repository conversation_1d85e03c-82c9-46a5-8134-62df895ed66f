<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'points-stock-form',
	'enableAjaxValidation'=>false,
	'htmlOptions'=>array('class'=>'J_ajaxForm'),
));
$productModel = PointsProduct::model()->findByPk($model->product_id);
?>
<div class="pop_cont pop_table" style="height:auto;">
    <table width="100%">
        <colgroup>
            <col class="th" />
            <col />
        </colgroup>
        <tbody>
            <tr>
                <th><?php echo $form->labelEx($model,'product_id'); ?></th>
                <td><?php echo $productModel->getContent(); ?><?php echo $form->hiddenField($model,'product_id');?></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'num'); ?></th>
                <td><?php echo $form->numberField($model,'num', array('maxlength'=>255,'class'=>'input length_2')); ?></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'memo'); ?></th>
                <td><?php echo $form->textArea($model,'memo', array('class'=>'length_5')); ?></td>
            </tr>
        </tbody>
    </table>
</div>
<div class="pop_bottom">
    <button type="button" class="btn fr" id="J_dialog_close"><?php echo Yii::t('global','Cancel');?></button>
    <button class="btn fr btn_submit mr10 J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
</div>
<?php $this->endWidget(); ?>