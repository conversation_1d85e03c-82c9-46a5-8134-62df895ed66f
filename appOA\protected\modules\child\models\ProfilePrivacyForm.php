<?php

/**
 * 设置分享报告
 */
class ProfilePrivacyForm extends CFormModel
{
	public $mother_mobile; //母亲电话
	public $father_mobile; //父亲电话
	public $mother_email; //母亲电邮
	public $father_email; //父亲电邮
	public $home_phone; //家庭电话
	
	public function rules()
	{
		return array(
			array('mother_mobile,father_mobile,mother_email,father_email,home_phone', 'boolean'),
		);
	}
	
	public function attributeLabels()
	{
		return array(
			'mother_mobile'=>Yii::t("labels", "Mother's Mobile Number"),
			'father_mobile'=>Yii::t("labels", "Father's Mobile Number"),
			'mother_email'=>Yii::t("labels", "Mother's Email Address"),
			'father_email'=>Yii::t("labels", "Father's Email Address"),
			'home_phone'=>Yii::t("labels", "Home Telephone Number"),
		);
	}
	
	public function iniData($child){
		$privacy = unserialize($child->misc->privacy);
        foreach($this->attributes as $k=>$v){
            if ($privacy && !in_array($k, $privacy)){
                $this->$k = 1;
            }
            else{
                $this->$k = 0;
            }
        }
	}
	
	public function genData(){
		$ret = array();
		foreach($this->attributes as $k=>$v){
			if(!$v) $ret[] = $k;
		}
		return serialize($ret);
	}
	
}