<?php
/**
 * author: xin.ran
 */
class CXoWebUser extends CWebUser
{

	public $_keyPrefix;
	//protected $isStaff=null;
	private $_visitids=null;
	private $_adminids=null;
	//private $Childs=null;
	public $errorCode;
	
	const ERROR_INVALID_CHILDID=101; /*访客访问某孩子链接时孩子id错误*/
	const ERROR_INVALID_VISITPASS=102; /*访客访问某孩子链接时访问口令错误*/
	
	const XOGROUP_PARENT=9;
	const XOGROUP_STAFF=4;
	
	const ROLE_PARENT='parent';
	const ROLE_STAFF='staff';
	
	public $loginUrl="/user/login";
	/**
	 * @return string a prefix for the name of the session variables storing user session data.
	 */
	public function getStateKeyPrefix()
	{
		if($this->_keyPrefix!==null)
			return $this->_keyPrefix;
		else
			return $this->_keyPrefix=md5('Yii.'.get_class($this).'.'.'Child.Mims');
	}
	
	/**
	 * 获取XOOPS中的用户组
	 */
	public function getXoGroups($userid)
	{
		$xoGroups = array();
		$groups = ($userid) ? XoGroupsUsersLink::model()->findAllByAttributes(array("uid"=>$userid)) : array();
		foreach($groups as $group){
			$xoGroups[$group->groupid] = $group->groupid;
		}
		return $xoGroups;
	}
	
	/**
	 * 初始化用户角色 登录时使用
	 */
	public function grantRole($userid=0,$forceRefresh=false)
	{
		if(!$userid) return false;
		$auth=Yii::app()->authManager;
		$roles = $auth->getAuthAssignments($userid);
		
		if( ( empty($roles) || $forceRefresh ) && $userid ){
		    Yii::trace('entering grant role');
		    if($forceRefresh && !empty($roles)){
				foreach($roles as $role){
				    $auth->revoke($role, $userid );	
				}
		    }
			$groups = Yii::app()->user->getXoGroups($userid);
			if(!empty($groups)){
				if(in_array(self::XOGROUP_PARENT, $groups)){
				    $auth->assign(self::ROLE_PARENT,$userid);
					$this->setState('_firstLogin', true);
					return true;
				}elseif(in_array(self::XOGROUP_STAFF, $groups)){
				    $auth->assign(self::ROLE_STAFF,$userid);
					$this->setState('_firstLogin', true);
					return true;
				}else{
					return false;
				}
			}
		}else{
			foreach(array_keys($roles) as $role){
				if(in_array($role, array(self::ROLE_PARENT, self::XOGROUP_STAFF))){
					return true;
				}
				
			}
		}
		return false;
	}
	
	/**
	 * 员工预览时生成一个key用于验证
	 *
	 */
	public function genAuthKey($userid=null){
		if(is_null($userid)){
			$userid = Yii::app()->user->getId();
		}
		return md5($this->getStateKeyPrefix() . $userid . $this->getStateKeyPrefix() );
	}
	
	/**
	 * 由于员工查看前台时无需登录，将此功能独立出来
	 * @param	Number	$userid	Description
	 * @return	Object			Description
	 */
	public function checkStaffRole($userid=0, $checkSecurityCode=true){
		if($checkSecurityCode){
			if(! Mims::checkSecurity() )
				return false;
		}

		if(!$userid) $userid = Yii::app()->user->getId();
		$auth = Yii::app()->authManager;
		$roles = $auth->getAuthAssignments($userid);
		$user = User::model()->findByPk($userid);
		if( is_null($user) || $user->isstaff < 1 && $user->level < 1 ){
			return false;
		}
		
		// 判断是否属于员工组；这个比apps的判断要宽松；但作为本app内部的简单权限判断，还是有必要存在;
		if( empty($roles) ){
			$groups = Yii::app()->user->getXoGroups($userid);
			if(!empty($groups)){
				if(in_array(self::XOGROUP_STAFF, $groups)){
					$auth->assign(self::ROLE_STAFF,$userid);
					return true;
				}
			}
		}else{
			if(in_array(self::ROLE_STAFF, array_keys($roles))){
				return true;
			}
		}
		return false;
	}
	
	/**
	 * 是否是第一次登录系统（第一次分配用户角色）
	 * @return	Object		Description
	 */
	public function isFirstLogin(){
		return $this->getState('_firstLogin',false);
	}
	
	/**
	 * 继承beforeLogin, 根据返回觉得是否login
	 */
	protected function beforeLogin($id,$states,$fromCookie)
	{
		return $this->grantRole($id);
	}
	
	/**
	 * 继承afterLogin, Loginc成功之后调用
	 */
	protected function afterLogin($fromCookie)
	{
		$this->setAdminCIds();
	}

	// 获取可以以访客身份访问的孩子id array
	public function getVisitCIds()
	{
		return $this->getState('_visitids',array());
	}
	// 获取可以以家长身份管理的孩子id array
	public function getAdminCIds()
	{
		return $this->getState('_adminids',array());
	}
	
	// 获取可以以家长身份访问的孩子id array
	public function setAdminCIds()
	{
		$this->setState('_visitids', null);
		$this->_adminids = $this->getAdminCIds();
		
		$childs = ChildProfileBasic::model()->auth()->getChildsByParentId(Yii::app()->user->getId());
		foreach($childs as $_child){
				//echo $_child->childid;
				if (!in_array($_child->childid, $this->_adminids)){
					$this->_adminids[] = $_child->childid;
				}
		}
		$this->setState('_adminids', $this->_adminids);
	}
	
	// 加入访问孩子id
	public function authVisit($childid)
	{
		$this->_visitids = $this->getVisitCIds();
		if (!in_array($childid, $this->_visitids)){
			$this->_visitids[] = $childid;
		    $this->setState('_visitids', $this->_visitids);
		}
		return $childid;
	}
	
	public function isStaff()
	{
		return $this->getState("__isstaff");
	}
	
	public function hideGuide(){
		$this->setState("_guideClosed", true);
	}
	
	public function getHideGuide(){
		return $this->getState("_guideClosed", false);
	}
	
	public function checkAccess($operation,$params=array(),$allowCaching=true) {
		$pStr =(!empty($params))? serialize(ksort($params)):'';
		$userId = Yii::app()->user->getId();
		if($userId){
			$cacheId = md5($userId . '+' . $operation . '+' . $pStr);
			$value=Yii::app()->cache->get($cacheId);
			if($value===false){
				$ret = parent::checkAccess($operation,$params,$allowCaching);
				Yii::app()->cache->set($cacheId, intval($ret), 30*60); //缓存不能保存布尔值 false
				return $ret;
			}else{
				return $value;
			}
		}else{
			return parent::checkAccess($operation,$params,$allowCaching);
		}
	}
	
}