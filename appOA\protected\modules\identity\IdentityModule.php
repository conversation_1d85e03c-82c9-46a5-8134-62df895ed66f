<?php

class IdentityModule extends CWebModule
{
	public function init()
	{
		// this method is called when the module is being created
		// you may place code here to customize the module or the application

		// import the module-level models and components
		$this->setImport(array(
			'identity.models.*',
			'identity.components.*',
		));
	}

	public function beforeControllerAction($controller, $action)
	{
		if(parent::beforeControllerAction($controller, $action))
		{
			// this method is called before any module controller action is performed
			// you may place customized code here
			return true;
		}
		else
			return false;
	}

	public function getMenu()
	{
		$action = Yii::app()->urlManager->parseUrl(Yii::app()->request);
		$mainMenu = array(
			array('label'=>Yii::t('site', 'Pick-up card list'), 'url'=>array("/identity/default/index")),
			array('label'=>Yii::t('site', 'Device list'), 'url'=>array("/identity/device/index")),
			array('label'=>Yii::t('campus', 'Cards to be made'), 'url'=>array("/identity/readyCrad/index")),
		);
		return $mainMenu;
	}
}
