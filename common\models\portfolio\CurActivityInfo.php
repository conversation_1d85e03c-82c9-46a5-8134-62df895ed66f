<?php

/**
 * This is the model class for table "ivy_cur_activity_info".
 *
 * The followings are the available columns in table 'ivy_cur_activity_info':
 * @property integer $aid
 * @property integer $pid
 * @property string $cn_title
 * @property string $en_title
 * @property integer $season
 * @property integer $activity_type
 * @property string $intelligence
 * @property string $age
 * @property string $learning
 * @property string $cultural
 * @property string $en_learning_obj
 * @property string $cn_learning_obj
 * @property string $materials_cn_content
 * @property string $materials_en_content
 * @property string $preparation_cn_content
 * @property string $preparation_en_content
 * @property string $tips_cn_content
 * @property string $tips_en_content
 * @property string $presentation_cn_content
 * @property string $presentation_en_content
 * @property string $extension_cn_content
 * @property string $extension_en_content
 * @property string $en_memo
 * @property string $cn_memo
 * @property integer $state
 * @property integer $addtime
 * @property integer $referenced
 * @property integer $comment
 * @property string $attachments
 * @property integer $weight
 * @property string $bgcolor
 * @property integer $is_routine
 * @property integer $copyfrom
 * @property string $change_summary
 * @property integer $official
 * @property integer $userid
 * @property integer $update_timestamp
 * @property string $d
 */
class CurActivityInfo extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_cur_activity_info';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('pids, cn_title, en_title', 'required'),
			array('pid, season, activity_type, state, addtime, referenced, comment, weight, is_routine, copyfrom, official, userid, update_timestamp', 'numerical', 'integerOnly'=>true),
			array('cn_title, en_title', 'length', 'max'=>500),
			array('bgcolor', 'length', 'max'=>255),
			array('season, activity_type, cultural, en_learning_obj, cn_learning_obj, en_memo, cn_memo, state, userid, update_timestamp,intelligence, age, learning, materials_cn_content, materials_en_content, preparation_cn_content, preparation_en_content, tips_cn_content, tips_en_content, presentation_cn_content, presentation_en_content, extension_cn_content, extension_en_content, attachments, change_summary, d', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('aid, pid, cn_title, en_title, season, activity_type, intelligence, age, learning, cultural, en_learning_obj, cn_learning_obj, materials_cn_content, materials_en_content, preparation_cn_content, preparation_en_content, tips_cn_content, tips_en_content, presentation_cn_content, presentation_en_content, extension_cn_content, extension_en_content, en_memo, cn_memo, state, addtime, referenced, comment, attachments, weight, bgcolor, is_routine, copyfrom, change_summary, official, userid, update_timestamp, pids', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'user'=>array(self:: BELONGS_TO,'User','userid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'aid' => 'Aid',
			'pid' => 'Pid',
			'cn_title' => Yii::t('curriculum','Cn Title'),
			'en_title' => Yii::t('curriculum','En Title'),
			'season' => Yii::t('curriculum','Season'),
			'activity_type' => Yii::t('curriculum','Suggested Place'),
			'intelligence' => Yii::t('curriculum','Intelligence'),
			'age' => Yii::t('curriculum','Age'),
			'learning' => Yii::t('curriculum','Learning Domains'),
			'cultural' => Yii::t('curriculum','Cultural'),
			'en_learning_obj' => Yii::t('curriculum','En Learning Objectives'),
			'cn_learning_obj' => Yii::t('curriculum','Cn Learning Objectives'),
			'materials_cn_content' => Yii::t('curriculum','Cn Materials and Equipment'),
			'materials_en_content' => Yii::t('curriculum','En Materials and Equipment'),
			'preparation_cn_content' => Yii::t('curriculum','Preparation Cn Content'),
			'preparation_en_content' => Yii::t('curriculum','Preparation En Content'),
			'tips_cn_content' => Yii::t('curriculum','Tips Cn Content'),
			'tips_en_content' => Yii::t('curriculum','Tips En Content'),
			'presentation_cn_content' => Yii::t('curriculum','Presentation Cn Content'),
			'presentation_en_content' => Yii::t('curriculum','Presentation En Content'),
			'extension_cn_content' => Yii::t('curriculum','Extension Cn Content'),
			'extension_en_content' => Yii::t('curriculum','Extension En Content'),
			'en_memo' => Yii::t('curriculum','En Memo'),
			'cn_memo' => Yii::t('curriculum','Cn Memo'),
			'state' => Yii::t('curriculum','State'),
			'addtime' => 'Addtime',
			'referenced' => 'Referenced',
			'comment' => 'Comment',
			'attachments' => 'Attachments',
			'weight' => 'Weight',
			'bgcolor' => 'Bgcolor',
			'is_routine' => 'Is Routine',
			'copyfrom' => 'Copyfrom',
			'change_summary' => 'Change Summary',
			'official' => Yii::t('curriculum','Education Team'),
			'userid' => 'Userid',
			'update_timestamp' => 'Update Timestamp',
			'd' => 'd',
			'attachments' => Yii::t('curriculum','Attachments'),
			'pids' => Yii::t('curriculum','Choose a theme'),
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('aid',$this->aid);
		$criteria->compare('pid',$this->pid);
		$criteria->compare('cn_title',$this->cn_title,true);
		$criteria->compare('en_title',$this->en_title,true);
		$criteria->compare('season',$this->season);
		$criteria->compare('activity_type',$this->activity_type);
		$criteria->compare('intelligence',$this->intelligence,true);
		$criteria->compare('age',$this->age,true);
		$criteria->compare('learning',$this->learning,true);
		$criteria->compare('cultural',$this->cultural,true);
		$criteria->compare('en_learning_obj',$this->en_learning_obj,true);
		$criteria->compare('cn_learning_obj',$this->cn_learning_obj,true);
		$criteria->compare('materials_cn_content',$this->materials_cn_content,true);
		$criteria->compare('materials_en_content',$this->materials_en_content,true);
		$criteria->compare('preparation_cn_content',$this->preparation_cn_content,true);
		$criteria->compare('preparation_en_content',$this->preparation_en_content,true);
		$criteria->compare('tips_cn_content',$this->tips_cn_content,true);
		$criteria->compare('tips_en_content',$this->tips_en_content,true);
		$criteria->compare('presentation_cn_content',$this->presentation_cn_content,true);
		$criteria->compare('presentation_en_content',$this->presentation_en_content,true);
		$criteria->compare('extension_cn_content',$this->extension_cn_content,true);
		$criteria->compare('extension_en_content',$this->extension_en_content,true);
		$criteria->compare('en_memo',$this->en_memo,true);
		$criteria->compare('cn_memo',$this->cn_memo,true);
		$criteria->compare('state',$this->state);
		$criteria->compare('addtime',$this->addtime);
		$criteria->compare('referenced',$this->referenced);
		$criteria->compare('comment',$this->comment);
		$criteria->compare('attachments',$this->attachments,true);
		$criteria->compare('weight',$this->weight);
		$criteria->compare('bgcolor',$this->bgcolor,true);
		$criteria->compare('is_routine',$this->is_routine);
		$criteria->compare('copyfrom',$this->copyfrom);
		$criteria->compare('change_summary',$this->change_summary,true);
		$criteria->compare('official',$this->official);
		$criteria->compare('userid',$this->userid);
		$criteria->compare('update_timestamp',$this->update_timestamp);
		$criteria->compare('d',$this->pids,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return CurActivityInfo the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
