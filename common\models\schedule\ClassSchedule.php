<?php

/**
 * This is the model class for table "ivy_class_schedule".
 *
 * The followings are the available columns in table 'ivy_class_schedule':
 * @property integer $id
 * @property integer $classid
 * @property integer $minutes
 * @property string $starttime
 * @property string $endtime
 * @property integer $weekday
 * @property integer $yid
 * @property integer $weekid
 * @property integer $weeknumber
 * @property integer $date_timestamp
 * @property integer $activity_id
 * @property integer $title_id
 * @property string $title
 * @property string $content
 * @property string $color
 * @property integer $weight
 * @property integer $updated_timestamp
 * @property integer $userid
 */
class ClassSchedule extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ClassSchedule the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_class_schedule';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('classid, minutes, starttime, endtime, weekday, yid, weekid, weeknumber, date_timestamp, activity_id, title_id, title, content, color, weight, updated_timestamp, userid', 'required'),
			array('classid, minutes, weekday, yid, weekid, weeknumber, date_timestamp, activity_id, title_id, weight, updated_timestamp, userid', 'numerical', 'integerOnly'=>true),
			array('starttime, endtime, title', 'length', 'max'=>255),
			array('color', 'length', 'max'=>10),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, classid, minutes, starttime, endtime, weekday, yid, weekid, weeknumber, date_timestamp, activity_id, title_id, title, content, color, weight, updated_timestamp, userid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'activityInfo'=>array(self::BELONGS_TO, 'CurActivityInfo', 'activity_id')
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'classid' => 'Classid',
			'minutes' => 'Minutes',
			'starttime' => 'Starttime',
			'endtime' => 'Endtime',
			'weekday' => 'Weekday',
			'yid' => 'Yid',
			'weekid' => 'Weekid',
			'weeknumber' => 'Weeknumber',
			'date_timestamp' => 'Date Timestamp',
			'activity_id' => 'Activity',
			'title_id' => 'Title',
			'title' => 'Title',
			'content' => 'Content',
			'color' => 'Color',
			'weight' => 'Weight',
			'updated_timestamp' => 'Updated Timestamp',
			'userid' => 'Userid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('minutes',$this->minutes);
		$criteria->compare('starttime',$this->starttime,true);
		$criteria->compare('endtime',$this->endtime,true);
		$criteria->compare('weekday',$this->weekday);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('weekid',$this->weekid);
		$criteria->compare('weeknumber',$this->weeknumber);
		$criteria->compare('date_timestamp',$this->date_timestamp);
		$criteria->compare('activity_id',$this->activity_id);
		$criteria->compare('title_id',$this->title_id);
		$criteria->compare('title',$this->title,true);
		$criteria->compare('content',$this->content,true);
		$criteria->compare('color',$this->color,true);
		$criteria->compare('weight',$this->weight);
		$criteria->compare('updated_timestamp',$this->updated_timestamp);
		$criteria->compare('userid',$this->userid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}