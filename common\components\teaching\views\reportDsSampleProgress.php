<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="language" content="en"/>
    <style>
        body {
            font-size: 10px;
            /*line-height: 1.42857143;*/
            color: #333333;
            margin: 0;
            padding: 0;
            background-color: #efefef;
        }
        .page-break-after {
            page-break-after: always;
            /*margin-bottom: 13px;*/
        }
        .text-center {
            text-align: center !important;
        }
        .report-page {
            min-height: 877px;
            position: relative;
            background-color: #fff;
        }
        .report-wrapper {
            width: 620px;
            margin: 0 auto;
            padding: 0;
        }
        .pull-content {
            padding: 24px 30px 0;
        }
        .bg_gray {
            background: #dfe9f5;
        }
        .logo {
            width: 102px;
            height: 102px;
            padding-right:18px;
            float: left;
        }
        .font18 {
            font-size: 18px
        }
        .font14 {
            font-size: 14px
        }
        .bgGray {
            background: #F2F3F5
        }
        .tableText tr td,.tableText tr th {
            text-align: left;
            padding: 10px 12px;
            border: 1px solid #ddd;
        }
        .subject tr td,.subject tr th {
            padding: 7px 12px;
        }
        .subject tr th:nth-child(2){
            width:100px
        } 
        table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #E5E6EB;
        }
        .title{
            font-size:18px;
            font-weight:bold;
            color:#333;
            display:inline-block ;
            position: relative;
        }
        .title .title1{
            overflow: hidden;
            position: sticky;
            z-index: 100;
        }
        .border{
            width: 100%;
            height:6px;
            background: url('https://m2.files.ivykids.cn/cloud01-file-8025768Fg6SnQeMcXK5FPVCvRGZEsd3l50n.png') no-repeat;
            background-size: 100% 100%;
            display:inline-block ;
        }
        .color3{
            color:#333
        }
        .mt18{
            margin-top:18px
        }
        .mt5{
            margin-top:5px
        }
        .mt24{
            margin-top:24px
        }
        .mt20{
            margin-top:20px
        }
        .mb16{
            margin-bottom:16px
        }
        .ml24{
            margin-left:20px
        }
        .line14{
            line-height:14px
        }
        .mt12{
            margin-top:12px
        }
        .mt10{
            margin-top:10px
        }
        .pt24{
            padding-top:24px
        }
        .fontBold{
            font-weight:bold;
        }
        .nowrap{
            white-space: nowrap;
        }
        .line_height{
            line-height:16px
        }
        .title1{
            font-size:22px;
            font-weight:bold;
            color:#333;
            margin-top:12px
        }
        .borderTop{
            border-top:1px solid #093190
        }
        .font12{
            font-size:12px
        }
        .font10{
            font-size:10px
        }
        .font16{
            font-size:16px
        }
        .goal{
            background-color: #fff;
            border: 1px solid transparent;
            border-radius: 4px;
            -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, .05);
            box-shadow: 0 1px 1px rgba(0, 0, 0, .05);
            border-color: #ddd;
            margin-top:16px
        }
        .goalTitle{
            color: #333;
            background-color: #f5f5f5;
            border-color: #ddd;
            font-size:12px;
            padding:8px 16px;
            border-bottom: 1px solid transparent;
            border-top-left-radius: 3px;
            border-top-right-radius: 3px;
        }
        .children{
            padding:0 16px 6px;
            font-size:12px;
        }
        .color6{
            color:#666
        }
        .mt4{
            margin-top:4px
        }
        .navbar-inverse {
            background-color: #333333;
            border-color: #efefef;
        }
        .navbar-fixed-top {
            top: 0;
            position: fixed;
            right: 0;
            left: 0;
            z-index: 1030;
            -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
        }

        .navbar-inverse form {
            line-height: 50px;
            height: 50px;
            text-align: center !important;
        }

        .navbar-inverse form input {
            margin-top: 13px;
        }
        .intro{
            background: #F0F5FB;
            border-radius: 4px;
            padding:8px 10px;
            margin-bottom:6px
        }
        .introTitle{
            color: #4D88D2;
            font-size:11px;
        }
        .introTitle img{
            width:14px;
            height: 14px;
            margin-right:4px;
            float: left;
            margin-top:1px
        }
        .text{
            padding-left: 20px;
            display: flex  !important ;
            display: -webkit-flex  !important ;
            align-items: center;
            font-size: 10px;
            color: #666;
            margin-top:2px;
        }
        .point{
            width: 5px;
            height: 5px;
            background: rgba(77, 136, 210, 0.3);
            display: inline-block;
            border-radius: 50%;
            margin-right: 8px;
        }
        .pagemt20{
            margin-top:20px
        }
        .pagemt50{
            margin-top:50px
        }
        .flex{
            display: flex  !important ;
            display: -webkit-flex  !important ;
            display: -moz-flex  !important ;
            display: -o-flex  !important ;
        }
        .flex1{
            flex:1  !important 
        }
        .mission{
            display: -webkit-flex  !important ;
            display:flex  !important ;
            height: 72px;
            background: #EBF1FF;
        }
        .mission1{
            padding:10px 12px;
            background: #093190;
            color: #FFFFFF;
            font-size:14px;
            float: left;
            margin-right:16px
        }
        .mission1 span{
            font-size:20px;
            font-weight:bold
        }
        .mission2{
            padding:8px 16px
        }
        .pt10{
            padding-top:10px
        }
        .mt16{
            margin-top:16px
        }
        .cis{
            float:right;
            height:56px;
            margin-top:8px
        }
        .font11{
            font-size:11px
        }
        .infoSub{
            width: 50%;
            float:left
        }
        .info{
            float: left;
            width: calc(100% - 120px);
        }
        .ml30{
            margin-left:30px
        }
        .progress{
            width: 100%;
            height:10px;
        }
        .yellow{
            background :rgba(246, 171, 0, 1);
            width:5%;
            float:left;
            height: 10px;
        }
        .green{
            background :rgba(0, 153, 68, 1);
            width:5%;
            float:left;
            height: 10px;
        }
        .blue{
            background :rgba(9, 49, 144, 1);
            width:90%;
            float:left;
            height: 10px;
        }
        @media print {
            .intro{
                background: #F0F5FB !important;
            }
            .navbar-fixed-top{
                display:none
            }
            body{
                background-color:#fff
            }
            .pagemt20{
                margin-top:0
            }
            .pagemt50{
                margin-top:0
            }
            .pagemb20{
                margin-bottom:0
            }
            .report-wrapper {
                width: 100%;
            }
        }
    </style>
</head>
<body>
<?php if ($withToolBar): ?>
    <div class="navbar navbar-fixed-top navbar-inverse" role="navigation">
        <?php
        echo CHtml::form($downloadActionUrl);
        echo CHtml::hiddenField('childid', $params['childid']);
        echo CHtml::hiddenField('id', $params['id']);
        echo CHtml::hiddenField('classid', $params['classid']);
        echo CHtml::hiddenField('yid', $params['yid']);
        echo CHtml::hiddenField('courseid', $params['courseid']);
        echo CHtml::submitButton('Download PDF / 下载 PDF', array('style' => 'font-family: Microsoft Yahei'));
        echo CHtml::endForm();
        ?>
    </div>
<?php endif; ?>

<?php
    $cs = Yii::app()->clientScript;
    $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.css');
    $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.js');
$reportId = $params['id'];

$reportItems = array();
$reportKey = array();
$reportStandIntro = array();
foreach ($childcourse as $k => $_childcourse) {
    $reportItems[$k]['courseId'] = $_childcourse->courseid;
    $reportItems[$k]['course'] = $_childcourse->reportCourse->getName();
    $reportItems[$k]['teacherName'] = $_childcourse->teacherName->getName();
    foreach ($_childcourse->reportCourse->courseScores as $val) {
        $array = array();
        foreach ($val->items as $_item) {
            $array[$_item->id] = $_item->fraction;
        }
        $reportKey[$val->getTitle()] = $val->getTitle();
        $reportStandIntro[$val->getTitle()] = $val->getName();
        $optionId = $options[$_childcourse->courseid][$val->id][$reportId];
        $reportItems[$k]['optionText'][$val->getTitle()] = $array[$optionId];
    }
}
// td key
$tdKey = array_values($reportKey);

$rportTitle = nl2br($report->getTitle());
// 报告时间
$reportDate = date("Y-m-d", $report->end_time);
// 校长寄语
$presidentMessage = nl2br($report->getPresident());
// 报告周期
$cycleTextArray = array(
    1 => Yii::t('principal', 'First'),
    2 => Yii::t('principal', 'Second'),
    3 => Yii::t('principal', 'Third'),
    4 => Yii::t('principal', 'Fourth'),
);
$cycleText = $cycleTextArray[$report->cycle];
// 学生姓名
$childName = $data->getReportChildName(true);
?>

<div class="report-wrapper web-view">
    <!--page one-->
    <div id="page-1" class="report-page page-break-after pagemt50">
        <div class='progress'>
            <div class='yellow'></div>
            <div class='green'></div>
            <div class='blue'></div>
        </div>
        <div style='padding:16px 30px 0'>
            <div class=''>
                <div><img src="http://ivyschools-www-uploads.oss-cn-beijing.aliyuncs.com/files/daystar_logo_v3.png" alt="" class='logo'></div>
                <div class='mt24 info'>
                    <div class='font16 fontBold color3'><?php echo $childName ?></div>
                    <div class='mt18'>
                        <div class='infoSub '>
                            <div class='font10 '>
                            <?php if (Yii::app()->language == 'zh_cn'): ?>
                                <span class='color6' style='width:40px;display:inline-block'>年级</span>
                            <?php else: ?>
                                <span class='color6' style='width:65px;display:inline-block'>Grade Level</span>
                            <?php endif; ?>
                                <span class='color3 fontBold'><?php echo $data->ivyclass->title ?></span>
                            </div>
                            <div class='font10 mt5'>
                            <?php if (Yii::app()->language == 'zh_cn'): ?>
                                <span class='color6' style='width:40px;display:inline-block'>班主任</span>
                            <?php else: ?>
                                <span class='color6' style='width:65px;display:inline-block'>Advisor</span>
                            <?php endif; ?>
                                <span class='color3 fontBold'><?php foreach ($homeroomTeacher as $item) {?><span><?php echo $item ?></span><?php } ?></span>
                            </div>
                            
                        </div>
                        <div class='infoSub'>
                            <div class='font10 ml30 nowrap'>
                            <?php if (Yii::app()->language == 'zh_cn'): ?>
                                <span class='color6'  style='width:75px;display:inline-block'>评估报告周期</span>
                            <?php else: ?>
                                <span class='color6'  style='width:90px;display:inline-block'>Reporting Period</span>
                            <?php endif; ?>
                                <span class='color3 fontBold'><?php echo $cycleText ?></span>
                            </div>
                            <div class='font10 mt5 ml30 nowrap'>
                            <?php if (Yii::app()->language == 'zh_cn'): ?>
                                <span class='color6' style='width:75px;display:inline-block'>评估报告日期</span>
                            <?php else: ?>
                                <span class='color6' style='width:90px;display:inline-block'>Reporting Date</span>
                            <?php endif; ?>
                                <span class='color3 fontBold'><?php echo $reportDate ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div style='clear:both'></div>
            <?php if (Yii::app()->language == 'zh_cn'): ?>
                <div class='flex mission mt20'>
                <div class='mission1'><div>启明星 <span style='line-height:52px'>宗旨</span></div></div>
                <div class='font10 color3 flex1 mission2' style='padding:22px 16px 22px 0'>我们培养具有高尚品格、好奇心、进取心的社会创新者。他们有勇气激励他人，并以积极的行动创造更加公正及可持续发展的和合世界。
                </div>
            </div>
            <div class='pt10 mt20'>
                <div class='title'><span class='title1'>中学校长寄语</span><span class='border'></span> </div>
            <?php else: ?>
                <div class='flex mission mt20'>
                <div class='mission1'><div>The <span>Mission</span></div> <div class='mt5'>of Daystar Academy</div></div>
                <div class='font10 color3 flex1 mission2'>
                    We cultivate social innovators to inspire the best in humanity and create a world that is more equitable, sustainable and united. We nurture students’ character, curiosity and motivation to positively impact their communities, demonstrating the courage to change and inspire.
                </div>
            </div>
            <div class='mt20'>
                <div class='title'><span class='title1'>Principals' Message</span><span class='border'></span> </div>
            <?php endif; ?>

                <div class='font11 color3 mt18 line_height'><?php echo $presidentMessage; ?></div>
                <div><img src="https://m2.files.ivykids.cn/cloud01-file-8025768FtGZFI0NX8KG5Wm3YDW5VV7nAVq1.png" alt="" class='cis'></div>
                <div style='clear:both'></div>
            </div>
        </div>
    </div>
    <div id="page-2" class="report-page page-break-after pagemt20">
        <div class='progress'>
            <div class='yellow'></div>
            <div class='green'></div>
            <div class='blue'></div>
        </div>
        <div class="pull-content">
        <?php if (Yii::app()->language == 'zh_cn'): ?>
            <div class='title'><span  class='title1'>学生学习目标</span><span class='border'></span> </div>
            <div class='font11 color3 mt12'>我今年的学习技能发展目标：</div>
        <?php else: ?>
            <div class='title'><span  class='title1'>Student Learning Goal</span><span class='border'></span> </div>
            <div class='font11 color3 mt12'>My goals for ATL skills development this year: </div>
        <?php endif; ?>
            <?php
                foreach ($goalData['goal_tree'] as $goal) {
                    echo '<div class="goal">';
                    echo '<div class="goalTitle">' . $goal['title'] . '</div>';
                    if (isset($goal['children']) && !empty($goal['children'])) {
                        echo '<div class="children">';
                         foreach ($goal['children'] as $children) {
                            echo '<div class="font11 color3 mt12">' . $children["title"].'</div>';
                            if (!empty($children['intro'])) {
                                echo '<div  class="font10 color6">' . $children['intro'] . '</div>';
                            }
                            if (isset($children['children']) && !empty($children['children'])) {
                                echo '<div class="mt10">';
                                foreach ($children['children'] as $childrens) {
                                    echo '<div class="intro"><div class="introTitle"><div><img src="https://m2.files.ivykids.cn/cloud01-file-8025768Fg_XpR8F5LpbS0iFSj0Idfgh2Ahb.png"></div><div class="ml24">' . $childrens["title"].'</div></div>';
                                    if (!empty($childrens['intro'])) {
                                        echo '<div>';
                                        foreach ($childrens['intro'] as $intro) {
                                            echo '<div class="text"><span class="point"></span>' . $intro . '</div>';
                                        }
                                        echo '</div>';
                                    }
                                echo '</div>';
                                }
                                echo '</div>';
                            }
                        }
                        echo '</div>';
                    }
                    echo '</div>';
                }
            ?>
        </div>
    </div>
    <div id="page-3" class="report-page page-break-after pagemt20" >
        <div class='progress'>
            <div class='yellow'></div>
            <div class='green'></div>
            <div class='blue'></div>
        </div>
        <div class='pull-content wordText font12'>
            <div>
            <?php if (Yii::app()->language == 'zh_cn'): ?>
                <div class='title'><span class='title1'>课程报告</span><span class='border'></span> </div>
            <?php else: ?>
                <div class='title'><span  class='title1'>Class Reports</span><span class='border'></span> </div>
            <?php endif; ?>
            <?php foreach ($reportStandIntro as $key => $value) { ?>
                <div class='mt18 color3'>
                    <div class='font12 fontBold'><?php echo $key ?>：</div>
                    <div class='font11 mt5'><?php echo $value ?></div>
                </div>
            <?php } ?>
                <table class='mt18 tableText subject'> 
                    <tr class='bgGray font11'>
                        <th><?php echo Yii::t('ptc', 'Subject') ?></th>
                        <?php foreach ($tdKey as $index => $td) { ?>
                            <th class='text-center' width='130px'><?php echo $td ?></th>
                        <?php } ?>
                    </tr>
                    <tbody>
                        <?php  foreach ($reportItems as $course) {?>
                        <tr>
                            <td>
                                <div class='font10 color3'><?php echo $course["course"] ?></div> 
                                <div class='color6' style='font-size:9px'><?php echo $course["teacherName"] ?></div>
                            </td>
                            <?php foreach ($tdKey as $td) { ?>
                                <td class='text-center'>
                                    <div  class='font10 color3'><?php echo $course["optionText"][$td]; ?></div>
                                </td>
                            <?php } ?>
                        </tr>
                        <?php } ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div id="page-4" class="report-page page-break-after pagemt20 pagemb20" >
        <div class='progress'>
            <div class='yellow'></div>
            <div class='green'></div>
            <div class='blue'></div>
        </div>
        <div class='pull-content wordText font12'>
        <?php if (Yii::app()->language == 'zh_cn'): ?>
            <div class='title'><span  class='title1'>过程性报告描述</span><span class='border'></span> </div>
            <table class='mt20 tableText'>
                <tr class='bgGray '>
                    <th colspan='2' class='text-center font11'>任务完成情况等级说明</th>
                </tr>
                <tr>
                    <td width='90' >
                        <div class='font10 color3 line14'>BE – 低于预期</div>
                    </td>
                    <td>
                        <div class='font10 color3 line14'><span class='fontBold'>任务完成情况：</span><span>学生在完成指定任务时持续遇到困难。作业经常不提交、不完整或远远超过截止日期才提交。已完成的作业质量显著低于课程的预期标准。</span></div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <div class='font10 color3 line14'>AE – 接近预期</div>
                    </td>
                    <td>
                        <div class='font10 color3 line14'><span class='fontBold'>任务完成情况：</span><span>学生完成了一些任务，但可能在一惯性方面存在困难。作业有时会迟交或不完整。工作质量正在提高，但在某些方面仍未达到课程预期。</span></div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <div class='font10 color3 line14'>ME – 达到预期</div>
                    </td>
                    <td>
                        <div class='font10 color3 line14'><span class='fontBold'>任务完成情况：</span><span>学生定期按时完成指定任务，并达到令人满意的标准。工作始终满足课程要求，表现出对学科内容的扎实理解。</span></div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <div class='font10 color3 line14'>EE – 超出预期</div>
                    </td>
                    <td>
                        <div class='font10 color3 line14'><span class='fontBold'>任务完成情况：</span><span>学生始终以高标准完成所有指定任务，经常超出要求。作业总是按时提交，并展示出对概念的卓越理解和应用，常常超越标准课程预期。</span></div>
                    </td>
                </tr>
            </table>
            <table class='mt20 tableText'>
                <tr class='bgGray '>
                    <th colspan='2' class='text-center font11'>课堂参与度等级说明</th>
                </tr>
                <tr>
                    <td width='90'>
                       <div class='font10 color3 line14'> Beginning – 起步</div>
                    </td>
                    <td>
                        <div class='font10 color3 line14'><span class='fontBold'>课堂参与度：</span><span>学生开始探索参与课堂的方式。他们开始理解参与的价值，在鼓励下可能偶尔会参与讨论。</span></div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <div class='font10 color3 line14'>Developing – 发展</div>
                    </td>
                    <td>
                        <div class='font10 color3 line14'><span class='fontBold'>课堂参与度：</span><span>学生在课堂参与方面变得更加自在。他们正在建立提问和分享的信心，尽管可能仍需要偶尔的提醒。随着他们对所学内容理解的加深，他们的贡献增多，相关性更强。</span></div>
                    </td>
                </tr>
                <tr>
                    <td>
                   <div class='font10 color3 line14'>Proficient – 熟练</div> 
                    </td>
                    <td>
                        <div class='font10 color3 line14'><span class='fontBold'>课堂参与度：</span><span> 学生积极参与课堂活动和讨论。他们贡献相关想法，提出深思熟虑的问题，并与同学良好合作。他们逐渐深刻理解自己的参与如何提升自己和同学的学习。</span></div>
                    </td>
                </tr>
                <tr>
                    <td>
                    <div class='font10 color3 line14'>Mastering – 精通</div> 
                    </td>
                    <td>
                        <div class='font10 color3 line14'><span class='fontBold'>课堂参与度：</span><span> 学生表现出具有榜样性的参与度，始终热情地参与课堂活动和讨论。他们分享有见地的评论，提出深入的问题，并在小组任务中发挥领导作用。学生的贡献显著提升了整个班级的学习体验。</span></div>
                    </td>
                </tr>
            </table>
        <?php else: ?>
            <div class='title'><span  class='title1'>Progress Report Descriptors</span><span class='border'></span> </div>
            <table class='mt20 tableText'>
                <tr class='bgGray '>
                    <th colspan='2' class='text-center font11'>Task Completion Rating Description</th>
                </tr>
                <tr>
                    <td width='90'>
                        <div class='font10 color3 line14'>BE - Below</div>
                        <div class='font10 color3 line14'>Expectations</div>
                    </td>
                    <td>
                        <div class='font10 color3 line14'><span class='fontBold'>Task Completion: </span><span>The student consistently struggles to complete assigned tasks. Work is often missing, incomplete, or submitted well past deadlines. The quality of completed work falls significantly below the expected standards for the course.</span></div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <div class='font10 color3 line14'>AE - Approaching</div>
                        <div class='font10 color3 line14'>Expectations</div>
                    </td>
                    <td>
                        <div class='font10 color3 line14'><span class='fontBold'>Task Completion: </span><span>The student completes some tasks but may struggle with consistency. Work is sometimes submitted late or incomplete. The quality of work is improving but still falls short of the course expectations in some areas.</span></div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <div class='font10 color3 line14'>ME - Meeting</div>
                        <div class='font10 color3 line14'>Expectations</div>
                    </td>
                    <td>
                        <div class='font10 color3 line14'><span class='fontBold'>Task Completion: </span><span> The student regularly completes assigned tasks on time and to a satisfactory standard. Work consistently meets the course requirements, demonstrating a solid understanding of the subject matter.</span></div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <div class='font10 color3 line14'>EE - Exceeding</div>
                        <div class='font10 color3 line14'>Expectations</div>
                    </td>
                    <td>
                        <div class='font10 color3 line14'><span class='fontBold'>Task Completion: </span><span> The student consistently completes all assigned tasks to a high standard, often exceeding requirements. Work is always submitted on time and demonstrates exceptional understanding and application of concepts, often going beyond the standard course expectations.</span></div>
                    </td>
                </tr>
            </table>
            <table class='mt20 tableText'>
                <tr class='bgGray '>
                    <th colspan='2' class='text-center font11'>Class Engagement and Participation Rating Description</th>
                </tr>
                <tr>
                    <td width='90'>
                       <div class='font10 color3 line14'>Beginning</div> 
                    </td>
                    <td>
                        <div class='font10 color3 line14'><span class='fontBold'>Class Engagement and Participation: </span><span>The student is starting to explore ways to engage in class. They are beginning to understand the value of participation and may occasionally contribute to discussions when encouraged.</span></div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <div class='font10 color3 line14'>Developing</div>
                    </td>
                    <td>
                        <div class='font10 color3 line14'><span class='fontBold'>Task Completion: </span><span>The student is growing more comfortable with class participation. They are developing the confidence to share ideas and ask questions, though they may still need occasional prompting. Their contributions are becoming more frequent and relevant as they gain confidence in their understanding.</span></div>
                    </td>
                </tr>
                <tr>
                    <td>
                    <div class='font10 color3 line14'>Proficient</div>
                    </td>
                    <td>
                        <div class='font10 color3 line14'><span class='fontBold'>Task Completion: </span><span> The student actively engages in class activities and discussions. They contribute relevant ideas, ask thoughtful questions, and collaborate well with peers. They are developing a strong sense of how their participation enhances both their own learning and that of their classmates.</span></div>
                    </td>
                </tr>
                <tr>
                    <td>
                    <div class='font10 color3 line14'>Mastering</div>
                    </td>
                    <td>
                        <div class='font10 color3 line14'><span class='fontBold'>Task Completion: </span><span> The student demonstrates exemplary engagement, consistently participating in class activities and discussions with enthusiasm. They offer insightful comments, ask probing questions, and take leadership roles in group work. The student's contributions significantly enhance the learning experience for the entire class.</span></div>
                    </td>
                </tr>
            </table>
        <?php endif; ?>
        </div>
    </div>
</div>
</body>
</html>
