<?php
Yii::import('common.models.calendar.*');
Yii::import('application.components.policy.*');
$policyApi = new PolicyApi($this->branchId);
$availableYears = $policyApi->getAvailableYears($this->branchId);
$semesterList = array(0=>'全部', 1=>'上学期', 2=>'下学期');
$semester = $_GET['semester'] ? $_GET['semester'] : 0;
if(isset($_GET['startyear']) && $_GET['startyear']){
    $startyear = $_GET['startyear'];
    $sModel = CalendarSchool::model()->findByAttributes(array('branchid'=>$this->branchId, 'startyear'=>$startyear));
    //如果选择的是下一学年
    $nextYear = 0;
    if (!$sModel->is_selected) {
        $crit = new CDbCriteria;
        $crit->compare('nextYear.schoolid', $this->branchId);
        $crit->compare('nextYear.stat', 20);
        $crit->compare('status', ChildProfileBasic::STATS_REGISTERED);
        $crit->with = 'nextYear';
        $nextYear = ChildProfileBasic::model()->count($crit);
    }
    $crit = new CDbCriteria();
    $crit->compare('schoolid', $this->branchId);
    $crit->compare('classid', 0);
    $crit->compare('status', ChildProfileBasic::STATS_REGISTERED);

    $db = Yii::app()->db;
    $sql = "SELECT COUNT(*) FROM (SELECT COUNT(*) FROM ivy_child_profile_basic AS a LEFT JOIN ivy_invoice_invoice AS b ON a.childid = b.childid WHERE 
        a.schoolid = '$this->branchId' AND  
        a.classid = 0 AND  
        (a.status = 888 or a.status = 999) AND 
        b.status < 88 AND 
        b.calendar_id = $sModel->yid
        GROUP BY a.childid) AS c;";
    $command = $db->createCommand($sql);
    $noclassCount = $command->queryScalar() + ChildProfileBasic::model()->count($crit) - $nextYear;
}
else{
    $sModel = CalendarSchool::model()->findByAttributes(array('branchid'=>$this->branchId, 'is_selected'=>1));
    $startyear = $sModel->startyear;
}
$yid = $sModel->yid;
$relation = $sModel->is_selected ? 'studentCount' : 'reserveCount';
$classList = IvyClass::model()->with($relation)->getClassList($this->branchId, $yid);
global $paymentMappings;

$invoiceTypes = $this->invoiceTypes();
$langFile = 'payment_'.$policyApi->constantFlag;
$paymentKeyFlip = array_flip($paymentMappings['dbFeeType']);

$paymentTypeCss = $this->paymentTypeCss();
?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><a href="<?php echo $this->createUrl('//mfinance/invoice/index');?>"><?php echo Yii::t($langFile, '财务管理');?></a></li>
        <li class="active"><?php echo Yii::t($langFile, '学生费用报表');?></li>
    </ol>
    <div class="row">
        <div class="col-md-2">
            <div class="panel panel-default">
                <div class="panel-heading"><?php echo Yii::t('campus','Students List');?></div>
                <div class="list-group class-list-group">
                    <?php
                        foreach($classList as $_class):
                            if (!$sModel->is_selected) {
                                $crit = new CDbCriteria;
                                $crit->compare('nextYear.schoolid', $this->branchId);
                                $crit->compare('nextYear.classid', $_class->classid);
//                                $crit->compare('nextYear.stat', 20);
                                $crit->with = 'nextYear';
                                $crit->addCondition('t.status<100 or t.status>100');
                            }else{
                                $crit = new CDbCriteria;
                                $crit->compare('t.schoolid', $this->branchId);
                                $crit->compare('t.classid', $_class->classid);
                                $crit->addCondition('t.status<100 or t.status>100');
                            }
                            $studentNum = ChildProfileBasic::model()->count($crit);
                    ?>
                        <a href="javascript:;" data-classid="<?php echo $_class->classid?>" onclick="loadGroupInvoice(this)" class="list-group-item"><?php echo $_class->title;?> <span class="badge"><?php echo $studentNum;?></span></a>
                    <?php endforeach;?>
                    <a href="javascript:;" data-classid="0" onclick="loadGroupInvoice(this)" class="list-group-item">
                        <?php echo Yii::t('message','No class assigned');?> <span class="badge"><?php echo $noclassCount;?></span></a>
                </div>
            </div>
        </div>
        <div class="col-md-10">
            <ul class="nav nav-pills mb10">
                <li class="active dropdown">
                    <a data-toggle="dropdown" href="javascript:;"><?php echo $startyear.' - '.($startyear+1)?> <span class="caret"></span></a>
                    <?php if(count($availableYears) > 1):?>
                    <ul class="dropdown-menu">
                        <?php foreach($availableYears as $year):?>
                        <li><a href="<?php echo $this->createUrl('index', array('startyear'=>$year))?>"><?php echo $year.' - '.($year+1)?></a></li>
                        <?php endforeach;?>
                    </ul>
                    <?php endif;?>
                </li>
                <li class="active dropdown">
                    <a data-toggle="dropdown" href="javascript:;"><?php echo $semesterList[$semester]; ?> <span class="caret"></span></a>
                    <ul class="dropdown-menu">
                        <li><a href="<?php echo $this->createUrl('index', array('startyear'=>$startyear, 'semester'=>0))?>">全部</a></li>
                        <li><a href="<?php echo $this->createUrl('index', array('startyear'=>$startyear, 'semester'=>1))?>">上学期</a></li>
                        <li><a href="<?php echo $this->createUrl('index', array('startyear'=>$startyear, 'semester'=>2))?>">下学期</a></li>
                    </ul>
                </li>
                <li>
                    <a class="btn btn-default btn-lg" style="padding:8px 14px;border-radius:4px;" role="button" href="<?php echo $this->createUrl('///mfinance/student/export',array('calendarId'=>$yid,'startYear'=>$startyear,'semester'=>$semester)); ?>"><span class="glyphicon glyphicon-log-out"></span> <?php echo Yii::t($langFile, '导出EXCEL');?></a>
                </li>
                <li>
                    <button type="button" class="btn btn-default btn-lg excell" style="padding:8px 14px;border-radius:4px;" role="button" ><span class="glyphicon glyphicon-log-out"></span> <?php echo Yii::t($langFile, '导出EXCEL');?>-Beta</button>
                </li>
            </ul>
            <div id="select-type" class="well well-sm" style="display:none;">
                <?php
                foreach ($paymentTypeCss as $key=>$val):
                ?>
                <span class="mr10">
                    <?php echo $val['title'];?>
                    <label style="cursor:pointer;" class="label <?php if ($val['default']===false){echo 'label-default';}else{echo $val['css'];}?>" title="<?php echo $val['title'];?>" data-value="<?php echo $key;?>" onclick="checkData(this)" data-default="<?php echo $val['default'];?>">&nbsp</label></span>
                <?php endforeach;?>
                <span class="alert alert-success" role="alert" style="padding:5px;"><?php echo Yii::t($langFile,'点击色块过滤所对应的类型');?></span>
            </div>
            <table class="table table-bordered" id="main-zone" style="display: none;">
                <thead>
                    <tr class="active">
                        <th width="9.7%"><h4><span class="glyphicon glyphicon-user"></span> <?php echo Yii::t($langFile,'Student');?></h4></th>
                        <td width="9.7%"><h5>账户余额</h5></td>
                        <?php foreach($invoiceTypes as $_ptype):?>
                            <?php if ($_ptype === 'sum'):?>
                                <th width="9.7%" data-batchptype="<?php echo $_ptype;?>">
                                    <h5><?php echo Yii::t($langFile,$_ptype);?></h5></th>
                            <?php else:?>
                                <th width="9.7%" data-batchptype="<?php echo $_ptype;?>">
                                    <h5><?php echo Yii::t($langFile, $paymentMappings['titleFeeType'][$paymentKeyFlip[$_ptype]]);?></h5></th>
                            <?php endif;?>
                        <?php endforeach;?>
                    </tr>
                </thead>
                <tbody id="class-child-invoices">

                </tbody>
            </table>
        </div>
    </div>
</div>
<div id="add-invoice-template" style="display: none">
    <table>
        <tbody>
            <?php foreach ($paymentTypeCss as $key=>$pt):?>
            <tr>
                <td data-litype="<?php echo $key;?>" <?php if($pt['default']=== false):?> style="display:none;" <?php endif;?>></td>
            </tr>
             <?php endforeach;?>
        </tbody>
    </table>
    <!--
    <div class="progress">
        <div class="progress-bar progress-bar-success" style="width: 40%">
            <span class="sr-only">1000</span>
        </div>
        <div class="progress-bar progress-bar-warning progress-bar-striped" style="width: 30%">
            <span class="sr-only">5000</span>
        </div>
        <div class="progress-bar progress-bar-warning progress-bar-striped" style="width: 30%">
            <span class="sr-only">5000</span>
        </div>
    </div>
    -->
</div>

<script type="text/template" id="student-invoices-row-template">
    <tr data-studentid="<%= id %>">
        <th class="active">
            <h4>
            <a href="/child/invoice/finance?t=invoice&childid=<%= id %>" target="_blank">
            <%- name %></a>
            </h4>
            <h5 title="<?php echo Yii::t('child','Class');?>" class="text-muted"><%- className %></h5>
            <div><%- status %></div>
        </th>
        <td><%- credit %></td>
        <?php foreach($invoiceTypes as $_ptype):?>
            <td data-ptype="<?php echo $_ptype;?>">
            </td>
        <?php endforeach;?>
    </tr>
</script>

<script>
    var loadGroupInvoice;
    var renderTableSchema = null;
    var renderTableInvoices = null;
    var export_data = null;//要导出的所有数据
    var childInvoices = null;
    var studentRow = _.template($('#student-invoices-row-template').html());
    var selectTypeTemplate = $('#select-type').html();
    var invoiceTypes = <?php echo CJSON::encode($invoiceTypes);?>;
    var busy = 0;

    var currentCalendarId = <?php echo $yid;?>;
    var currentClassId = null;
    var paymentTypeCss = <?php echo CJSON::encode($paymentTypeCss);?>;
    var semester = <?php echo $semester;?>;
    var filename_base = "<?php echo $startyear.'-'.($startyear+1).$this->branchObj->title?>学生费用报表";
    //js导出
    $(".excell").click(function(){
        var $btn = $(this).button('loading')
        var rows = [{hpt:25},{hpt:25},{hpt:25}];//行高
        $.post('<?php echo $this->createUrl('getAllStudentInvoice')?>',
            {
                calendar: currentCalendarId,
                semester:semester
            },
            function(data){
                if (data.state === 'success'){
                    export_data = data.data
                    const filename = filename_base+'.xlsx';
                    const ws_name = "A1";
                    const exportDatas = [];
                    const header = {
                        'ID': '序号',
                        'eName': 'English Name',
                        'cName': '姓名',
                        'status': '状态',
                        'className': '年级',
                        'withDrawalTxt': '退学流程',
                        'credit': '账户余额',
                        'isnew': '是否新生',
                        'fee_type': '付款类型',
                        'discount': '使用折扣',
                        'tuition': '学费',
                        'tuition1': '学费1',
                        'tuition2': '学费2',
                        'tuition3': '学费3',
                        'deposit': '预缴学费',
                        'deposit1': '预缴学费1',
                        'deposit2': '预缴学费2',
                        'deposit3': '预缴学费3',
                        'tuition_sum': '学费汇总',
                        'tuition_sum1': '学费汇总1',
                        'tuition_sum2': '学费汇总2',
                        'tuition_sum3': '学费汇总3',
                        'bus': '校车费',
                        'bus1': '校车费1',
                        'bus2': '校车费2',
                        'bus3': '校车费3',
                        'lunch': '午餐费',
                        'lunch1': '午餐费1',
                        'lunch2': '午餐费2',
                        'lunch3': '午餐费3',
                        'uniform': '校服费',
                        'uniform1': '校服费1',
                        'uniform2': '校服费2',
                        'uniform3': '校服费3',
                        'sum': '汇总',
                        'sum1': '汇总1',
                        'sum2': '汇总2',
                        'sum3': '汇总3',
                    };
                    exportDatas.push(header)
                    const merges = [
                        {s: {r: 0, c: 10}, e: {r: 0, c: 13}},
                        {s: {r: 0, c: 14}, e: {r: 0, c: 17}},
                        {s: {r: 0, c: 18}, e: {r: 0, c: 21}},
                        {s: {r: 0, c: 22}, e: {r: 0, c: 25}},
                        {s: {r: 0, c: 26}, e: {r: 0, c: 29}},
                        {s: {r: 0, c: 30}, e: {r: 0, c: 33}},
                        {s: {r: 0, c: 34}, e: {r: 0, c: 37}},
                        {s: {r: 0, c: 38}, e: {r: 0, c: 41}},

                        {s: {r: 0, c: 10}, e: {r: 0, c: 13}},
                        {s: {r: 0, c: 14}, e: {r: 0, c: 17}},
                        {s: {r: 0, c: 18}, e: {r: 0, c: 21}},
                        {s: {r: 0, c: 22}, e: {r: 0, c: 25}},
                        {s: {r: 0, c: 26}, e: {r: 0, c: 29}},
                        {s: {r: 0, c: 30}, e: {r: 0, c: 33}},
                        {s: {r: 0, c: 34}, e: {r: 0, c: 37}},
                        {s: {r: 0, c: 38}, e: {r: 0, c: 41}},
                    ];//需要合并的单元格
                    exportDatas.push({
                        'ID': '',
                        'eName': '',
                        'cName': '',
                        'status': '',
                        'className': '',
                        'withDrawalTxt': '',
                        'credit': '',
                        'isnew': '',
                        'fee_type': '',
                        'discount': '',
                        'tuition': '应收',
                        'tuition1': '已收',
                        'tuition2': '未收',
                        'tuition3': '已退',
                        'deposit': '应收',
                        'deposit1': '已收',
                        'deposit2': '未收',
                        'deposit3': '已退',
                        'tuition_sum': '应收',
                        'tuition_sum1': '已收',
                        'tuition_sum2': '未收',
                        'tuition_sum3': '已退',
                        'bus': '应收',
                        'bus1': '已收',
                        'bus2': '未收',
                        'bus3': '已退',
                        'lunch': '应收',
                        'lunch1': '已收',
                        'lunch2': '未收',
                        'lunch3': '已退',
                        'uniform': '应收',
                        'uniform1': '已收',
                        'uniform2': '未收',
                        'uniform3': '已退',
                        'sum': '应收',
                        'sum1': '已收',
                        'sum2': '未收',
                        'sum3': '已退',
                    })
                    const amount_field = [
                        'tuition',
                        'deposit',
                        'tuition_sum',
                        'bus',
                        'lunch',
                        'uniform',
                        'sum',
                    ]
                    for (let i=0;i<export_data.length;i++){
                        if(export_data[i]['students']){
                            var studentsList = export_data[i]['students'];
                            for (let child_id in studentsList){
                                var student_data_base = {}
                                student_data_base.ID = child_id
                                student_data_base.eName = studentsList[child_id]['eName']
                                student_data_base.cName = studentsList[child_id]['cName']
                                student_data_base.status = studentsList[child_id]['status']
                                student_data_base.className = studentsList[child_id]['className']
                                student_data_base.withDrawalTxt = studentsList[child_id]['withDrawalTxt']
                                student_data_base.credit = studentsList[child_id]['credit']
                                student_data_base.isnew = studentsList[child_id]['isnew']
                                student_data_base.fee_type = studentsList[child_id]['fee_type']
                                student_data_base.discount = studentsList[child_id]['discount']
                                if(export_data[i]['invoices']){
                                    var studentInvoicesByChildId = export_data[i]['invoices'];//学生的账单
                                    for (let j=0;j<amount_field.length;j++){
                                        //应收
                                        student_data_base[amount_field[j]] = studentInvoicesByChildId[child_id] && studentInvoicesByChildId[child_id][amount_field[j]] && studentInvoicesByChildId[child_id][amount_field[j]]['accountsAmount'] ? parseFloat(studentInvoicesByChildId[child_id][amount_field[j]]['accountsAmount']) : ''
                                        //已收
                                        student_data_base[amount_field[j]+'1'] = studentInvoicesByChildId[child_id] && studentInvoicesByChildId[child_id][amount_field[j]] &&studentInvoicesByChildId[child_id][amount_field[j]]['payedAmount'] ? parseFloat(studentInvoicesByChildId[child_id][amount_field[j]]['payedAmount']) : ''
                                        //未收
                                        student_data_base[amount_field[j]+'2'] = studentInvoicesByChildId[child_id] && studentInvoicesByChildId[child_id][amount_field[j]] &&studentInvoicesByChildId[child_id][amount_field[j]]['unpayAmount'] ? parseFloat(studentInvoicesByChildId[child_id][amount_field[j]]['unpayAmount']) : ''
                                        //已退
                                        student_data_base[amount_field[j]+'3'] = studentInvoicesByChildId[child_id] && studentInvoicesByChildId[child_id][amount_field[j]] &&studentInvoicesByChildId[child_id][amount_field[j]]['refundAmount'] ? parseFloat(studentInvoicesByChildId[child_id][amount_field[j]]['refundAmount']) : ''
                                    }
                                }
                                exportDatas.push(student_data_base)
                                rows.push({hpt:25})//行高
                            }
                        }
                    }
                    var wb = XLSX.utils.json_to_sheet(exportDatas,{
                        origin:'A1',// 从A1开始增加内容
                        header: Object.keys(header),
                    });
                    wb['!merges'] = merges;//需要合并的单元格
                    wb['!rows'] = rows;
                    const workbook = XLSX.utils.book_new();
                    XLSX.utils.book_append_sheet(workbook, wb, ws_name);
                    const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                    const blob = new Blob([wbout], {type: 'application/octet-stream'});
                    let link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    link.download = filename;
                    link.click();
                    setTimeout(function() {
                        // 延时释放掉obj
                        URL.revokeObjectURL(link.href);
                        link.remove();
                        $btn.button('reset')
                    }, 500);
                }else {
                    alert(data.message);
                }
            },
            'json');
    })

    $(function(){
        $.datepicker.setDefaults($.datepicker.regional['<?php echo (Yii::app()->language == 'en_us')? '': 'zh-CN'?>']);

        loadGroupInvoice = function(obj){
            if(busy) return;
            busy = 1;
            $('#main-zone').hide();
            var _classid = $(obj).data('classid');
            currentClassId = _classid;
            $(obj).siblings().removeClass('active');
            $(obj).addClass('active');

            $.post('<?php echo $this->createUrl('getStudentInvoice')?>', {classid: _classid, calendar: currentCalendarId, semester}, function(data){
                if (data.state === 'success'){
                    $('#main-zone').show();
                    childInvoices = data.data;
                    renderTableSchema();
                    renderTableInvoices(childInvoices.invoices);
                }
                else {
                    alert(data.message);
                }
                busy = 0;
            }, 'json');
        };

        //画表体
        renderTableSchema = function(){
            $('#class-child-invoices').empty();
            $('#select-type').empty();
            var _trs = '';
            _.each(childInvoices.students, function(item,index){
                _trs += studentRow(item);
            });
            $('#class-child-invoices').append(_trs);
            $('#class-child-invoices').find('td[data-ptype]').append( $('#add-invoice-template').html() );
            $('#select-type').show();
            $('#select-type').append(selectTypeTemplate);
        }

        //画表格内容
        renderTableInvoices = function(invoices){
            _.each(invoices, function(_invoice,index){
                var _tr = $('#class-child-invoices').find('tr[data-studentid|="'+index+'"]');
                _.each(_invoice, function(value,key){
                    var _td = _tr.find('td[data-ptype|="'+key+'"]');
                    _.each(value, function(val,k){
                        if (val){
                            _td.find('td[data-litype|="'+k+'"]').append('<span class="label '+paymentTypeCss[k].css+'" title="'+paymentTypeCss[k].title+'">&nbsp;</span><span class="ml5">'+$.number(val,2)+'</span>');
                        }
                    })
                })
            })
        }

        //根据选择显示不同数据
        checkData = function(_this){
            var value = $(_this).data('value');
            if ($(_this).hasClass('label-default')){
                $(_this).removeClass('label-default');
                $(_this).addClass(paymentTypeCss[value].css);
            }else{
                $(_this).addClass('label-default');
                $(_this).removeClass(paymentTypeCss[value].css);
            }
            $('#class-child-invoices').find('td[data-litype|="'+value+'"]').toggle();
        }

    })

</script>

<style>
    #class-child-invoices td .label{
        font-size: 100%;
        line-height: 2;
    }
</style>
