<?php

/**
 * This is the model class for table "ivy_learning_semester".
 *
 * The followings are the available columns in table 'ivy_learning_semester':
 * @property integer $id
 * @property integer $yid
 * @property integer $semester
 * @property integer $childid
 * @property integer $classid
 * @property integer $lid
 * @property string $intro_img
 * @property string $intro_img_cropper
 * @property string $items_img
 * @property string $items_img_cropper
 * @property string $proof_text
 * @property integer $status
 * @property integer $created_at
 * @property integer $created_by
 * @property integer $updated_at
 * @property integer $updated_by
 */
class LearningSemester extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_learning_semester';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('yid, semester, childid, classid, lid, status, created_at, created_by, updated_at, updated_by', 'required'),
			array('intro_img, items_img, yid, semester, childid, classid, lid, status, created_at, created_by, updated_at, updated_by', 'numerical', 'integerOnly' => true),
			array('proof_text', 'length', 'max' => 800),
			array('intro_img_cropper, items_img_cropper', 'length', 'max' => 255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, yid, semester, childid, classid, lid, intro_img, items_img, proof_text, status, created_at, created_by, updated_at, updated_by', 'safe', 'on' => 'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array();
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'yid' => 'Yid',
			'semester' => 'Semester',
			'childid' => 'Childid',
			'classid' => 'Classid',
			'lid' => 'Lid',
			'intro_img' => '配图1',
			'items_img' => '配图2',
			'proof_text' => Yii::t('teaching', 'Evidence from Teachers\' Observation'),
			'status' => 'Status',
			'created_at' => 'Created At',
			'created_by' => 'Created By',
			'updated_at' => 'Updated At',
			'updated_by' => 'Updated By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria = new CDbCriteria;

		$criteria->compare('id', $this->id);
		$criteria->compare('yid', $this->yid);
		$criteria->compare('semester', $this->semester);
		$criteria->compare('childid', $this->childid);
		$criteria->compare('classid', $this->classid);
		$criteria->compare('lid', $this->lid);
		$criteria->compare('intro_img', $this->intro_img, true);
		$criteria->compare('items_img', $this->items_img, true);
		$criteria->compare('proof_text', $this->proof_text, true);
		$criteria->compare('status', $this->status);
		$criteria->compare('created_at', $this->created_at);
		$criteria->compare('created_by', $this->created_by);
		$criteria->compare('updated_at', $this->updated_at);
		$criteria->compare('updated_by', $this->updated_by);

		return new CActiveDataProvider($this, array(
			'criteria' => $criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return LearningSemester the static model class
	 */
	public static function model($className = __CLASS__)
	{
		return parent::model($className);
	}

}
