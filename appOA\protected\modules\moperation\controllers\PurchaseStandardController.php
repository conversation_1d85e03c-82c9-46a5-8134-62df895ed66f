<?php

class PurchaseStandardController extends ProtectedController
{
	public $actionAccessAuths = array(
		// 'View'=>'ivystaff_opgeneral',
		'Index'=>'oOperationsView',
		'StandardList'=>'oOperationsView',
		'Update'=>'oOperationsAdmin',
		'CopyStandard'=>'oOperationsAdmin',
		'Delete'=>'oOperationsAdmin',
		'DeleteItem'=>'oOperationsAdmin',
	);

	public $dialogWidth = 600;
	
	public function init() {
	    parent::init();
	    Yii::import('common.models.purchase.*');

	    Yii::app()->theme = 'blue';
	    $this->layout = "//layouts/column1";

	    $this->modernMenuFlag = 'hqOp';
	    $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
	    $this->modernMenuCategoryTitle = Yii::t('site','HQ Operations');
	}

	/**
	 * Displays a particular model.
	 * @param integer $id the ID of the model to be displayed
	 */
	public function actionView($id)
	{
		$psModel = $this->loadModel($id);
		if (!$psModel->isNewRecord) {
			$ppiModel = new PurchaseProductsItem();
			$ppiModel->unsetAttributes();  // clear any default values
			$ppiModel->sid = $id;
			$name = Yii::app()->request->getParam('name',0);
			if ($name) {
				$ppiModel->name = $name;
			}
			$this->render('view',array(
				'psModel'=>$psModel,
				'ppiModel'=>$ppiModel,
			));
		}
		$this->redirect(array('index'));
	}


	/**
	 * Updates a particular model.
	 * If update is successful, the browser will be redirected to the 'view' page.
	 * @param integer $id the ID of the model to be updated
	 */
	public function actionUpdate($id=0)
	{
		$model=$this->loadModel($id);

		if(isset($_POST['PurchaseStandard']))
		{
			$time = time();
			$model->attributes=$_POST['PurchaseStandard'];
			if ($model->isNewRecord) {
				$model->add_timestamp = $time;
			}
			$model->update_timestamp = $time;
			$model->add_user = $this->staff->uid;
			if($model->save()){
				$this->addMessage('state','success');
				$this->addMessage('callback','cbStandard');
				$this->addMessage('message', Yii::t('message','Data saved!'));
			}else{
				$this->addMessage('state','fail');
				$errs = current($model->getErrors());
				$this->addMessage('message', $errs?$errs[0]:Yii::t('message','Saving Failed!'));
			}
			$this->showMessage();
		}

		$this->renderPartial('update',array(
			'model'=>$model,
		));
	}

	/**
	 * 复制标配
	 * @param  integer $id [description]
	 * @return [type]      [description]
	 */
	public function actionCopyStandard($id=0)
	{
		$model=$this->loadModel();

		if(isset($_POST['PurchaseStandard']))
		{
			$_POST['PurchaseStandard']['add_timestamp'] = time();
			$_POST['PurchaseStandard']['add_user'] = $this->staff->uid;
			$model->attributes=$_POST['PurchaseStandard'];
			$model->update_timestamp = time();
			if($model->save()){
				//复制商品相关表
				$ppiModels = PurchaseProductsItem::model()->findAll('sid=:sid',array('sid'=>$id));
				foreach ($ppiModels as $ppiModel) {
					$newModel = new PurchaseProductsItem;
					$newModel->attributes = $ppiModel->attributes;
					$newModel->sid = $model->id;
					$newModel->save();
				}
				$this->addMessage('state','success');
				$this->addMessage('message', Yii::t('message','Data saved!'));
				$this->addMessage('callback','cbStandard');
			}else{
				$this->addMessage('state','fail');
				$errs = current($model->getErrors());
				$this->addMessage('message', $errs?$errs[0]:Yii::t('message','Saving Failed!'));
			}
			$this->showMessage();
		}

		$this->renderPartial('update',array(
			'model'=>$model,
		));
	}

	/**
	 * Deletes a particular model.
	 * If deletion is successful, the browser will be redirected to the 'admin' page.
	 * @param integer $id the ID of the model to be deleted
	 */
	public function actionDelete()
	{
		if (isset($_POST['id'])) {
			$id = Yii::app()->request->getPost('id',0);
			$model = $this->loadModel($id);
			if ($model->delete()){
				//删除purchaseProductsItem相关的记录
				PurchaseProductsItem::model()->deleteAll('sid = :sid',array(':sid'=>$id));
				$this->addMessage('state','success');
				$this->addMessage('message','Delete Successed!');
				$this->addMessage('callback','cbStandard');
			}else {
				$this->addMessage('state','fail');
			}
			$this->showMessage();
		}
	}
	/**
	 * [actionDelete description]
	 * @param  integer $id [description]
	 * @return [type]      [description]
	 */
	public function actionDeleteItem()
	{
		if (isset($_POST['id'])) {
			$id = Yii::app()->request->getPost('id',0);
			$model = PurchaseProductsItem::model()->findByPk($id);
			if($model->delete()){
				$this->addMessage('state','success');
				$this->addMessage('message','Delete Successed!');
				$this->addMessage('callback','cbStandard');
			}else{
				$this->addMessage('state','fail');
			}
			$this->showMessage();
		}
	}

	/**
	 * Manages all models.
	 */
	public function actionIndex()
	{
		$model=new PurchaseStandard('search');
		$model->unsetAttributes();  // clear any default values
		if(isset($_GET['PurchaseStandard']))
			$model->attributes=$_GET['PurchaseStandard'];

		$this->render('index',array(
			'model'=>$model,
		));
	}

	/**
	 * 标配列表
	 * @param  integer $id [description]
	 * @return [type]      [description]
	 */
	public function actionStandardList($id = 0)
	{
		$psModel = PurchaseStandard::model()->findByPk($id);
		$ppiModel = new PurchaseProductsItem();
		$ppiModel->unsetAttributes();  // clear any default values
		$ppiModel->sid = $id;
		$name = Yii::app()->request->getParam('name',0);
		if ($name) {
			$ppiModel->name = $name;
		}
		$this->render('standardList',array(
			'psModel'=>$psModel,
			'ppiModel'=>$ppiModel,
		));
	}

	/**
	 * 修改标配中商品的数量
	 * @return [type] [description]
	 */
	public function actionChangeNum()
	{
		if (isset($_POST)) {
			$id = Yii::app()->request->getPost('id',0);
			$num = Yii::app()->request->getPost('num',0);

			$ppiModel = PurchaseProductsItem::model()->findByPk($id);
			if (is_numeric($num)) {
				$ppiModel->num = $num;
				$ppiModel->save();
			}
		}
	}

	/**
	 * Returns the data model based on the primary key given in the GET variable.
	 * If the data model is not found, an HTTP exception will be raised.
	 * @param integer $id the ID of the model to be loaded
	 * @return PurchaseStandard the loaded model
	 * @throws CHttpException
	 */
	public function loadModel($id)
	{
		$model=PurchaseStandard::model()->findByPk($id);
		if($model===null)
			$model = new PurchaseStandard;
		return $model;
	}

	/**
	 * Performs the AJAX validation.
	 * @param PurchaseStandard $model the model to be validated
	 */
	protected function performAjaxValidation($model)
	{
		if(isset($_POST['ajax']) && $_POST['ajax']==='purchase-standard-form')
		{
			echo CActiveForm::validate($model);
			Yii::app()->end();
		}
	}

	public function showBtn($data)
	{

		// echo '<button class="btn btn-success btn-xs" type="button"  onclick="standardList('.$data->id.')" title="标配列表"><span class="glyphicon glyphicon-th-list"></span></button>  ';

		echo '<a class="btn btn-success btn-xs" target="_blank" href="'.$this->createUrl('standardList',array('id'=>$data->id)).'" title="标配列表"><span class="glyphicon glyphicon-th-list"></span></a>  ';

		echo '<button class="btn btn-info btn-xs" type="button" onclick="update('.$data->id.')" title="更新"><span class="glyphicon glyphicon-pencil"></span></button>  ';
		
		echo '<a class="btn btn-danger btn-xs J_ajax_del" href="'.$this->createUrl('delete',array('id'=>$data->id)).'" title="删除"><span class="glyphicon glyphicon-trash"></span></a>  ';
		
		echo '<button class="btn btn-primary btn-xs" onclick="copyStandard('."this,".$data->id.')" title="复制"> <span class="glyphicon glyphicon-share-alt"></span></button>';

	}

	public function showStandardBtn($data)
	{
		// echo '<button class="btn btn-danger btn-xs" onclick="deleteItem('."this,".$data->id.')" title="删除"><span class="glyphicon glyphicon-remove"></span></button>';

		echo '<a class="btn btn-danger btn-xs J_ajax_del" href="'.$this->createUrl('deleteItem',array('id'=>$data->id)).'" title="删除"><span class="glyphicon glyphicon-trash"></span></a>  ';
	}
	public function actionGetProducts()
	{
		$sep = '%';
		$data = array();
		$id = Yii::app()->request->getParam('id', '');
		$criteria = new CDbCriteria;
		$criteria->compare('t.sid', $id);
		$criteria->order = 'product.commodity_code DESC';
		$criteria->with = 'product';
		$products = PurchaseProductsItem::model()->findAll($criteria);
		if ($products) {
			foreach ($products as $product) {
				$data[] = $product->product->cn_name .$sep. $product->product->id .$sep. $product->product->price .$sep. $product->product->cid .$sep. $product->num;
			}
		}
		echo json_encode($data);
	}
}
