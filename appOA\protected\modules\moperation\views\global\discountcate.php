<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','HQ Operations'), array('/moperation'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Basic Configurations'), array('/moperation'))?></li>
        <li class="active"><?php echo Yii::t('site','Discount Management') ?></li>
    </ol>

    <div class="row">
        <div class="col-md-1 col-sm-2">
            <?php
            $mainMenu = array(
                array('label'=>Yii::t('user','校园折扣'), 'url'=>array("/moperation/global/discount")),
                array('label'=>Yii::t('user','折扣类别'), 'url'=>array("/moperation/global/discountCate")),
            );

            $this->widget('zii.widgets.CMenu',array(
                'items'=> $mainMenu,
                'id'=>'pageCategory',
                'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked text-right background-gray'),
                'activeCssClass'=>'active',
                'itemCssClass'=>''
            ));
            ?>
        </div>
        <div class="col-md-11 col-sm-10">
            <div class="table-responsive">
                <table width="100%" class="table table-hover">
                    <thead>
                    <tr class="background-gray">
                        <td colspan="3">
                            <a href="<?php echo $this->createUrl('addDiscountCate');?>" class="btn btn-primary J_dialog" title="添加折扣类别">
                                <span class="glyphicon glyphicon-plus"></span>
                                添加折扣类别
                            </a>
                        </td>
                    </tr>
                    <tr>
                        <th>标题</th>
                        <th class="text-center" width="100">操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php foreach ($items as $item): ?>
                        <tr>
                            <td>
                                <?php echo CommonUtils::autoLang($item->title_cn, $item->title_en);?>
                            </td>
                            <td class="text-center">
                                <?php echo CHtml::link('<i class="glyphicon glyphicon-pencil"></i>',
                                    array('addDiscountCate', 'id'=>$item->id),
                                    array('class'=>'J_dialog btn btn-info btn-xs', 'title'=>Yii::t('global', 'Edit')));?>
                                <?php echo CHtml::link('<i class="glyphicon glyphicon-remove"></i>',
                                    array('delDiscountCate', 'id'=>$item->id),
                                    array('class'=>'J_ajax_del btn btn-danger btn-xs', 'title'=>Yii::t('global', 'Delete')));?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>