<?php

/**
 * This is the model class for table "ivy_alipay_request_parameter".
 *
 * The followings are the available columns in table 'ivy_alipay_request_parameter':
 * @property integer $id
 * @property string $service
 * @property string $partner
 * @property string $_input_charset
 * @property string $sign_type
 * @property string $sign
 * @property string $notify_url
 * @property string $return_url
 * @property string $error_notify_url
 * @property string $out_trade_no
 * @property string $subject
 * @property string $payment_type
 * @property string $defaultbank
 * @property string $seller_email
 * @property string $buyer_email
 * @property string $seller_id
 * @property string $buyer_id
 * @property string $seller_account_name
 * @property string $buyer_account_name
 * @property double $price
 * @property double $total_fee
 * @property integer $quantity
 * @property string $body
 * @property string $show_url
 * @property string $paymethod
 * @property string $need_ctu_check
 * @property string $royalty_type
 * @property string $royalty_parameters
 * @property string $anti_phishing_key
 * @property string $exter_invoke_ip
 * @property string $extra_common_param
 * @property string $extend_param
 * @property string $it_b_pay
 * @property string $default_login
 * @property string $product_type
 * @property integer $update_timestamp
 */
class RequestParameter extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return RequestParameter the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_alipay_request_parameter';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('service, partner, _input_charset, sign_type, sign, out_trade_no, subject, payment_type, defaultbank', 'required'),
			array('quantity, update_timestamp', 'numerical', 'integerOnly'=>true),
			array('price, total_fee', 'numerical'),
			array('service, partner, _input_charset, sign_type, payment_type, defaultbank, seller_id, buyer_id, paymethod, need_ctu_check, royalty_type, anti_phishing_key, exter_invoke_ip, it_b_pay, default_login', 'length', 'max'=>25),
			array('sign, out_trade_no, seller_email, buyer_email, seller_account_name, buyer_account_name', 'length', 'max'=>125),
			array('notify_url, return_url, error_notify_url, subject, show_url, extra_common_param, extend_param, product_type', 'length', 'max'=>255),
			array('royalty_parameters', 'length', 'max'=>500),
			array('body', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, service, partner, _input_charset, sign_type, sign, notify_url, return_url, error_notify_url, out_trade_no, subject, payment_type, defaultbank, seller_email, buyer_email, seller_id, buyer_id, seller_account_name, buyer_account_name, price, total_fee, quantity, body, show_url, paymethod, need_ctu_check, royalty_type, royalty_parameters, anti_phishing_key, exter_invoke_ip, extra_common_param, extend_param, it_b_pay, default_login, product_type, update_timestamp', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'service' => 'Service',
			'partner' => 'Partner',
			'_input_charset' => 'Input Charset',
			'sign_type' => 'Sign Type',
			'sign' => 'Sign',
			'notify_url' => 'Notify Url',
			'return_url' => 'Return Url',
			'error_notify_url' => 'Error Notify Url',
			'out_trade_no' => 'Out Trade No',
			'subject' => 'Subject',
			'payment_type' => 'Payment Type',
			'defaultbank' => 'Defaultbank',
			'seller_email' => 'Seller Email',
			'buyer_email' => 'Buyer Email',
			'seller_id' => 'Seller',
			'buyer_id' => 'Buyer',
			'seller_account_name' => 'Seller Account Name',
			'buyer_account_name' => 'Buyer Account Name',
			'price' => 'Price',
			'total_fee' => 'Total Fee',
			'quantity' => 'Quantity',
			'body' => 'Body',
			'show_url' => 'Show Url',
			'paymethod' => 'Paymethod',
			'need_ctu_check' => 'Need Ctu Check',
			'royalty_type' => 'Royalty Type',
			'royalty_parameters' => 'Royalty Parameters',
			'anti_phishing_key' => 'Anti Phishing Key',
			'exter_invoke_ip' => 'Exter Invoke Ip',
			'extra_common_param' => 'Extra Common Param',
			'extend_param' => 'Extend Param',
			'it_b_pay' => 'It B Pay',
			'default_login' => 'Default Login',
			'product_type' => 'Product Type',
			'update_timestamp' => 'Update Timestamp',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('service',$this->service,true);
		$criteria->compare('partner',$this->partner,true);
		$criteria->compare('_input_charset',$this->_input_charset,true);
		$criteria->compare('sign_type',$this->sign_type,true);
		$criteria->compare('sign',$this->sign,true);
		$criteria->compare('notify_url',$this->notify_url,true);
		$criteria->compare('return_url',$this->return_url,true);
		$criteria->compare('error_notify_url',$this->error_notify_url,true);
		$criteria->compare('out_trade_no',$this->out_trade_no,true);
		$criteria->compare('subject',$this->subject,true);
		$criteria->compare('payment_type',$this->payment_type,true);
		$criteria->compare('defaultbank',$this->defaultbank,true);
		$criteria->compare('seller_email',$this->seller_email,true);
		$criteria->compare('buyer_email',$this->buyer_email,true);
		$criteria->compare('seller_id',$this->seller_id,true);
		$criteria->compare('buyer_id',$this->buyer_id,true);
		$criteria->compare('seller_account_name',$this->seller_account_name,true);
		$criteria->compare('buyer_account_name',$this->buyer_account_name,true);
		$criteria->compare('price',$this->price);
		$criteria->compare('total_fee',$this->total_fee);
		$criteria->compare('quantity',$this->quantity);
		$criteria->compare('body',$this->body,true);
		$criteria->compare('show_url',$this->show_url,true);
		$criteria->compare('paymethod',$this->paymethod,true);
		$criteria->compare('need_ctu_check',$this->need_ctu_check,true);
		$criteria->compare('royalty_type',$this->royalty_type,true);
		$criteria->compare('royalty_parameters',$this->royalty_parameters,true);
		$criteria->compare('anti_phishing_key',$this->anti_phishing_key,true);
		$criteria->compare('exter_invoke_ip',$this->exter_invoke_ip,true);
		$criteria->compare('extra_common_param',$this->extra_common_param,true);
		$criteria->compare('extend_param',$this->extend_param,true);
		$criteria->compare('it_b_pay',$this->it_b_pay,true);
		$criteria->compare('default_login',$this->default_login,true);
		$criteria->compare('product_type',$this->product_type,true);
		$criteria->compare('update_timestamp',$this->update_timestamp);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}