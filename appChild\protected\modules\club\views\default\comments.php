<?php
foreach($comments as $comment):
    ?>
    <div class="comment" id="comment-<?php echo $comment['id']?>">
        <div class="pull-left">
            <img class="img-circle" src="<?php echo Yii::app()->params['uploadBaseUrl'].'users/thumbs/'.$users[$comment['userid']]->user->user_avatar;?>" style="width:48px;">
        </div>
        <div class="comment-wrapper">
            <div class="comment-meta">
                <p>
                    <?php if( $comment['parent_id'] ):?>
                        <span></span><?php echo CHtml::encode($users[$comment['userid']]->uname);?> 回复 <span><?php echo CHtml::encode($users[$comment['mention_uid']]->uname);?></span> · <?php echo CommonUtils::time_elapsed_string($comment['created'])?>
                    <?php else:?>
                        <span><?php echo CHtml::encode($users[$comment['userid']]->uname);?></span> · <?php echo CommonUtils::time_elapsed_string($comment['created'])?>
                    <?php endif;?>
                    <?php if(!Yii::app()->user->isGuest):?>
                    <a href="javascript:;" onclick="reply(<?php echo $comment['id']?>)">回复</a>
                    <?php endif;?>
                </p>
            </div>
            <div class="comment-main">
                <p><?php echo CHtml::encode($comment['content']);?></p>
            </div>
        </div>
    </div>
<?php
endforeach;
$ceil = ceil($count/$limit);
if ($ceil>1):
?>
<div id="comment-pages">
    <ul class="pagination">
        <?php for($i=1; $i<=$ceil; $i++):?>
        <li class="<?php echo $page == $i ? 'active' : '' ?>"><a href="javascript:;" onclick="loadComment(<?php echo $i;?>)"><?php echo $i;?></a></li>
        <?php endfor;?>
    </ul>
</div>
<?php endif;?>