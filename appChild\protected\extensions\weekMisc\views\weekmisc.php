

<?php
$is_iPad = (bool) strpos($_SERVER['HTTP_USER_AGENT'],'iPad');
if (!$is_iPad){
    $colorbox = $this->widget('application.extensions.colorbox.JColorBox');
    $colorbox->addInstance('.colorbox_s', array('iframe'=>true, 'width'=>'1030', 'height'=>'80%'));
    $colorbox->addInstance('.colorbox_l', array('iframe'=>true, 'width'=>'910', 'height'=>'60%'));
}
?>
<div class="item span-6 week-item">
    <div class="title">
        <h5><?php echo Yii::t("welcome", "This");?></h5>
        <h6>
		<?php $mon = mktime (0,0,0,date("m") , HtoolKits::getMonday(date("w")),date("Y"));//echo date('n/d', $mon)."-". date('n/d', $mon+3600*24*4)?>
		<?php echo Mims::formatDateTime($mon)?> <br /><em class="v-line-12"></em><br /> <?php echo Mims::formatDateTime($mon+(3600*24*5)-1)?>
		</h6>
    </div>
    <div class="desc">
        <h5 class="hover ml-title"><?php echo Yii::t("welcome", 'Week')?></h5>
        <div class="image-list">
                <ul>
                    <li class="week-misc">
                        <div class="menu">
                            <a href="<?php echo Yii::app()->getController()->createUrl("//portfolio/portfolio/lunch", array('schoolid'=>$child['schoolid'],'yid'=>$child['yid']));?>" class="colorbox_l" <?php if ($is_iPad):?>target="_blank"<?php endif;?>></a>
                        </div>
                        <div class="calendar">
                            <a href="<?php echo Yii::app()->getController()->createUrl("//portfolio/portfolio/schedule", array('classid'=>$child['classid']));?>" class="colorbox_s" <?php if ($is_iPad):?>target="_blank"<?php endif;?>></a>
                        </div>
                    </li>
                </ul>
        </div>
    </div>
</div>