<?php
/*
 * <PERSON><PERSON>
 * 2012-7-10
 *
 */
?>
<?php if ($show === 1):?>
<?php if (!empty($sd_info) || !empty($fb_info)): ?>
    <div id="surveybox" class="votebox">
        <h4 class="survey"><span><?php echo Yii::t("survey", "Parent Survey"); ?></span></h4>
        <div class="voteinfo">
            <?php if (!empty($sd_info)): ?>
                <?php foreach ($sd_info as $val):?>
                <div class="poll pd10 prebg">
                    <h5 class="vtitle">
                        <a
                            href="<?php echo $this->controller->createUrl('survey/index', array('id' => $val->survey_id, "t" => time())); ?>"
                            target="_blank"> <?php echo HtoolKits::getContentByLang($val->survey->title_cn,$val->survey->title_en); ?>
                        </a>
                    </h5>
                    <div class="pollother">
                        <div class="polldesc" style="padding: 10px;">
                            <?php if ($val->survey->survey_type == Survey::SURVEY_TYPE_ONE): ?>
                                <?php
                                if($val->survey->id == 14){
                                    echo Yii::t("survey", 'Your response will also help us better understand how to prepare your children for elementary school.');
                                }
                                else{
                                    echo Yii::t("survey", 'Please help us to improve our program by taking the Parent Survey. Thanks!');
                                }
                                ?>
                            <?php else:?>
                                <?php
                                    $schoolid = $this->getController()->getSchoolId();
                                    $this->controller->renderPartial('application.modules.child.views.survey.' . Yii::t("survey", '_en_us_return_message'), array('schoolid'=>$schoolid, 'surveyid'=>$val->survey->id)); 
                                ?>
                            <?php endif;?>
                        </div>
                        <span class="fl txt"><em><?php echo Yii::t("survey", 'End Date'); ?></em><span>
                                <?php
                                echo Mims::formatDateTime($val->end_time);
                                ?>
                            </span>
                        </span> 
                    </div>
                    <div class="pollact">
                        <span class="fr btn001">
                            <a href="<?php echo $this->controller->createUrl('survey/index', array('id' => $val->survey_id, "t" => time())); ?>" target="_blank"><span><?php echo Yii::t("survey", 'Take Survey'); ?></span>
                            </a>
                        </span>
                    </div>		

                    <div class="clear"></div>
                </div>
                <?php endforeach; ?>
            <?php endif; ?>
            <?php if (!empty($fb_info)): ?>
                <?php foreach ($fb_info as $val):?>
                    <div class="poll pd10 prebg">
                        <?php 
                        if ($val->survey->survey_type == Survey::SURVEY_TYPE_ONE && ($val->end_time-$yesterday_timestamp)/86400>10): 
                         
                            ?>
                        <h5 class="vtitle">
                            <a href="javascript:">
                                <?php echo HtoolKits::getContentByLang($val->survey->title_cn,$val->survey->title_en); ?>
                            </a>
                        </h5>	
                        
                        <p>
                            <?php echo Yii::t("survey", 'We have successfully received your survey submission, thank you! As it will take our staff some time to read through every survey carefully and to compile the results, please be patient in that we may not be able to respond to you immediately or individually. '); ?>
                        </p>

                        <p>
                            <?php echo Yii::t("survey", 'If you have further comments, you can always email our team at Ivy headquarters: <a href="mailto:<EMAIL>"><EMAIL></a>.'); ?>
                        </p>

                        <p>
                            <?php echo Yii::t("survey", 'Ivy Education Group'); ?>
                        </p>
                        <?php elseif ($val->survey->survey_type == Survey::SURVEY_TYPE_TWO):?>
                            <h5 class="vtitle">
                               <a href="javascript:">
                                   <?php echo HtoolKits::getContentByLang($val->survey->title_cn,$val->survey->title_en); ?>
                               </a>
                           </h5>
                            <?php
                             $param = '';
                             $_id = $val->survey->id;

                             $addParams = CommonUtils::LoadConfig('CfgSurveyParams');
                             if(isset($addParams[$_id])){
                                $param = isset($addParams[$_id][$schoolid]) ? Mims::formatDateTime
                                    (strtotime($addParams[$_id][$schoolid]), 'long') :
                             '';

                             }
                             $url = 'http://mega.ivymik.cn/Ways_of_payment_'.Yii::app()->language.'.jpg';
                             $payMentUrl = $this->controller->createUrl('/child/payment/summary');
                             if (in_array($schoolid, array('BJ_DS', 'BJ_SLT'))) {
                                 echo Yii::t('survey', '<p>Thank you for submitting your re-enrolment confirmation. The next step for you to complete the re-enrollment process is to pay the placement deposit by January 31, 2019  through online "<a href=":url">Payment-Tuition Deposit</a>". If we don\'t receive your payment by January 31, 2019, your child will be put into waitlist.<p>', array(':url' => $payMentUrl));
                                 echo '<br>';
                                 echo Yii::t('survey', '<p>If you have any questions regarding re-enrollment please feel free to contact admissions:</p><p>Beigao Campus: <br>5603-9446 <br> <a href="mailto:<EMAIL>"><EMAIL></a></p><p>Sanlitun Campus: <br>8532-2500 <br> <a href="mailto:<EMAIL>"><EMAIL></a></p>');
                             } else {
                                 echo Yii::t('survey', '<p>Thank you for submitting your re-enrollment confirmation. The next step for you to complete the re-enrollment process is to pay the placement deposit by Tuesday, May 21st  through online "<a href=":url">Payment-Tuition Deposit</a>". If we don\'t receive your payment by Tuesday, May 21st, your child will be put into waitlist.<p>', array(':url' => $payMentUrl));

                                 echo '<br>';
                             }
                             ?>

                            <?php  ?>
                        <?php endif;?>
                    </div>
                 <?php endforeach; ?>
            <?php endif; ?>
        </div>	
    </div>
<?php endif; ?>
<?php endif; ?>