(function () {

    var utils = UM.utils,
        browser = UM.browser,
        Base = {
        checkURL: function (url) {
            if(!url)    return false;
            url = utils.trim(url);
            if (url.length <= 0) {
                return false;
            }
            if (url.search(/http:\/\/|https:\/\//) !== 0) {
                url += 'http://';
            }

            url=url.replace(/\?[\s\S]*$/,"");

            if (!/(.gif|.jpg|.jpeg|.png)$/i.test(url)) {
                return false;
            }
            return url;
        },
        getAllPic: function (sel, $w, editor) {
            var me = this,
                arr = [],
                $imgs = $(sel, $w);

            $.each($imgs, function (index, node) {
                $(node).removeAttr("width").removeAttr("height");

                if (node.width > editor.options.initialFrameWidth) {
                    me.scale(node, editor.options.initialFrameWidth -
                        parseInt($(editor.body).css("padding-left"))  -
                        parseInt($(editor.body).css("padding-right")));
                }

                return arr.push({
                    width: node.width,
                    height: node.height,
                    _src: node.src,
                    src: node.src
                });
            });

            return arr;
        },
        scale: function (img, max, oWidth, oHeight) {
            var width = 0, height = 0, percent, ow = img.width || oWidth, oh = img.height || oHeight;
            if (ow > max || oh > max) {
                if (ow >= oh) {
                    if (width = ow - max) {
                        percent = (width / ow).toFixed(2);
                        img.height = oh - oh * percent;
                        img.width = max;
                    }
                } else {
                    if (height = oh - max) {
                        percent = (height / oh).toFixed(2);
                        img.width = ow - ow * percent;
                        img.height = max;
                    }
                }
            }

            return this;
        },
        close: function ($img) {

            $img.css({
                top: ($img.parent().height() - $img.height()) / 2,
                left: ($img.parent().width()-$img.width())/2
            }).prev().on("click",function () {

                if ( $(this).parent().remove().hasClass("edui-image-upload-item") ) {
                    //显示图片计数-1
                    Upload.showCount--;
                    Upload.updateView();
                }

            });

            return this;
        },
        createImgBase64: function (img, file, $w) {
            if (browser.webkit) {
                //Chrome8+
                img.src = window.webkitURL.createObjectURL(file);
            } else if (browser.gecko) {
                //FF4+
                img.src = window.URL.createObjectURL(file);
            } else {
                //实例化file reader对象
                var reader = new FileReader();
                reader.onload = function (e) {
                    img.src = this.result;
                    $w.append(img);
                };
                reader.readAsDataURL(file);
            }
        },
        callback: function (editor, $w, url, state) {

            if (state == "SUCCESS") {
                //显示图片计数+1
                Upload.showCount++;
                var $img = $("<img src='" + editor.options.imagePath + url + "' class='edui-image-pic' />"),
                    $item = $("<div class='edui-image-item edui-image-upload-item'><div class='edui-image-close'></div></div>").append($img);

                if ($("#edui-image-Jupload2", $w).length < 1) {
                    $("#edui-image-Jcontent", $w).append($item);

                    Upload.render("#edui-image-Jcontent", 2)
                        .config("#edui-image-Jupload2");
                } else {
                    $("#edui-image-Jupload2", $w).before($item).show();
                }

                $img.on("load", function () {
                    Base.scale(this, 120);
                    Base.close($(this));
                });

            } else {
                currentDialog.showTip( state );
                window.setTimeout( function () {

                    currentDialog.hideTip();

                }, 3000 );
            }

            Upload.toggleMask();

        }
    };

    /*
     * 本地上传
     * */
    var Upload = {
        showCount: 0,
        uploadTpl: '<div class="edui-image-upload%%" id="edui-image-Jupload%%">' +
            '<span class="edui-image-icon"></span>' +
            '<form class="edui-image-form" method="post" enctype="multipart/form-data" target="up">' +
            '<input style=\"filter: alpha(opacity=0);\" class="edui-image-file" type="file" hidefocus name="upfile" accept="image/gif,image/jpeg,image/png,image/jpg,image/bmp"/>' +
            '</form>' +
            '<iframe name="up" style="display: none"></iframe>' +
            '</div>',
        init: function (editor, $w) {
            var me = this;

            me.editor = editor;
            me.dialog = $w;

            me.render("#edui-image-Jlocal", 1);
            me.config("#edui-image-Jupload1");
            me.submit();
            me.drag();

            $("#edui-image-Jupload1").hover(function () {
                $(".edui-image-icon", this).toggleClass("hover");
            });

            if (!(UM.browser.ie && UM.browser.version <= 9)) {
                $("#edui-image-JdragTip", me.dialog).css("display", "block");
            }


            return me;
        },
        render: function (sel, t) {
            var me = this;

            $(sel, me.dialog).append($(me.uploadTpl.replace(/%%/g, t)));

            return me;
        },
        config: function (sel) {
            var me = this,
                url=me.editor.options.imageUrl;

            url=url + (url.indexOf("?") == -1 ? "?" : "&") + "editorid="+me.editor.id;//初始form提交地址;

            $("form", $(sel, me.dialog)).attr("action", url);

            return me;
        },
        submit: function (callback) {

            var me = this,
                input = $( '<input style="filter: alpha(opacity=0);" class="edui-image-file" type="file" hidefocus="" name="upfile" accept="image/gif,image/jpeg,image/png,image/jpg,image/bmp">'),
                input = input[0];

            $(me.dialog).delegate( ".edui-image-file", "change", function ( e ) {

                if ( !this.parentNode ) {
                    return;
                }

                $(this).parent()[0].submit();
                Upload.updateInput( input );
                me.toggleMask("Loading....");
                callback && callback();

            });

            return me;
        },
        //更新input
        updateInput: function ( inputField ) {

            $( ".edui-image-file", this.dialog ).each( function ( index, ele ) {

                ele.parentNode.replaceChild( inputField.cloneNode( true ), ele );

            } );

        },
        //更新上传框
        updateView: function () {

            if ( Upload.showCount !== 0 ) {
                return;
            }

            $(".edui-image-upload2", this.dialog).hide();
            $(".edui-image-dragTip", this.dialog).show();
            $(".edui-image-upload1", this.dialog).show();

        },
        drag: function () {
            var me = this;
            //做拽上传的支持
            if (!UM.browser.ie9below) {
                me.dialog.find('#edui-image-Jcontent').on('drop',function (e) {

                    //获取文件列表
                    var fileList = e.originalEvent.dataTransfer.files;
                    var img = document.createElement('img');
                    var hasImg = false;
                    $.each(fileList, function (i, f) {
                        if (/^image/.test(f.type)) {
                            //创建图片的base64
                            Base.createImgBase64(img, f, me.dialog);

                            var xhr = new XMLHttpRequest();
                            var upUrl = me.editor.getOpt('imageUrl').indexOf("?") == -1 ? me.editor.getOpt('imageUrl') + "?type=ajax" : me.editor.getOpt('imageUrl') + "&type=ajax";
                            xhr.open("post", upUrl, true);
                            xhr.setRequestHeader("X-Requested-With", "XMLHttpRequest");

                            //模拟数据
                            var fd = new FormData();
                            fd.append(me.editor.getOpt('imageFieldName'), f);

                            xhr.send(fd);
                            xhr.addEventListener('load', function (e) {
                                Base.callback(me.editor, me.dialog, e.target.response, "SUCCESS");
                                if (i == fileList.length - 1) {
                                    $(img).remove()
                                }
                            });
                            hasImg = true;
                        }
                    });
                    if (hasImg) {
                        e.preventDefault();
                        me.toggleMask("Loading....");
                    }

                }).on('dragover', function (e) {
                        e.preventDefault();
                    });
            }
        },
        toggleMask: function (html) {
            var me = this;

            var $mask = $("#edui-image-Jmask", me.dialog);
            if (html) {
                if (!(UM.browser.ie && UM.browser.version <= 9)) {
                    $("#edui-image-JdragTip", me.dialog).css( "display", "none" );
                }
                $("#edui-image-Jupload1", me.dialog).css( "display", "none" );
                $mask.addClass("active").html(html);
            } else {

                $mask.removeClass("active").html();

                if ( Upload.showCount > 0 ) {
                    return me;
                }

                if (!(UM.browser.ie && UM.browser.version <= 9) ){
                    $("#edui-image-JdragTip", me.dialog).css("display", "block");
                }
                $("#edui-image-Jupload1", me.dialog).css( "display", "block" );
            }

            return me;
        }
    };

    /*
     * 网络图片
     * */
    var NetWork = {
        init: function (editor, $w) {
            var me = this;

            me.editor = editor;
            me.dialog = $w;

            me.initEvt();
        },
        initEvt: function () {
            var me = this,
                url,
                $ele = $("#edui-image-JsearchTxt", me.dialog);

            $("#edui-image-JsearchAdd", me.dialog).on("click", function () {
                url = Base.checkURL($ele.val());

                if (url) {

                    $("<img src='" + url + "' class='edui-image-pic' />").on("load", function () {



                        var $item = $("<div class='edui-image-item'><div class='edui-image-close'></div></div>").append(this);

                        $("#edui-image-JsearchRes", me.dialog).append($item);

                        Base.scale(this, 120);

                        $item.width($(this).width());

                        Base.close($(this));

                        $ele.val("");
                    });
                }
            })
                .hover(function () {
                    $(this).toggleClass("hover");
                });
        }
    };

    /*
     * 图片管理
     * */
    var ImgMgt = {
        init: function (editor, $w) {
            var me = this;

            me.editor = editor;
            me.dialog = $w;

            me.render();

        },
        render: function () {
            var me = this,
                gorun = true;

            $('#JimgMgt', me.dialog).on('click', function(){
                if(gorun){
                    gorun = false;
                    me.toggleMask("Loading....");
                    $.getJSON(me.editor.getOpt('imageMgt'), {action: 'get'}, function(data){
                        me.toggleMask();
                        $.each(data,function(index, value){
                            $("<img src='" + value.url + "' class='edui-image-pic Jimgmgt' id='imgmgt-"+value.id+"' />").on("load", function () {
                                var iscover = value.cover ? 'iscover' : '';
                                var $item = $("<div class='edui-image-item'><div class='edui-image-size'>"+value.size+"</div><div class='edui-image-cover "+iscover+"' title='设为封面'></div><div class='edui-image-close' title='删除'></div></div>").append(this);

                                $("#edui-image-JMgtRes", me.dialog).append($item);

                                Base.scale(this, 120);

                                $item.width($(this).width());

                                me.delImg($(this));

                                me.cover($(this));

                                me.selected('imgmgt-'+value.id);

                                me.showTitle(this, value.title);
                            });
                        });
                    });
                }
            });
        },
        selected: function(imgid) {
            var me = this;
            $('#'+imgid, me.dialog).on('click', function(){
                if ( $(this).hasClass('active') ){
                    $(this).removeClass('active');
                    $(this).parents('.edui-image-item').css({'opacity': 1, 'border': '2px solid #ccc'});
                }
                else{
                    $(this).addClass('active');
                    $(this).parents('.edui-image-item').css({'opacity': 0.5, 'border': '2px solid blue'});
                }
            });
        },
        delImg: function($img){
            var me = this;
            $img.css({
                top: ($img.parent().height() - $img.height()) / 2,
                left: ($img.parent().width()-$img.width())/2
            }).prev().on("click",function () {
                if (confirm('确定删除？')){
                    var imgobj=this;
                    var id = $img.attr('id').split('-')[1];
                    $.getJSON(me.editor.getOpt('imageMgt'), {action: 'del', _id: id}, function(data){
                        if(data.state == 'SUCCESS')
                            $(imgobj).parent().remove();
                    });
                }
            });
        },
        cover: function($img){
            var me = this;
            $img.prevAll('.edui-image-cover').on("click",function () {
                if (confirm('确定设为封面？')){
                    var imgobj=this;
                    var id = $img.attr('id').split('-')[1];
                    $.getJSON(me.editor.getOpt('imageMgt'), {action: 'cover', _id: id}, function(data){
                        if(data.state == 'SUCCESS'){
                            $('#edui-image-JMgtRes .edui-image-item .edui-image-cover').removeClass('iscover');
                            $(imgobj).addClass('iscover');
                        }
                    });
                }
            });
        },
        showTitle: function(_this, t){
            var me = this;
            $(_this).css('position', 'static');
            if(!t)
                t = '双击添加标题';
            $(_this).parent().append('<div class="tac J_title">'+t+'</div>');
            $('.J_title', $(_this).parent()).on('dblclick', function(){
                $(this).html('<input type="text" value="'+t+'" class="input length_2" />');
                $('input', $(_this).parent()).on('blur', function(){
                    var id = $(_this).attr('id').split('-')[1];
                    if(t != $(this).val()){
                        $.post(me.editor.getOpt('imageMgt'), {action: 'update', _id: id, val: $(this).val()}, function(data){
                            t = data;
                            $('.J_title', $(_this).parent()).html('<div class="tac J_title">'+t+'</div>');
                        });
                    }
                    else{
                        $('.J_title', $(_this).parent()).html('<div class="tac J_title">'+t+'</div>');
                    }
                }).on('keydown', function(e){
                    if(e.keyCode==13){
                        $(this).blur();
                    }
                });
            });
        },
        toggleMask: function (html) {
            var me = this;

            var $mask = $("#edui-image-Jmask1", me.dialog);
            if (html) {
                $mask.addClass("active").html(html);
            }
            else{
                $mask.removeClass('active').html();
            }

            return me;
        }
    };

    var $tab = null,
        currentDialog = null;

    UM.registerWidget('image', {
        tpl: "<link rel=\"stylesheet\" type=\"text/css\" href=\"<%=image_url%>image.css\">" +
            "<div id=\"edui-image-Jwrapper\" class=\"edui-image-wrapper\">" +
            "<ul class=\"edui-tab-nav\">" +
            "<li class=\"edui-tab-item active\"><a href=\"#edui-image-Jlocal\" class=\"edui-tab-text\"><%=lang_tab_local%></a></li>" +
            "<li  class=\"edui-tab-item\"><a href=\"#edui-image-JimgSearch\" class=\"edui-tab-text\"><%=lang_tab_imgSearch%></a></li>" +
            "</ul>" +
            "<div class=\"edui-tab-content\">" +
            "<div id=\"edui-image-Jlocal\" class=\"edui-tab-pane active\">" +
            "<div class=\"edui-image-content\" id=\"edui-image-Jcontent\"></div>" +
            "<div class=\"edui-image-mask\" id=\"edui-image-Jmask\"></div>" +
            "<div id=\"edui-image-JdragTip\" class=\"edui-image-dragTip\"><%=lang_input_dragTip%></div>" +
            "</div>" +
            "<div id=\"edui-image-JimgSearch\" class=\"edui-tab-pane\">" +
            "<div class=\"edui-image-searchBar\">" +
            "<table><tr><td><input class=\"edui-image-searchTxt\" id=\"edui-image-JsearchTxt\" type=\"text\"></td>" +
            "<td><div class=\"edui-image-searchAdd\" id=\"edui-image-JsearchAdd\"><%=lang_btn_add%></div></td></tr></table>" +
            "</div>" +
            "<div class=\"edui-image-searchRes\" id=\"edui-image-JsearchRes\"></div>" +
            "</div>" +
            "</div>" +
            "</div>",
        customtpl: "<link rel=\"stylesheet\" type=\"text/css\" href=\"<%=image_url%>image.css\">" +
            "<div id=\"edui-image-Jwrapper\" class=\"edui-image-wrapper\">" +
            "<ul class=\"edui-tab-nav\">" +
            "<li class=\"edui-tab-item active\"><a href=\"#edui-image-Jlocal\" class=\"edui-tab-text\"><%=lang_tab_local%></a></li>" +
            "<li class=\"edui-tab-item\"><a href=\"#edui-image-JimgSearch\" class=\"edui-tab-text\"><%=lang_tab_imgSearch%></a></li>" +
            "<li id='JimgMgt' class=\"edui-tab-item\"><a href=\"#edui-image-JimgMgt\" class=\"edui-tab-text\"><%=lang_tab_imgMgt%></a></li>" +
            "</ul>" +
            "<div class=\"edui-tab-content\">" +
            "<div id=\"edui-image-Jlocal\" class=\"edui-tab-pane active\">" +
            "<div class=\"edui-image-content\" id=\"edui-image-Jcontent\"></div>" +
            "<div class=\"edui-image-mask\" id=\"edui-image-Jmask\"></div>" +
            "<div id=\"edui-image-JdragTip\" class=\"edui-image-dragTip\"><%=lang_input_dragTip%></div>" +
            "</div>" +
            "<div id=\"edui-image-JimgSearch\" class=\"edui-tab-pane\">" +
            "<div class=\"edui-image-searchBar\">" +
            "<table><tr><td><input class=\"edui-image-searchTxt\" id=\"edui-image-JsearchTxt\" type=\"text\"></td>" +
            "<td><div class=\"edui-image-searchAdd\" id=\"edui-image-JsearchAdd\"><%=lang_btn_add%></div></td></tr></table>" +
            "</div>" +
            "<div class=\"edui-image-searchRes\" id=\"edui-image-JsearchRes\"></div>" +
            "</div>" +
            "<div id=\"edui-image-JimgMgt\" class=\"edui-tab-pane\">" +
            "<div class=\"edui-image-searchBar\"></div>" +
            "<div class=\"edui-image-searchRes\" id=\"edui-image-JMgtRes\"></div>" +
            "<div class=\"edui-image-mask\" id=\"edui-image-Jmask1\"></div>" +
            "</div>" +
            "</div>",
        initContent: function (editor, $dialog) {
            var lang = editor.getLang('image')["static"],
                opt = $.extend({}, lang, {
                    image_url: UMEDITOR_CONFIG.UMEDITOR_HOME_URL + 'dialogs/image/'
                });

            Upload.showCount = 0;

            if (lang) {
                if( editor.getOpt('imageMgt') ){
                    var html = $.parseTmpl(this.customtpl, opt);
                }
                else{
                    var html = $.parseTmpl(this.tpl, opt);
                }
            }

            currentDialog = $dialog.edui();

            this.root().html(html);

        },
        initEvent: function (editor, $w) {
            $tab = $.eduitab({selector: "#edui-image-Jwrapper"})
                .edui().on("beforeshow", function (e) {
                    e.stopPropagation();
                });

            Upload.init(editor, $w);

            NetWork.init(editor, $w);

            if( editor.getOpt('imageMgt') )
                ImgMgt.init(editor, $w);
        },
        buttons: {
            'ok': {
                exec: function (editor, $w) {
                    var sel = "",
                        index = $tab.activate();

                    if (index == 0) {
                        sel = "#edui-image-Jcontent .edui-image-pic";
                    } else if (index == 1) {
                        sel = "#edui-image-JsearchRes .edui-image-pic";
                    } else if (index == 2) {
                        sel = "#edui-image-JMgtRes img.active";
                    }

                    var list = Base.getAllPic(sel, $w, editor);

                    if (index != -1) {
                        editor.execCommand('insertimage', list);
                    }
                }
            },
            'cancel': {}
        },
        width: 700,
        height: 408
    }, function (editor, $w, url, state) {
        Base.callback(editor, $w, url, state)
    })
})();

