<?php
Class ClassroomController extends BranchBasedController
{
    public $actionAccessAuths = array(
        'index'                     => 'o_A_Access',
        'SaveClassroom'             => 'o_A_Adm_Class',
        'DeteteClassroom'           => 'o_A_Adm_Class',
    );

    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

        Yii::import('common.models.classTeacher.EClassroom');
        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mcampus/classroom/index');
    }

    public function actionSelect(){
        $this->render('//layouts/common/branchSelect');
    }

    public function actionIndex(){
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
        $this->render('index');
    }

    /*
     * 保存教室
     */
    public function actionSaveClassroom(){
        $id = Yii::app()->request->getParam('id',0);
        $this->addMessage('state', 'fail');
        $this->addMessage('message', Yii::t('message','Failed!'));
        if (Yii::app()->request->isAjaxRequest && isset($_POST['EClassroom'])){
            $model = EClassroom::model()->findByPk($id);
            if (empty($model)){
                $model = new EClassroom();
                $model->setScenario('add');
            }else{
                if ($model->schoolid !== $this->branchId){
                    $this->showMessage();
                }
                if ($model->code == trim($_POST['EClassroom']['code'])){
                    $model->setScenario('update');
                }else{
                    $model->setScenario('add');
                }
            }
            $model->schoolid = $this->branchId;
            $_POST['EClassroom']['schoolid'] = $this->branchId;
            $model->attributes = $_POST['EClassroom'];
            $typeList = array('is_multifunc', 'is_hall', 'is_lib');
            $type = isset($_POST['EClassroom']['type']) ? $_POST['EClassroom']['type'] : array();
            foreach ($typeList as $val) {
                if (in_array($val, $type)) {
                    $model->$val = 1;
                } else {
                    $model->$val = 0;
                }
            }

            if ($model->validate()){
                if ($model->save()){
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message','success'));
                    $this->addMessage('data',$model->getAttributes());
                    $this->addMessage('callback', 'updateCallback');
                    $this->showMessage();
                }else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('classroom', '提交的信息有错误.'));
                    $this->showMessage();
                }
            }else{
                $errs = current($model->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('error', $model->getErrors());
                $this->addMessage('message',  $errs ? $errs[0] : '请填写必填项');
                $this->showMessage();
            }
        }
        $this->showMessage();
    }

    /*
     * 删除教室
     */
    public function actionDeteteClassroom(){
        $id = !empty($_POST['id']) ? $_POST['id'] : 0;
        $this->addMessage('state', 'fail');
        $this->addMessage('message', Yii::t('message','Failed!'));
        if (Yii::app()->request->isAjaxRequest && $id){
            $used = IvyClass::model()->countByAttributes(array('introduction'=>$id));
            //已分配到班级不能删除
            if($used){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '此教室已被当前或以前的班级占用，不能删除');
            }else{
                $model = EClassroom::model()->findByPk($id);
                //非本校园不能删除
                if ($model->schoolid === $this->branchId){
                    if ($model->delete()){
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', Yii::t('message','success'));
                        $this->addMessage('callback', 'deleteCallback');
                        $this->addMessage('data',$id);
                    }
                }
            }
        }
        $this->showMessage();
    }

}