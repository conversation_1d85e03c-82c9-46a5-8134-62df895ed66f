<?php

class CrmController extends ProtectedController
{
    public $branchArr;
    public $dialogWidth = 500;
    public $cfg = array();
    
    public function beforeAction($action){
		if($action->getId() != 'nopermit'){
			if(!Yii::app()->user->checkAccess('tVisitAdmin')){
				$this->forward('nopermit');
			}
		}
		return true;
	}
	
	public function actionNopermit(){
		echo 'No Permission';
	}
    
    public function init() {
        parent::init();
        Yii::import('common.models.visit.*');
        $this->cfg = CommonUtils::LoadConfig('CfgVisit');
        
        foreach ( Branch::model()->getBranchList(null,true, '') as $branch){
            $this->branchArr[$branch['id']] = $branch['title'];
        }
    }
    
	public function actionIndex($state=10)
	{
        foreach($this->cfg['status'] as $stat=>$status){
            $this->mainMenu[] = array(
                'label'=>$status,
                'url'=>array('//operations/crm/index', 'state'=>$stat),
            );
        }
        
        $criteria=new CDbCriteria;
        $criteria->with=array('record', 'newlog');
        
        $parent_name = Yii::app()->request->getParam('parent_name','');
        $child_name = Yii::app()->request->getParam('child_name','');
        $email = Yii::app()->request->getParam('email','');
        $tel = Yii::app()->request->getParam('tel','');
        $age = Yii::app()->request->getParam('age','');
        $branchId = Yii::app()->request->getParam('school','');
        $purpose = Yii::app()->request->getParam('purpose','');
        if ($parent_name)
            $criteria->compare('parent_name', $parent_name, true);
        if ($child_name)
            $criteria->compare('child_name', $child_name, true);
        if ($email)
            $criteria->compare('email', $email, true);
        if ($tel)
            $criteria->compare('tel', $tel, true);
        if ($age){
            $tm = strtotime('-'.$age.' year');
            if ($age == 8){
                $criteria->compare('birth_timestamp', '<='.$tm);
            }
            elseif($age == 9){
                $criteria->compare('birth_timestamp', 0);
            }
            else {
                $criteria->addBetweenCondition('birth_timestamp', $tm, $tm+31536000);
            }
        }
        if ($branchId)
            $criteria->compare('record.schoolid', $branchId);
        if ($purpose)
            $criteria->compare('purpose', $purpose);
        
        $criteria->compare('t.status', $state);
		$dataProvider=new CActiveDataProvider('IvyVisitsBasicInfo', array(
			'criteria'=>$criteria,
		));
        
        $this->render('index', array(
            'dataProvider'=>$dataProvider,
            'parent_name'=>$parent_name,
            'child_name'=>$child_name,
            'email'=>$email,
            'tel'=>$tel,
            'age'=>$age,
            'branchId'=>$branchId,
            'purpose'=>$purpose,
        ));
	}
    
    public function actionLog($id=0)
    {
        $this->layout='//layouts/dialog';
        $this->dialogWidth = 600;
        
        $model = IvyVisitsBasicInfo::model()->findByPk($id);
        $modelLog = new VisitsTraceLog;
        
        if (isset( $_POST['VisitsTraceLog'] ) && isset( $_POST['IvyVisitsBasicInfo'] )){
            $model->attributes = $_POST['IvyVisitsBasicInfo'];
            $model->update_timestamp = time();
            $model->update_user = Yii::app()->user->id;
            $modelLog->attributes = $_POST['VisitsTraceLog'];
            $modelLog->basic_id=$id;
            
            
        }
        
        $criteria=new CDbCriteria;
        $criteria->compare('basic_id', $id);
        $criteria->order='update_timestamp desc';
        $dataProvider = new CActiveDataProvider('VisitsTraceLog', array(
            'criteria'=>$criteria,
        ));
        
        $this->render('log', array('model'=>$model, 'modelLog'=>$modelLog, 'dataProvider'=>$dataProvider));
    }
    
    public function actionEdit()
    {
        $dataProvider=null;
        $id = Yii::app()->request->getParam('id', 0);
        $model = IvyVisitsBasicInfo::model()->findByPk($id);
        if ($model === null){
            $model = new IvyVisitsBasicInfo;
        }
        
        if (Yii::app()->request->isPostRequest){
            if (isset($_POST['IvyVisitsBasicInfo'])){
                $model->attributes = $_POST['IvyVisitsBasicInfo'];
            }
            
            if (isset($_POST['checkex'])){
                if ($model->email || $model->tel){
                   $criteria = new CDbCriteria();
                   if ($model->email)
                       $criteria->compare('email', $model->email);
                   if ($model->tel)
                       $criteria->compare('tel', $model->tel, false, 'or');
                   $model=IvyVisitsBasicInfo::model()->find($criteria);
                   if ($model === null){
                       $model = new IvyVisitsBasicInfo;
                       $model->attributes = $_POST['IvyVisitsBasicInfo'];
                       $model->isNewRecord = false;
                   }
                }
            }
            else {
                $model->birth_timestamp = strtotime($model->birth_timestamp);
                $model->child_enroll = strtotime($model->child_enroll);
                $model->concerns = isset($_POST['IvyVisitsBasicInfo']['concerns']) ? implode(',', $_POST['IvyVisitsBasicInfo']['concerns']) : '';
                if (!$model->id){
                    $model->register_timestamp = time();
                }
                $model->update_timestamp = time();
                $mdoel->update_user = Yii::app()->user->getId();
                
                if ($model->save()){
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t("global","Data Saved!"));
                    $this->addMessage('refresh', true);
                }
                else{
                    $this->addMessage('state', 'fail');
                    $errs = current($model->getErrors());
                    $this->addMessage('message', $errs?$errs[0]:Yii::t('message','Failed!'));
                }
                $this->showMessage();
            }
        }
        
        if ($model->id){
            $criteria = new CDbCriteria();
            $criteria->compare('basic_id', $model->id);
            $dataProvider = new CActiveDataProvider('IvyVisitsRecord', array(
                'criteria'=>$criteria,
            ));
        }
        
        $model->birth_timestamp = OA::formatDateTime($model->birth_timestamp);
        $model->child_enroll = OA::formatDateTime($model->child_enroll);
        
        $this->render('edit', array('model'=>$model, 'dataProvider'=>$dataProvider));
    }
    
    public function actionEditRecord($id=0)
    {
        $this->layout='//layouts/dialog';
        
        if (isset( $_POST['IvyVisitsRecord'] )){
            $model = IvyVisitsRecord::model()->findByAttributes(array('basic_id'=>$id,'schoolid'=>$_POST['IvyVisitsRecord']['schoolid']));
            if ($model === null)
                $model = new IvyVisitsRecord;
            
            $model->attributes = $_POST['IvyVisitsRecord'];
            if ($model->category == 'visit'){
                $model->visit_timestamp= strtotime($_POST['IvyVisitsRecord']['visit_timestamp']);
            }
            else{
                $model->appointment_date= strtotime($_POST['IvyVisitsRecord']['appointment_date']);
            }
            $model->basic_id = $id;
            $model->update_timestamp = time();
            $model->update_user = Yii::app()->user->getId();
            if ($model->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t("global","Data Saved!"));
            }
            else{
                $this->addMessage('state', 'fail');
                $errs = current($model->getErrors());
                $this->addMessage('message', $errs?$errs[0]:Yii::t('message','Failed!'));
            }
            $this->showMessage();
        }
        
        $adminBranch = AdmBranchLink::model()->getMultipleBranches(Yii::app()->user->getId());
        $adBranch=array();
        foreach ($adminBranch as $brkey){
            $adBranch[$brkey] = $this->branchArr[$brkey];
        }
        
        $model = new IvyVisitsRecord;
        $model->category='visit';
        
        $this->render('editrecord', array('adBranch'=>$adBranch, 'model'=>$model));
    }
}