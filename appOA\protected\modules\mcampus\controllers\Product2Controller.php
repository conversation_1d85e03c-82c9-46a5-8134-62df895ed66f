<?php

class Product2Controller extends BranchBasedController
{

    public $childsName;
    public $userName;
    public $className;
    public $productsAttrs;
    public $printFW = array();
    public $actionAccessAuths = array(
        'index' => 'o_A_Access',
        'confirmorder'=> 'ivystaff_opschool',//确认订单（发放完成）
        'confirmrefund'=> 'ivystaff_opschool',//确认退款
        'clearrefund'=> 'ivystaff_opschool',//取消退款
        'confirmwxrefund'=> 'ivystaff_opschool',//确认退款
        'commitexchange'=> 'ivystaff_opschool',//提交换货
        'finishexchange'=> 'ivystaff_opschool',//确认完成换货
        'clearexchange'=> 'ivystaff_opschool',//取消换货
        'showconfirmexchangedetail'=> 'ivystaff_opschool',//提交换货展示窗口数据
        'showconfirmrefunddetail'=> 'ivystaff_opschool',//提交退款信息展示窗口数据
    );
    //不需要选择学校的方法
    public $notSelectSchoolAction = array(
        'salesStat','orderSales'
    );
    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        $pcid = Yii::app()->request->getParam('pcid', null);
        if (!empty($pcid)) {
            $params['pcid'] = $pcid;
        }
        $state = Yii::app()->request->getParam('status', null);
        if (!empty($state)) {
            $params['state'] = $state;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";
        Yii::import('common.models.products.*');

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Campus Workspace');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mcampus/product/index');
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/modern/css/wizard/bootstrap-nav-wizard.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/vue2.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/excellentexport.js');
        $cs->registerCssFile($cs->getCoreScriptUrl() . '/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/jquery.jPrintArea.js');
//        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/xlsx.full.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/exceljs/xlsx.core.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/exceljs/xlsxStyle.core.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/exceljs/openDownload.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/tinymce/tinymce.min.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/echarts.min.js');

    }

    public function beforeAction($action)
    {
        if(in_array($action->id,$this->notSelectSchoolAction)){
            $this->multipleBranch = false;
        }
        return parent::beforeAction($action);
    }


    public function actionIndex()
    {
        $this->render('index');
    }

    //显示订单
    public function actionShowConfirmOrder()
    {
        $status = Yii::app()->request->getParam('status', 0);
        $pcid = Yii::app()->request->getParam('pcid', "");
        $productsCatModel = ProductsCat::model()->findByPk($pcid);
        $templateMap = array(
            20 => 'paid',
            30 => 'confirm',
            40 => 'send',
            41 => 'waitRefund',
            45 => 'refunding',
            50 => 'refunded',
            55 => 'exchange',
            60 => 'exchanged',
        );
        $this->render('showConfirmOrder', array(
            'status' => $status,
            'template' => $templateMap[$status],
            'productsCatModel' => $productsCatModel,
        ));
    }

    //销售 统计页面
    public function actionSalesStat()
    {
        $this->render('salesStat');
    }

    public function getMenu()
    {
        $status = Yii::app()->request->getParam('status', '');
        $pcid = Yii::app()->request->getParam('pcid', '');
        return array(
            array('label' => Yii::t('user', '等待确认'), 'url' => array("/mcampus/product2/showConfirmOrder", 'pcid' => $pcid, 'status' => ProductsInvoice::STATS_PAID), 'active' => $status == '20' ? true : false),
            array('label' => Yii::t('user', '等待发放'), 'url' => array("/mcampus/product2/showConfirmOrder", 'pcid' => $pcid, 'status' => ProductsInvoice::STATS_CONFIRM), 'active' => $status == '30' ? true : false),
            array('label' => Yii::t('user', '已经发放'), 'url' => array("/mcampus/product2/showConfirmOrder", 'pcid' => $pcid, 'status' => ProductsInvoice::STATS_SEND), 'active' => $status == '40' ? true : false),
        );
    }

    public function getExchangeMenu()
    {
        $status = Yii::app()->request->getParam('status', '');
        $pcid = Yii::app()->request->getParam('pcid', '');
        return array(
            array('label' => Yii::t('user', '等待换货'), 'url' => array("/mcampus/product2/showConfirmOrder", 'pcid' => $pcid, 'status' => ProductsInvoice::STATS_EXCHANGE), 'active' => $status == '55' ? true : false),
            array('label' => Yii::t('user', '换货完成'), 'url' => array("/mcampus/product2/showConfirmOrder", 'pcid' => $pcid, 'status' => ProductsInvoice::STATS_EXCHANGED), 'active' => $status == '60' ? true : false),
        );
    }

    public function getRefundMenu()
    {
        $status = Yii::app()->request->getParam('status', '');
        $pcid = Yii::app()->request->getParam('pcid', '');
        return array(
            array('label' => Yii::t('user', '等待退款'), 'url' => array("/mcampus/product2/showConfirmOrder", 'pcid' => $pcid, 'status' => ProductsInvoice::STATS_WAITREFUND), 'active' => $status == '41' ? true : false),
            array('label' => Yii::t('user', '退款中'), 'url' => array("/mcampus/product2/showConfirmOrder", 'pcid' => $pcid, 'status' => ProductsInvoice::STATS_REFUNDING), 'active' => $status == '45' ? true : false),
            array('label' => Yii::t('user', '退款完成'), 'url' => array("/mcampus/product2/showConfirmOrder", 'pcid' => $pcid, 'status' => ProductsInvoice::STATS_REFUNDED), 'active' => $status == '50' ? true : false),
        );
    }

    public function actionGetProductOrder()
    {
        $pcid = Yii::app()->request->getParam("pcid");
        $status = Yii::app()->request->getParam("status");
        $search_input = Yii::app()->request->getParam("search_input", array());
        $page = Yii::app()->request->getParam("page", 1);
        $pageSize = Yii::app()->request->getParam("pageSize", 20);
        $sort = Yii::app()->request->getParam("sort", '');
        $res = CommonUtils::requestDsOnline('product/getProductOrder', array(
            'school_id' => $this->branchId,
            'pcid' => $pcid,
            'status' => $status,
            'search_input' => $search_input,
            'page' => $page,
            'pageSize' => $pageSize,
            'sort' => $sort
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    //导出不同状态的订单
    public function actionExportProductOrder()
    {
        $pcid = Yii::app()->request->getParam("pcid");
        $status = Yii::app()->request->getParam("status");
        $search_input = Yii::app()->request->getParam("search_input", array());
        $res = CommonUtils::requestDsOnline('product/exportProductOrder', array(
            'school_id' => $this->branchId,
            'pcid' => $pcid,
            'status' => $status,
            'search_input' => $search_input,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    /**
     * 获取需要备的货物
     */
    public function actionGetPrepareProduct()
    {
        $pcid = Yii::app()->request->getParam("pcid");
        $status = Yii::app()->request->getParam("status");
        $search_input = Yii::app()->request->getParam("search_input", array());
        $res = CommonUtils::requestDsOnline('product/getPrepareProduct', array(
            'school_id' => $this->branchId,
            'pcid' => $pcid,
            'status' => $status,
            'search_input' => $search_input,
        ));
        $prepareExtra = CommonUtils::requestDsOnline('product/getPrepareExtra', array(
            'school_id' => $this->branchId,
            'pcid' => $pcid,
            'status' => $status,
        ));
        $data['list'] = $res['code'] == 0 ? $res['data'] : array();
        $data['extra'] = $prepareExtra['code'] == 0 ? $prepareExtra['data'] : array();
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionStorePrepareDetail(){
        $pcid = Yii::app()->request->getParam("pcid");
        $status = Yii::app()->request->getParam("status");
        $search_input = Yii::app()->request->getParam("search_input", array());
        $data_input = Yii::app()->request->getParam("data_input", array());
        //按照勾选的打印订单
        if(!empty($search_input['invoiceItemId'])){
            $search_input['invoice_item_ids'] = array();
            foreach ($search_input['invoiceItemId'] as $k => $v){
                $search_input['invoice_item_ids'] = array_merge($search_input['invoice_item_ids'],explode(',',$v));
            }
            unset($search_input['invoiceItemId']);
        }
        $res = CommonUtils::requestDsOnline2('product/storePrepareDetail', array(
            'school_id' => $this->branchId,
            'pcid' => $pcid,
            'status' => $status,
            'search_input' => $search_input,
            'data_input' => $data_input,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    //确认订单
    public function actionConfirmOrder()
    {
        $ids = Yii::app()->request->getParam("ids");
        $status = Yii::app()->request->getParam("status");
        $res = CommonUtils::requestDsOnline('product/confirmOrder', array(
            'ids' => $ids,
            'status' => $status,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    //确认退款前的详情展示
    public function actionShowConfirmRefundDetail()
    {
        $id = Yii::app()->request->getParam("id");
        $res = CommonUtils::requestDsOnline('product/showConfirmRefundDetail', array(
            'id' => $id,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }


    //确认退款 提交等待退款
    public function actionConfirmRefund()
    {
        $ids = Yii::app()->request->getParam("ids");
        $reason = Yii::app()->request->getParam("reason");
        $res = CommonUtils::requestDsOnline('product/confirmRefund', array(
            'ids' => $ids,
            'reason' => $reason,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    //取消退款
    public function actionClearRefund()
    {
        $ids = Yii::app()->request->getParam("ids");
        $res = CommonUtils::requestDsOnline('product/clearRefund', array(
            'ids' => $ids,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    //确认退款 执行微信退款
    public function actionConfirmWxRefund()
    {
        $ids = Yii::app()->request->getParam("ids");
        $res = CommonUtils::requestDsOnline('product/confirmWxRefund', array(
            'ids' => $ids,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionShowConfirmExchangeDetail()
    {
        $id = Yii::app()->request->getParam("id");
        $res = CommonUtils::requestDsOnline('product/showConfirmExchangeDetail', array(
            'id' => $id,
            'school_id' => $this->branchId
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionCommitExchange()
    {
        $id = Yii::app()->request->getParam("id");
        $paid = Yii::app()->request->getParam("paid");
        $sendback = Yii::app()->request->getParam("sendback", 1);#是否需要退回1需要2不需要
        $res = CommonUtils::requestDsOnline('product/commitExchange', array(
            'id' => $id,
            'paid' => $paid,
            'sendback' => $sendback,
            'school_id' => $this->branchId
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionFinishExchange()
    {
        $ids = Yii::app()->request->getParam("ids");
        $is_sendback = Yii::app()->request->getParam("is_sendback", 2);#是否已经退回1未退回2已退回
        $remark = Yii::app()->request->getParam("remark", '');#备注
        $res = CommonUtils::requestDsOnline('product/finishExchange', array(
            'ids' => $ids,
            'is_sendback' => $is_sendback,
            'remark' => $remark,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionClearExchange()
    {
        $ids = Yii::app()->request->getParam("ids");
        $res = CommonUtils::requestDsOnline('product/clearExchange', array(
            'ids' => $ids,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionShowExchangeDetail()
    {
        $id = Yii::app()->request->getParam("id");
        $res = CommonUtils::requestDsOnline('product/showExchangeDetail', array(
            'id' => $id,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionGetExchangeOrder()
    {
        $pcid = Yii::app()->request->getParam("pcid");
        $search_input = Yii::app()->request->getParam("search_input", array());
        $status = Yii::app()->request->getParam("status");
        $page = Yii::app()->request->getParam("page", 1);
        $pageSize = Yii::app()->request->getParam("pageSize", 20);
        $sort = Yii::app()->request->getParam("sort", '');
        $sendback = Yii::app()->request->getParam("sendback");
        $res = CommonUtils::requestDsOnline('product/getExchangeOrder', array(
            'school_id' => $this->branchId,
            'pcid' => $pcid,
            'search_input' => $search_input,
            'page' => $page,
            'pageSize' => $pageSize,
            'sort' => $sort,
            'sendback' => $sendback,
            'status' => $status,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionExportExchangeOrder()
    {
        $pcid = Yii::app()->request->getParam("pcid");
        $search_input = Yii::app()->request->getParam("search_input", array());
        $status = Yii::app()->request->getParam("status");
        $sendback = Yii::app()->request->getParam("sendback");
        $res = CommonUtils::requestDsOnline('product/exportExchangeOrder', array(
            'school_id' => $this->branchId,
            'pcid' => $pcid,
            'search_input' => $search_input,
            'sendback' => $sendback,
            'status' => $status,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }


    //打印备货单
    public function actionPrintPrepare(){
        $this->layout = "//layouts/print";
        $id = Yii::app()->request->getParam("id");
        $data = CommonUtils::requestDsOnline2('product/printPrepare', array(
            'id' => $id,
        ),'get');
//        var_dump($data);
        //获取备货单 详情
        $view = 'printPrepare';
        $this->render($view, array(
            'product_info' => $data['data']['product_info'],
            'order_list' => $data['data']['order_list'],
            'delivery_detail' => $data['data']['delivery_detail'],
            'acceptor_detail' => $data['data']['acceptor_detail'],
            'order_id' => $data['data']['order_id'],
            'party_a' => $data['data']['party_a'],
            'party_b' => $data['data']['party_b'],
        ));
    }

    /**
     * 获取需要备的货物按照是否备货分类
     */
    public function actionGetPrepareProduct2()
    {
        $pcid = Yii::app()->request->getParam("pcid");
        $status = Yii::app()->request->getParam("status");
        $search_input = Yii::app()->request->getParam("search_input", array());
        $res = CommonUtils::requestDsOnline('product/getPrepareProduct2', array(
            'school_id' => $this->branchId,
            'pcid' => $pcid,
            'status' => $status,
            'search_input' => $search_input,
        ));
        $prepareExtra = CommonUtils::requestDsOnline('product/getPrepareExtra', array(
            'school_id' => $this->branchId,
            'pcid' => $pcid,
            'status' => $status,
        ));
        $data['list'] = $res['code'] == 0 ? $res['data'] : array();
        $data['extra'] = $prepareExtra['code'] == 0 ? $prepareExtra['data'] : array();
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    //存储交货日期验收人等信息并生成备货单号和标记备货
    public function actionStorePrepareDetail2(){
        $pcid = Yii::app()->request->getParam("pcid");
        $status = Yii::app()->request->getParam("status");
        $search_input = Yii::app()->request->getParam("search_input", array());
        $data_input = Yii::app()->request->getParam("data_input", array());
        //按照勾选的打印订单
        if(!empty($search_input['invoiceItemId'])){
            $search_input['invoice_item_ids'] = array();
            foreach ($search_input['invoiceItemId'] as $k => $v){
                $search_input['invoice_item_ids'] = array_merge($search_input['invoice_item_ids'],explode(',',$v));
            }
            unset($search_input['invoiceItemId']);
        }
        $res = CommonUtils::requestDsOnline2('product/storePrepareDetail2', array(
            'school_id' => $this->branchId,
            'pcid' => $pcid,
            'status' => $status,
            'search_input' => $search_input,
            'data_input' => $data_input,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    //取消备货
    public function actionCancelPrepareProduct()
    {
        $products_prepare_id= Yii::app()->request->getParam("products_prepare_id");
        $res = CommonUtils::requestDsOnline2('product/cancelPrepareProduct', array(
            'school_id' => $this->branchId,
            'products_prepare_id' => $products_prepare_id,
        ),'post');
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    //学校订单销售统计
    public function actionOrderSales ()
    {
        $type = Yii::app()->request->getParam("type");
        $data = CommonUtils::requestDsOnline2('product/orderSales', array('type'=>$type),'get');
        $this->addMessage('state', 'success');
        $this->addMessage('data', $data['data']);
        $this->showMessage();
    }
}

