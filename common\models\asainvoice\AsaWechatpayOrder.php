<?php

/**
 * This is the model class for table "ivy_asa_wechatpay_order".
 *
 * The followings are the available columns in table 'ivy_asa_wechatpay_order':
 * @property string $orderid
 * @property double $fact_amount
 * @property double $payable_amount
 * @property string $schoolid
 * @property integer $childid
 * @property string $type
 * @property integer $status
 * @property integer $settlement_status
 * @property string $ext
 * @property integer $order_time
 * @property integer $updated
 * @property integer $updated_userid
 */
class AsaWechatpayOrder extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_asa_wechatpay_order';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('orderid, payable_amount, schoolid, childid, type, order_time, updated, updated_userid, invoice_id', 'required'),
			array('childid, status, settlement_status, order_time, updated, updated_userid', 'numerical', 'integerOnly'=>true),
			array('fact_amount, payable_amount', 'numerical'),
			array('orderid', 'length', 'max'=>50),
			array('schoolid, type', 'length', 'max'=>32),
			array('ext', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('orderid, fact_amount, payable_amount, schoolid, childid, type, status, settlement_status, ext, order_time, updated, updated_userid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'orderid' => 'Orderid',
			'fact_amount' => 'Fact Amount',
			'payable_amount' => 'Payable Amount',
			'schoolid' => 'Schoolid',
			'childid' => 'Childid',
			'type' => 'Type',
			'status' => 'Status',
			'settlement_status' => 'Settlement Status',
			'ext' => 'Ext',
			'order_time' => 'Order Time',
			'updated' => 'Updated',
			'updated_userid' => 'Updated Userid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('orderid',$this->orderid,true);
		$criteria->compare('fact_amount',$this->fact_amount);
		$criteria->compare('payable_amount',$this->payable_amount);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('type',$this->type,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('settlement_status',$this->settlement_status);
		$criteria->compare('ext',$this->ext,true);
		$criteria->compare('order_time',$this->order_time);
		$criteria->compare('updated',$this->updated);
		$criteria->compare('updated_userid',$this->updated_userid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AsaWechatpayOrder the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public function genOrderID($prefix){
		$prefix = CommonUtils::isProduction() ? $prefix : '99'.$prefix;
        $maxId = Yii::app()->subdb->createCommand()
            ->select('max(orderid)')
            ->from($this->tableName())
            ->where('orderid LIKE :prefix', array(':prefix'=>$prefix.'%'))
            ->queryRow();
        if(empty($maxId)) return $prefix . '001';

        $maxId = array_shift($maxId);
        $postfix = sprintf('%03s', intval(substr($maxId, -3)) + 1);
        return $prefix . $postfix;
    }
}
