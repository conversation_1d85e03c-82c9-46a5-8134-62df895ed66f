 <!-- 转给校长办公室处理 -->
 <div class="modal fade"  id="officeModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel"  aria-labelledby="wantSayModalLabel"  data-backdrop="static">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title">{{forwardType=="office"?'<?php echo Yii::t("referral", "Assign to Principal Office"); ?>':'<?php echo Yii::t("referral", "Transfer to Principal Office"); ?>'}}</h4>
        </div>
        <div class="modal-body p24">
            <div>
                <div v-if='forwardType=="office"' class='mb24'>
                    <div class='fontBold color3 '><?php echo Yii::t("referral", "Select a staff"); ?></div>
                    <div class='mt8' v-if='officeList.staff_info'>
                        <el-select v-model="officeData.notify_ids"  size='small' placeholder="<?php echo Yii::t("global", "Please Select"); ?>" style='width:100%'>
                            <el-option
                            v-for="(item,key,index) in officeList.staff_info"
                            :key="item.uid"
                            :label="item.name"
                            :value="item.uid">
                            </el-option>
                        </el-select>
                    </div>
                </div>
                <div v-else>
                    <div class='fontBold color3 font14'><?php echo Yii::t("referral", "Notification will be sending to:"); ?></div>
                    <div class='mt8' v-if='nextNode'>
                        <div class="flex mb16" v-for='(list,key,index) in nextNode'>
                            <div class="">
                                <img :src="list.photoUrl" data-holder-rendered="true" class="avatar">
                            </div> 
                            <div class="flex1 ml10 text_overflow">
                                <div class="mt8 font14 color3 text_overflow"> {{list.name}} <span class='labelbg' v-if='key==config.user_data.uid'><?php echo Yii::t("directMessage", "Me"); ?></span></div> 
                                <div class="color6 mt4 text_overflow">{{list.hrPosition}}</div>
                            </div>
                        </div>
                    </div>
                    <div class='fontBold color3 font14'><?php echo Yii::t("referral", "Comment"); ?></div>
                    <div  class='mt8'><textarea class="form-control" placeholder="<?php echo Yii::t("teaching", "Input"); ?>" v-model='officeData.remark' rows="3"></textarea></div>
                    <div class='flex mb10 align-items mt24'>
                        <div class='fontBold color3 font14'><?php echo Yii::t("curriculum", "Attachments"); ?></div>
                        <div class='flex1 ml10'>
                            <el-button type="text" icon="el-icon-circle-plus-outline" id='officeAddFile' class='font14 bluebg'><?php echo Yii::t("referral", "Upload"); ?></el-button>
                        </div>
                    </div>
                    <div>
                        <div class='' v-if='attachments.img.length>0'>
                            <div class='imgData mr8'  v-for='(list,i) in attachments.img'>
                                <div v-if="list.types=='1'">
                                    <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Uploading");?></div>
                                </div>
                                <div v-else>
                                    <img class='imgList' @click='uploadShowImg(list.file_key)'  :src="list.file_key" alt="">
                                    <span aria-hidden="true" class='closeImg'  @click.stop='delImg("img",list,i)'>×</span>
                                </div>
                            </div>
                            <template v-if='loadingType==1'>
                                <div class='imgData mr8'  v-for='(list,i) in loadingList'>
                                    <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Upload queue");?></div>
                                </div>
                            </template>
                        </div>
                        <div>
                            <div class='mt16' v-if='attachments.other.length>0'>
                                <div class='flex uploadFile' v-for='(list,index) in attachments.other'>
                                    <span class='glyphicon glyphicon-paperclip mr8'></span>
                                    <a target="_blank" class='flex1'  style='line-height:26px' :href='list.file_key' v-if='!list.isEdit'>{{list.title}}</a>
                                    <span class='flex1' v-else><input type="input" class='inputStyle'  @keyup.enter="saveFile(list,index)"  v-model='attachmentName' ></span>
                                    <span v-if="list.types=='1'"></span>
                                    <template v-else>
                                        <template v-if='!list.isEdit'>
                                            <span class='glyphicon glyphicon-edit icon mr16' v-if='list.file_key!=""' @click.stop='list.isEdit=true,attachmentName=list.title'></span>
                                            <span class='glyphicon glyphicon-trash icon' v-if='list.file_key!=""' @click.stop='delImg("link",list,index)'></span>
                                        </template>
                                        <span style='width:90px'  class='text-right inline-block' v-else>
                                            <button type="button" class="btn btn-primary btn-xs" @click='saveFile(list,index)'><?php echo Yii::t("global", "Save");?></button>
                                            <button type="button" class="btn btn-default btn-xs" @click='list.isEdit=false'><?php echo Yii::t("global", "Cancel");?></button>
                                        </span>
                                    </template>
                                </div>
                            </div>
                            <template v-if='loadingType==2'>
                                <div class='flex uploadFile'  v-for='(list,i) in loadingList'>
                                    <span class='glyphicon glyphicon-paperclip mr8'></span>
                                    <a  class='flex1'  style='line-height:26px' > <?php echo Yii::t("directMessage", "Upload queue");?></a>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
            <button type="button" class="btn btn-primary" @click='saveTeacher("office")' v-if='forwardType=="isp"'><?php echo Yii::t("global", "OK"); ?></button>
            <button type="button" class="btn btn-primary" @click='saveGrade("office")' v-if='forwardType=="grade"'><?php echo Yii::t("global", "OK"); ?></button>
            <button type="button" class="btn btn-primary" @click='saveOffice("office")' v-if='forwardType=="office"'><?php echo Yii::t("global", "OK"); ?></button>
            <button type="button" class="btn btn-primary" @click='saveSubject("office")' v-if='forwardType=="subject"'><?php echo Yii::t("global", "OK"); ?></button>
        </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
<!-- 转给ISP处理 -->
<div class="modal fade"  id="ispModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel"  aria-labelledby="wantSayModalLabel"  data-backdrop="static">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title"><?php echo Yii::t("referral", "Transfer to Support Groups"); ?></h4>
        </div>
        <div class="modal-body p24 overflow-y scroll-box " :style="'max-height:'+(height)+'px;overflow-x: hidden;'">
            <div class='font14'  >
                <div class='fontBold color3' ><?php echo Yii::t("referral", "Support Type"); ?></div>
                <div class='mt16'>
                    <div class="radio">
                        <label>
                            <input type="radio" value="1" v-model='ispData.help_type'>
                            <?php echo Yii::t("referral", "Need to involve Support Group only this time"); ?>
                        </label>
                    </div>
                    <div class="radio">
                        <label>
                        <input type="radio" value="2" v-model='ispData.help_type'> 
                        <?php echo Yii::t("referral", "Teacher can directly request assistance from ISP to this student in future."); ?>
                        </label>
                    </div>
                 
                </div>
                <div>
                    <div class='fontBold color3 mt24'><?php echo Yii::t("referral", "Assign to"); ?></div>
                    <div v-if='ispForwardType=="office"'>
                        <div class='flex align-items height32'>
                            <label class="radio-inline">
                                <input type="radio"  value="2" v-model='ispData.support'><?php echo Yii::t("referral", "Learning Support"); ?>  
                            </label>
                            <div class='flex1 ml24'>
                                <el-select v-model="learningValue"  size='small' placeholder="<?php echo Yii::t("global", "Please Select"); ?>" style='width:100%'  v-show='ispData.support==2'>
                                    <el-option
                                    v-for="(item,index) in learningTeacher"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                    </el-option>
                                </el-select>
                            </div>
                        </div>
                        <div class='flex align-items height32'>
                            <label class="radio-inline">
                                <input type="radio"  value="3" v-model='ispData.support'><?php echo Yii::t("referral", "Behavior Support"); ?>  
                            </label>
                            <div class='flex1 ml24'>
                                <el-select v-model="behaviorValue" size='small' placeholder="<?php echo Yii::t("global", "Please Select"); ?>" style='width:100%' v-show='ispData.support==3'>
                                    <el-option
                                    v-for="(item,index) in behaviorTeacher"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                    </el-option>
                                </el-select>
                            </div>
                        </div>
                    </div>
                    <div v-else>                
                        <div>
                            <div class="radio">
                                <label>
                                    <input type="radio"  value="2" v-model='ispData.support'><?php echo Yii::t("referral", "Learning Support"); ?>  
                                </label>
                            </div>
                        </div>
                        <div class=''>
                            <div class="radio">
                                <label>
                                    <input type="radio" value="3" v-model='ispData.support'><?php echo Yii::t("referral", "Behavior Support"); ?> 
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-if='ispForwardType!="office" && ispData.support!=""'>
                    <div class='fontBold color3 mt24'><?php echo Yii::t("referral", "Notification will be sending to:"); ?></div>
                    <div class='mt16'>
                        <div v-if='ispData.support==2'>
                            <div v-for='(list,key,index) in learningTeacher'>
                                <div v-if='list.photoUrl' class="flex mb16" >
                                    <div class="">
                                        <img :src="list.photoUrl" data-holder-rendered="true" class="avatar">
                                    </div> 
                                    <div class="flex1 ml10 text_overflow">
                                        <div class="mt8 font14 color3 text_overflow"> {{list.label}} </div> 
                                        <div class="color6 mt4 text_overflow">{{list.hrPosition}}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-if='ispData.support==3'>
                            <div v-for='(list,key,index) in behaviorTeacher'>
                                <div v-if='list.photoUrl' class="flex mb16" >
                                    <div class="">
                                        <img :src="list.photoUrl" data-holder-rendered="true" class="avatar">
                                    </div> 
                                    <div class="flex1 ml10 text_overflow">
                                        <div class="mt8 font14 color3 text_overflow"> {{list.label}} </div> 
                                        <div class="color6 mt4 text_overflow">{{list.hrPosition}}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class='fontBold color3 mt24'><?php echo Yii::t("referral", "Comment"); ?></div>
                <div  class='mt8'><textarea class="form-control" placeholder="<?php echo Yii::t("teaching", "Input"); ?>" v-model='ispData.remark' rows="3"></textarea></div>
                <div class='flex mb10 align-items mt24'>
                    <div class='fontBold color3 font14'><?php echo Yii::t("curriculum", "Attachments"); ?></div>
                    <div class='flex1 ml10'>
                        <el-button type="text" icon="el-icon-circle-plus-outline" id='ispAddFile' class='font14 bluebg'><?php echo Yii::t("referral", "Upload"); ?></el-button>
                    </div>
                </div>
                <div>
                    <div class='' v-if='attachments.img.length>0'>
                        <div class='imgData mr8'  v-for='(list,i) in attachments.img'>
                            <div v-if="list.types=='1'">
                                <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Uploading");?></div>
                            </div>
                            <div v-else>
                                <img class='imgList' @click='uploadShowImg(list.file_key)'  :src="list.file_key" alt="">
                                <span aria-hidden="true" class='closeImg'  @click.stop='delImg("img",list,i)'>×</span>
                            </div>
                        </div>
                        <template v-if='loadingType==1'>
                            <div class='imgData mr8'  v-for='(list,i) in loadingList'>
                                <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Upload queue");?></div>
                            </div>
                        </template>
                    </div>
                    <div>
                        <div class='mt16' v-if='attachments.other.length>0'>
                            <div class='flex uploadFile' v-for='(list,index) in attachments.other'>
                                <span class='glyphicon glyphicon-paperclip mr8'></span>
                                <a target="_blank" class='flex1'  style='line-height:26px' :href='list.file_key' v-if='!list.isEdit'>{{list.title}}</a>
                                <span class='flex1' v-else><input type="input" class='inputStyle'  @keyup.enter="saveFile(list,index)"  v-model='attachmentName' ></span>
                                <span v-if="list.types=='1'"></span>
                                <template v-else>
                                    <template v-if='!list.isEdit'>
                                        <span class='glyphicon glyphicon-edit icon mr16' v-if='list.file_key!=""' @click.stop='list.isEdit=true,attachmentName=list.title'></span>
                                        <span class='glyphicon glyphicon-trash icon' v-if='list.file_key!=""' @click.stop='delImg("link",list,index)'></span>
                                    </template>
                                    <span style='width:90px'  class='text-right inline-block' v-else>
                                        <button type="button" class="btn btn-primary btn-xs" @click='saveFile(list,index)'><?php echo Yii::t("global", "Save");?></button>
                                        <button type="button" class="btn btn-default btn-xs" @click='list.isEdit=false'><?php echo Yii::t("global", "Cancel");?></button>
                                    </span>
                                </template>
                            </div>
                        </div>
                        <template v-if='loadingType==2'>
                            <div class='flex uploadFile'  v-for='(list,i) in loadingList'>
                                <span class='glyphicon glyphicon-paperclip mr8'></span>
                                <a  class='flex1'  style='line-height:26px' > <?php echo Yii::t("directMessage", "Upload queue");?></a>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
            <button type="button" class="btn btn-primary"  @click='saveOffice("isp")' v-if='ispForwardType=="office"'><?php echo Yii::t("global", "OK"); ?></button>
            <button type="button" class="btn btn-primary" @click='saveGrade("isp")' v-if='ispForwardType=="grade"'><?php echo Yii::t("global", "OK"); ?></button>
            <button type="button" class="btn btn-primary" @click='saveSubject("isp")' v-if='ispForwardType=="subject"'><?php echo Yii::t("global", "OK"); ?></button>
        </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
<!-- 转给年级组长处理 -->
<div class="modal fade"  id="gradeModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel"  aria-labelledby="wantSayModalLabel"  data-backdrop="static">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title"><?php echo Yii::t("referral", "Transfer to Head of Grade"); ?></h4>
        </div>
        <div class="modal-body p24 overflow-y scroll-box " :style="'max-height:'+(height)+'px;overflow-x: hidden;'">
            <div class='font14' >
                <div v-if='GradeForwardType=="office"'>
                    <div class='fontBold color3 '><?php echo Yii::t("referral", "Assign to"); ?></div>
                    <div class='mt8' v-if='nextNode'>
                        <el-select v-model="gradeData.notify_ids"  size='small' placeholder="<?php echo Yii::t("global", "Please Select"); ?>" style='width:100%'>
                            <el-option
                            v-for="(item,key,index) in nextNode"
                            :key="item.uid"
                            :label="item.name"
                            :value="item.uid">
                            </el-option>
                        </el-select>
                    </div>
                </div>
                <div v-else>
                    <div class='fontBold color3'><?php echo Yii::t("referral", "Notification will be sending to:"); ?></div>
                    <div class='mt8 ' v-if='nextNode'>
                        <div class="flex mb16" v-for='(list,key,index) in nextNode'>
                            <div class="">
                                <img :src="list.photoUrl" data-holder-rendered="true" class="avatar">
                            </div> 
                            <div class="flex1 ml10 text_overflow">
                                <div class="mt8 font14 color3 text_overflow"> {{list.name}} <span class='labelbg' v-if='key==config.user_data.uid'><?php echo Yii::t("directMessage", "Me"); ?></span></div> 
                                <div class="color6 mt4 text_overflow">{{list.hrPosition}}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class='fontBold color3 mt24'><?php echo Yii::t("referral", "Comment"); ?></div>
                <div  class='mt8'><textarea class="form-control" placeholder="<?php echo Yii::t("teaching", "Input"); ?>" v-model='gradeData.remark' rows="3"></textarea></div>
                <div class='flex mb10 align-items mt24'>
                    <div class='fontBold color3 font14'><?php echo Yii::t("curriculum", "Attachments"); ?></div>
                    <div class='flex1 ml10'>
                        <el-button type="text" icon="el-icon-circle-plus-outline" id='gradeAddFile' class='font14 bluebg'><?php echo Yii::t("referral", "Upload"); ?></el-button>
                    </div>
                </div>
                <div>
                    <div class='' v-if='attachments.img.length>0'>
                        <div class='imgData mr8'  v-for='(list,i) in attachments.img'>
                            <div v-if="list.types=='1'">
                                <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Uploading");?></div>
                            </div>
                            <div v-else>
                                <img class='imgList' @click='uploadShowImg(list.file_key)'  :src="list.file_key" alt="">
                                <span aria-hidden="true" class='closeImg'  @click.stop='delImg("img",list,i)'>×</span>
                            </div>
                        </div>
                        <template v-if='loadingType==1'>
                            <div class='imgData mr8'  v-for='(list,i) in loadingList'>
                                <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Upload queue");?></div>
                            </div>
                        </template>
                    </div>
                    <div>
                        <div class='mt16' v-if='attachments.other.length>0'>
                            <div class='flex uploadFile' v-for='(list,index) in attachments.other'>
                                <span class='glyphicon glyphicon-paperclip mr8'></span>
                                <a target="_blank" class='flex1'  style='line-height:26px' :href='list.file_key' v-if='!list.isEdit'>{{list.title}}</a>
                                <span class='flex1' v-else><input type="input" class='inputStyle'  @keyup.enter="saveFile(list,index)"  v-model='attachmentName' ></span>
                                <span v-if="list.types=='1'"></span>
                                <template v-else>
                                    <template v-if='!list.isEdit'>
                                        <span class='glyphicon glyphicon-edit icon mr16' v-if='list.file_key!=""' @click.stop='list.isEdit=true,attachmentName=list.title'></span>
                                        <span class='glyphicon glyphicon-trash icon' v-if='list.file_key!=""' @click.stop='delImg("link",list,index)'></span>
                                    </template>
                                    <span style='width:90px'  class='text-right inline-block' v-else>
                                        <button type="button" class="btn btn-primary btn-xs" @click='saveFile(list,index)'><?php echo Yii::t("global", "Save");?></button>
                                        <button type="button" class="btn btn-default btn-xs" @click='list.isEdit=false'><?php echo Yii::t("global", "Cancel");?></button>
                                    </span>
                                </template>
                            </div>
                        </div>
                        <template v-if='loadingType==2'>
                            <div class='flex uploadFile'  v-for='(list,i) in loadingList'>
                                <span class='glyphicon glyphicon-paperclip mr8'></span>
                                <a  class='flex1'  style='line-height:26px' > <?php echo Yii::t("directMessage", "Upload queue");?></a>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
            <button type="button" class="btn btn-primary" @click='saveOffice("grade")' v-if='GradeForwardType=="office"'><?php echo Yii::t("global", "OK"); ?></button>
            <button type="button" class="btn btn-primary" @click='saveTeacher("grade")' v-if='GradeForwardType=="isp"'><?php echo Yii::t("global", "OK"); ?></button>
            <button type="button" class="btn btn-primary" @click='saveSubject("grade")' v-if='GradeForwardType=="subject"'><?php echo Yii::t("global", "OK"); ?></button>
        </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<!-- 转给学科组长 -->
<div class="modal fade"  id="subjectModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel"  aria-labelledby="wantSayModalLabel"  data-backdrop="static">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title"><?php echo Yii::t("referral", "Transfer to Head of Department"); ?></h4>
        </div>
        <div class="modal-body p24 overflow-y scroll-box " :style="'max-height:'+(height)+'px;overflow-x: hidden;'">
            <div class='font14' >
                <div class="alert alert-warning" role="alert">
                <?php echo Yii::t("referral", "Type of this entry will be converted to Academic when transferred to Lead of Subjects"); ?>
                </div>
                <div class='fontBold color3 '><?php echo Yii::t("application", "Subject"); ?></div>
                <div class='mt10'>
                    <label class="radio-inline" v-for='(list,index) in subjectList' >
                        <input type="radio" :value="list.value" v-model='subjectData.subject' @change='getSendEmail(list.value,5)'> {{list.title}}
                    </label>
                </div>
                <div class='mt24' v-if='subjectData.subject!=""'>
                    <div class='fontBold color3'><?php echo Yii::t("referral", "Notification will be sending to:"); ?></div>
                    <div class='mt8' v-if='nextNode'>
                        <div class="flex mb16" v-for='(list,key,index) in nextNode'>
                            <div class="">
                                <img :src="list.photoUrl" data-holder-rendered="true" class="avatar">
                            </div> 
                            <div class="flex1 ml10 text_overflow">
                                <div class="mt8 font14 color3 text_overflow"> {{list.name}} <span class='labelbg' v-if='key==config.user_data.uid'><?php echo Yii::t("directMessage", "Me"); ?></span></div> 
                                <div class="color6 mt4 text_overflow">{{list.hrPosition}}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class='fontBold color3 mt24'><?php echo Yii::t("referral", "Comment"); ?></div>
                <div  class='mt8'><textarea class="form-control" placeholder="<?php echo Yii::t("teaching", "Input"); ?>" v-model='subjectData.remark' rows="3"></textarea></div>
                <div class='flex mb10 align-items mt24'>
                    <div class='fontBold color3 font14'><?php echo Yii::t("curriculum", "Attachments"); ?></div>
                    <div class='flex1 ml10'>
                        <el-button type="text" icon="el-icon-circle-plus-outline" id='subjectAddFile' class='font14 bluebg'><?php echo Yii::t("referral", "Upload"); ?></el-button>
                    </div>
                </div>
                <div>
                    <div class='' v-if='attachments.img.length>0'>
                        <div class='imgData mr8'  v-for='(list,i) in attachments.img'>
                            <div v-if="list.types=='1'">
                                <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Uploading");?></div>
                            </div>
                            <div v-else>
                                <img class='imgList' @click='uploadShowImg(list.file_key)'  :src="list.file_key" alt="">
                                <span aria-hidden="true" class='closeImg'  @click.stop='delImg("img",list,i)'>×</span>
                            </div>
                        </div>
                        <template v-if='loadingType==1'>
                            <div class='imgData mr8'  v-for='(list,i) in loadingList'>
                                <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Upload queue");?></div>
                            </div>
                        </template>
                    </div>
                    <div>
                        <div class='mt16' v-if='attachments.other.length>0'>
                            <div class='flex uploadFile' v-for='(list,index) in attachments.other'>
                                <span class='glyphicon glyphicon-paperclip mr8'></span>
                                <a target="_blank" class='flex1'  style='line-height:26px' :href='list.file_key' v-if='!list.isEdit'>{{list.title}}</a>
                                <span class='flex1' v-else><input type="input" class='inputStyle'  @keyup.enter="saveFile(list,index)"  v-model='attachmentName' ></span>
                                <span v-if="list.types=='1'"></span>
                                <template v-else>
                                    <template v-if='!list.isEdit'>
                                        <span class='glyphicon glyphicon-edit icon mr16' v-if='list.file_key!=""' @click.stop='list.isEdit=true,attachmentName=list.title'></span>
                                        <span class='glyphicon glyphicon-trash icon' v-if='list.file_key!=""' @click.stop='delImg("link",list,index)'></span>
                                    </template>
                                    <span style='width:90px'  class='text-right inline-block' v-else>
                                        <button type="button" class="btn btn-primary btn-xs" @click='saveFile(list,index)'><?php echo Yii::t("global", "Save");?></button>
                                        <button type="button" class="btn btn-default btn-xs" @click='list.isEdit=false'><?php echo Yii::t("global", "Cancel");?></button>
                                    </span>
                                </template>
                            </div>
                        </div>
                        <template v-if='loadingType==2'>
                            <div class='flex uploadFile'  v-for='(list,i) in loadingList'>
                                <span class='glyphicon glyphicon-paperclip mr8'></span>
                                <a  class='flex1'  style='line-height:26px' > <?php echo Yii::t("directMessage", "Upload queue");?></a>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
            <button type="button" class="btn btn-primary" @click='saveOffice("subject")' v-if='subForwardType=="office"'><?php echo Yii::t("global", "OK"); ?></button>
            <button type="button" class="btn btn-primary" @click='saveTeacher("subject")' v-if='subForwardType=="isp"'><?php echo Yii::t("global", "OK"); ?></button>
            <button type="button" class="btn btn-primary" @click='saveGrade("subject")' v-if='subForwardType=="grade"'><?php echo Yii::t("global", "OK"); ?></button>          
        </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->