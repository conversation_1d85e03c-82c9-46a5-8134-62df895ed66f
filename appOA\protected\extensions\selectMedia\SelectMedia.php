<?php
class SelectMedia extends CWidget
{
    public $branchid='';
    public $startyear=0;
    public $classid=0;
    public $yid=0;
    public $childid=0;
    public $ldomain=0;
    public $weeknum=0;
    public $freetag='';
    public $children = array();
    public $buttonLabel='Add';
    public $multiple=false;
    public $callback='';
    public $enablePreview=true;
    public $fetchBigPhoto=false;

    public function run()
    {
        $this->render('selectmedia', array(
            'sConfs' => $this->controller->getServerConf($this->classid),
            'children' => $this->children ? $this->children : $this->controller->getNameList($this->classid),
        ));
    }
}