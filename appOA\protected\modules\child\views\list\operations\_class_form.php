<?php
$this->renderPartial('header');
?>

<div class="mb10">
    <a href="<?php echo $this->createUrl("/child/list/operations", array("category"=>"classes", "act"=>"editClass"))?>" role="button" class="btn">
        <span class="add"></span>
        添加班级
    </a>
</div>

<div class="h_a">编辑班级</div>
<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'class-form',
	'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm', 'enctype' => 'multipart/form-data'),
)); ?>
<div class="table_full">
    <table width="100%" class="" style="margin-bottom:0;">
        <col width="150" />
        <col width="400" />
        <col />
        <tbody>
            <tr>
                <th><?php echo $form->labelEx($model,'schoolid'); ?></th>
                <td>
                    <?php
                   echo $model->schoolInfo->title;
                    ?>
                </td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'yid'); ?></th>
                <td>
                    <?php echo $form->dropDownList($model,'yid', $schyears, array('class'=>'select_5', 'empty'=>Yii::t('global','Please Select'))); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'yid');?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'title'); ?></th>
                <td>
                    <?php echo $form->textField($model,'title',array('maxlength'=>255,'class'=>'input length_5')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'title');?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'classtype'); ?></th>
                <td>
                    <?php echo $form->dropDownList($model,'classtype', IvyClass::getClassTypes(), array('class'=>'select_5', 'empty'=>Yii::t('global','Please Select'))); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'classtype');?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'child_age'); ?></th>
                <td>
                    <?php echo $form->dropDownList($model,'child_age', IvyClass::getAgeList(), array('class'=>'select_5', 'empty'=>Yii::t('global','Please Select'))); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'child_age');?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'capacity'); ?></th>
                <td>
                    <?php echo $form->numberField($model,'capacity', array('class'=>'input length_2')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'capacity');?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'periodtime'); ?></th>
                <td>
                    <div class="mb10">
                        <?php
                        $pert = explode('|', $model->periodtime);
                        echo '上学时间：'.$form->numberField($model, 'periodtime', array('name'=>'IvyClass[periodtime][h1]', 'value'=>$pert[0], 'class'=>'input length_1', 'min'=>6, 'max'=>18)).' 时 '.$form->numberField($model, 'periodtime', array('name'=>'IvyClass[periodtime][m1]', 'value'=>$pert[1], 'class'=>'input length_1', 'min'=>0, 'max'=>59, 'step'=>5)).' 分'
                        ?>
                    </div>
                    <div class="mb10">
                        <?php
                        echo '放学时间：'.$form->numberField($model, 'periodtime', array('name'=>'IvyClass[periodtime][h2]', 'value'=>$pert[2], 'class'=>'input length_1', 'min'=>6, 'max'=>18)).' 时 '.$form->numberField($model, 'periodtime', array('name'=>'IvyClass[periodtime][m2]', 'value'=>$pert[3], 'class'=>'input length_1', 'min'=>0, 'max'=>59, 'step'=>5)).' 分'
                        ?>
                    </div>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'periodtime');?></div></td>
            </tr>
            <?php if ($model->picture):?>
            <tr>
                <th><?php echo $form->labelEx($model,'picture'); ?></th>
                <td>
                    <?php
                    echo CHtml::openTag('p');
                    echo CHtml::image(OA::CreateOAUploadUrl('classmgt/thumbs', $model->picture));
                    echo CHtml::closeTag('p');
                    ?>
                </td>
            </tr>
            <?php endif;?>
            <tr>
                <th><?php echo $form->labelEx($model,'uploadPhoto'); ?></th>
                <td>
                    <?php echo $form->fileField($model,'uploadPhoto',array('class'=>'')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'uploadPhoto');?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'introduction'); ?></th>
                <td>
                    <?php echo $form->textArea($model,'introduction',array('maxlength'=>255,'class'=>'length_5')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'introduction');?></div></td>
            </tr>
        </tbody>
    </table>
</div>
<div class="btn_wrap">
    <div class="btn_wrap_pd">
        <button class="btn btn_submit mr10 J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
    </div>
</div>
<?php $this->endWidget(); ?>