<?php

/**
 * This is the model class for table "ivy_child_label_group".
 *
 * The followings are the available columns in table 'ivy_child_label_group':
 * @property integer id
 * @property string group_name
 * @property string desc
 * @property integer created_by
 * @property integer updated_by
 * @property integer created_at
 * @property integer updated_at
 */
class ChildLabelGroup extends CActiveRecord
{
    CONST MAXLABEL = 64;//每个分组字段最多设置64个标签

	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ChildClassLink the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_child_label_group';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(

		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'group_name' => 'Group Name',
			'desc' => 'Desc',
			'created_by' => 'Created By',
			'updated_by' => 'Updated By',
			'created_at' => 'Created At',
			'updated_at' => 'Updated At',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('group_name',$this->group_name);
		$criteria->compare('desc',$this->desc);
		$criteria->compare('created_by',$this->created_by);
		$criteria->compare('updated_by',$this->updated_by);
		$criteria->compare('created_at',$this->created_at);
		$criteria->compare('updated_at',$this->updated_at);
		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}