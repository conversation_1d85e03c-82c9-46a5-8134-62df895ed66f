<?php if ($ChNotesModel&&$ChNotesModel->en_content){?>
<div class="journal-box">
    <h3><?php echo Yii::t("portfolio", 'Special Notes');?></h3>
    <script>
        document.write( normalText(<?php echo CJSON::encode($ChNotesModel->en_content); ?>) );
    </script>
</div>
<?php }?>

<?php if ($ClassMedia){?>
<div class="journal-box">
    <h3><?php echo Yii::t("portfolio", 'Class Media');?></h3>
    <?php
        $shareWeibo = Yii::app()->user->checkAccess('shareWeibo', array("childid" => $this->getController()->getChildId())); // 是否有分享至微博的权限?
        foreach ($ClassMedia as $m):
            if ($m->photoInfo):
        ?>
        <?php echo $m->photoInfo->renderMedia(false, $m->content, array(), $shareWeibo);?>
        <p class="caption">
        <?php echo $m->content;?>
        </p>
    <?php endif;endforeach;?>
</div>
<?php }?>
