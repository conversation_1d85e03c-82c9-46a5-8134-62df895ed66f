<?php

/**
 * This is the model class for table "ivy_invoice_child_refund".
 *
 * The followings are the available columns in table 'ivy_invoice_child_refund':
 * @property integer $rid
 * @property integer $invoice_id
 * @property integer $on_invoice_id
 * @property integer $transaction_id
 * @property integer $on_transaction_id
 * @property integer $childid
 * @property integer $yid
 * @property string $title
 * @property string $memo
 * @property double $amount
 * @property double $newamount
 * @property integer $classid
 * @property string $schoolid
 * @property string $payment_type
 * @property integer $startdate
 * @property integer $enddate
 * @property integer $userid
 * @property integer $timestamp
 * @property integer $status
 * @property integer $refund_type
 */
class InvoiceChildRefund extends CActiveRecord
{

    const STATS_COMPLETED = 20;    	//退款完成
    const STATS_AWAITING  = 77; 	//等待审核
    const STATS_CANCELLED = 99;     //作废（申请被驳回）

	/**
	 * Returns the static model of the specified AR class.
	 * @return InvoiceChildRefund the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_invoice_child_refund';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('childid, yid, schoolid, payment_type, userid, timestamp,amount', 'required'),
			array('title, transaction_id, childid, yid, schoolid, payment_type, userid, timestamp,amount, startdate, enddate, memo', 'required','on'=>'refund'),
			array('invoice_id, on_invoice_id, transaction_id, on_transaction_id, childid, yid, classid, startdate, enddate, userid, timestamp, status, refund_type', 'numerical', 'integerOnly'=>true),
			array('amount, newamount', 'numerical','min'=>0),
			array('title', 'length', 'max'=>255),
			array('schoolid', 'length', 'max'=>10),
			array('payment_type', 'length', 'max'=>100),
			array('memo', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('rid, invoice_id, on_invoice_id, transaction_id, on_transaction_id, childid, yid, title, memo, amount, newamount, classid, schoolid, payment_type, startdate, enddate, userid, timestamp, status, refund_type', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
				'Invoice'=>array(self::BELONGS_TO,'Invoice','on_invoice_id')
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'rid' => 'Rid',
			'invoice_id' => 'Invoice',
			'on_invoice_id' => 'On Invoice',
			'transaction_id' => 'Transaction',
			'on_transaction_id' => 'On Transaction',
			'childid' => 'Childid',
			'yid' => 'Yid',
			'title' => Yii::t('invoice', 'Title'),
			'memo' => Yii::t('invoice', 'Memo'),
			'amount' => Yii::t('invoice', 'Amount'),
			'newamount' => 'Newamount',
			'classid' => 'Classid',
			'schoolid' => 'Schoolid',
			'payment_type' => 'Payment Type',
			'startdate' => Yii::t('invoice', 'Startdate'),
			'enddate' => Yii::t('invoice', 'Enddate'),
			'userid' => 'Userid',
			'timestamp' => Yii::t('invoice', 'Timestamp'),
			'status' => Yii::t('invoice', 'Status'),
			'refund_type' => 'Refund Type',
			'peroid' => Yii::t('invoice', 'Refund Peroid'),
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('rid',$this->rid);
		$criteria->compare('invoice_id',$this->invoice_id);
		$criteria->compare('on_invoice_id',$this->on_invoice_id);
		$criteria->compare('transaction_id',$this->transaction_id);
		$criteria->compare('on_transaction_id',$this->on_transaction_id);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('title',$this->title,true);
		$criteria->compare('memo',$this->memo,true);
		$criteria->compare('amount',$this->amount);
		$criteria->compare('newamount',$this->newamount);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('payment_type',$this->payment_type,true);
		$criteria->compare('startdate',$this->startdate);
		$criteria->compare('enddate',$this->enddate);
		$criteria->compare('userid',$this->userid);
		$criteria->compare('timestamp',$this->timestamp);
		$criteria->compare('status',$this->status);
		$criteria->compare('refund_type',$this->refund_type);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 *　判断取消午餐的天是否在工作流退费审核进行中
	 * @param int $childId
	 * @param int $cancelDay
	 * <AUTHOR>
	 * @copyright Ivy
	 * @return int
	 */
	public function getCancleLunchExist($childId,$cancelDay)
	{
		$criteria = new CDbCriteria;
		$criteria->compare('childid', $childId);
		$criteria->compare('payment_type','lunch');
		$criteria->addCondition('startdate <='.$cancelDay.' and enddate>='.$cancelDay);
//		$criteria->addNotInCondition('status', array(99));
		$criteria->compare('status', self::STATS_AWAITING);
		$refundCount = $this->model()->count($criteria);
		return $refundCount;
	}
}