<!-- Modal -->
<?php
$invoiceLabels = Invoice::model()->attributeLabels();
?>
<div class="modal" id="batchInvoiceModal" tabindex="-1" role="dialog" aria-labelledby="batchInvoiceModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel"><?php echo Yii::t('billing','Creating Invoices');?> <small></small></h4>
            </div>
            <div class="modal-body">
                <form class="form J_ajaxForm" action="<?php echo $this->createUrl('createInvoice');?>" method="POST">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="panel panel-default J_check_wrap">
                                <div class="panel-heading">
                                    <div class="checkbox">
                                        <label>
                                            <input type="checkbox" class="J_check_all" data-checklist="J_check_c1" data-direction="y"> <?php echo Yii::t('global','Select All');?>
                                        </label>
                                    </div>
                                </div>
                                <div class="panel-body" id="invoices-students-list">
                                    <!-- students list place holder -->
                                </div>
                            </div>
                        </div>
                        <div class="col-md-8">

                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <?php echo Yii::t('global','Invoice Information');?>
                                </div>
                                <div class="panel-body" id="invoices-form">
                                    <!-- form data place holder -->
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="pop_bottom">
                            <button onclick="$('#batchInvoiceModal').modal('hide')" type="button" class="btn btn-default pull-right"><?php echo Yii::t('global','Cancel');?></button>
                            <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global','Submit');?></button>
                        </div>
                    </div>
                </form>

            </div>
        </div>
    </div>

<script type="text/template" id="invoice-form-child-template">
    <div class="checkbox">
        <label>
            <input type="checkbox" name="postData[students][]" value="<%= id %>" data-studentid="<%= id %>" class="J_check" data-yid="J_check_c1">
            <span class="<% if(disabled){print("text-muted");} %>"> <%- name %> <%- dob %></span>
            <% if(disabled){ %> <span class="text-danger">账单已存在</span> <% } %>
        </label>
    </div>
</script>

<script type="text/template" id="invoice-form-items-template">
    <div class="form-horizontal">
        <?php echo CHtml::hiddenField("postData[classid]", '<%= classid %>', array('encode'=>false));?>
        <?php echo CHtml::hiddenField("postData[calendarid]", '<%= calendarid %>', array('encode'=>false));?>
        <div class="form-group">
            <?php echo CHtml::label($invoiceLabels['payment_type'], CHtml::getIdByName('postData[payment_type]'), array('class'=>'col-sm-3 control-label'));?>
            <div class="col-sm-9">
                <?php echo CHtml::dropDownList('postData[payment_type]', null, array(), array('class'=>'form-control select_4'))?>
            </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label($invoiceLabels['title'], CHtml::getIdByName('postData[title]'), array('class'=>'col-sm-3 control-label'));?>
            <div class="col-sm-9">
                <?php echo CHtml::textField('postData[title]', '', array('class'=>'form-control'))?>
            </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label($invoiceLabels['amount'], CHtml::getIdByName('postData[amount]'), array('class'=>'col-sm-3 control-label'));?>
            <div class="col-sm-9">
                <?php echo CHtml::textField('postData[amount]', '', array('class'=>'form-control length_2'))?>
            </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label($invoiceLabels['startdate'], CHtml::getIdByName('postData[startdate]'), array('class'=>'col-sm-3 control-label'));?>
            <div class="col-sm-9">
                <?php echo CHtml::textField('postData[startdate]', '', array('class'=>'form-control length_2'))?>
            </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label($invoiceLabels['enddate'], CHtml::getIdByName('postData[enddate]'), array('class'=>'col-sm-3 control-label'));?>
            <div class="col-sm-9">
                <?php echo CHtml::textField('postData[enddate]', '', array('class'=>'form-control length_2'))?>
            </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label($invoiceLabels['duetime'], CHtml::getIdByName('postData[duetime]'), array('class'=>'col-sm-3 control-label'));?>
            <div class="col-sm-9">
                <?php echo CHtml::textField('postData[duetime]', '', array('class'=>'form-control length_2'))?>
            </div>
        </div>
        <div class="form-group">
            <?php echo CHtml::label($invoiceLabels['memo'], CHtml::getIdByName('postData[memo]'), array('class'=>'col-sm-3 control-label'));?>
            <div class="col-sm-9">
                <?php echo CHtml::textArea('postData[memo]', '', array('class'=>'form-control'))?>
            </div>
        </div>
    </div>
</script>

<script>
    var iniForm = null;
    var studentItemTemplate = _.template($('#invoice-form-child-template').html());
    var formItemTemplate = _.template($('#invoice-form-items-template').html());
    $(function(){
        iniForm = function(paymentType, studentId, semster){
            $('#invoices-students-list').empty();
            var onlyBatchOne = (paymentType != 'other') ? true : false;
            if(_.isNull(studentId)){

                var existedStudentIds = [];
                if( onlyBatchOne ){
                    var groupedInvoice = _.groupBy(childInvoices.invoices, 'payment_type');
//                    if(_.contains(invoiceTypes.main, paymentType)){
//                        var _tmpInvoices = _.groupBy(groupedInvoice[paymentType], 'semester');
//                        var existedStudentIds = _.keys( _.groupBy(_tmpInvoices[semster], 'childid') );
//                    }else{
//                        var existedStudentIds = _.keys( _.groupBy(groupedInvoice[paymentType], 'childid') );
//                    }
                    var existedStudentIds = _.keys( _.groupBy(groupedInvoice[paymentType], 'childid') );
                    console.log(existedStudentIds);
                };
                $('#invoices-students-list').siblings('.panel-heading').show();
                _.each(childInvoices.students, function(student, id){
                    student.disabled = (_.contains(existedStudentIds, id)) ?  1 : 0;
                    var _item = studentItemTemplate(student);
                    $('#invoices-students-list').append(_item);
                })
            }else{
                var _tmpStudent = childInvoices.students[studentId];
                _tmpStudent.disabled = 0;
                var _item = studentItemTemplate(_tmpStudent);

                $('#invoices-students-list').siblings('.panel-heading').hide();
                $('#invoices-students-list').append(_item);
                $('#invoices-students-list').find('input[type|="checkbox"]').attr('checked',true);
            }

            var _invoiceForm = formItemTemplate({classid: currentClassId, calendarid: currentCalendarId});
            $('#invoices-form').empty().append(_invoiceForm);
            if(onlyBatchOne){
                var _candi = [paymentType]
            }else{
                var _candi = invoiceTypes.other
            }
            $('#invoices-form').find('#postData_payment_type').empty();

            //加入账单类型下拉框选项
            _.each(_candi, function(_key,_index){
                $('#invoices-form').find('#postData_payment_type')
                    .append($('<option></option>').val(_key).html(invoiceTypeTitles[_key]));
            })

            //时间控件
            var minDate = new Date(parseInt(childInvoices.calendars[currentCalendarId][0])*1000);
            var maxDate = new Date(parseInt(childInvoices.calendars[currentCalendarId][3])*1000);
            var cfg1 = {'changeMonth':false,'changeYear':false,'dateFormat':'yy-mm-dd', minDate: minDate, maxDate: maxDate};
            var cfg2 = {'changeMonth':false,'changeYear':false,'dateFormat':'yy-mm-dd', maxDate: maxDate};
            if(_.contains(invoiceTypes['reg'], paymentType)){
                $('#invoices-form #postData_startdate').val('').attr('readonly',true);
                $('#invoices-form #postData_enddate').val('').attr('readonly',true);
            }
//            else if(_.contains(invoiceTypes['main'], paymentType)){
//                $('#invoices-form #postData_startdate').datepicker(cfg1);
//                var endDate = (parseInt(semster) == 1)? childInvoices.calendars[currentCalendarId][1] : childInvoices.calendars[currentCalendarId][3];
//                endDate = parseInt(endDate) * 1000;
//                $('#invoices-form #postData_enddate').val( $.datepicker.formatDate('yy-mm-dd', new Date(endDate))).attr('readonly',true);
//            }
            else{
                $('#invoices-form #postData_startdate').datepicker(cfg1);
                $('#invoices-form #postData_enddate').datepicker(cfg1);
            }
            $('#invoices-form #postData_duetime').datepicker(cfg2);
        }
    });
</script>