<div id="childinfo">
    <?php $this->widget('ext.childInfo.ChildInfo', array('childObj'=>$this->childObj))?>
</div>

<div class="h_a"><?php echo Yii::t("child", "Basic Profile");?></div>
<div class="table_c1" id="container" v-cloak>
<table width=100%>
	<colgroup>
		<col class="th" width=180>
		<col width=null>
		<col width=200>
	</colgroup>
	<tbody>
		<tr>
			<td class="level0">
				<?php
                    $profileMenu = array(
                        'childProfile'=>array(
                            'label' => Yii::t("child", "Child Info"),
                            'url' => array('//child/profile/index', "t"=>"child")
                        ),
                        'parentProfile'=>array(
                            'label' => Yii::t("child", "Parent Info"),
                            'url' => array('//child/profile/index', "t"=>"parent")
                        ),
                        'miscInfo'=>array(
                            'label' => Yii::t("child", "Miscellaneous"),
                            'url' => array('//child/profile/index', "t"=>"misc")
                        ),
                        'privacy'=>array(
                            'label' => Yii::t("labels", "Privacy"),
                            'url' => array('//child/profile/index', "t"=>"privacy")
                        ),
                        'addrInfo'=>array(
                            'label' => Yii::t("labels", "Address"),
                            'url' => array('//child/profile/index', "t"=>"address")
                        ),
                    );
					$this->widget('zii.widgets.CMenu', array(
						'id' => 'invoice-type',
						'items' => $profileMenu,
                        'htmlOptions' => array('class'=>'subm'),
                        'activeCssClass' => 'current'                        
                        )
					);
				?>
			</td>
			<td>
                <div id="profile-edit">
                    <?php $this->renderPartial(sprintf("_profile_%s", $t), array("model"=>$model));?>
				</div>
			</td>
            <?php if ($siblings['isRelation']):?>
            <td>
                <div style="margin-left:10px;">
                    <h3>兄弟姐妹</h3>
                    <?php foreach($siblings['info'] as $sibling):?>
                    <ul style="margin-bottom: 10px;">
                        <li>
                            <?php
                            if($sibling->photo):
                                echo CHtml::openTag('p');
                                echo CHtml::image(CommonUtils::childPhotoUrl($sibling->photo), '', array("width"=>120));
                                echo CHtml::closeTag('p');
                            endif;
                            ?>
                        </li>
                        <li><?php echo CHtml::link($sibling->getChildName(), array('/child/profile/index', 'childid'=>$sibling->childid));?></li>
                        <li><?php echo $sibling->birthday_search;?></li>
                        <li><?php $gender=OA::getChildGenderList();echo $gender[$sibling->gender];?></li>
                        <li><?php echo $sibling->getStatus();?></li>
                    </ul>
                    <?php endforeach;?>
                </div>
            </td>
            <?php endif;?>
		</tr>
	</tbody>
</table>
</div>
<?php if($t=='parent'){?>
    <?php $this->renderPartial("_parent_js");?>
<?php }?>