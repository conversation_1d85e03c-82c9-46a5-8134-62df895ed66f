<?php

class BankAccountInfoController extends BranchBasedController
{
    public $printFW;
    public $actionAccessAuths = array(
        'index'             => 'o_A_Access',
    );

    public function init()
    {
        parent::init();

        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Campus Workspace');

        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mcampus/bankAccountInfo/index');
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl() . '/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/vue2.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/excellentexport.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/xlsx.full.min.js');
    }

    public function createUrl($route, $params = array(), $ampersand = '&', $param = false)
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function actionIndex()
    {
        $this->render('index');
    }

    // 获取首页的数据
    public function actionGetData()
    {
        $schoolId = $this->branchId;
        $res = CommonUtils::requestDsOnline('bankAccountInfo/get/' . $schoolId);
        if ($res['code'] == 0) {
            $data = $res['data'];

            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    // 编辑新增 pickupCardStatus
    public function actionEdit()
    {
        $start = Yii::app()->request->getPost("start");
        $end = Yii::app()->request->getPost("end");
        $tipsTextCn = Yii::app()->request->getPost("tips_text_cn");
        $tipsTextEn = Yii::app()->request->getPost("tips_text_en");

        $schoolId = $this->branchId;
        $this->addMessage('state', 'fail');
        if (!$start || !$end || !$tipsTextCn || !$tipsTextEn) {
            $this->addMessage('message', 'pararm error');
            $this->showMessage();
        }

        $startTimestamp = strtotime($start);
        if (!$startTimestamp) {
            $this->addMessage('message', 'start error');
            $this->showMessage();
        }
        // 1-7月开始年为去年，8-12月开始年为当前年
        if (date('n', $startTimestamp) < 8) {
            $startYear = date('Y', $startTimestamp) - 1;
        } else {
            $startYear = date('Y', $startTimestamp);
        }

        $data = array(
            'start_year' => $startYear,
            'start' => $start,
            'end' => $end,
            'tips_text_cn' => $tipsTextCn,
            'tips_text_en' => $tipsTextEn,
        );

        $res = CommonUtils::requestDsOnline('bankAccountInfo/edit/' . $schoolId, $data);
        if ($res['code'] == 0) {
            $data = $res['data'];

            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    // 获取家庭住址列表
    public function actionList()
    {
        $class_id = Yii::app()->request->getPost("class_id");
        $audit_status = Yii::app()->request->getPost("audit_status");
        $page_num = Yii::app()->request->getPost("page_num", 1);

        $schoolId = $this->branchId;
        $this->addMessage('state', 'fail');

        $data = array(
            'class_id' => $class_id,
            'audit_status' => $audit_status,
            'page_num' => $page_num,
            'school_id' => $schoolId,
        );

        $res = CommonUtils::requestDsOnline('bankAccountInfo/list', $data);
        if ($res['code'] == 0) {
            $data = $res['data'];

            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    // 获取家庭住址详情
    public function actionDetail()
    {
        $id = Yii::app()->request->getParam("id");
        $this->addMessage('state', 'fail');
        if (!$id) {
            $this->addMessage('message', 'id not be blank');
            $this->showMessage();
        }

        $res = CommonUtils::requestDsOnline('bankAccountInfo/detail/' . $id);
        if ($res['code'] == 0) {
            $data = $res['data'];

            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    // 审核家庭住址
    public function actionAudit()
    {
        $id = Yii::app()->request->getParam("id");
        $sendWechat = Yii::app()->request->getParam("send_wechat");
        $this->addMessage('state', 'fail');
        if (!$id) {
            $this->addMessage('message', 'id not be blank');
            $this->showMessage();
        }
        $data = array(
            'audit_status' => Yii::app()->request->getParam("audit_status"),
            'comment' => Yii::app()->request->getParam("comment"),
            'staff_name' => $this->staff->getName(),
            'send_wechat' => $sendWechat,
        );

        $res = CommonUtils::requestDsOnline('bankAccountInfo/audit/' . $id, $data);
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    // 重置家庭住址状态
    public function actionReset()
    {
        $id = Yii::app()->request->getParam("id");
        $this->addMessage('state', 'fail');
        if (!$id) {
            $this->addMessage('message', 'id not be blank');
            $this->showMessage();
        }
        $data = array(
            'staff_name' => $this->staff->getName(),
        );

        $res = CommonUtils::requestDsOnline('bankAccountInfo/reset/' . $id, $data);
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    // 微信消息发送详情
    public function actionSendInfo()
    {
        $child_ids = Yii::app()->request->getPost("child_ids");
    
        $this->addMessage('state', 'fail');
        if (!$child_ids) {
            $this->addMessage('message', 'child_ids not be blank');
            $this->showMessage();
        }
        $data = array(
            'child_ids' => $child_ids
        );

        $res = CommonUtils::requestDsOnline('bankAccountInfo/sendInfo', $data);
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    // 微信消息发送
    public function actionSend()
    {
        $child_ids = Yii::app()->request->getPost("child_ids");
    
        $this->addMessage('state', 'fail');
        if (!$child_ids) {
            $this->addMessage('message', 'child_ids not be blank');
            $this->showMessage();
        }
        $data = array(
            'child_ids' => $child_ids
        );

        $res = CommonUtils::requestDsOnline('bankAccountInfo/send', $data);
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

    // 批量通过
    public function actionBatchpass()
    {
        $ids = Yii::app()->request->getPost("ids");
        $this->addMessage('state', 'fail');
        if (count($ids) < 0) {
            $this->addMessage('message', 'ids not be blank');
            $this->showMessage();
        }
        $data = array(
            'ids' => $ids,
            'staff_name' => $this->staff->getName()
        );

        $res = CommonUtils::requestDsOnline('bankAccountInfo/batchPass', $data);
        if ($res['code'] == 0) {
            $data = $res['data'];
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
        } else {
            $this->addMessage('message', $res['msg']);
        }
        $this->showMessage();
    }

}
