<?php
$this->widget('ext.ivyCGridView.BsCGridView', array(
    'id'=>'school-grid',
    'dataProvider'=>$sModel->search(),
    'afterAjaxUpdate'=>'js:function(){head.Util.ajaxDel()}',
    //	'filter'=>$model,
    'colgroups'=>array(
        array(
            //"colwidth"=>array(null,150,150,150),
        )
    ),
    'columns'=>array(
        array(
            'name'=>'schoolid',
            'type'=>'raw',
            'value'=>array($this, 'getSchoolText'),
        ),
        array(
            'name'=>'',
            'type'=>'raw',
            'value' => array($this, 'getSchoolButton'),
        ),
    ),
)); ?>