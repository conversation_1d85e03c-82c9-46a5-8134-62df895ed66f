<?php

$status = array(
    0 => '未面试',
    40 => '通过',
    41 => '未通过',
    42 => '候补',
);
?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('//mcampus/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Basic'), array('//mcampus/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site', '招生管理'), array('//mcampus/admissions/index')) ?></li>
        <li class="active"><?php echo Yii::t('management','Arrange interview') ?></li>
    </ol>

    <div class="row">
        <!-- 左侧菜单 -->
        <div class="col-md-2 col-sm-2 mb10">
            <?php
            $this->widget('zii.widgets.CMenu',array(
                'items'=> $this->getMenu(),
                'id'=>'pageCategory',
                'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked background-gray'),
                'activeCssClass'=>'active',
                'itemCssClass'=>''
            ));
            ?>
        </div>

        <div class="col-md-10 col-sm-12">
            <div class="mb10 row">
                <!-- 搜索框 -->
                <form  class="" style="float: left;width: 100%" action="<?php echo $this->createUrl('nointerview'); ?>" method="get">
                    <?php echo Chtml::hiddenField('branchId',$this->branchId); ?>
                        <!-- 状态 -->
                        <div class="col-sm-2 form-group">
                            <?php echo Chtml::dropDownList('calender',$calender, $school_calendaro,array('class'=>'form-control')); ?>
                        </div>
                    <div class="">
                        <div class="">
                            <button class="btn btn-default ml5" type="submit"><span class="glyphicon glyphicon-search"> </span> </button>
                            <!-- <button class="btn btn-default ml5" type="button" onclick="exportData('all')">导出全部</button> -->
                        </div>
                    </div>
                </form>
            </div>
            <?php foreach($interviewTime as $timestamp){?>
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <div class="row">
                        <h5 class="col-md-9"><?php echo date('Y-m-d H:i',$timestamp); ?></h5><div class="button-column col-md-3"><button onclick="exportData(<?php echo $timestamp; ?>)" class=" btn btn-info pull-right" target="_blank" >导出</button></div>
                        </div>
                    </div>
                    <div class="panel-body">
                        <?php foreach($interviewData[$timestamp] as $item){?>
                                <div class="col-md-3 col-sm-4 col-xs-6">
                                    <div class="checkbox mb10 mr10">
                                        <?php echo $item['name'] ." <code>". $item['grade']."</code> " . $status[$item['stat']];?>
                                    </div>
                                </div>
                        <?php } ?>
                    </div>
                </div>
            <?php } ?>
        </div>
    </div>
</div>
<script>

    var allData = <?php echo json_encode($exportData); ?>;
    var dataTop= <?php echo json_encode(array('面试日期', '面试时间', '申请学年', '入学年级', '英文名字', '中文名字', '性别', '出生日期', '父亲电话', '母亲电话', '父亲邮箱', '母亲邮箱')); ?>;
    
    function exportData(t) {
        var data = [];
        if (t == 'all') {
            $.each(allData, function (e,i) {
                data.push(i);
            });
        } else {
            data = allData[t]['item'];
        }
        data.unshift(dataTop);
        const filename = 'Admission.xlsx';
        const ws_name = "SheetJS";

        const worksheet = XLSX.utils.aoa_to_sheet(data);
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, ws_name);
        // generate Blob
        const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
        const blob = new Blob([wbout], {type: 'application/octet-stream'});
        // save file
        let link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        link.click();
        setTimeout(function() {
            // 延时释放掉obj
            URL.revokeObjectURL(link.href);
            link.remove();
        }, 500);
    }
</script>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>