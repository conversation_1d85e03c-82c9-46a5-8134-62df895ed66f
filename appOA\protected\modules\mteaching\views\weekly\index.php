<style>
    .weeklyBar a {
        line-height: 26px;
    }
</style><?php
$weeklyArrcn = array(
    "mediaupload" => "http://oa.ivyonline.cn/modules/docscenter/doc.php?action=view_pdf&fn=docs_cn_2021_09_613edf47c1d8d.pdf",
    "videoupload" => "http://oa.ivyonline.cn/modules/docscenter/doc.php?action=view_pdf&fn=docs_cn_2021_09_613ee17da9a04.pdf",
    "mediamgt" => "http://oa.ivyonline.cn/modules/docscenter/doc.php?action=view_pdf&fn=docs_cn_2021_09_613edf47c1d8d.pdf",
    "schoolnews" => "http://oa.ivyonline.cn/modules/docscenter/doc.php?action=view_pdf&fn=docs_cn_2019_10_5dad25285f086.pdf",
    "classnews" => "http://oa.ivyonline.cn/modules/docscenter/doc.php?action=view_pdf&fn=docs_cn_2021_09_613ee0cbddb9f.pdf",
    "childreport" => "http://oa.ivyonline.cn/modules/docscenter/doc.php?action=view_pdf&fn=docs_cn_2016_05_573035ed1b4e7.pdf",
    "schedule" => "http://oa.ivyonline.cn/modules/docscenter/doc.php?action=view_pdf&fn=docs_cn_2021_09_613ee12eed018.pdf",
);
$weeklyArren = array(
    "mediaupload" => "http://oa.ivyonline.cn/modules/docscenter/doc.php?action=view_pdf&fn=docs_en_2021_09_613edf47c5138.pdf",
    "videoupload" => "http://oa.ivyonline.cn/modules/docscenter/doc.php?action=view_pdf&fn=docs_en_2021_09_613ee17daca2a.pdf",
    "mediamgt" => "http://oa.ivyonline.cn/modules/docscenter/doc.php?action=view_pdf&fn=docs_en_2021_09_613edf47c5138.pdf",
    "schoolnews" => "http://oa.ivyonline.cn/modules/docscenter/doc.php?action=view_pdf&fn=docs_cn_2019_10_5dad25285f086.pdf",
    "classnews" => "http://oa.ivyonline.cn/modules/docscenter/doc.php?action=view_pdf&fn=docs_en_2021_09_613ee0cbdf115.pdf",
    "childreport" => "http://oa.ivyonline.cn/modules/docscenter/doc.php?action=view_pdf&fn=docs_en_2016_08_57a1968038b0d.pdf",
    "schedule" => "http://oa.ivyonline.cn/modules/docscenter/doc.php?action=view_pdf&fn=docs_en_2021_09_613ee12eef0f8.pdf",
)
?>
<script>
    var classid = <?php echo $this->selectedClassId?>;
    var weeknum = <?php echo $this->selectedWeeknum?>;
    var task = '<?php echo $this->selectedTask?>';
</script>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><button type="button" id="showSelectTaskModal" class="btn btn-info"><?php echo Yii::t('site','Weekly Tasks');?> <span class="glyphicon glyphicon-chevron-right"></span></button></li>
        <?php if($this->selectedClassId):?>
        <li><?php echo $this->branchObj->title;?></li>
        <li><?php echo $this->schoolClasses['items'][$this->selectedClassId];?></li>
        <?php endif;?>
        <?php if($this->selectedWeeknum):?>
        <li><?php echo 'Week '.$this->selectedWeeknum;?></li>
        <?php endif;?>
    </ol>

    <?php if($this->selectedClassId && $this->selectedWeeknum):?>
    <ul class="nav nav-pills navbar weeklyBar">
        <?php
        foreach($this->weeklyTasks['mi'] as $_k => $_t){
            echo CHtml::openTag('li', array('class'=>$_k==$this->selectedTask?'active':''));
            echo CHtml::link($_t, array('index', 'classid'=>$this->selectedClassId, 'weeknum'=>$this->selectedWeeknum, 'task'=>$_k));
            echo CHtml::closeTag('li');
        }
        ?>

       
        <?php if(isset($_GET["task"]) ){?>
        <li class="pull-right">
            <a target="_blank" href="<?php echo Yii::app()->language == 'zh_cn' ? $weeklyArrcn[$_GET["task"]]:$weeklyArren[$_GET["task"]] ; ?>">
            <span class="glyphicon glyphicon-question-sign" style="font-size: 16px;padding-right:6px;top:3px"></span> <?php echo Yii::t('teaching','Instruction Guidance');?>
            </a>
        </li>
        <?php }?>
    </ul>
    <?php endif;?>
    <?php if($this->selectedClassId && $this->selectedWeeknum && $this->selectedTask):?>
        <div class="row">
            <div class="col-md-12">
                <?php if($taskData['stopping']): ?>
                    <div class="alert alert-warning" role="alert">
                        <span class="glyphicon glyphicon-info-sign"></span>
                        <?php echo Yii::t('teaching','Sorry, this function is not available for this campus/class.');?>
                    </div>
                <?php else: ?>
                    <?php $this->renderPartial(($this->selectedGradeTask) ? '_'.$this->selectedGradeTask : '_'.$this->selectedTask, array('taskData'=>$taskData, 'classid' => $classid, 'weeknum' => $weeknum));?>
                <?php endif; ?>
            </div>
        </div>
    <?php endif;?>

<!-- Modal -->
<div class="modal" id="selectTaskModal" tabindex="-1" role="dialog" aria-labelledby="selectTaskModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel"><?php echo Yii::t('teaching','Select teaching task');?> <small><?php echo $this->branchObj->title;?></small></h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="well">
                            <ul class="nav nav-pills" id="cless-selector">
                                <?php
                                foreach($this->schoolClasses['items'] as $_classid=>$title){
                                    if($this->myClasses){
                                        if(in_array($_classid, $this->myClasses)){
                                            echo CHtml::openTag('li');
                                            echo CHtml::link($title, '#', array('data-classid'=>$_classid));
                                            echo CHtml::closeTag('li');
                                        }
                                    }
                                    else{
                                        echo CHtml::openTag('li');
                                        echo CHtml::link($title, '#', array('data-classid'=>$_classid));
                                        echo CHtml::closeTag('li');
                                    }
                                }
                                ?>
                            </ul>
                        </div>

                        <div class="well">
                            <div id="week-selector"></div>
                            <?php
                            $timepoints = explode(',', $this->calendarModel->timepoints);
                            $startDate = date('Y-m-d', $timepoints[0]);
                            $endDate = date('Y-m-d', $timepoints[3]);
                            $jsFormat = 'yy-mm-dd';
                            $this->widget('zii.widgets.jui.CJuiDatePicker', array(
                                'name'=>'week-selector',
                                // additional javascript options for the date picker plugin
                                'options'=>array(
                                    'showWeek'=>true,
                                    'showOtherMonths'=>true,
                                    'selectOtherMonths'=>true,
                                    'calculateWeek'=>'js:fisc',
                                    'onSelect'=>'js:onSelect',
                                    'beforeShowDay'=>'js:beforeShowDay',
                                    'numberOfMonths'=>2,//去掉这个参数可以变为一个月的日历
                                    'firstDay'=>0,
                                    'dateFormat'=>$jsFormat,
                                    'minDate'=>$startDate,
                                    'maxDate'=>$endDate,
                                ),
                                'htmlOptions'=>array(
                                    'class'=>'hide',
                                ),
                            ));
                            ?>
                        </div>

                        <div class="well">
                            <ul class="nav nav-pills" id="task-selector">
                                <?php
                                foreach($this->weeklyTasks['mi'] as $_k => $_t):
                                ?>
                                    <li><a href="#" data-task="<?php echo $_k;?>"><?php echo $_t; ?></a></li>
                                <?php
                                endforeach;
                                ?>
                            </ul>
                        </div>

                        <?php echo CHtml::form($this->createUrl('index'), 'get', array('class'=>'form-horizontal', 'onsubmit'=>'return checkSubmit(this)'));?>
                            <div id="form-data">
                                <?php echo CHtml::hiddenField('classid');?>
                                <?php echo CHtml::hiddenField('weeknum');?>
                                <?php echo CHtml::hiddenField('task');?>
                            </div>

                            <div class="pop_bottom">
                                <button onclick="$('#selectTaskModal').modal('hide')" type="button" class="btn btn-default pull-right"><?php echo Yii::t('global','Cancel');?></button>
                                <button type="submit" class="btn btn-primary pull-right mr10"><?php echo Yii::t('global','OK');?></button>
                            </div>
                        <?php echo CHtml::endForm();?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php
$js = "
if(weeknum){
    var _date = fisc1('".$startDate."', weeknum);
    startDate = new Date(_date.getFullYear(), _date.getMonth(), _date.getDate() - _date.getDay());
    endDate = new Date(_date.getFullYear(), _date.getMonth(), _date.getDate() - _date.getDay() + 6);
    $('#week-selector').datepicker('setDate', $.datepicker.formatDate( '".$jsFormat."', _date ));
    $('#form-data input#weeknum').val( weeknum );
}
";
$cs = Yii::app()->clientScript;
$cs->registerScript('_date', $js);
?>

<script>
    var openSelectTaskModal;
    var startDate, endDate;
    var yy = <?php echo date('Y', $timepoints[0])?>;
    var mm = <?php echo date('m', $timepoints[0])-1?>;
    var dd = <?php echo date('d', $timepoints[0])?>;

    $(function(){
        $.datepicker.setDefaults($.datepicker.regional['<?php echo (Yii::app()->language == 'en_us')? '': 'zh-CN'?>']);

        openSelectTaskModal = function(){
            $('#selectTaskModal').modal();//{backdrop:'static', keyboard:false}
        };

        $('#showSelectTaskModal').click(function(){
            openSelectTaskModal();
        });

        if(!classid || !weeknum || !task)
            openSelectTaskModal();

        if(classid){
            var obj = $('#cless-selector li a[data-classid="'+classid+'"]');
            if( obj.length ){
                obj.parent().addClass('active');
                $('#form-data input#classid').val( classid );
            }
        }
        if(task){
            var obj = $('#task-selector li a[data-task="'+task+'"]');
            if( obj.length ){
                obj.parent().addClass('active');
                $('#form-data input#task').val( task );
            }
        }

        $('#cless-selector').on('click', 'a', function(e){
            e.preventDefault();
            $('#cless-selector li').removeClass('active');
            $(this).parent().addClass('active');
            $('#form-data input#classid').val( $(this).data('classid') );
        });

        $('#task-selector').on('click', 'a', function(e){
            e.preventDefault();
            $('#task-selector li').removeClass('active');
            $(this).parent().addClass('active');
            $('#form-data input#task').val( $(this).data('task') );
        });

        $('#week-selector').on('mousemove', '.ui-datepicker-calendar tr', function() { $(this).find('td a').addClass('ui-state-hover'); });
        $('#week-selector').on('mouseleave', '.ui-datepicker-calendar tr', function() { $(this).find('td a').removeClass('ui-state-hover'); });
    });

    function fisc(date) {
        var checkDate = new Date(date.getTime());
        //以周六为计算基准
        checkDate.setDate(checkDate.getDate() + 6 - (checkDate.getDay() || 7));
        var time = checkDate.getTime();
        var yearStart = new Date();
        yearStart.setFullYear(yy, mm, dd);
//        yearStart.setYear(yy);
//        yearStart.setMonth(mm); //0-11表示1-12
//        yearStart.setDate(dd);
        var week = (Math.floor(Math.round((time - yearStart) / 86400000) / 7) + 2);
        if (week < 1) {
            week = 52 + week;
        }
        return week;
    }

    function fisc1(st, weeknum) {
        var sTime = new Date(st);
        var sday = 6-sTime.getDay();
        if (sday !== 2){
            sTime.setDate(parseInt(sTime.getDate())+parseInt(sday-2));
        }
        sTime.setTime(sTime.getTime() + 86400000 * 7 * (weeknum-1));

        return sTime;
    }

    function onSelect(dateText, inst) {
        var date = $(this).datepicker('getDate');
        startDate = new Date(date.getFullYear(), date.getMonth(), date.getDate() - date.getDay());
        endDate = new Date(date.getFullYear(), date.getMonth(), date.getDate() - date.getDay() + 6);

        var weeknum = fisc(startDate);
        $('#form-data input#weeknum').val( weeknum );
    }

    function beforeShowDay(date) {
        var cssClass = '';
        if(date >= startDate && date <= endDate)
            cssClass = 'ui-datepicker-current-day';
        return [true, cssClass];
    }

    function checkSubmit(_this) {
        $('#J_fail_info').remove();
        var button = $(_this).find('button[type="submit"]');
        var flag = true, msg=[];

        if( $(_this).find('input#classid').val() == '' ){
            flag = false;
            msg.push('<?php echo Yii::t("teaching","class");?>');
        }
        if( $(_this).find('input#weeknum').val() == '' ){
            flag = false;
            msg.push('<?php echo Yii::t("teaching","week");?>');
        }
        if( $(_this).find('input#task').val() == '' ){
            flag = false;
            msg.push('<?php echo Yii::t("teaching","task");?>');
        }
        if(flag){
            return true;
        }
        else{
            $( '<span id="J_fail_info" class="text-warning"><i class="glyphicon glyphicon-remove text-warning"></i> <?php echo Yii::t("global","Please Select");?> ' + msg.join('、') + '！</span>' ).appendTo(button.parent()).fadeIn( 'fast' );
            return false;
        }
    }

    function showHelp(){
        $('.page-help[data-page|="'+task+'"]').toggle();
    }
</script>

<style>
.page-help{
    /*display: none;*/
}
#week-selector .ui-datepicker-today a.ui-state-highlight{
    background-color: #FFF !important;
    color: #555 !important;
}
#week-selector .ui-datepicker-current-day a{
    border: 1px solid #aaa;
    background-color: #FF8213 !important;
    background-image: none !important;
    font-weight: normal;
    color: #f2f2f2 !important;
}
</style>

<?php
$this->branchSelectParams['extraUrlArray'] = array('//mteaching/weekly/index');
$this->renderPartial('//layouts/common/branchSelectBottom');
?>