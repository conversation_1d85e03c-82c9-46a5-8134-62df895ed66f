<?php

class ScheduleController extends BranchBasedController
{
    // 访问action的初级权限
    public $actionAccessAuths = array(
        'saveSchedulePlan'  => 'o_A_scheduleMgt',
        'schedule'          => 'o_A_scheduleMgt',
        'getAssignClass'          => 'o_A_scheduleMgt',
        'delScheduleItem'          => 'o_A_scheduleMgt',
    );

    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }
    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mgrades/schedule/index');
        Yii::import('common.models.calendar.*');
    }

    public function actionIndex()
    {
        $programs = CommonUtils::LoadConfig('CfgCampusProgram');
        $program = isset( $programs[$this->branchObj->type] ) ? $programs[$this->branchObj->type] : null;

        if(empty($program)){
            echo 'Invalid Campus Type, DayStar Type use ONLY.';
            Yii::app()->end();
        }

        $scheduleId = Yii::app()->request->getParam('scheduleid', null);
        Yii::import('common.models.calendar.*');
        Yii::import('common.models.grades.*');

        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');

        $calendar = Calendar::model()->findByPk($this->branchObj->schcalendar);
        $crit = new CDbCriteria();
        $crit->compare('t.schoolid', $this->branchId);
        $crit->compare('t.yid', $this->branchObj->schcalendar);
        $crit->order = 'valid_from ASC';
        $schedules = GradesSchedule::model()->with('gradesScheduleLink')->findAll($crit);
        if(is_null($scheduleId)){
            $scheduleData = array();

            foreach($schedules as $_schedule){
                $scheduleData[$_schedule->id] = array(
                    'id' => $_schedule->id,
                    'label' => $_schedule->label,
                    'yid' => $_schedule->yid,
                    'valid_from' => date('Y-m-d', $_schedule->valid_from),
                    'valid_from_label' => OA::formatDateTime($_schedule->valid_from),
                    'valid_to' => date('Y-m-d', $_schedule->valid_to),
                    'valid_to_label' => OA::formatDateTime($_schedule->valid_to),
                    'schedule_code' => $_schedule->schedule_code,
                );
            }

            $this->render('index', array('calendar'=>$calendar, 'schedules'=>$schedules, 'scheduleData'=>$scheduleData,'programCfg'=>$program));
        }else{
            Yii::import('common.models.classTeacher.*');
            $criteria = new CDbCriteria();
            $criteria->compare('t.schoolid', $this->branchId);
            $criteria->compare('t.yid', $this->branchObj->schcalendar);
            $criteria->compare('t.child_age', '>=5');
            $criteria->compare('t.stat', '10');
            $criteria->order = 't.child_age ASC, t.title ASC';
            $classes = IvyClass::model()->with('teacherInfo')->findAll($criteria);
            $teachers = array();
            $users = User::model()->getUserByPosition($this->branchId);
            foreach($users as $user){
                $teachers[$user->uid] = $user->getName();
            }

            $teachersubject = array();
            $items = TeacherSubjectLink::model()->findAllByAttributes(array('schoolid'=>$this->branchId, 'status'=>1));
            foreach($items as $item){
                $teachersubject[$item->teacher_uid][] = $item->subject_flag;
            }
//            echo "<pre>";
//print_r($calendar);die;
            $this->render('schedule',
                array(
                    'calendar'=>$calendar,
                    'schedules'=>$schedules,
                    'classes'=>$classes,
                    'programCfg'=>$program,
                    'teachers'=>$teachers,
                    'teachersubject'=>$teachersubject,
                    'scheduleid'=>$scheduleId
                ));
        }

    }

    public function actionSaveSchedulePlan()
    {
        /**
         * 查看提交上来的form数组，对比 schoolid == this->branchid
         * 保存时 active 都置为0; updated 置为当前时间戳
         * 保存成功后返回数据
         * $data['item'] = array() 本条记录的各项值，其中valid_from, valid_to 需要用 'Y-m-d'的格式返回; 再增加两个值
         *      valid_from_label, valid_to_label, 用OA::formatDateTime格式化返回
         * $this->addMessage('data', $data)
         * $this->addMessage('callback', 'cbSchedulePlanSave')
         */
		Yii::import('common.models.grades.*');
		if(Yii::app()->request->isPostRequest && Yii::app()->request->isAjaxRequest){
			$GradesSchedule = Yii::app()->request->getParam('GradesSchedule', array());
			$id = $GradesSchedule['id'];
            $item = GradesSchedule::model()->findByPk($id);
            if($item == null){
                $item = new GradesSchedule;
            }
            if($_POST['GradesSchedule']['schoolid'] == $this->branchId){
                $item->attributes = $_POST['GradesSchedule'];
                $item->updated = time();
                $item->valid_from = strtotime($GradesSchedule['valid_from']);
                $item->valid_to = strtotime($GradesSchedule['valid_to']);
                $item->classid = isset($_POST['GradesScheduleItem']['classid']) ? $_POST['GradesScheduleItem']['classid'] : '';
                if($item->save()){
                    //delete all schedule link
                    GradesScheduleLink::model()->deleteAllByAttributes(array('schedule_id'=>$id));
                    //add schedule link
                    foreach ($_POST['GradesScheduleItem']['classid'] as $val){
                        $link = new GradesScheduleLink();
                        $link->schedule_id = $item->id;
                        $link->class_id = $val;
                        $link->schedule_code = $item->schedule_code;
                        $link->yid = $item->yid;
                        $link->schoolid = $item->schoolid;
                        $link->save();
                    }
                    $itemData = $item->getAttributes();
                    $itemData['valid_from_label'] = OA::formatDateTime($item->valid_from);
                    $itemData['valid_to_label'] = OA::formatDateTime($item->valid_to);
                    $data['item'] = $itemData;
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('', '保存成功!'));
                    $this->addMessage('data', $data);
                    $this->addMessage('callback', 'cbSchedulePlanSave');
                }
                else{
                    $this->addMessage('state', 'fail');
                    $err = current($item->getErrors());
                    $this->addMessage('message', $err[0]);
                }
            }
			$this->showMessage();
		}
    }

    public function actionLoadScheduleDataByClass(){
        $data = array();
        Yii::import('common.models.grades.*');
        $classId = intval( Yii::app()->request->getParam('classid', 0) );
        $scheduleId = intval( Yii::app()->request->getParam('scheduleid', 0) );
        $schedule = GradesSchedule::model()->findByAttributes(array(
            'schoolid' => $this->branchId,
            'id' => $scheduleId,
        ));
        if($schedule){
            $sItems = GradesScheduleItem::model()->findAllByAttributes(array('classid'=>$classId, 'schedule_id'=>$scheduleId));
            foreach ($sItems as $item) {
                $data['data'][$item->id] = $item->getAttributes();
                if(is_numeric($data['data'][$item->id]['teacher_uid'])){
                    foreach($data as $k=>$v){
                        $data['data'][$item->id]['teacher_uid'] =  json_encode(array($v[$item->id]['teacher_uid']));
                    }
                }
            }

            $data['classid'] = $classId;
        }
        echo CJSON::encode(
            array(
                'data' => $data,
                'state' => 'success'
            )
        );
        Yii::app()->end();
    }

    public function actionScheduleOverview(){
        Yii::import('common.models.grades.*');
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
//        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');

        $scheduleId = Yii::app()->request->getParam('scheduleid',0);
        $schedule = GradesSchedule::model()->findByPk($scheduleId);
//        Yii::msg($scheduleId);
//        echo "<pre>";
//        print_r($schedule);die;
        if($schedule && $schedule->schoolid == $this->branchId){
            $this->render('overview', array('scheduleid'=>$scheduleId,'schedule'=>$schedule));
        }

    }

    public function actionGetOverviewData(){
        $type = Yii::app()->request->getPost('type',null);
        $type = Yii::app()->request->getParam('type',null);
        $scheduleId = Yii::app()->request->getParam('scheduleid',0);
        $result = $data = array();
        switch($type){
            case 'schedules':
                Yii::import('common.models.grades.*');
                $schedule = GradesSchedule::model()->findByPk($scheduleId);
                if( $schedule && $schedule->schoolid == $this->branchId ){
                    $items = GradesScheduleItem::model()->findAllByAttributes(array(
                        'schedule_id' => $scheduleId,
                    ));
                    foreach($items as $item){
                        $data[] = $item->getAttributes(
                            array(
                                "classid",
                                "id",
                                "subject_flag",
                                "teacher_uid",
                                "timeslot",
                                "weekday"
                            )
                        );
                        if(is_numeric($item->teacher_uid)){
                            $item->teacher_uid =  json_encode(array($item->teacher_uid));
                        }
                        $teacherids = CJSON::decode($item->teacher_uid);
                        if ( $teacherids ){
                            foreach($teacherids as $tuid){
                                $data1[] = array(
                                    'id' => $item->id,
                                    'classid' => $item->classid,
                                    'subject_flag' => $item->subject_flag,
                                    'teacher_uid' => $tuid,
                                    'timeslot' => $item->timeslot,
                                    'weekday' => $item->weekday,
                                );
                            }
                        }
                    }
                }
                $result['data1'] = $data1;
                $result['cmd'] = 'pageData.scheduleList = data.data;pageData.scheduleList1 = data.data1;';
                break;
            case 'teachers':
                Yii::import('common.models.classTeacher.*');
                $userModels = User::model()->with(array('profile','staffInfo'))->getUserByPosition($this->branchId);
                if (!empty($userModels)){
                    foreach ($userModels as $user){
                        $data[$user->uid] = array(
                            'uid' => $user->uid,
                            'name' => $user->getName(),
                            'pubphoto' => $user->staffInfo->staff_photo
                        );
                    }
                }
                $result['cmd'] = 'pageData.teacherList = data.data;';
                break;
            case 'classes':
                Yii::import('common.models.classTeacher.*');
                $crit = new CDbCriteria();
                $crit->compare('t.schoolid', $this->branchId);
                $crit->compare('t.yid', $this->branchObj->schcalendar);
                $crit->compare('t.child_age', '>6');
                $crit->order = 't.child_age ASC, t.title ASC';
                $classModels = IvyClass::model()->with('teacherInfo')->findAll($crit);
                if(!empty($classModels)){
                    foreach($classModels as $class){
                        $_cdata = array(
                            'title' => $class->title,
                            'classid' => $class->classid
                        );
                        $data[$class->classid] = $_cdata;
                        $sortedClasses[] = $class->classid;
                        $scheduleGroup[] = $class->classid;
                    }
                }
                $result['cmd'] = 'pageData.classList = data.data; ' .
                    ' pageData.sortedClassIds = data.sortedClasses; ';
                break;
        }
        $result['data'] = $data;
        $result['sortedClasses'] = $sortedClasses;
        echo CJSON::encode($result);
        Yii::app()->end();
    }


    public function actionSchedule()
    {
        Yii::import('common.models.grades.*');
        $scheduleId = isset($_POST['extraData']['schedule_id']) && $_POST['extraData']['schedule_id'] ? intval($_POST['extraData']['schedule_id']) : 0;
        $weekday = $_POST['postData']['weekday'] ? $_POST['postData']['weekday'] : 0;
        $timeslot = $_POST['postData']['timeslot'] ? $_POST['postData']['timeslot'] : 0;
        $classid = $_POST['postData']['classid'] ? $_POST['postData']['classid'] : 0;
        $teacher_uid = $_POST['postData']['teacher_uid'] ? $_POST['postData']['teacher_uid'] : '';

        if(!$teacher_uid){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '老师不能为空');
            $this->showMessage();
        }
        $criteria = new CDbCriteria();
        $criteria->compare('weekday', $weekday);
        $criteria->compare('timeslot', $timeslot);
        $criteria->compare('classid', $classid);
        $scheduleItem = GradesScheduleItem::model()->find($criteria);
        if(empty($scheduleItem)){
            $scheduleItem = new GradesScheduleItem();
        }
        $scheduleItem->attributes = $_POST['postData'];
        $scheduleItem->updated = time();
        $scheduleItem->room_code = 1;
        $scheduleItem->teacher_uid =  json_encode($teacher_uid);
        $scheduleItem->schedule_id = $scheduleId;
        if($scheduleItem->save()){
            $this->addMessage('state', 'success');
            $this->addMessage('message', '成功');
            $this->addMessage('callback', 'cbSchedule');
            $this->addMessage('data', $scheduleItem->attributes);
        }
        else{
            $this->addMessage('state', 'fail');
            $errs = current($scheduleItem->getErrors());
            $this->addMessage('message', $errs?$errs[0]:'失败');
        }
        $this->showMessage();

    }

    public function actionClearScheduleByClass()
    {
		Yii::import('common.models.grades.*');
		$classid = Yii::app()->request->getPost('classid', 0);
		$scheduleid = Yii::app()->request->getPost('scheduleid', 0);
		if($classid && $scheduleid){
            $model = GradesSchedule::model()->findByPk($scheduleid);
            if($model != null && $model->schoolid == $this->branchId){
                $criter = new CDbCriteria();
                $criter->compare('classid', $classid);
                $criter->compare('schedule_id', $scheduleid);
                GradesScheduleItem::model()->deleteAll($criter);
                $this->addMessage('state', 'success');
                $this->addMessage('message', '清空成功');
            }
		}
        $this->showMessage();
    }
	
	public function actionDelSchedule()
	{
		Yii::import('common.models.grades.*');
		$scheduleid = Yii::app()->request->getPost('scheduleid', 0);
		$criter = new CDbCriteria();
		$criter->compare('schedule_id', $scheduleid);
		$items = GradesScheduleItem::model()->count($criter);
		if($items){
			$this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('', '课表未清空，删除失败！'));
		}
		else{
			$model = GradesSchedule::model()->findByAttributes(array('schoolid'=>$this->branchId, 'id'=>$scheduleid));
            if($model->delete()){
                //delete all schedule link
                GradesScheduleLink::model()->deleteAllByAttributes(array('schedule_id'=>$scheduleid));
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('', '删除成功！'));
                $this->addMessage('data', array('scheduleid' => $scheduleid));
                $this->addMessage('callback', 'cbDelPlan');
            }
            else{
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('', '删除失败！'));
            }
		}
		$this->showMessage();
	}
    
    public function actionGetAssignClass(){
        Yii::import('common.models.grades.GradesScheduleLink');
        Yii::import('common.models.calendar.Calendar');
        $data = array();
        $request = Yii::app()->request;
        $id = $request->getPost('id',0);
        if ($request->isAjaxRequest){
            $calendar = Calendar::model()->findByPk($this->branchObj->schcalendar);
            $data['bySchedule'] = GradesScheduleLink::getAssignedClassByScheduleId($id);
            $data['byBranch'] = GradesScheduleLink::getAssignedClass($this->branchId, $calendar->yid);
        }
        $this->addMessage('state', 'success');
        $this->addMessage('data', $data);
        $this->showMessage();
    }
    
    /*
     * 删除课程 
    */
    public function actionDelScheduleItem(){
        Yii::import('common.models.grades.*');
		$id = Yii::app()->request->getPost('id', 0);
		$subject_flag = Yii::app()->request->getPost('subject_flag', 0);
        if (Yii::app()->request->isAjaxRequest && $id && $subject_flag){
            $model = GradesScheduleItem::model()->findByPk($id);
            if (!empty($model) && $model->subject_flag == $subject_flag){
                if ($model->delete()){
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('', '删除成功！'));
                }else{
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('', '删除失败！'));
                }
            }
        }
        $this->showMessage();
    }

}