/*
 * jQuery Orbit Plugin 1.2.3
 * www.ZURB.com/playground
 * Copyright 2010, ZURB
 * Free to use under the MIT license.
 * http://www.opensource.org/licenses/mit-license.php
*/

(function(a){a.fn.orbit=function(b){var c={animation:"horizontal-push",animationSpeed:600,timer:true,advanceSpeed:4e3,pauseOnHover:false,startClockOnMouseOut:false,startClockOnMouseOutAfter:1e3,directionalNav:true,captions:true,captionAnimation:"fade",captionAnimationSpeed:600,bullets:false,bulletThumbs:false,bulletThumbLocation:"",afterSlideChange:function(){}};var b=a.extend(c,b);return this.each(function(){function J(a){function j(){k.eq(h).css({"z-index":1});l();b.afterSlideChange.call(this)}var h=c,i=a;if(h==i){return false}if(k.length=="1"){return false}if(!g){m();if(a=="next"){c++;if(c==d){c=0}}else if(a=="prev"){c--;if(c<0){c=d-1}}else{c=a;if(h<c){i="next"}else if(h>c){i="prev"}}I();k.eq(h).css({"z-index":2});if(b.animation=="fade"){k.eq(c).css({opacity:0,"z-index":3}).animate({opacity:1},b.animationSpeed,j)}if(b.animation=="horizontal-slide"){if(i=="next"){k.eq(c).css({left:e,"z-index":3}).animate({left:0},b.animationSpeed,j)}if(i=="prev"){k.eq(c).css({left:-e,"z-index":3}).animate({left:0},b.animationSpeed,j)}}if(b.animation=="vertical-slide"){if(i=="prev"){k.eq(c).css({top:f,"z-index":3}).animate({top:0},b.animationSpeed,j)}if(i=="next"){k.eq(c).css({top:-f,"z-index":3}).animate({top:0},b.animationSpeed,j)}}if(b.animation=="horizontal-push"){if(i=="next"){k.eq(c).css({left:e,"z-index":3}).animate({left:0},b.animationSpeed,j);k.eq(h).animate({left:-e},b.animationSpeed)}if(i=="prev"){k.eq(c).css({left:-e,"z-index":3}).animate({left:0},b.animationSpeed,j);k.eq(h).animate({left:e},b.animationSpeed)}}A()}}function I(){if(!b.bullets){return false}else{F.children("li").removeClass("active").eq(c).addClass("active")}}function A(){if(!b.captions||b.captions=="false"){return false}else{var d=k.eq(c).data("caption");_captionHTML=a(d).html();if(_captionHTML){z.attr("id",d).html(_captionHTML);if(b.captionAnimation=="none"){z.show()}if(b.captionAnimation=="fade"){z.fadeIn(b.captionAnimationSpeed)}if(b.captionAnimation=="slideOpen"){z.slideDown(b.captionAnimationSpeed)}}else{if(b.captionAnimation=="none"){z.hide()}if(b.captionAnimation=="fade"){z.fadeOut(b.captionAnimationSpeed)}if(b.captionAnimation=="slideOpen"){z.slideUp(b.captionAnimationSpeed)}}}}function o(){if(!b.timer||b.timer=="false"){return false}else{r=false;clearInterval(w);u.addClass("active")}}function n(){if(!b.timer||b.timer=="false"){return false}else if(q.is(":hidden")){w=setInterval(function(a){J("next")},b.advanceSpeed)}else{r=true;u.removeClass("active");w=setInterval(function(a){var b="rotate("+v+"deg)";v+=2;s.css({"-webkit-transform":b,"-moz-transform":b,"-o-transform":b});if(v>180){s.addClass("move");t.addClass("move")}if(v>360){s.removeClass("move");t.removeClass("move");v=0;J("next")}},b.advanceSpeed/180)}}function m(){g=true}function l(){g=false}var c=0,d=0,e,f,g;var h=a(this).addClass("orbit"),j=h.wrap('<div class="orbit-wrapper" />').parent();h.add(e).width("1px").height("1px");var k=h.children("img, a, div");k.each(function(){var b=a(this),c=b.width(),g=b.height();if(c>h.width()){h.add(j).width(c);e=h.width()}if(g>h.height()){h.add(j).height(g);f=h.height()}d++});if(k.length==1){b.directionalNav=false;b.timer=false;b.bullets=false}k.eq(c).css({"z-index":3}).fadeIn(function(){k.css({display:"block"})});if(b.timer){var p='<div class="timer"><span class="mask"><span class="rotator"></span></span><span class="pause"></span></div>';j.append(p);var q=j.children("div.timer"),r;if(q.length!=0){var s=a("div.timer span.rotator"),t=a("div.timer span.mask"),u=a("div.timer span.pause"),v=0,w;n();q.click(function(){if(!r){n()}else{o()}});if(b.startClockOnMouseOut){var x;j.mouseleave(function(){x=setTimeout(function(){if(!r){n()}},b.startClockOnMouseOutAfter)});j.mouseenter(function(){clearTimeout(x)})}}}if(b.pauseOnHover){j.mouseenter(function(){o()})}if(b.captions){var y='<div class="orbit-caption"></div>';j.append(y);var z=j.children(".orbit-caption");A()}if(b.directionalNav){if(b.directionalNav=="false"){return false}var B='<div class="slider-nav"><span class="right">Right</span><span class="left">Left</span></div>';j.append(B);var C=j.children("div.slider-nav").children("span.left"),D=j.children("div.slider-nav").children("span.right");C.hide();D.hide();C.click(function(){o();J("prev")});D.click(function(){o();J("next")});j.mouseenter(function(){C.fadeIn();D.fadeIn()});j.mouseleave(function(){C.fadeOut();D.fadeOut()})}if(b.bullets){var E='<ul class="orbit-bullets"></ul>';j.append(E);var F=j.children("ul.orbit-bullets");for(i=0;i<d;i++){var G=a("<li>"+(i+1)+"</li>");if(b.bulletThumbs){var H=k.eq(i).data("thumb");if(H){var G=a('<li class="has-thumb">'+i+"</li>");G.css({background:"url("+b.bulletThumbLocation+H+") no-repeat"})}}j.children("ul.orbit-bullets").append(G);G.data("index",i);G.click(function(){o();J(a(this).data("index"))})}I()}})}})(jQuery)