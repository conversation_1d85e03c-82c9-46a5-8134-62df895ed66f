<?php

class SiteController extends Controller
{
	public function actions()
	{
		return array(
			// captcha action renders the CAPTCHA image displayed on the contact page
			'captcha'=>array(
				'class'=>'CCaptchaAction',
				//'backColor'=>0xFFFFFF,
			),
			// page action renders "static" pages stored under 'protected/views/site/pages'
			// They can be accessed via: index.php?r=site/page&view=FileName
			'page'=>array(
				'class'=>'CViewAction',
			),
		);
	}

	public function actionLang($lang)
	{
		$lang = OA::setLangCookie($lang);

        echo CJSON::encode(array("result"=>"success","ret"=>$lang));
        Yii::app()->end();
		
	}
	/**
	 * This is the action to handle external exceptions.
	 */
	public function actionError()
	{
		
	    if($error=Yii::app()->errorHandler->error)
	    {
	    	if(Yii::app()->request->isAjaxRequest)
	    		echo $error['message'];
	    	else{
				$clientScript = Yii::app()->getClientScript();
				$clientScript->registerCoreScript('jquery');
				$this->setSubPageTitle(Yii::t("message","Error! :code" ,array(":code"=> $error['code'])) );
				$this->layout = '//layouts/site.maintain';			
	        	$this->render('error', array('error'=>$error));
	    	}
	    }
		//$this->render('error');
	}
	
}