<div class="container-fluid">
	<!-- 面包屑导航 -->
	<ol class="breadcrumb">
	    <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('//mcampus/default/index'))?></li>
	    <li><?php echo CHtml::link(Yii::t('site','Routines'), array('//mcampus/default/index'))?></li>
	    <li><?php echo CHtml::link(Yii::t('curriculum','Curriculum'), array('//mcampus/curriculum/project'))?></li>
	    <li><?php echo CHtml::link(Yii::t('curriculum','Activity List'), array('//mcampus/curriculum/activity'))?></li>
	    <li class="active"><?php echo Yii::t('curriculum','Activity Info');?></li>
	</ol>
	<div class="row">
		<!-- 左侧菜单栏 -->
		<div class="col-md-2">
			<div class="list-group" id="classroom-status-list">
			    <?php foreach ($this->leftMenu as $key => $value) { ?>
			    <a href="<?php echo $this->createUrl($value[0]);?>" class="list-group-item status-filter <?php echo $this->getAction()->getId()==$value[0]?'active':''; ?>"><?php echo $value[1]; ?></a></li>
			    <?php } ?>
			</div>
		</div>
		<div class="col-md-10">
			<div class="panel panel-primary">
				<div class="panel-heading"><h4><?php echo Yii::t('curriculum','Activity Title').'：'.CHtml::encode($activity->cn_title).' '.CHtml::encode($activity->en_title);?>
					<?php 
						if (Yii::app()->user->checkAccess('o_E_Edu_Common') || $this->staff->uid == $activity->userid) {
							echo "<a style='float:right' class='btn btn-default btn-xs' href=".$this->createUrl('editactivity',array('aid'=>$activity->aid)).">".Yii::t('curriculum','Edit')."</a>";
							echo "<button onclick='deleteAct(this,{$activity->aid})' style='float:right' class='btn btn-default btn-xs' >".Yii::t('curriculum','Delete')."</button>";
						}
					?>
					</a></h4>
				</div>
				<div class="panel-body">
					<!-- 所属项目 -->
					<?php 
						echo '<h4>'.Yii::t('curriculum','Belong Projects').'</h4>';
						foreach ($belongPro as $key => $value) {
							echo '<a class="btn btn-primary btn-xs" target="_blank" href="'.$this->createUrl('showproject',array('pid'=>$value->pid)).'">'.$value->cn_title.' '.$value->en_title.'</a> ';
						}
					 ?>
				  	<!-- 目标智能 -->
					<h4><?php echo Yii::t('curriculum','Intelligence'); ?>:</h4>
					<?php 
						$intelligence = explode(',',$activity->intelligence);
						foreach ($intelligence as $key => $value) {
							echo "<span class='label label-primary'>{$diglossia['intelligence'][$value]}</span>  ";
						}
					 ?>
					 <!-- 适合年龄 -->
					<h4><?php echo Yii::t('curriculum','Age'); ?>:</h4>
					<?php 
						$age = explode(',',$activity->age);
						foreach ($age as $key => $value) {
							echo "<span class='label label-primary'>{$diglossia['age'][$value]}</span>  ";
						}
					 ?>
					 <!-- 学习领域 -->
					<h4><?php echo Yii::t('curriculum','Learning Domains');?>:</h4>
					<?php 
						$learning = explode(',',$activity->learning);
						foreach ($learning as $key => $value) {
							echo "<span class='label label-primary'>{$diglossia['learning'][$value]}</span>  ";
						}
					 ?>
					 <!-- 学习目标 -->
					<h4><?php echo Yii::t('curriculum','Cn Learning Objectives');?>:</h4>
					<pre><?php echo CHtml::encode($activity->cn_learning_obj); ?></pre>

					<h4><?php echo Yii::t('curriculum','En Learning Objectives');?>:</h4>
					<pre><?php echo CHtml::encode($activity->en_learning_obj); ?></pre>
					<!-- 材料 -->
					<h4><?php echo Yii::t('curriculum','Cn Materials and Equipment');?>:</h4>
					<pre><?php echo CHtml::encode($activity->materials_cn_content); ?></pre>

					<h4><?php echo Yii::t('curriculum','En Materials and Equipment');?>:</h4>
					<pre><?php echo CHtml::encode($activity->materials_en_content); ?></pre>
					<!-- 准备 -->
					<h4><?php echo Yii::t('curriculum','Preparation Cn Content');?>:</h4>
					<pre><?php echo CHtml::encode($activity->preparation_cn_content);?></pre>

					<h4><?php echo Yii::t('curriculum','Preparation En Content');?>:</h4>
					<pre><?php echo CHtml::encode($activity->preparation_en_content);?></pre>
					<!-- 步骤 -->
					<h4><?php echo Yii::t('curriculum','Presentation Cn Content');?>:</h4>
					<pre><?php echo CHtml::encode($activity->presentation_cn_content); ?></pre>

					<h4><?php echo Yii::t('curriculum','Presentation En Content');?>:</h4>
					<pre><?php echo CHtml::encode($activity->presentation_en_content); ?></pre>
					<!-- 提示 -->
					<h4><?php echo Yii::t('curriculum','Tips Cn Content');?>:</h4>
					<pre><?php echo CHtml::encode($activity->tips_cn_content); ?></pre>

					<h4><?php echo Yii::t('curriculum','Tips En Content');?>:</h4>
					<pre><?php echo CHtml::encode($activity->tips_en_content); ?></pre>
					<!-- 相关活动 -->
					<?php 
						if ($activity) {
							echo '<h4>'.Yii::t('curriculum','Related activities').'</h4>';
							foreach ($similarActivity as $key => $value) {
								echo "<label class='btn btn-primary btn-xs'><a style='color:#fff;' href='{$this->createUrl('showactivity',array('aid'=>$value->aid))}'>$value->cn_title $value->en_title </a></label> ";
							} 
						}
					?>
					<!-- 活动延展 -->
					<h4><?php echo Yii::t('curriculum','Extension Cn Content');?>:</h4>
					<pre><?php echo CHtml::encode($activity->extension_cn_content); ?></pre>

					<h4><?php echo Yii::t('curriculum','Extension En Content');?>:</h4>
					<pre><?php echo CHtml::encode($activity->extension_en_content); ?></pre>
					<!-- 标签 -->
					<h4><?php echo Yii::t('curriculum','Tags');?>:</h4>
					<?php foreach ($tag as $key => $value) {
                        echo "<label class='label label-success'>{$value->tag->tag_term}</label> ";
					} ?>
					<!-- 附件 -->
					<h4><?php echo Yii::t('curriculum','Attachments');?>:</h4>
					<?php $this->showAttachments($activity->attachments); ?>
				</div>
			</div>
		</div>
	</div>
</div>

<!-- 添加相关活动模态框 -->
<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
		<div class="modal-header">
			<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
			<h4 class="modal-title" id="myModalLabel"><?php echo Yii::t('curriculum','Related activities');?></h4>
		</div>
		<div class="modal-body">
			<div class="input-group">
			    <div class="col-md-6">
			        <select name="project" onchange="proselect(this)" class="form-control">
			            <option value=""><?php echo Yii::t('curriculum','Choose'); ?></option>
			            <?php foreach ($projects as $key => $value) {
			            	echo "<option value='{$value->pid}'>{$value->cn_title},{$value->en_title}</option>";
			            } ?>
			        </select> 
				</div>
			    <div class="col-md-6">
			        <select id="actselect" name="activity" class="form-control">
			        </select> 
				</div>
			</div>
		</div>
		<div class="modal-footer">
			<button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('curriculum','Close');?></button>
			<button type="button" class="btn btn-primary" onclick="save(this)"><?php echo Yii::t('curriculum','Save');?></button>
      </div>
			<h3 id="info" class="text-center text-"></h3> 
    </div>
  </div>
</div>


<script>
	//二级联动菜单
	function proselect (proselect) {
		var pid = $(proselect).val();
		console.log(pid);
		$.ajax({
			url:'<?php echo $this->createUrl('similaractivity'); ?>',
			data:{type : 'search',pid : pid},
			type:'post',
			dataType:'json',
			success : function (data) {
				console.log(data);
				$('#actselect').empty();
				for(x in data.activity){
					// console.log(x);
					// console.log(data.activity[x]);
					$('#actselect').prepend("<option value="+x+" >"+data.activity[x]+"</option>");
				}
			},
			error : function (){
			}
		});
	}
	//搜索活动
	function search (btnsearch) {
		var search = $('#search').val();
		// console.log(search);
		$.ajax({
			url:'<?php echo $this->createUrl('similaractivity'); ?>',
			data:{type : 'search',search : search},
			type:'post',
			dataType:'json',
			success : function (data) {
				$('#list').empty();
				for(x in data.activity){
					// console.log(x);
					// console.log(data.activity[x]);
					$('#list').prepend("<input type='checkbox' name='list[]' value="+x+" />"+data.activity[x]+"<br>");
				}
			},
			error : function (){
			}
		});
	}
	//添加相关
	function save (btnsave) {
		var sc_id = $('#actselect').val();
		var bc_id = <?php echo $activity->aid; ?>;
		// console.log(sc_id);
		$.ajax({
			url:'<?php echo $this->createUrl('similaractivity'); ?>',
			data:{type : 'save',sc_id : sc_id,bc_id : bc_id},
			type:'post',
			dataType:'json',
			success : function (data) {
				if (data.msg=='success') {
					console.log(data.msg);
					$('#info').text('更新成功');
					window.location.reload();
				}else{
					$('#info').text(data.info);
				}
			},
			error : function (data){
				$('#info').text(data.info);
			}
		});
	}
	//删除相关活动
	function remove (btnremove,bc_id,sc_id) {
		if (confirm("你确定要删除吗")) {
			$.ajax({
				url:'<?php echo $this->createUrl('deletesimilaractivity') ?>',
				data:{bc_id : bc_id,sc_id : sc_id},
				type:'post',
				dataType:'json',
				success : function (data) {
					if (data.msg=='success') {
						$(btnremove).parent().remove();
					}
				},
				error : function (data) {
				
				}
			});	
		};	
	}
	//删除活动
	function deleteAct (deletebtn,aid) {
	    if (confirm('确定要删除吗？')) {
	        $.ajax( {    
	            url:'<?php echo $this->createUrl('deleteActivity'); ?>',    
	            data:{aid : aid},    
	            type:'post',    
	            dataType:'json',    
	            success:function(data) {    
	                if(data.msg == 'success'){    
	                    // $(deletebtn).remove();
	                    window.location.href='<?php echo $this->createUrl('activity'); ?>'; 
	                }else{    
	                    alert(data.msg);
	                    console.log(data);    
	                }    
	             },    
	            error : function() {    
	                console.log(data.error);    
	            }    
	        });  
	    }
	}
</script>

