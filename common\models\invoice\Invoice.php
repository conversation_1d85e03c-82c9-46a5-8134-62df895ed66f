<?php

/**
 * This is the model class for table "ivy_invoice_invoice".
 *
 * The followings are the available columns in table 'ivy_invoice_invoice':
 * @property integer $invoice_id
 * @property integer $calendar_id
 * @property double $amount
 * @property double $original_amount
 * @property double $nodiscount_amount
 * @property string $schoolid
 * @property integer $classid
 * @property integer $childid
 * @property string $payment_type
 * @property integer $afterschool_id
 * @property string $inout
 * @property integer $fee_type
 * @property integer $startdate
 * @property integer $enddate
 * @property string $installment
 * @property integer $duetime
 * @property integer $gen_latefees_date
 * @property string $title
 * @property string $memo
 * @property string $child_service_info
 * @property integer $discount_id
 * @property integer $agreeddate
 * @property string $fee_program
 * @property integer $userid
 * @property integer $timestamp
 * @property integer $send_timestamp
 * @property integer $last_paid_timestamp
 * @property integer $sender
 * @property integer $status
 * @property integer $latepay_id
 * @property integer $fee_other_id
 */
class Invoice extends CActiveRecord {
    /*
     * 暂时只是打印账单的时候用到
     */

    // 付款类型的名称
    public $paymentTypeTitle = null;
    // 应付费金额
    public $dueAmount = 0;
    //折扣名称
    public $discountTitle = '';
    //折扣 百分比
    public $discountPercent = '100.000％';
    // 账单有效区间 duration
    public $durDate = '';
    // 到期日期
    public $dueDate = '';
    // 押金金额
    public $depositAmount = 0;

    public $amountid = '';

    public $connectedInvoices = null;

    //亲子班课外活动节数
    public $project_num = 0;

    public $exist = array(); // array('create'=>true, 'model'=>null) 保存前判断

    const STATS_UNPAID = 10;            //未付款
    const STATS_PAID = 20;              //款付清
    const STATS_PARTIALLY_PAID = 30;    //部分付清
    const STATS_AWAITING_DISCOUNT = 40; //等待折扣审核
    const STATS_AWAITING_CHANGE = 77;   //等待更改审核
    const STATS_CHANGE_ARCHIVED = 88;   //账单更改历史
    const STATS_CANCELLED = 99;         //作废
    
    const INVOICE_INOUT_IN = 'in'; //收费
    const INVOICE_INOUT_OUT = 'out';  //退费

    public $payDate; # 时间戳，目前DayStart才用此属性

    /**
     * Returns the static model of the specified AR class.
     * @return Invoice the static model class
     */
    public static function model($className = __CLASS__) {
        return parent::model($className);
    }

    /**
     * @return string the associated database table name
     */
    public function tableName() {
        return 'ivy_invoice_invoice';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules() {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('title, calendar_id, amount, original_amount, schoolid, childid, payment_type, fee_type, userid, timestamp', 'required'),
            array('calendar_id, classid, childid, afterschool_id, gen_latefees_date, discount_id, agreeddate, userid, timestamp, send_timestamp, last_paid_timestamp, sender, status, latepay_id, fee_other_id, receivable_status, flag', 'numerical', 'integerOnly' => true),
            array('original_amount, nodiscount_amount', 'numerical'),
            array('schoolid, payment_type, fee_program', 'length', 'max' => 255),
            array('inout', 'length', 'max' => 3),
            array('installment', 'length', 'max' => 100),
            array('title', 'length', 'max' => 500),
            array('startdate, enddate', 'numerical', 'integerOnly' => true, 'except'=>'nodate'),
            array('apportion_status, memo, child_service_info, duetime, startdate, enddate, exist', 'safe'),
            // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            array('invoice_id, calendar_id, amount, original_amount, nodiscount_amount, schoolid, classid, childid, payment_type, afterschool_id, inout, fee_type, startdate, enddate, installment, duetime, gen_latefees_date, title, memo, child_service_info, discount_id, agreeddate, fee_program, userid, timestamp, send_timestamp, last_paid_timestamp, sender, status, latepay_id, fee_other_id, receivable_status, flag', 'safe', 'on' => 'search'),
            array('duetime', 'required', 'on'=>'daystar, nodate'),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations() {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
            'invoiceTransaction' => array(self::HAS_MANY, 'InvoiceTransaction', 'invoice_id'),
            'ChildRefund' => array(self::HAS_MANY, 'InvoiceChildRefund', 'on_invoice_id','select'=>'startdate,enddate'),
            'calendar'=> array(self::BELONGS_TO, 'Calendar', array("calendar_id"=>"yid")),
            'classInfo'=> array(self::BELONGS_TO, 'IvyClass', array("classid"=>"classid")),
            'childInfo' => array(self::BELONGS_TO, 'ChildProfileBasic', 'childid', 'together' => false, 'with' => 'homeaddr'),
            'childprofile' => array(self::BELONGS_TO, 'ChildProfileBasic', 'childid'),
            'discount' => array(self::BELONGS_TO, 'DiscountSchool', 'discount_id', 'with'=>'discountTitle'),
            'workflowRefund' => array(self::HAS_MANY, 'InvoiceChildRefund', array('on_invoice_id'=>'invoice_id'),'together'=>false),
            'paidSum' => array(self::STAT, 'InvoiceTransaction', 'invoice_id', 'select'=>"sum(`amount`*if(`inout`='in',1,-1))",),
            'feeOther' => array(self::BELONGS_TO, 'FeemgtFeeOther', 'fee_other_id', 'select'=>'amount,cn_title,en_title'),
            'childServiceInfo' => array(self::HAS_MANY, 'ChildServiceInfo', 'invoice_id'),
            'school' => array(self::BELONGS_TO, 'Branch', 'schoolid'),
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels() {
        return array(
            'invoice_id' => 'Invoice',
            'calendar_id' => Yii::t("invoice","School Calendar"),
            'amount' => Yii::t("payment", 'Invoice Amount'),
            'original_amount' => Yii::t("payment", 'Original Amount'),
            'nodiscount_amount' => 'Nodiscount Amount',
            'schoolid' => 'Schoolid',
            'classid' => Yii::t("invoice","School / Class"),
            'childid' => 'Childid',
            'payment_type' => Yii::t("payment", 'Payment Type'),
            'afterschool_id' => 'Afterschool',
            'inout' => 'Inout',
            'fee_type' => 'Fee Type',
            'startdate' => Yii::t("payment", 'Start Date'),
            'enddate' => Yii::t("payment", 'End Date'),
            'installment' => 'Installment',
            'duetime' => Yii::t("payment", 'Due Date'),
            'gen_latefees_date' => 'Gen Latefees Date',
            'title' => Yii::t("labels", 'Invoice Title'),
            'memo' => Yii::t("payment", 'Memo'),
            'child_service_info' => 'Child Service Info',
            'discount_id' => 'Discount',
            'agreeddate' => 'Agreeddate',
            'fee_program' => 'Fee Program',
            'userid' => 'Userid',
            'timestamp' => Yii::t("labels", 'Paid Date'),
            'send_timestamp' => 'Send Timestamp',
            'last_paid_timestamp' => Yii::t("labels", 'Paid Date'),
            'sender' => 'Sender',
            'status' => Yii::t("labels", 'Payment Status'),
            'latepay_id' => 'Latepay',
            'fee_other_id' => 'Fee Other',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search() {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('invoice_id', $this->invoice_id);
        $criteria->compare('calendar_id', $this->calendar_id);
        $criteria->compare('amount', $this->amount);
        $criteria->compare('original_amount', $this->original_amount);
        $criteria->compare('nodiscount_amount', $this->nodiscount_amount);
        $criteria->compare('schoolid', $this->schoolid, true);
        $criteria->compare('classid', $this->classid);
        $criteria->compare('childid', $this->childid);
        $criteria->compare('payment_type', $this->payment_type, true);
        $criteria->compare('afterschool_id', $this->afterschool_id);
        $criteria->compare('inout', $this->inout, true);
        $criteria->compare('fee_type', $this->fee_type);
        $criteria->compare('startdate', $this->startdate);
        $criteria->compare('enddate', $this->enddate);
        $criteria->compare('installment', $this->installment, true);
        $criteria->compare('duetime', $this->duetime);
        $criteria->compare('gen_latefees_date', $this->gen_latefees_date);
        $criteria->compare('title', $this->title, true);
        $criteria->compare('memo', $this->memo, true);
        $criteria->compare('child_service_info', $this->child_service_info, true);
        $criteria->compare('discount_id', $this->discount_id);
        $criteria->compare('agreeddate', $this->agreeddate);
        $criteria->compare('fee_program', $this->fee_program, true);
        $criteria->compare('userid', $this->userid);
        $criteria->compare('timestamp', $this->timestamp);
        $criteria->compare('send_timestamp', $this->send_timestamp);
        $criteria->compare('last_paid_timestamp', $this->last_paid_timestamp);
        $criteria->compare('sender', $this->sender);
        $criteria->compare('status', $this->status);
        $criteria->compare('latepay_id', $this->latepay_id);
        $criteria->compare('fee_other_id', $this->fee_other_id);

        return new CActiveDataProvider($this, array(
                    'criteria' => $criteria,
                ));
    }

    /**
     * 调收费类型全称
     * @param string $key
     * @return string
     * <AUTHOR>
     */
    public static function feeType($key) {
        $CfgFeeType = Mims::LoadConfig('CfgFeeType');
        $HtoolKits = Mims::LoadHelper('HtoolKits');
        $title = $HtoolKits->getContentByLang($CfgFeeType[$key]["cn"], $CfgFeeType[$key]["en"]);
        return $title;
    }

    /**
     * 格式化金额
     * @param string $inout
     * @param double $amount
     * @param boolean $format
     * @return string
     * <AUTHOR>
     */
    public static function formatAmount($inout = null, $amount, $format = true) {
        $fit = "";
        $fAmount = 0;
        if (!empty($inout) && strtolower($inout) == "out") {
            $fit = "-";
        }
        if ($format === true) {
            $amount = number_format($amount, 2);
        }
        $fAmount = $fit . $amount;

        return $fAmount;
    }

    public static function formatInout($inout) {
        $inout = strtolower($inout);
        $HtoolKits = Mims::LoadHelper('HtoolKits');
        if ($inout == 'in') {
            $inout = $HtoolKits->getContentByLang('转到个人帐户', 'To Credit');
        } elseif ($inout == 'out') {
            $inout = $HtoolKits->getContentByLang('使用个人帐户', 'Use Credit');
        }
        return $inout;
    }

    //howard
    //called on rendering the column for each row
    /**
     * 渲染支付类型 这么写就为了方便 gridview中进行调用
     * @param object $data
     * @param int $row
     * @return string
     * <AUTHOR>
     * @time 2012-8-10
     */
    public function renderPaymentType($data, $row) {
        $theCellValue = '';
        Mims::LoadHelper('HtoolKits');
        $invoiceConfig = Mims::LoadConfig('CfgInvoice');
        if (isset($invoiceConfig['fee_type'][$data->payment_type])) {
            $y = $invoiceConfig['fee_type'][$data->payment_type];
            $theCellValue = HtoolKits::getContentByLang($y['cn'], $y['en']);
        }
        return $theCellValue;
    }

    //called on rendering the column for each row
    /**
     * 渲染支付状态
     * @param unknown_type $data
     * @param unknown_type $row
     * @return string
     * <AUTHOR>
     * @time 2012-8-10
     */
    public function renderStatus($data, $row) {

        $theCellValue = '';
        Mims::LoadHelper('HtoolKits');
        $invoiceConfig = Mims::LoadConfig('CfgInvoice');
        if (isset($invoiceConfig['invoice_state'][$data->status])) {
            $y = $invoiceConfig['invoice_state'][$data->status];
            $theCellValue = HtoolKits::getContentByLang($y['cn'], $y['en']);
        }
        return $theCellValue;
    }

    //called on rendering the column for each row
    /**
     * 渲染 已付金额
     * @param unknown_type $data
     * @param unknown_type $row
     * @return Ambigous <string, mixed>
     * <AUTHOR>
     * @time 2012-8-10
     */
    public function renderPaidAmount($data, $row) {
        $theCellValue = 0;
        if (!empty($data->invoiceTransaction)) {
            foreach ($data->invoiceTransaction as $ti) {
                $theCellValue += $ti->amount;
            }
        }
        return floatval($theCellValue);
    }

    /**
     * 格式化 渲染的已付金额
     * @param unknown_type $data
     * @param unknown_type $row
     * @return Ambigous <string, mixed>
     * <AUTHOR>
     * @time 2012-8-10
     */
    public function renderPaidAmountF($data, $row) {
        $nf = new CNumberFormatter('zh-cn');
       // $theCellValue = $this->renderPaidAmount($data, $row);
        $theCellValue = $data->paidSum;
        return $nf->format('#,##0.00', $theCellValue);
    }

    //called on rendering the column for each row
    /**
     * 渲染应付金额
     * @param unknown_type $data
     * @param unknown_type $row
     * @return number
     * <AUTHOR>
     * @time 2012-8-10
     */
    public function renderDueAmount($data, $row) {
        $theCellValue = $data->amount;
//        if (!empty($data->invoiceTransaction)) {
//            foreach ($data->invoiceTransaction as $ti) {
//                $theCellValue -= $ti->amount;
//            }
//        }
        $theCellValue -= $data->paidSum;
        return round($theCellValue, 2);
    }

    public function renderDueAmountHtml($data, $row) {
        $nf = new CNumberFormatter('zh-cn');
        $da = $this->renderDueAmount($data, $row);
        return $nf->format('#,##0.00', $da) . CHtml::hiddenField('due_amount[' . $data->invoice_id . ']', $da, array('id' => "due_amount_" . $data->invoice_id)) . CHtml::hiddenField('school[' . $data->invoice_id . ']', $data->schoolid, array('id' => "school_" . $data->invoice_id));
    }

    /**
     * 得到总的应付金额
     * @param $datas Object of this model
     * @return number
     * <AUTHOR>
     * @time 2012-7-18
     */
    public function getTotalDueAmount($datas) {
        $tda = 0;
        if (!empty($datas)) {
            foreach ($datas as $data) {
                $tda += $this->renderDueAmount($data, 0);
            }
        }
        return $tda;
    }

    /**
     * 得到某一账单使用押金的金额
     * @param object $invoice invoice对象
     * @param int $invoice_id
     * @ 10 未付款 20 款付清 30 部分付清
     * @return number
     * <AUTHOR>
     * @time 2012-8-10
     */
    public function getDepositAmount($invoice = null, $invoice_id = null) {

        $deposit = 0;

        if (empty($invoice)) {
            if ($invoice_id) {
                $invoice = $this->findByPk($invoice_id);
            }
        }

        if (!empty($invoice)) {

            if ($invoice->status == 10) {

                $ctd_info = TemporaryDeposit::model()->findByPk($invoice->invoice_id);

                if (!empty($ctd_info->amount)) {
                    $deposit = $ctd_info->amount;
                }
            } else if ($invoice->status == 30) {

                $trans = $invoice->invoiceTransaction;

                $tran_ids = null;

                if (!empty($trans)) {
                    foreach ($trans as $tran) {
                        $tran_ids[$tran->id] = $tran->id;
                    }
                }
                if (!empty($tran_ids)) {

                    $cdh_cri = new CDbCriteria();
                    $cdh_cri->compare('`inout`', 'out');
                    $cdh_cri->compare('tran_id', $tran_ids);

                    $cdh_info = DepositHistory::model()->findAll($cdh_cri);

                    if (!empty($cdh_info)) {
                        foreach ($cdh_info as $cdh) {
                            $deposit += floatval($cdh->amount);
                        }
                    }
                }
            }
        }
        return $deposit;
    }


    public static function showVoucher($data,&$invoice_id = "",&$voucherAmount=0){

        Yii::import('application.components.policy.*');
        Yii::import('common.models.calendar.Calendar');
        Yii::import('common.models.invoice.InvoiceTransaction');
        $paymentType = $data->payment_type;
        $schoolId = $data->schoolid;
        $calendarId = $data->calendar_id;
        $childId = $data->childid;
        //一级判断
//        if ($paymentType != 'registration'){ 之前只给OE用注册费的抵用
        if ($paymentType != 'tuition'){ # 给YJ学费的保教费减免
            return FALSE;
        }
        if ($data->classInfo->classtype != 'k') {
            return FALSE;
        }

        $startYear = Calendar::model()->getCalendarInfo($calendarId,'startyear');

        $feeConfig = new IvyPolicy(OA::POLICY_PAY, $startYear, $schoolId);

        $voucherAmount = isset($feeConfig->configs[VOUCHER_AMOUNT]) ? $feeConfig->configs[VOUCHER_AMOUNT]:0;

        //二级判断
        if ($voucherAmount ==0){
            return FALSE;
        }

        //三级判断
//         $count = InvoiceTransaction::model()->count('childid=:childid and transactiontype=:transactiontype and calendar_id=:calendar_id ',array(
// //            ':childid'=>$childId,':transactiontype'=>InvoiceTransaction::TYPE_VOUCHER
//             ':childid'=>$childId,':transactiontype'=>InvoiceTransaction::TYPE_EDUFEE,'calendar_id' => $calendarId
//         ));
        $sql = "SELECT count(*) as num from ivy_invoice_transaction as t LEFT JOIN ivy_invoice_child_refund as r ON t.invoice_id = r.on_invoice_id WHERE t.childid={$childId} AND calendar_id={$calendarId} AND t.transactiontype = ".InvoiceTransaction::TYPE_EDUFEE." AND r.invoice_id IS NULL";
        $res = Yii::app()->db->createCommand($sql)->queryAll();
        $count = 0;
        if (isset($res[0])) {
            $count = $res['num'];
        }

        if ($count > 1){
            return FALSE;
        }

        //四级判断
        $vouchers_count = "";
        if($invoice_id){
            $vouchers_count = InvoiceTransaction::model()->count('invoice_id=:invoice_id  and transactiontype=:transactiontype',array(
                ':invoice_id'=>$invoice_id,':transactiontype'=>InvoiceTransaction::TYPE_EDUFEE
            ));
        }

        if($vouchers_count){
            return FALSE;
        }

        return TRUE;
    }

    static public function getStatusTxt($id = 0){
        $ret = array(
            self::STATS_UNPAID => 'UnPaid',
            self::STATS_PAID =>'Paid',
            self::STATS_PARTIALLY_PAID => 'Partially Paid',
            self::STATS_AWAITING_DISCOUNT => 'Awaiting for Discount Approval',
            self::STATS_AWAITING_CHANGE =>'Awaiting for Change Approval',
            self::STATS_CHANGE_ARCHIVED => 'Changed Archive',
            self::STATS_CANCELLED => 'Cancelled'
        );
        return ($id) ? $ret[$id] : $ret;
    }

    public function getBatchInvoicesData($schoolid, $month, $paymentType)
    {
        $startdate = strtotime($month.'01');
        $enddate = mktime(0, 0, 0, date('m', $startdate)+1, 1, date('Y', $startdate)) - 86400;
        $status = $paymentType == 'preschool_subsidy' ? self::STATS_UNPAID : self::STATS_PAID;
        $criteria = new CDbCriteria();
        $criteria->compare('t.payment_type', $paymentType);
        $criteria->compare('t.status', $status);
        $criteria->compare('t.schoolid', $schoolid);
        $criteria->addCondition('(t.startdate >= '.$startdate.' and t.enddate <='.$enddate.')');
        return $this->model()->findAll($criteria);
    }
    
    /*
     * 查询某上孩子的帐单统计根据帐单的状态
     * @param   childid
     * @param   status
     * @return  number
     */
    public function getInvoiceCountByStatus($childid, $status){
        $criter = new CDbCriteria();
        $criter->compare('childid', $childid);
        $criter->compare('status', $status);
        return $this->model()->count($criter);
    }

    public function genWXQrcode()
    {
        $url = '';
        if(in_array($this->status, array(10, 30))){
            Yii::import('common.extensions.wxPay.*');
            Yii::import('common.models.wxpay.*');


            $wxpayInfo = CommonUtils::LoadConfig('CfgWxPayGlobal');

            if(isset($wxpayInfo[$this->schoolid]['number_code'])){
                $number_code = $wxpayInfo[$this->schoolid]['number_code'];
                $wechatPayOrder = new WechatPayOrder();
                $wechatPayOrder->orderid = $wechatPayOrder->genOrderID($number_code.$this->invoice_id);
                $wechatPayOrder->payable_amount = ($this->amount - $this->paidSum);
                $wechatPayOrder->fact_amount = 0;
                $wechatPayOrder->schoolid = $this->schoolid;
                $wechatPayOrder->childid = $this->childid;
                $wechatPayOrder->type = 'NATIVE';
                $wechatPayOrder->status = 0;
                $wechatPayOrder->settlement_status = 0;
                $wechatPayOrder->order_time = time();
                $wechatPayOrder->update_timestamp = time();
                $wechatPayOrder->uid = Yii::app()->user->id;
                if($wechatPayOrder->save()){
                    $wechatPayOrderItem = new WechatPayOrderItem();
                    $wechatPayOrderItem->orderid = $wechatPayOrder->orderid;
                    $wechatPayOrderItem->invoice_id = $this->invoice_id;
                    $wechatPayOrderItem->amount = $wechatPayOrder->payable_amount;
                    if($wechatPayOrderItem->save()){
                        $nativePay = new NativePay();
                        $nativePay->cfg = $wxpayInfo[$this->schoolid];
                        $nativePay->values['body'] = $this->title;
                        $nativePay->values['product_id'] = $this->invoice_id;
                        $nativePay->values['notify_url'] = 'http://apps.ivyonline.cn/wechatPay/wechatPay';
                        $nativePay->values['out_trade_no'] = $wechatPayOrder->orderid;
                        $nativePay->values['total_fee'] = $nativePay->transformMoney($wechatPayOrder->payable_amount);
                        $nativePay->values['attach'] = $this->schoolid;
                        $nativePay->init();
                        $rs = $nativePay->createQRcode();
                        $url = $rs['code_url'];
                    }
                }
            }
        }
        return $url;
    }

    public function getDSInterval()
    {
        $data = date('Y-m-d',$this->startdate)."/".date('Y-m-d',$this->enddate);
        if (in_array($this->schoolid, array('BJ_DS', 'BJ_SLT'))) {
            if ($this->fee_type == 2) {
                $data = Yii::t('payment', 'Semester');
            } elseif ($this->fee_type == 1) {
                $data = Yii::t('payment', 'Annual');;
            }
        }
        return $data;
    }
}