<?php

class OaController extends ProtectedController
{
	public $childId = 0;
	public $journal = false;
	public $key1 = '!ivyOnline';
	public $key2 = 'journal';
	
	public function parseInput($str){
		$str = base64_decode($str);
		$params = explode("+", $str);
		$this->childId = intval(isset($params['0']) ? str_replace($this->key1,"",$params['0']) : 0 );
		$this->journal = isset($params['1'])? str_replace($this->key2,"",$params['1']) : false;		
	}
	//public function actionIndex($id)
	//{
	//	$this->parseInput($id);
	//	$child = ChildProfileBasic::model()->findByPk($this->childId);
	//	if($child != null){
	//		$this->layout = "//layouts/frameset2";
	//		Yii::app()->params["checkJournal"] = $this->journal;
	//		Yii::app()->params["ccid"] = $child->childid;
	//		$this->render('index',array("child"=>$child));
	//	}else{
	//		$this->forward('//backend/default');
	//	}
	//	Yii::app()->end();
	//}

	public function actionIndex()
	{
		$this->layout = "//layouts/full_column1";
		
        Yii::app()->clientScript->registerScriptFile(Yii::app()->request->baseUrl.'/js/dev/head.js?'.Yii::app()->params['assetsVersion']);
        Yii::app()->clientScript->registerCoreScript('jquery');
        Yii::app()->clientScript->registerScriptFile(Yii::app()->request->baseUrl.'/js/dev/pages/admin/common/common.js?'.Yii::app()->params['assetsVersion']);
		
        $menus = OA::genMenu();        
		$this->render('overall', array('menus'=>CJSON::encode($menus)));		
	}

	public function actionHome()
	{
		$this->layout = "//layouts/blank";
		$this->render('home');
	}
	
	public function actionToggle()
	{
		$css = Yii::app()->theme->baseUrl . '/css/toggle.css';
echo <<<OT
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<link rel="stylesheet" type="text/css" href="$css" />
</head>
<body>
OT;
		echo $this->renderPartial('toggle', null, true);

echo <<<ET
</body>
</html>
ET;
	}
	
	public function actionLogout()
	{
		Yii::app()->user->logout();
		$this->render("logout", array("url"=>Yii::app()->user->loginUrl));
		Yii::app()->end();
	}
	//
	//public function actionDefault()
	//{
	//	$this->layout = "//layouts/blank";
	//	$this->render('default');
	//}
	// Uncomment the following methods and override them if needed
	/*
	public function filters()
	{
		// return the filter configuration for this controller, e.g.:
		return array(
			'inlineFilterName',
			array(
				'class'=>'path.to.FilterClass',
				'propertyName'=>'propertyValue',
			),
		);
	}

	public function actions()
	{
		// return external action classes, e.g.:
		return array(
			'action1'=>'path.to.ActionClass',
			'action2'=>array(
				'class'=>'path.to.AnotherActionClass',
				'propertyName'=>'propertyValue',
			),
		);
	}
	*/
}