<style>
    @media print {
          /*table {page-break-inside: avoid;}*/
      }
</style>
<table class="table table-bordered">
    <thead>
    <tr>
        <th colspan="<?php echo count($schoolDay) + 5; ?>">
            <h4 class="text-center" style="padding-top: 10px">每月学生考勤报告</h4>
            <h5><?php echo $attendModel->class->title ?><span class="pull-right"><?php echo $attendModel->month ?> </span></h5>
        </th>
    </tr>
    </thead>
    <tr>
        <td width="10">#</td>
        <td width="100">姓名</td>
        <?php foreach ($schoolDay as $k => $item) { ?>
            <td width="40"><?php echo $item . " <br>" . date('D', $k) ?></td>
        <?php } ?>
        <td width="10">迟到</td>
        <td width="10">缺勤</td>
        <td width="10">出勤</td>
    </tr>
    <?php
    $type = AttendStatistics::getConfig();
    $signTotalPeople = array();
    $lateTotalPeople = array();
    $sickVacationTotalPeople = array();
    $thingvacationTotalPeople = array();
    $num = 1;
    foreach (json_decode($attendModel->data, true) as $chilid => $data) {
        ?>
        <tr>
            <td><?php echo $num; $num++; ?></td>
            <td><?php echo isset($childModel[$chilid]) ? $childModel[$chilid]->getChildName() : ''; ?></td>
            <?php
            $signNum = 0;
            $lateNum = 0;
            $sickVacationNum = 0;
            $thingvacationNum = 0;
            foreach ($schoolDay as $key => $item):
                $html = "";
                foreach ($data as $keys => $dataitems) {
                    if ($key <= $dataitems['endTime'] && $key >= $dataitems['startTime']) {
                        $html = $type[$dataitems['type']];
                        if ($dataitems['type'] == 1) {
                            $signNum++;
                            $signTotalPeople[$key] += 1;
                        } else if ($dataitems['type'] == 10) {
                            $sickVacationTotalPeople[$key] += 1;
                            $sickVacationNum++;
                        } else if ($dataitems['type'] == 40) {
                            $lateTotalPeople[$key] += 1;
                            $lateNum++;
                        } else if ($dataitems['type'] == 60) {
                            $lateTotalPeople[$key] += 1;
                            $lateNum++;
                            $signTotalPeople[$key] += 1;
                            $signNum++;
                        } else {
                            $thingvacationTotalPeople[$key] += 1;
                            $thingvacationNum++;
                        }
                    }
                }
                ?>

                <?php
                if($html){
                    echo $html;
                }else{
                    echo '<td></td>';
                }
                ?>
            <?php endforeach; ?>
            <td><?php echo $lateNum; ?></td>
            <td><?php echo $sickVacationNum + $thingvacationNum; ?></td>
            <td><?php echo $signNum; ?></td>
        </tr>
    <?php } ?>
    <tr>
        <td></td>
        <td>迟到人数</td>
        <?php
        $lateTotal = 0;
        foreach ($schoolDay as $timeKey => $itemVal) { ?>
            <td><?php
                if ($lateTotalPeople && $lateTotalPeople[$timeKey]) {
                    echo $lateTotalPeople[$timeKey];
                    $lateTotal = $lateTotal + $lateTotalPeople[$timeKey];
                } elseif (isset($signTotalPeople[$timeKey])) {
                    echo 0;
                }
                ?></td>
        <?php } ?>
        <td colspan="3"><?php echo $lateTotal; ?></td>
    </tr>
    <tr>
        <td></td>
        <td>出勤人数</td>
        <?php $signTotal = 0; foreach ($schoolDay as $timeKey => $itemVal) { ?>
            <td><?php
                if ($signTotalPeople && $signTotalPeople[$timeKey]) {
                    echo $signTotalPeople[$timeKey];
                    $signTotal += $signTotalPeople[$timeKey];
                }
                ?></td>
        <?php } ?>
        <td colspan="3"><?php echo $signTotal; ?></td>
    </tr>
    <tr>
        <td></td>
        <td>病假人数</td>
        <?php $sickTotal = 0; foreach ($schoolDay as $timeKey => $itemVal) { ?>
            <td><?php
                if ($sickVacationTotalPeople && $sickVacationTotalPeople[$timeKey]) {
                    echo $sickVacationTotalPeople[$timeKey];
                    $sickTotal += $sickVacationTotalPeople[$timeKey];
                } elseif (isset($signTotalPeople[$timeKey])) {
                    echo 0;
                }
                ?></td>
        <?php } ?>
        <td colspan="3"><?php echo $sickTotal; ?></td>
    </tr>
    <tr>
        <td></td>
        <td>事假人数</td>
        <?php $total = 0; foreach ($schoolDay as $timeKey => $itemVal) { ?>
            <td><?php
                if ($thingvacationTotalPeople && $thingvacationTotalPeople[$timeKey]) {
                    echo $thingvacationTotalPeople[$timeKey];
                    $total += $thingvacationTotalPeople[$timeKey];
                } elseif (isset($signTotalPeople[$timeKey])) {
                    echo 0;
                }
                ?></td>
        <?php } ?>
        <td colspan="3"><?php echo $total; ?></td>
    </tr>
    <tr>
        <td></td>
        <td>长假人数</td>
        <?php foreach ($schoolDay as $timeKey => $itemVal) { ?>
            <td></td>
        <?php } ?>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <tr>
        <td></td>
        <td>老师签字</td>
        <?php foreach ($schoolDay as $timeKey => $itemVal) { ?>
            <td></td>
        <?php } ?>
        <td></td>
        <td></td>
        <td></td>
    </tr>
</table>
<style>
    
    .table th{
        vertical-align: middle !important
    }
    .present{
        font-family:cursive;
        vertical-align: middle;
        color:#666
    }
    .sick{
        font-family:SimSun;
        vertical-align: middle;
        color: #25A4A8;
        background: #E4FAFA
    }
    .personal{
        font-family:SimSun;
        vertical-align: middle;
        color: #4D88D2;
        background: #e5f1ff;
    }
    .tardy{
        font-family:SimSun;
        vertical-align: middle;
        color: #ED6A0C;
        background: #FDF0E6
    }
    .tardy span{
        font-family:cursive;
    }
</style>