<style>
    [v-cloak] {
        display: none;
    }

    #datatable tr td {
        height: 51px;
    }

    .boxSize {
        width: 830px;
        height: 400px;
        margin: auto
    }
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('/mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', '返校调查列表'), array('/mcampus/surveyReturnAnswer/index')) ?></li>
        <li><?php echo Yii::t('site', '孩子填写结果列表'); ?></li>
    </ol>
    <div id='schoolTemplate' v-cloak>
        <div class="panel panel-default">
            <div class="panel-heading">
                <div class='pull-left'>{{tittle}}
                    <span class="badge">{{childInfo.length}}</span>
                </div>
                <a href="#" class="btn btn-primary btn-xs pull-right" @click="btn_export()">
                    <?php echo Yii::t('admissions', '导出Excel'); ?></a>
                <a href="#" class="btn btn-primary btn-xs pull-right mr15" :disabled="clickBtn" @click="view_statistics()"><?php echo Yii::t('admissions', '查看统计'); ?></a>
                <br />
            </div>
            <div class="panel-body">
                <div class="progress">
                    <div class="progress-bar progress-bar-success" :style="'width:'+Number(answer/childInfo.length*100).toFixed(2)+'%;min-width:25em;color:#fff;'">
                        已填写（{{answer}}/{{childInfo.length}}）{{Number(answer/childInfo.length*100).toFixed(2)}}%
                        <span v-if='is_positioning==1'>已支付（{{payNum}}）</span>

                    </div>
                    <!-- <div class="progress-bar progress-bar-warning progress-bar-striped" :style="'width:' +Number(payNum/childInfo.length*100).toFixed(2) +'%;min-width:15em;color:#fff;background-color:5cb85c'">
                        已支付（{{payNum}}/{{childInfo.length}}）{{Number(payNum/childInfo.length*100).toFixed(2)}}%
                    </div> -->
                </div>
                <div class="clearfix">
                    <div class="pull-left"></div>
                    <button type="button" class="btn btn-primary btn-xs" @click='reload()' :disabled="disabled"><?php echo Yii::t('site', '同步需填写人数'); ?></button>
                    <button type="button" class="btn btn-warning btn-xs" @click='showBlockedList()' :disabled="disabled"><?php echo Yii::t('site', '屏蔽名单'); ?></button>
                </div>
                <div class="form-inline pull-right">
                    <div class="form-group">
                        <select id="classFilter" class="form-control" v-model="class_id">
                            <option value="">所有班级</option>
                            <option v-for="(item, index) in classList" :value="item.class_id">{{ item.class_title }}</option>
                        </select>
                    </div>
                    <div class="checkbox">
                        <label>
                            <input type="checkbox" value="1" v-model="noAnswer"> 只看未填
                        </label>
                    </div>
                </div>
            </div>
        </div>
        <table class='table table-hover' id='datatable'>
            <thead>
                <tr style='height:51px'>
                    <th width='40'>#</th>
                    <th width='80'><?php echo Yii::t('reg', '姓名中文'); ?></th>
                    <th width='80'><?php echo Yii::t('reg', '姓名英文'); ?></th>
                    <th width='70'><?php echo Yii::t('labels', 'Student ID'); ?></th>
                    <th width='40'><?php echo Yii::t('child', '性别'); ?></th>
                    <th width='100'><?php echo Yii::t('child', '生日'); ?></th>
                    <th width='160'><?php echo Yii::t('child', 'Class'); ?></th>
                    <th width='100'><?php echo Yii::t('labels', 'Father Mobile'); ?></th>
                    <th width='120'><?php echo Yii::t('labels', 'Father Email'); ?></th>
                    <th width='100'><?php echo Yii::t('labels', 'Mother Mobile'); ?></th>
                    <th width='120'><?php echo Yii::t('labels', 'Mother Email'); ?></th>
                    <th width='160'><?php echo Yii::t('asa', '提交时间'); ?></th>
                    <th width='120' v-if='is_positioning==1'><?php echo Yii::t('asa', '是否交定位金'); ?></th>
                    <th><?php echo Yii::t('asa', '问卷结果'); ?></th>
                    <th width='100'><?php echo Yii::t('asa', 'action'); ?></th>
                </tr>
            </thead>
            <tbody>
                <tr v-for='(list,index) in filteredItems'>
                    <td>{{index+1}}</td>
                    <td><a :href="list.url" target="_Blank">{{list.childName}}</a></td>
                    <td><a :href="list.url" target="_Blank">{{list.childName_en}}</a></td>
                    <td>{{list.childid}}</td>
                    <td>{{list.gender}}</td>
                    <td>{{list.birthday}}</td>
                    <td>{{list.classid}}</td>
                    <td width='100' style="word-break:break-all">{{list.fatherTel}}</td>
                    <td width='100' style="word-break:break-all">{{list.fatherEmail}}</td>
                    <td width='100' style="word-break:break-all">{{list.motherTel}}</td>
                    <td width='100' style="word-break:break-all">{{list.motherEmail}}</td>
                    <td>{{list.updated_at}}</td>
                    <td v-if='is_positioning==1'>{{list.is_invoice==1?'已支付':''}}</td>
                    <td>
                        <span v-for='(list,index) in list.answerInfo'>
                            <template v-if='list.q_val instanceof Array'>{{list.q_id}}.<template v-for='(arr,idx) in list.q_val'><template v-if='list.q_val!=""'><template v-for='option in items'><template v-if='arr==option.id'><span>{{option.titleCn}}</span>,</template></template>
                                    </template></template><template v-if='list.q_signature!=""'><img :src='list.q_signature' width='70' class="img-thumbnail" @click='imgBg(list.q_signature_raw)'></template><span v-if='list.q_memo!=""'> <?php echo Yii::t('interview', 'Note'); ?>：{{list.q_memo}}</span>;</template><template v-else>{{list.q_id}}.<template v-if='list.q_val!=""'><template v-for='option in items'><span v-if='list.q_val==option.id'>{{option.titleCn}}</span></template></template><template v-if='list.q_signature!=""'><img :src='list.q_signature' width='70' class="img-thumbnail" @click='imgBg(list.q_signature_raw)'></template><span v-if='list.q_memo!=""'> <?php echo Yii::t('interview', 'Note'); ?>：{{list.q_memo}}</span>;
                            </template>
                        </span>
                    </td>
                    <td>
                        <a v-if='list.answerInfo.length!=0' :href="'<?php echo $this->createUrl('deleteAnswer') ?>&id='+list.id+''" class="btn btn-xs btn-danger J_ajax_del" data-msg="确定要删除问卷结果吗？" title="<?php echo Yii::t('site', '删除问卷结果'); ?>">
                            <span class="glyphicon glyphicon-trash"> </span>
                        </a>
                        <a v-else :href="'<?php echo $this->createUrl('addBlockedChild') ?>&id='+list.id+''" class="btn btn-xs btn-danger J_ajax_del" data-msg="确定要加入屏蔽名单吗？" title="<?php echo Yii::t('site', '加入屏蔽名单'); ?>">
                            屏蔽
                        </a>
                    </td>
                </tr>
            </tbody>
        </table>
        <div class="modal fade bs-example-modal-lg" id="statistics" tabindex="-1" role="dialog" data-backdrop="static">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                            &times;
                        </button>
                        <h4 class="modal-title" id="myModalLabel">
                            统计结果
                        </h4>
                    </div>
                    <div class="modal-body">
                        <div v-for='(list,index) in answercount'>
                            <div class="panel panel-default">
                                <div class="panel-heading">{{index+1}}. {{list.name}}</div>
                                <div class="panel-body">
                                    <div :id="'main' + index+1" class='boxSize'></div>
                                </div>
                            </div>

                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal fade bs-example-modal-lg" id="sign" tabindex="-1" role="dialog" data-backdrop="static">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                            &times;
                        </button>
                        <h4 class="modal-title" id="myModalLabel">
                            图片查看
                        </h4>
                    </div>
                    <div class="modal-body">
                        <div class='text-center'>
                            <img :src="signImg" alt="" class="img-thumbnail">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
        <!-- 排除名单 -->
        <div class="modal fade" id="blocked-modal" tabindex="-1" role="dialog" data-backdrop="static">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                            &times;
                        </button>
                        <h4 class="modal-title">屏蔽名单</h4>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-success" role="alert">屏蔽名单内的学生不需要参与问卷，同步时将被忽略</div>
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>学生ID</th>
                                    <th>学生姓名</th>
                                    <th>学生班级</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for='(item, index) in blockedList'>
                                    <td>{{item.childid}}</td>
                                    <td><a :href="item.url" target="_blank">{{item.childName}}</a></td>
                                    <td>{{item.className}}</td>
                                    <td>
                                        <a :href="`<?php echo $this->createUrl('delBlockedChild'); ?>&child_id=${item.childid}&survey_id=${return_id}`" class="btn btn-xs btn-danger J_ajax_del" data-msg="确定要移除吗？">移除</a>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
<script>
    var childInfo = <?php echo json_encode($childInfo); ?>;
    var items = <?php echo json_encode($question); ?>;
    var template_id = <?php echo json_encode($template_id); ?>;
    var return_id = <?php echo json_encode($return_id); ?>;
    var classChild = <?php echo json_encode($classChild); ?>;
    var classList = <?php echo json_encode($classList); ?>;
    var is_positioning = <?php echo $is_positioning ?>;
    var datalist = new Vue({
        el: "#schoolTemplate",
        data: {
            childInfo: childInfo,
            items: items,
            tittle: '<?php echo $templateTitle ?>',
            template_id: template_id,
            return_id: return_id,
            disabled: false,
            clickBtn: false,
            noAnswer: false,
            classList: classList,
            class_id: '',
            Strdatas: [],
            blockedList: [],
            exprotchildInfo: childInfo,
            answer: '<?php echo $answer ?>',
            payNum: '<?php echo $invoiceNum ?>',
            is_positioning: <?php echo $is_positioning ?>,
            answercount: '',
            signImg: ''
        },
        created: function() {},
        computed: {
            // 计算属性：动态筛选表格数据
            filteredItems() {
                return this.childInfo.filter(item => {
                    // 班级筛选
                    const classMatch = this.class_id === '' || item.class_id === this.class_id;
                    // 填写筛选
                    const answerMatch = this.noAnswer === false || item.is_answer == 0;
                    // 返回匹配的结果
                    return classMatch && answerMatch;
                });
            }
        },
        watch: {
            asyncArray: function() {
                this.$nextTick(function() {
                    this.echarts()
                });
            }
        },
        methods: {
            reload() {
                if (!confirm('确定要同步吗？（此操作不会同步屏蔽名单内的学生）')) {
                    return;
                }
                var that = this
                that.disabled = true
                $.ajax({
                    url: "<?php echo $this->createUrl('synchronizeChild') ?>",
                    type: 'post',
                    dataType: 'json',
                    data: {
                        template_id: that.template_id,
                        return_id: that.return_id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            let arr = data.data
                            for (let i = 0; i < arr.length; i++) {
                                const element = arr[i];
                                that.childInfo.push(element);
                            }
                            resultTip({
                                "msg": data.message
                            })
                            that.disabled = false
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.disabled = false
                        }
                    },
                    error: function(data) {
                        alert('请求错误')
                    }
                })
            },
            showBlockedList() {
                var that = this
                $.ajax({
                    url: "<?php echo $this->createUrl('blockedChildList') ?>",
                    type: 'post',
                    dataType: 'json',
                    data: {
                        survey_id: that.return_id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.blockedList = data.data
                            $('#blocked-modal').modal('show')
                            $('#blocked-modal').on('shown.bs.modal', function() {
                                // 模态框显示完成后执行的代码
                                head.Util.ajaxDel();
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });

                        }
                    },
                    error: function(data) {
                        alert('请求错误')
                    }
                })
            },
            imgBg(q_signature_raw) {
                var that = this
                $.ajax({
                    url: '<?php echo $this->createUrl("getBigImage") ?>',
                    type: "post",
                    async: true,
                    dataType: 'json',
                    data: {
                        q_signature_raw,
                    },
                    success: function(data) {
                        that.signImg = data.data
                        $('#sign').modal('show')
                    },
                    error: function(data) {
                        alert("请求错误")
                    }
                })
            },
            delAnswer(list, index) {
                $.ajax({
                    url: "<?php echo $this->createUrl('deleteAnswer') ?>",
                    type: 'post',
                    dataType: 'json',
                    data: {
                        id: list.id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            list.answerInfo = []
                            list.updated_at = ''
                            resultTip({
                                "msg": data.message
                            })
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.disabled = false
                        }
                    },
                    error: function(data) {
                        alert('请求错误')
                    }
                })
            },
            dateTime() {
                var now = new Date();
                var year = now.getFullYear(); //年
                var month = now.getMonth() + 1; //月
                var day = now.getDate(); //日
                var hh = now.getHours(); //时
                var mm = now.getMinutes(); //分

                var clock = year + "-";

                if (month < 10)
                    clock += "0";

                clock += month + "-";

                if (day < 10)
                    clock += "0";

                clock += day + " ";
                if (hh < 10)
                    clock += "0";
                clock += hh + ":";
                if (mm < 10) clock += '0';
                clock += mm;
                return (clock);
            },
            btn_export() {
                let title = 'Survey ' + this.dateTime()
                var elt = document.getElementById('datatable');
                var wb = XLSX.utils.table_to_book(elt, {
                    sheet: title
                });
                if (this.is_positioning == 1) {
                    for (var i = 0; i < this.childInfo.length; i++) {
                        let data = i + 1
                        delete wb.Sheets[title]['O' + data];
                    }
                } else {
                    for (var i = 0; i < this.childInfo.length; i++) {
                        let data = i + 1
                        delete wb.Sheets[title]['N' + data];
                    }
                }
                return XLSX.writeFile(wb, title + '.xlsx');
            },


            view_statistics() {
                let that = this
                that.clickBtn = true
                $.ajax({
                    url: '<?php echo $this->createUrl("answercount") ?>',
                    type: "post",
                    async: true,
                    dataType: 'json',
                    data: {
                        template_id: <?php echo json_encode($template_id) ?>,
                        return_id: <?php echo json_encode($return_id) ?>
                    },
                    success: function(data) {
                        that.answercount = data.data
                        $('#statistics').modal('show')
                        that.$nextTick(function() {
                            that.echarts()
                            that.clickBtn = false
                        });
                    },
                    error: function(data) {
                        that.clickBtn = false
                        alert("请求错误")
                    }
                })
            },
            echarts() {
                var myChartsArr = []
                for (var i = 0; i < this.answercount.length; i++) {
                    var id = 'main' + i + '1'
                    var myCharts = echarts.init(document.getElementById(id))
                    var textName = []
                    for (var j = 0; j < this.answercount[i].data.length; j++) {
                        this.answercount[i].data[j].name = this.answercount[i].data[j].name + '<?php echo Yii::t('directMessage', ' ('); ?>' + this.answercount[i].data[j].value + '<?php echo Yii::t('directMessage', ')'); ?>'
                        textName.push(this.answercount[i].data[j].name)
                    }
                    myChartsArr.push(myCharts)
                    var option = this.optionData(myCharts, this.answercount[i].data, textName)
                    myCharts.setOption(option);
                }
                window.onresize = function() { // 自适应
                    for (var j = 0; j < myChartsArr.length; j++) {
                        if (myChartsArr[j].resize()) {
                            myChartsArr[j].resize()
                        }
                    }
                }
            },
            optionData(myCharts, destData, titleText) {
                var option = {
                    // title: {
                    //     text: '某站点用户访问来源',
                    //     left: 'center'
                    // },
                    tooltip: {
                        trigger: 'item',
                        formatter: '{a} <br/>{b} : {c} ({d}%)'
                    },
                    legend: {
                        orient: 'vertical',
                        left: 'left',
                        data: titleText
                    },
                    series: [{
                        name: '访问来源',
                        type: 'pie',
                        radius: '55%',
                        center: ['50%', '60%'],
                        data: destData,
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        }
                    }]
                };
                return option
            },
        }
    })

    function cbResultCallback(data) {
        for (i = 0; i < datalist.childInfo.length; i++) {
            if (datalist.childInfo[i].id == data) {
                datalist.childInfo[i].answerInfo = []
                datalist.childInfo[i].updated_at = ''
            }
        }
    }

    function cbBlockedCallback(data) {
        for (i = 0; i < datalist.childInfo.length; i++) {
            if (datalist.childInfo[i].id == data) {
                datalist.childInfo.splice(i, 1)
            }
        }
    }

    function cbBlockedDelCallback(data) {
        for (i = 0; i < datalist.blockedList.length; i++) {
            if (datalist.blockedList[i].childid == data) {
                datalist.blockedList.splice(i, 1)
            }
        }
    }
</script>