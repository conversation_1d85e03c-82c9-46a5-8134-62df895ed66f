<div class="container-fluid"   id='container'  v-cloak>
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('quote','Campus Operations'), array('//mcampus/default/index'))?></li>
        <li><?php echo Yii::t('quote','Shortcuts')?></li>
    </ol>
    <div >
        <div class='warn'>
            <span class='el-icon-info mt4'></span>
            <span class='flex1 ml5'><?php echo Yii::t('global','Please note that you are performing global management of shortcuts, and the configuration will be applied to all corresponding staffs.');?></span>  
        </div>
        <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane :label="list.title" :name="list.category" v-for='(list,index) in initData.category'>
            </el-tab-pane>
        </el-tabs>
        <div v-if='initData.type'>
            <div class='flex mt12 mb24 align-items'>
                <div class='flex1'>
                    <button type="button" class="btn btn-primary" @click='addInfo()'><span class='el-icon-circle-plus-outline'></span> <?php echo Yii::t('global','Add');?></button>  
                </div>
                <label class="radio-inline">
                    <input type="radio" v-model='grade_group' @change='filterTable' value="0"> <?php echo Yii::t('ptc','All');?>
                </label>
                <label class="radio-inline ml20" v-for='(list,index) in initData.type'>
                    <input type="radio" v-model='grade_group' @change='filterTable' :value="list.type"> {{list.title}}
                </label>
            </div>
            <table class='table table-hover tableElent'>
                <thead>
                    <tr>
                        <th width="340"><?php echo Yii::t("event", "Title Cn"); ?></th>
                        <th width="340"><?php echo Yii::t("event", "Title En"); ?></th>
                        <th width="600"><?php echo Yii::t('global','Link');?></th>
                        <th width="340" class="fixed-width"><?php echo Yii::t('newDS','Applicable to');?></th>
                        <th width="200" class="actions"><?php echo Yii::t("newDS", "Actions"); ?></th>
                    </tr>
                </thead>
                <tbody class='table_count' > 
                    <tr v-for='(list,index) in tableData'>
                        <td class='word-break'>{{list.title_cn}}</td>
                        <td class='word-break'>{{list.title_en}}</td>
                        <td class='ellipsis link-cell'><div class='colorBlue ' style='width:100%'><div class='ellipsis'><a :href="list.link" target='_blank'>{{list.link}}</a> </div></div></td>
                        <td>
                            <span class='tagLabel' v-for='(item,i) in list.types'>{{showType(item)}}</span>
                        </td>
                        <td class=''>
                            <div style='white-space: nowrap;'>
                                <button type="button" class="btn btn-link p6" @click='editInfoContent(list)'><?php echo Yii::t('schoolTeams','Edit');?></button>
                                <button type="button" class="btn btn-link p6" @click='delList(list.group)'><?php echo Yii::t("newDS", "Delete");?></button>
                                <span class='el-icon-rank font14 ml10 handle cur-p' v-if='grade_group!=0'></span>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
            <el-empty v-if='initData.type && tableData.length==0' description="<?php echo Yii::t('ptc', 'No Data') ?>"></el-empty>
        </div>
    </div>
    <!-- 编辑 -->
    <div class="modal fade" id="editListModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static" >
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel">{{listInfoData.group!=""?'<?php echo Yii::t('schoolTeams','Edit');?>':'<?php echo Yii::t('global','Add');?>'}}</h4>
                </div>
                <div class="modal-body p24 overflow-y scroll-box" :style="'max-height:'+(height-180)+'px;overflow-x: hidden;'">
                    <div class='mb12'>
                        <label for="input_link" class='color3 font14' style='font-weight:normal'><?php echo Yii::t('global', 'Link');?></label>
                        <div class="input-group">
                            <div class="input-group-btn">
                                <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><span>{{listInfoData.protocol}}</span> <span class="caret"></span></button>
                                <input type="hidden" name="http_url_hide" id="http_url_hide" value="https://">
                                <ul class="dropdown-menu">
                                    <li><a href="javascript:;" @click="urlPrefix('https://')">https://</a></li>
                                    <li><a href="javascript:;" @click="urlPrefix('http://')">http://</a></li>
                                </ul>
                            </div><!-- /btn-group -->
                            <input type="text" class="form-control"v-model='listInfoData.address' name="input_link" placeholder="<?php echo Yii::t('global', 'Please input link');?>" @blur="urlHandle()">
                        </div>
                    </div>
                    <div class='font14 color3 mb12'><?php echo Yii::t("event", "Title Cn"); ?></div>
                    <div class='relative'>
                        <input type="text" class="form-control" v-model='listInfoData.title_cn' placeholder="<?php echo Yii::t("leave", "Input"); ?>">
                    </div>
                    <div class='font14 color3 mb12 mt24'><?php echo Yii::t("event", "Title En"); ?></div>
                    <div class='relative'>
                        <input type="text" class="form-control" v-model='listInfoData.title_en' placeholder="<?php echo Yii::t("leave", "Input"); ?>">
                    </div>
                    <div class='font14 color3 mb12 mt24'><?php echo Yii::t("event", "Introduction Cn"); ?></div>
                    <div class='relative'>
                        <textarea class="form-control" rows="3"  v-model='listInfoData.intro_cn' placeholder="<?php echo Yii::t("leave", "Input"); ?>"></textarea>
                    </div>
                    <div class='font14 color3 mb12 mt24'><?php echo Yii::t("event", "Introduction En"); ?></div>
                    <div class='relative'>
                        <textarea class="form-control" rows="3"  v-model='listInfoData.intro_en' placeholder="<?php echo Yii::t("leave", "Input"); ?>"></textarea>
                    </div>
                    <div class='font14 color3 mb12 mt24'><?php echo Yii::t('newDS','Applicable to');?></div>
                    <div>
                    <label class="checkbox-inline mr16" v-for='(list,index) in initData.type'>
                            <input type="checkbox" v-model='listInfoData.types'  :value="list.type"> {{list.title}}
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" :disabled='btnDisanled' @click='savelList()'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 删除 -->
    <div class="modal fade" id="delModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t('newDS', 'Warning') ?></h4>
                </div>
                <div class="modal-body p24" >
                    <?php echo Yii::t('directMessage', 'Proceed to remove?') ?>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" :disabled='btnDisanled' @click='delList()'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    var height=document.documentElement.clientHeight;
    var container=new Vue({
        el: "#container",
        data: {
            activeName:0,
            grade_group:0,
            tableData: [],
            listInfoData:{},
            btnDisanled:false,
            initData:{},
            category:''
        },
        created: function() {
            let that=this
            this.getList()
        },
        methods: {
            getList(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("adminManageList") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        category:this.activeName
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            data.data.category.forEach(item => {
                                item.category=item.category+''
                            });
                            that.initData=data.data     
                            that.category=data.data.categoryActive          
                            that.activeName=data.data.categoryActive+''
                            if(that.grade_group==0){
                                that.tableData=that.initData.allList
                            }else{
                                that.tableData=that.initData.listByType[that.grade_group]?that.initData.listByType[that.grade_group]:[]
                            }
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            filterTable(){
                if(this.grade_group==0){
                    this.tableData=this.initData.allList
                }else{
                    this.tableData=this.initData.listByType[this.grade_group]?this.initData.listByType[this.grade_group]:[]
                }
                let that=this
                this.$nextTick(function () {
                    const el = document.querySelector('.table_count')
                    new Sortable(el, {
                        animation: 150,
                        handle: '.handle',
                        ghostClass: 'blue-background-class',
                        onEnd: function ({ newIndex, oldIndex }) { 
                            var list=JSON.parse( JSON.stringify (that.tableData))
                            list.splice(newIndex, 0, list.splice(oldIndex, 1)[0])
                            var newArray = list.slice(0)
                            that.sortTable(newArray)
                            that.tableData = []
                            that.$nextTick(function () {
                                that.tableData = newArray
                            })
                        }
                    });
                }) 
            },
            sortTable(newArray){
                let that=this
                var data=[]
                for(var i=0;i<newArray.length;i++){
                    data.push({id:newArray[i].id})
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("adminManageSort") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{ids:data},
                    success: function(data) {
                        if (data.state == 'success') {
                           resultTip({
                                msg: data.state
                            });
                            that.getStorg()
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {},
                })
            },
            handleClick(tab, event){
            this.getList()
            },
            showType(type){
                if(type==0){
                    return ''
                }else{
                    let title=this.initData.type.filter((i) => i.type == type)
                    if(title.length){
                        return title[0].title
                    }
                }
            },
            urlPrefix(url){
                this.listInfoData.protocol=url
            },
            urlHandle(){
                const isHttp = this.listInfoData.address.substr(0, 7) === 'http://';
                const isHttps = this.listInfoData.address.substr(0, 8) === 'https://';
                if (isHttps) {
                    this.listInfoData.protocol='https://'
                    this.listInfoData.address = this.listInfoData.address.substr(8)
                }
                if (isHttp) {
                    this.listInfoData.protocol='http://'
                    this.listInfoData.address = this.listInfoData.address.substr(7)
                }
            },
            addInfo(){
                this.listInfoData={
                    protocol:'https://',
                    address:'',
                    title_cn:'',
                    title_en:'',
                    intro_cn:'',
                    intro_en:'',
                    types:[],
                    category:this.category,
                    group:''
                }
                $("#editListModal").modal('show')
            },
            editInfoContent(list){
                this.listInfoData={
                    protocol:list.protocol,
                    address:list.address,
                    title_cn:list.title_cn,
                    title_en:list.title_en,
                    intro_cn:list.intro_cn,
                    intro_en:list.intro_en,
                    types:list.types,
                    category:this.category,
                    group:list.group
                }
                $("#editListModal").modal('show')
            },
            savelList(){
                let that=this
                that.btnDisanled=true
                this.listInfoData.link=this.listInfoData.protocol+this.listInfoData.address
                $.ajax({
                    url: '<?php echo $this->createUrl("adminManageSave") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{data:this.listInfoData},
                    success: function(data) {
                        if (data.state == 'success') {
                            that.getList()
                            resultTip({
                                msg: data.message
                            });
                            that.getStorg()
                            $("#editListModal").modal('hide')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.btnDisanled=false
                    },
                    error: function(data) {
                        that.btnDisanled=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            delList(id){
                if(id){
                    this.delId=id
                    $("#delModal").modal('show')
                    return
                }
                let that=this
                that.btnDisanled=true
                $.ajax({
                    url: '<?php echo $this->createUrl("adminManageRemove") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        group:this.delId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.getList('del')
                            resultTip({
                                msg: data.message
                            });
                            that.getStorg()
                            $("#delModal").modal('hide')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.btnDisanled=false
                    },
                    error: function(data) {
                        that.btnDisanled=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            getStorg(){
                var GV = {
                    'lang': "<?php echo Yii::app()->language;?>"
                };
                const sid = document.cookie.substr(document.cookie.indexOf('XOSESSIONID2=')+13, 27);
                const linksKey = 'SHORTCUT-LINK-' + sid + '-' + GV.lang;
                sessionStorage.removeItem(linksKey);
                jQuery.ajax({
                    url: '<?php echo $this->createUrl('/backend/apps/shortcutLink');?>',
                    type: 'get',
                    dataType: 'json',
                }).done(function (res) {
                    if (res.state == 'success') {
                        linksData = JSON.stringify(res.data);
                        sessionStorage.setItem(linksKey, linksData);
                    }
                });
            }
        },
    })

</script>

<style>
    .tagLabel{
        background: #F2F3F5;
        border-radius: 2px;
        font-weight: 400;
        font-size: 12px;
        color: #333333;
        line-height: 12px;
        padding:4px 6px;
        margin-right:8px;
        text-overflow: ellipsis;
        display: inline-block;
        margin-bottom: 5px;
    }
    .colorBlue{
        color:#4D88D2
    }
    .tableElent > thead > tr > th {
        border-bottom: 1px solid #dddddd;
        background: rgb(247, 247, 248);
    }
    .tableElent td,.tableElent th{
        vertical-align: middle !important;
        padding: 16px 12px !important;
        color:#333
    }
    .ellipsis {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .link-cell {
        max-width: 150px;
    }
    .word-break{
        /* max-width: 100px; */
        word-break: break-word;
    }
    .fixed-width{
        /* min-width: 150px; */
    }
    .actions{
        min-width: 120px;
    }
    .warn{
        font-size: 14px;
        color: #F0AD4E;
        margin-bottom:18px;
        display:flex
    }
</style>
