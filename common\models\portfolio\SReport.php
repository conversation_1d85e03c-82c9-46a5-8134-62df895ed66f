<?php

/**
 * This is the model class for table "ivy_sreport".
 *
 * The followings are the available columns in table 'ivy_sreport':
 * @property integer $id
 * @property string $schoolid
 * @property integer $classid
 * @property integer $childid
 * @property integer $yid
 * @property integer $startyear
 * @property integer $semester
 * @property string $template_id
 * @property integer $custom
 * @property string $custom_pdf
 * @property integer $stat
 * @property integer $overrule
 * @property integer $pdf_file
 * @property integer $timestamp
 * @property integer $uid
 * @property integer $learning_type
 */
class SReport extends CActiveRecord
{
    const STATUS_OFFLINE = 10;
    const STATUS_CHECK = 15; // 提交
    const STATUS_PASS = 16; // 通过
    const STATUS_OVERRULE = 14; // 驳回
    const STATUS_ONLINE = 20;
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_sreport';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('template_id', 'required'),
			array('classid, childid, yid, startyear, semester, custom, stat, pdf_file, timestamp, uid', 'numerical', 'integerOnly'=>true),
			array('schoolid, learning_type', 'length', 'max'=>15),
			array('template_id', 'length', 'max'=>16),
			array('custom_pdf, overrule, lang', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, schoolid, classid, childid, yid, startyear, semester, template_id, custom, custom_pdf, stat, pdf_file, timestamp, uid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'items'=>array(self::HAS_MANY, 'SReportItem', 'report_id'),
			'branch'=>array(self::BELONGS_TO, 'Branch', 'schoolid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'schoolid' => 'Schoolid',
			'classid' => 'Classid',
			'childid' => 'Childid',
			'yid' => 'Yid',
			'startyear' => 'Startyear',
			'semester' => 'Semester',
			'template_id' => 'Template',
			'custom' => 'Custom',
			'custom_pdf' => 'Custom Pdf',
			'stat' => 'Stat',
			'overrule' => Yii::t('reg','Reject reason'),
			'pdf_file' => 'Pdf File',
			'timestamp' => 'Timestamp',
			'uid' => 'Uid',
			'learning_type' => 'Learning Type',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('startyear',$this->startyear);
		$criteria->compare('semester',$this->semester);
		$criteria->compare('template_id',$this->template_id,true);
		$criteria->compare('custom',$this->custom);
		$criteria->compare('custom_pdf',$this->custom_pdf,true);
		$criteria->compare('stat',$this->stat);
		$criteria->compare('overrule',$this->overrule);
		$criteria->compare('pdf_file',$this->pdf_file);
		$criteria->compare('timestamp',$this->timestamp);
		$criteria->compare('uid',$this->uid);
		$criteria->compare('learning_type',$this->learning_type);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

    public function getDbConnection()
    {
        return Yii::app()->subdb;
    }

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return SReport the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	public static function getStatConfig()
    {
        $data = array(
            SReport::STATUS_OVERRULE => Yii::t('reg','Rejected'),
            SReport::STATUS_CHECK => Yii::t('curriculum','Disabled'),
            SReport::STATUS_PASS => Yii::t('global','Confirmed'),
            SReport::STATUS_ONLINE => Yii::t('teaching','Report is online'),
        );

        return $data;
    }

	public function beforeSave() {

	    if ($this->isNewRecord) {
	        $this->lang = $this->getReportLang($this->schoolid);
	    }
		// 如果是自定义 PDF，则缓存字段为1
		if ($this->custom == 1) {
			$this->pdf_file = 1;
		}

	    return parent::beforeSave();
	}

	public function getReportLang($schoolid)
	{
		$config = array(
			'BJ_DS' => 'en', 
			'BJ_SLT' => 'en', 
			'BJ_QF' => 'en',
			'BJ_QFF' => 'en',
			'BJ_IASLT' => 'en', 
			'BJ_CP' => 'en', 
			'BJ_OE' => 'en', 
			'BJ_OG-PH' => 'cn', 
			'CD_LH' => 'cn', 
			'CD_LT' => 'cn', 
			'NB_FJ' => 'cn', 
			'NB_HH' => 'cn', 
			'TJ_EB' => 'cn', 
			'TJ_EC' => 'cn', 
			'TJ_ES' => 'cn', 
			'XA_GJ' => 'cn', 
			'XA_LB' => 'cn', 
		);
		return isset($config[$schoolid]) ? $config[$schoolid]: 'en';
	}
}
