<?php 
    $definationType = WorkflowDefination::getDefinationType();
    $branchType = WorkflowDefination::getBranchType();
    $status = WorkflowDefination::getStatus();
    $display = WorkflowDefination::getDisplay();
?>
<script>
    var definationType = <?php echo CJSON::encode($definationType);?>;
    var branchType = <?php echo CJSON::encode($branchType);?>;
    var statusList = <?php echo CJSON::encode($status);?>;
    var displayList = <?php echo CJSON::encode($display);?>;
</script>
<!--新建工作流程基本信息模版-->
<script type="text/template" id="workflow-basicEdit-template">
    <div class="form-group" model-attribute="defination_name_cn">
        <?php echo CHtml::label($clabels['defination_name_cn'], CHtml::getIdByName('WorkflowDefination[defination_name_cn]'), array('class'=>'col-sm-3 control-label'));?>
        <div class="col-sm-9">
            <?php echo CHtml::textField('WorkflowDefination[defination_name_cn]','<%= defination_name_cn %>', array('encode'=>false,'class'=>'form-control length_5')); ?>
        </div>
    </div>
    <div class="form-group" model-attribute="defination_name_en">
        <?php echo CHtml::label($clabels['defination_name_en'], CHtml::getIdByName('WorkflowDefination[defination_name_en]'), array('class'=>'col-sm-3 control-label'));?>
        <div class="col-sm-9">
            <?php echo CHtml::textField('WorkflowDefination[defination_name_en]','<%= defination_name_en %>', array('encode'=>false,'class'=>'form-control length_5')); ?>
        </div>
    </div>
    <div class="form-group" model-attribute="defination_type">
        <?php echo CHtml::label($clabels['defination_type'], CHtml::getIdByName('WorkflowDefination[defination_type]'), array('class'=>'col-sm-3 control-label'));?>
        <div class="col-sm-9">
            <?php echo CHtml::dropDownList('WorkflowDefination[defination_type]','<%= defination_type %>',$definationType, array('class'=>'form-control select_3','empty'=>Yii::t('global', 'Please Select')));?>
        </div>
    </div>
    <div class="form-group" model-attribute="branch_type">
        <?php echo CHtml::label($clabels['branch_type'], CHtml::getIdByName('WorkflowDefination[branch_type]'), array('class'=>'col-sm-3 control-label'));?>
        <div class="col-sm-9">
            <?php echo CHtml::dropDownList('WorkflowDefination[branch_type]','<%= branch_type %>',$branchType, array('class'=>'form-control select_3','empty'=>Yii::t('global', 'Please Select')));?>
        </div>
    </div>
    <div class="form-group" model-attribute="defination_handle">
        <?php echo CHtml::label($clabels['defination_handle'], CHtml::getIdByName('WorkflowDefination[defination_handle]'), array('class'=>'col-sm-3 control-label'));?>
        <div class="col-sm-9">
            <?php echo CHtml::textField('WorkflowDefination[defination_handle]','<%= defination_handle %>', array('encode'=>false,'class'=>'form-control length_5')); ?>
        </div>
    </div>
    <div class="form-group" model-attribute="status">
        <?php echo CHtml::label($clabels['status'], CHtml::getIdByName('WorkflowDefination[status]'), array('class'=>'col-sm-3 control-label'));?>
        <div class="col-sm-9">
            <?php echo CHtml::radioButtonList('WorkflowDefination[status]','<%= status %>',$status,array('separator'=>'&nbsp;')); ?>
        </div>
    </div>
    <div class="form-group" model-attribute="display">
        <?php echo CHtml::label($clabels['display'], CHtml::getIdByName('WorkflowDefination[display]'), array('class'=>'col-sm-3 control-label'));?>
        <div class="col-sm-9">
            <?php echo CHtml::radioButtonList('WorkflowDefination[display]','<%= display %>',$display,array('separator'=>'&nbsp;')); ?>
        </div>
    </div>
    <div class="form-group" model-attribute="defination_memo">
        <?php echo CHtml::label($clabels['defination_memo'], CHtml::getIdByName('WorkflowDefination[defination_memo]'), array('class'=>'col-sm-3 control-label'));?>
        <div class="col-sm-9">
            <?php echo CHtml::textArea('WorkflowDefination[defination_memo]','<%= defination_memo %>',array('encode'=>false,'class'=>'form-control length_5')); ?>
            <?php echo CHtml::hiddenField('id', 0);?>
        </div>
    </div>
</script>

<!--工作流程基本信息显示模版-->
<script type="text/template" id="workflow-list-template">
    <tr id="<%= defination_id %>" data-status="<%= status %>" data-display="<%= display %>">
        <td><h4><%= defination_name_cn %></h4></td>
        <td><%= defination_name_en %></td>
        <td class="text-center"><%= definationType[defination_type] %></td>
        <td class="text-center"><%= branchType[branch_type] %></td>
        <td class="text-center"><%= defination_handle %></td>
        <td><span> <%= statusList[status] %></span></td>
        <td><span> <%= displayList[display] %></span></td>
        <td>
            <a class="btn btn-primary btn-xs mb5" title="工作流节点列表" href="<?php echo $this->createUrl('/workflow/admin/getNodesList');?>?id=<%=defination_id%>"><i class="glyphicon glyphicon-list-alt"></i></a>
            <a class="btn btn-primary btn-xs mb5" title="<?php echo Yii::t('global','Edit');?>" href="javascript:void(0)" onclick="newWorkflowTemplate(<%= defination_id %>);"><i class="glyphicon glyphicon-pencil"></i></a>
            <a class="J_ajax_del btn btn-danger btn-xs mb5" title="<?php echo Yii::t('global','Delete');?>" href="<?php echo $this->createUrl('/workflow/admin/deteteWorkflow');?>?id=<%=defination_id%>"><i class="glyphicon glyphicon-trash"></i></a>
        </td>
    </tr>
</script>
