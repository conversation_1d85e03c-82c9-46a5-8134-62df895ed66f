<?php

class StockController extends ProtectedController
{
	/**
	 * @var string the default layout for the views. Defaults to '//layouts/column2', meaning
	 * using two-column layout. See 'protected/views/layouts/column2.php'.
	 */
	public $layout='//layouts/column2';
    
    public $dialogWidth=400;

	public function init() {
        parent::init();
        Yii::import('common.models.points.*');
		
    }
	/**
	 * Creates a new model.
	 * If creation is successful, the browser will be redirected to the 'view' page.
	 */
	public function actionCreate()
	{
		$productId = Yii::app()->request->getParam('productid',0);
		$model=new PointsStock;
		$model->product_id = $productId;
		// Uncomment the following line if AJAX validation is needed
		if(isset($_POST['PointsStock']))
		{
            $this->addMessage('fail', 'success');
            $this->addMessage('message', Yii::t('message','Saving Failed!'));
			$model->attributes=$_POST['PointsStock'];
			$model->userid = Yii::app()->user->id;
			$model->update_timestamp = time();
			if($model->save())
			{
				$model->refresh();
				//count current stock
				$stock = PointsStock::model()->countStock($model->product_id);
				//update current stock
				if (PointsProduct::model()->updateStock($model->product_id, $stock))
				{
                    $this->addMessage('state', 'success');
                    $this->addMessage('callback', 'callback');
                    $this->addMessage('message', Yii::t('message','Data saved!'));
				}
			}
            $this->showMessage();
		}
        $this->layout='//layouts/dialog';
		$stockModel = new PointsStock('search');
		$stockModel->product_id = $stockModel->product_id;
		$this->render('create', array('model' => $model,'stockModel'=>$stockModel), false, true);
	}

	/**
	 * Deletes a particular model.
	 * If deletion is successful, the browser will be redirected to the 'index' page.
	 * @param integer $id the ID of the model to be deleted
	 */
	public function actionDelete($id)
	{
		if(Yii::app()->request->isPostRequest)
		{
			$model = $this->loadModel($id);
			$stock = PointsStock::model()->countStock($model->product_id);
			if ($stock - $model->num >=0)
			{
				if ($model->delete())
				{
					$stock = PointsStock::model()->countStock($model->product_id);
					PointsProduct::model()->updateStock($model->product_id, $stock);
					Yii::app()->end();
				}
			}
			else
			{
				echo Yii::t('operations', '不能删除，添加的库存已有订单生成！');
			}
		}
		else
		{
			throw new CHttpException(400,'Invalid request. Please do not repeat this request again.');
		}
	}

	/**
	 * Manages all models.
	 */
	public function actionAdmin()
	{
		$model=new PointsStock('search');
		$model->unsetAttributes();  // clear any default values
		if(isset($_GET['PointsStock']))
			$model->attributes=$_GET['PointsStock'];

		$this->render('admin',array(
			'model'=>$model,
		));
	}

	/**
	 * Returns the data model based on the primary key given in the GET variable.
	 * If the data model is not found, an HTTP exception will be raised.
	 * @param integer the ID of the model to be loaded
	 */
	public function loadModel($id)
	{
		$model=PointsStock::model()->findByPk((int)$id);
		if($model===null)
			throw new CHttpException(404,'The requested page does not exist.');
		return $model;
	}

	/**
	 * Performs the AJAX validation.
	 * @param CModel the model to be validated
	 */
	protected function performAjaxValidation($model)
	{
		if(isset($_POST['ajax']) && $_POST['ajax']==='points-stock-form')
		{
			echo CJSON::encode(CActiveForm::validate($model));
			Yii::app()->end();
		}
	}
}
