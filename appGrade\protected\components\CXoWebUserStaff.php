<?php
/**
 * author: xin.ran
 */
class CXoWebUserStaff extends CXoWebUser
{

	/**
	 * 如果是员工；将BizRule的参数清空；因为带参数的checkAccess在同一请求中也不会有缓存
	 * @param	Object	$operation		Description
	 * @param	Object	$params			Description
	 * @param	Boolean	$allowCaching	Description
	 * @return	Boolean
	 */
	public function checkAccess($operation,$params=array(),$allowCaching=true) {
		//参数没有意义了，员工权限不会随孩子ID变化而变化
		return parent::checkAccess($operation,array(),$allowCaching);
	}
	
	/**
	 * 生成preViewCC Token，跟apps写入SESSION的进行比较
	 * @return	Object
	 */
	public function genAppsToken(){
        $ccTokenFlag = "@!pre%sVi%sew%dCC!"; //需与员工查看孩子前台时的FLAG一致；
        $str = sprintf($ccTokenFlag, Mims::genSecurityCode(), Yii::app()->session->sessionId,Yii::app()->user->getId());
        return md5($str);
	}
	
}