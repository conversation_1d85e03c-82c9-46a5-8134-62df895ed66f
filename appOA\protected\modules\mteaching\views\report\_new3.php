<style>
    [v-cloak] {
        display: none;
    }
    .colorBlue{
        color:#4D88D2
    }
    .subList{
        padding:10px
    }
    /* .subList .font14{
        color:#333
    }
    .subList .font12{
        color:#666
    } */
    .subList:hover{
        background: #F7F7F8;
        border-radius: 4px;
        cursor: pointer;
    }
    .subListActive,.subListActive div{
        background: #4D88D2 !important;
        border-radius: 4px;
        color:#ffffff !important
    }
    .borderRight{
        border-right: 1px solid #E5E6EB;
    }
    .title{
        padding:4px 12px;
        background: #F2F3F5;
        border-radius: 4px 4px 0px 0px;
        font-size:14px
    }
    td{
        padding:24px 12px 0 !important;
        vertical-align: middle !important;
    }
    .img42{
        width: 42px;
        height: 42px;
        object-fit: cover;
        border-radius:50%;
    }
    .avatar32{
        width: 32px;
        height: 32px;
        object-fit: cover;
        border-radius:50%;
    }
    .borderBto{
        border-bottom: 1px solid #E5E6EB;
    }
    .line{
        width: 4px;
        height: 14px;
        background: #4D88D2;
        display: inline-block;
        margin-left:-24px
    }
    .point{
        width: 5px;
        height: 5px;
        background: rgba(77,136,210,0.3);
        display: inline-block;
        border-radius:50%
    }
    .max_height{
        max-height:300px;
        overflow-y:auto;
        padding: 0 12px
    }
    .green{
        color:#5CB85C;
        padding:4px 6px;
        background: #F2F9F2;
        border-radius: 2px;
        font-size:12px;
        line-height: 12px;
    }
    .scroll-box::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius   : 10px;
        background-color: #ccc;
        background-image: none
    }
    .scroll-box::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0);
        background   : #fff;
        border-radius: 10px;
        border:none
    }
    .table{
        margin-bottom:0
    }
    .teacherList{
        padding:16px;
        border-radius: 4px;
        border: 1px solid #DCDEE0;
    }
    .teacherList:hover,.teacherListEd{
        border: 1px solid #4D88D2;
        cursor: pointer;
    }
    .tag{
        padding:4px 6px;
        background: #F2F3F5;
        border-radius: 2px;
        color: #333333;
        font-size:12px;
        line-height: 12px;
        margin-right:-16px
    }
    .borderCenter{
        width: 1px;
        height: 100%;
        background: #E5E6EB;
        position: absolute;
        left: 50%;
        top:0
    }
    .emailContent{
        padding:52px 24px 24px;
        background: linear-gradient( 180deg, #F8FBFF 0%, #FFFFFF 100%);
        border-radius: 4px;
        border: 1px solid #DCDEE0;
        position: relative;
        margin-top:52px;
        margin-bottom:24px
    }
    .report{
        position: absolute;
        width: 76px;
        height: 76px;
        z-index: 999;
        top: -38px;
        left: 50%;
        margin-left:-38px
    }
    .linkBox{
        background: #F2F3F5;
        border-radius: 4px;
        padding:12px 16px
    }
    .m0{
        margin:0
    }
    .notSubmit{
        color:#D9534F;
        padding:2px 6px;
        background: #FCF1F1;
        border-radius: 2px;
        font-size:12px;
        line-height: 12px;
        margin-left:8px
    }
    .width50{
        text-align:right;
        width:50px
    }
    .text_overflow{
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .colorYellow{
        color:#F0AD4E
    }
    .selectChild{
        padding:12px 8px
    }
    .selectChild:hover{
        background: #FAFAFA;
        border-radius: 4px;
    }
    .p0{
        padding:0 !important
    }
    .popover{
        max-width:300px;
        z-index:9999
    }
    .colorC{
        color:#CCCCCC
    }
    .el-loading-mask{
        background-color:rgb(255 255 255 / 0%) !important
    }
    .filterDefault{
        color:#666;
        padding:6px 8px;
        cursor: pointer;
        display: inline-block;
    }
    .filterDefault .badge{
        color:#4D88D2;
        background:#F0F5FB;
        font-size:12px;
        padding:2px 5px
    }
    .filterActive{
        padding:6px 8px;
        background:#4D88D2;
        color:#fff;
        border-radius:4px;
        cursor: pointer;
        display: inline-block;
    }
    .filterActive .badge{
        color:#4D88D2;
        background:#fff;
        font-size:12px;
        padding:2px 5px
    }
    .filterDefault:hover{
        background: #F7F7F8;
        border-radius: 4px;
        cursor: pointer;
    }
    .greenAll{
        color:#5CB85C;
        padding:6px 12px;
        background: #F2F9F2;
        border-radius: 2px;
        font-size:14px;
        line-height: 14px;
        cursor: pointer;
    }
</style>
<div class="container-fluid" id='container' v-cloak>
    <div class="row" >
        <!-- <div class=' col-md-12 col-sm-12' v-if='isShow && classList.length==0'>
            <div class='alert alert-warning'><?php echo Yii::t('newDS', 'None class has been assigned to you.') ?></div>
        </div> -->
        <div v-if='userData.name'>
            <?php if(Yii::app()->user->checkAccess('ivystaff_it') || Yii::app()->user->checkAccess('ivystaff_dsedu_viewall')){?>
                <div class="col-md-12 col-sm-12 mb10 ">
                <span class='colorBlue cur-p' @click='tabTeacher()'>
                    <span class='el-icon-user-solid font16'></span>
                    <span class=' font18 fontBold'>{{userData.name}}</span>
                    <span class='el-icon-arrow-down font16'></span>
                </span>
                </div>
            <?php }?>
            <div v-if='loading && coursesList.length!=0'>
                <div class="col-md-2 col-sm-12 borderRight scroll-box" :style="'height:'+(height-240)+'px;overflow-x: hidden;'">
                    <div class="subList" v-for='(list,index) in coursesList' :class='list.course_id==activeId?"subListActive":""' @click='coursesActive(list)'>
                        <div class="font14 color3"  >{{list.title}}</div>
                        <div class='color6 font12'>{{list.course_code}}</div>
                    </div>
                </div>
                <div class="col-md-10 col-sm-10" v-if='childData.child_list'  v-loading="fullscreenLoading">
                    <div v-if='childData.child_list.length'>
                        <div class='flex align-items mb24'>
                            <button type="button" class="btn btn-primary" @click='sendEmailModal()'><?php echo Yii::t('teaching', 'Send Email Notifications');?></button>
                            <span class='el-icon-warning-outline ml16 font14 color6'></span>
                            <span class='color6 font12 ml5'><?php echo Yii::t('teaching', "Please note: Email will be sent to {StudentId}@daystaracademy.cn, please contact campus IT if student's email does not exist.");?></span>
                        </div>
                        <div class='mt24 font14 color3 mb24' v-if='!allSubmit'>
                            <span class='mr8 ' :class='filterType=="all"?"filterActive":"filterDefault"' @click='switchInfo("all")'><?php echo Yii::t('ptc', 'All') ?></span>
                            <span class='mr8' :class='filterType=="1"?"filterActive":"filterDefault"' @click='switchInfo(1)'><?php echo Yii::t('reg', 'Submitted') ?><span class="badge ml4">{{resultsNum(1)}}</span></span>
                            <span class='mr8' :class='filterType=="2"?"filterActive":"filterDefault"' @click='switchInfo(2)'><?php echo Yii::t('reg', 'Not submitted') ?><span class="badge ml4">{{resultsNum(2)}}</span></span>
                        </div>
                        <div class='mt24 font14 color3 mb24' v-if='allSubmit'>
                            <span class='greenAll'><span class='el-icon-success mr8'></span><?php echo Yii::t('teaching', 'All Submitted') ?>：{{childList.length}}</span>
                        </div>
                        <div class='scroll-box' :style="'height:'+(height-351)+'px;overflow-x: hidden;'">
                            <table class='table table-bordered' v-if='childList.length'>
                                <tr v-for='(list,index) in childList'>
                                    <td width='200'>
                                        <div class='text-center mb24'>
                                            <img :src="list.avatar" alt="" class='img42'>
                                            <div class='font14 color3 mt8'>{{list.bilingual_name}}</div>
                                            <div class='font12 color6 mt4'>{{list.class_name}}</div>
                                            <div class='font12 color6 mt4 mb4'>ID：{{list.id}}</div>
                                            <span class='green ' v-if='childData.learningGoalData[list.id] '>{{childData.learningGoalData[list.id].leaves_count}} selected</span>
                                        </div>
                                    </td>
                                    <td  width='600'>
                                        <div class='scroll-box max_height' v-if='childData.learningGoalData[list.id]'>
                                            <div v-for='(list,index) in childData.learningGoalData[list.id].goalTree' class='mb24'>
                                                <div class='borderBto'><span class='title fontBold'>{{list.title}}</span> </div>
                                                <div v-for='(item,idx) in list.children'>
                                                    <div class='mt16 flex align-items mb4'>
                                                        <span class='line'></span>
                                                        <span class='ml20'>
                                                            <span class='font14 color3 fontBold'>{{item.title}}</span>
                                                            <span class='color6 font12'>｜ {{item.intro}}</span>
                                                        </span>
                                                    </div>
                                                    <div  v-for='(_item,_i) in item.children'>
                                                        <div class='colorBlue font12 mt8 flex align-items'>
                                                            <span class='el-icon-circle-check'></span>
                                                            <span class='ml8'>{{_item.title}}</span>
                                                        </div>
                                                        <div class='flex align-items ml20 mt5' v-for='(intro,i) in _item.intro'>
                                                            <div class='point'></div>
                                                            <div class='color9 font12 ml8'>{{intro}}</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div v-else class='color9 font12'>
                                        <?php echo Yii::t('reg', 'Not submitted') ?>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                            <div v-else>
                                <el-empty description="<?php echo Yii::t('attends', 'No students') ?>"></el-empty>
                            </div>
                        </div>
                    </div>
                    <!-- <div v-else >
                        <el-empty description="<?php echo Yii::t('attends', 'No students') ?>"></el-empty>
                    </div> -->
                </div>
                <div v-else class="col-md-10 col-sm-10" v-loading="fullscreenLoading">
                    <el-empty :description="childData.error"></el-empty>
                </div>
            </div>
            <div v-else-if='loading' class="col-md-12 col-sm-12  ">
                <el-empty description="<?php echo Yii::t('ptc', 'No Data') ?>"></el-empty>
            </div>
        </div>
    </div>
    <!--切换老师 -->
    <div class="modal fade" id="teacherModal" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title"><?php echo Yii::t('teaching', 'Select a Teacher');?></h4>
                </div>
                <div class="modal-body p24">
                    <div class='row scroll-box' v-if='initData.teacher_info' :style="'max-height:'+(height-200)+'px;overflow-x: hidden;'">
                        <div class='col-md-4 mb16' v-for='(list,index) in homeRoomCourses'>
                            <div class='teacherList' :class='userData.uid==list.teacher_id?"teacherListEd":""' @click='filterTacher(list.teacher_id)'>
                                <div class="flex align-items">
                                    <span class='font14 color3 flex1 fontBold'>{{list.title}}</span>
                                    <span class='tag'>{{list.course_code}}</span>
                                </div>
                                <div class="flex align-items mt8" v-if='teacher_info[list.teacher_id]'>
                                    <img class="avatar32" :src="teacher_info[list.teacher_id].photoUrl"/>
                                    <span class="font14 color3 flex1 ml8">{{teacher_info[list.teacher_id].name}}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--发邮件 -->
    <div class="modal fade" id="sendEmailModal" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title"><?php echo Yii::t('teaching', 'Send Email Notifications');?></h4>
                </div>
                <div class="modal-body  " style='padding:0 24px '>
                    <div class='borderCenter'></div>
                    <div class='row positionStatic'>
                        <div class='col-md-6 p0'>
                            <div class='scroll-box pt24'  v-if='childData.child_list' :style="'max-height:'+(height-230)+'px;overflow-x: hidden;padding: 0 8px;'">
                                <div class="" style='padding: 0 8px;'>
                                    <span class='font14 color3 flex1 fontBold'><?php echo Yii::t('referral', 'Select Student');?></span>
                                    <div class="checkbox font14 mt16 mb4">
                                        <label style='line-height: 1.5;'>
                                            <input type="checkbox"  v-model="checkAll" @change='checkAllList()'><span class='colorBlue'><?php echo Yii::t('global', 'Select All');?></span> 
                                        </label>
                                    </div>
                                </div>
                                <div class=" m0 selectChild" v-for='(list,index) in childData.child_list'>
                                    <div class="flex align-items checkbox ">
                                        <label class="flex align-items flex1">
                                            <input type="checkbox" :value='list.id' v-model='checkChild' @change='checkChildList()'>
                                            <img class="img42 ml12" :src="list.avatar"/>
                                            <div class='flex1 ml8'>
                                                <div class="font14 color3">{{list.bilingual_name}} <span class='notSubmit' v-if='!list.submit'><?php echo Yii::t('reg', 'Not submitted');?></span></div>
                                                <div class='font12 color6' style='line-height:12px'>{{list.class_name}}</div>
                                            </div>
                                        </label>
                                        <!-- <a href="#" class="colorBlue font14 cur-p" :id="'popover-' + index"  data-toggle="popover" :data-content="getPopoverContent(list)">访问信息</a> -->
                                        <el-popover
                                            placement="bottom-start"
                                            width="320"
                                            v-model="tipVisibles[index]"
                                            trigger="click">
                                            <div style='padding:10px 12px'>
                                                <div class='flex align-items'>
                                                    <span class='font14 color3 fontBold flex1 '><?php echo Yii::t('teaching', 'Access Info');?></span>
                                                    <span class='el-icon-close cur-p font14 color3' @click='popoverHide(index)'></span>
                                                </div>
                                                <div class='flex align-items mt20'>
                                                    <span class='font14 color3 fontBold width50'>Email：</span>
                                                    <span class='font14 color3 flex1'>{{list.email}}</span>
                                                    <button class="btn btn-default btn-xs" type="submit" @click='copyToClipboard(list.email)'><?php echo Yii::t('lunch', 'Copy');?></button>
                                                </div>
                                                <div class='flex align-items mt20'>
                                                    <span  class='font14 color3 fontBold width50'>Link：</span>
                                                    <a class='flex1 text_overflow colorBlue font14' :href='list.link' target="_blank">{{list.link}}</a>
                                                    <button class="btn btn-default btn-xs ml16" type="submit" @click='copyToClipboard(list.link)'><?php echo Yii::t('lunch', 'Copy');?></button>

                                                </div>
                                                <div class='flex align-items mt20'>
                                                    <span  class='font14 color3 fontBold width50'>Code：</span>
                                                    <span  class='font16 color3 flex1'>{{list.code}}</span>
                                                    <button class="btn btn-default btn-xs" type="submit" @click='copyToClipboard(list.code)'><?php echo Yii::t('lunch', 'Copy');?></button>
                                                </div>
                                            </div>
                                            <span class='colorBlue font14 cur-p' slot="reference"><?php echo Yii::t('teaching', 'Access Info');?></span>
                                        </el-popover>
                                    </div>  
                                    <div class='colorYellow  mt8 flex align-items' style='padding-left:82px;line-height:12px' v-if='list.last_mail_time!=""'><span class='el-icon-message font14'></span><span class='font12 ml5'><?php echo Yii::t('teaching', 'Latest Sent : ');?>{{list.last_mail_time}}</span></div>
                                    <div v-else class='colorC flex align-items' style='padding-left:82px;line-height:12px'><span class='el-icon-message font14'></span><span class='font12 ml5'><?php echo Yii::t('teaching', 'Never sent');?></span></div>
                                </div>
                            </div>
                        </div>
                        <div class='col-md-6 scroll-box pt24' :style="'max-height:'+(height-230)+'px;overflow-x: hidden;'">
                            <div class='font14 color3 fontBold'><?php echo Yii::t('teaching', 'Email Content');?></div>
                            <div class='emailContent'>
                                <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/report.png' ?>" alt="" class='report'>
                                <div class='font16 color3 fontBold'>
                                Setting Learning Goals for Approaches to Learning (ATL) Skills Development
                                </div>
                                <div class='font14 color6 mt16'>
                                It's time to set your learning goals for this academic year, focusing on Approaches to Learning (ATL) skills. These skills are crucial for your academic success and personal development. This goal-setting process will help you identify and articulate your objectives for the upcoming year as you work on two key areas: Self-management and Research. By developing these ATL skills, you will not only enhance your current studies but also prepare yourself for future challenges.</div>
                                <div class='font14 color6 mt16'>
                                    Why this matters: The goals you choose will be stated in your Quarter 1 report. This shows how important they are for your learning journey this year. Take your time to think about what you'd like to achieve. Choose goals that will challenge you but are also possible to reach.
                                </div>
                                <div class='linkBox mt20'>
                                    <div class='font14 color3'>To set your goals, please visit this link:</div>
                                    <div class='flex align-items'><span class='font14 colorBlue'>https://mockup.mockup.mockup</span><span class='colorYellow font14'>（示例）</span></div>
                                    <div class='font14 color3 mt8'>You'll need this special code to log in</div>
                                    <div class='flex align-items'><span class='font14 colorBlue'>QCEFG</span><span class='colorYellow font14'>（示例）</span></div>
                                </div>
                                <div class='font14 color3 mt24 fontBold'>How to Set Your Goals ?</div>
                                <div class='font14 color6 mt16'>Go to the link mentioned above and use your special code to log in. </div>
                                <div class='font14 color6 mt16'>Choose 3 to 5 ATL skills you want to work on this academic year. You can pick these from any of the two categories: Self-management and Research. It's up to you how many you choose from each category, as long as you have at least 3 in total and no more than 5.</div>
                                <div class='font14 color6 mt16'>For each skill you choose, think about how you want to improve it and how you'll know when you've succeeded.</div>
                                <div class='font14 color6 mt16'>Consider your current strengths and areas for improvement when setting your goals. This will help you set the right goals for you. You will reflect on your skills development in Quarter 3.</div>
                                <div class='font14 color6 mt16'>Be honest and realistic about what you can achieve within the academic year.</div>
                                <div class='font14 color6 mt16'>Make sure you finish setting your goals by 24th October.</div>
                                <div class='font14 color3 mt24 fontBold'>Remember</div>
                                <div  class='font14 color6 mt16'>Should you require clarification or guidance, please do not hesitate to consult your homeroom advisor. </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <span class='pull-left color6 font14 mt4'><?php echo Yii::t("newDS", " ");?>{{checkChild.length}}<?php echo Yii::t("newDS", " student(s) selected");?></span>
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button" class="btn btn-primary" :disabled='disBtn' @click='sendEmail()'><?php echo Yii::t("teaching", "Send Email");?></button>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    $(document).on('click', function (e) {
      $('[data-toggle="popover"]').each(function () {
        if (!$(this).is(e.target) && $(this).has(e.target).length === 0 && $('.popover').has(e.target).length === 0) {
          $(this).popover('hide');
        }
      });
    });
    function popoverHide() {
        $('[data-toggle="popover"]').each(function () {
          $(this).popover('hide');
        });
    }
    function copyToClipboard(text) {
        container.copyToClipboard(text)
    }
    var height=document.documentElement.clientHeight;
    var quarter = "<?php echo $data['quarter']?>"
    var container = new Vue({
        el: "#container",
        data: {
            height:height,
            initData:{},
            teacherCourses:{},
            userData:{},
            teacher_info:{},
            homeRoomCourses:{},
            coursesList:[],
            quarter:quarter,
            activeId:'',
            childData:{},
            checkChild:[],
            checkAll:false,
            tipVisibles:[],
            disBtn:false,
            fullscreenLoading:false,
            viewCopy:'',
            activeList:{},
            filterType:'all',
            childList:[],
            childListAll:[],
            allSubmit:null
        },
        watch:{},
        created:function(){
            this.initList()
        },
        methods: {
            getPopoverContent(list) {
            return `
                <div style='padding:10px 12px'>
                    <div class='flex align-items'>
                        <span class='font14 color3 fontBold flex1'>访问信息</span>
                        <span class='el-icon-close cur-p font14 color3'  onclick='popoverHide()'></span>
                    </div>
                    <div class='flex align-items mt20'>
                        <span class='font14 color3 fontBold width50'>Email：</span>
                        <span class='font14 color3'>${list.email}</span>
                    </div>
                    <div class='flex align-items mt20'>
                        <span class='font14 color3 fontBold width50'>Link：</span>
                        <a class='flex1 text_overflow colorBlue font14' href='${list.link}' target="_blank">${list.link}</a>
                        <button class="btn btn-default btn-xs ml16" type="button" onclick='copyToClipboard("${list.link}")'>Copy</button>
                    </div>
                    <div class='flex align-items mt20'>
                        <span class='font14 color3 fontBold width50'>Code：</span>
                        <span class='font16 color3 flex1'>${list.code}</span>
                        <button class="btn btn-default btn-xs" type="button" onclick='copyToClipboard("${list.code}")'>Copy</button>
                    </div>
                </div>
            `;
            },
            popoverHide(index){
               Vue.set(this.tipVisibles, index, false);
            },
            resultsNum(type){
                if(type==1){
                    return Object.keys(this.childData.learningGoalData).length
                }else{
                    let com=Object.keys(this.childData.learningGoalData).length
                    return this.childData.child_list.length-com
                }
            },
            switchInfo(type){
                this.filterType=type
                if(type=='all'){
                    this.childList=this.childListAll
                }else {
                    const filteredData = [];
                    const filteredDataNum = [];
                    this.childList=[]
                    this.childListAll.forEach(item => {
                        if(this.childData.learningGoalData[item.id]){
                            filteredData.push(item)
                        }else{
                            filteredDataNum.push(item)
                        }
                    });
                    if(type==1){
                        this.childList=filteredData
                    }else{
                        this.childList=filteredDataNum
                    }
                }
                
            },
            initList(){
                let that=this
                this.loading=false
                $.ajax({
                    url: '<?php echo $this->createUrl("teacherList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.initData = data.data ;
                            that.userData = data.data.userData ;
                            that.teacher_info = data.data.teacher_info ;
                            that.homeRoomCourses = data.data.homeRoomCourses ;
                            that.teacherCourses = data.data.teacherCourses ;
                            if(that.teacherCourses[that.userData.uid]){
                                that.coursesList=that.teacherCourses[that.userData.uid]
                            }else{
                                that.coursesList=[]
                            }
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.loading = true;
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.loading = true;
                    },
                })
            },
            tabTeacher(){
                $("#teacherModal").modal('show');  
            },
            filterTacher(teacher_id){

                this.userData =this.teacher_info[teacher_id];
                if(this.teacherCourses[teacher_id]){
                    this.coursesList=this.teacherCourses[teacher_id]
                    this.coursesActive(this.coursesList[0])
                }else{
                    this.coursesList=[]
                }
                this.childData={}
                $("#teacherModal").modal('hide');  
            },
            coursesActive(list){
                this.activeId=list.course_id
                this.activeList=list
                let that=this
                this.fullscreenLoading = true;
                $.ajax({
                    url: '<?php echo $this->createUrl("studentData") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        quarter:this.quarter,
                        course_id:list.course_id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.filterType='all'
                            data.data.child_list.sort((a, b)=> a.bilingual_name.localeCompare(b.bilingual_name, 'zh'));
                            that.childList=data.data.child_list
                            that.childListAll=JSON.parse(JSON.stringify(data.data.child_list))
                            that.childData=data.data
                            that.fullscreenLoading = false;
                            if(data.data.child_list.length==Object.keys(data.data.learningGoalData).length){
                                that.allSubmit=true
                            }else{
                                that.allSubmit=false
                            }
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.fullscreenLoading = false;
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.fullscreenLoading = false;
                    },
                })
            },
            sendEmailModal(){
                this.checkChild=[]
                this.checkAll=false
                $("#sendEmailModal").modal('show');
                this.$nextTick(() => {
                    $('[data-toggle="popover"]').popover({
                        html: true,
                        placement: 'bottom',
                        container:'body'
                    });
                })
            },
            checkAllList(){
                this.checkChild=[]
                if(this.checkAll){
                    this.childData.child_list.forEach(item => {
                        this.checkChild.push(item.id)
                    });
                }
            },
            checkChildList(){
                if(this.checkChild.length==this.childData.child_list.length){
                    this.checkAll=true
                }else{
                    this.checkAll=false
                }
            },
            copyToClipboard(text) {
                let input = document.createElement("input");
                document.body.appendChild(input);
                input.value = text; 
                input.focus();
                input.select();
                this.$nextTick(() => {
                    input.focus(); 
                    input.select(); 
                    document.execCommand("Copy");
                    resultTip({"msg":'<?php echo Yii::t("teaching", "Data Copied");?>'})
                    document.body.removeChild(input);
                });
            },
            sendEmail(){
                let that=this
                if(this.checkChild.length==0){
                    resultTip({
                        error: 'warning',
                        msg: '请选择学生'
                    });
                    return
                }
                this.disBtn=true
                $.ajax({
                    url: '<?php echo $this->createUrl("sendEmail") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        quarter:this.quarter,
                        child_ids:this.checkChild
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg: data.message
                            });
                            that.coursesActive(that.activeList)
                            $("#sendEmailModal").modal('hide');
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.disBtn=false
                    },
                    error: function(data) {
                        that.disBtn=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            }
        }
    })
</script>