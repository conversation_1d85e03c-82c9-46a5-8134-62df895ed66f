<?php

/**
 * This is the model class for table "ivy_calendar_school".
 *
 * The followings are the available columns in table 'ivy_calendar_school':
 * @property integer $schid
 * @property integer $yid
 * @property string $startyear
 * @property string $branchid
 * @property integer $is_selected
 */
class CalendarSchool extends CActiveRecord {

    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return CalendarSchool the static model class
     */
    public static function model($className = __CLASS__) {
        return parent::model($className);
    }

    /**
     * @return string the associated database table name
     */
    public function tableName() {
        return 'ivy_calendar_school';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules() {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('is_selected', 'required'),
            array('yid, is_selected', 'numerical', 'integerOnly' => true),
            array('startyear', 'length', 'max' => 4),
            array('branchid', 'length', 'max' => 10),
            // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            array('schid, yid, startyear, branchid, is_selected', 'safe', 'on' => 'search'),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations() {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
            'months' => array(self::HAS_MANY, 'CalendarSchoolDays', array('yid'=>'yid')),
            'cTemplate' => array(self::BELONGS_TO, 'Calendar',array('yid'=>'yid'))
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels() {
        return array(
            'schid' => 'Schid',
            'yid' => 'Yid',
            'startyear' => 'Startyear',
            'branchid' => 'Branchid',
            'is_selected' => 'Is Selected',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search() {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('schid', $this->schid);
        $criteria->compare('yid', $this->yid);
        $criteria->compare('startyear', $this->startyear, true);
        $criteria->compare('branchid', $this->branchid, true);
        $criteria->compare('is_selected', $this->is_selected);

        return new CActiveDataProvider($this, array(
                    'criteria' => $criteria,
                ));
    }

    public function getCalendar($startyear, $schoolAbb) {
        if (!empty($startyear) && !empty($schoolAbb)) {
            $branch = Branch::model()->find('abb=:abb', array(':abb' => $schoolAbb));
            $branchid = $branch->branchid;
            $obj['calendar'] = $this->model()->find('startyear=:startyear AND branchid=:branchid', array(':startyear' => $startyear, ':branchid' => $branchid));
            $obj['branch'] = $branch;
            return $obj;
        }
        return null;
    }

    public function getCalendarDays() {
        $ret = array();
        if ($this->yid) {
            $days = Yii::app()->db->createCommand()
                    ->select('*')
                    ->from('ivy_calendar_day')
                    ->where('yid=:yid AND privacy=0 AND ( schoolid = 0 OR schoolid =:branchid)', array(':yid' => $this->yid, ':branchid' => $this->branchid))
                    ->order('schoolid ASC') //排序很重要，校园如果有自定义内容，将覆盖模版里的内容
                    ->queryAll();
            foreach ($days as $day) {
                $ret[$day['date']] = $day;
            }
            return $ret;
        }
        return null;
    }

    public function getCalendarMonths() {
        $ret = array();
        if ($this->yid) {
            $months = Yii::app()->db->createCommand()
                    ->select('*')
                    ->from('ivy_calendar_schooldays')
                    ->where('yid=:yid', array(':yid' => $this->yid))
                    ->order('month_label ASC') //排序很重要，校园如果有自定义内容，将覆盖模版里的内容
                    ->queryAll();
            foreach ($months as $month) {
                $key = str_replace("-", "", $month['month_label']);
                $ret[$key] = $month;
                $ret[$key]['month'] = substr($key, -2);
                $ret[$key]['yearmonth'] = date('M, Y', strtotime($month['month_label'] . '-01'));
            }
            return $ret;
        }
        return null;
    }

    public function getCalendarSemesters() {
        $ret = array();
        if ($this->yid) {
            $semesters = Yii::app()->db->createCommand()
                    ->select('*')
                    ->from('ivy_calendar_semester')
                    ->where('yid=:yid', array(':yid' => $this->yid))
                    ->order('semester_flag ASC')
                    ->queryAll();
            foreach ($semesters as $semester) {
                $ret[$semester['semester_flag']] = $semester;
            }
            return $ret;
        }
        return null;
    }

    public function getCalendarDesc($calendarId = 0, $schoolId = null) {
        $ret = array();
        if ($calendarId && !empty($schoolId)) {
            $this->yid = $calendarId;
            $this->branchid = $schoolId;
        }
        if ($this->yid) {
            $descs = Yii::app()->db->createCommand()
                    ->select('*')
                    ->from('ivy_calendar_outsite')
                    ->where('calendarid=:yid AND ( schoolid = 0 OR schoolid =:branchid)', array(':yid' => $this->yid, ':branchid' => $this->branchid))
                    ->order('schoolid ASC') //排序很重要，校园如果有自定义内容，将覆盖模版里的内容
                    ->queryAll();
            foreach ($descs as $desc) {
                $key = str_replace("-", "", $desc['month']);
                $ret[$key] = $desc;
            }
            return $ret;
        }
        return null;
    }

    public function getCurrentSchoolYearCalendar($schoolId) {

        $sc_cri = new CDbCriteria();
        $sc_cri->compare('branchid', $schoolId);
        $sc_cri->compare('is_selected', 1);
        $sc_info = $this->find($sc_cri);
        if ($sc_info) {
            return $sc_info->yid;
        }
        return 0;
    }
    
    

    /**
     * 获取某一学年校历的主键
     * @param string $schoolId
     * @param int $startYear
     * @return number
     * <AUTHOR>
     */
    public function getCalendarId($schoolId,$startYear)
    {
    	$calendarId = 0;
    	$model = $this->find('branchid=:branchid and startyear=:startyear',array(':branchid'=>$schoolId,':startyear'=>$startYear));
    	if (!empty($model))
    	{
    		$calendarId = $model->yid;
    	}
    	return $calendarId;
    }

    /**
     * @param $schoolid
     * @param string $order
     */
    public function getSchoolCalender($schoolid, $order = 'asc')
    {
        $schoolyear = array();
        $criteria = new CDbCriteria();
        $criteria->compare('branchid', $schoolid);
        $criteria->order='startyear '.$order;
        $years = $this->findAll($criteria);
        $is_show = 0;
        foreach($years as $year){
            if($year->is_selected){
                $is_show = 1;
            }
            if($is_show){
                $schoolyear[$year->yid] = $year->startyear;
            }
        }

        return $schoolyear;
    }
}