<?php

class HrController extends ProtectedController
{
    var $cfg;
    var $cfs;

    public function init() {
        parent::init();
        Yii::import('common.models.staff.*');
        Yii::import('common.models.hr.*');
        $this->cfg = CommonUtils::LoadConfig('CfgHr');
        $this->cfs = CommonUtils::LoadConfig('CfgHr');

        foreach ( Branch::model()->getBranchList(null,true, '') as $branch){
            $this->cfg['branch'][$branch['id']] = array(
                'title' => $branch['title'],
                'type' => $branch['type'],
            );
            $this->cfs['branch'][$branch['id']] =  $branch['title'];
        }
    }

	public function actionIndex($type=1)
	{
        $this->mainMenu = array(
			array(
				'label' => '未激活',
				'url' => array('//operations/hr/index', 'type'=>-1),
			),
			array(
				'label' => '在职',
				'url' => array('//operations/hr/index', 'type'=>1)
			),
			array(
				'label' => '离职',
				'url' => array('//operations/hr/index', 'type'=>0)
			),
		);

        $criteria = new CDbCriteria;
        $criteria->compare('level', $type);
        $criteria->compare('rank', 0);
        $criteria->compare('isstaff', 1);
        $criteria->with='profile';

        $_name = Yii::app()->request->getParam('name', '');
        $_email = Yii::app()->request->getParam('email', '');
        if (isset($_POST['action']) && $_POST['action'] == 'search'){
            if ($_name)
                $criteria->compare('name', $_name, true);
            if ($_email)
                $criteria->compare('email', $_email, true);
        }

        $dataProvider = new CActiveDataProvider('User', array(
            'criteria'=>$criteria,
            'sort' => array(
                'defaultOrder' => 'uname asc',
            ),
        ));

        $nationalityID = array();
        foreach ($dataProvider->getData() as $val){
            $nationalityID[$val->profile->nationality]=$val->profile->nationality;
        }

        if ($nationalityID){
            $criteria = new CDbCriteria;
            $criteria->compare('id', $nationalityID);
            $items=Country::model()->findAll($criteria);
            foreach ($items as $item){
                $this->cfg['country'][$item->id]=CommonUtils::autoLang($item->country, $item->country_cn);
            }
        }

        $this->render('index', array('dataProvider'=>$dataProvider, 'type'=>$type, '_name'=>$_name, '_email'=>$_email));
	}

    public function actionUpdate($id=0, $type=1)
    {
        if ( Yii::app()->user->checkAccess("oSuperAdmin") ){
            $this->mainMenu = array(
                array(
                    'label' => '未激活',
                    'url' => array('//operations/hr/index', 'type'=>-1),
                ),
                array(
                    'label' => '在职',
                    'url' => array('//operations/hr/index', 'type'=>1),
                    'active'=>$type == 1 ? true : false,
                ),
                array(
                    'label' => '离职',
                    'url' => array('//operations/hr/index', 'type'=>0),
                    'active'=>$type == 0 ? true : false,
                ),
            );

            if ($type == 1 || $type == -1)
                unset($this->cfg['level'][-1]);

            $model = User::model()->findByPk($id);
            if (!is_object($model)){
                $model = new User;
                $model->profile = new UserProfile;
                $model->staff = new Staff;
                $model->staffApprover = new StaffApprover;
                $model->setScenario('addStaff');
                $model->profile->setScenario('addStaff');
                $model->staff->setScenario('addStaff');
                $model->staffApprover->setScenario('addStaff');
            }
            else {
                if (empty($model->staffApprover)){
                    $model->staffApprover = new StaffApprover;
                }
                $model->setScenario('editStaff');
                $model->profile->setScenario('editStaff');
                $model->staff->setScenario('editStaff');
                $model->staffApprover->setScenario('editStaff');
                $model->staffApprover->approvers = StaffApprover::getApproverUserBySystem($model->profile->branch);
            }
            if (!$model->isNewRecord && $model->level == -1){
                $model->setScenario('addStaff');
                $model->profile->setScenario('addStaff');
                $model->staff->setScenario('addStaff');
            }

            if ( isset($_POST['User'], $_POST['UserProfile'], $_POST['Staff']) ){

                $model->attributes = $_POST['User'];
                $model->profile->attributes = $_POST['UserProfile'];
                $model->staff->attributes = $_POST['Staff'];
                $model->staffApprover->attributes = $_POST['StaffApprover'];

                if ($model->getScenario() == 'addStaff'){
                    $model->user_regdate = time();
                }
                $model->isstaff = 1;
                $model->iniPassword = trim($model->iniPassword);
                if ($model->iniPassword){
                    $model->pass = md5($model->iniPassword);
                }

                $model->profile->first_name = ucfirst(strtolower($model->profile->first_name));
                $model->profile->last_name = ucfirst(strtolower($model->profile->last_name));
                $model->uname = $model->profile->first_name.' '.substr($model->profile->last_name, 0, 1);
                $model->timezone_offset = 8.0;
                $model->flag = 2;
                $model->staff->card_id_due = strtotime($_POST['Staff']['card_id_due']);
                $model->staff->startdate = strtotime($_POST['Staff']['startdate']);
                $model->staff->leavedate = strtotime($_POST['Staff']['leavedate']);
                $model->staff->contract_period = strtotime($_POST['Staff']['contract_period']);
                $model->uploadPhoto = CUploadedFile::getInstance($model, 'uploadPhoto');
                $oldPhoto = $model->user_avatar;

                $valid=$model->validate();
                $valid=$model->profile->validate() && $valid;
                $valid=$model->staff->validate() && $valid;
                $valid=$model->staffApprover->validate() && $valid;

                if ($valid){
                    if ($model->uploadPhoto){
                        $delold = ($oldPhoto == 'blank.gif') ? false : true;
                        $upResult = OA::processPicUpload($model->uploadPhoto, 'userPhoto', $oldPhoto, $delold);
                        $model->user_avatar = count($upResult)==3 ? $upResult['filename'] : 'blank.gif';
                    }
                    $model->save(false);

                    $staffprimarygroup = 0;
                    # Xoops 权限
                    $staffgroups = array(XoGroups::STAFFGROUP);
                    $auth = DepPosLink::model()->findByPk($model->profile->occupation_en);
                    if ($auth){
                        XoGroupsUsersLink::model()->removeAllGroup($model->uid);
                        $staffgroups = array_merge($staffgroups, explode(',', $auth->authority));
                        XoGroupsUsersLink::model()->addUserToGroup(XoGroups::USERGROUP, $model->uid);
                        foreach ($staffgroups as $groupid){
                            XoGroupsUsersLink::model()->addUserToGroup($groupid, $model->uid);
                            if (!$staffprimarygroup && ($groupid != XoGroups::STAFFGROUP))
                                $staffprimarygroup = $groupid;
                        }
                    }

                    # Yii 权限
                    Yii::import('srbac.models.*');
                    if ($model->uid){
                        $criteria=new CDbCriteria();
                        $criteria->compare('userid', $model->uid);
                        $criteria->compare('itemname', '<>superDude');
                        Assignments::model()->deleteAll($criteria);

                        /* 根据xoops权限 插入Yii角色
                        $criteria=new CDbCriteria();
                        $criteria->compare('groupid', $staffgroups);
                        $gs = XoGroups::model()->findAll($criteria);
                        foreach ($gs as $g){
                            $amodel = new Assignments;
                            $amodel->itemname = $g->group_type;
                            $amodel->userid = $model->uid;
                            $amodel->save();
                        }
                        */

                        # 如果放开上面的代码 请注释本段代码
                        $criteria = new CDbCriteria();
                        $criteria->compare('title_id', $model->profile->occupation_en);
                        $gs = HrTitleRole::model()->findAll($criteria);
                        foreach ($gs as $g){
                            $amodel = new Assignments;
                            $amodel->itemname = $g->role;
                            $amodel->userid = $model->uid;
                            $amodel->save();
                        }
                    }

                    # 同步搜索表
                    StaffSync::model()->sync($model->uid);

                    $model->profile->uid=$model->uid;
                    $model->profile->staffprimarygroup=$staffprimarygroup;
                    $model->profile->save(false);
                    $model->staff->sid=$model->uid;
                    $model->staff->department = $auth->department_id;
                    $model->staff->save(false);
                    $model->staffApprover->staff_uid=$model->uid;
                    $model->staffApprover->updated=time();
                    $model->staffApprover->updator=Yii::app()->user->getId();
                    $model->staffApprover->save(false);

                    $emailTip = '';
                    if ($model->getScenario() == 'addStaff'){
                        if(OA::isProduction()){
//                            Yii::import('application.components.user.ExMail');
//                            $exMail = new ExMail();
//                            $postParams = array(
//                                'action' => 2,
//                                'Alias' => trim($model->email),
//                                'Name' => trim($model->getName()),
//                                'Gender' => $model->profile->user_gender,
//                                'Position' => '',
//                                'ExtId' => $model->uid,
//                                'Password' => $model->pass,
//                                'Md5' => 1,
//                                'PartyPath' => sprintf('/%s', $this->cfg['branch'][$model->profile->branch]['title']),
//                                'OpenType' => 1,
//                            );
//                            $ret = $exMail->AddUser($postParams);
//                            $emailTip = '';
//                            if($ret != 1){
//                                $emailTip = '邮箱没有创建成功！';
//                            }
                        }

                        # 发帐号信到私人邮件
                        if(isset($_POST['isSend']) && $_POST['isSend']){
                            $mailer = Yii::createComponent('common.extensions.mailer.Aliyun');
                            $mailer->AddAddress($model->staff->pemail);

                            if($this->cfg['branch'][$model->profile->branch]['type'] == 50){
                                $mailer->Subject = $model->profile->nationality == 36 ? '欢迎加入启明星' : 'Welcome to Daystar';
                                $mailer->AddReplyTo('<EMAIL>');
                                $mview = 'dsstaffaccount';
                            }
                            else{
                                $mailer->Subject = $model->profile->nationality == 36 ? '欢迎加入艾毅' : 'Welcome to Ivy';
                                $mailer->AddReplyTo('<EMAIL>');
                                $mview = 'staffaccount';
                            }

                            $mailer->iniMail( OA::isProduction() ); // 此行代码要放到AddAddress, AddCC方法下面
                            $mailer->getView($mview, array('model'=>$model));
                            $mailer->Send();
                        }
                        if($this->cfg['branch'][$model->profile->branch]['type'] != 50) {
                            Staff::updateWechat($model->uid, 'create');
                        }
                    }else {
                        if (!$model->isNewRecord) {
                            if($this->cfg['branch'][$model->profile->branch]['type'] != 50){
                                Staff::updateWechat($model->uid, 'update');
                            }
                        }
                    }

                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message','success').$emailTip);
                    $this->addMessage('refresh', true);
                    $this->addMessage('referer', $this->createUrl('/operations/hr/index', array('type'=>1)));
                }
                else {
                    $this->addMessage('state', 'fail');
                    $errs = current($model->getErrors());
                    $errs = !$errs ? current($model->profile->getErrors()) : $errs;
                    $errs = !$errs ? current($model->staff->getErrors()) : $errs;
                    $errs = !$errs ? current($model->staffApprover->getErrors()) : $errs;
                    $this->addMessage('message', $errs?$errs[0]:Yii::t('message','Failed!'));
                }
                $this->showMessage();
            }

            $model->staff->card_id_due = OA::formatDateTime($model->staff->card_id_due);
            $model->staff->startdate = OA::formatDateTime($model->staff->startdate);
            $model->staff->leavedate = OA::formatDateTime($model->staff->leavedate);
            $model->staff->contract_period = OA::formatDateTime($model->staff->contract_period);
            $this->render('update', array('model'=>$model));
        }
    }

    public function actionAddStaff($id=0)
    {
        if ( Yii::app()->user->checkAccess("oSuperAdmin") ){
            $this->mainMenu = array(
                array(
                    'label' => '未激活',
                    'url' => array('//operations/hr/index', 'type'=>-1),
                    'active'=>true,
                ),
                array(
                    'label' => '在职',
                    'url' => array('//operations/hr/index', 'type'=>1),
                ),
                array(
                    'label' => '离职',
                    'url' => array('//operations/hr/index', 'type'=>0)
                ),
            );
            $model = User::model()->findByPk($id);
            if (!is_object($model)){
                $model = new User;
                $model->profile = new UserProfile;
                $model->staff = new Staff;
                $model->setScenario('addStaff');
                $model->profile->setScenario('addStaff');
                $model->staff->setScenario('addStaff');
            }
            else {
                $model->setScenario('editStaff');
                $model->profile->setScenario('editStaff');
                $model->staff->setScenario('editStaff');
            }

            if ( isset($_POST['User'], $_POST['UserProfile'], $_POST['Staff']) ){
                $model->attributes = $_POST['User'];
                $model->profile->attributes = $_POST['UserProfile'];
                $model->staff->attributes = $_POST['Staff'];

                $newRecord = $model->isNewRecord;
                if ($newRecord){
                    $model->user_regdate = time();
                }
                $model->isstaff = 1;
                $model->level = -1;
                $model->iniPassword = 'barneybarney'; # 为了配合场景 激活帐号会重置密码
                $model->staff->emailPass = 'barneybarney'; # 为了配合场景 激活帐号会重置密码

                $model->profile->first_name = ucfirst(strtolower($model->profile->first_name));
                $model->profile->last_name = ucfirst(strtolower($model->profile->last_name));
                $model->uname = $model->profile->first_name.' '.substr($model->profile->last_name, 0, 1);
                $model->timezone_offset = 8.0;
                $model->flag = 2;
                $model->staff->startdate = strtotime($_POST['Staff']['startdate']);

                $valid=$model->validate();
                $valid=$model->profile->validate() && $valid;
                $valid=$model->staff->validate() && $valid;

                if ($valid){
                    $model->save(false);

                    $staffprimarygroup = 0;
                    # Xoops 权限
                    $staffgroups = array(XoGroups::STAFFGROUP);
                    $auth = DepPosLink::model()->findByPk($model->profile->occupation_en);
                    if ($auth){
                        XoGroupsUsersLink::model()->removeAllGroup($model->uid);
                        $staffgroups = array_merge($staffgroups, explode(',', $auth->authority));
                        XoGroupsUsersLink::model()->addUserToGroup(XoGroups::USERGROUP, $model->uid);
                        foreach ($staffgroups as $groupid){
                            XoGroupsUsersLink::model()->addUserToGroup($groupid, $model->uid);
                            if (!$staffprimarygroup)
                                $staffprimarygroup = $groupid;
                        }
                    }

                    # Yii 权限
                    Yii::import('srbac.models.*');
                    if ($model->uid){
                        $criteria=new CDbCriteria();
                        $criteria->compare('userid', $model->uid);
                        $criteria->compare('itemname', '<>superDude');
                        Assignments::model()->deleteAll($criteria);

                        /*$criteria=new CDbCriteria();
                        $criteria->compare('groupid', $staffgroups);
                        $gs = XoGroups::model()->findAll($criteria);
                        foreach ($gs as $g){
                            $amodel = new Assignments;
                            $amodel->itemname = $g->group_type;
                            $amodel->userid = $model->uid;
                            $amodel->save();
                        }*/

                        # 如果放开上面的代码 请注释本段代码
                        $criteria = new CDbCriteria();
                        $criteria->compare('title_id', $model->profile->occupation_en);
                        $gs = HrTitleRole::model()->findAll($criteria);
                        foreach ($gs as $g){
                            $amodel = new Assignments;
                            $amodel->itemname = $g->role;
                            $amodel->userid = $model->uid;
                            $amodel->save();
                        }
                    }

                    # 同步搜索表
                    StaffSync::model()->sync($model->uid);

                    $model->profile->uid=$model->uid;
                    $model->profile->staffprimarygroup=$staffprimarygroup;
                    $model->profile->save(false);
                    $model->staff->sid=$model->uid;
                    $model->staff->department = $auth->department_id;
                    $model->staff->save(false);

                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message','success'));
                    $this->addMessage('refresh', true);
                    $this->addMessage('referer', $this->createUrl('/operations/hr/index', array('type'=>-1)));
                }
                else {
                    $this->addMessage('state', 'fail');
                    $errs = current($model->getErrors());
                    $errs = !$errs ? current($model->profile->getErrors()) : $errs;
                    $errs = !$errs ? current($model->staff->getErrors()) : $errs;
                    $this->addMessage('message', $errs?$errs[0]:Yii::t('message','Failed!'));
                }
                $this->showMessage();
            }

            $this->render('addstaff', array('model'=>$model));
        }
    }

    public function actionGetUser(){
        $branchid = Yii::app()->request->getPost('branchid',0);
        $data = StaffApprover::getApproverUserBySystem($branchid);
        echo CJSON::encode($data);
    }
}
