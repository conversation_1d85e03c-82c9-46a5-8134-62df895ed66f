<?php
$this->widget('zii.widgets.CMenu', array(
    'items' => $pageSubMenu,
    'activeCssClass' => 'current',
    'id' => 'page-subMenu',
    'htmlOptions' => array('class' => 'pageSubMenu')
));
echo CHtml::openTag("div", array("class" => "c"));
echo CHtml::closeTag("div");
?>
<style>
    .charts{
        color: #EA740C;
        display: inline-block;
        width: 150px;
    }
    div.itemOp a {
        display: inline-block;
        height: 16px;
        margin-right: 4px;
        text-indent: -9999px;
        width: 16px;
    }
    table td.memo:hover div.itemOp {
        visibility: visible;
    }
    
</style>
<div id="itemOp-template" style="display: none;">
	<a href="javascript:void(0);" onclick="openWind(this);" class="icon-tag J_dialog">Q</a>
</div>
<div class="h_a">详细描述</div>
<div class="table_full">
    <table width="100%" code="<?php echo $_GET['code'];?>">
        <colgroup>
            <col width="180">
        </colgroup>
        <tbody>
            <tr>
                <th></th>
                <?php foreach ($subCode as $key=>$val):?>
                <th><?php echo $val;?></th>
                <?php endforeach;?>
            </tr>
            <?php 
                for ($i=0;$i<12;$i++):
                $_time = mktime(0,0,0,date('m',$fallStart)+$i,1,date('Y',$fallStart));
                $_month = date('Ym',  $_time);
                $_format_month = date('Y/m',  $_time);
            ?>
            <tr month="<?php echo $_month;?>">
                <th><?php echo $_format_month;?></th>
                <?php foreach ($subCode as $key=>$val):?>
                <td bid="<?php echo $financeData[$_month][$key]['id'];?>" subcode="<?php echo $key;?>"  <?php if (isset($financeData[$_month][$key]) && $_GET['code'] == IvyStatsItem::CODE_FINANCE_ACTUAL):?>class="memo" <?php endif;?>>
                    <span class="charts">
                        <?php 
                            if (isset($financeData[$_month][$key])):
                                if (in_array($key, array(IvyStatsItem::SUBCODE_FINANCE_ENROLLMENT,IvyStatsItem::SUBCODE_FINANCE_TEACHERS,IvyStatsItem::SUBCODE_FINANCE_ADMIN))):
                                    echo round($financeData[$_month][$key]['data']);
                                else:
                                    echo $financeData[$_month][$key]['data'];
                                endif;
                            else:
                                if (in_array($key, array(IvyStatsItem::SUBCODE_FINANCE_ENROLLMENT,IvyStatsItem::SUBCODE_FINANCE_TEACHERS,IvyStatsItem::SUBCODE_FINANCE_ADMIN))):
                                    echo '0';
                                else:
                                    echo '0.00';
                                endif;
                               
                            endif;
                        ?>
                    </span>
                </td>
                <?php endforeach;?>
            </tr>
            <?php endfor;?>
        </tbody>
    </table>
</div>
<?php
$this->renderPartial('_charts_js',array('jsMemo'=>CJSON::encode($jsMemo)));
?>
<!--打开备注表单信息窗开始-->
<div id="J_dialog_memo" style="display:none;" class="core_pop_wrap" >
    <?php echo CHtml::beginForm($this->createUrl('//operations/charts/updateMemo'),'POST',array('class'=>'J_ajaxForm','id'=>'tag-form'));?>
    <div class="core_pop">
        <div class="pop_top">
				<a href="" id="J_dialog_memo_pop_x" class="pop_close"><?php echo Yii::t('global','Close');?></a>
				<strong id='d_tit'><?php echo Yii::t('charts', '编辑备注信息'); ?></strong>
		</div>
        <div class="pop_cont" style="height:auto;">
            <div class="tips_light"><?php echo Yii::t("charts", '本功能给分类下金额添加简单的备注信息')?></div>
            <p><?php echo CHtml::textArea('memo','',array('cols'=>39)); ?></p>
            <p><?php echo CHtml::hiddenField('id', $model->id) ?></p>
        </div>
        <div class="pop_bottom">
            <button type="button" class="btn fr" id="J_dialog_close"><?php echo Yii::t('global','Cancel');?></button>
            <button class="btn fr btn_submit mr10 J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
        </div>
    </div>
    <?php echo CHtml::endForm();?>
</div>
<!--提示窗结束-->
