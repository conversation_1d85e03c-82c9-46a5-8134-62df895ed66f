<style>
    #user-leave-info{
        padding-left: 20px;
    }
    #user-leave-info h5{
        padding: 10px 0px;
    }
    .table-b td{
        border-top: 8px solid #00a353;   
        border-right: 1px solid #e1e1e1;   
        border-bottom: 1px solid #e1e1e1;   
        border-left: 1px solid #e1e1e1;  
        width:260px;
        height:170px;
        background-color:#eef9fd;
        border-top: 8px solid #d36b68;
        background-color:#fff;
    }
    .table-b td:hover{
        border-top: 8px solid #00a353;
        background-color:#eef9fd;
    }
    
    .user-leave-header{
        border-bottom: 1px solid #e1e1e1;
        height:120px;
    }
    .user-leave-foot{
        height:50px;
        line-height: 50px;
    }
    .user-leave-header .fl{
        width: 115px;
    }
    .user-leave-header .u-type{

    }
    .user-leave-header .u-type div{
        line-height: 90px;
        width: 90px;
        height: 90px;
        margin: 10px auto;
        border-radius: 50%;
        border: 1px solid #D37470;
        color: #D37470;
        font-size: 26px;
    }
    .user-leave-header .u-count{
        margin: 10px 0;
    }
    .user-leave-header .u-count div{
        height: 30px;
        line-height: 30px;
    }
</style>
<?php
    $leaveType = UserLeave::getLeaveType();
?>
<div id="user-leave-info">
<h5>个人当前假期统计（2013.09.01 - 2014.09.30）</h5>
<table class="table-b">
    <tr>
        <?php 
        $i=1;
        foreach ($leaveType as $key=>$val):
        ?>
        <td>
            <div class="user-leave-header">
                <div class="fl u-type">
                    <div class="tac"><?php echo $val;?></div>
                </div>
                <div class="fl u-count">
                    <div>【已请】：<?php echo isset($data['useLeaveItem'][$key]['agree']['hours']) ? CHtml::link(UserLeave::getLeaveFormat($data['useLeaveItem'][$key]['agree']['hours']),$this->createUrl('/user/hr/leaveView',array('type'=>$key,'status'=>LeaveItem::STATUS_AGREE)),array('class'=>'J_dialog','title'=> $val.Yii::t('user','已请假期列表'))) : '0天';?></div>
                    <div>【正在进行】：<?php echo isset($data['useLeaveItem'][$key]['waiting']['hours']) ?  CHtml::link(UserLeave::getLeaveFormat($data['useLeaveItem'][$key]['waiting']['hours']),$this->createUrl('/user/hr/leaveView',array('type'=>$key,'status'=>LeaveItem::STATUS_WAITING)),array('class'=>'J_dialog','title'=> $val.Yii::t('user','等待审核假期列表'))) : '0天';?></div>
                    <?php if  (in_array($key, array(UserLeave::TYPE_ANNUAL_LEAVE,UserLeave::TYPE_SICK_LEAVE,UserLeave::TYPE_DAYS_OFF))):?>
                    <div>【总共】：<?php echo isset($data['leaveList'][$key]['hours']) ? UserLeave::getLeaveFormat($data['leaveList'][$key]['hours']) : '0天';?></div>
                    <?php endif;?>
                </div>
                <div class="c"></div>
            </div>
            <div class="user-leave-foot tac">
                <a class="btn btn_submit J_dialog" href="<?php echo $this->createUrl('/user/hr/index',array('t'=>'leave','type'=>$key));?>" title="<?php echo $val;?>申请">申请</a>
            </div>
        </td>
        <?php
            if ($i%4==0){
                echo "</tr><tr>";
            }
            $i++;
        ?>
        <?php endforeach;?>
    </tr>

</table>
</div>