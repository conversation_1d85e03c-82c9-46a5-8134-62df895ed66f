<?php

class QiniuController extends Controller
{
    public function init() {
        /*parent::init();
        if (OA::isProduction()){
            Yii::app()->end();
        }*/
    }

    public function actionIndex()
    {
        require_once( Yii::getPathOfAlias('common.components.qiniu.auth_digest') . '.php' );
        $serverConfs = CommonUtils::LoadConfig('CfgOnlineServer');
        $auth = new Qiniu_Mac($QINIU_ACCESS_KEY, $QINIU_SECRET_KEY);
        //获取回调的body信息
        $callbackBody = file_get_contents('php://input');
        Yii::log($callbackBody, 'info', 'callbackIndex1');
        //回调的contentType
        $contentType = 'application/json';
        //回调的签名信息，可以验证该回调是否来自七牛
        $authorization = $_SERVER['HTTP_AUTHORIZATION'];
        //七牛回调的url，具体可以参考：http://developer.qiniu.com/docs/v6/api/reference/security/put-policy.html

        $sConfs = (!OA::isProduction()) ? $serverConfs['dev'] : $serverConfs['prod'];
        $url = $sConfs['callBackUrl'];
        $isQiniuCallback = $auth->verifyCallback($authorization, $url, $callbackBody);
        $resp = array('state' => 'failed');
        if ($isQiniuCallback) {
            if(isset($callbackBody)) {
                $time = time();
                $callbackData = CJSON::decode($callbackBody, true);
                Yii::log($callbackBody, 'info', 'callbackIndex2');
                Yii::app()->language = $callbackData['l'];
                Yii::import('common.models.online.*');
                $filename = urldecode($callbackData['fname']);
                $display = str_replace(strrchr($filename, "."),"",$filename);
                $model = new OnlineFile();
                $model->school_id = $callbackData['s'];
                $model->pid = $callbackData['pid'];
                $model->display = $display;
                $model->type = $callbackData['filetype'];
                $model->path_original = $callbackData['key'];
                $model->handle_status = ($callbackData['filetype'] == 'video') ? 0 : 1;
                $model->size = $callbackData['fsize'];
                $model->status = 1;
                $model->created_at = $time;
                $model->created_by = $callbackData['u'];
                $model->updated_at = $time;
                $model->updated_by = $callbackData['u'];
                if ($model->save()) {
                    $userName = User::model()->findByPk($model->created_by);
                    $resp['data'] = array(
                        'id' => $model->id,
                        'school_id' => $model->school_id,
                        'pid' => $model->pid,
                        'type' => $model->type,
                        'display' => $model->display,
                        'path_original' => $sConfs['url'] . '/' . $model->path_original,
                        'path_processed' => '',
                        'thumbnail' => '',
                        'handle_status' => $model->handle_status,
                        'size' => $model->switchSize($model->size),
                        'status' => $model->status,
                        'created_at' => date("Y-m-d H:i:s", $time),
                        'created_by' => ($userName) ? $userName->getName() : $model->created_by,
                        'updated_at' => date("Y-m-d H:i:s", $time),
                        'updated_by' => ($userName) ? $userName->getName() : $model->updated_by,
                    );
                    $resp['state'] = 'success';
                    $resp['message'] = 'success';
                } else {
                    $error = current($model->getErrors());
                    $resp['message'] = $error[0];
                }
            }
        }
        echo CJSON::encode($resp);
    }

    public function actionVideoTranscoding()
    {
        $callbackBody = file_get_contents('php://input');
        if(isset($callbackBody)) {
            $callbackData = CJSON::decode($callbackBody, true);
            Yii::log($callbackBody, 'info', 'callbackData');
            Yii::import('common.models.online.*');
            if (isset($callbackData) && isset($callbackData['inputKey'])) {
                $criteria = new CDbCriteria();
                $criteria->compare('path_original', $callbackData['inputKey']);
                $onlineFoleModel = OnlineFile::model()->findAll($criteria);
                if ($onlineFoleModel) {
                    foreach ($onlineFoleModel as $val){
                        $val->path_processed = $callbackData['items'][1]['key'];
                        $val->handle = $callbackData['items'][1]['cmd'];
                        $val->handle_status = 1;
                        $val->thumbnail = $callbackData['items'][0]['key'];
                        if (!$val->save()) {
                            Yii::log($callbackBody, 'error', 'callbackSaveError');
                        }
                    }
                }
            }
        }
        echo 'success';
    }

    // 打包图片回调
    public function actionPackpic()
    {
        $callbackBody = file_get_contents('php://input');
        if(isset($callbackBody)) {
            $callbackData = CJSON::decode($callbackBody, true);
            Yii::log($callbackBody, 'info', 'packpic');
            Yii::import('common.models.portfolio.*');
            if (isset($callbackData) && isset($callbackData['inputKey'])) {
                if($callbackData['items'][0]['code'] < 1) {
                    $listurl = base64_decode(substr($callbackData['inputKey'],6));
                    $criteria = new CDbCriteria();
                    $criteria->compare('listurl', $listurl);
                    $taskPackpicModel = TaskPackpic::model()->find($criteria);
                    $emails = array();
                    if ($taskPackpicModel && !$taskPackpicModel->zipurl) {
                        $emails[] = $taskPackpicModel->pemail;
                        $taskPackpicModel->zipurl = $callbackData['items'][0]['key'];
                        $taskPackpicModel->uploaded_at = time();
                        $taskPackpicModel->save();
                    }
                    if ($emails) {
                        $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
                        $mailer->Subject = "Ivy Schools Photo Download 艾毅幼儿园图片下载";
                        foreach ($emails as $val){
                            $mailer->AddAddress($val);
                        }
                        //$mailer->AddAddress('<EMAIL>');
                        //$mailer->AddCC('<EMAIL>'); //抄送
                        $mailer->getCommandView('packpic', array(
                            'data' => 1,
                        ), 'main');
                        $isProduction = OA::isProduction();
                        $mailer->iniMail($isProduction);
                        if(!$mailer->Send()){
                            Yii::log(CJSON::encode($mailer->getError()), 'error', 'packpic.sendmail');
                        }
                        echo 'success';
                    }
                }
            }
        }
    }
}
