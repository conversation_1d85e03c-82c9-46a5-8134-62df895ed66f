<?php
/**
 * User: XINRAN
 * Date: 14-5-13
 * Time: 上午10:19
 */
class OAMenu
{
    static function getMenu($category="main"){
        //主菜单
        $userMenu['main'] = array(
//            'user' => array(
//                'label'=> '<span class="glyphicon glyphicon-user"></span> ' . Yii::t('site','User Center').' <b class="caret"></b>',
//                'itemOptions'=>array('class'=>'dropdown'),
//                'url'=>'#',
//                'linkOptions'=>array('class'=>'dropdown-toggle', 'data-toggle'=>'dropdown'),
//                'submenuOptions'=>array('class' => 'dropdown-menu'),
//                'items'=>array(
//                    'profile' => array('label'=>Yii::t('site','My Profile'), 'url'=>array('//user/profile/index','category'=>'basic'),'roles'=>array('ivystaff')),
//                    'lvot' => array('label'=>Yii::t('site','Leave & Overtime'), 'url'=>array("//user/leaveot"),'roles'=>array('ivystaff')),
//                )
//            ),
            'pub' => array(
                'label'=> '<span class="glyphicon glyphicon-th-large"></span> ' . Yii::t('site','Ivy Family').' <b class="caret"></b>',
                'itemOptions'=>array('class'=>'dropdown'),
                'url'=>'#',
                'linkOptions'=>array('class'=>'dropdown-toggle', 'data-toggle'=>'dropdown'),
                'submenuOptions'=>array('class' => 'dropdown-menu'),
                'items'=>array(
                    'campus' => array('label'=>Yii::t('site','Campuses'), 'url'=>array('//mpub/default/campus'),'roles'=>array('ivystaff')),
                    'ivyer' => array('label'=>Yii::t('site','Ivyers'), 'url'=>array("//mpub/default/ivyers"),'roles'=>array('ivystaff')),
                    'kb' => array('label'=>Yii::t('site','KnowledgeBase'), 'url'=>Yii::app()->params['OABaseUrl'].'/modules/docscenter/','roles'=>array('ivystaff'), 'linkOptions'=>array('target'=>'_blank')),
                    'wechatMsg' => array('label'=>Yii::t('site','微信推送统计'), 'url'=>array("//mpub/default/wechatMsg"),'roles'=>array('ivystaff_it')),
                )
            ),
            'campusw' => array(
                'label'=> '<span class="glyphicon glyphicon-file"></span> ' . Yii::t('site','Campus Workspace').' <b class="caret"></b>',
                'itemOptions'=>array('class'=>'dropdown'),
                'url'=>'#',
                'linkOptions'=>array('class'=>'dropdown-toggle', 'data-toggle'=>'dropdown'),
                'submenuOptions'=>array('class' => 'dropdown-menu'),
                'items'=>array(
                    'op' => array('label'=>Yii::t('site','Campus Operations'), 'url'=>array('//mcampus/default/index'),
                        'roles'=>array('ivystaff_opschool', 'ivystaff_opgeneral', 'ivystaff_tygmgt', 'ivystaff_depthead', 'ivystaff_eduhead', 'ivystaff_it', 'ivystaff_hr', 'ivystaff_finance', 'ivystaff_communicate', 'ivystaff_doctor', 'ivystaff_teacher', 'ivystaff_counselor')),
                    'teaching' => array('label'=>Yii::t('site','Teaching Tasks'), 'url'=>array("//mteaching/default/index"),
                        'roles'=>array('ivystaff_teacher', 'ivystaff_opschool', 'ivystaff_eduhead', 'ivystaff_it', 'ivystaff_counselor')),
                    'lib' => array('label'=>Yii::t('site','Library Management'), 'url'=>array("//mlibrary/default/index"),
                        'roles'=>array('ivystaff_opschool', 'ivystaff_library', 'ivystaff_it')),
                )
            ),
            'finance' => array(
                'label'=> '<span class="glyphicon glyphicon-file"></span> ' . Yii::t('site','Finance Mgt').' <b class="caret"></b>',
                'itemOptions'=>array('class'=>'dropdown'),
                'url'=>'#',
                'linkOptions'=>array('class'=>'dropdown-toggle', 'data-toggle'=>'dropdown'),
                'submenuOptions'=>array('class' => 'dropdown-menu'),
                'items'=>array(
                    'invoice' => array('label'=>Yii::t('payment','Invoice Management'), 'url'=>array('//mfinance/invoice/index'),'roles'=>array('ivystaff_opschool', 'ivystaff_it')),
                    'A' => array('label'=>Yii::t('site','对账管理'), 'url'=>array('//mfinance/statements/index'),'roles'=>array('ivystaff_it', 'ivystaff_finance')),
                    'commission' => array('label'=>Yii::t('site','手续费查询'), 'url'=>array('//mfinance/commission/index'),'roles'=>array('ivystaff_it', 'ivystaff_finance')),
                    'gov' => array('label'=>Yii::t('site','补贴和代收'), 'url'=>array('//mfinance/gov/index'),'roles'=>array('ivystaff_it', 'ivystaff_finance')),
                    'B' => array('label'=>Yii::t('payment','Cash Handover'), 'url'=>array("//mfinance/cash/index"),'roles'=>array('ivystaff_it', 'ivystaff_finance')),
//                    'C' => array('label'=>Yii::t('site','发票管理'), 'url'=>array("//mfinance/fapiao/index"),'roles'=>array('ivystaff_opschool', 'ivystaff_it')),
                    'D' => array('label'=>Yii::t('site','学生费用报表'), 'url'=>array("//mfinance/student/index"),'roles'=>array('ivystaff_opschool', 'ivystaff_it', 'ivystaff_finance')),
                    'E' => array('label'=>Yii::t('site','银行转帐'), 'url'=>array("//mfinance/bank/index"),'roles'=>array('ivystaff_it', 'ivystaff_finance')),
                    'F' => array('label'=>Yii::t('site','个人账户查询'), 'url'=>array("//mfinance/credit/index"),'roles'=>array('ivystaff_finance', 'ivystaff_it')),
                    'G' => array('label'=>Yii::t('site','付款记录查询'), 'url'=>array("//mfinance/payment/index"),'roles'=>array('ivystaff_finance', 'ivystaff_it')),
                    'H' => array('label'=>Yii::t('site','课后课财务'), 'url'=>array("//mfinance/asa/index"),'roles'=>array('ivystaff_finance', 'ivystaff_it', 'ivystaff_asa', 'ivystaff_pd')),
                )
            ),
            'hq' => array(
                'label'=> '<span class="glyphicon glyphicon-wrench"></span> ' . Yii::t('site','HQ Workspace').' <b class="caret"></b>',
                'itemOptions'=>array('class'=>'dropdown'),
                'url'=>'#',
                'linkOptions'=>array('class'=>'dropdown-toggle', 'data-toggle'=>'dropdown'),
                'submenuOptions'=>array('class' => 'dropdown-menu'),
                'items'=>array(
//                    'op' => array('label'=>Yii::t('site','HQ Operations'), 'url'=>array('//moperation/global/discount'), 'roles'=>array('ivystaff_tygmgt', 'ivystaff_opgeneral', 'ivystaff_eduhead', 'ivystaff_depthead', 'ivystaff_it')),
                    'op' => array('label'=>Yii::t('site','HQ Operations'), 'url'=>array('//moperation/default/index'), 'roles'=>array('ivystaff_tygmgt', 'ivystaff_opgeneral', 'ivystaff_eduhead', 'ivystaff_depthead', 'ivystaff_it', 'ivystaff_communicate')),
//                    'hr' => array('label'=>Yii::t('site','HR Management'), 'url'=>array('//mhr/default/index','category'=>'base'), 'roles'=>array('ivystaff_it', 'ivystaff_hr')),
                    'system' => array('label'=>Yii::t('site','System Management'), 'url'=>array("//msettings/default/index"), 'roles'=>array('ivystaff_it', 'ivystaff_hr')),
                )
            ),
        );

        //总部运营
        $userMenu['hqOp'] = array(
            'cfg' => array(
                'label'=> '<span class="glyphicon glyphicon-book"></span> ' . Yii::t('site','Basic Configurations').' <b class="caret"></b>',
                'itemOptions'=>array('class'=>'dropdown'),
                'url'=>'#',
                'linkOptions'=>array('class'=>'dropdown-toggle', 'data-toggle'=>'dropdown'),
                'submenuOptions'=>array('class' => 'dropdown-menu'),
                'items'=>array(
                    'discount' => array('label'=>Yii::t('site','Discount Management'), 'url'=>array('//moperation/global/discount'),'roles'=>array('ivystaff_it', 'ivystaff_communicate', 'ivystaff_opgeneral')),
                    'calendar' => array('label'=>Yii::t('site','School Calendar Template'), 'url'=>array('//moperation/calendarNew/index'),'roles'=>array('ivystaff_it', 'ivystaff_opgeneral')),
                    'mailersender' => array('label'=>Yii::t('site','Mailer Sender'),'url'=>array('//moperation/misc/mailsender'),'roles'=>array('ivystaff_it')),
                    'workflow' => array('label'=>Yii::t('site','Workflow Management'),'url'=>array('//workflow/admin/index'),'roles'=>array('ivystaff_it')),
                    'Security' => array('label'=>Yii::t('site','安全事故模板'),'url'=>array('//moperation/security/index'),'roles'=>array('ivystaff_it','ivystaff_tygmgt')),
                )
            ),
            'marketing' => array(
                'label'=> '<span class="glyphicon glyphicon-book"></span> ' . Yii::t('site','Marketing').' <b class="caret"></b>',
                'itemOptions'=>array('class'=>'dropdown'),
                'url'=>'#',
                'linkOptions'=>array('class'=>'dropdown-toggle', 'data-toggle'=>'dropdown'),
                'submenuOptions'=>array('class' => 'dropdown-menu'),
                'items'=>array(
                    'activity' => array('label'=>Yii::t('site','Marketing Activity'), 'url'=>array('//moperation/admissions/index'),'roles'=>array('ivystaff_it', 'ivystaff_communicate')),
                    'emailmgt' => array('label'=>Yii::t('site','邮件地址管理'), 'url'=>array('//moperation/emailmgt/index'),'roles'=>array('ivystaff_it')),
                )
            ),
            'support' => array(
                'label'=> '<span class="glyphicon glyphicon-book"></span> ' . Yii::t('site','Support').' <b class="caret"></b>',
                'itemOptions'=>array('class'=>'dropdown'),
                'url'=>'#',
                'linkOptions'=>array('class'=>'dropdown-toggle', 'data-toggle'=>'dropdown'),
                'submenuOptions'=>array('class' => 'dropdown-menu'),
                'items'=>array(
                    'expoint' => array('label'=>Yii::t('site','Points Exchange Order'), 'url'=>array('//moperation/order/admin'),'roles'=>array('ivystaff_it', 'ivystaff_opgeneral')),
                    'exstuff' => array('label'=>Yii::t('site','Exchange Items'), 'url'=>array('//moperation/product/admin'),'roles'=>array('ivystaff_it', 'ivystaff_opgeneral')),
                    'procure' => array('label'=>Yii::t('site','采购商品管理'), 'url'=>array('//moperation/purchaseProducts/index'),'roles'=>array('ivystaff_it', 'ivystaff_opgeneral', 'ivystaff_tygmgt')),
                    'vendor' => array('label'=>Yii::t('site','外包公司'), 'url'=>array('//moperation/vendor/index'),'roles'=>array('ivystaff_it', 'ivystaff_opgeneral', 'ivystaff_tygmgt')),
                    'survey' => array('label'=>Yii::t('site','问卷管理'), 'url'=>array('//moperation/survey/index'),'roles'=>array('ivystaff_it')),
                    'cookbook' => array('label'=>Yii::t('site','周餐谱审核'), 'url'=>array('//moperation/cookbook/index'),'roles'=>array('ivystaff_it', 'ivystaff_opgeneral', 'ivystaff_tygmgt')),
                    'service' => array('label'=>Yii::t('site','服务查看'), 'url'=>array('//moperation/service/index'),'roles'=>array('ivystaff_it')),
                    'returnschool' => array('label'=>Yii::t('site','General Survey Management'), 'url'=>array('//moperation/surveyReturn/template'),'roles'=>array('ivystaff_it')),
                )
            ),
            'management' => array(
                'label'=> '<span class="glyphicon glyphicon-book"></span> ' . Yii::t('site','校园管理').' <b class="caret"></b>',
                'itemOptions'=>array('class'=>'dropdown'),
                'url'=>'#',
                'linkOptions'=>array('class'=>'dropdown-toggle', 'data-toggle'=>'dropdown'),
                'submenuOptions'=>array('class' => 'dropdown-menu'),
                'items'=>array(
                    'expoint' => array('label'=>Yii::t('site','预算人数'), 'url'=>array('//moperation/campusManagement/index'),'roles'=>array('ivystaff_it', 'ivystaff_opgeneral')),
                    'discountreport' => array('label'=>Yii::t('site','Discount Report'), 'url'=>array('//moperation/campusManagement/discountreport'),'roles'=>array('ivystaff_it', 'ivystaff_finance')),
                    'qualityInspect' => array('label'=>Yii::t('site','校园质检'), 'url'=>array('//moperation/qualityInspect/index'),'roles'=>array('ivystaff_it', 'ivystaff_opgeneral', 'ivystaff_tygmgt')),
                    'refund' => array('label'=>Yii::t('site','批量退费'), 'url'=>array('//moperation/refund/index'),'roles'=>array('ivystaff_it')),
                )
            )
        );

        //教学管理
        $userMenu['teaching'] = array(
            'common' => array(
                'label' => '<span class="glyphicon glyphicon-book"></span> ' . Yii::t('site', 'General Tasks').' <b class="caret"></b>',
                'itemOptions'=>array('class'=>'dropdown'),
                'url' => '#',
                'linkOptions' => array('class'=>'dropdown-toggle', 'data-toggle'=>'dropdown'),
                'submenuOptions' => array('class' => 'dropdown-menu'),
                'roles' => array('ivystaff_it', 'ivystaff', 'ivy_teacher'),
                'items' => array(
                    'weekly' => array(
                        'label'=> Yii::t('site','Weekly Tasks'),
                        'url'=>array('//mteaching/weekly/index'),
                        'roles'=>array('ivystaff')
                    ),
                    'monthly' => array(
                        'label'=> Yii::t('teaching','Unit Plan'),
                        'url'=>array('//mteaching/monthly/index'),
                        'roles'=>array('ivystaff')
                    ),
                    'semesterly' => array(
                        'label'=> Yii::t('site','Semester Tasks'),
                        'url'=>array('//mteaching/semester/index'),
                        'roles'=>array('ivystaff')
                    ),
                    'assessments' => array('label'=>Yii::t('newDS','Assessments'), 'url'=>array('/mteaching/assessments/index'),'roles'=>array('ivystaff_it', 'ivystaff_teacher', 'ivystaff_opschool')),
                    'gradebook' => array(
                        'label'=> 'K-G5 GradeBook',
                        'url'=>array('//mteaching/gradebook/index'),
                        'roles'=>array('ivystaff_it', 'ivystaff_teacher', 'ivystaff_opschool')
                    ),
                    'attendance' => array(
                        'label'=> 'G6-12 '.Yii::t('campus','Student Attendance'),
                        'url'=>array('//mteaching/attendance/new', 'branchId'=>'BJ_DS'),
                        'roles'=>array('ivystaff_it', 'ivystaff_teacher', 'ivystaff_opschool')
                    ),
                    'student' => array(
                        'label'=> Yii::t('site','Student Behavior Management'),
                        'url'=>array('//mteaching/student/index', 'category' => 'allBehavior'),
                        'roles'=>array('ivystaff_it', 'ivystaff_teacher', 'ivystaff_opschool')
                    ),
                )
            ),


            'Student_Reports' => array(
                'label'=> '<span class="glyphicon glyphicon-book"></span> ' . Yii::t('message','Communication').' <b class="caret"></b>',
                'itemOptions'=>array('class'=>'dropdown'),
                'url'=>'#',
                'linkOptions'=>array('class'=>'dropdown-toggle', 'data-toggle'=>'dropdown'),
                'submenuOptions'=>array('class' => 'dropdown-menu'),
                'roles'=>array('ivystaff_it', 'ivystaff_opschool', 'ivystaff_teacher'),
                'items'=>array(
                    array('label'=>Yii::t('message','Journal'), 'url'=>array('/mteaching/journals/index'),'roles'=>array('ivystaff_it', 'ivystaff_counselor', 'ivystaff_cd', 'ivystaff_opschool', 'ivystaff_teacher')),
                    array('label'=>Yii::t('message','Notice'), 'url'=>array('/mteaching/notice/index'),'roles'=>array('ivystaff_it', 'ivystaff_counselor', 'ivystaff_cd', 'ivystaff_opschool', 'ivystaff_teacher')),
                    array('label'=>Yii::t('message','PTC'), 'url'=>array('/mteaching/ptc/index'),'roles'=>array('ivystaff_it', 'ivystaff_opschool', 'ivystaff_teacher')),
                    array('label'=>Yii::t('message','Direct Message'), 'url'=>array('/mteaching/directMessage/index'),'roles'=>array('ivystaff_it', 'ivystaff_counselor', 'ivystaff_cd', 'ivystaff_opschool', 'ivystaff_teacher')),
                )
            ),

            'secondaryreport' => array(
                'label'=> '<span class="glyphicon glyphicon-book"></span> ' . Yii::t('principal','G6-12 Achievement Reports').' <b class="caret"></b>',
                'itemOptions'=>array('class'=>'dropdown'),
                'url'=>'#',
                'linkOptions'=>array('class'=>'dropdown-toggle', 'data-toggle'=>'dropdown'),
                'submenuOptions'=>array('class' => 'dropdown-menu'),
                'roles'=>array('ivystaff_it'),
                'items'=>array(
                    array('label'=>Yii::t('principal','Start an Achievement Report'), 'url'=>array('/mteaching/principal/index', 'branchId'=>'BJ_DS'),'roles'=>array('ivystaff_it', 'ivystaff_counselor', 'ivystaff_cd')),
                    // array('label'=>Yii::t('principal','Generate Achievement Reports'), 'url'=>array('/mteaching/report/index'),'roles'=>array('ivystaff_it', 'ivystaff_counselor', 'ivystaff_teacher', 'ivystaff_cd')),
                    array('label'=>Yii::t('principal','Generate Achievement Reports'), 'url'=>array('/mteaching/report/new', 'branchId'=>'BJ_DS'),'roles'=>array('ivystaff_it', 'ivystaff_counselor', 'ivystaff_teacher', 'ivystaff_cd')),
                    array('label'=>Yii::t('principal','Assign Course Standards'), 'url'=>array('/mteaching/distribution/index', 'branchId'=>'BJ_DS'),'roles'=>array('ivystaff_it', 'ivystaff_counselor', 'ivystaff_cd')),
                    array('label'=>Yii::t('principal','Batch Preview Reports'), 'url'=>array('/mteaching/preview/index', 'branchId'=>'BJ_DS'),'roles'=>array('ivystaff_it', 'ivystaff_counselor', 'ivystaff_cd', 'ivystaff_teacher')),
                    array('label'=>Yii::t('user','Report Card Analytic Reports'), 'url'=>array('/mteaching/reportanalytic/index', 'branchId'=>'BJ_DS', 'type' => 'roster1'),'roles'=>array('ivystaff_it', 'ivystaff_counselor', 'ivystaff_teacher', 'ivystaff_cd')),
                )
            ),

            'others' => array(
                'label' => '<span class="glyphicon glyphicon-book"></span> ' . Yii::t('teaching', 'Others').' <b class="caret"></b>',
                'itemOptions'=>array('class'=>'dropdown'),
                'url' => '#',
                'linkOptions' => array('class'=>'dropdown-toggle', 'data-toggle'=>'dropdown'),
                'submenuOptions' => array('class' => 'dropdown-menu'),
                'roles' => array('ivystaff_it', 'ivystaff', 'ivystaff_teacher'),
                'items' => array(
                    'curriculum' => array(
                        'label'=> Yii::t('site','Curriculum'),
                        'url'=>array('//mteaching/curriculum/project','official'=>'1'),
                        'roles'=>array('ivystaff_it', 'ivystaff_eduhead', 'ivystaff_teacher')
                    ),
                    'LearningDomains' => array(
                        'label'=> Yii::t('site','学习领域'),
                        'url'=>array('//mteaching/Learningdomains/index'),
                        'roles'=>array('ivystaff_it', 'ivystaff_eduhead')
                    ),
                    'repoetReview' => array(
                        'label'=> Yii::t('global','Semester Report Review'),
                        'url'=>array('//mteaching/reportCheck/index'),
                        'roles'=>array('ivystaff_it', 'ivystaff_teacher', 'ivystaff_cd', 'ivystaff_eduhead', 'ivystaff_opschool')
                    ),
                    'parentMeet' => array(
                        'label'=> Yii::t('site', 'Parent Reservation'),
                        'url'=>array('//mteaching/parentReservation/index'),
                        'roles'=>array('ivystaff_it', 'ivystaff_teacher','ivystaff_opschool')
                    ),
                    'academic' => array(
                        'label'=> Yii::t('newDS', 'Academic Center'),
                        'url'=>array('//mteaching/learnInfo/index'),
                        'roles'=>array('ivystaff_it', 'ivystaff_teacher','ivystaff_opschool')
                    ),
                    'ranking' => array(
                        'label'=> Yii::t('newDS', 'G6-12 Internal Grade Analytics'),
                        'url'=>array('//mteaching/ranking/index', 'branchId' => 'BJ_DS'),
                        'roles'=>array('ivystaff_it', 'ivystaff_cd', 'ivystaff_dsedu_viewall')
                    ),
                    'transcript' => array(
                        'label'=> Yii::t('newDS', 'G6-12 Transcript'),
                        'url'=>array('//mteaching/transcript/index', 'branchId' => 'BJ_DS'),
                        'roles'=>array('ivystaff_it', 'ivystaff_cd', 'ivystaff_counselor')
                    ),
                    'zkScoreConversion' => array(
                        'label'=> Yii::t('newDS', 'DS-Zhongkao Score Conversion'),
                        'url'=>array('//mteaching/zkConvert/index', 'branchId' => 'BJ_DS'),
                        'roles'=>array('ivystaff_it', 'ivystaff_cd', 'ivystaff_dsedu_viewall', 'ivystaff_hos_office')
                    ),
                    'roaster' => array(
                        'label'=> Yii::t('referral','Roaster Generator'),
                        'url'=>array('//mteaching/examList/index'),
                        'roles'=>array('ivystaff_it','ivystaff_dsedu_editall', 'ivystaff_tygmgt',)
                    ),
                )
            )
        );

        //校园运营
        $userMenu['campusOp'] = array(
            'basic' => array(
                'label'=> '<span class="glyphicon glyphicon-book"></span> ' . Yii::t('site','Basic').' <b class="caret"></b>',
                'itemOptions'=>array('class'=>'dropdown'),
                'url'=>'#',
                'linkOptions'=>array('class'=>'dropdown-toggle', 'data-toggle'=>'dropdown'),
                'submenuOptions'=>array('class' => 'dropdown-menu'),
                'items'=>array(
                    'students' => array('label'=>Yii::t('site','Student Management'), 'url'=>array('//mcampus/student/index','category'=>'current'),'roles'=>array('ivystaff_opschool', 'ivystaff_it', 'ivystaff_finance', 'ivystaff_communicate', 'ivystaff_opgeneral', 'ivystaff_tygmgt', 'ivystaff_hr', 'ivystaff_eduhead', 'ivystaff_surveyedit', 'ivystaff_doctor', 'ivystaff_teacher', 'ivystaff_counselor'),'itemOptions'=>array('title'=>'注册学生、管理学生')),
                    'classroom' => array('label'=>Yii::t('site','Classroom Management'), 'url'=>array('//mcampus/classroom/index'),'roles'=>array('ivystaff_opschool', 'ivystaff_it')),
					'classes' => array('label'=>Yii::t('site','Class Management'), 'url'=>array('//mcampus/classes/index'),'roles'=>array('ivystaff_opschool', 'ivystaff_it', 'ivystaff_eduhead', 'ivystaff_hr')),
					'staff' => array('label'=>Yii::t('site','Staff Management'),
                        'url'=>array('//mcampus/staffProfile/index'),'roles'=>array('ivystaff_opschool', 'ivystaff_it', 'ivystaff_hr', 'ivystaff_eduhead')),
                    'calendar' => array('label'=>Yii::t('site','School Calendar Management'), 'url'=>array('//mcampus/calendarNew/index'),'roles'=>array('ivystaff_opschool', 'ivystaff_it', 'ivystaff_communicate')),
                )
            ),
            'grades' => array(
                'label'=> '<span class="glyphicon glyphicon-book"></span> ' . Yii::t('site','1-12 Grades').' <b class="caret"></b>',
                'itemOptions'=>array('class'=>'dropdown'),
                'url'=>'#',
                'linkOptions'=>array('class'=>'dropdown-toggle', 'data-toggle'=>'dropdown'),
                'submenuOptions'=>array('class' => 'dropdown-menu'),
                'items'=>array(
                    'grouping' => array('label'=>Yii::t('site','ES Teachers Grouping'), 'url'=>array('//mgrades/default/teacherGroup'),'roles'=>array('ivystaff_it', 'ivystaff_opschool', 'ivystaff_eduhead')),
//					'schedule' => array('label'=>Yii::t('site','Schedule Management'), 'url'=>array('//mgrades/schedule/index'),'roles'=>array('ivystaff_it', 'ivystaff_opschool', 'ivystaff_eduhead')),
//                    'reports' => array('label' => Yii::t('site', 'Reports Templates'), 'url'=>array('//mgrades/reports/index'), 'roles'=>array('ivystaff_it', 'ivystaff_opschool', 'ivystaff_teacher', 'ivystaff_counselor', 'ivystaff_eduhead')),
                    'reports' => array('label' => Yii::t('site', 'ES GradeBook Templates'), 'url'=>array('//mgrades/standard/template'), 'roles'=>array('ivystaff_it', 'ivystaff_opschool', 'ivystaff_teacher', 'ivystaff_counselor', 'ivystaff_eduhead')),
                    'ptc1' => array('label' => Yii::t('site', 'ES PTC Management'), 'url'=>array('//mteaching/ptcSchedule/index'), 'roles'=>array('ivystaff_it', 'ivystaff_opschool')),
                    'ESPreviewReports'=>array('label'=>Yii::t('site','ES Batch Preview Reports'),'url'=>array('//mteaching/reportsOnline/index'),'roles'=>array('ivystaff_it','ivystaff_cd')),
                    'secondayreports' => array('label' => Yii::t('principal', 'SS Achievement Reports'), 'url'=>array('//mgrades/secondary/index'), 'roles'=>array('ivystaff_it', 'ivystaff_counselor', 'ivystaff_opschool')),
                    'attendance' => array('label' => Yii::t('site', 'SS Schedule Management'), 'url'=>array('//mgrades/attendance/assignTeacher'), 'roles'=>array('ivystaff_it', 'ivystaff_counselor', 'ivystaff_opschool')),
                    'ptc6' => array('label' => Yii::t('site', 'SS PTC Management'), 'url'=>array('//mteaching/ptcSchedule/assign', 'branchId'=>'BJ_DS'), 'roles'=>array('ivystaff_it', 'ivystaff_opschool')),
                    'violationManagement' => array('label' => Yii::t('site', 'SS Violation Management'), 'url'=>array('//mgrades/violation/index', 'branchId'=>'BJ_DS'), 'roles'=>array('ivystaff_it', 'ivystaff_opschool')),
//                    'examList' => array('label' => Yii::t('referral','Roaster Generator'), 'url'=>array('//mcampus/examList/index'), 'roles'=>array('ivystaff_it', 'ivystaff_opschool')),
                )
            ),
            'routine' => array(
                'label'=> '<span class="glyphicon glyphicon-book"></span> ' . Yii::t('site','Routines').' <b class="caret"></b>',
                'itemOptions'=>array('class'=>'dropdown'),
                'url'=>'#',
                'linkOptions'=>array('class'=>'dropdown-toggle', 'data-toggle'=>'dropdown'),
                'submenuOptions'=>array('class' => 'dropdown-menu'),
                'items'=>array(
                    'cd' => array('label'=>Yii::t('site','CD Dashboard'), 'url'=>array('//charts/default/dashboard'), 'linkOptions'=>array('target'=>'_blank'), 'roles'=>array('ivystaff_it', 'ivystaff_finance', 'ivystaff_opschool', 'ivystaff_pd', 'ivystaff_eduhead', 'ivystaff_tygmgt', 'ivystaff_opgeneral', 'ivystaff_depthead')),
//                    'ot' => array('label'=>Yii::t('site','员工考勤管理'), 'url'=>array('//msettings/branch/points'),'roles'=>array('ivystaff_it')),
                    'workflow' => array('label'=>Yii::t('site','Workflow Workspace'), 'url'=>array('//workflow/index/index'),'roles'=>array('ivystaff_it','ivystaff_opschool','ivystaff_finance','ivystaff_pd', 'ivystaff_tygmgt', 'ivystaff_opgeneral', 'ivystaff_cd', 'ivystaff_opgeneral', 'ivystaff_casher', 'ivystaff_hr')),
                    'bulletin' => array('label'=>Yii::t('site','Daily Bulletin'), 'url'=>array('//mcampus/communicate/internal'),'roles'=>array('ivystaff_it', 'ivystaff_opschool', 'ivystaff_teacher', 'ivystaff_counselor', 'ivystaff_hr')),
                    'feedback' => array('label'=>Yii::t('site','Feedback'), 'url'=>array('//mcampus/feedback/index'),'roles'=>array('ivystaff_it', 'ivystaff_opschool', 'ivystaff_eduhead', 'ivystaff_pd', 'ivystaff_tygmgt')),
                    'tansfer' => array('label'=>Yii::t('site','Group transfer to next year'), 'url'=>array('//mcampus/transfer/index'),'roles'=>array('ivystaff_it', 'ivystaff_opschool', 'ivystaff_finance')),
                    'aqi' => array('label'=>Yii::t('site','AQI Log'), 'url'=>array('//mcampus/laseregg/logIndex'),'roles'=>array('ivystaff_it', 'ivystaff_opschool', 'ivystaff_opgeneral', 'ivystaff_doctor')),
                    'childsign' => array('label'=>Yii::t('site','Student Attendance Management'), 'url'=>array('//mcampus/childsign/index'),'roles'=>array('ivystaff_it', 'ivystaff_opschool', 'ivystaff_doctor', 'ivystaff_teacher', 'ivystaff_counselor', 'ivystaff_opgeneral')),
                    'childsign2' => array('label'=>Yii::t('site','Student Attendance Management'), 'url'=>array('//mcampus/childsign/index2'),'roles'=>array('ivystaff_it', 'ivystaff_opschool', 'ivystaff_doctor', 'ivystaff_teacher', 'ivystaff_counselor', 'ivystaff_opgeneral')),
                    'asa' => array('label'=>Yii::t('site','ASA Management'), 'url'=>array('//masa/vendor/index'),'roles'=>array('ivystaff_it', 'ivystaff_opschool', 'ivystaff_finance')),
                    'identity' => array('label'=>Yii::t('site','Pick-up card list'), 'url'=>array('//identity/default/index'),'roles'=>array('ivystaff_it', 'ivystaff_opschool', 'ivystaff_finance')),
                    'safety' => array('label'=>Yii::t('site','安全事故管理'), 'url'=>array('//safety/safety/index'),'roles'=>array('ivystaff_it','ivystaff_tygmgt', 'ivystaff_opschool','ivystaff_doctor')),
                    'withdrawal' => array('label'=>Yii::t('site', 'Withdrawal Process'), 'url'=>array('//mcampus/withdrawal/index'),'roles'=>array('ivystaff_it', 'ivystaff_opschool', 'ivystaff_doctor')),
                    'qualityInspect' => array('label'=>Yii::t('site','校园质检'), 'url'=>array('//mcampus/qualityInspect/indexSchool'),'roles'=>array('ivystaff_it', 'ivystaff_opschool', 'ivystaff_doctor', 'ivystaff_cd')),
                )
            ),
            'billing' => array(
                'label'=> '<span class="glyphicon glyphicon-book"></span> ' . Yii::t('site','Billings').' <b class="caret"></b>',
                'itemOptions'=>array('class'=>'dropdown'),
                'url'=>'#',
                'linkOptions'=>array('class'=>'dropdown-toggle', 'data-toggle'=>'dropdown'),
                'submenuOptions'=>array('class' => 'dropdown-menu'),
                'items'=>array(
                    array('label'=>Yii::t('site','Cash Handover'), 'url'=>array('//mcampus/cash/handover','category'=>'current'),'roles'=>array('ivystaff_it', 'ivystaff_opschool')),
                    array('label'=>Yii::t('site','Lunch Refund'), 'url'=>array('//mcampus/refund/lunchConfirm'),'roles'=>array('ivystaff_it', 'ivystaff_opschool')),
                    'overdue' => array('label'=>Yii::t('site','Overdue Reminder'), 'url'=>array('//mcampus/billings/overdues'),'roles'=>array('ivystaff_it', 'ivystaff_opschool')),
//                    array('label'=>Yii::t('site','Billing Reports'), 'url'=>array('//msettings/branch/points'),'roles'=>array('ivystaff_it')),
                )
            ),
            'support' => array(
                'label'=> '<span class="glyphicon glyphicon-flag"></span> ' . Yii::t('site','Support').' <b class="caret"></b>',
                'itemOptions'=>array('class'=>'dropdown'),
                'url'=>'#',
                'linkOptions'=>array('class'=>'dropdown-toggle', 'data-toggle'=>'dropdown'),
                'submenuOptions'=>array('class' => 'dropdown-menu'),
                'items'=>array(
                    'event' => array('label'=>Yii::t('site','Marketing Activity'), 'url'=>array('//mcampus/event/index'),'roles'=>array('ivystaff_it', 'ivystaff_opschool', 'ivystaff_communicate'),'itemOptions'=>array('title'=>'招生活动')),
                    'prospect' => array('label'=>Yii::t('site','Prospective Students'), 'url'=>array('//mcampus/admissions/index'),'roles'=>array('ivystaff_it', 'ivystaff_opschool', 'ivystaff_communicate', 'ivystaff_counselor'),'itemOptions'=>array('title'=>'预约参观、访客管理、招生活动')),
                    'catering' => array('label'=>Yii::t('site','周食谱管理'), 'url'=>array("//mcampus/catering/weeklySchedule"), 'roles'=>array('ivystaff_it', 'ivystaff_opschool', 'ivystaff_doctor', 'ivystaff_opgeneral')),
                    'health' => array('label'=>Yii::t('site','身体生长发育'), 'url'=>array("//mcampus/health/index"), 'roles'=>array('ivystaff_it')),
//                    'schoolbus' => array('label'=>Yii::t('site','Schoolbus Management'), 'url'=>array("//mcampus/schoolbus/index"), 'roles'=>array('ivystaff_it', 'ivystaff_opschool', 'ivystaff_opgeneral')),
                    'schoolbus' => array('label'=>Yii::t('site','Schoolbus Management'), 'url'=>array("//mcampus/schoolBus2/index"), 'roles'=>array('ivystaff_it', 'ivystaff_opschool', 'ivystaff_opgeneral')),
//                    'pta' => array('label'=>Yii::t('site','PTA Meeting Minutes'), 'url'=>array("//msettings/global/term"), 'roles'=>array('ivystaff_it')),
//                    'points' => array('label'=>Yii::t('site','Points Exchange Order'), 'url'=>array("//mcampus/order/admin"), 'roles'=>array('ivystaff_it', 'ivystaff_opgeneral', 'ivystaff_opschool')),
                    'lunchreport' => array('label'=>Yii::t('site','Lunch Report'), 'url'=>array('//mcampus/lunchReport/index'),'roles'=>array('ivystaff_it', 'ivystaff_opschool', 'ivystaff_finance')),
                    'bulkmail' => array('label'=>Yii::t('site','Bulk Mail'), 'url'=>array('//mcampus/bulkmail/index'),'roles'=>array('ivystaff_it', 'ivystaff_opschool', 'ivystaff_counselor', 'ivystaff_teacher')),
                    'information' => array('label'=>Yii::t('site','Parent Information'), 'url'=>array('//mcampus/information/index'),'roles'=>array('ivystaff_it', 'ivystaff_opschool')),
                    'uniform' => array('label'=>Yii::t('site','Product management'), 'url'=>array('//mcampus/product/index'),'roles'=>array('ivystaff_it', 'ivystaff_opschool')),
//                    'pickupcard' => array('label'=>Yii::t('site','家长信息确认'), 'url'=>array('//mcampus/pickupCard/index'),'roles'=>array('ivystaff_it', 'ivystaff_opschool')),
                    'registration' => array('label'=>Yii::t('site','新生注册'), 'url'=>array('//mcampus/registration/markstudent'),'roles'=>array('ivystaff_it', 'ivystaff_opschool')),
                    'coll' => array('label'=>Yii::t('site','学生信息收集'), 'url'=>array('//mcampus/coll/index'),'roles'=>array('ivystaff_it', 'ivystaff_opschool')),
//                    'reservationService' => array('label'=>Yii::t('site','预约服务功能'), 'url'=>array('//mcampus/service/index'),'roles'=>array('ivystaff_it')),
                    'returnschoolanswer' => array('label'=>Yii::t('site','General Survey Result'), 'url'=>array('//mcampus/surveyReturnAnswer/index'),'roles'=>array('ivystaff_it','ivystaff_opschool', 'ivystaff_eduhead', 'ivystaff_doctor')),
                    'onlineFile' => array('label'=>Yii::t('site','Cloud Drive'), 'url'=>array('//mcampus/onlineFile/index'),'roles'=>array('ivystaff_opschool', 'ivystaff_it', 'ivystaff_hr', 'ivystaff_finance', 'ivystaff_eduhead', 'ivystaff_communicate', 'ivystaff_asa', 'ivystaff_counselor', 'ivystaff_teacher', 'ivystaff_tygmgt')),
//                    'epidemic' => array('label'=>Yii::t('site','疫情相关'), 'url'=>array('//mcampus/affidavit/index'),'roles'=>array('ivystaff_opschool', 'ivystaff_it', 'ivystaff_hr', 'ivystaff_finance', 'ivystaff_eduhead', 'ivystaff_communicate', 'ivystaff_asa')),
                    'signature' => array('label'=>Yii::t('site','签名列表'), 'url'=>array('//mcampus/enterprise/index'),'roles'=>array('ivystaff_it', 'ivystaff_opschool')),
                    'quote' => array('label'=>Yii::t('quote','Weekly Quote'), 'url'=>array('//mcampus/quote/index'),'roles'=>array('ivystaff_it', 'ivystaff_opschool')),
                    'athletics' => array('label'=>Yii::t('site', 'Athletics'), 'url'=>array('//mcampus/schoolTeams/index'),'roles'=>array('ivystaff_it', 'ivystaff_athletics')),
                )
            ),
            'advance' => array(
                'label'=> '<span class="glyphicon glyphicon-flag"></span> ' . Yii::t('site',
                        'Advanced').' <b class="caret"></b>',
                'itemOptions'=>array('class'=>'dropdown'),
                'url'=>'#',
                'linkOptions'=>array('class'=>'dropdown-toggle', 'data-toggle'=>'dropdown'),
                'submenuOptions'=>array('class' => 'dropdown-menu'),
                'items'=>array(
                    'overdue' => array('label'=>Yii::t('site','Survey Report'),
                        'url'=>array('//mcampus/survey/reports'),'roles'=>array('ivystaff_it', 'ivystaff_opschool', 'ivystaff_eduhead', 'ivystaff_hr', 'ivystaff_surveyedit', 'ivystaff_tygmgt')),
                    'campusdiscountreport' => array('label'=>Yii::t('site','Campus Discount Report'), 'url'=>array('//mcampus/discountreport/index'),'roles'=>array('ivystaff_it', 'ivystaff_finance', 'ivystaff_opschool', 'ivystaff_depthead')),
                    'reportstatistics' => array('label'=>Yii::t('site','成长报告统计'), 'url'=>array('//mcampus/developmentchecklist/index'),'roles'=>array('ivystaff_it', 'ivystaff_eduhead')),
                    'promoted' => array('label'=>Yii::t('site','学生留存报告'), 'url'=>array('//mcampus/promoted/promoted'),'roles'=>array('ivystaff_it', 'ivystaff_finance', 'ivystaff_opschool', 'ivystaff_depthead')),
                    //'scholarship' => array('label'=>Yii::t('site','奖学金申请记录'), 'url'=>array('//mcampus/scholarship/index'),'roles'=>array('ivystaff_it','ivystaff_opschool')),
                    'scholarshipapply' => array('label'=>Yii::t('site','官网奖学金申请列表'), 'url'=>array('//mcampus/scholarship/scholarshipApplyList'),'roles'=>array('ivystaff_it','ivystaff_opschool')),
                    'dept' => array('label'=>Yii::t('site','家长联系部门管理'), 'url'=>array('//mcampus/dept/index'),'roles'=>array('ivystaff_it')),
                    'order' => array('label'=>Yii::t('site','KG销售统计'), 'url'=>array('//mcampus/product2/salesStat'),'roles'=>array('ivystaff_it')),
                    'tag' => array('label'=>Yii::t('labels','Student Tags'), 'url'=>array('//mcampus/label/index'),'roles'=>array('ivystaff_it','ivystaff_hos_office')),
                    'satisfaction' => array('label'=>Yii::t('teaching','Survey-Teacher Feedback'), 'url'=>array('//mcampus/parentSurvey/index'),'roles'=>array('ivystaff_it')),
                )
            )
        );

        //系统配置
        $userMenu['sysConfig'] = array(
            'permission' => array(
                'label'=> '<span class="glyphicon glyphicon-flag"></span> ' . Yii::t('site','Users & Permissions').' <b class="caret"></b>',
                'itemOptions'=>array('class'=>'dropdown','onclick'=>'javascript:void(0);'),
                'url'=>'#',
                'linkOptions'=>array('class'=>'dropdown-toggle', 'data-toggle'=>'dropdown'),
                'submenuOptions'=>array('class' => 'dropdown-menu'),
                'items'=>array(
                    'multi' => array('label'=>Yii::t('site','Campus Admins'), 'url'=>array("//msettings/staff/multiManage"), 'roles'=>array('ivystaff_it')),
                    'role' => array('label'=>Yii::t('site','System Roles'), 'url'=>array("//srbac/authitem/frontpage"), 'roles'=>array('ivystaff_it')),
                    array('label'=>Yii::t('site','部门职位管理'), 'url'=>array("//msettings/dept/index"), 'roles'=>array('ivystaff_it', 'ivystaff_hr')),
                )
            ),
            'setting' => array(
                'label'=> '<span class="glyphicon glyphicon-flag"></span> ' . Yii::t('site','Global Settings').' <b class="caret"></b>',
                'itemOptions'=>array('class'=>'dropdown','onclick'=>'javascript:void(0);'),
                'url'=>'#',
                'linkOptions'=>array('class'=>'dropdown-toggle', 'data-toggle'=>'dropdown'),
                'submenuOptions'=>array('class' => 'dropdown-menu'),
                'items'=>array(
                    'branch' => array('label'=>Yii::t('site','Campus & Office'), 'url'=>array("//msettings/global/branch"), 'roles'=>array('ivystaff_it')),
                    'term' => array('label'=>Yii::t('site','Bilingual Terms'), 'url'=>array("//msettings/global/term"), 'roles'=>array('ivystaff_it')),
                    'payment' => array('label'=>Yii::t('site','3rd Payment'), 'url'=>array("//msettings/payment/vendor"), 'roles'=>array('ivystaff_it')),
                )
            ),
//            array(
//                'label'=> '<span class="glyphicon glyphicon-book"></span> ' . Yii::t('site','校园相关').' <b class="caret"></b>',
//                'itemOptions'=>array('class'=>'dropdown'),
//                'url'=>'#',
//                'linkOptions'=>array('class'=>'dropdown-toggle', 'data-toggle'=>'dropdown'),
//                'submenuOptions'=>array('class' => 'dropdown-menu'),
//                'items'=>array(
//                    array('label'=>Yii::t('site','折扣管理'), 'url'=>array('//msettings/campus/discount'),'roles'=>array('ivystaff')),
//                )
//            ),
        );

        $result['main'] = $userMenu['main'];
        if(!empty($category) && $category!='main'){
            $result[$category] = $userMenu[$category];
        }

        $result = Variation::OAMenu(Yii::app()->params['siteFlag'], $result);

        return $result;
    }
}
