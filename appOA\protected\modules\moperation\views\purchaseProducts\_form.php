<div class="modal-header">
	<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
	<h4 class="modal-title" id="exampleModalLabel"><?php echo $model->id?'更新商品：':'新建商品：'; ?></h4>
</div>
<?php $form=$this->beginWidget('CActiveForm', array(
		'id'=>'purchase-products-form',
		'enableAjaxValidation'=>false,
		'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
)); ?>
<div class="modal-body">
	<div class="col-md-6">
	<h4 class="text-center mb15">商品选择</h4>
		<div class="form-group">
			<label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'cn_name'); ?></label>
			<div class="col-xs-8">
				<?php echo $form->textField($model,'cn_name',array('maxlength'=>255,'class'=>'form-control')); ?>
			</div>
		</div>
		<div class="form-group">
			<label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'cid'); ?></label>
			<div class="col-xs-8">
				<?php echo $form->dropDownList($model,'cid', $diglossiaArray['cate'] , array('class'=>'select_2 form-control', 'empty'=>Yii::t("global", 'Please Select'))); ?>
			</div>
		</div>
		<div class="form-group">
			<label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'aid'); ?></label>
			<div class="col-xs-8">
				<?php echo $form->dropDownList($model,'aid', $diglossiaArray['area'] , array('class'=>'select_2 form-control', 'empty'=>Yii::t("global", 'Please Select'))); ?>
			</div>
		</div>
		<div class="form-group">
			<label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'price'); ?></label>
			<div class="col-xs-8">
				<?php echo $form->textField($model,'price',array('maxlength'=>255,'class'=>'form-control')); ?>
			</div>
		</div>
		<div class="form-group">
			<label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'tid'); ?></label>
			<div class="col-xs-8">
				<?php echo $form->textField($model,'tid',array('maxlength'=>255,'class'=>'form-control')); ?>
			</div>
		</div>

        <div class="form-group">
            <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'commodity_code'); ?></label>
            <div class="col-xs-8">
                <?php echo $form->textField($model,'commodity_code',array('maxlength'=>255,'class'=>'form-control')); ?>
            </div>
        </div>
		<div class="form-group">
			<label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'model'); ?></label>
			<div class="col-xs-8">
				<?php echo $form->textField($model,'model',array('maxlength'=>255,'class'=>'form-control')); ?>
			</div>
		</div>
		<div class="form-group">
			<label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'pic'); ?></label>
			<div class="col-xs-8">
				<?php if ($model->pic) {?>
				<div>
					<a target="_blank" href="<?php echo Yii::app()->params['OAUploadBaseUrl'] . '/purchase/' . $model->pic;?>">
						<img width="260px" src="<?php echo Yii::app()->params['OAUploadBaseUrl'] . '/purchase/' . $model->pic;?>"></a><br><br>
					<button class="btn btn-danger btn-sm" type="button" onclick="deletePic(this,<?php echo $model->id; ?>)">删除图片</button>
				</div>
				<?php }else{
					echo $form->fileField($model,'uploadedFile');
			} ?>
			</div>
		</div>
		<div class="form-group">
			<label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'tip'); ?></label>
			<div class="col-xs-8">
				<?php echo $form->textArea($model,'tip',array('maxlength'=>255,'class'=>'form-control')); ?>
			</div>
		</div>
	</div>
	<div class="col-md-6" id="datalist">
	<h4 class="text-center">供应商选择</h4>
		<div class="form-group">
			<label>主营业务：</label>
			<div>
			<?php echo $form->dropDownList($model,'cagegorys_id', $data['cagegorys'] , array('class'=>'form-control', 'onchange' => 'getcom()','empty'=>Yii::t("global", 'Please Select'))); ?>
			</div>
		</div>

		<div class="form-group">
			<label>选择公司：</label>
			<div>
				<select class="form-control selectpicker" multiple name="" id="classselect" title="请选择">

				</select>
			</div>
		</div>
		<div class="gong">

		</div>
	</div>
	<div class="clearfix"></div>
</div>

<div class="modal-footer">
	<button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
	<button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
</div>
<?php $this->endWidget(); ?>

<script>
var data=<?php echo CJSON::encode($data); ?>;
var historys=<?php echo CJSON::encode($companyData); ?>;

console.log(historys)
//console.log(data)
	function deletePic(btn, id) {
		$.get('<?php echo $this->createUrl("deletePic"); ?>' + '?id=' + id, function(data, status) {
			console.log(data)
			console.log(status)
			if(status == 'success') {
				$(btn).parent().html('<input type="file" name="picture">');
			}
		});
	}
	//监听模态框
	$(function() {
		$('#modal').on('hide.bs.modal', function() {

			$('.modal-body').empty();
		})
	});
	var gongsi =data.data
	var datalist = new Vue({
		el: "#datalist",
		data: {
			list: data,
			selected: '',
			selectlist: ''
		},
	})
	$('#classselect').selectpicker({
		size: 5 
	});
	getcom()
	select()

	function getcom() {
		$("#classselect").html('')
		var selectedlist = []
		for(var i = 0; i < gongsi.length; i++) {
			if($("#PurchaseProducts_cagegorys_id option:selected").val() == gongsi[i].type_id) {
				selectedlist.push(gongsi[i])
				console.log(gongsi[i])
				$("#classselect").append("<option value=" + gongsi[i].id + ">" + gongsi[i].title + "</option>");
			}
		}
		datalist.selectlist = selectedlist
		$("#classselect").selectpicker("refresh");
		$(".filter-option-inner-inner").val('请选择')
		var arr = []
		for(var i=0;i<historys.length;i++){
			arr.push(historys[i].id)
		}
		console.log(arr)
		$('#classselect').selectpicker('val', arr);
		$("#classselect").selectpicker('render');

	}

	function select() {
		$('.gong').html('')
		var selectarr = $('#classselect').val()
		if(selectarr != null) {
			var showlist = []
			console.log(selectarr)
			console.log(datalist.selectlist)
			for(var i = 0; i < datalist.selectlist.length; i++) {
				for(var j = 0; j < selectarr.length; j++) {

					if(selectarr[j] == datalist.selectlist[i].id) {
						showlist.push(datalist.selectlist[i])
						$('.gong').html('')
					}
				}
			}
			console.log(showlist)
			var result=[]
			 for(var i = 0; i < showlist.length; i++){
			    var obj = showlist[i];
			    var num = obj.id;
			    var isExist = false;
			    for(var j = 0; j < historys.length; j++){
			        var aj = historys[j];
			        var n = aj.id;
			        if(n == num){
			            isExist = true;
			            break;
			        }
			    }
			    if(!isExist){
			        result.push(obj);
			    }
			} 
		    for(var j = 0; j < showlist.length; j++) {
		    	for(var i=0;i<historys.length;i++){
					if(historys[i].id==showlist[j].id){
						$('.gong').append('<div class="form-group"><label >' + showlist[j].title + '</label><div class="form-inline"><input type="text" class="form-control datepicker" name="company[' + showlist[j].id + '][start]" placeholder="请选择开始时间"  id="startdate' + showlist[j].id + '" value=' +historys[i].start + '> <input type="text" class="form-control datepicker" name="company[' + showlist[j].id + '][end]" placeholder="请选择结束时间" id="enddate' + showlist[j].id + '" value=' + historys[i].end + '><a href="javascript:close(' + showlist[j].id + ');" class="btn btn-xs btn-primary ml5"><span class="glyphicon glyphicon-remove"></span></a><div></div>')
					}
			    }
				
			}
			for(var j = 0; j < result.length; j++) {
			 	$('.gong').append('<div class="form-group"><label >' + result[j].title + '</label><div class="form-inline"><input type="text" class="form-control datepicker" name="company[' + result[j].id + '][start]" placeholder="请选择开始时间"  id="startdate' + result[j].id + '" value=""> <input type="text" class="form-control datepicker" name="company[' + result[j].id + '][end]" placeholder="请选择结束时间" id="enddate[' + result[j].id + ']" value=""><a href="javascript:close(' + result[j].id + ');" class="btn btn-xs btn-primary ml5"><span class="glyphicon glyphicon-remove"></span></a><div></div>')
			 }
			$(".datepicker").datepicker({
				dateFormat: "yy-mm-dd",
			});
		}
	}
	$('#classselect').on('changed.bs.select', function(e) {
		select()
	});

	function close(id) {
		var arr = $('#classselect').val()
		if(arr.length == 1) {
			for(var i = 0; i < arr.length; i++) {
				var code = arr[i];
				if(id == code) {
					arr.splice($.inArray(code, arr), 1);
				}
			}
			$('#classselect').selectpicker('val', arr);
			$("#classselect").selectpicker('render');
			$('.gong').html('')
		} else {
			for(var i = 0; i < arr.length; i++) {
				var code = arr[i];
				if(id == code) {
					arr.splice($.inArray(code, arr), 1);
				}
			}
			$('#classselect').selectpicker('val', arr);
			$("#classselect").selectpicker('render');
		}
		console.log(arr);

	}
</script>