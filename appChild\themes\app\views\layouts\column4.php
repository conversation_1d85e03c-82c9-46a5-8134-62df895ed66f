<?php $this->beginContent('//layouts/bootstrap4'); ?>

<?php echo $content; ?>
    <script>
        <?php if ($this->showModel == true) :?>
        $('#theModal').modal({
            backdrop: 'static',
            keyboard: false
        });
        <?php endif;?>
        <?php if ($this->isOpen == false) :?>
        $('body input').attr('disabled', 'disabled');
        $('body button').attr('disabled', 'disabled');
        $('body .examine').html('<?php echo Yii::t("reg", 'No content can be submitted while registration is closed'); ?>');
        <?php endif;?>        
        <?php if (isset($this->studentSteps[$this->step]) && $this->studentSteps[$this->step] == 1) :?>
        $('body input').attr('disabled', 'disabled');
        $('body button').attr('disabled', 'disabled');
        $('body .examine').html('<?php echo Yii::t("reg", 'The step has been reviewed. Please contact school to revise information.'); ?>');
        $('body .examine_status').html('<small><?php echo Yii::t("reg","Click a number in the top bar to do other step.") ?></small>');
        <?php endif;?>
    </script>
<?php $this->endContent(); ?>