<!-- Modal -->
<?php echo CHtml::form('/workflow/entrance/index', 'post',array('class'=>'J_ajaxForm'));?>
<div class="modal fade" id="<?php echo $data['modalNameId']?>" tabindex="-1" role="dialog" aria-labelledby="workflowModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel"><?php echo CommonUtils::autoLang($data['workflow']->definationObj->defination_name_cn,$data['workflow']->definationObj->defination_name_en);?> - <?php echo CommonUtils::autoLang($data['workflow']->nodeObj->node_name_cn,$data['workflow']->nodeObj->node_name_cn);?><a href="javascript:void(0)" id="print"> [打印]</a></h4>
            </div>
            <div class="modal-body" id="workflow-modal-body">
                <div class="alert alert-success" role="alert">
                    孩子姓名：<?php $config = WorkflowConfig::getType($data['workflow']->getOperateValue('operation_type'), $data['workflow']->getOperateValue('operation_object_id'));echo CHtml::link($config['function'], $config['href'],array('target'=>'_blank'));?>
                </div>
                <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <td>账单标题</td>
                            <td>账单金额</td>
                            <td>账单区间</td>
                            <td>账单状态</td>
                        </tr>
                    </thead>
                    <tbody>
                    <?php foreach ($data['changeInvoices'] as $data['changeInvoice']):?>
                        <tr>
                            <td><?php echo $data['changeInvoice']->invoice->title; ?></td>
                            <td><?php echo $data['changeInvoice']->invoice->amount; ?></td>
                            <td><?php echo OA::formatDateTime($data['changeInvoice']->invoice->startdate).' / '.OA::formatDateTime($data['changeInvoice']->invoice->enddate); ?></td>
                            <td>原账单</td>
                        </tr>
                        <tr>
                            <td>
                                <?php 
                                    if ($data['changeInvoice']->newInvoice->title != $data['changeInvoice']->invoice->title):
                                        echo CHtml::tag('span', array('style'=>'color:red;'),$data['changeInvoice']->newInvoice->title);
                                    else:
                                        echo $data['changeInvoice']->newInvoice->title;
                                    endif;
                                ?>
                            </td>
                            <td>
                                <?php 
                                    if ($data['changeInvoice']->newInvoice->amount != $data['changeInvoice']->invoice->amount):
                                        echo CHtml::tag('span', array('style'=>'color:red;'),$data['changeInvoice']->newInvoice->amount);
                                    else:
                                        echo $data['changeInvoice']->newInvoice->amount;
                                    endif;
                                ?>
                            </td>
                            <td>
                                <?php 
                                    if ($data['changeInvoice']->newInvoice->startdate != $data['changeInvoice']->invoice->startdate):
                                        echo CHtml::tag('span', array('style'=>'color:red;'),OA::formatDateTime($data['changeInvoice']->newInvoice->startdate));
                                    else:
                                        echo OA::formatDateTime($data['changeInvoice']->newInvoice->startdate);
                                    endif;
                                ?>
                                /
                                <?php 
                                    if ($data['changeInvoice']->newInvoice->enddate != $data['changeInvoice']->invoice->enddate):
                                        echo CHtml::tag('span', array('style'=>'color:red;'),OA::formatDateTime($data['changeInvoice']->newInvoice->enddate));
                                    else:
                                        echo OA::formatDateTime($data['changeInvoice']->newInvoice->enddate);
                                    endif;
                                ?>
                            </td>
                            <td>新账单</td>
                        </tr>
                    <?php endforeach; ?>
                    </tbody>
                </table>
                </div>
                <div class="retshow table-responsive">
                    <h3>批注日志</h3>
                     <?php 
                        $processList = $data['workflow']->getWorkflowProcess();
                        foreach ($processList as $process):?>
                    <h4>
                        <em><?php echo Yii::t('workflow', 'Date submitted:');?>： <?php echo OA::formatDateTime($process->start_time);?></em>
                        <span><?php echo $process->user->getName();?></span>
                    </h4>
                    <div class="clearfix"></div>
                    <div class="comment">
                        <label>意见</label>
                        <p><?php echo nl2br(htmlspecialchars($process->process_desc));?></p>
                    </div>
                    <?php endforeach;?>
                </div>
                <?php if ($data['workflow']->useRole && $data['workflow']->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_UNOP && !$data['workflow']->isFristNode()):?>
                <div class="form-group" style="padding-top:50px;">
                    <?php echo CHtml::textArea('memo','',array('class'=>'form-control','row'=>3));?>
                </div>
            <?php endif;?>
            </div>
            <?php if ($data['workflow']->useRole && $data['workflow']->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_UNOP && !$data['workflow']->isFristNode()):?>
            <div class="modal-footer">
                <?php echo CHtml::hiddenField('definationId', $data['workflow']->getWorkflowId());?>
                <?php echo CHtml::hiddenField('nodeId', $data['workflow']->getCurrentNodeId());?>
                <?php echo CHtml::hiddenField('branchId', $data['workflow']->schoolId);?>
                <?php echo CHtml::hiddenField('operationId', $data['workflow']->getOperateId());?>
                <?php echo CHtml::hiddenField('action', 'show');?>
                <?php echo CHtml::hiddenField('buttonCategory','');?>
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('workflow', 'Cancel');?></button>
                <button type="button" class="btn btn-danger J_ajax_submit_btn" data-category="<?php echo WorkflowOperation::WORKFLOW_STATS_OPDENY;?>">拒绝</button>
                <button type="button" class="btn btn-primary J_ajax_submit_btn" data-category="<?php echo WorkflowOperation::WORKFLOW_STATS_OPED;?>"><?php echo Yii::t('workflow', 'Yes');?></button>
            </div>
            <?php endif;?>
        </div>
    </div>
</div>
<?php CHtml::endForm();?>
<script type="text/javascript">
    //打印
    $(function(){  
         $("#print").click(function(){
            $("#workflow-modal-body a").removeAttr('href');
            $("#workflow-modal-body").printThis();
         })    
    }); 
</script>