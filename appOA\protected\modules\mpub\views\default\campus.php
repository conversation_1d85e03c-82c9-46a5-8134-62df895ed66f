<?php
$crit = new CDbCriteria();
$crit->compare('t.status', 10);
$crit->order = 't.group asc, t.created_timestamp asc';
$branches = Branch::model()->with(array('info','cityInfo'))->findAll($crit);

$i=0;
foreach($branches as $branch){
    $branchData[$i] = array(
        'basic' => $branch->getAttributes(array('branchid', 'group', 'type', 'city', 'title', 'abb', 'status', 'opentime')),
        'info' => $branch->info->getAttributes(),
        'city' => $branch->cityInfo->getAttributes(array('entitle','cntitle','weight'))
    );
    $branchData[$i]['basic']['age'] = ( $branch->opentime ) ? floor(( time() - $branch->opentime ) / (365 * 24 * 60 * 60)) : '';
    if($branchData[$i]['basic']['age'] !== '')
        $branchData[$i]['basic']['age'] = ($branchData[$i]['basic']['age'] == 0) ? '<1' : $branchData[$i]['basic']['age'];

    $i++;
}
?>

<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo Yii::t('site','Ivy Family');?></li>
        <li class="active"><?php echo Yii::t('site','Campuses');?></li>
    </ol>

    <div class="row">
        <div class="col-md-2 col-sm-2" id="check_wrap">
            <div class="panel panel-default J_check_wrap campus-filter">
                <!-- Default panel contents -->
                <div class="panel-heading">
                    <label>
                        <input type="checkbox" value="data-campusgroup" class="J_check_all" checked data-checklist="J_check_c1" data-direction="y"> <?php echo Yii::t('ivyer','All Types');?>
                    </label>
                </div>
                <!-- List group -->
                <ul class="list-group">
                    <li class="list-group-item">
                        <div class="form-inline">
                            <?php
                                foreach(OA::getProgram() as $_k=>$_p):
                            ?>
                                <div class="checkbox mr10">
                                    <label>
                                        <input type="checkbox" checked data-campusattr="data-campusgroup" value="<?php echo $_k;?>" class="J_check" data-yid="J_check_c1"> <?php echo $_p;?>
                                    </label>
                                </div>
                            <?php
                                endforeach;
                            ?>
                        </div>
                    </li>
                </ul>
            </div>

            <div id="citylist">
                <div class="panel panel-default J_check_wrap campus-filter">
                    <!-- Default panel contents -->
                    <div class="panel-heading"><label>
                            <input type="checkbox" value="data-campuscity" checked class="J_check_all" data-checklist="J_check_c2" data-direction="y"> <?php echo Yii::t('ivyer','All Cities');?>
                        </label></div>
                    <!-- List group -->
                    <ul class="list-group">
                        <li class="list-group-item">
                            <div class="form-inline" id="city-checklist">

                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="col-md-8 col-sm-10">

            <div class="panel panel-default">
                <!-- Default panel contents -->
                <div class="panel-heading"> <?php echo Yii::t('site','Campus & Office');?></div>

                <!-- Table -->
                <table class="table table-hover">
                    <thead>
                    <tr>
                        <th class="text-center" width="90"><?php echo Yii::t('ivyer','Age (Y)');?> <a sortflag="opentime" href="javascript:;" onclick="resortBranch(this)"> <span class="label label-default"><span class="glyphicon glyphicon-chevron-up"></span></span></a></th>
                        <th class="text-center" width="100"><?php echo Yii::t('labels','City');?> <a sortflag="city" href="javascript:;" onclick="resortBranch(this)"> <span class="label label-default"><span class="glyphicon glyphicon-chevron-up"></span></span></a></th>
                        <th><?php echo Yii::t('ivyer','Name & Address');?> </th>
                        <th width="220"><?php echo Yii::t('ivyer','Tel/Fax');?> </th>
                        <th width="40"></th>
                    </tr>
                    </thead>
                    <tbody id="branch-list-show">

                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script type="text/template" id="branch-table-row-template">
    <tr data-campuscity=<%- basic.city %> data-campusgroup=<%- basic.group %>>
        <td><h4 class="text-muted text-center"><%- basic.age  %></h4></td>
        <td>
            <h5 class="text-center"><%- city.cntitle %></h5>
            <h5 class="text-center"><%- city.entitle %></h5>
        </td>
        <td>
            <h4>
                <span class="mr5 glyphicon glyphicon-tags program<%= basic.group %>"></span>
                <strong><%- basic.title %></strong>
            </h4>

            <h4><strong><%- info.official_name %></strong></h4>
            <div class="text-muted"><%- info.address_cn %></div>
            <div class="text-muted"><%- info.address_en %></div>
        </td>
        <td>
            <h5 class="text-info"><span class="text-muted"><span title="Telephone" class="glyphicon glyphicon-phone-alt"></span></span> <%- info.tel %></h5>
            <h5 class="text-info"><span class="text-muted"><span title="Fax" class="glyphicon glyphicon-print"></span></span> <%- info.fax %></h5>
        </td>
        <td></td>
    </tr>
</script>

<script type="text/template" id="city-item-template">
    <div class="checkbox mr10">
        <label>
            <input type="checkbox" checked  data-campusattr="data-campuscity" value="<%- id %>" class="J_check" data-yid="J_check_c2">
            <% if(LANG=='zh_cn')
                print(cn);
                else{print(en);} %>
        </label>
    </div>
</script>

<script>
    var branchData = <?php echo CJSON::encode($branchData);?>;
    var displayBranches; //显示校园
    var resortBranch; //排序校园
    var rowTpl = _.template($('#branch-table-row-template').html());
    var sortedData;
    var sortOrder = '';
    var cities = {};
    var displayCity = null;
    var cityTpl = _.template($('#city-item-template').html());
    $(function(){
        displayBranches = function(sort){
            $('#branch-list-show').empty();
            var rows = '';
            if(sort == 'opentime'){
                sortedData = _.sortBy(branchData, function(item){return 0 - parseInt(item.basic.opentime)});
            }else if(sort == 'city'){
                sortedData = _.sortBy(branchData, function(item){return item.city.entitle});
            }else{
                sortedData = branchData.slice(0);
            }
            _.each(sortedData, function(element, index){
                var _row = rowTpl(element);
                rows += _row;
                if(_.isUndefined(cities[element.basic.city])){
                    cities[element.basic.city] = {id: element.basic.city, en:element.city.entitle, cn:element.city.cntitle}
                }

            })
            $('#branch-list-show').html(rows);
        }

        displayCity = function(){
            $('#city-checklist').empty();
            _.each(cities, function(_c,_i){
                var c = cityTpl(_c);
                $('#city-checklist').append(c);
            })
            head.Util.checkAll($('#citylist'));
        }

        resortBranch = function(obj){
            var sortflag = $(obj).attr('sortflag');
            if( sortflag == sortOrder ){
                sortOrder = '';
                $('a[sortflag] span.label').addClass('label-default').removeClass('label-primary');
            }else{
                $('a[sortflag] span.label').addClass('label-default').removeClass('label-primary');
                $(obj).children('span.label').addClass('label-primary').removeClass('label-default');
                sortOrder = sortflag;
            }
            displayBranches(sortOrder);
        }

        displayBranches(sortOrder);
        displayCity();

        $('.campus-filter input[type="checkbox"]').on('change', $('#check_wrap'), function(){
            var _this = $(this);

            $('#branch-list-show').find('tr').hide();
            var _groups = $('input[type="checkbox"][data-campusattr|="data-campusgroup"]');
            var _cities = $('input[type="checkbox"][data-campusattr|="data-campuscity"]');

            if( !_.isEmpty( _groups ) && !_.isEmpty( _cities ) ){
                _.each( _groups, function( _group, _index1 ){
                    if($(_group).is(':checked')){
                        _.each( _cities, function( _city, _index2 ){
                            if($(_city).is(':checked')){
                                $('#branch-list-show').find('tr[data-campusgroup|='+$(_group).val()+'][data-campuscity|='+$(_city).val()+']').show();
                            }
                        })

                    }
                } )
            }
        })
    })
</script>
