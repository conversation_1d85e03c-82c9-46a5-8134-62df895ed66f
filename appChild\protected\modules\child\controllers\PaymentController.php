<?php

class PaymentController extends ProtectedController {

    public $noServiceAction = null;
    //需要验证的action名称 及 验证的权限
    // action => validateName
    protected $needValidateAction = array(
        'credit' => 'viewPayment',
        'history' => 'viewPayment',
        'summary' => 'viewPayment',
        'tcredit' => 'viewPayment',
        'viewInvoice' => 'viewPayment',
        'cancelLunch' => 'viewPayment',
        'onlineNotice' => 'viewPayment',
        'bankInfo' => 'viewPayment',
        'onlineConfirm' => 'viewPayment',
        'onlineBeforePaid' => 'viewPayment',
        'printInvoice' => 'viewPayment',
        'online' => 'viewPayment',
        'exchange' => 'editProfile',
        'cancelOrder' => 'editProfile',
    );
    public $pointStartDate = '2013-06-01';
    public function init() {
        parent::init();
        Yii::import('common.models.invoice.*');
        Yii::import('common.models.yeepay.*');
        Yii::import('common.models.alipay.*');
        Yii::import('common.models.points.*');
    }

    protected function beforeAction(CAction $action) {
        parent::beforeAction($action);
        if (!empty($this->needValidateAction)) {
            $actionId = $action->getId();
            if (!empty($this->needValidateAction[$actionId])) {

                if (!Yii::app()->user->checkAccess($this->needValidateAction[$actionId], array("childid" => $this->getChildId()))) {
                    if (Yii::app()->request->isAjaxRequest) {
                        //$this->renderPartial('noservice_ajax');
                        //return false;
                        $ret['state'] = 'fail';
                        $ret['message'] = Yii::t("message", 'Unauthorized operation.');
                        echo CJSON::encode($ret);
                        Yii::app()->end();
                    } else {
                        $this->noServiceAction = $actionId;
                        $this->forward('noservice');
                    }
                }
            }
        }
        return true;
    }

    public function actionCredit() {
        $creditList = null;
        $type = Yii::app()->request->getQuery('type', 'none');
        //个人账户明细
        $criteria = new CDbCriteria();
        $criteria->select = "cid,amount,`inout`,itemname,updated_timestamp,balance";
        $criteria->compare('childid', $this->getChildId());
        if ($type == 'in') {
            $criteria->compare('`inout`', $type);
        } elseif ($type == 'out') {
            $criteria->compare('`inout`', $type);
        } elseif ($type == 'lunchrefund') {
            $criteria->compare('itemname', 'lunch');
            $criteria->compare('`inout`', 'in');
        }
        $criteria->order = 'updated_timestamp DESC';
        $creditList = new CActiveDataProvider('ChildCredit', array(
                    'criteria' => $criteria,
                    'pagination' => array("pagesize" => 30),
                ));
        $this->render('credit', array('creditList' => $creditList, 'type' => $type));
    }

    public function actionHistory() {
        $payList = null;
        // 款付清历史
        $paid_cri = new CDbCriteria();
        $paid_cri->compare('t.childid', $this->getChildId());
        $paid_cri->compare('t.status', 20);
        $paid_cri->compare('t.`inout`', 'in');
        // 不显示学前教育津贴
        $paid_cri->addNotInCondition('t.payment_type', array('preschool_subsidy'));
        $paid_cri->order = 't.last_paid_timestamp DESC';
        $paid_cri->limit = 10;
        $payList = new CActiveDataProvider('Invoice', array(
                    'criteria' => $paid_cri,
                    'pagination' => array('pageSize' => 30),
                ));

        Yii::app()->clientScript->registerScriptFile(Yii::app()->theme->baseUrl . '/js/jquery.jPrintArea.js', CClientScript::POS_END);

        $this->render('history', array('payList' => $payList));
    }

    public function actionNoservice() {
        $this->render("noservice");
        Yii::app()->end();
    }

    public function actionSummary() {
        $payList = null;
        $unPayList = null;
        $creditList = null;
        $credit = 0.00;
        $depositAmount = 0.00;
        // $childIds = Yii::app()->user->getAdminCIds();
        // 付款信息页面，只显示当前孩子的账单

        //查询未付款
        $schoolId = $this->getSchoolId();
        $criteria = new CDbCriteria();
        //$criteria->select = "invoice_id,childid,payment_type,amount,title,timestamp";
        // $criteria->condition = "childid = " . $this->getChildId() . " and status in (10,30) and schoolid='$schoolId'";
        $criteria->condition = "childid = " . $this->getChildId() . " and status in (10,30)";
        // 不显示学前教育津贴
        $criteria->addNotInCondition('payment_type', array('preschool_subsidy'));
        $criteria->order = 'timestamp DESC';
        $unPayList = new CActiveDataProvider('Invoice', array(
                    'criteria' => $criteria,
                    'pagination' => false,
                ));

        // 款付清
        $paid_cri = new CDbCriteria();
        $paid_cri->compare('childid', $this->getChildId());
        $paid_cri->compare('status', 20);
        $paid_cri->compare('`inout`', 'in');
        // 不显示学前教育津贴
        $paid_cri->addNotInCondition('payment_type', array('preschool_subsidy'));
        $paid_cri->order = 'last_paid_timestamp DESC';
        $paid_cri->limit = 10;
        $payList = new CActiveDataProvider('Invoice', array(
                    'criteria' => $paid_cri,
                    'pagination' => false,
                ));

        //个人账户明细
        /*
          $criteria = new CDbCriteria();
          $criteria->compare('childid', $this->getChildId());
          $criteria->order = 'updated_timestamp DESC';
          $criteria->limit = 10;
          $creditList = new CActiveDataProvider('ChildCredit',array(
          'criteria'   => $criteria,
          'pagination' => false,
          ));
         */

        //个人账户余额
        $credit = number_format($this->myChildObjs[$this->getChildId()]->credit, 2);

        //预交学费余额
        $depositAmount = number_format(DepositHistory::model()->getChildDepositBalance($this->getChildId()), 2);

        $points = InvoiceTransaction::model()->getPointsCredit($this->getChildId(),strtotime($this->pointStartDate));

        Yii::app()->clientScript->registerScriptFile(Yii::app()->theme->baseUrl . '/js/jquery.jPrintArea.js', CClientScript::POS_END);

        $this->render('summary', array('unPayList' => $unPayList, 'payList' => $payList, 'creditList' => $creditList, 'credit' => $credit, 'depositAmount' => $depositAmount, 'points'=>$points));
    }

    public function actionTcredit() {
        Yii::import('common.models.calendar.Calendar');
        $criteria = new CDbCriteria;
        $criteria->condition = 'childid = :childid';
        $criteria->params = array(':childid' => $this->getChildId());
        $criteria->with = 'calendarInfo';
        $creditList = new CActiveDataProvider('DepositHistory', array(
                    'criteria' => $criteria,
                    'pagination' => array("pagesize" => 30),
                ));
        $this->render('tcredit', array('depositList' => $creditList));
    }

    public function actionViewInvoice() {
        $invoice_id = (Yii::app()->request->getQuery('invoice_id')) ? Yii::app()->request->getQuery('invoice_id') : 0;
        $this->renderPartial('viewInvoice', array('invoiceId' => $invoice_id, 'childId' => $this->getChildId(), 'childName' => $this->getChildName()));
    }

    public function actionCancelLunch(){
        if (Yii::app()->user->checkAccess('adminChild', array("childid" => $this->getChildId()))) {
            $tipInfo = array('status' => 0);
            if (Yii::app()->request->getIsAjaxRequest()) {
                $day = Yii::app()->request->getPost('day');
                $operate = strtoupper(Yii::app()->request->getPost('operate'));
                $userId = Yii::app()->user->getId();
                $child = $this->myChildObjs[$this->getChildId()];
                if ($operate === 'DO'){
                    $tipInfo['status'] = Mims::lunchCancel($this->getChildId(), $day, $userId);
                    Mims::sendLunchEmail($child, $day, 'cancel');
                }elseif ($operate === 'UNDO') {
                    $tipInfo['status'] = Mims::lunchRecover($this->getChildId(), $day, $userId);
                    Mims::sendLunchEmail($child, $day, 'recover');
                }
            }
            echo CJSON::encode($tipInfo);
            exit;
        }
    }

    public function actionCancelLunch1() {
        if (Yii::app()->user->checkAccess('adminChild', array("childid" => $this->getChildId()))) {
            $tipInfo = array('status' => 0, 'info' => '');
            if (Yii::app()->request->getIsAjaxRequest()) {
                $day = Yii::app()->request->getPost('day');
                $schoolId = $this->myChildObjs[$this->getChildId()]->schoolid;
                $classId = $this->myChildObjs[$this->getChildId()]->classid;
                $calendarId = Branch::model()->getBranchInfo($schoolId, 'schcalendar');
                $dayTamp = 9 * 3600;
                if (strtotime($day) + $dayTamp < time()) {
                    $tipInfo['info'] = Yii::t("global", "Error found. Return Code :rc", array(':rc' => Mims::RC_LUNCH_CANCEL_EXCEED_TIME));
                    echo CJSON::encode($tipInfo);
                    exit;
                }
                if ($day) {
                    //判断是否为教学天数
                    $dayStamp = strtotime($day);
                    Yii::import('common.models.calendar.CalendarSchoolDays');
                    $schoolDayArr = CalendarSchoolDays::model()->getCalendarSchoolDay($calendarId, $dayStamp, $dayStamp);
                    $schoolDayList = current($schoolDayArr);
                    $schoolDay = ($schoolDayList->schoolday_array) ? explode(',', $schoolDayList->schoolday_array) : array();
                    if (!in_array(date('d', $dayStamp), $schoolDay)) {
                        $tipInfo['info'] = Yii::t("global", "Error found. Return Code :rc", array(':rc' => Mims::RC_LUNCH_CANCEL_NONE_SCHOOLDAY));
                        echo CJSON::encode($tipInfo);
                        exit;
                    }
                    //判断当天孩子是否吃饭
                    $childServArr = ChildServiceInfo::model()->getChildFeeInfo($this->getChildId(), 'lunch', $calendarId, $dayStamp, true);
                    if (is_array($childServArr) && count($childServArr)) {
                        $d = (date('D', $dayStamp));
                        foreach ($childServArr as $ca) {
                            $weekArr = array('Mon' => $ca->mon, 'Tue' => $ca->tue, 'Wed' => $ca->wed, 'Thu' => $ca->thu, 'Fri' => $ca->fri, 'Sat' => 10, 'Sun' => 10);
                            if (!in_array(intval($weekArr[$d]), array(10, 30))) {
                                $tipInfo['info'] = Yii::t("global", "Error found. Return Code :rc", array(':rc' => Mims::RC_LUNCH_CANCEL_TARGET_UNPAID));
                                echo CJSON::encode($tipInfo);
                                exit;
                            }
                        }
                    } else {
                        $tipInfo['info'] = Yii::t("global", "Error found. Return Code :rc", array(':rc' => Mims::RC_LUNCH_CANCEL_TARGET_UNPAID));
                        echo CJSON::encode($tipInfo);
                        exit;
                    }

                    //判断取消的午餐天是否在工作流退费审批中
                    $refundCount = InvoiceChildRefund::model()->getCancleLunchExist($this->getChildId(), $dayStamp);
                    if ($refundCount) {
                        $tipInfo['info'] = Yii::t("global", "You have already submitted the batch requests of lunch cancellation. please do not proceed again.");
                        echo CJSON::encode($tipInfo);
                        exit;
                    }

                    $branchInfo = BranchInfo::model()->findByPk($schoolId);
                    $support_email = null;
                    if (!empty($branchInfo)) {
                        $support_email = $branchInfo->support_email;
                    }
                    //判断（取消OR恢复）

                    $refundObj = RefundLunch::model()->getChildLunch($this->getChildId(), $dayStamp);
                    if ($refundObj == null) {
                        //get school lunch config
                        $feeObj = FeemgtSchoolConfig::model()->getSchoolFeeConfig($schoolId, $calendarId, 'lunch');
                        unset($weekArr['Sat']);
                        unset($weekArr['Sun']);
                        $amount = 0;
                        if (in_array(10, $weekArr)) {
                            $amount = ($feeObj->day_refund_amount) ? $feeObj->day_refund_amount : 0;
                        } elseif (in_array(30, $weekArr)) {
                            $amount = ($feeObj->half_day_refund_amount) ? $feeObj->half_day_refund_amount : 0;
                        }
                        $refundModel = new RefundLunch();
                        $refundModel->yid = $calendarId;
                        $refundModel->schoolid = $schoolId;
                        $refundModel->classid = $classId;
                        $refundModel->childid = $this->getChildId();
                        $refundModel->target_date = $day;
                        $refundModel->target_timestamp = $dayStamp;
                        $refundModel->amount = $amount;
                        $refundModel->userid = Yii::app()->user->id;
                        $refundModel->updated_timestamp = time();
                        $refundModel->child_credit_id = 0;
                        $refundModel->operator_uid = 0;
                        $refundModel->operate_timestamp = 0;
                        if ($refundModel->save()) {
                            $child_name = $this->getChildName($this->getChildId());
                            $class_info = IvyClass::model()->findByPk($classId);

                            $branchInfo = BranchInfo::model()->findByPk($this->getSchoolId());
                            $support_email = null;
                            if (!empty($branchInfo)) {
                                $support_email = $branchInfo->support_email;
                            }

                            // 取消午餐 成功  邮件提醒
                            //邮件发送给父母双方， 同时抄送校园support
                            $this->widget('ext.sendEmail.Send', array(
                                'viewName' => 'updateLunch',
                                'toParty' => false, //发送至 当事人 -参数 party_email
                                'toParent' => true, //发送至 父母 -
                                'toSupport' => false, //发送至 校园支持 -参数 support_email
                                'ccSpouse' => false, //抄送至 配偶 -
                                'ccParent' => false, //抄送至 父母 -
                                'ccSupport' => true, //抄送至 校园支持 -参数 support_email
                                'bccItDev' => true, // 密抄至 ItDev -
                                'replyToSupport' => true, // 回复至 校园支持 -参数 support_email
                                'params' => array(
                                    'support_email' => $support_email,
                                    'class_name' => $class_info->title,
                                    'child_name' => $child_name,
                                    'update_date' => $dayStamp,
                                    'action' => 'cancel',
                                    'support_email' => $support_email,
                                    'date_time' => time(),
                                ),
                            ));
                            $tipInfo['status'] = 11;
                            $tipInfo['info'] = Yii::t("global", "Success!");
                        } else {
                            $tipInfo['info'] = Yii::t("global", "Failed!");
                        }
                    } else {
                        if (is_object($refundObj) && count($refundObj)) {
                            if ($refundObj->child_credit_id || $refundObj->operator_uid) {
                                $tipInfo['info'] = Yii::t("global", "Error found. Return Code :rc", array(':rc' => Mims::RC_LUNCH_CANCEL_ALREADY_FUNDED));
                            } else {
                                if (RefundLunch::model()->deleteByPk($refundObj->id)) {
                                    $child_name = $this->getChildName($this->getChildId());
                                    $class_info = IvyClass::model()->findByPk($classId);

                                    $branchInfo = BranchInfo::model()->findByPk($this->getSchoolId());
                                    $support_email = null;
                                    if (!empty($branchInfo)) {
                                        $support_email = $branchInfo->support_email;
                                    }

                                    // 恢复午餐 成功  邮件提醒
                                    //邮件发送给父母双方， 同时抄送校园support
                                    $this->widget('ext.sendEmail.Send', array(
                                        'viewName' => 'updateLunch',
                                        'toParty' => false, //发送至 当事人 -参数 party_email
                                        'toParent' => true, //发送至 父母 -
                                        'toSupport' => false, //发送至 校园支持 -参数 support_email
                                        'ccSpouse' => false, //抄送至 配偶 -
                                        'ccParent' => false, //抄送至 父母 -
                                        'ccSupport' => true, //抄送至 校园支持 -参数 support_email
                                        'bccItDev' => true, // 密抄至 ItDev -
                                        'replyToSupport' => true, // 回复至 校园支持 -参数 support_email
                                        'params' => array(
                                            'support_email' => $support_email,
                                            'class_name' => $class_info->title,
                                            'child_name' => $child_name,
                                            'update_date' => $dayStamp,
                                            'action' => 'recover',
                                            'support_email' => $support_email,
                                            'date_time' => time(),
                                        ),
                                    ));
                                    $tipInfo['status'] = 22;
                                    $tipInfo['info'] = Yii::t("global", "Success!");
                                } else {
                                    $tipInfo['info'] = Yii::t("global", "Failed!");
                                }
                            }
                        }
                    }
                    echo CJSON::encode($tipInfo);
                    exit;
                }
            }
        }
    }

    /**
     * 打印未付账单 打印收据
     * <AUTHOR>
     * @time 2012-8-9
     */
    public function actionPrintInvoice() {

        Mims::LoadHelper('HtoolKits');
        Yii::import('common.models.ivyLibrary.BorrowingCard');

        $type_for = Yii::app()->getRequest()->getParam('for', 'invoice');
        if ('invoice' == $type_for) {
            $invoice_id = Yii::app()->getRequest()->getParam('unpaid_invoice_id', null);
        } else {
            //打印收据
            $invoice_id = Yii::app()->getRequest()->getParam('paid_invoice_id', null);
        }

        $child_id = $this->getChildId();

        $invoice_info = null;
        if (!empty($invoice_id)) {
            $invoice_cri = new CDbCriteria();
            if ('invoice' == $type_for) {
                $invoice_cri->addInCondition('t.status', array(10, 30));
            } else {
                $invoice_cri->compare('t.status', 20);
            }
            $invoice_cri->compare('t.childid', $child_id);
            $invoice_cri->compare('t.invoice_id', $invoice_id);
            $invoice_info = Invoice::model()->with("invoiceTransaction")->findAll($invoice_cri);
        }

//        $class_id = $this->myChildObjs[$child_id]->classid;
        $class_id = $invoice_info[0]->classid;
//        $school_id = $this->getSchoolId();
        $school_id = $invoice_info[0]->schoolid;

        $school_info = Branch::model()->findByPk($school_id);
        $calendar_id = null;
        $school_logo_url = '';

        if ($school_info->schcalendar) {
            $calendar_id = $school_info->schcalendar;
        }
        if ($school_info->logo) {
            $school_logo_url = Mims::CreateUploadUrl("branch/thumbs/" . $school_info->logo);
        }
        $class_info = IvyClass::model()->findByPk($class_id);
        $credit = empty($this->myChildObjs[$child_id]->credit) ? '0.00' : $this->myChildObjs[$child_id]->credit;
        $deposit = DepositHistory::model()->getChildDepositBalance($child_id);
        $school_desc = HtoolKits::getContentByLang($school_info->cn_description, $school_info->en_description);
        $nf = new CNumberFormatter('zh-cn');

        $base_info = array(
            "child_id" => $child_id,
            "class_id" => $class_id,
            "school_id" => $school_id,
            "child_name" => $this->getChildName($child_id),
            "class_name" => empty($class_info->title) ? null : $class_info->title,
            "credit" => $nf->format('#,##0.00', $this->myChildObjs[$child_id]->credit),
            "school_desc" => Yii::app()->format->formatNtext($school_desc),
            "school_logo_url" => $school_logo_url,
            "deposit" => $nf->format('#,##0.00', $deposit)
        );



        $invoice_list = null;
        $unpaid_total = 0;
        $paid_total = 0;
        if (!empty($invoice_info)) {
            $invoiceConfig = Mims::LoadConfig('CfgInvoice');
            foreach ($invoice_info as $ik => $invoice) {
                //到期日期
                if (isset($invoice->duetime)) {
                    $invoice->dueDate = Mims::formatDateTime($invoice->duetime);
                }
                // 账单区间
                if (!empty($invoice->startdate) && !empty($invoice->enddate) && empty($invoice->installment)) {
                    $invoice->durDate = Mims::formatDateTime($invoice->startdate) . " - " . Mims::formatDateTime($invoice->enddate);
                } else if (!empty($invoice->installment)) {
                    $invoice->durDate = $invoice->installment;
                } else {
                    $invoice->durDate = 'N/A';
                }
                // 应付金额
                $invoice->dueAmount = Invoice::model()->renderDueAmount($invoice, null);
                // 使用的押金 金额
                $invoice->depositAmount = Invoice::model()->getDepositAmount($invoice);
                // 2013.7.25 Invoice->amount 已经是减过押金的钱数
//                if ($invoice->depositAmount > 0) {
//                    $invoice->dueAmount -= $invoice->depositAmount;
//                }

                // 账单类型 学费 校车费
                if (isset($invoiceConfig['fee_type'][$invoice->payment_type])) {
                    $pt = $invoiceConfig['fee_type'][$invoice->payment_type];
                    $invoice->paymentTypeTitle = HtoolKits::getContentByLang($pt['cn'], $pt['en']);

                    // 如果是 课外活动 获取 课外活动的名称
                    if ($pt == $invoiceConfig['fee_type']['afterschool']) {
//						$as_info = FeemgtFeeOther::model()->findByPk($invoice->afterschool_id);
//						if(!empty($as_info)){
//							$invoice->paymentTypeTitle .= ' - '.HtoolKits::getContentByLang($as_info->cn_title, $as_info->en_title);
//						}
                        $invoice->paymentTypeTitle = '';
                    } else if ($pt == $invoiceConfig['fee_type']['library_card']) {
                        // 如果是 借书卡 加上 借书卡的卡名称
                        $bc_info = BorrowingCard::model()->findByPk($invoice->fee_other_id);
                        if (!empty($bc_info)) {
                            $invoice->paymentTypeTitle .= ' - ' . $bc_info->cardname;
                        }
                    }
                }
                // 如果是 折扣账单
                if (!empty($invoice->discount_id)) {
                    if (!empty($invoice->discount)) {
                        // 获取折扣百分比
                        $discount_info = $invoice->discount;
                        $invoice->discountPercent = $discount_info->discount . "％";
                    }
                    // 获取 折扣名称
                    if (!empty($discount_info->discountTitle)) {
                        $invoice->discountTitle = HtoolKits::getContentByLang($discount_info->discountTitle->title_cn, $discount_info->discountTitle->title_en);
                    }
                }
                // 账单交易信息
                if (!empty($invoice->invoiceTransaction)) {
                    $trans = null;
                    foreach ($invoice->invoiceTransaction as $itkey => $transaction) {
                        if (isset($invoiceConfig['payment_type'][$transaction->transactiontype])) {
                            $paymentType = $invoiceConfig['payment_type'][$transaction->transactiontype];
                            // 设置 账单交易 支付的类型  现金、网上支付、pos等等
                            $transaction->transactionTypeTitle = HtoolKits::getContentByLang($paymentType['cn'], $paymentType['en']);
                            $transaction->paidTime = Mims::formatDateTime($transaction->timestampe, 'medium', 'short');
                            $trans[$itkey] = $transaction;
                        }
                    }
                    $invoice->invoiceTransaction = $trans;
                }
                $invoice_list[$ik] = $invoice;

                $unpaid_total += floatval($invoice->dueAmount);
                $paid_total += floatval($invoice->amount);
            }
            unset($invoice_info);
        }

        $schoolId = $this->getSchoolId();
        $schoolId = $school_id;
        $bank_info = null;

        if (!empty($schoolId) && 'invoice' == $type_for) {
            $bank_info = BranchVar::model()->bankInfo($schoolId);
        }

        //Yii::app()->clientScript->registerScriptFile(Yii::app()->theme->baseUrl.'/js/jquery.jPrintArea.js');
        // 输出 变量到 视图文件
        $view_data = array(
            'base_info' => $base_info,
            'invoice_list' => $invoice_list,
            'unpaid_total' => $unpaid_total,
            'paid_total' => $paid_total,
            'bank_info' => $bank_info,
        );
        if ('invoice' == $type_for) {
            $this->renderPartial('printInvoice', $view_data);
        } else {
            $this->renderPartial('printReceipt', $view_data);
        }
    }

    public function actionError() {
        if ($error = Yii::app()->errorHandler->error) {
            if (Yii::app()->request->isAjaxRequest)
                echo $error['message'];
            else
                $this->render('error', $error);
        }
    }

    // 在线支付相关  ----------------------------------------------------------------

    /**
     * 显示未付账单
     * <AUTHOR>
     * @time 2012-11-19
     */
    public function actionOnline() {

        $schoolId = $this->getSchoolId();
        $paymentMethodInfo = Mims::LoadConfig('CfgPaymentMethodInfo');
        $paymentMethod = null;
        $currentPaymentMethod = null;
        $currentPaymentMethodHtml = null;
        if (isset($paymentMethodInfo[$schoolId])) {
            if (isset($paymentMethodInfo[$schoolId]['yeepay']) && true === $paymentMethodInfo[$schoolId]['yeepay']) {
                $paymentMethod['yeepay'] = 'yeepay';
            }
            if (isset($paymentMethodInfo[$schoolId]['bankTransfer']) && true === $paymentMethodInfo[$schoolId]['bankTransfer']) {
                $paymentMethod['bankTransfer'] = 'bankTransfer';
            }           
            if (isset($paymentMethodInfo[$schoolId]['alipay']) && true === $paymentMethodInfo[$schoolId]['alipay']) {
                $paymentMethod['alipay'] = 'alipay';
            }
            if (isset($paymentMethodInfo[$schoolId]['allinpay']) && true === $paymentMethodInfo[$schoolId]['allinpay']) {
                $paymentMethod['allinpay'] = 'allinpay';
            }
            if (isset($paymentMethodInfo[$schoolId]['wxpay']) && true === $paymentMethodInfo[$schoolId]['wxpay']) {
                $paymentMethod['wxpay'] = 'wxpay';
            }
        }
        //特殊 便于测试
//        $specialIds = array(6335);
//        if (in_array($this->getChildId(), $specialIds)) {
//            $paymentMethod = null;
//            $paymentMethod['alipay'] = 'alipay';
//            $paymentMethod['bankTransfer'] = 'bankTransfer';
//            $paymentMethod['yeepay'] = 'wxpay';
//        }
        if (!empty($paymentMethod)) {
            $currentPaymentMethod = end($paymentMethod);
            if ('yeepay' == $currentPaymentMethod) {
                $yeepayAvailable = false;
                $yeepayBankInfo = Mims::LoadConfig('CfgYeepayBankInfo');
                $yeepayMeridInfo = Mims::LoadConfig('CfgYeepayMeridInfo');
                if (!empty($yeepayMeridInfo[$this->getSchoolId()]['p1_MerId'])) {
                    $yeepayAvailable = true;
                }

                $ph_cri = new CDbCriteria();
                $ph_cri->compare('childid', $this->getChildId());
                $ph_cri->order = 'hotValue DESC';
                $ph_cri->limit = 3;
                $habits = YpPayHabit::model()->findAll($ph_cri);

                $usedBank = array();
                if (!empty($habits)) {
                    foreach ($habits as $key => $habit) {
                        if (!empty($yeepayBankInfo['yeepayBankInfo'][$habit->pd_FrpId])) {
                            $usedBank[$habit->pd_FrpId] = $yeepayBankInfo['yeepayBankInfo'][$habit->pd_FrpId];
                        }
                    }
                }

                Yii::app()->clientScript->registerCssFile(Yii::app()->theme->baseUrl . '/css/paying.css?t=' . Yii::app()->params['refreshAssets']);
                $currentPaymentMethodHtml = $this->renderPartial(
                        '/yeepay/onlineBankInfo', array(
                    'yeepayAvailable' => $yeepayAvailable,
                    'usedBank' => $usedBank,
                    'yeepayBankInfo' => $yeepayBankInfo['yeepayBankInfo']
                        ), true
                );
            } elseif ('alipay' == $currentPaymentMethod) {

                $alipayAvailable = false;
                $alipayBankInfo = Mims::LoadConfig('CfgAlipayBankInfo');
                $alipayPartnerInfo = Mims::LoadConfig('CfgAlipayPartnerInfo');
                if (isset(Yii::app()->params['onlineBanking']) && 'production' == Yii::app()->params['onlineBanking']) {
                    $schoolid = $this->getSchoolId();
                } else {
                    $schoolid = 'test';
                }
                if (isset($alipayPartnerInfo[$schoolid]) && !empty($alipayPartnerInfo[$schoolid]['partner']) && !empty($alipayPartnerInfo[$schoolid]['seller_email'])) {
                    $alipayAvailable = true;
                }
                $alipayBankList = array_keys($alipayBankInfo);
                $usedBank = array();
                $otherBankList = array();
                if (true == $alipayAvailable) {
                    $ph_cri = new CDbCriteria();
                    $ph_cri->compare('childid', $this->getChildId());
                    $ph_cri->order = 'hot_value DESC';
                    $ph_cri->limit = 3;
                    $ph_cri->index = 'bankid';
                    $habits = AlipayPayHabit::model()->findAll($ph_cri);

                    if (empty($habits)) {
                        $habitBank = array();
                    } else {
                        $habitBank = array_keys($habits);
                    }

                    foreach ($alipayBankList as $bankid) {
                        if (in_array($bankid, $habitBank)) {
                            $usedBank[$bankid] = '';
                        } else {
                            $otherBankList[$bankid] = '';
                        }
                    }
                }
                Yii::app()->clientScript->registerCssFile(Yii::app()->theme->baseUrl . '/css/alipay.css?t=' . Yii::app()->params['refreshAssets']);
                $currentPaymentMethodHtml = $this->renderPartial('/alipay/onlineBankInfo', array(
                    'alipayAvailable' => $alipayAvailable,
                    'usedBank' => $usedBank,
                    'otherBankList' => $otherBankList
                        ), true
                );
            } elseif ('bankTransfer' == $currentPaymentMethod) {
                Yii::import('common.models.calendar.CalendarSchool');
                $schoolId = $this->getSchoolId();
                $bankTransferAvailable = false;

                $bank_info = null;

                if (!empty($schoolId)) {

                    $bank_info = BranchVar::model()->bankInfo($schoolId);
//                    $calendar_id = CalendarSchool::model()->getCurrentSchoolYearCalendar($schoolId);
//
//                    $criteria = new CDbCriteria();
//                    $criteria->compare('schoolid', $schoolId);
//                    $criteria->compare('sign', 'invoice');
//                    $criteria->compare('yid', $calendar_id);
//                    $criteria->compare('info_type', 0);
//                    $criteria->order = 't.weight ASC';
//
//                    $bank_info = SchoolBankInfo::model()->findAll($criteria);
//
//                    if (empty($bank_info)) {
//                        $criteria = new CDbCriteria();
//                        $criteria->compare('schoolid', $schoolId);
//                        $criteria->compare('sign', 'invoice');
//                        $criteria->compare('info_type', 0);
//                        $criteria->order = 't.weight ASC';
//                        $bank_info = SchoolBankInfo::model()->findAll($criteria);
//                    }
                    if (!empty($bank_info)) {
                        $bankTransferAvailable = true;
                    }
                }

                // 输出 变量到 视图文件

                $currentPaymentMethodHtml = $this->renderPartial('/bankTransfer/onlineBankInfo', array(
                    'bankTransferAvailable' => $bankTransferAvailable,
                    'bank_info' => $bank_info
                        ), true
                );
            } elseif ('allinpay' == $currentPaymentMethod){
                $allinpayAvailable = false;
                $allinpayBankInfo = Mims::LoadConfig('CfgallinpayBankInfo');
                $allinpayPartnerInfo = CommonUtils::LoadConfig('CfgallinpayPartnerInfo');
                if (isset(Yii::app()->params['onlineBanking']) && 'production' == Yii::app()->params['onlineBanking']) {
                    $schoolid = $this->getSchoolId();
                } else {
                    $schoolid = 'test';
                }
                if (isset($allinpayPartnerInfo[$schoolid]) && !empty($allinpayPartnerInfo[$schoolid]['partner']) && !empty($allinpayPartnerInfo[$schoolid]['seller_email'])) {
                    $allinpayAvailable = true;
                }
                $allinpayBankList = array_keys($allinpayBankInfo);
                $usedBank = array();
                $otherBankList = array();
                if (true == $allinpayAvailable) {
                    $ph_cri = new CDbCriteria();
                    $ph_cri->compare('childid', $this->getChildId());
                    $ph_cri->order = 'hot_value DESC';
                    $ph_cri->limit = 3;
                    $ph_cri->index = 'bankid';
                    $habits = AlipayPayHabit::model()->findAll($ph_cri);

                    if (empty($habits)) {
                        $habitBank = array();
                    } else {
                        $habitBank = array_keys($habits);
                    }

                    foreach ($allinpayBankList as $bankid) {
                        if (in_array($bankid, $habitBank)) {
                            $usedBank[$bankid] = '';
                        } else {
                            $otherBankList[$bankid] = '';
                        }
                    }
                }
                Yii::app()->clientScript->registerCssFile(Yii::app()->theme->baseUrl . '/css/alipay.css?t=' . Yii::app()->params['refreshAssets']);
                $currentPaymentMethodHtml = $this->renderPartial('/allinpay/onlineBankInfo', array(
                    'allinpayAvailable' => $allinpayAvailable,
                    'usedBank' => $usedBank,
                    'otherBankList' => $otherBankList
                        ), true
                );
            } elseif ('wxpay' == $currentPaymentMethod) {
                $currentPaymentMethodHtml = $this->renderPartial('/wxpay/onlineBankInfo', array(
                        ), true
                );
            }
        }

        // unpaid
        $up_cri = new CDbCriteria();
        $up_cri->compare('t.childid', $this->getChildId());
        // $up_cri->compare('t.schoolid', $schoolId);
        $up_cri->addInCondition('t.status', array(10, 30));
        // 不显示学前教育津贴
        $up_cri->addNotInCondition('t.payment_type', array('preschool_subsidy'));
        $up_cri->order = 't.invoice_id ASC';
        $up_cri->with = array('invoiceTransaction');
        $up_cri->select = array('amount', 'payment_type', 'fee_type', 'title', 'status', 'schoolid');
        $dataProvider = new CActiveDataProvider(
                        'Invoice',
                        array(
                            'criteria' => $up_cri,
                            'pagination' => array('pageSize' => 50),
                        )
        );

        Yii::app()->clientScript->registerScriptFile(Yii::app()->theme->baseUrl . '/js/paying.js?t=' . Yii::app()->params['refreshAssets'] , CClientScript::POS_HEAD);
        // 输出 变量到 视图文件
        $view_data = array(
            'childId' => $this->getChildId(),
            'dataProvider' => $dataProvider,
            'currentPaymentMethod' => $currentPaymentMethod,
            'paymentMethod' => $paymentMethod,
            'currentPaymentMethodHtml' => $currentPaymentMethodHtml,
        );

        $this->render('online', $view_data);
    }

    /**
     * 确认账单信息
     * <AUTHOR>
     * @time 2012-11-19
     */
    public function actionOnlineConfirm() {

        if (Yii::app()->request->isPostRequest) {

            $paymentMothod = Yii::app()->request->getPost('payment_mothod');
            $childId = empty($_POST['childid']) ? $this->getChildId() : $_POST['childid'];
            $invoiceId = Yii::app()->request->getPost('invoice_id', array());
            $invoiceIdStr = implode(',', $invoiceId);

            $up_cri = new CDbCriteria();
            $up_cri->compare('t.childid', $childId);
            $up_cri->compare('t.invoice_id', $invoiceId);
            $up_cri->addInCondition('t.status', array(10, 30));
            $up_cri->order = 't.invoice_id ASC';
            $up_cri->with = 'invoiceTransaction';
            $up_cri->select = array('amount', 'payment_type', 'fee_type', 'title', 'status', 'schoolid');
            $dataProvider = new CActiveDataProvider(
                            'Invoice',
                            array(
                                'criteria' => $up_cri,
                                'pagination' => array('pageSize' => 50),
                            )
            );

            // 判断支付账单是否同时包含多个学校
            $schools = array();
            $invocieInfo = $dataProvider->getData();
            foreach ($invocieInfo as $invoice) {
                $schools[$invoice->schoolid] = $invoice->schoolid;
            }
            if (count($schools) > 1) {
                return false;
            }

            if ('yeepay' == $paymentMothod) {
                $bankId = Yii::app()->request->getPost('yeeBankId', "");

                Mims::LoadHelper('HyeepayCommon');

                $yeepayBankInfo = Mims::LoadConfig('CfgYeepayBankInfo');


                $selectedBank = null;
                if (!empty($yeepayBankInfo['yeepayBankInfo'][$bankId])) {
                    $selectedBank = $yeepayBankInfo['yeepayBankInfo'][$bankId];
                }

                $invocieInfo = $dataProvider->getData();
                if (!empty($invocieInfo)) {
                    $yeepayMeridInfo = Mims::LoadConfig('CfgYeepayMeridInfo');
                    $yc = new HyeepayCommon();
                    if (isset(Yii::app()->params['onlineBanking']) && 'production' == Yii::app()->params['onlineBanking']) {
                        $yc->init($this->getSchoolId());
                    } else {
                        $yc->initTest($this->getSchoolId());
                    }

                    $totalDueAmount = Invoice::model()->getTotalDueAmount($invocieInfo);
                    $first_invoice_id = current($invocieInfo)->invoice_id;
                    $orderIdPrefix = $yc->generateOrderId($yeepayMeridInfo[$this->getSchoolId()]['number_code'], $first_invoice_id);
                    $postfix = YpInvoiceTransaction::model()->getPostfix($orderIdPrefix);
                    if (empty($postfix) || $postfix > 99) {
                        $postfix = $yc->customRandom(3);
                    }

                    $autoOrderId = trim($orderIdPrefix . $postfix);
                    $successCallBackUrl = $this->createUrl("/child/payment/history");
                    $customHmac = $yc->HmacMd5($childId . $this->getSchoolId() . $autoOrderId . $totalDueAmount . $bankId . $yc->getReqUrlOnline() . $invoiceIdStr, 'gs');
                    // getReqHmacString($p2_Order,$p3_Amt,$p5_Pid,$p6_Pcat,$p7_Pdesc,$pa_MP,$pd_FrpId)
                    $hmac = $yc->getReqHmacString($autoOrderId, $totalDueAmount, '', '', '', $successCallBackUrl, $bankId);
                    $requestInfo = array(
                        "p0_Cmd" => $yc->getP0Cmd(),
                        "p1_MerId" => $yc->getP1MerId(),
                        "p2_Order" => $autoOrderId,
                        "p3_Amt" => $totalDueAmount,
                        "p4_Cur" => $yc->getP4Cur(),
                        "p8_Url" => $yc->getP8Url(),
                        "p9_SAF" => $yc->getP9Saf(),
                        "pa_MP" => $successCallBackUrl,
                        "pd_FrpId" => $bankId,
                        "pr_NeedResponse" => $yc->getPrNeedResponse(),
                        "hmac" => $hmac,
                    );
                }

                $beforePaidInfoModel = new BeforePaidInfoForm();

                $beforePaidInfoModel->childId = $childId;
                $beforePaidInfoModel->schoolId = $this->getSchoolId();
                $beforePaidInfoModel->bankId = $bankId;
                $beforePaidInfoModel->autoOrderId = $autoOrderId;
                $beforePaidInfoModel->totalDueAmount = $totalDueAmount;
                $beforePaidInfoModel->reqURL_onLine = $yc->getReqUrlOnline();
                $beforePaidInfoModel->customHmac = $customHmac;
                $beforePaidInfoModel->invoiceId = $invoiceIdStr;

                // 输出 变量到 视图文件
                $view_data = array(
                    'paymentMethod' => 'yeepay',
                    'dataProvider' => $dataProvider,
                    'selectedBank' => $selectedBank,
                    'requestInfo' => $requestInfo,
                    'beforePaidInfoModel' => $beforePaidInfoModel,
                );
            } elseif ('alipay' == $paymentMothod) {
                $bankId = Yii::app()->request->getPost('aliBankId', "");
                $alipayBankInfo = Mims::LoadConfig('CfgAlipayBankInfo');

                $childId = empty($_POST['childid']) ? $this->getChildId() : $_POST['childid'];
                $dueAmount = Yii::app()->request->getPost('due_amount', array());
                $usefullAmount = null;
                if (!empty($invoiceId)) {
                    foreach ($invoiceId as $i_id) {
                        $usefullAmount[$i_id] = $dueAmount[$i_id];
                    }
                }
                unset($dueAmount);
                $dueAmountStr = implode(',', $usefullAmount);
                $selectedBank = null;
                if (!empty($alipayBankInfo[$bankId])) {
                    $selectedBank = $alipayBankInfo[$bankId];
                }

                $invocieInfo = $dataProvider->getData();
                if (!empty($invocieInfo)) {

                    $invoiceTitle = null;
                    foreach ($invocieInfo as $invoice) {
                        $invoiceTitle[$invoice->invoice_id] = $invoice->title;
                    }
                    Yii::import('ext.alipay.*');
                    require_once Yii::app()->basePath . DIRECTORY_SEPARATOR . 'extensions' . DIRECTORY_SEPARATOR . 'alipay' . DIRECTORY_SEPARATOR . 'lib' . DIRECTORY_SEPARATOR . 'alipayCore.php';

                    if (isset(Yii::app()->params['onlineBanking']) && 'production' == Yii::app()->params['onlineBanking']) {
                        $totalDueAmount = Invoice::model()->getTotalDueAmount($invocieInfo);
                        // $schoolid = $this->getSchoolId();
                        $schoolid = current($schools);
                    } else {
                        $totalDueAmount = 0.01;
                        $schoolid = 'test';
                    }
                    $alipayProxy = new AlipayProxy($schoolid);
                    $first_invoice_id = current($invocieInfo)->invoice_id;
                    $orderIdPrefix = generateAlipayOrderId($alipayProxy->number_code, $first_invoice_id);
                    $postfix = AlipayOrder::model()->getPostfix($orderIdPrefix);
                    if (empty($postfix) || $postfix > 99) {
                        $postfix = customRandomForAlipay(3);
                    }
                    $requestParameter = new RequestParameter();
                    $alipayConfig = $alipayProxy->getAttributes();
//构造纯网关接口
                    $alipayService = new AlipayService($alipayConfig);
                    $requestParameter->attributes = $alipayConfig;
                    $requestParameter->service = 'create_direct_pay_by_user';
                    $requestParameter->payment_type = '1';
                    $requestParameter->out_trade_no = trim($orderIdPrefix . $postfix);
                    if (empty($invoiceTitle)) {
                        $requestParameter->subject = 'This Parameter is Required.';
                    } else {
                        $requestParameter->subject = implode(';', $invoiceTitle);
                    }
                    $requestParameter->body = '';
                    $requestParameter->total_fee = $totalDueAmount;
                    $requestParameter->paymethod = 'bankPay';
                    $requestParameter->defaultbank = $bankId;
                    $anti_phishing_key = $alipayService->query_timestamp(); //获取防钓鱼时间戳函数
//                    $anti_phishing_key = time(); //获取防钓鱼时间戳函数
                    $requestParameter->anti_phishing_key = $anti_phishing_key;
                    $requestParameter->exter_invoke_ip = getRealIp();
                    $requestParameter->show_url = '';
                    $requestParameter->extra_common_param = '';
                    $requestParameter->royalty_type = '';
                    $requestParameter->royalty_parameters = '';

                    $parameter = $requestParameter->attributes;
                    unset($parameter['id']);
                    unset($parameter['update_timestamp']);
                    $alipaySubmitHtml = $alipayService->create_direct_pay_by_user($parameter);

                    $alipayConfig['invoiceIdStr'] = $invoiceIdStr;
                    $alipayConfig['dueAmountStr'] = $dueAmountStr;
                    $alipayConfig['bankid'] = $requestParameter->defaultbank;
                    $alipayConfig['orderid'] = $requestParameter->out_trade_no;
//除去待签名参数数组中的空值和签名参数
                    $para_filter = paraFilter($alipayConfig);
//对待签名参数数组排序
                    $para_sort = argSort($para_filter);
//生成签名结果
                    $mySign = buildMysign($para_sort, trim($alipayConfig['key']), strtoupper(trim($alipayConfig['sign_type'])));
                    //////////////////
                    $para_filter = paraFilter($parameter);
//对待签名参数数组排序
                    $para_sort = argSort($para_filter);
//生成签名结果
                    $requestParameter->sign = buildMysign($para_sort, trim($alipayConfig['key']), strtoupper(trim($alipayConfig['sign_type'])));

                    Yii::app()->clientScript->registerCssFile(Yii::app()->theme->baseUrl . '/css/alipay.css?t=' . Yii::app()->params['refreshAssets']);
                }

// 输出 变量到 视图文件
                $view_data = array(
                    'paymentMethod' => 'alipay',
                    'dataProvider' => $dataProvider,
                    'selectedBank' => $selectedBank,
                    'mySign' => $mySign,
                    'requestParameter' => $requestParameter,
                    'requestParameterAttributes' => array_keys($parameter),
                    'alipaySubmitHtml' => $alipaySubmitHtml,
                );
            }elseif ('allinpay' == $paymentMothod) {
                $bankId = Yii::app()->request->getPost('allinBankId', "");
                $sub = Yii::app()->request->getPost('sub', "");
                $submoney = Yii::app()->request->getPost('submoney', "");
                $submoney = floatval(str_replace(',','',$submoney));
                $submoney = round($submoney,2);

                $allinpayBankInfo = Mims::LoadConfig('CfgAllinpayBankInfo');

                $childId = empty($_POST['childid']) ? $this->getChildId() : $_POST['childid'];
                $dueAmount = Yii::app()->request->getPost('due_amount', array());
                $usefullAmount = null;
                if (!empty($invoiceId)) {
                    foreach ($invoiceId as $i_id) {
                        $usefullAmount[$i_id] = $dueAmount[$i_id];
                    }
                }
                unset($dueAmount);
                $selectedBank = null;
                
                if (!empty($allinpayBankInfo[$bankId])) {
                    $selectedBank = $allinpayBankInfo[$bankId];
                }

                $invocieInfo = $dataProvider->getData();
                if (!empty($invocieInfo)) {
                    $invoice_id = current($invocieInfo)->invoice_id;
                    $invoiceTitle = null;
                    foreach ($invocieInfo as $invoice) {
                        $invoiceTitle[$invoice->invoice_id] = $invoice->title;
                    }

                    $totalDueAmount = Invoice::model()->getTotalDueAmount($invocieInfo);
                    $schoolid = $this->getSchoolId();
                    //用户选择了分开账单支付，且只选择了一个账单,分开支付的金额不大于该账单总金额
                    if ($sub && count($invoiceId) == 1 && $submoney < $totalDueAmount) {
                        $totalDueAmount = $submoney;
                    }

                    Yii::import('common.extensions.allinpay.*');
                    $allinpay = new Allinpay($schoolid);
                    $orderNo = $allinpay->generateOrderId($invoice_id);
                    $allinpay->pickupUrl = $this->createUrl('allinpayPickup');
                    if (isset(Yii::app()->params['onlineBanking']) && 'production' == Yii::app()->params['onlineBanking']) {
                        $allinpay->pickupUrl = $this->createUrl('history');
                    }
                    $allinpay->receiveUrl = Yii::app()->params['appsUrl'].'/allinpayorder/onlinepay';
                    $allinpay->orderAmount = $totalDueAmount*100;
                    $allinpay->orderDatetime = date('YmdHis',time());
                    $allinpay->payType = Yii::app()->request->getPost('payType','1');
                    $allinpay->issuerId = strtolower($bankId);
                    $allinpay->bulidSignMsg();
                    $allinpay->bulidForm();

                    $requestParameter = new RequestParameter();
                    $requestParameter->partner = $allinpay->merchantId;
                    $requestParameter->_input_charset = $allinpay->inputCharset;
                    $requestParameter->sign_type = $allinpay->sign_type;
                    $requestParameter->sign = $allinpay->bulidSignMsg();
                    $requestParameter->service = 'create_direct_pay_by_user';
                    $requestParameter->payment_type = $allinpay->payType;
                    $requestParameter->out_trade_no = $allinpay->orderNo;
                    if (empty($invoiceTitle)) {
                        $requestParameter->subject = 'This Parameter is Required.';
                    } else {
                        $requestParameter->subject = implode(';', $invoiceTitle);
                    }
                    $requestParameter->total_fee = $allinpay->getRealMoney();
                    $requestParameter->paymethod = 'bankPay';
                    $requestParameter->defaultbank = $allinpay->issuerId;
                    $requestParameter->exter_invoke_ip = $allinpay->getRealIp();
                    $requestParameter->return_url = $allinpay->pickupUrl;
                    $requestParameter->notify_url = $allinpay->receiveUrl;
                    $requestParameter->extra_common_param = '';
                    $requestParameter->royalty_type = '';
                    $requestParameter->royalty_parameters = '';

                    $parameter = $requestParameter->attributes;
                }

                $view_data = array(
                    'paymentMethod' => 'allinpay',
                    'dataProvider' => $dataProvider,
                    'selectedBank' => $selectedBank,
                    'mySign' => $allinpay->signMsg,
                    'orderDatetime' => $allinpay->orderDatetime,
                    'requestParameter' => $requestParameter,
                    'requestParameterAttributes' => array_keys($parameter),
                    'allinpayForm' => $allinpay->form,
                );
            }elseif ('wxpay' == $paymentMothod) {
                $url = '';
                $submoney = Yii::app()->request->getPost('submoney2',0);
                $submoney = floatval(str_replace(',','',$submoney));
                $submoney = round($submoney,2);
                $invocieInfo = $dataProvider->getData();
                if (!empty($invocieInfo)) {
                    $invoice_id = current($invocieInfo)->invoice_id;
                    $schoolid = current($invocieInfo)->schoolid;
                    $invoiceTitle = null;
                    foreach ($invocieInfo as $invoice) {
                        $invoiceTitle[$invoice->invoice_id] = $invoice->title;
                    }

                    $totalDueAmount = Invoice::model()->getTotalDueAmount($invocieInfo);
                    //用户选择了分开账单支付，且只选择了一个账单,分开支付的金额不大于该账单总金额
                    if ($submoney >0 && count($invoiceId) == 1 && $submoney < $totalDueAmount) {
                        $totalDueAmount = $submoney;
                    }
                    // $schoolid = $this->getSchoolId();

                    Yii::import('common.extensions.wxPay.*');
                    Yii::import('common.models.wxpay.*');
                    $wxpayInfo = CommonUtils::LoadConfig('CfgWxPayGlobal');

                    $number_code = $wxpayInfo[$schoolid]['number_code'];
                    $wechatPayOrder = new WechatPayOrder();
                    $wechatPayOrder->orderid = $wechatPayOrder->genOrderID($number_code.$invoice_id);
                    $wechatPayOrder->payable_amount = $totalDueAmount;
                    $wechatPayOrder->fact_amount = 0 ;
                    $wechatPayOrder->schoolid = $schoolid ;
                    $wechatPayOrder->childid = $this->getChildId() ;
                    $wechatPayOrder->type = 'NATIVE' ;
                    $wechatPayOrder->status = 0 ;
                    $wechatPayOrder->settlement_status = 0 ;
                    $wechatPayOrder->order_time = time() ;
                    $wechatPayOrder->update_timestamp = time() ;
                    $wechatPayOrder->uid = Yii::app()->user->id;
                    if($wechatPayOrder->save()){
                        foreach ($invocieInfo as $invoice) {
                            $wechatPayOrderItem = new WechatPayOrderItem();
                            $wechatPayOrderItem->orderid = $wechatPayOrder->orderid;
                            $wechatPayOrderItem->invoice_id = $invoice->invoice_id;
                            $wechatPayOrderItem->amount = Invoice::model()->renderDueAmount($invoice);
                            if (count($invocieInfo)==1) {
                            $wechatPayOrderItem->amount = $totalDueAmount;
                            }
                            $wechatPayOrderItem->save();
                        }
                        $invoiceTitle = count($invoiceTitle) == 1 ?current($invoiceTitle):current($invoiceTitle).'等';
                        $nativePay = new NativePay();
                        $nativePay->cfg = $wxpayInfo[$schoolid];
                        $nativePay->values['body'] = $invoiceTitle;
                        $nativePay->values['product_id'] = $invoice_id;
                        $nativePay->values['notify_url'] = Yii::app()->params['appsUrl'].'/wechatPay/wechatPay';
                        $nativePay->values['out_trade_no'] = $wechatPayOrder->orderid;
                        $nativePay->values['total_fee'] = $nativePay->transformMoney($totalDueAmount);
                        $nativePay->values['attach'] = $schoolid;
                        $nativePay->init();
                        $rs = $nativePay->createQRcode();
                        $url = $rs['code_url'];
                    }
                }
                $view_data = array(
                    'paymentMethod' => 'wxpay',
                    'dataProvider' => $dataProvider,
                    'url' => $url,
                    'totalDueAmount' => $totalDueAmount,
                    'orderid' => $wechatPayOrder->orderid,
                );
            }else {
                Yii::app()->user->setFlash('payment', "Please Choose a Payment Method.");
                $this->redirect($this->createUrl('profile/welcome'));
            }

            Yii::app()->clientScript->registerScriptFile(Yii::app()->theme->baseUrl . '/js/paying.js?t=' . Yii::app()->params['refreshAssets'], CClientScript::POS_HEAD);
            Yii::app()->clientScript->registerCssFile(Yii::app()->theme->baseUrl . '/css/paying.css?t=' . Yii::app()->params['refreshAssets']);

            $this->render('onlineConfirm', $view_data);
        } else {
            Yii::app()->user->setFlash('payment', "Request method must be post.");
            $this->redirect($this->createUrl('profile/welcome'));
        }
    }

    /**
     * 在将数据提交给第三方公司之前
     * 需要将必要信息存于ivyonline
     * <AUTHOR>
     * @time 2012-11-19
     */
    public function actionOnlineBeforePaid() {
        $json_data['flag'] = 'failed';
        if (Yii::app()->request->isAjaxRequest) {

            $paymentMothod = Yii::app()->request->getPost('payment_mothod');
            if ('yeepay' == $paymentMothod) {

                Mims::LoadHelper('HyeepayCommon');
                $yc = new HyeepayCommon();
                //$yc->init($schoolId);
                //$yc->initTest($schoolId);
                $due_amount = empty($_POST['due_amount']) ? null : $_POST['due_amount'];
                $beforePaidInfo = new BeforePaidInfoForm();
                $beforePaidInfo->attributes = $_POST['BeforePaidInfoForm'];
                $customHmac = $yc->HmacMd5($beforePaidInfo->childId . $beforePaidInfo->schoolId . $beforePaidInfo->autoOrderId . $beforePaidInfo->totalDueAmount . $beforePaidInfo->bankId . $beforePaidInfo->reqURL_onLine . $beforePaidInfo->invoiceId, 'gs');

                if ($beforePaidInfo->customHmac == $customHmac) {
                    //签名正确了 才进行操作
                    $current_time = time();
                    $orderInfo = array(
                        'orderId' => $beforePaidInfo->autoOrderId,
                        'payable_amount' => $beforePaidInfo->totalDueAmount,
                        'rb_BankId' => $beforePaidInfo->bankId,
                        'schoolId' => $beforePaidInfo->schoolId,
                        'childId' => $beforePaidInfo->childId,
                        'operatorId' => Yii::app()->user->id,
                        // 支付方式 最好写成配置 xoops全局常量  MIMS_PAYMENT_ONLINEPAYMENT 80
                        'payment_method' => 80,
                        'status' => 0,
                        'orderTime' => $current_time,
                        'updateTime' => $current_time,
                    );
                    $detailInfo = array(
                        'it_orderId' => $beforePaidInfo->autoOrderId,
                        'status' => 0,
                        'orderTime' => $current_time,
                        'updateTime' => $current_time,
                    );
                    $flag = YpInvoiceTransaction::model()->insertOrderAndDetail($orderInfo, $detailInfo, $due_amount);
                    if ($flag) {
                        $json_data['flag'] = "success";
                    }
                } else {
                    $json_data['flag'] = 'differentHmac';
                }
            } else if ('alipay' == $paymentMothod) {

                $mySign = Yii::app()->request->getPost('mySign', "");
                $dueAmount = Yii::app()->request->getPost('due_amount', array());
                $invoiceId = array_keys($dueAmount);

                $invoiceIdStr = implode(',', $invoiceId);
                $dueAmountStr = implode(',', $dueAmount);

                $requestParameter = new RequestParameter();
                $requestParameter->attributes = $_POST['RequestParameter'];

                require_once Yii::app()->basePath . DIRECTORY_SEPARATOR . 'extensions' . DIRECTORY_SEPARATOR . 'alipay' . DIRECTORY_SEPARATOR . 'lib' . DIRECTORY_SEPARATOR . 'alipayCore.php';
                Yii::import('ext.alipay.*');

                if (isset(Yii::app()->params['onlineBanking']) && 'production' == Yii::app()->params['onlineBanking']) {
                    // $schoolid = $this->getSchoolId();
                    $schools = $_POST['school'];
                    $schoolid = current($schools);
                } else {
                    $schoolid = 'test';
                }
                $alipayProxy = new AlipayProxy($schoolid);
                $alipayConfig = $alipayProxy->getAttributes();
                $alipayConfig['invoiceIdStr'] = $invoiceIdStr;
                $alipayConfig['dueAmountStr'] = $dueAmountStr;
                $alipayConfig['bankid'] = $requestParameter->defaultbank;
                $alipayConfig['orderid'] = $requestParameter->out_trade_no;
                //除去待签名参数数组中的空值和签名参数
                $para_filter = paraFilter($alipayConfig);
                //对待签名参数数组排序
                $para_sort = argSort($para_filter);
                //生成签名结果
                $confirmSign = buildMysign($para_sort, trim($alipayConfig['key']), strtoupper(trim($alipayConfig['sign_type'])));

                //签名一致 才进行操作
                if ($mySign == $confirmSign) {

                    $execute_failed = false;
                    $current_time = time();

                    $alipayOrder = new AlipayOrder();

                    $alipay_trans = $alipayOrder->dbConnection->beginTransaction();

                    $alipayOrder->id = $requestParameter->out_trade_no;
                    $alipayOrder->fact_amount = 0.00;
                    $alipayOrder->payable_amount = $requestParameter->total_fee;
                    $alipayOrder->schoolid = $schoolid;
                    $alipayOrder->childid = $this->getChildId();
                    $alipayOrder->operator_id = Yii::app()->user->id;
                    // 支付宝的 在线支付
                    $alipayOrder->payment_method = 80;
                    $alipayOrder->bankid = $requestParameter->defaultbank;
                    $alipayOrder->status = 0;
                    $alipayOrder->settlement_status = 0;
                    $alipayOrder->order_time = $current_time;
                    $alipayOrder->update_timestamp = $current_time;

                    try {
                        $order_flag = $alipayOrder->save();
                    } catch (Exception $exc) {
                        $order_flag = false;
                        //echo $exc->getTraceAsString();
                    }

                    if (true == $order_flag) {
                        foreach ($dueAmount as $invoice_id => $amount) {
                            $orderDetail = new AlipayOrderDetail();
                            $orderDetail->order_id = $alipayOrder->id;
                            $orderDetail->invoice_id = $invoice_id;
                            $orderDetail->amount = $amount;
                            $orderDetail->status = 0;
                            $orderDetail->order_time = $current_time;
                            $orderDetail->update_timestamp = $current_time;
                            try {
                                $order_detail_flag = $orderDetail->save();
                            } catch (Exception $exc) {
                                $order_detail_flag = false;
                                //echo $exc->getTraceAsString();
                            }
                            if (true != $order_detail_flag) {
                                $execute_failed = true;
                                break;
                            }
                        }
                    } else {
                        $execute_failed = true;
                    }
                    if (true == $execute_failed) {
                        // 回滚
                        $alipay_trans->rollback();
                    } else {
                        // 提交
                        $alipay_trans->commit();
                        $requestParameter->update_timestamp = $current_time;
                        $requestParameter->save();
                        $json_data['flag'] = "success";
                    }
                } else {
                    $json_data['flag'] = 'differentHmac';
                }
            } elseif ('allinpay' == $paymentMothod) {
                Yii::import('common.models.alipay.*');
                Yii::import('common.extensions.allinpay.*');

                $dueAmount = Yii::app()->request->getPost('due_amount', array());
                $invoiceId = array_keys($dueAmount);

                $invoiceIdStr = implode(',', $invoiceId);
                $dueAmountStr = implode(',', $dueAmount);

                $requestParameter = new RequestParameter();
                $requestParameter->attributes = Yii::app()->request->getPost('RequestParameter');
                $signMsg = $requestParameter->sign;
                $orderDatetime = Yii::app()->request->getPost('orderDatetime');

                if (isset(Yii::app()->params['onlineBanking']) && 'production' == Yii::app()->params['onlineBanking']) {
                    $schoolid = $this->getSchoolId();
                } else {
                    $schoolid = 'test';
                }
                //构建签名串
                $allinpay = new Allinpay($schoolid);
                $allinpay->pickupUrl = $requestParameter->return_url;
                $allinpay->receiveUrl = $requestParameter->notify_url;
                $allinpay->orderNo = $requestParameter->out_trade_no;
                $allinpay->orderAmount = $requestParameter->total_fee*100;
                $allinpay->orderDatetime = $orderDatetime;
                $allinpay->payType = $requestParameter->payment_type;
                $allinpay->issuerId = $requestParameter->defaultbank;
                $allinpay->bulidSignMsg();
                //验证签名串
                if ($allinpay->signMsg !='' && $allinpay->signMsg == $signMsg) {
                    $execute_failed = false;
                    $current_time = time();

                    $alipayOrder = new AlipayOrder();

                    $alipay_trans = $alipayOrder->dbConnection->beginTransaction();
                    //新建订单
                    $alipayOrder->id = $requestParameter->out_trade_no;
                    $alipayOrder->fact_amount = 0.00;
                    $alipayOrder->payable_amount = $requestParameter->total_fee;
                    $alipayOrder->schoolid = $allinpay->schoolid;
                    $alipayOrder->childid = $this->getChildId();
                    $alipayOrder->operator_id = Yii::app()->user->id;
                    $alipayOrder->payment_method = InvoiceTransaction::TYPE_ONLINE_ALLINPAY;
                    $alipayOrder->bankid = $requestParameter->defaultbank;
                    $alipayOrder->status = 0;
                    $alipayOrder->settlement_status = 0;
                    $alipayOrder->order_time = $current_time;
                    $alipayOrder->update_timestamp = $current_time;

                    try {
                        $order_flag = $alipayOrder->save();
                    } catch (Exception $exc) {
                        $order_flag = false;
                    }

                    if (true == $order_flag) {
                        foreach ($dueAmount as $invoice_id => $amount) {
                            $orderDetail = new AlipayOrderDetail();
                            $orderDetail->order_id = $alipayOrder->id;
                            $orderDetail->invoice_id = $invoice_id;
                            $orderDetail->amount = $amount;
                            $orderDetail->status = 0;
                            $orderDetail->order_time = $current_time;
                            $orderDetail->update_timestamp = $current_time;
                            try {
                                $order_detail_flag = $orderDetail->save();
                            } catch (Exception $exc) {
                                $order_detail_flag = false;
                            }
                            if (true != $order_detail_flag) {
                                $execute_failed = true;
                                break;
                            }
                        }
                    } else {
                        $execute_failed = true;
                    }
                    if (true == $execute_failed) {
                        // 回滚
                        $alipay_trans->rollback();
                    } else {
                        // 提交
                        $alipay_trans->commit();
                        $requestParameter->update_timestamp = $current_time;
                        $requestParameter->save();
                        $json_data['flag'] = "success";
                    }
                } else {
                    $json_data['flag'] = 'differentHmac';
                }
            }
        }
        echo CJSON::encode($json_data);
        Yii::app()->end();
    }

    public function actionPoints()
    {
    	//child school config
    	$schoolConfig = PointsConfig::model()->getSchoolConfig($this->getSchoolId());
    	if ($schoolConfig===null || !$schoolConfig->status )
    	{
    		$this->render('points_noservice');
            Yii::app()->end();
    	}
    	//get child of credits
    	$points = InvoiceTransaction::model()->getPointsCredit($this->getChildId(),strtotime($this->pointStartDate));
    	//image url
    	$viewData['credits'] = $points;
    	//product list
    	$condition = 'status=1';
    	if ($schoolConfig->maximum)
    	{
    		$condition .=  ' and credits<='.$schoolConfig->maximum;
    	}
    	$productList = new CActiveDataProvider('PointsProduct', array(
                        'criteria' => array(
                            'condition' => $condition,
                            'order' => 'status Desc ,credits ASC',
    						'with' => array('Images'),
                        ),
                        'pagination' => array(
                            'pageSize' => 20,
                        ),
                    ));
    	//order list
    	$orderList = new CActiveDataProvider('PointsOrder', array(
                        'criteria' => array(
                            'condition' => 'childid=:childid',
                            'params' => array(':childid' => $this->getChildId()),
                            'order' => 'created_timestamp Desc',
    						'with' => array('Product'),
                        ),
                        //'pagination' => array(
                        //    'pageSize' => 20,
                        //),
         ));
        Yii::app()->clientScript->registerCssFile(Yii::app()->theme->baseUrl.'/css/points.css?t='.Yii::app()->params['refreshAssets']);
    	$this->render('points',array('points'=>$points,'productList'=>$productList,'orderList'=>$orderList,'viewData'=>$viewData));
    }

    public function actionExchange()
    {
    	$id = Yii::app()->request->getPost('id',0);
    	$number = intval(Yii::app()->request->getPost('number',0));
        $ret = array();
    	if ($id && is_int($number) && $number > 0 )
    	{
	    	$product = PointsProduct::model()->findByPk($id);
	    	//get child of credits
	    	$credit = InvoiceTransaction::model()->getPointsCredit($this->getChildId(),strtotime($this->pointStartDate));
	    	if ($product->status)
	    	{
                if ($number <= $product->stock){
                    if ($credit >= $product->credits*$number)
		    	{
		    		$model = new PointsOrder;
		    		$model->category = 'order';
		    		$model->productid = $id;
		    		$model->childid = $this->getChildId();
		    		$model->schoolid = $this->getSchoolId();
		    		$model->created_timestamp = time();
		    		$model->created_userid = Yii::app()->user->id;
		    		$model->credits = $product->credits;
		    		$model->status = PointsStatus::STATS_CREATED;
                        $model->quantity = $number;
		    		$model->update_timestamp = 0;
		    		$model->update_userid = 0;
		    		if ($model->save())
		    		{
		    			$stock = PointsStock::model()->countStock($id);
						PointsProduct::model()->updateStock($id, $stock);
		    			$status = new PointsStatus;
		    			$status->itemid = $model->id;
		    			$status->status = PointsStatus::STATS_CREATED;
		    			$status->update_timestamp = time();
		    			$status->update_userid = Yii::app()->user->id;
		    			if ($status->save())
		    			{
                                $ret['state'] = 'success';
		    				Yii::app()->user->setFlash('success', Yii::t("payment", 'Thank you for your order. The gift will be delivered to the campus in about one month and our office staff will inform you when it has arrived.'));
                                //$this->redirect(array('points'));
		    			}
		    		}
		    	}
		    	else
		    	{
                        $ret['state'] = 'fail';
                        $ret['message'] = Yii::t("payment", 'You do not have sufficient Ivy Points to order this product!');
    //		    		Yii::app()->user->setFlash('error', Yii::t("payment", 'You do not have sufficient Ivy Points to order this product!'));
//                	$this->redirect(array('points'));
		    	}
	    	}
                else {
                    $ret['state'] = 'fail';
                    $ret['message'] = Yii::t("payment", 'Temporarily out of stock. We will replenish as soon as possible!');
                }
	    	}
	    	else
	    	{
                $ret['state'] = 'fail';
                $ret['message'] = Yii::t("payment", 'This product is temporarily out of stock!');
//	    		Yii::app()->user->setFlash('error', Yii::t("payment", 'This product is temporarily out of stock!'));
//                $this->redirect(array('points'));
	    	}
            echo CJSON::encode($ret);
    	}
    }

    public function actionCancelOrder()
    {
    	$id = Yii::app()->request->getPost('id',0);
    	if ($id)
    	{
    		$order = PointsOrder::model()->findByPk($id);
    		if (time()<=$order->created_timestamp+PointsOrder::model()->cancleOrderParam() && $order->status==PointsStatus::STATS_CREATED)
    		{
    			if ($order->delete())
    			{
    				$stock = PointsStock::model()->countStock($order->productid);
					PointsProduct::model()->updateStock($order->productid, $stock);
					PointsStatus::model()->deleteAll('itemid=:itemid',array(':itemid'=>$id));
    				Yii::app()->user->setFlash('success', Yii::t("payment", 'Order has been cancelled!'));
                    $ret['state'] = 'success';
//                	$this->redirect(array('points'));
    			}
    		}
    		else
    		{
//    			Yii::app()->user->setFlash('error', Yii::t("payment", 'Order is being proceeded and cannot be cancelled!'));
                $ret['state'] = 'fail';
                $ret['message'] = Yii::t("payment", 'Order is being proceeded and cannot be cancelled!');
//                $this->redirect(array('points'));
    		}
            echo CJSON::encode($ret);
    	}
    }

    public function actionPointsDetail()
    {
    	//get child of credits balance
    	$tranCredit = InvoiceTransaction::model()->getTotalPoints($this->getChildId(),strtotime($this->pointStartDate));
    	$usedCredit = PointsOrder::model()->getUsedCredits($this->getChildId());
//    	$tranPointList = new CActiveDataProvider('InvoiceTransaction', array(
// 		    'criteria'=>array(
// 		        'condition'=>'childid=:childid and transactiontype=:transactiontype and timestampe>:timestampe',
// 	    		'params'=>array(':childid'=>$this->getChildId(),':transactiontype'=>80,':timestampe'=>strtotime($this->pointStartDate)),
// 		        'order'=>'timestampe Desc',
//    			'select'=>'title,amount,timestampe'
// 		    ),
// 		    'pagination'=>array(
// 		    	'pageSize'=>20
// 		    ),
// 		));
// //    	$usedPointList = PointsOrder::model()->getUsedCreditDetail($this->getChildId());
// 		$usedPointList = new CActiveDataProvider('PointsOrder', array(
// 		    'criteria'=>array(
// 		        'condition'=>'t.category=:category and t.childid=:childid',
// 	    		'params'=>array(':childid'=>$this->getChildId(),':category'=>'order'),
// 		        'order'=>'t.created_timestamp DESC',
//    			'select'=>'credits,productid,created_timestamp',
//    			'with'=>'Product'
// 		    ),
// 		    'pagination'=>array(
// 		    	'pageSize'=>20
// 		    ),
// 		));
    	$this->renderPartial('points_detail',array(
    											'tranCredit'=>$tranCredit,
    											'usedCredit'=>$usedCredit,
    											//'tranPointList'=>$tranPointList,
    											//'usedPointList'=>$usedPointList,
    										 ),false, true
    	);
    }

    /**
     * ajax方式返回银行限额提示
     * @param  [string] $bankId [银行代码]
     */
    public function actionGetBankInfo($bankId)
    {
        $file = '/allinpay/quota/'.$bankId;
        $common = '/allinpay/quota/common';
        if($this->getViewFile($file) != false){
            $allinpayBankInfo = Mims::LoadConfig('CfgAllinpayBankInfo');
            $bankTitle = $allinpayBankInfo[$bankId]['cn_name'];
            echo CHtml::openTag('div',array('class'=>'bank-quota'));
            echo CHtml::openTag('h2');
            echo Yii::t("payment", ":bankTitle 网上支付限额", array(":bankTitle"=>$bankTitle));
            echo CHtml::closeTag('h2');
            echo $this->renderPartial($file);
            echo $this->renderPartial($common);
            //刷新支付金额
            echo CHtml::script("initTotalDueAmount();");
            echo CHtml::closeTag('div');
        }else{
            echo CHtml::openTag('div');
            echo CHtml::closeTag('div');
        }
    }

    /**
     * 接收处理通联返回的支付结果（同步）
     */
    public function actionAllinpayPickup()
    {
        if(Yii::app()->request->isPostRequest){
            Yii::import('common.models.alipay.*');
            Yii::import('common.models.invoice.*');

            $orderNo = Yii::app()->request->getPost('orderNo','');
            $alipayOrder = AlipayOrder::model()->findByPk($orderNo);
            if (isset($alipayOrder)) {
                $schoolid = $alipayOrder->schoolid;
                $allinpayPartnerInfo = CommonUtils::LoadConfig('CfgAllinpayPartnerInfo');
                if (!empty($allinpayPartnerInfo[$schoolid])) {
                    Yii::import('common.extensions.allinpay.*');

                    $allinpay = new Allinpay($schoolid);
                    //验证签名
                    if($allinpay->verifySignMsg($_POST))
                    {   
                        if($allinpay->payResult == 1)
                        {
                            //处理订单相关表状态
                            if ($alipayOrder->status == 0) {
                                //更新订单表
                                $alipayOrder->fact_amount = $allinpay->getRealMoney();
                                $alipayOrder->status = 1;
                                $alipayOrder->update_timestamp = time();
                                if($alipayOrder->save()){
                                    $payinfo = '订单支付成功！此次支付金额为：'.$allinpay->getRealMoney();
                                    //更新账单状态
                                    $criteria=new CDbCriteria;
                                    $criteria->compare('order_id',$allinpay->orderNo);
                                    $alipayOrderDetails = AlipayOrderDetail::model()->findAll($criteria);

                                    $payType = InvoiceTransaction::TYPE_ONLINE_ALLINPAY;
                                    Yii::import('common.components.policy.*');
                                    $policyApi = new PolicyApi($schoolid);
                                    $amount = round($allinpay->getRealMoney(),2);

                                    $invoices = array();
                                    foreach ($alipayOrderDetails as $alipayOrderDetail) {
                                        $invoices[] = $alipayOrderDetail->invoice;
                                    }
                                    $totalDueAmount = round(Invoice::model()->getTotalDueAmount($invoices),2);
                                    //如果与该订单号相关的账单记录只有一条，且$amount不大于$totalDueAmount,则此次支付为分割账单
                                    $rs = 1;
                                    if (count($invoices) == 1 && $totalDueAmount >= $amount) {
                                        $rs = $policyApi->Pay($invoices,$payType,$amount);
                                    }else{
                                        //如果与该订单相关的多个账单总金额等于订单返回金额
                                        if ($totalDueAmount == $amount) {
                                            $rs = $policyApi->Pay($invoices,$payType,$amount); 
                                        }
                                    }
                                    if (!$rs) {
                                        $payinfo .= ',账单状态已更改';
                                        //更新订单详情表
                                        AlipayOrderDetail::model()->updateAll(array('status'=>1,'update_timestamp'=>time()),'order_id = :order_id',array('order_id'=>$allinpay->orderNo));
                                    }else{
                                        $payinfo .= ',账单状态更新失败';
                                    }
                                }else{
                                    $payinfo = '订单更新失败';
                                }
                            }else{
                                $payinfo = '订单已处理';
                            }
                        }else{
                            $payinfo = '订单支付失败！';
                        } 
                    }else{
                        $payinfo = '报文验签失败!';
                    }
                }else{
                    $payinfo = '学校参数出错!';
                }
            }else{
                $payinfo = '订单号出错!';
            }

            $this->render('allinpaypickup',array(
                'payinfo'=>$payinfo,
                ));
        }
    }

}