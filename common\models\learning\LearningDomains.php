<?php

/**
 * This is the model class for table "ivy_learning_domains".
 *
 * The followings are the available columns in table 'ivy_learning_domains':
 * @property integer $id
 * @property string $type
 * @property string $title_cn
 * @property string $title_en
 * @property string $intro_cn
 * @property string $intro_en
 * @property integer $sort
 * @property integer $status
 * @property integer $updated_at
 * @property integer $updated_by
 */
class LearningDomains extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_learning_domains';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('type, title_cn, title_en, intro_cn, intro_en, status, updated_at, updated_by', 'required'),
			array('sort, status, updated_at, updated_by, version', 'numerical', 'integerOnly'=>true),
			array('type, title_cn, title_en, special_type, school_type', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, type, title_cn, title_en, intro_cn, intro_en, sort, status, updated_at, updated_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'items' => array(self::HAS_MANY, 'LearningDomainsItems', 'pid', 'order'=>'items.sort ASC', 'condition'=>'items.status=1'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'type' => Yii::t('learing', '适用班级'),
			'title_cn' => Yii::t('learing', '中文标题'),
			'title_en' => Yii::t('learing', '英文标题'),
			'intro_cn' => Yii::t('learing', '中文介绍'),
			'intro_en' => Yii::t('learing', '英文介绍'),
			'sort' => '显示顺序',
			'special_type' => '显示顺序',
			'version' => 'Version',
			'school_type' => 'SchoolType',
			'status' => 'Status',
			'updated_at' => Yii::t('learing', '更新时间'),
			'updated_by' => Yii::t('learing', '操作人'),
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('type',$this->type,true);
		$criteria->compare('title_cn',$this->title_cn,true);
		$criteria->compare('title_en',$this->title_en,true);
		$criteria->compare('intro_cn',$this->intro_cn,true);
		$criteria->compare('intro_en',$this->intro_en,true);
		$criteria->compare('sort',$this->sort);
		$criteria->compare('status',$this->status);
		$criteria->compare('updated_at',$this->updated_at);
		$criteria->compare('updated_by',$this->updated_by);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return LearningDomains the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	public static function getOptionTitle($selectedSemester)
	{
		if ($selectedSemester == 1) {
			return array(
				'4' => Yii::t('teaching', 'Not Observed'),
				'1' => Yii::t('teaching', 'Making Progress'),
				'2' => Yii::t('teaching', 'Meeting Expectations'),
				'3' => Yii::t('teaching', 'Exceeding Expectations'),
			);
		} else {
			return array(
				'1' => Yii::t('teaching', 'Making Progress'),
				'2' => Yii::t('teaching', 'Meeting Expectations'),
				'3' => Yii::t('teaching', 'Exceeding Expectations'),
			);
		}
	}

	public static function getQfOptionTitle()
	{
		return array(
			'1' => Yii::t('teaching', 'Experiencing Challenges'),
			'2' => Yii::t('teaching', 'Making Progress'),
			'3' => Yii::t('teaching', 'Meeting Expectations'),
			'4' => Yii::t('teaching', 'Achieving Excellence'),
		);
	}

    public function getContent($type = "title")
    {
        switch (strtolower($type)) {
            case "title":
                return ( Yii::app()->language == "zh_cn" ) ? $this->title_cn : $this->title_en;
                break;
            case "intro":
                return ( Yii::app()->language == "zh_cn" ) ? $this->intro_cn : $this->intro_en;
                break;
        }
        return false;
    }

    public function keyMilestones()
    {
        $ld = $this->getContent();
		if ($this->school_type == 'qf') {
			return $ld;
		}
        if($ld == 'The Arts')
            $ld = 'Arts';
        return Yii::t('teaching', 'Key :ld Milestones', array(':ld'=>$ld));
    }

	public static function getDomainsTypeList($type = Branch::TYPE_CAMPUS_MI_PRESCHOOL) {
		$typeList = IvyClass::getClassTypes(true, $type);
        if (isset($typeList['c'])) {
            unset($typeList['c']);
        }
		return $typeList;
	}
}
