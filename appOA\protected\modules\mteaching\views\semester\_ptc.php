<?php

$disabled = array('class'=>"form-control");
$disabledHour = array('class' => 'form-control', 'style'=>'width:50%', 'empty'=>Yii::t('teaching', 'Hour'));
$disabledMinute = array('class' => 'form-control', 'style'=>'width:50%', 'empty'=>Yii::t('teaching', 'Minute'));
//if($this->branchObj->type == 50){
//    $disabled = array('class'=>"form-control", 'disabled' => 'disabled');
//    $disabledHour = array('class' => 'form-control', 'style'=>'width:50%', 'empty'=>Yii::t('teaching', 'Hour'), 'disabled' => 'disabled');
//    $disabledMinute = array('class' => 'form-control', 'style'=>'width:50%', 'empty'=>Yii::t('teaching', 'Minute'), 'disabled' => 'disabled');
//}

?>
<div class="col-lg-3 col-md-3 col-sm-4 col-xs-6">
    <div class="panel panel-default">
        <div class="panel-heading">
            <?php echo Yii::t('teaching','1. Basic Settings');?>
        </div>
        <div class="panel-body">
            <div class="form-group">
                <?php echo CHtml::label($taskData['plan']->getAttributeLabel('default_duration'),
                    CHtml::getIdByName('ParentMeetingPlan[default_duration]'),array('class'=>"control-label")) ?>
                <div class="input-group">
                    <?php for($i=5;$i<121;$i+=5){$durations[$i]=$i.' '.Yii::t('global', 'min');};
                        echo CHtml::dropDownList('ParentMeetingPlan[default_duration]',
                            $taskData['plan']->default_duration, $durations, $disabled) ?>
                        <span class="input-group-btn">
                           <?php if(0 && $this->branchObj->type == 50){ ?>
                                <button class="btn btn-primary disabled"><?php echo Yii::t('global','Save');?></button>
                            <?php }else{ ?>
                                <button class="btn btn-primary" type="button" onclick="saveDuration()"><?php echo Yii::t('global','Save');?></button>
                            <?php } ?>
                        </span>
                </div><!-- /input-group -->
            </div>
            <div class="form-group">
                <?php echo CHtml::label($taskData['plan']->getAttributeLabel('timeslot_starts'), false); ?>
                <ul class="list-group" id="timeslot-starts"></ul>

                <div class="input-group">
                    <?php for($i=8;$i<21;$i++){$t=sprintf('%02d', $i);$hours[$t]=$t;};
                        echo CHtml::dropDownList('timeslot_starts[hour]', '', $hours,
                            array('class'=>"form-control", 'style'=>'width:50%', 'empty'=>Yii::t('teaching', 'Hour'))) ?>
                    <?php for($i=0;$i<60;$i+=5){$t=sprintf('%02d', $i);$minutes[$t]=$t;};
                        echo CHtml::dropDownList('timeslot_starts[minute]', '', $minutes,
                            array('class'=>"form-control", 'style'=>'width:50%', 'empty'=>Yii::t('teaching', 'Minute'))) ?>
                    <div class="input-group-btn">
                        <button class="btn btn-primary" type="button" onclick="saveTimeslot()"><?php echo Yii::t('global','Add');?></button>
                    </div>
                </div><!-- /input-group -->
            </div>
        </div>
    </div>

    <div class="panel panel-default">
        <div class="panel-heading">
            <?php echo Yii::t('teaching','3. Open to parents');?>
        </div>
        <div class="panel-body" id="status-wrapper">

        </div>
    </div>

</div>


<div class="col-lg-9 col-md-9 col-sm-8 col-xs-6">
    <div class="panel panel-default">
        <div class="panel-heading">
            <?php echo Yii::t('teaching','2. Available appointment');?>
        </div>
        <div class="panel-body">

            <div class="row">
                <div class="col-md-10">
                    <ul class="nav nav-pills" id="ptc-month-list">
                        <?php foreach($taskData['months'] as $month):?>
                        <li data-month="<?php echo $month;?>-01" class="<?php echo $month==$taskData['selectMonth']?'active':'';?>">
                            <a href="javascript:"><?php echo $month;?></a>
                        </li>
                        <?php endforeach;?>
                        <div class="pull-right">
                            <a href="<?php echo $this->createUrl('printPtc', array('id'=>$taskData['plan']->id));?>"
                               class="btn btn-primary" target="_blank"><span class="glyphicon glyphicon-print"></span> <?php echo Yii::t('global', 'Print')?></a>
                        </div>
                    </ul>

                    <?php
                    $this->widget('common.extensions.EFullCalendar.EFullCalendar', array(
                        'id' => 'ptcFull-calendar',
//                        'themeCssFile'=>'cupertino/jquery-ui.min.css',

                        // raw html tags
                        'htmlOptions'=>array(
                            // you can scale it down as well, try 80%
                            'style'=>'width:100%'
                        ),
                        // FullCalendar's options.
                        'options'=>array(
                            'header'=>array(),
                            'firstDay' => 0,
                            'timezone' => 'local',
                            'defaultDate' => $taskData['selectMonth'],
//                            'editable' => true,

                            'selectable' => true,
//                  'selectHelper' => true,
                            'select' => "js:function(start,end){dayClick(start,end)}",
                            'eventClick' => "js:function(event, element){timeSlotClick(event, element)}",

                            //示例数据
                            'events' => $taskData['events'],
                            'timeFormat' => 'HH:mm',
                            'displayEventEnd' => true,
                        )
                    ));
                    ?>

                </div>
                <div class="col-md-2">
                    <div id="planClass">
                        <?php $plan =  $taskData['plan']; ?>
                        <?php
                        if($plan->status){ ?>
                            <!-- <button class="btn btn-primary pull-right" onclick='allSend()'><?php echo Yii::t("teaching", 'Send Reminder');?></button> -->
                        <?php } ?>
                    </div>
                    <div class="clearfix"></div>
                    <ul class="media-list mt15" id="ptc-child-list"></ul>
                </div>
            </div>


        </div>
    </div>

</div>

<div id="timeslotwithday" class="modal fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true"  onclick="closeds()">&times;</span><span class="sr-only">Close</span></button>
                <h4 class="modal-title"><?php echo Yii::t('teaching','Select PTC Timeslot');?> <small></small></h4>
            </div>
            <?php echo CHtml::form($this->createUrl('saveTimeslotwithDay', array('classid'=>$this->selectedClassId)), 'post', array('class'=>'J_ajaxForm'));?>
            <?php echo CHtml::hiddenField('planid', $taskData['plan']->id)?>
            <div class="modal-body">
                <div class="J_check_wrap p20" id="timeslot-options">
                    <div class="checkbox">
                        <label><input type="checkbox" class="J_check_all" data-checklist="J_check_c1" data-direction="y"> <?php echo Yii::t("global", 'Select All');?></label>
                    </div>
                </div>
            </div>
            <div class="modal-footer pop_bottom">
                <button type="button" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Save')?></button>
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Close')?></button>
            </div>
            <?php echo CHtml::endForm();?>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
<!--启明星ds-->
<div id="Dstimeslotwithday" class="modal fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true"  onclick="closeds()">&times;</span><span class="sr-only">Close</span></button>
                <h4 class="modal-title"><?php echo Yii::t('teaching','Select PTC Timeslot');?> <small></small></h4>
            </div>
            <div class="modal-body">
                <div class="J_check_wrap" id="Dstimeslot-options">
                    <div class="alert alert-info" role="alert"><?php echo Yii::t('global', 'Choose the start times for all the timeslots on that date. Each timeslot will open up three default spots.')?></div>
                </div>
            </div>
            <div class="modal-footer pop_bottom">
                <button type="button" class="btn btn-default" data-dismiss="modal" onclick="closeds()"><?php echo Yii::t('global', 'Close')?></button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<div id="timeslotwithitem" class="modal fade">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                <h4 class="modal-title"></h4>
            </div>
            <?php echo CHtml::form($this->createUrl('saveTimeslotwithItem', array('classid'=>$this->selectedClassId)),
                'post', array('class'=>'J_ajaxForm form-horizontal'));?>
            <?php echo CHtml::hiddenField('unikey')?>
            <div class="modal-body">
                <div class="row" id="un-child-list"></div>
                <hr />
                <div class="form-group">
                    <label for="inputEmail3" class="col-sm-2 control-label"><?php echo Yii::t('teaching', 'Starting Time')?></label>
                    <div class="col-sm-10">
                        <p class="form-control-static" id="starttime"></p>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label"><?php echo Yii::t('teaching', 'Ending Time')?></label>
                    <div class="col-sm-10">
                        <div class="input-group">
                            <?php
                            $hours['21']='21';echo CHtml::dropDownList('endtime[hour]', '', $hours, $disabledHour) ?>
                            <?php echo CHtml::dropDownList('endtime[minute]', '',
                                $minutes, $disabledMinute) ?>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label for="inputEmail3" class="col-sm-2 control-label"><?php echo Yii::t('labels', 'Memo')?></label>
                    <div class="col-sm-10">
                        <?php echo CHtml::textArea('memo', '', array('class'=>'form-control', 'rows'=>3));?>
                    </div>
                </div>
            </div>
            <div class="modal-footer pop_bottom">
                <a class="btn btn-danger J_ajax_del" id="delchild" href="#"><?php echo Yii::t('teaching', 'Remove Appointment')?></a>
                <button type="button" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Save')?></button>
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Close')?></button>
            </div>
            <?php echo CHtml::endForm();?>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
<div class="sendcontent"> </div>
<style>
    .img-item{background: #f2f2f2;border: 1px solid #d5d5d5}
    .child-face{max-width: 40px;}
    #ptc-child-list .media{padding-left: 22px; position: relative}
    #ptc-child-list h5.flag{position: absolute;top: 2px; left: 0px; font-size:16px;color: #f2f2f2}
    #ptc-child-list .online h5.flag{color: #008000}
    th.fc-day-header{vertical-align: bottom;padding: 8px;}
    .fc-day-grid-event .fc-time{font-weight: normal !important;}
    /*.fc-time,.fc-title{color: #fff}*/
    .fc-event {font-size: 1em;}
    .pot .media{padding-left: 22px; position: relative}
    .pot h4.flag{position: absolute;top: 2px; left: 0px; font-size:16px;color: #f2f2f2}
    .pot .online h4.flag{color: #008000}
    .table tr td{
        vertical-align:middle !important;
    }
    .checkbox-custom {
        position: relative;
        margin-top: 0;
        display: inline-block;
    }
    /*
    将初始的checkbox的样式改变
    */
    .checkbox-custom input[type="checkbox"] {
        opacity: 0;/*将初始的checkbox隐藏起来*/
        position: absolute;
        cursor: pointer;
        z-index: 2;
        margin: -6px 0 0 0;
        top: 50%;
        left: 3px;
    }
    /*
    设计新的checkbox，位置
    */
    .checkbox-custom label:before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        margin-top: -9px;
        width:16px;
        height:16px;
        display: inline-block;
        border-radius: 2px;
        border: 1px solid #bbb;
        background: #fff;
    }
    /*
    点击初始的checkbox，将新的checkbox关联起来
    */
    .checkbox-custom input[type="checkbox"]:checked +label:after {
        position: absolute;
        display: inline-block;
        font-family: 'Glyphicons Halflings';
        content: "\e013";
        top:34%;
        left:0px;
        margin-top: -5px;
        font-size: 11px;
        line-height: 1;
        /*width: 14px;
        height: 13px;*/
        color: #333;
        width: 16px;
        height:16px;
        padding-top: 1px;
        padding-left: 2px;
        border-radius: 2px;
    }
    .mb0{
        margin-bottom: 0 !important
    }
    .bodbotnone{
        border-top: none;
        padding: 0;
        padding-bottom: 15px
    }
    .addchild tr td,.childname tr td{
        word-wrap:break-word;word-break:break-all
    }
    .mt5{
        
        margin-bottom: 0
    }
    .imgbig{
        width:2rem;
        height:2rem;
    }
    .sendlist{
        position: relative;
    }
    .textsend{
        position: absolute;
        right:10px;
        top: 50%;
        margin-top: -8px;
    }
    .pr0{
        padding-right: 0 !important
    }
    .pl20{
        padding:10px 0;
    }
    .dashed{
        border: 1px dashed #ddd;
        width: 50px;
        height:50px;
        line-height:50px;
        text-align: center;
        top: 0;
        border-radius: 5px;
    }
    .childrenName{
        height:22px;
        overflow: hidden;
        line-height: 22px;
        text-align: center;
    }
    .childrenNum{
        padding-top: 10px;
        width: 50px;
        position: relative;
        padding-left: 0 !important;
        margin-right:10px;
        height: 90px;
    }
    .closeimg{
        position: absolute;
        right: -5px;
        top: 5px;
        display: inline-block;
        width: 15px;
        height: 15px;
        border: 1px solid #989898;
        line-height: 13px;
        text-align: center;
        border-radius: 50%;
        color: #989898;
        background: #fff;
        font-size: 16px;
    }
    .childTime{
        padding-left:20px
    }
</style>

<script type="text/template" id="child-li-item-template">
    <li class="media class-child<%= online?' online':''%>" childid=<%- id %> <%if(online){%>onclick="childPopover(this)"<%}%> data-toggle="popover" data-placement="left" data-trigger="focus" data-content="" data-html="true">
        <img class="media-object child-face img-thumbnail pull-left" src="<?php echo Yii::app()->params['OAUploadBaseUrl']?>/childmgt/<%= photo %>" title="<%- name %>">
        <div class="media-body">
            <h5 class="media-heading"><%- name %></h5>
        </div>
        <h5 class="flag"><span class="glyphicon glyphicon-ok-sign"></span></h5>
    </li>
</script>

<script type="text/template" id="child-radio-item-template">
    <div class="col-sm-2 text-center">
        <label>
            <div>
                <img style="width: 80px;" class="img-thumbnail" src="<?php echo Yii::app()
                ->params['OAUploadBaseUrl']?>/childmgt/<%= photo %>" title="<%- name %>">
            </div>
            <div style="height: 22px;overflow: hidden;font-weight: normal;">
                <input type="radio" name="childid" value="<%= id%>" onchange="changeChild(this)"> <%- name%>
            </div>
        </label>
    </div>
</script>

<script type="text/template" id="ptc-plan-setstatus-template">
    <% if(parseInt(status) == 1){ %>
        <div class="alert alert-success text-center">
            <div class="p20"> <span class="glyphicon glyphicon-ok-sign"></span> <?php echo Yii::t('teaching', 'PTC is now opening for parents to make appointment')?></div>
            <button type="button" class="btn btn-danger btn-lg" onclick="setPlanStatus('offline', <%- id %>, this)"><?php echo Yii::t('teaching', 'Close Appointment')?></button>
        </div>
    <% }else{ %>
        <div class="alert alert-danger text-center">
            <div class="p20"> <span class="glyphicon glyphicon-exclamation-sign"></span> <?php echo Yii::t('teaching', 'PTC is now closed for parents to make appointment')?></div>
            <button type="button" class="btn btn-primary btn-lg" onclick="setPlanStatus('online', <%- id %>, this)"><?php echo Yii::t('teaching', 'Open Appointment')?></button>
        </div>
    <% } %>
</script>

<script type="text/template" id="send-plan-setstatus-template">
    <div id="sendData" class="modal fade" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog  modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" ><span onclick="closesend()">&times;</span><span class="sr-only">Close</span></button>
                <h4 class="modal-title"><?php echo Yii::t('teaching', 'Send wechat notification to parents')?></h4>
            </div>
            <div class="modal-body">
                <div class="col-md-6">
                    <div class="panel panel-info">
                        <div class="panel-heading"><?php echo Yii::t('teaching', 'Available to send')?></div>
                        <div class="J_check_wrap">
                            <table class="table  table-striped mb0">
                                <thead>
                                    <tr style="height: 33px">
                                        <th width='40' class="textcen">
                                            <% if(sendlist.length!=''){%>
                                            <div class="checkbox-custom checkbox-default">
                                                <input type="checkbox" class="J_check_all " data-checklist="J_check_c1" data-direction="y" checked>
                                                <label></label>
                                            </div>
                                            <%}%>
                                        </th>
                                        <th width="150"><?php echo Yii::t('global', 'Name')?></th>
                                        <th><img style="width: 20px" src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/weixin.png' ?>" alt=""></th>
                                    </tr>
                                </thead>
                            </table>
                            <div class="pre-scrollable" style="height:340px;margin-top:-1px">
                                <table class="table mb0">
                                    <tbody class="childname">
                                        <% for(var i=0;i< sendlist.length;i++){%>
                                        <tr id='tr<%=sendlist[i].id%>'>
                                            <td width='40' id='check<%=sendlist[i].id%>' class="textcen">
                                                <div class="checkbox-custom checkbox-default">
                                                    <input class="J_check" data-yid="J_check_c1" type="checkbox" name='send' value='<%=sendlist[i].id%>' checked>
                                                    <label></label>
                                                </div>
                                            </td>
                                            <td width="40" class="pr0">
                                                  <img class="media-object child-face img-thumbnail pull-left" src="<?php echo Yii::app()->params['OAUploadBaseUrl']?>/childmgt/<%= sendlist[i].photo %>" title="<%- sendlist[i].name %>">
                                            </td>
                                            <td width="100" >
                                                <span> <%= sendlist[i].name %></span>
                                            </td>
                                            <td id='<%=sendlist[i].id%>' class='sendlist'>
                                                <p class="mt5">
                                                    <% for(key in sendlist[i].parent){%> 
                                                    <img class="img-circle imgbig" src="<%= sendlist[i].parent[key].headimgurl %>" alt="<%= sendlist[i].parent[key].headimgurl %>" title="<%= sendlist[i].parent[key].name %>">
                                                    <%}%>
                                                </p>
                                                <p class="mt5 textsend" id="text<%=sendlist[i].id%>"></p>
                                            </td>

                                        </tr>
                                        <%}%>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer bodbotnone">
                        <button type="submit" class="btn btn-primary sendbtn" onclick="send()"><?php echo Yii::t('global', 'Send')?></button>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="panel panel-default">
                        <div class="panel-heading"><?php echo Yii::t('teaching', 'Unavailable to send')?></div>
                        <table class="table table-striped mb0">
                            <thead>
                                <tr>
                                    <th  width="150"><?php echo Yii::t('global', 'Name')?></th>
                                    <th><img style="width: 20px" src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/weixin.png' ?>" alt=""></th>
                                    <th width="78"><?php echo Yii::t('admissions', 'Status')?></th>
                                </tr>
                            </thead>
                        </table>
                        <div class="pre-scrollable" style="height:340px;margin-top:-1px">
                            <table class="table mb0">
                                <tbody class="addchild">
                                    <% for(var i=0;i< limit.length;i++){%>
                                    <tr>
                                        <td width="40" class="pr0">
                                            <img class="media-object child-face img-thumbnail pull-left" src="<?php echo Yii::app()->params['OAUploadBaseUrl']?>/childmgt/<%= limit[i].photo %>" title="<%- limit[i].name %>">
                                        </td>
                                        <td width="100"> 
                                           <span><%= limit[i].name %></span></td>
                                        <td>
                                            <% if(limit[i].parent.length!=''){%>
                                            <p class="mt5"> 
                                            <% for(key in limit[i].parent){%> 
                                            <img class="img-circle imgbig" src="<%= limit[i].parent[key].headimgurl %>" alt="<%= limit[i].parent[key].headimgurl %>" title="<%= limit[i].parent[key].name %>">
                                            <%}%>
                                            </p>
                                            <%}%>
                                            <div class="clearfix"></div>
                                        </td>
                                        <td  width="60"><?php echo Yii::t('teaching', 'Sent')?></td>
                                    </tr>
                                    <%}%>
                                    <% for(var i=0;i< unbound.length;i++){%>
                                    <tr>
                                     <td width="40" class="pr0"> 
                                         <img class="media-object child-face img-thumbnail pull-left" src="<?php echo Yii::app()->params['OAUploadBaseUrl']?>/childmgt/<%= unbound[i].photo %>" title="<%- unbound[i].name %>">
                                     </td>
                                        <td width="100"> 
                                            <span><%= unbound[i].name %></span>
                                        </td>
                                        <td><?php echo Yii::t('teaching', 'No wechat account linked')?></td>
                                        <td  width="50"></td>
                                    </tr>
                                    <%}%>
                                    <% for(var i=0;i< booked.length;i++){%>
                                    <tr>
                                        <td width="40" class="pr0"> 
                                            <img class="media-object child-face img-thumbnail pull-left" src="<?php echo Yii::app()->params['OAUploadBaseUrl']?>/childmgt/<%= booked[i].photo %>" title="<%- booked[i].name %>">
                                        </td>
                                        <td width="100">
                                             <span><%= booked[i].name %></span> 
                                        </td>
                                        <td>
                                            <p class="mt5">
                                             <% for(key in booked[i].parent){%> 
                                            <img class="img-circle imgbig" src="<%= booked[i].parent[key].headimgurl %>" alt="<%= booked[i].parent[key].headimgurl %>" title="<%= booked[i].parent[key].name %>">
                                            <%}%>
                                            </p>
                                        </td>
                                        <td  width="60"><?php echo Yii::t('teaching', 'Already subscribed')?></td>
                                    </tr>
                                    <%}%>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="modal-footer bodbotnone">
                         <span><?php echo Yii::t('teaching', 'Notification can be sent only once within 24 hours.')?></span>
                    </div>
                </div>

            </div>
            <div class="clearfix"></div>
        </div>
    </div>
    <!-- /.modal-content -->
</div>
<!-- /.modal-dialog -->
</div>
</script>
<script>
    var canSubmit = true;
    var dayClick = null;
    var timeSlotClick = null;
    var setPlanStatus = null;
    var currentData = {};
    var schoolData = '<?php echo Yii::app()->request->getParam('branchId', '')?>';
    var classid = <?php echo Yii::app()->request->getParam('classid', '')?>;

    var memos = <?php echo CJSON::encode($taskData['arr_memo'])?>;
    var select_childid = <?php echo CJSON::encode($taskData['select_childid']);?>;
    var childids = <?php echo CJSON::encode($taskData['arr_childid']);?>;
    var cdata = <?php echo CJSON::encode($taskData['timeslots']); ?>;
    var childDataRaw = <?php echo CJSON::encode($taskData['children']); ?>;
    var expired = <?php echo CJSON::encode($taskData['expired']); ?>;
    var childData = _.sortBy(childDataRaw, 'name');
    var childItemTemplate = _.template( $('#child-li-item-template').html() );
    var ptcStatusTemplate = _.template( $('#ptc-plan-setstatus-template').html() );
    var sendStatusTemplate = _.template( $('#send-plan-setstatus-template').html() );
    var TimeslotList = Backbone.Collection.extend({
        comparator: 'time'
    });
    var Timeslots = new TimeslotList;
    currentData.plan = <?php echo CJSON::encode($taskData['plan']);?>;
    var types = '<?php echo $this->branchObj->type ?>';
    $('#ptc-child-list').html('');
    $(function () {
        var itemView = Backbone.View.extend({
            tagName: 'li',
            className: 'list-group-item',
            events: {
                'click .J_remove': 'delItem'
            },
            initialize: function() {
                this.listenTo(this.model, 'change', this.render);
                this.listenTo(this.model, 'destroy', this.remove);
            },
            delItem: function(){
                var model = this.model;
                $.post(
                    '<?php echo $this->createUrl('delTimeslot', array('id'=>$taskData['plan']->id, 'classid'=>$this->selectedClassId,'semester'=>$this->selectedSemester))?>',
                    {time: this.model.get('time')},
                    function(data){
                        if(data.state == 'success'){
                            resultTip({msg: data.message});
                            model.destroy();
                        }
                        else{
                            resultTip({msg: data.message, error: 1});
                        }
                    },
                    'json'
                );
            },
            render: function(){
                this.$el.html( _.template('<span class="glyphicon glyphicon-time"></span><span class="pull-right"><a href="javascript:void(0);"><span class="glyphicon glyphicon-remove J_remove"></span></a></span> <%= time%>', this.model.attributes) );
                return this;
            }
        });
        var optionView = Backbone.View.extend({
            tagName: 'div',
            className: 'checkbox',
            initialize: function() {
                this.listenTo(this.model, 'destroy', this.remove);
            },
            render: function(){
                if(types=='50'){
                    this.$el.html( _.template(' <div class="panel panel-default"><div class="panel-body"><label><input class="J_check" data-yid="J_check_c1" type="checkbox" value="<%= time%>" onclick="add(this)"> <%= time%><span class="classTime"></span></label><div class="childTime"></div></div></div>', this.model.attributes) );
                 }else{
                    this.$el.html( _.template('<label><input class="J_check" data-yid="J_check_c1" type="checkbox" name="timeslot[]" value="<%= time%>"> <%= time%> <span></span></label>', this.model.attributes) );
                 }
                return this;
            }
        });
        var AppView = Backbone.View.extend({
            //el: $("#timeslot-starts"),
            initialize: function () {
                this.listenTo(Timeslots, 'reset', this.addAll);
            },
            addAll: function(){
                Timeslots.each(this.addOne,this);
            },
            addOne: function(model) {
                var item = new itemView({model: model});
                $("#timeslot-starts").append(item.render().el);

                var option = new optionView({model: model});
                if(types=='50'){
                    $('#Dstimeslot-options').append(option.render().el);
                    return
                }
                $('#timeslot-options').append(option.render().el);


            }
        });
        new AppView;
        Timeslots.reset(cdata);

        $('#ptc-month-list li').click(function(){
            $(this).siblings().removeClass('active');
            $(this).addClass('active');
            var _date = $(this).data('month');
            $('#ptcFull-calendar').fullCalendar('gotoDate', _date);
        });

        renderChildList = function(){
            $('#ptc-child-list').html('');
            _.each(childData, function(_data){
                _data.online = childids.indexOf(Number(_data.id)) == -1 ? false : true;
                _data.schoolData = schoolData;
                _data.classid = classid;

                var _item = childItemTemplate(_data);
                var _child = _.first($(_item));
                $('#ptc-child-list').append( $(_child) );
            });
        };
        renderChildList();

        $('#status-wrapper').html( ptcStatusTemplate(currentData.plan) );

        setPlanStatus = function(status, id, obj){
            $(obj).attr('disabled', 'disabled');
            $.ajax({
                type: 'post',
                url: '<?php echo $this->createUrl('//mteaching/semester/setPlanStatus', array('classid'=>$this->selectedClassId));?>',
                dataType: 'json',
                data: {status:status,plan_id:currentData.plan.id}
            }).done(function(data){
                if(data.state == 'success'){
                    currentData.plan.status = data.data.status;
                    $('#status-wrapper').html( ptcStatusTemplate(currentData.plan) );
                    if(currentData.plan.status == 1){
                        // var as = "<div><button class='btn btn-primary pull-right' onclick='allSend()'><?php echo Yii::t('teaching', 'Send Reminder');?></button></div>";
                        // $('#planClass').append(as);
                    }else{
                        $('#planClass button').remove();
                    }
                    resultTip({msg: data.message});
                }else{
                    resultTip({msg: data.message, error: 1});
                }
                $(obj).removeAttr('disabled');
            });
        };
        Date.prototype.format = function() {  
              var s = '';  
              var mouth = (this.getMonth() + 1)>=10?(this.getMonth() + 1):('0'+(this.getMonth() + 1));  
              var day = this.getDate()>=10?this.getDate():('0'+this.getDate());  
              s += this.getFullYear() + '-'; // 获取年份。  
              s += mouth + "-"; // 获取月份。  
              s += day; // 获取日。  
              return (s); // 返回日期。  
        };
        dayClick = function(start,end){
            if(types=='50'){
                if(Timeslots.length<1){
                    alert('<?php echo Yii::t('teaching', 'Please set PTC time slots first.')?>');
                    return;
                }
                $('#Dstimeslotwithday form input[name="timeslot[]"]').attr('checked', false);
                var c=0;
                Dsdays=[];
                for(var i=start['_d'].getTime(); i<end['_d'].getTime(); i+=86400000){
                    Dsdays.push((i/1000));
                    c++;
                }
                if(c == 1){
                    var dateT = start['_d'].getFullYear()+'/'+(start['_d'].getMonth()+1)+'/'+start['_d'].getDate();
                    $('#Dstimeslotwithday .modal-title small').text(dateT);
                    $.ajax({
                        url : '<?php echo $this->createUrl('getDayInfoDS');?>',
                        data: {planid: '<?php echo $taskData['plan']->id?>', target_date: dateT},
                        type: 'post',
                        dataType: 'json'
                    }).done(function(data){
                        $('#Dstimeslot-options input[type="checkbox"]').attr('checked', false).attr('disabled', false);
                        for(var st in data){
                            var obj = $('#Dstimeslot-options input[value="'+st+'"]').attr('checked', true);
                            for(var i=0;i<data[st].length;i++){
                                var add='<div class="childrenNum pull-left" onclick="addone(this,\''+data[st][i].timeslot_s+'\')"><div class="dashed"><span class="glyphicon glyphicon-plus" aria-hidden="true" ></span></div><div class="childrenName"></div></div><div class="clearfix"></div>';
                                if(data[st][i].childid != 0){
                                    obj.attr('disabled', true);
                                    for(var j=0;j<childData.length;j++){
                                        if(childData[j].id==data[st][i].childid){
                                            var htmlname='<div class="childrenNum pull-left"><div><img style="width:50px;" class="img-thumbnail" src="<?php echo Yii::app()->params['OAUploadBaseUrl']?>/childmgt/'+childData[j].photo+'" title='+data[st][i].childName+'></div><div class="childrenName " >'+data[st][i].childName+'</div></div>';
                                            obj.parent().next().append(htmlname)
                                        }
                                    }
                                }
                                else{
                                    var html='<div class="childrenNum pull-left"><div><img style="width:50px;" class="img-thumbnail" src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/childwho.jpg' ?>"><span class="closeimg" aria-hidden="true" onclick="Dsdel(this,\''+data[st][i].timeslot_s+'\','+data[st][i].meet_index+')">&times;</span></div><div class="childrenName name'+data[st][i].meet_index+'" ></div></div>';
                                    obj.parent().next().append(html)
                                }
                            }
                            obj.parent().next().append(add)
                        }

                        $('.classTime').html('（ <?php echo Yii::t('global', 'Duration %d mins'); ?>'.replace(/%d/g,$('#ParentMeetingPlan_default_duration').val())+'）');

                        $('#Dstimeslotwithday').modal({
                            backdrop: false
                        });
                         
                    });
                }else{
                    var datalist=$('#ptcFull-calendar').fullCalendar('clientEvents');
                    var theend = new Date(end['_d'].getTime() - 86400000);
                    var startTime = start['_d'].getTime(); 
                    var dateArrthis=[];
                    for (var k = startTime; k <= theend;) {  
                        dateArrthis.push((new Date(parseInt(k))).format());  
                        k = k + 24 * 60 * 60 * 1000;  
                    }
                    thisData=[];
                    for(var i=0;i<datalist.length;i++){
                        for(var j=0;j<dateArrthis.length;j++){
                            var eventStart = datalist[i].start.format('YYYY-MM-DD');
                            var theDate = dateArrthis[j];
                            if(theDate==eventStart){
                                thisData.push(datalist[i])
                            }
                        }
                    }
                    if(thisData.length>0){
                        resultTip({error: 'warning', msg: '<?php echo Yii::t('global', '禁止连续选择'); ?>'});
                        return
                    }
                    $('#Dstimeslotwithday .modal-title small').text(start['_d'].getFullYear()+'/'+(start['_d'].getMonth()+1)+'/'+start['_d'].getDate()+' - '+theend.getFullYear()+'/'+(theend.getMonth()+1)+'/'+theend.getDate());
                    $('#Dstimeslot-options input[type="checkbox"]').attr('checked', false).attr('disabled', false);
                    $('#Dstimeslotwithday').modal({
                        backdrop: false
                    });
                 }
                return
            }
            if(Timeslots.length<1){
                alert('<?php echo Yii::t('teaching', 'Please set PTC time slots first.')?>');
                return;
            }
            $('#timeslotwithday form input[name="days[]"]').remove();
            $('#timeslotwithday form input[name="timeslot[]"]').attr('checked', false);
            var c=0;
            for(var i=start['_d'].getTime(); i<end['_d'].getTime(); i+=86400000){
                $('#timeslotwithday form').append('<input type="hidden" name="days[]" value="'+(i/1000)+'">');
                c++;
            }
            if(c == 1){
                var dateT = start['_d'].getFullYear()+'/'+(start['_d'].getMonth()+1)+'/'+start['_d'].getDate();
                $('#timeslotwithday .modal-title small').text(dateT);

                $.ajax({
                    url : '<?php echo $this->createUrl('getDayInfo');?>',
                    data: {planid: '<?php echo $taskData['plan']->id?>', target_date: dateT},
                    async: false,
                    dataType: 'json'
                }).done(function(data){
                    $('#timeslot-options input[type="checkbox"]').attr('checked', false).attr('disabled', false).nextAll('span').html('');
                    for(var st in data){
                        var obj = $('#timeslot-options input[value="'+data[st].timeslot_s+'"]').attr('checked', true);
                        if(data[st].childid != 0){
                            obj.attr('disabled', true);
                            obj.next('span').html(data[st].childName);
                        }
                        else{
                            obj.attr('disabled', false);
                            obj.next('span').html('');
                        }
                    }
                    $('#timeslotwithday').modal({
                        backdrop: false
                    });
                });
            }
            else{
                var theend = new Date(end['_d'].getTime() - 86400000);
                $('#timeslotwithday .modal-title small').text(start['_d'].getFullYear()+'/'+(start['_d'].getMonth()+1)+'/'+start['_d'].getDate()+' - '+theend.getFullYear()+'/'+(theend.getMonth()+1)+'/'+theend.getDate());
                $('#timeslot-options input[type="checkbox"]').attr('checked', false).attr('disabled', false).nextAll('span').html('');
                $('#timeslotwithday').modal({
                    backdrop: false
                });
            }
            head.Util.checkAll( $('#timeslotwithday') );
        };
        add = function(obj){
            var checked=$(obj).parent().find('input');
            var timeslotsTime=$(obj).val();
            if(checked.is(':checked')){
                $.ajax({
                    url : '<?php echo $this->createUrl('openTimeslot');?>',
                    data: {planid: '<?php echo $taskData['plan']->id?>', days: Dsdays,'timeslot':timeslotsTime},
                    type: 'post',
                    dataType: 'json',
                    success:function(data){
                        if(data.state='success'){
                            var add='<div class="childrenNum pull-left" onclick="addone(this,\''+timeslotsTime+'\')"><div class="dashed"><span class="glyphicon glyphicon-plus" aria-hidden="true" ></span></div><div class="childrenName"></div></div><div class="clearfix"></div>';
                            var html='';
                            for(var i=0;i<data.data.length;i++){
                                html+='<div class="childrenNum  pull-left"><div><img style="width:50px;" class="img-thumbnail" src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/childwho.jpg' ?>"><span class="closeimg" aria-hidden="true" onclick="Dsdel(this,\''+timeslotsTime+'\','+data.data[i]+')">&times;</span></div><div class="childrenName"></div></div>'
                            }
                            $(obj).parent().next().append(html);
                            $(obj).parent().next().append(add)
                        }
                    }
                })
            }else{
                 $.ajax({
                    url : '<?php echo $this->createUrl('cancelTimeslot');?>',
                    data: {planid: '<?php echo $taskData['plan']->id?>', days: Dsdays,'timeslot':timeslotsTime},
                    type: 'post',
                    dataType: 'json',
                    success:function(data){
                       if(data.state=='success'){
                            $(obj).parent().next().html('')
                       }
                    }
                })
            }
        };
        addone = function(obj,timeslotsTime){
            $.ajax({
                url : '<?php echo $this->createUrl('addNum');?>',
                data: {planid: '<?php echo $taskData['plan']->id?>', days: Dsdays,'timeslot':timeslotsTime},
                type: 'post',
                dataType: 'json',
                success:function(data){
                    if(data.state=='success'){
                        var html='<div  class="childrenNum pull-left"><div><img style="width:50px;" class="img-thumbnail" src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/childwho.jpg' ?>"><span class="closeimg" aria-hidden="true" onclick="Dsdel(this,\''+timeslotsTime+'\','+data.data+')">&times;</span></div><div class="childrenName"></div></div>';
                        $(obj).before(html)
                    }
                }
            })
        };
        Dsdel=function(obj,timeslotsTime,index){
            $.ajax({
                url : '<?php echo $this->createUrl('delNum');?>',
                data: {planid: '<?php echo $taskData['plan']->id?>', days: Dsdays,'timeslot':timeslotsTime,index:index},
                type: 'post',
                dataType: 'json',
                success:function(data){
                    if(data.state=='success'){
                        $(obj).parent().parent().remove()
                    }
                }
            })
        };
        closeds=function(){
            if(types=='50'){
                location.reload();
            }
        };
        timeSlotClick = function(event, element){
            var html = '';
            var startObj = event['start']['_i'].split('T');
            var starttime = startObj[1].substring(0,5);
            var endtime = event['end']['_i'].split('T')[1];
            $('#timeslotwithitem .modal-title').text(startObj[0]+' '+starttime+' - '+endtime);
            $('#unikey').val(event.id);
            $('#starttime').text(starttime);

            $.getJSON(
                '<?php echo $this->createUrl('getTimeslotInfo');?>',
                {unikey: event.id},
                function(data){
                    if(data.childid != 0){
                        $('#delchild').attr('href', '<?php echo $this->createUrl('clearChild', array('classid'=>$this->selectedClassId));?>&unikey='+event.id).show();
                        $('#un-child-list').html( _.template($('#child-radio-item-template').html(), {id: data.childid, name: childDataRaw[data.childid].name, photo: childDataRaw[data.childid].photo}) );
                    }
                    else{
                        _.each(childData, function(_data){
                            if(childids.indexOf(Number(_data.id)) == -1){
                                var _item = _.template($('#child-radio-item-template').html(), _data);
                                html += _item;
                            }
                        });
                        $('#timeslotwithitem #un-child-list').html( html );

                        $('#delchild').hide();
                    }

                    $('#timeslotwithitem input[value='+data.childid+']').attr('checked', true);
                    $('#endtime_hour').val(data.endtimeh);
                    $('#endtime_minute').val(data.endtimem);
                    $('#memo').val(data.memo);

                    $('#timeslotwithitem').modal({
                        backdrop: false
                    });
                }
            );
        };

        saveDuration = function()
        {
            if(canSubmit){
                canSubmit = false;
                $.post(
                    '<?php echo $this->createUrl('saveDuration', array('classid'=>$this->selectedClassId, 'semester'=>$this->selectedSemester))?>',
                    {duration: $('#ParentMeetingPlan_default_duration').val()},
                    function(data){
                        canSubmit = true;
                        if(data.state == 'success'){
                            resultTip({msg: data.message});
                        }
                        else{
                            resultTip({msg: data.message, error: 1});
                        }
                    },
                    'json'
                );
            }
        };

        saveTimeslot = function()
        {
            var th_obj = $('#timeslot_starts_hour');
            var tm_obj = $('#timeslot_starts_minute');
            if(th_obj.val() == ''){
                return false;
            }
            if(tm_obj.val() == ''){
                return false;
            }
            if(canSubmit){
                canSubmit = false;
                $.post(
                    '<?php echo $this->createUrl('saveTimeslot', array('id'=>$taskData['plan']->id, 'classid'=>$this->selectedClassId,'semester'=>$this->selectedSemester))?>',
                    {hour: th_obj.val(), minute: tm_obj.val()},
                    function(data){
                        canSubmit = true;
                        if(data.state == 'success'){
                            th_obj.val('');
                            tm_obj.val('');
                            Timeslots.reset(data.data);
                            //Timeslots.create(data.data);
                            resultTip({msg: data.message});
                        }
                        else{
                            resultTip({msg: data.message, error: 1});
                        }
                    },
                    'json'
                );
            }
        };

        // 选孩子回调函数
        cbChooseChild = function(data)
        {
            location.reload();
            var event = $("#ptcFull-calendar").fullCalendar('clientEvents', data.unikey)[0];
            event.title = data.childName;
            event.color = '#3a87ad';
            $("#ptcFull-calendar").fullCalendar('updateEvent', event);
            childids.push(Number(data.childid));
            select_childid[data.childid] = data.childTimeslot;
            memos[data.childid] = data.memo;
            renderChildList();
            $('#timeslotwithitem').modal('hide');
            location.reload();
        };

        // 选时间点回调函数
        cbChooseTimeslot = function(data)
        {
            var delTimeslot = data.delTimeslot;
            var newTimeslot = data.newTimeslot;
            for(var i in delTimeslot){
                $("#ptcFull-calendar").fullCalendar('removeEvents', delTimeslot[i]);
            }
            for(var i in newTimeslot){
                var eventData = {
                    id: newTimeslot[i].id,
                    title: '',
                    start: newTimeslot[i].start,
                    end: newTimeslot[i].end,
                    color: '#257e4a'
                };
                $("#ptcFull-calendar").fullCalendar('renderEvent', eventData, true);
            }
            $('#timeslotwithday').modal('hide');
        };

        // 删除预约成功的孩子回调函数
        cbClearChild = function(data)
        {
            var event = $("#ptcFull-calendar").fullCalendar('clientEvents', data.unikey)[0];
            event.title = '';
            event.color = '#257e4a';
            $("#ptcFull-calendar").fullCalendar('updateEvent', event);
            delete childids[childids.indexOf(Number(data.childid))];
            renderChildList();
            $('#timeslotwithitem').modal('hide');
        };

        changeChild = function(_this)
        {
            var childid = $(_this).val();
            $('#memo').val(memos[childid]);
        };

        childPopover = function(_this)
        {
            var childid = $(_this).attr('childid');
            $(_this).attr('title', select_childid[childid]);
            $(_this).attr('data-content', memos[childid]==''?'<?php echo Yii::t('teaching', 'N/A')?>':nl2br(_.escape(memos[childid])));
            $(_this).popover('toggle');
        }

    });
    function allSend(){
       var booked=[];//已预约
       var unbound=[];//未绑定微信
       var limit=[];//24小时
       var sendlist=[];//可发送
        for(var i=0;i<childData.length;i++){
            if(childData[i].online){
                booked.push(childData[i])
            }
            if(!childData[i].online){
                if(childData[i].bindingStatus=='0'){
                    unbound.push(childData[i])
                }else{
                    if(expired[childData[i].id]){
                        limit.push(childData[i])
                    }else{
                        sendlist.push(childData[i])
                    }
                }
            }
        }
        var datas={};
        datas.booked=booked;
        datas.unbound=unbound;
        datas.limit=limit;
        datas.sendlist=sendlist;
        $('.sendcontent').html( sendStatusTemplate(datas) );
        $('#sendData').modal('show');
         head.Util.checkAll($('#sendData'));
    }

    closeindex=0;
    read='';
    function send(){
        read='';
        var chaildfiler=[];
        $.each($('input[name="send"]:checked'),function(){
            chaildfiler.push($(this).val());
        });
        if(chaildfiler.length==''){
             resultTip({msg:'<?php echo Yii::t('teaching', 'Please select')?>', error: 1});
        }else{
            sendAjaxS(chaildfiler);
        }
    }

    function sendAjaxS(id) {
        var index = 0;
        var result = [];
        var childindex='';
        var sendsucc=[];
        var successData='';
        sendAjax(index);
        function sendAjax(index) {
            
            closeindex=1;
            $('.sendbtn').attr("disabled", true);
            $('#text'+id[index]).css('visibility','visible');
            $('#text'+id[index]).html('<?php echo Yii::t('teaching', 'Sending...')?>');
            if(childindex!=''){
                var html='';
                for(var i=0;i<childData.length;i++){
                  if(childindex==childData[i].id){
                    $('#tr'+childindex).fadeToggle(1000);
                    html+='<tr id=add'+childData[i].id+' style="display:none"> <td width="40" class="pr0"> <img class="media-object child-face img-thumbnail pull-left" src="<?php echo Yii::app()->params['OAUploadBaseUrl']?>/childmgt/'+childData[i].photo+'" title='+childData[i].name+'></td><td width:100"><span>'+childData[i].name+'</span></td><td><p class="mt5">';
                        for(key in successData.data[childindex]){
                            if(successData.data[childindex][key].name!=''){
                                html+=' <img class="img-circle imgbig" src='+successData.data[childindex][key].headimgurl+' alt='+successData.data[childindex][key].name+' title='+successData.data[childindex][key].name+'>'
                            }
                        }
                    html+='</p></td><td width="60"><?php echo Yii::t('teaching', 'Sent')?></td></tr>'
                    }
                }
                $('.addchild').prepend(html);
                $('#add'+childindex).fadeIn(1000)
            }
            if(index >= id.length) {
                doSomething(id,result);
                return;
            }
            if(read=='close'){
                colsesend(sendsucc);
                return;
            }
            $.ajax({
                url: '<?php echo $this->createUrl("SendMessage")?>',
                type: "POST",
                async: true,
                dataType: 'json',
                data: {
                    'planid': '<?php echo $taskData['plan']->id?>',
                    'childid': id[index],
                    'classid':classid,
                    'semester':<?php echo $this->selectedSemester ?>
                },
                success: function(data) {
                    if(data.state == 'success') {
                        result.push(data);
                        sendsucc.push(id[index]);
                        $('#check'+id[index]).find('div').remove();
                        $('#text'+id[index]).html(' <span class="glyphicon glyphicon-ok" aria-hidden="true"></span> <?php echo Yii::t('teaching', 'Sent')?>');
                        childindex=id[index];
                        successData=data;
                        setTimeout(function(){
                            index++;
                            sendAjax(index);
                        }, 1000);
                    } else {
                        $('#'+id[index]).html(data.message);
                        closeindex=0;
                        $('.sendbtn').attr("disabled", true);
                        resultTip({msg: data.message, error: 1});
                    }
                },
                error: function() {
                    alert("请求错误")
                }
            });
         }
    }
    function doSomething(id,data) {
        closeindex=0;
        $('.sendbtn').attr("disabled", false);
        for(var i=0;i<id.length;i++){
             expired[id[i]]=id[i]
        }
    }
    function colsesend(sendsucc){
        closeindex=0;
        $('.sendbtn').attr("disabled", false);
        for(var i=0;i<sendsucc.length;i++){
             expired[sendsucc[i]]=sendsucc[i]
        }
    }
    function closesend(){
        if(closeindex==0){
            $('#sendData').modal('hide');
        }else{
            var con;
            con=confirm("<?php echo Yii::t('teaching', 'Sending task is not completed, are you sure to stop it?')?>"); //在页面上弹出对话框
            if(con){
                $('#sendData').modal('hide');
                read='close'
            }
            
        }
    }
</script>