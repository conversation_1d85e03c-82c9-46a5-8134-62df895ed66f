<?php

/**
 * This is the model class for table "ivy_asa_course_group_agreement".
 *
 * The followings are the available columns in table 'ivy_asa_course_group_agreement':
 * @property integer $groupid
 * @property integer $status
 * @property string $content_cn
 * @property string $content_en
 * @property integer $updated
 * @property integer $updated_by
 */
class AsaCourseGroupAgreement extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_asa_course_group_agreement';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('groupid, status, content_cn, content_en, updated, updated_by', 'required'),
			array('groupid, status, updated, updated_by', 'numerical', 'integerOnly'=>true),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('groupid, status, content_cn, content_en, updated, updated_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'groupid' => 'Groupid',
			'status' => Yii::t('asainvoice', 'Status'),
			'content_cn' => Yii::t('asainvoice', '中文介绍'),
			'content_en' => Yii::t('asainvoice', '英文介绍'),
			'updated' => 'Updated',
			'updated_by' => 'Updated By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('groupid',$this->groupid);
		$criteria->compare('status',$this->status);
		$criteria->compare('content_cn',$this->content_cn,true);
		$criteria->compare('content_en',$this->content_en,true);
		$criteria->compare('updated',$this->updated);
		$criteria->compare('updated_by',$this->updated_by);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AsaCourseGroupAgreement the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public function getTitle()
    {
        switch (Yii::app()->language) {
            case "zh_cn":
                return  $this->content_cn ;
                break;
            case "en_us":
                return  $this->content_en;
                break;
        }
    }
}
