<div>
    <div>
        <div class='ml8 pb24 flex1 width100'>
            <div class='font14 color3 fontBold'>财务</div>
            <div class='mt16 form-horizontal font14'>
                <div class='flex flexStart mb20'>
                    <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Refund');?></div>
                    <div class='flex1 color3 amount'> </div>
                </div>
                <!-- <div class='flex mb20 flexStart'>
                    <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Comment');?></div>
                    <div class='flex1 color3'>无</div>
                </div> -->
            </div>
        </div>
        <div class='ml8 flex1 width100 admission_confirm'>
            <div class='font14 color3 fontBold'><?php echo Yii::t('withdrawal','Admissions confirmation');?></div>
            <div class='mt16 form-horizontal font14'>
                <div class='flex flexStart mb20'>
                    <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Final student status');?></div>
                    <div class='flex1 color3 student_status' > </div>
                </div>
                <div class='flex mb20 flexStart'>
                    <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Final withdrawal date');?></div>
                    <div class='flex1 color3 withdrawal_date'></div>
                </div>
                <div class='flex mb20 flexStart'>
                    <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Ultimate withdrawal reason');?></div>
                    <div class='flex1'>
                        <div class='color3 withdrawal_reason'></div>
                        <div class='color6 mt4 withdrawal_reason_memo'></div>
                    </div>
                </div>
                <div class='flex mb20 flexStart'>
                    <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Comment');?></div>
                    <div class='flex1 color3 memo'></div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
     lib()
     function lib(){
        let forms=childDetails.forms[0]
        var totalEnd=0
        let amountTable = forms.return.filter((i) => i.key != 'enrollment' && i.key != 'insurance')
        for(let k=0;k<amountTable.length;k++){
            totalEnd+=  Number(amountTable[k].data.totalAmount)
        }
        $('.amount').html(formatAmount(totalEnd))
        if(forms.admission_confirm[0]){
            if(forms.admission_confirm[0].data.student_status=='1'){
                $('.student_status').text('已更新为退学')
            }else{
                $('.student_status').text('')
            }
            let indexData=container.indexDataList

            $('.withdrawal_date').text(forms.admission_confirm[0].data.withdrawal_date)
            $('.withdrawal_reason').text(indexData.reasonList[forms.admission_confirm[0].data.withdrawal_reason])
            if(forms.admission_confirm[0].data.withdrawal_reason_memo!=null){
                $('.withdrawal_reason_memo').text('<?php echo Yii::t('withdrawal','Comment');?>：'+forms.admission_confirm[0].data.withdrawal_reason_memo)
            }
            if(forms.admission_confirm[0].data.memo!=null){
                $('.memo').text(forms.admission_confirm[0].data.memo)
            }
        }else{
            $('.admission_confirm').hide()
        }
    }
</script>
<style>
    .rejectInput{
        display:none
    }
    .pushWechat{
        background: #F0F5FB;
        border-radius: 4px;
        padding:24px;
    }
    .financeStep{
        width: 28px;
        height: 28px;
        background: #E9F0F9;
        display: inline-block;
        font-size: 18px;
        border-radius: 50%;
        text-align: center;
        line-height: 28px;
        color: #4D88D2;
    }
    .financeStep1{
        width: 28px;
        height: 28px;
        background: #4D88D2;
        display: inline-block;
        font-size: 14px;
        border-radius: 50%;
        text-align: center;
        line-height: 28px;
        color: #fff;
    }
    .financeLine{
        position: absolute;
        width: 1px;
        height: 100%;
        left: 13px;
        top: 28px;
        background: #4D88D2;
    }
    .resubmit{
        background: #F0F5FB;
        border-radius: 2px;
        color: #4D88D2;
        padding:4px 6px;
        font-size:12px;
        margin-left:16px;
    }
</style>