$(function(){
    var $conformAccount = $('#conformAccount');
    var $tooltips = $('.js_tooltips');

    if ($tooltips.length) {
        setTimeout(function () {
            $tooltips.hide();
        }, 2000);
    }

    $('#dialogs').on('click', '.weui-dialog__btn_default', function(){
        $(this).parents('.js_dialog').hide();
    });

    $('#dialogs').on('click', '.weui-dialog__btn_primary', function(){
        var uid = $(this).data('uid');
        var link = $('#assign_' + uid).data('link');
        var phone = $(this).data('phone');

        $.post(link, {uid: uid, phone: phone}, function(data){
            if (data.state == 'success') {
                window.location.href = data.message;
            }
        }, 'json');
    });

    $('.page').on('click', '.assign-user', function(){
        var uid = $(this).data('uid');
        $('#conformAccount .weui-dialog__btn_primary').data('uid', uid);
        $conformAccount.show();
    });

    $('#login-form').on('click', '.weui-vcode-btn', function(){
        var link = $(this).data('link');
        var phoneObj = $('#PhoneBindForm_phone');
        var phoneVal = phoneObj.val();
        if (phoneVal != '') {
            $.post(link, {'SmsVcode[phone]': phoneVal}, function(data){
                if(data.state !== 'success'){
                    if (!$('.js_tooltips').length) {
                        $('.container').prepend("<div class='weui-toptips weui-toptips_warn js_tooltips' style='display: none;'></div>")
                    }
                    if($('.js_tooltips').css('display') == 'none'){
                        $('.js_tooltips').text(data.message).show();
                        setTimeout(function () {
                            $('.js_tooltips').hide();
                        }, 2000);
                    }
                }else {
                    //计时器
                    var countdown = 60;
                    $('.weui-vcode-btn > .vcodeText').text(sendAgain);
                    function settime(val) {
                        if (countdown == 0) {
                            $('.weui-vcode-btn').removeAttr('disabled').css('color','#3cc51f');
                            $('.weui-vcode-btn > .countDown').empty();
                            countdown = 60;
                            return;
                        } else {
                            $('.weui-vcode-btn').attr('disabled','disabled').css('color','gray');
                            $('.weui-vcode-btn > .countDown').text(countdown + 's');
                            countdown--;
                        }
                        setTimeout(function () {
                            settime(val)
                        }, 1000)
                    }
                    settime();
                }
            }, 'json');
        }
        else {
            phoneObj.focus();
        }
    });



    var winH = $(window).height();
    var categorySpace = 10;

    $('.js_item').on('click', function(){
        var id = $(this).data('id');
        window.pageManager.go(id);
    });
    $('.js_category').on('click', function(){
        $('li').removeClass("js_show");
        var $this = $(this),
            $inner = $this.next('.js_categoryInner'),
            $page = $this.parents('.page'),
            $parent = $(this).parent('li');
        var innerH = $inner.data('height');

        if(!innerH){
            $inner.css('height', 'auto');
            innerH = $inner.height();
            $inner.removeAttr('style');
            $inner.data('height', innerH);
        }

        if($parent.hasClass('js_show')){
            $parent.removeClass('js_show');
        }else{
            $parent.siblings().removeClass('js_show');

            $parent.addClass('js_show');
            if(this.offsetTop + this.offsetHeight + innerH > $page.scrollTop() + winH){
                var scrollTop = this.offsetTop + this.offsetHeight + innerH - winH + categorySpace;

                if(scrollTop > this.offsetTop){
                    scrollTop = this.offsetTop - categorySpace;
                }

                $page.scrollTop(scrollTop);
            }
        }
    });

});