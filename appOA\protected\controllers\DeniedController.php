<?php

class DeniedController extends ProtectedController
{
    public function init()
    {
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'main';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
    }

    public function actionIndex()
    {
        $this->render('index');
    }

    public function actionError($flag=""){
        $this->render('error', array('flag'=>$flag));
    }
}