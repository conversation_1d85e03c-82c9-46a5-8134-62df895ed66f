<div class="table_list">
	<table width="100%" style="table-layout:fixed;">
        <thead>
            <tr>
                <td>商品</td>
                <td>积分</td>
                <td>数量</td>
                <td>孩子</td>
            </tr>
        </thead>
		<tbody>
			<?php foreach ($models as $model):?>
			<tr>
				<td><?php echo $model->Product->getContent()?></td>
                <td><?php echo $model->credits?></td>
                <td><?php echo $model->quantity?></td>
                <td><?php echo $model->ChildProfile->getChildName()?></td>
			</tr>
			<?php endforeach; ?>
		</tbody>

    </table>
</div>

<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'pack-form',
	'enableAjaxValidation'=>false,
	'htmlOptions'=>array('class'=>'J_ajaxForm'),
));
?>
<div class="pop_cont pop_table" style="height:auto;">
    <table width="100%">
        <colgroup>
            <col class="th" />
            <col />
        </colgroup>
        <tbody>
            <tr>
                <th><?php echo $form->labelEx($omodel,'shipinfo')?></th>
                <td><?php echo $form->textField($omodel, 'shipinfo', array('class'=>'input length_5'))?></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($smodel,'memo')?><?php echo CHtml::hiddenField('op', 'send')?></th>
                <td><?php echo $form->textArea($smodel, 'memo', array('class'=>'length_5'))?></td>
            </tr>
        </tbody>
    </table>
</div>
<div class="pop_bottom">
    <button type="button" class="btn fr" id="J_dialog_close"><?php echo Yii::t('global','Cancel');?></button>
    <button class="btn fr btn_submit mr10 J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
</div>
<?php $this->endWidget(); ?>