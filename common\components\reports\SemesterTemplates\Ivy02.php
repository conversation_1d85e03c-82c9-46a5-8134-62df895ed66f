<?php

/**
 * IVY IA 的学期报告结构模版
 */

return array(

    //封面：孩子基本信息
    array(
        'sign' => 'FrontCover',
        'title' => 'Basic Information',
        'items' => array(
            array(
                'sign' => 'FrontCoverPhoto',
                'title' => 'Front Cover',
                'content' => array('photo')
            ),
        )
    ),

    array(
        'sign' => 'PreSummary',
        'title' => 'General Summary',
        'items' => array(
            array(
                'sign' => 'StudentReflection',
                'title' => 'Student Reflection',
                'content' => 'photo+text',
            ),
            array(
                'sign' => 'AsLearner',
                'title' => 'The Child as a Learner',
                'content' => 'text',
            )
        )
    ),

    //学习领域
    array(
        'sign' => 'LearningDomains',
        'title' => 'Section 1: Learning Domains',
        'ops' => array(
            'add'=>'LearningDomainItem',
//            'sort'=>'LearningDomainItem'
        ),
        'items' => array(
            array(
                'sign' => 'LDGrid',
                'title' => 'Photo Grid',
                'ops' => 'edit',
                'content' => array('photos')
            ),
        )
    ),

    //教学项目
    array(
        'sign' => 'LearningProjects',
        'title' => 'Section 2: Learning Projects',
        'ops' => array(
            'add'=>'LearningProjectItem',
//            'sort'=>'LearningProjectItem',
        ),
        'items' => array(
            array(
                'sign' => 'LPGrid',
                'title' => 'Photo Grid',
                'ops' => 'edit',
                'content' => array('photos')
            ),
        )
    ),

    //文字，教师评语
    array(
        'sign' => 'PostSummary',
        'title' => 'Teacher Reflection',
        'items' => array(
            array(
                'sign' => 'TRef',
                'title' => 'Teacher Reflection',
                'content' => 'text'
            )
        )
    ),

    //封底，一张幼儿与教师的合影
    array(
        'sign' => 'BackCover',
        'title' => 'Back cover',
        'items' => array(
            array(
                'sign' => 'BackCoverPhoto',
                'title' => 'Back Cover Photo',
                'content' => 'photo'
            )
        )
    ),
);
