<?php

/**
 *
 */

class PolicyCommand extends CConsoleCommand {

	public function init(){
		Yii::setPathOfAlias('common', dirname(__FILE__) . '/../../../common');
		Yii::import('common.models.*');
	}
	public $batchNum = 200;

	/**
	 * 初始化孩子基本表中的状态信息，并去掉该表中新孩子明年班级id
	 * @return	Object		Description
	 */
	public function actionUpdateChildBasicStatus(){
		//1. 把当前班级状态表的孩子状态直接复制到基本表中
		$sqlText = 'update ivy_child_profile_basic B, ivy_child_school_class C  set B.status = C.stat, B.classid = C.classid where B.childid = C.childid';
		$dbCommand = Yii::app()->db->createCommand()->setText($sqlText)->execute();
		
		//2. 得到未来学年（2013）已经分班的班级id
		$sqlText2 = 'SELECT distinct(R.classid) as c1, C.startyear FROM `ivy_child_reserve` R left join ivy_calendar_yearly C on R.calendar = C.yid where C.startyear=2013';
		$dbCommand = Yii::app()->db->createCommand()->setText($sqlText2);
		$cids = $dbCommand->queryColumn(array('c1'));
		
		//3. 若孩子基本中班级id等于2中得到的，将孩子状态和班级id都置为0
		if(count($cids)){
			$crit = new CDbCriteria;
			$crit->compare('classid', $cids);
			ChildProfileBasic::model()->updateAll(array('classid'=>0, 'status'=>0), $crit);
		}
		
		//把孩子基本表中的状态为10的全部变更为20; 
		ChildProfileBasic::model()->updateAll(array('status'=>20), 'status=:status', array(':status'=>10));
		
		//把孩子当前班级表中的状态为10的全部变更为20; 
		ChildClassLink::model()->updateAll(array('stat'=>20), 'stat=:status', array(':status'=>10));
		
		//其他表中存在10状态的情况，暂不做处理，包括表：学习历史表、未来表
		
		echo "OK..."."\r\n";
	}
	
	public function actionTest(){
		Yii::import('application.components.policy.PolicyApi');
		Yii::import('common.models.invoice.*');
		$test = PolicyApi::getDepositAmount(1219,0,true);
		print_r($test);
	}
	
}