i18n.set({lang:yiiLang, path:theme+'/js/'});
var IVY = IVY || {};
IVY.Page = IVY.Page || {};
IVY.Page.winBlur = 0;
IVY.Page.PV = function () {
$.track.trackInfo.userStatic.requestId = null;
    vjEventTrack("");
    jQuery.track.trackPage();
}

IVY.Page.slide = {
    settings: {
        curr: 0,
        timeout: 5 * 1000,
        adChange: 10,
        t: null,
        autoPlay: false,
        noPlay: false,
        n: 4,
        imgLoadTimeOut1: null,
        imgLoadTimeOut2: null,
        timeCountOut: null,
        cellWidth: 100 + 3,
        timeCount: 0,
        cellsWidth: function () { return this.cellWidth * $("#photolist li").length },
        max: 0,
        _tmp: null,
        resize: false
    },
    init: function () {
         var p = this;
		p.settings.max = $("#photolist li").length;
		var nURL = parseInt(location.hash.substring(2));
		if(nURL>=0 && nURL<=this.settings.max){
			this.settings.curr = nURL - 1;
		}
		else{
			location.hash = '#p1';
		}
        // p.mask();
        var description = $("section .description div.none").text();
//		var subDescription = description.length >  130 ? description.substr(0, 130) + "... " : description;
		var subDescription = description;
        $("html,body").blur(function () { IVY.Page.winBlur = 1; });
        $("html,body").focus(function () { IVY.Page.winBlur = 0; });
        $("section .description>p .txt").html(subDescription);
        $(".num>.all").html($("#photolist li").length);
        $(".num>.curr").html(p.settings.curr + 1);
        $("#photolist .mask li").append("<s></s>");
//        $("section .description p .more").click(function () {
//            if ($(this).hasClass("up")) {
//                $(this).removeClass("up");
//                $("section .description>p .txt").html(subDescription);
//                $("section .description").css("height", "75px");
//                $(this).html("查看更多<s></s>");
//            }
//            else {
//                $(this).addClass("up");
//                $("section .description>p .txt").html(description);
//                $("section .description").css("height", "auto");
//                $(this).html("缩回<s></s>");
//            }
//        });
        $("section .tool li,#photolist .btn_l, #photolist .btn_r").hover(
       function () {
           if ($.browser.msie && parseInt($.browser.version) < 7) return;
           $(this).addClass("on");
       },
       function () {
           if ($.browser.msie && parseInt($.browser.version) < 7) return;
           $(this).removeClass("on");
       });
        p.touch();
        $(".tool .prev").click(function () {            
			if (p.settings.noPlay) {
                return;
            }
            p.stop();
            p.setIntervalPlay();
            p.prev();
        });
        $(".tool .next").click(function () {
			if (p.settings.noPlay) return;
            p.stop();
            p.setIntervalPlay();
            p.next();
        });
        $(".tool .play").click(function () {
            if (p.settings.noPlay) return;
            if ($(this).hasClass("stop"))
            { p.play(); }
            else
            { p.stop(); }
        });
		$(".tool .play").click();
        $(".tool .download").click(function () {
            if (p.settings.noPlay) return;
            window.open($("#mainimg").attr("src"));
        });
        $(".tool .list").click(function () {
            if ($(this).hasClass("grey")) return;
            if ($(this).hasClass("s")) {
                p.settings.noPlay = false;
                $(".tool .prev,.tool .play,.tool .next,.tool .download").removeClass("grey");
                $(this).removeClass("s");                
				$(".summary,#picbox").show();
                $("#photolist").removeClass("list");
                $("#photolist .mask ul").css("top", 0);
				
				if($(".summary .description").text().length > 0 ){
				}else{
					p.hideImgSummary();
				}
                $("#photolist .listnav").html('');
            }
            else {
                p.settings.noPlay = true;
                $(".tool .prev,.tool .play,.tool .next,.tool .download").addClass("grey");
                $(this).addClass("s");
                $(".summary,#picbox").hide();
                $("#photolist").addClass("list");
                $("#photolist .mask ul").css("left", 0);
                p.list();
            }
        });
        $(".summary .icon").click(function () {
            if ($(this).hasClass("off")) {
                p.showImgSummary(true);
            }
            else {
               p.hideImgSummary(true);
            }
        });
        $("#more .nextplay span").click(function () {
            location.href = $("#more .nextplay a").attr("href");
            return false;
        });
        $("#more .replay span").click(function () {
            p.settings.noPlay = false;
            //$(".summary,#picbox,#photolist").show();
            $(".summary,#picbox").show();
            $("#more").hide();
            clearInterval(p.settings.timeCountOut);
            $("#photolist .mask li").eq(0).click();
        });
        $("#more .close span").click(function () {
            window.close();
        });
        $("#photolist .mask li").click(function () {
            var i = $(this).index();
            var isList = false;
            if ($("#photolist").hasClass("list")) {
                isList = true;
            }
            p.settings.noPlay = false;
            $(".tool li").removeClass("grey");
            $(".tool .list").removeClass("s");
            $(".summary,#picbox").show();
            $("#photolist").removeClass("list");
            $("#photolist .mask ul").css("top", 0);
            $("#photolist .listnav").html('');
            p.goto(i);
            if (isList) {
                var min = -1 * p.settings.cellWidth * p.settings.n;
                var max = -1 * p.settings.cellsWidth() + 825;
                var left = -$(this).offset().left + $("#photolist").offset().left + 77;
                if (left >= max) {
                    $("#photolist .mask ul").animate({ left: left }, "slow");
                }
                else {
                    $("#photolist .mask ul").animate({ left: max}, "slow");
                    $("#photolist .btn_r").addClass("grey");
                }
            }
            return false;
        });
		var regExp_PageURL = /\d+_(\d+).shtml/ig;		
		var regResult = regExp_PageURL.exec(location.href);
		if(regResult){
			var i = parseInt(regResult[1]) - 1;
			$("#photolist .mask li").eq(i).click();
		}		
        $("#photolist .btn_l").click(function () {
            p.stop();
            p.balanceL();
        });
        $("#photolist .btn_r").click(function () {
            p.stop();
            p.balanceR();
        });
        $("#picbox .p_left").click(function () {
            $(".tool .prev").click();
        });
        $("#picbox .p_right").click(function () {
            $(".tool .next").click();
        });
		if ($(".summary .description").text().length > 0) {
			p.showImgSummary();
		}
		else {
		   p.hideImgSummary();
		}
        p.setIntervalPlay();

    },
    play: function () {
        $(".tool .play").removeClass("stop");
        this.settings.autoPlay = true;
        this.setIntervalPlay();
        clearInterval(this.settings._tmp);
    },
    stop: function () {
        if ($(".tool .play").hasClass("stop"))return;
        $(".tool .play").addClass("stop");
        this.settings.autoPlay = false;
        this.setIntervalPlay();
        this.settings._tmp = setInterval(function(){$('.stop .stoptip').fadeOut().fadeIn();}, 1000);
    },
    setIntervalPlay: function () {
        var p = this;
        clearInterval(p.settings.t);
        if (p.settings.autoPlay) {
            p.settings.t = setInterval(function () { p.next(); }, p.settings.timeout);
        }
    },
	showImgSummary : function(hasAnimate){
		$(".summary .icon").removeClass("off");
		$(".summary .description").show();
		if(typeof(hasAnimate) == "undefined"){
			hasAnimate = false;
		}
		if(hasAnimate){
			$(".summary .description").animate({ width: "710px" }, "slow");
		}else{
			$(".summary .description").css("width","710px");
		}
               $(".summary .icon").html("<s></s>");
	},
	hideImgSummary : function(hasAnimate){
		$(".summary .icon").addClass("off");
		if(typeof(hasAnimate) == "undefined"){
			hasAnimate = false;
		}
		if(hasAnimate){
			$(".summary .description").animate({ width: "0px" }, "slow", function () { $(".summary .description").hide(); });
		}else{
			$(".summary .description").hide();
		}
                $(".summary .icon").html("<s></s>");
	},	
    goto: function (i) {
        $("#more").hide();
        IVY.Page.PV();
        var p = this;
		if (!IVY.Page.winBlur && (i + 1) % p.settings.adChange == 0) {
            $(".slide_ad iframe").each(function () {
                $(this).attr("src", $(this).attr("src"));
            });
        }
        if (p.settings.noPlay) return;
        var $e = $("#photolist li").eq(i);
        $("#photolist li").removeClass("on");
        $e.addClass("on");
        p.settings.curr = i;
		location.hash='#p'+parseInt(this.settings.curr+1);//
        var src = $e.find("img").attr("bigpic");
        var desc = $e.find("img").attr("desc") || "";
        p.settings.noPlay = true;
        $("#imgLoading em").html(i18n._('Loading...'));
        $("#imgLoading").show();
        $(".num>.curr").html(i + 1);
        $(".summary .description").html("<p>" + desc + "</p>");
        $("<img />").attr("src", src).load(function () {
            p.settings.noPlay = false;
            $("#mainimg").attr("src", src);
            p.setIntervalPlay();
            $("#imgLoading").hide();
            clearTimeout(p.settings.imgLoadTimeOut1);
            clearTimeout(p.settings.imgLoadTimeOut2);
            if (p.settings.resize == true){
                p.photoResize();
            }
        });
        var curr_index = ($e.offset().left - $("#photolist").offset().left - 77) / p.settings.cellWidth;
        if (curr_index >= 4) {
//            $("#photolist .btn_r").click();
            p.balanceR();
        }
        if (curr_index < 1) {
//            $("#photolist .btn_l").click();
            p.balanceL();
        }
        p.settings.imgLoadTimeOut1 = setTimeout(function () { if (p.settings.noPlay) $("#imgLoading em").html(i18n._('Loading....')); }, 5000);
        p.settings.imgLoadTimeOut2 = setTimeout(function () { if (p.settings.noPlay) $("#imgLoading em").html(i18n._('Loading Failed!')); $("#imgLoading").fadeOut("slow"); p.settings.noPlay = false; }, 10000);
        if ($.browser.msie && parseInt($.browser.version) < 9) {
            p.settings.noPlay = false;
            $("#mainimg").attr("src", src);
            $("#picbox .p_left, #picbox .p_right").css("height", $("#picbox").height());
            p.setIntervalPlay();
            $("#imgLoading").hide();
            clearTimeout(p.settings.imgLoadTimeOut1);
            clearTimeout(p.settings.imgLoadTimeOut2);
        }
        if(i + 1 < p.settings.max){
			$("<img />").attr("src", $("#photolist li").eq(i + 1).find("img").attr("bigpic"));
		}		
		if($(".summary .description").text().length > 0 ){
			p.showImgSummary();
		}else{
			p.hideImgSummary();
		}
      //if(i == 1) $("html,body").animate({ scrollTop: $("article").offset().top }, 800);
    },
    next: function () {
        $(".tool .prev,.tool .next").removeClass("grey");
        var i = this.settings.curr + 1;
        if (i == this.settings.max) {
            $(".tool .next").addClass("grey");
			location.hash='';
            this.end();
            return;

        }
        this.curr = i;
        this.goto(i);
    },
    prev: function () {
        $(".tool .prev,.tool .next").removeClass("grey");
        this.settings.noPlay = false;
        var i = this.settings.curr - 1;
        if (i == -1) {
            //  i = this.settings.max - 1;
            $(".tool .prev").addClass("grey");
            return;
        }
        this.curr = i;
        this.goto(i);
    },
    end: function () {
        var p = this;
        if (p.settings.noPlay) return;
        p.settings.noPlay = true;
        $(".tool .prev,.tool .play,.tool .next,.tool .list,.tool .download").addClass("grey");
        clearInterval(p.settings.t);
        p.settings.timeCount = 15;
        clearInterval(p.settings.timeCountOut);
        $("#more .timecount").html(p.settings.timeCount);
        $(".summary,#picbox").hide();
//        $(".summary,#picbox,#photolist").hide();
        $("#more").show();
         var timeCount = p.settings.timeCount;
        p.settings.timeCountOut = setInterval(function () {
            timeCount = --timeCount;
            if(timeCount>0)
            $("#more .timecount").html(timeCount);
            if (timeCount <= 0) {
                clearInterval(p.settings.timeCountOut);
				var nextUrl = $("#more .nextplay a").attr("href");
               if(!nextUrl){
				  nextUrl = location.href.replace(/imglib\/\d+.shtml/,"photo.shtml");
				}
				location.href = nextUrl ;
            }
        }, 1500);
    },
    list: function () {
        var p = this;
        var n = Math.ceil(p.settings.max / 25);
        var curr = parseInt(p.settings.curr / 25);
        var str = "";
        if (n == 1) return;
        str += "<ul class='cf'><li class='prev ";
        if (curr == 0)
            str += "grey";
        str += "'></li>";
        $("#photolist .mask ul").css("top", curr * (-550));
        for (var i = 0; i < n; i++) {
            str += "<li";
            if (curr == i) str += " class='on' ";
            str += "></li>";
        }
        str += "<li class='next ";
        if (curr == n - 1)
            str += "grey";
        str += "'></li></ul>";
        $("#photolist .listnav").html(str);
        $("#photolist .listnav ul li").click(function () {
            $(".listnav .prev,.listnav .next").removeClass("grey");
            if ($(this).hasClass("prev")) {
                if (curr < 2) {
                    $(".listnav .prev").addClass("grey");
                }
                if (curr == 0) return;

                curr--;
                $("#photolist .listnav ul li").removeClass("on");
                $("#photolist .listnav ul li").eq(curr + 1).addClass("on");
                $("#photolist .mask ul").animate({ top: curr * (-550) }, "slow");
                return;
            }
            if ($(this).hasClass("next")) {
                if (curr > n - 3) {
                    $(".listnav .next").addClass("grey");
                }
                if (curr == n - 1) return;
                curr++;
                $("#photolist .listnav ul li").removeClass("on");
                $("#photolist .listnav ul li").eq(curr + 1).addClass("on");
                $("#photolist .mask ul").animate({ top: curr * (-550) }, "slow");
                return;
            }
            if ($(this).hasClass("on")) {
                return;
            }
            curr = $(this).index() - 1;
            $("#photolist .listnav ul li").removeClass("on");
            $("#photolist .listnav ul li").eq(curr + 1).addClass("on");
            $("#photolist .mask ul").animate({ top: curr * (-550) }, "slow");
        });
    },
    touch: function () {
        if (window.Touch) {
            var startX;
            document.getElementById("picbox").ontouchstart = function (event) { event.preventDefault(); startX = event.changedTouches[0].pageX; }
            document.getElementById("picbox").ontouchend = function (event) {
                event.preventDefault();
                var c = event.changedTouches[0].pageX - startX;
                if (c > 0) { $(".tool .prev").click(); }
                else
                { $(".tool .next").click(); }
            }
        }
    },
    balanceL: function () {
        var p = this;
        $("#photolist .btn_l,#photolist .btn_r").removeClass("grey");
        var min = -1 * p.settings.cellWidth * p.settings.n;
        var left = parseInt($("#photolist .mask ul").css("left"));
        if (left >= min) {
            $("#photolist .mask ul").animate({ left: 0 }, "slow");
            $("#photolist .btn_l").addClass("grey");
        }
        else {
            $("#photolist .mask ul").animate({ left: left - min }, "slow");
        }
    },
    balanceR: function () {
        var p = this;
        $("#photolist .btn_l,#photolist .btn_r").removeClass("grey");
        var min = -1 * p.settings.cellWidth * p.settings.n;
        var max = -1 * p.settings.cellsWidth() + p.settings.cellWidth * p.settings.n;
        var left = parseInt($("#photolist .mask ul").css("left"));
        if (left <= max - min) {
            $("#photolist .mask ul").animate({ left: max }, "slow");
            $("#photolist .btn_r").addClass("grey");
        }
        else {
            $("#photolist .mask ul").animate({ left: left + min }, "slow");
        }
    },
    photoResize: function () {
        $("#mainimg").height('auto');
        var boxh = $('#picbox').height();
        var winh = $(window).height();
        $('article').height(winh);
        if (boxh > winh){
            $("#mainimg").height(winh-20);
            $('#picbox').css('top', 0);
            $('.summary').css('bottom', 0);
        }
        else {
            var toph = (winh-boxh)*(1-0.618);
            $('#picbox').css('top', toph);
            $('.summary').css('bottom', winh-toph-$("#mainimg").height()-$('.summary').height()-25);
        }
    }
}