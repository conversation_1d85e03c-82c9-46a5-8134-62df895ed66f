<?php $end = $calendarModel->monday_timestamp + 345600; ?>
<h3 style="text-align: center"><?php echo $class_model->title; ?> 每周安排 <small>Week <?php echo $weeknum; ?> <?php echo date('Y/m/d', $calendarModel->monday_timestamp) . ' - ' . date('Y/m/d', $end)?></small></h3>
<table style="width: 100%" border="1">
    <tr>
        <?php for ($i = 0; $i <= 4; $i++) { ?>
        <td style="padding:5px" width="380">
            <?php
            $dayTitle[$i] = Yii::app()->locale->getWeekDayName($i + 1);
            echo $dayTitle[$i]; ?>
        </td>
        <?php } ?>
    </tr>
<?php
$scheduleData = json_decode(base64_decode($schedule->data->data));
//print_r($scheduleData);exit;
$i=0;
$loop = true;
$tiems = array();
while ($loop == true) {
    echo '<tr>';
    $j=0;
    foreach ($scheduleData as $key=>$item) {
        $json = json_decode($item);
        if(isset($json[$i])){
            echo '<td style="padding:5px;vertical-align: top">';
                if(isset($tiems[$key])){
                    echo date('H:i', $tiems[$key][$i]) . '<br>';
                }else{
                    $tiems[$key][$i] = $classTimestart;
                    echo date('H:i', $tiems[$key][$i]) . '<br>';
                }
            echo $json[$i]->title . '<br>' ;
            if(isset($json[$i]->content)){
                echo $json[$i]->content  . '<br>';
            }
            echo '</td>';
        }else{
            echo '<td></td>';
        }
        if (!isset($json[$i])){
            $j+=1;
        }
        $tiems[$key][$i+1] = $tiems[$key][$i] + $json[$i]->period * 60;
    }
    if ($j>=5){
        $loop =false;
    }
    $i++;
}
?>
</table>



