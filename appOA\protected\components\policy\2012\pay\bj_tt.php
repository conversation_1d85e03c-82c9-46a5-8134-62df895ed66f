<?php
if(!defined("IVY_POLICY"))
throw new CException('DO NOT USE THIS FILE DIRECTLY');

return array( 'policy'=>array(

	//是否老生老办法
	ENABLE_CONSTANT_TUITION => true,
		
	//学期四个时间点；取前后两端
	TIME_POINTS   => array(201209,201212,201301,201308),
		
	//寒暑假月份，及其每月计费天数
	HOLIDAY_MONTH => array(
         ITEMS => array(
            FEETYPE_TUITION,
            FEETYPE_LUNCH,
            FEETYPE_SCHOOLBUS,
		),
        MONTH=>array(201308=>22),
        TUITION_CALC_BASE => BY_MONTH,
    ),

	//计算基准：BY_MONTH, 或者 BY_SETTING, 前者表示所有的学费都基于月费用计算，后者表示所有的学费都基于学期或学年
	TUITION_CALCULATION_BASE => array(
	
		BY_MONTH => array(
			
			//月收费金额
			AMOUNT => array(
				PROGRAM_MIK => 3380,
				OTHER_PROGRAM_CHN => 1680,
			),
			
			//收费月份，及其权重
			MONTH_FACTOR => array(
				201209=>1,
				201210=>1,
				201211=>1,
				201212=>1,
				201301=>1,
				201303=>1,
				201304=>1,
				201305=>1,
				201306=>1,
				201307=>1
			),
			
			//折扣
			/*
			GLOBAL_DISCOUNT => array(
				DISCOUNT_ANNUAL => '0.97:10:2013-12-01'
			)
			*/
		),
	),
	
	//每餐费用
	LUNCH_PERDAY => 20,
	
	//账单到期日和账单开始日之差
	DUEDAYS_OFFSET => 0,

));

?>