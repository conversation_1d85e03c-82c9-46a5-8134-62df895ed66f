<?php 
    $form=$this->beginWidget('CActiveForm', array(
        'id'=>'Transfers',
        'enableAjaxValidation'=>false,
        'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
    ));
	$branchList = Branch::model()->getBranchList();
?>
	<div class="modal-header">
		<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
		<h4 class="modal-title">孩子转移校园</h4>
	</div>
	<div class="modal-body">
		<div class="form-group">
			<label class="col-xs-3 control-label">校园</label>
			<div class="col-xs-9">
			   <?php echo CHtml::hiddenField('schoolid[schoolid]',$visitId) ?>
			   <?php echo CHtml::hiddenField('schoolid[childid]',$childids) ?>
			   <?php echo CHtml::dropDownList('schoolid',$this->branchId,$branchList, array('class'=>'form-control','disabled'=>'false')); ?>
			</div>
		</div>
		<div class="form-group">
			<label class="col-xs-3 control-label">接收校园</label>
			<div class="col-xs-9">
			   <?php unset($branchList[$this->branchId]);echo CHtml::dropDownList('transferBranchId','',$branchList, array('class'=>'form-control'));?>
			</div>
		</div>
	</div>
	<div class="modal-footer">
		<button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
		<button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
	</div>
<?php $this->endWidget(); ?>
<script>
	$("#Transfer input").each(function(i,val){
		if(val.checked){
			$(this).attr("disabled", "disabled")
		}
	});
</script>
<script>
    function cbTransfer() {
        $('#modal').modal('hide');
    }
</script>