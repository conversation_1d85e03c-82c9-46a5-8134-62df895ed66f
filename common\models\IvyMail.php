<?php

/**
 * This is the model class for table "ivy_sendmail_history".
 *
 * The followings are the available columns in table 'ivy_sendmail_history':
 * @property integer $id
 * @property string $to
 * @property string $cc
 * @property string $bcc
 * @property string $replyto
 * @property string $authorized
 * @property string $subject
 * @property string $content
 * @property string $enclosures
 * @property integer $continue
 * @property integer $status
 * @property integer $updated_timestamp
 * @property integer $userid
 */
class IvyMail extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return IvyMail the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_sendmail_history';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('to, subject, content', 'required'),
			array('continue, status, updated_timestamp, userid', 'numerical', 'integerOnly'=>true),
			array('subject, enclosures', 'length', 'max'=>255),
			array('cc, bcc, replyto, authorized, userid', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, to, cc, bcc, replyto, authorized, subject, content, enclosures, continue, status, updated_timestamp, userid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'to' => Yii::t("labels", 'To'),
			'cc' => Yii::t("labels", 'Cc'),
			'bcc' => Yii::t("labels", 'Bcc'),
			'replyto' => Yii::t("labels", 'Replyto'),
			'authorized' => 'Authorized',
			'subject' => Yii::t("labels", 'Subject'),
			'content' => Yii::t("labels", 'Content'),
			'enclosures' => 'Enclosures',
			'continue' => 'Continue',
			'status' => 'Status',
			'updated_timestamp' => 'Updated Timestamp',
			'userid' => 'Userid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('to',$this->to,true);
		$criteria->compare('cc',$this->cc,true);
		$criteria->compare('bcc',$this->bcc,true);
		$criteria->compare('replyto',$this->replyto,true);
		$criteria->compare('authorized',$this->authorized,true);
		$criteria->compare('subject',$this->subject,true);
		$criteria->compare('content',$this->content,true);
		$criteria->compare('enclosures',$this->enclosures,true);
		$criteria->compare('continue',$this->continue);
		$criteria->compare('status',$this->status);
		$criteria->compare('updated_timestamp',$this->updated_timestamp);
		$criteria->compare('userid',$this->userid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}