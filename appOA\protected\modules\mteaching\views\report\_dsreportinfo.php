<style>
    .ui-radio {
        width: 16px;
        height: 16px;
        position: relative;
        margin-right: 5px;
        border-radius: 30px;
    }
    .normalSelect {width: 104px !important;padding: 0 15px}
    .normalInputGroup {width: 304px !important;padding: 0 15px;display: inline-block}
    .label {
        font-size: 14px;
    }

    input[type="radio"]:checked + label {
        color: red;
    }

    input[type="radio"]:hover + label {
        color: red;
    }
    .counters {
        color: #b2b2b2;
        text-align: right;
    }
</style>
<?php
    $MYP = array(
        Yii::t('principal','Self-Manage. Skills'),
        Yii::t('principal','Research Skills'),
        Yii::t('principal','Social Skills'),
        Yii::t('principal','Thinking Skills'),
        Yii::t('principal','Communication Skills'),
    );
    $MYP_value = array('E', 'P', 'L', 'N');
    $attendance_ext = $model->attendance_ext ? CJSON::decode($model->attendance_ext) : array();
?>
<div class="row">
    <div class="col-sm-3">
        <div>
            <?php if ($childrepoer->is_stat == 1): ?>
                <div class="alert alert-success text-center">
                    <h4><?php echo $child->getChildName(); ?></h4>

                    <div class="p20"><span class="glyphicon glyphicon-ok-sign"></span>
                        <?php echo Yii::t('teaching', 'Report is online'); ?>
                        <?php if($childrepoer->pdf_file != 1) { ?>
                            (<?php echo Yii::t('teaching','PDF being generated');?>)
                        <?php } ?>
                    </div>
                    <button type="button" class="btn btn-danger"
                            onclick="setReportStatus('offline', <?php echo $childrepoer->id ? $childrepoer->id : 0; ?>)">

                        <?php echo Yii::t('teaching', 'Make Offline'); ?>
                    </button>
                    <button type="button" class="btn btn-info"
                            onclick="reportPreview(<?php echo $child->childid; ?>,<?php echo $semester; ?>,<?php echo $classid ?>,<?php echo $yid ?>,<?php echo $childrepoer->id ?>)">
                        <?php echo Yii::t('teaching', 'Preview'); ?></button>
                </div>
            <?php else: ?>
                <div class="alert alert-danger text-center">
                    <h4><?php echo $child->getChildName(); ?></h4>

                    <div class="p20"><span class="glyphicon glyphicon-exclamation-sign"></span>
                        <?php echo Yii::t('teaching', 'Report is offline'); ?>
                    </div>
                    <button type="button" class="btn btn-primary"
                            onclick="setReportStatus('online', <?php echo $childrepoer->id ? $childrepoer->id : 0; ?>)">
                        <?php echo Yii::t('teaching', 'Make Online'); ?>
                    </button>
                    <button type="button" class="btn btn-info"
                            onclick="reportPreview(<?php echo $child->childid; ?>,<?php echo $semester; ?>,<?php echo $classid ?>,<?php echo $yid ?>,<?php echo $childrepoer->id ?>)">
                        <?php echo Yii::t('teaching', 'Preview'); ?>
                    </button>
                </div>
            <?php endif; ?>
        </div>
    </div>
    <div class="col-sm-3">
        <img class="img-thumbnail"
             src="<?php echo CommonUtils::childPhotoUrl($child->photo); ?>"
             alt="<?php echo $child->getChildName(); ?>" width="147">
    </div>
</div>

<div class="row">
    <div class="col-sm-12">
        <form action="<?php echo $this->createUrl('reportchild'); ?>" method="post" class="J_ajaxForm">
            <div class="panel panel-default">
                <div class="panel-body">
                    <div class="row">
                        <div class="normalInputGroup">
                            <div class="input-group input-group">
                                <span class="input-group-addon"
                                      id="sizing-addon1"><?php echo Yii::t('principal', 'Days in the Reporting Period'); ?></span>
                                <input type="text" class="form-control" aria-describedby="sizing-addon1" name="evaluate"
                                       value="<?php echo $childrepoer->evaluate_number ?>" readonly>
                            </div>
                        </div>
                        <div class="normalInputGroup">
                            <div class="input-group input-group">
                                <span class="input-group-addon"
                                      id="sizing-addon2"><?php echo Yii::t('principal', 'Days Absent'); ?></span>
                                <input type="text" class="form-control" aria-describedby="sizing-addon2" name="absent"
                                       value="<?php echo $childrepoer->absent_days ?>" readonly>
                            </div>
                        </div>
                    </div>
                    <!--<button type="submit"
                            class="btn btn-primary J_ajax_submit_btn"><php /*echo Yii::t('global', 'Save'); */?></button>-->
                    <input type="hidden" name="childid" value="<?php echo $child->childid; ?>">
                    <input type="hidden" name="classid" value="<?php echo $classid; ?>">
                    <input type="hidden" name="semester" value="<?php echo $semester; ?>">
                </div>
            </div>
        </form>
        <div class="form-group">
            <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#modal1"
                    data-backdrop="static">
                <?php echo Yii::t('principal','Add a course'); ?>
            </button>
        </div>
        <?php if ($course): ?>

            <?php foreach ($course as $_course): ?>
                <form action="<?php echo $this->createUrl('saveDsreport'); ?>" method="post" class="J_ajaxForm">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <div class="row">
                                <h4 class="col-md-7">
                                    <a href='<?php echo $this->createUrl('updatReport', array('id' => $_course->id)); ?>'
                                       type="button" class="J_modal btn btn-xs btn-info">
                                        <span class="glyphicon glyphicon-wrench"></span>
                                    </a>
                                    <a href="<?php echo $this->createUrl('delDsreport', array('id' => $_course->id)); ?>"
                                       class="btn btn-xs btn-danger J_ajax_del" data-msg = '<?php echo Yii::t('principal','Are you sure you want to delete?') ?>' data-oktext = '<?php echo Yii::t('principal','Delete') ?>' data-canceltext = '<?php echo Yii::t('global','Cancel') ?>'>
                                        <span class="glyphicon glyphicon-minus"></span>
                                    </a>
                                    <?php echo $_course->reportCourse->getName() ?>

                                    <?php echo $user_name[$_course->teacherid] ?>

                                </h4>
                                <h4 class="pull-right text-right" style="line-height: 30px">
                                    <?php echo Yii::t('principal','Year-End Score'); ?>

                                    <div class="btn-group normalSelect pull-right">
                                        <?php echo CHtml::dropDownList("optionvalue", $_course->childTotal->oprion_value, array("1" => 0, 1, 2, 3, 4, 5, 6, 7), array("class" => "form-control", 'empty' => Yii::t('teaching', '-'))); ?>
                                    </div>
                                </h4>


                            </div>
                        </div>
                        <div class="panel-body">
                            <table class="table table-border">
                                <tbody>
                                <tr>
                                    <td class="row" style="border-top: none"><span class="col-md-2"></span>

                                        <div class="col-md-12">
                                            <div class='normalSelect pull-right'>
                                                <span><?php echo Yii::t("principal", "Final") ?></span>
                                            </div>
                                            <?php
                                            foreach ($reports as $_rep) {
                                                echo "<div class='normalSelect pull-right'>";
                                                echo "<span>" . $_rep->cycle . "</span>";
                                                echo "</div>";
                                            } ?>
                                        </div>
                                    </td>
                                </tr>

                                <?php
                                $frecyion_list = json_decode($_course->childTotal()->frecyion_total, TRUE);
                                foreach ($_course->reportCourse->courseScores as $val): ?>
                                    <tr>
                                        <td class="row"><span class="col-md-2"><?php echo $val->title_cn; ?></span>

                                            <div class="col-md-12">
                                                <?php
                                                $array = array();
                                                foreach ($val->items as $_item) {
                                                    $array[$_item->id] = $_item->fraction;
                                                }

                                                echo "<div class='normalSelect pull-right'>";
                                                echo CHtml::dropDownList("totlevalue[$val->id]", $frecyion_list[$val->id], $array, array("class" => "form-control", 'empty' => Yii::t('teaching', '-')));
                                                echo "</div>";

                                                foreach ($reports as $_reports) {
                                                    echo "<div class='normalSelect pull-right'>";
                                                    if ($_course->reportid == $_reports->id) {
                                                        echo CHtml::dropDownList("option[$val->id]", $option[$_course->courseid][$val->id][$_reports->id], $array, array("class" => "form-control", 'empty' => Yii::t('teaching', '-')));
                                                    } else {
                                                        echo CHtml::dropDownList("", $option[$_course->courseid][$val->id][$_reports->id], $array, array("class" => "form-control", "disabled" => "disabled", 'empty' => Yii::t('teaching', '-')));
                                                    }
                                                    echo "</div>";
                                                }
                                                ?>

                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                                </tbody>
                            </table>
                            <div class="form-group">
                                <div class="label label-info"><?php echo Yii::t('principal','Teacher Comments') ?></div>
                            </div>
                            <div class="form-group">
                                <?php echo CHtml::textArea("AchievementReportChildCourse[teacher_message_en]", $_course->teacher_message_en, array('class' => "form-control status", 'rows' => 6,'maxLength'=>500,'onkeyup'=>"countChar('teachercounters$_course->id')")) ?>
                                <div class="counters teachercounters<?php echo $_course->id ?>" style="display: none"><span class="counter"></span>/500 </div>
                            </div>

                            <!-- <div class="form-group">
                                <div class="label label-info"><?php echo Yii::t('principal','Student Comments') ?></div>
                            </div>
                            <div class="form-group">
                                <?php echo CHtml::textArea("AchievementReportChildCourse[student_message_en]", $_course->student_message_en, array('class' => "form-control status", 'rows' => 6,'maxLength'=>500,'onkeyup'=>"countChar('studentcounters$_course->id')")) ?>
                                <div class="counters studentcounters<?php echo $_course->id ?>" style="display: none"><span class="counter"></span>/500 </div>
                            </div> -->
                            <div class="form-group">
                                <div class="label label-info"><?php echo Yii::t('principal','MYP Approaches to Learning Student Progress Summary') ?></div>
                            </div>

                            <div class="row form-group">
                                <?php
                                $myp_list = json_decode($_course->myp);
                                foreach ($MYP as $k => $_myp) {
                                    echo "<div class='col-md-2 mb10'>";
                                    echo "<p>" . $_myp . "</p>";
                                    echo CHtml::dropDownList("myp[$k]", $myp_list[$k], $MYP_value, array("class" => "form-control", 'empty' => Yii::t('teaching', '-')));
                                    echo "</div>";
                                }
                                ?>
                                <div class="col-md-12"><hr/></div>
                                <div class="form-group col-md-12">
                                    <button type="submit"
                                            class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Save'); ?></button>
                                </div>
                            </div>
                            <input type="hidden" name="childid" value="<?php echo $child->childid; ?>">
                            <input type="hidden" name="classid" value="<?php echo $classid; ?>">
                            <input type="hidden" name="semester" value="<?php echo $semester; ?>">
                            <input type="hidden" name="course" value="<?php echo $_course->courseid; ?>">

                        </div>

                    </div>
                </form>
            <?php endforeach; ?>
        <?php endif; ?>

        <form action="<?php echo $this->createUrl('reportchild'); ?>" method="post" class="J_ajaxForm">
            <div class="panel panel-default">
                <div class="panel-heading"><h4><?php echo Yii::t('principal','Homeroom Teacher Comments') ?></h4></div>
                <div class="panel-body">
                    <div class="form-group">
                        <?php echo CHtml::textArea('counselor_en', $childrepoer->counselor_message_en, array('class' => "form-control status", 'rows' => 6,'maxLength'=>500,'onkeyup'=>"countChar('counters3')")) ?>
                        <div class="counters counters3" style="display: none"><span class="counter"></span>/500 </div>
                    </div>
                    <hr/>
                    <div class="form-group">
                        <button type="submit"
                                class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Save'); ?></button>
                    </div>
                    <input type="hidden" name="childid" value="<?php echo $child->childid; ?>">
                    <input type="hidden" name="classid" value="<?php echo $classid; ?>">
                    <input type="hidden" name="semester" value="<?php echo $semester; ?>">
                </div>
            </div>
        </form>
    </div>
</div>

<div class="modal fade" id="modal1" tabindex="-1" role="dialog" aria-labelledby="modal1">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">×</span></button>
                <h4 class="modal-title"><?php echo Yii::t('principal', 'Add a course') ?></h4>
            </div>
            <?php $form = $this->beginWidget('CActiveForm', array(
                'id' => 'visits-form',
                'enableAjaxValidation' => false,
                'action' => $this->createUrl('coures'),
                'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form'),
            )); ?>
            <div class="panel-body">
                <div class="form-group">
                    <label class="col-xs-3 control-label"><?php echo Yii::t('principal','Course Title') ?></label>

                    <div class="col-xs-9">
                        <?php echo CHtml::dropDownList('course', "", $kecheng, array('class' => 'form-control', 'empty' => Yii::t('curriculum', 'Choose'))); ?>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-xs-3 control-label"><?php echo Yii::t('principal','Teacher Name') ?></label>

                    <div class="col-xs-9">
                        <?php echo CHtml::dropDownList('teacherid', Yii::app()->user->id, $user_name, array('class' => 'form-control', 'empty' => Yii::t('curriculum', 'Choose'))); ?>
                        <input type="hidden" name="childid" value="<?php echo $child->childid; ?>">
                        <input type="hidden" name="classid" value="<?php echo $classid; ?>">
                        <input type="hidden" name="semester" value="<?php echo $semester; ?>">
                        <input type="hidden" name="yid" value="<?php echo $yid; ?>">
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit"
                        class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
                <button type="button" class="btn btn-default"
                        data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
            </div>
            <?php $this->endWidget(); ?>
        </div>
    </div>
</div>

<script>
    var modal = '<div class="modal fade" id="modal" class="1123" tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog" role="document"><div class="modal-content"></div></div></div>';
    $('body').append(modal);

    $(function () {
        $('[data-toggle="tooltip"]').tooltip()
    })
    function countChar(spanName) {
        $('.' + spanName).show();
        let textlength = $('.' + spanName).prev('.status').val().length;
        $('.' + spanName).children('.counter').html(function () {
            if (textlength >= 500) {
                $(this).parent().css('color','red');
                return textlength;
            } else {
                $(this).parent().css('color','#b2b2b2');
                return textlength;
            }
        });
    }
</script>