<?php

/**
 * This is the model class for table "ivy_branch_var".
 *
 * The followings are the available columns in table 'ivy_branch_var':
 * @property integer $id
 * @property string $branchid
 * @property string $category
 * @property string $subcategory
 * @property integer $flag
 * @property string $data
 * @property integer $user
 * @property integer $updated
 */
class BranchVar extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_branch_var';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('branchid, category', 'required'),
			array('flag, user, updated', 'numerical', 'integerOnly'=>true),
			array('branchid', 'length', 'max'=>10),
			array('category, subcategory', 'length', 'max'=>32),
			array('data', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, branchid, category, subcategory, flag, data, user, updated', 'safe', 'on'=>'search'),
            array('data', 'numerical', 'integerOnly'=>true, 'on'=>'points'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'branchid' => 'Branchid',
			'category' => 'Category',
			'subcategory' => 'Subcategory',
			'flag' => 'Flag',
			'data' => 'Data',
			'user' => 'User',
			'updated' => 'Updated',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('branchid',$this->branchid,true);
		$criteria->compare('category',$this->category,true);
		$criteria->compare('subcategory',$this->subcategory,true);
		$criteria->compare('flag',$this->flag);
		$criteria->compare('data',$this->data,true);
		$criteria->compare('user',$this->user);
		$criteria->compare('updated',$this->updated);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return BranchVar the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public function bankInfo($schoolId='')
    {
        if($schoolId){
            $cacheId = 'bankinfo'.$schoolId;
            $bank_info = Yii::app()->cache->get($cacheId);
            if(!$bank_info){
                $model = $this->findByAttributes(array('category'=>'bankInfo', 'branchid'=>$schoolId));
                $items = unserialize(base64_decode($model->data));
                if($items){
                    $diglossias = Term::model()->bankinfo()->findAll();
                    foreach($diglossias as $dig){
                        $tmpid = 'd'.$dig->diglossia_id;
                        if( isset($items[$tmpid]) && $items[$tmpid]['display'] == 1 ){
                            $bank_info[$dig->diglossia_id] = array(
                                'title_en' => $dig->entitle,
                                'title_cn' => $dig->cntitle,
                                'content' => Yii::app()->format->ntext($items[$tmpid]['tdata']),
                            );
                        }
                    }
                }
                Yii::app()->cache->set($cacheId, $bank_info, 0, new CDbCacheDependency("SELECT MAX(updated) FROM ".$this->tableName()." where category='bankInfo'"));
            }
        }
        return $bank_info;
    }
}
