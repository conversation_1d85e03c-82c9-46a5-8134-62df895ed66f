<?php
foreach($classes['theClasses'] as $theClass):
?>

	<div class="table_full classInvoice_box">
		<table width="100%">
			<colgroup>
				<col width="100">
			</colgroup>
			<tbody>
				<?php
				$index =0;
				$childList = ($flag == 'current') ? $theClass->children : $theClass->reserveChildren;
				foreach( $childList as $child):
				$childObj = ($flag == 'current') ? $child : $child->childProfile;
				if( ($index % 4) == 0):
				?>
				<tr class="head">
					<td></td>
					<td>
							<ul class="classInvoice-column">
							<?php
								echo CHtml::openTag('li');
								echo CHtml::closeTag('li');
								
								foreach($classes['defaults'] as $default){
									echo CHtml::openTag('li');
									echo $default;
									echo CHtml::closeTag('li');
								}
							?>
							</ul>
					</td>
				</tr>
				
				<?php endif;?>
				
				<tr class="row-status-<?php echo $childObj->status;?>">
					<th><?php
							$sts = $childObj->getSwitchStatus(false);
							
							echo CHtml::openTag("span");
							echo CHtml::link($childObj->getChildName(), array("/child/index/index", "childid"=>$childObj->childid), array("target"=>"_blank"));
							echo CHtml::openTag("i", array("class"=>"stats-".$childObj->status." mr15"));
							echo OA::getChildStatus($childObj->status);
							echo CHtml::closeTag("i");		
							echo CHtml::closeTag("span");
						?></th>
					<td>
						<?php foreach(array('tuition','lunch','bus') as $feeType):?>
							<ul class="classInvoice-list">
							<?php
							
								echo CHtml::openTag('li');
								echo $policyApi->renderFeeType($feeType);
								echo CHtml::closeTag('li');
							
							
								foreach($classes['defaults'] as $default){
									echo CHtml::openTag('li');
									if(isset($classes['data'][$theClass->classid][$childObj->childid][$feeType][$default])){
										$monthData = $classes['data'][$theClass->classid][$childObj->childid][$feeType][$default];
										foreach($monthData as $_iId=>$_data){
											echo CHtml::link($_iId, array("//child/invoice/viewinvoice", "invoiceid"=>$_iId, "childid"=>$childObj->childid), array(
												'class'=>'invoice-status '.$feeType . ' status-' . $_data,
												'target'=>"_blank"
											));
										}
									}
									echo CHtml::closeTag('li');
								}
							?>
							</ul>
						<?php endforeach;?>
					</td>
				</tr>
				<?php
				$index++;
				endforeach;?>
			</tbody>
		</table>
	</div>

<?php
endforeach;
?>