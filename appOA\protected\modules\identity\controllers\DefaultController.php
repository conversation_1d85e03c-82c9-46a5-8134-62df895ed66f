<?php

class DefaultController extends BranchBasedController
{

	public function createUrl($route, $params = array(), $ampersand = '&')
	{
		if (empty($params['branchId'])) {
			$params['branchId'] = $this->branchId;
		}
		return parent::createUrl($route, $params, $ampersand);
	}

	public $childsName = array();
	public $schoolsName = array();

	public function init(){
		parent::init();
		Yii::app()->theme = 'blue';
		//$this->layout = "//layouts/column1";
		Yii::import('common.models.identity.*');
		$this->modernMenuFlag = 'campusOp';
		$this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
		$this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

		$this->branchSelectParams['urlArray'] = array('//identity/default/index');

		// jquery ui
		$cs = Yii::app()->clientScript;
		$cs->registerCoreScript('jquery.ui');
		$cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
		$cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue2.js');
	}

	public function actionIndex()
	{
		$username = Yii::app()->request->getParam('username', '');
		$sign = Yii::app()->request->getParam('sign', 0);
		$childObj = array();
		if($username){
			$criteria = new CDbCriteria;
			$criteria->compare('schoolid', $this->branchId);
			if($username){$criteria->addCondition("concat_ws(',',name_cn,first_name_en,middle_name_en,last_name_en) like '%{$username}%' ");}
			$criteria->index = "childid";
			$childObj = ChildProfileBasic::model()->findAll($criteria);
		}

		$critre = new CDbCriteria;
		$critre->compare('schoolid', $this->branchObj->abb);
        $signStatus = Cards::SIGN_WAIT;
		if($sign){
		    $critre->compare('sign', $sign);
		}else{
            $critre->compare('sign', "<>{$signStatus}");
        }
		if($childObj){$critre->compare('childid', array_keys($childObj));}
		$cardsObj = new CActiveDataProvider('Cards', array(
			'criteria' => $critre,
			'pagination'=>array(
				'pageSize'=>30,
			),
		));


		foreach($cardsObj->getData() as $child){
			$childId[$child->childid] = $child->childid;
		}

		if($childId){
			$childModel = ChildProfileBasic::model()->findAllByPk($childId);

			foreach($childModel as $child){
				$this->childsName[$child->childid] = $child->getChildName();
			}
		}
		$this->render('index', array(
			'cardsObj' => $cardsObj,
		));
	}

	public function actionUpdata()
	{
		$cardid = intval(Yii::app()->request->getParam('cardid',0));
		$model = Cards::model()->findByPk($cardid);
		if($model){
			if(Yii::app()->request->isPostRequest){
				$model->attributes = $_POST['Cards'];
                $model->uploadFile = CUploadedFile::getInstance($model, 'uploadFile');
				$model->updated = time();
				if($model->save()){
					$this->addMessage('state', 'success');
					$this->addMessage('message', Yii::t('message','success'));
					$this->addMessage('callback', 'cbUpdateCard');
					$this->showMessage();
				}else{
					$error = current($model->getErrors());
					$this->addMessage('state', 'fail');
					$this->addMessage('message', Yii::t('message', $error[0]));
					$this->showMessage();
				}

			}

			$this->renderPartial('_update', array(
				'model' => $model,
			));
		}
	}

	public function actionEditphoto($cardid = 0)
	{
		$model = Cards::model()->findByPk($cardid);
		if($model){
			if(Yii::app()->request->isPostRequest){
				$base = Yii::app()->request->getPost('base64');
				if ($base) {
					$arr = CJSON::decode($model->data);
					$file_path = Yii::app()->params['OAUploadBasePath'] . '/crads/' . $arr['photo'];
					$base64_body = substr(strstr($base, ','), 1);
					$data = base64_decode($base64_body);
					file_put_contents($file_path, $data);
					$this->addMessage('state', 'success');
					$this->addMessage('message', Yii::t('message','success'));
					$this->addMessage('callback', 'cbUpdateCard');
					$this->showMessage();
				}
			}
			$this->renderPartial('_editphoto', array(
				'model' => $model,
			));
		}
	}

	public function getButton($data)
	{
		echo CHtml::link(Yii::t('global', 'Edit'), array('updata', 'cardid' => $data->id, "branchId"=>Yii::app()->controller->branchId), array('class' => 'J_modal btn btn-xs btn-info')) . ' ';
		echo CHtml::link(Yii::t('global', '图片'), array('editphoto', 'cardid' => $data->id, "branchId"=>Yii::app()->controller->branchId), array('class' => 'J_modal btn btn-xs btn-info')) . ' ';
		//echo CHtml::link('删除', array('deleteStaff', 'id' => $data->id, "branchId"=>Yii::app()->controller->branchId), array('class' => 'J_ajax_del btn btn-xs btn-info'));
	}

	public function getChildName($data)
	{
		echo $this->childsName[$data->childid];
	}

	public function getSchoolName($data)
	{
		$schoolName = $this->schoolsName[$data->schoolid];
		echo $schoolName['title'];
	}

	public function getSign($data)
	{
		$array = Cards::getConfig();
		echo $array[$data->sign];
	}

	public function getParentName($data)
	{
	    $parentName = CJSON::decode($data->data);
	    echo $parentName['name'];
	}

	public function getParentTel($data)
	{
	    $parentName = CJSON::decode($data->data);
	    echo $parentName['tel'];
	}

	public function getParentRelation($data)
	{
	    $parentName = CJSON::decode($data->data);
	    echo $parentName['relation'];
	}

	public function getPhoto($data)
	{
	    $info = CJSON::decode($data->data);
	    $src = Yii::app()->params['OAUploadBaseUrl'] . '/crads/' . $info['photo'] . "?" . time();
	    echo '<img style="width:50px;" src="' .$src. '">';
	}
}