<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo Yii::t('site','Finance Management');?></li>
        <li class="active">补贴和代收</li>
    </ol>
    <form action="<?php echo $this->createUrl('query')?>" class="form-inline mb15 J_ajaxForm" method="post">
        <?php echo CHtml::dropDownList('month', '', $months, array('class'=>'form-control form-group', 'empty'=>Yii::t('global', 'Please Select')));?>
        <button class="btn btn-primary J_ajax_submit_btn">查询</button>
    </form>
    <div class="row hide" id="viewData">

    </div>
</div>

<div class="modal fade" id="viewItem">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">账单详情</h4>
            </div>
            <div class="modal-body">
                <table class="table table-bordered">
                    <thead>
                    <tr>
                        <th>孩子姓名</th>
                        <th>账单标题</th>
                        <th>账单金额</th>
                    </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<script type="text/template" id="gov-template">
    <div class="col-md-12">
        <h4>
        <% if(typeof(preschool_subsidy) != 'undefined'){%>
        <%= branchs[preschool_subsidy.schoolid]%>
        <% }else{%>
        <%= branchs[sponsorship_fee.schoolid]%>
        <% }%>
        </h4>
        <table class="table table-bordered">
            <thead>
            <tr>
                <th style="width: 20%;">标题</th>
                <th style="width: 20%;">类型</th>
                <th style="width: 20%;">金额</th>
                <th style="width: 20%;">更新时间</th>
                <th style="width: 20%;">操作</th>
            </tr>
            </thead>
            <tbody>
            <% if(typeof(preschool_subsidy) != 'undefined'){%>
            <tr>
                <td><%= preschool_subsidy.title%></td>
                <td>补贴</td>
                <td class="text-right"><a href="javascript:;" onclick="viewItem(<%= preschool_subsidy.id%>)"><%= preschool_subsidy.amount%></a></td>
                <td><%= preschool_subsidy.create_timestamp%></td>
                <td>
                    <a class="btn btn-primary btn-xs J_ajax_del" href="<?php echo $this->createUrl('pay');?>?id=<%= preschool_subsidy.id%>" role="button" data-msg="确定付款<%= preschool_subsidy.amount%>？">付款</a>
                    <a class="btn btn-default btn-xs J_ajax_del" href="<?php echo $this->createUrl('update');?>?id=<%= preschool_subsidy.id%>" role="button" data-msg="确定更新<%= preschool_subsidy.amount%>？">更新</a>
                    <a class="btn btn-default btn-xs" href="<?php echo $this->createUrl('export');?>?id=<%= preschool_subsidy.id%>" role="button">导出</a>
                </td>
            </tr>
            <% }%>
            <% if(typeof(sponsorship_fee) != 'undefined'){%>
            <tr>
                <td><%= sponsorship_fee.title%></td>
                <td>代收</td>
                <td class="text-right"><a href="javascript:;" onclick="viewItem(<%= sponsorship_fee.id%>)"><%= sponsorship_fee.amount%></a></td>
                <td><%= sponsorship_fee.create_timestamp%></td>
                <td>
                    <a class="btn btn-primary btn-xs J_ajax_del" href="<?php echo $this->createUrl('pay');?>?id=<%= sponsorship_fee.id%>" role="button" data-msg="确定付款<%= sponsorship_fee.amount%>？">付款</a>
                    <a class="btn btn-default btn-xs J_ajax_del" href="<?php echo $this->createUrl('update');?>?id=<%= sponsorship_fee.id%>" role="button" data-msg="确定更新<%= sponsorship_fee.amount%>？">更新</a>
                    <a class="btn btn-default btn-xs" href="<?php echo $this->createUrl('export');?>?id=<%= sponsorship_fee.id%>" role="button">导出</a>
                </td>
            </tr>
            <% }%>
            </tbody>
        </table>
    </div>
</script>

<script>
    var branchs = <?php echo CJSON::encode($branchs);?>;
    function callback(data)
    {
        var html = '';
        $('#viewData table tbody').html(html);
        var feerateTemplate = $('#gov-template').html();
        for(var datum in data){
            html += _.template(feerateTemplate, data[datum]);
        }
        $('#viewData').html(html);
        $('#viewData').removeClass('hide');
        head.Util.ajaxDel($('#viewData'));
    }

    function viewItem(id)
    {
        $.getJSON('<?php echo $this->createUrl('viewItem')?>', {id: id}, function(data){
            var html = '';
            data = _.sortBy(data, 'amount');
            for(var datum in data){
                html += _.template('<tr><td><%= name%></td><td><a target="_blank" href="<?php echo $this->createUrl('/child/invoice/viewInvoice')?>?invoiceid=<%= id%>&childid=<%= childid%>"><%= title%></a></td><td class="text-right"><%= amount%></td></tr>', data[datum]);
            }
            $('#viewItem .modal-body table tbody').html(html);
            $('#viewItem').modal();
        });
    }

    function cbUpdate()
    {
        $('form.J_ajaxForm button.J_ajax_submit_btn').click();
    }
</script>