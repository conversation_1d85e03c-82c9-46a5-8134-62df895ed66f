<style>
    [v-cloak] {
		display: none;
	}
    .colorBlue{
        color:#4D88D2
    }
    .project{
        display: inline-flex;
        align-items:center;
        padding:18px;
        border-radius: 4px;
        border: 1px solid #DCDEE0;
        margin-right:24px
    }
    .project:hover,.projected{
        background:#F0F5FB;
        cursor: pointer;
        border: 1px solid #4D88D2;
    }
    .addProject{
        cursor: pointer;
        display: inline-flex;
        align-items:center;
        padding:18px;
        border-radius: 4px;
        border: 1px dashed #4D88D2;
    }
    .projectStaff{
        display: inline-flex;
        align-items:center;
        margin-right:24px
    }
    .projectStaff img{
        width: 28px;
        height: 28px;
        border-radius: 50%;
        object-fit: cover;
    }
    .standard{
        border-radius: 4px;
        margin-bottom: 24px;
        border: 1px solid transparent;
        overflow: hidden
    }
    
    .title{
        display: flex;
        margin-top:24px;
        align-items: center;
    }
    .lineHeight{
        line-height:15px
    }
    .blueLine{
        width: 4px;
        height: 16px;
        background: #4D88D2;
    }
    .maxWidth{
        max-width:750px
    }
    .status{
        border-radius: 4px;
        border: 1px solid #E5E6EB;
        padding:16px 8px;
        position: relative;
    }
    .colorRed{
        color:#D9534F
    }
    .colorWait{
        color:#F0AD4E
    }
    .colorGreen{
        color:#5CB85C
    }
    .statusGreen{
        background:#F2F9F2;
        border: 1px solid rgba(92, 184, 92, 0.30);
    }
    .statusRed{
        background:#FCF1F1;
        border: 1px solid rgba(217, 83, 79, 0.30);
    }
    .statusRed:hover,.statusFill:hover,.statusGreen:hover,.statusWait:hover{
        box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.1);
    }
    .statusWait{
        background:#FDF8F1;
        border: 1px solid rgba(240, 173, 78, 0.30);
    }
    .listMedia{
        padding:8px;
        border: 1px solid #fff;
    }
    .listMedia:hover{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        border: 1px solid #4D88D2;
        cursor: pointer;
    }
    .colorC{
        color:#ccc
    }
    .tag{
        font-size: 12px;
        color: #333333;
        line-height: 12px;
        padding:4px 6px;
        background: #F2F3F5;
        border-radius: 2px;
    }
    .start_date:hover{
        color:#4D88D2 !important;
        text-decoration: underline;
    }
    .moreBtn{
        margin-top:-2px;
        display:none
    }
    .table {
        width: 100%;
        border-collapse: collapse;
        table-layout: fixed;
        margin: 0;
    }
    .sticky {
        position: sticky;
        left: 0;
        z-index: 1;
        background-color: white;
    }
    .table thead th:first-child {
        left: 0;
        z-index: 999 !important;
    }
    .table thead th {
        position: sticky;
        top: 0;
        z-index: 10;
    }
    .table  th{
        border-bottom: 1px solid #E5E6EB !important;
        background:#F7F7F8;
        font-size:14px
    }
    .table  td, .table  th{
        vertical-align: middle !important;
        padding: 16px 8px !important;
    }
    .text-left{
        text-align:left !important
    }
    .table > tbody + tbody{
        border-top: 1px solid #E5E6EB;
    }
    .tableList {
        overflow-x: auto;
    }
    .warning:hover{
        color:#4D88D2 !important;
    }
    hr{
        margin-bottom: 0px;
    }
    .scrollBox::-webkit-scrollbar {
        width : 10px; 
        height:10px;
    }
    .scrollBox::-webkit-scrollbar-thumb {
        border-radius   : 10px;
        background-color: #ccc;
    }
    .scrollBox::-webkit-scrollbar-track {
        box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0.2);
        background   : #ededed;
        border-radius: 10px;
    }
    .timeout{
        background: #FCF1F1;
        border-radius: 4px;
        padding:12px;
        display:flex;
        align-items:center;
        cursor: pointer;
        border:1px solid #FCF1F1;
    }
    .myuser{
        background: #F0F5FB;
        border-radius: 4px;
        padding:12px;
        margin-right:16px;
        display:flex;
        align-items:center;
        border:1px solid #F0F5FB;
        cursor: pointer;

    }
    .myuser img,.timeout img{
        width:24px;
        margin-right:8px
    }
    .timeExpired{
        position: absolute;
        right: 0;
        top: -8px;
        height: 16px;
        background: #D9534F;
        border-radius: 8px 4px 0px 8px;
        color: #fff;
        font-size: 12px;
        line-height: 16px;
        padding: 0 4px;
    }
    .borderBlue{
        border:1px solid #4D88D2
    }
    .borderRed{
        border: 1px solid #D9534F;
    }
</style>
<div class="container-fluid"  id='template' v-cloak>
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','校园管理'), array('//moperation/default/index')) ?></li>
        <li class="active">质检系统</li>
    </ol>
    <div class="row" >
        <div class="col-md-12 col-sm-12">
            <!-- <ul class="nav nav-pills nav-stacked text-left background-gray" id="pageCategory">
                <li class="active" ><a href="<?php echo $this->createUrl('index', array('branchId' => $this->branchId)); ?>">年度质检</a></li>
                <li class="" ><a href="<?php echo $this->createUrl('template', array('branchId' => $this->branchId)); ?>">质检标准</a></li>
            </ul> -->
            <el-tabs v-model="activeName" @tab-click="handleClick">
                <el-tab-pane name="first"> <span slot="label">年度质检</el-tab-pane>
                <el-tab-pane name="second"> <span slot="label">质检标准</el-tab-pane>
            </el-tabs>
        </div>
        <div class='col-md-12 col-sm-12' >
            <div v-if='inintList.tasks_list && inintList.inspect_items' class='mt8'>
                <div class='flex'>
                    <div class='flex1'><button type="button" class="btn btn-primary" @click='addNewQuality'><span class='el-icon-circle-plus-outline'></span> 新的质检</button></div>
                    <div>
                        <select class="form-control"  v-model='current_year' @change='initDataList()'>
                            <option v-for='(list,index) in inintList.year_list' :value='list.key'>{{list.title}}</option>
                        </select>
                    </div>
                </div>
                <div v-if='inintList.tasks_list && inintList.tasks_list.length!=0'>
                    <div v-for='(list,index) in inintList.tasks_list' class='pb20'>
                        <div class='flex mt20' style='align-items: end'>
                            <div class='flex1'>     
                                <div class='title'>
                                    <div class='blueLine'></div>
                                    <span class='font16 fontBold color3 ml8 lineHeight'>{{list.title}}</span> 
                                    <span class='el-icon-edit font16 ml16 cur-p' @click='editQuality(list)'></span>
                                </div>                       
                                <div class='maxWidth'>
                                    <div class='color6 font12 mt12' v-html='html(list.desc)'></div>
                                </div>
                            </div>
                            <div class='myuser' :class='list.filterType=="charge" && list.filterUser?"borderBlue":""' @click='filterUserTime(list,index,"charge")'>
                                <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/qualityUser.png' ?>" alt="">
                                <span>我负责的</span> 
                            </div>
                            <div class='timeout' :class='list.filterType=="time_out" && list.filterUser?"borderRed":""'  @click='filterUserTime(list,index,"time_out")'>
                                <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/qualityTime.png' ?>" alt="">
                                <span class='color3 font12'>超时未完成</span>
                                <span class='ml16 font18 color3 fontBold'>{{list.timeOutTotal}}</span> 
                            </div>
                        </div>
                        <!-- <div class="checkbox">
                            <label>
                            <input type="checkbox" v-model='list.filterUser'> 仅显示我负责的项目
                            </label>
                        </div> -->
                        <hr>
                        <div v-if='showTable'>
                            <div class='' v-show='!list.filterUser'>
                                <div class='tableList scrollBox' :style="'height:'+(height-90)+'px;overflow-y: auto;'">
                                    <table class='table' id='table' :style="'height:'+(height-90)+'px;overflow-y: auto;'">
                                        <colgroup>
                                            <col style="width:62px">
                                            <col style="width:167px" v-for='(_item,key,inx) in list.inspect_items'>
                                        </colgroup>
                                        <thead class='table-header'>
                                            <tr>
                                                <th class="sticky">校园</th>
                                                <th v-for='(_item,key,inx) in list.inspect_items'>{{_item.title}}</th>
                                            </tr>
                                        </thead>
                                        <tbody class='table-body '>
                                        <tr v-for='(item,key,inx) in list.items'>
                                            <td class="sticky">
                                                <el-tooltip class="item" effect="dark" :content="inintList.school_info[item[0].school_id].title" placement="top">
                                                    <span style='cursor: default' class='font14'>{{inintList.school_info[item[0].school_id].abb}}</span>
                                                </el-tooltip>
                                            </td>
                                            <td v-for='(_item,keys,idx) in list.inspect_items' :class='item[idx].status==3?"blue":""' >
                                                <div class='status cur-p' :class='item[idx].status==5?"statusGreen":item[idx].status==4?"statusRed":item[idx].status==3?"statusWait":"statusFill"' >
                                                    <span v-if='item[idx].time_out==1' class='timeExpired'>已超时</span>
                                                    <div>
                                                        <div v-if='item[idx].start_date==""' class='colorC font14 start_date' @click='setDate(item[idx])'>无质检时间</div> 
                                                        <div v-else class='flex align-items font14 ' >
                                                            <span class='start_date mr8' @click='setDate(item[idx])'>{{item[idx].start_date}}</span>
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/quality7.png' ?>" style='width:32px;height:18px' alt="" v-if='item[idx].check_form=="1"'> 
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/quality6.png' ?>" style='width:32px;height:18px' alt="" v-if='item[idx].check_form=="2"'> 
                                                            <el-popover
                                                                v-if='item[idx].remark!=null && item[idx].remark!=""'
                                                                placement="bottom-start"
                                                                width="200"
                                                                trigger="click">
                                                                <div style="margin:0;padding:4px 6px;">
                                                                    <div class='font14 color3'>备注</div>
                                                                    <div class='font14 color6 mt8'>{{item[idx].remark}}</div>
                                                                </div>
                                                                <span class='el-icon-warning-outline ml5 color6 font14 warning' slot="reference"></span>
                                                            </el-popover>
                                                        </div>
                                                    </div>
                                                    <div  class='flex mt8 font12 align-items' > 
                                                        <div class='flex1'>
                                                            <span @click='hrefLink(item[idx])'>                                                        
                                                                <span class='colorBlue align-items' v-if='item[idx].status==0 || item[idx].status==1 || item[idx].status==2'>
                                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/quality4.png' ?>" class='pull-left' style='width:16px;height:16px' alt=""> 
                                                                    <span class='ml4 pull-left'>待填写质检 <span class='el-icon-arrow-right'></span></span>
                                                                </span>
                                                                <span class='colorWait' v-if='item[idx].status==3'>
                                                                    <span class='el-icon-setting'></span> 待整改 
                                                                    <span class='el-icon-arrow-right'></span>
                                                                </span>
                                                                <span class='colorGreen' v-if='item[idx].status==5'> 
                                                                    <span class='el-icon-circle-check'></span> 已完成 
                                                                    <span class='el-icon-arrow-right'></span>
                                                                </span>
                                                                <span class='colorRed' v-if='item[idx].status==4'>
                                                                    <span class='el-icon-s-check'></span> 待验收 
                                                                    <span class='el-icon-arrow-right'></span>
                                                                </span>
                                                                <span class='colorRed' v-if='item[idx].status==6'>
                                                                    <span class='el-icon-s-check'></span> 待确认
                                                                    <span class='el-icon-arrow-right'></span>
                                                                </span>
                                                            </span>
                                                        </div>                                           
                                                        <div class=' color3' v-if='inintList.rectify_count[item[idx].id]'><span class='color9'>整改</span><span class='fontBold'> {{inintList.rectify_count[item[idx].id].count}}</span></div>
                                                        <div v-else>
                                                            <div v-if='item[idx].status==5' ><span class='color9'>整改</span><span class='fontBold'> 0</span></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div v-show='list.filterUser' class='scrollBox' :style="'max-height:'+(height-90)+'px;overflow-y: auto;'">
                                <table class='table ' id='table'  v-if='list.filterItems && list.filterItems.length!=0'>
                                    <colgroup>
                                        <col style="width:80px">
                                        <col style="width:200px" v-for='(_item,key,inx) in list.filterheader'>
                                    </colgroup>
                                    <thead>
                                        <tr>
                                            <th class="sticky">校园</th>
                                            <th width="200" v-for='(_item,key,inx) in list.filterheader'>{{list.inspect_items[_item].title}}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    <tr v-for='(item,key,inx) in list.filterSchool'>
                                        <td class="sticky">
                                            <el-tooltip class="item" effect="dark" :content="inintList.school_info[item].title" placement="top">
                                                <span style='cursor: default' class='font14'>{{inintList.school_info[item].abb}}</span>
                                            </el-tooltip>
                                        </td>
                                        <td v-for='(_item,idx) in list.filterheader'  >
                                            <div v-for='(list,id) in list.filterItems'>
                                                <div v-if='list.inspect_items_id==_item && list.school_id==item'>
                                                    <div class='status cur-p' :class='list.status==5?"statusGreen":list.status==4?"statusRed":list.status==3?"statusWait":"statusFill"'  >
                                                        <span v-if='list.time_out==1' class='timeExpired'>已超时</span>
                                                        <div>
                                                            <div v-if='list.start_date==""' class='colorC font14 start_date' @click='setDate(list)'>无质检时间</div> 
                                                            <div v-else class='flex align-items font14 ' >
                                                                <span class='start_date mr8' @click='setDate(list)'>{{list.start_date}}</span>
                                                                <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/quality7.png' ?>" style='width:32px;height:18px' alt="" v-if='list.check_form=="1"'> 
                                                                <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/quality6.png' ?>" style='width:32px;height:18px' alt="" v-if='list.check_form=="2"'> 
                                                                <el-popover
                                                                    v-if='list.remark!=null && list.remark!=""'
                                                                    placement="bottom-start"
                                                                    width="200"
                                                                    trigger="click">
                                                                        <div style="margin:0;padding:4px 6px;">
                                                                            <div class='font14 color3'>备注</div>
                                                                            <div class='font14 color6 mt8'>{{list.remark}}</div>
                                                                        </div>
                                                                    <span class='el-icon-warning-outline ml5 color6 font14 warning' slot="reference"></span>
                                                                </el-popover>
                                                            </div>
                                                        </div>
                                                        <div  class='flex mt8 font12 align-items' > 
                                                            <div class='flex1'>
                                                                <span @click='hrefLink(list)'>                                                        
                                                                    <span class='colorBlue align-items' v-if='list.status==0 || list.status==1 || list.status==2'>
                                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/quality4.png' ?>" class='pull-left' style='width:16px;height:16px' alt=""> 
                                                                        <span class='ml4 pull-left'>待填写质检 <span class='el-icon-arrow-right'></span></span>
                                                                    </span>
                                                                    <span class='colorWait' v-if='list.status==3'>
                                                                        <span class='el-icon-setting'></span> 待整改 
                                                                        <span class='el-icon-arrow-right'></span>
                                                                    </span>
                                                                    <span class='colorGreen' v-if='list.status==5'> 
                                                                        <span class='el-icon-circle-check'></span> 已完成 
                                                                        <span class='el-icon-arrow-right'></span>
                                                                    </span>
                                                                    <span class='colorRed' v-if='list.status==4'>
                                                                        <span class='el-icon-s-check'></span> 待验收 
                                                                        <span class='el-icon-arrow-right'></span>
                                                                    </span>
                                                                    <span class='colorRed' v-if='list.status==6'>
                                                                        <span class='el-icon-s-check'></span> 待确认 
                                                                        <span class='el-icon-arrow-right'></span>
                                                                    </span>
                                                                </span>
                                                            </div>                                           
                                                            <div class=' color3' v-if='inintList.rectify_count[list.id]'><span class='color9'>整改</span><span class='fontBold'> {{inintList.rectify_count[list.id].count}}</span></div>
                                                            <div v-else>
                                                                <div v-if='list.status==5' ><span class='color9'>整改</span><span class='fontBold'> 0</span></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                                <div v-else>
                                    <el-empty description="<?php echo Yii::t('ptc', 'No Data') ?>"></el-empty>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-else-if='inintList.tasks_list' class='mt24'>
                    <el-empty description="<?php echo Yii::t('ptc', 'No Data') ?>"></el-empty>
                </div>
            </div>
            <div v-else-if='inintList.tasks_list' class='pt24'>
                <div class='font14 color4 mt24 text-center fontBold'>请选创建质检模版</div>
                <el-empty description="<?php echo Yii::t('ptc', 'No Data') ?>"></el-empty>
            </div>
        </div>
    </div>
    <!-- 新加质检 -->
    <div class="modal fade" id="addQualityModal" tabindex="-1" role="dialog" data-backdrop="static" >
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t('leave', '新的质检') ?></h4>
                </div>
                <div class="modal-body relative" style='padding:24px'>
                    <div class='font14 color3 mb24'>{{currentYearValue}}</div>
                    <div class='font14 color6 mb12'>质检</div>
                    <div class='mb24'> 
                        <select class="form-control length_4" v-model='num'>
                            <option value=''>请选择</option>
                            <option v-for='(list,key,index) in inintList.quality_times' :value='list.value'>{{list.title}}</option>
                        </select>
                    </div>
                    <div class='font14 color6 mb12'>质检简介（中文）</div>
                    <div class='mb24'><textarea class="form-control" rows="3" v-model='desc_cn'></textarea></div>
                    <div class='font14 color6 mb12'>质检简介（英文）</div>
                    <div class='mb24'><textarea class="form-control" rows="3" v-model='desc_en'></textarea></div>
                </div> 
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel");?></button>
                    <el-button type="button" :disabled='btnDis' class="btn btn-primary" v-if='editId==""' @click='confirmAdd()' ><?php echo Yii::t("global", "OK");?></el-button>
                    <el-button type="button" :disabled='btnDis'  class="btn btn-primary" v-else @click='confirmEdit()' ><?php echo Yii::t("global", "OK");?></el-button>
                </div>
            </div>
        </div>
    </div>
    <!-- 预计质检时间 -->
    <div class="modal fade" id="addDateModal" tabindex="-1" role="dialog" data-backdrop="static" >
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t('leave', '设置预计质检时间') ?></h4>
                </div>
                <div class="modal-body relative" style='padding:24px'>
                    
                    <div class='font14 color3 mb24' v-if='addDateItem.inspect_items'>
                        <span class='font14 color3 fontBold'>{{addDateItem.inspect_items.title}} </span>
                        <span class='tag ml12'>{{inintList.school_info[addDateItem.school_id].title}}</span>
                    </div>
                    <div class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-2 control-label font14 color6 text-left">质检时间</label>
                            <div class="col-sm-10">
                                <el-date-picker
                                    size='small'
                                    type="date"
                                    format="yyyy-MM-dd"
                                    value-format="yyyy-MM-dd"
                                    placement="bottom-start"
                                    v-model='qualityTime'
                                    placeholder="<?php echo Yii::t('ptc','Select a date') ?>">
                                </el-date-picker>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label font14 color6 text-left">质检形式</label>
                            <div class="col-sm-10">
                                <label class="radio-inline">
                                    <input type="radio"  value="1" v-model='check_form'> 实地考察
                                </label>
                                <label class="radio-inline">
                                    <input type="radio"  value="2" v-model='check_form'> 远程抽查
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label font14 color6 text-left">备注</label>
                            <div class="col-sm-10">
                                <el-input
                                    type="textarea"
                                    :rows="2"
                                    placeholder="请输入内容"
                                    v-model="remark">
                                </el-input>
                            </div>
                        </div>
                    </div>
                </div> 
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel");?></button>
                    <el-button type="button" :disabled='btnDis' class="btn btn-primary" @click='confirmDate()' ><?php echo Yii::t("global", "OK");?></el-button>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
function DoubleScroll(element) {
    var scrollbar = document.createElement('div');
    scrollbar.setAttribute("class", "scrollBox");
    scrollbar.appendChild(document.createElement('div'));
    scrollbar.style.overflow = 'auto';
    scrollbar.style.overflowY = 'hidden';
    scrollbar.firstChild.style.width = element.scrollWidth+'px';
    scrollbar.firstChild.style.paddingTop = '1px';
    scrollbar.firstChild.appendChild(document.createTextNode('\xA0'));
    scrollbar.onscroll = function() {
        element.scrollLeft = scrollbar.scrollLeft;
    };
    element.onscroll = function() {
        scrollbar.scrollLeft = element.scrollLeft;   
    };
    element.parentNode.insertBefore(scrollbar, element);
}

    var height=document.documentElement.clientHeight;
    var  container = new Vue({
        el: "#template",
        data: {
            height:height,
            num:'',
            desc_cn:'',
            desc_en:'',
            title_en:"",
            current_year:'',
            inintList:{},
            currentYearValue:'',
            editId:'',
            btnDis:false,
            addDateItem:{},
            qualityTime:'',
            remark:'',
            check_form:'',
            filterSchool:[],
            filterheader:[],
            filteredData:[],
            showTable:true,
            activeName:'first',
            filterType:'',
            tasksList:{}
        },
        created: function() {
            this.initDataList()
        },
        computed: {
            
        },
        mounted() {
            window.addEventListener('resize', this.handleResize);
        },
        methods: {
            handleResize() {
                this.height = document.documentElement.clientHeight;
            },
            html(data){
                if(data!='' && data!=null){
                    return  data.replace(/\n/g, '<br/>')
                }
            },
            initDataList(){
                let that=this
                this.showTable=false
                $.ajax({
                    url: '<?php echo $this->createUrl("getQualityItem") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        start_year:this.current_year,
                        head_user:''
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            data.data.year_list.forEach(item => {
                                if(item.current==1){
                                    that.currentYearValue=item.title
                                }
                            });
                            that.current_year=data.data.current_year
                            that.inintList=data.data
                            that.tasksList= JSON.parse(JSON.stringify(data.data.tasks_list));
                            that.inintList.tasks_list.forEach(list => {
                                Vue.set(list, 'filterUser', false);
                                Vue.set(list, 'filterType', '');
                            });
                            // 表头
                            that.$nextTick(() => {
                                $('.tableList').each(function(index, element) { DoubleScroll(element); });
                            })
                            that.showTable=true
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })  
            },
            filterUserTime(list,index,type){
                if(list.filterType==type){
                    list.filterType=''
                    list.filterUser=false
                    return
                }
                list.filterType=type
                list.filterUser=true
                const filteredArray = this.tasksList[index].items.flatMap(subarray =>
                    subarray.filter(item => item[type] === 1)
                );
                const filteredData = this.filterByStatus(this.tasksList[index].items,type);
                const flatInputArray = filteredData.flat();
                const uniqueInspectItemIds = [...new Set(flatInputArray.map(item => item.inspect_items_id))];
                const school_id = [...new Set(flatInputArray.map(item => item.school_id))];
                list.filterSchool=school_id
                list.filterItems=filteredArray
                list.filterheader=uniqueInspectItemIds
            },
            addNewQuality(){
                this.num=''
                this.desc_cn=''
                this.desc_en=''
                this.editId=''
                $("#addQualityModal").modal('show')
            },
            editQuality(list){
                this.num=list.num
                this.desc_cn=list.desc_cn
                this.editId=list.id
                this.desc_en=list.desc_en
                $("#addQualityModal").modal('show')
            },
            confirmAdd(){
                let that=this
                that.btnDis=true
                $.ajax({
                    url: '<?php echo $this->createUrl("addQuality") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        start_year:this.current_year,
                        num:this.num,
                        desc_cn: this.desc_cn,
                        desc_en: this.desc_en
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            $("#addQualityModal").modal('hide')
                            that.initDataList()
                            resultTip({
                                msg: data.message
                            });
                            that.btnDis=false
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.btnDis=false
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.btnDis=false
                    },
                })  
            },
            confirmEdit(){
                let that=this
                that.btnDis=true
                $.ajax({
                    url: '<?php echo $this->createUrl("editQualityInspect") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        id:this.editId,
                        num:this.num,
                        desc_cn: this.desc_cn,
                        desc_en: this.desc_en
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            $("#addQualityModal").modal('hide')
                            that.initDataList()
                            resultTip({
                                msg: data.message
                            });
                            that.btnDis=false
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.btnDis=false
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.btnDis=false
                    },
                })  
            },
            hrefLink(item){
                window.location.href='<?php echo $this->createUrl('detail', array('branchId' => $this->branchId)); ?>'+'&id='+item.id
            },
            setDate(item){
                this.addDateItem=item
                this.qualityTime=item.start_date
                this.remark=item.remark
                this.check_form=item.check_form
                $("#addDateModal").modal('show')
            },
            confirmDate(){
                let that=this
                that.btnDis=true
                $.ajax({
                    url: '<?php echo $this->createUrl("setQualityItemStartDate") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        id:this.addDateItem.id,
                        start_date:this.qualityTime,
                        check_form:this.check_form,
                        remark:this.remark,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            $("#addDateModal").modal('hide')
                            that.initDataList()
                            resultTip({
                                msg: data.message
                            });
                            that.btnDis=false
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.btnDis=false
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.btnDis=false
                    },
                })  
            },
            filterByStatus(data, targetStatus) {
                return data.reduce((resultArray, subArray) => {
                    const filteredSubArray = subArray.filter(item => item[targetStatus] === 1);
                    if(filteredSubArray.length > 0) {
                        resultArray.push(filteredSubArray);
                    }
                    return resultArray;
                }, []);
            },
            handleClick(){
                if(this.activeName=='second'){
                    window.location.href="<?php echo $this->createUrl('template', array('branchId' => $this->branchId)); ?>"
                }
            }
        },
    })
</script>
