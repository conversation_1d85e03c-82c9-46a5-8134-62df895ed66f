<?php $clientScript = Yii::app()->getClientScript();
    $clientScript->registerCoreScript('jquery');
?>
<script src="<?php echo Yii::app()->theme->baseUrl; ?>/js/vue.js"></script>
<script src="<?php echo Yii::app()->theme->baseUrl;?>/js/jquery.qrcode.min.js?"></script>

<script src="http://res.wx.qq.com/open/js/jweixin-1.0.0.js"></script>
<script>
    var key='<p>&nbsp;</p>';
    var br='<p><br\/><br\/><\/p>';
    var strong='<p><strong>&nbsp;</strong></p>';
    var strongbr='<p><strong style=\"font-size: 10pt;\"><br\/><\/strong><\/p>';
    var divbr='<div><br \/><\/div>';
    var pspanstyle='<p 50=\"\" 59=\"\" ms=\"\" yahei=\"\" arial=\"\" helvetica=\"\" sans-serif=\"\" vertical-align:=\"\" baseline=\"\" color:=\"\" rgb=\"\" style=\"font-weight: bold; font-size: 13.3333px; margin: 0px 0px 1em; padding: 0px; border: 0px; font-variant-numeric: inherit; font-stretch: inherit; line-height: inherit;\"><span style=\"font-size: 13.3333px;\"><br \/><\/span><\/p>';
    var pstyle='<p 50=\"\" 59=\"\" ms=\"\" yahei=\"\" arial=\"\" helvetica=\"\" sans-serif=\"\" vertical-align:=\"\" baseline=\"\" color:=\"\" rgb=\"\" style=\"font-weight: bold; font-size: 13.3333px; margin: 0px 0px 1em; padding: 0px; border: 0px; font-variant-numeric: inherit; font-stretch: inherit; line-height: inherit;\"><br \/><\/p>';
    var pstrongstyle='<p 50=\"\" 59=\"\" ms=\"\" yahei=\"\" arial=\"\" helvetica=\"\" sans-serif=\"\" vertical-align:=\"\" baseline=\"\" color:=\"\" rgb=\"\" style=\"margin: 0px 0px 1em; padding: 0px; border: 0px; font-variant-numeric: inherit; font-stretch: inherit; font-size: 13.3333px; line-height: inherit;\"><strong><br \/><\/strong><\/p>';
    var pbrstyle='<p inherit=\"\" baseline=\"\" 13px=\"\" sans-serif=\"\" helvetica=\"\" arial=\"\" yahei=\"\" ms=\"\" style=\"font-weight: bold; font-size: 13.3333px; margin: 0px 0px 1em; padding: 0px; border: 0px currentcolor; color: rgb(68, 59, 50);\"><br \/><\/p>';
    var preSt='<pre>';
    var preEn='</pre>';
    function normalText(s){
        return s.replace(new RegExp(strongbr,'g'),"").replace(new RegExp(pstyle,'g'),"").replace(new RegExp(pbrstyle,'g'),"").replace(new RegExp(preSt,'g'),"<p>").replace(new RegExp(preEn,'g'),"</p>").replace(new RegExp(pstrongstyle,'g'),"").replace(new RegExp(pspanstyle,'g'),"").replace(new RegExp(divbr,'g'),"").replace(new RegExp(key,'g'),"").replace(new RegExp(br,'g'),"").replace(new RegExp(strong,'g'),"").replace(/font-size/gi, '').replace(/font-family/gi, '').replace(/margin/gi, '').replace(/padding/gi, '')
    }
</script>
<style type="text/css">
    body,html{
        font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
    }
    .container {
        height: auto !important;
    }
    .mt0{
        margin-top: 0
    }
    ol,ul{
       padding-left:20px
    }
    .weui_btn+.weui_btn {
        margin-top: 0;
    }

    .content{
       
    }
    .current {
        background-color: #04BE02;
        color: #fff !important;
    }
    [v-cloak] {
        display: none;
    }
    a,span{
        text-decoration:none !important;
    }
    .navbg{
        position: fixed;
        font-size: 14px;
        border-bottom: 1px solid #ddd;
        text-align: center;
        z-index:999;
        background:#fff;
        width:100%;
        white-space:nowrap;
        overflow: hidden;
        height: 47px
    }
    .nav{
        overflow-y:scroll;
        height: 47px;
        padding: 10px 0;
    }
    .nav1{
        margin:0 5px;
    }
    .weui_btn{
        overflow: visible;
    }
    .weui_icon_info:before{
        margin-bottom: 7px
    }
    #content{
      resize:none;
    }
    .feedback textarea{
        line-height:25px;
        height:25px;
        margin-top: 2px
    }
    .weui_article img{
        height:auto !important;
    }
    .text-right{
        text-align: right;
        font-weight: normal;
        color: #4D88D2;
        font-size: 14px;
        display: flex;
        align-items: center;
    }
    .videoCopy{
        margin-bottom:20px !important
    }
    .text-right img{
        width: 16px;
        margin-right:6px
    }
    .download{
        color:#666 !important;
        margin-right:24px
    }
    .infoVideo:before{
        color: #666 !important;
        font-size: 16px !important;
        margin-bottom: 2px !important;
        margin-right: 6px !important;
    }
    .flex1{
        flex:1
    }
    .modal {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 999;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        display:none
    }
    .modalText{
        top: 45%;
        left: 50%;
        width: 85%;
        overflow: hidden;
        background-color: #fff;
        border-radius: 16px;
        transform: translate3d(-50%, -50%, 0);
        backface-visibility: hidden;
        transition: 0.3s;
        transition-property: transform, opacity;
        height: auto;
        margin: 0 auto;
        position: absolute;
        text-align:center;
        color: #333;
        opacity: 0;
        transform: translate3d(-50%, -50%, 0) scale(0.5);
    }
    .modalText.visible {
        opacity: 1;
        transform: translate3d(-50%, -50%, 0) scale(1);
    }
    .modalTitle{
        font-size: 18px;
        margin-top: 30px;
    }
    .modalContent{
        padding:5px 24px 24px;
    }
    .modalDesc{
        font-size:12px;
        color:#666;
        margin-bottom:15px;
    }
    .copyLink{
        font-size:16px;
        color: #323233;
        background: #FFFFFF;
        border-radius: 4px;
        border: 1px solid #EBEDF0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding: 8px 12px;
        margin-top: 12px;
    }
    .copyFlex{
        text-align: center;
        height: 50px;
        line-height: 50px;
        border-top: 1px solid #ebedf0;
        display:flex
    }
    .copyFlex span{
        flex:1
    }
    .copyButton{
        border-left: 1px solid #ebedf0;
        color: #4D88D2
    }
    .copyButton .success:before{
        font-size: 16px;
        color: #4D88D2;
        margin-right:5px
    }
    .copied{
        display:none
    }
    .overHidden{
        overflow: hidden;
    }
</style>
<?php 
    $yid = Yii::app()->request->getParam('yid', '');
    $weeknum = Yii::app()->request->getParam('weeknum', '');
    $classid = Yii::app()->request->getParam('classid', '');
    $childid = Yii::app()->request->getParam('childid', '');
    $span = array();
    $vals = 0;
    if($notesSchool->en_important){
        $span[] = Yii::t('portfolio', 'Key Reminders');
    }
    if($notesSchool->en_content){
        $span[] = Yii::t('portfolio', 'School News');
    }
    foreach ($notesClass as $val){
        if($val->en_content){
            $vals = 1; 
        }
    }
    if($vals == 1){
        $span[] = Yii::t('portfolio', 'Class Notes');
    }
    if($notesChild->en_content){
        $span[] = Yii::t('portfolio', 'Child Report');
    }
    if($childMediaModel){
        $span[] = Yii::t('portfolio', 'Class Media');
    }
   $lang=Yii::t('portfolio', 'Video Link');
   $down=Yii::t('portfolio', 'How to Download Videos');
?>
<div id='weeknews' v-cloak>
    <div v-if='data.length!=0'>
        <div class="navbg  weui-navbar" ref="elememt">
            <div class="nav">
                <a href="javascript:void(0)" ref='width' class="weui_btn weui_btn_mini weui_btn_default nav1" v-for='(datas,id) in data' @click='jump(id)'>{{datas}}</a>
            </div>
        </div>
    </div>
</div>
<div class="content">
    <?php if($notesSchool->en_important): ?>
        <div class="cont">
            <div class="weui_cells_title"><?php echo Yii::t('portfolio', 'Key Reminders'); ?></div>
            <div class="weui_panel mt0">
                <article class="weui_article">
                    <section>
                        <script>
                            document.write( normalText(<?php echo CJSON::encode($notesSchool->en_important); ?>) );
                        </script>
                    </section>
                </article>
            </div>
        </div>
    <?php endif ?>
    <?php if($notesSchool->en_content): ?>
        <div class="cont">
        <div class="weui_cells_title"><?php echo Yii::t('portfolio', 'School News'); ?></div>
        <div class="weui_panel mt0">
            <article class="weui_article">
                <section>
                     <script>
                        document.write( normalText(<?php echo CJSON::encode($notesSchool->en_content); ?>) );
                    </script>
                </section>
            </article>
        </div>
    </div>
    <?php endif; ?>
    <?php if($vals==1): ?>
        <div class="cont">
            <?php foreach ($notesClass as $val):?>
                <?php if($val->en_content): ?>
                    <div class="conts">
                        <div class="weui_cells_title"><?php echo $val->en_title ? $val->en_title : Yii::t('portfolio', 'Class Notes');?></div>
                        <div class="weui_panel mt0">
                            <article class="weui_article">
                                <section>
                                    <script>
                                        document.write( normalText(<?php echo CJSON::encode($val->en_content); ?>) );
                                    </script>
                                </section>
                            </article>
                        </div>
                    </div>
                <?php endif; ?>
            <?php endforeach;?>
        </div>
    <?php endif; ?>
    <?php if($notesChild->en_content): ?>
        <div class="cont">
            <div class="weui_cells_title"><?php echo Yii::t('portfolio', 'Child Report'); ?></div>
            <div class="weui_panel mt0">
                <article class="weui_article">
                    <section>
                        <script>
                            document.write( normalText(<?php echo CJSON::encode($notesChild->en_content); ?>) );
                        </script>
                    </section>
                </article>
            </div>
        </div>
    <?php endif; ?>
    
        <?php if($childMediaModel):?>
            <div class="cont pots" >
            <div class="weui_cells_title"><?php echo Yii::t('portfolio', 'Class Media'); ?></div>
            <div class="weui_panel mt0">
            <div class="weui_article">
        <?php
            foreach ($childMediaModel as $childMedia):
                if ($childMedia->photoInfo) {
                    $html = '';
                    echo '<section>';
                    $mediaUrl = $childMedia->photoInfo->getMediaUrl();
                    if ($childMedia->photoInfo->type == 'photo') {
                        $html = CHtml::image($mediaUrl, '', array('class'=>'img-responsive', 'onclick'=>'funcReadImgInfo'));
                    }
                    elseif ($childMedia->photoInfo->type == 'video' && $childMedia->photoInfo->server == 20){
                        $html = '<video  controls="controls" class="img-responsive videoPoster" >';
                        $html .= '<source src="'.$mediaUrl.'" type="video/mp4">';
                        $html .= '</video>';
                        if ($this->state == 'ds') {
                            $html .= "<p class='text-right videoCopy'><span class='flex1'></span><span class='text-right' data-src='".$mediaUrl."' onclick='copyVideo(this)' ><img src='https://m2.files.ivykids.cn/cloud01-file-8025768FuMutkiVYd6yavrKdKYMzGPJEJaI.png'>".$lang."</span></p>";
                        }else{
                            $html .= "<p class='text-right videoCopy'><span class='flex1'></span><span class='mr-20 download'><span class='weui_icon_info_circle infoVideo'></span>".$down."</span><span class='text-right' data-src='".$mediaUrl."' onclick='copyVideo(this)' ><img src='https://m2.files.ivykids.cn/cloud01-file-8025768FuMutkiVYd6yavrKdKYMzGPJEJaI.png'>".$lang."</span></p>";
                        }
                    }
                    $html .= "<p>{$childMedia->content}</p>";
                    echo $html;
                    // echo $childMedia->photoInfo->renderMedia();
                    echo '</section>';
                }
            endforeach;
        ?>
        </div>
        </div>
        </div>
        <?php endif;?>   
     
</div>
<!-- 反馈 -->
<div class="feedback weui_cells weui_cells_form">
    <div class="weui_cell weui_cell_switch">
        <div class="weui_cell_hd">
           <img src="<?php echo Yii::app()->theme->baseUrl; ?>/images/journal4.png" alt="" style="width:24px;margin-right:5px;display:block;margin-top:-2px">
        </div>
        <div class="weui_cell_hd weui_cell_primary">
        
        <textarea rows="6" placeholder="<?php echo Yii::t('portfolio', 'Your feedback to this week\'s report'); ?>" id="content"></textarea></div>
        <div class="weui_cell_ft" id="replynumber_parent">
            <?php if ($ifread) echo '<span></span>'; ?>
            <a class="weui_btn weui_btn_mini weui_btn_default" href="<?php echo $feedbacka ? $this->createUrl('feedBack', array('weeknum' =>$weeknum, 'yid'=>$yid)) : 'javascript:void(0)'; ?>" id="replynumber"><?php echo $feedbacka?>
            </a>
            <a class="weui_btn weui_btn_mini weui_btn_primary" href="javascript:;" id="showTooltips" onclick="showTooltips()" style="display: none;"><?php echo Yii::t('global', 'Submit'); ?></a>
        </div>
    </div>
</div>
<!-- 复制视频 -->
<div class="modal" id="qrcodeModal">
    <div class='modalText'>
        <div class='modalTitle'><?php echo Yii::t('portfolio', 'Video Link'); ?></div>
        <div class='modalContent'>
            <div class='modalDesc'><?php echo Yii::t('portfolio', 'You can download or share the video via link or QR code below.'); ?></div>
            <div id="qrcode"></div>
            
            <img id="logoDs" src='<?php echo Yii::app()->theme->baseUrl; ?>/images/qr_ds_logo.png' alt="" style="display:none;">
            <img id="logoIvy" src='<?php echo Yii::app()->theme->baseUrl; ?>/images/qr_ivy_logo.png' alt="" style="display:none;">

            <div class='copyLink' id='videoUrl'></div>
        </div>
        <div class='copyFlex'>
            <span onclick='closeCopy()'><?php echo Yii::t('portfolio', 'Close'); ?></span>
            <span class='copyButton' id="copyButton"><?php echo Yii::t('portfolio', 'Copy Link'); ?></span>
            <span class='copyButton copied' ><span class='weui_icon_success_no_circle success'></span><?php echo Yii::t('portfolio', 'Copied'); ?></span>
        </div>
        
       
    </div>
</div>
<script>
funcReadImgInfo()
var navdata = <?php echo json_encode($span); ?>;
function funcReadImgInfo(){ 
    var imgs = [];
    var imgObj = $(".pots img");
    for(var i=0; i<imgObj.length; i++){ 
        imgs.push(imgObj.eq(i).attr('src').replace('ww600', 'w2000').replace('ww600A', 'w2000A').replace('ww600B', 'w2000B').replace('ww600C', 'w2000C').replace('w600', 'w2000').replace('w600A', 'w2000A').replace('w600B', 'w2000B').replace('w600C', 'w2000C'));
        imgObj.eq(i).click(function(){
            var nowImgurl = $(this).attr('src').replace('ww600', 'w2000').replace('ww600A', 'w2000A').replace('ww600B', 'w2000B').replace('ww600C', 'w2000C').replace('w600', 'w2000').replace('w600A', 'w2000A').replace('w600B', 'w2000B').replace('w600C', 'w2000C');
                wx.previewImage({
                "urls":imgs,
                "current": nowImgurl
            });
        });
    }
    const videoList = document.getElementsByClassName('videoPoster');
    for (let i = 0; i < videoList.length; i++) {
        let videoHtml=videoList[i]
        var sources = videoHtml.getElementsByTagName('source')[0];
        var url = sources.src;
        const video = document.createElement('video')
        video.src = url 
        video.crossOrigin = 'anonymous' // 解决跨域问题，也就是提示污染资源无法转换视频
        video.currentTime = 1 // 第一帧
        let canvas = document.createElement('canvas') 
        const ctx = canvas.getContext('2d') 
        video.addEventListener('loadeddata', function() {
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
            const imageDataUrl = canvas.toDataURL('image/png');
            videoHtml.setAttribute('poster',imageDataUrl)
            video.remove()
            canvas.remove()
        });
    };
}

 var weeknews = new Vue({
        el: "#weeknews",
        data: {
          scroll: '',
          data:navdata,
          height:'',
        },
        created: function() {
        },
        watch: {
            scroll: function() {
                this.loadSroll()
            },
        },
        mounted() {
           window.addEventListener('scroll', this.dataScroll, true);
           if(this.data!=''){
                this.height= this.$refs.elememt.offsetHeight;
                $('.content').css('padding-top',this.height)
                $('.content').css('padding-bottom',$('.feedback').height())
           }
           
        },
        methods: {
            dataScroll: function() {
                this.scroll = document.documentElement.scrollTop || window.pageYOffset || document.body.scrollTop;
            },
            jump(index,e) {
                var jump = document.getElementsByClassName('cont');
                var total = jump[index].offsetTop-this.height-10 ;
                window.scrollTo({
                    top: total,
                    behavior: 'smooth'
                });
            },
            loadSroll: function() {
                var self = this;
                var $navs = $(".nav1");
                var modwidth=document.body.clientWidth
                var sections = document.getElementsByClassName('cont');
                var height = (document.documentElement.clientHeight || document.body.clientHeight) / 2
                for(var i = sections.length - 1; i >= 0; i--) {
                    var widths=0
                    for(var j=0;j<i+1;j++){
                        widths+=self.$refs.width[j].offsetWidth+10
                    }
                    var navleft=$('.nav').scrollLeft()+self.$refs.width[i].offsetWidth
                    if(modwidth<widths){
                        $('.nav').scrollLeft(navleft);
                    }else{
                        $('.nav').scrollLeft(0);
                    }
                    if(self.scroll >= sections[i].offsetTop - height) {
                        $navs.eq(i).addClass("current").siblings().removeClass("current")
                        break;
                    }
                }
            },
        }
    });
 //反馈
function showTooltips(){
    $('#loadingToast').show();
    var schoolid = '<?php echo $schoolid; ?>';
    var yid = '<?php echo $yid; ?>';
    var weeknum = '<?php echo $weeknum; ?>';
    var classid = '<?php echo $classid; ?>';
    var comrootid = 1;
    var childid = <?php echo $childid; ?>;  
    $.ajax({
        type: "POST",
        url: '<?php echo $this->createUrl('SaveFeedback'); ?>',
        data: {childid:childid,schoolid:schoolid,comrootid:comrootid,yid:yid,weeknumber:weeknum,classid:classid,content:$("#content").val(),},
        dataType:"json",
        success:function(data){
            if(data.state == 'success'){
                showMessage('<?php echo Yii::t('global', 'Success!'); ?>');
                location.href='<?php echo $this->createUrl('feedBack', array('weeknum'=>$weeknum, 'yid'=>$yid));?>';
            }else{
                showTips('<?php echo Yii::t('global', 'Failed!'); ?>', data.message);
            }
        },
        complete:function () {
            $('#loadingToast').hide();
        }
    }); 
}

$('#content').focus(function(){
    $('#showTooltips').show();
    $('#replynumber').hide();
}).blur(function(){
    if($(this).val() == ''){
        $('#replynumber').show();
        $('#showTooltips').hide();
        $(this).height( 25 );
    }
}).keyup(function(){
    $(this).height( $(this).prop('scrollHeight') );
    $('.content').css('padding-bottom',$('.feedback').height())
});

//小红点显示
if($("#replynumber_parent > span").css("display") == "block"){
    $("#content").focusin(function(){
        $("#replynumber_parent > span").css("display","none")
    })
    $("#content").focusout(function(){
        $("#replynumber_parent > span").css("display","block")
        })
    };
var downloads = document.getElementsByClassName('download')
    for (let i = 0; i < downloads.length; i++) {
        downloads[i].onclick = () => {
        if ('<?php echo Yii::app()->language ?>'== 'zh_cn') {
            wx.previewImage({
                current: 'https://m2.files.ivykids.cn/cloud01-file-8025768FhqVad_IuSH7i0eD4nb1Wf7d02Kd.jpg',
                urls:['https://m2.files.ivykids.cn/cloud01-file-8025768FhqVad_IuSH7i0eD4nb1Wf7d02Kd.jpg']
            })
        }else{
            wx.previewImage({
                current: 'https://m2.files.ivykids.cn/cloud01-file-8025768Fhgwc-9-2tE_02XD8mOt9LKEU9DV.jpg',
                urls:['https://m2.files.ivykids.cn/cloud01-file-8025768Fhgwc-9-2tE_02XD8mOt9LKEU9DV.jpg']
            })
        }
    };
}
function copyVideo(that) {
   var src=$(that).attr('data-src')
   $('#videoUrl').html(src)
   $('.modal').show()
   $('body').addClass('overHidden')
   $('.copied').hide()
   $("#qrcode").html('')
    var qrcode = $("#qrcode");
    var type='<?php echo $this->state; ?>'
    if(type=='ds'){
        var logo = $('#logoDs');
    }else{
        var logo = $('#logoIvy');
    }
    qrcode.qrcode({
        width: 144,
        height: 144,
        text:src,
        correctLevel: 0,
        render: "canvas", 
    });
    var canvas = qrcode.find('canvas').get(0);
    if (canvas.getContext) {
        var context = canvas.getContext('2d');
        var image = new Image();
        image.src = logo.attr('src');
        image.onload = function() {
            var logo_w = 30;
            var logo_h = 30;
            var logo_x = (canvas.width - logo_w) / 2;
            var logo_y = (canvas.height - logo_h) / 2;
            context.drawImage(image, logo_x, logo_y, logo_w, logo_h);
        };
    }
    $('#copyButton').show()
    $('.modalText').addClass('visible')
}
function closeCopy() {
    $('body').removeClass('overHidden')
    $('.modalText').removeClass('visible')
    $('.modal').hide()
}
$("#copyButton").click(function() {
    var copyText = document.getElementById("videoUrl");
    var input = document.createElement("input"); // 创建input对象
    input.value = copyText.innerText; // 设置复制内容
    document.body.appendChild(input); // 添加临时实例
    input.select(); // 选择实例内容
    document.execCommand("Copy"); // 执行复制
    document.body.removeChild(input); // 删除临时实例
    $('.copied').show()
    $('#copyButton').hide()
});
</script>