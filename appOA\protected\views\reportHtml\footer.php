<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Footer Example</title>
    <style>
        body {
            font-family:"Arial";
            margin: 0;
            padding: 0;
        }
        .footer{
            width: 170mm;
            padding:0 20mm;
            height:20mm
        }
        .footerFlex{
            display: -webkit-box; 
            display: -ms-flexbox; 
            display: -webkit-flex;
            display: flex;    
            padding-top:8mm;
        }
        .footer .line{
            -webkit-box-flex: 1;
            -webkit-flex: 1;
            flex: 1;
            height:1px;
            border-top: 1px dashed #AFCFFF;
            background-size: 10px 1px;
            background-repeat: repeat-x;
            margin:20px 12px
        }
        .footer .pageNum{
            margin-right:8px;
            font-size: 12px;
            color: #333333;
            line-height: 13px;
            margin-top: 14px;
        }
        .footer .pageNum span{
            font-weight: normal;
            font-size: 14px;
            color: #093190;
            line-height: 16px;
        }
    </style>
    <script>
        function subst() {
            var vars = {};
            var query_strings_from_url = document.location.search.substring(1).split('&');
            for (var query_string in query_strings_from_url) {
                if (query_strings_from_url.hasOwnProperty(query_string)) {
                    var temp_var = query_strings_from_url[query_string].split('=', 2);
                    vars[temp_var[0]] = decodeURI(temp_var[1]);
                }
            }
            var css_selector_classes = ['page', 'frompage', 'topage', 'webpage', 'section', 'subsection', 'date', 'isodate', 'time', 'title', 'doctitle', 'sitepage', 'sitepages'];
            for (var css_class in css_selector_classes) {
                if (css_selector_classes.hasOwnProperty(css_class)) {
                    var element = document.getElementsByClassName(css_selector_classes[css_class]);
                    for (var j = 0; j < element.length; ++j) {
                        element[j].textContent = vars[css_selector_classes[css_class]];
                    }
                }
            }
        }
    </script>
</head>
<body onload="subst()">
<footer class='footer'>
    <div class='footerFlex'>
        <img src="https://m2.files.ivykids.cn/cloud01-file-8025768Fn_mZoNj2iI8FsQo2G-xpY_W8eeH.jpg" alt="" style='width:333px'>
        <div class='line'></div>
        <div class='pageNum'><span class="page">02</span> / <span class="topage">10</span></div>
        <img src="https://m2.files.ivykids.cn/cloud01-file-8025768FvUhjJdCebzx7Op5cfmZy0qhDExd.png" alt="" style='width:20px;height:12px;margin-top: 14px;'>
    </div>
   
</footer>
</html>