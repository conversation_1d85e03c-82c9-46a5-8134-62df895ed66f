<?php

class ContractController extends BranchBasedController
{
    public $childName;
    //重写createUrl
    public function createUrl($route, $params = array(), $ampersand = '&', $parentOnly = false)
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    //初始化
    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";
        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Campus Workspace');
        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = false;
        $this->branchSelectParams['urlArray'] = array('//workflow/contract/index');
        $cs = Yii::app()->clientScript;
        $localPathJs = dirname(__FILE__) . "/../assets/workflow.js";
        $localPathCss = dirname(__FILE__) . "/../assets/workflow.css";
        $publicPathJs = Yii::app()->getAssetManager()->publish($localPathJs, false, -1, YII_DEBUG);
        $publicPathCss = Yii::app()->getAssetManager()->publish($localPathCss, false, -1, YII_DEBUG);
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/jquery.printThis.js');
        $cs->registerScriptFile($publicPathJs,  CClientScript::POS_END);
        $cs->registerCssFile($publicPathCss);
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl() . '/jui/css/base/jquery-ui.css');

        Yii::import('common.models.workflow.*');
    }

    public function actionSelect()
    {
        $criter = new CDbCriteria;
        $criter->compare('t.assign_user', Yii::app()->user->getId());
        $criter->compare('t.current_operator', WorkflowRole::WORKFLOW_ROLE_CURRENT_CHECK_USER);
        $waits = WorkflowRole::model()->with('operation')->findAll($criter);

        foreach ($waits as $wait) {
            if (isset($this->branchSelectParams['branchCount'][$wait->operation->branchid])) {
                $this->branchSelectParams['branchCount'][$wait->operation->branchid]++;
            } else {
                $this->branchSelectParams['branchCount'][$wait->operation->branchid] = 1;
            }
        }
        $this->render('//layouts/common/branchSelect');
    }

    public function actionIndex()
    {
        $definationIds = $this->getDefinationIds(array('Contract', "ContractHq"));

        Yii::import('common.models.workflow.contract.ContractApply');
        $model = new WorkflowOperation();
        $crit = new CDbCriteria;
        $crit->compare('defination_id', $definationIds);
        $crit->compare('start_user', $this->staff->uid);
        $crit->compare('branchid', $this->branchId);
        $crit->compare('t.state', WorkflowOperation::WORKFLOW_STATS_OPED);
        $crit->with = array('contract');

        $dataProvider = new CActiveDataProvider($model, array(
            'criteria' => $crit,
            'sort' => array(
                'defaultOrder' => 'start_time DESC',
            ),
            'pagination' => array(
                'pageSize' => 30,
            ),
        ));

        $this->render('index', array(
            'dataProvider' => $dataProvider,
            'branchId' => $this->branchId,
            'categoryList' => ContractApply::getCategoryList(),
            'cycleList' => ContractApply::getCycleList(),
        ));
    }

    public function showContractTitle($data)
    {        
        $title = $data->contract->title;
        echo CHtml::link(
            $title,
            Yii::app()->createUrl("//workflow/entrance/index", array(
                "definationId" => $data->defination_id,
                "nodeId" => $data->current_node_index,
                "operationId" => $data->id,
                "branchId" => $data->branchid,
                "action" => "show",
            )),
            array("class" => "J_ajax", "data-method" => "get")
        );
    }

    public function showContractPaymentPrice($data) {
        // 查找已付金额
        $paidData = ContractApply::getPaymentList($data->contract->id);
        echo number_format($paidData['total'], 2);
    }

    
    public function showContractButton($data)
    {
        Yii::import('common.models.workflow.contract.ContractApply');
        $paidData = ContractApply::getPaymentList($data->contract->id);
        if ($data->contract->state == ContractApply::CANCEL) {
            echo "<label class='label label-danger'>已作废</label>";
            return;
        }
        if ($data->contract->category == 2 || $data->contract->price_total > bcmul($paidData['total'], 100)) {
            echo CHtml::link(
                '发起付款',
                Yii::app()->createUrl("//workflow/entrance/index", array(
                    "definationId" => $data->contract->payment_flow_id,
                    "contractId" => $data->contract->id,
                    "branchId" => $data->branchid,
                    "action" => "initi",
                )),
                array("class" => "btn btn-primary btn-xs J_ajax", "data-method" => "get")
            );

            echo CHtml::link(
                '作废',
                $this->createUrl("cancel", array(
                    "definationId" => $data->contract->payment_flow_id,
                    "contractId" => $data->contract->id,
                    "branchId" => $data->branchid,
                )),
                array("class" => "ml5 btn btn-danger btn-xs J_ajax_del", "data-method" => "get")
            );
        }
    }

    public function actionCancel()
    {
        Yii::import('common.models.workflow.contract.ContractApply');
        $contractId = Yii::app()->request->getParam('contractId');
        $contract = ContractApply::model()->findByPk($contractId);
        if (!$contract) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '合同不存在');
            $this->showMessage();
        }
        $contract->state = ContractApply::CANCEL;
        $contract->updated_by = Yii::app()->user->getId();
        $contract->updated_at = time();
        if ($contract->save()) {
            $this->addMessage('state', 'success');
            $this->addMessage('callback', 'cb_del');
            $this->addMessage('message', '作废成功');
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '作废失败');
        }
        $this->showMessage();
    }

    public function getDefinationIds($handle)
    {
        $criter = new CDbCriteria;
        $criter->compare('branch_type', $this->branchObj->type);
        $criter->compare('defination_id', ">10");
        $criter->compare('defination_handle', $handle);
        $criter->compare('status', 1);
        $models = WorkflowDefination::model()->findAll($criter);
        $definationIds = array();
        foreach ($models as $model) {
            $definationIds[] = $model->defination_id;
        }
        return $definationIds;
    }
}
