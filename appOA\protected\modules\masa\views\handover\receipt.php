
<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
    </button>
    <h4 class="modal-title" id="exampleModalLabel"><?php echo $model->id ? '更新：' : '添加：'; ?></h4>
</div>
<?php $form = $this->beginWidget('CActiveForm', array(
    'id' => 'course-forms',
    'enableAjaxValidation' => false,
    'action' =>$this->createUrl('updateHandovers', array('id'=>$model->id)),
    'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form','enctype'=>"multipart/form-data"),
)); ?>
<div class="modal-body">
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'bank_ref'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model, 'bank_ref', array('maxlength' => 255, 'class' => 'form-control'));?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'bank_deposit_time'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textField($model, 'bank_deposit_time', array('class' => 'form-control', 'id' =>'datepicker')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, '凭证图片'); ?></label>
        <div class="col-xs-9">
            <?php if($model->bank_ref_url){ ?>
                <p class="mt"><a href="<?php echo $this->createUrl('downloads',array('id' => $model->id));?>" target="_blank" class="btn btn-info btn-xs"><span class="glyphicon glyphicon-file"></span> 点击查看附件</a></p>
             <?php }?>
            <?php echo $form->fileField($model, 'bank_ref_file'); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'creater_memo'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->textArea($model, 'creater_memo', array('class' => 'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'status'); ?></label>
        <div class="col-xs-9">
            <label>
            <?php echo $form->checkBox($model, 'status', array('template'=>"<div class=\"test\">{label}{input}{error}</div>")); ?>
                <span class="text-danger">提交给财务确认</span>
            </label>
        </div>
    </div>
</div>
<div class="modal-footer">
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
</div>
<?php $this->endWidget(); ?>
<script>
    $('#datepicker').datepicker({
        changeMonth: true,
        changeYear: true,
        dateFormat:'yy-mm-dd'
    });

    function cbUpdateCash() {
        location.reload();
    }
</script>
