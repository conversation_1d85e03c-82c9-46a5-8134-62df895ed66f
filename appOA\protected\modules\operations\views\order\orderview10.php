<div class="h_a"><?php echo $v;?></div>
<div class="prompt_text">
	<ul>
		<li><?php echo $v;?></li>
	</ul>
</div>

<?php $form=$this->beginWidget('CActiveForm', array(
    'id'=>'form-'.$s,
    'action' => $this->createUrl('/operations/order/confirm', array('branchId'=>$branchId)),
    'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm'),
)); ?>
<div class="table_list J_check_wrap">
	<table width="100%" id="J_table_list" style="table-layout:fixed;">
		<colgroup>
			<col width="50">
			<col width="50">
			<col width="300">
			<col width="150">
			<col width="150">
			<col width="150">
			<col width="150">
			<col>
		</colgroup>
		<thead>
			<tr>
				<td class='tac'><?php echo CHtml::checkBox('chall', false, array('data-checklist'=>'J_check_c1', 'class'=>'J_check_all', 'data-direction'=>'y'));?></td>
				<td class='tac'>数量</td>
				<td>商品</td>
				<td>孩子</td>
				<td>学校</td>
				<td><?php echo Yii::t('labels', 'Created Time');?></td>
				<td>创建人</td>
                <td></td>
			</tr>
		</thead>
		<tbody>
			<?php foreach ($sData as $order):?>
			<tr>
				<td class='tac'><?php echo CHtml::checkBox('orderid[]', false, array('value'=>$order->id,'data-yid'=>'J_check_c1', 'class'=>'J_check'));?></td>
                <td class='tac'><?php echo $order->quantity?></td>
				<td><?php echo $order->Product->getContent()?></td>
				<td><?php echo $order->ChildProfile->getChildName()?></td>
				<td><?php echo $order->school->title?></td>
				<td><?php echo OA::formatDateTime($order->created_timestamp, "medium", "short")?></td>
				<td><?php echo $order->orderUser->getName()?></td>
                <td>
                    <?php
                    if ($this->oAdmin){
                    echo CHtml::link('直接发放', $this->createUrl('/operations/order/godone', array('id'=>$order->id, "branchId"=>$order->schoolid)), array('class'=>'godone'));
                    }
                    ?>
                </td>
			</tr>
			<?php endforeach; ?>
		</tbody>

		</table>
</div>
<div class="btn_wrap">
    <div class="btn_wrap_pd">
        <button class="btn btn_submit mr10 J_ajax_submit_btn <?php if (!$this->oAdmin):?>disabled<?php endif;?>" <?php if (!$this->oAdmin):?>disabled<?php endif;?>>确认订单</button>
    </div>
</div>
<?php $this->endWidget(); ?>
