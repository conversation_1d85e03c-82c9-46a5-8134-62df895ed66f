
<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
    </button>
    <h4 class="modal-title" id="exampleModalLabel"><?php echo $model->id ? '更新：' : '添加：'; ?></h4>
</div>
<?php $form = $this->beginWidget('CActiveForm', array(
    'id' => 'course-forms',
    'enableAjaxValidation' => false,
    'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form'),
)); ?>
<div class="modal-body">
    <div class="form-group mb5">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'vendor_id'); ?></label>
        <div class="col-xs-9">
            <select id="InformationDs_cagegory" name="AsaCourseStaff[vendor_id]" class="form-control">
                <option value="">请选择</option>
                <?php foreach ($teacherList as $k => $item): ?>
                    <optgroup label="<?php echo $k ?>">
                        <?php foreach ($item as $key => $ite) { ?>
                            <option value="<?php echo $key ?>" <?php echo ($key == $model->vendor_id ) ? "selected" : "" ; ?>><?php echo $ite ?></option>
                        <?php } ?>
                    </optgroup>
                <?php endforeach; ?>
            </select>
            <p class="help-block"><?php echo Yii::t('global', '请在“员工及机构”中先添加人员，再从中选择。'); ?></p>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'unit_salary'); ?></label>
        <div class="col-xs-4">
            <?php echo $form->textField($model, 'unit_salary', array('class' => 'form-control')); ?>
        </div>
        <div class="col-xs-5">
            <?php echo $form->DropDownList($model, 'unit_type', $unitType, array('maxlength' => 255, 'class' => 'form-control', 'empty' => '请选择计费类型')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'job_type'); ?></label>
        <div class="col-xs-9">
            <?php echo CHtml::hiddenField('id',$groupId); ?>
            <?php echo $form->DropDownList($model, 'job_type', $staffJobType, array('maxlength' => 255, 'class' => 'form-control', 'empty' => '请选择职位')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'status'); ?></label>
        <div class="col-xs-9">
            <?php echo $form->DropDownList($model, 'status', array('0'=>'未确定','1'=>'双方已确定'), array('maxlength' => 255, 'class' => 'form-control')); ?>
        </div>
    </div>
</div>
<div class="modal-footer">
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
    <button type="button" class="btn btn-default"
            data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
</div>
<?php $this->endWidget(); ?>