<?php
/**
 * 本Widget里无权限检查，请调用前自行检查，最好只通过 ChildBasedController 为基类的Controller调用
 */
class ViewInvoice extends CWidget
{
    public $childId;
    public $invoiceId;
    public $childObj=null;
    
    public function run()
    {
        $this->childObj = is_null($this->getController()->childObj) ?
                ChildProfileBasic::model()->with('stat.classInfo')->findByPk($this->childId) :
                $this->getController()->childObj;
                
        if($this->childObj){
            Yii::import('application.components.policy.PolicyApi');
            Yii::import('common.models.calendar.Calendar');
            $invoice = Invoice::model()->with('invoiceTransaction','calendar','classInfo', 'workflowRefund', 'discount','feeOther')->findByAttributes(array("invoice_id"=>$this->invoiceId, "childid"=>$this->childId));
            if($invoice){
                $policyApi = new PolicyApi($invoice->schoolid);
                $userIds[] = $invoice->userid;
                if(!is_null($invoice->invoiceTransaction)){
                    foreach($invoice->invoiceTransaction as $t){
                        $userIds[] = $t->operator_uid;
                    }
                }
                $userList = User::model()->findAllByPk($userIds);
                foreach($userList as $u){
                    $userNames[$u->uid] = $u->getName();
                }
                $invoice->depositAmount = OA::formatMoney($invoice->original_amount - $invoice->amount);
                $invoice->amount = OA::formatMoney($invoice->amount);
                $this->render('viewinvoice',array("invoice"=>$invoice, "userNames"=>$userNames, 'policyApi'=>$policyApi));
            }
        }
    }
}