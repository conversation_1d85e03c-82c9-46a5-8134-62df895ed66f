<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Routines'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('site', '课后课管理')?></li>
        <li class="active"><?php echo Yii::t('site', '退费列表')?></li>
    </ol>

    <div class="row">
        <div class="col-md-2 col-sm-2 mb10">
        <?php
        $this->widget('zii.widgets.CMenu', array(
            'items' => $this->module->getMenu(),
            'id' => 'pageCategory',
            'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
            'activeCssClass' => 'active',
        ));
        ?>
        </div>
        <div class="col-md-10 col-sm-12">
            <!--分类菜单-->
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->AttendMenu(),
                'id' => 'AttendMenu',
                'htmlOptions' => array('class' => 'nav nav-tabs'),
                'activeCssClass' => 'active',
                'itemCssClass' => ''
            ));
            ?>
            <!-- 驳回列表 -->
            <div class="col-md-12">
                <div class="page-header">
                    <h3><?php echo Yii::t('asa', '缺少资料');?></h3>
                </div>

                <?php
                $this->widget('ext.ivyCGridView.BsCGridView', array(
                    'id' => 'refund',
                    'afterAjaxUpdate' => 'js:head.Util.modal',
                    'dataProvider' => $refund,
                    'template' => "{items}{pager}",
                    //状态为无效时标红
                    //'rowCssClassExpression' => '( $data->status == 0 ? "active" : "" )',
                    'colgroups' => array(
                        array(
                            "colwidth" => array(50, 100, 100, 100, 100, 100, 100, 100),
                        )
                    ),
                    'columns' => array(
                        array(
                            'name' => '#',
                            'value'=>'$data->id',
                        ),
                        array(
                            'name' => Yii::t('child', 'childName'),
                            'value' => array($this, "getChildName"),
                            "type" => "raw",
                        ),
                        array(
                            'name' => Yii::t('child', '课程名称 (时间表名称)'),
                            'value' => array($this, "getCourseName"),
                            "type" => "raw",
                        ),
                        array(
                            'name' => "refund_total_amount",
                            'value'=> array($this, "getAmount"),
                        ),
                        array(
                            'name' => 'refund_method',
                            'value' => array($this, "getMethod"),
                        ),
                        array(
                            'name' => 'dropout_date',
                            'value' => 'date("Y-m-d", $data->dropout_date)',
                        ),
                        array(
                            'name' => 'updated',
                            'value' => 'date("Y-m-d", $data->updated)',
                        ),
                        array(
                            'name'=> Yii::t('asa','操作'),
                            'value'=>array($this, "getButton"),
                        ),
                    ),
                ));
                ?>
            </div>
            <!-- 待审核 -->
            <div class="col-md-12">
                <div class="page-header">
                    <h3><?php echo Yii::t('asa', '待审核');?>  <small>等待园长或校园课后课负责人审批</small></h3>
                </div>

                <?php
                $this->widget('ext.ivyCGridView.BsCGridView', array(
                    'id' => 'review',
                    'afterAjaxUpdate' => 'js:head.Util.modal',
                    'dataProvider' => $review,
                    'template' => "{items}{pager}",
                    //状态为无效时标红
                    //'rowCssClassExpression' => '( $data->status == 0 ? "active" : "" )',
                    'colgroups' => array(
                        array(
                            "colwidth" => array(50, 100, 100, 100, 100, 100, 100, 100),
                        )
                    ),
                    'columns' => array(
                        array(
                            'name' => '#',
                            'value'=>'$data->id',
                        ),
                        array(
                            'name' => Yii::t('child', 'childName'),
                            'value' => array($this, "getChildName"),
                            "type" => "raw",
                        ),
                        array(
                            'name' => Yii::t('child', '课程名称 (时间表名称)'),
                            'value' => array($this, "getCourseName"),
                            "type" => "raw",
                        ),
                        array(
                            'name' => "refund_total_amount",
                            'value'=> array($this, "getAmount"),
                        ),
                        array(
                            'name' => 'refund_method',
                            'value' => array($this, "getMethod"),
                        ),
                        array(
                            'name' => 'dropout_date',
                            'value' => 'date("Y-m-d", $data->dropout_date)',
                        ),
                        array(
                            'name' => 'updated',
                            'value' => 'date("Y-m-d", $data->updated)',
                        ),
                        array(
                            'name'=> Yii::t('asa','操作'),
                            'value'=>array($this, "getButton"),
                        ),
                    ),
                ));
                ?>
            </div>
            <!-- 退费中 -->
            <div class="col-md-12">
                <div class="page-header">
                    <h3><?php echo Yii::t('asa', '退费中');?></h3>
                </div>

                <?php
                $this->widget('ext.ivyCGridView.BsCGridView', array(
                    'id' => 'refundIntermediate',
                    'afterAjaxUpdate' => 'js:head.Util.modal',
                    'dataProvider' => $refundIntermediate,
                    'template' => "{items}{pager}",
                    //状态为无效时标红
                    //'rowCssClassExpression' => '( $data->status == 0 ? "active" : "" )',
                    'colgroups' => array(
                        array(
                            "colwidth" => array(50, 100, 100, 100, 100, 100, 100, 100),
                        )
                    ),
                    'columns' => array(
                        array(
                            'name' => '#',
                            'value'=>'$data->id',
                        ),
                        array(
                            'name' => Yii::t('child', 'childName'),
                            'value' => array($this, "getChildName"),
                            "type" => "raw",
                        ),
                        array(
                            'name' => Yii::t('child', '课程名称 (时间表名称)'),
                            'value' => array($this, "getCourseName"),
                            "type" => "raw",
                        ),
                        array(
                            'name' => "refund_total_amount",
                            'value'=> array($this, "getAmount"),
                        ),
                        array(
                            'name' => 'refund_method',
                            'value' => array($this, "getMethod"),
                        ),
                        array(
                            'name' => 'dropout_date',
                            'value' => 'date("Y-m-d", $data->dropout_date)',
                        ),
                        array(
                            'name' => 'updated',
                            'value' => 'date("Y-m-d", $data->updated)',
                        ),
                        array(
                            'name'=> Yii::t('asa','操作'),
                            'value'=>array($this, "getButton"),
                        ),
                    ),
                ));
                ?>
            </div>
        </div>
    </div>
</div>
<script>
    var modal = '<div class="modal fade" id="modal" class="1123" tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog" role="document"><div class="modal-content"></div></div></div>';
    $('body').append(modal);

    function cbDelRefind()
    {
        $.fn.yiiGridView.update('refund');
    }
    function cbUpdateRefind()
    {
        $('#refund-forms').modal('hide');
        $.fn.yiiGridView.update('refund');
    }
</script>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>