<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo Yii::t('site','Campus Workspace');?></li>
        <li><?php echo Yii::t('site','Reports Templates');?></li>
        <li class="active"><?php echo Yii::t('report', 'Progress Definitions');?></li>
    </ol>

    <div class="row">
        <div class="col-md-2">
            <div class="mb10"><button class="btn btn-primary" onclick="optionGroup()">
                    <?php echo Yii::t('report', 'Definition Groups');?></button></div>
            <ul class="nav nav-pills nav-stacked">
                <?php foreach($groups as $group):?>
                    <li <?php if($gid==$group->id){echo 'class="active"';}?>>
                        <?php echo CHtml::link($group->getTitle(), array('option', 'gid'=>$group->id))?>
                    </li>
                <?php endforeach;?>
            </ul>
        </div>
        <div class="col-md-10">
            <?php if($gid):?>
            <div class="mb10"><button class="btn btn-default" onclick="editOption(0)"><?php echo Yii::t('report',
                        'Add Option');?></button></div>
            <ul class="list-group" id="option-list">
                <?php foreach($options as $option):?>
                <li data-id="<?php echo $option->id;?>" class="list-group-item">
                    <div class="row">
                        <div class="col-md-1">
                            <?php if($option->label):?>
                            <h2><span class="label label-primary"><?php echo $option->label;?></span></h2>
                            <?php endif;?>
                        </div>

                        <div class="col-md-3">
                            <h4><?php echo $option->title_cn;?></h4>
                            <h4><?php echo $option->title_en;?></h4>
                        </div>
                        <div class="col-md-6">
                            <p><?php echo $option->desc_cn;?></p>
                            <p><?php echo $option->desc_en;?></p>
                        </div>
                        <div class="col-md-2">
                            <a role="button" class="btn btn-xs btn-success" style="cursor: move;"><span class="glyphicon
                            glyphicon-move sort-header"></span></a>
                            <button class="btn btn-primary btn-xs" type="button" onclick="editOption(<?php echo $option->id?>)">
                                <span class="glyphicon glyphicon-pencil"></span>
                            </button>
                            <a href="<?php echo $this->createUrl('delOption', array('id'=>$option->id));?>" class="btn btn-danger btn-xs J_ajax_del">
                                <span class="glyphicon glyphicon-remove"></span>
                            </a>
                        </div>
                    </div>
                </li>
                <?php endforeach;?>
            </ul>
            <?php endif;?>
        </div>
    </div>
</div>

<!--选项组管理弹框-->
<?php $optionGroupLabels = ReportsOptionGroup::model()->attributeLabels(); ?>
<div class="modal" id="option-group">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title"><?php echo Yii::t('report','Definition Groups');?> <small></small></h4>
            </div>
            <form class="form-horizontal J_ajaxForm"
                  action="<?php echo $this->createUrl('saveOptionGroup');?>"
                  method="POST">
                <div class="modal-body">
                    <div id="optionGroup-item">
                        <?php foreach($groups as $group):?>
                        <div class="form-group">
                            <div class="col-sm-2 col-md-1 text-center">
                                <div class="checkbox">
                                    <label>
                                        <?php echo CHtml::checkBox('old['.$group->id.'][active]',
                                            ($group->active==1)?true:false);?> <?php echo $optionGroupLabels["active"] ;?>
                                    </label>
                                </div>

                            </div>
                            <div class="col-sm-4 col-md-5">
                                <?php echo CHtml::textField('old['.$group->id.'][title_en]',
                                    $group->title_en,
                                    array('class'=>'form-control','encode'=>false,
                                        'placeholder'=>$optionGroupLabels['title_en']));?>
                            </div>
                            <div class="col-sm-4 col-md-5">
                                <?php echo CHtml::textField('old['.$group->id.'][title_cn]',
                                    $group->title_cn,
                                    array('class'=>'form-control','encode'=>false,
                                        'placeholder'=>$optionGroupLabels['title_cn']));?>
                            </div>
                            <div class="col-sm-2 col-md-1 text-center">
                                <?php echo CHtml::link('<span class="glyphicon glyphicon-remove"></span>',
                                    $this->createUrl('delOptionGroup', array('id'=>$group->id)),
                                    array('class'=>'btn btn-danger btn-sm J_ajax_del'));?>
                            </div>
                        </div>
                        <?php endforeach;?>
                    </div>
                    <div class="row">
                        <div class="col-sm-1"></div>
                        <div class="col-sm-11">
                            <button type="button" class="btn btn-primary btn-sm" onclick="addOptionGroup()"><?php echo
                                Yii::t
                                    ('report', 'Add Group');
                                ?></button>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <input type="hidden" name="ReportsOptionGroup[id]" id="ReportsOptionGroup_id">
                    <button type="button" class="btn btn-default pull-right" data-dismiss="modal">
                        <?php echo Yii::t('global','Cancel');?></button>
                    <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10">
                        <?php echo Yii::t('global','Submit');?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<!--选项编辑弹窗-->
<?php $optionLabels = ReportsOption::model()->attributeLabels(); ?>
<div class="modal" id="optionEditModal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title"><?php echo Yii::t('report','Options Edit');?> <small></small></h4>
            </div>
            <form class="form-horizontal J_ajaxForm"
                  action="<?php echo $this->createUrl('saveOption');?>"
                  method="POST">
                <div class="modal-body">
                    <div class="form-group">
                        <div class="col-sm-12">
                            <?php echo CHtml::textField('ReportsOption[label]', '',
                                array('class'=>'form-control length_2','encode'=>false,
                                    'placeholder'=>$optionLabels['label']));?>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-12">
                            <?php echo CHtml::textField('ReportsOption[title_en]', '',
                                array('class'=>'form-control','encode'=>false,
                                    'placeholder'=>$optionLabels['title_en']));?>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-12">
                            <?php echo CHtml::textField('ReportsOption[title_cn]', '',
                                array('class'=>'form-control','encode'=>false,
                                    'placeholder'=>$optionLabels['title_cn']));?>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-12">
                            <?php echo CHtml::textArea('ReportsOption[desc_en]', '',
                                array('class'=>'form-control','encode'=>false,
                                    'placeholder'=>$optionLabels['desc_en']));?>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-12">
                            <?php echo CHtml::textArea('ReportsOption[desc_cn]', '',
                                array('class'=>'form-control','encode'=>false,
                                    'placeholder'=>$optionLabels['desc_cn']));?>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <?php echo CHtml::hiddenField('ReportsOption[id]');?>
                    <input type="hidden" name="ReportsOption[group_id]" value="<?php echo $gid;?>">
                    <button type="button" class="btn btn-default pull-right" data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
                    <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global','Submit');?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<script type="text/template" id="optionGroup-item-template">
    <div class="form-group">
        <div class="col-sm-2 col-md-1 text-center">
            <div class="checkbox">
                <label>
                    <input type="checkbox" checked name="new[active][<%= id%>]"> <?php echo $optionGroupLabels["active"] ;?>
                </label>
            </div></div>
        <div class="col-sm-4 col-md-5">
            <input class="form-control" placeholder="<?php echo $optionGroupLabels["title_en"] ;?>" type="text"
                   value=""
                   name="new[title_en][<%= id%>]"></div>
        <div class="col-sm-4 col-md-5">
            <input class="form-control" placeholder="<?php echo $optionGroupLabels["title_cn"] ;?>" type="text"
                   value="" name="new[title_cn][<%= id%>]"></div>
        <div class="col-sm-2 col-md-1 text-center">
            <button type="button" class="btn btn-danger btn-sm" onclick="delGroup(this);">
                <span class="glyphicon glyphicon-remove"></span></button></div>
    </div>
</script>

<script>
    var i=0;
    function optionGroup()
    {
        $('#option-group').modal();
    }

    function addOptionGroup()
    {
        $('#optionGroup-item').append(_.template($('#optionGroup-item-template').html(), {id: i++}));
    }

    function delGroup(_this)
    {
        $(_this).parents('.form-group').remove();
    }

    function editOption(id)
    {
        var data;
        if(id){
            $.ajax({
                type: 'get',
                url: '<?php echo $this->createUrl('getOption')?>',
                data: {id: id},
                dataType: 'json',
                async: false
            }).done(function(ret){
                data = ret;
            });
        }
        else{
            data = {label: '', title_en: '', title_cn: '', desc_en: '', desc_en: '', id: 0}
        }
        $('#ReportsOption_label').val(data.label);
        $('#ReportsOption_title_en').val(data.title_en);
        $('#ReportsOption_title_cn').val(data.title_cn);
        $('#ReportsOption_desc_en').val(data.desc_en);
        $('#ReportsOption_desc_cn').val(data.desc_en);
        $('#ReportsOption_id').val(id);

        $('#optionEditModal').modal();
    }

    $( "#option-list" ).sortable({
        placeholder: "ui-state-highlight",
        handle: ".sort-header",
        axis: 'y',
        opacity: 0.8,
        start: function( event, ui ) {
            $('.ui-state-highlight').height($(ui['item'][0]).outerHeight());
        },
        update: function(event, ui){
            var sort = {};
            var cc = 1;
            $(ui['item'][0]).parents('#option-list').find('li').each(function(){
                sort[$(this).data('id')] = cc++;
            });
            $.post('<?php echo $this->createUrl('updateSortOption');?>', {id: $(ui['item'][0]).data('sid'), sort: sort}, function(data){

            });
        }
    }).disableSelection();
</script>

<style>
    .ui-state-highlight{
        background-color: #FCFAF1;
        list-style-type: none;
        border: 0px;
    }
</style>