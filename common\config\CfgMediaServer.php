<?php
/**
 * 
 */
return array(
	'defaultServer' => 20,

	//媒体服务器配置
	'servers' => array(
        '1' => array(
            "id" => "1",
            "url" => "http://img01.ivyschools.cn/",
        ),
		'20_old' => array(
			'id' => '20',
			'url' => 'http://m1.media.ivykids.cn/',
			// 'url' => 'http://ivy001.qiniudn.com/',
			'uploadUrl' => 'https://upload.qiniup.com',
			'callBackUrl' => 'http://**************/qiniuNotify/Receive',
			'persistentsUrl' => 'http://**************/qiniuNotify/Persistents',
            'bucket' => 'ivy001',
            'pipeline' => 'ivyQ1',
			'securityKey' => 'QuHyb4fTLt3w/dudZLWibJ7dSMk=',
		),
        '20' => array(
            'id' => '20',
//            'url' => 'http://m1.media.ivykids.cn/',
            'url' => 'https://m3.media.ivykids.cn/',
            'uploadUrl' => 'https://upload.qiniup.com',
            'callBackUrl' => 'https://transfer.api.ivykids.cn/api/qiniu/receive',
            'persistentsUrl' => 'https://transfer.api.ivykids.cn/api/qiniu/persistents',
            'bucket' => 'ivy001',
            'pipeline' => 'ivyQ1',
            'securityKey' => 'QuHyb4fTLt3w/dudZLWibJ7dSMk=',
        ),
	),
	
	//分配媒体服务器，如果配置了某用户，则以用户配置为准
	'byUser' => array(
//		'2' => '10',
	),
	
	//分配媒体服务器，如果某班级ID有配置，则忽略该班级所在校园的配置
	'byClass' => array(
//		'201' => '10',
	),
	
	//分配媒体服务器，如果该班没有配置，则读取该校园配置，如果都没有配置，则取默认值;
	'byCampus' => array(
//		'BJ_CP' => '20_branch',
//		'CD_FC' => '20_branch',
	)
);