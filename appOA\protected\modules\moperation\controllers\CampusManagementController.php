<?php

class CampusManagementController extends BranchBasedController
{
    public $actionAccessAuths = array(
        'index'     => 'o_A_Access',
        'saveBudget'     => 'tCampusAdmin',
    );
    
    public $dialogWidth=500;
    
    public function init() {
        parent::init();

        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'hqOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','HQ Operations');

        $this->multipleBranch = false;
        Yii::import('common.models.campusmanage.CampusBudget');
    }

    public function actionIndex()
    {
        $allBranch =  $this->getAllBranch();
        $schools = array();
        foreach ($this->accessBranch as $schoolid) {
            if ($schoolid == 'BJ_TYG') {
                continue;
            }
            $schools[$schoolid] = $allBranch[$schoolid]['title'];
        }
        $this->render('index', array(
            'schools' => $schools,
        ));
    }

    // 预算报表
    public function actionSaveBudget()
    {
        $schoolid = Yii::app()->request->getParam('schoolid');
        $yid = Yii::app()->request->getParam('yid');
        $items = Yii::app()->request->getParam('items');
        if (!$schoolid || !$yid) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '参数错误');
            $this->showMessage();
        }
        $criteria = new CDbCriteria();
        $criteria->compare('schoolid', $schoolid);
        $criteria->compare('yid', $yid);
        $criteria->compare('status', 1);
        $criteria->addCondition('classid != 0');
        $budgets = CampusBudget::model()->findAll($criteria);

        $monthNum = array();

        foreach ($budgets as $budget) {
            if (isset($items[$budget->classid][$budget->month])) {
                $num = $items[$budget->classid][$budget->month];
                $budget->num = $num;
                $monthNum[$budget->month] += $num;
            } else {
                $budget->status = 0;
            }
            $budget->updated_at = time();
            $budget->updated_by = $this->staff->uid;
            $budget->save();
            unset($items[$budget->classid][$budget->month]);
        }

        foreach ($items as $classid => $months) {
            $total = 0;
            foreach ($months as $month => $num) {
                $monthNum[$month] += $num;
                $model = new CampusBudget();
                $model->schoolid = $schoolid;
                $model->classid = (int)$classid;
                $model->yid = $yid;
                $model->month = $month;
                $model->num = $num;
                $model->created_at = time();
                $model->created_by = $this->staff->uid;
                $model->updated_at = $model->created_at;
                $model->updated_by = $model->created_by;
                $model->save();
            }
        }

        foreach ($monthNum as $month => $num) {
            // 存储某个月总数
            $criteria = new CDbCriteria();
            $criteria->compare('schoolid', $schoolid);
            $criteria->compare('month', $month);
            $criteria->compare('yid', $yid);
            $criteria->compare('classid', 0);
            $criteria->compare('status', 1);
            $model = CampusBudget::model()->find($criteria);
            if (!$model) {
                $model = new CampusBudget();
                $model->created_at = time();
                $model->created_by = $this->staff->uid;
            }
            $model->schoolid = $schoolid;
            $model->classid = 0;
            $model->yid = $yid;
            $model->month = $month;
            $model->num = $num;
            $model->updated_at = time();
            $model->updated_by = $this->staff->uid;
            $model->save();
        }

        $this->addMessage('state', 'success');
        $this->addMessage('message', '保存成功');
        $this->showMessage();
    }

    // 获取指定学年学校的预算详情
    public function actionGetBudget()
    {
        Yii::import('common.models.calendar.*');

        $schoolid = Yii::app()->request->getParam('schoolid');
        $yid = Yii::app()->request->getParam('yid');

        if (!$schoolid) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '学校不能为空');
            $this->showMessage();
        }

        // 取所有校历
        $schoolyear = array();
        $criteria = new CDbCriteria();
        $criteria->compare('branchid', $schoolid);
        $criteria->order='startyear asc';
        $years = CalendarSchool::model()->findAll($criteria);
        foreach($years as $year){
            $schoolyear[$year->yid] = $year->startyear;
        }
        if (!$yid) {
            // 取学校当前校历
            $yid = CalendarSchool::model()->getCurrentSchoolYearCalendar($schoolid);
        }

        // 获取校历月份
        $schoolDays = CalendarSchoolDays::model()->getCalendarSchooldays($yid);
        $months = array_unique(array_keys($schoolDays[$yid]['month']));
        asort($months);

        // 获取班级列表
        $classList = IvyClass::getClassList($schoolid, $yid);

        // 获取已保存的数据
        $crit = new CDbCriteria();
        $crit->compare('schoolid', $schoolid);
        $crit->compare('yid', $yid);
        $crit->compare('status', 1);
        $budgets = CampusBudget::model()->findAll($crit);

        $budgetsArr = array();
        foreach ($budgets as $budget) {
            $budgetsArr[$budget->classid][$budget->month] = $budget->num;
        }
        
        $data = array(
            'schoolyear' => $schoolyear,
            'yid' => $yid,
            'month' => $months,
            'monthTotal' => array(),
            'items' => array(),
            'classes' => array(),
        );
        foreach ($classList as $class) {
            $class->classid = (string) $class->classid;
            $data['classes'][] = $class->classid;
            foreach ($months as $month) {
                $data['items'][$class->classid]['month'][$month] = 0;
                $data['items'][$class->classid]['title'] = $class->title;
                if (isset($budgetsArr[$class->classid][$month])) {
                    $data['monthTotal'][$month] += $budgetsArr[$class->classid][$month];
                    $data['items'][$class->classid]['month'][$month] = $budgetsArr[$class->classid][$month];
                }
            }
        }
        $this->addMessage('state', 'success');
        $this->addMessage('data', $data);
        $this->showMessage();
    }

    // 折扣报表
    public function actionDiscountReport()
    {
        $cs = Yii::app()->clientScript;
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile($cs->getCoreScriptUrl().'/jui/js/jquery-ui.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/echarts.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        
        $branchList = Branch::model()->getBranchList(null, true);
        $newBranchList = array();
        foreach ($branchList as $branchId => $branch) {
            $newBranchList[$branch['group']][$branchId] = $branch;
        }
        ksort($newBranchList);
        $typeList = array(
            10 => Yii::t('campus', 'Ivy Academy'),
            20 => Yii::t('campus', 'Ivy Bilingual School'),
            30 => Yii::t('campus', 'Ivy MI Kindergarten'),
            50 => Yii::t('campus', 'Daystar Academy'),
        );
        $date = Yii::app()->request->getParam('date', date('Y-m-d'));
        
        $this->render('discountreport', array(
            'branchList' => $newBranchList,
            'date' => $date,
            'typeList' => $typeList,
        ));
    }

    // 获取折扣报表信息
    public function actionGetDiscount()
    {
        $targetDate = Yii::app()->request->getParam('date');
        $schoolid = Yii::app()->request->getParam('schoolid', 'BJ_DS');
        $timestamp = strtotime($targetDate) ? strtotime($targetDate) : time();
        $this->addMessage('state', 'fail');
        $data = array(
            'total_num' => 0,
            'total_amount' => 0,
            'discount_num' => 0,
            'undiscount_num' => 0,
            'discount_original_amount' => 0,
            'discount_actual_amount' => 0,
            'discount_items' => array()
        );
        if (in_array($schoolid, $this->accessBranch)) {
            // -- 1 计算指定日期内账单数，实际账单总额
            $result = Yii::app()->db->createCommand()
                          ->select('count(t.invoice_id) AS invoice_num, SUM(a.original_amount) AS total_amount')
                          ->from('ivy_child_service_info AS t')
                          ->join('ivy_invoice_invoice AS a', 't.invoice_id = a.invoice_id')
                          ->where("t.startdate <= :timestamp AND t.enddate >= :timestamp AND t.schoolid = :branchId AND t.payment_type = 'tuition'", [':timestamp' => $timestamp, ':branchId' => $schoolid])
                          ->queryRow();
            if (count($result) > 0) {
                $data['total_num'] = $result['invoice_num'];
                $data['total_amount'] = $result['total_amount'];
            }
            // -- 2 计算指定日期内折扣账单数，折扣前账单总额，折扣后账单总额
            $result2 = Yii::app()->db->createCommand()
                          ->select('count(t.invoice_id) AS discount_invoice_num, SUM(a.nodiscount_amount) AS nodiscount_total_amount, SUM(a.original_amount) AS discount_total_amount')
                          ->from('ivy_child_service_info AS t')
                          ->join('ivy_invoice_invoice AS a', 't.invoice_id = a.invoice_id')
                          ->where("t.startdate <= :timestamp AND t.enddate >= :timestamp AND t.schoolid = :branchId AND t.payment_type = 'tuition' AND a.discount_id > 0", [':timestamp' => $timestamp, ':branchId' => $schoolid])
                          ->queryRow();
           if (count($result2) > 0) {
                $data['discount_num'] = $result2['discount_invoice_num'];
                $data['discount_original_amount'] = $result2['nodiscount_total_amount'] ? $result2['nodiscount_total_amount'] : 0;
                $data['discount_actual_amount'] = $result2['discount_total_amount'] ? $result2['discount_total_amount'] : 0;
            }
            // -- 3 计算指定日期分类折扣统计信息
            $result3 = Yii::app()->db->createCommand()
                          ->select('a.discount_id AS discount_id, count(t.invoice_id) AS discount_invoice_num, SUM(a.nodiscount_amount) AS nodiscount_total_amount, SUM(a.original_amount) AS discount_total_amount')
                          ->from('ivy_child_service_info AS t')
                          ->join('ivy_invoice_invoice AS a', 't.invoice_id = a.invoice_id')
                          ->where("t.startdate <= :timestamp AND t.enddate >= :timestamp AND t.schoolid = :branchId AND t.payment_type = 'tuition' AND a.discount_id > 0", [':timestamp' => $timestamp, ':branchId' => $schoolid])
                          ->group('a.discount_id')
                          ->queryAll();

            foreach ($result3 as $item) {
                $data['discount_items'][$item['discount_id']]['num'] = $item['discount_invoice_num'];
                $data['discount_items'][$item['discount_id']]['original_amount'] = $item['nodiscount_total_amount'];
                $data['discount_items'][$item['discount_id']]['actual_amount'] = $item['discount_total_amount'];
            }
            $discountIds = array_keys($data['discount_items']);
            Yii::import('common.models.invoice.*');
            $discounts = DiscountSchool::model()->findAllByPk($discountIds, array('with'=>'discountTitle'));
            foreach ($discounts as $discount) {
                $data['discount_items'][$discount->id]['name'] = $discount->discountTitle->title_cn;
                // $data['discount_items'][$discount->id]['discount_discount'] = round($discount->discount,2);
            }
            $data['undiscount_num'] = $data['total_num'] - $data['discount_num'];

            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
            $this->addMessage('message', 'success');
            $this->showMessage();
        }
        $this->addMessage('message', '学校错误');
        $this->showMessage();
    }
    
}
