<?php

/**
 * This is the model class for table "ivy_visits_basic_info_bak".
 *
 * The followings are the available columns in table 'ivy_visits_basic_info_bak':
 * @property integer $id
 * @property string $child_name
 * @property string $parent_name
 * @property integer $birth_timestamp
 * @property string $tel
 * @property string $email
 * @property string $address
 * @property integer $communication
 * @property integer $child_enroll
 * @property integer $country
 * @property integer $knowus
 * @property string $concerns
 * @property string $memo
 * @property integer $register_timestamp
 * @property integer $status
 * @property integer $childid
 * @property integer $update_timestamp
 * @property integer $update_user
 */
class IvyVisitsBasicInfoBak extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return IvyVisitsBasicInfoBak the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_visits_basic_info_bak';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('register_timestamp', 'required'),
			array('birth_timestamp, communication, child_enroll, country, knowus, register_timestamp, status, childid, update_timestamp, update_user', 'numerical', 'integerOnly'=>true),
			array('child_name, parent_name, email, address, concerns', 'length', 'max'=>255),
			array('tel', 'length', 'max'=>25),
			array('memo', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, child_name, parent_name, birth_timestamp, tel, email, address, communication, child_enroll, country, knowus, concerns, memo, register_timestamp, status, childid, update_timestamp, update_user', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'child_name' => 'Child Name',
			'parent_name' => 'Parent Name',
			'birth_timestamp' => 'Birth Timestamp',
			'tel' => 'Tel',
			'email' => 'Email',
			'address' => 'Address',
			'communication' => 'Communication',
			'child_enroll' => 'Child Enroll',
			'country' => 'Country',
			'knowus' => 'Knowus',
			'concerns' => 'Concerns',
			'memo' => 'Memo',
			'register_timestamp' => 'Register Timestamp',
			'status' => 'Status',
			'childid' => 'Childid',
			'update_timestamp' => 'Update Timestamp',
			'update_user' => 'Update User',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('child_name',$this->child_name,true);
		$criteria->compare('parent_name',$this->parent_name,true);
		$criteria->compare('birth_timestamp',$this->birth_timestamp);
		$criteria->compare('tel',$this->tel,true);
		$criteria->compare('email',$this->email,true);
		$criteria->compare('address',$this->address,true);
		$criteria->compare('communication',$this->communication);
		$criteria->compare('child_enroll',$this->child_enroll);
		$criteria->compare('country',$this->country);
		$criteria->compare('knowus',$this->knowus);
		$criteria->compare('concerns',$this->concerns,true);
		$criteria->compare('memo',$this->memo,true);
		$criteria->compare('register_timestamp',$this->register_timestamp);
		$criteria->compare('status',$this->status);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('update_timestamp',$this->update_timestamp);
		$criteria->compare('update_user',$this->update_user);

		return new CActiveDataProvider(get_class($this), array(
			'criteria'=>$criteria,
		));
	}
}