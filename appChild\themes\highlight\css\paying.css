
.bankinfo {
    /*
     *
     border-bottom: 1px solid #CCCCCC;
     border-left: 1px solid #CCCCCC;
     border-right: 1px solid #CCCCCC;
     width: 650px;
     */
    padding-top: 5px;
    padding-bottom: 5px;
}
.bankinfo .ui-list-icons input {
    vertical-align: middle;
}
.bankinfo img {
    vertical-align: middle;
}
.bankinfo .ui-list-icons li {
    float: left;
    margin: 3px;
    position: relative;
    width: 182px;
    height: 33px;
}
.bankinfo .icon-box {
    background-color: #EEEEEE;
    border: 1px solid #DDDDDD;
    display: inline-block;
    position: relative;
    vertical-align: middle;
    width: 154px;
}
.bankinfo .current {
    border-color: #FF6600 !important;
}
.bankinfo li .shuhu {
    position: absolute;
    display: block;
    width: 155px;
    height: 34px;
    top: 0;
    left: 0;
    filter: alpha(opacity = 0);
    background-color: #fff;
}
.bankinfo li span {
    margin: 20px;
    width: 155px;
}
.bankinfo h3{
	margin: 0.8em 0 0.5em;
}
.bank-list:before{
    clear:both;
}
.bank-list ul li{
    float: left;
    width: 200px;
    margin-bottom: 2px;
}
span.faq{
	display: inline-block;
	height:64px;
    margin: 0 0 10px 10px;
}
span.faq em{
	display: inline-block;
	width:64px;
	height:64px;
	float: left;
	background: url(../images/icons/faq_bg.png) no-repeat left -64px transparent;
	padding-right: 10px;
}
a span.faq:hover em{
	background-position: left top;
}

span.faq span{
	line-height: 64px;
	font-size:18px;
}
a span.faq span:hover{
	color:#FF6200;
	text-decoration: underline;
}
.operateCol_div{
    margin: 1em 0;
}

.onlinenotice{
    padding: 20px;
}
.onlinenotice .partners{
	text-align: left;
    border-bottom: 2px solid #078D00;
    margin-bottom: 1em;
}
.onlinenotice .banklist li{
    display: inline;
    margin: 2px;
}