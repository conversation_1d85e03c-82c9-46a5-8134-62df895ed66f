<?php

/**
 * This is the model class for table "ivy_asa_course_follower".
 *
 * The followings are the available columns in table 'ivy_asa_course_follower':
 * @property integer $id
 * @property integer $group_id
 * @property integer $course_id
 * @property integer $childid
 * @property integer $userid
 * @property integer $status
 * @property integer $updated
 */
class AsaCourseFollower extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_asa_course_follower';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('group_id, course_id, childid, userid, status, updated', 'required'),
			array('group_id, course_id, childid, userid, status, updated', 'numerical', 'integerOnly'=>true),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, group_id, course_id, childid, userid, status, updated', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'child' => array(self::HAS_ONE, 'ChildProfileBasic', array('childid'=>'childid')),
			'user' => array(self::HAS_ONE, 'User', array('uid'=>'userid')),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'group_id' => 'Group',
			'course_id' => 'Course',
			'childid' => 'Childid',
			'userid' => 'Userid',
			'status' => 'Status',
			'updated' => 'Updated',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('group_id',$this->group_id);
		$criteria->compare('course_id',$this->course_id);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('userid',$this->userid);
		$criteria->compare('status',$this->status);
		$criteria->compare('updated',$this->updated);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AsaCourseFollower the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
