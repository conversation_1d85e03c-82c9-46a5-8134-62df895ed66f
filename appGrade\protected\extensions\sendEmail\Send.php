<?php

/*
 * 结构参见 http://www.cnblogs.com/davidhhuan/archive/2012/01/09/2316851.html
 */

class Send extends CWidget {
    /*
     * 资源(images css js)的主目录
     */

    public $assetsHomeUrl = null;
    /*
     * 脚本文件 true 为默认 null 为不加载脚本文件
     */
    public $scriptFile = true;
    /*
     * css 文件 true 为默认 null 为不加载css文件
     */
    public $cssFile = true;
    /*
     * 视图文件名称
     */
    public $viewName = '';

    /**
     * 邮件的语言
     * true 中英文都显示 false 当前语言环境决定 cn 中文 en 英文
     */
    public $lang = true;

    /**
     * 发送至当事人（找回密码）
     */
    public $toParty = false;

    /**
     * 发送至父母双方
     */
    public $toParent = false;

    /**
     * 发送至 support
     */
    public $toSupport = false;

    /**
     * 抄送至配偶
     */
    public $ccSpouse = false;

    /**
     * 抄送至父母双方
     */
    public $ccParent = false;

    /**
     * 抄送至 support
     */
    public $ccSupport = false;

    /**
     * 密抄至it-dev
     */
    public $bccItDev = false;

    /**
     * 回复至 校园支持
     */
    public $replyToSupport = false;
    //  -----------------------------//-----------------------------//-----------------------------
    /**
     * 发送成功标识
     */
    private $sendSuccess = false;

    /**
     * 邮件的model
     */
    private $emailModel = null;

    /**
     * 邮件发送器
     */
    private $emailer = null;

    /**
     * 自定义参数
     */
    public $params = array();

    /*
     * 初始化 设定属性的默认值
     * 此方法会被 CController::beginWidget() 调用
     */

    public function init() {

        /*         * *
          if(empty($this->assetsHomeUrl)){
          $this->assetsHomeUrl = Yii::app()->getAssetManager()->publish(dirname(__FILE__).'/assets',false, -1, YII_DEBUG);
          }

          $cs = Yii::app()->getClientScript();

          if($this->scriptFile == true){
          $this->scriptFile = '';
          }
          if($this->cssFile == true){
          $this->cssFile = '';
          }
          if(!empty($this->scriptFile)){
          $cs->registerScriptFile($this->assetsHomeUrl.'/js/'.$this->scriptFile,CClientScript::POS_HEAD);
          }
          if(!empty($this->cssFile)){
          $cs->registerCssFile($this->assetsHomeUrl.'/css/'.$this->cssFile);
          }
         * * */

        if (false === $this->lang) {
            $this->lang = Yii::app()->language == 'en_us' ? 'en' : 'cn';
        }

//        $this->emailer = Yii::createComponent('application.extensions.mailer.EMailer');
        $this->emailer = Yii::createComponent('common.extensions.mailer.EMailer');

        $this->emailModel = new IvyMail();
        $this->emailModel->continue = 1;
    }

    /*
     * 此方法会被 CController::endWidget() 调用
     */

    public function run() {

        if (!empty($this->params['subject'])) {
            $subject = $this->params['subject'];
        } else {
            // 输出 变量到 视图文件
            $subject = $this->render($this->viewName . 'Subject', $this->params, true);
        }
        $this->params['subjectFormat'] = $subject;
        $this->emailer->Subject = $subject;
        $this->emailModel->subject = $subject;

        // 如以后有托管校园 需在branch info 表中加一字段以区分ivy校园
        // 例如 contactCampus 托管校园的模板为 contactCampusXXX XXX为托管校园的ID
        // 输出 变量到 视图文件

        if (!empty($this->params['content']) && empty($this->params['useStyle'])) {
            $content = $this->params['content'];
        } else {
            //$content = $this->render($this->viewName, $this->params, true);
            $this->params['viewName'] = $this->viewName;
            $content = $this->render(Mims::unIvy()?'mailTemplateUnivy':'mailTemplate', array('params' => $this->params), true);
        }

        $this->emailer->MsgHTML($content); //echo $content; die;
        $this->emailModel->content = $content;

        $this->emailModel->updated_timestamp = time();
        if (isset(Yii::app()->user->id)) {
            $this->emailModel->userid = Yii::app()->user->id;
        } else {
            if (isset($this->params['user_id'])) {
                $this->emailModel->userid = $this->params['user_id'];
            }
        }

        $this->initEmail();

        if ($this->emailModel->save()) {

            if ($this->emailer->Send()) {
                $this->sendSuccess = true;
                $this->emailModel->saveAttributes(array('status' => 1));
            }
        } else {
            print_r($this->emailModel->getErrors());
        }
    }

    public function initEmail() {

        if (1 || 'production' != Yii::app()->params['onlineBanking']) {
            $this->emailer->IsSMTP();
            $this->emailer->SMTPAuth = true;
            $this->emailer->Host = 'smtp.office365.com';
            $this->emailer->Port = 25;
            $this->emailer->Username = '<EMAIL>';
            $this->emailer->Password = 'Ds12345!';

            $this->emailer->Sender = '<EMAIL>';
            $this->emailer->From = '<EMAIL>';
            $this->emailer->FromName = 'DsOnline';
        } else {
            $this->emailer->IsSendmail();
            $this->emailer->Host = 'localhost';
            $this->emailer->SetFrom('<EMAIL>', 'IvyOnline - 艾毅在线');
        }

        $this->emailer->CharSet = 'UTF-8';
        $this->emailer->IsHTML(true);
        $show_html_for_test = '<br/>发送、抄送等邮件地址信息。只在测试邮件中可见：';
        if (true == $this->toParty) {
            $this->emailer->AddAddress($this->params['party_email']);
            $this->emailModel->to = $this->params['party_email'];
            $show_html_for_test .= '<br/>To:' . $this->params['party_email'];
        }

        if (true == $this->ccSpouse || true == $this->toParent || true == $this->ccParent) {

            $childInfo = $this->getController()->myChildObjs[$this->getController()->getChildId()];
            if (!empty($childInfo->fid)) {
                $parentIds[$childInfo->fid] = $childInfo->fid;
            }
            if (!empty($childInfo->mid)) {
                $parentIds[$childInfo->mid] = $childInfo->mid;
            }

            if (!empty($parentIds)) {
                Yii::import('common.models.*');
                $user_cri = new CDbCriteria();
                $user_cri->compare('uid', $parentIds);
                $userInfo = User::model()->findAll($user_cri);
                //$userInfo = User::model()->findAllByPk($parentIds);
                $ppEmails = null;
                $spouse_email = null;
                if (!empty($userInfo)) {
                    foreach ($userInfo as $user) {
                        if (!empty($user->email)) {
                            if (true == $this->toParent) {
                                $this->emailer->AddAddress($user->email);
                                $ppEmails[] = $user->email;
                                $show_html_for_test .= '<br/>To:' . $user->email;
                            } else if (true == $this->ccParent) {
                                $this->emailer->AddCC($user->email);
                                $ppEmails[] = $user->email;
                                $show_html_for_test .= '<br/>Cc:' . $user->email;
                            } else if (true == $this->ccSpouse) {
                                if (strtolower($user->email) != strtolower($this->params['party_email'])) {
                                    $this->emailer->AddCC($user->email);
                                    $spouse_email = $user->email;
                                    $show_html_for_test .= '<br/>Cc:' . $user->email;
                                }
                            }
                        }
                    }
                }
                $ppEmailString = null;
                if (count($ppEmails) > 1) {
                    $ppEmailString = implode(";", $ppEmails);
                } else if (!empty($ppEmails)) {
                    $ppEmailString = current($ppEmails);
                }
                if (true == $this->toParent) {
                    $this->emailModel->to = $ppEmailString;
                } else if (true == $this->ccParent) {
                    $this->emailModel->cc = $ppEmailString;
                } else if (true == $this->ccSpouse) {
                    $this->emailModel->cc = $spouse_email;
                }
            }
        }
        // 如果不 抄送给 父母双方 并且 有 抄送参数（校园支持就是这种方式）
        if (false == $this->ccParent && !empty($this->params['cc'])) {
            $this->emailModel->cc = $this->params['cc'];
            $ccs = explode(';', $this->params['cc']);
            foreach ($ccs as $cc) {
                if (!empty($cc)) {
                    $this->emailer->AddCC($cc);
                    $show_html_for_test .= '<br/>Cc:' . $cc;
                }
            }
        }

        if (true == $this->bccItDev) {
            $this->emailer->AddBCC('<EMAIL>');
            $this->emailModel->bcc = '<EMAIL>';
            $show_html_for_test .= '<br/>Bcc:<EMAIL>';
        }

        if (true == $this->toSupport) {
            $support_email = $this->params['support_email'];
            if (empty($support_email)) {
                $support_email = '<EMAIL>';
            }
            if(is_array($support_email)){
                foreach($support_email as  $items){
                    $this->emailer->AddAddress($items);
                    $this->emailModel->to = $items;
                    $show_html_for_test .= '<br/>To:' . $items;
                }
            }else{
                $this->emailer->AddAddress($support_email);
                $this->emailModel->to = $support_email;
                $show_html_for_test .= '<br/>To:' . $support_email;
            }
        }

        if (true == $this->ccSupport) {
            $support_email = $this->params['support_email'];
            if (empty($support_email)) {
                $support_email = '<EMAIL>';
            }
            $this->emailer->AddCC($support_email);
            $this->emailModel->cc = $support_email;
            $show_html_for_test .= '<br/>Cc:' . $support_email;
        }
        if (true == $this->replyToSupport) {
            $support_email = $this->params['support_email'];
            if (empty($support_email)) {
                $support_email = '<EMAIL>';
            }
            $this->emailer->AddReplyTo($support_email);
            $this->emailModel->replyto = $support_email;
            $show_html_for_test .= '<br/>Replyto:' . $support_email;
        }

        if ('production' != Yii::app()->params['onlineBanking']) {

            $this->emailer->ClearAddresses();
            $this->emailer->ClearCCs();
            $this->emailer->ClearBCCs();
            $this->emailer->ClearReplyTos();

//            $this->emailer->AddAddress("<EMAIL>", "IVY IT");
//            $this->emailer->AddAddress("<EMAIL>", "IVY IT");
//            $this->emailer->AddAddress("<EMAIL>", "IVY IT");
//            $this->emailer->AddAddress("<EMAIL>", "IVY IT");
            $this->emailer->AddAddress("<EMAIL>", "IVY IT");

            $this->emailer->Body .= $show_html_for_test;
        }
    }

    public function getSendFlag() {
        return $this->sendSuccess;
    }

}