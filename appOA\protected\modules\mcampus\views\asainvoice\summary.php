<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('//mcampus/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Basic'), array('//mcampus/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site', '课后课管理'), array('//mcampus/asainvoice/index')) ?></li>
        <li class="active">课后课账单</li>
    </ol>

    <div class="row">
        <!-- 左侧菜单 -->
        <div class="col-md-2 col-sm-2 mb10">
            <?php
            $this->widget('zii.widgets.CMenu',array(
                'items'=> $this->getMenu(),
                'id'=>'pageCategory',
                'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked background-gray'),
                'activeCssClass'=>'active',
                'itemCssClass'=>''
            ));
            ?>
        </div>

        <div class="col-md-10 col-sm-12">
            <!--<div class="mb10 col-sm-1 row">
                <a href="<php /*echo $this->createUrl('exportAdmissions', array("childid"=>"")) */?>" class="btn btn-info" target="_blank"><?php /*echo Yii::t('user', 'Export');*/?></a>
            </div>-->
            <div class="mb10 row">
                <!-- 搜索框 -->
                <form  class="" style="float: left;width: 100%" action="<?php echo $this->createUrl('summary'); ?>" method="get">
                    <?php echo Chtml::hiddenField('branchId',$this->branchId); ?>
                    <div class="col-sm-2 form-group">
                        <input type="text" class="form-control" name="username" placeholder="孩子姓名" value="<?php echo Yii::app()->request->getParam('username','')?Yii::app()->request->getParam('username',''):''; ?>">
                    </div>
                    <div class="col-sm-2 form-group">
                        <?php echo Chtml::dropDownList('status', $_GET['status'], $grouplist,array('class'=>'form-control')); ?>
                    </div>
                    <div class="col-sm-2 form-group">
                        <?php echo Chtml::dropDownList('paytype', $_GET['paytype'], array('1'=>'现金','2'=>'微信'),array('class'=>'form-control',  'empty' => Yii::t('teaching', '支付方式'))); ?>
                    </div>
                    <div class="col-sm-2 form-group">
                        <?php echo Chtml::dropDownList('instatus', $_GET['instatus'], array('10'=>'未付款','20'=>'款付清','99'=>'作废'),array('class'=>'form-control',  'empty' => Yii::t('teaching', '支付状态'))); ?>
                    </div>
                    <div class="">
                        <!-- 入学时间 -->
                        <div class="col-sm-2" style="float: left;">
                            <input type="text" class="form-control" id="datepicker" placeholder="支付时间"   name="times" value="<?php echo Yii::app()->request->getParam('times','')?Yii::app()->request->getParam('times',''):''; ?>">
                        </div>
                        <!-- 内容匹配 -->
                        <div class="">
                            <button class="btn btn-default ml5" type="submit"><span class="glyphicon glyphicon-search"> </span> </button>
                        </div>
                    </div>
                </form>
            </div>

            <div class="col-md-12"></div>
            <div class="panel panel-default">
                <div class="panel-body">
                    <?php
                    $this->widget('ext.ivyCGridView.BsCGridView', array(
                        'id'=>'confirmed-visit-grid',
                        'afterAjaxUpdate'=>'js:head.Util.modal',
                        'dataProvider'=>$dataProvider,
                        'template'=>"{items}{pager}",
                        'colgroups'=>array(
                            array(
                                "colwidth"=>array(200,100,100,100,100,100,100,100,200),
                            )
                        ),
                        'columns'=>array(
                            array(
                                'name'=>Yii::t('site','名字和课程'),
                                'type'=>'raw',
                                'value'=>'$data->asainvoice->title',
                            ),
                            array(
                                'name'=>Yii::t('site','原始金额'),
                                'type'=>'raw',
                                'value'=>'$data->asainvoice->amount_original',
                            ),
                            array(
                                'name'=>Yii::t('site','实付金额'),
                                'type'=>'raw',
                                'value'=>'$data->asainvoice->amount_actual',
                            ),
                            array(
                                'name'=>Yii::t('site','支付方式'),
                                'type'=>'raw',
                                'value'=>'($data->asainvoice->pay_type == 1) ? "现金" : "微信"',
                            ),
                            array(
                                'name'=>Yii::t('site','支付状态'),
                                'type'=>'raw',
                                'value' => array($this, 'getTeacher'),
                            ),
                            /*array(
                                'name'  =>'start_grade',
                                'type'=>'raw',
                                'value'=> ($this->branchId == "BJ_IASLT") ? '$data->getCatename("grade_ayalt")': '$data->getCatename("grade")',
                            ),*/
                            array(
                                'name'=>'更新时间',
                                'type'=>'raw',
                                'value'=>'date("Y-m-d H:i", $data->asainvoice->updated)',
                            ),

                        ),
                    ));
                    ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>

    $('#datepicker').datepicker({
        changeMonth: true,
        changeYear: true,
        dateFormat:'yy-mm-dd'
    });

</script>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>