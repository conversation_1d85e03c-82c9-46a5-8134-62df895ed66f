<?php

$nterview = array(
    AdmissionsDs::STATS__INTERVIEW_PASS,
    AdmissionsDs::STATS__TRANSFER_CHILD
);//面试

$interview_results = array(
    AdmissionsDs::STATS_HAS_BEEN_PAID,
    AdmissionsDs::STATS__INTERVIEW_PASS,
    AdmissionsDs::STATS__INTERVIEW_OUT,
    AdmissionsDs::STATS__INTERVIEW_ALTERNATE,
    AdmissionsDs::STATS__TRANSFER_CHILD,
);

$results = array(
    AdmissionsDs::STATS__INTERVIEW_PASS,
    AdmissionsDs::STATS__TRANSFER_CHILD,
);


if($child->child_avatar){
    $oss = CommonUtils::initOSSCS('private');
    $object = 'admissions/' . $child->child_avatar;
    $style = 'style/w80';
    $child_photo = $oss->getImageUrl($object, $style);
}

?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site','Campus Support'), array('//mcampus/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site', '招生管理'), array('//mcampus/admissions/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', '入学申请'), array('//mcampus/admissions/admissions')) ?></li>
        <li><?php echo $child->getName() ?></li>
    </ol>
    <div class="row">
        <!-- 左侧菜单 -->
        <div class="col-md-2 col-sm-2 mb10">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->getMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                'activeCssClass' => 'active',
                'itemCssClass' => ''
            ));
            ?>
        </div>

        <div class="col-md-10 col-sm-20">
            <div class="panel panel-default">
                <div class="panel-body">
                    <div class="row text-center">
                        <div class="col-lg-1 col-md-2">
                            <img src="<?php echo $child_photo; ?>" class="img-rounded" style="width:80px;height: 80px;"/>
                        </div>
                        <ul class="col-md-10 text-left list-unstyled">
                            <li class="p5"><?php echo  Yii::t('user','Name') ?>:<?php echo $child->getName() ?></li>
                            <li class="p5"><?php echo  Yii::t('user','Gender') ?>:<?php echo ($child->gender == 1) ? Yii::t('child','Boy') : Yii::t('child','Girl'); ?></li>
                            <li class="p5"><?php echo  Yii::t('user','Date Of Bith') ?>:<?php echo date("Y-m-d", $child->birthday) ?></li>
                        </ul>
                    </div>
                    <div class="
                    <?php if ($child->status == AdmissionsDs::STATS__TRANSFER_CHILD ) {
                                echo "bg-success";
                            }else{
                                echo "bg-warning";
                            }
                    ?> p10 mb15">

                        <h3> 1. <?php echo  Yii::t('management','Arrange interview') ?>
                            <div class="pull-right mr15">
                                <button type="button" class="btn btn-primary" data-toggle="modal" data-target='#modal3' data-backdrop="static"
                                    <?php if ($child->status == AdmissionsDs::STATS__TRANSFER_CHILD ) {
                                            echo "disabled";
                                        } ?>
                                    >
                                    <?php echo Yii::t('interview','Evaluation Date')?>
                                </button>
                                <?php if($childinterview): ?>
                                    <?php if ($child->status != AdmissionsDs::STATS__TRANSFER_CHILD ) {
                                        echo CHtml::link(Yii::t('interview','取消面试'), array('cancelInterview', 'interviewId' => $childinterview->id), array('class' => 'J_ajax_del btn btn-primary', 'data-msg' => "取消面试,面试结果也会取消，确定取消面试吗？"));
                                    } else{?>
                                        <button type="button" disabled="disabled" class="btn btn-primary"><?php echo  Yii::t('interview','取消面试')?></button>
                                    <?php }?>
                                <?php endif; ?>
                            </div>
                        </h3>
                        <li class="list-unstyled p10">
                        <?php if($childinterview):?>
                            <table class="table">
                                <thead>
                                <tr>
                                    <th style="width:136px"><?php echo  Yii::t('management','日期') ?></th>
                                    <th style="width:136px"><?php echo  Yii::t('management','时间') ?></th>
                                </tr>
                                </thead>
                                    <tr>
                                        <td><?php echo date('Y-m-d',$childinterview->interview_time) ?></td>
                                        <td><?php echo date('H:i',$childinterview->interview_time) ?></td>
                                    </tr>
                            </table>
                            <?php else: ?>
                                <div><?php echo  Yii::t('management','No interview arrangement yet') ?></div>
                            <?php endif;?>
                        </li>
                    </div>

                    <div class="
                        <?php if ($child->status == AdmissionsDs::STATS__TRANSFER_CHILD ) {
                            echo "bg-success";
                        }else{
                            echo "bg-warning";
                        }
                    ?> bg-success  p10 mb15">

                        <h3>2. <?php echo Yii::t('management', 'Application Fee') ?>
                            <div class="pull-right mr15">
                                <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#modal" data-backdrop="static"
                                    <?php if ($child->status == AdmissionsDs::STATS__TRANSFER_CHILD ) {
                                            echo "disabled";
                                        } ?>
                                    >
                                    <?php echo  Yii::t('child','Create Invoice') ?>
                                </button>
                            </div>
                        </h3>

                        <?php if ($child_Invoice) { ?>
                            <table class="table">
                                <thead>
                                <tr>
                                    <th style="width:136px"><?php echo  Yii::t('invoice','Invoice Title') ?></th>
                                    <th style="width:136px"><?php echo  Yii::t('payment','Invoice Amount') ?></th>
                                    <th style="width:300px"><?php echo  Yii::t("payment", 'Due Date') ?></th>
                                </tr>
                                </thead>
                                <tbody>
                                <?php foreach ($child_Invoice as $_invoice) { ?>
                                    <tr>
                                        <td><?php echo $_invoice->title; ?></td>
                                        <td><?php echo $_invoice->original_amount; ?></td>
                                        <td><?php echo date("Y-m-d", $_invoice->duetime); ?></td>
                                    </tr>
                                <?php } ?>
                                </tbody>
                            </table>
                        <?php } else { ?>
                            <li class="list-unstyled p10"><?php echo  Yii::t('management','No billing') ?></li>
                        <?php } ?>
                    </div>

                    <div class="
                   <?php if ($child->status == AdmissionsDs::STATS__TRANSFER_CHILD ) {
                        echo "bg-success";
                    }else{
                        echo "bg-warning";
                    }
                    ?> p10 mb15">
                        <h3>3. <?php echo  Yii::t('management','Status of Application Fee Payment') ?>
                            <div class="pull-right mr15">
                                <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#modal2" data-backdrop="static">
                                    <?php echo  Yii::t('management','Please make payment') ?>
                                </button>
                            </div>
                        </h3>
                        <?php if ($child_Invoice) { ?>
                            <table class="table">
                                <thead>
                                <tr>
                                    <th width="385"><?php echo  Yii::t('invoice','Invoice Title') ?></th>
                                    <th width="400"><?php echo  Yii::t('payment','Invoice Amount') ?></th>
                                    <th width="400"><?php echo  Yii::t("payment", 'Due Date') ?></th>
                                    <th width="400"><?php echo  Yii::t('campus','Invoice Status') ?></th>
                                </tr>
                                </thead>
                                <tbody>
                                <?php foreach ($child_Invoice as $_invoice) { ?>
                                    <tr>
                                        <td><?php echo $_invoice->title; ?></td>
                                        <td><?php echo $_invoice->original_amount; ?></td>
                                        <td><?php echo date("Y-m-d", $_invoice->duetime); ?></td>
                                        <?php if ($_invoice->status == Invoice::STATS_PAID) { ?>
                                            <td><?php echo  Yii::t('payment','Paid') ?></td>
                                        <?php } else { ?>
                                            <td><?php echo  Yii::t('invoice','Unpaid') ?></td>
                                        <?php } ?>
                                    </tr>
                                <?php } ?>
                                </tbody>
                            </table>
                        <?php } ?>
                    </div>

                    <div class="
                    <?php if ($child->status == AdmissionsDs::STATS__TRANSFER_CHILD ) {
                        echo "bg-success";
                    }else{
                        echo "bg-warning";
                    }
                    ?> p10 mb15">

                        <h3>4. <?php echo  Yii::t('management','Assessment Result') ?>
                            <div class="pull-right mr15">
                                <button type="button" class="btn btn-primary" data-toggle="modal" data-target='#modal4' data-backdrop="static">
                                    <?php echo  Yii::t('management','Check the interview result') ?>
                                </button>
                            </div>
                        </h3>

                        <li class="list-unstyled p10">
                            <?php if ($child){
                                echo "<p>";
                                if($child->status == '40'){
                                    echo Yii::t('management','Interview passed');
                                } else if($child->status == '41'){
                                    echo Yii::t('management','Interview failed');
                                } else if($child->status == '42'){
                                    echo Yii::t('management','候补学生');
                                } else {
                                    echo Yii::t('management','No interview results yet');
                                }
                                echo "</p>";
                            } ?>
                        </li>
                    </div>


                    <div class="
                    <?php if ($child->status == AdmissionsDs::STATS__TRANSFER_CHILD ) {
                        echo "bg-success";
                    }else{
                        echo "bg-warning";
                    }
                    ?> p10">
                        <h3>5. <?php echo Yii::t('management','Acceptance');?>

                            <div class="pull-right mr15">
                                <a class="J_ajax_del btn btn-primary" href="<?php echo $this->createUrl('deleteChild', array('childid' => $child->id)) ?>"><?php echo Yii::t('management','录取不来');?></a>
                                <button type="button" class="btn btn-primary" data-toggle="modal" data-target='#modal5' data-backdrop="static"
                                    <?php if ($child->status == AdmissionsDs::STATS__TRANSFER_CHILD ) {
                                        echo "disabled";
                                    } ?>
                                    >
                                    <?php echo Yii::t('management','Admission in progress');?>
                                </button>
                            </div>
                        </h3>

                        <li class="list-unstyled p10">
                            <?php if ($child): ?>
                                <?php
                                if($child->status == AdmissionsDs::STATS__STATS_NOTCOMING){
                                    echo '录取不来';
                                }
                                ?>
                                <p>
                                    <?php echo ($child->status == AdmissionsDs::STATS__TRANSFER_CHILD ) ? Yii::t('management','学生已经转移到正式学生中'): ""; ?>
                                </p>
                            <?php endif; ?>
                        </li>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
<!-- 模态框1 安排面试
<php if (!in_array($child->status,$nterview)):?> -->
<?php if ($child->status != AdmissionsDs::STATS__TRANSFER_CHILD ):?>
<div class="modal fade" id="modal3" tabindex="-1" role="dialog" aria-labelledby="modal">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">×</span></button>
                <h4 class="modal-title"><?php echo Yii::t('management','Arrange interview'); ?></h4>
            </div>
            <?php $this->renderPartial("admissions/interview", array('child' => $child)); ?>
        </div>
    </div>
</div>
<?php  endif; ?>
<!-- 模态框w  发送费用邮件
<php if ($child->status == AdmissionsDs::STATS_BILLING):?>-->
<?php if ($child->status != AdmissionsDs::STATS__TRANSFER_CHILD ):?>
<div class="modal fade" id="modal" tabindex="-1" role="dialog" aria-labelledby="modal">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">×</span></button>
                <h4 class="modal-title"><?php echo $modalTitle; ?><?php echo  Yii::t('management','Application Fee') ?></h4>
            </div>
            <?php $this->renderPartial("admissions/_audit", array('child' => $child)); ?>
        </div>
    </div>
</div>
<?php  endif; ?>
<!-- 模态框3 缴费方式
<php if ($child->status == AdmissionsDs::STATS_PAYMENT):?> -->

<div class="modal fade" id="modal2" tabindex="-1" role="dialog" aria-labelledby="modal">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">×</span></button>
                <h4 class="modal-title"><?php echo Yii::t('management','Payment and payment options'); ?></h4>
            </div>
            <?php $this->renderPartial("admissions/_invoice", array('child' => $child)); ?>
        </div>
    </div>
</div>


<!-- 模态框4 面试结果
< if (in_array($child->status, $interview_results)):?>-->
<div class="modal fade" id="modal4" tabindex="-1" role="dialog" aria-labelledby="modal">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">×</span></button>
                <h4 class="modal-title">
                    <?php echo Yii::t('management','Assessment Result'); ?>
                    <?php if(in_array($child->school_id, array('DS','SLT'))){ ?>
                        <span class="text-center"><a target="_blank" href="<?php echo $this->createUrl('printInterviewResults', array('childid' => $child->id)) ?>"><?php echo Yii::t('interview', '打印'); ?></a></span>
                    <?php } ?>
                </h4>
            </div>
            <?php
            if(in_array($child->school_id, array('DS','SLT'))){
                if(in_array($child->start_grade, array(1,2,3,4,21,22,23,24))){
                    $this->renderPartial("admissions/graderesults_g", array('child' => $child, 'status' => 2, 'childinterview'=>$childinterview, 'calender' => $calender));
                }else{
                    $this->renderPartial("admissions/graderesults", array('child' => $child, 'status' => 2,  'childinterview'=>$childinterview, 'calender' => $calender));
                }
            }else{
                $this->renderPartial("admissions/interviewresults", array('child' => $child, 'childinterview'=>$childinterview, 'calender' => $calender));
            }
            ?>
        </div>
    </div>
</div>

<!-- 模态框5 转移孩
<php if ($child->status == AdmissionsDs::STATS__INTERVIEW_PASS ):?> -->
<?php if ($child->status != AdmissionsDs::STATS__TRANSFER_CHILD ):?>
<div class="modal fade" id="modal5" tabindex="-1" role="dialog" aria-labelledby="modal">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">×</span></button>
                <h4 class="modal-title"><?php echo Yii::t('management','Acceptance'); ?></h4>
            </div>
            <?php
            $this->renderPartial("admissions/transferstudents", array('child' => $child, 'before_child' => $before_child));
            ?>
        </div>
    </div>
</div>
<?php  endif; ?>
<script>
    // 回调：添加成功
    function cbSuccess() {
        setTimeout(function () {
            location.reload();
        }, 1000)
    }
    // 取消面试
    function cancelInterview(interviewId) {
        if(confirm('确定取消吗？')){
            var interviewId = interviewId;
            var url = "<?php echo $this->createUrl('cancelInterview');?>";
            $.ajax({
                type: "POST",
                url: url,
                data: {interviewId:interviewId}
            }).done(location.reload());
        }
    }
</script>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>

