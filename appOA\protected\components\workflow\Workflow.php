<?php
class Workflow{
    
    public $definationObj = null;
    public $nodeObj = null;
    public $opertationObj = null;
    public $useRole = -1; //1:编辑 0:查看 -1:无权限
    public $schoolId = null;
    public $nodeIds = null;
    public $nodeKeyIds = null;
    public $nodesList;
    function __construct($definationId,$nodeId=0,$operationId=0,$schoolId){
        Yii::import('common.models.workflow.*');
        Yii::import('common.models.hr.StaffApprover');
        $definationModel = Yii::app()->cache->get(WorkflowNode::WORKFLOW_NODES_CACHE_ID);
        if (isset($definationModel[$definationId])){
            $this->definationObj = $definationModel[$definationId]['obj'];
        }else{
            $this->definationObj = WorkflowDefination::model()->findByPk($definationId);
        }
        $this->nodeIds = ($this->definationObj->node_order) ? explode(',', $this->definationObj->node_order) : array();
        $this->nodeKeyIds = array_flip($this->nodeIds);
        if (!$nodeId){
            $nodeId = current($this->nodeIds);
        }
        if (isset($definationModel[$definationId]['obj']['workflowNode'][$nodeId])){
            $this->nodeObj = $definationModel[$definationId]['obj']['workflowNode'][$nodeId];
            $this->nodesList = $definationModel[$definationId]['obj']['workflowNode'];
        }else{
            $this->nodeObj = WorkflowNode::model()->findByPk($nodeId);
            $criter = new CDbCriteria;
            $criter->compare('defination_id', $definationId);
            $criter->index = 'node_id';
            $this->nodesList = WorkflowNode::model()->findAll($criter);
            unset($criter);
        }
        if ($operationId){
            $this->opertationObj = WorkflowOperation::model()->findByPk($operationId);
        }
        $this->schoolId = $schoolId;
        $this->getRoleAuthority();
    }
    
    /**
    * 根据条件判断节点权限
    * <AUTHOR>
    */
    public function getRoleAuthority(){
        if ($this->isFristNode() === true){
            $useList = $this->getNodeAllowModUser();
            if (is_array($useList) && count($useList)){
                if (in_array(Yii::app()->user->id, array_keys($useList))){
                    $this->useRole = 1;
                }
            }
            if ($this->useRole != 1) {
                //判断特殊用户组(总部员工付款)
                if (User::model()->isHQstaff(Yii::app()->user->id)){
                    $this->useRole = 1;
                }
            }
        }else{
            //判断特殊用户组(总部员工)
            if (User::model()->isHQstaff(Yii::app()->user->id)){
                $this->useRole = 0;
            }
            // 为管理多校园的用户添加查看的权限
            $adminBranch = AdmBranchLink::model()->getMultipleBranches(Yii::app()->user->getId());
            if (in_array($this->schoolId, $adminBranch)) {
                $this->useRole = 0;
            }
            //判断用户是否在当前节点
            if (!empty($this->opertationObj)){
                $useList = $this->getSavedUserRoleList($this->nodeIds);
                if (in_array(Yii::app()->user->id, array_keys($useList))) {
                    $this->useRole = 0;
                    if (($this->getCurrentNodeId() == $useList[Yii::app()->user->id]['nodeid'])) {
                        $this->useRole = 1;
                    }
                }
            }
        }
        return true;
    }
    
    /**
    * 获取某节点允许审核用户
    * @param int $nodeId 节点
    * @param int $userId 用户ID（WORKFLOW_STAFF_APPROVER\WORKFLOW_ASSIGNED_USER两种情况下使用）
    * <AUTHOR>
    */
    public function getNodeAllowModUser($nodeId=0){
        $userList = array();
        if ($nodeId){
            $nodeModel = WorkflowNode::model()->findByPk($nodeId);
            $executor = ($nodeModel->executor) ? unserialize($nodeModel->executor) : array();
        }else{
            $executor = ($this->nodeObj->executor) ? unserialize($this->nodeObj->executor) : array();
        }
        if (is_array($executor) && count($executor)){
            $arrKey = key($executor);
            $arrVal = current($executor);
            switch ($arrKey){
                case WorkflowDefination::WORKFLOW_AUTH_SYSTEM_GROUP:
                    $criter = new CDbCriteria;
                    $criter->compare('schoolid',$this->schoolId);
                    $criter->compare('type', $arrVal);
                    $AdmModel = AdmBranchLink::model()->with('user')->findAll($criter);
                    if (!empty($AdmModel)){
                        foreach ($AdmModel as $val){
                            $userList[$val->uid] = $val->user->email;
                        }
                    }else{
                        if (AdmBranchLink::ADM_TYPE_CD == $arrVal){
                            $criter = new CDbCriteria;
                            $criter->compare('profile.branch',$this->schoolId);
                            $criter->compare('profile.occupation_en', 54);
                            $criter->compare('t.isstaff', 1);
                            $criter->addCondition('t.level>0 and t.rank<>7');
                            $userModel = User::model()->with('profile')->findAll($criter);
                            if (!empty($userModel)){
                                foreach ($userModel as $val){
                                    $userList[$val->uid] = $val->email;
                                }
                            }
                        }
                    }
                    break;
                case WorkflowDefination::WORKFLOW_AUTH_SYSTEM_RULE:
                    Yii::import('application.modules.srbac.models.Assignments');
                    $criter = new CDbCriteria;
                    $criter->compare('t.itemname', $arrVal);
                    $criter->compare('profile.branch', $this->schoolId);
                    $criter->compare('user.isstaff', 1);
                    $criter->addCondition('user.level>0 and user.rank<>7');
                    $userModel = Assignments::model()->with('profile','user')->findAll($criter);
                    if (!empty($userModel)) {
                        foreach ($userModel as $val) {
                            $userList[$val->userid] = $val->user->email;
                        }
                    }
                    break;
                case WorkflowDefination::WORKFLOW_AUTH_SYSTEM_POSITION:
                    Yii::import('common.models.hr.DepPosLink');
                    $userObj = new User();
                    if(next($executor) == Branch::TYPE_OFFICE){
                        $officeBranch = CommonUtils::LoadConfig('CfgOffice');
                        $schoolId = $officeBranch;
                    } else {
                        // 共享学校人员可以提交多个学校的工作流
                        $schoolId = $this->shareSchool($this->schoolId);
                    }
                    $users = $userObj->getUserByPosition($schoolId,  explode(',', $arrVal));
                    if (!empty($users)){
                        foreach ($users as $user){
                            $userList[$user->uid] = $user->email;
                        }
                    }
                    // 添加管理多校园(om,cd)发起工作流的权限
                    // if ($this->isFristNode() === true && in_array($this->schoolId, array('TJ_ES', 'TJ_EB'))) {
                    //     $criter = new CDbCriteria;
                    //     $criter->compare('schoolid', array('TJ_ES', 'TJ_EB'));
                    //     $criter->compare('type', array('om', 'cd'));
                    //     $AdmModel = AdmBranchLink::model()->with('user')->findAll($criter);
                    //     if (!empty($AdmModel)){
                    //         foreach ($AdmModel as $val){
                    //             $userList[$val->uid] = $val->user->email;
                    //         }
                    //     }
                    // }                   
                    break;
                case WorkflowDefination::WORKFLOW_AUTH_USER_LEVEL:
                    
                    break;
                
            }
        }
        return $userList;
    }
    
    
    public function getSavedUserRoleList($nodeId=0){
        $useList = array();
        $criter = new CDbCriteria;
        $criter->compare('operation_id', $this->opertationObj->id);
        if (!empty($nodeId))
        {
            $criter->compare('user_node_id', $nodeId);
        }else{
            $criter->compare('user_node_id', $this->nodeObj->node_id);
        }
        $roleModel = WorkflowRole::model()->with('user')->findAll($criter);
        if (!empty($roleModel)) {
            foreach ($roleModel as $val) {
                // 判断员工是否为在职状态
                if($val->user->level == 1){
                    $useList[$val->assign_user]['email'] = $val->user->email;
                    $useList[$val->assign_user]['nodeid'] = $val->user_node_id;
                }
            }
        }
        return $useList;
    }
    
    /**
    * 根据条件取得下一个结点
    * @return int
    * <AUTHOR>
    */
    function getNextNodeId(){
        $newVar = $this->nodeKeyIds[$this->nodeObj->node_id];
        $preNodeId = isset($this->nodeIds[$newVar+1]) ? $this->nodeIds[$newVar+1] : 0 ;
        return $preNodeId;
    }
    
    /**
    * 根据条件取得上一个结点
    * @return int
    * <AUTHOR>
    */
    function getPreviousNodeId(){
        $newVar = $this->nodeKeyIds[$this->nodeObj->node_id];
        $preNodeId = isset($this->nodeIds[$newVar-1]) ? $this->nodeIds[$newVar-1] : 0 ;
        return $preNodeId;
    }

    /**
     * 根据条件取得当前结点之前的所有节点
     * @return int
     * <AUTHOR>
     */
    function getPreviousNodeIds(){
        $newVar = $this->nodeKeyIds[$this->nodeObj->node_id];
        $preNodeIds = array_slice($this->nodeIds,0,$newVar,TRUE);
        return $preNodeIds;
    }
    
    /**
     * 判断操作节是否为最后节点
     * @return boolean 
     */
    public function isEndNode(){
        $newVar = $this->nodeKeyIds[$this->nodeObj->node_id];
        return ($newVar == count($this->nodeKeyIds)-1) ? true : false;
    }
    
     /**
     * 判断操作节是否为第一节点
     * @return boolean 
     */
    public function isFristNode(){
        return ($this->nodeKeyIds[$this->nodeObj->node_id] == 0) ? true : false;
    }
    
    /**
     * 取当前的节点ID
     * @return int 节点ID
     */
    public function getCurrentNodeId(){
        return $this->nodeObj->node_id;
    }

    /**
     * 取得当前业务ID
     * @return int 业务ID
     */
    public function getOperateId(){
        return (empty($this->opertationObj)) ? 0 : $this->opertationObj->id;
    }
    
    /**
     * 取工作流ID
     * @return int
     */
    public function getWorkflowId(){
        return (empty($this->definationObj)) ? 0 : $this->definationObj->defination_id;
    }
    
    /**
     *  获取业务关系Id
     */
    public function getOperateObjId(){
        return (empty($this->opertationObj)) ? 0 : $this->opertationObj->operation_object_id;
    }
    
    /**
     *  获取业务状态
     */
    public function getOperateStatus(){
        return (empty($this->opertationObj)) ? null : $this->opertationObj->state;
    }
    
    /**
     * 获取业务属性
     */
    public function getOperateValue($val){
        return (empty($this->opertationObj)) ? null : $this->opertationObj->$val;
    }

    /**
     * 创建用户角色
     * @param int $nodeId 节点ID
     * @param int $operationId 业务ID
     * @param object $transaction  事务对象
     * @param array $userList 外部传过用户
     * <AUTHOR>
     */
    public function createRole($nodeId,$operationId,$transaction,$userList = array()){
        //如果当前节点审核用户为外部传入用户，直接调用
        if (is_array($userList) && count($userList)){
            // if (isset($this->definationObj['workflowNode'][$nodeId])){
            //     $nodeObj = $this->definationObj['workflowNode'][$nodeId];
            // }else{
            //     $nodeObj = WorkflowNode::model()->findByPk($nodeId);
            // }
            // $executor = ($nodeObj->executor) ? unserialize($nodeObj->executor) : array();
            // if (is_array($executor) && count($executor)){
            //     $arrKey = key($executor);
            //     if ($arrKey != WorkflowDefination::WORKFLOW_AUTH_ASSIGNED_USER){
            //         return false;
            //     }
            // }else{
            //     return false;
            // }
        }else{
            $userList = $this->getNodeAllowModUser($nodeId);
        }
        foreach ($userList as $k=>$var){
            $model = WorkflowRole::model()->find('operation_id=:operation_id and assign_user=:assign_user and user_node_id=:user_node_id',array(':operation_id'=>$operationId,':assign_user'=>$k,':user_node_id'=>$nodeId));
            if (empty($model)){
                $model = new WorkflowRole();
            }
            $model->operation_id = $operationId;
            $model->defination_id = $this->definationObj->defination_id;
            $model->assign_user = $k;
            $model->user_node_id = $nodeId;
            $model->current_operator = WorkflowRole::WORKFLOW_ROLE_CURRENT_CHECK_USER;
            $model->state = WorkflowOperation::WORKFLOW_STATS_UNOP;
            $model->timestamp = time();
            if (!$model->save()){
                $transaction->rollBack();
                return false;
            }
        }
        return true;
    }
    
    /**
     * 判断当前节点是否操作
     */
    public function isNodeOperation(){
        $crite = new CDbCriteria;
        $crite->compare('operation_id', $this->getOperateId());
        $crite->compare('state', array(10,20,30));
        $crite->compare('current_node_index', $this->getCurrentNodeId());
        return WorkflowProcess::model()->count($crite);
    }

    /**
     * 共享员工的校园配置
     */
    public function shareSchool($schoolId)
    {
        $config = array(
            array('TJ_ES', 'TJ_EB'),
            array('BJ_OG', 'BJ_OG-PH'),
            array('BJ_XHL', 'BJ_LJ-IBS'),
            array('BJ_DS', 'BJ_SLT', 'BJ_QF', 'BJ_QFF', 'BJ_IASLT'),
            array('NB_HH', 'NB_FJ'),
            array('BJ_TYG', 'BJ_SS', 'BJ_BU'),
        );
        foreach ($config as $k => $v) {
            if (in_array($schoolId, $v)) {
                return $config[$k];
            }
        }
        return $schoolId;
    }
}
?>
