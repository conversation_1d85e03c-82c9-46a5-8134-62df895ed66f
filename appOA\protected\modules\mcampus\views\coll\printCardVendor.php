<?php
if ($this->branchObj->type == 50) {
 $frontendSrc='https://m2.files.ivykids.cn/cloud01-file-5FqM3lKp2rtr7gyFlVLrG83u0WEG3.jpg';
 $backendSrc='https://m2.files.ivykids.cn/cloud01-file-5Fi8-otOVCH4MpyGqvXrXWeoX1hgB.jpg';
}else {
	$imgSrc='http://m2.files.ivykids.cn/cloud01-file-8025768FqoRUgyX5mkN_M9pWPUH9fEAKZvK.jpg';
}
if($this->branchId == 'BJ_QFF'){
//    $imgSrc='https://m2.files.ivykids.cn/cloud01-file-8025768Fvhwd17XZV4h5Tm4E6j4YLlXZFl9.png';
}
?>
<style>
<?php if(Yii::app()->language == 'en_us'):?>
html, body {
    margin: 0;
    padding: 0;
    font-family: 'Myriad Pro';
    font-weight: normal;
    font-style: normal;
}
<?php else:?>
html, body {
    margin: 0;
    padding: 0;
    font-family: '思源黑体 CN';
    font-weight: normal;
    font-style: normal;
}
<?php endif;?>
.page {
    margin: 0 auto 24px auto;
    padding: 0;
    width: 76mm;
    height: 106mm;
    background: white;
    /*box-shadow: 0 0 0.5cm rgba(0,0,0,0.5);*/
    page-break-after: always;
    position: relative;
}
.page .bg {
    width: 76mm;
    height: 106mm;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 0;
}
.page-01 .content {
    width: 76mm;
    height: 106mm;
    position: absolute;
    left: 0;
    top: 0;
    overflow: hidden;
}
.page-01 .content .sub-box {
    margin-left: 22.5mm;
}
.parent-avatar {
    margin: 27mm 0 3mm 0;
    width: 31mm;
    height: 31mm;
    border-radius: 1mm;
}

.page .text-1 {
    font-weight: bold;
}

.child-avatar {
    width: 25mm;
    height: 25mm;
    margin-bottom: 3mm;
    border-radius: 1mm;
}

.sub-box-2 {
    width: 25mm;
    height: 10mm;
    font-weight: bold;
    position: absolute;
    right: 4mm;
    bottom: 2mm;
}
.sub-box-3 {
    width: 25mm;
    height: 10mm;
    font-size: 4.2mm;
    position: absolute;
    right: 4mm;
    bottom: -3mm;
}

.page-02 .content {
    width: 76mm;
    height: 106mm;
    position: absolute;
    left: 0;
    top: 0;
    overflow: hidden;
}

.page-02 .content .sub-box {
    padding: 10mm;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}

.sub-box-4 {
    width: 25mm;
    height: 46mm;
    /* flex: 0 0 50%; */
}

.tips-block {
    text-align: center;
    font-size: 14px;
    background-color: #ffecba;
    top: 0;
    left: 0;
    width: 100%;
    height: 44px;
    line-height: 44px;
    z-index: 9;
}
.mb16 {
    margin-bottom: 16px;
}
.mt-16 {
    margin-top: -16px;
}
.web-only input {
    padding: 4px;
    width: 66px;
}
.web-only span {
    font-size: 12px;
    color: #666;
    margin-left: 16px;
}
@media print {
    @page {
        size: 76mm 106mm;
        margin: 0;
        padding: 0;
    }
    html, body {
        margin: 0;
        padding: 0;
    }
    .web-only {
        display: none;
    }
}
</style>
<div>
    <div class="tips-block mb16 web-only">使用本打印功能，请确保您的电脑已安装下列字体 <a href="https://mega.ivymik.cn/fonts%2FMyriadPro-Regular.otf">MyriadPro</a>&nbsp;&nbsp;<a href="https://mega.ivymik.cn/fonts%2FSourceHanSansCN-Regular_0.otf">思源黑体</a> （下载后双击打开进行安装）</div>
    <div>
        <?php
        function schoolYearLess($schoolYear)
        {
            if ($schoolYear) {
                $schoolYear = trim($schoolYear);
                return substr($schoolYear, 0, 5) . substr($schoolYear, 7);
            }
            return $schoolYear;
        }
        function classLess($classStr = '')
        {
            $classStr = trim($classStr);
            $prune = array('A', 'B', 'C', 'D', 'E', 'F');
            $endStr = substr($classStr, -1, 1);
            if (in_array($endStr, $prune)) {
                return substr($classStr, 0, -1);
            }
            return $classStr;
        }
        $i = 0;
        foreach ($data['pickups'] as $item):
        ?>
        <div class="page page-01">
            <img src="<?php echo $frontendSrc;?>" alt="" class="bg">
            <div class="content">
                <div class="sub-box">
                    <div class="sub-box-1">
                        <img src="<?php echo $item['head_url'];?>" alt="" class="parent-avatar">
                        <div class="text-1"><?php echo Yii::t('global','Name') . Yii::t('global',': ') . trim($item['name']);?></div>
                        <div class="text-1"><?php echo Yii::t('asa','Phone') . Yii::t('global',': ') . trim($item['phone']);?></div>
                        <div class="text-1"><?php echo Yii::t('site','Relationship') . Yii::t('global',': ') . trim($item['relation']);?></div>
                    </div>
                    <svg class="sub-box-2">
                        <text font-size="4.2mm" y="14" x="10" fill="#ffffff">
                            <?php echo $data['school_title']; ?>
                        </text>
                    </svg>
                    <svg class="sub-box-3">
                        <text font-size="4.2mm" y="14" x="10" fill="#ffffff">
                            <?php echo schoolYearLess($data['start_year']); ?>
                        </text>
                    </svg>
                </div>
            </div>
        </div>
        <div class="page page-02">
            <img src="<?php echo $backendSrc;?>" alt="" class="bg">
            <div class="content">
                <div class="sub-box">
                    <?php foreach ($item['children'] as $child):?>
                        <div class="sub-box-4">
                            <img src="<?php echo $child['avatar'];?>" alt="" class="child-avatar">
                            <div class="text-1"><?php echo $child['name'];?></div>
                            <svg class="text-2" style="width: 100%;">
                                <text font-size="2.4mm" y="12" x="0" fill="#000">
                                    <?php
                                    if ($this->branchObj->type == 50 || $this->branchId == 'BJ_QFF') {
                                        echo classLess(schoolYearLess($child['className']));
                                    }
                                    else {
                                        echo schoolYearLess($child['className']);
                                    }
                                    ?>
                                </text>
                            </svg>
                        </div>
                    <?php endforeach;?>
                </div>
            </div>
        </div>
        <?php
        $i++;
        endforeach;
        ?>
    </div>
</div>
