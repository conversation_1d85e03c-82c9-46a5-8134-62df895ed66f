<?php

class StaticController extends Controller
{

	public function actionUserPhoto($uid = 0)
	{
        if($uid){
            $suerObj = User::model()->findByPk($uid);
            if($suerObj){
                if($suerObj->user_avatar == 'blank.gif'){
                    $suerObj->user_avatar = ($suerObj->profile->user_gender == 1)? 'male.png' : 'female.png';
                }
                ob_end_clean();
                $file_dir = Yii::app()->params['OAUploadBaseUrl'] ."/users/";
                $fileres = file_get_contents($file_dir . $suerObj->user_avatar . '?' . time());
                header('Content-type: image/jpeg');
                echo $fileres;
            }
        }
	}

    public function actionCardPhoto($cardid = 0)
    {
        Yii::import('common.models.identity.*');
        $crite = new CDbCriteria;
        $crite->compare('cid', $cardid);
        $suerObj = Cards::model()->find($crite);
        ob_end_clean();
        $file_dir = Yii::app()->params['OAUploadBaseUrl'] ."/users/";
        $fileres = "blank.gif";
        if($suerObj){
            $data = CJSON::decode($suerObj->data);
            $file_dir = Yii::app()->params['OAUploadBaseUrl'] ."/crads/";
            $fileres = file_get_contents($file_dir . $data['photo'] . '?' . time());
        } else {
            // 获取qrcode签到的家长头像
            $qrcode = Qrcode::model()->findByAttributes(array('code' => $cardid));
            if ($qrcode) {
                $this->actionUserPhoto($qrcode->pid);
                Yii::app()->end();
            }
        }
        header('Content-type: image/jpeg');
        echo $fileres;

    }
}