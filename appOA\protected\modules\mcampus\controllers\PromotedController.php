<?php

class PromotedController extends BranchBasedController{

    public $class_up_total = 0;
    public $class_down_total = 0;

    public $pre_class_up_total = 0;
    public $pre_class_down_total = 0;

    public $cur_class_up_total = 0;
    public $cur_class_new_total = 0;
    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

        Yii::import('common.models.child.*');
        Yii::import('common.models.calendar.*');
        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = false;
        $this->branchSelectParams['urlArray'] = array('//mcampus/promoted/promoted');
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue2.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/echarts.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile($cs->getCoreScriptUrl().'/jui/js/jquery-ui.min.js');
    }

    /*
     *
     *
     */
    public function actionIndex()
    {
        $this->branchSelectParams['urlArray'] = array('//mcampus/promoted/index');
        $branck = new Branch();
        $branckInfo = $branck->getBranchList();
        $schoolid = $this->branchId;
        $this->render('index', array(
            'branckInfo' => $branckInfo,
            'schoolid' => $schoolid,
        ));
    }

    // 升学报告数据统计
    public function actionPromotedData()
    {
        $data = array('previous' => array(), 'current' => array());
        $pre_total = array();
        $cur_total = array();
        if(Yii::app()->request->isPostRequest){
            $last_school_end_one = Yii::app()->request->getParam('pre_start', ''); // 2019-08-08
            $last_school_end_two = Yii::app()->request->getParam('pre_end', '');
            $next_school_begin_one = Yii::app()->request->getParam('cur_start', ''); // 2019-09-08
            $next_school_begin_two = Yii::app()->request->getParam('cur_end', '');
            $next_schoolid = Yii::app()->request->getParam('next_school', '');
            if(!$last_school_end_one && !$last_school_end_two && !$next_school_begin_one && !$next_school_begin_two){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '四个采样日期为必填项');
                $this->showMessage();
            }

            if(strtotime($next_school_begin_one) <= strtotime($last_school_end_one) || strtotime($next_school_begin_one) <= strtotime($last_school_end_two) || strtotime($next_school_begin_one) <= strtotime($last_school_end_one) || strtotime($next_school_begin_two) <= strtotime($last_school_end_two)){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '后两个采样日期必须大于前两个采样日期');
                $this->showMessage();
            }
            /* ----------------------- start -------------------*/
            /* -----------------------上学年末 -------------------*/
            // 上学年第一个时间数据
            $schoolid = $this->branchId;
            $previous_one = array();
            if($last_school_end_one){
                $previous_one_time = strtotime($last_school_end_one);
                $previous_one = $this->getChild($schoolid, $previous_one_time);
            }
            // 上学年第二个时间数据
            $previous_two = array();
            if($last_school_end_two){
                $previous_one_two = strtotime($last_school_end_two);
                $previous_two = $this->getChild($schoolid, $previous_one_two);
            }
            $previousInfo = $this->getChildmerge($previous_one, $previous_two); //  两个日期相加所有不同的孩子总数据
            $previousClassInfo = $this->getClass($previousInfo); //  两个日期的总班级数据

            /* ----------------------- end -------------------*/
            /* ----------------------- start -------------------*/
            /*
             * 下学年初
             */
            $next_schoolid = ($next_schoolid) ? $next_schoolid : $schoolid;
            // 下学年第一个时间数据
            $current_one = array();
            if($next_school_begin_one){
                $current_one_time = strtotime($next_school_begin_one);
                $current_one = $this->getChild($next_schoolid, $current_one_time);
            }
            // 下学年第二个时间数据
            $current_two = array();
            if($next_school_begin_two){
                $current_one_end = strtotime($next_school_begin_two);
                $current_two = $this->getChild($next_schoolid, $current_one_end);
            }
            $currentInfo = $this->getChildmerge($current_one, $current_two); //  两个日期相加所有不同的孩子总数据
            $currentClassInfo = $this->getClass($currentInfo); //  两个日期的总班级数据
            $pre_total = isset($previousInfo) && isset($previousInfo[0]) ? $previousInfo[0] : array();
            $cur_total = isset($currentInfo) && isset($currentInfo[0]) ? $currentInfo[0] : array();
            $childNameInfo = $this->getChildData($pre_total, $cur_total); //  两个日期的总班级数据

            /* ----------------------- end -------------------*/
            /* ------------------------ 上学年末数据 ------------*/
            if($previousClassInfo) {
                foreach ($previousClassInfo as $class) {
                    $classUpNum = 0;
                    $classTotalNum = count($previousInfo[$class->classid]);
                    foreach ($previousInfo[$class->classid] as $child) {
                        $upStatus = (isset($currentInfo[0][$child]) && $currentInfo[0][$child]) ? 1 : 0;
                        $childInfo[$class->classid][] = array(
                            'childid' => $child,
                            'name' => isset($childNameInfo[$child]) ? $childNameInfo[$child]->getChildName() : 'None',
                            'status' => ($upStatus) ? 'up' : 'down',
                        );
                        $classUpNum = ($upStatus) ? $classUpNum + 1 : $classUpNum;
                    }
                    $class_down = $classTotalNum - $classUpNum;
                    $pre_name = substr($class->title, 0 ,9) . '学年';
                    $pre_percent = sprintf("%.2f", $classUpNum / $classTotalNum * 100);
                    $data['previous']['items'][] = array(
                        'class_name' => $class->title,
                        'class_total' => $classTotalNum,
                        'class_up' => $classUpNum,
                        'class_down' => $class_down,
                        'pre_percent' => $pre_percent,
                        'pre_percents' => 100 -  $pre_percent,
                        'children' => $childInfo[$class->classid],
                    );
                    $this->class_up_total += $classUpNum;
                    $this->class_down_total += $class_down;
                }
            }
            /* ------------------------- end ------------------------*/
            /* ------------------------ 下学年初数据 start ------------*/

            if($currentClassInfo) {
                foreach ($currentClassInfo as $class) {
                    $cur_classUpNum = 0;
                    $cru_classTotalNum = count($currentInfo[$class->classid]);
                    foreach ($currentInfo[$class->classid] as $child) {
                        $cur_upStatus = (isset($previousInfo) && $previousInfo[0][$child]) ? 1 : 0;
                        $cur_childInfo[$class->classid][] = array(
                            'childid' => $child,
                            'name' => isset($childNameInfo[$child]) ? $childNameInfo[$child]->getChildName() : 'None',
                            'status' => ($cur_upStatus) ? 'up' : 'new',
                        );
                        $cur_classUpNum = ($cur_upStatus) ? $cur_classUpNum+1 : $cur_classUpNum;
                    }
                    $class_new = $cru_classTotalNum - $cur_classUpNum;
                    $cur_name = substr($class->title, 0 ,9) . '学年';
                    $cur_percent = sprintf("%.2f", $class_new / $cru_classTotalNum * 100);
                    $data['current']['items'][] = array(
                        'class_name' => $class->title,
                        'class_total' => $cru_classTotalNum,
                        'class_up' => $cur_classUpNum,
                        'class_new' => $class_new,
                        'cur_percent' => $cur_percent,
                        'cur_percents' => 100 -  $cur_percent,
                        'children' => $cur_childInfo[$class->classid],
                    );
                    $this->cur_class_up_total += $cur_classUpNum;
                    $this->cur_class_new_total += $class_new;
                }
            }
            /* ------------------------- end ------------------------*/
        }

        $data['previous']['pre_name'] = $pre_name;
        $data['previous']['pre_total'] = ($pre_total) ? count($pre_total) : 0;
        $data['previous']['pre_up'] = $this->class_up_total;
        $data['previous']['pre_down'] = $this->class_down_total;
        $data['previous']['pre_percent'] = sprintf("%.2f", $this->class_up_total/count($pre_total) * 100);

        $data['current']['cur_name'] = $cur_name;
        $data['current']['cur_total'] = ($cur_total) ? count($cur_total) : 0;
        $data['current']['cur_up'] = $this->cur_class_up_total;
        $data['current']['cur_new'] = $this->cur_class_new_total;
        $data['current']['cur_percent'] = sprintf("%.2f", $this->cur_class_new_total/count($cur_total) * 100);

        $this->addMessage('state', 'success');
        $this->addMessage('data', $data);
        $this->addMessage('message', Yii::t("event","success"));
        $this->showMessage();
    }

    // 根据学校和时间去查询园长工具表，拿到某一天的数据
    public static function getChild($schoolid, $time=0)
    {
        $childids = array();
        if ($schoolid && $time){
            $criteria = new CDbCriteria;
            $criteria->compare('period_timestamp', $time);
            $criteria->compare('schoolid', $schoolid);
            $items = StatsChildCount::model()->findAll($criteria);
            foreach($items as $item){
                $chids = $item->childids1.','.$item->childids2;
                foreach(explode(',',$chids) as $cid){
                    if ($cid){
                        $childids[$item->classid][$cid] = $cid;
                    }
                }
            }
        }
        return $childids;
    }

    // 给两个孩子数组去重合并成一个
    public static function getChildmerge($childOne, $childTwo)
    {
        $childInfos = array();
        if ($childOne && $childTwo){
            $childFirst = array_keys($childOne);
            $childLast = array_keys($childTwo);
            $classInfo = array_flip($childFirst)+array_flip($childLast);
            foreach ($classInfo as $class => $val){
                $childO = array();
                if(isset($childOne[$class])){
                    $childO = $childOne[$class];
                }
                $childT = array();
                if($childTwo[$class]){
                    $childT = $childTwo[$class];
                }
                $childInfos[$class] = $childO + $childT;
            }
        }

        if(!$childInfos){
            if($childOne){
                $childInfos = $childOne;
            }
            if($childTwo){
                $childInfos = $childTwo;
            }
        }

        $ret = array();
        if ($childInfos) {
            $_tmp = array();
            foreach ($childInfos as $classid => $children) {
                foreach ($children as $childid) {
                    if($classid != 0 && !in_array($childid, $_tmp)) {
                        $_tmp[] = $childid;
                        $ret[$classid][$childid] = $childid;
                    }
                }
            }
            $ret[0] = $childInfos[0];
        }

        return $ret;
    }

    // 获取班级信息 四个日期的
    public static function getClass($classids)
    {
        $classids = isset($classids) ? array_keys($classids) : array();
        $classInfo = array();
        if($classids){
            $criteria = new CDbCriteria;
            $criteria->compare('classid', $classids);
            $criteria->index = 'classid';
            $criteria->order='child_age ASC, title ASC';
            $classInfo = IvyClass::model()->findAll($criteria);
        }
        return $classInfo;
    }

    // 获取班级信息 两个学期用得
    public static function getClassNew($classids)
    {
        $classids = isset($classids) ? array_keys($classids) : array();
        $classInfo = array();
        if($classids){
            $criteria = new CDbCriteria;
            $criteria->compare('classid', $classids);
            $criteria->index = 'classid';
            $criteria->order='child_age ASC, title ASC';
            $classModel = IvyClass::model()->findAll($criteria);
            if($classModel){
                foreach ($classModel as $classid => $val){
                    $classInfo[$classid] = array(
                        'classid' => $classid,
                        'title' => $val->title,
                    );
                }
            }
        }
        return $classInfo;
    }

    // 获取孩子信息
    public static function getChildData($childids_one = array(), $childids_Two = array())
    {
        $childModel = array();
        $childids = array();
        if($childids_one && $childids_Two){
            $childids = $childids_one + $childids_Two;
        }
        if(!$childids){
            if($childids_one){
                $childids = $childids_one;
            }
            if($childids_Two){
                $childids = $childids_Two;
            }
        }

        if($childids) {
            $criteria = new CDbCriteria;
            $criteria->compare('childid', $childids);
            $criteria->index = 'childid';
            $childModel = ChildProfileBasic::model()->findAll($criteria);
        }

        return $childModel;
    }

    /*
     * $yearList 学年数据
     * $branckInfo 学校数据
     * $schoolid  当前学校
     */
    public function actionPromoted()
    {
        $this->branchSelectParams['urlArray'] = array('//mcampus/promoted/promoted');
        $branck = new Branch();
        $branckInfo = $branck->getBranchList();
        $schoolid = $this->branchId;
        $yearList = $this->getcalendar($this->branchId);

        $this->render('promoted', array(
            'yearList' => $yearList,
            'branckInfo' => $branckInfo,
            'schoolid' => $schoolid,
        ));
    }
    /*
     * 1, 上学年 < 下学年（且下学年不是当前学年）  //  查询新表
     * 2, 上学年 = 下学年                       // 下学年去查园长表，（以最后一天往前推一星期）
     * 3, 上学年 < 下学年（下学年是当前学年）     // 拿当天的数据
     *
     * $pre_year 第一个学年
     * $cur_year 第二个学年
     * $schoolid 学校
     * $type 查询类型  现在写死不用传
     */

    public function actionInquirePromoted()
    {
        if(Yii::app()->request->isPostRequest) {
            $perYear = Yii::app()->request->getParam('pre_year', '');
            $curYear = Yii::app()->request->getParam('cur_year', '');
            $schoolid = Yii::app()->request->getParam('schoolid', '');
            $type = Yii::app()->request->getParam('type', 'yearly');

            if(!$perYear || !$curYear){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t("event","两个学年都为必填"));
                $this->showMessage();
            }

            if($perYear > $curYear){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t("event","第二学年不可大于第一学年"));
                $this->showMessage();
            }

            // 第一个学年数据
            $pre_data = array(
                'type' => $type,
                'perYear' => $perYear,
                'schoolid' => $this->branchId,
            );
            $preInfo = $this->getSchoolYearData($pre_data);

            $criteria = new CDbCriteria;
            $criteria->compare('startyear', $curYear);
            $criteria->compare('branchid', $this->branchId);
            $calendarSchoolModel = CalendarSchool::model()->find($criteria);

            //  第一个学年 < 第二个学年（且下学年不是当前学年）  查询新表
            $schoolid = ($schoolid) ? $schoolid : $this->branchId;
            $cur_data = array(
                'type' => $type,
                'perYear' => $curYear,
                'schoolid' => $schoolid,
            );
            $curInfo = $this->getSchoolYearData($cur_data);

            $calendarModel = Calendar::model()->findByPk($calendarSchoolModel->yid);
            $endDate = end(explode(',', $calendarModel->timepoints));
            $times = strtotime(date("Y-m-d", time()));
            if(($calendarSchoolModel->is_selected && $endDate >= $times) || $perYear == $curYear) {
                // 第一个学年 < 第二个学年（下学年是当前学年且现在时间小于学历的最后时间）     // 拿当天的数据
                if ($perYear == $curYear) {  // 上学年 = 下学年  // 下学年去查园长表，（以最后一天往前推一星期）
                    $times = $endDate - 86400 * 7;
                }
                $statsChildCountModel = $this->getChilds($schoolid, $times); // 根据学校和时间去查询园长表某一天的数据
                $curInfo = $this->getChildmerges($statsChildCountModel); // 整合查询出园长表的数据
            }

            $data['previous']['items'] = array();
            if($preInfo){
                $classidArray =  array_flip(array_keys($preInfo['childArr']));
                $pre_Class_Info = $this->getClassNew($classidArray); //班级数据
                $differenceInfo = $this->getClassNum($classidArray,$pre_Class_Info);
                $pre_Class_Info = isset($cur_Class_Info) ? array_merge_recursive($cur_Class_Info,$differenceInfo): $pre_Class_Info;
                $pre_child_Name_Info = $this->getChildData(array_keys($preInfo['childsData'])); // 孩子信息
                foreach ($pre_Class_Info as $class){
                    $pre_child_up = 0;
                    $pre_class_total = count($preInfo['childArr'][$class['classid']]);
                    foreach ($preInfo['childArr'][$class['classid']] as $child){
                        $pre_upStatus = isset($curInfo) && isset($curInfo['childsData'][$child]) ? 1 : 0;
                        $pre_childInfo[$class['classid']][] = array(
                            'childid' => $child,
                            'name' => isset($pre_child_Name_Info[$child]) ? $pre_child_Name_Info[$child]->getChildName() : 'None',
                            'status' => ($pre_upStatus) ? 'up' : 'down',
                        );
                        $pre_child_up = ($pre_upStatus) ? $pre_child_up+1 : $pre_child_up;
                    }
                    $pre_class_down = $pre_class_total-$pre_child_up;
                    $pre_percent = sprintf("%.2f", $pre_child_up / $pre_class_total * 100);
                    $data['previous']['items'][] = array(
                        'class_name' => $class['title'],
                        'class_total' => $pre_class_total,
                        'class_up' => $pre_child_up,
                        'class_down' => $pre_class_down,
                        'pre_percent' => $pre_percent,
                        'pre_percents' => 100 - $pre_percent,
                        'children' => $pre_childInfo[$class['classid']],
                    );
                    $this->pre_class_up_total += $pre_child_up;  // 留存总人数
                    $this->pre_class_down_total += $pre_class_down; // 流失总人数
                }
            }
            $data['current']['items'] = array();
            if($curInfo){
                $classidArray = array_flip(array_keys($curInfo['childArr']));
                $cur_Class_Info = $this->getClassNew($classidArray); //班级数据
                $differenceInfo = $this->getClassNum($classidArray,$cur_Class_Info);
                $cur_Class_Info = isset($cur_Class_Info) ? array_merge_recursive($cur_Class_Info,$differenceInfo): $cur_Class_Info;

                $cur_child_Name_Info = $this->getChildData(array_keys($curInfo['childsData'])); // 孩子信息
                foreach ($cur_Class_Info as $class){
                    $cur_child_up = 0;
                    $cru_class_total = count($curInfo['childArr'][$class['classid']]);
                    foreach ($curInfo['childArr'][$class['classid']] as $child){
                        $cur_upStatus = isset($preInfo) && isset($preInfo['childsData'][$child]) ? 1 : 0;
                        $cur_childInfo[$class['classid']][] = array(
                            'childid' => $child,
                            'name' => isset($cur_child_Name_Info[$child]) ? $cur_child_Name_Info[$child]->getChildName() : 'None',
                            'status' => ($cur_upStatus) ? 'up' : 'new',
                        );
                        $cur_child_up = ($cur_upStatus) ? $cur_child_up+1 : $cur_child_up;
                    }
                    $cur_class_new = $cru_class_total-$cur_child_up;
                    $cur_percent = sprintf("%.2f", $cur_child_up / $cru_class_total * 100);
                    $data['current']['items'][] = array(
                        'class_name' => $class['title'],
                        'class_total' => $cru_class_total,
                        'class_up' => $cur_child_up,
                        'class_new' => $cur_class_new,
                        'cur_percent' => $cur_percent,
                        'cur_percents' => 100 - $cur_percent,
                        'children' => $cur_childInfo[$class['classid']],
                    );
                    $this->cur_class_up_total += $cur_child_up;  // 留存总人数
                    $this->cur_class_new_total += $cur_class_new; // 新增总人数
                }
            }

            $perEndYear = $perYear+1;
            $pre_total = isset($preInfo) ? $preInfo['num'] : 0;
            $data['previous']['pre_name'] = $perYear . ' - '.  $perEndYear;
            $data['previous']['pre_total'] = isset($preInfo) ? $preInfo['num'] : 0;
            $data['previous']['pre_up'] = $this->pre_class_up_total;
            $data['previous']['pre_down'] = $this->pre_class_down_total;
            $data['previous']['pre_percent'] = sprintf("%.2f", $this->pre_class_up_total/$pre_total * 100);

            $curEndYear = $curYear+1;
            $cur_total = isset($preInfo) ? $preInfo['num'] : 0;
            $data['current']['cur_name'] = $curYear . ' - '. $curEndYear;
            $data['current']['cur_total'] = isset($curInfo) ? $curInfo['num'] : 0;
            $data['current']['cur_up'] = $this->cur_class_up_total;
            $data['current']['cur_new'] = $this->cur_class_new_total;
            $data['current']['cur_percent'] = sprintf("%.2f", $this->cur_class_up_total/$cur_total * 100);
        }

        $this->addMessage('state', 'success');
        $this->addMessage('data', $data);
        $this->addMessage('message', Yii::t("event","success"));
        $this->showMessage();
    }

    //
    public static function getSchoolYearData($data)
    {
        $dataInfo = array();
        $crit = new CDbCriteria;
        $crit->compare('type', $data['type']);
        $crit->compare('startyear', $data['perYear']);
        $crit->compare('schoolid', $data['schoolid']);
        $crit->order = 'updated_at DESC';
        $sycModel = SchoolyearStatsChild::model()->find($crit);
        if($sycModel){
            $chidldArr = explode(',', $sycModel->childids);
            $classArr = explode(',', $sycModel->classids);
            $childClass = array();
            foreach ($classArr as $key=>$val){
                $childClass[$val][] = $chidldArr[$key];
            }
            $dataInfo = array(
                'num' => $sycModel->num,
                'startDate' => $sycModel->startdate,
                'endDate' => $sycModel->enddate,
                'childsData' => array_flip($chidldArr),
                'childArr' => $childClass,
            );
        }
        return $dataInfo;
    }

    // 根据学校和时间去查询园长工具表，拿到某一天的数据
    public static function getChilds($schoolid, $time=0)
    {
        $childids = array();
        if ($schoolid && $time){
            $criteria = new CDbCriteria;
            $criteria->compare('period_timestamp', $time);
            $criteria->compare('schoolid', $schoolid);
            $items = StatsChildCount::model()->findAll($criteria);
            foreach($items as $item){
                $chids = $item->childids1.','.$item->childids2;
                foreach(explode(',',$chids) as $cid){
                    if ($cid){
                        $childids[$item->classid][$cid] = $cid;
                    }
                }
            }
        }
        return $childids;
    }

    // 整合查询出园长表里的数据
    public static function getChildmerges($childData)
    {
        $ret = array();
        if ($childData) {
            $ret['num'] = count($childData[0]);
            $ret['childsData'] = $childData[0];
            $_tmp = array();
            foreach ($childData as $classid => $children) {
                foreach ($children as $childid) {
                    if($classid != 0 && !in_array($childid, $_tmp)) {
                        $_tmp[] = $childid;
                        $ret['childArr'][$classid][$childid] = $childid;
                    }
                }
            }
        }
        return $ret;
    }
    // 拿到当前学校所有的学年  $schoolid 学校ID
    public static function getcalendar($schoolid)
    {
        $criteria = new CDbCriteria();
        $criteria->compare('branchid', $schoolid);
        $criteria->order = 'startyear asc';
        $calendarModel = CalendarSchool::model()->findAll($criteria);
        $calendarInfo = array();
        if($calendarModel){
            foreach ($calendarModel as $val){
                $end = $val->startyear + 1;
                $calendarData[] = array(
                    'yid' => $val->yid,
                    'id' => $val->startyear,
                    'year' => $val->startyear . ' - ' . $end,
                );
            }

            $criteria = new CDbCriteria();
            $criteria->compare('schoolid', $schoolid);
            $criteria->order = 'startyear asc';
            $criteria->limit = 1;
            $model = SchoolyearStatsChild::model()->find($criteria);

            foreach ($calendarData as $val) {
                if ($val['id'] >= $model->startyear) {
                    $calendarInfo[] = array(
                        'yid' => $val['yid'],
                        'id' => $val['id'],
                        'year' => $val['year'],
                    );
                }
            }
        }


        return $calendarInfo;
    }

    // 已经删除得班级加入到查询出班级列表里，并重组数组
    public static function getClassNum($class1,$class2)
    {
        $difference = array_diff_key($class1,$class2);
        $classinfo = array();
        if($difference){
            $num = 1;
            foreach ($difference as $classid => $val){
                $classinfo[$classid] = array(
                    'classid' => $classid,
                    'title' => '未知班级 - ' . $num,
                );
                $num++;
            }
        }
        return $classinfo;
    }
}

