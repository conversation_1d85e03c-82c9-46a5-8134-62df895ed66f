<?php

/**
 * This is the model class for table "ivy_batch_invoices".
 *
 * The followings are the available columns in table 'ivy_batch_invoices':
 * @property integer $id
 * @property string $schoolid
 * @property integer $yid
 * @property string $payment_type
 * @property integer $startyear
 * @property integer $startmonth
 * @property integer $endmonth
 * @property double $amount
 * @property string $title
 * @property integer $status
 * @property integer $create_timestamp
 * @property integer $update_timestamp
 * @property integer $create_userid
 */
class BatchInvoices extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_batch_invoices';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('schoolid, startyear, startmonth, endmonth, amount, title, status, create_timestamp, create_userid', 'required'),
			array('yid, startyear, startmonth, endmonth, status, create_timestamp, update_timestamp, create_userid', 'numerical', 'integerOnly'=>true),
			array('amount', 'numerical'),
			array('schoolid', 'length', 'max'=>25),
			array('payment_type', 'length', 'max'=>50),
			array('title', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, schoolid, yid, payment_type, startyear, startmonth, endmonth, amount, title, status, create_timestamp, update_timestamp, create_userid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'items' => array(self::HAS_MANY, 'BatchInvoicesItem', 'batch_id'),
            'school' => array(self::BELONGS_TO, 'Branch', 'schoolid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'schoolid' => 'Schoolid',
			'yid' => 'Yid',
			'payment_type' => 'Payment Type',
			'startyear' => 'Startyear',
			'startmonth' => 'Startmonth',
			'endmonth' => 'Endmonth',
			'amount' => 'Amount',
			'title' => 'Title',
			'status' => 'Status',
			'create_timestamp' => 'Create Timestamp',
			'update_timestamp' => 'Update Timestamp',
			'create_userid' => 'Create Userid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('payment_type',$this->payment_type,true);
		$criteria->compare('startyear',$this->startyear);
		$criteria->compare('startmonth',$this->startmonth);
		$criteria->compare('endmonth',$this->endmonth);
		$criteria->compare('amount',$this->amount);
		$criteria->compare('title',$this->title,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('create_timestamp',$this->create_timestamp);
		$criteria->compare('update_timestamp',$this->update_timestamp);
		$criteria->compare('create_userid',$this->create_userid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return BatchInvoices the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
