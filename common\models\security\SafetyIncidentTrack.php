<?php

/**
 * This is the model class for table "ivy_safety_incident_track".
 *
 * The followings are the available columns in table 'ivy_safety_incident_track':
 * @property integer $id
 * @property string $school_id
 * @property integer $report
 * @property integer $accident_type
 * @property integer $invidet_report
 * @property integer $incidet_truth
 * @property integer $process_cd
 * @property integer $staff
 * @property integer $media
 * @property integer $education
 * @property integer $parents
 * @property integer $solving
 * @property integer $development_score
 * @property integer $status
 * @property integer $created_at
 * @property integer $created_by
 * @property integer $updated_at
 * @property integer $updated_by
 */
class SafetyIncidentTrack extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_safety_incident_track';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('invidet_report, accident_type, incidet_truth, process_cd, staff, media, education, parents, solving, status, created_at, created_by, updated_at, updated_by', 'required'),
			array('report, invidet_report, accident_type, incidet_truth, process_cd, staff, media, education, parents, solving, development_score, status, created_at, created_by, updated_at, updated_by', 'numerical', 'integerOnly'=>true),
			array('school_id', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, school_id, report, invidet_report, accident_type, incidet_truth, process_cd, staff, education, media, parents, solving, development_score, status, created_at, created_by, updated_at, updated_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'school_id' => 'School',
			'report' => 'Report',
			'accident_type' => 'Accident Type',
			'invidet_report' => 'Invidet Report',
			'incidet_truth' => 'Incidet Truth',
			'process_cd' => 'Process Cd',
			'staff' => 'Staff',
			'media' => 'Media',
			'education' => 'Education',
			'parents' => 'Parents',
			'solving' => 'Solving',
			'development_score' => 'Development Score',
			'status' => 'Status',
			'created_at' => 'Created At',
			'created_by' => 'Created By',
			'updated_at' => 'Updated At',
			'updated_by' => 'Updated By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('school_id',$this->school_id,true);
		$criteria->compare('report',$this->report);
		$criteria->compare('invidet_report',$this->invidet_report);
		$criteria->compare('incidet_truth',$this->incidet_truth);
		$criteria->compare('process_cd',$this->process_cd);
		$criteria->compare('solving',$this->solving);
		$criteria->compare('development_score',$this->development_score);
		$criteria->compare('status',$this->status);
		$criteria->compare('created_at',$this->created_at);
		$criteria->compare('created_by',$this->created_by);
		$criteria->compare('updated_at',$this->updated_at);
		$criteria->compare('updated_by',$this->updated_by);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return SafetyIncidentTrack the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
