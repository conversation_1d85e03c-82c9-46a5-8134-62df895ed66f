<?php 
/**
* 通联支付
*/
class Allinpay 
{
	public $return_url = "";
	public $notify_url = "";
	public $schoolid = "";
	public $sign_type = "MD5";
	public $transport = "http";
	public $schoolNo = "";

	public $key = "";
	public $serverUrl = 'http://ceshi.allinpay.com/gateway/index.do';		#请求地址		
	public $pickupUrl = '';
	public $receiveUrl = '';		
	public $inputCharset = '1';		
	public $version = 'v1.0';		
	public $language = '';		
	public $signType = '0';		#签名方式，1：证书；0：md5		
	public $merchantId = '';	#商户号		
	public $payerName = '';		
	public $payerEmail = '';		
	public $payerTelephone = '';			
	public $pid = '';		
	public $orderNo = '';
	public $orderAmount = '';
	public $orderCurrency = '';
	public $orderDatetime = '';
	public $orderExpireDatetime = '';
	public $productName = '';
	public $productPrice = '';
	public $productNum = '';
	public $productId = '';
	public $productDesc = '';
	public $ext1 = '';
	public $ext2 = '';
	public $customsExt = '';		
	public $extTL = '';
	public $payType = '';		
	public $issuerId = '';
	public $pan = '';	
	public $tradeNature = 'GOODS';

	public $paymentOrderId = "";
	public $payDatetime = "";
	public $payResult = "";
	public $errorCode = "";
	public $returnDatetime = "";

	public $returnSignMsg = "";

	public $signMsg = '';		#签名串
	public $form = '';

	public function __construct($schoolid) {
	    $this->schoolid = $schoolid;
	    $this->init();
	}

	public function init() {
	    $allinpayPartnerInfo = CommonUtils::LoadConfig('CfgAllinpayPartnerInfo');
	    if (!empty($allinpayPartnerInfo[$this->schoolid])) {
	        $this->merchantId = $allinpayPartnerInfo[$this->schoolid]['partner'];
	        $this->key = $allinpayPartnerInfo[$this->schoolid]['key'];
	        $this->schoolNo = $allinpayPartnerInfo[$this->schoolid]['number_code'];
	    }
	}

	/**
	 * 构建签名串
	 * @return [string] $signMsg [签名串]
	 */
	public function bulidSignMsg()
	{
		if ($this->sign_type == 'MD5') {
			$bufSignSrc = '';
			if($this->inputCharset != "")
				$bufSignSrc=$bufSignSrc."inputCharset=".$this->inputCharset."&";		
			if($this->pickupUrl != "")
				$bufSignSrc=$bufSignSrc."pickupUrl=".$this->pickupUrl."&";		
			if($this->receiveUrl != "")
				$bufSignSrc=$bufSignSrc."receiveUrl=".$this->receiveUrl."&";		
			if($this->version != "")
				$bufSignSrc=$bufSignSrc."version=".$this->version."&";		
			if($this->language != "")
				$bufSignSrc=$bufSignSrc."language=".$this->language."&";		
			if($this->signType != "")
				$bufSignSrc=$bufSignSrc."signType=".$this->signType."&";		
			if($this->merchantId != "")
				$bufSignSrc=$bufSignSrc."merchantId=".$this->merchantId."&";		
			if($this->payerName != "")
				$bufSignSrc=$bufSignSrc."payerName=".$this->payerName."&";		
			if($this->payerEmail != "")
				$bufSignSrc=$bufSignSrc."payerEmail=".$this->payerEmail."&";		
			if($this->payerTelephone != "")
				$bufSignSrc=$bufSignSrc."payerTelephone=".$this->payerTelephone."&";				
			if($this->pid != "")
				$bufSignSrc=$bufSignSrc."pid=".$this->pid."&";		
			if($this->orderNo != "")
				$bufSignSrc=$bufSignSrc."orderNo=".$this->orderNo."&";
			if($this->orderAmount != "")
				$bufSignSrc=$bufSignSrc."orderAmount=".$this->orderAmount."&";
			if($this->orderCurrency != "")
				$bufSignSrc=$bufSignSrc."orderCurrency=".$this->orderCurrency."&";
			if($this->orderDatetime != "")
				$bufSignSrc=$bufSignSrc."orderDatetime=".$this->orderDatetime."&";
			if($this->orderExpireDatetime != "")
				$bufSignSrc=$bufSignSrc."orderExpireDatetime=".$this->orderExpireDatetime."&";
			if($this->productName != "")
				$bufSignSrc=$bufSignSrc."productName=".$this->productName."&";
			if($this->productPrice != "")
				$bufSignSrc=$bufSignSrc."productPrice=".$this->productPrice."&";
			if($this->productNum != "")
				$bufSignSrc=$bufSignSrc."productNum=".$this->productNum."&";
			if($this->productId != "")
				$bufSignSrc=$bufSignSrc."productId=".$this->productId."&";
			if($this->productDesc != "")
				$bufSignSrc=$bufSignSrc."productDesc=".$this->productDesc."&";
			if($this->ext1 != "")
				$bufSignSrc=$bufSignSrc."ext1=".$this->ext1."&";

			//如果海关扩展字段不为空，需要做个MD5填写到ext2里
			if($this->ext2 == "" && $this->customsExt != "")
			{
				$this->ext2 = strtoupper(md5($this->customsExt));
				$bufSignSrc=$bufSignSrc."ext2=".$this->ext2."&";
			}
			else if($this->ext2 != "")
			{
				$bufSignSrc=$bufSignSrc."ext2=".$this->ext2."&";
			}
				
			if($this->extTL != "")
				$bufSignSrc=$bufSignSrc."extTL".$this->extTL."&";
			if($this->payType != "")
				$bufSignSrc=$bufSignSrc."payType=".$this->payType."&";		
			if($this->issuerId != "")
				$bufSignSrc=$bufSignSrc."issuerId=".$this->issuerId."&";
			if($this->pan != "")
				$bufSignSrc=$bufSignSrc."pan=".$this->pan."&";	
			if($this->tradeNature != "")
				$bufSignSrc=$bufSignSrc."tradeNature=".$this->tradeNature."&";
			//通联返回的部分参数
			if($this->paymentOrderId != "")
				$bufSignSrc=$bufSignSrc."paymentOrderId=".$this->paymentOrderId."&";
			if($this->payDatetime != "")
				$bufSignSrc=$bufSignSrc."payDatetime=".$this->payDatetime."&";
			if($this->payResult != "")
				$bufSignSrc=$bufSignSrc."payResult=".$this->payResult."&";
			if($this->errorCode != "")
				$bufSignSrc=$bufSignSrc."errorCode=".$this->errorCode."&";
			if($this->returnDatetime != "")
				$bufSignSrc=$bufSignSrc."returnDatetime=".$this->returnDatetime."&";

			$bufSignSrc=$bufSignSrc."key=".$this->key; //key为MD5密钥，密钥是在通联支付网关商户服务网站上设置。

			//签名，设为signMsg字段值。
			$this->signMsg = strtoupper(md5($bufSignSrc));	
		}
		return $this->signMsg;
	}

	/**
	 * 构建提交表单
	 * @return [string] [表单内容]
	 */
	public function bulidForm()
	{
		$this->form .= "<form action='{$this->serverUrl}' method='post' name='frmOnlinePaySubmit' id='frmOnlinePaySubmit' >";
		$this->form .= "<input type='hidden' value='{$this->inputCharset}' name='inputCharset' id='inputCharset'>";
		$this->form .= "<input type='hidden' value='{$this->pickupUrl}' name='pickupUrl' id='pickupUrl'>";
		$this->form .= "<input type='hidden' value='{$this->receiveUrl}' name='receiveUrl' id='receiveUrl'>";
		$this->form .= "<input type='hidden' value='{$this->version}' name='version' id='version'>";
		$this->form .= "<input type='hidden' value='{$this->language}' name='language' id='language'>";
		$this->form .= "<input type='hidden' value='{$this->signType}' name='signType' id='signType'>";
		$this->form .= "<input type='hidden' value='{$this->merchantId}' name='merchantId' id='merchantId'>";
		$this->form .= "<input type='hidden' value='{$this->payerName}' name='payerName' id='payerName'>";
		$this->form .= "<input type='hidden' value='{$this->payerEmail}' name='payerEmail' id='payerEmail'>";
		$this->form .= "<input type='hidden' value='{$this->payerTelephone}' name='payerTelephone' id='payerTelephone'>";
		$this->form .= "<input type='hidden' value='{$this->payerIDCard}' name='payerIDCard' id='payerIDCard'>";
		$this->form .= "<input type='hidden' value='{$this->pid}' name='pid' id='pid'>";
		$this->form .= "<input type='hidden' value='{$this->orderNo}' name='orderNo' id='orderNo'>";
		$this->form .= "<input type='hidden' value='{$this->orderAmount}' name='orderAmount' id='orderAmount'>";
		$this->form .= "<input type='hidden' value='{$this->orderCurrency}' name='orderCurrency' id='orderCurrency'>";
		$this->form .= "<input type='hidden' value='{$this->orderDatetime}' name='orderDatetime' id='orderDatetime'>";
		$this->form .= "<input type='hidden' value='{$this->orderExpireDatetime}' name='orderExpireDatetime' id='orderExpireDatetime'>";
		$this->form .= "<input type='hidden' value='{$this->productName}' name='productName' id='productName'>";
		$this->form .= "<input type='hidden' value='{$this->productPrice}' name='productPrice' id='productPrice'>";
		$this->form .= "<input type='hidden' value='{$this->productNum}' name='productNum' id='productNum'>";
		$this->form .= "<input type='hidden' value='{$this->productId}' name='productId' id='productId'>";
		$this->form .= "<input type='hidden' value='{$this->productDesc}' name='productDesc' id='productDesc'>";
		$this->form .= "<input type='hidden' value='{$this->ext1}' name='ext1' id='ext1'>";
		$this->form .= "<input type='hidden' value='{$this->ext2}' name='ext2' id='ext2'>";
		$this->form .= "<input type='hidden' value='{$this->extTL}' name='extTL' id='extTL'>";
		$this->form .= "<input type='hidden' value='{$this->payType}' name='payType' id='payType'>";
		$this->form .= "<input type='hidden' value='{$this->issuerId}' name='issuerId' id='issuerId'>";
		$this->form .= "<input type='hidden' value='{$this->pan}' name='pan' id='pan'>";
		$this->form .= "<input type='hidden' value='{$this->tradeNature}' name='tradeNature' id='tradeNature'>";
		$this->form .= "<input type='hidden' value='{$this->customsExt}' name='customsExt' id='customsExt'>";
		$this->form .= "<input type='hidden' value='{$this->signMsg}' name='signMsg' id='signMsg'>";
		$this->form .= "</form>";

		return $this->form;
	}

	/**
	 * 获取真实IP
	 * @return [IP]
	 */
	public function getRealIp() {
	    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {   //check ip from share internet
	        $ip = $_SERVER['HTTP_CLIENT_IP'];
	    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {   //to check ip is pass from proxy
	        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
	    } else {
	        $ip = $_SERVER['REMOTE_ADDR'];
	    }
	    return $ip;
	}

	/**
	 * 生成订单ID
	 * @param  [type] $invoice_id [账单id]
	 * @return [type]             [订单id]
	 */
	public function generateOrderId($invoice_id)
	{		
		$schoolNo = $this->schoolNo;

		$invoiceId = sprintf("%06s\n", $invoice_id);

		$orderIdPrefix = $schoolNo.$invoice_id;
		
		$postfix = AlipayOrder::model()->getPostfix($orderIdPrefix);

		$this->orderNo = trim($orderIdPrefix.$postfix);

		return $this->orderNo;
	}

	/**
	 * 根据通联返回的信息构建签名并验证
	 * @param  [type] $_POST [通联返回的POST信息]
	 * @return [bool]        [签名验证结果]
	 */
	public function verifySignMsg($_POST)
	{
		$this->merchantId=$_POST["merchantId"];
		$this->version=$_POST['version'];
		$this->language=$_POST['language'];
		$this->signType=$_POST['signType'];
		$this->payType=$_POST['payType'];
		$this->issuerId=$_POST['issuerId'];
		$this->paymentOrderId=$_POST['paymentOrderId'];
		$this->orderNo=$_POST['orderNo'];
		$this->orderDatetime=$_POST['orderDatetime'];
		$this->orderAmount=$_POST['orderAmount'];
		$this->payDatetime=$_POST['payDatetime'];
		$this->payAmount=$_POST['payAmount'];
		$this->ext1=$_POST['ext1'];
		$this->ext2=$_POST['ext2'];
		$this->payResult=$_POST['payResult'];
		$this->errorCode=$_POST['errorCode'];
		$this->returnDatetime=$_POST['returnDatetime'];

		$this->returnSignMsg=$_POST["signMsg"];

		$bufSignSrc="";
		if($this->merchantId != "")
			$bufSignSrc=$bufSignSrc."merchantId=".$this->merchantId."&";      
		if($this->version != "")
			$bufSignSrc=$bufSignSrc."version=".$this->version."&";        
		if($this->language != "")
			$bufSignSrc=$bufSignSrc."language=".$this->language."&";      
		if($this->signType != "")
			$bufSignSrc=$bufSignSrc."signType=".$this->signType."&";      
		if($this->payType != "")
			$bufSignSrc=$bufSignSrc."payType=".$this->payType."&";
		if($this->issuerId != "")
			$bufSignSrc=$bufSignSrc."issuerId=".$this->issuerId."&";
		if($this->paymentOrderId != "")
			$bufSignSrc=$bufSignSrc."paymentOrderId=".$this->paymentOrderId."&";
		if($this->orderNo != "")
			$bufSignSrc=$bufSignSrc."orderNo=".$this->orderNo."&";
		if($this->orderDatetime != "")
			$bufSignSrc=$bufSignSrc."orderDatetime=".$this->orderDatetime."&";
		if($this->orderAmount != "")
			$bufSignSrc=$bufSignSrc."orderAmount=".$this->orderAmount."&";
		if($this->payDatetime != "")
			$bufSignSrc=$bufSignSrc."payDatetime=".$this->payDatetime."&";
		if($this->payAmount != "")
			$bufSignSrc=$bufSignSrc."payAmount=".$this->payAmount."&";	
		if($this->ext1 != "")
			$bufSignSrc=$bufSignSrc."ext1=".$this->ext1."&";
		if($this->ext2 != "")
			$bufSignSrc=$bufSignSrc."ext2=".$this->ext2."&";
		if($this->payResult != "")
			$bufSignSrc=$bufSignSrc."payResult=".$this->payResult."&";
		if($this->errorCode != "")
			$bufSignSrc=$bufSignSrc."errorCode=".$this->errorCode."&";
		if($this->returnDatetime != "")
			$bufSignSrc=$bufSignSrc."returnDatetime=".$this->returnDatetime."&";

		$bufSignSrc=$bufSignSrc."key=".$this->key; //key为MD5密钥，密钥是在通联支付网关商户服务网站上设置。

		//签名，设为signMsg字段值。
		$this->signMsg = strtoupper(md5($bufSignSrc));

		if ($this->signMsg == $this->returnSignMsg) {
			return true;
		}
		return false;
	}

	/**
	 * 转换订单金额单位为元
	 * @return [type] [订单金额]
	 */
	public function getRealMoney()
	{
		return $this->orderAmount*0.01;
	}

}
