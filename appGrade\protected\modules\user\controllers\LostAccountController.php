<?php

class LostAccountController extends Controller
{
	public $defaultAction = 'index';
	public $layout = '//layouts/auth.blank';

    public function actions() {
        return array(
            'captcha' => array(
                'class' => 'CCaptchaAction',
                'backColor' => 0xFFFFFF,
                'foreColor' => 0x666666,
            ),
        );
    }

    /**
	 * Displays the multiple children selection page
	 */
	public function actionIndex()
	{
		$this->setSubPageTitle(Yii::t("auth","What is my IvyOnline account?"));
		if (Yii::app()->user->id) {
            $this->redirect(Yii::app()->controller->module->returnUrl);
        } else {
            Yii::import('common.models.support.LostAccount');
            Mims::LoadHelper('HtoolKits');
            
            $schoolsel = array(''=>Yii::t("global", 'Please Select'));
            $criteria = new CDbCriteria;
            $criteria->compare('status', 10);
            $criteria->compare('type', 20);
            $schools = Branch::model()->with()->findAll($criteria);
            foreach ($schools as $school){
                $schoolsel[Yii::app()->language == 'zh_cn' ? $school->cityInfo->cntitle : $school->cityInfo->entitle][$school->branchid] = $school->title;
            }
            
            $model = new LostAccount;
            
            if (isset($_POST['LostAccount'])){
                $schoolsupport = array();
                foreach ($schools as $school){
                    $schoolsupport[$school->branchid] = array(
                        'title' => $school->title,
                        'email' => $school->info->support_email,
                    );
                }
                
                $model->attributes = $_POST['LostAccount'];
                $model->birthday = strtotime($_POST['LostAccount']['birthday']);
                $model->ip = HtoolKits::getIpAddress();
                $model->timestamp = time();
                if ($model->save()){
                    $sendEmail = $this->widget('ext.sendEmail.Send', array(
                        'viewName' => 'lostAccount',
                        'toParty' => true, //发送至 当事人 -参数 party_email
                        'toParent' => false, //发送至 父母 -
                        'toSupport' => false, //发送至 校园支持 -参数 support_email
                        'ccSpouse' => false, //抄送至 配偶 -
                        'ccParent' => false, //抄送至 父母 -
                        'ccSupport' => true, //抄送至 校园支持 -参数 support_email
                        'bccItDev' => true, // 密抄至 ItDev -
                        'replyToSupport' => false, // 回复至 校园支持 -参数 support_email
                        'params' => array(
                            'party_email' => $schoolsupport[$model->schoolid]['email'],
                            'support_email' => $model->email,
                            'date_time' => time(),
                            'model' => $model,
                            'school' => $schoolsupport[$model->schoolid]['title'],
                        ),
                    ));
                    Yii::app()->user->setFlash('success', Yii::t("auth", "Thank you for your submission. Our campus support team will follow up on this request shortly."));
                }
                else {
                    $model->birthday = $_POST['LostAccount']['birthday'];
//                    print_r($model->getErrors());
                }
            }
            
            Yii::app()->clientScript->registerCssFile(Yii::app()->theme->baseUrl . '/css/lostaccount.css');
            
			$this->render('/user/lostaccount', array('model'=>$model, 'schoolsel'=>$schoolsel));
		}
		//if ( UserModule::checkLogin() ){
		//	$children = ChildProfileBasic::model()->findAllByPk(Yii::app()->user->getAdminCids());
		//	if ( count($children) === 1){
		//		$this->redirect(array("//child/profile/welcome", "childid"=>$children[0]->childid));
		//	}elseif( count($children) > 1){
		//		$this->render('/user/select',array('children'=>$children));
		//	}else{
		//		Yii::app()->user->logout();
		//		$this->redirect(Yii::app()->controller->module->lostAccountUrl);
		//	}
		//}
	}

}