<?php
if (true === $this->lang || 'cn' == $this->lang) {
    $df_cn = new CDateFormatter('zh-cn');
    $this->render('mailHeader_lang', array('lang' => 'cn'));
    ?>

    <div style="font-family: 'Microsoft Yahei'">

        <p>班级：&nbsp;&nbsp;<?php echo $classTitle; ?><br/>
            孩子姓名：&nbsp;&nbsp;<?php echo $childName; ?><br/>
            家长Email：&nbsp;&nbsp;<?php echo str_replace(';', ';&nbsp;&nbsp;',$parentEmail); ?></p>

        <p>
            <?php echo $content; ?>
        </p>

        <p>
            时间：<?php echo $df_cn->formatDateTime($dateTime, 'medium', 'short'); ?></p>

    </div>

    <?php
    $this->render('mailFooter_lang', array('lang' => 'cn'));
}
?>


<?php
if (true === $this->lang || 'en' == $this->lang) {
    $df_en = new CDateFormatter('en-us');
    $this->render('mailHeader_lang', array('lang' => 'en'));
    ?>

    <div style="font-family: 'Trebuchet MS','Microsoft Yahei',Arial,Helvetica,sans-serif">

        <p>Class:&nbsp;&nbsp;<?php echo $classTitle; ?><br/>
            Child Name:&nbsp;&nbsp;<?php echo $childName; ?><br/>
            Parent Email:&nbsp;&nbsp;<?php echo str_replace(';', ';&nbsp;&nbsp;',$parentEmail); ?></p>

        <p>
            <?php echo $content; ?>
        </p>

        <p>
            <?php echo $df_en->formatDateTime($dateTime, 'medium', 'short'); ?></p>


    </div>
    <?php
    $this->render('mailFooter_lang', array('lang' => 'en'));
}
?>



