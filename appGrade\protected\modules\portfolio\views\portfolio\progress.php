<?php
$this->breadcrumbs=array(
	'Portfolio'=>array('/portfolio/portfolio/classes'),
	Yii::t("navigations",'Semester Report'),
);?>

<div id="left-col" class="no-bg span-6">
    <ul class="sub-nav">
        <?php foreach ($reportsList as $sy=>$ms):?>
        <li <?php if ($sy == $startyear):?>class="active"<?php endif;?>><a href="<?php echo Yii::app()->getController()->createUrl('//portfolio/portfolio/progress', array('startyear'=>$sy))?>"><?php echo IvyClass::formatSchoolYear($sy)?> <?php echo Yii::t("portfolio", 'School Year');?></a><em></em></li>
        <?php endforeach;?>
    </ul>
</div>

<div id="main-col" class="span-18 last">
    <?php if ($reportHtml):?>
	<div class="semester-report">
		<div class="header">
		</div>
		<div class="main-content">
			<?php echo $reportHtml;?>
			<div class="clear"></div>
		</div>
		<div class="foot"></div>
    </div>
    
    <div style="margin-top: 15px;">
		<p>
		<span class="btn001">
        <?php echo CHtml::link('<span>'.Yii::t("portfolio", 'Download PDF').'</span>', Yii::app()->getController()->createUrl('//portfolio/portfolio/semesterpdf', array('id'=>max($reportsList[$startyear]), 'startyear' => $startyear)), array("target"=>"_blank"))?>
		</span>
		</p>
    </div>
	
	<?php else:?>
	<p>
		<?php echo Yii::t("portfolio","The Progress Report will be available online at the end of the semester. Please check back later. Thank you.");?>
	</p>	
    <?php endif;?>
</div>

<style>
    .pta-items li span a{
        text-decoration:none;
    }
    .pta-items li span a span{
        display: block;
        text-align: center;
        padding: 20px 0;
        background: #F36601;
        margin-bottom: 6px;
        font-size: 20px;
        color:#fff;
    }
</style>