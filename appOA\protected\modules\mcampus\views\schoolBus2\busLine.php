<style>
    [v-cloak] {
		display: none;
	}
    #mapExample{
        width: 100%;
    }
    .amap-info-content{
        border-radius: 8px;
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.08);
    }
    .amap-info-close{
        right:5px !important;
        top:9px !important
    }
    .colorList{
        width:10px;
        height:10px;
        display: inline-block;
    }
    .checkbox-inline ,.checkbox-inline + .checkbox-inline{
        margin-right:16px;
        margin-left:0;
        width:35px;
        font-weight:bold
    }
    .checkbox-inline.last-item {
        margin-right: 0;
    }
    .checkbox input[type="checkbox"], .checkbox-inline input[type="checkbox"]{
        margin-left: -18px
    }
    .legend{
        position: absolute;
        right:0;
        top:0;
        z-index:999;
        background:#fff;
        border-radius:4px;
        max-width: 500px;
        overflow: auto;
        white-space: nowrap;
        max-height:500px;
        padding:16px;
        box-shadow: 0px 2px 6px 0px rgba(0,0,0,0.08);
    }
    .number {
        width: 13px;
        height: 13px;
        background: #ebf4ff;
        font-weight: 500;
        border-radius: 50%;
        font-size: 12px;
        color: #4396ff;
        line-height: 13px;
        text-align: center;
        margin-right: 6px;
        display: inline-block;
    }

    .time {
        display: inline-block;
        width: 30px;
        line-height: 13px;
        text-align: center;
    }
    .line {
        position: absolute;
        width: 1px;
        height: 13px;
        background: #c8c9cc;
        left: 6.5px;
        top: -11px;
    }
</style>
<div class="container-fluid" id='box' v-cloak>
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('schoolBus', '校车管理'); ?></li>
    </ol>
    <div class='loadingData' >
        <div class='menu'>
            <div class='mb16'>
                <el-select v-model="startYear" size='small' @change='getSiteList()' placeholder="请选择">
                    <el-option
                    v-for="item in startYearList"
                    :key="item.key"
                    :label="item.value"
                    :value="item.key">
                    </el-option>
                </el-select>
                <div class="btn-group ml24" role="group" aria-label="..." v-if='routesData.length'>
                    <button type="button" class="btn" :class='siteLineType=="1"?"btn-primary":"btn-default"' @click='tabLineType("1")'><?php echo Yii::t("coll", "pick-up"); ?></button>
                    <button type="button" class="btn" :class='siteLineType=="2"?"btn-primary":"btn-default"' @click='tabLineType("2")'><?php echo Yii::t("coll", "drop-off"); ?></button>
                </div>
                <button type="button" class="btn btn-primary pull-right" :disabled='refreshBtn' @click='refreshData("refresh")'><span class='glyphicon glyphicon-refresh'></span> {{refreshBtn?"刷新中":"刷新"}}</button>
            </div>
            <div v-if='lineList.routes'>
                <label class="checkbox-inline" :class="{ 'last-item': index === routesData.length - 1 }" v-for='(list,index) in routesData'>
                    <input type="checkbox" v-model='checkLine' :value="index" @change='viewLineRoute()'><span class='' :style="'color:'+colorList[index]">{{list.title}}</span> 
                </label>
            </div>
        </div>
        <div class='mt10 relative' >
            <div class='legend' v-if='checkLine.length'>
                <div class='flex'>
                    <div v-for='(list,index) in reversedCheckLine' class=' pl8 pr8'>
                        <div class='font14 color3 fontBold mb16'>{{lineList.routes[list].title}}</div>
                        <div v-for='(item,i) in legendData[list]' class='relative' :class='i!=0?"mt8":""'>
                            <span class="line" v-if='i!=0'></span>
                            <span class='number'>{{i+1}}</span>
                            <span class="time" :class=" item.type==2?'color9':'color6'">{{ item.sid==schoolSids?"---":siteLineType == 1 ? item.pickup_time : item.dropoff_time }}</span>
                            <span class="flex1">{{lineList.sitesObj[item.sid].title }}</span>
                        </div>
                    </div>
                </div>
            </div>
            <div id='mapExample' :style="'height:' + (height-305) + 'px'" >
            <div id="overlay" style="position:absolute;top:0;left:0;width:100%;height:100%;background:rgba(255,255,255,0.5);pointer-events:none;"></div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
  window._AMapSecurityConfig = {
    serviceHost: "https://apps.ivyonline.cn/_AMapService",
  };
</script>
<script type="text/javascript" src="https://webapi.amap.com/maps?v=1.4.12&key=166c9d81f042ef0ae55c7a2c85e19a7e&plugin=AMap.Driving"></script>
<script>
    var height=document.documentElement.clientHeight;
    var container=new Vue({
        el: "#box",
        data: {
            height:height,
            startYearList: [],
            startYear:'',
            map:null,
            carSiteList:[],
            carRouterData:{},
            lineList:{},
            siteLineType:'1',
            currentRouteSite:'',
            lat: "", //上学放学字段
            lon: "", //上学放学字段
            pointLine: "",
            routeLine: {}, //当前线路
            lineMarker: [], //当前线路点
            checkLine:[],
            polylines:[],
            markerList:[],
            routesData:[],
            schoolSids:'',
            markers:{},
            refreshBtn:false,
            colorList:['#c03935','#005f98','#942413','#008e9c','#a6217f','#d29700','#f5c587','#169a6c','#90c232','#189cbe','#E7AF92','#835634','#E6D42F','#D4a7a2','#5b2e67','#77a237','#25989A','#CF9CC2','#094d9b','#dd84b2','#e21278','#e2612e','#a29bbb','#b35a20','#e31025','#e4071c','#4396FF','#f296a0','#96ce97']
        },
        created: function() {
			let that=this
            $.ajax({
                url: '<?php echo $this->createUrl("api") ?>',
                type: "post",
                dataType: 'json',
                data:{
                    url:'indexData',
                },
                success: function(data) {
                    if (data.state == 'success') {
                       that.startYearList=data.data.startYearList
                       let year=data.data.startYearList.filter((i) => i.current ==1)
                       that.startYear=year[0].key
                       that.getSiteList() 
                    }else{
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                },
                error: function(data) {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                },
            })
        },
        computed: {
            reversedCheckLine() {
                return [...this.checkLine].reverse();
            },
            legendData() {
                const result = {};
                const isPickup = this.siteLineType === '1';
                const lineTime = isPickup ? "pickup_time" : "dropoff_time";
                this.checkLine.forEach(list => {
                    const id = this.lineList.routes[list].id;
                    const lineListSource = isPickup
                        ? this.lineList.route2Site[id]
                        : this.lineList.route2Site_drop[id];
                    const mapList = lineListSource.filter(item => {
                        if (item.sid === this.schoolSids) {
                        return true;
                        } else {
                        return item[lineTime] !== null && item[lineTime].trim() !== '';
                        }
                    });
                    result[list] = mapList;
                });
                return result;
            }
        },
        methods: {
            getSiteList(type){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        url:'siteList',
                        start_year:this.startYear
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            let schoolSids = data.data.siteData.filter(site => site.type == 2)
                            that.schoolSids=schoolSids[0].id
                            that.carSiteList=data.data.siteData
                            that.carRouterData=data.data.routerData
                            if(!type){
                                that.checkLine=[]
                            }
                            that.getLineList(type)
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            init() {
                this.map = new AMap.Map("mapExample", {
                    zoom: 12,
                    center: [116.39, 39.92],
                    resizeEnable: true,
                    mapStyle: "amap://styles/whitesmoke", 
                });
			},
            getLineList(type){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("api") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        url:'routeList',
                        start_year:this.startYear,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.routesData=JSON.parse(JSON.stringify(data.data.routes));
                            that.routesData.forEach(item => {
                                const num = parseInt(item.title.replace('Line #', ''), 10);
                                item.title = num < 10 ? `0${num}` : `${num}`;
                            });
                            that.lineList=data.data
                            let carSiteList=JSON.parse(JSON.stringify(that.carSiteList));
                            var sitesObj={}
                            carSiteList.forEach(item => {
                                sitesObj[item.id]=item
                            });
                            that.lineList.sitesObj=sitesObj
                            let route2Site_drop={}
                            let route2Site=JSON.parse(JSON.stringify(data.data.route2Site));
                            for(let key in route2Site){
                                route2Site_drop[key]=route2Site[key].sort(function(a, b) {
                                    return a.drop_weight - b.drop_weight;
                                })
                            }
                            that.lineList.route2Site_drop=route2Site_drop  
                            that.$nextTick(() => {
                                var element = document.querySelector('.menu');
                                var cssheight = element.offsetHeight;
                                var bodyheight=document.documentElement.clientHeight;
                                let height=bodyheight-cssheight-200+'px'
                                document.getElementById('mapExample').style.height =height; 
                                if(!type){
                                    that.init()
                                }
                            })
                            that.refreshBtn=false
                            if(type){
                                that.viewLineRoute(type)
                                resultTip({
                                    msg: '刷新成功'
                                });
                            }
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            tabLineType(type,refresh){
                this.siteLineType=type
                this.viewLineRoute()
            },
            removeMarker(){
                if (this.markers) this.map.remove(this.markers);
                if (this.lineMarker && this.lineMarker.length > 0) {
                    this.map.remove(this.lineMarker);
                    this.lineMarker = [];
                }
                if (this.routeLine) this.map.remove(this.routeLine);
                if (this.polylines && this.polylines.length > 0) {
                    this.map.remove(this.polylines);
                    this.polylines = [];
                }
                if (this.markerList && this.markerList.length > 0) {
                    this.map.remove(this.markerList);
                    this.markerList = [];
                }
                this.map.clearMap(); 
            },
            refreshData(){
                this.refreshBtn=true
                this.getSiteList('refresh')
            },
            viewLineRoute(refresh){
                this.removeMarker()
                if(this.checkLine.length==0){
                    return
                }
                this.checkLine.forEach(list => {
                    let id=this.lineList.routes[list].id
                    const isPickup = this.siteLineType === '1';
                    let lineListSource = isPickup ? this.lineList.route2Site[id] : this.lineList.route2Site_drop[id];
                    this.lat = isPickup ? "pickup_latitude" : "dropoff_latitude";
                    this.lon = isPickup ? "pickup_longitude" : "dropoff_longitude";
                    this.pointLine = isPickup ? "pickup_point" : "dropoff_point";
                    let lineTime = isPickup ? "pickup_time" : "dropoff_time";
                    this.mapList = lineListSource.map(id => {
                        const matchedItem = this.carSiteList.find(item => {
                            if (item.type === 1) {
                                return id[lineTime] !== null && id[lineTime] !== '' && item.id === id.sid;
                            } else if (item.type === 2) {
                                return item.id === id.sid;
                            }
                        });
                        return matchedItem;
                    }).filter(item => item);
                    this.drawPolyline(list);
                    this.markPoints(list,refresh);
                });
            },
            markPoints(color,refresh) {
                let markerData=[]
                var infoWindow = new AMap.InfoWindow({
                    anchor: 'bottom-center',
                });
                let firstIcon = new AMap.Icon({
                    image: "https://m2.files.ivykids.cn/cloud01-file-8025768Fo2S6IiXsrTrlVvmjXGXBxekmIpo.png",
                    size: new AMap.Size(25, 34),
                    imageSize: new AMap.Size(25, 34),
                });
                let lastIcon = new AMap.Icon({
                    image: "https://m2.files.ivykids.cn/cloud01-file-8025768FkLP4hoEGFMUuKCbOqDJ59vtJBGO.png",
                    size: new AMap.Size(25, 34),
                    imageSize: new AMap.Size(25, 34),
                });
                let middleIcon = null;
                this.mapList.forEach((item, index) => {
                    const isFirst = index === 0;
                    const isLast = index + 1 === this.mapList.length;
                    let content = '';
                    let icon = null;
                    let offset = new AMap.Pixel(-10, -10);
                    if (isFirst || isLast) {
                        icon = isFirst ? firstIcon : lastIcon;
                        offset = new AMap.Pixel(-12, -36);
                    } else {
                        content = `<div style="width:20px;height:20px;border-radius:50%;background:${this.colorList[color]};border:1px solid #fff;color:#fff;text-align:center;line-height:18px;font-weight:bold">${index+1}</div>`;
                    }
                   this.markers = new AMap.Marker({
                        icon: icon,
                        content: content,
                        map: this.map,
                        offset: offset,
                        extData: item, // 保存数据供点击事件使用
                        position: new AMap.LngLat(item[this.lon], item[this.lat]),
                    });
                    this.markers.on("click", (e) => {
                        const item = e.target.getExtData();
                        this.currentRouteSite = item.id;
                        const markerContent = this.createMarkerContent(item,color);
                        infoWindow.setContent(markerContent);
                        infoWindow.open(this.map, e.lnglat);
                    });
                    this.map.add(this.markers);
                    this.lineMarker.push(this.markers);
                    markerData.push(this.markers);
                });
                if(!refresh){
                    this.map.setFitView(markerData);
                }
            },
            markerClick(e,content) {
                var infoWindow = new AMap.InfoWindow({
                    anchor: 'bottom-center',
                    content:content
                });
                infoWindow.setContent(e.target.content);
                infoWindow.open(this.map, e.target.getPosition());
            },
            createMarkerContent(item,index) {
                return `<div class="mr20 pl5">
                    <div class="title">${item.title}</div>
                    <div class="text">${item[this.pointLine]}</div>
                    <div class="text">${this.lineList.routes[index].title}</div>
                </div>`;
            },
            drawPolyline(index) {
                let that = this;
                var driving = new AMap.Driving({
                    policy: 2,
                });
                var marker = [];
                this.mapList.forEach((item, index) => {
                    if (index != 0 && index < this.mapList.length){ marker.push(new AMap.LngLat(item[this.lon], item[this.lat]))};
                });
                driving.search(
                    new AMap.LngLat(this.mapList[0][this.lon], this.mapList[0][this.lat]),
                    new AMap.LngLat(this.mapList[this.mapList.length - 1][this.lon], this.mapList[this.mapList.length - 1][this.lat]),
                    {
                    waypoints: marker,
                    },
                    function (status, result) {
                    if (status === "complete") {
                        if (result.routes && result.routes.length) {
                        that.drawRoute(result.routes[0],index);
                        }
                    } else {
                        // log.error('获取驾车数据失败：' + result)
                    }
                    }
                );
            },
            drawRoute(route,index) {
                var path = this.parseRouteToPath(route);
                var canvasDir = document.createElement("canvas");
                var width = 24;
                canvasDir.width = width;
                canvasDir.height = width;
                var context = canvasDir.getContext("2d");
                context.strokeStyle = "#fff";
                context.lineJoin = "round";
                context.lineWidth = 10;
                context.moveTo(-4, width - 4);
                context.lineTo(width / 2, 6);
                context.lineTo(width + 4, width - 4);
                context.stroke();
                this.routeLine = new AMap.Polyline({
                    path: path,
                    isOutline: false,
                    showDir: true,
                    dirImg: canvasDir,
                    strokeWeight: 7,
                    strokeOpacity: 1,
                    strokeColor: this.colorList[index],
                    lineJoin: "bevel",
                });
                this.polylines.push(this.routeLine)
                this.map.add(this.routeLine);
            },
            parseRouteToPath(route) {
                var path = [];
                for (var i = 0, l = route.steps.length; i < l; i++) {
                    var step = route.steps[i];
                    for (var j = 0, n = step.path.length; j < n; j++) {
                    path.push(step.path[j]);
                    }
                }
                return path;
            },
            Refresh(){

            }
        }
    })
</script>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
