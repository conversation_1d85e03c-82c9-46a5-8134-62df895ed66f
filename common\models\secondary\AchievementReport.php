<?php

/**
 * This is the model class for table "ivy_achievement_report".
 *
 * The followings are the available columns in table 'ivy_achievement_report':
 * @property integer $id
 * @property string $report_name_cn
 * @property string $report_name_en
 * @property string $schoolid
 * @property integer $calendar
 * @property integer $type
 * @property integer $cycle
 * @property integer $leaders
 * @property integer $start_time
 * @property integer $end_time
 *@property integer $fill_in_end_time
 * @property string $president_meassge_cn
 * @property string $president_meassge_en
 * @property integer $status
 * @property integer $uid
 * @property integer $create_time
 * @property integer $update_time
 */
class AchievementReport extends CActiveRecord
{
    public $ms_principal;
    public $msv_principal;
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_achievement_report';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('report_name_cn, report_name_en,schoolid, calendar, cycle, type, start_time, end_time, fill_in_end_time,leaders, status, uid, create_time, update_time,president_meassge_en,president_meassge_cn, startyear', 'required'),
			array('calendar, start_time, end_time, fill_in_end_time,status, uid, create_time, update_time, startyear', 'numerical', 'integerOnly'=>true),
			array('report_name_cn, cycle, type, leaders, report_name_en, schoolid', 'length', 'max'=>255),
			//array('president_meassge_cn, president_meassge_en', 'length', 'max'=>1000),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, report_name_cn, report_name_en, schoolid, calendar, cycle, type, start_time, end_time,fill_in_end_time, president_meassge_cn, leaders, president_meassge_en, status, uid, create_time, update_time', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			"principalID" => array(self::BELONGS_TO, 'User', "uid"),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'report_name_cn' => Yii::t('curriculum','Cn Title'),
			'report_name_en' => Yii::t('curriculum','En Title'),
			'schoolid' => 'Schoolid',
			'calendar' => 'Calendar',
			'cycle' => Yii::t('principal','Report NO.'),
			'type' => Yii::t('principal','Report Type'),
			'leaders' => Yii::t('principal','Leaders'),
			'start_time' => Yii::t('asainvoice','Start Date'),
			'end_time' => Yii::t('asainvoice','End Date'),
            'fill_in_end_time' => Yii::t('asainvoice','Fill In End Date'),
			'president_meassge_cn' => Yii::t('principal','From Chinese Principal'),
			'president_meassge_en' => Yii::t('principal','From English Principal'),
			'status' => 'Status',
			'uid' => 'Uid',
			'create_time' => 'Create Time',
			'update_time' => 'Update Time',
			'ms_principal' => Yii::t('principal','MS Principle'),
			'msv_principal' => Yii::t('principal','Coordinator'),
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('report_name_cn',$this->report_name_cn,true);
		$criteria->compare('report_name_en',$this->report_name_en,true);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('calendar',$this->calendar);
		$criteria->compare('cycle',$this->cycle);
		$criteria->compare('type',$this->type);
		$criteria->compare('leaders',$this->leaders);
		$criteria->compare('start_time',$this->start_time);
		$criteria->compare('end_time',$this->end_time);
        $criteria->compare('fill_in_end_time',$this->fill_in_end_time);
		$criteria->compare('president_meassge_cn',$this->president_meassge_cn,true);
		$criteria->compare('president_meassge_en',$this->president_meassge_en,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('uid',$this->uid);
		$criteria->compare('create_time',$this->create_time);
		$criteria->compare('update_time',$this->update_time);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AchievementReport the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	public function getTitle()
	{
		switch (Yii::app()->language) {
			case "zh_cn":
				return  $this->report_name_cn ;
				break;
			case "en_us":
				return  $this->report_name_en;
				break;
		}
	}

	public function getPresident()
	{
		switch (Yii::app()->language) {
			case "zh_cn":
				return  $this->president_meassge_cn ;
				break;
			case "en_us":
				return  $this->president_meassge_en;
				break;
		}
	}

	// 获取某个报告区间学生的出勤天数
	public function getStudentsTotalDays()
	{
		Yii::import('common.models.attendance.*');
		$starttime = date("Y-m" ,$this->start_time);
		$endtime = date("Y-m" ,$this->end_time);
		$criteria = new CDbCriteria();
		$criteria->compare('yid', $this->calendar);
		$criteria->compare('month_label', ">=$starttime");
		$criteria->compare('month_label', "<=$endtime");
		$criteria->order = 'month_label';
		$schoolDays = CalendarSchoolDays::model()->findAll($criteria);

		$reset = reset($schoolDays);

		$reset_s = explode(",", $reset->schoolday_array);

		$end = end($schoolDays);
		$end_s = explode(",", $end->schoolday_array);

		$newArr = array();
		foreach($reset_s as $vo){
		    if($vo >=date('d', $this->start_time)){
		        $newArr[] = $vo;
		    };
		}

		$newArr_t = array();
		foreach($end_s as $vo){
		    if($vo <= date('d', $this->end_time)){
		        $newArr_t[] = $vo;
		    };
		}

		array_pop($schoolDays);
		array_shift($schoolDays);
		$totaldays = array();
		if($schoolDays){
		    foreach($schoolDays as $v){
		        $totaldays[] = $v->schoolday;
		    }
		}
		$totaldays[] = count($newArr);
		$totaldays[] = count($newArr_t);
		$totaldays = array_sum($totaldays);
		return $totaldays;
	}

	// 获取某个报告区间某个学生的缺勤天数
	public function getStudentsObsentDays($childid)
	{
		Yii::import('common.models.attendance.*');
		$criteria = new CDbCriteria();
		$criteria->compare('child_id', $childid);
        //事假 病假 旷课为缺勤
		$criteria->compare('type', array(ChildVacation::VACATION_SICK_LEAVE,ChildVacation::VACATION_AFFAIR_LEAVE,ChildVacation::VACATION_ABSENT));
		$criteria->compare('vacation_time_start', ">={$this->start_time}");
		$criteria->compare('vacation_time_end', "<={$this->end_time}");
		$childvacation = ChildVacation::model()->findAll($criteria);

		$yid = $this->calendar;
		$absent_days = array();
		if($childvacation){
		    foreach($childvacation as $item){ 
		        if($item->vacation_time_start == $item->vacation_time_end){
		            $absent_days[$item->vacation_time_start] = $item->vacation_time_start;
		        }else{
		            $calendarSchlloModel = CalendarSchoolDays::model()->countCalendarSchoolday($yid, $item->vacation_time_start, $item->vacation_time_end);

		            foreach($calendarSchlloModel as $calendarItem){
		                foreach ($calendarItem as $key=>$day) {
		                    foreach ($day['day'] as $dayItem) {
		                        $timeDay = $key . $dayItem;
		                        $absent_days[strtotime($timeDay)] = strtotime($timeDay);
		                    }
		                }
		            }
		        }
		    }
		}
		return count($absent_days);
	}
}
