<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link('总部运营', array('/moperation'))?></li>
        <li><?php echo CHtml::link('活动管理', array('/moperation/admissions/index'))?></li>
        <li class="active"><?php echo CommonUtils::autoLang($model->cn_title, $model->en_title)?></li>
    </ol>

    <div class="row">
        <div class="col-md-1 col-sm-2">
            <?php
            $this->widget('zii.widgets.CMenu',array(
                'items'=> $pageSubMenu,
                'id'=>'pageCategory',
                'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked text-right background-gray'),
                'activeCssClass'=>'active',
                'itemCssClass'=>''
            ));
            ?>
        </div>
        <div class="col-md-11 col-sm-10">
            <div class="background-gray p8">
                <?php echo CHtml::link('<span class="glyphicon glyphicon-plus"></span> 添加', array('updatememberivy', 'event_id'=>$model->id), array('class'=>'btn btn-primary J_dialog', 'title'=>'添加'));?>
                <?php echo CHtml::link('<span class="glyphicon glyphicon-log-out"></span> 导出Excel', array('exportivy', 'id'=>$model->id), array('class'=>'btn btn-primary', 'title'=>'导出Excel'));?>
            </div>
            <?php
            $this->widget('ext.ivyCGridView.BsCGridView', array(
                'id'=>'event-memberivy-grid',
                'dataProvider'=>$dataProvider,
                'colgroups'=>array(
                    array(
                        "colwidth"=>array(80,80,80,80,80,80,80,80,80,80,80,80),
                    )
                ),
                'columns'=>array(
                    'parent_name',
                    'telphone',
                    'email',
                    array(
                        'name'=>'schoolid',
                        'value'=>'Yii::app()->controller->branchArr[$data->schoolid]',
                    ),
                    'ivyclass',
                    'child_name',
                    'adult_number',
                    'kid_number',
                    array(
                        'name'=>'timestamp',
                        'value'=>'OA::formatDateTime($data->timestamp, "medium", "short")',
                    ),
                    array(
                        'class'=>'BsCButtonColumn',
                        'updateButtonUrl'=>'Yii::app()->createUrl("/moperation/admissions/updateMemberivy", array("id" => $data->id, "event_id"=>'.$model->id.'))',
                        'deleteButtonUrl'=>'Yii::app()->createUrl("/moperation/admissions/deleteMemberivy", array("id" => $data->id))',
                    ),
                ),
            )); ?>
        </div>
    </div>
</div>