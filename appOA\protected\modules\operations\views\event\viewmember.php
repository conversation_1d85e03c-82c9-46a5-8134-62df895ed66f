<?php
$this->widget('zii.widgets.CMenu',array(
    'items'=> $pageSubMenu,
    'activeCssClass'=>'current',
    'id'=>'page-subMenu',
    'htmlOptions'=>array('class'=>'pageSubMenu')
));
echo CHtml::openTag("div", array("class"=>"c"));
echo CHtml::closeTag("div");
?>

<div class="h_a">活动名称</div>
<div class="mb10 prompt_text">
    <ul>
        <li><?php echo CommonUtils::autoLang($model->cn_title, $model->en_title)?></li>
    </ul>
</div>
<div class="mb10">
    <a href="<?php echo $this->createUrl("/operations/event/updatemember", array('event_id'=>$model->id));?>" class="btn J_dialog" title="添加"><span class="add"></span>添加</a>
    <a href="<?php echo $this->createUrl("/operations/event/import", array('id'=>$model->id));?>" class="btn J_dialog" title="导入名单"><span class="add"></span>导入名单</a>
    <a href="javascript:;" onclick="tocrm();" class="btn" title="批量转为潜客"><span class="add"></span>批量转为潜客</a>
    <a href="<?php echo $this->createUrl("/operations/event/toCrm", array('id'=>$model->id));?>" id="tocrm" class="J_dialog" title="批量转为潜客"></a>
    <a href="<?php echo $this->createUrl("/operations/event/export", array('id'=>$model->id));?>" class="btn" title="导出Excel"><span class="add"></span>导出Excel</a>
</div>

<?php
$this->widget('ext.ivyCGridView.IvyCGridView', array(
	'id'=>'event-member-grid',
	'dataProvider'=>$dataProvider,
    'colgroups'=>array(
        array(
            "colwidth"=>array(80,80,80,80,80,80,80,80,80,80,80,80),
        )
    ),
	'columns'=>array(
		'parent_name',
		'child_name',
        array(
            'name'=>'child_birthday',
            'value'=>'OA::formatDateTime($data->child_birthday)',
        ),
		'email',
		'tel',
		'adult_number',
		'kid_number',
		'address',
		'from',
        array(
            'name'=>'timestamp',
            'value'=>'OA::formatDateTime($data->timestamp, "medium", "short")',
        ),
        array(
            'class'=>'CButtonColumn',
            'template' => '{update} {delete}',
            'updateButtonImageUrl'=>false,
            'deleteButtonImageUrl'=>false,
            'updateButtonUrl'=>'Yii::app()->createUrl("/operations/event/updateMember", array("id" => $data->id, "event_id"=>'.$model->id.'))',
            'deleteButtonUrl'=>'Yii::app()->createUrl("/operations/event/deleteMember", array("id" => $data->id))',
            'updateButtonOptions'=>array('class'=>'J_dialog'),
        ),
	),
)); ?>

<script type="text/javascript">
function tocrm()
{
    if ( confirm("确定将本活动下的所有用户转为潜客？\n若电邮/手机号已在潜客系统中，该用户将不再重复转化。") ){
        $('#tocrm').click();
    }
    return false;
}
</script>