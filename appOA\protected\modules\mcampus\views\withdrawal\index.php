<div  id='container'  v-cloak>
    <div class='container-fluid'>
        <ol class="breadcrumb">
            <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('//mcampus/default/index'))?></li>
            <li><?php echo CHtml::link(Yii::t('site','Routines'), array('//mcampus/default/index'))?></li>
            <li class="active"><?php echo Yii::t('site','Withdrawal Process');?></li>
        </ol>
    </div>
    <div v-if='indexDataList.node'>
        <div class='flex container-fluid pb24'>
            <div class='flex1'>
                <button type="button" class="btn btn-primary mr15 btn-sm" @click='addChild()'><span class='el-icon-plus'></span> <?php echo Yii::t('withdrawal','Add Student');?></button>
                <?php if(Yii::app()->user->checkAccess('ivystaff_it') || Yii::app()->user->checkAccess('ivystaff_depthead')){?>
                    <button type="button" class="btn btn-primary mr15 btn-sm" @click='tasksLink()'><span class='el-icon-s-custom font12'></span>
                        <?php echo Yii::t('withdrawal','Task Owners Settings');?>
                    </button>
                <?php }?>
            </div>
            <div>
                <el-input placeholder="<?php echo Yii::t('withdrawal','Search by student name');?>" v-model="searchTable" size='small' clearable prefix-icon="el-icon-search" class='searchText' @clear='initData(fromType,filterNode,filterTask)'  @keyup.enter.native='searchStu'>
                    <template slot="append" ><span @click='searchStu'><?php echo Yii::t('withdrawal','Search');?></span> </template>
                </el-input>
            </div>
            <el-select v-model="startYear" class='ml15' placeholder="<?php echo Yii::t('labels','Please Select School Year');?>"  size='small' @change='indexData()' v-if='searchType'>
                <el-option
                v-for="item in indexDataList.schoolYear"
                :key="item.yid"
                :label="item.title"
                :value="item.startyear">
                </el-option>
            </el-select>
        </div>
        <div class="overflow-y container-fluid scroll-box " :style="'height:'+(height-246)+'px'">
            <div class='row ' v-if='searchType'>
                <div class='col-md-7'>
                    <div class='myWait'  @mouseenter="showMyMouseEnter()" @mouseleave="showMyMouseLeave()">
                        <div class='font14 color3 pr24 myWaiting'><?php echo Yii::t('withdrawal','Pending Tasks');?></div>
                        <template v-if='indexDataList.myNode.length!=0'> 
                            <template v-if='indexDataList.myNode.length>3'>
                                <div class='waitNum' v-for='(list,key,index) in indexDataList.myNode.slice(0, 3)' :class='fromType=="my" && filterTask==list.task &&filterNode==list.node?"waitNumBorder":""'  @click='initData("my",list.node,list.task)'>
                                    <div class='font20 color3'>{{list.count}}</div>
                                    <div class='font12 color6' >{{list.title}}</div>
                                </div>
                                <el-popover
                                    placement="right-start"
                                    width="250"
                                    trigger="hover"
                                    v-if='indexDataList.myNode.length>3'
                                    >
                                    <div>
                                        <el-dropdown-item v-for='(item,idx) in indexDataList.myNode.slice(3, indexDataList.myNode.length)' :key='idx' class='flex mydropdownHover' :class='fromType=="my" && filterTask==item.task &&filterNode==item.node?"waitNumBorder":""'  @click.native='initData("my",item.node,item.task)'>
                                            <span class='color6 font12 flex1'>{{item.title}}</span>  
                                            <span class='color3 font20 ml16'>{{item.count}}</span>
                                        </el-dropdown-item>
                                    </div>
                                    <div class='waitNum' slot="reference">
                                        <div class='font20 color3'>{{totalCountNode}}</div>
                                        <div class='font12 color6' ><?php echo Yii::t('referral','More');?></div>
                                    </div>
                                </el-popover>
                            </template>  
                            <template v-else>
                                <div class='waitNum' v-for='(list,key,index) in indexDataList.myNode' :class='fromType=="my" && filterTask==list.task &&filterNode==list.node?"waitNumBorder":""' @click='initData("my",list.node,list.task)'>
                                    <div class='font20 color3'>{{list.count}}</div>
                                    <div class='font12 color6' >{{list.title}}</div>
                                </div>
                            </template>
                        </template>
                        <div v-else class='waitNum'>
                            <span class='font20 color3'>0</span>
                        </div>
                    </div>
                </div>
                <div class='col-md-5 text-right'>
                    <div class='inline_flex'>                    
                        <div  v-if='indexDataList.node.completed.indexOf("completed_confirm")!=-1' >
                            <div class='continue_confirm'>
                                <div class='font14 color3 fontBold myWaiting pr16'><?php echo Yii::t('withdrawal','Revoke Applications');?></div> 
                                <div class='confirmNum' @click='initData("list","completed_confirm","")' :class="filterNode=='completed_confirm'?'confirmNum1':''">
                                    <div class='font20 color3'>{{indexDataList.node.nodes['completed_confirm'].count}}</div>
                                    <div class='font12 color6' ><?php echo Yii::t('withdrawal','Pending');?></div>
                                </div>
                                <div class='continueNum ml8' @click='initData("list","completed_continue","")'  :class="filterNode=='completed_continue'?'continueNum1':''">
                                    <div class='font20 color3'>{{indexDataList.node.nodes['completed_continue'].count}}</div>
                                    <div class='font12 color6' ><?php echo Yii::t('withdrawal','Ended');?></div>
                                </div>
                            </div>
                        </div>
                        <div v-if='indexDataList.node.completed.indexOf("completed_quit")!=-1' class='withdrawNum ml16'
                        @click='initData("list","completed_quit","")' :class="filterNode=='completed_quit'?'withdrawNum1':''">
                            <div class='font14 color3 text-left'>
                                <div class='font14 color3 fontBold'>{{indexDataList.node.nodes['completed_quit'].title}}</div> 
                            </div>
                            <div class='font20 color3 ml16'>{{indexDataList.node.nodes['completed_quit'].count}}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class='mt24' v-if='searchType'>
                <el-steps  align-center>
                    <template  v-for='(list,index) in indexDataList.node.sortList' >
                        <el-step class='stepHover'  v-if='!indexDataList.node.nodes[list].sub' :key='index'  @click.native='initData("list",list,"")' :class='fromType=="list" &&filterNode==list?"stepActive":""'>
                            <template slot="title">
                                <span class='color3 font14 stepTitle'> {{indexDataList.node.nodes[list].title}}</span>
                            </template>
                            <template slot="description">
                                <span class='color3 font20 stepNum'>{{indexDataList.node.nodes[list].count}}</span> 
                            </template>
                        </el-step>
                        <el-step class='stepHover' :key='index'  v-if='indexDataList.node.nodes[list].sub'  @mouseenter.native="handleMouseEnter(index)" @mouseleave.native="handleMouseLeave(index)" :class='fromType=="list" && filterNode==list?"stepActive":""'>
                            <template slot="title">
                                <span class='color3 font14 stepTitle'> {{indexDataList.node.nodes[list].title}}</span>
                            </template>
                            <template slot="description">
                                <div v-if='indexDataList.node.nodes[list].sub.length>2' class='inline_flex align-items '>
                                    <span v-for='(item,idx) in indexDataList.node.nodes[list].sub.slice(0, 2)' class='flex align-items flex1 ellipsis filterTask ' :class="[fromType=='list' && filterTask==item.key?'selectSub':'',idx+1<2?'borderRight':'']"  @click.stop='filterItem(item,list)'>
                                        <span class='color6 font12 mr4 ellipsis stepNum'> {{item.title}} </span>
                                        <span class='color3 font20 stepNum '>{{item.count}} </span>
                                    </span>
                                    <span class='stepMore' >...</span>
                                </div>
                                <div v-else  class='flex align-items'>
                                    <span v-for='(item,idx) in indexDataList.node.nodes[list].sub' class='flex align-items flex1 ellipsis filterTask' :class="[fromType=='list' && filterTask==item.key?'selectSub':'',idx+1<indexDataList.node.nodes[list].sub.length?'borderRight':'']"  @click.stop='filterItem(item,list)'>
                                        <span class='color6 font12 mr4 ellipsis stepNum'>{{item.title}} </span>
                                        <span class='color3 font20 stepNum'>{{item.count}} </span>
                                    </span>
                                </div> 
                                <el-popover
                                    placement="bottom"
                                    width="265"
                                    trigger="hover"
                                    :ref="`popover-${index}`"
                                    v-model="tipVisibles[index]"
                                    >
                                    <div>
                                        <el-dropdown-item v-for='(item,idx) in indexDataList.node.nodes[list].sub' :key='idx' class='flex dropdownHover' :class='fromType=="list" && filterTask==item.key?"selectSub":""'  @click.native='filterItem(item,list)'>
                                            <span class='color6 font12 flex1'>{{item.title}}</span>  
                                            <span class='color3 font20 ml16'>{{item.count}}</span>
                                        </el-dropdown-item>
                                    </div>
                                    <div class='color3 font14 stepTitle' slot="reference"> </div>
                                </el-popover>
                            </template>
                        </el-step>
                    </template>
                </el-steps>
            </div>
            <div class='mt24 mb24' v-if='pageData.items'>
                <div class='flex align-items pb16 borderBto'  v-if='searchType'>
                    <div class='flex1 font16 color3'>
                        <span class='flex align-items'>
                            <template  v-if='filterTask!=""'>
                                <span class='fontBold' v-if='fromType=="list"'>{{showSub(indexDataList.node.nodes[filterNode].sub,'title')}}</span>
                                <span  class='fontBold' v-if='fromType=="my"'>{{showSub(indexDataList.myNode,'title')}}</span>
                            </template>
                            <template  v-else>
                                <span  class='fontBold' v-if='filterNode=="completed_confirm" || filterNode=="completed_continue"'><?php echo Yii::t('withdrawal','Revoke Applications');?> </span>
                                <span  class='fontBold' v-else>{{indexDataList.node.nodes[filterNode].title}} </span>
                            </template>
                            <span class="badge ml8" v-if='pageData.items'>{{pageData.items.length}}</span>
                        </span>
                    </div>
                    <!-- <button type="button" class="btn btn-primary ml15 btn-sm" @click='sendWechat()'>发送微信申请单</button> -->
                    <button type="button" class="btn btn-default ml15 btn-sm" @click='exportTab()'><?php echo Yii::t('withdrawal','Export File/Table');?></button>
                </div>
                <div class='flex align-items mt16'>
                    <div class='flex1'>
                        <span v-if='!searchType' class='font14 color3'>共 {{searchTableList.items.length}} 条搜索结果</span> 
                        <ul class="nav nav-pills navTab" v-else-if='indexDataList.node.completed.indexOf(filterNode)==-1'>
                            <li role="presentation" :class='showAll==1?"active":""'><a href="javascript:;"  @click='showAll=1,initData(fromType,filterNode,filterTask)'><?php echo Yii::t('withdrawal','All');?></a></li>
                            <li role="presentation" :class='showAll==0?"active":""'><a href="javascript:;" @click='showAll=0,initData(fromType,filterNode,filterTask)'><?php echo Yii::t('leave','Pending');?></a></li>
                        </ul>
                    </div>
                    <div  v-if='searchType'>
                        <button type="button" class="btn btn-primary ml15 btn-sm" @click='batchHandle' v-if='(fromType=="my" || myNodeList.indexOf(filterTask)!=-1) && showAll==0 && batchList.indexOf(filterTask)!=-1'>批量处理</button>
                    </div>
                </div>
                <el-table
                    class='mt24'
                    id='exportTable'
                    v-if='pageData.items'
                    :data="pageData.items"
                    :header-cell-style="{background:'#fafafa',color:'#333'}"
                    v-loading="tableLoading"
                    @selection-change="handleSelectionChange"
                    style="width: 100%">
                    <el-table-column
                        v-if='searchType && (fromType=="my" || myNodeList.indexOf(filterTask)!=-1) && showAll==0 && batchList.indexOf(filterTask)!=-1'
                        type="selection"
                        key='1'
                        sortable
                        min-width="20">
                        </el-table-column>
                    <el-table-column
                        prop="unique_id"
                        label="<?php echo Yii::t('withdrawal','Serial Number');?>"
                        key='2'
                        sortable
                        min-width="140">
                    </el-table-column>
                    <el-table-column
                        prop="child_id"
                        key='3'
                        sortable
                        label="<?php echo Yii::t('child','Name');?>"
                        min-width="180">
                        <template slot-scope="scope">
                            <div class='flex align-items'>
                                <img :src="pageData.childInfo[scope.row.child_id].avatar" alt="" class='avatar38'>
                                <span class='ml10 flex1'>
                                    <a  target="_blank" :href="'<?php echo $this->createUrl('//child/index/index');?>&childid='+scope.row.child_id"  class='bluebg'>{{pageData.childInfo[scope.row.child_id].name}}</a>
                                    <div class='font12 color6' style='line-height:1'>ID：{{scope.row.child_id}}</div>
                                </span>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="class_id"
                        label="<?php echo Yii::t('child','Class');?>"
                        key='10'
                        sortable
                        min-width="160">
                        <template slot-scope="scope">
                        {{pageData.classInfo[scope.row.class_id]}}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="type"
                        key='4'
                        min-width="100"
                        sortable
                        label="<?php echo Yii::t('newDS','Type');?>">
                        <template slot-scope="scope">
                            {{indexDataList.typeList[scope.row.type]}}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="withdrawal_date"
                        min-width="160"
                        key='5'
                        sortable
                        label="<?php echo Yii::t('withdrawal','Withdrawal Date');?>">
                    </el-table-column>
                    <el-table-column
                        prop="status"
                        min-width="220"
                        key='6'
                        sortable
                        label="<?php echo Yii::t('user','Status');?>">
                        <template slot-scope="scope">
                        {{indexDataList.node.nodes[scope.row.status].title}}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="status"
                        key='7'
                        sortable
                        min-width="220"
                        v-if='searchType'
                        label="<?php echo Yii::t('withdrawal','Processing Status');?>">
                        <template slot-scope="scope">
                            <div v-if='scope.row.task_status==0'><span class='redBg redColor font12 tagStatus'><?php echo Yii::t('leave','Pending');?></span></div>
                            <div v-if='scope.row.task_status==1'>
                                <div ><span class='greenBg greenColor font12 tagStatus'> <?php echo Yii::t('withdrawal','Completed');?></span> </div>
                                <div class='color6 font12 mt4' v-if='scope.row.implementer!=""'>
                                    <span class='el-icon-user font14'></span>  
                                    <span>{{pageData.staffInfo[scope.row.implementer].name}} |</span>
                                    <span>{{scope.row.implement_at_format}}</span>
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="detail.total_amount"
                        v-if='searchType && showAll==1 && filterNode=="finance_confirm"'
                        min-width="110"
                        sortable
                        label="<?php echo Yii::t('withdrawal','Amount');?>" >
                        <template slot-scope="scope">
                            <div class='font14'>
                                <div class='redColor'  v-if='scope.row.detail.total_amount<0'>
                                    <div>{{formatAmount(scope.row.detail.total_amount)}}</div>  
                                </div>
                                <div :class='scope.row.detail.total_amount>0?"greenColor":""' v-else>   
                                    <div>{{formatAmount(scope.row.detail.total_amount)}}</div>  
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="status"
                        min-width="120"
                        v-if='searchType && showAll==1 && filterNode=="finance_confirm"'
                        :filters="filterTable"
                        :filter-method="filterHandler"
                        :filter-multiple='false'	
                        label="<?php echo Yii::t('withdrawal','withdrawal refund status');?>" >
                        <template slot-scope="scope">
                            <div  class=' font14'>
                                <span>{{getStatusLabel(indexDataList.financeStateList, scope.row.finance_state)}}</span>  
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="status"
                        min-width="220"
                        key='8'
                        sortable
                        v-if='searchType && ( (filterNode=="return") || filterNode=="completed_quit" || filterNode=="accounts")'
                        label="处理情况">
                        <template slot-scope="scope">
                            <div  >
                                <div v-if='scope.row.detail.type=="it"'>
                                    <span v-if='scope.row.detail.closed==1' class='color3 font14'>已关闭</span>
                                    <span v-if='scope.row.detail.closed==0' class='color3 font14'>未关闭</span>
                                </div>
                                <div v-if='scope.row.detail.type=="academic"'>
                                    <span v-if='scope.row.detail.closed==1' class='color3 font14'>已关闭</span>
                                    <span v-if='scope.row.detail.closed==0' class='color3 font14'>未关闭</span>
                                </div>
                                <div v-if='scope.row.detail.type=="lib"'>
                                    <span v-if='scope.row.detail.balance==1' class='color3 font14'><?php echo Yii::t('withdrawal','Fully Reimbursed');?></span>
                                    <div v-if='scope.row.detail.balance==0' class='font14'>
                                        <div>
                                            <span class='color3'><?php echo Yii::t('withdrawal','Compensation Required');?>：</span> 
                                            <span class='redColor'>欠费 {{ formatAmount(scope.row.detail.totalAmount) }}</span> 
                                        </div>
                                        <div class='flex items-center' v-for='(list,index) in scope.row.detail.goods'>
                                            <el-tooltip class="item" effect="dark" :content="list.name" placement="top">
                                                <span class='color3 ' style='overflow: hidden;text-overflow: ellipsis;white-space: nowrap;'>{{list.name}}：</span> 
                                            </el-tooltip>
                                            <span class='redColor' style='white-space: nowrap;'>-{{ formatAmount(list.amount) }}</span> 
                                        </div>
                                    </div>
                                </div>
                                <div v-if='scope.row.detail.type=="lib_class"'>
                                    <span v-if='scope.row.detail.balance==1' class='color3 font14'><?php echo Yii::t('withdrawal','Fully Reimbursed');?></span>
                                    <div v-if='scope.row.detail.balance==0' class='font14'>
                                        <div>
                                            <span class='color3'><?php echo Yii::t('withdrawal','Compensation Required');?>：</span> 
                                            <span class='redColor'>欠费 {{ formatAmount(scope.row.detail.totalAmount)}}</span>
                                        </div> 
                                        <div class='flex items-center'  v-for='(list,index) in scope.row.detail.goods'>
                                            <el-tooltip class="item" effect="dark" :content="list.name" placement="top">
                                                <span class='color3' style='overflow: hidden;text-overflow: ellipsis;white-space: nowrap;'>{{list.name}}：</span>
                                            </el-tooltip>
                                            <span class='redColor' style='white-space: nowrap;'>-{{ formatAmount(list.amount) }}</span> 
                                        </div>
                                    </div>
                                </div>
                                
                                <div v-if='scope.row.detail.type=="it_equ"'>
                                    <div v-if='scope.row.detail.balance==1' class='color3 font14'>已归还—状态完好</div>
                                    <div v-if='scope.row.detail.balance==2' class='color3 font14'>已归还—存在设备损坏</div>
                                    <div v-if='scope.row.detail.balance==0' class='font14 color3'>未归还</div>
                                    <div v-if='scope.row.detail.balance==0 || scope.row.detail.balance==2' class='font14'><span class='color3'><?php echo Yii::t('withdrawal','Compensation Required');?>：</span> <span class='redColor'>欠费 {{formatAmount(scope.row.detail.totalAmount)}}</span> </div>
                                </div>
                                <div v-if='scope.row.detail.type=="insurance"'>
                                    <span v-if='scope.row.detail.balance==1' class='color3 font14'>有社保</span>
                                    <span v-if='scope.row.detail.balance==0' class='font14 color3'>无社保</span>
                                </div>
                                <div v-if='scope.row.detail.type=="enrollment"'>
                                    <span v-if='scope.row.detail.balance==1' class='color3 font14'>有学籍</span>
                                    <span v-if='scope.row.detail.balance==0' class='font14 color3'>无学籍</span>
                                </div>
                                <div v-if='scope.row.detail.type=="tuition"'>
                                    <div class=' font14'>
                                        <span class='color3'><?php echo Yii::t('withdrawal', 'School Fees'); ?>：</span>     
                                        <span v-if='scope.row.detail.goods[0].balance==1' class='redColor'> 欠费 {{formatAmount(scope.row.detail.goods[0].amount)}}</span>  
                                        <span v-if='scope.row.detail.goods[0].balance==0' class='greenColor'> <?php echo Yii::t('withdrawal','Refund');?> {{formatAmount(scope.row.detail.goods[0].amount)}}</span>  
                                    </div>
                                    <div class='color3 font14'>
                                        <span class='color3'><?php echo Yii::t('withdrawal','Account balance');?>：</span>     
                                        <span v-if='scope.row.detail.goods[1].balance==0' class='greenColor'> <?php echo Yii::t('withdrawal','Refund');?> {{formatAmount(scope.row.detail.goods[1].amount)}}</span>  
                                    </div>
                                </div>
                                <div v-if='scope.row.detail.type=="lunch"'>
                                    <div class=' font14'>
                                        <span class='color3'><?php echo Yii::t('withdrawal','School Lunch Fee');?>：</span>     
                                        <span v-if='scope.row.detail.goods[0].balance==1' class='redColor'> 欠费 {{formatAmount(scope.row.detail.goods[0].amount)}}</span>  
                                        <span v-if='scope.row.detail.goods[0].balance==0' class='greenColor'> <?php echo Yii::t('withdrawal','Refund');?> {{formatAmount(scope.row.detail.goods[0].amount)}}</span>  
                                    </div>
                                </div>
                                <div v-if='scope.row.detail.type=="bus"'>
                                    <div class=' font14'>
                                        <span class='color3'><?php echo Yii::t('withdrawal', 'School Bus Fee'); ?>：</span>     
                                        <span v-if='scope.row.detail.goods[0].balance==1' class='redColor'> 欠费 {{formatAmount(scope.row.detail.goods[0].amount)}}</span>  
                                        <span v-if='scope.row.detail.goods[0].balance==0' class='greenColor'> <?php echo Yii::t('withdrawal','Refund');?> {{formatAmount(scope.row.detail.goods[0].amount)}}</span>  
                                    </div>
                                </div>
                                <div v-if='filterNode=="completed_quit"'>
                                    <div class=' font14'>
                                        <span>{{getStatusLabel(indexDataList.financeStateList, scope.row.finance_state)}}</span>  
                                    </div>
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="address"
                        width="130"
                        key='9'
                        label="<?php echo Yii::t('withdrawal','Action');?>">
                        <template slot-scope="scope">
                            <span class='font14 bluebg cur-p'  @click='child_id=scope.row.child_id;openModal(scope.row.id,filterNode,scope.row.child_id)'><?php echo Yii::t('withdrawal','Details');?></span>
                            <el-dropdown v-if='showAll==1 && filterNode=="finance_confirm" &&  scope.row.task_status==1'>
                                <span class="el-dropdown-link font14 bluebg ml20 cur-p">
                                    更多<i class="el-icon-arrow-down el-icon--right"></i>
                                </span>
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item @click.native='continueRefund(scope.row.id)'>追加退费</el-dropdown-item>
                                    <el-dropdown-item @click.native='financeState(scope.row.id, scope.row.finance_state)'>财务状态</el-dropdown-item>
                                </el-dropdown-menu>
                            </el-dropdown>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
    </div>
     <!-- 发送微信申请单 -->
     <div class="modal fade" id="sendWechatModal" tabindex="-1" role="dialog" data-backdrop="static" data-keyboard="false" >
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t("withdrawal", "Send WeChat request form"); ?></h4>
                </div>
                <div class="modal-body overflow-y  scroll-box p24" :style="'max-height:'+(height-180)+'px'">
                    <div class='mb24'>
                        <!-- <el-checkbox v-model="checked">全选</el-checkbox> -->
                    </div>
                    <div class='mt24 pt24 borderTop' v-for='index in 4'>
                        <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange" class='flex align-items'>
                            <div class='flex1 flex align-items'>
                                <img src="https://oa.daystar.ivyonline.cn/uploads/childmgt/avatar/202312/6718889df06ab04fc669b3fec4dfb17d!w200" alt="" class='avatar42'>
                                <div class='flex1 ml8'>
                                    <div class='color3 font14 mt5'>Barbara Pittman</div>
                                    <div class='color6 font12'>2023-2024 Grade 4 A</div>
                                </div>
                            </div>
                        </el-checkbox>
                        <el-checkbox-group v-model="checkedCities" @change="handleCheckedCitiesChange" class='mt24 ml24'>
                            <el-checkbox v-for="city in cities" :label="city" :key="city"  class='wechatCheck flex '> 
                                <div>
                                    <div class='flex align-items'>
                                        <img src="https://oa.daystar.ivyonline.cn/uploads/childmgt/avatar/202312/cce2323bc3d760f8f56caf4805e5a511!w200" alt="" class='img28'>
                                        <div class='font14 color3 ml8'>Justin Carroll </div>
                                        <div class='font14 color6'>｜<?php echo Yii::t('global','Father');?></div>
                                    </div>
                                    <div class='font12 color6 mt10'>最近一次发送：2024.04.28 12:00 <span class='el-icon-arrow-right'></span></div>
                                    <div class='mt5 yellow'>3小时后可再次发送</div>
                                </div>
                            </el-checkbox>
                        </el-checkbox-group>
                    </div>
                </div> 
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" ><?php echo Yii::t("global", "发送"); ?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 选择学生 -->
    <div class="modal fade" id="addClassModal" tabindex="-1" role="dialog" data-backdrop="static" data-keyboard="false" >
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t("referral", "Student"); ?></h4>
                </div>
                <div class="modal-body p24 relative">
                    <span class='borderLeftpos'></span>
                    <div style='max-height:600px;' class='row'>
                        <div class='col-md-6 col-sm-6'>
                            <div>
                                <el-input
                                placeholder="<?php echo Yii::t("global", "Search"); ?>"
                                v-model='searchText' 
                                size='small'
                                clearable>
                                </el-input>
                            </div>
                            <div  class="tab-pane active mt15 scroll-box" id="class" v-if='searchText==""'  style='max-height:500px;overflow-y:auto'>
                                <div v-for='(list,index) in classList' class='relative mb16'>
                                    <p  @click='getChild(list)'>
                                        <span  class='font14 color606 cur-p'>{{list.title}} </span>
                                        <span class='el-icon-arrow-down ml5' v-if='classId!=list.classid'></span>
                                        <span class='el-icon-arrow-up ml5' v-else></span>
                                    </p>
                                    <p  class='allCheck' v-if='classId==list.classid'><button class="btn btn-default pull-right btn-xs" type="button" @click='selectAll(list,index)'  v-if='list.childData && list.childData.length!=0'><?php echo Yii::t("global", "Select All");?></button></p>
                                    <div  class='border scroll-box mr10 childList' v-if='classId==list.classid'>
                                        <div v-if='!childLoading'>
                                            <div class='' v-if='list.childData && list.childData.length!=0'>
                                                <div class="media mb10 listMedia" v-for='(item,idx) in list.childData'>
                                                    <div class="media-left pull-left media-middle">
                                                        <a href="javascript:void(0)">
                                                            <img :src="item.avatar" data-holder-rendered="true" class="media-object img-circle avatar42">
                                                        </a>
                                                    </div>
                                                    <div v-if='item.stuLoading'>
                                                        <div v-if='item.disabled' class="media-right pull-right text-muted lineHeight">
                                                            <span class='cur-p mt10 color9'><?php echo Yii::t("newDS", "Subscribed");?></span>
                                                        </div>
                                                        <div v-else class="media-right pull-right text-muted" @click='assignChildren(item,index,idx)'>
                                                            <span class='cur-p font16 bluebg mt15 el-icon-circle-plus-outline'></span>
                                                        </div>
                                                    </div>
                                                    <div class='childLoading' v-else>
                                                        <span></span>
                                                    </div>
                                                    <div class="media-body media-middle">
                                                        <h4 class="media-heading font14 lineHeight">{{item.name}}</h4>
                                                    </div>
                                                </div>
                                            </div>
                                            <div v-else>
                                                <div class='font12 text-muted text-center'><?php echo Yii::t("newDS", "no student in this class");?></div>
                                            </div>
                                        </div>
                                        <div class='loading' v-else>
                                            <span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else>
                                <div v-if='searchChildList.length!=0'  class='scroll-box'   style='max-height:500px;overflow-y:auto'>                               
                                    <div class="media mt10 listMedia" v-for='(item,idx) in searchChildList'>
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                                <img :src="item.avatar" data-holder-rendered="true" class="media-object img-circle avatar42">
                                            </a>
                                        </div>
                                        <div v-if='item.disabled' class="media-right pull-right text-muted lineHeight">
                                            <span class='cur-p mt10'><?php echo Yii::t("newDS", "Subscribed");?></span>
                                        </div>
                                        <div v-else class="media-right pull-right text-muted" @click='assignChildren(item,idx,"search")'>
                                            <span class='cur-p font16 bluebg mt15 el-icon-circle-plus-outline'></span>
                                        </div>
                                        <div class="media-body media-middle">
                                            <h4 class="media-heading font14 color3 mt5">{{item.name}}</h4>
                                            <div class="text-muted color6">{{item.className}}</div>
                                        </div>
                                    </div>
                                </div>
                                <div v-else-if='searchText!=""'>
                                        <div class='font14 color6 text-center mt20'><?php echo Yii::t("ptc", "No Data"); ?></div>    
                                </div>
                            </div>
                        </div>
                        <div class='col-md-6 col-sm-6 borderLeft'>
                            <p class='mt10 font14 color606'>
                            <?php echo Yii::t("newDS", " ");?>{{childSelected.length}}<?php echo Yii::t("newDS", " student(s) selected");?>
                                <button class="btn btn-link pull-right btn-xs font14" v-if='childSelected.length!=0' type="button" @click='batchDel("modal")'><?php echo Yii::t("directMessage", "Clear All");?></button>
                            </p>
                            <div class='scroll-box p10 overflow-y' style='height:500px'>
                                <div class="media m0 listMedia" v-for='(list,index) in childSelected'>
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)">
                                            <img :src="list.avatar" data-holder-rendered="true" class="media-object img-circle avatar42">
                                        </a>
                                    </div>
                                    <div class="media-right pull-right text-muted" @click='Unassign(list,index)'>
                                        <span class='closeChild cur-p mt15 font16 el-icon-circle-close'></span>
                                    </div>
                                    <div class="media-body media-middle">
                                        <h4 class="media-heading font14 color3 mt5">{{list.name}} 
                                           
                                        </h4>
                                        <div class="text-muted color6">{{list.className}}</div>
                                        <div  v-if='list.checkedSkip=="1"' class='childSkip'>
                                            <label class="checkbox">
                                                <input type="checkbox"  v-model='list.checked'> <?php echo Yii::t('withdrawal','Skip interview process');?>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class='clearfix'></div>
                    </div>
                </div> 
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" @click='saveChild()' :disabled='btnDisanled'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 详情 -->
    <div class="modal fade" tabindex="-1" role="dialog" id="myModal" data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title"><?php echo Yii::t('withdrawal','Details');?></h4>
                    </div>
                    <div class="modal-body overflow-y p24 scroll-box" :style="'max-height:'+(height-120)+'px'">
                        <div class='pb8'>
                            <div class='flex align-items relative'>
                                <div class='flex1 flex align-items'>
                                    <img :src="childDetailsInfo.childPhoto" alt="" class='avatar42'>
                                    <div class='flex1 ml8'>
                                        <div class='color3 font14 mt5'>{{childDetailsInfo.childName}}</div>
                                        <div class='color6 font12'>{{childDetailsInfo.className}}</div>
                                    </div>
                                </div>
                                <div class='' v-if='indexDataList.node && indexDataList.node.completed.indexOf(childDetailsInfo.status)==-1 && indexDataList.cancelUser'>
                                    <span class='redColor  cur-p  flex align-items' @click='stopDropout("stop")'><span class='el-icon-switch-button font16'></span> <span class='font14 ml5'><?php echo Yii::t('withdrawal','Revoke Applications');?></span></span> 
                                </div>
                                <div v-if='childDetailsInfo.status=="completed_continue"' >
                                    <div class='dropout_confirm'>
                                        <div>
                                            <div>已终止退学</div>
                                            <div v-if='implementer'>{{implementer.name}}｜{{implementerDate}}</div>
                                        </div>
                                    </div>
                                </div>
                                <div v-if='childDetailsInfo.status=="completed_confirm"' >
                                    <div class='dropout_confirm'>
                                        <div class='flex1 pr16' style='border-right: 1px solid rgba(217, 83, 79, 0.12);'>
                                            <div>{{implementer.name}} 申请终止退学</div>
                                            <div>{{implementerDate}}</div>
                                        </div>
                                        <span class='bluebg ml16 cur-p' v-if='indexDataList.superuser || childDetails.forms[0].created_by==childDetails.info.appInfo.currentStaff' @click='cancelWithdraw("cancelWithdraw")'>取消申请</span>
                                        <button type="button" v-if='indexDataList.superuser' class="btn btn-danger btn-sm ml16" @click='stopDropout("confirm")'>同意</button>
                                        <span class='color9 ml16' v-else>审批中</span>
                                    </div>
                                </div>
                                <div v-if='childDetailsInfo.status=="completed_quit"' class='completedQuit'>
                                    <div class='completedText'><?php echo Yii::t('withdrawal','Withdrawal Complete');?></div>
                                </div>
                            </div>
                            <div>
                                <div class='mt24 font14'>
                                    <span class='color3  fontBold mt24 mr10'><?php echo Yii::t('withdrawal','Basic Information');?></span>     
                                    <span class='bluebg cur-p' v-if='isExpand' @click='isExpand=false'><?php echo Yii::t('newDS','Collapse');?> <span class='el-icon-arrow-up'></span></span>
                                    <span class='bluebg cur-p' v-else @click='isExpand=true'><?php echo Yii::t('newDS','Expand');?> <span class='el-icon-arrow-down'></span></span>
                                </div>
                                <div class='row' id='childInfo' v-if='isExpand'>
                                    <div class='col-md-6'>
                                        <div class='font14 mt16 flex align-items'>
                                            <span class='color6 infoWidth'><?php echo Yii::t('user','Name');?></span>
                                            <span class='color3 flex1'>{{childDetailsInfo.childName}}</span>
                                        </div>
                                    </div>
                                    <div class='col-md-6'>
                                        <div class='font14 mt16 flex align-items'>
                                            <span class='color6 infoWidth'><?php echo Yii::t('user','Gender');?></span>
                                            <span class='color3 flex1'>{{childDetailsInfo.gender==1?"M":"F"}}</span>
                                        </div>
                                    </div>
                                    <div class='col-md-6'>
                                        <div class='font14 mt16 flex align-items'>
                                            <span class='color6 infoWidth'><?php echo Yii::t('labels','Class');?></span>
                                            <span class='color3 flex1'>{{childDetailsInfo.className}}</span>
                                        </div>
                                    </div>
                                    <div class='col-md-6'>
                                        <div class='font14 mt16 flex align-items'>
                                            <span class='color6 infoWidth'>学籍ID</span>
                                            <span class='color3 flex1'>{{childDetailsInfo.educationalId}}</span>
                                        </div>
                                    </div>
                                    <div class='col-md-6'>
                                        <div class='font14 mt16 flex align-items'>
                                            <span class='color6 infoWidth'><?php echo Yii::t('global','Father');?></span>
                                            <span class='color3 flex1'>{{childDetailsInfo.fName}}</span>
                                        </div>
                                    </div>
                                    <div class='col-md-6'>
                                        <div class='font14 mt16 flex align-items'>
                                            <span class='color6 infoWidth'><?php echo Yii::t('global','Mother');?></span>
                                            <span class='color3 flex1'>{{childDetailsInfo.mName}}</span>
                                        </div>
                                    </div>
                                    <div class='col-md-6'>
                                        <div class='font14 mt16 flex align-items'>
                                            <span class='color6 infoWidth'><?php echo Yii::t('labels','Tel');?></span>
                                            <span class='color3 flex1'>{{childDetailsInfo.fPhone}}</span>
                                        </div>
                                    </div>
                                    <div class='col-md-6'>
                                        <div class='font14 mt16 flex align-items'>
                                            <span class='color6 infoWidth'><?php echo Yii::t('labels','Tel');?></span>
                                            <span class='color3 flex1'>{{childDetailsInfo.mPhone}}</span>
                                        </div>
                                    </div>
                                    <div class='col-md-6'>
                                        <div class='font14 mt16 flex align-items'>
                                            <span class='color6 infoWidth'><?php echo Yii::t('child','Email');?></span>
                                            <span class='color3 flex1'>{{childDetailsInfo.fEmail}}</span>
                                        </div>
                                    </div>
                                    <div class='col-md-6'>
                                        <div class='font14 mt16 flex align-items'>
                                            <span class='color6 infoWidth'><?php echo Yii::t('child','Email');?></span>
                                            <span class='color3 flex1'>{{childDetailsInfo.mEmail}}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-if='indexDataList.node'>
                            <div class='mb24 mt24'>
                                <ul class="nav nav-wizard navSteps mt10" >
                                    <li v-for='(list,index) in editSortNav' class='mb10' :class='stepName==list?"active":successStep.indexOf(list)!=-1?"successactive":""'>
                                        <el-tooltip class="item" effect="dark" :content="indexDataList.node.nodes[list].desc" placement="top">
                                            <a href="javascript:;" @click="stepInfo(list)">
                                                <span class='el-icon-circle-check' v-if='successStep.indexOf(list)!=-1'></span>
                                                <span :class='childDetails.info.baseInfo.status==list?"colorStep":""' v-if='childDetails.info.baseInfo.status==list'> {{ indexDataList.node.nodes[list].title}}</span>
                                                <span :class='stepinfoList.indexOf(list)==-1?"color9":""' v-else> {{ indexDataList.node.nodes[list].title}}</span>
                                            </a>
                                        </el-tooltip>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div v-if='stepName=="interview01" || stepName=="interview02" || stepName=="admission_confirm" || stepName=="completed_quit"'>
                            <div class='ml8 pb24 flex1' v-if='applyUserInfo.applyUser && applyUserInfo.applyUser.name'>
                                <div class='font14 color3 fontBold'>申请信息</div>
                                <div class='mt16 form-horizontal font14'>
                                    <div class='flex flexStart mb20'>
                                        <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Applicant');?></div>
                                        <div class='flex1 color3'>
                                            <div v-if='applyUserInfo.applyUser.is_staff==0' class='flex align-items'>
                                                <img :src="applyUserInfo.applyUser.avatar" class='img28' alt="">
                                                <span class='ml5'>{{applyUserInfo.applyUser.name}}</span>
                                                <span class='tagList ml10'>
                                                    {{applyUserInfo.applyUser.pid==childDetails.info.baseInfo.mid?'<?php echo Yii::t('global','Mother');?>':applyUserInfo.applyUser.pid==childDetails.info.baseInfo.fid?'<?php echo Yii::t('global','Father');?>':"<?php echo Yii::t('newDS','Other');?>"}}</span>
                                            </div>
                                            <div v-if='applyUserInfo.applyUser.is_staff==1'>
                                                {{applyUserInfo.applyUser.name}} <span class='tagList ml10' ><?php echo Yii::t('withdrawal','Submission on behalf of parent');?></span> 
                                            </div>
                                        </div>
                                    </div>
                                    <div class='flex mb20 flexStart'>
                                        <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Withdrawal Date');?></div>
                                        <div class='flex1 color3'>
                                            <span>{{applyUserInfo.applyUser.withdrawal_date}}</span> 
                                            <span v-for='(list,index) in applyUserInfo.applyFormInfo.items' class='labelDate'>{{list.withdrawal_date}} — {{applyUserInfo.applyFormInfo.staffInfo[list.talk_user].name}}</span>
                                        </div>
                                    </div>
                                    <div class='flex mb20 flexStart'>
                                        <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Where are you transferring to?');?></div>
                                        <div class='flex1'>
                                            <div class='color3'>{{indexDataList.whereList[applyUserInfo.applyUser.withdrawal_where]}}</div>
                                            <div class='color6 mt4 font12' v-if='applyUserInfo.applyUser.withdrawal_where_memo'><?php echo Yii::t('withdrawal','Comment');?>：{{applyUserInfo.applyUser.withdrawal_where_memo}}</div>
                                            <div class='moreText' v-if='applyUserInfo.applyFormInfo.items.length!=0'>
                                                <div class=' ' :class='index+1<applyUserInfo.applyFormInfo.items.length?"borderBto pb10 mb10":""' v-for='(list,index) in applyUserInfo.applyFormInfo.items'>
                                                    <div>{{indexDataList.whereList[list.withdrawal_where]}}</div>
                                                    <div class='mt4 font12 flex' v-if='list.withdrawal_where_memo!=null '>
                                                        <span><?php echo Yii::t('withdrawal','Comment');?>：</span>
                                                        <span class='flex1 ml5' v-html='list.withdrawal_where_memo.replace(/\n/g, "<br>")'></span>
                                                    </div>
                                                    <div class='mt4'> — {{applyUserInfo.applyFormInfo.staffInfo[list.talk_user].name}}</div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                    </div>
                                    <div class='flex mb20 flexStart'>
                                        <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Reason for withdrawal');?></div>
                                        <div class='flex1'>
                                            <div class='color3'>{{indexDataList.reasonList[applyUserInfo.applyUser.withdrawal_reason]}}</div>
                                            <div class='color6 mt4 font12' v-if='applyUserInfo.applyUser.withdrawal_reason_memo'><?php echo Yii::t('withdrawal','Comment');?>：{{applyUserInfo.applyUser.withdrawal_reason_memo}}</div>
                                            <div class='moreText' v-if='applyUserInfo.applyFormInfo.items.length!=0'>
                                                <div class=' ' :class='index+1<applyUserInfo.applyFormInfo.items.length?"borderBto pb10 mb10":""' v-for='(list,index) in applyUserInfo.applyFormInfo.items'>
                                                    <div>{{indexDataList.reasonList[list.withdrawal_reason]}}</div>
                                                    <div class='mt4 font12 flex' v-if='list.withdrawal_reason_memo!=null'>
                                                        <span><?php echo Yii::t('withdrawal','Comment');?>：</span>
                                                        <span class='flex1 ml5' v-html='list.withdrawal_reason_memo.replace(/\n/g, "<br>")'></span>
                                                    </div>
                                                    <div class='mt4'> — {{applyUserInfo.applyFormInfo.staffInfo[list.talk_user].name}}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class='flex mb20 flexStart'>
                                        <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Are there any aspects of school you are unsatisfied with?');?></div>
                                        <div class='flex1'>
                                            <div class='color3'>{{applyUserInfo.applyUser.withdrawal_unsatisfied}}</div>
                                            <div class='moreText' v-if='applyUserInfo.applyFormInfo.items.length!=0'>
                                                <div class=' ' :class='index+1<applyUserInfo.applyFormInfo.items.length?"borderBto pb10 mb10":""' v-for='(list,index) in applyUserInfo.applyFormInfo.items'>
                                                    <div v-html='list.withdrawal_unsatisfied.replace(/\n/g, "<br>")'></div>
                                                    <div class='mt4'> — {{applyUserInfo.applyFormInfo.staffInfo[list.talk_user].name}}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class='flex mb20 flexStart'>
                                        <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Do you have any advice for us?');?></div>
                                        <div class='flex1'>
                                            <div class='color3'>{{applyUserInfo.applyUser.withdrawal_recommendations}}</div>
                                            <div class='moreText'  v-if='applyUserInfo.applyFormInfo.items.length!=0'>
                                                <div class=' ' :class='index+1<applyUserInfo.applyFormInfo.items.length?"borderBto pb10 mb10":""' v-for='(list,index) in applyUserInfo.applyFormInfo.items'>
                                                    <div v-html='list.withdrawal_recommendations.replace(/\n/g, "<br>")'></div>
                                                    <div class='mt4'> — {{applyUserInfo.applyFormInfo.staffInfo[list.talk_user].name}}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class='flex mb20 flexStart' v-if='applyUserInfo.applyUser.withdrawal_contact_user!=null'>
                                        <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Who do you wish to contact about the withdrawal process?');?></div>
                                        <div class='flex1'>
                                            <span class="el-icon-user mr5"></span>
                                            {{applyUserInfo.applyUser.withdrawal_contact_name}}
                                            <span class="tagList ml8">{{applyUserInfo.applyUser.withdrawal_contact_user==childDetails.info.baseInfo.mid?'<?php echo Yii::t('global','Mother');?>':applyUserInfo.applyUser.withdrawal_contact_user==childDetails.info.baseInfo.fid?'<?php echo Yii::t('global','Father');?>':'<?php echo Yii::t('newDS','Other');?>'}}</span>
                                            <span class="glyphicon glyphicon-earphone ml16 mr8 color3"></span>
                                            {{applyUserInfo.applyUser.withdrawal_contact_phone}}
                                        </div>
                                    </div>
                                    <div class='flex mb20 flexStart'>
                                        <div class='color6 flexWidth'><?php echo Yii::t('withdrawal','Comment');?></div>
                                        <div class='flex1' v-if='applyUserInfo.applyFormInfo.items.length!=0'>
                                            <div v-for='(list,index) in applyUserInfo.applyFormInfo.items'>                                            
                                                <div class=' ' v-if='list.memo!=null' :class='index+1<applyUserInfo.applyFormInfo.items.length?"borderBto pb10 mb10":""' >  
                                                    <div v-html='list.memo.replace(/\n/g, "<br>")'></div>
                                                    <div class='mt4'> — {{applyUserInfo.applyFormInfo.staffInfo[list.talk_user].name}}</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div v-else>
                                            -
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- <div class='flex align-items mb20' v-if='applyUserInfo.settlement_date && (stepName=="finance_confirm" || (stepName=="return" && (activeName=="tuition" || activeName=="lunch" || activeName=="bus")))'>
                            <span class='redColor font14'>清算日期：{{applyUserInfo.settlement_date}}</span> 
                            <span class='color6 font12 ml15'><span class='el-icon-warning-outline mr5'></span>以此日期为在学截止日进行费用清算</span>
                        </div> -->
                        <div v-if='childDetails.forms && (childDetails.forms.length>1 || indexDataList.node.nodes[stepName].sub)'>
                            <el-tabs v-model="activeName" @tab-click="tabClick">
                                <el-tab-pane :label="list.title" :name="list.key+ ''"  v-for='(list,index) in childDetails.forms' :key='list.key'>
                                    <span slot="label" class='relative tabHover'>
                                        <span v-if='list.status==0' ><span class='el-icon-question question'></span><span class='ml4'>{{list.title}}</span></span>
                                        <span v-if='list.status==99' ><span class='el-icon-question question'></span><span class='ml4'>{{list.title}}</span></span>
                                        <span v-if='list.status==1' class='greenColor'><span class='el-icon-success '></span><span class='ml4'>{{list.title}}</span></span>
                                         <span v-if='list.redDot' class='redDot'></span>
                                    </span>
                                </el-tab-pane>
                            </el-tabs>
                        </div>
                        <div id='stepTemplate'></div>
                    </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
    <!-- 追加退费 -->
    <div class="modal fade" id='addRefundModal'  tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">
                        <span><?php echo Yii::t("global", "追加退费"); ?></span> 
                    </h4>
                </div>
                <div class="modal-body" >
                    <div  v-for='(list,index) in refundData'>
                        <div v-if='list.isEdit' class='flex align-items moreText mb12 ' style='padding:16px 24px'>                        
                            <div class='flex1'>
                                <div class='font14 color3'>{{formatAmount(list.amount)}}元</div>
                                <div class='font12 color6 mt5'>备注：{{list.memo}}</div>
                                <div class='font12 color6 mt5'><span>填写人：{{refundstaffInfo[list.updated_by].name}}</span><span class='ml24'>填写时间：{{list.updated_at}}</span></div>
                            </div>
                            <button type="button" class="btn btn-default" @click='list.isEdit=false'>编辑</button>
                        </div>
                        <div v-else class='editRefund mb12 mt8'>
                            <div class='flex mb16 align-items'>
                                <div class='font14 color6' style='width:90px'>退费金额/元</div>
                                <div class='flex1'><el-input v-model="list.amount" placeholder="请输入" type="number" min="0" step="0.01" size='small'></el-input></div>
                            </div>
                            <div class='flex mb16'>
                                <div class='font14 color6' style='width:90px'>备注</div>
                                <div class='flex1'>
                                    <el-input
                                    type="textarea"
                                    :rows="2"
                                    placeholder="请输入备注"
                                    v-model="list.memo">
                                    </el-input>
                                </div>
                            </div>
                            <div class='pull-right'>
                                <button type="button" class="btn btn-link" @click='delRefund(list,index)'>删除</button>
                                <button type="button" class="btn btn-default ml15" v-if='list.id!=""' @click='canRefund(list,index)'>取消</button>
                                <button type="button" class="btn btn-primary ml15" @click='saveRefund(list,index)'>保存</button>
                            </div>
                            <div class='clearfix'></div>
                        </div>
                    </div>
                    <div class='mt24 mb10'><span class='bluebg font14 cur-p' @click='addRefund()'><span class='el-icon-plus'></span>追加退费</span></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 设置财务状态 -->
    <div class="modal fade" id='financeStateModal'  tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" data-backdrop="static">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">
                        <span><?php echo Yii::t("global", "设置财务状态"); ?></span> 
                    </h4>
                </div>
                <div class="modal-body">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <label for="inputEmail3" class="col-sm-2 control-label">状态</label>
                            <div class="col-sm-10">
                                <label class="radio-inline mr24 ml0" v-for="item in indexDataList.financeStateList" :key="item.value">
                                    <input type="radio" v-model="financeStateValue" :value="item.value"> {{ item.label }}
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="inputPassword3" class="col-sm-2 control-label">附件</label>
                            <div class="col-sm-10">
                                <el-upload
                                    action="https://up-z1.qiniup.com"
                                    :show-file-list="false"
                                    :on-success="handleAvatarSuccess"
                                    :data="uploadToken"
                                    :before-upload="beforeAvatarUpload"
                                    class="upload-demo">
                                    <span class='bluebg mt8 inline-block'><span class='el-icon-plus'></span> {{loadingImg?'正在上传中':'<?php echo Yii::t("newDS", "Add"); ?>'}}</span>
                                </el-upload>
                                <div class='mt16'>
                                    <div class='flex flexList' v-for='(list,id) in file_list' >
                                        <span class='el-icon-paperclip mr8 font16' ></span>
                                        <a class='flex1' target='_blank' :href='list.file_key'>{{list.title}}</a>
                                        <span class='el-icon-delete ml10 cur-p' @click='delFile(list._id)'></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="inputPassword3" class="col-sm-2 control-label">备注</label>
                            <div class="col-sm-10">
                                <textarea class="form-control mt10" rows="3" v-model="financeTextarea" placeholder="请输入备注"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button" class="btn btn-primary ml15" @click='setFinanceState()'><?php echo Yii::t("global", "Save");?></button>
                </div>
            </div>
        </div>
    </div>
     <!-- 删除 -->
     <div class="modal fade" id='delModal'  tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel">
        <div class="modal-dialog" :class='delType=="stop"?"":"modal-sm"' role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">
                        <span v-if='delType=="del" || delType=="delRefund" || delType=="file"'><?php echo Yii::t("global", "Delete"); ?></span> 
                        <span v-if='delType=="stop"'><?php echo Yii::t("global", "终止本退学申请流程"); ?></span> 
                        <span v-if='delType=="cancel"'><?php echo Yii::t("global", "提示"); ?></span> 
                        <span v-if='delType=="confirm"'><?php echo Yii::t("global", "终止本退学申请流程"); ?></span> 
                        <span v-if='delType=="cancelWithdraw"'><?php echo Yii::t("global", "终止本退学申请流程"); ?></span> 
                    </h4>
                </div>
                <div class="modal-body" >
                    <div v-if='delType=="del" || delType=="delRefund" || delType=="file"'>
                        <?php echo Yii::t("directMessage", "Proceed to remove?");?> 
                    </div>
                    <div v-if='delType=="stop"'>
                        <div class='color3 font14 mb16'>该操作将提起申请终止本退学流程，申请将由 <span v-for='(list,key,index) in indexDataList.superuserInfo'>{{list.name}} <span v-if='index+1<Object.values(indexDataList.superuserInfo).length'>、</span></span>审核</div>
                        <label class="checkbox-inline">
                            <input type="checkbox"  v-model='confirm_continue'> 继续操作
                        </label>
                    </div>
                    <div v-if='delType=="cancel"'>
                        <?php echo Yii::t("directMessage", "确认撤销吗？");?> 
                    </div>
                    <div v-if='delType=="confirm"'>
                        <div class='color3 font14 mb16'>该操作将终止本退学流程。</div>
                        <label class="checkbox-inline">
                            <input type="checkbox" id="inlineCheckbox1" v-model='confirm'> 继续操作
                        </label>
                    </div>
                    <div v-if='delType=="cancelWithdraw"'>
                        <div class='color3 font14 mb16'>该操作将取消终止本退学流程的申请</div>
                        <label class="checkbox-inline">
                            <input type="checkbox" id="inlineCheckbox1" v-model='cancel_apply'> 继续操作
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button" class="btn btn-primary" v-if='delType=="del"' @click='delApplication()'><?php echo Yii::t("message", "OK");?></button>
                    <button type="button" class="btn btn-primary" v-if='delType=="delRefund"' @click='delRefund()'><?php echo Yii::t("message", "OK");?></button>
                    <button type="button" class="btn btn-primary" v-if='delType=="stop" || delType=="confirm"' @click='stopDropout()'><?php echo Yii::t("message", "OK");?></button>
                    <button type="button" class="btn btn-primary" v-if='delType=="cancelWithdraw"' @click='cancelWithdraw()'><?php echo Yii::t("message", "OK");?></button>
                    <button type="button" class="btn btn-primary" v-if='delType=="cancel"' @click='cancelSubmitData()'><?php echo Yii::t("message", "OK");?></button>
                    <button type="button" class="btn btn-primary" v-if='delType=="file"' @click='delFile()'><?php echo Yii::t("message", "OK");?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 撤销驳回 -->
    <div class="modal fade" id='cancelRejectModal'  tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">
                        <span><?php echo Yii::t("global", "撤销驳回"); ?></span> 
                    </h4>
                </div>
                <div class="modal-body" >
                    <div>
                        <?php echo Yii::t("directMessage", "确认撤销驳回吗？");?> 
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button" class="btn btn-primary" @click='confirmCancelReject()'><?php echo Yii::t("message", "OK");?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 驳回记录 -->
    <div class="modal fade" id='rejectedListModal'  tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">
                        <span><?php echo Yii::t("global", "驳回记录"); ?></span> 
                    </h4>
                </div>
                <div class="modal-body" >
                    <div class='flex relative' v-for='(list,index) in rejectedListData.log'>
                        <div class='dot'></div>
                        <span class='stepBorderLeft' v-if='rejectedListData.log.length>index+1'></span>
                        <div class='ml8 pb24 flex1 width90' >
                            <div>
                                <span class='font14 color3 fontBold'> {{list.date}} </span>
                                <span class='labelTag yellow yellowBg' v-if='list.status==1'>取消驳回</span>
                                <span class='labelTag redColor redBg' v-if='list.status==2'>驳回</span>
                                <span class='labelTag greenColor greenBg' v-if='list.status==3'>重新提交</span>
                            </div>
                            <div class='flex align-items mt12'>
                                <img :src="rejectedListData.staffList[list.uid].photoUrl" alt="" class='img28'>
                                <div class='font14 color3 ml8'>{{rejectedListData.staffList[list.uid].name}}</div>
                            </div>
                            <div class='color6 font14 mt8' v-if='list.status==2'>驳回理由：{{list.reject_memo}}</div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 驳回 -->
    <div class="modal fade" id='rejectModal'  tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">
                        <span><?php echo Yii::t("global", "驳回理由"); ?></span> 
                    </h4>
                </div>
                <div class="modal-body" >
                    <div>
                        <textarea class="form-control" rows="3" v-model='rejectMemo' placeholder="请输入驳回理由"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button" class="btn btn-primary" @click='confirmReject()' :disabled='rejectBtn'><?php echo Yii::t("message", "OK");?></button>
                </div>
                </div>
        </div>
    </div>
    <!-- 发送申请单记录 -->
    <div class="modal fade" id='dateListModal'  tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">
                        <span><?php echo Yii::t("global", "推送记录"); ?></span> 
                    </h4>
                </div>
                <div class="modal-body" >
                    <div id='dateList'></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 添加其他面谈人 -->
    <div class="modal fade" id='addOtherTalkModal'  tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">
                        <span><?php echo Yii::t("global", "添加其他面谈人"); ?></span> 
                    </h4>
                </div>
                <div class="modal-body" >
                    <div class='flex mb20'>
                        <el-select
                            v-model="staffCheck"
                            filterable
                            remote
                            clearable
                            size='small'
                            class='inline-input flex1 formControl'
                            reserve-keyword
                            placeholder="<?php echo Yii::t("directMessage", "搜索姓名"); ?>"
                            :remote-method="stuRemoteMethod"
                            prefix-icon="el-icon-search"
                            :loading="loading">
                            <el-option
                                v-for="item in otherStaff"
                                :key="item.uid"
                                :label="item.name"
                                class='mb8'
                                style='height:auto'
                                :value="item.uid">
                                <div class="media">
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)">
                                            <img :src="item.photoUrl" data-holder-rendered="true" class="media-object img-circle avatar42">
                                        </a>
                                    </div>
                                    <div class="media-body mt5 media-middle">
                                        <h4 class="media-heading font14 color3 text_overflow">{{item.name}}</h4>
                                        <div class="text-muted text_overflow font12" style='line-height:14px'>{{ item.hrPosition }}</div>
                                    </div>
                                </div>
                            </el-option>
                        </el-select>
                        <button type="button" class="btn btn-primary ml16" size='small' @click='addOtherTalk' :disabled='staffCheck==""?true:false'><?php echo Yii::t("directMessage", "Proceed to add"); ?></button>
                    </div>
                </div>
                <div class='clearfix'></div>
                <!-- <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button" class="btn btn-primary" @click='confirmReject()' :disabled='rejectBtn'><?php echo Yii::t("message", "OK");?></button>
                </div> -->
            </div>
        </div>
    </div>
    <!-- 批量处理 -->
    <div class="modal fade" id='batchModal'  tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" data-backdrop="static">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">
                        <span><?php echo Yii::t("global", "批量处理"); ?></span> 
                    </h4>
                </div>
                <div class="modal-body p24" >
                    <div v-if='filterTask=="lib" || filterTask=="lib_class"'>   
                        <label class="checkbox-inline">
                            <input type="checkbox" id="inlineCheckbox1" v-model='batchBalanceLib' > <?php echo Yii::t('withdrawal','Fully Reimbursed');?>
                        </label>
                    </div>
                    <div v-if='filterTask=="insurance"'>   
                        <label class="radio-inline font14 color6 lableHeight">
                            <input type="radio"   value="1" v-model='batchBalance'> 有社保
                        </label>
                        <label class="radio-inline font14 color6 lableHeight ml24">
                            <input type="radio"   value="0" v-model='batchBalance'> 无社保
                        </label>
                    </div>
                    <div v-if='filterTask=="enrollment"'>   
                        <label class="radio-inline font14 color6 lableHeight">
                            <input type="radio"   value="1" v-model='batchBalance'> 有学籍
                        </label>
                        <label class="radio-inline font14 color6 lableHeight ml20">
                            <input type="radio"   value="0" v-model='batchBalance'> 无学籍
                        </label>
                    </div>
                    <div v-if='filterTask=="tuition" || filterTask=="lunch" || filterTask=="bus"'>
                        <label class="checkbox-inline">
                            <input type="checkbox" id="inlineCheckbox1" v-model='batchBalanceLib' > 
                            <span  v-if='filterTask=="tuition"'>学费和账户余额无退费</span> 
                            <span  v-if='filterTask=="lunch"'><?php echo Yii::t('withdrawal', 'School Lunch Fee'); ?>无退费</span> 
                            <span  v-if='filterTask=="bus"'><?php echo Yii::t('withdrawal', 'School Bus Fee'); ?>无退费</span> 
                        </label>
                    </div>
                    <div v-if='filterTask=="it" || filterTask=="academic"'>
                        <label class="checkbox-inline">
                            <input type="checkbox" v-model='closed'> <?php echo Yii::t('global','Close');?>
                        </label>
                    </div>
                    <textarea  v-if='filterTask!="it" && filterTask!="academic"' class="form-control mt20" rows="2" name="memo" v-model='batchMemo' placeholder="请输入备注"></textarea>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button" class="btn btn-primary" @click='confirmBatch()' :disabled='rejectBtn'><?php echo Yii::t("message", "OK");?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 推送记录 -->
    <div class="modal fade" id='pushListModal'  tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">
                        <span><?php echo Yii::t("global", "推送记录"); ?></span> 
                    </h4>
                </div>
                <div class="modal-body" v-if='stepName=="finance_confirm" && childDetails.forms && childDetails.forms[0].data.send_log'>
                    <div class='flex relative' v-for='(list,index) in getReverseList(childDetails.forms[0].data.send_log)'>
                        <div class='dot'></div>
                        <span class='stepBorderLeft' v-if='childDetails.forms[0].data.send_log.length>index+1'></span>
                        <div class='ml8 pb24 flex1 width90' >
                            <div>
                                <span class='font14 color3 fontBold'>已推送 {{list.date}}</span>
                            </div>
                            <div v-for='(item,idx) in list.openid'>
                                <div class='flex align-items mt10'>
                                    <img :src="filterParent(item,'headimgurl')" alt="" class='img28'>
                                    <span class='color3 font14 ml8'>{{filterParent(item,'nickname')}}</span>
                                    <span class='color6 ml5 mr5'>|</span>
                                    <span class='color6 font14'>{{filterParent(item,'parentType')=='father'?'<?php echo Yii::t('global','Father');?>':'<?php echo Yii::t('global','Mother');?>'}}</span>
                                </div>
                            </div>
                            <div class='color6 font12 mt10 flex'>
                                <span>推送内容：</span>
                                <span class='flex1' v-html='list.memo.replace(/\n/g, "<br>")'></span>
                            </div>
                            <div class='color6 font12 mt10'>核算退款金额 {{formatAmount(list.amount)}} 元</div>
                            
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- 家长确认记录 -->
    <div class="modal fade" id='parentConfirmModal'  tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">
                        <span><?php echo Yii::t("global", "家长确认记录"); ?></span> 
                    </h4>
                </div>
                <div class="modal-body" v-if='stepName=="finance_confirm" &&  childDetails.forms && childDetails.forms[0].data.parent_feedback_log'>
                    <div class='flex relative' v-for='(list,index) in getReverseList(childDetails.forms[0].data.parent_feedback_log)'>
                        <div class='dot'></div>
                        <span class='stepBorderLeft' v-if='childDetails.forms[0].data.parent_feedback_log.length>index+1'></span>
                        <div class='ml8  flex1 width90' >
                            <div>
                                <span class='font14 color3 fontBold'>{{list.status==2?"有疑问":'已确认'}} {{list.date}}</span>
                            </div>
                            <div class='color9 font12 mt8'>核算退款金额 {{formatAmount(list.amount)}} 元</div>
                            <!-- <div class='flex align-items mt10'>
                                <img :src="filterParent(list.openid,'headimgurl')" alt="" class='img28'>
                                <span class='color3 font14 ml8'>{{filterParent(list.openid,'nickname')}}</span>
                                <span class='color6 ml5 mr5'>|</span>
                                <span class='color6 font14'>{{filterParent(list.openid,'parentType')=='father'?'父亲':'母亲'}}</span>
                            </div> -->
                            <div class='flex align-items mt10'>
                                <img src="https://thirdwx.qlogo.cn/mmopen/vi_32/EXt78tjhHxfFNkZ2BSfh0oP5VRE1dl7A1qiaxsXNtezicAuoibhqScn4G2zFgJ7loI8SMicoTyaY16dnej3njLknLTnyOGKh9ZwZOlToNmkuL98/132" alt="" class='img28'>
                                <span class='color3 font14 ml8'>名字</span>
                                <span class='color6 ml5 mr5'>|</span>
                                <span class='color6 font14'><?php echo Yii::t('global','Father');?></span>
                            </div>
                            <div class='reject mt10' v-if='list.status==2'>
                                <div class='redColor font12'><span class='el-icon-warning-outline mr8'></span>家长有反馈</div>
                                <div class='font12 color3 mt8 flex'>
                                   <span>留言：</span> 
                                   <span class='flex1 ml5' v-html='list.memo.replace(/\n/g, "<br>")'></span></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                </div>
            </div>
        </div>
    </div>
    
</div>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>

<script>
    $(document).ready(function () {
        // 通过该方法来为每次弹出的模态框设置最新的zIndex值，从而使最新的modal显示在最前面
        $(document).on('show.bs.modal', '.modal', function (event) {
            var zIndex = 1040 + (10 * $('.modal:visible').length);
            $(this).css('z-index', zIndex);
            setTimeout(function() {
                $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');
            }, 0);
        });
    });
    const uid = '<?php echo Yii::app()->user->id;?>';
    const startYear = '<?php echo $startYear;?>';
    var height=document.documentElement.clientHeight;
    var childDetails={}
    function cbSuccess(){
        container.indexData('callback')
        container.initData(container.fromType,container.filterNode,container.filterTask,'from')
        setTimeout(() => {
            $('#myModal').modal('hide')
        }, 1000);
    }
    function cbSuccess2(){
        container.openModal(container.applicationId,'')
        container.initData(container.fromType,container.filterNode,container.filterTask,'from')
    }
    function tooltip() {
        $('[data-toggle="tooltip"]').tooltip()
    }
    var container=new Vue({
        el: "#container",
        data: {
            height:height,
            uid:uid,
            startYear:startYear+'',
            pageData:{},
            skip_talk:{},
            filterNode:'',
            searchTable:'',
            isExpand:true,
            border:['rgba(240, 173, 78)','rgba(77,136,210)','rgba(92,184,92)'],
            checkAll: false,
            checkedCities: [],
            cities: [],
            isIndeterminate: true,
            classList:[],
            classId:'',
            childSelected:[],
            searchText:'',
            teacherUid:{},
            teacherSelected:[],
            childDetails:{},
            childDetailsInfo:{},
            stepName:'',
            activeName:'',
            applicationId:'',
            editSortNav:[],
            successStep:[],
            stepinfoList:[],
            rejectMemo:'',
            rejectType:'',
            cancelRejectType:'',
            rejectedListData:[],
            rejectBtn:false,
            filterTask:'',
            tableLoading:false,
            searchTableList:{},
            searchType:true,
            delType:'',
            tipVisibles:[],
            indexDataList:{},
            showAll:'0',
            fromType:'my',
            multipleSelection:[],
            batchList:['lib','lib_class','insurance','enrollment','tuition','lunch','bus','it','academic'],
            batchBalanceLib:false,
            closed:false,
            batchBalance:'',
            batchMemo:'',
            myListNode:false,
            cancelType:'',
            confirm:false,
            implementer:'',
            implementerDate:'',
            btnDisanled:false,
            myNodeList:[],
            otherStaff:[],
            staffCheck:'',
            loading:false,
            addOtherList:{},
            applyUserInfo:{},
            confirm_continue:false,
            cancel_apply:false,
            refundData:[],
            refundstaffInfo:{},
            refundDataCopy:[],
            editRefundId:'',
            delRefundList:{},
            delRefundIndex:'',
            financeStateValue:0,
            child_id:'',
            uploadToken:{},
            financeTextarea:'',
            file_list:[],
            delId:'',
            loadingImg:false,
            totalCountNode:null,
            filterTable:[]
        },
        created: function() {
           this.indexData()
        },
        watch:{
            searchText(old,newValue){
                if(old!=''){
                    this.searchChild()
                }
            },
        },
        methods: {
            searchStu(){
                let that=this
                if(this.searchTable==''){
                    this.initData(container.fromType,container.filterNode,container.filterTask)
                    return
                }
                this.tableLoading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("search") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        childName:this.searchTable,
                        startYear: this.startYear
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                          that.tableLoading=false
                          
                          if(data.data.items){
                              that.searchTableList=data.data
                              that.pageData.items=data.data.items
                              that.pageData.childInfo=data.data.childInfo
                              that.pageData.classInfo=data.data.classInfo
                          }else{
                            that.pageData.items=[]
                            that.searchTableList.items=[]
                          }
                          that.searchType=false
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.tableLoading=false
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.tableLoading=false
                    },
                })
            },
            filterItem(list,type){
                $('.dropdown-menu').addClass('d-none');
                this.initData("list",type,list.key)
            },
            filterParent(list,type){
                let data=this.childDetails.forms[0].send_list.filter((i) => i.openid==list)
                return data[0][type]
            },
            getReverseList(data){
                let list = JSON.parse(JSON.stringify(data))
                return list.reverse();
            },
            handleMouseEnter(index) {
                Vue.set(this.tipVisibles, index, true);
            },
            handleMouseLeave(index) {
                Vue.set(this.tipVisibles, index, false);
            },
            showMyMouseEnter() {
               this.myListNode=true
            },
            showMyMouseLeave() {
                this.myListNode=false
            },
            showSub(list,type){
                let subData={}
                if(this.fromType=="list"){
                    subData=list.filter((i) => i.key==this.filterTask)
                }else{
                    subData=list.filter((i) => i.task==this.filterTask)
                }
                return subData[0][type]
            },
            indexData(callback){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("indexData") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        startYear: this.startYear, 
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            data.data.node.sortList=data.data.node.sort.filter((i) => data.data.node.completed.indexOf(i) == -1)
                            that.indexDataList=data.data
                            that.filterTable = data.data.financeStateList.map(item => {
                                return {
                                    text: item.label, 
                                    value: item.value 
                                };
                            });
                            that.skip_talk=data.data.node.skip_talk
                            that.pageData={}
                            if(data.data.myNode.length!=0){
                                if(that.filterNode!='' || that.filterTask!=''){
                                    that.initData(that.fromType,that.filterNode,that.filterTask)
                                }else{
                                    that.initData("my",data.data.myNode[0].node,data.data.myNode[0].task)
                                }
                               that.myNodeList= data.data.myNode.map(item => item.task).filter(task => task !== "");
                               if(data.data.myNode.length>3){
                                   let myNodeCount=data.data.myNode.slice(3, data.data.myNode.length)
                                   that.totalCountNode = myNodeCount.reduce((accumulator, currentValue) => accumulator + currentValue.count, 0);
                               }
                            }else{
                                that.pageData={}
                                that.filterNode='' 
                                that.filterTask=''
                            }
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.tableLoading=false
                    },
                    error: function(data) {
                        that.tableLoading=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            initData(type,node,task,from){
                this.fromType=type
                this.filterNode=node
                this.filterTask=task
                if(from && from=='from'){
                    this.tableLoading=false
                }else{
                    this.tableLoading=true
                }
                let showAll=this.indexDataList.node.completed.indexOf(node)!=-1?0:this.showAll
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("list") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        startYear: this.startYear, 
                        node: node,
                        task:task,
                        showAll:showAll
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.pageData=data.data
                            that.searchType=true
                            that.searchTableList={}
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.tableLoading=false
                    },
                    error: function(data) {
                        that.tableLoading=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            sendWechat(){
                $('#sendWechatModal').modal('show')
            },
            handleCheckAllChange(val) {
                this.checkedCities = val ? cityOptions : [];
                this.isIndeterminate = false;
            },
            handleCheckedCitiesChange(value) {
                let checkedCount = value.length;
                this.checkAll = checkedCount === this.cities.length;
                this.isIndeterminate = checkedCount > 0 && checkedCount < this.cities.length;
            },
            addChild(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("/mteaching/journals/classList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            data.data.forEach(item => {
                                item.childData=[]
                            })
                            that.classList=data.data
                            that.searchChildList=[]
                            that.childSelected=[]
                            that.searchText=''
                            that.classId=''
                            that.btnDisanled=false
                            $('#addClassModal').modal('show')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            getChild(list){
                let that=this
                if(that.classId==list.classid){
                    that.classId=''
                    return
                }
                that.classId=list.classid
                var childId=[]
                if(this.childSelected!=null){
                    for(var i=0;i<this.childSelected.length;i++){
                        childId.push(this.childSelected[i].id)
                    }
                }
                for(var i=0;i<that.classList.length;i++){
                    if(that.classList[i].classid==list.classid){
                        if(that.classList[i].childData.length!=0){
                            for(var j=0;j<that.classList[i].childData.length;j++){
                                if (childId.indexOf(that.classList[i].childData[j].id)!=-1) {
                                    that.classList[i].childData[j].disabled=true
                                }else{
                                    that.classList[i].childData[j].disabled=false
                                }
                            }
                            that.$forceUpdate()
                            return
                        }
                    }
                }
                that.childLoading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("/mteaching/student/childList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        classId:list.classid
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            for(var i=0;i<data.data.length;i++){
                                data.data[i].classTitle=list.title
                                data.data[i].stuLoading=true
                                data.data[i].loading=true
                                if (childId.indexOf(data.data[i].id)!=-1) {
                                    data.data[i].disabled=true
                                }else{
                                    data.data[i].disabled=false
                                }
                            }
                            that.sortData(data.data)
                            for(var i=0;i<that.classList.length;i++){
                                if(that.classList[i].classid==list.classid){
                                    that.classList[i].childData=data.data
                                }
                            }
                            that.childLoading=false
                            that.$forceUpdate()
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            searchChild(){
                var childId=[]
                if(this.childSelected!=null){
                    for(var i=0;i<this.childSelected.length;i++){
                        childId.push(this.childSelected[i].id+'')
                    }
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("/mteaching/student/studentSearch") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        child_name:this.searchText
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            for(var i=0;i<data.data.length;i++){
                                data.data[i].loading=true
                                if (childId.indexOf(data.data[i].id+'')!=-1) {
                                    data.data[i].disabled=true
                                }else{
                                    data.data[i].disabled=false
                                }
                            }
                            that.searchChildList=data.data
                            that.$forceUpdate()
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            sortData(list){
                <?php if (Yii::app()->language == 'zh_cn') { ?>
                    list.sort((a, b)=> a.name.localeCompare(b.name, 'zh'));
                <?php } else { ?>
                    list.sort((a, b) => a.name.charCodeAt(0) - b.name.charCodeAt(0));
                <?php } ?>
            },
            selectAll(list,index){
                for(var i=0;i<list.childData.length;i++){
                    if (!list.childData[i].disabled) {
                        if(this.skip_talk.by_grade.cond.indexOf(list.childData[i].level)!=-1){
                            list.childData[i].checkedSkip='1'
                            list.childData[i].checked=this.skip_talk.by_grade.default_check
                        }else if(this.skip_talk.by_uid.cond.map(String).indexOf(uid+"")!=-1){
                            list.childData[i].checkedSkip='1'
                            list.childData[i].checked=this.skip_talk.by_uid.default_check
                        }else{
                            list.childData[i].checked=false
                        }
                        this.childSelected.push(list.childData[i])
                        Vue.set(this.classList[index].childData[i], 'disabled', true);
                    }
                }
                this.$forceUpdate()
            },
            assignChildren(list,index,idx){
                if(idx=='search'){
                    this.searchChildList[index].disabled=true
                }else{
                    Vue.set(this.classList[index].childData[idx], 'disabled', true);
                }
                this.$forceUpdate()
                if(this.skip_talk.by_grade.cond.indexOf(list.level)!=-1){
                    list.checkedSkip='1'
                    list.checked=this.skip_talk.by_grade.default_check
                }else if(this.skip_talk.by_uid.cond.map(String).indexOf(uid+"")!=-1){
                    list.checkedSkip='1'
                    list.checked=this.skip_talk.by_uid.default_check
                }else{
                    list.checked=false
                }
                this.childSelected.push(list)
            },
            Unassign(data,index,type){
                for(var i=0;i<this.classList.length;i++){
                    for(var j=0;j<this.classList[i].childData.length;j++){
                        if(data.id==this.classList[i].childData[j].id){
                            Vue.set(this.classList[i].childData[j], 'disabled', false);
                        }
                    }
                }
                for(var i=0;i<this.searchChildList.length;i++){
                    if(data.id==this.searchChildList[i].id){
                        Vue.set(this.searchChildList[i], 'disabled', false);
                    }
                }
                this.$forceUpdate()
                this.childSelected.splice(index,1)
                if(type){
                    this.getTeacher()
                }
            },
            batchDel(){
                this.childSelected=[]
                for(var i=0;i<this.classList.length;i++){
                    for(var j=0;j<this.classList[i].childData.length;j++){
                        this.classList[i].childData[j].disabled=false
                    }                        
                }                
                this.$forceUpdate() 
            },
            saveChild(){
                let that=this
                this.childSelected
                if(this.childSelected.length==0){
                    resultTip({
                        error: 'warning',
                        msg: '请选择学生'
                    });
                    return
                }
                let childIds=[]
                let skip=[]
                for(var i=0;i<this.childSelected.length;i++){
                    childIds.push(this.childSelected[i].id)
                    skip.push(this.childSelected[i].checked)
                }
                this.btnDisanled=true
                $.ajax({
                    url: '<?php echo $this->createUrl("addApplication") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        childIds:childIds,
                        skip:skip,
                        startYear: this.startYear
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg:'添加成功'
                            });
                            that.indexData()
                            that.initData(that.fromType,that.filterNode,that.filterTask)
                            $('#addClassModal').modal('hide')
                            that.btnDisanled=false
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.btnDisanled=false
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.btnDisanled=false
                    },
                })
            },
            getqiniu(){
                $.ajax({
                    url: '<?php echo $this->createUrl("getQiniuToken") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        applicationId:this.applicationId,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            container.token=data.data.data
                            config['token'] = data.data.data
                            var uploader = new plupload.Uploader(config);
                            uploader.init();
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.msg
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            openModal(list,node){
                this.applicationId=list
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("node") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        applicationId:list,
                        node:node,
                        startYear: this.startYear
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.childDetails=data.data
                            that.applyUserInfo=data.data.info.appInfo
                            if(data.data.forms.length==1 && (data.data.info.baseInfo.status=='completed_continue' || data.data.info.baseInfo.status=='completed_confirm')){
                                that.implementer=that.childDetails.staffInfo[that.childDetails.forms[0].implementer]
                                that.implementerDate=data.data.forms[0].updated_at
                            }
                            childDetails=data.data
                            that.stepinfoList=[]
                            that.successStep=[]
                            that.editSortNav=[]
                            that.childDetailsInfo=data.data.info.baseInfo
                            if(node!=''){
                                that.stepName=node
                            }else{
                                // if(that.childDetails.info.baseInfo.status=='completed_continue'){
                                //     that.stepName='return'
                                // }else{
                                    that.stepName=data.data.info.baseInfo.status
                                // }
                            } 
                            that.editSortNav=that.indexDataList.node.sort.filter((i) => i!='completed_continue')
                            if(that.childDetails.info.baseInfo.status!='completed_continue' && that.childDetails.info.baseInfo.status!='completed_confirm'){
                                let index = that.editSortNav.indexOf(data.data.info.baseInfo.status);
                                if (index !== -1) {
                                    that.successStep = that.editSortNav.slice(0, index);
                                }
                                if(that.childDetails.info.baseInfo.status=='completed_quit'){
                                    that.successStep=that.successStep.concat(that.childDetails.info.baseInfo.status)
                                }
                            }else{
                                that.successStep=data.data.nodes
                            }
                            let clickStep=JSON.parse(JSON.stringify(that.successStep));
                            that.stepinfoList=  [...new Set(clickStep.concat(that.childDetails.info.baseInfo.status))];
                        
                            $('#myModal').modal('show')
                            $('#myModal .modal-body #stepTemplate').html('')
                            that.activeName=''
                            that.$nextTick(() => {
                                if(that.indexDataList.node.nodes[that.stepName].sub  || data.data.forms.length>1){
                                    let activeData=data.data.forms.filter(item => item.status == 0 && item.template_state=='edit');
                                    var currentData=activeData.length!=0?activeData[0]:data.data.forms[0]
                                    $('#type').val(currentData.key)
                                    $('#myModal .modal-body #stepTemplate').html( currentData.template )
                                    if(currentData.key=="it_equ" && currentData.template_state=='edit'){
                                        that.getqiniu()
                                    }
                                    setTimeout(() => {
                                        that.activeName=currentData.key
                                    }, 500);
                                }else{
                                    for(let i=0;i<data.data.forms.length;i++){
                                        $('#myModal .modal-body #stepTemplate').append(data.data.forms[i].template)
                                    }
                                }
                                head.Util.ajaxForm();
                                tooltip()
                                $('#applicationId').val(that.applicationId)
                                $('#node').val(that.stepName)
                            });                            
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            tabClick(tab, event){
                let data= this.childDetails.forms.find(item2 =>item2.key === this.activeName)
                $('#myModal .modal-body #stepTemplate').html( data.template )
                $('#type').val(data.key)
                $('#node').val(this.stepName)
                $('#applicationId').val(this.applicationId)
                head.Util.ajaxForm();
                if(data.key=="it_equ" && data.template_state=='edit'){
                    this.getqiniu()
                }
            },
            stepInfo(list){
                if(this.stepinfoList.indexOf(list)!=-1){
                    if(this.childDetails.nodes.indexOf(list)!=-1){
                        this.openModal(this.applicationId,list)
                    }else{
                        resultTip({
                            error: 'warning',
                            msg: '已跳过退学流程中的面谈步骤'
                        });
                    }
                }else{
                    if(this.childDetails.info.baseInfo.status=='completed_continue' || this.childDetails.info.baseInfo.status=='completed_confirm' ){
                        resultTip({
                            error: 'warning',
                            msg: '未填写'
                        });  
                    }else{
                        resultTip({
                            error: 'warning',
                            msg: '请完成当前步骤'
                        });  
                    }
                }
            },
            
            exportTab() {
                var exportData = [];
                const ws_name = "SheetJS";
                var myDate=new Date;
                var year=myDate.getFullYear(); //获取当前年
                var mon=myDate.getMonth()+1<10?"0"+(myDate.getMonth()+1):myDate.getMonth()+1; //获取当前月
                var date=myDate.getDate()<10?"0"+myDate.getDate():myDate.getDate(); //获取当前日
                let nowDate=year+'-'+mon+'-'+date
                var names=''
                if(this.filterTask!='' && this.indexDataList.node.nodes[this.filterNode].sub){
                    let sub=this.indexDataList.node.nodes[this.filterNode].sub.filter((i) => i.key==this.filterTask)
                    names=this.indexDataList.node.nodes[this.filterNode].title+'_'+sub[0].title+'_'+nowDate              
                }else{
                    names=this.indexDataList.node.nodes[this.filterNode].title+'_'+nowDate
                }
                var filename=names+'.xlsx'
                var table = document.getElementById('exportTable');
                if (table.querySelector('.el-table__fixed')) {
                    table.removeChild(table.querySelector('.el-table__fixed'));
                }
                if (table.querySelector('.el-table__fixed-right')) {
                    table.removeChild(table.querySelector('.el-table__fixed-right'));
                }
                var wb = XLSX.utils.table_to_book(table);
                // 获取第一个工作表
                const ws = wb.Sheets[wb.SheetNames[0]];

                // 获取所有行
                const rows = XLSX.utils.sheet_to_json(ws, { header: 1 });

                // 删除每一行的最后一列
                rows.forEach(row => row.pop());

                // 创建新的工作表
                const newWs = XLSX.utils.json_to_sheet(rows, { skipHeader: true });

                // 更新工作簿
                wb.Sheets[wb.SheetNames[0]] = newWs;
                // return
                // this.itemKey = Math.random();
                return XLSX.writeFile(wb, filename+'.xlsx');

                // for(let i=0;i<this.pageData.items.length;i++){
                //     var data={
                //         '退学编号':this.pageData.items[i].unique_id,
                //         '<?php echo Yii::t('global', '学生ID') ?>':this.pageData.items[i].child_id,
                //         "<?php echo Yii::t('labels', '学生姓名') ?>":this.pageData.childInfo[this.pageData.items[i].child_id].name,
                //         "<?php echo Yii::t('labels', '班级') ?>":this.pageData.childInfo[this.pageData.items[i].child_id].className,
                //         "<?php echo Yii::t('labels', '类型') ?>":this.indexDataList.typeList[this.pageData.items[i].type],
                //         "<?php echo Yii::t('labels', '退学日期') ?>":this.pageData.items[i].withdrawal_date
                //     }
                //     if(this.pageData.items[i].task_status===0){
                //         var  task_status={
                //             "<?php echo Yii::t("labels", '处理状态')?>": '待处理'
                //         }
                //     }else{
                //         var  task_status={
                //             "<?php echo Yii::t("labels", '处理状态')?>": '已完成 | '+this.pageData.staffInfo[this.pageData.items[i].implementer].name +' | '+this.pageData.items[i].implement_at
                //         }
                //     }
                //     data=Object.assign(data,task_status)
                //     exportData.push(data)
                // }
                // let xlslHeader= [
                //     '<?php echo Yii::t('global', '退学编号') ?>',
                //     '<?php echo Yii::t('labels', '学生ID') ?>',
                //     '<?php echo Yii::t('labels', '学生姓名') ?>',
                //     '<?php echo Yii::t('labels', '班级') ?>',
                //     '<?php echo Yii::t('labels', '类型') ?>',
                //     '<?php echo Yii::t('labels', '退学日期') ?>',
                //     '<?php echo Yii::t('labels','处理状态')?>',
                //     '<?php echo Yii::t('labels','处理状态')?>',
                // ]
                // var wb=XLSX.utils.json_to_sheet(exportData,{
                //     origin:'A1',
                //     header: xlslHeader
                // });
                // const workbook = XLSX.utils.book_new();
                // XLSX.utils.book_append_sheet(workbook, wb, ws_name);
                // const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                // const blob = new Blob([wbout], {type: 'application/octet-stream'});
                // let link = document.createElement('a');
                // link.href = URL.createObjectURL(blob);
                // link.download = filename;
                // link.click();
                // setTimeout(function() {
                //     URL.revokeObjectURL(link.href);
                //     link.remove();
                // }, 500);
            },
            delApplication(id){
                if(id){
                    this.delData=id
                    this.delType='del'
                    $('#delModal').modal('show')
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("delApplication") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        applicationId:this.delData,
                        startYear: this.startYear
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.initData(that.fromType,that.filterNode,that.filterTask)
                           $('#delModal').modal('hide')
                           resultTip({
                                msg: data.state
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {},
                })
            },
            confirmReject(){
                let that=this
                if(this.rejectMemo==''){
                    resultTip({
                        error: 'warning',
                        msg: '请输入驳回理由'
                    });
                    return
                }
                this.rejectBtn=true
                $.ajax({
                    url: '<?php echo $this->createUrl("reject") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        applicationId: this.applicationId,
                        node:'return',
                        type:this.rejectType,
                        memo:this.rejectMemo,
                        startYear: this.startYear
                    },
                    success: function(data) {
                        that.rejectBtn=false
                        if (data.state == 'success') {
                            that.indexData()
                            // that.initData(that.fromType,that.filterNode,that.filterTask,'from')
                            that.openModal(that.applicationId,'')
                           $('#rejectModal').modal('hide')
                           resultTip({
                                msg: data.state
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.rejectBtn=false
                    },
                })
            },
            confirmCancelReject(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("cancelReject") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        applicationId: this.applicationId,
                        node:'return',
                        type:this.cancelRejectType,
                        startYear: this.startYear
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.indexData()
                            // that.initData(that.fromType,that.filterNode,that.filterTask,'from')
                            that.openModal(that.applicationId,'')
                           $('#cancelRejectModal').modal('hide')
                           resultTip({
                                msg: data.state
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {},
                })
            },
            getrejectedList(id){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("rejectLog") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        applicationId: this.applicationId,
                        node:'return',
                        type:id,
                        startYear: this.startYear
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.rejectedListData=data.data
                           $('#rejectedListModal').modal('show')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {},
                })
            },
            cancelWithdraw(type){
                if(type){
                    this.delType=type
                    $('#delModal').modal('show')
                    return
                }
                if(!this.cancel_apply){
                    resultTip({
                        error: 'warning',
                        msg: '请勾选继续操作'
                    });
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("recover") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        applicationId:this.applicationId,
                        startYear: this.startYear
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.indexData()
                            that.initData(that.fromType,that.filterNode,that.filterTask)
                           $('#delModal').modal('hide')
                           $('#myModal').modal('hide')
                           resultTip({
                                msg: data.state
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {},
                })
            },
            stopDropout(type){
                if(type){
                    this.delType=type
                    $('#delModal').modal('show')
                    return
                }
                var data={}
                if(this.delType=='confirm'){
                    if(!this.confirm){
                        resultTip({
                            error: 'warning',
                            msg: '请勾选继续操作'
                        });
                        return
                    }
                    data={
                        applicationId:this.applicationId,
                        confirm:this.confirm,
                        startYear: this.startYear
                    }
                }else{
                    if(!this.confirm_continue){
                        resultTip({
                            error: 'warning',
                            msg: '请勾选继续操作'
                        });
                        return
                    }
                    
                    data={
                        applicationId:this.applicationId,
                        startYear: this.startYear
                    }
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("cancelApplication") ?>',
                    type: "post",
                    dataType: 'json',
                    data:data,
                    success: function(data) {
                        if (data.state == 'success') {
                            that.indexData()
                            that.initData(that.fromType,that.filterNode,that.filterTask)
                           $('#delModal').modal('hide')
                           $('#myModal').modal('hide')
                           resultTip({
                                msg: data.state
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {},
                })
            },
            handleSelectionChange(val) {
                this.multipleSelection = val;
            },
            batchHandle(){
                if(this.multipleSelection.length==0){
                    resultTip({
                        error: 'warning',
                        msg: '请选择批量处理的学生'
                    });
                    return
                }
                this.batchBalanceLib=false
                this.batchMemo=''
                this.batchBalance=''
                $('#batchModal').modal('show')
            },
            confirmBatch(){
                var ids=[]
                var balance=''
                for(var i=0;i<this.multipleSelection.length;i++){
                    ids.push(this.multipleSelection[i].id)
                }
                var data={}
                if(this.filterTask=="insurance" || this.filterTask=="enrollment"){
                    data={
                        ids:ids,
                        node:this.filterNode,
                        type:this.filterTask,
                        remark:this.batchMemo,
                        balance:this.batchBalance,
                        startYear: this.startYear
                    }
                }else if(this.filterTask=="academic" || this.filterTask=="it"){
                    data={
                        ids:ids,
                        node:this.filterNode,
                        type:this.filterTask,
                        remark:this.batchMemo,
                        closed:this.closed?1:0,
                        startYear: this.startYear
                    }
                }else{
                    if(!this.batchBalanceLib){
                        resultTip({
                            error: 'warning',
                            msg: '请勾选'
                        });
                        return
                    }
                    data={
                        ids:ids,
                        node:this.filterNode,
                        type:this.filterTask,
                        remark:this.batchMemo,
                        startYear: this.startYear
                    }
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("batchSave") ?>',
                    type: "post",
                    dataType: 'json',
                    data: data,
                    success: function(data) {
                        if (data.state == 'success') {
                            that.indexData()
                            that.initData(that.fromType,that.filterNode,that.filterTask)
                           $('#batchModal').modal('hide')
                           resultTip({
                                msg: data.state
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {},
                })
            },
            cancelSubmitData(type){
                if(type){
                    this.delType='cancel'
                    this.cancelType=type
                    $('#delModal').modal('show')
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("revokeTask") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        applicationId:this.applicationId,
                        node:this.filterNode,
                        task:this.cancelType,
                        startYear: this.startYear
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            container.openModal(container.applicationId,'')
                            // container.initData(container.fromType,container.filterNode,container.filterTask,'from')
                           $('#delModal').modal('hide')
                           resultTip({
                                msg: data.state
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {},
                })
            },
            stuRemoteMethod(query) {
                let that=this
                if (query !== '') {
                    this.loading = true;
                    $.ajax({
                        url: '<?php echo $this->createUrl("teacherSearch") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            searchString:query,
                            startYear: this.startYear
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.otherStaff = Object.values(data.data);
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                            that.loading = false;
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.loading = false;
                        },
                    })
                } else {
                    this.options = [];
                }
            },
            addOtherTalk(id){
                this.otherStaff.forEach(item => {
                    if(item.uid==this.staffCheck){
                        this.addOtherList=item
                    }
                })
                addTalkDom()
                this.otherStaff=[]
                this.staffCheck=''
                $('#addOtherTalkModal').modal('hide')
            },
            continueRefund(id){
                this.editRefundId=id
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("refundRecordList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        app_id:id,
                        startYear: this.startYear
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            if(data.data.recordList.length!=0){
                                data.data.recordList.forEach(item => {
                                    item.isEdit=true
                                });
                                that.refundData = data.data.recordList;
                                that.refundDataCopy = JSON.parse(JSON.stringify(data.data.recordList));
                            }
                            that.refundstaffInfo = data.data.staffInfo;
                            $('#addRefundModal').modal('show')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.loading = false;
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.loading = false;
                    },
                })
            },
            financeState(id, financeState){
                this.applicationId=id
                
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getFinanceStateInfo") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{
                        app_id:id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            console.log(data)
                            that.financeStateValue=data.data.state
                            that.financeTextarea=data.data.state_memo
                            that.file_list=data.data.file_list
                            that.uploadToken = {'token':data.data.qiniu_token};
                            $('#financeStateModal').modal('show')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
               
            },
            handleAvatarSuccess(res, file) {
                resultTip({
                    msg: '上传成功'
                });
                this.loadingImg=false
                this.file_list.push({
                    file_key:res.data.file_key,
                    link_id: res.data.link_id,
                    link_type:res.data.link_type,
                    mime_type: res.data.mime_type,
                    sort: res.data.sort,
                    title: res.data.title,
                    _id: res.data._id,
                })
            },
            beforeAvatarUpload(file) {
                let fileType=file.type.split('/')
                const isJPG = true;
                const isLt2M = file.size / 1024 / 1024 < 2;
                if(fileType[0]=="image"){
                    if (!isLt2M) {
                        resultTip({
                            error: 'warning',
                            msg:'上传图片大小不能超过 2MB!'
                        });
                    }
                    return isJPG && isLt2M;
                }
                this.loadingImg=true
            },
            delFile(id){
                if(id){
                    this.delId=id
                    this.delType='file'
                    $("#delModal").modal('show')
                    return
                }
                let that=this
                that.btnDisanled=true
                $.ajax({
                    url: '<?php echo $this->createUrl("delFinanceStateFile") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        id:this.delId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.file_list = that.file_list.filter(item => item._id !== that.delId);
                            resultTip({
                                msg: data.message
                            });
                            $("#delModal").modal('hide')
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.btnDisanled=false
                    },
                    error: function(data) {
                        that.btnDisanled=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            setFinanceState(){
                let that = this
                $.ajax({
                    url: '<?php echo $this->createUrl("setFinanceState") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        app_id:that.applicationId,
                        state: that.financeStateValue,
                        startYear: this.startYear,
                        state_memo:this.financeTextarea,
                        file_list:this.file_list
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg: data.message
                            });
                            $('#financeStateModal').modal('hide')
                            that.indexData();
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            delRefund(list,index){
                if(list){
                    this.delRefundList=list
                    this.delRefundIndex=index
                }
                if(this.delRefundList.id==''){
                    this.refundData.splice(this.delRefundIndex,1)
                }else{
                    if(list){
                        this.delType='delRefund'
                        $('#delModal').modal('show')
                        return
                    }
                    let that=this
                    $.ajax({
                        url: '<?php echo $this->createUrl("refundRecordDel") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            record_id:this.delRefundList.id,
                            startYear: this.startYear
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                resultTip({
                                    msg: data.message
                                });
                                $('#delModal').modal('hide')
                                that.refundData.splice(that.delRefundIndex,1)
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                            that.loading = false;
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.loading = false;
                        },
                    })
                }
            },
            canRefund(list,index){
                this.refundData[index]=this.refundDataCopy[index]
                this.refundData[index].isEdit=true
                this.$forceUpdate()
            },
            saveRefund(list,index){
                let that=this
                    $.ajax({
                        url: '<?php echo $this->createUrl("refundRecordEdit") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            app_id:this.editRefundId,
                            amount:list.amount,
                            memo:list.memo,
                            record_id:list.id,
                            startYear: this.startYear
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                resultTip({
                                    msg: data.message
                                });
                                that.continueRefund(that.editRefundId)
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                            that.loading = false;
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.loading = false;
                        },
                    })
            },
            addRefund(){
                for(let i=0;i<this.refundData.length;i++){
                    if(this.refundData[i].id==''){
                        resultTip({
                            error: 'warning',
                            msg:'请填写完已追加退费金额和内容'
                        });
                        return
                    }
                }
                this.refundData.push({
                    amount: '',
                    memo: '',
                    isEdit:false,
                    id:''
                })
                this.refundDataCopy.push({
                    amount: '',
                    memo: '',
                    isEdit:false,
                    id:''
                })
            },
            formatAmount(o_amount) {
                let amount = Number(o_amount);
                if (typeof amount !== 'number' || isNaN(amount)) {
                    return '';
                }
                try {
                    return amount.toLocaleString('zh-CN', {
                        style: 'currency',
                        currency: 'CNY'
                    });
                } catch (error) {
                    return '';
                }
            },
            // 根据 value 返回对应的 label
            getStatusLabel(statusList, value) {
                const status = statusList.find(item => item.value === value);
                return status ? status.label : '未知状态';
            },
            tasksLink(){
                window.open('<?php echo $this->createUrl('tasksOwner', array('branchId' => $this->branchId)); ?>')
            },
            formatter(row, column) {
                return row.detail.total_amount;
            },
            filterHandler(value, row) {
                return row.finance_state === value;
            },
        },
    })

function formatAmount(o_amount) {
    let amount = Number(o_amount);
    if (typeof amount !== 'number' || isNaN(amount)) {
        return '';
    }
    try {
        return amount.toLocaleString('zh-CN', {
            style: 'currency',
            currency: 'CNY'
        });
    } catch (error) {
        return '';
    }
}
</script>
<style>
    [v-cloak]{display: none;}
    .myWait{
        padding:8px 24px;
        background: rgba(217, 83, 79, 0.08);
        border-radius: 4px;
        display:inline-flex;
        align-items:center;
        height:75px;
        border: 1px solid transparent;
    }
    .waitNum{
        text-align: center;
        padding:5px 16px;
        border: 1px solid transparent;
        /* border-left: 1px solid #D9D9D9; */
    }
    .myWaiting{
        height: 100%;
        line-height: 56px;
        border-right: 1px solid #E5E6EB;
        margin-right: 16px;
    }
    .waitNumBorder,.waitNum:hover{
        cursor: pointer;
        border: 1px solid rgba(217, 83, 79, 1);
        border-radius: 4px;
        background:rgba(217, 83, 79, 0.08)
    }
    .waitNum:hover div, .waitNumBorder div{
        color: rgba(217, 83, 79, 1) !important;
    }
    .withdrawNum:hover,.withdrawNum1 {
        cursor: pointer;
        border: 1px solid rgba(77,136,210) !important;
    }
    .withdrawNum{
        padding:24px 24px;
        background:rgba(77,136,210,0.08);
        border-radius: 4px;
        display:inline-flex;
        align-items:center;
        height:75px;
        border: 1px solid transparent;
        position: relative;
    }
    .continue_confirm{
        padding:8px 16px;
        border-radius: 4px;
        display:inline-flex;
        align-items:center;
        height:75px;
        border: 1px solid #E5E6EB;
        position: relative;
    }
    .continueNum{
        background: rgba(92, 184, 92, 0.08);
        border-radius: 4px;
        text-align: center;
        padding:5px 16px;
        border: 1px solid transparent;
    }
    .continueNum:hover,.continueNum1{
        border: 1px solid rgba(92, 184, 92, 1) !important;
        color:rgba(92, 184, 92, 1);
        cursor: pointer;
    }
    .continueNum:hover div,.continueNum1 div{
        color:rgba(92, 184, 92, 1) !important;
    }
    .confirmNum:hover,.confirmNum1{
        border: 1px solid #D9534F !important;
        color:rgba(217, 83, 79, 1);
        cursor: pointer;
    }
    .confirmNum:hover div,.confirmNum1 div{
        color:rgba(217, 83, 79, 1) !important;
    }
    .confirmNum{
        background: rgba(217,83,79,0.08);
        border-radius: 4px;
        text-align: center;
        padding:5px 16px;
        border: 1px solid transparent;
    }
    .el-step__icon-inner{
        font-size:12px;
        color:#333
    }
    .el-step.is-horizontal:nth-last-child(2) .el-step__head .el-step__line{
        width: 95%;
    }
    .el-step.is-horizontal .el-step__line{
        left: 50%;
        right: 0;
        width: 150%;
        background-color: #E5E6EB;
        height: 1px;
    }
    /* .el-step__line{
        background-color: #E5E6EB;
    }
    .el-step.is-horizontal .el-step__line{
        height: 1px;
    } */
    .el-step__icon.is-text{
        border: 1px solid #D9D9D9;
    }
    .el-step.is-center .el-step__description{
        padding-left:0%;
        padding-right: 0%;
    }
    .listMedia{
        padding:8px;
        border: 1px solid #fff;
        margin:0
    }
    .listMedia:hover{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        border: 1px solid #428bca;
        cursor: pointer;
    }
    .p0{
        padding:0
    }
    .avatar42 {
        width: 42px;
        height: 42px;
        border-radius: 50%;
        object-fit: cover;
    }
    .avatar24 {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        object-fit: cover;
    }
    .avatar38 {
        width:38px;
        height: 38px;
        border-radius: 50%;
        object-fit: cover;
    }
    .lineHeight {
        line-height:42px;
    }
    .childList {
        max-height: 250px;
        min-height: 40px;
        padding: 14px;
        overflow-y: auto;
    }
    .border {
        border: 1px solid #E8EAED;
        border-radius: 4px;
    }
    .allCheck {
        position: absolute;
        right: 10px;
        top: 4px;
    }
    .borderLeft{
        border-left: 1px solid #EBEDF0;
    }
    .scroll-box::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius   : 10px;
        background-color: #ccc;
        background-image: none
    }
    .scroll-box::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0);
        background   : #fff;
        border-radius: 10px;
        border:none
    }
    .bluebg{
        color: #428bca;
    }
    
    .borderTopNone{
        border-top:none !important
    }
    .p0{
        padding:0
    }
    .ellipsis{
        flex: 1;
  min-width: 0; /* 防止子元素固定宽度问题 */
  overflow: hidden; /* 超出的文本会被隐藏 */
  white-space: nowrap; /* 文本不会换行 */
  text-overflow: ellipsis; /* 超出的文本显示为省略号 */
        overflow: hidden; 
        text-overflow: ellipsis; 
        white-space: nowrap;
    }
    .el-divider--vertical{
        height:2em
    }
    .width90{
        width:90%
    }
    .flexStart{
        align-items:start
    }
    .flexWidth{
        width:155px
    }
    .flexStart .radio-inline{
        padding-top:0px
    }
    .moreText{
        padding:12px;
        font-size:12px;
        background: #F7F7F8;
        border-radius: 2px;
        margin-top:8px
    }
    .labelDate{
        background: #F7F7F8;
        border-radius: 2px;
        padding:2px 6px;
        margin-left:10px;
        font-size:12px
    }
    .el-checkbox__input.is-checked .el-checkbox__inner, .el-checkbox__input.is-indeterminate .el-checkbox__inner{
        background-color: #428bca;
        border-color: #428bca;
    }
    .el-checkbox__input.is-checked+.el-checkbox__label{
        color: #428bca;
    }
    .img28{
        width: 28px;
        height: 28px;
        border-radius: 50%;
        object-fit: cover;
    }
    .wechatCheck{
        height: 112px;
        background: #FAFAFA;
        border-radius: 4px;
        display:inline-flex;
        padding:6px 16px;
        margin-right:16px;
        align-items:center
    }
    .borderTop{
        border-top:1px solid #E5E6EB
    }
    .financeList{
        padding:16px 0
    }
    .borderBto{
        border-bottom: 1px solid #E5E6EB;
    }
    .yellow{
        color: #F0AD4E;
    }
    .yellowBg{
        background:#FDF8F1
    }
    .redColor{
        color:#D9534F;
    }
    .redBg{
        background: #FCF1F1
    }
    .greenColor{
        color:#5CB85C
    }
    .greenBg{
        background: #F2F9F2
    }
    .reject{
        padding:16px;
        background: #FCF1F1;
        border-radius: 4px;
        margin-bottom:24px
    }
    .align_end{
        align-items: end;
    }
    .financeInput {
      accent-color: #D9534F;
    }
    .financeInput[type="checkbox"]:not(:checked) {
        border-color: #D9534F; /* 灰色 */
    }
    .redDot{
        width: 6px;
        height: 6px;
        background: #D9534F;
        display: inline-block;
        border-radius: 50%;
        position: absolute;
        right: -6px;
        top: -6px;
    }
   
    label{
        /* line-height: 1.42857143; */
    }
    .completedText{
        color: #fff;
        text-align: center;
        -webkit-transform: rotate(-45deg);
        transform: rotate(45deg);
        -ms-transform: rotate(-45deg);
        -o-transform: rotate(-45deg);
        position: absolute;
        padding:5px 0;
        left: -72px;
        top: -85px;
        font-size: 14px;
        width:80px
    }
    .completedQuit{
        display: inline-block;
        width: 0;
        height: 0;
        border-top: 100px solid #666666;
        border-left: 100px solid transparent;
        position: fixed;
        top: 53px;
        right: 1px;
    }
    .colorStep{
        color:#333
    }
    .nav-wizard .active .colorStep{
        color:#fff !important
    }
    .stepHover:hover .stepTitle,.stepHover:hover .stepNum{
        color:#428bca !important;
        cursor: pointer;
        
    }
  
    .stepHover:hover .is-text{
        background:#428bca
    }
    .stepHover:hover .is-text .el-step__icon-inner{
        color:#fff;
    }

    .stepActive .is-text{
        background:#428bca
    }
    .stepActive .is-text .el-step__icon-inner{
        color:#fff;
    }
    .stepTitle{
        white-space: nowrap
    }
    .stepActive .stepTitle,.stepActive .stepNum{
        color: #428bca !important;
        
    }
    .inline_flex{
        display: inline-flex
    }
    .tableCss{
        margin-top:9px;
    }
    .tableCss td{
        vertical-align: middle !important;
        font-size:14px;
        padding: 12px 16px !important;
        color:#666
    }
    .tableCss thead tr{
        background:#FAFAFA
    }
    .tableCss thead tr td{
        color:#333;
        font-weight:bold
    }
    .lableHeight{
        line-height: 1.42857143
    }
    .childSkip{
        background: #FAFAFA;
        border-radius: 4px;
        padding: 1px 36px;
        color: #666;
        margin-top: 8px;
    }
    .dot{
        width: 8px;
        height: 8px;
        background: #428bca;
        border-radius:50%;
        margin-top: 5px;
    }
    .borderAppli{
        border-radius: 4px;
        border: 1px solid #E5E6EB;
        padding:24px
    }
    .stepBorderLeft{
        position: absolute;
        height: 100%;
        width: 1px;
        background: #428bca;
        left: 3px;
        top: 8px;
    }
    .labelTag{
        font-size:12px;
        padding:4px 8px;
        margin-left:16px
    }
    .itemReturn{
        margin: 9px 0;
    }
    .infoWidth{
        width:60px
    }
    .filterTag{
        background-color: rgba(77, 136, 210, 0.1);
        border-color: rgba(77, 136, 210, 1);
        color: rgba(77, 136, 210, 1);
    }
    .filterTag .el-tag__close{
        color: rgba(77, 136, 210, 1);
    }
    .filterTag .el-tag__close:hover{
        background: rgba(77, 136, 210, 1);
    }
    .borderRight{
        border-right:1px solid #ccc
    }
    .filterTask{
        padding: 2px 10px;
    }
    .filterTask:hover{
        background: rgba(77, 136, 210, 0.08);
        color:#428bca;
        border-radius:4px;
        cursor: pointer;
    }
   .stepMore:hover{
        background:rgba(77, 136, 210, 0.08);
        color:#428bca;
        cursor: pointer;
    }
    .stepMore{
        display: inline-block;
        padding: 0 4px 4px 4px;
        line-height: 1.5;
        color:#333;
        border-radius:4px;

    }
    .selectSub span{
        color:#428bca !important;
    }
    .selectSub,.dropdownHover:hover{
        background:rgba(77, 136, 210, 0.08) !important;
    }
    .mydropdownHover{
        border: 1px solid transparent;
        line-height:24px;
        padding:6px
    }

    body span{
        word-break: keep-all !important;
        overflow-wrap: normal !important; /* 防止溢出时断词 */
    }

    .waitNumBorder span,.mydropdownHover:hover span{
        color:rgba(217, 83, 79, 1) !important
    }
    .waitNumBorder,.mydropdownHover:hover{
        background:rgba(217, 83, 79, 0.08) !important;
        border: 1px solid rgba(217, 83, 79, 1);
        border-radius:4px
    }
    .el-tabs__item.is-active ,.el-tabs__item.is-active .question ,.el-tabs__item.is-active .greenColor{
        color:#428bca !important;
    }
    .question{
        color:#999
    }
    .tabHover:hover, .tabHover:hover .greenColor,.tabHover:hover .question{
        color:#428bca !important;
    }
    .searchText .el-input-group__append{
        background-color: #428bca;
        color: #fff;
        border: 1px solid #428bca;
        padding:0;
        cursor: pointer;
    }
    .searchText .el-input-group__append span{
        display: block;
        height: 30px;
        padding: 0 20px;
        line-height: 30px;
        background-color: #428bca;
        color: #fff;
    }
    .navTab > li > a{
        padding: 6px 15px;
    }
    .tagStatus{
        border-radius:4px;
        padding:4px 6px
    }
    .myMore{
        display: block;
        padding: 5px;
        border: 1px solid transparent;
        height: 26px;
        line-height: 10px;
    }
    .myMore:hover{
        background: rgba(217, 83, 79, 0.08) !important;
        border: 1px solid rgba(217, 83, 79, 1);
        border-radius: 4px;
        color:rgba(217, 83, 79, 1) !important
    }
    .dropout_confirm{
        background: #FCF1F1;
        border-radius: 4px;
        padding:10px 16px;
        display:flex;
        color: #D9534F;
        font-size: 12px;
        align-items: center;
    }
    .tagList{
        background: #F0F5FB;
        border-radius: 2px;
        font-size: 12px;
        color: #4D88D2;
        padding:4px 6px ;
        display:inline-block;
        line-height:1
    }
    .nav-wizard > li>a:hover{
	   background:#d5d5d5
	}
	.nav-wizard > li{
	    text-align: center;
	}
	.nav-wizard a{
		color:#ADADAD;
		font-size:13px
	}
    .nav-wizard > li.successactive > a:after{
        border-left-color: rgba(77, 136, 210, 0.12);
    }
    .nav-wizard > li.successactive > a, .nav-wizard > li.successactive > a:hover, .nav-wizard > li.successactive > a:focus{
        color: #428bca !important;
        background-color: rgba(77, 136, 210, 0.12);
    }
    .successactive .colorStep{
        color: #428bca !important;
    }
    .nav-wizard > li:not(:last-child) > a:after{
        border-bottom: 18px inset transparent;
    }
    .navSteps > li > a{
        padding: 10px 14px;
    }
    .eventsNone{
        opacity: 0.7;
        pointer-events: none;
    }
    .notAllowed{
        cursor: not-allowed;
    }
    .editRefund{
        padding:16px 24px;
        background: #F0F5FB;
        border-radius: 4px;
    }
    .implementerPoint{
        position: absolute;
        width: 9px;
        height: 9px;
        background: #5CB85C;
        border-radius: 50%;
        left: 22px;
        top: 0;
        border: 1px solid #fff;
    }
    .eventsNone{
        opacity: 0.7;
        pointer-events: none;
    }
    .notAllowed{
        cursor: not-allowed;
    }
    .el-tabs__item{
        padding: 0px 15px; 
    }
    .el-upload__input{
        display:none !important
    }
    .flexList{
        padding: 5px 12px;
        background: #F7F7F8;
        font-size: 14px;
        align-items: center;
        margin-top:8px
    }
    .flexList .flex1{
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .ml0{
        margin-left:0 !important
    }
    .bgRed{
        background:#D9534F
    }
    .el-table-filter__list-item.is-active{
        background-color: #428bca !important;
        color: #fff !important
    }
    .el-table-filter__list-item:hover{
        background-color: #ecf5ff;
        color: #428bca
    }
</style>