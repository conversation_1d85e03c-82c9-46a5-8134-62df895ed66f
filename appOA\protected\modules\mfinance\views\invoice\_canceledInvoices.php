<?php   Yii::import('application.components.policy.PolicyApi');
		$policyApi = new PolicyApi($invoice->schoolid);
 ?>
		<div class="row" id="viewData">
        <div class="col-md-12">
            <table class="table table-bordered table-hover">
                <thead>
                <tr>
                    <th style="width: 5%;">班级ID</th>
                    <th style="width: 12%;">班级</th>
                    <th style="width: 6%;">学生ID</th>
                    <th style="width: 9%;">学生名字</th>
                    <th style="width: 12%;">标题</th>
                    <th style="width: 9%;">类型</th>
                    <th style="width: 11%;">开始时间</th>
                    <th style="width: 11%;">结束时间</th>
                    <th style="width: 11%;">作废时间</th>
                    <th style="width: 7%;">金额</th>
                    <th style="width: 7%;">状态</th>
                </tr>
                </thead>
                <tbody>
				<?php foreach($invoice as $user): ?>
					<tr>
					   <th><?php echo $user->classid ?></th>
					   <th><?php echo $user->classInfo->title ?></th>
					   <th><?php echo $user->childid ?></th>
					   <th><?php echo $user->childprofile->getChildName()?></th>
					   <th><?php echo $user->title ?></th>
					   <th> <?php echo $policyApi->renderFeeType($user->payment_type);?></th>
					   <th><?php if($user->startdate){ echo date("Y-m-d",$user->startdate) ;}?></th>
					   <th><?php if($user->startdate){ echo date("Y-m-d",$user->enddate) ;} ?></th>
					   <th><?php echo date("Y-m-d H:i:s",$user->send_timestamp) ?></th>
					   <th class="text-right"><?php echo $user->amount ?></th>
					   <th><?php  if($user->apportion_status){?>已导入<?php }else{ ?> 未导入<?php }?></th>
					</tr>
				<?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
	<a href="<?php echo $this->createUrl('invoice/ExportCanceledInvoices',array('branchId'=>Yii::app()->request->getParam('branchId'),'enddate'=>Yii::app()->request->getParam('enddate'),'startdate'=>Yii::app()->request->getParam('startdate'))); ?>" class="btn btn-success">导出</a>