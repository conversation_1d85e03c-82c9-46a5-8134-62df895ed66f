<?php
class WxPayDataBase
{
    public $cfg = array();
	public $values = array();
	
	/**
	* 设置签名，详见签名生成算法
	* @param string $value 
	**/
	public function SetSign()
	{
		$sign = $this->MakeSign();
		$this->values['sign'] = $sign;
		return $sign;
	}
	
	/**
	* 获取签名，详见签名生成算法的值
	* @return 值
	**/
	public function GetSign()
	{
		return $this->values['sign'];
	}
	
	/**
	* 判断签名，详见签名生成算法是否存在
	* @return true 或 false
	**/
	public function IsSignSet()
	{
		return array_key_exists('sign', $this->values);
	}

	/**
	 * 输出xml字符
	 * @throws WxPayException
	**/
	public function ToXml()
	{
		if(!is_array($this->values) 
			|| count($this->values) <= 0)
		{
    		return false;
    	}
    	
    	$xml = "<xml>";
    	foreach ($this->values as $key=>$val)
    	{
    		if (is_numeric($val)){
    			$xml.="<".$key.">".$val."</".$key.">";
    		}else{
    			$xml.="<".$key."><![CDATA[".$val."]]></".$key.">";
    		}
        }
        $xml.="</xml>";
        return $xml; 
	}
	
    /**
     * 将xml转为array
     * @param string $xml
     * @throws WxPayException
     */
	public function FromXml($xml)
	{	
		if(!$xml){
			return false;
		}
        //将XML转为array
        //禁止引用外部xml实体
        libxml_disable_entity_loader(true);
        $this->values = json_decode(json_encode(simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA)), true);		
		return $this->values;
	}
	
	/**
	 * 格式化参数格式化成url参数
	 */
	public function ToUrlParams()
	{
		$buff = "";
		foreach ($this->values as $k => $v)
		{
			if($k != "sign" && $v != "" && !is_array($v)){
				$buff .= $k . "=" . $v . "&";
			}
		}
		
		$buff = trim($buff, "&");
		return $buff;
	}
	
	/**
	 * 生成签名
	 * @return 签名，本函数不覆盖sign成员变量，如要设置签名需要调用SetSign方法赋值
	 */
	public function MakeSign()
	{
		//签名步骤一：按字典序排序参数
		ksort($this->values);
		$string = $this->ToUrlParams();
		//签名步骤二：在string后加入KEY
		$string = $string . "&key=".$this->cfg['key'];
		//签名步骤三：MD5加密
		$string = md5($string);
		//签名步骤四：所有字符转为大写
		$result = strtoupper($string);
		return $result;
	}
	
	/**
	 * 获取设置的值
	 */
	public function GetValues()
	{
		return $this->values;
	}

    /**
     *
     * 检测签名
     */
    public function CheckSign()
    {
        // if(!$this->IsSignSet()){
        //     return true;
        // }

        $sign = $this->MakeSign();
        if($this->GetSign() == $sign){
            return true;
        }
        return false;
    }

    /**
     * 将xml转为array
     * @param string $xml
     * @throws WxPayException
     */
    public function xml2array($xml)
    {
        $this->FromXml($xml);
        if($this->CheckSign()){
            return $this->GetValues();
        }
        return false;
    }

    /**
    * 设置接收微信支付异步通知回调地址
    * @param string $value 
    **/
    public function SetNotify_url($value)
    {
    	$this->values['notify_url'] = $value;
    }
    /**
    * 获取接收微信支付异步通知回调地址的值
    * @return 值
    **/
    public function GetNotify_url()
    {
    	return $this->values['notify_url'];
    }
    /**
    * 判断接收微信支付异步通知回调地址是否存在
    * @return true 或 false
    **/
    public function IsNotify_urlSet()
    {
    	return array_key_exists('notify_url', $this->values);
    }


    /**
    * 设置取值如下：JSAPI，NATIVE，APP，详细说明见参数规定
    * @param string $value 
    **/
    public function SetTrade_type($value)
    {
    	$this->values['trade_type'] = $value;
    }
    /**
    * 获取取值如下：JSAPI，NATIVE，APP，详细说明见参数规定的值
    * @return 值
    **/
    public function GetTrade_type()
    {
    	return $this->values['trade_type'];
    }
    /**
    * 判断取值如下：JSAPI，NATIVE，APP，详细说明见参数规定是否存在
    * @return true 或 false
    **/
    public function IsTrade_typeSet()
    {
    	return array_key_exists('trade_type', $this->values);
    }
    /**
    * 设置APP和网页支付提交用户端ip，Native支付填调用微信支付API的机器IP。
    * @param string $value 
    **/
    public function SetSpbill_create_ip($value)
    {
    	$this->values['spbill_create_ip'] = $value;
    }  
    /**
    * 设置trade_type=NATIVE，此参数必传。此id为二维码中包含的商品ID，商户自行定义。
    * @param string $value 
    **/
    public function SetProduct_id($value)
    {
    	$this->values['product_id'] = $value;
    }
    /**
    * 获取trade_type=NATIVE，此参数必传。此id为二维码中包含的商品ID，商户自行定义。的值
    * @return 值
    **/
    public function GetProduct_id()
    {
    	return $this->values['product_id'];
    }
    /**
    * 判断trade_type=NATIVE，此参数必传。此id为二维码中包含的商品ID，商户自行定义。是否存在
    * @return true 或 false
    **/
    public function IsProduct_idSet()
    {
    	return array_key_exists('product_id', $this->values);
    }


    /**
    * 设置trade_type=JSAPI，此参数必传，用户在商户appid下的唯一标识。下单前需要调用【网页授权获取用户信息】接口获取到用户的Openid。 
    * @param string $value 
    **/
    public function SetOpenid($value)
    {
    	$this->values['openid'] = $value;
    }
    /**
    * 获取trade_type=JSAPI，此参数必传，用户在商户appid下的唯一标识。下单前需要调用【网页授权获取用户信息】接口获取到用户的Openid。 的值
    * @return 值
    **/
    public function GetOpenid()
    {
    	return $this->values['openid'];
    }
    /**
    * 判断trade_type=JSAPI，此参数必传，用户在商户appid下的唯一标识。下单前需要调用【网页授权获取用户信息】接口获取到用户的Openid。 是否存在
    * @return true 或 false
    **/
    public function IsOpenidSet()
    {
		return array_key_exists('openid', $this->values);
	}
}
