<?php
$this->breadcrumbs = array(
    Yii::t("navigations", "Payment") => array('/child/payment/summary'),
    Yii::t("navigations", "Payment History"),
);
$colorbox = $this->widget('application.extensions.colorbox.JColorBox');
$colorbox->addInstance('.colorbox', array('iframe' => false, 'width' => '800', 'height' => '80%'));
$colorbox->addInstance(
        '.colorboxForPrint', array(
    'iframe' => false,
    'width' => '720',
    'height' => '80%',
    'title' => '<span class="btn001">' . CHtml::link('<span>' . Yii::t("payment", "Print") . '</span>', "javascript:void();", array('onclick' => "startPrint();return false;")) . '</span>'
));

echo CHtml::link("", "#", array(
    "id" => "common_colorbox",
    "class" => "colorboxForPrint",
    "style" => "display:none",
));
?>

<h1><label><?php echo Yii::t("navigations", 'Payment History'); ?></label></h1>
<script type="text/javascript">

    function viewPrintReceipt(invoice_id){
        if(invoice_id == undefined){
            invoice_id = $("#paid_invoices .items input[type=checkbox]").not('#paid_invoice_id_all').serialize();
        }else{
            invoice_id = "paid_invoice_id="+invoice_id;
        }
        if(invoice_id != ""){
            var base_url = "<?php echo $this->createUrl("payment/printInvoice", array('for' => 'receipt')); ?>";
            $("#common_colorbox").attr('href',base_url+"&"+invoice_id);
            $("#common_colorbox").click();
            $("#noticeDialog").dialog("close");
		
        }else{
            $("#noticeDialog").html("<div><?php echo Yii::t("payment", 'Please select at least one invoice.'); ?></div>");
            $("#noticeDialog").dialog("open");
            setTimeout('$("#noticeDialog").dialog("close");',2000);
		
        }
	
    }

    //开始打印
    function startPrint(){
        if ($.browser.msie) {
            $("#print_info").printArea({
                mode: 'popup', popClose: true
            });
        }
        else {
            $("#print_info").printArea();
        }
    }


</script>
<?php
$printSrc = Yii::app()->theme->baseUrl . '/images/icons/printer32.png';
$printReceiptLink = CHtml::openTag("a", array(
            "title" => Yii::t("payment", "Print"),
            "href" => "javascript:void();",
            "onclick" => 'viewPrintReceipt();return false;',
        ));
$printReceiptLink.= CHtml::image($printSrc, Yii::t("payment", "Print"));
$printReceiptLink.= CHtml::closeTag("a");


$this->widget('zii.widgets.jui.CJuiDialog', array(
    'id' => 'noticeDialog',
    'options' => array(
        // 提示
        'title' => Yii::t("payment", 'Tips'),
        'autoOpen' => false,
    ),
));


$this->widget('zii.widgets.grid.CGridView', array(
    'cssFile' => Yii::app()->theme->baseUrl . '/widgets/gridview/styles.css',
    'id' => 'paid_invoices',
    'dataProvider' => $payList,
    'enableSorting' => false,
    'pager' => array(
        'class' => 'CLinkPager',
        'cssFile' => Yii::app()->theme->baseUrl . '/css/pager.css',
    ),
    'columns' => array(
        array(
            'class' => 'CCheckBoxColumn',
            'id' => "paid_invoice_id",
            'value' => '$data->invoice_id',
            'selectableRows' => 2,
            'checkBoxHtmlOptions' => array(
                'name' => 'paid_invoice_id[]',
            ),
            'footer' => (empty($payList->data)) ? null : $printReceiptLink,
        ),
        array(
            'name' => 'title',
            'type' => 'raw',
            'htmlOptions' => array("class" => "alignLeft"),
            'value' => 'CHtml::link($data->title,"javascript:void();",array("onclick"=>"viewPrintReceipt($data->invoice_id);return false;"))',
        ),
//				array(
//        			'name'   =>'payment_type',
//					'value'  => 'Invoice::feeType($data->payment_type)',
//				),
        array(
            'name' => 'amount',
            'value' => 'Yii::app()->numberFormatter->format("#,##0.00",$data->amount)',
        ),
        array(
            'name' => 'timestamp',
            'value' => 'Mims::formatDateTime($data->last_paid_timestamp, "medium", "short")',
        ),
    ),
    'ajaxUpdate' => false,
    'template' => "{summary}{pager}{items}{summary}{pager}"
));
?>
