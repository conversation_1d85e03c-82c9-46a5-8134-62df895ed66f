<style>
    .unsign img {
        -webkit-filter: grayscale(100%);
        -moz-filter: grayscale(100%);
        -o-filter: grayscale(100%);
        -ms-filter: grayscale(100%);
        filter: grayscale(100%);
    }

    .collapsed_Parents {
        position: relative;
        height: 40px;
    }

    .collapsed_Parents > p {
        position: absolute;
        width: 100%;
        font-size: 16px;
        left: 0;
        top: 0;
        margin: 0;
        line-height: 40px;
        height: 40px;
        padding: 0 10px;
    }

    .student_Leave {
        position: absolute;
        left: 0;
        bottom: 0;
        padding: 2px;
        border-radius: 3px;
        color: #c7254e;
        background: #f9f2f4;
    }

    .signed, .unsign {
        padding: 0 10px;
    }

    @media screen and (max-width: 768px) {
        .have_Checked {
            padding: 10px 15px;
        }
    }

    .thebtn{
        width: 120%;
        overflow: hidden;
    }
    .student_num{
        padding:10px 16px;
        border-radius: 4px;
        width: 140px;
    }
    .completed{
        background: #F0F5FB;
    }
    .present{
        background: #F2F9F2;
    }
    .colorLeave{
        color:#E68834
    }
    .leave{
        background:#FDF8F1
    }
    .tardy{
        padding:1px 4px;
        color:#fff;
        border-radius: 2px;
        font-size:12px;
        margin-left:12px;
        background: #D9534F;
    }
    .font500{
        font-weight:500
    }
    .ml1{
        margin-left:1px
    }
    .form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control{
        background-color: #fff;
    }
</style>
<?php
$leave = 0;
$tardy = 0;
$online = 0;
$classVacations = array();
foreach ($vacations as $_cid => $v) {
    if ($v['type'] == ChildVacation::VACATION_LATE || $v['type'] == ChildVacation::VACATION_LEAVE) {
        $tardy++;
    }elseif($v['type'] == ChildVacation::VACATION_SICK_LEAVE || $v['type'] == ChildVacation::VACATION_AFFAIR_LEAVE){
        $leave++;
        $classVacations[$v['classid']][] = $_cid;
    }else if($v['type'] == ChildVacation::VACATION_ONLINE_PRESENT ){
        $online ++;
        $classOnline[$v['classid']][] = $_cid;
    }
}
$data = ChildVacation::getMonth();
?>
<script>var totalNumber = 0;</script>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('default/index')); ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Routines'), array('//mcampus/default/index')) ?></li>
        <?php if(Yii::app()->params['siteFlag'] == 'daystar'){?>
            <li><?php echo CHtml::link(Yii::t('site', 'Student Attendance Management'), array('//mcampus/childsign/index2')) ?></li>
        <?php }else{?>
            <li><?php echo CHtml::link(Yii::t('site', 'Student Attendance Management'), array('//mcampus/childsign/index')) ?></li>
        <?php }?>
        <li class="active"><?php echo Yii::t('campus', 'Student Attendance'); ?></li>
    </ol>
    <div class="row">
        <div class="col-md-2">
            <div class="list-group" id="classroom-status-list">
                <a href="<?php echo $this->createUrl('index'); ?>"
                   class="list-group-item status-filter active"><?php echo Yii::t('campus', 'Student Attendance'); ?></a>
                <a href="<?php echo $this->createUrl('vacation'); ?>"
                   class="list-group-item status-filter"><?php echo Yii::t('campus', 'Student leave list'); ?></a>
                <a href="<?php echo $this->createUrl('comeLate'); ?>"
                   class="list-group-item status-filter"><?php echo Yii::t('campus', 'Late arrival/early release'); ?></a><!--<a href="<php /*echo $this->createUrl('check'); */?>"
                   class="list-group-item status-filter">php /*echo Yii::t('campus', '审核列表'); */?></a>-->
                <a href="<?php echo $this->createUrl('track'); ?>"
                   class="list-group-item status-filter"><?php echo Yii::t('campus', 'Sick leave follow-up'); ?></a>
                <a href="<?php echo $this->createUrl('signlog'); ?>" class="list-group-item status-filter"><?php echo Yii::t('campus', 'Operations log'); ?></a>
                <a href="<?php echo $this->createUrl('report'); ?>"
                   class="list-group-item status-filter"><?php echo Yii::t('campus', 'Monthly report'); ?></a>
            </div>
        </div>
        <div class="col-md-10">
            <div class="mb10 row">
                <div class="col-md-2">
                    <input id="datepicker" class="form-control" name="date"
                           value="<?php echo $_GET['date'] ? $_GET['date'] : date('Y-m-d', time()); ?>" readonly style="cursor: pointer">
                </div>
                <div class="col-md-2">
                    <form id="autosign" method="post">
                        <input id="signid" class="form-control" name="signid"
                               placeholder="<?php echo Yii::t('campus', 'Click here to scan') ?>" autocomplete="off">
                    </form>
                </div>
                <!-- <div class="col-md-4 input-sm  have_Checked">
                    <?php echo Yii::t('campus', 'Completed: ') ?><span class="label label-default"> <span
                                id='total-num'><?php echo count($signChilds)+$leave+$online; ?></span>/<?php echo count($childData) ?></span>
                    <?php echo Yii::t('campus', 'Signed in: ') ?><span class="label label-default"> <span
                                id='signed-num'><?php echo count($signChilds)+$online; ?></span></span>
                    <?php echo Yii::t("campus", 'On leave') ?><?php echo Yii::t("global", ': ') ?>
                    <span class="label label-default"> <span id='leave-num'><?php echo $leave; ?></span></span>
                    <?php echo Yii::t("campus", 'On tardy') ?><?php echo Yii::t("global", ': ') ?>
                    <span class="label label-default"> <span id='tardy-num'><?php echo $tardy; ?></span></span>
                </div> -->
            </div>
            <div class="flex mb15 mt20">
                <div class='mr16 student_num completed'>
                    <div class='font12 color3'><?php echo Yii::t('withdrawal', 'Completed'); ?></div>
                    <div class='font14 color3 mt4 ml1 font500' id="total-num"><?php echo count($signChilds)+$leave+$online; ?></span>/<?php echo count($childData) ?></div>
                </div>
                <div class='mr16 student_num present'> 
                    <div class='font12 color3'><?php echo Yii::t('attends', 'Present'); ?></div>    
                    <div class='mt4 ml1'>
                        <span class="iconColor font14 font500" id='signed-num'><?php echo count($signChilds)+$online; ?></span>
                        <span class='tardy' id='tardy-num'><?php echo Yii::t('attends', 'Tardy'); ?>：<?php echo $tardy; ?></span>
                    </div>
                </div>
                <div class='mr16 student_num leave'>
                    <div class='font12 color3'><?php echo Yii::t('attends', 'Leave'); ?></div>
                    <div class='font14 colorLeave mt4 font500 ml1'  id='leave-num'><?php echo $leave; ?></div>
                </div>
            </div>
            <div>
                <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
                    <?php foreach ($classes as $class): ?>
                        <?php
                        if (count($classnum[$class->classid]) == 0) {
                            continue;
                        }
                        ?>
                        <div class="panel panel-default">
                            <div class="panel-heading collapsed_Parents" role="tab"
                                    id="<?php echo $class->classid; ?>">
                                <p class="collapsed" data-toggle="collapse" style="cursor: pointer"
                                    data-parent="#accordion" href="#col-<?php echo $class->classid ?>"
                                    aria-expanded="false"
                                    aria-controls="col-<?php echo $class->classid ?>"><?php echo $class->title; ?>
                                    <?php
                                    //已完成=正常出勤+请假+线上出勤
                                    $_counted = count($signData[$class->classid])+count($classVacations[$class->classid])+count($classOnline[$class->classid]);
                                    $_total = count($classnum[$class->classid]);
                                    $className = ($_counted == $_total) ? 'label-default' : 'label-danger';
                                    ?>
                                    <span class="label <?php echo $className;?>"><?php echo '<span id="' . $class->classid . '-num">' . $_counted . '</span>/' . $_total; ?></span>
                                </p>
                            </div>
                            <div id="col-<?php echo $class->classid ?>"
                                    class="panel-collapse collapse children_Class_SignIn " role="tabpanel"
                                    aria-labelledby="<?php echo $class->classid; ?>">
                                <div class="panel-body">
                                    <?php if(isset($classList) && in_array($class->classid, $classList)){ ?>
                                        <div class="alert alert-danger" role="alert">
                                            <span class="glyphicon glyphicon-exclamation-sign" aria-hidden="true"></span>
                                            <span class="sr-only">Error:</span>
                                            <?php echo Yii::t('campus', 'MS attendance are taken by class teachers, data synced to this page automatically. (based on 1st period class)')?>
                                        </div>
                                    <?php }else{ ?>
                                        <button class="btn btn-primary"
                                                onclick="signAll(<?php echo $class->classid ?>)"><?php echo Yii::t('campus', 'Mark All Present')?>
                                        </button>
                                    <?php } ?>


                                    <div class="row unsign" style="border-left:5px solid #f0ad4e">
                                        <h3 class=""><?php echo Yii::t('campus', 'Absent')?></h3>
                                        <?php foreach ($classnum[$class->classid] as $child): ?>
                                            <?php if (!in_array($child['id'], $signChildids) && !isset($vacations[$child['id']])): ?>
                                                <div class="col-lg-1 col-md-2 col-sm-3 col-xs-6 text-center">
                                                    <label id="<?php echo $child['id']; ?>" class="text-center"
                                                            style="height: 140px !important;">
                                                        <a style="display: inline-block;position: relative"
                                                            href="javascript:"
                                                            onclick="sign(<?php echo $child['id']; ?>);">
                                                            <img class="img-responsive img-rounded"
                                                                    src="<?php echo CommonUtils::childPhotoUrl($child['photo'], 'small'); ?>">
                                                        </a>

                                                        <div class="dropdown dropup">
                                                            <button class="btn btn-default dropdown-toggle thebtn" type="button" id="dropdownMenu-<?php echo $child['id']?>" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" data-studentname="<?php echo $child['name'];?>">
                                                                <span class="caret"></span>
                                                                <?php echo $child['name'];?>
                                                            </button>

                                                            <ul class="dropdown-menu" aria-labelledby="dropdownMenu-<?php echo $child['id']?>">
                                                                <li><a href="javascript:vacationAdd(<?php echo $child['id']?>, 10);"><?php echo Yii::t('campus', 'Sick leave of today')?></a></li>
                                                                <li><a href="javascript:vacationAdd(<?php echo $child['id']?>, 20);"><?php echo Yii::t('campus', 'Other leave of today')?></a></li>
                                                                <li><a href="javascript:vacationAdd(<?php echo $child['id']?>, 40)"><?php echo Yii::t('campus', 'Late for today')?></a></li>
                                                                <li role="separator" class="divider"></li>
                                                                <li><a href="javascript:sign(<?php echo $child['id']?>);"><?php echo Yii::t('campus', 'Tag present for today')?></a></li>
                                                            </ul>
                                                        </div>

                                                    </label>
                                                </div>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                        <?php foreach ($vacations as $k=>$v) : ?>
                                            <?php if(!in_array($k, $signChildids) && $v['classid'] == $class->classid): ?>
                                                <div class="col-lg-1 col-md-2 col-sm-3 col-xs-6 text-center">
                                                    <label id="<?php echo $k; ?>" class="text-center <?php echo  ($v['type'] == 40) ? 'tardyStatus' : '' ; ?>"
                                                            style="height: 140px !important;">
                                                        <a style="display: inline-block;position: relative"
                                                            href="javascript:"
                                                            <?php if($v['type'] == ChildVacation::VACATION_LATE || $v['type'] == ChildVacation::VACATION_LEAVE){?>
                                                                onclick="sign(<?php echo $k;?>)"
                                                            <?php }else{  ?>
                                                                onclick="leave(<?php echo $k;?>,'<?php echo $v['vacation_reason'];?>')"
                                                            <?php } ?>
                                                        >
                                                            <img class="img-responsive img-rounded"
                                                                    src="<?php echo CommonUtils::childPhotoUrl($v['photo'], 'small'); ?>">
                                                            <span class="student_Leave"><?php echo $v['title'] ?></span>
                                                        </a>
                                                        <div class="dropdown dropup">
                                                            <button class="btn btn-default dropdown-toggle thebtn" type="button" id="dropdownMenu-<?php echo $k?>" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" data-studentname="<?php echo $v['name'];?>">
                                                                <?php if($v['type'] == ChildVacation::VACATION_LATE || $v['type'] == ChildVacation::VACATION_LEAVE):?>
                                                                    <span class="caret"></span>
                                                                <?php endif; ?>
                                                                <?php echo $v['name']; ?>
                                                            </button>

                                                            <?php if($v['type'] == ChildVacation::VACATION_LATE || $v['type'] == ChildVacation::VACATION_LEAVE):?>
                                                                <ul class="dropdown-menu" aria-labelledby="dropdownMenu-<?php echo $k?>">
                                                                    <li><a href="javascript:vacationAdd(<?php echo $k?>, 10);"><?php echo Yii::t('campus', 'Sick leave of today')?></a></li>
                                                                    <li><a href="javascript:vacationAdd(<?php echo $k?>, 20);"><?php echo Yii::t('campus', 'Other leave of today')?></a></li>
                                                                    <li role="separator" class="divider"></li>
                                                                    <li><a href="javascript:sign(<?php echo $k?>);"><?php echo Yii::t('campus', 'Tag present for today')?></a></li>
                                                                </ul>
                                                            <?php endif; ?>
                                                        </div>

                                                    </label>
                                                </div>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </div>
                                    <div class="row signed" style="border-left:5px solid #008000">
                                        <h3 class="text-success"><?php echo Yii::t('campus', 'Present')?> <small><?php echo Yii::t('campus', 'Click to undo tag present');?></small></h3>
                                        <?php if (!empty($signData)){?>
                                            <?php foreach ($signData[$class->classid] as $sign) : ?>
                                                <div class="col-lg-1 col-md-2 col-sm-3 col-xs-6 text-center thisChild">
                                                    <label id="<?php echo $sign->childid; ?>" class="text-center"
                                                            style="height: 160px !important;">
                                                        <a style="display: inline-block" href="javascript:"  id="delchild_<?php echo $sign->id; ?>"
                                                            onclick="delchild(<?php echo $sign->id . ',' . $sign->classid . ',this' ?>)"  data-studentname="<?php echo $sign->child->getChildName();?>">
                                                            <img class="img-responsive img-rounded"
                                                                    src="<?php echo CommonUtils::childPhotoUrl($sign->child->photo, 'small'); ?>">
                                                        </a>
                                                        <p class="text-center text-success"><?php echo $sign->child->getChildName(); ?></p>
                                                        <code class="J_action" title="<?php echo date('y-m-d h:i', $sign->update_timestamp)?>"><?php echo $sign->actionStaff->uname; ?></code>
                                                    </label>
                                                </div>
                                            <?php endforeach; ?>
                                        <?php }?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>


    <!-- 显示底部学校选择栏 -->
    <?php $this->renderPartial('//layouts/common/branchSelectBottom'); ?>

    <?php $model = new ChildVacation();?>

    <div id="leaveAdd" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <form action="<?php echo $this->createUrl('vacationadd')?>" method="post" class="J_ajaxForm form-horizontal">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title"><?php echo Yii::t('campus', 'Add leave record'); ?></h4>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label class="col-xs-3 control-label"><?php echo Yii::t('labels','Student') ?></label>
                            <div class="col-xs-9">
                                <?php echo CHtml::textField('childLabel', '', array('class'=>'form-control', 'id'=>'childLabel', 'disabled'=>'disabled'))?>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-3 control-label"><?php echo Yii::t('labels','Date') ?></label>
                            <div class="col-xs-9">
                                <?php echo CHtml::textField('dateLabel', '', array('class'=>'form-control', 'id'=>'dateLabel', 'disabled'=>'disabled'))?>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-3 control-label"><?php echo Yii::t('labels','Memo') ?></label>
                            <div class="col-xs-9">
                                <?php echo CHtml::activeTextArea($model, 'vacation_reason', array('class'=>'form-control'));?>
                            </div>
                        </div>
                        <?php if(0&&$this->branchId == 'BJ_DS'): ?>
                            <div class="form-group">
                                <label class="col-xs-3 control-label"><?php echo Yii::t('labels','同步到课表') ?></label>
                                <div class="col-xs-9">
                                    <?php echo CHtml::CheckBox('sync', '', array('class'=>'', 'id'=>'sync'))?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="modal-footer">
                        <?php echo CHtml::activeHiddenField($model, 'child_id')?>
                        <?php echo CHtml::activeHiddenField($model, 'type')?>
                        <?php echo CHtml::activeHiddenField($model, 'vacation_time_start')?>
                        <?php echo CHtml::activeHiddenField($model, 'vacation_time_end')?>
                        <?php echo CHtml::hiddenField('type_id', 1)?>
                        <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit');?></button>
                        <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel');?></button>
                    </div>
                </form>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->

    <div id="tardyDefine" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <form action="<?php echo $this->createUrl('autoSign')?>" method="post" class="J_ajaxForm form-horizontal">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title"><?php echo Yii::t('campus', 'Add leave record'); ?></h4>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label class="col-xs-3 control-label"><?php echo Yii::t('safety', 'Student Name') ?></label>
                            <div class="col-xs-9">
                                <?php echo CHtml::textField('childName', '', array('class'=>'form-control', 'id'=>'childName', 'disabled'=>'disabled'))?>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-3 control-label"><?php echo Yii::t('campus','EsteEstimated Time of Arrival') ?></label>
                            <div class="col-xs-9">
                                <?php echo CHtml::textField('dateLabelTime', '', array('class'=>'form-control', 'id'=>'dateLabelTime', 'disabled'=>'disabled'))?>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-3 control-label"><?php echo Yii::t('labels','Type') ?></label>
                            <div class="col-xs-9">
                                <?php echo CHtml::textField('type', '', array('class'=>'form-control', 'id'=>'type', 'disabled'=>'disabled'))?>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-3 control-label"><?php echo Yii::t('campus','Actual Time of Arrival') ?> <span class="required">*</span></label>
                            <div class="col-xs-3">
                                <?php echo CHtml::dropDownList('timeEst', "", $data['hour'], array('class' => "form-control mr10", 'empty' => '时'));?>
                            </div>
                            <div class="col-xs-3">
                                <?php echo CHtml::dropDownList('minuteEst', "", $data['minute'], array('class' => "form-control mr10", 'empty' => '分'));?>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <?php echo CHtml::HiddenField("vacation_id", "")?>
                        <?php echo CHtml::HiddenField("date", "")?>
                        <?php echo CHtml::HiddenField("signid", "")?>
                        <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit');?></button>
                        <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel');?></button>
                    </div>
                </form>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->

    <div id="tardyAdd" class="modal fade" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <form action="<?php echo $this->createUrl('vacationadd')?>" method="post" class="J_ajaxForm form-horizontal">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title"><?php echo Yii::t('campus', 'Add late record'); ?></h4>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label class="col-xs-3 control-label"><?php echo Yii::t('labels','Student') ?></label>
                            <div class="col-xs-9">
                                <?php echo CHtml::textField('childLabel', '', array('class'=>'form-control', 'id'=>'childLabel', 'disabled'=>'disabled'))?>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-3 control-label"><?php echo Yii::t('labels','Date') ?></label>
                            <div class="col-xs-9">
                                <?php echo CHtml::textField('dateLabel', '', array('class'=>'form-control', 'id'=>'dateLabel', 'disabled'=>'disabled'))?>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-3 control-label"></label>
                            <div class="col-xs-9">
                                <label class="radio-inline">
                                    <input type="radio" name="arrival" id="inlineRadio1" value="out" onclick="arrivals(this)" checked> <?php echo Yii::t('campus','Not arrived yet') ?>
                                </label>
                                <label class="radio-inline">
                                    <input type="radio" name="arrival" id="inlineRadio2" value="on" onclick="arrivals(this)"> <?php echo Yii::t('campus','Already Arrived') ?>
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-3 control-label"></label>
                            <div class="col-xs-3 text-right">
                                <label id="expected_arrivals" title="<?php echo Yii::t('campus','EsteEstimated Time of Arrival'); ?>"><?php echo Yii::t('campus','ETA') ?> <span class="glyphicon glyphicon-question-sign"></span></label>
                                <label id="actual_arrivals" title="<?php echo Yii::t('campus','Actual Time of Arrival'); ?>" style="display: none;"><?php echo Yii::t('campus','ATA') ?> <span class="glyphicon glyphicon-question-sign"></span> <span class="required">*</span></label>
                            </div>
                            <div class="col-xs-3">
                                <?php echo CHtml::dropDownList('timeEst', "", $data['hour'], array('class' => "form-control mr10", 'empty' => Yii::t('campus','hour')));?>
                            </div>
                            <div class="col-xs-3">
                                <?php echo CHtml::dropDownList('minuteEst', "", $data['minute'], array('class' => "form-control mr10", 'empty' => Yii::t('campus','minute')));?>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-xs-3 control-label"><?php echo Yii::t('labels','Memo') ?></label>
                            <div class="col-xs-9">
                                <?php echo CHtml::activeTextArea($model, 'vacation_reason_late', array('class'=>'form-control'));?>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <?php echo CHtml::activeHiddenField($model, 'child_id_late')?>
                        <?php echo CHtml::activeHiddenField($model, 'vacation_time_start_late')?>
                        <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit');?></button>
                        <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel');?></button>
                    </div>
                </form>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
    <div  id="leaveNotes" class="modal fade" tabindex="-1" role="dialog" data-dismiss="modal"  aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title"><?php echo Yii::t('campus', '查看请假备注'); ?></h4>
                </div>
                <div class="modal-body">
                    <p id='notes'></p>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
    <!-- script代码 -->
    <script>

        $('#total-number').text(totalNumber);
        var count = <?php echo count($classes);?>;
        if (count == 1) {
            $('#col-<?php echo $classes[0]->classid; ?>').collapse('show');
        }

        $('#datepicker').datepicker({
            dateFormat: 'yy-mm-dd',
            maxDate: '<?php echo date('Y-m-d', time()); ?>'
        });

        $('#datepicker').bind('change', function (e) {
            window.location.href = '<?php echo $this->createUrl('index'); ?>&date=' + $(this).val();
        });

        $('#autosign').submit(function (e) {
            e.preventDefault();
            sign($('#signid').val());
        });

        function signNum(classid, operate) {
            var calssid = classid || undefined;
            var operate = operate || 'add';
            var totalnum = $('#total-num');
            var signednum = $('#signed-num');
            var classnum = $('#' + classid + '-num');

            if (operate == 'add') {
                totalnum.text(parseInt(totalnum.text()) + 1);
                signednum.text(parseInt(signednum.text()) + 1);
                classnum.text(parseInt(classnum.text()) + 1);
            } else {
                totalnum.text(parseInt(totalnum.text()) - 1);
                signednum.text(parseInt(signednum.text()) - 1);
                classnum.text(parseInt(classnum.text()) - 1);
            }
        }

        function delchild(id, classid, span) {
            var date = $('#datepicker').val();
            document.getElementById('delchild_'+id).onclick=null
            $.ajax({
                type: 'post',
                dataType: 'json',
                url: '<?php echo $this->createUrl('delSign'); ?>',
                data: {id: id,date: date},
                success: function (response) {
                    if (response.state == 'success') {
//                        var html = $(span).parents(".thisChild");
//                        $(span).attr('onclick', '').unbind('click');
//                        $(span).click(function () {
//                            sign($(span).parent().attr('id'));
//                        });
//                        $(span).next().removeClass('text-success').addClass('text-info');
//                        $(span).nextAll('code.J_action').remove();
                        var spanObj = $(span);
                        var childid = spanObj.parent().attr('id');
                        var childname = spanObj.data('studentname');
                        var childpic = spanObj.find('img').attr('src');

                        spanObj.parents(".thisChild").remove();

                        if(response.data.type === '40'){
                            var html = '<div class="col-lg-1 col-md-2 col-sm-3 col-xs-6 text-center"><label id="'+childid+'"  class="text-center tardyStatus" style="height: 140px !important;"> <a style="display: inline-block;position: relative" href="javascript:;" onclick="sign('+childid+');"> <img class="img-responsive img-rounded" src="'+childpic+'"><span class="student_Leave">迟到</span></a><div class="dropdown dropup"> <button class="btn btn-default dropdown-toggle thebtn" type="button" id="dropdownMenu-'+childid+'" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" data-studentname="'+childname+'"> <span class="caret"></span> '+childname+'</button> <ul class="dropdown-menu" aria-labelledby="dropdownMenu-'+childid+'"> <li><a href="javascript:vacationAdd('+childid+', 10);">标记本日病假</a></li> <li><a href="javascript:vacationAdd('+childid+', 20);">标记本日事假</a></li> <li><a href="javascript:vacationAdd('+childid+', 40)">标记本日迟到</a></li> <li role="separator" class="divider"></li> <li><a href="javascript:sign('+childid+');">标记本日已出勤</a></li> </ul> </div> </label> </div>';
                        }else{
                            var html = '<div class="col-lg-1 col-md-2 col-sm-3 col-xs-6 text-center"><label id="'+childid+'" class="text-center" style="height: 140px !important;"> <a style="display: inline-block;position: relative" href="javascript:;" onclick="sign('+childid+');"> <img class="img-responsive img-rounded" src="'+childpic+'"></a> <div class="dropdown dropup"> <button class="btn btn-default dropdown-toggle thebtn" type="button" id="dropdownMenu-'+childid+'" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" data-studentname="'+childname+'"> <span class="caret"></span> '+childname+'</button> <ul class="dropdown-menu" aria-labelledby="dropdownMenu-'+childid+'"> <li><a href="javascript:vacationAdd('+childid+', 10);">标记本日病假</a></li> <li><a href="javascript:vacationAdd('+childid+', 20);">标记本日事假</a></li> <li><a href="javascript:vacationAdd('+childid+', 40)">标记本日迟到</a></li> <li role="separator" class="divider"></li> <li><a href="javascript:sign('+childid+');">标记本日已出勤</a></li> </ul> </div> </label> </div>';
                        }
                        $('#col-' + classid + ' .unsign').append(html);

                        signNum(classid, true)
                    } else{
                        resultTip({msg: response.message, error: 'warning'});
                    }
                }
            });
        }
        function leave(childid,data){
            $("#leaveNotes").modal('show');
            $('#notes').html(data)
        }
        function sign(childid) {
            $('#autosign input').val('');
            childid = Number(childid);
            var date = $('#datepicker').val();
            if ( $("#" +childid+ "").hasClass("tardyStatus") ){
                if ($('.unsign #' + childid).length > 0) {
                    $('#tardyDefine #timeEst').val("");
                    $('#tardyDefine #minuteEst').val("");
                    $.ajax({
                        type: 'post',
                        dataType: 'json',
                        url: '<?php echo $this->createUrl('showSign'); ?>',
                        data: {signid: childid, date: date},
                        success: function (response) {
                            if (response.state == 'success') {
                                $('#tardyDefine #childName').val(response.data.childName);
                                $('#tardyDefine #dateLabelTime').val( response.data.time );
                                $('#tardyDefine #type').val( response.data.type );
                                $('#tardyDefine #vacation_id').val( response.data.id );
                                $('#tardyDefine #date').val( date );
                                $('#tardyDefine #signid').val( response.data.childid );

                                $("#tardyDefine").modal({backdrop: 'static'});
                            }
                        }
                    });
                }
            }else{
                if ($('.unsign #' + childid).length > 0) {
                    $.ajax({
                        type: 'post',
                        dataType: 'json',
                        url: '<?php echo $this->createUrl('autoSign'); ?>',
                        data: {signid: childid, date: date},
                        success: function (response) {
                            if (response.state == 'success') {
                                var data = response.data;
                                cbSigned(data);
                            } else{
                                resultTip({msg: response.message, error: 'warning'});
                            }
                        }
                    });
                }

            }
        }

        function signAll(classid) {
            if (confirm('<?php echo Yii::t('campus', 'Please confirm that the students of this class have all arrived in school.')?>')) {
                var childData = [];
                $('#col-' + classid + ' .unsign label').each(function (index, obj) {
                    var childid = $(obj).attr('id');
                    if (childid > 0) {
                        childData[index] = childid;
                    }
                });

                if (childData.length > 0) {
                    var date = $('#datepicker').val();
                    $.ajax({
                        type: 'post',
                        dataType: 'json',
                        url: '<?php echo $this->createUrl('signAll'); ?>',
                        data: {childData: childData, date: date}
                    }).done(function (data) {
                        if (data.state == 'fail') {
                            resultTip({msg: data.message, error: 'warning'});
                        } else{
                            var items = data.data.items;
                            for (var childid in items) {
                                cbSigned(items[childid]);
                            }
                        }
                    });
                }
            }
        }

        function cbSigned(data) {
            $('#col-' + data.classid).collapse('show');
            var html = '<div class="col-lg-1 col-md-2 col-sm-3 col-xs-6 text-center thisChild"><label id="' + data.childid + '" style="height: 160px !important;"><a href="javascript:;" style="display: inline-block" id="delchild_'+data.id +'" onclick="delchild(' + data.id + ',' + data.classid + ',this)" data-studentname="' + data.name + '"><img class="img-responsive img-rounded" src="' + data.photo + '"></a><p class="text-center text-success">' + data.name + '</p><code title="'+data.time+'" class="J_action">' + data.actionStaff + '</code></label></div>';
            $('#' + data.childid).parent().remove();
            $('#col-' + data.classid + ' .signed').append(html);
            signNum(data.classid);
        }

        var lType = 0;
        function vacationAdd(childid, type)
        {
            var date = '<?php echo isset($_GET['date']) ? $_GET['date'] : date('Y-m-d')?>';
            lType = type;
            if(type == 40){
                $('#tardyAdd #childLabel').val( $('#dropdownMenu-'+childid).data('studentname') );
                $('#tardyAdd #dateLabel').val( date );
                $('#tardyAdd #ChildVacation_child_id_late').val( childid );
                $('#tardyAdd #ChildVacation_vacation_time_start_late').val( date );
                $('#tardyAdd #ChildVacation_vacation_reason_late').val( '' );

                $("#tardyAdd").modal({backdrop: 'static'});
            }
            else{
                $('#leaveAdd #childLabel').val( $('#dropdownMenu-'+childid).data('studentname') );
                $('#leaveAdd #dateLabel').val( date );
                $('#leaveAdd #ChildVacation_child_id').val( childid );
                $('#leaveAdd #ChildVacation_type').val( type );
                $('#leaveAdd #ChildVacation_vacation_time_start').val( date );
                $('#leaveAdd #ChildVacation_vacation_time_end').val( date );
                $('#leaveAdd #ChildVacation_vacation_reason').val( '' );

                $("#leaveAdd").modal({backdrop: 'static'});
            }
        }
        function cbVacation(data)
        {
            var typeConf = <?php echo CJSON::encode(ChildVacation::typeConf())?>;
            $('#tardyAdd, #leaveAdd, #tardyDefine').modal('hide');
            $('#' + data.childid).addClass("tardyStatus");
            $('#' + data.childid + '> a')
            $('#' + data.childid + '> a').append('<span class="student_Leave">'+typeConf[lType]+'</span>');
            if(data.late != 40) {
                $('#' + data.childid + '> div ul').remove();
                $('#' + data.childid + '> div button span').removeClass();
                var notes = "'"+data.vacation_reason+"'";
                console.log(notes)
                $('#' + data.childid + ' a').attr('onclick','leave('+data.childid+','+notes+')');
            }
        }

        $('#leaveAdd, #tardyAdd').on('shown.bs.modal', function (e) {
            $(this).find('textarea').focus();
        });

        function cbSuccess(data) {
            $('#tardyAdd, #leaveAdd, #tardyDefine').modal('hide');
            cbSigned(data);
        }

        function arrivals(_this) {
            if($(_this).val() == 'on'){
                $('#actual_arrivals').show();
                $('#expected_arrivals').hide();
            }
            else {
                $('#actual_arrivals').hide();
                $('#expected_arrivals').show();
            }
        }
    </script>