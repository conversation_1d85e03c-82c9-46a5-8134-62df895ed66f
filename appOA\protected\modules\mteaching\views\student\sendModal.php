<!-- 后续处理 -->
<div class="modal fade"  id="followModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel"  aria-labelledby="wantSayModalLabel"  data-backdrop="static">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title"><?php echo Yii::t("referral", "Detail"); ?></h4>
        </div>
        <div class="modal-body p24">
            <div class='flex align-items' v-if='followItem.child && contentList.student_info_list[followItem.child.id]'>
                <div class="media flex1" >
                    <div class="media-left pull-left media-middle">
                        <a href="javascript:void(0)">
                            <img :src="contentList.student_info_list[followItem.child.id].avatar" data-holder-rendered="true" class="avatar">
                        </a>
                    </div>
                    <div class="media-body pt4 media-middle">
                        <div class="lineHeight20  text-primary">
                            <span class='font14 color3 nowrap'>{{contentList.student_info_list[followItem.child.id].name}} </span>
                        </div>
                        <div class="font12 color6 nowrap">{{contentList.student_info_list[followItem.child.id].className}}</div>
                    </div>
                </div>
                <div>
                    <button type="button" class="btn btn-primary" v-if='followType=="show"' @click='follow(followItem,"edit")'><?php echo Yii::t("ptc", "Change"); ?></button>
                </div>
            </div>
            <div class='mt24 color3 font14'><?php echo Yii::t("referral", "Suspention Schedule"); ?></div>
            <div class='flex mt12 align-items'>
                <span class='mr10 color6 font14'><?php echo Yii::t("labels", "Date"); ?></span>
                <el-date-picker
                    :disabled='followType=="edit"?false:true'
                    v-model="followData.date"
                    type="daterange"
                    placement="bottom-start"
                    range-separator="至"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    start-placeholder="<?php echo Yii::t("campus", "Start Date"); ?>"
                    end-placeholder="<?php echo Yii::t("campus", "End Date"); ?>">
                </el-date-picker>
            </div>
            <div class='flex mt12 align-items'>
                <span class='mr10 color6 font14'><?php echo Yii::t("labels", "Time"); ?></span>
                <el-time-picker
                    is-range
                    :disabled='followType=="edit"?false:true'
                    v-model="followData.time"
                    range-separator="至"
                    value-format="HH:mm"
                    format="HH:mm"
                    placement="bottom-start"
                    start-placeholder="<?php echo Yii::t("campus", "Start Date"); ?>"
                    end-placeholder="<?php echo Yii::t("campus", "End Date"); ?>">
                </el-time-picker>
            </div>
            <div class='mt24 color3 font14'><?php echo Yii::t("labels", "Memo"); ?></div>
            <div  class='mt8'><textarea class="form-control" placeholder="<?php echo Yii::t("labels", "Memo"); ?>" v-model='followData.remark' rows="3"  :disabled='followType=="edit"?false:true'></textarea></div>

        </div>
        <div class="modal-footer" v-if='followType=="edit"'>
            <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
            <button type="button" class="btn btn-primary" @click='saveFollow()'><?php echo Yii::t("global", "OK"); ?></button>
        </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
    
<div class="modal fade"  id="sendTeacherModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel"  aria-labelledby="wantSayModalLabel"  data-backdrop="static">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title"><?php echo Yii::t("referral", "Notify To"); ?></h4>
        </div>
        <div class="modal-body p24">
            <div class='flex font14' v-if='sendItem.child'>
                <div style='width:360px'>
                    <div :class='sendItem.result.identified==1?"sendImg":"greenSendImg"'>
                        <div class='text-center sendTitle'>
                            <div class='font16'><?php echo Yii::t("referral", "Notification of a behaviour handling"); ?></div>
                            <div>NO.{{sendItem.id}}</div>
                        </div>
                        <div class='p24'>
                            <div class="media" >
                                <div class="media-left pull-left media-middle">
                                    <a href="javascript:void(0)">
                                        <img :src="contentList.student_info_list[sendItem.child.id].avatar" data-holder-rendered="true" class="avatar">
                                    </a>
                                </div>
                                <div class="media-body pt4 media-middle">
                                    <div class="lineHeight20  text-primary">
                                        <span class='font14 color3 nowrap'>{{contentList.student_info_list[sendItem.child.id].name}} </span>
                                    </div>
                                    <div class="font12 color6 nowrap">{{contentList.student_info_list[sendItem.child.id].className}}</div>
                                </div>
                            </div>
                            <div class=' mt12'>
                                <span class=' color6 font14 '><?php echo Yii::t("referral", "Decision"); ?></span>
                                <div class='flex1 text-right ml24 color3'><span class="label " :class='sendItem.result.identified==1?"label-danger":"label-success"'>{{showTitle(sendItem.result.identified,'fact')}}</span></div>
                            </div>
                            <div class='flex mt12' v-if='sendItem.result.identified==1'>
                                <span class='color6 font14 '><?php echo Yii::t("referral", "Final Decision"); ?></span>
                                <div class='flex1 text-right ml24 color3'>
                                    <span class='ml10' v-for='(list,index) in sendItem.result.actions'>{{showTitle(list,'actions')}}</span>
                                    <div>
                                        <span class=''  v-if='sendItem.followup.date'>{{sendItem.followup.date[0]}} ~ {{sendItem.followup.date[1]}}</span>
                                    </div>
                                </div>
                            </div>
                            <div class='flex mt12'>
                                <span class='color6 font14 '><?php echo Yii::t("referral", "Contact parents"); ?></span>
                                <div class='flex1 text-right ml24  color3'><span class='mr10' v-for='(list,index) in sendItem.result.contact_parents'>{{showTitle(list,'contact_parents')}}</span></div>
                            </div>
                            <div class='flex mt12'>
                                <span class='color6 font14 '><?php echo Yii::t("referral", "Comment"); ?></span>
                                <div class='flex1 ml24 text-right  color3' v-html='htmlBr(sendItem.result.remark)'></div>
                            </div>
                            <div class='flex mt12 align-items '>
                                <span class='color6 font14 '><?php echo Yii::t("referral", "By"); ?></span>
                                <div class='flex1 ml24 color3'>
                                    <div class='flex align-items pull-right'>
                                        <img :src="contentList.staff_info_list[sendItem.updated_by].photoUrl" data-holder-rendered="true" class="avatar32">
                                        <span class='ml10'>{{contentList.staff_info_list[sendItem.updated_by].name}}</span>
                                    </div>
                                </div>
                            </div>
                            <div class='flex mt12'>
                                <span class='color6 font14 '><?php echo Yii::t("ptc", "Time"); ?></span>
                                <div class='flex1 text-right ml24 color3'>{{sendItem.updated_at}}</div>
                            </div>
                            <div class='text-center mt20'>
                                <div class="qrcodeShow" ></div>
                                <!-- <img src="data:image/png;base64, iVBORw0KGgoAAAANSUhEUgAAAHgAAAB4CAIAAAC2BqGFAAAACXBIWXMAAA7EAAAOxAGVKw4bAAADSElEQVR4nO2dQY7bMAxFO0VPk/ufJccJuiigRQXLJEW/ODPvrYLEkpwPmqZIyf56vV6/5Hp+v/sEfgoKDaHQEAoNodAQCg2h0BAKDaHQEH/WPz8ej53en8/n6TFjiPngyE+pQWsdRjj9p1o0hEJDnLiOQcQJDObLcP5mdDg+1HxI5OBF85nNf3qEFg2h0BBR1zGo3e7nqzjS4WL0Wjyz8GCL5jMp9/IPLRpCoSHSrqNGxD8sgoRaGBMZAkOLhlBoCMh1pG7TtYlGJKJIddiLFg2h0BBp11G76FL5h8jB8zeRECXllHrdixYNodAQUdcBRPg1j9EeSFz0T7VoCIWG+Lrzst2IW1jMU3rznJto0RAKDXHiOlK34M0kZFfzma4k7U6oo0VDKDRE1HWkruvUfT9VMF3QNdG4qFyrRUMoNEQ96qjNC2qpifZSS6TnyFTIqON2KDTESZr0upJErZa6WWpZ9Lz50ylaNIRCQ6SjjtrMZe5w8y5fC3UiPUdaFdCiIRQaIlphqeVL5+apFEeqVfsZLih4FS0aQqEh6mnS2ubTVNrhtgtEzXXcF4WGqE9YUnTtRAPSpL3Z0YEWDaHQEA17WGoF3EU/g838w+ZcxgrLR6LQEOkJS+qYWnqza2v8oufrUi5HaNEQCg1RjzraH+G1OKb2qJ9a8xRGHbdDoSEaKiy1DSaLg2vzlK4kbWQIcx33RaEhOheip1IBXRmSxWmkxrq6rKNFQyg0xGdUWGodLthcRGqu474oNET9scabN+5Uq81iR235ei0tc4QWDaHQEJ2PNR50RQKpVqlsTMSHzJjr+AAUGqLzAYMRR7Eof0TG6tqumzqNVM9HaNEQCg0BPSWsfUHF5mqx1DRnpjC70aIhFBri/W9/q60tX/QzDk6lUlNPFbDCcl8UGqJhNWmq1eZqscigKTajjjhaNIRCQ0Bvf0s5k/ZQZxF+tG9vOUKLhlBoCOgVThE2t89H1p937aAx6rgvCg3xNtfRVfUYRPqJ7IPrWlf/H1o0hEJDQG9/i/STKsV2TT1SYy2+OUWLhlBoCPrtb5s7RFJV2pp78Xkdn41CQ9z67W/fCS0aQqEhFBpCoSEUGkKhIRQaQqEhFBpCoSH+Aqcv/+FR4DDkAAAAAElFTkSuQmCC" alt="" class="qrcodeImg"> -->
                                <div class='color6 font12 mt8'><?php echo Yii::t("referral", "Wechat scan for detail"); ?></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class='flex1 ml20'>
                    <div class='color3 font14  mb5'>{{contentList.student_info_list[sendItem.child.id].className}}</div>
                    <el-select v-model="teacher_ids" style='width:100%' multiple placeholder="<?php echo Yii::t("global", "Please Select"); ?>" @change="selectSendTeacher()">
                        <el-checkbox
                            v-model="checkall"
                            class="ml20"
                            @change="allTeacher()"
                        >
                        <?php echo Yii::t("global", "Select All"); ?>
                        </el-checkbox>
                        <el-option
                        v-for="item in classTeacher"
                        :key="item.uid"
                        :label="item.name"
                        :value="item.uid">
                        </el-option>
                    </el-select>
                    <div class='color3 font14 mt20  mb5'><?php echo Yii::t("labels", "Memo"); ?></div>
                    <div  class='mt8'><textarea class="form-control" placeholder="<?php echo Yii::t("teaching", "Input"); ?>" v-model='sendRemark' rows="3" ></textarea></div>

                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
            <button type="button" class="btn btn-primary" @click='saveSend()'><?php echo Yii::t("global", "OK"); ?></button>
        </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
<!-- 通知记录 -->
<div class="modal fade"  id="viewSendLogModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel"  >
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title"><?php echo Yii::t("referral", "Notify To"); ?></h4>
        </div>
        <div class="modal-body p24" v-if='sendLogList.send_log'>
            <div class=' mb24 relative overflow' v-for='(item,id) in sendLogList.send_log'>
                <div class='mb16'><span class='round'></span><span class='font14 fontBold color3 ml8'>{{item.created_at}}</span></div>
                <span class='roundSolid'></span>
                <div class='row'>
                    <div class='col-md-1'></div>
                    <div class='col-md-11'>
                        <div class='flex  font14'>
                            <div style='width:360px'>
                                <div :class='item.result.identified==1?"sendImg":"greenSendImg"'>
                                    <div class='text-center sendTitle'>
                                        <div class='font16'><?php echo Yii::t("referral", "Notification of a behaviour handling"); ?></div>
                                        <div>NO.{{sendLogList.id}}</div>
                                    </div>
                                    <div class='p24'>
                                        <div class="media" >
                                            <div class="media-left pull-left media-middle">
                                                <a href="javascript:void(0)">
                                                    <img :src="contentList.student_info_list[sendLogList.child.id].avatar" data-holder-rendered="true" class="avatar">
                                                </a>
                                            </div>
                                            <div class="media-body pt4 media-middle">
                                                <div class="lineHeight20  text-primary">
                                                    <span class='font14 color3 nowrap'>{{contentList.student_info_list[sendLogList.child.id].name}} </span>
                                                </div>
                                                <div class="font12 color6 nowrap">{{contentList.student_info_list[sendLogList.child.id].className}}</div>
                                            </div>
                                        </div>
                                        <div class=' mt12'>
                                            <span class=' color6 font14 '><?php echo Yii::t("referral", "Decision"); ?></span>
                                            <div class='flex1 text-right ml24 color3'><span class="label " :class='item.result.identified==1?"label-danger":"label-success"'>{{showTitle(item.result.identified,'fact')}}</span></div>
                                        </div>
                                        <div class='flex mt12' v-if='item.result.identified==1'>
                                            <span class='color6 font14 '><?php echo Yii::t("referral", "Final Decision"); ?></span>
                                            <div class='flex1 text-right ml24 color3'>
                                                <span class='ml10' v-for='(list,index) in item.result.actions'>{{showTitle(list,'actions')}}</span>
                                                <div>
                                                    <span class=''  v-if='item.followup.date'>{{item.followup.date[0]}} ~ {{item.followup.date[1]}}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class='flex mt12'>
                                            <span class='color6 font14 '><?php echo Yii::t("referral", "Contact parents"); ?></span>
                                            <div class='flex1 text-right ml24  color3'><span class='mr10' v-for='(list,index) in item.result.contact_parents'>{{showTitle(list,'contact_parents')}}</span></div>
                                        </div>
                                        <div class='flex mt12'>
                                            <span class='color6 font14 '><?php echo Yii::t("referral", "Comment"); ?></span>
                                            <div class='flex1 ml24 text-right  color3' v-html='htmlBr(item.result.remark)'></div>
                                        </div>
                                        <div class='flex mt12 align-items '>
                                            <span class='color6 font14 '><?php echo Yii::t("referral", "By"); ?></span>
                                            <div class='flex1 ml24 color3'>
                                                <div class='flex align-items pull-right'>
                                                    <img :src="contentList.staff_info_list[item.created_by].photoUrl" data-holder-rendered="true" class="avatar32">
                                                    <span class='ml10'>{{contentList.staff_info_list[item.created_by].name}}</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class='flex mt12'>
                                            <span class='color6 font14 '><?php echo Yii::t("ptc", "Time"); ?></span>
                                            <div class='flex1 text-right ml24 color3'>{{item.created_at}}</div>
                                        </div>
                                        <div class='text-center mt20'>
                                            <div class="qrcodeShow" ></div>
                                            <!-- <img src="data:image/png;base64, iVBORw0KGgoAAAANSUhEUgAAAHgAAAB4CAIAAAC2BqGFAAAACXBIWXMAAA7EAAAOxAGVKw4bAAADSElEQVR4nO2dQY7bMAxFO0VPk/ufJccJuiigRQXLJEW/ODPvrYLEkpwPmqZIyf56vV6/5Hp+v/sEfgoKDaHQEAoNodAQCg2h0BAKDaHQEH/WPz8ej53en8/n6TFjiPngyE+pQWsdRjj9p1o0hEJDnLiOQcQJDObLcP5mdDg+1HxI5OBF85nNf3qEFg2h0BBR1zGo3e7nqzjS4WL0Wjyz8GCL5jMp9/IPLRpCoSHSrqNGxD8sgoRaGBMZAkOLhlBoCMh1pG7TtYlGJKJIddiLFg2h0BBp11G76FL5h8jB8zeRECXllHrdixYNodAQUdcBRPg1j9EeSFz0T7VoCIWG+Lrzst2IW1jMU3rznJto0RAKDXHiOlK34M0kZFfzma4k7U6oo0VDKDRE1HWkruvUfT9VMF3QNdG4qFyrRUMoNEQ96qjNC2qpifZSS6TnyFTIqON2KDTESZr0upJErZa6WWpZ9Lz50ylaNIRCQ6SjjtrMZe5w8y5fC3UiPUdaFdCiIRQaIlphqeVL5+apFEeqVfsZLih4FS0aQqEh6mnS2ubTVNrhtgtEzXXcF4WGqE9YUnTtRAPSpL3Z0YEWDaHQEA17WGoF3EU/g838w+ZcxgrLR6LQEOkJS+qYWnqza2v8oufrUi5HaNEQCg1RjzraH+G1OKb2qJ9a8xRGHbdDoSEaKiy1DSaLg2vzlK4kbWQIcx33RaEhOheip1IBXRmSxWmkxrq6rKNFQyg0xGdUWGodLthcRGqu474oNET9scabN+5Uq81iR235ei0tc4QWDaHQEJ2PNR50RQKpVqlsTMSHzJjr+AAUGqLzAYMRR7Eof0TG6tqumzqNVM9HaNEQCg0BPSWsfUHF5mqx1DRnpjC70aIhFBri/W9/q60tX/QzDk6lUlNPFbDCcl8UGqJhNWmq1eZqscigKTajjjhaNIRCQ0Bvf0s5k/ZQZxF+tG9vOUKLhlBoCOgVThE2t89H1p937aAx6rgvCg3xNtfRVfUYRPqJ7IPrWlf/H1o0hEJDQG9/i/STKsV2TT1SYy2+OUWLhlBoCPrtb5s7RFJV2pp78Xkdn41CQ9z67W/fCS0aQqEhFBpCoSEUGkKhIRQaQqEhFBpCoSH+Aqcv/+FR4DDkAAAAAElFTkSuQmCC" alt="" class="qrcodeImg"> -->
                                            <div class='color6 font12 mt8'><?php echo Yii::t("referral", "Wechat scan for detail"); ?></div>
                                        </div>
                                    </div>
                                </div>
                            </div>      
                            <div class='flex1 ml20'>
                                <div class='color3 font14 mb16 fontBold mt4'>{{item.assist.class_name}}</div>
                                <div class='row'>
                                    <div class='col-md-6 mb16'  v-for='(list,index) in item.assist.teacher_ids'><span class='color6 font14'>{{contentList.staff_info_list[list].name}}</span></div>
                                </div>
                                <div class='color3 font14 mt24 fontBold'><?php echo Yii::t("labels", "Memo"); ?></div>
                                <div class='color6 font14 mt8' v-html='htmlBr(item.remark)'></div>

                            </div>
                        </div>   
                    </div>
                </div>
            </div>
        </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
 <!-- 认定记录 -->
 <div class="modal fade" id="affirmRecordListModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("referral", "Behavior Submission Entries"); ?></h4>
            </div>
            <div class="modal-body p24" >
                <div class="media pb10" v-if='affirmData.id'>
                    <div class="media-left pull-left media-middle relative">
                        <img :src="contentList.student_info_list[affirmData.id].avatar" data-holder-rendered="true" class="avatar">
                    </div> 
                    <div class="pull-left media-middle">
                        <div class="mt4 color3">{{contentList.student_info_list[affirmData.id].name}} 
                            <span v-if='affirmData.tag==2 || affirmData.tag==3' class='glyphicon glyphicon-bookmark font12' :class='affirmData.tag==2?"waitingColor":affirmData.tag==3?"colorRed":""'></span>
                            <!-- <span :class='affirmData.tag==2?"tagLabelYellow":affirmData.tag==3?"tagLabel":""'>{{showTitle(affirmData.tag,'ISP_type')}}</span> -->
                        </div> 
                        <div class="color6 font12">{{contentList.student_info_list[affirmData.id].className}}</div>
                    </div>
                </div>
                <div v-for='(item,idx) in affirmRecordList.list' class='bgGrey presenter mt12 relative font14'>
                    <span class='NOID'>No.{{item.id}}</span>
                    
                    <div class='flex mt12'>
                        <span class='color6 font14'><?php echo Yii::t("referral", "Decision"); ?>：</span>
                        <div class='flex1 color3'><span class="label fontWeightNormal" :class='item.result.identified==1?"label-danger":"label-success"'>{{showTitle(item.result.identified,'fact')}}</span></div>
                    </div>
                    <div class='flex mt12'>
                        <span class='color6 font14'><?php echo Yii::t("referral", "Time in Office"); ?>：</span>
                        <div class='flex1 color3'>{{item.result.duration}}</div>
                    </div>
                    <div class='flex mt12' v-if='item.result.identified==1'>
                        <span class='color6 font14'><?php echo Yii::t("referral", "Final Decision"); ?>：</span>
                        <div class='flex1 color3'>
                            <span class='mr10' v-for='(list,index) in item.result.actions'>{{showTitle(list,'actions')}}</span>
                            <span v-if='item.followup.date'>{{item.followup.date[0]}} ~ {{item.followup.date[1]}}</span>
                        </div>
                    </div>
                    <div class='flex mt12'>
                        <span class='color6 font14'><?php echo Yii::t("referral", "Contact parents"); ?>：</span>
                        <div class='flex1 color3'><span class='mr10' v-for='(list,index) in item.result.contact_parents'>{{showTitle(list,'contact_parents')}}</span></div>
                    </div>
                    <div class='flex mt12'>
                        <span class='color6 font14'><?php echo Yii::t("referral", "Comment"); ?>：</span>
                        <div class='flex1 color3' v-html='htmlBr(item.result.remark)'></div>
                    </div>
                    <div class='flex mt12' v-if='item.result.attachments && (item.result.attachments.img || item.result.attachments.other)'>
                        <span class='color6 font14'><?php echo Yii::t("curriculum", "Attachments"); ?>：</span>
                        <div class='flex1 color3'>
                            <div class=''>
                                <ul class='mb12 imgLi'  id='record'  v-if='item.result.attachments.img'>
                                    <li v-for='(list,i) in item.result.attachments.img'>
                                        <img :src="affirmRecordList.attachmentList[list].file_key" class='imgList mb8 mr8' @click='showImg("record",item.result.attachments.img)' alt=""  >
                                    </li>
                                </ul>
                            </div>
                            <div class='mt12 color3' v-if='item.result.attachments.other'>
                                <div v-for='(list,j) in item.result.attachments.other'>
                                    <div class='flex fileLink' >
                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/PDF1.png' ?>" alt="" class="pdfImg" v-if='affirmRecordList.attachmentList[list].mime_type=="application/pdf"' />
                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/excel.png' ?>" alt="" class="pdfImg" v-else-if='affirmRecordList.attachmentList[list].mime_type=="application/vnd.ms-excel" || affirmRecordList.attachmentList[list].mime_type=="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"' />
                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/word1.png' ?>" alt="" class="pdfImg" v-else-if='affirmRecordList.attachmentList[list].mime_type=="application/msword" || affirmRecordList.attachmentList[list].mime_type=="application/vnd.openxmlformats-officedocument.wordprocessingml.document"' />
                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/zip.png' ?>" alt="" class="pdfImg" v-else-if='affirmRecordList.attachmentList[list].mime_type=="application/x-zip-compressed"' />
                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" v-else /> 
                                        <a class='flex1 ml5' target= "_blank" :href="list.url">{{affirmRecordList.attachmentList[list].title}}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class='flex mt12 align-items '>
                        <span class='color6 font14'><?php echo Yii::t("referral", "By"); ?>：</span>
                        <div class='flex1 color3'>
                            <div class='flex align-items '>
                                <img :src="affirmRecordList.staff_info[item.result.created_by].photoUrl" data-holder-rendered="true" class="avatar32">
                                <span class='ml10'>{{affirmRecordList.staff_info[item.result.created_by].name}}</span>
                            </div>
                        </div>
                    </div>
                    <div class='flex mt12'>
                        <span class='color6 font14'><?php echo Yii::t("ptc", "Time"); ?>：</span>
                        <div class='flex1 color3'>{{item.result.created_at}}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 发通知记录 -->
<div class="modal fade"  id="allSendLogModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel"  >
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title"><?php echo Yii::t("referral", "Share To"); ?></h4>
            </div>
            <div class="modal-body p24" v-if='sendLogList' style='min-height:400px'>
                <div>
                    <div class="alert alert-warning" role="alert" style='padding:10 15px'><span class='glyphicon glyphicon-exclamation-sign mr5'></span><?php echo Yii::t("referral", "Staff whom shared to will be added as collaborator"); ?></div>
                    <div  class='flex mt16 mb16'>
                        <el-select
                                class='flex1'
                                v-model="sendTeacherUid"
                                filterable
                                remote
                                multiple 
                                size='small'
                                clearable
                                @visible-change="loseFocus"
                                class='inline-input flex1 formControl'
                                placeholder="<?php echo Yii::t("directMessage", "Staff Name"); ?>"
                                :remote-method="sendRemoteMethod"
                                prefix-icon="el-icon-search"
                                :reserve-keyword='false'
                                @change='senTeacherOption'
                                :loading="loading">
                            <el-option
                                    v-for="item in sendTecherOptions"
                                    :key="item.uid"
                                    :label="item.name"
                                    class='optionSearch mb8'
                                    :value="item.uid">
                                <div class="media">
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)">
                                            <img :src="item.photoUrl" data-holder-rendered="true" class="media-object img-circle avatar">
                                        </a>
                                    </div>
                                    <div class="media-body mt5 media-middle">
                                        <h4 class="media-heading font14 color3 text_overflow">{{item.name}}</h4>
                                        <div class="text-muted text_overflow font12">{{ item.hrPosition }}</div>
                                    </div>
                                </div>
                            </el-option>
                        </el-select>
                        <button type="button" class="btn btn-primary ml16"  :disabled='sendTeacherUid.length==0?true:false' @click='sendConfirmTeacher'><?php echo Yii::t("global", "Send"); ?></button>
                    </div>
                </div>
                <div class='mt24' v-if='contentList.behavior && contentList.behavior.send_log.length!=0'>
                    <div class='font14 color3 mb8 fontBold'><?php echo Yii::t("referral", "Share Logs"); ?></div>
                    <div class='pb24 relative overflow' v-for='(item,id) in contentList.behavior.send_log'>
                        <div class='mb16'><span class='round'></span><span class='font14 color3 ml8'>{{item.created_at}}</span></div>
                        <span class='roundSolid'></span>
                        <div style='margin-left:28px'>
                            <div class='bgGrey '>
                                <span class='mr12 mb8 mt8' v-for='(list,index) in item.teacher_ids'><span class='color6 font14'>{{contentList.staff_info_list[list].name}}</span></span>
                            </div>
                        </div>
                    </div>
                </div>
            
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->