<?php

/**
 * This is the model class for table "ivy_visits_basic_info".
 *
 * The followings are the available columns in table 'ivy_visits_basic_info':
 * @property integer $id
 * @property string $child_name
 * @property string $parent_name
 * @property integer $birth_timestamp
 * @property string $tel
 * @property string $email
 * @property string $address
 * @property integer $communication
 * @property integer $child_enroll
 * @property integer $country
 * @property integer $knowus
 * @property string $concerns
 * @property string $memo
 * @property integer $register_timestamp
 * @property integer $state_timestamp
 * @property integer $recent_log
 * @property integer $status
 * @property integer $active_date
 * @property integer $childid
 * @property string $receive_language
 * @property integer $update_timestamp
 * @property integer $update_user
 */
class IvyVisitsBasicInfo extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return IvyVisitsBasicInfo the static model class
	 */
	public $schoolid;
	public $times;
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_visits_basic_info';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('register_timestamp, state_timestamp, recent_log', 'required'),
			array('birth_timestamp, communication, child_enroll, country, knowus, register_timestamp, state_timestamp, recent_log, status, active_date, childid, update_timestamp, update_user', 'numerical', 'integerOnly'=>true),
			array('child_name, parent_name, email, address, concerns', 'length', 'max'=>255),
			array('tel', 'length', 'max'=>25),
			array('receive_language', 'length', 'max'=>10),
			array('memo', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, child_name, parent_name, birth_timestamp, tel, email, address, communication, child_enroll, country, knowus, concerns, memo, register_timestamp, state_timestamp, recent_log, status, active_date, childid, receive_language, update_timestamp, update_user', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'record'=>array(self::HAS_MANY, 'IvyVisitsRecord', 'basic_id', 'together'=>true),
            'newlog'=>array(self::HAS_ONE, 'VisitsTraceLog', 'basic_id', 'order'=>'newlog.update_timestamp desc'),
            'log'=>array(self::HAS_MANY, 'VisitsTraceLog', 'basic_id', 'order'=>'log.update_timestamp desc'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'child_name' => Yii::t("labels", '孩子姓名'),
			'parent_name' => Yii::t("labels", '家长姓名'),
			'birth_timestamp' => Yii::t("labels", '孩子生日'),
			'tel' => Yii::t("labels", '电话'),
			'email' => Yii::t("labels", '邮箱'),
			'address' => Yii::t("labels", '地址'),
			'communication' => '联系方式',
			'child_enroll' => '预计入学时间',
			'country' => '国家',
			'knowus' => Yii::t("labels", '如何知道我们'),
			'concerns' => '家长关注',
			'memo' => Yii::t("labels", '备注'),
			'register_timestamp' => '注册时间',
			'state_timestamp' => 'State Timestamp',
			'recent_log' => 'Recent Log',
			'status' => '设置状态',
			'active_date' => 'Active Date',
			'childid' => 'Childid',
			'receive_language' => Yii::t("labels", '接待语言'),
			'update_timestamp' => 'Update Timestamp',
			'update_user' => 'Update User',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('child_name',$this->child_name,true);
		$criteria->compare('parent_name',$this->parent_name,true);
		$criteria->compare('birth_timestamp',$this->birth_timestamp);
		$criteria->compare('tel',$this->tel,true);
		$criteria->compare('email',$this->email,true);
		$criteria->compare('address',$this->address,true);
		$criteria->compare('communication',$this->communication);
		$criteria->compare('child_enroll',$this->child_enroll);
		$criteria->compare('country',$this->country);
		$criteria->compare('knowus',$this->knowus);
		$criteria->compare('concerns',$this->concerns,true);
		$criteria->compare('memo',$this->memo,true);
		$criteria->compare('register_timestamp',$this->register_timestamp);
		$criteria->compare('state_timestamp',$this->state_timestamp);
		$criteria->compare('recent_log',$this->recent_log);
		$criteria->compare('status',$this->status);
		$criteria->compare('active_date',$this->active_date);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('receive_language',$this->receive_language,true);
		$criteria->compare('update_timestamp',$this->update_timestamp);
		$criteria->compare('update_user',$this->update_user);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
	
	public function getOneVisitBasicInfo($phone, $email= null)
	{
		$criteria = new CDbCriteria;
		if (!empty($email))
		{
//			$criteria->condition = "email='".$email."' OR tel=".$phone;
			$criteria->compare('email', $email);
		}
		else
		{
			$criteria->compare('tel', $phone);
		}
		return $this->find($criteria);
	}
    
    public static function communications()
    {
        return array(
            1 => '电话',
            2 => '邮件',
            3 => '俩者皆可',
        );
    }
    
    public function renderSchool()
    {
        foreach ($this->record as $record){
            echo CHtml::openTag('div');
            echo Yii::app()->controller->branchArr[$record->schoolid];
            echo CHtml::closeTag('div');
        }
    }

    public static function status()
    {
        return array(
            10 => '有效',
            20 => '过期',
            30 => '已注册',
            40 => '拒绝联系',
            98 => '已存在',
        );
    }
}