<?php
/**
 * 采购流程工作流
 */
class Purchase implements WorkflowTemplate{
    private $template;
    private $isJumpNode = 0;
    public $controller;
    public $workflow;

    public function __construct() {
        Yii::import('common.models.purchase.*');
        Yii::import('common.models.Uploads');
    }

    public function initi(){
        $purchaseApplication = new PurchaseApplication;
        $data['jsFunction'] = 'WorkflowModalShow';
        $data['modalNameId'] = 'workflow-purchase-modal';
        if (Yii::app()->request->isPostRequest) {
            $purchaseArr = Yii::app()->request->getPost('purchaseArr',0);
            if (empty($purchaseArr)) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '保存失败, 原因：至少添加一个商品。');
                $this->controller->showMessage();
            }
            $transaction = Yii::app()->db->beginTransaction();
            try {
                //保存账单申请表
                $time = time();
                $purchaseApplication->price = $_POST['purchase_price'];
                $purchaseApplication->budget = $_POST['PurchaseApplication']['budget'];
                $purchaseApplication->title = $_POST['PurchaseApplication']['title'];
                $purchaseApplication->content = $_POST['PurchaseApplication']['content'];
                $purchaseApplication->delivery_timestamp = strtotime($_POST['PurchaseApplication']['delivery_timestamp']);
                $purchaseApplication->add_user = $this->controller->staff->uid;
                $purchaseApplication->status = PurchaseApplication::INPROGRESS;
                $purchaseApplication->add_timestamp = $time;
                $purchaseApplication->update_timestamp = $time;
                //保存采购申请表
                if (!$purchaseApplication->save()) {
                    $errors = current($purchaseApplication->getErrors());
                    throw new Exception('保存失败, 原因：采购申请表保存失败，'.current($errors));
                }
                // 保存采购申请中间表
                foreach ($purchaseArr as $v) {
                    $model = new PurchaseApplicationItem;
                    $model->attributes = $v['PurchaseApplicationItem'];
                    $model->aid = $purchaseApplication->id;
                    $model->cid = $model->cid;
                    $model->total_price = $model->price * $model->num;
                    if(!$model->save()){
                        $purchaseApplication->delete();
                        PurchaseApplicationItem::model()->deleteAll('aid=:aid',array(':aid'=>$purchaseApplication->id));
                        $transaction->rollBack();
                        throw new Exception('保存失败, 原因：采购申请中间表保存失败。');
                    }
                }
                //保存工作流业务流程
                $operationData = array('operation_type'=>0,'operation_object_id'=>$purchaseApplication->id,'defination_id'=>$this->workflow->definationObj->defination_id,'branchid'=>$this->workflow->schoolId,'state'=>WorkflowOperation::WORKFLOW_STATS_UNOP,'current_node_index'=>$this->workflow->getNextNodeId(),'exte1'=>'');
                $operationRet = $this->workflow->saveFirstNodeForWorkflow($operationData,array($purchaseApplication->id),$purchaseApplication->title,$transaction);
                if ($operationRet === false){
                    throw new Exception('保存失败, 原因：工作流底层更新失败。');
                }
                // 更新相关附件的 link_id
                if ($files = $_POST['files']) {
                    foreach ($files as $file) {
                        $uploadModel = Uploads::model()->findByPk($file);
                        $uploadModel->link_id = $this->workflow->opertationObj->id;
                        $uploadModel->save();
                    }
                }
                $transaction->commit();
                // 发送邮件
                $message = 'success';
                if ($this->jumpMail() === false){
                    $message = 'save success, 邮件发送失败';
                }
                $this->controller->addMessage('state', 'success');
                $this->controller->addMessage('message', $message);
                $this->controller->addMessage('refresh', true);
                $this->controller->showMessage();
            } catch (Exception $e) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', $e->getMessage());
                $this->controller->showMessage();
            }
        }
        $this->setTemplate('initi');
        $data['purchaseApplication'] = $purchaseApplication;
        $data['workflow'] = $this->workflow;
        return $data;
    }
    
    public function show(){
        $this->setTemplate('show');
        $data['jsFunction'] = 'WorkflowModalShow';
        $data['modalNameId'] = 'workflow-purchase-modal';
        //查找相关的采购商品、标配
        $oDetail = WorkflowOperationDetail::model()->find('operation_id=:operation_id',array(':operation_id'=>$this->workflow->opertationObj->id));
        $purchaseApplication = PurchaseApplication::model()->findByPk($oDetail->operation_object_id);
        $purchaseApplication->update_timestamp = time();
        if (empty($purchaseApplication)){
            throw new Exception('工作流程业务参数错误！',500);
        }
        if (Yii::app()->request->isPostRequest) {
            Yii::log("purchase start", 'info', 'workflow');
            //接收数据
            $buttonCategory = Yii::app()->request->getPost('buttonCategory',0);
            $memo = Yii::app()->request->getPost('memo','');
            $workflowStatus = WorkflowOperation::WORKFLOW_STATS_UNOP;
            if (empty($memo)){
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '保存失败，原因：备注必填！');
                $this->controller->showMessage();
            }
            $transaction = Yii::app()->db->beginTransaction();
            try {
                // 处理商品表
                // 被拒绝
                if ($buttonCategory == WorkflowOperation::WORKFLOW_STATS_OPDENY) {
                    $workflowStatus = WorkflowOperation::WORKFLOW_STATS_OPDENY;
                    $purchaseApplication->status = PurchaseApplication::REFUSE;
                    if (!$purchaseApplication->save()) {
                        $errors = current($purchaseApplication->getErrors());
                        $transaction->rollBack();
                        throw new Exception('原因：采购申请表保存失败，' . current($errors));
                    }
                } else {
                    // 预算内根据节点权限金额判断是否中止
                    if ($purchaseApplication->budget) {
                        if ($purchaseApplication->price <= $this->workflow->nodeObj->field && $workflowStatus == WorkflowOperation::WORKFLOW_STATS_UNOP) {
                            $workflowStatus = WorkflowOperation::WORKFLOW_STATS_OPED;
                            $purchaseApplication->status = PurchaseApplication::AGREE;
                            //处理采购申请表
                            if (!$purchaseApplication->save()) {
                                $errors = current($purchaseApplication->getErrors());
                                $transaction->rollBack();
                                throw new Exception('原因：采购申请表保存失败，' . current($errors));
                            }
                            $this->workflow->endNode = true;
                        }
                    } else {
                        //判断是不是最后节点
                        if ($this->workflow->isEndNode()) {
                            $workflowStatus = WorkflowOperation::WORKFLOW_STATS_OPED;
                            $purchaseApplication->status = PurchaseApplication::AGREE;
                            if (!$purchaseApplication->save()) {
                                $errors = current($purchaseApplication->getErrors());
                                $transaction->rollBack();
                                throw new Exception('原因：采购申请表保存失败，' . current($errors));
                            }
                        }
                    }
                }

                //处理工作流
                if ($this->workflow->saveWorkflow($memo,$workflowStatus,$transaction) === false){
                    throw new Exception('工作流保存失败');
                }

                $transaction->commit();
                // 发送邮件
                $message = '保存成功';
                if ($this->jumpMail() === false){
                    $message = '保存成功，邮件发送失败';
                }

                $this->controller->addMessage('state', 'success');
                $this->controller->addMessage('message', $message);
                $this->controller->addMessage('data',array('modalNameId'=>'workflow-purchase-modal','operatorId'=>$this->workflow->getOperateId(),'html'=>$this->workflow->getNodeListUseHtml()));
                $this->controller->addMessage('callback', 'saveWorkflowOperator');
                $this->controller->showMessage();
            } catch (Exception $e) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '工作流保存异常：' . $e->getMessage());
                $this->controller->showMessage();
            }

        }

        $data['purchaseApplication'] = $purchaseApplication;
        $data['workflow'] = $this->workflow;
        return $data;
    }
    
    public function jumpMail() {
        $this->workflow->wechatPush();

        $branchList = Branch::model()->getBranchList();
        $branchTitle = $branchList[$this->workflow->getOperateValue('branchid')];
        $applyName = $this->workflow->opertationObj->userInfo->getName();
        $workflowTitle = CommonUtils::autoLang($this->workflow->definationObj->defination_name_cn,$this->workflow->definationObj->defination_name_en);
        $nodesTitle = CommonUtils::autoLang($this->workflow->nodesList[$this->workflow->getNextNodeId()]->node_name_cn,$this->workflow->nodesList[$this->workflow->getNextNodeId()]->node_name_en);
        // 判断是否为跳节点
        if ($this->isJumpNode) {
            $nodesTitle = CommonUtils::autoLang(end($this->workflow->nodesList)->node_name_cn,end($this->workflow->nodesList)->node_name_en);
        }
        if ($this->workflow->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_OPDENY){
            $subject = sprintf('%s 校园 %s 提出 %s申请被拒绝',$branchTitle,$applyName,$workflowTitle);
        }else{
            if ($this->workflow->isEndNode()){
                $subject = sprintf('%s 校园 %s 提出 %s申请已完成',$branchTitle,$applyName,$workflowTitle);
            }else{
                $subject = sprintf('%s 校园 %s 提出 %s申请需要您处理',$branchTitle,$applyName,$workflowTitle);
            }
        }

        $link = $this->controller->createUrl('/workflow/index', array('branchId'=>$this->workflow->schoolId));
        $body = $this->controller->renderPartial('/mail/templater',array('branchTitle'=>$branchTitle,'childName'=>$applyName,'workflowTitle'=>$workflowTitle,'nodes'=>$nodesTitle,'link'=>$link,'workflow'=>$this->workflow),true);
        
        if ($this->isJumpNode) {
            $jumpNode = end($this->workflow->nodeIds);
            return $this->workflow->sendEmail($subject,$body,false,$jumpNode);
        }

        if($this->workflow->sendEmail($subject,$body)){
            if ($this->workflow->endNode || $this->workflow->isEndNode()) {
                // 发送采购清单
                Yii::import('application.components.OA');
                $mailer = Yii::createComponent('common.extensions.mailer.Aliyun');

                $subject = '生成订单提醒';
                $mailer->Subject = $subject;
                $mailer->AddAddress('<EMAIL>');
                $mailer->iniMail( OA::isProduction() ); // 此行代码要放到AddAddress, AddCC方法下面
                $mailer->getView('purchaseList', array('applicationModel' => $this), 'main');
                $mailer->Send();
            }
            if ($this->workflow->isFristNode()) {
                // 第一个节点发送邮件给运营部
                Yii::import('application.components.OA');
                $mailer = Yii::createComponent('common.extensions.mailer.Aliyun');
                $subject = '有校园发起新的采购订单申请';
                $mailer->Subject = $subject;
                $mailer->AddAddress('<EMAIL>');
                $mailer->iniMail( OA::isProduction() ); // 此行代码要放到AddAddress, AddCC方法下面
                $mailer->getView('workflow', array('body' => $body), 'main');
                $mailer->Send();
            }
            return true;
        }

        return false;
    }

    //实现接口方法
    public function setTemplate($name){
        $this->template = $name;
    }

    //实现接口方法
    public function getTemplate(){
        return $this->template;
    }

    //删除工作流
    public function delete(){
    }

    public function getStandard()
    {
        if (Yii::app()->request->isPostRequest) {
            $schoolType = Yii::app()->request->getParam('schoolType','');
            $proType = Yii::app()->request->getParam('proType','');

            $purchaseStandards = PurchaseStandard::model()->findAll('status=:status AND school_type=:school_type AND pro_type=:pro_type',array(':status'=>1,':school_type'=>$schoolType,':pro_type'=>$proType));
            $data = '<option>选择标配</option>';
            foreach ($purchaseStandards as $purchaseStandard) {
                $data .= '<option data-price="'. $purchaseStandard->getTotalMoney() . '" value="'.$purchaseStandard->id.'">'.$purchaseStandard->cn_title.'</option>';
            }

            $this->controller->addMessage('data',$data);
            $this->controller->showMessage();
        }
    }

    /**
     * 保存上传的附件
     * @return [type] [description]
     */
    public function saveUploadFile()
    {       
        if ($file = CUploadedFile::getInstanceByName('file')) {
            if ($file->size > 2*1024*1024) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '上传失败，资料过大。');
                $this->controller->showMessage();
            }
            $needType = array('jpg','png','pdf','gif','jpeg', 'doc', 'docx', 'xls', 'xlsx', 'tif', 'tiff');
            if (!in_array($file->getExtensionName(), $needType)) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '上传失败，资料类型出错。');
                $this->controller->showMessage();
            }
            $datePath = date('Y') .'/'. date('m') . '/';
            $filePath = Yii::app()->params['xoopsVarPath'] . '/uploads/' . $datePath;
            if (!is_dir($filePath)) {
                mkdir($filePath, 0777, true);
            }
            $baseUrl = Yii::app()->params['OABaseUrl'] . "/modules/myspace/task/getthumb.php?img=";
            $ext = $file->getExtensionName();
            $fileName = uniqid() . '.' . $ext;
            if (!$file->saveAs($filePath . $fileName)) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '资料保存失败。');
                $this->controller->showMessage();
            }
            //保存表ivy_uploads
            $uploadModel = new Uploads();
            $uploadModel->link_id = $this->workflow->opertationObj->id;
            $uploadModel->mod_name = 'workflow';
            $uploadModel->func_name = 'certificate';
            $uploadModel->file_name = $datePath . $fileName;

            $uploadModel->notes = pathinfo($file->name,PATHINFO_FILENAME);
            $uploadModel->update_time = time();
            $uploadModel->update_user = Yii::app()->user->id;
            if (!$uploadModel->save()) {
                unlink($filePath . $fileName);
                $error = current($uploadModel->getErrors());
                $this->controller->addMessage('message', '资料表保存失败。'.$error[0]);
                $this->controller->showMessage();
            }
            $this->controller->addMessage('data', array('fileId'=>$uploadModel->id,'fileName'=>$uploadModel->notes,'url'=>$baseUrl . $uploadModel->file_name));
            $this->controller->addMessage('state', 'success');
            $this->controller->addMessage('message', '资料上传成功。');
            $this->controller->showMessage();
        }
        Yii::app()->end();
    }

    /**
     * 删除附件
     * @return [type] [description]
     */
    public function deleteUploadFile()
    {
        if (Yii::app()->request->isPostRequest) {
            $id = Yii::app()->request->getPost('id',0);
            if ($uploadModel = Uploads::model()->findByPk($id)) {
                //删除资料
                $fileName = $uploadModel->file_name;
                $filePath = Yii::app()->params['xoopsVarPath'] . '/uploads/' ;
                if ($uploadModel->delete()) {
                    unlink($filePath . $fileName);
                    $this->controller->addMessage('state', 'success');
                    $this->controller->addMessage('message', '资料删除成功。');
                    $this->controller->showMessage();
                }
            }
        }
        $this->controller->addMessage('state', 'fail');
        $this->controller->addMessage('message', '资料删除失败。');
        $this->controller->showMessage();
    }

    /**
     * 修改附件显示名
     * @param string $value [description]
     */
    public function changeFileName()
    {
        if (Yii::app()->request->isPostRequest) {
            $id = Yii::app()->request->getPost('id',0);
            $notes = Yii::app()->request->getPost('notes',0);
            if ($notes && $uploadModel = Uploads::model()->findByPk($id)) {
                $uploadModel->notes = $notes;
                if ($uploadModel->save()) {
                    $this->controller->addMessage('state', 'success');
                    $this->controller->addMessage('message', '资料修改成功。');
                    $this->controller->showMessage();
                }
            }
        }
        $this->controller->addMessage('state', 'fail');
        $this->controller->addMessage('message', '资料修改失败。');
        $this->controller->showMessage();
    }

}