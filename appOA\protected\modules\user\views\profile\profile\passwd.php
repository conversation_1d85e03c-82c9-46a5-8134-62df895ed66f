<?php
$labels = User::model()->attributeLabels();
$this->staff->pass = '';
?>
<?php echo CHtml::form($this->createUrl('//user/profile/changePd'), 'post', array('class'=>'form-horizontal J_ajaxForm','role'=>'form'))?>
<div class="panel panel-default">
    <div class="panel-heading">
        <h3 class="panel-title edit-title">
        <!--更改密码-->
        </h3>
    </div>
    <div class="panel-body">
        <div class="form-group">
            <?php echo CHtml::label($labels['email'], CHtml::getIdByName('User[email]'), array('class'=>'control-label col-sm-2')); ?>
            <div class="col-lg-3 col-sm-10">
                <?php echo CHtml::textField('User[email]', $this->staff->email,array('class'=>'form-control','disabled'=>'disabled'));?>
            </div>
        </div>
        <div class="form-group" model-attribute="pass">
            <?php echo CHtml::label($labels['pass'], CHtml::getIdByName('User[pass]'), array('class'=>'control-label col-sm-2')); ?>
            <div class="col-lg-3 col-sm-10">
                <?php echo CHtml::passwordField('User[pass]', $this->staff->pass,array('class'=>'form-control','placeholder'=>$labels['pass']));?>
            </div>
        </div>
        <div class="form-group" model-attribute="changePassword">
            <?php echo CHtml::label($labels['changePassword'], CHtml::getIdByName('User[changePassword]'), array('class'=>'control-label col-sm-2')); ?>
            <div class="col-lg-3 col-sm-10">
                <?php echo CHtml::passwordField('User[changePassword]', $this->staff->changePassword,array('class'=>'form-control','placeholder'=>$labels['changePassword']));?>
            </div>
        </div>
        <div class="form-group" model-attribute="verifyPassword">
            <?php echo CHtml::label($labels['verifyPassword'], CHtml::getIdByName('User[verifyPassword]'), array('class'=>'control-label col-sm-2')); ?>
            <div class="col-lg-3 col-sm-10">
                <?php echo CHtml::passwordField('User[verifyPassword]', $this->staff->verifyPassword,array('class'=>'form-control','placeholder'=>$labels['verifyPassword']));?>
            </div>
        </div>
    </div>
    <div class="panel-footer">
        <button type="button" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Save');?></button>
    </div>
</div>
<?php echo CHtml::endForm()?>