<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link('总部运营', array('/moperation'))?></li>
        <li><?php echo CHtml::link('活动管理', array('/moperation/admissions/index'))?></li>
        <li class="active"><?php echo CommonUtils::autoLang($model->cn_title, $model->en_title)?></li>
    </ol>

    <div class="row">
        <div class="col-md-1 col-sm-2">
        <?php
        $this->widget('zii.widgets.CMenu',array(
            'items'=> $pageSubMenu,
            'id'=>'pageCategory',
            'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked text-right background-gray'),
            'activeCssClass'=>'active',
            'itemCssClass'=>''
        ));
        ?>
        </div>
        <div class="col-md-11 col-sm-10">
            <div class="background-gray p8">
                <?php echo CHtml::link('<span class="glyphicon glyphicon-plus"></span> 添加', array('updatemember', 'event_id'=>$model->id), array('class'=>'btn btn-primary J_dialog mb5', 'title'=>'添加'));?>
                <?php echo CHtml::link('<span class="glyphicon glyphicon-log-in"></span> 导入名单', array('import', 'id'=>$model->id), array('class'=>'btn J_dialog btn-primary mb5', 'title'=>'导入名单'));?>
                <?php echo CHtml::link('<span class="glyphicon glyphicon-retweet"></span> 批量转为潜客', '#', array('id'=>'_tocrm', 'class'=>'btn btn-primary mb5'));?>
                <?php echo CHtml::link('', array('toCrm', 'id'=>$model->id), array('id'=>'tocrm', 'class'=>'J_dialog', 'title'=>'批量转为潜客'))?>
                <?php echo CHtml::link('<span class="glyphicon glyphicon-log-out"></span> 导出Excel', array('export', 'id'=>$model->id), array('class'=>'btn btn-primary mb5', 'title'=>'导出Excel'));?>
            </div>

            <?php
            $this->widget('ext.ivyCGridView.BsCGridView', array(
                'id'=>'event-member-grid',
                'dataProvider'=>$dataProvider,
                'colgroups'=>array(
                    array(
                        "colwidth"=>array(80,80,80,80,80,80,80,80,80,80,80,80),
                    )
                ),
                'columns'=>array(
                    'parent_name',
                    'child_name',
                    array(
                        'name'=>'child_birthday',
                        'value'=>'OA::formatDateTime($data->child_birthday)',
                    ),
                    'email',
                    'tel',
                    'adult_number',
                    'kid_number',
                    'address',
                    'from',
                    array(
                        'name'=>'timestamp',
                        'value'=>'OA::formatDateTime($data->timestamp, "medium", "short")',
                    ),
                    array(
                        'class'=>'BsCButtonColumn',
                        'updateButtonUrl'=>'Yii::app()->createUrl("/moperation/admissions/updateMember", array("id" => $data->id, "event_id"=>'.$model->id.'))',
                        'deleteButtonUrl'=>'Yii::app()->createUrl("/moperation/admissions/deleteMember", array("id" => $data->id))',
                    ),
                ),
            )); ?>
        </div>
    </div>
</div>

<script type="text/javascript">
    $('#_tocrm').on('click', function(e){
        e.preventDefault();
        if ( confirm("确定将本活动下的所有用户转为潜客？\n若电邮/手机号已在潜客系统中，该用户将不再重复转化。") ){
            $('#tocrm').click();
        }
    })
</script>