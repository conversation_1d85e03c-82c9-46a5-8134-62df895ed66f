<?php $this->renderPartial("//layouts/auth._header"); ?>

<div id="login">
    <div class="seperator">
    </div>

    <div class="span-15 colborder">
        <?php
        $loginpage = array(
            array(
                'image' => true,
                'file' => Yii::app()->theme->baseUrl . '/images/slider01.jpg',
                //'url'=>'javascript:(void);',
                'style' => 'background-color:white;',
                //'caption'=>'Demo caption, this is demo caption',
            ),
            array(
                'image' => true,
                'file' => Yii::app()->theme->baseUrl . '/images/slider02.jpg',
                //'url'=>'javascript:(void);',
                'style' => 'background-color:white;display:none;',
                //'caption'=>'Demo caption, this is demo caption',
            ),
            array(
                'image' => true,
                'file' => Yii::app()->theme->baseUrl . '/images/slider03.jpg',
                //'url'=>'javascript:(void);',
                'style' => 'background-color:white;display:none;',
                //'caption'=>'Demo caption, this is demo caption',
            ),
            array(
                'image' => true,
                'file' => Yii::app()->theme->baseUrl . '/images/slider04.jpg',
                //'url'=>'javascript:(void);',
                'style' => 'background-color:white;display:none;',
                //'caption'=>'Demo caption, this is demo caption',
            ),
        );
        if(Mims::unIvy()){
            $loginpage = Yii::app()->params['ownerConfig']['loginPage'];
        }
        $this->widget('ext.OrbitSlider.OrbitSlider', array(
            'content' => $loginpage,
            'slider_options' => array(
                'animation' => 'horizontal-slide',
                'bullets' => false,
                'directionalNav' => true,
                'advanceSpeed' => '5000',
                'timer' => true,
            ),
            'width' => '590',
            'height' => '310',
        ));
        ?>
    </div>
    <div class="span-8 last">
        <?php echo $content; ?>
    </div>
    <div class="clear"></div>


</div>



</div><!-- page -->

<?php if(!Mims::unIvy()):?>
<div id="describer">
    <div class="container">
        <div class="span-17">
			<?php
			if($this->beginCache($this->genCacheId('testimonial'), array('duration'=>1800))) {
			
				$this->widget('ext.testimonial.LoginPage');
				
			$this->endCache(); }
			?>
        </div>
        <div class="span-7 last">
        &nbsp;
        </div>
    </div>
</div>
<?php endif;?>

<div class="container">
    <div id="footer">

        <?php $this->renderPartial("//layouts/_footer_links"); ?>

        <div class="clear"></div>
    </div><!-- footer -->
</div><!-- footer -->
<!--[if IE 6]>
<?php $this->renderPartial("//layouts/ie6_alert"); ?>
<![endif]-->

<?php
if(Yii::app()->params['baiduStats'])
	$this->renderPartial('//statsScripts/baiduStats_common');
?>
</body>
</html>