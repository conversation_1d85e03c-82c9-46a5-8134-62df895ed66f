<?php

/* 
 * 批量更改孩子账单关联班级不正确情况
 * @param schoolId
 * @param calendarId
 */
class ChangeClassCommand extends CConsoleCommand{

    public $batchNum = 200;

    public function init(){
        Yii::import('common.models.invoice.Invoice');
        Yii::import('common.models.invoice.InvoiceTransaction');
        Yii::import('common.models.invoice.ChildServiceInfo');
        Yii::import('common.models.invoice.DepositHistory');
        Yii::import('common.models.calendar.*');
    }
    
    /*
     * 入口
     * @param string $schoolId
     * @param int    $calendarId
     */
    public function actionIndex($schoolId=null,$calendarId=0){
        if (empty($schoolId) && !$calendarId){
            echo $this->getHelp();
            return;
        }
        $childs = Yii::app()->db->createCommand()
            ->select('childid,classid')
            ->from('ivy_child_school_class')
            ->where('schoolid=:schoolId and calendar=:calendarId', array(':schoolId'=>$schoolId,':calendarId'=>$calendarId))
            ->queryAll();
        foreach ($childs as $child){
            $criteria = new CDbCriteria();
            $criteria->compare('t.childid', $child['childid']);
            $criteria->compare('t.payment_type', array('tuition','bus','lunch','entrance'));
            $criteria->compare('t.calendar_id', $calendarId);
            $criteria->compare('t.schoolid', $schoolId);
            $invices = Invoice::model()->with('invoiceTransaction')->findAll($criteria);
            if (!empty($invices)){
                foreach ($invices as $invoice){
                    if ($invoice->classid != $child['classid']){
                        echo $child['childid']."..".$invoice->classid." to ".$child['classid'].".....is ok \n";
                        $invoice->classid = $child['classid'];
                        $invoice->save();
                        //transaction
                        if (!empty($invoice->invoiceTransaction)){
                            foreach ($invoice->invoiceTransaction as $tran){
                                $tran->classid = $child['classid'];
                                $tran->save();
                                $active = ChildActiveStatus::model()->find('transaction_id=:transaction_id',array(':transaction_id'=>$tran->id));
                                if (!empty($active)){
                                    $active->classid = $child['classid'];
                                    $active->save();
                                }
                            }
                        }
                        //service info
                        $service = ChildServiceInfo::model()->find('invoice_id=:invoice_id',array(':invoice_id'=>$invoice->invoice_id));
                        if (!empty($service)){
                            $service->classid = $child['classid'];
                            $service->save();
                        }
                    }
                }
            }
        }
    }

    /*
     * 更新孩子表第一个有效账单学年字段，  根据孩子有效账单的学年来存储数据
     */

    public function actionUpdateFirstStaryear($first = 0)
    {
        $criteria = new CDbCriteria();
        if(!$first) {
            $criteria->compare('t.status', array(ChildProfileBasic::STATS_ACTIVE_WAITING, ChildProfileBasic::STATS_ACTIVE, ChildProfileBasic::STATS_REGISTERED));
        }

        $criteria->addCondition("first_startyear IS NULL");
        $total = ChildProfileBasic::model()->count($criteria);

        $cycle = ceil($total/$this->batchNum);
        $num = 1;
        for($i=0; $i<$cycle; $i++){
            $criteria->limit=$this->batchNum;
            $criteria->offset=0;
            $items = ChildProfileBasic::model()->findAll($criteria);
            foreach ($items as $val){
                $criteria1 = new CDbCriteria();
                $criteria1->compare('childid', $val->childid);
                $criteria1->compare('schoolid',$val->schoolid);
                $criteria1->compare('payment_type', 'tuition');
                $criteria1->compare('status', '<>99');
                $criteria1->order = 'timestamp ASC';
                $criteria1->limit = '1';
                $invoiceModel = Invoice::model()->find($criteria1);
                if($invoiceModel){
                    $calendarModel = Calendar::model()->findByPk($invoiceModel->calendar_id);
                    $val->first_startyear = $calendarModel->startyear;
                    if($val->save(false)){
                        echo $num . ' - ' .$val->childid;
                        echo "\n";
                        $num++;
                    }
                }
                else {
                    $val->first_startyear = 1;
                    $val->save(false);
                }
            }
        }
        // todo 更新所有first_startyear=1的为NUll
        $sql = "UPDATE `ivy_child_profile_basic` SET `first_startyear` = NULL where `first_startyear` = 1";
        Yii::app()->db->createCommand($sql)->execute();
    }

    public function actionSchoolStartyear($year=0, $all=0)
    {
        $yids = array();

        if($all == 0)
            $all = time()-86400;
        if($year) {
            $calendarObjs = Yii::app()->db->createCommand()
                ->from('ivy_calendar_yearly')
                ->where('startyear=:year', array(':year'=>$year))
                ->queryAll();
            foreach ($calendarObjs as $cObj) {
                $yids[$cObj['yid']] = $cObj['yid'];
            }
        }

        $branchObjs = Yii::app()->db->createCommand()
            ->from('ivy_branch')
            ->where('status=10')
            ->queryAll();
        foreach ($branchObjs as $bObj) {
            if($year){
                $sql = "SELECT * FROM `ivy_invoice_invoice` WHERE schoolid='%s' and payment_type='tuition' and status!=99 and status!=88 and calendar_id in (%s) and timestamp>%s";
                $sql = sprintf($sql, $bObj['branchid'], implode(',', $yids), $all);
            }
            else {
                $sql = "SELECT * FROM `ivy_invoice_invoice` WHERE schoolid='%s' and payment_type='tuition' and status!=99 and status!=88 and timestamp>%s";
                $sql = sprintf($sql, $bObj['branchid'], $all);
            }
            $invoiceObjs = Yii::app()->db->createCommand($sql)->queryAll();

            foreach ($invoiceObjs as $iObj){
                $criteria = new CDbCriteria();
                $criteria->compare('schoolid', $iObj['schoolid']);
                $criteria->compare('childid', $iObj['childid']);
                $count = SchoolStartyear::model()->count($criteria);
                if(!$count) {
                    $calendarModel = Calendar::model()->findByPk($iObj['calendar_id']);

                    $model = new SchoolStartyear();
                    $model->childid=$iObj['childid'];
                    $model->schoolid=$iObj['schoolid'];
                    $model->classid=$iObj['classid'];
                    $model->from_invoiceid=$iObj['invoice_id'];
                    $model->statryear=$calendarModel->startyear;
                    $model->updated_by=time();
                    if ($model->save()) {
                        echo ".";
                    }
                    else {
                        print_r($model->getErrors());die;
                    }
                }
            }
        }
    }
}
