<?php

/**
 * This is the model class for table "ea_apply".
 *
 * The followings are the available columns in table 'ea_apply':
 * @property integer $id
 * @property string $serial_number
 * @property integer $child_id
 * @property integer $school_id
 * @property integer $family_id
 * @property integer $schoolyear_id
 * @property integer $grade_id
 * @property integer $is_staff
 * @property integer $many_children
 * @property integer $xueji
 * @property integer $is_paid
 * @property integer $step
 * @property string $complete_step
 * @property integer $interview_id
 * @property string $memo
 * @property integer $status
 * @property integer $created_by
 * @property integer $created_at
 * @property integer $updated_by
 * @property integer $updated_at
 * @property integer $updated_type
 */
class Apply extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ea_apply';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('child_id, school_id, family_id, schoolyear_id, grade_id, is_staff, many_children, is_paid, step, created_by, created_at, updated_by, updated_at', 'required'),
			array('child_id, school_id, family_id, schoolyear_id, grade_id, is_staff, many_children, xueji, is_paid, step, interview_id, status, created_by, created_at, updated_by, updated_at, updated_type', 'numerical', 'integerOnly'=>true),
			array('serial_number, complete_step', 'length', 'max'=>255),
			array('memo', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, serial_number, child_id, school_id, family_id, schoolyear_id, grade_id, is_staff, many_children, xueji, is_paid, step, complete_step, interview_id, memo, status, created_by, created_at, updated_by, updated_at, updated_type', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'serial_number' => 'Serial Number',
			'child_id' => 'Child',
			'school_id' => 'School',
			'family_id' => 'Family',
			'schoolyear_id' => 'Schoolyear',
			'grade_id' => 'Grade',
			'is_staff' => 'Is Staff',
			'many_children' => 'Many Children',
			'xueji' => 'Xueji',
			'is_paid' => 'Is Paid',
			'step' => 'Step',
			'complete_step' => 'Complete Step',
			'interview_id' => 'Interview',
			'memo' => 'Memo',
			'status' => 'Status',
			'created_by' => 'Created By',
			'created_at' => 'Created At',
			'updated_by' => 'Updated By',
			'updated_at' => 'Updated At',
			'updated_type' => 'Updated Type',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('serial_number',$this->serial_number,true);
		$criteria->compare('child_id',$this->child_id);
		$criteria->compare('school_id',$this->school_id);
		$criteria->compare('family_id',$this->family_id);
		$criteria->compare('schoolyear_id',$this->schoolyear_id);
		$criteria->compare('grade_id',$this->grade_id);
		$criteria->compare('is_staff',$this->is_staff);
		$criteria->compare('many_children',$this->many_children);
		$criteria->compare('xueji',$this->xueji);
		$criteria->compare('is_paid',$this->is_paid);
		$criteria->compare('step',$this->step);
		$criteria->compare('complete_step',$this->complete_step,true);
		$criteria->compare('interview_id',$this->interview_id);
		$criteria->compare('memo',$this->memo,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('created_by',$this->created_by);
		$criteria->compare('created_at',$this->created_at);
		$criteria->compare('updated_by',$this->updated_by);
		$criteria->compare('updated_at',$this->updated_at);
		$criteria->compare('updated_type',$this->updated_type);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->mmxxcxdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return Apply the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
