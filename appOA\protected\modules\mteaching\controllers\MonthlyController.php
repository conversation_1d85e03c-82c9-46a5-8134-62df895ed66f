<?php

class MonthlyController extends TeachBasedController
{
    public $selectedClassId=0;
    public $selectedMonth=0;
    public $selectedTask='';
    public $calendarModel=null;
    public $months = array();

    public $printFW = array();

    // 访问action的初级权限
    public $actionAccessAuths = array(
        'index'      => 'o_T_Access',
        'GetLeaView' => 'o_T_Access',
        'PrintLea'   => 'o_T_Access',
    );

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'teaching';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Teaching Tasks');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mteaching/monthly/index');


        Yii::import('common.models.portfolio.*');
    }

	public function actionIndex($classid=0, $month='', $task='')
	{
        parent::initExt();
        list( $this->selectedClassId, $this->selectedMonth, $this->selectedTask ) = array( $classid, $month, $task);
        Yii::import('common.models.calendar.*');
        $this->calendarModel = Calendar::model()->findByPk($this->calendarId);

       $this->months = TeachingMonthly::configType();
//        $weeks = array();
//        $criteria = new CDbCriteria();
//        $criteria->compare('yid', $this->calendarId);
//        $criteria->order = 'weeknumber asc';
//        $items = CalendarWeek::model()->findAll($criteria);
//        foreach ($items as $item) {
//            $weeks[$item->weeknumber] = $item->monday_timestamp;
//        }
//        foreach ($weeks as $week) {
//            $_month = date('Y-m', $week);
//            $this->months[$_month] = $_month;
//        }
        //该校园或班级不支持此功能
        $stopped = array(
            Branch::PROGRAM_DAYSTAR => array(
//                'learntargets',
            ),
            Branch::PROGRAM_IA => array(
                //'learntargets',
            )
        );

        //校园支持本功能
        $stopping = false;
        if(isset($stopped[$this->branchObj->group])){
            if(in_array($task, $stopped[$this->branchObj->group] )){
                //校园不支持本功能
                $stopping = true;
            }
        }
        $taskData = array();
        $taskData['stopping'] = $stopping;

        if(!$stopping){
            if($classid && $task){
                $taskfun = 'task'.ucfirst($task);
                $taskData = $this->$taskfun();
            }
        }

        $this->render('index', array(
            'taskData'=>$taskData,
        ));
	}

    public function taskLearntargets()
    {
        if(Yii::app()->request->isAjaxRequest && Yii::app()->request->isPostRequest){
            if(!$this->checkTeachers() || !Yii::app()->user->checkAccess('o_T_Adm_Common')){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', 'Unauthorized operation, please contact IT Dept.');
                $this->showMessage();
            }
            $flag = Yii::app()->request->getParam('flag', '');
            if($flag){
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','Data saved!'));
                $this->addMessage('callback', 'callback');
                if(strlen($this->selectedMonth) == 1 ){
                    $model = TeachingMonthly::model()->with('content')->findByAttributes(array('classid'=>$this->selectedClassId, 'unit_type'=>$this->selectedMonth));
                }else{
                    $model = TeachingMonthly::model()->with('content')->findByAttributes(array('classid'=>$this->selectedClassId, 'month'=>$this->selectedMonth));
                }

                if($model === null){
                    $model = new TeachingMonthly();
                    $model->content = new TeachingMonthlyContent();
                    $model->schoolid = $this->branchId;
                    $model->classid = $this->selectedClassId;
                    $model->yid = $this->calendarId;
                    if(strlen($this->selectedMonth) == 1 ){
                        $model->unit_type = $this->selectedMonth;
                    }else{
                        $model->month = $this->selectedMonth;
                    }

                }
                $model->timestamp = time();
                $model->uid = Yii::app()->user->id;
                switch($flag){
                    case 'cn':
                        $model->stat_cn = Yii::app()->request->getParam('stat', 0);
                        if(!$model->save()){
                            $this->addMessage('state', 'fail');
                            $errs = current($model->getErrors());
                            $this->addMessage('message', $errs[0]);
                            $this->addMessage('callback', '');
                        }
                        $model->content->id = $model->id;
                        $model->content->content_cn = base64_encode(CJSON::encode(Yii::app()->request->getParam('content', array())));
                        $model->content->objectives_cn = base64_encode(CJSON::encode(Yii::app()->request->getParam('objectives', array())));
                        if(!$model->content->save()){
                            $this->addMessage('state', 'fail');
                            $errs = current($model->content->getErrors());
                            $this->addMessage('message', $errs[0]);
                            $this->addMessage('callback', '');
                        }
                        break;
                    case 'en':
                        $model->stat_en = Yii::app()->request->getParam('stat', 0);
                        if(!$model->save()){
                            $this->addMessage('state', 'fail');
                            $errs = current($model->getErrors());
                            $this->addMessage('message', $errs[0]);
                            $this->addMessage('callback', '');
                        }
                        $model->content->id = $model->id;
                        $model->content->content_en = base64_encode(CJSON::encode(Yii::app()->request->getParam('content', array())));
                        $model->content->objectives_en = base64_encode(CJSON::encode(Yii::app()->request->getParam('objectives', array())));
                        if(!$model->content->save()){
                            $this->addMessage('state', 'fail');
                            $errs = current($model->content->getErrors());
                            $this->addMessage('message', $errs[0]);
                            $this->addMessage('callback', '');
                        }
                        break;
                    case 'mik':
                        $model->stat_en = Yii::app()->request->getParam('stat', 0);
                        if(!$model->save()){
                            $this->addMessage('state', 'fail');
                            $errs = current($model->getErrors());
                            $this->addMessage('message', $errs[0]);
                            $this->addMessage('callback', '');
                        }
                        $model->content->id = $model->id;
                        $model->content->content_mik_en = base64_encode(CJSON::encode(Yii::app()->request->getParam('content', array())));
                        if(!$model->content->save()){
                            $this->addMessage('state', 'fail');
                            $errs = current($model->content->getErrors());
                            $this->addMessage('message', $errs[0]);
                            $this->addMessage('callback', '');
                        }
                        break;
                }
                Yii::log(sprintf('%s,c%d,m%s,u%d',
                            $this->branchId,
                            $this->selectedClassId,
                            $this->selectedMonth,
                            Yii::app()->user->id
                    ),
                    CLogger::LEVEL_INFO, 'teaching.learningTargets');

                $this->showMessage();
            }
        }

        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/phpfunc.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
//        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
    }

    public function actionGetLeaView($classid=0, $month='')
    {
        $data = $this->genLeaData($classid, $month);
        echo CJSON::encode($data);
    }

    public function actionPrintLea($classid=0, $month='', $lang='cn')
    {
        $this->layout='//layouts/print';
        $this->printFW = $this->branchObj->getPrintHeader();
        $data = $this->genLeaData($classid, $month);
        $_data = $data[$lang];
        if($lang == 'en'){
            if(isset($data['mik'])){
                $_data = $data['mik'];
            }
        }
        $classModel=IvyClass::model()->findByPk($classid);
        $this->render('printlea', array('data'=>$_data, 'classModel'=>$classModel, 'month'=>$month));
    }

    public function genLeaData($classid=0, $month='')
    {
        $data = array();
        if($classid && $month){
            $classModel=IvyClass::model()->findByPk($classid);
            if(strlen($month) == 1 ){
                $model = TeachingMonthly::model()->with('content')->findByAttributes(array('classid'=>$classid, 'unit_type'=>$month));
            }else{
                $model = TeachingMonthly::model()->with('content')->findByAttributes(array('classid'=>$classid, 'month'=>$month));
            }
            if($model === null){
                $model = new TeachingMonthly;
                $model->content = new TeachingMonthlyContent();
            }
            $model->content->expVista();
            $keyA = array('title', 'questions', 'activities', 'trips', 'song', 'festivals', 'language', 'mathematical', 'science', 'physical', 'social', 'art', 'social_studies', 'newIdea', 'newPhysical','newLanguage','newSocial','newScience','newArt');
            // = array('title', 'newIdea', 'questions', 'activities', 'trips', 'song', 'festivals', 'newPhysical','newLanguage','newSocial','newScience','newArt');
            //$keyB = array('words', 'sentences', 'letters', 'numbers', 'colors', 'shapes', 'animals', 'body', 'others');
            $keyB = array('words', 'sentences', 'letters', 'songs', 'stories', 'functional');
            if($this->branchObj->group == 30 && strpos($classModel->title, 'IBS') === false){
                foreach($keyB as $key){
                    $data['mik'][$key]=CHtml::encode($model->content->$key);
                }
                $data['mik']['flag'] = 'mik';
                $data['mik']['stat'] = $model->stat_en;
            }
            else{
                foreach($keyA as $key){
                    $data['en'][$key]=CHtml::encode($model->content->{$key.'_en'});
                }
                $data['en']['flag'] = 'en';
                $data['en']['stat'] = $model->stat_en;
            }
            foreach($keyA as $key){
                $data['cn'][$key]=CHtml::encode($model->content->{$key.'_cn'});
            }
            $data['cn']['flag'] = 'cn';
            $data['cn']['stat'] = $model->stat_cn;
        }
        return $data;
    }
}