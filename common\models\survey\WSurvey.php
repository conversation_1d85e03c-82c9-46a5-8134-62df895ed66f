<?php

/**
 * This is the model class for table "ivy_survey".
 *
 * The followings are the available columns in table 'ivy_survey':
 * @property integer $id
 * @property string $template_id
 * @property string $title_cn
 * @property string $title_en
 * @property string $service_year
 * @property string $respondents
 * @property integer $is_anonymous
 * @property string $intro
 * @property integer $status
 * @property integer $update_user
 * @property integer $update_time
 */
class W<PERSON>urvey extends CActiveRecord
{
	// cn en
	public $title = "";
	/**
	 * Returns the static model of the specified AR class.
	 * @return Survey the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_survey';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
				array('intro', 'required'),
				array('is_anonymous, status, update_user, update_time', 'numerical', 'integerOnly'=>true),
				array('template_id, title_cn, title_en', 'length', 'max'=>255),
				array('service_year', 'length', 'max'=>25),
				array('respondents', 'length', 'max'=>6),
				// The following rule is used by search().
				// Please remove those attributes that should not be searched.
				array('id, template_id, title_cn, title_en, service_year, respondents, is_anonymous, intro, status, update_user, update_time', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
				'surveyDetail'=>array(self::HAS_ONE,'WSurveyDetail','survey_id'),
				'fbInfo'=>array(self::HAS_ONE,'WSurveyFeedback',array('survey_id'=>'survey_id'),'through'=>'surveyDetail'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
				'id' => 'ID',
				'template_id' => 'Template',
				'title_cn' => 'Title Cn',
				'title_en' => 'Title En',
				'service_year' => 'Service Year',
				'respondents' => 'Respondents',
				'is_anonymous' => 'Is Anonymous',
				'intro' => 'Intro',
				'status' => 'Status',
				'update_user' => 'Update User',
				'update_time' => 'Update Time',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('template_id',$this->template_id,true);
		$criteria->compare('title_cn',$this->title_cn,true);
		$criteria->compare('title_en',$this->title_en,true);
		$criteria->compare('service_year',$this->service_year,true);
		$criteria->compare('respondents',$this->respondents,true);
		$criteria->compare('is_anonymous',$this->is_anonymous);
		$criteria->compare('intro',$this->intro,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('update_user',$this->update_user);
		$criteria->compare('update_time',$this->update_time);

		return new CActiveDataProvider($this, array(
				'criteria'=>$criteria,
		));
	}

    /**
     * 有效的templateId应该只有一个，由于历史原因，有的调查问卷有多个templateID,
     * 需要去掉值为3和4的ID
     */
    public function getOnlyTemplateId(){
        $templateIds = unserialize($this->template_id);
        $templateIds = array_diff($templateIds, array(3,4));
        $templateId = array_pop($templateIds);
        return $templateId;
    }


    public function getTitle() {
        return (Yii::app()->language == 'zh_cn') ? $this->title_cn : $this->title_en;
    }
}