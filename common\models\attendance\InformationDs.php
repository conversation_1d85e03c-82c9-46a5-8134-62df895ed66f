<?php

/**
 * This is the model class for table "ivy_information_ds".
 *
 * The followings are the available columns in table 'ivy_information_ds':
 * @property integer $id
 * @property string $schoolid
 * @property integer $times
 * @property integer $cagegory
 * @property string $title_en
 * @property string $title_cn
 * @property string $file_en
 * @property string $file_cn
 * @property integer $published
 * @property integer $is_home
 * @property integer $updated
 * @property integer $uid
 */
class InformationDs extends CActiveRecord
{
	public $inform_file_cn;
	public $inform_file_en;
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_information_ds';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('schoolid, cagegory, title_en, title_cn, published, is_home, times, updated, uid', 'required'),
			array('times, cagegory, published, is_home, updated, uid', 'numerical', 'integerOnly'=>true),
			array('schoolid', 'length', 'max'=>30),
			array('title_en, title_cn, file_en, file_cn', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, schoolid, times, cagegory, title_en, title_cn, file_en, file_cn, published, is_home, updated, uid', 'safe', 'on'=>'search'),
			array("inform_file_cn","file", "types" => "pdf", "maxSize" => 1024*1024*10, "allowEmpty" => ($this->isNewRecord || !$this->file_cn) ? false : true,'tooLarge' => '文件大小不能超过 10M'),
			array("inform_file_en","file", "types" => "pdf", "maxSize" => 1024*1024*10, "allowEmpty" => ($this->isNewRecord || !$this->file_cn) ? false : true, 'tooLarge' => '文件大小不能超过 10M'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'schoolid' => Yii::t('campusreport','schoolid'),
			'times' => Yii::t('portfolio','Time'),
			'cagegory' =>Yii::t('labels','Type'),
			'title_en' => Yii::t('curriculum','En Title'),
			'title_cn' => Yii::t('curriculum','Cn Title'),
			'file_en' => Yii::t('curriculum','En File'),
			'file_cn' => Yii::t('curriculum','Cn File'),
			'published' => Yii::t('curriculum','published'),
			'is_home' => Yii::t('curriculum','is_home'),
			'updated' => Yii::t('curriculum','Updated_time'),
			'uid' => Yii::t('labels','Operator'),
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('times',$this->times);
		$criteria->compare('cagegory',$this->cagegory);
		$criteria->compare('title_en',$this->title_en,true);
		$criteria->compare('title_cn',$this->title_cn,true);
		$criteria->compare('file_en',$this->file_en,true);
		$criteria->compare('file_cn',$this->file_cn,true);
		$criteria->compare('published',$this->published);
		$criteria->compare('is_home',$this->is_home);
		$criteria->compare('updated',$this->updated);
		$criteria->compare('uid',$this->uid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return InformationDs the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	protected function beforeSave()
	{
		if (parent::beforeSave()) {
			$file_cn = ($this->file_cn) ? $this->file_cn : "";
			$file_en = ($this->file_en) ? $this->file_en : "";

			if($file = $this->inform_file_cn){
				$picPath = Yii::app()->params['OAUploadBasePath'] . '/information/';
				$picNames = $this->schoolid. '_inform_file_cn' . uniqid() . '.' . $file->getExtensionName();
				if(!is_dir($picPath)){
					mkdir($picPath,0777);
				}
				$file->saveAs($picPath . $picNames);
				if($file_cn){
					unlink($picPath . $file_cn);
				}
				$this->file_cn = $picNames;
			}else{
				$this->file_cn = $this->file_cn;
			}



			if($files = $this->inform_file_en){
				$picPath = Yii::app()->params['OAUploadBasePath'] . '/information/';
				$picNames = $this->schoolid. '_inform_file_en' . uniqid() . '.' . $files->getExtensionName();
				if(!is_dir($picPath)){
					mkdir($picPath,0777);
				}
				$files->saveAs($picPath . $picNames);
				if($file_en){
					unlink($picPath . $file_en);
				}
				$this->file_en = $picNames;
			}else{
				$this->file_en = $this->file_en;
			}

			return true;
		}
		return false;
	}

	public function getName($name = '')
	{
		if($name == 'title'){
			if (Yii::app()->language == 'zh_cn') {
				return $this->title_cn;
			} else {
				return $this->title_en;
			}
		}
		if($name == 'file'){
			$fileName = Yii::app()->language == 'zh_cn' ? $this->file_cn : $this->file_en;
			$fileType = strtolower(substr($fileName, 0, 4));
			if ($fileType == 'http') {
				return $fileName;
			}
			else {
				return Yii::app()->params['uploadBaseUrl'] . 'information/' . $fileName;
			}
		}
	}
}
