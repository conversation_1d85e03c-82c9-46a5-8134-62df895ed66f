<?php

class Weather extends CWidget {
    /*
     * 资源(images css js)的主目录
     */

    public $assetsHomeUrl = null;
    /*
     * 脚本文件 true 为默认 null 为不加载脚本文件
     */
    public $scriptFile = true;
    /*
     * css 文件 true 为默认 null 为不加载css文件
     */
    public $cssFile = true;
    /*
     * 视图文件名称
     */
    public $view = 'weather';

    /**
     * 城市名称信息
     */
    protected $cities = null;

    /*
     * 初始化 设定属性的默认值
     * 此方法会被 CController::beginWidget() 调用
     */

    public function init() {

        Yii::import('ext.weather.include.WeatherConfig');
        Yii::import('ext.weather.class.*');

        if (empty($this->assetsHomeUrl)) {
            $this->assetsHomeUrl = Yii::app()->getAssetManager()->publish(dirname(__FILE__) . '/assets', false, -1, YII_DEBUG);
        }
        /*
          $cs = Yii::app()->getClientScript();

          if($this->scriptFile == true){
          $this->scriptFile = '';
          }
          if($this->cssFile == true){
          $this->cssFile = '';
          }
          if(!empty($this->scriptFile)){
          $cs->registerScriptFile($this->assetsHomeUrl.'/js/'.$this->scriptFile,CClientScript::POS_HEAD);
          }
          if(!empty($this->cssFile)){
          $cs->registerCssFile($this->assetsHomeUrl.'/css/'.$this->cssFile);
          }
         */
    }

    /*
     * 此方法会被 CController::endWidget() 调用
     */

    public function run() {

        $place = 'Beijing';
        $schoolid = $this->getController()->getSchoolId();

        $branch_cri = new CDbCriteria();
        $branch_cri->compare('t.branchid', $schoolid);
        $branch_info = Branch::model()->with('cityInfo')->find($branch_cri);
        if (isset($branch_info->cityInfo->entitle)) {
            $place = ucfirst(strtolower($branch_info->cityInfo->entitle));
        }
        $place = str_replace("'", "", $place);

        $wc = new WeatherConfig();
        $this->cities = $wc->getCitiesInfo();
        $weatherImpl = new WeatherImpl($place);
        $wi = $weatherImpl->getWeather($wc->getWeatherApiForCn(), $wc->getWeatherApiForEn());
        $aq = $weatherImpl->getAirQualityInfo($wc->getAQIApiForCn(), $wc->getAQIApiForEn());

        // 输出 变量到 视图文件
        $view_data = array(
            'place' => $place,
            'wi' => $wi,
            'aq' => $aq,
            'aqiView' => Yii::app()->language == 'en_us' ? strtolower($wc->getWeatherApiForEn()) . 'View' : strtolower($wc->getWeatherApiForCn()) . 'View',
        );
        $this->render($this->view, $view_data);
    }

}