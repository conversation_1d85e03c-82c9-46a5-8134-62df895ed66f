<?php
$medicalCn = '';
$medicalEn = '';
$filedata = '';
$data = '';
if($model){
    $data = json_decode($model->data);
    $filedata = ($data->filedata) ? $data->filedata : '' ;
}
?>
<style>
.container-fluid{
    width:860px;
    margin:0 auto
}
.page{
    margin-top:20px
}
img{
    margin-top:10px;
    max-width:100%;
    height:auto
}
.sign{
    float: right;
    margin-right:20px;
    margin-bottom:20px;
    max-height: 15mm;
}
.sign img{
    width:200px;
    height: 110px;
}
@media print {
    .print_img{
        max-height: 280mm;
        display: block;
        margin: 0 auto;
        page-break-after:always;
    }

    .print_protocol_img
    {
        max-height: 260mm;
        display: block;
        margin: 0 auto;
    }
    .print_sign
    {
        max-height: 100mm;
        display: block;
        margin: 0 auto;
    }
}
</style>
<div class="container-fluid">
    <?php if(isset($step_data['extraInfo']['protocol']) && !empty($step_data['extraInfo']['protocol'])){?>
    <!--    协议-->
    <div class="row">
        <?php $protocol_num = count($step_data['extraInfo']['protocol'])?>
        <?php foreach ($step_data['extraInfo']['protocol'] as $k=>$v){?>
            <div>
                <?php echo $child_info->getChildName() ?>
                <?php echo $child_info->ivyclass->title?>
            </div>
            <p>
                <img  class="img-responsive print_protocol_img" src="<?php echo $v; ?>">
            </p>
            <?php if(($k+1)<$protocol_num){?>
                <div style="page-break-after:always;"></div>
            <?php }?>
            <?php if(($k+1)==$protocol_num){?>
                <?php if(!empty($step_data['childStatus']['sign_url'])){?>
                <div class="sign">
                    <img src="<?php echo $step_data['childStatus']['sign_url']?>" alt="" >
                </div>
                <?php }?>
                <div style="page-break-after:always;"></div>
            <?php }?>
        <?php }?>

    </div>
    <?php }?>
</div>