<?php
 
class Guide extends CWidget {
 
    public $options=array();

    public function init() {
        parent::init();
    }
 
    /**
     * Run the widget, including the js files.
     */
    public function run() {
        if (!isset($this->options['demo']) || !$this->options['demo']){
            $guide = Yii::app()->request->getParam("demo", "");
            if ($guide != "guide"){
                return;
            }
        }
        
        if (isset($this->options['demo']) && $this->options['demo']){
            //Yii::app()->user->hideGuide();
        }
        
        $cs = Yii::app()->clientScript;
 
        $dir = dirname(__FILE__) . DIRECTORY_SEPARATOR;
        $baseUrl = Yii::app()->getAssetManager()->publish($dir . 'assets', false, -1, YII_DEBUG);
 
        foreach($this->options as $key=>$val){
            if ($key == 'guides'){
                foreach ($val as $kn=>$vkn){
                    $this->options[$key][$kn]=$vkn;
                    if(isset($vkn['caption'])){
						$this->options[$key][$kn]['caption']=$this->render($vkn['caption'],null, true);
                    }
                }
            }
        }
        $this->options = CJavaScript::encode($this->options);
 
        $clientScript = Yii::app()->getClientScript();
 
//        $clientScript->registerCssFile($baseUrl . '/guide.css');
        $clientScript->registerCssFile($baseUrl . '/guide.css?t='.Yii::app()->params['refreshAssets']);
 
        $clientScript->registerCoreScript('jquery');
 		
        //$clientScript->registerScriptFile(Yii::app()->theme->baseUrl.'/js/i18n-1.0.js');
        $clientScript->registerScriptFile($baseUrl . '/jquery.guide.js');
 
        $js =  "$(document).ready(function() {
            $.guide(
        $this->options,
                0
            );
        });
        $(window).resize(function() {
            var ind = $.guide.getindex();
            $.guide(
        $this->options,
                ind
            );
        })";
 
        $cs->registerScript('Yii.Guide', $js);
    }
 
}
?>