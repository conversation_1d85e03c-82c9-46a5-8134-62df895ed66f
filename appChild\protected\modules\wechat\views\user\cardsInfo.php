<?php
$form = $this->beginWidget('CActiveForm', array(
    'id' => 'inf1m',
    'htmlOptions' => array('enctype' => 'multipar1a')
));
?>
<div class="weui_cells_title"><?php echo Yii::t('wechat', 'Persons Authorized to Pick Up'); ?></div>
<div class="weui_cells weui_cells_form">
    <div class="weui_cell">
        <div class="weui_cell_bd weui_cell_primary weui_cell_primary">
            <div class="weui_uploader">
                <div class="weui_uploader_bd">
                    <ul class="weui_uploader_files">
                        <li id="ChildProfileBasic_photo" class="weui_uploader_file"
                            style="background-size:100%;background-image:url(<?php echo $parnet['parentPhoto']; ?>)">
                            <div class="weui_uploader_status_content">
                                <!-- <i class="weui_icon_warn" style="display:none"></i> -->
                            </div>
                        </li>
                    </ul>
                    <div class="weui_uploader_input_wrp">
                        <input id="ytChildProfileBasic_uploadPhoto" type="hidden" value=""><input
                            class="weui_uploader_input" name="upload_file" id="ChildProfileBasic_uploadPhoto"
                            type="file"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="weui_cells weui_cells_form">
        <div class="weui_cell">
            <div class="weui_cell_hd"><label class="weui_label"><label class="weui_label"
                                                                       for="ChildProfileBasic_name_cn"><?php echo Yii::t('wechat', 'Parent:') ?></label></label>
            </div>
            <div class="weui_cell_bd weui_cell_primary">
                <input class="weui_input praentName" name="" id="ChildProfileBasic_name_cn" type="text" maxlength="255"
                       value="<?php echo $parnet['parentName'] ?>" <?php echo ($parnet) ? "disabled" : "" ?> >
            </div>
            <div class="weui_cell_ft">
                <i class="weui_icon_warn"></i>
            </div>
        </div>
        <div class="weui_cell">
            <div class="weui_cell_hd"><label class="weui_label"><?php echo Yii::t('wechat', 'Phone:'); ?></label></div>
            <div class="weui_cell_bd weui_cell_primary">
                <input class="weui_input parentTel" type="text" name="" pattern="[0-9]*"
                       value="<?php echo $parnet['parentTel'] ?>" <?php echo ($parnet) ? "disabled" : "" ?> >
            </div>
        </div>
        <div class="weui_cell">
            <div class="weui_cell_hd"><label class="weui_label"><label class="weui_label"
                                                                       for="ChildProfileBasic_first_name_en"><?php echo Yii::t('wechat', 'Relationship:'); ?></label></label>
            </div>
            <div class="weui_cell_bd weui_cell_primary">
                <input class="weui_input praentRelation" name="" type="text" maxlength="255"
                       value="<?php echo $parnet['parentRelation'] ?>" <?php echo ($parnet) ? "disabled" : "" ?>>
            </div>
            <div class="weui_cell_ft">
                <i class="weui_icon_warn"></i>
            </div>
        </div>
        <?php if ($parnet['sign'] === "0" || $parnet['sign'] == 1) { ?>
            <div class="weui_cell">
                <div class="weui_cell_hd"><label class="weui_label"><label class="weui_label"
                                                                           for="ChildProfileBasic_first_name_en"><?php echo Yii::t('wechat', 'Valid:'); ?></label></label>
                </div>
                <div class="weui_cell_bd weui_cell_primary">
                    <input class="weui_switch switchValid" name="sign"
                           value="0" <?php echo ($parnet['sign'] == 0) ? "checked" : "" ?> type="checkbox">
                </div>
                <div class="weui_cell_ft">
                </div>
            </div>
        <?php } ?>

        <input type="hidden" id="fileName" name=""/>

        <?php if (!$parnet): ?>
            <div class="weui_cell" id="fail_Info">
                <div class="weui_cell_bd weui_cell_primary">
                    <p style="color:#E64340"></p>
                </div>
                <!--                    <div class="weui_cell_ft">说明文字</div>-->
            </div>
            <div class="weui_btn_area">
                <a class="weui_btn weui_btn_primary submit" data-prefix="ChildProfileBasic"
                   href="javascript:void(0)"><?php echo Yii::t('user', 'Save') ?></a>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php
$this->endWidget();
?>
<!-- JS -->
<script
    src="<?php echo Yii::app()->theme->baseUrl; ?>/js/lrz/lrz.bundle.js?v1<?php echo Yii::app()->params['refreshAssets']; ?>"></script>
<script>
    $('#ChildProfileBasic_uploadPhoto').change(function () {
        if (this.files[0] == undefined) return;
        if (this.files[0].type != 'image/png' && this.files[0].type != 'image/jpg' && this.files[0].type != 'image/jpeg' && this.files[0].type != 'image/gif')return;
        if (this.files.length === 0) return;
        $('#loadingToast').show();
        $('.weui_uploader_status_content').show();
        $('#ChildProfileBasic_photo').addClass('weui_uploader_status');
        // lrz(file, [options]);
        // file 通过 input:file 得到的文件，或者直接传入图片路径
        // [options] 这个参数允许忽略
        // width {Number} 图片最大不超过的宽度，默认为原图宽度，高度不设时会适应宽度。
        // height {Number} 同上
        // quality {Number} 图片压缩质量，取值 0 - 1，默认为0.7
        lrz(this.files[0], {width: 600, height: 600, quality: 1})
            .then(function (rst) {
                // 处理成功会执行
                $('#ChildProfileBasic_photo').css('background-image', 'url(' + rst.base64 + ')');
                upload(rst.origin);
            })
            .catch(function (err) {
                // 处理失败会执行
                $('#loadingToast').hide();
            })
            .always(function () {
                // 不管是成功失败，都会执行
            });
    });
    //上传图片
    function upload(file) {
        formData = new FormData();
        formData.append('upload_file', file);
        $('#loadingToast').show();
        $.ajax({
            type: 'POST',
            url: '<?php echo $this->createUrl('cardFiles', array('cardId'=> ($parnet) ? $parnet['cardId'] : "0")); ?>',
            dataType: 'json',
            data: formData,
            contentType: false,
            processData: false,
            xhr: function () {
                var xhr = $.ajaxSettings.xhr();
                //绑定上传进度的回调函数
                　　xhr.upload.onprogress = function (e) {
                    　　　　if (e.lengthComputable) {
                    　　　　　　 var complete = (e.loaded / e.total * 100 | 0);
                        progress = $('.weui_uploader_status_content');
                        pecent = ~~(100 * e.loaded / e.total);
                        progress.html(pecent - 1 + "%");
                    }
                };
                return xhr;
            },
            success: function (data) {
                if (data && data.state == 'success') {
                    var _str = data.data.photoUrl;
                    var ret = _str.split("crads/")[1];
                    $('#fileName').val(ret);
                    $('#ChildProfileBasic_photo').removeClass('weui_uploader_status');
                    $('.weui_uploader_status_content').hide();
                    showMessage();
                } else {
                    alert(data.data)
                    $('.weui_uploader_status_content').html('<i class="weui_icon_warn"></i>');
                }
            },
            error: function () {
                alert('上传error')
                $('.weui_uploader_status_content').html('<i class="weui_icon_warn"></i>');
            },
            complete: function () {
                $('#loadingToast').hide();
            }
        });
    }
    $('.switchValid').on('click', function () {
        var _check;
        if ($(this).is(':checked')) {
            _check = 0;
        } else {
            _check = 1;
        }
        $('#loadingToast').show();
        $.ajax({
            type: "POST",
            timeout: 5000,
            url: "<?php echo $this->createUrl('updateSign');?>",
            data: {sign: _check, cardId:<?php echo ($parnet) ? $parnet['cardId'] : "0" ?>},
//            data: {sign: _check, cardId:<?php //echo ($parnet) ? 1 : "0" ?>//},
            dataType: 'json',
            success: function (data) {
                $('#loadingToast').hide();
                if (data.state == 'success') {
                    $('#toast').show();
                    setTimeout(function () {
                        $('#toast').hide();
                    }, 2000);
                } else {

                }
            }, error: function (data) {
                alert('error');
                $('#loadingToast').hide();
            }
        });
    });
    $('.submit').on('click', function () {
        var _praentName = $('.praentName').val();
        var _parentTel = $('.parentTel').val();
        var _praentRelation = $('.praentRelation').val();
        var _parentPhoto = $('#fileName').val();
        $('#loadingToast').show();
        $.ajax({
            type: 'POST',
            dataType: 'json',
            data: {praentName: _praentName, parentTel:_parentTel, praentRelation: _praentRelation, parentPhoto: _parentPhoto},
            timeout: 5000,
            success: function (data) {
                if (data.state == 'success') {
                    $('#fail_Info').hide();
                    $('#loadingToast').hide();
                    $('#toast').show();
                    setTimeout(function () {
                        $('#toast').hide();
                    }, 2000);
                    location.href = '<?php echo $this->createUrl('cards');?>';
                }
                if (data.state == 'fail') {
                    $('#fail_Info').show().find('p').text(data.message);
                    $('#loadingToast').hide();
                }
            },
            error: function () {
                alert('error')
            },
            complete: function () {
                $('#loadingToast').hide();
            }
        });
    });

//    var submitFail = function (msg) {
//    };
//    var submitSuccess = function () {
//        history.go(-1);
//        location.href = '<?php //echo $this->createUrl('cards');?>//';
//    }
</script>