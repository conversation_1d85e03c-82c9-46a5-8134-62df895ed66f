<?php $form=$this->beginWidget('CActiveForm', array(
    'id'=>'schools-form',
    'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm'),
)); ?>
<div class="pop_cont">
    <div class="double_list">
        <ul class="cc J_check_wrap">
            <li class="all_check">
                <label>
                    <input type="checkbox" data-checklist="J_check_c1" class="J_check_all" data-direction="y" />
                    <span>全部</span>
                </label>
            </li>
            <?php foreach ($branchList as $bid=>$branch):?>
            <li>
                <label>
                    <input type="checkbox" data-yid="J_check_c1" class="J_check" name="school[<?php echo $bid;?>]" value="<?php echo $branch['title']?>" <?php if ($branch['selected']):?>checked='checked'<?php endif;?>/>
                    <span><?php echo $branch['title']?></span>
                </label>
            </li>
            <?php endforeach;?>
        </ul>
    </div>
</div>
<div id="J_submit_tips"></div>
<div class="pop_bottom tar">
    <button type="submit" class="btn btn_submit mr10 J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
    <button id="J_dialog_close" type="button" class="btn btn_cancel"><?php echo Yii::t('global','Cancel');?></button>
</div>
<?php $this->endWidget(); ?>
<script type="text/javascript">
function callback(data)
{
    data = eval('('+data+')');
    $(parent.document.getElementById('adm_'+data.uid)).html(data._data);
    window.parent.head.dialog.closeAll();
}
</script>