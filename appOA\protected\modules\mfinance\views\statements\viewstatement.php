<?php
$payType = array(
    'alipay' => '网银金额（支付宝）',
    'wechatpay' => '微信支付',
    'allinpay' => 'POS支付（通联）',
    'allinpayOnline' => '网银支付（通联）',
    'yeepay' => 'POS支付（易宝）',
);
$amountAbb = array();
foreach ($data as $abb => $datum):
    ?>
    <div class="panel panel-default" id="workflow-modal-body<?php echo $abb ?>">
        <div class="panel-heading">
            <?php echo $schools[$abb]['abb'];?>
            <span id="amount_<?php echo $abb?>" style="cursor: pointer" class="ml20" title="订单总额"></span>
            <?php if($type == 'wechatpay'): ?>
                <span class="ml20" style="cursor: pointer" title="银行到账金额"> <?php echo ($datum['actual']) ? '银行到账金额: ' . $datum['actual']: '' ?></span>
                <span class="ml20" style="cursor: pointer" title="手续费"><?php echo ($datum['handling_amount']) ? '手续费: ' . $datum['handling_amount']: '' ?></span>
            <?php endif; ?>
        <span class="pull-right noPrint">
            [<?php echo CHtml::link('导出', array('export', 'id' => $id, 'schoolid' => $abb, 'type' => $type));?>]
            [<a href="javascript:;" class="print" _value="<?php echo $abb ?>">打印</a>]
        </span>
        </div>
        <div class="panel-body" >
            <?php foreach ($datum['order'] as $key => $order) { ?>
                <h4>订单号：<?php echo $datum['orderid'][$key]; ?>
                    ；孩子名字：<?php echo (!$order->ChildProfile && count($order->items) == 1) ? $child_name_t[$order->items[0]->invoice->admission_id] : $order->ChildProfile->getChildName(); ?>
                    ；时间：<?php echo $type == 'alipay' || $type == 'wechatpay' || $type == 'allinpayOnline' ? date('Y-m-d H:i:s', $order->update_timestamp) : date('Y-m-d H:i:s', $order->updateTime) ?></h4>
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center" width="178">账单标题</th>
                            <th class="text-center" width="178">账单金额</th>
                            <th class="text-center" width="178"><?php echo $payType[$type]; ?></th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php foreach ($order->items as $item): ?>
                            <tr>
                                <td><?php echo CHtml::link($item->invoice->title, array('/child/invoice/viewInvoice', 'invoiceid' => $item->invoice->invoice_id, 'childid' => $order->ChildProfile->childid), array('target' => '_blank')); ?></td>
                                <td class="text-right"><?php echo $item->invoice->amount; ?></td>
                                <td class="text-right">
                                    <?php
                                    $tAmount = 0;
                                    // foreach($item->invoice->invoiceTransaction as $tran){
                                    //     if($type == 'alipay' && $tran->transactiontype == InvoiceTransaction::TYPE_ONLINEPAYMENT){
                                    //         $tAmount += $tran->amount;
                                    //     }
                                    //     elseif($type != 'alipay' && ($tran->transactiontype == InvoiceTransaction::TYPE_POS || $tran->transactiontype == InvoiceTransaction::TYPE_POS_ALLINPAY)){
                                    //         $tAmount += $tran->amount;
                                    //     }
                                    //     elseif($type == 'wechatpay' && ($tran->transactiontype == InvoiceTransaction::TYPE_WX_NATIVE || $tran->transactiontype == InvoiceTransaction::TYPE_WX_MICROPAY)){
                                    //         $tAmount += $tran->amount;
                                    //     }
                                    //     elseif($type == 'allinpayOnline' && ($tran->transactiontype == InvoiceTransaction::TYPE_ONLINE_ALLINPAY)){
                                    //         $tAmount += $tran->amount;
                                    //     }
                                    // }
                                    // $amountAbb[$abb] += $tAmount;

                                    if ($type == 'yeepay' || $type == 'allinpay' || $type == 'allinpayOnline') {
                                        $tAmount = $item->payable_amount;
                                    } elseif ($type == 'wechatpay' || $type == 'alipay') {
                                        $tAmount = $item->amount;
                                    }
                                    $amountAbb[$abb] += $tAmount;
                                    echo number_format($tAmount, 2);
                                    ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php }?>
        </div>
    </div>
<?php endforeach; ?>

<script>
    var amountAbb = <?php echo CJSON::encode($amountAbb);?>;
    for (var i in amountAbb) {
        $('#amount_' + i).text('订单总额: ' + amountAbb[i]);
    }
    $(".print").click(function () {
        var _value = $(this).attr('_value');
        $("#workflow-modal-body" + _value + " a").removeAttr('href');
        $("#workflow-modal-body" + _value).find('.noPrint').hide();
        $("#workflow-modal-body" + _value).printThis();
        setTimeout(function(){
            $('.noPrint').show();
        },1000)
    })
</script>
