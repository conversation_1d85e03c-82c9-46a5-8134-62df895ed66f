<?php 
    // 由于微信支付安全目录问题，特殊处理支付页面的跳转URL
    $payMentUrl = Yii::app()->createUrl('wechat/user/unpaidInvoices')
        . '/?openid=' . $_GET['openid']
        . '&access_token=' . $_GET['access_token']
        . '&state=' . $_GET['state'];
 ?>

<?php if($result == 'success'): ?>
<div class="weui_msg">
    <div class="weui_icon_area">
        <i class="weui_icon_success weui_icon_msg"></i>
    </div>
    <div class="weui_text_area">
        <h2 class="weui_msg_title"><?php echo Yii::t('payment', 'Payment successful!'); ?></h2>
    </div>
    <div class="weui_opr_area">
        <p class="weui_btn_area">
            <?php echo CHtml::link(Yii::t('wechat', 'Continue'), $payMentUrl, array('class'=>'weui_btn weui_btn_primary'))?>
            <?php echo CHtml::link(Yii::t('wechat', 'Close'), 'javascript:wx.closeWindow();', array('class'=>'weui_btn weui_btn_default'))?>
        </p>
    </div>
</div>
<?php else: ?>
<div class="weui_msg">
    <div class="weui_icon_area">
        <i class="weui_icon_msg weui_icon_warn"></i>
    </div>
    <div class="weui_text_area">
        <h2 class="weui_msg_title"><?php echo Yii::t('wechat', 'Payment Failed'); ?></h2>
    </div>
    <div class="weui_opr_area">
        <p class="weui_btn_area">
            <?php echo CHtml::link(Yii::t('wechat', 'Continue'), $payMentUrl, array('class'=>'weui_btn weui_btn_primary'))?>
            <?php echo CHtml::link(Yii::t('wechat', 'Close'), 'javascript:wx.closeWindow();', array('class'=>'weui_btn weui_btn_default'))?>
        </p>
    </div>
</div>
<?php endif; ?>