<?php

class PtcController extends TeachBasedController
{
    public $yid;
    public $userid;

    public function createUrlReg($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        if (empty($params['yid'])) {
//            $params['yid'] = $this->yid;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function beforeAction($action)
    {
        parent::beforeAction($action);
        // 初始化通用数据
        $this->yid = Yii::app()->request->getParam('yid');
        if (!$this->yid) {
            $this->getCalendars();
            // 默认下学年的yid
            if (isset($this->calendarYids['nextYid'])) {
                $this->yid = $this->calendarYids['nextYid'];
            } else {
                $this->yid = $this->calendarYids['currentYid'];
            }
        }
        return true;
    }

    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'teaching';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Teaching Tasks');

        //初始化选择校园页面
        $this->branchSelectParams['hideKG'] = true;
        $this->branchSelectParams['urlArray'] = array('//mteaching/ptc/index', 'type' => $_GET['type']);
        $this->userid = Yii::app()->user->getId();
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl() . '/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/vue2.js');

        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/css/iconfont.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/v-calendar/vue.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/v-calendar/v-calendar.umd.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/backbone-min.js');
        $cs->registerCssFile(Yii::app()->theme->baseUrl . '/css/calendar.css');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/xlsx.full.min.js');
    }

    public function actionIndex()
    {
        $this->render('index');
    }

    public function actionMyPtc()
    {
        $this->render('myPtc');
    }

    public function actionGetHomePageData()
    {
        $yid = Yii::app()->request->getParam("input_yid");
        $semester = Yii::app()->request->getParam("input_semester");
        $teacher_id = Yii::app()->request->getParam("teacher_id");
        $res = CommonUtils::requestDsOnline('ptc/getHomePageData', array(
            'school_id' => $this->branchId,
            'teacher_id' => empty($teacher_id) ? $this->userid : $teacher_id,
            'input_yid' => $yid,
            'input_semester' => $semester,
        ));
        if ($res['code'] == 0) {
            parent::initExt();
            //当前学年
            $currentYid = $res['data']['year_list']['currentYid'];
            $select_year = $res['data']['year_list']['select_year'];
            $yid = array_search($select_year,$res['data']['year_list']['startYearList']);
            $crit = new CDbCriteria();
            $crit->compare('schoolid', $this->branchId);
            $crit->compare('yid', $yid);
            $crit->compare('teacherid', Yii::app()->user->getId());
            $crit->compare('ishelpteacher', 1);
            $res['data']['showPtcSet'] = ClassTeacher::model()->exists($crit);
            if ($this->myClasses) {
                $newClassList = array();
                $select_year = $res['data']['year_list']['select_year'];
                $current_year = $res['data']['year_list']['startYearList'][$currentYid];
                if($select_year == $current_year){
                    foreach ($res['data']['class_list'] as $item) {
                        if (in_array($item['id'], $this->myClasses)) {
                            $newClassList[] = $item;
                        }
                    }
                }else{
                    foreach ($res['data']['class_list'] as $item) {
                        if (in_array($item['id'], $res['data']['schedule_teacher_history'])) {
                            $newClassList[] = $item;
                        }
                    }
                }
                $res['data']['class_list'] = $newClassList;
            }
            //it和助教,校长可以查看 ptc设置
            if(Yii::app()->user->checkAccess('ivystaff_it') || Yii::app()->user->checkAccess('ivystaff_cd')){
                $res['data']['showPtcSet'] = true;
            }
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionGetStudentListByClass()
    {
        $class_id = Yii::app()->request->getParam("class_id");
        $start_year = Yii::app()->request->getParam("start_year");
        $semester = Yii::app()->request->getParam("semester");
        $ptc_subject = Yii::app()->request->getParam("ptc_subject");
//        $teacher_id = Yii::app()->request->getParam("teacher_id");
        $res = CommonUtils::requestDsOnline('ptc/getStudentListByClass', array(
            'school_id' => $this->branchId,
            'class_id' => $class_id,
            'start_year' => $start_year,
            'semester' => $semester,
            'teacher_id' => $this->userid,
            'ptc_subject' => $ptc_subject,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionGetStudentListByCourse()
    {
        $yid = Yii::app()->request->getParam("yid");
        $course_id = Yii::app()->request->getParam("course_id");
        $start_year = Yii::app()->request->getParam("start_year");
        $semester = Yii::app()->request->getParam("semester");
        $res = CommonUtils::requestDsOnline('ptc/getStudentListByCourse', array(
            'school_id' => $this->branchId,
            'yid' => $yid,
            'course_id' => $course_id,
            'start_year' => $start_year,
            'semester' => $semester,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionCreateStudentPtc()
    {
        $child_info = Yii::app()->request->getParam("child_info");
        $class_id = Yii::app()->request->getParam("class_id");
        $start_year = Yii::app()->request->getParam("start_year");
        $semester = Yii::app()->request->getParam("semester");
        $ptc_subject = Yii::app()->request->getParam("ptc_subject");
        $res = CommonUtils::requestDsOnline('ptc/createStudentPtc', array(
            'child_info' => $child_info,
            'class_id' => $class_id,
            'teacher_id' => $this->userid,
            'start_year' => $start_year,
            'semester' => $semester,
            'school_id' => $this->branchId,
            'ptc_subject' => $ptc_subject,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionGetPtcByClass()
    {
        $class_id = Yii::app()->request->getParam("class_id");
        $start_year = Yii::app()->request->getParam("start_year");
        $semester = Yii::app()->request->getParam("semester");
        $teacher_id = Yii::app()->request->getParam("teacher_id");//选择的老师id
        $ptc_subject = Yii::app()->request->getParam("ptc_subject");//选择的老师id
        $res = CommonUtils::requestDsOnline('ptc/getPtcByClass', array(
            'class_id' => $class_id,
            'teacher_id' => $teacher_id,
            'start_year' => $start_year,
            'semester' => $semester,
            'school_id' => $this->branchId,
            'user_id' => $this->userid,
            'ptc_subject' => $ptc_subject,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionGetPtcByCourse()
    {
        $course_id = Yii::app()->request->getParam("course_id");
        $course_code = Yii::app()->request->getParam("ptc_subject");
        $start_year = Yii::app()->request->getParam("start_year");
        $semester = Yii::app()->request->getParam("semester");
        $yid = Yii::app()->request->getParam("yid");
        $teacher_id = Yii::app()->request->getParam("teacher_id");
        $res = CommonUtils::requestDsOnline('ptc/getPtcByCourse', array(
            'school_id' => $this->branchId,
            'teacher_id' => empty($teacher_id)?$this->userid:$teacher_id,
            'course_id' => $course_id,
            'yid' => $yid,
            'start_year' => $start_year,
            'semester' => $semester,
            'course_code' => $course_code,
            'user_id'=>$this->userid,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionSaveRemarkData()
    {
        $child_info = Yii::app()->request->getParam("child_info");
        $start_year = Yii::app()->request->getParam("start_year");
        $semester = Yii::app()->request->getParam("semester");
        $ptc_subject = Yii::app()->request->getParam("ptc_subject");
        $res = CommonUtils::requestDsOnline('ptc/saveRemarkData', array(
            'start_year' => $start_year,
            'semester' => $semester,
            'school_id' => $this->branchId,
            'teacher_id' => $this->userid,
            'child_info' => $child_info,
            'ptc_subject' => $ptc_subject,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    //添加成绩页面展示成绩
    public function actionGetScoreData()
    {
        $child_info = Yii::app()->request->getParam("child_info");
        $start_year = Yii::app()->request->getParam("start_year");
        $semester = Yii::app()->request->getParam("semester");
        $ptc_subject = Yii::app()->request->getParam("ptc_subject", '');#小学的必传
        $res = CommonUtils::requestDsOnline('ptc/getScoreData', array(
            'child_info' => $child_info,
            'start_year' => $start_year,
            'semester' => $semester,
            'school_id' => $this->branchId,
            'teacher_id' => $this->userid,
            'ptc_subject' => $ptc_subject,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionSaveScoreData()
    {
        $child_info = Yii::app()->request->getParam("child_info");
        $start_year = Yii::app()->request->getParam("start_year");
        $semester = Yii::app()->request->getParam("semester");
        $res = CommonUtils::requestDsOnline('ptc/saveScoreData', array(
            'child_info' => $child_info,
            'start_year' => $start_year,
            'semester' => $semester,
            'school_id' => $this->branchId,
            'teacher_id' => $this->userid,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionDelPtcItem()
    {
        $start_year = Yii::app()->request->getParam("start_year");
        $semester = Yii::app()->request->getParam("semester");
        $child_id = Yii::app()->request->getParam("child_id");
        $subject = Yii::app()->request->getParam("subject");
        $res = CommonUtils::requestDsOnline('ptc/delPtcItem', array(
            'school_id' => $this->branchId,
            'teacher_id' => $this->userid,
            'start_year' => $start_year,
            'semester' => $semester,
            'child_id' => $child_id,
            'subject' => $subject,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionSubjectStatus()
    {
        $class_id = Yii::app()->request->getParam("class_id");
        $start_year = Yii::app()->request->getParam("start_year");
        $semester = Yii::app()->request->getParam("semester");
        $res = CommonUtils::requestDsOnline('ptc/subjectStatus', array(
            'start_year' => $start_year,
            'class_id' => $class_id,
            'semester' => $semester,
            'school_id' => $this->branchId,
            'teacher_id' => $this->userid,
        ));
        if ($res['code'] == 0) {
            $res['data']['user_id'] = $this->userid;
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionOperateSubjectStatus()
    {
        $class_id = Yii::app()->request->getParam("class_id");
        $start_year = Yii::app()->request->getParam("start_year");
        $semester = Yii::app()->request->getParam("semester");
        $subject_id = Yii::app()->request->getParam("subject_id");
        $teacher_id = Yii::app()->request->getParam("teacher_id");
        $res = CommonUtils::requestDsOnline('ptc/operateSubjectStatus', array(
            'class_id' => $class_id,
            'teacher_id' => empty($teacher_id) ? $this->userid : $teacher_id,
            'start_year' => $start_year,
            'semester' => $semester,
            'school_id' => $this->branchId,
            'subject_id' => $subject_id,
            'user_id' => $this->userid,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    //认领科目和添加学生一起操作
    public function actionOperateSubjectStatus2()
    {
        $class_id = Yii::app()->request->getParam("class_id");
        $start_year = Yii::app()->request->getParam("start_year");
        $semester = Yii::app()->request->getParam("semester");
        $subject_id = Yii::app()->request->getParam("subject_id");
        $teacher_id = Yii::app()->request->getParam("teacher_id");
        $child_info = Yii::app()->request->getParam("child_info");
        $res = CommonUtils::requestDsOnline('ptc/operateSubjectStatus2', array(
            'class_id' => $class_id,
            'teacher_id' => empty($teacher_id) ? $this->userid : $teacher_id,
            'start_year' => $start_year,
            'semester' => $semester,
            'school_id' => $this->branchId,
            'subject_id' => $subject_id,
            'user_id' => $this->userid,
            'child_info' => $child_info,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }


    public function actionSaveSubjectStatus()
    {
        $id = Yii::app()->request->getParam("id");
        $res = CommonUtils::requestDsOnline('ptc/saveSubjectStatus', array(
            'id' => $id,
            'user_id' => $this->userid,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('data', $res['data']);
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionDelSubjectStatus()
    {
        $id = Yii::app()->request->getParam("id");
        $res = CommonUtils::requestDsOnline('ptc/delSubjectStatus', array(
            'id' => $id,
            'user_id' => $this->userid,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }
    //获取模板
    public function actionStepTemplate()
    {
        $startyear = Yii::app()->request->getParam("startyear");
        $semester = Yii::app()->request->getParam("semester");
        $pageType = Yii::app()->request->getParam("pageType");
        $yid = Yii::app()->request->getParam("yid");

        if(empty($startyear)){
            $yid = $this->branchObj->schcalendar;//当前学年yid
            $crit = new CDbCriteria();
            $crit->compare('branchid', $this->branchId);
            $crit->compare('yid', $yid);
            $calendarSchoolData = CalendarSchool::model()->find($crit);
            $startyear = $calendarSchoolData->startyear;
        }
        if(empty($semester)){
            $semester = $this->branchObj->semester;
        }
        $requestData = array(
            'startyear' => $startyear,
            'semester' => $semester,
            'school_id' => $this->branchId,
        );

        $res = CommonUtils::requestDsOnline('ptc/schedule/classList', $requestData);
        if (!isset($res['code'])) {
            $this->addMessage('message', 'api error');
            $this->showMessage();
        }
        if ($res['code'] != 0) {
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
        $data['showPtcSet'] = false;
        $crit = new CDbCriteria();
        $crit->compare('schoolid', $this->branchId);
        $crit->compare('yid', $yid);
        $crit->compare('teacherid', Yii::app()->user->getId());
        $crit->compare('ishelpteacher', 1);
        $classTeacherData = ClassTeacher::model()->findAll($crit);
        $help_class_id_list = array();//助教班级id
        if($classTeacherData){
            $data['showPtcSet'] = true;
            foreach ($classTeacherData as $item2){
                $help_class_id_list[] = $item2->classid;
            }
        }
        if(Yii::app()->user->checkAccess('ivystaff_it')){
            $data['showPtcSet'] = true;
        }
        $class_list = $res['data'];
        if($res['code'] == 0){
            parent::initExt();
            if ($this->myClasses) {
                $newClassList = array();
                $select_year = $res['data']['year_list']['select_year'];
                $current_year = $res['data']['year_list']['startYearList'][$this->branchObj->schcalendar];
                //只展示助教的班级
                if($select_year == $current_year){
                    foreach ($res['data'] as $item) {
                        if (in_array($item['id'], $this->myClasses) && in_array($item['id'],$help_class_id_list)) {
                            $newClassList[] = $item;
                        }
                    }
                }else{
                    foreach ($res['data'] as $item) {
                        if (in_array($item['id'], $res['data']['schedule_teacher_history']) && in_array($item['id'],$help_class_id_list)) {
                            $newClassList[] = $item;
                        }
                    }
                }
                $class_list = $newClassList;
            }
        }
        $data['template'] = $this->renderPartial($pageType,array(
            'yid'=>$startyear,
            'startyear'=>$startyear,
            'semester'=>$semester,
            'class_list'=>$class_list,
        ),true);
        $this->addMessage('state', 'success');
        $this->addMessage('data', $data);
        $this->showMessage();
    }

    protected $startYear;
    protected $semester;
    protected $startYearList = array();
    protected $semesterList = array();
    protected $manageAccess = false;

    public function createUrlPtc($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        if (empty($params['startyear'])) {
            $params['startyear'] = $this->startYear;
        }
        if (empty($params['semester'])) {
            $params['semester'] = $this->semester;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function initPtc()
    {
        $this->getCalendars();
        $this->yid = $this->calendarYids['currentYid'];
        if ($startYear = Yii::app()->request->getParam('startyear')) {
            $this->startYear = $startYear;
        } else {
            // 获取当前校历的开始年
            $this->startYear = current($this->calendarStartYear);
        }
        if ($semester = Yii::app()->request->getParam('semester')) {
            $this->semester = $semester;
        } else {
            // 指定学期
            $res = CalendarSemester::model()->getSemesterTimeStamp($this->yid);
            $this->semester = 10;
            if (time() > $res['spring_start']) {
                $this->semester = 20;
            }
        }

        $this->semesterList = array(
            10 => Yii::t('labels', '1st Semester'),
            20 => Yii::t('labels', '2nd Semester'),
        );
        $start = 2021;
        while ($start <= current($this->calendarStartYear)) {
            $next = $start + 1;
            $this->startYearList[$start] = "$start - $next";
            $start++;
        }

        // 是否具有管理权限
        if (Yii::app()->user->checkAccess('ivystaff_it') || Yii::app()->user->checkAccess('ivystaff_opschool')) {
            $this->manageAccess = true;
        }
    }

    // 中学已分配数据
    public function actionAssign()
    {
        $this->initPtc();
        $this->branchSelectParams['urlArray'] = array('//mteaching/ptc/assign');
        $requestData = array(
            'startyear' => $this->startYear,
            'semester' => $this->semester,
            'school_id' => $this->branchId,
            'day' => Yii::app()->request->getParam('day'),
        );
        $res = CommonUtils::requestDsOnline('ptc/schedule/assignChildData', $requestData);
        $data = array();
        if ($res['code'] == 0) {
            $data = $res['data'];
        }
        $opAuth = Yii::app()->user->checkAccess('ivystaff_opschool');
        $this->render('assignRead', array('data' => $data, 'opAuth' => $opAuth));
    }

    public function actionGetClassPlanData(){
        $class_id = Yii::app()->request->getParam('class_id');
        $start_year = Yii::app()->request->getParam('start_year');
        $semester = Yii::app()->request->getParam('semester');
        $teacher_id = Yii::app()->request->getParam('teacher_id');
        $school_id = $this->branchId;
        $ptc_subject = Yii::app()->request->getParam('ptc_subject');
        $res = CommonUtils::requestDsOnline('ptc2/getClassPlanData',array(
            'start_year'=>$start_year,
            'class_id'=>$class_id,
            'semester'=>$semester,
            'teacher_id'=>$teacher_id,
            'school_id'=>$school_id,
            'ptc_subject'=>$ptc_subject,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionAutoCompleteMapData()
    {
        $start_year = Yii::app()->request->getParam('start_year');
        $semester = Yii::app()->request->getParam('semester');
        $school_id = $this->branchId;
        $ptc_subject = Yii::app()->request->getParam('ptc_subject');
        $child_ids = Yii::app()->request->getParam('child_ids');
        $class_id = Yii::app()->request->getParam('class_id');
        $res = CommonUtils::requestDsOnline('ptc2/autoCompleteMapData',array(
            'start_year'=>$start_year,
            'semester'=>$semester,
            'school_id'=>$school_id,
            'child_ids'=>$child_ids,
            'ptc_subject'=>$ptc_subject,
            'class_id'=>$class_id,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionSavePlanData()
    {
        $class_id = Yii::app()->request->getParam('class_id');
        $start_year = Yii::app()->request->getParam('start_year');
        $semester = Yii::app()->request->getParam('semester');
        $child_data = Yii::app()->request->getParam('child_data');
        $school_id = $this->branchId;
        $ptc_subject = Yii::app()->request->getParam('ptc_subject');
        $res = CommonUtils::requestDsOnline('ptc2/savePlanData',array(
            'start_year'=>$start_year,
            'class_id'=>$class_id,
            'semester'=>$semester,
            'child_data'=>$child_data,
            'school_id'=>$school_id,
            'ptc_subject'=>$ptc_subject,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        }
    }

    //导出数据和下载模板
    public function actionDownloadTemplate()
    {
        //请求数据
        $class_id = Yii::app()->request->getParam('class_id');
        $start_year = Yii::app()->request->getParam('start_year');
        $semester = Yii::app()->request->getParam('semester');
        $teacher_id = Yii::app()->request->getParam('teacher_id');
        $school_id = $this->branchId;
        $ptc_subject = Yii::app()->request->getParam('ptc_subject');
        $is_template = Yii::app()->request->getParam('is_template',1);
        $res = CommonUtils::requestDsOnline('ptc2/getClassPlanData',array(
            'start_year'=>$start_year,
            'class_id'=>$class_id,
            'semester'=>$semester,
            'teacher_id'=>$teacher_id,
            'school_id'=>$school_id,
            'ptc_subject'=>$ptc_subject,
        ));
        //按照拼音排序
        $child_list_new = array();
        $fengeName = Yii::createComponent('common.extensions.pinyin.Utf8ToPinyin');
        $pinyin = Yii::createComponent('common.components.pinyin.PinyinTool');
        foreach ($res['data']['child_list_by_id'] as $key => $item) {
            $childName = $fengeName->splitName($item['name']);
            $pinyinName = $pinyin->tool->sentence($childName[0]).' '.str_replace(' ', '', $pinyin->tool->sentence($childName[1]));
            $new_array[$key] = $pinyinName;
        }
        asort($new_array);
        foreach ($new_array as $child_id => $name) {
            $child_list_new[] = $res['data']['child_list_by_id'][$child_id];
        }
        spl_autoload_unregister(array('YiiBase','autoload'));
        Yii::import('ext.PHPExcel.PHPExcel',true);
        spl_autoload_register(array('YiiBase','autoload'));
        $file_path_base = dirname(__FILE__).'/../excel/';
        $res_file = $res['data']['template_file'];
        if(empty($res_file)){
            return '';
        }
        $file = $file_path_base.'/'.$res_file;
        $fileType = pathinfo($file, PATHINFO_EXTENSION);
        //根据文件类型，加载不同类库
        switch ($fileType) {
            case 'xls':
                $type = 'Excel5';
                break;
            case 'xlsx':
                $type = 'Excel2007';
                break;
        }
        $objReader = \PHPExcel_IOFactory::createReader($type);
        $objPHPExcel =$objReader->load($file, $encode = 'utf-8'); // 加载文件
        //将活动工作表索引设置为第一个工作表：
        //获取活动工作表
        $worksheet = $objPHPExcel->getActiveSheet();
        //从第二行开始填充数据
        $row = 2;
        foreach ($child_list_new as $child_info){
            $worksheet->getCell('A'.$row)->setValue($child_info['id']); //id
            $worksheet->getCell('B'.$row)->setValue($child_info['bilingual_name']);//姓名
            $worksheet->getCell('C'.$row)->setValue($res['data']['class_title']);//班级
            //对齐方式
            $worksheet->getStyle('B'.$row)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_LEFT);
            $worksheet->getStyle('C'.$row)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_LEFT);
            if($row % 2 == 0){
                //背景色
                $worksheet->getStyle('A'.$row)->getFill()->setFillType(\PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB("D9D9D9");
                $worksheet->getStyle('B'.$row)->getFill()->setFillType(\PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB("D9D9D9");
                $worksheet->getStyle('C'.$row)->getFill()->setFillType(\PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB("D9D9D9");
            }

            foreach ($res['data']['template_title'] as $key=>$v){
                if($key!=='plan'){
                    //对齐方式
                    $worksheet->getStyle($v.$row)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_LEFT);
                    if($row % 2 == 0){
                        //设置背景色
                        $worksheet->getStyle($res['data']['template_title'][$key].$row)->getFill()->setFillType(\PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB("D9D9D9");
                    }
                }

                if(!$is_template){
                    if($row % 2 == 0){
                        //设置背景色
                        $worksheet->getStyle($res['data']['template_title'][$key].$row)->getFill()->setFillType(\PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB("D9D9D9");
                    }
                }
            }
            //导出数据需要用到其他列
            $excel_text = array(
                'ON LEVEL'=>'学习拓展',
                'SUPPORT'=>'学习支持'
            );
            if(!$is_template){
                foreach ($res['data']['ptc_plan_data'][$child_info['id']]['score'] as $key=>$value){
                    if($key === 'supportOrOnLevel'){
                        //语文和1-3年级数学导出数据使用中文
                        if($ptc_subject == 1 || ($ptc_subject == 2 && in_array($res['data']['classtype'],array('mk','e1','e2','e3')))){
                            $value = $excel_text[$value];
                        }
                    }
                    $worksheet->getCell($res['data']['template_title'][$key].$row)->setValue($value);
                }
                //导出数据增加提升计划
                if(!empty($res['data']['template_title']['plan'])){
                    $plan_comment = $res['data']['ptc_plan_data'][$child_info['id']]['comment'];//提升计划
                    $worksheet->getCell($res['data']['template_title']['plan'].'1')->setValue(Yii::t('ptc','Improvement Plan'));
                    $worksheet->getStyle($res['data']['template_title']['plan'].'1')->getFill()->setFillType(\PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB("92D050");
                    $worksheet->getStyle($res['data']['template_title']['plan'].'1')->getFont()->getColor()->setRGB("FFFFFF");//白色
                    $worksheet->getStyle($res['data']['template_title']['plan'].'1')->getFont()->setBold(true);//加粗
                    $worksheet->getStyle($res['data']['template_title']['plan'].'1')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);//水平居中
                    $worksheet->getStyle($res['data']['template_title']['plan'].'1')->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);//垂直居中
                    $worksheet->getCell($res['data']['template_title']['plan'].$row)->setValue($plan_comment);
                }
            }
            $row++;
        }
        //下载修改后的excel文件
        if($is_template){
            $file_type = '-Template';//模板
        }else{
            $file_type = '-ExportData';//导出数据
        }
        $ptc_subject_title = array(
            1=>'CLA',
            2=>'MATH',
            3=>'ELA',
        );
        $xlsx_name = $res['data']['class_title'].'-'.$ptc_subject_title[$ptc_subject].$file_type.'.xlsx';//下载文件的名字
        header('pragma:public');
        header('Content-type:application/vnd.ms-excel;charset=utf-8;');
        header('Content-Disposition:attachment;filename='.$xlsx_name);//"xls"参考下一条备注
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, $type);
        //"Excel2007"生成2007版本的xlsx，"Excel5"生成2003版本的xls 调用工厂类
        return $objWriter->save('php://output');
    }


    public function actionGetMiddlePlanData()
    {
        $yid = Yii::app()->request->getParam('yid');
        $start_year = Yii::app()->request->getParam('start_year');
        $semester = Yii::app()->request->getParam('semester');
        $school_id = $this->branchId;
        $ptc_subject = Yii::app()->request->getParam('ptc_subject');
        $template_id = Yii::app()->request->getParam('template_id');
        $res = CommonUtils::requestDsOnline('ptc2/getMiddlePlanData',array(
            'start_year'=>$start_year,
            'yid'=>$yid,
            'semester'=>$semester,
            'school_id'=>$school_id,
            'ptc_subject'=>$ptc_subject,
            'template_id'=>$template_id,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->showMessage();
        }
    }

    public function actionSaveMiddlePlanData()
    {
        $class_id = Yii::app()->request->getParam('class_id');
        $start_year = Yii::app()->request->getParam('start_year');
        $semester = Yii::app()->request->getParam('semester');
        $child_data = Yii::app()->request->getParam('child_data');
        $school_id = $this->branchId;
        $ptc_subject = Yii::app()->request->getParam('ptc_subject');
        $template_id = Yii::app()->request->getParam('template_id');
        $template_type = Yii::app()->request->getParam('template_type');
        $res = CommonUtils::requestDsOnline('ptc2/saveMiddlePlanData',array(
            'start_year'=>$start_year,
            'class_id'=>$class_id,
            'semester'=>$semester,
            'child_data'=>$child_data,
            'school_id'=>$school_id,
            'ptc_subject'=>$ptc_subject,
            'template_id'=>$template_id,
            'template_type'=>$template_type,
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
            $this->showMessage();
        }
    }

    //导出数据和下载模板
    public function actionDownloadMiddleTemplate()
    {
        //请求数据
        $yid = Yii::app()->request->getParam('yid');
        $start_year = Yii::app()->request->getParam('start_year');
        $semester = Yii::app()->request->getParam('semester');
        $school_id = $this->branchId;
        $ptc_subject = Yii::app()->request->getParam('ptc_subject');
        $is_template = Yii::app()->request->getParam('is_template',1);
        $template_id = Yii::app()->request->getParam('template_id');
        $res = CommonUtils::requestDsOnline('ptc2/getMiddlePlanData',array(
            'start_year'=>$start_year,
            'yid'=>$yid,
            'semester'=>$semester,
            'school_id'=>$school_id,
            'ptc_subject'=>$ptc_subject,
            'template_id'=>$template_id,
            'is_export'=>!$is_template
        ));
        //按照拼音排序
        $child_list_new = array();
        $fengeName = Yii::createComponent('common.extensions.pinyin.Utf8ToPinyin');
        $pinyin = Yii::createComponent('common.components.pinyin.PinyinTool');
        foreach ($res['data']['child_list_by_id'] as $key => $item) {
            $childName = $fengeName->splitName($item['name']);
            $pinyinName = strtolower($pinyin->tool->sentence($childName[0])).' '.str_replace(' ', '', $pinyin->tool->sentence($childName[1]));
            $new_array[$key] = $pinyinName;
        }
        asort($new_array);
        foreach ($new_array as $child_id => $name) {
            $child_list_new[] = $res['data']['child_list_by_id'][$child_id];
        }
        spl_autoload_unregister(array('YiiBase','autoload'));
        Yii::import('ext.PHPExcel.PHPExcel',true);
        spl_autoload_register(array('YiiBase','autoload'));
        $file_path_base = dirname(__FILE__).'/../excel/';
        $res_file = $res['data']['template_file'];
        $use_cn = $res['data']['template_type'];//1 中文评语 3英文评语
        if(!empty($res_file)){
            $file = $file_path_base.'/'.$res_file;
        }else {
            return '';
        }
        $fileType = pathinfo($file, PATHINFO_EXTENSION);
        //根据文件类型，加载不同类库
        switch ($fileType) {
            case 'xls':
                $type = 'Excel5';
                break;
            case 'xlsx':
                $type = 'Excel2007';
                break;
        }
        $objReader = \PHPExcel_IOFactory::createReader($type);
        $objPHPExcel =$objReader->load($file, $encode = 'utf-8'); // 加载文件
        //将活动工作表索引设置为第一个工作表：
//        $objPHPExcel->setActiveSheetIndex(0);
        //获取活动工作表
        $worksheet = $objPHPExcel->getActiveSheet();
        //从第二行开始填充数据
        $row = 2;
        $subject_title = $res['data']['subject_title']['title_cn']."\n".$res['data']['subject_title']['title_en'];
        foreach ($child_list_new as $child_info){
            $worksheet->getCell('A'.$row)->setValue($child_info['id']); //id
            $worksheet->getCell('B'.$row)->setValue($child_info['bilingual_name']);//姓名
            if(!$res['data']['import_map']['C']){
                $worksheet->getCell('C'.$row)->setValue($subject_title);//课程
            }
            //对齐方式
            $worksheet->getStyle('B'.$row)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_LEFT);
            $worksheet->getStyle('C'.$row)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_LEFT);
            $worksheet->getStyle('C'.$row)->getAlignment()->setWrapText(true);
            if($row % 2 == 0){
                //背景色
                $worksheet->getStyle('A'.$row)->getFill()->setFillType(\PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB("D9D9D9");
                $worksheet->getStyle('B'.$row)->getFill()->setFillType(\PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB("D9D9D9");
                $worksheet->getStyle('C'.$row)->getFill()->setFillType(\PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB("D9D9D9");
            }
            foreach ($res['data']['template_title'] as $key=>$v){
                if($key!=='plan'){
                    //对齐方式
                    $worksheet->getStyle($v.$row)->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_LEFT);
                    if($row % 2 == 0){
                        //设置背景色
                        if(!empty($res['data']['template_title'][$key])){
                            $worksheet->getStyle($res['data']['template_title'][$key].$row)->getFill()->setFillType(\PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB("D9D9D9");
                        }
                    }
                }
                if(!$is_template){
                    if($row % 2 == 0){
                        //设置背景色
                        if(!empty($res['data']['template_title'][$key])){
                            $worksheet->getStyle($res['data']['template_title'][$key].$row)->getFill()->setFillType(\PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB("D9D9D9");
                        }
                    }
                }
            }
            //导出数据需要用到其他列
            if(!$is_template){
                foreach ($res['data']['ptc_plan_data'][$child_info['id']]['score'] as $key=>$value){
                    if($key !== 'SupportEnrichmentStrategy'){
                        //需要中文模板的将选项内容换成中文
                        if($use_cn == 1){
                            $value = empty($res['data']['score_type'][$key]['option'][$value]) ? '' : $res['data']['score_type'][$key]['option'][$value];
                        }
                    }
                    if(!empty($res['data']['template_title'][$key])){
                        $worksheet->getCell($res['data']['template_title'][$key].$row)->setValue($value);
                    }
                }
                //导出数据增加提升计划
                if(!empty($res['data']['template_title']['plan'])){
                    $plan_comment = $res['data']['ptc_plan_data'][$child_info['id']]['comment'];//提升计划
                    $worksheet->getCell($res['data']['template_title']['plan'].'1')->setValue(Yii::t('ptc','Improvement Plan'));
                    $worksheet->getStyle($res['data']['template_title']['plan'].'1')->getFill()->setFillType(\PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setRGB("92D050");
                    $worksheet->getStyle($res['data']['template_title']['plan'].'1')->getFont()->getColor()->setRGB("FFFFFF");//白色
                    $worksheet->getStyle($res['data']['template_title']['plan'].'1')->getFont()->setBold(true);//加粗
                    $worksheet->getStyle($res['data']['template_title']['plan'].'1')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);//水平居中
                    $worksheet->getStyle($res['data']['template_title']['plan'].'1')->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);//垂直居中
                    $worksheet->getCell($res['data']['template_title']['plan'].$row)->setValue($plan_comment);
                }
            }
            $row++;
        }
        //下载修改后的excel文件
        if($is_template){
            $file_type = '-Template';//模板
        }else{
            $file_type = '-ExportData';//导出数据
        }

        $xlsx_name = $ptc_subject.'-'.$res['data']['subject_title']['title_cn'].$res['data']['subject_title']['title_en'].$file_type.'.xlsx';//下载文件的名字
        header('pragma:public');
        header('Content-type:application/vnd.ms-excel;charset=utf-8;');
        header('Content-Disposition:attachment;filename='.$xlsx_name);//"xls"参考下一条备注
        $objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, $type);
        //"Excel2007"生成2007版本的xlsx，"Excel5"生成2003版本的xls 调用工厂类
        return $objWriter->save('php://output');
    }

}
