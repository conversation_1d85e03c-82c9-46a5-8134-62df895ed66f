<?php
class PromotedCommand extends CConsoleCommand
{
    public $batchNum = 200;

    public function init() {
        parent::init();
        Yii::import('common.models.child.*');
        Yii::import('common.models.calendar.*');
    }

    // 每天去根据园长表去更新当年的年 学期 月 的数据
    public function actionSaveNow()
    {
        $schoolList = $this->getschool();
        $calendarInfo = $this->getcalendar($schoolList,1);
        foreach ($schoolList as $schoolid) {
            if ($calendarInfo[$schoolid]) {
                $types = array('yearly','termly','monthly');//yearly,termly,monthly
                foreach ($types as $type){
                    $crit = new CDbCriteria;
                    $crit->compare('yid', $calendarInfo[$schoolid]['yid']);
                    $crit->index = 'yid';
                    $calendar = Calendar::model()->find($crit);
                    $timepoints = explode(',', $calendar->timepoints);

                    $startdate = reset($timepoints);
                    $enddate = end($timepoints);

                    $term = 0;
                    if($type == 'termly') {
                        $startdate = 0;
                        $enddate = 0;
                        $semesterDate = array(
                            'lastSemester' => array(
                                $timepoints[0],
                                $timepoints[1],
                            ),
                            'nextSemester' => array(
                                $timepoints[2],
                                $timepoints[3],
                            )
                        );

                        if (time() >= $semesterDate['lastSemester'][0] && time() <= $semesterDate['lastSemester'][1]) {
                            $term = 1;
                            $startdate = $semesterDate['lastSemester'][0];
                            $enddate = $semesterDate['lastSemester'][1];
                        }
                        if (time() >= $semesterDate['nextSemester'][0] && time() <= $semesterDate['nextSemester'][1]) {
                            $term = 2;
                            $startdate = $semesterDate['nextSemester'][0];
                            $enddate = $semesterDate['nextSemester'][1];
                        }
                    }

                    if($type == 'monthly'){
                        $startdate = strtotime(date("Y-m-01"));  // 获取当前月份第一天的时间戳
                        $enddate = strtotime('last day of this month 23:59:59');  // 获取当前月份最后一天 23:59:59 的时间戳
                    }

                    $data = array(
                        'yid' => $calendarInfo[$schoolid]['yid'],
                        'startyear' => $calendarInfo[$schoolid]['startyear'],
                        'schoolid' => $schoolid,
                        'term' => $term,
                        'startdate' => $startdate,
                        'enddate' => $enddate,
                        'type' => $type,
                    );
                    $this->saveNewSchoolyearStatsChild($data);
                }
            }
        }
    }

    // 增加流失率表 以年为单位
    public function actionUpdateYear()
    {
        $schoolList = $this->getschool();
        $calendarInfo = $this->getcalendar($schoolList);
        foreach ($schoolList as $schoolid){
            if($calendarInfo[$schoolid]) {
                foreach ($calendarInfo[$schoolid] as $val) {
                    $crit = new CDbCriteria;
                    $crit->compare('yid', $val['yid']);
                    $crit->index = 'yid';
                    $calendar = Calendar::model()->find($crit);
                    $timepoints = explode(',', $calendar->timepoints);

                    $data = array(
                        'yid' => $val['yid'],
                        'startyear' => $val['startyear'],
                        'schoolid' => $schoolid,
                        'term' => '',
                        'startdate' => ($timepoints) ? reset($timepoints) : 0,
                        'enddate' => ($timepoints) ? end($timepoints) : 0,
                        'type' => 'yearly',
                    );
                    $this->saveNewSchoolyearStatsChild($data);
                }
            }
        }
    }

    // 增加流失率表 以学期为单位 yearly
    public function actionUpdateTermly()
    {
        $schoolList = $this->getschool();
        $calendarInfo = $this->getcalendar($schoolList);
        foreach ($schoolList as $schoolid){
            if($calendarInfo[$schoolid]) {
                foreach ($calendarInfo[$schoolid] as $val) {
                    $crit = new CDbCriteria;
                    $crit->compare('yid', $val['yid']);
                    $crit->index = 'yid';
                    $calendar = Calendar::model()->find($crit);

                    $timepoints = explode(',', $calendar->timepoints);
                    $semesterDate = array(
                        'lastSemester' => array(
                            $timepoints[0],
                            $timepoints[1],
                        ),
                        'nextSemester' => array(
                            $timepoints[2],
                            $timepoints[3],
                        )
                    );
                    $term = 1;
                    foreach ($semesterDate as $semester) {
                        $data = array(
                            'yid' => $val['yid'],
                            'startyear' => $val['startyear'],
                            'schoolid' => $schoolid,
                            'term' => $term,
                            'startdate' => $semester[0],
                            'enddate' => $semester[1],
                            'type' => 'termly',
                        );
                        $term++;
                        $this->saveNewSchoolyearStatsChild($data);
                    }
                }
            }
        }
    }

    // 增加流失率表 以月为单位
    public function actionUpdateMonthly()
    {
        $schoolList = $this->getschool();
        $calendarInfo = $this->getcalendar($schoolList);
        foreach ($schoolList as $schoolid){
            if($calendarInfo[$schoolid]) {
                foreach ($calendarInfo[$schoolid] as $val) {
                    $crit = new CDbCriteria;
                    $crit->compare('yid', $val['yid']);
                    $crit->index = 'yid';
                    $calendar = Calendar::model()->find($crit);

                    $timepoints = explode(',', $calendar->timepoints);

                    $time1 = reset($timepoints); // 自动为00:00:00 时分秒
                    $time2 = end($timepoints);
                    $months = array();
                    while( ($time1 = strtotime('+1 month', $time1)) <= $time2){
                        $months[] = date('Y-m-d',$time1); // 取得递增月;
                    }
                    $lastTime = date("Y-m", $timepoints[0]) . '-01';
                    array_unshift($months, $lastTime);

                    foreach ($months as $month) {
                        $startMonth = strtotime($month);
                        $EndDate = date('Y-m-d', strtotime("$month +1 month -1 day"));
                        $endMonth= strtotime("next day", strtotime($EndDate)) - 1;//指定年月份月末时间戳

                        $data = array(
                            'yid' => $val['yid'],
                            'startyear' => $val['startyear'],
                            'schoolid' => $schoolid,
                            'term' => "",
                            'startdate' => $startMonth,
                            'enddate' => $endMonth,
                            'type' => 'monthly',
                        );
                        $this->saveNewSchoolyearStatsChild($data);
                    }
                }
            }
        }
    }


    public function saveNewSchoolyearStatsChild($data)
    {
        $criteria = new CDbCriteria;
        $criteria->compare('yid', $data['yid']);
        $criteria->compare('schoolid', $data['schoolid']);
        $criteria->compare('classid', ">0");
        if($data['type'] != 'yearly'){
            $criteria->compare('period_timestamp', ">={$data['startdate']}");
            $criteria->compare('period_timestamp', "<={$data['enddate']}");
        }
        $total = StatsChildCount::model()->count($criteria);
        if($total) {
            $childids = array();
            $cycle = ceil($total / $this->batchNum);
            for ($i = 0; $i < $cycle; $i++) {
                $criteria->limit = $this->batchNum;
                $criteria->offset = $i * $this->batchNum;
                $criteria->order = 'period_timestamp asc';
                $model = StatsChildCount::model()->findAll($criteria);
                foreach ($model as $item) {
                    $chids = $item->childids1 . ',' . $item->childids2;
                    foreach (explode(',', $chids) as $cid) {
                        if ($cid) {
                            $childids[$cid] = $item->classid;
                        }
                    }
                }
            }

            $info = array(
                'yid' => $data['yid'],
                'startyear' => $data['startyear'],
                'schoolid' => $data['schoolid'],
                'term' => $data['term'],
                'childids' => $childids,
                'startdate' => $data['startdate'],
                'enddate' => $data['enddate'],
                'type' => $data['type'],
            );
            $err = $this->saveSchoolyear($info);
            echo $err;
        }
    }

    // 写入SchoolyearStatsChild数据
    public static function saveSchoolyear($data)
    {
        $criteria = new CDbCriteria();
        $criteria->compare('type', $data['type']);
        $criteria->compare('yid', $data['yid']);
        $criteria->compare('startyear', $data['startyear']);
        if ($data['type'] !== 'yearly') {
            $criteria->compare('startdate', $data['startdate']);
            $criteria->compare('enddate', $data['enddate']);
        }
        $criteria->compare('schoolid', $data['schoolid']);
        $schoolYearModel = SchoolyearStatsChild::model()->find($criteria);

        if(!$schoolYearModel){
            $schoolYearModel = new SchoolyearStatsChild();
        }
        $schoolYearModel->type = $data['type'];
        $schoolYearModel->yid = $data['yid'];
        $schoolYearModel->term = $data['term'];
        $schoolYearModel->startyear = $data['startyear'];
        $schoolYearModel->startdate = $data['startdate']; //isset($weekModel) ? $startdate->monday_timestamp : 0;
        $schoolYearModel->enddate = $data['enddate'];  //isset($weekModel) ? $enddate->monday_timestamp : 0;
        $schoolYearModel->schoolid = $data['schoolid'];
        $schoolYearModel->num = isset($data['childids']) ? count($data['childids']) : 0;
        $schoolYearModel->childids = isset($data['childids']) ? implode(',', array_keys($data['childids'])) : '';
        $schoolYearModel->classids = isset($data['childids']) ? implode(',', array_values($data['childids'])) : '';
        $schoolYearModel->updated_at = time();
        if($schoolYearModel->save()){
            return $data['schoolid'] . ' - ' . date("Y-m-d", $schoolYearModel->startdate) . ' - ' . date("Y-m-d", $schoolYearModel->enddate) . ' - ' . $data['yid'] .  ' - ' .  count($data['childids']) . "\n";
        } else {
            return $data['schoolid'] . ' - ' . date("Y-m-d", $schoolYearModel->startdate) . ' - ' . date("Y-m-d", $schoolYearModel->enddate) . ' - ' . $data['yid'] .  ' - ' .  count($data['childids']) . "- FAIL \n";
        }
    }

    // 获取所有有效的校园信息
    public static function getschool()
    {
        $criteria = new CDbCriteria();
        $criteria->compare('type', array(20,50));
        $criteria->compare('status', 10);
        $criteria->order = 'abb ASC';
        $branchInfo = Branch::model()->findAll($criteria);
        $schoolInfo = array();
        if($branchInfo){
           foreach ($branchInfo as $val){
               $schoolInfo[$val->branchid] = $val->branchid;
           }
        }
        return $schoolInfo;
    }

    public static function getcalendar($schools, $is_selected = 0)
    {
        $calendarInfo = array();

        $criteria = new CDbCriteria();
        $criteria->compare('branchid', $schools);
        if($is_selected) {
            $criteria->compare('is_selected', $is_selected);
        }
        $calendarModel = CalendarSchool::model()->findAll($criteria);
        if ($calendarModel) {
            foreach ($calendarModel as $val) {
                if ($is_selected) {
                    $calendarInfo[$val->branchid] = array(
                        'yid' => $val->yid,
                        'startyear' => $val->startyear,
                    );
                } else {
                    $calendarInfo[$val->branchid][] = array(
                        'yid' => $val->yid,
                        'startyear' => $val->startyear,
                    );
                }
            }
        }
        return $calendarInfo;
    }


    public function actionPromotedNum($yid, $schoolid, $type, $time = 0, $term = 0)
    {
        $startMonth = 0;
        $endMonth = 0;
        if(in_array($type, array('yearly','termly'))){
            $crit = new CDbCriteria;
            $crit->compare('yid', $yid);
            $crit->index = 'yid';
            $calendar = Calendar::model()->find($crit);
            $timepoints = explode(',', $calendar->timepoints);
            if($type == 'yearly'){
                $startMonth = $timepoints[0];
                $endMonth = $timepoints[3];
            }
            if($type == 'termly'){
                $semesterDate = array(
                    'lastSemester' => array(
                        $timepoints[0],
                        $timepoints[1],
                    ),
                    'nextSemester' => array(
                        $timepoints[2],
                        $timepoints[3],
                    )
                );
                if($term == 1){
                    $startMonth = $semesterDate['lastSemester'][0];
                    $endMonth = $semesterDate['lastSemester'][1];
                }
                if($term == 2){
                    $startMonth = $semesterDate['nextSemester'][0];
                    $endMonth = $semesterDate['nextSemester'][1];
                }
            }
        }

        if($type == 'monthly'){ //yearly,termly,monthly
            $startMonth = strtotime($time);
            $EndDate = date('Y-m-d', strtotime("$time +1 month -1 day"));
            $endMonth= strtotime("next day", strtotime($EndDate)) - 1;//指定年月份月末时间戳
        }

        $criteria = new CDbCriteria;
        $criteria->compare('yid', $yid);
        $criteria->compare('schoolid', $schoolid);
        $criteria->compare('classid', 0);
        $criteria->compare('period_timestamp', ">={$startMonth}");
        $criteria->compare('period_timestamp', "<={$endMonth}");
        $total = StatsChildCount::model()->findAll($criteria);

        if($total){
            foreach ($total as $item){
                $chids = $item->childids1.','.$item->childids2;
                foreach(explode(',',$chids) as $cid){
                    if ($cid){
                        $childids[$cid] = $cid;
                    }
                }
            }
            var_dump(count($childids));
        }else{
            echo 0;
        }
    }
}
