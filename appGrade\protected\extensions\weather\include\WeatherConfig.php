<?php

/*
 * 关于 天气的一些配置信息
 * @authur Howard.Wang
 * @date 2012-08-06
 */

class WeatherConfig {
    /*
     * 城市信息（名称）
     */

    private $citiesInfo = null;
    /*
     * 城市代码 nmc专用
     */
    private $cityCodeForNmc = null;
    /*
     * 城市代码 Yahoo专用
     */
    private $cityCodeForYahoo = null;
    /*
     * 城市代码 Moji AQI专用
     */
    private $cityCodeForMoji = null;
    /*
     * 中文的天气API名称
     */
    private $weatherApiForCn = null;
    /*
     * 英文的天气API名称
     */
    private $weatherApiForEn = null;
    /*
     * 显示空气质量的城市
     */
    private $showAqCities = null;

    public function getCitiesInfo() {
        $this->citiesInfo = array(
            'Beijing' => Yii::t('weatherWidget', 'Beijing'),
            'Chengdu' => Yii::t('weatherWidget', 'Chengdu'),
            'Ningbo' => Yii::t('weatherWidget', 'Ningbo'),
            "Xian" => Yii::t('weatherWidget', "Xi'an"),
            'Tianjin' => Yii::t('weatherWidget', 'Tianjin'),
        );
        return $this->citiesInfo;
    }

    /*
     * 城市代码 nmc专用
     */

    public function getCityCodeForNmc() {
        $this->cityCodeForNmc = array(
            'Beijing' => '101010100',
            'Chengdu' => '101270101',
            'Ningbo' => '101210401',
            "Xian" => '101110101',
            'Tianjin' => '101030100',
        );
        return $this->cityCodeForNmc;
    }

    /*
     * 城市代码 Yahoo专用
     */

    public function getCityCodeForYahoo() {
        $this->cityCodeForYahoo = array(
            'Beijing' => 'CHXX0008',
            'Chengdu' => 'CHXX0016',
            'Ningbo' => 'CHXX0522',
            "Xian" => 'CHXX0141',
            'Tianjin' => 'CHXX0133',
        );
        return $this->cityCodeForYahoo;
    }

    /*
     * 城市代码 Moji AQI专用
     */

    public function getCityCodeForMoji() {
        $this->cityCodeForMoji = array(
            'Beijing' => '33',
            'Chengdu' => '53',
            'Ningbo' => '1434',
            "Xian" => '411',
            'Tianjin' => '379',
        );
        return $this->cityCodeForMoji;
    }

    /*
     * 中文的天气API名称
     */

    public function getWeatherApiForCn() {
        // 		$this->weatherApiForCn = 'Nmc';
        // 		return $this->weatherApiForCn;
        return 'Nmc';
    }

    /*
     * 英文的天气API名称
     */

    public function getWeatherApiForEn() {
        // 		$this->weatherApiForEn = 'Yahoo';
        // 		return $this->weatherApiForEn;
        return 'Yahoo';
    }

    public function getAQIApiForCn() {
        return 'Moji';
    }

    public function getAQIApiForEn() {
        return 'Moji';
    }

    public function getAQIApi() {
        return 'Moji';
    }

}