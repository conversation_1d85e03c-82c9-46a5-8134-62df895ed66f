<?php

/**
 * This is the model class for table "ivy_mail_recipient".
 *
 * The followings are the available columns in table 'ivy_mail_recipient':
 * @property integer $id
 * @property string $flag
 * @property string $branch_id
 * @property string $mail_reply_to
 * @property string $send_to
 * @property string $cc_to
 * @property string $bcc_to
 * @property integer $updated
 * @property integer $updated_userid
 */
class MailRecipient extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_mail_recipient';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('flag, branch_id', 'required'),
			array('updated, updated_userid', 'numerical', 'integerOnly'=>true),
			array('flag', 'length', 'max'=>32),
			array('branch_id', 'length', 'max'=>16),
			array('mail_reply_to, send_to, cc_to, bcc_to, support_email', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, flag, branch_id, mail_reply_to, send_to, cc_to, bcc_to, updated, updated_userid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'flag' => Yii::t('mailmisc','Mail Identifier'),
			'branch_id' => Yii::t('global', 'Branch'),
			'mail_reply_to' => Yii::t('mailmisc', 'Reply To'),
			'send_to' => Yii::t('mailmisc', 'Send To'),
			'cc_to' => Yii::t('mailmisc', 'CC'),
			'bcc_to' => Yii::t('mailmisc', 'BCC'),
			'updated' => 'Updated',
			'updated_userid' => 'Updated Userid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('flag',$this->flag,true);
		$criteria->compare('branch_id',$this->branch_id,true);
		$criteria->compare('mail_reply_to',$this->mail_reply_to,true);
		$criteria->compare('send_to',$this->send_to,true);
		$criteria->compare('cc_to',$this->cc_to,true);
		$criteria->compare('bcc_to',$this->bcc_to,true);
		$criteria->compare('updated',$this->updated);
		$criteria->compare('updated_userid',$this->updated_userid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return MailRecipient the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
