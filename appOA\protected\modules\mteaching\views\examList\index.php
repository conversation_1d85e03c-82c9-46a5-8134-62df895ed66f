<style>
    .el-radio__input.is-checked .el-radio__inner{
        border-color: #4D88D2;
        background: #4D88D2;
    }
    .el-radio{
        margin-right:16px
    }
    .el-radio.is-bordered.is-checked{
        border-color: #4D88D2
    }
    .el-radio__input.is-checked+.el-radio__label,.colorBlue{
        color: #4D88D2
    }
    .grade{
        background:#F7F7F8;
        padding:16px 0 0 16px;
        border-radius: 4px

    }
    .img28{
        width:28px;
        height:28px;
        border-radius:50%;
        object-fit: cover;
    }
    .class{
        background: #E5E6EB;
        border-radius: 2px;
        padding:2px 4px;
        font-size:12px;
        color:#333;
        
    }
    .scroll-box::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius   : 10px;
        background-color: #ccc;
        background-image: none
    }
    .scroll-box::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0);
        background   : #F7F7F8;
        border-radius: 10px;
        border:none
    }
    .max_height{
        height:126px;
        overflow-y:auto;
    }
    .p16{
        padding:8px 24px
    }
    .plr6{
        padding-left:6px;
        padding-right:6px
    }
    .panel{
        box-shadow:none;
    }
    .panel-default , .panel-heading{
        border-color: #E5E6EB;
    }
    .colorGreen{
        color:#5CB85C
    }
    .colorWait{
        color:#F0AD4E
    }
    .courseTitle{
        padding:6px 12px;
        background: #F7F7F8;
        border-radius: 4px;
    }
    .setGrade{
        border-radius: 4px;
        border: 1px solid #E5E6EB;
        padding:16px;
        margin-top:12px
    }
    .addTeacher{
        width: 28px;
        height: 28px;
        background: #F0F5FB;
        border: 1px dashed #4D88D2;
        color: #4D88D2;
        display: inline-block;
        text-align: center;
        line-height: 28px;
        border-radius: 50%;
        cursor: pointer;
    }
    .setClass{
        padding:8px 10px;
        background: #F7F7F8;
        border-radius: 4px;
        margin:8px 0;
        display:flex;
        align-items:center
    }
    .setClass .width{
        width:100px
    }
    .dots{
        position: absolute;
        width: 9px;
        height: 9px;
        background: #D9534F;
        right: 0;
        top: 0;
        border-radius: 50%;
    }
    .inline{
        display:inline-block;
        position: relative;
        margin-right:8px
    }
    .closeTea{
        position: absolute;
        right: -4px;
        top: -4px;
        color: #333;
        font-size: 14px;
        cursor: pointer;
    }
    .optionSearch{
        height:auto
    }
    .imageSearch{
        width: 44px;
        height: 44px;
        object-fit: cover;
    }
    .text_overflow {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        line-height: 14px;
    }
    .el-select__tags-text{
        color:#666
    }
    .ptb8{
        margin-top: 4px;
        margin-bottom: 4px;
        border-top: 1px solid #dddddd;
    }
    .photoUrl{
        display:inline-block;
        margin-left:-8px
    }
    .photoUrl:hover{
        transform: scale(1.1); 
    }
</style>
<div class="container-fluid"  id='container'>
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Teaching Tasks'), array('//mteaching/default/index'))?></li>
        <li class="active"><?php echo Yii::t('referral','Roaster Generator');?></li>
    </ol>
    <div class="" v-if='configData.config'>
        <div>
            <el-radio-group v-model="course" size="small"  ref="radio">
                <el-radio :label="list.value" border v-for='(list,index) in configData.config'>{{list.title}}</el-radio>
            </el-radio-group> 
        </div>
        <div v-if='course=="MAP"'>
            <div class="panel panel-default mt20">
                <div class="panel-heading p16 flex align-items">
                    <div class='color3 font14 flex1 fontBold'>
                        <span v-if="configData.isKGSchool">KG</span>
                        <span v-else>ES</span>
                    </div>
                    <span class='colorBlue font14 cur-p' @click='esSet'><span class='el-icon-setting'></span> <?php echo Yii::t('referral','Teacher Setting');?></span>
                </div>
                <div class="panel-body p24">
                    <div class='row'>
                        <div class='col-md-2 plr6' v-for='(list,index) in category_sub[course].ES'>
                            <div class='grade' >
                                <div class='font14 color3 fontBold mb8'>{{list.title}}</div>
                                <div  class='scroll-box max_height'>
                                    <template v-if='newConfigListByGroup.ES && newConfigListByGroup.ES[list.value]' >
                                        <div v-for='(sub,idx) in newConfigListByGroup.ES[list.value]'>     
                                                
                                            <div v-if='newConfigListByGroup.ES[list.value][idx].config_type=="class"' class=' pr16'>   
                                                <hr v-if='idx==1' class='ptb8'>                         
                                                <div v-for='(item,i) in newConfigListByGroup.ES[list.value][idx].config' class='mb12'>
                                                    <div class='class'>{{item.class_title}}</div>
                                                    <div class='flex align-items mt8 ' >
                                                        <div class='flex1 color3 mr16'>{{subjectMap[newConfigListByGroup.ES[list.value][idx].subject]}}</div>
                                                        <div  v-if='item.user.length' class='relative'>
                                                            <span v-for='(_item,i) in item.user' class='photoUrl'>
                                                                <el-tooltip class="item" effect="dark" placement="top">
                                                                    <div slot="content">{{ESTeacherInfo[_item].name}} <span v-if='ESTeacherInfo[_item].level==0'>（<?php echo Yii::t('newDS','Resigned');?>）</span></div>
                                                                    <img :src="ESTeacherInfo[_item].photoUrl" class='img28' alt="" >
                                                                </el-tooltip>
                                                                <span class='dots' v-if='ESTeacherInfo[_item].level==0'></span>
                                                            </span>
                                                        </div>
                                                        <div v-else class='color9'><?php echo Yii::t('referral','Teacher not set');?></div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div v-else class='pr16'>      
                                                <hr v-if='idx==1'  class='ptb8'>                              
                                                <div class='flex align-items relative pt4 pb4' :class='idx==1?"mb16":""' v-if='newConfigListByGroup.ES[list.value][idx].config.user.length'>
                                                    <div class='flex1 color3 mr16'>{{subjectMap[newConfigListByGroup.ES[list.value][idx].subject]}}</div>
                                                    <span v-for='(_item,i) in newConfigListByGroup.ES[list.value][idx].config.user' class='photoUrl'>
                                                        <el-tooltip class="item" effect="dark"  placement="top">
                                                            <div slot="content">{{ESTeacherInfo[_item].name}} <span v-if='ESTeacherInfo[_item].level==0'>（<?php echo Yii::t('newDS','Resigned');?>）</span></div>
                                                            <img :src="ESTeacherInfo[_item].photoUrl" class='img28' alt="">
                                                        </el-tooltip>
                                                        <span class='dots' v-if='ESTeacherInfo[_item].level==0'></span>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                    <div v-else>
                                       <div class='color9 font14 pt24 text-center'><?php echo Yii::t('referral','Teacher not set');?></div> 
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel panel-default mt24" v-if='category_sub[course].SS'>
                <div class="panel-heading p16 flex align-items">
                    <div class='color3 font14 flex1 fontBold'>SS</div>
                    <span class='colorBlue font14 cur-p' @click='ssSet'><span class='el-icon-setting'></span> <?php echo Yii::t('referral','Teacher Setting');?></span>
                </div>
                <div class="panel-body p24">
                    <div class='row'>
                        <div class='col-md-3 plr6' v-for='(list,index) in category_sub[course].SS'>
                            <div class='grade mb12' >
                                <div class='font14 color3 fontBold'>{{list.title}}</div>
                                <div class='scroll-box max_height'>
                                    <div v-if='newConfigListByGroup.SS && newConfigListByGroup.SS[list.value]' class='pr16'>
                                        <div class='flex align-items mt8 relative' v-for='(item,idx) in newConfigListByGroup.SS[list.value].config'>
                                            <div class='flex1 color3 mr16'>{{SSCourseTeacher[item].title}}</div>
                                            <el-tooltip class="item" effect="dark" :content="SSCourseTeacher[item].teacherInfo.name" placement="top">
                                                <div slot="content">{{SSCourseTeacher[item].teacherInfo.name}} <span v-if='SSCourseTeacher[item].teacherInfo.level==0'>（<?php echo Yii::t('newDS','Resigned');?>）</span></div>
                                                <img :src="SSCourseTeacher[item].teacherInfo.photoUrl" class='img28' alt="">
                                            </el-tooltip>
                                            <span class='dots' v-if='SSCourseTeacher[item].teacherInfo.level==0'></span>
                                        </div>
                                    </div>
                                    <div v-else>
                                       <div class='color9 font14 pt24 text-center'><?php echo Yii::t('referral','Teacher not set');?></div> 
                                    </div>
                                </div>
                                
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class='flex'>
                <div class='flex1'></div>
                <button type="button" class="btn btn-primary " @click='createTable' :disabled='tableLoading'><?php echo Yii::t('referral','Generate Roaster');?></button>
            </div>
            <hr>
            <div v-if='studentData.length!=0'>
                <div class='flex'>
                    <div class='flex1'>
                        <span class='font16 color3 fontBold mr16'><?php echo Yii::t('referral','Roaster');?></span>
                        <span class='colorGreen font14'><span class='el-icon-success mr5'></span>{{translate("<?php echo Yii::t('referral', 'Generated at %s'); ?>", mapTime)}}</span>
                    </div>
                    <button type="button" class="btn btn-primary "  @click='exportData()'><?php echo Yii::t('referral','Export');?></button>
                </div>
                <div class='mt24'>
                    <el-table
                        id='mapTable'
                        height="800"
                        :data="studentData"
                        border
                        :header-cell-style="{background:'#fafafa',color:'#333'}"
                        style="width: 100%">
                        <el-table-column
                        prop="school_name"
                        label="School Name"
                        fixed
                        width="120">
                        </el-table-column>
                        <el-table-column
                        prop="instructor_id"
                        label="Instructor ID"
                        width="120">
                        </el-table-column>
                        <el-table-column
                        prop="instructor_last_name"
                        label="Instructor Last Name"
                        width="180">
                        </el-table-column>
                        <el-table-column
                        prop="instructor_first_name"
                        label="Instructor First Name"
                        width="180">
                        </el-table-column>
                        <el-table-column
                        prop="user_name"
                        label="User Name"
                        width="180">
                        </el-table-column>
                        <el-table-column
                        prop="email_address"
                        label="Email Address"
                        width="200">
                        </el-table-column>
                        <el-table-column
                        prop="export_class_name"
                        label="Class Name"
                        width="120">
                        </el-table-column>
                        <el-table-column
                        prop="student_id"
                        label="Student ID"
                        width="120">
                        </el-table-column>
                        <el-table-column
                        prop="student_last_name_en"
                        label="Student Last Name"
                        width="180">
                        </el-table-column>
                        <el-table-column
                        prop="student_first_name_en"
                        label="Student First Name"
                        width="180">
                        </el-table-column>
                        <el-table-column
                        prop="student_birthday"
                        label="Student Date Of Birth"
                        width="180">
                        </el-table-column>
                        <el-table-column
                        prop="student_gender"
                        label="Student Gender"
                        width="180">
                        </el-table-column>
                        <el-table-column
                        prop="student_grade"
                        label="Student Grade"
                        width="120">
                        </el-table-column>
                        <el-table-column
                        prop="student_country_name"
                        label="Student Ethnic Group Name"
                        width="220">
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </div>
        <div v-else>
            <div class='flex'>
                <div class='flex1'></div>
                <button type="button" class="btn btn-primary " @click='createTableLc' :disabled='tableLoading'><?php echo Yii::t('referral','Generate Roaster');?></button>
            </div>
            <hr>
            <div v-if='studentDataES.length!=0'>
                <div class='flex'>
                    <div class='flex1'>
                        <span class='font16 color3 fontBold mr16'><?php echo Yii::t('referral','Roaster');?></span>
                        <span class='colorGreen font14'><span class='el-icon-success'></span> {{translate("<?php echo Yii::t('referral', 'Generated at %s'); ?>", LCETime)}}</span>
                    </div>
                    <button type="button" class="btn btn-primary " @click='exportDataLc()'><?php echo Yii::t('referral','Export');?></button>
                </div>
                <div class='mt24'>
                    <el-table
                        id='exportTable'
                        :data="studentDataES"
                        border
                        height="800"
                        :header-cell-style="{background:'#fafafa',color:'#333'}"
                        style="width: 100%">
                        <el-table-column
                        prop="student_id"
                        label="Username"
                        >
                        </el-table-column>
                        <el-table-column
                        prop="student_cn_name"
                        label="中文名字"
                        >
                        </el-table-column>
                        <el-table-column
                        prop="student_first_name_en"
                        label="法定名（拼音）"
                        >
                        </el-table-column>
                        <el-table-column
                        prop="student_last_name_en"
                        label="法定姓（拼音）"
                        >
                        </el-table-column>
                        <el-table-column
                        prop="group_identifier1"
                        label="group_identifier">
                        </el-table-column>
                        <el-table-column
                        prop="group_identifier2"
                        label="group_identifier">
                        </el-table-column>
                        <el-table-column
                        prop="group_identifier3"
                        label="group_identifier">
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </div>
    </div>
    <!-- ES -->
    <div class="modal fade" id="ESModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static" style="overflow-y: auto;">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <div class='flex'>
                        <div class='flex1'>
                            <h4 class="modal-title" id="parentReplyLabel">
                                <?php if($this->branchId ==='BJ_IASLT'){ ?>
                                    <?php echo Yii::t("quote", "Teacher Setting"); ?>
                                <?php }else{?>
                                    <?php echo Yii::t("quote", "Teacher Setting"); ?>
                                <?php }?>
                            </h4>
                        </div>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close" ><span aria-hidden="true">&times;</span></button>
                    </div>
                </div>
                <div class="modal-body p24 relative scroll-box" :style="'max-height:'+(height-190)+'px;overflow-y: auto;'">
                    <div v-for='(list,index) in eSConfig.subject'>
                        <div  class='courseTitle font14 color3 fontBold' :class='index==1?"mt24":""'>{{subjectMap[list]}}</div>
                        <div>
                            <div class='setGrade' v-for='(item,key,idx) in ESData[list]'>
                                <div class='flex'>
                                    <div class='flex1 font14 color3'>{{classShort[key]}}</div>
                                    <label class="radio-inline">
                                        <input type="radio"  v-model='item.showClass' value="1" @change='setClassTab(item)'> 按班级设置
                                    </label>
                                    <label class="radio-inline ml16">
                                        <input type="radio"  v-model='item.showClass'  value="2" @change='setClassTab(item)'> 按年级设置
                                    </label>
                                </div>
                                <div class='' v-if='item.showClass=="1"'>
                                    <div class='setClass' v-for='(classid,i) in item.class'>
                                        <div class='color3 font14 width'>{{classid.class_title}}</div>
                                        <div class='flex1'>
                                            <span v-if='classid.user.length>0'>
                                                <span v-for='(_item,j) in classid.user' class='inline'>
                                                <el-tooltip class="item" effect="dark" placement="top">
                                                    <div slot="content">{{ESTeacherInfo[_item].name}}</div>
                                                    <img :src="ESTeacherInfo[_item].photoUrl" class='img28' alt="">
                                                </el-tooltip>
                                                <span class='el-icon-error closeTea'  @click='delEsTeacher(list,key,"class",j,i)'></span>
                                                </span>
                                            </span>
                                            <span class='addTeacher' @click='addTeacher(item,"class",i)'><span class='el-icon-plus'></span></span>
                                        </div>
                                    </div>
                                </div>
                                <div class='mt8' v-if='item.showClass=="2"'>
                                    <span v-if='item.group.user.length>0'>
                                        <span v-for='(_item,j) in item.group.user' class='inline'>
                                            <el-tooltip class="item" effect="dark" placement="top">
                                                <div slot="content">{{ESTeacherInfo[_item].name}}</div>
                                                <img :src="ESTeacherInfo[_item].photoUrl" class='img28' alt="">
                                            </el-tooltip>
                                            <span class='el-icon-error closeTea' @click='delEsTeacher(list,key,"group",j)'></span>
                                        </span>
                                    </span>
                                    <span class='addTeacher'  @click='addTeacher(item,"group")'><span class='el-icon-plus'></span></span>
                                </div>
                               
                            </div>
                        </div>
                   </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button" class="btn btn-primary" @click='saveES()'><?php echo Yii::t("message", "OK");?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- SS -->
    <div class="modal fade" id="SSModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static" style="overflow-y: auto;">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <div class='flex'>
                        <div class='flex1'>
                            <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("quote", "Teacher Setting"); ?></h4>
                        </div>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close" ><span aria-hidden="true">&times;</span></button>
                    </div>
                </div>
                <div class="modal-body p24 relative scroll-box" :style="'max-height:'+(height-190)+'px;overflow-y: auto;'">
                    <div class='flex mb24' v-for='(list,index) in addSubSS'>
                        <div class='mt5 color3 font14 fontBold' style='width:80px'>{{list.title}}</div>
                        <div class='flex1' v-if='ssOption[list.value]'>
                            <div class='flex'>
                                <el-select
                                    v-model="list.subject"
                                    multiple
                                    class='flex1'
                                    collapse-tags
                                    size='small'
                                    placeholder="请选择">
                                    <el-option
                                        v-for="(item,i) in ssOption[list.value]"
                                        :key="i"
                                        :label="item.title"
                                        :value="item.course_id">
                                        <span >{{item.course_code }} </span>
                                        <span > {{ item.title }}</span>
                                    </el-option>
                                </el-select>
                                <!-- <button type="button" class="btn btn-default ml16">添加</button> -->
                            </div>
                            <div>
                                <div class='courseTitle flex mt8 align-items' v-for='(item,id) in list.subject'>
                                    <div class='flex1'>
                                        <div class='font12 color3'>{{courseList2[list.value][item].title}}</div>
                                        <div class='mt8'>
                                            <el-tooltip class="item" effect="dark" placement="top">
                                                <div slot="content">{{courseList2[list.value][item].teacher_info.name}}</div>
                                                <img :src="courseList2[list.value][item].teacher_info.photoUrl" alt="" class='img28'>
                                            </el-tooltip>
                                            <span class='font12 color6 ml4'>{{courseList2[list.value][item].teacher_info.name}}</span>
                                        </div>
                                    </div>
                                    <span class='el-icon-circle-close font14' @click='delSubSS(index,id)'></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button" class="btn btn-primary" @click='saveSubSS()'><?php echo Yii::t("message", "OK");?></button>
                </div>
            </div>
        </div>
    </div>
    <!-- es添加老师 -->
    <div class="modal fade" id="authorizedModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("directMessage", "添加老师"); ?></h4>
                </div>
                <div class="modal-body" >
                    <div class='color6 font14'><?php echo Yii::t("global", "Search"); ?></div>
                    <div  class='flex mt16'>
                        <el-select
                            v-model="teacherUid"
                            filterable
                            ref="agentSelect"
                            @hook:mounted="cancalReadOnly" 
                            @visible-change="cancalReadOnly" 
                            remote
                            class='inline-input flex1'
                            reserve-keyword
                            placeholder="<?php echo Yii::t("directMessage", "Key words"); ?>"
                            :remote-method="remoteMethod"
                            prefix-icon="el-icon-search"
                            :loading="loading">
                            <el-option
                                v-for="item in options"
                                :key="item.uid"
                                :label="item.name"
                                class='optionSearch mb8'
                                :value="item.uid">
                                <div class="media">
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)">
                                            <img :src="item.photoUrl" data-holder-rendered="true" class="media-object img-circle imageSearch">
                                        </a>
                                    </div>
                                    <div class="media-body mt5 media-middle">
                                        <h4 class="media-heading font14 color3 text_overflow">{{item.name}}</h4>
                                        <div class="text-muted text_overflow font12">{{ item.hrPosition }}</div>
                                    </div>
                                </div>
                            </el-option>
                        </el-select>
                        <button type="button" class="btn btn-primary ml16"  :disabled='teacherUid==""?true:false' @click='confirmAdmin'><?php echo Yii::t("directMessage", "Proceed to add"); ?></button>
                    </div>
                    <div class='clearfix'></div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>

<script>
    $(document).ready(function () {
    // 通过该方法来为每次弹出的模态框设置最新的zIndex值，从而使最新的modal显示在最前面
        $(document).on('shown.bs.modal', '.modal.in', function(event) {
            var zIndex = 1040 + (10 * $('.modal:visible').length);
                $(this).css('z-index', zIndex);
                setTimeout(function() {
                    $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');
                }, 0);
        }).on('hidden.bs.modal', '.modal', function(event) {
            if ($('.modal.in').size() >= 1) {
                $('body').addClass('modal-open')
            }
        })
    });
    var branchId = '<?php echo $_GET['branchId']?>';
    var height=document.documentElement.clientHeight;
    var  container = new Vue({
        el: "#container",
        data: {
            height:height,
            course:'MAP',
            configData:{},
            newConfigListByGroup:{},
            ESClassInfo:{},
            ESTeacherInfo:{},
            SSCourseTeacher:{},
            category_sub:{},
            classShort:{},
            config:{},
            subjectMap:{},
            value:[],
            options: [],
            teacherUid:'',
            loading: false,
            addSubSS:[],
            ssOption:{},
            courseList2:{},
            eSConfig:{},
            ESList:{},
            addItem:{},
            optionsObj:{},
            studentData:[],
            teacherData:[],
            mapTime:'',
            tableLoading:false,
            studentDataES:[],
            LCETime:'',
        },
        created: function() {
            this.initList()
        },
        methods: {
            translate(text, ...args) {
                return text.replace(/%s/g, () => args.shift());
            },
            initList(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getMAPExamList") ?>',
                    type: "get",
                    dataType: 'json',
                    data:{data:this.temAdd},
                    success: function(data) {
                        if (data.state == 'success') {
                           that.configData=data.data
                           configData=data.data
                           that.newConfigListByGroup=data.data.newConfigListByGroup
                           that.subjectMap=data.data.subjectMap
                           that.category_sub=data.data.category_sub
                           that.SSCourseTeacher=data.data.SSCourseTeacher
                           that.classShort=data.data.classShort
                           that.ESTeacherInfo=data.data.ESTeacherInfo
                           
                           that.$nextTick(() => {
                            that.$refs.radio.$children.forEach((item) => {
                                    item.$refs.radio.removeAttribute("aria-hidden");
                                });
                            });
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            ssSet(){
                this.addSubSS=[]
                for(let i=0;i<this.category_sub.MAP.SS.length;i++){
                    let value=this.category_sub.MAP.SS[i].value
                    if(this.newConfigListByGroup.SS && this.newConfigListByGroup.SS[value]){
                        let numArray = this.newConfigListByGroup.SS[value].config.map(str => parseInt(str));
                        this.addSubSS.push({
                            title:this.category_sub.MAP.SS[i].title,
                            value:this.category_sub.MAP.SS[i].value,
                            subject:numArray
                        })
                    }else{
                        this.addSubSS.push({
                            title:this.category_sub.MAP.SS[i].title,
                            value:this.category_sub.MAP.SS[i].value,
                            subject:[]
                        })
                    }
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getSSClassCourseTeacher") ?>',
                    type: "get",
                    dataType: 'json',
                    success: function(data) {
                        if (data.state == 'success') {
                           that.ssOption=data.data.courseList
                           that.courseList2=data.data.courseList2
                            $('#SSModal').modal('show') 
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            delSubSS(index,i){
                this.addSubSS[index].subject.splice(i,1)
            },
            saveSubSS(){
                let that=this
                const filteredData = this.addSubSS.filter(item => item.subject.length > 0);
                $.ajax({
                    url: '<?php echo $this->createUrl("addSSConfig") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{list:filteredData},
                    success: function(data) {
                        if (data.state == 'success') {
                            $('#SSModal').modal('hide')   
                            that.initList()
                           resultTip({
                                msg: data.message
                            });
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            esSet(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getESConfig") ?>',
                    type: "get",
                    dataType: 'json',
                    success: function(data) {
                        if (data.state == 'success') {
                            that.eSConfig=data.data
                            that.ESList={}
                            let list=JSON.parse(JSON.stringify(that.newConfigListByGroup));
                            for(let key in list.ES){
                                that.ESList[key]={}
                                for(let i=0;i<list.ES[key].length;i++){
                                    let sub=list.ES[key][i].subject
                                    that.ESList[key][sub]=list.ES[key][i]
                                }
                            }
                            that.ESData={}
                            for(let key in data.data.subjectGrade){
                                that.ESData[key]={}
                                for(let i=0;i<data.data.subjectGrade[key].length;i++){
                                    let sub=data.data.subjectGrade[key][i]
                                    that.ESData[key][sub]={
                                        class:[],
                                        group:{
                                            user:[]
                                        },
                                        showClass:'1'
                                    }
                                    if(data.data.classList[sub]){
                                        for(let j=0;j<data.data.classList[sub].length;j++){
                                            that.ESData[key][sub].class.push({
                                                class_id:data.data.classList[sub][j].classid,
                                                class_title:data.data.classList[sub][j].title,
                                                user:[]
                                            })
                                        }
                                    }
                                    if(that.ESList[sub] && that.ESList[sub][key]){
                                        if(that.ESList[sub][key].config_type=='class'){
                                            that.ESData[key][sub].class=that.ESList[sub][key].config
                                            that.ESData[key][sub].showClass='1'
                                        }else{
                                            that.ESData[key][sub].group=that.ESList[sub][key].config
                                            that.ESData[key][sub].showClass='2'
                                        }
                                    }
                                }
                            }
                            $('#ESModal').modal('show') 
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })   
            },
            setClassTab(item){
                // item.showClass=!item.showClass
                this.$forceUpdate()
            },
            addTeacher(item,type,i){
                console.log(item,type,i);
                this.addItem={
                    item:item,
                    type:type,
                    class:i
                }
                this.teacherUid=''
                this.options=[]
                $('#authorizedModal').modal('show')
            },
            cancalReadOnly(onOff) {
                this.$nextTick(() => {
                    if (!onOff) {
                        const Selects = this.$refs
                        if (Selects.agentSelect) {
                            const input = Selects.agentSelect.$el.querySelector('.el-input__inner')
                            input.removeAttribute('readonly')
                        }
                    }
                })
            },
            remoteMethod(query) {
                let that=this
                if (query !== '') {
                    this.loading = true;
                    $.ajax({
                        url: '<?php echo $this->createUrl("teacherSearch") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            searchString:query
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.options = Object.values(data.data) ;
                                that.optionsObj = data.data
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                            that.loading = false;
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.loading = false;
                        },
                    })
                } else {
                    this.options = [];
                }
            },
            confirmAdmin(){
                this.ESTeacherInfo[this.teacherUid]= this.optionsObj[this.teacherUid]
                if(this.addItem.type=='class'){
                    console.log(this.addItem.item);
                    this.addItem.item.class[this.addItem.class].user.push(this.teacherUid)
                }else{
                    this.addItem.item.group.user.push(this.teacherUid)
                }
                $('#authorizedModal').modal('hide')
                this.$forceUpdate()
            },
            delEsTeacher(list,key,type,j,i){
                if(type=='class'){
                    this.ESData[list][key].class[i].user.splice(j, 1)
                }else{
                    this.ESData[list][key].group.user.splice(j, 1)
                }
                this.$forceUpdate()
            },
            checkAllUsersEmpty(array) {
                for (let item of array) {
                    if (item.user != "" && item.user != null) {
                        return false;
                    }
                }
                return true;
            },
            saveES(){
                let that=this
                var data={}
                for(let key in that.ESData){
                    data[key]=[]
                    for(let i in that.ESData[key]){
                        if(that.ESData[key][i].showClass=='1'){
                           var  classed=this.checkAllUsersEmpty(that.ESData[key][i].class)
                           var  groups=true
                        }else{
                           var  classed=true
                            var groups=that.ESData[key][i].group.user!=null && that.ESData[key][i].group.user!=''?false:true 
                        }
                        if(classed && groups){
                        }else{
                            data[key].push({
                                grade: i,
                                config_type:that.ESData[key][i].showClass=='1'?'class':'group',
                                config:that.ESData[key][i].showClass=='1'?that.ESData[key][i].class:that.ESData[key][i].group
                            })
                        }
                        
                    }
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("addESConfig") ?>',
                    type: "post",
                    dataType: 'json',
                    data:{list:data},
                    success: function(data) {
                        if (data.state == 'success') {
                            $('#ESModal').modal('hide')   
                            that.initList()
                           resultTip({
                                msg: data.message
                            });
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            createTable(){
                let that=this
                this.tableLoading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("generateMAPExamList") ?>',
                    type: "post",
                    dataType: 'json',
                    success: function(data) {
                        if (data.state == 'success') {
                           that.studentData=data.data.studentData
                           that.teacherData=data.data.teacherData
                           that.mapTime=data.data.time
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.tableLoading=false
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.tableLoading=false
                    },
                })
            },
            exportData(){
                const today = new Date();
                const year = today.getFullYear();
                let month = today.getMonth() + 1; // 月份是从0开始的，所以我们加1
                let day = today.getDate();
                
                // 补0操作
                month = month < 10 ? '0' + month : month;
                day = day < 10 ? '0' + day : day;
                var title=year + '-' + month + '-' + day+' MAP考试名单.xlsx'
                var workbook = {
                    SheetNames: [],
                    Sheets: {}
                };
                var exportDatas=[]
                for(var i=0;i<this.studentData.length;i++){
                    exportDatas.push(
                        {
                        'School State Code':'',
                        'School Name':this.studentData[i].school_name,
                        'Previous Instructor ID':'',
                        'Instructor ID':this.studentData[i].instructor_id,
                        'Instructor State ID':'',
                        'Instructor Last Name':this.studentData[i].instructor_last_name,
                        'Instructor First Name':this.studentData[i].instructor_first_name,
                        'Instructor Middle Initial':'',	
                        'User Name':this.studentData[i].user_name,	
                        'Email Address':this.studentData[i].email_address,	
                        'Class Name':this.studentData[i].export_class_name,	
                        'Subject':'',	
                        'Previous Student ID':'',	
                        'Student ID':this.studentData[i].student_id,	
                        'Student State ID':'',
                        'Clever ID':'',
                        'Ed-Fi ID':'',	
                        'ClassLink ID':'',
                        'OneRoster ID':'',
                        'Student Information System ID':'',
                        'Student Last Name':this.studentData[i].student_last_name_en,	
                        'Student First Name':this.studentData[i].student_first_name_en,	
                        'Student Middle Initial':'',	
                        'Student Date Of Birth':this.studentData[i].student_birthday,	
                        'Student Gender':this.studentData[i].student_gender,	
                        'Student Grade':this.studentData[i].student_grade,	
                        'Student Ethnic Group Name':this.studentData[i].student_country_name,	
                        'Student User Name':'',	
                        'Student Email':'',	
                        }
                    );
                }
                var worksheet_1=XLSX.utils.json_to_sheet(exportDatas,{
                    origin:'A1',// 从A1开始增加内容
                    header: [
                        'School State Code',
                        'School Name',
                        'Previous Instructor ID',
                        'Instructor ID',
                        'Instructor State ID',
                        'Instructor Last Name',
                        'Instructor First Name',
                        'Instructor Middle Initial',	
                        'User Name',	
                        'Email Address',	
                        'Class Name',	
                        'Subject',	
                        'Previous Student ID',	
                        'Student ID',
                        'Student State ID',
                        'Clever ID',
                        'Ed-Fi ID',	
                        'ClassLink ID',
                        'OneRoster ID',
                        'Student Information System ID',
                        'Student Last Name',	
                        'Student First Name',	
                        'Student Middle Initial',	
                        'Student Date Of Birth',	
                        'Student Gender',	
                        'Student Grade',	
                        'Student Ethnic Group Name',	
                        'Student User Name',	
                        'Student Email'
                    ],
                });
                // 添加第一个工作表
                var sheet_name_1 = 'StandardRoster';
                workbook.SheetNames.push(sheet_name_1);
                workbook.Sheets[sheet_name_1] = worksheet_1;
                // 添加第二个工作表
                var sheet_name_2 = 'Additional Users';
                var teacherData=[];
                for(let i=0;i<this.teacherData.length;i++){
                    teacherData.push({
                        'School State Code':'',
                        'School Name':this.teacherData[i].school_name,
                        'Instructor ID':this.teacherData[i].instructor_id,
                        'Instructor State ID':'',
                        'Last Name':this.teacherData[i].last_name,
                        'First Name':this.teacherData[i].first_name,
                        'Middle Name':this.teacherData[i].middle_name,	
                        'User Name':this.teacherData[i].user_name,	
                        'Email Address':this.teacherData[i].email_address,	
                        }
                    );
                }
                var worksheet_2=XLSX.utils.json_to_sheet(teacherData,{
                    origin:'A1',// 从A1开始增加内容
                    header: [
                        'School State Code',
                        'School Name',
                        'Instructor ID',
                        'Instructor State ID',
                        'Last Name',
                        'First Name',
                        'Middle Name',	
                        'User Name',	
                        'Email Address',	
                    ],
                });
                workbook.SheetNames.push(sheet_name_2);
                workbook.Sheets[sheet_name_2] = worksheet_2;
                const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                const blob = new Blob([wbout], {type: 'application/octet-stream'});
                let link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = title;
                link.click();
                setTimeout(function() {
                    // 延时释放掉obj
                    URL.revokeObjectURL(link.href);
                    link.remove();
                }, 500);
            },
            createTableLc(){
                let that=this
                this.tableLoading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("generateLCExamList") ?>',
                    type: "post",
                    dataType: 'json',
                    success: function(data) {
                        if (data.state == 'success') {
                           that.studentDataES=data.data.list
                           that.LCETime=data.data.time
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.tableLoading=false
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.tableLoading=false
                    },
                })
            },
            exportDataLc(){
                const today = new Date();
                const year = today.getFullYear();
                let month = today.getMonth() + 1; // 月份是从0开始的，所以我们加1
                let day = today.getDate();
                
                // 补0操作
                month = month < 10 ? '0' + month : month;
                day = day < 10 ? '0' + day : day;
                var title=year + '-' + month + '-' + day+' Level Chinese考试名单.xlsx'
                var workbook = {
                    SheetNames: [],
                    Sheets: {}
                };
                var table = document.getElementById('exportTable');
                if (table.querySelector('.el-table__fixed')) {
                    table.removeChild(table.querySelector('.el-table__fixed'));
                }
                if (table.querySelector('.el-table__fixed-right')) {
                    table.removeChild(table.querySelector('.el-table__fixed-right'));
                }
                $(".rule").each(function(){
                    $(this).html('')
                });
                var wb = XLSX.utils.table_to_book(table);
                // 添加第一个工作表
                var sheet_name_1 = 'Daystar '+branchId;
                workbook.SheetNames.push(sheet_name_1);
                workbook.Sheets[sheet_name_1] = wb.Sheets.Sheet1;
                // // 添加第二个工作表
                // if(this.studentDataSS.length!=0){
                //     var sheet_name_2 = 'Daystar SS';
                //     var teacherData=[];
                //     for(let i=0;i<this.studentDataSS.length;i++){
                //         teacherData.push({
                //             'Username':this.studentDataSS[i].student_id,
                //             '中文名字':this.studentDataSS[i].student_cn_name,
                //             '法定名（拼音）':this.studentDataSS[i].student_first_name_en,
                //             '法定姓（拼音）':this.studentDataSS[i].student_last_name_en,
                //             'group_identifier': this.studentDataSS[i].group_identifier1,	
                //             }
                //         );
                //     }
                //     var worksheet_2=XLSX.utils.json_to_sheet(teacherData,{
                //         origin:'A1',// 从A1开始增加内容
                //         header: [
                //             'Username',
                //             '中文名字',
                //             '法定名（拼音）',
                //             '法定姓（拼音）',
                //             'group_identifier',
                //         ],
                //     });
                //     workbook.SheetNames.push(sheet_name_2);
                //     workbook.Sheets[sheet_name_2] = worksheet_2;
                // }
                const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                const blob = new Blob([wbout], {type: 'application/octet-stream'});
                let link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = title;
                link.click();
                setTimeout(function() {
                    // 延时释放掉obj
                    URL.revokeObjectURL(link.href);
                    link.remove();
                }, 500);
            },
        },
    })
</script>