<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yiic message' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE, this file must be saved in UTF-8 encoding.
 *
 * @version $Id: $
 */
return array (
  'Add' => '',
  'Assigned Promot' => '',
  'Assigned products' => '',
  'Delete' => '',
  '不能删除，添加的库存已有订单生成！' => '',
  '为 "{code}" 添加产品' => '',
  '为 "{code}" 添加赠品' => '',
  '商品列表' => '',
  '库存管理' => '',
  '数据保存成功！' => '',
  '新增商品' => '',
  '新增库存' => '',
  '显示' => '',
  '没找到相关产品' => '',
  '隐藏' => '',
);
