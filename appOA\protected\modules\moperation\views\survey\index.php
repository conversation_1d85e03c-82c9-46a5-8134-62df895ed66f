<style>
	[v-cloak] {
		display: none;
	}
	td {
		border-top: none !important;
	}
	
	.progress {
		margin-bottom: 0
	}
</style>
<div class="container-fluid">
	<ol class="breadcrumb">
		<li>
			<?php echo CHtml::link(Yii::t('site','HQ Operations'), array('/moperation'))?>
		</li>
		<li>
			<?php echo CHtml::link(Yii::t('site','问卷管理'), array('index'))?>
		</li>
		<li class="active">
			<?php echo Yii::t('site','问卷管理') ?>
		</li>
	</ol>

	<div class="row">
		<div class="col-md-2 col-sm-2">
			<?php

            $this->widget('zii.widgets.CMenu',array(
                'items'=> $this->getSubMenu(),
                'id'=>'pageCategory',
                'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked text-left background-gray'),
                'activeCssClass'=>'active',
                'itemCssClass'=>''
            ));
            ?>
		</div>
		<div class="col-md-10 col-sm-10" id='survey' v-cloak>

			<div class="form-inline">
				<?php echo CHtml::dropDownList('serviceYear', $serviceYear, $yearList, array('class' => 'form-control','onChange'=>"selectYear(this)")); ?>
				<button type="submit" class="btn btn-primary pull-right" @click='addEdit(0)'>添加</button>
			</div>
			<ul class="list-group mt15">
				<li class="list-group-item" v-for="(item,index) in menuList">
					<div class="oneMenu pull-left"  @click="showToggle(item,index)">
						<span :class="activeClass == index ? 'glyphicon glyphicon-chevron-down':'glyphicon glyphicon-chevron-right'" aria-hidden="true"></span>
						<span  v-if='item.data.length==0'> {{item.title}} （未发布）</span>
						<span v-else> {{item.title}}</span>
					</div>
					<div class=" pull-right">
						<div v-if='item.data.length==0'>
							<button type="button" class="btn btn-default btn-xs" @click='addEdit(schoolkey,item.survey_id)'>编辑信息</button>
							<button type="button" class="btn btn-default btn-xs" @click='updateEdit(schoolkey,item.survey_id)'>编辑提示</button>
							<a :href="'<?php echo $this->createUrl('previewSurvey') ?>?surveyid='+item.survey_id+''" class="btn btn-default btn-xs" role="button" target="_blank">预览问卷</a>
						</div>
						<div v-else>
							<button type="button" class="btn btn-default btn-xs" @click='updateEdit(schoolkey,item.survey_id)'>编辑提示</button>
							<button type="button" class="btn btn-default btn-xs" @click='report(item.survey_id,index)'>生成报告</button>
							<!-- <button type="button" class="btn btn-default btn-xs" v-if='item.has_report==1'>查看报告</button> -->
							<a :href="'<?php echo $this->createUrl('previewSurvey') ?>?surveyid='+item.survey_id+''" class="btn btn-default btn-xs" role="button" target="_blank">预览问卷</a>
						</div>
					</div>
					<div class="clearfix"></div>
					<table class="table table-hover mt15" v-show="item.isSubShow">
						<tr v-if='barlists[item.survey_id]'>
							<td>总进度</td>
							<td>
								<div class="progress">
									<div class="progress-bar" role="progressbar" aria-valuenow="parsefloat(barlists[item.survey_id].complete/barlists[item.survey_id].total)" aria-valuemin="0" aria-valuemax="100" :style="'width:'+ parsefloat(barlists[item.survey_id].complete/barlists[item.survey_id].total) +'%;'">
										（{{barlists[item.survey_id].complete}}/{{barlists[item.survey_id].total}}） {{parsefloat(barlists[item.survey_id].complete/barlists[item.survey_id].total)}}%
									</div>
								</div>
							</td>
							<td></td>
							<td></td>
						</tr>
						<tr v-for="(schooldata,schoolkey,index) in schoolList">

							<td>{{schooldata}}</td>
							<td width="40%">
								<div v-if='item.data[schoolkey]'>
									<template v-if='barlists[item.survey_id]'>
										<div class="progress" v-if='barlists[item.survey_id][schoolkey]'>
											<div class="progress-bar" role="progressbar" aria-valuenow="40" aria-valuemin="0" aria-valuemax="100" :style="'width:'+ parsefloat(barlists[item.survey_id][schoolkey].is_fb/barlists[item.survey_id][schoolkey].count) +'%;'">
												（{{barlists[item.survey_id][schoolkey].is_fb}}/{{barlists[item.survey_id][schoolkey].count}}） {{parsefloat(barlists[item.survey_id][schoolkey].is_fb/barlists[item.survey_id][schoolkey].count)}}%
											</div>
										</div>
										<div class="progress" v-else>
											<div class="progress-bar" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="min-width:5em;">
												（0/0）0%
											</div>
										</div>
									</template>
									<template v-else>
										 <div class="progress">
											<div class="progress-bar" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="min-width:5em;">
												（0/0）0%
											</div>
										</div>
									</template>
								</div>
								<div v-else>未发布</div>
							</td>
							<td>
								<div v-if='item.data[schoolkey]'>
									{{item.data[schoolkey].start_time}} - {{item.data[schoolkey].end_time}}
								</div>
							</td>
							<td class="text-right">
								<template v-if='item.data[schoolkey]'>
									<a :href="'<?php echo $this->createUrl('feedbackList') ?>?surveyId='+item.survey_id+'&schoolId='+schoolkey+'&isFb=1&offset=1'"  target="_blank" class="btn btn-xs btn-default mr5" title="已完成列表"><span class="glyphicon glyphicon-align-justify"></span></a>
									<a href="javascript:;" class="btn btn-xs btn-default mr5"  title="未完成名单" @click='unfinished(schoolkey,item.survey_id,0)'><span class="glyphicon glyphicon-user"></span></a>
									<a href="javascript:;" class="btn btn-xs btn-default" title="发布管理" @click='release(item.title,schooldata,item.survey_id,item.data,schoolkey)'><span class="glyphicon glyphicon-upload"></span></a>
								</template>
								<template v-else>
									<a href="javascript:;" class="btn btn-xs btn-default" title="发布管理" @click='release(item.title,schooldata,item.survey_id,item.data,schoolkey)'><span class="glyphicon glyphicon-upload"></span></a>
								</template>
							</td>
						</tr>
					</table>
				</li>
			</ul>
			<!-- 添加 -->
			<div class="modal fade bs-example-modal-lg" id="addEdit" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" data-backdrop="static">
				<div class="modal-dialog ">
					<div class="modal-content">
						<div class="modal-header">
							<button type="button" class="close" data-dismiss="modal" aria-hidden="true">
							&times;
						</button>
							<h4 class="modal-title" id="myModalLabel">添加问卷</h4>
						</div>
						<div class="modal-body">
							<div class="form-horizontal">
								<div class="form-group">
									<label for="inputEmail3" class="col-sm-2 control-label">中文名称</label>
									<div class="col-sm-10">
										<input type="text" class="form-control">
									</div>
								</div>
								<div class="form-group">
									<label for="inputEmail3" class="col-sm-2 control-label">英文名称</label>
									<div class="col-sm-10">
										<input type="text" class="form-control">
									</div>
								</div>
								<div class="form-group">
									<label for="inputEmail3" class="col-sm-2 control-label">服务学年</label>
									<div class="col-sm-10">
										<select class="form-control">
											<option>家长</option>
											<option>员工</option>
										</select>
									</div>
								</div>
								<div class="form-group">
									<label for="inputEmail3" class="col-sm-2 control-label">调查对象</label>
									<div class="col-sm-10">
										<select class="form-control">
											<option>家长</option>
											<option>员工</option>
										</select>
									</div>
								</div>
								<div class="form-group">
									<label for="inputEmail3" class="col-sm-2 control-label">问卷类型</label>
									<div class="col-sm-10">
										<select class="form-control">
											<option value="1">常规家长问卷</option>
											<option value="2">下学年返校调查问卷</option>
											<option value="3">其他</option>
										</select>
									</div>
								</div>
								<div class="form-group">
									<label for="inputEmail3" class="col-sm-2 control-label">匿名</label>
									<div class="col-sm-10">
										<input type="checkbox" value="">
									</div>
								</div>
								<div class="form-group">
									<label for="inputEmail3" class="col-sm-2 control-label">简要说明</label>
									<div class="col-sm-10">
										<textarea class="form-control" rows="3"></textarea>
									</div>
								</div>
								<div class="form-group">
									<label for="inputEmail3" class="col-sm-2 control-label">选择模板</label>
									<div class="col-sm-10">
										<select multiple class="form-control">
											<option>1</option>
											<option>2</option>
											<option>3</option>
											<option>4</option>
											<option>5</option>
										</select>
									</div>
								</div>
							</div>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
							<button type="button" class="btn btn-primary">添加</button>
						</div>
					</div>
				</div>
			</div>
			<!-- 更新 -->
			<div class="modal fade bs-example-modal-lg" id="updateSurvey" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" data-backdrop="static">
				<div class="modal-dialog modal-lg">
					<div class="modal-content">
						<div class="modal-header">
							<button type="button" class="close" data-dismiss="modal" aria-hidden="true">
							&times;
						</button>
							<h4 class="modal-title" id="myModalLabel">编辑提示信息</h4>
						</div>
						<div class="modal-body">
							<div class="form-horizontal">
								<div class="form-group">
									<label for="inputEmail3" class="col-sm-2 control-label">常规问卷中文提示</label>
									<div class="col-sm-10">
										<textarea class="form-control" rows="3" v-model="current_survey.common_cn">{{current_survey.common_cn}}</textarea>
									</div>
								</div>
								<div class="form-group">
									<label for="inputEmail3" class="col-sm-2 control-label">常规问卷英文提示</label>
									<div class="col-sm-10">
										<textarea class="form-control" rows="3" v-model="current_survey.common_en">{{current_survey.common_en}}</textarea>
									</div>
								</div>
								<div class="form-group">
									<label for="inputEmail3" class="col-sm-2 control-label">教师中文评估提示</label>
									<div class="col-sm-10">
										<textarea class="form-control" rows="3" v-model="current_survey.teacher_cn">{{current_survey.teacher_cn}}</textarea>
									</div>
								</div>
								<div class="form-group">
									<label for="inputEmail3" class="col-sm-2 control-label">教师评估英文提示</label>
									<div class="col-sm-10">
										<textarea class="form-control" rows="3" v-model="current_survey.teacher_en">{{current_survey.teacher_en}}</textarea>
									</div>
								</div>
								<div class="form-group">
									<label for="inputEmail3" class="col-sm-2 control-label">教师中文标题提示</label>
									<div class="col-sm-10">
										<textarea class="form-control" rows="3" v-model="current_survey.teacher_title_cn">{{current_survey.teacher_title_cn}}</textarea>
									</div>
								</div>
								<div class="form-group">
									<label for="inputEmail3" class="col-sm-2 control-label">教师英文标题提示</label>
									<div class="col-sm-10">
										<textarea class="form-control" rows="3" v-model="current_survey.teacher_title_en">{{current_survey.teacher_title_en}}</textarea>
									</div>
								</div>
							</div>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
							<button type="button" class="btn btn-primary" @click="updateSurveyDesc(this)">更新</button>
						</div>
					</div>
				</div>
			</div>
			<!--发布-->
			<div class="modal fade bs-example-modal-lg" id="release" tabindex="-1" role="dialog" data-backdrop="static">
				<div class="modal-dialog ">
					<?php $form = $this->beginWidget('CActiveForm', array(
				        'id' => 'expense-forms',
				        'enableAjaxValidation' => false,
				        'action' => ($model->id) ? $this->createUrl('expenesAdd', array('expenesId' => $model->id)) : $this->createUrl('updateSurvey'),
				        'htmlOptions' => array('class' => 'J_ajaxForm', 'role' => 'form', 'enctype' => "multipart/form-data"),
				    )); ?>
					<div class="modal-content">
						<div class="modal-header">
							<button type="button" class="close" data-dismiss="modal" aria-hidden="true">
							&times;
						</button>
							<h4 class="modal-title" id="myModalLabel">
							发布{{releaseText}}{{schooldata}}问卷
						</h4>
						</div>
						<div class="modal-body">
							<input type="text" :value="survey_id" name='surveyid' class="hidden">
							<input type="text" :value='schoolkey' name='schoolid' class="hidden">
							<div class="form-group">
								<label for="exampleInputEmail1">填写前文字提示-中文</label>
								<textarea class="form-control" rows="3" name='SurveyDetail[before_memo_cn]' :value='releaseData.before_memo_cn'></textarea>
							</div>
							<div class="form-group">
								<label for="exampleInputEmail1">填写前文字提示-英文</label>
								<textarea class="form-control" rows="3" name="SurveyDetail[before_memo_en]" :value='releaseData.before_memo_en'></textarea>
							</div>
							<div class="form-group">
								<label for="exampleInputEmail1">填写中文字提示-中文</label>
								<textarea class="form-control" rows="3" name="SurveyDetail[process_memo_cn]" :value='releaseData.process_memo_cn'></textarea>
							</div>
							<div class="form-group">
								<label for="exampleInputEmail1">填写中文字提示-英文</label>
								<textarea class="form-control" rows="3" name="SurveyDetail[process_memo_en]" :value='releaseData.process_memo_en'></textarea>
							</div>
							<div class="form-group">
								<label for="exampleInputEmail1">填写后文字提示-中文</label>
								<textarea class="form-control" rows="3" name="SurveyDetail[after_memo_en]" :value='releaseData.after_memo_en'></textarea>
							</div>
							<div class="form-group">
								<label for="exampleInputEmail1">填写后文字提示-英文</label>
								<textarea class="form-control" rows="3" name='SurveyDetail[after_memo_cn]' :value='releaseData.after_memo_cn'></textarea>
							</div>
							<div class="form-group">
								<label for="exampleInputEmail1">发布区间</label>
								<div class="form-inline">
									<input type="text" class="form-control form-group" id="startdatepicker" placeholder="请选择开始日期" name="SurveyDetail[start_time]" :value='schooldate.start_time'>
									<label>-</label>
									<div class="form-group">
										<input type="text" class="form-control form-group" id="enddatepicker" placeholder="请选择结束日期" name='SurveyDetail[end_time]' :value='schooldate.end_time'>
									</div>
								</div>
							</div>
							<div class="form-group">
								<label for="exampleInputEmail1">是否发布</label>
								<div class="form-group">
									<label class="radio-inline">
									<input type="radio" id="inlineRadio1" value="1" name='SurveyDetail[is_published]' checked="checked">是
								</label>
									<label class="radio-inline">
									<input type="radio" id="inlineRadio2" value="0"  name='SurveyDetail[is_published]'>否
								</label>
								</div>
							</div>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
							<button type="button" class="btn btn-primary J_ajax_submit_btn">发布</button>
						</div>
					</div>
					<?php $this->endWidget(); ?>
				</div>
			</div>
			<!--未完成名单-->
			<div class="modal fade bs-example-modal-lg" id="unfinished" tabindex="-1" role="dialog" data-backdrop="static">
				<div class="modal-dialog modal-lg" >
					<div class="modal-content">
						<div class="modal-header">
							<button type="button" class="close" data-dismiss="modal" aria-hidden="true">
							&times;
						</button>
							<h4 class="modal-title" id="myModalLabel">
							查看未提交问卷孩子名单
						</h4>
						</div>
						<div class="modal-body">
							<div class="panel panel-default" v-for='(pop,key,index) in unfinishedlist'>
							    <div class="panel-heading">{{pop.classTitle}}</div>
							    <div class="panel-body">
							    	<div class="col-xs-12 col-sm-12 col-md-12 col-lg-4 mb5" v-for='(child,index) in pop.data'>{{child}}</div>
							    </div>
							</div>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<script>
//添加问卷 
//已完成人员编辑  备注

	$(function() {
		$("#startdatepicker").datepicker({
			dateFormat: "yy-mm-dd",
		});
		$("#enddatepicker").datepicker({
			dateFormat: "yy-mm-dd",
		});
	});
	var datas = <?php echo json_encode($surveyData) ?>;
	var lists = <?php echo json_encode($data) ?>;
	var schoolList = <?php echo json_encode($schoolList) ?>;
	console.log(schoolList)
	var survey = new Vue({
		el: "#survey",
		data: {
			menuList: datas,
			schoolList: schoolList,
			barlists: lists,
			activeClass: 0,
			releaseText: '',
			schooldata: '',
			survey_id: '',
			schooldate: '',
			addlist:'',
			unfinishedlist:'',
			schoolkey:'',
			releaseData:'',
			current_survey: {
				'common_cn': '',
				'common_en': '',
				'teacher_cn': '',
				'teacher_en': '',
				'teacher_title_cn': '',
				'teacher_title_en': '',
			},

		},
		created: function() {
			for(var i=0;i<this.menuList.length;i++){
				Vue.set(this.menuList[i], 'isSubShow', false)
			}
			Vue.set(this.menuList[0], 'isSubShow', true)
		},
		computed: {},
		methods: {
			parsefloat(num) {
				return Number(num * 100).toFixed(2);
			},
			reverse(){
				return this.yearList.reverse()
			},
			
			showToggle: function(item, ind) {
				this.activeClass = ind;
				this.menuList.forEach(i => {
					console.log(this.menuList[ind])
					if(i.isSubShow !== this.menuList[ind].isSubShow) {
						i.isSubShow = false;
					}
				});
				item.isSubShow = !item.isSubShow;
			},
			release(text, school, survey_id,date,schoolkey) {
				$.ajax({
			        url: "<?php echo $this->createUrl('updateSurvey')?>",
			        type: 'post',
			        dataType: 'json',
			        data:{
			        	surveyid:survey_id,
			        	schoolid:schoolkey
			        },
			        success:function(data){
			           console.log(data)
		            	if(data.state=='success'){
		            		survey.releaseData=data.data
		            		$('#release').modal('show')
		            	}else{
		            		resultTip({
								error: 'warning',
								msg:data.message
							});
		            	}
			        },
			        error:function(data) {
			        	console.log(data)
			            alert('请求错误')
			        }
			    })
				this.schooldata = school
				this.releaseText = text
				this.survey_id = survey_id
				this.schoolkey=schoolkey
				if(date[schoolkey]){
					this.schooldate = date[schoolkey]
				}else{
					var myDate = new Date();
					var year=myDate.getFullYear();
					var month=myDate.getMonth()+1;
					var day=myDate.getDate();
					if (month < 10 ) {
		                month = '0' + month;
		            }
		            if (day < 10) {
		              day = '0' + day;
		            } 
					var dates = new Date(year, month, day);
		            dates.setMonth((dates.getMonth()-1) + 2);
		            var years = dates.getFullYear();
		            var mons = dates.getMonth()+1;
		            var days = dates.getDate();
		            if (mons < 10 ) {
		                mons = '0' + mons;
		            }
		            if (days < 10) {
		              days = '0' + days;
		            }
					this.schooldate = Object.assign({},this.schooldate)
					Vue.set(this.schooldate, 'start_time', year+'-'+month+"-"+day)
					Vue.set(this.schooldate, 'end_time', years + '-' + mons + '-' + days)
				}
				
			},
			addEdit(school,id){
				console.log(school)
				console.log(id)
				$('#addEdit').modal('show')
			},
			updateEdit(school,id){
				console.log(school)
				console.log(id)
				console.log(datas)
				for(var i=0;i<datas.length;i++){
					if (datas[i].survey_id == id) {
						this.current_survey = datas[i]
						console.log(this.current_survey)
					}
				}
				$('#updateSurvey').modal('show')
			},
			updateSurveyDesc(btn){
				$.ajax({
			        url: "<?php echo $this->createUrl('updateSurveyDesc')?>",
			        type: 'post',
			        dataType: 'json',
			        data: this.current_survey,
			        success:function(data){
			           console.log(data)
		            	if(data.state=='success'){
		            		resultTip({"msg": data.message})
							$("#updateSurvey").modal('hide');
		            	}else{
		            		resultTip({
								error: 'warning',
								msg:data.message
							});
		            	}
			        },
			        error:function(data) {
			            alert('请求错误')
			        }
			    })
			},
			report(surveyid,index){
				console.log(index)
				$.ajax({
			        url: "<?php echo $this->createUrl('generalSurveyReport')?>",
			        type: 'post',
			        dataType: 'json',
			        data:{
			        	surveyid:surveyid,
			        },
			        success:function(data){
			           console.log(data)
		            	if(data.state=='success'){
		            		resultTip({"msg": data.message})
		            		Vue.set(survey.menuList[index],'has_report','1')
		            	}else{
		            		resultTip({
								error: 'warning',
								msg:data.message
							});
		            	}
			        },
			        error:function(data) {
			            alert('请求错误')
			        }
			    })
			},
			unfinished(school,id,state){
				$.ajax({
			        url: "<?php echo $this->createUrl('feedbackList')?>",
			        type: 'post',
			        dataType: 'json',
			        data:{
			        	schoolId:school,
			        	surveyId:id,
			        	isFb:state
			        },
			        success:function(data){
			           console.log(data)
		            	if(data.length==0){
		            		resultTip({
								error: 'warning',
								msg:'暂无未完成人员'
							});
		            	}else{
		            		survey.unfinishedlist=data
		           			$('#unfinished').modal('show')
		            	}
			        },
			        error:function(data) {
			            alert('请求错误')
			        }
			    })
				
			}
		},
	})
	function cbUpdateSurvey(data){
		setTimeout(function(){ 
			window.location.reload();
		},1000)
	}
	function selectYear(obj){
		window.location.href='<?php echo $this->createUrl('index') ?>?serviceYear='+$(obj).val()+''
	}
</script>