<?php

/* 
 * 退费表单
 */
class InvoiceChangeThreeForm extends CFormModel
{
    public $startdate;
    public $enddate;
    public $amount;
    public $invoice_id;
    public $title;
    public function rules(){
        return array(
			array('title, startdate, enddate, amount,invoice_id', 'required'),
            array('startdate, enddate,invoice_id', 'numerical', 'integerOnly'=>true),
            array('amount','numerical','min'=>0,'tooSmall'=>Yii::t("workflow", "金额大于 [ 0 ]"))
		);
    }
    
    public function attributeLabels(){
        return array(
			'startdate'=>Yii::t("workflow", "开始日期"),
			'enddate'=>Yii::t("workflow", "结束日期"),
			'amount'=>Yii::t("workflow", "金额"),
		);
    }
    
    
}
