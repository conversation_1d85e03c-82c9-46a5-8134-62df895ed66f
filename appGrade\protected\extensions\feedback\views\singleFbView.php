<dl class="indent fb">
    <dt>
    <?php
    $staff_photo = Mims::CreateUploadUrl('infopub/staff/blank.gif');
    if (isset($fb->staffInfo->staff_photo) && $fb->staffInfo->staff_photo != "") {
        $staff_photo = Mims::CreateUploadUrl( 'infopub/staff/' . $fb->staffInfo->staff_photo );
    }
    if ($fb->com_user_type != 1) {
        $staff_photo = Mims::CreateUploadUrl( 'users/thumbs/' . $fb->userWithProfile->user_avatar );
    }
    echo CHtml::image($staff_photo, $fb->userWithProfile->getName(), array("class" => "face"));
    ?>
    </dt>
    <dd>
        <h5>
            <?php echo $fb->userWithProfile->getName(); ?>
            <?php echo addColon(Yii::t("portfolio", "said")); ?>
        </h5>
        <p>
            <?php echo Yii::app()->format->ntext($fb->com_content); ?>
            <span><?php echo Mims::formatDateTime($fb->com_created_time, 'medium', 'short'); ?> </span>
        </p>
    </dd>
</dl>