<?php
$H = array('08' => '08', '09' => '09', 10 => 10, 11 => 11, 12 => 12, 13 => 13, 14 => 14, 15 => 15, 16 => 16, 17 => 17, 18 => 18, 19 => 19, 20 => 20);
$S = array('00' => '00', '05' => '05', 10 => 10, 15 => 15, 20 => 20, 25 => 25, 30 => 30, 35 => 35, 40 => 40, 45 => 45, 50 => 50, 55 => 55);
$HourS = array(
    'start' => array(
        'stat' => '08',
        'end' => '00',
    ),
    'end' => array(
        'stat' => '09',
        'end' => '00',
    )
);


?>
<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
    </button>
    <h4 class="modal-title" id="exampleModalLabel"><?php echo $model->id ? '更新：' : '添加：'; ?></h4>
</div>
<?php $form = $this->beginWidget('CActiveForm', array(
    'id' => 'course-form',
    'enableAjaxValidation' => false,
    'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form'),
)); ?>
<div class="modal-body">
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'title_cn'); ?></label>

        <div class="col-xs-10">
            <?php echo $form->textField($model, 'title_cn', array('maxlength' => 255, 'class' => 'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'title_en'); ?></label>

        <div class="col-xs-10">
            <?php echo $form->textField($model, 'title_en', array('maxlength' => 255, 'class' => 'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'unit_price'); ?></label>

        <div class="col-xs-10">
            <?php echo $form->textField($model, 'unit_price', array('maxlength' => 255, 'class' => 'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, '是否是自有课程'); ?> <span class="required">*</span></label>
        <div class="col-xs-4">
            <?php echo $form->dropDownList($model, 'course_provider', array(10=> '是', 20=>'否'), array('class' => 'form-control', 'empty' => Yii::t('asa','请选择'))); ?>

        </div>
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, '是否包含助教薪资'); ?></label>
        <div class="col-xs-4">
            <?php echo $form->dropDownList($model, 'assistant_fee', array(10 => '是', 20 => '否'), array('class' => 'form-control', 'empty' => Yii::t('asa','请选择'))); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, '最大开课人数'); ?> <span class="required">*</span></label>
        <div class="col-xs-4">
            <?php echo $form->textField($model, 'capacity', array('maxlength' => 255, 'class' => 'form-control')); ?>
        </div>
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'capacity_min'); ?></label>
        <div class="col-xs-4">
            <?php echo $form->textField($model, 'capacity_min', array('maxlength' => 255, 'class' => 'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'age_min'); ?></label>

        <div class="col-xs-4">
            <?php echo $form->dropDownList($model, 'age_min', $ageConfigs, array('class' => 'form-control', 'empty' => Yii::t('asa','请选择'))); ?>
        </div>
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'age_max'); ?></label>

        <div class="col-xs-4">
            <?php echo $form->dropDownList($model, 'age_max', $ageConfigs,  array('class' => 'form-control', 'empty' => Yii::t('asa','请选择'))); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"></label>
        <div class="col-xs-10">
            <div><?php echo Yii::t('asa','最小和最大适合年龄选项必须一致：同为年龄或年级') ?></div>
        </div>
    </div>
    <div class="form-group">
    <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, '是否开启年龄检查'); ?></label>
        <div class="col-xs-10">
            <?php echo $form->dropDownList($model, 'age_check', array(0 => '否', 1 => '是'), array('class' => 'form-control', 'empty' => Yii::t('asa','是否开启年龄检查'))); ?>
        </div>
    </div>
    <!-- 开课时间 -->
    <div class="form-group">
        <label class="col-sm-2 control-label required"><?php echo Yii::t('asa', '上课时间') ?> * </label>
        <div class="col-xs-10">
            <?php echo $form->dropDownList($model, 'schedule_id', $scheduleArray, array('class' => 'form-control', 'empty' => Yii::t('asa','请选择'))); ?>
        </div>
    </div>

    <div class="form-group">
        <label class="col-sm-2 control-label required"><?php echo Yii::t('asa', '退费截至') ?> * </label>
        <div class="col-xs-6">
            <?php echo $form->radioButtonList($model, "refundClassStatus", array(1 => '按时间: 截止到某天', 2 => '按课时: (在某节课开始之前)'), array('template' => "<span class='classTimeSelect'> {input} {label} </span>", "separator" => "&nbsp;")); ?>
        </div>

        <div class="col-xs-4">
            <?php echo $form->textField($model, 'refundClassIndexTime', array('class' => 'form-control','style' => 'display:none','disabled' => 'disabled', 'id'=>"refundClassIndexTime")); ?>
        </div>

        <div class="col-xs-4">
            <?php echo $form->dropDownList($model, 'refundClassIndex', AsaCourseGroup::getRefund(), array('class' => 'form-control','style' => 'display:none','disabled' => 'disabled', 'empty' => Yii::t('asa','请选择退费截止选项'))); ?>
        </div>

    </div>

    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'status'); ?></label>

        <div class="col-xs-10">
            <?php echo $form->dropDownList($model, 'status', array(1 => '家长可见可购买', 2 => '家长可见但不能购买' , 0 => '家长不可见'), array('class' => 'form-control', 'empty' => Yii::t('asa','请选择'))); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'gid'); ?></label>

        <div class="col-xs-10">
            <?php echo $form->dropDownList($model, 'gid', $groupArray, array('class' => 'form-control')); ?>
        </div>
    </div>
</div>
<div class="modal-footer">
    <span id="start_fail_info" class="pull-left text-warning" style="display: none;"><i
            class="glyphicon glyphicon-remove text-warning"></i><?php echo Yii::t('asa', '当天该时间段已有课程') ?></span>
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
    <button type="button" class="btn btn-default"
            data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
</div>
<?php $this->endWidget(); ?>

<script>
    var kindex = 9999;

    function AddTime(date) {
        var week = $('#timelist #selectweek').find("option:selected").text();
        var weekval = $('#timelist #selectweek').find("option:selected").val();
        var courseready_s = $('#courseready_s').val();
        var courseready_e = $('#courseready_e').val();
        var courseend_s = $('#courseend_s').val();
        var courseend_e = $('#courseend_e').val();
        var startflag = true;
        if (courseready_s > courseend_s) {
            startflag = 0;
        }
        if (courseready_s == courseend_s && courseready_e >= courseend_e) {
            startflag = 0;
        }
        $.each($('.has_been_added'), function (i, n) { //判断当天开始时间是否重复
            if ($(n).attr('_value') == weekval) {
                if ($(n).find('.starttime').attr('_value') === (courseready_s + ":" + courseready_e)) {
                    startflag = false;
                }
            }
        });

        if (startflag) {
            $('#start_fail_info').hide();
            $("#" + date).append('<div class="form-inline mb5 ml5 has_been_added" _value="' + weekval + '" ><label class="mr10">' + week + '</label><label class="mr5">开始</label><input type="text" class="form-control starttime" name="period[' + weekval + '][' + kindex + '][startTime]" _value="' + courseready_s + ':' + courseready_e + '"value="' + courseready_s + ":" + courseready_e + '" readonly/><label class="mr5 ml5">结束</label><input type="text" class="form-control" name="period[' + weekval + '][' + kindex + '][endTime]" value="' + courseend_s + ":" + courseend_e + '" readonly/><button class="btn btn-danger btn-sm ml5 removetimes"><i class="glyphicon glyphicon-remove"></i></button></div>');
            kindex++;
        } else if (startflag === 0) {
            $('#start_fail_info').html('<i class="glyphicon glyphicon-remove text-warning"></i> 时间段添加错误').show();
        } else {
            $('#start_fail_info').html('<i class="glyphicon glyphicon-remove text-warning"></i> 当天该时间段已有课程').show();
        }

    }
    $("body").on("click", ".removetimes", function (e) {
        $(this).parent().remove();
        kindex--;
    });
    $('.classTimeSelect').on('click',':radio',function(){
        if($(this).val() == 1){
            $('#refundClassIndexTime').removeProp('disabled').show();
            $('#AsaCourse_refundClassIndex').prop('disabled','disabled').hide();
        }else {
            $('#refundClassIndexTime').prop('disabled','disabled').hide();
            $('#AsaCourse_refundClassIndex').removeProp('disabled').show();
        }
    });
    if($('#AsaCourse_refundClassStatus').find(':radio:checked').val() == 1){
        $('#refundClassIndexTime').removeProp('disabled').show();
        $('#AsaCourse_refundClassIndex').prop('disabled','disabled').hide();
    }else if ($('#AsaCourse_refundClassStatus').find(':radio:checked').val() == 2){
        $('#refundClassIndexTime').prop('disabled','disabled').hide();
        $('#AsaCourse_refundClassIndex').removeProp('disabled').show();
    }
    $('#refundClassIndexTime').datepicker({
        changeMonth: true,
        changeYear: true,
        dateFormat:'yy-mm-dd'
    });

</script>