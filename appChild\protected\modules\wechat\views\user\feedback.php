
<div class="weui_cells_title"><?php echo Yii::t('portfolio', 'Journal Feedback & Response') .' '. Yii::t('portfolio', 'Week :weeknumber', array(':weeknumber'=>$weeknum)); ?></div>
<div class="weui_cells">
		<?php foreach ($feedbacks[0] as $id => $feedback): ?>
			<div class="mychat_box_right">
                <div class="mychat_img_box">
	            	<?php echo CHtml::image(Mims::CreateUploadUrl($feedback['avatar'])) ?>
	            </div>
	            <div class="mychat_box_talk">
	                <div><?php echo CHtml::encode($feedback['content']); ?></div>
	                <p><?php echo date('Y/m/d', $feedback['time']) .' '. $feedback['name'];?></p>
                </div>
            </div>
			<?php foreach ($feedbacks[$id] as $key => $value):?>
				<div class="<?php echo $value['type'] ? 'mychat_box_left' : 'mychat_box_right'; ?>">
	                <div class="mychat_img_box">
	                	<?php echo CHtml::image(Mims::CreateUploadUrl($value['avatar'])) ?>
	                </div>
	                	<div class="mychat_box_talk">
		                	<div><?php echo CHtml::encode($value['content']); ?></div>
		                    <p><?php echo date('Y/m/d', $value['time']) .' '. $value['name']; ?></p>
	                	</div>
	            </div>
			<?php endforeach; ?>
			<div class="weui_cells">
				<div class="weui_cell">
					<div class="weui_cell_bd weui_cell_primary" id ="feedback">
						<textarea class="weui_textarea" placeholder="<?php echo Yii::t('portfolio', 'Make a comment');?>" id="content_<?php echo $id;?>" rows="2"></textarea>
						<!-- <div class="weui_textarea_counter"><span>0</span>/50</div> -->
					</div>
				</div>
			</div>
			<div class="weui_btn_area">
				<a class="weui_btn weui_btn_primary" href="javascript:" onclick="reply(<?php  echo $id  ?>)"><?php echo Yii::t('global', 'Submit'); ?></a>
			</div>
		<?php endforeach; ?>
</div>
<script>
function reply(id){
	$('#loadingToast').show();
	var content = $('#content_'+id).val();
	$.ajax({
		type: "POST",
		url: '<?php echo $this->createUrl('saveFeedback'); ?>',
		data: {content:content,id:id},
		dataType:"json",
		success:function(data){
			if(data.state == 'success'){
				showMessage('<?php echo Yii::t('global', 'Success!'); ?>');
				location.reload();
			}else{
				showTips('<?php echo Yii::t('global', 'Failed!'); ?>', data.message);
			}
		},
		complete:function () {
			$('#loadingToast').hide();
		}
	});
}
</script>