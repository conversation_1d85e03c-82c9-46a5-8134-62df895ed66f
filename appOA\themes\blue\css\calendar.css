#calendar-detail{padding: 1em;}
.ui-datepicker-calendar tbody td a, .ui-datepicker-calendar tbody td span{text-align: center;font-family:arial;}
a.ui-state-default{color:#333}
.ui-datepicker-week-end a.ui-state-default{color:#c1c1c1 !important;}
.ui-widget{font-weight: inherit !important;font-family: inherit !important}
.ui-widget input,.ui-widget select,.ui-widget textarea,.ui-widget button{font-family:inherit !important}
.ui-datepicker select.ui-datepicker-month, .ui-datepicker select.ui-datepicker-year {width:35%}
.dayflag .ui-state-default{
    background: #e6e6e6 url("https://b4oss01.ivyonline.cn/images01/images/days-bg.gif") 50% 50% no-repeat;
}
.dayflag-1030 .ui-state-default{
    background-position: 50% -26px !important;
    font-weight: bold !important;
    color:#333333 !important;
}
.dayflag-1020 .ui-state-default{ /*fullday event*/
    background-position: 50% -52px;
    color:#ffffff;
    border-top: 0;
    border-left: 0;
}
.dayflag-1010 .ui-state-default{ /*fullday holiday*/
    background-position: 50% -78px;
    color:#ffffff;
}
.dayflag-2020 .ui-state-default{ /*halfday event*/
    background-position: 50% -130px;
    color:#2C5700;
}
.dayflag-2010 .ui-state-default{ /*halfday holiday*/
    background-position: 50% -156px;
    color:#2C5700;
}
.days-list dl a.hover{
    visibility: hidden;
}
.days-list dt:hover a.hover{
    visibility: visible;
}