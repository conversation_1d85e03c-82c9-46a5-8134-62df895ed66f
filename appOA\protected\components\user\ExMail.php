<?php
class ExMail
{
    public $URLs;
    public $cacheId;
    private $adminId = 'superdude';
    private $key = '8662f9cd304153ae7b0cf8df900e8332';
    public $tokenData;

    public function __construct(){
        $this->URLs = array(
            'token' => 'https://exmail.qq.com/cgi-bin/token',
            'user-sync' => 'http://openapi.exmail.qq.com:12211/openapi/user/sync',
            'party-sync' => 'http://openapi.exmail.qq.com:12211/openapi/party/sync',
        );
        $this->cacheId['access_token'] = 'exmail-access-token';
        $this->cacheId['token_type'] = 'exmail-token-type';
    }

    public function getToken(){
        $accessToken = Yii::app()->cache->get($this->cacheId['access_token']);
        $tokenType = Yii::app()->cache->get($this->cacheId['token_type']);
        if ( $accessToken === false) {
            $params = array(
                'client_id' => $this->adminId,
                'client_secret' => $this->key,
                'grant_type' => 'client_credentials'
            );
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $this->URLs['token']);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

            $result = CJSON::decode(curl_exec($ch));
            if( is_array($result) && isset($result['access_token']) && isset($result['token_type']) && isset
                ($result['expires_in']) ) {
                $expire = max($result['expires_in'] - 100, 100);
                Yii::app()->cache->set($this->cacheId['access_token'], $result['access_token'], $expire);
                Yii::app()->cache->set($this->cacheId['token_type'], $result['token_type'], $expire);
                $this->tokenData = array(
                    'access_token' => $result['access_token'],
                    'token_type' => $result['token_type']
                );
            }
        } else {
            $this->tokenData = array(
                'access_token' => $accessToken,
                'token_type' => $tokenType
            );
        }
        return $this->tokenData;
    }

    public function AddUser($postParams=array()){
        if($postParams){
            $this->getToken();
            $ch = curl_init();
            $url = $this->URLs['user-sync'];
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_HTTPHEADER,array('Authorization: ' . $this->tokenData['token_type'] . ' ' .
            $this->tokenData['access_token']));
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postParams);
            $result = curl_exec($ch);
            return $result;
        }
    }
}
