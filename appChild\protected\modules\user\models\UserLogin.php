<?php

/**
 * LoginForm class.
 * LoginForm is the data structure for keeping
 * user login form data. It is used by the 'login' action of 'SiteController'.
 */
class UserLogin extends CFormModel
{
	public $username;
	public $password;
	public $rememberMe;
	public $redirectToOA=false;

	/**
	 * Declares the validation rules.
	 * The rules state that username and password are required,
	 * and password needs to be authenticated.
	 */
	public function rules()
	{
		return array(
			// username and password are required
			array('username, password', 'required'),
//			array('username', 'email'),
			// rememberMe needs to be a boolean
			array('rememberMe', 'boolean'),
			// password needs to be authenticated
			array('password', 'authenticate'),
		);
	}

	/**
	 * Declares attribute labels.
	 */
	public function attributeLabels()
	{
		return array(
			'rememberMe'=>Yii::t("user", "Remember me next time"),
			'username'=>Yii::t("user", "Email"),
			'password'=>Yii::t("user", "Password"),
		);
	}

	/**
	 * Authenticates the password.
	 * This is the 'authenticate' validator as declared in rules().
	 */
	public function authenticate($attribute,$params)
	{
		if(!$this->hasErrors())  // we only want to authenticate when no input errors
		{
			$identity=new UserIdentity($this->username,$this->password);
			$identity->authenticate();
			switch($identity->errorCode)
			{
				case UserIdentity::ERROR_EMAIL_INVALID:
				case UserIdentity::ERROR_USERNAME_INVALID:
                default:
					$this->addError("username",Yii::t("user", "Email does not exist. :supportLink",array(
                        ":supportLink"=>Mims::unIvy()?'':CHtml::link(Yii::t("user", "Need help?"), Yii::app()->controller->module->lostAccountUrl, array("target"=>"_blank"))
                        )));
					break;
				case UserIdentity::ERROR_STATUS_NOTACTIV:
					$this->addError("status",Yii::t("user", "You account is not activated."));
					break;
				case UserIdentity::ERROR_IS_STAFF:
					$this->redirectToOA = true;
					$this->addError("status",Yii::t("user", "You account is for staff."));
					break;
				case UserIdentity::ERROR_PASSWORD_INVALID:
					$this->addError("password",Yii::t("user", "Password is incorrect. :lostPassword",array(":lostPassword"=>CHtml::link(Yii::t("user", "Lost Password?"), array('//user/recovery/recovery')))));
					break;
                case UserIdentity::ERROR_NONE:
                    $duration=$this->rememberMe ? 3600*24*30 : 0; // 30 days
                    $visits = Yii::app()->user->getState("_visitids");
                    $login = Yii::app()->user->login($identity,$duration);
                    if($login){
                        Yii::app()->user->setState("_visitids", $visits);
                    }else{
                        $this->addError("status",Yii::t("user", "You account has no permission set."));
                    }
                    break;
                case UserIdentity::ERROR_UNIVY:
                    $this->addError("status",Mims::unIvy()?Yii::t("user", "请登录艾毅在线系统"):Yii::t("user", "请登录本校幼儿园管理系统"));
                    break;
			}
		}
	}
}
