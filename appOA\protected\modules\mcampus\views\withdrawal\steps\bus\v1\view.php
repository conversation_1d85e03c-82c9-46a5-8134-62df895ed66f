<div>  
    <div class='flex align-items  mb24 mt10'>
        <span class='font14 color3'>处理人：</span>
        <div class='flex1 flex align-items lib_class_handle'>
        </div>
    </div>
    <div class='flex align-items mb20 settlement'>
        <span class='redColor font14 settlement_date'></span> 
        <span class='color6 font12 ml15'><span class='el-icon-warning-outline mr5'></span>以此日期为在学截止日进行费用清算</span>
    </div>
    <table  class="table tableCss">
        <thead>
            <tr style='background:#FAFAFA' class='font14 color3'>
                <td  width='100'><?php echo Yii::t('leave','Title');?></td>
                <td  width='100'><?php echo Yii::t('withdrawal','Type');?></td>
                <td width='100'>金额/元</td>
                <td  width='200'><?php echo Yii::t('withdrawal','Comment');?></td>
            </tr>
        </thead>
        <tbody class='tbody'>
        <tr> 
            <td><?php echo Yii::t('withdrawal', 'School Bus Fee'); ?></td>
            <td>
                <span class='notAllowed'>
                    <label class="radio-inline font14 color6 lableHeight eventsNone">
                        <input type="radio"  name="balance[]" value="0"> <?php echo Yii::t('withdrawal','Refund');?>
                    </label>
                </span>
                <span class='notAllowed'>
                    <label class="radio-inline font14 color6 lableHeight eventsNone ml24">
                        <input type="radio"  name="balance[]" value="1" > 欠费
                    </label>
                </span>
            </td>
            <td><input type="text" class="form-control" disabled name="amount[]" id='amount' value=''></td>
            <td><input type="text" class="form-control" disabled name="memo[]" id='memo' value=''></td>
        </tr>
        </tbody>
    </table>
    <textarea class="form-control mb24 mt8" rows="3" placeholder="<?php echo Yii::t('withdrawal','Comment');?>" name="remark" id='remark' disabled class='font14'></textarea>
    <div class='text-right font14 color3'><span class='submit'></span>  <span class='cur-p ml10 redColor canRevoke' onclick='cancelSubmit()'>撤销</span></div>
</div>
<script>
     lib()
     $(function () {
        $('[data-toggle="tooltip"]').tooltip()
    })
     function lib(){
        let forms=childDetails.forms.find(item2 =>item2.key === 'bus')
        for(var key in forms.owner_info){
            if (key !== '2' && key !== '5') {
                if(forms.implementer==key && forms.data._id){
                    $('.lib_class_handle').append('<div class="flex align-items mr16 relative"><img src='+forms.owner_info[key].photoUrl+' alt="" class="avatar38"/><div class="ml8"><div class="font14 color3 ">'+forms.owner_info[key].name+'</div><div class="font12 color6">处理时间：'+forms.implementAt+'</div></div></div>')
                }else if(!forms.data._id){
                    $('.lib_class_handle').append('<div class="flex align-items mr16"><img src='+forms.owner_info[key].photoUrl+' alt="" class="img28"/><div class="font14 color3 ml8">'+forms.owner_info[key].name+'</div></div>')
                }
            }
        }
        if(childDetails.info.appInfo.settlement_date){
            $('.settlement_date').text('清算日期：'+childDetails.info.appInfo.settlement_date)
        }else{
            $('.settlement').hide()
        }
        if(forms.data.goods && forms.data.goods.length){
            if(forms.data.goods && forms.data.goods.length){
                if(forms.data.goods[0]){
                    $('input[name="balance[]"]').filter('[value="'+forms.data.goods[0].balance+'"]').prop('checked', true);
                    $('#amount').val(forms.data.goods[0].amount);
                    $('#memo').val(forms.data.goods[0].memo);
                }
            }
        }
        if(forms.data.remark!=''){
            $('#remark').val(forms.data.remark);
        }
        if(forms.canRevoke){
            $('.canRevoke').show()
            $('.submit').html('由 '+childDetails.staffInfo[forms.implementer].name+' 提交')
        }else{
            $('.canRevoke').hide()
            $('.submit').html()
        }
    }
    function cancelSubmit(){
        container.cancelSubmitData('bus')
    }
</script>
