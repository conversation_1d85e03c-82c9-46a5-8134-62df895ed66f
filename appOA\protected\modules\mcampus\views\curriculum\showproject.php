<div class="container-fluid">
	<!-- 面包屑导航 -->
	<ol class="breadcrumb">
	    <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('//mcampus/default/index'))?></li>
	    <li><?php echo CHtml::link(Yii::t('site','Routines'), array('//mcampus/default/index'))?></li>
	    <li><?php echo CHtml::link(Yii::t('curriculum','Curriculum'), array('//mcampus/curriculum/project'))?></li>
	    <li><?php echo CHtml::link(Yii::t('curriculum','Projects List'), array('//mcampus/curriculum/project'))?></li>
	    <li class="active"><?php echo Yii::t('curriculum','Projects Info');?></li>
	</ol>
	<div class="row">
		<!-- 左侧菜单栏 -->
		<div class="col-md-2">
			<div class="list-group" id="classroom-status-list">
			    <?php foreach ($this->leftMenu as $key => $value) { ?>
			    <a href="<?php echo $this->createUrl($value[0]);?>" class="list-group-item status-filter <?php echo $this->getAction()->getId()==$value[0]?'active':''; ?>"><?php echo $value[1]; ?></a></li>
			    <?php } ?>
			</div>
		</div>
		<div class="col-md-10">
			<div class="panel panel-primary">
                <div class="panel-heading">
                    <h4><?php echo Yii::t('curriculum','Project Title').':'.CHtml::encode($project->cn_title).' '.CHtml::encode($project->en_title)?>
                        <?php 
                            if (Yii::app()->user->checkAccess('o_E_Edu_Common') || $this->staff->uid == $project->userid) {
                                echo "<a style='float:right' class='btn btn-default btn-xs' href=".$this->createUrl('editproject',array('pid'=>$project->pid)).">".Yii::t('curriculum','Edit')."</a>";
                            }
                        ?>
                    </h4>
                </div>
			    <div class="panel-body">
                    <h4><?php echo Yii::t('curriculum','Cn Desc'); ?>：</h4>
				    <?php echo CHtml::encode($project->cn_desc); ?>
                    <h4><?php echo Yii::t('curriculum','En Desc'); ?>：</h4>
                    <?php echo CHtml::encode($project->en_desc); ?>

                    <h4><?php echo Yii::t('curriculum','Tags'); ?>：</h4>
                    <?php foreach ($tag as $key => $value) {
                        echo "<label class='label label-success' >{$value->tag->tag_term}</label> ";
                    } ?>
                    <h4><?php echo Yii::t('curriculum','Attachments'); ?>：</h4>
                    <div>
                        <?php foreach ($project->attachments as $key => $value) {
                            $fileUrl = Yii::app()->params['OAUploadBaseUrl'].'/curriculum/'.$key;
                            echo "<label class='btn btn-success btn-xs'> <a style='color:#fff' target='_blank' href='{$fileUrl}' >{$value}</a></label> ";
                        } ?>
                    </div>
                    <hr>
				    <h4><?php echo Yii::t('curriculum','Activity List'); ?>：</h4>
				    <table class="table table-hover">
				    	<?php 
                            echo '<tr><th>'.Yii::t('curriculum','Activity Title').'</th>'; 
                            echo '<th>'.Yii::t('curriculum','Public').'</th>'; 
                            echo '<th>'.Yii::t('curriculum','Public Time').'</th>'; 
                            echo '<th>'.Yii::t('curriculum','Operation').'</th></tr>'; 

    					    foreach ($activity as $key => $value) {
    					    	$date = date('Y-m-d h:i',$value->addtime);
    					    	echo "<tr><td><a href='{$this->createUrl('showActivity',array('aid'=>$value->aid))}'>{$value->cn_title} {$value->en_title}</a></td><td>{$this->getUserName($value)}</td><td>{$date}</td><td>";
    					    	echo "{$this->getActButton($value)}</td></tr>";
    					    } 
                        ?>
					</table>
			    </div>
			</div>
		</div>
	</div>
</div>


<script>
    //删除活动
    function deleteAct (deletebtn,aid) {
        if (confirm('确定要删除吗？')) {
            $.ajax( {    
                url:'<?php echo $this->createUrl('deleteActivity'); ?>',    
                data:{aid : aid},    
                type:'post',    
                dataType:'json',    
                success:function(data) {    
                    if(data.msg == 'success'){    
                        window.location.href='<?php echo $this->createUrl('activity'); ?>'; 
                        // $(deletebtn).parent().parent().remove();
                        // window.location.reload(); 
                    }else{    
                        alert(data.msg);
                        console.log(data);    
                    }    
                 },    
                error : function() {    
                    console.log(data.error);    
                }    
            });  
        }
    }
    //活动状态控制
    function toggle (togglebtn,aid) {
        $.ajax( {
            url:'<?php echo $this->createUrl('state'); ?>',
            data:{aid : aid},
            type:'post',
            dataType:'json',
            success:function(data){
                console.log(data);
                if (data.msg == "success") {
                    $(togglebtn).text(data.btnvalue);
                    $(togglebtn).removeClass();
                    $(togglebtn).addClass(data.btnclass);
                }else{
                    console.log(data.msg);
                }
            },
            error:function(data){
                console.log(data.error);
            }
        });
    }
</script>