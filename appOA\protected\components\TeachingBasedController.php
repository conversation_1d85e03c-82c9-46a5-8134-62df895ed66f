<?php
/**
 * 待 TeachBasedController 完成之后，即可删除
 * 必须要指定一所校园的Controller, 默认为当前用户所在的校园，若当前用户有权限管理多所校园，则出现选择校园的界面
 */
class TeachingBasedController extends ProtectedController
{
    public $layout = '//layouts/teachingBasedMain';
	public $branchId = '';
	public $classId = 0;
	public $selectBranchActionId = 'select'; //选择校园的ActionId,
	public $branchSelectParams = array();
	public $optionalData = array();
	
	public $classSelection = array();
	
	
	public $schoolClasses = array();
	public $myClasses = array();
	public $calendarIds = array();
	public $schoolYears = array();
    public $currentWeek;
	
	public $showHistory = 0;
	public $historyStartYear = null;
	
	public $teachingTasks = array();
	
	public function init(){
		parent::init();
		Yii::import('common.models.classTeacher.*');
		Yii::import('common.models.calendar.*');
		$this->branchBased = true;
		$this->multipleBranch = $this->accessMultipleBranches();
		
		$this->branchSelectParams = array(
			'hideOffice' => true,
			'showList' => false,
			'selectedBid' => '',
			'selectLabel' => '',
			'urlArray' => array('/settings/demo', 'test1'=>'test'),
			'keyId' => 'branchId',
		);
		
		$this->teachingTasks = array(
			"mediaupload" => Yii::t("teaching", "Media Upload"),
			"mediamgt" => Yii::t("teaching", "Media Management"),
			"schoolnews" => Yii::t("teaching", "School News"),
			"classnews" => Yii::t("teaching", "Class Report"),
			"schedule" => Yii::t("teaching", "Weekly Schedule"),
			"devchecklist" => Yii::t("teaching", "Development Checklist"),
			"semesterreport" => Yii::t("teaching", "Semester Report"),		
		);
	}
	
	public function beforeAction($action){
		return true;
		//if($this->multipleBranch){
		//	$branchId = Yii::app()->request->getParam('branchId',null);
		//	$route = '/'.$this->module->getId().'/'.$this->getId().'/'.$this->selectBranchActionId;
		//	if( empty($branchId) && $this->selectBranchActionId != $action->getId() ){
		//		$this->redirect($route);
		//	}
		//	if( !empty($branchId) && !$this->checkBranchAccess($branchId) ){
		//		$this->redirect($route);
		//	}
		//	$this->branchId = $branchId;
		//}else{
		//	$this->branchId = $this->staff->profile->branch;
		//}
		//$getParam = Yii::app()->request->getParam('branchId',null);
		//if( !empty($getParam) && $this->branchId != $getParam ){
		//	throw new CException('YOU DO NOT HAVE THE PERMISSION');
		//}
		//
		//return true;
	}
	
	public function checkBranchAccess($branchId){
		//代码
		$resultAccess = false;
		if (!empty($branchId))
		{
			if(in_array($branchId, $this->accessBranch)){
				$resultAccess = true;
			}
		}
		return $resultAccess;
	}
	
	public function accessMultipleBranches(){
		//TODO: 需要加入判断该用户是否在可以管理多所校园的用户名单里
		$myBranch = $this->staff->profile->branch;
		$officeBranch = CommonUtils::LoadConfig('CfgOffice');
		if( in_array($myBranch, $officeBranch) ||  $this->staff->admtype )
		{
			$adminBranch = AdmBranchLink::model()->getMultipleBranches(Yii::app()->user->getId());
			if (in_array($myBranch, $officeBranch))
			{
				$this->accessBranch = array_keys($this->getAllBranch());
				$this->adminBranch = $adminBranch;
			}
			else 
			{
				$this->accessBranch = $adminBranch;
				$this->adminBranch = $adminBranch;
			}
			return true;
		}
		else{
			$this->accessBranch = $myBranch;
			$this->adminBranch = $myBranch;
			
			return false;
		}
	}
	
	public function actionMyClasses($json=false){
		if( count($this->accessBranch) == 1 ){
			$myClasses = ClassTeacher::model()->findAllByAttributes(array('yid'=>$branchObj->schcalendar, 'teacherid'=>Yii::app()->user->id));
		}
	}
	
	public function actionSchoolClasses($startYear=0){
		$crit = new CDbCriteria;
        $crit->compare('type', '<>'.Branch::TYPE_OFFICE);
        $crit->compare('status', Branch::STATUS_ACTIVE);
		$crit->index = 'branchid';
		if($startYear){
			Yii::import('common.models.calendar.CalendarSchool');
			$branches = Branch::model()->findAllByPk($this->accessBranch, $crit);
			$schoolCalendars = CalendarSchool::model()->findAllByAttributes(
				array(
					'branchid'=>$this->accessBranch,
					'startyear'=>$startYear
				)
			);
			$this->calendarIds = CHtml::listData($schoolCalendars, 'branchid', 'yid');

		}else{
			$branches = Branch::model()->with('currentClasses')->findAllByPk($this->accessBranch, $crit);
			$this->calendarIds = CHtml::listData($branches, 'branchid', 'schcalendar');
		}
		
		
		foreach($branches as $branch){
			$this->schoolClasses[$branch->branchid]['title'] = $branch->title;
			if($startYear){
				$crit = new CDbCriteria;
				$crit->compare('schoolid', $this->accessBranch);
				$crit->compare('yid', $this->calendarIds);
				
				$historyClasses = IvyClass::model()->findAll($crit);
				
				foreach($historyClasses as $hclass){
					$this->schoolClasses[$hclass->schoolid]['items'][$hclass->classid] = $hclass->title;
				}
			}else{
				$this->schoolClasses[$branch->branchid]['items'] = CHtml::listData($branch->currentClasses, 'classid', 'title');
			}
		}
        $this->schoolYears = CalendarSemester::model()->getCalendarRange($this->calendarIds);
		
		//针对校园人员
		if(count($this->accessBranch) == 1){
			if($startYear){
				$this->myClasses = CHtml::listData(ClassTeacher::model()->findAllByAttributes(
					array('yid'=>$this->calendarIds[$this->staff->profile->branch], 'teacherid'=>Yii::app()->user->id, 'schoolid'=>$this->staff->profile->branch)
				), 'classid','classid');				
			}else{
				$this->myClasses = CHtml::listData(ClassTeacher::model()->findAllByAttributes(
					array('yid'=>$branches[$this->staff->profile->branch]->schcalendar, 'teacherid'=>Yii::app()->user->id, 'schoolid'=>$this->staff->profile->branch)
				), 'classid','classid');
			}
            
            $criteria = new CDbCriteria();
            $t = time();
			
            $criteria->condition = "monday_timestamp < ".$t." and monday_timestamp+3600*24*7 > ".$t." and yid=".current($this->calendarIds);
            $weekModel = CalendarWeek::model()->find($criteria);
			
            $this->currentWeek = is_null($weekModel) ? 1 : $weekModel->weeknumber;
			
		}
	}
	
	public function getNameList($classid=0, $activeOnly=true, $return='array'){
		$nameList = array();
		if($this->showHistory && $this->historyStartYear){
			
		}else{
			$crit = new CDbCriteria;
			$crit->compare('classid', $classid);
			if($activeOnly){
				$crit->compare('status','<'.ChildProfileBasic::STATS_GRADUATED);
				$crit->compare('status','>'.ChildProfileBasic::STATS_REGISTERED);
			}
			$childObjs = ChildProfileBasic::model()->findAll($crit);
			
			foreach($childObjs as $child){
				//$nameList[] = array(
				$ret = array(
					'id' => $child->childid,
					'name' => $child->getChildName(true,true),
					'photo'=> $child->photo
				);
				
				if($return == 'array')
					$nameList[$child->childid] = $ret;
				elseif($return == 'json')
					$nameList[] = CJSON::encode($ret);
			}
		}
		return $nameList;
	}
	
	
}