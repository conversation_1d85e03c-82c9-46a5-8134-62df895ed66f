<?php

/**
 * This is the model class for table "ivy_vote_options".
 *
 * The followings are the available columns in table 'ivy_vote_options':
 * @property integer $id
 * @property integer $vote_id
 * @property string $title_cn
 * @property string $title_en
 * @property string $detail_description_cn
 * @property string $detail_description_en
 */
class WVoteOptions extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return VoteOptions the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_vote_options';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('vote_id', 'numerical', 'integerOnly'=>true),
			array('title_cn, title_en', 'length', 'max'=>1024),
			array('detail_description_cn, detail_description_en', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, vote_id, title_cn, title_en, detail_description_cn, detail_description_en', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'vote_id' => 'Vote',
			'title_cn' => 'Title Cn',
			'title_en' => 'Title En',
			'detail_description_cn' => 'Detail Description Cn',
			'detail_description_en' => 'Detail Description En',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('vote_id',$this->vote_id);
		$criteria->compare('title_cn',$this->title_cn,true);
		$criteria->compare('title_en',$this->title_en,true);
		$criteria->compare('detail_description_cn',$this->detail_description_cn,true);
		$criteria->compare('detail_description_en',$this->detail_description_en,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}