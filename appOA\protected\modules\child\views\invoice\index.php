<div id="childinfo">
    <?php $this->widget('ext.childInfo.ChildInfo', array('childObj'=>$child))?>
</div>

<div class="mb10">
    <?php echo CHtml::dropDownList('schoolyear', $schoolyear, $years);?>
</div>

<?php if ($plans):?>
<div class="tips_light tips_block">
    <span>该孩子已经设置了月计划：<?php echo implode(', ', $plans)?></span>
</div>
<?php endif;?>

<?php if ($schoolyear && $schoolPolicy->configs):?>
<div class="h_a">账单管理</div>
<div class="table_c1">
<table width=100%>
	<colgroup>
		<col class="th" width=180>
		<col width=null>
	</colgroup>
	<tbody>
		<tr>
			<td>
				<?php
					$paymentMenu = $schoolPolicy->getMenus();
					$this->widget('zii.widgets.CMenu', array(
						'id' => 'invoice-type',
						'items' => $paymentMenu,
                        'htmlOptions' => array('class'=>'J_invoice-type'),
						)
					);
				?>
			</td>
			<td>
                <div id="gen_invoice">
					<div class="not_content_mini">
						<i></i>请点击左侧账单类型
					</div>				
				</div>
                <div id="pre_invoice"></div>
                <div id="view_invoice"></div>
			</td>
		</tr>
	</tbody>
</table>
</div>
<?php else:?>
	<div class="not_content_mini">
		<i></i>没有找到配置文件
	</div>
<?php endif;?>

<script type="text/javascript">
var canSubmit = 1;
var lding = '<div class="pop_loading" id="J_loading"></div>';
$('#schoolyear').on('change', function(){
    window.location.href='<?php echo Yii::app()->createUrl('/child/invoice/index', array('childid'=>$child->childid ));?>&schoolyear='+$(this).val();
});

$('.J_invoice-type a').live('click', function(){
	$('.J_invoice-type a').parent().removeClass('current');
	$(this).parent().addClass('current');
    ginv($(this).attr('href'));
    return false;
});

$('.J_feetit input').live('change', function(){
    $(this).parent('div').next('div').toggle();
});

$('.service-day i').live('click', function(){
    var cssprefix = 'day-';
    var halfday = $(this).attr('halfday');
    var dayArray = halfday ? ['20', '40'] : ['10', '40'];
    for (var i in dayArray){
        if ($(this).hasClass(cssprefix+dayArray[i])){
            var j = parseInt(i)+1;
            if (j>=dayArray.length){
                var j = 0;
            }
            $(this).removeClass().addClass(cssprefix+dayArray[j]);
            $('#'+$(this).attr('rel')).val(dayArray[j]);
            break;
        }
    }
});

function showServiceInfo(ele){
	var v=$(ele).val();
	var reg = new RegExp('[2,3,4]D');
	found = reg.exec(v);
	if(found){
		$('.service-day').show();
		$('.service-day i').removeAttr('class');
		$('.service-day i').addClass('day-40');
		$('.service-day input').val(40);
		$('input.showServiceDay').val(1);
	}else{
		$('.service-day').hide();
		$('.service-day i').removeAttr('class');
		$('.service-day i').addClass('day-10');
		$('.service-day input').val(10);
		$('input.showServiceDay').val(0);
	}
}

function invoicePreview(_this)
{
    var action = $(_this.form).attr('action');
    var url = (typeof action === 'string') ? $.trim(action) : '';
    var btn = $(_this);
    $.ajax({
        url: url,
        type: 'post',
        data: $(_this.form).serialize(),
        dataType: 'json',
        beforeSend: function(arr, $form, options) {
            var text = btn.text();
            btn.text(text +'中...').prop('disabled',true).addClass('disabled');
        },
        success: function(data){
            var text = btn.text();    
            btn.removeClass('disabled').text(text.replace('中...', '')).removeProp('disabled');
        
            if (data.state==='preinvoice'){
				$('.error').removeClass('error');
                $('#pre_invoice').html(data.data);
            }
            else if (data.state==='preplan'){
                $('.error').removeClass('error');
                $('#plan_main').html(data.data);
                $('#J_sub').html('<button class="btn btn_submit mr10" type="button" onclick="addop(this);invoicePreview(this)">开启自动生成账单功能</button>');
            }
            else if (data.state==='plan'){
                $('.error').removeClass('error');
                $("#plan_main").html(data.data);
                $('#J_sub').html('');
                $('#theop').remove();
            }
            else {
				$('#gen_invoice').html(data.data);
                $('#pre_invoice').html('');
			}
			resetDatePicker();
        }
    });
}

function invoiceGenerate(_this)
{
    var action = $(_this.form).attr('action');
    var url = (typeof action === 'string') ? $.trim(action) : '';
    var btn = $(_this);
    $.ajax({
        url: url,
        type: 'post',
        data: $(_this.form).serialize(),
        dataType: 'json',
        beforeSend: function(arr, $form, options) {
            var text = btn.text();
            btn.text(text +'中...').prop('disabled',true).addClass('disabled');
        },
        success: function(data){
            var text = btn.text();
            btn.removeClass('disabled').text(text.replace('中...', '')).removeProp('disabled');
            
            if (data.state==='postInvoiceSave'){
                $('#pre_invoice').html("");
                $('#view_invoice').append(data.data);
            }
            else if (data.state === 'fail'){
                alert(data.message);
            }
            else {
                $('#pre_invoice').html(data.data);
            }
        }
    });
}
function resetDatePicker(){
	$( ".specialDatePicker" ).datepicker(
		{
			'changeMonth':true,
			'changeYear':true,
			'dateFormat':'yy-mm-dd',
		}
	);		
}
function ginv(url)
{
    $('#pre_invoice').html('');
    $('#gen_invoice').html(lding);
    $.get(url, null, function(data){
        $('#gen_invoice').html(data);
		resetDatePicker();
    });
}
function setplandate(id)
{
    if (confirm('确定操作?') && canSubmit === 1){
        canSubmit = 0;
        $.post('<?php echo $this->createUrl("/child/invoice/processPlanRuntime");?>', {id:id}, function(data){
            if (data.state === 'success'){
                ginv('<?php echo $this->createUrl('/child/invoice/task');?>');
            }
            else {
                alert(data.message);
            }
            canSubmit = 1;
        }, 'json');
    }
}
function saveplan(id)
{
    if (confirm('确定操作?') && canSubmit === 1){
        canSubmit = 0;
        $.post('<?php echo $this->createUrl("/child/invoice/genInvoiceFromTask");?>', {taskId:id}, function(data){
            canSubmit = 1;
            if (data.state === 'success'){
                ginv('<?php echo $this->createUrl('/child/invoice/task');?>');
            }
            else {
                alert(data.message);
            }
        }, 'json');
    }
}
function cancelInvoice(id)
{
    if (confirm('确定操作?') && canSubmit === 1){
        canSubmit = 0;
        $.post('<?php echo $this->createUrl("/child/invoice/processCancelInvoice");?>', {id:id}, function(data){
            canSubmit = 1;
            if (data.state === 'success'){
                $('#invoice_'+id).remove();
            }
            else {
                alert('作废账单失败！');
            }
        }, 'json');
    }
}
function renew(id)
{
    if (confirm('确定操作?') && canSubmit === 1){
        canSubmit = 0;
        $.post('<?php echo $this->createUrl("/child/invoice/processRenewTask");?>', {id:id}, function(data){
            canSubmit = 1;
            if (data.state === 'success'){
                ginv('<?php echo $this->createUrl('/child/invoice/task');?>');
            }
            else {
                alert(data.message);
            }
        }, 'json');
    }
}
</script>
<style type="text/css">
.switch_list li {
    width: 200px;
}
</style>