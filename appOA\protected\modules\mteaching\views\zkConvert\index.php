
<div id="container" v-cloak>
    <div  class="container-fluid">
        <ol class="breadcrumb">
            <li><?php echo CHtml::link(Yii::t('site', 'Teaching Tasks'), array('//mteaching/default/index')) ?></li>
            <li><?php echo Yii::t('labels', 'DS-Zhongkao Score Conversion') ?></li>
        </ol>
        <div class='flex align-items score'>
            <div class='fontBold'><?php echo Yii::t('labels', 'Score Mapping (IB to Zhongkao)') ?>：</div>
            <div class='flex1'>
                <span v-for='(list,key,index) in indexData.centesimal_system'>{{key}} → {{list}}</span>
            </div>
        </div>
        <div class='mt24'>
            <el-select v-model="schoolid" size='small' placeholder="<?php echo Yii::t('labels', 'Please Select Campus') ?>" class='mr12'>
                <el-option
                v-for="(item,key,index) in indexData.school_list"
                :key="key"
                :label="item"
                :value="key">
                </el-option>
            </el-select>
            <el-select v-model="yearid" size='small' placeholder="<?php echo Yii::t('labels', 'Please Select School Year') ?>" class='mr12'>
                <el-option
                v-for="(item,index) in indexData.year"
                :key="item.startyear"
                :label="item.schoolyear"
                :value="item.startyear">
                </el-option>
            </el-select>
            <el-select v-model="grade" size='small' placeholder="<?php echo Yii::t('labels', 'Please Select Grade') ?>" class='mr12'>
                <el-option
                v-for="(item,key,index) in indexData.grade"
                :key="key"
                :label="item"
                :value="key">
                </el-option>
            </el-select>
            <el-select v-model="term" size='small' placeholder="<?php echo Yii::t('labels', 'Please Select Semester') ?>" class='mr12'>
                <el-option
                v-for="(item,key,index) in indexData.term"
                :key="key"
                :label="item"
                :value="key">
                </el-option>
            </el-select>
            <el-button type="button" class="btn btn-primary " style='margin-top:-3px;height:32px' :loading="loadingCreate" @click='createScore'><?php echo Yii::t('labels', 'OK') ?></el-button>
        </div>
        <div v-if='scoreData.child_list'>
            <hr>
            <div class='flex align-items'>
                <div class='flex1'>
                    <span class='note'>
                        <span class='glyphicon glyphicon-info-sign'></span>
                        <span class='ml4'><?php echo Yii::t('labels', 'R1: Rule 1 matched') ?></span>
                        <span class='ml8'><?php echo Yii::t('labels', 'R2: Rule 2 matched') ?></span>
                    </span> 
                </div>
                <button type="button" class="btn btn-default mr16" @click='createAll'><?php echo Yii::t('labels', 'Convert All') ?></button>
                <button type="button" class="btn btn-primary" @click='exportTab'><?php echo Yii::t('labels', 'Export') ?></button>
            </div>
            <el-table
                id='exportTable'
                :key='itemKey'
                v-loading="tableLoading"
                class='mt16 tableAuto'
                :data="scoreData.child_list"
                style="width: 100%"
                border
                :cell-style="{textAlign:'center' }"
                :header-cell-style="{background:'#fafafa',color:'#333', textAlign:'center'}"
                :default-sort = "{prop: 'className', order: 'ascending'}"
                @sort-change="handleSort"
                :max-height="height-320">
                <el-table-column
                    fixed
                    class-name='cellBg'
                    type="index"
                    width="40">
                </el-table-column>
                <el-table-column
                fixed
                prop="name"
                class-name='cellBg'
                label="<?php echo Yii::t('campus', 'Student name') ?>"
                sortable
                width="130">
                    <template  slot-scope="scope">
                        <a target="_blank" :href ="'<?php echo $this->createUrl('learnInfo/index'); ?>&childid='+scope.row.id+'#Assessment'"> {{scope.row.name}}</a>
                    </template>
                </el-table-column>
                <el-table-column
                prop="id"
                fixed
                sortable
                class-name='cellBg'
                label="DSO ID"
                width="100">
                </el-table-column>
                <el-table-column
                prop="educational_id"
                label="<?php echo Yii::t('labels', 'EDU ID') ?>"
                sortable
                class-name='cellBg'
                fixed
                width="100">
                </el-table-column>
                <el-table-column
                prop="className"
                label="<?php echo Yii::t('labels', 'Class') ?>"
                sortable
                class-name='cellBg'
                fixed
                width="160">
                </el-table-column>
                <el-table-column
                sortable
                v-for='(item,index) in indexData.grade_subject[tableGrade]'
                :key='index'
                :width="columnCount(item)"
                sortable
                :prop="item.zk_course">
                <template slot="header" slot-scope="scope">
                    {{item.title}}
                    <!-- ' -->
                    <i class='el-icon-setting cur-p ml10' :class='yearSetRule.indexOf(item.zk_course)!=-1?"breenColor":"color3"'  @click.stop='editSub(item)'></i>
                </template>
                <template slot-scope="scope">
                    <div class='flex align-items' v-if='scoreData.child_store[scope.row.id] && scoreData.child_store[scope.row.id][item.zk_course]'>
                        <div class='flex1'>
                            <span  v-if='scoreData.child_store[scope.row.id][item.zk_course].rule_num!=0 && scoreData.child_store[scope.row.id][item.zk_course].ds_score_source!=0'  @click='clickPop($event,item,scope.row.id)' class='cur-p ruleScore'>{{scoreData.child_store[scope.row.id][item.zk_course].zk_score}}</span>
                            <span v-else>{{scoreData.child_store[scope.row.id][item.zk_course].zk_score}}</span>
                        </div>
                        <span class='rule' v-if='scoreData.child_store[scope.row.id][item.zk_course].rule_num!=0 && scoreData.child_store[scope.row.id][item.zk_course].ds_score_source!=0' :class="scoreData.child_store[scope.row.id][item.zk_course].rule_num=='1'?'averageTerm':'averageYellow'">R{{ scoreData.child_store[scope.row.id][item.zk_course].rule_num }}</span>
                    </div>
                </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
    <!-- 编辑 -->
    <div class="modal fade"  id="editModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel"  aria-labelledby="wantSayModalLabel"  data-backdrop="static">
        <div class="modal-dialog  modal-lg" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title"><?php echo Yii::t("labels", "Conversion Rules"); ?></h4>
            </div>
            <div class="modal-body p0 overflow-y scroll-box " :style="'max-height:'+(height)+'px;overflow-x: hidden;'">
                <div class=''>
                    <div class='col-sm-9 col-md-8 p24' style='border-right:1px solid #E4E7ED'>
                        <div>
                            <div class='font14 color3 mb16 fontBold'><?php echo Yii::t("labels", "Convert to"); ?></div>
                            <div class='row font14' v-if='indexData.school_list'>
                                <div class='col-sm-6 col-md-6 mb12'>
                                    <span class='color6'><?php echo Yii::t("labels", "Campus"); ?>：</span>
                                    <span class='color3'>{{indexData.school_list[schoolid]}}</span>
                                </div>
                                <div class='col-sm-6 col-md-6 mb12'>
                                    <span class='color6'><?php echo Yii::t("labels", "School Year"); ?>：</span>
                                    <span class='color3'>{{getYearList(yearid)}}</span>
                                </div>
                                <div class='col-sm-6 col-md-6 mb12'>
                                    <span class='color6'><?php echo Yii::t('user','Grade') ?>：</span>
                                    <span class='color3'>{{indexData.grade[grade]}}</span>
                                </div>
                                <div class='col-sm-6 col-md-6 mb12'>
                                    <span class='color6'><?php echo Yii::t('principal','Semester') ?>：</span>
                                    <span class='color3'>{{indexData.term[term]}}</span>
                                </div>
                                <div class='col-sm-12 col-md-12 mb12'>
                                    <span class='color6'><?php echo Yii::t('ptc','Subject') ?>：</span>
                                    <span class='color3'>{{editTable.cn_title}} {{editTable.en_title}}</span>
                                </div>

                            </div>
                        </div>
                        <hr>
                        <div class='font14 color3 fontBold'><?php echo Yii::t("labels", "Source"); ?></div>
                        <div class='average mt16 mb16'>Rule #1</div>
                        <div class="form-horizontal">
                            <div class="form-group">
                                <span class="col-sm-2 control-label"><?php echo Yii::t("labels", "School Year"); ?></span>
                                <div class="col-sm-10">
                                    <el-select v-model="ds_course_1st.year" placeholder="<?php echo Yii::t('global','Please Select') ?>" size='small' class='flex1' style='width:100%'>
                                        <el-option
                                            v-for="(item,id) in editData.yearList"
                                            :key="item.startyear"
                                            :label="item.schoolyear"
                                            :value="item.startyear">
                                            <span >{{ item.schoolyear }} <span v-if='item.is_selected==1'>（<?php echo Yii::t("labels", "Current"); ?>）</span></span>
                                        </el-option>
                                    </el-select>
                                </div>
                            </div>
                            <div class="form-group">
                                <span class="col-sm-2 control-label"><?php echo Yii::t('ptc','Subject') ?></span>
                                <div class="col-sm-10">
                                    <el-select v-model="ds_course_1st.program" filterable  placeholder="<?php echo Yii::t('global','Please Select') ?>" size='small' class='flex1' style='width:100%'>
                                        <el-option
                                        v-for="(item,id) in editData.coursesList"
                                        :key="item.program"
                                        :label="item.program+' '+item.title"
                                        :label="item.title"
                                        :value="item.program">
                                        <span >{{item.program}} {{ item.title }}</span>
                                        </el-option>
                                    </el-select>
                                </div>
                            </div>
                            <div class="form-group">
                                <span class="col-sm-2 control-label">
                                    <?php echo Yii::t("labels", "Average of"); ?>
                                </span>
                                <div class="col-sm-10">
                                    <label class="checkbox-inline mr10" v-for='(list,key,index) in indexData.rule_cycle'>
                                        <input type="checkbox" v-model='ds_course_1st.cycle' :value="key"> {{key}}
                                    </label>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class='col-sm-12'>
                                    <label class="control-label ">
                                        <el-switch
                                        v-model="pursuing"
                                        active-color="#337ab7">
                                        </el-switch>
                                        <span class='ml4'><?php echo Yii::t("labels", "If rule #1 is not matched, the rule #2 will be proceeded."); ?></span> 
                                    </label>
                                </div>
                            </div>
                            <div class='average mt4 mb16' v-if='pursuing'>Rule #2</div>
                            <div class="form-group" v-if='pursuing'>
                                <span class="col-sm-2 control-label"><?php echo Yii::t("labels", "School Year"); ?></span>
                                <div class="col-sm-10">
                                    <el-select v-model="ds_course_2nd.year" placeholder="<?php echo Yii::t('global','Please Select') ?>" size='small' class='flex1' style='width:100%'>
                                        <el-option
                                            v-for="(item,id) in editData.yearList"
                                            :key="item.startyear"
                                            :label="item.schoolyear"
                                            :value="item.startyear">
                                            <span >{{ item.schoolyear }} <span v-if='item.is_selected==1'>（<?php echo Yii::t("labels", "Current"); ?>）</span></span>
                                        </el-option>
                                    </el-select>
                                </div>
                            </div>
                            <div class="form-group" v-if='pursuing'> 
                                <span class="col-sm-2 control-label"><?php echo Yii::t('ptc','Subject') ?></span>
                                <div class="col-sm-10">
                                    <el-select v-model="ds_course_2nd.program" filterable  placeholder="<?php echo Yii::t('global','Please Select') ?>" size='small' class='flex1' style='width:100%'>
                                        <el-option
                                        v-for="(item,id) in editData.coursesList"
                                        :key="item.program"
                                        :label="item.program+' '+item.title"
                                        :value="item.program">
                                        <span >{{item.program}} {{ item.title }}</span>
                                        </el-option>
                                    </el-select>
                                </div>
                            </div>
                            <div class="form-group" v-if='pursuing'>
                                <span class="col-sm-2 control-label"><?php echo Yii::t("labels", "Average of"); ?></span>
                                <div class="col-sm-10">
                                    <label class="checkbox-inline mr10" v-for='(list,key,index) in indexData.rule_cycle'>
                                        <input type="checkbox" v-model='ds_course_2nd.cycle' :value="key"> {{key}}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class='col-sm-3 col-md-4 p24'>
                        <div :class='isCheck?"checkNumber":"checkNumberNu"' class='cur-p' @click='checkNum'>
                            <span ><span class='iconfont icon-find mr5 font14'  v-if='!checkClick'></span><span  v-if='checkClick' class='el-icon-loading mr5 font14'></span><span><?php echo Yii::t("labels", "Preview matched number"); ?></span></span>
                        </div>
                        <div class='mt24' v-if='checkRule'>
                            <div class='checkList' :class='checkData.rule_1!=0?"checkInfo":""'>
                                <div class='flex1'>Rule #1</div>
                                <div :class='checkData.rule_1!=0?"checkSuccess":""'><span class='el-icon-success mr4' v-if='checkData.rule_1!=0'></span>{{checkData.rule_1}} <?php echo Yii::t("labels", "p"); ?></div>
                            </div>
                            <div class='checkList' :class='checkData.rule_2!=0?"checkInfo":""'>
                                <div class='flex1'>Rule #2</div>
                                <div  :class='checkData.rule_2!=0?"checkSuccess":""'><span class='el-icon-success mr4' v-if='checkData.rule_2!=0'></span>{{checkData.rule_2}} <?php echo Yii::t("labels", "p"); ?></div>
                            </div>
                            <div class='checkList' :class='checkData.no_rule!=0?"checkError":""'>
                                <div class='flex1'><?php echo Yii::t("labels", "No Match"); ?></div>
                                <div class='' :class='checkData.no_rule!=0?"checkWarning":""'><span class='el-icon-warning mr4' v-if='checkData.no_rule!=0'></span>{{checkData.no_rule}} <?php echo Yii::t("labels", "p"); ?></div>
                            </div>
                        </div>                        
                    </div>
                </div>
                
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                <button type="button" class="btn btn-primary" @click='saveSubject()'><?php echo Yii::t("labels", "Save & Convert"); ?></button>
            
            </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
    <!--全部生成 -->
    <div class="modal fade"  id="createModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel"  aria-labelledby="wantSayModalLabel"  data-backdrop="static">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title"><?php echo Yii::t('labels', 'Convert All') ?></h4>
            </div>
            <div class="modal-body p24 overflow-y scroll-box " :style="'max-height:'+(height)+'px;overflow-x: hidden;'">
                <div v-if='nuSetRule.length!=0'>
                    <div class='fontBold font14 color3 mb16'><?php echo Yii::t('labels', 'Following subjects will be skipped for not setting conversion rule, proceed?') ?></div>
                    <div> <span class='color3 mr8 font14' v-for='(list,index) in nuSetRule'>{{list.title}} <span v-if='index+1<nuSetRule.length'>、</span></span></div>
                </div>
                <div v-else>
                    <div class='fontBold font14 color3'><?php echo Yii::t('labels', 'All Zhongkao grades will be regenerated, proceed?') ?></div> 
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                <el-button type="button" class="btn btn-primary" @click='saveCreate()' :loading="loadingCreate"><?php echo Yii::t('global', 'OK') ?></el-button>
            
            </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
    <el-popover
        v-if='showPop'
        ref='pop'
        :reference='reference'
        placement="bottom"
        width="340"
        trigger="click"
        popper-class='popperRule'
    >
    <div v-if='activeItem.zk_course'>
        <div class='popoverBg'>
            <div class='flex align-items mb5'>
            <div class='flex1 font14 fontBold'> Rule # {{activeItem.rule_num}}</div>
            <span class='el-icon-close cur-p' @click='$refs.pop.doClose()'></span>
            </div>
            <div class='flex align-items mb5'>
                <span class='el-icon-date font13'></span>
                <div class='flex1 ml10 font12'>{{activeItem.rule_year}}</div>
            </div>
            <div class='flex '>
                <span class='el-icon-collection font13 mt1'></span>
                <div class='flex1 ml10 font12' v-if='scoreData.timetableCourses[activeItem.rule_course_code]'>{{activeItem.rule_course_code}} {{scoreData.timetableCourses[activeItem.rule_course_code].title}}</div>
            </div>
        </div>
        <div class='popoverTable'> 
            <table class="table table-bordered text-center modalTable">
                <tr>
                    <td width='100'></td>
                    <template v-if='Object.values(activeItem.ds_scores_many)[0].abcd.length==4'>
                        <th >A</th>
                        <th >B</th>
                        <th >C</td>
                        <th >D</td>
                    </template>
                    <th v-else>-</th>
                </tr>
                <tr v-for='(item,index) in Object.keys(activeItem.ds_scores_many)'>
                    <th>{{item}}</th>
                    <td v-for='(list,idx) in activeItem.ds_scores_many[item].abcd'>{{list??'-'}}</td>
                </tr>
                <tr >
                    <th><?php echo Yii::t('labels', 'Average score') ?></th>
                    <td :colspan='Object.values(activeItem.ds_scores_many)[0].abcd.length'>{{activeItem.ds_scores_avg}}</td>
                </tr>
            </table>
        </div>
     </div>
    </el-popover>
</div>
<?php
//$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
let lang='<?php echo Yii::app()->language ?>'
// $('#editModal').modal('show')
var height=document.documentElement.clientHeight;
var container = new Vue({
    el: "#container",
    data: {
        lang:lang,
        schoolid:'',
        yearid:'',
        grade:'',
        term:'',
        height:height,
        pursuing:false,
        value: '',
        indexData:{},
        scoreData:{},
        editData:{},
        numList:[],
        editTable:{},
        ds_course_1st:{
            cycle:[],
            program:'',
            year:''
        },
        ds_course_2nd:{
            cycle:[],
            program:'',
            year:''
        },
        isCheck:false,
        checkData:{},
        yeartitle:'',
        tableGrade:'',
        tableLoading:false,
        itemKey:'',
        nuSetRule:[],
        yearSetRule:[],
        loadingCreate:false,
        checkRule:false,
        checkClick:false,
        tipVisibles:[],
        reference:{},
        showPop: false,
        activeId:{},
        activeItem:{}
    },
    created: function() {
        this.initData()
    },
    watch:{
        pursuing(newVal){
            this.checkRule=false
        },
        ds_course_1st:{
            handler(values){
                this.checkRule=false
                if(values.cycle.length!=0 && values.program!='' && values.year!=''){
                    this.isCheck=true
                }else{
                    this.isCheck=false
                }
            },
            deep: true,
            immediate: true
        },
        ds_course_2nd:{
            handler(newVal){
                this.checkRule=false
                if(newVal.cycle.length!=0 && newVal.program!='' && newVal.year!=''){
                    this.isCheck=true
                }
            },
            deep: true,
            immediate: true
        },
    },
    methods: {
        clickPop(event,item,id){
            // 这个操作是为了避免与源码中的点击reference doToggle方法冲突
            if (this.activeId === item && this.showPop) return
            this.showPop = false
            this.activeId = id
            this.activeItem = this.scoreData.child_store[id][item.zk_course]
            this.reference = event.target;
            this.$nextTick(() => {
                // 等待显示的popover销毁后再 重新渲染新的popover
                this.showPop = true
                this.$nextTick(() => {
                    // 此时才能获取refs引用
                    this.$refs.pop.doShow()
                })
            })
        },
        getYearList(id){
            for(var i=0;i<this.indexData.year.length;i++){
                if(this.indexData.year[i].startyear==id){
                    return this.indexData.year[i].schoolyear
                }
            }                
        },
        initData(){
            let that=this
            $.ajax({
                url: '<?php echo $this->createUrl("IndexData") ?>',
                type: "post",
                dataType: 'json',
                data: {
                },
                success: function(data) {
                    if (data.state == 'success') {
                        console.log(data)
                        that.indexData=data.data
                        that.schoolid=Object.keys(data.data.school_list)[0]
                        let currentYear=data.data.year.filter((i) => i.is_selected==1)
                        that.yearid=currentYear[0]?currentYear[0].startyear:''
                    }else{
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                },
                error: function(data) {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                },
            })
        },
        createScore(){
            let that=this
            this.tableLoading=true
            this.loadingCreate=true
            $.ajax({
                url: '<?php echo $this->createUrl("score") ?>',
                type: "get",
                dataType: 'json',
                data: {
                    start_year:this.yearid,
                    grade:this.grade,
                    term:this.term,
                    school_id:this.schoolid
                },
                success: function(data) {
                    if (data.state == 'success') {
                        that.tableLoading=false
                        that.loadingCreate=false
                        data.data.child_list.forEach(item => {
                            if(data.data.child_store[item.id]){
                                let score={}
                                for (let key in data.data.child_store[item.id]) {
                                    score[key]=data.data.child_store[item.id][key].zk_score
                                }
                                item=Object.assign(item, score);
                            }
                        });
                        that.scoreData=data.data
                        that.tableGrade=that.grade
                        that.yearSetRule=[]
                        if(Object.values(data.data.child_store).length!=0){
                            that.yearSetRule=Object.keys(Object.values(data.data.child_store)[0])
                        }
                    }else{
                        that.tableLoading=false
                        that.loadingCreate=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                },
                error: function(data) {
                    that.tableLoading=false
                    that.loadingCreate=false
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                },
            })
        },
        editSub(list){
            this.editTable=list
            let that=this
            $.ajax({
                url: '<?php echo $this->createUrl("ruleData") ?>',
                type: "get",
                dataType: 'json',
                data: {
                    start_year:this.yearid,
                    grade:this.grade,
                    term:this.term,
                    school_id:this.schoolid,
                    zk_course:list.zk_course
                },
                success: function(data) {
                    if (data.state == 'success') {
                        that.editData=data.data
                        that.ds_course_1st=data.data.convertRule.ds_course_1st?data.data.convertRule.ds_course_1st:{
                            cycle:[],
                            program:'',
                            year:''
                        }
                        that.ds_course_2nd=data.data.convertRule.ds_course_2nd?data.data.convertRule.ds_course_2nd:{
                            cycle:[],
                            program:'',
                            year:''
                        }
                        if(data.data.convertRule.ds_course_2nd && data.data.convertRule.ds_course_2nd.program){
                            that.pursuing=true
                        }else{
                            that.pursuing=false
                        }
                        that.isCheck=false
                        that.checkRule=false
                        that.checkClick=false
                        $('#editModal').modal('show') 
                    }else{
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                },
                error: function(data) {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                },
            })
        },
        checkNum(){
            let that=this
            if(that.ds_course_1st.program=='' || that.ds_course_1st.year=='' || that.ds_course_1st.cycle.length==0){
                resultTip({
                    error: 'warning',
                    msg: '请选择Rule #1'
                });
                return
            }
            if(that.pursuing){
                if(that.ds_course_2nd.program=='' || that.ds_course_2nd.year=='' || that.ds_course_2nd.cycle.length==0){
                    resultTip({
                        error: 'warning',
                        msg: '请选择Rule #2'
                    });
                    return
                }
            }
            that.checkClick=true
            $.ajax({
                url: '<?php echo $this->createUrl("ruleCheck") ?>',
                type: "get",
                dataType: 'json',
                data: {
                    start_year:this.yearid,
                    grade:this.grade,
                    term:this.term,
                    school_id:this.schoolid,
                    zk_course:this.editTable.zk_course,
                    first_rule:this.ds_course_1st,
                    second_rule:that.pursuing?this.ds_course_2nd:{}
                },
                success: function(data) {
                    if (data.state == 'success') {
                        console.log(data)
                        that.checkRule=true
                        that.checkClick=false
                        that.checkData=data.data
                    }else{
                        that.checkClick=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                },
                error: function(data) {
                    that.checkClick=false
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                },
            })
        },
        saveSubject(){
            let that=this
            if(that.ds_course_1st.program=='' || that.ds_course_1st.year=='' || that.ds_course_1st.cycle.length==0){
                resultTip({
                    error: 'warning',
                    msg: '请选择Rule #1'
                });
                return
            }
            if(that.pursuing){
                if(that.ds_course_2nd.program=='' || that.ds_course_2nd.year=='' || that.ds_course_2nd.cycle.length==0){
                    resultTip({
                        error: 'warning',
                        msg: '请选择Rule #2'
                    });
                    return
                }
            }
            $.ajax({
                url: '<?php echo $this->createUrl("ruleScoreStore") ?>',
                type: "get",
                dataType: 'json',
                data: {
                    start_year:this.yearid,
                    grade:this.grade,
                    term:this.term,
                    school_id:this.schoolid,
                    zk_course:this.editTable.zk_course,
                    first_rule:this.ds_course_1st,
                    second_rule:that.pursuing?this.ds_course_2nd:{}
                },
                success: function(data) {
                    if (data.state == 'success') {
                        console.log(data)
                        that.createScore()
                        $('#editModal').modal('hide') 
                        resultTip({
                            msg: data.message
                        });
                    }else{
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                },
                error: function(data) {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                },
            })
        },
        createAll(){
            this.nuSetRule=[]
            this.nuSetRule=this.indexData.grade_subject[this.tableGrade].filter((i) => this.scoreData.have_rule.indexOf(i.zk_course) == -1)
            $('#createModal').modal('show')      
        },
        saveCreate(){
            let that=this
            this.loadingCreate=true
            let zk_course=[]
            this.indexData.grade_subject[this.tableGrade].forEach(item => {
                zk_course.push(item.zk_course)
            });
            $.ajax({
                url: '<?php echo $this->createUrl("scoreStore") ?>',
                type: "get",
                dataType: 'json',
                data: {
                    start_year:this.yearid,
                    grade:this.grade,
                    term:this.term,
                    school_id:this.schoolid,
                    zk_course:zk_course
                },
                success: function(data) {
                    if (data.state == 'success') {
                        console.log(data)
                        that.createScore()
                        $('#createModal').modal('hide') 
                        resultTip({
                            msg: data.message
                        });
                    }else{
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                    that.loadingCreate=false
                },
                error: function(data) {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                    that.loadingCreate=false
                },
            })
        },
        exportTab() {
            var classTitle=this.indexData.grade[this.grade]
            if(classTitle.indexOf('Grade')!=-1){
                classTitle=classTitle.substr(6,classTitle.length)
            }
            var title='G'+classTitle+'-S'+this.term+'-成绩转换';
            if(title.length>31){
                title=title.substr(13,title.length)
            }
            var table = document.getElementById('exportTable');
            if (table.querySelector('.el-table__fixed')) {
                table.removeChild(table.querySelector('.el-table__fixed'));
            }
            if (table.querySelector('.el-table__fixed-right')) {
                table.removeChild(table.querySelector('.el-table__fixed-right'));
            }
            $(".rule").each(function(){
                $(this).html('')
            });
            var wb = XLSX.utils.table_to_book(table, {sheet:title});
            this.itemKey = Math.random();
            return XLSX.writeFile(wb, title+'.xlsx');
        },
        handleSort({ column, prop, order }) {
            let fieldname = prop;
            let sortType = order;
            if (sortType === "ascending") {
                this.scoreData.child_list = this.scoreData.child_list.sort((a, b) => b[fieldname] - a[fieldname]);
                // console.log(this.tableData);
            } else if (sortType === "descending") {
                this.scoreData.child_list = this.scoreData.child_list.sort((a, b) => a[fieldname] - b[fieldname]);
            }
        },
        columnCount (label)  {
            let labelLong = label.title.length 
            let size=''
            let width=''
            if(lang=='en_us'){
                if(labelLong < 11) {  
                    labelLong = 11
                }
                size = 8
                width=50
            }else{
                if(labelLong < 3) {  
                    labelLong = 3
                }
                size = 20 
                width=40

            }
            let labelWidth = labelLong * size 
            return labelWidth+width
        }

    }
})
</script>
<style >
[v-cloak]{display: none;}
.score{
    padding:16px;
    background:rgba(77, 136, 210, 0.10);
    border-radius:4px
}
.score span{
    padding-left:16px
}
.average{
    background: rgba(77, 136, 210, 0.10);
    border-radius: 2px;
    height: 16px;
    line-height: 16px;
    color: #4D88D2;
    display: inline-block;
    padding: 2px 5px;
    height: 20px;
}
.averageTerm{
    background: rgba(77, 136, 210, 0.06);
    border-radius: 2px;
    height: 16px;
    line-height: 16px;
    color: rgb(166,209,255);
    display: inline-block;
    padding: 0 2px;
    position: absolute;
    right: 10px;
}
.averageYellow{
    color:rgb(255 215 158); 
    border-radius: 2px;
    height: 16px;
    line-height: 16px;
    background:rgb(255,249,223);
    position: absolute;
    right: 10px;
    padding: 0 2px;

}
.control-label{
    text-align:left !important;
    color:#666;
    font-size:14px;
    padding-right:0
}
.checkNumber{
    border: 1px solid #4D88D2;
    border-radius: 4px;
    color: #4D88D2;
    font-size:14px;
    padding:6px 12px;
    display:inline-block;
}
.checkNumberNu{
    background: #F2F3F5;
    border: 1px solid #E5E6EB;
    border-radius: 4px;
    color: #999999;
    font-size:14px;
    padding:6px 12px;
    display:inline-block;
}
.cellBg{
    background:#fafafa;
    color:#333
}
.breenColor{
    color:#5cb85c
}
.checkList{
    background:#F7F7F8 ;
    border-radius: 4px;
    font-size: 14px;
    color: #333333;
    padding:12px 16px;
    margin-bottom:12px;
    display:flex;
    align-items:center
}
.checkInfo{
    background:rgba(92,184,92,0.08) !important
}
.checkSuccess{
    color: #5CB85C
}
.checkError{
    background:rgba(217,83,79,0.08) !important
}
.checkWarning{
    color: #d9534f;
}
.p0{
    padding:0
}
.el-table__body-wrapper::-webkit-scrollbar ,.bgGray::-webkit-scrollbar ,.selectTeacher::-webkit-scrollbar,.tablescroll::-webkit-scrollbar,.popper_class::-webkit-scrollbar{
    width: 8px; /*滚动条宽度*/
    height: 8px; /*滚动条高度*/
}
.el-table__body-wrapper::-webkit-scrollbar-track , .bgGray::-webkit-scrollbar-track ,.selectTeacher::-webkit-scrollbar-track,.tablescroll::-webkit-scrollbar-track,.popper_class::-webkit-scrollbar-track {
    border-radius: 10px; /*滚动条的背景区域的圆角*/
    -webkit-box-shadow: inset 0 0 6px rgba(238,238,238, 0.1);
    background-color: #F5F7FA; /*滚动条的背景颜色*/
}
.el-table__body-wrapper::-webkit-scrollbar-thumb,.bgGray::-webkit-scrollbar-thumb ,.selectTeacher::-webkit-scrollbar-thumb,.tablescroll::-webkit-scrollbar-thumb,.popper_class::-webkit-scrollbar-thumb {
    border-radius: 10px; /*滚动条的圆角*/
    -webkit-box-shadow: inset 0 0 6px rgba(145, 143, 0143, 0.1);
    background-color: #ccc; /*滚动条的背景颜色*/
}
.el-table .el-table__fixed{
    height:auto !important;
    bottom:8px !important;
}
.el-table{
    font-size:12px;
}
.el-table th{
    padding:5px 0 !important
}
.el-popover{
    padding:0
}
.popoverBg{
    padding: 16px 24px;
    background: #4D88D2;
    border-radius: 4px 4px 0px 0px;
    color: #fff;
}
.popoverTable{
    padding: 20px 24px 3px;
    font-size:12px;
    background: #CDE3FF;
    border-radius: 0px 0px 4px 4px;
}
.mt1{
    margin-top:1px
}
.popperRule{
    border:none
}
.popperRule .popper__arrow::after{
    border-bottom-color: #4D88D2 !important
}
.el-popper[x-placement^=top] .popper__arrow::after{
    border-top-color: #CDE3FF !important;
}
.ruleScore:hover{
    color: #4D88D2;
}
th,td{
    text-align:center
}
.note{
    padding:10px 15px;
    background: #F7F7F8;
    border-radius: 4px;
    color:#666
}
.modalTable{
    background:rgba(255, 255, 255, 0.80)
}
.modalTable th,.modalTable td{
    border: 1px solid #E3EEFF !important;
}
</style>
 