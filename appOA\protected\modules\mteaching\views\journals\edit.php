<style>
     [v-cloak] {
        display: none;
    }
    .flexWidth{
        width:120px
    }
    .text-ellipsis{
        text-overflow: -o-ellipsis-lastline;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        font-size:14px
    }
    .lineHeight{
        line-height:48px
    }
    .lineHeight40{
        line-height:32px
    }
    .pb12{
        padding-bottom:12px
    }
    .image{
        width: 48px; 
        height: 48px;
        object-fit:cover;
    }
    .point{
        width: 5px;
        height: 5px;
        display: inline-block;
        background: red;
        border-radius: 50%;
        margin-right: 5px;
        margin-bottom: 1px;
    }
    .loading{
        width:100%;
        height:100%;
        background:#fff;
        position: absolute; 
        opacity: 0.5;
        z-index: 99
    }
    .loading span{
        width:100%;
        height:100%;
        display:block;
        background: url("<?php echo Yii::app()->theme->baseUrl?>/images/loading.gif")no-repeat center center ;
    }
    .maxHieght{
        max-height:300px;
        overflow-y:auto;
        border: 1px solid #ccc;
        padding: 10px;
        border-radius: 5px;
    }
    .flexDate{
        width:140px
    }
    .defaultBg{
        background:#e9e9e9;
        color:#666
    }
    .content{
        word-break: break-word;
    }
    .content img{
        max-width:100%;
        margin-bottom: 1em; 
    }
    .badgeNum{
        display: inline-block;
        width: 6px;
        height: 6px;
        background: #d9534f;
        border-radius: 50%;
        margin-top: 6px;
    }
    .color94442FF{
        color:#A94442FF
    }
    .readPoint{
        width: 5px;
        height: 5px;
        background: #333333;
        display: inline-block;
        border-radius: 50%;
        border: 1px solid #333333;
        position: absolute;
        left: 0;
        top: 8px;
    }
    .border{
        border: 2px solid #428BCA;
    }
    .childImage{
        width: 32px; 
        height: 32px;
        object-fit:cover;
    }
    .delSearchText{
        position: absolute;
        left: -25px;
        top:10px;
        z-index: 8;
        color: #ccc;
    }
    .yellow{
        color: #F0AD4E;
    }
    mark{
        background: yellow;
        padding:0
    }
    .borderRight{
        border-right:1px solid #E4E7EDFF
    }
    .borderLeft{
        border-left:1px solid #E4E7EDFF
    }
    .borderbto{
        border-bottom:1px solid #E4E7EDFF
    }
    .messageChild{
        max-height:700px;
        overflow-y:auto;
        padding-right:15px
    }
    .pl10{
        padding-left:15px
    }
    .commentImage{
        width:40px;
        height:40px;
        object-fit:cover;
    }
    .borderTop{
        border-top:1px solid #ddd;
    }
    .borderB{
        border-bottom:1px solid #ddd;
    }
    .font18{
        font-size:18px
    }
    .shareContent{
        width:375px;
        /* border-radius: 18px; */
        margin:0 auto
    }
    .shareStatus{
        position: absolute;
        left:0;
        top:0
    }
    .bgPto{
        background-repeat: no-repeat;
        background-size: contain;
    }
    .sharePto .logo{
        width:150px;
        margin-top:32px;
    }
    .sharePto .title{
        font-size: 26px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #FFFFFF;
        line-height: 38px;
        margin-top:24px
    }
    .sharePto .view{
        width: 100%;
        margin-top:8px;
        background: #E69119;       
        font-size: 14px;
        color:#fff;
        padding:6px
    }
    .shareContent .contentText{
        margin:16px 16px 0;
        border-radius: 16px;
    }
    .contentText .title{
        font-size:16px;
        line-height: 20px;
        margin-bottom:4px
    }
    .contentText .summary{
        font-size: 13px;
        line-height: 19px;
        margin-bottom:4px
    }
    .shareContent .bottom{
        margin:0 16px
    }
    .shareContent .bottom img{
        width: 100%;
        margin-bottom:16px
    }
    .contentText .wechat{
        border-top: 1px dashed #D0D0D0;
        margin-top: 20px;
        text-align: center;
    }
    .wechat{
        justify-content: center;
        align-items: center;
    }
    .fontWight{
        font-weight:600
    }
    .white{
        color:#fff
    }
    .Thumbnail{
        position: absolute;
        right: 0;
        top: 0;
        width:100px;
        text-align:center
    }
    .Thumbnail img{
        width:90px;
        padding:10px
    }
    .Thumbnail div{
        background:#F7F7F8
    }
    .Thumbnail .checkImg{
        border: 2px solid #4D88D2;
        border-radius: 8px;
    }
    .wechatImg{
        width:120px;
        height:120px
    }
    .shareAvatar{
        width:40px;
        height:40px;
        object-fit:cover;
        border:1px solid #fff
    }
    .white08{
        opacity: 0.8;
    }
    .pt5{
        padding-top:5px
    }
    .p24{
        padding:24px !important
    }
    .p8{
        padding:8px
    }
    .Options{
        position: absolute;
        left: 0;
        top: 0;
        width:200px
    }
    .pt2{
        padding-top:2px
    }
    .commentBorder{
        border: 2px solid #D9534F;
        display: inline-block;
        border-radius: 50%;
        box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.4);
    }
    .commentBorder img{
       width:44px;
       height:44px
    }
    .modal-body{
        overflow:hidden
    }
    .readBorder{
        border: 2px solid #D9534F;
        display: inline-block;
        border-radius: 50%;
        box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.4);
    }
    .readBorder img{
       width:36px;
       height:36px
    }
    .schoolTitle,.schoolTitle1{
        color: #fff;
        font-size: 16px;
        margin-top: 12px;
        position: relative;
        display: inline-block;
    }
    .schoolTitle:before, .schoolTitle:after {
        content: ''; 
        position: absolute; 
        top: 50%;
        background: #fff; 
        width: 25px;
        height: 1px;
    }
    .schoolTitle:before,.schoolTitle1:before{
        left: -35px;
    }
    .schoolTitle:after,.schoolTitle1:after {
        right: -35px;
    }
    .schoolTitle1:before, .schoolTitle1:after {
        content: ''; 
        position: absolute; 
        top: 50%;
        background: #061F9D; 
        width: 25px;
        height: 1px;
    }
    .forwarded{
        background:#F7F7F8;
        border: 1px solid #EBEDF0;
        padding:12px 16px;
        border-radius: 4px;
    }
    .replyColor{
        color:#F0AD4E
    }
    .select{
        display: flex;
        flex-direction: column;
        width: 100%;
        display: inline-block;
        position: relative;
    }
    .icon{
        position: absolute;
        right: 10px;
        top: 14px;
        color: #999;
    }
    .input {
        -webkit-appearance: none;
        background-color: #FFF;
        border-radius: 4px;
        border: 1px solid #DCDFE6;
        box-sizing: border-box;
        color: #606266;
        display: inline-block;
        font-size: inherit;
        height: 40px;
        line-height: 40px;
        outline: 0;
        padding: 0 15px;
        transition: border-color .2s cubic-bezier(.645,.045,.355,1);
        width: 100%;
        cursor: pointer;
    }
    .select ul {
        width: 100%;
        cursor: pointer;
        border-radius: 4px;
        z-index: 9;
        list-style: none;
        padding: 6px 0;
        box-sizing: border-box;
        border: 1px solid #E4E7ED;
        border-radius: 4px;
        background-color: #FFF;
        -webkit-box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
        box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
        margin: 8px 0;
        max-height: 274px;
        overflow-y:auto;
        position:absolute;
    }
    .select ul:hover{
      border: 1 solid #409eff;
    }
    .select ul li{
        font-size: 14px;
        padding: 4px 20px;
        position: relative;
        white-space: nowrap;
        overflow: hidden;
        -o-text-overflow: ellipsis;
        text-overflow: ellipsis;
        color: #606266;
        box-sizing: border-box;
        cursor: pointer;
    }
    .select ul li:hover{
        background-color: #F5F7FA;
    }
    .contentAvatar {
        width: 44px;
        height: 44px;
        border-radius: 50%;
        object-fit: cover;
    }
    .tagLabel {
        background: #EBEDF0;
    }
    .contentPostObject{
        width: 36px;
        height: 36px;
        border-radius: 50%;
        object-fit:cover;
    }
    .postMore{
        font-size: 14px;
        display: inline-block;
        height: 36px;
        line-height: 36px;
        background: #4D88D2;
        color: #fff;
        padding: 0 10px;
        border-radius: 18px;
        vertical-align: middle;
    }
    .forwardText{
        background: #FAFAFA;
        border-radius:4px;
        padding:16px
    }
    .textareaCss{
        border: none;
        border-radius: 0;
        box-shadow: none;
    }
    .uploadIcon{
        background: #fff;
        font-size: 16px;
        color: #555555;
        margin-top:10px
    }
    .uploadImg{
        overflow-x: auto;
        white-space: nowrap;
        margin-top:20px
    }
    .fileImgs{
        width:72px;
        height:72px;
        object-fit: cover;
        display: inline-block;
    }
    .fileImg{
        width:72px;
        height:72px;
        object-fit: cover;
        display: inline-block;
        margin-bottom:10px;
        cursor: pointer;
    }
    .imgData{
        position: relative;
        display: inline-block;
    }
    .imgData span{    
        position: absolute;
        right: 3px;
        top: 3px;
        background: #333333;
        opacity: 1;
        color: #fff;
        width: 17px;
        height: 17px;
        border-radius: 50%;
        font-weight: 200;
        text-align: center;
        line-height: 15px;
        font-size: 19px;
    }
    .uploadFile {
        font-size:14px;
        padding:6px 12px;
        align-items: center;
    }
    .uploadFile .iconFile{
        display:none
    }
    .uploadFile:hover{
        background: #F7F7F8;

    }
    .uploadFile:hover .iconFile{
        display:block
    }
    .uploadFile a{
        color:#4D88D2
    }
    .fileLink{
        background: #F7F7F8;
        display: inline-block;
        border-radius: 4px;
        border: 1px solid #EBEDF0;
        padding: 6px 12px;
        font-size: 14px;
        margin-right: 16px;
        margin-bottom:8px;
        line-height:20px
    }
    .fileLink img{
        width:20px
    }
    .uploadLoading{
        width: 72px;
        height: 72px;
        background: #D9D9D9;
        border-radius: 4px;
        line-height: 72px;
        text-align: center;
    }
    .inputStyle{
        border: none;
        border-bottom: 1px solid #ccc;
        padding: 0;
        height: 25px;
        line-height: 25px;
        width: 100%;
    }
    .imgLi{
        list-style: none;
        padding-left: 0;
    }
    .imgLi li{
        display:inline-block
    }
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Teaching Tasks'), array('//mteaching/default/index')) ?></li>
        <li><?php echo Yii::t('newDS', 'Journal') ?></li>
        <li><?php echo Yii::t("newDS", "Mine");?></li>
    </ol>
    <div class="row" id='container'  v-cloak>
        <div class="col-md-2 col-sm-12">
            <div class="list-group" id="classroom-status-list">
                <a href="<?php echo $this->createUrl('index'); ?>" class="list-group-item status-filter"><?php echo Yii::t("newDS", "All Journals");?></a>
                <a href="<?php echo $this->createUrl('edit'); ?>" class="list-group-item status-filter active"><?php echo Yii::t("newDS", "Mine");?></a>
                <a href="<?php echo $this->createUrl('feedback'); ?>" class="list-group-item status-filter"><span  v-if='toReplyNum>"0"' class='badgeNum pull-right'></span><?php echo Yii::t("newDS", "Parent Feedback");?></a>
            </div>
        </div>
        <div>
            <div class='loading'  v-if='loading'>
                <span></span>
            </div>
            <div v-if='managetypeList.length==1 && managetypeList[0]=="teacher" || managetypeList.length==2'  :class='managetypeList.length>1?"col-md-5 col-sm-5":"col-md-10 col-sm-10" '>
                <div class="panel panel-default">
                    <div class="panel-heading flex">
                        <h4 class='flex1'>
                            My Journals 
                            <!-- <span v-if="teacherIds.length > 0" class='ml15 mb20'>
                                <span class="glyphicon glyphicon-user color6"></span>
                                <span style='color:#4D88D2' class='font16'>{{teacherUser(staff, 'name')}}</span>
                                <span style='color:#4D88D2' title="<?php echo Yii::t('newDS', 'View Journals of Other Teachers')?>" @click="chooseTeacher()" class="glyphicon glyphicon-chevron-down"></span>
                            </span> -->
                        </h4> 
                        <div class='pull-right'>
                            <a href="<?php echo $this->createUrl('show', array('type' => 'teacher')); ?>"  class="btn  btn-primary btn-sm pull-right" role="button"><span class='glyphicon glyphicon-plus'></span> <?php echo Yii::t("newDS", "New");?></a>
                        </div>
                    </div>
                    <div class="panel-body relative">
                        <div class='loading'  v-if='teacherLoading'>
                            <span></span>
                        </div>
                        <div class="form-group has-feedback">
                            <div class="input-group">
                                <input type="text" class="form-control" v-model='teacherSearchText' placeholder='<?php echo Yii::t("newDS", "Input text to search title or content");?>'  @keyup.enter='getList("teacher","")'>
                                <a href='javascript:;' class="input-group-addon relative" @click='getList("teacher","")'><span class='glyphicon glyphicon-search'></span> <?php echo Yii::t("global", "Search");?>
                                    <span class='glyphicon glyphicon-remove delSearchText' v-if='teacherSearchText!=""'  @click='teacherSearchText="";getList("teacher","")'></span> 
                                </a>
                            </div>
                        </div>
                        <p class='text-center' v-if='teacherList.length==0 && !loading' ><?php echo Yii::t("newDS", "No items found");?></p>
                        <div v-else>
                            <div v-for='(list,idx) in teacherList'>
                                <div class='flex' >
                                    <p class='flex1 text-ellipsis  text-primary cur-p fontBold' data-toggle="modal"  @click='viewContent(list,"teacher")'>
                                        {{list.title}}
                                    </p>
                                    <div class="media-right flexDate text-right text-muted">
                                        <span v-if='list.status!="1"' class='mr5'><i class='point'></i><?php echo Yii::t("newDS", "offline");?></span>
                                        <span class='glyphicon glyphicon-trash mr10 cur-p'  @click="popover(list,'teacher')" ></span>
                                        <a :href="'<?php echo $this->createUrl('show', array('type' => 'teacher')); ?>&id='+list._id" class='glyphicon glyphicon-edit mr5' ></a>
                                    </div>
                                </div>
                                <div>
                                    <span class="label label-default defaultBg mr5 mb5 fontWeightNormal pull-left" >{{journalCategory[list.category]}}</span>
                                    <span class="label label-info mr5 mb5 fontWeightNormal  pull-left" v-for='(item,id) in list.subjects'>{{subjectList[item]}}</span>
                                    <span class='ml20 cur-p yellow' @click='viewContent(list,"teacher")' v-if='list.comment_num>0'><?php echo Yii::t("newDS", "View Comments");?></span>
                                </div>
                                <div class='clearfix'></div>
                                <div class="media ">
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)">
                                        <img class="media-object img-circle image"  :src="teacherUser(list.sign_as_uid,'photo')" data-holder-rendered="true" >
                                        </a>
                                    </div>
                                    <div class="media-right pull-right text-right select_4"  :class='list.status=="0"?"lineHeight":"pt10"'>
                                        <span style="font-size:24px" :class='list.status=="0"?"pt10":""' class="glyphicon glyphicon-qrcode cur-p pull-left" @click='oneQrcode(list,"teacher")' aria-hidden="true"></span>
                                        <h4 class="media-heading font12" v-if='list.status=="1"'>{{list.format_publish_at}}</h4>
                                        <div class='text-muted'  >
                                            <span  v-if='list.targets_num && list.targets_num>0' >
                                             {{list.targets_num}}<?php echo Yii::t("newDS", " subscriber assigned");?>丨<span class="text-primary cur-p" @click='reviewedList(list)'>{{list.reviewed_num}} <?php echo Yii::t("newDS", "read");?></span> 
                                            </span>
                                            
                                            <div class='color94442FF pull-right' v-else onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" title="<?php echo Yii::t("newDS", "Add subscriber when editing a journal");?>" data-placement="left"> 
                                                <span  class='glyphicon glyphicon-exclamation-sign'></span>
                                                <span><?php echo Yii::t("newDS", "no subscriber assigned");?></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="media-body pt10 media-middle">
                                        <h4 class="media-heading font12">{{teacherUser(list.sign_as_uid,'name')}}</h4>
                                        <div class='text-muted'>
                                        {{teacherUser(list.sign_as_uid,'title')}}
                                        </div>
                                    </div>
                                </div>
                                <hr  v-if='idx!=teacherList.length-1'>
                            </div>
                        </div>
                        <p class='text-center' v-else ><?php echo Yii::t("newDS", "No items found");?></p>
                    </div>
                    <nav aria-label="Page navigation" v-if='teacherCopyPage.pages>1'  class="text-left ml10">
                        <ul class="pagination">
                            <li v-if='teacherPageNum >1'>
                                <a href="javascript:void(0)" @click="plus('teacher',1)" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            <li v-else class="disabled">
                                <a aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            <li class="previous" v-if='teacherPageNum >1'>
                                <a href="javascript:void(0)" @click="prev('teacher',teacherPageNum)">‹</a>
                            </li>
                            <li class="disabled" v-else>
                                <a href="javascript:void(0)">‹</a>
                            </li>
                            <li v-for='(data,index) in teacherPage.pages' :class="{ active:data==teacherPageNum }">
                                <a href="javascript:void(0)" @click="plus('teacher',data)">{{data}}</a>
                            </li>
                            <li class="previous" v-if='teacherPageNum <teacherCopyPage.pages'>
                                <a href="javascript:void(0)" @click="next('teacher',teacherPageNum)">›</a>
                            </li>
                            <li class="previous disabled" v-else>
                                <a href="javascript:void(0)">›</a>
                            </li>
                            <li v-if='teacherPageNum <teacherCopyPage.pages'>
                                <a href="javascript:void(0)" @click="plus('teacher',teacherCopyPage.pages)" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li v-else class="disabled">
                                <a aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                        </ul>
                        <div class='summary mb10'>第 <span v-if='teacherPageNum*20-20==0'>1</span><span v-else>{{teacherPageNum*20-20}}</span>-{{teacherPageNum*20}} 条, 共 {{teacherCopyPage.total}} 条.</div>
                    </nav>
                </div>
            </div>
            <div v-if='managetypeList.length==1 && managetypeList[0]=="leader" || managetypeList.length==2'    :class='managetypeList.length>1?"col-md-5 col-sm-5":"col-md-10 col-sm-10" '>
                <div class="panel panel-default">
                    <div class="panel-heading flex">
                        <h4 class='flex1 '>Journals from School Leaders</h4>
                        <div class='flexWidth'>
                        <a href="<?php echo $this->createUrl('show', array('type' => 'leader')); ?>" class="btn  btn-primary btn-sm pull-right" role="button"><span class='glyphicon glyphicon-plus'></span> <?php echo Yii::t("newDS", "New");?></a>
                        </div>
                    </div>
                    <div class="panel-body relative">
                        <div class='loading'  v-if='leaderLoading'>
                            <span></span>
                        </div>
                        <div class="form-group has-feedback">
                            <div class="input-group">
                                <input type="text" class="form-control" v-model='leaderSearchText'  @keyup.enter='getList("leader","")' placeholder='<?php echo Yii::t("newDS", "Input text to search title or content");?>' >
                                <a href='javascript:;' class="input-group-addon relative" @click='getList("leader","")'><span class='glyphicon glyphicon-search'></span> <?php echo Yii::t("global", "Search");?>
                                    <span class='glyphicon glyphicon-remove delSearchText' v-if='leaderSearchText!=""' @click='leaderSearchText="";getList("leader","")'></span>
                                </a>
                            </div>
                            
                        </div>
                        <p class='text-center' v-if='leaderList.length==0 && !loading' ><?php echo Yii::t("newDS", "No items found");?></p>
                        <div v-else>
                            <div v-for='(list,index) in leaderList'>
                            <div class='flex' >
                                    <p class='flex1 text-ellipsis  text-primary cur-p fontBold' data-toggle="modal"  @click='viewContent(list,"leader")'>
                                        {{list.title}}
                                    </p>
                                    <div class="media-right flexDate text-right text-muted">
                                        <span v-if='list.status!="1"' class='mr5'><i class='point'></i><?php echo Yii::t("newDS", "offline");?></span>
                                        <span class='glyphicon glyphicon-trash mr10 cur-p'  @click="popover(list,'leader')" ></span>
                                        <a :href="'<?php echo $this->createUrl('show', array('type' => 'leader')); ?>&id='+list._id" class='glyphicon glyphicon-edit mr5'  ></a>
                                    </div>
                                </div>
                                <!-- <p class='text-ellipsis  text-primary cur-p fontBold' data-toggle="modal" @click='viewContent(list,"leader")'>{{list.title}}</p> -->
                                <span class="label label-default defaultBg mr5 fontWeightNormal pull-left" >{{journalCategory[list.category]}}</span>
                                <div>
                                    <span class="label label-default defaultBg mr5 fontWeightNormal" v-for='(item,id) in list.grade_group_id'>{{gradeGroupList[item]}}</span>
                                    <span class='ml20 cur-p yellow' @click='viewContent(list,"leader")'  v-if='list.comment_num>0'><?php echo Yii::t("newDS", "View Comments");?></span>
                                </div>
                                <div class='clearfix'></div>
                                <div class="media ">
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)">
                                        <img class="media-object img-circle image"  :src="leaderUser(list.sign_as_uid,'photo')" data-holder-rendered="true" >
                                        </a>
                                    </div>
                                    <div class="media-right pull-right text-right select_4 pt10" >
                                        <div style="font-size:24px" class="glyphicon glyphicon-qrcode cur-p pull-left " @click='oneQrcode(list,"leader")' aria-hidden="true"></div>
                                        <h4 class="media-heading font12">{{list.format_publish_at}}</h4>
                                        <span class='text-primary cur-p' @click='leaderRead(list)'>{{list.reviewed_num}} <?php echo Yii::t("newDS", "read");?></span> 
                                    </div>
                                    <div class="media-body pt10 media-middle">
                                        <h4 class="media-heading font12">{{leaderUser(list.sign_as_uid,'name')}}</h4>
                                        <div class='text-muted'>
                                        {{leaderUser(list.sign_as_uid,'title')}}
                                        </div>
                                    </div>
                                </div>
                                <hr v-if='index!=leaderList.length-1'>
                            </div>
                        </div>
                        <p class='text-center' v-else ><?php echo Yii::t("newDS", "No items found");?></p>
                    </div>
                    <nav aria-label="Page navigation" v-if='leaderCopyPage.pages>1'  class="text-left ml10">
                        <ul class="pagination">
                            <li v-if='leaderPageNum >1'>
                                <a href="javascript:void(0)" @click="plus('leader',1)" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            <li v-else class="disabled">
                                <a aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            <li class="previous" v-if='leaderPageNum >1'>
                                <a href="javascript:void(0)" @click="prev('leader',leaderPageNum)">‹</a>
                            </li>
                            <li class="disabled" v-else>
                                <a href="javascript:void(0)">‹</a>
                            </li>
                            <li v-for='(data,index) in leaderPage.pages' :class="{ active:data==leaderPageNum }">
                                <a href="javascript:void(0)" @click="plus('leader',data)">{{data}}</a>
                            </li>
                            <li class="previous" v-if='leaderPageNum <leaderCopyPage.pages'>
                                <a href="javascript:void(0)" @click="next('leader',leaderPageNum)">›</a>
                            </li>
                            <li class="previous disabled" v-else>
                                <a href="javascript:void(0)">›</a>
                            </li>
                            <li v-if='leaderPageNum <leaderCopyPage.pages'>
                                <a href="javascript:void(0)" @click="plus('leader',leaderCopyPage.pages)" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li v-else class="disabled">
                                <a aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                        </ul>
                        <div class='summary mb10'>第 <span v-if='leaderPageNum*20-20==0'>1</span><span v-else>{{leaderPageNum*20-20}}</span>-{{leaderPageNum*20}} 条, 共 {{leaderCopyPage.total}} 条.</div>
                    </nav>
                </div>
            </div>
           
        </div>
        
        <div class="modal fade" id="contentModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
            <div class="modal-dialog modal-lg" style='width:1000px' role="document">
                <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel">Journal <?php echo Yii::t("newDS", "Detail");?></h4>
                </div>
                <div class="modal-body" v-if='Object.keys(contentData).length!=0'>
                    <div class='col-md-6 col-sm-6'  style='min-height:400px' :class='!borderShow?"borderRight":""'  ref='content'>
                        <div><span class="label label-default tagLabel  color6"><?php echo Yii::t("newDS", "School Year"); ?>：{{contentData.journalData.start_year}}-{{contentData.journalData.start_year+1}}</span></div>
                        <div v-if='contentData.journalData.category==3 || contentData.journalData.category==4'>
                            <div class='font20 color3 mt24'><label>{{contentData.journalData.title}}</label> </div>
                            <div v-if='contentData.journalData.category==3'>
                                <div class="media mt24">
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)">
                                        <img :src="contentData.userInfo[contentData.journalData.sign_as_uid].photoUrl" data-holder-rendered="true" class="contentAvatar">
                                        </a>
                                    </div>
                                    <div class="media-body pt8 media-middle">
                                        <h4  class="media-heading font12">{{contentData.userInfo[contentData.journalData.sign_as_uid].name}}</h4>
                                        <div class='text-muted'>{{contentData.userInfo[contentData.journalData.sign_as_uid].hrPosition}}</div>
                                    </div>
                                </div>
                            </div>
                            <div class='mt24' v-if='contentData.journalData.category==4'>
                                <div class='mt24 mb24 breakAll' v-html='contentData.deptData.deptDesc'></div>
                                <div v-if='contentData.deptData.staffs.length!=0'>                                
                                    <p class='color3 font14'><strong><?php echo Yii::t("directMessage", "Team members"); ?></strong> <span class="badge ml5">{{contentData.deptData.staffs.length}}</span></p>
                                    <div v-for='(list,index) in contentData.deptData.staffs' class='mt8 mb8' >
                                        <div class="media">
                                            <div class="media-left pull-left media-middle">
                                                <a href="javascript:void(0)">
                                                    <img class="contentAvatar" :src="contentData.userInfo[list].photoUrl" data-holder-rendered="true" >
                                                </a>
                                            </div>
                                            <div class="media-body pt8 media-middle">
                                                <h4  class="media-heading font12">{{contentData.userInfo[list].name}}</h4>
                                                <div class='text-muted'>{{contentData.userInfo[list].hrPosition}}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-else>
                            <form class="form-horizontal">
                                <div class="form-group" v-if='contentData.journalData.type=="teacher"'>
                                    <div class="col-sm-12">
                                        <h3><strong class='font18 titleCn'> {{contentData.journalData.title}}</strong></h3>
                                    </div>
                                </div>
                                <div  v-if='contentData.journalData.type=="leader"'>
                                    <div class="form-group">
                                        <div class="col-sm-12">
                                            <h3><strong class='font18 titleCn'> {{contentData.journalData.title_cn}}</strong></h3>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="col-sm-12">
                                        <h3><strong class='font18 titleEn'>{{contentData.journalData.title_en}}</strong></h3>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group" >
                                    <div class="col-sm-12 pt7">
                                        <span class="label label-default defaultBg defaultBg mr5 mb5" > {{journalCategory[contentData.journalData.category]}}</span>
                                    </div>
                                </div>
                                <div class="form-group"  v-if='contentData.journalData.type=="leader"'>
                                    <div class="col-sm-12 pt7">
                                        <span class="label label-default defaultBg defaultBg mr5" v-for='(item,id) in contentData.journalData.grade_group_id'>{{gradeGroupList[item]}}</span>
                                    </div>
                                </div>
                                <div class="form-group" v-if='contentData.journalData.type=="teacher"'>
                                    <div class="col-sm-12 pt7">
                                    <span class="label label-info mr5 mb5 fontWeightNormal pull-left" v-for='(item,id) in contentData.journalData.subjects'>{{subjectList[item]}}</span>
                                    </div>
                                </div>       
                                <div class="media mt24 mb24">
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)">
                                            <img class="contentAvatar" :src="contentData.userInfo[contentData.journalData.sign_as_uid].photoUrl" data-holder-rendered="true" >
                                        </a>
                                    </div>
                                    <div class="media-body pt8 media-middle">
                                        <h4  class="media-heading font12">{{contentData.userInfo[contentData.journalData.sign_as_uid].name}}<span class="label label-default color6 tagLabel  ml5"><?php echo Yii::t("directMessage", "Signature"); ?></span></h4>
                                        <div class='text-muted'>{{contentData.journalData.sign_as_title}}</div>
                                    </div>
                                </div>
                                <div class="form-group" v-if='contentData.journalData.type=="teacher"'>
                                    <div class="col-sm-12 pt7" style="padding: 0 30px;">
                                        <div class='content contentCn' id='contentCn' v-html='contentData.journalData.content'></div>
                                    </div>
                                </div>
                                <div v-if='contentData.journalData.type=="leader"'>
                                    <div class="form-group">
                                        <div class="col-sm-12 pt7" style="padding: 0 30px;">
                                            <div class='content contentCn' id='contentCn' v-html='contentData.journalData.content'></div>
                                        </div>
                                    </div>
                                    <div class="form-group" >
                                        <div class="col-sm-12 pt7" style="padding: 0 30px;">
                                            <div class='content contentEn'  id='contentEn'  v-html='contentData.journalData.content_en'></div>
                                        </div>
                                    </div>
                                </div>
                                <div class='mt24'  v-if='contentData.journalData.attachments.length!=0'>
                                    <p class='color3 font14'><strong><?php echo Yii::t("curriculum", "Attachments"); ?></strong> <span class="badge ml5">{{contentData.journalData.attachments.length}}</span></p>
                                    <div v-for='(list,index) in contentData.journalData.attachments' class='mt16' style='word-break:break-all;'>
                                        <a :href="list.file_key" target='_blank'><span class='glyphicon glyphicon-paperclip mr4'></span>{{list.title}}</a>    
                                    </div>
                                </div>
                                <div class='mt24'>
                                    <p class='color3 font14'><strong><?php echo Yii::t("newDS", "Subscribers"); ?></strong> <span class="badge ml5">{{contentData.journalData.targets_num}}</span></p>
                                    <div class='mt16'>
                                        <img class="contentPostObject mr16 mb8"  v-for='(list,index) in contentData.journalData.targets'  :src="contentData.childData[list].avatar" data-holder-rendered="true" >    
                                        <span class='postMore cur-p' v-if='contentData.journalData.targets_num>10' @click='showMore(contentData.journalData)'>+ {{contentData.journalData.targets_num-10}} ></span>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class='col-md-6 col-sm-6 ' :class='borderShow?"borderLeft":""' ref='comment'>
                        <p class='font14 color3'><?php echo Yii::t("global", "Comment");?></p>
                        <div  v-if='Object.keys(commentChildList).length!=0'>
                            <div class='pt10 pb10 borderbto'>
                                <div v-for='(childList,key,index) in commentChildList' class='pull-left flex wechat'  style='width:60px;height:55px;text-align:center;' >
                                    <span class='inline-block' :class='key==commentChildId?"commentBorder":""' >
                                        <img class='img-circle commentImage cur-p'  onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' :title="`<div>${childList.name}</div>${childList.class_name}`"  data-placement="top"  @click='commentList(key)'  :src="childList.avatar" alt="">                                    
                                    </span>
                                </div>
                                <div class='clearfix'></div>
                            </div>
                            <div class="alert alert-info mt20" role="alert" v-if='commentChildId==""'><?php echo Yii::t("newDS", "Click a child to view comments.");?></div>
                        </div>
                        <div class="alert alert-warning" role="alert" v-else><?php echo Yii::t("newDS", "No Comment");?></div>
                        <div class=' scroll-box messageChild relative'>
                            <div class='loading'  v-if='commentLoading'>
                                <span></span>
                            </div>
                            <div  v-for='(fItem,i) in commentData.items'>
                                <div class="media mt20" v-if='fItem.mark_as_staff==0'>
                                    <div class='flex' v-if="fItem.creator_type == 'parent'">
                                        <div class="">
                                            <input type="checkbox" class='mr15' v-if='isForward' :value='i' v-model='inputForward'>
                                            <img :src="commentChildData.avatar" data-holder-rendered="true" class="img-circle commentImage">
                                        </div>
                                        <div class="flex1">
                                            <div class="flex1 ml8" v-if='fItem.repost==0 || fItem.repost==1'>
                                                <h4 class="media-heading color3 font14 flex1" style='line-height:37px'><strong>{{commentChildData.name}}</strong> </h4>
                                                <div class="content-2" v-html='html(fItem.content)'></div>
                                                <div>
                                                    <ul class='mb12 imgLi' :id='fItem.id' v-if='fItem.imgUrl.length!=0'>
                                                        <li v-for='(list,j) in fItem.imgUrl'>
                                                            <img :src="list.url" class='fileImg mr8' @click='showImg(fItem)' alt="" >
                                                        </li>
                                                    </ul>
                                                    <div >
                                                        <div class='flex fileLink' v-for='(list,j) in fItem.pdfUrl'>
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/PDF1.png' ?>" alt="" class="pdfImg" v-if='list.mimetype=="application/pdf"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/excel.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/vnd.ms-excel" || list.mimetype=="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/word1.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/msword" || list.mimetype=="application/vnd.openxmlformats-officedocument.wordprocessingml.document"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/zip.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/x-zip-compressed"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" v-else /> 
                                                            <a class='flex1 ml5' target= "_blank" :href="list.url">{{list.title}}
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="text-muted" v-if="commentChildData[`p_${fItem.created_by}`] == 'f'"><?php echo Yii::t("newDS", "From student's Dad");?><span class='ml16'>{{ fItem.updated_at }}</span></div>
                                                <div class="text-muted font12" v-else><?php echo Yii::t("newDS", "From student's Mom");?> <span class='ml16'>{{ fItem.updated_at }}</span> </div>
                                                <div class='forwarded color6 mt15' v-if='fItem.repost==1'>
                                                    <div v-if='fItem.repost_to_type==3'><?php echo Yii::t("directMessage", "Handover to: ");?>{{commentData.staff[fItem.repost_to_target].name}}</div>  
                                                    <div v-if='fItem.repost_to_type==4'><?php echo Yii::t("directMessage", "Handover to: ");?>{{commentData.deptInfo[fItem.repost_to_target]}}</div>  
                                                    <div  class='mt5 flex' v-if='fItem.repost_to_mark!=""'><span><?php echo Yii::t("labels", "Memo");?>：</span><div class='flex1' v-html='html(fItem.repost_to_mark)'></div></div>
                                                </div>
                                            </div>
                                            <div class="flex1 ml8"  v-if='fItem.repost==2'>
                                                <div class="align-items flex">
                                                    <h4 class="media-heading color3 font14 flex1" style='line-height:37px'><strong>{{commentChildData.name}}</strong> </h4>
                                                    <div class='font14 text-primary cur-p' @click='showOriginalInfo(fItem.id)'><img width='16' src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/more.png' ?>" alt=""> <?php echo Yii::t("directMessage", "Original Information");?></div>
                                                </div>
                                                <div class="content-2" v-html='html(fItem.content)'></div>
                                                <div>
                                                    <ul class='mb12 imgLi' :id='fItem.id' v-if='fItem.imgUrl.length!=0'>
                                                        <li v-for='(list,j) in fItem.imgUrl'>
                                                            <img :src="list.url" class='fileImg mr8' @click='showImg(fItem)' alt="" >
                                                        </li>
                                                    </ul>
                                                    <div >
                                                        <div class='flex fileLink' v-for='(list,j) in fItem.pdfUrl'>
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/PDF1.png' ?>" alt="" class="pdfImg" v-if='list.mimetype=="application/pdf"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/excel.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/vnd.ms-excel" || list.mimetype=="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/word1.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/msword" || list.mimetype=="application/vnd.openxmlformats-officedocument.wordprocessingml.document"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/zip.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/x-zip-compressed"' />
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" v-else /> 
                                                            <a class='flex1 ml5' target= "_blank" :href="list.url">{{list.title}}
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="text-muted font12" ><?php echo Yii::t("directMessage", "Handover at: ");?>{{ fItem.updated_at }}</div>
                                                <div class='forwarded color6 mt15' v-if='fItem.repost==2'>
                                                    <div><?php echo Yii::t("directMessage", "Handover from: ");?>{{commentData.staff[fItem.repost_by].name}}</div>  
                                                    <div  class='mt5 flex'  v-if='fItem.repost_mark!=""'><span><?php echo Yii::t("labels", "Memo");?>：</span><div class='flex1' v-html='html(fItem.repost_mark)'></div></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class='flex' v-else>
                                        <div class="">
                                            <input type="checkbox" class='mr15' disabled  v-if='isForward'>
                                            <img :src="commentData.staff[fItem.created_by].avatar" data-holder-rendered="true" class="img-circle commentImage ">
                                        </div>
                                        <div class="flex1 pl12">
                                            <h4 class="media-heading color3 font14 "  style='line-height:37px'><strong>{{commentData.staff[fItem.created_by].name}}</strong> </h4>
                                            <div class='color3 font14' v-html='fItem.content'></div>
                                            <div>
                                                <ul class='mb12 imgLi' :id='fItem.id' v-if='fItem.imgUrl.length!=0'>
                                                        <li v-for='(list,j) in fItem.imgUrl'>
                                                            <img :src="list.url" class='fileImg mr8' @click='showImg(fItem)' alt="" >
                                                        </li>
                                                    </ul>
                                                <div >
                                                    <div class='flex fileLink' v-for='(list,j) in fItem.pdfUrl'>
                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/PDF1.png' ?>" alt="" class="pdfImg" v-if='list.mimetype=="application/pdf"' />
                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/excel.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/vnd.ms-excel" || list.mimetype=="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"' />
                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/word1.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/msword" || list.mimetype=="application/vnd.openxmlformats-officedocument.wordprocessingml.document"' />
                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/zip.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/x-zip-compressed"' />
                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" v-else /> 
                                                        <a class='flex1 ml5' target= "_blank" :href="list.url">{{list.title}}
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class='text-muted mt15'>{{ fItem.updated_at }}</div>
                                        </div>
                                    </div>
                                </div>
                                <div v-else class='p20 mt20 text-center borderTop media mt20' :class='i!=commentData.items.length-1?"borderB":""'>
                                    <p class='font14 color3'>
                                        <span class='glyphicon glyphicon-pushpin mr10' style='color:#EC971FFF'></span>{{commentData.staff[fItem.created_by].name}} <?php echo Yii::t("newDS", "marked No Reply Needed");?> <span class='font14 color6 ml10'>{{fItem.updated_at}}</span>
                                    </p>
                                    <div  class='font12 color9'>
                                        <?php echo Yii::t("newDS", "(This message is invisible to parents)");?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="flex" v-if='isForward'>
                            <div class='flex1'>
                                <div class="replyColor mb16 font14 flex"><span aria-hidden="true" class="glyphicon glyphicon-info-sign"  style='margin-top:2px'></span><span class='ml4'><?php echo Yii::t("directMessage", "Please select items and click the “Handover” button, then select target staff or deptartment.");?></span></div>
                            </div>
                            <div>
                                <span class='color6 mr16'><?php echo Yii::t("directMessage", "selected");?> {{inputForward.length}}</span>
                                <button type="button" class="btn btn-link mr20"  @click.stop="forwardCancel"><?php echo Yii::t("global", "Cancel");?></button>
                                <button class="btn btn-primary"  @click.stop="forwardTeacher()" :disabled="loading"  ><?php echo Yii::t("directMessage", "Handover");?></button>
                            </div>
                        </div>
                        <div v-else-if='commentData.items && commentData.items.length!=0 && (staff == 0 || staff == currentUid)'>
                            <div class='uploadImg' v-if='uploadImgList.length>0'>
                                <div class='imgData mr8'  v-for='(list,i) in uploadImgList'>
                                    <div v-if="list.types=='1'">
                                        <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Uploading");?></div>
                                    </div>
                                    <div v-else>
                                        <img class='fileImgs' @click='uploadShowImg(list.file_key)'  :src="list.file_key" alt="">
                                        <span aria-hidden="true"  @click.stop='delImg("img",list,i)'>×</span>
                                    </div>
                                </div>
                                <template v-if='loadingType==1'>
                                    <div class='imgData mr8'  v-for='(list,i) in loadingList'>
                                            <div class='uploadLoading'> <?php echo Yii::t("directMessage", "Upload queue");?></div>
                                    </div>
                                </template>
                            </div>
                            <div class='mt16' v-if='uploadLinkList.length>0'>
                                <div class='flex uploadFile' v-for='(list,index) in uploadLinkList'>
                                    <span class='glyphicon glyphicon-paperclip mr8'></span>
                                    <a target="_blank" class='flex1'  style='line-height:26px' :href='list.file_key' v-if='!list.isEdit'>{{list.title}}</a>
                                    <span class='flex1' v-else><input type="input" class='inputStyle'  @keyup.enter="saveFile(list,index)"  v-model='attachmentName' ></span>
                                    <template v-if='!list.isEdit'>
                                        <span class='glyphicon glyphicon-edit iconFile mr16' v-if='list.file_key!=""' @click.stop='list.isEdit=true,attachmentName=list.title'></span>
                                        <span class='glyphicon glyphicon-trash iconFile' v-if='list.file_key!=""' @click.stop='delImg("link",list,index)'></span>
                                    </template>
                                    <span style='width:90px'  class='text-right inline-block' v-else>
                                        <button type="button" class="btn btn-primary btn-xs" @click='saveFile(list,index)'><?php echo Yii::t("global", "Save");?></button>
                                        <button type="button" class="btn btn-default btn-xs" @click='list.isEdit=false'><?php echo Yii::t("global", "Cancel");?></button>
                                    </span>
                                </div>
                            </div>
                            <template v-if='loadingType==2'>
                                <div class='flex uploadFile'  v-for='(list,i) in loadingList'>
                                    <span class='glyphicon glyphicon-paperclip mr8'></span>
                                    <a  class='flex1'  style='line-height:26px' > <?php echo Yii::t("directMessage", "Upload queue");?></a>
                                </div>
                            </template>
                            <div class='uploadIcon'>
                                <div class='cur-p inline-block' id='pickfilesPhoto'>
                                    <span class='glyphicon glyphicon-picture pull-left' style='margin-top:2px'></span>
                                    <span class='ml5 font14 color6'><?php echo Yii::t("directMessage", "Photo");?></span> 
                                </div>
                                <div class='cur-p inline-block ml10' id='pickfilesPDF'>
                                    <span class='glyphicon glyphicon-paperclip pull-left' style='margin-top:2px'></span>
                                    <span class='ml5 font14 color6'><?php echo Yii::t("curriculum", "Attachments");?></span> 
                                </div>
                            </div>
                            <textarea class="form-control" rows="5" v-model='commentContent' placeholder='<?php echo Yii::t("newDS", "Please input your reply");?>'></textarea>
                            <div class='flex align-items'>
                                <div class='font14 cur-p flex1'>
                                    <div v-if='contentData.category==3 || contentData.category==4'>
                                    <span class='text-primary pull-left cur-p font14' @click.stop='forwardEvent()' ><?php echo Yii::t("directMessage", "Handover");?></span>
                                    <span class="glyphicon glyphicon-info-sign color6 ml5 p2"   onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' title="<?php echo Yii::t("directMessage", "If enquiries should not be answered by you, you can handover to others.");?>"  data-placement="right"></span>
                                    </div>
                                 </div>
                                <p class='text-right pull-right mt10'>
                                    <button type="button" class="btn btn-link mr20"  @click.stop="markNoReply"><?php echo Yii::t("newDS", "No Reply Needed");?></button>
                                    <button class="btn btn-primary" @click.stop="reply('0')" :disabled="loadingBtn"><?php echo Yii::t("newDS", "Reply");?></button>
                                </p>
                            </div>
                            
                            <div class='clearfix'></div>
                        </div>
                        <br>
                        <div v-if='commentData.items && commentData.items.length!=0 && staff > 0 && staff != currentUid' class="alert alert-warning" role="alert"><?php echo Yii::t('newDS', 'You cannot reply journals of other teachers in review mode.'); ?></div>
                    </div>
                    <div class='clearfix'></div>
                </div>
                </div>
            </div>
        </div>
        <!-- 查看全部发布对象 -->
        <div class="modal fade" id="postObjectModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "查看全部发布对象"); ?></h4>
                    </div>
                    <div class="modal-body" >
                        <div class="form-group">
                            <div class="input-group">
                            <div class="input-group-addon"><span class='glyphicon glyphicon-search'></span></div>
                            <input type="text" class="form-control" v-model='search' placeholder="请输入姓名">
                            </div>
                        </div>
                        <div class='font14'>
                            <span class='glyphicon glyphicon-user color6'> </span><span class='color3 pl4'>共{{Object.keys(searchData).length}}人</span>
                        </div>
                        <div>
                            <div class="media mt10 mb10 col-md-6 col-sm-6"  v-for='(list,key,index) in searchData' >
                                <div class="media-left pull-left media-middle">
                                    <a href="javascript:void(0)">
                                        <img class="contentAvatar" :src="list.avatar" data-holder-rendered="true" >
                                    </a>
                                </div>
                                <div class="media-body pt8 media-middle">
                                    <h4  class="media-heading font12">{{list.name}}</h4>
                                    <div class='text-muted'>{{list.class_name}}</div>
                                </div>
                            </div>  
                            <div class='clearfix'></div>  
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 标记为未回复 -->
        <div class="modal fade" id="noReplyModal" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-sm" role="document">
                <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "Warning");?></h4>
                </div>
                <div class="modal-body">
                    <div ><?php echo Yii::t("newDS", 'Proceed to mark it as "No Reply Needed"?');?></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button" class="btn btn-primary" @click='reply("noReply")'><?php echo Yii::t("message", "OK");?></button>
                </div>
                </div>
            </div>
        </div>
        <div class="modal fade" id="delModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
            <div class="modal-dialog modal-sm" role="document">
                <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "Delete");?></h4>
                </div>
                <div class="modal-body">
                    
                    <div v-if='delStatus==10'><?php echo Yii::t("newDS", "Cannot delete due to existence of parent comments, make it offline instead?");?></div>        
                    <div v-else><?php echo Yii::t("newDS", "Confirm to delete this item?");?></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    
                    <button type="button" v-if='delStatus==10' class="btn btn-primary" @click='journalOffline()'><?php echo Yii::t("workflow", "Offline");?></button>

                    <button type="button" v-else class="btn btn-primary" @click='delList(delData,delType)'><?php echo Yii::t("newDS", "Delete");?></button>
                </div>
                </div>
            </div>
        </div>
        <!-- 二维码 -->
        <div class="modal fade" id="qrcodeModal" tabindex="-1" role="dialog"  >
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t("newDS", "Qr-codes list");?></h4>
                </div>
                <div class="modal-body">
                    <div class='col-md-6 col-sm-6 text-center'>
                        <p><strong><?php echo Yii::t("newDS", "For Preview Only");?></strong> </p>                   
                        <div><?php echo Yii::t("newDS", "Don't Share");?></div>        
                        <div class='mt20'>
                            <img class='qrcodeImg' :src="previewQrcode" alt="">
                        </div>     
                        <div class='mt15 mb20'>
                            <button type="button" class="btn btn-primary"  @click='shareImg("view")'><?php echo Yii::t("reg", "Beautify this QrCode");?></button>
                        </div>      
                    </div>
                    <div class='col-md-6 col-sm-6 text-center'>
                        <p><strong><?php echo Yii::t("newDS", "For Parent Sharing");?></strong></p>                   
                        <div><?php echo Yii::t("newDS", "Need Parent Identity");?></div> 
                        <div class='mt20'>
                            <img class='qrcodeImg'  :src="shareQrcode" alt="">
                        </div>   
                        <div class='mt15 mb20'>
                            <button type="button" class="btn btn-primary" @click='shareImg("parent")'><?php echo Yii::t("reg", "Beautify this QrCode");?></button>
                        </div>           
                    </div>
                    <div class='clearfix'></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                </div>
                </div>
            </div>
        </div>
        <div class="modal fade" id="shareModal" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t("reg", "QrCode template");?></h4>
                </div>
                <div class="modal-body">
                    <?php $this->renderPartial("share");?>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                </div>
                </div>
            </div> 
        </div>
        <!-- 阅读统计 -->
        <div class="modal fade" id="readModal" tabindex="-1" role="dialog"  >
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t("newDS", "Read Report");?></h4>
                </div>
                <div class="modal-body">
                    <div class='loading'  v-if='readLoading'>
                        <span></span>
                    </div>
                    <div v-if='targetsChildId!=""'>
                        <div class='col-md-8 pb20'  style='border-right:1px solid #E4E7EDFF'>
                            <div class='col-md-12'>
                                <div class='flex mt10' >
                                    <div style='width:85px'>
                                        <img :src="reviewChilds[targetsChildId].avatar" style='width:70px;height:70px' class='media-object img-circle image' alt="">
                                    </div>
                                    <div class='flex1 mt20'>
                                        <p class='font14 color3 mb5'><strong>{{reviewChilds[targetsChildId].name}}</strong> </p>
                                        <div class='font12 color6'>{{reviewChilds[targetsChildId].class_name}}</div>
                                    </div>
                                </div>
                            </div>
                            <div class='col-md-6' v-for="(list,index) in targetsParent">
                                <p class='font14 color3 mb10 relative mt15'><span class='readPoint'></span><span class='ml15'>{{list.parent_relationship_name}}</span> </p>
                                <div class='ml15'>
                                    <p class='mb10 font14 flex'><span style='width:15px;padding-top: 3px;' class='glyphicon glyphicon-user color6'></span><span class='ml10 flex1'>{{list.parent_name}}</span></p>
                                    <p class='mb10 font14 color6 flex' ><span style='width:15px;padding-top: 3px;' class='glyphicon glyphicon-earphone'></span> <span class='ml10 flex1'>{{list.parent_tel}}</span></p>
                                    <p class='mb10 font14 color6 flex'><span style='width:15px;padding-top: 3px;' class='glyphicon glyphicon-envelope'></span> <span class='ml10 flex1' style='word-break:break-all;'>{{list.parent_email}}</span></p>
                                </div>
                            </div>
                        </div>
                        <div class='col-md-4 pb20'>
                            <p><strong><?php echo Yii::t("newDS", "Wechat message receivers");?></strong>  </p>
                            <div class=' scroll-box' style='max-height:185px;overflow-y:auto' v-if='wxReceivers && wxReceivers.length!=0'>
                                <div class='flex mt10'  v-for='(list,index) in wxReceivers'>
                                    <div style='width:50px'>
                                            <img v-if='list.headimgurl!=""' :src="list.headimgurl" style='width:40px;height:40px' class='media-object img-circle image' alt="">
                                            <img v-else src="http://m2.files.ivykids.cn/cloud01-file-8025768Fn6dkbMWLydi9RSVfmuPHU2CITQ6.png" style='width:40px;height:40px' class='media-object img-circle image' alt="">
                                    </div>
                                    <div class='flex1'>
                                        <p><span class="label label-info" v-if='list.isDev'>IT DEV</span> <strong> {{list.nickname}}</strong></p>
                                        <div><?php echo Yii::t("newDS", "Recent received:");?> {{list.recent}}</div>
                                    </div>
                                </div>
                            </div>
                            <div v-else class="alert alert-warning" role="alert"><?php echo Yii::t("newDS", "No messsage sent successfully in recent 60 days.");?></div>
                        </div>
                    </div>
                    <div class='pt10 col-md-12'  :style='targetsChildId!=""?"border-top:1px solid #E4E7EDFF":""'>
                        <div class='col-md-6 col-sm-6 text-center '  :class='clientWidth>"750"?"borderRight":""'>     
                            <div id='echart' style='height:400px;width:100%'></div>     
                        </div>
                        <div class='col-md-6 col-sm-6 scroll-box' style='max-height:400px;overflow-y:auto;padding:5px 18px'>
                            <p class='font14 color3 mb5'><?php echo Yii::t("newDS", "Subscribers List");?></p>
                            <div class='pt10' style='padding-left:20px'  v-if='reviewTargets.length!=0'>
                                <div class='pull-left flex wechat' v-for='(list,index) in reviewed'  style='width:50px;height:50px;text-align:center;' >
                                    <span class='inline-block' :class='list==targetsChildId?"readBorder":""'  style='margin:8px 10px'>
                                        <img class='img-circle childImage cur-p'  onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' :title="`<div>${reviewChilds[list].name}</div>${reviewChilds[list].class_name}`"  data-placement="top" @click='childInfo(list)'  :src="reviewChilds[list].avatar" alt="">
                                    </span>
                                </div>
                                <div class='pull-left flex wechat' v-for='(list,index) in unreadList'  style='width:50px;height:50px;text-align:center;' >
                                    <span class='inline-block' :class='list==targetsChildId?"readBorder":""'  style='margin:8px 10px;opacity:0.4;'>
                                        <img class='img-circle childImage cur-p'  onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' :title="`<div>${reviewChilds[list].name}</div>${reviewChilds[list].class_name}`"  data-placement="top"  @click='childInfo(list)' :src="reviewChilds[list].avatar" alt="">
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class='clearfix'></div>
                </div>
                </div>
            </div>
        </div>
        <div class="modal fade" id="parentReplyModal" tabindex="-1" role="dialog" >
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="parentReplyLabel">{{readData.title}}</h4>
                    </div>
                    <div class="modal-body">
                        <div class="well well-sm">  
                            <span class='text-muted mr10'><?php echo Yii::t("newDS", "Not-Read ");?><?php echo Yii::t("newDS", "(Gray)");?> </span> 
                            <a href="javascript:;" class='mr10'><?php echo Yii::t("newDS", "Read ");?><?php echo Yii::t("newDS", "(Blue)");?></a> 
                        </div>
                        <div class="panel panel-default" v-for='(list,index) in reviewedChild'>
                            <div class="panel-heading">
                                {{list.title}}
                            </div> 
                            <div class="panel-body row">
                                <ul class="nav nav-pills">
                                    <li  v-for='(item,key) in list.item'>
                                        <a target="_blank" :class='readChildid(item.id,"view")?"":"text-muted"' :href="'<?php echo $this->createUrl('/child/index/index'); ?>&childid='+item.id" class="labels">{{item.name}}
                                        </a>
                                    </li>
                                </ul> 
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 教师列表 -->
        <div class="modal fade" id="teacherList" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" ><?php echo Yii::t('attends','Select a Teacher') ?> <span id="teacherList_t"></span></h4>
                    </div>
                    <div class="form-horizontal">
                        <div class="modal-body">
                            <div name='teacherId'>
                                <a v-for='teacherId in normalList'  class='mb5 btn btn-default mr10' :href='getUrl(teacherId.uid)'>
                                    {{teacherId.name}}
                                </a>
                            </div>
                            <div v-if='resignList.length!=0'>
                                <h4><?php echo Yii::t('newDS', 'Resigned'); ?></h4>
                                <div name='teacherId'>
                                    <a v-for='teacherId in resignList'  class='mb5 btn btn-default mr10' :href='getUrl(teacherId.uid)'>
                                        {{teacherId.name}}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 移交 -->
        <div class="modal fade" id="forwardModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("directMessage", "Handover");?></h4>
                </div>
                <div class="modal-body p24" >
                    <div class='row'>
                        <div class='col-md-6 borderRight' style='min-height:480px' v-if='inputForward.length!=0 && commentChildId!=""'>
                            <div><label class='font14'><?php echo Yii::t("directMessage", "Handover Content");?></label></div>
                            <div class='color6'><?php echo Yii::t("directMessage", "Multiple messages will be merged.");?></div>
                            <div class='flex mt15'>
                                <div>
                                    <img :src="commentChildData.avatar" height="50" width="50" class="img-circle" style="object-fit: cover;">
                                </div>
                                <div class='flex1 ml15'>
                                    <div class='color3 font14 mt5'>{{commentChildData.name}}</div>
                                    <div class='color6 mt5'>{{commentChildData.class_name}}</div>
                                    <div class='forwardText mt20'>
                                        <div v-for='(list,index) in inputForward' class=''>
                                            <div class='color3 font14 mb12' v-html='html(commentData.items[list].content)'></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class='col-md-6'>
                            <div class='mt16 mb16'>
                                <label class="radio-inline">
                                    <input type="radio" v-model="forwardType" value="3"> <?php echo Yii::t("directMessage", "Handover to staff");?> 
                                </label>
                                <label class="radio-inline">
                                    <input type="radio"  v-model="forwardType" value="4"> <?php echo Yii::t("directMessage", "Handover to a team");?>
                                </label>
                            </div>
                            <div class='mb20' v-if='forwardType==3'>
                                <div class="select" >
                                    <input type="text" readonly="readonly" autocomplete="off" placeholder="<?php echo Yii::t("directMessage", "Handover to staff");?> "  @blur="optionShow=false"
                                         @focus='optionShow=true' class='input'
                                        v-model="teacherName">
                                        <span class='glyphicon glyphicon-chevron-down icon'></span>
                                    <ul v-show='optionShow'>
                                        <li v-for="(item,key) in teacherListForward" :key="key+'opt'" @mousedown="mousedown(item,'teacherName')" class='flexbox justify_bet'>
                                            <div class="media">
                                                <div class="media-left pull-left media-middle">
                                                    <a href="javascript:void(0)">
                                                        <img :src="item.photoUrl" data-holder-rendered="true" class="media-object img-circle contentAvatar">
                                                    </a>
                                                </div>
                                                <div class="media-body mt5 media-middle">
                                                    <h4 class="media-heading font14 color3 text_overflow">{{item.name}}</h4>
                                                    <div class="color6  text_overflow font12">{{ item.hrPosition }}</div>
                                                </div>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <div class='mb20' v-if='forwardType==4'>
                                <div class="select" >
                                    <input type="text" readonly="readonly" autocomplete="off" placeholder="<?php echo Yii::t("directMessage", "Handover to a team");?> "  @blur="optionShow=false"
                                         @focus='optionShow=true' class='input'
                                        v-model="deptName">
                                        <span class='glyphicon glyphicon-chevron-down icon'></span>
                                    <ul v-show='optionShow'>
                                        <li v-for="(item,key) in deptList" :key="key+'opt'" @mousedown="mousedown(item,'deptName')" class='flexbox justify_bet'>
                                        <h4 class="media-heading font14 color3 text_overflow">{{item.name}}</h4>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <div>
                                <div class='mb16'><span class='font14 color3'><?php echo Yii::t("labels", "Memo");?></span> <span class='color6 font12 ml10'><?php echo Yii::t("directMessage", "Only handover target can see the memo");?></span></div>
                                <textarea class="form-control" rows="3" v-model='forwardMark'></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button" class="btn btn-primary" @click='repost'><?php echo Yii::t("directMessage", "Confirm to handover");?></button>
                </div>
                </div>
            </div>
        </div>
        <div class="modal fade" id="originalInfoModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("directMessage", "Original Information");?></h4>
                </div>
                <div class="modal-body p24" >
                    <div class='flex mt15' v-if='commentChildId!=""'>
                        <div>
                            <img :src="commentChildData.avatar" height="50" width="50" class="img-circle" style="object-fit: cover;">
                        </div>
                        <div class='flex1 ml15'>
                            <div class='color3 font14 mt5'>{{commentChildData.name}}</div>
                            <div class='color6 mt5'>{{commentChildData.class_name}}</div>
                            <div class='forwardText mt20'>
                                <div v-for='(list,index) in repostDetailList.commentList' class=' pb16 pt16' :class='repostDetailList.commentList.length!=(index+1)?"borderBto":""'>
                                    <div class='color3 font14 mb12' v-html='html(list.content)'></div>
                                    <div class='color6 font12'>
                                        <div class='mb8'><?php echo Yii::t("directMessage", "Originally sent to: ");?>{{repostDetailList.contactInfo[list.link_id]}}</div>
                                        <div class='mb8'><?php echo Yii::t("directMessage", "Submitted at: ");?>{{list.updated_at}}</div>
                                        <div>
                                            <span v-if="commentData.items[commentChildId][`p_${list.created_by}`] == 'f'"><?php echo Yii::t("newDS", "From student's Dad");?></span>
                                            <span v-else><?php echo Yii::t("newDS", "From student's Mom");?> </span> 
                                        </div>
                                       
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                </div>
                </div>
            </div>
        </div>
</div>
</div>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    $(document).ready(function () {
        $(document).on('show.bs.modal', '.modal', function (event) {
            var zIndex = 1040 + (10 * $('.modal:visible').length);
            $(this).css('z-index', zIndex);
            setTimeout(function() {
                $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');

            }, 0); 
        })
        $(document).on('hidden.bs.modal', '.modal', function(event) {       
            if ($('.modal.in').size() >=1) {      
                $('body').addClass('modal-open')     
            }    
        });  
    });
    
    var data = <?php echo json_encode($data); ?>;
    var managetypeList = <?php echo json_encode($this->managetypeList); ?>;
    var type = <?php echo json_encode($type); ?>;
    var toReplyNum = '<?php echo $this->toReplyNum; ?>';
    var staff = <?php echo CHtml::encode(Yii::app()->request->getParam('staff', Yii::app()->user->getId())); ?>;
    var container = new Vue({
        el: "#container",
        data: {
            journalTypeList:data.journalTypeList,
            gradeGroupList:data.gradeGroupList,
            managetypeList:managetypeList,
            classList:data.classList,
            leaderData:data.leaderList,
            subjectList:data.subjectList,
            toReplyNum:toReplyNum,
            journalCategory:data.journalCategory,
            teacherList:[],
            leaderList:[],
            leaderPage:{},
            teacherPage:{},
            leaderPage: {},
            teacherPage: {},
            leaderCopyPage: {},
            teacherCopyPage: {},
            teacherPageNum:1,
            leaderPageNum:1,
            leaderuserList:{},
            teacheruserList:{},
            teacherFilterType:'all',
            gradeGroupType:'all',
            teacherLoading:false,
            leaderLoading:false,
            loading:false,
            contentData:{},
            attachments:[],
            delData:{},
            delType:'',
            delStatus:'',
            delData:{},
            previewQrcode:'',
            shareQrcode:'',
            reviewChilds:{},
            reviewed:[],
            unreadList:[],
            reviewTargets:[],
            targetsChildId:'',
            targetsParent:[],
            readLoading:false,
            clientWidth:'',
            readData:{},
            reviewedChild:{},
            leaderSearchText:"",
            teacherSearchText:"",
            journalId:'',
            commentData:{},
            loadingBtn:false,
            commentContent:'',
            commentChildId:'',
            commentChildData:{},
            commentLoading:false,
            borderShow:false,
            staff:staff,
            wxReceivers:[],
            parentReviewed:[],
            teacherIds:[],
            currentUid: <?php echo Yii::app()->user->getId(); ?>,
            resignList:[],
            normalList:[],
            shareList:{},
            shareType:'',
            showSummary:true,
            showPublisher:true,
            htmlContent:1,
            sharetUser:'',
            isSearch:false,
            copyTeacherSearchText:'',
            copyLeaderSearchText:'',
            school_title:'',
            isForward:false,
            inputForward:[],
            forwardTo:'',
            options:[],
            forwardType:'',
            forwardMark:'',
            teacherListForward:[],
            deptList:[],
            repostDetailList:[],
            optionShow:false,
            teacherName:'',
            deptName:'',
            commentChildList:{},
            forwardCategory:'',
            postObjectList:{},
            search:'',
            uploadImgList:[],
            uploadLinkList:[],
            attachmentName:'',
            uploadShow:false,
            loadingList:[],
            loadingType:0,
            token:''
        },
        watch:{
            forwardType(old,newValue){
                if(old!=newValue){
                    this.forwardTo=''
                    this.teacherName=''
                    this.deptName=''
                }
            }
        },
        created:function(){
            let that=this
            for(var i=0;i<this.managetypeList.length;i++){
                this.getList(that.managetypeList[i],'')
            }
        },
        computed: {
            searchData: function() {
                var search = this.search;
                var searchVal = ''; //搜索后的数据
                if(search) {
                    searchVal =Object.values(this.postObjectList).filter(function(product) {
                        return Object.keys(product).some(function(key) {
                            return String(product['name']).indexOf(search) !== -1;
                        })
                    })
                    return searchVal;
                }
                return this.postObjectList;
            }
        },
        methods: {
            html(data){
              return  data.replace(/\n/g, '<br/>')
            },
            shareImg(type){
                this.htmlContent=1
                this.showPublisher=true
                this.shareType=type
                $('#shareModal').modal('show')
            },
            popover(list,type,id){
                this.delData=list;
                this.delType=type;
                this.delStatus=1;
                $('#delModal').modal('show')
            },
            newDate(dateTime){
                let time = new Date(dateTime).getTime();
                const date = new Date(time);
                const Y = date.getFullYear() + '-';
                const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
                const D = (date.getDate() < 10 ? '0'+date.getDate() : date.getDate()) + '  ';
                const h = (date.getHours() < 10 ? '0'+date.getHours() : date.getHours()) + ':';
                const m = (date.getMinutes() <10 ? '0'+date.getMinutes() : date.getMinutes()) + ':';
                const s = date.getSeconds() <10 ? '0'+date.getSeconds() : date.getSeconds() // 秒
                const dateString = Y + M + D + h + m + s;
                return dateString;
            },
            classId(id){
                var title
                this.classList.forEach(item => {
                     if (item.classid==id) {
                        title= item.title
                    } 
                })
                return title
            },
            leaderUser(id,type){
                var data
                this.leaderuserList.forEach(item => {
                     if (item.uid==id) {
                         data= item[type]
                    } 
                })
                return data
            },
            teacherUser(id,type){
                var data
                this.teacheruserList.forEach(item => {
                     if (item.uid==id) {
                         data= item[type]
                    } 
                })
                return data
            },
            getList(typedata,filter,page){
                var _this=this;
                if(page){
                    _this[typedata + 'Loading'] = true
                }else{
                    _this.loading = true
                }
                var dataList
                if(typedata=='teacher'){
                    dataList={
                        type:typedata,
                        isMine:'1',
                        pageNum: page ? this.teacherPageNum : 1,
                        searchText:this.teacherSearchText,
                        staff: _this.staff,
                    }
                }else{
                    dataList={
                        type:typedata,
                        isMine:'1',
                        searchText:this.leaderSearchText,
                        pageNum: page ? this.leaderPageNum : 1,
                        staff: _this.staff,
                    }
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("getList")?>',
                    type: "post",
                    dataType: 'json',
                    data: dataList,
                    success: function(data) {
                        if(data.state=='success'){  
                            _this.school_title=data.data.school_title
                            if(dataList.searchText!=''){
                                _this.isSearch=true
                                _this.copyTeacherSearchText= JSON.parse(JSON.stringify(_this.teacherSearchText))
                                _this.copyLeaderSearchText= JSON.parse(JSON.stringify(_this.leaderSearchText))
                            }else{
                                _this.copyTeacherSearchText=''
                                _this.copyLeaderSearchText= ''
                                _this.isSearch=false
                            }
                            if(data.data.list!=null){
                                _this[typedata+'List']=data.data.list
                            }else{
                                _this[typedata+'List']=[]
                            }
                            _this[typedata+'CopyPage']=data.data.pagination
                            _this[typedata+'userList']=data.data.userList
                            if(typedata=='teacher') {
                                _this.teacherIds=data.data.teacherIds
                            }
                            if(page){
                                _this[typedata + 'Loading'] = false
                            }else{
                                _this.loading = false
                                _this[typedata + 'Page'] = data.data.pagination
                                _this.initPage(typedata)
                            }
                        }
                    },
                    error: function(data) {
                        if(page){
                            _this[typedata + 'Loading'] = false
                        }else{
                            _this.loading = false
                        }
                    },
                })
            },
            initPage(typedata){
                var _this = this;
                _this[typedata + 'CopyPage']=JSON.parse(JSON.stringify(_this[typedata + 'Page']))
                if(_this[typedata + 'CopyPage'].pages>=10){
                    _this[typedata + 'Page'].pages=[1,2,3,4,5,6,7,8,9,10]
               }else{
                    var numPage=[]
                    for(var i=1;i<=_this[typedata + 'CopyPage'].pages;i++){
                        numPage.push(i)
                    }
                    _this[typedata + 'Page'].pages=numPage
                
               }
            },
            plus(type, index) { 
                var _this = this;
                _this[type + 'PageNum'] = Number(index)
                this.pagesSize(type)
                if (type == 'teacher') {
                    this.getList(type, this.teacherFilterType, 'page')
                } else {
                    this.getList(type, this.gradeGroupType, 'page')
                }
            },
            pagesSize(type){
                var _this = this;
                if(_this[type + 'PageNum']<10){
                    if(_this[type + 'CopyPage'].pages>=10){
                        _this[type + 'Page'].pages=[1,2,3,4,5,6,7,8,9,10]
                    }else{
                        var numPage=[]
                        for(var i=1;i<=_this[type + 'CopyPage'].pages;i++){
                            numPage.push(i)
                        }
                        _this[type + 'Page'].pages=numPage
                    }
                }else if(_this[type + 'PageNum']<=_this[type + 'CopyPage'].pages){
                    if(_this[type + 'CopyPage'].pages-_this[type + 'PageNum']>=4){
                        var minPage=_this[type + 'PageNum']-5
                        var maxPage=_this[type + 'PageNum']+4
                    }else{
                        var minPage=_this[type + 'PageNum']-(9-((_this[type + 'CopyPage'].pages-_this[type + 'PageNum'])))
                        var maxPage=_this[type + 'PageNum']+(_this[type + 'CopyPage'].pages-_this[type + 'PageNum'])
                    }
                    var numPage=[]
                    for(var i=minPage;i<=maxPage;i++){
                        numPage.push(i)
                    }
                    _this[type + 'Page'].pages=numPage
                }
            },
            next(type, index){
                var _this = this;
                _this[type + 'PageNum'] = Number(index) + 1
                this.pagesSize(type)
                if (type == 'teacher') {
                    this.getList(type, this.teacherFilterType, 'page')
                } else {
                    this.getList(type, this.gradeGroupType, 'page')
                }
            },
            prev(type, index) {
                var _this = this;
                _this[type + 'PageNum'] = Number(index) - 1
                this.pagesSize(type)
                if (type == 'teacher') {
                    this.getList(type, this.teacherFilterType, 'page')
                } else {
                    this.getList(type, this.gradeGroupType, 'page')
                }
            },
            delList(list,type){
                this.delData=list    
                var _this=this
                $.ajax({
                    url: '<?php echo $this->createUrl("delete") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        type: type,
                        id:list._id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            _this.delStatus=data.data
                            if(data.data==1){
                                _this[type+'List'].forEach((item,index) => {
                                    if (item._id==list._id) {
                                        _this[type+'List'].splice(index, 1)
                                    } 
                                })
                                $('#delModal').modal('hide')
                            }else if(data.data==10){
                               $('#delModal').modal('show')
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg:'<?php echo Yii::t("newDS", "Cannot delete due to existence of parent comments.");?>'
                                })
                            }
                        } else {
                            resultTip({
                                error: 'warning',
                                msg:data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            journalOffline(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("offline") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:that.delData._id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.getList(that.delData.type)
                            $('#delModal').modal('hide')
                            resultTip({
                                msg:data.message
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg:data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg:data.message
                        });
                    },
                })
            },
            commentChild(data,type){
                for(var i=0;i<this.contentData.targets_info.length;i++){
                    if(this.contentData.targets_info[i].id==data){
                        return this.contentData.targets_info[i][type]
                    }
                }
            },
            viewContent(list,type){
                let that=this
                this.journalId=list._id
                this.contentData={}
                this.forwardType=''
                this.forwardTo=''
                this.forwardMark=''
                this.commentChildId=''
                this.isForward=false
                this.inputForward=[]
                this.commentChildList=[]
                $.ajax({
                    url: '<?php echo $this->createUrl("directMessage/view") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        journal_id: list._id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           that.contentData=data.data
                           that.commentChildId=''
                           that.commentData={}
                           that.borderShow=false
                           var content_cn = data.data.journalData.content;
                           var content_en = data.data.journalData.content_en;
                           $('#contentModal').modal('show')
                           that.$nextTick(() => {    
                               if(type=='teacher' && that.copyTeacherSearchText!='' && that.isSearch){
                                    var context = document.querySelector(".contentCn");
                                    var instance = new Mark(context);
                                    instance.mark(that.copyTeacherSearchText ,{separateWordSearch: false});

                                    var title = document.querySelector(".titleCn");
                                    var instance = new Mark(title);
                                    instance.mark(that.copyTeacherSearchText ,{separateWordSearch: false});
                               }
                               if(type=='leader' && that.copyLeaderSearchText!=''  && that.isSearch){
                                    var context = document.querySelector(".contentCn");
                                    var instance = new Mark(context);
                                    instance.mark(that.copyLeaderSearchText ,{separateWordSearch: false});
                                    var context = document.querySelector(".contentEn");
                                    var instance = new Mark(context);
                                    instance.mark(that.copyLeaderSearchText ,{separateWordSearch: false});

                                    var title = document.querySelector(".titleCn");
                                    var instance = new Mark(title);
                                    instance.mark(that.copyLeaderSearchText ,{separateWordSearch: false});
                                    var titleEn = document.querySelector(".titleEn");
                                    var instance = new Mark(titleEn);
                                    instance.mark(that.copyLeaderSearchText ,{separateWordSearch: false});
                               }
                           })
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
                $.ajax({
                    url: '<?php echo $this->createUrl("directMessage/commentChildIdlist") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        journal_id: list._id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.commentChildList=data.data
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            commentList(list){
                let that=this
                this.isForward=false
                this.inputForward=[]
                this.commentChildId=list
                this.commentLoading=true
                this.commentChildData=this.commentChildList[list]
                this.uploadImgList=[]
                this.uploadLinkList=[]
                this.loadingList=[]
                $.ajax({
                    url: '<?php echo $this->createUrl("item") ?>',
                    type: 'get',
                    dataType: 'json',
                    data: {
                        id: that.journalId,
                        child_id:list
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            data.data.items.forEach((list) => {
                                list.imgUrl=[]
                                list.pdfUrl=[]
                                if(list.attachments.length!=0){
                                    list.attachments.forEach((item) => {
                                        if(data.data.attachmentList[item]){
                                            let type=data.data.attachmentList[item].mimetype.split('/')[0]
                                            if(type=="image"){
                                            list.imgUrl.push(data.data.attachmentList[item])
                                            }else{
                                            list.pdfUrl.push(data.data.attachmentList[item])
                                            }
                                        }
                                    })
                                }
                            })
                            that.getQiniu()
                            that.commentData = data.data 
                            that.commentLoading=false
                            that.$nextTick(() => {
                                var container = that.$el.querySelector(".scroll-box");
                                container.scrollTop = container.scrollHeight;
                               if(that.$refs.content.offsetHeight<that.$refs.comment.offsetHeight){
                                    that.borderShow=true
                               }else{
                                    that.borderShow=false
                               }
                            })
                        }
                        else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.commentLoading=false
                        }
                    }
                });
            },
            showMore(list){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("directMessage/subscribers") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        journal_id:list.journal_id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.postObjectList=data.data
                            $('#postObjectModal').modal('show')
                        }
                    },
                    error: function(data) {
                        
                    },
                })
            },
            reply(typeData) {
                var _this = this;
                var _this = this;
                if(this.loadingList.length!=0){
                    resultTip({
                        error: 'warning',
                        msg: '<?php echo Yii::t("directMessage", "Uploading");?>'
                    });
                    return
                }
                var imgIds=[]
                var pdfIds=[]
                var ids=[]
                this.uploadImgList.forEach((item) => {
                    ids.push(item._id)
                    imgIds.push({
                        url:item.file_key,
                        id:item._id,
                        mimetype:item.mimetype,
                        title:item.title
                    })
                })
                this.uploadLinkList.forEach((item) => {
                    ids.push(item._id)
                    pdfIds.push({
                        url:item.file_key,
                        id:item._id,
                        mimetype:item.mimetype,
                        title:item.title,
                    })
                })
                if(typeData!='noReply'){
                    if(ids.length==0 && _this.content==''){
                        resultTip({
                            error: 'warning',
                            msg: '<?php echo Yii::t("directMessage", "Content cannot be empty.");?>'
                        });
                        return
                    }
                }
                this.loadingBtn = true
                $.ajax({
                    url: '<?php echo $this->createUrl("saveComment") ?>',
                    type: 'post',
                    dataType: 'json',
                    data: {
                        id: _this.journalId,
                        child_id: _this.commentChildId,
                        content: _this.commentContent,
                        mark_as_staff:typeData=='noReply'?1:0,
                        attachments:ids
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            _this.loadingBtn = false
                            if(typeData=='noReply'){
                                $('#noReplyModal').modal('hide')
                            }
                            _this.uploadImgList=[]
                             _this.uploadLinkList=[]
                            _this.commentContent=''
                            _this.commentList(_this.commentChildId)
                            resultTip({
                                msg: data.state
                            });
                        }
                        else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            _this.loadingBtn = false
                        }
                    }
                });
            },
            markNoReply(){
                $('#noReplyModal').modal()
            },
            oneQrcode(list,type){
                let that=this
                that.sharetUser=type
                $.ajax({
                    url: '<?php echo $this->createUrl("getQrCode") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:list._id,
                        type:'preview'
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.previewQrcode=data.data.preview
                            that.shareQrcode=data.data.share
                            $('#qrcodeModal').modal('show')
                        }
                    },
                    error: function(data) {
                        
                    },
                })
                $.ajax({
                    url: '<?php echo $this->createUrl("getOne") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id: list._id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           that.shareList=data.data
                           that.shareList.school_title=that.school_title
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            reviewedList(list){
                this.clientWidth=document.body.clientWidth
                let that=this
                that.targetsChildId=''
                that.reviewChilds={}
                that.reviewed=[]
                that.reviewTargets=[]
                that.unreadList=[]
                $.ajax({
                    url: '<?php echo $this->createUrl("getReviewed") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:list._id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            $('#echart').removeAttr('_echarts_instance_');
                            that.reviewChilds=data.data.childs
                            that.reviewed=[...new Set(data.data.reviewed.map(Number))]
                            for(var i=0;i<data.data.targets.length;i++){
                                if(that.reviewed.indexOf(data.data.targets[i])==-1){
                                    that.unreadList.push(data.data.targets[i])
                                }
                            }
                            that.reviewTargets=data.data.targets
                            let noRead=data.data.targets.length-data.data.reviewed.length
                            var data=[{value: data.data.reviewed.length, name: '<?php echo Yii::t("newDS", "Read");?>'},
                                {value: noRead, name: '<?php echo Yii::t("newDS", "Unread");?>'},]
                            $('#readModal').modal('show')
                            $('#readModal').on('shown.bs.modal', function (e) {
                                var myCharts = echarts.init(document.getElementById('echart'))
                                var option = that.optionData(myCharts,data)
                                myCharts.setOption(option,true);
                                myCharts.resize()
                            })
                        }
                    },
                    error: function(data) {
                        
                    },
                })
            },
            childInfo(list){
                this.readLoading=true
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getParents") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        child_id:list,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.targetsParent=data.data.parentData
                            that.wxReceivers=data.data.wxReceivers
                            that.targetsChildId=list
                            that.readLoading=false
                        }
                    },
                    error: function(data) {
                        
                    },
                })
            },
            optionData(myCharts, destData){
                var option = {
                    tooltip: {
                        trigger: 'item',
                        formatter: '{a} <br/>{b} : {c} ({d}%)'
                    },
                    legend: {
                        // orient: 'vertical',
                        bottom: 10,
                        left: 'center',
                    },
                    color:['#F8C42DFF','#5B8FF9FF'],
                    series: [
                        {
                            name: '<?php echo Yii::t("newDS", "Read Report");?>',
                            type: 'pie',
                            radius: '55%',
                            center: ['50%', '45%'],
                            data:destData,
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            }
                        }
                    ]
                };
                return option
            },
            leaderRead(list){
                this.readData=list
                this.parentReviewed=[]
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getReviewed") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:list._id,
                    },
                    success: function(data) {
                        that.reviewedChild=data.data.reviewedChild
                        that.parentReviewed=data.data.reviewed
                        $('#parentReplyModal').modal('show')
                    }
                })
            },
            readChildid(id){
                if(this.parentReviewed.indexOf(id)!=-1){
                    return true
                }
            },
            chooseTeacher(){
                var resignList=[]
                var normalList=[]
                this.teacheruserList.forEach(item => { 
                    if (this.teacherIds.indexOf(parseInt(item.uid))!=-1){
                        if(item.level=='0'){
                            resignList.push(item)
                        }else{
                            normalList.push(item)
                        }
                    }
                })
                this.normalList=this.sortTea(normalList)
                this.resignList=this.sortTea(resignList)
                $("#teacherList").modal('show');
            },
            sortTea(list){
                list.sort((x,y)=>{
                    return x['name'].localeCompare(y['name'])
                })
                return list
            },
            getUrl(uid) {
                return '<?php echo $this->createUrl('edit'); ?>' + "&staff=" + uid;
            },
            forwardEvent(){
                this.isForward=true
            },
            forwardCancel(){
                this.isForward=false
                this.inputForward=[]
            },
            forwardTeacher(){
                if(this.inputForward.length==0){
                    resultTip({
                        error: 'warning',
                        msg:'<?php echo Yii::t("global", "Please Select");?><?php echo Yii::t("directMessage", "Handover Content");?>'
                    });
                    return
                }
                this.inputForward=this.inputForward.sort()
                this.forwardType=''
                this.forwardTo=''
                this.forwardToName=''
                this.forwardMark=''
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("directMessage/childTeacherList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        childId:this.commentChildId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.teacherListForward = Object.values(data.data) ;
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.loading = false;
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.loading = false;
                    },
                })
                $.ajax({
                    url: '<?php echo $this->createUrl("directMessage/childDeptList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {},
                    success: function(data) {
                        if (data.state == 'success') {
                            that.deptList = Object.values(data.data) ;
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.loading = false;
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.loading = false;
                    },
                })
                $('#forwardModal').modal('show')
            },
            repost(){
                var comment_id_list=[]
                this.inputForward.forEach(item => {
                    comment_id_list.push(this.commentData.items[item].id)
                });
                let that=this
                if(this.forwardType==''){
                    resultTip({
                        error: 'warning',
                        msg:'<?php echo Yii::t("global", "Please Select");?>移交类型'
                    });
                    return
                }
                if(this.forwardTo==''){
                    resultTip({
                        error: 'warning',
                        msg:'<?php echo Yii::t("global", "Please Select");?>移交人或者部门'
                    });
                    return
                }
                var forwardTo=''
                if(this.forwardType==3){
                    forwardTo=this.forwardTo.uid
                }else{
                    forwardTo=this.forwardTo.id
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("directMessage/repost") ?>',
                    type: 'post',
                    dataType: 'json',
                    data:{
                        comment_id_list:comment_id_list ,
                        repost_type: this.forwardType, 
                        repost_to: forwardTo,
                        repost_mark:this.forwardMark
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            $('#forwardModal').modal('hide')
                            that.initLoading = false;
                            that.commentList(that.commentChildId)
                            resultTip({
                                msg: data.message
                            });
                        }
                        else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        that.classLoading=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                }); 
            },
            showOriginalInfo(id){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("directMessage/repostDetail") ?>',
                    type: 'post',
                    dataType: 'json',
                    data:{
                        commentId:id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            $('#originalInfoModal').modal('show')
                          that.repostDetailList=data.data
                        }
                        else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        that.classLoading=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                }); 
            },
            mousedown(item,type) {
                this[type]=item.name
                this.forwardTo=item
                this.show=false
            },
            getQiniu(){
                let that=this
                if(that.token!=''){
                    return
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/getCommentQiniuToken") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        linkId: that.journalId,
                        linkType:'journal'
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.token=data.data.data
                            that.uploadShow=true
                            that.$nextTick(()=>{
                                var btn=[{id:'pickfilesPhoto',type:'photo'},{id:'pickfilesPDF',type:'pdf'}]
                                
                                for(var i in btn){
                                    config['token'] = data.data.data;
                                    config['browse_button'] =btn[i].id;
                                    config['filters']={
                                        mime_types:btn[i].type=='photo'?[{title : "<?php echo Yii::t('teaching', 'Image files');?>", extensions : "jpg,gif,png,jpeg"}]:[ {title : "<?php echo Yii::t('teaching', 'pdf files');?>", extensions : "pdf,doc,xls,zip,xlsx,docx"}]
                                    };
                                    var uploader = new plupload.Uploader(config);
                                    uploader.init();
                                }
                            })
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            delImg(type,list,index){
                var that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/deleteAttachment") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        attachmentId:list._id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            if(type=='img'){
                                that.uploadImgList.forEach((item,index) => {
                                    if (item._id==list._id) {
                                        that.uploadImgList.splice(index, 1)
                                    } 
                                })
                            }else{
                                that.uploadLinkList.forEach((item,index) => {
                                    if (item._id==list._id) {
                                        that.uploadLinkList.splice(index, 1)
                                    } 
                                })
                            }
                            resultTip({
                                msg:data.state
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            saveFile(list,index) {
                var that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/updateAttachmentName") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        attachmentId:list._id,
                        attachmentName:this.attachmentName

                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.uploadLinkList.forEach((item,index) => {
                                if (item._id==list._id) {
                                    Vue.set(that.uploadLinkList[index], 'title',data.data.title);
                                    Vue.set(that.uploadLinkList[index], 'file_key', data.data.url);
                                    Vue.set(that.uploadLinkList[index], 'isEdit', false);
                                } 
                            })
                            resultTip({
                                msg:'<?php echo Yii::t("newDS", "Data Saved!");?>'
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            
            showImg(list){
                var id=list.id 
                var viewer = new Viewer(document.getElementById(id),{
                    fullscreen: false,
                    title:false,
                    show:function(){ 
                        $('.nextImg').show()
                        if(list.imgUrl.length==1){
                            $('.viewer-prev').hide()
                            $('.viewer-next').hide()
                        }
                    },
                    hide:function(){
                        $('.nextImg').hide() 
                        viewer.destroy()
                    }
                });
                if(document.getElementById('next')){
                    document.getElementById('next').onclick = function() {
                        viewer.next();
                    }
                }
                if(document.getElementById('prev')){
                    document.getElementById('prev').onclick = function() {
                        viewer.prev();
                    }
                }
                $("#"+id).click();
            },
        }
    })
    var config = {
        runtimes: 'html5,flash,html4', //上传模式,依次退化
        container: 'container', //上传区域DOM ID，默认是browser_button的父元素，
        browse_button: 'pickfilesPhoto', //上传选择的点选按钮，**必需**
        token: '',
        flash_swf_url: '/js/plupload/Moxie.swf', // flash的相对地址
        auto_start: true, //选择文件后自动上传，若关闭需要自己绑定事件触发上传
        // multi_selection: false, //不允许多选
        url:'https://upload-z1.qiniup.com',

        init: {
            'FilesAdded': function(up, files) {
                container.disabledUpload=true
                let fileType=files[0].type.split('/')
                if(fileType[0]=="image"){
                  container.loadingType=1
                }else{
                  container.loadingType=2
                }
                container.loadingList=files
                up.start();
            },
            BeforeUpload: function(up, file) {
                //设置参数
                up.setOption({
                    multipart_params:{token:container.token}
                })
                let fileType=file.type.split('/')
                if(fileType[0]=="image"){
                    container.uploadImgList.push({types:'1'})
                }else{
                    container.uploadLinkList.push({title: '<?php echo Yii::t("directMessage", "Uploading");?>'})  
                }
                container.loadingList.splice(0,1)
            },
            UploadProgress: function(up, file) {
                $('#progress').css('width', file.percent+'%').html(file.percent+'%');
            },
            'FileUploaded': function(up, file, info) {
                var response = eval('('+info.response+')');
                if(info.status == 200){
                    let fileType=response.data.mimetype.split('/')
                    if(fileType[0]=="image"){
                        container.uploadImgList.splice(container.uploadImgList.length-1,1)
                        container.uploadImgList.push(response.data)
                    }else{
                        response.data.isEdit=false
                        container.uploadLinkList.splice(container.uploadLinkList.length-1,1)
                        container.uploadLinkList.push(response.data)
                    }
                }
            },
            'Error': function(up, err, errTip) {
                if(err.response){
                    var response = eval('('+err.response+')');
                }
                else{
                    var response = {message: err.message};
                }
                if(response.error == 'token not specified'){
                    up.stop();
                    $.ajax({
                        url: '<?php echo $this->createUrl("journals/getCommentQiniuToken") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            journalId: container.journal_id
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                container.token=data.data.data
                                config['uptoken'] = data.data.data;
                                up.setOption("multipart_params", {"token":data.data.token,})
                                up.start();
                            } else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.msg
                                });
                            }
                        },
                        error: function(data) {

                        },
                    })
                }
            },
            'UploadComplete': function(up, file) {
                //队列文件处理完毕后,处理相关的事情
            }
        }
    };
</script>