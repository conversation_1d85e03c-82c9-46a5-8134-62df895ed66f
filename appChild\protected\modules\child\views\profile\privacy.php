<?php
$this->breadcrumbs=array(
		Yii::t("navigations","Profile")=>array('/child/profile'),
		Yii::t("navigations","Privacy Settings"),
);?>

<h1><label><?php echo Yii::t("navigations","Privacy Settings") ?></label></h1>
<p class="desc"><?php //echo Yii::t("global","Please check your contact information below that you would like other parents to see.");?></p>

<?php
	foreach(Yii::app()->user->getFlashes() as $key => $message) {
        echo '<div class="flash-' . $key . '">' . $message . "</div>\n";
    }
?>

<div class="form privacy_bg">
<?php 

$colorbox = $this->widget('application.extensions.colorbox.JColorBox');
$colorbox->addInstance('.colorbox', array('iframe'=>false, 'width'=>'725', 'height'=>'80%'));

$form=$this->beginWidget('CActiveForm', array(
	'id'=>'privacy-form',
)); ?>
	
	<?php echo CHtml::errorSummary($model); ?>

	
	<dl class="row">
		<dt>
			<label><?php echo Yii::t("userinfo","Family Directory");?></label>
		</dt>
		<dd>
			<p class="desc">
			<?php echo addColon(Yii::t("userinfo", "Please put a mark in the box for item(s) to be published in internally distributed :fd",
				array(
					":fd"=>CHtml::link(Yii::t("userinfo","Family Directory"), $this->createUrl("//portfolio/portfolio/contact", array('classid'=>$currentClassId)), array("class"=>"colorbox"))
				)),
				array("target"=>"_blank")
			); 
                        if(!empty($classIdList)){
                            
                            foreach ($classIdList as $classid){
                                echo CHtml::link(Yii::t("userinfo","Family Directory"), $this->createUrl("//portfolio/portfolio/contact", array('classid'=>$classid)), array("class"=>"colorbox",'id'=>'contact_class_'.$classid,'style'=>'display:none;'));
                            }
                        }
                        
                        ?>
			</p>
			<div class="inline">
				<?php echo CHtml::activeCheckBox($model,'mother_mobile'); ?>
				<?php echo CHtml::activeLabelEx($model,'mother_mobile') ?>
			</div>
				
			<div class="inline">
				<?php echo CHtml::activeCheckBox($model,'father_mobile'); ?>
				<?php echo CHtml::activeLabelEx($model,'father_mobile') ?>
			</div>
		
			<div class="inline">
				<?php echo CHtml::activeCheckBox($model,'mother_email'); ?>
				<?php echo CHtml::activeLabelEx($model,'mother_email') ?>
			</div>
				
			<div class="inline">
				<?php echo CHtml::activeCheckBox($model,'father_email'); ?>
				<?php echo CHtml::activeLabelEx($model,'father_email') ?>
			</div>
		
			<div class="inline">
				<?php echo CHtml::activeCheckBox($model,'home_phone'); ?>
				<?php echo CHtml::activeLabelEx($model,'home_phone') ?>
			</div>
		</dd>			
	</dl>	
			
	<dl class="row submit">
		<dt></dt>
		<dd><?php
				if( Yii::app()->user->checkAccess('editProfile',array("childid"=>$this->getChildId())) )
					echo CHtml::submitButton(Yii::t("global", "Save"), array("class"=>"w100 bigger"));
				else
					echo CHtml::submitButton(Yii::t("global", "Save"), array("class"=>"w100 bigger","disabled"=>"disabled"));
			?>
		</dd>
	</dl>

	
	
<?php $this->endWidget(); ?>
</div><!-- form -->
<script type="text/javascript">
    function changeContactClass(_this){
        $('#contact_class_'+$(_this).val()).click();
    }
</script>