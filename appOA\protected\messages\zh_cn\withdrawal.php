<?php
return array(
  'Search by student name' => '搜索学生姓名',
  'Search' => '搜索',
  'Add Student' => '添加学生',
  'Pending Tasks' => '待我处理',
  'Revoke Applications' => '终止流程',
  'Pending' => '待终止',
  'Ended' => '已终止',
  'Withdrawal Complete' => '退学完成',
  'Export File/Table' => '导出表格',
  'All' => '全部',
  'Withdrawal Number' => '退学编号',
  'Mid-term Withdrawal' => '中途退学',
  'Withdrawal' => '中途退学',
  'Not Returning' => '不返校',
  'Withdrawal Date' => '退学日期',
  'Processing Status' => '处理状态',
  'Completed' => '已完成',
  'Action' => '操作',
  'Type' => '类型',
  'Details' => '详情',
  'New Withdrawal Request(s)' => '新的退学申请',
  'Fully Reimbursed' => '已全部归还',
  'Compensation Required' => '需要赔偿',
  'School Fees' => '学费',
  'School Lunch Fee' => '餐费',
  'School Bus Fee' => '校车费',
  'Basic Information' => '基本信息',
  'Send WeChat request form' => '推送微信申请单',
  'Form submission' => '填写申请单',
  'Awaiting parent submission' => '等待家长填写',
  'Submission on behalf of parent' => '代填写',
  'Skip interview process' => '跳过退学流程中的面谈步骤',
  'Applicant' => '申请人',
  'Where are you transferring to?' => '您要转到哪里？',
  'Reason for withdrawal' => '退学原因',
  'Are there any aspects of school you are unsatisfied with?' => '对学校最满意的地方？',
  'Do you have any advice for us?' => '对学校有什么建议？',
  'Who do you wish to contact about the withdrawal process?' => '你希望退学事宜联系谁？',
  'Comment' => '备注',
  'Interviewer' => '面谈人',
  'Interview result' => '面谈结果',
  'Schedule school board interview' => '邀请校董面谈',
  'End withdrawal, continue study' => '终止退学，继续就读',
  'Continue withdrawal' => '继续退学',
  'Account balance' => '账户余额',
  'Positive value refund to parent' => '正数为应退家长',
  'Negative value deduction from parent' => '负数为家长应支付校园',
  'Refund' => '退费',
  'Admissions confirmation' => '招生确认',
  'Final student status' => '最终学生状态',
  'Final withdrawal date' => '最终退学日期',
  'Ultimate withdrawal reason' => '最终退学原因',
  'Management Interview(s)' => '管理组面谈',
  'Admissions Interview(s)' => '招生组面谈',
  'Xueji' => '学籍',
  'Library Returns' => '校图书馆借阅',
  'Class Material Returns' => '班级图书借阅',
  'Laptop' => '电脑',
  'Medical Insurance' => '医疗保险',
  'School Fees & Account' => '学费&个人账户',
  'Account Settlement' => '设备及费用清算',
  'Finance Confirmation' => '财务组确认',
  'Serial Number' => '序列号',
  'Send for Parent Confirmation' => '推送给家长确认',
  'Send to' => '推送人',
  'No Records' => '无推送记录',
  'Content' => '推送内容',
  'Send' => '推送',
  'Sent' => '推送',
  'Parent Confirmation' => '家长确认',
  'Pending Confirmation' => '等待家长确认',
  'Confirmed' => '确认文件',
  'Mark this step as completed' => '标记此步已完成',
  'All Approved' => '已全部通过',
  'Cancel Approval' => '取消通过',
  'Skip Interview' => '跳过面谈',
  'Insured through Daystar, transferred out.'=>'通过学校参保，已转出',
  'Insured through Daystar, not transferred out yet.'=>'通过学校参保，暂未转出',
  'Not insured through Daystar.'=>'未通过学校参保',
  'In withdraw process.' => '退学流程处理中',
  'Edit' => '信息编辑',
  'Task Owners Settings' => '操作人员管理',
  'Semester' => '本期',
  'Today' => '本日',
  'Month' => '本月',
  'Year' => '本年',
  'D' => '本日',
  'M' => '本月',
  'S' => '本期',
  'Y' => '本年',
  'withdrawal refund status' => '退学退费状态',
  'Amount' => '金额',
);
