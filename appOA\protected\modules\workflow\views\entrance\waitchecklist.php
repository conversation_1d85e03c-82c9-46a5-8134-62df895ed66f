<?php
    $paymentIdList = array();
    $contractIdList = array();
    foreach ($currentCheckUser as $item) {
        $nodes = explode(',', $nodesList[$item->defination_id]['obj']->node_order);
        
        $handle = $nodesList[$item->defination_id]['obj']->defination_handle;
        if (in_array(strtolower($handle), array('payment', 'paymenthq'))) {
            $objectId = $item->operation->operation_object_id;
            $paymentIdList[] = $objectId;
        }
        if (in_array(strtolower($handle), array('contract', 'contracthq'))) {
            $objectId = $item->operation->operation_object_id;
            $contractIdList[] = $objectId;
        }
    }
    $paymentList = array();
    $contractList = array();
    if ($paymentIdList) {
        Yii::import('common.models.workflow.payment.PaymentApply');
        $criteria = new CDbCriteria;
        $criteria->compare("id", $paymentIdList);
        $criteria->compare("budget", 1);
        $criteria->index = 'id';
        $paymentList = PaymentApply::model()->findAll($criteria);
    }
    if ($contractIdList) {
        Yii::import('common.models.workflow.contract.ContractApply');
        $criteria = new CDbCriteria;
        $criteria->compare("id", $contractIdList);
        $criteria->compare("budget", 1);
        $criteria->index = 'id';
        $contractList = ContractApply::model()->findAll($criteria);
    }
?>
<div class="panel panel-info">
    <div class="panel-heading" data-branchfilter="dailyBulletin"><span class="glyphicon glyphicon-bullhorn"></span>【<?php echo count($currentCheckUser) . '】' . Yii::t('workflow','Workflows Requiring My Approval'); ?></div>
    <div class="panel-body table-responsive" id="wait-check-list">
        <?php if ($currentCheckUser):?>
            <?php foreach ($currentCheckUser as $user): $objectId = $user->operation->operation_object_id;?>
                <?php 
                    $nodes = explode(',', $nodesList[$user->defination_id]['obj']->node_order);
                    $noNeedNodes =  explode(',', $user->operation->exte4);
                ?>
                <div class="task-data" id="operation_id_<?php echo $user->operation_id;?>">
                    <?php if ($user->operation->state == WorkflowOperation::WORKFLOW_STATS_STEP && $user->operation->current_node_index == current($nodes)):?>
                    <a type="button" class="close J_ajax_del" aria-label="Close" style="color:red;font-size:28px;" href="
                            <?php
                                echo $this->createUrl('//workflow/entrance/index', array('definationId' => $user->operation->defination_id,
                                    'nodeId' => $user->operation->current_node_index,
                                    'operationId' => $user->operation_id,
                                    'branchId' => $user->operation->branchid,
                                    'action' => 'delete',
                                ));
                                ?>"><span aria-hidden="true">&times;</span>
                    </a>
                    <?php endif;?>
                    <ul class="task-info" style="width: 610px;padding-left:2px;">
                        <li>
                            <?php echo $nodesList[$user->defination_id]['name']; ?>
                            <?php 
                                $config = WorkflowConfig::getType($user->operation->operation_type,$user->operation->operation_object_id);
                                if ($config['href']){
                                    echo CHtml::link($config['function'],$config['href'],array('target'=>'_blank'));
                                }else{
                                    echo $config['function'];
                                }
                            ?>
                        </li>
                        <li>
                            <?php echo Yii::t('workflow', 'School:') .' '. $branchList[$user->operation->branchid];?>
                        </li>
                        <li>
                            <?php echo Yii::t('workflow', 'Requester:') .' '. $usersList[$user->operation->start_user];?>
                        </li>
                        <li>
                            <?php echo Yii::t('workflow', 'Request Time:') .' '. OA::formatDateTime($user->operation->start_time,'medium','short');?>
                        </li>
                    </ul>
                    <div class="clearfix"></div>
                    <ul class="nav nav-wizard" style='padding-bottom: 20px;'>
                        <?php
                        $flag = false;
                        foreach ($nodes as $node):
                            if (in_array($node, $noNeedNodes)) {
                                continue;
                            }
                            if ($flag) {
                                break;
                            }
                            $nodeObj = $nodesList[$user->defination_id]['obj']['workflowNode'][$node];
                            $handle = strtolower($nodesList[$user->defination_id]['obj']->defination_handle);
                            if(in_array($handle, array('payment', 'paymenthq')) && $nodeObj->field > 0 && isset($paymentList[$objectId]) && $nodeObj->field >= $paymentList[$objectId]->price_total / 100)
                            { $flag = true;}
                            if(in_array($handle, array('contract', 'contracthq')) && $nodeObj->field > 0 && isset($contractList[$objectId]) && $nodeObj->field >= $contractList[$objectId]->price_total / 100)
                            { $flag = true;}
                            ?>
                            <li <?php if ($node == $user->operation->current_node_index): ?> class="active" <?php endif; ?>>
                                <a href="<?php
                                echo $this->createUrl('//workflow/entrance/index', array('definationId' => $user->operation->defination_id,
                                    'nodeId' => $node,
                                    'operationId' => $user->operation_id,
                                    'branchId' => $user->operation->branchid,
                                    'action' => 'show',
                                ));
                                ?>" class="J_ajax" data-method="get">
                                    <?php echo $nodesList[$user->defination_id]['nodes'][$node]['name']; ?>
                                </a>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                    <div class="clearfix"></div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
</div>