<?php
$crit = new CDbCriteria();
$crit->order = 't.startyear DESC';
$returnModels = Calendar::model()->with(array('campuses'))->findAll($crit);

$calendarData = array();
$calendarBigMap = array();
$activeYears = array();
foreach($returnModels as $_calendar){
    $timePoints = explode(',', $_calendar->timepoints);
    $calendarBigMap[$_calendar->startyear][] = $_calendar->yid;
    $calendarData[$_calendar->yid] = array(
        'yid' => $_calendar->yid,
        'title' => $_calendar->title,
        'startyear' => $_calendar->startyear,
        'firstSemester' => array(date('Y-m-d',$timePoints[0]),date('Y-m-d',$timePoints[1])),
        'secondSemester' => array(date('Y-m-d',$timePoints[2]),date('Y-m-d',$timePoints[3])),
        'isNew' => false,
        'isStatus' => $_calendar->stat,
    );
    foreach($_calendar->campuses as $_campus){
        $calendarData[$_calendar->yid]['campuses'][] = array(
            'branchid' => $_campus->branchid,
            'selected' => intval($_campus->is_selected),
        );
    }
}

$calendarList = array();
foreach ($calendarBigMap as $val){
    foreach ($val as $item) {
        $calendarList[$item] = $item;
    }
}

$crit = new CDbCriteria();
$crit->compare('yid',$calendarList);
$schoolDaysModel = CalendarSchoolDays::model()->findAll($crit);
$schoolDayList = array();
$schoolyear = array();
if($schoolDaysModel){
    foreach ($schoolDaysModel as $val){
        $schoolDayList[$val->yid]['month'][$val->month] = $val->schoolday;
        $firstSemester = date("Y-m", strtotime($calendarData[$val->yid]['firstSemester'][1]));
        $secondSemester = date("Y-m", strtotime($calendarData[$val->yid]['secondSemester'][0]));
        if($firstSemester == $secondSemester && $firstSemester == $val->month_label){
            $autumn = 0;
            $spring = 0;
            foreach (explode(',', $val->schoolday_array) as $item){
                $dataDay = $val->month_label . '-' . $item;
                if($dataDay <= $calendarData[$val->yid]['firstSemester'][1]){
                    $autumn++;
                }else{
                    $spring++;
                }
            }
            $schoolDayList[$val->yid]['semester'][10] += $autumn;
            $schoolDayList[$val->yid]['semester'][20] += $spring;
        }else{
            $schoolDayList[$val->yid]['semester'][$val->semester_flag] += $val->schoolday;
        }
        $schoolDayList[$val->yid]['year'] += $val->schoolday;
    }
}

//新建校历的初始值
$calendarData[0] = array(
    'title' => '',
    'startyear' => 0,
    'firstSemester' => array('',''),
    'secondSemester' => array('',''),
    'isNew' => true,
    'isStatus' => 20,
);

$branches = Branch::model()->getBranchList();
$clabels = Calendar::attributeLabels();
$activeYears['min'] = empty($calendarBigMap) ? date('Y')-5 : min(array_keys($calendarBigMap));
$activeYears['max'] = empty($calendarBigMap) ? date('Y')+5 : max(array_keys($calendarBigMap)) + 2;
for($i=$activeYears['max'];$i>=$activeYears['min'];$i--){
    $optionYears[$i] = sprintf('%d-%d',$i,$i+1);
}

$crit = new CDbCriteria();
$crit->compare('category_sign', array('calendar_day_10','calendar_day_20','calendar_day_30'));
$crit->index = 'category_sign';
$terms = DiglossiaCategory::model()->with('terms')->findAll($crit);

$termsData = array();
foreach($terms as $key=>$_termCat){
    foreach($_termCat->terms as $_term){
        $termsData[$key][] = array(
            'entitle' => $_term->entitle,
            'cntitle' => $_term->cntitle
        );
    }
}

$newCalendarDay = new CalendarDay();
$newCalendarData = $newCalendarDay->getAttributes();
$newCalendarData['datestr'] = '';

$cdLabels = CalendarDay::attributeLabels();
?>

<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','HQ Operations'), array('/moperation'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Basic Configurations'), array('/moperation'))?></li>
        <li class="active"><?php echo Yii::t('site','School Calendar Template');?></li>
    </ol>

    <div class="row">
        <div class="col-md-1 col-sm-2">
            <button type="button" class="btn btn-primary mb10" onclick="newCalendarTemplate()"><span class="glyphicon glyphicon-plus"></span> 新建校历模版</button>
            <ul class="nav nav-pills nav-stacked background-gray" id="calendar-years-list">
                <?php
                foreach($calendarBigMap as $_year => $_v){
                    echo CHtml::openTag('li');
                    echo CHtml::link(sprintf('%s - %s', $_year, $_year + 1), 'javascript:void(0);', array('startyear'=>$_year,'displayItems'=>''));
                    echo CHtml::closeTag('li');
                }
                ?>
            </ul>
        </div>
        <div class="col-md-2 col-sm-3">
            <div id="calendar-campus-list">
                <!--place holder-->
            </div>
            <div class="well campus-total" style="display: none;">
                共分配校历到 <span class="text-primary"></span> 家校园
            </div>
        </div>

        <div class="col-md-9 col-sm-7" id="calendar-detail-box" style="display: none;">
            <div class="row">
                <div class="col-md-12">
                    <div class="panel panel-default">
                        <!-- Default panel contents -->
                        <div class="panel-heading calendar-basic-edit">
                            <span></span>
                        </div>
                        <div class="panel-body" id="calendar-edit">
                            <form method="post" action="<?php echo $this->createUrl('//moperation/calendar/saveCalendar');?>" class="form-horizontal J_ajaxForm" role="form">
                                <div id="calendar-edit-basic">
                                </div>
                                <div id='schoolnum'></div>
                                <div class="form-group">
                                    <div class="col-sm-2"></div>
                                    <div class="col-sm-10">
                                        <?php echo CHtml::hiddenField('yid'); ?>
                                        <button type="button" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Save');?></button>
                                        <button type="button" class="btn btn-default" onclick="editCalendarDetail()">
                                            <?php echo Yii::t('site','Edit Calendar');?></button>
                                    </div>
                                </div>

                            </form>

                            <div id="calendar-detail">

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>


<?php $this->renderPartial('moperation.views.calendar.templates.backboneTemplate',array('clabels'=>$clabels,'optionYears'=>$optionYears,'cdLabels'=>$cdLabels));?>

<!-- Calendar Template Modal -->
<div class="modal fade" id="newCalendarModal" tabindex="-1" role="dialog" aria-labelledby="newCalendarModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel"><?php echo Yii::t('site','School Calendar Template');?> <small></small></h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="well">
                            说明
                        </div>
                        <form class="form-horizontal J_ajaxForm" action="<?php echo $this->createUrl('//moperation/calendar/saveCalendar');?>" method="POST">
                            <div id="form-data">
                                <!--place holder-->
                            </div>

                            <div class="pop_bottom">
                                <button onclick="$('#newCalendarModal').modal('hide')" type="button" class="btn btn-default pull-right"><?php echo Yii::t('global','Cancel');?></button>
                                <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global','Submit');?></button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="CalendarSaveASModal" tabindex="-1" role="dialog" aria-labelledby="CalendarSaveASLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel">校历模版另存为 <small></small></h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="well">
                            说明
                        </div>
                        <form class="form-horizontal J_ajaxForm" action="<?php echo $this->createUrl('templateSaveAs');?>" method="POST">
                            <div class="form-group">
                                <div class="col-sm-offset-2 col-sm-10">
                                    <span id="startyear"></span>
                                    <input type="hidden" name="fromyid" id="fromyid">
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="newCalendarName" class="col-sm-2 control-label">校历名称</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" id="newCalendarName" placeholder="校历名称" name="title">
                                </div>
                            </div>
                            <div class="form-group" model-attribute="title">
                                <?php echo CHtml::label($clabels['stat'], CHtml::getIdByName('stat'), array('class'=>'col-sm-2 control-label'));?>
                                <div class="col-sm-10 form-inline">
                                    <div class="checkbox">
                                        <label>
                                           <input type="checkbox" value="1" name='stat' id='stat' checked='checked'>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="pop_bottom">
                                <button onclick="$('#CalendarSaveASModal').modal('hide')" type="button" class="btn btn-default pull-right"><?php echo Yii::t('global','Cancel');?></button>
                                <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global','Submit');?></button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<div id="dropdown-calendar-template" style="display: none;">
    <ul class="dropdown-menu">
        <li><a href="#" onclick="editCalendarTemplate(this)"><span class="glyphicon glyphicon-pencil"></span> <?php echo Yii::t('asa','Edit');?></a></li>
        <li><a href="#" onclick="saveCalendarTemplateAs(this)"><span class="glyphicon glyphicon-floppy-disk"></span> 另存为</a></li>
        <li class="divider"></li>
        <li delete-action><a href="#" onclick="deleteCalendarTemplate(this)"><span class="glyphicon glyphicon-remove"></span> <?php echo Yii::t('asa','Delete');?></a></li>
    </ul>
</div>

<div id="dropdown-item-template" style="display: none;">
    <ul class="dropdown-menu">
        <li><a href="javascript:;" onclick="gotoSchoolCalendar(this)"><span class="glyphicon glyphicon-search"></span> 查看校园自定义校历</a></li>
        <li><a href="javascript:;" onclick="setCurrentCalendar(this)"><span class="glyphicon glyphicon-pushpin"></span> 设当前年为默认学年</a></li>
        <li class="divider"></li>
        <li><a href="javascript:;" onclick="unassignCalendar(this)"><span class="glyphicon glyphicon-remove"></span> 从本校历模版中移除</a></li>
    </ul>
</div>

<script>
    var calendarBigMap = <?php echo CJSON::encode($calendarBigMap);?>;
    var calendarData = <?php echo CJSON::encode($calendarData);?>;
    var branchData = <?php echo CJSON::encode($branches);?>;
    var calendarTemplate = _.template($('#calendar-item-template').html());
    var campusYesTemplate = _.template($('#calendar-campusYes-template').html());
    var campusNoTemplate = _.template($('#calendar-campusNo-template').html());
    var basicEditCalendarTemplate = _.template($('#calendar-basicEdit-template').html());
    var schoolday = _.template($('#schooldays').html());
    var schoolDayList = <?php echo CJSON::encode($schoolDayList);?>;
    var thisSchoolDayList;
    var totalCount;
    var calendarId;//当前学校id
    var currentCalendarId;
    var currentStartYear;

    var newCalendarTemplate; //新建校历模版，弹窗
    var editCalendarTemplate; //编辑校历模版，非弹窗
    var saveCalendarTemplateAs; //校历模版另存为
    var deleteCalendarTemplate; //删除校历模版
    var displayCalendarEditForm; //显示校历模版基本信息
    var editCalendarDetail; //编辑日历详细信息
    var enableEdit; //默认是readonly
    var dateSelect; //点击日历某天的编辑框
    var beforeShowDay;
    var startYearChange; //开始年下拉框onChange事件
    var renderCalenderCampusList; //显示校历及所分配的校园信息
    var closeEditingDayWindow; //关闭编辑校历日界面

    var calendarDayData = {};
    var displayCalendarDays;

    var calendarDayDisplayTemplate = _.template($('#days-item-template').html()); //日历天显示模版
    var calendarDayEditTemplate = _.template($('#day-item-edit-template').html()); //日历天编辑模版
    var setCalendarDate;
    var editingDays=[];

    var globalDefaultDays = <?php echo CJSON::encode($termsData);?>; //系统定义的假期、时间、工作日调休等

    var changeEventType; //选择日历日类型时自动弹出预定义的假期、事件类型
    var setDefaultEvent;

    var unassignedCampusList; //未分配校历模版的校园ID数组
    var showUnassignedCampus; //显示未分配校历模版的校园列表
    var historydays={}//修改后存入数据对象
    $(function(){

        $.datepicker.setDefaults($.datepicker.regional['<?php echo (Yii::app()->language == 'en_us')? '': 'zh-CN'?>']);

        renderCalenderCampusList = function(startYear){
            unassignedCampusList = _.keys(branchData);
            $('#calendar-campus-list').empty();
            $.each(calendarBigMap[startYear],function(_m, _yid){
                var _campus_list = '';
                if(!_.isUndefined(calendarData[_yid]) && !_.isUndefined(calendarData[_yid]['campuses'])){
                    $.each(calendarData[_yid]['campuses'],function(_n, _campus){
                        if(!_.isUndefined(_campus)){
                            unassignedCampusList = _.without(unassignedCampusList, _campus.branchid);
                            if(_campus.selected==1){
                                var _view = campusYesTemplate(_campus);
                            }else{
                                var _view = campusNoTemplate(_campus);
                            }
                            _campus_list += _view;
                            totalCount++;
                        }
                    });
                }
                var _calendar = calendarTemplate({yid:_yid, campuses:_campus_list});
                $('#calendar-campus-list').append(_calendar);

            });

            if(unassignedCampusList.length == 0){
                $('#calendar-campus-list .assign-campus').remove();
            }

            $('.campus-total span').html(totalCount);
            $('.campus-total').show();

            //初始化模版下拉菜单
            $('#calendar-campus-list a[calendar-dropdown]').click(function(){
                if(_.isUndefined($(this).attr('dropdown-ini'))){
                    $(this).after($('#dropdown-calendar-template').html());
                    $(this).attr('dropdown-ini',1);
                    if($(this).parents('.panel-heading').siblings('.panel-body').children('span').length > 0){
                        $(this).siblings('ul.dropdown-menu').children('li[delete-action]').addClass('disabled');
                    }
                }
            });

            //初始化校园校历下拉菜单
            $('#calendar-campus-list a[campus-dropdown]').click(function(){
                if(_.isUndefined($(this).attr('dropdown-ini'))){
                    $(this).after($('#dropdown-item-template').html());
                    $(this).attr('dropdown-ini',1);
                }
            });

        }

        //显示所选学年的校历模版信息
        $('#calendar-years-list a[displayItems]').click(function(){
            totalCount = 0;
            $('#calendar-years-list li').removeClass('active');
            $(this).parents('li').addClass('active');

            var startYear = $(this).attr('startyear');
            renderCalenderCampusList(startYear);
        });
        schooldays=function(calendarId,num) {
            if(num==0){
                if(historydays[calendarId]){
                     thisSchoolDayList=historydays[calendarId]
                    calendarData[calendarId].year=thisSchoolDayList.year
                    calendarData[calendarId].semester=thisSchoolDayList.semester
                    calendarData[calendarId].isshow='1'
                }else{

                    thisSchoolDayList=schoolDayList[calendarId]
                    calendarData[calendarId].year=thisSchoolDayList.year
                    calendarData[calendarId].semester=thisSchoolDayList.semester
                    calendarData[calendarId].isshow='1'
                }
            }else{
                 thisSchoolDayList=historydays[calendarId]
                calendarData[calendarId].year=num.year
                calendarData[calendarId].semester=num.semester
                calendarData[calendarId].isshow='1'
            }
            var view = schoolday(calendarData[calendarId])
            $('#schoolnum').html(view);
        }
        displayCalendarEditForm = function(calendarId, domWrapperId){
            var _view = basicEditCalendarTemplate(calendarData[calendarId]);
            $(domWrapperId).html(_view);
            if(!calendarData[calendarId].isNew){
                $('#yid').val(calendarId);
                $(domWrapperId + ' ' + '#Calendar_startyear').attr('disabled','disabled');
                $(domWrapperId + ' ' + 'input[name|="Calendar[firstSemester][0]"]').attr('readonly','readonly');
                $(domWrapperId + ' ' + 'input[name|="Calendar[firstSemester][1]"]').attr('readonly','readonly');
                $(domWrapperId + ' ' + 'input[name|="Calendar[secondSemester][0]"]').attr('readonly','readonly');
                $(domWrapperId + ' ' + 'input[name|="Calendar[secondSemester][1]"]').attr('readonly','readonly');
                $(domWrapperId + ' ' + '#Calendar_startyear option[value="'+calendarData[calendarId].startyear+'"]').attr('selected', true).attr('disabled','disabled');
                $(domWrapperId + ' ' + 'a[enableEdit]').show();
                $('#calendar-detail-box').show();
            }else{
                $(domWrapperId + ' ' + 'a[enableEdit]').hide();
            }
            currentCalendarId = calendarId;
        }

        editCalendarTemplate = function(obj){
            var _cid = $(obj).parents('.panel').attr('calendarid');
             calendarId=_cid
            $('#calendar-detail-box .calendar-basic-edit span').html(calendarData[_cid].title);
            displayCalendarEditForm(_cid, '#calendar-edit-basic');
            schooldays(_cid,0)
            $( "#calendar-detail").empty();
        }

        newCalendarTemplate = function(){
            $('#J_fail_info').remove();
            $('#newCalendarModal').modal();
            displayCalendarEditForm(0, '#newCalendarModal #form-data');
        }

        enableEdit = function(obj){
            var _s = $(obj).attr('semester');
            var _jobj = $('input[semester|="'+_s+'"]');
            var _attr = _jobj.first().attr('readonly');
            if(_.isUndefined(_attr)){
                _jobj.attr('readonly','readonly');
            }else{
                _jobj.removeAttr('readonly');
            }
        }

        editCalendarDetail = function(){
            $.ajax({
                async: false,
                type: 'post',
                url: '<?php echo $this->createUrl('//moperation/calendar/getCalendarDays');?>',
                dataType: 'json',
                data: {calendarId: currentCalendarId}
            }).done(function(data){
                if(data.state == 'success'){
                    calendarDayData = data.data.cdata;
                    calendarDayData[0] = <?php echo CJSON::encode($newCalendarData);?>;

                    var minDate=new Date();
                    var maxDate=new Date();
                    var _date1 = explode('-',data.data.startdate1);
                    var _date2 = explode('-',data.data.enddate1);
                    minDate.setFullYear(parseInt(_date1[0]),parseInt(_date1[1]) - 1, parseInt(_date1[2]));
                    maxDate.setFullYear(parseInt(_date2[0]),parseInt(_date2[1]) - 1, parseInt(_date2[2]));

                    $( "#calendar-detail").empty();
                    $('<div id="calendar-no-1"></div>').appendTo($( "#calendar-detail" ));
                    if( $("#calendar-detail #calendar-no-1").html() != "" ){
                        $( "#calendar-detail #calendar-no-1").datepicker('refresh');
                    }else{
                        $( "#calendar-detail #calendar-no-1").datepicker({
                            minDate: minDate,
                            maxDate: maxDate,
                            numberOfMonths: 3,
                            dateFormat: 'yy-mm-dd',
                            onSelect: dateSelect,
                            selectOtherMonths: false,
                            showOtherMonths: false,
                            changeMonth: true,
                            changeYear: true,
                            firstDay: 0,
                            beforeShowDay: beforeShowDay,
                            onChangeMonthYear:onChangeMonthYear,
                        });
                    }

                    $('<div id="edit-zone"></div>').appendTo($( "#calendar-detail #calendar-no-1 .ui-datepicker-inline" ));
                    $('#edit-zone').html('<div class="mt20 alert alert-info">点击日历中的日期编辑</div>');

                    displayCalendarDays();
                    var year = $('.ui-datepicker-year option:selected').val();
                    var month = parseInt($('.ui-datepicker-month option:selected').val())+1;
                    onChangeMonthYear(year,month)
                }
            });
        }

        refreshCalendarDays = function(){
            $('.days-list[daytype]').empty();
            $.each(_.keys(calendarDayData), function(_k, day){
                var _display = calendarDayDisplayTemplate(calendarDayData[day]);
                $('#days-list .days-list[daytype|="'+calendarDayData[day].event+'"]').append(_display);
            });
            _.each($('#days-list .days-list[daytype]'), function(_box){
                var _heading = $(_box).siblings('div.panel-heading');
                _heading.find('small').html($(_box).children('dl[theday]').length);
            });

            head.Util.ajaxDel($('#days-list'));
        }

        displayCalendarDays = function(){
            $( "#calendar-detail").append($('<div id="days-list" class="row mt20"></div>'));
            $( '#days-list').html($('#hidden-days-list-category').html());
            refreshCalendarDays();
        }


        dateSelect = function(date){
            selectedDate = new Date(date);
            var theDate = $.datepicker.formatDate('yymmdd',selectedDate);
            if($('#calendar-day-edit').length == 0){
                $('#calendar-no-1').after($('<div id="calendar-day-edit" class="mt20"></div> '));
                $('#calendar-day-edit').html($('#day-edit-form-wrapper').html());
            }


            if(_.indexOf(editingDays, date) < 0){

                if(editingDays.length < 2 && _.isUndefined( $("#selectManyDays").attr("checked")) ){
                    if(_.isUndefined(calendarDayData[theDate])){
                        var _model = calendarDayData[0];
                    }else{
                        var _model = calendarDayData[theDate];
                    }
                    var _edit_form = calendarDayEditTemplate(_model);
                    $('#calendar-day-edit form').html(_edit_form);
                    $('#CalendarDay_privacy').attr('checked', _model.privacy == 1 ? true : false);
                    $('#CalendarDay_day_type').val(_model.day_type);
                    $('#CalendarDay_event').val(_model.event);
                    if(_model.event > 0){
                        changeEventType(document.getElementById('CalendarDay_event'));
                    }
                    head.Util.ajaxForm( $('#calendar-day-edit') );
                }

                if(_.isUndefined( $("#selectManyDays").attr("checked"))) {
                    editingDays = [];
                }
                editingDays.push(date);
                renderEditingDays();
                $('#editingDays').val(editingDays.join(','));
                $('#CalendarDay_yid').val(currentCalendarId);
            }
        }

        //编辑校历日时，显示在左边的日期列表
        function renderEditingDays(){
            $('#editing-days-list').empty();
            $.each(editingDays, function(_k, _d){
                var _item = ' <li mydate="'+_d+'" class="list-group-item"><span class="glyphicon glyphicon-calendar"></span> ' +_d + ' <span class="pull-right"><a href="javascript:;" onclick="removeEditingDay(this)"><span class="glyphicon glyphicon-remove"></span></a> </span> </li> ';
                $('#editing-days-list').append(_item);
            });
            if(editingDays.length>1){
                $("#selectManyDays").attr("disabled","disabled");
            }else{
                $("#selectManyDays").removeAttr("disabled");
            }
        }

        beforeShowDay = function(date){
            var theDate = $.datepicker.formatDate('yymmdd',date);
            var _css = [];
            _css.push(theDate);
            if(!_.isUndefined(calendarDayData[theDate])){
                var _tip = [calendarDayData[theDate].title_cn, calendarDayData[theDate].title_en, calendarDayData[theDate].memo_cn, calendarDayData[theDate].memo_en];
                _css.push('dayflag');
                _css.push('dayflag-' + calendarDayData[theDate].day_type + calendarDayData[theDate].event);
                return [true, _css.join(' '), _tip.join("\n")];
            }else{
                return [true, _css.join(' '), ''];
            }
        }
        onChangeMonthYear=function(date,mon){
        if (mon >= 1 && mon <= 9) {
            mon = '0' + mon;
        }else{
            mon=mon
        }
        var datapush=[]
        var year2 = date;
        var month2 = parseInt(mon);
         for(var i=1;i<3;i++){
            if (parseInt(month2)+i >12) {
                year3 = parseInt(year2) + parseInt((parseInt(month2)+1 - 12 == 0 ? 1 : parseInt(month2)+1 - 12));
                month4 = parseInt(month2)+i - 12;
                if (month4 < 10) {
                    month4 = '0' + month4;
                }
                datapush.push( year3.toString()+ month4.toString())
            }else{
               month3=parseInt(month2)+i
                if (month3 < 10) {
                    month3 = '0' + month3;
                }
                 datapush.push( year2.toString() + month3.toString())
            }
        }
         if($('#daynum').length == 0){
            $('#calendar-no-1').append($('<div class="panel panel-default mt15" style="width:56em"> <div class="panel-body"  id="daynum"></div></div>'));
         }
        if(historydays[calendarId]){
            days=historydays[calendarId]
            var html=''
                html+='<div class="col-md-4">'+date.toString()+mon.toString()+'：'+'<?php echo Yii::t('global', 'School Days num'); ?>'.replace(/num/g,days.month[date.toString()+mon.toString()])+'</div>'
                if(days.month[datapush[0]]){

                    html+='<div class="col-md-4">'+datapush[0]+'：'+'<?php echo Yii::t('global', 'School Days num'); ?>'.replace(/num/g,days.month[datapush[0]])+'</div>'
                }
                if(days.month[datapush[1]]){
                    html+='<div class="col-md-4">'+datapush[1]+'：'+'<?php echo Yii::t('global', 'School Days num'); ?>'.replace(/num/g,days.month[datapush[1]])+'</div>'
                }
            $('#daynum').html(html);
        }else{
            var str=''
                str+='<div class="col-md-4">'+date.toString()+mon.toString()+'：'+'<?php echo Yii::t('global', 'School Days num'); ?>'.replace(/num/g,thisSchoolDayList.month[date.toString()+mon.toString()])+'</div>'
                if(thisSchoolDayList.month[datapush[0]]){
                    str+='<div class="col-md-4">'+datapush[0]+'：'+'<?php echo Yii::t('global', 'School Days num'); ?>'.replace(/num/g,thisSchoolDayList.month[datapush[0]])+'</div>'
                }
                if(thisSchoolDayList.month[datapush[1]]){
                    str+='<div class="col-md-4">'+datapush[1]+'：'+'<?php echo Yii::t('global', 'School Days num'); ?>'.replace(/num/g,thisSchoolDayList.month[datapush[1]])+'</div>'
                }
            $('#daynum').html(str);

        }

    }
        startYearChange = function(obj){
            currentStartYear = $(obj).children("option:selected").val()
        }

        setCalendarDate = function(obj){
            var theDay = $.datepicker.parseDate( 'yymmdd', $(obj).attr("theday"));
            $( "#calendar-detail #calendar-no-1").datepicker("setDate", theDay );
            var year = $('.ui-datepicker-year option:selected').val();
            var month = parseInt($('.ui-datepicker-month option:selected').val())+1;
            onChangeMonthYear(year,month)
        }

        removeEditingDay = function(obj){
            var _mydate = $(obj).parents('li[mydate]').attr('mydate');
            editingDays = _.without(editingDays, _mydate);
            if(editingDays.length == 0){
                closeEditingDayWindow();
            }else{
                renderEditingDays();
            }
        }

        closeEditingDayWindow = function(){
            editingDays = [];
            $('#calendar-day-edit').remove();
        }

        var daysEventOptions = {};
        changeEventType = function(obj){
            $('#event-default-list').hide();
            var _type = $(obj).find('option:selected').val();
            if(!_.isEmpty(_type)){
                var _key = 'calendar_day_' + _type;
                if(_.isUndefined(daysEventOptions[_type])){
                    daysEventOptions[_type] = '';
                    var _itemTemplate = _.template('<li><a onclick="setDefaultEvent(this)" data-index=<%= index %> data-event=<%= key %> href="javascript:;" title="<%= othertitle %>"><%= title %></a></li>');
                    if(!_.isUndefined(globalDefaultDays) && globalDefaultDays && globalDefaultDays[_key]){
                        for(var _i=0; _i<globalDefaultDays[_key].length; _i++){
                            var _title = (LANG == 'en_us') ? globalDefaultDays[_key][_i].entitle: globalDefaultDays[_key][_i].cntitle;
                            var _othertitle = (LANG == 'en_us') ? globalDefaultDays[_key][_i].cntitle: globalDefaultDays[_key][_i].entitle;
                            daysEventOptions[_type] += _itemTemplate({index:_i,key:_type,title:_title, othertitle: _othertitle});
                        }
                    }
                }

                $('#event-default-list').empty().html(daysEventOptions[_type]);
                $('#event-default-list').show();
            }
        }

        setDefaultEvent = function(obj){
            var bterm = globalDefaultDays['calendar_day_' + $(obj).attr('data-event')][$(obj).attr('data-index')];
            $('#CalendarDay_title_cn').val(bterm.cntitle);
            $('#CalendarDay_title_en').val(bterm.entitle);
        }

        deleteCalendarTemplate = function(obj){
            var yid = $(obj).parents('div[calendarid]').attr('calendarid');
            head.load('<?php echo Yii::app()->themeManager->baseUrl;?>/base/js/dialog/dialog.js', function(){
                $.post('<?php echo $this->createUrl('delCalendar')?>', {yid: yid}, function(data){
                    if(data.state == 'success'){
                        resultTip({msg: data.message});
//                        delete calendarData[yid];
                        $.each(calendarBigMap[data.data.startyear],function(_m, _yid){
                            if(yid == _yid){
                                calendarBigMap[data.data.startyear].splice(_m, 1);
                            }
                        });
                        renderCalenderCampusList(data.data.startyear);
                        $('#calendar-detail-box').hide();
                    }
                    else{
                        head.dialog.alert(data.message);
                    }
                }, 'json');
            });
        }

        saveCalendarTemplateAs = function(obj){

            var yid = $(obj).parents('div[calendarid]').attr('calendarid');
            var startyear = calendarData[yid].startyear;
            $("#stat").attr("checked","checked");
            $('#CalendarSaveASModal #fromyid').val(yid);
            $('#CalendarSaveASModal #startyear').text(startyear+' - '+(parseInt(startyear)+1));
            $('#CalendarSaveASModal').modal();

        }

        showUnassignedCampus = function(obj){
            $(obj).siblings('ul.dropdown-menu').empty();
            var _templ = _.template('<li><a data-schoolid="<%= schoolId%>" href="#" onclick="assignCalendarTemplate(this)"><%= branchData[schoolId] %></a></li>');
            $.each(unassignedCampusList,function(_i,_d){
                var _view = _templ({schoolId: _d});
                $(obj).siblings('ul.dropdown-menu').append(_view);
            })
        }

        assignCalendarTemplate = function(obj){
            var schoolid    = $(obj).data('schoolid');
            var yid         = $(obj).parents('div.panel').attr('calendarid');
            $.post('<?php echo $this->createUrl('assignCalendar');?>', {yid: yid, schoolid: schoolid}, function(data){
                if(data.state == 'success'){
                    resultTip({msg: data.message});
                    if(_.isUndefined(calendarData[yid]['campuses'])){
                        calendarData[yid]['campuses'] = [];
                    }
                    calendarData[yid]['campuses'].push({branchid: schoolid, selected: 0});
                    renderCalenderCampusList(calendarData[yid].startyear);
                }
                else{
                    head.load('<?php echo Yii::app()->themeManager->baseUrl;?>/base/js/dialog/dialog.js', function(){
                        head.dialog.alert(data.message);
                    });
                }
            }, 'json');
        }

        unassignCalendar = function(obj){
            var schoolid    = $(obj).parents('span.dropdown').data('schoolid');
            var yid         = $(obj).parents('div.panel').attr('calendarid');
            if(schoolid && yid){
                $.post('<?php echo $this->createUrl('unassignCalendar');?>', {yid: yid, schoolid: schoolid}, function(data){
                    if(data.state == 'success'){
                        resultTip({msg: data.message});
                        $.each(calendarData[yid]['campuses'], function(key, value){
                            if(!_.isUndefined(value) && value.branchid == schoolid){
                                calendarData[yid]['campuses'].splice(key, 1);
                            }
                        })
                        renderCalenderCampusList(calendarData[yid].startyear);
                    }
                    else{
                        head.load('<?php echo Yii::app()->themeManager->baseUrl;?>/base/js/dialog/dialog.js', function(){
                            head.dialog.alert(data.message);
                        });
                    }
                }, 'json');
            }
            else{
                head.load('/<?php echo Yii::app()->themeManager->baseUrl;?>/base/js/dialog/dialog.js', function(){
                    head.dialog.alert('正在使用不能移除！');
                });
            }
        }

        setCurrentCalendar = function(obj){
            var schoolid    = $(obj).parents('span.dropdown').data('schoolid');
            var yid         = $(obj).parents('div.panel').attr('calendarid');
            if(schoolid && yid){
                $.post('<?php echo $this->createUrl('setCurrentCalendar');?>', {yid: yid, schoolid: schoolid}, function(data){
                    if(data.state == 'success'){
                        resultTip({msg: data.message});
                        $.each(calendarData[yid]['campuses'], function(key, value){
                            if(!_.isUndefined(value) && value.branchid == schoolid){
                                calendarData[yid]['campuses'][key]['selected'] = 1;
                            }
                        })
                        renderCalenderCampusList(calendarData[yid].startyear);
                    }
                    else{
                        head.load('<?php echo Yii::app()->themeManager->baseUrl;?>/base/js/dialog/dialog.js', function(){
                            head.dialog.alert(data.message);
                        });
                    }
                }, 'json');
            }
            else{
                resultTip({error: 1,msg: '已经是默认，不要重复设置。'});
            }
        }

        gotoSchoolCalendar = function(obj){
            var schoolid    = $(obj).parents('span.dropdown').data('schoolid');
            window.open('<?php echo $this->createUrl('/mcampus/calendar/index');?>?branchId='+schoolid);
        }
    });

    // 编辑校历回调函数
    function cb(data)
    {
        historydays[calendarId]=data.schoolDay[calendarId]
        schooldays(calendarId,data.schoolDay[calendarId]);
        var my_element = $("#calendar-no-1" )
        if(my_element.length>0){
            $('#calendar-detail').html('')
        }
        if(data.isNew){
            setTimeout(function(){$('#newCalendarModal').modal('hide')}, 1000);
            calendarBigMap[data.startyear].push(data.yid);
            $('#calendar-years-list li a[startyear="'+data.startyear+'"]').parent().addClass('active');
        }
        else{
            $('#calendar-detail-box .calendar-basic-edit span').html(data.title);
            $('#calendar-detail').html();
            var campuses = calendarData[data.yid].campuses;
            delete calendarData[data.yid];
        }
        calendarData[data.yid] = {
            campuses: campuses,
            firstSemester: data.firstSemester,
            secondSemester: data.secondSemester,
            startyear: data.startyear,
            title: data.title,
            yid: data.yid,
            stat: data.stat,
            isNew: false,
            isStatus:data.isStatus
        };
        renderCalenderCampusList(data.startyear);
    }

    // 编辑天（假期、事件等）回调函数
    function cbc(data)
    {
        historydays[calendarId]=data.calendar[calendarId]
        var year = $('.ui-datepicker-year option:selected').val();
        var month = parseInt($('.ui-datepicker-month option:selected').val())+1;
        onChangeMonthYear(year,month)
        schooldays(calendarId,data.calendar[calendarId]);
        setTimeout(function(){closeEditingDayWindow();}, 1000);
        $.each(data.data, function(key, value){
            calendarDayData[key] = value;
        });
        refreshCalendarDays();
        $( "#calendar-detail #calendar-no-1").datepicker('refresh');
    }

    // 删除天回调函数
    function deldayCallback(data)
    {
        if(data.data.length!=''){
             historydays[calendarId]=data.data.calendar[calendarId]
            var year = $('.ui-datepicker-year option:selected').val();
            var month = parseInt($('.ui-datepicker-month option:selected').val())+1;
            onChangeMonthYear(year,month)
            schooldays(calendarId,data.data.calendar[calendarId]);
        }
        delete calendarDayData[data.day];
        refreshCalendarDays();
        $( "#calendar-detail #calendar-no-1").datepicker('refresh');
    }

    // 校历模版另存回调函数
    function reshowC(data)
    {
        setTimeout(function(){$('#CalendarSaveASModal').modal('hide');}, 1000);
        calendarData[data.yid] = {
            campuses: [],
            startyear: data.startyear,
            title: data.title,
            yid: data.yid,
            isNew: false,
            firstSemester: calendarData[data.fromyid].firstSemester,
            secondSemester: calendarData[data.fromyid].secondSemester,
            isStatus:data.isStatus,
            year:calendarData[data.fromyid].year,
            semester:calendarData[data.fromyid].semester
        };
        schoolDayList[data.yid] = {
            month: schoolDayList[data.fromyid].month,
            year:schoolDayList[data.fromyid].year,
            semester:schoolDayList[data.fromyid].semester
        };
        calendarBigMap[data.startyear].push(data.yid);
        renderCalenderCampusList(data.startyear);
    }
</script>

<div id="hidden-days-list-category" style="display: none;">
    <div class="col-md-4">
        <div class="panel panel-default">
            <div class="panel-heading"><?php echo Yii::t('site','Holiday List');?> <small class="text-primary"></small></div>
            <div class="panel-body days-list" daytype="10">
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="panel panel-default">
            <div class="panel-heading"><?php echo Yii::t('site','Event List');?> <small class="text-primary"></small></div>
            <div class="panel-body days-list" daytype="20">
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="panel panel-default">
            <div class="panel-heading"><?php echo Yii::t('site','Schooldays Make-up');?> <small class="text-primary"></small></div>
            <div class="panel-body days-list" daytype="30">
            </div>
        </div>
    </div>
</div>

<div id="day-edit-form-wrapper" style="display: none;">
    <div class="panel panel-primary">
        <div class="panel-heading"><?php echo Yii::t('message','Edit Calendar Day');?> <span></span></div>
        <div class="panel-body">
            <form class="day-edit-form J_ajaxForm" method="post" action="<?php echo $this->createUrl('saveCalendarDays');?>"></form>
        </div>
    </div>
</div>
