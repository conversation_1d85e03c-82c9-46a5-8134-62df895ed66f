<?php
class ChildSearchHandy extends CWidget
{
	public $inputCSS = 'length_5';
	public $name = 'searchChild';
	public $popPosition = array('my'=>"left top", 'at'=>"left bottom", 'collision'=>"none");
	public $simpleDisplay = false;
    public $displayLabel = null;
    public $restrictSchool = '';
    public $allowMultipleSchool = false;
    public $abb = '';
    public $abbHtml = '';
	
	public function run(){
        if($this->displayLabel === null){
            $this->displayLabel = Yii::t('navs','Child Search');
        }
		if ($this->allowMultipleSchool && $this->abb) {
			$abb = sprintf(Yii::t('child', "%s only"), $this->abb);
			$this->abbHtml = "<div class=\'pull-right\'><label class=\'checkbox-inline\'><input type=\'checkbox\' id=\'schoolCheckbox\' onChange=handleSchoolCheckbox(this)>" .$abb. "</label></div>";
		}
		$this->render('childsearchhandy');
	}

}