<div style="max-height:200px;overflow-y:auto;">
<?php $this->widget('ext.ivyCGridView.IvyCGridView', array(
	'id'=>'points-stock-grid',
	'dataProvider'=>$dataProvider,
    'hideHeader'=>true,
    'colgroups'=>array(
        array(
            "colwidth"=>array(80,100,400),
        )
    ),
	'columns'=>array(
		array(
            'name'=>'schoolid',
            'value'=>'$data->schoolid',
        ),
        array(
            'name'=>'update_timestamp',
            'value'=>'OA::formatDateTime($data->update_timestamp)',
        ),
        'content',
	),
)); 
?>
</div>

<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'log-form',
	'enableAjaxValidation'=>false,
	'htmlOptions'=>array('class'=>'J_ajaxForm'),
));
?>
<div class="pop_cont pop_table" style="height:auto;">
    <div>正在添加 <?php echo $model->parent_name ? $model->parent_name : $model->child_name;?> 的跟踪记录</div>
    <table width="100%">
        <colgroup>
            <col class="th" />
            <col />
        </colgroup>
        <tbody>
            <tr>
                <th><?php echo $form->labelEx($modelLog,'content'); ?></th>
                <td><?php echo $form->textArea($modelLog,'content', array('class'=>'length_5')); ?></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($modelLog,'purpose'); ?></th>
                <td><?php echo $form->dropDownList($model, 'purpose', $this->cfg['purpose'], array('empty'=>Yii::t('global','Please Select')));?></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($modelLog,'status'); ?></th>
                <td><?php echo $form->dropDownList($model, 'status', $this->cfg['status']);?></td>
            </tr>
        </tbody>
    </table>
</div>
<div class="pop_bottom">
    <button type="button" class="btn fr" id="J_dialog_close"><?php echo Yii::t('global','Cancel');?></button>
    <button class="btn fr btn_submit mr10 J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
</div>
<?php $this->endWidget(); ?>