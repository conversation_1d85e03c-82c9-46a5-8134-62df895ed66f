
<?php
$this->renderPartial('header');
	$this->widget('zii.widgets.CMenu',array(
		'items'=> $pageSubMenu,
		'activeCssClass'=>'current',
		'id'=>'page-subMenu',
		'htmlOptions'=>array('class'=>'pageSubMenu')
	));
	echo CHtml::openTag("div", array("class"=>"c"));
	echo CHtml::closeTag("div");
?>
<div class="h_a"><?php echo Yii::t("campus", "搜索条件");?></div>
<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'search-form',
    'htmlOptions'=>array('class'=>'J_ajaxForm form'),
	'clientOptions' => array(
	),
));
$model = new Invoice;
?>
<div class="prompt_text">
    <ul>
        <li style="list-style: none;">
            <?php echo Yii::t('campus', '折扣信息');?> <?php echo $form->dropDownList($model,'discount_id',$discountList);?>
             <?php echo Yii::t('campus', '开始日期');?> <?php
                    echo $this->widget('zii.widgets.jui.CJuiDatePicker', array(
							"model"=>$model,
							"attribute"=>"startdate",
							"options"=>array(
								'changeMonth'=>true,
								'changeYear'=>true,
								'dateFormat'=>'yy-mm-dd',
							),
							'htmlOptions'=>array('class'=>'mr10 input length_2'),
						), true);
            ?>
            <?php echo Yii::t('campus', '结束日期');?> <?php
                    echo $this->widget('zii.widgets.jui.CJuiDatePicker', array(
							"model"=>$model,
							"attribute"=>"enddate",
							"options"=>array(
								'changeMonth'=>true,
								'changeYear'=>true,
								'dateFormat'=>'yy-mm-dd',
							),
							'htmlOptions'=>array('class'=>'mr10 input length_2'),
						), true);
            ?>
            <button class="btn btn_submit mr10" type="button" onclick="invoiceSearch(this)"><?php echo Yii::t('campus', '搜索帐单');?></button>
        </li>
    </ul>
</div>
<?php $this->endWidget(); ?>
<div class="table_full">
    
</div>
<script type="text/javascript">
function invoiceSearch(_this){
    var action = $(_this.form).attr('action');
    var url = (typeof action === 'string') ? $.trim(action) : '';
    var btn = $(_this);
    $.ajax({
        url: url,
        type: 'post',
        data: $(_this.form).serialize(),
        dataType: 'json',
        beforeSend: function(arr, $form, options) {
            var text = btn.text();
            btn.text(text +'中...').prop('disabled',true).addClass('disabled');
        },
        success: function(data){
            var text = btn.text();    
            btn.removeClass('disabled').text(text.replace('中...', '')).removeProp('disabled');
            $(".table_full").html(data);
        }
    });
}
function operateInvoice(invoiceId,childId,type){
    $.ajax({
        url: "<?php echo $this->createUrl('//child/list/subsidy');?>",
        type: 'post',
        data: 'childId='+childId+'&invoiceId='+invoiceId+'&type='+type,
        dataType: 'json',
        success: function(data){
            $("#subsidy_"+childId+"_"+invoiceId).html(data);
        }
    });
}

function saveSubsidy(invoiceId,childId,type){
    var amount = $("#amount_"+invoiceId).val();
    if (amount && invoiceId && type && childId){
        $.ajax({
            url: "<?php echo $this->createUrl('//child/list/processSubsidy');?>",
            type: 'post',
            data: 'childId='+childId+'&invoiceId='+invoiceId+'&type='+type+'&amount='+amount,
            dataType: 'json',
            success: function(data){
                $("#message_"+childId+"_"+invoiceId).html(data);
            }
        });
    }
}
</script>