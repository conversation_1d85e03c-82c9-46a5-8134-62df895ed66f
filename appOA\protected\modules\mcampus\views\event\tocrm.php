<div class="displayResult" id="log" style="height: 400px; overflow-y: auto;">
    <ol id="loginner"></ol>
</div>
<script type="text/javascript">
var log = '<?php echo $rStr?>';
var n = 0;
var timer = 0;
log = log.split('<ivy>');
function GoPlay(){
	if (n > log.length-1) {
		n=-1;
		clearIntervals();
	}
	if (n > -1) {
		postcheck(n);
		n++;
	}
}
function postcheck(n){
	document.getElementById('loginner').innerHTML += log[n];
	document.getElementById('log').scrollTop = document.getElementById('log').scrollHeight;
}
function setIntervals(){
	timer = setInterval('GoPlay()',50);
}
function clearIntervals(){
	clearInterval(timer);
}

setTimeout(setIntervals, 100);
</script>