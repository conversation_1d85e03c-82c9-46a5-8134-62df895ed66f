<?php 
$purchaseProducts = Yii::app()->request->getParam('PurchaseProducts','');
 ?>
<div class="container-fluid">
<?php $form=$this->beginWidget('CActiveForm', array(
		'id'=>'actiivity',
		'enableAjaxValidation'=>false,
		'htmlOptions'=>array('class'=>'J_ajaxForm table-responsive', 'role'=>'form'),
)); ?>

<div class="form-horizontal">
</div>
	<div>正在预览园长报告，确认无误后请点击生成报告</div>
	<table class="table table-bordered">
		<tr>
			<td></td>
			<td></td>
			<td colspan="6" style="text-align: center">人数(实际付款人数)/全天率</td>
		</tr>
		<tr>
			<td>班级</td>
			<td>满班人数</td>
			<td>一</td>
			<td>二</td>
			<td>三</td>
			<td>四</td>
			<td>五</td>
			<td>周全天率</td>
		</tr>

		<?php foreach($class_capacity as $k=> $value): ?>	
			<tr>
				<td><?php echo $value->title ?></td>
				<td><?php echo $value->capacity ?></td>
				
				<?php
				$total = 0;
				$w = 0;
				$q = 0;
				foreach($childArr[$value->classid] as  $v){
						if($value->capacity != 0){
							echo '<td>' . $v['sum'] . '/' . $v['paid'] . '/'.number_format($v['sum']/$value->capacity * 100,2).'%' .'</td>';
						}	
					$total += $v['sum'];
					$w++;
				}
				if(!$value->capacity){
					for($q;$q<5;$q++){
							echo "<td> N/A </td>";
						}
				}else{
					if($w != 5){
						for($w;$w<5;$w++){
							echo "<td></td>";
						}
					}
				}
				?>
				<td><?php 			
				if($value->capacity != 0){
					echo number_format($total/($value->capacity*count($childArr[$value->classid]))*100,2).'%' ;
				}
				?></td>				
			</tr>
		<?php endforeach; ?>
			<tr>
				<td>合计人数</td>
				<td><?php echo $people; ?></td>
				<?php
				$mun = 0;
				$tue = 0;
				$wed = 0;
				$for = 0;
				$fri = 0;
				$muns = 0;
				$tues = 0;
				$weds = 0;
				$fors = 0;
				$fris = 0;
				foreach($array as $v){
					$mun += $v[1];
					$tue += $v[2];
					$wed += $v[3];
					$for += $v[4];
					$fri += $v[5];
					$muns += $v['right'][1];
					$tues += $v['right'][2];
					$weds += $v['right'][3];
					$fors += $v['right'][4];
					$fris += $v['right'][5];
				}?>
				<td><?php echo $mun. '/' .number_format($mun/$people*100,2) ?>%</td>
				<td><?php echo $tue. '/' .number_format($tue/$people*100,2) ?>%</td>
				<td><?php echo $wed. '/' .number_format($wed/$people*100,2) ?>%</td>
				<td><?php echo $for. '/' .number_format($for/$people*100,2) ?>%</td>
				<td><?php echo $fri. '/' .number_format($fri/$people*100,2) ?>%</td>
				<td><?php echo number_format(($mun+$tue+$wed+$for+$fri)/($people*5)*100,2) ?>%</td>
			</tr>
		
		<tr>
			<td>合计付款人数</td>
			<td></td>
			<td><?php echo $muns?></td>
			<td><?php echo $tues?></td>
			<td><?php echo $weds?></td>
			<td><?php echo $fors?></td>
			<td><?php echo $fris?></td>
			<td></td>
		</tr>
		
		</table>
		<div class="row">
		  <div class="col-xs-6 col-md-3">本周退学人数</div>
		  <div class="col-xs-6 col-md-3"><?php  echo $exitschool;?></div>
		  <div class="col-xs-6 col-md-3">交付定金人数</div>
		  <div class="col-xs-6 col-md-3"><?php echo $deposit ?></div>
		</div>
		<div class="row">
		  <div class="col-xs-6 col-md-3">本周来电来访人数</div>
		  <div class="col-xs-6 col-md-3"><?php  echo $visit;?></div>
		  <div class="col-xs-6 col-md-3">本周跟踪人数</div>
		  <div class="col-xs-6 col-md-3"><?php echo $tracelog ?></div>
		</div>
<?php if($dataProvider): ?>		
		<div class="panel-body">
				<?php
					$this->widget('ext.ivyCGridView.BsCGridView', array(
						'id'=>'injuredlist',
						'afterAjaxUpdate' => 'js:function(){head.Util.ajaxDel()}',
						'dataProvider' => $dataProvider,
						'columns' => array(
							array(
								'name' => 'schoolid',
								'value' => '$data->class_name->title',
							),
							array(
								'name' => 'childid',
								'value' => '$data->child_name->getChildName()',
							),
							'injuryreport',
							'parentback',
						),
					)); 
				?>
		</div>
		<?php endif; ?>
			<h3>本周主要活动</h3>
		<?php if(!empty($activit)): ?>		
			<div id="accontent"><?php echo $activit->accontent  ?></div>
		<?php else: ?>
			<div>N/A</div>
		<?php endif;?>
			<h3>其他建议和活动</h3>
		<?php if($activit): ?>		
			<div id="comments"><?php echo $activit->comments  ?></div>
		<?php else:?>
			<div>N/A</div>
		<?php endif;?>
		<?php if($crcampuscount):?>
				<div>园长报告已生成，如有问题可点击撤销重新生成<span  class="btn btn-primary" style="cursor: pointer" onclick="preview(1,2)">撤销</span></div>
		<?php else: ?>
				<div>正在预览园长报告，确认无误后请点击生成报告<span  class="btn btn-primary" style="cursor: pointer" onclick="preview(1,1)">生成报告</span></div>
		<?php endif; ?>
		
<?php $this->endWidget(); ?>
</div>
<script>
function getContent(value){
		var weekid = value;
		$(weekid).attr("selected", true); //设置Select的Text值为jQuery的项选中 
		window.location.href="<?php echo $this->createUrl('preview');?>"+'&weeknumber='+ weekid+'&found='+<?php echo $yid ?>;
	}
</script>

<?php $this->renderPartial('//layouts/common/branchSelectBottom');?>
