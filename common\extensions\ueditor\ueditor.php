<?php

class ueditor extends CInputWidget {
    
    /**
	 * Editor language
	 * Supports: zh-cn  or en
	 */
	public $language = 'zh-cn';
    
    /**
	 * Editor toolbars
	 * Supports: 
	 */
	public $toolbars = '';
    
    /**
	 * Html options that will be assigned to the text area
	 */
	public $htmlOptions = array();
    
	/**
	 * Editor options that will be passed to the editor
	 */
	public $editorOptions = array();
    
	/**
	 * Debug mode
	 * Used to publish full js file instead of min version
	 */
    public $debug = false;
    
    /**
	 * Editor width
	 */
	public $width = '100%';
    
	/**
	 * Editor height
	 */
	public $height = '400px';
    
    /**
	 * Editor theme
     * Supports: default
	 */
	public $theme = 'default';

    public $upImageUrl = '';

    public $imageMgt = '';

    /**
	 * Display editor
	 */
    public function run() {
	
		// Resolve name and id
		list($name, $id) = $this->resolveNameID();

		// Get assets dir
        $baseDir = dirname(__FILE__);
        $assets = Yii::app()->getAssetManager()->publish($baseDir.DIRECTORY_SEPARATOR.'ueditor_mini', true, -1, YII_DEBUG);

		// Publish required assets
		$cs = Yii::app()->getClientScript();

        $cs->registerCssFile($assets.'/themes/'.$this->theme.'/css/umeditor.min.css');

        $cs->registerScriptFile($assets.'/umeditor.config.js');

		$jsFile = $this->debug ? 'umeditor.js' : 'umeditor.min.js';
		$cs->registerScriptFile($assets.'/' . $jsFile);

        $this->htmlOptions['id'] = $id;

        if (!array_key_exists('style', $this->htmlOptions)) {
            $this->htmlOptions['style'] = "width:{$this->width}";
        }
        
        if($this->toolbars){
            $this->editorOptions['toolbars'][] = $this->toolbars;
        }

        if(!$this->upImageUrl){
            $this->upImageUrl = $assets.'/php/imageUp.php';
        }

		$options = CJSON::encode(array_merge(array('lang' => $this->language, 'imageUrl' => $this->upImageUrl, 'imageMgt' => $this->imageMgt),$this->editorOptions));

        $js =<<<EOP
UM.getEditor('$id',$options);
EOP;
		// Register js code
		$cs->registerScript('Yii.'.get_class($this).'#'.$id, $js, CClientScript::POS_READY);
	
		// Do we have a model
		if($this->hasModel()) {
            $html = CHtml::activeTextArea($this->model, $this->attribute, $this->htmlOptions);
        } else {
            $html = CHtml::textArea($name, $this->value, $this->htmlOptions);
        }

		echo $html;
    }
    
}