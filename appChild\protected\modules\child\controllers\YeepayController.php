<?php

class YeepayController extends ProtectedController {

    public function init() {
        parent::init();
        Yii::import('common.models.invoice.*');
        Yii::import('common.models.yeepay.*');
    }

    public function actionOnlineBankInfo() {

        $yeepayAvailable = false;

        $yeepayBankInfo = Mims::LoadConfig('CfgYeepayBankInfo');
        $yeepayMeridInfo = Mims::LoadConfig('CfgYeepayMeridInfo');
        if (!empty($yeepayMeridInfo[$this->getSchoolId()]['p1_MerId'])) {
            $yeepayAvailable = true;
        }

        $ph_cri = new CDbCriteria();
        $ph_cri->compare('childid', $this->getChildId());
        $ph_cri->order = 'hotValue DESC';
        $ph_cri->limit = 3;
        $habits = YpPayHabit::model()->findAll($ph_cri);

        $usedBank = array();
        if (!empty($habits)) {
            foreach ($habits as $key => $habit) {
                if (!empty($yeepayBankInfo['yeepayBankInfo'][$habit->pd_FrpId])) {
                    $usedBank[$habit->pd_FrpId] = $yeepayBankInfo['yeepayBankInfo'][$habit->pd_FrpId];
                }
            }
        }
        Yii::app()->clientScript->registerCssFile(Yii::app()->theme->baseUrl . '/css/paying.css?t=' . Yii::app()->params['refreshAssets']);
        $this->renderPartial('/yeepay/onlineBankInfo', array(
            'yeepayAvailable' => $yeepayAvailable,
            'usedBank' => $usedBank,
            'yeepayBankInfo' => $yeepayBankInfo['yeepayBankInfo']
                )
        );
    }

    public function actionOnlineNotice() {

        $yeepayBankInfo = Mims::LoadConfig('CfgYeepayBankInfo');

        Yii::app()->clientScript->registerCssFile(Yii::app()->theme->baseUrl . '/css/notice.css?t=' . Yii::app()->params['refreshAssets']);
        // 输出 变量到 视图文件
        $view_data = array(
            'childId' => $this->getChildId(),
            'langKey' => Yii::app()->language == 'en_us' ? 'en' : 'cn',
            'yeepayBankInfo' => $yeepayBankInfo['yeepayBankInfo'],
        );
        $this->renderPartial('onlineNotice', $view_data);
    }

}
