<?php

/**
 * This is the model class for table "ivy_branch".
 *
 * The followings are the available columns in table 'ivy_branch':
 * @property string $branchid
 * @property integer $group
 * @property integer $type
 * @property integer $city
 * @property string $title
 * @property string $abb
 * @property integer $status
 * @property string $description
 * @property string $address
 * @property string $contact
 * @property string $map
 * @property string $logo
 * @property integer $created_timestamp
 * @property integer $schcalendar
 * @property integer $semester
 * @property integer $semester_stat
 * @property string $cn_description
 * @property string $en_description
 * @property string $cn_map
 * @property string $en_map
 * @property integer $handover_timestamp
 * @property string $infopub_sort
 */
class Branch extends CActiveRecord {

    var $upload;

    CONST TYPE_OFFICE = 10;
//    CONST TYPE_CAMPUS = 20; //往前兼容；默认MI幼儿园；新程序不要使用这个
    CONST TYPE_CAMPUS_MI_PRESCHOOL = 20;
    CONST TYPE_CAMPUS_MONT_PRESCHOOL = 30;
    CONST TYPE_CAMPUS_ELEMENTARY_SCHOOL = 40;
    CONST TYPE_CAMPUS_DAYSTAR = 50;

    CONST PROGRAM_NONE = 0;
    CONST PROGRAM_IA = 10;
    CONST PROGRAM_IBS = 20;
    CONST PROGRAM_MIK = 30;
    CONST PROGRAM_DAYSTAR = 50;

    CONST STATUS_ACTIVE = 10;

    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return Branch the static model class
     */
    public static function model($className = __CLASS__) {
        return parent::model($className);
    }

    /**
     * @return string the associated database table name
     */
    public function tableName() {
        return 'ivy_branch';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules() {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('group, title, created_timestamp,abb,type,status', 'required'),
            array('group, type, city, status, created_timestamp, schcalendar, semester, semester_stat, handover_timestamp, opentime', 'numerical', 'integerOnly' => true),
            array('branchid', 'length', 'max' => 10),
            array('title, map, logo, cn_map, en_map', 'length', 'max' => 255),
            array('abb', 'length', 'max' => 25),
            array('infopub_sort, description, address, contact, map, logo, schcalendar, cn_description, en_description, cn_map, en_map', 'safe'),
            // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            array('branchid, group, type, city, title, abb, status, description, address, contact, map, logo, created_timestamp, schcalendar, semester, semester_stat, cn_description, en_description, cn_map, en_map, handover_timestamp, infopub_sort, opentime', 'safe', 'on' => 'search'),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations() {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
            "info" => array(self::HAS_ONE, 'BranchInfo', 'branchid'),
            /*
             * 学校 隶属于 城市
             */
            "cityInfo" => array(self::BELONGS_TO, 'Diglossia', 'city'),
            'points'   =>array(self::HAS_ONE, 'PointsConfig', 'schoolid'),
			'currentClasses' => array(self::HAS_MANY, 'IvyClass', array('yid'=>'schcalendar', 'schoolid'=>'branchid'), 'condition'=>'stat=10', 'together'=>false, 'order'=>'currentClasses.child_age, currentClasses.title'),
			'calendar' => array(self::BELONGS_TO, 'Calendar',array('schcalendar'=>'yid')),
            'voucherInvoice'=>array(self::HAS_MANY, 'InvoiceTransaction', array('schoolid'=>'branchid'), 'condition'=>'voucherInvoice.transactiontype=:transactiontype and (voucherInvoice.timestampe BETWEEN :startdate AND :enddate)', 'with'=>array('classInfo','childInfo'),'order'=>'voucherInvoice.timestampe ASC'),
        );
    }

	/**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels() {
        return array(
            'branchid' => 'Branchid',
            'group' => 'Group',
            'type' => 'Type',
            'city' => 'City',
            'title' => 'Title',
            'abb' => 'Abb',
            'status' => 'Status',
            'description' => 'Description',
            'address' => 'Address',
            'contact' => 'Contact',
            'map' => 'Map',
            'logo' => 'Logo',
            'created_timestamp' => 'Created Timestamp',
            'schcalendar' => 'Schcalendar',
            'semester' => 'Semester',
            'semester_stat' => 'Semester Stat',
            'cn_description' => 'Cn Description',
            'en_description' => 'En Description',
            'cn_map' => 'Cn Map',
            'en_map' => 'En Map',
            'handover_timestamp' => 'Handover Timestamp',
            'infopub_sort' => 'Infopub Sort',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search() {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('branchid', $this->branchid, true);
        $criteria->compare('group', $this->group);
        $criteria->compare('type', $this->type);
        $criteria->compare('city', $this->city);
        $criteria->compare('title', $this->title, true);
        $criteria->compare('abb', $this->abb, true);
        $criteria->compare('status', $this->status);
        $criteria->compare('description', $this->description, true);
        $criteria->compare('address', $this->address, true);
        $criteria->compare('contact', $this->contact, true);
        $criteria->compare('map', $this->map, true);
        $criteria->compare('logo', $this->logo, true);
        $criteria->compare('created_timestamp', $this->created_timestamp);
        $criteria->compare('schcalendar', $this->schcalendar);
        $criteria->compare('semester', $this->semester);
        $criteria->compare('semester_stat', $this->semester_stat);
        $criteria->compare('cn_description', $this->cn_description, true);
        $criteria->compare('en_description', $this->en_description, true);
        $criteria->compare('cn_map', $this->cn_map, true);
        $criteria->compare('en_map', $this->en_map, true);
        $criteria->compare('handover_timestamp', $this->handover_timestamp);
        $criteria->compare('infopub_sort', $this->infopub_sort, true);

        return new CActiveDataProvider($this, array(
                    'criteria' => $criteria,
                ));
    }

    /**
     * 取学校信息
     * @param string $schoolId
     * @param array|string $param
     * @return array|string|obj
     * <AUTHOR>
     */
    public function getBranchInfo($schoolId, $param = array()) {
        if ($schoolId) {
            $obj = $this->findByPk($schoolId);
            if (is_array($param) && count($param)) {
                return $obj->getAttributes($param);
            } elseif (is_string($param) && strlen($param)) {
                return $obj->getAttribute($param);
            } else {
                return $obj;
            }
        }
        return null;
    }

    /**
     *
     * @param type $withkey
     * @param type $type 10: office; 20: campus;
     * <AUTHOR>
     */
    public function getBranchList($firstLabel = null, $withkey = false, $type = '<>10', $status = 10) {

        $criteria = new CDbCriteria();
        if (!empty($type)) {
            if($type == 20){
                $criteria->compare('type', array(20,50));
            }
            else{
                $criteria->compare('type', $type);
            }
        }
        if(!empty($status))
        $criteria->compare('status', $status);
        $criteria->order = 'title ASC';
        $branchInfo = $this->findAll($criteria);
        $branchList = null;
        if (!empty($branchInfo)) {
            if ($withkey) {
                foreach ($branchInfo as $branch) {
                    $branchList[$branch->branchid] = array(
                        'id' => $branch->branchid,
                        'type' => $branch->type,
                        'title' => $branch->title,
                        'group' => $branch->group,
                        'city' => $branch->city,
                        'abb' => $branch->abb,
                        'yid' => $branch->schcalendar,
						'opentime' => floor((time() - $branch->opentime )/(365*24*60*60) )
                    );
                }
            } else {
                if ($firstLabel) {
                    $branchList[0] = $firstLabel;
                }
                foreach ($branchInfo as $branch) {
                    $branchList[$branch->branchid] = $branch->title;
                }
            }
        }
        return $branchList;
    }

    public function getNextCalendarId(){
        Yii::import('common.models.calendar.*');
        $currentCalendar = Calendar::model()->findByPk($this->schcalendar);

        $nextSchoolCalendar = CalendarSchool::model()->findByAttributes(array('branchid'=>$this->branchid,'startyear'=>$currentCalendar->startyear+1));

        return is_null($nextSchoolCalendar) ? 0 : $nextSchoolCalendar->yid;
    }

    public function getPrintHeader($lang=null)
    {
        $ret = array(
            'tel' => $this->info->tel,
            'fax' => $this->info->fax,
            'email' => $this->info->email,
            'address' => CommonUtils::autoLang($this->info->address_cn, $this->info->address_en, $lang),
            'logo' => $this->logo,
        );
        return $ret;
    }

    public static function branchTypes()
    {
        return array(
            Branch::TYPE_OFFICE=>"办公室",
            Branch::TYPE_CAMPUS_MI_PRESCHOOL=>'多元智能幼儿园',
            Branch::TYPE_CAMPUS_DAYSTAR=>'启明星模式校园',
            //Branch::TYPE_CAMPUS_MONT_PRESCHOOL=>'蒙台梭利幼儿园',
            //Branch::TYPE_CAMPUS_ELEMENTARY_SCHOOL=>'小学'
        );
    }

    public function getTitleEmail()
    {
        $cacheId = 'titleemail';
        $titleEmail = Yii::app()->cache->get($cacheId);
        if(!$titleEmail){
            $items = $this->with('info')->findAllByAttributes(array('status'=>10));
            foreach($items as $item){
                $titleEmail[$item->branchid] = array(
                    'title' => $item->title,
                    'support' => $item->info->support_email,
                );
            }
            Yii::app()->cache->set($cacheId, $titleEmail, 300);
        }
        return $titleEmail;
    }

    public static function branchAbb($abb='')
    {
        if($abb) {
            $sAbb = array(
                'IASLT' => 'SL',
                'SLT' => 'DT',
            );
            if (in_array($abb, array_keys($sAbb))){
                return $sAbb[$abb];
            }
            return $abb;
        }
    }

    public static function branchType()
    {
        $type = array(
            10 => Yii::t('global','Ivy Academy'),
            20 => Yii::t('global','Ivy Academy'),
            30 => Yii::t('global','Ivy Academy'),
            50 => Yii::t('global','Daystar Academy'),
        );

        return $type;
    }
}
