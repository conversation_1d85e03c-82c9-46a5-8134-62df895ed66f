<div style="font-family: 'Microsoft Yahei'">
    <p>请注意：本名单统计于本邮件发送之时，不包含在这之后的行政新增的操作名单
	</p>    
        <p>
        以下是[<?php echo $time ?>]<?php echo $k;?>午餐取消列表：
        </p>
	<?php
    foreach ($classInfo as $class => $val):
        if(isset($lunchs[$class]) && $lunchs[$class]):
	        echo $val;?>
		<table border='1' cellpadding='2' cellspacing='0' width="90%">
			<tr>
				<th width="100" style="font-family: 'Microsoft Yahei';font-size:12px;">孩子姓名</th>
				<th width="100" style="font-family: 'Microsoft Yahei';font-size:12px;">操作日期</th>
				<th width="100" style="font-family: 'Microsoft Yahei';font-size:12px;">操作人</th>
			</tr>
		<?php foreach($lunchs[$class] as $sch =>$lunch1): ?>
			<tr>
				<td style="font-family: 'Microsoft Yahei';font-size:12px;">
				   <?php echo $lunch1['child'];?>
				</td>
				
				<td style="font-family: 'Microsoft Yahei';font-size:12px;">
				 <?php echo $lunch1['timestamp'];?>
				</td>
				<td style="font-family: 'Microsoft Yahei';font-size:12px;">
					<?php if($lunch1['user']):
						echo $userInfo[$lunch1['user']];?>
					<?php endif?>
				</td>
			</tr>
		<?php endforeach;?>
		</table>
		<br>
	<?php endif;?>
	<?php endforeach;?>

	
	<p>要查询最新的或历史的请到“校园管理” - “班级管理” - “午餐情况报告”下操作</p>
	<p>Auto Email,please do not reply.<br>
	系统自动邮件，请不要回复。</p>
	
</div>