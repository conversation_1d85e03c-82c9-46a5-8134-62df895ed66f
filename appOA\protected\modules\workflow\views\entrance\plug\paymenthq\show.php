<?php
$disabled = true;
if ($data['workflow']->useRole == 1 && in_array($data['workflow']->getOperateStatus(), array(WorkflowOperation::WORKFLOW_STATS_UNOP, WorkflowOperation::WORKFLOW_STATS_RESET)) && $data['isFinance']) {
    $disabled = false;
}
$isPaid = $data['paymentApply']->payment_user > 0;
$payDate = date('Y.m.d', $data['paymentApply']->payment_time);
?>
<!-- Modal -->
<?php echo CHtml::form('/workflow/entrance/index', 'post', array('class' => 'J_ajaxForm')); ?>
<div class="modal fade" id="<?php echo $data['modalNameId'] ?>" tabindex="-1" role="dialog" aria-labelledby="workflowModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span>&times;</span></button>
                <h4 class="modal-title" id="myModalLabel"><?php echo CommonUtils::autoLang($data['workflow']->definationObj->defination_name_cn, $data['workflow']->definationObj->defination_name_en); ?> - <?php echo CommonUtils::autoLang($data['workflow']->nodeObj->node_name_cn, $data['workflow']->nodeObj->node_name_en); ?><a href="#" id="print"> [<?php echo Yii::t('workflow', 'print'); ?>]</a></h4>
            </div>
            <div class="modal-body" id="workflow-modal-body">
                <div class="row">
                    <div class="panel panel-default" style="margin: 10px;">
                        <div class="panel-body">
                            <!-- 左侧Logo -->
                            <div class='flex'>
                                <div class="img-div">
                                    <img class="centered-img" style="width: 180px;display: block;margin: 0 auto;" src="https://m2.files.ivykids.cn/cloud01-file-8014547FjYiFdWDDZo4YK_rBXjNuY1Snut9.png" alt="Logo">
                                </div>
                                <div class="flex1" style="margin-left: 180px;">
                                    <h4><?php echo CommonUtils::autoLang($data['workflow']->definationObj->defination_name_cn, $data['workflow']->definationObj->defination_name_en); ?></h4>
                                    <h4>唯一编号：<?php echo $data['workflow']->opertationObj->id; ?></h4>
                                </div>
                                <?php if ($isPaid): ?>
                                    <div class='text-center mr10'>
                                        <img style='width:60px;height:60px' src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/payment.png' ?>" alt="Logo">
                                        <div style='color:#5CB85C' class='mt5'><?php echo $payDate; ?> </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <table class="table table-bordered table-contract">
                            <?php if (!$disabled && $data['flowList']): ?>
                                <tr>
                                    <th><label>适用工作流 *</label></th>
                                    <td>
                                        <select name="flowId" class="form-control">
                                            <option value="">请选择</option>
                                            <?php foreach ($data['flowList'] as $flow) : ?>
                                                <option value="<?php echo $flow->defination_id; ?>" <?php if ($flow->defination_id == $data['lastFlowId']) echo 'selected'; ?>>
                                                    <?php echo CommonUtils::autoLang($flow->defination_name_cn, $flow->defination_name_en); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </td>
                                </tr>
                            <?php endif; ?>
                            <tr>
                                <th><label>付款方 *</label></th>
                                <td><?php echo $data['branchObj']->title; ?></td>
                            </tr>
                            <tr>
                                <th><label>申请标题 *</label></th>
                                <td>
                                    <?php if ($disabled): ?>
                                        <p><?php echo $data['paymentApply']->title; ?></p>
                                    <?php else: ?>
                                        <input name="title" class="form-control" type="text" value="<?php echo $data['paymentApply']->title; ?>">
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <th><label>收款方 *</label></th>
                                <td>
                                    <input id="vendor_id" name="vendor_id" value="<?php echo $data['paymentModel']->vendor_id; ?>" hidden>
                                    <?php echo $data['paymentApply']->bank_user; ?>
                                </td>
                            </tr>
                            <tr>
                                <th><label>银行账号 *</label></th>
                                <td><?php echo $data['paymentApply']->bank_account; ?></td>
                            </tr>
                            <tr>
                                <th><label>开户银行 *</label></th>
                                <td><?php echo $data['paymentApply']->bank_name; ?></td>
                            </tr>
                            <tr>
                                <th><label>付款总金额 *</label></th>
                                <td><?php echo number_format($data['paymentApply']->price_total / 100, 2); ?></td>
                            </tr>
                            <tr>
                                <th><label>预算标准 *</label></th>
                                <td>
                                    <?php if ($disabled): echo $data['paymentApply']->budget == 1 ? '预算中' : '预算外'; ?>
                                        
                                    <?php else: ?>
                                        <label class="form-check-label">
                                            <input <?php if ($data['paymentApply']->budget == 1) echo 'checked';?> type="radio" name="budget" value="1">
                                            预算内
                                        </label>
                                        <label class="form-check-label">
                                            <input <?php if ($data['paymentApply']->budget == 0) echo 'checked';?> type="radio" name="budget" value="0">
                                            预算外
                                        </label>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        </table>

                    </div>
                    <!-- 关联合同信息 -->
                    <?php if (isset($data['contractModel'])) : ?>
                        <div class="col-xs-12">
                            <label class="col-xs-4">关联合同信息：</label>
                            <div class="col-xs-8" id="constract_link">
                                <?php echo $data['contractLink']; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <td><?php echo Yii::t('workflow', '付款描述'); ?></td>
                            <td><?php echo Yii::t('workflow', '付款分类1'); ?></td>
                            <td><?php echo Yii::t('workflow', '付款分类2'); ?></td>
                            <td><?php echo Yii::t('workflow', '付款价格'); ?></td>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($data['paymentItem'] as $applicationItem) {
                            echo '<tr><td>' . $applicationItem->desc . '</td>';
                            echo '<td>' . $applicationItem->category1 . '</td>';
                            echo '<td class="text-right">' . $applicationItem->category2 . '</td>';
                            echo '<td class="text-right">' . number_format($applicationItem->price / 100, 2) . '</td>';
                        } ?>
                        <tr>
                            <td colspan="3"><?php echo Yii::t('workflow', 'Total:'); ?></td>
                            <td class="text-right"><?php echo number_format($data['paymentApply']->price_total / 100, 2); ?></td>
                        </tr>
                        <tr>
                            <td colspan="4"><?php echo Yii::t('workflow', 'Attachments:'); ?></td>
                        </tr>
                        <?php foreach ($data['workflow']->getAttachmentList() as $uploadFile) : ?>
                            <tr>
                                <td colspan="5"><a target="_blank" href="<?php echo $uploadFile['bigimages']; ?>"><?php echo $uploadFile['notes']; ?></a></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                <?php $this->renderPartial('workflow.views.entrance.plug.progressDetail', array('data' => $data)); ?>
            </div>
            <?php if ($data['workflow']->useRole == 1 && $data['workflow']->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_UNOP && !$data['workflow']->isFristNode()) : ?>
                <div class="modal-footer">
                    <?php echo CHtml::hiddenField('definationId', $data['workflow']->definationObj->defination_id); ?>
                    <?php echo CHtml::hiddenField('nodeId', $data['workflow']->nodeObj->node_id); ?>
                    <?php echo CHtml::hiddenField('operationId', $data['workflow']->opertationObj->id); ?>
                    <?php echo CHtml::hiddenField('branchId', $data['workflow']->opertationObj->branchid); ?>
                    <?php echo CHtml::hiddenField('action', 'show'); ?>
                    <?php echo CHtml::hiddenField('buttonCategory', ''); ?>
                    <button type="button" class="btn btn-primary J_ajax_submit_btn" data-category="<?php echo WorkflowOperation::WORKFLOW_STATS_OPED; ?>"><?php echo Yii::t('workflow', 'Yes'); ?></button>
                    <button type="button" class="btn btn-info J_ajax_submit_btn" data-category="<?php echo WorkflowOperation::WORKFLOW_STATS_RESET; ?>"><?php echo Yii::t('workflow', '退回修改'); ?></button>
                    <button type="button" class="btn btn-danger J_ajax_submit_btn" data-category="<?php echo WorkflowOperation::WORKFLOW_STATS_OPDENY; ?>"><?php echo Yii::t('workflow', '拒绝并作废'); ?></button>
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php CHtml::endForm(); ?>

<script>
    //打印
    $(function() {
        head.Util.ajax($('#constract_link'));
        const searchUrl = '<?php echo Yii::app()->createUrl("/workflow/index/searchVendor", array('branchId' => $data['workflow']->schoolId)); ?>';

        $("#bank_user").autocomplete({
            source: searchUrl,
            // minLength: 2,
            select: function(event, ui) {
                if (ui.item.value <= 0) {
                    return false;
                }
                $("#vendor_id").val(ui.item.vendor_id);
                $("#bank_user").val(ui.item.bank_user);
                $("#bank_name").val(ui.item.bank_name);
                $("#bank_account").val(ui.item.bank_account);
                // return false;
            },
        });

        $("#print").click(function() {
            $("#workflow-modal-body a").removeAttr('href');
            $("#workflow-modal-body").printThis({
                popTitle: '采购申请单'
            });
        })
    });
</script>