<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','HQ Operations'), array('default/index'));?></li>
        <li><?php echo CHtml::link(Yii::t('site','Support'), array('default/index'));?></li>
        <li class="active"><?php echo Yii::t('site','Exchange Items') ;?></li>
    </ol>
    <div class="row">
        <div class="col-md-12">
            <div class="background-gray p8">
                <?php echo CHtml::link('<span class="glyphicon glyphicon-plus"></span> 新增商品', array('update', 'event_id'=>$model->id), array('class'=>'btn btn-primary ', 'title'=>'新增商品'));?>
            </div>
            <?php
            $this->widget('ext.ivyCGridView.BsCGridView', array(
                'id'=>'points-product-grid',
                'afterAjaxUpdate' => 'js:function(){head.Util.ajaxDel()}',
                'dataProvider'=>$model->search(),
            //	'filter'=>$model,
                'colgroups'=>array(
                    array(
                        "colwidth"=>array(200,80,80,80,80,80,100,200),
            //            "htmlOptions"=>array("align"=>"left", "span"=>2)
                    )
                ),
                'columns'=>array(
                    'title'=>array(
                        'name'=>'商品名称',
                        'type'=>'raw',
                        'value'=>'$data->en_title."<br/>".$data->cn_title.$data->showCover("div", "", array("style"=>"height:80px"))',
                    ),
                    'credits',
                    'p_price',
                    'm_price',
                    'stock',
                    array(
                        'name' => 'status',
                        'value' => '$data->showStatus()',
                    ),
                    array(
                        'name'=>'updated',
                        'value'=>'OA::formatDateTime($data->updated)',
                    ),
                    array(
                        'class'=>'BsCButtonColumn',
                        // 'template' => '{gallery} {stock} {update} {delete}',
                        'template' => ' {stock} {update} {delete}',
                        'updateButtonOptions' => array('class'=>'btn btn-info btn-xs'),
                        'buttons'=>array(
                            'stock'=>array(
                                'label'=>'<span class="glyphicon glyphicon-plus-sign"></span>',
                                'url'=>'Yii::app()->createUrl("/moperation/product/stockcreate", array("productid" => $data->id))',
                                'options'=>array('class'=>'J_dialog btn btn-info btn-xs', 'title'=>'添加库存'),
                            ),
                            // 'gallery'=>array(
                            //     'label'=>'<span class="glyphicon glyphicon-picture"></span>',
                            //     'url'=>'Yii::app()->createUrl("/moperation/product/gallery", array("id" => $data->id))',
                            //     'options'=>array('class'=>'J_dialog btn btn-info btn-xs', 'title'=>'上传照片'),
                            // ),
                        ),
                    ),
                    array('value'=>''),
                ),
            )); ?>
        </div>
    </div>
</div>
