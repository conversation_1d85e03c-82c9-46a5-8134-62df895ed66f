<?php

class DeviceController extends BranchBasedController
{

	public function createUrl($route, $params = array(), $ampersand = '&')
	{
		if (empty($params['branchId'])) {
			$params['branchId'] = $this->branchId;
		}
		return parent::createUrl($route, $params, $ampersand);
	}

	public $schoolsName = array();

	public function init(){
		parent::init();
		Yii::app()->theme = 'blue';
		//$this->layout = "//layouts/column1";
		Yii::import('common.models.identity.*');
		$this->modernMenuFlag = 'campusOp';
		$this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
		$this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

		$this->branchSelectParams['urlArray'] = array('//identity/default/index');

		// jquery ui
		$cs = Yii::app()->clientScript;
		$cs->registerCoreScript('jquery.ui');
		$cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
		$cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue2.js');
	}

	public function actionIndex()
	{

		$critre = new CDbCriteria;
		$critre->compare('schoolid', $this->branchObj->abb);
		$cardsObj = new CActiveDataProvider('Devices', array(
			'criteria' => $critre,
			'sort' => array(
				'defaultOrder'=>array(
					'status'=>CSort::SORT_DESC,
					'updated'=>CSort::SORT_DESC,
				)
			),
			'pagination'=>array(
				'pageSize'=>50,
			),
		));

		$this->schoolsName = $this->getAllBranch();
		$this->render('index', array(
			'cardsObj' => $cardsObj,
		));
	}

	public function actionUpdateDevice()
	{
		$devicesid = intval(Yii::app()->request->getParam('id',0));

		$model = Devices::model()->findByPk($devicesid);
		if(empty($model)){
			$model = new Devices();
		}

		if(Yii::app()->request->isPostRequest){
			$model->attributes = $_POST['Devices'];
			$model->updated = time();
			$model->schoolid = $this->branchObj->abb;
			$model->uid = Yii::app()->user->id;
			if($model->save()){
				$this->addMessage('state', 'success');
				$this->addMessage('message', Yii::t('message','success'));
				$this->addMessage('callback', 'cbUpdateDevices');
				$this->showMessage();
			}else{
				$error = current($model->getErrors());
				$this->addMessage('state', 'fail');
				$this->addMessage('message', Yii::t('message', $error[0]));
				$this->showMessage();
			}

		}
		$this->renderPartial('_update', array(
			'model' => $model,
		));
	}

	public function getButton($data)
	{
		echo CHtml::link(Yii::t('global', 'Edit'), array('updateDevice', 'id' => $data->id, "branchId"=>Yii::app()->controller->branchId), array('class' => 'J_modal btn btn-xs btn-info')) . ' ';
	}

	public function getChildName($data)
	{
		echo $this->childsName[$data->childid];
	}

	public function getSchoolName($data)
	{
		$schoolName = $this->schoolsName[$data->schoolid];
		echo $schoolName['title'];
	}
}