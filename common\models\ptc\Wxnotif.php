<?php

/**
 * This is the model class for table "ivy_wxnotif".
 *
 * The followings are the available columns in table 'ivy_wxnotif':
 * @property integer $id
 * @property string $openid
 * @property string $wx_account
 * @property string $task_type
 * @property integer $taskid
 * @property integer $notify_at
 * @property integer $notified
 * @property string $randtag
 * @property integer $user_responed
 * @property integer $updated_at
 * @property integer $updated_by
 */
class Wxnotif extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_wxnotif';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('openid, wx_account, task_type, taskid, notify_at, randtag, updated_at, updated_by', 'required'),
			array('taskid, notify_at, notified, user_responed, updated_at, updated_by', 'numerical', 'integerOnly'=>true),
			array('openid', 'length', 'max'=>128),
			array('wx_account, task_type, randtag', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, openid, wx_account, task_type, taskid, notify_at, notified, randtag, user_responed, updated_at, updated_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'openid' => 'Openid',
			'wx_account' => 'Wx Account',
			'task_type' => 'Task Type',
			'taskid' => 'Taskid',
			'notify_at' => 'Notify At',
			'notified' => 'Notified',
			'randtag' => 'Randtag',
			'user_responed' => 'User Responed',
			'updated_at' => 'Updated At',
			'updated_by' => 'Updated By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('openid',$this->openid,true);
		$criteria->compare('wx_account',$this->wx_account,true);
		$criteria->compare('task_type',$this->task_type,true);
		$criteria->compare('taskid',$this->taskid);
		$criteria->compare('notify_at',$this->notify_at);
		$criteria->compare('notified',$this->notified);
		$criteria->compare('randtag',$this->randtag,true);
		$criteria->compare('user_responed',$this->user_responed);
		$criteria->compare('updated_at',$this->updated_at);
		$criteria->compare('updated_by',$this->updated_by);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return Wxnotif the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
