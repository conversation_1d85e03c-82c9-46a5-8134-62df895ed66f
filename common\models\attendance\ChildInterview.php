<?php

/**
 * This is the model class for table "ivy_child_interview".
 *
 * The followings are the available columns in table 'ivy_child_interview':
 * @property integer $id
 * @property string $school_id
 * @property integer $interview_time
 * @property integer $child_id
 * @property string $stat
 * @property string $discipline_stat
 * @property string $intreview_content
 * @property string $type
 * @property integer $updated_time
 * @property integer $uid
 */
class ChildInterview extends CActiveRecord
{
	public $host;
	public $time;
	public $day;
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_child_interview';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('school_id, interview_time, type', 'required'),
			array('interview_time, child_id, updated_time, uid', 'numerical', 'integerOnly'=>true),
			array('school_id, stat, type', 'length', 'max'=>255),
			array('intreview_content', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, school_id, interview_time, child_id, stat, discipline_stat, intreview_content, type, updated_time, uid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'childProfile' => array(self::HAS_MANY, 'ChildInterviewTeacher', array('child_interview_list'=>'id')),
			'admissionsds_child' => array(self::BELONGS_TO, 'AdmissionsDs', array('child_id'=>'id')),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'school_id' => 'School',
			'interview_time' => 'Interview Time',
			'child_id' => 'Child',
			'stat' => 'Stat',
			'discipline_stat' => 'Discipline Stat',
			'intreview_content' => 'Intreview Content',
			'type' => 'Type',
			'updated_time' => 'Updated Time',
			'uid' => 'Uid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('school_id',$this->school_id,true);
		$criteria->compare('interview_time',$this->interview_time);
		$criteria->compare('child_id',$this->child_id);
		$criteria->compare('stat',$this->stat,true);
		$criteria->compare('discipline_stat',$this->discipline_stat,true);
		$criteria->compare('intreview_content',$this->intreview_content,true);
		$criteria->compare('type',$this->type,true);
		$criteria->compare('updated_time',$this->updated_time);
		$criteria->compare('uid',$this->uid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	public static function getConfig()
	{
		return array(
			'course' => array(
				'1' => Yii::t('interview','Chinese Test'),
				'2' => Yii::t('interview','Math Test'),
				'3' => Yii::t('interview','English Test'),
			)
		);
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return ChildInterview the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
