<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <table class="table table-condensed">
                <tr>
                    <td>英文姓</td>
                    <td><?php echo $child->en_name ?></td>
                </tr>
                <tr>
                    <td>英文中间名</td>
                    <td><?php echo $child->en_name_middle ?></td>
                </tr>
                <tr>
                    <td>英文名</td>
                    <td><?php echo $child->en_name_last ?></td>
                </tr>
                <tr>
                    <td>中文姓</td>
                    <td><?php echo $child->cn_name ?></td>
                </tr>
                <tr>
                    <td>中文名</td>
                    <td><?php echo $child->cn_name_last ?></td>
                </tr>
                <tr>
                    <td>拼音姓</td>
                    <td><?php echo $child->pinyin_first ?></td>
                </tr>
                <tr>
                    <td>拼音名</td>
                    <td><?php echo $child->pinyin_last ?></td>
                </tr>
                <tr>
                    <td>孩子性别</td>
                    <td><?php echo ($child->gender == 1) ? "男" : "女";?></td>
                </tr>
                <tr>
                    <td>学生生日</td>
                    <td><?php echo date("Y-m-d" , $child->birthday) ?></td>
                </tr>
                <tr>
                    <td>上学学年</td>
                    <td><?php echo $calender[$child->start_year] ?></td>
                </tr>
                <tr>
                    <td>学生国籍</td>
                    <td><?php echo $configa['countries'][$child->nationality] ?></td>
                </tr>
                <tr>
                    <td>护照号/身份证号 </td>
                    <td><?php echo $child->passportid ?></td>
                </tr>

                <tr>
                    <td>母语</td>
                    <td><?php echo $configa['language'][$child->native_lang] ?></td>
                </tr>
                <tr>
                    <td>其他语言</td>
                    <td><?php echo $configa['language'][$child->other_lang] ?></td>
                </tr>
                <tr>
                    <td>入学日期</td>
                    <td><?php echo $child->start_date ? date('Y-m-d', $child->start_date) : '' ?></td>
                </tr>
                <tr>
                    <td>入学年级 </td>
                    <td><?php echo $configa['grade'][$child->start_grade] ?></td>
                </tr>
                <tr>
                    <td>是否需要学籍 </td>
                    <td><?php echo ($child->require_bus == 1) ? "是" :"否" ?></td>
                </tr>
                <?php if(in_array($child->school_id, array('DS','SLT'))){ ?>
                    <tr>
                        <td>是否有兄弟姐妹在启明星 </td>
                        <td><?php echo ($child->is_siblings == 2) ? "是" :"否" ?></td>
                    </tr>
                    <tr>
                        <td>是否为启明星家长推荐 </td>
                        <td><?php echo ($child->is_recommended == 2) ? "是" :"否" ?></td>
                    </tr>
                <?php } ?>
                <tr>
                    <td>是否为艾毅/启明星员工 </td>
                    <td><?php echo ($item->has_xueji == 1) ? "是" :"否" ?></td>
                </tr>
                <tr>
                    <td>家庭详细住址 </td>
                    <td><?php echo $child->home_address ?></td>
                </tr>

                <tr>
                    <td>学习经历</td>
                    <td colspan=1 rowspan=1>
                        <table class="table table-bordered table-condensed">
                            <tr>
                                <td> 学校名称 </td>
                                <td> 所在城市 </td>
                                <td> 就读时间 </td>
                                <td> 年级 </td>
                                <td> 课程 </td>
                                <td> 教学语言 </td>
                            </tr>
                            <?php foreach(json_decode($child->school_history) as $item){ ?>
                                <tr>
                                    <td><?php echo $item->school ?></td>
                                    <td><?php echo $item->location ?></td>
                                    <td><?php echo $item->studytime ?></td>
                                    <td><?php echo $item->grade ?></td>
                                    <td><?php echo $item->curriculum ?></td>
                                    <td><?php echo $item->language ?></td>
                                </tr>
                            <?php } ?>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td>兄弟姐妹信息</td>
                    <td colspan=1 rowspan=1>
                        <table class="table table-bordered table-condensed">
                            <tr>
                                <td> 姓名 </td>
                                <td> 年龄 </td>
                                <td> 性别 </td>
                                <td> 目前就读学校 </td>
                                <td> 年级 </td>
                            </tr>
                            <?php foreach(json_decode($child->sibling_info) as $sibling){ ?>
                                <tr>
                                    <td><?php echo $sibling->name ?></td>
                                    <td><?php echo $sibling->age ?></td>
                                    <td><?php echo $sibling->gender ?></td>
                                    <td><?php echo $sibling->school ?></td>
                                    <td><?php echo $sibling->grade ?></td>
                                </tr>
                            <?php } ?>
                        </table>
                    </td>
                </tr>
                <?php if($child->father_name){ ?>
                    <tr>
                        <td>父亲名字</td>
                        <td><?php echo $child->father_name ?></td>
                    </tr>
                    <tr>
                        <td>父亲国籍</td>
                        <td><?php echo $configa['countries'][$child->father_nation] ?></td>
                    </tr>
                    <tr>
                        <td>父亲母语</td>
                        <td><?php echo $configa['language'][$child->father_lang] ?></td>
                    </tr>
                    <tr>
                        <td>父亲家庭交流语言</td>
                        <td><?php echo $configa['language'][$child->father_home_lang] ?></td>
                    </tr>
                    <tr>
                        <td>父亲工作单位</td>
                        <td><?php echo $child->father_employer?></td>
                    </tr>
                    <tr>
                        <td>父亲职位</td>
                        <td><?php echo $child->father_position ?></td>
                    </tr>
                    <tr>
                        <td>父亲学历</td>
                        <td><?php echo $configa['education'][$child->father_education] ?></td>
                    </tr>
                    <tr>
                        <td>父亲联系电话</td>
                        <td><?php echo $child->father_phone ?></td>
                    </tr>
                    <tr>
                        <td>父亲邮箱</td>
                        <td><?php echo $child->father_email ?></td>
                    </tr>
                <?php } ?>
                <?php if($child->mother_name){ ?>
                    <tr>
                        <td>母亲名字</td>
                        <td><?php echo $child->mother_name ?></td>
                    </tr>
                    <tr>
                        <td>母亲国籍</td>
                        <td><?php echo $configa['countries'][$child->mother_nation] ?></td>
                    </tr>
                    <tr>
                        <td>母亲母语</td>
                        <td><?php echo $configa['language'][$child->mother_lang] ?></td>
                    </tr>
                    <tr>
                        <td>目前家庭交流语言</td>
                        <td><?php echo $configa['language'][$child->mother_home_lang] ?></td>
                    </tr>
                    <tr>
                        <td>母亲工作单位</td>
                        <td><?php echo $child->mother_employer ?></td>
                    </tr>
                    <tr>
                        <td>母亲职位</td>
                        <td><?php echo $child->mother_position ?></td>
                    </tr>
                    <tr>
                        <td>母亲学历</td>
                        <td><?php echo $configa['education'][$child->mother_education] ?></td>
                    </tr>
                    <tr>
                        <td>母亲联系电话</td>
                        <td><?php echo $child->mother_phone ?></td>
                    </tr>
                    <tr>
                        <td>母亲邮箱</td>
                        <td><?php echo $child->mother_email ?></td>
                    </tr>
                <?php } ?>
                <tr>
                    <td>学习困难</td>
                    <td><?php echo $child->learn_diffculties ?></td>
                </tr>
                <tr>
                    <td>健康问题</td>
                    <td><?php echo $child->medical_conditions ?></td>
                </tr>
                <tr>
                    <td>残疾或障碍</td>
                    <td><?php echo $child->physical_disabilities ?></td>
                </tr>
                <tr>
                    <td>特长</td>
                    <td><?php echo $child->talent ?></td>
                </tr>

                <tr>
                    <td>近两年学业报告</td>
                    <td><?php echo ($child->academic_report) ? "有附件" : "无附件" ?></td>
                </tr>

                <tr>
                    <td>户口本或护照复印件</td>
                    <td><?php echo ($child->persion_copy) ? "有附件" : "无附件" ?></td>
                </tr>
            </table>
        </div>
    </div>
</div>
