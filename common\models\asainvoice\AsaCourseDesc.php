<?php

/**
 * This is the model class for table "ivy_asa_course_desc".
 *
 * The followings are the available columns in table 'ivy_asa_course_desc':
 * @property integer $course_id
 * @property string $brief_cn
 * @property string $brief_en
 * @property string $desc_cn
 * @property string $desc_en
 * @property integer $updated
 * @property integer $updated_userid
 */
class AsaCourseDesc extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_asa_course_desc';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('course_id, updated, updated_userid', 'required'),
			array('course_id, updated, updated_userid', 'numerical', 'integerOnly'=>true),
			array('brief_cn, brief_en, desc_cn, desc_en', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('course_id, brief_cn, brief_en, desc_cn, desc_en, updated, updated_userid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'course_id' => 'Course',
			'brief_cn' => Yii::t('asainvoice', '中文简介'),
			'brief_en' => Yii::t('asainvoice', '英文简介'),
			'desc_cn' => Yii::t('asainvoice', '中文详细介绍'),
			'desc_en' => Yii::t('asainvoice', '英文详细介绍'),
			'updated' => 'Updated',
			'updated_userid' => 'Updated Userid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('course_id',$this->course_id);
		$criteria->compare('brief_cn',$this->brief_cn,true);
		$criteria->compare('brief_en',$this->brief_en,true);
		$criteria->compare('desc_cn',$this->desc_cn,true);
		$criteria->compare('desc_en',$this->desc_en,true);
		$criteria->compare('updated',$this->updated);
		$criteria->compare('updated_userid',$this->updated_userid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AsaCourseDesc the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
