<div id="childinfo">
    <?php $this->widget('ext.userInfo.UserInfo', array('staff'=>$this->staff,'branches'=>$this->getAllBranch()));?>
</div>
<div class="h_a"><?php echo Yii::t("hr", "人事考勤");?></div>
<div class="table_c1">
<table width=100%>
	<colgroup>
		<col class="th" width=180>
		<col width=null>
		<col width=200>
	</colgroup>
	<tbody>
		<tr>
			<td class="level0">
				<?php
                    $profileMenu = array(
                        'leaveApplication'=>array(
                            'label' => Yii::t("hr", "休假"),
                            'url' => array('//user/hr/index', 't'=>'leavelist')
                        ),
                        'toApplication'=>array(
                            'label' => Yii::t("hr", "加班"),
                            'url' => array('//user/hr/index','t'=>'otlist')
                        ),
                        'toCount'=>array(
                            'label' => Yii::t("hr", "统计"),
                            'url' => array('//user/hr/count')
                        ),
                    );                    
					$this->widget('zii.widgets.CMenu', array(
						'id' => 'invoice-type',
						'items' => $profileMenu,
                        'htmlOptions' => array('class'=>'subm'),
                        'activeCssClass' => 'current'                        
                        )
					);
				?>
			</td>
			<td>
                <div id="leave-edit">
                    <ul>
                        
                        <?php 
                            $notification = new Notification();
                            foreach ($data as $k=>$val):
                        ?>
                        <li>
                            <?php
                                $config = $notification->filterConfigs($k);
                                echo sprintf("%s 共( %s )个申请",$config['name'],CHtml::link($val['count'],$config['url']));
                            ?>
                        </li>
                        <?php endforeach;?>
                    </ul>
				</div>
			</td>
		</tr>
	</tbody>
</table>
</div>