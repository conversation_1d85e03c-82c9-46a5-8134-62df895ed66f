<?php

/**
 * model 形式的
 * 将 widget 的 action 请求写在widget结构之内便于管理
 * 参考 http://www.yiiframework.com/wiki/146/how-to-use-a-widget-as-an-action-provider/(没有用这个啊 看 3713 )
 * why do you directly config the actions function of controller oh
 * <AUTHOR>
 * @date 2012-07-25
 */
class feedbackModelAction extends CAction{

	public function run(){
		Yii::import('common.models.survey.*');
		if(Yii::app()->request->isAjaxRequest){
			$json_data['flag'] = 'failed';
			$childid = $this->getController()->getChildId();
			if(Yii::app()->user->checkAccess('adminChild',array("childid"=>$childid)))
			{
				$update_user = Yii::app()->user->id;
				$update_time = time();
				if(empty($update_user) || empty($childid)){
					$json_data['flag'] = 'not_login';
				}else{

					$fb_form = new WSurveyFeedbackForm();
					$fb_form->attributes = $_POST['WSurveyFeedbackForm'];

					$execute_failed = false;
					$fb_model = WSurveyFeedback::model();

					// 获取 家长反馈信息
					$fb_cri = new CDbCriteria();
					$fb_cri->compare('survey_id', $fb_form->survey_id);
					$fb_cri->compare('childid', $fb_form->child_id);
					$fb_cri->compare('status', 0);
					$fb_cri->compare('is_fb', 0);
					$fb_cri->limit = 1;
					$fb_obj = $fb_model->find($fb_cri);

					if(empty($fb_obj)){
						$execute_failed = true;
						$json_data['flag'] = 'already_fb';
					}else{

						$fb_trans= $fb_model->dbConnection->beginTransaction();

						if(is_array($_POST['WSurveyFeedbackOptionForm'])){
							$fbo_flag =false;
							$fbo_obj = new WSurveyFeedbackOption();
							$fbo_obj->fb_id = $fb_obj->id;
							$fbo_obj->survey_id = $fb_obj->survey_id;
							$fbo_obj->classid = $fb_obj->classid;
							$fbo_obj->childid = $fb_obj->childid;
							$fbo_obj->userid = $update_user;
							$fbo_obj->ic_id = 0;
							$fbo_obj->schoolid = $fb_obj->schoolid;
							$fbo_obj->followup = 0;
							foreach ($_POST['WSurveyFeedbackOptionForm'] as $tid => $option) {
								//多选
								if (count($option,1) > 1 ) {
									foreach ($option as $oo) {
										$fbo_obj->setIsNewRecord(true);
										$fbo_obj->id = null;
										$fbo_obj->topic_id = $tid;
										$fbo_obj->attributes = $oo;
										$fbo_flag = $fbo_obj->save();
										if(true != $fbo_flag){
											$fb_trans->rollBack();
											$execute_failed = true;
											break 2;
										}
									}
									// 单选
								}elseif(is_array($option)){
									// 发送生成预交学费MQ
									if ($tid == 635) {
										if (in_array($option['option_answer'], array(1,2))) {
											$classid = 0;
											Yii::import('common.models.invoice.ChildReserve');
											$childReserve = ChildReserve::model()->findByAttributes(array('childid'=>$childid));
											if ($childReserve) {
												$classid = $childReserve->classid;
											}
											$mqData = array(
												'childid' => $childid,
												'classid' => $classid,
												'uid' => $update_user,
												'schoolid' => 'BJ_DS'
											);
											if ($option['option_answer'] == 2) {
												$mqData['schoolid'] = 'BJ_SLT';
											}
											CommonUtils::addProducer('survey', "Invoice.createDepositInvoice", CJSON::encode($mqData), 0);
										}
									}

									$fbo_obj->setIsNewRecord(true);
									$fbo_obj->id = null;
									$fbo_obj->topic_id = $tid;
									$fbo_obj->attributes = $option;
									$fbo_flag = $fbo_obj->save();
									if(true != $fbo_flag){
										$fb_trans->rollBack();
										$execute_failed = true;
										break;
									}
								}
							}
						}

						if(is_array($_POST['WSurveyFeedbackTextForm'])){
							$fbt_flag = false;
							$fbt_obj = new WSurveyFeedbackText();
							$fbt_obj->fb_id = $fb_obj->id;
							$fbt_obj->survey_id = $fb_obj->survey_id;
							$fbt_obj->classid = $fb_obj->classid;
							$fbt_obj->childid = $fb_obj->childid;
							$fbt_obj->userid = $update_user;
							$fbt_obj->ic_id = 0;
							$fbt_obj->schoolid = $fb_obj->schoolid;
							$fbt_obj->rating = 0;
							$fbt_obj->hide2school = 0;
							$fbt_obj->followup = 0;
							foreach ($_POST['WSurveyFeedbackTextForm'] as $tid => $feedback) {
								if(!empty($feedback)){
									$fbt_obj->setIsNewRecord(true);
									$fbt_obj->id = null;
									$fbt_obj->topic_id = $tid;
									$fbt_obj->attributes = $feedback;
									$fbt_flag = $fbt_obj->save();
									if(true != $fbt_flag){
										$fb_trans->rollBack();
										$execute_failed = true;
										break;
									}
								}
							}
						}

						if (false == $execute_failed) {
							// 更改 家长反馈

							$fb_obj->is_fb = 1;
							$fb_obj->fb_user = $update_user;
							$fb_obj->fb_time = $update_time;

							$fb_flag = $fb_obj->save(false);
							if(true != $fb_flag){
								$execute_failed = true;
							}
						}

						if (true == $execute_failed) {
							// 回滚
							$fb_trans->rollback();
						} else {
							$json_data['flag'] = "success";
							// 提交
							$fb_trans->commit();
						}
					}

				}
			}

			echo CJSON::encode($json_data);
		}
	}

}