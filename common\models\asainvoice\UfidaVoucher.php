<?php

/**
 * This is the model class for table "ivy_ufida_voucher".
 *
 * The followings are the available columns in table 'ivy_ufida_voucher':
 * @property integer $id
 * @property integer $month
 * @property integer $voucherid
 * @property string $relevancyid
 * @property integer $timestamp
 */
class UfidaVoucher extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_ufida_voucher';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('relevancyid', 'required'),
			array('month, voucherid, timestamp', 'numerical', 'integerOnly'=>true),
			array('relevancyid', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, month, voucherid, relevancyid, timestamp', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'month' => 'Month',
			'voucherid' => 'Voucherid',
			'relevancyid' => 'Relevancyid',
			'timestamp' => 'Timestamp',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('month',$this->month);
		$criteria->compare('voucherid',$this->voucherid);
		$criteria->compare('relevancyid',$this->relevancyid,true);
		$criteria->compare('timestamp',$this->timestamp);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return UfidaVoucher the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public static function getVoucherid($month)
    {
        $voucherid = 0;
    	$spl = "SELECT max(voucherid) as voucherid FROM `ivy_ufida_voucher` WHERE month=:month";
    	$voucherObj = UfidaVoucher::model()->findBySql($spl, array(":month"=>$month));
    	if ($voucherObj && $voucherObj->voucherid)
    	{
			$voucherid = $voucherObj->voucherid;
    	}

    	return $voucherid + 1;
    }
}
