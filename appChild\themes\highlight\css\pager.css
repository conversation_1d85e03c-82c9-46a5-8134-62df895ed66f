/**
 * CSS styles for CLinkPager.
 *
 * <AUTHOR> <<EMAIL>>
 * @link http://www.yiiframework.com/
 * @copyright Copyright &copy; 2008-2010 Yii Software LLC
 * @license http://www.yiiframework.com/license/
 * @version $Id: pager.css 1678 2010-01-07 21:02:00Z qiang.xue $
 * @since 1.0
 */

ul.yiiPager
{
	font-size:11px;
	border:0;
	margin:0;
	padding:0;
	line-height:100%;
	display:inline;
}

ul.yiiPager li
{
	display:inline;
	border: 1px solid #CDC3B7;
	border-radius: 4px;
	background: #F5F1E6;
	padding: 2px;
}

ul.yiiPager li.selected
{
	background:#F48204;
	border-color: #F48204;
}

ul.yiiPager li:hover
{
	border-color: #F48204;
}
ul.yiiPager li.hidden:hover
{
	border-color: #CDC3B7;
}

ul.yiiPager a:link,
ul.yiiPager a:visited
{
	/*border:1px solid #CDC3B7;*/
	color:#FF6A00;
	padding:1px 6px;
	text-decoration:none;
}

ul.yiiPager .page a
{
	font-weight:normal;
	color: #FF6A00;
}

ul.yiiPager a:hover
{
	/*border:solid 1px #F48204;*/
}

ul.yiiPager .selected a
{

	color:#FFFFFF;
}

ul.yiiPager .hidden a
{
	/*border:solid 1px #bbb;*/
	color:#888888;
}

/**
 * Hide first and last buttons by default.
 */
ul.yiiPager .first,
ul.yiiPager .last
{
	display:none;
}