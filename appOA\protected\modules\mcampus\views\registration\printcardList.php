<?php
 if ($this->branchObj->type == 50 || $this->branchId == 'BJ_QFF') {
 $borderNone='borderNone';
 $logoWdith='width:200px';
} else {
	$borderNone='ivy';
	$logoWdith='height:60px';

} 	
?>
<style>
	.border{
		border:1px solid #ccc;
		border-radius:5px
	}
	.pb5{
		padding-bottom: 15px
	}
	.ptintImg{
		height: 120px
	}
	.borderNone{
		border:1px solid #ccc;
		background: url(http://m2.files.ivykids.cn/cloud01-file-8025768FtLJ88p3PQvUtBNeUSKGQPXQBynO.jpg) no-repeat;
		background-size:100% 100%;
		/* background:url('http://m2.files.ivykids.cn/cloud01-file-8025768FhElG0oo8VA01MzYMy9Wa9jXD4YB.jpg') no-repeat 100% 100% */
	}
	.ivy{
		border:1px solid #ccc;
		background: url(http://m2.files.ivykids.cn/cloud01-file-8025768FqoRUgyX5mkN_M9pWPUH9fEAKZvK.jpg) no-repeat;
		background-size:100% 100%;
	}
	.borderRight{
		border-right:1px solid #ccc;
	}
	.padd0{
		padding: 0 !important
	}
	[v-cloak] {
		display: none;
	}
	.h100{
		height: 100%
	}
	.chilidPto{
		max-height:150px
	}
	.odd{
		padding-top:10px;
		border-top: 1px solid #ddd;
	}
	.classWid{
		display: inline-block;
		width:20%;
		margin-left: 0!important;
		margin-bottom:10px
	}
	.pl0{
		padding-left: 0
	}
	.relative{
		position: relative;
	}
	.absolute{
		position: absolute;
    bottom: 15px;
    left: 15px;
    font-size: 16px;
    color: #fff;
	}
	.datalist{
		float: left;
		width: 50%;
		margin-top:10px;
	}
	.datalist div{
		font-size:12px
	}
	.list{
		width:120px;
		margin:0 auto;
	}
</style>
<div class="container-fluid">
	<ol class="breadcrumb">
	    <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
	    <li><?php echo CHtml::link(Yii::t('site', '新生注册'), array('//mcampus/registration/index')) ?></li>
		<li class="active"><?php echo Yii::t('site','打印接送卡') ?></li>
	</ol>
	<div class="row">
		<div class="col-md-2 col-sm-2">
			<ul class="nav nav-pills nav-stacked text-left background-gray" id="pageCategory">
                <?php foreach ($this->getSubMenu() as $val){ ?>
                    <li class="<?php echo ($this->getAction()->getId() == $val['url']) ? "active " : ""; ?>" ><a href="<?php echo $this->createUrlReg($val['url']) ?>"><?php echo $val['label'] ?></a></li>
                <?php } ?>
            </ul>
		</div>
		<div class="col-md-10 col-sm-10" id='print' v-cloak>
		<form action="<?php echo $this->createUrlReg('Printcard', array('branchId' => $this->branchId,'yid' => $this->yid)); ?>" method="post">
			<div>
				<div class="form-inline" >
					<div class="btn-group mb15 wid"><button type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" class="btn btn-default dropdown-toggle  wid">{{startYearList[yid]}} <span class="caret"></span></button>
						<ul class="dropdown-menu wid">
							<li v-for='(data,index) in YearList'>
								<a :href ="'<?php echo $this->createUrl('/mcampus/registration/printcardList', array('branchId' => $this->branchId)); ?>&yid='+data.id+''">{{data.title}} </a>
							</li>
						</ul>
					</div>
					<button class="btn btn-primary pull-right">打印</button>
					<!-- <button type="submit" class="btn btn-default pull-right mr15" @click='printModel()'>上传打印模板</button> -->
				</div>
			</div>
			<p class="mb15">
				<template v-for='(classdata,key,index) in classData'>
					<label class="checkbox-inline classWid">
					    <input type="checkbox" id="inlineCheckbox1" :value="key" @click='pritData(key,$event)'>{{classdata}}
					</label>
				</template>
			</p>
			<div class="col-md-12 padd0" >
			<!-- <div class="panel panel-default">
				<div class="panel-body"> -->
					<div class="col-md-6 pb5 odd" v-for='(list,index) in arrChild'>
						<div class="">
							<label class="padd0  col-md-12">
								<p><input type="checkbox" :value='list.childId' name='childid[]' checked></p>

								<div class="<?php echo $borderNone;?> padd0" style="height:400px;">
									<div class="col-md-6 borderRight padd0 h100">
									<div class="">
										<div class="datalist" v-for='(parentlist,idx) in list.parent' :class="(idx + 1) % 2==0?'pl0':''">
											<div class='list'>
											<img :src="parentlist.photo" alt="" class="ptintImg">
											<p class="clearfix"></p>
											<div><?php echo Yii::t('global','Name') ?><?php echo Yii::t('global',': ') ?>{{parentlist.name}}</div>
											<div><?php echo Yii::t('asa','Phone') ?><?php echo Yii::t('global',': ') ?>{{Trim(parentlist.tel)}}</div>
											<div><?php echo Yii::t('site','Relationship') ?><?php echo Yii::t('global',': ') ?>{{parentlist.relation}}</div>
											</div>
										</div>
									</div>
									<div class="clearfix"></div>
									</div>
									<div class="col-md-6 pt10 h100 text-center relative">
										<p class="mt15 pt10"><img :src="logo" alt="" style="<?php echo $logoWdith;?>;"></p>
										<p class="mt15"><img :src="list.photo" class="center-block chilidPto " alt=""></p>
										<p class="text-center pt10"><?php echo Yii::t('global','Name') ?><?php echo Yii::t('global',': ') ?>{{list.childName}}</p>
										<div class='text-left absolute'>
											  <div>{{yearData}}</div>
											  <div>{{branchTitle}}</div>
										</div>
									</div>
									<div class="clearfix"></div>
								</div>
							</label>
						</div>
					</div>
				<!-- </div>
			</div> -->
			<!-- <div class="modal fade bs-example-modal-lg" id="printModel" tabindex="-1" role="dialog" data-backdrop="static">
				<div class="modal-dialog">
					<div class="modal-content">
						<div class="modal-header">
							<button type="button" class="close" data-dismiss="modal" aria-hidden="true">
							&times;
						</button>
						<h4 class="modal-title" id="myModalLabel">
							编辑
						</h4>
						</div>
						<div class="modal-body">
							查看
						</div>
						<div class="modal-footer">
					        <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
					        <button type="button" class="btn btn-primary">提交</button>
					    </div>
					</div>
				</div>
			</div> -->
		</div>
		<!-- <div v-else>
			<div class="alert alert-warning" role="alert">暂无数据</div>
		</div> -->
		</form>
		</div>
	</div>
</div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
	//var data = <?php echo json_encode($data) ?>;//班级 孩子数据
	var logo = '<?php echo $logo ?>';
	var startYearList = <?php echo json_encode($startYearList) ?>;//学年
	var yid = <?php echo $yid ?>;
	var classData = <?php echo json_encode($classData) ?>;//班级
	var year = <?php echo json_encode($year) ?>;//班级
	var branchTitle = <?php echo json_encode($branchTitle) ?>;//班级

	function sortKey(array, key) {
        return array.sort(function(a, b) {
            var x = parseInt(a[key])
            var y = parseInt(b[key]);
            return((x > y) ? -1 : (x < y) ? 1 : 0)
        })
    }
	var print = new Vue({
		el: "#print",
		data: {
			datas:{},
			startYearList:startYearList,
			yid:yid,
			YearList:'',
			classData:classData,
			printchild:{},
			arrChild:[],
			logo:logo,
			yearData:year,
			branchTitle:branchTitle
		},
		created: function() {
		
			var newcalendarArr = []
            for(key in startYearList) {
                var calendarArrkey = {}
                calendarArrkey.id = parseInt(key)
                calendarArrkey.title = startYearList[key]
                newcalendarArr.push(calendarArrkey)
            }
            this.YearList = sortKey(newcalendarArr, 'id')
		},
		computed: {},
		methods: {
			Trim(str) { 
				var is_global='g'
				var result;
				result = str.replace(/(^\s+)|(\s+$)/g,"");
				if(is_global.toLowerCase()=="g")
				{
				result = result.replace(/\s/g,"");
				}
			  var tel=result.replace("+86",'')
				return tel;
		    },
			pritData(id,e){
				var checked=e.target.checked
				if(checked){
					if(print.datas[id]){
						 for(key in print.datas[id]){
			        		Vue.set(print.datas[id][key], 'childId', key)
					        print.arrChild.push(print.datas[id][key])
			        	}
					}else{
						$.ajax({
					        url:'<?php echo $this->createUrlReg('ClassCard'); ?>',
					        type: 'post',
					        dataType:'json',
					        data:{
					        	classid:id
					        },
					        success:function(data){
	                            print.datas[id.toString()] = data.data
					        	for(key in data.data){

					        		Vue.set(data.data[key], 'childId', key)
					        		print.arrChild.push(data.data[key])
					        	}
					        },error:function(data){
					             resultTip({error: 'warning', msg: '请求错误'});
					        }
					    })
					}
				}else{
			        for(key in print.datas[id]){
		        		for(var i=0;i<print.arrChild.length;i++){
		        			if(key==print.arrChild[i].childId){
		        				print.arrChild.splice(i,1);
		        			}
		        		}
		        	}
				}
				
			}
		},
	})  
</script>