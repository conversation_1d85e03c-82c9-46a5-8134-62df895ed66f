<?php

/**
 * This is the model class for table "ivy_service".
 *
 * The followings are the available columns in table 'ivy_service':
 * @property integer $id
 * @property integer $service_id
 * @property integer $school_id
 * @property string $service_title
 * @property string $start_time
 * @property string $end_time
 * @property integer $status
 * @property integer $vendor_status
 * @property integer $created_by
 * @property integer $created_at
 * @property integer $updated_by
 * @property integer $updated_at
 * @property integer $vendor_by
 * @property integer $vendor_at
 */


class Service extends CActiveRecord
{
	const IT_SUPPORT = 1;
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_service';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('service_id, service_title, status, start_time, end_time, created_by, created_at, updated_by, updated_at', 'required'),
			array('service_id, status, vendor_status, created_by, created_at, updated_by, updated_at, vendor_by, vendor_at', 'numerical', 'integerOnly' => true),
			array('service_title, school_id', 'length', 'max' => 255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, service_id, school_id, service_title, status, start_time, end_time, vendor_status, created_by, created_at, updated_by, updated_at, vendor_by, vendor_at', 'safe', 'on' => 'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'serviceField' => array(self::HAS_MANY, 'ServiceField', array('sid' => 'id')),
            'serviceExpense' => array(self::HAS_MANY, 'ServiceExpense', array('sid' => 'id')),
        );
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'service_id' => 'Service',
			'school_id' => 'School Id',
			'service_title' => 'Service Title',
			'status' => 'Status',
            'start_time' => 'Star Time',
            'end_time' => 'End Time',
            'vendor_status' => 'Vendor Status',
			'created_by' => 'Created By',
			'created_at' => 'Created At',
			'updated_by' => 'Updated By',
			'updated_at' => 'Updated At',
			'vendor_by' => 'Vendor By',
			'vendor_at' => 'Vendor At',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria = new CDbCriteria;

		$criteria->compare('id', $this->id);
		$criteria->compare('service_id', $this->service_id);
		$criteria->compare('school_id', $this->school_id, true);
		$criteria->compare('service_title', $this->service_title, true);
		$criteria->compare('status', $this->status);
		$criteria->compare('start_time', $this->status);
		$criteria->compare('end_time', $this->status);
		$criteria->compare('vendor_status', $this->vendor_status);
		$criteria->compare('created_by', $this->created_by);
		$criteria->compare('created_at', $this->created_at);
		$criteria->compare('updated_by', $this->updated_by);
		$criteria->compare('updated_at', $this->updated_at);
		$criteria->compare('vendor_by', $this->vendor_by);
		$criteria->compare('vendor_at', $this->vendor_at);

		return new CActiveDataProvider($this, array(
			'criteria' => $criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return Service the static model class
	 */
	public static function model($className = __CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * 根据学校ID获取配置项
	 * @param string $schoolId
	 * @return array $schoolConfig
	 */
	public static function getSchoolService($schoolId)
	{
		$config = array(
			'BJ_OE' => array(self::IT_SUPPORT),
			'BJ_OG' => array(self::IT_SUPPORT),
			'BJ_IASLT' => array(self::IT_SUPPORT),
			'BJ_CP' => array(self::IT_SUPPORT),
			'BJ_XHL' => array(self::IT_SUPPORT),
		);
		return $config[$schoolId];
	}

	/**
	 * 获取所有 Service 配置
	 * @return array $serviceInfo
	 */
	public static function getServiceInfo()
	{
		$serviceInfo = array(
			self::IT_SUPPORT => array(
				'title' => 'IT 现场支持&IT SUPPORT',
				'targets' => array('BJ_OE', 'BJ_OG', 'BJ_IASLT', 'BJ_CP', 'BJ_XHL'),
				'manager' => array(5), // 管理员
				'vendor' => array(8003166), // 接收者
				'requestFields' => array(
					1 => array(
						"id" => 1,
						"title" => "开始日期",
						"type" => "timestamp",
						"required" => true
					),
					2 => array(
						"id" => 2,
						"title" => "结束日期",
						"type" => "timestamp",
						"required" => true
					),
					3 => array(
						"id" => 3,
						"title" => "备注",
						"type" => "text",
						"required" => false
					),
				),
				'expenseFields' => array(
					1 => array(
						"id" => 1,
						"title" => "去程费用",
						"type" => "float",
						"required" => false
					),
					2 => array(
						"id" => 2,
						"title" => "返程费用",
						"type" => "float",
						"required" => false
					),
					3 => array(
						"id" => 3,
						"title" => "住宿费用",
						"type" => "float",
						"required" => false
					),
					4 => array(
						"id" => 4,
						"title" => "备注",
						"type" => "text",
						"required" => false
					),
				),
			),
		);
		return $serviceInfo;
	}

    /**
     * 判断
     * @param string $schoolId
     * @return array $schoolConfig
     */
    public static function judge($config = array(), $service_data = array(), $manager = array())
    {
        $data = array('status' => 1, 'message' => '成功');
        if($config && $service_data) {
            foreach ($config as $key => $judge) {
                if ($judge['required'] == 1 && $data['status'] == 1) {
                    if (!$service_data[$key]) {
                        $data = array('status' => 0, 'message' => $judge['title'] . '不能为空');
                    }
                }

                if ($judge['type'] == 'timestamp' && $manager && $data['status'] == 1) {
                    $strtimes = strtotime($service_data[$key]);
                    if(!in_array(Yii::app()->user->id, $manager)){
                        $todaystart = strtotime(date("Y-m-d",time()));
                        if($strtimes < $todaystart){
                            $data = array('status' => 0, 'message' => '权限不够，请联系IT');
                        }
                    }
                }
            }
        }else{
            $data = array('status' => 0, 'message' => '参数错误');
        }

        return $data;
    }

    /**
     * 判断
     * @param string $schoolId
     * @return array $schoolConfig
     */
    public static function ruleJudge($configStatus = array(), $fileld_content = '')
    {
        $status = 1;
        $message = '成功';
        $date = $fileld_content;
        if($configStatus && $date) {
            if($configStatus['type'] == 'timestamp'){
                $date = strtotime($date);
                if(!$date || !is_int($date)){
                    $status = 0;
                    $message = $status['title'] . '格式错误';
                }
            }
            if($configStatus['type'] == 'float'){
                if(!$date || !(float)$date){
                    $status = 0;
                    $message = $status['title'] . '格式错误';
                }
            }
        }

        return array(
            'status' => $status,
            'message' => $message,
            'date' => $date,
        );
    }
}
