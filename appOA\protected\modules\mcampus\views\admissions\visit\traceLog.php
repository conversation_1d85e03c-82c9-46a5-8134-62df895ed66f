<?php 
    $form=$this->beginWidget('CActiveForm', array(
        'id'=>'confirmed-visit-grid',
        'enableAjaxValidation'=>false,
        'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
    ));
    $model = new VisitsTraceLog();
?>
<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
    <h4 class="modal-title"><?php echo $modalTitle;?></h4>
</div>
<div class="modal-body">
    <div class="col-md-12">
        <table class="table table-bordered">
            <colgroup>
                <col width="30">
                <col width="100">
            </colgroup>
            <tr>
                <td>家长姓名：</td>
                <td><?php echo $basicInfo->parent_name;?></td>
            </tr>        
            <tr>
                <td>电话：</td>
                <td><?php echo $basicInfo->tel;?></td>
            </tr>        
            <tr>
                <td>地址：</td>
                <td><?php echo $basicInfo->address;?></td>
            </tr>
        </table>
        <?php 
            if ($records) {
                echo '<table class="table"><tr><td>预约日期</td><td>预约时间</td><td>来访时间</td></tr>';
                foreach ($records as $record){
                    if (!$record->appointment_date) {
                        continue;
                    }
                    $appointment_date = $record->appointment_date ? date('Y/m/d', $record->appointment_date) : '';
                    $visit_timestamp = $record->visit_timestamp ? date('Y/m/d', $record->visit_timestamp) : '';
                    echo '<tr><td>'.$appointment_date.'</td><td>'.$record->appointment_time.'</td><td>'.$visit_timestamp.'</td></tr>';
                }
                echo '</table>';
            }
        ?>
        <?php 
            if ($traceLogs) {
                foreach ($traceLogs as $traceLog) {
                    $user = User::model()->findByPk($traceLog->update_user);
                    $branch = Branch::model()->findByPk($traceLog->schoolid);
                    echo '<blockquote><p>'.
                        $traceLog->content.
                        '</p><footer>'.
                        $user->getName().
                        '，'. 
                        $branch->title.
                        '，'.
                        date('Y/m/d H:i', $traceLog->update_timestamp).
                        '</footer></blockquote>';
                }
            }
         ?>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($basicInfo,'status'); ?></label>
        <div class="col-xs-10">
            <?php echo $form->radioButtonList($basicInfo,'status',IvyVisitsBasicInfo::status(),array('template'=>'<label class="radio-inline">{input}{label}</label>','separator'=>'')); ?>
        </div>
    </div>
    
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'content'); ?></label>
        <div class="col-xs-10">
            <?php echo $form->textArea($model,'content',array('class'=>'form-control','rows'=>3)); ?>
        </div>
    </div>


    <?php echo $form->hiddenField($model,'basic_id',array('value'=>$basicId)); ?>
    <?php echo $form->hiddenField($model,'visit_id',array('value'=>$visitId)); ?>
</div>
<div class="modal-footer">
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
    <button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
</div>
</div>

<?php $this->endWidget(); ?>
<script>
    // 日期插件
    $('.datepicker').datepicker({'dateFormat':'yy-mm-dd'});
    // 回调：添加成功
    function cbVisit() {
        $('#modal').modal('hide');
        $.fn.yiiGridView.update('confirmed-visit-grid');
    }
    // 自动添加状态信息
    $("#IvyVisitsBasicInfo_status input").bind("click", function () {
        var status = $(this).val();
        if (status == 10) {
            $("#VisitsTraceLog_content").text('');
        }
        if (status == 20) {
            $("#VisitsTraceLog_content").text('孩子已过期。');
        }
        if (status == 30) {
            $("#VisitsTraceLog_content").text('孩子已注册。');
        }
        if (status == 40) {
            $("#VisitsTraceLog_content").text('孩子拒绝联系。');
        }
        if (status == 98) {
            $("#VisitsTraceLog_content").text('孩子已存在。');
        }
    });
</script>