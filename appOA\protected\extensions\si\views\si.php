
<div class="animate">
    <div class="si-logo si-logo-off"><span></span></div>
</div>

<div class="si-tips hide">
    <div class='flex baseline' style='height:17px'>
        <div class='flex1'></div>
        <?php if (Yii::app()->user->checkAccess('o_A_Adm_Quote')):?>
        <a href="<?php echo Yii::app()->getController()->createUrl('//mcampus/quote')?>" target="_blank" class="close color6 blueHover" style="margin-right: 12px; font-size: 16px;opacity:1"><span class="glyphicon glyphicon-cog" title="<?php echo Yii::t('management', 'Management');?>"></span></a>
        <?php endif;?>
        <span class='color6 closed blueHover' onclick="si_show()">×</span>
    </div>
    <div class='flex baseline mt20'>
        <span class='iconfont icon-quote_fill quote_fill'></span>
        <div class='flex1 ml16'>
            <div class='font16 color3  ' id="well_know_saying" style="font-style: italic;"></div>
            <div class='font12 color6 text-right mt4'> — <span id="celebrity"></span></div>
        </div>
    </div>
    <div class='mt24 text-center'>
        <span class='goodIcon'>
            <div class="good" onclick="theLike()"></div>
            <span id="well_know_like" class='badge'>0</span>
        </span>
    </div>
    <div class='linkUser'>
        <div class='user'></div>
    </div>
    <script>
        $(function () {
            $('body').tooltip({
            selector: '[data-toggle="tooltip"]'
            });
        })
        var tipsGood = <?php echo json_encode($tips);?>;
        tipsGood = JSON.parse(tipsGood);
        var randomNum = Math.floor(Math.random() * (tipsGood.length - 0)) + 0;
        $('#well_know_saying').html(tipsGood[randomNum].content)
        $('#celebrity').text(tipsGood[randomNum].author)
        $('#well_know_like').text(tipsGood[randomNum].like_count)
        if(tipsGood[randomNum].like_count==0){
            $('#well_know_like').hide()
        }
        var like_id = tipsGood[randomNum].log_id
        if(tipsGood[0].I_like){
            $('.good').html("<span class='iconfont icon-good_fill2 goodLike'></span>")
            $('.good').addClass('blueBg')
        }else{
            $('.good').addClass('blueBgHover')
            $('.good').html("<span class='iconfont icon-good2'></span>")
        }
        let user=tipsGood[0].like_users
        if(user.length==0){
            $('.linkUser').hide()
        }else{
            $('.linkUser').show()
        }
        showAll()
        function showAll(){
            $('.user').html('')
            var html=''
            for (var i = 0; i < user.length; i ++) {
                html += "<img src="+user[i].photoUrl+" class='img28' data-toggle='tooltip' data-placement='top' title='"+user[i]["name"]+"'>";
            }
            $('.user').html(html)
        }
        function theLike()
    {
        if(tipsGood[0].I_like){
            resultTip({
                error: 'warning',
                msg: '<?php echo Yii::t('quote','Already liked!');?>'
            });
            return
        }
        $.ajax({
            url: '/mcampus/quote/setSiLike',
            type: 'POST',
            dataType: 'json',
            data: {
                'id': like_id
            },
            success: function(data) {
                if (data.state == 'success') {
                    $('#well_know_like').text(user.length+1)
                    $('#well_know_like').show()
                    $('.good').removeClass('blueBgHover')
                    $('.good').addClass('blueBg')
                    $('.good').html("<span class='iconfont icon-good_fill2 goodLike'></span>")
                    $('.linkUser').show()
                    const good = document.querySelector('.si-tips .good span');
                    good.style.setProperty('--animate-duration', '0.6s');
                    animateCSS('.si-tips .good span', 'fadeOutUp')
                    const newItemDiv = "<img src="+data.data.user.photoUrl+" class='img28 addImg' data-toggle='tooltip' data-placement='top' title='"+data.data.user["name"]+"'>";
                    $('.user').append(newItemDiv);
                    const element = document.querySelector('.addImg');
                    element.style.setProperty('--animate-delay', '0.1s'); 
                    element.style.setProperty('--animate-duration', '0.6s');
                    animateCSS('.addImg', 'zoomIn')
                }else{
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                }
            }
        })
    }
    </script>
    <div class="mask"></div>
</div>

<?php
$cookie_key_today = 'si-opened-01';
$cookie_key_weekend = 'si-opened-02';
$weekday = date('w');
$expire_01 = strtotime('today') + 86399;
$expire_02 = strtotime('next sunday') + 86399;

if ( (in_array($weekday, array(1, 5)) && !isset($_COOKIE[$cookie_key_today])) || (in_array($weekday, array(0, 2, 3, 4, 6)) && !isset($_COOKIE[$cookie_key_weekend])) ):?>
<script>
    setTimeout(function () {
        si_show()
    }, 2100)

    setTimeout(function () {
        const d = new Date()
        d.setTime(<?php echo $expire_01*1000?>)
        document.cookie = "<?php echo $cookie_key_today;?>=1; expires=" + d + "; path=/";
        d.setTime(<?php echo $expire_02*1000?>)
        document.cookie = "<?php echo $cookie_key_weekend;?>=1; expires=" + d + "; path=/";
    }, 3000)
</script>
<?php endif;?>
<style>
.si-tips .good span {
  --animate-duration: 0.2s;
}
</style>