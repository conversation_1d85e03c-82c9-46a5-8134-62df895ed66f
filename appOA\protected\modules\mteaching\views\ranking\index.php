<?php if (!$show):?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Teaching Tasks'), array('//mteaching/default/index'));?></li>
        <li><?php echo Yii::t('newDS', 'G6-12 Internal Grade Analytics');?></li>
    </ol>
    <div class="row">
        <div class="col-md-12">
            <div class="alert alert-warning" role="alert">Sorry, grade analytics is for secondary school only.</div>
        </div>
    </div>
</div>
<?php else:?>
<div class="container-fluid" id="container">
    <transition name="fade">
        <div v-if="isLoading" class="loading"></div>
    </transition>
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Teaching Tasks'), array('//mteaching/default/index'));?></li>
        <li><?php echo Yii::t('newDS', 'G6-12 Internal Grade Analytics');?></li>
    </ol>
    <div class="row" v-if="schoolYear">
        <div class="col-md-3 col-sm-12">
            <div class="mb20">
                <select class="form-control" v-model="year">
                    <option v-for="(item, index) in schoolYear" :key="index" :value="item.id">{{item.title}}</option>
                </select>
            </div>
            <div>
                <div class="mb16 font-01">Grade</div>
                <button type="button"
                        class="btn btn-default btn-sm mr15 mb15"
                        :class="{ 'btn-primary': item == grade }"
                        v-for="(item, index) in grades"
                        :key="index"
                        @click="btnGrade(item)">Grade {{item}}</button>
            </div>
            <div>
                <div class="font-01">
                    <label><input type="checkbox" v-model="allPeriods" id="all_periods" @change="drawTable"> Score Periods</label>
                </div>
                <label v-for="(item, index) in periods" :key="index" class="mr15">
                    <input type="checkbox" :value="item" v-model="period" @change="drawTable">
                    <span>P</span>{{item}}
                </label>
                <label v-for="(item, index) in finalPeriods" :key="index" class="mr15">
                    <input type="checkbox" :value="item" v-model="finalPeriod" @change="drawTable">
                    {{item}}
                </label>
            </div>
            <div v-if="mainData">
                <div class="mt16 mb10 block-01 font-01 title-01">
                    <div>
                        <label><input type="checkbox" v-model="allCourse" id="check_all" @change="drawTable"> All Courses</label>
                    </div>
                    <div>Credit</div>
                </div>
                <div class="block-01" v-for="(item, index, i) in mainData.courses" :key="index">
                    <div class="mb10">
                        <label><input type="checkbox" v-model="calcCourses" :value="index" @change="drawTable"> {{item.title}}</label>
                    </div>
                    <div><input class="form-control" type="text" size="3" v-model="calcWeight[index]" @keyup="drawTable"></div>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-sm-12 block-02" v-if="mainData && mainData.courses">
            <div class="font-01 mb16" v-if="showMapping1 || showMapping2">Score Mapping</div>
            <div class="mb24" v-if="showMapping1">
                <div class="block-03">
                    <div class="mb16">Courses scored using ABCD: <span v-for="(title, index) in mapping1Data" :key="index" class="tag-01">{{title}}</span></div>
                    <div class="row">
                        <div class="col-md-3" v-for="(item, index) in mapping1" :key="index">
                            {{index}}: <input type="text" v-model="mapping1[index]" size="3" class="form-control ml8" disabled style="width: 80px;display: inline-block;">
                        </div>
                    </div>
                </div>
            </div>
            <div class="row" v-if="showMapping2">
                <div v-for="(item, index) in mapping2" :key="index" class="col-md-12 mb16">
                    <div style="display: inline-block;width: 38px;">{{index}}:</div>
                    <label class="ml16"><input type="radio" :name="`m2_${index}`" value="0" v-model="mapping2[index]" disabled> Count as 0</label>
                    <label class="ml24"><input type="radio" :name="`m2_${index}`" value="--" v-model="mapping2[index]" disabled> Exclude</label>
                </div>
            </div>
            <hr v-if="showMapping1 || showMapping2">
            <div class="font-01 mb16">Score Analytics</div>
            <div v-if="mainData && mainData.courses">
                <div class="block-01 mb16">
                    <button class="btn btn-primary" @click="openTop15">Star Tag (Top 15%)</button>
                    <button class="btn btn-default" @click="exportExcel">Export</button>
                </div>
                <div>
                    <table class="table-01">
                        <thead>
                            <tr>
                                <th style="width: 80px;">Rank</th>
                                <th>Student</th>
                                <th style="width: 100px;">Total</th>
                                <th style="width: 100px;">Average</th>
                                <th style="width: 100px;"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(item, index) in tableData" :key="index">
                                <td class="font-03">{{index+1}}</td>
                                <td>
                                    <div class="info-01">
                                        <div>
                                            <img :src="mainData.childInfo[item.id].avatar" alt="">
                                        </div>
                                        <div class="ml8">
                                            <div class="font-01 mb5">
                                                {{mainData.childInfo[item.id].name}}
                                                <button type="button" class="btn btn-warning btn-xs" v-if="mainData.childInfo[item.id].star" style="width: 20px;height: 18px;padding: 0 3px;">
                                                    <span class="glyphicon glyphicon-star" aria-hidden="true"></span>
                                                </button>
                                            </div>
                                            <div class="font-02">
                                                {{mainData.childInfo[item.id].overClass}}
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="font-03">
                                    <a href="javascript:;" @click="showDetail(item.id, item.total, item.count)">{{item.total}}/{{item.count}}</a>
                                </td>
                                <td class="font-03">{{item.average}}</td>
                                <td>
                                    <div v-if="item.top15" class="info-02">Top 15%</div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div v-else-if="mainData" class="col-md-9 col-sm-12"><div class="alert alert-warning">No scores found in this school year.</div></div>
        <div class="col-md-3 col-sm-12" v-if="mainData && mainData.courses">
            <div v-if="mainData && pieData1.length<2 && pieData2.length<2">Only one class in this grade, no distribution charts displayed.</div>
            <div v-else>
                <div id="chart_pie1" style="width: 100%;height: 290px;"></div>
                <div id="chart_pie2" style="width: 100%;height: 290px;"></div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="score_modal">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span></button>
                    <h4 class="modal-title">Quantitative Achievement Summary</h4>
                </div>
                <div class="modal-body" v-if="childDetail">
                    <div class="block-01 mb24">
                        <div class="info-01">
                            <div>
                                <img :src="childDetail.child.avatar" alt="">
                            </div>
                            <div class="ml8">
                                <div class="font-01 mb5">
                                    {{childDetail.child.name}}
                                    <button type="button" class="btn btn-warning btn-xs" v-if="childDetail.child.star" style="width: 20px;height: 18px;padding: 0 3px;">
                                        <span class="glyphicon glyphicon-star" aria-hidden="true"></span>
                                    </button>
                                </div>
                                <div class="font-02">
                                    {{childDetail.child.overClass}}
                                </div>
                            </div>
                        </div>
                        <div class="font-03">
                            Average: {{childDetail.average}} = {{childDetail.total}}/{{childDetail.count}}
                        </div>
                    </div>
                    <div>
                        <table class="table table-bordered table-02">
                            <thead>
                            <tr>
                                <th rowspan="2">Course Title</th>
                                <th rowspan="2" style="width: 50px;">Criteria</th>
                                <th colspan="4" style="width: 150px;">Reporting Period</th>
                                <th rowspan="2" style="width: 130px;">Final Criteria Score</th>
                                <th rowspan="2" style="width: 135px;">Year-End Final Grade</th>
                            </tr>
                            <tr>
                                <th style="width: 50px;">1</th>
                                <th style="width: 50px;">2</th>
                                <th style="width: 50px;">3</th>
                                <th style="width: 50px;">4</th>
                            </tr>
                            </thead>
                            <tbody v-for="(item, index) in childDetail.score" :key="index">
                                <tr v-for="(_item, _index) in (item.score.periods[1] || item.score.periods[0])" :key="_index">
                                    <td :rowspan="(item.score.periods[1] || item.score.periods[0]).length" style="text-align: left;" v-if="_index==0">{{item.title}}</td>
                                    <td>{{Object.keys(mapping1)[_index]}}</td>
                                    <td>{{item.score.periods[0] ? item.score.periods[0][_index] : '--'}}</td>
                                    <td>{{item.score.periods[1] ? item.score.periods[1][_index] : '--'}}</td>
                                    <td>{{item.score.periods[2] ? item.score.periods[2][_index] : '--'}}</td>
                                    <td>{{item.score.periods[3] ? item.score.periods[3][_index] : '--'}}</td>
                                    <td>{{item.score.final ? item.score.final[0][_index] : '--'}}</td>
                                    <td :rowspan="item.score.periods[0].length" v-if="_index==0">{{item.score.final ? item.score.final[1] : '--'}}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="top15_modal">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span></button>
                    <h4 class="modal-title">Star Tag Top15%</h4>
                </div>
                <div class="modal-body" v-if="tableData">
                    <div class="alert alert-warning" role="alert">
                        The following grade analytics are calculated based on the current configuration, and the top 15% of student labels will be updated.
                    </div>
                    <div v-for="(item, index) in tableData" :key="index">
                        <div class="info-01 mb16" v-if="item.top15">
                            <div>
                                <img :src="mainData.childInfo[item.id].avatar" alt="">
                            </div>
                            <div class="ml8">
                                <div class="font-01 mb5">
                                    {{mainData.childInfo[item.id].name}}
                                    <button type="button" class="btn btn-warning btn-xs" v-if="mainData.childInfo[item.id].star" style="width: 20px;height: 18px;padding: 0 3px;">
                                        <span class="glyphicon glyphicon-star" aria-hidden="true"></span>
                                    </button>
                                </div>
                                <div class="font-02">
                                    {{mainData.childInfo[item.id].overClass}}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" @click="starTag">OK</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    new Vue({
        el: "#container",
        data: {
            isLoading: null,
            grades: [6, 7, 8, 9, 10, 11, 12],
            periods: [1, 2, 3, 4],
            finalPeriods: ['Final Criteria Score', 'Year-End Final Grade'],
            schoolYear: [],
            year: '',
            grade: 0,
            period: [],
            finalPeriod: [],
            mapping1: {
                'A': 7,
                'B': 5,
                'C': 3,
                'D': 1
            },
            mapping2: {
                'NA': '--',
                'NE': '--',
                '-': '--'
            },
            mainData: null,
            tableData: [],
            calcCourses: [],
            calcWeight: {},
            pieData1: [],
            pieData2: [],
            childDetail: null,
            showMapping1: false,
            showMapping2: false,
            mapping1Data: [],
            totalCourse: 0
        },
        created: function() {
            this.getSchoolYear()
        },
        computed: {
            allCourse: {
                get: function () {
                    if (this.mainData && this.mainData.courses) {
                        if (Object.keys(this.mainData.courses).length === this.calcCourses.length) {
                            if (document.getElementById('check_all'))
                                document.getElementById('check_all').indeterminate = false
                            return true
                        }
                        else {
                            if (this.calcCourses.length > 0) {
                                if (document.getElementById('check_all'))
                                    document.getElementById('check_all').indeterminate = true
                            }
                            return false
                        }
                    }
                    return true
                },
                set: function(val) {
                    if (this.mainData && this.mainData.courses && val) {
                        for (let c in this.mainData.courses) {
                            if (this.calcCourses.indexOf(c) < 0) {
                                this.calcCourses.push(c)
                            }
                        }
                    }
                    else {
                        this.calcCourses = []
                    }
                }
            },
            allPeriods: {
                get: function () {
                    if (this.period.length === 4 && this.finalPeriod.length === 2) {
                        if (document.getElementById('all_periods'))
                            document.getElementById('all_periods').indeterminate = false
                        return true
                    }
                    else {
                        if (this.period.length > 0 || this.finalPeriod.length > 0) {
                            if (document.getElementById('all_periods'))
                                document.getElementById('all_periods').indeterminate = true
                        }
                        return false
                    }
                },
                set: function(val) {
                    if (val === false) {
                        this.period = []
                        this.finalPeriod = []
                    }
                    else {
                        this.period = this.periods
                        this.finalPeriod = this.finalPeriods
                    }
                }
            }
        },
        watch: {
            year: function () {
                this.getMainData()
            },
            grade: function () {
                this.getMainData()
            },
            mapping1: {
                handler() {
                    this.drawTable()
                },
                deep: true
            },
            mapping2: {
                handler() {
                    this.drawTable()
                },
                deep: true
            }
        },
        methods: {
            getSchoolYear() {
                $.getJSON('<?php echo $this->createUrl("/mteaching/ranking/schoolyear")?>', {}, (data) => {
                    this.schoolYear = data.data
                    this.year = this.schoolYear[0].id
                })
            },
            btnGrade(grade) {
                this.grade = grade
            },
            getMainData() {
                if (this.year && this.grade){
                    this.isLoading = true
                    this.showMapping1 = false
                    this.showMapping2 = false
                    this.mapping1Data = []
                    $.getJSON('<?php echo $this->createUrl("/mteaching/ranking/mainData")?>', {
                        schoolYear: this.year,
                        grade: this.grade
                    }, (data) => {
                        this.mainData = data.data
                        if (typeof data.data.periods != 'undefined') {
                            if (this.period.length === 0 && this.finalPeriod.length === 0) {
                                // this.period = this.mainData.periods
                                // this.finalPeriod = this.mainData.finalPeriods
                                this.finalPeriod.push('Year-End Final Grade')
                            }
                            this.calcCourses = []
                            for(let index in this.mainData.courses) {
                                this.calcCourses.push(index)
                                this.calcWeight[index] = typeof this.mainData['credit'][index] == 'undefined' ? 1 : this.mainData['credit'][index]
                            }
                            this.totalCourse = this.calcCourses.length
                            this.drawTable()
                        }
                        else {
                            this.calcCourses = []
                            this.totalCourse = 0
                            this.calcWeight = {}
                        }
                        this.isLoading = false
                    })
                }
            },
            drawTable() {
                let ret = []
                if (this.mainData) {
                    for (let index in this.mainData['child']) {
                        let obj = {}
                        const child = this.mainData['child'][index]
                        obj['id'] = child.id

                        const calc = this.calcTotal(child.score)
                        obj['count'] = calc[0]
                        obj['total'] = calc[1]

                        obj['average'] = calc[0] !== 0 ? (calc[1]/calc[0]).toFixed(2) : 0

                        ret.push(obj)
                    }
                    ret.sort(function (a, b) {
                        return b.average - a.average
                    })

                    const topLine = Math.round(ret.length*0.15)
                    const bottomLine = ret.length-topLine

                    const topLineScore = ret[topLine-1] ? ret[topLine-1].average : ''
                    const bottomLineScore = ret[bottomLine-1] ? ret[bottomLine-1].average : ''

                    let tmpArr1 = []
                    let tmpArr2 = []
                    this.pieData1 = []
                    this.pieData2 = []
                    for (let item in ret) {
                        // let c = parseInt(item)+1
                        // ret[item]['top15'] = (c <= topLine)
                        // ret[item]['bottom15'] = (c >= bottomLine)

                        ret[item]['top15'] = (topLineScore <= ret[item].average && ret[item].average > 0)
                        ret[item]['bottom15'] = (bottomLineScore >= ret[item].average && ret[item].average < topLineScore)

                        if (ret[item]['top15'] === true) {
                            let cname = this.mainData.childInfo[ret[item].id].overClass ? 'G'+this.mainData.childInfo[ret[item].id].overClass.substring(16).trim() : ''
                            if (tmpArr1.indexOf(cname) >= 0) {
                                for(let p in this.pieData1) {
                                    if (this.pieData1[p].name == cname) {
                                        this.pieData1[p].value += 1
                                    }
                                }
                            }
                            else {
                                tmpArr1.push(cname)
                                this.pieData1.push({
                                    name: cname,
                                    value: 1
                                })
                            }
                        }
                        if (ret[item]['bottom15'] === true) {
                            let cname = this.mainData.childInfo[ret[item].id].overClass ? 'G'+this.mainData.childInfo[ret[item].id].overClass.substring(16).trim() : ''
                            if (tmpArr2.indexOf(cname) >= 0) {
                                for(let p in this.pieData2) {
                                    if (this.pieData2[p].name == cname) {
                                        this.pieData2[p].value += 1
                                    }
                                }
                            }
                            else {
                                tmpArr2.push(cname)
                                this.pieData2.push({
                                    name: cname,
                                    value: 1
                                })
                            }
                        }
                    }
                }
                this.tableData = ret
                this.$nextTick(() => {
                    if (this.mainData.courses) {
                        this.drawPie()
                    }
                })
            },
            calcTotal(score) {
                let count = 0.0
                let total = 0
                for(let key in this.calcCourses) {
                    let cTotal = 0
                    const program = this.calcCourses[key]
                    const cIds = this.mainData['courses'][program]['ids']
                    for (let _i in cIds) {
                        const cKey = cIds[_i]
                        for(let _key in this.period) {
                            const circle = this.period[_key]-1
                            if (typeof score[cKey] != 'undefined'){
                                const val = score[cKey]['periods'][circle]
                                for(let __key in val) {
                                    let v = this.scoreMapping(val[__key], program)
                                    if (v !== '--') {
                                        cTotal += parseInt(v) * parseFloat(this.calcWeight[program])
                                        count = count + parseFloat(this.calcWeight[program])
                                    }
                                }
                            }
                        }
                        for(let _key in this.finalPeriod) {
                            if (this.finalPeriod[_key] === 'Final Criteria Score') {
                                if (typeof score[cKey] != 'undefined') {
                                    const val = typeof score[cKey]['final'] != 'undefined' ? score[cKey]['final'][0] : ['--', '--', '--', '--']
                                    for (let __key in val) {
                                        let v = this.scoreMapping(val[__key], program)
                                        if (v !== '--') {
                                            cTotal += parseInt(v) * parseFloat(this.calcWeight[program])
                                            count = count + parseFloat(this.calcWeight[program])
                                        }
                                    }
                                }
                            }
                            if (this.finalPeriod[_key] === 'Year-End Final Grade') {
                                if (typeof score[cKey] != 'undefined') {
                                    const val = typeof score[cKey]['final'] != 'undefined' ? score[cKey]['final'][1] : '--'
                                    let v = this.scoreMapping(val, program)
                                    if (v !== '--') {
                                        cTotal += parseInt(v) * parseFloat(this.calcWeight[program])
                                        count = count + parseFloat(this.calcWeight[program])
                                    }
                                }
                            }
                        }
                    }
                    total += cTotal
                    // total += cTotal * (this.calcWeight[program])
                    // count *= (this.calcWeight[program])
                }
                return [parseFloat(count.toFixed(2)), parseFloat(total.toFixed(2))];
            },
            scoreMapping(score, program) {
                if (typeof this.mapping1[score] != 'undefined') {
                    if (this.showMapping1 === false) {
                        this.showMapping1 = true
                        this.mapping1Data.push(this.mainData.courses[program].title)

                        // this.calcCourses.splice(this.calcCourses.indexOf(program), 1)
                    }
                    return this.mapping1[score]
                }
                if (typeof this.mapping2[score] != 'undefined') {
                    if (this.showMapping2 === false) {
                        this.showMapping2 = true
                    }
                    return this.mapping2[score]
                }
                return score
            },
            drawPie() {
                const chartDom1 = document.getElementById('chart_pie1')
                const chartDom2 = document.getElementById('chart_pie2')
                const myChart1 = echarts.init(chartDom1);
                const myChart2 = echarts.init(chartDom2);
                let option1, option2;

                let datas1 = [ this.pieData1 ];
                let datas2 = [ this.pieData2 ];
                option1 = {
                    title: {
                        text: 'Top 15% Distribution',
                        left: 'center',
                        textStyle: {
                            color: '#999',
                            fontWeight: 'normal',
                            fontSize: 14
                        }
                    },
                    series: datas1.map(function (data, idx) {
                        return {
                            type: 'pie',
                            radius: '55%',
                            left: 'center',
                            width: '100%',
                            itemStyle: {
                                borderColor: '#fff',
                                borderWidth: 1
                            },
                            label: {
                                alignTo: 'edge',
                                formatter: '{c} \n {time|{b}}',
                                // minMargin: 5,
                                edgeDistance: 0,
                                // lineHeight: 15,
                                rich: {
                                    time: {
                                        fontSize: 10,
                                        color: '#999'
                                    }
                                }
                            },
                            labelLine: {
                                length: 15,
                                length2: 0,
                                maxSurfaceAngle: 80
                            },
                            labelLayout: function (params) {
                                const isLeft = params.labelRect.x < myChart1.getWidth() / 2;
                                const points = params.labelLinePoints;
                                // Update the end point.
                                points[2][0] = isLeft
                                    ? params.labelRect.x
                                    : params.labelRect.x + params.labelRect.width;
                                return {
                                    labelLinePoints: points
                                };
                            },
                            data: data
                        };
                    })
                };
                option2 = {
                    title: {
                        text: 'Bottom 15% Distribution',
                        left: 'center',
                        textStyle: {
                            color: '#999',
                            fontWeight: 'normal',
                            fontSize: 14
                        }
                    },
                    series: datas2.map(function (data, idx) {
                        return {
                            type: 'pie',
                            radius: '55%',
                            left: 'center',
                            width: '100%',
                            itemStyle: {
                                borderColor: '#fff',
                                borderWidth: 1
                            },
                            label: {
                                alignTo: 'edge',
                                formatter: '{c} \n {time|{b}}',
                                edgeDistance: 0,
                                rich: {
                                    time: {
                                        fontSize: 10,
                                        color: '#999'
                                    }
                                }
                            },
                            labelLine: {
                                length: 15,
                                length2: 0,
                                maxSurfaceAngle: 80
                            },
                            labelLayout: function (params) {
                                const isLeft = params.labelRect.x < myChart2.getWidth() / 2;
                                const points = params.labelLinePoints;
                                // Update the end point.
                                points[2][0] = isLeft
                                    ? params.labelRect.x
                                    : params.labelRect.x + params.labelRect.width;
                                return {
                                    labelLinePoints: points
                                };
                            },
                            data: data
                        };
                    })
                };

                option1 && myChart1.setOption(option1);
                option2 && myChart2.setOption(option2);
            },
            showDetail(id, total, count) {
                this.childDetail = {}
                this.childDetail['child'] = this.mainData['childInfo'][id]
                this.childDetail['total'] = total
                this.childDetail['count'] = count
                this.childDetail['average'] = (total/count).toFixed(2)

                let items = []
                for (let i in this.mainData['child'][id]['score']) {
                    const item = this.mainData['child'][id]['score'][i]

                    let title = ''
                    for (let j in this.mainData['courses']) {
                        if (this.mainData['courses'][j]['ids'].indexOf(parseInt(i)) >= 0) {
                            title = this.mainData['courses'][j]['title']
                        }
                    }

                    let obj = {
                        title: title,
                        score: item
                    }
                    items.push(obj)
                }
                this.childDetail['score'] = items

                $('#score_modal').modal()
            },
            exportExcel() {
                const aoa = []
                aoa.push(['Ranking', 'Student Number', 'Student Name', 'Academic Year', 'Grade Level', 'Homeroom', 'Total Score', 'Total Course', 'Average'])

                const childInfo = this.mainData.childInfo
                this.tableData.map( (value, index) => {
                    const child = childInfo[value.id]
                    const classArray = child.overClass.trim().split(' ')
                    aoa.push([
                        index + 1,
                        value.id,
                        child.name,
                        classArray[0],
                        classArray[2] +' '+ classArray[3],
                        classArray[3] + (classArray[4] || ''),
                        value.total,
                        value.count,
                        value.average
                    ])
                } )

                const workbook = XLSX.utils.book_new()
                const worksheet = XLSX.utils.aoa_to_sheet(aoa)
                XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')
                const date = new Date()
                XLSX.writeFile(workbook, 'Score Analytics_'+'Grade'+this.grade+'_'+date.getFullYear()+'-'+(date.getMonth()+1)+'-'+date.getDate()+'.xlsx')
            },
            openTop15() {
                if (this.totalCourse != this.calcCourses.length) {
                    alert('All subjects should be selected when tagging top students.')
                }
                else {
                    $('#top15_modal').modal()
                }
            },
            starTag() {
                let clear_child_ids = []
                let child_ids = []
                for(let t in this.tableData) {
                    const item = this.tableData[t]
                    clear_child_ids.push(item.id)
                    if (item.top15) {
                        child_ids.push(item.id)
                    }
                }
                $.post('<?php echo $this->createUrl('saveStarFlag')?>', {
                    clear_child_ids: clear_child_ids,
                    child_ids: child_ids
                }, (data) => {
                    data = eval('(' + data + ')')
                    if (data.state === 'success') {
                        resultTip({msg: 'Success!'})
                        this.getMainData()
                        $('#top15_modal').modal('hide')
                    }
                })
            }
        }
    })
</script>

<style>
.block-01 {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.block-02 {
    background-color: #FAFAFA;
    padding: 24px;
}
.block-03 {
    background: #F2F3F5;
    border-radius: 8px;
    padding: 16px 20px;
}
.tag-01 {
    background: #E5E6EB;
    border-radius: 2px;
    padding: 2px 6px;
    height: 20px;
    display: inline-block;
    margin-left: 5px;
}
.font-01 {
    font-size: 14px;
    font-weight: 500;
    color: #333333;
    line-height: 20px;
}
.font-02 {
    font-size: 12px;
    font-weight: 400;
    color: #666666;
    line-height: 12px;
}
.font-03 {
    font-size: 14px;
    font-weight: 400;
    color: rgba(0,0,0,0.65);
    line-height: 22px;
}
.table-01 {
    width: 100%;
}
.table-01 th, .table-01 td {
    padding: 16px;
}
.table-01 thead tr{
    height: 54px;
    background-color: #F2F3F5;
}
.table-01 tbody tr{
    height: 75px;
    background-color: #FEFAF5;
}
.table-01 td {
    border-bottom: 1px solid #E8E8E8;
}
.table-02 thead {
    background-color: #FAFAFA;
}
.table-02 thead th {
    text-align: center;
}
.table-02 tbody td {
    text-align: center;
    vertical-align: middle !important;
}
.info-01 {
    display: flex;
    align-items: center;
}
.info-01 img {
    width: 42px;
    height: 42px;
    border-radius: 21px;
}
.info-02 {
    width: 60px;
    height: 26px;
    line-height: 26px;
    border: 2px solid #F0AD4E;
    color: #F0AD4E;
    text-align: center;
    transform: rotate(345deg);
    font-weight: bold;
}
.title-01 {
    background: rgb(242, 243, 245);
    height: 42px;
    line-height: 42px;
    border-radius: 4px;
    padding: 5px;
    margin: -5px;
}
</style>
<?php endif;?>