<?php

class ChartsModule extends CWebModule
{
	public function init()
	{
		// this method is called when the module is being created
		// you may place code here to customize the module or the application

		// import the module-level models and components
//		$this->setImport(array(
//			'charts.models.*',
//			'charts.components.*',
//		));
        Yii::app()->theme = 'charts';
        Yii::import('common.models.charts.*');
	}

	public function beforeControllerAction($controller, $action)
	{
		if(parent::beforeControllerAction($controller, $action))
		{
            $yiicookies = Yii::app()->request->cookies;
            $thekey = 'chartstheme_'.Yii::app()->user->id;
            $controller->chartsTheme = isset($yiicookies[$thekey]->value) && $yiicookies[$thekey]->value ? $yiicookies[$thekey]->value : 'gray';
            $cookie = new CHttpCookie($thekey, $controller->chartsTheme);
            $cookie->expire=time()+3600*24*7;
            $yiicookies[$thekey]=$cookie;

            $controller->menu = array(
                array(
                    'label'=>'Operational Data <b class="caret"></b>',
                    'itemOptions'=>array('class'=>'dropdown'),
                    'url'=>'#',
                    'linkOptions'=>array('class'=>'dropdown-toggle', 'data-toggle'=>'dropdown'),
                    'submenuOptions'=>array('class' => 'dropdown-menu'),
                    'items'=>array(
                        array('label'=>'Dashboard', 'url'=>array('/charts/default/dashboard')),
//                        array('label'=>'Child Number & Revenue', 'url'=>array('/charts/default/index')),
//                        array('label'=>'Monthly Revenue', 'url'=>array('/charts/default/revenue')),
//                        array('label'=>'Visit', 'url'=>array('/charts/default/visit')),
                    )
                ),
                array(
                    'label'=>'User <b class="caret"></b>',
                    'itemOptions'=>array('class'=>'dropdown'),
                    'url'=>'#',
                    'linkOptions'=>array('class'=>'dropdown-toggle', 'data-toggle'=>'dropdown'),
                    'submenuOptions'=>array('class' => 'dropdown-menu'),
                    'items'=>array(
                        array('label'=>'Settings', 'url'=>array('settings')),
                        array('label'=>'English', 'url'=>array("//site/lang", "lang" => "english"), 'linkOptions'=>array('class'=>'J_lang')),
                        array('label'=>'中文', 'url'=>array("//site/lang", "lang" => "schinese_utf8"), 'linkOptions'=>array('class'=>'J_lang')),
                        array('label'=>'Logout', 'url'=>Yii::app()->user->logoutUrl),
                    )
                ),
            );
			return true;
		}
		else
			return false;
	}
}
