<style>
    [v-cloak] {
        display: none;
    }
    .panel-body .badge {
        position: absolute;
        right: 15px;
    }
    .loading{
        width:98%;
        height:100%;
        background:#fff;
        position: absolute;
        opacity: 0.5;
        z-index: 99;
        overflow: hidden;
    }
    .loading span{
        width:100%;
        height:100%;
        display:block;
        background: url("<?php echo Yii::app()->theme->baseUrl?>/images/loading.gif")no-repeat center center ;
    }
    .badge {
        background-color: #F56B6C;
        color: #fff;
    }
    .bg7{
        background-color: #777777;
    }
    .comment-box {
        background-color: #fff;
        padding: 15px;
        border: 1px solid #dddddd;
        margin-bottom: 17px;
        margin-top: -17px;
    }
    .bg-info {
        background-color: #F7F7F8;
    }
    .active-1 {
        background-color: #FCF8E4 !important;
        border: 1px solid #ddd;
    }
    .name-2 {
        font-size: 14px;
        font-weight: 700;
        color: #333333;
        margin-bottom: 8px;
        line-height:50px
    }
    .content-2 {
        font-size: 14px;
        font-weight: 400;
        color: #333333;
        margin-bottom: 10px;
    }
    .bg-hover:hover {
        background-color: #F7F7F8;
    }
    .badge.dot {
        width: 8px;
        height: 8px;
        border-radius: 4px;
        min-width: unset;
        padding: 0;
    }
    .allImage{
        width: 48px;
        height: 48px;
        object-fit: cover;
    }
    .cover{
        object-fit: cover;
    }
    .displayFlex{
        display:flex
    }
    .flexWidth{
        width:162px
    }
    .ellipsis{
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .badgeNum{
        display: inline-block;
        width: 6px;
        height: 6px;
        background: #d9534f;
        border-radius: 50%;
        margin-top: 6px;
    }
    .nav-tabs > li > a{
        border:none
    }
    .image{
        width: 48px; 
        height: 48px;
        object-fit:cover;
    }
    .content img{
        max-width:100%;
        margin-bottom: 1em; 
    }
    .gray{
        background:#777
    }
    .Unreplied{
        right:74px !important;
    }
    .borderTop{
        border-top:1px solid #ddd;
    }
    .borderBto{
        border-bottom:1px solid #ddd;
    }
    .p0{
        padding:0
    }
    .pr15{
        padding-right:15px
    }
    .commentImage{
        width:40px;
        height:40px;
        object-fit:cover;
    }
    .point{
        border: 3px solid #EC971F;
    }
    .border{
        border: 2px solid #428BCA;
    }
    .pl10{
        padding-left:10px
    }
    .maxHeight{
        max-height:500px;
        overflow-y:auto
    }
    .tagLabel{
        background:#EBEDF0
    }
    .contentPostObject{
        width: 36px;
        height: 36px;
        border-radius: 50%;
        object-fit:cover;
    }
    .contentAvatar{
        width: 44px;
        height: 44px;
        border-radius: 50%;
        object-fit:cover;
    }
    .postMore{
        font-size: 14px;
        display: inline-block;
        height: 36px;
        line-height: 36px;
        background: #4D88D2;
        color: #fff;
        padding: 0 10px;
        border-radius: 18px;
        vertical-align: middle;
    }
    .replyColor{
        color:#F0AD4E
    }
    .forward{
        display: none;
        padding:10px 0 
    }
    .p1015{
        padding:10px 15px;
    }
    /* .parentForward:hover{
        cursor: pointer;
        background:#fff;
    }
    .parentForward:hover .forward{
        display: block 
    } */
    .forwardText{
        background: #FAFAFA;
        border-radius:4px;
        padding:16px
    }
    .borderRight{
        border-right:1px solid #ddd
    }
    .forwarded{
        background:#F7F7F8;
        border: 1px solid #EBEDF0;
        padding:12px 16px;
        border-radius: 4px;
    }
    .optionSearch{
        height:auto;
        width:100%
    }
    .text_overflow{
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        line-height: 14px;
    }
    .breakAll{
        word-break: break-word;
        text-align: justify
    }
    .breakAll img{
        max-width:100%
    }
    .textareaCss{
        border: none;
        border-radius: 0;
        box-shadow: none;
    }
    .uploadIcon{
        background: #fff;
        font-size: 16px;
        padding: 10px 12px;
        color: #555555;
        margin-top:10px
    }
    .uploadImg{
        overflow-x: auto;
        white-space: nowrap;
    }
    .fileImgs{
        width:72px;
        height:72px;
        object-fit: cover;
        display: inline-block;
    }
    .fileImg{
        width:72px;
        height:72px;
        object-fit: cover;
        display: inline-block;
        margin-bottom:10px;
        cursor: pointer;
    }
    .imgData{
        position: relative;
        display: inline-block;
    }
    .imgData span{    
        position: absolute;
        right: 3px;
        top: 3px;
        background: #333333;
        opacity: 1;
        color: #fff;
        width: 17px;
        height: 17px;
        border-radius: 50%;
        font-weight: 200;
        text-align: center;
        line-height: 15px;
        font-size: 19px;
    }
    .uploadFile {
        font-size:14px;
        padding:6px 12px;
        align-items: center;
    }
    .uploadFile .icon{
        display:none
    }
    .uploadFile:hover{
        background: #F7F7F8;

    }
    .uploadFile:hover .icon{
        display:block
    }
    .uploadFile a{
        color:#4D88D2
    }
    .fileLink{
        background: #F7F7F8;
        display: inline-block;
        border-radius: 4px;
        border: 1px solid #EBEDF0;
        padding: 6px 12px;
        font-size: 14px;
        margin-right: 16px;
        margin-bottom:8px;
        line-height:20px
    }
    .fileLink img{
        width:20px
    }
    .uploadLoading{
        width: 72px;
        height: 72px;
        background: #D9D9D9;
        border-radius: 4px;
        line-height: 72px;
        text-align: center;
    }
    .inputStyle{
        border: none;
        border-bottom: 1px solid #ccc;
        padding: 0;
        height: 25px;
        line-height: 25px;
        width: 100%;
    }
    .imgLi{
        list-style: none;
        padding-left: 0;
    }
    .imgLi li{
        display:inline-block
    }
    .wechatQrcode{
        display: inline-block;
        padding: 6px 10px;
        color: #50B674;
        background: #FFFFFF;
        border-radius: 4px;
        border: 1px solid #50B674;
        cursor: pointer;
    }
    .wechatQrcode img{
        width:16px;
        float:left;
        border-radius: 50%;
    }
    .qrcodeBox{
        position: absolute;
        left: 0;
        /* top: 15px; */
        width: 290px;
        /* height: 274px; */
        border: 1px solid #ddd;
        background: #fff;
        border-radius: 4px;
        text-align:center;
        font-size:14px;
        color:#333;
        box-shadow: 0px 2px 8px 0px rgb(0 0 0 / 20%);
        z-index:9;
        padding:20px 10px
    }
    .qrcodeBox .qrcodeWechat{
        width:124px;
        height:124px;
        margin:0 auto
    }
    .img24{
        width: 24px;
        height: 24px;
        border-radius: 12px;
        border: 1px solid #FFFFFF;
        object-fit: cover;
    }
    .jointImg{
        margin-left:-5px
    }
    .absoluteFlex{
        position: absolute;
        top: 5px;
    }
    .wechatIcon{
        height:16px;
        position: absolute;
        bottom: 0;
        right: 0;
        width: 16px;
    }
    .avatarWechat{
        width:60px;
        height:60px;
        border-radius:50%
    }
    .deptHover:hover{
        cursor: pointer;
        color:#428bca !important
    }
    .viewer-prev::before, .viewer-next::before{
        background-image: none !important;
    }
    .overflowH{
        overflow-x:hidden;
        overflow-y:auto;
    }
    video{
        max-height: 300px;
        max-width: 100%;
        width: auto !important;
        margin-bottom:8px
    }
    .panel-body{
        position: relative;
    }
    .scrollhidden{
        overflow: hidden;
    }
</style>

<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Teaching Tasks'), array('//mteaching/default/index')) ?></li>
        <li><?php echo Yii::t('directMessage', 'Direct Message') ?></li>
        <li><?php echo Yii::t("newDS", "Parent Feedback");?></li>
    </ol>
    <div class="row scrollhidden" id='container' v-cloak>
        <div class="col-md-3 col-lg-2 col-sm-12">
            <div class="list-group">
                <a href="<?php echo $this->createUrl('index'); ?>" class="list-group-item status-filter"><?php echo Yii::t("newDS", "Mine");?></a></li>
                <a href="<?php echo $this->createUrl('feedback'); ?>" class="list-group-item status-filter active"><span  v-if='toReplyNum>0' class='badgeNum pull-right'></span><?php echo Yii::t("newDS", "Parent Feedback");?></a></li>
            </div>
            <div class='relative'>            
                <div class='wechatQrcode mb15' @click.stop='showUnWechatQrcode' v-if='wechatData.state==0'>
                    <div>
                        <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/wechatDM.png' ?>" alt="">
                        <span class='ml4'><?php echo Yii::t("directMessage", "Wechat Notification"); ?></span>
                    </div>
                </div>
                <div class="qrcodeBox"  v-if='scanQrcodeBox'>
                    <div class='font14 color3'><strong><?php echo Yii::t("directMessage", "Wechat Notification"); ?></strong></div>
                    <div class='mt8 font12 color6'><?php echo Yii::t("directMessage", "Response parent feedback on wechat!"); ?></div>
                    <div>
                        <div id='wechatQrcodeBind' alt="" class='qrcodeWechat mt24'></div>
                        <div class='font12 mt16 color6 '><?php echo Yii::t("directMessage", "Wechat scan to bind your account."); ?></div>
                    </div>
                </div>
                <div class='wechatQrcode mb15' @click.stop='qrcodeBox=true'  v-if='wechatData.state==1'>
                    <div>
                        <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/wechatDM.png' ?>" alt="" v-if='wechatData.headimgurl=="" || wechatData.headimgurl==null'>
                        <img :src="wechatData.headimgurl" alt="" v-if='wechatData.headimgurl!=""'>
                        <span class='ml4'><?php echo Yii::t("directMessage", "Wechat Enabled"); ?></span>
                    </div>
                </div>
                <div  class="qrcodeBox"  v-if='qrcodeBox'>
                    <div class='font14 color3'><strong><?php echo Yii::t("directMessage", "Wechat Enabled"); ?></strong></div>
                    <div class='mt8 font12 color6'><?php echo Yii::t("directMessage", "Response parent feedback on wechat!"); ?></div>
                    <div class='relative inline-block' v-if='wechatData.headimgurl!=""'>
                        <img :src="wechatData.headimgurl" alt="" class='avatarWechat mt16'>
                        <img src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/wechatDM.png' ?>" alt="" class='wechatIcon'>
                    </div>
                    <div class='font12 mt16 color6 '  v-if='wechatData.nickname!=""'>{{wechatData.nickname}}</div>
                    <div class='font12 mt24 mt16 color6 '><button class="btn btn-default" type="button" @click.stop='unbind()'><?php echo Yii::t("directMessage", "Unbind your account"); ?></button></div>
                </div>
            </div>
        </div>
        <div class="col-md-9 col-lg-10 col-sm-12">
            <div >
                <!-- Nav tabs -->
                <ul class="nav nav-tabs" role="tablist">
                    <li role="presentation" class="active"><a id="tab_unreply" href="#unreply" aria-controls="unreply" role="tab" data-toggle="tab" @click="change('unreply')"><?php echo Yii::t("newDS", "My unreplied");?> <span class="badge" v-if='tabType=="unreply" &&  feedback.count &&  feedback.count!=0'>{{ replied }}</span></a></li>
                    <li role="presentation"><a id="tab_replied" href="#replied" aria-controls="replied" role="tab" data-toggle="tab" @click="change('replied')"><?php echo Yii::t("newDS", "My replied");?>  </a></li>
                    <li role="presentation"><a id="tab_viewStu" href="#viewStu" aria-controls="replied" role="tab" data-toggle="tab" @click="viewStudent()"><?php echo Yii::t("newDS", "View By Student");?>  </a></li>
                    <?php if(in_array('leader', $this->managetypeList)): ?>
                    <li role="presentation"><a id="tab_all" href="#all" aria-controls="all" role="tab" data-toggle="tab" @click="change('all')"><?php echo Yii::t("newDS", "All (Admin Only)");?></a></li>
                    <li role="presentation"><a id="tab_allUnreplied" href="#allUnreplied" aria-controls="allUnreplied" role="tab" data-toggle="tab" @click="change('allUnreplied')"><?php echo Yii::t("newDS", "Unreplied (Admin Only)");?></a></li>
                    <?php endif;?>
                </ul>
                <!-- Tab panes -->
                <div class="tab-content mt20 overflowH pr8" :style="'max-height:'+(height-250)+'px'">
                    <div class='loading' v-if='loading'>
                        <span></span>
                    </div>
                    <div role="tabpanel" class="tab-pane active" id="unreply">
                        <div v-if='initLoading'>
                            <?php $this->renderPartial("_reply", array('type' => 'unreply'));?>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane" id="replied">
                        <?php $this->renderPartial("_reply", array('type' => 'replied'));?>
                        <nav aria-label="Page navigation" v-if='CopyPages.count>1'  class="text-left ml10">
                            <ul class="pagination">
                                <li v-if='pageNum >1'>
                                    <a href="javascript:void(0)" @click="plus('replied',1)" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                <li v-else class="disabled">
                                    <a aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                <li class="previous" v-if='pageNum >1'>
                                    <a href="javascript:void(0)" @click="prev('replied',pageNum)">‹</a>
                                </li>
                                <li class="disabled" v-else>
                                    <a href="javascript:void(0)">‹</a>
                                </li>
                                <li v-for='(data,index) in pages.count' :class="{ active:data==pageNum }">
                                    <a href="javascript:void(0)" @click="plus('replied',data)">{{data}}</a>
                                </li>
                                <li class="previous" v-if='pageNum <CopyPages.count'>
                                    <a href="javascript:void(0)" @click="next('replied',pageNum)">›</a>
                                </li>
                                <li class="previous disabled" v-else>
                                    <a href="javascript:void(0)">›</a>
                                </li>
                                <li v-if='pageNum <CopyPages.count'>
                                    <a href="javascript:void(0)" @click="plus('replied',CopyPages.count)" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                <li v-else class="disabled">
                                    <a aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                            </ul>
                            <div class='summary mb10'>第 <span v-if='pageNum*20-20==0'>1</span><span v-else>{{pageNum*20-20}}</span>-{{pageNum*20}} 条, 共 {{CopyPages.total}} 条.</div>
                        </nav>
                    </div>
                    <div role="tabpanel" class="tab-pane" id="allUnreplied">
                        <?php $this->renderPartial("_reply", array('type' => 'allUnreplied'));?>
                    </div>
                    <div role="tabpanel" class="tab-pane" id="all">
                        <div  class='flex mb16'>
                            <div class='flex1'></div>
                            <el-autocomplete
                                popper-class="my-autocomplete"
                                v-model="searchChildName"
                                clearable
                                size='small'
                                @clear='confirmAdmin("clear")'
                                :fetch-suggestions="querySearchAsync"
                                placeholder="<?php echo Yii::t('withdrawal','Search by student name');?>"
                                 class='inline-input'
                                @select="handleSelect">
                                <template slot-scope="{ item }">
                                    <div v-if='item.not' class='text-center'>{{item.name}}</div>
                                    <div v-else>
                                        <div class="media mb8" v-if='item.avatar'>
                                            <div class="media-left pull-left media-middle">
                                                <a href="javascript:void(0)">
                                                    <img :src="item.avatar" data-holder-rendered="true" class="media-object img-circle contentAvatar">
                                                </a>
                                            </div>
                                            <div class="media-body mt5 media-middle">
                                                <h4 class="media-heading font14 color3 text_overflow">{{item.name}}</h4>
                                                <div class="text-muted text_overflow font12">{{ item.class_name }}</div>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                            </el-autocomplete>
                            <button type="button" class="btn btn-primary btn-sm ml16"  :disabled='searchChildId==""?true:false' @click='confirmAdmin("search")'><?php echo Yii::t("withdrawal", "Search"); ?></button>
                        </div>
                        <div class='pr8' :style="'max-height:'+(height-300)+'px;overflow-y:auto'">
                            <div class="panel panel-default" v-for='(item,index) in allItems.items'>
                                <div class="panel-body">
                                    <div class="media " v-if='item.creator_type == "parent"'>
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                                <img class="media-object img-circle allImage" :src='allItems.childs[item.child_id].avatar' data-holder-rendered="true" >
                                            </a>
                                        </div>
                                        <div class="media-right pull-right" >
                                            <a href='javascript:void(0)'style='line-height:48px' @click='linked(item)'><?php echo Yii::t("newDS", "Read all conversations");?></a>
                                        </div>
                                        <div class="media-body pt10 media-middle">
                                            <h4  class="media-heading font14">{{allItems.childs[item.child_id].name}}</h4>
                                            <div class='text-muted font12'>{{allItems.childs[item.child_id].class_name}}</div>
                                        </div>
                                    </div>
                                    <div class="media " v-if='item.creator_type == "staff"'>
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                                <img class="media-object img-circle allImage" :src='allItems.staff[item.created_by].avatar' data-holder-rendered="true" >
                                            </a>
                                        </div>
                                        <div class="media-right pull-right" >
                                            <a href='javascript:void(0)'style='line-height:48px' @click='linked(item)' ><?php echo Yii::t("newDS", "Read all conversations");?></a>
                                        </div>
                                        <div class="media-body pt10 media-middle">
                                            <h4  class="media-heading font14 pt10">{{allItems.staff[item.created_by].name}}</h4>
                                        </div>
                                    </div>
                                    <div class='pt10' style='padding-left:58px'>
                                        <div class='font14 color3' v-html='html(item.content)'> </div>
                                        <div class='mb10 font14 mt10' v-if='item.attachments>0'><a href="javascript:void(0)" @click='linked(item)'><span class='glyphicon glyphicon-paperclip'></span> {{item.attachments}} <?php echo Yii::t("curriculum", "Attachments"); ?></a> </div>
                                        <p class='font12 color6 mt10'>{{item.updated_at}}</p>
                                    </div>
                                </div>    
                                <div class="panel-footer displayFlex">
                                    <div class="flex1 ellipsis">
                                        <?php echo Yii::t("newDS", "Source:");?>  <a href='javascript:void(0)' @click='viewContent(item.journal_id)'>{{item.journal_title}}</a>
                                    </div>
                                    <div class="flexWidth color6 text-right"> {{newDate(item.journal_updated_at)}} </div>
                                </div>
                            </div>
                            <nav aria-label="Page navigation" v-if='CopyPages.count>1'  class="text-left ml10">
                                <ul class="pagination">
                                    <li v-if='pageNum >1'>
                                        <a href="javascript:void(0)" @click="plus('all',1)" aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                    <li v-else class="disabled">
                                        <a aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                    <li class="previous" v-if='pageNum >1'>
                                        <a href="javascript:void(0)" @click="prev('all',pageNum)">‹</a>
                                    </li>
                                    <li class="disabled" v-else>
                                        <a href="javascript:void(0)">‹</a>
                                    </li>
                                    <li v-for='(data,index) in pages.count' :class="{ active:data==pageNum }">
                                        <a href="javascript:void(0)" @click="plus('all',data)">{{data}}</a>
                                    </li>
                                    <li class="previous" v-if='pageNum <CopyPages.count'>
                                        <a href="javascript:void(0)" @click="next('all',pageNum)">›</a>
                                    </li>
                                    <li class="previous disabled" v-else>
                                        <a href="javascript:void(0)">›</a>
                                    </li>
                                    <li v-if='pageNum <CopyPages.count'>
                                        <a href="javascript:void(0)" @click="plus('all',CopyPages.count)" aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                    <li v-else class="disabled">
                                        <a aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                </ul>
                                <div class='summary mb10'>第 <span v-if='pageNum*20-20==0'>1</span><span v-else>{{pageNum*20-20}}</span>-{{pageNum*20}} 条, 共 {{CopyPages.total}} 条.</div>
                            </nav>
                        </div>
                    </div>
                    <div role="tabpanel" class="tab-pane" id="viewStu"> 
                        <div v-if='!loading'>
                            <div class="col-md-2 col-sm-12 p0 pr15" v-if='classList.list && classList.list.length!=0'>
                                <div class="list-group mt5">
                                    <a href="javascript:;" class="list-group-item status-filter" v-for='(list,index) in classList.list' @click='messageStu(list)' :class='list.id==selectedId?"active":""'>{{list.title}}</a></li>
                                </div>
                            </div>
                            <div class="alert alert-warning" role="alert" v-else><?php echo Yii::t("newDS", "No comments replied to you.");?></div>
                        </div>
                        <div  class="col-md-10 col-sm-12 p0">
                            <div class='loading' v-if='classLoading'>
                                <span></span>
                            </div>
                            <div v-if='selectedId!="" && showChildName'>
                                <div class="alert alert-warning" role="alert" v-if='commentedIds.length==0 && unCommentedIds.length==0'><?php echo Yii::t("newDS", "No comment found.");?></div>
                                <div v-else>
                                    <p class='mb20 mt5 font14 color6'><?php echo Yii::t("newDS", "Students with comments");?></p>
                                    <div class='pb20' :class='unCommentedIds.length==0?"borderBto":""' v-if='commentedIds.length!=0'>
                                        <img  class='img-circle commentImage cur-p' v-for='(list,index) in commentedIds' onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' :title="`<div>${studentInfo[list].name}</div>${classTitle}`"  data-placement="top" :class='list==commentChildId?"point":""' @click='commentList(list)' style='margin: 0 10px 10px 0' :src="studentInfo[list].avatar" alt="">
                                    </div>
                                    <div class="alert alert-warning" role="alert" v-else><?php echo Yii::t("newDS", "No comment found.");?></div>
                                </div> 
                                <div v-if='unCommentedIds.length!=0'>
                                    <p class='mb20 font14 color6'><?php echo Yii::t("newDS", "Students without comments");?></p>
                                    <div class=' borderBto pb20'>
                                        <img v-for='(list,index) in unCommentedIds' class='img-circle commentImage cur-p'  onMouseOver="$(this).tooltip('show')"  data-toggle="tooltip" data-html='true' :title="`<div>${studentInfo[list].name}</div>${classTitle}`"  data-placement="top"  style='margin: 0 10px 10px 0;opacity:0.4' :src="studentInfo[list].avatar" alt="">
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class='loading' v-if='commentLoading'>
                                    <span></span>
                                </div>
                                <div class="alert alert-warning mt20" role="alert" v-if='selectedId!="" && commentChildId=="" && commentedIds.length!=0'><?php echo Yii::t("newDS", "Select a student to view comments.");?></div>
                                <div class='mt20 pt7' v-if='selectedId!="" && commentChildId!=""'>                               
                                    <div class="panel panel-default mb20" v-for='(list,i) in commentedList'>
                                        <div class="panel-heading">
                                            <p>
                                                <a href='javascript:;' class='font14' @click='viewContent(list._id)'>{{list.title}}</a>
                                            </p>
                                            <div>
                                                <!-- <span class="label label-default defaultBg mr5 mb5 fontWeightNormal pull-left">{{journalCategory[list.category]}}</span> -->
                                                <span class='text-muted'>{{list.created}}</span>
                                            </div>
                                        </div>
                                        <div class="panel-body">
                                            <div class="media" v-for='(fItem,i) in list.items'>
                                                <div v-if='fItem.mark_as_staff==0'>
                                                    <div v-if="fItem.creator_type == 'parent'">
                                                        <div class="media-left pull-left media-middle">
                                                            <a href="javascript:void(0)">
                                                                <img :src="commentChildData.avatar" data-holder-rendered="true" class="media-object img-circle commentImage">
                                                            </a>
                                                        </div> 
                                                        <div class="media-body media-middle pl10">
                                                            <h4 class="media-heading color3 font14" style='line-height:37px'><strong>{{commentChildData.name}}</strong> </h4> 
                                                            <div class='color3 font14' v-html='html(fItem.content)'></div>
                                                            <div class='mt10'>
                                                                <ul class='mb12 imgLi':id='tabType+"_"+fItem.id' v-if='fItem.imgUrl.length!=0'>
                                                                    <li v-for='(list,j) in fItem.imgUrl'>
                                                                        <img :src="list.url" class='fileImg mr8' @click='showImg(fItem)' alt=""  >
                                                                    </li>
                                                                </ul>
                                                                <div  v-if='fItem.videoUrl.length!=0'>
                                                                    <div v-for="(list, j) in fItem.videoUrl" :key="list.id">
                                                                        <div v-if="list.video_convert_status == 0" class="flex fileLink">
                                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" /> 
                                                                            <a class='flex1 ml5' target= "_blank" :href="list.url"><?php echo Yii::t('message', "Convert") ?></a>
                                                                        </div>
                                                                        <video v-else :src="list.url" :poster="list.video_cover"  controls muted preload="auto" style="width: 100%; height: 100%; fit: fill"></video>
                                                                    </div>
                                                                </div>
                                                                <div >
                                                                    <div class='flex fileLink' v-for='(list,j) in fItem.pdfUrl'>
                                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/PDF1.png' ?>" alt="" class="pdfImg" v-if='list.mimetype=="application/pdf"' />
                                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/excel.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/vnd.ms-excel" || list.mimetype=="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"' />
                                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/word1.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/msword" || list.mimetype=="application/vnd.openxmlformats-officedocument.wordprocessingml.document"' />
                                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/zip.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/x-zip-compressed"' />
                                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" v-else /> 
                                                                        <a class='flex1 ml5' target= "_blank" :href="list.url">{{list.title}}
                                                                        </a>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class='mt15 text-muted'>
                                                                <span class='mr20' v-if="commentChildData[`p_${fItem.created_by}`] == 'f'"><?php echo Yii::t("newDS", "From student's Dad");?></span>
                                                                <span class='mr20' v-if="commentChildData[`p_${fItem.created_by}`] == 'm'"><?php echo Yii::t("newDS", "From student's Mom");?></span>
                                                                <span class='ml20'> {{ fItem.created_at }}</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div v-else>
                                                        <div class="media-left pull-left media-middle">
                                                            <a href="javascript:void(0)">
                                                                <img :src="teacherInfo.photo" data-holder-rendered="true" class="media-object img-circle commentImage">
                                                            </a>
                                                        </div> 
                                                        <div class="media-body media-middle pl10">
                                                            <h4 class="media-heading color3 font14 "  style='line-height:37px'><strong>{{teacherInfo.name}}</strong> </h4> 
                                                            <div class='color3 font14' v-html='html(fItem.content)'></div>
                                                            <div class='mt10'>
                                                                <ul class='mb12 imgLi' :id='tabType+"_"+fItem.id' v-if='fItem.imgUrl.length!=0'>
                                                                    <li v-for='(list,j) in fItem.imgUrl'>
                                                                        <img :src="list.url" class='fileImg mr8' @click='showImg(fItem)' alt=""  >
                                                                    </li>
                                                                </ul>
                                                                <div  v-if='fItem.videoUrl.length!=0'>
                                                                    <div v-for="(list, j) in fItem.videoUrl" :key="list.id">
                                                                        <div v-if="list.video_convert_status == 0" class="flex fileLink">
                                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" /> 
                                                                            <a class='flex1 ml5' target= "_blank" :href="list.url"><?php echo Yii::t('message', "Convert") ?></a>
                                                                        </div>
                                                                        <video v-else :src="list.url" :poster="list.video_cover"  controls muted preload="auto" style="width: 100%; height: 100%; fit: fill"></video>
                                                                    </div>
                                                                </div>
                                                                <div >
                                                                    <div class='flex fileLink' v-for='(list,j) in fItem.pdfUrl'>
                                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/PDF1.png' ?>" alt="" class="pdfImg" v-if='list.mimetype=="application/pdf"' />
                                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/excel.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/vnd.ms-excel" || list.mimetype=="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"' />
                                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/word1.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/msword" || list.mimetype=="application/vnd.openxmlformats-officedocument.wordprocessingml.document"' />
                                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/zip.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/x-zip-compressed"' />
                                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" v-else /> 
                                                                        <a class='flex1 ml5' target= "_blank" :href="list.url">{{list.title}}
                                                                        </a>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class='text-muted mt15'>{{ fItem.created_at }}</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div v-else class='p20 mt20 text-center borderTop' :class='i!=list.items.length-1?"borderBto":""'>
                                                    <p class='font14 color3'>
                                                        <span class='glyphicon glyphicon-pushpin mr10' style='color:#EC971FFF'></span>{{teacherInfo.name}} <?php echo Yii::t("newDS", "marked No Reply Needed");?> <span class='font14 color6 ml10'>{{fItem.created_at}}</span>
                                                    </p>
                                                    <div  class='font12 color9'>
                                                        <?php echo Yii::t("newDS", "(This message is invisible to parents)");?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <nav aria-label="Page navigation" v-if='CopyPages.count>1'  class="text-left ml10">
                                <ul class="pagination">
                                    <li v-if='pageNum >1'>
                                        <a href="javascript:void(0)" @click="plus('viewStu',1)" aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                    <li v-else class="disabled">
                                        <a aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                    <li class="previous" v-if='pageNum >1'>
                                        <a href="javascript:void(0)" @click="prev('viewStu',pageNum)">‹</a>
                                    </li>
                                    <li class="disabled" v-else>
                                        <a href="javascript:void(0)">‹</a>
                                    </li>
                                    <li v-for='(data,index) in pages.count' :class="{ active:data==pageNum }">
                                        <a href="javascript:void(0)" @click="plus('viewStu',data)">{{data}}</a>
                                    </li>
                                    <li class="previous" v-if='pageNum <CopyPages.count'>
                                        <a href="javascript:void(0)" @click="next('viewStu',pageNum)">›</a>
                                    </li>
                                    <li class="previous disabled" v-else>
                                        <a href="javascript:void(0)">›</a>
                                    </li>
                                    <li v-if='pageNum <CopyPages.count'>
                                        <a href="javascript:void(0)" @click="plus('viewStu',CopyPages.count)" aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                    <li v-else class="disabled">
                                        <a aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                </ul>
                                <div class='summary mb10'>第 <span v-if='pageNum*CopyPages.limit-CopyPages.limit==0'>1</span><span v-else>{{pageNum*CopyPages.limit-CopyPages.limit}}</span>-{{pageNum*CopyPages.limit}} 条, 共 {{CopyPages.total}} 条.</div>
                            </nav>
                        </div>
                    </div>
                </div>
                
                 
            </div>
        </div>
        <div style='position: fixed; z-index: 10000;display:none' class='nextImg'>
            <div id='prev' class='viewer-prev'></div>
            <div id='next' class='viewer-next'></div>
        </div>
        <!-- 查看内容 -->
        <div class="modal fade" id="contentModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "Content");?></h4>
                    </div>
                    <div class="modal-body" v-if='contentData.journalData' >
                        <div class="col-md-12 col-sm-12"  style='min-height:400px' ref='contentShow'  >
                            <div><span class="label label-default tagLabel  color6"><?php echo Yii::t("newDS", "School Year"); ?>：{{contentData.journalData.start_year}}-{{contentData.journalData.start_year+1}}</span></div>
                            <div class='font20 color3 mt24'><label>{{contentData.journalData.title}}</label> </div>
                            <div v-if='contentData.journalData.category==3 || contentData.journalData.category==4'>
                                <div v-if='contentData.journalData.category==3'>
                                <div class="media mt24">
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                            <img :src="contentData.userInfo[contentData.journalData.sign_as_uid].photoUrl" data-holder-rendered="true" class="contentAvatar">
                                            </a>
                                        </div>
                                        <div class="media-body pt8 media-middle">
                                            <h4  class="media-heading font12">{{contentData.userInfo[contentData.journalData.sign_as_uid].name}}</h4>
                                            <div class='text-muted'>{{contentData.userInfo[contentData.journalData.sign_as_uid].hrPosition}}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class='mt24' v-if='contentData.journalData.category==4'>
                                    <div class='mt24 mb24 breakAll' v-html='contentData.deptData.deptDesc'></div>
                                    <div v-if='contentData.deptData.staffs.length!=0'>                                
                                        <p class='color3 font14'><strong><?php echo Yii::t("directMessage", "Team members"); ?></strong> <span class="badge bg7 ml5">{{contentData.deptData.staffs.length}}</span></p>
                                        <div v-for='(list,index) in contentData.deptData.staffs' class='mt8 mb8' >
                                            <div class="media">
                                                <div class="media-left pull-left media-middle">
                                                    <a href="javascript:void(0)">
                                                        <img class="contentAvatar" :src="contentData.userInfo[list].photoUrl" data-holder-rendered="true" >
                                                    </a>
                                                </div>
                                                <div class="media-body pt8 media-middle">
                                                    <h4  class="media-heading font12">{{contentData.userInfo[list].name}}</h4>
                                                    <div class='text-muted'>{{contentData.userInfo[list].hrPosition}}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else>
                                
                                <div class="media mt24">
                                    <div class="media-left pull-left media-middle">
                                        <a href="javascript:void(0)">
                                            <img class="contentAvatar" :src="contentData.userInfo[contentData.journalData.sign_as_uid].photoUrl" data-holder-rendered="true" >
                                        </a>
                                    </div>
                                    <div class="media-body pt8 media-middle">
                                        <h4  class="media-heading font12">{{contentData.userInfo[contentData.journalData.sign_as_uid].name}}<span class="label label-default color6 tagLabel  ml5"><?php echo Yii::t("directMessage", "Signature"); ?></span></h4>
                                        <div class='text-muted'>{{contentData.journalData.sign_as_title}}</div>
                                    </div>
                                </div>
                                <div class='mt24 breakAll' v-html='contentData.journalData.content'></div>
                                <div class='mt24'  v-if='contentData.journalData.attachments.length!=0'>
                                    <p class='color3 font14'><strong><?php echo Yii::t("curriculum", "Attachments"); ?></strong> <span class="badge bg7 ml5">{{contentData.journalData.attachments.length}}</span></p>
                                    <div v-for='(list,index) in contentData.journalData.attachments' class='mt16' style='word-break:break-all;'>
                                        <a :href="list.file_key" target='_blank'><span class='glyphicon glyphicon-paperclip mr4'></span>{{list.title}}</a>    
                                    </div>
                                </div>
                                <div class='mt24'>
                                    <p class='color3 font14'><strong><?php echo Yii::t("newDS", "Subscribers"); ?></strong> <span class="badge bg7 ml5">{{contentData.journalData.targets_num}}</span></p>
                                    <div class='mt16'>
                                        <img class="contentPostObject mr16 mb8"  v-for='(list,index) in contentData.journalData.targets'  :src="contentData.childData[list].avatar" data-holder-rendered="true" >    
                                        <span class='postMore cur-p' v-if='contentData.journalData.targets_num>10' @click='showMore(contentData.journalData)'>+ {{contentData.journalData.targets_num-10}} ></span>
                                    </div>
                                </div>
                                <div class='mt24' v-if='contentData.journalData.joint_admins.length!=0'>
                                    <p class='color3 font14'><strong><?php echo Yii::t("directMessage", "Collaboration"); ?></strong> <span class="badge bg7 ml5">{{contentData.journalData.joint_admins.length}}</span></p>
                                    <div v-for='(list,index) in contentData.journalData.joint_admins' class='mt8 mb8' >
                                        <div class="media">
                                            <div class="media-left pull-left media-middle">
                                                <a href="javascript:void(0)">
                                                    <img class="contentAvatar" :src="contentData.userInfo[list].photoUrl" data-holder-rendered="true" >
                                                </a>
                                            </div>
                                            <div class="media-body pt8 media-middle">
                                                <h4  class="media-heading font12">{{contentData.userInfo[list].name}}</h4>
                                                <div class='text-muted'>{{contentData.userInfo[list].hrPosition}}</div>
                                            </div>
                                        </div>    
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class='clearfix'></div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 查看全部发布对象 -->
        <div class="modal fade" id="postObjectModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "查看全部发布对象"); ?></h4>
                    </div>
                    <div class="modal-body" >
                        <div class="form-group">
                            <div class="input-group">
                            <div class="input-group-addon"><span class='glyphicon glyphicon-search'></span></div>
                            <input type="text" class="form-control" v-model='search' placeholder="请输入姓名">
                            </div>
                        </div>
                        <div class='font14'>
                            <span class='glyphicon glyphicon-user color6'> </span><span class='color3 pl4'>共{{Object.keys(searchData).length}}人</span>
                        </div>
                        <div>
                            <div class="media mt10 mb10 col-md-6 col-sm-6"  v-for='(list,key,index) in searchData' >
                                <div class="media-left pull-left media-middle">
                                    <a href="javascript:void(0)">
                                        <img class="contentAvatar" :src="list.avatar" data-holder-rendered="true" >
                                    </a>
                                </div>
                                <div class="media-body pt8 media-middle">
                                    <h4  class="media-heading font12">{{list.name}}</h4>
                                    <div class='text-muted'>{{list.class_name}}</div>
                                </div>
                            </div>  
                            <div class='clearfix'></div>  
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 标记为未回复 -->
        <div class="modal fade" id="noReplyModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
            <div class="modal-dialog modal-sm" role="document">
                <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "Warning");?></h4>
                </div>
                <div class="modal-body">
                    <div v-if='joint_admins_count!=0'>
                        <div><?php echo Yii::t("directMessage", 'This DM involves multi collaborators. This operation will cause everyone to no longer receive unreplied reminders about this feedback. Are you sure to continue?');?> </div>
                        <div class="checkbox">
                            <label>
                                <input type="checkbox" v-model='noReplySure'><?php echo Yii::t("directMessage", 'Yes, pretty sure');?>
                            </label>
                        </div>
                    </div>
                    <div v-else><?php echo Yii::t("newDS", 'Proceed to mark it as "No Reply Needed"?');?></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel");?></button>
                    <button type="button" class="btn btn-primary" @click='reply("noReply")'><?php echo Yii::t("message", "OK");?></button>
                </div>
                </div>
            </div>
        </div>
        <!-- 移交 -->
        <div class="modal fade" id="forwardModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("directMessage", "Handover");?></h4>
                </div>
                <div class="modal-body p24" >
                    <div class='row'>
                        <div class='col-md-6 borderRight' style='min-height:480px' v-if='inputForward.length!=0 && childid!=0'>
                            <div><label class='font14'><?php echo Yii::t("directMessage", "Handover Content");?></label></div>
                            <div class='color6'><?php echo Yii::t("directMessage", "Multiple messages will be merged.");?></div>
                            <div class='flex mt15'>
                                <div>
                                    <img :src="feedback.childInfo[childid].avatar" height="50" width="50" class="img-circle" style="object-fit: cover;">
                                </div>
                                <div class='flex1 ml15'>
                                    <div class='color3 font14 mt5'>{{feedback.childInfo[childid].name}}</div>
                                    <div class='color6 mt5'>{{feedback.childInfo[childid].class_name}}</div>
                                    <div class='forwardText mt20'>
                                        <div v-for='(item,index) in inputForward' class=''>
                                            <div class='color3 font14 mb12' v-html='html(feedbackItem.items[item].content)'></div>
                                            <div>
                                                <ul class='mb12 imgLi' v-if='feedbackItem.items[item].imgUrl.length!=0'>
                                                    <li v-for='(list,j) in feedbackItem.items[item].imgUrl'>
                                                        <img :src="list.url" class='fileImg mr8' alt=""  >
                                                    </li>
                                                </ul>
                                                <div  v-if='feedbackItem.items[item].videoUrl.length!=0'>
                                                    <div v-for="(list, j) in feedbackItem.items[item].videoUrl" :key="list.id">
                                                        <div v-if="list.video_convert_status == 0" class="flex fileLink">
                                                            <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" /> 
                                                            <a class='flex1 ml5' target= "_blank" :href="list.url"><?php echo Yii::t('message', "Convert") ?></a>
                                                        </div>
                                                        <video v-else :src="list.url" :poster="list.video_cover"  controls muted preload="auto" style="width: 100%; height: 100%; fit: fill"></video>
                                                    </div>
                                                </div>
                                                <div >
                                                    <div class='flex fileLink' v-for='(list,j) in feedbackItem.items[item].pdfUrl'>
                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/PDF1.png' ?>" alt="" class="pdfImg" v-if='list.mimetype=="application/pdf"' />
                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/excel.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/vnd.ms-excel" || list.mimetype=="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"' />
                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/word1.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/msword" || list.mimetype=="application/vnd.openxmlformats-officedocument.wordprocessingml.document"' />
                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/zip.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/x-zip-compressed"' />
                                                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" v-else /> 
                                                        <a class='flex1 ml5' target= "_blank" :href="list.url">{{list.title}}
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class='col-md-6'>
                            <div class='mt16 mb16'>
                                <label class="radio-inline">
                                    <input type="radio" v-model="forwardType" value="3"> <?php echo Yii::t("directMessage", "Handover to staff");?> 
                                </label>
                                <label class="radio-inline">
                                    <input type="radio"  v-model="forwardType" value="4"> <?php echo Yii::t("directMessage", "Handover to a team");?>
                                </label>
                            </div>
                            <div class='mb20' v-if='forwardType==3'>
                                <el-select v-model="forwardTo" placeholder="<?php echo Yii::t("directMessage", "Handover to staff");?> " class='optionSearch mb8'>
                                    <el-option
                                        v-for="item in teacherList"
                                        :key="item.uid"
                                        class='optionSearch mb8'
                                        :label="item.name"
                                        :value="item.uid">
                                        <div class="media">
                                            <div class="media-left pull-left media-middle">
                                                <a href="javascript:void(0)">
                                                    <img :src="item.photoUrl" data-holder-rendered="true" class="media-object img-circle contentAvatar">
                                                </a>
                                            </div>
                                            <div class="media-body mt5 media-middle">
                                                <h4 class="media-heading font14 color3 text_overflow">{{item.name}}</h4>
                                                <div class="color6  text_overflow font12">{{ item.hrPosition }}</div>
                                            </div>
                                        </div>
                                    </el-option>
                                </el-select>
                            </div>
                            <div class='mb20' v-if='forwardType==4'>
                                <el-select v-model="forwardTo" placeholder="<?php echo Yii::t("directMessage", "Handover to a team");?>" class='optionSearch mb8'>
                                    <el-option
                                        class='optionSearch mb8'
                                        v-for="item in deptList"
                                        :key="item.id"
                                        :label="item.name"
                                        :value="item.id">
                                        <h4 class="media-heading font14 color3 text_overflow">{{item.name}}</h4>
                                    </el-option>
                                </el-select>
                            </div>
                            <div>
                                <div class='mb16'><span class='font14 color3'><?php echo Yii::t("labels", "Memo");?></span> <span class='color6 font12 ml10'><?php echo Yii::t("directMessage", "Only handover target can see the memo");?></span></div>
                                <textarea class="form-control" rows="3" v-model='forwardMark'></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button" class="btn btn-primary" :disabled='btnDis' @click='repost'><?php echo Yii::t("directMessage", "Confirm to handover");?></button>
                </div>
                </div>
            </div>
        </div>
        <div class="modal fade" id="originalInfoModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("directMessage", "Original Information");?></h4>
                </div>
                <div class="modal-body p24" >
                    <div class='flex mt15' v-if='childid!=0'>
                        <div>
                            <img :src="feedback.childInfo[childid].avatar" height="50" width="50" class="img-circle" style="object-fit: cover;">
                        </div>
                        <div class='flex1 ml15'>
                            <div class='color3 font14 mt5'>{{feedback.childInfo[childid].name}}</div>
                            <div class='color6 mt5'>{{feedback.childInfo[childid].class_name}}</div>
                            <div class='forwardText mt20'>
                                <div v-for='(list,index) in repostDetailList.commentList' class=' pb16 pt16' :class='repostDetailList.commentList.length!=(index+1)?"borderBto":""'>
                                    <div class='color3 font14 mb12' v-html='html(list.content)'></div>
                                    <div>
                                        <ul class='mb12 imgLi' :id='tabType+"_"+list.id' v-if='list.imgUrl.length!=0'>
                                            <li v-for='(list,j) in list.imgUrl'>
                                                <img :src="list.url" class='fileImg mr8' @click='showImg(list)' alt=""  >
                                            </li>
                                        </ul>
                                        <div  v-if='list.videoUrl.length!=0'>
                                            <div v-for="(item, j) in list.videoUrl" :key="item.id">
                                                <div v-if="item.video_convert_status == 0" class="flex fileLink">
                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" /> 
                                                    <a class='flex1 ml5' target= "_blank" :href="item.url"><?php echo Yii::t('message', "Convert") ?></a>
                                                </div>
                                                <video v-else :src="item.url" :poster="item.video_cover"  controls muted preload="auto" style="width: 100%; height: 100%; fit: fill"></video>
                                            </div>
                                        </div>
                                        <div >
                                            <div class='flex fileLink' v-for='(item,j) in list.pdfUrl'>
                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/PDF1.png' ?>" alt="" class="pdfImg" v-if='item.mimetype=="application/pdf"' />
                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/excel.png' ?>" alt="" class="pdfImg" v-else-if='item.mimetype=="application/vnd.ms-excel" || item.mimetype=="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"' />
                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/word1.png' ?>" alt="" class="pdfImg" v-else-if='item.mimetype=="application/msword" || item.mimetype=="application/vnd.openxmlformats-officedocument.wordprocessingml.document"' />
                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/zip.png' ?>" alt="" class="pdfImg" v-else-if='item.mimetype=="application/x-zip-compressed"' />
                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" v-else /> 
                                                <a class='flex1 ml5' target= "_blank" :href="item.url">{{item.title}}
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class='color6 font12'>
                                        <div class='mb8'><?php echo Yii::t("directMessage", "Originally sent to: ");?>{{repostDetailList.contactInfo[list.link_id]}}</div>
                                        <div class='mb8'><?php echo Yii::t("directMessage", "Submitted at: ");?>{{list.updated_at}}</div>
                                        <div>
                                            <span v-if="feedback.childInfo[childid][`p_${list.created_by}`] == 'f'"><?php echo Yii::t("newDS", "From student's Dad");?></span>
                                            <span v-else><?php echo Yii::t("newDS", "From student's Mom");?> </span> 
                                        </div>
                                       
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                </div>
                </div>
            </div>
        </div>
        <div class="modal fade" id="linkedModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel">{{journal_title}}
                        <div class='font12 color6 pt10'>{{newDate(journal_updated_at)}}</div></h4>
                </div>
                <div class="modal-body" v-if='allChildid!=0'>
                    <div  class="comment-box mt20" v-if='Object.keys(linkData).length!=0'>
                        <div class="row" style="margin-right: 0;">
                            <div class="col-md-4">
                                <div class="list-group scroll-box"  style="max-height: 500px;overflow-y: auto; overflow-x: hidden; margin-bottom: 15px;">
                                    <a href="javascript:;" class="list-group-item" :class="{'active-1': allChildid==_childid}" v-for="(list,_childid,index) in linkData.childs" :key="_childid"  :id='"id"+_childid' @click.stop="itemAllChild(_childid)">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <img :src="linkData.childs[_childid].avatar" class="img-circle allImage" width="50">
                                            </div>
                                            <div class="col-md-9" style="margin-top: 5px;">
                                                <h4 class="list-group-item-heading">{{linkData.childs[_childid].name}}</h4>
                                                <p class="list-group-item-text">{{linkData.childs[_childid].class_name}}</p>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                            </div>
                            <div  class="col-md-8 active-1 scroll-box" style="padding: 15px; border-radius: 4px;max-height: 500px;overflow-y: auto; overflow-x: hidden; margin-bottom: 15px;"  id='scrollItem'>
                                <div class='scroll-box'  v-if='Object.keys(allFeedbackItem).length!=0'>
                                    <div style="margin-bottom: 15px;" v-for="(fItem, i) in allFeedbackItem.items" :key="i" :id='fItem.id'>
                                        <div v-if='fItem.mark_as_staff==0'>
                                            <div class="flex" v-if="fItem.creator_type == 'parent'">
                                                <div class="mr15 text-center">
                                                    <img :src="linkData.childs[allChildid].avatar" class="img-circle allImage" width="50">
                                                </div>
                                                <div class="flex1">
                                                    <div class="name-2">{{linkData.childs[allChildid].name}}</div>
                                                    <div class="content-2"  v-html='html(fItem.content)'></div>
                                                    <div  class='mt10'>
                                                        <ul class='mb12 imgLi' :id='tabType+"_"+fItem.id' v-if='fItem.imgUrl.length!=0'>
                                                            <li v-for='(list,j) in fItem.imgUrl'>
                                                                <img :src="list.url" class='fileImg mr8' @click='showImg(fItem)' alt=""  >
                                                            </li>
                                                        </ul>
                                                        <div  v-if='fItem.videoUrl.length!=0'>
                                                            <div v-for="(list, j) in fItem.videoUrl" :key="list.id">
                                                                <div v-if="list.video_convert_status == 0" class="flex fileLink">
                                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" /> 
                                                                    <a class='flex1 ml5' target= "_blank" :href="list.url"><?php echo Yii::t('message', "Convert") ?></a>
                                                                </div>
                                                                <video v-else :src="list.url" :poster="list.video_cover"  controls muted preload="auto" style="width: 100%; height: 100%; fit: fill"></video>
                                                            </div>
                                                        </div>
                                                        <div >
                                                            <div class='flex fileLink' v-for='(list,j) in fItem.pdfUrl'>
                                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/PDF1.png' ?>" alt="" class="pdfImg" v-if='list.mimetype=="application/pdf"' />
                                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/excel.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/vnd.ms-excel" || list.mimetype=="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"' />
                                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/word1.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/msword" || list.mimetype=="application/vnd.openxmlformats-officedocument.wordprocessingml.document"' />
                                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/zip.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/x-zip-compressed"' />
                                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" v-else /> 
                                                                <a class='flex1 ml5' target= "_blank" :href="list.url">{{list.title}}
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="text-muted" v-if="linkData.childs[allChildid][`p_${fItem.created_by}`] == 'f'"><?php echo Yii::t("newDS", "From student's Dad");?> {{ fItem.updated_at }}</div>
                                                    <div class="text-muted" v-if="linkData.childs[allChildid][`p_${fItem.created_by}`] == 'm'"><?php echo Yii::t("newDS", "From student's Mom");?> {{ fItem.updated_at }}</div>
                                                </div>
                                            </div>
                                            <div class="flex" v-if="fItem.creator_type == 'staff'">
                                                <div class="mr15 text-center">
                                                    <img :src="allFeedbackItem.staff[fItem.created_by].avatar" class="img-circle allImage" width="50">
                                                </div>
                                                <div class="flex1">
                                                    <div class="name-2">{{allFeedbackItem.staff[fItem.created_by].name}}</div>
                                                    <div class="content-2" v-html='html(fItem.content)'></div>
                                                    <div  class='mt10'>
                                                        <ul class='mb12 imgLi'  :id='tabType+"_"+fItem.id'  v-if='fItem.imgUrl.length!=0'>
                                                            <li v-for='(list,j) in fItem.imgUrl'>
                                                                <img :src="list.url" class='fileImg mr8' @click='showImg(fItem)' alt=""  >
                                                            </li>
                                                        </ul>
                                                        <div  v-if='fItem.videoUrl.length!=0'>
                                                            <div v-for="(list, j) in fItem.videoUrl" :key="list.id">
                                                                <div v-if="list.video_convert_status == 0" class="flex fileLink">
                                                                    <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" /> 
                                                                    <a class='flex1 ml5' target= "_blank" :href="list.url"><?php echo Yii::t('message', "Convert") ?></a>
                                                                </div>
                                                                <video v-else :src="list.url" :poster="list.video_cover"  controls muted preload="auto" style="width: 100%; height: 100%; fit: fill"></video>
                                                            </div>
                                                        </div>
                                                        <div >
                                                            <div class='flex fileLink' v-for='(list,j) in fItem.pdfUrl'>
                                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/PDF1.png' ?>" alt="" class="pdfImg" v-if='list.mimetype=="application/pdf"' />
                                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/excel.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/vnd.ms-excel" || list.mimetype=="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"' />
                                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/word1.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/msword" || list.mimetype=="application/vnd.openxmlformats-officedocument.wordprocessingml.document"' />
                                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/zip.png' ?>" alt="" class="pdfImg" v-else-if='list.mimetype=="application/x-zip-compressed"' />
                                                                <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/link.png' ?>" alt="" class="pdfImg" v-else /> 
                                                                <a class='flex1 ml5' target= "_blank" :href="list.url">{{list.title}}
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="text-muted">{{ fItem.updated_at }}</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div v-else class='p20 mt20 text-center borderTop ' :class='i!=allFeedbackItem.items.length-1?"borderBto":""'>
                                            <p class='font14 color3'>
                                                <span class='glyphicon glyphicon-pushpin mr10' style='color:#EC971FFF'></span>{{allFeedbackItem.staff[fItem.created_by].name}} <?php echo Yii::t("newDS", "marked No Reply Needed");?> <span class='font14 color6 ml10'>{{fItem.updated_at}}</span>
                                            </p>
                                            <div  class='font12 color9'>
                                                <?php echo Yii::t("newDS", "(This message is invisible to parents)");?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
       
    </div>
</div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    $(function () { $("[data-toggle='tooltip']").tooltip(); });
    
    var data = <?php echo json_encode($data); ?>;
    var staff = <?php echo CHtml::encode(Yii::app()->request->getParam('staff', 0)); ?>;
    var height=document.documentElement.clientHeight;
    var container=new Vue({
        el: "#container",
        data: {
            height:height,
            admin:'<?php echo $this->isAdmin(); ?>',
            loading: false,
            toReplyNum:0,
            opened: '',
            childid: 0,
            searchChildId:'',
            searchChildName:'',
            searchChildNameCopy:'',
            content: '',
            feedback: {},
            feedbackNonAlert: [],
            feedbackItem: {},
            allItems:{},
            linkData:{},
            replied:null,
            pageNum:'1',
            pages:{},
            CopyPages:{},
            journal_title:'',
            journal_updated_at:'',
            contentData:{},
            attachments:[],
            journalCategory:data.journalCategory,
            subjectList:data.subjectList,
            leaderuserList: data.leaderList,
            teacheruserList: {},
            gradeGroupList:data.gradeGroupList,
            tabType:'',
            commentChildId:'',
            selectedId:'',
            classTitle:'',
            classList:{},
            commentedIds:[],
            unCommentedIds:[],
            showChildName:false,
            studentInfo:{},
            commentedList:[],
            commentChildData:{},
            teacherInfo:{},
            classLoading:false,
            commentLoading:false,
            staff: staff,
            search:'',
            postObjectList:[],
            isForward:false,
            inputForward:[],
            forwardTo:'',
            options:[],
            forwardType:'',
            forwardMark:'',
            teacherList:[],
            deptList:[],
            initLoading:false,
            repostDetailList:[],
            allFeedbackItem:{},
            allChildid:0,
            forwardCategory:'',
            token:'',
            videoToken:'',
            uploadImgList:[],
            uploadLinkList:[],
            attachmentName:'',
            uploadShow:false,
            loadingList:[],
            loadingType:0,
            joint_admins_count:null,
            noReplySure:false,
            qrcode:'',
            wechatData:{},
            scanQrcodeBox:false,
            qrcodeBox:false,
            tab: '<?php echo isset($_GET['tab']) ? $_GET['tab'] : 'unreply';?>',
            btnDis:false
        },
        created: function() {
            this.showWechatQrcode()
            this.change(this.tab)
            $('#tab_'+this.tab).tab('show')
            let that=this
            $.ajax({
                url: '<?php echo $this->createUrl("unRepliedNum") ?>',
                type: "post",
                dataType: 'json',
                data: {
                },
                success: function(data) {
                    if (data.state == 'success') {
                        that.toReplyNum=data.data
                    }
                },
                error: function(data) {
                    
                },
            })
        },
        watch:{
            forwardType(old,newValue){
                if(old!=newValue){
                    this.forwardTo=''
                }
            }
        },
        computed: {
            searchData: function() {
                var search = this.search;
                var searchVal = ''; //搜索后的数据
                if(search) {
                    searchVal =Object.values(this.postObjectList).filter(function(product) {
                        return Object.keys(product).some(function(key) {
                            return String(product['name']).indexOf(search) !== -1;
                        })
                    })
                    return searchVal;
                }
                return this.postObjectList;
            }
        },
        methods: {
            html(data){
              return  data.replace(/\n/g, '<br/>')
            },
            newDate(dateTime){
                var dateString=''
                if(dateTime!=''){ 
                    let time = new Date(dateTime).getTime();
                    const date = new Date(time);
                    const Y = date.getFullYear() + '-';
                    const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
                    const D = (date.getDate() < 10 ? '0'+date.getDate() : date.getDate()) + '  ';
                    const h = (date.getHours() < 10 ? '0'+date.getHours() : date.getHours()) + ':';
                    const m = (date.getMinutes() <10 ? '0'+date.getMinutes() : date.getMinutes()) + ':';
                    const s = date.getSeconds(); // 秒
                    dateString = Y + M + D + h + m + s;
                }
                return dateString;
            },
            open(item) {
                this.forwardCategory=item.category
                if (this.opened == item.id) {
                    this.opened = ''
                }
                else {
                    if (this.content != '') {
                        if (!confirm('您填写的内容还未提交，确定切换到其他学生吗？')) {
                            return;
                        }
                    }
                    if (this.opened != item.id) {
                        this.opened = item.id;
                        this.childid = 0;
                        this.content = '';
                    }
                }
            },
            itemChild(childid,type) {
                if (this.content != '') {
                    if (!confirm('您填写的内容还未提交，确定切换到其他学生吗？')) {
                        return;
                    }
                }
                var _this = this;
                this.uploadImgList=[]
                this.uploadLinkList=[]
                this.childid = childid;
                this.feedbackItem={}
                this.content = '';
                this.loading = true
                _this.isForward=false
                _this.inputForward=[]
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/item") ?>',
                    type: 'get',
                    dataType: 'json',
                    data: {
                        id: _this.opened,
                        child_id: childid
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            _this.loading = false
                            _this.uploadShow=false
                            _this.getQiniu()
                            data.data.items.forEach((list) => {
                                list.imgUrl=[]
                                list.pdfUrl=[]
                                list.videoUrl=[]
                                if(list.attachments.length!=0){
                                    list.attachments.forEach((item) => {
                                        if(data.data.attachmentList[item]){
                                            let type=data.data.attachmentList[item].mimetype.split('/')[0]
                                            if(type=="image"){
                                            list.imgUrl.push(data.data.attachmentList[item])
                                            }else if(type=="video"){
                                                list.videoUrl.push(data.data.attachmentList[item])
                                            }else{
                                            list.pdfUrl.push(data.data.attachmentList[item])
                                            }
                                        }
                                    })
                                }
                            })
                            _this.feedbackItem = data.data 
                            _this.$nextTick(function () {
                                setTimeout(() => {
                                    if(_this.tabType=='unreply'){
                                        _this.$refs.scrollHeight[2].scrollTop = _this.$refs.scrollHeight[2].scrollHeight
                                    }
                                    if(_this.tabType=='replied'){
                                        _this.$refs.scrollHeight[1].scrollTop = _this.$refs.scrollHeight[1].scrollHeight
                                    }
                                    if(_this.tabType=='allUnreplied'){
                                        _this.$refs.scrollHeight[0].scrollTop = _this.$refs.scrollHeight[0].scrollHeight
                                    }
                                }, 500);
                            });
                        }
                        else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            _this.loading = false
                        }
                    }
                });
            },
            itemAllChild(childid) {
                var _this = this;
                this.allChildid = childid;
                this.uploadImgList=[]
                this.uploadLinkList=[] 
                this.allFeedbackItem={}
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/item") ?>',
                    type: 'get',
                    dataType: 'json',
                    data: {
                        id: _this.opened,
                        child_id: childid
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            data.data.items.forEach((list) => {
                                list.imgUrl=[]
                                list.pdfUrl=[]
                                list.videoUrl=[]
                                if(list.attachments.length!=0){
                                    list.attachments.forEach((item) => {
                                        if(data.data.attachmentList[item]){
                                            let type=data.data.attachmentList[item].mimetype.split('/')[0]
                                            if(type=="image"){
                                            list.imgUrl.push(data.data.attachmentList[item])
                                            }else if(type=="video"){
                                            list.videoUrl.push(data.data.attachmentList[item])
                                            }else{
                                            list.pdfUrl.push(data.data.attachmentList[item])
                                            }
                                        }
                                    })
                                }
                            })
                            _this.allFeedbackItem = data.data 
                            _this.$nextTick(function () {
                                setTimeout(() => {
                                    if(_this.tabType=='unreply'){
                                        _this.$refs.scrollHeight[2].scrollTop = _this.$refs.scrollHeight[2].scrollHeight
                                    }
                                    if(_this.tabType=='replied'){
                                        _this.$refs.scrollHeight[1].scrollTop = _this.$refs.scrollHeight[1].scrollHeight
                                    }
                                    if(_this.tabType=='allUnreplied'){
                                        _this.$refs.scrollHeight[0].scrollTop = _this.$refs.scrollHeight[0].scrollHeight
                                    }
                                }, 500);
                            });
                        }
                        else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    }
                });
            },
            reply(typeData) {
                var _this = this;
                if(this.loadingList.length!=0){
                    resultTip({
                        error: 'warning',
                        msg: '<?php echo Yii::t("directMessage", "Uploading");?>'
                    });
                    return
                }
                var imgIds=[]
                var pdfIds=[]
                var videoIds=[]
                var ids=[]
                this.uploadImgList.forEach((item) => {
                    ids.push(item._id)
                    imgIds.push({
                        url:item.file_key,
                        id:item._id,
                        mimetype:item.mimetype,
                        title:item.title
                    })
                })
                this.uploadLinkList.forEach((item) => {
                    ids.push(item._id)
                    let type=item.mimetype.split('/')[0]
                    if(type=='video'){
                        videoIds.push({
                            url:item.file_key,
                            id:item._id,
                            mimetype:item.mimetype,
                            title:item.title,
                            video_convert_status: item.video_convert_status,
                            video_cover: item.video_cover,
                        })
                    }else{
                        pdfIds.push({
                            url:item.file_key,
                            id:item._id,
                            mimetype:item.mimetype,
                            title:item.title
                        })
                    }
                })
                if(typeData=='noReply' && this.joint_admins_count!=0){
                    if(!this.noReplySure){
                        resultTip({
                            error: 'warning',
                            msg: '<?php echo Yii::t("newDS", "Please confirm");?>'
                        });
                        return
                    }
                }
                if((_this.tabType=='unreply' || _this.tabType=='replied') && typeData!='noReply'){
                    if(ids.length==0 && _this.content==''){
                        resultTip({
                            error: 'warning',
                            msg: '<?php echo Yii::t("directMessage", "Content cannot be empty.");?>'
                        });
                        return
                    }
                }
                this.loading = true
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/saveComment") ?>',
                    type: 'post',
                    dataType: 'json',
                    data: {
                        id: _this.opened,
                        child_id: _this.childid,
                        content: _this.content,
                        mark_as_staff:typeData=='noReply'?1:0,
                        attachments:ids
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            _this.feedbackItem.items.push({
                                id: data.data._id,
                                content: data.data.content,
                                created_by: data.data.created_by,
                                creator_type: data.data.creator_type,
                                updated_at: data.data.created_at_format,
                                mark_as_staff:data.data.mark_as_staff,
                                imgUrl:imgIds,
                                pdfUrl:pdfIds,
                                videoUrl:videoIds
                            })
                            _this.loading = false
                            _this.content = '';
                            _this.feedbackNonAlert.push(_this.childid)
                            if(typeData=='noReply'){
                                $('#noReplyModal').modal('hide')
                            }
                             _this.count()
                             _this.uploadImgList=[]
                             _this.uploadLinkList=[]
                        }
                        else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            _this.loading = false
                        }
                    }
                });
            },
            count(){
                if(Number(this.replied)!=0){
                    this.replied=Number(this.replied) - 1
                }
                if(this.replied==0){
                    this.toReplyNum=0
                }
                for(var i=0;i<this.feedback.items.length;i++){
                    if(this.feedback.items[i].id==this.opened){
                        if(Number(this.feedback.items[i].count)!=0){
                            this.feedback.items[i].count=Number(this.feedback.items[i].count) - 1
                        }
                    }
                }
            },
            change(type,page) {
                this.feedback={}
                this.tabType=type
                this.loading = true
                this.opened = '';
                this.childid = 0;
                this.content = '';
                var _this = this;
                this.linkData={}
                if(!page){
                    _this.pageNum='1'
                }
                if (type == 'unreply') {
                    $.ajax({
                        url: '<?php echo $this->createUrl("unrepliedList") ?>',
                        type: 'get',
                        dataType: 'json',
                        data:{
                            staff:staff
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                _this.replied=data.data.count
                                _this.feedback = data.data;
                                _this.loading = false;
                                _this.initLoading = true;
                                _this.$forceUpdate()
                            }
                            else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                                _this.loading = false

                            }
                        }
                    });
                }
                else if (type == 'allUnreplied') {
                    $.ajax({
                        url: '<?php echo $this->createUrl("journals/AllUnreply") ?>',
                        type: 'get',
                        dataType: 'json',
                        success: function(data) {
                            if (data.state == 'success') {
                                _this.feedback = data.data
                                _this.feedback.childInfo=data.data.childs
                                _this.loading = false
                            }
                            else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                                _this.loading = false
                            }
                        }
                    });
                }
                else if (type == 'replied') {
                    $.ajax({
                        url: '<?php echo $this->createUrl("repliedList") ?>',
                        type: 'get',
                        dataType: 'json',
                        data:{
                            page:_this.pageNum,
                            staff:staff
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                _this.pageNum=data.data.pages.page
                                _this.pages=data.data.pages
                                _this.feedback = data.data
                                _this.loading = false
                                if(!page){
                                    _this.pageNum='1'
                                    _this.initPage()
                                }
                            }
                            else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                               _this.loading = false

                            }
                        }
                    });
                }
                else if (type == 'all') {                  
                    $.ajax({
                        url: '<?php echo $this->createUrl("journals/all") ?>',
                        type: 'get',
                        dataType: 'json',
                        data:{
                            page:_this.pageNum,
                            child_id:this.searchChildId
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                            //    _this.pageNum=data.data.pages.page
                               _this.CopyPages=data.data.pages
                               _this.allItems=data.data
                               _this.loading = false
                               if(!page){
                                    _this.pages=data.data.pages
                                    _this.pageNum='1'
                                    _this.initPage()
                                }
                            }
                            else {
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                                _this.loading = false
                            }
                        }
                    });
                }else if(type=='viewStu'){
                    this.commentList(this.commentChildId,_this.pageNum)
                }
            },
            handleSelect(item) {
                if(item.id){
                    this.searchChildName=item.name
                    this.searchChildNameCopy=JSON.parse(JSON.stringify(item.name))
                    this.searchChildId=item.id
                }
            },
            confirmAdmin(type){
                if(type!='search'){
                    this.searchChildId=''
                }
                this.change('all') 
            },
            querySearchAsync(query,cb) {
                let that=this
                if(this.options.length && this.searchChildNameCopy==query){
                    cb(this.options);
                    return
                }
                if (query && query != '') {
                    this.loading = true;
                    this.searchChildNameCopy=JSON.parse(JSON.stringify(query))

                    $.ajax({
                        url: '<?php echo $this->createUrl("journals/searchStudent") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            child_name:query
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.options = Object.values(data.data) ;
                                if(that.options.length==0){
                                    that.options=[{not:true,name:'<?php echo Yii::t('ptc', 'No Data') ?>'}]
                                }
                                cb( that.options);
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                            that.loading = false;
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.loading = false;
                        },
                    })
                } else {
                    cb([]);
                    if(this.options.length){
                        this.searchChildId=''
                        this.options = [];
                        this.change('all') 
                    }
                }
            },
            initPage(){
                var _this = this;
                _this.CopyPages=JSON.parse(JSON.stringify(_this.pages))
                if(_this.CopyPages.count>=10){
                    _this.pages.count=[1,2,3,4,5,6,7,8,9,10]
               }else{
                    var numPage=[]
                    for(var i=1;i<=_this.CopyPages.count;i++){
                        numPage.push(i)
                    }
                    _this.pages.count=numPage
               }
            },
            plus(type, index) { 
                var _this = this;
                _this.pageNum = Number(index)
                this.pagesSize(type)
                
                if(type=='viewStu'){
                    this.commentList(this.commentChildId,'page')
                }else{
                    this.change(type, 'page') 
                }
            },
            pagesSize(type){
                var _this = this;
                if(_this.pageNum<10){
                    if(_this.CopyPages.count>=10){
                        _this.pages.count=[1,2,3,4,5,6,7,8,9,10]
                    }else{
                        var numPage=[]
                        for(var i=1;i<=_this.CopyPages.count;i++){
                            numPage.push(i)
                        }
                        _this.pages.count=numPage
                    }
                }else if(_this.pageNum<=_this.CopyPages.count){
                    if(_this.CopyPages.count-_this.pageNum>=4){
                        var minPage=_this.pageNum-5
                        var maxPage=_this.pageNum+4
                    }else{
                        var minPage=_this.pageNum-(9-((_this.CopyPages.count-_this.pageNum)))
                        var maxPage=_this.pageNum+(_this.CopyPages.count-_this.pageNum)
                    }
                    var numPage=[]
                    for(var i=minPage;i<=maxPage;i++){
                        numPage.push(i)
                    }
                    _this.pages.count=numPage
                }
            },
            next(type, index){
                var _this = this;
                _this.pageNum = Number(index) + 1
                this.pagesSize(type)
                if(type=='viewStu'){
                    this.commentList(this.commentChildId,'page')
                }else{
                    this.change(type, 'page') 
                }
            },
            prev(type, index) {
                var _this = this;
                _this.pageNum = Number(index) - 1
                this.pagesSize(type)
                if(type=='viewStu'){
                    this.commentList(this.commentChildId,'page')
                }else{
                    this.change(type, 'page') 
                }
            },
            linked(list){
                var _this = this;
                this.opened=list.journal_id
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/viewComment") ?>',
                    type: 'get',
                    dataType: 'json',
                    data: {
                        id: list.id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            _this.allChildid = data.data.current_child_id;
                            _this.linkData=data.data
                            data.data.items.forEach((list) => {
                                list.imgUrl=[]
                                list.pdfUrl=[]
                                list.videoUrl=[]
                                if(list.attachments.length!=0){
                                    list.attachments.forEach((item) => {
                                        if(data.data.attachmentList[item]){
                                            let type=data.data.attachmentList[item].mimetype.split('/')[0]
                                            if(type=="image"){
                                            list.imgUrl.push(data.data.attachmentList[item])
                                            }else if(type=="video"){
                                            list.videoUrl.push(data.data.attachmentList[item])
                                            }else{
                                            list.pdfUrl.push(data.data.attachmentList[item])
                                            }
                                        }
                                    })
                                }
                            })
                            _this.allFeedbackItem.items = data.data.items
                            _this.allFeedbackItem.staff = data.data.staff
                            _this.journal_title=list.journal_title
                            _this.journal_updated_at=list.journal_updated_at
                            $('#linkedModal').modal('show')
                            _this.$nextTick(()=>{
                                _this.showlink(list) 
                            });
                        }
                        else {
                            alert(data.message)
                        }
                    }
                });
            },
            showlink(list){
                let that=this
                this.$nextTick(()=>{
                    setTimeout(() => {
                        let btn = document.getElementById(list.id)
                        let scrollItem = document.getElementById('scrollItem')
                        scrollItem.scrollTop = btn.offsetTop
                        var targetElement = document.querySelector('#id'+list.child_id);
                        if (targetElement) {
                            targetElement.scrollIntoView({behavior: 'smooth'});
                        }
                    }, 500);
                });
                
            },
            viewContent(journal_id){
                var that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("view") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        journal_id: journal_id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           that.contentData=data.data
                           $('#contentModal').modal('show')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            showMore(list){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("subscribers") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        journal_id:list.journal_id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.postObjectList=data.data
                            $('#postObjectModal').modal('show')
                        }
                    },
                    error: function(data) {
                        
                    },
                })
            },
            markNoReply(item){
                this.noReplySure=false
                this.joint_admins_count=item.joint_admins_count
                $('#noReplyModal').modal()
            },
            viewStudent(){
               let that=this
               this.CopyPages={}
                this.pages={}
                this.pageNum='1'
                this.loading=true
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/getCategoryList") ?>',
                    type: 'get',
                    dataType: 'json',
                    data: {
                        staff:this.staff
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.classList=data.data
                            that.teacherInfo=data.data.teacherInfo
                            that.loading=false

                        }
                        else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.loading=false
                        }
                    }
                });
            },
            messageStu(list){
                this.selectedId=list.id
                this.classTitle=list.title
                this.commentedList=[]
                this.commentChildId=''
                this.CopyPages={}
                this.pages={}
                this.pageNum='1'
                let that=this
                this.classLoading=true
                var dataList={}
                if(that.classList.type=='course'){
                    dataList={
                        'id': list.course_code,
	                    'type': that.classList.type,
                        'staff':this.staff
                    } 
                }else{
                    dataList={
                        'id': list.id,
	                    'type': that.classList.type,
                        'staff':this.staff
                    } 
                }
                this.showChildName=false
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/getChildList") ?>',
                    type: 'get',
                    dataType: 'json',
                    data:dataList,
                    success: function(data) {
                        if (data.state == 'success') {
                           if(data.data!=null){
                                that.commentedIds=data.data.commentedIds
                                that.studentInfo=data.data.studentInfo
                                that.unCommentedIds=data.data['non-commentedIds']
                           }else{
                                that.commentedIds=[]
                                that.studentInfo={}
                                that.unCommentedIds=[]
                           }
                           that.classLoading=false
                           that.showChildName=true
                        }
                        else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.classLoading=false
                            that.showChildName=true

                        }
                    },
                    error: function(data) {
                        that.classLoading=false
                        that.showChildName=true
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                });
            },
            commentList(list,page){
                this.commentChildId=list
                this.commentChildData=this.studentInfo[list]
                this.commentLoading=true
                let that=this
                if(!page){
                    that.pageNum='1'
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/getFeedbackByChild") ?>',
                    type: 'get',
                    dataType: 'json',
                    data: {
                        childId: list,
	                    pageNum:that.pageNum,
                        staff:this.staff
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            if(data.data!=null && data.data.length!=0){
                                that.CopyPages={
                                    count:data.data.pagination.pages,
                                    page: data.data.pagination.pageNum,
                                    total:data.data.pagination.total,
                                    limit:data.data.pagination.limit,
                                }
                                if(!page){
                                    that.pages={
                                        count:data.data.pagination.pages,
                                        page: data.data.pagination.pageNum,
                                        total:data.data.pagination.total,
                                        limit:data.data.pagination.limit,
                                    }
                                    that.pageNum='1'
                                    that.initPage()
                                }
                                data.data.commentList.forEach((_item) => {
                                    _item.items.forEach((list) => {
                                        list.imgUrl=[]
                                        list.pdfUrl=[]
                                        list.videoUrl=[]
                                        if(list.attachments.length!=0){
                                            list.attachments.forEach((item) => {
                                                if(data.data.attachmentList[item]){
                                                    let type=data.data.attachmentList[item].mimetype.split('/')[0]
                                                    if(type=="image"){
                                                    list.imgUrl.push(data.data.attachmentList[item])
                                                    }else if(type=="video"){
                                                    list.videoUrl.push(data.data.attachmentList[item])
                                                    }else{
                                                    list.pdfUrl.push(data.data.attachmentList[item])
                                                    }
                                                }
                                            })
                                        }
                                    })
                                })
                                that.commentedList=data.data.commentList
                            }
                            that.commentLoading=false
                        }
                        else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.commentLoading=false
                        }
                    },
                    error: function(data) {
                        that.commentLoading=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                });
            },
            forwardEvent(){
                this.isForward=true
            },
            forwardCancel(){
                this.isForward=false
                this.inputForward=[]
            },
            forwardTeacher(){
                if(this.inputForward.length==0){
                    resultTip({
                        error: 'warning',
                        msg:'<?php echo Yii::t("global", "Please Select");?><?php echo Yii::t("directMessage", "Handover Content");?>'
                    });
                    return
                }
                this.inputForward=this.inputForward.sort()
                this.forwardType=''
                this.forwardTo=''
                this.forwardMark=''
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("childTeacherList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        childId:this.childid
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.teacherList = Object.values(data.data) ;
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.loading = false;
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.loading = false;
                    },
                })
                $.ajax({
                    url: '<?php echo $this->createUrl("childDeptList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {},
                    success: function(data) {
                        if (data.state == 'success') {
                            that.deptList = Object.values(data.data) ;
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.loading = false;
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.loading = false;
                    },
                })
                $('#forwardModal').modal('show')
            },
            repost(){
                var comment_id_list=[]
                this.inputForward.forEach(item => {
                    comment_id_list.push(this.feedbackItem.items[item].id)
                });
                let that=this
                if(this.forwardType==''){
                    resultTip({
                        error: 'warning',
                        msg:'<?php echo Yii::t("global", "Please Select");?>移交类型'
                    });
                    return
                }
                if(this.forwardTo==''){
                    resultTip({
                        error: 'warning',
                        msg:'<?php echo Yii::t("global", "Please Select");?>移交人或者部门'
                    });
                    return
                }
                this.btnDis=true
                $.ajax({
                    url: '<?php echo $this->createUrl("repost") ?>',
                    type: 'post',
                    dataType: 'json',
                    data:{
                        comment_id_list:comment_id_list ,
                        repost_type: this.forwardType, 
                        repost_to: this.forwardTo,
                        repost_mark:this.forwardMark
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            $('#forwardModal').modal('hide')
                            that.initLoading = false;
                            that.change(that.tabType)
                            resultTip({
                                msg: data.message
                            });
                            that.btnDis=false
                        }
                        else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.btnDis=false
                        }
                    },
                    error: function(data) {
                        that.classLoading=false
                        that.btnDis=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                }); 
            },
            showOriginalInfo(id){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("repostDetail") ?>',
                    type: 'post',
                    dataType: 'json',
                    data:{
                        commentId:id,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            $('#originalInfoModal').modal('show')
                            data.data.commentList.forEach((list) => {
                                list.imgUrl=[]
                                list.pdfUrl=[]
                                list.videoUrl=[]
                                if(list.attachments.length!=0){
                                    list.attachments.forEach((item) => {
                                        if(data.data.attachmentsList[item]){
                                            let type=data.data.attachmentsList[item].mimetype.split('/')[0]
                                            if(type=="image"){
                                            list.imgUrl.push(data.data.attachmentsList[item])
                                            }else if(type=="video"){
                                            list.videoUrl.push(data.data.attachmentsList[item])
                                            }else{
                                            list.pdfUrl.push(data.data.attachmentsList[item])
                                            }
                                        }
                                    })
                                }
                            })
                          that.repostDetailList=data.data
                        }
                        else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        that.classLoading=false
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                }); 
            },
            getQiniu(){
                this.initUploader(1);
                this.initUploader(0);
            },
            initUploader(isVideo) {
                let that = this;
                let url = '<?php echo $this->createUrl("journals/getCommentQiniuToken") ?>';
                let data = {
                    linkId: that.opened,
                    linkType: 'journal'
                };

                if (isVideo) data.isVideo = 1;

                $.ajax({
                    url: url,
                    type: "POST",
                    dataType: 'json',
                    data: data,
                    success: function(res) {
                        if (res.state === 'success') {
                            if (isVideo) {
                                that.videoToken = res.data.data;
                            } else {
                                that.token = res.data.data;
                            }
                            that.uploadShow = true;
                            that.$nextTick(() => {
                                let btns = [];
                                if (that.tabType === 'unreply') {
                                    btns = isVideo ? [{id: 'pickfilesVideo', type: 'video'}] :
                                        [{id: 'pickfilesPhoto', type: 'photo'}, {id: 'pickfilesPDF', type: 'pdf'}];
                                } else if (that.tabType === 'replied') {
                                    btns = isVideo ? [{id: 'pickfilesVideo1', type: 'video'}] :
                                        [{id: 'pickfilesPhoto1', type: 'photo'}, {id: 'pickfilesPDF1', type: 'pdf'}];
                                } else if (that.tabType === 'allUnreplied') {
                                    btns = isVideo ? [{id: 'pickfilesVideo2', type: 'video'}] :
                                        [{id: 'pickfilesPhoto2', type: 'photo'}, {id: 'pickfilesPDF2', type: 'pdf'}];
                                }
                                btns.forEach(btn => {
                                    const mimeTypes = that.getMimeTypes(btn.type);
                                    let uploaderConfig = Object.assign({}, config, {
                                        token: isVideo ? that.videoToken : that.token,
                                        browse_button: btn.id,
                                        filters: mimeTypes
                                    });
                                    let uploader = new plupload.Uploader(uploaderConfig);
                                    uploader.init();
                                });
                            });
                        } else {
                            resultTip({ error: 'warning', msg: res.message });
                        }
                    },
                    error: function() {
                        resultTip({ error: 'warning', msg: '请求 Token 失败' });
                    }
                });
            },
            getMimeTypes(type) {
                let mimeTypes;
                if (type === 'photo') {
                    mimeTypes = [{
                        title: "<?php echo Yii::t('teaching', 'Image files'); ?>",
                        extensions: "jpg,gif,png,jpeg"
                    }];
                } else if (type === 'video') {
                    mimeTypes = [{
                        title: "<?php echo Yii::t('teaching', 'Video files'); ?>",
                        extensions: "mp4,avi,mov,wmv"
                    }];
                } else {
                    mimeTypes = [{
                        title: "<?php echo Yii::t('teaching', 'PDF and document files'); ?>",
                        extensions: "pdf,doc,xls,zip,xlsx,docx"
                    }];
                }
                return mimeTypes;
            },
            delImg(type,list,index){
                var that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/deleteAttachment") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        attachmentId:list._id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            if(type=='img'){
                                that.uploadImgList.forEach((item,index) => {
                                    if (item._id==list._id) {
                                        that.uploadImgList.splice(index, 1)
                                    } 
                                })
                            }else{
                                that.uploadLinkList.forEach((item,index) => {
                                    if (item._id==list._id) {
                                        that.uploadLinkList.splice(index, 1)
                                    } 
                                })
                            }
                            resultTip({
                                msg:data.state
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            saveFile(list,index) {
                var that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("journals/updateAttachmentName") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        attachmentId:list._id,
                        attachmentName:this.attachmentName

                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.uploadLinkList.forEach((item,index) => {
                                if (item._id==list._id) {
                                    Vue.set(that.uploadLinkList[index], 'title',data.data.title);
                                    Vue.set(that.uploadLinkList[index], 'file_key', data.data.url);
                                    Vue.set(that.uploadLinkList[index], 'isEdit', false);
                                } 
                            })
                            resultTip({
                                msg:'<?php echo Yii::t("newDS", "Data Saved!");?>'
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            showImg(list){
                var id=this.tabType+"_"+list.id 
                var viewer = new Viewer(document.getElementById(id),{
                    fullscreen: false,
                    title:false,
                    show:function(){ 
                        $('.nextImg').show()
                        if(list.imgUrl.length==1){
                            $('.viewer-prev').hide()
                            $('.viewer-next').hide()
                        }
                    },
                    hide:function(){ 
                        viewer.destroy()
                        $('.nextImg').hide()

                    }
                });
                document.getElementById('next').onclick = function() {
                    viewer.next();
                }
                document.getElementById('prev').onclick = function() {
                    viewer.prev();
                }
                $("#"+id).click();
            },
            showUnWechatQrcode(){
                let that=this
                if(this.scanQrcodeBox){
                    clearInterval(this.timer);
                    that.scanQrcodeBox=!that.scanQrcodeBox
                    return
                }else{
                    this.timer =setInterval(this.showWechatQrcode, 5000);   
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("teacherBindQrcode") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        staff_id:this.otherTeacherId==0?this.indexList.uid:this.otherTeacherId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.scanQrcodeBox=!that.scanQrcodeBox
                            that.$nextTick(() => {
                                $('#wechatQrcodeBind').qrcode({width: 124,height: 124,text:data.data});
                            })
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            showWechatQrcode(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("teacherBindInfo") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {},
                    success: function(data) {
                        if (data.state == 'success') {
                            that.wechatData=data.data
                            if(data.data.state==1){
                                if(that.scanQrcodeBox){
                                    that.scanQrcodeBox=false
                                    that.qrcodeBox=true
                                }
                                clearInterval(that.timer);
                            }
                        }
                    },
                    error: function(data) {

                    },
                })
            },
            unbind(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("teacherUnbind") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {},
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg:data.message
                            })
                            that.qrcodeBox=false
                            that.showWechatQrcode()
                        }else{
                            resultTip({
                                error: 'warning',
                                msg:data.message
                            })
                        }
                    },
                    error: function(data) {

                    },
                })
            },
        }
    })
    var config = {
        runtimes: 'html5,flash,html4', //上传模式,依次退化
        container: 'container', //上传区域DOM ID，默认是browser_button的父元素，
        browse_button: 'pickfilesPhoto', //上传选择的点选按钮，**必需**
        token: '',
        flash_swf_url: '/js/plupload/Moxie.swf', // flash的相对地址
        auto_start: true, //选择文件后自动上传，若关闭需要自己绑定事件触发上传
        // multi_selection: false, //不允许多选
        url:'https://upload-z1.qiniup.com',
        init: {
            'FilesAdded': function(up, files) {
                container.disabledUpload=true
                let fileType=files[0].type.split('/')
                if(fileType[0]=="image"){
                  container.loadingType=1
                }else{
                  container.loadingType=2
                }
                container.loadingList=files
                up.start();
            },
            BeforeUpload: function(up, file) {
                let fileType=file.type.split('/')
                let token=fileType[0]=="video"?container.videoToken:container.token
                up.setOption({
                    multipart_params:{token:token}
                })
                if(fileType[0]=="image"){
                    container.uploadImgList.push({types:'1'})
                }else{
                    container.uploadLinkList.push({title:' <?php echo Yii::t("directMessage", "Uploading");?>'})  
                }
                container.loadingList.splice(0,1)
            },
            UploadProgress: function(up, file) {
                $('#progress').css('width', file.percent+'%').html(file.percent+'%');
            },
            'FileUploaded': function(up, file, info) {
                var response = eval('('+info.response+')');
                if(info.status == 200){
                    let fileType=response.data.mimetype.split('/')
                    if(fileType[0]=="image"){
                        container.uploadImgList.splice(container.uploadImgList.length-1,1)
                        container.uploadImgList.push(response.data)
                    }else{
                        response.data.isEdit=false
                        container.uploadLinkList.splice(container.uploadLinkList.length-1,1)
                        container.uploadLinkList.push(response.data)
                    }
                }
            },
            'Error': function(up, err, errTip) {
                if(err.response){
                    var response = eval('('+err.response+')');
                }
                else{
                    var response = {message: err.message};
                }
                if(response.error == 'token not specified'){
                    up.stop();
                    resultTip({
                        error: 'warning',
                        msg: err.message
                    });
                }
            },
            'UploadComplete': function(up, file) {
                //队列文件处理完毕后,处理相关的事情
            }
        }
    };
    $(document).click(function(event) {
        container.qrcodeBox=false;
        container.scanQrcodeBox=false;
        clearInterval(container.timer);
    });
</script>
