<style>
	.control-label span{
		color:red;
	}
</style>
<?php 
$cfgs = $this->loadConfig();
$concerns = array();
if (isset($cfgs['concerns']))
{
    foreach ($cfgs['concerns'] as $k=>$v)
    {
        $concerns[$k] = (Yii::app()->language=='zh_cn') ? $v['cn'] : $v['en'];
    }
}
?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('//mcampus/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Basic'), array('//mcampus/default/index'))?></li>
        <li class="active">招生管理</li>
    </ol>
    <div class="row">
        <!-- 左侧菜单 -->
        <div class="col-md-2 col-sm-2">
            <?php
            $this->widget('zii.widgets.CMenu',array(
                'items'=> $this->getMenu(),
                'id'=>'visitForm',
                'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked background-gray'),
                'activeCssClass'=>'active',
                'itemCssClass'=>''
            ));
            ?>
        </div>
        <div class="col-md-10 col-sm-10">
			<?php $form=$this->beginWidget('CActiveForm', array(
				'id'=>'purchase-products-form',
				'enableAjaxValidation'=>false,
				'action' =>$this->createUrl('AddForm'),
				'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
			)); ?>
			<div class="modal-body">
				<div class="form-group">
					<label class="col-xs-2 control-label">预约时间 <span>*</span> </label>
					<div class="col-xs-8">
						<?php echo $form->textField($model,'visit_date',array('maxlength'=>255,'class'=>'form-control', 'id'=>'week')); ?>
					</div>
				</div>
				<div class="form-group">
					<label class="col-xs-2 control-label">每天接待时间 <span>*</span> </label>
					<div class="col-xs-8">
						<?php echo CHtml::dropDownList( 'day', '', $time, array('class'=>"form-control")); ?>
					</div>
				</div>
				<div class="form-group">
					<label class="col-xs-2 control-label">孩子姓名 <span>*</span> </label>
					<div class="col-xs-8">
						<?php echo $form->textField($model,'child_name',array('maxlength'=>255,'class'=>'form-control')); ?>
					</div>
				</div>
				<div class="form-group">
					<label class="col-xs-2 control-label">孩子生日 <span>*</span> </label>
					<div class="col-xs-8">
						<?php echo $form->textField($model,'birthdate',array('maxlength'=>255,'class'=>'form-control', 'id'=>'childBirthdate')); ?>
					</div>
				</div>
				<div class="form-group">
					<label class="col-xs-2 control-label">家长姓名 <span>*</span> </label>
					<div class="col-xs-8">
						<?php echo $form->textField($model,'parent_name',array('maxlength'=>255,'class'=>'form-control')); ?>
					</div>
				</div>
				<div class="form-group">
					<label class="col-xs-2 control-label">电话 <span>*</span> </label>
					<div class="col-xs-8">
						<?php echo $form->textField($model,'phone',array('maxlength'=>255,'class'=>'form-control')); ?>
					</div>
				</div>
				<div class="form-group">
					<label class="col-xs-2 control-label">电邮&nbsp;&nbsp;</label>
					<div class="col-xs-8">
						<?php echo $form->textField($model,'email',array('maxlength'=>255,'class'=>'form-control')); ?>
					</div>
				</div>
				<div class="form-group">
					<label class="col-xs-2 control-label">家庭住址&nbsp;&nbsp;</label>
					<div class="col-xs-8">
						<?php echo $form->textField($model,'address',array('maxlength'=>255,'class'=>'form-control')); ?>
					</div>
				</div>
				<div class="form-group">
					<label class="col-xs-2 control-label">接待语言 <span>*</span> </label>
					<div class="col-xs-8">
						<?php echo CHtml::dropDownList( 'receive_language', '', $language, array('class'=>"form-control")); ?>
					</div>
				</div>
				<div class="form-group">
					<label class="col-xs-2 control-label">家长关注&nbsp;&nbsp;</label>
					<div class="col-xs-8">
						 <?php echo $form->checkBoxList($model,'concerns',$concerns); ?>
					</div>
				</div>
				<div class="form-group">
					<label class="col-xs-2 control-label">您如何知道艾毅? <span>*</span> </label>
					<div class="col-xs-8">
						<?php echo CHtml::dropDownList( 'knowus', '', $knowusList, array('class'=>"form-control")); ?>
					</div>
				</div>
				<div class="form-group">
					<label class="col-xs-2 control-label">备注&nbsp;&nbsp;</label>
					<div class="col-xs-8">
						<?php echo $form->textArea($model,'memo',array('maxlength'=>255,'class'=>'form-control')); ?>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
				<button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
			</div>
			<?php $this->endWidget(); ?>
        </div>
    </div>
</div>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
function cbAddForm()
{
	location = '<?php echo $this->createUrl('form')?>';
}
	var modal = '<div class="modal fade" id="modal" tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog" role="document"><div class="modal-content"></div></div></div>';
	$('body').append(modal);
	$('#childBirthdate').datepicker({'dateFormat':'yy-mm-dd'});
	$( "#week").datepicker({
		minDate : 'd',
		dateFormat:'yy-mm-dd',
		beforeShowDay : function(date){
			<?php if($week || $day){?>
				<?php
				if($week){
				 foreach($week as $k=>$v){ ?>
					if(date.getDay() == <?php echo $v; ?>){
						var ret = true;
						var cssClass = "" ;
					}
				<?php } }?>
				<?php
				if($day){
                    foreach($day as $k=>$v){?>
                    if(date.getTime()/1000 == <?php echo (strtotime($v)); ?>){
                        var ret = true;
                        var cssClass = "" ;
                    }
				<?php }} ?>
				return [ret, cssClass, ""];
			<?php }else{?>
				return [false, '', ""];
			<?php }?>
		}
	});
</script>