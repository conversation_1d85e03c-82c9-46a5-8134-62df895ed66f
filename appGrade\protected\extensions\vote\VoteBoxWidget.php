<?php
/*
 * 结构参见 http://www.cnblogs.com/davidhhuan/archive/2012/01/09/2316851.html
*/
class VoteBoxWidget extends CWidget {

	/*
	 * 孩子ID
	*/
	public $childId = 0;
	/*
	 * 班级ID
	*/
	public $classId = 0;
	/*
	 * 学校ID
	*/
	public $schoolId = 0;

	/*
	 * 资源(images css js)的主目录
	*/
	public $assetsHomeUrl = null;
	/*
	 * 脚本文件 true 为默认 null 为不加载脚本文件
	*/
	public $scriptFile = true;
	/*
	 * css 文件 true 为默认 null 为不加载css文件
	*/
	public $cssFile = true;
	/*
	 * 视图文件名称
	*/
	public $view = 'voteBox';
	/*
	 * 初始化 设定属性的默认值
	* 此方法会被 CController::beginWidget() 调用
	*/
	public function init() {

		Yii::import('common.models.vote.*');
		if(empty($this->childId)){
			$this->childId  = $this->getController()->getChildId();
		}
		if(empty($this->classId)){
			$this->classId  = $this->getController()->myChildObjs[$this->childId]->classid;
		}
		if(empty($this->schoolId)){
			$this->schoolId  = $this->getController()->getSchoolId();
		}
		$cs = Yii::app()->getClientScript();
		if(empty($this->assetsHomeUrl)){
			$this->assetsHomeUrl = Yii::app()->getAssetManager()->publish(dirname(__FILE__).'/assets',false, -1, YII_DEBUG);
		}
		if($this->scriptFile == true){
			$this->scriptFile = '';
		}
		if($this->cssFile == true){
			$this->cssFile = 'votebox.css';
		}
		if(!empty($this->scriptFile)){
			$cs->registerScriptFile($this->assetsHomeUrl.'/js/'.$this->scriptFile,CClientScript::POS_HEAD);
		}
		if(!empty($this->cssFile)){
			//$cs->registerCssFile($this->assetsHomeUrl.'/css/'.$this->cssFile);
//			$cs->registerCssFile(Yii::app()->theme->baseUrl.'/css/vote_survey.css');
			$cs->registerCssFile(Yii::app()->theme->baseUrl.'/css/vote_survey.css?t='.Yii::app()->params['refreshAssets']);
		}

	}

	/*
	 * 此方法会被 CController::endWidget() 调用
	*/
	public function run() {

		$vi_cri = new CDbCriteria();
		$vi_cri->compare('t.whether_show', 0);
		$vi_cri->compare('t.school_id', $this->schoolId);
		$vi_cri->compare('permission.valid_object', $this->classId);
		$vote_infos = WVoteBasicInfo::model()->with('permission','options')->findAll($vi_cri);

		// 输出 变量到 视图文件
		$view_data = array(
				'vote_infos'=>$vote_infos,
		);

		$this->render($this->view,$view_data);
	}

}