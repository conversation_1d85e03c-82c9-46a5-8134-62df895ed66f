<div>
    <div class='flex align-items  mb24 mt10'>
        <span class='font14 color3'>处理人：</span>
        <div class='flex1 flex align-items lib_class_handle'>
        </div>
    </div>
    <div class='reject redColor'>
        <div class='font14'><span class='el-icon-warning mr5 font16'></span>已驳回</div>
        <div class='mt8'>
        驳回理由：<span class='rejectMemo'></span>
        </div>
    </div>
    <form class="J_ajaxForm formStu" action="<?php echo $this->createUrl("saveNode");?>" method="post" onsubmit="return check(this)">
        <p class='mt5 pt4'>
            <label class="radio-inline font14 color6 lableHeight">
                <input type="radio"  name="balance" value="1"> <?php echo Yii::t('withdrawal', 'Insured through Daystar, transferred out.'); ?>
            </label>
            <label class="radio-inline font14 color6 lableHeight ml24">
                <input type="radio"  name="balance" value="2"> <?php echo Yii::t('withdrawal', 'Insured through Daystar, not transferred out yet.'); ?>
            </label>
            <label class="radio-inline font14 color6 lableHeight ml24">
                <input type="radio"  name="balance" value="0"> <?php echo Yii::t('withdrawal', 'Not insured through Daystar.'); ?>
            </label>
        </p>
        <p class='explain hide'>
        </p>
        <textarea class="form-control" rows="3" placeholder="<?php echo Yii::t('withdrawal','Comment');?>" name="remark" id='remark'  class='font14'></textarea>
        <input type="hidden" name="type" id="type"  value='insurance'>
        <input type="hidden" name="applicationId" id="applicationId">
        <input type="hidden" name="node" id="node">
        <div class='modal-footer borderTopNone'>
            <label class="checkbox-inline">
                <input type="checkbox" name="status" value="1"> <?php echo Yii::t('withdrawal', 'Mark this step as completed'); ?>
            </label>
            <button type="submit" class="btn btn-primary ml24 submitSend"><?php echo Yii::t('global','Submit');?></button>
            <button type="button" class="btn btn-primary J_ajax_submit_btn ml24"><?php echo Yii::t('global','Submit');?></button>
        </div>
    </form>
</div>

<script>
   var explainList={
        1:'亲爱的家长您好，根据北京市社会医疗保险（一老一小）的要求，在校学生的社会医疗保险需要跟随学生的就读学校进行迁移。由于学生需要从启明星学校转出，我校将不再为其办理下一年度社会医疗保险的续保事宜。您孩子的社会医疗保险关系即日起将从本校转出，建议您尽快联系孩子的新学校或户口所在社区进行续保，以确保不会断保。',
        2:'亲爱的家长您好，根据北京市社会医疗保险（一老一小）的要求，学生的保险需要跟随学生的所在学校/学籍进行迁移。对于已经离校的学生，启明星学校原则上不再为学生办理一老一小保险的参保及续保手续。对于未到转出学籍窗口期，学生虽已离校，但学籍仍需暂时由我校代为保管的情况，需要家长知晓：1. 请家长在转出学籍窗口期尽快完成学籍转移，并同时联系校医室办理一老一小保险的转出。2. 由于学籍暂未转出原因，需要代管一老一小保险的最长代管期为一年，逾期将默认进行减员。3. 请家长主动联系校医室申请一老一小保险代管。未在学生转走一个月内联系校医室，学生的一老一小保险将默认进行减员。'
    }
     lib()
     $(document).ready(function() {
        $('input[name="amount[]"]').on('input', function() {
            var value = $(this).val();
            if (!/^[1-9]\d*|0$/.test(value)) {
                $(this).val('');
            }
        });
        
    });
     function lib(){
        let forms=childDetails.forms.find(item2 =>item2.key === 'insurance')
        for(var key in forms.owner_info){
            if (key !== '2' && key !== '5') {
            $('.lib_class_handle').append('<div class="flex align-items mr16"><img src='+forms.owner_info[key].photoUrl+' alt="" class="img28"/><div class="font14 color3 ml8">'+forms.owner_info[key].name+'</div></div>')
            }
        }
        $('#type').val(forms.key)
        if(forms.confirm_status==2){
            $('.reject').show()
            $('.rejectMemo').html(forms.reject_memo)
        }else{
            $('.reject').hide()
            $('.rejectMemo').html(forms.reject_memo)
        }
        if(forms.status=='1'){
            $('input[type="checkbox"][name="status"][value='+forms.status+']').prop('checked', true);
        }
        if(forms.data.balance){
            $('input[type="radio"][name="balance"][value='+forms.data.balance+']').prop('checked', true);
            if(forms.data.balance!='0'){
                $('.explain').html(explainList[forms.data.balance])
                $('.explain').removeClass('hide')
            }else{
                $('.explain').addClass('hide')
            }
        }
        if(forms.data.remark!=''){
            $('#remark').val(forms.data.remark);
        }
     }
     $('input[name="balance"]').click(function () {
        if ($(this).val() != '0') {
            $('.explain').html(explainList[$(this).val()])
            $('.explain').removeClass('hide')
        }
        else {
            $('.explain').addClass('hide')
        }
    })
    function delTable(_this){
         $(_this).parent().parent().remove();
     }
    function addTable(){
        $('.tbody').append('<tr> <td><input type="text" class="form-control" name="name[]" placeholder="<?php echo Yii::t('leave','Input');?>"></td><td><input type="number" min="0" step="0.01" class="form-control" name="amount[]" placeholder="<?php echo Yii::t('leave','Input');?>"></td><td><input type="text" class="form-control" name="memo[]" placeholder="<?php echo Yii::t('leave','Input');?>"></td><td><span class="glyphicon glyphicon-trash cur-p mt8" onclick="delTable(this)"></span></td></tr>');
    }
    $('.submitSend').show()
    $('.J_ajax_submit_btn').hide()
    function check(_this){
        let balanceId=$('input[name="balance"]:checked').val()
        $('#J_fail_info').remove();
        var button = $(_this).find('button[type="submit"]');
        var flag = true, msg=[];
        if(!balanceId){
            flag = false;
            msg.push('<?php echo Yii::t("teaching","请选择社保状态");?>');
        }
        if(flag){
            $('.submitSend').hide()
            $('.J_ajax_submit_btn').show()
            $('.formStu .J_ajax_submit_btn').click();
            return false;
        }
        else{
            $( '<span id="J_fail_info" class="text-warning"><i class="glyphicon glyphicon-remove text-warning"></i> ' + msg.join('、') + '！</span>' ).appendTo(button.parent()).fadeIn( 'fast' );
            return false;
        }
    }
</script>
<style>
    .explain{
        padding:8px 10px;
        background:#F7F7F8;
        border-radius:4px;
        margin-bottom:16px;
        color:#666
    }
</style>