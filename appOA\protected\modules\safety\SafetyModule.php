<?php

class SafetyModule extends CWebModule
{
	public function init()
	{
		// this method is called when the module is being created
		// you may place code here to customize the module or the application

		// import the module-level models and components
//		$this->setImport(array(
//			'charts.models.*',
//			'charts.components.*',
//		));
        Yii::import('common.models.asainvoice.*');
	}

	public function beforeControllerAction($controller, $action)
	{
        if(parent::beforeControllerAction($controller, $action))
        {
            // this method is called before any module controller action is performed
            // you may place customized code here
            return true;
        }
        else
            return false;
	}

    public function getMenu()
    {
        $action = Yii::app()->urlManager->parseUrl(Yii::app()->request);

        $mainMenu = array(
            array('label'=>Yii::t('asa','安全自查'), 'url'=>array("/safety/safety/index", 'type' => 'show'), 'active'=>in_array($action, array('safety/safety/index', 'safety/safety/showCheck'))),
            array('label'=>Yii::t('asa','事故上报'), 'url'=>array("/safety/safety/accident"), 'active'=>in_array($action, array('safety/safety/accident', 'safety/safety/update', 'safety/safety/showReport'))),
        );
        return $mainMenu;
    }
}
