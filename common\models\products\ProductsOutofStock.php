<?php

/**
 * This is the model class for table "ivy_products_outof_stock".
 *
 * The followings are the available columns in table 'ivy_products_outof_stock':
 * @property integer $id
 * @property string $school_id
 * @property integer $child_id
 * @property integer $cid
 * @property integer $pid
 * @property integer $aid
 * @property string $attr1
 * @property string $attr2
 * @property string $attr3
 * @property string $attr4
 * @property string $attr5
 * @property integer $register_num
 * @property integer $register_by
 * @property integer $register_at
 * @property string $register_openid
 * @property integer $status
 * @property integer $stock_id
 * @property integer $updated_by
 * @property integer $updated_at
 */
class ProductsOutofStock extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_products_outof_stock';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('school_id, cid, pid, aid, register_by, register_at', 'required'),
			array('child_id, cid, pid, aid, register_num, register_by, register_at, status, updated_by, updated_at, stock_id', 'numerical', 'integerOnly'=>true),
			array('school_id, attr1, attr2, attr3, attr4, attr5, register_openid', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, school_id, child_id, cid, pid, aid, attr1, attr2, attr3, attr4, attr5, register_num, register_by, register_at, register_openid, status, updated_by, updated_at', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'products' => array(self::HAS_ONE, 'Products', array('id'=>'pid')),
            'child' => array(self::HAS_ONE, 'ChildProfileBasic', array('childid'=>'child_id')),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'school_id' => 'School',
			'child_id' => 'Child',
			'cid' => 'Cid',
			'pid' => 'Pid',
			'aid' => 'Aid',
			'attr1' => 'Attr1',
			'attr2' => 'Attr2',
			'attr3' => 'Attr3',
			'attr4' => 'Attr4',
			'attr5' => 'Attr5',
			'register_num' => 'Register Num',
			'register_by' => 'Register By',
			'register_at' => 'Register At',
			'register_openid' => 'Register Openid',
			'status' => 'Status',
			'stock_id' => 'StockId',
			'updated_by' => 'Updated By',
			'updated_at' => 'Updated At',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('school_id',$this->school_id,true);
		$criteria->compare('child_id',$this->child_id);
		$criteria->compare('cid',$this->cid);
		$criteria->compare('pid',$this->pid);
		$criteria->compare('aid',$this->aid);
		$criteria->compare('attr1',$this->attr1,true);
		$criteria->compare('attr2',$this->attr2,true);
		$criteria->compare('attr3',$this->attr3,true);
		$criteria->compare('attr4',$this->attr4,true);
		$criteria->compare('attr5',$this->attr5,true);
		$criteria->compare('register_num',$this->register_num);
		$criteria->compare('register_by',$this->register_by);
		$criteria->compare('register_at',$this->register_at);
		$criteria->compare('register_openid',$this->register_openid,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('stock_id',$this->stock_id);
		$criteria->compare('updated_by',$this->updated_by);
		$criteria->compare('updated_at',$this->updated_at);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return ProductsOutofStock the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
