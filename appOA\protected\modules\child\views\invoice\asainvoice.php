<style>
.level0 .subms li a {
    display: block;
    padding-right:10px;
    line-height: 25px;
    flex:1;
    cursor:pointer;
}
.level0 .subms li{
    display:flex;
    padding-bottom:5px;
}
.level0 .subms li strong {
    width:30px;
    background: url(<?php echo Yii::app()->themeManager->baseUrl . '/modern/images/admin/layout/bg.png'?>);
    background-position: -265px 9px;
    background-repeat: no-repeat;
    height:25px;
}
.level0 .subms li a:hover{
	text-decoration:none;
}
.level0 .subms li:hover{
    background-color:#e9e9e9;
}
.level0 .subms li.current{
    background-color: #dbdada;
}
.level0 .subms li.current strong {
    background-position: -265px -11px;
}
.cursor{
    cursor:pointer;
    text-decoration:none;
}
#J_region_pop{
    width:800px;
    max-height:400px
}
.wind_dialog_mask{
    display:none;
}
.wrap{
    position: relative;
}
.text-right{
    text-align:right
}
.text-center{
    text-align:center
}
</style>
<div id="childinfo">
    <?php $this->widget('ext.childInfo.ChildInfo', array('childObj'=>$this->childObj))?>
</div>
<div class="table_c1">
<table width="100%">
	<colgroup>
		<col width='200'>
		<col width="null">
	</colgroup>
	<tbody>
		<tr>
			<td>
				<ul class="J_invoice-type" id="invoice-type">
                <li class="level0">
                    <span>课程组列表</span>
                    <ul class="subms">
                    </ul>
                </li>
                </ul>
	        </td>
			<td>
            <div id="gen_invoice">
                <div class="not_content_mini">
                    <i></i>请点击左侧课程组
                </div>
            </div>
            <div class="table_list groupData">
                <table width="100%" id="J_table_list" class="items">
                    <colgroup><col width="300"><col width="120"><col width="140"><col width="120"><col width="200"></colgroup>
                    <thead>
                        <tr>
                            <th>课程</th>
                            <th class='text-right'>单价</th>
                            <th class='text-right'>已购买课时</th>
                            <th class='text-right'>付款状态</th>
                            <th class='text-right'>总价</th>
                            <th class='text-center'>退费信息</th>
                        </tr>
                    </thead>
                    <tbody class='course'>
                    </tbody>
                </table>
            </div>
            </td>
        </tr>
    </tbody>
</table>
<div class="mb10 tac" id="btn">
    <button class="btn btn_submit mr10" type="button" onclick="openBlank()">课后课账单管理</button>
</div>
</div>
<div class="wind_dialog_mask" style="width: 100%; height: 100%; opacity: 0.6; background-color: rgb(255, 255, 255); z-index: 9; position: absolute; left: 0px; top: 0px;"></div>
<div class="not_content_mini noBills" style='display:none'>
    <i></i>暂无账单
</div>
<div class="core_pop_wrap" id="J_region_pop" style='display:none'>
	<div class="core_pop">
		<div style="width:800px;">
			<div class="pop_top">
				<a href="" id="J_region_pop_x" class="pop_close"><?php echo Yii::t('global','Close');?></a>
				<strong><?php ;?>退费详情</strong>
			</div>
			<div class="pop_cont">
				<div class="pop_table table_full" style="height:auto;">
                    <table width="100%">

                        <thead>
                            <tr>
                                <th  class="text-right">退费金额</th>
                                <th  class="text-right">退费课时</th>
                                <th  class="text-center">退课开始日期</th>
                                <th>退费原因</th>
                                <th>退费平台</th>
                                <th>退费状态</th>
                                <th width='150'>退费备注</th>
                            </tr>
                        </thead>
                        <tbody class='refundList'>
                        </tbody>
                    </table>
				</div>
			</div>
		</div>
	</div>
</div>
<script>
    var groupInfo=<?php echo json_encode($groupInfo); ?>;
    var invoiceInfo=<?php echo json_encode($invoiceInfo); ?>;
    $(function(){
        $('.groupData').hide()
        $('#btn').hide()
        if(groupInfo.length==0){
            $('.table_c1').hide()
            $('.noBills').show()
            return
        }
        var groupStr=''
        for(var i=0;i<groupInfo.length;i++){
            groupStr+='<li id='+groupInfo[i].id+'><strong></strong><a onclick="group(this,'+groupInfo[i].id+')">'+groupInfo[i].title+'</a></li>'
        }
        $('.subms').html(groupStr)
    });
    var groupId
    function group(obj,id){
        $(obj).parent().siblings().removeClass("current");
        $(obj).parent().addClass("current")
        $('#gen_invoice').hide()
        $('.groupData').show()
        $('#btn').show()
       var courseStr=''
       groupId=id
       for(var i=0;i<invoiceInfo[id].length;i++){
          courseStr+='<tr><td>'+invoiceInfo[id][i].courseTitle+'</td><td class="text-right">'+invoiceInfo[id][i].unit_price+'</td><td class="text-right">'+invoiceInfo[id][i].class_count+'</td><td class="text-right">'+invoiceInfo[id][i].invoice_status+'</td><td class="text-right">'+invoiceInfo[id][i].actual_total+'</td>'
          if(invoiceInfo[id][i].refund_info.length==0){
            courseStr+='<td class="text-center">'+invoiceInfo[id][i].refund_status+'</td></tr>'
          }else{
            courseStr+='<td class="text-center"><a class="mr5 cursor"  onclick="openD('+invoiceInfo[id][i].courseId+')">'+invoiceInfo[id][i].refund_status+'</a></td></tr>'
          }
       }
       $('.course').html(courseStr)
    }
    head.load('<?php echo Yii::app()->themeManager->baseUrl;?>/base/js/dialog/dialog.js', '<?php echo Yii::app()->themeManager->baseUrl;?>/base/js/util_libs/draggable.js', function(){
        region_pop = $('#J_region_pop');
        region_pop.draggable( { handle : '.pop_top'} );
        $('#J_region_pop_x, #J_region_pop_close').on('click', function(e){
            e.preventDefault();
            region_pop.hide();
            $('.wind_dialog_mask').hide()
        });

    });
function openD(courseId){
    var refundData=''
    for(var i=0;i<invoiceInfo[groupId].length;i++){
        if(invoiceInfo[groupId][i].courseId==courseId){
            refundData=invoiceInfo[groupId][i].refund_info
        }
    }
    var refundStr=''
    for(var j=0;j<refundData.length;j++){
        refundStr+='<tr><td class="text-right">'+refundData[j].refund_total_amount+'</td><td  class="text-right">'+refundData[j].refund_class_count+'</td><td  class="text-center">'+refundData[j].dropout_date+'</td><td>'+refundData[j].refund_reason+'</td><td>'+refundData[j].refund_method+'</td><td>'+refundData[j].refund_type+'</td>'
        if(refundData[j].memo==null || refundData[j].memo==''){
            refundStr+='<td></td></tr>'
          }else{
            refundStr+='<td>'+refundData[j].memo+'</td></tr>'
          }
    }
    $('.refundList').html(refundStr)
    $('.wind_dialog_mask').show()
    $('#J_region_pop').show().css({
        left : ($(window).width() - region_pop.outerWidth())/2,
        top : ($(window).height() - region_pop.outerHeight())/2
    });
}
function openBlank(){
    window.open("<?php echo $this->createUrl('/masa/invoice/index',array('branchId'=>$branchId, 'classid'=>$classid,'status'=>'on'));?>");
}
</script>
