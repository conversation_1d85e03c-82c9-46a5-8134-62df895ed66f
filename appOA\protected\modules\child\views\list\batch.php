<?php $this->renderPartial('header');?>
<?php if ($show == FALSE):?>
<div class="not_content_mini">
	<i></i>本功能仅每年4-8月、且在校历切换之前开放。
</div>
<?php else: ?>
<div class="table_list">
	<table width="100%" class="table_c1">
		<colgroup>
			<col width=220>
		</colgroup>
		<tbody>
			<tr>
				<td class="level0">
					<?php
						$nextClassList = OA::getClassListByYid($nextyid, $this->branchObj->branchid);
						$nextClassHtmlOpt = array();
						if(count($nextClassList)){
							$nextClassList[0] = Yii::t("campus", "Select a next year class");
							$nextClassHtmlOpt['options']=array('0'=>array("disabled"=>true));
						}else{
							$nextClassList[0] = Yii::t("campus", "No next year class created");
							$nextClassHtmlOpt['disabled']='disabled';
						}
                        $nextClassHtmlOpt['onchange']='batHandle(this, "a")';
					
						$switchMenu = array(
							'current'=>array(
								'label' => Yii::t("campus", "Current List"),
								'url' => array('//child/list/batch', 'flag'=>'current')
							),
							'new'=>array(
								'label' => Yii::t("site", "Newly Registered"),
								'url' => array('//child/list/batch', 'flag'=>'newreg')
							),
						);
						$this->widget('zii.widgets.CMenu', array(
							'id' => 'invoice-type',
							'items' => $switchMenu,
							'htmlOptions' => array('class'=>'subm'),
							'activeCssClass' => 'current'
							)
						);
					?>				
				</td>
				<td>
					<?php if ($calendar):?>
					
						<div class="h_a"><?php echo Yii::t("campus", "School Year");?></div>
						<div class="prompt_text">
							<ul>
								<li><?php echo $calendar->startyear . ' ~ ' . intval($calendar->startyear + 1);?></li>
							</ul>
						</div>
				
					<?php if($flag == "current"):
						$this->renderPartial("batch_current", array("calendar"=>$calendar, "nextClassList"=>$nextClassList, "nextClassHtmlOpt"=>$nextClassHtmlOpt));
					else:
						$this->renderPartial("batch_newreg", array("calendar"=>$calendar, "nextClassList"=>$nextClassList, "nextClassHtmlOpt"=>$nextClassHtmlOpt, "children"=>$children));
					endif;?>
				
				<?php endif;?>

				</td>
			</tr>
		</tbody>
	</table>
</div>
<script type="text/javascript">
function batHandle(_this, t)
{
    var classid = $(_this).attr('classid'), rData = new Array();
    var toClassid = t === 'a' ? $(_this).val() : 0;
    $('#classid-'+classid+' input.J_check:enabled:checked').each(function(index){
            rData[index]=$(this).val();
    });
    if (rData.length < 1){
        alert('<?php echo Yii::t("campus", "Select at least one child")?>');
        return false;
    }
    
    if (confirm('<?php echo Yii::t("child", "确定操作？")?>')){
        $.ajax({
            type: 'post',
            url: '<?php echo $this->createUrl('/child/list/processBat');?>',
            data: {t: t, ids: rData, classid: classid, toClassid: toClassid, flag: '<?php echo $flag?>'},
            dataType: 'json',
            success: function(data){
                if (data.state === 'success'){
                    location.reload();
                }
                else {
					if(typeof(data.data)!='undefined'){
						for(var i in data.data){
							$('#c_'+data.data[i]).addClass('tipRed');
						}					
					}
                    alert(data.message);
                }
            }
        });
    }
}
</script>
<?php endif;?>