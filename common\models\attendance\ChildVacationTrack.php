<?php

/**
 * This is the model class for table "ivy_child_vacation_track".
 *
 * The followings are the available columns in table 'ivy_child_vacation_track':
 * @property integer $id
 * @property integer $vid
 * @property string $school_id
 * @property integer $class_id
 * @property integer $child_id
 * @property string $old_info
 * @property string $track_memo
 * @property integer $track_type
 * @property integer $track_by
 * @property integer $track_at
 */
class ChildVacationTrack extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_child_vacation_track';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('vid, school_id, class_id, child_id, old_info, track_memo, track_type, track_by, track_at', 'required'),
			array('vid, class_id, child_id, track_type, track_by, track_at', 'numerical', 'integerOnly'=>true),
			array('school_id', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, vid, school_id, class_id, child_id, old_info, track_memo, track_type, track_by, track_at', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'user' => array(self::BELONGS_TO, 'User', 'track_by'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'vid' => 'Vid',
			'school_id' => 'School',
			'class_id' => 'Class',
			'child_id' => 'Child',
			'old_info' => 'Old Info',
			'track_memo' => 'Track Memo',
			'track_type' => 'Track Type',
			'track_by' => 'Track By',
			'track_at' => 'Track At',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('vid',$this->vid);
		$criteria->compare('school_id',$this->school_id,true);
		$criteria->compare('class_id',$this->class_id);
		$criteria->compare('child_id',$this->child_id);
		$criteria->compare('old_info',$this->old_info,true);
		$criteria->compare('track_memo',$this->track_memo,true);
		$criteria->compare('track_type',$this->track_type);
		$criteria->compare('track_by',$this->track_by);
		$criteria->compare('track_at',$this->track_at);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return ChildVacationTrack the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
