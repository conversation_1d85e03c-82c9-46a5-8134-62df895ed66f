<?php
$classTypes = IvyClass::getClassTypes(true, $branchObj->type);
$crit = new CDbCriteria();
$crit->compare('branchid',$this->branchId);
$crit->compare('category', array('classcfg','classtime'));
$crit->index = 'category';
$branchVars = BranchVar::model()->findAll($crit);
$altTitle = array();
if(empty($branchVars['classcfg'])||!$branchVars['classcfg']->flag){
    unset($classTypes['c']);
}
if(!empty($branchVars['classcfg']) && !empty($branchVars['classcfg']->data)){
    $altTitle = explode(';',$branchVars['classcfg']->data);
}
if(!empty($branchVars['classtime']) && !empty($branchVars['classtime']->data)){
    $defaultTimeSlot = $branchVars['classtime']->data;
}else{
    $defaultTimeSlot = '8|00|18|00';
}
?>
<!-- Modal -->
<div class="modal" id="classEditModal" tabindex="-1" role="dialog" aria-labelledby="classEditModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel"><?php echo Yii::t('campus','Edit Class Info');?> <small></small></h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="well">
                            说明
                        </div>
                        <form class="form-horizontal J_ajaxForm" action="<?php echo $this->createUrl('//mcampus/classes/saveClass');?>" method="POST">
                            <div id="form-data">
                                <!--place holder-->
                            </div>

                            <div class="pop_bottom">
                                <button onclick="$('#classEditModal').modal('hide')" type="button" class="btn btn-default pull-right"><?php echo Yii::t('global','Cancel');?></button>
                                <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global','Submit');?></button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/template" id="class-item-template">
    <?php $this->renderPartial('class/_classEdit_template',array('classTypes'=>$classTypes));?>
</script>

<script>

    var classEditTemplate = _.template($('#class-item-template').html());
    var altTitles = <?php echo CJSON::encode($altTitle); ?>;
    var baseTitles = <?php echo CJSON::encode(IvyClass::getClassTypes(false, $branchObj->type));?>;
    var showTitles;
    var displayTitles;
    var saveDefaultClassTime;
    var openClassEditModal;
    var defaultTimeSlot = <?php echo CJSON::encode(explode('|', $defaultTimeSlot));?>;
    $(function(){
        displayTitles = function(){
            if(!$('#title-selection-box').hasClass('in')){
                showTitles();
            }
        };

        showTitles = function(){
            var _year = $('#yid4show').find("option:selected").text();
            var _codes= [''];
            var _keys = _.keys(baseTitles);
            var _classkey = $('#IvyClass_classtype').val();
            var _classType = baseTitles[_classkey];
//            var _i = _.indexOf(_keys, $('#IvyClass_classtype').val()); //混龄班用
            var _i = <?php echo $branchObj->type == 20 ? 1 : 0?>; //混龄班用

            var reg = /^e\d{1,}$/;
            if(reg.test(_classkey)){
                $('#period').hide();
            }
            else{
                $('#period').show();
            }

            $('#title-list').empty();
            if(_.isEmpty(_classType)) {
                $('#title-list').html('<div class="alert alert-warning p15">请先选择班级类型。</div>')
                return;
            }

            $("input.classCode[classCode][type|='checkbox']").each(function(){
                if($(this).attr("checked")){
                    _codes.push($(this).attr('classCode'));
                }
            });
            var _programs=[];
            var results = [];
            if(altTitles.length){
                altTitles.forEach(function(item){
                    _programs.push(item);
                });
            }else{
                _programs.push('');
            }

            if(_i > 0){
                $('#mixed-class').show();
            }else{
                $('#mixed-class').hide();
            }
            _programs.forEach(function(_p){
                _codes.forEach(function(_code){
                    results.push(_year + ' ' + _p + ' ' + _classType + ' ' + _code);
                })
            });

            if(_i > 0 && $('input[type|="checkbox"][mixed]').attr("checked") ){
                var _mixTitle = baseTitles[_keys[_i - 1]] + '/' + baseTitles[_keys[_i]];
                _programs.forEach(function(_p){
                    _codes.forEach(function(_code){
                        results.push(_year + ' ' + _p + ' ' + _mixTitle + ' ' + _code);
                    })
                });
            }


            results.forEach(function(title){
                $('#title-list').append($('<a title-selection-item class="list-group-item"></a>').html(title));
            });

            $('a[title-selection-item]').click(function(){
                $("input#IvyClass_title").val($(this).html());
                $('#title-selection-box').collapse('hide');
            });

        };

        classEdit = function(obj){
            $('#classEditModal h4.modal-title small').html( $(obj).parents('h4').find('label').html() );
            var _classId = $(obj).parents('.list-group-item').attr('classId');
            if(!_.isUndefined(classData[_classId])){
                openClassEditModal(_classId);
            }
        };

        openClassEditModal = function(classId){
            $('#J_fail_info').remove();
            $('#postData_classid').val(classId);
            $('div.form-group').addClass('has-error');
            $('#classEditModal').modal();
            var _tempData = classData[classId];
            var _formData = classEditTemplate(_tempData);
            $('#form-data').html(_formData);
//            $('#form-data #IvyClass_yid option[value="'+_tempData.yid+'"]').attr('selected', true);
            $('#form-data #yid4show option[value="'+_tempData.yid+'"]').attr('selected', true);
            $('#form-data #IvyClass_classtype option[value="'+_tempData.classtype+'"]').attr('selected', true);
            $('#form-data #IvyClass_introduction option[value="'+_tempData.introduction+'"]').attr('selected', true);
            $('#form-data #IvyClass_stat option[value="'+_tempData.stat+'"]').attr('selected', true);
            if(!classId){
                $('#class_stat').hide();
            }
            var reg = /^e\d{1,}$/;
            if(reg.test(_tempData.classtype)){
                $('#period').hide();
            }
            else{
                $('#form-data #IvyClass_periodData_0 option[value="'+_tempData.periodData[0]+'"]').attr('selected', true);
                $('#form-data #IvyClass_periodData_1 option[value="'+_tempData.periodData[1]+'"]').attr('selected', true);
                $('#form-data #IvyClass_periodData_2 option[value="'+_tempData.periodData[2]+'"]').attr('selected', true);
                $('#form-data #IvyClass_periodData_3 option[value="'+_tempData.periodData[3]+'"]').attr('selected', true);
            }
        };

        createClass = function(calendarId){
            var _tempData = classData[0];
            _tempData.yid = calendarId;
            for(var i=0; i<4; i++){
                _tempData.periodData[i] = defaultTimeSlot[i];
            }
            openClassEditModal(0);
        };

        saveDefaultClassTime = function(obj){
            postData = {
                'classTime[0]':$('#IvyClass_periodData_0').find("option:selected").text(),
                'classTime[1]':$('#IvyClass_periodData_1').find("option:selected").text(),
                'classTime[2]':$('#IvyClass_periodData_2').find("option:selected").text(),
                'classTime[3]':$('#IvyClass_periodData_3').find("option:selected").text()
            };
            $.ajax({
                url: "<?php echo $this->createUrl('//mcampus/classes/saveDefaultClassTime');?>",
                type: 'POST',
                dataType: 'json',
                data: postData
            }).done(function(data) {
                if(data.state == 'success'){
                    var msg = $('<span class="glyphicon glyphicon-ok-sign text-success"></span>');
                    $(obj).after(msg);
                    msg.hide(1800,function(){msg.remove()});
                }else{
                    var msg = $('<span class="glyphicon glyphicon-question-sign text-danger"></span>').after(' '+data.message);
                    $(obj).after(msg);
                    msg.hide(3000,function(){msg.remove()});
                }
            });
        };

    });

    function emptyTitle(){
        $("input#IvyClass_title").val("");
        $('#title-selection-box').collapse();
        showTitles();
    }
    function postFeedback(data){
        var attrs = _.keys(data);
        attrs.forEach(function(attr){$('div.form-group[model-attribute|="'+attr+'"]').addClass('has-error')});
        console.log(data);
    }

</script>