<?php

/**
 * This is the model class for table "ivy_asa_invoice_discount_link".
 *
 * The followings are the available columns in table 'ivy_asa_invoice_discount_link':
 * @property integer $invoice_id
 * @property integer $discount_id
 * @property double $amount_pre_discount
 * @property double $amount_final
 */
class AsaInvoiceDiscountLink extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_asa_invoice_discount_link';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('invoice_id, discount_id, amount_pre_discount, amount_final', 'required'),
			array('discount_id', 'numerical', 'integerOnly'=>true),
			array('amount_pre_discount, amount_final', 'numerical'),
			array('invoice_id', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('invoice_id, discount_id, amount_pre_discount, amount_final', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'invoice_id' => 'Invoice',
			'discount_id' => 'Discount',
			'amount_pre_discount' => 'Amount Pre Discount',
			'amount_final' => 'Amount Final',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('invoice_id',$this->invoice_id);
		$criteria->compare('discount_id',$this->discount_id);
		$criteria->compare('amount_pre_discount',$this->amount_pre_discount);
		$criteria->compare('amount_final',$this->amount_final);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AsaInvoiceDiscountLink the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
