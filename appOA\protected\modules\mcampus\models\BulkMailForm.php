<?php

/* 
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
class BulkMailForm extends CFormModel
{

    public $user = '';
    public $password = '';
    public $smtp = '';
    public $port = '';
    public $to = '';
    public $theme = '';
    public $main = '';
    public $replyto = '';
    public $fromname = '';
    public $atta = '';

    public function rules()
    {
        return array(
            array('user','required','message'=>Yii::t('user', '用户名不能为空')),
            array('password','required','message'=>Yii::t('password', '请输入密码')),
            array('smtp','required','message'=>Yii::t('smtp', '请输入SMTP地址')),
            array('port','required','message'=>Yii::t('port', '请输入端口号')),
            array('to','required','message'=>Yii::t('to', '请选择收件人')),
            array('theme','required','message'=>Yii::t('theme', '邮件主题不能为空')),
            array('main','required','message'=>Yii::t('main', '邮件内容不能为空')),
            array('replyto','safe','message'=>Yii::t('replyto', '')),
            array('fromname','safe','message'=>Yii::t('fromname', '')),
            array('atta','safe','message'=>Yii::t('atta', '')),
        );
    }
}
