<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
    <h4 class="modal-title" id="exampleModalLabel"><?php echo Yii::t('asa','跨学校关联'); ?></h4>
</div>
<?php $form=$this->beginWidget('CActiveForm', array(
    'id'=>'courseGroup-form',
    'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
)); ?>

<div class="modal-body">
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model,'site_id'); ?></label>
        <div class="col-xs-10">
            <?php echo CHtml::checkBoxList("site_id", $checkAll, $allBranch,array('maxlength'=>255, 'template' => "<div class='col-xs-3'>{input}{label}</div>","separator"=>"")); ?>
        </div>
    </div>

</div>
<div class="modal-footer">
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
    <button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
</div>

<?php $this->endWidget(); ?>
<script>
    function cbGroupshare() {
        var groupId = '<?php echo $model->id; ?>';
        $('#modal').modal('hide');
        window.location.reload(true);
    }
</script>