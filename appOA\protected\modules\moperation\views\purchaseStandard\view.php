<div class="container-fluid">
	<ol class="breadcrumb">
	    <li><?php echo CHtml::link(Yii::t('site','HQ Operations'), array('default/index'));?></li>
	    <li><?php echo CHtml::link(Yii::t('site','Support'), array('default/index'));?></li>
	    <li><?php echo CHtml::link(Yii::t('site','标配管理'), array('purchaseStandard/index'));?></li>
	    <li class="active"><?php echo $psModel->cn_title; ?></li>
	</ol>
	<div class="col-xs-12">
		<div class="form-group">
			<?php
			    $this->widget('ext.ivyCGridView.BsCGridView', array(
			        'id'=>'purchases-standlist-view',
			        'afterAjaxUpdate'=>'js:function(){head.Util.ajaxDel()}',
			        'dataProvider'=>$ppiModel->search(),
			        'colgroups'=>array(
			            array(
			                "colwidth"=>array(100,100,100,100,100),
			            )
			        ),
			        'columns'=>array(
			            array(
			               'header' => '名称',
			               'type' => 'raw',
			               'value' => 'CHtml::link($data->product->cn_name,Yii::app()->createUrl("moperation/purchaseProducts/view",array("id"=>$data->product->id)),array("target"=>"_blank"))'
			            ),
			            array(
			                'name'=>'单价',
			                'value'=>'$data->product->price'
			                ),
			            array(
			                'name'=>'数量',
			                'value'=>'$data->num'
			                ),
			            array(
			                'name'=>'总价',
			                'value'=>'number_format($data->num*$data->product->price, 2, ".", "")'
			                ),
			        ),
			    ));
			?>
			<strong>总价：<?php echo $psModel->getTotalMoney(); ?></strong>
		</div>
	</div>
</div>

