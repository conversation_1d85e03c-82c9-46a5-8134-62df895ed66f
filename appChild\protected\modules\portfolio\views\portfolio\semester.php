<?php
$this->breadcrumbs=array(
	'Portfolio'=>array('/portfolio/portfolio/classes'),
	Yii::t("navigations",'Semester Report'),
);?>

<div id="left-col" class="no-bg span-6">
    <ul class="sub-nav">
        <?php foreach ($mod as $sy=>$ms):?>
        <li <?php if ($sy == $startyear):?>class="active"<?php endif;?>><a href="<?php echo Yii::app()->getController()->createUrl('//portfolio/portfolio/semester', array('startyear'=>$sy))?>"><?php echo IvyClass::formatSchoolYear($sy)?> <?php echo Yii::t("portfolio", 'School Year');?></a><em></em>
            <ul class="sub-group">
                <?php if (isset($ms[20])):?><li <?php if ($sy == $startyear && $semester == 20):?>class="active"<?php endif;?>><a href="<?php echo Yii::app()->getController()->createUrl('//portfolio/portfolio/semester', array('startyear'=>$sy, 'semester'=>20))?>"><?php echo Yii::t("portfolio", 'Spring Semester');?></a></li><?php endif;?>
                <?php if (isset($ms[10])):?><li <?php if ($sy == $startyear && $semester == 10):?>class="active"<?php endif;?>><a href="<?php echo Yii::app()->getController()->createUrl('//portfolio/portfolio/semester', array('startyear'=>$sy, 'semester'=>10))?>"><?php echo Yii::t("portfolio", 'Fall Semester');?></a></li><?php endif;?>
            </ul>
        </li>
        <?php endforeach;?>
    </ul>
</div>

<div id="main-col" class="span-18 last">
    <?php if (isset($mod[$startyear][$semester])&&$mod[$startyear][$semester]):?>
    <?php $params = array("mod"=>$mod, "classteacher"=>$classteacher, "pitems"=>$pitems, "startyear"=>$startyear, "semester"=>$semester, 'terms'=>$terms);?>
    <?php if (!$mod[$startyear][$semester]->custom):?>
	<div class="semester-report">
		<div class="head">
		</div>
		<div class="main-content">
			<?php
				$this->widget('ext.OrbitSlider.OrbitSlider', array(
					'content'=>array(
						array(
							'content'=>$this->renderPartial("_semester_page1", $params , true),
							'style'=>'background-color:white;',
							'caption'=>sprintf(Yii::t("portfolio",'Page %d'), 1),
						), 
						array(
							'content'=>$this->renderPartial("_semester_page2", $params , true),
							'style'=>'background-color:white;display:none;',
							'caption'=>sprintf(Yii::t("portfolio",'Page %d'), 2)
						), 
						array(
							'content'=>$this->renderPartial("_semester_page3", $params , true),
							'style'=>'background-color:white;display:none;',
							'caption'=>sprintf(Yii::t("portfolio",'Page %d'), 3)
						), 
						array(
							'content'=>$this->renderPartial("_semester_page4", $params , true),
							'style'=>'background-color:white;display:none;',
							'caption'=>sprintf(Yii::t("portfolio",'Page %d'), 4)
						), 
					),
					'slider_options'=>array(
						'animation'=>'horizontal-slide',
						'bullets'=>true,
						'directionalNav'=>true,
						'advanceSpeed'=>'5000',
						'timer'=>false,
						//'directionalNav'=>false,
					),
					'width'=>'680',
					'height'=>'1000',
				));
			?>
			<div class="clear"></div>
		</div>
		<div class="foot"></div>
    </div>

    <?php else:?>

    <div class="semester-report">
        <div class="head"></div>
        <div class="main-content">
            <div id="page-1" style="width: 680px;height: 380px;">
                <div class="span-11">
                    <ul class="basic">
                        <li><label><?php echo Yii::t("labels", 'Child Name');?></label><?php echo $this->getChildName()?></li>
                        <li><label><?php echo Yii::t("labels", 'Date of Birth');?></label><?php echo $this->myChildObjs[$this->getChildId()]->birthday_search?></li>
                        <li><label><?php echo Yii::t("labels", 'Campus');?></label><?php echo $mod[$startyear][$semester]->schoolInfo->title?></li>
                        <li><label><?php echo Yii::t("portfolio", 'Semester');?></label><?php echo IvyClass::formatSchoolYear($startyear)?> <?php echo ($semester == 10) ? Yii::t("portfolio", 'Fall') : Yii::t("portfolio", 'Spring');?></li>
                        <li><label><?php echo Yii::t("labels", 'Class');?></label><?php echo $mod[$startyear][$semester]->classInfo->title?></li>
                        <?php
                            $idx = 0;
                            if ($classteacher):?>
                                <?php foreach ($classteacher as $teacher):?>
                                <?php if($idx++ == 0):?>
                                <li><label><?php echo Yii::t("portfolio", 'Class Teachers');?></label><?php echo $teacher->userWithProfile->getName();?></li>
                                <?php else:?>
                                <li><label>&nbsp;</label><?php echo $teacher->userWithProfile->getName();?></li>
                                <?php endif;?>
                                <?php endforeach;?>
                            <?php endif;?>
                    </ul>					
                </div>
                <div class="span-6 last">
                    <p>
                    <?php echo CHtml::image(CommonUtils::childPhotoUrl($this->myChildObjs[$this->getChildId()]->photo), $this->myChildObjs[$this->getChildId()]->getChildName() );?>
                    </p>
                    <div>
                        <ul class="pta-items">
                            <li>
                                <span><?php echo CHtml::link('<span>'.Yii::t("portfolio", "View Report").'</span>', $this->createUrl('/portfolio/portfolio/semesterpdf', array('id'=>$mod[$startyear][$semester]->id, 'f'=>1)), array('target'=>'_blank'))?></span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="foot"></div>
    </div>
    
    <?php endif;?>
    
    <div style="margin-top: 15px;">
		<p>
		<span class="btn001">
        <?php echo CHtml::link('<span>'.Yii::t("portfolio", 'Download PDF').'</span>', Yii::app()->getController()->createUrl('//portfolio/portfolio/semesterpdf', array('id'=>$mod[$startyear][$semester]->id)), array("target"=>"_blank"))?>
		</span>
		</p>
    </div>
	
	<?php else:?>
	<p>
		<?php echo Yii::t("portfolio","The Semester Report will be available online after Parent Teacher Conferences near the end of the semester. Please check back later.");?>
	</p>	
    <?php endif;?>
</div>

<style>
    .pta-items li span a{
        text-decoration:none;
    }
    .pta-items li span a span{
        display: block;
        text-align: center;
        padding: 20px 0;
        background: #F36601;
        margin-bottom: 6px;
        font-size: 20px;
        color:#fff;
    }
</style>