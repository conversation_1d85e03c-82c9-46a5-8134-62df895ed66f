<?php
$cs = Yii::app()->clientScript;
$cs->registerScriptFile(Yii::app()->themeManager->baseUrl . "/base/js/bootstrap.min.js");
$cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/css/bootstrap-iso.css');
$cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/vue.global.js');
$cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.css');
$cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.js');
//标签字体需要的文件
$cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/label/iconfont.css?v=20231124');
?>
<style>
    .m10 {
        margin: 10px !important;
    }

    .mr10 {
        margin-right: 10px !important;
    }

    .mb10 {
        margin-bottom: 10px !important;
    }

    .mb12 {
        margin-bottom: 12px !important;
    }

    .mt10 {
        margin-top: 10px !important;
    }
    .mt5 {
        margin-top: 5px !important;
    }
    .mt14 {
        margin-top: 14px !important;
    }
    .ml5{
        margin-left: 5px !important;

    }
    .ml10 {
        margin-left: 10px !important;
    }
    .ml16 {
        margin-left: 16px !important;
    }
   .ml24{
        margin-left:24px !important;
    }
    .mr16 {
        margin-right: 16px !important;
    }

    .mt28 {
        margin-top: 28px !important;
    }

    .p4-12 {
        padding: 4px 12px !important;
    }

    .mt30 {
        margin-top: 30px !important;
    }
    .mb5 {
        margin-bottom: 5px !important;
    }
    .mb6 {
        margin-bottom: 6px !important;
    }
    .mb16{
        margin-bottom:16px !important
    }
    .mt8{
        margin-top:8px !important
    }
    .label-no-select {
        margin-right: 10px;
        margin-bottom: 10px;
        padding: 0px 15px;
        background: #F7F7F8;
        border-radius: 4px;
        padding: 3px 15px 4px;
        border:1px solid #F7F7F8;
        position: relative;
        cursor: pointer;
        font-size:13px;
        display:inline-block
    }

    .child_name {
        color: #333333;
        font-size: 14px;
        margin-bottom: 4px !important;
    }

    .flag_name {
        color: #666666;
        font-size: 12px;
        margin-top: 4px
    }

    .flex {
        display: flex;
    }

    .min-height-300 {
        min-height: 300px !important;
    }

    .flag_div {
        display: flex;
        align-items: center
    }

    .choose_flag_div {
        display: flex;
        /* width: 90%; */
        flex: 1;
        flex-wrap: wrap;
        align-items: center;
        align-content: space-between
    }

    .modal-backdrop {
        height: 100%;
    }

    .mask {
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.6);
        position: fixed;
        left: 0px;
        top: 0px;
        z-index: 1001;
    }

    .flag_span {
        margin-right:10px;
        display:flex;
        line-height:18px
    }
    .flag_span .iconfont{
        font-size:14px
    }
    .lh30{
        line-height: 30px;
        color:#333;
        font-weight:500;
        font-size:14px;
    }
    .flagSselected{
        position: absolute;
        left: 0px;
        display: inline-block;
        width: 12px;
        height: 12px;
        opacity:0;
        line-height: 11px;
        text-align: center;
        border-radius: 0px 0 4px;
        color: #fff;
        top: 0;
        font-size:12px
    }
    .block{
        opacity:1 !important
    }
    .text{
        height: 16px;
        line-height: 13px;
        text-align: center;
        display:inline-block;
    }
    .p24{
        padding:24px !important
    }
    .text .iconfont{
        font-size:14px
    }
    .flex1{
        flex:1
    }
    .item_center{
        align-items:center
    }
    .addNum{
        color:#4D88D2;
        font-size:24px;
        /* background: #EDF3FA; */
        /* border-radius: 50%; */
        cursor: pointer;
        margin-right:10px
    }
    .imgs{
        width: 24px;
        height: 24px;
        border: 1px solid #FFFFFF;
        border-radius:50%;
        margin-left:-5px;
        object-fit: cover;
    }
    .lastNum{
        width: 25px;
        height: 25px;
        border: 1px solid #FFFFFF;
        border-radius:50%;
        margin-left:-5px;
        font-size: 12px;
        color: #666666;
        background: #F2F3F5;
        display:inline-block;
        text-align: center;
        line-height: 23px;
        cursor: pointer;
    }
    .scroll-box {
        font-size: 100%;
    }
    .scroll-box::-webkit-scrollbar {
        /*滚动条整体样式*/
        width : 6px;  /*高宽分别对应横竖滚动条的尺寸*/
        height: 1px;
    }
    .scroll-box::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius   : 10px;
        background-color: #ccc;
       
    }
    .scroll-box::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0.2);
        background   : #ededed;
        border-radius: 10px;
    }
    .listMedia{
        padding:8px !important;
        border: 1px solid #fff;
    }
    .listMedia:hover{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        border: 1px solid #4D88D2;
        cursor: pointer;
    }
    .borderLeftpos {
        display: inline-block;
        border-left: 1px solid #E4E7ED;
        height: 100%;
        width: 1px;
        position: absolute;
        left: 50%;
    }
    .optionSearch{
        height:auto;
        padding:5px 20px
    }
    .text_overflow {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        line-height: 14px;
    }
    .childList{
        border: 1px solid #ccc;
        border-radius: 4px;
        padding: 8px !important;
        margin-top: 10px !important;
    }
    .font14{
        font-size:14px
    }
    .department_name{
      padding:6px 12px;
      background: #F7F7F8;
      border-radius: 4px;
      margin-bottom:8px
    }
    .img42{
        width: 42px;
        height: 42px;
        object-fit: cover;
        border-radius:50%;
    }
   .cur-p{
        cursor: pointer;
   }
   .font16{
        font-size:16px
   }
   .color3{
        color: #333 !important;
   }
   .color6{
        color: #666 !important;
   }
   .color606 {
        color: #606266;
        line-height: 30px;
   }
   .wordInherit{
        word-break: break-all;
    }
</style>

<script>
    var container = new Vue({
        el: "#child_info_container",
        data: {
            flagRaw: '',//原始数据
            flag: '',
            mainInfo: '',
            flagList: '',
            setFlagData1: [],//设置1 增加标签时插入 需要在原始数组中不存在的标签
            setFlagData0: [],//设置0 删除标签时插入 需要在原始数据中存在的标签
            showNonSelectable: false,//是否展示不可选择标签
            nonSelectableFlag: [],//不可选择的标签
            changeable:[],//可选择的标签
            label_no_select: 'label-no-select',
            active_style: {},
            spanIndex: [],
            defColor: "#d9534f",//默认标签选中背景色
            date:'',
            checked:false,
            labelList:{},
            schoolId:'',
            schoolList:[],
            searchText:'',
            currentDept:'',
            staffSelected:[],
            dep_name:'',
            allDept:{},
            currentDept:{},
            staffInfo:{},
            addTeacherItem:{},
            addTeacherType:'',
            setDynamicFlagData0:[],
            setDynamicFlagData1:[],
            staffSelectedCopy:[]
        },
        mounted() {
            //获取孩子的标签信息
            this.getFlag()
            var that = this
            $('#childFlag').on('hide.bs.modal', function (e) {
                $(".mask").fadeOut()
                for (const key in that.flagList) {
                    let dom='flag_'+that.flagList[key].flag
                   if(that.$refs[dom]){
                       that.$refs[dom][0].firstChild.style.opacity='0'
                   }
                }
            })
            $('#childFlag').on('show.bs.modal', function (e) { 
                $(".mask").fadeIn()
            })
        },
        computed: {
            searchStaffList: function() {
                var search = this.searchText;
                var searchVal = ''; //搜索后的数据
                if(search) {
                    searchVal =Object.values(this.currentDept.user_info).filter(function(product) {
                        return Object.keys(product).some(function(key) {
                            return String(product['name'].toLowerCase()).indexOf(search.toLowerCase()) !== -1;
                        })
                    })
                    return searchVal;
                }
                return this.searchStaffList;
            },
        },
        methods: {
            getFlag() {
                var that = this
                $.get(
                    '<?php echo Yii::app()->getController()->createUrl('/child/index/getFlag');?>',
                    {},
                    function (data) {
                        if(data.state=='success'){
                            that.flagRaw = data.data.flagInfo
                            that.mainInfo = data.data.mainInfo
                            that.flagList = data.data.flagConfig
                            that.schoolId = data.data.school_id
                            that.staffInfo=data.data.push_teacher
                            that.setLable()
                            that.flag = JSON.parse(JSON.stringify(that.flagRaw)); // 拷贝原数据, 深拷贝
                        }
                    },
                    'json');
            },
            setLable(){
                var that = this
                that.labelList={}
                for(let key in that.flagList){
                    if(that.flagList[key].editable_by_staff!='false' && that.flagList[key].is_dynamic==1){
                        that.labelList[key]=that.flagList[key]
                        Vue.set(that.labelList[key], 'label', {});
                        if(that.mainInfo.label[key]){
                            Vue.set(that.labelList[key], 'label', that.mainInfo.label[key]);
                            if(that.labelList[key].label.start_time==0){
                                Vue.set(that.labelList[key].label, 'start_time_checked', true);
                                Vue.set(that.labelList[key].label, 'start_time_date', '');
                            }else{
                                Vue.set(that.labelList[key].label, 'start_time_checked', false);
                                Vue.set(that.labelList[key].label, 'start_time_date', that.labelList[key].label.start_time);
                            }
                            if(that.labelList[key].label.end_time==0){
                                Vue.set(that.labelList[key].label, 'end_time_checked', true);
                                Vue.set(that.labelList[key].label, 'end_time_date', '');
                            }else{
                                Vue.set(that.labelList[key].label, 'end_time_checked', false);
                                Vue.set(that.labelList[key].label, 'end_time_date', that.labelList[key].label.end_time);
                            }
                        }else{
                            Vue.set(that.labelList[key].label, 'start_time_checked', false);
                            Vue.set(that.labelList[key].label, 'start_time_date', '');
                            Vue.set(that.labelList[key].label, 'end_time_checked', false);
                            Vue.set(that.labelList[key].label, 'end_time_date', '');
                            Vue.set(that.labelList[key].label, 'start_push', []);
                            Vue.set(that.labelList[key].label, 'end_push', []);
                        }
                    }
                }
            },
            showFlag() {
                var that = this
                that.nonSelectableFlag = []
                that.changeable = []
                that.spanIndex = [];
                that.setFlagData1 = [];
                that.setFlagData0 = [];
                that.active_style = {}
                that.showNonSelectable = false
                that.setLable()
                for (const key in that.flagRaw) {
                     let id=that.flagRaw[key]
                     if(that.flagList[id]){
                        if(that.flagList[id].editable_by_staff!='false' && that.flagList[id].is_dynamic==1){
                            that.chooseFlag(id,'label')
                        }else{
                            that.chooseFlag(id,'flag')
                        }
                     }
                }
                for (const key in that.flagList) {
                    if (that.flagList[key]['editable_by_staff'] == 'false') {
                        that.showNonSelectable = true
                        that.nonSelectableFlag.push(that.flagList[key])
                    }else{
                        that.changeable.push(that.flagList[key])
                    }
                }
                $("#childFlag").modal('show');
            },
            defaultColor(color){
                return {'color':color}
            },
            chooseFlag(flag,type) {
                let dom='flag_'+flag
                var that = this
                var index = Number(flag)
                let arrIndex = that.spanIndex.indexOf(index);
                if (arrIndex > -1) {
                    //删除标签
                    that.spanIndex.splice(arrIndex, 1);
                    that.active_style[index] = '';
                    if(type=='flag'){
                        //删除现有的数据
                        if (!that.setFlagData0.includes(flag) && that.flagRaw.includes(flag)) {
                            that.setFlagData0.push(flag)
                        }
                        //删除页面操作的数据
                   
                        if (that.setFlagData1.includes(flag)) {
                            that.setFlagData1.splice(that.setFlagData1.findIndex(item => item === flag), 1)
                        }
                    }else{
                        if (!that.setDynamicFlagData0.includes(flag) && that.flagRaw.includes(flag)) {
                            that.setDynamicFlagData0.push(flag)
                        }
                    }
                  
                    this.$nextTick(()=>{
                        this.$refs[dom][0].firstChild.style.opacity='0'
                    })
                } else {
                    //添加标签
                    that.spanIndex.push(index);
                    //本次选的标签
                    let active_styles = JSON.parse(JSON.stringify(that.active_style))
                    var backgroundColor = that.flagList[flag] && that.flagList[flag]['color'] ? that.flagList[flag]['color'] : that.defColor
                    that.active_style = {
                        [index]: {
                            backgroundColor:'rgba('+backgroundColor+',0.1)' ,
                            border:'1px solid rgb('+ backgroundColor+')',
                        }
                    }
                    that.$nextTick(()=>{
                        if(that.$refs[dom]){
                            that.$refs[dom][0].firstChild.style.opacity='1'
                            that.$refs[dom][0].firstChild.style.backgroundColor=backgroundColor
                        }
                    })
                    that.active_style = {...active_styles, ...that.active_style};//选中样式
                    if(type=='flag'){
                        if (!that.flagRaw.includes(flag)) {
                            that.setFlagData1.push(flag)
                        }
                        //添加的标签在setFlagData0需要去掉
                        if (that.setFlagData0.includes(flag)) {
                            that.setFlagData0.splice(that.setFlagData0.findIndex(item => item === flag), 1)
                        }
                    }else{
                        //添加的标签在setFlagData0需要去掉
                        if (that.setDynamicFlagData0.includes(flag)) {
                            that.setDynamicFlagData0.splice(that.setFlagData0.findIndex(item => item === flag), 1)
                        }
                    }
                }
            },
            setFlag() {
                var that = this
                that.setDynamicFlagData1=[]
                for(let key in this.labelList){
                    if((this.labelList[key].label.start_time_checked ||( this.labelList[key].label.start_time_date!=='' && this.labelList[key].label.start_time_date!==null)) || (this.labelList[key].label.end_time_checked || (this.labelList[key].label.end_time_date!=='' && this.labelList[key].label.end_time_date!==null)) || this.labelList[key].label.start_push.length!=0 || this.labelList[key].label.end_push.length!=0){
                        if(that.active_style[key]==undefined || that.active_style[key].backgroundColor ==undefined){
                            resultTip({
                                error: 'warning',
                                msg: '请选择动态标签'
                            });
                            return
                        }
                        if(this.labelList[key].label.start_time_checked==false && (this.labelList[key].label.start_time_date=='' || this.labelList[key].label.start_time_date==null)){
                            resultTip({
                                error: 'warning',
                                msg: '请选择生效时间'
                            });
                            return
                        }
                        if(this.labelList[key].label.end_time_checked==false && (this.labelList[key].label.end_time_date=='' || this.labelList[key].label.end_time_date==null)){
                            resultTip({
                                error: 'warning',
                                msg: '请选择失效时间'
                            });
                            return
                        }
                        that.setDynamicFlagData1.push(
                            {
                                flag:key,
                                start_time:this.labelList[key].label.start_time_checked?0:this.labelList[key].label.start_time_date,
                                start_type:this.labelList[key].label.start_time_checked?1:0,
                                end_time:this.labelList[key].label.end_time_checked?0:this.labelList[key].label.end_time_date,
                                end_type:this.labelList[key].label.end_time_checked?1:0,
                                start_push:this.labelList[key].label.start_push,
                                end_push:this.labelList[key].label.end_push
                            }
                        )
                    }
                }
                $.post(
                    '<?php echo Yii::app()->getController()->createUrl('/child/index/setFlag');?>',
                    {
                        setFlagData1: that.setFlagData1,
                        setFlagData0: that.setFlagData0,
                        setDynamicFlagData1:that.setDynamicFlagData1,
                        setDynamicFlagData0:that.setDynamicFlagData0
                    },
                    function (data) {
                        if (data.state === 'success') {
                            resultTip({
                                msg: data.state,
                                callback: function () {
                                    location.reload();
                                }
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    'json')
            },
            addTeacher(item,type){
                this.addTeacherItem=item
                this.addTeacherType=type
                let that=this
                if(that.schoolList.length!=0){
                    that.addStaff()
                    return
                }
                $.ajax({
                    url: '<?php echo Yii::app()->getController()->createUrl('/mcampus/leave/schoolList');?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        branchId:this.schoolId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.schoolList=data.data.list  
                            that.schoolId=data.data.current_school
                            that.addStaff()

                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            addStaff(){
                let that=this
                $.ajax({
                    url: '<?php echo Yii::app()->getController()->createUrl("/mcampus/label/schoolTeacher") ?>&branchId='+this.schoolId,
                    type: "post",
                    dataType: 'json',
                    data:{
                        "school_id":this.schoolId,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.currentDept=data.data
                            that.staffInfo=Object.assign( that.staffInfo, data.data.user_info)
                            that.allDept=Object.assign(that.allDept, that.staffInfo)
                            if(that.addTeacherType=='start'){
                                that.staffSelected=JSON.parse(JSON.stringify(that.flagList[that.addTeacherItem.flag].label.start_push))
                            }else{
                                that.staffSelected=JSON.parse(JSON.stringify(that.flagList[that.addTeacherItem.flag].label.end_push))
                            }
                            that.dep_name=''
                            $("#addStaffModal").modal('show');
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            showDepName(list){
                if(this.dep_name==list.dep_name){
                    this.dep_name=''
                    return
                }
                this.dep_name=list.dep_name
                for(let i=0;i<list.user.length;i++){
                    if(this.staffSelected.indexOf(list.user[i].uid)!=-1){
                        Vue.set(this.currentDept.user_info[list.user[i].uid], 'disabled', true);
                    }
                }
            },
            assignStaff(list,index,idx){
                if(idx=='search'){
                    this.searchStaffList[index].disabled=true
                }else{
                    Vue.set(this.currentDept.user_info[list.uid], 'disabled', true);
                }
                this.staffSelected.push(list.uid)
            },
            Unassign(list,index){
                if(this.currentDept.user_info[list]){
                    Vue.set(this.currentDept.user_info[list], 'disabled', false);
                }
                this.staffSelected.splice(index,1)
            },
            delSelect(id,type){
                this.reportData.last.splice(id,1)
            },
            confirmSatff(){
                if(this.addTeacherType=='start'){
                    Vue.set(this.labelList[this.addTeacherItem.flag].label, 'start_push', this.staffSelected);
                }else{
                    Vue.set(this.labelList[this.addTeacherItem.flag].label, 'end_push', this.staffSelected);
                }
                $("#addStaffModal").modal('hide');
                this.$forceUpdate()
            },
            setDate(item,type){
                if(item.label[type+'date']!=''){
                    item.label[[type+'checked']]=false
                }
            },
            setCheck(item,type){
                if(item.label[type+'checked']){
                    item.label[[type+'date']]=''
                }
            },
        }
    })
</script>