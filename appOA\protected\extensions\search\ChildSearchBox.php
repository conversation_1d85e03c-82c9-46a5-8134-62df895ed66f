<?php
class ChildSearchBox extends CWidget
{
	//ac: autoComplete
	public $acInputCSS = 'length_5';
	public $acName = 'searchChild';
	public $acPopPosition = array('my'=>"left top", 'at'=>"left bottom", 'collision'=>"none");
	public $withAlumni = false; //是否需要搜索不在读的孩子
	public $simpleDisplay = true; //是否显示简明信息
	public $allowMultiple = false; //是否可以多选
    public $allowMultipleSchool = false; //允许查询多校园孩子
    public $extendCss = true;  //扩展CSS

    public $useModel = false; //如果使用model，请配置下面两个变量
	public $model;
	public $attribute;

	public $name; //如果不使用model，请忽略上面两个变量，配置这两个变量
	public $select;

	public $data = array();
	public $htmlOptions;

	public function run(){
		$this->render('childSearchBox');
	}
}