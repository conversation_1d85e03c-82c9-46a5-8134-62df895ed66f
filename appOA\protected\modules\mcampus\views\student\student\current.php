<style>
    .badgeBg{
        background-color: #777777 !important;
    }
    .imgCover{
        width:100px;height:100px;object-fit:cover;border-radius:50%;border:1px solid #428bca
    }
    a.s10 span, a.s30 span {
        color: red
    }

    a.s20 span {
        color: #008000
    }

    .class-item .nav > li > a {
        padding: 6px 8px;
    }

    .nav-pills > li.highlight > a, .nav-pills > li.highlight > a:hover, .nav-pills > li.highlight > a:focus {
        color: #ffffff;
        background-color: #d9534f;
    }
    .nav-pills > li.highlight0 > a, .nav-pills > li.highlight0 > a:hover, .nav-pills > li.highlight0 > a:focus {
        color: #ffffff;
        background-color: #d9534f;
    }
    .nav-pills > li.highlight1 > a, .nav-pills > li.highlight1 > a:hover, .nav-pills > li.highlight1 > a:focus {
        color: #ffffff;
        background-color: #312a29;
    }

    .nav-pills > li.highlight2 > a, .nav-pills > li.highlight2 > a:hover, .nav-pills > li.highlight2 {
        color: #ffffff;
        background-color: #f0ad4e;
    }
    .nav-pills > li.highlight3 > a, .nav-pills > li.highlight3 > a:hover, .nav-pills > li.highlight3 {
        color: #ffffff;
        background-color: #5cb85c;
    }

    li.highlight4 > a,li.highlight4 > a:hover {
        color: #ffffff;
        background-color: #f0ad4e;
    }
    li.highlight5 > a,li.highlight5 > a:hover {
        color: #ffffff;
        background-color: rgb(91, 192, 222);
    }
    .nav-pills > li.active1 > a, .nav-pills > li.active1 > a:hover, .nav-pills > li.active1 > a:focus {
        color: #ffffff;
        background-color: #428bca;
    }
    #class-invoice-table tbody {
        height: 100px !important
    }
    .flex{
        display:flex
    }
    #total{
        flex:1
    }
    #StuFamily .panel-body{
        padding:0;
        background:#FAFAFA
    }
    #StuFamily .bg{
        background:#fff;
        width: 50%;
    }
    .parentFamily{
        width: 50%;
        height: 100%;
    }
    .reserveImg {
        width: 44px;
        height: 44px;
        border-radius: 50%;
        object-fit: cover;
    }
    .pt5{
        padding-top:5px
    }
    .classTitle{
        background:#DFF0D8;
        color:#5CB85C;
        padding:3px 5px;
    }
    .ss{
        background:#D9EDF7;
        color:#5BC0DE;
    }
    .parentLabel{
        border:1px solid rgba(0, 0, 0, 0.15);
        background:rgba(0, 0, 0, 0.04);
        padding: 2px 5px;
        margin-right:10px
    }
    .wechat{
        background: #FAFAFA;
        padding:16px 16px 0;
        display:none
    }
    .wechat .imgParent img{
        width:32px;
        height:32px;
        border-radius:50%
    }
    .down{
        display:none
    }
    .wechatName{
        line-height:32px
    }
    .font20{
        font-size:20px
    }
    .qrcode{
        position: absolute;
        width: 200px;
        height: 200px;
        right: 30px;
        top: 20px;
        background: #fff;
        border-radius:6px;
        box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.2);
        display:none;
        z-index:9
    }
    .qrcode img{
        width:120px;
        height:120px
    }
    .mr2{
        margin-right:2px
    }
    .textBlue{
        color:#4D88D2
    }
    .stuImg{
        width:16px;
        height:16px;
        display: inline-block;
        margin-top: -3px;
        margin-right: 3px;
    }
    .mt-2{
        margin-top:-2px
    }
    .nextChild{
        background: #F0F5FB;
        border-radius: 4px;
        padding:20px 24px 20px 10px;
        margin:0 0 20px 0
    }
    .nextChild .pr0{
        padding-right:0
    }
    .nextChildTips{
        background: #FDF8F1;
        border-radius: 4px;
        border: 0.5px solid #F0AD4E;
        padding:16px;
    }
    .colorOrange{
        color:#F0AD4E;
    }
    .nextChildTable td, .nextChildTable th{
        text-align:center;
        padding:8px 12px !important;
        vertical-align: middle !important;
    }
    .nextChildTable th{
        font-weight:normal;
        background: #e5e5e5;
    }
    .nextChildTable td{
        background:#fff;
        font-weight:bold;
    }
    .nextChildTable td .text-primary{
        cursor: pointer;
        float: left;
    }
    .nextChildTableBg{
        background: #F0AD4E !important;
    }
    .nextChildTableBg div{
        color:#fff
    }
    .childPaidNum{
        padding:0px 6px;
        background: #F9E2E1;
        border-radius: 2px;
        color: #D9534F !important;
        font-size: 12px;
        /* position: absolute;
        right:7px;
        top:8px; */
        float: right;
        cursor: pointer;
    }
    .childPaidNumBg{
        background:#D9534F;
        color:#fff !important
    }
    .text-left{
        text-align:left !important
    }
    .colorRed{
        color:#D9534F;
        cursor: pointer;
    }
</style>

<?php

$_yids = $this->getCalendars();
$yid = ($category == 'current') ? $_yids['currentYid'] : $_yids['nextYid'];
$year = $this->calendarStartYear[$yid];

$_classes = array();
if (!is_null($yid)):
    if ($this->isTeacher()) {
        $classIds = CHtml::listData(ClassTeacher::model()->findAllByAttributes(
            array('yid' => $yid, 'teacherid' => Yii::app()->user->id, 'schoolid' => $this->staff->profile->branch)
        ), 'classid', 'classid');
        if ($classIds) {
            $_classes = IvyClass::model()->findAllByPk($classIds);
        }
    } else {
        $_classes = IvyClass::getClassList($this->branchId, $yid);
    }

    $classes = array();
    $classIds = array();
    foreach ($_classes as $_class) {
        $_age = (!$_class->child_age) ? 1 : $_class->child_age;
        $classes[] = array('id' => $_class->classid, 'title' => $_class->title, 'age' => $_age);
        $classIds[$_class->classid] = $_class->classid;
        $label = '-' . $_age . '-';
        $ages[$label] = $label;
    }
    $hasClass = count($classes);
    @ksort($ages);
    $redalertCount = 10;
    $droppingCount = 10;
    if ($category == 'current') {
        $childData = $this->getStuduentsByClass($this->branchId, $classIds, array(10,20,888), $redalertCount, $droppingCount, $year);
    }
    elseif ($category == 'next') {
        $childData = $this->getNextStudentsByClass($this->branchId, $classIds, '<100', $year);
    }

$showReturnTable = false;
if (Yii::app()->user->checkAccess('ivystaff_opschool')) {
    $showReturnTable = in_array($this->branchId, array('BJ_DS', 'BJ_SLT', 'BJ_QFF', 'BJ_OE', 'BJ_IASLT', 'TJ_EB', 'NB_FJ')) && $category=='next';
}
$fromText = 'Other DS';

// switch ($this->branchId) {
//     case 'BJ_DS':
//         $fromText = 'DT/QF';
//         break;
//     case 'BJ_SLT':
//         $fromText = 'DS/QF';
//         break;
//     default:
//     $fromText = 'DS';
//         break;
// }
$returnData = array();
// 获取返校数据
if ($showReturnTable) {
    $requestUrl = 'student/switch/returnTableData';
    $requestData = array('school_id' => $this->branchId);
    $res = CommonUtils::requestDsOnline($requestUrl, $requestData);
    if (isset($res['code']) && $res['code'] == 0) {
        $returnData = $res['data'];
    }
}

    ?>

    <script>
        var isCurrent = <?php echo ($category=='current')? 1: 0;?>;
        var selectedYid = <?php echo $yid; ?>;
    </script>

<?php endif; ?>

<?php
if (is_null($yid) || !$hasClass):
    ?>

    <div class="col-md-5">
        <div class="alert alert-danger p20">尚未建立下学年校历或班级，请完成后再尝试。</div>
    </div>

<?php else: ?>
    <div class="col-md-10">
        <div class="row mb15">
            <div class="col-md-6">
                <h1 class="flex  font24  mt20" style='align-items:center;'> 
                    <span class="dropdown text-primary tipRelative" data-position='top' data-id='tips1' data-title='<?php echo Yii::t('admissions', 'Advanced Functions');?>' data-desc='<?php echo Yii::t('admissions', 'Click here to perform Birthday highlights, view Sibling Families, data Export and etc.');?>' data-btnText='<?php echo Yii::t("admissions","Got it");?>'>
                        <a data-toggle=dropdown href="#" >
                            <span class="glyphicon glyphicon-list glyphiconList " ></span>
                        </a>
                        <ul class="dropdown-menu">
                            <li>
                                <a onclick="highlightBirthStudent()" href="javascript:void(0)">
                                    <span class="glyphicon glyphicon-ok-circle"></span> <?php echo Yii::t('site', 'Highlight month B-Day'); ?>
                                </a>
                            </li>
                             <?php if ($category == 'current'): ?>
                            <li>
                                <a href="javascript:" onclick="highlightInvoiceAlert()">
                                    <span class="glyphicon glyphicon-warning-sign"></span> <?php echo Yii::t('site', 'HightLight students without invoice'); ?>
                                </a>
                            </li>
                            <li>
                                <a onclick="highlightLabeled()" href="javascript:void(0)">
                                    <span class="glyphicon glyphicon-star"></span> <?php echo Yii::t('site', 'Highlight Starred Students'); ?>
                                </a>
                            </li>
                            <li>
                                <a onclick="showFamilyInfo()" href="javascript:void(0)">
                                    <span class="glyphicon glyphicon-home"></span> <?php echo Yii::t('site', 'Sibling Families'); ?>
                                </a>
                            </li>

                             <?php endif ?>
                            <!--                            <li><a onclick="showContact()" href="javascript:void(0)"><span class="glyphicon glyphicon-list-alt"></span> 家长联系方式</a></li>-->
                            <?php if (!$isTeacher): ?>
                                <li>
                                    <a onclick="exportChildInfo(this)" href="javascript:void(0)"><span class="glyphicon glyphicon-export"></span> <?php echo Yii::t('site', 'Export Name List'); ?>
                                    </a></li>
                            <?php endif ?>
                        </ul>
                    </span>
                    <span class='ml4 fontBold'><?php echo Yii::t('site', 'Total Enrolled'); ?></span>  
                    <span class="flex" style='align-items: flex-end;'>
                        <span class="label label-default ml8" id="total-number"></span>
                        <span class="ml15 font14 color6" id="class-number"></span>
                        <span class='font14 ml5 color6'><?php echo Yii::t('site', 'classes'); ?></span>  
                    </span>
                </h1>
            </div>
            <div class="col-md-6">
            <?php if($category == 'current'){ ?>
                    <h1 class="text-primary pull-right">
                        <?php if (0 && $droppingCount): ?>
                        <button class="btn btn-warning" type="button" onclick="highlightDroppingAlert()">
                            <?php echo Yii::t('child','Dropping Out');?> <span class="badge"><?php echo $droppingCount; ?></span>
                        </button>
                        <?php endif; ?>
                        <?php if ($redalertCount): ?>
                        <button class="btn btn-danger" type="button" onclick="highlightInvoiceAlert()">
                            <?php echo Yii::t('site','# without invoice of today');?> <span class="badge"><?php echo $redalertCount; ?></span>
                        </button>
                        <?php endif; ?>
                    </h1>
            <?php } ?>
            </div>
        </div>        
        <?php if ($showReturnTable && $returnData): ?>
            <div class="row nextChild">
                <div class="col-lg-8 col-sm-12 col-wrapper pr0">
                    <div class='flex align-items mb10'>
                        <div class='flex1'>

                            <div class='font16 color3 fontBold'><?php echo Yii::t('child', 'Breakdown of Next Year Student'); ?></div>
                            <div class='font12 color6'><?php echo Yii::t('child', 'Clicking a number will highlight corresponding students (scroll down to student list)'); ?></div>
                        </div>
                        <button class="btn btn-primary ml16 " onclick="exportReturnData()"><?php echo Yii::t('labels', 'Export'); ?></button>
                    </div>
                </div>
                <div class="col-lg-8 col-sm-12 col-wrapper pr0">
                    <table class="table nextChildTable table-bordered">
                        <tbody>
                            <tr>
                                <th width='10%' rowspan=2><span class='fontBold'><?php echo $year; ?>-<?php echo $year+1; ?></span></th>
                                <th width='10%' rowspan=2 style='font-weight:bold;background:#5CB85C;color:#fff'>New</th>
                                <th colspan='4' style='font-weight:bold;background:#F0AD4E;color:#fff'>Return</th>
                                <th width='13%' rowspan=2>Total Grade</th>
                                <th width='13%' rowspan=2 style='background:#FCF1F1;color:#D9534F'>Only Deposit Paid</th>
                            </tr>
                            <tr >
                                <th width='12%' style='background:#F0AD4E;color:#fff'>Other Ivy</th>
                                <th width='12%' style='background:#F0AD4E;color:#fff'><?php echo $fromText; ?></th>
                                <th width='12%' style='background:#F0AD4E;color:#fff'><?= $this->branchObj->abb; ?></th>
                                <th width='12%' style='background:#F0AD4E;color:#fff'>Total Return</th>
                            </tr>
                            <?php foreach ($returnData as $item):?>
                                <tr>
                                    <th style='background: #F2F3F5;'><?php echo $item['classTitle']; ?></th>
                                    <td style='background:#F2F9F2'>
                                        <div onclick="highlightReturn('<?= $item['classType'];?>', 'newChildData', 'all',this,<?= $item['newChildData']['allNum']; ?>)" class="<?= $item['newChildData']['allNum'] > 0 ? 'text-primary text-left' : 'text-left'; ?>"><?= $item['newChildData']['allNum']; ?></div>
                                        <?php if($item['newChildData']['unPaidNum'] > 0): ?>
                                            <span onclick="highlightReturn('<?= $item['classType'];?>', 'newChildData', 'unpaid',this,<?= $item['newChildData']['unPaidNum']; ?>)" class="childPaidNum" data-toggle="tooltip" data-placement="left" title="<?php echo Yii::t('child', 'Tuition & Deposit Unpaid: '); ?><?= $item['newChildData']['unPaidNum']; ?>"><?= $item['newChildData']['unPaidNum']; ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td style='background:#FDF8F1;' >
                                        <div onclick="highlightReturn('<?= $item['classType'];?>', 'ivyChildData', 'all',this,<?= $item['ivyChildData']['allNum']; ?>)" class="<?= $item['ivyChildData']['allNum'] > 0 ? 'text-primary text-left' : ' text-left'; ?>"><?= $item['ivyChildData']['allNum']; ?></div>
                                            <?php if($item['ivyChildData']['unPaidNum'] > 0): ?>
                                                <span onclick="highlightReturn('<?= $item['classType'];?>', 'ivyChildData', 'unpaid',this,<?= $item['ivyChildData']['unPaidNum']; ?>)" class="childPaidNum" data-toggle="tooltip" data-placement="left" title="<?php echo Yii::t('child', 'Tuition & Deposit Unpaid: '); ?><?= $item['ivyChildData']['unPaidNum']; ?>" ><?= $item['ivyChildData']['unPaidNum']; ?></span>
                                            <?php endif; ?>
                                    </td>
                                    <td  style='background:#FDF8F1;' >
                                        <div onclick="highlightReturn('<?= $item['classType'];?>', 'transChildData', 'all',this,<?= $item['transChildData']['allNum']; ?>)" class="<?= $item['transChildData']['allNum'] > 0 ? 'text-primary text-left' : 'text-left'; ?>"><?= $item['transChildData']['allNum']; ?></div>
                                            <?php if($item['transChildData']['unPaidNum'] > 0): ?>
                                                <span onclick="highlightReturn('<?= $item['classType'];?>', 'transChildData', 'unpaid',this,<?= $item['transChildData']['unPaidNum']; ?>)" class="childPaidNum" data-toggle="tooltip" data-placement="left" title="<?php echo Yii::t('child', 'Tuition & Deposit Unpaid: '); ?><?= $item['transChildData']['unPaidNum']; ?>" ><?= $item['transChildData']['unPaidNum']; ?></span>
                                            <?php endif; ?>
                                    </td>
                                    <td  style='background:#FDF8F1;' >
                                        <div onclick="highlightReturn('<?= $item['classType'];?>', 'localChildData', 'all',this,<?= $item['localChildData']['allNum']; ?>)" class="<?= $item['localChildData']['allNum'] > 0 ? 'text-primary text-left' : 'text-left'; ?>"><?= $item['localChildData']['allNum']; ?></div>
                                            <?php if($item['localChildData']['unPaidNum'] > 0): ?>
                                                <span onclick="highlightReturn('<?= $item['classType'];?>', 'localChildData', 'unpaid',this,<?= $item['localChildData']['unPaidNum']; ?>)" class="childPaidNum" data-toggle="tooltip" data-placement="left" title="<?php echo Yii::t('child', 'Tuition & Deposit Unpaid: '); ?><?= $item['localChildData']['unPaidNum']; ?>" ><?= $item['localChildData']['unPaidNum']; ?></span>
                                            <?php endif; ?>
                                    </td>
                                    <td  style='background:#FDF8F1;' >
                                        <div onclick="highlightReturn('<?= $item['classType'];?>', 'returnChildData', 'all',this,<?= $item['returnChildData']['allNum']; ?>)" class="<?= $item['returnChildData']['allNum'] > 0 ? 'text-primary text-left' : 'text-left'; ?>"><?= $item['returnChildData']['allNum']; ?></div>
                                            <?php if($item['returnChildData']['unPaidNum'] > 0): ?>
                                                <span onclick="highlightReturn('<?= $item['classType'];?>', 'returnChildData', 'unpaid',this,<?= $item['returnChildData']['unPaidNum']; ?>)" class="childPaidNum" data-toggle="tooltip" data-placement="left" title="<?php echo Yii::t('child', 'Tuition & Deposit Unpaid: '); ?><?= $item['returnChildData']['unPaidNum']; ?>" ><?= $item['returnChildData']['unPaidNum']; ?></span>
                                            <?php endif; ?>
                                    </td>
                                    <td style='background: #F2F3F5;'><?php echo $item['classNum']; ?></td>
                                    <td style='background:#FCF1F1'>
                                        <div onclick="highlightReturn2('<?= $item['classType'];?>', 'paidChildData', 'deposit',this,<?= $item['paidChildData']['depositNum']; ?>)" class="<?= $item['paidChildData']['depositNum'] > 0 ? 'colorRed' : ''; ?>">
                                            <?php if($item['paidChildData']['depositNum'] > 0): ?>
                                                <span  data-toggle="tooltip" data-placement="left" title="<?php echo Yii::t('child', 'Deposit Paid, Tuition NOT Paid: '); ?><?= $item['paidChildData']['depositNum']; ?>"><?= $item['paidChildData']['depositNum']; ?></span>
                                            <?php else:; ?>
                                                <span><?= $item['paidChildData']['depositNum']; ?></span>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <!-- <td style='background:#FCF1F1'>
                                        <div onclick="highlightReturn2('<?= $item['classType'];?>', 'paidChildData', 'none',this,<?= $item['paidChildData']['noneNum']; ?>)"  class="<?= $item['paidChildData']['noneNum'] > 0 ? 'colorRed' : ''; ?>">
                                            <?php if($item['paidChildData']['noneNum'] > 0): ?>
                                                <span  data-toggle="tooltip" data-placement="left" title="<?php echo Yii::t('child', 'None of Deposit and Tuition Paid: '); ?><?= $item['paidChildData']['noneNum']; ?>"><?= $item['paidChildData']['noneNum']; ?></span>
                                            <?php else:; ?>
                                                <span><?= $item['paidChildData']['noneNum']; ?></span>
                                            <?php endif; ?>
                                        </div>
                                    </td> -->
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <div class="col-lg-4 col-sm-12 col-wrapper pr0">
                    <div class='nextChildTips'>
                        <div class='font14 colorOrange fontBold flex'><span class='glyphicon glyphicon-info-sign mr4 mt2'></span><?php echo Yii::t('child', 'This table is generated based on class placement and tuition/deposit for the next year.'); ?></div>
                        <div class='font12 color3 mt10 mb8'><?php echo Yii::t('child', 'Rules are as follows: '); ?></div>
                        <div class='font12 color3 '><span class='fontBold'><?php echo Yii::t('child', 'New Students: '); ?></span><?php echo Yii::t('child', 'No tuition billing records at this school in the current academic year.'); ?></div>
                        <div class='font12 color3 mt4'><span class='fontBold'><?php echo Yii::t('child', 'Transfer Students: '); ?></span><?php echo Yii::t('child', 'Currently enrolled in another school.'); ?></div>            
                        <div class='font12 color3 mt4'><span class='fontBold'><?php echo Yii::t('child', 'Returning Students: '); ?></span><?php echo Yii::t('child', 'Currently enrolled at this school.'); ?></div>          
                    </div>
                </div>
            </div>
        <?php endif; ?>
        <div class="row">
            <div class="col-md-12">
                <div class="row">
                    <?php
                    if ($this->branchObj->type == 50) {
                        $col = (count($ages) == 4) ? "col-md-3 col-sm-6" : "col-md-4 col-sm-6";
                        if($this->branchId == 'BJ_QF' || $this->branchId == 'BJ_QFF' ){
                            $ages = array(
                                '-2-' => '-2-',
                                '-3-' => '-3-',
                                '-4-' => '-4-',
                                '-5-' => '-5-',
                            );
                        }
                        else if($this->branchId == 'BJ_SLT') {
                            $ages = array(
                                '-5-6-7-8-' => '-5-6-7-8-',
                                '-9-10-' => '-9-10-',
                                '-11-' => '-11-',
                            );
                        }
                        else{
                            $ages = array(
                                '-5-6-7-8-9-' => '-5-6-7-8-9-',
                                '-10-11-12-' => '-10-11-12-',
                                '-13-14-15-16-17-18-' => '-13-14-15-16-17-18-',
                            ); 
                        }
                    } else {
                        $col = (count($ages) >= 4) ? "col-md-3 col-sm-6" : "col-md-4 col-sm-6";
                        if (count($ages) == 5) {
                            $ages = array(
                                '-1-2-' => '-1-2-',
                                '-3-' => '-3-',
                                '-4-' => '-4-',
                                '-5-' => '-5-',
                            );
                        }
                    }
                    foreach (array_keys($ages) as $age):
                        ?>
                        <div class="<?php echo $col;?> col-wrapper" age="<?php echo $age?>" style="display: none;"></div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <div style="display: none">
            <?php foreach ($classes as $class): ?>
                <div class="class-item" age="<?php echo $class['age']; ?>">
                    <div class="panel panel-default">
                        <div class="panel-heading"><span class="text font14"><?php echo $class['title']; ?>
                                <span class="badge badgeBg ml8" id="<?php echo $class['id'] ?>"></span>
                                <a href="<?php echo $this->createUrl('//mcampus/student/exportUser', array('childsClass' => $class['id']));  ?>"  target="_blank" class="btn btn-xs btn-primary  print-link" style="display:none">
                                    <span class="glyphicon glyphicon-print"></span> <?php echo Yii::t('site', '打印'); ?>
                                </a>
                            </span>
                            <span class="dropdown pull-right">
                                <a class-dropdown=flag data-toggle=dropdown href="#">
                                    <span class="glyphicon glyphicon-list"></span>
                                </a>
                            </span>
                        </div>
                        <div class="panel-body" id="class-box-<?php echo $class['id']; ?>">
                            <ul class="nav nav-pills">
                            </ul>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>

    <!-- Invoice Status Modal -->
    <div class="modal" id="invoiceModal" tabindex="-1" role="dialog" aria-labelledby="invoiceModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                    <h4 class="modal-title" id="myModalLabel"></h4>
                </div>
                <div class="modal-body">
                    <div class="well">
                        <p>
                            图例：
                            <span class="glyphicon glyphicon-briefcase"></span> 入园材料费
                            <span class="glyphicon glyphicon-heart"></span> 预缴学费
                            <span class="glyphicon glyphicon-book"></span> 学费
                            <span class="glyphicon glyphicon-cutlery"></span> 餐费
                            <span class="glyphicon glyphicon-road"></span> 校车费
                        </p>

                        <p>
                            颜色：
                            <a href="javascript:(void)" class="s20"><span>已付款</span></a>
                            <a href="javascript:(void)" class="s10"><span>未付款</span></a>
                        </p>
                        <p class='flex'>
                           <span>总计：</span>
                            <!-- <span class="glyphicon glyphicon-briefcase"></span> 入园材料费
                            <span class="glyphicon glyphicon-heart"></span> 预缴学费
                            <span class="glyphicon glyphicon-book"></span> 学费
                            <span class="glyphicon glyphicon-cutlery"></span> 餐费
                            <span class="glyphicon glyphicon-road"></span> 校车费 -->
                            <span id='total'></span>
                        </p>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-bordered" id="class-invoice-table">
                            <thead></thead>
                            <tbody id="class-invoice-table-body table table-hover">

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Parent Contact Modal -->
    <div class="modal" id="contactModal" tabindex="-1" role="dialog" aria-labelledby="contactModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                    <h4 class="modal-title" id="contactModalLabelTitle"></h4>
                </div>

                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="table-responsive">
                                <table id="parent-info" class="table">
                                    <thead>
                                    <tr>
                                        <th class="col-md-4"><?php echo Yii::t('site', 'Childern'); ?></th>
                                        <th class="col-md-4"><?php echo Yii::t('site', 'Father'); ?></th>
                                        <th class="col-md-4"><?php echo Yii::t('site', 'Mother'); ?></th>
                                    </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="contactModal-two" tabindex="-1" role="dialog" aria-labelledby="modal">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                    <h4 class="modal-titles"></h4>
                </div>

                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="table-responsive">
                                <table id="parent-info-card" class="table">
                                    <thead>
                                    <tr>
                                        <th class="col-md-12"><?php echo Yii::t('site', '制卡资料显示 (在输入框点击,然后Ctrl+A 全选)'); ?></th>
                                    </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" tabindex="-1" role="dialog" id='childInfo'  aria-labelledby="modal">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title"><?php echo Yii::t("site", "Student Information");?></h4>
            </div>
            <div class="modal-body">
                <div id='childrenInfo'></div>
            </div>
            </div>
        </div>
    </div>

    <div class="modal fade" tabindex="-1" role="dialog" id='pushMsgListModal'>
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="pushMsgListModalTitle"></h4>
                </div>
                <div class="modal-body">
                    <div id='pushMsgList'></div>
                </div>
            </div>
        </div>
    </div>


    <!-- 显示家庭信息 -->
    <div class="modal fade" tabindex="-1" role="dialog" id='familyInfoModal'>
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title"><?php echo Yii::t("site", "Sibling Families");?></h4>
            </div>
            <div class="modal-body">
                <div id='familyInfo'></div>
            </div>
            </div>
        </div>
    </div>
    <script type="text/template" id="child-item-template">
    <?php if(Yii::app()->user->checkAccess('ivystaff_opschool') && !isset($_GET['it'])){ ?>
        <li country="<%= country %>" gender="<%= gender %>" dob="<%= birth %>" childIndex=<%= childIndex %> childid=<%=
            id %> style="padding-top:2px" class="<%= withDrawalStyle %>"><a target="_blank" href="<% print(childBaseUrl+'&childid='+id); %>" title="ID: <%= id %>&#13;Age: <%= age %><%= isWithDrawal == 1 ? '&#13;<?php echo Yii::t('withdrawal', 'In withdraw process.');?>' : ''%>"><%= name %></a>
        </li>
    <?php }elseif (Yii::app()->user->checkAccess('ivystaff_teacher') || Yii::app()->user->checkAccess('ivystaff_counselor')) { ?>
        <li country="<%= country %>" gender="<%= gender %>" dob="<%= birth %>" childIndex=<%= childIndex %> childid=<%=
            id %>   onclick='getChildInfoModal(<%= id %>)' style="padding-top:2px" class="<%= withDrawalStyle %>" ><a  href="javascript:void(0);" title="ID: <%= id %>&#13;Age: <%= age %><%= isWithDrawal == 1 ? '&#13;<?php echo Yii::t('withdrawal', 'In withdraw process.');?>' : ''%>"><%= name %></a>
        </li>
    <?php  }else{ ?>
        <li country="<%= country %>" gender="<%= gender %>" dob="<%= birth %>" childIndex=<%= childIndex %> childid=<%=
            id %> style="padding-top:2px" class="<%= withDrawalStyle %>"><a target="_blank" href="<% print(childBaseUrl+'&childid='+id); %>" title="ID: <%= id %>&#13;Age: <%= age %><%= isWithDrawal == 1 ? '&#13;<?php echo Yii::t('withdrawal', 'In withdraw process.');?>' : ''%>"><%= name %></a>
        </li>
    <?php  } ?>
    </script>

    <script type="text/template" id="invoice-item-template">
        <a class="s<%= status %>"
           href="<?php echo $this->createUrl('/child/invoice/viewInvoice'); ?>&invoiceid=<%= id%>&childid=<%= childid%>"
           target="_blank" title="<%= id %>"><span class="glyphicon glyphicon-<%= renderIcon(type) %>"></span></a>
    </script>

    <script type="text/template" id="contact-class-item-template">
        <li class="list-group-item">
            <div class="checkbox">
                <label>
                    <input type="checkbox"> <%= title %>
                </label>
            </div>
        </li>
    </script>

    <script>
        var childData = <?php echo CJSON::encode($childData); ?>;
        <?php if (Yii::app()->language == 'zh_cn') { ?>
            childData.sort((a, b)=> a.name.localeCompare(b.name, 'zh'));
        <?php } else { ?>
            childData.sort((a, b) => a.name.charCodeAt(0) - b.name.charCodeAt(0));
        <?php } ?>

        var classList = <?php echo CJSON::encode($classes); ?>;
        var studentCount = {};
        var showStats;
        var viewInvoice;
        var showClassInvoice;
        var childBasicStats; //显示孩子基本统计信息
        var isChildBasicStatsShown = false; //是否已经执行过孩子基本信息统计; 默认为否
        var ymTemplate;
        var total = 0;
        var classLen = 0;
        var template = _.template($('#child-item-template').html());
        var invoiceTemplate = _.template($('#invoice-item-template').html());
        var tmpChildIds = [];
        var tmpChildIndexes = [];
        var childInvoices = {};
        var paymentTypes = ['tuition', 'lunch', 'bus'];
        var extraPaymentTypes = ['registration', 'deposit'];
        var paymentTypeIcons = {
            tuition: 'book',
            lunch: 'cutlery',
            bus: 'road',
            deposit: 'heart',
            registration: 'briefcase'
        };
        var currentMonth = '<?php echo date("m");?>';
        var currentDay = '<?php echo date("m-d");?>';
        var showContact = null; //显示
        var showContactInit = false;

        var highlightBirthStudent;
        var highlightInvoiceAlert;
        var highlightDroppingAlert;
        var highlightLabeled;
        var highlighReturn;
        var exportReturnData;

        //家长账户
        var parentAccount;
        var showParents;
        var exportParent;
        var exportReports;
        var childCards;
        var childsData;
        var parents = {};
        var redalertCount = '<?php echo 'redalert' . $redalertCount; ?>';
        var show = "<?php echo Yii::app()->user->checkAccess('ivystaff_it') ? 1 : 0; ?>"
        var accountName = {mmx: 'ASA', ivy: 'IVY', ds: 'DSO'};

        var returnData = <?= CJSON::encode($returnData); ?>;


        function renderIcon(type) {
            return paymentTypeIcons[type];
        }
        $(function () {
            $('[data-toggle="tooltip"]').tooltip()
            showContact = function (classid) {
                if (!showContactInit) {
                    var contactClassTpl = _.template($('#contact-class-item-template').html());
                    _.each(classList, function (classData, i) {
                        var _view = contactClassTpl(classData);
                        $('.contact-class-list-container').append(_view);
                    })
                }
                var classes = $('div.class-item .panel-heading span.text');
                $('#contactModal').modal();
            };

            highlightBirthStudent = function () {
                if (!_.isNull(childData)) {
                    if ($('li[dob]').hasClass('active1') || $('li[dob]').hasClass('highlight0')) {
                        $('li[dob]').removeClass('active1');
                        $('li[dob]').removeClass('highlight0');
                    }
                    else {
                        for (var i = 0; i < childData.length; i++) {
                            if (childData[i].birth.indexOf('-' + currentDay) > 0) {
                                $('li[dob|="' + childData[i].birth + '"]').addClass('highlight0');
                            }
                            else if (childData[i].birth.indexOf('-' + currentMonth + '-') > 0) {
                                $('li[dob|="' + childData[i].birth + '"]').addClass('active1');
                            }
                        }
                    }
                    if ($('li[dob]').hasClass('active') || $('li[dob]').hasClass('highlight') || $('li[dob]').hasClass('highlight3')) {
                        $('li[dob]').removeClass('highlight');
                        $('li[dob]').removeClass('highlight3');
                    }
                }
            };

            highlightInvoiceAlert = function() {
                if (!_.isNull(childData)) {
                    if ($('li[dob]').hasClass('highlight')) {
                        $('li[dob]').removeClass('highlight');
                    }
                    else {
                        for (var i = 0; i < childData.length; i++) {
                            if (childData[i].redalert == 1) {
                                $('li[childid="' + childData[i].id + '"]').addClass('highlight');
                            }
                        }
                    }
                    if ($('li[dob]').hasClass('active') || $('li[dob]').hasClass('active1') || $('li[dob]').hasClass('highlight0')|| $('li[dob]').hasClass('highlight3')) {
                        $('li[dob]').removeClass('active');
                        $('li[dob]').removeClass('active1');
                        $('li[dob]').removeClass('highlight0');
                        $('li[dob]').removeClass('highlight3');
                    }
                }
            };
            highlightLabeled = function(){
                if (!_.isNull(childData)) {
                    if($('li[dob]').hasClass('highlight3')){
                        $('li[dob]').removeClass('highlight3');
                    }else{
                        for (var i = 0; i < childData.length; i++) {
                            if (childData[i].label.length > 0) {
                                for (var j =0; j < childData[i].label.length; j++){
                                    if(childData[i].label[j]['flag'] == 1){
                                        $('li[childid|="' + childData[i].id + '"]').addClass('highlight3');
                                    }
                                }
                            }
                        }
                    }

                    if ($('li[dob]').hasClass('active') || $('li[dob]').hasClass('active') || $('li[dob]').hasClass('highlight0')|| $('li[dob]').hasClass('highlight')) {
                        $('li[dob]').removeClass('active');
                        $('li[dob]').removeClass('active1');
                        $('li[dob]').removeClass('highlight0');
                        $('li[dob]').removeClass('highlight');
                    }
                }
            };

            highlightDroppingAlert = function() {
                if (!_.isNull(childData)) {
                    if ($('li[dob]').hasClass('highlight2')) {
                        $('li[dob]').removeClass('highlight2');
                    }
                    else {
                        for (var i = 0; i < childData.length; i++) {
                            if (childData[i].status == "888") {
                                $('li[childid="' + childData[i].id + '"]').addClass('highlight2');
                            }
                        }
                    }
                }
            };

            highlightReturn = function (classType, returnType, showType,obj,num) {
                if(num==0){
                    return
                }
                if (showType === 'all') {
                    const $parent = $(obj).parent();
                    if ($parent.hasClass('nextChildTableBg')) {
                        $parent.removeClass('nextChildTableBg');
                        $('li[dob]').removeClass('highlight4');
                        return;
                    }
                } else if ($(obj).hasClass('childPaidNumBg')) {
                    $(obj).removeClass('childPaidNumBg');
                    $('li[dob]').removeClass('highlight4');
                    return;
                }
                $('.nextChildTable td').removeAttr('class');
                $('.childPaidNum').removeClass('childPaidNumBg');
                $.each(returnData, function(_index, _item) {
                    if (_item['classType'] == classType) {
                        let childIds = [];
                        if (showType == 'all') {
                            childIds = [..._item[returnType].paidChildIds, ..._item[returnType].unPaidChildIds];
                        } else {
                            childIds = _item[returnType].unPaidChildIds;
                        }
                        if (childIds.length > 0 && !_.isNull(childData)) {
                            if (showType == 'all') {
                                $(obj).parent().addClass('nextChildTableBg');
                            } else {
                                $(obj).addClass('childPaidNumBg');
                            }
                            if ($('li[dob]').hasClass('highlight4')) {
                                $('li[dob]').removeClass('highlight4');
                            }
                            for (var i = 0; i < childData.length; i++) {
                                // 判断 childData.id 是否在数组 childIds 中
                                // childData.id 转换为int类型
                                childData[i].id = parseInt(childData[i].id)
                                if (childIds.indexOf(childData[i].id) > -1) {
                                    $('li[childid="' + childData[i].id + '"]').addClass('highlight4');
                                }
                            }
                        }
                    }
                })
            };

            highlightReturn2 = function (classType, returnType, showType,obj, num) {
                if(num==0){
                    return
                }
                const $parent = $(obj).parent();
                if ($parent.hasClass('nextChildTableBg')) {
                    $parent.removeClass('nextChildTableBg');
                    $('li[dob]').removeClass('highlight4');
                    return;
                }
                $('.nextChildTable td').removeAttr('class');
                $('.childPaidNum').removeClass('childPaidNumBg');
                $.each(returnData, function(_index, _item) {
                    if (_item['classType'] == classType) {
                        let childIds = [];
                        if (showType == 'deposit') {
                            childIds = _item[returnType].depositChildIds;
                        } else {
                            childIds = _item[returnType].noneChildIds;
                        }
                        if (childIds.length > 0 && !_.isNull(childData)) {
                            $(obj).parent().addClass('nextChildTableBg');
                            if ($('li[dob]').hasClass('highlight4')) {
                                $('li[dob]').removeClass('highlight4');
                            }
                            for (var i = 0; i < childData.length; i++) {
                                // 判断 childData.id 是否在数组 childIds 中
                                // childData.id 转换为int类型
                                childData[i].id = parseInt(childData[i].id)
                                if (childIds.indexOf(childData[i].id) > -1) {
                                    $('li[childid="' + childData[i].id + '"]').addClass('highlight4');
                                }
                            }
                        }
                    }
                })
            };
          
            //初始化校园校历下拉菜单
            $('div.class-item .dropdown a[class-dropdown]').click(function () {
                if (_.isUndefined($(this).attr('dropdown-ini'))) {
                    $(this).after($('#dropdown-item-template').html());
                    $(this).attr('dropdown-ini', 1);
                }
            });

            showStats = function () {
                var boxlist = $('.class-item');
                for (var i = 0; i < boxlist.length; i++) {
                    var count =  $(boxlist[i]).find('ul.nav li').length;
                    total += count;
                    if (isCurrent == 0 || count > 0) {
                        classLen += 1;
                    }
                    $(boxlist[i]).find('.panel-heading span.badge').html(count);
                }
                $('#total-number').html(total);
                $('#class-number').html(classLen);
            };

            showClassInvoice = function (obj) {
                $('#class-invoice-table tbody').empty();
                if ($('#class-invoice-table thead').html() == '') {
                    var thdr = $('<tr></tr>').append('<th></th>');
                    for (var i = 0; i < ymTemplate.length; i++) {
                        thdr.append($('<th class="text-center"></th>').html('<small>' + ymTemplate[i] + '</small>'));
                    }
                    // thdr.appendTo($('#class-invoice-table thead'));
                    $('#class-invoice-table thead').append(thdr);
                }

                $('.modal-title').html($(obj).parents('.panel-heading').find('span.text').html());
                var tmpChilds = $(obj).parents('.panel-heading').siblings('.panel-body').find('ul.nav li');
                tr = '';
                var _x = 0;
                // console.log(obj)
                // console.log(tmpChilds)
                // console.log(extraPaymentTypes)
                // console.log(paymentTypes)
                $.each(tmpChilds, function (_index, _item) {
                    var _childid = $(_item).attr('childid');
                    tr += '<tr><th rowspan="3" childid=' + _childid + '>' + $(_item).find('a').html() + '<div>';
                    var _data = '';
                    for (var y = 0; y < extraPaymentTypes.length; y++) {
                        if (!_.isUndefined(childInvoices[_childid]) && !_.isUndefined(childInvoices[_childid][extraPaymentTypes[y]])) {
                            var _tmpInvoice = childInvoices[_childid][extraPaymentTypes[y]];
                            _data += invoiceTemplate(_tmpInvoice);
                        }
                    }
                    tr += _data + '</div></th>';
                    for (var i = 0; i < paymentTypes.length; i++) {
                        if (i != 0) tr += '<tr>';
                        for (var j = 0; j < ymTemplate.length; j++) {
                            if (!_.isUndefined(childInvoices[_childid]) && !_.isUndefined(childInvoices[_childid][paymentTypes[i]]) && !_.isUndefined(childInvoices[_childid][paymentTypes[i]][ymTemplate[j]])) {
                                var _tmpArrs = childInvoices[_childid][paymentTypes[i]][ymTemplate[j]];
                                var _data = '';
                                for (var z = 0; z < _tmpArrs.length; z++) {
                                    _data += invoiceTemplate(_tmpArrs[z]);
                                }
                                tr += '<td class="text-center">' + _data + '</td>'
                            } else {
                                tr += '<td class="text-center">&nbsp;</td>';
                            }

                        }
                        tr += '</tr>';
                    }
                    _x += 1;
                    if ((_x % 5) == 0) {
                        tr += $('#class-invoice-table thead').html();
                    }
                });
                $('#class-invoice-table tbody').html(tr);
            };

            viewInvoice = function (obj) {
                var tmpChilds = $(obj).parents('.panel-heading').siblings('.panel-body').find('ul.nav li');
                tmpChildIds = [];
                tmpChildIndexes = [];
                $.each(tmpChilds, function (_index, _item) {
                    tmpChildIds.push($(_item).attr('childid'));
                    tmpChildIndexes.push($(_item).attr('childindex'));
                });
                if (_.isUndefined(childInvoices[tmpChilds.first().attr('childid')])) {
                    var postData = {};
                    postData.selectedYid = selectedYid;
                    if (childData.length <= 60) { //如果孩子总数少于60，一次取回
                        postData.rangeAll = 1;
                    } else {//孩子总数大于100，分班取回
                        postData.childIds = encodeURI(tmpChildIds.join());
                        postData.rangeAll = 0;
                    }
                    $.ajax({
                        url: "<?php echo $this->createUrl('//mcampus/student/invoices');?>",
                        type: 'POST',
                        dataType: 'json',
                        async: false,
                        data: postData
                    }).done(function (data) {
                        if (data.state == 'success') {
                            // console.log(data)
                            var cids = _.keys(data.data.invoices);
                            for (var m = 0; m < cids.length; m++) {
                                childInvoices[cids[m]] = data.data.invoices[cids[m]];
                            }
                            totalData(data.data.invoices)
                            ymTemplate = data.data.ym;
                        } else {
                            alert(data.message);
                        }
                    });
                }

                $('#invoiceModal').modal();
                showClassInvoice(obj);
            };
            function totalData(childInvoices){
                // console.log(childInvoices)
                var arr = []
                for(var key in childInvoices){
                    for(var tle in childInvoices[key]){
                        // console.log(childInvoices[key])
                        arr.push(tle)
                    }
                }
                // console.log(arr)
               function getWordCnt(){
                    var obj = {};
                    for(var i= 0, l = arr.length; i< l; i++){
                        var item = arr[i];
                        obj[item] = (obj[item] +1 ) || 1;
                    }
                    return obj;
                }
                var list=getWordCnt()
                var data='<span class="glyphicon glyphicon-briefcase"></span> 入园材料费：'+(list.registration?list.registration:0)+'人<br><span class="glyphicon glyphicon-heart"></span> 预缴学费：'+(list.deposit?list.deposit:0)+'人<br><span class="glyphicon glyphicon-book"></span> 学费：'+(list.tuition?list.tuition:0)+'人<br><span class="glyphicon glyphicon-cutlery"></span> 餐费：'+(list.lunch?list.lunch:0)+'人<br><span class="glyphicon glyphicon-road"></span> 校车费：'+(list.bus?list.bus:0)+'人'
                $('#total').html(data)
                // console.log(getWordCnt())
            }
            var _ages = _.keys(_.groupBy(classList, 'age'));
            _.each(_ages, function (_age) {
                $('div.class-item[age|="' + _age + '"]').appendTo($('div.col-wrapper[age*="-' + _age + '-"]'));
            });
            //当前学年，隐藏空班级；未来学年不隐藏
            if (isCurrent==0){
                $('.class-item').show();
                $('.class-item').parent().show()
            }else{
                $('.class-item').hide();
            }
            if (!_.isNull(childData)) {
                for (var i = 0; i < childData.length; i++) {
                    childData[i]['childIndex'] = i;
                    var view = template(childData[i]);
                    var box = $('#class-box-' + childData[i]['classid'] + ' .nav-pills');
                    box.parents('.class-item').parent().show();
                    box.parents('.class-item').show();
                    box.append(view);
                }
                showStats();
            }

            childBasicStats = function () {
                if (!isChildBasicStatsShown) {
                    $.each($('div.class-item'), function (_i, _d) {
                        var _bomTpl = $('<div class="panel-footer"></div>');
                        var _numFemale = _numMale = _numCNMain = _numCNOther = _numNonCN = 0;
                        if ($(_d).find('.panel .panel-body li[childid]').length) {
                            $.each($(_d).find('.panel .panel-body li[childid]'), function (_m, _n) {
                                if ($(_n).attr('gender') == "1") {
                                    _numMale += 1;
                                } else {
                                    _numFemale += 1;
                                }
                                var _country = $(_n).attr('country');
                                if (_country == "36") {
                                    _numCNMain += 1;
                                } else if (_country == "175") {
                                    _numCNOther += 1;
                                } else {
                                    _numNonCN += 1;
                                }
                            });
                            _bomTpl.html('' +
                                ( (_numMale > 0) ? '<span class="mr5"><small><a href="javascript:;" onclick="filteByAttr(this, \'gender\',\'1\')"><?php echo Yii::t('child','Boy');?> (' + _numMale + ')</a></small></span>' : '') +
                                ( (_numFemale > 0) ? '<span class="mr5"><small><a href="javascript:;" onclick="filteByAttr(this, \'gender\',\'2\')"><?php echo Yii::t('child','Girl');?> (' + _numFemale + ')</a></small></span>' : '') +
                                ( (_numCNMain > 0) ? '<span class="mr5"><small><a href="javascript:;" onclick="filteByAttr(this, \'country\',\'36\')"><?php echo Yii::t('child','CN Mainland');?> (' + _numCNMain + ')</a></small></span>' : '') +
                                ( (_numCNOther > 0) ? '<span class="mr5"><small><a href="javascript:;" onclick="filteByAttr(this, \'country\',\'175\')"><?php echo Yii::t('child','CN Other');?> (' + _numCNOther + ')</a></small></span>' : '') +
                                ( (_numNonCN > 0) ? '<span class="mr5"><small><a href="javascript:;" onclick="filteByAttr(this, \'country\',\'other\')"><?php echo Yii::t('child','Foreigner');?> (' + _numNonCN + ')</a></small></span>' : '')
                            );
                            $(_d).find('div.panel').append(_bomTpl);
                        }
                    });
                    isChildBasicStatsShown = true;
                }
            };

            filteByAttr = function (obj, type, data) {
                var _items = $(obj).parents('div.panel-footer').siblings('div.panel-body').find('li[childid]');
                _items.removeClass('active');
                if (type == 'country' && data == 'other') {
                    $(obj).parents('div.panel-footer').siblings('div.panel-body').find('li[childid][country!="36"][country!="175"]').addClass('active');
                } else {
                    $(obj).parents('div.panel-footer').siblings('div.panel-body').find('li[childid][' + type + '|=' + data + ']').addClass('active');
                }
                $(obj).parents('div.panel-footer').siblings('div.panel-body').find('li[childid]').removeClass('active1')
                $(obj).parents('div.panel-footer').siblings('div.panel-body').find('li[childid]').removeClass('highlight')
                $(obj).parents('div.panel-footer').siblings('div.panel-body').find('li[childid]').removeClass('highlight0')
                $(obj).parents('div.panel-footer').siblings('div.panel-body').find('li[childid]').removeClass('highlight3')
            };

            childBasicStats();

            // 家长账户
            parentAccount = function (obj) {
                var tmpChilds = $(obj).parents('.panel-heading').siblings('.panel-body').find('ul.nav li');
                tmpChildIds = [];
                tmpChildIndexes = [];
                $.each(tmpChilds, function (_index, _item) {
                    tmpChildIds.push($(_item).attr('childid'));
                    tmpChildIndexes.push($(_item).attr('childindex'));
                });
                if (_.isUndefined(parents[tmpChilds.first().attr('childid')])) {
                    var postData = {};
                    postData.childIds = encodeURI(tmpChildIds.join());
                    postData.rangeAll = 0;
                    $.ajax({
                        url: "<?php echo $this->createUrl('//mcampus/student/getParentAccount');?>",
                        type: 'POST',
                        dataType: 'json',
                        async: false,
                        data: postData
                    }).done(function (data) {
                        if (data.state == 'success') {
                            var cids = _.keys(data.data);
                            for (var m = 0; m < cids.length; m++) {
                                parents[cids[m]] = data.data[cids[m]];
                            }
                        } else {
                            alert(data.message);
                        }
                    });
                }

                showParents(obj);
                $("#contactModal").modal();
            };

            showParents = function (obj) {
                $('#parent-info tbody').empty();
                if ($('#parent-info tbody').html() == '') {
                    var thdr = $('<tr></tr>').append('');
                    $('#parent-info tbody').append(thdr);
                }

                $('.modal-title').html($(obj).parents('.panel-heading').find('span.text').html());
                $('.modal-title .print-link').show();
                var tmpChilds = $(obj).parents('.panel-heading').siblings('.panel-body').find('ul.nav li');
                var tr = '';
                $.each(tmpChilds, function (_index, _item) {
                    var _childid = $(_item).attr('childid');
                    var child = parents[_childid];
                    var father = parents[_childid]['father'];
                    var mother = parents[_childid]['mother'];
                    tr += '<tr>';
                    tr += '<td class="text-center">' + child['photo'];
                    tr += '<p class="color3 mt10 font14">' + child['name'] + '</p>';
                    tr += '<p class="color3 mt10 font14">#' + child['childid'] + '</p></td>';
                    if (father['email']) {
                        tr += '<td><p class="color6"><span class="glyphicon glyphicon-user mr5"> </span> ' + father['name'] + '</p>';
                        tr += '<p class="color6"><span class="glyphicon glyphicon-phone mr5"> </span> ' + father['mphone'] + '</p>';
                        tr += '<p class="color6"><span class="glyphicon glyphicon-envelope mr5"> </span> ' + father['email'] + '</p>';
                        tr += '<p class="color6"><span class="glyphicon glyphicon-lock mr5"> </span> ' + father['passwordText'] + father['passChanged'] + '</p>';
                        tr += '<p class="textBlue" onclick="showWechat(this)"><img src="/themes/base/images/wechat.png" class="mr2" /> ' + father['asd'];
                        if(father.fWechat.length!=0){
                            tr += '<span class="glyphicon glyphicon-chevron-down ml5 text-primary up" ></span><span class="glyphicon glyphicon-chevron-up down ml5 text-primary" ></span></p>';
                            tr += '<div class="wechat mb20">'
                            for(var key in father.fWechat){
                                tr += '<div class="relative pb20"><p class="flex"><span class="color6 flex1">'+accountName[key]+' 已绑定微信</span> <span>'
                                if(show==1){
                                    tr += '<span class="glyphicon glyphicon-qrcode font20" onclick="showQrcode(this)" data-key='+key+' data-id='+father.pid+'></span>'
                                }
                                tr += '</span></p><div class="qrcode"></div>'
                                for(var i=0;i<father.fWechat[key].length;i++){
                                    tr += '<div class="flex mt10"><span class="imgParent"><img src=' + father.fWechat[key][i]['headimgurl']+' /></span>' +
                                        '<div><span class="flex1 color6 ml10 wechatName" >'+ father.fWechat[key][i]['nickname']
                                    if(father.fWechat[key][i]['isDev']==1){
                                        tr += ' <span class="label label-info">IT DEV</span></span>'
                                    }
                                    if(father.fWechat[key][i]['openid']){
                                        tr += ' <p class=""><a href="javascript:;" onclick="showPushMsg(\''+father.fWechat[key][i]['openid']+'\')">'+father.fWechat[key][i]['openid']+'</a></p>'
                                    }
                                    tr += '</div></div>'
                                }
                                tr+='</div>';
                            }
                            tr+='</div>'
                        }
                        tr += father['loginInfo'] + '</td>';
                    } else {
                        tr += '<td><p class="color6"><?php echo Yii::t('site', 'No Info'); ?></p></td>';
                    }
                    if (mother['email']) {
                        tr += '<td><p class="color6"><span class="glyphicon glyphicon-user mr5"> </span> ' + mother['name'] + '</p>';
                        tr += '<p class="color6"><span class="glyphicon glyphicon-phone mr5"> </span> ' + mother['mphone'] + '</p>';
                        tr += '<p class="color6"><span class="glyphicon glyphicon-envelope mr5"> </span> ' + mother['email'] + '</p>';
                        tr += '<p class="color6"><span class="glyphicon glyphicon-lock mr5"> </span> ' + mother['passwordText'] + mother['passChanged'] + '</p>';
                        tr += '<p class="textBlue" onclick="showWechat(this)"><img src="/themes/base/images/wechat.png" class="mr2"/> ' + mother['asd']
                        if(mother.mWechat.length!=0){
                            tr += '<span class="glyphicon glyphicon-chevron-down ml5 text-primary up" ></span><span class="glyphicon glyphicon-chevron-up down ml5 text-primary"></span></p>';
                            tr += '<div class="wechat mb20">'
                            for(var key in mother.mWechat){
                                tr += '<div class="relative pb20"><p class="flex"><span class="color6 flex1">'+accountName[key]+' 已绑定微信</span> <span>'
                                if(show==1){
                                    tr += '<span class="glyphicon glyphicon-qrcode font20" onclick="showQrcode(this)" data-key='+key+' data-id='+mother.pid+'></span>'
                                }
                                tr += '</span></p><div class="qrcode"></div>'
                                for(var i=0;i<mother.mWechat[key].length;i++){
                                    tr += '<div class="flex mt10"><span class="imgParent"><img src=' + mother.mWechat[key][i]['headimgurl']+' /></span>' +
                                        '<div><span class="flex1 color6 ml10 wechatName" >'+ mother.mWechat[key][i]['nickname']
                                    if(mother.mWechat[key][i]['isDev']==1){
                                        tr += ' <span class="label label-info">IT DEV</span></span>'
                                    }else{
                                        tr += '</span>'
                                    }
                                    if(mother.mWechat[key][i]['openid']){
                                        tr += '<p class=""><a href="javascript:;" onclick="showPushMsg(\''+mother.mWechat[key][i]['openid']+'\')">'+mother.mWechat[key][i]['openid']+'</a></p>'
                                    }
                                    tr += '</div></div>'
                                }
                                tr+='</div>';
                            }
                            tr+='</div>'
                        }
                        tr += mother['loginInfo'] + '</td>';
                    } else {
                        tr += '<td><p class="color6"><?php echo Yii::t('site', 'No Info'); ?></p></td>';
                    }
                    tr += '</tr>';
                });
                $('#parent-info tbody').html(tr);
            };
            showPushMsg = function (obj){
                $.ajax({
                    url: "<?php echo $this->createUrl('//mcampus/student/getPushMsg');?>",
                    type: 'POST',
                    dataType: 'json',
                    async: false,
                    data: {openid: obj}
                }).done(function (data) {
                    if (data.state == 'success') {
                        $('#pushMsgList').html(data.data)
                        $("#pushMsgListModalTitle").html('推送列表')
                        $('#pushMsgListModal').modal()
                    }
                });
            }
            $("#pushMsgListModal").on("hidden.bs.modal",function(){
                $(document.body).addClass("modal-open");
            });
            showWechat= function (obj) {
                var node=$(obj).find('.up')
                if(!node.is(':visible')){
                    $(obj).parent().find('.wechat').hide()
                    $(obj).find('.up').show()
                    $(obj).find('.down').hide()
                }else{
                    $(obj).parent().find('.wechat').show()
                    $(obj).find('.up').hide()
                    $(obj).find('.down').show()
                }
            }
            showQrcode = function (obj) {
                let account=$(obj).attr('data-key')
                let pid=$(obj).attr('data-id')
                $('.qrcode').hide()
                $.ajax({
                    url: "<?php echo $this->createUrl('//mcampus/student/getBindQrcode');?>",
                    type: 'POST',
                    dataType: 'json',
                    async: false,
                    data: {account, pid}
                }).done(function (data) {
                    if (data.state == 'success') {
                        var str='<p class="mt10 text-right"><span class="glyphicon glyphicon-remove mr10 font14" onclick="hideQrcode(this)"></span></p><div class="text-center"><img src='+data.data+' /></div><div class="color6 font14 text-center mt10">扫码绑定微信</div>'
                        $(obj).parent().parent().next().html(str)
                        $(obj).parent().parent().next().show()
                    } else {
                        alert(data.message);
                    }
                });


            }
            hideQrcode= function (obj) {
                $(obj).parent().parent().hide()
            }
            // 导出家长信息
            exportParent = function (obj) {
                var tmpChilds = $(obj).parents('.panel-heading').siblings('.panel-body').find('ul.nav li');
                tmpChildIds = [];
                tmpChildIndexes = [];
                $.each(tmpChilds, function (_index, _item) {
                    tmpChildIds.push($(_item).attr('childid'));
                    tmpChildIndexes.push($(_item).attr('childindex'));
                });
                if (_.isUndefined(parents[tmpChilds.first().attr('childid')])) {
                    var childIds = encodeURI(tmpChildIds.join());
                    var url = "<?php echo $this->createUrl('//mcampus/student/exportParent', array('category'=>$category));?>" + '&childIds=' + childIds;

                    $.ajax({
                      url: url,
                      type: 'POST',
                      data: {},
                      success: function (res) {
                        if (res.state == 'success') {
                            var data = res.data.items;
                            const filename = res.data.title;
                            const ws_name = "";

                            let rows = [];
                            data.forEach((r, k) => {
                                if (k !== 0) {
                                    r[7] = new Date(r[7]);
                                }
                                rows.push(r);
                            });

                            const worksheet = XLSX.utils.aoa_to_sheet(data);
                            const workbook = XLSX.utils.book_new();
                            XLSX.utils.book_append_sheet(workbook, worksheet, ws_name);
                            // generate Blob
                            const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                            const blob = new Blob([wbout], {type: 'application/octet-stream'});
                            // save file
                            let link = document.createElement('a');
                            link.href = URL.createObjectURL(blob);
                            link.download = filename;
                            link.click();
                            setTimeout(function() {
                                // 延时释放掉obj
                                URL.revokeObjectURL(link.href);
                                link.remove();
                            }, 500);
                        }
                      },
                      dataType: 'json'
                    });
                }
            };

            exportReturnData = function () {
                let exportData = [];
                $.each(returnData, function(_index, _item) {
                    if(_item.classType!='all'){

                    
                    const classType = _item['classType'];
                    const newChildData = _item['newChildData'];
                    const ivyChildData = _item['ivyChildData'];
                    const transChildData = _item['transChildData'];
                    const localChildData = _item['localChildData'];
                    const depositChildIds = _item['paidChildData']['depositChildIds'];

                    $.each(newChildData['paidChildIds'], function(_index, childId) {
                        const tempChild = getChildInfo(childId);
                        // 修改这里，使用push将数据添加到exportData数组
                        if(depositChildIds.includes(childId)){
                            exportData.push([classType, childId, tempChild.name, 'New', 'Only Deposit Paid']);
                        }else{
                            exportData.push([classType, childId, tempChild.name, 'New', 'Tuition Paid']);
                        }
                    });
                    $.each(newChildData['unPaidChildIds'], function(_index, childId) {
                        const tempChild = getChildInfo(childId);
                        // 修改这里，使用push将数据添加到exportData数组
                        exportData.push([classType, childId, tempChild.name, 'New', 'Tuition & Deposit Unpaid']);
                    });
                    $.each(ivyChildData['paidChildIds'], function(_index, childId) {
                        const tempChild = getChildInfo(childId);
                        // 修改这里，使用push将数据添加到exportData数组
                        if(depositChildIds.includes(childId)){
                            exportData.push([classType, childId, tempChild.name, 'Other Ivy', 'Only Deposit Paid']);
                        }else{
                            exportData.push([classType, childId, tempChild.name, 'Other Ivy', 'Tuition Paid']);
                        }
                    });
                    $.each(ivyChildData['unPaidChildIds'], function(_index, childId) {
                        const tempChild = getChildInfo(childId);
                        // 修改这里，使用push将数据添加到exportData数组
                        exportData.push([classType, childId, tempChild.name, 'Other Ivy', 'Tuition & Deposit Unpaid']);
                    })
                    $.each(transChildData['paidChildIds'], function(_index, childId) {
                        const tempChild = getChildInfo(childId);
                        // 修改这里，使用push将数据添加到exportData数组
                        if(depositChildIds.includes(childId)){
                            exportData.push([classType, childId, tempChild.name, '<?php echo $fromText; ?>', 'Only Deposit Paid']);
                        }else{
                            exportData.push([classType, childId, tempChild.name, '<?php echo $fromText; ?>', 'Tuition Paid']);
                        }
                    });
                    $.each(transChildData['unPaidChildIds'], function(_index, childId) {
                        const tempChild = getChildInfo(childId);
                        // 修改这里，使用push将数据添加到exportData数组
                        exportData.push([classType, childId, tempChild.name, '<?php echo $fromText; ?>', 'Tuition & Deposit Unpaid']);
                    });
                    $.each(localChildData['paidChildIds'], function(_index, childId) {
                        const tempChild = getChildInfo(childId);
                        // 修改这里，使用push将数据添加到exportData数组
                        if(depositChildIds.includes(childId)){
                            exportData.push([classType, childId, tempChild.name, 'Returning', 'Only Deposit Paid']);
                        }else{
                            exportData.push([classType, childId, tempChild.name, 'Returning', 'Tuition Paid']);
                        }
                    });
                    $.each(localChildData['unPaidChildIds'], function(_index, childId) {
                        const tempChild = getChildInfo(childId);
                        // 修改这里，使用push将数据添加到exportData数组
                        exportData.push([classType, childId, tempChild.name, 'Returning', 'Tuition & Deposit Unpaid']);
                    });
                }
                });
                let branchAbb = '<?php echo $this->branchObj->abb;?>';
                let currentYear = '<?php echo $year; ?>';
                const nowDate = new Date();
                const year = nowDate.getFullYear();
                const month = String(nowDate.getMonth() + 1).padStart(2, '0'); 
                const day = String(nowDate.getDate()).padStart(2, '0'); 
                const filename = branchAbb+'-'+currentYear+'-StudentsData_' +year+month+day+ '.xlsx';
                const ws_name = "SheetJS";
                
                // 加上表头
                exportData.unshift(['年级', '学生ID', '学生姓名', '返校类型', '账单状态']);
                const worksheet = XLSX.utils.aoa_to_sheet(exportData);
                const workbook = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(workbook, worksheet, ws_name);
                // generate Blob
                const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                const blob = new Blob([wbout], {type: 'application/octet-stream'});
                // save file
                let link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = filename;
                link.click();
                setTimeout(function() {
                    // 延时释放掉obj
                    URL.revokeObjectURL(link.href);
                    link.remove();
                }, 500);

            };

            getChildInfo = function (childId) {
                childId = parseInt(childId);
                // 修改这里，将const改为let，因为后面会修改这个对象
                let childInfo = {};
                $.each(childData, function(_index, _item) {
                    if (parseInt(_item.id) == childId) {
                        childInfo = _item;
                        return false;
                    }
                });
                return childInfo;
            };

            // 导出孩子列表
            exportChildInfo = function (obj) {
                var url = "<?php echo $this->createUrl('//mcampus/student/exportChildInfo', array('category' => $category)); ?>";
                $.ajax({
                  url: url,
                  type: 'POST',
                  // dataType: 'json',
                  data: {},
                  success: function (res) {
                    res = eval("("+res+")");
                    if (res.state == 'success') {
                        var data = res.data.items;
                        const filename = res.data.title;
                        const ws_name = "";

                        let rows = [];
                        data.forEach((r, k) => {
                            if (k !== 0) {
                                r[8] = new Date(r[8]);
                            }
                            rows.push(r);
                        });

                        const worksheet = XLSX.utils.aoa_to_sheet(rows);
                        const workbook = XLSX.utils.book_new();
                        XLSX.utils.book_append_sheet(workbook, worksheet, ws_name);
                        // generate Blob
                        const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                        const blob = new Blob([wbout], {type: 'application/octet-stream'});
                        // save file
                        let link = document.createElement('a');
                        link.href = URL.createObjectURL(blob);
                        link.download = filename;
                        link.click();
                        setTimeout(function() {
                            // 延时释放掉obj
                            URL.revokeObjectURL(link.href);
                            link.remove();
                        }, 500);
                    }
                  },
                  error: function(XMLHttpRequest, textStatus, errorThrown){
                    // alert(XMLHttpRequest.readyState + XMLHttpRequest.status + XMLHttpRequest.responseText);
                  },
                }).done(function ( data, textStatus, jqXHR) {
                    // console.log(jqXHR.status);
                });
            };

            exportReports = function (obj) {

                var tmpChilds = $(obj).parents('.panel-heading').siblings('.panel-body').find('ul.nav li');
                tmpChildIds = [];
                tmpChildIndexes = [];
                $.each(tmpChilds, function (_index, _item) {
                    tmpChildIds.push($(_item).attr('childid'));
                });
                if (_.isUndefined(parents[tmpChilds.first().attr('childid')])) {
                    var childIds = encodeURI(tmpChildIds.join());
                    window.open("<?php echo $this->createUrl('//mcampus/student/exportReports', array('category'=>$category));?>" + '&childIds=' + childIds, "_blank");
                }
            };

            childCards = function (obj) {

                var tmpChilds = $(obj).parents('.panel-heading').siblings('.panel-body').find('ul.nav li');
                tmpChildIds = [];
                tmpChildIndexes = [];
                $.each(tmpChilds, function (_index, _item) {
                    tmpChildIds.push($(_item).attr('childid'));
                });
                if (_.isUndefined(parents[tmpChilds.first().attr('childid')])) {
                    var childIds = encodeURI(tmpChildIds.join());
                    window.open("<?php echo $this->createUrl('//mcampus/student/childcards', array('category'=>$category));?>" + '&childIds=' + childIds, "_blank");
                }
            };



            //制卡资料
            childsData = function (obj) {
                var Cdata;
                var tmpChilds = $(obj).parents('.panel-heading').siblings('.panel-body').find('ul.nav li');
                tmpChildIds = [];
                tmpChildIndexes = [];
                $.each(tmpChilds, function (_index, _item) {
                    tmpChildIds.push($(_item).attr('childid'));
                    tmpChildIndexes.push($(_item).attr('childindex'));
                });
                if (_.isUndefined(parents[tmpChilds.first().attr('childid')])) {
                    var postData = {};
                    postData.childIds = encodeURI(tmpChildIds.join());
                    postData.rangeAll = 0;
                    $.ajax({
                        url: "<?php echo $this->createUrl('//mcampus/student/crads');?>",
                        type: 'POST',
                        dataType: 'json',
                        async: false,
                        data: postData
                    }).done(function (data) {
                        if (data.state == 'success') {
                            Cdata = data.data;
                            var cids = _.keys(data.data);
                            for (var m = 0; m < cids.length; m++) {
                                parents[cids[m]] = data.data[cids[m]];
                            }
                        } else {
                            alert(data.message);
                        }
                    });
                }

                showChilds(obj,Cdata);
                $("#contactModal-two").modal();
            };

            showChilds = function (obj,Cdata) {
                $('#parent-info-card tbody').empty();
                if ($('#parent-info-card tbody').html() == '') {
                    var thdr = $('<tr></tr>').append('');
                    $('#parent-info-card tbody').append(thdr);
                }

                $('.modal-titles').html($(obj).parents('.panel-heading').find('span.text').html());
                var tmpChilds = $(obj).parents('.panel-heading').siblings('.panel-body').find('ul.nav li');
                var tr = '<textarea  rows="20"  class="col-md-12" style="overflow-x:auto;,overflow-y:auto;">';

//                childName
//                childid
//                name
//                photo
//                relation
//                schoolid
//                tel
//                $.each(tmpChilds, function (_index, _item) {
//                    var _childid = $(_item).attr('childid');
//                    var child = parents[_childid];
//                    console.log(_childid);
//                    var father = parents[_childid]['father'];
//                    var mother = parents[_childid]['mother'];
//                    if (father['email']) {
//                        tr += child['childid'] + ',' + child['name'] + ',' + child['schooldid'] + ',' + child['class'] + ',' + father['name'] + ',' + father['cardmphone'] + ',爸爸,' + father['userAvatar'] + "," + '\n'
//                    }
//                    if (mother['email']) {
//                        tr += child['childid'] + ',' + child['name'] + ',' + child['schooldid'] + ',' + child['class'] + ',' + mother['name'] + ',' + mother['cardmphone'] + ',妈妈,' + mother['userAvatar'] + "," +  '\n'
//                    }

//                });
                $.each(Cdata, function (_index, _item) {
                    tr += _item['childid'] + ',' + _item['childName'] + ',' + _item['campus'] + ',' + _item['class'] + ',' + _item['name'] + ',' + _item['tel'] + ',' + _item['relation'] + "," + _item['photo']  + "," + '\n'
                });
                tr += '</textarea >';
                $('#parent-info-card tbody').html(tr);
            };


        });
        // 孩子信息
        function getChildInfoModal(childId){
            $.ajax({
                url: '<?php echo $this->createUrl("childWindowInfo") ?>',
                type: "get",
                dataType: 'html',
                data: {
                    childid:childId
                },
                success: function(html) {
                    $('#childrenInfo').html(html)
                    $('#childInfo').modal()
                },
                error: function(data) {},
            })
        }
        // 家庭信息
        function showFamilyInfo () {
            var familyInfoUrl = '<?php echo $this->createUrl("familyInfo"); ?>';
            $('#familyInfo').html('');
            $('#familyInfoModal').off('show.bs.modal').on('show.bs.modal', function (e) {
                $('#familyInfo').load(familyInfoUrl, function () {
                    head.Util.ajaxForm($('#familyInfoModal'));
                });
            });
            $('#familyInfoModal').modal("show");
        }


    </script>
   

    <div id="dropdown-item-template" style="display: none;">
        <ul class="dropdown-menu">
            <li><a onclick="viewInvoice(this)" href="javascript:void(0)"><span
                        class="glyphicon glyphicon-adjust"></span> <?php echo Yii::t('site', 'Invoice Overview'); ?></a>
            </li>
            <li><a onclick="parentAccount(this)" href="javascript:void(0)"><span
                        class="glyphicon glyphicon-user"></span> <?php echo Yii::t('site', 'Parent Account'); ?></a>
            </li>
            <li><a onclick="exportParent(this)" href="javascript:void(0)"><span
                        class="glyphicon glyphicon-export"></span> <?php echo Yii::t('site', 'Export Name List'); ?></a>
            </li>
            <li><a onclick="exportReports(this)" href="javascript:void(0)"><span
                        class="glyphicon glyphicon-print"></span> <?php echo Yii::t('site', 'Print attendance table'); ?></a>
            </li>
            <?php if($this->branchObj->type == 50): ?>
                <li><a onclick="childCards(this)" href="javascript:void(0)"><span
                            class="glyphicon glyphicon-credit-card"></span> <?php echo Yii::t('site', '学生卡片'); ?></a>
                </li>
            <?php else:; ?>
                <li><a onclick="childsData(this)" href="javascript:void(0)"><span
                            class="glyphicon glyphicon-credit-card"></span> <?php echo Yii::t('site', '制卡资料'); ?></a>
                </li>
            <?php endif; ?>
        </ul>
    </div>


<?php endif; ?>


