<div class="h_a"><?php echo Yii::t("campus", "User Profile");?></div>
<div class="table_full">
    <table width="100%">
        <colgroup>
            <col width="130">
        </colgroup>
        <tbody>
            <tr>
                <th rowspan="2">
                    <?php echo $staff->getName()?>
                    <?php
                        if($staff->user_avatar):
                            echo CHtml::image(OA::CreateOAUploadUrl('users', $staff->user_avatar), '', array("width"=>80));
                        endif;
                    ?>
                </th>
                <td>
                    <ul>
                        <li><?php echo $branches[$staff->profile->branch]['title']; ?></li>
                        <li><?php echo $staff->email;?></li>
                        <li><?php echo OA::renderChildGender($staff->profile->user_gender);?></li>
                        <li><?php echo $countList[$staff->profile->nationality];?></li>
                        <li><?php echo $this->staff->profile->occupation_en->getName();?></li>
                    </ul>
                </td>
            </tr>
        </tbody>
    </table>
</div>

<div class="nav">
<?php

    $childMainMenu = array(
        'profile'=>array(
            'label' => Yii::t('user', '信息资料'),
            'url' => array('/user/myspace/profile')
        ),
        'account'=>array(
            'label' => Yii::t('user', '人事考勤'),
            'url' => array('/user/hr/index')
        ),
    );
    
    if(OA::isProduction()){
        unset($childMainMenu['lunch']);
    }

	$this->widget('zii.widgets.CMenu',array(
		'items'=> $childMainMenu,
		'activeCssClass'=>'current',
		'htmlOptions'=>array(
			'class'=>'cc',
		)
	));

?>
</div>