*{
	/* On a production site, use a real reset instead of this! */
	margin:0;
	padding:0;  
}            
    
body{
	background:#f5f5f5;
	font-family:"Microsoft Yahei","Lucida Grande", Arial;
	font-size:13px;
	line-height:21px;
	color:#444;  
}

p{
	margin: 0 0 1em 0;
}              

h2{
}

#container{
	/*background:#fff;*/
	/*border-left:1px #ddd solid;*/
	/*border-right:1px #ddd solid;*/
	/*border-bottom:1px #ddd solid;*/
	width:600px;
	margin:0 auto;
}  

header{
	margin: 10px 0;
}
header h1#logo{
	text-indent:-9999px;
	display:block;
	width:600px;
	height:60px;
	background:url(../images/logo_med.png) no-repeat 0 0;
}             

#content{
	width:100%;
	margin:0 auto;
}

footer{
	text-align:center;
	width:100%;
	display:block;    
	font-size:11px;
	margin-top: 2em;
	color:#999;
}
#journal footer, #journal h2, #journal h3, #journal p.week{
	text-shadow: 0px -1px #eee,0 2px #fff;
}
#journal h2, #journal h3{
	margin-bottom: 0.2em;
}
#journal h3{
	padding-bottom:0.2em;
	border-bottom: 1px dotted #999;
}
#journal h2 span{
	font-weight:normal;
	font-size: 80%;
	padding-left: 0.5em;
}
#journal div.html{
	margin-bottom: 1em;
}
#journal p.week{
	font-size: 80%;
	color:#999;
}
#journal span.uname{
	font-size: 80%;
	padding-right: 1em;
}
img {
	max-width:570px;
	margin:0.1em 0;
}                                             
img.face{
	max-width:90px;
	margin-right: 0.5em;
	float: left;
	border: 1px solid #eee;
	background:#fff;
	padding: 2px;
}
.child-avatar{
	position: absolute;
	top: 0.2em;
	right: 0.2em;
}
.child-avatar img{
	border: 1px solid #dedede;
	background:#fff;
	padding: 2px;
	height: 160px;
}
@media screen and (min-width:1200px){
	body{
		font-size: 100%;
	}
	img {
		max-width:1000px;
	}
	img.face{
		max-width: 80px;
	}
	#container{
		width:1100px;
	}  
	
	header h1#logo{
		width:1100px;
		height:60px;
		background:url(../images/logo_med.png) no-repeat top left;
	}
	.input{
		width: 40%;
	}
	.child-avatar img{
		height: 120px;
	}
}

@media screen and (max-width:767px){  
	body{
		font-size: 12px;
	}
	img {
    	max-width:305px;
	}
	img.face{
		max-width: 50px;
	}
	#container{
		width:320px;
	}
	
	header h1#logo{
		width:320px;
		height:40px;
        background-size: contain;
		background:url(../images/logo_small.png) no-repeat 0 0;
	}                           
	.input{
		width: 60%;
	}
	.child-avatar img{
		height: 80px;
	}	
}

@media screen and (max-width:322px){
	body{
		font-size: 12px;
	}
	img {
    	max-width:280px;
	}
	img.face{
		max-width: 40px;
	}
	#container{
		width:280px;
		padding: 0 16px;
	}
	
	header h1#logo{
		width:280px;
		height:40px;
        background-size: contain;
		background:url(../images/logo_small.png) no-repeat 0 0;
	}                           
	.input{
		width: 60%;
	}
	.child-avatar img{
		height: 70px;
	}	
	
}

.input, textarea, select {
	padding: 4px 4px;
	font-size: 100%;
	line-height: 18px;
	border: 1px solid #ccc;
	background-color: #fff;
	box-shadow: 2px 2px 2px #f0f0f0 inset;
	vertical-align: middle;
	margin: 0;
	font-family: inherit;
	width: 50%;
}
.btn {
	color: #333;
	background: #e6e6e6 url(../images/btn.png);
	border: 1px solid #c4c4c4;
	border-radius: 2px;
	text-shadow: 0 1px 1px rgba(255, 255, 255, 0.75);
	padding: 4px 10px;
	display: inline-block;
	cursor: pointer;
	font-size: 100%;
	line-height: normal;
	text-decoration: none;
	overflow: visible;
	vertical-align: middle;
	text-align: center;
	zoom: 1;
	white-space: nowrap;
	font-family: inherit;
	_position: relative;
	margin: 0;
}
.msg-notice{
	margin: 1em 0;
	border: 1px solid #F6DE70;
	background: #FFF2BB;
	padding: 1em 2em;
}
