<?php

class FrontendController extends ProtectedController
{
	public $childId = 0;
	public $journal = false;
	public $key1 = '!ivyOnline';
	public $key2 = 'journal';
	
	public function parseInput($str){
		$str = base64_decode($str);
		$params = explode("+", $str);
		$this->childId = intval(isset($params['0']) ? str_replace($this->key1,"",$params['0']) : 0 );
		$this->journal = isset($params['1'])? str_replace($this->key2,"",$params['1']) : false;		
	}
	//public function actionIndex($id)
	//{
	//	$this->parseInput($id);
	//	$child = ChildProfileBasic::model()->findByPk($this->childId);
	//	if($child != null){
	//		$this->layout = "//layouts/frameset2";
	//		Yii::app()->params["checkJournal"] = $this->journal;
	//		Yii::app()->params["ccid"] = $child->childid;
	//		$this->render('index',array("child"=>$child));				
	//	}else{
	//		$this->forward('//backend/default');
	//	}
	//	Yii::app()->end();
	//}

	public function actionIndex($id)
	{
		Yii::app()->theme = 'classic';
		if(Yii::app()->user->checkAccess('tPreviewCC')):
			$this->parseInput($id);
			$child = ChildProfileBasic::model()->findByPk($this->childId);
			if($child != null){
				//把可以预览孩子前台的标识写入SESSION；供孩子前台判断
				Yii::app()->user->setState('_previewCCToken', OA::genPreviewCCToken());
				$this->layout = "//layouts/frameset2";
				Yii::app()->params["checkJournal"] = $this->journal;
				Yii::app()->params["ccid"] = $child->childid;
				$branch = Branch::model()->findByPk($child->schoolid);
                if ($branch->type == Branch::PROGRAM_DAYSTAR){
//                if (CommonUtils::isGradeSchool($child->classid, true)){
                    Yii::app()->params["ccPreviewUrl"] = Yii::app()->params['cccPreviewUrl'];
                }else{
                    Yii::app()->params["ccPreviewUrl"] = Yii::app()->params['ccPreviewUrl'];
                }
				$this->render('index',array("child"=>$child));				
			}else{
				$this->forward('//backend/default');
			}
		else:
			$this->forward('//backend/default');
		endif;
		
		Yii::app()->end();
	}

	public function actionStaffview()
	{
		$this->layout = "//layouts/blank";
		$this->render('staffview');
	}
	
	public function actionLogout()
	{
		Yii::app()->user->logout();
		$this->render("logout", array("url"=>Yii::app()->user->loginUrl));
		Yii::app()->end();
	}
	//
	//public function actionDefault()
	//{
	//	$this->layout = "//layouts/blank";
	//	$this->render('default');
	//}
	// Uncomment the following methods and override them if needed
	/*
	public function filters()
	{
		// return the filter configuration for this controller, e.g.:
		return array(
			'inlineFilterName',
			array(
				'class'=>'path.to.FilterClass',
				'propertyName'=>'propertyValue',
			),
		);
	}

	public function actions()
	{
		// return external action classes, e.g.:
		return array(
			'action1'=>'path.to.ActionClass',
			'action2'=>array(
				'class'=>'path.to.AnotherActionClass',
				'propertyName'=>'propertyValue',
			),
		);
	}
	*/
}