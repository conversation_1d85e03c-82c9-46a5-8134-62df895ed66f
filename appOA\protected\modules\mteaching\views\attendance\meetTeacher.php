<!-- 加载底部导航栏 -->
<?php
$this->branchSelectParams['extraUrlArray'] = array('//mgrades/attendance/index');

$timeData = array(5=>5, 10=>10, 15=>15, 20=>20);
?>

<div class="row modal-text">

    <div class="col-md-12">
        <div class="row">
            <div class="col-md-8">
                <h4>
                    <span><?php echo $meet_base_data['title'] ; ?></span>
                </h4>
                <p>
                    <span class="glyphicon glyphicon-time"></span>
                    <?php
                        $week = substr( $weekday, 0, 1 );
                        $period = substr($weekday,-1,1);
                        $weekArr = array(
                            1=> Yii::t('attends','Mon'),
                            2=> Yii::t('attends','Tue'),
                            3=> Yii::t('attends','Wed'),
                            4=> Yii::t('attends','Thu'),
                            5=> Yii::t('attends','Fri'),
                            );
                        $getTime = TimetableCourses::getTime();
                        echo sprintf(Yii::t('attends', '%s period #%s'), $weekArr[$week], $period);
                    ?>
                    <span class="label label-info"><?php echo $getTime[$period]; ?></span>
                </p>
            </div>
        </div>
        <?php if($status < 1):?>
            <div class="alert alert-danger"><?php echo Yii::t('attends','View Only! You have no attendance permission of this course.');?></div>
        <?php endif;?>
        <hr>
            <input type="hidden" value='<?php echo $status ?>' id='status'>
        <div>
            <div class='color6 mt20 font14'><?php echo Yii::t('attends','Meet Owner') ?></div>
            <div class='col-md-4'>
                <div class="media mt15">
                    <div class="media-left pull-left media-middle">
                        <a href="javascript:void(0)">
                            <img src="<?php echo $meet_teachers[$meet_leader_id]['photoUrl']?>" data-holder-rendered="true" style='width:48px;height:48px' class="media-object img-circle image"></a>
                    </div>
                    <div class="media-body pt10 media-middle">
                        <h4 class="media-heading font12"><?php echo $meet_teachers[$meet_leader_id]['name']?>&nbsp;&nbsp;<span class="label label-success">发起人</span></h4>
                        <div class="text-muted"><?php echo $meet_teachers[$meet_leader_id]['hrPosition']?></div>
                    </div>
                </div>
            </div>
            <div class='clearfix'></div>
        </div>
        <div>
            <div class='color6 mt20 font14'><?php echo Yii::t('attends','Members') ?>  <span class="badge"><?php echo count($acceding_teacher_id)?></span></div>
            <?php foreach ($acceding_teacher_id as $k=>$id){?>
                <div class='col-md-4'>
                    <div class="media mt15">
                        <div class="media-left pull-left media-middle">
                            <a href="javascript:void(0)">
                                <img src="<?php echo $meet_teachers[$id]['photoUrl']?>" data-holder-rendered="true" style='width:48px;height:48px' class="media-object img-circle image"></a>
                        </div>
                        <div class="media-body pt10 media-middle">
                            <h4 class="media-heading font12"><?php echo $meet_teachers[$id]['name']?></h4>
                            <div class="text-muted omit"><?php echo $meet_teachers[$id]['hrPosition']?></div>
                        </div>
                    </div>
                </div>
            <?php }?>
            <div class='clearfix'></div>
        </div>

    </div>
</div>
<style>
    .modal-text .active, .modal-text .btn-default.active{
        color: #ffffff;
        background-color: #428bca;
        border-color: #357ebd;
    }
    .modal-text .label-1 {
        border: 0;
        padding: 6px 12px;
    }
    .btn-group > .btn:hover {
        z-index: 99;
    }
    .omit{
        overflow:hidden;
        text-overflow: ellipsis;
        white-space:nowrap;
    }
</style>
