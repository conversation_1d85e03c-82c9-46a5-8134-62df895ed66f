<script type="text/javascript">
$(function() {
    var chart ={
        data: {
            table: ''
        },
        chart: {
            type: 'column'
        },
        title: {
            text: ''
        },
        yAxis: {
            allowDecimals: false,
            title: {
                text: '￥'
            }
        }
    };
    
    <?php foreach ($revenue as $mkey=>$mrev):?>
    chart.data.table = document.getElementById('datatable_<?php echo $mkey?>');
    chart.title.text = '<?php echo ucfirst($mkey)?>';
    $('#container_<?php echo $mkey?>').highcharts(chart);
    <?php endforeach;?>
});
</script>

<?php foreach ($revenue as $mkey=>$mrev):?>
<div style="margin-bottom: 20px;">
<div id="container_<?php echo $mkey?>" style="min-width: 310px; height: 400px; margin: 0 auto;"></div>

<table id="datatable_<?php echo $mkey?>" style="display: none;">
	<thead>
		<tr>
			<th></th>
			<th>Budgeted Revenue</th>
			<th>Autual Revenue</th>
		</tr>
	</thead>
	<tbody>
        <?php foreach ($mrev as $rkey=>$rev):?>
		<tr>
			<th><?php echo $rkey?></th>
			<td><?php echo $rev[0]?></td>
			<td><?php echo $rev[1]?></td>
		</tr>
        <?php endforeach;?>
	</tbody>
</table>

<div class="table_list">
    <table width="100%">
        <thead>
            <tr>
                <th width="88"></th>
                <?php foreach (array_keys($mrev) as $rkey):?>
                <th><?php echo $rkey?></th>
                <?php endforeach;?>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>Budgeted Revenue</td>
                <?php foreach ($mrev as $rev):?>
                <td align="center"><?php echo $rev[0]?></td>
                <?php endforeach;?>
            </tr>
            <tr>
                <td>Autual Revenue</td>
                <?php foreach ($mrev as $rev):?>
                <td align="center"><?php echo $rev[1]?></td>
                <?php endforeach;?>
            </tr>
        </tbody>
    </table>
</div>
</div>
<?php endforeach;?>
<script src="<?php echo Yii::app()->theme->baseUrl; ?>/js/modules/data.js"></script>