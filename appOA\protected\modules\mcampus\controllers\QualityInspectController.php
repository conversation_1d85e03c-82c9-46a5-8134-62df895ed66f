<?php

class QualityInspectController extends BranchBasedController
{

    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";
        Yii::import('common.models.products.*');

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Campus Workspace');
        //不选择学校
//        $this->multipleBranch = false;
//        初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mcampus/qualityInspect/indexSchool');
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/viewer/viewer.css');

        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/plupload/plupload.full.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/qiniu.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/vue.global.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/sortable/Sortable.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/viewer/viewer.js');
        
    }

    public function beforeAction($action)
    {
        //$this->accessBranch 可以看的学校
        foreach ($this->accessBranch as $key => $item) {
            if (in_array($item, array('BJ_QFF', 'BJ_DS', 'BJ_SLT', 'BJ_BU', 'BJ_SS','BJ_IASLT'))) {
                unset($this->accessBranch[$key]);
            }
        }
        //不是是否总部员工不可以查看模板
        $isHQstaff = $this->isHQ();
        if (!$isHQstaff && strtolower($action->getId()) == 'index') {
            throw new CHttpException(403, 'No permission');
        }
        return parent::beforeAction($action);
    }

    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function isHQ(){
        if($this->staff->uid == 8020811){
            #特殊员工处理
            return true;
        }elseif($this->staff->isHQstaff()){
            return true;
        }else{
            return false;
        }
    }

    //校园质检项目
    public function actionIndex()
    {
//        header('Content-Type: text/html; charset=utf-8');
//        echo "<pre>";
//        var_dump($this->accessBranch);die;
        $mySchool = $this->accessBranch;
        $this->render('index', array('isHQstaff' => $this->isHQ()));
    }

    public function actionDetail()
    {
        $this->render('detail', array(
            'isHQstaff' => $this->isHQ(),
        ));
    }

    public function actionOverview()
    {
        $this->render('overview', array(
            'isHQstaff' => $this->isHQ(),
        ));
    }
    public function actionIndexSchool()
    {
        if($this->branchId == 'BJ_TYG'){
            $this->render('index', array('isHQstaff' => $this->isHQ()));
        }else{
            $this->render('indexSchool', array('isHQstaff' => $this->isHQ()));
        }
    }
    //质检项目模板
    public function actionTemplate()
    {
        $this->render('template', array());
    }

    public function actionCategoryManage()
    {
        $this->render('categoryManage', array());
    }

    //获取模板列表
    public function actionGetTemplateList()
    {
        $mySchool = $this->accessBranch;
        $this->remote('qualityTemp/list', 'get', array(
            'school_id' => $this->branchId,
        ));
    }

    //获取模板的详情
    public function actionTemplateInfo()
    {
        $id = Yii::app()->request->getParam('id', 0);
        $this->remote('qualityTemp/templateInfo/'.$id, 'get', array(
            'school_id' => $this->branchId,
        ));
    }

    //添加或者修改模板项目
    public function actionSaveTemplate()
    {
        $data = Yii::app()->request->getParam('data', array());
        $this->remote('qualityTemp/saveTemplate', 'post', array(
            'school_id' => $this->branchId,
            'data'      => $data,
        ));
    }

    //添加模板项目的分类
    public function actionSaveCategory()
    {
        $data = Yii::app()->request->getParam('data', array());
        $this->remote('qualityTemp/saveCategory', 'post', array(
            'data' => $data,
        ));
    }

    //添加模板分类的小分类
    public function actionSaveCategorySub()
    {
        $items_id = Yii::app()->request->getParam('items_id');
        $category_id = Yii::app()->request->getParam('category_id');
        $list = Yii::app()->request->getParam('list', array());
        $this->remote('qualityTemp/saveCategorySub/'.$items_id.'/'.$category_id, 'post', array(
            'list' => $list,
        ));
    }

    //小类排序
    public function actionCategorySubSort()
    {
        $sub_ids = Yii::app()->request->getParam('sub_ids');
        $this->remote('qualityTemp/categorySubSort', 'post', array(
            'sub_ids' => $sub_ids,
        ));
    }

    //批量添加模板分类的小分类
    public function actionSaveCategorySubMany()
    {
        $items_id = Yii::app()->request->getParam('items_id');
        $list = Yii::app()->request->getParam('list', array());
        $this->remote('qualityTemp/saveCategorySubMany/'.$items_id, 'post', array(
            'list' => $list,
        ));
    }

    //删除模板项目的分类
    public function actionDelCategory()
    {
        $id = Yii::app()->request->getParam('id', 0);
        $this->remote('qualityTemp/delCategory', 'delete', array(
            'id' => $id,
        ));
    }

    //删除模板分类的小分类
    public function actionDelCategorySub()
    {
        $id = Yii::app()->request->getParam('id', 0);
        $this->remote('qualityTemp/delCategorySub', 'delete', array(
            'id' => $id,
        ));
    }

    //删除标准
    public function actionDelCategorySubAttach()
    {
        $id = Yii::app()->request->getParam('id', 0);
        $this->remote('qualityTemp/delCategorySubAttach/'.$id, 'delete', array());
    }

    //批量编辑标准
    public function actionSaveCategorySubAttachMany()
    {
        $items_id = Yii::app()->request->getParam('items_id');
        $category_id = Yii::app()->request->getParam('category_id');
        $sub_id = Yii::app()->request->getParam('sub_id');
        $list = Yii::app()->request->getParam('list', array());
        $this->remote('qualityTemp/saveCategorySubAttachMany/'.$items_id.'/'.$category_id.'/'.$sub_id, 'post', array(
            'school_id' => $this->branchId,
            'list'      => $list,
        ));
    }

    //编辑标准
    public function actionSaveCategorySubAttach()
    {
        $items_id = Yii::app()->request->getParam('items_id');
        $category_id = Yii::app()->request->getParam('category_id');
        $sub_id = Yii::app()->request->getParam('sub_id');
        $number = Yii::app()->request->getParam('number');
        $standards_cn = Yii::app()->request->getParam('standards_cn');
        $standards_en = Yii::app()->request->getParam('standards_en');
        $subAttachId = Yii::app()->request->getParam('sub_attach_id');
        $this->remote('qualityTemp/saveCategorySubAttach/'.$subAttachId, 'post', array(
            'items_id'     => $items_id,//模板项目
            'category_id'  => $category_id,//大类id
            'sub_id'       => $sub_id,//小类id
            'number'       => $number,//编号
            'standards_cn' => $standards_cn,//合格标准
            'standards_en' => $standards_en,//合格标准
        ));
    }

    //启停标准
    public function actionSaveAttachStatus()
    {
        $attach_id = Yii::app()->request->getParam('id', 0);
        $this->remote('qualityTemp/saveAttachStatus/'.$attach_id, 'post', array(
            'school_id' => $this->branchId,
        ));
    }

    //获取上传的token
    public function actionGetUploadToken()
    {
        $linkType = Yii::app()->request->getParam('linkType', 0);
        $this->remote('qualityInspect/getUploadToken', 'get', array(
            'school_id' => $this->branchId,
            'linkType'  => $linkType,
        ));
    }

    //删除附件
    public function actionDelAttach()
    {
        $attachmentId = Yii::app()->request->getParam('attachment_id', 0);
        $this->remote('qualityInspect/delAttach/'.$attachmentId, 'delete', array(
            'school_id' => $this->branchId,
        ));
    }

    //总部获取质检项目
    public function actionGetQualityItem()
    {
        $start_year = Yii::app()->request->getParam('start_year', 0);
        $head_user = Yii::app()->request->getParam('head_user', 0);
        $this->remote('qualityInspect/getQualityItem', 'get', array(
            'school_id'  => $this->branchId,
            'start_year' => $start_year,
            'head_user' => empty($head_user) ? 0 : Yii::app()->user->getId(),
        ));
    }

    //校园获取质检项目
    public function actionGetQualityItemSingle()
    {
        $start_year = Yii::app()->request->getParam('start_year', 0);
        $this->remote('qualityInspect/getQualityItemSingle', 'get', array(
            'school_id'  => $this->branchId,
            'start_year' => $start_year,
        ));
    }

    //添加质检
    public function actionAddQuality()
    {
        $start_year = Yii::app()->request->getParam('start_year', 0);
//        $title_cn = Yii::app()->request->getParam('title_cn', '');
//        $title_en = Yii::app()->request->getParam('title_en', '');
        $num = Yii::app()->request->getParam('num', '');
        $desc_cn = Yii::app()->request->getParam('desc_cn', '');
        $desc_en = Yii::app()->request->getParam('desc_en', '');
        $this->remote('qualityInspect/addQuality', 'post', array(
            'start_year' => $start_year,
//            'title_cn'   => $title_cn,
//            'title_en'   => $title_en,
            'num'   => $num,
            'desc_cn'    => $desc_cn,
            'desc_en'    => $desc_en,
        ));
    }

    //获取质检信息
    public function actionGetQualityItemDetail()
    {
        $id = Yii::app()->request->getParam('id', 0);
        $this->remote('qualityInspect/getQualityItemDetail', 'get', array(
            'id' => $id,
            'school_id'  => $this->branchId,
            'is_school' => 1
        ));
    }

    //编辑质检标题介绍
    public function actionEditQualityInspect()
    {
        $id = Yii::app()->request->getParam('id', 0);
        $title_cn = Yii::app()->request->getParam('title_cn', '');
        $title_en = Yii::app()->request->getParam('title_en', '');
        $num = Yii::app()->request->getParam('num', '');
        $desc_cn = Yii::app()->request->getParam('desc_cn', '');
        $desc_en = Yii::app()->request->getParam('desc_en', '');
        $this->remote('qualityInspect/editQualityInspect', 'post', array(
            'id'       => $id,
//            'title_cn' => $title_cn,
//            'title_en' => $title_en,
            'num' => $num,
            'desc_cn'  => $desc_cn,
            'desc_en'  => $desc_en,
        ));
    }

    //总部填写质检信息
    public function actionSaveQualityInspect()
    {
        $data = $_POST;
        $data['school_id'] = $this->branchId;
        $this->remote('qualityInspect/saveQualityInspect', 'post', $data);
    }

    //整改记录的列表
    public function actionGetQualityRectifyList()
    {
        $tasks_item_id = Yii::app()->request->getParam('tasks_item_id', array());
        $this->remote('qualityInspect/getQualityRectifyList', 'get', array(
            'tasks_item_id' => $tasks_item_id,
            'is_school' => 1,
        ));
    }

    //质检整改记录查看
    public function actionGetQualityRectify()
    {
        $rectify_id = Yii::app()->request->getParam('rectify_id');
        $this->remote('qualityInspect/getQualityRectify', 'get', array(
            'rectify_id' => $rectify_id,
        ));
    }

    //填写整改
    public function actionSaveRectificationRecord()
    {
        $data = $_REQUEST;
        $data['school_id'] = $this->branchId;
        $this->remote('qualityInspect/saveRectificationRecord', 'post', $data);
    }

    //负责人审核整改信息
    public function actionAuditQualityRectify()
    {
        $rectify_id = Yii::app()->request->getParam('rectify_id', '');
        $type = Yii::app()->request->getParam('type', '');
        $status = Yii::app()->request->getParam('status', '');
        $comment = Yii::app()->request->getParam('comment', '');
        $this->remote('qualityInspect/auditQualityRectify', 'post', array(
            'rectify_id' => $rectify_id,
            'type'       => $type,
            'status'     => $status,
            'comment'    => $comment,
        ));
    }

    public function actionAuditQualityRectifyByDepartment()
    {
        $rectify_id = Yii::app()->request->getParam('rectify_id', 0);
        $status = Yii::app()->request->getParam('status', 0);
        $this->remote('qualityInspect/auditQualityRectifyByDepartment', 'post', array(
            'rectify_id' => $rectify_id,
            'status'     => $status,
        ));
    }

    //质检总览 getQualityInspectSummary
    public function actionGetQualityInspectSummary()
    {
        $tasks_id = Yii::app()->request->getParam('tasks_id', 0);
        $this->remote('qualityInspect/getQualityInspectSummary', 'get', array(
            'school_id' => $this->branchId,
            'tasks_id'  => $tasks_id,
        ));
    }

    public function actionSchoolList()
    {
        $this->remote('qualityTemp/schoolList', 'get', array(
            'school_id' => $this->branchId,
        ));
    }

    public function actionGetAllDepartment()
    {
        $school_id = Yii::app()->request->getParam('school_id', '');
        $this->remote('qualityTemp/getAllDepartment', 'get', array(
            'school_id' => $school_id,
        ));
    }

    public function actionEditOpenStatus()
    {
        $id = Yii::app()->request->getParam('id',0);
        $this->remote('qualityInspect/editOpenStatus', 'post', array(
            'id' => $id,
        ));
    }

    public function actionSwitchedItem(){
        $id = Yii::app()->request->getParam('id',0);
        $type = '';
        $this->remote('qualityInspect/switchedItem', 'get', array(
            'type'=>2,
            'id' => $id,
        ));
    }


    public function remote($requestUrl, $method = 'post', $requestData = array())
    {
        $res = CommonUtils::requestDsOnline2($requestUrl, $requestData, $method);
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        }
        $this->showMessage();
    }

}