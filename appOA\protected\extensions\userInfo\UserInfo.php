<?php
class UserInfo extends CWidget
{
    public $staff=null;
    public $branches = null;
    public function run()
    {
        if (is_null($this->staff)){
            $this->staff = $this->getController()->staff;
        }
        if (is_null($this->branches)){
            $this->branches = $this->getController()->getAllBranch();
        }
        $countList = Country::model()->getCountryList();
        Yii::import('common.models.hr.HrPosition');
        $this->staff->profile->occupation_en = HrPosition::model()->findByPk($this->staff->profile->occupation_en);
        $this->render('userinfo', array('staff'=>$this->staff,'branches'=>$this->branches,'countList'=>$countList));
    }
}