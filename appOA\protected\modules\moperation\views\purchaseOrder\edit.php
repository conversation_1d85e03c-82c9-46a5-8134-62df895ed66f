<div class="modal-header">
	<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
	<h4 class="modal-title">选择商品：</h4>
</div>
	<?php $form = $this->beginWidget('CActiveForm',array(
		'id'=>'purchase-order-form',
		// 'enableAjaxValidation'=>false,
		'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
	)) ?>
<div class="modal-body">
	<h4 class="text-center">订单信息</h4>
		<!-- 订单标题 -->
		<div class="form-group">
			<label class="col-xs-2 control-label"><?php echo $form->labelEx($orderModel,'title'); ?></label>
			<div class="col-xs-8">
				<?php echo $form->textField($orderModel,'title',array('maxlength'=>255,'class'=>'form-control')); ?>
			</div>
		</div>
		<!-- 订单标题 -->
		<div class="form-group">
			<label class="col-xs-2 control-label"><?php echo $form->labelEx($orderModel,'pid'); ?></label>
			<div class="col-xs-8">
				<?php echo $form->textField($orderModel,'pid',array('maxlength'=>255,'class'=>'form-control')); ?>
			</div>
		</div>
		<!-- 供应商 -->
		<div class="form-group">
			<label class="col-xs-2 control-label"><?php echo $form->labelEx($orderModel,'vendorid'); ?></label>
			<div class="col-xs-4">
				<?php echo CHtml::dropDownList('vendorType','',$vendorTypeArr,array('maxlength'=>255,'class'=>'form-control','onChange'=>"choseVendor(this)")); ?>
			</div>
			<div class="col-xs-4">
				<?php echo $form->dropDownList($orderModel,'vendorid',$vendorArr,array('maxlength'=>255,'class'=>'form-control','id'=>'vendor')); ?>
			</div>
		</div>
		<!-- 订购校园 -->
		<div class="form-group">
			<label class="col-xs-2 control-label"><?php echo $form->labelEx($orderModel,'school_name'); ?></label>
			<div class="col-xs-8">
				<?php echo $form->textField($orderModel,'school_name',array('maxlength'=>255,'class'=>'form-control')); ?>
			</div>
		</div>
		<?php echo CHtml::hiddenField('PurchaseOrder[schoolid]',$schoolInfo->branchid); ?>
		<!-- 交货日期 -->
		<div class="form-group">
			<label class="col-xs-2 control-label"><?php echo $form->labelEx($orderModel,'delivery_timestamp'); ?></label>
			<div class="col-xs-8">
				<?php echo $form->textField($orderModel,'delivery_timestamp',array('maxlength'=>255,'class'=>'form-control', 'value'=>date('Y-m-d', $orderModel->delivery_timestamp))); ?>
			</div>
		</div>
		<!-- 交货地点 -->
		<div class="form-group">
			<label class="col-xs-2 control-label"><?php echo $form->labelEx($orderModel,'delivery_place'); ?></label>
			<div class="col-xs-8">
				<?php echo $form->textField($orderModel,'delivery_place',array('maxlength'=>255,'class'=>'form-control')); ?>
			</div>
		</div>
		<!-- 付款方式 -->
		<div class="form-group">
			<label class="col-xs-2 control-label"><?php echo $form->labelEx($orderModel,'pay_type'); ?></label>
			<div class="col-xs-8">
				<?php echo $form->dropDownList($orderModel,'pay_type',array('网银'=>'网银','现金'=>'现金','支票'=>'支票'),array('maxlength'=>255,'class'=>'form-control')); ?>
			</div>
		</div>
		<!-- 发票抬头 -->
		<div class="form-group">
			<label class="col-xs-2 control-label"><?php echo $form->labelEx($orderModel,'invoice_title'); ?></label>
			<div class="col-xs-8">
				<?php echo $form->textField($orderModel,'invoice_title',array('maxlength'=>255,'class'=>'form-control','value'=>$schoolInfo->official_name)); ?>
			</div>
		</div>
		<!-- 结算方式 -->
		<div class="form-group">
			<label class="col-xs-2 control-label"><?php echo $form->labelEx($orderModel,'billing_type'); ?></label>
			<div class="col-xs-8" height="100px">
				<?php echo $form->textArea($orderModel,'billing_type',array('maxlength'=>255,'class'=>'form-control','rows'=>12)); ?>
			</div>
		</div>
		<!-- 联络信息 -->
		<div class="form-group">
			<label class="col-xs-2 control-label"><?php echo $form->labelEx($orderModel,'content'); ?></label>
			<div class="col-xs-8">
				<?php echo $form->textArea($orderModel,'content',array('maxlength'=>255,'class'=>'form-control'
				)); ?>
			</div>
		</div>
		<!-- 质量及售后 -->
		<div class="form-group">
			<label class="col-xs-2 control-label"><?php echo $form->labelEx($orderModel,'quality_aftersale'); ?></label>
			<div class="col-xs-8">
				<?php echo $form->textArea($orderModel,'quality_aftersale',array('maxlength'=>255,'class'=>'form-control','rows'=>4)); ?>
			</div>
		</div>
	<h4 class="text-center">选择商品</h4>

	<table class="table table-condensed">
		<colgroup><col width="100"><col width="100"><col width="100"></colgroup>
		<?php 
			foreach ($orderModel->item  as $orderItem) {
				echo '<tr><td><label><input checked="checked"  onChange=countPrice() name="products[]" value="'.$orderItem->pid.'" type="checkbox" class="cate_'.$orderItem->product->cid.'"> '.$orderItem->product->title.'</label></td>';
				echo '<td>'.CHtml::textField($orderItem->pid.'_price',$orderItem->product->price,array('readonly'=>'readonly','size'=>'7')).'</td>';
				echo '<td><input type="number" oninput=nums('.$orderItem->pid.',this,'.$orderItem->num.') onblur="blurnum(this,'.$orderItem->num.')" name='.$orderItem->pid.'_num'.' id='.$orderItem->pid.'_num'.' min=1  value='.$orderItem->num.' onChange=countPrice() ></td>';
				echo '</tr>';
			} 
		?>
		<p>总价:<span id="total_price">¥ 0.00</span></p>
		<?php echo CHtml::hiddenField('PurchaseOrder[total_price]',0.00); ?>
		<?php echo CHtml::hiddenField('PurchaseOrder[schoolid]',$orderModel->schoolid); ?>
	</table>
</div>
<div class="modal-footer">
	<label class="col-xs-2 control-label"></label>
	<button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
	<button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
</div>
	<?php $this->endWidget(); ?>
<script>
	$('#PurchaseOrder_delivery_timestamp').datepicker({'dateFormat':'yy-mm-dd'});
	$(document).ready(function(){
		vendor = eval(<?php echo json_encode($vendorArr);?>);
	});
	var datas=<?php echo json_encode($nums);?>;
	console.log(datas)
	//批量选择商品
	function chosePro(inp,cid) {
		if($(inp).attr("checked")=='checked'){
			$('.cate_'+cid).attr("checked","checked");
		} else{
			$('.cate_'+cid).attr("checked",false);
		}
		countPrice();
	}
	function nums(id,obj,num){
        if(parseInt($(obj).val()) > parseInt(datas[id])){
            $(obj).val(num)
        }
         var reg = /^[0]+[0-9]*$/gi;
		if(reg.test($(obj).val())){
		 	$(obj).val('')
		 }
	}
	function blurnum(obj,num){
		 if($(obj).val()==''){
            $(obj).val(num)
        }
	}
	//筛选外包公司
	function choseVendor(inp) {
		var vendorTypeId = $(inp).val();
		var vendorChosed = vendor[vendorTypeId];
		var str = '';
		$.each(vendorChosed,function(i,value) {
			str += '<option value='+i+'>'+value+'</option>';
		})
		$("#vendor").html(str);
	}
	// 计算商品总额
	function countPrice() {
		var totalPrice = 0;
		$('input').each(function() {
			if ($(this).attr("checked")=='checked') {
				var pid = $(this).val();
				if (pid != 'on') {
					totalPrice += parseFloat($('#'+pid+'_price').val()) * parseInt($('#'+pid+'_num').val());
				}
			}
		})
		$('#total_price').text(totalPrice.formatMoney());
		$('#PurchaseOrder_total_price').val(totalPrice);
	}

	//转换货币格式
	Number.prototype.formatMoney = function (places, symbol, thousand, decimal) {
	    places = !isNaN(places = Math.abs(places)) ? places : 2;
	    symbol = symbol !== undefined ? symbol : "¥ ";
	    thousand = thousand || ",";
	    decimal = decimal || ".";
	    var number = this,
	        negative = number < 0 ? "-" : "",
	        i = parseInt(number = Math.abs(+number || 0).toFixed(places), 10) + "",
	        j = (j = i.length) > 3 ? j % 3 : 0;
	    return symbol + negative + (j ? i.substr(0, j) + thousand : "") + i.substr(j).replace(/(\d{3})(?=\d)/g, "$1" + thousand) + (places ? decimal + Math.abs(number - i).toFixed(places).slice(2) : "");
	};
	countPrice();
	function cbOrder() {
	    $('#modal').modal('hide');
	    $.fn.yiiGridView.update('purchase-order');
	}
</script>