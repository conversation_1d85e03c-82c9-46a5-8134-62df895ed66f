<?php
$initBranchId = $this->branchId;

Yii::import('common.models.hr.*');
Yii::import('common.models.classTeacher.*');

$crit = new CDbCriteria;
$crit->index = 'branchid';
$crit->compare('branchid', $initBranchId);
$crit->order = '`group` ASC';
$branch = Branch::model()->find($crit);

$crit = new CDbCriteria();
$crit->order = 'weight ASC';
$crit->index = 'id';

//所有部门
$depModels = HrDepartment::model()->findAll($crit);
//职位与部门对应关系
$depPosLinkModels = DepPosLink::model()->findAll();

$userData[$initBranchId] = $this->getStaffByBranch($initBranchId);

foreach ($depPosLinkModels as $linkModel) {
    $depPosData[$linkModel->position_id] = array(
        'isLead' => $linkModel->is_lead,
        'depId' => $linkModel->department_id,
    );
}
?>

<script>
    var selectedBranchId = '<?php echo $initBranchId; ?>';
    var userData = <?php echo CJSON::encode( $userData );?>;
    var userArray = [];
    _.each(userData[selectedBranchId], function (v) {
        userArray.push({uid: v.uid, weight: parseInt(v.weight), n: v.positionTitle});
    });
    userArray = _.sortBy(userArray, 'weight');
    var depPosLinks = <?php echo CJSON::encode( $depPosData );?>;
    var countryData = <?php echo CJSON::encode( Country::model()->getCountryList() ); ?>;
</script>

<div class="row">
    <div class="col-md-2">
        <?php
        /*
        $this->widget('ext.search.StaffSearchHandy', array(
            'inputCSS' => 'form-control mb10 search-input',
            'displayLabel'=>'',
            'popPosition' => array('collision'=>"none"),
            'restrictSchool' => 'BJ_OE',
        ))
        */
        ?>

        <div class="panel panel-default">
            <div class="panel-heading">
                <?php echo $branch->title; ?>
            </div>

            <!-- List group -->
            <div class="p10">
                <ul class="list-group" id="staff-stats">
                    <a onclick="stafffilter(this)" href="javascript:void(0)" class="list-group-item active"
                       id="stats-total-count"><?php echo Yii::t('ivyer', 'Total Staff'); ?> <span class="badge"></span></a>
                    <a onclick="stafffilter(this)" href="javascript:void(0)" class="list-group-item stats"
                       uattr="uattr-gender" uattr-value="=2"><?php echo Yii::t('ivyer', 'Female Staff'); ?> <span
                            class="badge"></span></a>
                    <a onclick="stafffilter(this)" href="javascript:void(0)" class="list-group-item stats"
                       uattr="uattr-gender" uattr-value="=1"><?php echo Yii::t('ivyer', 'Male Staff'); ?> <span
                            class="badge"></span></a>
                    <a onclick="stafffilter(this)" href="javascript:void(0)" class="list-group-item stats"
                       uattr="uattr-country" uattr-value="=36"><?php echo Yii::t('ivyer', 'China Mainland'); ?> <span
                            class="badge"></span></a>
                    <a onclick="stafffilter(this)" href="javascript:void(0)" class="list-group-item stats"
                       uattr="uattr-country" uattr-value="=175"><?php echo Yii::t('ivyer', 'China Other'); ?> <span
                            class="badge"></span></a>
                    <a onclick="stafffilter(this)" href="javascript:void(0)" class="list-group-item stats"
                       uattr="uattr-country" uattr-value="=999"><?php echo Yii::t('ivyer', 'Foreigners'); ?> <span
                            class="badge"></span></a>
                </ul>
                <?php
                if (Yii::app()->user->checkAccess('o_H_Export_Staff')) {
                    echo CHtml::link('导出Excel', array('exportExcel'), array('class' => 'btn btn-primary'));
                }
                ?>
            </div>
        </div>
    </div>

    <div class="col-md-10">
        <div class="panel panel-default p20">
            <?php
            foreach ($depModels as $dModel):

                echo CHtml::openTag('div', array('dep-id' => $dModel->id, 'class' => 'dep-container'));
                echo '<h3 class="dep-item"><span class="glyphicon glyphicon-chevron-right"></span> ';
                echo(Yii::app()->language == 'zh_cn' ? $dModel->cn_name : $dModel->en_name);
                echo ' <span class="badge"></span></h3>';
                echo CHtml::openTag('ul', array('class' => 'media-list'));

                echo CHtml::closeTag('ul');
                echo CHtml::closeTag('div');

            endforeach;
            ?>
        </div>
    </div>
</div>


<!-- Modal: 上传员工照片 -->
<div class="modal" id="StaffPhotoModal">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel"><?php echo Yii::t('campus', 'Upload Photo'); ?>
                    <small></small>
                </h4>
            </div>
            <div class="modal-body" id="up-container">
                <button class="btn btn-primary mb15" id="select-photo"
                        type="button"><?php echo Yii::t('campus', 'Photo Select'); ?></button>
                <form class="avatar-form J_ajaxForm" method="post"
                      action="<?php echo $this->createUrl('saveAvatar'); ?>">
                    <div class="row mb15">
                        <input type="hidden" name="staffid" id="staffid">
                        <input type="hidden" name="data" id="data">
                        <input type="hidden" name="ext" id="ext">

                        <div class="col-md-9">
                            <div class="avatar-wrapper"></div>
                        </div>
                        <div class="col-md-3">
                            <div class="avatar-preview preview mb10"></div>
                            <div class="text-center" style="width: 90px;">
                                <a href="" class="btn btn-danger J_ajax_del"
                                   title="<?php echo Yii::t('global', 'Delete'); ?>" id="del_avatar">
                                    <span
                                        class="glyphicon glyphicon-trash"></span> <?php echo Yii::t('global', 'Delete'); ?>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="pop_bottom">
                            <button onclick="$('#StaffPhotoModal').modal('hide')" type="button"
                                    class="btn btn-default pull-right"><?php echo Yii::t('global', 'Cancel'); ?></button>
                            <button type="submit"
                                    class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global', 'Submit'); ?></button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Modal: 上传员工简介 -->
<div class="modal" id="StaffProfileModal">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title"><?php echo Yii::t('campus', 'Edit Profile'); ?>
                    <small></small>
                </h4>
            </div>
            <div class="modal-body">
                <form class="form-horizontal J_ajaxForm" method="post"
                      action="<?php echo $this->createUrl('saveProfile'); ?>">
                    <div id="profile-data"></div>
                    <div class="row">
                        <div class="pop_bottom">
                            <button onclick="$('#StaffProfileModal').modal('hide')" type="button"
                                    class="btn btn-default pull-right"><?php echo Yii::t('global', 'Cancel'); ?></button>
                            <button type="submit"
                                    class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global', 'Submit'); ?></button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Modal: 员工离职设置 -->
<div class="modal" id="StaffResignModal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title"><?php echo Yii::t('ivyer', 'Set Resignment'); ?>
                    <small></small>
                </h4>
            </div>
            <div class="modal-body">
                <?php
                Yii::import('common.models.staff.*');
                $resignLabels = StaffResign::model()->attributeLabels(); ?>
                <form class="form-horizontal J_ajaxForm" method="post"
                      action="<?php echo $this->createUrl('saveResign'); ?>">

                    <h4 id="StaffResignStaffName"><!--place holder--></h4>

                    <div class="alert alert-warning cancel-resign" role="alert">
                        <div class="checkbox">
                            <label>
                                <?php echo CHtml::checkBox('StaffResignRemove', false); ?>
                                <?php echo Yii::t('ivyer', 'Cancel Resign'); ?>
                            </label>
                        </div>
                    </div>

                    <div id="staff-resign-data">
                        <?php echo CHtml::hiddenField('StaffResign[id]', 0); ?>
                        <?php echo CHtml::hiddenField('StaffResign[staff_uid]', 0); ?>

                        <div class="form-group" model-attribute="resign_timestamp">
                            <label class="col-sm-4 control-label">
                                <?php echo $resignLabels['resign_timestamp']; ?></label>

                            <div class="col-sm-8">
                                <?php echo CHtml::textField('StaffResign[resign_timestamp]', '',
                                    array('class' => "form-control length_3")) ?>
                            </div>
                        </div>
                        <div class="form-group" model-attribute="account_close_timestamp">
                            <label class="col-sm-4 control-label">
                                <?php echo $resignLabels['account_close_timestamp']; ?></label>

                            <div class="col-sm-8">
                                <?php echo CHtml::textField('StaffResign[account_close_timestamp]', '',
                                    array('class' => "form-control length_3")) ?>
                            </div>
                        </div>
                        <div class="form-group" model-attribute="departure_why">
                            <label class="col-sm-4 control-label">
                                <?php echo $resignLabels['departure_why']; ?></label>

                            <div class="col-sm-8">
                                <?php echo CHtml::textArea('StaffResign[departure_why]', '',
                                    array('class' => "form-control length_5"))
                                ?>
                                <br/><span class="text-danger"> <span class="glyphicon glyphicon-info-sign"></span>
                                    <?php echo Yii::t('ivyer',
                                        'Account deactivation processes at 17:00 of the day, select a expired date to deactivate account immediately.'); ?>
                                    </span>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="pop_bottom">
                            <button onclick="$('#StaffResignModal').modal('hide')" type="button"
                                    class="btn btn-default pull-right">
                                <?php echo Yii::t('global', 'Cancel'); ?></button>
                            <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10">
                                <?php echo Yii::t('global', 'Submit'); ?></button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Modal: 编辑员工信息 -->
<div class="modal" id="StaffEditModal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title"><?php echo Yii::t('ivyer', 'Edit Account'); ?>
                    <small></small>
                </h4>
            </div>
            <form class="form-horizontal J_ajaxForm" method="post"
                  action="<?php echo $this->createUrl('editAccount'); ?>">
                <div class="modal-body">

                </div>
                <div class="modal-footer">

                    <button type="button"
                            class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t("global", 'Submit'); ?></button>
                    <button type="button" class="btn btn-default"
                            data-dismiss="modal"><?php echo Yii::t("global", 'Cancel'); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>
<!-- Modal: 修改邮箱密码 -->
<div class="modal" id="modifyEmailPasswordModal" backdrop="false">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title"><?php echo Yii::t('ivyer', 'Reset Email Password'); ?>
                    <small></small>
                </h4>
            </div>
            <div class="modal-body">
                <h3>Please contact 请联系 <a href="mailto:<EMAIL>"><EMAIL></a></h3>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", 'Cancel'); ?></button>
            </div>
            <!-- <form class="form-horizontal J_ajaxForm" method="post"
                  action="<?php echo $this->createUrl('changePassword'); ?>">
                <div class="modal-body">

                    <h4 class="col-xs-3" id="modifyEmailUserName">

                    </h4>
                    <div class="col-xs-9"></div>
                    <div class="clearfix"></div>
                    <br/>
                    <div class="form-group">
                        <label class="control-label col-xs-3"><?php echo Yii::t('ivyer', 'Staff Email'); ?></label>
                        <div class="col-xs-9">
                            <input type="text" class="form-control" id="modifyEmailUserEmail" value="" disabled>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-xs-3"><?php echo Yii::t('ivyer', 'New Password'); ?></label>
                        <div class="col-xs-9">
                            <input type="password" class="form-control" name="newPass" value="">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label col-xs-3"><?php echo Yii::t('ivyer', 'Confirm Password'); ?></label>
                        <div class="col-xs-9">
                            <input type="password" class="form-control" name="rePass" value="">
                        </div>
                    </div>
                    <input type="hidden" name="uid" id="modifyEmail_uid"/>
                </div>
                <div class="modal-footer">
                    <input type="reset" id="modifyEmailReset" style="display: none "/>
                    <button type="button"
                            class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t("global", 'Submit'); ?></button>
                    <button type="button" class="btn btn-default"
                            data-dismiss="modal"><?php echo Yii::t("global", 'Cancel'); ?></button>
                </div>
            </form> -->
        </div>
    </div>
</div>
<?php
$this->branchSelectParams['extraUrlArray'] = array('//mcampus/staffProfile/index');
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script type="text/template" id="staff-li-item-template">
    <li class="staff-item active" uattr-gender=<%- gender%> uattr-country=<%- nationality %> data-staffid=<%= uid%>>
        <h4><span class="glyphicon glyphicon-user"></span> <%= name %>
            <small><%= positionTitle %></small>
            </span></h4>
        <div class="media">
            <a class="pull-left" href="javascript:">
                <img class="pull-left img-thumbnail img-face" src="<%- uploadBaseUrl %>/infopub/staff/<%- pubPhoto %>"
                     onclick="openUploadModal(<%= uid %>)">
            </a>

            <div class="media-body" style="min-height: 120px;">
                <button type="button" class="btn btn-default mb5" onclick="openUploadModal(<%= uid %>)">
                    <span class="glyphicon glyphicon-picture"></span> <?php echo Yii::t('campus', 'Upload Photo'); ?>
                </button>
                <button type="button" class="btn btn-default mb5" onclick="openProfileModal(<%= uid %>)">
                    <span class="glyphicon glyphicon-pencil"></span> <?php echo Yii::t('campus', 'Edit Profile'); ?>
                </button>
                <div class="btn-group staff-advanced-actions" data-uid=<%= uid %>>
                    <button type="button" class="btn btn-danger dropdown-toggle mb5" onclick="extraActions(this)"
                            id="extra-<%= uid %>"
                            data-toggle="dropdown">
                        <span class="glyphicon glyphicon-asterisk"></span>
                        <?php echo Yii::t('ivyer', 'Advanced'); ?> <span class="caret"></span>
                    </button>
                </div>

                <p><%= pubCn %></p>

                <p><%= pubEn %></p>
            </div>
        </div>
    </li>
</script>

<div type="text/template" id="staff-extra-actions-template" style="display: none">
    <ul class="dropdown-menu" role="menu">
        <li><a href="javascript:" onclick="editQuit(this)"><?php echo Yii::t('ivyer', 'Set Resignment'); ?></a></li>
        <li><a href="javascript:" onclick="editAccount(this)"><?php echo Yii::t('ivyer', 'Edit Account'); ?></a></li>
        <li><a href="javascript:" onclick="modifyEmailPassword(this)"><?php echo Yii::t('ivyer', 'Reset Email Password'); ?></a></li>
    </ul>
</div>

<script type="text/template" id="staff-profile-template">
    <input type="hidden" name="staffid" id="staffid" value="<%= staffid%>">
    <div class="form-group">
        <label class="col-sm-2 control-label"><?php echo Yii::t('ivyer', 'Biography in Chinese'); ?></label>

        <div class="col-sm-10">
            <textarea class="form-control" rows="8" name="InfopubStaffExtend[intro_cn]"><%= cn%></textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label"><?php echo Yii::t('ivyer', 'Biography in English'); ?></label>

        <div class="col-sm-10">
            <textarea class="form-control" rows="8" name="InfopubStaffExtend[intro_en]"><%= en%></textarea>
        </div>
    </div>
</script>

<script type="text/template" id="staff-resign-hint">
    <p class="text-danger">
        <span class="glyphicon glyphicon-info-sign"></span>
        <?php echo sprintf(Yii::t('ivyer', 'Resign data: %s. Account deactive date: %s'),
            "<%- resign_timestamp %>", "<%- account_close_timestamp %>"
        ); ?></p>
</script>

<script type="text/template" id="staff-edit-hint">
    <?php
    $userLabels = User::model()->attributeLabels();
    $profileLabels = UserProfile::model()->attributeLabels();
    $staffLabels = Staff::model()->attributeLabels();
    ?>
    <input type="hidden" name="id" value="<%= uid%>">
    <div class="form-group">
        <label class="control-label col-xs-3" for="User_email"><?php echo $userLabels['email'] ?></label>

        <div class="col-xs-9">
            <input class="form-control" name="User[email]" id="User_email" value="<%= email%>" disabled>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $userLabels['user_sig'] ?></label>

        <div class="col-xs-9">
            <?php echo CHtml::dropDownList('User[user_sig]', '', User::getSalutation(), array('class' => 'form-control', 'empty' => Yii::t('global', 'Please Select'))) ?>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-3"
               for="UserProfile_first_name"><?php echo $profileLabels['first_name'] ?></label>

        <div class="col-xs-9">
            <input class="form-control" name="UserProfile[first_name]" id="UserProfile_first_name"
                   value="<%= firstname%>">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-3"
               for="UserProfile_last_name"><?php echo $profileLabels['last_name'] ?></label>

        <div class="col-xs-9">
            <input class="form-control" name="UserProfile[last_name]" id="UserProfile_last_name" value="<%= lastname%>">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-3" for="User_name"><?php echo $userLabels['name'] ?></label>

        <div class="col-xs-9">
            <input class="form-control" name="User[name]" id="User_name" value="<%= name%>">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-3" for="mobile_telephone"><?php echo $staffLabels['mobile_telephone'] ?></label>

        <div class="col-xs-9">
            <input class="form-control" name="Staff[mobile_telephone]" id="mobile_telephone" value="<%= mobile_telephone%>">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-3" for="card_id"><?php echo $staffLabels['card_id'] ?></label>

        <div class="col-xs-9">
            <input class="form-control" name="Staff[card_id]" id="card_id" value="<%= card_id%>">
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label" for="Staff_contract_type">* <?php echo $staffLabels['contract_type']?></label>
        <div class="col-xs-9">
            <?php echo CHtml::dropDownList('Staff[contract_type]', 'full_time', Staff::contractTypeList(), array('class'=>'form-control', 'empty'=>Yii::t("labels",'Contract Type')))?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $profileLabels['user_gender'] ?></label>

        <div class="col-xs-9">
            <?php echo CHtml::dropDownList('UserProfile[user_gender]', '', OA::getChildGenderList(), array('class' => 'form-control', 'empty' => Yii::t('global', 'Please Select'))) ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $profileLabels['nationality'] ?></label>

        <div class="col-xs-9">
            <?php echo CHtml::dropDownList('UserProfile[nationality]', '', Country::model()->getData(), array('class' => 'form-control', 'empty' => Yii::t('global', 'Please Select'))) ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $profileLabels['occupation_en'] ?></label>

        <div class="col-xs-9">
            <?php
            $str = array('disabled' => 'disabled', 'class' => 'form-control', 'empty' => Yii::t('global', 'Please Select'));
            if (Yii::app()->user->checkAccess('ivystaff_hr') || Yii::app()->user->checkAccess('ivystaff_it')) {
                unset($str['disabled']);
                //$str = array( 'class'=>'form-control', 'empty'=>Yii::t('global', 'Please Select'));
            }

            echo CHtml::dropDownList('UserProfile[occupation_en]', '', DepPosLink::model()->getDepPos($this->branchId), $str);

            ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-3 control-label"><?php echo $staffLabels['dwelling_place'] ?></label>
        <div class="col-xs-9">
            <input class="form-control" name="Staff[dwelling_place]" id="dwelling_place" value="<%= dwelling_place%>">
        </div>
    </div>
</script>

<script>
    var drawStaff = null; //显示员工列表`
    var uploadBaseUrl = '<?php echo Yii::app()->params['OAUploadBaseUrl'];?>'; //员工照片地址
    var staffTemp = _.template($('#staff-li-item-template').html()); //员工模版
    var refreshStats = null; //统计信息
    var stafffilter = null; //过滤员工
    var openUploadModal = null; //上传头像窗口
    var openProfileModal = null; //编辑文字窗口
    var extraActions = null; //点击高级操作
    var editQuit = null; //设置用户离职
    var editAccount = null; //编辑用户帐号信息
    var modifyEmailPassword = null; //修改用户邮箱密码

    var uploader;

    $(function () {
        //员工离职
        var staffQuitPopStarted = false;
        editQuit = function (obj) {
            var uid = $(obj).parents('div.staff-advanced-actions').attr('data-uid');
            if (!_.isEmpty(uid)) {
                $.getJSON('<?php echo $this->createUrl('getResignInfo');?>', {staffid: uid}, function (data) {
                    if (data.state == 'success') {
                        if (!staffQuitPopStarted) {
                            $('#staff-resign-data #StaffResign_resign_timestamp').datepicker({
                                'changeMonth': true,
                                'changeYear': true, 'dateFormat': 'yy-mm-dd'
                            });
                            $('#staff-resign-data #StaffResign_account_close_timestamp').datepicker({
                                'changeMonth': true,
                                'changeYear': true, 'dateFormat': 'yy-mm-dd'
                            });
                        }
                        $('#StaffResignModal #StaffResignRemove').removeAttr('checked');
                        $('#StaffResignModal div.cancel-resign').toggle(!_.isEmpty(data.data.id));
                        $('#staff-resign-data #StaffResign_staff_uid').val(uid);
                        $('#staff-resign-data #StaffResign_id').val(data.data.id);
                        $('#staff-resign-data #StaffResign_resign_timestamp').val(data.data.resign_timestamp);
                        $('#staff-resign-data #StaffResign_account_close_timestamp').val(data.data.account_close_timestamp);
                        $('#staff-resign-data #StaffResign_departure_why').val(data.data.departure_why);
                        $('#staff-resign-data #Staff_contract_type').val(data.data.contract_type);

                        $('#StaffResignModal #StaffResignStaffName').html(userData[selectedBranchId][uid].name);

                        $('#StaffResignModal').modal({backdrop: 'static'});
                    }
                });
            }
        };


        $('#StaffResignModal #StaffResignRemove').click(function () {
            $("#staff-resign-data").toggle(!this.checked);
        });

        //editAccount
        editAccount = function (obj) {
            var uid = $(obj).parents('div.staff-advanced-actions').attr('data-uid');
            $.getJSON('<?php echo $this->createUrl('editAccount')?>', {uid: uid}, function (data) {
                $('#StaffEditModal .modal-body').html(_.template($('#staff-edit-hint').html(), data));
                $('#StaffEditModal .modal-body #UserProfile_user_gender').val(data.gender);
                $('#StaffEditModal .modal-body #UserProfile_nationality').val(data.nationality);
                $('#StaffEditModal .modal-body #UserProfile_occupation_en').val(data.position);
                $('#StaffEditModal .modal-body #Staff_contract_type').val(data.contract_type);
                $('#StaffEditModal .modal-body #User_user_sig').val(data.user_sig);
                $('#StaffEditModal').modal({
                    backdrop: 'static'
                });
            });
        };

        //修改邮箱密码

        modifyEmailPassword = function (obj) {
            var uid = $(obj).parents('div.staff-advanced-actions').attr('data-uid');
            $.getJSON('<?php echo $this->createUrl('editAccount');?>', {uid: uid}, function (data) {
                $('#modifyEmailPasswordModal  #modifyEmailUserName').html(data.name);
                $('#modifyEmailPasswordModal  #modifyEmailUserName').append(' <span>(' + data.firstname + data.lastname + ')</span>');
                $('#modifyEmailPasswordModal .modal-body #modifyEmailUserEmail').val(data.email);
                $('#modifyEmailPasswordModal .modal-body #modifyEmail_uid').val(uid);
                $('#modifyEmailPasswordModal').modal({backdrop: 'static'});
            });
        };
        //点击高级选项
        extraActions = function (obj) {
            if (_.isEmpty($(obj).attr('item-added'))) {
                $(obj).attr('item-added', 1);
                $(obj).parent('div.btn-group').append($('#staff-extra-actions-template').html());
            }
        };

        uploader = new plupload.Uploader({
            runtimes: 'flash,html5,silverlight,html4',
            browse_button: 'select-photo',
            container: document.getElementById('up-container'),
            url: '<?php echo $this->createUrl('upAvatar')?>',
            flash_swf_url: '<?php echo Yii::app()->themeManager->baseUrl.'/base/js/plupload/Moxie.swf'?>',
            silverlight_xap_url: '<?php echo Yii::app()->themeManager->baseUrl.'/base/js/plupload/Moxie.xap'?>',
            multi_selection: false,
//            multipart_params : {staffid: 8011384},
            resize: {
                width: 643,
                height: 363
            },
            filters: {
                max_file_size: '10mb',
                mime_types: [
                    {title: "<?php echo Yii::t('teaching', 'Image files');?>", extensions: "jpg,gif,png,jpeg"}
                ]
            },

            init: {
                QueueChanged: function (up) {
                    // Called when queue is changed by adding or removing files
                    up.start();
                    var html = '<div class="progress">';
                    html += '<div class="progress-bar" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>';
                    html += '</div>';
                    $('#up-container .avatar-wrapper').html(html);
                },
                UploadProgress: function (up, file) {
                    $('#up-container .avatar-wrapper .progress .progress-bar').attr('aria-valuenow', file.percent).css('width', file.percent + '%').html(file.percent + '%');
                },

                FileUploaded: function (up, file, info) {
                    // Called when file has finished uploading
                    if (info.status == 200) {
                        var response = eval('(' + info.response + ')');
                        $('#ext').val(response.ext);
                        var img = $('<img src="' + response.url + '">');
                        $('#up-container .avatar-wrapper').html(img);
                        img.cropper({
                            aspectRatio: 0.75,
                            minWidth: 90,
                            dragCrop: false,
                            preview: $('#up-container .preview'),
                            done: function (data) {
                                var json = [
                                    '{"x":' + data.x,
                                    '"y":' + data.y,
                                    '"height":' + data.height,
                                    '"width":' + data.width + "}"
                                ].join();
                                $('#data').val(json);
                            }
                        });
                    }
                },
                Error: function (up, args) {
                    alert(args.message);
                }
            }
        });
        uploader.init();

        openUploadModal = function (staffId) {
            $('#staffid').val(staffId);
            $('#del_avatar').attr('href', '<?php echo $this->createUrl('delAvatar');?>&staffid=' + staffId);
            $('#up-container .avatar-wrapper').html('');
            $('#StaffPhotoModal').modal({backdrop: 'static'});
            var src = $('.media-list .staff-item[data-staffid="' + staffId + '"] img.img-face').attr('src');
            if (src)
                $('#up-container .preview').html('<img src="' + src + '">');

            uploader.setOption('multipart_params', {staffid: staffId});
        };

        openProfileModal = function (staffId) {
            if (staffId) {
                $.getJSON('<?php echo $this->createUrl('profile');?>', {staffid: staffId}, function (data) {
                    $('#profile-data').html(_.template($('#staff-profile-template').html(), data));
                    $('#StaffProfileModal').modal({backdrop: 'static'});
                });
            }
        };

        refreshStats = function () {
            $('#stats-total-count span').html($('.media-list li').length);
            _.each($('#staff-stats a.stats'), function (dom, i) {
                var _span = $(dom).find('span');
                var _total = $('.media-list li[' + $(dom).attr('uattr') + '|' + $(dom).attr('uattr-value') + ']').length;
                if (_total > 0) {
                    _span.html(_total);
                    $(dom).show();
                } else {
                    $(dom).hide();
                }
            })
        };

        stafffilter = function (obj) {

            $('ul.media-list li.media').removeClass('active');
            if (_.isNull(obj)) {
                obj = $('#stats-total-count');
            } else {
                if ($(obj).hasClass('stats')) {
                    $('.media-list li[' + $(obj).attr('uattr') + '|' + $(obj).attr('uattr-value') + ']').removeClass('muted').addClass('active');
                    $('.media-list li[' + $(obj).attr('uattr') + '!' + $(obj).attr('uattr-value') + ']').removeClass('active').addClass('muted');
                } else {
                    $('.media-list li').removeClass('muted').addClass('active');
                }
            }

            _.each($('.dep-container[dep-id]'), function (dom, i) {
                if ($(dom).find('li.staff-item.active').length == 0) {
                    $(dom).hide();
                } else {
                    $(dom).show();
                }
            });

            $(obj).siblings().removeClass("active");
            $(obj).addClass('active');
        };

        drawStaff = function () {
            $('div.dep-container').hide();
            $('div.dep-container ul').empty();
            $('div.dep-container h3 span.badge').empty();
            _.each(userArray, function (_u) {
                u = userData[selectedBranchId][_u.uid];
                var depContainer = $('div.dep-container[dep-id|="' + depPosLinks[u.positionId]['depId'] + '"]');
                var _staffView = staffTemp(u);
                if (depPosLinks[u.positionId]['isLead'] > 0)
                    depContainer.find('ul').prepend(_staffView);
                else
                    depContainer.find('ul').append(_staffView);
                depContainer.show();
            });

            _.each($('div.dep-container[dep-id]'), function (obj, i) {
                if ($(obj).is(":visible")) {
                    var children = $(obj).find('ul.media-list li');
                    $(obj).find('h3 span.badge').html(children.length);
                }
            });

            refreshStats();

            $('#campus-list a').removeClass('active');
            $('#campus-list a[branchid|="' + selectedBranchId + '"]').addClass('active');

        };

        drawStaff();

        $('.dep-container h3.dep-item').on('click', function (e) {
            var children = $(this).parent('.dep-container').find(' > ul > li');
            if (children.is(":visible")) {
                children.hide();
                $(this).find('span.glyphicon').addClass('glyphicon-chevron-down').removeClass('glyphicon-chevron-right');
            } else {
                children.show();
                $(this).find('span.glyphicon').addClass('glyphicon-chevron-right').removeClass('glyphicon-chevron-down');
            }
            e.stopPropagation();
        });
    });

    //回调
    function cbPhoto(data) {
        $('.media-list .staff-item[data-staffid="' + data.staffid + '"] img.img-face').attr('src', data.url);
        setTimeout(function () {
            $('#StaffPhotoModal').modal('hide');
        }, 1000);
    }

    function cbProfile(data) {
        userData[data.branchid][data.staffid]['pubCn'] = data.cn;
        userData[data.branchid][data.staffid]['pubEn'] = data.en;
        drawStaff();
        setTimeout(function () {
            $('#StaffProfileModal').modal('hide');
        }, 1000);
    }

    function cbDelAvatar(data) {
        $('.media-list .staff-item[data-staffid="' + data.id + '"] img.img-face').attr('src', uploadBaseUrl + '/infopub/staff/blank.jpg');
        $('#up-container .preview').html('<img src="' + uploadBaseUrl + '/infopub/staff/blank.jpg">');
    }

    function cbResignSaved(data) {
        $('li.staff-item[data-staffid|=' + data.staff_uid + ']').find('p.text-danger').remove();
        if (_.isUndefined(data.deleted)) {
            var hint = _.template($('#staff-resign-hint').html(), data);
            $('li.staff-item[data-staffid|=' + data.staff_uid + ']').children('div.media').prepend(hint);
        }

        setTimeout(function () {
            $("#staff-resign-data").show();
            $('#StaffResignModal').modal('hide');
        }, 500);
    }

    function cbEditAccount(data) {
        userData[data.branchid][data.staffid]['countryId'] = data.countryId;
        userData[data.branchid][data.staffid]['name'] = data.name;
        userData[data.branchid][data.staffid]['positionId'] = data.positionId;
        userData[data.branchid][data.staffid]['positionTitle'] = data.positionTitle;
        drawStaff();
        setTimeout(function () {
            $('#StaffEditModal').modal('hide');
        }, 500);
    }
    function cbModifyEmailPassword(data) {
        setTimeout(function () {
            $('#modifyEmailReset').click();
            $('#modifyEmailPasswordModal').modal('hide');
        }, 1000);
    }


</script>

<style>
    .dep-container h3.dep-item {
        -moz-border-radius: 5px;
        -webkit-border-radius: 5px;
        border: 1px solid #999;
        border-radius: 5px;
        display: inline-block;
        padding: 3px 10px;
        text-decoration: none;
        line-height: 1.5;
        cursor: pointer;
    }

    .dep-container h3.dep-item span.badge {
        font-size: 19px;
    }

    .img-face {
    }

    li.muted {
        display: none;
    }

    .media-list li.staff-item {
        padding-left: 2.5em;
        padding-bottom: 1em;
    }

    .media-list li.staff-item .media {
        padding-left: 2em;
    }

    .avatar-view {
        display: block;
        margin: 15% auto 5%;
        height: 220px;
        width: 220px;
        border: 3px solid #fff;
        border-radius: 5px;
        box-shadow: 0 0 5px rgba(0, 0, 0, .15);
        cursor: pointer;
        overflow: hidden;
    }

    .avatar-view img {
        width: 100%;
    }

    .avatar-body {
        padding-right: 15px;
        padding-left: 15px;
    }

    .avatar-upload {
        overflow: hidden;
    }

    .avatar-upload label {
        display: block;
        float: left;
        clear: left;
        width: 100px;
    }

    .avatar-upload input {
        display: block;
        margin-left: 110px;
    }

    .avater-alert {
        margin-top: 10px;
        margin-bottom: 10px;
    }

    .avatar-wrapper {
        height: 364px;
        width: 100%;
        box-shadow: inset 0 0 5px rgba(0, 0, 0, .25);
        background-color: #fcfcfc;
        overflow: hidden;
        text-align: center;
        line-height: 364px;
    }

    .avatar-wrapper img {
        display: block;
        height: auto;
        max-width: 100%;
    }

    .avatar-preview {
        float: left;
        margin-right: 15px;
        border: 1px solid #eee;
        border-radius: 4px;
        background-color: #fff;
        overflow: hidden;
    }

    .avatar-preview:hover {
        border-color: #ccf;
        box-shadow: 0 0 5px rgba(0, 0, 0, .15);
    }

    .avatar-preview img {
        width: 100%;
    }

    .preview {
        height: 120px;
        width: 90px;
    }

    @media (min-width: 992px) {
        .avatar-preview {
            float: none;
        }
    }

    .progress {
        width: 300px;
        margin: 140px auto;
    }
</style>
