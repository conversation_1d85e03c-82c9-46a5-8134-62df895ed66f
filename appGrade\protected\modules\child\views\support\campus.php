<?php
$this->breadcrumbs=array(
		Yii::t("navigations", 'Support')=>array('/child/support'),
		Yii::t("navigations", 'Contact Campus'),
);
?>

<?php foreach($branches as $branch):?>

<h1>
	<label>
	<?php 
	
	//echo $branch->title;
	echo $branch->info->getLangContent("title");
	
	?>
	</label>
</h1>


<div class="form contact-campus-bg">

	<?php
	$attrs=array("tel","fax","support_email");

	foreach($attrs as $attr):
	?>

	<dl class="row">
		<dt>
			<?php echo addColon($branch->info->getAttributeLabel($attr));?>
		</dt>
		<dd
			class="read <?php if(in_array($attr, array("tel","fax"))) echo "bignumber";?>">
			<?php echo $branch->info->getAttribute($attr);?>
			<?php
			if($attr=="support_email"){
				echo CHtml::openTag("a", array("href"=>$this->createUrl("//child/support/email")));
				echo CHtml::image(Yii::app()->theme->baseUrl."/images/icons/email.png", Yii::t("support","Send Email"));
				echo CHtml::closeTag("a");
			}
			?>
		</dd>
	</dl>

	<?php endforeach;?>
	<dl class="row">
		<dt>
			<?php echo addColon($branch->info->getAttributeLabel("address_en"));?>
		</dt>
		<dd class="read">
			<?php echo $branch->info->getLangContent("address");?>
		</dd>
	</dl>

</div>



<?php endforeach;?>


<div class="clearfix"></div>

<?php if(!Mims::unIvy()):?>
<p class="mt20 alignRight">
	<span class="btn001"><?php echo CHtml::link("<span>".Yii::t("support","View All Campuses")."</span>", array("//child/support/campus", "show"=>"all"));?>
	</span>
</p>
<?php endif;?>

<?php

Yii::import('ext.guide.class.*');
$this->widget('ext.guide.Guide', array(
		'options'=>array(
				'guides'=>array(
						0=>GuideItem::navGuide(10, null, true,'navigations_support', 217),
							
						10 =>GuideItem::subNavGuide(null, null, "subnav_support"),

				)
		)
));
//endif;
?>