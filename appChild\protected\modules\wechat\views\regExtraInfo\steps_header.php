<div class="py-2 bg-primary">
    <div class="container">
        <div class="row no-gutters d-flex align-items-start align-items-center px-3 px-md-0">
            <div class="col-lg-12 d-block">
                <div class="row d-flex">
                    <a href="<?php echo $this->createUrl('index');?>">
                        <div class="col-md pr-4 d-flex topper align-items-center">
                            <div class="icon bg-fifth mr-2 d-flex justify-content-center align-items-center"><span class="fas fa-home"></span></div>
                            <span class="text"><?php echo Yii::t("reg", 'Return to start page'); ?></span>
                        </div>
                    </a>
                    <div class="col-md pr-4 d-flex topper align-items-center">
                        <div class="icon bg-secondary mr-2 d-flex justify-content-center align-items-center"><span class="fas fa-clock"></span></div>
                        <span class="text">
                            <?php echo Yii::t("reg", "Please complete the registration before :date", array(":date"=>Yii::t("reg", date('m/d', $this->regStudent->end_time))) );?>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<nav class="navbar navbar-expand-lg navbar-dark bg-dark ftco_navbar ftco-navbar-light" id="ftco-navbar">
    <div class="row justify-content-center steps">
        <?php
        foreach ($this->steps as $k=>$step):
        if (isset($this->studentSteps[$step])) {
            if ($this->studentSteps[$step] == -1) {
                // 驳回
                $statClass = 'reject';
            } elseif ($this->studentSteps[$step] == 1) {
                // 审核通过
                $statClass = 'approved';
            } else {
                $statClass = 'finished';
            }
        }
        else {
            $statClass = 'todo';
        }
        if($step == $currentStep){
            $statClass = 'current';
        }
        ?>
        <div class="item-step">
            <a href="<?php echo $this->createUrl('step'.$step);?>">
                <div class="step bg-light mr-2 d-flex justify-content-center align-items-center <?php echo $statClass;?>"><?php echo $k+1;?></div>
            </a>
        </div>
        <?php endforeach;?>

    </div>
</nav>
<!-- <script src="http://res.wx.qq.com/open/js/jweixin-1.0.0.js"></script> -->
<script>
    (function($) {
        // scroll
        var scrollWindow = function() {
            $(window).scroll(function(){
                var $w = $(this),
                    st = $w.scrollTop(),
                    navbar = $('.ftco_navbar'),
                    sd = $('.js-scroll-wrap');

                if (st > 150) {
                    if ( !navbar.hasClass('scrolled') ) {
                        navbar.addClass('scrolled');
                    }
                }
                if (st < 150) {
                    if ( navbar.hasClass('scrolled') ) {
                        navbar.removeClass('scrolled sleep');
                    }
                }
                if ( st > 200 ) {
                    if ( !navbar.hasClass('awake') ) {
                        navbar.addClass('awake');
                    }

                    if(sd.length > 0) {
                        sd.addClass('sleep');
                    }
                }
                if ( st < 200 ) {
                    if ( navbar.hasClass('awake') ) {
                        navbar.removeClass('awake');
                        navbar.addClass('sleep');
                    }
                    if(sd.length > 0) {
                        sd.removeClass('sleep');
                    }
                }
            });
        };
        scrollWindow();
    })(jQuery);
     //放大图片
    window.onload=function (){
        funcReadImgInfo()
        // form 提交后禁止disable提交按钮
        $('form').on('submit', function(e){
            e.preventDefault();
            $('.examine').attr('disabled', 'disabled');
            this.submit();
        });
    }
    function funcReadImgInfo(){
    var imgs = [];
        var imgObj = $('.edit-content p img');//这里改成相应的对象
    for(var i=0; i<imgObj.length; i++){
         imgs.push(imgObj.eq(i).attr('src'));
         imgObj.eq(i).click(function(){
              var nowImgurl = $(this).attr('src');
                  WeixinJSBridge.invoke("imagePreview",{
               "urls":imgs,
               "current":nowImgurl
          });
             });
         }
    }

    //监听微信返回
    function pushHistory() { 
        var state = { 
            title: "title", 
            url: "#forward" 
        }; 
        window.history.pushState(state, null, "#forward"); 
    } 
    // 在需要监听的页面执行该方法
    pushHistory();
    window.addEventListener("popstate", function(e) { 
        // 如果监听到返回
        if(typeof cropperBack === "function"){
            cropperBack()
        }
        if($("#mapCon").is(":visible")){
            out()
        }
        if($("#signature-div").is(":visible")){
            var canvas = document.getElementById('signature-pad');
            $('#signature-div').hide();
            clea = canvas.getContext("2d");
            clea.clearRect(0,0,500,500);
        }
        
    }, false); 
</script>

<style>
    div.step {
        width: 24px;
        height: 24px;
        border-radius: 12px;
    }
    .todo{
        background-color: #fff !important;
        color: rgba(0, 0, 0, 0.5);
    }
    .finished{
        background-color: #17a2b8 !important;
        color: #fff;
    }
    .current{
        background-color: #0062cc !important;
        color: #fff;
    }
    .reject{
        background-color: #bd2130 !important;
        color: #fff;
    }
    .approved{
        background-color: #1e7e34 !important;
        color: #fff;
    }
    .ftco-navbar-light.scrolled{
        background: #fafafa !important;
    }
    .ftco-navbar-light{
        background: #fafafa !important;
    }
    .htitle{
        color: #1eaaf1;
        margin-top:0.5rem;
        border-bottom: 1px dotted #999;
    }
    .edit-content img {
        width: 100%;
        -webkit-box-shadow: 0 0.125rem 0rem rgba(0, 0, 0, 0.075) !important;
        box-shadow: 0 0.125rem 0rem rgba(0, 0, 0, 0.075) !important;
    }
    .sub-title{
        color:#1eaaf1; border-bottom: 0.5px solid #1eaaf1
    }
    .sub-title span{
        font-size: 1rem;
    }
    .steps {
        width: 100%;
        flex-direction: row;
        flex-wrap: nowrap;
        justify-content: space-around !important;
        margin-left: 0;
    }
    .item-step{
        /*width: 100px;*/
        flex-shrink: 1;
    }
    .edit-content ul{
        padding-left:17px
    }
    .edit-content ol{
        padding-left:21px
    }
</style>
