<?php

/*
 * To change this template, choose Tools | Templates
 */
class TemporaryCommand extends CConsoleCommand
{
    public function init()
    {
        parent::init();
        Yii::import('common.models.visit.*');
    }


    public function actionRepairLearning()
    {
        /* Yii::import('common.models.learning.*');
         $criteria = new CDbCriteria();
         $criteria->select = "id, childid, lid, status";
         $model = LearningSemester::model()->findAll($criteria);

         $data = array();
         foreach ($model as $val){
             $data[$val->childid][$val->lid][] = $val->id;
         }

         $data = array();
         foreach ($data as $lid){
             foreach ($lid as $item){
                 $data[] =  max($item);
             }
         }

         $criteria = new CDbCriteria();
         $criteria->addNotInCondition('id', $data);//，是NOT IN
         $modesl = LearningSemester::model()->findAll($criteria);
         $i = 1;
         if($modesl) {
             foreach ($modesl as $val) {
                 $val->status = 0;
                 $val->save();
                 echo $i . "\n";
                 $i++;
             }
         }*/

        Yii::import('common.models.learning.LearningSemester');
        $criteria = new CDbCriteria();
        $criteria->select = "id, childid, lid";
        $models = LearningSemester::model()->findAll($criteria);
        $data = array();
        $invalidid = array();
        foreach ($models as $model) {
            $childid = $model->childid;
            $lid = $model->lid;
            if (isset($data[$childid]) && isset($data[$childid][$lid])) {
                if ($model->id > $data[$childid][$lid]) {
                    $invalidid[] = $data[$childid][$lid];
                    $data[$childid][$lid] = $model->id;
                }
            } else {
                $data[$childid][$lid] = $model->id;
            }
        }
        LearningSemester::model()->updateByPk($invalidid, array('status' => 0));
    }

    // 修复SQ2019（校历128）
    public function actionFixShareUfida()
    {
        Yii::import('common.models.invoice.IvyInvoiceShareUfida');
        $month1 = 1577808000;
        $month2 = 1580486400;
        $criteria = new CDbCriteria();
        $criteria->compare('month_stamp', array($month1, $month2));
        $criteria->compare('calendar_id', 128);
        $criteria->compare('schoolid', 'CD_LH');
        $models = IvyInvoiceShareUfida::model()->findAll($criteria);

        $data = array();
        foreach ($models as $model) {
            $data[$model->childid][$model->invoice_id][$model->month_stamp] = $model;
        }
        foreach ($data as $childid => $v1) {
            foreach ($v1 as $invoice_id => $v2) {
                if (isset($v2[$month1]) && isset($v2[$month1])) {
                    if ($v2[$month1] && $v2[$month2]) {
                        $model1 = $v2[$month1];
                        $model2 = $v2[$month2];
                        $amunt1 = $model1->amount;
                        $amunt2 = $model2->amount;
                        if (abs($amunt1) < abs($amunt2)) {
                            $model1->amount = $amunt2;
                            $model2->amount = $amunt1;
                            if (!$model1->save()) {
                                print_r($model1->getErrors());
                                exit;
                            }
                            echo $model1->id . 'success' . "\n\r";
                            if (!$model2->save()) {
                                print_r($model2->getErrors());
                                exit;
                            }
                            echo $model2->id . 'success' . "\n\r";
                        }
                    }
                }
            }
        }
    }

    // 读取 csv，更新media表
    public function actionUpdateMedia($csvName)
    {
        if (!is_file($csvName . '.csv')) {
            echo $csvName . '.csv not a file';
            return;
        }
        $whandle = fopen($csvName . '_fail' . time() . '.csv', 'wb');

        // 查找 2009-2014 期间班级对应起始年关系
        Yii::import('common.models.calendar.CalendarSchool');
        $calendarModels = CalendarSchool::model()->findAllByAttributes(array('startyear' => array(2009, 2010, 2011, 2012, 2013, 2014)));
        $calendarArr = array();
        foreach ($calendarModels as $calendarModel) {
            $calendarArr[$calendarModel->yid] = $calendarModel->startyear;
        }
        unset($calendarModels);
        $yids = array_keys($calendarArr);
        $classModels = IvyClass::model()->findAllByAttributes(array('yid' => $yids));
        $classArr = array();
        foreach ($classModels as $classModel) {
            $classArr[$classModel->classid] = $calendarArr[$classModel->yid];
        }
        unset($classModels);

        $data = $this->getCsv($csvName . '.csv');
        $i = 1;
        foreach ($data as $k => $v) {
            if (!isset($v[0]) || !isset($v[1]) || !isset($v[2]) || !isset($v[3])) {
                continue;
            }
            $schoolid = $v[0];
            $classid = $v[1];
            $weeknum = $v[2];
            $filename = $v[3];
            if (!isset($classArr[$classid])) {
                continue;
            }
            $startyear = $classArr[$classid];
            // 处理相关 media 表数据
            $res = $this->processMedia($schoolid, $classid, $weeknum, $filename, $startyear);
            if ($res == 'success') {
                echo $i . '_' . $schoolid . $startyear . $classid . $weeknum . $filename . "--success\n";
            } else {
                echo $i . '_' . $schoolid . $startyear . $classid . $weeknum . $filename . "--{$res}\n";
                fwrite($whandle, $schoolid . ',' . $classid . ',' . $weeknum . ',' . $filename . ',' . $res . ',' . "\n");
            }
            $i++;
        }

        fclose($whandle);
    }

    public function getCsv($fname)
    {
        $handle = fopen($fname, 'rb');
        while (feof($handle) === false) {
            yield fgetcsv($handle);
        }
        fclose($handle);
    }

    public function processMedia($schoolid, $classid, $weeknum, $filename, $startyear)
    {
        Yii::import('common.models.portfolio.ChildMedia');

        $tableName = 'ivy_child_media_' . $startyear;
        $row = Yii::app()->db->createCommand()
            ->select('id, schoolid, classid, yid, weeknum, tag, type, filename, server')
            ->from($tableName)
            ->where(
                'schoolid=:schoolid and classid=:classid and weeknum=:weeknum and filename=:filename and type=:type and server=:server',
                array(':schoolid' => $schoolid, ':classid' => $classid, ':weeknum' => $weeknum, ':filename' => $filename, ':type' => 'photo', ':server' => 1)
            )
            ->queryRow();
        if (!$row) {
            return 'notfound';
        }
        // 更新数据
        $id = $row['id'];
        $newname = "o/{$schoolid}/{$classid}/{$weeknum}/{$filename}";

        $update = Yii::app()->db->createCommand()->update(
            $tableName,
            array('filename' => $newname, 'server' => 20),
            'id=:id',
            array(':id' => $id)
        );

        if (!$update) {
            return 'savefail';
        }
        return 'success';
    }

    public function actionUpdateVideo($logName)
    {
        if (!is_file($logName . '.log')) {
            echo $logName . '.log not a file';
            return;
        }
        $whandle = fopen($logName . '_fail' . time() . '.log', 'wb');

        // 查找 2009-2014 期间班级对应起始年关系
        Yii::import('common.models.calendar.CalendarSchool');
        $calendarModels = CalendarSchool::model()->findAllByAttributes(array('startyear' => array(2009, 2010, 2011, 2012, 2013, 2014)));
        $calendarArr = array();
        foreach ($calendarModels as $calendarModel) {
            $calendarArr[$calendarModel->yid] = $calendarModel->startyear;
        }
        unset($calendarModels);
        $yids = array_keys($calendarArr);
        $classModels = IvyClass::model()->findAllByAttributes(array('yid' => $yids));
        $classArr = array();
        foreach ($classModels as $classModel) {
            $classArr[$classModel->classid] = $calendarArr[$classModel->yid];
        }
        unset($classModels);

        $data = $this->getLog($logName . '.log');
        $i = 1;
        foreach ($data as $v) {
            if (!$v) {
                continue;
            }
            $newname = trim($v, 'Success: ');
            $newname = trim($newname, "\r\n");
            $arr = explode('/', $newname);
            $schoolid = trim($arr[1], 's_');
            $classid = trim($arr[2], 'c_');
            $weeknum = trim($arr[3], 'w_');
            $filename = trim($arr[4], "\r\n");
            $filename = rtrim($filename, ".mp4");

            if (!isset($classArr[$classid])) {
                continue;
            }
            $startyear = $classArr[$classid];
            // 处理相关 media 表数据
            $res = $this->processVideo($schoolid, $classid, $weeknum, $filename, $startyear, $newname);
            if ($res == 'success') {
                echo $i . '-' . $schoolid . $startyear . $classid . $weeknum . $filename . "--success\n";
            } else {
                echo $i . '-' . $schoolid . $startyear . $classid . $weeknum . $filename . "--{$res}\n";
                fwrite($whandle, $v);
            }
            $i++;
        }

        fclose($whandle);
    }

    public function getLog($fname)
    {
        $handle = fopen($fname, 'rb');
        while (feof($handle) === false) {
            yield fgets($handle);
        }
        fclose($handle);
    }

    public function processVideo($schoolid, $classid, $weeknum, $filename, $startyear, $newname)
    {
        Yii::import('common.models.portfolio.ChildMedia');

        $tableName = 'ivy_child_media_' . $startyear;
        $row = Yii::app()->db->createCommand()
            ->select('id, schoolid, classid, yid, weeknum, tag, type, filename, server')
            ->from($tableName)
            ->where(
                'schoolid=:schoolid and classid=:classid and weeknum=:weeknum and filename=:filename and type=:type and server=:server',
                array(':schoolid' => $schoolid, ':classid' => $classid, ':weeknum' => $weeknum, ':filename' => $filename, ':type' => 'video', ':server' => 1)
            )
            ->queryRow();
        if (!$row) {
            return 'notfound';
        }
        // 更新数据
        $id = $row['id'];

        $update = Yii::app()->db->createCommand()->update(
            $tableName,
            array('filename' => $newname, 'server' => 20),
            'id=:id',
            array(':id' => $id)
        );

        if (!$update) {
            return 'savefail';
        }
        return 'success';
    }

    // 导出指定月份应退金额
    public function actionExportRefund($feetype, $month, $branch = 'all')
    {
        // error_reporting(E_ALL);
        $starttime = strtotime($month . '01');
        if (!$starttime) {
            echo 'error month';
            return;
        }
        $endtime = strtotime("next month", $starttime) - 1;

        // 查找有效的学校列表
        $crit = new CDbCriteria();
        $crit->compare('type', 20);
        $crit->compare('status', 10);
        if ($branch != 'all') {
            $crit->compare('branchid', $branch);
        }
        $crit->select = 'branchid';
        $schoolList = Branch::model()->findAll($crit);


        Yii::import('common.models.invoice.*');
        Yii::import('common.models.calendar.CalendarSchoolDays');
        foreach ($schoolList as $item) {
            $schoolid = $item->branchid;
            $exceptInvoiceIds = array();
            $exceptChilds = array();
            // 查找工作流退费记录
            $crit = new CDbCriteria();
            $crit->compare('schoolid', $schoolid);
            $crit->compare('payment_type', $feetype);
            $crit->compare('status', array(InvoiceChildRefund::STATS_COMPLETED, InvoiceChildRefund::STATS_AWAITING));
            $crit->addCondition('(t.startdate <=' . $starttime . ' and t.enddate >=' . $starttime . ') or (t.startdate <=' . $endtime . ' and t.enddate >=' . $endtime . ') or (t.startdate >' . $starttime . ' and t.enddate <' . $endtime . ')');

            $crit->select = 'schoolid, payment_type, startdate, enddate, status, on_invoice_id,childid';
            $crit->index = 'on_invoice_id';
            $refundModles = InvoiceChildRefund::model()->findAll($crit);
            $exceptInvoiceIds = array_keys($refundModles);
            // 查找每日餐费退费记录
            if ($feetype == 'lunch') {
                $crit = new CDbCriteria();
                $crit->compare('schoolid', $schoolid);
                $crit->compare('target_timestamp', '>=' . $starttime);
                $crit->compare('target_timestamp', '<' . $endtime);
                $crit->compare('operator_uid', '>0');
                $crit->select = 'schoolid, target_timestamp, operator_uid, childid';
                $crit->index = 'childid';
                $lunchModels = RefundLunch::model()->findAll($crit);
                $exceptChilds += array_keys($lunchModels);
            }
            // 查找已付账单列表
            $crit = new CDbCriteria();
            $crit->compare('schoolid', $schoolid);
            $crit->compare('payment_type', $feetype);
            $crit->compare('status', Invoice::STATS_PAID);
            $crit->compare('`inout`', 'in');
            $crit->addCondition('(t.startdate <=' . $starttime . ' and t.enddate >=' . $starttime . ') or (t.startdate <=' . $endtime . ' and t.enddate >=' . $endtime . ') or (t.startdate >' . $starttime . ' and t.enddate <' . $endtime . ')');
            $crit->addNotInCondition('childid', $exceptChilds);
            $crit->addNotInCondition('invoice_id', $exceptInvoiceIds);
            $crit->select = 'schoolid, startdate, enddate, childid, status, invoice_id, original_amount, `inout`, calendar_id, invoice_id';
            $invoiceModels = Invoice::model()->findAll($crit);
            $data = array();
            foreach ($invoiceModels as $invoiceModel) {
                // 计算当月实际缴费amount
                // 如果是月付
                if ($invoiceModel->startdate >= $starttime && $invoiceModel->enddate <= $endtime) {
                    $amount = $invoiceModel->original_amount;
                } else {
                    $calendarId = $invoiceModel->calendar_id;
                    $actualStart = ($starttime >= $invoiceModel->startdate) ? $starttime : $invoiceModel->startdate;
                    $actualEnd = ($endtime >= $invoiceModel->enddate) ? $invoiceModel->enddate : $endtime;
                    $shoolDays = CalendarSchoolDays::model()->countCalendarSchoolday($calendarId, $actualStart, $actualEnd, $actualStart);
                    if (isset($shoolDays['totalActualSchoolday'])) {
                        if ($feetype == 'lunch') {
                            $amount = $this->getLunchFee('2019', $schoolid, $invoiceModel->invoice_id, $shoolDays['totalActualSchoolday']);
                        } elseif ($feetype == 'tuition') {
                            $amount = $this->getTuitionFee('2019', $schoolid, $invoiceModel->startdate, $invoiceModel->enddate, $month, $invoiceModel->original_amount);
                        } else {
                            continue;
                        }
                    }
                }

                if (isset($data[$schoolid][$invoiceModel->childid])) {
                    $data[$invoiceModel->childid] += $amount;
                } else {
                    $data[$invoiceModel->childid] = $amount;
                }
            }
            // 生成csv
            $whandle = fopen($schoolid . '_' . $feetype . '_' . $month . '.csv', 'wb');
            foreach ($data as $chilid => $amount) {
                fwrite($whandle, $chilid . ',' . $amount . "\n");
            }
            fclose($whandle);
        }
    }

    public function getLunchFee($startYear, $schoolid, $invoice_id, $days)
    {
        Yii::import('application.components.policy.*');
        $ivyPolicy = new IvyPolicy('pay', $startYear, $schoolid);
        $amount = 0;
        Yii::import('common.models.invoice.InvoiceLunchUnit');
        $lunchUnitModel = InvoiceLunchUnit::model()->findByAttributes(array('invoice_id' => $invoice_id));
        if ($lunchUnitModel) {
            $amount = $lunchUnitModel->price;
        } elseif (isset($ivyPolicy->configs['LUNCH_PERDAY'])) {
            if (is_array($ivyPolicy->configs['LUNCH_PERDAY'])) {
                if (isset($ivyPolicy->configs['LUNCH_PERDAY_OLD'])) {
                    $amount = $ivyPolicy->configs['LUNCH_PERDAY_OLD'];
                }
            } else {
                $amount = $ivyPolicy->configs['LUNCH_PERDAY'];
            }
        }
        if (!isset($ivyPolicy->configs['LUNCH_PERMONTH'])) {
            $amount = $amount * $days;
        }
        return $amount;
    }

    public function getTuitionFee($startYear, $schoolid, $start, $end, $month, $total)
    {
        Yii::import('application.components.policy.*');
        $ivyPolicy = new IvyPolicy('pay', $startYear, $schoolid);
        $amount = 0;
        if (isset($ivyPolicy->configs['TUITION_CALCULATION_BASE']['BY_MONTH']['MONTH_FACTOR'])) {
            $month_factor = $ivyPolicy->configs['TUITION_CALCULATION_BASE']['BY_MONTH']['MONTH_FACTOR'];
            $weight = 0;
            $totalWeight = 0;
            foreach ($month_factor as $k => $v) {
                if ($k >= date('Ym', $start) && $k <= date('Ym', $end)) {
                    $totalWeight += $v;
                    if ($k == $month) {
                        $weight = $v;
                    }
                }
            }
            $amount = $total * ($weight / $totalWeight);
        }
        return round($amount);
    }

    // 更新用户手机号
    public function actionUpdateStaffMphone()
    {
        Yii::import('common.models.staff.Staff');
        $data = $this->getCsv('user.csv');
        $i = 1;
        $updated = 0;
        foreach ($data as $v) {
            if (!isset($v[0]) || !isset($v[1])) {
                continue;
            }
            $email = strtolower($v[0]);
            $mphone = $v[1];
            $userModel = User::model()->findByAttributes(array('email' => $email));
            if (!$userModel) {
                echo $email . "--not found\n";
                continue;
            }
            $staffModel = Staff::model()->findByPk($userModel->uid);
            // 已有手机号不更新
            if ($staffModel->mobile_telephone) {
                echo $email . "--had mphone\n";
                continue;
            }
            $staffModel->mobile_telephone = $mphone;
            $staffModel->save();
            // 更新手机号
            echo $email . "--updated\n";
            $updated++;
            $i++;
        }
        echo $updated .  "--success\n";
    }

    // 批量使用个人账户到学费账单
    public function actionUseCredit($schoolid, $csvname)
    {
        // 根据学校ID初始化 policy
        Yii::import('common.models.invoice.*');
        Yii::import('application.components.policy.PolicyApi');
        Yii::import('common.components.RC');

        $policyApi = new PolicyApi($schoolid);
        // 错误列表
        $whandle = fopen($csvname . '_fail' . time() . '.csv', 'wb');
        // 循环读取csv
        $csv = $csvname . '.csv';
        $data = $this->getCsv($csv);
        foreach ($data as $v) {
            $childid = trim($v[0]);
            $credit = trim($v[1]);
            if (!$childid || !$credit) {
                continue;
            }

            $childModel = ChildProfileBasic::model()->findByPk($childid);

            if (!$childModel) {
                $childid . 'not found' . "\r\n";
                fwrite($whandle, $childid . ',' . $credit . ',' . 'child not found' . "\n");
                continue;
            }
            // 对比系统余额
            if (abs($credit - $childModel->credit) > 0.001) {
                fwrite($whandle, $childid . ',' . $credit . ',' . 'credit not equal' . "\n");
                continue;
            }
            // 查找学费账单
            $yid = 135; // 2020-2021 年启明星校历ID
            $crit = new CDbCriteria();
            $crit->compare('childid', $childid);
            $crit->compare('calendar_id', $yid);
            $crit->compare('payment_type', 'tuition');
            $crit->compare('status', array(Invoice::STATS_UNPAID, Invoice::STATS_PARTIALLY_PAID));
            $invoiceModel = Invoice::model()->find($crit);
            if (!$invoiceModel) {
                fwrite($whandle, $childid . ',' . $credit . ',' . 'invoice not found' . "\n");
                continue;
            }

            // 使用余额
            $ret = $policyApi->Pay(array($invoiceModel), InvoiceTransaction::TYPE_USERCREDIT, $credit, 1);

            if ($ret == 0) {
                echo "$childid success \r\n";
            } else {
                fwrite($whandle, $childid.','.$credit.','.$ret."\n" );
                continue;
            }
        }
        fclose($whandle);
    }

    // 根据excel生成去掉预交学费、个人账户余额的账单
    public function actionGenInvoice($csvname)
    {
        Yii::import('common.models.invoice.*');
        $whandle = fopen($csvname . '_fail' . time() . '.csv', 'wb');
        $whandle2 = fopen($csvname . '_success' . time() . '.csv', 'wb');
        // 循环读取csv
        $csv = $csvname . '.csv';
        $data = $this->getCsv($csv);
        // 2020 年校历
        $yid = 135;
        foreach ($data as $row) {
            $failCsv = implode(',', $row);

            $schoolid = trim($row[0]);
            $childid = trim($row[1]);
            $totalFee = trim($row[2]);
            $deposit = trim($row[3]);
            $credit = trim($row[4]);
            $actualFee = trim($row[5]);
            // 验证金额
            // if (abs($totalFee - $deposit - $credit - $actualFee) > 0.01) {
            //     fwrite($whandle, $failCsv."\n" );
            //     echo $childid . " fee not equal \r\n";
            //     continue;
            // }
            $childObj = ChildProfileBasic::model()->findByPk($childid);
            if (!$childObj) {
                fwrite($whandle, $failCsv."\n" );
                echo $childid . " not found \r\n";
                continue;
            }
            // 查找已开未支付的学费账单
            $invoiceModel = Invoice::model()->findByAttributes(array('childid' => $childid, 'calendar_id' => $yid, 'schoolid' => $schoolid, 'status' => array(10, 30)));
            if (!$invoiceModel) {
                fwrite($whandle, $failCsv."\n" );
                echo $childid . " not invoice \r\n";
                continue;
            }
            if ($deposit > 0) {
                // 查找年付账单使用的预交续费
                $useDeposit = TemporaryDeposit::model()->getDeposit($invoiceModel->invoice_id);
                // 对比预交学费余额
                if (abs($deposit - $useDeposit->amount) > 0.001) {
                    fwrite($whandle, $failCsv."\n" );
                    echo $childid . " deposit not equal \r\n";
                    continue;
                }
            }

            if ($credit > 0) {
                // 查找年付账单使用的个人余额
                $crit = new CDbCriteria();
                $crit->compare('childid', $childid);
                $crit->compare('schoolid', $schoolid);
                $crit->compare('yid', $yid);
                $crit->compare('invoice_id', $invoiceModel->invoice_id);
                $crit->compare('`inout`', 'out');
                $crit->select = 'amount';
                $creditModel = ChildCredit::model()->find($crit);
                if (!$creditModel) {
                    fwrite($whandle, $failCsv."\n" );
                    echo $childid . " credit not found \r\n";
                    continue;
                }
                // 对比个人余额
                if (abs($credit - $creditModel->amount) > 0.001) {
                    fwrite($whandle, $failCsv."\n" );
                    echo $childid . " credit not equal \r\n";
                    continue;
                }
            }

            // // 对比个人账户余额
            // if (abs($childObj->credit - $credit) > 0.001) {
            //     fwrite($whandle, $failCsv."\n" );
            //     echo $childid . "credit not equal \r\n";
            //     continue;
            // }
            // 获取当前学校的预交学费余额
            // $cDeposit = DepositHistory::model()->getChildDepositBalance($childid, $yid);
            // // 对比预交学费余额
            // if (abs($deposit - $cDeposit) > 0.001) {
            //     fwrite($whandle, $failCsv."\n" );
            //     echo $childid . " deposit not equal \r\n";
            //     continue;
            // }
            // 查找下学年的班级
            $classid = $invoiceModel->classid;
            // 生成账单
            $invoiceAttr = array(
                'calendar_id' => $yid,
                'amount' => $actualFee,
                'original_amount' => $actualFee,
                'schoolid' => $schoolid,
                'childid' => $childid,
                'classid' => $classid,
                'payment_type' => 'tuition',
                'inout' => Invoice::INVOICE_INOUT_IN,
                'startdate' => 1598889600,
                'enddate' => 1611590400,
                'fee_type' => 2,
                'fee_program' => 'FULL5D',
                'duetime' => time()+7*86400,
                'title' => '2020-2021 第一学期 学费',
                'discount_id' =>  0,
                'userid' => 1,
                'timestamp' => time(),
                'status' => Invoice::STATS_UNPAID,
                'child_service_info' => 'a:5:{s:3:"mon";i:20;s:3:"tue";i:20;s:3:"wed";i:20;s:3:"thu";i:20;s:3:"fri";i:20;}',
            );
            $model = new Invoice();
            $model->setAttributes($invoiceAttr);
            if(!$model->save()){
                fwrite($whandle, $failCsv."\n" );
                echo $childid . " save fail \r\n";
                continue;
            }
            $row[] = $model->invoice_id;
            $successCsv = implode(',', $row);
            fwrite($whandle2, $successCsv."\n" );
            echo $childid . " success \r\n";
        }
        fclose($whandle);
        fclose($whandle2);
    }

    public function actionProcessChildAvatar($fileName)
    {
        if (!is_file($fileName)) {
            echo 'file not found';
            Yii::app()->end();
        }
        $data = $this->getCsv($fileName);
        $i = 0;
        $success = 0;
        $failed = 0;
        $processed = 0;
        foreach ($data as $v) {
            if ($i == 0) {
                $i++;
                continue;
            }
            $childId = $v[0];
            $newPhoto = $v[2];
            if ($childId) {
                echo "childid: $childId, ";
                $childModel = ChildProfileBasic::model()->findByPk($childId);
                if (!$childModel) {
                    echo "fail: child not found\r\n";
                    $failed++;
                    continue;
                }
                if ($childModel->photo == $newPhoto) {
                    echo "processed\r\n";
                    $processed++;
                    continue;
                }
                if ($childModel->photo != 'blank.gif') {
                    // 旧头像存档
                    $data = array(
                        'school_id' => $childModel->schoolid,
                        'class_id' => $childModel->classid,
                        'child_id' => $childModel->childid,
                        'photo' => $childModel->photo,
                        'updated_by' => 1,
                        'created_by' => 1,
                    );
                    $res = CommonUtils::requestDsOnline('child/childPhotoRepo', $data, 'post', 1);
                    if ($res['code'] != 0) {
                        $msg = $res['msg'];
                        echo "fail: $msg\r\n";
                        continue;
                    }
                }
                $childModel->photo = $v[2];
                $childModel->save();
                echo "success \r\n";
                $success++;
            }
        }
        echo "success: $success;processed: $processed;failed: $failed";
    }

    /**
     * 批量提现
     * @param string $csv
     */
    public function actionWithdrawalBat($csv='')
    {
        if (file_exists($csv)) {
            Yii::import('common.models.invoice.ChildCredit');
            Yii::import('application.components.policy.PolicyApi');
            $items = file($csv);
            foreach ($items as $item) {
                $dd = explode(',', $item);
                if (PolicyApi::childOutCash($dd[0], floatval($dd[1]))) {
                    echo $dd[0].": ok\n";
                }
                else {
                    echo $dd[0].": no\n";
                }
            }
        }
    }

    public function actionServiceInfo($del=0)
    {
        Yii::import('common.models.invoice.*');

        $sql = "SELECT invoice_id,payment_type,startdate,enddate,COUNT(*) as c FROM `ivy_child_service_info` GROUP BY invoice_id,payment_type,startdate,enddate HAVING c>1";
        $rows = Yii::app()->db->createCommand($sql)->queryAll();
        foreach ($rows as $row) {
            echo $row['invoice_id']."\n";
            $criteria = new CDbCriteria();
            $criteria->compare('invoice_id', $row['invoice_id']);
            $criteria->compare('payment_type', $row['payment_type']);
            $criteria->compare('startdate', $row['startdate']);
            $criteria->compare('enddate', $row['enddate']);
            $items = ChildServiceInfo::model()->findAll($criteria);
            foreach ($items as $item) {
                 if (InvoiceTransaction::model()->findByPk($item->transaction_id) != null) {
                     echo $item->transaction_id."-OK\n";
                 }
                 else {
                     echo $item->transaction_id."-NO\n";
                     if ($del)
                        $item->delete();
                 }
            }
            echo "---------------------------------\n";
        }
    }
}
