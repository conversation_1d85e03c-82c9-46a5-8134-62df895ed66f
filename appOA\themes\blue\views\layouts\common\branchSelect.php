<div class="container-fluid">

    <p class="text-center p20">
        <a style="" id="branch-selector" class="btn btn-primary">
            <?php echo Yii::t('campus','Select a Campus');?>
        </a>
    </p>
    <div id="branch-list" class="hidden">
        <ul class="nav nav-pills">
            <?php
            $allbranches = $this->getAllBranch();

            foreach($this->accessBranch as $_branch):
                if(isset($allbranches[$_branch])):
                    if ($this->branchSelectParams['hideKG']):
                        if ($allbranches[$_branch]['type'] == 50 || $_branch == 'BJ_QFF'):
                        $text = '<span class="glyphicon glyphicon-tags program'.$allbranches[$_branch]['group'].'"></span> '.$allbranches[$_branch]['title'];
                        ?>
                        <li class="branchItem">
                            <?php
                            if(isset($this->branchSelectParams['branchCount'][$_branch]) && $this->branchSelectParams['branchCount'][$_branch] && in_array($_branch, array_keys($this->branchSelectParams['branchCount']))){
                                $text .= ' <span class="badge">'.$this->branchSelectParams['branchCount'][$_branch].'</span>';
                            }
                            ?>
                            <?php echo CHtml::link($text, array_merge($this->branchSelectParams['urlArray'], array($this->branchSelectParams['keyId']=>$_branch)));?>
                        </li>
                    <?php endif;
                    else:
                        if(!$this->branchSelectParams['hideOffice'] || $allbranches[$_branch]['type'] != 10):
                            $text = '<span class="glyphicon glyphicon-tags program'.$allbranches[$_branch]['group'].'"></span> '.$allbranches[$_branch]['title'];
                            ?>
                            <li class="branchItem">
                                <?php
                                if(isset($this->branchSelectParams['branchCount'][$_branch]) && $this->branchSelectParams['branchCount'][$_branch] && in_array($_branch, array_keys($this->branchSelectParams['branchCount']))){
                                    $text .= ' <span class="badge">'.$this->branchSelectParams['branchCount'][$_branch].'</span>';
                                }
                                ?>
                                <?php echo CHtml::link($text, array_merge($this->branchSelectParams['urlArray'], array($this->branchSelectParams['keyId']=>$_branch)));?>
                            </li>
                        <?php
                        endif;
                    endif;
                endif;
            endforeach;

            ?>
        </ul>
    </div>


</div>
<style>
    .popover{max-width: 50%}
    .branchItem{height: 37px;width: 170px;}
</style>
<script>
    $(function() {
        var _opts = {placement:'bottom',html:'true',content:$('#branch-list').html()};
        $('a#branch-selector').popover(_opts);
        $('a#branch-selector').popover('show');
    });
</script>