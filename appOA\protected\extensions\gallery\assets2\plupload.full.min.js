(function(){var f=0,c=[],g={},a={},h;function e(){this.returnValue=false}function b(){this.cancelBubble=true}(function(j){var k=j.split(/,/),l,n,m;for(l=0;l<k.length;l+=2){m=k[l+1].split(/ /);for(n=0;n<m.length;n++){a[m[n]]=k[l]}}})("application/msword,doc dot,application/pdf,pdf,application/pgp-signature,pgp,application/postscript,ps ai eps,application/rtf,rtf,application/vnd.ms-excel,xls xlb,application/vnd.ms-powerpoint,ppt pps pot,application/zip,zip,application/x-shockwave-flash,swf swfl,application/vnd.openxmlformats,docx pptx xlsx,audio/mpeg,mpga mpega mp2 mp3,audio/x-wav,wav,image/bmp,bmp,image/gif,gif,image/jpeg,jpeg jpg jpe,image/png,png,image/svg+xml,svg svgz,image/tiff,tiff tif,text/html,htm html xhtml,text/rtf,rtf,video/mpeg,mpeg mpg mpe,video/quicktime,qt mov,video/x-flv,flv,video/vnd.rn-realvideo,rv,text/plain,asc txt text diff log,application/octet-stream,exe");var d={STOPPED:1,STARTED:2,QUEUED:1,UPLOADING:2,FAILED:4,DONE:5,mimeTypes:a,extend:function(m){var k,l,j;for(j=1;j<arguments.length;j++){l=arguments[j];for(k in l){m[k]=l[k]}}return m},cleanName:function(j){var k,l;l=[/[\300-\306]/g,"A",/[\340-\346]/g,"a",/\307/g,"C",/\347/g,"c",/[\310-\313]/g,"E",/[\350-\353]/g,"e",/[\314-\317]/g,"I",/[\354-\357]/g,"i",/\321/g,"N",/\361/g,"n",/[\322-\330]/g,"O",/[\362-\370]/g,"o",/[\331-\334]/g,"U",/[\371-\374]/g,"u"];for(k=0;k<l.length;k+=2){j=j.replace(l[k],l[k+1])}j=j.replace(/\s+/g,"_");j=j.replace(/[^a-z0-9_\-\.]+/gi,"");return j},addRuntime:function(i,j){j.name=i;c[i]=j;c.push(j);return j},guid:function(){var j=new Date().getTime().toString(32),k;for(k=0;k<5;k++){j+=Math.floor(Math.random()*65535+f).toString(32)}return j},formatSize:function(i){if(i>1048576){return Math.round(i/1048576,1)+" MB"}if(i>1024){return Math.round(i/1024,1)+" KB"}return i+" b"},getPos:function(l,j){var i=0,m=0,k;l=l;j=j||document.body;k=l;while(k&&k!=j&&k.nodeType){i+=k.offsetLeft||0;m+=k.offsetTop||0;k=k.offsetParent}k=l.parentNode;while(k&&k!=j&&k.nodeType){i-=k.scrollLeft||0;m-=k.scrollTop||0;k=k.parentNode}return{x:i,y:m}},parseSize:function(i){var j;if(typeof(i)=="string"){i=/^([0-9]+)([mgk]+)$/.exec(i.toLowerCase().replace(/[^0-9mkg]/g,""));j=i[2];i=parseInt(i[1]);if(j=="g"){i*=1073741824}if(j=="m"){i*=1048576}if(j=="k"){i*=1024}}return i},xmlEncode:function(k){var j={"<":"lt",">":"gt","&":"amp",'"':"quot","'":"#39"},i=/[<>&\"\']/g;return(this.xmlEncode=function(l){return l?(""+l).replace(i,function(m){return j[m]?"&"+j[m]+";":m}):l})(k)},toArray:function(l){var k,j=[];for(k=0;k<l.length;k++){j[k]=l[k]}return j},addI18n:function(i){return plipload.extend(g,i)},translate:function(i){return g[i]||i},addEvent:function(j,i,k){if(j.attachEvent){j.attachEvent("on"+i,function(){var l=window.event;if(!l.target){l.target=l.srcElement}l.preventDefault=e;l.stopPropagation=b;k(l)})}else{if(j.addEventListener){j.addEventListener(i,k,false)}}}};d.Uploader=function(k){var j={},n,m=[],o;n=new d.QueueProgress();k=d.extend({chunk_size:"1mb",max_file_size:"1gb",multi_selection:true,filters:[{title:"Image files",extensions:"jpg,gif,png"}]},k);function l(){var p;if(this.state==d.STARTED&&o<m.length){p=m[o++];if(p.status==d.QUEUED){this.trigger("UploadFile",p)}else{l.call(this)}}else{this.stop()}}function i(){var p;n.reset();for(p=0;p<m.length;p++){n.size+=m[p].size;n.loaded+=m[p].loaded;if(m[p].status==d.DONE){n.uploaded++}else{if(m[p].status==d.FAILED){n.failed++}else{n.queued++}}}n.percent=n.size>0?Math.ceil(n.loaded/n.size*100):0}d.extend(this,{state:d.STOPPED,features:{},files:m,settings:k,total:n,id:d.guid(),init:function(){var u=this,v,r,q,t=0,s;k.page_url=k.page_url||document.location.pathname.replace(/\/[^\/]+$/g,"/");if(!/^(\w+:\/\/|\/)/.test(k.url)){k.url=k.page_url+k.url}k.chunk_size=d.parseSize(k.chunk_size);k.max_file_size=d.parseSize(k.max_file_size);u.bind("FilesAdded",function(w,z){var y,x;for(y=0;y<z.length;y++){x=z[y];x.loaded=0;x.percent=0;if(z[y].size>k.max_file_size){x.status=d.FAILED}else{x.status=d.QUEUED}m.push(x)}u.trigger("QueueChanged");u.refresh()});u.bind("UploadProgress",function(w,x){if(x.status==d.QUEUED){x.status=d.UPLOADING}x.percent=x.size>0?Math.ceil(x.loaded/x.size*100):0;i()});u.bind("QueueChanged",i);u.bind("FileUploaded",function(w,x){x.status=d.DONE;w.trigger("UploadProgress",x);l.call(u)});if(k.runtimes){r=[];s=k.runtimes.split(/,/);for(v=0;v<s.length;v++){r.push(c[s[v]])}}else{r=c}function p(){var w=r[t++];if(w){w.init(u,function(x){if(x&&x.success){u.trigger("Init",{runtime:w.name});u.trigger("PostInit");u.refresh()}else{p()}})}}p()},refresh:function(){this.trigger("Refresh")},start:function(){if(this.state!=d.STARTED){o=0;this.state=d.STARTED;this.trigger("StateChanged");l.call(this)}},stop:function(){if(this.state!=d.STOPPED){this.state=d.STOPPED;this.trigger("StateChanged")}},getFile:function(q){var p;for(p=m.length-1;p>=0;p--){if(m[p].id===q){return m[p]}}},removeFile:function(q){var p;for(p=m.length-1;p>=0;p--){if(m[p].id===q.id){return this.splice(p,1)[0]}}},splice:function(r,p){var q;q=m.splice(r,p);this.trigger("FilesRemoved",q);this.trigger("QueueChanged");return q},trigger:function(q){var s=j[q.toLowerCase()],r,p;if(s){p=Array.prototype.slice.call(arguments);p[0]=this;for(r=0;r<s.length;r++){if(s[r].func.apply(s[r].scope,p)===false){return false}}}return true},bind:function(p,r,q){var s;p=p.toLowerCase();s=j[p]||[];s.push({func:r,scope:q||this});j[p]=s},unbind:function(p,r){var s=j[p.toLowerCase()],q;if(s){for(q=s.length-1;q>=0;q--){if(s[q].func===r){s.splice(q,1)}}}}})};d.File=function(l,j,k){var i=this;i.id=l;i.name=j;i.size=k;i.loaded=0;i.percent=0;i.status=0};d.Runtime=function(){this.init=function(i,j){}};d.QueueProgress=function(){var i=this;i.size=0;i.loaded=0;i.uploaded=0;i.failed=0;i.queued=0;i.percent=0;i.reset=function(){i.size=i.loaded=i.uploaded=i.failed=i.queued=i.percent=0}};d.runtimes={};window.plupload=d})();(function(b){var d={},c=true;function a(g,j,e,l,k){var f,h,i;h=google.gears.factory.create("beta.canvas");h.decode(g);scale=Math.min(j/h.width,e/h.height);if(scale<1){j=Math.round(h.width*scale);e=Math.round(h.height*scale)}else{j=h.width;e=h.height}h.resize(j,e);return h.encode(k,{quality:l/100})}b.runtimes.Gears=b.addRuntime("gears",{init:function(g,i){var h;if(!window.google||!google.gears){return i({success:false})}try{h=google.gears.factory.create("beta.desktop")}catch(e){return i({success:false})}function f(l){var k,j,m=[],n;for(j=0;j<l.length;j++){k=l[j];n=b.guid();d[n]=k.blob;m.push(new b.File(n,k.name,k.blob.length))}g.trigger("FilesAdded",m)}g.bind("PostInit",function(){var k=g.settings,j=document.getElementById(k.drop_element);if(j){b.addEvent(j,"dragover",function(l){l.preventDefault()});b.addEvent(j,"drop",function(m){var l=h.getDragData(m,"application/x-gears-files");if(l){f(l.files)}m.preventDefault()});j=0}b.addEvent(document.getElementById(k.browse_button),"click",function(p){var o=[],m,l,n;p.preventDefault();for(m=0;m<k.filters.length;m++){n=k.filters[m].extensions.split(",");for(l=0;l<n.length;l++){o.push("."+n[l])}}h.openFiles(f,{singleFile:!k.multi_selection,filter:o})})});g.bind("UploadFile",function(j,o){var n=0,q,p,m=0,l=j.settings.resize;p=j.settings.chunk_size;q=Math.ceil(o.size/p);if(l&&/\.(png|jpg|jpeg)$/i.test(o.name)){d[o.id]=a(d[o.id],l.width,l.height,l.quality||90,/\.png$/i.test(o.name)?"image/png":"image/jpeg")}o.size=d[o.id].length;k();function k(){var r=j.settings.url,s,t;if(o.status==b.DONE||o.status==b.FAILED||j.state==b.STOPPED){return}t=Math.min(p,o.size-(n*p));s=google.gears.factory.create("beta.httprequest");s.open("POST",r+(r.indexOf("?")==-1?"?":"&")+"name="+escape(o.target_name||o.name)+"&chunk="+n+"&chunks="+q);s.setRequestHeader("Content-Disposition",'attachment; filename="'+o.name+'"');s.setRequestHeader("Content-Type","application/octet-stream");s.upload.onprogress=function(u){o.loaded=m+u.loaded;j.trigger("UploadProgress",o)};s.onreadystatechange=function(){var u;if(s.readyState==4){if(s.status==200){u={file:o,chunk:n,chunks:q,response:s.responseText};j.trigger("ChunkUploaded",u);if(u.cancelled){o.status=b.FAILED;j.trigger("FileUploaded",o);return}m+=t;if(++n>=q){o.status=b.DONE;j.trigger("FileUploaded",o)}else{k()}}else{j.trigger("UploadChunkError",{file:o,chunk:n,chunks:q,error:"Status: "+s.status})}}};if(n<q){s.send(d[o.id].slice(n*p,t))}}});g.features={dragdrop:c,jpgresize:c,pngresize:c,chunks:c};i({success:c})}})})(plupload);(function(b){var a={};function c(n){var p=false;var d=null;try{var j=null;try{j=new ActiveXObject("AgControl.AgControl");if(n==null){p=true}else{if(j.IsVersionSupported(n)){p=true}}j=null}catch(m){var k=navigator.plugins["Silverlight Plug-In"];if(k){if(n===null){p=true}else{var f=k.description;if(f==="1.0.30226.2"){f="2.0.30226.2"}var g=f.split(".");while(g.length>3){g.pop()}while(g.length<4){g.push(0)}var h=n.split(".");while(h.length>4){h.pop()}var o,i,l=0;do{o=parseInt(h[l]);i=parseInt(g[l]);l++}while(l<h.length&&o===i);if(o<=i&&!isNaN(o)){p=true}}}}}catch(m){p=false}return p}b.silverlight={trigger:function(h,e){var g=a[h],f,d;if(g){d=b.toArray(arguments).slice(1);d[0]="Silverlight:"+e;setTimeout(function(){g.trigger.apply(g,d)},0)}}};b.runtimes.Silverlight=b.addRuntime("silverlight",{init:function(j,k){var h,f="",g=j.settings.filters,e;if(!c("2.0.31005.0")){k({success:false});return}a[j.id]=j;h=document.createElement("div");h.id=j.id+"_silverlight_container";b.extend(h.style,{position:"absolute",top:"0",background:j.settings.silverlight_bgcolor||"transparent",width:"100px",height:"100px",opacity:0.01});h.className="plupload_silverlight";document.body.appendChild(h);for(e=0;e<g.length;e++){f+=(f!=""?"|":"")+g[e].title+" | *."+g[e].extensions.replace(/,/g,";*.")}h.innerHTML='<object id="'+j.id+'_silverlight" data="data:application/x-silverlight," type="application/x-silverlight-2" width="100%" height="100%"><param name="source" value="'+j.settings.silverlight_xap_url+'"/><param name="background" value="Transparent"/><param name="windowless" value="true"/><param name="initParams" value="id='+j.id+",filter="+f+'"/><param name="onerror" value="onSilverlightError" /></object>';function d(){return document.getElementById(j.id+"_silverlight").content.Upload}j.bind("Silverlight:Init",function(){var i,l={};j.bind("Silverlight:StartSelectFiles",function(m){i=[]});j.bind("Silverlight:SelectFile",function(m,p,n,o){var q;q=b.guid();l[q]=p;l[p]=q;i.push(new b.File(q,n,o))});j.bind("Silverlight:SelectSuccessful",function(){j.trigger("FilesAdded",i)});j.bind("Silverlight:UploadFileProgress",function(m,q,n,p){var o=m.getFile(l[q]);o.size=p;o.loaded=n;m.trigger("UploadProgress",o)});j.bind("Refresh",function(m){var n,o;n=document.getElementById(m.settings.browse_button);o=b.getPos(n);b.extend(document.getElementById(m.id+"_silverlight_container").style,{top:o.y+"px",left:o.x+"px",width:n.clientWidth+"px",height:n.clientHeight+"px"})});j.bind("Silverlight:UploadChunkSuccessful",function(m,p,n,s,r){var q,o=m.getFile(l[p]);q={file:o,chunk:n,chunks:s,response:r};m.trigger("ChunkUploaded",q);if(q.cancelled){d().CancelUpload();o.status=b.FAILED;m.trigger("FileUploaded",o);return}});j.bind("Silverlight:UploadSuccessful",function(m,o){var n=m.getFile(l[o]);n.status=b.DONE;m.trigger("FileUploaded",n)});j.bind("FilesRemoved",function(m,o){var n;for(n=0;n<o.length;n++){d().RemoveFile(l[o[n].id])}});j.bind("UploadFile",function(m,p){var o=m.settings.url,n=m.settings.resize||{};o+=(o.indexOf("?")==-1?"?":"&")+"name="+escape(p.target_name||p.name);d().UploadFile(l[p.id],o,m.settings.chunk_size,n.width,n.height,n.quality||90)});k({success:true})})}})})(plupload);(function(c){var a={};function b(){var d;try{d=navigator.plugins["Shockwave Flash"];d=d.description}catch(e){try{d=new ActiveXObject("ShockwaveFlash.ShockwaveFlash").GetVariable("$version")}catch(e){d="0.0"}}d=d.match(/\d+/g);return parseFloat(d[0]+"."+d[1])}c.flash={trigger:function(f,d,e){setTimeout(function(){var j=a[f],h,g;if(j){j.trigger("Flash:"+d,e)}},0)}};c.runtimes.Flash=c.addRuntime("flash",{init:function(f,k){var j,e,g,d,l=0;if(b()<10){k({success:false});return}a[f.id]=f;j=document.getElementById(f.settings.browse_button);e=document.createElement("div");e.id=f.id+"_flash_container";c.extend(e.style,{position:"absolute",top:"0px",background:f.settings.flash_bgcolor||"transparent",width:"100%",height:"100%"});e.className="plupload_flash";document.body.appendChild(e);g="id="+escape(f.id);e.innerHTML='<object id="'+f.id+'_flash" width="100%" height="100%" style="outline:0" type="application/x-shockwave-flash" data="'+f.settings.flash_swf_url+'"><param name="movie" value="'+f.settings.flash_swf_url+'" /><param name="flashvars" value="'+g+'" /><param name="wmode" value="transparent" /><param name="allowscriptaccess" value="always" /></object>';function i(){return document.getElementById(f.id+"_flash")}function h(){if(l++>5000){k({success:false});return}if(!d){setTimeout(h,1)}}h();j=e=null;f.bind("Flash:Init",function(){var p={},n,o=f.settings.filters,m=f.settings.resize||{};d=true;for(n=0;n<o.length;n++){o[n].extensions="*."+o[n].extensions.replace(/,/g,";*.")}i().setFileFilters(o,f.settings.multi_selection);f.bind("UploadFile",function(q,s){var t=q.settings,r=t.url;r+=(r.indexOf("?")==-1?"?":"&")+"name="+escape(s.target_name||s.name);i().uploadFile(p[s.id],r,t.chunk_size,m.width,m.height,m.quality||90,/\.(jpg|jpeg)$/i.test(s.name)?"jpg":"png")});f.bind("Flash:UploadProcess",function(r,q){var s=r.getFile(p[q.id]);s.loaded=q.loaded;s.size=q.size;r.trigger("UploadProgress",s)});f.bind("Flash:UploadChunkComplete",function(q,s){var t,r=q.getFile(p[s.id]);t={file:r,chunk:s.chunk,chunks:s.chunks,response:s.text};q.trigger("ChunkUploaded",t);if(t.cancelled){i().cancelUpload();r.status=c.FAILED;q.trigger("FileUploaded",r);return}});f.bind("Flash:UploadComplete",function(r,q){var s=r.getFile(p[q.id]);s.status=c.DONE;r.trigger("FileUploaded",s)});f.bind("Flash:SelectFiles",function(q,t){var s,r,u=[],v;for(r=0;r<t.length;r++){s=t[r];v=c.guid();p[v]=s.id;p[s.id]=v;u.push(new c.File(v,s.name,s.size))}f.trigger("FilesAdded",u)});f.bind("QueueChanged",function(q){f.refresh()});f.bind("FilesRemoved",function(q,s){var r;for(r=0;r<s.length;r++){i().removeFile(p[s[r].id])}});f.bind("StateChanged",function(q){f.refresh()});f.bind("Refresh",function(q){var r,s;r=document.getElementById(q.settings.browse_button);s=c.getPos(r);c.extend(document.getElementById(q.id+"_flash_container").style,{top:s.y+"px",left:s.x+"px",width:r.clientWidth+"px",height:r.clientHeight+"px"})});k({success:true})})}})})(plupload);(function(a){var b=true,c=false;a.runtimes.BrowserPlus=a.addRuntime("browserplus",{init:function(i,k){var g=window.BrowserPlus,j={},f=i.settings,e=f.resize;function h(p){var o,n,l=[],m,q;for(n=0;n<p.length;n++){m=p[n];q=a.guid();j[q]=m;l.push(new a.File(q,m.name,m.size))}if(n){i.trigger("FilesAdded",l)}}if(g){g.init(function(m){var l=[{service:"Uploader",version:"3"},{service:"DragAndDrop",version:"1"},{service:"FileBrowse",version:"1"}];if(e){l.push({service:"ImageAlter",version:"4"})}if(m.success){g.require({services:l},function(){if(m.success){d()}else{k()}})}else{k()}})}else{k()}function d(){i.bind("PostInit",function(){var o,m=f.drop_element,q=i.id+"_droptarget",l=document.getElementById(m),n;if(l){function r(t,s){g.DragAndDrop.AddDropTarget({id:t},function(u){g.DragAndDrop.AttachCallbacks({id:t,hover:function(v){if(!v&&s){s()}},drop:function(v){if(s){s()}h(v)}},function(){})})}if(document.attachEvent&&(/MSIE/gi).test(navigator.userAgent)){o=document.createElement("div");o.setAttribute("id",q);a.extend(o.style,{position:"absolute",top:"-1000px",background:"red",filter:"alpha(opacity=0)",opacity:0});document.body.appendChild(o);a.addEvent(l,"dragenter",function(t){var s,u;s=document.getElementById(m);u=a.getPos(s);a.extend(document.getElementById(q).style,{top:u.y+"px",left:u.x+"px",width:s.offsetWidth+"px",height:s.offsetHeight+"px"})});function p(){document.getElementById(q).style.top="-1000px"}r(q,p)}else{r(m)}}a.addEvent(document.getElementById(f.browse_button),"click",function(x){var v=[],t,s,w=f.filters,u;x.preventDefault();for(t=0;t<w.length;t++){u=w[t].extensions.split(",");for(s=0;s<u.length;s++){v.push(a.mimeTypes[u[s]])}}g.FileBrowse.OpenBrowseDialog({mimeTypes:v},function(y){if(y.success){h(y.value)}})});l=dropTarget=0});i.bind("UploadFile",function(l,n){var m=l.settings.url,p=j[n.id];function o(q){n.size=q.size;g.Uploader.upload({url:m+(m.indexOf("?")==-1?"?":"&")+"&multipart=true&name="+escape(n.target_name||n.name),files:{file:q},cookies:document.cookies,progressCallback:function(r){n.loaded=r.fileSent;l.trigger("UploadProgress",n)}},function(r){if(r.success){n.status=a.DONE;l.trigger("FileUploaded",n)}})}if(e&&/\.(png|jpg|jpeg)$/i.test(n.name)){BrowserPlus.ImageAlter.transform({file:p,quality:e.quality||90,actions:[{scale:{maxwidth:e.width,maxheight:e.height}}]},function(q){if(q.success){o(q.value.file)}})}else{o(p)}});i.features={dragdrop:b,jpgresize:b,pngresize:b,chunks:c};k({success:b})}}})})(plupload);(function(b){var c=true;function a(i,l,j,d,k){var f,e,h,g;f=document.createElement("canvas");f.style.display="none";document.body.appendChild(f);e=f.getContext("2d");h=new Image();h.onload=function(){var o,m,n;scale=Math.min(l/h.width,j/h.height);if(scale<1){o=Math.round(h.width*scale);m=Math.round(h.height*scale)}else{o=h.width;m=h.height}f.width=o;f.height=m;e.drawImage(h,0,0,o,m);g=f.toDataURL(d);g=g.substring(g.indexOf("base64,")+7);g=atob(g);f.parentNode.removeChild(f);k({success:true,data:g})};h.src=i}b.runtimes.Html5=b.addRuntime("html5",{init:function(h,i){var d={},f;function g(m){var k,j,l=[],n;for(j=0;j<m.length;j++){k=m[j];n=b.guid();d[n]=k;l.push(new b.File(n,k.fileName,k.fileSize))}h.trigger("FilesAdded",l)}function e(){var j;if(window.XMLHttpRequest){j=new XMLHttpRequest();return !!(j.sendAsBinary||j.upload)}return false}if(!e()){i({success:false});return}h.bind("Init",function(k){var q,j=[],l,p,o=k.settings.filters,n,m;q=document.createElement("div");q.id=k.id+"_html5_container";for(l=0;l<o.length;l++){n=o[l].extensions.split(/,/);for(p=0;p<n.length;p++){m=b.mimeTypes[n[p]];if(m){j.push(m)}}}b.extend(q.style,{position:"absolute",background:h.settings.html5_bgcolor||"transparent",width:"100px",height:"100px",overflow:"hidden",opacity:0});q.className="plupload_html5";document.body.appendChild(q);q.innerHTML='<input id="'+h.id+'_html5" style="width:100%;" type="file" accept="'+j.join(",")+'" '+(h.settings.multi_selection?'multiple="multiple"':"")+" />";document.getElementById(h.id+"_html5").onchange=function(){g(this.files);this.value=""}});h.bind("PostInit",function(){var j=document.getElementById(h.settings.drop_element);if(j){b.addEvent(j,"dragover",function(k){k.preventDefault()});b.addEvent(j,"drop",function(l){var k=l.dataTransfer;if(k&&k.files){g(k.files)}l.preventDefault()})}});h.bind("Refresh",function(j){var k,l;k=document.getElementById(h.settings.browse_button);l=b.getPos(k);b.extend(document.getElementById(h.id+"_html5_container").style,{top:l.y+"px",left:l.x+"px",width:k.offsetWidth+"px",height:k.offsetHeight+"px"})});h.bind("UploadFile",function(j,n){var p=new XMLHttpRequest(),m,l=j.settings.url,k=j.settings.resize,o;if(n.status==b.DONE||n.status==b.FAILED||j.state==b.STOPPED){return}if(m=p.upload){m.onload=function(){n.status=b.DONE;j.trigger("FileUploaded",n)};m.onprogress=function(q){n.loaded=q.loaded;j.trigger("UploadProgress",n)}}p.onreadystatechange=function(){if(p.readyState==4){n.status=b.DONE;n.loaded=n.size;j.trigger("UploadProgress",n);j.trigger("FileUploaded",n)}};p.open("post",l+(l.indexOf("?")==-1?"?":"&")+"name="+escape(n.target_name||n.name),true);p.setRequestHeader("Content-Type","application/octet-stream");o=d[n.id];if(p.sendAsBinary){if(k&&/\.(png|jpg|jpeg)$/i.test(n.name)){a(o.getAsDataURL(),k.width,k.height,/\.png$/i.test(n.name)?"image/png":"image/jpeg",function(q){if(q.success){n.size=q.data.length;p.sendAsBinary(q.data)}else{p.sendAsBinary(o.getAsBinary())}})}else{p.sendAsBinary(o.getAsBinary())}}else{p.send(o)}});f=!!(File&&File.prototype.getAsDataURL);h.features={dragdrop:window.mozInnerScreenX!==undefined,jpgresize:f,pngresize:f};i({success:c})}})})(plupload);