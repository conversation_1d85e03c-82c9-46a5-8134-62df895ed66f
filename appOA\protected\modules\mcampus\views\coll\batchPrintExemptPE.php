<style>
    .container-fluid{
        width:860px;
        margin:0 auto
    }
    .page{
        margin-top:20px
    }
    img{
        margin-top:10px;
        max-width:100%;
        height:auto
    }
    .sign{
        float: right;
        margin-right:20px;
        margin-bottom:20px;
        max-height: 15mm;
    }
    .sign img{
        width:200px;
        height: 110px;
    }
    @media print {
        .print_img{
            max-height: 290mm;
            display: block;
            margin: 0 auto;
        }
        .alwaysAttr{
            page-break-after:always;
        }

        .print_protocol_img
        {
            max-height: 290mm;
            display: block;
            margin: 0 auto;
        }
    }
</style>
<?php $studentNum = count($child_infos);$i=0;?>
<?php foreach ($child_infos as $child_id=>$child_info){?>
    <?php
    $i++;
    //将所有图片合并
    $all_photo[$child_id] = array();
    foreach ($step_data[$child_id]['medical_certificate'] as $medical_certificate_url){
        $all_photo[$child_id][] = array(
            'type'=>'Rx',
            'photo'=>$medical_certificate_url,
        );
    }
    ?>
    <div class="container-fluid">
        <div class ="row">
            <table class="table">
                <tbody>
                <tr>
                    <th  width="25%"><?php echo Yii::t('reg', 'Name') ?></th>
                    <td colspan="2"><?php echo $child_info->getChildName() ?></td>
                </tr>
                <tr>
                    <th><?php echo Yii::t('labels', 'Date of Birth') ?></th>
                    <td colspan="2"><?php echo $child_info->birthday_search ?></td>
                </tr>
                <tr>
                    <th><?php echo Yii::t('labels', 'Class') ?></th>
                    <td colspan="2"><?php echo $child_info->ivyclass->title?></td>
                </tr>

                <tr>
                    <th> <?php echo Yii::t('coll', 'Please describe in detail the medical reasons why your child needs to apply for exemption from PE') ?> </th>
                    <td colspan="2"><?php echo $step_data[$child_id]['medical_reasons']?></td>
                </tr>
                <tr>
                    <th> <?php echo Yii::t('coll', 'Please fill in the application dates for your child exemption from PE') ?> </th>
                    <td colspan="2">
                        <?php if(!empty($step_data[$child_id]['application_dates'][0]) && !empty($step_data[$child_id]['application_dates'][1])){?>
                            <?php echo $step_data[$child_id]['application_dates'][0]?><?php echo Yii::t('coll', 'To')?><?php echo $step_data[$child_id]['application_dates'][1]?>
                        <?php }?>
                    </td>
                </tr>
                <tr>
                    <th> <?php echo Yii::t('coll', 'Others') ?> </th>
                    <td colspan="2"><?php echo $step_data[$child_id]['other']?></td>
                </tr>
                <?php if(!empty($step_data[$child_id]['medical_certificate'])){?>
                    <tr>
                        <th><?php echo Yii::t('coll', 'Please upload the medical certificate issued by doctor (Diagnosis certificate with "medical excuse from physical education and dates" written on it or Medical Excuse Form From Physical Education)') ?> </th>
                        <td colspan="2">
                            <?php if(!empty($all_photo[$child_id])){?>
                                <?php echo Yii::t('coll', 'See annex')?>
                            <?php }else{?>
                                <?php echo Yii::t('coll', 'None')?>
                            <?php }?>
                        </td>
                    </tr>
                <?php }?>
                <!---护士审核情况--->
                <tr >
                    <th rowspan="5">
                       <?php echo Yii::t('reg', 'Summary of Nurse Review Status') ?>
                    </th>
                </tr>
                <tr>
                    <th><?php echo Yii::t('coll', 'Confirm whether students need to exemption from physical education') ?></th>
                    <td>
                        <?php echo $step_data[$child_id]['remarks']['nonPhysicalEducation'] ==1 ? Yii::t('coll','Yes')  : Yii::t('coll','No') ?>
                    </td>
                </tr>
                <?php if($step_data[$child_id]['remarks']['nonPhysicalEducation'] ==1){?>
                    <tr>
                        <th><?php echo Yii::t('coll', 'Reasons of exemption from PE') ?></th>
                        <td>
                            <?php echo $step_data[$child_id]['remarks']['reasons_cn']; ?>
                            <?php echo $step_data[$child_id]['remarks']['reasons_en']; ?>
                        </td>
                    </tr>
                    <tr>
                        <th><?php echo Yii::t('coll', 'Exemption Date') ?></th>
                        <td>
                            <?php if(!empty($step_data[$child_id]['remarks']['exemptionDate'][0]) && !empty($step_data[$child_id]['remarks']['exemptionDate'][1])){?>
                                <?php echo $step_data[$child_id]['remarks']['exemptionDate'][0].' '.Yii::t('coll', 'To').' '.$step_data[$child_id]['remarks']['exemptionDate'][1]?>
                            <?php }else{?>
                                <?php echo Yii::t('coll', 'None')?>
                            <?php }?>
                        </td>
                    </tr>
                <?php }?>
                <?php if(!empty($step_data[$child_id]['remarks']['other_cn']) || !empty($step_data[$child_id]['remarks']['other_en'])){?>
                    <tr>
                        <th><?php echo Yii::t('coll', 'Other Information') ?></th>
                        <td>
                            <?php echo $step_data[$child_id]['remarks']['other_cn']; ?> <br>
                            <?php echo $step_data[$child_id]['remarks']['other_en']; ?>
                        </td>
                    </tr>
                <?php }?>
            </tbody>
            </table>
        </div>
        <!--协议-->
        <?php $protocol_num = count($step_data[$child_id]['extraInfo']['protocol'])?>
        <!--        有图片才添加分页-->
        <?php if(!empty($all_photo[$child_id]) || !empty($protocol_num)){?>
               <div style="page-break-after:always;"></div>
        <?php }?>
        <?php if(isset($step_data[$child_id]['extraInfo']['protocol']) && !empty($step_data[$child_id]['extraInfo']['protocol'])){?>
            <!--    协议-->
            <div class="row">
                <?php foreach ($step_data[$child_id]['extraInfo']['protocol'] as $k=>$v){?>
                    <div>
                        <?php echo Yii::t('coll','Agreement')?>
                        <?php echo $child_info->getChildName() ?>
                        <?php echo $child_info->ivyclass->title?>
                    </div>
                    <p>
                        <img  class="img-responsive print_protocol_img" src="<?php echo $v; ?>">
                    </p>
                    <?php if(($k+1)<$protocol_num){?>
                    <div style="page-break-after:always;"></div>
                    <?php }?>
                    <?php if(($k+1)==$protocol_num){?>
                    <?php if(!empty($step_data[$child_id]['childStatus']['sign_url'])){?>
                    <div class="sign">
                        <?php echo Yii::t('coll','Signature')?>
                        <img src="<?php echo $step_data[$child_id]['childStatus']['sign_url']?>" alt="" >
                    </div>
                    <?php }?>
                    <div style="page-break-after:always;"></div>
                    <?php }?>
                <?php }?>
            </div>
        <?php }?>

        <!-- 处方照片 -->
        <?php if(!empty($all_photo[$child_id])){?>
            <div>
                <?php foreach ($all_photo[$child_id] as $k=>$val){?>
                    <div>
                        <?php echo Yii::t('coll','Attach')?>
                        <?php echo Yii::t('coll', 'Medical certificate'); ?>
                        <?php echo $child_info->getChildName() ?>
                        <?php echo $child_info->ivyclass->title?>
                    </div>
                    <p>
                        <?php if(count($all_photo[$child_id]) == $k+1){?>
                            <img  class="img-responsive print_img" src="<?php echo $val['photo']; ?>">
                        <?php } else {?>
                            <img  class="img-responsive print_img alwaysAttr" src="<?php echo $val['photo']; ?>">
                        <?php }?>
                    </p>
                <?php }?>
            </div>
        <?php }?>
    </div>
    <?php if($studentNum != $i){?>
        <div style="page-break-after:always;"></div>
    <?php }?>
<?php }?>
