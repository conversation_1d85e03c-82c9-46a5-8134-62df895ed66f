<?php

/**
 *
 */

class InitialCommand extends CConsoleCommand {
	public $batchNum = 200;
	/**
	 * 将SQL文件切割成SQL命令
	 * @param	Object	$sqlfile	SQL文件路径
	 * @param	Boolean	$exeSql		是否执行
	 * @return	Object				Description
	 */
	private function iniDB($sqlfile, $exeSql=false){
		if(!$exeSql) return true;
		$error = false;
		if (!file_exists($sqlfile)) {
			$errs[] = sprintf( "%s not found!", $sqlfile);
			$error = true;
		} else {
			$sql_query = fread(fopen($sqlfile, 'r'), filesize($sqlfile));
			$sql_query = trim($sql_query);
			$sqlUtility = new SqlUtility();
			$sqlUtility->splitMySqlFile($pieces, $sql_query);
			$created_tables = array();
			foreach ($pieces as $piece) {
				Yii::app()->db->createCommand($piece)->execute();
			}
			// if there was an error, delete the tables created so far, so the next installation will not fail
			if ($error == true) {
				print_r($errs);
				Yii::app()->end();
			}
		}
		return true;
	}
	
	/**
	 * 准备数据库表
	 * @return	Object		Description
	 */
	public function actionIniDB(){
		$sqlfile = dirname(__FILE__) . "./../data/child.mysql.sql";
		$this->iniDB($sqlfile, true);
	}
	
	/**
	 * 将已经设置过访客访问的孩子信息添加到新的访问KEY中
	 * @return	Object		Description
	 */
	public function actionChildAccess(){
		$criteria = new CDbCriteria();
		$criteria->condition = "access_pass <> ''";	
		$total = ChildProfileBasic::model()->count($criteria);

		echo "total number of contents : ".$total."\r\n";

		$cycle=ceil($total/$this->batchNum);
		for($i=0; $i<$cycle; $i++){
			//echo "start from " . $i*$this->batchNum. "\r\n";
			$criteria->limit=$this->batchNum;
			$criteria->offset=$i*$this->batchNum;
			$items = ChildProfileBasic::model()->findAll($criteria);
				
			foreach($items as $item){
				$newitem = new ChildAccess();
				$newitem->setAttributes(array(
						'accesskey'=>md5($item->childid),
						'childid'=>$item->childid,
				));
				try{
					$newitem->insert();
				}
				catch(Exception $e){
					echo "insert failed.\r\n";
				}
			}

		}
		echo "completed.\r\n";

	}
	
	/**
	 * 初始化基于角色控制的权限和角色设置
	 * @return	Object		Description
	 */
	public function actionRBAC()
	{
		
		$rbac = include_once(dirname(__FILE__) . "./include/rbac.config.php");
		$auth=Yii::app()->authManager;
		
		foreach($rbac['operations'] as $operation=>$comment){
			$auth->createOperation($operation,$comment);
		}
		
		foreach($rbac['tasks'] as $key=>$_task){
			$bizRule = isset($_task['bizRule']) ? $_task['bizRule'] : NULL;
			$desc = isset($_task['desc']) ? $_task['desc'] : "";
			$task=$auth->createTask($key,$desc,$bizRule);
			if(isset($_task['items']) && count($_task['items'])){
				foreach($_task['items'] as $child){
					$task->addChild($child);
				}
			}
		}
		
		foreach($rbac['roles'] as $key=>$_role){
			$bizRule = isset($_role['bizRule']) ? $_role['bizRule'] : NULL;
			$desc = isset($_role['desc']) ? $_role['desc'] : "";
			$role=$auth->createRole($key, $desc, $bizRule);
			foreach($_role['items'] as $child){
				$role->addChild($child);
			}
		}
		
		/*
		$auth->createOperation('readLunchMenu','Read child lunch menu');
		$auth->createOperation('readClassContact','Read child contact list');
		$auth->createOperation('readClassTeacher','Read class teacher contact list');
		
		$auth->createOperation('updateChildInfo','update child information');
		$auth->createOperation('updateFamilyInfo','update child family information');
		$auth->createOperation('cancelLunch','Cancel lunch');
		/*
		$bizRule='return (in_array($params["childid"], Yii::app()->user->getVisitCIds() ) || Yii::app()->user->isStaff() || in_array($params["childid"], Yii::app()->user->getAdminCIds() ) );';
		$task=$auth->createTask('visitChild','access child info by visitor',$bizRule);
		$task->addChild('readJournal');
		$task->addChild('readLunchMenu');

		$bizRule='return (in_array($params["childid"], Yii::app()->user->getAdminCIds() ) || Yii::app()->user->isStaff() );';
		$task=$auth->createTask('privateInfo','Access child private info',$bizRule);
		$task->addChild('readClassContact');
		$task->addChild('readClassTeacher');

		$bizRule='return in_array($params["childid"], Yii::app()->user->getAdminCIds() );';
		$task=$auth->createTask('adminChild','admin child info by parent',$bizRule);
		$task->addChild('updateChildInfo');
		$task->addChild('updateFamilyInfo');
		$task->addChild('cancelLunch');
		
		
		$role=$auth->createRole('visitor');
		$role->addChild('visitChild');
		
		$role=$auth->createRole('staff');
		$role->addChild('privateInfo');
		
		$role=$auth->createRole('parent');
		$role->addChild('visitor');
		$role->addChild('privateInfo');
		$role->addChild('adminChild');
		*/
		//$role->save();
		//$auth->assign('admin',2);
		
		//$auth->createOperation('createPost','create a post');
		//$auth->createOperation('readPost','read a post');
		//$auth->createOperation('updatePost','update a post');
		//$auth->createOperation('deletePost','delete a post');
		//
		//$bizRule='return Yii::app()->user->id==$params["post"]->authID;';
		//$task=$auth->createTask('updateOwnPost','update a post by author himself',$bizRule);
		//$task->addChild('updatePost');
		// 
		//$role=$auth->createRole('reader');
		//$role->addChild('readPost');
		// 
		//$role=$auth->createRole('author');
		//$role->addChild('reader');
		//$role->addChild('createPost');
		//$role->addChild('updateOwnPost');
		//
		//$role=$auth->createRole('editor');
		//$role->addChild('reader');
		//$role->addChild('updatePost');
		// 
		//$role=$auth->createRole('admin');
		//$role->addChild('editor');
		//$role->addChild('author');
		//$role->addChild('deletePost');
		// 
		//$auth->assign('reader','readerA');
		//$auth->assign('author','authorB');
		//$auth->assign('editor','editorC');
		//$auth->assign('admin','adminD');
		//
		echo "finished";
	}	

}