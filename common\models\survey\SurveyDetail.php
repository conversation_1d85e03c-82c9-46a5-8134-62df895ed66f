<?php

/**
 * This is the model class for table "ivy_survey_detail".
 *
 * The followings are the available columns in table 'ivy_survey_detail':
 * @property integer $id
 * @property integer $survey_id
 * @property integer $start_time
 * @property integer $end_time
 * @property string $schoolid
 * @property integer $is_published
 * @property integer $has_report
 * @property integer $open2school
 * @property integer $update_user
 * @property integer $update_time
 * @property string $before_memo_en
 * @property string $before_memo_cn
 * @property string $process_memo_en
 * @property string $process_memo_cn
 * @property string $after_memo_en
 * @property string $after_memo_cn
 */
class SurveyDetail extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_survey_detail';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('survey_id, start_time, end_time, is_published, has_report, open2school, update_user, update_time', 'numerical', 'integerOnly'=>true),
			array('schoolid', 'length', 'max'=>255),
			array('before_memo_en, before_memo_cn, process_memo_en, process_memo_cn, after_memo_en, after_memo_cn', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, survey_id, start_time, end_time, schoolid, is_published, has_report, open2school, update_user, update_time, before_memo_en, before_memo_cn, process_memo_en, process_memo_cn, after_memo_en, after_memo_cn', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'survey_id' => 'Survey',
			'start_time' => 'Start Time',
			'end_time' => 'End Time',
			'schoolid' => 'Schoolid',
			'is_published' => 'Is Published',
			'has_report' => 'Has Report',
			'open2school' => 'Open2school',
			'update_user' => 'Update User',
			'update_time' => 'Update Time',
			'before_memo_en' => 'Before Memo En',
			'before_memo_cn' => 'Before Memo Cn',
			'process_memo_en' => 'Process Memo En',
			'process_memo_cn' => 'Process Memo Cn',
			'after_memo_en' => 'After Memo En',
			'after_memo_cn' => 'After Memo Cn',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('survey_id',$this->survey_id);
		$criteria->compare('start_time',$this->start_time);
		$criteria->compare('end_time',$this->end_time);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('is_published',$this->is_published);
		$criteria->compare('has_report',$this->has_report);
		$criteria->compare('open2school',$this->open2school);
		$criteria->compare('update_user',$this->update_user);
		$criteria->compare('update_time',$this->update_time);
		$criteria->compare('before_memo_en',$this->before_memo_en,true);
		$criteria->compare('before_memo_cn',$this->before_memo_cn,true);
		$criteria->compare('process_memo_en',$this->process_memo_en,true);
		$criteria->compare('process_memo_cn',$this->process_memo_cn,true);
		$criteria->compare('after_memo_en',$this->after_memo_en,true);
		$criteria->compare('after_memo_cn',$this->after_memo_cn,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return SurveyDetail the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
