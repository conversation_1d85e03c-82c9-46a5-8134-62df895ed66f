<?php if ($schoolid):?>
<div class="member">
    <?php foreach ($dps as $dkey=>$dp):?>
    <h3><?php echo $deptitle[$dkey];?></h3>
    <ul>
        <?php foreach ($dp as $dk=>$d):?>
        <?php foreach ($posids[$dk] as $uid=>$user):?>
        <li class="item">
            <label>
            <?php echo CHtml::image($user['photo'], '', array('id'=>'avatar_'.$user['uid']))?>
            <div><input type="checkbox" name="member" value="<?php echo $uid?>" <?php echo in_array($uid, $usered) ? 'checked' : ''?>><span id="name_<?php echo $uid?>" class="fljs"><?php echo $user['name']?></span></div>
            </label>
        </li>
        <?php endforeach;?>
        <?php endforeach;?>
        <li class="clear"></li>
    </ul>
    <div class="clear"></div>
    <?php endforeach;?>
</div>
    <div class="pop_bottom">
        <button type="button" class="btn fr" id="J_dialog_close"><?php echo Yii::t('global','Cancel');?></button>
        <button class="btn fr btn_submit mr10" onclick="assignname1();">确定</button>
    </div>
<?php else:?>
<h2><center>请先选择学校</center></h2>
<?php endif; ?>

<style>
    .member{
        overflow-y: auto;
        max-height: 300px;
        padding: 0 5px;
    }
    .member li.item{
        float: left;
        margin: 5px;
        width: 70px;
        height: 90px;
        overflow: hidden;
    }
    .member ul{
        margin-bottom: 20px;
    }
    .member li.item img{
        width: 70px;
        height: 70px;
    }
</style>

<script>
    function assignname1()
    {
        var h = '', v = '';
        $('.member input[name="member"]').each(function(){
            var _this = $(this);
            if (_this.attr('checked') == 'checked'){
                v += _this.val()+',';
                h += '<img src="'+$("#avatar_"+_this.val()).attr("src")+'" style="width:30px;"> ';
            }
        })
        parent.document.getElementById("PtaMemo_pta_admins").value = v;
        parent.document.getElementById("aptamembers").innerHTML = h;
        if(window.parent.head.dialog) {
            window.parent.head.dialog.closeAll();
        }
    }
</script>