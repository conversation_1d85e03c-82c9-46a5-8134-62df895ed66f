<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo Yii::t('site','Finance Management');?></li>
        <li class="active">课后课财务</li>
    </ol>

    <div class="row">
        <!-- 左侧菜单 -->
        <div class="col-md-2 col-sm-2 mb10">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->getMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                'activeCssClass' => 'active',
                'itemCssClass' => ''
            ));
            ?>
        </div>

        <div class="col-md-10 col-sm-12">
            <form action="" class="form-inline mb15 J_ajaxForm" method="post">
                <?php echo CHtml::dropDownList('schoolId', '', CHtml::listData($this->getAllBranch(), 'id', 'title'), array('class'=>'form-control form-group', 'empty'=>Yii::t('global', 'Please Select')));?>
                <?php echo CHtml::dropDownList('groupId', '', array(), array('class'=>'form-control form-group', 'empty'=>Yii::t('global', 'Please Select')));?>
                <!--<button class="btn btn-primary J_ajax_submit_btn">查询</button>-->
                <div id='dao' class='pull-right'></div>
                <!-- <button onclick="daochu()" class="btn btn-primary btn-sm pull-right">导出表格</button> -->
            </form>
            <div class="row J_check_wrap" id="viewData"></div>
        </div>
    </div>
</div>

<div class="modal fade" id="modal" tabindex="-1" role="dialog" aria-labelledby="modal">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="exampleModalLabel">添加</h4>
            </div>
            <?php $form = $this->beginWidget('CActiveForm', array(
                'id' => 'course-form',
                'enableAjaxValidation' => false,
                'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form'),
                'action' => $this->createUrl('saveOther'),
            )); ?>
            <div class="modal-body">
                <div class="form-group">
                    <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'school_id'); ?></label>
                    <div class="col-xs-10">
                        <?php echo CHtml::dropDownList('AsaOtherIncome[school_id]', '', CHtml::listData($this->getAllBranch(), 'id', 'title'), array('class'=>'form-control', 'empty'=>Yii::t('global', 'Please Select')));?>
                    </div>
                </div>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'group_id'); ?></label>

                    <div class="col-xs-10">
                        <?php echo CHtml::dropDownList('AsaOtherIncome[group_id]', '', array(), array('class'=>'form-control', 'empty'=>Yii::t('global', 'Please Select')));?>
                    </div>
                </div>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'category'); ?></label>

                    <div class="col-xs-10">
                        <?php echo $form->dropDownList($model, 'category', AsaOtherIncome::category(), array('class' => 'form-control')); ?>
                    </div>
                </div>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'start_time'); ?></label>

                    <div class="col-xs-10">
                        <?php echo $form->textField($model, 'start_time', array('maxlength' => 255, 'class' => 'form-control datepicker')); ?>
                    </div>
                </div>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'end_time'); ?></label>

                    <div class="col-xs-10">
                        <?php echo $form->textField($model, 'end_time', array('maxlength' => 255, 'class' => 'form-control datepicker')); ?>
                    </div>
                </div>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'fee'); ?></label>

                    <div class="col-xs-10">
                        <?php echo $form->textField($model, 'fee', array('maxlength' => 255, 'class' => 'form-control')); ?>
                    </div>
                </div>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'contract'); ?></label>

                    <div class="col-xs-10">
                        <div id="attachment"></div>
                        <hr>
                        <input id="inputfile" onchange="uploadatta()" type="file">
                        <br>
                        <span id="fileinfo"></span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <span id="start_fail_info" class="pull-left text-warning" style="display: none;"><i
                class="glyphicon glyphicon-remove text-warning"></i><?php echo Yii::t('asa', '当天该时间段已有课程') ?></span>
                <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
                <button type="button" class="btn btn-default"
                        data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
            </div>
            <?php $this->endWidget(); ?>
        </div>
    </div>
</div>

<script>
    $('.datepicker').datepicker({
        changeMonth: true,
        changeYear: true,
        dateFormat:'yy-mm-dd'
    });
    var groups = <?php echo CJSON::encode($groups)?>;
    var groupId;
    var schoolId;
    $('#schoolId').change(function(){
        $('#groupId').html('<option value=""><?php echo Yii::t('global', 'Please Select')?></option>');
        $('#viewData').empty()
        var options = groups[$(this).val()];
        if (options) {
            for(var id in options){
                $('<option></option>').val(options[id]['id']).text(options[id]['name']).appendTo( $('#groupId') );
            }
        }
    });

    $('#groupId').change(function(){
        groupId = $(this).val()
        schoolId = $("#schoolId").val()
        getStr(groupId,schoolId)

    });

    $('#AsaOtherIncome_school_id').change(function(){
        var options = groups[$(this).val()];
        $("#AsaOtherIncome_group_id").empty();
        if (options) {
            for(var id in options){
                $('<option></option>').val(options[id]['id']).text(options[id]['name']).appendTo( $('#AsaOtherIncome_group_id') );
            }
        }else{
            $('<option></option>').val('').text('请选择').appendTo( $('#AsaOtherIncome_group_id') );
        }
    });

    function getStr(groupId,schoolId){
        var url = '<?php echo $this->createUrl('otherList')?>';
        $.ajax({
            type: "POST",
            url: url,
            data: {schoolId:schoolId, groupId:groupId},
            dataType: "json",
            success: function(data){
                var html = '';
                var btn=''
                btn+='<button onclick="zengjia()" type="button" class="btn btn-primary"><?php echo Yii::t('user','增加'); ?></button>'
                html += '<div class="col-md-12" id="block">';
                html += '<div class="table-responsive">';
                html += '<table class="table table-bordered table-hover">';
                html += '<thead>';
                html += '<tr>';
                html += '<th class="text-center">ID</th>';
                html += '<th class="text-center"><?php echo Yii::t("principal","类型"); ?></th>';
                html += '<th class="text-center"><?php echo Yii::t("asa","合同"); ?></th>';
                html += '<th class="text-center"><?php echo Yii::t("asa","开始时间"); ?></th>';
                html += '<th class="text-center"><?php echo Yii::t("asa","结束时间"); ?></th>';
                html += '<th class="text-center"><?php echo Yii::t("asa","合同金额"); ?></th>';
                html += '<th class="text-center"><?php echo Yii::t("asa","增加时间"); ?></th>';
                html += '<th class="text-center"><?php echo Yii::t("asa","增加人"); ?></th>';
                html += '</tr>';
                html += '</thead>';
                html += '<tbody>';

                if(data['state'] == 'success'){
                    $.each(data['data'],function(i,item){
                        html += '<tr>';
                        html += '<td class="text-right">' + item.id + '</td>';
                        html += '<td class="text-right">' + item.category + '</td>';
                        html += '<td class="text-right">' + item.contract + '</td>';
                        html += '<td class="text-right">' + item.start_time + '</td>';
                        html += '<td class="text-right">' + item.end_time + '</td>';
                        html += '<td class="text-right">' + item.fee + '</td>';
                        html += '<td class="text-right">' + item.created_at + '</td>';
                        html += '<td class="text-right">' + item.created_by + '</td>';
                        html += '</tr>';
                    })
                }
                $('#viewData').html(html);
                $('#dao').html(btn);
            }
        });
    }

    function zengjia() {
        $("#modal input").val("");
        $("#modal #attachment").empty();
        $("#AsaOtherIncome_school_id").val(schoolId);
        var options = groups[schoolId];
        if (options) {
            $("#AsaOtherIncome_group_id").html('')
            for(var id in options){
                $('<option></option>').val(options[id]['id']).text(options[id]['name']).appendTo( $('#AsaOtherIncome_group_id') );
            }
            $("#AsaOtherIncome_group_id").val(groupId);
        }else{
            $('<option></option>').val('').text('请选择').appendTo( $('#AsaOtherIncome_group_id') );
        }
        $("#modal").modal('show');

    }



    function callback(){
        $("#modal").modal('hide');
        getStr(groupId,schoolId)

    }

    function daochu(){
        var elt = document.getElementById('block');
        var wb = XLSX.utils.table_to_book(elt, {sheet:"课程收入"});
        return XLSX.writeFile(wb, '课程收入.xlsx');
    }

    //上传附件
    function uploadatta() {
        var data = new FormData();
        $.each($('#inputfile')[0].files, function (i, file) {
            data.append('upload_file', file);
        });
        $.ajax({
            url: '<?php echo $this->createUrl('uploadatta'); ?>',
            data: data,
            type: 'post',
            dataType: 'json',
            contentType: false,    //不可缺
            processData: false,    //不可缺
            success: function (data) {
                if (data.msg == 'success') {
                    //清空文件域
                    $("#inputfile").after($("#inputfile").clone().val(""));
                    $("#inputfile").remove();
                    //显示附件
                    var span = '<span onclick="delAtt(this,\'' + data.saveName + '\')" class="glyphicon glyphicon-remove" aria-hidden="true"></span> ';
                    var atta = '<label class="btn btn-success btn-xs"><a style="color:#fff" target="_blank" href="' + data.url + '">' + data.fileName + '</a> '+span+'<input type="hidden" name="AsaOtherIncome[contract][]" value="' + data.saveName + '" /></label> ';
                    $("#attachment").append(atta);
                } else {
                    $("#fileinfo").text(data.msg);
                }
            },
            error: function (data) {
                $("#fileinfo").text(data.msg);
            }
        });
    }

    // 删除附件
    function delAtt(obj,saveName) {
        var r = confirm('确定删除？');
        if (r == true) {
            $.ajax({
                url: '<?php echo $this->createUrl('delOssfileRedit'); ?>',
                data: {saveName:saveName},
                type: 'post',
                dataType: 'json',
                success: function (data) {
                    $(obj).parent().remove();
                },
                error: function (data) {
                    $("#fileinfo").text(data.msg);
                }
            });
        }
    }
</script>
