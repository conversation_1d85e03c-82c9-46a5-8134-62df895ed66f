<?php
$data = array(); 
$yids = array();
$action = Yii::app()->controller->action->id;
foreach ($this->allTimetable as $tid => $item) {
    $yids[$item['yid']] = $tid;
}
Yii::import('common.models.calendar.CalendarSchool');
$calendars = CalendarSchool::model()->findAllByAttributes(array('branchid' => $this->branchId, 'yid' => array_keys($yids)));

foreach ($calendars as $calendar){
    $tid = $yids[$calendar->yid];
    $end = $calendar->startyear + 1;
    $data[$tid] = $calendar->startyear . ' - ' . $end;
}
$data = array_reverse($data, true);
?>

<ul class="nav nav-pills">
    <?php foreach ($data as $tid => $year): ?>
        <?php if($tid == $this->tid){ ?>
            <li role="presentation" class="active"><a href="javascript:void(0)"><?php echo $year; ?></a></li>
        <?php }else{ ?>
            <li role="presentation" class="background-gray"><a href="<?php echo $this->createTidUrl($action, array('tid' => $tid)) ?>"><?php echo $year ?> </a></li>
        <?php } ?>
    <?php endforeach; ?>
</ul>
<hr>