<style>
    [v-cloak]{display: none;}
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('default/index')); ?></li>
        <li class="active"><?php echo Yii::t('site', '服务申请'); ?></li>
    </ol>
    <div class="row" id="service" v-cloak>
        <div class="col-md-12">
            <div class="btn-group col-md-12">
                <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    服务申请 <span class="caret"></span>
                </button>
                <ul class="dropdown-menu">
                    <li v-for="(item, key) in config"><a @click="newService(item.serviceId)" href="javascript:;">{{item.title}}</a></li>
                </ul>
            </div>
            <div class="col-md-12">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th width="10%" class="nosort">服务标题</th>
                            <th width="10%">服务类型</th>
                            <th width="5%">开始时间</th>
                            <th width="5%">结束时间</th>
                            <th width="5%">去程费用</th>
                            <th width="5%">返程费用</th>
                            <th width="5%">住宿费用</th>
                            <th width="5%" class="nosort">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="(item, i) in serviceData" :id="'item_' + i">
                            <td>{{item.service_title}}</td>
                            <td>{{config[item.service_id].title}}</td>
                            <td>{{item.start_time}}</td>
                            <td>{{item.end_time}}</td>
                            <td>{{item.serviceExpense[1]}}</td>
                            <td>{{item.serviceExpense[2]}}</td>
                            <td>{{item.serviceExpense[3]}}</td>
                            <td>
                                <button @click="show(i)" class="btn btn-primary btn-xs">查看</button>
                                <a v-if="item.status == 1" :href="'<?php echo $this->createUrl('delete'); ?>&service_id='+item.id" class="btn btn-danger btn-xs J_ajax_del" data-msg="确定要撤回吗？">撤回</a>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <nav aria-label="Page navigation">
                    <ul class="pagination">
                        <?php
                        $prevClass = 'disabled';
                        $prevHref = '#';
                        if ($page - 1 > 0) {
                            $prevClass = '';
                            $prevHref = $this->createUrl('index', array('page' => $page - 1));
                        }
                        echo '<li class="' . $prevClass . '"><a href="' . $prevHref . '" aria-label="Previous"> <span aria-hidden="true">&laquo;</span></a> </li>';

                        for ($i = 1; $i <= $totalPages; $i++) {
                            $class = ($i == $page) ? 'active' : '';
                            $href = $this->createUrl('index', array('page' => $i));
                            echo '<li class="' . $class . '"><a href="' . $href . '">' . $i . '</a></li>';
                        }

                        $prevClass = 'disabled';
                        $prevHref = '#';
                        if ($totalPages - $page > 0) {
                            $prevClass = '';
                            $prevHref = $this->createUrl('index', array('page' => $page + 1));
                        }
                        echo '<li class="' . $prevClass . '"><a href="' . $prevHref . '" aria-label="Next"> <span aria-hidden="true">&raquo;</span></a> </li>';
                        ?>
                    </ul>
                </nav>
            </div>
        </div>
        <!-- 新增模态框 -->
        <div class="modal fade" id="newmodal" tabindex="-1" role="dialog" aria-labelledby="modal">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" v-if="serviceId != 0">{{config[serviceId].title}}</h4>
                    </div>
                    <div class="modal-body">
                        <form v-if="serviceId != 0" id="requestForm" class="form-horizontal" action="<?php echo $this->createUrl('new'); ?>">
                            <div class="form-group">
                                <label class="col-xs-3 control-label">
                                    <label for="service_title">服务标题 *</label>
                                </label>
                                <div class="col-xs-8">
                                    <input maxlength="255" class="form-control" name="service_title" type="text" :value="config[serviceId].title">
                                </div>
                            </div>
                            <div v-for="(field, i) in config[serviceId].requestFields" class="form-group">
                                <label class="col-xs-3 control-label">
                                    <label for="">{{field.title}}{{field.required ? ' *' : ''}}</label>
                                </label>
                                <div class="col-xs-8">
                                    <template v-if="field.type == 'text' ">
                                        <textarea class="form-control" :name="'service_data[' +field.id+ ']'" :id=field.id rows="3"></textarea>
                                    </template>
                                    <template v-else-if="field.type == 'timestamp' ">
                                        <input maxlength="255" class="form-control datepicker" :name="'service_data[' +field.id+ ']'" :id=field.id type="text">
                                    </template>
                                    <template v-else>
                                        <input maxlength="255" class="form-control" :name="'service_data[' +field.id+ ']'" :id=field.id type="text">
                                    </template>
                                </div>
                            </div>
                            <input type="hidden" name="service_id" :value=serviceId>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary btn-save" @click="save">保存</button>
                        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    </div>
                </div>
            </div>
        </div>
        <!-- 显示模态框 -->
        <div class="modal fade" id="showmodal" tabindex="-1" role="dialog" aria-labelledby="modal">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" v-if="dataId != 0">{{config[serviceId].title}}</h4>
                    </div>
                    <div class="modal-body">
                        <form v-if="dataId != 0" id="requestForm" class="form-horizontal" action="<?php echo $this->createUrl('new'); ?>">
                            <div class="form-group">
                                <label class="col-xs-3 control-label">
                                    <label for="service_title">服务标题 *</label>
                                </label>
                                <div class="col-xs-8">
                                    <input maxlength="255" class="form-control" type="text" :value="serviceData[dataId].service_title" disabled>
                                </div>
                            </div>
                            <div v-for="(field, i) in config[serviceId].requestFields" class="form-group">
                                <label class="col-xs-3 control-label">
                                    <label for="">{{field.title}}{{field.required ? ' *' : ''}}</label>
                                </label>
                                <div class="col-xs-8">
                                    <template v-if="field.type == 'text' ">
                                        <textarea class="form-control" rows="3" :value=serviceData[dataId].serviceField[field.id] disabled></textarea>
                                    </template>
                                    <template v-else-if="field.type == 'timestamp' ">
                                        <input maxlength="255" class="form-control datepicker" type="text" :value=serviceData[dataId].serviceField[field.id] disabled>
                                    </template>
                                    <template v-else>
                                        <input maxlength="255" class="form-control" type="text" :value=serviceData[dataId].serviceField[field.id] disabled>
                                    </template>
                                </div>
                            </div>
                            <hr>
                            <div v-if="JSON.stringify(serviceData[dataId].serviceExpense) !== '{}'" v-for="(field, i) in config[serviceId].expenseFields" class="form-group">
                                <label class="col-xs-3 control-label">
                                    <label for="">{{field.title}}{{field.required ? ' *' : ''}}</label>
                                </label>
                                <div v-if="serviceData[dataId].serviceExpense" class="col-xs-8">
                                    <template v-if="field.type == 'text' ">
                                        <textarea class="form-control" rows="3" :value=serviceData[dataId].serviceExpense[field.id] disabled></textarea>
                                    </template>
                                    <template v-else-if="field.type == 'timestamp' ">
                                        <input maxlength="255" class="form-control datepicker" type="text" :value=serviceData[dataId].serviceExpense[field.id] disabled>
                                    </template>
                                    <template v-else>
                                        <input maxlength="255" class="form-control" type="text" :value=serviceData[dataId].serviceExpense[field.id] disabled>
                                    </template>
                                </div>
                            </div>
                            <input type="hidden" name="service_id" :value=serviceId>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
<!-- 显示底部学校选择栏 -->
<?php $this->renderPartial('//layouts/common/branchSelectBottom'); ?>

<!-- script代码 -->
<script>
    var config = <?php echo json_encode($config); ?>;
    var data = <?php echo json_encode((object)$data); ?>;
    var service = new Vue({
        el: "#service",
        data: {
            config: config,
            serviceId: 0,
            isManager: false,
            serviceData: data,
            dataId: 0,
            table: '',
        },
        mounted: function() {
            this.createTable();
        },
        created: function() {},
        updated: function() {
            head.Util.ajaxDel();
            let minDate = 'd';
            if (this.isManager == true) {
                minDate = '';
            }
            $('.datepicker').datepicker({
                minDate: minDate,
                dateFormat: 'yy-mm-dd',
            });
            if (this.table == '') {
                this.createTable();
            }
        },
        watch: {
            // 如果 `question` 发生改变，这个函数就会运行
            serviceData: function(newData, oldData) {
                service.table.destroy();
                service.table = '';
            }
        },
        computed: {},
        methods: {
            newService(serviceId) {
                this.serviceId = serviceId;
                this.isManager = this.config[serviceId].isManager;
                $('#newmodal').modal('show');
            },
            show(id) {
                this.dataId = id;
                this.serviceId = this.serviceData[id].service_id;
                $('#showmodal').modal('show');
            },
            save: function(e) {
                $(e.currentTarget).attr('disabled', 'disabled');
                let form = $('#requestForm');
                $.ajax({
                    url: '<?php echo $this->createUrl("new") ?>',
                    type: "post",
                    dataType: 'JSON',
                    data: form.serialize(),
                    success: function(res) {
                        if (res.state == 'success') {
                            service.$set(service.serviceData, res.data.id, res.data);
                            $('#newmodal').modal('hide');
                            resultTip({
                                error: 'success',
                                msg: 'success'
                            });
                        } else {
                            let msg = res.message
                            resultTip({
                                error: 'error',
                                msg: msg
                            });
                        }
                    },
                    error: function(res) {
                        resultTip({
                            error: 'error',
                            msg: 'error'
                        });
                    },
                }).done(function() {
                    $('.btn-save').removeAttr('disabled');
                });
            },
            removeData(id) {
                delete this.serviceData[id];
                this.serviceData.shift();
                this.$set(this.serviceData, 1, {});
            },
            createTable() {
                this.table = $('.table').DataTable({
                    aaSorting: [2, 'desc'], // 默认排序
                    paging: false,
                    info: false,
                    searching: false,
                    columnDefs: [{
                        "targets": 'nosort',
                        "orderable": false
                    }],
                });
            },
        },
    });

    function refreshData(data) {
        service.$delete(service.serviceData, data);
        // service.table.destroy();
        // service.createTable();
    }
</script>