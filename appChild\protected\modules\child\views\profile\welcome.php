<?php
$this->breadcrumbs=array(
	Yii::t("navigations","Welcome"),
);?>

<script>
function showProfileHover(){
	$('#child-operations').addClass('showOperation');
}
function recoverProfileHover(){
	$('#child-operations').removeClass('showOperation');
}
</script>

<?php
$this->widget('application.extensions.notice.promotion',
	array(
		'startDate'=>'2015/04/22',
		'endDate'=>'2017/03/11',
		'view'=>'voices'
	)
);

if(!Yii::app()->user->isGuest){
	if(!Mims::unIvy() || Yii::app()->params['ownerConfig']['key'] != 'BJ_DS'){
		$this->widget('application.extensions.notice.wechat',
			array(
			)
		);
	}
	$this->widget('application.extensions.notice.promotion',
		array(
			'startDate'=>'2015/05/15',
			'endDate'=>'2015/06/12',
			'view'=>'survey'
			)
	);
}

if(Yii::app()->params['ownerConfig']['key'] == 'BJ_TT'){
	$this->widget('application.extensions.notice.promotion',
		array(
			'startDate'=>'2015/04/22',
			'endDate'=>'2016/08/21',
			'view'=>'fortt'
		)
	);
}

?>


<?php
/*
 * 孩子首页显示问卷信息
*/
if(!Mims::unIvy()){
	$this->widget('application.extensions.notice.handbook', array('childid'=>$this->childid, 'version'=>'v1'));

	$this->widget('application.extensions.birthdayReminder.BirthdayReminder',
		array(
			'useTodayParam'=>true, //使用诸如 ?today=2012/04/19 参数进行模拟
			'beforeDay'=>7,
			'afterDay'=>7,
		)
	);
}
?>

<?php if(Yii::app()->params['ownerConfig']['key'] != 'BJ_DS'):?>
<div id="recent-items">
    <?php $this->widget('application.extensions.weekMisc.weekMisc', array())?>
    <?php $this->widget('application.extensions.latestJournal.latestJournal', array('schoolId'=>$schoolId, 'classId'=>$classId, 'yId'=>$yId))?>
    <?php $this->widget('application.extensions.latestReport.latestReport', array())?>
</div>
<div class="clear"></div>
<?php endif;?>


<?php
/*
 * 孩子首页显示问卷信息
*/
//$this->widget('application.extensions.survey.SurveyBoxWidget',array('scriptFile'=>false));
?>

<?php
/*
 * 孩子首页显示投票信息
*/
$this->widget('application.extensions.vote.VoteBoxWidget',array('cssFile'=>true))

?>
<?php
if($this->beginCache($this->genCacheId('CampusList',$this->getChildId()), array('duration'=>$this->cacheDuration)) ) {
$this->widget('application.extensions.welcome.Welcome', array(
        'schoolId'=>$schoolId,
        'yId'=>$yId
    )
);
$this->endCache(); }
?>
<?php
//$this->widget('application.extensions.schoolnews.SchoolNews', array(
//        'schoolId'=>$schoolId,
//    )
//);
?>

	<?php
	$flashMessages = Yii::app()->user->getFlashes();
	if ($flashMessages) {
		echo '<div class="flashes">';
		echo '<ul >';
		foreach($flashMessages as $key => $message) {
			echo '<li><div class="flash-' . $key . '">' . $message . "</div></li>\n";
		}
		echo '</ul>';
		echo '</div>';
	}
	Yii::app()->clientScript->registerScript(
			'myHideEffect',
			'$(".flashes").animate({opacity: 1.0}, 3000).fadeOut("slow");',
			CClientScript::POS_READY
	);
	?>


<?php

$nextUrl = $this->createUrl("//child/profile/profile",array("demo"=>"guide"));

Yii::import('ext.guide.class.*');

$closeajax = Yii::app()->user->isFirstLogin() ? $this->createUrl('/child/profile/closeguide') : false;

$this->widget('ext.guide.Guide', array(
    'options'=>array(
        'guides'=>array(
			//欢迎页
            0=>GuideItem::welcomeGuide(10, null),

			//孩子头像
			10=>GuideItem::childSelectorGuide(20, 0),

			//导航
			20=>GuideItem::navGuide(30, 10),

			//导航
			30=>GuideItem::navGuide(40, 20, true, "navigations_portfolio"),

			//子导航
			40=>GuideItem::subNavGuide($nextUrl, 30, "subnav_portfolio"),


        ),
//        'closeajax'=>$closeajax,
        'demo'=>( Yii::app()->user->isFirstLogin() && !Yii::app()->user->getHideGuide() ) ? true : false
    )
));
?>
