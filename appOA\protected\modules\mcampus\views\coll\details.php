<style>
    [v-cloak] {
		display: none;
	}
    .speed {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 50%;
    }
    .orange{
		background: orange;
	}
	.gray{
		background:#ccc;
	}
	.blue{
		background:#428BCA;
	}
	.green{
		background:green;
	}
	.red{
		background:red;
	}
    .text-overflow{
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .border{
        border:1px solid #E5E7EB;
        border-radius:4px;
    }
    .borderBto{
        border-bottom:1px solid #E5E7EB;

    }
    .height2{
        height:200px;
        overflow-y:auto
    }
    .p0{
        padding:0
    }
    .font20{
        font-size:20px;
    }
    .font16{
        font-size:16px
    }
    .pl10{
        padding-left:10px
    }
    .F0AD4E{
        color:#F0AD4E
    }
    .number{
        display: inline-block;
        width: 17px;
        height: 17px;
        border: 1px solid #ADADAD;
        border-radius: 50%;
        text-align: center;
        line-height: 17px;
        margin-right: 14px;
        color:#ADADAD
    }
    .active .number {
        color:#fff;
        border: 1px solid #fff;
    }
    .display{
        display:none
    }
    .child:hover .display{
        display:inline-block
    }
	.nav-wizard > li>a:hover{
	   background:#d5d5d5
	}
	.nav-wizard > li{
	    text-align: center;
	}
	.nav-wizard a{
		color:#ADADAD;
		font-size:14px
	}
    .nav-wizard > li:not(:first-child) > a:before{
	    border-top: 23px inset transparent;
		border-bottom: 18px inset transparent;
	}
	.nav-wizard > li:not(:last-child) > a:after{
	    border-top: 23px inset transparent;
   		border-bottom: 17px inset transparent;
	}
    .greenStep{
        color:#3EB435 !important;
    }
    .redStep{
        color:#DB4545 !important;
    }
    .orgStep{
        color:#F19853 !important;
    }
    .stuImg{
        width:60px;
        height:60px;
        border-radius:50%
    }
    .imgbig{
        width:2rem;
        height:2rem;
    }
    .mb0 {
        margin-bottom: 0 !important;
    }
    .bodbotnone {
        border-top: none;
        padding: 0;
        padding-bottom: 15px;
    }
    .greenIcon{
        color:#5DB85B
    }
    .redIcon{
        color:#D5514E
    }
    .yellowIcon{
        color:#F1AD4E
    }
    .signImg{
        width:100px;
        border:1px solid #ccc
    }
    .signImgDetails{
        width:200px;
        border:1px solid #ccc
    }
    .stepUpImg{
        max-width:200px;
        margin-right:10px
    }
    .avatarImage{
        width:55px;
        height:55px;
    }
    .flexWidth {
        border-left:1px solid #ddd;
    }
    .listSteps .flexWidth:last-child {
        border-left:none !important
    }
    .stepNumber{
        width: 24px;
        height: 24px;
        font-size: 14px;
        text-align: center;
        line-height: 23px;
        margin-left:-12px;
        background: #fff;
    }
    .colorBlue{
        color:#4D88D2
    }
    .grey{
        background:#F7F8FA;
        border-radius:8px
    }
    /*使图片在浏览器中居中显示*/
    .bigImg {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%,-50%);
        max-width:90%;
        max-height:90%
    }
    .bg {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%,-50%);
    }
    .opacityBottom {
        width: 100%;
        height: 100%;
        position: fixed;
        background: rgba(0,0,0,0.8);
        z-index: 9999;
        top: 0;
        left: 0;
    }
    .qrcode{
        position: absolute;
        right: 320px;
        top: 90px;
        z-index: 99;
        background: #fff;
        border-radius: 5px;
        box-shadow: 0px 2px 8px 0px rgb(0 0 0 / 20%);
        text-align:center;
        margin:0 auto;
        padding-bottom: 0;
        width: 250px;
    }
    .loading{
        width:98%;
        height:100%;
        background:#fff;
        position: absolute;
        opacity: 0.5;
        z-index: 99
    }
    .loading span{
        width:100%;
        height:100%;
        display:block;
        background: url("<?php echo Yii::app()->theme->baseUrl?>/images/loading.gif")no-repeat center center ;
    }
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site','Campus Support'), array('//mcampus/default/index'))?></li>
        <li class="active"><a href="<?php echo $this->createUrl('index', array('branchId' => $this->branchId)); ?>">学生信息收集</a></li>
        <li class="active"><?php echo Yii::t('site', '详情') ?></li>
    </ol>
    <div class="row"  id='registration' v-cloak>
        <div class="col-md-2 col-sm-2">
            <ul class="nav nav-pills nav-stacked text-left background-gray" id="pageCategory">
                    <li class="active" ><a href="<?php echo $this->createUrl('index', array('branchId' => $this->branchId)); ?>">学生信息收集</a></li>
                    <li class="" ><a href="<?php echo $this->createUrl('busStation', array('branchId' => $this->branchId)); ?>">班车站点</a></li>
            </ul>
        </div>
        <div class="col-md-10 col-sm-10" @click='qrcodeShow=false'>
            <p class='font14'><a href="<?php echo $this->createUrl('index', array('branchId' => $this->branchId)); ?>"><span class='glyphicon glyphicon-chevron-left font12'></span> 学生信息收集</a> | <span>详情</span> </p>
            <div class="media p15 border" style='background:#F9FAFB'>
                <div class='media-right pull-right mt15'>
                    <a href='javascript:;' class='glyphicon glyphicon-qrcode pl10 relative viewQrcode' @click="getQRCode"><span class='ml5'>二维码分享</span> </a>
                    <button type="button" class="btn btn-primary ml10" @click='editInfo()'><?php echo Yii::t('site', 'Edit basic information') ?></button>
                    <button type="button" class="btn btn-primary ml10" @click='editConfigure'>编辑注册配置</button>
                    <div class='qrcode font14 color3 p20' v-if='qrcodeShow'>
                        <p>{{showList.master.title}}</p>
                        <img class=" img-face mb10" style='width:150px;height:150px' :src="qrcodeImg">
                        <p class=''>扫码查看</p>
                        <p>可与家长分享</p>
                    </div>
                </div>
                <div class="media-body media-middle" v-if='showList.master'>
                    <p class='font20'><strong>{{showList.master.title}}</strong></p>
                    <p v-html='html(showList.master.desc)'></p>
                    <p class='font14' v-if="showList.master.type==1">{{showList.master.startdate}} - {{showList.master.enddate}}</p>
                    <p class='font14' v-if="showList.master.type==2">长期收集</p>
                </div>
            </div>
            <div class='F0AD4E font14 mt20 flex' style="align-items: center;height: 40px">
                <div class='flex1'><span class='glyphicon glyphicon-info-sign'></span> 选择步骤可查看完成情况</div>
                <div style='width:100px;color:#4D88D2;display:none' class='font14 cancelStep cur-p' @click='cancelStep()'>
                    <span class='glyphicon glyphicon-remove-circle'></span> 取消选择
                </div>
            </div>
            <div class='mb10'>
                <ul class="nav nav-wizard  mt10">
                    <li v-for='(list,index) in showList.step' class='mb10' :class='stepName==list.flag?"active":""'>
                        <a href="javascript:;" @click="stepInfo(list.flag)">
                            <span class="number">{{index+1}}</span>
                            {{list.title}}
                        </a>
                    </li>
                </ul>
            </div>
            <div class="stepInfo" id="stepInfo"></div>
            <div class="collDetail">
                <div class="mb15 mt20">
                        <a href="javascript:;" class="btn btn-primary"  @click='addChild("add")'>添加新学生</a>
                    <div data-example-id="single-button-dropdown" class="bs-example pull-right">
                        <div class="btn-group">
                            <button type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" class="btn btn-default dropdown-toggle">
                                提取邮件地址 <span class="caret"></span></button> 
                            <ul class="dropdown-menu">
                                <li><a href="javascript:;" @click='email(1)'>全部家长邮件地址</a></li> 
                                <li><a href="javascript:;" @click='email(2)'>未绑定微信家长邮件地址</a></li>
                            </ul>
                        </div> 
                        <div class="btn-group"><a href="javascript:;" class="btn btn-default" @click='send()' ><?php echo Yii::t('teaching', 'Send wechat notification to parents') ?></a></div>
                    </div>
                </div>
                <div>
                    <label class="mr15" v-for='(list,key,index) in showList.status_map'>
                        <span class="speed gray" v-if='key==0'></span> 
                        <span class="speed orange" v-if='key==1'></span> 
                        <span class="speed green" v-if='key==2'></span> 
                        <span class="speed blue" v-if='key==3'></span> 
                        <span class="speed red" v-if='key==4'></span> 
                        {{list}}
                    </label>
                </div>
                <div  v-for='(list,index) in showList.class_list'>
                    <div class="panel panel-default mt20" v-if='showList.student[list.classid]'>
                        <div class="panel-heading">{{list.title}}</div> 
                        <div class="panel-body">
                            <div class="col-xs-2 col-sm-2 col-md-2 col-lg-2 mb20 child" v-for='(item,index) in showList.student[list.classid]'>
                                <span class="speed gray" v-if='item.status==0'></span> 
                                <span class="speed orange" v-if='item.status==1'></span> 
                                <span class="speed green" v-if='item.status==2'></span> 
                                <span class="speed blue" v-if='item.status==3'></span> 
                                <span class="speed red" v-if='item.status==4'></span> 
                                <a href="javascript:;" @click='stuDetails(item.childid)'>{{item.name}}</a>
                                <span class='glyphicon glyphicon-trash ml20 cur-p display' @click='delStu(item)'></span> 
                            </div>
                        </div>
                    </div>
                </div>
                
            </div>
        </div>
        <div class="modal fade" id="editConfigure" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false">

        </div>
        <div class="modal fade" id="addChild" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false" data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" ><?php echo Yii::t('teaching', 'Add Students') ?> <span id="remarks_t"></span></h4>
                    </div>
                    <div class="modal-body" v-if='addStuList.child_by_class'>
                        <ul class="nav nav-pills mb20" v-if='yearData.addStudentShowYearList'>
                            <li role="presentation" :class='addChildType==list.yid+list.type?"active":""' v-for='(list,index) in yearData.addStudentShowYearList' @click='addChild(list)'><a href="javascript:;">{{list.title}}</a></li>
                            <li role="presentation" @click='addChild("-1")' :class='addChildType==-1?"active":""'>
                                <a href="javascript:;">New</a>
                            </li>
                        </ul>
                        <p  class="checkbox ml15" v-if='checkData.length!=0'>
                            <label>
                                <input type="checkbox" v-model='checkedAll'  class="emailStu"  @change='classChecked()'><?php echo Yii::t('global', 'Select All') ?>
                            </label>
                        </p>
                        <div style='max-height:500px;overflow-y:auto'>
                            <div class="panel panel-default" v-for='(list,index) in checkData'>
                                <div class="panel-heading">
                                    <div class="checkbox"> 
                                        <label v-if='list.selected'>
                                            <input type="checkbox"  disabled='true' checked class="emailStu" >{{list.title}}
                                        </label>
                                        <label v-else>
                                            <input type="checkbox" v-model='list.checked'  class="emailStu"  :value="list.id" @change='allChecked(list)'>{{list.title}}
                                        </label>
                                    </div>
                                </div> 
                                <div class="panel-body">
                                    <div class="col-xs-3 col-sm-3 col-md-3 col-lg-3" v-for='(item,idx) in list.stuList'>
                                        <div class="checkbox"> 
                                            <label v-if='item.selected==1'>
                                                <input type="checkbox" disabled='true' checked class="emailStu" >{{item.child_name}}
                                            </label>
                                            <label v-else>
                                                <input type="checkbox" class="emailStu" v-model='list.checkStu' :value="item.childid">{{item.child_name}}
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel') ?></button>
                        <button type="button" class="btn btn-primary" :disabled='addStuBtn' @click='saveAddStu()'><?php echo Yii::t('global', 'Save') ?></button>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal fade" id="stuDetailsHome" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" ><?php echo Yii::t('attends', '整体情况') ?> <span id="remarks_t"></span></h4>
                    </div>
                    <div class="modal-body color6" v-if='childData.child'>
                        <p><strong class='font14 color3'>学生资料</strong></p>
                        <div class='flex'>
                            <div style='width:80px'><img :src="childData.child.avatar" class='stuImg'></div>
                            <div class='flex1'>
                                <p class='mt10'><strong class='font14 color3'>{{childData.child.name}}</strong> </p>
                                <p class='mb20'>{{childData.child.class_name}}</p>
                                <div>
                                    <!-- <div class='col-md-1 p0'>
                                        
                                    </div> -->
                                    <div class='col-md-4'>
                                    <strong class='color3'><?php echo Yii::t('site', 'Father') ?>：</strong>  
                                        <span class='glyphicon glyphicon-user'></span> {{childData.parents.finfo.name}}
                                    </div>
                                    <div class='col-md-4'>
                                        <span class='glyphicon glyphicon-phone'></span> {{childData.parents.finfo.mphone}}
                                    </div>
                                    <div class='col-md-4'>
                                        <span class='glyphicon glyphicon-envelope'></span> {{childData.parents.finfo.email}}
                                    </div>
                                </div>
                                <div class='clearfix'></div>
                                <div class='mt10'>
                                    <!-- <div class='col-md-1 p0'>母亲:
                                    </div> -->
                                    <div class='col-md-4'>
                                    <strong class='color3'><?php echo Yii::t('site', 'Mother') ?>：</strong> 
                                        <span class='glyphicon glyphicon-user'></span> {{childData.parents.minfo.name}}
                                    </div>
                                    <div class='col-md-4'>
                                        <span class='glyphicon glyphicon-phone'></span> {{childData.parents.minfo.mphone}}
                                    </div>
                                    <div class='col-md-4'>
                                        <span class='glyphicon glyphicon-envelope'></span> {{childData.parents.minfo.email}}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <ul class="nav nav-wizard pb20 mt20">
                            <li v-for='(list,index) in childData.step_status'>
                                <template v-if="list.status == 0">
                                    <a href="javascript:;" style="cursor:default;" :data-tab="list.step_id" :data-status="list.status" :data-name="childData.child.name">
                                        <span class="number">{{index+1}}</span>{{list.title}}
                                    </a>
                                </template>
                                <template v-else>
                                    <a href="javascript:;"  :data-tab="list.step_id" :data-status="list.status" :data-name="childData.child.name" @click="stepInfo(list.step_id,childData.child.name,list.status)">
                                        <span class="number">{{index+1}}</span>{{list.title}}
                                    </a>
                                </template>
                                <p class='mt10 color6' :class="list.status==4?'greenStep':list.status==3?'redStep':list.status==1?'orgStep':''">{{ childData.status_map[list.status]}}</p>
                            </li>
                        </ul>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel') ?></button>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal fade" id="editInfo" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false" data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" ><?php echo Yii::t('attends', '基本信息') ?> <span id="remarks_t"></span></h4>
                    </div>
                    <div class="modal-body color6">
                        <div class="form-horizontal">
                            <div class="form-group">
                                <label for="inputPassword3" class="col-sm-2 control-label"><?php echo Yii::t('event', 'Title Cn') ?></label>
                                <div class="col-sm-10">
                                <input type="text" class="form-control" id="inputPassword3" :value='coll.title_cn' v-model='coll.title_cn'>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inputPassword3" class="col-sm-2 control-label"><?php echo Yii::t('event', 'Title En') ?></label>
                                <div class="col-sm-10">
                                <input type="text" class="form-control" id="inputPassword3" :value='coll.title_en' v-model='coll.title_en'>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inputPassword3" class="col-sm-2 control-label">中文简介</label>
                                <div class="col-sm-10">
                                    <textarea class="form-control" rows="3" :value='coll.desc_cn' v-model='coll.desc_cn' ></textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inputPassword3" class="col-sm-2 control-label">英文简介</label>
                                <div class="col-sm-10">
                                    <textarea class="form-control" rows="3" :value='coll.desc_en' v-model='coll.desc_en'></textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inputPassword3" class="col-sm-2 control-label">填写完成的展示文字（中文）</label>
                                <div class="col-sm-10">
                                    <textarea class="form-control" rows="3" :value='coll.sub_success_cn' v-model='coll.sub_success_cn'></textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inputPassword3" class="col-sm-2 control-label">填写完成的展示文字（英文）</label>
                                <div class="col-sm-10">
                                    <textarea class="form-control" rows="3" :value='coll.sub_success_en' v-model='coll.sub_success_en'></textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inputPassword3" class="col-sm-2 control-label">审核完成的展示文字（中文）</label>
                                <div class="col-sm-10">
                                    <textarea class="form-control" rows="3" :value='coll.audit_success_cn' v-model='coll.audit_success_cn'></textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inputPassword3" class="col-sm-2 control-label">审核完成的展示文字（英文）</label>
                                <div class="col-sm-10">
                                    <textarea class="form-control" rows="3" :value='coll.audit_success_en' v-model='coll.audit_success_en'></textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inputPassword3" class="col-sm-2 control-label"><?php echo Yii::t('labels', 'Date') ?></label>
                                <div class="col-sm-10">
                                <input type="text" class="form-control select_2 pull-left mr10 ml0" @blur='publish_atVal' id="startDate" v-model='coll.startdate'  placeholder="<?php echo Yii::t("newDS", "Select a date"); ?>"  :value='coll.startdate'>
                                <span class='pull-left' style='line-height:30px'>-</span>
                                <input type="text" class="form-control select_2 pull-left ml10"  @blur='expired_atVal' id="endDate" v-model='coll.enddate'  placeholder="<?php echo Yii::t("newDS", "Select a date"); ?>"  :value='coll.enddate'>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inputPassword3" class="col-sm-2 control-label">步骤</label>
                                <div class="col-sm-10">
                                    <div class='col-sm-5 p0'>
                                        <div class='border'>
                                            <p class='text-center borderBto p10'>全部步骤</p>
                                            <div class='p10 height2'>
                                                <div class="checkbox" v-for='(item, index) in configStepAll'>
                                                    <label  :for=`checkbox${item.flag}`>
                                                        <input type="checkbox" value=""  :id=`checkbox${item.flag}` name="checkbox" :checked="item.select" @click="item.select=!item.select">{{ item.title }}
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class='col-sm-2 text-center'>
                                        <div style='margin-top:80px'>
                                            <button type="button" class="btn btn-primary" @click='del'><</button>
                                            <button type="button" class="btn btn-primary ml10" @click='push'>></button>
                                        </div>
                                    </div>
                                    <div class='col-sm-5 p0'>
                                        <div class='border '>
                                            <p class='text-center borderBto p10'>已选步骤</p>
                                            <div class='p10 height2'>
                                                <div class="checkbox flex" v-for='(item, index) in new_info'  :class='item.dragStatus?"colorBlue":""'>
                                                    <label :for=`newcheckbox${item.flag}` class='flex1'>
                                                        <input type="checkbox" value="" :id=`newcheckbox${item.flag}` name="newcheckbox" :checked="item.select" @click="item.select=!item.select">
                                                        {{ item.title }}
                                                    </label>
                                                    <span style='width:35px' class='text-right inline-block mt5 relative' v-if='!item.isEdit'>
                                                        <span  class='dragStatus'
                                                            draggable="true"
                                                            @dragstart="handleDragStart($event, item, new_info)" 
                                                            @dragover.prevent="handleDragOver($event, item, new_info)" 
                                                            @dragenter="handleDragEnter($event, item, new_info)" 
                                                            @dragend="handleDragEnd($event, item, new_info, index)" >
                                                            <span 
                                                            >
                                                                <span class="glyphicon glyphicon-move"></span>
                                                            </span>
                                                        </span>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel') ?></button>
                        <button type="button" class="btn btn-primary"  :disabled='addStuBtn' @click='saveInfo()'><?php echo Yii::t('global', 'Save') ?></button>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal fade" tabindex="-1" role="dialog" id='emailData' data-backdrop="static">
            <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title"><?php echo Yii::t('labels', 'Email') ?></h4>
                </div>
                <div class="modal-body">
                <p class="break">
                    <textarea class="form-control" rows="10" id="emailText" :value='parentEmail'></textarea>
                    <!-- <input type="email" class="form-control" id="exampleInputEmail1" placeholder="Email"> -->
                    <!-- {{parentEmail}} -->
                </p>
                </div>
                <div class="modal-footer">
                <button type="button" class="btn btn-default" @click="updateEmailText(1)">使用分号分割</button>
                <button type="button" class="btn btn-default" @click="updateEmailText(2)">使用逗号分割</button>
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel') ?></button>
                </div>
            </div>
            </div>
        </div>
        <div class="modal fade bs-example-modal-lg" id="release" tabindex="-1" role="dialog" data-backdrop="static" data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                        <span @click="closesend()">&times;</span>
                        </button>
                        <h4 class="modal-title" id="myModalLabel"><?php echo Yii::t('newDS', 'Send Notification') ?></h4>
                    </div>
                    <div class="modal-body">
                        <div class="col-md-6">
                            <div class="panel panel-info">
                                <div class="panel-heading">
                                    <?php echo Yii::t('teaching', 'Available to send')?>
                                    （{{sends_length}}）
                                </div>
                                <div class="J_check_wrap">
                                    <table class="table  table-striped mb0">
                                        <thead>
                                            <tr style="height: 33px">
                                                <th width="40" class="textcen">
                                                    
                                                    <div class="checkbox-custom checkbox-default">
                                                            <input type="checkbox" class="J_check_all " data-checklist="J_check_c1" data-direction="y"   v-model="checked" @click="changeAllChecked()">
                                                        <label></label>
                                                    </div>
                                                    
                                                </th>
                                                <th width="150"><?php echo Yii::t('global', 'Name') ?></th>
                                                <th><img style="width: 20px" src="/themes/base/images/weixin.png" alt=""></th>
                                            </tr>
                                        </thead>
                                    </table>
                                    <div class="pre-scrollable" style="height:340px;margin-top:-1px">
                                        <table class="table mb0">
                                            <tbody class="childname">
                                                <tr  v-for='(sendChild,index) in sends' :id="'tr'+sendChild.id">
                                                    <td width="40" :id="'check'+sendChild.id" class="textcen">
                                                        <div class="checkbox-custom checkbox-default">
                                                                <input class="J_check" data-yid="J_check_c1" type="checkbox" name='send' :value="sendChild.id"  v-model="checkedNames">
                                                            <label></label>
                                                        </div>
                                                    </td>
                                                    <td width="40" class="pr0">
                                                            <img class="media-object child-face img-thumbnail pull-left" :src="sendChild.photo" :title="sendChild.name">
                                                    </td>
                                                    <td width="100">
                                                        <span>{{sendChild.name}}</span>
                                                    </td>
                                                    <td :id="sendChild.id" class="sendlist">
                                                        <p class="mt5">
                                                            <template v-for='(parent,idx) in sendChild.parent'>
                                                                <img class="img-circle imgbig ml5" v-if='parent.headimgurl!=""' :src="parent.headimgurl" :alt="parent.headimgurl" :title="parent.name">
                                                                <img class="img-circle imgbig ml5" v-else src="http://oa.ivyonline.cn/uploads/childmgt/blank.gif!w200" :alt="parent.headimgurl" :title="parent.name">
                                                            </template>
                                                        </p>
                                                        <p class="mt5 textsend" :id="'text'+sendChild.id"></p>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer bodbotnone">
                                <button type="submit" class="btn btn-primary sendbtn" @click='sendChild()'><?php echo Yii::t('global', 'Send')?></button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <?php echo Yii::t('teaching', 'Unavailable to send')?>
                                    （{{sent.length+noWechat.length}}）
                                </div>
                                <table class="table table-striped mb0">
                                    <thead>
                                        <tr>
                                            <th width="150"><?php echo Yii::t('global', 'Name') ?></th>
                                            <th><img style="width: 20px" src="/themes/base/images/weixin.png" alt=""></th>
                                            <th width="78"><?php echo Yii::t('user', 'Status') ?></th>
                                        </tr>
                                    </thead>
                                </table>
                                <div class="pre-scrollable" style="height:340px;margin-top:-1px">
                                    <table class="table mb0">
                                        <tbody class="addchild">
                                        
                                            <tr v-for='(sendChild,index) in sent'>
                                                <td width="40" class="pr0">
                                                        <img class="media-object child-face img-thumbnail pull-left" :src="sendChild.photo" :title="sendChild.name">
                                                </td>
                                                <td width="100">
                                                    <span>{{sendChild.name}}</span>
                                                </td>
                                                <td :id="sendChild.id" class="sendlist">
                                                    <p class="mt5">
                                                        <template v-for='(parent,idx) in sendChild.parent'>
                                                            <img class="img-circle imgbig ml5" v-if='parent.headimgurl!=""' :src="parent.headimgurl" :alt="parent.headimgurl" :title="parent.name">
                                                            <img class="img-circle imgbig ml5" v-else src="http://oa.ivyonline.cn/uploads/childmgt/blank.gif!w200" :alt="parent.headimgurl" :title="parent.name">
                                                        </template>
                                                    </p>
                                                    <p class="mt5 textsend" :id="'text'+sendChild.id"></p>
                                                </td>
                                                <td  width="60"><?php echo Yii::t('teaching', 'Sent')?></td>
                                            </tr>
                                            <tr  v-for='(Wechat,index) in noWechat'>
                                                <td width="40" class="pr0"> 
                                                    <img class="media-object child-face img-thumbnail pull-left" :src="Wechat.photo" :title="Wechat.name">
                                                </td>
                                                <td width="100"> 
                                                    <span>{{Wechat.name}}</span>
                                                </td>
                                                <td><?php echo Yii::t('teaching', 'No wechat account linked')?></td>
                                                <td width="50"></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="modal-footer bodbotnone">
                                <span><?php echo Yii::t('teaching', 'Notification can be sent only once within 24 hours.') ?></span>
                            </div>
                        </div>
                    </div>
                    <div class="clearfix"></div>
                </div>
            </div>
        </div>
        <div class="modal fade" id="delModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
            <div class="modal-dialog modal-sm" role="document">
                <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "Delete");?></h4>
                </div>
                <div class="modal-body">      
                    <div><?php echo Yii::t("newDS", "确认删除该学生吗？");?></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button" class="btn btn-primary" @click='delStu("hide")'><?php echo Yii::t("newDS", "Delete");?></button>
                </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
$(document).on('focusin', function(e) {
  if ($(e.target).closest(".tox-tinymce, .tox-tinymce-aux, .moxman-window, .tam-assetmanager-root").length) {
    e.stopImmediatePropagation();
  }
});
    $(document).ready(function () {
    // 通过该方法来为每次弹出的模态框设置最新的zIndex值，从而使最新的modal显示在最前面
        $(document).on('shown.bs.modal', '.modal.in', function(event) {
            var zIndex = 1040 + (10 * $('.modal:visible').length);
                $(this).css('z-index', zIndex);
                setTimeout(function() {
                    $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');
                }, 0);
        }).on('hidden.bs.modal', '.modal', function(event) {
            if ($('.modal.in').size() >= 1) {
                $('body').addClass('modal-open')
            }
        })
    });
    $(document).on('click', '#stuDetails a', function(event) {
        event.preventDefault();
        let tab = $(this).attr('data-tab');
        let status = $(this).attr('data-status');//0不可点击
        let chosen_name = $(this).attr('data-name');//选择的名字
        if (status != 0) { // 当 data-status 不等于 "0" 时执行
            $('#stuDetails').modal('hide');
            $('.modal-backdrop').remove(); // 隐藏后移除遮罩层
            stepInfoGlobal(tab, chosen_name, status);
        }
    });
    var coll_id='<?php echo Yii::app()->request->getParam('coll_id', ''); ?>'

    var  registrations =   new Vue({
        el: "#registration",
        data: {
            content_en:'',
            coll_id:coll_id,
            showList:{},
            childData:{},
            addStuList:{},
            yearData:{},
            yid:'',
            checkData:[],
            coll:{
                startdate:'',
                enddate:'',
                title_cn:'',
                title_en:''
            },
            config_data:{},
            currentStepConfig:'',
            collStepTemplateStyle: 'collStepConfigDefaultTemplate',
            isTinymce:true,
            parentEmail:'',
            copyParentEmail:'',
            sendAll:[],//发送全部人员
			sends:[],//可发送
            sends_length:0,
			sent:[],//已发送
			checked: true,
            sendArr:[],//全选
            noWechat:[],//未绑定微信
            step_html:'',
            type:'filled',
            stepName:'',
            WatermarkImg:'!v1000',
            closeindex:0,
            delList:{},
            configStepAll:[],
            new_info:[],
            qrcodeImg:'',
            qrcodeShow:false,
            addChildType:'',
            checkedAll:false,
            addStuGroup:{},
            Copynew_info:[],
            CopyconfigStepAll:[],
            addStuBtn:false,
            search_name:'',
            namesArray: [],
            handleFocus:false,
        },
        created: function() {
            window.stepInfoGlobal = this.stepInfo;
            let that = this
            $.ajax({
                url: '<?php echo $this->createUrlReg('addCollShow'); ?>',
                type: 'post',
                dataType: 'json',
                data: {
                },
                success: function(data) {
                    if(data.state == 'success') {
                        that.configStepAll=data.data.configStepAll
                        that.configStepAll.map((val,key)=>{ that.$set(val,'select',false) });
                        that.showData()
                    } else {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                },
                error: function(data) {
                    resultTip({
                        error: 'warning',
                        msg: '请求错误'
                    });
                }
            })
            $.ajax({
                url: '<?php echo $this->createUrlReg('getYearList'); ?>',
                type: 'post',
                dataType: 'json',
                data: {
                },
                success: function(data) {
                    if(data.state == 'success') {
                        that.yearData=data.data
                        that.addStuGroup['new']={}
                        for(var i=0;i<data.data.addStudentShowYearList.length;i++){
                            that.addStuGroup[data.data.addStudentShowYearList[i].yid+data.data.addStudentShowYearList[i].type]={}
                        }
                    } else {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                },
                error: function(data) {
                    resultTip({
                        error: 'warning',
                        msg: '请求错误'
                    });
                }
            })
            
        },
        computed: {},
        methods: {
            allStudent(){
                let that = this;
                var activeTab = $('.nav-tabs li.active a').attr('aria-controls');
                var type = activeTab === 'home' ? 'filled' : 'unfilled';
                that.namesArray = [];
                if(type === 'filled'){
                    if(that.showList.master.type == 1){
                        $('#filled tbody tr').each(function() {
                            var name = $(this).find('td:nth-child(2) a').text().trim();
                            that.namesArray.push({value:name});
                        });
                    }else{
                        $('#waitAudited tbody tr').each(function() {
                            var name = $(this).find('td:nth-child(2) a').text().trim();
                            that.namesArray.push({value:name});
                        });
                    }
                }else{
                    if(that.showList.master.type == 1){
                        $('#unfilled tbody tr').each(function() {
                            var name = $(this).find('td:nth-child(1) a').text().trim();
                            that.namesArray.push({value:name});
                        });
                    }else{
                        $('#audited tbody tr').each(function() {
                            var name = $(this).find('td:nth-child(2) a').text().trim();
                            that.namesArray.push({value:name});
                        });
                    }
                }
            },
            html(data){
              return  data.replace(/\n/g, '<br/>')
            },
            showData(){
                let that=this
                $.ajax({
					url: '<?php echo $this->createUrlReg('detailColl'); ?>',
					type: 'post',
					dataType: 'json',
					data: {
                        coll_id:this.coll_id
					},
					success: function(data) {
						if(data.state == 'success') {
							that.showList=data.data
                            that.new_info=data.data.step
                            that.Copynew_info=data.data.step
                            let newList = []; 
                            for(let len = 0; len<that.configStepAll.length;len++){
                                let item = that.configStepAll[len]
                                let isExist = false;
                                for(var j = 0; j < that.new_info.length; j++){
                                var flag = that.new_info[j].flag;
                                if(item.flag== flag){
                                    isExist = true;
                                    break;
                                }
                                }
                                if(!isExist){
                                    newList.push(item);
                                }
                            }
                            that.configStepAll=newList
                            that.CopyconfigStepAll=newList
                            that.yid=data.data.master.yid
                            that.coll=data.data.master
						} else {
							resultTip({
								error: 'warning',
								msg: data.message
							});
						}
					},
					error: function(data) {
						resultTip({
							error: 'warning',
							msg: '请求错误'
						});
					}
				})
            },
            push(){
                let that = this;
                let configStepAll = JSON.parse(JSON.stringify(that.configStepAll));
                configStepAll.forEach((item, index )=>{
                    if (item.select){
                        that.new_info = that.new_info.concat(item).sort((a,b)=>{ return a.flag - b.flag });
                        delete configStepAll[index];
                        item.select = false;
                    }
                })
                configStepAll = configStepAll.filter(function (val) { return val });
                that.configStepAll = configStepAll;
            },
            // 移除数据
            del(){
                let that = this;
                let info = JSON.parse(JSON.stringify(that.new_info)); // 拷贝原数据, 深拷贝
                info.forEach((item, index )=>{
                    if (item.select){
                        that.configStepAll = that.configStepAll.concat(item).sort((a,b)=>{ return a.id - b.id }); // 添加到新数据框, 排序
                        delete info[index];    // 删除数据
                        item.select = false;
                    }
                })
                info = info.filter(function (val) { return val }); // 过滤 undefined 
                that.new_info = info; // 更新原数据
            },
            editConfigure(){
                if(tinymce.editors.length){
                    tinymce.editors['tinymce'].destroy();
                    tinymce.editors['tinymceEn'].destroy();
                }
                this.stepConfig(this.showList.step[0])
                $('#editConfigure').modal('show')
            },
            stepConfig(list){
                this.currentStepConfig = list.flag
                this.collStepTemplateStyle = list.step_template

                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrlReg('editCollConfigShow'); ?>',
                    type: 'post',
                    dataType: 'json',
                    data: {
                        step_flag:this.currentStepConfig,
                        coll_id:this.coll_id,
                        style: this.collStepTemplateStyle,
                    },
                    success: function(data) {
                        if(data.state == 'success') {
                            $("#editConfigure").html(data.data);
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: '请求错误'
                        });
                    }
                })
            },
            delStu(list){
                if(list!='hide'){
                    this.delList=list
                    $('#delModal').modal('show')
                    return
                }
                let that=this
                $.ajax({
					url: '<?php echo $this->createUrlReg('delStudent'); ?>',
					type: 'post',
					dataType: 'json',
					data: {
                        id:this.delList.id
					},
					success: function(data) {
						if(data.state == 'success') {
                            resultTip({
								msg: data.state
							});
                            $('#delModal').modal('hide')
                            that.showData()

						} else {
							resultTip({
								error: 'warning',
								msg: data.message
							});
						}
					},
					error: function(data) {
						resultTip({
							error: 'warning',
							msg: '请求错误'
						});
					}
				})
                
            },
            addChild(obj){
                let that=this
                if(obj=='-1' && Object.keys(this.addStuGroup['new']).length!=0){
                    this.checkData= this.addStuGroup['new']
                    that.addChildType=-1
                    return
                }
                if(obj!='add' && obj!='-1' && Object.keys(this.addStuGroup[obj.yid+obj.type]).length!=0){
                    that.addChildType=obj.yid+obj.type
                    this.checkData= this.addStuGroup[obj.yid+obj.type]
                    return
                }
                if(obj=='add'){
                    for(var key in this.addStuGroup){
                        that.addStuGroup[key]={}
                    }
                }
                $.ajax({
					url: '<?php echo $this->createUrlReg('addStudentShow'); ?>',
					type: 'post',
					dataType: 'json',
					data: {
                        coll_id:this.coll_id,
                        yid:obj=='-1'?'-1':obj!=''?obj.yid:'',
                        student_type:obj!=''?obj.type:''
					},
					success: function(data) {
						if(data.state == 'success') {
							that.addStuList=data.data
                            that.checkedAll=false
                            var checkData=[]
                            for(var i=0;i<that.addStuList.class_list.length;i++){
                                checkData.push({
                                    title:that.addStuList.class_list[i].title,
                                    id:that.addStuList.class_list[i].classid,
                                    checked:false,
                                    stuList:that.addStuList.child_by_class[that.addStuList.class_list[i].classid],
                                    checkStu:[],
                                    selected:that.addStuList.child_by_class[that.addStuList.class_list[i].classid]?that.disabledClick(that.addStuList.child_by_class[that.addStuList.class_list[i].classid]):false
                                })
                            }
                            if(obj!='-1'){
                                that.addChildType=that.addStuList.yid+that.addStuList.student_type
                                that.addStuGroup[that.addChildType]=checkData
                            }else{
                                that.addChildType=-1
                                that.addStuGroup['new']=checkData
                            }
                            that.addStuGroup[that.addChildType]=checkData
                            that.checkData=checkData
                            $('#addChild').modal('show')
						} else {
							resultTip({
								error: 'warning',
								msg: data.message
							});
						}
					},
					error: function(data) {
						resultTip({
							error: 'warning',
							msg: '请求错误'
						});
					}
				})  
            },
            disabledClick(data){
                let dis=true
                data.forEach((item,index) => {
                   if(item.selected!=1){
                        dis=false
                        return
                   }
                })
                return dis
            },
            stuDetails(child_id){
                let that=this
                $.ajax({
					url: '<?php echo $this->createUrlReg('getStudentOverall'); ?>',
					type: 'post',
					dataType: 'json',
					data: {
                        coll_id:this.coll_id,
                        child_id: child_id,
					},
					success: function(data) {
						if(data.state == 'success') {
							that.childData=data.data
                            $('#stuDetailsHome').modal('show')
						} else {
							resultTip({
								error: 'warning',
								msg: data.message
							});
						}
					},
					error: function(data) {
						resultTip({
							error: 'warning',
							msg: '请求错误'
						});
					}
				})  
            },
            classChecked(){
                for(var j=0;j<this.checkData.length;j++){
                    let list=this.checkData[j]
                    if(this.checkedAll){
                        list.checked=true
                        for(var i=0;i<list.stuList.length;i++){
                            if(list.stuList[i].selected!=1){
                                list.checkStu.push(list.stuList[i].childid)
                            }
                        }
                    }else{
                        list.checked=false
                        list.checkStu=[]
                    }
                    
                }
            },
            allChecked(list){
                if(list.checked){
                    list.checkStu=[]
                    for(var i=0;i<list.stuList.length;i++){
                        if(list.stuList[i].selected!=1){
                            list.checkStu.push(list.stuList[i].childid)
                        }
                    }
                }else{
                    list.checkStu=[]
                }
            },
            saveAddStu(){
                var data={}
                for(var key in this.addStuGroup){
                    for(var j=0;j<this.addStuGroup[key].length;j++){
                        if(this.addStuGroup[key][j].checkStu.length!=0){
                            if(data[this.addStuGroup[key][j].id]){
                                data[this.addStuGroup[key][j].id]=data[this.addStuGroup[key][j].id].concat(this.addStuGroup[key][j].checkStu)
                            }else{
                                data[this.addStuGroup[key][j].id]=[]
                                data[this.addStuGroup[key][j].id]=this.addStuGroup[key][j].checkStu
                            }
                        }
                    }
                   
                }
                let that=this
                that.addStuBtn=true
                $.ajax({
					url: '<?php echo $this->createUrlReg('addStudentOpe'); ?>',
					type: 'post',
					dataType: 'json',
					data: {
                        coll_id:this.coll_id,
                        data: data,
					},
					success: function(data) {
						if(data.state == 'success') {
                            resultTip({
								msg: data.state
							});
                            that.addStuBtn=false
                            that.showData()
                            $('#addChild').modal('hide')
						} else {
							resultTip({
								error: 'warning',
								msg: data.message
							});
                            that.addStuBtn=false
						}
					},
					error: function(data) {
						resultTip({
							error: 'warning',
							msg: '请求错误'
						});
                        that.addStuBtn=false
					}
				})  
            },
            editInfo(){
                let that=this
                setTimeout(function() {
                    that.$nextTick(()=>{
                        $("#startDate").datepicker({
                            dateFormat: "yy-mm-dd ",
                        });
                        $("#endDate").datepicker({
                            dateFormat: "yy-mm-dd ",
                        });
                    })
                }, 500);
                $('#editInfo').modal('show')
            },
            publish_atVal(){
                let that=this
                setTimeout(function() {
                    that.$nextTick(()=>{
                        that.coll.startdate = $('#startDate').val();
                    })
                }, 500);
            },
            expired_atVal(){
                let that=this
                setTimeout(function() {
                    that.$nextTick(()=>{
                        that.coll.enddate = $('#endDate').val();
                    })
                }, 500);
            },
            handleDragStart(e, item) {
                this.dragging = item;
            },
            handleDragEnd(e, item) {
                this.dragging = null
                var fileList=[];
                this.new_info.forEach((item,index) => {
                    fileList.push(item)
                    Vue.set(item, 'dragStatus', false);
                })
                this.new_info = fileList
            },
            handleDragOver(e) {
                e.dataTransfer.dropEffect = 'move' // e.dataTransfer.dropEffect="move";//在dragenter中针对放置目标来设置!
            },
            handleDragEnter(e, item) {
                e.dataTransfer.effectAllowed = "move" //为需要移动的元素设置dragstart事件
                if(item === this.dragging) {
                    return
                }
                const newItems = [...this.new_info]
                const src = newItems.indexOf(this.dragging)
                const dst = newItems.indexOf(item)
                newItems.splice(dst, 0, ...newItems.splice(src, 1))
                this.new_info = newItems
                this.new_info.forEach((item,index) => {
                    Vue.set(item, 'dragStatus', false);
                })
                Vue.set(this.new_info[dst], 'dragStatus', true);
            },
            saveInfo(){
                let that=this
                var coll_step=[] 
                for(var i=0;i<this.new_info.length;i++){
                    coll_step.push(this.new_info[i].flag)
                }
                that.addStuBtn=true
                $.ajax({
					url: '<?php echo $this->createUrlReg('editColl'); ?>',
					type: 'post',
					dataType: 'json',
					data: {
                        coll_id:this.coll_id,
                        "coll[startdate]":this.coll.startdate,
                        "coll[enddate]": this.coll.enddate,
                        "coll[title_cn]": this.coll.title_cn,
                        "coll[title_en]": this.coll.title_en,
                        "coll[desc_cn]":this.coll.desc_cn,
                        "coll[desc_en]":this.coll.desc_en,
                        "coll[sub_success_cn]": this.coll.sub_success_cn,
                        "coll[sub_success_en]": this.coll.sub_success_en,
                        "coll[audit_success_cn]": this.coll.audit_success_cn,
                        "coll[audit_success_en]": this.coll.audit_success_en,
                        coll_step:coll_step,
                        "coll[type]": this.coll.type
					},
					success: function(data) {
						if(data.state == 'success') {
							resultTip({
								msg: data.state
							});
                            that.addStuBtn=false
                            that.showData()
                            $('#editInfo').modal('hide')
						} else {
							resultTip({
								error: 'warning',
								msg: data.message
							});
                            that.addStuBtn=false
                            that.new_info=that.Copynew_info
                            that.configStepAll=that.CopyconfigStepAll
						}
					},
					error: function(data) {
                        that.addStuBtn=false
						resultTip({
							error: 'warning',
							msg: '请求错误'
						});
					}
				})  
            },
            send(){
                let that=this
				that.sends=[]
				that.sendArr=[]
				that.checkedNames=[]
				that.sent=[]
				that.noWechat=[]
				$.ajax({
			        url:'<?php echo $this->createUrlReg('taskData'); ?>',
			        type: 'post',
			        dataType:'json',
			        data:{
                        id:this.coll_id,
                    },
			        success:function(data){
			        	that.sendAll=data.data.children
                        that.sends_length = 0
		        		$('#release').modal('show')
		        		for(var i=0;i<data.data.children.length;i++){
		        			if(!data.data.expired[data.data.children[i].id]){
		        				if(data.data.children[i].bindingStatus==1){
                                    that.sends_length++
		        					that.sends.push(data.data.children[i])
		        					that.sendArr.push(data.data.children[i].id)
		        					that.checkedNames.push(data.data.children[i].id)
		        				}else{
		        					that.noWechat.push(data.data.children[i])
		        				}
		        			}else{
		        				that.sent.push(data.data.children[i])
		        			}
		        		}
			        },error:function(data){
			             resultTip({error: 'warning', msg: '请求错误'});
			        }
			    })
			},
            email(type){
                let that=this
				$.ajax({
			        url:'<?php echo $this->createUrlReg('getChildsParentEmail'); ?>',
			        type: 'post',
			        dataType:'json',
			        data:{
			        	id:this.coll_id,
                        type:type
			        },
			        success:function(data){
			        	if(data.state=="success"){
			        		that.parentEmail=data.data
			        		that.copyParentEmail=data.data
			        		$('#emailData').modal('show')
			        	}else{
                            resultTip({error: 'warning', msg: data.message});
                        }
			        },error:function(data){
			             resultTip({error: 'warning', msg: '请求错误'});
			        }
			    })
			},
            updateEmailText(data){
                if(data == 1){
                    re=new RegExp(",","g");
                    var newstart=this.copyParentEmail.replace(re,";");
                }else{
                    re=new RegExp(";","g");
                    var newstart=this.copyParentEmail.replace(re,",");
                }
                this.parentEmail=newstart
            },
            cancelStep(){
                $('.stepInfo').html('')
                $(".collDetail").show();
                $(".search").hide();
                $('.cancelStep').hide()
                this.stepName=''
                sessionStorage.setItem('chosen_name', '');
            },
            stepInfo(stepId,chosen_name,status){
                console.log('---',chosen_name,'====')
                this.stepName=stepId
                this.search_name = ''
                if(chosen_name){
                    this.search_name = chosen_name
                }
                var master_type = 'filled'
                if(this.showList.master.type == 2){
                    //4审核完成  1等待审核
                    if( status == 4 || status == 3){
                         master_type = 'audited'
                    }else{
                         master_type = 'waitAudited'
                    }
                }
                $.ajax({
                    url:'<?php echo $this->createUrlReg('stepTemplate'); ?>',
                    type: 'get',
                    dataType:'json',
                    data:{
                        coll_id:this.coll_id,
                        step_id:stepId,
                        // type:this.type,
                        type:master_type,
                        type_status:status,
                    },
                    success:function(data){
                        if(data.state=="success"){
                            $('.stepInfo').html(data.data)
                            $(".collDetail").hide();
                            $(".search").show();
                            $('.cancelStep').show();
                        }
                        if(chosen_name){
                            sessionStorage.setItem('chosen_name',chosen_name)
                        }else{
                            sessionStorage.setItem('chosen_name','')
                        }
                        $('#stuDetailsHome').modal('hide')
                    },
                    error:function(data){
                        resultTip({error: 'warning', msg: '请求错误'});
                    }
                })
            },
            changeAllChecked(){
                var self=this;
                if (!self.checked) {
                    self.checkedNames=[]
                } else {
                    self.checkedNames=self.sendArr;
                }
            },
            sendChild(){
                read=''
                var chaildfiler=[]
                $.each($('input[name="send"]:checked'),function(){
                    chaildfiler.push($(this).val());
                })
                if(chaildfiler.length==''){
                    resultTip({msg:'<?php echo Yii::t('teaching', 'Please select')?>', error: 1});
                }else{
                    this.sendAjaxS(chaildfiler);
                }
            },
            sendAjaxS(id) {
                let that=this;
                var index = 0;
                var result = [];
                var childindex='';
                var sendsucc=[]
                var successData=''
                sendAjax(index);
                function sendAjax(index) {
                    that.closeindex=1
                    $('.sendbtn').attr("disabled", true);
                    $('#text'+id[index]).css('visibility','visible')
                    $('#text'+id[index]).html('<?php echo Yii::t('teaching', 'Sending...')?>')
                    if(childindex!=''){
                        var html=''
                        for(var i=0;i<that.sendAll.length;i++){
                            if(childindex==that.sendAll[i].id){
                                $('#tr'+childindex).fadeToggle(1000)
                                that.sent.unshift(that.sendAll[i])
                                that.sends_length--
                            }
                        }
                    }
                    if(index >= id.length) {
                        that.doSomething(id,result);
                        return;
                    }
                    if(read=='close'){
                        that.colsesend(sendsucc)
                        return;
                    }
                    $.ajax({
                        url: '<?php echo $this->createUrlReg("SendCollWxMsg")?>',
                        type: "POST",
                        async: true,
                        dataType: 'json',
                        data: {
                            'child_id': id[index],
                            'coll_id': coll_id,
                        },
                        success: function(data) {
                            if(data.state == 'success') {
                                result.push(data);
                                sendsucc.push(id[index])
                                $('#check'+id[index]).find('div').remove()
                                $('#text'+id[index]).html(' <span class="glyphicon glyphicon-ok" aria-hidden="true"></span> <?php echo Yii::t('teaching', 'Sent')?>')
                                childindex=id[index]
                                successData=data
                                setTimeout(function(){
                                    index++;
                                    sendAjax(index);
                                }, 1000);
                            } else {
                                $('#'+id[index]).html(data.message)
                                that.closeindex=0
                                $('.sendbtn').attr("disabled", true);
                                resultTip({msg: data.message, error: 1});
                            }
                        },
                        error: function() {
                            alert("请求错误")
                        }
                    });
                }
            },
            doSomething(id,data) {
                this.closeindex=0
                $('.sendbtn').attr("disabled", false);
            },
            colsesend(sendsucc){
                this.closeindex=0
                $('.sendbtn').attr("disabled", false);
            },
            closesend(){
                if(this.closeindex==0){
                    $('#release').modal('hide');
                }else{
                    var con;
                    con=confirm("<?php echo Yii::t('teaching', 'Sending task is not completed, are you sure to stop it?')?>"); //在页面上弹出对话框
                    if(con){
                        $('#release').modal('hide');
                        read='close'
                    }
                }
            },
            getQRCode(){
                let that=this
                $.ajax({
                    type: "POST",
                    url: "<?php echo $this->createUrlReg('getQRCode')?>",
                    data:{
                        coll_id:this.coll_id,
                    },
                    dataType: "json",
                    success: function(data){
                        if(data.state=='success'){
                            that.qrcodeImg=data.data
                            that.qrcodeShow=true
                        }
                        that.disabled=false
                    },
                    error:function(){
                        that.disabled=false
                    }
                });
            }
        }
    })
    // var container = registrations;
    function overalls(obj){
        var id=$(obj).attr('data-id')
        var name=$(obj).attr('data-name')
        var class_title=$(obj).attr('data-class')
        registrations.name=name
        registrations.class_title=class_title
        $.ajax({
            url: "<?php echo $this->createUrlReg('overall')?>",
            type: 'get',
            dataType: 'json',
            data: {
                childid:id
            },
            success: function(data) {
                if(data.state == "success") {
                    registrations.getOverAll=data.data
                    $('#overallModel').modal('show')
                } else {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                }
            },
            error: function(data) {
                alert('请求错误')
            }
        })
    }
</script>
