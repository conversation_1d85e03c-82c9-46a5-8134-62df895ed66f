<?php
$type = ChildVacation::getConfig();
$type['40'] = Yii::t('campus','Tardy');
?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Basic'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site','Student Attendance Management'), array('//mcampus/childsign/index'))?></li>
        <li class="active"><?php echo Yii::t('campus', '审核列表'); ?></li>
    </ol>

    <div class="row">
        <!-- 左侧菜单 -->
        <div class="col-md-2">
            <div class="list-group" id="classroom-status-list">
                <a href="<?php echo $this->createUrl('index'); ?>"
                   class="list-group-item status-filter"><?php echo Yii::t('campus', 'Student Attendance'); ?></a>
                <a href="<?php echo $this->createUrl('vacation'); ?>"
                   class="list-group-item status-filter"><?php echo Yii::t('campus', 'List of students on leave/tardy'); ?></a>
                <a href="<?php echo $this->createUrl('check'); ?>"
                   class="list-group-item status-filter active"><?php echo Yii::t('campus', '审核列表'); ?></a></div>
        </div>

        <div class="col-md-10 col-sm-12">
            <div class="panel panel-default">
                <div class="panel-body">

                    <?php
                    $this->widget('ext.ivyCGridView.BsCGridView', array(
                        'id'=>'check_child_vacation',
                        'dataProvider'=>$dataProvider,
                        'afterAjaxUpdate'=>'js:function(){head.Util.ajaxDel()}',
                        'colgroups'=>array(
                            array(
                                "colwidth" => array(100, 100, 100, 100, 100, 100, 100, 100, 100),
                            )
                        ),
                        'columns' => array(
                            array(
                                'name' => Yii::t('ivyer', 'child_id'),
                                'value' => '$data->childProfile->getChildName()',
                            ),
                            array(
                                'name' => Yii::t('ivyer', 'class_id'),
                                'value' => 'Yii::app()->controller->class_list[$data->class_id]',
                                //'value' => array($this, 'getClassid'),
                            ),
                            array(
                                'name' => Yii::t('ivyer', 'type'),
                                'value' => array($this, 'status'),
                            ),
                            array(
                                'name' => Yii::t('ivyer', 'vacation_time_start'),
                                'value' => 'date("Y-m-d", $data->vacation_time_start)',
                            ),
                            array(
                                'name' => Yii::t('ivyer', 'vacation_time_end'),
                                'value' => 'date("Y-m-d", $data->vacation_time_end)',
                            ),
                            array(
                                'name' => Yii::t('labels','Memo'),
                                'value' => '$data->vacation_reason',
                            ),
                            array(
                                'name' => Yii::t('ivyer', 'uid'),
                                'value' => '$data->user->getName()',
                            ),
                            array(
                                'name' => Yii::t('ivyer', 'updata_time'),
                                'value' => 'date("Y-m-d H:i:s", $data->updata_time)',
                            ),
                            array(
                                'name' => Yii::t('global', 'Action'),
                                'type' => 'raw',
                                'value' => array($this, 'getCheckButton'),
                            ),
                        ),
                    ));
                    ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    var modal = '<div class="modal fade" id="modal" class="1123" tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog" role="document"><div class="modal-content"></div></div></div>';
    $('body').append(modal);

    function check (id,stat) {
        $.ajax({
            url:'<?php echo $this->createUrl('checkVacation');?>',
            data:{id: id,stat:stat},
            type:'post',
            dataType:'json',
            success:function (data) {
                if (data.state == 'success') {
                    cbcheck();
                }else{
                    resultTip({msg: data.message, error: 'warning'});
                }
            }
        });
    }

    function cbcheck() {
        $.fn.yiiGridView.update('check_child_vacation');
    }
    function cbSuccess() {
        $('#modal').modal('hide');
        $.fn.yiiGridView.update('check_child_vacation');
    }
</script>