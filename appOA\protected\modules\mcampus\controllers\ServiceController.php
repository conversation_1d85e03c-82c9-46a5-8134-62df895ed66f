<?php

class ServiceController extends BranchBasedController
{
    public $batchNum = 50;
    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public $actionAccessAuths = array(
        'Index' => 'o_A_Adm_Support',
        'Delete' => 'o_A_Adm_Support',
        'New' => 'o_A_Adm_Support',
    );


    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Campus Workspace');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mcampus/service/index');
        // 引入资源文件
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl() . '/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/vue2.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/xlsx.full.min.js');
        // 引入model文件
        Yii::import('common.models.service.*');
    }

    public function actionIndex()
    {
        $cs = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/datatables/jquery.dataTables.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/datatables/dataTables.bootstrap.min.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/datatables/dataTables.bootstrap.min.css');

        $schoolService = Service::getSchoolService($this->branchId);
        $serviceInfo = Service::getServiceInfo();


        $page = Yii::app()->request->getParam('page', 1);

        // 读取该校园 Service 配置
        $config = array();
        foreach ($schoolService as $serviceId) {
            $info = $serviceInfo[$serviceId];
            $isManager = in_array($this->staff->uid, $info['manager']);
            $config[$serviceId] = array(
                'serviceId' => $serviceId,
                'isManager' => $isManager,
                'title' => $info['title'],
                'requestFields' => $info['requestFields'],
                'expenseFields' => $info['expenseFields'],
            );
        }

        // 读取校园所有进行中的数据
        $data = array();
        $offset = ($page - 1) * $this->batchNum;

        $criteria = new CDbCriteria;
        $criteria->compare('school_id', $this->branchId);
        $criteria->compare('status', array(1, 2));
        $criteria->limit = $this->batchNum;    //取1条数据，如果小于0，则不作处理
        $criteria->offset = $offset;   //两条合并起来，则表示 limit 10 offset 1,或者代表了。limit 1,10

        $criteria->order = "status ASC, start_time ASC";
        $model = Service::model()->findAll($criteria);
        if ($model) {
            foreach ($model as $val) {
                $serviceField = array();
                if ($val->serviceField) {
                    foreach ($val->serviceField as $item) {
                        $fileld_content = $item->fileld_content;
                        if ($serviceInfo[$val->service_id]['requestFields'][$item->fileld]['type'] == 'timestamp') {
                            $fileld_content = date("Y-m-d", $fileld_content);
                        }
                        $serviceField[$item->fileld] = $fileld_content;
                    }
                }
                $serviceExpense = array();
                if ($val->serviceExpense) {
                    foreach ($val->serviceExpense as $item) {
                        $expenseFileldContent = $item->fileld_content;
                        if (in_array($item->fileld, array(1, 2, 3))) {
                            $expenseFileldContent = sprintf("%.2f", $item->fileld_content);
                        }
                        $serviceExpense[$item->fileld] = $expenseFileldContent;
                    }
                }
                $data[$val->id] = array(
                    'id' => $val->id,
                    'service_id' => $val->service_id,
                    'service_title' => $val->service_title,
                    'start_time' => date('Y-m-d', $val->start_time),
                    'end_time' => date('Y-m-d', $val->end_time),
                    'status' => $val->status,
                    'serviceField' => $serviceField,  // 校园提交得服务数据
                    'serviceExpense' => $serviceExpense, // 负责人提交得报销数据
                );
            }
        }

        $criteria = new CDbCriteria;
        $criteria->compare('school_id', $this->branchId);
        $criteria->compare('status', array(1, 2));
        $total = Service::model()->count($criteria);
        $totalPages = ($total) ? ceil($total / $this->batchNum) : 0;

        $this->render('index', array(
            'config' => $config,
            'data' => $data,
            'totalPages' => $totalPages,
            'page' => $page,
        ));
    }

    /**
     * 提起服务
     *
     * @return void
     */
    public function actionNew()
    {
        $id = Yii::app()->request->getParam('id', '');
        $service_id = Yii::app()->request->getParam('service_id', '');
        $service_title = Yii::app()->request->getParam('service_title', '');
        $service_data = Yii::app()->request->getParam('service_data', array());

        $transaction = Yii::app()->subdb->beginTransaction();
        $serviceInfo = Service::getServiceInfo();
        try {
            $config = $serviceInfo[$service_id]['requestFields'];

            $judge = Service::judge($config, $service_data, $serviceInfo[$service_id]['manager']);
            if (!$judge['status']) {
                $transaction->rollback();
                $this->addMessage('state', 'fail');
                $this->addMessage('message', $judge['message']);
                $this->showMessage();
            }

            $model = new Service();
            $model->created_by = $this->staff->uid;
            $model->created_at = time();
            $model->school_id = $this->branchId;
            $model->service_id = $service_id;
            $model->start_time = strtotime($service_data[1]);
            $model->end_time = strtotime($service_data[2]);
            $model->service_id = $service_id;
            $model->status = 1;
            $model->vendor_status = 0;
            $model->service_title = $service_title;
            $model->updated_by = $this->staff->uid;
            $model->updated_at = time();

            if (!$model->save()) {
                $transaction->rollback();
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err ? $err[0] : '失败');
                $this->showMessage();
            }

            if ($model->start_time > $model->end_time) {
                $transaction->rollback();
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '开始时间不得大于结束时间');
                $this->showMessage();
            }

            $serviceField = array();
            foreach ($service_data as $key => $val) {
                $configStatus = $serviceInfo[$service_id]['requestFields'][$key];
                $ruleJudge = Service::ruleJudge($configStatus, $val);
                if (!$ruleJudge['status']) {
                    $transaction->rollback();
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', $ruleJudge['message']);
                    $this->showMessage();
                }

                $modelServiceField = new ServiceField();
                $modelServiceField->sid = $model->id;
                $modelServiceField->fileld = $key;
                $modelServiceField->fileld_content = $ruleJudge['date'];
                if (!$modelServiceField->save()) {
                    $transaction->rollback();
                    $this->addMessage('state', 'fail');
                    $err = current($modelServiceField->getErrors());
                    $this->addMessage('message', $err ? $err[0] : '失败');
                    $this->showMessage();
                }
                $serviceField[$modelServiceField->fileld] = $modelServiceField->fileld_content;
            }

            $data = array(
                'id' => $model->id,
                'service_id' => $model->service_id,
                'service_title' => $model->service_title,
                'start_time' => date('Y-m-d', $model->start_time),
                'end_time' => date('Y-m-d', $model->end_time),
                'status' => $model->status,
                'serviceField' => $serviceField,  // 校园提交得服务数据
                'serviceExpense' => array(), // 负责人提交得报销数据
            );

            $transaction->commit();
            $this->addMessage('state', 'success');
            $this->addMessage('data', $data);
            $this->addMessage('message', Yii::t('message', 'success'));
            $this->showMessage();
        } catch (Exception $e) {
            $transaction->rollBack();
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '操作错误');
            $this->showMessage();
        }
    }

    /**
     * 删除服务
     *
     * @return void
     */
    public function actionDelete()
    {
        $service_id = Yii::app()->request->getParam('service_id', '');

        $model = Service::model()->findByPk($service_id);
        if ($model && !$model->vendor_status && $model->status == 1) {
            $model->status = 0;
            $model->updated_by = $this->staff->uid;
            $model->updated_at = time();
            if (!$model->save()) {
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err ? $err[0] : '失败');
                $this->showMessage();
            }
            $this->addMessage('state', 'success');
            $this->addMessage('callback', 'refreshData');
            $this->addMessage('data', $service_id);
            $this->addMessage('message', '成功');
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '数据错误');
            $this->showMessage();
        }
    }
}
