<?php
$crit = new CDbCriteria;
$crit->index = 'branchid';
$crit->compare('type', '<>'.Branch::TYPE_OFFICE);
$crit->compare('status', Branch::STATUS_ACTIVE);
$crit->order = '`group` ASC';
$branches = Branch::model()->findAll($crit);
?>

<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','HQ Operations'), array('/moperation'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Basic Configurations'), array('/moperation'))?></li>
        <li class="active"><?php echo Yii::t('site','Discount Management') ?></li>
    </ol>

    <div class="row">
        <div class="col-md-1 col-sm-2">
            <?php
            $mainMenu = array(
                array('label'=>Yii::t('user','校园折扣'), 'url'=>array("/moperation/global/discount")),
                array('label'=>Yii::t('user','折扣类别'), 'url'=>array("/moperation/global/discountCate")),
            );

            $this->widget('zii.widgets.CMenu',array(
                'items'=> $mainMenu,
                'id'=>'pageCategory',
                'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked text-right background-gray'),
                'activeCssClass'=>'active',
                'itemCssClass'=>''
            ));
            ?>
        </div>
        <div class="col-md-1 col-sm-2">
            <div class="list-group" id="campus-list">
                <?php
                foreach($branches as $branch):
                    $_title = '<span class="glyphicon glyphicon-tags program'.$branch->group.'"></span> ';
                    $_title .= $branch->abb;
                    echo CHtml::link($_title,'javascript:void(0)', array('branchid'=>$branch->branchid,'class'=>'list-group-item'));
                endforeach;
                ?>
            </div>
        </div>
        <div class="col-md-10 col-sm-8" id="discount-display-box">
            <div class="row">
                <div class="col-md-12">
                    <div class="panel panel-default" id="branch-discount-box">
                        <!-- Default panel contents -->
                        <div class="panel-heading">
                        </div>
                        <div class="panel-body">
                            <dl class="dl-horizontal">
                                <dt>100% ~ 95%（含）</dt>
                                <dd>园长折扣范围</dd>
                            </dl>
                            <dl class="dl-horizontal">
                                <dt>85%（含） ~ 95%</dt>
                                <dd>项目负责人折扣范围</dd>
                            </dl>
                            <dl class="dl-horizontal">
                                <dt>0 ~ 85%</dt>
                                <dd>CEO折扣范围</dd>
                            </dl>
                            <dl class="dl-horizontal">
                                <dt>其他</dt>
                                <dd>收退费政策明确规定的折扣由园长审批</dd>
                            </dl>
                            <dl class="dl-horizontal">
                                <dt>
                                    <?php echo CHtml::link(
                                        '<span class="glyphicon glyphicon-plus"></span> 添加折扣',
                                        array('addDiscount'),
                                        array('class'=>'btn btn-primary J_dialog', 'id'=>'J_add')
                                    )?>
                                </dt>
                                <dd></dd>
                            </dl>
                        </div>

                        <!-- Table -->
                        <table class="table table-hover" id="discount-table">
                            <thead>
                            <tr class="active">
                                <th width="60">#</th>
                                <th width="120" class="text-center">Discount</th>
                                <th width="260" class="text-center">Title</th>
                                <th width="260" class="text-center">需审核</th>
                                <th width="60" class="text-center">可绑定</th>
                                <th width="100" class="text-center">有效期</th>
                                <th>操作</th>
                            </tr>
                            </thead>
                            <tbody id="discount-group-1">
                            </tbody>
                            <tbody id="discount-group-2">
                            </tbody>
                            <tbody id="discount-group-3">
                            </tbody>
                            <tbody id="discount-group-4">
                            </tbody>
                        </table>
                        <div class="p15 show-more-box">
                            <a class="btn btn-info btn-xs" onclick="$('#discount-group-4').toggle();" href="javascript:void(0)" role="button"></a>
                        </div>

                    </div>
                </div>
            </div>
        </div>

    </div>

</div>
<script type="text/template" id="discount-item-template">
    <tr>
        <td><%= info.id %></td>
        <td class="text-center"><%= info.discount %></td>
        <td class="text-center"><%= title.title_en %><br><%= title.title_cn %></td>
        <td class="text-center"><% if(approver.uid > 0) print(approver.name); %></td>
        <td class="text-center"><% if(info.allow_bind2child > 0) print('<span class="glyphicon glyphicon-ok"></span>'); %></td>
        <td class="text-center"><%= expire %></td>
        <td>
            <a class="J_dialog btn btn-info btn-xs" title="编辑" href="<?php echo $this->createUrl('addDiscount');?>?id=<%= info.id%>&schoolid=<%= info.schoolid%>" role="button"><i class="glyphicon glyphicon-pencil"></i></a>
        </td>
    </tr>
</script>
<script>
    var thisDay = <?php echo time();?>;
    var branchData={};
    var template = _.template($('#discount-item-template').html());
    var disabledCount; //已经无效的折扣
    $(function(){
        $('#discount-display-box').hide();

        showBranchDiscount=function(obj){
            $('#J_add').removeClass('J_dialog');
            $('#campus-list a').removeClass('background-gray');
            $(obj).addClass('background-gray');
            $('#discount-display-box').show();
            disabledCount = 0;
            $('#branch-discount-box .panel-heading').html($(obj).html()).append(' 折扣列表');
            var branchId = $(obj).attr('branchid');
            $('#J_add').attr('href', '<?php echo $this->createUrl('addDiscount')?>?schoolid='+branchId).attr('title', $(obj).text()+' 添加折扣');
            if(_.isUndefined(branchData[branchId])){
                loadDiscounts(branchId);
            }
            $('#discount-table tbody').empty();
            for(var i=0; i<branchData[branchId].length; i++){
                var _discount = parseFloat(branchData[branchId][i]['info']['discount']);
                var _group = ( branchData[branchId][i]['info']['stat'] != 10 || (branchData[branchId][i]['info']['expire_date'] < thisDay) ) ? '4' :
                    (_discount>=95)?'1':((_discount>=85)? '2': '3');
                branchData[branchId][i]['info']['active'] = true;
                if(_group == '4'){
                    branchData[branchId][i]['info']['active'] = false;
                    disabledCount++;
                }
                branchData[branchId][i]['info']['active'] = (_group == '4') ? false : true;
                var view = template(branchData[branchId][i]);
                $('#discount-group-'+_group).append(view);
            }
            $('#discount-group-4').hide();
            if(parseInt(disabledCount)==0){
                $('.show-more-box').hide();
            }else{
                $('.show-more-box').show();
                $('div.show-more-box > a.btn').html('显示已失效折扣（'+disabledCount+'）');
                $('#discount-group-4 td').addClass('text-muted');
            }
            head.Util.aDialog();
        };

        loadDiscounts=function(branchId){
            delete branchData[branchId];
            $.ajax({
                type: "GET",
                dataType: "json",
                async: false,
                url: "<?php echo $this->createUrl('//moperation/global/getDiscounts');?>",
                data: { branchId: branchId}
            }).done(function(data) {
                if(data.state == 'success'){
                    branchData[branchId] = data.data;
                }
            });
        }

        $('#campus-list a').click(function(){showBranchDiscount(this)});

    });
    function redis(data)
    {
        delete branchData[data.branchid];
        $('#campus-list a[branchid="'+data.branchid+'"]').click();
    }
</script>