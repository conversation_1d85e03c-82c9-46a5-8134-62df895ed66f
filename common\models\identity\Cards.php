<?php

/**
 * This is the model class for table "ivy_cards".
 *
 * The followings are the available columns in table 'ivy_cards':
 * @property integer $id
 * @property string $cid
 * @property integer $childid
 * @property string $data
 * @property integer $sign
 * @property integer $updated
 * @property string $schoolid
 */
class Cards extends CActiveRecord
{
    const SIGN_DEFAULT = 0;
    const SIGN_INVALID = 1;
    const SIGN_EXPIRED = 2;
    const SIGN_SPECIAL = 3;
    const SIGN_WAIT = 4;
    public $campus;
    public $child;
    public $class;
    public $name;
    public $tel;
    public $relation;
    public $expire;
    public $uploadFile;
    public $updatePhoto;
    public $showPhoto;
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_cards';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('childid, data, updated, schoolid,', 'required'),
			array('childid, sign, updated', 'numerical', 'integerOnly'=>true),
			array('cid, schoolid', 'length', 'max'=>255),
            array("uploadFile","file", "types" => "jpg, gif, png, jpeg", "maxSize" => 1024*1024*2, "allowEmpty" => true, 'tooLarge' => '图片大小不能超过 2MB ！'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, cid, childid, data, sign, campus, updated, schoolid, uploadFile', 'safe', 'on'=>'search'),
		);
	}

    protected function beforeSave()
    {
        if (parent::beforeSave()) {
            if($file = $this->uploadFile){
                $picPath = Yii::app()->params['OAUploadBasePath'] . '/crads/';
                if (!is_dir($picPath)) {
                    mkdir($picPath, 0777);
                }
                $data = CJSON::decode($this->data);
                if(file_exists($picPath . $data["photo"])){
                    unlink($picPath . $data["photo"]);
                }
                $picNames = $this->id . '.' . $file->getExtensionName();
                if($file->saveAs($picPath . $picNames)){
                    Yii::import('application.extensions.image.Image');
                    $image = new Image($picPath . $picNames);
                    $image->quality(100);
                    $image->resize(600,600);
                    $image->save( $picPath . $picNames);
                    $data['photo'] = $picNames;
                    $this->data = CJSON::encode($data);
                }
            }
            return true;
        }
        return false;
    }
	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'cid' => Yii::t('site','Card ID'),
			'childid' => Yii::t('site','Stduent name'),
			'data' => Yii::t('asa','数据'),
			'sign' => Yii::t('asa','Status'),
			'updated' => Yii::t('site','Date added'),
			'schoolid' => Yii::t('asa','学校'),
			'uploadFile' => Yii::t('asa','头像'),
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('cid',$this->cid,true);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('data',$this->data,true);
		$criteria->compare('sign',$this->sign);
		$criteria->compare('updated',$this->updated);
		$criteria->compare('schoolid',$this->schoolid,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return Cards the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	public static function getConfig()
	{
		return  array(
            '0' => '正常',
            '1' => '过期卡',
            '2' => '损坏卡',
            '3' => '遗失卡',
            '4' => '待制作',
		);
	}
}
