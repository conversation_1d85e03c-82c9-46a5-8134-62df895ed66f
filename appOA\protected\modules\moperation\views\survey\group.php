<style>
    [v-cloak] {
        display: none;
    }
    .item {
        transition: all linear .3s;
        cursor:pointer;
    }
    </style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li>
            <?php echo CHtml::link(Yii::t('site','HQ Operations'), array('/moperation'))?>
        </li>
        <li>
            <?php echo CHtml::link(Yii::t('site','问卷管理'), array('option'))?>
        </li>
        <li class="active">
            <?php echo Yii::t('site','选项管理') ?>
        </li>
    </ol>

    <div class="row">
        <div class="col-md-2 col-sm-2">
            <?php

            $this->widget('zii.widgets.CMenu',array(
                'items'=> $this->getSubMenu(),
                'id'=>'pageCategory',
                'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked text-left background-gray'),
                'activeCssClass'=>'active',
                'itemCssClass'=>''
            ));
            ?>
        </div>
        <div class=""  id='group'  v-cloak>
            <div class="col-md-5 col-sm-5">
                <div class="panel panel-default">
                    <!-- Default panel contents -->
                    <div class="panel-heading">选项组 
                        <a href="javascript:;" class="btn btn-xs btn-primary pull-right" @click='addOptionGroup(0)'><span class="glyphicon glyphicon-plus"></span> 添加</a>
                    </div>
                    
                    <table class="table">
                        <tbody>
                            <tr @click='groupopt(group.id,index,group.option_cat)' :class="activeClass == index ? 'warning':''"  v-for='(group,index) in grouplist'>
                                <td width="">
                                    <input type="checkbox" disabled="true"  v-if='group.option_cat==2'>
                                    <input type="radio" disabled="true"  v-if='group.option_cat==1'>
                                </td>
                                <td width="88%">
                                    {{group.title_cn}}（{{group.title_en}}）
                                </td>
                                <td width="12%"  @click.stop>
                                    <a href="javascript:;" class="btn btn-xs btn-primary" @click='addOptionGroup(group)'><span class="glyphicon glyphicon-pencil"></span></a>
                                    <a href="javascript:;" class="btn btn-xs btn-danger" @click='removeOptionGroup(group.id,index)'><span class="glyphicon glyphicon-trash"></span></a>
                                  
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <!-- List group -->
                   <!--  <ul class="list-group">
                        <li class="list-group-item" @click='groupopt(group.id,index,group.option_cat)' :class="activeClass == index ? 'active':''"  v-for='(group,index) in grouplist'>
                            <label class="checkbox-inline col-md-10" v-if='group.option_cat==2'>
                                <input type="checkbox" disabled="true">
                                {{group.title_cn}}（{{group.title_en}}）
                            </label>
                            <label class="radio-inline col-md-10" v-if='group.option_cat==1'>
                                <input type="radio" disabled="true">{{group.title_cn}}（{{group.title_en}}）
                            </label>
                            <div class="pull-right col-md-2"  @click.stop>
                                <a href="javascript:;" class="btn btn-xs btn-danger pull-right ml5" @click='removeOptionGroup(group.id,index)'><span class="glyphicon glyphicon-trash"></span></a>
                                <a href="javascript:;" class="btn btn-xs btn-primary pull-right" @click='addOptionGroup(group)'><span class="glyphicon glyphicon-pencil"></span></a>
                            </div>
                            <div class="clearfix"></div>
                        </li>
                    </ul> -->
                </div>
            </div>
            <div class="col-md-5 col-sm-5">
                <div class="panel panel-default">
                    <!-- Default panel contents -->
                    <div class="panel-heading">选项
                        <a href="javascript:;" class="btn btn-xs btn-primary pull-right mr5" v-if='groupId!=""' @click='addOption(0)'><span class="glyphicon glyphicon-plus"></span> 添加</a>
                        <a href="javascript:;" class="btn btn-xs btn-primary pull-right mr5"  v-if='groupId!=""' @click='sortOption(0)'>更新显示顺序</a>
                    </div>
                    <div class="panel-body" v-if='groupId==""'>
                        <p>请在左边选择选项组</p>
                    </div>
                    <div class="panel-body" v-if='groupId!="" && items==""'>
                        <p>请在添加选项组</p>
                    </div>
                    <table class="table"> 
                        <tbody>
                            <transition-group tag="div" >
                                <tr class="item" v-for="(item,index,key) in items" :key="item.weight" draggable="true" @dragstart="handleDragStart($event, item)" @dragover.prevent="handleDragOver($event, item)" @dragenter="handleDragEnter($event, item)" @dragend="handleDragEnd($event, item)" title="可拖动">
                                    <td>
                                        <input type="checkbox" disabled="true"  v-if='option_cat==2'>
                                        <input type="radio" disabled="true" v-if='option_cat==1'>
                                    </td>
                                    <td  width="88%">{{item.title_cn}}（{{item.title_en}}）</td>
                                    <td  width="12%">
                                        <div  @click.stop>
                                            <a href="javascript:;" class="btn btn-xs btn-danger" @click='removeOption(item.id,index)'><span class="glyphicon glyphicon-trash"></span></a>
                                            <a href="javascript:;" class="btn btn-xs btn-primary" @click='addOption(item)'><span class="glyphicon glyphicon-pencil"></span></a>
                                        </div>
                                    </td>
                                </tr>
                            </transition-group>
                        </tbody>
                    </table>
                     <!-- <ul class="list-group" v-else>
                        <transition-group tag="div" >
                            <li class="list-group-item item" v-for="(item,index,key) in items" :key="item.weight" draggable="true" @dragstart="handleDragStart($event, item)" @dragover.prevent="handleDragOver($event, item)" @dragenter="handleDragEnter($event, item)" @dragend="handleDragEnd($event, item)" title="可拖动">
                                <label class="checkbox-inline col-md-10" v-if='option_cat==2'>
                                    <input type="checkbox" disabled="true">{{item.id}} {{item.title_cn}}（{{item.title_en}}）
                                </label>
                                <label class="radio-inline col-md-10" v-if='option_cat==1'>
                                    <input type="radio" disabled="true">{{item.id}} {{item.title_cn}}（{{item.title_en}}）
                                </label>
                                <div class="pull-right col-md-2"  @click.stop>
                                    <a href="javascript:;" class="btn btn-xs btn-danger pull-right ml5" @click='removeOption(item.id,index)'><span class="glyphicon glyphicon-trash"></span></a>
                                    <a href="javascript:;" class="btn btn-xs btn-primary pull-right" @click='addOption(item)'><span class="glyphicon glyphicon-pencil"></span></a>
                                </div>
                                <div class="clearfix"></div>
                            </li>
                        </transition-group>
                    </ul> -->
                </div>
            </div>
            <div class="modal fade bs-example-modal-lg" id="addgroup" tabindex="-1" role="dialog" data-backdrop="static">
                <div class="modal-dialog" >
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                            &times;
                        </button>
                            <h4 class="modal-title" id="myModalLabel">
                            {{modelTitle}}选项组
                        </h4>
                        </div>
                        <div class="modal-body">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <label for="inputEmail3" class="col-sm-2 control-label">选项类型</label>

                                    <div class="col-sm-10" v-if='eidtGroup.option_cat==2'>
                                        <label class="checkbox-inline col-sm-3"><input type="radio"  value="1" name='grp' > 单选
                                        </label>
                                        <label class="checkbox-inline  col-sm-3"><input type="radio" value="2" name='grp' checked="checked"> 多选
                                        </label>
                                    </div>
                                    <div class="col-sm-10" v-else>
                                        <label class="checkbox-inline col-sm-3"><input type="radio"  value="1" name='grp' checked="checked"> 单选
                                        </label>
                                        <label class="checkbox-inline  col-sm-3"><input type="radio" value="2" name='grp'> 多选
                                        </label>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputEmail3" class="col-sm-2 control-label">中文名称</label>
                                    <div class="col-sm-10">
                                      <input type="text" class="form-control" id='Grouptitle_cn' :value='eidtGroup.title_cn'>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputEmail3" class="col-sm-2 control-label">英文名称</label>
                                    <div class="col-sm-10">
                                      <input type="text" class="form-control" id='Grouptitle_en' :value='eidtGroup.title_en'>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                            <button type="button" class="btn btn-primary" @click='updateGroup()'>更新</button>
                        </div>
                    </div>
                </div>
            </div>
             <div class="modal fade bs-example-modal-lg" id="option" tabindex="-1" role="dialog" data-backdrop="static">
                <div class="modal-dialog" >
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                            &times;
                        </button>
                            <h4 class="modal-title" id="myModalLabel">
                            {{modelTitle}}选项组
                        </h4>
                        </div>
                        <div class="modal-body">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <label for="inputEmail3" class="col-sm-2 control-label">选项值</label>
                                    <div class="col-sm-10">
                                       <input type="text" class="form-control" id='option_value' :value='eidtOption.option_value'>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputEmail3" class="col-sm-2 control-label">中文名称</label>
                                    <div class="col-sm-10">
                                      <input type="text" class="form-control" id='optTitle_cn' :value='eidtOption.title_cn'>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputEmail3" class="col-sm-2 control-label">英文名称</label>
                                    <div class="col-sm-10">
                                      <input type="text" class="form-control" id='optTitle_en' :value='eidtOption.title_en'>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                            <button type="button" class="btn btn-primary" @click='updateOption()'>更新</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        var group=<?php echo json_encode($groupData); ?>;
        console.log(group)
        var group = new Vue({
            el: "#group",
            data: {
                grouplist:<?php echo json_encode($groupData); ?>,
                items:'',
                dragging: null,
                optionlist:'',
                activeClass:-1,
                eidtGroup:'',
                option_cat:'',
                groupId:'',
                eidtOption:'',
                modelTitle:''
            },
            methods: {
                groupopt(id,index,option_cat){
                    if(index!='-1'){
                        this.activeClass=index
                        this.option_cat=option_cat
                        this.groupId=id  
                    }
                    $.ajax({
                        url: "<?php echo $this->createUrl('getTopic')?>",
                        type: 'post',
                        dataType: 'json',
                        data:{
                            groupId:id,
                        },
                        success:function(data){
                           console.log(data)
                            if(data.state=='success'){
                                group.items=data.data
                            }else{
                                survey.unfinishedlist=data
                                $('#unfinished').modal('show')
                            }
                        },
                        error:function(data) {
                            alert('请求错误')
                        }
                    })
                },
                addOptionGroup(thisdata){
                    $('#addgroup').modal('show')
                    if(thisdata==0){
                        this.eidtGroup=''
                        this.myVal=1
                        this.modelTitle='添加'
                        return
                    }
                    this.modelTitle='更新'
                    this.Grouptitle_en=thisdata.title_en
                    this.Grouptitle_cn=thisdata.title_cn
                    this.eidtGroup=thisdata
                    this.myVal=thisdata.option_cat
                },
                updateGroup(){
                    $.ajax({
                        url: "<?php echo $this->createUrl('updateGroup')?>",
                        type: 'post',
                        dataType: 'json',
                        data:{
                            groupId:this.eidtGroup.id,
                            option_cat: $("input:radio[name='grp']:checked").val(),
                            title_cn:$('#Grouptitle_cn').val(),
                            title_en:$('#Grouptitle_en').val()
                        },
                        success:function(data){
                           console.log(data)
                            if(data.state=='success'){
                                if(group.eidtGroup!=''){
                                    Vue.set( group.eidtGroup,'title_cn', $('#Grouptitle_cn').val())
                                    Vue.set( group.eidtGroup,'title_en', $('#Grouptitle_en').val())
                                    Vue.set( group.eidtGroup,'option_cat',$("input:radio[name='grp']:checked").val())
                                }else{
                                    setTimeout(function(){ 
                                        window.location.reload();
                                    },1000)
                                }
                                resultTip({"msg": data.message})
                                $('#addgroup').modal('hide')
                            }else{
                                 resultTip({error: 'warning', msg: data.message});
                            }
                        },
                        error:function(data) {
                            alert('请求错误')
                        }
                    })
                   
                },
                removeOptionGroup(id,index){
                    console.log(index)
                    $.ajax({
                        url: "<?php echo $this->createUrl('deleteGroup')?>",
                        type: 'post',
                        dataType: 'json',
                        data:{
                            groupId:id,
                        },
                        success:function(data){
                           console.log(data)
                            if(data.state=='success'){
                                group.grouplist.splice(index,1);
                                resultTip({"msg": data.message})
                            }
                        },
                        error:function(data) {
                            alert('请求错误')
                        }
                    })
                },
                addOption(items){
                    $('#option').modal('show')
                    if(items==0){
                        this.eidtOption=''
                        this.modelTitle='添加';
                        return
                    }
                    this.eidtOption=items
                    this.modelTitle='更新';
                    console.log(items)
                },
                updateOption(){
                    $.ajax({
                        url: "<?php echo $this->createUrl('updateTopic')?>",
                        type: 'post',
                        dataType: 'json',
                        data:{
                            groupId:this.groupId,
                            optionId:this.eidtOption.id,
                            option_value:$('#option_value').val(),
                            title_cn:$('#optTitle_cn').val(),
                            title_en:$('#optTitle_en').val(),
                            weight:this.eidtOption.weight
                        },
                        success:function(data){
                           console.log(data)
                            if(data.state=='success'){
                                // if(group.eidtOption!=''){
                                //     Vue.set( group.eidtOption,'title_cn', $('#optTitle_cn').val())
                                //     Vue.set( group.eidtOption,'title_en', $('#optTitle_en').val())
                                //     Vue.set( group.eidtOption,'option_value',$('#option_value').val())
                                // }else{
                                    group.groupopt(group.groupId,-1,-1)
                                    // setTimeout(function(){ 
                                    //     window.location.reload();
                                    // },1000)
                               // }
                                
                                resultTip({"msg": data.message})
                                $('#option').modal('hide')
                            }else{
                                 resultTip({error: 'warning', msg: data.message});
                            }
                        },
                        error:function(data) {
                            alert('请求错误')
                        }
                    })

                },
                removeOption(optionid,index){
                    $.ajax({
                        url: "<?php echo $this->createUrl('deleteTopic')?>",
                        type: 'post',
                        dataType: 'json',
                        data:{
                            optionId:optionid,
                        },
                        success:function(data){
                           console.log(data)
                            if(data.state=='success'){
                                group.items.splice(index,1);
                                resultTip({"msg": data.message})
                            }
                        },
                        error:function(data) {
                            alert('请求错误')
                        }
                    })
                },
                handleDragStart(e, item) {
                    this.dragging = item;
                },
                handleDragEnd(e, item) {
                    this.dragging = null
                },
                //首先把div变成可以放置的元素，即重写dragenter/dragover
                handleDragOver(e) {
                    e.dataTransfer.dropEffect = 'move' // e.dataTransfer.dropEffect="move";//在dragenter中针对放置目标来设置!
                },
                handleDragEnter(e, item) {
                    e.dataTransfer.effectAllowed = "move" //为需要移动的元素设置dragstart事件
                    if(item === this.dragging) {
                        return
                    }
                    const newItems = [...this.items]
                    console.log(newItems)
                    const src = newItems.indexOf(this.dragging)
                    const dst = newItems.indexOf(item)
                    newItems.splice(dst, 0, ...newItems.splice(src, 1))
                    this.items = newItems
                },
                sortOption(){
                    var option_order=[]
                    for(var i=0;i<this.items.length;i++){
                        option_order.push(this.items[i].id)
                    }
                    console.log(option_order)
                    $.ajax({
                        url: "<?php echo $this->createUrl('sortTopic')?>",
                        type: 'post',
                        dataType: 'json',
                        data:{
                            groupId:this.groupId,
                            option_order:option_order,
                        },
                        success:function(data){
                           console.log(data)
                            if(data.state=='success'){
                                resultTip({"msg": data.message})
                            }else{
                                 resultTip({error: 'warning', msg: data.message});
                            }
                        },
                        error:function(data) {
                            alert('请求错误')
                        }
                    })
                }
            }
        })
    </script>


