<style>
    .ui-radio {
        width: 16px;
        height: 16px;
        position: relative;
        margin-right: 5px;
        border-radius: 30px;
    }

    .label {
        font-size: 14px;
    }

    input[type="radio"]:checked+label {
        color: red;
    }

    input[type="radio"]:hover+label {
        color: red;
    }

    .tooltip-left-align .tooltip-inner {
        text-align: left;
    }

    .radio_inline:hover,
    .radio_inline label:hover {
        cursor: not-allowed !important;
    }

    .radio_inline input {
        background-color: rgba(233, 233, 233, 0.5);
        appearance: none;
        width: 13px;
        height: 13px;
        border-radius: 50%;
        border: 1px solid #bfb7b7;
    }

    .radio_inline input[type="radio"]:checked {
        appearance: auto;
    }

    .radio_inline input[type="radio"]:focus {
        outline: none;
    }
    .custom_class {
        max-width: 400px;
    }
    .custom_class .popover-title{
        white-space: nowrap;
    }
    .custom_class .arrow{
        top:none
    }
</style>
<?php $attendance_ext = $model->attendance_ext ? CJSON::decode($model->attendance_ext) : array(); ?>
<div class="row">
    <div class="col-sm-3">
        <div>
            <?php if ($model->status == 1) : ?>
                <div class="alert alert-success text-center">
                    <h4><?php echo $child->getChildName(); ?></h4>
                    <div class="p20"> <span class="glyphicon glyphicon-ok-sign"></span>
                        <?php echo Yii::t('teaching', 'Report is online'); ?>
                        <?php if ($model->pdf_file != 1) { ?>
                            (<?php echo Yii::t('teaching', 'PDF being generated'); ?>)
                        <?php } ?>
                    </div>
                    <?php if ($classObj->classtype == 'mc') { ?>
                        <?php if ($model->custom_pdf) { ?>
                            <a class="btn btn-info" target="_blank" href="<?php echo $this->createUrl('showPdfDs', array('reportId' => $model->id)); ?>"><span class="glyphicon glyphicon-file"></span> PDF</a>
                        <?php } ?>
                        <button type="button" class="btn btn-danger" onclick="setReportStatus('offline', <?php echo $model->id ?>)">
                            <?php echo Yii::t('teaching', 'Make Offline'); ?>
                        </button>
                    <?php } ?>
                    <?php if ($classObj->classtype != 'mc') { ?>
                        <div>
                            <button type="button" class="btn btn-danger" onclick="setReportStatus('offline', <?php echo $model->id ?>)">
                                <?php echo Yii::t('teaching', 'Make Offline'); ?>
                            </button>

                            <button type="button" class="btn btn-info" onclick="reportPreview(<?php echo $child->childid; ?>,<?php if (empty($model)) {
                                                                                                                                echo 0;
                                                                                                                            } else {
                                                                                                                                echo $model->id;
                                                                                                                            }; ?>,<?php echo $classid ?>)">
                                <?php echo Yii::t('teaching', 'Preview'); ?>
                            </button>

                        </div>
                    <?php } ?>
                </div>
            <?php else : ?>
                <div class="alert alert-danger text-center">
                    <h4><?php echo $child->getChildName(); ?></h4>
                    <div class="p20"> <span class="glyphicon glyphicon-exclamation-sign"></span>
                        <?php echo Yii::t('teaching', 'Report is offline'); ?>
                    </div>
                    <?php if ($classObj->classtype == 'mc') { ?>
                        <div class="p20">
                            <span id="container_ds">
                                <button id="select-photo-ds" class="btn btn-primary"><span class="glyphicon glyphicon-plus"></span> <?php echo Yii::t('teaching', 'Select Files'); ?></button>
                            </span>
                            <span id="viewReportPDFDs">
                                <?php if ($model->custom_pdf) { ?>
                                    <a class="btn btn-info" target="_blank" href="<?php echo $this->createUrl('showPdfDs', array('reportId' => $model->id)); ?>"><span class="glyphicon glyphicon-file"></span> PDF</a>
                                <?php } ?>
                            </span>
                        </div>
                    <?php } ?>
                    <div>
                        <button type="button" class="btn btn-primary" onclick="setReportStatus('online', <?php echo $model->id ? $model->id : 0; ?>)">
                            <?php echo Yii::t('teaching', 'Make Online'); ?>
                        </button>
                        <?php if ($classObj->classtype != 'mc') { ?>
                            <button type="button" class="btn btn-info" onclick="reportPreview(<?php echo $child->childid; ?>,<?php if (empty($model)) {
                                                                                                                                echo 0;
                                                                                                                            } else {
                                                                                                                                echo $model->id;
                                                                                                                            }; ?>,<?php echo $classid ?>)">
                                <?php echo Yii::t('teaching', 'Preview'); ?>
                            </button>
                        <?php } ?>
                        <a class="btn btn-primary J_modal" href="<?php echo $this->createUrl('updateGroup'); ?>"><span class="glyphicon glyphicon-edit" aria-hidden="true"></span> <?php echo Yii::t("report", "Teacher List"); ?></a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
    <div class="col-sm-4">
        <img class="img-thumbnail" src="<?php echo CommonUtils::childPhotoUrl($child->photo); ?>" alt="<?php echo $child->getChildName(); ?>" width="147">
    </div>
</div>

<div class="row">
    <div class="col-sm-12">
        <form action="<?php echo $this->createUrl('saveDsreportAttendance'); ?>" method="post" class="J_ajaxForm">
            <div class="panel panel-default">
                <div class="panel-body">
                    <div class="row">
                        <div class="col-lg-2">
                            <div class="input-group input-group">
                                <span class="input-group-addon" id="sizing-addon1"><?php echo Yii::t('report', 'Days Absent'); ?></span>
                                <input type="text" class="form-control" aria-describedby="sizing-addon1" disabled name="absent" value="<?php echo $attendance_ext['absent'] ?>">
                            </div>
                        </div>
                        <div class="col-lg-2">
                            <div class="input-group input-group">
                                <span class="input-group-addon" id="sizing-addon2"><?php echo Yii::t('report', 'Days Tardy'); ?></span>
                                <input type="text" class="form-control" aria-describedby="sizing-addon2" disabled name="tardy" value="<?php echo $attendance_ext['tardy'] ?>">
                            </div>
                        </div>
                        <div class="col-lg-2">
                            <div class="input-group input-group">
                                <span class="input-group-addon"><?php echo Yii::t('report', '上线时间'); ?></span>
                                <input type="text" id="uptime" class="form-control" aria-describedby="uptime" name="uptime" value="<?php echo $model->uptime > 0 ? date('Y-m-d', $model->uptime) : ''; ?>">
                            </div>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-lg-1">
                            <label><input type="checkbox" name="leap" value="1" <?php if ($attendance_ext['leap']) : ?>checked<?php endif; ?>> LEAP</label>
                        </div>
                        <div class="col-lg-1">
                            <label><input type="checkbox" name="csl" value="1" <?php if ($attendance_ext['csl']) : ?>checked<?php endif; ?>> Yuyue</label>
                        </div>
                        <div class="col-lg-2">
                            <label><input type="checkbox" name="support" value="1" <?php if ($attendance_ext['support']) : ?>checked<?php endif; ?>> <?php echo Yii::t('report', 'Learning Support'); ?></label>
                        </div>
                        <div class="col-lg-3">
                            <div class="input-group">
                                <span class="input-group-addon">
                                    <input type="checkbox" name="other" value="1" <?php if ($attendance_ext['other']) : ?>checked<?php endif; ?>> <?php echo Yii::t('report', 'Other') ?>
                                </span>
                                <input type="text" class="form-control" name="other_data" value="<?php echo $attendance_ext['other_data'] ?>">
                            </div>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Save'); ?></button>
                    <input type="hidden" name="childid" value="<?php echo $child->childid; ?>">
                    <input type="hidden" name="classid" value="<?php echo $classid; ?>">
                    <input type="hidden" name="semester" value="<?php echo $semester; ?>">
                </div>
            </div>
        </form>
        <?php foreach ($roots as $rt) : ?>
            <?php if($rt['score_comment_separate']){?>
                <div class="panel panel-default">
                        <!-- Default panel contents -->
                        <div class="panel-heading">
                            <h4><?php echo $rt['title']; ?></h4>
                        </div>
                        <!--分数保存start-->
                        <div>
                            <form action="<?php echo $this->createUrl('saveDsreportOption'); ?>" method="post" class="J_ajaxForm">
                                <div class="panel-body">
                                    <?php
                                    $selected = (isset($reportsOption[$rt['id']]['option'][$rt['id']])) ? $reportsOption[$rt['id']]['option'][$rt['id']] : '';
                                    if (isset($options[$rt['option_groupid']])) :
                                        foreach ($options[$rt['option_groupid']] as $option) :
                                            $checked = false;
                                            if ($selected == $option->id) {
                                                $checked = true;
                                            }
                                            $title = CommonUtils::autoLang($option->title_cn, $option->title_en) . '<br><br>' . CommonUtils::autoLang($option->desc_cn, $option->desc_en);
                                            echo CHtml::openTag('span', array('class' => 'mr20', 'data-toggle' => 'tooltip', 'data-placement' => 'top', 'title' => $title));
                                            echo CHtml::radioButton("option[{$rt['id']}]", $checked, array('value' => $option->id, 'id' => 'data_' . $rt['id'] . '_' . $option->id, 'class' => 'ui-radio'));
                                            echo CHtml::label($option->label, "data_{$rt['id']}_{$option->id}");
                                            echo CHtml::closeTag('span');
                                        endforeach;
                                    endif;
                                    ?>
                                </div>
                                <?php if (isset($subs[$rt['id']])) : ?>
                                    <?php foreach ($subs[$rt['id']] as $ss) : ?>
                                        <div class="label label-default ml10"><?php echo $ss['title']; ?></div>
                                        <!-- Table -->
                                        <div>
                                            <table class="table table-hover">
                                                <thead>
                                                <tr>
                                                    <th colspan="2"></th>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                <?php if (isset($items[$ss['id']])) : ?>
                                                    <?php foreach ($items[$ss['id']] as $ks => $is) : ?>
                                                        <tr>
                                                            <td width="61.8%"><?php echo $is; ?></td>
                                                            <td>
                                                                <?php
                                                                $selected = (isset($reportsOption[$rt['id']]['option'][$ss['id']][$ks])) ? $reportsOption[$rt['id']]['option'][$ss['id']][$ks] : '';
                                                                if (isset($options[$ss['option_groupid']])) :
                                                                    foreach ($options[$ss['option_groupid']] as $option) :
                                                                        $checked = false;
                                                                        $disabled = '';
                                                                        $onclick = '';
                                                                        // 判断是否评估
                                                                        if (in_array($ks, $disabledIdList)) {
                                                                            $disabled = 'disabled radio_inline';
                                                                            $onclick = 'return false;';
                                                                            if ($option->label == 'N/A') {
                                                                                $checked = true;
                                                                            }
                                                                        } else {
                                                                            // 是否已经做过评估
                                                                            if ($selected == $option->id) {
                                                                                $checked = true;
                                                                            } else {
                                                                                // 特定标准组才计算（ ivy_reports_option_group id 5）
                                                                                if (in_array($ss['option_groupid'], array(5))) {
                                                                                    $gradebookScoreItem = $gradebookScore[$ss['id']][$ks];
                                                                                    if (!isset($gradebookScore[$ss['id']][$ks])) {
                                                                                        if ($option->label == 'N/E') {
                                                                                            $checked = true;
                                                                                        }
                                                                                    } else {
                                                                                        $gradebookScoreMap = array(4 => 'E', 3 => 'P', 2 => 'L', 1 => 'N');
                                                                                        // $gradebookScoreMap2 = array(4 => 'C', 3 => 'O', 2 => 'S', 1 => 'R');
                                                                                        $gradebookScoreLable = array();
                                                                                        if (isset($gradebookScoreMap[$gradebookScoreItem])) {
                                                                                            $gradebookScoreLable[] = $gradebookScoreMap[$gradebookScoreItem];
                                                                                        }
                                                                                        if (isset($gradebookScoreMap2[$gradebookScoreItem])) {
                                                                                            $gradebookScoreLable[] = $gradebookScoreMap2[$gradebookScoreItem];
                                                                                        }
                                                                                        if (in_array($option->label, $gradebookScoreLable)) {
                                                                                            $checked = true;
                                                                                        }
                                                                                    }
                                                                                }
                                                                            }
                                                                        }
                                                                        $title = CommonUtils::autoLang($option->title_cn, $option->title_en) . '<br><br>' . CommonUtils::autoLang($option->desc_cn, $option->desc_en);
                                                                        echo CHtml::openTag('span', array('class' => 'mr20 ', 'data-toggle' => 'tooltip', 'data-placement' => 'top', 'title' => $title));
                                                                        echo CHtml::openTag('label', array('class' => 'radio-inline ' . $disabled,));
                                                                        echo CHtml::radioButton("option[{$ss['id']}][{$ks}]", $checked, array('value' => $option->id, 'id' => 'data_' . $ss['id'] . '_' . $ks . '_' . $option->id, 'onclick' => $onclick, 'class' => $disabled,));
                                                                        echo CHtml::label($option->label, "data_{$ss['id']}_{$ks}_{$option->id}");
                                                                        echo CHtml::closeTag('label');
                                                                        echo CHtml::closeTag('span');
                                                                    endforeach;
                                                                endif;
                                                                ?>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                <?php endif; ?>
                                                </tbody>
                                            </table>
                                            <hr style="margin-bottom: 10px">
                                        </div>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                                <div class="ml10 mb10">
                                    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('report', 'Save (Grade-Level Only)'); ?></button>
                                    <input type="hidden" name="childid" value="<?php echo $child->childid; ?>">
                                    <input type="hidden" name="classid" value="<?php echo $classid; ?>">
                                    <input type="hidden" name="semester" value="<?php echo $semester; ?>">
                                    <input type="hidden" name="category_id" value="<?php echo $rt['id']; ?>">
                                </div>
                            </form>
                        </div>
                        <!--分数保存end-->
                        <!--评语区域保存start-->
                        <div>
                            <form action="<?php echo $this->createUrl('saveDsreportMemo'); ?>" method="post" class="J_ajaxForm">
                                <div class="mt10">
                                    <table class="table" style="margin-bottom: 0">
                                        <tbody>
                                        <tr>
                                            <td>
                                                <div class="label label-info"><?php echo Yii::t('report', 'Teacher\'s Comment / Next Step'); ?></div>
                                                <?PHP echo CHtml::textArea('memo', $reportsOption[$rt['id']]['memo'], array('class' => 'form-control', 'rows' => 3)); ?>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="ml10 mb10">
                                    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('report', 'Save (Comment Only)'); ?></button>
                                    <input type="hidden" name="childid" value="<?php echo $child->childid; ?>">
                                    <input type="hidden" name="classid" value="<?php echo $classid; ?>">
                                    <input type="hidden" name="semester" value="<?php echo $semester; ?>">
                                    <input type="hidden" name="category_id" value="<?php echo $rt['id']; ?>">
                                </div>
                            </form>
                        </div>
                        <!--评语区域保存end-->
                    </div>
            <?php }else{?>
            <form action="<?php echo $this->createUrl('saveDsreport'); ?>" method="post" class="J_ajaxForm">
                <div class="panel panel-default">
                    <!-- Default panel contents -->
                    <div class="panel-heading">
                        <div class="row">
                            <div class="col-lg-10">
                                <h4><?php echo $rt['title']; ?></h4>
                            </div>
                            <?php $currentSs = isset($subs[$rt['id']]) ? current($subs[$rt['id']]) : array();?>
                            <?php if(isset($currentSs['gradebook']) && $currentSs['gradebook']){?>
                            <div class="col-lg-2">
                                <button type="button" class="pull-right btn btn-primary" onclick="useGradebookScore(this)"><?php echo Yii::t('report', 'Auto-populate data from GradeBook'); ?></button>
                            </div>
                            <?php }?>
                        </div>
                    </div>
                    <div class="panel-body">
                        <?php
                        $selected = (isset($reportsOption[$rt['id']]['option'][$rt['id']])) ? $reportsOption[$rt['id']]['option'][$rt['id']] : '';
                        if (isset($options[$rt['option_groupid']])) :
                            foreach ($options[$rt['option_groupid']] as $option) :
                                $checked = false;
                                if ($selected == $option->id) {
                                    $checked = true;
                                }
                                $title = CommonUtils::autoLang($option->title_cn, $option->title_en) . '<br><br>' . CommonUtils::autoLang($option->desc_cn, $option->desc_en);
                                echo CHtml::openTag('span', array('class' => 'mr20', 'data-toggle' => 'tooltip', 'data-placement' => 'top', 'title' => $title));
                                echo CHtml::radioButton("option[{$rt['id']}]", $checked, array('value' => $option->id, 'id' => 'data_' . $rt['id'] . '_' . $option->id, 'class' => 'ui-radio'));
                                echo CHtml::label($option->label, "data_{$rt['id']}_{$option->id}");
                                echo CHtml::closeTag('span');
                            endforeach;
                        endif;
                        ?>
                    </div>
                    <?php $showNotice = false; ?>
                    <?php if (isset($subs[$rt['id']])) : ?>
                        <?php foreach ($subs[$rt['id']] as $ss) : ?>
                            <div class="label label-default ml10"><?php echo $ss['title']; ?></div>
                            <!-- Table -->
                            <div>
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th></th>
                                            <th>Final Level</th>
                                            <?php if ($ss['gradebook']) : ?>
                                                <th>GradeBook</th>
                                            <?php endif; ?>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if (isset($items[$ss['id']])) : ?>
                                            <?php foreach ($items[$ss['id']] as $ks => $is) : ?>
                                                <tr>
                                                    <td width="61.8%"><?php echo $is; ?></td>
                                                    <td>
                                                        <?php
                                                        $categoryId = $ss['id'];
                                                        $itemId = $ks;
                                                        // 已保存的选项答案
                                                        $selected = (isset($reportsOption[$rt['id']]['option'][$categoryId][$itemId])) ? $reportsOption[$rt['id']]['option'][$categoryId][$itemId] : '';
                                                        $selectedText = '';
                                                        if ($ss['gradebook']) {
                                                            $gradebookScoreText = 'N/E';
                                                            if (isset($gradebookScore[$categoryId][$itemId])) {
                                                                $gradebookScoreItem = $gradebookScore[$categoryId][$itemId];
                                                                $gradebookScoreText = $ss['grademap'][$gradebookScoreItem];
                                                            }
                                                        }
                                                    
                                                        if (isset($options[$ss['option_groupid']])) :                                                            
                                                            foreach ($options[$ss['option_groupid']] as $option) :
                                                                $checked = false;
                                                                $disabled = '';
                                                                $onclick = '';
                                                                // 判断是否评估
                                                                if (in_array($itemId, $disabledIdList)) {
                                                                    $disabled = 'disabled radio_inline';
                                                                    $onclick = 'return false;';
                                                                    if ($option->label == 'N/A') {
                                                                        $checked = true;
                                                                        $gradebookScoreText = 'N/A';
                                                                    }
                                                                } else {
                                                                    // 是否已经做过评估
                                                                    if ($selected) {
                                                                        if ($selected == $option->id) {
                                                                            $checked = true;
                                                                            $selectedText = $option->label;
                                                                        }
                                                                    } else {
                                                                        // 特定标准组才计算（ ivy_reports_option_group id 5）
                                                                        if ($ss['gradebook']) {
                                                                            // if (!$selected) {
                                                                            //     $showNotice = true;
                                                                            // }
                                                                            // if ($option->label == $gradebookScoreText) {
                                                                            //     $checked = true;
                                                                            // }
                                                                        }
                                                                    }
                                                                }

                                                                $title = CommonUtils::autoLang($option->title_cn, $option->title_en) . '<br><br>' . CommonUtils::autoLang($option->desc_cn, $option->desc_en);
                                                                echo CHtml::openTag('span', array('class' => 'mr20 ', 'data-toggle' => 'tooltip', 'data-placement' => 'top', 'title' => $title));
                                                                echo CHtml::openTag('label', array('class' => 'radio-inline ' . $disabled,));
                                                                echo CHtml::radioButton("option[{$categoryId}][{$itemId}]", $checked, array('value' => $option->id, 'id' => 'data_' . $categoryId . '_' . $itemId . '_' . $option->id, 'onclick' => $onclick, 'class' => $disabled, 'onchange' => "showNotice(this, '{$rt['id']}')"));
                                                                echo CHtml::label($option->label, "data_{$categoryId}_{$itemId}_{$option->id}");
                                                                echo CHtml::closeTag('label');
                                                                echo CHtml::closeTag('span');
                                                            endforeach;
                                                        endif;
                                                        ?>
                                                    </td>
                                                    <?php if ($ss['gradebook']) : ?>
                                                        <td>
                                                            <?php
                                                                $textClass = 'btn-default';
                                                                if ($selected && $gradebookScoreText != 'N/A' && $gradebookScoreText != $selectedText) {
                                                                    $textClass = 'btn-warning';
                                                                }
                                                                echo CHtml::openTag('button', array('class' => 'btn btn-sm gradebook-btn ' . $textClass, 'type' => 'button', 'data-container'=>'body', 'data-toggle'=>'popover', 'data-html' => 'true', 'data-placement'=>'top',  'data-title'=>'GradeBook Evidence Data:', 'data-content'=>'', 'data-trigger'=>'manual', 'data-category' => $categoryId, 'data-item' => $itemId, 'onclick' => 'getGradebookScoreDetail(this)'));
                                                                echo '<span style="width:20px; display:inline-block">' . $gradebookScoreText . '</span>';
                                                                echo CHtml::closeTag('button');
                                                             ?>
                                                        </td>
                                                    <?php endif; ?>
                                                </tr>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                                <hr>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                    <table class="table">
                        <thead>
                            <tr>
                                <td></td>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="label label-info"><?php echo Yii::t('report', 'Teacher\'s Comment / Next Step'); ?></div>
                                    <?PHP echo CHtml::textArea('memo', $reportsOption[$rt['id']]['memo'], array('class' => 'form-control', 'rows' => 3, 'oninput' => "showNotice(this, '{$rt['id']}')")); ?>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="checkbox">
                                        <label>
                                            <input type="checkbox" value="1" name="status" <?php if (isset($reportsOption[$rt['id']]['status']) && ($reportsOption[$rt['id']]['status'])) : ?> checked="checked" <?php endif; ?>> <?php echo Yii::t('teaching', 'Make Online'); ?>
                                        </label>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Save'); ?></button>
                                    <span id="<?= $rt['id'];?>_notice" class="text-danger <?= $showNotice ? '' : 'hidden';?>"><span class="glyphicon glyphicon-exclamation-sign"></span> <?php echo Yii::t('teaching', 'Changes not saved!'); ?></span>
                                    <input type="hidden" name="childid" value="<?php echo $child->childid; ?>">
                                    <input type="hidden" name="classid" value="<?php echo $classid; ?>">
                                    <input type="hidden" name="semester" value="<?php echo $semester; ?>">
                                    <input type="hidden" name="category_id" value="<?php echo $rt['id']; ?>">
                                </td>
                            </tr>
                        </tbody>

                    </table>
                </div>
            </form>
        <?php }?>
        <?php endforeach; ?>
    </div>
</div>
<script>
    var reportid = <?php echo $model->id ? $model->id : 0; ?>;
    var canSave = <?php echo $canSave; ?>;
    $(function() {
        if (!canSave) {
            $('.report-edit-zone .col-sm-12').find('input, select, textarea, button').prop('disabled', true);
            $('.gradebook-btn').prop('disabled', false);
        }

        $('[data-toggle="tooltip"]').tooltip({
            html: true,
            template: '<div class="tooltip  tooltip-left-align" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner tooltip-left-align"></div></div>'
        });

        $('.J_modal').on('click', function(e) {
            e.preventDefault();
            $('#teacherList').html('');
            $.ajax({
                type: 'post',
                url: '<?php echo $this->createUrl('getClassTeachers', array('childid'=> $child->childid, 'classid' => $classid, 'semester' => $semester)); ?>',
                dataType: 'json',
                data: {
                    // 你的数据
                },
                success: function(response) {
                    response.data.forEach(function(item) {
                        var html = item.is_custom == 0 ? classTeacherHtml(item.teacherid, item.name, item.weight) : customTeacherHtml(item.teacherid, item.name, item.weight);
                        $('#teacherList').append(html);
                    });
                },
                error: function(error) {
                    // 处理错误
                    console.error(error);
                }
            });

            $('#searchStaffModal').modal({
                show: true,
                backdrop: 'static',
                keyboard: false
            });
        });
    })

    var uploader = new plupload.Uploader({
        runtimes: 'html5,flash,silverlight,html4',
        browse_button: 'select-photo-ds',
        container: document.getElementById('container_ds'),
        url: '<?php echo $this->createUrl('setReportPdfDs') ?>',
        flash_swf_url: '<?php echo Yii::app()->themeManager->baseUrl . '/base/js/plupload/Moxie.swf' ?>',
        silverlight_xap_url: '<?php echo Yii::app()->themeManager->baseUrl . '/base/js/plupload/Moxie.xap' ?>',
        multipart_params: {
            report_id: <?php echo $model->id ? $model->id : 0; ?>
        },
        filters: {
            max_file_size: '50mb',
            mime_types: [{
                title: "<?php echo Yii::t('teaching', 'Image files'); ?>",
                extensions: "pdf"
            }]
        },

        init: {
            QueueChanged: function(up) {
                uploader.start();
            },

            UploadProgress: function(up, file) {
                $('#' + file.id + ' dt span').hide();
                $('#' + file.id + ' div.progress-bar').attr('aria-valuenow', file.percent).css('width', file.percent + '%').html(file.percent + '%');
            },

            FileFiltered: function(up, file) {
                // Called when file successfully files all the filters
                queueHtml(file);
                $("#modal").modal();
                $('#' + file.id + ' span').click(function() {
                    $('#' + file.id).remove();
                    uploader.removeFile(file);
                });
            },

            FileUploaded: function(up, file, info) {
                var json = eval('(' + info.response + ')');
                $('#' + file.id).remove();

                var html = '<a class="btn btn-info" target="_blank" href="' + json.pdfUrl + '"><span class="glyphicon glyphicon-file"></span> PDF</a>';
                $("#viewReportPDFDs").html(html);
                $('#modal').modal('hide');
            }
        }
    });
    uploader.init();

    function queueHtml(file) {
        var filelist = $('#filelist');
        var queueHtml = '<dl id="' + file.id + '"><dt><span class="glyphicon glyphicon-remove"></span> ';
        queueHtml += file.name + ' <small>(' + plupload.formatSize(file.size) + ')</small>';
        queueHtml += '</dt><dd class="progress">';
        queueHtml += '<div class="progress-bar" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>';
        queueHtml += '</div></dd></dl>';
        filelist.find('div.panel-body').append(queueHtml);
    }

    function showNotice(ele, cid) {
        $('#' + cid + '_notice').removeClass('hidden');
        
        // 获取当前元素
        let currentEle = $(ele);
        // 获取当前元素的父元素
        let parentEle = currentEle.parent();
        // 获取父元素下的所有 input 元素
        let inputEles = parentEle.find('input');
        // 遍历所有 input 元素
        inputEles.each(function() {
            // 如果当前 input 元素被选中
            if ($(this).is(':checked')) {
                // 选中的 label 的文字
                let labelText = $(this).parent().find('label').first().text().trim();
                // 获取下一行的 获取 gradebook-btn 元素
                let nextTd = $(this).closest('td').next();
                let gradebookBtn = nextTd.find('.gradebook-btn');
                if (labelText == gradebookBtn.text()) {
                    gradebookBtn.addClass('btn-default');
                    gradebookBtn.removeClass('btn-warning');
                } else {
                    gradebookBtn.addClass('btn-warning');
                    gradebookBtn.removeClass('btn-default');
                }
            }
        });
    }
    
    function getGradebookScoreDetail(e) {
        // 检查弹出框是否已显示
        if ($(e).attr('aria-describedby')) {
            // 如果弹出框已显示，则隐藏它
            $(e).popover('hide');
            return;
        }
        // 关闭其他show的弹窗
        $('.gradebook-btn[aria-describedby]').each(function() {
            $(this).popover('hide');
        });
        
        
        // 如果已有内容，直接显示弹窗而不再请求
        if ($(e).data('content') != '') {
            $(e).popover('show');
            return;
        }
        
        if ($(e).text() == 'N/A') {
            $(e).attr('data-content', 'N/A');
            $(e).popover('show');
            return;
        }
        // if ($(e).text() == 'N/E') {
        //     $(e).attr('data-content', 'N/E');
        //     $(e).popover('show');
        //     return;
        // }

        // 设置按钮不可以点
        $(e).prop('disabled', true);
        
        $.ajax({
            url: '<?= $this->createUrl("getGradebookScoreDetail") ?>',
            type: "post",
            dataType: 'json',
            data: {
                childid: <?= $child->childid; ?>,
                startyear: <?= $startyear; ?>,
                semester: <?= $semester; ?>,
                category: $(e).data('category'),
                item: $(e).data('item'),
            },
            success: function(data) {
                if (data.state == 'success') {
                    var contentHtml = "<table class='table table-bordered'><th>Evidence</th><th>Score</th>";
                    for (let index = 0; index < data.data.length; index++) {
                        const element = data.data[index];
                        contentHtml += '<tr><td>' + element.title + '</td><td>' + element.score + '</td></tr>';
                    }
                    contentHtml += '</table>';
                    $(e).attr('data-content', contentHtml);
                    $(e).on('shown.bs.popover', function () {
                        $('.popover').addClass('custom_class');
                    });
                    $(e).popover('show');
                } else {
                    resultTip({
                        error: 'warning',
                        msg: data.message
                    });
                }
                $(e).prop('disabled', false); // 恢复按钮的可用性
            },
            error: function(data) {
                resultTip({
                    error: 'warning',
                    msg: data.message
                });
                $(e).prop('disabled', false); // 恢复按钮的可用性
            },
        })
    }

    function useGradebookScore(e) {
        if (confirm('<?= Yii::t('report', 'Confirm to fill in the report levels based on GradeBook evidences (existing report levels) will be overwritten)?'); ?>')) {
            $(e).closest('.panel .panel-default').find('.gradebook-btn').each(function() {
                if ($(this).text() != 'N/A') {
                    let category_id = $(this).data('category');
                    let item_id = $(this).data('item');
                    let score = $(this).text();
                    // 找到 name 为 option[category_id][item_id] 并匹配，如果匹配到，调用 change

                    $('input[name="option[' + category_id + '][' + item_id + ']"]').filter(function(i, e) {
                        return $(e).parent().find('label').first().text().trim() == score;
                    }).prop('checked', true).change(); // 调用 change 事件，触发 showNotice 函数，显示提示信息
                }
            });
        }
    }

    $("#uptime").datepicker({
        dateFormat: "yy-mm-dd ",
    });

</script>
