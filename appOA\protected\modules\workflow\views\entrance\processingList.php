<?php
    $paymentIdList = array();
    $contractIdList = array();
    foreach ($processingList as $item) {
        $nodes = explode(',', $nodesList[$item->defination_id]['obj']->node_order);
        
        $handle = $nodesList[$item->defination_id]['obj']->defination_handle;
        if (in_array(strtolower($handle), array('payment', 'paymenthq'))){
            $objectId = $item->operation_object_id;
            $paymentIdList[] = $objectId;
        }
        if (in_array(strtolower($handle), array('contract', 'contracthq'))) {
            $objectId = $item->operation_object_id;
            $contractIdList[] = $objectId;
        }
    }
    $paymentList = array();
    $contractList = array();
    if ($paymentIdList) {
        Yii::import('common.models.workflow.payment.PaymentApply');
        $criteria = new CDbCriteria;
        $criteria->compare("id", $paymentIdList);
        $criteria->compare("budget", 1);
        $criteria->index = 'id';
        $paymentList = PaymentApply::model()->findAll($criteria);
    }
    if ($contractIdList) {
        Yii::import('common.models.workflow.contract.ContractApply');
        $criteria = new CDbCriteria;
        $criteria->compare("id", $contractIdList);
        $criteria->compare("budget", 1);
        $criteria->index = 'id';
        $contractList = ContractApply::model()->findAll($criteria);
    }
?>
<div class="panel panel-default">
    <div class="panel-heading" data-branchfilter="dailyBulletin"><span class="glyphicon glyphicon-bullhorn"></span>【<?php echo count($processingList) . '】' . Yii::t('workflow', 'Workflows in process'); ?> </div>
    <div class="panel-body table-responsive" id="history-check-list">
        <?php if ($processingList) : ?>
            <?php foreach ($processingList as $workflow) : $objectId = $workflow->operation_object_id; ?>
                <?php 
                    $nodes = explode(',', $nodesList[$workflow->defination_id]['obj']->node_order);
                    $noNeedNodes =  explode(',', $workflow->exte4);
                ?>
                <div class="task-data">
                    <?php if ($workflow->state == WorkflowOperation::WORKFLOW_STATS_STEP && $workflow->current_node_index == current($nodes)) : ?>
                        <a type="button" class="close J_ajax_del" aria-label="Close" style="color:red;font-size:28px;" href="
                            <?php
                            echo $this->createUrl('//workflow/entrance/index', array(
                                'definationId' => $workflow->defination_id,
                                'nodeId' => $workflow->current_node_index,
                                'operationId' => $workflow->id,
                                'branchId' => $workflow->branchid,
                                'action' => 'delete',
                            ));
                            ?>"><span aria-hidden="true">&times;</span>
                        </a>
                    <?php endif; ?>
                    <ul class="task-info" style="width: 610px;padding-left:2px;">
                        <li>
                            <?php echo $nodesList[$workflow->defination_id]['name']; ?>
                            <?php
                            $config = WorkflowConfig::getType($workflow->operation_type, $workflow->operation_object_id);
                            if ($config['href']) {
                                echo CHtml::link($config['function'], $config['href'], array('target' => '_blank'));
                            } else {
                                echo $config['function'];
                            }
                            ?>
                        </li>
                        <li>
                            <?php echo Yii::t('workflow', 'School:') . ' ' . $branchList[$workflow->branchid]; ?>
                        </li>
                        <li>
                            <?php echo Yii::t('workflow', 'Requester:') . ' ' . $usersList[$workflow->start_user]; ?>
                        </li>
                        <li>
                            <?php echo Yii::t('workflow', 'Request Time:') . ' ' . OA::formatDateTime($workflow->start_time, 'medium', 'short'); ?>
                        </li>
                    </ul>
                    <div class="clearfix"></div>
                    <ul class="nav nav-wizard" style='padding-bottom: 20px;'>
                        <?php
                        $flag = false;
                        foreach ($nodes as $node) :
                            if (in_array($node, $noNeedNodes)) {
                                continue;
                            }
                            if ($flag) {
                                break;
                            }
                            $nodeObj = $nodesList[$workflow->defination_id]['obj']['workflowNode'][$node];
                            $handle = strtolower($nodesList[$workflow->defination_id]['obj']->defination_handle);
                            if(in_array($handle, array('payment', 'paymenthq')) && $nodeObj->field > 0 && isset($paymentList[$objectId]) && $nodeObj->field >= $paymentList[$objectId]->price_total / 100)
                            { $flag = true;}
                            if(in_array($handle, array('contract', 'contracthq')) && $nodeObj->field > 0 && isset($contractList[$objectId]) && $nodeObj->field >= $contractList[$objectId]->price_total / 100)
                            { $flag = true;}
                        ?>
                            <li <?php if ($node == $workflow->current_node_index) : ?> class="active" <?php endif; ?>>
                                <a href="<?php
                                            echo $this->createUrl('//workflow/entrance/index', array(
                                                'definationId' => $workflow->defination_id,
                                                'nodeId' => $node,
                                                'operationId' => $workflow->id,
                                                'branchId' => $workflow->branchid,
                                                'action' => 'show',
                                            ));
                                            ?>" class="J_ajax" data-method="get">
                                    <?php echo $nodesList[$workflow->defination_id]['nodes'][$node]['name']; ?>
                                </a>
                            </li>
                        <?php endforeach; ?>
                        <button type="button" class="btn btn-primary btn-xs" style="margin-left: 20px; margin-top: 7px;" onclick="show('<?php echo $workflow->id; ?>')">当前审核人</button>
                    </ul>
                    <div class="clearfix"></div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
</div>


<!-- Modal -->
<div class="modal fade" id="currentModel" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel">当前审核人</h4>
            </div>
            <div class="modal-body">
                <div class="table-responsive mb5">
                    <label for="">当前审核人：</label>
                    <div id="current_users"></div>
                </div>
                <hr>
                <div class="table-responsive mb5">
                    <label for="">最后通知时间：<span id="notifyDate" class="text-primary"></span></label>
                </div>
                <div class="table-responsive mb5">
                    <button id="notifyBtn" style="display:none;" class="btn btn-primary mb5" disabled="disabled" onclick="notify(this)">催办</button>
                </div>
                <?php if (Yii::app()->user->checkAccess('ivystaff_it')) : ?>
                    <div class="table-responsive" style="margin-top: 10px;">
                        <label for="">添加新审核人：</label>
                        <?php $this->widget('ext.search.StaffSearchBox', array(
                            'acInputCSS' => 'form-control',
                            'htmlOptions' => array('class' => 'form-control'),
                            'data' => array(),
                            'name' => 'uid',
                            'allowMultiple' => false,
                            'withAlumni' => false,
                        )) ?>
                        <br>
                        <button class="btn btn-primary mb5" onclick="addUser(this)">新增</button>
                    </div>
                <?php endif; ?>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Close'); ?></button>
            </div>
        </div>
    </div>
</div>
<script>
    var c_operationid = 0;
    function show(operationid) {
        $('#notifyBtn').hide();
        c_operationid = operationid;
        $('#current_users').html('');
        var url = '<?php echo $this->createUrl('currentUser'); ?>';
        var params = {
            'operationid': operationid
        }
        $.get(url, params, function(data) {
            if (data.state === 'success') {
                if (data.data) {
                    $.each(data.data.userList, function(key, item) {
                        var span = '<span class="label label-info">' + item + '</span> ';
                        $('#current_users').append(span);
                        $('#notifyDate').text(data.data.notifyDate);
                        if (data.data.canNotify == true) {
                            $('#notifyBtn').removeAttr('disabled');
                            $('#notifyBtn').show();
                        } else {
                            $('#notifyBtn').attr('disabled', 'disabled');
                        }
                    });
                }

                $('#currentModel').modal('show');
                // 添加
            } else if (data.state === 'fail') {
                head.dialog.alert(data.message);
            }
        }, 'json');
    }

    function addUser(btn) {
        $(btn).attr('disabled', 'disabled');
        var uid = $('#uid').val();
        var operationid = c_operationid;
        if (!uid) {
            resultTip({
                'error': 'warning',
                'msg': '请先选择一个用户'
            })
        }
        var url = '<?php echo $this->createUrl('addUser'); ?>';
        var params = {
            'uid': uid,
            'operationid': operationid
        }
        $.post(url, params, function(data) {
            if (data.state === 'success') {
                var text = $('#uid :selected').text();
                var span = '<span class="label label-info">' + text + '</span> ';
                $('#current_users').append(span);
                resultTip({
                    msg: data.message
                });
                $('#currentModel').modal('hide');
                // 添加
            } else if (data.state === 'fail') {
                head.dialog.alert(data.message);
            }
            $(btn).removeAttr('disabled');
        }, 'json');

    }

    function notify(btn) {
        $(btn).attr('disabled', 'disabled');
        var operationid = c_operationid;
        var url = '<?php echo $this->createUrl('currentUser'); ?>';
        var params = {
            'operationid': operationid
        }
        $.post(url, params, function(data) {
            if (data.state === 'success') {
                resultTip({
                    msg: data.message
                });
            } else if (data.state === 'fail') {
                head.dialog.alert(data.message);
            }
        }, 'json');
    }
</script>