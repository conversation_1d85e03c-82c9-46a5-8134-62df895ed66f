<?php

/**
 * 根据不同校园过滤导航菜单
 */
class Variation{
    static function OAMenu($siteFlag, $menus){
        unset($menus['main']['user']['items']['lvot']);
        unset($menus['main']['campusw']['items']['lib']);
        switch($siteFlag){
            case 'ivygroup':
                unset($menus['main']['finance']['items']['D']);
                unset($menus['main']['finance']['items']['invoice']);
                unset($menus['campusOp']['basic']['items']['classroom']);
                unset($menus['campusOp']['grades']);
                unset($menus['teaching']['secondaryreport']);
//                unset($menus['teaching']['Student_Reports']);
                 unset($menus['teaching']['Student_Reports']['items']['0']);
                 unset($menus['teaching']['Student_Reports']['items']['2']);
//                unset($menus['teaching']['common']['items']['assessments']);
                unset($menus['teaching']['others']['items']['wida']);
                unset($menus['teaching']['others']['items']['academic']);
                unset($menus['campusOp']['advance']['items']['scholarship']);
                unset($menus['campusOp']['routine']['items']['childsign2']);
                unset($menus['teaching']['common']['items']['gradebook']);
                unset($menus['teaching']['common']['items']['attendance']);
                unset($menus['teaching']['common']['items']['student']);
                break;
            case 'longteng':
                unset($menus['main']['finance']['items']['D']);
                unset($menus['main']['finance']['items']['invoice']);
                unset($menus['campusOp']['basic']['items']['classroom']);
                unset($menus['campusOp']['grades']);
                unset($menus['campusOp']['routine']);
                unset($menus['campusOp']['support']);
                unset($menus['campusOp']['advance']);
            default:
                break;
            case 'daystar':
//                unset($menus['main']['campusw']['items']['teaching']);
                unset($menus['main']['hq']);
                // unset($menus['campusOp']['basic']['items']['prospect']);
//                unset($menus['main']['finance']['items']['A']);
//                unset($menus['main']['finance']['items']['B']);
                unset($menus['main']['finance']['items']['C']);
                unset($menus['main']['finance']['items']['gov']);
                unset($menus['campusOp']['routine']['items']['ot']);
//                unset($menus['campusOp']['routine']['items']['workflow']);
//                unset($menus['campusOp']['routine']['items']['feedback']);
                unset($menus['campusOp']['routine']['items']['hr']);
//                unset($menus['campusOp']['billing']);
//                unset($menus['campusOp']['support']['items']['catering']);
                unset($menus['campusOp']['support']['items']['points']);
                unset($menus['sysConfig']);
                unset($menus['campusOp']['routine']['items']['childsign']);
//                unset($menus['teaching']['weekly']);
//                $menus['teaching']['weekly']['url'] = array('//mteaching/daystarWeekly/index');
//                unset($menus['teaching']['monthly']);
                unset($menus['campusOp']['routine']['items']['qualityInspect']);

                break;
        }
        return $menus;
    }
}