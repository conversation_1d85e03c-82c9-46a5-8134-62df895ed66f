<h1>
<?php echo Yii::t("navigations","Payment"); ?>
</h1>
<?php if (Yii::app()->language == 'en_us'): ?>
<p>
	This category displays all the payment history, as well as the tuition and general credit.
</p>
<p>
	Click "Next" to see sub navigations of this category.
</p>
<p>
	Or jump to guide of other categories by clicking the links below:
</p>
<?php else: ?>
<p>
	你可以在该目录下查看历史付款记录以及预缴学费和个人账户信息等。
</p>
<p>
	请点击“下一步”查看该目录的具体项目。
</p>
<p>
	或者点击以下链接跳转到其他页面的向导：
</p>
<?php endif ?>
<ul class="list">
<?php foreach(Mims::Nav() as $key=>$nav):?>
	<li>
		<?php
		if($key == "portfolio"){
			$nav['url'] = array('//portfolio/portfolio/classes');
		}
		$url = array_merge($nav['url'], array("demo"=>"guide"));
		echo CHtml::link($nav['label'], $url);
		?>
	</li>
<?php endforeach;?>
</ul>