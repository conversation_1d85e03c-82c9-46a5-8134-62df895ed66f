<?php
/**
 * Message translations.
 *
 * This file is automatically generated by 'yiic message' command.
 * It contains the localizable messages extracted from source code.
 * You may modify this file by translating the extracted messages.
 *
 * Each array element represents the translation (value) of a message (key).
 * If the value is empty, the message is considered as not translated.
 * Messages that no longer need translation will have their translations
 * enclosed between a pair of '@@' marks.
 *
 * Message string can be used with plural forms format. Check i18n section
 * of the guide for details.
 *
 * NOTE, this file must be saved in UTF-8 encoding.
 *
 * @version $Id: $
 */
return array (
  'Card Type ID' => '',
  'ID' => '',
  'Barcode' => '图书条形码',
  'Book Title' => '图书名称',
  'Books Outstanding' => '已借图书',
  'Borrowed / Maximum' => '已借阅数量 / 可借阅总数',
  'Borrowed Number' => '借阅数量',
  'Card NO.' => '图书卡号',
  'Date Borrowed' => '借书时间',
  'Date Returned' => '还书时间',
  'Due Date' => '约定还书时间',
  'End Time' => '结束时间',
  'Expires' => '过期时间',
  'History of Books Borrowed' => '借阅历史',
  'Ivy Library Introduction' => '图书馆简介',
  'My Library Card' => '我的图书卡',
  'Our campus provides an extensive and open children’s library where every book has been carefully selected by our team of educational experts.  To get a library card or to find out more about the library, please contact our office staff.' => '校园为小朋友们提供了内容丰富的、开放式儿童图书馆。每一本书都是由我们的教育专家所精心挑选。图书卡办理、借阅规则和其他相关注意事项，请您联系校园支持团队。',
  'School ID' => '校园',
  'Start Time' => '开始时间',
  'Status' => '状态',
  'Update Time' => '更新时间',
  'User ID' => '用户ID',
  'User Name' => '用户名称',
  'User Type' => '用户类型',
  'You have no library card.' => '你还没有图书卡',
);
