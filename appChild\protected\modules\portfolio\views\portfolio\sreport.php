<?php
$this->breadcrumbs=array(
	'Portfolio'=>array('/portfolio/portfolio/classes'),
	Yii::t("navigations",'Semester Report'),
);?>

<div id="left-col" class="no-bg span-6">
    <ul class="sub-nav report-list">
        <?php foreach ($reportList as $sy=>$ms):?>
        <li role="yearly">
            <?php
            echo CHtml::link(IvyClass::formatSchoolYear($sy),
                array('//portfolio/portfolio/semester', 'startyear'=>$sy));
            echo '<em></em>';
            ?>
            <ul class="sub-group">
                <?php
                    $reportTitles = array(
                        '2' => Yii::t("portfolio", 'Spring Semester'),
                        '1' => Yii::t("portfolio", 'Fall Semester')
                    );
                    foreach($reportTitles as $_k => $_t):
                ?>
                        <?php if (isset($ms[$_k])):?>
                        <li role="sub">
                            <?php
                            echo CHtml::link(
                                $_t,
                                array('//portfolio/portfolio/semester', 'startyear'=>$sy, 'semester'=>$_k),
                                array('data-report'=>sprintf('%d-%d', $sy, $_k),
                                    'data-report-id'=>$ms[$_k]
                                )
                            );
                            ?>
                        </li>
                    <?php endif;?>

                <?php
                    endforeach;
                ?>

            </ul>

        </li>
        <?php endforeach;?>
    </ul>
</div>

<div id="main-col" class="span-18 last">
    <?php if (isset($reportList[$startyear])&&$reportList[$startyear][$semester]):?>
        <?php $params = array(
            'data' => $data);
        ?>
        <?php
        if (!$report->custom && in_array($report->template_id, array('ivy01', 'Ivy01'))):?>
            <div class="semester-report">
                <div class="head">
                </div>
                <div class="main-content">
                    <?php
                    $this->widget('ext.OrbitSlider.OrbitSlider', array(
                        'content'=>array(
                            array(
                                'content'=>$this->renderPartial("_sreport_p1", $params , true),
                                'style'=>'background-color:white;',
                                'caption'=>sprintf(Yii::t("portfolio",'Page %d'), 1),
                            ),
                            array(
                                'content'=>$this->renderPartial("_sreport_p2", $params , true),
                                'style'=>'background-color:white;display:none;',
                                'caption'=>sprintf(Yii::t("portfolio",'Page %d'), 2)
                            ),
                            array(
                                'content'=>$this->renderPartial("_sreport_p3", $params , true),
                                'style'=>'background-color:white;display:none;',
                                'caption'=>sprintf(Yii::t("portfolio",'Page %d'), 3)
                            ),
                            array(
                                'content'=>$this->renderPartial("_sreport_p4", $params , true),
                                'style'=>'background-color:white;display:none;',
                                'caption'=>sprintf(Yii::t("portfolio",'Page %d'), 4)
                            ),
                        ),
                        'slider_options'=>array(
                            'animation'=>'horizontal-slide',
                            'bullets'=>true,
                            'directionalNav'=>true,
                            'advanceSpeed'=>'5000',
                            'timer'=>false,
                            //'directionalNav'=>false,
                        ),
                        'width'=>'680',
                        'height'=>'1000',
                    ));
                    ?>
                    <div class="clear"></div>
                </div>
                <div class="foot"></div>
            </div>

            <?php if(!Mims::unIvy()):?>
            <div style="margin-top: 15px;">
                <p>
		            <span class="btn001">
                    <?php echo CHtml::link('<span>'.Yii::t("portfolio", 'Download PDF').'</span>', Yii::app()->getController()->createUrl('//portfolio/portfolio/semesterpdf', array('id'=>$data['report']['id'])))?>
		            </span>
                </p>
            </div>
            <?php endif;?>


        <?php else:?>

            <div class="semester-report">
                <div class="head"></div>
                <div class="main-content">
                    <div id="page-1" style="width: 680px;height: 380px;">
                        <div class="span-11">
                            <ul class="basic">
                                <li><label><?php echo Yii::t("labels", 'Child Name');?></label>
                                    <?php echo $data['child']->getChildName()?></li>
                                <li><label><?php echo Yii::t("labels", 'Date of Birth');?></label>
                                    <?php echo $data['child']->birthday_search?></li>
                                <li><label><?php echo Yii::t("labels", 'Campus');?></label>
                                    <?php echo $data['campusTitle']?></li>
                                <li><label><?php echo Yii::t("portfolio", 'Semester');?></label>
                                    <?php echo IvyClass::formatSchoolYear($data['report']['startyear'])?>
                                    <?php echo ($data['report']['semester'] == 1) ? Yii::t("portfolio", 'Fall') : Yii::t("portfolio", 'Spring');?></li>
                                <li><label><?php echo Yii::t("labels", 'Class');?></label>
                                    <?php echo $data['classTitle']?></li>
                                <?php
                                $idx = 0;
                                $criteria = new CDbCriteria();
                                $criteria->compare('classid', $data['report']['classid']);
                                $criteria->order = 't.weight asc';
                                $classteacher = ClassTeacher::model()->with('userWithProfile')->findAll($criteria);
                                if ($classteacher):?>
                                    <?php foreach ($classteacher as $teacher):?>
                                        <?php if($idx++ == 0):?>
                                            <li><label><?php echo Yii::t("portfolio", 'Class Teachers');?></label><?php echo $teacher->userWithProfile->getName();?></li>
                                        <?php else:?>
                                            <li><label>&nbsp;</label><?php echo $teacher->userWithProfile->getName();?></li>
                                        <?php endif;?>
                                    <?php endforeach;?>
                                <?php endif;?>
                            </ul>
                        </div>
                        <div class="span-6 last">
                            <p>
                                <?php echo CHtml::image(CommonUtils::childPhotoUrl($this->myChildObjs[$this->getChildId()]->photo), $this->myChildObjs[$this->getChildId()]->getChildName() );?>
                            </p>
                            <div>
                                <ul class="pta-items">
                                    <li>
                                        <span><?php echo CHtml::link('<span>'.Yii::t("portfolio", "View Report").'</span>',
                                                $this->createUrl('/portfolio/portfolio/semesterpdf', array('id'=>$data['report']['id'], 'f'=>1)), array('target'=>'_blank'))?></span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="foot"></div>
            </div>

        <?php endif;?>

    <?php else:?>
        <p>
            <?php echo Yii::t("portfolio","The Semester Report will be available online after Parent Teacher Conferences near the end of the semester. Please check back later.");?>
        </p>
    <?php endif;?>
</div>


<style>
    .pta-items li span a{
        text-decoration:none;
    }
    .pta-items li span a span{
        display: block;
        text-align: center;
        padding: 20px 0;
        background: #F36601;
        margin-bottom: 6px;
        font-size: 20px;
        color:#fff;
    }
</style>

<script>
    $(function(){
        var currentReportId = <?php echo $data['report']['id'] ;?>;
        var activeLink = $('.report-list').find('a[data-report-id|="'+currentReportId+'"]');
        activeLink.parents('li[role|="sub"]').addClass('active');
        activeLink.parents('li[role|="yearly"]').addClass('active');
    })
</script>
