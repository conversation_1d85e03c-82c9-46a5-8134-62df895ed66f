<?php
/* 
 * 银行转帐控制器
 */
class BankController extends BranchBasedController
{
    public $actionAccessAuths = array(
        'index'                 => 't_F_Finance',
        'searchData'            => 't_F_Finance',
        'searchInvoiceData'     => 't_F_Finance',
        'saveInvoiceData'       => 't_F_Finance',
    );

    public $menuItem;
    public $selectMenu;
    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'main';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Finance Mgt');
        
        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $this->branchSelectParams['urlArray'] = array('//mfinance/bank/index');
        
        $this->menuItem = array(
            'index'=>Yii::t('site', '银行转账'),
            'list'=>Yii::t('site', '转账列表'),
        );
        Yii::import('common.models.invoice.*');

    }
    
    public function createUrl($route, $params = array(), $ampersand = '&', $parentOnly = false){
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        if (empty($params['selectMenu'])) {
            $params['selectMenu'] = $_GET['selectMenu'] ? $_GET['selectMenu'] : 'index';
            if ($_GET['selectMenu'] == 'list') {
                if (!empty($_GET['start'])) {
                    $params['start'] = $_GET['start'];
                }
                if (!empty($_GET['end'])) {
                    $params['end'] = $_GET['end'];
                }
            }
        }
        return parent::createUrl($route, $params, $ampersand);
    }

	public function actionIndex(){
        $id = Yii::app()->request->getParam('id',null);
        $id = trim($id);
        $model = new Banktransfer('search');
        $model->unsetAttributes();
        $model->ostatus = Banktransfer::BANKTRANSFER_STATUS_YES;
        if (!empty($id)){
            $model->id = $id;
        }
        if(isset($_GET['Banktransfer']))
            $model->attributes = $_GET['Banktransfer'];
        $model->schoolid = $this->branchId;
        $this->selectMenu = Yii::app()->request->getParam('selectMenu','index');
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/jquery.number.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/jquery.printThis.js');
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/xlsx.full.min.js');
        $this->render('index',array(
            'model'=>$model
        ));
	}
    
    //查询交易流水号
    public function actionSearchData(){
        $request = Yii::app()->request;
        $bankTransferId = $request->getPost('id', '');
        $bankTransferId = trim($bankTransferId);
        $ret = array();
        if ($request->isAjaxRequest && !empty($bankTransferId)){
            $model = Banktransfer::model()->findByPk($bankTransferId);
            if (empty($model)){
                $model = new Banktransfer();
                $model->id = $bankTransferId;
                $ret['new'] = 1;
                $ret['data'] = $model->getAttributes();
            }else{
                if ($model->ostatus == Banktransfer::BANKTRANSFER_STATUS_YES){
                    $ret['new'] = 2;
                    $ret['status'] = Banktransfer::BANKTRANSFER_STATUS_YES;
                    $ret['data'] = $model->getAttributes();
                }else{
                    $ret['new'] = 0;
                    $ret['status'] = Banktransfer::BANKTRANSFER_STATUS_NO;
                    $ret['data'] = $model->getAttributes();
                    $ret['data']['btimestamp'] = date('Y-m-d',$model->btimestamp);
                    //取孩子信息
                    if ($model->childid){
                        $ftsChilds = FtsChild::model()->findAllByPk(explode(',', $model->childid));
                        $option = '';
                        foreach ($ftsChilds as $val){
                            preg_match('/{(.*)}/', $val->tdata, $matches);
                            $_dataStr = $matches[1];
                            $option .= '<option value ="'.$val->id.'" selected="selected">'.$_dataStr.'</option>'; 
                        }
                        $ret['data']['option'] = $option;
                    }
                }
            }
        }
        $this->addMessage('state', 'success');
        $this->addMessage('message', Yii::t('message','success'));
        $this->addMessage('data',$ret);
        $this->addMessage('callback','backBankInfo');
        $this->showMessage();
    }
    
    /*
     * 过滤帐单信息
     */
    public function actionSearchInvoiceData(){
        $ret = array();
        $bankTransferId = ($_POST['Banktransfer']['id']) ? $_POST['Banktransfer']['id'] : '';
        $bankTransferId = trim($bankTransferId);
        if (Yii::app()->request->isAjaxRequest && !empty($bankTransferId)){
            $model = Banktransfer::model()->findByPk($bankTransferId);
            if (empty($model)){
                $model = new Banktransfer();
            }
            $model->attributes = $_POST['Banktransfer'];
            $childIdList = $model->childid;
            $model->btimestamp = ($model->btimestamp) ? strtotime($model->btimestamp) : '';
            $model->childid = ($model->childid) ? implode(',', $model->childid) : '';
            $model->otimestamp = time();
            $model->userid = Yii::app()->user->getId();
            $model->schoolid = $this->branchId;
            if ($model->validate()){
                if ($model->save(false)){
                    $ret['bankInfo'] = $model->getAttributes();
                    $criteria = new CDbCriteria();
                    $criteria->compare('t.childid', $childIdList);
                    $criteria->compare('t.schoolid', $this->branchId);
                    $criteria->compare('t.status', array(Invoice::STATS_UNPAID,Invoice::STATS_PARTIALLY_PAID));
                    $criteria->order = 't.childid ASC,t.startdate ASC';
                    $invoiceList = Invoice::model()->with('childprofile','invoiceTransaction')->findAll($criteria);

                    if (!empty($invoiceList)){
                        foreach ($invoiceList as $val){
                            $ret['invoiceList'][] = array(
                                'invoiceId'=>$val->invoice_id,
                                'childid'=>$val->childid,
                                'childName'=>$val->childprofile->getChildName(),
                                'schoolid'=>$val->school->title,
                                'title'=>$val->title,
                                'amount'=>$val->amount,
                                'payed'=>$val->renderPaidAmount($val),
                                'unpay'=>$val->renderDueAmount($val),
                            );
                            
                            $ret['childList'][$val->childid]['childid'] = $val->childid;
                            $ret['childList'][$val->childid]['childName'] = $val->childprofile->getChildName();
                        }
                    }
                }
            }else{
                $error = current($model->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('message',$error ? $error[0] :'填写必填项！');
                $this->showMessage();
            }
        }

        $this->addMessage('state', 'success');
        $this->addMessage('message', Yii::t('message','success'));
        $this->addMessage('data',$ret);
        $this->addMessage('callback','backInvoiceInfo');
        $this->showMessage();
    }
    
    //帐单保存
    public function actionSaveInvoiceData(){
        $invoiceIds = Yii::app()->request->getPost('invioceId',null);
        $transferid = Yii::app()->request->getPost('transferid',null);
        $childCredit = Yii::app()->request->getPost('childCredit',null);
        $sendMail = Yii::app()->request->getPost('sendMail',null);
        $transferid = trim($transferid);
        Yii::import('application.components.policy.PolicyApi');
        if (Yii::app()->request->isAjaxRequest && $invoiceIds !== null && $transferid !== null){
            $model = Banktransfer::model()->findByPk($transferid);
            if (!empty($model) && $model->ostatus == Banktransfer::BANKTRANSFER_STATUS_NO){
                $modelArr = Invoice::model()->with('childprofile')->findAllByPk($invoiceIds);
                $creditChildInvoiceList = array();
                $invoiceList = array();
                $sumList = array();
                $childIds = array();
                $childName = array();
                $policyApi = new PolicyApi($this->branchId);
                global $paymentMappings;
                $dbFeeType = array_flip($paymentMappings['dbFeeType']);
                $titleFeeType = $paymentMappings['titleFeeType'];
                if (!empty($modelArr)){
                    foreach ($modelArr as $val){
                        if (!empty($childCredit)){
                            if ($childCredit == $val->childid){
                                $creditChildInvoiceList[$val->invoice_id] = $val;
                            }else{
                                $invoiceList[$val->invoice_id] = $val;
                            }
                        }else{
                           $invoiceList[$val->invoice_id] = $val;
                        }
                        $childIds[$val->childid][$val->payment_type]['title'] = $titleFeeType[$dbFeeType[$val->payment_type]];
                        $childName[$val->childid] = $val->childprofile->getChildName();
                        $dealWith = $val->amount - $val->paidSum;
                        $sumList['sum'] += $dealWith;
                        $sumList['invoiceSum'][$val->invoice_id]= isset($sumList['invoiceSum'][$val->invoice_id]) ? $sumList['invoiceSum'][$val->invoice_id]+$dealWith : $dealWith;
                    }
                    if (count($modelArr)>1){
                        if ($model->bamount-number_format($sumList['sum'],2,'.','') < 0){
                            $this->addMessage('state', 'fail');
                            $this->addMessage('message','系统不支持部分付清！');
                            $this->showMessage();
                        }
                    }
                    
                    //判断多个孩子的情况
                    if ($model->bamount-$sumList['sum']>0.001){
                        if (count($childIds)>1){
                            if (!empty($childCredit)){
                                $this->saveInvoiceData($policyApi,$invoiceList, $model, $sumList,$creditChildInvoiceList);
                            }else{
                                $this->addMessage('state', 'fail');
                                $this->addMessage('message','请指定剩余金额冲入哪个孩子的个人账户！');
                                $this->showMessage();
                            }
                        }else{
                            $this->saveInvoiceData($policyApi,$invoiceList, $model, $sumList);
                        }
                    }else{
                        $this->saveInvoiceData($policyApi,$invoiceList, $model, $sumList);
                    }
                    //查询流水数据
                    $invoiceList = Invoice::model()->with('invoiceTransaction')->findAllByPk($invoiceIds);
                    //向中间表插入数据
                    foreach ($invoiceList as $invoice){
                        $tranIds = BanktransferDetail::model()->getTrantionIdByInvoice($invoice->invoice_id);
                        foreach ($invoice->invoiceTransaction as $tran){
                            //判断
                            if (($tran->transactiontype == InvoiceTransaction::TYPE_TRANSFER) && (!in_array($tran->id, $tranIds))){
                                $detail = new BanktransferDetail();
                                $detail->transferid = $model->id;
                                $detail->invoiceid = $invoice->invoice_id;
                                $detail->tranid = $tran->id;
                                $detail->childid = $invoice->childid;
                                if (!$detail->save()){
                                    $this->addMessage('state', 'fail');
                                    $this->addMessage('message','更新数据失败！请联系IT人员');
                                    $this->showMessage();
                                }
                            }
                        }
                    }
                    $model->title = $this->getTitle($childIds, $childName);
                    $model->ostatus = Banktransfer::BANKTRANSFER_STATUS_YES;
                    if (!$model->save()){
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message','更新数据失败！请联系IT人员');
                        $this->showMessage();
                    }else{
                        if ($sendMail) {
                            PolicyApi::transferToEmail(array($model->id), 'parent', true, OA::isProduction());
                        }
                        $this->addMessage('state', 'success');
                        $this->addMessage('message','操作成功！');
                        $this->addMessage('refresh',true);
                        $this->addMessage('referer',$this->createUrl('//mfinance/bank/index',array('selectMenu'=>'list')));
                        $this->showMessage();
                    }
                }
            }
        }else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message','请选择账单，再确认付款！');
            $this->showMessage();
        }
    }

    public function actionOrderInfo()
    {
        $id = Yii::app()->request->getparam('id', 0);
        $id = trim($id);
        $bankModel = Banktransfer::model()->findByPk($id);
        $bankDetails = BanktransferDetail::model()->findAllByAttributes(array('transferid' => $id));
        $tranIds = array();
        foreach ($bankDetails as $item) {
            $tranIds[] = $item->tranid;

        }
        $tranModels = InvoiceTransaction::model()->findAllByPk($tranIds);
        $this->renderPartial('_orderinfo', array('tranModels' => $tranModels, 'bankModel' => $bankModel));
    }
    
    /*
     * 公共存账单保存方法
     */
    public function saveInvoiceData($policyApi,$modelList,$model,$sumList,$creditChildList=null){
        $balanceAmount = 0;
        $payedAmount = 0;
        if (empty($creditChildList)){
            $InvoiceKeys = array_keys($modelList);
            $lastInvoiceId = end($InvoiceKeys);
        }
        foreach ($modelList as $invoiceId=>$invoiceObj){
            $balanceAmount = $model->bamount - $payedAmount;
            if (empty($creditChildList)){
                $payBalanceAmount = ($lastInvoiceId==$invoiceId) ? $balanceAmount : $sumList['invoiceSum'][$invoiceId];
            }else{
                $payBalanceAmount = $sumList['invoiceSum'][$invoiceId];
            }
            $ret = $policyApi->Pay(array($invoiceObj), InvoiceTransaction::TYPE_TRANSFER,$payBalanceAmount);
            if ($ret != 0){
                $this->addMessage('state', 'fail');
                $this->addMessage('message','更新数据失败！请联系IT人员');
                $this->showMessage();
            }else{
                $payedAmount = $payedAmount+$payBalanceAmount;
            }
        }
        if ($creditChildList !== null){
            $InvoiceKeys = array_keys($creditChildList);
            $lastInvoiceId = end($InvoiceKeys);
            foreach ($creditChildList as $invoiceId=>$invoiceObj){
                $balanceAmount = $model->bamount - $payedAmount;
                $ret = $policyApi->Pay(array($invoiceObj), InvoiceTransaction::TYPE_TRANSFER,($lastInvoiceId==$invoiceId) ? $balanceAmount : $sumList['invoiceSum'][$invoiceId]);
                if ($ret != 0){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message','更新数据失败！请联系IT人员');
                    $this->showMessage();
                }else{
                    $payedAmount = $payedAmount + $sumList['invoiceSum'][$invoiceId];
                }
            }
        }
        return true;
    }
    
    /*
     * 获得关联账单信息
     */
    public function getInvoiceInfo($data){
        $ret = CHtml::openTag('ol');
        foreach ($data->banktransferDetail as $val){
            $ret .= CHtml::openTag('li').CHtml::link($val->invoice->title,$this->createUrl('//child/invoice/viewInvoice',array('invoiceid'=>$val->invoice->invoice_id,'childid'=>$val->invoice->childid)),array('class'=>'J_dialog')).CHtml::closeTag('li');
        }
        $ret .= CHtml::closeTag('ol');
        echo $ret;
    }

    /*
     * 获得转账信息
     */
    public function getOrderInfo($data){
        echo "<a href='javascript:void(0)' onclick='viewOrder(this)'>$data->id</a>";
    }
    
    /*
     * 标题
     */
    public function getTitle($childIds,$childName){
        $title = '';
        foreach ($childIds as $key=>$payment){
            $mt = '[';
            foreach ($payment as $val){
                $mt .= ($mt == '[') ? $val['title'] : ','.$val['title'];
            }
            $mt .= ']';
            $title .= $childName[$key].$mt;
        }
        return $title;
    }
}
