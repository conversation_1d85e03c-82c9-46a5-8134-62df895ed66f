<?php

class NoticeController extends TeachBasedController
{
    public function createUrlReg($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

//        $this->modernMenuFlag = 'teaching';
//        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
//        $this->modernMenuCategoryTitle = Yii::t('site', 'Teaching Tasks');
        $schoolList = self::allSchoolList();
        foreach ($this->accessBranch as $key => $item) {
            if (!in_array($item, $schoolList)) {
                unset($this->accessBranch[$key]);
            }
        }
        //初始化选择校园页面
        $this->branchSelectParams['urlArray'] = array('//superman/notice/index',);
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl() . '/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/vue2.js');
    }


    //展示页面
    public function actionIndex()
    {
        $res = CommonUtils::requestDsOnline('notice/signatory/'.$this->branchId);
        $model = new User();
        $this->render('index', array(
            'model' => $model,
            'data' => array_values($res['user_info']),
        ));

    }
    //添加签署人
    public function actionAddNoticeSignatory()
    {
        $user_id = Yii::app()->request->getParam("user_id");
        $res = CommonUtils::requestDsOnline('notice/addSignatory',array(
            'school_id'=>$this->branchId,
            'user_id'=>$user_id
        ));
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', '添加成功');
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '添加失败');
        }
        $this->showMessage();
    }
    //删除签署人
    public function actionDelNoticeSignatory()
    {
        $user_id = Yii::app()->request->getParam("user_id");
        $res = CommonUtils::requestDsOnline('notice/delSignatory',array(
            'school_id'=>$this->branchId,
            'user_id'=>$user_id
        ));
        if($res){
            $this->addMessage('state', 'success');
            $this->addMessage('message', '删除成功');
            $this->showMessage();
        }
    }

    public static function allSchoolList()
    {
        return array_merge(self::dsSchoolList(), self::ivySchoolList());
    }

    public static function dsSchoolList()
    {
        return array(
            'BJ_DS',
            'BJ_SLT',
            'BJ_QFF',
        );
    }

    public static function ivySchoolList()
    {
        return array(
            'BJ_IASLT',
        );
    }
}
