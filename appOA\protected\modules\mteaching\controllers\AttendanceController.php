<?php

class AttendanceController extends BranchBasedController
{
    public $siderMenu = array();
    public $tid = -1;
    public $timetable = '';
    public $allTimetable = array();
    public $printFW = array();
    // 访问action的初级权限
    public $actionAccessAuths = array(
        // 教师
//        'index'  => 'ivystaff_teacher',
//        'courseStudent'  => array('ivystaff_teacher', 'o_MS_TimetableMgt'),
//        'updateRecords'  => 'ivystaff_teacher',
        // 工作人员
        'student' => 'o_MS_Timetable_Attendance',
//        'showCourseChild' => 'o_MS_TimetableMgt',
        'childCourseList' => 'o_MS_TimetableMgt',
        'updateChildCourse' => 'o_MS_TimetableMgt',
        'deleteChildCourse' => 'o_MS_TimetableMgt',
        'childRecords' => 'o_MS_Timetable_Attendance',
        'updateRecordsAll' => 'o_MS_Timetable_Attendance',
        'updateRecordsOne' => 'o_MS_Timetable_Attendance',
        'showAttendStudent' => 'o_MS_Timetable_Attendance',
    );
    public function createUrl($route, $params = array(), $ampersand = '&', $parentOnly = false)
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        if (empty($params['tid'])) {
            $params['tid'] = $this->tid;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue2.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl.'/base/css/bootstrap-select.min.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/bootstrap-select.min.js');
        $this->modernMenuFlag = 'teaching';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Teaching Tasks');

        //初始化选择校园页面
//        $this->branchSelectParams['hideOffice'] = true;
//        $this->branchSelectParams['urlArray'] = array('//mteaching/attendance/new');
        //标签字体需要的文件
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/label/iconfont.css?v=20231124');
        Yii::import('common.models.timetable.*');
        Yii::import('common.models.portfolio.*');

        $this->siderMenu = array(
            'attendance' => array(
                'label' => Yii::t('campus', 'Student Attendance'),
                'url' => array('//mteaching/attendance/new'),
            ),
            'courseAlias' => array(
                'label' => Yii::t('attends', 'My Courses'),
                'url' => array('//mteaching/attendance/courseAlias'),
            ),
        );

    }

    public function beforeAction($action)
    {
        if(!parent::beforeAction($action)){
            return false;
        }

        $this->getCalendars();

        $this->tid = Yii::app()->request->getParam('tid', '-1');

        $criteria = new CDbCriteria();
        $criteria->compare('schoolid', $this->branchId);
        $criteria->compare('status', 1);
        $timetables = Timetable::model()->findAll($criteria);

        if ($this->branchId == 'BJ_SLT') {
            unset($this->modernMenu['teaching']['common']['items']['attendance']);
        }

        if (!$timetables) {
            return false;
        }

        foreach ($timetables as $timetable) {
            $this->allTimetable[$timetable->id] = array(
                'yid' => $timetable->yid,
            );
            if ($this->tid == $timetable->id) {
                $this->timetable = $timetable;
            }
        }
        if (!$this->timetable) {
            $this->timetable = $timetables[0];
            $this->tid = $timetables[0]->id;
        }
        return true;
    }

    public function actionNew()
    {
        $tid = Yii::app()->request->getParam('tid', '');
        $teacherId = Yii::app()->request->getParam('teacherId', '');
        $weeknumber = Yii::app()->request->getParam('weeknumber', '');

        $tids = $tid ? $tid : $this->tid ;

        $uid = $teacherId ? $teacherId : Yii::app()->user->id;
        $userModel = User::model()->findByPk($uid);
        //todo :: 8030837 临时打开替换老师权限 带庞静通知时删除
        $canReplace = (Yii::app()->user->checkAccess('ivystaff_it') || in_array(Yii::app()->user->id,array('8032242','8030837', '8039871')));

        $this->render('new', array(
            'schoolid' => $this->branchId,
            'tid' => $tids,
            'userModel' => $userModel,
            'uid' => $uid,
            'teacherId' => $teacherId,
            'weeknumber' => $weeknumber,
            'canReplace' => $canReplace,
        ));
    }

    public function actionTeacherInfo()
    {
        $tid = Yii::app()->request->getParam('tid', '');
        $teacherId = Yii::app()->request->getParam('teacherId', '');
        $weeknumber = Yii::app()->request->getParam('weeknumber', '');

        $res = CommonUtils::requestDsOnline('timetable/teacherInfo', array(
            'branchId' => $this->branchId,
            'tid' => $tid,
            'teacherId' => $teacherId,
            'weeknumber' => $weeknumber,
        ), 'get');

        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    /**
     * 我的课程（老师） 转发到laravel
     */
    public function actionMyCourse(){
        $teacher_id = Yii::app()->request->getParam('teacherId', '');
        $tid = Yii::app()->request->getParam('tid', '');
        $teacher_id = ($teacher_id) ?: Yii::app()->user->id;
        $tid = ($tid) ?: $this->tid;
        $res = CommonUtils::requestDsOnline("timetable/myCourse",array(
            'branchId' => $this->branchId,
            'tid' => $tid,
            'teacherId' => $teacher_id,
        ),'get');
        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    public function actionStudentSituation()
    {
        $tid = Yii::app()->request->getParam('tid', '');
        $teacherId = Yii::app()->request->getParam('teacherId', '');
        $weekMon = Yii::app()->request->getParam('weekMon', '');

        $res = CommonUtils::requestDsOnline('timetable/studentSituation', array(
            'branchId' => $this->branchId,
            'tid' => $tid,
            'teacherId' => $teacherId,
            'weekMon' => $weekMon,
        ), 'get');

        if ($res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    public function actionCourseStudentNew()
    {
        $weekday_period = Yii::app()->request->getParam('weekday', ''); // 例如 1-2
        $course_code = Yii::app()->request->getParam('course_code', ''); // 课程的code
        $datatime = Yii::app()->request->getParam('datatime', ''); // 选的时间
        $tid = Yii::app()->request->getParam('tid', '');
        $uid = Yii::app()->request->getParam('uid', '');
        $next_same = false;#下节课相同
        $tids = !empty($tid) ? $tid : $this->tid ;
        $studentList = array();
        $studentList_t = array();
        $courseModel = array();
        $recordsArr = array();
        $numberTypes = array(
            TimetableRecords::ATTENDANCE_STATUS => 0,
            TimetableRecords::LATE_STATUS => 0,
            TimetableRecords::LEAVE_STATUS => 0,
            TimetableRecords::ABSENTEE_STATUS => 0,
            0 => 0,
            1 => 0,
            60=>0,//未使用电子设备
            61=>0,//使用电子设备
        );
        //不同类型的出勤对应的统计类型计数
        $count_types_map = array(
            31=>TimetableRecords::LEAVE_STATUS,//病假=>事假
            11=>TimetableRecords::ATTENDANCE_STATUS,//线上出勤=>出勤
            41=>TimetableRecords::ABSENTEE_STATUS,//校内停学=>旷课
            42=>TimetableRecords::ABSENTEE_STATUS,//校外停学=>旷课
        );
        if($course_code){
            // 根据时间判断是第一学期还是第二学期
            $semesterId = $this->getSemesterIndex($this->timetable->yid, strtotime($datatime));
            $studentDataModel = $this->getStudentCourseData($tids, $course_code, $semesterId);
            //本学年的学期开始结束时间
            $crit = new CDbCriteria;
            $crit->compare('yid', $this->timetable->yid);
            $calendar_list = Calendar::model()->find($crit);
            $timepoints = explode(',',$calendar_list->timepoints);
            if(strtotime($datatime) >= $timepoints[0] && strtotime($datatime) < $timepoints[2]){
                //第一学期
                $start_time = $timepoints[0];
                $end_time = $timepoints[1];
            }else{
                //第二学期
                $start_time = $timepoints[2];
                $end_time = $timepoints[3];
            }
            //本学期学生着装违规总次数
            $criteria = new CDbCriteria;
            $criteria->select='childid,COUNT(*) as dress';//必须是表里面存在的字段否则无法取到值
            $criteria->compare('school_id', $this->branchId);
            $criteria->compare('status', 10);
            $criteria->compare('period', 1);
            $criteria->compare('dress', 1);
            $criteria->compare('target_date', ">=".$start_time);
            $criteria->compare('target_date', "<=".$end_time);
            $criteria->index='childid';
            $criteria->group='childid';
            $dress_model = TimetableRecords::model()->findAll($criteria);
            $dressCode_violation_num = array();
            foreach ($dress_model as $child=>$item){
                $dressCode_violation_num[$child] = $item->dress;
            }
            $childids = array();
            foreach ($studentDataModel as $item){
                $childids[] = $item->child_id;
            }
            //获取孩子标签
            $res = CommonUtils::requestDsOnline2('label/allChild', array(
                'child_ids'=>$childids,
            ), 'get');
            foreach ($studentDataModel as $val) {
                if(in_array($val->childProfile->status, array(ChildProfileBasic::STATS_ACTIVE_WAITING, ChildProfileBasic::STATS_ACTIVE))){
                    $studentList[$val->child_id] = array(
                        'class_room' => $val->class_room,
                        'course_code' => $val->course_code,
                        'childName' => isset($val->childProfile) ? $val->childProfile->getChildName(false,false,true) : "",
                        'childLabel' =>  empty($res['data'][$val->child_id]['label']) ?  '' : $res['data'][$val->child_id]['label'],
                        'photo' => isset($val->childProfile) ? $val->childProfile->photo : "",
                        'gender' => isset($val->childProfile) ? $val->childProfile->gender : "",
                    );
                    asort($studentList);
                }else{
                    $studentList_t[$val->child_id] = array(
                        'class_room' => $val->class_room,
                        'course_code' => $val->course_code,
                        'childName' => isset($val->childProfile) ? $val->childProfile->getChildName(false,false,true) : "",
                        'childLabel' => empty($res['data'][$val->child_id]['label']) ?  '' : $res['data'][$val->child_id]['label'],
                        'photo' => isset($val->childProfile) ? $val->childProfile->photo : "",
                        'gender' => isset($val->childProfile) ? $val->childProfile->gender : "",
                    );
                    asort($studentList_t);
                }
            }
            $recordsArr = array();
            $die = 5;
            foreach ($studentList as $childid => $val){
                $dress_num = $dressCode_violation_num[$childid];
                if($dress_num > $die){
                    $dress_modulo = $dress_num%$die; //记录？次
                    $dress_trunc = floor($dress_num/$die);//已违反？次
                }else{
                    $dress_modulo = $dress_num;
                    $dress_trunc = 0;
                }

                $recordsArr[$childid] = array(
                    'type' => 10,
                    'memo' => '',
                    'late_time' => '',
                    'dress' => '',
                    'electronic' => 0,
                    'is_admin' => 0,
                    'dress_num'=>$dress_num,//着装违规次数
                    'dress_modulo'=>$dress_modulo,//记录？次
                    'dress_trunc'=>$dress_trunc,//已违反？次
                );
            }
            $criteria = new CDbCriteria;
            $criteria->compare('course_code', $course_code);
            $courseModel = TimetableCourses::model()->find($criteria);
            if($weekday_period && $datatime){
                $weekday = substr( $weekday_period, 0, 1 );
                $period = substr($weekday_period,-1,1);
                $next_period = $period+1;
                $criteria = new CDbCriteria;
                $criteria->compare('course_code', $course_code);
                $criteria->compare('weekday', $weekday);
                $criteria->compare('period', $period);
                $criteria->compare('target_date', strtotime($datatime));
                $criteria->compare('school_id', $this->branchId);
                $criteria->compare('tid', $tids);
                $criteria->compare('status', 10);
                $model = TimetableRecords::model()->findAll($criteria);
                if($model){
                    foreach ($model as $val){
                        $dress_num = $dressCode_violation_num[$val->childid];
                        $recordsArr[$val->childid] = array(
                            'type' => $val->type,
                            'memo' => $val->memo,
                            'late_time' => $val->late_time,
                            'dress' => $val->dress,
                            'electronic' => $val->electronic,
                            'is_admin' => $val->is_admin,
                            'dress_num'=>$dress_num,
                            'dress_modulo'=>$recordsArr[$val->childid]['dress_modulo'],
                            'dress_trunc'=>$recordsArr[$val->childid]['dress_trunc'],
                        );
                        if(isset($studentList_t) && isset($studentList_t[$val->childid])){
                            $studentList[$val->childid] = $studentList_t[$val->childid];
                        }
                        asort($studentList);
                        if(!empty($count_types_map[$val->type])){
                            $numberTypes[$count_types_map[$val->type]] += 1;
                        }else{
                            $numberTypes[$val->type] += 1;
                        }
                        //违规使用电子设备
                        if($val->electronic==60){
                            $numberTypes[60] += 1;
                        }elseif(in_array($val->electronic,array(61,62,63))){
                            $numberTypes[61] += 1;
                        }
                        $numberTypes[$val->dress] += 1;
                    }
                }

                $date = new DateTime($datatime);
                $date->modify('this week monday');
                $monday = $date->format('Ymd');
                $crit = new CDbCriteria;
                $crit->compare('tid', $tids);
                $crit->compare('monday', $monday);
                $yearlySchedule = TimetableYearlySchedule::model()->find($crit);
                $schedule = empty($yearlySchedule->schedule) ? 'A' : $yearlySchedule->schedule;
                $crit = new CDbCriteria;
                $crit->compare('course_code',$course_code);
                $crit->compare('tid',$tids);
                $crit->compare('category',$schedule);
                $crit->compare('weekday',$weekday);
                $crit->compare('period',$next_period);
                $next_same = TimetableCourseData::model()->exists($crit);
            }
        }
        $status = 0;
        if($uid == Yii::app()->user->id || Yii::app()->user->checkAccess('o_MS_TimetableMgt')){
            $status = 1;
        }
        $this->renderPartial('courseStudentNew', array(
            'studentList' => $studentList,
            'datatime' => $datatime,
            'weekday' => $weekday_period,
            'course_code' => $course_code,
            'courseModel' => $courseModel,
            'recordsArr' => $recordsArr,
            'numberTypes' => $numberTypes,
            'status' => $status,
            'uid' => $uid,
            'next_same' => $next_same,#下节课是否和当前相同
        ));
    }

    //获取会议的老师 负责人和参会人
    public function actionMeetTeacher()
    {
        $course_code = Yii::app()->request->getParam('course_code', ''); // 课程的code
        $tid = Yii::app()->request->getParam('tid', '');
        $uid = Yii::app()->request->getParam('uid', '');
        $weekday_period = Yii::app()->request->getParam('weekday', ''); // 例如 1-2
        $datatime = Yii::app()->request->getParam('datatime', ''); // 选的时间
        $tid = $tid ? $tid : $this->tid ;
        $res = CommonUtils::requestDsOnline('attendance/meetTeacher', array(
            'tid'=>$tid,
            'school_id'=>$this->branchId,
            'course_code'=>$course_code,
        ), 'get');
        $status = 0;
        if($uid == Yii::app()->user->id || Yii::app()->user->checkAccess('o_MS_TimetableMgt')){
            $status = 1;
        }
        $this->renderPartial('meetTeacher',array(
            'meet_base_data'=>$res['data']['meet_base_data'],
            'meet_leader_id'=>$res['data']['meet_leader_id'],
            'acceding_teacher_id'=>$res['data']['acceding_teacher_id'],
            'meet_teachers'=>$res['data']['meet_teachers'],
            'datatime' => $datatime,
            'weekday' => $weekday_period,
            'status'=>$status,
            'uid'=>$uid,
            'course_code' => $course_code,
        ));
    }

    public function actionGetRecordsArr()
    {
        $weekday_period = Yii::app()->request->getParam('weekday', ''); // 例如 1-2
        $course_code = Yii::app()->request->getParam('course_code', ''); // 课程的code
        $datatime = Yii::app()->request->getParam('datatime', ''); // 选的时间
        $tid = Yii::app()->request->getParam('tid', '');
        $uid = Yii::app()->request->getParam('uid', '');

        $tids = $tid ? $tid : $this->tid ;

        $studentLiat = array();
        $studentLiat_t = array();
        $courseModel = array();
        $recordsArr = array();
        $numberTypes = array(
            TimetableRecords::ATTENDANCE_STATUS => 0,
            TimetableRecords::LATE_STATUS => 0,
            TimetableRecords::LEAVE_STATUS => 0,
            TimetableRecords::ABSENTEE_STATUS => 0,
            0 => 0,
            1 => 0,
        );
        if($course_code){
            // 根据时间判断是第一学期还是第二学期
            $semesterId = $this->getSemesterIndex($this->timetable->yid, strtotime($datatime));
            $studentDataModel = $this->getStudentCourseData($tids, $course_code, $semesterId);

            foreach ($studentDataModel as $val) {
                if(in_array($val->childProfile->status, array(ChildProfileBasic::STATS_ACTIVE_WAITING, ChildProfileBasic::STATS_ACTIVE))){
                    $studentLiat[$val->child_id] = array(
                        'class_room' => $val->class_room,
                        'course_code' => $val->course_code,
                        'childName' => isset($val->childProfile) ? $val->childProfile->getChildName(false,false,true) : "",
                        'photo' => isset($val->childProfile) ? $val->childProfile->photo : "",
                        'gender' => isset($val->childProfile) ? $val->childProfile->gender : "",
                    );
                    asort($studentLiat);
                }else{
                    $studentLiat_t[$val->child_id] = array(
                        'class_room' => $val->class_room,
                        'course_code' => $val->course_code,
                        'childName' => isset($val->childProfile) ? $val->childProfile->getChildName(false,false,true) : "",
                        'photo' => isset($val->childProfile) ? $val->childProfile->photo : "",
                        'gender' => isset($val->childProfile) ? $val->childProfile->gender : "",
                    );
                    asort($studentLiat_t);
                }
            }
            $recordsArr = array();
            foreach ($studentLiat as $childid => $val){
                $recordsArr[$childid] = array(
                    'type' => 10,
                    'memo' => '',
                    'late_time' => '',
                    'dress' => '',
                    'electronic' => 0,
                    'is_admin' => 0,
                );
            }
            if($weekday_period && $datatime){
                $weekday = substr( $weekday_period, 0, 1 );
                $period = substr($weekday_period,-1,1);

                $criteria = new CDbCriteria;
                $criteria->compare('course_code', $course_code);
                $criteria->compare('weekday', $weekday);
                $criteria->compare('period', $period);
                $criteria->compare('target_date', strtotime($datatime));
                $criteria->compare('school_id', $this->branchId);
                $criteria->compare('tid', $tids);
                $criteria->compare('status', 10);
                $model = TimetableRecords::model()->findAll($criteria);

                if($model){
                    foreach ($model as $child=>$val){
                        if(strstr($val->electronic,',') !== false){
                            $val->electronic = explode(',',$val->electronic);
                        }
                        $recordsArr[$val->childid] = array(
                            'type' => $val->type,
                            'memo' => $val->memo,
                            'late_time' => $val->late_time,
                            'dress' => $val->dress,
                            'electronic' => $val->electronic,
                            'is_admin' => $val->is_admin,
                        );
                    }
                }
            }
        }

        $this->addMessage('state', 'fail');
        $this->addMessage('data', $recordsArr);
        $this->showMessage();
    }

    public function actionCourseStudentPrint()
    {
        $weekday_period = Yii::app()->request->getParam('weekday', ''); // 例如 1-2
        $course_code = Yii::app()->request->getParam('course_code', ''); // 课程的code
        $datatime = Yii::app()->request->getParam('datatime', ''); // 选的时间
        $tid = Yii::app()->request->getParam('tid', '');
        $uid = Yii::app()->request->getParam('uid', '');

        $tids = $tid ? $tid : $this->tid ;

        $week = substr( $weekday_period, 0, 1 );
        $period = substr($weekday_period,-1,1);
        $weekArr = array(
            1=> Yii::t('attends','Mon'),
            2=> Yii::t('attends','Tue'),
            3=> Yii::t('attends','Wed'),
            4=> Yii::t('attends','Thu'),
            5=> Yii::t('attends','Fri'),
            );
        $getTime = TimetableCourses::getTime();
        $weekTitle =sprintf(Yii::t('attends', '%s period #%s'), $weekArr[$week], $period);
        $classTime = $getTime[$period];

        $studentLiat = array();
        $studentLiat_t = array();
        if($course_code){
            // 根据时间判断是第一学期还是第二学期
            $semesterId = $this->getSemesterIndex($this->timetable->yid, strtotime($datatime));
            $studentDataModel = $this->getStudentCourseData($tids, $course_code, $semesterId);

            foreach ($studentDataModel as $val) {
                if(in_array($val->childProfile->status, array(ChildProfileBasic::STATS_ACTIVE_WAITING, ChildProfileBasic::STATS_ACTIVE))){
                    $studentLiat[$val->child_id] = array(
                        'class_room' => $val->class_room,
                        'course_code' => $val->course_code,
                        'childName' => isset($val->childProfile) ? $val->childProfile->getChildName(false,false,true) : "",
                        'photo' => isset($val->childProfile) ? $val->childProfile->photo : "",
                        'gender' => isset($val->childProfile) ? $val->childProfile->gender : "",
                    );
                    asort($studentLiat);
                }else{
                    $studentLiat_t[$val->child_id] = array(
                        'class_room' => $val->class_room,
                        'course_code' => $val->course_code,
                        'childName' => isset($val->childProfile) ? $val->childProfile->getChildName(false,false,true) : "",
                        'photo' => isset($val->childProfile) ? $val->childProfile->photo : "",
                        'gender' => isset($val->childProfile) ? $val->childProfile->gender : "",
                    );
                    asort($studentLiat_t);
                }
            }
        }

        $teacherModel = User::model()->findByPk($uid);
        $courseModel = TimetableCourses::model()->findByAttributes(array('course_code' => $course_code));

        $this->layout = '//layouts/print';

        $this->render('courseStudentPrint', array(
            'studentLiat' => $studentLiat,
            'weekTitle' => $weekTitle,
            'classTime' => $classTime,
            'courseModel' => $courseModel,
            'teacherModel' => $teacherModel,
        ));
    }

    public function actionUpdateRecords()
    {
        $weekday_period = Yii::app()->request->getParam('weekday', ''); // 例如 1-2 周一-第二节
        $course_code = Yii::app()->request->getParam('course_code', ''); // 课程的code
        $datatime = Yii::app()->request->getParam('datatime', ''); // 选的时间
        $type = Yii::app()->request->getParam('type', '');
        $dress = Yii::app()->request->getParam('dress', '');//着装违规
        $electronic = Yii::app()->request->getParam('electronic', '');
        $content = Yii::app()->request->getParam('content', ''); //
        $select = Yii::app()->request->getParam('select', ''); //
        $tid = Yii::app()->request->getParam('tid', ''); //
        $teacherId = Yii::app()->request->getParam('teacherId', ''); //
        $branchId = Yii::app()->request->getParam('branchId', ''); //
        $nextPeriod = Yii::app()->request->getParam('nextPeriod', ''); #同步更新相邻课程
        $tids = $tid ? $tid : $this->tid ;
        $weekday = substr( $weekday_period, 0, 1 );
        $period = substr($weekday_period,-1,1);
        $sign_period = array($period);
        if(!empty($nextPeriod)){
            $next_period = $period+1;#相邻课
            $date = new DateTime($datatime);
            $date->modify('this week monday');
            $monday = $date->format('Ymd');
            $crit = new CDbCriteria;
            $crit->compare('tid', $tids);
            $crit->compare('monday', $monday);
            $yearlySchedule = TimetableYearlySchedule::model()->find($crit);
            $schedule = empty($yearlySchedule->schedule) ? 'A' : $yearlySchedule->schedule;
            $crit = new CDbCriteria;
            $crit->compare('course_code',$course_code);
            $crit->compare('tid',$tids);
            $crit->compare('category',$schedule);
            $crit->compare('weekday',$weekday);
            $crit->compare('period',$next_period);
            $next_same = TimetableCourseData::model()->exists($crit);
            if($next_same){
                $sign_period = array($period,$next_period);#循环签到
            }
        }
        foreach ($sign_period as $period){
            if(!$type){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('attends', 'type error'));
                $this->showMessage();
            }
            //查询这周是否换课
            $criteria = new CDbCriteria;
            $criteria->compare('target_day', date('Ymd',strtotime($datatime)));
            $criteria->compare('school_id', $branchId);
            $criteria->compare('tid', $tids);
            $dailyReplaceModel = TimetableDailyReplace::model()->findAll($criteria);
            if($dailyReplaceModel){
                $weekday = date('w',strtotime($datatime));
            }
            if (!Yii::app()->user->checkAccess('o_MS_TimetableMgt')) {
                if(strtotime($datatime) != strtotime(date("Y-m-d", time()))){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('attends','The teachers can mark/edit the students attendance records within the current school day only'));
                    $this->showMessage();
                }
            }
            $criteria = new CDbCriteria;
            $criteria->compare('course_code', $course_code);
            $courseModel = TimetableCourses::model()->find($criteria);
            if(!$courseModel){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '错误');
                $this->showMessage();
            }
            $criteria = new CDbCriteria;
            $criteria->compare('course_code', $course_code);
            $criteria->compare('child_id', array_keys($type));
            $criteria->compare('tid', $tids);
            $criteria->compare('status', 1);
            $criteria->index = 'child_id';
            $childidModel = TimetableStudentData::model()->findAll($criteria);
            $courseTeacher = array(Yii::app()->user->id);
            $weekNum = 0;
            $crit = new CDbCriteria();
            $crit->compare('yid', $this->timetable->yid);
            $crit->compare('monday_timestamp', '<=' . strtotime($datatime));
            $crit->order = 'monday_timestamp DESC';
            $weekModel = CalendarWeek::model()->find($crit);
            if ($weekModel) {
                $weekNum = (int)$weekModel->weeknumber;
            }
            // 查找替补老师
            $criteria = new CDbCriteria();
            $criteria->compare('course_id', $courseModel->id);
            $criteria->compare('new_teacher', $teacherId);
            $criteria->compare('weeknum', $weekNum);
            $criteria->compare('weekday', $weekday);
            $criteria->compare('period', $period);
            $criteria->compare('deleted_at', null);
            $replaceTeacher = TimetableCourseTeacherReplace::model()->find($criteria);
            if ($replaceTeacher) {
                $courseTeacher = $replaceTeacher->old_teacher;
            }
            $criteria = new CDbCriteria;
            $criteria->compare('course_id', $courseModel->id);
            $criteria->compare('teacher_id', $courseTeacher);
            $criteria->compare('status', 1);
            $teacherModel = TimetableCourseTeacher::model()->count($criteria);
            if(!($teacherModel || Yii::app()->user->checkAccess('o_MS_TimetableMgt'))){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('attends','View Only! You have no attendance permission of this course.'));
                $this->showMessage();
            }
            $status = 0;
            foreach ($type as $key=>$val){
                if($val == TimetableRecords::LATE_STATUS){
                    if(!$select[$key]){
                        $status = 1;
                    }
                }
            }
            if($status){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('attends', '迟到未填写迟到时间'));
                $this->showMessage();
            }
            $data = array(
                TimetableRecords::ATTENDANCE_STATUS => 0,
                TimetableRecords::LATE_STATUS => 0,
                TimetableRecords::LEAVE_STATUS => 0,
                TimetableRecords::ABSENTEE_STATUS => 0,
            );
            $class_room = "";
            $criteria = new CDbCriteria;
            $criteria->compare('course_code', $course_code);
            $criteria->compare('weekday', $weekday);
            $criteria->compare('period', $period);
            $criteria->compare('target_date', strtotime($datatime));
            $criteria->compare('school_id', $this->branchId);
            $criteria->compare('childid', array_keys($type));
            $criteria->compare('tid', $tids);
            $recordsModel = TimetableRecords::model()->findAll($criteria);
            if($recordsModel){
                foreach ($recordsModel as $item) {
                    if($item->is_admin < 1){
                        $item->status = 99;
                        $item->updated_at = time();
                        $item->updated_by = Yii::app()->user->id;
                        $item->save();
                    }else{
                        if($item->status != 99){
                            $data[$item->type] += 1;
                        }
                    }
                }
            }
            $criteria = new CDbCriteria;
            $criteria->compare('childid', array_keys($type));
            $criteria->index = 'childid';
            $childModel = ChildProfileBasic::model()->findAll($criteria);
            foreach ($type as $childid => $val){
                $criteria = new CDbCriteria;
                $criteria->compare('course_code', $course_code);
                $criteria->compare('weekday', $weekday);
                $criteria->compare('period', $period);
                $criteria->compare('target_date', strtotime($datatime));
                $criteria->compare('school_id', $this->branchId);
                $criteria->compare('childid', $childid);
                $criteria->compare('tid', $tids);
                $criteria->compare('status', 10);
                $criteria->compare('is_admin', 1);
                $modelCount = TimetableRecords::model()->find($criteria);
                if(!$modelCount){
                    $criteria = new CDbCriteria;
                    $criteria->compare('course_code', $course_code);
                    $criteria->compare('weekday', $weekday);
                    $criteria->compare('period', $period);
                    $criteria->compare('target_date', strtotime($datatime));
                    $criteria->compare('school_id', $this->branchId);
                    $criteria->compare('childid', $childid);
                    $criteria->compare('tid', $tids);
                    $criteria->compare('created_by', Yii::app()->user->id);
                    $model = TimetableRecords::model()->find($criteria);
                    if(!$model){
                        $model = new TimetableRecords();
                        $model->tid = $tids;
                        $model->childid = $childid;
                        $model->school_id = $this->branchId;
                        $model->created_at = time();
                        $model->created_by = Yii::app()->user->id;
                    }
                    $model->status = 10;
                    $model->course_code = $courseModel->course_code;
                    $model->class_room = $childidModel[$childid]->class_room;
                    $model->class_id =  $childModel[$childid]->classid;
                    $model->weekday = $weekday;
                    $model->period = $period;
                    $model->teacher_id = $teacherId;
                    $model->target_date = strtotime($datatime);
                    $model->late_time = ($val == TimetableRecords::LATE_STATUS) ? $select[$childid] : "";
                    $model->type = $val;
                    $model->dress = $dress[$childid];
                    $model->electronic = $electronic[$childid];
                    $model->memo = $content[$childid];
                    $model->updated_at = time();
                    $model->updated_by = Yii::app()->user->id;
                    $model->save();
                    $data[$val] += 1;
                    $class_room = $model->class_room;
                }else{
                    $modelCount->dress = $dress[$childid];
                    $modelCount->electronic = $electronic[$childid];
                    $modelCount->save();
                }
                if($modelCount->is_admin != 1  && $period == 1){
                    TimetableRecords::childSignDel($childid, $this->branchId,strtotime($datatime),Yii::app()->user->id);
                    //10出勤 11线上出勤  20 迟到 30事假 31病假  40旷课 41校内停学 42校外停学 着装违规同步
                    if (in_array($val, array(10,11,20,30,31,40,41,42))){
                        $memo = empty($content[$childid]) ? '无' : $content[$childid];
                        $late_time = ($val == TimetableRecords::LATE_STATUS) ? $select[$childid] : 0;
                        TimetableRecords::childSign($childModel[$childid], strtotime($datatime), $val, $late_time, $memo);
                    }
                    //着装违规
                    if(!empty($dress[$childid])){
                        TimetableRecords::childViolation($childModel[$childid], strtotime($datatime), 50);
                    }
                }
            }
            if($data){
                $criteria = new CDbCriteria;
                $criteria->compare('course_id', $courseModel->id);
                $criteria->compare('weekday', $weekday);
                $criteria->compare('period', $period);
                $criteria->compare('target_date', strtotime($datatime));
                $criteria->compare('tid', $tids);
                $numberCacheModel = TimetableStudentNumberCache::model()->find($criteria);
                if(!$numberCacheModel){
                    $numberCacheModel = new TimetableStudentNumberCache();
                    $numberCacheModel->tid = $tids;
                    $numberCacheModel->course_id = $courseModel->id;
                    $numberCacheModel->target_date = strtotime($datatime);
                    $numberCacheModel->weekday = $weekday;
                    $numberCacheModel->period = $period;
                    $numberCacheModel->class_room = $class_room;
                    $numberCacheModel->updated_at = time();
                }
                $numberCacheModel->data = json_encode($data);
                $numberCacheModel->save();
            }
        }
        $this->addMessage('state', 'success');
        $this->addMessage('message', Yii::t('message', "成功"));
        $this->addMessage('callback', 'cbSuccess');
        $this->showMessage();
    }

    // 获取所有的教学老师
    public function getCampusTeachers(){
        $teacherids = OA::getCampusTeachers($this->branchId);

        $crit = new CDbCriteria();
        $crit->select = "uid";
        $crit->compare('schoolid',$this->branchId);
        $crit->compare('type','edu');
        $teachers_edu = AdmBranchLink::model()->findAll($crit);
        $teacher_edu = array();
        if($teachers_edu){
            foreach($teachers_edu as $_teache){
                $teacher_edu[] = $_teache->uid;
            }
            $teacherids = array_merge($teacherids,$teacher_edu);
        }

        $teachers = array();
        if($teacherids){
            $crit = new CDbCriteria();
            $crit->compare('t.uid', $teacherids);
            $crit->index = 'uid';
            $teachers = User::model()->with(array('profile','staffInfo'))->findAll($crit);
        }

        return $teachers;
    }
    /**
     * 获取所有的中学老师
     */

    public function getMiddleSchoolTeachers()
    {
        Yii::import('common.models.classTeacher.*');
        $criteria = new CDbCriteria();
        $criteria->compare('department_id', array(137));
        $criteria->select  = 'position_id';
        $depPosLink = DepPosLink::model()->findAll($criteria);
        $position_id = array();
        foreach($depPosLink as $v){
            $position_id[] = $v->position_id;
        }
        $teachers = array();
        if($position_id){
            $criteria = new CDbCriteria();
            $criteria->compare('t.level', 1);
            $criteria->compare('profile.occupation_en', $position_id);
            $criteria->index = 'uid';
            $teachers = User::model()->with('profile')->findAll($criteria);
        }
        return array_keys($teachers);
    }

    // 切换老师
    public function actionTeacher()
    {
        $tid = Yii::app()->request->getParam('tid', '');
        $startYear = Yii::app()->request->getParam('startyear', '');

        if (!$tid) {
            $criteria = new CDbCriteria();
            $criteria->compare('startyear', $startYear);
            $criteria->compare('branchid', $this->branchId);
            $calendar = CalendarSchool::model()->find($criteria);

            $criteria = new CDbCriteria();
            $criteria->compare('yid', $calendar->yid);
            $criteria->compare('schoolid', $this->branchId);
            $timetable = Timetable::model()->find($criteria);
            $tid = $timetable->id;
        }
        $criteria = new CDbCriteria;
        $criteria->compare('tid', $tid);
        $criteria->compare('status', 1);
        $criteria->compare('schoolid', $this->branchId);
        $criteria->index = 'teacher_id';
        $teacherModel = TimetableCourseTeacher::model()->findAll($criteria);#分配过课程的老师
        $teacherModelIds = array_keys($teacherModel);#分配过课程的老师id
        $middleSchoolTeacherIds = $this->getMiddleSchoolTeachers();#所有中学老师id
        $teacher_ids = array_values(array_unique(array_merge($teacherModelIds,$middleSchoolTeacherIds)));
        $teacherList = array();
        if($teacher_ids){
            Yii::import('common.models.classTeacher.InfopubStaffExtend');
            $criteria = new CDbCriteria;
            $criteria->compare('uid', $teacher_ids);
            $criteria->index = 'uid';
            $criteria->with = 'staffInfo';
            $userModel = User::model()->findAll($criteria);
            foreach ($userModel as $teacher_id=>$item){
                $photo = empty($item->staffInfo->staff_photo) ? "blank.jpg" : $item->staffInfo->staff_photo;
                $teacherList[$teacher_id] = array(
                    'name' => $item->getName(),
                    'photo' =>  OA::CreateOAUploadUrl('infopub/staff', $photo),
                );
            }
        }
        echo json_encode($teacherList);
    }

    // 根据当前永华获取需要修改的课程列表
    public function actionCourseAlias()
    {
        $teacher_id = Yii::app()->request->getParam('teacherId', '');
        $tid = Yii::app()->request->getParam('tid', '');
        $teacher_id = ($teacher_id) ? $teacher_id : Yii::app()->user->id;
        $tid = ($tid) ? $tid : $this->tid;

        $userModel = User::model()->findByPk($teacher_id);

        $model = Timetable::model()->findAll();
        $yidDate = array();
        if($model) {
            Yii::import('common.models.calendar.*');
            $criteria = new CDbCriteria();
            $criteria->compare('yid', $model->yid);
            $criteria->compare('branchid', $this->branchId);
            $schoolModel = CalendarSchool::model()->findAll($criteria);
            if($schoolModel){
                foreach ($schoolModel as $val){
                    $end = $val->startyear + 1;
                    $date[$val->yid] = ($val) ? $val->startyear . ' - ' . $end: "";
                }
            }
            foreach ($model as $val){
                $yidDate[$val->id] = $date[$val->yid];
            }
        }

        $criteria = new CDbCriteria();
        $criteria->compare('tid', $tid);
        $criteria->compare('teacher_id', $teacher_id);
        $criteria->compare('status', 1);
        $teacherModel = TimetableCourseTeacher::model()->findAll($criteria);

        $courseList = array();
        if($teacherModel) {
            foreach ($teacherModel as $val) {
                $courseList[] =  array(
                    'id' => $val->course_id,
                    'course_code' => $val->course->course_code,
                    'title' => $val->course->getTitle(),
                    'alias' => ($val->course->alias) ? $val->course->alias->alias_cn : '',
                );
            }
        }

        $status = 0;
        if($teacher_id == Yii::app()->user->id){
            $status = 1;
        }

        $this->render('courseListAlias', array(
            'courseList' => $courseList,
            'userModel' => $userModel,
            'teacher_id' => $teacher_id,
            'tid' => $tid,
            'status' => $status,
            'yidDate' => $yidDate
        ));
    }

    // 修改课程别名
    public function actionShowAlias()
    {
        $course_id = Yii::app()->request->getParam('course_id', '');
        $courseModel = TimetableCourses::model()->findByPk($course_id);
        $criteria = new CDbCriteria();
        $criteria->compare('tid', $this->tid);
        $criteria->compare('program', $courseModel->program);
        $criteria->compare('course_code', $courseModel->course_code);
        $aliasModel = TimetableCoursesAlias::model()->find($criteria);
        $data['alias'] = '';
        if($aliasModel){
            $data['alias'] =  $aliasModel->alias_cn;
            $data['course_code'] =  $aliasModel->course_code;
        }
        $this->addMessage('state', 'success');
        $this->addMessage('data', $data);
        $this->showMessage();
    }

    //增加和修改课程的别名
    public function actionUpdateAlias()
    {
        $course_id = Yii::app()->request->getParam('course_id', '');
        $alias = Yii::app()->request->getParam('alias', '');
        $teacherId = Yii::app()->request->getParam('teacherId', '');

        if($teacherId != Yii::app()->user->id){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('attends','Only courses assigned to you could be edited.'));
            $this->showMessage();
        }

        $courseModel = TimetableCourses::model()->findByPk($course_id);
        $criteria = new CDbCriteria();
        $criteria->compare('tid', $this->tid);
        $criteria->compare('program', $courseModel->program);
        $criteria->compare('course_code', $courseModel->course_code);
        $model = TimetableCoursesAlias::model()->find($criteria);
        if(!$model){
            $model = new TimetableCoursesAlias();
            $model->tid = $this->tid;
            $model->program = $courseModel->program;
            $model->course_code = $courseModel->course_code;
            $model->created_at = time();
            $model->created_by = Yii::app()->user->id;
        }
        $model->alias_cn = $alias;
        $model->alias_en = $alias;
        $model->updated_at = time();
        $model->updated_by = Yii::app()->user->id;
        if(!$model->save()){
            $errors = current($model->getErrors());
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $errors[0]);
            $this->showMessage();
        }

        $this->addMessage('state', 'success');
        $this->addMessage('message', Yii::t('teaching','Save succefully!'));
        $this->showMessage();
    }

    /**
     * 按周获取代课信息
     *
     * @return void
     */
    public function actionReplaceTeacherData()
    {
        $tid = $this->tid;
        $schoolId = $this->branchId;
        $weeknum = Yii::app()->request->getParam('weeknum');
        $teacherId = Yii::app()->request->getParam('teacher_id');

        $res = CommonUtils::requestDsOnline('timetable/replaceTeacherData', array(
            'tid' => $tid,
            'schoolid' => $schoolId,
            'weeknum' => $weeknum,
            'teacher_id' => $teacherId,
        ));

        if (isset($res['code']) && $res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("message", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    /**
     * 添加代课老师
     *
     * @return void
     */
    public function actionReplaceTeacherSave()
    {
        $tid = $this->tid;
        $schoolId = $this->branchId;
        $course_code = Yii::app()->request->getParam('course_code');
        $weeknum = Yii::app()->request->getParam('weeknum');
        $room = Yii::app()->request->getParam('room');
        $weekday = Yii::app()->request->getParam('weekday');
        $period = Yii::app()->request->getParam('period');
        $new_teacher = Yii::app()->request->getParam('new_teacher');
        $old_teacher = Yii::app()->request->getParam('old_teacher');
        $category = Yii::app()->request->getParam('category');
        $source_day = Yii::app()->request->getParam('source_day');

        $res = CommonUtils::requestDsOnline('timetable/replaceTeacherSave', array(
            'tid' => $tid,
            'schoolid' => $schoolId,
            'course_code' => $course_code,
            'weeknum' => $weeknum,
            'room' => $room,
            'weekday' => $weekday,
            'period' => $period,
            'new_teacher' => $new_teacher,
            'old_teacher' => $old_teacher,
            'category' => $category,
            'source_day' => $source_day,
        ));

        if (isset($res['code']) && $res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("message", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    /**
     * 取消代课老师
     *
     * @return void
     */
    public function actionReplaceTeacherCancel()
    {
        $tid = $this->tid;
        $schoolId = $this->branchId;
        $course_code = Yii::app()->request->getParam('course_code');
        $weeknum = Yii::app()->request->getParam('weeknum');
        $weekday = Yii::app()->request->getParam('weekday');
        $period = Yii::app()->request->getParam('period');

        $res = CommonUtils::requestDsOnline('timetable/replaceTeacherCancel', array(
            'tid' => $tid,
            'school_id' => $schoolId,
            'course_code' => $course_code,
            'weeknum' => $weeknum,
            'weekday' => $weekday,
            'period' => $period,
        ));

        if (isset($res['code']) && $res['code'] == 0) {
            $this->addMessage("state", "success");
            $this->addMessage("message", "success");
            $this->addMessage("data", $res['data']);
        } else {
            $this->addMessage("state", "fail");
            $this->addMessage("message", $res['msg']);
        }
        $this->showMessage();
    }

    // 获取当前
    public function getCurrentClass()
    {
        $criteria = new CDbCriteria();
        $criteria->compare('yid', $this->timetable->yid);
        $criteria->compare('classtype', array("e6","e7",'e8','e9'));
        // $criteria->compare('stat', 10);
        $criteria->index = 'classid';
        $classModel = IvyClass::model()->findAll($criteria);
        return $classModel;
    }

    // 根据时间判断是第一学期还是第二学期
    public function getSemesterIndex($yid, $timestamp)
    {
        Yii::import('common.models.calendar.CalendarSemester');
        $semesterInfo = CalendarSemester::model()->getSemesterTimeStamp($yid);
        $semester = 1;
        if (isset($semesterInfo) && $timestamp >= $semesterInfo['spring_start']) {
            $semester = 2;
        }
        return $semester;
    }

    // 获取schedule缓存
    public function getScheduleCache($type, $targetId, $tid, $semesterId = 0)
    {
        $criteria = new CDbCriteria;
        $criteria->compare('type', $type);
        $criteria->compare('target_id', $targetId);
        $criteria->compare('tid', $tid);
        $criteria->index = 'term';
        $scheduleCacheModels = TimetableWeeklyScheduleCache::model()->findAll($criteria);

        $scheduleCacheModel = null;
        if ($scheduleCacheModels) {
            $scheduleCacheModel = $scheduleCacheModels[0];
            if (isset($scheduleCacheModels[$semesterId])) {
                $scheduleCacheModel = $scheduleCacheModels[$semesterId];
            }
        }
        return $scheduleCacheModel;
    }

    public function getStudentCourseData($tid, $course_code, $semesterId = 0)
    {
        $criteria = new CDbCriteria;
        $criteria->compare('course_code', $course_code);
        $criteria->compare('tid', $tid);
        $criteria->compare('status', 1);
        $models = TimetableStudentData::model()->findAll($criteria);
        $returnModel = null;
        foreach ($models as $model) {
            if (isset($returnModel[$model->child_id])) {
                if ($model->term == $semesterId) {
                    $returnModel[$model->child_id] = $model;
                }
            } else {
                $returnModel[$model->child_id] = $model;
            }
        }
        return $returnModel;
    }

    // 出了改变tid，其他参数全部不变
    public function createTidUrl($route, $params = array(), $ampersand = '&')
    {
        foreach ($_GET as $k => $v) {
            if (!isset($params[$k])) {
                $params[$k] = $v;
            }
        }

        return parent::createUrl($route, $params, $ampersand);
    }
}
