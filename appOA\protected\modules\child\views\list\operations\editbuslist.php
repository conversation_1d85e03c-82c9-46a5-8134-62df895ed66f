<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'bus-chilren-form',
	'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm'),
)); ?>
<div class="pop_cont pop_table" style="height:auto;">
    <table width="100%">
        <col width="150" class="th" />
        <col width="400" />
        <col />
        <tbody>
            <tr>
                <th><?php echo $form->labelEx($model,'bus_title'); ?></th>
                <td>
                    <?php echo $form->textField($model,'bus_title',array('maxlength'=>255,'class'=>'input length_5')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'bus_title'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'bus_code'); ?></th>
                <td>
                    <?php echo $form->textField($model,'bus_code',array('maxlength'=>255,'class'=>'input length_5')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'bus_code'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'bus_type'); ?></th>
                <td>
                    <?php echo $form->textField($model,'bus_type',array('maxlength'=>255,'class'=>'input length_5')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'bus_type'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'acctual_children'); ?></th>
                <td>
                    <?php echo $form->textField($model,'acctual_children',array('maxlength'=>255,'class'=>'input length_5')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'acctual_children'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'driver_name'); ?></th>
                <td>
                    <?php echo $form->textField($model,'driver_name',array('maxlength'=>255,'class'=>'input length_5')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'driver_name'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'driver_mobile'); ?></th>
                <td>
                    <?php echo $form->textField($model,'driver_mobile',array('maxlength'=>255,'class'=>'input length_5')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'driver_mobile'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'aunt_name'); ?></th>
                <td>
                    <?php echo $form->textField($model,'aunt_name',array('maxlength'=>255,'class'=>'input length_5')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'aunt_name'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'aunt_mobile'); ?></th>
                <td>
                    <?php echo $form->textField($model,'aunt_mobile',array('maxlength'=>255,'class'=>'input length_5')); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'aunt_mobile'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'branchid'); ?></th>
                <td>
                    <?php echo $form->dropDownList($model,'branchid',Branch::model()->getBranchList(),array('class'=>'select_5','empty'=>Yii::t('global','Please Select'))); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'branchid'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'state'); ?></th>
                <td>
                    <?php echo $form->dropDownList($model,'state',$model->getState(1),array('class'=>'select_5','empty'=>Yii::t('global','Please Select'))); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'state'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'city_id'); ?></th>
                <td>
                    <?php echo $form->dropDownList($model,'city_id',CHtml::listData($cityModel, 'diglossia_id', 'entitle'),array('class'=>'select_5','empty'=>Yii::t('global','Please Select'))); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'city_id'); ?></div></td>
            </tr>
            <tr>
                <th><?php echo $form->labelEx($model,'vendor_id'); ?></th>
                <td>
                    <?php echo $form->dropDownList($model,'vendor_id',$cityArr,array('class'=>'select_5','empty'=>Yii::t('global','Please Select'))); ?>
                </td>
                <td><div class="fun_tips"><?php echo $form->error($model,'vendor_id'); ?></div></td>
            </tr>
        </tbody>
    </table>
</div>
<div class="pop_bottom">
    <button type="button" class="btn fr" id="J_dialog_close"><?php echo Yii::t('global','Cancel');?></button>
    <button class="btn fr btn_submit mr10 J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
</div>
<?php $this->endWidget(); ?>

<script type="text/javascript">
$('#SchoolBus_city_id').change(function(){
    var id = $(this).val();
    $.post('<?php echo $this->createUrl('/child/list/processVendor')?>', {id:id}, function(data){
        var obj = $('#SchoolBus_vendor_id');
        obj.html('<option value="">请选择</option>');
        for(var i in data){
            $('<option></option>').val(i).text(data[i]).appendTo( obj );
        }
    }, 'json');
});
</script>