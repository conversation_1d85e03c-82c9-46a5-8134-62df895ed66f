var enrolls = new Backbone.Collection;
var noclassModel = new Backbone.Model;

function getCampusEnrollData(branchId)
{
    var progress = 0;
    var step = ( 100 *( 1 / getCampusDataRequest.enroll.length) ).toFixed(2);
    var complete = 0;
    var attendance, enrollData;
    $('#overall-progress .progress-bar').css('width','0').html('0%');
    $('#overall-progress').modal({keyboard: false,backdrop:'static'});
    for(var i=0; i<getCampusDataRequest.enroll.length;i++){
        if(i == 0 && $('#enroll-item-template').length > 0) {
            progress = parseInt(progress) + parseInt(step);
            complete +=1;
            continue;
        }
        $.ajax({
            type: "POST",
            url: getCampusDataRequest.enroll[i].url,
            data: getCampusDataRequest.enroll[i].data,
            dataType: 'json',
            async: true,
            success: function(data) {
                progress = parseInt(progress) + parseInt(step);
                complete +=1;
                if(progress > 100) progress = 100;
                if(complete == getCampusDataRequest.enroll.length) progress = 100;

                if(data.type == 'append'){
                    $('body').append(data.data);
                }else if(data.type == 'attendance'){
                    attendance = data;
                }
                else {
                    enrollData = data;
                }

                $('#overall-progress .progress-bar').css('width',progress+'%').html(progress+'%');
                if(progress == 100){
                    $('#overall-progress .hint').html('Preparing Data...');
                    showEnrollData(branchId, enrollData, attendance);
                }
            }
        });
    }
}

function showEnrollData(branchId, data, attendance)
{
    if(data.length){
        var _data = [];
        var _noClass = [];
        var total;
        var capacity=0;
        for(var index in data){
            var datum = data[index];
            datum.data.enrollment = parseInt(datum.data.num1)+parseInt(datum.data.num2);
            datum.data.future = parseInt(datum.data.num15)+parseInt(datum.data.num16);
            datum.data.waiting = parseInt(datum.data.num3)+parseInt(datum.data.num4);
            datum.data.refunds = parseInt(datum.data.num13)+parseInt(datum.data.num14);
            datum.data.fill_ratio = (datum.data.enrollment/datum.data.capacity*100).toFixed(2)+'%';
            if(datum.id != 0){
                capacity += parseInt(datum.data.capacity);
                _data.push(datum);
            }
            else{
                total = datum;
                total.title = 'Total';
            }
        }
        total.data.capacity = capacity;
        total.data.fill_ratio = (total.data.enrollment/total.data.capacity*100).toFixed(2)+'%';
        _data.push(total);
        var _container = $('#container-campus-'+branchId+' .campus-main');
        var rdm = Math.floor(Math.random()*100000);
        _container.html('').html( _.template($('#enroll-structure-template').html(), {rdm: rdm}) );

        var itemView = Backbone.View.extend({
            tagName : 'tr',
            template : _.template($('#enroll-item-template').html()),
            initialize : function(){
                this.model.bind('change', this.render, this);
            },
            render: function() {
                this.$el.html( this.template(this.model.attributes) );
                return this;
            }
        });

        var EnrollData = Backbone.View.extend({
            el: $('#enroll-structure-template'),
            initialize: function(){
                enrolls.bind('reset', this.addAll, this);
                enrolls.reset(_data);
            },
            addOne : function(enrollModel){
                var view = new itemView({model:enrollModel});
                $('#c_data tbody').append(view.render().el);
            },
            addAll : function(){
                $('#e-data').hide();
                $('#c_data tbody').html('');
                enrolls.each(this.addOne);
                $('#e-data').fadeIn(500);
            }
        });

        new EnrollData();
        $('#container-campus-'+branchId+' #t_data tbody').html(_.template($('#enroll-noclass-template').html(), total) );
        $('#container-campus-'+branchId+' #pay_data tbody').html(_.template($('#enroll-pay-template').html(), total) );
    }
    else{
        $('#e-data').hide();
    }

    $( "#specified-date1" ).datepicker({
        dateFormat: 'yy-mm-dd',
        maxDate: todayDate,
        onSelect: function(date){
            specifiedDate = date;
            loadCampusInfo({schoolid:currentBranchId}, $('.list-group a[datatype="enroll"]'));
        }
    });
    $('#specified-date1').val(specifiedDate);

    $('.J_attned span.badge').text(attendance.count);
    $('.J_attned').attr('cids', attendance.diff);

    setTimeout("js:$('#overall-progress').modal('hide')", 500);
}