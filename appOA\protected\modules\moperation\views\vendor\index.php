<?php
$purchaseProducts = Yii::app()->request->getParam('PurchaseProducts', '');
?>
<style>
	[v-cloak] {
		display: none;
	}
	
	.glyphicon {
		cursor: pointer
	}
	
	.citymodify {
		height: 500px;
		overflow: auto;
	}
	td{
		word-break:break-all
	}
</style>
<div class="container-fluid">
	<ol class="breadcrumb">
		<li>
			<?php echo CHtml::link(Yii::t('site', 'HQ Operations'), array('default/index')); ?>
		</li>
		<li>
			<?php echo CHtml::link(Yii::t('site', 'Support'), array('default/index')); ?>
		</li>
		<li class="active">
			<?php echo Yii::t('site', '外包公司'); ?>
		</li>
	</ol>
	<div class="row" id='datalist' v-cloak>
		<div class="col-md-12">
			<ul class="list-group col-md-2">
				<a class="list-group-item" data-toggle="modal" data-target="#myModal"> <span class=" glyphicon glyphicon-th-list"></span> 分类管理</a>
				<a href="javascript:void(0)" class="list-group-item all active" @click="all($event)">所有类型</a>
				<a href="javascript:void(0)" class="list-group-item all" :class='{active:index==listindex}' @click="alllist(index,list.id)" v-for='(list,index) in cagegorys'>{{list.title}}</a>

			</ul>
			<div class="col-md-10">
				<ul class="nav nav-pills col-md-12 mb15">
					<li @click="allcity($event)" class="allcity active">
						<a href="javascript:void(0)">全部</a>
					</li>
					<li v-for='(Data,index) in city' :class='{active:index==qwerqwre}' @click="currentcity(Data.diglossia_id,index)">
						<a href="javascript:void(0)">{{Data.title}}</a>
					</li>
				</ul>
				<div class="form-inline">
					<div class="form-group">
						<input type="text" class="form-control" placeholder="请输入公司名字" v-model="search">
					</div>
					<button type="submit" class="btn btn-primary pull-right" @click='modify(0)'>添加公司</button>
				</div>

				<div class="panel panel-default mt15">
				<div class="panel-body">
				<table class="table" style="table-layout:fixed">
					<thead>
						<tr>
							<th width=150>名称</th>
							<th width=100>联系人</th>
							<th width=100>联系电话</th>
							<th width=100>城市</th>
							<th width=80>是否签约</th>
							<th width=100>银行账户</th>
							<th>联系地址</th>
							<th width=100>操作</th>
						</tr>
					</thead>
					
					<tr v-for='(data,index) in searchData'>
						<td width=150>{{data.title}}</td>
						<td width=100>{{data.linkman}}</td>
						<td width=100>{{data.tel}}</td>
						<td width=100>
							<template v-for='Data in city'>
								<template v-for='city in data.city'>
									<template v-if='city==Data.diglossia_id'>
										{{Data.title}}
									</template>
								</template>
							</template>
						</td>
						<td width=80>
							<template v-if='data.is_signed==1'>
								是
							</template>
							<template v-else>
								否
							</template>
						</td>
						<td width=150>{{data.bank_account}}</td>
						<td>{{data.address}}</td>
						<td width=100>
							<a href="javascript:;" class="btn btn-xs btn-primary mr5" @click='modify(data.id)'><span class="glyphicon glyphicon-pencil"></span></a>
							<a href="javascript:;" class="btn btn-xs btn-danger  mr5" @click='vendorDel(data.id,index)'><span class="glyphicon glyphicon-trash"></span></a>

						</td>
					</tr>
				</table>
				</div>
				</div>
			</div>
		</div>
		<!-- 分类 -->
		<div class="modal fade bs-example-modal-lg" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" data-backdrop="static">
			<div class="modal-dialog " style="width: 625px;">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-hidden="true">
							&times;
						</button>
						<h4 class="modal-title" id="myModalLabel">
							分类管理
						</h4>
					</div>
					<div class="modal-body citymodify">
						<div>
							<div class="form-inline mt15" v-for='(list,index) in cagegorys'>
								<input type="text" class="form-control sign" :value='list.sign'>
								<input type="text" class="form-control cn_title" :value='list.cn_title'>
								<input type="text" class="form-control en_title" :value='list.en_title'>
								<a href="javascript:;" class="btn btn-xs btn-primary mr5" @click='addlist(list.id,$event,index)'><span class="glyphicon glyphicon-ok"></span></a>
								<a href="javascript:;" class="btn btn-xs btn-danger" @click='dellist(list.id,index)'><span class="glyphicon glyphicon-trash"></span></a>
							</div>
						</div>
					</div>
					<div class="modal-footer  text-left">
						<div class="form-inline  text-left">
							<input type="text" class="form-control sign" placeholder="请输入标记">
							<input type="text" class="form-control cn_title" placeholder="请输入中文名称">
							<input type="text" class="form-control en_title" placeholder="请输入英文名称">
							<!-- <span class="glyphicon glyphicon-ok mr15 " @click='addlist(0,$event,0)'></span> -->
							<a href="javascript:;" class="btn btn-xs btn-primary mr5" @click='addlist(0,$event,0)'><span class="glyphicon glyphicon-ok"></span></a>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!-- 公司 -->
		<div class="modal fade" id="company" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true" data-backdrop="static">
			<div class="modal-dialog">
				<?php $form = $this->beginWidget('CActiveForm', array(
				        'id' => 'expense-forms',
				        'enableAjaxValidation' => false,
				        'action' => ($model->id) ? $this->createUrl('expenesAdd', array('expenesId' => $model->id)) : $this->createUrl('vendorSave'),
				        'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form', 'enctype' => "multipart/form-data"),
				    )); ?>
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
						<h4 class="modal-title" id="myModalLabel">添加公司</h4>
					</div>
					<div class="modal-body">
						<form class="form-horizontal">
							<input type="text" :value='vendor.vendor_id' name='vendor_id' class="hidden">
							<div class="form-group">
								<label for="inputPassword" class="col-sm-2 control-label">名称：</label>
								<div class="col-sm-10">
									<input type="text" class="form-control" id="inputPassword" :value='vendor.cn_title' name='Vendor[cn_title]'>
								</div>
							</div>
							<div class="form-group">
								<label for="inputPassword" class="col-sm-2 control-label">网址：</label>
								<div class="col-sm-10">
									<input type="text" class="form-control" id="inputPassword" :value='vendor.website' name='Vendor[website]'>
								</div>
							</div>
							<div class="form-group">
								<label for="inputPassword" class="col-sm-2 control-label">联系人：</label>
								<div class="col-sm-10">
									<input type="text" class="form-control" id="inputPassword" :value='vendor.linkman' name='Vendor[linkman]'>
								</div>
							</div>
							<div class="form-group">
								<label for="inputPassword" class="col-sm-2 control-label">联系电话：</label>
								<div class="col-sm-10">
									<input type="text" class="form-control" id="inputPassword" :value='vendor.contact_method' name='Vendor[contact_method]'>
								</div>
							</div>
							<div class="form-group">
								<label for="inputPassword" class="col-sm-2 control-label">电子邮件：</label>
								<div class="col-sm-10">
									<input type="text" class="form-control" id="inputPassword" :value='vendor.email' name='Vendor[email]'>
								</div>
							</div>
							<div class="form-group">
								<label for="inputPassword" class="col-sm-2 control-label">联系地址：</label>
								<div class="col-sm-10">
									<input type="text" class="form-control" id="inputPassword" :value='vendor.address' name='Vendor[address]'>
								</div>
							</div>
							<div class="form-group">
								<label for="inputPassword" class="col-sm-2 control-label">供货周期：</label>
								<div class="col-sm-10">
									<input type="text" class="form-control" id="inputPassword" :value='vendor.lead_time' name='Vendor[lead_time]'>
								</div>
							</div>
							<div class="form-group">
								<label for="inputPassword" class="col-sm-2 control-label">主营业务：</label>
								<div class="col-sm-10">
									<select class="form-control" v-model="selected">
										<option v-for='(list,index) in cagegorys' :value="list">{{list.title}}</option>
									</select>
								</div>
							</div>
							<input type="text" :value='selected.id' name='Vendor[vendor_type_id]' class="hidden">
							<input type="text" :value='selectdept.id' name='Vendor[dept_id]'  class="hidden">
							<div class="form-group">
								<label for="inputPassword" class="col-sm-2 control-label">维护部门：</label>
								<div class="col-sm-10">
									<select class="form-control"v-model="selectdept">
										<option v-for='(list,index) in dept_id' :value="list">{{list.name}}</option>
									</select>
								</div>
							</div>
							<div class="form-group">
								<label for="inputPassword" class="col-sm-2 control-label"></label>
								<div class="col-sm-10">
									<label class="checkbox-inline col-sm-4">
										  <template v-if='vendor.is_company=="1"'>
										  	<input type="checkbox" id="inlineCheckbox3" value="1" checked="checked" name='Vendor[is_company]'> 是否为公司
										  </template>
										  <template v-else>
										  <input type="checkbox" id="inlineCheckbox3" value="1"  name='Vendor[is_company]'> 是否为公司
										  </template>
										</label>
									<label class="checkbox-inline  col-sm-3">
						
										<template v-if='vendor.is_corporatechain=="1"'>
										  	<input type="checkbox" id="inlineCheckbox3" value="1" checked="checked"  name='Vendor[is_corporatechain]'> 是否连锁
										  </template>
										  <template v-else>
										  <input type="checkbox" id="inlineCheckbox3"  value="1"  name='Vendor[is_corporatechain]'> 是否连锁
										  </template>
										</label>
									<label class="checkbox-inline col-sm-3">
										  <template v-if='vendor.is_signed=="1"'>
										  	<input type="checkbox" id="inlineCheckbox3" value="1" checked="checked"  name='Vendor[is_signed]'> 是否签约
										  </template>
										  <template v-else>
										  <input type="checkbox" id="inlineCheckbox3" value="1"  name='Vendor[is_signed]'> 是否签约
										  </template>
										</label>
								</div>
							</div>
							<div class="form-group">
								<label for="inputPassword" class="col-sm-2 control-label">城市：</label>
								<div class="col-sm-10" v-if='vendor.length!=0'>
									<template v-if='indexs.length>0'>
										<label class="checkbox-inline" v-for='Data in city'>
	                                		<input class="chk_1" type="checkbox" v-model="indexs" :value="Data.diglossia_id" name='Vendor[vendor_city][]'>{{Data.title}}
										</label>
									</template>
									<template v-else>
										<label class="checkbox-inline" v-for='Data in city'>
		                                	<input class="chk_1" type="checkbox"  :value="Data.diglossia_id" name='Vendor[vendor_city][]'>{{Data.title}}
										</label>
									</template>
								</div>
								<div class="col-sm-10" v-else>
									<template v-for='Data in city'>
										<label class="checkbox-inline">
											<input type="checkbox" id="inlineCheckbox1" :value="Data.diglossia_id" name='Vendor[vendor_city][]'> {{Data.title}}
										</label>
									</template>
								</div>
							</div>
							<div class="form-group">
								<label for="inputPassword" class="col-sm-2 control-label">开户名：</label>
								<div class="col-sm-10">
									<input type="text" class="form-control" id="inputPassword" :value='vendor.account_title' name='VendorBankAccount[account_title]'>
								</div>
							</div>
							<div class="form-group">
								<label for="inputPassword" class="col-sm-2 control-label">账号：</label>
								<div class="col-sm-10">
									<input type="text" class="form-control" id="inputPassword" :value='vendor.account' name='VendorBankAccount[account]'>
								</div>
							</div>
							<div class="form-group">
								<label for="inputPassword" class="col-sm-2 control-label">开户行：</label>
								<div class="col-sm-10">
									<input type="text" class="form-control" id="inputPassword" :value='vendor.account_bank' name='VendorBankAccount[account_bank]'>
								</div>
							</div>
							<div class="form-group">
								<label for="inputPassword" class="col-sm-2 control-label">公司简介：</label>
								<div class="col-sm-10">
									<textarea class="form-control" placeholder="简介" name='Vendor[intro]'></textarea>
								</div>
							</div>
							<div class="form-group">
								<label for="inputPassword" class="col-sm-2 control-label">备注：</label>
								<div class="col-sm-10">
									<textarea class="form-control" placeholder="备注" name='Vendor[memo]'></textarea>
								</div>
							</div>
						</form>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-primary J_ajax_submit_btn">确定</button>
						<button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
					</div>
				</div>
				<!-- /.modal-content -->
				<?php $this->endWidget(); ?>
			</div>
			<!-- /.modal -->
		</div>
	</div>
</div>
<!-- 模态框 -->
<script>
	var datas = <?php echo json_encode($data) ?>;
	var lists = <?php echo json_encode($data) ?>;
	console.log(datas)

	var datalist = new Vue({
		el: "#datalist",
		data: {
			list: datas.data,
			search: "",
			jsondata: datas.data,
			datalist: datas.data,
			city: datas.city,
			cagegorys: datas.cagegorys,		
			qwerqwre: "-1",
			listindex: '-1',
			modifys: '',
			vendor: '',
			dept_id: '',
			indexs: '',
			cityid: '',
			venId: '',
			selected:'',
			selectdept:''	
			
		},
		created: function() {
			this.search = ''  
			this.showcagegorys = lists.cagegorys
		},
		computed: {

			searchData: function() {
				var search = this.search;
				var searchVal = ''; //搜索后的数据
				if(search) {
					searchVal = this.jsondata.filter(function(product) {
						return Object.keys(product).some(function(key) {
							return String(product['title']).indexOf(search) !== -1;
						})
					})
					return searchVal;
				}
				return this.jsondata;
			}
		},
		methods: {
			getarrayitem() {
				this.indexs = this.modifys.city.map(function(json) {
					return json
				})
			},
			all(e) {
				this.jsondata = datas.data
				this.list = datas.data
				var thisDom = e.currentTarget;
				$(thisDom).addClass('active')
				this.listindex = '-1';
				if(this.cityid == '') {
					this.qwerqwre = '-1';
					$('.allcity').addClass('active')
				} else {
					this.currentcity(this.cityid, this.qwerqwre)
				}
			},
			allcity(e) {
				var thisDom = e.currentTarget;
				$(thisDom).addClass('active')
				this.qwerqwre = '-1';
				this.cityid = '';
				this.jsondata = this.list

			},
			alllist(index, id) {
				var arr = []
				this.listindex = index  
				$('.all').removeClass('active')
				for(var i = 0; i < datalist.datalist.length; i++) {
					if(id == datalist.datalist[i].type_id) {
						arr.push(datalist.datalist[i])
					}
				}
				if(this.cityid == '') {
					this.qwerqwre = '-1';
					$('.allcity').addClass('active')
					this.list = arr
					this.jsondata = arr
				} else {
					this.list = arr
					this.currentcity(this.cityid, this.qwerqwre)
				}
			},
			currentcity(cityid, index) {
				$('.allcity').removeClass('active')
				this.qwerqwre = index;
				this.cityid = cityid
				var arr = []
				for(var i = 0; i < this.list.length; i++) {
					if(this.list[i].city != null) {
						for(var j = 0; j < this.list[i].city.length; j++) {
							if(cityid == this.list[i].city[j]) {
								arr.push(this.list[i])
							}
						}
					}
				}
				this.jsondata = arr
			},
			addlist(id, event, index) {
				var dom = event.currentTarget
				  console.log($(".citymodify").scrollTop());
				$.ajax({
					url: '<?php echo $this->createUrl("categorySave")?>',
					type: "post",
					dataType: 'json',
					data: {
						type_id: id,
						cn_title: $(dom).parent().find('.cn_title').val(),
						en_title: $(dom).parent().find('.en_title').val(),
						sign: $(dom).parent().find('.sign').val()
					},
					success: function(data) {
						console.log(data)

						if(data.state == 'success') {
							if(id != "0") {
								Vue.set(datalist.cagegorys, index, data.data);	
							} else {
								datalist.cagegorys.push(data.data)
								$(dom).parent().find('input').val('')
								$('.citymodify').animate({scrollTop:$('.citymodify div').height()+'px'},500)
							}
							resultTip({
								"msg": data.message
							})
						} else {
							resultTip({
								error: 'warning',
								msg: data.message
							});
						}
					},
					error: function() {
						alert("请求错误")
					}
				});
			},
			dellist(id, index) {
				$.ajax({
					url: '<?php echo $this->createUrl("categoryDel")?>',
					type: "post",
					dataType: 'json',
					data: {
						type_id: id,
					},
					success: function(data) {
						console.log(data)

						if(data.state == 'success') {
							for(var i = 0; i < datalist.cagegorys.length; i++) {
								if(id == datalist.cagegorys[i].id) {
									Vue.delete(datalist.cagegorys, i);
									
								}
							}
							resultTip({
								"msg": data.message
							})
						} else {
							resultTip({
								error: 'warning',
								msg: data.message
							});
						}
					},
					error: function() {
						alert("请求错误")
					}
				});
			},
			modify(id) {
				console.log(id)
				datalist.venId = id
				if(id == 0) {
					$(".checkbox-inline input").prop("checked", false);
				}
				$('#J_fail_info').text('')
				for(var i = 0; i < datalist.jsondata.length; i++) {
					if(id == datalist.jsondata[i].id) {
						datalist.modifys = datalist.jsondata[i]
					}
				}
				if(datalist.modifys != '') {
					if(jQuery.type(datalist.modifys.city) === "array") {
						datalist.getarrayitem();
					}
				}
				$.ajax({
					url: '<?php echo $this->createUrl("ShowVendor")?>',
					type: "post",
					dataType: 'json',
					data: {
						vendor_id: id,
					},
					success: function(data) {
						console.log(data)
						if(data.state == 'success') {
							datalist.vendor = data.message.data
							datalist.dept_id = data.message.deptArr
							if(id == 0) {
								datalist.selectdept=data.message.deptArr[0]
								datalist.selected=datalist.cagegorys[0]
							}else{
								for(var i=0;i<data.message.deptArr.length;i++){
									if(data.message.deptArr[i].id==data.message.data.dept_id){
										datalist.selectdept=data.message.deptArr[i]
									}
								}
								for(var i=0;i<datalist.cagegorys.length;i++){
									if(datalist.cagegorys[i].id==data.message.data.vendor_type_id){
										datalist.selected=datalist.cagegorys[i]
									}
								}
							}
							$('#company').modal('show')
						} else {
							resultTip({
								error: 'warning',
								msg: data.message
							});
						}
					},
					error: function() {
						alert("请求错误")
					}
				});

			},
			vendorDel(id, index) {
				$.ajax({
					url: '<?php echo $this->createUrl("vendorDel")?>',
					type: "post",
					dataType: 'json',
					data: {
						vendor_id: id,
					},
					success: function(data) {
						console.log(data)
						if(data.state == 'success') {
							datalist.jsondata.splice(index, 1);
							for(var i = 0; i < datalist.datalist.length; i++) {
								if(data.data == datalist.datalist[i].id) {
									console.log(i)
									Vue.delete(datalist.datalist, i);
								}
							}
							for(var i = 0; i < datalist.list.length; i++) {
								if(data.data == datalist.list[i].id) {
									console.log(i)
									Vue.delete(datalist.list, i);
								}
							}
							resultTip({
								"msg": data.message
							})
						} else {
							resultTip({
								error: 'warning',
								msg: data.message
							});
						}
					},
					error: function() {
						alert("请求错误")
					}
				});
			}

		},

	})

	function cbSuccess(obj) {
		$('#company').modal('hide')
		resultTip({
			"msg": '添加成功'
		})
		if(datalist.venId == '0') {
			datalist.datalist.push(obj)
			if(datalist.list[0].type_id == obj.type_id) {
				datalist.list.push(obj)
			}
			if(jQuery.type(obj.city) === "array") {
				for(var i = 0; i < obj.city.length; i++) {
					if(obj.city[i] == datalist.cityid) {
						datalist.jsondata.push(obj)
					}
				}
			}
		} else {
			for(var i = 0; i < datalist.datalist.length; i++) {

				if(datalist.datalist[i].id == obj.id) {
					console.log(datalist.datalist[i].id)
					datalist.$set(datalist.datalist, i, obj);
				}
			}
			for(var i = 0; i < datalist.list.length; i++) {

				if(datalist.list[i].id == obj.id) {
					console.log(datalist.list[i].id)
					datalist.$set(datalist.list, i, obj);
				}
			}
			for(var i = 0; i < datalist.jsondata.length; i++) {
				if(datalist.jsondata[i].id == obj.id) {
					console.log(datalist.jsondata[i].id)
					datalist.$set(datalist.jsondata, i, obj);
				}
			}
		}
	}
</script>
