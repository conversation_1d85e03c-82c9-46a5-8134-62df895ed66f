<style>
*{
	margin:0;
	padding:0;
}
#api-s-c{
	background:transparent;
	width:665px;
	color: #333333;
	font-family: "Trebuchet MS";
	font-size:12px;	
}
#api-s-c ul, li {
    list-style-type: none;
}
#api-s-c .box-head{
	/*background: url("http://www.ivyonline.cn/themes/ivy/images/calendar_bg_header.png") no-repeat;*/
	height:18px;
	width:665px;
}
#api-s-c .calendar{
	/*background: url("http://www.ivyonline.cn/themes/ivy/images/calendar_bg.png") repeat-y scroll 0 0 transparent;*/
	padding: 0px 10px 4px;
}
#api-s-c .calendar h5{
	font-family: Georgia;
	font-size: 18px;
}
#api-s-c .calendar .caldesc{
	margin: 10px 0 10px 10px;
}
#api-s-c .calendar .caldesc li{
    background: url("https://b4oss01.ivyonline.cn/images01/images/right_gray.gif") no-repeat scroll 4px 8px transparent;
    color: #666666;
    line-height: 1.6em;
    padding-left: 14px;
}
#api-s-c .calendar .a-month{
	margin: 20px 0 40px;
}
#api-s-c .calendar .a-month .m-head{
	margin: 10px 0 4px;
	background: url("https://b4oss01.ivyonline.cn/images01/images/a-mon-head.gif") no-repeat scroll 0px 0px transparent;
	height: 51px;
}

.m-head li{
	float: left;
}
.m-head li.m-num{
	font-family: Georgia;
	font-size: 30px;
	color: #ffffff;
	line-height: 50px;
	width: 60px;
	text-align: center;
	display: inline-block;
}
.m-head li.m-year{
	font-family: Georgia;
	font-size: 18px;
	color: #2B3E42;
	line-height: 25px;
	margin-top: 20px;
	width: 60px;
	text-align: center;
	display: inline-block;

}
.m-head li.m-lite{
	font-family: Georgia;
	font-size: 16px;
	color: #2B3E42;
	line-height: 20px;
	margin-top: 0px;
	margin-left: -50px;
	width: 100px;
	text-align: left;
	display: inline-block;
}
.m-head li.m-days{
	float: right !important;
	font-family: Georgia;
	font-size: 12px;
	color: #2B3E42;
	line-height: 25px;
	margin-top: 20px;
	margin-right: 20px;
	width: 100px;
	text-align: right;
	display: inline-block;
}
.m-head li.m-days span{
	padding-right: 4px;
}
.m-data .l-main{
	float: left;
	margin-left: 9px;
	width: 323px;
	margin-right: 10px;
}
.m-data .r-desc{
	float: left;
	width: 300px;
}
#m-calendar{
	width:323px;
	margin:0;
	padding:0;
	border-left: 1px solid #D3CAAA;
	border-spacing: 0px;
	border-collapse:collapse;
}

#m-calendar td, #m-calendar th{
	line-height: 24px;
	width:46px;
	margin:0;
	padding:0;
	text-align:center;
	height:26px;
    border-bottom: 1px solid #D3CAAA;
    border-right: 1px solid #D3CAAA;	
	font-family: Georgia;
	font-size: 12px;
}
#m-calendar th{
	background: #DED5B3;	
	color: #ffffff;
}
#m-calendar .noschool, #m-calendar .schoolday, #m-calendar .weekend, #m-calendar .thisdate, #m-calendar .s1010{
	background:url("https://b4oss01.ivyonline.cn/images01/images/days-bg.gif") no-repeat scroll 0px 0px transparent;
	font-weight: bold;
}
#m-calendar .schoolday{
	background-position: 0 -26px;
	font-weight:bold;
}
#m-calendar .s1030{
	background-position: 0 -26px !important;
	font-weight: bold !important;
	color:#333333 !important;
}
#m-calendar .s1020{ /*fullday event*/
	background-position: 0 -52px;
	color:#ffffff;
	border-top: 0;
	border-left: 0;
}
#m-calendar .s1010{ /*fullday holiday*/
	background-position: 0 -78px;
	color:#ffffff;
}
#m-calendar .s2020{ /*halfday event*/
	background-position: 0 -130px;
	color:#2C5700;
}
#m-calendar .s2010{ /*halfday holiday*/
	background-position: 0 -156px;
	color:#2C5700;
}
#m-calendar .thisdate{
	background-position: 0 -104px;
	color:#ffffff;
	border-top: 0;
	border-left: 0;
}
#m-calendar .weekend{
	color:#999999;
	background-position: 0 0px;
	font-weight: normal;
}
#m-calendar .noschool{
	font-weight: normal;
}
#m-calendar .hint{
	color:#dedede;
	font-weight: normal;
}
.c-memo{
	padding-top: 26px;
	line-height: 26px;
	color: #2B3E42;
}
.clear{
	clear:both;
}
</style>



<div id="api-s-c">
<div class="box-head">
</div>
<div class="calendar">
	<h5><?php echo sprintf(Yii::t("global", "%d-%d %s School Calendar"), $this->startYear, $this->startYear+1, $branch->title);?></h5>
	<div class="caldesc">
		<ul>
			<li><?php echo sprintf(Yii::t("global",'Total School Days: %d Days'),$stats['total']);?></li>
			<li><?php echo sprintf(Yii::t("global",'Fall Semester School Days: %d Days'),$stats['fall']);?></li>
			<li><?php echo sprintf(Yii::t("global",'Spring Semester School Days: %d Days'),$stats['spring']);?></li>			
		</ul>					
	</div>
	<?php foreach($months as $key=>$month):?>
	<div class="a-month">
		<div class="m-head">
			<ul>
				<li class="m-days"><span><?php echo $month['schoolday'];?></span><?php echo Yii::t("global", 'Days');?></li>
				<li class="m-num"><?php echo $month['month'];?></li>
				<li class="m-year"></li>
				<li class="m-lite"><?php echo $month['yearmonth'];?></li>
			</ul>
		</div>
		<div class="clear"></div>
		<div class="m-data">
			<div class="l-main">
				<table id="m-calendar">
				<thead>
				<tr>
					<th><span title="Sunday">Sun</span></th>
					<th><span title="Monday">Mon</span></th>
					<th><span title="Tuesday">Tue</span></th>
					<th><span title="Wednesday">Wed</span></th>
					<th><span title="Thursday">Thu</span></th>
					<th><span title="Friday">Fri</span></th>
					<th><span title="Saturday">Sat</span></th>
				</tr>
				</thead>
				<tbody>
				<tr align="center">
				
				<?php 
				if ($month['calendars']['calendar']):
				foreach($month['calendars']['calendar'] as $day):?>

				  <td class="
					<?php
					if(isset($day['hint'])&&$day['hint'])
						echo 'hint ';
					elseif(isset($day['isschoolday'])&&$day['isschoolday'])
						echo 'schoolday ';
					elseif(isset($day['isweekend'])&&$day['isweekend'])
						echo 'weekend ';
					if(!empty($day['iscalendarday']))
						echo $day['iscalendarday']['class'];
					?>
				  "
					
					<?php
					if(!empty($day['iscalendarday']))
						echo "title='". CHtml::encode($day['iscalendarday']['tip'])."'";
					?>
				  
				  >
					<?php echo empty($day['date'])?' ':$day['date'];?>
				  </td>
				<?php if(isset($day['newline'])&&$day['newline']):?>
				</tr>
					<?php if(isset($day['islastday'])&&$day['islastday']==0):?>
					<?php endif;?>
				<?php endif;?>

				<?php endforeach;?>
				<?php endif;?>
				<?php if(!empty($month['endnulls'])):?>
					</tr>
				<?php endif;?>
				</tbody>
				</table>			
			</div>
			<div class="r-desc c-memo">
                <?php if (isset($descs[$key])):?>
                <?php $f = new CFormatter();?>
				<?php echo $f->formatNtext((Yii::app()->language =='zh_cn') ? $descs[$key]['memo_cn']:$descs[$key]['memo_en']);?>
                <?php endif;?>
			</div>
			<div class="clear"></div>
		</div>

	</div>
	<?php endforeach; ?>
	
	<p>
	<?php
	echo Yii::t("global","Note: Calendar subject to change based on government's announcement of official public holiday schedule.");
	?>
	</p>
	<div class="clear"></div>
	
</div>
</div>
