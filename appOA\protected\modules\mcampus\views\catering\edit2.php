<?php $weekday = array('mon', 'tue', 'wed', 'thu', 'fri');?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo Yii::t('site','Campus Workspace');?></li>
        <li><?php echo Yii::t('site','Support');?></li>
        <li><?php echo CHtml::link(Yii::t('site','Catering Management'), array('index'));?></li>
        <li class="active"><?php echo $model->title_cn?></li>
    </ol>
    <?php
    echo CHtml::form($this->createUrl('save2'), 'post', array('class'=>'J_ajaxForm'));
    echo CHtml::hiddenField('id', $model->id);
    ?>
    <table class="table">
        <thead>
        <tr>
            <th width="10%"></th>
            <?php for($i=1;$i<6;$i++):?>
            <th width="15%"><?php echo Yii::app()->locale->getWeekDayName($i);?></th>
            <?php endfor;?>
        </tr>
        </thead>
        <tbody>
        <?php foreach($lunchs as $lkey=>$lunch):?>
        <tr>
            <th><?php echo $lunch;?></th>
            <?php foreach($weekday as $week):?>
                <td>
                    <?php echo CHtml::textArea('menu['.$lkey.']['.$week.']', $menus[$lkey][$week]['content'], array('class'=>'form-control mb10', 'rows'=>8));?>
                    <?php if(in_array($lkey, array(102, 103, 104, 105, 106))):?>
                        <div class="mb10 text-center" id="img-<?php echo $lkey.$week?>">
                            <?php
                            if($menus[$lkey][$week]['photo']){
                                echo CHtml::image(
                                    Yii::app()->params['OAUploadBaseUrl'].'/lunch/'.$menus[$lkey][$week]['photo'],
                                    '',
                                    array('style'=>'width:100%;', 'class'=>'img-rounded')
                                );
                            }
                            ?>
                        </div>
                        <div class="text-center">
                            <button class="btn btn-primary btn-xs" type="button" id="upload-<?php echo $lkey.$week;?>"><?php echo Yii::t('lunch', 'Upload Photos');?></button>
                            <?php echo CHtml::link(Yii::t('lunch', 'Delete Photos'), array('delPhoto', 'lkey'=>$lkey, 'id'=>$model->id, 'weekday'=>$week), array('class'=>'btn btn-danger btn-xs J_ajax_del'))?>
                        </div>
                    <?php endif;?>
                </td>
            <?php endforeach;?>
        </tr>
        <?php endforeach;?>
        </tbody>
    </table>
    <div class="text-center"><button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit')?></button></div>
    <?php echo CHtml::endForm();?>
</div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>

<script>
    var config = {
        runtimes : 'html4',
        url : '<?php echo $this->createUrl('upload')?>',
        filters : {
            max_file_size : '4mb',
            mime_types: [
                {title : "<?php echo Yii::t('teaching', 'Image files');?>", extensions : "jpg,gif,png,jpeg"}
            ]
        },
        init: {
            FilesAdded: function(up, files) {
                up.start();
            },
            Error: function(up, err) {
                resultTip({msg: err.message, error: 1});
            },
            FileUploaded: function(up, file, info) {
                var response = eval('('+info.response+')');
                if(info.status == 200){
                    var img = new Image();
                    img.className = 'img-rounded';
                    img.style.width = '100%';
                    img.src = response.data.photo;
                    img.onload = function(){
                        $('#img-'+response.data.lkey+response.data.weekday).html(img);
                    }
                    resultTip({msg: response.message});
                }
            }
        }
    };

    var weekdays = <?php echo CJSON::encode($weekday)?>;
    var lkeys = <?php echo CJSON::encode(array_keys($lunchs))?>;
    for(var k in lkeys){
        var lkey = lkeys[k];
        for(var i in weekdays){
            config['browse_button'] = 'upload-'+lkey+weekdays[i];
            config['multipart_params'] = {lkey: lkey , weekday: weekdays[i], id: <?php echo $model->id?>};
            var uploader = new plupload.Uploader(config);
            uploader.init();
        }
    }


    function cbDel(data)
    {
        $('#img-'+data.lkey+data.weekday).html('');
    }
</script>