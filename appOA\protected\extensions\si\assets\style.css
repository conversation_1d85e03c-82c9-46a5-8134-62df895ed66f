@import "animate.min.css";
@import "/themes/label/iconfont.css?v20241108";

.animate {
    position: fixed;
    bottom: 220px;
    right: calc(50% - 20px);
    height: 40px;
    width:40px;
    z-index:1000
}

.si-logo {
    height: 40px;
    position: fixed;
    right: 0px;
    width: calc(50% - 0px);
    animation: bounce;
    animation-duration: 2s;
}

.si-logo span {
    /*box-shadow: 0 2px 16px 0 rgba(0,0,0,0.2);*/
    /*border: 1px solid #F2F3F5;*/
    /*border-radius:12px;*/
    display: flex;
    justify-content: center;
    align-items: center;
    animation: bounce;
    content: " ";
    background-image: url("si-logo-on.gif");
    background-size: 40px 40px;
    background-repeat: no-repeat;
    height: 40px;
    width: 40px;
    float: right;
    background-position: center;
}

.si-logo-off span {
    background-image: url("si-logo-off.png") !important;
}


.si-tips {
    z-index:1001;
    position: fixed;
    bottom: 200px;
    left:50%;
    transform: translateX(-50%);
    max-width: 600px;
    min-width: 340px;
    height: 216px;
    background: #FFFBF1;
    box-shadow: 0px 2px 16px 0px rgba(0,0,0,0.2);
    border-radius: 8px;
    padding:20px 24px;
    /*background-image: url(box-bg.png);*/
    background-size: 496px;
    background-repeat: no-repeat;
    background-position: bottom;
    font-family: "Helvetica Neue","PingFang SC","Microsoft YaHei","微软雅黑",Arial,sans-serif;
    background: linear-gradient(180deg, #FFF5D6 0%, #FFFDF8 100%)
}
.si-tips::before {
    content: " ";
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: #FFF5D7;
    display: block;
    position: absolute;
    left: calc(50% - 40px);
    top: -30px;
    box-shadow: 0px 2px 16px 0px rgba(0,0,0,0.2);
}

.si-tips::after {
    content: " ";
    width: 60px;
    height: 60px;
    display: block;
    position: absolute;
    left: calc(50% - 30px);
    top: -21px;
    background-image: url("si-logo-window.png");
    background-size: contain;
    background-repeat: no-repeat;
    background-position-x: center;
}

.si-tips .mask {
    width: 110px;
    height: 64px;
    display: block;
    position: absolute;
    left: calc(50% - 55px);
    top: 0;
    background: linear-gradient(180deg, #FFF5D7 0%, #FFF7DD 100%)
}

.si-tips .image{
    background-image: url("si-logo.png");
    background-size: cover;
    width:119px;
    height:36px;
}
.si-tips .flexStart{
    align-items: start;
}
.si-tips .baseline{
    display: flex;
    align-items: baseline;
}
.si-tips .quote_fill{
    color:#ccc;
    font-size:21px;
}
 .image{
    background-image: url("si-logo.png");
    background-size: cover;
    width:105px;
    height:36px;
}
.si-tips .good{
    border: 1px solid #4D88D2;
    font-size: 16px;
    border-radius: 50%;
    text-align: center;
    padding: 8px;
    color: #4D88D2;
    cursor: pointer;
    width: 34px;
    height: 34px;
    margin: 0 auto;
}
.si-tips .good span{
    display: block;
    width: 16px;
    height: 36px;
    margin-top: -3px;
}
.animate__backInRights {
    -webkit-animation-name: backInRights;
    animation-name: backInRights
}
.animate__backOutRights {
    -webkit-animation-name: backOutRights;
    animation-name: backOutRights
}

.si-tips{
    height:auto
}
.color6{
    color:#666 !important
}
.closed{
    font-size: 24px;
    height: 10px;
    display: inline-block;
    line-height:13px;
    cursor: pointer;
}
.blueHover:hover{
    color:#428bca !important
}
.linkUser{
    text-align: center;
    margin: 0 20px;
}
.img28{
    width: 28px;
    height: 28px;
    border-radius: 50%;
    margin: 4px 0;
    object-fit: cover;
    border:1px solid #fff;
    display:inline-block;
    margin-left:-10px
}
.user{
    padding: 10px 0 0 10px
}
.img28:hover{
    transform: scale(1.35);
    box-shadow: 0px 2px 16px 0px rgba(0,0,0,0.2), 0px 2px 6px 0px rgba(0,0,0,0.3);
}
.blueBg{
    background:#428bca
}
.goodLike{
    color:#fff
}
.blueBgHover:hover{
    background:#E8F6FF
}
.goodIcon{
    display: inline-block;
    position: relative;
}
#well_know_like{
    position: absolute;
    left:20px;
    top: -12px;
    background: #E8F6FF;
    font-size: 14px;
    color: #4D88D2;
    font-weight:normal;
    border: 1px solid #FFFFFF;
    padding:2px 6px;
}