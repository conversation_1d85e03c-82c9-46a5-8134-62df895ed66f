<style>
    .bg{
        width:20px;
        height: 20px;
        border-radius:50%;
        display: inline-block;
        line-height:20px;
        text-align: center;
        padding: 0;
    }
    [v-cloak] {
        display: none;
    }
    .nav-wizard > li>a:hover{
        background:#d5d5d5
    }
    .nav-wizard > li{
        text-align: center;
    }
    .nav-wizard a{
        color:#ADADAD;
        font-size:14px
    }
    .number{
        display: inline-block;
        width:17px;
        height:17px;
        border:1px solid #ADADAD;
        border-radius:50%;
        text-align: center;
        line-height:17px;
        margin-right:14px
    }
    .nav-wizard > li:not(:first-child) > a:before{
        border-top: 23px inset transparent;
        border-bottom: 18px inset transparent;
    }
    .nav-wizard > li:not(:last-child) > a:after{
        border-top: 23px inset transparent;
        border-bottom: 17px inset transparent;
    }
    .active .number{
        border:1px solid #fff;
    }
    .sign{
	    width:50px;
    }
    .card{
        width:40px
    }
    .badge-error {
      background-color: #b94a48;
    }
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', '新生注册'), array('//mcampus/registration/index')) ?></li>
        <li class="active"><?php echo Yii::t('site','完成情况') ?></li>
    </ol>
    <div class="row">
        <div class="col-md-2 col-sm-2">
            <ul class="nav nav-pills nav-stacked text-left background-gray" id="pageCategory">
                <?php foreach ($this->getSubMenu() as $val){ ?>
                    <li class="<?php echo ($this->getAction()->getId() == $val['url']) ? "active " : ""; ?>" ><a href="<?php echo $this->createUrlReg($val['url']) ?>"><?php echo $val['label'] ?></a></li>
                <?php } ?>
            </ul>
        </div>
        <div class="col-md-10 panel panel-default">
            <div class="btn-group mb10 row">
                <!-- 搜索框 -->
                <div class="col-md-12">
                    <br>
                </div>
                <form action="<?php echo $this->createUrl('studentsList'); ?>" method="get">
                    <?php echo Chtml::hiddenField('branchId',$this->branchId); ?>
                    <div class="col-lg-12 form-inline">
                        <!-- 内容匹配 -->
                        <?php echo Chtml::dropDownList('yid', $yid, $startYearList,array('class'=>'form-control')); ?>
                        <div class="form-group">
                            <div class="input-group">
                                <input placeholder="孩子姓名" type="text" class="form-control" name="search" value="<?php echo Yii::app()->request->getParam('search','')?Yii::app()->request->getParam('search',''):''; ?>">
                                <span class="input-group-btn">
                            <button class="btn btn-default" type="submit"><span class="glyphicon glyphicon-search"> </span> </button>
                            </span>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="col-md-12">
                <br>
                <button class="btn btn-primary btn-xs" onclick="visitcheck(this)">批量打印学生家庭证件</button>
                <button class="btn btn-primary btn-xs" onclick="visitAllcheck(this)">批量打印学生信息</button>
                <button class="btn btn-primary btn-xs" onclick="exportChildInfo(this)">导出Excel</button>
            </div>
            <div class="panel-body">
                <?php
                $this->widget('ext.ivyCGridView.BsCGridView', array(
                    'id'=>'confirmed-visit-grid',
                    'afterAjaxUpdate'=>'js:head.Util.modal',
                    'dataProvider'=>$dataProvider,
                    'template'=>"{items}{pager}{summary}",
                    'enableSorting'=>false,
                    'colgroups'=>array(
                        array(
                            "colwidth"=>array(100,100,100,100,100,100,100,100,200),
                        )
                    ),
                    'columns'=>array(
                        array(
                            'class' => 'CCheckBoxColumn',
                            'selectableRows' => 2,
                            'checkBoxHtmlOptions' => array(
                                'class' => 'childid',
                                'value' => '$data->id',
                            ),
                        ),
                        array(
                            'name'=> Yii::t('user','中文姓名'),
                            'type'=>'raw',
                            'value'=>'$data->childProfile->getChildName()',
                        ),
                        array(
                            'name'=> Yii::t('user','班级'),
                            'type'=>'raw',
                            'value'=>'$data->childProfile->ivyclass->title',
                        ),
                        array(
                            'name'=> Yii::t('user','生日'),
                            'type'=>'raw',
                            'value'=>'$data->childProfile->birthday_search',
                        ),
                        array(
                            'name'=>'操作',
                            'value'=> array($this, 'getButton'),
                        ),
                    ),
                ));
                ?>
            </div>
            <div class="col-md-12">
                <br>
                <button class="btn btn-primary btn-xs" onclick="visitcheck(this)">批量打印学生家庭证件</button>
                <button class="btn btn-primary btn-xs" onclick="visitAllcheck(this)">批量打印学生信息</button>
                <button class="btn btn-primary btn-xs" onclick="exportChildInfo(this)">导出Excel</button>
            </div>
            <div class="col-md-12">
                <br>
                <span id="visit-error" class="text-warning"></span>
            </div>
        </div>
    </div>
</div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    // 批量打印学⽣家庭证件
    function visitcheck(btn) {
        var send = $("#visit-send").prop('checked');
        var id = [];

        $('input[class="childid"]:checked').each(function(){
            id.push($(this).val());
        });

        if (id.length == 0) {
            resultTip({msg: "至少选择一个记录", error: 1});
            return;
        }
        window.open("<?php echo $this->createUrlReg('printStudent') ?>?yid=<?php echo $yid ?>&ids="+ id.toString());
    }
    // 批量打印学⽣家庭证件+学⽣学籍信息采集+安全协议
    function visitAllcheck(btn) {
        var send = $("#visit-send").prop('checked');
        var id = [];
        $('input[class="childid"]:checked').each(function(){
            id.push($(this).val());
        });

        if (id.length == 0) {
            resultTip({msg: "至少选择一个记录", error: 1});
            return;
        }
        window.open("<?php echo $this->createUrlReg('printStudentAllInfo') ?>?yid=<?php echo $yid ?>&ids="+ id.toString());
    }

    function exportChildInfo(btn) {
        var send = $("#visit-send").prop('checked');
        var id = [];
        $('input[class="childid"]:checked').each(function(){
            id.push($(this).val());
        });
        if (id.length == 0) {
            resultTip({msg: "至少选择一个记录", error: 1});
            return;
        }
        $.ajax({
            url: "<?php echo $this->createUrlReg('exportChildsInfo')?>",
            type: 'POST',
            data: {id:id},
            success: function (res) {
                if (res.state == 'success') {
                    var data = res.data.items;
                    const filename = res.data.title;
                    const ws_name = "SheetJS";

                    const worksheet = XLSX.utils.aoa_to_sheet(data);
                    const workbook = XLSX.utils.book_new();
                    XLSX.utils.book_append_sheet(workbook, worksheet, ws_name);
                    // generate Blob
                    const wbout = XLSX.write(workbook, {bookType: 'xlsx', type: 'array'});
                    const blob = new Blob([wbout], {type: 'application/octet-stream'});
                    // save file
                    let link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    link.download = filename;
                    link.click();
                    setTimeout(function() {
                        // 延时释放掉obj
                        URL.revokeObjectURL(link.href);
                        link.remove();
                    }, 500);
                }
            },
            dataType: 'json'
        });
    }
</script>