<style>
    hr {
        margin:20px 0;
    }
</style>
<div class="">
    <div class="">
        <h4 class="text-center" style="margin: 0 0 20px"><?php echo Yii::t("asa","INVOICE");?></h4>
        <div class="col-xs-4"><?php echo Yii::t("asa","For Student:");?> <?php echo $child->getChildName() ?></div>

        <div class="col-xs-4"><?php echo Yii::t("asa","Class:");?> <?php echo $child->ivyclass->title ?></div>

        <div class="col-xs-4"><?php echo Yii::t("asa","Due Date:");?> <div style="display:inline-block;width: 100px;height: 14px;border-bottom: 1px solid #0f0f0f"> </div></div>

        <div style="clear: both"></div>
    </div>
    <hr/>
    <div class="">

        <?php if($invoice): ?>
            <?php foreach($invoice->item as $k => $item): ?>
                <div class="col-xs-4"><?php echo ($k < 1) ? Yii::t("asa","Invoice") : "";?></div>
                <div class="col-xs-4">
                    <div><?php echo $item->asacourse->title_cn ?></div>
                </div>
                <div class="col-xs-4 text-right">
                    <div><?php echo $item->unit_price . " * " . $item->class_count . " = " . $item->actual_total ?></div>
                </div>
                <div style="clear: both"></div>
            <?php endforeach; ?>
        <?php endif; ?>

    </div>
    <hr/>
    <div class="">
        <div class="col-xs-2"><?php echo Yii::t("asa","Total");?></div>

        <div class="col-xs-10 text-right">
            <div>¥ <?php echo $invoice->amount_original; ?></div>
        </div>
        <div style="clear: both"></div>
    </div>
    <hr/>
    <div class="">
        <div class="col-xs-8">
            <?php echo ($groupAgreement) ? nl2br($groupAgreement->getTitle()) : "" ?>
        </div>
        <div class="col-xs-4 text-center">
            <div class=""><?php echo Yii::t("asa","Please scan the QR code with Wechat to make the payment online.");?><br/></div>
            <img width="200px" src="http://osspub.ivykids.cn/asa/qrcode.jpg" alt="QRcode"/>
        </div>
        <div style="clear: both"></div>
    </div>
</div>