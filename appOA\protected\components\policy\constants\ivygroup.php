<?php

if(defined("POLICY_CONSTANTS"))
return;
Yii::import('common.models.invoice.*');
$defines = array(

	"ITEMS",
	"LUNCHA",
	"LUNCHB",
	"PRE_FILTER",
	"TUITION_CALC_BASE", //学费计算基准，配合 PAYGROUP_ANNUAL 等配置

	//账单分类
	"CAT_INITIALS", 	//初始账单
	"CAT_NORMAL",		//常规账单
	"CAT_OTHER",		//其他账单

	//账单类型
	"FEETYPE_MATERIAL",  	  //入园材料费
	"FEETYPE_DEPOSIT",		  //预缴学费
	"FEETYPE_SCHOOLBUS",	  //校车费
	"FEETYPE_CAROUSEL",		  //亲子班
	"FEETYPE_AFTERSCHOOL",	  //课后课
	"FEETYPE_TUITION",		  //学费
	"FEETYPE_LUNCH",		  //餐费
	"FEETYPE_CHILDCARE",	  //保育费
	"FEETYPE_SUBSIDY",		  //政府补贴
	"FEETYPE_COLLECTION",	  //代收代缴
    "FEETYPE_LIBCARD",        //图书卡工本费
    "FEETYPE_ACCOUNTBALANCE", //个人账户处置
    "FEETYPE_BREAKFAST",      //早餐费
    "FEETYPE_CASH_OUT",       //提现
    "FEETYPE_TRANSFER_CASH",  //转校个户充值
	"FEETYPE_UNIFORM",  	  //校服费

	"REG_DEPOSIT",

	//是否老生老办法
	"ENABLE_CONSTANT_TUITION",

	"FROM_CONSTANT", 	//读取的老生老办法配置
	"FROM_CONFIG",		//读取的配置文件

	//年保育费
	"ANNUAL_CHILDCARE_AMOUNT",
    "CALC_CHILDCARE_PEROID",//保育费付款类型区分标识

	//是否开放课外活动账单
	"ENABLE_AFTERSCHOOLS",

	//图书馆工本费
//	"FEETYPE_LIBCARD",

	//学期四个时间点；取前后两端
	"TIME_POINTS",

	//寒暑假月份，及其每月计费天数
	"HOLIDAY_MONTH",
	"MONTH",

	"PAYGROUP_ANNUAL",				//年付费类型组
	"PAYGROUP_SEMESTER",			//学期付费类型组
	"PAYGROUP_MONTH",				//月付费类型组

	"TUITION_CALCULATION_BASE", 	//计算基准
	"BY_MONTH",
	"BY_ANNUAL",
	"BY_SEMESTER1",
	"BY_SEMESTER2",

	//每餐费用
	"LUNCH_PERDAY",

	//账单到期日和账单开始日之差
	"DUEDAYS_OFFSET",

	//全局折扣
	"GLOBAL_DISCOUNT",
	"DISCOUNT_ANNUAL", 			//年付折扣
	"DISCOUNT_SEMESTER1", 	//上学期折扣
	"DISCOUNT_SEMESTER2", 	//下学期折扣
	"TITLE",
	"FEETYPE",
	"AMOUNT", 					//金额
	"DISCOUNTID",				//折扣
	"FULL5D", "FULL2D", "HALF2D", "HALF3D", "HALF5D", "FULL3D", "FULL4D", "HALF4D",

	"PROGRAM_MIK",
	"PROGRAM_IBS",
	"PROGRAM_IA",
	"OTHER_PROGRAM_CHN",
	"OTHER_PROGRAM_CHN_1",
	"OTHER_PROGRAM_CHILDCARE",

	//收费月份，及其权重
	"MONTH_FACTOR",
    //代金卷金额
    "VOUCHER_AMOUNT",
    "EXCEPTS",
    "ENDDATE",
    "MORE_FEE_BY_SCHOOL",//学校下多种收费方式

    "FENTAN_FACTOR",
    "DAILY",
    "LUNCH_PERMONTH",
    "BY_SEMESTER"
);

$defines[] = "POLICY_CONSTANTS";
foreach($defines as $define){
	define($define, $define);
}

global $paymentMappings;
$paymentMappings = array(
	"dbFeeType" => array(
            FEETYPE_MATERIAL => 'registration',
            FEETYPE_DEPOSIT => 'deposit',
            FEETYPE_SCHOOLBUS => 'bus',
            FEETYPE_CAROUSEL => 'carousel',
            FEETYPE_AFTERSCHOOL => 'afterschool',
            FEETYPE_TUITION => 'tuition',
            FEETYPE_LUNCH => 'lunch',
            FEETYPE_CHILDCARE => 'childcare',
            FEETYPE_LIBCARD => 'library_card_cost',
            FEETYPE_SUBSIDY => 'preschool_subsidy',
            FEETYPE_COLLECTION => 'sponsorship_fee',
            FEETYPE_ACCOUNTBALANCE => 'account_balance_disposal',
            FEETYPE_BREAKFAST => 'breakfast',
            FEETYPE_CASH_OUT => 'cash',
            FEETYPE_TRANSFER_CASH => 'transfer_cash',
			FEETYPE_UNIFORM => 'uniform',

    ),

	"titleFeeType" => array(
            FEETYPE_MATERIAL => "Material Fee",
            FEETYPE_DEPOSIT => "Tuition Deposit",
            FEETYPE_SCHOOLBUS => "School Bus",
            FEETYPE_CAROUSEL => "CaRousel",
            FEETYPE_AFTERSCHOOL => "After School",
            FEETYPE_TUITION => "Tuition",
            FEETYPE_LUNCH => "Lunch",
            FEETYPE_CHILDCARE => "保育费",
            FEETYPE_LIBCARD => "Library Card",
            FEETYPE_SUBSIDY => '政府补贴',
            FEETYPE_COLLECTION => '代收代缴',
            FEETYPE_ACCOUNTBALANCE => '个人账户处置',
            FEETYPE_CASH_OUT => '提现',
            FEETYPE_TRANSFER_CASH => '转校个户充值',
			FEETYPE_UNIFORM => 'Uniform',
	),

	"invoiceStatus" => array(
		Invoice::STATS_UNPAID => "UnPaid",
		Invoice::STATS_PAID => "Paid",
		Invoice::STATS_PARTIALLY_PAID => "Partially Paid",
		Invoice::STATS_AWAITING_DISCOUNT => "Awaiting for Discount Approval",
		Invoice::STATS_AWAITING_CHANGE => "Awaiting for Change Approval",
		Invoice::STATS_CHANGE_ARCHIVED => "Changed Archive",
		Invoice::STATS_CANCELLED => "Cancelled",
	),

	"refundStatus" => array(
		InvoiceChildRefund::STATS_COMPLETED => "退款完成",
		InvoiceChildRefund::STATS_AWAITING => "等待审核",
		InvoiceChildRefund::STATS_CANCELLED => "申请被驳回",
	),

	"transactionType"=>array(
		InvoiceTransaction::TYPE_CASH => "现金",
		InvoiceTransaction::TYPE_TRANSFER => "银行转帐",
		InvoiceTransaction::TYPE_CHECK => "支票",
		InvoiceTransaction::TYPE_USERCREDIT => "使用个人账户",
		InvoiceTransaction::TYPE_BANKCARD => "非订单POS",
		InvoiceTransaction::TYPE_POS => "易宝POS",
		InvoiceTransaction::TYPE_POS_ALLINPAY => "通联POS",
		InvoiceTransaction::TYPE_ONLINEPAYMENT => "在线支付",
		InvoiceTransaction::TYPE_TOCREDIT => "转到个人账户",
		InvoiceTransaction::TYPE_VOUCHER => "代金券",
		InvoiceTransaction::TYPE_EDUFEE => "学前保教费减免",
		InvoiceTransaction::TYPE_WX_MICROPAY => "微信刷卡支付",
		InvoiceTransaction::TYPE_WX_NATIVE => "微信扫码支付",
		InvoiceTransaction::TYPE_ONLINE_ALLINPAY => "通联在线支付",
	),

	"programTitle" => array(
		FULL5D => '5 Full Days',
		FULL4D => '4 Full Days',
		FULL3D => '3 Full Days',
		FULL2D => '2 Full Days',
		HALF5D => '5 Half Days',
		HALF4D => '4 Half Days',
		HALF3D => '3 Half Days',
		HALF2D => '2 Half Days',
		OTHER_PROGRAM_CHN => 'Chinese Class',
		OTHER_PROGRAM_CHN_1 => 'Chinese Class',
		PROGRAM_MIK => 'MIK Class',
        OTHER_PROGRAM_CHILDCARE => '亲子班',
	),

    "invoicePeriodTypes" => array(
        PAYGROUP_ANNUAL => 1,
        PAYGROUP_SEMESTER => 2,
        PAYGROUP_MONTH => 10,
    ),

    'lunch' => array(
        LUNCHA => 'Lunch A',
        LUNCHB => 'Lunch B',
    ),
);
