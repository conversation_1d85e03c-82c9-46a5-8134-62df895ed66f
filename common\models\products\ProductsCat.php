<?php

/**
 * This is the model class for table "ivy_products_cat".
 *
 * The followings are the available columns in table 'ivy_products_cat':
 * @property integer $id
 * @property string $title_cn
 * @property string $title_en
 * @property string $desc_cn
 * @property string $desc_en
 * @property integer $status
 * @property string $school_id
 * @property integer $updated_userid
 * @property integer $updated_time
 */
class ProductsCat extends CActiveRecord
{
    public $photo;
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_products_cat';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('title_cn, title_en, status, school_id, updated_userid, updated_time', 'required'),
			array('status, updated_userid, updated_time, sort', 'numerical', 'integerOnly'=>true),
			array('title_cn, title_en, img, school_id', 'length', 'max'=>255),
			array('desc_cn, desc_en', 'length', 'max'=>500),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, title_cn, title_en, desc_cn,desc_en,img, status, school_id, photo, updated_userid, updated_time, sort', 'safe', 'on'=>'search'),
            array("photo", "file", "types" => "jpg, gif, png, jpeg", "maxSize" => 1024*1024*2, "allowEmpty" => ($this->img) ? true : false),
		);
	}

    protected function beforeSave()
    {
        if (parent::beforeSave()) {
            $imgs= ($this->img) ? $this->img : "";
            if($this->photo)
            {
                $file_ext = $this->photo->getExtensionName();
                $picPath = Yii::app()->params['OAUploadBasePath'] . '/productsCat/';
                $picName = 'productsCat' . uniqid() . '.' . $file_ext;
                if ($this->photo->saveAs($picPath . $picName)){
                    Yii::import('application.extensions.image.Image');
                    $image = new Image($picPath . $picName);
                    $image->quality(100);
                    $image->resize(900,500);
                    $image->save( $picPath . $picName);
                    $this->img = $picName;

                    if($imgs){
                        unlink($picPath . $imgs);
                    }
                }
            }
            return true;
        }
        return false;
    }

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'title_cn' => Yii::t('asainvoice','Title Cn'),
			'title_en' => Yii::t('asainvoice','Title En'),
			'desc_cn' => Yii::t('asainvoice','Subtitle Cn'),
			'desc_en' => Yii::t('asainvoice','Subtitle En'),
			'img' => Yii::t('user','Select a Photo'),
			'status' => Yii::t('invoice','Status'),
			'sort' => Yii::t('campus','Sort'),
			'school_id' => 'School',
			'updated_userid' => 'Updated Userid',
			'updated_time' => 'Updated Time',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('title_cn',$this->title_cn,true);
		$criteria->compare('title_en',$this->title_en,true);
		$criteria->compare('desc_cn',$this->desc_cn,true);
		$criteria->compare('desc_en',$this->desc_en,true);
		$criteria->compare('img',$this->img,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('sort',$this->sort);
		$criteria->compare('school_id',$this->school_id,true);
		$criteria->compare('updated_userid',$this->updated_userid);
		$criteria->compare('updated_time',$this->updated_time);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return ProductsCat the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public function getName()
    {
        switch (Yii::app()->language) {
            case "zh_cn":
                return $this->title_cn;
                break;
            case "en_us":
                return $this->title_en;
                break;
        }
    }

    public function getDesc()
    {
        switch (Yii::app()->language) {
            case "zh_cn":
                return $this->desc_cn;
                break;
            case "en_us":
                return $this->desc_en;
                break;
        }
    }
}
