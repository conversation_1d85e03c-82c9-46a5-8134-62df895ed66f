<?php
error_reporting(0);

class ChildSignController extends BranchBasedController
{

    public $actionAccessAuths = array(
        'index'           => 'o_A_Access',
        'Vacation'        => 'o_A_Access',
        'Vacationadd'     => 'o_A_Access',
        'Vacation_detele' => 'o_A_Access',
        'AutoSign'        => 'o_A_Access',
        'DelSign'         => 'o_A_Access',
        'SignAll'         => 'o_A_Access',
        'AddTrack'        => 'o_A_Access',
    );

    public $dialogWidth = 600;
    public $class_list  = array();
    public $printFW     = array();

    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout     = "//layouts/column1";

        $this->modernMenuFlag          = 'campusOp';
        $this->modernMenu              = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Campus Workspace');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        if (Yii::app()->params['siteFlag'] == 'daystar') {
            $this->branchSelectParams['urlArray'] = array('//mcampus/childsign/index2');
        } else {
            $this->branchSelectParams['urlArray'] = array('//mcampus/childsign/index');
        }
        Yii::import('common.models.child.*');
        // jquery ui
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl() . '/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile($cs->getCoreScriptUrl() . '/jui/js/jquery-ui.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/jquery.jPrintArea.js');
//        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/xlsx.full.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/exceljs/xlsx.core.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/exceljs/xlsxStyle.core.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/exceljs/openDownload.js');
        $cs->registerCssFile(Yii::app()->theme->baseUrl . '/css/calendar.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/v-calendar/vue.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/v-calendar/v-calendar.umd.min.js');
        Yii::import('common.models.attendance.*');
        Yii::import('common.models.calendar.*');
        Yii::import('common.models.timetable.*');
    }

    // 自动签到
    public function actionIndex()
    {
        $this->branchSelectParams['urlArray'] = array('//mcampus/childsign/index');
        $date      = Yii::app()->request->getParam('date', '');
        $timestamp = strtotime($date);
        $classes_data = $this->getClassList2($timestamp);
        $select_yid   = $classes_data['select_yid'];//选择的学年
        $current_yid  = $classes_data['currentYid'];//当前学年
        // 已经请假的孩子数据
        $vacations = ChildVacation::model()->getVacations($timestamp, $this->branchId);
        //已签到的孩子数据
        $signChilds = $this->getSignChild($timestamp);
        //获取所有孩子
        if ($select_yid == $current_yid) {
            //当前学年
            $classes = $this->getClassList();
            $classIds = array();
            foreach ($classes as $_class) {
                $classIds[$_class->classid] = $_class->classid;
            }
            $status    = '<999';
            $childData = $this->getStuduentsByClass($this->branchId, array_keys($classIds), $status, $timestamp);
        } else {
            //历史学年
            $classes      = $classes_data['class'];
            $childData = $this->getHistoryStudent($timestamp);
        }
        // 处理出勤数据
        $signData     = array();
        $signChildids = array();
        $signDataByChildId = array();
        foreach ($signChilds as $item){
            $signDataByChildId[$item['childid']] = $item;
            $signChildids[]             = $item['childid'];
            $signData[$item['classid']][] = $item;
        }
        foreach ($childData as $k=>$v){
            #已经操作考勤的学生有可能更换班级 需要重新将classid赋值为签到时的班级
            $child_id = $v['id'];
            $class_id = $v['classid'];
            if(!empty($signDataByChildId[$child_id]) && $signDataByChildId[$child_id]['classid'] != $class_id){
                $childData[$k]['classid'] = $signDataByChildId[$child_id]['classid'];
            }
            if(!empty($vacations[$child_id]) && $vacations[$child_id]['classid'] != $class_id){
                $childData[$k]['classid'] = $vacations[$child_id]['classid'];
            }
        }
        $classnum = array();
        $childIdByClass = array();
        foreach ($childData as $child) {
            $classnum[$child['classid']][$child['id']] = $child;
            $childIdByClass[$child['classid']][] = $child['id'];
        }
        $criteria = new CDbCriteria();
        $criteria->compare('schoolid', $this->branchId);
        $criteria->compare('classtype', array('e6', 'e7', 'e8', 'e9', 'e10', 'e11', 'e12'));
        $ivyclass  = IvyClass::model()->findAll($criteria);
        $classList = array();
        if ($ivyclass) {
            foreach ($ivyclass as $val) {
                $classList[] = $val->classid;
            }
        }

        $signChildList = array();
        foreach ($signData as $class_id=>$child_item){
            foreach ($child_item as $item){
                $signChildList[$class_id][] = $item['childid'];
            }
        }
        $classVacations = array();
        $classOnline = array();
        foreach ($vacations as $_cid => $v) {
           if($v['type'] == ChildVacation::VACATION_SICK_LEAVE || $v['type'] == ChildVacation::VACATION_AFFAIR_LEAVE){
                $classVacations[$v['classid']][] = $_cid;
            }else if($v['type'] == ChildVacation::VACATION_ONLINE_PRESENT ){
                $classOnline[$v['classid']][] = $_cid;
            }
        }
        $childAll = array();
        $allChildTotal = 0;
        foreach ($childIdByClass as $classid=>$child_ids){
            $childAll[$classid] = array_unique(array_merge($child_ids , (!empty($signChildList[$classid]) ? $signChildList[$classid] : array()) , (!empty($classVacations[$classid]) ? $classVacations[$classid]:array()) , (!empty($classOnline[$classid]) ? $classOnline[$classid] : array())));
            $allChildTotal += count($childAll[$classid]);
        }
        $this->render('index3', array(
            'classes'        => $classes,
            'signData'       => $signData,
            'signChilds'     => $signChilds,
            'signChildids'   => $signChildids,
            'childData'      => $childData,
            'classnum'       => $classnum,
            'vacations'      => $vacations,
            'classList'      => $classList,
            'classVacations' => $classVacations,
            'allChildTotal'  => $allChildTotal,
            'childAll'       => $childAll,
        ));
    }

    // 手动签到
    public function actionManual()
    {
        // 当前学校的所有班级
        //Yii::msg($this->isTeacher());
        $classes   = $this->getClassList();
        $date      = Yii::app()->request->getParam('date', '');
        $timestamp = strtotime($date);

        // 已经请假了的孩子数据
        $vacations = ChildVacation::model()->getVacations($timestamp, $this->branchId);

        $classIds = array();
        foreach ($classes as $_class) {
            $classIds[$_class->classid] = $_class->classid;
        }
        //所有孩子
        $childData = $this->getStuduentsByClass($this->branchId, $classIds, '<999');

        // 获取已签到的学生
        $classAry = array();
        $classnum = array();
        foreach ($classes as $class) {
            $classAry[] = $class->classid;
            foreach ($childData as $child) {
                if ($class->classid == $child['classid']) {
                    $classnum[$class->classid][] = $child;
                }
            }
        }

        $signChilds = $this->getSignChild($timestamp, $classAry);

        // 处理数据
        $signData           = array();
        $signData_operation = array();
        $signData_operations = array();
        foreach ($signChilds as $sign) {
            $signData[$sign->classid][]                 = $sign->childid;
            $signData_operations[]                      = $sign->uid;
            $signData_operation[$sign->classid]['time'] = date("Y-m-d H:i", $sign->update_timestamp);;
            $signData_operation[$sign->classid]['uid'] = $sign->uid;
        }
        $uid = User::model()->findAllByPk($signData_operations);


        foreach ($signData_operation as $k => $vs) {
            foreach ($uid as $v) {
                if ($vs['uid'] == $v->uid) {
                    $signData_operation[$k]['name'] = $v->getName();
                }
            }
        }

        $this->render('manual', array(
            'classes'       => $classes,
            'signData'      => $signData,
            'signChilds'    => $signChilds,
            'childData'     => $childData,
            'classnum'      => $classnum,
            'operation_uid' => $signData_operation,
            'vacations'     => $vacations,
        ));
    }

    public function actionManualAdd()
    {
        return;
        $childids = Yii::app()->request->getPost('childid', '');
        $classid  = Yii::app()->request->getPost('classid', '');
        $day      = Yii::app()->request->getPost('day', '');
        ChildDailySign::model()->deleteAll('classid=' . $classid, 'day=' . strtotime($day));
        if ($childids && $day) {
            foreach ($childids as $childid) {
                $model                   = new ChildDailySign;
                $model->childid          = $childid;
                $model->classid          = $classid;
                $model->schoolid         = $this->branchId;
                $model->sign_timestamp   = strtotime($day);
                $model->uid              = Yii::app()->user->id;
                $model->update_timestamp = time();
                $model->save();
            }
        }
        $classes  = $this->getClassList();
        $classAry = array();
        foreach ($classes as $class) {
            $classAry[] = $class->classid;
        }

        $signChilds = $this->getSignChild(strtotime($day), $classAry);
        $childlist  = array();
        $childnum   = array();
        foreach ($signChilds as $signChild) {
            if ($signChild['classid'] == $classid) {
                $childlist[]                                    = $signChild;
                $signData_operations[]                          = $signChild->uid;
                $signData_operation['time']                     = date("Y-m-d H:i", $signChild->update_timestamp);
                $signData_operation[$signChild->classid]['uid'] = $signChild->uid;
            }
        }
        $uid = User::model()->findAllByPk($signData_operations);


        foreach ($signData_operation as $k => $vs) {
            foreach ($uid as $v) {
                if ($vs['uid'] == $v->uid) {
                    $signData_operation['name'] = $v->getName();
                }
            }
        }

        $childnum['signChilds']    = count($signChilds);
        $childnum['childlist']     = count($childlist);
        $childnum['operation_uid'] = $signData_operation;
        $this->addMessage('state', "success");
        $this->addMessage('data', $childnum);
        $this->showMessage();
        /*else{
            $this->addMessage('state', "fail");
            $this->showMessage();
        }*/


    }

    public function actionExporyVacation()
    {
        $type         = Yii::app()->request->getParam('type', '');
        $vacation_day = Yii::app()->request->getParam('vacation_day', '');
        $search       = Yii::app()->request->getParam('search', '');

        $criteria = new CDbCriteria;
        if ($type == 1) {
            $criteria->compare('t.type', array(10, 20, 11, 60));
        } else if ($type == 2) {
            $criteria->compare('t.type', array(40, 50));
        } else {
            $criteria->compare('t.type', $type);
        }

        if ($vacation_day) {
            $vacation_time = strtotime($vacation_day);
            $criteria->compare('t.vacation_time_start', "<={$vacation_time}");
            $criteria->compare('t.vacation_time_end', ">={$vacation_time}");
        }
        $criteria->compare('t.school_id', $this->branchId);
        if ($search) {
            $criteria->with = 'childProfile';
            $criteria->addCondition("concat_ws(',',childProfile.name_cn, childProfile.first_name_en, childProfile.middle_name_en, childProfile.last_name_en) like '%{$search}%' ");
        }
        $criteria->compare('t.stat', ChildVacation::STATUS_CHECKED);
        $criteria->order = "vacation_time_start DESC, updata_time DESC";

        $vacationObj      = ChildVacation::model()->findAll($criteria);
        $status           = array(
            ChildVacation::VACATION_ONLINE_PRESENT => "线上出勤",
            ChildVacation::VACATION_SICK_LEAVE     => "病假",
            ChildVacation::VACATION_AFFAIR_LEAVE   => "事假",
            ChildVacation::VACATION_OTHER          => "其他",
            ChildVacation::VACATION_LATE           => "迟到",
            ChildVacation::VACATION_ABSENT         => "旷课"
        );
        $criteria         = new CDbCriteria;
        $criteria->select = "classid , title";
        $criteria->compare('schoolid', $this->branchId);
        $criteria->compare('stat', IvyClass::STATS_OPEN);
        $classList = CHtml::listData(IvyClass::model()->findAll($criteria), 'classid', 'title');

        /*ob_end_clean();
        header("Content-type:application/vnd.ms-excel");
        header("Content-Disposition:filename=" . $this->branchId . "_" . date("Y-m-d" , time()) . "_" .date("l", time())  . "_请假数据.xls");
        echo $this->_t("星期&日期: " . date("Y-m-d" , time()) . "_" .date("l", time()) ). "\t";
        echo "\n";
        echo $this->_t('序号') . "\t";
        echo $this->_t('中文姓名 ') . "\t";
        echo $this->_t('English Name') . "\t";
        echo $this->_t('班级/Grade') . "\t";
        echo $this->_t('状态/sick leave') . "\t";
        echo $this->_t('备注/Absent Reason') . "\t";
        echo "\n";*/
        if (in_array($type, array(1, 10, 20))) {
            $title = $this->branchId . "_" . $vacation_day . "_" . date("l", strtotime($vacation_day)) . "_请假数据.xls";
        } else {
            $title = $this->branchId . "_" . $vacation_day . "_" . date("l", strtotime($vacation_day)) . "_迟到/早退数据.xls";
        }

        $data             = array();
        $data['title']    = $title;
        $data['items'][0] = array("星期&日期: " . $vacation_day . "_" . date("l", strtotime($vacation_day)));
        $data['items'][1] = array('序号', 'ID', '中文姓名', 'English Name', '生日/birthday', '班级/Grade', '状态/sick leave', '备注/Absent Reason');
        if ($vacationObj) {
            $num = 1;
            foreach ($vacationObj as $itemid => $childid) {
                $array                   = array();
                $array[]                 = $num;
                $array[]                 = $childid->child_id;
                $array[]                 = $childid->childProfile->name_cn;
                $array[]                 = $childid->childProfile->first_name_en . " " . $childid->childProfile->middle_name_en . " " . $childid->childProfile->last_name_en;
                $array[]                 = $childid->childProfile->birthday_search;
                $array[]                 = $classList[$childid->class_id];
                $array[]                 = $status[$childid->type];
                $array[]                 = $childid->vacation_reason;
                $data['items'][$num + 1] = $array;
                /*echo $this->_t($num) . "\t";
                echo $this->_t($childid->childProfile->name_cn) . "\t";
                echo $this->_t($childid->childProfile->first_name_en .  " " . $childid->childProfile->middle_name_en . " ".  $childid->childProfile->last_name_en) . "\t";
                echo $this->_t($classList[$childid->class_id]) . "\t";
                echo $this->_t($status[$childid->type]) . "\t";
                echo $this->_t($childid->vacation_reason) . "\t";
                echo "\n";*/
                $num++;
            }
        }

        $this->addMessage('state', 'success');
        $this->addMessage('data', $data);
        $this->showMessage();
    }

    public function _t($str = '')
    {
        $str = "\"" . $str . "\"";
        return iconv("UTF-8", "GBK//IGNORE", $str);
    }

    public function actionVacation()
    {
        $this->branchSelectParams['urlArray'] = array('//mcampus/childsign/vacation');

        $type         = Yii::app()->request->getParam('type', '');
        $vacation_day = Yii::app()->request->getParam('vacation_day', '');
        $search       = Yii::app()->request->getParam('search', '');

        $criteria = new CDbCriteria;
        if ($type) {
            $criteria->compare('t.type', $type);
        } else {
            $criteria->compare('t.type', array(
                ChildVacation::VACATION_SICK_LEAVE,
                ChildVacation::VACATION_AFFAIR_LEAVE,
                ChildVacation::VACATION_ONLINE_PRESENT,
                ChildVacation::VACATION_ABSENT,
            ));
        }
        if ($vacation_day) {
            $vacation_time = strtotime($vacation_day);
            $criteria->compare('t.vacation_time_start', "<={$vacation_time}");
            $criteria->compare('t.vacation_time_end', ">={$vacation_time}");
        }
        $criteria->compare('t.school_id', $this->branchId);
        $criteria->compare('t.stat', 1);
        if ($search) {
            $criteria->with = 'childProfile';
            $criteria->addCondition("concat_ws(',',childProfile.name_cn, childProfile.first_name_en, childProfile.middle_name_en, childProfile.last_name_en) like '%{$search}%' ");
        }

        $dataProvider = new CActiveDataProvider(new ChildVacation, array(
            'criteria'   => $criteria,
            'pagination' => array('pageSize' => 20),
            'sort'       => array(
                'defaultOrder' => array(
                    'vacation_time_start' => CSort::SORT_DESC,
                    'updata_time'         => CSort::SORT_DESC,
                )
            ),
        ));


        $t = strtotime('today');

        //查询今天请假的学生
        $vactionChildren = ChildVacation::model()->getVacations($t, $this->branchId);
        $vactionChildids = array();
        foreach ($vactionChildren as $cid => $vacationChild) {
            if (in_array($vacationChild['type'], array(ChildVacation::VACATION_SICK_LEAVE, ChildVacation::VACATION_AFFAIR_LEAVE)))
                $vactionChildids[$cid] = $cid;
        }

        //查询今天已经签到的学生
        $signChildren = $this->getSignChild();
        $signChildids = array();
        foreach ($signChildren as $signChild) {
            $signChildids[$signChild->childid] = $signChild->childid;
        }

        $allsign = $vactionChildids + $signChildids;

        //查询所有需要签到的学生
        $classIds    = array();
        $allChildids = array();
        $classes     = $this->getClassList();
        foreach ($classes as $_class) {
            $classIds[$_class->classid] = $_class->classid;
        }
        $childData = $this->getStuduentsByClass($this->branchId, $classIds, '<999');
        foreach ($childData as $child) {
            $allChildids[$child['id']] = $child['id'];
        }

        $childParents = array();
        $phoneNumbers = array();
        if ((count($allChildids) - count($allsign)) < 31) {
            $diffChildids = array_diff($allChildids, $allsign);

            if ($diffChildids) {
                $criteria = new CDbCriteria();
                $criteria->compare('childid', $diffChildids);
                $items = ChildProfileBasic::model()->findAll($criteria);
                foreach ($items as $item) {
                    $parentids[$item->fid]        = $item->fid;
                    $parentids[$item->mid]        = $item->mid;
                    $childParents[$item->childid] = array(
                        'name' => $item->getChildName(),
                        'fid'  => $item->fid,
                        'mid'  => $item->mid,
                    );
                }

                if ($parentids) {
                    $parents = IvyParent::model()->findAllByPk($parentids);
                    foreach ($parents as $parent) {
                        if ($parent->mphone)
                            $phoneNumbers[$parent->pid] = $parent->mphone;
                    }
                }
            }
        }

        $criteria         = new CDbCriteria;
        $criteria->select = "classid , title";
        $criteria->compare('schoolid', $this->branchId);
        $criteria->compare('stat', IvyClass::STATS_OPEN);
        $this->class_list = CHtml::listData(IvyClass::model()->findAll($criteria), 'classid', 'title');

        $this->render('vacation', array(
            'dataProvider' => $dataProvider,
            'childParents' => $childParents,
            'phoneNumbers' => $phoneNumbers,
        ));
    }

    public function actionComeLate()
    {
        $this->branchSelectParams['urlArray'] = array('//mcampus/childsign/comeLate');

        $type         = Yii::app()->request->getParam('type', '');
        $vacation_day = Yii::app()->request->getParam('vacation_day', '');
        $search       = Yii::app()->request->getParam('search', '');

        $criteria = new CDbCriteria;
        if ($type) {
            $criteria->compare('t.type', $type);
        } else {
            $criteria->compare('t.type', array(ChildVacation::VACATION_LATE, ChildVacation::VACATION_LEAVE));
        }
        if ($vacation_day) {
            $vacation_time = strtotime($vacation_day);
            $criteria->compare('t.vacation_time_start', "<={$vacation_time}");
            $criteria->compare('t.vacation_time_end', ">={$vacation_time}");
        }
        $criteria->compare('t.school_id', $this->branchId);
        $criteria->compare('t.stat', 1);
        if ($search) {
            $criteria->with = 'childProfile';
            $criteria->addCondition("concat_ws(',',childProfile.name_cn, childProfile.first_name_en, childProfile.middle_name_en, childProfile.last_name_en) like '%{$search}%' ");
        }
        $dataProvider = new CActiveDataProvider(new ChildVacation, array(
            'criteria'   => $criteria,
            'pagination' => array('pageSize' => 20),
            'sort'       => array(
                'defaultOrder' => array(
                    'vacation_time_start' => CSort::SORT_DESC,
                    'updata_time'         => CSort::SORT_DESC,
                )
            ),
        ));


        $t = strtotime('today');

        //查询今天请假的学生
        $vactionChildren = ChildVacation::model()->getVacations($t, $this->branchId);
        $vactionChildids = array();
        foreach ($vactionChildren as $cid => $vacationChild) {
            if (in_array($vacationChild['type'], array(ChildVacation::VACATION_SICK_LEAVE, ChildVacation::VACATION_AFFAIR_LEAVE)))
                $vactionChildids[$cid] = $cid;
        }

        //查询今天已经签到的学生
        $signChildren = $this->getSignChild();
        $signChildids = array();
        foreach ($signChildren as $signChild) {
            $signChildids[$signChild->childid] = $signChild->childid;
        }

        $allsign = $vactionChildids + $signChildids;

        //查询所有需要签到的学生
        $classIds    = array();
        $allChildids = array();
        $classes     = $this->getClassList();
        foreach ($classes as $_class) {
            $classIds[$_class->classid] = $_class->classid;
        }
        $childData = $this->getStuduentsByClass($this->branchId, $classIds, '<999');
        foreach ($childData as $child) {
            $allChildids[$child['id']] = $child['id'];
        }

        $childParents = array();
        $phoneNumbers = array();
        if ((count($allChildids) - count($allsign)) < 31) {
            $diffChildids = array_diff($allChildids, $allsign);

            if ($diffChildids) {
                $criteria = new CDbCriteria();
                $criteria->compare('childid', $diffChildids);
                $items = ChildProfileBasic::model()->findAll($criteria);
                foreach ($items as $item) {
                    $parentids[$item->fid]        = $item->fid;
                    $parentids[$item->mid]        = $item->mid;
                    $childParents[$item->childid] = array(
                        'name' => $item->getChildName(),
                        'fid'  => $item->fid,
                        'mid'  => $item->mid,
                    );
                }

                if ($parentids) {
                    $parents = IvyParent::model()->findAllByPk($parentids);
                    foreach ($parents as $parent) {
                        if ($parent->mphone)
                            $phoneNumbers[$parent->pid] = $parent->mphone;
                    }
                }
            }
        }

        $criteria         = new CDbCriteria;
        $criteria->select = "classid , title";
        $criteria->compare('schoolid', $this->branchId);
        $criteria->compare('stat', IvyClass::STATS_OPEN);
        $this->class_list = CHtml::listData(IvyClass::model()->findAll($criteria), 'classid', 'title');

        $this->render('comeLate', array(
            'dataProvider' => $dataProvider,
            'childParents' => $childParents,
            'phoneNumbers' => $phoneNumbers,
        ));
    }

    //审核列表
    public function actionCheck()
    {
        $this->branchSelectParams['urlArray'] = array('//mcampus/childsign/check');
        $criteria                             = new CDbCriteria;
        $criteria->compare('t.school_id', $this->branchId);
        $criteria->compare('t.stat', ChildVacation::STATUS_UNCHECKED);
        $dataProvider = new CActiveDataProvider(new ChildVacation, array(
            'criteria'   => $criteria,
            'sort'       => array(//'defaultOrder' => 'updated DESC',
            ),
            'pagination' => array(
                'pageSize' => 10,
            ),
        ));

        $this->render('check', array(
            'dataProvider' => $dataProvider,
        ));
    }

    // 审核
    /*public function actionCheckVacation()
    {
        $id = Yii::app()->request->getParam('id', '');

        if (Yii::app()->request->isPostRequest) {
            $model = ChildVacation::model()->findByPk($id);
            $model->stat = ChildVacation::STATUS_CHECKED;
            $model->uid = Yii::app()->user->id;
            $model->updata_time = time();
            if($model->save()){
                $child = ChildProfileBasic::model()->findByPk($model->child_id);

                $view = 'ckeckVacation';
                $main = 'main';
                $sub = '[Ivyonline] ';
                if ($this->branchId == 'BJ_DS' || $this->branchId == 'BJ_SLT') {
                    $main = 'todsparent';
                    $sub = '[DayStar] ';
                }
                $bankinfo = BranchInfo::model()->findByPk($this->branchId);
                $subject = '考勤审核通知';
                $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
                foreach($child->getParents() as $item){
                    $pattern = "/^([0-9A-Za-z\\-_\\.]+)@([0-9a-z]+\\.[a-z]{2,3}(\\.[a-z]{2})?)$/i";
                    if ( preg_match( $pattern, $item->email ) ) {
                        $mailer->AddAddress($item->email);
                    }
                }
                $mailer->AddCC($bankinfo->support_email);
                $mailer->Subject = $sub . $subject ;
                $mailer->getView($view, array('child' => $child, 'model' => $model), $main);
                $mailer->iniMail( OA::isProduction() ); // 此行代码要放到AddAddress, AddCC，getView法下面
                $mailer->Send();


                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message', 'success'));
            }else{
                $err = current($model->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('message', $err ? $err[0] : '失败');
            }
            $this->showMessage();
        }
    }*/

    // 增加时间迟到和早退信息
    /*public function actionUpdateCheck()
    {
        $id = Yii::app()->request->getParam('id', '');
        $time = Yii::app()->request->getParam('time', '');
        $minute = Yii::app()->request->getParam('minute', '');
        $model = ChildVacation::model()->findByPk($id);
        $childModel = ChildProfileBasic::model()->findByPk($model->child_id);
        if (Yii::app()->request->isPostRequest) {
            if(empty($time)){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', "小时不能为空");
                $this->showMessage();
            }
            if(empty($minute)){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', "分钟不能为空");
                $this->showMessage();
            }
            $model->begin_time = strtotime(date("Y-m-d", $model->vacation_time_start) . $time . ":" . $minute);
            $model->stat = 1;
            $model->uid = Yii::app()->user->id;
            $model->updata_time = time();
            if(!$model->save()){
                $err = current($model->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('message', $err ? $err[0] : '失败');
                $this->showMessage();
            }
            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('message', 'success'));
            $this->addMessage('callback', 'cbSuccess');
            $this->showMessage();
        }
        $this->renderPartial('updateCheck', array(
            'model' => $model,
            'childModel' => $childModel,
        ));
    }*/

    // 删除前台请假
    public function actionDeteleCheck()
    {
        $id = Yii::app()->request->getParam('id', '');
        if (Yii::app()->request->isPostRequest) {
            $model    = ChildVacation::model()->findByPk($id);
            $criteria = new CDbCriteria;
            $criteria->compare('childid', $model->child_id);
            $criteria->compare("target_timestamp", ">={$model->vacation_time_start}");
            $criteria->compare("target_timestamp", "<={$model->vacation_time_end}");
            $refundModel = RefundLunch::model()->findAll($criteria);
            $model->uid  = $this->staff->uid;
            if ($model->delete()) {
                if ($refundModel) {
                    foreach ($refundModel as $item) {
                        $resultList['status'] = PolicyApi::cancelLunch($item->childId, $item->classId, $item->schoolId, $item->yid, $item->target_timestamp, "UNDO");
                    }
                }
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message', 'success'));
            } else {
                $err = current($model->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('message', $err ? $err[0] : '失败');
            }
            $this->showMessage();
        }
    }

    public function actionVacationadd()
    {
        $respectively_type_id = Yii::app()->request->getParam('type_id', '');
        $typeId               = Yii::app()->request->getParam('typeId', '');
        $time                 = Yii::app()->request->getParam('time', '');
        $timeEst              = Yii::app()->request->getParam('timeEst', '');
        $minute               = Yii::app()->request->getParam('minute', '');
        $minuteEst            = Yii::app()->request->getParam('minuteEst', '');
//        $sync                 = Yii::app()->request->getParam('sync');
        $arrival = Yii::app()->request->getParam('arrival', 'out');

        $child_id      = ($_POST['ChildVacation']['child_id']) ? $_POST['ChildVacation']['child_id'] : "";
        $child_late    = ($_POST['ChildVacation']['child_id_late']) ? $_POST['ChildVacation']['child_id_late'] : "";
        $child_leave   = ($_POST['ChildVacation']['child_id_leave']) ? $_POST['ChildVacation']['child_id_leave'] : "";
        $child_list_id = ($child_id) ? $child_id : (($child_late) ? $child_late : $child_leave);

        // 判断孩子是否为中学孩子
        $childModel = ChildProfileBasic::model()->findByPk($child_list_id);
        if (isset($childModel) && in_array($childModel->ivyclass->classtype, array('e6', 'e7', 'e8', 'e9', 'e10', 'e11', 'e12'))) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('campus', 'MS attendance are taken by class teachers, data synced to this page automatically. (based on 1st period class)'));
            $this->showMessage();
        }


        $times    = "";
        $timesEst = "";
        if ($child_list_id) {
            $start_time = strtotime($_POST['ChildVacation']['vacation_time_start']);
            $end_time   = strtotime($_POST['ChildVacation']['vacation_time_end']);

            $model = new ChildVacation();
            if ($respectively_type_id == 1) { // 判断是请假还是迟到
                //请假
                if ($end_time < $start_time) {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('campus', 'End Date cannot be earlier than Start Date'));
                    $this->showMessage();
                }
                $criteria = new CDbCriteria;
                $criteria->compare('childid', $child_list_id);
                $criteria->compare('sign_timestamp', ">={$start_time}");
                $criteria->compare('sign_timestamp', "<={$end_time}");
                $child_sign = ChildDailySign::model()->count($criteria);
            } else {
                //迟到
                $start_time = ($_POST['ChildVacation']['vacation_time_start_late']) ? strtotime($_POST['ChildVacation']['vacation_time_start_late']) : strtotime($_POST['ChildVacation']['vacation_time_start_leave']);
                $end_time   = ($end_time) ? $end_time : $start_time;
                if ($timeEst && $minute) {
                    $times = strtotime(date("Y-m-d", $start_time) . " " . $timeEst . "" . $minute);
                }
                if ($timeEst) {
                    if (empty($minuteEst)) {
                        $minuteEst = '00';
                    }
                    $timesEst = strtotime(date("Y-m-d", $start_time) . " " . $timeEst . ":" . $minuteEst);
                }

                if (empty($typeId)) {
                    $criteria = new CDbCriteria;
                    $criteria->compare('childid', $child_list_id);
                    $criteria->compare('sign_timestamp', $start_time);
                    $child_sign = ChildDailySign::model()->count($criteria);
                }
            }

            if ($child_sign) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('campus', 'There are attendance records during the Leave period'));
                $this->showMessage();
            }

            if ($respectively_type_id == 1) {
                $dataTime = array(ChildVacation::VACATION_LATE, ChildVacation::VACATION_LEAVE);
                $criteria = new CDbCriteria;
                $criteria->compare('child_id', $child_list_id);
                $criteria->compare('id', "<>{$child_list_id}");
                $criteria->addNotInCondition('type', $dataTime);
                $criteria->compare('vacation_time_start', "<={$end_time}");
                $criteria->compare('vacation_time_end', ">={$start_time}");
                $criteria->compare('t.stat', ChildVacation::STATUS_CHECKED);
                $child_vacation = ChildVacation::model()->count($criteria);

            }
            /*else{
                $dataTime = array(ChildVacation::VACATION_LATE,ChildVacation::VACATION_LEAVE);
                $criteria = new CDbCriteria();
                $criteria->compare('child_id', $childid);
                $criteria->addNotInCondition('type', $dataTime);

                $criteria = new CDbCriteria;
                $criteria->compare('child_id', $child_list_id);
                $criteria->compare('vacation_time_start', "<={$start_time}");
                $criteria->compare('vacation_time_end', ">={$start_time}");
                $child_vacation = ChildVacation::model()->count($criteria);
            }*/


            if ($child_vacation) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('campus', 'The leave period overlaps with the school holiday'));
                $this->showMessage();
            }

            $child_name = ChildProfileBasic::model()->findByPk($child_list_id);

            if ($child_name->schoolid != $this->branchId) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('campus', 'Selected student(s) is not enrolled in this campus'));
                $this->showMessage();
            }

            $model->child_id            = $child_name->childid;
            $model->school_id           = $child_name->schoolid;
            $model->class_id            = $child_name->classid;
            $model->vacation_time_start = $start_time;
            if ($arrival === 'out') {
                $model->est_begin_time = $timesEst;
            }
            if ($arrival === 'on') {
                $model->begin_time = $timesEst;
            }

            if ($respectively_type_id) {
                $model->type              = $_POST['ChildVacation']['type'];
                $model->vacation_time_end = $end_time;
                $model->vacation_reason   = $_POST['ChildVacation']['vacation_reason'];
            } else {
                $model->type              = (empty($typeId)) ? ChildVacation::VACATION_LATE : ChildVacation::VACATION_LEAVE;
                $model->vacation_time_end = $start_time;
                $model->vacation_reason   = ($_POST['ChildVacation']['vacation_reason_late']) ? $_POST['ChildVacation']['vacation_reason_late'] : $_POST['ChildVacation']['vacation_reason_leave'];
            }
            $model->uid         = Yii::app()->user->id;
            $model->updata_time = time();
            $model->stat        = 1;


            if ($model->save()) {
                // 同步课表
//                if ($sync && in_array($model->type, array(ChildVacation::VACATION_SICK_LEAVE, ChildVacation::VACATION_AFFAIR_LEAVE))) {
//                    Yii::import('common.models.timetable.*');
//                    $tid      = 1;
//                    $criteria = new CDbCriteria;
//                    $criteria->compare('schoolid', $this->branchId);
//                    $criteria->compare('status', 1);
//                    $timaTableModel = Timetable::model()->find($criteria);
//                    if (isset($timaTableModel)) {
//                        $tid = $timaTableModel->id;
//                    }
//                    $schoolid   = $this->branchId;
//                    $targetDate = $model->vacation_time_start;
//                    $type       = TimetableRecords::LEAVE_STATUS;
//                    $childid    = $model->child_id;
//                    $uid        = $this->staff->uid;
                // ****中学学生签到在其他界面操作****
                // 判断请假是上学期还是下学期
                // $calenderModel = CalendarSchool::getCurrentSchoolYearCalendar($schoolid);
                // $yid = $calenderModel->yid;
                // $semesterInfo = CalendarSemester::model()->getSemesterTimeStamp($yid);
                // $semester = 1;
                // if (isset($semesterInfo) && $model->vacation_time_start >= $semesterInfo['spring_start']) {
                //     $semester = 2;
                // }
                // TimetableRecords::updateRecordsAllDay($tid, $semester, $schoolid, $targetDate, $type, $childid, $uid);
//                }

                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message', 'success'));

                if (empty($respectively_type_id) && $arrival == 'on') {
                    $childObj = ChildProfileBasic::model()->findByPk($model->child_id);
                    $tardyTotal = 0;
                    if (($childObj && $childObj->schoolid == $this->branchId)) {
                        if ($signModel = $this->childSign($childObj, $model->vacation_time_start)) {
                            $vacations = ChildVacation::model()->getVacations($model->vacation_time_start, $this->branchId);
                            foreach ($vacations as $_cid => $v) {
                                if ($v['type'] == ChildVacation::VACATION_LATE || $v['type'] == ChildVacation::VACATION_LEAVE) {
                                    $tardyTotal++;
                                }
                            }
                            $data['id']          = $signModel->id;
                            $data['classid']     = $childObj->classid;
                            $data['childid']     = $childObj->childid;
                            $data['name']        = $childObj->getChildName();
                            $data['url']         = $this->createUrl('/child/index/index', array('childid' => $childObj->childid));
                            $data['photo']       = CommonUtils::childPhotoUrl($childObj->photo, 'small');
                            $data['actionStaff'] = $signModel->actionStaff->uname;
                            $data['time']        = date("Y-m-d H:i", $signModel->update_timestamp);
                            $data['type']        = $model->type;
                            $data['lateTotal']   = $tardyTotal;
                        }
                    }

                    $this->addMessage('data', $data);
                    $this->addMessage('callback', 'cbSuccess');
                } else {
                    $this->addMessage('callback', 'cbVacation');
                    $t       = strtotime('today');
                    $inToday = 0;
                    if ($model->vacation_time_start <= $t && $model->vacation_time_end >= $t) {
                        $inToday = 1;
                    }
                    $this->addMessage('data', array(
                        'childid' => $model->child_id,
                        'inToday' => $inToday,
                        'late' => $model->type,
                        'vacation_reason' => $model->vacation_reason
                        ));
                }
            } else {
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err ? $err[0] : '失败');
            }
            $this->showMessage();
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('campus', '请假学生不能为空'));
            $this->showMessage();
        }
    }

    /***
     * 请假审核代码，方法名需要修改
     */
    public function actionVacationaddVacationaddVacationaddVacationadd()
    {
        $id = Yii::app()->request->getParam('id', '');
        if ($id) {
            $child_name = ChildVacation::model()->findByPk($id);
            if ($child_name->stat == 1) {
                $child_name->stat = 0;
            } else {
                $child_name->stat = 1;
            }
            if ($child_name->save()) {
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message', 'success'));
                $this->addMessage('callback', 'cbVacation_detele');
            } else {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', '失败'));
            }
            $this->showMessage();
        }
    }

    public function actionVacation_edit()
    {
        $id                           = Yii::app()->request->getParam('id', '');
        $child_name                   = ChildVacation::model()->findByPk($id);
        $child                        = array();
        $child['vacation_time_start'] = date("Y-m-d", $child_name->vacation_time_start);
        $child['vacation_time_end']   = date("Y-m-d", $child_name->vacation_time_end);
        $child['vacation_reason']     = $child_name->vacation_reason;
        $child['type']                = $child_name->type;
        $child['id']                  = $child_name->id;
        $child['child_id']            = $child_name->child_id;
        $child['name']                = $child_name->childProfile->getChildName();

        echo json_encode($child);
    }

    public function actionVacation_detele()
    {
        $id = Yii::app()->request->getParam('id', '');
        if ($id) {
            $vacation = ChildVacation::model()->findByPk($id);
            if (!$vacation) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', '签到不存在'));
                $this->showMessage();
            }
            $logInfo     = Yii::app()->user->id . '：' . json_encode($vacation->attributes);
            $classid     = $vacation->class_id;
            $accessClass = $this->getClassList();

            $classIds = array();
            foreach ($accessClass as $_class) {
                $classIds[$_class->classid] = $_class->classid;
            }
            if (!in_array($classid, $classIds)) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', '班级权限不够'));
                $this->showMessage();
            }
            $vacation->uid = $this->staff->uid;
            $res           = $vacation->delete();
            if ($res) {
                // 添加日志
                Yii::log($logInfo, 'info', 'vacation_delete');
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message', 'success'));
                $this->addMessage('callback', 'cbVacation_detele');
            } else {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', '失败'));
            }
            $this->showMessage();
        }
    }

    public function actionShowSign()
    {
        $childid  = Yii::app()->request->getParam('signid', '');
        $date     = Yii::app()->request->getParam('date', '');
        $criteria = new CDbCriteria();
        $criteria->compare('child_id', $childid);
        $criteria->compare('type', ChildVacation::VACATION_LATE);
        $criteria->compare('vacation_time_start', strtotime($date));
        $model = ChildVacation::model()->find($criteria);
        $data  = array();
        if ($model) {
            $childModel = ChildProfileBasic::model()->findByPk($model->child_id);
            $childConf  = ChildVacation::typeConf();
            $data       = array('id' => $model->id, 'childid' => $model->child_id, 'type' => $childConf[$model->type], 'childName' => $childModel->getChildName(), 'time' => ($model->est_begin_time) ? date("Y-m-d H:i:s", $model->est_begin_time) : "");
        }
        echo CJSON::encode(array(
            'state' => 'success',
            'data'  => $data,
        ));
    }

    // 签到动作
    public function actionAutoSign()
    {
        $signid      = Yii::app()->request->getParam('signid', '');
        $date        = Yii::app()->request->getParam('date', '');
        $time        = Yii::app()->request->getParam('timeEst', '');
        $minute      = Yii::app()->request->getParam('minuteEst', '');
        $vacation_id = Yii::app()->request->getParam('vacation_id', '');
        $timestamp   = strtotime($date);

        if ($this->limitRole($timestamp)) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '在此时段您没有权限操作');
            $this->showMessage();
        }

        if ($vacation_id) {
            if (empty($time) && empty($minute)) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('campus', '实际迟到小时和分钟不能为空'));
                $this->showMessage();
            }
        }

        $childid = ltrim($signid, 0);


        $childModel = ChildProfileBasic::model()->findByPk($childid);
        if (isset($childModel) && in_array($childModel->ivyclass->classtype, array('e6', 'e7', 'e8', 'e9', 'e10', 'e11', 'e12'))) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('campus', 'MS attendance are taken by class teachers, data synced to this page automatically. (based on 1st period class)'));
            $this->showMessage();
        }

        $state    = 'fail';
        $data     = array();
        $dataTime = array(ChildVacation::VACATION_LATE, ChildVacation::VACATION_LEAVE);
        $criteria = new CDbCriteria();
        $criteria->compare('child_id', $childid);
        $criteria->addNotInCondition('type', $dataTime);
        $criteria->compare('vacation_time_start', "<={$timestamp}");
        $criteria->compare('vacation_time_end', ">={$timestamp}");
        $criteria->compare('stat', ChildVacation::STATUS_CHECKED);
        $count = ChildVacation::model()->count($criteria);

        if (!$count) {
            $childObj = ChildProfileBasic::model()->findByPk($childid);
            if (($childObj && $childObj->schoolid == $this->branchId)) {
                if ($signModel = $this->childSign($childObj, $timestamp)) {
                    if ($vacation_id) {
                        $vacationMdel              = ChildVacation::model()->findByPk($vacation_id);
                        $vacationMdel->begin_time  = strtotime(date("Y-m-d", $vacationMdel->vacation_time_start) . " " . $time . ":" . $minute);
                        $vacationMdel->updata_time = time();
                        $vacationMdel->uid         = Yii::app()->user->id;
                        $vacationMdel->save();
                    }
                    $state               = 'success';
                    $data['id']          = $signModel->id;
                    $data['classid']     = $childObj->classid;
                    $data['childid']     = $childObj->childid;
                    $data['name']        = $childObj->getChildName();
                    $data['url']         = $this->createUrl('/child/index/index', array('childid' => $childObj->childid));
                    $data['photo']       = CommonUtils::childPhotoUrl($childObj->photo, 'small');
                    $data['actionStaff'] = $signModel->actionStaff->uname;
                    $data['time']        = date("Y-m-d H:i", $signModel->update_timestamp);

                }
            }
        } else {
            $this->addMessage('message', Yii::t('campus', 'The leave period overlaps with the school holiday'));
        }
        $this->addMessage('state', $state);
        $this->addMessage('data', $data);
        if ($vacation_id && $data) {
            $this->addMessage('callback', 'cbSuccess');
        }
        $this->showMessage();
    }

    // 删除签到
    public function actionDelSign()
    {
        if (Yii::app()->request->isPostRequest) {
            $date = Yii::app()->request->getParam('date', '');

            $timestamp = strtotime($date);
            if ($this->limitRole($timestamp)) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '在此时段您没有权限操作');
                $this->showMessage();
            }
            $data  = array();
            $state = 'fail';
            $id    = Yii::app()->request->getParam('id', '');
            if ($id) {
                $childDailySign = ChildDailySign::model()->findByPk($id);
                if (empty($childDailySign)) {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '已删除');
                    $this->showMessage();
                }
                $childid = $childDailySign->childid;
                $classid = $childDailySign->classid;
                //删除其他异常考勤
                $criteria = new CDbCriteria;
                $criteria->compare('child_id', $childid);
                $criteria->compare('vacation_time_start', "<={$timestamp}");
                $criteria->compare('vacation_time_end', ">={$timestamp}");
                $criteria->compare('t.stat', 1);
                $vacationModel = ChildVacation::model()->findAll($criteria);
                $tardy = array();
                if ($vacationModel) {
                    foreach ($vacationModel as $val) {
                        if($val->type == 40){
                            $tardy = array('childid' => $val->child_id, 'type' => $val->type);
                        }
                        $val->uid = $this->staff->uid;
                        $val->delete();
                    }
                }
                //着装违规
                $criteria2 = new CDbCriteria;
                $criteria2->compare('child_id', $childid);
                $criteria2->compare('updata_time', "<={$timestamp}");
                $criteria2->compare('updata_time', ">={$timestamp}");
                $violationModel = ChildViolation::model()->findAll($criteria2);
                if ($violationModel) {
                    foreach ($violationModel as $val) {
                        $val->delete();
                    }
                }

                $childModel = ChildProfileBasic::model()->findByPk($childid);
                if (isset($childModel) && in_array($childModel->ivyclass->classtype, array('e6', 'e7', 'e8', 'e9', 'e10', 'e11', 'e12'))) {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('campus', 'MS attendance are taken by class teachers, data synced to this page automatically. (based on 1st period class)'));
                    $this->showMessage();
                }

                $childDailySign->uid = $this->staff->uid;
                if ($childDailySign->delete()) {
                    $data          = $tardy;
                    $criteria = new CDbCriteria;
                    $criteria->compare('vacation_time_start', "<={$timestamp}");
                    $criteria->compare('vacation_time_end', ">={$timestamp}");
                    $criteria->compare('type', ChildVacation::VACATION_LATE);
                    $lateTotal  = ChildVacation::model()->count($criteria);
                    $data['lateTotal'] = $lateTotal;
                    $state         = 'success';
                }

            }
            $this->addMessage('state', $state);
            $this->addMessage('data', $data);
            $this->showMessage();
        }
    }


    // 每月报告
    public function actionReport()
    {
        $this->branchSelectParams['urlArray'] = array('//mcampus/childsign/report');
        $month                                = date("Ym", time());
        $date                                 = Yii::app()->request->getParam('date', "");
        $classid                              = Yii::app()->request->getParam('class', "");
        $dateTime                             = ($date) ? $date : $month;
        $classList                            = $this->getClassList();
        $classAry                             = array();
        foreach ($classList as $class) {
            $classAry[$class->classid] = $class->title;
        }

        $oldTime  = date("Ym", strtotime("-2 year"));
        $timeList = array();
        while ($oldTime <= $month) {
            $timeList[$oldTime] = $oldTime;
            $oldTime            = date('Ym', strtotime($oldTime . "01 +1 month"));
        }

        $attendModel      = array();
        $schoolDay        = array();
        $childModel       = array();
        $schoolDayByClass = array();
        if ($date) {
            // 获取选择月份的校历日期
            $criteria = new CDbCriteria();
            $criteria->compare('months.month', $dateTime);
            $criteria->compare('t.branchid', $this->branchId);
            $criteria->compare('months.yid', $this->calendarYids['currentYid']);
            $criteria->with = "months";
            $calenderModel  = CalendarSchool::model()->find($criteria);

            foreach ($calenderModel->months as $item) {
                foreach (explode(",", $item->schoolday_array) as $dayItem) {
                    $day                        = $item->month . $dayItem;
                    $schoolDay[strtotime($day)] = $dayItem;
                }
            }
            $classIds = ($classid) ? array($classid) : array_keys($classAry);

            if ($date == date("Ym", time())) {
                $this->getReport($classIds, $date);
            } else {
                $this->setOldReport($classIds, $date);
            }

            $criteria = new CDbCriteria();
            $criteria->compare('schoolid', $this->branchId);
            $criteria->compare('classid', $classIds);
            $criteria->compare('month', $date);
            $criteria->index = "classid";
            $attendModel     = AttendStatistics::model()->findAll($criteria);
            //校历以外有签到数据的日期 添加到各个班级
            foreach ($attendModel as $class_id => $item) {
                $schoolDayByClass[$class_id] = $schoolDay;
                $child_statistics            = json_decode($item->data, 1);
                foreach ($child_statistics as $child_id => $child_item) {
                    $child_statistics_days = array_keys($child_item);
                    foreach ($child_statistics_days as $time_stamp) {
                        if (date("Ym", $time_stamp) != $dateTime) {
                            continue;
                        }
                        $schoolDayByClass[$class_id][$time_stamp] = date('d', $time_stamp);
                    }
                }
                asort($schoolDayByClass[$class_id]);
            }
            $criteria = new CDbCriteria();
            $criteria->compare('status', array(
                ChildProfileBasic::STATS_ACTIVE_WAITING,
                ChildProfileBasic::STATS_ACTIVE,
                ChildProfileBasic::STATS_DROPPINGOUT
            ));
            $criteria->compare('classid', $classIds);
            $criteria->compare('schoolid', $this->branchId);
            $criteria->index = "childid";
            $childModel      = ChildProfileBasic::model()->findAll($criteria);
        }

        $this->render('report', array(
            'classAry'         => $classAry,
            'timeList'         => $timeList,
            'date'             => $dateTime,
            'attendModel'      => $attendModel,
            'schoolDay'        => $schoolDay,
            'childModel'       => $childModel,
            'schoolDayByClass' => $schoolDayByClass,
        ));
    }

    // 根据班级,时间来修改报告数据
    public function actionUpdateAttend()
    {
        $id = Yii::app()->request->getParam('id', "");
        if ($id) {
            $attendModel = AttendStatistics::model()->findByPk($id);
            if ($attendModel) {
                $startTime          = strtotime($attendModel->month . "01");
                $entTime            = strtotime($attendModel->month . "01 +1 month") - 1;
                $childDailSignModel = $this->setSignData($attendModel->classid, $startTime, $entTime);
                $childArr           = array();
                if ($childDailSignModel) {
                    foreach ($childDailSignModel as $item) {
                        $childArr[$item->childid][$item->sign_timestamp] = array(
                            "startTime" => $item->sign_timestamp,
                            "endTime"   => $item->sign_timestamp,
                            "type"      => 1,
                        );
                    }
                }
                //请假数据
                $childVacationModel = $this->setLeaveData($attendModel->classid, $startTime, $entTime);
                if ($childVacationModel) {
                    foreach ($childVacationModel as $item) {
                        $type = $item->type;
                        //旷课
                        if ($type == ChildVacation::VACATION_ABSENT) {
                            $type = 70;
                        }
                        if (isset($childArr[$item->child_id][$item->vacation_time_start]) && $type == ChildVacation::VACATION_LATE) {
                            $type = 60;
                        }
                        //线上出勤
                        if ($type == ChildVacation::VACATION_ONLINE_PRESENT) {
                            $type = 1;
                        }
                        $childArr[$item->child_id][$item->vacation_time_start] = array(
                            "startTime" => $item->vacation_time_start,
                            "endTime"   => $item->vacation_time_end,
                            "type"      => $type,
                        );
                    }

                }

                $childData  = $this->setChildList($attendModel->classid);
                $attendData = array();
                if ($childData) {
                    foreach ($childData as $k => $childAr) {
                        foreach ($childAr as $childid) {
                            if ($childArr[$childid]) {
                                $attendData[$childid] = $childArr[$childid];
                            } else {
                                $attendData[$childid] = array();
                            }
                        }
                    }
                }
                $attendModel->data = ($attendData) ? json_encode($attendData) : "";
                if (!$attendModel->save()) {
                    $error = current($attendModel->getErrors());
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('message', $error[0]));
                    $this->showMessage();
                }
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message', '成功!'));
                $this->showMessage();
            } else {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', '参数错误'));
                $this->showMessage();
            }
        }
    }

    // 根据ID 打印报告
    public function actionPrintReport()
    {
        $id = Yii::app()->request->getParam('id', "");
        if ($id) {
            $attendModel = AttendStatistics::model()->findByPk($id);
            if ($attendModel) {
                $criteria = new CDbCriteria();
                $criteria->compare('status', array(
                    ChildProfileBasic::STATS_ACTIVE_WAITING,
                    ChildProfileBasic::STATS_ACTIVE,
                    ChildProfileBasic::STATS_DROPPINGOUT,
                ));
                $criteria->compare('classid', $attendModel->classid);
                $criteria->compare('schoolid', $this->branchId);
                $criteria->index = "childid";
                $childModel      = ChildProfileBasic::model()->findAll($criteria);

                $criteria = new CDbCriteria();
                $criteria->compare('months.month', $attendModel->month);
                $criteria->compare('t.branchid', $this->branchId);
                $criteria->with = "months";
                $calenderModel  = CalendarSchool::model()->find($criteria);
                foreach ($calenderModel->months as $item) {
                    foreach (explode(",", $item->schoolday_array) as $dayItem) {
                        $day                        = $item->month . $dayItem;
                        $schoolDay[strtotime($day)] = $dayItem;
                    }
                }
                //校历以外有签到数据的日期 添加到各个班级
                $child_statistics = json_decode($attendModel->data, 1);
                foreach ($child_statistics as $child_id => $child_item) {
                    $child_statistics_days = array_keys($child_item);
                    foreach ($child_statistics_days as $time_stamp) {
                        $schoolDay[$time_stamp] = date('d', $time_stamp);
                    }
                }
                asort($schoolDay);
                $this->layout  = '//layouts/print';
                $this->printFW = $this->branchObj->getPrintHeader();
                $this->render('print', array(
                    'attendModel' => $attendModel,
                    'childModel'  => $childModel,
                    'schoolDay'   => $schoolDay,
                ));
            }
        }
    }


    // ***************************************** 方法 *****************************************


    // 选择当前月份和班级查询数据 写报告
    public function getReport($class, $dataTime)
    {
        if ($class && $dataTime) {
            $startTime = strtotime($dataTime . "01");
            $entTime   = strtotime($dataTime . "01 +1 month");
            // 签到数据
            $childDailSignModel = $this->setSignData($class, $startTime, $entTime);
            $childArr           = array();
            if ($childDailSignModel) {
                foreach ($childDailSignModel as $item) {
                    $childArr[$item->classid][$item->childid][$item->sign_timestamp] = array(
                        "startTime" => $item->sign_timestamp,
                        "endTime"   => $item->sign_timestamp,
                        "type"      => 1,
                    );
                }
            }
            //请假数据
            $childVacationModel = $this->setLeaveData($class, $startTime, $entTime);
            if ($childVacationModel) {
                foreach ($childVacationModel as $item) {
                    $type = $item->type;
                    //旷课
                    if ($type == ChildVacation::VACATION_ABSENT) {
                        $type = 70;
                    }
                    //迟到已到校
                    if (isset($childArr[$item->class_id][$item->child_id][$item->vacation_time_start]) && $type == ChildVacation::VACATION_LATE) {
                        $type = 60;
                    }
                    //线上出勤
                    if ($type == ChildVacation::VACATION_ONLINE_PRESENT) {
                        $type = 1;
                    }
                    $childArr[$item->class_id][$item->child_id][$item->vacation_time_start] = array(
                        "startTime" => $item->vacation_time_start,
                        "endTime"   => $item->vacation_time_end,
                        "type"      => $type,
                    );
                }
            }
            $childData  = $this->setChildList($class);
            $attendData = array();
            if ($childData) {
                foreach ($childData as $k => $childAr) {
                    foreach ($childAr as $childid) {
                        if ($childArr[$k][$childid]) {
                            $attendData[$k][$childid] = $childArr[$k][$childid];
                        } else {
                            $attendData[$k][$childid] = array();
                        }
                    }
                }
            }

            $criteria = new CDbCriteria();
            $criteria->compare('schoolid', $this->branchId);
            $criteria->compare('classid', $class);
            $criteria->compare('month', $dataTime);
            $criteria->index = "classid";
            $attendModel     = AttendStatistics::model()->findAll($criteria);

            foreach ($class as $item) {
                if (isset($attendModel) && isset($attendModel[$item])) {
                    $attendModel[$item]->data       = ($attendData && $attendData[$item]) ? json_encode($attendData[$item]) : "";
                    $attendModel[$item]->updated    = time();
                    $attendModel[$item]->updated_by = Yii::app()->user->id;
                    $attendModel[$item]->save();
                } else {
                    $model             = new AttendStatistics();
                    $model->schoolid   = $this->branchId;
                    $model->classid    = $item;
                    $model->month      = $dataTime;
                    $model->data       = ($attendData && $attendData[$item]) ? json_encode($attendData[$item]) : "";
                    $model->updated    = time();
                    $model->updated_by = Yii::app()->user->id;
                    $model->save();
                }
            }
            return true;
        } else {
            return false;
        }
    }

    //选择之前的月份查询
    public function setOldReport($class, $dataTime)
    {
        $criteria = new CDbCriteria();
        $criteria->compare('schoolid', $this->branchId);
        $criteria->compare('classid', $class);
        $criteria->compare('month', $dataTime);
        $criteria->index = "classid";
        $attendModel     = AttendStatistics::model()->findAll($criteria);

        $classList = array();
        foreach ($class as $classid) {
            if (empty($attendModel) || empty($attendModel[$classid])) {
                $classList[] = $classid;
            }
        }
        if ($classList) {
            $startTime = strtotime($dataTime . "01");
            $entTime   = strtotime($dataTime . "01 +1 month");

            $childDailSignModel = $this->setSignData($class, $startTime, $entTime);
            $childArr           = array();
            if ($childDailSignModel) {
                foreach ($childDailSignModel as $item) {
                    $childArr[$item->classid][$item->childid][$item->sign_timestamp] = array(
                        "startTime" => $item->sign_timestamp,
                        "endTime"   => $item->sign_timestamp,
                        "type"      => 1,
                    );
                }
            }
            //请假数据
            $childVacationModel = $this->setLeaveData($class, $startTime, $entTime);
            if ($childVacationModel) {
                foreach ($childVacationModel as $item) {
                    $type = $item->type;
                    //旷课
                    if ($type == ChildVacation::VACATION_ABSENT) {
                        $type = 70;
                    }
                    if (isset($childArr[$item->class_id][$item->child_id][$item->vacation_time_start])) {
                        $type = 60;
                    }
                    //线上出勤
                    if ($type == ChildVacation::VACATION_ONLINE_PRESENT) {
                        $type = 1;
                    }
                    $childArr[$item->class_id][$item->child_id][$item->vacation_time_start] = array(
                        "startTime" => $item->vacation_time_start,
                        "endTime"   => $item->vacation_time_end,
                        "type"      => $type,
                    );
                }
            }

            $childData  = $this->setChildList($class);
            $attendData = array();
            if ($childData) {
                foreach ($childData as $k => $childAr) {
                    foreach ($childAr as $childid) {
                        if ($childArr[$k][$childid]) {
                            $attendData[$k][$childid] = $childArr[$k][$childid];
                        } else {
                            $attendData[$k][$childid] = array();
                        }
                    }
                }
            }

            foreach ($classList as $classId) {
                $model             = new AttendStatistics();
                $model->schoolid   = $this->branchId;
                $model->classid    = $classId;
                $model->month      = $dataTime;
                $model->data       = ($attendData && $attendData[$classId]) ? json_encode($attendData[$classId]) : "";
                $model->updated    = time();
                $model->updated_by = Yii::app()->user->id;
                $model->save();
            }
        }
        return true;
    }

    // 获取签到数据
    public function setSignData($class, $startTime, $entTime)
    {
        $criteria = new CDbCriteria();
        $criteria->compare('schoolid', $this->branchId);
        $criteria->compare('classid', $class);
        $criteria->compare('sign_timestamp', ">={$startTime}");
        $criteria->compare('sign_timestamp', "<={$entTime}");
        $childDailSignModel = ChildDailySign::model()->findAll($criteria);
        return $childDailSignModel;
    }

    // 获取请假数据
    public function setLeaveData($class, $startTime, $entTime)
    {
        $criteria = new CDbCriteria();
        $criteria->compare('school_id', $this->branchId);
        $criteria->compare('class_id', $class);
        $criteria->compare('stat', ChildVacation::STATUS_CHECKED);
        $criteria->compare('type', array(10, 20, 40, 11, 60));
        $criteria->addCondition('
        (t.vacation_time_start >= ' . $startTime . ' and t.vacation_time_end <=' . $entTime . ') or 
        (t.vacation_time_start <' . $startTime . ' and t.vacation_time_end >=' . $startTime . ') or 
        (t.vacation_time_start <=' . $entTime . ' and t.vacation_time_end >' . $entTime . ')');
        $childVacationModel = ChildVacation::model()->findAll($criteria);

        /*$criteria = new CDbCriteria();
        $criteria->compare('school_id', $this->branchId);
        $criteria->compare('class_id', $class);
        $criteria->compare('type', array(10,20,40));
        $criteria->compare('vacation_time_start' , ">={$startTime}");
        $criteria->compare('vacation_time_end' , "<={$entTime}");
        $childVacationModel1 = ChildVacation::model()->findAll($criteria);

        $criteria = new CDbCriteria();
        $criteria->compare('school_id', $this->branchId);
        $criteria->compare('class_id', $class);
        $criteria->compare('type', array(10,20,40));
        $criteria->compare('vacation_time_start' , "<{$startTime}");
        $criteria->compare('vacation_time_end' , ">={$startTime}");
        $childVacationModel2 = ChildVacation::model()->findAll($criteria);

        $criteria = new CDbCriteria();
        $criteria->compare('school_id', $this->branchId);
        $criteria->compare('class_id', $class);
        $criteria->compare('type', array(10,20,40));
        $criteria->compare('vacation_time_start' , ">{$startTime}");
        $criteria->compare('vacation_time_end' , ">{$entTime}");
        $childVacationModel3 = ChildVacation::model()->findAll($criteria);
        $childVacationModel = $childVacationModel1 + $childVacationModel2 + $childVacationModel3;*/
        return $childVacationModel;
    }

    public function setChildList($class)
    {
        $childArr = array();
        if ($class) {
            $criteria = new CDbCriteria();
            $criteria->compare('schoolid', $this->branchId);
            $criteria->compare('classid', $class);
            $criteria->compare('status', array(
                ChildProfileBasic::STATS_ACTIVE_WAITING,
                ChildProfileBasic::STATS_ACTIVE,
                ChildProfileBasic::STATS_DROPPINGOUT,
            ));
            $childModel = ChildProfileBasic::model()->findAll($criteria);

            foreach ($childModel as $child) {
                $childArr[$child->classid][] = $child->childid;
            }
        }
        return $childArr;
    }

    // 获取当前学校所有开放的班级
    public function getClassList()
    {
        $_yids = $this->getCalendars();
        if ($this->isTeacher()) {
            Yii::import('common.models.classTeacher.*');
            $classIds = CHtml::listData(ClassTeacher::model()->findAllByAttributes(
                array('yid' => $_yids['currentYid'], 'teacherid' => Yii::app()->user->id, 'schoolid' => $this->staff->profile->branch)
            ), 'classid', 'classid');
            if ($classIds) {
                $_classes = IvyClass::model()->findAllByPk($classIds);
            }
        } else {
            $crit = new CDbCriteria;
            $crit->compare('t.schoolid', $this->branchId);
            $crit->compare('t.stat', IvyClass::STATS_OPEN);
            $crit->compare('t.yid', $_yids['currentYid']);
            $crit->order = 't.child_age ASC, t.title ASC';
            $_classes    = IvyClass::model()->findAll($crit);
        }
        return $_classes;
    }


    // 获取选中时间后 学校所有开放的班级
    public function getClassList2($timestamp)
    {
        //学校所有学年
        $crit = new CDbCriteria;
        $crit->compare('branchid', $this->branchId);
        $all_school_calendar = CalendarSchool::model()->findAll($crit);
        $yids                = array();
        foreach ($all_school_calendar as $item) {
            $yids[] = $item->yid;
        }
        $crit = new CDbCriteria;
        $crit->compare('yid', $yids);
        $calendar_list = Calendar::model()->findAll($crit);
        $select_yid    = '';
        //选中的学年
        foreach ($calendar_list as $item) {
            $timepoints = explode(',', $item->timepoints);
            if ($timestamp >= $timepoints[0] && $timestamp <= $timepoints[3]) {
                $select_yid = $item->yid;
            }
        }
        $_yids      = $this->getCalendars();
        $currentYid = $_yids['currentYid'];
        if (empty($select_yid)) {
            return false;
        }
        $_classes = array();
        if ($this->isTeacher()) {
            Yii::import('common.models.classTeacher.*');
            $classIds = CHtml::listData(ClassTeacher::model()->findAllByAttributes(
                array('yid' => $select_yid, 'teacherid' => Yii::app()->user->id, 'schoolid' => $this->staff->profile->branch)
            ), 'classid', 'classid');
            if ($classIds) {
                $_classes = IvyClass::model()->findAllByPk($classIds);
            }
        } else {
            if ($currentYid == $select_yid) {
                $stat = IvyClass::STATS_OPEN;
            } else {
                $stat = IvyClass::STATS_CLOSED;
            }
            $crit = new CDbCriteria;
            $crit->compare('t.schoolid', $this->branchId);
            $crit->compare('t.stat', $stat);
            $crit->compare('t.yid', $select_yid);
            $crit->order = 't.child_age ASC, t.title ASC';
            $_classes    = IvyClass::model()->findAll($crit);
        }
        return array('select_yid' => $select_yid, 'currentYid' => $currentYid, 'class' => $_classes);
    }


    /**
     * [获取已经签到的学生]
     * @param  integer $timestamp [日期零点时间戳]
     * @param  integer $classid   [班级id，不指定则获取所有]
     * @return [type]             [description]
     */
    public function getSignChild($timestamp = 0, $classid = 0)
    {
        if (!$timestamp) {
            $timestamp = strtotime('today');
        }

        $crit = new CDbCriteria;
        $crit->compare('schoolid', $this->branchId);
        $crit->compare('sign_timestamp', $timestamp);
        if ($classid) {
            $crit->compare('classid', $classid);
        }

        return ChildDailySign::model()->findAll($crit);
    }

    /**
     * [孩子签到操作]
     * @param  [type]  $childObj  [孩子对象]
     * @param  integer $timestamp [指定日期]
     * @return [type]             [description]
     */
    public function childSign($childObj, $timestamp = 0)
    {

        if (!$timestamp) {
            $timestamp = strtotime('today');
        }

        $crit = new CDbCriteria;
        $crit->compare('childid', $childObj->childid);
        $crit->compare('sign_timestamp', $timestamp);
        if (ChildDailySign::model()->exists($crit)) {
            // $model = ChildDailySign::model()->find($crit);
            return false;
        } else {
            $model = new ChildDailySign;
        }
        $model->childid          = $childObj->childid;
        $model->classid          = $childObj->classid;
        $model->schoolid         = $childObj->schoolid;
        $model->sign_timestamp   = $timestamp;
        $model->uid              = Yii::app()->user->id;
        $model->update_timestamp = time();

        if ($model->save()) {
            return $model;
        }

        return false;
    }

    public function getStuduentsByClass($branchId, $classIds, $status = '<100', $timestamp = '')
    {
        if (!$timestamp) {
            $timestamp = strtotime('today');
        }
        $crit = new CDbCriteria();
        $crit->compare('schoolid', $branchId);
        $crit->compare('classid', $classIds);
        $crit->compare('status', $status);
        $crit->select = 'childid,birthday_search,classid,status,gender,nick,name_cn,first_name_en,middle_name_en,last_name_en,birthday,country,photo';
        $crit->order  = 'birthday ASC';
        $childrenObjs = ChildProfileBasic::model()->findAll($crit);
        foreach ($childrenObjs as $child) {
            // 过滤掉毕业的学生
            if ($child->status == ChildProfileBasic::STATS_GRADUATED) {
                continue;
            }
            if ($child->enter_date != 0) {
                if ($child->enter_date > $timestamp) {
                    continue;
                }
            } else {
                if ($child->est_enter_date > $timestamp) {
                    continue;
                }
            }
            $childData[] = array(
                'id'      => $child->childid,
                'birth'   => $child->birthday_search,
                'name'    => CHtml::encode($child->getChildName()),
                'classid' => intval($child->classid),
                'status'  => $child->status,
                'gender'  => $child->gender,
                'nick'    => CHtml::encode($child->nick),
                'age'     => CommonUtils::getAge($child->birthday),
                'country' => $child->country,
                'photo'   => $child->photo,
            );
        }
        return $childData;
    }

    //历史学年下签到的孩子data
    public function getHistoryStudent($timestamp)
    {
        //历史学年
        $crit         = new CDbCriteria;
        $crit->select = 'childid,classid';
        $crit->compare('schoolid', $this->branchId);
        $crit->compare('sign_timestamp', $timestamp);
        $histories      = ChildDailySign::model()->findAll($crit);
        $child_class_id = CHtml::listData($histories, 'childid', 'classid');
        //孩子的id
        $child_ids1       = array_keys($child_class_id);
        $criteria         = new CDbCriteria;
        $criteria->select = 'child_id,class_id';
        $criteria->compare('school_id', $this->branchId);
        $criteria->compare('vacation_time_start', '<=' . $timestamp);
        $criteria->compare('vacation_time_end', '>=' . $timestamp);
        $criteria->compare('stat', ChildVacation::STATUS_CHECKED);
        $vacation_histories      = ChildVacation::model()->findAll($criteria);
        $vacation_child_class_id = CHtml::listData($vacation_histories, 'child_id', 'class_id');
        $all_child_class_id      = $child_class_id + $vacation_child_class_id;
        $child_ids2              = array_keys($vacation_child_class_id);

        $child_ids = array_merge($child_ids1, $child_ids2);//历史数据中说有孩子的id
        $childData = $this->getStuduentsByChildIds($child_ids);//孩子的当前信息
        foreach ($childData as $k => $item) {
            $childData[$k]['classid'] = $all_child_class_id[$item['id']];//赋值历史学年的classid
        }

        return $childData;
    }

    public function getStuduentsByChildIds($childIds)
    {
        $crit = new CDbCriteria();
        $crit->compare('childid', $childIds);
        $crit->order  = 'birthday ASC';
        $crit->select = 'childid,birthday_search,classid,status,gender,nick,name_cn,first_name_en,middle_name_en,last_name_en,birthday,country,photo';
        $childrenObjs = ChildProfileBasic::model()->findAll($crit);
        foreach ($childrenObjs as $child) {
            $childData[] = array(
                'id'      => $child->childid,
                'birth'   => $child->birthday_search,
                'name'    => CHtml::encode($child->getChildName()),
                'classid' => intval($child->classid),
                'status'  => $child->status,
                'gender'  => $child->gender,
                'nick'    => CHtml::encode($child->nick),
                'age'     => CommonUtils::getAge($child->birthday),
                'country' => $child->country,
                'photo'   => $child->photo,
            );
        }
        return $childData;
    }

    public function actionSaveVacation()
    {
        $id    = Yii::app()->request->getParam('id', "");
        $model = ChildVacation::model()->findByPk($id);
        if (Yii::app()->request->isPostRequest) {
            $model->attributes  = $_POST['ChildVacation'];
            $model->updata_time = time();
            $model->uid         = Yii::app()->user->id;
            if ($model->save()) {
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message', 'success'));
                $this->addMessage('callback', 'cbSaveVaceion');
            } else {
                $this->addMessage('state', 'fail');
                $err = current($model->getErrors());
                $this->addMessage('message', $err ? $err[0] : '失败');
            }
            $this->showMessage();
        }
        $childModel = ChildProfileBasic::model()->findByPk($model->child_id);
        $classModel = IvyClass::model()->findByPk($model->class_id);
        $this->renderpartial('saveVacation', array(
            'model'      => $model,
            'childModel' => $childModel,
            'classModel' => $classModel,
        ));

    }

    public function isTeacher()
    {
        if (in_array('ivystaff_opschool', Yii::app()->user->roles)) {
            return false;
        } elseif (in_array('ivystaff_teacher', Yii::app()->user->roles)) {
            return true;
        } else {
            return false;
        }
    }

    public function status($data)
    {
        switch ($data->type) {
            case ChildVacation::VACATION_SICK_LEAVE:
                echo Yii::t('campus', 'Sick Leave');
                break;
            case ChildVacation::VACATION_AFFAIR_LEAVE:
                echo Yii::t('campus', 'Personal Leave');
                break;
            case ChildVacation::VACATION_LATE:
                echo Yii::t('campus', 'Tardy');
                break;
            case ChildVacation::VACATION_LEAVE:
                echo Yii::t('campus', 'early leaving');
                break;
            case ChildVacation::VACATION_ONLINE_PRESENT:
                echo Yii::t('attends', 'Online Present');
                break;
            case ChildVacation::VACATION_ABSENT:
                echo Yii::t('attends', 'Absent');
                break;
            default:
                echo Yii::t('payment', 'Other');
                break;
        }
    }

    public function getButton($data)
    {
        if (in_array($data->type, array(ChildVacation::VACATION_SICK_LEAVE, ChildVacation::VACATION_AFFAIR_LEAVE))) {
            echo '<a class="J_modal btn btn-info btn-xs" href="' . $this->createUrl("saveVacation", array("id" => $data->id)) . '" ><span class="glyphicon glyphicon-pencil"></span></a> ';
        }
        echo '<a class="J_ajax_del btn btn-danger btn-xs" href="' . $this->createUrl("vacation_detele", array("id" => $data->id, 'type' => 'detele')) . '"><span class="glyphicon glyphicon-remove"></span></a> ';
        if ($data->type == ChildVacation::VACATION_LATE)
            echo '<a class="btn btn-info btn-xs" href="javascript:void(0);" data-backdrop="static" onclick="vacationEdit(' . $data->id . ')"><span class="glyphicon glyphicon-plus"></span></a> ';
    }

    public function getCheckButton($data)
    {
        echo '<a title="审核" class="btn btn-warning btn-xs" href="javascript:;" onclick="check(' . $data->id . ',1)"><span class="glyphicon glyphicon-unchecked"></span></a>' . ' ';
        echo '<a class="J_ajax_del btn btn-danger btn-xs" href="' . $this->createUrl("deteleCheck", array("id" => $data->id, 'type' => 'detele')) . '"><span class="glyphicon glyphicon-remove"></span></a> ';
    }


    public function actionSignAll()
    {
        if (Yii::app()->request->isAjaxRequest) {
            $childData = Yii::app()->request->getParam('childData', array());
            $date      = Yii::app()->request->getParam('date', '');

            if ($childData && $date) {
                $data = array();

                $timestamp = strtotime($date);
                if ($this->limitRole($timestamp)) {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '您在此时段没有权限操作');
                    $this->showMessage();
                }
                $crit = new CDbCriteria();
                $crit->compare('childid', $childData);
                $crit->index = 'childid';
                $childModel  = ChildProfileBasic::model()->findAll($crit);

                $crit = new CDbCriteria();
                $crit->compare('classtype', array('e6', 'e7', 'e8', 'e9', 'e10', 'e11', 'e12'));
                $classModel = IvyClass::model()->findAll($crit);
                $classList  = array();
                foreach ($classModel as $val) {
                    $classList[] = $val->classid;
                }

                foreach ($childData as $childid) {
                    if (isset($childModel) && !in_array($childModel[$childid]->classid, $classList)) {
                        $criteria = new CDbCriteria();
                        $criteria->compare('child_id', $childid);
                        //$criteria->compare('type', '<>'.ChildVacation::VACATION_LATE);
                        $criteria->compare('vacation_time_start', "<={$timestamp}");
                        $criteria->compare('vacation_time_end', ">={$timestamp}");
                        $criteria->compare('stat', ChildVacation::STATUS_CHECKED);
                        $count = ChildVacation::model()->count($criteria);
                        if (!$count) {
                            $childObj = ChildProfileBasic::model()->findByPk($childid);
                            if (($childObj && $childObj->schoolid == $this->branchId)) {
                                if ($signModel = $this->childSign($childObj, $timestamp)) {
                                    $data['items'][$childObj->childid] = array(
                                        'id'          => $signModel->id,
                                        'classid'     => $childObj->classid,
                                        'childid'     => $childObj->childid,
                                        'name'        => $childObj->getChildName(),
                                        'url'         => $this->createUrl('/child/index/index', array('childid' => $childObj->childid)),
                                        'photo'       => CommonUtils::childPhotoUrl($childObj->photo, 'small'),
                                        'actionStaff' => $signModel->actionStaff->uname,
                                        'time'        => date("Y-m-d H:i", $signModel->update_timestamp),
                                    );
                                }
                            }
                        }
                    }
                }

                $this->addMessage('state', 'success');
                $this->addMessage('data', $data);
                $this->showMessage();
            }
        }
    }

    // 获取不可以操作的用户角色
    public function limitRole($currentTime = 0)
    {
        //历史学年的也不能操作
        //学校所有学年
        $crit = new CDbCriteria;
        $crit->compare('branchid', $this->branchId);
        $all_school_calendar = CalendarSchool::model()->findAll($crit);
        $yids                = array();
        foreach ($all_school_calendar as $item) {
            $yids[] = $item->yid;
        }
        $crit = new CDbCriteria;
        $crit->compare('yid', $yids);
        $calendar_list = Calendar::model()->findAll($crit);
        $select_yid    = '';
        //选中的学年
        foreach ($calendar_list as $item) {
            $timepoints = explode(',', $item->timepoints);
            if ($currentTime >= $timepoints[0] && $currentTime <= $timepoints[3]) {
                $select_yid = $item->yid;
            }
        }
        $_yids      = $this->getCalendars();
        $currentYid = $_yids['currentYid'];
        //历史学年不能操作
        if ($currentYid != $select_yid) {
            return true;
        }
        //六日不上课不能操作
        $crit = new CDbCriteria;
        $crit->compare('yid', $select_yid);
        $crit->compare('month', date('Ym', $currentTime));
        $calendar_schooldays = CalendarSchoolDays::model()->find($crit);
        if ($calendar_schooldays) {
            $schoolday      = explode(',', $calendar_schooldays->schoolday_array);
            $half_schoolday = explode(',', $calendar_schooldays->half_schoolday_array);
            $all_schoolday  = array_merge($schoolday, $half_schoolday);
            if (!in_array(date('d', $currentTime), $all_schoolday)) {
//                return true;
            }
        } else {
//            return true;
        }

        $rulerList = ChildDailySign::getRulersByTime();
        if (isset($rulerList[$this->branchId])) {
            if ($currentTime == 0) {
                $currentTime = time();
            }
            $ruler = array();
            foreach ($rulerList[$this->branchId] as $k => $v) {
                $timestamp = strtotime($k);
                if ($currentTime < strtotime('today') || $timestamp < time()) {
                    $ruler = array_merge($ruler, $v);
                }
            }
            $rules = Yii::app()->user->roles;
            $ruler = array_unique($ruler);
            $flag  = false;
            foreach ($rules as $rule) {
                if (in_array($rule, $ruler)) {
                    $flag = true;
                    break;
                }
            }
            return $flag;
        }
        return false;
    }

    // 病假追踪页面
    public function actionTrack()
    {
        $this->branchSelectParams['urlArray'] = array('//mcampus/childsign/track');
        $cs                                   = Yii::app()->clientScript;
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/vue.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/datatables/jquery.dataTables.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/datatables/dataTables.bootstrap.min.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/datatables/dataTables.bootstrap.min.css');
        $this->render('track', array());
    }

    public function actionTrackData()
    {
        $vacation_day = Yii::app()->request->getParam('vacation_day', '2020-01-09');
        $search       = Yii::app()->request->getParam('search', '');
        $start_day = Yii::app()->request->getParam('start_day');
        $end_day = Yii::app()->request->getParam('end_day');
        $is_export = Yii::app()->request->getParam('is_export');
        $criteria      = new CDbCriteria;
        $criteria->compare('t.type', ChildVacation::VACATION_SICK_LEAVE);
        $criteria->compare('t.school_id', $this->branchId);
        $criteria->compare('t.stat', 1);
        if($start_day && $end_day){
            $vacation_time_start = strtotime($start_day);
            $vacation_time_end = strtotime($end_day);
            if($vacation_time_start > $vacation_time_end){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '请正确选择时间段');
                $this->showMessage();
            }
            $start_month = date('Ym',$vacation_time_start);
            $end_month = date('Ym',$vacation_time_end);
            //查询两个时间是否超出了一个学期
            $criteria1 = new CDbCriteria();
            $criteria1->compare('t.branchid', $this->branchId);
            $criteria1->compare('months.month', ">=".$start_month);
            $criteria1->compare('months.month', "<=".$end_month);
            $criteria1->with = "months";
            $calenderModel  = CalendarSchool::model()->findAll($criteria1);
            if(count($calenderModel)>1){
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '不可跨学年');
                $this->showMessage();
            }
            $criteria->addCondition("(t.vacation_time_start >= {$vacation_time_start} AND t.vacation_time_start <= {$vacation_time_end}) OR (t.vacation_time_end >= {$vacation_time_start} AND t.vacation_time_end <= {$vacation_time_end}) ");
        }else{
            $vacation_time = strtotime($vacation_day) ? strtotime($vacation_day) : time();
            $criteria->compare('t.vacation_time_start', "<={$vacation_time}");
            $criteria->compare('t.vacation_time_end', ">={$vacation_time}");
        }
        if ($search) {
            $criteria->with = 'childProfile,tracks,ivyclass';
            $criteria->addCondition("concat_ws(',',childProfile.name_cn, childProfile.first_name_en, childProfile.middle_name_en, childProfile.last_name_en) like '%{$search}%' ");
        }
        $vacationModels = ChildVacation::model()->findAll($criteria);
        $data           = array();
        $userIds        = array();
        $child_ids = array();
        $vids = array();
        $vacation_class_ids = array();
        if($vacationModels){
            foreach ($vacationModels as $item){
                $child_ids[$item->child_id] = $item->child_id;
                $vids[] = $item->id;
                $vacation_class_ids[$item->class_id] = $item->class_id;
            }
            $child_ids = array_keys($child_ids);
            //取出孩子的姓名和家长的id
            $childList = ChildProfileBasic::model()->findAllByPk($child_ids, array('index'=>'childid'));
            $child_info = array();
            foreach ($childList as $item){
                $child_info[$item->childid] = array(
                    'child_name'=>$item->getChildName(),
                    'fid'=>$item->fid,
                    'mid'=>$item->mid,
                );
            }
            //取出跟踪记录
            $criteria = new CDbCriteria();
            $criteria->compare('vid',$vids);
            $ChildVacationTrack = ChildVacationTrack::model()->findAll($criteria);
            $ChildVacationTrackByVid = array();
            foreach ($ChildVacationTrack as $item){
                $ChildVacationTrackByVid[$item->vid][] = $item;
            }
            //取出班级信息
            $class_info = IvyClass::model()->findAllByPk($vacation_class_ids,array('index'=>'classid'));
            foreach ($vacationModels as $vacationModel) {
                $trackData    = array();
                $teacherTrack = 0;
                $healthTrack  = 0;
                foreach ($ChildVacationTrackByVid[$vacationModel->id] as $track){
                    $userIds[]   = $track->track_by;
                    $trackData[] = array(
                        'track_user' => $track->track_by,
                        'track_memo' => nl2br($track->track_memo),
                        'track_at'   => date('Y-m-d H:i:s', $track->track_at),
                    );
                    if ($track->track_type == 1) {
                        $teacherTrack = 1;
                    }
                    if ($track->track_type == 2) {
                        $healthTrack = 1;
                    }
                }
                $data[]    = array(
                    'vid'                 => $vacationModel->id,
                    'childname'           => $child_info[$vacationModel->child_id]['child_name'],
                    'classname'           => $class_info[$vacationModel->class_id]['title'],
                    'fid'                 => $child_info[$vacationModel->child_id]['fid'],
                    'mid'                 => $child_info[$vacationModel->child_id]['mid'],
                    'vacation_time_start' => date('Y-m-d', $vacationModel->vacation_time_start),
                    'vacation_time_end'   => date('Y-m-d', $vacationModel->vacation_time_end),
                    'vacation_reason'     => $vacationModel->vacation_reason,
                    'teacherTrack'        => $teacherTrack,
                    'healthTrack'         => $healthTrack,
                    'track_data'          => $trackData,
                );
                $userIds[] = $child_info[$vacationModel->child_id]['fid'];
                $userIds[] = $child_info[$vacationModel->child_id]['mid'];
            }
            $userModels = User::model()->findAllByPk($userIds);
            $userData   = array();
            foreach ($userModels as $k => $userModel) {
                $userData[$userModel->uid]['name']  = $userModel->getName();
                //导出数据不需要家长信息
                if(empty($is_export)) {
                    $userData[$userModel->uid]['email'] = $userModel->email;
                    if (isset($userModel->parent)) {
                        $userData[$userModel->uid]['mphone'] = $userModel->parent->mphone;
                    }
                }
                unset($userModels[$k]);
            }
            foreach ($data as $k => $item) {
                $fid     = $item['fid'];
                $mid     = $item['mid'];
                $fname   = '';
                $femail  = '';
                $fmphone = '';
                $mname   = '';
                $memail  = '';
                $mmphone = '';
                if (isset($userData[$fid])) {
                    $fname   = $userData[$fid]['name'];
                    $femail  = $userData[$fid]['email'];
                    $fmphone = $userData[$fid]['mphone'];
                }
                if (isset($userData[$mid])) {
                    $mname   = $userData[$mid]['name'];
                    $memail  = $userData[$mid]['email'];
                    $mmphone = $userData[$mid]['mphone'];
                }
                $data[$k]['fname']   = $fname;
                $data[$k]['femail']  = $femail;
                $data[$k]['fmphone'] = $fmphone;
                $data[$k]['mname']   = $mname;
                $data[$k]['memail']  = $memail;
                $data[$k]['mmphone'] = $mmphone;
                foreach ($item['track_data'] as $k2 => $item2) {
                    if (isset($userData[$item2['track_user']])) {
                        $data[$k]['track_data'][$k2]['track_user'] = $userData[$item2['track_user']]['name'];
                    }
                }
            }
        }
        $this->addMessage('state', 'success');
        $this->addMessage('data', $data);
        $this->showMessage();
    }

    // 新增跟踪记录
    public function actionAddTrack()
    {
        $vid        = Yii::app()->request->getParam('vid');
        $memo       = Yii::app()->request->getParam('memo');
        $changeType = Yii::app()->request->getParam('change_type');

        $this->addMessage('state', 'fail');
        if (!$vid || !$memo) {
            $this->addMessage('message', '参数错误');
            $this->showMessage();
        }
        $type = ChildVacation::VACATION_SICK_LEAVE;
        if ($changeType) {
            $type = ChildVacation::VACATION_AFFAIR_LEAVE;
        }
        $vacation = ChildVacation::model()->findByPk($vid);
        if (!$vacation) {
            $this->addMessage('message', '参数错误');
            $this->showMessage();
        }
        $oldInfo        = json_encode($vacation->attributes);
        $vacation->type = $type;
        if (!$vacation->save()) {
            $this->addMessage('message', '请假表保存失败');
            $this->showMessage();
        }

        $track_type = 1;
        if (Yii::app()->user->checkAccess('ivystaff_doctor')) {
            $track_type = 2;
        }
        $trackModel             = new ChildVacationTrack();
        $trackModel->vid        = $vid;
        $trackModel->school_id  = $vacation->school_id;
        $trackModel->class_id   = $vacation->class_id;
        $trackModel->child_id   = $vacation->child_id;
        $trackModel->old_info   = $oldInfo;
        $trackModel->track_memo = $memo;
        $trackModel->track_type = $track_type;
        $trackModel->track_by   = $this->staff->uid;
        $trackModel->track_at   = time();
        if (!$trackModel->save()) {
            $this->addMessage('message', '追踪记录表保存失败');
            $this->showMessage();
        }

        $this->addMessage('state', 'success');
        $this->addMessage('message', '保存成功');
        $this->showMessage();

    }

    // 操作记录
    public function actionSignlog()
    {
        $this->branchSelectParams['urlArray'] = array('//mcampus/childsign/report');
        $date                                 = Yii::app()->request->getParam('date', "");
        $classid                              = Yii::app()->request->getParam('class', "");
        $dateTime                             = ($date) ? $date : date("Ymd", time());;
        $classList = $this->getClassList();
        $classAry  = array();
        foreach ($classList as $class) {
            $classAry[$class->classid] = $class->title;
        }

        $timeList = array();
        for ($i = 0; $i < 15; $i++) {
            $timestamp      = time() - 86400 * $i;
            $tmp            = date('Ymd', $timestamp);
            $timeList[$tmp] = $tmp;
        }
        // 查找签到记录
        $criteria = new CDbCriteria();
        $criteria->compare('t.schoolid', $this->branchId);
        $criteria->compare('t.sign_start', '>=' . $dateTime);
        $criteria->compare('t.sign_end', '<=' . $dateTime);
        if ($classid) {
            $criteria->compare('t.classid', $classid);
        }
        $criteria->order = 'update_at DESC,id DESC';
        $criteria->with  = array('child', 'user');
        $signlogModel    = ChildDailySignLog::model()->findAll($criteria);
        $data            = array();
        foreach ($signlogModel as $model) {
            $data[$model->classid][] = array(
                'status'    => $model->statusText(),
                'childname' => $model->child ? $model->child->getChildName() : $model->childid,
                'username'  => $model->user->getNameLang(),
                'update'    => date('Y-m-d H:i:s', $model->update_at),
            );
        }

        $this->render('signlog', array(
            'classAry' => $classAry,
            'timeList' => $timeList,
            'date'     => $dateTime,
            'data'     => $data,
        ));
    }

    /***
     * 学生签到2.0
     */
    public function actionIndex2()
    {
        $this->branchSelectParams['urlArray'] = array('//mcampus/childsign/index2');
        $this->render('index2');
    }

    //学校所有班级和班级下学生总数已经签到的总数
    public function actionAllClasses()
    {
        $date      = Yii::app()->request->getParam('date', '');
        $timestamp = strtotime($date);
        if ($timestamp > strtotime(date('Ymd'))) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '不可超越当天时间');
            $this->showMessage();
        }
        $classes_data = $this->getClassList2($timestamp);
        $classes      = $classes_data['class'];
        $select_yid   = $classes_data['select_yid'];//选择的学年
        $current_yid  = $classes_data['currentYid'];//当前学年
        if (!$classes) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '暂无班级');
            $this->showMessage();
        }
        $classIds                  = array();
        $attendance_num            = 0;//已考勤
        $student_num               = 0;//总人数
        $sign_num                  = 0;//出勤总数
        $tardy_num                 = 0;//迟到总数
        $vacation_num              = 0;//请假总人数
        $truancy_num               = 0;//旷课总数
        $child_vacation_reason     = array();//异常考勤的孩子的备注信息
        $no_sign_childids_by_class = array();//未考勤的学生list
        $child_est_begin_time      = array();//预计到校时间
        $child_begin_time          = array();//实际到校时间
        $all_child_id              = array();//所有孩子的id
        $signChildByCLass          = array();//班级维度-已考勤的孩子信息
        $signChildids              = array();//已考勤的孩子id
        $teacher_id                = array();//操作老师的id
        $teacher_name_list         = array();//操作老师的名字
        $childSignStatus           = array();//孩子的具体签到状态 出勤 线上出勤 病假 事假  旷课 着装违规
        $dressCode_violation_num   = array();//孩子本学期着装违规的次数
        foreach ($classes as $_class) {
            $classIds[$_class->classid] = $_class->classid;
        }
        //六日是否上课
        $crit = new CDbCriteria;
        $crit->compare('yid', $select_yid);
        $crit->compare('month', date('Ym', $timestamp));
        $calendar_schooldays = CalendarSchoolDays::model()->find($crit);
        if ($calendar_schooldays) {
            $schoolday      = explode(',', $calendar_schooldays->schoolday_array);
            $half_schoolday = explode(',', $calendar_schooldays->half_schoolday_array);
            $all_schoolday  = array_merge($schoolday, $half_schoolday);
            if (!in_array(date('d', $timestamp), $all_schoolday)) {
//                $this->addMessage('state', 'fail');
//                $this->addMessage('message', '此时间段无课');
//                $this->showMessage();
            }
        } else {
//            $this->addMessage('state', 'fail');
//            $this->addMessage('message', '此时间段无课');
//            $this->showMessage();
        }
        //获取所有孩子
        if ($select_yid == $current_yid) {
            //当前学年
//            array(0,10,20,100,888)
            $status    = '<999';
            $childData = $this->getStuduentsByClass($this->branchId, array_keys($classIds), $status, $timestamp);
        } else {
            //历史学年
            $childData = $this->getHistoryStudent($timestamp);
        }
        $child_info_by_child_id = array();
        foreach ($childData as $item) {
            $all_child_id[]                      = $item['id'];
            $child_info_by_child_id[$item['id']] = $item;
        }
        $classes = array_values($classes);
        //正常出勤的数据
        $signChilds = $this->getSignChild($timestamp);
        foreach ($signChilds as $sign) {
            if (in_array($sign['childid'], $all_child_id)) {
                $teacher_id [] = $sign['uid'];//老师的id
                $sign_num++;//出勤总数
                $attendance_num++;//已考勤人数
                $signChildByCLass[$sign->classid][]                      = array(
                    'childid'    => (int)$sign['childid'],
                    'teacher_id' => $sign['uid'],
                );
                $signChildids[]                                          = $sign->childid;//记录已经考勤的学生id
                $childSignStatus[$sign->classid][$sign->childid]['main'] = '1';//正常出勤
                $normal_sign[]                                           = $sign->childid;//记录出勤的学生id
                //数据调整-签到完成后又进行了分班 class_child 数据需要调整
                if ($child_info_by_child_id[$sign->childid]['classid'] != $sign->classid) {
                    foreach ($childData as $k => $v) {
                        if ($v['id'] == $sign->childid) {
                            $childData[$k]['classid'] = $sign->classid;
                        }
                    }
                }
            }
        }

        $late_by_class = array();//迟到未到校的孩子信息
        $all_online    = array();//记录所有线上出勤的人
        $all_late      = array();//记录所有迟到的人
//        //线上出勤的数据
//        $all_online_list  = ChildVacation::model()->getVacations($timestamp, $this->branchId);
//        foreach ($all_online_list as $child_id=>$item){
//            $all_online[] = $item['child_id'];
//        }
        //异常考勤的孩子数据
        $vacations = ChildVacation::model()->getVacations($timestamp, $this->branchId);
        foreach ($vacations as $child_id => $item) {
            if (in_array($item['child_id'], $all_child_id)) {
                $signChildids[] = $item['child_id'];//已考勤的孩子id
                $teacher_id []  = $item['uid'];//老师的id
                $attendance_num++;//已考勤
                if (in_array($item['type'], array(10, 20))) {
                    $vacation_num++;//请假总人数
                }
                if ($item['type'] == 11) {
                    $sign_num++;//线上出勤
                }
                if ($item['type'] == 40) {
                    $tardy_num++;//迟到总数
                    $all_late[] = $item['child_id'];
                    if (!empty($item['est_begin_time'])) {
                        $child_est_begin_time[$item['child_id']] = date('H:i', $item['est_begin_time']);
                    }
                    if (!empty($item['begin_time'])) {
                        $child_begin_time[$item['child_id']] = date('H:i', $item['begin_time']);
                    }
                    $attendance_num--;//迟到已到校的人数记录到正常出勤 未到校的不记录
                    //迟到未到校的 不是正常出勤且到校时间为空
                    if (!in_array($item['child_id'], $normal_sign) && empty($item['begin_time'])) {
                        $late_by_class[$item['classid']][] = array(
                            'childid'    => $item['child_id'],
                            'teacher_id' => $item['uid'],
                        );
                    }
                    //迟到已到校 不在正常出勤且begin_time不为空
                    if (!in_array($item['child_id'], $normal_sign) && !empty($item['begin_time'])) {
                        $signChildByCLass[$item['classid']][] = array(
                            'childid'    => (int)$child_id,
                            'teacher_id' => $item['uid'],
                        );
                    }
                }
                if ($item['type'] == 60) {
                    $truancy_num++; //旷课总数
                }
                //迟到已到校的数据已经记录到$signChildByCLass 未到校的记录到$late_by_class
                if ($item['type'] != 40) {
                    $signChildByCLass[$item['classid']][] = array(
                        'childid'    => (int)$child_id,
                        'teacher_id' => $item['uid'],
                    );
                }
                $childSignStatus[$item['classid']][$item['child_id']]['main'] = $item['type'];
                //数据调整-签到完成后又进行了分班 class_child 数据需要调整
                if ($child_info_by_child_id[$item['child_id']]['classid'] != $item['classid']) {
                    foreach ($childData as $k => $v) {
                        if ($v['id'] == $item['child_id']) {
                            $childData[$k]['classid'] = $item['classid'];
                        }
                    }
                }
            }
            //线上出勤的数据不用展示备注信息
            if ($item['type'] != 11) {
                $child_vacation_reason[$item['child_id']] = $item['vacation_reason'];
            }
        }
        //获取老师的名字
        if (!empty($teacher_id)) {
            $criteria = new CDbCriteria();
            $criteria->compare('uid', $teacher_id);
            $criteria->select = 'uid,name,uname';
            $teacher_data     = User::model()->findAll($criteria);
            foreach ($teacher_data as $item) {
                $teacher_name_list[$item->uid] = $item->getName();
            }
        }
        //未考勤的学生
        foreach ($childData as $item) {
            if (!in_array($item['id'], $signChildids) && in_array($item['id'], $all_child_id)) {
                $no_sign_childids_by_class[$item['classid']][] = $item['id'];
            }
        }
        //其他违规数据 着装违规
        $violation = ChildViolation::model()->getViolations($timestamp, $this->branchId);
        foreach ($violation as $item) {
            if (in_array($item['child_id'], $all_child_id)) {
                $childSignStatus[$item['classid']][$item['child_id']]['other'] = $item['type'];
            }
        }
        $class_child = array();
        foreach ($childData as $k => $item) {
            $student_num++;
            $item['photo']                              = CommonUtils::childPhotoUrl($item['photo'], 'small');
            $class_child[$item['classid']][$item['id']] = $item;
        }
        foreach ($classes as $k => $_class) {
            if (empty($class_child[$_class->classid])) {
                unset($classes[$k]);
            }
        }
        $main_status_map  = array(
            1  => Yii::t('attends', 'Present'),
            11 => Yii::t('attends', 'Online Present'),
            10 => Yii::t('campus', 'Sick Leave'),
            20 => Yii::t('campus', 'Personal Leave'),
            40 => Yii::t('campus', 'Tardy'),
            60 => Yii::t('attends', 'Absent'),
        );
        $other_status_map = array(
            10 => Yii::t('attends', 'Uniform Infraction'),
        );
        //学生着装违规的本学期总次数
        //本学年的学期开始结束时间
        $crit = new CDbCriteria;
        $crit->compare('yid', $select_yid);
        $calendar_list = Calendar::model()->find($crit);
        $timepoints    = explode(',', $calendar_list->timepoints);
        if ($timestamp >= $timepoints[0] && $timestamp < $timepoints[2]) {
            //第一学期
            $start_time = $timepoints[0];
            $end_time   = $timepoints[1];
        } else {
            //第二学期
            $start_time = $timepoints[2];
            $end_time   = $timepoints[3];
        }
        $criteria = new CDbCriteria();
        $criteria->compare('school_id', $this->branchId);
        $criteria->compare('type', 10);
        $criteria->compare('updata_time', '>=' . $start_time);
        $criteria->compare('updata_time', '<=' . $end_time);
        $criteria->select          = 'child_id,COUNT(*) as type';//必须是表里面存在的字段否则无法取到值
        $criteria->group           = 'child_id';
        $criteria->index           = 'child_id';
        $dressCode_violation_model = ChildViolation::model()->findAll($criteria);
        foreach ($dressCode_violation_model as $child => $item) {
            $dressCode_violation_num[$child] = (int)$item->type;
        }
        $data = array(
            'is_history'              => $select_yid != $current_yid,//历史学年的数据不可以操作考勤
            'class_list'              => $classes,
            'class_child'             => $class_child,
            'teacher_name_list'       => $teacher_name_list,//操作老师的名字
            'no_sign_childids'        => $no_sign_childids_by_class,//未签到孩子的id
            'late_list'               => $late_by_class,//班级维度的孩子id-迟到未到校
            'sign_childids'           => $signChildByCLass,//班级维度-已经考勤孩子信息
            'child_sign_status'       => $childSignStatus,//班级维度孩子的具体签到状态
            'attendance_num'          => $attendance_num,//已考勤人数
            'student_num'             => $student_num,//学生总人数
            'sign_num'                => $sign_num,//出勤总数
            'tardy_num'               => $tardy_num,//迟到总数
            'vacation_num'            => $vacation_num,//请假总人数
            'truancy_num'             => $truancy_num,//旷课总数
            'main_status_map'         => $main_status_map,//主要的出勤数据状态对应的展示信息
            'other_status_map'        => $other_status_map,//其他违规状态对应的展示信息
            'child_reason'            => $child_vacation_reason,//请假 旷课 迟到的备注信息
            'child_est_begin_time'    => $child_est_begin_time,//迟到-预计到校时间 est_begin_time
            'child_begin_time'        => $child_begin_time,//迟到-实际到校时间 begin_time
            'dressCode_violation_num' => $dressCode_violation_num,//着装违规次数
        );
        $this->addMessage('state', 'success');
        $this->addMessage('data', $data);
        $this->showMessage();
    }

    //班级批量签到 正常出勤 线上出勤
    public function actionClassSignIn()
    {
        $childData = Yii::app()->request->getParam('childData', array());
        $date      = Yii::app()->request->getParam('date', '');
        $type      = Yii::app()->request->getParam('type', '');
        if ($childData && $date && $type) {
            $timestamp = strtotime($date);
            if ($this->limitRole($timestamp)) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '您在此时段没有权限操作');
                $this->showMessage();
            }
            $crit = new CDbCriteria();
            $crit->compare('childid', $childData);
            $crit->index = 'childid';
            $childModel  = ChildProfileBasic::model()->findAll($crit);

            //中学不能操作
            $crit = new CDbCriteria();
            $crit->compare('classtype', array('e6', 'e7', 'e8', 'e9', 'e10', 'e11', 'e12'));
            $classModel = IvyClass::model()->findAll($crit);
            $classList  = array();
            foreach ($classModel as $val) {
                $classList[] = $val->classid;
            }
            //检查孩子是否已经有其他签到数据
            $criteria = new CDbCriteria();
            $criteria->compare('child_id', $childData);
            $criteria->compare('vacation_time_start', "<={$timestamp}");
            $criteria->compare('vacation_time_end', ">={$timestamp}");
            $criteria->compare('stat', ChildVacation::STATUS_CHECKED);
//            var_dump(ChildVacation::model()->findAll($criteria));die;
            $count = ChildVacation::model()->count($criteria);
            if ($count) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '数据异常，请刷新重试');
                $this->showMessage();
            }
            foreach ($childData as $childid) {
                if (isset($childModel) && !in_array($childModel[$childid]->classid, $classList)) {
                    #正常出勤
                    if ($type == 1) {
                        $childObj = ChildProfileBasic::model()->findByPk($childid);
                        if ($childObj && $childObj->schoolid === $this->branchId) {
                            $signModel = $this->childSign($childObj, $timestamp);
                        }
                    }
                    #线上出勤
                    if ($type == 11) {
                        $late_time = 0;
                        TimetableRecords::childSign($childModel[$childid], $timestamp, 11, $late_time);
                    }
                } else {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('campus', 'MS attendance are taken by class teachers, data synced to this page automatically. (based on 1st period class)'));
                    $this->showMessage();
                }
            }
            $this->addMessage('state', 'success');
            $this->addMessage('message', 'success');
            $this->showMessage();
        }
        $this->addMessage('state', 'fail');
        $this->addMessage('message', 'error');
        $this->showMessage();
    }

    //单个孩子签到 正常出勤 和 线上出勤
    public function actionStudentSignIn()
    {
        $child_id = Yii::app()->request->getParam('child_id', '');
        $date     = Yii::app()->request->getParam('date', '');
        $type     = Yii::app()->request->getParam('type', '');//1 正常出勤 11 线上出勤
        $time     = Yii::app()->request->getParam('time', '');//到校时间
        if ($child_id && $date && in_array($type, array(1, 11))) {
            $timestamp  = strtotime($date);
            $childModel = ChildProfileBasic::model()->findByPk($child_id);
            if ($this->limitRole($timestamp)) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '在此时段您没有权限操作');
                $this->showMessage();
            }
            if (isset($childModel) && in_array($childModel->ivyclass->classtype, array('e6', 'e7', 'e8', 'e9', 'e10', 'e11', 'e12'))) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('campus', 'MS attendance are taken by class teachers, data synced to this page automatically. (based on 1st period class)'));
                $this->showMessage();
            }
            //检查学生是否迟到 早退
            $criteria = new CDbCriteria();
            $criteria->compare('child_id', $child_id);
            $criteria->compare('vacation_time_start', "<={$timestamp}");
            $criteria->compare('vacation_time_end', ">={$timestamp}");
            $criteria->compare('stat', ChildVacation::STATUS_CHECKED);
            $vacationModel = ChildVacation::model()->find($criteria);//异常考勤数据
            if ($vacationModel) {
                //迟到的学生签到需要实际迟到时间 其他类型的异常签到直接删除异常考勤数据
                if (in_array($vacationModel->type, array(ChildVacation::VACATION_LATE, ChildVacation::VACATION_LEAVE))) {

                    if (empty($time)) {
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('campus', '实际到校时间不能为空'));
                        $this->showMessage();
                    }

                    $begin_time = strtotime(date("Y-m-d", $vacationModel->vacation_time_start) . " " . $time);
                    if (!$begin_time) {
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('campus', 'time error'));
                        $this->showMessage();
                    }
                    $vacationModel->begin_time  = $begin_time;
                    $vacationModel->updata_time = time();
                    $vacationModel->uid         = Yii::app()->user->id;
                    $vacationModel->save();
                } else {
                    $vacationModel->delete();
                }
            }
            $res = false;
            if ($type == 1) {
                $res = $this->childSign($childModel, $timestamp);
            }
            if ($type == 11) {
                //删除正常出勤
                $crit = new CDbCriteria;
                $crit->compare('childid', $child_id);
                $crit->compare('schoolid', $this->branchId);
                $crit->compare('sign_timestamp', $timestamp);
                $model = ChildDailySign::model()->findAll($crit);
                if ($model) {
                    foreach ($model as $val) {
                        $val->uid = Yii::app()->user->id;
                        $val->delete();
                    }
                }
                $late_time = 0;
                $res       = TimetableRecords::childSign($childModel, $timestamp, 11, $late_time);
            }
            if ($res) {
                $this->addMessage('state', 'success');
                $this->addMessage('message', 'success');
                $this->showMessage();
            }
        }
        $this->addMessage('state', 'fail');
        $this->addMessage('message', 'error');
        $this->showMessage();
    }

    //单个孩子签到   病假 事假 旷课
    public function actionSetVacation()
    {
        $child_id = Yii::app()->request->getParam('child_id', '');
        $date     = Yii::app()->request->getParam('date', '');
        $type     = Yii::app()->request->getParam('type', '');//10病假  20事假 60旷课
        $memo     = Yii::app()->request->getParam('memo', '');//备注
        if (empty($memo)) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t("reg", 'Memo required'));
            $this->showMessage();
        }
        if ($child_id && $date && in_array($type, array(10, 20, 60))) {
            $timestamp = strtotime($date);
            if ($this->limitRole($timestamp)) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '在此时段您没有权限操作');
                $this->showMessage();
            }
            $childModel = ChildProfileBasic::model()->findByPk($child_id);
            if (isset($childModel) && in_array($childModel->ivyclass->classtype, array('e6', 'e7', 'e8', 'e9', 'e10', 'e11', 'e12'))) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('campus', 'MS attendance are taken by class teachers, data synced to this page automatically. (based on 1st period class)'));
                $this->showMessage();
            }
            TimetableRecords::childSignDel($child_id, $this->branchId, $timestamp, $this->staff->uid);
//            //取消正常出勤数据
//            $crit = new CDbCriteria;
//            $crit->compare('childid', $child_id);
//            $crit->compare('sign_timestamp', $timestamp);
//            ChildDailySign::model()->deleteAll($crit);
//            //先删除之前的异常考勤记录
//            $criteria = new CDbCriteria;
//            $criteria->compare('child_id', $child_id);
//            $criteria->compare('school_id', $this->branchId);
//            $criteria->compare('vacation_time_start', "<={$timestamp}");
//            $criteria->compare('vacation_time_end', ">={$timestamp}");
//            ChildVacation::model()->deleteAll($criteria);
            $type_map = array(
                10 => 31,//病假
                20 => 30,//事假
                60 => 40,//旷课
            );
            if (TimetableRecords::childSign($childModel, $timestamp, $type_map[$type], 0, $memo)) {
                $this->addMessage('state', 'success');
                $this->addMessage('message', 'success');
                $this->showMessage();
            }
        }
        $this->addMessage('state', 'fail');
        $this->addMessage('message', 'error');
        $this->showMessage();
    }

    //单个孩子签到   迟到
    public function actionSetLate()
    {
        $child_id = Yii::app()->request->getParam('child_id', '');
        $date     = Yii::app()->request->getParam('date', '');
        $time     = Yii::app()->request->getParam('time', '');//到校时间 09:11
        $type     = Yii::app()->request->getParam('type', '');//1尚未到校 2目前已到校
        $memo     = Yii::app()->request->getParam('memo', '');//备注
        if (empty($memo)) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t("reg", 'Memo required'));
            $this->showMessage();
        }
        if ($child_id && $date) {
            $timestamp = strtotime($date);
            if ($this->limitRole($timestamp)) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '在此时段您没有权限操作');
                $this->showMessage();
            }
            $childModel = ChildProfileBasic::model()->findByPk($child_id);
            if (isset($childModel) && in_array($childModel->ivyclass->classtype, array('e6', 'e7', 'e8', 'e9', 'e10', 'e11', 'e12'))) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('campus', 'MS attendance are taken by class teachers, data synced to this page automatically. (based on 1st period class)'));
                $this->showMessage();
            }
            if ($childModel->schoolid != $this->branchId) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('campus', 'Selected student(s) is not enrolled in this campus'));
                $this->showMessage();
            }
            //删除原始数据
            TimetableRecords::childSignDel($child_id, $this->branchId, $timestamp, $this->staff->uid);
            //插入新数据
            $timesEst = '';
            if (!empty($time)) {
                $timesEst = strtotime(date("Y-m-d", $timestamp) . " " . $time);
                if (!$timesEst) {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', 'time error');
                    $this->showMessage();
                }
            }
            $childmodel                      = new ChildVacation();
            $childmodel->child_id            = $child_id;
            $childmodel->school_id           = $childModel->schoolid;
            $childmodel->class_id            = $childModel->classid;
            $childmodel->vacation_time_start = $timestamp;
            $childmodel->vacation_time_end   = $timestamp;
            $childmodel->type                = 40;
            $childmodel->vacation_reason     = $memo;
            $childmodel->uid                 = Yii::app()->user->id;
            $childmodel->updata_time         = time();
            $childmodel->stat                = 1;
            //1尚未到校
            if ($type == 1) {
                $childmodel->est_begin_time = $timesEst;//预计到校时间
            }
            if ($type == 2) {
                $childmodel->begin_time = $timesEst;//实际到校时间
            }
            $res = $childmodel->save();
            //2已到校
            if ($type == 2 && $res) {
                if (!$this->childSign($childModel, $timestamp)) {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', 'error');
                    $this->showMessage();
                }
            }
            $this->addMessage('state', 'success');
            $this->addMessage('message', 'success');
            $this->showMessage();
        }
        $this->addMessage('state', 'fail');
        $this->addMessage('message', 'error');
        $this->showMessage();
    }

    //取消考勤2.0
    public function actionDelSign2()
    {
        $child_id = Yii::app()->request->getParam('child_id', '');
        $date     = Yii::app()->request->getParam('date', '');
        $class_id = Yii::app()->request->getParam('class_id', '');
        if ($child_id && $date && $class_id) {
            $timestamp = strtotime($date);
            if ($this->limitRole($timestamp)) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '在此时段您没有权限操作');
                $this->showMessage();
            }
            $childModel = ChildProfileBasic::model()->findByPk($child_id);
            if (isset($childModel) && in_array($childModel->ivyclass->classtype, array('e6', 'e7', 'e8', 'e9', 'e10', 'e11', 'e12'))) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('campus', 'MS attendance are taken by class teachers, data synced to this page automatically. (based on 1st period class)'));
                $this->showMessage();
            }
            TimetableRecords::childSignDel($child_id, $this->branchId, $timestamp, $this->staff->uid);


            $this->addMessage('state', 'success');
            $this->addMessage('message', 'success');
            $this->showMessage();

        }
        $this->addMessage('state', 'fail');
        $this->addMessage('message', 'error');
        $this->showMessage();
    }

    //学生违规  10着装违规
    public function actionSetViolation()
    {
        $child_id = Yii::app()->request->getParam('child_id', '');
        $class_id = Yii::app()->request->getParam('class_id', '');
        $date     = Yii::app()->request->getParam('date', '');
        if ($child_id && $date) {
            $timestamp = strtotime($date);
            if ($this->limitRole($timestamp)) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '您在此时段没有权限操作');
                $this->showMessage();
            }
            $childModel = ChildProfileBasic::model()->findByPk($child_id);
            if (isset($childModel) && in_array($childModel->ivyclass->classtype, array('e6', 'e7', 'e8', 'e9', 'e10', 'e11', 'e12'))) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('campus', 'MS attendance are taken by class teachers, data synced to this page automatically. (based on 1st period class)'));
                $this->showMessage();
            }
            $model              = new ChildViolation();
            $model->child_id    = $child_id;
            $model->class_id    = $class_id;
            $model->school_id   = $this->branchId;
            $model->type        = 10;
            $model->uid         = Yii::app()->user->id;
            $model->updata_time = strtotime($date);
            $model->save();
        }
        $this->addMessage('state', 'success');
        $this->addMessage('message', 'success');
        $this->showMessage();

    }

    //取消学生着装违规
    public function actionCancelViolation()
    {
        $child_id = Yii::app()->request->getParam('child_id', '');
        $date     = Yii::app()->request->getParam('date', '');
        if ($child_id && $date) {
            $timestamp = strtotime($date);
            if ($this->limitRole($timestamp)) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '您在此时段没有权限操作');
                $this->showMessage();
            }
            $childModel = ChildProfileBasic::model()->findByPk($child_id);
            if (isset($childModel) && in_array($childModel->ivyclass->classtype, array('e6', 'e7', 'e8', 'e9', 'e10', 'e11', 'e12'))) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('campus', 'MS attendance are taken by class teachers, data synced to this page automatically. (based on 1st period class)'));
                $this->showMessage();
            }
            $criteria = new CDbCriteria();
            $criteria->compare('child_id', $child_id);
            $criteria->compare('updata_time', "<={$timestamp}");
            $criteria->compare('updata_time', ">={$timestamp}");
            $criteria->compare('type', 10);
            $data = ChildViolation::model()->findAll($criteria);
            if (empty($data)) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', 'data null');
                $this->showMessage();
            }
            $ChildViolation = ChildViolation::model()->findAll($criteria);
            foreach ($ChildViolation as $item) {
                $item->delete();
            }
        }
        $this->addMessage('state', 'success');
        $this->addMessage('message', 'success');
        $this->showMessage();
    }


}
