<?php
if(!defined("IVY_POLICY"))
    throw new CException('DO NOT USE THIS FILE DIRECTLY');

return array( 'policy'=>array(

    //是否老生老办法
    ENABLE_CONSTANT_TUITION => true,
    FENTAN_FACTOR => DAILY,

    //是否开放课外活动账单
    ENABLE_AFTERSCHOOLS => true,

    //图书馆工本费
    FEETYPE_LIBCARD => 10,

    //学期四个时间点；取前后两端
    TIME_POINTS   => array(202009,202101,202102,202107),

    //年付开通哪些缴费类型
  PAYGROUP_ANNUAL => array(
      ITEMS => array(
          FEETYPE_TUITION,
          FEETYPE_LUNCH,
          FEETYPE_SCHOOLBUS,
      ),
      TUITION_CALC_BASE => BY_ANNUAL,
  ),
//
//   //学期付开通哪些缴费类型
  PAYGROUP_SEMESTER => array(
      ITEMS => array(
          FEETYPE_TUITION,
          FEETYPE_LUNCH,
          FEETYPE_SCHOOLBUS,
      ),
      TUITION_CALC_BASE => BY_SEMESTER,
  ),

   //月付开通哪些缴费类型
   // PAYGROUP_MONTH => array(
   //     ITEMS => array(
   //         // FEETYPE_TUITION,
   //         // FEETYPE_LUNCH,
   //         FEETYPE_SCHOOLBUS,
   //     ),
   //     TUITION_CALC_BASE => BY_MONTH,
   // ),

    //计算基准：BY_MONTH, 或者 BY_SETTING, 前者表示所有的学费都基于月费用计算，后者表示所有的学费都基于学期或学年
    TUITION_CALCULATION_BASE => array(

        BY_MONTH => array(

            //月收费金额
            /*
            AMOUNT => array(
                FULL5D => 15055,
                HALF5D => 10840,
            ),
             *
             */

            //收费月份，及其权重
            MONTH_FACTOR => array(
                202009=>1,
                202010=>1,
                202011=>1,
                202012=>1,
                202101=>1,
                202102=>1,
                202103=>1,
                202104=>1,
                202105=>1,
                202106=>1,
                202107=>0,
            ),

            //折扣

            GLOBAL_DISCOUNT => array(
                DISCOUNT_ANNUAL => '1:1:2020-12-01'
            )


        ),

        //东湖配置实例

        BY_ANNUAL => array(
            AMOUNT => array(
                FULL5D => 211340,
                // HALF2D => 63286,
                // HALF3D => 91000,
                // HALF5D => 147600,
            ),
        ),

        BY_SEMESTER1 => array(
            AMOUNT => array(
                FULL5D => 110315,
                // HALF2D => 32800,
                // HALF3D => 47163,
                // HALF5D => 81485,
                //HALF4D => 41731,
            ),
        ),

        BY_SEMESTER2 => array(
            AMOUNT => array(
                FULL5D => 101025,
                // HALF2D => 32443,
                // HALF3D => 46651,
                // HALF5D => 70675,
                //HALF4D => 62597,
            ),
        ),

    ),

    //每餐费用
    LUNCH_PERDAY => 42,

    //账单到期日和账单开始日之差
    DUEDAYS_OFFSET => 1,

));