<?php if(!empty($wi)):?>
<style>
.weather table{
	margin-left:30px;
	background:#f3f3f3;
	width:190px;
}
.weather td{
	font-family: "Microsoft Yahei";
	padding: 0;
	margin: 0;
	color:#ff6309;
}
.weather td a{
	color:#ff6309;
	text-decoration:none;
}
.weather td.big{
	font-size: 28px;
	line-height: 30px;
	width:40px;
}
.weather td.big span{
	margin: 2px 0;
	width:40px;
	text-align:center;
	background:#ff6309;
	color:#fff;
	display: inline-block;
}
.weather td.temp{
	width:100px;
}
.weather td.middle{
	font-size: 18px;
	line-height: 30px;
}
.weather td.normal{
	font-size: 12px;
	line-height: 22px;
}
.weather td.normal span{
	font-size: 10px;
}
</style>

<table cellpadding=0 cellspacing=0>
	<tr>
		<td class="big tac">
			<span><?php echo date('d');?></span>
		</td>
		<td rowspan="2" width=50 class="tac">
			<?php echo CHtml::image($wi['imageUrl'], $wi['weatherDesc'], array("width"=>46));?>
		</td>
		<td class="middle temp">
			<?php echo CHtml::link($wi['temp'], $wi['weatherDescUrl'], array("target"=>"_blank"));?>
		</td>
	</tr>
	<tr>
		<td class="normal tac">
			<?php echo Yii::app()->locale->getMonthName(date('n'),'abbreviated');?>
		</td>
		<td class="normal">
			<?php echo CHtml::link($wi['cityName'], $wi['weatherDescUrl'], array("target"=>"_blank"));?>
			<?php if(!empty($aq)):?>
				<span class="aq">
				<?php
					echo CHtml::link(addColon(Yii::t("weatherWidget",'Air')).$aq['aqNumber'] . '<em></em>',
					//"http://pm25.moji001.com/aqi/index-".$aq['cityCode'].".html",
					"http://aqicn.org/city/".strtolower($aq['cityName'])."/",
					array(
						"target"=>"_blank",
						"title"=>Yii::t("weatherWidget",'Air Quality Index (PM2.5)')."\r\n". addColon($aq['aqNumber']).$aq['aqSignName']."\r\n".$aq['aqDesc'],
						"class"=>$aq['aqSignClass']
						)
					);
				?></span>
			<?php endif;?>
		</td>
	</tr>
</table>
<?php endif;?>