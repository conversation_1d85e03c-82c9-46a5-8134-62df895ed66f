<?php

/**
 * This is the model class for table "ivy_laseregg_info".
 *
 * The followings are the available columns in table 'ivy_laseregg_info':
 * @property integer $id
 * @property string $schoolid
 * @property string $laseregg_id
 * @property string $laseregg_mac
 * @property string $laseregg_name
 * @property integer $add_timestamp
 * @property integer $add_user
 */
class LasereggInfo extends CActiveRecord
{
	
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_laseregg_info';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('schoolid, laseregg_mac, laseregg_name, add_timestamp, add_user', 'required'),
			array('add_timestamp, add_user', 'numerical', 'integerOnly'=>true),
			array('schoolid', 'length', 'max'=>30),
			array('laseregg_id, laseregg_mac, laseregg_name', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, schoolid, laseregg_id, laseregg_mac, laseregg_name, add_timestamp, add_user', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'schoolid' => Yii::t('laseregg', 'Schoolid'),
			'laseregg_id' => Yii::t('laseregg', 'Laseregg'),
			'laseregg_mac' => Yii::t('laseregg', 'Laseregg Pass'),
			'laseregg_name' => Yii::t('laseregg', 'Sensor name'),
			'add_timestamp' => Yii::t('laseregg', 'Add Timestamp'),
			'add_user' => 'Add User',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('laseregg_id',$this->laseregg_id,true);
		$criteria->compare('laseregg_mac',$this->laseregg_mac,true);
		$criteria->compare('laseregg_name',$this->laseregg_name,true);
		$criteria->compare('add_timestamp',$this->add_timestamp);
		$criteria->compare('add_user',$this->add_user);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return LasereggInfo the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	public function showName()
	{
		return $this->laseregg_name . ' (' . $this->laseregg_mac . ')';
	}
}
