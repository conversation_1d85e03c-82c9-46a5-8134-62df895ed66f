<?php
class StatsItemCommand extends CConsoleCommand
{
    public $batchNum = 200;
    
    public function init() {
        parent::init();
        
        Yii::import('common.models.charts.*');
    }
    
    public function actionVisit()
    {
        $nowtime = time();
        $dateN = date('N');
        $monday = $nowtime - ($dateN-1)*3600*24;
        $mondayF = date('Ymd', $monday);
        $startyear = date('m', $monday) < 9 ? date('Y', $monday)-1 : date('Y', $monday);
        $branchs = Branch::model()->getBranchList();
        
        foreach ($branchs as $schoolid=>$branch){
            echo $schoolid."\n";
            # New
            $sql = "select count(*) as count from ivy_visits_basic_info where id in (select basic_id from ivy_visits_record where schoolid='".$schoolid."') and register_timestamp >= ".strtotime($mondayF);
            $clients = Yii::app()->db->createCommand($sql)->queryRow();
            $count = $clients['count'] ? $clients['count'] : 0;

            $criteria = new CDbCriteria();
            $criteria->compare('period', $mondayF);
            $criteria->compare('schoolid', $schoolid);
            $criteria->compare('category', IvyStatsItem::CATEGORY_VISIT);
            $criteria->compare('code', IvyStatsItem::CODE_VISIT_NEW);
            $model = IvyStatsItem::model()->find($criteria);
            if ($model === null)
                $model = new IvyStatsItem;
            $model->period_type = IvyStatsItem::TYPE_W;
            $model->period = $mondayF;
            $model->schoolid = $schoolid;
            $model->category = IvyStatsItem::CATEGORY_VISIT;
            $model->code = IvyStatsItem::CODE_VISIT_NEW;
            $model->data = $count;
            $model->startyear = $startyear;
            $model->userid = 0;
            $model->created = $nowtime;
            $model->updated = $nowtime;
            if (!$model->save()){
                print_r($model->getErrors());
            }
            else {
                echo "New: ".$count."\n";
            }

            # Invalid
            $sql = "select count(*) as count from ivy_visits_basic_info where id in (select basic_id from ivy_visits_record where schoolid='".$schoolid."') and status <> 10 and status <> 30 and state_timestamp >= ".strtotime($mondayF);
            $clients = Yii::app()->db->createCommand($sql)->queryRow();
            $count = $clients['count'] ? $clients['count'] : 0;
            $criteria = new CDbCriteria();
            $criteria->compare('period', $mondayF);
            $criteria->compare('schoolid', $schoolid);
            $criteria->compare('category', IvyStatsItem::CATEGORY_VISIT);
            $criteria->compare('code', IvyStatsItem::CODE_VISIT_INVALID);
            $model = IvyStatsItem::model()->find($criteria);
            if ($model === null)
                $model = new IvyStatsItem;
            $model->period_type = IvyStatsItem::TYPE_W;
            $model->period = $mondayF;
            $model->schoolid = $schoolid;
            $model->category = IvyStatsItem::CATEGORY_VISIT;
            $model->code = IvyStatsItem::CODE_VISIT_INVALID;
            $model->data = $count;
            $model->startyear = $startyear;
            $model->userid = 0;
            $model->created = $nowtime;
            $model->updated = $nowtime;
            if (!$model->save()){
                print_r($model->getErrors());
            }
            else {
                echo "Invalid: ".$count."\n";
            }

            # Succeed
            $sql = "select count(*) as count from ivy_visits_basic_info where id in (select basic_id from ivy_visits_record where schoolid='".$schoolid."') and status = 30 and state_timestamp >= ".strtotime($mondayF);
            $clients = Yii::app()->db->createCommand($sql)->queryRow();
            $count = $clients['count'] ? $clients['count'] : 0;
            $criteria = new CDbCriteria();
            $criteria->compare('period', $mondayF);
            $criteria->compare('schoolid', $schoolid);
            $criteria->compare('category', IvyStatsItem::CATEGORY_VISIT);
            $criteria->compare('code', IvyStatsItem::CODE_VISIT_SUCCEED);
            $model = IvyStatsItem::model()->find($criteria);
            if ($model === null)
                $model = new IvyStatsItem;
            $model->period_type = IvyStatsItem::TYPE_W;
            $model->period = $mondayF;
            $model->schoolid = $schoolid;
            $model->category = IvyStatsItem::CATEGORY_VISIT;
            $model->code = IvyStatsItem::CODE_VISIT_SUCCEED;
            $model->data = $count;
            $model->startyear = $startyear;
            $model->userid = 0;
            $model->created = $nowtime;
            $model->updated = $nowtime;
            if (!$model->save()){
                print_r($model->getErrors());
            }
            else {
                echo "Succeed: ".$count."\n";
            }

            # 30天内未跟进人数
            $sql = "select count(*) as count from ivy_visits_basic_info where id in (select basic_id from ivy_visits_record where schoolid='".$schoolid."') and status = 10 and recent_log < ".($nowtime-2592000);# 2592000 = 3600*24*30
            $clients = Yii::app()->db->createCommand($sql)->queryRow();
            $count = $clients['count'] ? $clients['count'] : 0;
            $criteria = new CDbCriteria();
            $criteria->compare('period', $mondayF);
            $criteria->compare('schoolid', $schoolid);
            $criteria->compare('category', IvyStatsItem::CATEGORY_VISIT);
            $criteria->compare('code', IvyStatsItem::CODE_VISIT_NOLOG);
            $model = IvyStatsItem::model()->find($criteria);
            if ($model === null)
                $model = new IvyStatsItem;
            $model->period_type = IvyStatsItem::TYPE_W;
            $model->period = $mondayF;
            $model->schoolid = $schoolid;
            $model->category = IvyStatsItem::CATEGORY_VISIT;
            $model->code = IvyStatsItem::CODE_VISIT_NOLOG;
            $model->data = $count;
            $model->startyear = $startyear;
            $model->userid = 0;
            $model->created = $nowtime;
            $model->updated = $nowtime;
            if (!$model->save()){
                print_r($model->getErrors());
            }
            else {
                echo "NoLog: ".$count."\n";
            }
            
            # 有效人数
            $sql = "select count(*) as count from ivy_visits_basic_info where id in (select basic_id from ivy_visits_record where schoolid='".$schoolid."') and status = 10";
            $clients = Yii::app()->db->createCommand($sql)->queryRow();
            $count = $clients['count'] ? $clients['count'] : 0;
            $criteria = new CDbCriteria();
            $criteria->compare('period', $mondayF);
            $criteria->compare('schoolid', $schoolid);
            $criteria->compare('category', IvyStatsItem::CATEGORY_VISIT);
            $criteria->compare('code', IvyStatsItem::CODE_VISIT_ACTIVE);
            $model = IvyStatsItem::model()->find($criteria);
            if ($model === null)
                $model = new IvyStatsItem;
            $model->period_type = IvyStatsItem::TYPE_W;
            $model->period = $mondayF;
            $model->schoolid = $schoolid;
            $model->category = IvyStatsItem::CATEGORY_VISIT;
            $model->code = IvyStatsItem::CODE_VISIT_ACTIVE;
            $model->data = $count;
            $model->startyear = $startyear;
            $model->userid = 0;
            $model->created = $nowtime;
            $model->updated = $nowtime;
            if (!$model->save()){
                print_r($model->getErrors());
            }
            else {
                echo "Active: ".$count."\n";
            }
        }
    }
    
    public function actionFinance(){
        
         //执行时间
        $startDate = mktime(0,0,0,date('m',time())-1,date('d',time()),date('Y',time()));
		$endDate = mktime(23,59,59,date("m",$startDate),date("t",$startDate),date("Y",$startDate));
        if (intval(date('j',$startDate)) == 1){
            Yii::import('common.models.child.StatsChildCount');
            Yii::import('common.models.calendar.CalendarSchoolDays');
            Yii::import('common.models.calendar.Calendar');
            Yii::import('common.models.hr.HrPosition');
            Yii::import('common.models.invoice.IvyInvoiceShareUfida');
            
            //获取学校当前校历
            $modelList = Branch::model()->getBranchList(null,true);
            foreach ($modelList as $schoolVal){
                $calanderId = $schoolVal['yid'];
                //取校历的开始年
                $calendarModel = Calendar::model()->findByPk($calanderId);
                //实际在读人数
                $days = CalendarSchoolDays::getFriOfDay($calanderId, $startDate, $endDate);
                $activeNum = StatsChildCount::getEnrollmentAverageNumber($schoolVal['id'], $days);
                //收入
                $amount = IvyInvoiceShareUfida::sumAmountForSchool($schoolVal['id'], date('Y-m',$startDate));
                //教师
                $teacherNum = User::countActiveStaff($schoolVal['id'], explode(',', HrPosition::IVY_TEACHER));
                //行政人员
                $adminNum = User::countActiveStaff($schoolVal['id'], explode(',', HrPosition::IVY_CAMPUS_ADMIN),TRUE);
                //定义类型关系
                $financeActual = array(
                    IvyStatsItem::SUBCODE_FINANCE_ENROLLMENT=>$activeNum,
                    IvyStatsItem::SUBCODE_FINANCE_REVENUE=>$amount,
                    IvyStatsItem::SUBCODE_FINANCE_TEACHERS=>$teacherNum,
                    IvyStatsItem::SUBCODE_FINANCE_ADMIN=>$adminNum,
                );
                $_month = date('Ym',$startDate);
                //循环保存
                foreach ($financeActual as $key=>$val){
                    $crite = new CDbCriteria;
                    $crite->compare('schoolid', $schoolVal['id']);
                    $crite->compare('category', IvyStatsItem::CATEGORY_FINANCE);
                    $crite->compare('period', $_month);
                    $crite->compare('code', IvyStatsItem::CODE_FINANCE_ACTUAL);
                    $crite->compare('subcode', $key);
                    $model = IvyStatsItem::model()->find($crite);
                    if (empty($model)){
                        $model = new IvyStatsItem();
                        $model->created = time();
                    }
                    $model->schoolid = $schoolVal['id'];
                    $model->period_type = IvyStatsItem::TYPE_M;
                    $model->period = $_month;
                    $model->category = IvyStatsItem::CATEGORY_FINANCE;
                    $model->code = IvyStatsItem::CODE_FINANCE_ACTUAL;
                    $model->subcode = $key;
                    $model->data = $val;
                    $model->startyear = IvyStatsItem::getStartYear($_month);
                    $model->userid = 1;
                    $model->updated = time();
                    if ($model->save()){
                        echo $key.":".$val."\r\n";
                    }else{
                        print_r($model->getErrors())."\r\n";
                    }
                }
                echo $schoolVal['id'].":is Ok.\r\n";
            }
            
        }else{
            echo "Invalid date!\r\n";
        }
    }
    
    public function actionImportDemoData(){
        //定义数组
        $data = array(
            IvyStatsItem::CODE_FINANCE_ACTUAL => array(
                IvyStatsItem::SUBCODE_FINANCE_ENROLLMENT => array(100,250), //招生人数
                IvyStatsItem::SUBCODE_FINANCE_REVENUE => array(240000,800000),//收入
                IvyStatsItem::SUBCODE_FINANCE_PERSONNEL_COST => array(100000,350000),//人事费用
                IvyStatsItem::SUBCODE_FINANCE_FACILITIES_COST => array(6000,30000),//设备费用
                IvyStatsItem::SUBCODE_FINANCE_OVERHEND => array(100000,200000),//校园日常动作成本
                IvyStatsItem::SUBCODE_FINANCE_OTHER_COSTS => array(10000,100000),//其它(折旧、市场管理费用等)
                IvyStatsItem::SUBCODE_FINANCE_CASH_FLOW => array(2400000,15050000),//现金流
                IvyStatsItem::SUBCODE_FINANCE_TEACHERS => array(9,30),//教师（全职、包括保育教师）
                IvyStatsItem::SUBCODE_FINANCE_ADMIN => array(1,5),//行政人员（园长、副园长、行政经理、助理、保健医）
                IvyStatsItem::SUBCODE_FINANCE_RENT => array(10000,100000),//租房
            ),
            IvyStatsItem::CODE_FINANCE_BUDGET =>array(
                IvyStatsItem::SUBCODE_FINANCE_ENROLLMENT => 0, //招生人数
                IvyStatsItem::SUBCODE_FINANCE_REVENUE => 0,//收入
                IvyStatsItem::SUBCODE_FINANCE_PERSONNEL_COST => 0,//人事费用
                IvyStatsItem::SUBCODE_FINANCE_FACILITIES_COST => 0,//设备费用
                IvyStatsItem::SUBCODE_FINANCE_OVERHEND => 0,//校园日常动作成本
                IvyStatsItem::SUBCODE_FINANCE_OTHER_COSTS => 0,//其它(折旧、市场管理费用等)
                IvyStatsItem::SUBCODE_FINANCE_CASH_FLOW => 0,//现金流
                IvyStatsItem::SUBCODE_FINANCE_TEACHERS => 0,//教师（全职、包括保育教师）
                IvyStatsItem::SUBCODE_FINANCE_ADMIN => 0,//行政人员（园长、副园长、行政经理、助理、保健医）
                IvyStatsItem::SUBCODE_FINANCE_RENT => 0,//租房
            )
        );
        Yii::import('common.models.calendar.CalendarSemester');
        $modelList = Branch::model()->getBranchList(null,true);
        IvyStatsItem::model()->deleteAll('category=:category',array(':category'=>IvyStatsItem::CATEGORY_FINANCE));
        foreach ($modelList as $schoolVal){
            $calendarList = CalendarSemester::model()->getSemesterTimeStamp($schoolVal['yid']);
			$endMonth = date('Ym',$calendarList['spring_end']);
            $_month = $calendarList['fall_start'];
            print_r($schoolVal['id']."\n");
            while (date('Ym',$_month)<=$endMonth){
                $_month_ = date('Ym',$_month);
                print_r($_month_."\n");
                foreach ($data as $key=>$val){
                    foreach ($val as $k=>$v){
                        if ($key == IvyStatsItem::CODE_FINANCE_BUDGET){
                            $crite = new CDbCriteria;
                            $crite->compare('schoolid', $schoolVal['id']);
                            $crite->compare('category', IvyStatsItem::CATEGORY_FINANCE);
                            $crite->compare('period', $_month_);
                            $crite->compare('code', IvyStatsItem::CODE_FINANCE_ACTUAL);
                            $crite->compare('subcode', $k);
                            $model1 = IvyStatsItem::model()->find($crite);
                            $_data = rand($model1->data,($model1->data+$model1->data*0.3));
                            unset($model1);
                        }else{
                            $_data = rand($v[0], $v[1]);
                        }
                        $model = new IvyStatsItem();
                        $model->schoolid = $schoolVal['id'];
                        $model->period_type = IvyStatsItem::TYPE_M;
                        $model->period = $_month_;
                        $model->category = IvyStatsItem::CATEGORY_FINANCE;
                        $model->code = $key;
                        $model->subcode = $k;
                        $model->data = $_data;
                        $model->startyear = date('Y',$calendarList['fall_start']);
                        $model->userid = 1;
                        $model->updated = time();
                        $model->created = time();
                        if ($model->save()){
                           print_r($k.":".$_data."\n");
                        }else{
                            print_r($model->getErrors());
                        }
                    }
                }
                $_month = mktime(0,0,0,date('m',$_month)+1,1,date('Y',$_month));
            }
        }
    }

    /*TDI 测试数据输入*/
    public function actionVisitTDI()
    {
        $nowtime = time();
        $dateN = date('N');
        $monday = $nowtime - ($dateN-1)*3600*24;
        $startyear = date('m', $monday) < 9 ? date('Y', $monday)-1 : date('Y', $monday);
        $branchs = Branch::model()->getBranchList();
        IvyStatsItem::model()->deleteAllByAttributes(array('category'=>IvyStatsItem::CATEGORY_VISIT));
        foreach(array_keys($branchs) as $skey){
            for($i=0; $i<8; $i++){
                $weekmon = date('Ymd', $monday-$i*604800);
                foreach(array(IvyStatsItem::CODE_VISIT_NEW, IvyStatsItem::CODE_VISIT_SUCCEED, IvyStatsItem::CODE_VISIT_NOLOG, IvyStatsItem::CODE_VISIT_ACTIVE) as $key){
                    switch($key){
                        case IvyStatsItem::CODE_VISIT_NEW:
                            $thedata = rand(0, 50);
                            break;
                        case IvyStatsItem::CODE_VISIT_SUCCEED:
                            $thedata = rand(0, 10);
                            break;
                        case IvyStatsItem::CODE_VISIT_NOLOG:
                            $thedata = rand(0, 100);
                            break;
                        case IvyStatsItem::CODE_VISIT_ACTIVE:
                            $thedata = rand(100, 300);
                            break;
                    }
                    $model = new IvyStatsItem();
                    $model->schoolid = $skey;
                    $model->period_type = IvyStatsItem::TYPE_W;
                    $model->period = $weekmon;
                    $model->category = IvyStatsItem::CATEGORY_VISIT;
                    $model->code = $key;
                    $model->data = $thedata;
                    $model->startyear = $startyear;
                    $model->userid = 1;
                    $model->updated = $nowtime;
                    $model->created = $nowtime;
                    if ($model->save() ){
                        echo $model->id."\n";
                    }
                    else {
                        echo "失败！\n";
                    }
                }
            }
        }
    }
}