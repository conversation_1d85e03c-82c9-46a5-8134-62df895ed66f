<?php
$msType = array('e6', 'e7', 'e8', 'e9');
if (in_array($this->reserveObj->ivyclass->classtype, $msType)) {
    $sizeXType = array(
        140 => "（推荐身高：135cm-145cm）",
        150 => "（推荐身高：145cm-155cm）",
        160 => "（推荐身高：155cm-165cm）",
        165 => "（推荐身高：165cm-170cm）",
        170 => "（推荐身高：170cm-175cm）",
        175 => "（推荐身高：175cm-180cm）",
        180 => "（推荐身高：180cm-185cm）",
        185 => "（推荐身高：185cm-190cm）",
    );
    if($this->childObj->gender != 1){
        unset($sizeXType[180]);
        unset($sizeXType[185]);
    }
} else {
    $sizeXType = array(
        100 => "100 (推荐身高: 95cm-105cm)",
        110 => "110 (推荐身高: 105cm-115cm)",
        120 => "120 (推荐身高: 115cm-125cm)",
        130 => "130 (推荐身高: 125cm-135cm)",
        140 => "140 (推荐身高: 135cm-145cm)",
        150 => "150 (推荐身高: 145cm-155cm)",
        160 => "160 (推荐身高: 155cm-165cm)",
        170 => "170 (推荐身高: 165cm-175cm)",
    );
}

$productsUrlCn = $this->createUrl('/wechat/products/index', array('lang' => 'zh_cn'));
$productsUrlEn = $this->createUrl('/wechat/products/index', array('lang' => 'en_us'));
?>
<?php echo $this->renderPartial('steps_header', array('currentStep'=>3)); ?>
<div class="container">  
    <h5 class="htitle"><span class="fas fa-tshirt"></span> <?php echo $this->getStepTitle($this->step); ?></h5>
    <?php echo $this->renderPartial('_nouser_header'); ?>
    <div class="edit-content">
    <?php 
    $gender = '';
    if (in_array($this->reserveObj->ivyclass->classtype, $msType)) {
        // 中学
        $content = RegConfig::getContent($this->regStudent->reg_id, $this->step);
        if ($content) {
            if($this->childObj->gender == 1){ // 男
                $gender = 'uniform_primary_man';
            }else{
                $gender = 'uniform_primary_woman';
            }
        }
     }else { 
        // 小学
        $content = RegConfig::getContent($this->regStudent->reg_id, $this->step);
        if ($content) {
            if($this->childObj->gender == 1){ // 男
                $gender = 'uniform_man';
            }else{
                $gender = 'uniform_woman';
            }
        }
    }
    if ($gender) {
        
        $contentCn = base64_decode($content['cn'][$gender]);
        $contentEn = base64_decode($content['en'][$gender]);
        $contentCn = str_replace('http://producturl', $productsUrlCn , $contentCn);
        $contentEn = str_replace('http://producturl', $productsUrlEn , $contentEn);

        echo CommonUtils::autoLang($contentCn, $contentEn);
    }
    ?>
    </div>
    <div class="">
        <?php $form = $this->beginWidget('CActiveForm', array(
            'id' => 'sep1-form',
            'enableAjaxValidation' => false,
            'htmlOptions' => array(
                // 'class'=>'J_ajaxForm form-horizontal',
                'role' => 'form',
            ),
        ));
        ?>
        <input type="hidden" name="RegStep3Form[gender]" value="1">
<!--         <div class="form-group">
            <h5 class="sub-title">
                <span><?php echo Yii::t('reg', 'Selecting Size'); ?></span>
            </h5>
            <?php echo $form->radioButtonList($formModel3, "size", $sizeXType, array(
                'template' => '<div class="form-check">{input} {label}</div>',
                'separator' => '',
                'class'=>'form-check-input'
            ), array(
                'template' => '<div class="form-check">{input} {label}</div>',
                'separator' => '',
                'class'=>'form-check-input'
            )) ?>
        </div> -->
        <div class="mt-3">
            <button type="submit" class="btn btn-primary btn-lg btn-block examine"><?php echo Yii::t("reg", 'I fully understand'); ?></button>
            <p class="text-black-50 mt-3 text-center examine_status">

            </p>
        </div>
        <?php $this->endWidget(); ?>
    </div>
</div>