.guide{
    position: absolute;
    z-index: 10009;
    filter:alpha(opacity=50);
    -moz-opacity:0.5;
    opacity:0.5;
    background-color:#000;
}

.highlight{
    position: absolute;
    z-index: 99999;
    /*background-color: transparent;*/
    filter:alpha(opacity=10);
    -moz-opacity:0.1;
    opacity:0.1;
}
.tip{
    position: absolute;
    /*border: 2px solid white;*/
    /*background-color: white;*/
    z-index: 99999;
    width: 0;
    height: 0;
}

.galert{
    position: absolute;
    border: 4px solid red;
    z-index: 99991;
}
#main-guide{
    position: relative;
}
.tip em{
    position: absolute;
}
.tip em.up{
    width:55px;
    height:108px;
    display: block;
    background:url("arrow_up.png") no-repeat transparent;
    /*position: absolute;*/
}
.tip em.upright{
    width:91px;
    height:98px;
    display: block;
    background:url("arrow_upright.png") no-repeat transparent;
    /*position: absolute;*/
}
.tip em.upleft{
    width:91px;
    height:98px;
    display: block;
    background:url("arrow_upleft.png") no-repeat transparent;
    /*position: absolute;*/
}
.tip em.downright{
    width:91px;
    height:98px;
    display: block;
    background:url("arrow_downright.png") no-repeat transparent;
    /*position: absolute;*/
}
.guide-content{
    position: absolute;
/*    position: relative;*/
    width: 425px;
    height: 122px;
    background: url("guide24.png") no-repeat transparent;
}
.guide-content .content{
    position: absolute;
    top: 34px;
    left: 28px;
    width: 315px;
    font-size: 14px;
    line-height: 30px;
    height:60px;
    overflow-y: auto;
}
.guide-content .content h1{
    font-size: 28px;
}
.guide-content .content p{
    font-size: 16px;
}
.guide-content .content ul.list li{
    display: inline;
    margin-right: 20px;
}
.guide-content .close{
    width: 32px;
    height: 32px;
    background: url("close.png") no-repeat transparent;
    position: absolute;
    top: 16px;
    right: 50px;
    cursor: pointer;

}
.guide-content .guide-action{
    position: absolute;
    top: 330px;
    right: 180px;
}
.guide-content .guide-action span.btn001{
    margin-right: 20px;
}
.guide-content .guide-action span.prev{
    margin-right: 150px;
}