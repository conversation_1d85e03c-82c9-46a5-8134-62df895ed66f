<?php

/**
 * 合同审批工作流
 */
class Contract implements WorkflowTemplate
{
    private $template;
    private $isJumpNode = 0;
    public $controller;
    public $workflow;

    public function __construct()
    {
        Yii::import('common.models.Uploads');
        Yii::import('common.models.workflow.payment.*');
        Yii::import('common.models.workflow.contract.ContractApply');
    }

    public function initi()
    {
        $data['jsFunction'] = 'WorkflowModalShow';
        $data['modalNameId'] = 'workflow-contract-modal';
        $contractId = $this->workflow->getOperateObjId();
        if ($contractId) {
            $contractModel = ContractApply::model()->findByPk($contractId);
            $contractModel->start_date = date('Y-m-d', $contractModel->start_date);
            $contractModel->end_date = date('Y-m-d', $contractModel->end_date);
            $contractModel->price_total = round($contractModel->price_total/100, 2);
        } else {
            $contractModel = new ContractApply();
            $contractModel->start_date = '';
            $contractModel->end_date = '';
            $contractModel->price_total = '';
        }

        if (Yii::app()->request->isPostRequest) {
            $memo = Yii::app()->request->getPost('memo');
            $title = Yii::app()->request->getPost('title');
            $party_a = Yii::app()->request->getPost('party_a');
            $party_b = Yii::app()->request->getPost('party_b');
            $start_date = Yii::app()->request->getPost('start_date');
            $end_date = Yii::app()->request->getPost('end_date');
            $price_total = Yii::app()->request->getPost('price_total');
            $budget = Yii::app()->request->getPost('budget');
            $bank_user = Yii::app()->request->getPost('bank_user');
            $bank_account = Yii::app()->request->getPost('bank_account');
            $bank_name = Yii::app()->request->getPost('bank_name');
            $vendor_id = Yii::app()->request->getPost('vendor_id');
            $category = Yii::app()->request->getPost('category');
            $cycle = Yii::app()->request->getPost('cycle');

            if (!in_array($budget, array(0, 1))) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '保存失败, 原因：请选择预算。');
                $this->controller->showMessage();
            }

            if (!$memo) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '保存失败, 原因：申请备注不能为空。');
                $this->controller->showMessage();
            }
            if (!$party_a) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '保存失败, 原因：甲方不能为空。');
                $this->controller->showMessage();
            }
            if (!$party_b) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '保存失败, 原因：乙方不能为空。');
                $this->controller->showMessage();
            }
            if (!$start_date) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '保存失败, 原因：开始时间不能为空。');
                $this->controller->showMessage();
            }
            if (!$end_date) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '保存失败, 原因：结束时间不能为空。');
                $this->controller->showMessage();
            }
            if (!$price_total) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '保存失败, 原因：总价不能为空。');
                $this->controller->showMessage();
            }
            if (!$category) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '保存失败, 原因：合同类型不能为空。');
                $this->controller->showMessage();
            }
            if (!$cycle) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '保存失败, 原因：付款周期不能为空。');
                $this->controller->showMessage();
            }


            $transaction = Yii::app()->db->beginTransaction();
            try {
                $uid = $this->controller->staff->uid;
                // 查找供应商
                // 去除 $bank_account 除数字外的特殊符号
                $bank_account = preg_replace('/[^\d]/', '', $bank_account);
                $bank_name = trim($bank_name);
                $bank_user = trim($bank_user);
                $vendorModel = PaymentVendor::model()->findByAttributes(array('bank_account' => $bank_account));
                if (!$vendorModel) {
                    $vendorModel = new PaymentVendor();
                    $vendorModel->type = 1;
                    $vendorModel->state = 1;
                    $vendorModel->name = $bank_user;
                    $vendorModel->bank_name = $bank_name;
                    $vendorModel->bank_user = $bank_user;
                    $vendorModel->bank_account = $bank_account;
                    $vendorModel->created_by = Yii::app()->user->id;
                    $vendorModel->created_at = time();
                    $vendorModel->updated_by = Yii::app()->user->id;
                    $vendorModel->updated_at = time();
                    $vendorModel->save();
                }
                // 保存付款申请表
                $contractModel->school_id = $this->workflow->schoolId;
                $contractModel->budget = $budget;
                $contractModel->title = $title;
                $contractModel->party_a = $party_a;
                $contractModel->party_b = $party_b;
                $contractModel->start_date = strtotime($start_date);
                $contractModel->end_date = strtotime($end_date);
                $contractModel->price_total = $price_total * 100;
                $contractModel->state = ContractApply::INPROGRESS;
                $contractModel->vendor_id = $vendorModel->id;
                $contractModel->bank_user = $bank_user;
                $contractModel->bank_account = $bank_account;
                $contractModel->bank_name = $bank_name;
                $contractModel->created_by = $uid;
                $contractModel->category = $category;
                $contractModel->cycle = $cycle;
                $contractModel->desc = $memo;
                $contractModel->created_at = time();
                $contractModel->updated_by = $uid;
                $contractModel->updated_at = time();
                if (!$contractModel->save()) {
                    $errors = current($contractModel->getErrors());
                    throw new Exception('保存失败, 原因：合同申请表保存失败，' . current($errors));
                }
                $email = $this->controller->staff->email;
                $operationData = array('operation_type' => WorkflowConfig::WORKFLOW_TYPE_CONTRACT, 'operation_object_id' => $contractModel->id, 'defination_id' => $this->workflow->definationObj->defination_id, 'branchid' => $this->workflow->schoolId, 'state' => WorkflowOperation::WORKFLOW_STATS_UNOP, 'current_node_index' => $this->workflow->getNextNodeId(), 'exte1' => '');
                //保存工作流业务流程
                if ($this->workflow->opertationObj->id) {
                    $operationData['id'] = $this->workflow->getOperateId();
                    $operationData['exte4'] = $this->workflow->opertationObj->exte4;
                }
                $operationRet = $this->workflow->saveFirstNodeForWorkflow($operationData, array($contractModel->id), $memo, $transaction, array($uid => $email));
                if ($operationRet === false) {
                    throw new Exception('保存失败, 原因：工作流底层更新失败。');
                }
                // 更新相关附件的 link_id
                if ($files = $_POST['files']) {
                    foreach ($files as $file) {
                        $uploadModel = Uploads::model()->findByPk($file);
                        if (!$uploadModel) {
                            continue;
                        }
                        $uploadModel->link_id = $this->workflow->opertationObj->id;
                        $uploadModel->save();
                    }
                }
                $transaction->commit();
                // 发送邮件
                $message = 'success';
                if ($this->jumpMail() === false) {
                    $message = 'save success, 邮件发送失败';
                }
                $this->controller->addMessage('state', 'success');
                $this->controller->addMessage('message', $message);
                $this->controller->addMessage('refresh', true);
                $this->controller->showMessage();
            } catch (Exception $e) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', $e->getMessage());
                $this->controller->showMessage();
            }
        }
        $this->setTemplate('initi');
        $branchObj = Branch::model()->getBranchInfo($this->workflow->schoolId);
        $data['branchObj'] = $branchObj;
        $data['workflow'] = $this->workflow;
        $data['contractApplyModel'] = $contractModel;
        $data['categoryList'] = $this->getCategoryList();
        $data['cycleList'] = $this->getCycleList();
        $data['checked'] = ($this->controller->staff->profile->branch == $this->workflow->schoolId);

        return $data;
    }

    public function show()
    {
        if (($this->workflow->isFristNode() === true) && $this->workflow->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_STEP) {
            return $this->initi();
        }
        if (Yii::app()->request->isPostRequest) {
            $operationId = $this->workflow->opertationObj->id;
            $buttonCategory = Yii::app()->request->getPost('buttonCategory', 0);
            $memo = Yii::app()->request->getPost('memo', '');
            $budget = Yii::app()->request->getPost('budget', '');
            $paymentFlowId = Yii::app()->request->getPost('paymentFlowId', 0);
            if (!in_array($budget, array(0, 1))) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '保存失败, 原因：请选择预算。');
                $this->controller->showMessage();
            }
            if (!$buttonCategory) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '保存失败, 原因：请选择操作。');
                $this->controller->showMessage();
            }
            if (!$memo) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '保存失败, 原因：申请备注不能为空。');
                $this->controller->showMessage();
            }
            if ($this->workflow->nodeObj->field2 == 'finance' && $this->workflow->definationObj->display == 1) {
                if (!$paymentFlowId) {
                    $this->controller->addMessage('state', 'fail');
                    $this->controller->addMessage('message', '保存失败，原因：请选择付款工作流！');
                    $this->controller->showMessage();
                }
            }

            switch ($buttonCategory) {
                case WorkflowOperation::WORKFLOW_STATS_RESET:
                    $process = 'reset';
                    break;
                case WorkflowOperation::WORKFLOW_STATS_OPED:
                    $process = 'pass';
                    break;
                case WorkflowOperation::WORKFLOW_STATS_OPDENY:
                    $process = 'reject';
                    break;
                default:
                    $process = '';
                    break;
            }
            if ($process == '') {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '保存失败，原因：请选择！');
                $this->controller->showMessage();
            }

            $requestUrl = "workflow/process/$process";
            $requestData = array(
                'operationId' => $operationId,
                'budget' => $budget,
                'paymentFlowId' => $paymentFlowId,
                'desc' => $memo,
            );
            $res = CommonUtils::requestDsOnline($requestUrl, $requestData);
            if ($res['code'] == 0) {
                $this->controller->addMessage('state', 'success');
                $this->controller->addMessage('message', $res['msg']);
                $this->controller->addMessage('refresh', true);
            } else {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', '工作流保存异常：' . $res['msg']);
                $this->controller->addMessage('data', $res['data']);
            }
            $this->controller->showMessage();
        }

        $this->setTemplate('show');
        $data['jsFunction'] = 'WorkflowModalShow';
        $data['modalNameId'] = 'workflow-contract-modal';
        //查找相关的采购商品、标配
        $oDetail = WorkflowOperationDetail::model()->find('operation_id=:operation_id', array(':operation_id' => $this->workflow->opertationObj->id));
        $contractApply = ContractApply::model()->findByPk($oDetail->operation_object_id);
        $isFinance = $this->workflow->nodeObj->field2 == 'finance';
        if (empty($contractApply)) {
            throw new Exception('工作流程业务参数错误！', 500);
        }


        $branchObj = Branch::model()->getBranchInfo($this->workflow->schoolId);
        $data['branchObj'] = $branchObj;
        $data['isFinance'] = $isFinance;
        $data['contractApplyModel'] = $contractApply;
        $data['workflow'] = $this->workflow;
        if ($this->workflow->definationObj->display == 1 && $isFinance) {
            // $data['flowList'] = $this->getFlowList();
            $isHq = false;
            if (in_array($this->workflow->schoolId, (array)CommonUtils::LoadConfig('CfgOffice'))) {
                $isHq = true;
            }
            $data['paymentFlowList'] = $this->getPaymentFlowList($isHq);
        }
        $data['categoryList'] = $this->getCategoryList();
        $data['cycleList'] = $this->getCycleList();
        if ($contractApply->state == 1) {
            $paidData = ContractApply::getPaymentList($contractApply->id);
            $data['paidData'] = $paidData;
        }
        return $data;
    }


    public function jumpMail()
    {

        $this->workflow->wechatPush();

        $branchList = Branch::model()->getBranchList();
        $branchTitle = $branchList[$this->workflow->getOperateValue('branchid')];
        $applyName = $this->workflow->opertationObj->userInfo->getName();
        $workflowTitle = CommonUtils::autoLang($this->workflow->definationObj->defination_name_cn, $this->workflow->definationObj->defination_name_en);
        $nodesTitle = CommonUtils::autoLang($this->workflow->nodesList[$this->workflow->getNextNodeId()]->node_name_cn, $this->workflow->nodesList[$this->workflow->getNextNodeId()]->node_name_en);
        // 判断是否为跳节点
        if ($this->isJumpNode) {
            $nodesTitle = CommonUtils::autoLang(end($this->workflow->nodesList)->node_name_cn, end($this->workflow->nodesList)->node_name_en);
        }
        if ($this->workflow->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_OPDENY) {
            $subject = sprintf('%s 校园 %s 提出 %s申请被拒绝并作废', $branchTitle, $applyName, $workflowTitle);
        } elseif ($this->workflow->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_RESET) {
            $subject = sprintf('%s 校园 %s 提出 %s申请被退回修改', $branchTitle, $applyName, $workflowTitle);
        } else {
            if ($this->workflow->isEndNode()) {
                $subject = sprintf('%s 校园 %s 提出 %s申请已完成', $branchTitle, $applyName, $workflowTitle);
            } else {
                $subject = sprintf('%s 校园 %s 提出 %s申请需要您处理', $branchTitle, $applyName, $workflowTitle);
            }
        }

        $link = $this->controller->createUrl('/workflow/index', array('branchId' => $this->workflow->schoolId));
        $body = $this->controller->renderPartial('/mail/templater', array('branchTitle' => $branchTitle, 'childName' => $applyName, 'workflowTitle' => $workflowTitle, 'nodes' => $nodesTitle, 'link' => $link, 'workflow' => $this->workflow), true);

        if ($this->isJumpNode) {
            $jumpNode = end($this->workflow->nodeIds);
            return $this->workflow->sendEmail($subject, $body, false, $jumpNode);
        }

        if ($this->workflow->sendEmail($subject, $body)) {
            return true;
        }

        return false;
    }


    //实现接口方法
    public function setTemplate($name)
    {
        $this->template = $name;
    }

    //实现接口方法
    public function getTemplate()
    {
        return $this->template;
    }

    //删除工作流
    public function delete()
    {
        $request = Yii::app()->request;
        if ($this->workflow->getOperateStatus() == WorkflowOperation::WORKFLOW_STATS_STEP) {
            $transaction = Yii::app()->db->beginTransaction();
            try {
                $contractId = $this->workflow->getOperateObjId();
                if ($contractId) {
                    ContractApply::model()->updateByPk($contractId, array('state' => ContractApply::CANCEL));
                }
                if (!$this->workflow->deleteOperationNode($transaction)) {
                    $this->controller->addMessage('state', 'fail');
                    $this->controller->addMessage('message', "工作流底层删除失败!");
                    $this->controller->showMessage();
                }
                $transaction->commit();
                $this->controller->addMessage('state', 'success');
                $this->controller->addMessage('message', '工作流删除成功');
                $this->controller->addMessage('data', $this->workflow->getOperateId());
                $this->controller->addMessage('callback', 'deleteWorkflowOperator');
                $this->controller->showMessage();
            } catch (Exception $ex) {
                $transaction->rollBack();
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', $ex->getMessage());
                $this->controller->showMessage();
            }
        }
    }

    /**
     * 保存上传的附件
     * @return [type] [description]
     */
    public function saveUploadFile()
    {
        if ($file = CUploadedFile::getInstanceByName('file')) {
            if ($file->size > 10 * 1024 * 1024) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', $file->name . '上传失败，最大10m。');
                $this->controller->showMessage();
            }
            $ext = strtolower($file->getExtensionName());
            $needType = array('jpg', 'png', 'pdf', 'gif', 'jpeg', 'doc', 'docx', 'xls', 'xlsx', 'tif', 'tiff');
            if (!in_array($ext, $needType)) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message', $file->name . '上传失败，资料类型出错。');
                $this->controller->showMessage();
            }
            $datePath = date('Y') . '/' . date('m') . '/';
            $filePath = Yii::app()->params['xoopsVarPath'] . '/uploads/' . $datePath;
            if (!is_dir($filePath)) {
                mkdir($filePath, 0777, true);
            }
            $baseUrl = Yii::app()->params['OABaseUrl'] . "/modules/myspace/task/getthumb.php?img=";
            $fileName = uniqid() . '.' . $ext;
            if (!$file->saveAs($filePath . $fileName)) {
                $this->controller->addMessage('state', 'fail');
                $this->controller->addMessage('message',  $file->name . '资料保存失败。');
                $this->controller->showMessage();
            }
            //保存表ivy_uploads
            $uploadModel = new Uploads();
            $uploadModel->link_id = $this->workflow->opertationObj->id;
            $uploadModel->mod_name = 'workflow';
            $uploadModel->func_name = 'contract';
            $uploadModel->file_name = $datePath . $fileName;

            $uploadModel->notes = pathinfo($file->name, PATHINFO_FILENAME);
            $uploadModel->update_time = time();
            $uploadModel->update_user = Yii::app()->user->id;
            if (!$uploadModel->save()) {
                unlink($filePath . $fileName);
                $error = current($uploadModel->getErrors());
                $this->controller->addMessage('message', $file->name . '保存失败: ' . $error[0]);
                $this->controller->showMessage();
            }
            $this->controller->addMessage('data', array('fileId' => $uploadModel->id, 'fileName' => $uploadModel->notes, 'url' => $baseUrl . $uploadModel->file_name));
            $this->controller->addMessage('state', 'success');
            $this->controller->addMessage('message', '资料上传成功。');
            $this->controller->showMessage();
        }
        Yii::app()->end();
    }

    /**
     * 删除附件
     * @return [type] [description]
     */
    public function deleteUploadFile()
    {
        if (Yii::app()->request->isPostRequest) {
            $id = Yii::app()->request->getPost('id', 0);
            if ($uploadModel = Uploads::model()->findByPk($id)) {
                //删除资料
                $fileName = $uploadModel->file_name;
                $filePath = Yii::app()->params['xoopsVarPath'] . '/uploads/';
                if ($uploadModel->delete()) {
                    unlink($filePath . $fileName);
                    $this->controller->addMessage('state', 'success');
                    $this->controller->addMessage('message', '资料删除成功。');
                    $this->controller->showMessage();
                }
            }
        }
        $this->controller->addMessage('state', 'fail');
        $this->controller->addMessage('message', '资料删除失败。');
        $this->controller->showMessage();
    }

    /**
     * 修改附件显示名
     * @param string $value [description]
     */
    public function changeFileName()
    {
        if (Yii::app()->request->isPostRequest) {
            $id = Yii::app()->request->getPost('id', 0);
            $notes = Yii::app()->request->getPost('notes', 0);
            if ($notes && $uploadModel = Uploads::model()->findByPk($id)) {
                $uploadModel->notes = $notes;
                if ($uploadModel->save()) {
                    $this->controller->addMessage('state', 'success');
                    $this->controller->addMessage('message', '资料修改成功。');
                    $this->controller->showMessage();
                }
            }
        }
        $this->controller->addMessage('state', 'fail');
        $this->controller->addMessage('message', '资料修改失败。');
        $this->controller->showMessage();
    }

    public function getFlowList()
    {
        return WorkflowDefination::model()->findAllByAttributes(array('defination_handle' => 'Contract', 'display' => 0, 'status' => 1));
    }

    public function getPaymentFlowList($isHq = false)
    {
        $handleName = 'Payment';
        $display = 1;
        if ($isHq) {
            $handleName = 'PaymentHq';
            $display = 0;
        }
        return WorkflowDefination::model()->findAllByAttributes(array('defination_handle' => $handleName, 'display' => $display, 'status' => 1));
    }

    public function getCategoryList()
    {
        return ContractApply::getCategoryList();
    }

    public function getCycleList()
    {
        return ContractApply::getCycleList();
    }
}
