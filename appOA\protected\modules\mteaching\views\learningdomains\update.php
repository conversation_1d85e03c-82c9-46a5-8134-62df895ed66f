<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
    </button>
    <h4 class="modal-title" id="exampleModalLabel"><?php echo $model->id ? Yii::t('global', 'Update') : Yii::t('global', 'Add'); ?>：</h4>
</div>
<?php $form = $this->beginWidget('CActiveForm', array(
    'id' => 'learning-form',
    'enableAjaxValidation' => false,
    'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form'),
)); ?>
<div class="modal-body">
    <div class="form-group">
        <?php echo $form->labelEx($model, 'title_cn', array('class' => 'col-xs-2 control-label')); ?>
        <div class="col-xs-8">
            <?php echo $form->textField($model, 'title_cn', array('class' => 'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <?php echo $form->labelEx($model, 'title_en', array('class' => 'col-xs-2 control-label')); ?>
        <div class="col-xs-8">
            <?php echo $form->textField($model, 'title_en', array('class' => 'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <?php echo $form->labelEx($model, 'type', array('class' => 'col-xs-2 control-label')); ?>
        <div class="col-xs-8">
            <?php echo $form->dropDownList($model, 'type', $typeList, array('class' => 'form-control', 'empty' => Yii::t('global','Please Select'))); ?>
        </div>
    </div>
    <div class="form-group">
        <?php echo $form->labelEx($model, 'sort', array('class' => 'col-xs-2 control-label')); ?>
        <div class="col-xs-8">
            <?php echo $form->textField($model, 'sort', array('class' => 'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <?php echo $form->labelEx($model, 'intro_cn', array('class' => 'col-xs-2 control-label')); ?>
        <div class="col-xs-8">
            <?php echo $form->textArea($model, 'intro_cn', array('class' => 'form-control', 'rows' => '5')); ?>
        </div>
    </div>
    <div class="form-group">
        <?php echo $form->labelEx($model, 'intro_en', array('class' => 'col-xs-2 control-label')); ?>
        <div class="col-xs-8">
            <?php echo $form->textArea($model, 'intro_en', array('class' => 'form-control', 'rows' => '5')); ?>
        </div>
    </div>
</div>
<div class="modal-footer">
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
</div>
<?php $this->endWidget(); ?>