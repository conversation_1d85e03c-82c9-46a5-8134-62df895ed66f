<?php
class FeedbackComment extends CWidget{
    public $classId;
    public $weekNum;
    public $cssFile;
    public $assetsUrl;
	
	public function init(){
		Yii::import('common.models.feedback.Comments');

//        if($this->assetsUrl===null)
//			$this->assetsUrl=Yii::app()->getAssetManager()->publish(dirname(__FILE__) . '/assets', false, -1, YII_DEBUG);
//
//        $cs = Yii::app()->clientScript;
//        if (false !== $this->cssFile)
//        {
//            if (null === $this->cssFile)
//                $this->cssFile = $this->assetsUrl . '/css.css';
//            $cs->registerCssFile($this->cssFile);
//        }
	}
	
	public function run(){
        
        if ($this->classId && $this->weekNum){
            Mims::LoadHelper('HtoolKits');

            Yii::import('common.models.classTeacher.InfopubStaffExtend');

            $criteria = new CDbCriteria();
            $criteria->compare('com_child_id', $this->getController()->childid);
            $criteria->compare('com_class_id', $this->classId);
            $criteria->compare('com_week_num', $this->weekNum);
            $criteria->order='com_created_time ASC';
            $items = Comments::model()->with('userWithProfile', 'staffInfo')->findAll($criteria);
            $comments = array();
            $comments_replay = array();
            $reply = array();
            foreach ($items as $item){
                $comments[$item->id] = $item->attributes;
                $comments[$item->id]['name'] = $item->userWithProfile->getName();
                if ($item->com_user_type == 1){
                    $teacherphoto = isset($item->staffInfo->staff_photo) && $item->staffInfo->staff_photo ? $item->staffInfo->staff_photo : 'blank.gif';
                }
                $comments[$item->id]['photo'] = $item->com_user_type == 1 ? Mims::CreateUploadUrl('infopub/staff/'.$teacherphoto) : $item->userWithProfile->getPhotoSubUrl();
            }

            foreach ($comments as $cid=>$comment){
                if ($comment['com_root_id'] == 0){
                    $comments_replay[$cid] = $comment;
                }
                else {
                    $reply[$comment['com_root_id']][$cid] = $comment;
                }
            }

            $model=new Comments();

            $this->render("comments", array('reply'=>$reply, 'comments_replay'=>$comments_replay, 'model'=>$model));
        }
	}
	
}