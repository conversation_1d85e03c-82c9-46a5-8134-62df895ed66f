<?php
/**
 * Created by PhpStorm.
 * User: XINRAN
 * Date: 14-3-20
 * Time: 下午1:50
 */
?>
<div class="modal fade" id="loginModal" tabindex="-1" role="dialog" aria-labelledby="ModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="ModalLabel">艾毅幼儿园家长登录</h4>
            </div>
            <div class="modal-body">
                <form class="form-horizontal" role="form" id="loginForm">
                    <div id="auth-box">
                        <div class="form-group">
                            <label for="username" class="col-sm-2 control-label hidden-xs">家长帐号</label>
                            <div class="col-sm-10">
                                <input type="text" class="form-control" id="username" name="UserLogin[username]" placeholder="请输入家长帐号">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="password" class="col-sm-2 control-label hidden-xs">密码</label>
                            <div class="col-sm-10">
                                <input type="password" class="form-control" id="password" name="UserLogin[password]" placeholder="请输入密码">
                            </div>
                        </div>
                    </div>

                    <div class="form-group sr-only" id="form-status">
                        <div class="col-sm-offset-2 col-sm-10">
                            <div class="alert alert-danger"></div>
                        </div>
                    </div>

                    <div id="clubuname-box" class="sr-only">
                        <div class="form-group" id="form-uname">
                            <label for="uname" class="col-sm-2 hidden-xs control-label">显示名</label>
                            <div class="col-sm-10">
                                <input type="text" class="form-control" id="uname" name="ClubUsers[uname]" placeholder="请输入显示名">
                            </div>
                        </div>
                        <div class="form-group sr-only" id="form-uname-status">
                            <div class="col-sm-offset-2 col-sm-10">
                                <div class="alert alert-danger"></div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-sm-offset-2 col-sm-10">
                            <button type="button" class="btn btn-primary" id="signbtn">登录</button>
                            <button type="button" class="btn btn-primary sr-only" id="submitbtn">提交</button>
                        </div>
                    </div>
                </form>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<script>

    $('input#username, input#password').keyup(function(event){
        if(event.keyCode==13){
            $('#signbtn').click();
        }
    });
    $('input#uname').keyup(function(event){
        if(event.keyCode==13){
            $('#submitbtn').click();
        }
    });

    $('#signbtn', '#loginModal').on('click', function(){
        $.ajax({
            type: 'POST',
            url: '<?php echo $this->createUrl('//user/login/ajaxLogin', array('checkUname'=>1));?>',
            data: $('#loginForm').serialize(),
            dataType: 'json',
            beforeSend: function( xhr ){
                $('div.form-group').removeClass('has-error');
            }
        }).done(function(data){
            if (data.state == 'fail'){
                for(var datum in data['msg']){
                    if ( datum == 'status' ){
                        $('#form-status').removeClass('sr-only');
                        $('#form-status .alert').html(data['msg'][datum]);
                    }
                    else{
                        $('#form-status').addClass('sr-only');
                        $('#'+datum).parents('div.form-group').addClass('has-error');
                    }
                }
            }
            else if (data.state == 'pass'){
                $('input#uname').focus();
                $('#auth-box').addClass('sr-only');
                $('#signbtn').addClass('sr-only');

                $('#clubuname-box,#form-status').removeClass('sr-only');
//                $('#form-status').removeClass('sr-only');
                $('#form-status .alert').html(data.msg).removeClass('alert-danger').addClass('alert-success');
                $('#submitbtn').removeClass('sr-only');

                $('#username').addClass('disabled').attr('disabled', true);
                $('#password').addClass('disabled').attr('disabled', true);
            }
            else if (data.state == 'success'){
                window.location.reload();
            }
        });
    });
    $('#submitbtn', '#loginModal').on('click', function(){
        $.ajax({
            type: 'POST',
            url: '<?php echo $this->createUrl('//user/login/uname');?>',
            data: {uname: $('#uname').val()},
            dataType: 'json'
        }).done(function(data){
            if(data.state == 'success'){
                window.location.reload();
            }
            else{
                $('#form-uname-status').removeClass('sr-only');
                $('#form-uname-status .alert').html(data.msg);
            }
        });
    });
    function openLoginModel(){
        $('#loginModal').modal({
            keyboard: false,
            backdrop: 'static'
        });
    }
</script>