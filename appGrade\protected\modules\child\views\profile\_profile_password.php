<?php
	foreach(Yii::app()->user->getFlashes() as $key => $message) {
        echo '<div class="flash-' . $key . '">' . $message . "</div>\n";
    }
?>
<style>
    .warningContent{
        color: #8a6d3b;
        background-color: #fcf8e3;
        border-color: #faebcc;
        padding: 15px;
        font-size: 12px;
        margin-bottom: 20px;
        border: 1px solid transparent;
        border-radius: 4px;
    }
</style>
<div class="form home_bg">
<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'password-form',
	'enableClientValidation'=>true,
	'clientOptions'=>array(
		'validateOnSubmit'=>true,
	),
)); ?>
    <script>
        var toChangeEmail = false;
        function clearEmailChange(){
            $('#User_changeEmail').val('');
        }
    </script>
	<?php //echo CHtml::errorSummary($model); ?>

        <div style="display:none">
            <input name="fakepassword" id="fakepassword" type="password" />
        </div>

        <dl class="row">
            <dt>
            <?php echo CHtml::activeLabelEx($model, 'changePassword'); ?>
            </dt>
            <dd>
                <?php echo CHtml::activePasswordField($model, 'changePassword'); ?>
                <?php echo CHtml::error($model, 'changePassword'); ?>
                <p class="hint" id="hint_pass1">
                    <?php echo Yii::t("userinfo", "Leave blank if no change."); ?>
                    <span>
                        <?php echo Yii::t("user", "Combination of letters and numbers, min 8-length."); ?>
                    </span>
                </p>

            </dd>
        </dl>
        <dl class="row">
            <dt>
            <?php echo CHtml::activeLabelEx($model, 'verifyPassword'); ?>
            </dt>
            <dd>
                <?php echo CHtml::activePasswordField($model, 'verifyPassword'); ?>
                <p class="hint hint-error" id="hint_pass2">
                    <span class="hide">
                        <?php echo Yii::t("user", "Password do not match."); ?>
                    </span>
                </p>
                <?php echo CHtml::error($model, 'verifyPassword'); ?>
            </dd>
        </dl>
	
	<dl class="row submit">
		<dd>
		<?php if(Yii::app()->user->checkAccess('editProfile',array("childid"=>$this->getChildId()))){
			echo CHtml::submitButton(Yii::t("global", "Save"),array("class"=>"w100 bigger"));
		}else{
			echo CHtml::submitButton(Yii::t("global", "Save"),array("class"=>"w100 bigger","disabled"=>"disabled"));
		}
		
		?>
		</dd>
	</dl>
	
	
	
<?php $this->endWidget(); ?>
</div>

<script lang="text/javascript">

    $(document).ready(function() {

        var pass1 = $('#User_changePassword'),
        pass2 = $('#User_verifyPassword'),
        submit = $('#submit_profile');

        $('.form input:password').val('');
        pass1.keyup(function(){

            var showError = false;
            var pv = pass1.val();
            if(pv.length >7){
                var num = new RegExp(/.*[0-9]+/).test(pv) ? true : false;
                var let = new RegExp(/.*[a-zA-Z]+/).test(pv) ? true : false;
                if(num == true && let == true){
                    pass2.removeAttr('disabled');
                    $('#hint_pass1').removeClass('hint-error');
                }else{
                    showError = true;
                }
            }else{
                showError = true;
                if(pv.length == 0){
                    pass2.val('');
                    submit.removeAttr('disabled');
                    showError = false;
                }
            }
            if(showError == true){
                pass2.attr('disabled','true');
                $('#hint_pass1').addClass('hint-error');
                submit.attr('disabled','true');
            }

        });

        pass2.keyup(function(){
            if(pass2.val() == pass1.val()){
                submit.removeAttr('disabled');
                $('#hint_pass2 span').addClass('hide');
            }
            else{
                submit.attr('disabled','true');
                $('#hint_pass2 span').removeClass('hide');
            }
        });


    });


</script>