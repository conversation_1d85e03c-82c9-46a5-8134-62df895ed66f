<?php
Yii::import('common.models.visit.*');
$class_list = $child->getConfig();
if (!$childinterview) {
    $childinterview = new ChildInterview();
}
$options = array(
    'a' => 'A',
    'b' => 'B',
    'c' => 'C',
    'd' => 'D',
);

$form = $this->beginWidget('CActiveForm', array(
    'id' => 'child_interivew',
    'enableAjaxValidation' => false,
    'action' => $this->createUrl('interviewResults', array('childid' => $child->id)),
    'htmlOptions' => array('class' => 'J_ajaxForm', 'role' => 'form'),
));

$select_interview_results = array(
    AdmissionsDs::STATS_HAS_BEEN_PAID,
    AdmissionsDs::STATS__INTERVIEW_PASS,
    AdmissionsDs::STATS__INTERVIEW_OUT,
    AdmissionsDs::STATS__INTERVIEW_ALTERNATE,
);
$results = array();
if ($childinterview->discipline_stat) {
    $results = CJSON::decode($childinterview->discipline_stat);
}
$cfgs = AdmissionsDs::getConfig();
?>

<div class="modal-body">
    <table class="table table-bordered">
        <h3 class="text-left"><?php echo Yii::t('interview', 'Current Class'); ?>
            ：<?php echo $class_list['grade'][$child->start_grade] ?></h3>

        <h3 class="text-center"><?php echo Yii::t('interview', 'Assignment Student Card'); ?></h3><br/>
    </table>
    <div class="table-responsive">
        <table class="table table-bordered">
        </table>
    </div>
    <table class="table table-bordered">
        <tbody>
        <tr>
            <td width="280" colspan="2"><h4><?php echo Yii::t('user', 'Child Name') ?>：<?php echo $child->getName() ?></h4></td>
            <td width="280"><h4><?php echo Yii::t('user', '国籍') ?>：<?php echo $cfgs['countries'][$child->nationality] ?></h4>
                </td>
            <td width="280" colspan="3"><h4><?php echo Yii::t('interview', 'Gender') ?>：<?php echo ($child->gender == 1) ? '男' : '女'; ?></h4>
            </td>
        </tr>
        <tr>
            <th style="width: 170px"><h4
                    style="display: inline-block"><?php echo Yii::t('interview', 'content of interview') ?>：</h4>
            </th>
            <th style="width: 170px">
                <h4 class="pull-right"><?php echo Yii::t('interview', 'Chinese Teacher') ?></h4>
            </th>
            <th style="width: 170px">
                <h4 class="pull-right"><?php echo Yii::t('interview', 'English Teacher') ?></h4>
            </th>
            <th style="width: 170px">
                <h4 class="pull-right"><?php echo Yii::t('interview', 'LEAP/CSL Teacher') ?></h4>
            </th>
        </tr>
        <tr>
            <td>
                <h4 style="display: inline-block"><?php echo Yii::t('interview', 'Speaking Level') ?></h4>
            </td>
            <td>
                <div class="radio-inline pull-right">
                    <?php echo CHtml::textField("interview[speakingC]", ($results) ? $results['speakingC'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                </div>
            </td>
            <td>
                <div class="radio-inline pull-right">
                    <?php echo CHtml::textField("interview[speakingE]", ($results) ? $results['speakingE'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                </div>
            </td>
            <td>
                <div class="radio-inline pull-right">
                    <?php echo CHtml::textField("interview[speakingL]", ($results) ? $results['speakingL'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                </div>
            </td>
        </tr>
        <tr>
            <td>
                <h4 style="display: inline-block"><?php echo Yii::t('interview', 'Listening Level') ?></h4>
            </td>
            <td>
                <div class="radio-inline pull-right">
                    <?php echo CHtml::textField("interview[listeningC]", ($results) ? $results['listeningC'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                </div>
            </td>
            <td>
                <div class="radio-inline pull-right">
                    <?php echo CHtml::textField("interview[listeningE]", ($results) ? $results['listeningE'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                </div>
            </td>
            <td>
                <div class="radio-inline pull-right">
                    <?php echo CHtml::textField("interview[listeningL]", ($results) ? $results['listeningL'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                </div>
            </td>
        </tr>
        <tr>
            <td>
                <h4 style="display: inline-block"><?php echo Yii::t('interview', 'Reading Level') ?></h4>
            </td>
            <td>
                <div class="radio-inline pull-right">
                    <?php echo CHtml::textField("interview[interviewC]", ($results) ? $results['interviewC'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                </div>
            </td>
            <td>
                <div class="radio-inline pull-right">
                    <?php echo CHtml::textField("interview[interviewE]", ($results) ? $results['interviewE'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                </div>
            </td>
            <td>
                <div class="radio-inline pull-right">
                    <?php echo CHtml::textField("interview[interviewL]", ($results) ? $results['interviewL'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                </div>
            </td>
        </tr>
        <tr>
            <td>
                <h4 style="display: inline-block"><?php echo Yii::t('interview', 'Writing Level') ?></h4>
            </td>
            <td>
                <div class="radio-inline pull-right">
                    <?php echo CHtml::textField("interview[writingC]", ($results) ? $results['writingC'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                </div>
            </td>
            <td>
                <div class="radio-inline pull-right">
                    <?php echo CHtml::textField("interview[writingE]", ($results) ? $results['writingE'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                </div>
            </td>
            <td>
                <div class="radio-inline pull-right">
                    <?php echo CHtml::textField("interview[writingL]", ($results) ? $results['writingL'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                </div>
            </td>
        </tr>
        <tr>
            <td>
                <h4 style="display: inline-block"><?php echo Yii::t('interview', 'Math Level') ?></h4>
            </td>
            <td>
                <div class="radio-inline pull-right">
                    <?php echo CHtml::textField("interview[mathC]", ($results) ? $results['mathC'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                </div>
            </td>
            <td>
                <div class="radio-inline pull-right">
                    <?php echo CHtml::textField("interview[mathE]", ($results) ? $results['mathE'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                </div>
            </td>
            <td>
                <div class="radio-inline pull-right">
                    <?php echo CHtml::textField("interview[mathL]", ($results) ? $results['mathL'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                </div>
            </td>
        </tr>
        <tr>
            <td>
                <h4 style="display: inline-block"><?php echo Yii::t('interview', 'Behavior') ?></h4>
            </td>
            <td>
                <div class="radio-inline pull-right">
                    <?php echo CHtml::textField("interview[behaviorC]", ($results) ? $results['behaviorC'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                </div>
            </td>
            <td>
                <div class="radio-inline pull-right">
                    <?php echo CHtml::textField("interview[behaviorE]", ($results) ? $results['behaviorE'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                </div>
            </td>
            <td>
                <div class="radio-inline pull-right">
                    <?php echo CHtml::textField("interview[behaviorL]", ($results) ? $results['behaviorL'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                </div>
            </td>
        </tr>
        <tr>
            <td>
                <h4 style="display: inline-block"><?php echo Yii::t('interview', 'Effort') ?></h4>
            </td>
            <td>
                <div class="radio-inline pull-right">
                    <?php echo CHtml::textField("interview[effortC]", ($results) ? $results['effortC'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                </div>
            </td>
            <td>
                <div class="radio-inline pull-right">
                    <?php echo CHtml::textField("interview[effortE]", ($results) ? $results['effortE'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                </div>
            </td>
            <td>
                <div class="radio-inline pull-right">
                    <?php echo CHtml::textField("interview[effortL]", ($results) ? $results['effortL'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                </div>
            </td>
        </tr>
        <tr>
            <td>
                <h4 style="display: inline-block"><?php echo Yii::t('interview', 'Positive Peer Relationship') ?></h4>
            </td>
            <td>
                <div class="radio-inline pull-right">
                    <?php echo CHtml::textField("interview[positiveC]", ($results) ? $results['positiveC'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                </div>
            </td>
            <td>
                <div class="radio-inline pull-right">
                    <?php echo CHtml::textField("interview[positiveE]", ($results) ? $results['positiveE'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                </div>
            </td>
            <td>
                <div class="radio-inline pull-right">
                    <?php echo CHtml::textField("interview[positiveL]", ($results) ? $results['positiveL'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                </div>
            </td>
        </tr>
        <tr>
            <td>
                <h4 style="display: inline-block"><?php echo Yii::t('interview', 'Other Comment') ?></h4>
            </td>
            <td>
                <div class="radio-inline pull-right">
                    <?php echo CHtml::textField("interview[otherC]", ($results) ? $results['otherC'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                </div>
            </td>
            <td>
                <div class="radio-inline pull-right">
                    <?php echo CHtml::textField("interview[otherE]", ($results) ? $results['otherE'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                </div>
            </td>
            <td>
                <div class="radio-inline pull-right">
                    <?php echo CHtml::textField("interview[otherL]", ($results) ? $results['otherL'] : "", array('class' => "form-control", 'style' => 'width:100%')) ?>
                </div>
            </td>
        </tr>
        <tr>
            <td colspan="2" class="text-center">Academics	Descriptors:</td>
            <td colspan="2" class="text-center">Non- Academic	Descriptors:</td>
        </tr>
        <tr>
            <td colspan="2" class="text-left">
                E : <?php echo Yii::t('interview', 'Exceeds grade level standards') ?><br/>P : <?php echo Yii::t('interview', 'Proficient in grade level standards') ?><br/>D : <?php echo Yii::t('interview', 'Developing progress in grade level standards') ?><br/>B : <?php echo Yii::t('interview', 'Beginning progress in grade level standards') ?>
            </td>
            <td colspan="2" class="text-left">
                C :  <?php echo Yii::t('interview', 'Consistently display desired behavior') ?><br/> O : <?php echo Yii::t('interview', 'Often displays desired behavior') ?><br/>I : <?php echo Yii::t('interview', 'Improvement needed') ?>
            </td>
        </tr>
        </tbody>
    </table>
    <?php
    if (Yii::app()->user->checkAccess('o_A_Adm_Visits') && $child->status != AdmissionsDs::STATS__TRANSFER_CHILD): ?>
        <div class="form-group"><?php echo Yii::t('interview', 'Whether to pass the interview') ?>:
            <label for="exampleInputEmail1">
                <?php echo CHtml::radioButtonList('stat', $child->status, array('41' => Yii::t('user', 'No'), '40' => Yii::t('user', 'Yes'), '42' => Yii::t('user', '候补')), array('separator' => '&nbsp;&nbsp;')); ?>
        </div>
    <?php endif; ?>
</div>

<div class="modal-footer">
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit') ?></button>
    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel') ?></button>
</div>
<?php $this->endWidget(); ?>
