<style>
#model{
    position: fixed;
    left: 50%;
    top: 30%;
    margin-left: -300px;
    display:none
}
</style>
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <h1 class="page-header">
                <span class="glyphicon glyphicon-cog"></span> 生成账单 Service Info
            </h1>
        </div>
    </div>
    <div class="row">
        <form action="<?php echo $this->createUrl('service'); ?>" method="get">
            <div class="col-lg-12 form-inline mb10">
                <!-- 名字 -->
                <div class="form-group">
                    <input type="text" class="form-control" name="invoiceid" placeholder="账单ID" value="<?php echo $invoiceid ? $invoiceid:''; ?>">
                </div>
                <!-- 内容匹配 -->
                <div class="form-group">
                    <button class="btn btn-default" type="submit"><span class="glyphicon glyphicon-search"> </span> </button>
                    <!--<a href="javascript:void(0);" onclick="exportInfo()" class="btn btn-info" target="_blank"><?php /*echo Yii::t('user', 'Export');*/?></a>-->
                </div>
            </div>
        </form>

        <div class="col-md-12">
            <?php if($invoiceid):?>
                <h2>
                    <?php echo $data['name']?> Service Info：<?php echo $data['credit']?> <button onclick="addservice()">新增</button>
                </h2>
                <div class="table-responsive">
                    <table class="table table-hover table-bordered">
                        <thead>
                        <tr>
                            <th width="20">#</th>
                            <th width="100">账单ID</th>
                            <th width="100">类型</th>
                            <th width="100">开始时间</th>
                            <th width="100">结束时间</th>
                            <th width="100">账单开始时间</th>
                            <th width="100">账单结束时间</th>
                            <th width="100">学生id - 学生姓名</th>
                            <th width="100">班级 - 班级名称</th>
                            <th width="100">学校</th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php
                        if($serviceInfoModel):
                            $total = 0;
                            foreach($serviceInfoModel as $key=>$item):
                                ?>
                                <tr>
                                    <th width="20">#</th>
                                    <th width="100"><?php echo $item->invoice_id ?></th>
                                    <th width="100"><?php echo $item->payment_type ?></th>
                                    <th width="100"><?php echo date("Y-m-d", $item->startdate) ?></th>
                                    <th width="100"><?php echo date("Y-m-d", $item->enddate) ?></th>
                                    <th width="100"><?php echo date("Y-m-d", $invoiceModel->startdate) ?></th>
                                    <th width="100"><?php echo date("Y-m-d", $invoiceModel->enddate) ?></th>
                                    <th width="100"><?php echo $item->childid . ' - ' . $item->childInfo->getChildName()?></th>
                                    <th width="100"><?php echo $item->classid . ' - ' . $item->classInfo->title ?></th>
                                    <th width="100"><?php echo $item->schoolid ?></th>
                                </tr>
                                <?php
                            endforeach;
                        endif;
                        ?>
                        </tbody>
                    </table>
                </div>
            <?php endif;?>
        </div>
    </div>
</div>
<div id='model'>
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="cancel()"><span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="exampleModalLabel">新增</h4>
            </div>
            <?php $form = $this->beginWidget('CActiveForm', array(
                'id' => 'course-desc',
                'enableAjaxValidation' => false,
                'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form'),
                'action' => $this->createUrl('updateServiceInfo', array('invoiceid' => $invoiceid ? $invoiceid : 0)),
            )); ?>
            <div class="modal-body">
                <div class="form-group">
                    <label class="col-xs-2"><?php echo Yii::t('label','开始时间') ?></label>
                    <div class="col-xs-10">
                        <input maxlength="255" class="form-control datepicker" name="startdate" id="startdate" type="text">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-xs-2"><?php echo Yii::t('label','结束时间') ?></label>
                    <div class="col-xs-10">
                        <input maxlength="255" class="form-control datepicker" name="enddate" id="enddate" type="text">
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
                <button type="button" class="btn btn-default" onclick="cancel()"><?php echo Yii::t('global', 'Cancel'); ?></button>
            </div>
            <?php $this->endWidget(); ?>
        </div>
    </div>
</div>
<script>
    function addservice(){
        $('#model').show()
    }

    function cancel() {
        $("#startdate").val("");
        $("#enddate").val("");
        $('#model').hide()
    }

    function cbSuccess(data)
    {
        if(data.state == 'success'){
            $("#startdate").val("");
            $("#enddate").val("");
            $('#model').hide()
            resultTip({msg: data.message, callback: function(){
                    reloadPage(window);
                }});
        } else {
            resultTip({msg: data.message, error: 1});
        }
    }

    $('.datepicker').datepicker({
        changeMonth: true,
        changeYear: true,
        dateFormat: 'yy-mm-dd'
    });
</script>
