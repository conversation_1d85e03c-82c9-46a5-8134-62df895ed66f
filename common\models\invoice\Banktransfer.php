<?php

/**
 * This is the model class for table "ivy_banktransfer".
 *
 * The followings are the available columns in table 'ivy_banktransfer':
 * @property string $id
 * @property string $title
 * @property string $receiptid
 * @property double $tamount
 * @property double $bamount
 * @property integer $btimestamp
 * @property string $childid
 * @property string $schoolid
 * @property integer $userid
 * @property integer $ostatus
 * @property integer $ustatus
 * @property integer $otimestamp
 */
class Banktransfer extends CActiveRecord
{
    const BANKTRANSFER_STATUS_YES = 1;
    const BANKTRANSFER_STATUS_NO = 0;

    /**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_banktransfer';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('id, receiptid, bamount, btimestamp, childid, schoolid, userid, otimestamp', 'required'),
			array('btimestamp, userid, ostatus, ustatus, otimestamp, company_payment', 'numerical', 'integerOnly'=>true),
			array('bamount', 'numerical','min'=>0.001),
			array('id', 'length', 'max'=>64),
			array('title', 'length', 'max'=>255),
			array('receiptid, childid', 'length', 'max'=>100),
			array('schoolid', 'length', 'max'=>10),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, title, receiptid, tamount, bamount, btimestamp, childid, schoolid, userid, ostatus, ustatus, otimestamp, company_payment', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'banktransferDetail'=>array(self::HAS_MANY,'BanktransferDetail','transferid'),
            'school' => array(self::BELONGS_TO, 'Branch', 'schoolid'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => Yii::t('invoice', '交易流水号'),
			'title' => Yii::t('invoice', '标题'),
			'receiptid' => Yii::t('invoice', '银行回单号'),
			'tamount' => 'Tamount',
			'bamount' => Yii::t('invoice', '到帐金额'),
			'btimestamp' => Yii::t('invoice', '银行到帐日期'),
			'childid' => Yii::t('invoice', '孩子'),
			'schoolid' => Yii::t('invoice', '所属校园'),
			'userid' => 'Userid',
			'ostatus' => 'Ostatus',
			'ustatus' => 'Ustatus',
			'otimestamp' => 'Otimestamp',
			'company_payment' => '公司付款',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('t.id',$this->id,true);
		$criteria->compare('t.title',$this->title,true);
		$criteria->compare('t.receiptid',$this->receiptid,true);
		$criteria->compare('t.tamount',$this->tamount);
		$criteria->compare('t.bamount',$this->bamount);
		$criteria->compare('t.btimestamp',$this->btimestamp);
		$criteria->compare('t.childid',$this->childid,true);
		$criteria->compare('t.schoolid',$this->schoolid,true);
		$criteria->compare('t.userid',$this->userid);
		$criteria->compare('t.ostatus',$this->ostatus);
		$criteria->compare('t.ustatus',$this->ustatus);
		$criteria->compare('t.otimestamp',$this->otimestamp);
		$criteria->compare('t.company_payment',$this->company_payment);
        $criteria->with = array('banktransferDetail');
        $criteria->order = 't.otimestamp Desc';
		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return Banktransfer the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
