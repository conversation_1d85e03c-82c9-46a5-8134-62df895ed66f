<?php

/**
 * This is the model class for table "ivy_calendar_week".
 *
 * The followings are the available columns in table 'ivy_calendar_week':
 * @property integer $wid
 * @property integer $yid
 * @property integer $sid
 * @property integer $monday
 * @property integer $monday_timestamp
 * @property integer $weeknumber
 */
class CalendarWeek extends CActiveRecord {

    /**
     * Returns the static model of the specified AR class.
     * @param string $className active record class name.
     * @return IvyCalendarWeek the static model class
     */
    public static function model($className = __CLASS__) {
        return parent::model($className);
    }

    /**
     * @return string the associated database table name
     */
    public function tableName() {
        return 'ivy_calendar_week';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules() {
        // NOTE: you should only define rules for those attributes that
        // will receive user inputs.
        return array(
            array('yid, sid', 'required'),
            array('yid, sid, monday, monday_timestamp, weeknumber', 'numerical', 'integerOnly' => true),
            // The following rule is used by search().
            // Please remove those attributes that should not be searched.
            array('wid, yid, sid, monday, monday_timestamp, weeknumber', 'safe', 'on' => 'search'),
        );
    }

    /**
     * @return array relational rules.
     */
    public function relations() {
        // NOTE: you may need to adjust the relation name and the related
        // class name for the relations automatically generated below.
        return array(
        );
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels() {
        return array(
            'wid' => 'Wid',
            'yid' => 'Yid',
            'sid' => 'Sid',
            'monday' => 'Monday',
            'monday_timestamp' => 'Monday Timestamp',
            'weeknumber' => 'Weeknumber',
        );
    }

    /**
     * Retrieves a list of models based on the current search/filter conditions.
     * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
     */
    public function search() {
        // Warning: Please modify the following code to remove attributes that
        // should not be searched.

        $criteria = new CDbCriteria;

        $criteria->compare('wid', $this->wid);
        $criteria->compare('yid', $this->yid);
        $criteria->compare('sid', $this->sid);
        $criteria->compare('monday', $this->monday);
        $criteria->compare('monday_timestamp', $this->monday_timestamp);
        $criteria->compare('weeknumber', $this->weeknumber);

        return new CActiveDataProvider($this, array(
                    'criteria' => $criteria,
                ));
    }

    public static function weeks($yid, $ext = false) {
        $w = array();
        if ($yid) {
            $criteria = new CDbCriteria;
            $criteria->compare('yid', $yid);
            $criteria->compare('monday_timestamp', '<=' . time());
            $criteria->order = 'weeknumber ASC';
            $weeks = CalendarWeek::model()->findAll($criteria);
            foreach ($weeks as $week) {
                if ($ext) {
                    $w[$week->weeknumber]['txt'] = sprintf(Yii::t('global', 'Week %s (%s - %s)'), $week->weeknumber, CommonUtils::formatDateTime($week->monday_timestamp), CommonUtils::formatDateTime($week->monday_timestamp + 7 * 24 * 3600 - 1));
                    //$w[$week->weeknumber]['txt'] = Yii::t('global', 'Week').' '.$week->weeknumber.' ('.date('Y/m/d', $week->monday_timestamp).' - '.date('Y/m/d', $week->monday_timestamp+7*24*3600-1).')';
                    $w[$week->weeknumber]['monday'] = $week->monday_timestamp;
                } else {
                    //$w[$week->weeknumber] = Yii::t('global', 'Week').' '.$week->weeknumber.' ('.date('Y/m/d', $week->monday_timestamp).' - '.date('Y/m/d', $week->monday_timestamp+7*24*3600-1).')';
                    $w[$week->weeknumber] = sprintf(Yii::t('global', 'Week %s (%s - %s)'), $week->weeknumber, CommonUtils::formatDateTime($week->monday_timestamp), CommonUtils::formatDateTime($week->monday_timestamp + 7 * 24 * 3600 - 1));
                }
            }
        }
        return $w;
    }

    public function getWeekTimeSlot($yid, $weekNum = null, $title = false) {
        $time_list = null;

        $week_cri = new CDbCriteria;
        if (!empty($yid)) {
            $week_cri->compare('yid', $yid);
        }
        if (!empty($weekNum)) {
            $week_cri->compare('weeknumber', $weekNum);
        }
        $week_cri->order = 'weeknumber ASC';

        $week_list = $this->findAll($week_cri);

        if (!empty($week_list)) {
            if (true == $title) {
                foreach ($week_list as $week) {
                    $time_list[$week->yid][$week->weeknumber] = sprintf(Yii::t("global", 'Week %s (%s - %s)'), $week->weeknumber, Mims::formatDateTime($week->monday_timestamp), Mims::formatDateTime($week->monday_timestamp + 7 * 24 * 3600 - 1));
                }
            } else {
                foreach ($week_list as $week) {
                    $time_list[$week->yid][$week->weeknumber] = Mims::formatDateTime($week->monday_timestamp) . ' - ' . Mims::formatDateTime($week->monday_timestamp + 7 * 24 * 3600 - 1);
                }
            }
        }

        return $time_list;
    }

}