<?php
$securityType = CommonUtils::LoadConfig('CfgSecurity');

$s = array();
if($safetyData){
    foreach ($safetyData as $k=>$item) {
        $s[$item['type']][$k] = $item['name'];
    }
}
?>
    <table class="table table-bordered pre-scrollable">
        <thead>
            <tr>
                <th colspan="8" class="text-center"><?php echo $model->getName(); ?> <span  style="font-weight:lighter" class="pull-right text-muted"><?php echo $yearMonth; ?></span></th>
            </tr>
        </thead>
        <?php if($safetySelfCategoryArr): ?>
            <tr>
                <th width="100" rowspan="2"><?php echo Yii::t('safety','Category') ?></th>
                <th colspan="2" rowspan="2"><?php echo Yii::t('safety','Detailed rules') ?></th>
                <th colspan="2"><?php echo Yii::t('safety','Work arrangement') ?></th>
                <th rowspan="2"><?php echo Yii::t('safety','Inspection record') ?></th>
                <th rowspan="2"><?php echo Yii::t('safety','Rectification measures (measure+deadline)') ?></th>
                <th width="200"><?php echo Yii::t('safety','Self-inspection score') ?></th>
            </tr>
            <tr>
                <td><?php echo Yii::t('safety','Responsible person (name)') ?></td>
                <td><?php echo Yii::t('safety','Record/document') ?></td>
                <td><?php echo Yii::t('safety','0~5 points or N/A (not applicable)') ?></td>
            </tr>
            <?php

            foreach ($safetySelfCategoryArr as $k=>$item): ?>
                <?php if(isset($ruleArr) && isset($ruleArr[$k])): ?>
                    <tr>
                        <td rowspan="<?php echo count($ruleArr[$k]) + 1 ?>"><?php echo $item;  ?></td>
                    </tr>
                    <?php
                    $i = 1;
                    foreach ($ruleArr[$k] as $ks=>$val): ?>
                        <tr>
                            <td><?php echo $i ?></td>
                            <td><?php echo $val ?></td>
                            <td id="principal<?php echo $ks ?>"><?php echo isset($safetySelfItemArr[$ks])  ? $safetySelfItemArr[$ks]['principal'] : '' ?></td>
                            <td><?php echo isset($safetySelfItemArr[$ks])  ? $safetySelfItemArr[$ks]['document'] : ''; ?></td>
                            <td><?php echo isset($safetySelfItemArr[$ks])  ? $safetySelfItemArr[$ks]['record'] : '' ?></td>
                            <td><?php echo isset($safetySelfItemArr[$ks])  ? $safetySelfItemArr[$ks]['impore'] : '' ?></td>
                            <td>
                                <div class="col-md-6">
                                    <?php echo isset($safetySelfItemArr[$ks])  ? $safetySelfItemArr[$ks]['score'] : '' ;?>
                                </div>                             
                            </td>
                        </tr>
                        <?php
                        $i++;
                    endforeach; ?>
                <?php endif; ?>
            <?php endforeach; ?>
        <?php endif; ?>
    </table>