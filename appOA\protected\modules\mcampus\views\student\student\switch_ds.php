<style>
    .font16{
        font-size:16px
    }
    .icon{
        width:16px;
        height:16px;
        margin-top:2px
    }
    .fee{
        width: 16px;
        height: 16px;
        
        border-radius: 1px;
        color:#fff;
        display:inline-block;
        text-align:center;
        line-height:16px;
        font-size:12px
    }
    .blue{
        background: #4D88D2;
    }
    .green{
        background: #5CB85C;
    }
    .red{
        background:#FCF1F0;
        padding:2px 6px;
        border-radius:4px;
        color: #D9534F;
    }
    .childList{      
        border: 1px solid #E8EAED;
        border-radius: 4px;
        padding:16px 0 16px 16px;
        height:90px
    }
    .flexStart{
        align-items: flex-start;
    }
    .classText{
        border-radius: 4px;
        background: #F7F7F8;
        border-radius: 100px 0px 0px 100px;
        padding: 3px 8px;
        color:#666;
        display: inline-flex;
        align-items: center;
    }
    .border{
        border: 1px solid #cccccc;
        border-radius: 4px;
        width: 20px;
        height: 22px;
        font-size: 12px;
        text-align: center;
        line-height: 21px;
    }
    .expected{
        border: 1px solid #D9534F;
        position: relative;
    }
    .expectedText{
        position: absolute;
        left: -1px;
        top: -10px;
        background: #D9534F;
        border-radius: 2px;
        color: #fff;
        padding: 2px 5px;
    }
    .historyNum{
        display:inline-block;
        width: 16px;
        height: 16px;
        background: #999999;
        color:#fff;
        border-radius:50%;
        text-align: center;
        line-height: 16px;
        margin-top:2px
    }
    .historyBorder{
        position: absolute;
        border-left: 1px dashed #ccc;
        height: 100%;
        left: 8px;
        top: 20px;
    }
    .w90{
        width:90px
    }
    .width24{
        width:24px
    }
    .PayFee{
        background: #F6FCF4;
    }
    .orange{
        color:#F0AD4E
    }
    [v-cloak] {
		display: none;
	}
    .borderLeft{
        border-left: 1px solid #D9D9D9;
    }
    .plr20{
        padding:5px 16px
    }
    .font20{
        font-size:24px
    }
    .font_bold{
        font-weight: bold
    }
    .border10{
        border-radius:10px
    }
    .greenColor{
        color:#5CB85C
    }
    .blueColor{
        color:#4D88D2
    }
    .classGreen{
        background: #E5F6E0;
    }
    .avatar42{
        width: 42px;
        height: 42px;
        border-radius: 50%;
        object-fit: cover;
    }
    .greenLabel{
        background: #5CB85C;
        color:#fff;
        padding:2px 6px;
        border-radius:4px
    }
    .tips{
        background: #FAFAFA;
        margin-top:20px;
        border-radius: 4px;
        padding:16px;
    }
    .closeClass{
        font-size:16px;
        color: #ccc;
    }
    .filterCss:hover ,.filterCss1{
        background: rgba(77,136,210,0.12);
        border-radius:4px;
        border-left:1px solid transparent !important;
        color:rgba(77, 136, 210, 1)
    }
    .filterCss:hover div,.filterCss:hover span,.filterCss1 div,.filterCss1 span{
        color:rgba(77, 136, 210, 1) !important
    }
    .filterCss:hover + .borderLeft,.filterCss1+.borderLeft {
        border-left:1px solid transparent !important;
    }
    .redColor{
        color:#D9534F
    }
    .bgGrey{
        background: #FAFAFA;
        border-radius:4px;
        padding:16px
    }
    .img24{
        width:24px;
        height: 24px;
        border-radius: 50%;
        object-fit: cover;
    }
</style>
<div id='container' v-cloak>
    <div class="col-md-10" v-if='Object.keys(dataList).length!=0'>
        <div class="row mb20 mt20 align-items">
            <div class="col-lg-4 col-sm-12 font20 flex align-items">
                <span class="font20 fontBold color3"><?php echo Yii::t('site','Total Enrolled');?> </span>
                <span class="label label-default ml5">{{dataList.total_num}}</span>
            </div>
            <div class='col-lg-8 col-sm-12' style='text-align:right'>
                <!-- <div class='mr16'> -->
                    <button type="button" class="btn btn-primary" v-if='dataList.sync_status==1' @click='syncSurvey()' :disabled='syncBtn'>{{syncBtn?"同步返校问卷结果中":"同步返校问卷结果"}}</button>
                    <span v-if='dataList.sync_status==2' class='greenColor font14'><span class='glyphicon glyphicon-ok-sign'></span> 已同步返校问卷结果</span>
                    <span v-if='dataList.sync_status==2' class='blueColor ml10 font14 cur-p' @click='syncSurvey()'>
                    <span v-if='!syncBtn'>
                        <span class='el-icon-refresh mr4 ' ></span>追加同步 
                        <el-popover
                            placement="top-start"
                            title="提示"
                            width="200"
                            trigger="hover"
                            content="每个学生的问卷结果只会同步一次，本操作仅对之前未同步的学生进行追加同步。">
                            <span class='el-icon-question color9 font16' slot="reference"></span>
                        </el-popover>
                    </span>
                    <span v-else><span class='el-icon-loading mr4' ></span>同步中</span>
                    </span>
                <!-- </div> -->
                <button type="button" class="btn btn-primary mr24 ml16" @click='statusRatio'>状态对比</button>
                <select class="form-control length_3 inline-block" v-model='startyear' @change='init("year")'>
                    <option v-for='(list,index) in yearList' :value='list.key'>{{list.value}}</option>
                </select>
            </div>
        </div>
        <div class='flex align-items'>
            <div class="flex1">
                <div class="pull-left  plr20 filterCss cur-p" style='border-left:1px solid transparent'  @click='filterDeposit("8")'  :class='filterType=="8"?"filterCss1":""' >
                    <div class='flex'>
                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/tuition_fee.png' ?>" class='icon' alt="">
                        <div class='flex1 ml5'>
                            <div class='color6 font14'>下学年已缴费</div>
                            <div class='color3 font16 mt5'>{{dataList.next_fee_num}}人 <span class='font12 color6 ml5'>定位金：{{dataList.next_deposit_num}}人，学费：{{dataList.next_tuition_num}}人</span></div>
                        </div>
                    </div>
                </div>
                <div class="pull-left plr20 borderLeft filterCss cur-p" @click='nextClass("7")' :class='filterType=="7"?"filterCss1":""'>
                    <div class='flex'>
                        <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/child.png' ?>" class='icon' alt="">
                        <div class='flex1 ml5'>
                            <div class='color6 font14'>下学年已分班</div>
                            <div class='color3 font16 mt5'>{{dataList.next_class_num}}人</div>
                        </div>
                    </div>
                </div>
                <div class="pull-left plr20 borderLeft filterCss cur-p" @click='nextClass("6")' :class='filterType=="6"?"filterCss1":""'>
                    <div class='flex'>
                        <span class='el-icon-warning font18 redColor'></span>
                        <div class='flex1 ml5'>
                            <div class='color6 font14'>下学年未分班</div>
                            <div class='color3 font16 mt5'>{{nextCLass.length}}人</div>
                        </div>
                    </div>
                </div>
                <div class="pull-left borderLeft plr20 filterCss cur-p" @click='filterNum("2")'  :class='filterType=="2"?"filterCss1":""'>
                    <div class='flex'>
                        <span class='el-icon-warning font18 redColor'></span>
                        <!-- <img src="<?php echo Yii::app()->themeManager->baseUrl.'/base/images/lost_staff.png' ?>" class='icon' alt="">  -->
                        <div class='flex1 ml5'>
                            <div class='color6 font14'>下学年不返校</div>
                            <div class='color3 font16 mt5'>{{dataList.next_leave_num}}人</div>
                        </div>
                    </div>
                </div>
                <div class="pull-left plr20 borderLeft filterCss cur-p" @click='filterNum("3")' :class='filterType=="3"?"filterCss1":""'>
                    <div class='flex'>
                        <span class='el-icon-warning font18 redColor'></span>
                        <div class='flex1 ml5'>
                            <div class='color6 font14'>本学年退学</div>
                            <div class='color3 font16 mt5'>{{dataList.quit_num}}人</div>
                        </div>
                    </div>
                </div>
                <div class="pull-left plr20 borderLeft filterCss cur-p" @click='filterSurveyNum("4")' :class='filterType=="4"?"filterCss1":""'>
                    <div class='flex'>
                        <span class='el-icon-warning font18 redColor'></span>
                        <div class='flex1 ml5'>
                            <div class='color6 font14'>问卷不确定</div>
                            <div class='color3 font16 mt5'>{{indeterminacy.length}}人</div>
                        </div>
                    </div>
                </div>
                <div class="pull-left plr20 borderLeft filterCss cur-p" @click='filterSurveyNum("5")' :class='filterType=="5"?"filterCss1":""'>
                    <div class='flex'>
                        <span class='el-icon-warning font18 redColor'></span>
                        <div class='flex1 ml5'>
                            <div class='color6 font14'>问卷未填写</div>
                            <div class='color3 font16 mt5'>{{notFilled.length}}人</div>
                        </div>
                    </div>
                </div>
                
            </div>
        </div>
        <div class='clearfix'></div>
        <div v-for='(data,index) in dataList.class_info'>
            <div class="panel panel-default mt20"  v-if='allClassList[data.classid]'>
                <div class="panel-heading">
                    <label class="checkbox-inline">
                        <input type="checkbox" id="inlineCheckbox1" value="option1" v-model='data.checked' @change='allChecked(data,"class")'><span class='color3 font14 font_bold'> {{data.title}}</span>  <span class="label label-default ml10 mr10 border10" style='font-size:100%'>{{dataList.class_child[data.classid].length}}</span>
                    </label>
                    <button type="button" class="btn btn-primary pull-right ml10" @click='saveAssign(data,"class")'>保存</button>
                    <div class="form-inline pull-right">                   
                    <select class="form-control " v-model='data.assignClass'>
                        <option value=''>分配班级或设置状态</option>
                        <optgroup label="分配到未来学年">
                            <option v-for='(list,id) in dataList.next_class_list' :value='list.classid'>{{list.title}}</option>
                        </optgroup>
                        <optgroup label="设置状态">
                            <option value="setGraduated">设置为已毕业</option>
                            <option value="setDroppedout">设置为已退学</option>
                        </optgroup>
                    </select>
                    </div>
                    <div class='clearfix'></div>
                </div>
                <div class="panel-body" style='padding:24px 24px 0'>
                    <div class='row'>
                        <template   v-for='(list,idx) in allClassList[data.classid]'>
                            <div class="col-md-4 pb24" v-if='filterType==""'>
                                <div :class='dataList.child_data[list].deposit || dataList.child_data[list].tuition?"PayFee":""'>
                                    <div class='childList  ' :class='dataList.child_data[list].status==0 && (dataList.child_data[list].survey_result==2 || dataList.child_data[list].survey_result==3 || dataList.child_data[list].survey_result==4 || dataList.child_data[list].survey_result==5)?"expected":""'>  
                                        <span class='expectedText' v-if='dataList.child_data[list].status==0 && dataList.child_data[list].survey_result==2'>问卷结果：转校</span>            
                                        <span class='expectedText' v-if='dataList.child_data[list].status==0 && dataList.child_data[list].survey_result==3'>问卷结果：不返校</span>            
                                        <span class='expectedText' v-if='dataList.child_data[list].status==0 && dataList.child_data[list].survey_result==4'>问卷结果：不确定</span>            
                                        <span class='expectedText' v-if='dataList.child_data[list].status==0 && dataList.child_data[list].survey_result==5'>问卷结果：未填写</span>                      
                                        <div class='flex flexStart '>
                                            <input type="checkbox" id="inlineCheckbox1" :value="list" v-model='data.checkList' @change='Checked(data)'>
                                            <div class='flex1 ml10'>
                                                <div class='color3 '>
                                                    <span class='font14 mr10 fontBold'>{{dataList.child_data[list].name}}</span>     
                                                    <span class='fee blue' v-if='dataList.child_data[list].deposit'>定</span> 
                                                    <span class='fee green'  v-if='dataList.child_data[list].tuition'>学</span>
                                                    <!-- <span class='greenLabel' v-if='dataList.child_data[list].status==1'>下学年返校</span> -->
                                                    <span class='red' v-if='dataList.child_data[list].status==2'>下学年不返校</span>
                                                    <span class='red' v-if='dataList.child_data[list].status==3'>本学年将退学</span>
                                                    <div class='pull-right mr20'>
                                                        <a class="btn btn-default btn-xs mr10" target="_blank" :href="'<?php echo $this->createUrl('//child/index/index');?>&childid='+list" role="button">
                                                            <span class="glyphicon glyphicon-link"></span>
                                                        </a>
                                                        <div class="dropdown pull-right">
                                                            <button class="btn btn-default dropdown-toggle btn-xs width24" type="button" id="dropdownMenu1" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                                                                <span class="caret"></span>
                                                            </button>
                                                            <ul class="dropdown-menu" aria-labelledby="dropdownMenu1">
                                                                <li v-if='dataList.child_data[list].status==0'><a href="javascript:;" @click='showStatus(dataList.child_data[list],data.classid,"survey")'>确认问卷结果</a></li>
                                                                <li  v-else><a href="javascript:;" @click='showStatus(dataList.child_data[list],data.classid,"status")'>修改状态</a>
                                                                    <!-- <li  v-if='dataList.child_data[list].status!=0'><a href="javascript:;" @click='cancelStatus(dataList.child_data[list])'>重置状态</a></li> -->
                                                                <!-- </template> -->
                                                            </ul>
                                                        </div>
                                                    </div>
                                                    
                                                </div>
                                                <div class='color6 font12 mt10 flex align-items'>
                                                    <span class='color6 flex1'>{{dataList.child_data[list].gender==1?'男':'女'}} | {{dataList.child_data[list].age}}</span>
                                                    <span class='classText'  :class='dataList.child_data[list].deposit || dataList.child_data[list].tuition?"classGreen":""' v-if='dataList.child_data[list].next_class_id!=null'>{{filterClass(dataList.child_data[list].next_class_id,"next_class_info")}}
                                                        <el-tooltip class="item" effect="dark" content="取消分班" placement="top">
                                                            <span class='el-icon-error closeClass ml4' @click='cancelClass(list)' ></span>
                                                        </el-tooltip>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-4 pb24" v-else-if="shouldRenderChild(filterType, dataList.child_data[list], filterStatus) ">
                                <div :class='dataList.child_data[list].deposit || dataList.child_data[list].tuition?"PayFee":""'>
                                    <div class='childList  ' :class='dataList.child_data[list].status==0 && (dataList.child_data[list].survey_result==2 || dataList.child_data[list].survey_result==3 || dataList.child_data[list].survey_result==4 || dataList.child_data[list].survey_result==5)?"expected":""'>  
                                        <span class='expectedText' v-if='dataList.child_data[list].status==0 && dataList.child_data[list].survey_result==2'>问卷结果：转校</span>            
                                        <span class='expectedText' v-if='dataList.child_data[list].status==0 && dataList.child_data[list].survey_result==3'>问卷结果：不返校</span>            
                                        <span class='expectedText' v-if='dataList.child_data[list].status==0 && dataList.child_data[list].survey_result==4'>问卷结果：不确定</span>            
                                        <span class='expectedText' v-if='dataList.child_data[list].status==0 && dataList.child_data[list].survey_result==5'>问卷结果：未填写</span>                  
                                        <div class='flex flexStart '>
                                            <input type="checkbox" id="inlineCheckbox1" :value="list" v-model='data.checkList' @change='Checked(data)'>
                                            <div class='flex1 ml10'>
                                                <div class='color3 '>
                                                    <span class='font14 mr10 fontBold'>{{dataList.child_data[list].name}}</span>     
                                                    <span class='fee blue' v-if='dataList.child_data[list].deposit'>定</span> 
                                                    <span class='fee green'  v-if='dataList.child_data[list].tuition'>学</span>
                                                    <!-- <span class='greenLabel' v-if='dataList.child_data[list].status==1'>下学年返校</span> -->
                                                    <span class='red' v-if='dataList.child_data[list].status==2'>下学年不返校</span>
                                                    <span class='red' v-if='dataList.child_data[list].status==3'>本学年将退学</span>
                                                    <div class='pull-right mr20'>
                                                        <a class="btn btn-default btn-xs mr10" target="_blank" :href="'<?php echo $this->createUrl('//child/index/index');?>&childid='+list" role="button">
                                                            <span class="glyphicon glyphicon-link"></span>
                                                        </a>
                                                        <div class="dropdown pull-right">
                                                            <button class="btn btn-default dropdown-toggle btn-xs width24" type="button" id="dropdownMenu1" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                                                                <span class="caret"></span>
                                                            </button>
                                                            <ul class="dropdown-menu" aria-labelledby="dropdownMenu1">
                                                                <li v-if='dataList.child_data[list].status==0'><a href="javascript:;" @click='showStatus(dataList.child_data[list],data.classid,"survey")'>确认问卷结果</a></li>
                                                                <li  v-else><a href="javascript:;" @click='showStatus(dataList.child_data[list],data.classid,"status")'>修改状态</a>
                                                                    <!-- <li  v-if='dataList.child_data[list].status!=0'><a href="javascript:;" @click='cancelStatus(dataList.child_data[list])'>重置状态</a></li> -->
                                                                <!-- </template> -->
                                                            </ul>
                                                        </div>
                                                    </div>
                                                    
                                                </div>
                                                <div class='color6 font12 mt10 flex align-items'>
                                                    <span class='color6 flex1'>{{dataList.child_data[list].gender==1?'男':'女'}} | {{dataList.child_data[list].age}}</span>
                                                    <span class='classText'  :class='dataList.child_data[list].deposit || dataList.child_data[list].tuition?"classGreen":""' v-if='dataList.child_data[list].next_class_id!=null'>{{filterClass(dataList.child_data[list].next_class_id,"next_class_info")}}
                                                        <el-tooltip class="item" effect="dark" content="取消分班" placement="top">
                                                            <span class='el-icon-error closeClass ml4' @click='cancelClass(list)' ></span>
                                                        </el-tooltip>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>
        <div class="panel panel-default mt20"  v-if='allClassList[0]'>
            <div class="panel-heading">
                <label class="checkbox-inline">
                    <input type="checkbox" id="inlineCheckbox1" value="option1" v-model='newChecked' @change='allChecked(allClassList[0],"new")'><span class='color3 font14 font_bold'> 过期或新注册数据</span>  <span class="label label-default ml10 mr10 border10" style='font-size:100%'>{{allClassList[0].length}}</span>
                </label>
                <button type="button" class="btn btn-primary pull-right ml10" @click='saveAssign(allClassList[0],"new")'>保存</button>
                <div class="form-inline pull-right">                   
                <select class="form-control " v-model='newAssignClass'>
                    <option value=''>分配班级或设置状态</option>
                    <optgroup label="分配到未来学年">
                        <option v-for='(list,id) in dataList.next_class_list' :value='list.classid'>{{list.title}}</option>
                    </optgroup>
                    <optgroup label="设置状态">
                        <option value="setGraduated">设置为已毕业</option>
                        <option value="setDroppedout">设置为已退学</option>
                    </optgroup>
                </select>
                </div>
                <div class='clearfix'></div>
            </div>
            <div class="panel-body" style='padding:24px 24px 0'>
                <div class='row'>
                    <template   v-for='(list,idx) in allClassList[0]'>
                        <div class="col-md-4 pb24" v-if='filterType==""'>
                            <div :class='dataList.child_data[list].deposit || dataList.child_data[list].tuition?"PayFee":""'>
                                <div class='childList  ' >  
                                    <!--  :class='dataList.child_data[list].status==0 && (dataList.child_data[list].survey_result==2 || dataList.child_data[list].survey_result==3 || dataList.child_data[list].survey_result==4 || dataList.child_data[list].survey_result==5)?"expected":""' -->

                                    <!-- <span class='expectedText' v-if='dataList.child_data[list].status==0 && dataList.child_data[list].survey_result==2'>问卷结果：转校</span>            
                                    <span class='expectedText' v-if='dataList.child_data[list].status==0 && dataList.child_data[list].survey_result==3'>问卷结果：不返校</span>            
                                    <span class='expectedText' v-if='dataList.child_data[list].status==0 && dataList.child_data[list].survey_result==4'>问卷结果：不确定</span>            
                                    <span class='expectedText' v-if='dataList.child_data[list].status==0 && dataList.child_data[list].survey_result==5'>问卷结果：未填写</span>                       -->
                                    <div class='flex flexStart '>
                                        <input type="checkbox" id="inlineCheckbox1" :value="list" v-model='newCheckList' @change='Checked("","new")'>
                                        <div class='flex1 ml10'>
                                            <div class='color3 '>
                                                <span class='font14 mr10 fontBold'>{{dataList.child_data[list].name}}</span>     
                                                <span class='fee blue' v-if='dataList.child_data[list].deposit'>定</span> 
                                                <span class='fee green'  v-if='dataList.child_data[list].tuition'>学</span>
                                                <!-- <span class='greenLabel' v-if='dataList.child_data[list].status==1'>下学年返校</span> -->
                                                <span class='red' v-if='dataList.child_data[list].status==2'>下学年不返校</span>
                                                <span class='red' v-if='dataList.child_data[list].status==3'>本学年将退学</span>
                                                <div class='pull-right mr20'>
                                                    <a class="btn btn-default btn-xs mr10" target="_blank" :href="'<?php echo $this->createUrl('//child/index/index');?>&childid='+list" role="button">
                                                        <span class="glyphicon glyphicon-link"></span>
                                                    </a>
                                                    <div class="dropdown pull-right">
                                                        <button class="btn btn-default dropdown-toggle btn-xs width24" type="button" id="dropdownMenu1" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                                                            <span class="caret"></span>
                                                        </button>
                                                        <ul class="dropdown-menu" aria-labelledby="dropdownMenu1">
                                                            <!-- <li v-if='dataList.child_data[list].status==0'><a href="javascript:;" @click='showStatus(dataList.child_data[list],data.classid,"survey")'>确认问卷结果</a></li> -->
                                                            <li  ><a href="javascript:;" @click='newShowStatus(dataList.child_data[list],0,"new")'>修改状态</a>
                                                                <!-- <li  v-if='dataList.child_data[list].status!=0'><a href="javascript:;" @click='cancelStatus(dataList.child_data[list])'>重置状态</a></li> -->
                                                            <!-- </template> -->
                                                        </ul>
                                                    </div>
                                                </div>
                                                
                                            </div>
                                            <div class='color6 font12 mt10 flex align-items'>
                                                <span class='color6 flex1'>{{dataList.child_data[list].gender==1?'男':'女'}} | {{dataList.child_data[list].age}}</span>
                                                <span class='classText'  :class='dataList.child_data[list].deposit || dataList.child_data[list].tuition?"classGreen":""' v-if='dataList.child_data[list].next_class_id!=null'>{{filterClass(dataList.child_data[list].next_class_id,"next_class_info")}}
                                                    <el-tooltip class="item" effect="dark" content="取消分班" placement="top">
                                                        <span class='el-icon-error closeClass ml4' @click='cancelClass(list)' ></span>
                                                    </el-tooltip>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 pb24" v-else-if=' shouldRenderChild(filterType, dataList.child_data[list], filterStatus)'>
                            <div :class='dataList.child_data[list].deposit || dataList.child_data[list].tuition?"PayFee":""'>
                                <div class='childList  ' >  
                                    <!--  :class='dataList.child_data[list].status==0 && (dataList.child_data[list].survey_result==2 || dataList.child_data[list].survey_result==3 || dataList.child_data[list].survey_result==4 || dataList.child_data[list].survey_result==5)?"expected":""' -->

                                    <!-- <span class='expectedText' v-if='dataList.child_data[list].status==0 && dataList.child_data[list].survey_result==2'>问卷结果：转校</span>            
                                    <span class='expectedText' v-if='dataList.child_data[list].status==0 && dataList.child_data[list].survey_result==3'>问卷结果：不返校</span>            
                                    <span class='expectedText' v-if='dataList.child_data[list].status==0 && dataList.child_data[list].survey_result==4'>问卷结果：不确定</span>            
                                    <span class='expectedText' v-if='dataList.child_data[list].status==0 && dataList.child_data[list].survey_result==5'>问卷结果：未填写</span>                       -->
                                    <div class='flex flexStart '>
                                        <input type="checkbox" id="inlineCheckbox1" :value="list" v-model='newCheckList' @change='Checked("","new")'>
                                        <div class='flex1 ml10'>
                                            <div class='color3 '>
                                                <span class='font14 mr10 fontBold'>{{dataList.child_data[list].name}}</span>     
                                                <span class='fee blue' v-if='dataList.child_data[list].deposit'>定</span> 
                                                <span class='fee green'  v-if='dataList.child_data[list].tuition'>学</span>
                                                <!-- <span class='greenLabel' v-if='dataList.child_data[list].status==1'>下学年返校</span> -->
                                                <span class='red' v-if='dataList.child_data[list].status==2'>下学年不返校</span>
                                                <span class='red' v-if='dataList.child_data[list].status==3'>本学年将退学</span>
                                                <div class='pull-right mr20'>
                                                    <a class="btn btn-default btn-xs mr10" target="_blank" :href="'<?php echo $this->createUrl('//child/index/index');?>&childid='+list" role="button">
                                                        <span class="glyphicon glyphicon-link"></span>
                                                    </a>
                                                    <div class="dropdown pull-right">
                                                        <button class="btn btn-default dropdown-toggle btn-xs width24" type="button" id="dropdownMenu1" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                                                            <span class="caret"></span>
                                                        </button>
                                                        <ul class="dropdown-menu" aria-labelledby="dropdownMenu1">
                                                            <!-- <li v-if='dataList.child_data[list].status==0'><a href="javascript:;" @click='showStatus(dataList.child_data[list],data.classid,"survey")'>确认问卷结果</a></li> -->
                                                            <li  ><a href="javascript:;" @click='newShowStatus(dataList.child_data[list],0,"new")'>修改状态</a>
                                                                <!-- <li  v-if='dataList.child_data[list].status!=0'><a href="javascript:;" @click='cancelStatus(dataList.child_data[list])'>重置状态</a></li> -->
                                                            <!-- </template> -->
                                                        </ul>
                                                    </div>
                                                </div>
                                                
                                            </div>
                                            <div class='color6 font12 mt10 flex align-items'>
                                                <span class='color6 flex1'>{{dataList.child_data[list].gender==1?'男':'女'}} | {{dataList.child_data[list].age}}</span>
                                                <span class='classText'  :class='dataList.child_data[list].deposit || dataList.child_data[list].tuition?"classGreen":""' v-if='dataList.child_data[list].next_class_id!=null'>{{filterClass(dataList.child_data[list].next_class_id,"next_class_info")}}
                                                    <el-tooltip class="item" effect="dark" content="取消分班" placement="top">
                                                        <span class='el-icon-error closeClass ml4' @click='cancelClass(list)' ></span>
                                                    </el-tooltip>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </div>
        <div v-if='quit_child_data.length!=0'>
            <div class="panel panel-default mt20"  >
                <div class="panel-heading">
                    <label class="checkbox-inline">
                        <span class='color3 font14 font_bold'>已退学学生</span>  <span class="label label-default ml10 mr10 border10" style='font-size:100%'>{{quit_child_data.length}}</span>
                    </label>
                </div>
                <div class="panel-body" style='padding:24px 24px 0'>
                    <div class='row'>
                        <template   v-for='(list,idx) in quit_child_data'>
                            <div class="col-md-4 pb24" v-if='filterType==""'>
                                <div >
                                    <div class='childList  ' >  
                                        <div class='flex flexStart '>
                                            <!-- <input type="checkbox" id="inlineCheckbox1" :value="list" v-model='newCheckList' @change='Checked("","new")'> -->
                                            <div class='flex1 ml10'>
                                                <div class='color3 '>
                                                    <span class='font14 mr10 fontBold'>{{list.name}}</span>     
                                                    <div class='pull-right mr20'>
                                                        <a class="btn btn-default btn-xs " target="_blank" :href="'<?php echo $this->createUrl('//child/index/index');?>&childid='+list.childid" role="button">
                                                            <span class="glyphicon glyphicon-link"></span>
                                                        </a>
                                                    </div>
                                                    
                                                </div>
                                                <div class='color6 font12 mt10 flex align-items'>
                                                    <span class='color6 flex1'>{{list.gender==1?'男':'女'}} | {{list.age}}</span>
                                                    <span class='classText'  :class='list.deposit || list.tuition?"classGreen":""' v-if='list.next_class_id!=null'>{{filterClass(list.next_class_id,"next_class_info")}}
                                                        <el-tooltip class="item" effect="dark" content="取消分班" placement="top">
                                                            <span class='el-icon-error closeClass ml4' @click='cancelClass(list)' ></span>
                                                        </el-tooltip>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 pb24" v-else-if="shouldRenderChild(filterType, dataList.child_data[list], filterStatus)">
                                <div :class='list.deposit || list.tuition?"PayFee":""'>
                                    <div class='childList  ' >  
                                        <div class='flex flexStart '>
                                            <!-- <input type="checkbox" id="inlineCheckbox1" :value="list" v-model='newCheckList' @change='Checked("","new")'> -->
                                            <div class='flex1 ml10'>
                                                <div class='color3 '>
                                                    <span class='font14 mr10 fontBold'>{{list.name}}</span>     
                                                    <div class='pull-right mr20'>
                                                        <a class="btn btn-default btn-xs " target="_blank" :href="'<?php echo $this->createUrl('//child/index/index');?>&childid='+list.childid" role="button">
                                                            <span class="glyphicon glyphicon-link"></span>
                                                        </a>
                                                        
                                                    </div>
                                                    
                                                </div>
                                                <div class='color6 font12 mt10 flex align-items'>
                                                    <span class='color6 flex1'>{{list.gender==1?'男':'女'}} | {{list.age}}</span>
                                                    <span class='classText'  :class='list.deposit || list.tuition?"classGreen":""' v-if='list.next_class_id!=null'>{{filterClass(list.next_class_id,"next_class_info")}}
                                                        <el-tooltip class="item" effect="dark" content="取消分班" placement="top">
                                                            <span class='el-icon-error closeClass ml4' @click='cancelClass(list)' ></span>
                                                        </el-tooltip>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>
        <div v-if='Object.keys(allClassList).length==0 && quit_child_data.length==0' class='mt20'>
            <div class="alert alert-warning" role="alert">暂无数据</div>
        </div>
    </div> 
    <div class="modal fade" id="contentModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" >
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content ">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "确认结果");?></h4>
                </div>
                <div class="modal-body">
                    <div class='flex' v-if='editChild.name'>
                        <div>
                            <img :src="editChild.avatar" alt="" class='avatar42'>
                        </div>
                        <div class='flex1 ml10'>
                            <div class='font14 color3 mt4'>{{editChild.name}}</div>
                            <div class='font12 color6 mt4' v-if='editType!="new"'>{{filterClass(editClassid,"class_info")}}</div>
                        </div>
                    </div>
                    <div class='mt24'>
                        <div class="radio font14">
                            <label>
                                <input type="radio"  value="1" v-model='status'>
                                下学年将返校
                            </label>
                        </div>
                        <div class="radio font14">
                            <label>
                                <input type="radio" value="2" v-model='status'>
                                下学年不返校
                            </label>
                        </div>
                        <div class="radio font14">
                            <label>
                                <input type="radio" value="3" v-model='status'>
                                本学年将退学
                            </label>
                        </div>
                    </div>
                    <div v-if='status==2 || status==3' class='tips'>
                        <div class='orange font14'><span class='glyphicon glyphicon-info-sign mr5'></span>此操作会自动将学生转入退学流程</div>
                        <div class="checkbox"  v-if='editChild.checkedSkip=="1"'>
                            <label class='font14'>
                                <input type="checkbox" value="" v-model='editChild.checked' disabled><span class='font14 color3'>跳过退学流程中的面谈步骤</span> 
                            </label>
                        </div>
                    </div>
                    <!-- <div class='font14 color3 mb5 '>备注说明：</div>
                    <textarea class="form-control" rows="3" v-model='memo'></textarea>
                    -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button" class="btn btn-primary" @click='editStatus()' v-if='editType=="status" || editType=="new"'><?php echo Yii::t("newDS", "确认");?></button>
                    <button type="button" class="btn btn-primary" @click='editSurvey()' v-else><?php echo Yii::t("newDS", "确认");?></button>
                </div>
                <div class='clearfix'></div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="cancelModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" >
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content ">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "提示");?></h4>
                </div>
                <div class="modal-body">
                   <div v-if='cancelType=="status"'>确认重置当前学生状态吗？</div> 
                   <div v-if='cancelType=="class"'>确认取消分班吗？</div> 
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button" class="btn btn-primary"  v-if='cancelType=="status"' @click='cancelStatus()'><?php echo Yii::t("newDS", "确认");?></button>
                    <button type="button" class="btn btn-primary" v-if='cancelType=="class"' @click='cancelClass()'><?php echo Yii::t("newDS", "确认");?></button>
                </div>
                <div class='clearfix'></div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="statusRatioModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" >
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content ">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("newDS", "状态对比");?></h4>
                </div>
                <div class="modal-body">
                   <div class='font14 color3'>设置对比时间</div>
                   <div class='flex mt16 bgGrey align-items'>
                        <div class='flex1'>
                            <el-date-picker
                                v-model="startDate"
                                type="date"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                                size="small"
                                placement="bottom-start"
                                placeholder="<?php echo Yii::t("ptc", "Select a date"); ?>">
                            </el-date-picker>
                            <span class='el-icon-sort font18 ml10 mr10' style='transform: rotate(90deg);'></span>
                            <el-date-picker
                                v-model="endDate"
                                type="date"
                                size="small"
                                format="yyyy-MM-dd"
                                value-format="yyyy-MM-dd"
                                placement="bottom-start"
                                placeholder="<?php echo Yii::t("ptc", "Select a date"); ?>">
                            </el-date-picker>
                        </div>
                        <button type="button" class="btn btn-primary" @click='dataRatio'>生成对比数据</button>
                   </div>
                   <div v-if='ratioData.list'>
                        <div class='flex align-items mt24' v-if='ratioData.list.length>0'>
                            <div class='flex1 color3 font14'>{{ratioData.list.length}} 名学生状态有变化</div>
                            <button type="button" class="btn btn-default" @click='exportRatio'>导出</button>
                        </div>
                        <div class='mt16'>
                            <el-table
                                id='exportTable'
                                :header-cell-style="{background:'#fafafa',color:'#333'}"
                                :data="ratioData.list"
                                style="width: 100%">
                                <el-table-column
                                    prop="date"
                                    label="学生"
                                    width="180">
                                    <template slot-scope="scope">
                                        <div class='flex align-items'>
                                        <img :src="ratioData.childInfo[scope.row.childId].avatar" alt="" class='img24'>
                                        <span class='font14 color6 ml10'> {{ratioData.childInfo[scope.row.childId].name}}</span>
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column
                                    prop="name"
                                    
                                    width="180">
                                    <template slot="header" slot-scope="scope">
                                        {{startDate}}
                                    </template>
                                    <template slot-scope="scope">
                                        <div class='font14 color6' v-if='scope.row.startStatus!=0'>
                                            <span v-if='scope.row.startStatus==1'>下学年返校</span>
                                            <span v-if='scope.row.startStatus==2'>下学年不返校</span>
                                            <span v-if='scope.row.startStatus==3'>本学年将退学</span>
                                        </div>
                                        <div v-else>
                                            <span  v-if='scope.row.startSurveyResult==4'>问卷结果：不确定</span>            
                                            <span  v-if='scope.row.startSurveyResult==5'>问卷结果：未填写</span>    
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column
                                    prop="address"
                                    width="180"
                                    >
                                    <template slot="header" slot-scope="scope">
                                        {{endDate}}
                                    </template>
                                    <template slot-scope="scope">
                                        <div class='font14 color6'>
                                            <span v-if='scope.row.endStatus==1'>下学年返校</span>
                                            <span v-if='scope.row.endStatus==2'>下学年不返校</span>
                                            <span v-if='scope.row.endStatus==3'>本学年将退学</span>
                                        </div>
                                    </template>
                                </el-table-column>
                                <el-table-column
                                    prop="address"
                                    label="最后操作人">
                                    <template slot-scope="scope">
                                        <div class='font14 color6'>
                                            {{ratioData.staffInfo[scope.row.log.uid].name}} | {{scope.row.log.log_time}}
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                   </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                </div>
                <div class='clearfix'></div>
            </div>
        </div>
    </div>
</div> 
    <script>
        var childBaseUrl = '<?php echo $this->createUrl('//child/index/index')."&childid=";?>';
        // $('#historyModal').modal('show')
         var container = new Vue({
        el: "#container",
        data: {
            dataList:{},
            assignClass:'',
            type:'',
            historyData:[],
            effect_time_y:'',
            effect_time_s:'',
            memo:'',
            currentChild:'',
            child_type:false,
            startyear:'0',
            yearList:[],
            editChild:{},
            editClassid:'',
            status:'',
            editType:'',
            syncBtn:false,
            cancelId:'',
            cancelType:'',
            filterType:'',
            allClassList:[],
            startDate:'',
            endDate:'',
            ratioData:{},
            newChecked:false,
            newAssignClass:'',
            newCheckList:[],
            quit_child_data:[],
            filterStatus:'',
            indeterminacy:[],
            notFilled:[],
            nextCLass:[]
        },
        watch:{},
        created:function(){
            this.init()
        },
        methods:{
            filterClass(classid,type){
                let classType=this.dataList[type].find(item2 =>item2.classid === parseInt(classid)).title
               return classType
            },
            init(year){
                var that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getTransitionData") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        startyear:this.startyear
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            data.data.class_info.forEach((item,index) => {
                                item.checked=false
                                item.assignClass=''
                                item.checkList=[]
                            })
                            Object.values(data.data.quit_child_data).forEach((item,index) => {
                                item.checked=false
                                item.assignClass=''
                                item.checkList=[]
                            })
                            that.quit_child_data=Object.values(data.data.quit_child_data)
                            that.effect_time_y=data.data.current_year
                            that.dataList=data.data
                            that.allClassList=data.data.class_child
                            that.notFilled=[]
                            that.nextCLass=[]
                            that.indeterminacy=[]
                            for(let key in data.data.class_child){
                                if(key!=0){
                                    data.data.class_child[key].forEach(item => {
                                        if(data.data.child_data[item].survey_result==5 && data.data.child_data[item].status==0){
                                            that.notFilled.push(item)
                                        }
                                        if(data.data.child_data[item].survey_result==4 && data.data.child_data[item].status==0){
                                            that.indeterminacy.push(item)
                                        }
                                        if(data.data.child_data[item].next_class_id==null){
                                            that.nextCLass.push(item)
                                        }
                                    });
                                }
                            }
                            if(year){
                                that.filterType=''
                            }else{
                                if(that.filterType!=''){
                                    if(that.filterStatus=='survey_result'){
                                        that.filterSurveyNum(that.filterType,'init')
                                    }else if(that.filterStatus=='status'){
                                        that.filterNum(that.filterType,'init')
                                    }else if(that.filterStatus=='next_class_id'){
                                        that.nextClass(that.filterType,'init')
                                    }
                                }
                            }
                            that.startyear=data.data.startyear+''
                            that.yearList=data.data.startyear_list
                            that.syncBtn=false
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                        that.showLoading=false
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.showLoading=false
                    }
                })
            },
            allChecked(list,type){
                if(this.filterType==""){                
                    if(type=='class'){
                        list.checkList=[]
                        if(list.checked){
                            list.checkList=this.dataList.class_child[list.classid]
                        }
                    }else{
                        this.newCheckList=[]
                        if(this.newChecked){
                            this.newCheckList=this.allClassList[0]
                        }
                    }
                }else{
                    
                    if(type=='class'){
                        list.checkList=[]
                        if(list.checked){
                            for(let i=0;i<this.allClassList[list.classid].length;i++){
                                let data=this.allClassList[list.classid][i]
                                if(this.filterType==6 && this.dataList.child_data[data].next_class_id==null){
                                    list.checkList.push(data)
                                }else if(this.filterType==this.dataList.child_data[data][this.filterStatus]){
                                    list.checkList.push(data)
                                }
                            }
                        }
                    }else{
                        this.newCheckList=[]
                        if(this.newChecked){
                            for(let i=0;i<this.allClassList[0].length;i++){
                                let data=this.allClassList[0][i]
                                if(this.filterType==6 && this.dataList.child_data[data].next_class_id==null){
                                    this.newCheckList.push(data)
                                }else if(this.filterType==this.dataList.child_data[data][this.filterStatus]){
                                    this.newCheckList.push(data)
                                }
                            }
                        }
                    }
                }
            },
            Checked(list,type){
                if(this.filterType==""){    
                    if(type=='new'){
                        if(this.newCheckList.length==this.allClassList[0].length){
                            this.newChecked=true
                        }else{
                            this.newChecked=false
                        }
                    }else{
                        if(list.checkList.length==this.dataList.class_child[list.classid].length){
                            list.checked=true
                        }else{
                            list.checked=false
                        }
                    }
                }else{
                    if(type=='new'){
                        let lens=[]
                        for(let i=0;i<this.allClassList[0].length;i++){
                            let data=this.allClassList[0][i]
                            if(this.filterType==6 && this.dataList.child_data[data].next_class_id==null){
                                lens.push(data)
                            }else if(this.filterType==this.dataList.child_data[data][this.filterStatus]){
                                lens.push(data)
                            }
                        }
                        if(this.newCheckList.length==lens.length){
                            this.newChecked=true
                        }else{
                            this.newChecked=false
                        }
                    }else{
                        let classLen=[]
                        for(let i=0;i<this.dataList.class_child[list.classid].length;i++){
                            let data=this.dataList.class_child[list.classid][i]
                            if(this.filterType==6 && this.dataList.child_data[data].next_class_id==null){
                                classLen.push(data)
                            }else if(this.filterType==this.dataList.child_data[data][this.filterStatus]){
                                classLen.push(data)
                            }
                        }
                        if(list.checkList.length==classLen.length){
                            list.checked=true
                        }else{
                            list.checked=false
                        }
                    }
                }
            },
            saveAssign(data,type){
                var that=this
                var datas={}
                if(type=='class'){
                    if(data.checkList.length==0){
                        resultTip({
                            error: 'warning',
                            msg: '请勾选学生'
                        });
                        return
                    }
                    datas={
                        'assignData[classid]':data.assignClass,
                        'assignData[childid]':data.checkList,
                    }
                }else{
                    if(this.newCheckList.length==0){
                        resultTip({
                            error: 'warning',
                            msg: '请勾选学生'
                        });
                        return
                    }
                    datas={
                        'assignData[classid]':this.newAssignClass,
                        'assignData[childid]':this.newCheckList,
                    }
                }
                
                $.ajax({
                    url: '<?php echo $this->createUrl("assignClasses") ?>',
                    type: "post",
                    dataType: 'json',
                    data:datas,
                    success: function(data) {
                        if (data.state == 'success') {
                            that.init()
                            resultTip({
                                msg: data.message
                            });
                            
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                })
            },
            showStatus(list,classid,type){
                if(type=='survey'){
                    this.status=''
                }else{
                    this.status=list.status
                }
                this.editClassid=classid
                this.editType=type
                let level=this.dataList.class_info.find(item2 =>item2.classid === parseInt(classid)).classtype
                if(this.dataList.skip_config.show.by_grade.cond.indexOf(level)!=-1){
                    list.checkedSkip='1'
                    list.checked=this.dataList.skip_config.show.by_grade.default_check
                }else if(this.dataList.skip_config.show.by_uid.cond.map(String).indexOf(this.dataList.uid+"")!=-1){
                    list.checkedSkip='1'
                    list.checked=this.dataList.skip_config.show.by_uid.default_check
                }else{
                    list.checked=false
                }
                this.editChild=list
                $('#contentModal').modal('show')
            },
            newShowStatus(list,classid,type){
                if(this.dataList.skip_config.show.by_uid.cond.map(String).indexOf(this.dataList.uid+"")!=-1){
                    list.checkedSkip='1'
                    list.checked=this.dataList.skip_config.show.by_uid.default_check
                }else{
                    list.checked=false
                }
                this.editType=type
                this.editChild=list
                $('#contentModal').modal('show')
            },
            editStatus(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("setTransitionStatus") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        startyear:this.startyear,
                        childid:this.editChild.childid,
                        status:this.status,
                        memo:this.memo,
                        skip:this.editChild.checked
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.init()
                            resultTip({
                                msg: data.state
                            });
                            $('#contentModal').modal('hide')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                })
            },
            editSurvey(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("confirmTransitionStatus") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        startyear:this.startyear,
                        childid:this.editChild.childid,
                        status:this.status,
                        memo:this.memo,
                        skip:this.editChild.checked
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.init()
                            resultTip({
                                msg: data.state
                            });
                            $('#contentModal').modal('hide')
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                })
            },
            syncSurvey(){
                let that=this
                this.syncBtn=true
                $.ajax({
                    url: '<?php echo $this->createUrl("syncSurveyResult") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        startyear:this.startyear
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.init()
                            resultTip({
                                msg: data.state
                            });
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.syncBtn=false
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                        that.syncBtn=false
                    }
                })
            },
            cancelStatus(list){
                if(list){
                    this.cancelId=list.childid
                    this.cancelType='status'
                    $('#cancelModal').modal('show')
                    return 
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("cancelTransitionStatus") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        startyear:this.startyear,
                        childid:this.cancelId,
                        memo:''
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.init()
                            resultTip({
                                msg: data.state
                            });
                            $('#cancelModal').modal('hide')  
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                })
            },
            cancelClass(list){
                if(list){
                    this.cancelId=list
                    this.cancelType='class'
                    $('#cancelModal').modal('show')
                    return 
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("cancelNextClass") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        startyear:this.startyear,
                        childid:this.cancelId,
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.init()
                            resultTip({
                                msg: data.state
                            });
                            $('#cancelModal').modal('hide')  
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                })
            },
            initCheck(){
                this.dataList.class_info.forEach((item,index) => {
                    item.checked=false
                    item.assignClass=''
                    item.checkList=[]
                })
                Object.values(this.dataList.quit_child_data).forEach((item,index) => {
                    item.checked=false
                    item.assignClass=''
                    item.checkList=[]
                })
            },
            filterSurveyNum(type,init){
                this.initCheck()
                this.filterStatus='survey_result'
                if(this.filterType==type && !init){
                    this.initFilterType()
                }else{
                    this.filterType=type
                    this.allClassList={}
                    for(var key in this.dataList.class_child ){
                        for(let i=0;i<this.dataList.class_child[key].length;i++){
                            if(this.dataList.child_data[this.dataList.class_child[key][i]].status==0 && this.dataList.child_data[this.dataList.class_child[key][i]].survey_result==type){
                                this.allClassList[key]=this.dataList.class_child[key]
                            }
                        }   
                    }
                    if(this.allClassList[0]){
                       delete this.allClassList[0]
                    }
                    this.quit_child_data=[]
                    if(Object.values(this.dataList.quit_child_data).length!=0){
                        for(let i=0;i<Object.values(this.dataList.quit_child_data).length;i++){
                            if(Object.values(this.dataList.quit_child_data)[i].status==0 && Object.values(this.dataList.quit_child_data)[i].survey_result==type){
                                this.quit_child_data.push(Object.values(this.dataList.quit_child_data)[i])
                            }
                        } 
                    }
                }
            },
            initFilterType(){
                this.filterType=''
                this.allClassList=this.dataList.class_child
                this.quit_child_data=Object.values(this.dataList.quit_child_data)
            },
            filterNum(type,init){
                this.initCheck()
                this.filterStatus='status'
                if(this.filterType==type && !init){
                    this.initFilterType()
                }else{
                    this.filterType=type
                    this.allClassList={}
                    for(var key in this.dataList.class_child ){
                        for(let i=0;i<this.dataList.class_child[key].length;i++){
                            if(this.dataList.child_data[this.dataList.class_child[key][i]].status==type){
                                this.allClassList[key]=this.dataList.class_child[key]
                            }
                        }   
                    }
                    if(this.allClassList[0]){
                        for(let i=0;i<this.allClassList[0].length;i++){
                            if(this.dataList.child_data[this.allClassList[0][i]].status==type){
                                this.allClassList[key]=this.allClassList[0]
                            }
                        } 
                    }
                    this.quit_child_data=[]
                    if(Object.values(this.dataList.quit_child_data).length!=0){
                        for(let i=0;i<Object.values(this.dataList.quit_child_data).length;i++){
                            if(Object.values(this.dataList.quit_child_data)[i].status==type){
                                this.quit_child_data.push(Object.values(this.dataList.quit_child_data)[i])
                            }
                        } 
                    }
                }         
            },
            nextClass(type,init){
                this.initCheck()
                this.filterStatus='next_class_id'
                if(this.filterType==type && !init){
                    this.initFilterType()
                }else{
                    this.filterType=type
                    this.allClassList={}
                    for(var key in this.dataList.class_child ){
                        for(let i=0;i<this.dataList.class_child[key].length;i++){
                            if(this.filterType==6 && this.dataList.child_data[this.dataList.class_child[key][i]].next_class_id==null){
                                this.allClassList[key]=this.dataList.class_child[key]
                            }else if(this.filterType==7 && this.dataList.child_data[this.dataList.class_child[key][i]].next_class_id!=null){
                                this.allClassList[key]=this.dataList.class_child[key]
                            }
                        }   
                    }
                    this.quit_child_data=[]
                    if(Object.values(this.dataList.quit_child_data).length!=0){
                        for(let i=0;i<Object.values(this.dataList.quit_child_data).length;i++){
                            if(this.filterType==6 && Object.values(this.dataList.quit_child_data)[i].next_class_id==null){
                                this.quit_child_data.push(Object.values(this.dataList.quit_child_data)[i])
                            }else if(this.filterType==7 && Object.values(this.dataList.quit_child_data)[i].next_class_id!=null){
                                this.quit_child_data.push(Object.values(this.dataList.quit_child_data)[i])
                            }
                        } 
                    }
                }         
            },
            filterDeposit(type,init){
                this.initCheck()
                this.filterStatus='deposit'
                if(this.filterType==type && !init){
                    this.initFilterType()
                }else{
                    this.filterType=type
                    this.allClassList={}
                    for(var key in this.dataList.class_child ){
                        for(let i=0;i<this.dataList.class_child[key].length;i++){
                            if(this.filterType==8 && (this.dataList.child_data[this.dataList.class_child[key][i]].deposit || this.dataList.child_data[this.dataList.class_child[key][i]].tuition)){
                                this.allClassList[key]=this.dataList.class_child[key]
                            }
                        }   
                    }
                    this.quit_child_data=[]
                    if(Object.values(this.dataList.quit_child_data).length!=0){
                        for(let i=0;i<Object.values(this.dataList.quit_child_data).length;i++){
                            if(this.filterType==8 && (Object.values(this.dataList.quit_child_data)[i].deposit || Object.values(this.dataList.quit_child_data)[i].tuition) ){
                                this.quit_child_data.push(Object.values(this.dataList.quit_child_data)[i])
                            }
                        } 
                    }
                }         
            },
            shouldRenderChild(filterType, childData, filterStatus) {
                if (filterType == 6) {
                    return childData.next_class_id == null;
                }
                if (filterType == 7) {
                    return childData.next_class_id != null;
                }
                if (filterType == 8) {
                    return childData.deposit || childData.tuition;
                }
                return (
                    filterType == childData[filterStatus] &&
                    (filterStatus !== 'survey_result' || childData.status == 0)
                );
            },
            formatDate(date) {
                const year = date.getFullYear();
                const month = (date.getMonth() + 1).toString().padStart(2, '0');
                const day = date.getDate().toString().padStart(2, '0');
                return `${year}-${month}-${day}`;
            },
            statusRatio(){
                const today = new Date();
                const yesterday = new Date(today);
                yesterday.setDate(yesterday.getDate() - 1);
                this.endDate= this.formatDate(today);
                this.startDate = this.formatDate(yesterday);
                this.ratioData={}
                $('#statusRatioModal').modal('show')
            },
            dataRatio(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getDsTransitionComparisonStatus") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        days:[this.startDate,this.endDate]
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                           that.ratioData=data.data
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    }
                })
            },
            exportRatio(){
                var wb = XLSX.utils.book_new();
                var exportDatas=[]
                const filename =this.startDate+'_'+this.endDate+'状态对比';
                for(var i=0;i<this.ratioData.list.length;i++){
                    let startText=''
                    if(this.ratioData.list[i].startStatus==0){
                        startText= this.ratioData.list[i].startSurveyResult==4?"问卷结果：不确定":this.ratioData.list[i].startSurveyResult==5?"问卷结果：未填写":''
                    }else{
                        startText= this.ratioData.list[i].startStatus==1?"下学年返校":this.ratioData.list[i].startStatus==2?"下学年不返校":this.ratioData.list[i].startStatus==3?'本学年将退学':''
                    }
                    exportDatas.push([
                        this.ratioData.childInfo[this.ratioData.list[i].childId].name,
                        startText,
                        this.ratioData.list[i].endStatus==1?"下学年返校":this.ratioData.list[i].endStatus==2?"下学年不返校":this.ratioData.list[i].endStatus==3?'本学年将退学':'',
                        this.ratioData.staffInfo[this.ratioData.list[i].log.uid].name+'|'+ this.ratioData.list[i].log.log_time
                    ])
                }
                var header=[['学生',this.startDate,this.endDate,'最后操作人']].concat(exportDatas)
                var ws = XLSX.utils.aoa_to_sheet(header);
                XLSX.utils.book_append_sheet(wb, ws, "Sheet1");
                XLSX.writeFile(wb, filename+".xlsx");
            }
        }
    })
    </script>