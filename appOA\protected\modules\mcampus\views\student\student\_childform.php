<div class="form-group" model-attribute="name_cn">
    <?php echo CHtml::label($clabels['name_cn'], CHtml::getIdByName('ChildProfileBasic[name_cn]'), array('class'=>'col-sm-3 control-label'));?>
    <div class="col-sm-9">
        <?php echo CHtml::textField('ChildProfileBasic[name_cn]','',array('placeholder'=>$clabels['name_cn'], 'class'=>'form-control'));?>
    </div>
</div>

<div class="form-group">
    <?php echo CHtml::label($clabels['englishName'], CHtml::getIdByName('ChildProfileBasic[first_name_en]'), array('class'=>'col-sm-3 control-label'));?>
    <div class="col-sm-9">
        <div class="col-sm-4">
            <div class="form-group" model-attribute="first_name_en">
                <?php echo CHtml::textField('ChildProfileBasic[first_name_en]', '', array('placeholder' => $clabels['first_name_en'], 'class' => 'form-control')); ?>
            </div>
        </div>
        <div class="col-sm-4">
            <div class="form-group" model-attribute="middle_name_en">
                <?php echo CHtml::textField('ChildProfileBasic[middle_name_en]', '', array('placeholder' => $clabels['middle_name_en'], 'class' => 'form-control')); ?>
            </div>
        </div>
        <div class="col-sm-4">
            <div class="form-group" model-attribute="last_name_en">
                <?php echo CHtml::textField('ChildProfileBasic[last_name_en]', '', array('placeholder' => $clabels['last_name_en'], 'class' => 'form-control')); ?>
            </div>
        </div>
    </div>
</div>

<div class="form-group" model-attribute="nick">
    <?php echo CHtml::label($clabels['nick'], CHtml::getIdByName('ChildProfileBasic[nick]'), array('class'=>'col-sm-3 control-label'));?>
    <div class="col-sm-9">
        <?php echo CHtml::textField('ChildProfileBasic[nick]','',array('placeholder'=>$clabels['nick'], 'class'=>'form-control '));?>
    </div>
</div>

<div class="form-group" model-attribute="gender">
    <?php echo CHtml::label($clabels['gender'], CHtml::getIdByName('ChildProfileBasic[gender]'), array('class'=>'col-sm-3 control-label'));?>
    <div class="col-sm-9">
        <?php echo CHtml::radioButtonList('ChildProfileBasic[gender]',null,array('1'=>Yii::t('child','Male'),'2'=>Yii::t('child','Female')),
            array('template'=>'<label class="radio-inline">{input}{labelTitle}</label>','separator'=> ' ', 'encode'=>false)
        );?>
    </div>
</div>

<div class="form-group" model-attribute="birthday_search">
    <?php echo CHtml::label($clabels['birthday_search'], CHtml::getIdByName('ChildProfileBasic[birthday_search]'), array('class'=>'col-sm-3 control-label'));?>
    <div class="col-sm-9">
        <?php echo CHtml::textField('ChildProfileBasic[birthday_search]','',array('placeholder'=>$clabels['birthday_search'], 'class'=>'form-control length_2'));?>
    </div>
</div>

<div class="form-group" model-attribute="country">
    <?php echo CHtml::label($clabels['country'], CHtml::getIdByName('ChildProfileBasic[country]'), array('class'=>'col-sm-3 control-label'));?>
    <div class="col-sm-9">
        <?php echo CHtml::dropDownList('ChildProfileBasic[country]', '', Country::getData(), array('class'=>'form-control','empty'=>Yii::t('global','Please Select')));?>
    </div>
</div>

<div class="form-group" model-attribute="identity">
    <?php echo CHtml::label($clabels['identity'], CHtml::getIdByName('ChildProfileBasic[identity]'), array('class'=>'col-sm-3 control-label'));?>
    <div class="col-sm-9">
        <?php echo CHtml::textField('ChildProfileBasic[identity]','',array('placeholder'=>$clabels['identity'], 'class'=>'form-control '));?>
    </div>
</div>

<div class="form-group" model-attribute="lang">
    <?php echo CHtml::label($clabels['lang'], CHtml::getIdByName('ChildProfileBasic[lang]'), array('class'=>'col-sm-3 control-label'));?>
    <div class="col-sm-9">
        <?php echo CHtml::dropDownList('ChildProfileBasic[lang]','',CHtml::listData($langModels,'diglossia_id','cntitle'),array('class'=>'form-control','empty'=>Yii::t('global','Please Select')));?>
    </div>
</div>

<div class="form-group">
    <?php echo CHtml::label($clabels['schoolid'], null, array('class'=>'col-sm-3 control-label'));?>
    <div class="col-sm-9">
        <?php echo CHtml::textField('campusDisplay',$branches[$this->branchId]['title'],array('class'=>'form-control length_2','readonly'=>'readonly'));?>
    </div>
</div>

<div class="form-group" model-attribute="est_enter_date">
    <?php echo CHtml::label($clabels['est_enter_date'], CHtml::getIdByName('ChildProfileBasic[est_enter_date]'), array('class'=>'col-sm-3 control-label'));?>
    <div class="col-sm-9">
        <?php echo CHtml::textField('ChildProfileBasic[est_enter_date]','',array('placeholder'=>$clabels['est_enter_date'], 'class'=>'form-control length_2'));?>
    </div>
</div>

<script>
    $('#ChildProfileBasic_name_cn').blur(function(){
        var obj = $(this);
        var name = obj.val().trim();
        obj.parent().find('div.alert').remove();
        if(name){
            $.getJSON('<?php echo $this->createUrl('checkChild')?>', {name: name}, function(data){
                if(!$.isEmptyObject(data)){
                    var html = '<div class="alert alert-warning" role="alert">';
                    for(var i in data){
                        html += '<p><strong>同名孩子（注意请勿重复添加）</strong> '+data[i].name+' '+data[i].schoolabb+' '+data[i].birthday+'</p>';
                    }
                    html += '</div>';

                    obj.parent().append(html);
                }
                else{
                    obj.parent().find('div.alert').remove();
                }
            });
        }
        else{
            obj.parent().find('div.alert').remove();
        }
    });
</script>