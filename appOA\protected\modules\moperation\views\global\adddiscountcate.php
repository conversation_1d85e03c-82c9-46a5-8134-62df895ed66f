<?php $form=$this->beginWidget('CActiveForm', array(
    'id'=>'points-product-form',
    'enableAjaxValidation'=>false,
    'htmlOptions'=>array('class'=>'J_ajaxForm form-horizontal', 'role'=>'form'),
)); ?>
    <div class="pop_cont">
        <div class="form-group">
            <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'title_en'); ?></label>
            <div class="col-xs-9">
                <?php echo $form->textField($model, 'title_en', array('class'=>'form-control'));?>
            </div>
        </div>
        <div class="form-group">
            <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'title_cn'); ?></label>
            <div class="col-xs-9">
                <?php echo $form->textField($model, 'title_cn', array('class'=>'form-control'));?>
            </div>
        </div>
        <div class="form-group">
            <label class="col-xs-3 control-label"><?php echo $form->labelEx($model,'stat'); ?></label>
            <div class="col-xs-9">
                <?php echo $form->checkBox($model, 'stat', array('value'=>10));?>
            </div>
        </div>
    </div>
    <div class="pop_bottom">
        <button id="J_dialog_close" type="button" class="btn btn-default pull-right"><?php echo Yii::t('global','Cancel');?></button>
        <button type="submit" class="btn btn-primary J_ajax_submit_btn pull-right mr10"><?php echo Yii::t('global','Submit');?></button>
    </div>
<?php $this->endWidget(); ?>