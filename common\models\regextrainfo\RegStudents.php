<?php

/**
 * This is the model class for table "ivy_reg_students".
 *
 * The followings are the available columns in table 'ivy_reg_students':
 * @property integer $id
 * @property integer $reg_id
 * @property integer $classid
 * @property integer $childid
 * @property integer $status
 * @property integer $is_selected
 * @property integer $updated_by
 * @property integer $updated_at
 * @property integer $schoolid
 * @property integer $start_time
 * @property integer $end_time
 */
class RegStudents extends CActiveRecord
{
    const STATUS_DEFAULT = 0;
    const STATUS_CONDUCT = 1;
    const STATUS_COMPLETE = 2;
    const STATUS_AUDITED = 3;
    const STATUS_OVERRULE = 4;
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_reg_students';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('reg_id, classid, schoolid, is_selected, start_time, end_time, childid, status, updated_by, updated_at', 'required'),
			array('reg_id, classid, childid, is_selected, start_time, end_time, status, updated_by, updated_at', 'numerical', 'integerOnly'=>true),
			// The following rule is used by search().
            array('schoolid', 'length', 'max'=>255),
			// @todo Please remove those attributes that should not be searched.
			array('id, reg_id, classid, childid, status, updated_by, updated_at', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'childProfile'=>array(self::HAS_ONE, 'ChildProfileBasic', array('childid'=>'childid')),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'reg_id' => 'Reg',
			'classid' => 'Classid',
			'childid' => 'Childid',
			'status' => 'Status',
			'is_selected' => 'IS Selected',
			'updated_by' => 'Updated By',
			'updated_at' => 'Updated At',
			'schoolid' => 'schoolid',
			'start_time' => 'start_time',
			'end_time' => 'end_time',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('reg_id',$this->reg_id);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('status',$this->status);
		$criteria->compare('is_selected',$this->is_selected);
		$criteria->compare('updated_by',$this->updated_by);
		$criteria->compare('updated_at',$this->updated_at);
		$criteria->compare('start_time',$this->start_time);
		$criteria->compare('end_time',$this->end_time);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return RegStudents the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * [getStudentnum 根据regID获取整个调查所有人数]
	 * @param  [type]  $regId  [description]
	 * @param  boolean $status [description]
	 * @return [type]          [description]
	 */
	public static function getStudentnum($regId, $status = false)
	{
		$criteria = new CDbCriteria();
		$criteria->compare('reg_id', $regId);
		$criteria->compare('is_selected', 1);
		if ($status !== false) {
			$criteria->compare('complete', $status);
		}
		$count = RegStudents::model()->count($criteria);
		return $count;
	}

	/**
	 * [getStudentnum 根据regID某个步骤下的数据]
	 * @param  [type]  $regId  [description]
	 * @param  boolean $step [第几步]
	 * @param  boolean $complete [是否完成]
	 * @return [type]          [description]
	 */
	public static function getStudentnumByStep($regId, $step = false, $complete = false)
	{
		$criteria = new CDbCriteria();
		$criteria->compare('t.reg_id', $regId);
		$criteria->compare('t.`status`', 1);
		$criteria->compare('student.is_selected', 1);
		$criteria->with = 'student';
		if ($step !== false) {
			$criteria->compare('t.step', $step);
		}
		if ($complete !== false) {
			$criteria->compare('t.complete', $complete);
		}
		$data = RegExtraInfo::model()->findAll($criteria);
		return $data;
	}


    public function getNameList($childid, $schoolType)
    {
        $childObjs = ChildProfileBasic::model()->findAllByPk($childid);
        $parents = array();
        foreach ($childObjs as $item){
            $parents[$item->fid] = $item->fid;
            $parents[$item->mid] = $item->mid;
        }
        $parentOpenid = array();
        $parentinfo = array();

        Yii::import('common.models.wechat.*');
        $crit = new CDbCriteria();
        $crit->compare('userid', $parents);
        $crit->compare('valid', 1);
        if ($schoolType == 50) {
            $crit->compare('account', 'ds');
        } else {
            $crit->compare('account', 'ivy');
        }
        $crit->with = 'info';
        $wechatModel = WechatUser::model()->findAll($crit);
        ;
        foreach ($wechatModel as $val) {
            $parentOpenid[$val->userid] = 1;
            if($val->info){
                $information = json_decode($val->info->info);
                $parentinfo[$val->userid][] = array(
                    'name' => $information->nickname,
                    'headimgurl' => $information->headimgurl,
                );
            }
        }

        foreach($childObjs as $child){
            $bindingStatus = 0;
            $fidArray = array();
            $midArray = array();
            if (isset($parentOpenid)) {
                if ($parentOpenid[$child->fid] || $parentOpenid[$child->mid]) {
                    $bindingStatus = 1;
                }
            }
            if ($child->fid && $parentinfo[$child->fid]) {
                $fidArray = $parentinfo[$child->fid];
            }
            if ($child->mid && $parentinfo[$child->mid]) {
                $midArray = $parentinfo[$child->mid];
            }

            $ret[] = array(
                'id' => $child->childid,
                'name' => ( Yii::app()->language == 'en_us' )?
                    $child->getChildName(null,null, true):
                    $child->getChildName(true, true),
                'photo' => CommonUtils::childPhotoUrl($child->photo, 'small'),
                'ccUrl' => OA::genCCUrlHome($child->childid),
                'bindingStatus' => $bindingStatus,
                'parent' => array_merge($fidArray,$midArray),
            );
        }

        return $ret;
    }

    // 根据 info 表更新孩子当前调查状态
    public function updateStatus()
    {
    	// 查找是否存在驳回的步骤
    	$infos = RegExtraInfo::model()->findAllByAttributes(array('childid' => $this->childid, 'reg_id' => $this->reg_id, 'status' => 1, 'schoolid'=>$this->schoolid));
    	$count = count($infos);
    	if ($count == 0) {
    		$status = 0;
    	} else {
    		$status = 1;
    		$flag = false;
    		$noComplete = false;
    		// 查找是否存在驳回的步骤
    		foreach ($infos as $info) {
    			if ($info->complete != 1) {
    				$noComplete = true;
    			}
    			if ($info->complete == -1) {
    				$flag = true;
    				break;
    			}
    		}
    		if ($flag == true) {
    			$status = 4;
    		} else {
	    		if ($count == count(RegExtraInfo::getStepsBySchool($this->schoolid))) {
	    			if ($noComplete) {
	    				$status = 2;
	    			}else{
		    			$status = 3;
	    			}
	    		}
    		}
    	}
    	$this->status = $status;
    	$this->save();
    }

    // 获取有效的新生注册
    public static function getActive($schoolid, $childid)
    {
    	$criteria = new CDbCriteria();
    	if (!is_null($schoolid)) {
	    	$criteria->compare('schoolid', $schoolid);
    	}
    	$criteria->compare('childid', $childid);
    	$criteria->compare('is_selected', 1);
    	$criteria->order = 'updated_at DESC';
    	$model = RegStudents::model()->find($criteria);
    	return $model; 
    }

    public static function getStudentConfig()
    {
        $all = array(
            self::STATUS_DEFAULT => Yii::t('reg', 'pending for register'),
            self::STATUS_CONDUCT => Yii::t('reg', 'In progress'),
            self::STATUS_COMPLETE => Yii::t('reg', 'It has been submitted'),
            self::STATUS_AUDITED => Yii::t('reg', 'It has been processed'),
            self::STATUS_OVERRULE => Yii::t('reg', 'Information to be revised'),
        );
        return $all;
    }
}
