# GlobalState

Snapshotting of global state, factored out of PHPUnit into a stand-alone component.

[![Build Status](https://travis-ci.org/sebastian<PERSON>mann/global-state.svg?branch=master)](https://travis-ci.org/sebastianbergmann/global-state)

## Installation

To add this package as a local, per-project dependency to your project, simply add a dependency on `sebastian/global-state` to your project's `composer.json` file. Here is a minimal example of a `composer.json` file that just defines a dependency on GlobalState:

    {
        "require": {
            "sebastian/global-state": "1.0.*"
        }
    }
