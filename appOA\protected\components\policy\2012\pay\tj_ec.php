<?php
if(!defined("IVY_POLICY"))
throw new CException('DO NOT USE THIS FILE DIRECTLY');

return array( 'policy'=>array(

	//是否老生老办法
	//ENABLE_CONSTANT_TUITION => false,
	
	//年保育费
	//ANNUAL_CHILDCARE_AMOUNT => 8000,
	
	//是否开放课外活动账单
	ENABLE_AFTERSCHOOLS => false,
	
	//图书馆工本费
	FEETYPE_LIBCARD => 10,
		
	//学期四个时间点；取前后两端
	TIME_POINTS   => array(201209,201212,201301,201308),
		
	//寒暑假月份，及其每月计费天数
	//HOLIDAY_MONTH => array(201402=>20,201408=>20),
	
	//年付开通哪些缴费类型
	/*
	PAYGROUP_ANNUAL => array(
		FEETYPE_TUITION,
		FEETYPE_LUNCH,
		FEETYPE_SCHOOLBUS,
	),
	*/
		
	//学期付开通哪些缴费类型
	PAYGROUP_SEMESTER => array(
		ITEMS => array(
			FEETYPE_TUITION,
			FEETYPE_LUNCH,
			FEETYPE_SCHOOLBUS,		
		),
		TUITION_CALC_BASE => BY_MONTH,
	),
	
	//月份开通哪些缴费类型
	PAYGROUP_MONTH => array(
		ITEMS => array(
			FEETYPE_TUITION,
			FEETYPE_LUNCH,
			FEETYPE_SCHOOLBUS,		
		),
		TUITION_CALC_BASE => BY_MONTH,
	),
	
	//计算基准：BY_MONTH, 或者 BY_SETTING, 前者表示所有的学费都基于月费用计算，后者表示所有的学费都基于学期或学年
	TUITION_CALCULATION_BASE => array(
	
		BY_MONTH => array(
			
			//月收费金额
			AMOUNT => array(
				FULL5D => 1450,
			),
			
			//收费月份，及其权重
			MONTH_FACTOR => array(
				201209=>1,
				201210=>1,
				201211=>1,
				201212=>1,
				201301=>1,
				201302=>1,
				201303=>1,
				201304=>1,
				201305=>1,
				201306=>1,
				201307=>1,
				201308=>1
			),

			//依次执行，直至遇到满足条件的项
			PRE_FILTER => array(
				//外籍孩子
				'foreigner' => array(AMOUNT=>2450),
				'discounts' => array(
					//补贴500，需缴纳 950
					'150'=>array(
						AMOUNT=>950,
						'linkedInvoice'=>array(
							array(
								AMOUNT=>500,
								FEETYPE => FEETYPE_SUBSIDY,
								DISCOUNTID => 150,
							)
						)
					),
					//补贴1000，需缴纳 450
					'162'=>array(
						AMOUNT=>450,
						'linkedInvoice'=>array(
							array(
								AMOUNT=>1000,
								FEETYPE => FEETYPE_SUBSIDY,
								DISCOUNTID => 162,
							)
						)
					),
					//补贴0，需缴纳 1450
					/*
					'164'=>array(
						AMOUNT=>1450,
						//'linkedInvoice'=>array(
							//"amount"=>0, //0不生成新账单
						//)
					),
					*/
				),
				//外小区孩子
				'default' => array(
					AMOUNT=>1450,
					'linkedInvoice'=>array(
						array(
							AMOUNT=>1000,
							FEETYPE=> FEETYPE_COLLECTION
						)
					)
				)
			),
			
			//折扣
			/*
			GLOBAL_DISCOUNT => array(
				DISCOUNT_ANNUAL => '0.97:10:2013-12-01'
			)
			*/
		),
		
		//东湖配置实例
		/*
		BY_ANNUAL => array(
			AMOUNT => array(
				FULL5D => 146030,
				HALF2D => 52570,
				HALF3D => 75700,
				HALF5D => 105140,
			),		
		),
		
		BY_SEMESTER1 => array(
			AMOUNT => array(
				FULL5D => 60220,
				HALF2D => 21680,
				HALF3D => 31220,
				HALF5D => 43360,
			),		
		),
		
		BY_SEMESTER2 => array(
			AMOUNT => array(
				FULL5D => 90330,
				HALF2D => 32520,
				HALF3D => 46830,
				HALF5D => 65040,
			),		
		),
		*/
	),
	
	//每餐费用
	//LUNCH_PERDAY => 25,
	
	//账单到期日和账单开始日之差
	DUEDAYS_OFFSET => 0,
	
));

?>