<?php

/**
 * This is the model class for table "ivy_points_product".
 *
 * The followings are the available columns in table 'ivy_points_product':
 * @property integer $id
 * @property string $en_title
 * @property string $cn_title
 * @property string $en_memo
 * @property string $cn_memo
 * @property integer $credits
 * @property double $p_price
 * @property double $m_price
 * @property integer $status
 * @property integer $cover
 * @property integer $userid
 * @property integer $updated
 */
class PointsProduct extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return PointsProduct the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_points_product';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('en_title, cn_title, credits, p_price, m_price,status', 'required'),
			array('credits, status, cover, userid, updated', 'numerical', 'integerOnly'=>true),
			array('p_price, m_price', 'numerical'),
			array('en_title, cn_title', 'length', 'max'=>255),
			array('en_memo, cn_memo', 'safe'),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, en_title, cn_title, en_memo, cn_memo, credits, p_price, m_price, status, cover, stock, userid, updated', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'user'=>array(self::BELONGS_TO, 'User', 'userid'),
            'Image'=>array(self::BELONGS_TO, 'PointsImages', 'cover','select'=>'image'),
            'Images'=>array(self::HAS_MANY, 'PointsImages', 'productid','select'=>'image','index'=>'id','order'=>'weight Asc'),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => Yii::t('points', '标识'),
			'en_title' => Yii::t('points', '英文名称'),
			'cn_title' => Yii::t('points', '中文名称'),
			'en_memo' => Yii::t('points', '英文备注'),
			'cn_memo' => Yii::t('points', '中文备注'),
			'credits' => Yii::t('points', '积分'),
			'p_price' => Yii::t('points', '采购价'),
			'm_price' => Yii::t('points', '市场价'),
			'status' => Yii::t('points', '状态'),
			'cover' => Yii::t('points', '封面'),
			'stock' => Yii::t('points', '库存'),
			'userid' => Yii::t('points', '操作用户'),
			'updated' => Yii::t('points', '更新日期'),
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('en_title',$this->en_title,true);
		$criteria->compare('cn_title',$this->cn_title,true);
		$criteria->compare('en_memo',$this->en_memo,true);
		$criteria->compare('cn_memo',$this->cn_memo,true);
		$criteria->compare('credits',$this->credits);
		$criteria->compare('p_price',$this->p_price);
		$criteria->compare('m_price',$this->m_price);
		$criteria->compare('status',$this->status);
		$criteria->compare('cover',$this->cover);
		$criteria->compare('stock',$this->stock);
		$criteria->compare('user.uname',$this->userid, true);
		$criteria->compare('updated',$this->updated);
//        $criteria->order='updated DESC';
        
        $criteria->with=array('user','Image');

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
            'pagination'=>array(
                // 'pageSize'=>200,
            ),
            'sort'=>array(
                'defaultOrder'=>'t.status DESC,t.id',
            ),
		));
	}
	
	
	public function showStatus()
	{
		return ($this->status) ? Yii::t('points','显示') : Yii::t('points','隐藏');
	}

	/**
	 * 根据条件过滤产品
	 * @param double $maxCredits
	 * @param int $status
	 * @return ArrayObject
	 * <AUTHOR>
	 * @copyright ivygroup
	 */
	public function getProductList($maxCredits=0,$status=0)
	{
		$criteria = new CDbCriteria();
		if ($status)
		{
			$criteria->compare('status', $status);
		}
		if ($maxCredits)
		{
			$criteria->addCondition('credits <='.$maxCredits);
		}
		return $this->model()->findAll($criteria);
	}
	
 	/**
 	 * 根据参数取得产品的中英文内容
 	 * @param string $type
 	 * @return string
 	 */
 	public function getContent($type = "title") {
        switch (strtolower($type)) {
            case "title":
                return ( Yii::app()->language == "zh_cn" ) ? $this->cn_title : $this->en_title;
                break;
            case "memo":
                return ( Yii::app()->language == "zh_cn" ) ? $this->cn_memo : $this->en_memo;
                break;
        }
        return false;
    }
    
    /**
     * 更新某个产品的当前库存
     * @param int $productId
     * @param int $stock
     * @return boolean
     */
    public function updateStock($productId,$stock)
    {
    	$model = $this->findByPk($productId);
    	$model->stock = $stock;
    	if ($model->save())
    	{
    		return true;
    	}
    	return false;
    }
    
    public function showCover($tag='', $alt='', $htmlOptions=array())
    {
        if (isset($this->Image) && $this->Image)
            echo CHtml::openTag($tag).CHtml::image(Yii::app()->params['OAUploadBaseUrl'].'/points/thumb/'
                    .$this->Image->image, $alt, $htmlOptions).CHtml::closeTag($tag);
    }
}