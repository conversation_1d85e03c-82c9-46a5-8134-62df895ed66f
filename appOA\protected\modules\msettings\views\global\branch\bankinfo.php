<?php
$bankInfoCat = CHtml::listData(Term::model()->bankinfo()->findAll(), 'diglossia_id', function($term){return $term->entitle.'<br>'.$term->cntitle;});

$emptyData = array();
foreach(array_keys($bankInfoCat) as $_id){
    $emptyData['d'.$_id] = array('display'=>0, 'tdata'=>'');
}

$bModel = Branch::model();
$bModel->getMetaData()->addRelation(
    'bankInfo',
    array(
        CActiveRecord::HAS_ONE,
        'BranchVar',
        'branchid',
        'on'=>'category=:category',
        'params'=>array(':category'=>'bankInfo'),
    )
);
$crit = new CDbCriteria();
$crit->index = 'branchid';
$crit->compare('type', '<>'.Branch::TYPE_OFFICE);
$crit->compare('status', Branch::STATUS_ACTIVE);
$crit->order = '`group` ASC';
$branches = $bModel->with('bankInfo')->findAll($crit);
?>
<div class="col-md-1">
    <div class="list-group" id="branch-list">
        <?php
        foreach($branches as $branch):
            if($branch->bankInfo == null){
                $branch->bankInfo = new BranchVar();
                $branch->bankInfo->data = base64_encode(serialize($emptyData));
            }


//            //模拟数据，下面这个base64之后的东西存在数据库里
//            $branch->bankInfo->data = base64_encode(serialize(
//                array(
//                'd150' => array('display'=>1, 'tdata'=>$branch->title . ' testing testing'),
//                'd151' => array('display'=>1, 'tdata'=>$branch->title . ' testing testing'),
//                'd152' => array('display'=>1, 'tdata'=>$branch->title . ' testing testing'),
//                'd153' => array('display'=>1, 'tdata'=>$branch->title . ' testing testing'),
//                'd149' => array('display'=>1, 'tdata'=>$branch->title . ' testing testing')
//                )
//            ));
//
//            echo $branch->bankInfo->data;

            $_title = '<span class="glyphicon glyphicon-tags program'.$branch->group.'"></span> ';
            $_title .= $branch->abb;
            echo CHtml::link($_title,'javascript:void(0)', array('branchid'=>$branch->branchid,'class'=>'list-group-item'));
            $branchData[$branch->branchid] = unserialize(base64_decode($branch->bankInfo->data));
        endforeach;
        ?>
    </div>
</div>

<script type="text/template" id="branch-form-template">
    <div class="col-md-12">
        <table class="table">
            <thead>
                <th style="width:80px"></th>
                <th style="width: 20%">类别</th>
                <th>内容</th>
            </thead>
            <tbody>
            <?php
            foreach($bankInfoCat as $_tid => $_text):
            ?>
                <tr>
                    <th><div class="checkbox"><label><?php echo CHtml::checkBox(sprintf("tid[%s]", $_tid)); ?> 显示</label></div></th>
                    <td><?php echo $_text; ?></td>
                    <td><?php echo CHtml::textArea(sprintf("tdata[%s]", $_tid), '<% print(typeof(d'.$_tid.') != "undefined" ? d'.$_tid.'.tdata : "") %>',array('class'=>"form-control","rows"=>5,"encode"=>false)) ?></td>
                </tr>
            <?php
            endforeach;
            ?>
            </tbody>
        </table>
    </div>
</script>


<div class="col-md-10">
    <?php echo CHtml::beginForm('', 'post', array('class'=>'J_ajaxForm form-horizontal')); ?>

    <div class="row" id="form-content">
        <!--place holder-->
    </div>

    <div class="row hidden" id="submit-box">
        <div class="col-md-12">
            <button type="submit" class="btn btn-primary J_ajax_submit_btn mb10"><?php echo Yii::t('global','Submit');?></button>
            <input type="hidden" id="branchid" name="branchid" value="">
        </div>
    </div>

    <?php echo CHtml::endForm(); ?>
</div>

<script>
    var branchData=<?php echo CJSON::encode($branchData)?>;
    var currentBranchData;
    var template = _.template($('#branch-form-template').html());
    $(function(){
        showBranchForm=function(obj){
            var view = template(branchData[$(obj).attr('branchid')]);
            $("#form-content").html(view);
        };
        $('#branch-list a').click(function(){
            $('#branch-list a').removeClass('background-gray');
            $(this).addClass('background-gray');
            $('#submit-box').removeClass('hidden');
            var branchid = $(this).attr('branchid');
            $('#submit-box #branchid').val(branchid);
            showBranchForm(this);
            for(var vv in branchData[branchid]){
                if(branchData[branchid][vv]['display'] == 1){
                    var num = vv.substr(1);
                    $('#form-content #tid_'+num).attr('checked', true);
                }
            }
        });

    });

    function callback(data)
    {
        branchData[data.branchid]=data.data;
    }
</script>