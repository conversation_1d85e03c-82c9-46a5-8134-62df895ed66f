<?php
$type = AttendStatistics::getConfig();
?>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Operations'), array('//mcampus/default/index')) ?></li>
        <li><?php echo CHtml::link(Yii::t('site', 'Basic'), array('//mcampus/default/index')) ?></li>
        <?php if(Yii::app()->params['siteFlag'] == 'daystar'){?>
            <li><?php echo CHtml::link(Yii::t('site', 'Student Attendance Management'), array('//mcampus/childsign/index2')) ?></li>
        <?php }else{?>
            <li><?php echo CHtml::link(Yii::t('site', 'Student Attendance Management'), array('//mcampus/childsign/index')) ?></li>
        <?php }?>
        <li class="active"><?php echo Yii::t('campus', 'List of students on leave/tardy'); ?></li>
    </ol>

    <div class="row">
        <!-- 左侧菜单 -->
        <?php
        $this->renderPartial('/childsign/left');
        ?>
        <div class="col-md-10 col-sm-12">
            <div class="mb10 row">
                <!-- 搜索框 -->
                <form action="<?php echo $this->createUrl('report'); ?>" method="get">
                    <?php echo Chtml::hiddenField('branchId', $this->branchId); ?>
                    <div class="col-md-8 row">
                        <div class="form-group col-sm-3">
                            <?php echo Chtml::dropDownList('class', $_GET['class'], $classAry, array('class' => 'form-control', 'empty' => Yii::t('campus', 'All classes'))); ?>
                        </div>
                        <div class="form-group col-sm-3">
                            <?php echo Chtml::dropDownList('date', $date, $timeList, array('class' => 'form-control')); ?>
                        </div>
                        <div class="form-group col-sm-1">
                            <button class="btn btn-default" type="submit"><span
                                    class="glyphicon glyphicon-search"></span></button>
                        </div>
                        <?php if (Yii::app()->request->getParam('vacation_day', '')) { ?>
                            <a class="btn btn-info"
                               href="<?php echo $this->createUrl('exporyVacation', array('type' => $_GET['type'], 'vacation_day' => Yii::app()->request->getParam('vacation_day', ''), 'search' => $_GET['search'])); ?>"><?php echo Yii::t('user', 'Export') ?></a>
                        <?php } ?>
                    </div>
                </form>
            </div>

            <?php
            foreach ($classAry as $classid => $classtitle) {
                if (isset($attendModel[$classid])) {
                    $attendModelitems = $attendModel[$classid];

                if (json_decode($attendModelitems->data, true)) {
            ?>
            <div class="panel panel-default">
                <div class="panel-heading headBg">
                    <h4><?php echo $classtitle; ?>
                        <a class="pull-right"
                           href="<?php echo $this->createUrl('printReport', array('id' => $attendModelitems->id)) ?>"
                           target="_black"><?php echo Yii::t('global', 'Print') ?></a>
                    </h4>
                </div>
                <?php if(date("Ym",time()) != $date): ?>
                <div class="panel-body">
                        <?php echo Yii::t('campus', 'Please click Regenerate if there is any change in class attendance this month') ?>
                            <a class="btn btn-default btn-xs" href="javascript:;"
                            onclick="updateAttend(<?php echo $attendModelitems->id ?>)"><?php echo Yii::t('campus', 'Regenerate') ?></a>


                    <div class="clearfix"></div>
                </div>
                <?php endif; ?>
                    <div class="table-responsive" style="overflow-y: hidden;overflow-x: auto;">
                    <table class="table table-bordered" style="min-width: 1020px;">
                        <tr  class='bg'>
                            <th>#</th>
                            <th width="80"><?php echo Yii::t('reg', 'Name') ?> </th>
                            <?php foreach ($schoolDayByClass[$classid] as $k => $item) { ?>
                                <th class="text-center"><?php echo $item . " " . date('D', $k) ?></th>
                            <?php } ?>
                            <th width="50"><?php echo Yii::t('campus', 'Tardy days') ?></th>
                            <th width="50"><?php echo Yii::t('campus', 'Absent days') ?></th>
                            <th width="50"><?php echo Yii::t('campus', 'Present days') ?></th>
                        </tr>
                        <?php
                        $signTotalPeople = array();
                        $lateTotalPeople = array();
                        $sickVacationTotalPeople = array();
                        $thingvacationTotalPeople = array();
                        $absentTotalPeople = array();
                        $num = 1;
                        foreach (json_decode($attendModelitems->data, true) as $chilid => $data) {
                            ?>
                            <tr>
                                <td class='bg'><?php echo $num; $num++; ?></td>
                                <td class='bg'><a href="<?php echo $this->createUrl("/child/index/cancelLunch", array("childid" => $chilid)); ?>" target="_blank"><?php echo ($childModel[$chilid]) ? $childModel[$chilid]->getChildName() : $chilid; ?></a></td>
                                <?php
                                $signNum = 0;//到校人数
                                $lateNum = 0;//迟到人数
                                $sickVacationNum = 0;//病假人数
                                $thingvacationNum = 0;//事假人数
                                $absentNum = 0;//旷课人数
                                foreach ($schoolDayByClass[$classid] as $key => $item):
                                    $html = "";
                                    foreach ($data as $keys => $dataitems) {
                                        if ($key <= $dataitems['endTime'] && $key >= $dataitems['startTime']) {
                                            $html = $type[$dataitems['type']];
                                            if ($dataitems['type'] == 1) {
                                                $signNum++;
                                                $signTotalPeople[$key] += 1;
                                            } else if ($dataitems['type'] == 10) {
                                                $sickVacationTotalPeople[$key] += 1;
                                                $sickVacationNum++;
                                            } else if ($dataitems['type'] == 40) {
                                                $lateTotalPeople[$key] += 1;
                                                $lateNum++;
                                            } else if ($dataitems['type'] == 60) {
                                                $lateTotalPeople[$key] += 1;
                                                $lateNum++;
                                                $signTotalPeople[$key] += 1;
                                                $signNum++;
                                            }else if($dataitems['type'] == 70){
                                                $absentTotalPeople[$key] += 1;
                                                $absentNum++;
                                            } else {
                                                $thingvacationTotalPeople[$key] += 1;
                                                $thingvacationNum++;
                                            }
                                        }
                                    }
                                    ?>
                                    <?php
                                    if($html){
                                        echo $html;
                                    }else{
                                        echo '<td></td>';
                                    }
                                    ?>
                                <?php endforeach; ?>
                                <td style="text-align: right" class='bg'><?php echo $lateNum; ?></td>
                                <td style="text-align: right" class='bg'><?php echo $sickVacationNum + $thingvacationNum+$absentNum; ?></td>
                                <td style="text-align: right" class='bg'><?php echo $signNum; ?></td>
                            </tr>
                        <?php } ?>
                        <tr class='bg'>
                            <td colspan="2" class="text-center" style="white-space:nowrap"><?php echo Yii::t('campus', 'Present students') ?></td>
                            <?php
                            $attendance = 0;
                            foreach ($schoolDayByClass[$classid] as $timeKey => $itemVal) { ?>
                                <td class="text-right"><?php
                                    if ($signTotalPeople && $signTotalPeople[$timeKey]) {
                                        echo $signTotalPeople[$timeKey];
                                        $attendance = $attendance + $signTotalPeople[$timeKey];
                                    } elseif (isset($signTotalPeople[$timeKey])) {
                                        echo 0;
                                    }
                                    ?></td>
                            <?php } ?>
                            <td colspan="3"><?php echo $attendance; ?></td>
                        </tr>
                        <tr  class='bg'>
                            <td colspan="2" class="text-center" style="white-space:nowrap"><?php echo Yii::t('campus', 'Tardy students') ?></td>
                            <?php
                            $lateTotal = 0;
                            foreach ($schoolDayByClass[$classid] as $timeKey => $itemVal) { ?>
                                <td class="text-right"><?php
                                    if ($lateTotalPeople && $lateTotalPeople[$timeKey]) {
                                        echo $lateTotalPeople[$timeKey];
                                        $lateTotal = $lateTotal + $lateTotalPeople[$timeKey];
                                    } elseif (isset($signTotalPeople[$timeKey])) {
                                        echo 0;
                                    }
                                    ?></td>
                            <?php } ?>
                            <td colspan="2"><?php echo $lateTotal; ?></td>
                        </tr>
                        <tr  class='bg'>
                            <td colspan="2" class="text-center" style="white-space:nowrap"><?php echo Yii::t('campus', 'Sick leave students') ?></td>
                            <?php
                            $sick = 0;
                            foreach ($schoolDayByClass[$classid] as $timeKey => $itemVal) { ?>
                                <td class="text-right"><?php
                                    if ($sickVacationTotalPeople && $sickVacationTotalPeople[$timeKey]) {
                                        echo $sickVacationTotalPeople[$timeKey];
                                        $sick = $sick + $sickVacationTotalPeople[$timeKey];
                                    } elseif (isset($signTotalPeople[$timeKey])) {
                                        echo 0;
                                    }
                                    ?></td>
                            <?php } ?>
                            <td colspan="3"><?php echo $sick; ?></td>
                        </tr>
                        <tr  class='bg'>
                            <td colspan="2" class="text-center" style="white-space:nowrap"><?php echo Yii::t('campus', 'Personal leave students') ?></td>
                            <?php
                            $leave = 0;
                            foreach ($schoolDayByClass[$classid] as $timeKey => $itemVal) { ?>
                                <td class="text-right"><?php
                                    if ($thingvacationTotalPeople && $thingvacationTotalPeople[$timeKey]) {
                                        echo $thingvacationTotalPeople[$timeKey];
                                        $leave = $leave + $thingvacationTotalPeople[$timeKey];
                                    } elseif (isset($signTotalPeople[$timeKey])) {
                                        echo 0;
                                    }
                                    ?></td>
                            <?php } ?>
                            <td colspan="3"><?php echo $leave; ?></td>
                        </tr>

                        <tr  class='bg'>
                            <td colspan="2" class="text-center" style="white-space:nowrap"><?php echo Yii::t('campus', 'Absent students') ?></td>
                            <?php
                            $absent = 0;
                            foreach ($schoolDayByClass[$classid] as $timeKey => $itemVal) { ?>
                                <td class="text-right"><?php
                                    if ($absentTotalPeople && $absentTotalPeople[$timeKey]) {
                                        echo $absentTotalPeople[$timeKey];
                                        $absent = $absent + $absentTotalPeople[$timeKey];
                                    } elseif (isset($signTotalPeople[$timeKey])) {
                                        echo 0;
                                    }
                                    ?></td>
                            <?php } ?>
                            <td colspan="2"><?php echo $absent; ?></td>
                        </tr>
                    </table></div>
            </div>
            <?php }}}; ?>

        </div>
    </div>
</div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    function updateAttend(id) {
        $.ajax({
            url: '<?php echo $this->createUrl('updateAttend'); ?>',
            data: {id: id},
            type: 'post',
            dataType: 'json',
            success: function (data) {
                if (data.state == 'success') {
                    resultTip({"msg": data.message});
                    location.reload();
                } else {
                    resultTip({msg: data.message, error: 1});
                    //resultTip({"msg": data.message})
                }
            }
        });
    }
</script>
<style>
    .bg{
        background:#FAFAFA
    }
    .headBg{
        background-color:#EDEEF2 !important
    }
    .table th{
        vertical-align: middle !important
    }
    .present{
        font-family:cursive;
        vertical-align: middle;
        color:#666;
    }
    .sick{
        font-family:SimSun;
        vertical-align: middle;
        color: #25A4A8;
        background: #E4FAFA
    }
    .personal{
        font-family:SimSun;
        vertical-align: middle;
        color: #4D88D2;
        background: #e5f1ff;
    }
    .tardy{
        font-family:SimSun;
        vertical-align: middle;
        color: #ED6A0C;
        background: #FDF0E6
    }
    .tardy span{
        font-family:cursive;
    }
</style>