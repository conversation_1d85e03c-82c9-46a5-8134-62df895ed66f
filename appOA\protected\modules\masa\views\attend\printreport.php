<style>
    .reportTbody > tr > td, .reportTbody > tr > th {
        vertical-align: middle !important;
        text-align: center;
    }
    .tableHr {
        margin-top: 2px;
        margin-bottom: 2px;
    }
</style>
<div class="table-responsive" id="tableDataVue">
    <table class="table table-bordered" style="vertical-align: middle;">
        <thead>
        <tr>
            <th colspan="10"><h4 class="text-center">ASA课后活动考勤表（个人教师考勤表）</h4>
            </th>
        </tr>
        <tr>
            <th colspan="2"><h4 class="text-center">校园：{{tableData.school}}</h4>
            </th>
            <th colspan="5"><h4 class="text-center">
                    考勤区间：{{tableData.month}}</h4></th>
            <th colspan="3"><h4 class="text-center">最后更新：{{tableData.update_time}}</h4>
            </th>
        </tr>
        </thead>
        <tbody class="reportTbody">
        <tr>
            <th width="50">NO.</th>
            <td width="100">姓名<br/>Chinese Name</td>
            <td width="100">手机号<br/>cellphone</td>
            <td width="150">身份证号码 / 护照号码 <br/>ID Card # / Password #</td>
            <td width="100">总计 <br/>Total Cost</td>
            <td width="120">开户行预留姓名 <br/> Account Name</td>
            <td width="120">银行账号 <br/> Bank Account</td>
            <td width="100">供应商类型 </td>
            <td width="100">费用合并（IVY KG 使用） </td>
            <td width="400">详细信息 </td>
        </tr>
        <tr v-for="(tData,id,index) in tableData.vendors">
            <th>{{id+1}}</th>
            <td>{{tData.name}}</td>
            <td>{{tData.cellphone}}</td>
            <td>'{{tData.identify}}</td>
            <td>{{tData.amount}}</td>
            <td>{{tData.bank_name}}</td>
            <td>'{{tData.bank_account}}</td>
            <td>{{tData.type}}</td>
            <td>{{tData.fee_merge == 1 ? '合并' : ''}}</td>

            <td style="text-align: left;">
                <!-- <span>{{tData.type}}({{tData.type_en}})</span>, -->
                <span v-for="(course,cid,index) in tData.courses">
                    <span v-for="(position,idx) in course"> {{position.position}}({{position.position_en}})</span>,
                </span>
                <span v-for="(course,cid,index) in tData.courses">
                    <span v-for="(name,idx) in course"> {{name.course_name}}({{name.course_name_en}})</span>,
                </span>
                <span v-for="(course,cid,index) in tData.courses">
                    <span v-for="(num,cid,index) in course">
                        {{parsefloat(num.unit_price)}} × {{parsefloat(num.course_count)}} = {{parsefloat(num.unit_price) * num.course_count}}
                    </span>
                </span>
                <span v-if="tData.variation_amout">
                    <span v-for="(course,cid,index) in tData.variation_amout">
                    <span v-if='course!="0.00"'>{{course}}</span>
                    <span v-for="(courses,cid,index) in tData.variation_memo"><span v-if='course!="0.00"'>备注：{{courses}}</span></span>
                </span>
                </span>
            </td>
        </tr>
        </tbody>
    </table>
</div>
<script>
    var tableData = <?php echo CJSON::encode($data); ?>;   //有追加的数据
    console.log(tableData)
    var tableDataVue = new Vue({
        el: "#tableDataVue",
        data: {
            tableData: tableData
        },
        updated: function () {

        },
        methods: {
            arrLength: function (arr) {
                return Object.keys(arr).length;
            },parsefloat: function (num) {
                return parseFloat(num);
            }
        },
        computed: {}
    });
</script>