<?php
/**
 * Created by PhpStorm.
 * User: XINRAN
 * Date: 14-4-16
 * Time: 上午10:53
 */ ?>
<div class="container-fluid">
    <p class="form-inline"><input type="text" class="form-control" id="specified-date" value="" style="z-index: 3;position:relative"></p>

    <div class="btn-group" data-toggle="buttons">
        <label class="btn btn-primary action-group-by active" data="program">
            <input type="radio" name="groupbyoptions"> By Program
        </label>
        <label class="btn btn-primary action-group-by" data="opentime">
            <input type="radio" name="groupbyoptions"> By Opentime
        </label>
        <label class="btn btn-primary action-group-by" data="city">
            <input type="radio" name="groupbyoptions"> By City
        </label>
        <label class="btn btn-primary action-group-by" data="progress">
            <input type="radio" name="groupbyoptions"> By Progress
        </label>

        <button type="button" class="btn btn-default" id="export">
            <span class="glyphicon glyphicon-export"></span> Export
        </button>
        <button type="button" class="btn btn-default" id="reload-general-page">
            <span class="glyphicon glyphicon-refresh"></span> Refresh
        </button>

    </div>
    <div class="pull-right">Total <span id="totleNumber"></span></div>

    <div class="row" id="branch-list" style="display: none;">
        <!--place holder-->
    </div>

    <div class="row" id="branch-list-by-group">
        <!--place holder-->
    </div>

</div>

<script type="text/template" id="item-branch-template">
        <div class="panel panel-default">
            <div class="panel-heading">
                <span branchid="<%= id %>" class="branch-title <% print(branchTypeMapping[branchObjs[id].group]);  %>-text"><span class="glyphicon glyphicon-tag"></span><% print(branchObjs[id].abb); %></span>
                <span class="pull-right"><%= data[0] %></span></div>
            <div class="panel-body">
                <p><span class="pull-right"><span class="glyphicon glyphicon-time"></span> <% print(branchObjs[id].opentime); %>Y </span><span><% print(branchObjs[id].title); %></span></p>
                <table width="100%">
                    <tr>
                        <th class="text-right" width="30%"></th>
                        <td class="text-center" width="35%"></td>
                        <td class="text-center target-complete" width="35%"></td>
                    </tr>
                    <tr>
                        <th class="text-right">Target</th>
                        <td class="text-center"><% print(DataTarget[id]); %></td>
                        <td class="text-center target-complete" width="35%"><% print(getPercent(data[0], DataTarget[id])); %></td>
                    </tr>
                    <tr>
                        <th class="text-right">1D</th>
                        <td class="text-center"><%= data[1] %></td>
                        <td class="text-center"><% print(printGap(data[0] - data[1])); %></td>
                    </tr>
                    <tr>
                        <th class="text-right">7D</th>
                        <td class="text-center"><%= data[2] %></td>
                        <td class="text-center"><% print(printGap(data[0] - data[2])); %></td>
                    </tr>
                    <tr>
                        <th class="text-right">30D</th>
                        <td class="text-center"><%= data[3] %></td>
                        <td class="text-center"><% print(printGap(data[0] - data[3])); %></td>
                    </tr>
                    <tr>
                        <th class="text-right">Next Year NO.</th>
                        <td class="text-center">
                            <span title="已付款"><%= nextNum.num10%></span>+<span title="未付款"><%= nextNum.num11%></span>-<span title="退学未退费"><%= nextNum.num17%></span>
                        </td>
                        <td></td>
                    </tr>
                </table>
            </div>
        </div>
</script>

<script type="text/template" id="data-campus-template">
    <div branchId="<%= branchId %>" class="container-fluid dashboard-page" id="container-campus-<%= branchId %>" style="display: none;">
        <div class="row">
            <div class="col-md-2">
                <h3><a href="javascript:void(0);" onclick="showGeneral()">
                    <span class="glyphicon glyphicon-chevron-left"></span></a> <% print(branchObjs[branchId].abb); %></h3>

                <div class="list-group campus-actions" branchId="<%= branchId %>">
                    <a href="javascript:void(0);" class="list-group-item" dataType="enroll"><?php echo Yii::t('enrollment', 'Enrollment'); ?></a>
                    <!--<a href="javascript:void(0);" class="list-group-item" dataType="finance">Financial Statistics</a>-->
                    <a href="javascript:void(0);" class="list-group-item" dataType="report"><?php echo Yii::t('enrollment', 'Weekly Report Quality'); ?></a>
                </div>
            </div>
            <div class="col-md-10">
                <div class="container-fluid  campus-main">
                    <div class="month-list"></div>
                    <div class="month-data"></div>
                </div>
            </div>
        </div>
    </div>
</script>

<script>
    $("#export").click(function(event) {
        var time = $("#specified-date").val();
        window.open('<?php echo $this->createUrl('default/export');?>'+'?time='+time);
    });
</script>