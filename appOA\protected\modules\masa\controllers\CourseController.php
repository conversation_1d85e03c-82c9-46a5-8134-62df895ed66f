<?php

class CourseController extends BranchBasedController
{
    public $batchNum = 100;

    public function createUrl($route, $params = array(), $ampersand = '&', $parentOnly = false)
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init()
    {
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Campus Workspace');

        $this->branchSelectParams['urlArray'] = array('//masa/course/index');

        // jquery ui
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl() . '/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/vue2.js');

        $baseDir = Yii::getPathOfAlias('common.extensions.ueditor.ueditor_full');
        $assets = Yii::app()->getAssetManager()->publish($baseDir, true, -1);
        $cs->registerScriptFile($assets . '/ueditor.teacher.config.js');
        $cs->registerScriptFile($assets . '/ueditor.all.nop.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/clipboard.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/tinymce/tinymce.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/xlsx.full.min.js');
    }

    public function actionIndex($groupId = 0)
    {
        $courseGroup = AsaCourseGroup::getGroup($this->branchId, false, false, false);
        $newCourseGroup = array();
        //        echo "<pre>";
        $courseGroupArr = json_decode(CJSON::encode($courseGroup), TRUE);
        foreach ($courseGroupArr as $v) {
            $newCourseGroup[$v['startyear']][] = $v;
        }
        $startyearArr = array_keys($newCourseGroup);
        $criteria = new CDbCriteria;
        $criteria->compare('gid', $groupId);
        $criteria->compare('schoolid', $this->branchId);
        $criteria->compare('status', array(AsaCourse::STATUS_ACTIVE, AsaCourse::STATUS_FAIL, AsaCourse::STATUS_TWO));
        $criteria->order = 'weight ASC';
        $courseModel = AsaCourse::model()->findAll($criteria);

        $criteria = new CDbCriteria;
        $criteria->compare('group_id', $groupId);
        $courseNumber = AsaCourseNumber::model()->findAll($criteria);
        $courseNumberS = array();
        if ($courseNumber) {
            foreach ($courseNumber as $item) {
                $courseNumberS[$item->course_id] = $item->number_taken + $item->number_hold;
            }
        }

        $type = $this->module->getteachertype();
        foreach ($type['jobType'] as $k => $v) {
            if ($k > 9) {
                $typeNum[] = $k;
            }
        }
        $crit = new CDbCriteria;
        $crit->compare('program_id', $groupId);
        $crit->compare('site_id', $this->branchId);
        $crit->compare('job_type', $typeNum);
        $staffCourseModel = new CActiveDataProvider('AsaCourseStaff', array(
            'criteria' => $crit,
            'sort' => array(
                'defaultOrder' => 'updated DESC',
            ),
            'pagination' => array(
                'pageSize' => 10,
            ),
        ));

        $courseGroupObj = AsaCourseGroup::model()->findByPk($groupId);

        if ($groupId != 0 && !$courseGroupObj) {
            $this->redirect(array('/masa/course/index', 'branchId' => $this->branchId));
        }
        $crit = new CDbCriteria;
        $crit->compare('startyear', $courseGroupObj->startyear);
        if (isset($courseGroupObj) && $courseGroupObj->program_type == AsaCourseGroup::PROGRAM_DELAYCARE) {
            $crit->compare('program_id', $groupId);
        } else {
            $crit->compare('program_id', array(0, $groupId));
        }
        $crit->compare('schoolid', $this->branchId);
        $schedule = new CActiveDataProvider('AsaSchedule', array(
            'criteria' => $crit,
            'sort' => array(
                'defaultOrder' => array(
                    'state' => CSort::SORT_DESC,
                    'updated' => CSort::SORT_DESC,
                    'week_day' => CSort::SORT_ASC,
                ),
            ),
            'pagination' => array(
                'pageSize' => 10,
            ),
        ));


        $this->render('index', array(
            //            'courseGroup' => $courseGroup,
            'newCourseGroup' => $newCourseGroup,
            'startyearArr' => $startyearArr,
            'courseModel' => $courseModel,
            'groupId' => $groupId,
            'staffCourseModel' => $staffCourseModel,
            'schedule' => $schedule,
            'courseNumberS' => $courseNumberS,
            'schoolid' => $this->branchId,
        ));
    }

    // 新建更新课程组
    public function actionUpdateGroup($groupId = 0)
    {
        Yii::import('common.models.asainvoice.AsaCourseGroup');
        $buyFrom = Yii::app()->request->getParam('buyFrom', array());
        $buyEnd = Yii::app()->request->getParam('buyEnd', array());
        $model = AsaCourseGroup::model()->findByPk($groupId);
        if (empty($model)) {
            $model = new AsaCourseGroup;
        }
        if (Yii::app()->request->isPostRequest) {
            if (empty($buyFrom['hour']) || empty($buyFrom['minute']) || empty($buyEnd['hour']) || empty($buyEnd['minute'])) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', '小时和分钟不能为空'));
                $this->showMessage();
            }
            if ($model->id) {
                $criteria = new CDbCriteria;
                $criteria->compare('gid', $model->id);
                $courseObj = AsaCourse::model()->findAll($criteria);
            }

            $model->attributes = $_POST['AsaCourseGroup'];
            $model->open_date_start = strtotime($model->open_date_start);
            $model->open_date_end = strtotime($model->open_date_end);
            $model->buy_from = strtotime($model->buy_from . ' ' . $buyFrom['hour'] . ':' . $buyFrom['minute']);
            $model->buy_end = strtotime($model->buy_end . ' ' . $buyEnd['hour'] . ':' . $buyEnd['minute']);
            $model->show_from = strtotime($model->show_from);
            $model->show_end = strtotime($model->show_end);
            $model->updated = time();
            $model->updated_userid = $this->staff->uid;
            if (!$model->schoolid) {
                $model->schoolid = $this->branchId;
            }
            if ($model->save()) {
                if ($courseObj && $_POST['AsaCourseGroup']['refundStatus']) {
                    foreach ($courseObj as $item) {
                        $item->refund_class_index = ($_POST['AsaCourseGroup']['refundStatus']) == 1 ? strtotime($_POST['AsaCourseGroup']['refundTime']) + 86399 : $_POST['AsaCourseGroup']['refundTime'];
                        $item->save();
                    }
                }
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message', 'success'));
                $this->addMessage('callback', 'cbGroup');
                $this->showMessage();
            }
            $error = current($model->getErrors());
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', $error[0]));
            $this->showMessage();
        }

        $buyFromStatr = "";
        $buyFromEnd = "";
        if ($model->open_date_start) {
            $buyFromStatr['hour'] = date('H', $model->buy_from);
            $buyFromStatr['minute'] = date('i', $model->buy_from);
            $buyFromEnd['hour'] = date('H', $model->buy_end);
            $buyFromEnd['minute'] = date('i', $model->buy_end);
            $model->open_date_start = date('Y-m-d', $model->open_date_start);
            $model->open_date_end = date('Y-m-d', $model->open_date_end);
            $model->buy_from = date('Y-m-d', $model->buy_from);
            $model->buy_end = date('Y-m-d', $model->buy_end);
            $model->show_from = date('Y-m-d', $model->show_from);
            $model->show_end = date('Y-m-d', $model->show_end);
        }


        $statusArr = array(
            AsaCourseGroup::PROGRAM_ACTIVE => Yii::t('asa', '课后课'),
            AsaCourseGroup::PROGRAM_DELAYCARE => Yii::t('asa', '延时看护'),
            AsaCourseGroup::PROGRAM_CAMP => Yii::t('asa', '冬夏令营'),
        );

        $seasonArr = array(
            96 => Yii::t('asa', '第一季'),
            97 => Yii::t('asa', '第二季'),
            98 => Yii::t('asa', '第三季'),
        );

        if (!in_array($this->branchId, array('BJ_OE', 'BJ_XHL', 'TJ_EC'))) {
            unset($statusArr[AsaCourseGroup::PROGRAM_DELAYCARE]);
        }


        $this->renderPartial('updategruop', array('model' => $model, 'buyFromStatr' => $buyFromStatr, 'buyFromEnd' => $buyFromEnd, 'statusArr' => $statusArr, 'seasonArr' => $seasonArr));
    }

    public function actionUpdateCampus($groupId = 0)
    {
        Yii::import('common.models.asainvoice.AsaCourseGroup');
        $site_id = Yii::app()->request->getParam('site_id', array());

        $schoolAll = $this->allBranch;
        $allBranch = array();
        foreach ($schoolAll as $k => $schoolItem) {
            $allBranch[$k] = $schoolItem['title'];
        }
        unset($allBranch[$this->branchId]);


        $criteria = new CDbCriteria;
        $criteria->compare('group_id', $groupId);
        $modelObj = AsaCourseGroupShare::model()->findAll($criteria);
        $checkAll = array();
        if ($modelObj) {
            foreach ($modelObj as $item) {
                $checkAll[] = $item->site_id;
            }
        }
        $model = new AsaCourseGroupShare;

        if (Yii::app()->request->isPostRequest) {
            if ($modelObj) {
                foreach ($modelObj as $item) {
                    $item->delete();
                }
            }
            if ($site_id) {
                foreach ($site_id as $siteItem) {
                    $model = new AsaCourseGroupShare;
                    $model->group_id = $groupId;
                    $model->site_id = $siteItem;
                    $model->updated = time();
                    $model->updated_by = Yii::app()->user->id;
                    $model->save();
                }
            }
            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('message', 'success'));
            $this->addMessage('callback', 'cbGroupshare');
            $this->showMessage();
        }

        $this->renderPartial('updateCampus', array(
            'model' => $model,
            'allBranch' => $allBranch,
            'checkAll' => $checkAll,
        ));
    }

    public function actionRefundProtocol($groupId = 0)
    {
        $crit = new CDbCriteria;
        $crit->compare('groupid', $groupId);
        $model = AsaCourseGroupAgreement::model()->find($crit);

        if (empty($model)) {
            $model = new AsaCourseGroupAgreement;
        }

        if (Yii::app()->request->isPostRequest) {
            $model->attributes = $_POST['AsaCourseGroupAgreement'];
            $model->updated = time();
            $model->groupid = $groupId;
            $model->updated_by = $this->staff->uid;
            if ($model->save()) {
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message', 'success'));
                $this->addMessage('callback', 'cbAgreement');
                $this->showMessage();
            }
            $error = current($model->getErrors());
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', $error[0]));
            $this->showMessage();
        }

        $this->renderPartial('refundProtocol', array('model' => $model));
    }

    // 添加更新课程
    public function actionUpdateCourse($id = 0, $groupId = 0)
    {
        $weekDay = intval(Yii::app()->request->getParam('weekDay', 0));
        $model = AsaCourse::model()->findByPk($id);
        $courseGroupObj = AsaCourseGroup::model()->findByPk($groupId);
        if (!$model) {
            $model = new AsaCourse;
            $model->gid = $groupId;
        }

        if (Yii::app()->request->isPostRequest) {
            if ($model->id) {
                $crit = new CDbCriteria;
                $crit->compare('course_id', $model->id);
                $attCount = AsaStaffAttendance::model()->count($crit);
                if ($attCount) {
                    if ($_POST['AsaCourse']['schedule_id'] != $model->schedule_id) {
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('message', "已经有签到,不可修改时间"));
                        $this->showMessage();
                    }
                }

                $crit = new CDbCriteria;
                $crit->compare('course_id', $model->id);
                $crit->compare('status', array(10, 20));
                $invoiceItemCount = AsaInvoiceItem::model()->count($crit);
                if ($invoiceItemCount) {
                    if ($_POST['AsaCourse']['schedule_id'] != $model->schedule_id) {
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('message', "已有家长购买过不可修改时间"));
                        $this->showMessage();
                    }
                }
            }


            $model->attributes = $_POST['AsaCourse'];
            $scheduleObj = AsaSchedule::model()->findByPk($model->schedule_id);
            $model->refund_class_index = ($_POST['AsaCourse']['refundClassIndexTime']) ? strtotime($_POST['AsaCourse']['refundClassIndexTime'] . " 23:59:59") : $_POST['AsaCourse']['refundClassIndex'];
            $model->default_count = $scheduleObj->total_count;
            $model->remaining_class_count = $model->default_count;
            $model->updated = time();
            $model->updated_userid = $this->staff->uid;
            $model->schoolid = $this->branchId;

            if ($model->age_min < 100 && $model->age_max > 100) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', "最小和最大适合年龄选项必须一致：同为年龄或年级"));
                $this->showMessage();
            }

            if ($model->age_min > 100 && $model->age_max < 100) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', "最小和最大适合年龄选项必须一致：同为年龄或年级"));
                $this->showMessage();
            }

            if ($model->save()) {
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message', 'success'));
                $this->addMessage('callback', 'cbCourse');
                $this->showMessage();
            }
            $error = current($model->getErrors());
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', $error[0]));
            $this->showMessage();
        }

        $scheduleArray = array();
        $criteria = new CDbCriteria;
        if ($courseGroupObj->program_type == AsaCourseGroup::PROGRAM_DELAYCARE) {
            $criteria->compare('program_id', $groupId);
        } else {
            $criteria->compare('program_id', array(0, $groupId));
        }
        $criteria->compare('startyear', $courseGroupObj->startyear);
        $criteria->compare('schoolid', $this->branchId);
        $criteria->compare('state', 1);
        $scheduleObj = AsaSchedule::model()->findAll($criteria);
        foreach ($scheduleObj as $item) {
            $scheduleArray[$item->id] = $item->title . ' * ' . $item->total_count;
        }
        $groupArray = array();
        $courseGroup = AsaCourseGroup::getGroup($this->branchId, false, false, false);

        foreach ($courseGroup as $v) {
            $groupArray[$v->id] = $v->title_cn;
        }

        $config = AsaCourse::getConfig();
        $ageConfigs = array();
        if ($this->branchObj->type == 50) {
            $ageConfigs = $config['age'] + $config['class'];
        } else {
            $ageConfigs = $config['age'];
        }

        if ($model->id) {
            $model->refundClassIndex = (strlen($model->refund_class_index) == 10) ? "" : $model->refund_class_index;
            $model->refundClassIndexTime = (strlen($model->refund_class_index)) == 10 ? date("Y-m-d", $model->refund_class_index) : "";
            $model->refundClassStatus = (strlen($model->refund_class_index) == 10) ? 1 : 2;
        }

        $this->renderPartial('updateCourse', array(
            'model' => $model,
            'groupArray' => $groupArray,
            'scheduleArray' => $scheduleArray,
            'courseGroupObj' => $courseGroupObj,
            'ageConfigs' => $ageConfigs,
        ));
    }

    // 删除课程
    public function actionDeleteCourse()
    {
        $id = Yii::app()->request->getParam('id', '');
        $model = AsaCourse::model()->findByPk($id);
        if (!$model) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', "参数错误"));
            $this->showMessage();
        }

        if ($model->schoolid != $this->branchId) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', "清选择所在校园数据进行操作"));
            $this->showMessage();
        }

        if (Yii::app()->request->isPostRequest) {
            $crit = new CDbCriteria;
            $crit->compare('course_id', $id);
            $crit->compare('program_id', $model->gid);
            $crit->compare('status', 1);
            $asaStaff = AsaCourseStaff::model()->count($crit);

            if (!$asaStaff) {
                /*$numStatus = 1;
                        $criteria = new CDbCriteria;
                        $criteria->compare('course_id', $id);
                        $number = AsaCourseNumber::model()->find($criteria);
                        if($number){
                            if($number->number_taken > 0 || $number->number_hold > 0){
                                $numStatus = 0;
                            }
                        }*/

                //order_id
                $criteria = new CDbCriteria;
                $criteria->compare('order_id', $model->id);
                $criteria->compare('course_group_id', $model->gid);
                $invoiceItemCount = AsaInvoiceItem::model()->count($criteria);


                if (!$invoiceItemCount) {
                    $criteria = new CDbCriteria;
                    $criteria->compare('course_id', $id);
                    $criteria->compare('program_id', $model->gid);
                    $criteria->compare('status', 0);
                    $countAttendance = AsaStaffAttendance::model()->count($criteria);
                    if (empty($countAttendance)) {
                        //先查看是否有关联的账单
                        $asainvoiceitem = $model->asainvoiceitem(array('condition' => 'status != ' . AsaInvoice::STATS_CANCELLED));
                        if (!$asainvoiceitem) {
                            if ($model->delete()) {
                                $this->addMessage('state', 'success');
                                $this->addMessage('message', Yii::t('message', 'success'));
                                $this->addMessage('callback', 'cbDelCourse');
                                $this->showMessage();
                            }
                            $error = current($model->getErrors());
                            $this->addMessage('state', 'fail');
                            $this->addMessage('message', Yii::t('message', $error[0]));
                            $this->showMessage();
                        } else {
                            //查询关联账单学生姓名
                            // $childidList = array();
                            // foreach($asainvoiceitem as $item){
                            //     if ($item->status == AsaInvoice::STATS_UNPAID) {
                            //         $childidList['unpaid'][$item->asainvoice->childid] = $item->asainvoice->childid;
                            //     } else{
                            //         $childidList['paid'][$item->asainvoice->childid] = $item->asainvoice->childid;
                            //     }
                            // }
                            // $unpaidChildList = ChildProfileBasic::model()->findAllByPk($childidList['unpaid']);
                            // $paidChildList = ChildProfileBasic::model()->findAllByPk($childidList['paid']);

                            // $message = '';
                            // if ($unpaidChildList) {
                            //     $message .= '未支付账单：';
                            // }
                            // foreach($unpaidChildList as $child){
                            //     $message .= $child->getChildName() . ',';
                            // }
                            // if ($paidChildList) {
                            //     $message .= ' 已支付账单：';
                            // }
                            // foreach($paidChildList as $child){
                            //     $message .= $child->getChildName() . ',';
                            // }
                            $message = '此课程已产生过相关账单！';
                            $this->addMessage('state', 'fail');
                            $this->addMessage('message', Yii::t('message', $message));
                            $this->showMessage();
                        }
                    } else {
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('message', "已经有签到的记录,不可删除"));
                        $this->showMessage();
                    }
                } else {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('message', "已有过账单，不可删除"));
                    $this->showMessage();
                }
            } else {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', "已经分配了老师,不可删除"));
                $this->showMessage();
            }
        }
        $this->renderPartial('updateCourse', array(
            'model' => $model,
            //'groupArray' => $groupArray,
        ));
    }

    /**
     * 编辑课程信息
     */

    public function actionUpdateCourseDesc()
    {
        $courseid = Yii::app()->request->getParam('id', '');
        $model = AsaCourseDesc::model()->findByPk($courseid);
        if (!$model) {
            $model = new AsaCourseDesc();
        }
        if (Yii::app()->request->isPostRequest) {
            $model->attributes = $_POST['AsaCourseDesc'];
            $model->course_id = $courseid;
            $model->updated = time();
            $model->updated_userid = Yii::app()->user->id;
            if ($model->save()) {
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message', "添加成功"));
                $this->addMessage('callback', 'cbCourseDesc');
            } else {
                $error = current($model->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('global', $error[0]));
            }
            $this->showMessage();
        }
        $this->renderPartial('updateCourseDesc2', array(
            'model' => $model,
        ));
    }


    /*
       * 本校园的所有老师
       */
    public function actionSchoolTeacherlist()
    {
        Yii::import('common.models.staff.*');

        /*$criteria = new CDbCriteria;
            $criteria->compare('t.level', 1);
            $criteria->compare('t.isstaff', 1);
            $criteria->compare('profile.branch', $this->branchId);
            $criteria->with = 'profile';       //调用profile
            $teacherNameList = User::model()->findAll($criteria);*/

        $teacherlist = array();
        /* if($teacherNameList){
                 foreach($teacherNameList as $k => $teacher){
                     $teacherlist[1][$k] = array(
                         'value' =>  $teacher->getName(),
                         'teacherPhone' =>  $teacher->staff->mobile_telephone,
                         'teacherId' =>  $teacher->uid,
                     );
                 }
             }*/

        $groupId = Yii::app()->request->getParam('groupId', 0);

        //外部企业老师
        $criteria = new CDbCriteria;
        $criteria->compare('group_id', $groupId);
        $criteria->index = "site_id";
        $groupShareObj = AsaCourseGroupShare::model()->findAll($criteria);

        $schoolid = array_keys($groupShareObj) + array(99 => $this->branchId);

        $criteria = new CDbCriteria;
        //$criteria->compare('type', array('2','3'));
        $criteria->compare('active', 1);
        $criteria->compare('site_id', $schoolid);
        $criteria->order = "type";
        $wTeacher = AsaVendor::model()->findAll($criteria);

        if ($wTeacher) {
            foreach ($wTeacher as $k => $_teacher) {
                $teacherlist[$_teacher->type][] = array(
                    'value' => $_teacher->getName(),
                    'teacherPhone' => $_teacher->cellphone,
                    'teacherId' => $_teacher->vendor_id,
                );
            }
        }

        echo CJSON::encode($teacherlist);
    }

    /*
       * 当前课程下的所有老师资料
       */
    public function actionTeacherlist()
    {
        $courseid = Yii::app()->request->getParam('courseid', '');


        if ($courseid) {
            // 员工内部 老师
            $criteria = new CDbCriteria;
            $criteria->compare('course_id', $courseid);
            $criteria->compare('status', 1);
            $courseStaff = AsaCourseStaff::model()->findAll($criteria);
            $teacherlist = array();

            $type = $this->module->getteachertype();
            if ($courseStaff) {
                foreach ($courseStaff as $k => $_coures) {
                    $teacherlist[$k] = array(
                        'tacherName' => $_coures->vendor->getName(),
                        'phone' => $_coures->vendor->cellphone,
                        'type' => $type['type'][$_coures->vendor->type],
                        'job_type' => $type['jobType'][$_coures->job_type],
                        'teacherPrice' => $_coures->unit_salary,
                        'staffId' => $_coures->id,
                        'unitType' => $type['unit_type'][$_coures->unit_type],
                    );
                }
            }

            echo CJSON::encode($teacherlist);
        }
    }


    /*
       * 增加老师
       *
       */
    public function actionAddteacher()
    {
        Yii::import('common.models.staff.*');

        $teacherid = Yii::app()->request->getParam('teacher', '');        // 内部员工的ID 或者 外部个人老师的ID
        $courseId = Yii::app()->request->getParam('courseId', '');        // 课程ID
        $teacherPrice = Yii::app()->request->getParam('teacherPrice', ''); // 老师课时价格
        $teacherJob = Yii::app()->request->getParam('teacher_job', '');   // 老师职位
        $unitType = Yii::app()->request->getParam('unitType', '');        // 计时种类deng
        $teacherType = Yii::app()->request->getParam('teacherType', '');  // 老师来源
        $teacherPhone = Yii::app()->request->getParam('teacherPhone', ''); // 外部企业或者个人的ID

        /*if($teacherType == 1){
                if(empty($teacherid)){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('message', '老师不能为空'));
                    $this->showMessage();
                }
                $criteria = new CDbCriteria;
                $criteria->compare('ivy_uid', $teacherid);
                $isteacher = AsaVendor::model()->find($criteria);
                if(empty($isteacher)){
                    if($teacherPhone){
                        $criteria = new CDbCriteria;
                        $criteria->compare('cellphone', $teacherPhone);
                        $criteria->compare('type', 1);
                        $phonecount = AsaVendor::model()->count($criteria);
                        if($phonecount){
                            $this->addMessage('state', 'fail');
                            $this->addMessage('message', Yii::t('message', "电话号码重复,请重新填写电话号码"));
                            $this->showMessage();
                        }
                    }

                    $teacherStaff = Staff::model()->findByPk($teacherid);
                    $teacherStaff->mobile_telephone = $teacherPhone;
                    if(!$teacherStaff->save()){
                        $error = current($teacherStaff->getErrors());
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('message', $error[0]));
                        $this->showMessage();
                    }
                    $teacher = User::model()->findByPk($teacherid);
                    $isteacher = new AsaVendor;
                    $isteacher->name_en = $teacher->profile->first_name . " " . $teacher->profile->last_name;
                    $isteacher->name_cn = ($teacher->name) ? $teacher->name : $isteacher->name_en;
                    $isteacher->cellphone = $teacher->staff->mobile_telephone;
                    $isteacher->email = $teacher->email;
                    $isteacher->site_id = $teacher->profile->branch;
                    $isteacher->active = 1;
                    $isteacher->ivy_uid = $teacherid;
                    $isteacher->type = $teacherType;
                    $isteacher->updated = time();
                    $isteacher->updated_by = Yii::app()->user->id;

                    if(!$isteacher->save()){
                        $error = current($isteacher->getErrors());
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('message', $error[0]));
                        $this->showMessage();
                    }
                }
            }else{
                $isteacher = AsaVendor::model()->findByPk($teacherid);
                if(!$isteacher){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('message', "没有该老师"));
                    $this->showMessage();
                }
            }*/

        $isteacher = AsaVendor::model()->findByPk($teacherid);
        if (!$isteacher) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', "没有该老师"));
            $this->showMessage();
        }
        if ($isteacher) {
            $criteria = new CDbCriteria;
            $criteria->compare('course_id', $courseId);
            $criteria->compare('vendor_id', $isteacher->vendor_id);
            $criteria->compare('status', 1);
            $asastaffcount = AsaCourseStaff::model()->count($criteria);
            if ($asastaffcount) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', '该老师已经添加过该课程'));
                $this->showMessage();
            }
        }

        $coures = AsaCourse::model()->findByPk($courseId);
        $courseGroup = AsaCourseGroup::model()->findByPk($coures->gid);
        if (empty($courseGroup)) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', "没有该课程组"));
            $this->showMessage();
        }

        $model = new AsaCourseStaff;
        $model->program_id = $courseGroup->id;
        $model->course_id = $coures->id;
        $model->vendor_id = $isteacher->vendor_id;
        $model->site_id = $this->branchId;
        $model->job_type = $teacherJob;
        $model->unit_salary = $teacherPrice;
        $model->status = 1;
        $model->updated = time();
        $model->updated_by = Yii::app()->user->id;
        $model->unit_type = $unitType;
        if ($model->save()) {
            $type = $this->module->getteachertype();
            $data = array(
                'tacherName' => $isteacher->getName(),
                'phone' => $isteacher->cellphone,
                'job_type' => $type['jobType'][$model->job_type],
                'teacherPrice' => $model->unit_salary,
                'type' => $type['type'][$isteacher->type],
                'staffId' => $model->id,
                'unitType' => $type['unit_type'][$model->unit_type],
            );

            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('message', "添加成功"));
            $this->addMessage('data', $data);
            $this->addMessage('callback', '_pushteacher');
            $this->showMessage();
        } else {
            $error = current($model->getErrors());
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', $error[0]));
            $this->showMessage();
        }
    }

    //根据ID 删除课程
    public function actionDelcourse()
    {
        $staffId = Yii::app()->request->getParam('id', '');
        if ($staffId) {
            $staff = AsaCourseStaff::model()->findByPk($staffId);
            if ($staff) {
                $criteria = new CDbCriteria;
                $criteria->compare('course_id', $staff->course_id);
                $criteria->compare('course_staff_id', $staff->id);
                $criteria->compare('status', 1);
                $model = AsaStaffAttendance::model()->findAll($criteria);
                if (!$model) {
                    $staff->status = 0;
                    if ($staff->save()) {
                        $this->addMessage('state', 'success');
                        $this->addMessage('data', $staffId);
                        $this->addMessage('message', Yii::t('message', "删除成功"));
                        $this->addMessage('callback', 'removeTeacher');

                        $this->showMessage();
                    } else {
                        $error = current($staff->getErrors());
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('global', "Deletion failed"));
                        $this->showMessage();
                    }
                } else {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('global', "有过签到不可删除"));
                    $this->showMessage();
                }
            }
        }
    }

    public function actionShowCourseInvoice($id = 0)
    {
        $childList = array();
        $model = array();
        $childidList = array();
        if ($id) {
            $childObj = $this->childInvoice($id);
            $childList = $childObj['childList'];
            $model = $childObj['model'];
            $childidList = $childObj['childidList'];
        }
        $this->renderPartial('courseInvoice', array(
            'id' => $id,
            'childList' => $childList,
            'model' => $model,
            'childidList' => $childidList,
        ));
    }

    public function actionExportCourseInvoice()
    {
        $id = Yii::app()->request->getParam('id', 0);
        if ($id) {
            $childObj = $this->childInvoice($id);
            if ($childObj) {
                $fileName = sprintf('%s_%s.xls', $this->branchId, $childObj['model']->title);
                //$fileName 去掉特殊符号以避免ERR_RESPONSE_HEADERS_MULTIPLE_CONTENT_DISPOSITION DS 篮球校队：U9（ 周三,1.5小时，无校车）
                $fileName = $this->sanitize_filename($fileName);
                ob_end_clean();
                header("Content-type:application/vnd.ms-excel");
                header("Content-Disposition:filename={$fileName}");
                echo $this->_t('编号') . "\t";
                echo $this->_t('中文姓名') . "\t";
                echo $this->_t('英文姓名') . "\t";
                echo $this->_t('班级') . "\t";
                echo $this->_t('父亲手机') . "\t";
                echo $this->_t('母亲手机') . "\t";
                echo $this->_t('父亲邮箱') . "\t";
                echo $this->_t('母亲邮箱') . "\t";
                echo $this->_t('账单') . "\t";
                echo "\n";
                $num = 1;

                foreach ($childObj["childidList"] as $child) {
                    $parents = array('father' => "", "mother" => "");
                    $childNamecn = "";
                    $childNameen = "";
                    if ($child['childId']) {
                        $parents = $childObj["childList"][$child['childId']]->getParents();
                        $childNamecn = $childObj["childList"][$child['childId']]->name_cn;
                        if (in_array($childObj["childList"][$child['childId']]->schoolid, array('BJ_DS', 'BJ_SLT'))) {
                            $childNameen = $childObj["childList"][$child['childId']]->nick;
                        } else {
                            $childNameen = $childObj["childList"][$child['childId']]->first_name_en . " " . $childObj["childList"][$child['childId']]->middle_name_en . " " . $childObj["childList"][$child['childId']]->last_name_en;
                        }
                    }
                    echo $this->_t($num) . "\t";
                    echo $this->_t($childNamecn) . "\t";
                    echo $this->_t($childNameen) . "\t";
                    echo $this->_t($childObj["childList"][$child['childId']]->ivyclass->title) . "\t";
                    echo $this->_t($parents['father'] ? $parents['father']->parent->mphone : '') . "\t";
                    echo $this->_t($parents['mother'] ? $parents['mother']->parent->mphone : '') . "\t";
                    echo $this->_t($parents['father'] ? $parents['father']->email : '') . "\t";
                    echo $this->_t($parents['mother'] ? $parents['mother']->email : '') . "\t";
                    echo $this->_t($child['status']) . "\t";
                    echo "\n";
                    $num++;
                }
            }
        }
    }

    public function actionCalculation()
    {
        $id = Yii::app()->request->getParam('id', 0);
        if ($id) {
            Yii::import('common.components.AliYun.MQ.MQProducer');
            CommonUtils::addProducer(MQProducer::TAG_ASA, "Invoice.releaseHoldSpot", CJSON::encode(array($id)));
            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('message', 'success'));
            $this->addMessage('callback', 'cbDelCourse');
            $this->showMessage();
        }
    }

    public function childInvoice($id = 0)
    {
        $model = AsaCourse::model()->findByPk($id);
        $childidList = array();
        $childList = array();
        if ($model) {
            $asainvoiceitem = $model->asainvoiceitem(array('condition' => 'status <= ' . AsaInvoice::STATS_PAID, 'order' => 'status', 'index' => 'id'));

            if ($asainvoiceitem) {
                $criteria = new CDbCriteria;
                $criteria->compare('invoice_item_id', array_keys($asainvoiceitem));
                $criteria->compare('refund_type', "<>3");
                $criteria->compare('status', array(1, 2, 3, 4, 5));
                $refundObj = AsaRefund::model()->findAll($criteria);
                $refundItem = array();
                if ($refundObj) {
                    foreach ($refundObj as $item) {
                        $refundItem[$item->invoice_item_id] = $item->status;
                    }
                }
                $begintime = mktime(0, 0, 0, date('m'), date('d'), date('Y'));
                $endtime = mktime(0, 0, 0, date('m'), date('d') + 1, date('Y')) - 1;
    
                foreach ($asainvoiceitem as $item) {
                    $dateColor = 0;
                    if ($item->asainvoice->childid) {
                        if ($item->asainvoice->created_from == 4) {
                            continue;
                        }
                        $refund = '';
                        if (isset($refundItem) && isset($refundItem[$item->id])) {
                            #已退费的
                            if ($refundItem[$item->id] == 5) {
                                continue;
                            } else {
                                $refund = 50;
                            }
                        }
                        if ($begintime <= $item->updated && $endtime >= $item->updated) {
                            $dateColor = 1;
                        }
                        $childidList[$item->id] = array(
                            'childId' => $item->asainvoice->childid,
                            'status' => ($refund ? '退费中' : (($item->status == 10) ? '未付款' : '已付款')) . " " . date("Y-m-d H:i:m", $item->updated),
                            'stat' => ($refund) ? $refund : $item->status,
                            'time' => $dateColor,
                        );
                        $childids[] = $item->asainvoice->childid;
                    }
                }
            }

            if ($childidList) {
                $childList = ChildProfileBasic::model()->findAllByPk($childids, array('index' => 'childid'));
            }
        }

        return array('childList' => $childList, 'model' => $model, 'childidList' => $childidList);
    }

    public function actionExportGroup()
    {
        Yii::import('common.models.calendar.*');
        Yii::import('common.models.schoolbus.*');
        Yii::import('common.models.invoice.*');
        $groupId = Yii::app()->request->getParam('groupId', 0);

        $criteria = new CDbCriteria;
        $criteria->compare('branchid', $this->branchId);
        $criteria->compare('is_selected', 1);
        $calenerObj = CalendarSchool::model()->find($criteria);

        if ($groupId)
        {
            $groupObj = AsaCourseGroup::model()->findByPk($groupId);

            $criteria = new CDbCriteria;
            $criteria->compare('gid', $groupId);
            $criteria->index = 'id';
            $model = AsaCourse::model()->findAll($criteria);
            if ($model)
            {
                $courseArr = array();
                //获取课程时间和标题拼成数组
                foreach($model as $item)
                {
                    $courseArr[$item->id] = array(
                        'courseId' => $item->id,
                        'titleCN' => $item->title_cn,
                        'titleEN' => $item->title_en,
                        'weekday' => $item->schedule->week_day,
                    );
                }

                $weekday = array(
                    1 => 'Monday',
                    2 => 'Tuesday',
                    3 => 'Wednesday',
                    4 => 'Thursday',
                    5 => 'Friday',
                    6 => 'Saturday',
                    7 => 'Sunday',
                );

                ob_end_clean();
                header("Content-type:application/vnd.ms-excel");
                header("Content-Disposition:filename=" . $this->branchId . "_" . $groupObj->title_en .".xls");
                echo $this->_t('WeekDay') . "\t";
                echo $this->_t('chidlid') . "\t";
                echo $this->_t('English Name') . "\t";
                echo $this->_t('Chinese Name') . "\t";
                echo $this->_t('School Class') . "\t";
                echo $this->_t('Dragon Program - ID') . "\t";
                echo $this->_t('Dragon Program - Chinese') . "\t";
                echo $this->_t('Dragon Program - English') . "\t";
                echo $this->_t('School Bus') . "\t";
                echo $this->_t('Invoice Status') . "\t";
                echo "\n";

                $num = array();
                $childNum = 0;

                $status = array(AsaInvoice::STATS_UNPAID,AsaInvoice::STATS_PAID);

                $criteria = new CDbCriteria;
                $criteria->compare('status', $status);
                $criteria->compare('course_id', array_keys($model));
                $total = AsaInvoiceItem::model()->count($criteria);
                $cycle = ceil($total/$this->batchNum);
                for ($i=0; $i < $cycle; $i++)
                {
                    $criterias = new CDbCriteria;
                    $criterias->compare('status', $status);
                    $criterias->compare('course_id', array_keys($model));
                    $criterias->limit=$this->batchNum;
                    $criterias->offset=$i*$this->batchNum;
                    $criterias->order = 'status DESC';
                    $criterias->index = "id";
                    $asainvoiceitem = AsaInvoiceItem::model()->findAll($criterias);

                    $childidList = array();
                    $criteria = new CDbCriteria;
                    $criteria->compare('invoice_item_id', array_keys($asainvoiceitem));
                    $criteria->compare('refund_type', array(1,2,4,10));
                    $criteria->compare('status', array(1,2,3,4,5));
                    $refundObj = AsaRefund::model()->findAll($criteria);
                    $refundItem = array();
                    if($refundObj)
                    {
                        foreach($refundObj as $item){
                            $refundItem[$item->invoice_item_id] = 1;
                        }
                    }

                    foreach($asainvoiceitem as $item)
                    {
                        if($item->asainvoice->childid){
                            if(isset($refundItem) && isset($refundItem[$item->id])){
                                continue;
                            }
                            $childidList[$item->id] = array(
                                'childId' => $item->asainvoice->childid,
                                'classid' => $item->asainvoice->classid,
                                'courseId' => $item->course_id,
                                'status' => $item->status,
                            );
                            $childids[] = $item->asainvoice->childid;
                        }
                    }
                    $childNum += count($childidList);
                    if($childidList)
                    {
                        $childList = ChildProfileBasic::model()->findAllByPk($childids, array('index'=>'childid'));

                        $crite = new CDbCriteria;
                        $crite->compare('t.startyear', $calenerObj->startyear);
                        $crite->compare('t.childid', array_keys($childList));
                        $crite->compare('t.schoolid', $this->branchId);
                        $crite->index = 'childid';
                        $childByschoolbus = SchoolBusChild::model()->findAll($crite);

                        $crite = new CDbCriteria;
                        $crite->compare('t.calendar_id', $calenerObj->yid);
                        $crite->compare('t.schoolid', $this->branchId);
                        $crite->compare('t.childid', array_keys($childList));
                        $crite->compare('t.payment_type', 'bus');
                        $crite->compare('t.inout', 'in');
                        $crite->compare('t.status', array(Invoice::STATS_PAID,Invoice::STATS_UNPAID,Invoice::STATS_PARTIALLY_PAID));
                        $crite->index = 'childid';
                        $crite->group = 't.childid';
                        $invoiceList = Invoice::model()->with('childprofile')->findAll($crite);

                        foreach ($childidList as $item) {
                            $classTitle = '';
                            if(isset($item['classid'])){
                                $classModel = IvyClass::model()->findByPk($item['classid']);
                                if($classModel) {
                                    $classTitle = $classModel->title;
                                }
                            }
                            $num[$item['childId']] +=  1;
                            $childNamecn = "";
                            $childNameen = "";
                            $bus = "";
                            if($childList[$item['childId']]){
                                $childNamecn = $childList[$item['childId']]->name_cn;
                                $childNameen = ($childList[$item['childId']]->is_legel_cn_name) ? $childList[$item['childId']]->nick . " " . $childList[$item['childId']]->last_name_en : $childList[$item['childId']]->first_name_en . " " . $childList[$item['childId']]->last_name_en;
                            }

                            if($childByschoolbus[$item['childId']] && $invoiceList[$item['childId']]){
                                $bus = $childByschoolbus[$item['childId']]->businfo->bus_title . " - " . $childByschoolbus[$item['childId']]->businfo->bus_type;
                            }
                            $week = ($courseArr[$item['courseId']]['weekday'] == 10) ? '延时课' : $weekday[$courseArr[$item['courseId']]['weekday']];
                            echo $this->_t($week) . "\t";
                            echo $this->_t($item['childId']) . "\t";
                            echo $this->_t($childNameen) . "\t";
                            echo $this->_t($childNamecn) . "\t";
                            echo $this->_t($classTitle) . "\t";
                            //echo $this->_t($childList[$item['childId']]->ivyclass->title) . "\t";
                            echo $this->_t($courseArr[$item['courseId']]['courseId']) . "\t";
                            echo $this->_t($courseArr[$item['courseId']]['titleCN']) . "\t";
                            echo $this->_t($courseArr[$item['courseId']]['titleEN']) . "\t";
                            echo $this->_t($bus) . "\t";
                            echo $this->_t(($item['status'] == 10) ? '未付款' : '已付款') . "\t";
                            echo "\n";
                        }
                    }
                }

                echo $this->_t('总人次') . "\t";
                echo $this->_t($childNum) . "\t";
                echo "\n";

                echo $this->_t('总人数') . "\t";
                echo $this->_t(count($num)) . "\t";
                echo "\n";
            }
        }
    }

    public function actionAjaxExportGroup()
    {
        Yii::import('common.models.calendar.*');
        Yii::import('common.models.schoolbus.*');
        Yii::import('common.models.invoice.*');
        $groupId = Yii::app()->request->getParam('groupId', 0);

        $criteria = new CDbCriteria;
        $criteria->compare('branchid', $this->branchId);
        $criteria->compare('is_selected', 1);
        $calenerObj = CalendarSchool::model()->find($criteria);
        if ($groupId) {
            $groupObj = AsaCourseGroup::model()->findByPk($groupId);
            $criteria = new CDbCriteria;
            $criteria->compare('gid', $groupId);
            $criteria->index = 'id';
            $model = AsaCourse::model()->findAll($criteria);
            if ($model) {
                $courseArr = array();
                //获取课程时间和标题拼成数组
                foreach ($model as $item) {
                    $courseArr[$item->id] = array(
                        'courseId' => $item->id,
                        'titleCN' => $item->title_cn,
                        'titleEN' => $item->title_en,
                        'weekday' => $item->schedule->week_day,
                    );
                }
                $weekday = array(
                    1 => 'Monday',
                    2 => 'Tuesday',
                    3 => 'Wednesday',
                    4 => 'Thursday',
                    5 => 'Friday',
                    6 => 'Saturday',
                    7 => 'Sunday',
                );
                $title = array(
                     'WeekDay',
                     'childid',
                     'English Name',
                     'Chinese Name',
                     'School Class',
                     'Dragon Program - ID',
                     'Dragon Program - Chinese',
                     'Dragon Program - English',
                     'School Bus',
                     'Invoice Status',
                     'Invoice Amount',
                     'Refund Amount',
                     'Father Name',
                     'Father Email',
                     'Father Mobile',
                     'Mother Name',
                     'Mother Email',
                     'Mother Mobile',
                );
                $num = array();
                $childNum = 0;
                $status = array(AsaInvoice::STATS_UNPAID, AsaInvoice::STATS_PAID);
                $criteria = new CDbCriteria;
                $criteria->compare('status', $status);
                $criteria->compare('course_id', array_keys($model));
                $total = AsaInvoiceItem::model()->count($criteria);
                $cycle = ceil($total / $this->batchNum);
                for ($i = 0; $i < $cycle; $i++) {
                    $criterias = new CDbCriteria;
                    $criterias->compare('status', $status);
                    $criterias->compare('course_id', array_keys($model));
                    $criterias->limit = $this->batchNum;
                    $criterias->offset = $i * $this->batchNum;
                    $criterias->order = 'course_id DESC';
                    $criterias->index = "id";
                    $asainvoiceitem = AsaInvoiceItem::model()->findAll($criterias);

                    $childidList = array();
                    $criteria = new CDbCriteria;
                    $criteria->compare('invoice_item_id', array_keys($asainvoiceitem));
                    $criteria->compare('refund_type', array(1, 2, 3, 4, 10));
                    $criteria->compare('status', array(1, 2, 3, 4, 5));
                    $refundObj = AsaRefund::model()->findAll($criteria);
                    $refundItem = array();
                    if ($refundObj) {
                        foreach ($refundObj as $item) {
                            $refundItem[$item->invoice_item_id] = $item;
                        }
                    }
                    $invoiceItemRefund = array();
                    foreach ($asainvoiceitem as $item) {
                        if (!isset($invoiceItemRefund[$item->id])) {
                            $invoiceItemRefund[$item->id] = 0;
                        }
                        if ($item->asainvoice->childid) {
                            if (isset($refundItem) && isset($refundItem[$item->id])) {
                                $refundObj = $refundItem[$item->id];
                                if ($refundObj->refund_type != 3) {
                                    continue;
                                } else {
                                    $invoiceItemRefund[$item->id] += $refundObj->refund_total_amount;
                                }
                            }
                            $childidList[$item->id] = array(
                                'childId' => $item->asainvoice->childid,
                                'classid' => $item->asainvoice->classid,
                                'courseId' => $item->course_id,
                                'status' => $item->status,
                                'actual_total' => $item->actual_total,
                                'refund_total' => $invoiceItemRefund[$item->id],
                            );
                            $childids[] = $item->asainvoice->childid;
                        }
                    }
                    $childNum += count($childidList);
                    if ($childidList) {
                        $childList = ChildProfileBasic::model()->findAllByPk($childids, array('index' => 'childid'));
                        $prent_id = array();
                        $child_parent = array();
                        foreach ($childList as $child_id => $value) {
                            $child_parent[$child_id]['f_id'] =  '';
                            $child_parent[$child_id]['m_id'] =  '';
                            if ($value->fid) {
                                $prent_id[] = $value->fid;
                                $child_parent[$child_id]['f_id'] =  $value->fid;
                            }
                            if ($value->mid) {
                                $prent_id[] = $value->mid;
                                $child_parent[$child_id]['m_id'] =  $value->mid;
                            }
                        }
                        if ($prent_id) {
                            $parentsUserInfo = User::model()->findAllByPk($prent_id, array('index' => 'uid'));
                            $parentsInfo = IvyParent::model()->findAllByPk($prent_id, array('index' => 'pid'));
                            $child_prent_info = array();
                            foreach ($child_parent as $child_id => $value) {
                                $child_prent_info[$child_id] = array(
                                    'f_name' => null,
                                    'f_email' => null,
                                    'f_phone' => null,
                                    'm_name' => null,
                                    'm_email' => null,
                                    'm_phone' => null,
                                );
                                if (!empty($value['f_id'])) {
                                    $child_prent_info[$child_id]['f_name'] = $parentsUserInfo[$value['f_id']]->getName();
                                    $child_prent_info[$child_id]['f_email'] = $parentsUserInfo[$value['f_id']]['email'];
                                    $child_prent_info[$child_id]['f_phone'] = $parentsInfo[$value['f_id']]['mphone'];
                                }
                                if (!empty($value['m_id'])) {
                                    $child_prent_info[$child_id]['m_name'] = $parentsUserInfo[$value['m_id']]->getName();
                                    $child_prent_info[$child_id]['m_email'] = $parentsUserInfo[$value['m_id']]['email'];
                                    $child_prent_info[$child_id]['m_phone'] = $parentsInfo[$value['m_id']]['mphone'];
                                }
                            }
                        }
                        $crite = new CDbCriteria;
                        $crite->compare('t.startyear', $calenerObj->startyear);
                        $crite->compare('t.childid', array_keys($childList));
                        $crite->compare('t.schoolid', $this->branchId);
                        $crite->index = 'childid';
                        $childByschoolbus = SchoolBusChild::model()->findAll($crite);

                        $crite = new CDbCriteria;
                        $crite->select='t.childid,t.invoice_id';
                        $crite->compare('t.calendar_id', $calenerObj->yid);
                        $crite->compare('t.schoolid', $this->branchId);
                        $crite->compare('t.childid', array_keys($childList));
                        $crite->compare('t.payment_type', 'bus');
                        $crite->compare('t.inout', 'in');
                        $crite->compare('t.status', array(Invoice::STATS_PAID, Invoice::STATS_UNPAID, Invoice::STATS_PARTIALLY_PAID));
                        $crite->index = 'childid';
                        $crite->group = 't.childid,t.invoice_id';
                        $invoiceList = Invoice::model()->with('childprofile')->findAll($crite);
                        foreach ($childidList as $item) {
                            $classTitle = '';
                            if (isset($item['classid'])) {
                                $classModel = IvyClass::model()->findByPk($item['classid']);
                                if ($classModel) {
                                    $classTitle = $classModel->title;
                                }
                            }
                            $num[$item['childId']] += 1;
                            $childNamecn = "";
                            $childNameen = "";
                            $bus = "";
                            if ($childList[$item['childId']]) {
                                $childNamecn = $childList[$item['childId']]->name_cn;
                                $childNameen = ($childList[$item['childId']]->is_legel_cn_name) ? $childList[$item['childId']]->nick . " " . $childList[$item['childId']]->last_name_en : $childList[$item['childId']]->first_name_en . " " . $childList[$item['childId']]->last_name_en;
                            }
                            if ($childByschoolbus[$item['childId']] && $invoiceList[$item['childId']]) {
                                $bus = $childByschoolbus[$item['childId']]->businfo->bus_title . " - " . $childByschoolbus[$item['childId']]->businfo->bus_type;
                            }
                            $week = ($courseArr[$item['courseId']]['weekday'] == 10) ? '延时课' : $weekday[$courseArr[$item['courseId']]['weekday']];
                            $list[] = array(
                                'WeekDay'=>$week,
                                'childid'=>$item['childId'],
                                'English Name'=>$childNameen,
                                'Chinese Name'=>$childNamecn,
                                'School Class'=>$classTitle,
                                'Dragon Program - ID'=>$courseArr[$item['courseId']]['courseId'],
                                'Dragon Program - Chinese'=>$courseArr[$item['courseId']]['titleCN'],
                                'Dragon Program - English'=>$courseArr[$item['courseId']]['titleEN'],
                                'School Bus'=>$bus,
                                'Invoice Status'=>($item['status'] == 10) ? '未付款' : '已付款',
                                'Invoice Amount'=>$item['actual_total'],
                                'Refund Amount'=>$item['refund_total'],
                                'Father Name'=>$child_prent_info[$item['childId']]['f_name'],
                                'Father Email'=>$child_prent_info[$item['childId']]['f_email'],
                                'Father Mobile'=>$child_prent_info[$item['childId']]['f_phone'],
                                'Mother Name'=>$child_prent_info[$item['childId']]['m_name'],
                                'Mother Email'=>$child_prent_info[$item['childId']]['m_email'],
                                'Mother Mobile'=>$child_prent_info[$item['childId']]['m_phone'],
                            );
                        }
                    }
                }
                $data = array(
                    'filename' => $this->branchId . "_" . $groupObj->title_en .".xls",
                    'title'=>$title,
                    'list'=>$list,
                    'total_people'=>count($num),
                    'total_number'=>$childNum,
                );
                $this->addMessage('state', 'success');
                $this->addMessage('data', $data);
            }else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', 'Course is empty');
            }
        }else{
            $this->addMessage('state', 'fail');
            $this->addMessage('message', 'GroupId is empty');
        }
        $this->showMessage();
    }

    public function _t($str = '')
    {
        $str = "\"" . $str . "\"";
        return iconv("UTF-8", "GBK//IGNORE", $str);
    }

    public function actionShowFollower($id = 0)
    {
        $model = AsaCourse::model()->findByPk($id);
        if ($model) {
            $followers = AsaCourseFollower::model()->findAllByAttributes(array('course_id' => $id, 'status' => 1));

            $this->renderPartial('courseFollower', array(
                'model' => $model,
                'followers' => $followers,
            ));
        }
    }

    //分配工作人员
    public function actionUpdataStaffCourse()
    {
        $teacherid = Yii::app()->request->getParam('teacherid', '');
        $groupId = Yii::app()->request->getParam('groupId', '');
        $AsaCourseStaff = Yii::app()->request->getParam('AsaCourseStaff', '');

        $criteria = new CDbCriteria;
        $criteria->compare('group_id', $groupId);
        $criteria->index = "site_id";
        $groupShareObj = AsaCourseGroupShare::model()->findAll($criteria);

        $schoolid = array_keys($groupShareObj) + array(99 => $this->branchId);

        $criteria = new CDbCriteria;
        $criteria->compare('site_id', $schoolid);
        $criteria->order = 'type ASC';
        $teacherVendor = AsaVendor::model()->findAll($criteria);
        $teacherList = array();
        $venteacher = array('1' => '内部', 2 => '外部公司', 3 => '外部个人');
        foreach ($teacherVendor as $_teacher) {
            $teacherList[$venteacher[$_teacher->type]][$_teacher->vendor_id] = $_teacher->getName();
        }

        $typeList = CommonUtils::LoadConfig('CfgASA');
        $unitType = array();
        foreach ($typeList['unit_type'] as $k => $_type) {
            $unitType[$k] = (Yii::app()->language == 'zh_cn') ? $_type['cn'] : $_type['en'];
        }

        $staffJobType = array();
        foreach ($typeList['job_type'] as $k => $job_type) {
            if ($k > 9) {
                $staffJobType[$k] = (Yii::app()->language == 'zh_cn') ? $job_type['cn'] : $job_type['en'];
            }
        }


        $model = new AsaCourseStaff();
        if ($teacherid) {
            $model = AsaCourseStaff::model()->findByPk($teacherid);
        }

        if (Yii::app()->request->isPostRequest) {
            if (empty($AsaCourseStaff['vendor_id'])) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('global', "请选择员工"));
                $this->showMessage();
            }
            $criteria = new CDbCriteria;
            $criteria->compare('vendor_id', $AsaCourseStaff['vendor_id']);
            $criteria->compare('program_id', $groupId);
            $criteria->compare('job_type', $AsaCourseStaff['job_type']);
            $criteria->compare('id', "<>{$teacherid}");
            $staffCount = AsaCourseStaff::model()->count($criteria);
            if ($staffCount) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('global', "该员工在本课程组下已经添加"));
                $this->showMessage();
            }

            /*if($model->status){
                      $this->addMessage('state', 'fail');
                      $this->addMessage('message', Yii::t('global', "您没有修改权限，请联系IT部门"));
                      $this->showMessage();
                  }*/
            $model->attributes = $_POST['AsaCourseStaff'];
            $model->program_id = $groupId;
            $model->course_id = 0;
            $model->site_id = $this->branchId;
            $model->updated = time();
            $model->updated_by = Yii::app()->user->id;
            if ($model->save()) {
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message', "添加成功"));
                $this->addMessage('callback', 'cbCourseStaff');
            } else {
                $error = current($model->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('global', $error[0]));
            }
            $this->showMessage();
        }
        unset($unitType[1]);
        unset($unitType[99]);
        $groupObj = AsaCourseGroup::model()->findByPk($groupId);
        if ($groupObj->program_type == 1) {
            unset($staffJobType[40]);
        }
        $this->renderPartial('updateStaff', array(
            'model' => $model,
            'teacherList' => $teacherList,
            'unitType' => $unitType,
            'staffJobType' => $staffJobType,
            'groupId' => $groupId,
        ));
    }

    //增加时间
    public function actionSchedule()
    {
        $groupId = intval(Yii::app()->request->getParam('groupId', 0));
        $scheduleId = intval(Yii::app()->request->getParam('scheduleId', 0));
        $date = Yii::app()->request->getParam('date', array());
        $defaultTime = Yii::app()->request->getParam('defaultTime', array());
        $groupObj = AsaCourseGroup::model()->findByPk($groupId);
        $scheduleIdDateObj = array();
        $getLastSignins = 0;
        $courseList = array();
        $courseGroup = array();
        $invoiceItemCount = 0;
        $model = AsaSchedule::model()->findByPk($scheduleId);
        if ($model->id) {
            $criteria = new CDbCriteria;
            $criteria->compare('schedule_id', $model->id);
            $criteria->index = 'id';
            $courseObj = AsaCourse::model()->findAll($criteria);

            if ($courseObj) {
                foreach ($courseObj as $courserItem) {
                    $courseList[] = $courserItem->getTitle();
                    $courseGroup[$courserItem->gid] = $courserItem->gid;
                }
            }
            $endDate = AsaCourse::getLastSignin(array_keys($courseObj));

            if ($courseObj) {
                $crit = new CDbCriteria;
                $crit->compare('course_id', array_keys($courseObj));
                $crit->compare('status', array(10, 20));
                $invoiceItemCount = AsaInvoiceItem::model()->count($crit);
            }
        }

        if (empty($model)) {
            $model = new AsaSchedule();
            $model->startyear = $groupObj->startyear;
        }

        if (Yii::app()->request->isPostRequest) {
            if (empty($defaultTime['startH']) || empty($defaultTime['startM']) || empty($defaultTime['endH']) || empty($defaultTime['endM'])) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('global', "默认时间为必填项"));
                $this->showMessage();
            }

            if ($model->id) {
                if ($endDate || $invoiceItemCount) {
                    if (count($model->items) <> count($date)) {
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('global', "上课节数不能变"));
                        $this->showMessage();
                    }
                }
            }
            if (empty($date) && $groupObj->program_type != AsaCourseGroup::PROGRAM_DELAYCARE) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('global', "安排时间不能为空"));
                $this->showMessage();
            }

            $model->attributes = $_POST['AsaSchedule'];
            if ($model->week_day == 0) {
                $model->week_multiple = implode(',', $model->week_multiple);
            } else {
                $model->week_multiple = $model->week_day;
            }
            $program_id = ($courseGroup) ? reset($courseGroup) : $groupId;

            $model->program_id = ($_POST['AsaSchedule']['shared'] == 1) ? 0 : $program_id;
            $model->schoolid = $this->branchId;
            $model->default_time_start = sprintf("%02d", $defaultTime['startH']) . ":" . sprintf("%02d", $defaultTime['startM']);
            $model->default_time_end = sprintf("%02d", $defaultTime['endH']) . ":" . sprintf("%02d", $defaultTime['endM']);
            $model->updated = time();
            $model->total_count = ($_POST['AsaSchedule']['shared'] == 1) ? count($date) : (($groupObj->program_type == 1) ? count($date) : 1);
            $model->updated_by = Yii::app()->user->id;

            if ($model->save()) {
                if ($groupObj->program_type != 10) {
                    AsaScheduleItem::model()->deleteAll('schedule_id=:scheduleid', array(':scheduleid' => $model->id));
                    $key = 1;
                    foreach ($date as $item) {
                        $scheduleItmeModel = new AsaScheduleItem();
                        $scheduleItmeModel->schedule_id = $model->id;
                        $scheduleItmeModel->schedule_date = date("Ymd", strtotime($item['schedule_date']));
                        $scheduleItmeModel->time_start = sprintf("%02d", $item['timeStartH']) . ':' . sprintf("%02d", $item['timeStartM']);
                        $scheduleItmeModel->time_end = sprintf("%02d", $item['timeEndH']) . ':' . sprintf("%02d", $item['timeEndM']);
                        $scheduleItmeModel->course_index = $key;
                        $scheduleItmeModel->save();
                        $key++;
                    }
                }

                if ($courseGroup) {
                    array_shift($courseGroup);
                    if ($courseGroup) {
                        foreach ($courseGroup as $val) {
                            $modelS = new AsaSchedule();
                            $modelS->schoolid = $model->schoolid;
                            $modelS->startyear = $groupObj->startyear;
                            $modelS->title = $model->title;
                            $modelS->program_id = $val;
                            $modelS->week_day = $model->week_day;
                            $modelS->default_time_start = $model->default_time_start;
                            $modelS->default_time_end = $model->default_time_end;
                            $modelS->total_count = $model->total_count;
                            $modelS->state = $model->state;
                            $modelS->refund_class_index = $model->refund_class_index;
                            $modelS->updated = $model->updated;
                            $modelS->updated_by = $model->updated_by;
                            $modelS->week_multiple = $model->week_multiple;
                            $modelS->save();

                            $criteria = new CDbCriteria;
                            $criteria->compare('gid', $val);
                            $criteria->compare('schedule_id', $model->id);
                            $courseModels = AsaCourse::model()->findAll($criteria);
                            if ($courseModels) {
                                foreach ($courseModels as $val) {
                                    $val->schedule_id = $modelS->id;
                                    $val->save();
                                }
                            }
                            foreach ($model->asaScheduleItem as $val) {
                                $itemModel = new AsaScheduleItem();
                                $itemModel->schedule_id = $modelS->id;
                                $itemModel->schedule_date = $val->schedule_date;
                                $itemModel->time_start = $val->time_start;
                                $itemModel->time_end = $val->time_end;
                                $itemModel->course_index = $val->course_index;
                                $itemModel->save();
                            }
                        }
                    }
                }


                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message', "添加成功"));
                $this->addMessage('callback', 'cbCourseStaff');
                $this->showMessage();
            } else {
                $error = current($model->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('global', $error[0]));
            }
            $this->showMessage();
        }

        if ($model->id) {
            $modelStats = explode(':', $model->default_time_start);
            $modelEnd = explode(':', $model->default_time_end);
            $scheduleIdDateObj = array(
                "scheduleWeekday" => $model->week_day,
                "scheduleTitle" => $model->title,
                "defaultReady_s" => $modelStats[0],
                "defaultReady_e" => $modelStats[1],
                "defaultEnd_s" => $modelEnd[0],
                "defaultEnd_e" => $modelEnd[1],
                "status" => $model->state,
                "shared" => ($model->program_id > 0) ? 0 : 1,
                "scheduleDate" => array(),
                "weekMultiple" => explode(",", $model->week_multiple),
            );

            $criteria = new CDbCriteria;
            $criteria->compare('schedule_id', $model->id);
            $scheduleItem = AsaScheduleItem::model()->findAll($criteria);


            foreach ($scheduleItem as $k => $scheduleItm) {
                $stats = explode(':', $scheduleItm->time_start);
                $end = explode(':', $scheduleItm->time_end);
                $scheduleIdDateObj['scheduleDate'][$scheduleItm->schedule_date . sprintf("%02d", $scheduleItm->course_index)] = array(
                    'dateText' => date("Y-m-d", strtotime($scheduleItm->schedule_date)),
                    'week' => date("w", strtotime($scheduleItm->schedule_date)),
                    'ready_s' => $stats[0],
                    'ready_e' => $stats[1],
                    'end_s' => $end[0],
                    'end_e' => $end[1],
                    'isReadOnly' => (isset($endDate) && $endDate[$scheduleItm->schedule_date]) ? 1 : 0,
                );
            }
        }

        $this->renderPartial('updateSchedule', array(
            'groupObj' => $groupObj,
            'model' => $model,
            'courseList' => $courseList,
            'scheduleIdDateObj' => $scheduleIdDateObj,
        ));
    }

    //删除时间
    public function actionDeleteSchedule()
    {
        $scheduleId = intval(Yii::app()->request->getParam('scheduleId', 0));
        if ($scheduleId) {
            $scheduleObj = AsaSchedule::model()->findByPk($scheduleId);
            if ($scheduleObj && $scheduleObj->schoolid == $this->branchId) {
                $criteria = new CDbCriteria;
                $criteria->compare('schedule_id', $scheduleId);
                $courseStatus = AsaCourse::model()->count($criteria);
                if ($courseStatus) {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('global', "已有分配到课程不可删除"));
                    $this->showMessage();
                }
                if ($scheduleObj->delete()) {
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message', "删除成功"));
                    $this->addMessage('callback', 'cbCourseStaff');
                } else {
                    $error = current($scheduleObj->getErrors());
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('global', $error[0]));
                }
                $this->showMessage();
            }
        }
    }

    //删除工作人员信息
    public function actionDeleteStaff()
    {
        $id = Yii::app()->request->getParam('id', '');
        if (Yii::app()->request->isPostRequest) {
            $staff = AsaCourseStaff::model()->findByPk($id);
            if ($staff->site_id != $this->branchId) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('global', "请选择所在校园进行操作"));
                $this->showMessage();
            }
            $criteria = new CDbCriteria;
            $criteria->compare('course_staff_id', $staff->id);
            $staffAttendanceCount = AsaStaffAttendance::model()->count($criteria);
            if (!$staffAttendanceCount) {
                if ($staff->delete()) {
                    $this->addMessage('state', 'success');
                    $this->addMessage('message', Yii::t('message', "删除成功"));
                    $this->addMessage('callback', 'cbDelStaff');
                } else {
                    $error = current($staff->getErrors());
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('global', $error[0]));
                }
                $this->showMessage();
            } else {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('global', "该工作人员已经有签到,不可删除"));
                $this->showMessage();
            }
        }
    }


    //删除课程组
    public function actionDeletegroup()
    {
        $groupId = Yii::app()->request->getParam('groupId', '');
        if (Yii::app()->request->isPostRequest) {
            $group = AsaCourseGroup::model()->findByPk($groupId);
            if ($group->schoolid != $this->branchId) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('global', "请选择所在校园进行操作"));
                $this->showMessage();
            }

            $criteria = new CDbCriteria;
            $criteria->compare('program_id', $group->id);
            $scheduleCount = AsaSchedule::model()->count($criteria);
            if (!$scheduleCount) {
                $criteria = new CDbCriteria;
                $criteria->compare('program_id', $group->id);
                $criteria->compare('course_id', 0);
                $staffCount = AsaCourseStaff::model()->count($criteria);
                if (!$staffCount) {
                    $criteria = new CDbCriteria;
                    $criteria->compare('gid', $group->id);
                    $courseCount = AsaCourse::model()->count($criteria);
                    if (!$courseCount) {
                        $criteria = new CDbCriteria;
                        $criteria->compare('group_id', $group->id);
                        $shareCount = AsaCourseGroupShare::model()->count($criteria);
                        if (!$shareCount) {
                            if ($group->delete()) {
                                $this->addMessage('state', 'success');
                                $this->addMessage('message', Yii::t('message', "删除成功"));
                                $this->addMessage('data', $this->branchId);
                                $this->addMessage('callback', 'cbDelCourse');
                            } else {
                                $error = current($group->getErrors());
                                $this->addMessage('state', 'fail');
                                $this->addMessage('message', Yii::t('global', $error[0]));
                            }
                            $this->showMessage();
                        } else {
                            $this->addMessage('state', 'fail');
                            $this->addMessage('message', Yii::t('global', "该课程组多校园共享,删除请联系IT"));
                            $this->showMessage();
                        }
                    } else {
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', Yii::t('global', "有课程,删除请联系IT"));
                        $this->showMessage();
                    }
                } else {
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', Yii::t('global', "有工作人员，不可删除"));
                    $this->showMessage();
                }
            } else {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('global', "有安排时间, 请选删除时间"));
                $this->showMessage();
            }
        }
    }

    public function actionUpdateSort()
    {
        if (Yii::app()->request->isPostRequest) {
            $sort = Yii::app()->request->getPost('sort', array());
            foreach ($sort as $key => $val) {
                AsaCourse::model()->updateByPk($key, array('weight' => $val));
            }
        }
    }

    //导入课程
    public function actionImportCourse()
    {
        $groupId = Yii::app()->request->getParam('groupId', '');
        $criteria = new CDbCriteria;
        $criteria->compare('schoolid', $this->branchId);
        $criteria->compare('status', AsaCourseGroup::STATUS_ACTIVE);
        $criteria->compare('program_type', "<>10");
        $groupObj = AsaCourseGroup::model()->findAll($criteria);


        $criteria = new CDbCriteria();
        $criteria->compare('program_id', array(0, $groupId));
        $criteria->compare('schoolid', $this->branchId);
        $criteria->compare('state', 1);
        $schedule = AsaSchedule::model()->findAll($criteria);

        $groupArr = array();
        if ($groupObj) {
            foreach ($groupObj as $group) {
                $groupArr[$group->id] = $group->getName();
            }
        }
        $scheduleArr = array();
        if ($schedule) {
            foreach ($schedule as $group) {
                $scheduleArr[$group->id] = $group->title;
            }
        }

        $this->renderPartial('importCourse', array(
            'groupArr' => $groupArr,
            'scheduleArr' => $scheduleArr,
            'groupId' => $groupId,
        ));
    }

    //根据课程组拿到所有的课程
    public function actionCourseList()
    {
        $groupId = Yii::app()->request->getParam('groupId', '');
        $data = array();
        $criteria = new CDbCriteria();
        $criteria->compare('gid', $groupId);
        $criteria->compare('schoolid', $this->branchId);
        $courseObj = AsaCourse::model()->findAll($criteria);

        if ($courseObj) {
            foreach ($courseObj as $item) {
                $data[$item->id] = array(
                    'title' => $item->getTitle(),
                    'timeTitle' => $item->schedule->title,
                );
            }
        }
        $this->addMessage('state', 'success');
        $this->addMessage('data', $data);
        $this->showMessage();
    }

    public function actionImport()
    {
        $groupId = Yii::app()->request->getParam('groupId', '');
        $course = Yii::app()->request->getParam('course', array());
        $scheduleTime = Yii::app()->request->getParam('scheduleTime', '');

        if (empty($groupId)) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('global', "课程组不能为空"));
            $this->showMessage();
        }

        if (empty($course)) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('global', "课程不能为空"));
            $this->showMessage();
        }

        if (empty($scheduleTime)) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('global', "安排时间不能为空"));
            $this->showMessage();
        }

        if ($this->copyCourse($groupId, array_keys($course), $scheduleTime)) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('global', "导入成功"));
            $this->addMessage('callback', 'cbCourse');
        }
        $this->showMessage();
    }

    //  调整上课时间
    public function actionScheduleRevise()
    {
        $cid = Yii::app()->request->getParam('id', '');
        if (Yii::app()->request->isPostRequest) {
            $sid = Yii::app()->request->getPost('sid', '');
            $cid = Yii::app()->request->getPost('cid', '');
            $gid = Yii::app()->request->getPost('gid', '');
            $revise_date = Yii::app()->request->getPost('revise_date', '');
            $revise_date = date('Ymd', strtotime($revise_date));
            $timestart_hour = Yii::app()->request->getPost('timestart_hour', '');
            $timestart_minute = Yii::app()->request->getPost('timestart_minute', '');
            $timeend_hour = Yii::app()->request->getPost('timeend_hour', '');
            $timeend_minute = Yii::app()->request->getPost('timeend_minute', '');

            $this->addMessage('state', 'fail');
            if (!$sid || !$cid || !$gid || !$revise_date || !$timestart_hour || !$timestart_minute || !$timeend_hour || !$timeend_minute) {
                $this->addMessage('message', '参数错误');
                $this->showMessage();
            }
            $scheduleItem = AsaScheduleItem::model()->findByPk($sid);
            if (!$scheduleItem) {
                $this->addMessage('message', '时间表不存在');
                $this->showMessage();
            }
            $courseSchedule = "{$cid}_{$scheduleItem->schedule_id}_{$scheduleItem->course_index}";

            if (AsaStaffAttendance::model()->findByAttributes(array('course_schedule_id' => $courseSchedule, 'status' => 1))) {
                $this->addMessage('message', '旧课程时间表存在签到记录');
                $this->showMessage();
            }
            $time_start = $timestart_hour . ':' . $timestart_minute;
            $time_end = $timeend_hour . ':' . $timeend_minute;
            $revise = AsaScheduleItemRevise::model()->findByAttributes(array('cid' => $cid, 'schedule_item_id' => $sid));
            if (!$revise) {
                $revise = new AsaScheduleItemRevise();
            }
            $revise->attributes = $scheduleItem->attributes;
            $revise->schedule_item_id = $sid;
            $revise->gid = $gid;
            $revise->cid = $cid;
            $revise->schedule_date = $revise_date;
            $revise->time_start = $time_start;
            $revise->time_end = $time_end;
            $revise->updated = time();
            $revise->updated_by = $this->staff->uid;
            if (!$revise->save()) {
                $error = current($revise->getErrors());
                $this->addMessage('message', $error[0]);
                $this->showMessage();
            }
            $this->addMessage('state', 'success');
            $this->showMessage();
        }
        $course = AsaCourse::model()->findByPk($cid);
        $criteria = new CDbCriteria();
        $criteria->compare('cid', $cid);
        $criteria->index = 'schedule_item_id';
        $revises = AsaScheduleItemRevise::model()->findAll($criteria);
        $this->renderPartial('scheduleRevise', array(
            'course' => $course,
            'revises' => $revises,
        ));
    }


    public function getJobType($data)
    {
        $typeList = CommonUtils::LoadConfig('CfgASA');
        return (Yii::app()->language == 'zh_cn') ? $typeList['job_type'][$data->job_type]['cn'] : $typeList['job_type'][$data->job_type]['en'];
    }

    public function getUnitType($data)
    {
        $typeList = CommonUtils::LoadConfig('CfgASA');
        return (Yii::app()->language == 'zh_cn') ? $typeList['unit_type'][$data->unit_type]['cn'] : $typeList['unit_type'][$data->unit_type]['en'];
    }


    public function getButton($data)
    {
        echo CHtml::link('编辑', array('updataStaffCourse', 'teacherid' => $data->id, "groupId" => $data->program_id, "branchId" => Yii::app()->controller->branchId), array('class' => 'J_modal btn btn-xs btn-info')) . ' ';
        echo CHtml::link('删除', array('deleteStaff', 'id' => $data->id, "branchId" => Yii::app()->controller->branchId), array('class' => 'J_ajax_del btn btn-xs btn-danger'));
    }

    public function getScheduleButton($data)
    {
        echo CHtml::link('编辑', array('schedule', 'scheduleId' => $data->id, "groupId" => $_GET['groupId'], "branchId" => Yii::app()->controller->branchId), array('class' => 'J_modal btn btn-xs btn-info')) . ' ';
        echo CHtml::link('删除', array('deleteSchedule', 'scheduleId' => $data->id, "branchId" => Yii::app()->controller->branchId), array('class' => 'J_ajax_del btn btn-xs btn-danger'));
    }

    public function getCourseButton($data)
    {
        //echo '<a href="" class="btn btn-xs btn-info mb5 teachermodal_open" onclick="teacherModal(' . $data->id . ')">分配老师';
        echo CHtml::button(Yii::t('asa', 'Assign Teachers'), array('class' => 'btn btn-xs btn-info teachermodal_open', 'onclick' => 'javascript:teacherModal(' . $data->id . ')')) . ' ';
        echo CHtml::link(Yii::t('asa', 'NameList'), array('showCourseInvoice', 'id' => $data->id, "branchId" => Yii::app()->controller->branchId), array('class' => 'J_modal btn btn-xs btn-info')) . ' ';
        echo CHtml::link('编辑', array('updateCourse', 'id' => $data->id, "branchId" => Yii::app()->controller->branchId), array('class' => 'J_modal btn btn-info btn-xs')) . ' ';
        echo CHtml::link('删除', array('deleteCourse', 'id' => $data->id, "branchId" => Yii::app()->controller->branchId), array('class' => 'J_ajax_del btn btn-danger btn-xs'));
    }

    public function getDefaultTime($data)
    {
        echo $data->default_time_start . "-" . $data->default_time_end;
    }


    /**
     * [copyCourses 复制课程]
     * @param  [int] $gid        [新的课程组ID]
     * @param  [array] $cids       [需要复制的课程ID]
     * @param  [int] $scheduleId [课程表ID]
     * @return [boolean]             [是否成功]
     */
    public function copyCourse($gid, $cids, $scheduleId)
    {
        if (!$gid || !is_array($cids) || !$scheduleId) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', "参数错误"));
            return false;
        }
        $group = AsaCourseGroup::model()->findByPk($gid);
        if (!$group) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message', "课程组不存在"));
            return false;
        }
        $courses = AsaCourse::model()->findAllByPk($cids);
        foreach ($courses as $course) {
            $newCourse = new AsaCourse();
            $newCourse->attributes = $course->attributes;
            $newCourse->gid = $gid;
            $newCourse->schedule_id = $scheduleId;
            if (!in_array($newCourse->refund_class_index, array(0, 1, 2, 3))) {
                $newCourse->refund_class_index = 0;
            }
            // 根据选择上课时间表更新新课的节数
            $scheduleModel = AsaSchedule::model()->findByPk($scheduleId);
            if (!$scheduleModel) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', '所选时间不存在'));
            }
            $newCourse->default_count = $scheduleModel->total_count;
            $newCourse->updated = time();
            $newCourse->updated_userid = $this->staff->uid;
            if ($newCourse->save()) {
                // 复制详细介绍
                $desc = AsaCourseDesc::model()->findByPk($course->id);
                if ($desc) {
                    $newDesc = new AsaCourseDesc();
                    $newDesc->attributes = $desc->attributes;
                    $newDesc->course_id = $newCourse->id;
                    $newDesc->updated = time();
                    $newDesc->updated_userid = $this->staff->uid;
                    $newDesc->save();
                }
                // 复制分配的老师
                $staffs = AsaCourseStaff::model()->findAllByAttributes(array('course_id' => $course->id, 'status' => 1));
                foreach ($staffs as $staff) {
                    $newStaff = new AsaCourseStaff();
                    $newStaff->attributes = $staff->attributes;
                    $newStaff->program_id = $newCourse->gid;
                    $newStaff->course_id = $newCourse->id;
                    $newStaff->updated = time();
                    $newStaff->updated_by = $this->staff->uid;
                    $newStaff->save();
                }
            } else {
                $error = current($newCourse->getErrors());
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', $error[0]));
            }
        }
        return true;
    }

    /**
     * 清理文件名中的特殊字符，使其符合HTTP头规范
     *
     * @param string $filename 原始文件名
     * @return string 清理后的安全文件名
     */
    function sanitize_filename($filename) {
        // 1. 移除可能引起问题的字符，例如：< > : " / \ | ? *
        //    以及一些控制字符
        $filename = preg_replace('/[<>:"\/\\\|?*\x00-\x1F]/', '', $filename);

        // 2. 将可能引起头解析问题的字符（如 , ;）替换为下划线
        $filename = str_replace(array(';', ','), '_', $filename);

        // 3. 替换掉多个连续的空格为一个下划线
        $filename = preg_replace('/\s+/', '_', $filename);
        
        // 4. 移除文件名开头和结尾的下划线或点
        $filename = trim($filename, '._-');

        return $filename;
    }
}
