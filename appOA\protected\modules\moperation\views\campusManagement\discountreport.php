
<div class="container-fluid">
	 <ol class="breadcrumb">
	     <li><?php echo CHtml::link(Yii::t('site','HQ Operations'), array('default/index'));?></li>
	     <li><?php echo CHtml::link(Yii::t('site','校园管理'), array('default/index'));?></li>
	     <li class="active"><?php echo Yii::t('site','折扣报表') ;?></li>
	 </ol>
	 <div class="row">
        <div class="col-md-12">
            <p class="form-inline">
	 		    <input type="text" class="form-control form-group" id="datepicker" value="<?php echo $date; ?>" placeholder="请选择日期">
                <small class="text-muted">统计账单范围：所有包含所选日期的账单，根据校园不同，可以含学年账单、学期账单或月账单。</small>
            </p>
        </div>
        <div>
            <?php foreach ($branchList as $type => $branchs) : ?>
                <div class="col-md-12 checkbox">
                    <div class="panel panel-default">
                      <div class="panel-heading">
                        <label>
                          <input type="checkbox" onclick="selectAll('<?php echo $type; ?>', this)"> <?php echo $typeList[$type]; ?>
                        </label>
                      </div>
                      <div class="panel-body">
            	 		<?php foreach ($branchs as $branchId=>$branch) : ?>
            			  <label>
            			    <input type="checkbox" onclick="selectBranch('<?php echo $branchId; ?>', '<?php echo $branch['title']; ?>' , this)" id="<?php echo $branchId; ?>"> <?php echo $branch['title']; ?>
            			  </label>
             			<?php endforeach; ?>
                      </div>
                    </div>
                </div>
            <?php endforeach; ?>          
        </div>
	</div>
    <div id="checked-data">

    </div>
 	<div id="charts">

 	</div>
</div>

<script type="text/template" id="report-item-template">
    <div id="chart_<%= branch_id %>" class="panel panel-default">
        <div class="panel-heading"><%= branch_name %></div>
        <div style="height:600px;" class="panel-body" id="chart_content_<%= branch_id %>"></div>
        <table class="table table-bordered table-hover" id="table">
            <thead>
                <colgroup>
                    <col width="150">
                    <col width="100">
                    <col width="100">
                    <col width="100">
                    <col width="100">
                    <col width="100">
                    <col width="100">
                </colgroup>
                <tr class="active">
                    <th>账单总数</th>
                    <th colspan="6"><%= total_num %></th>
                </tr>                
                <tr class="active">
                    <th>实际账单总额（单位：K）</th>
                    <th colspan="6"><%= numberFormat(total_amount) %></th>
                </tr>
                <tr>
                    <th>折扣名称</th>
                    <th>折扣前总额（单位：K）</th>
                    <th>折扣后总额（单位：K）</th>
                    <th>折扣率</th>
                    <th>折扣账单数</th>
                    <th>总人数占比</th>
                    <th>折扣金额（单位：K）</th>
                </tr>
            </thead>
            <tbody>
                <% _.each(discount_items, function(item, key, list) { %>
                    <tr>
                        <td><%= item.name %></td>
                        <td class="text-right"><%= numberFormat(item.original_amount) %></td>
                        <td class="text-right"><%= numberFormat(item.actual_amount) %></td>
                        <td class="text-right"><%= (item.actual_amount / item.original_amount * 100).toFixed(2)%>%</td>
                        <td class="text-right"><%= item.num %></td>
                        <td class="text-right"><%= (item.num / total_num * 100).toFixed(2) %>%</td>
                        <td class="text-right"><%= numberFormat((item.original_amount - item.actual_amount)) %></td>
                    </tr>
                <% }); %>
                <tr class="active">
                    <td>总计：</td>
                    <td class="text-right"><%= numberFormat(discount_original_amount) %></td>
                    <td class="text-right"><%= numberFormat(discount_actual_amount) %></td>
                    <% if(discount_original_amount == 0 ){per = 0}else{per = (discount_actual_amount / discount_original_amount * 100).toFixed(2)} %>
                    <td class="text-right"><%= per %>%</td>
                    <td class="text-right"><%= discount_num %></td>
                    <td class="text-right"><%= (discount_num / total_num * 100).toFixed(2) %>%</td>
                    <td class="text-right"><%= numberFormat((discount_original_amount - discount_actual_amount)) %></td>
                </tr>
            </tbody>
        </table>
    </div>
</script>

<script type="text/template" id="checked-data-template">
    <table class="table table-bordered table-hover">
        <thead>
            <colgroup>
                <col width="100">
                <col width="100">
                <col width="100">
                <col width="100">
                <col width="100">
                <col width="100">
            </colgroup>
            <tr>
                <th rowspan="2">学校</th>
                <th rowspan="2">实际账单总额（单位：K）</th>
                <th colspan="3">折扣账单</th>
                <th rowspan="2">整体折扣率</th>
            </tr>
            <tr>
                <th>折扣前总额（单位：K）</th>
                <th>折扣后总额（单位：K）</th>
                <th>折扣率</th>
            </tr>
        </thead>
        <tbody>
            <% _.each(items, function(item, key, list) { %>
                <tr>
                    <td class="text-left"><%= item.branch_name %></td>
                    <td class="text-left"><%= numberFormat(item.total_amount) %></td>
                    <td class="text-right"><%= numberFormat(item.discount_original_amount) %></td>
                    <td class="text-right"><%= numberFormat(item.discount_actual_amount) %></td>
                    <% if(item.discount_original_amount == 0 ){per = 0}else{per = (item.discount_actual_amount / item.discount_original_amount * 100).toFixed(2)} %>
                    <td class="text-right"><%= per %>%</td>
                    <td class="text-right"><%= ((item.total_amount / (item.discount_original_amount-item.discount_actual_amount+item.total_amount))*100).toFixed(2) %>%</td>
                </tr>
            <% }); %>
            <tr>
                <td>总计</td>
                <td><%= numberFormat(total_amount) %></td>
                <td class="text-right"><%= numberFormat(discount_original_amount) %></td>
                <td class="text-right"><%= numberFormat(discount_actual_amount) %></td>
                <td class="text-right"><%= (discount_actual_amount / discount_original_amount * 100).toFixed(2) %>%</td>
                <td class="text-right"><%= ((total_amount / (discount_original_amount-discount_actual_amount+total_amount))*100).toFixed(2) %>%</td>
            </tr>
        </tbody>
    </table>
</script>

<script>
    var template = _.template($('#report-item-template').html());
    var checked_template = _.template($('#checked-data-template').html());
    var data = {};
    var branchList = <?php echo json_encode($branchList); ?>;

	$( function() {
		$( "#datepicker").datepicker({
		  dateFormat: "yy-mm-dd",
		});
        $( "#datepicker").bind('change', function () {
            var date = $('#datepicker').val();
            window.location.href = '<?php echo $this->createUrl('discountReport'); ?>?date=' + date;
        });
	})

	function selectBranch(branch_id, branch_name, btn) {
        if ($(btn).attr('checked'))
        {
            if (!data[branch_id]) {
                data[branch_id] = {};
                $.ajax({
                    url: '<?php echo $this->createUrl("getDiscount")?>',
                    type: "post",
                    async: true,
                    dataType: 'json',
                    data: {
                        date: $('#datepicker').val(),
                        schoolid: branch_id,
                    },
                    success: function(res) {
                        if (res.state == 'success') {
                            data[branch_id].res = res.data;
                            data[branch_id].branch_name = branch_name;
                            renderChart(branch_id);
                            renderChecked();
                        } else {
                            alert("数据返回错误")
                        }
                    },
                    error: function() {
                        alert("请求错误")
                    }
                });
            } else {
                renderChart(branch_id);
                renderChecked();
            }
		}
		else
		{
		    $('.' + branch_id).remove();
		    $('#chart_' + branch_id).remove();
            renderChecked();
        }
	}

    function selectAll(type, btn) {
        $.each(branchList[type], function (branch_id, branch) {
            if ($(btn).attr('checked')){
                $('#' + branch_id).attr('checked', true);
            } else {
                $('#' + branch_id).attr('checked', false);
            }
            selectBranch(branch_id, branch.title, $('#' + branch_id));
        });

    }

    // 渲染选中学校图表
    function renderChart(branch_id) {
        var res = data[branch_id].res;
        var branch_name = data[branch_id].branch_name;
        // 表格
        var chart_data = {'legend_data':['折扣账单数 ' + (res.discount_num), '正常账单数 ' + res.undiscount_num], "detail_data":[]};
        var render_data = res;
        render_data['branch_id'] = branch_id;
        render_data['branch_name'] = branch_name;
        var view = template(render_data);

        $.each(res.discount_items, function (key, item) {
            var detail_data = ((item.original_amount-item.actual_amount)/1000).toFixed();
            chart_data['legend_data'].push(item.name);
            chart_data['detail_data'].push({value:detail_data, name:item.name});
        });
        $("#charts").prepend(view);
        var chart = echarts.init(document.getElementById('chart_content_'+branch_id));
        var option = {
            // title: {
            //     text: '折扣数量及总折扣占比'
            // },
            tooltip: {
                trigger: 'item',
                formatter: "{a} <br/>{b}: {c} ({d}%)"
            },
            legend: {
                orient: 'vertical',
                x: 'left',
                data:chart_data['legend_data']
            },
            series: [
                {
                    name:'折扣数量比',
                    type:'pie',
                    selectedMode: 'single',
                    radius: [0, '30%'],

                    label: {
                        normal: {
                            position: 'inner'
                        }
                    },
                    labelLine: {
                        normal: {
                            show: false
                        }
                    },
                    data:[
                        {value:res.discount_num, name:'折扣账单数 ' + (res.discount_num), selected:true},
                        {value:res.undiscount_num, name:'正常账单数 ' + res.undiscount_num},
                    ]
                },
                {
                    name:'总折扣占比',
                    type:'pie',
                    radius: ['40%', '55%'],
                    label: {
                        normal: {
                            formatter: '  {b|{b}：}{c}K  {per|{d}%}   ',
                            backgroundColor: '#eee',
                            borderColor: '#aaa',
                            borderWidth: 1,
                            borderRadius: 4,
                            // shadowBlur:3,
                            // shadowOffsetX: 2,
                            // shadowOffsetY: 2,
                            // shadowColor: '#999',
                            // padding: [0, 7],
                            rich: {
                                a: {
                                    color: '#999',
                                    lineHeight: 22,
                                    align: 'center'
                                },
                                // abg: {
                                //     backgroundColor: '#333',
                                //     width: '100%',
                                //     align: 'right',
                                //     height: 22,
                                //     borderRadius: [4, 4, 0, 0]
                                // },
                                hr: {
                                    borderColor: '#aaa',
                                    width: '100%',
                                    borderWidth: 0.5,
                                    height: 0
                                },
                                b: {
                                    fontSize: 15,
                                    lineHeight: 33
                                },
                                per: {
                                    color: '#eee',
                                    backgroundColor: '#334455',
                                    padding: [2, 4],
                                    borderRadius: 2
                                }
                            }
                        }
                    },
                    data:chart_data['detail_data']
                }
            ]
        };
        chart.setOption(option);
    }
    // 聚合选中的数据
    function renderChecked() {
        var checked_data = [];
        checked_data.items = [];
        checked_data.discount_original_amount = 0;
        checked_data.discount_actual_amount = 0;
        checked_data.total_amount = 0;
        $.each($('.checkbox input:checked'), function (key, item) {
           var branch_id = $(item).attr('id');
           if (branch_id != undefined && data[branch_id]) {
               if (data[branch_id].res) {

                    var branch_name = data[branch_id].branch_name;
                    var discount_actual_amount = Number(data[branch_id].res.discount_actual_amount);
                    var discount_original_amount = Number(data[branch_id].res.discount_original_amount);
                    var total_amount = Number(data[branch_id].res.total_amount);

                    checked_data.discount_original_amount += Number(discount_original_amount);
                    checked_data.discount_actual_amount += Number(discount_actual_amount);
                    checked_data.total_amount += Number(total_amount);
                    checked_data.items.push({branch_name:branch_name, discount_actual_amount:discount_actual_amount,discount_original_amount:discount_original_amount,total_amount:total_amount});
               }
           }
        });
        var view = checked_template(checked_data);
        $("#checked-data").html(view);
    }

    function numberFormat(number) {
        var number_k = (number/1000).toFixed();
        return new Intl.NumberFormat('zh-Hans-CN').format(number_k)
    }
</script>