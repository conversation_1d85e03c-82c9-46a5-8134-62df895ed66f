<style>
.panel-body{
    height:220px
}
.panel-footer{
    height:40px;
    font-size:16px
}
.addIcon{
    text-align: center;
    line-height: 200px;
    font-size: 60px;
    display: block;
}
i{
    text-align:center
}
i:before{
    cursor:pointer
}
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('/moperation/default/index'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','返校调查列表'), array('/moperation/backschool/index'))?></li>
    </ol>
    <div id='dataList' class='row'>
        <div class='col-md-12 mb20'>
            <ul class="nav nav-pills">
                <li v-for='(year,key,index) in calendarArr'  :class="yid == year.yid ? 'active':''" >
                    <a >{{year.startyear}}</a>
                </li>
            </ul>
        </div>
        <div class='col-md-10'>
            <div class='row'>
                <div class='col-md-3' v-for='(list,index) in surveyData'>
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h3 class="panel-title">{{list.cn_title}}</h3>
                        </div>
                        <div class="panel-body">
                            <!-- <h4>{{list.cn_title}}</h4> -->
                            <p>{{list.cn_intro}}</p>
                            <p>{{list.start_timestamp}} — {{list.end_timestamp}}</p>
                            <div>
                                <span class="label label-primary mr5 mb5 p5" style='display:inline-block' v-for='classData in list.class_type'>
                                {{classTypes[classData]}}</span>
                            </div>
                        </div>
                        <div class="panel-footer">
                            <i class='glyphicon glyphicon-pencil col-md-3' @click='editQus(list)'></i>
                            <i class='glyphicon glyphicon-th-list col-md-2'></i>
                            <i class='glyphicon glyphicon-qrcode col-md-2'></i>
                            <i class='glyphicon glyphicon-check col-md-2'></i>
                            <i class='glyphicon glyphicon glyphicon-trash col-md-3' @click='delTemplate(list,index)'></i>
                            <div class='clear:both'></div>
                        </div>
                    </div>
                </div>
                <div class='col-md-3'>
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h3 class="panel-title">增加</h3>
                        </div>
                        <div class="panel-body">
                            <i class='glyphicon glyphicon-plus addIcon'  @click='editQus(1)'></i>
                        </div>
                        <div class="panel-footer">
                            <i class='glyphicon glyphicon-plus col-md-6'  @click='editQus(1)'></i>
                            <i class='glyphicon glyphicon-upload col-md-6'></i>
                            <div class='clear:both'></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <ul class="list-group col-md-2">
            <a data-toggle="modal" data-target="#myModal" class="list-group-item"><span class=" glyphicon glyphicon-th-list"></span> 未分配年级类型</a>
            <a href="javascript:void(0)" class="list-group-item all" v-for='item in classAssigned'>{{classTypes[item]}}</a>
        </ul>
        <!-- 编辑框 -->
        <div class="modal fade bs-example-modal-lg" id="company" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true"  data-backdrop="static">
            <div class="modal-dialog modal-lg">
                <?php $form = $this->beginWidget('CActiveForm', array(
                        'id' => 'expense-forms',
                        'enableAjaxValidation' => false,
                        'action' => ($model->id) ? $this->createUrl('expenesAdd', array('expenesId' => $model->id)) : $this->createUrl('updateSurveyReturn'),
                        'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form', 'enctype' => "multipart/form-data"),
                    )); ?>
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                        <h4 class="modal-title" id="myModalLabel">编辑信息</h4>
                    </div>
                    <div class="modal-body">
                        <form class="form-horizontal">
                            <input type="text" :value='editQusData.yid' name='SurveyReturn[yid]' class="hidden">
                            <input type="text" :value='editQusData.status' name='SurveyReturn[status]'  class="hidden">
                            <input type="text" :value='editQusData.id' name='id'  class="hidden">
                            <div class="form-group">
                                <label for="inputPassword" class="col-sm-2 control-label">中文标题：</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control"  placeholder="请输入中文标题"  :value='editQusData.cn_title' id="inputPassword"  name='SurveyReturn[cn_title]'>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inputPassword" class="col-sm-2 control-label">英文标题：</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" placeholder="请输入英文标题"  :value='editQusData.en_title' id="inputPassword" name='SurveyReturn[en_title]'>
                                </div>
                            </div>
                            <div class="form-inline row mb15">
                                <label for="inputPassword" class="col-sm-2 control-label">开放日期：</label>
                                <div class="col-sm-10">
                                    <input type="text" class="form-control" id="startTime"  :value='editQusData.start_timestamp' name='SurveyReturn[start_timestamp]' placeholder="开始时间">
                                    —
                                    <input type="email" class="form-control" id="enddatepicker"  :value='editQusData.end_timestamp' name='SurveyReturn[end_timestamp]' placeholder="结束时间">
                                    <span>开放到结束日期24点</span>
                                </div>
                            </div>
                            <div style='clear:both'></div>
                            <div class="form-group">
                                <label for="inputPassword" class="col-sm-2 control-label">年级：</label>
                                <div class="col-sm-10">
                                        <label class="checkbox-inline" v-for='(item,key,index) in classTypes'>
                                            <input type="checkbox" :value='key' v-model='classIndex'   name='SurveyReturn[class_type][]'>{{item}}
                                        </label>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inputPassword" class="col-sm-2 control-label">中文提示文字</label>
                                <div class="col-sm-10">
                                    <textarea class="form-control" placeholder="请输入中文提示文字" rows='4' :value='editQusData.cn_intro'  name='SurveyReturn[en_intro]'></textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inputPassword" class="col-sm-2 control-label">英文提示文字</label>
                                <div class="col-sm-10">
                                    <textarea class="form-control" placeholder="请输入英文提示文字"  rows='4'  :value='editQusData.en_intro' name='SurveyReturn[cn_intro]'></textarea>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary J_ajax_submit_btn">确定</button>
                        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    </div>
                </div>
                <!-- /.modal-content -->
                <?php $this->endWidget(); ?>
            </div>
            <!-- /.modal -->
        </div>
    </div>
</div>
<script>
$(function() {
    $( "#startTime" ).datepicker({
		  dateFormat: "yy-mm-dd",
		});
        $( "#enddatepicker" ).datepicker({
		  dateFormat: "yy-mm-dd",
		});
	});
	var classAssigned = <?php echo json_encode($classAssigned) ?>;//未分配得年级
	var classTypes = <?php echo json_encode($classTypes) ?>;//全部年级
	var calendarArr = <?php echo json_encode($calendarArr) ?>;//学年列表
	var surveyData = <?php echo json_encode($surveyData) ?>;//增加得返校模板
	var yid = '<?php echo $yid ?>';//学年
    console.log(yid)
    console.log(classAssigned)

    console.log(calendarArr)
    console.log(classTypes)
    console.log(surveyData)
        var datalist = new Vue({
        el: "#dataList",
        data: {
            yid:yid,
            classAssigned:classAssigned,
            classTypes:classTypes,
            calendarArr:calendarArr,
            surveyData:surveyData,
            editQusData:"",
            classIndex:[]
        },
        updated: function () {
        },
        computed: {
        },
        methods: {
            editQus(data){
                console.log(data)
                if(data==1){
                    this.editQusData=''
                    this.classIndex=[]
                }else{
                    this.editQusData=data
                    this.classIndex=data.class_type
                }
                $('#company').modal('show')
            },
            delTemplate(data,index){
                $.ajax({
                    url: '<?php echo $this->createUrl("delete")?>',
                    type: "post",
                    async: true,
                    dataType: 'json',
                    data: {
                        survey_id:data.id
                    },
                    success: function(data) {
                        console.log(data)
                        if(data.state == 'success') {
                            datalist.surveyData.splice(index,1);
                            resultTip({"msg": data.message})
                        } else {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        console.log(data)
                        alert("请求错误")
                    }
                });
            }
        }

    })
    function  cbSuccess(data){
        $('#company').modal('hide')
        resultTip({
            "msg": '添加成功'
        })
    }
</script>
