<style>
    .kindergarten{
        margin-top:0;
    }
    .title{
        margin-top: 8px;
        margin-bottom:0px !important;
        padding-bottom: 8px;
        border-bottom: 1px solid #D9D9D9;
    }
    .bordnone:before{
       border-top:none;
    }
    .weui_panel:after{
        border-bottom: none
    }
    .weui_cells:before{
        border-top:none
    }
    #weixinTip{
    position: fixed;
    left: 0;
    display: none;
    top: 0;
    height:100%;
    width: 100%;
    z-index: 99;
    background-color: rgba(0,0,0,0.8);
    filter: alpha(opacity=80);
}
.arrow{
    max-width: 100%;
    height: auto;
    width:75px;
    float:right;
    margin-top:20px;
    margin-right: 20px
}
.pto{
    width: 60%;
    text-align: center;
    margin-left: 20%;
    margin-top: 10%;
}
</style>
<div class="cell">
 <div class="weui_cells_title"><?php echo Yii::t("portfolio", "Semester Report"); ?></div>
    <div class="weui_cells weui_cells_access">
        <?php 
            //选择孩子
            $this->widget('ext.wechat.ChildSelector',array(
            'childObj'=>$childObj,
            'myChildObjs'=>$this->myChildObjs,
            'jumpUrl'=>'semester',
            ));
         ?>
    </div>
    <div>
    <?php if (count($reportList['g'])): ?>
    
    <div class="weui_panel weui_panel_access">
        <!-- 中学 -->
            <?php foreach ($reportList['g'] as $year => $gitem) : ?>
                <div class="weui_panel_bd"><?php echo IvyClass::formatSchoolYear($year); ?></div>
                <div class="weui_media_box weui_media_small_appmsg">
                <div class="weui_cells weui_cells_access">
                <?php foreach ($gitem as $v2) : ?>
                    <a class="weui_cell" href="<?php echo $this->createUrl('downloadSemeter3', array('childid'=>$childid, 'reportid'=>$v2['id'])); ?>" target="_blank">
                        <div class="weui_cell_hd"><img src="<?php echo Yii::app()->theme->baseUrl; ?>/images/smpdf.png" alt="" style="width:20px;margin-right:5px;display:block"></div>
                        <div class="weui_cell_bd weui_cell_primary">
                            <p><?php echo $v2['title']; ?></p>
                        </div>
                        <span class="weui_cell_ft"></span>
                    </a>  
                <?php endforeach; ?>
                </div>
                </div>
            <?php endforeach; ?>  
    </div>
    <?php endif; ?>
    <?php if (count($reportList['k'])): ?>
    <div class="weui_panel weui_panel_access">
        <!-- 小学 -->
            <?php foreach ($reportList['k'] as $year => $kitem) : ?>
                <div class="weui_panel_hd"> <?php echo IvyClass::formatSchoolYear($year); ?></div>
                <div class="weui_media_box weui_media_small_appmsg">
                <div class="weui_cells weui_cells_access">
                    <a class="weui_cell" href="<?php echo $this->createUrl('downloadSemeter2', array('reportid'=>current($kitem))); ?>" target="_blank">
                        <div class="weui_cell_hd"><img src="<?php echo Yii::app()->theme->baseUrl; ?>/images/smpdf.png" alt="" style="width:20px;margin-right:5px;display:block"></div>
                        <div class="weui_cell_bd weui_cell_primary">
                            <p><?php echo IvyClass::formatSchoolYear($year); ?></p>
                        </div>
                        <span class="weui_cell_ft"></span>
                    </a>  
                </div>
                </div>
            <?php endforeach; ?>
    </div>
    <?php endif; ?>
    <?php if (count($reportList['m'])): ?>
    <div class="weui_panel weui_panel_access">
        <!-- 幼儿园 -->
            <?php foreach ($reportList['m'] as $year => $mitem):?>
                <div class="weui_panel_hd"> 
                    <?php
                        echo IvyClass::formatSchoolYear($year);
                    ?>
                </div>
                <div class="weui_media_box weui_media_small_appmsg">
                <div class="weui_cells weui_cells_access">
                <?php foreach($mitem as $v): ?>
                    <a class="weui_cell" href="<?php echo $this->createUrl('downloadSemeter', array('reportid'=>$v['id'])); ?>" target="_blank">
                        <div class="weui_cell_hd"><img src="<?php echo Yii::app()->theme->baseUrl; ?>/images/smpdf.png" alt="" style="width:20px;margin-right:5px;display:block"></div>
                        <div class="weui_cell_bd weui_cell_primary">
                            <p><?php echo $v['title']; ?></p>
                        </div>
                        <span class="weui_cell_ft"></span>
                    </a>  
                <?php endforeach;?>
                    </div>
            </div>
            <?php endforeach;?>
        </div>
    <?php endif; ?>
    </div>
</div>
<div id="weixinTip">
    <img src="<?php echo Yii::app()->theme->baseUrl . '/images/live_weixin.png'; ?>" class="arrow" />
    <img src="<?php echo Yii::app()->theme->baseUrl . '/images/' . Yii::t('portfolio','pandaen') . '.png'; ?>" class="pto">
</div>
<script>
    function is_weixin() {
        var ua = navigator.userAgent.toLowerCase();
        if (ua.match(/MicroMessenger/i) == "micromessenger") {
            return true;
        } else {
            return false;
        }
    }
    var isWeixin = is_weixin();
    function hrefall(event){
         event = window.event || event;
         if(isWeixin){ // 如果不让跳转href对应地址
             $('#weixinTip').show()
            if(event.preventDefault){ // 禁止默认事件
                event.preventDefault();
            }else{
                event.returnValue = false;
            }
        }
    }
</script>