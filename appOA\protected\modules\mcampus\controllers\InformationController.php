<?php

class InformationController extends BranchBasedController{
    public $actionAccessAuths = array(
        'index'             => 'o_A_Access',
    );

    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }
    
    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";
        Yii::import('common.models.attendance.*');

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = true;
        $type = Yii::app()->request->getParam('type', '50');
        $this->branchSelectParams['urlArray'] = array('//mcampus/information/index','type' => $type);
    }
    

    public function actionIndex()
    {
        $id = Yii::app()->request->getParam('id', 0);
        $pid = Yii::app()->request->getParam('pid', 0);
        $type = Yii::app()->request->getParam('type', 0);

        $criteria = new CDbCriteria();
        $criteria->compare('schoolid', $this->branchId);
        $criteria->compare('pid', 0);
        $criteria->order='updated desc';
        $criteria->index = "id";
        $structureObj = InformationFrame::model()->findAll($criteria);

        $structureObjT = array();
        $structureList = array();
        if($id){
            $criteria = new CDbCriteria();
            $criteria->compare('schoolid', $this->branchId);
            $criteria->compare('pid', $id);
            $criteria->order='updated desc';
            $structureObjT = InformationFrame::model()->findAll($criteria);

            $criteria = new CDbCriteria();
            $criteria->compare('schoolid', $this->branchId);
            $criteria->compare('pid', array_keys($structureObj));
            $modelObj = InformationFrame::model()->findAll($criteria);
            if($structureObjT){
                foreach($structureObj as $item){
                    $title = $item->getName();
                    foreach($modelObj as $items){
                        if($item->id == $items->pid){
                            $structureList[$title][$items->id] = $items->getName();
                        }
                    }
                }
            }
        }
        /*$mailIds = CommonUtils::LoadConfig('CfgInformation');*/
        $criteria = new CDbCriteria();
        $criteria->compare('schoolid',$this->branchId);
        $criteria->compare('cagegory', $pid);

        $dataProvider = new CActiveDataProvider(new InformationDs, array(
            'criteria'=>$criteria,
            'pagination'=>array('pageSize'=>20),
            'sort' => array(
                'defaultOrder'=>array(
                    'published'=>CSort::SORT_DESC,
                    'times'=>CSort::SORT_DESC,
                )
            ),
        ));
        $model = new InformationDs;
        $Informationlist = CommonUtils::LoadConfig('CfgInformation');

        $this->render('index',array(
            "dataProvider" => $dataProvider,
            "type" => $type,
            "model" => $model,
            "Informationlist" => $Informationlist,
            "structureObj" => $structureObj,
            "structureObjT" => $structureObjT,
            "id" => $id,
            "pid" => $pid,
            "structureList" => $structureList,
            'modalTitle'=>'新建记录',
        ));
    }

    public function actionStructure()
    {
        $criteria = new CDbCriteria();
        $criteria->compare('schoolid', $this->branchId);
        $criteria->compare('pid', 0);
        $criteria->order='updated desc';
        $structureObj = InformationFrame::model()->findAll($criteria);

        $criteria = new CDbCriteria();
        $criteria->compare('schoolid', $this->branchId);
        $criteria->compare('pid', ">0");
        $criteria->order='updated desc';
        $items = InformationFrame::model()->findAll($criteria);

        $this->render('_structure', array('items'=>$items, 'structureObj' => $structureObj));
    }

    //外面大分组
    public function actionEdit($id=0, $category='')
    {
        $model = InformationFrame::model()->findByPk($id);
        if($model == null){
            $model = new InformationFrame;
        }

        if(isset($_POST['InformationFrame'])){
            $model->attributes = $_POST['InformationFrame'];
            $model->schoolid = $this->branchId;
            if($category == "grade"){
                $model->pid = 0;
            }
            $model->updated = time();
            $model->updated_by = Yii::app()->user->id;
            if($model->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('refresh', true);
            }
            else{
                $this->addMessage('state', 'fail');
                $errs = current($model->getErrors());
                $this->addMessage('message', $errs?$errs[0]:Yii::t('message','Failed!'));
            }
            $this->showMessage();
        }

        $grade = array();
        if($category == "class"){
            $criteria = new CDbCriteria();
            $criteria->compare('schoolid', $this->branchId);
            $criteria->compare('pid', 0);
            $structureObj = InformationFrame::model()->findAll($criteria);
            if($structureObj){
                foreach($structureObj as $item){
                    $grade[$item->id] = Yii::app()->language == 'zh_cn'  ? $item->title_cn : $item->title_en ;
                }
            }
        }

        $this->renderPartial('deptedit', array(
            'model'=>$model,
            "category" => $category,
            'grade' => $grade
        ));
    }


    //删除第一分类
    public function actionGradeDel()
    {
        if(Yii::app()->request->isPostRequest){
            $id = Yii::app()->request->getParam('id', 0);
            if($id){
                $criteria = new CDbCriteria();
                $criteria->compare('schoolid', $this->branchId);
                $criteria->compare('pid', $id);
                $count = InformationFrame::model()->count($criteria);
                if($count){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '请先删除第二分类！');
                }
                else{
                    $model = InformationFrame::model()->findByPk($id);
                    if($model->delete()){
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', '删除成功！');
                        $this->addMessage('refresh', true);
                    }
                    else{
                        $this->addMessage('state', 'fail');
                        $this->addMessage('message', '删除失败！');
                    }
                }
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '参数错误！');
            }
            $this->showMessage();
        }
    }

    //删除第二分类
    public function actionClassDel()
    {
        if(Yii::app()->request->isPostRequest){
            $id = Yii::app()->request->getParam('id', 0);
            if($id){
                $frameObj = InformationFrame::model()->findByPk($id);
                $criteria = new CDbCriteria();
                $criteria->compare('schoolid', $this->branchId);
                $criteria->compare('cagegory', $frameObj->id);
                $count = InformationDs::model()->count($criteria);
                if($count){
                    $this->addMessage('state', 'fail');
                    $this->addMessage('message', '请先分类下的内容！');
                }
                else{
                    if($frameObj->delete()){
                        $this->addMessage('state', 'success');
                        $this->addMessage('message', Yii::t('message','success'));
                        $this->addMessage('refresh', true);
                    }
                }
            }
            else{
                $this->addMessage('state', 'fail');
                $this->addMessage('message', '参数错误！');
            }
            $this->showMessage();
        }
    }


    //增加内容
    public function actionInform()
    {
        $Informationlist = CommonUtils::LoadConfig('CfgInformation');
        $type = Yii::app()->request->getParam('type', '');
        $id = Yii::app()->request->getParam('id', '');

        $model = InformationDs::model()->findByPk($id);
        if(!$model){
        $model = new InformationDs;
        }

        if($_POST){
            $model->schoolid = $this->branchId;
            $model->attributes=$_POST['InformationDs'];
            $model->times= strtotime($model->times);
            $model->updated = time();
            $model->uid = Yii::app()->user->id;
            $model->inform_file_cn = CUploadedFile::getInstance($model,'inform_file_cn');
            $model->inform_file_en = CUploadedFile::getInstance($model,'inform_file_en');

            if($model->save()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('callback', 'cbInformation');
            }else{
                $this->addMessage('state', 'fail');
                $errs = current($model->getErrors());
                $this->addMessage('message', $errs?$errs[0]:Yii::t('message','Failed!'));
            }
            $this->showMessage();

        }

        /*$this->renderpartial('informAdd', array(
            'model'=>$model,
            'Informationlist'=>$Informationlist,
            'id'=>$id,
        ));*/
    }

    // 查看文件
    public function actionDownloads()
    {
        $conditions = Yii::app()->request->getParam('attachment', 0);
        if($conditions){
            $file_dir = Yii::app()->params['OAUploadBasePath'] . '/information/';
            $fileres = file_get_contents($file_dir . $conditions);
            header('Content-type: application/pdf');
            echo $fileres;
        }
    }

    public function actionInformEied()
    {
        $id = Yii::app()->request->getParam('id', '');
        $model = InformationDs::model()->findByPk($id);
        if($model){
            $inform = array();
            $inform['id'] = $model->id;
            $inform['schoolid'] = $model->schoolid;
            $inform['times'] = date('Y-m-d' ,$model->times);
            $inform['cagegory'] = $model->cagegory;
            $inform['title_en'] = $model->title_en;
            $inform['title_cn'] = $model->title_cn;
            $inform['file_en'] = $model->file_en;
            $inform['file_cn'] = $model->file_cn;
            $inform['published'] = ($model->published) ? 1 : 0;
            $inform['is_home'] = ($model->is_home) ? 1 : 0;
            echo json_encode($inform);
        }
    }

    public function actionInformdel()
    {
        $id = Yii::app()->request->getParam('id', '');

        $model = InformationDs::model()->findByPk($id);
        if($model){
            if($model->delete()){
                $this->addMessage('state', 'success');
                $this->addMessage('message', Yii::t('message','success'));
                $this->addMessage('callback', 'cbInformation');
            }else{
                $this->addMessage('state', 'fail');
                $errs = current($model->getErrors());
                $this->addMessage('message', $errs?$errs[0]:Yii::t('message','Failed!'));
            }
            $this->showMessage();
        }
    }

    public function getButtons($data)
    {
        /*echo '<a class="J_modal btn btn-info btn-xs" href="'.$this->createUrl('Inform',array("id"=>$data->id)).'"><span class="glyphicon glyphicon-pencil"></span></a> ';*/
        echo '<a class="btn btn-info btn-xs" href="javascript:void(0);" data-backdrop="static" onclick="informEdit('.$data->id.')"><span class="glyphicon glyphicon-pencil"></span></a> ';
        echo '<a class="J_ajax_del btn btn-danger btn-xs" href="'.$this->createUrl("Informdel", array("id"=>$data->id)).'"><span class="glyphicon glyphicon-remove"></span></a>';
    }

    public function getMenu()
    {
        $Informationlist = CommonUtils::LoadConfig('CfgInformation');
        $type = ($_GET['type']) ? $_GET['type'] : 50;
        $mainMenu = array();
        $mainMenu[] = array('label'=>$Informationlist['category'][50], 'url'=>array('//mcampus/information/index', 'type'=>50), 'active'=>($type == 50 )?true:false);
        $mainMenu[] = array('label'=>$Informationlist['category'][60], 'url'=>array('//mcampus/information/structure', 'type'=>60), 'active'=>($type == 60 )?true:false);

        return $mainMenu;
    }

    public function getPublished($data)
    {
        switch ($data->published)
        {
            case 0:
                echo "否";
                break;
            case 1:
                echo "是";
                break;
        }

    }
    
}

