<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','Campus Operations'), array('/mcampus/default/index'))?></li>
        <li class="active"><?php echo Yii::t('site','Marketing Activity') ?></li>
    </ol>

    <div class="row">
        <div class="col-md-1 col-sm-2">
            <?php
            $mainMenu = array(
                array('label'=>Yii::t('user','活动管理'), 'url'=>array("/mcampus/event/index")),
            );

            $this->widget('zii.widgets.CMenu',array(
                'items'=> $mainMenu,
                'id'=>'pageCategory',
                'htmlOptions'=>array('class'=>'nav nav-pills nav-stacked text-right background-gray'),
                'activeCssClass'=>'active',
                'itemCssClass'=>''
            ));
            ?>
        </div>
        <div class="col-md-11 col-sm-10">
            <div class="background-gray p8">
                <?php echo CHtml::link('<span class="glyphicon glyphicon-plus"></span> 添加活动', array('update'), array('class'=>'J_dialog btn btn-primary', 'title'=>'添加活动'));?>
            </div>
            <?php
            $this->widget('ext.ivyCGridView.BsCGridView', array(
                'id'=>'event-grid',
                'dataProvider'=>$model,
            //	'filter'=>$model,
                'template'=>"{items}{pager}{summary}",
                'colgroups'=>array(
                    array(
                        "colwidth"=>array(null,150,150),
            //            "htmlOptions"=>array("align"=>"left", "span"=>2)
                    )
                ),
                'columns'=>array(
                    array(
                        'name'=> Yii::t('event', 'Title'),
                        'value'=>'CommonUtils::autoLang($data->cn_title, $data->en_title)',
                    ),
                    array(
                        'name'=> Yii::t('event', 'Event Date'),
                        'value'=>'OA::formatDateTime($data->event_date)',
                    ),
                    array(
                        'name' => Yii::t('global', 'Action'),
                        'type' => 'raw',
                        'value' => array($this, 'getButtons'),
                    ),
                ),
            )); ?>
        </div>
</div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>