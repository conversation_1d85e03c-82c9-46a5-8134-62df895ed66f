<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'clean-form',
	'enableAjaxValidation'=>false,
	'htmlOptions'=>array('class'=>'J_ajaxForm'),
));
?>

<div class="pop_cont" style="height:auto;">
    <div class="tips_light"><?php echo Yii::t("payment", '本功能在家长主动放弃或者长时间无法取得联系时使用，处置之后孩子个人账户余额将清零，这部分金额将转入收入。')?></div>
    <h2><?php echo $model->startyear . '~' . ($model->startyear+1) .' '. Yii::t("payment", '预缴学费余额');?> <?php echo OA::formatMoney($model->amount)?></h2>
    <p><?php echo $form->checkBox($model,'confirm'); ?> <?php echo $form->labelEx($model,'confirm'); ?></p>
    <?php echo $form->hiddenField($model, 'startyear');?>
</div>

<div class="pop_bottom">
    <button type="button" class="btn fr" id="J_dialog_close"><?php echo Yii::t('global','Cancel');?></button>
    <button class="btn fr btn_submit mr10 J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button>
</div>
<?php $this->endWidget(); ?>