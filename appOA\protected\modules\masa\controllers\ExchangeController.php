<?php

class ExchangeController extends BranchBasedController
{
    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site','Campus Workspace');

        $this->branchSelectParams['urlArray'] = array('//masa/exchange/index');

        // jquery ui
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/vue2.js');
    }

	public function actionIndex()
    {
        $groupId = Yii::app()->request->getParam('groupId', 0);
        $courseId = Yii::app()->request->getParam('courseId', 0);
        $year = Yii::app()->request->getParam('year', 0);

        $criteria=new CDbCriteria;
        $criteria->compare('schoolid', $this->branchId);
        $criteria->compare('program_type', "<>10");
        $criteria->index = "id";
        $courseGroups = AsaCourseGroup::model()->findAll($criteria);

        $criteria=new CDbCriteria;
        $criteria->compare('site_id', $this->branchId);
        $criteria->index = "group_id";
        $share = AsaCourseGroupShare::model()->findAll($criteria);
        if($share){
            $criteria=new CDbCriteria;
            $criteria->compare('id', array_keys($share));
            $criteria->index = "id";
            $group = AsaCourseGroup::model()->findAll($criteria);
        }

        $courseGroup = ($group) ? $courseGroups + $group : $courseGroups;
        $courseGroupArr = json_decode(CJSON::encode($courseGroup), TRUE);
        $courseGroupArr = array_values($courseGroupArr);

        usort($courseGroupArr, function($a, $b) {
            return $b['startyear']-$a['startyear'];
        });
        $newCourseGroup = array();
        $canReplaceCourseGroup = array();
        foreach ($courseGroupArr as $v) {
            $newCourseGroup[$v['startyear']][] = $v;
            if($v['startyear']>=$year){
                $canReplaceCourseGroup[] = $v;
            }
        }
        $startyearArr = array_keys($newCourseGroup);

        $courseList = array();
        $courseObj = array();
        $group = array();
        if($groupId){
            $criteria=new CDbCriteria;
            $criteria->compare('gid', $groupId);
            $criteria->compare('status', "<>99");
            $criteria->index = "id";
            $courseObj = AsaCourse::model()->findAll($criteria);
            foreach($courseObj as $item){
                $courseList[$item->id] = $item->getTitle();
            }
        }

        $childList = array();
        if($courseId){
            $criteria=new CDbCriteria;
            $criteria->compare('course_id', $courseId);
            $criteria->compare('status', AsaInvoice::STATS_PAID);
            $criteria->compare('spot_hold', 1);
            $invoiceItemObj = AsaInvoiceItem::model()->findAll($criteria);
            $childId = array();
            if($invoiceItemObj){
                foreach($invoiceItemObj as $item){
                    $childId[] = $item->asainvoice->childid;
                }
                if($childId){
                    $criteria=new CDbCriteria;
                    $criteria->compare('childid', $childId);
                    $criteria->index = 'childid';
                    $childObj = ChildProfileBasic::model()->findAll($criteria);
                    foreach($childObj as $item){
                        $childList[$item->childid] = array(
                            'childid' => $item->childid,
                            'childName' => $item->getChildName(),
                            'classid' => $item->ivyclass->title,
                        );

                    }
                }
            }
        }

        $this->render('index', array(
            'courseGroup' => $canReplaceCourseGroup,
            'newCourseGroup' => $newCourseGroup,
            'groupId' => $groupId,
            'courseList' => $courseList,
            'courseObj' => $courseObj,
            'courseId' => $courseId,
            'childObj' => $childList,
            'startyearArr'=>$startyearArr
        ));
	}

	public function actionDemandCourse()
    {
        $groupId = Yii::app()->request->getParam('groupId', 0);
        $courseObj = array();
        if($groupId){
            $criteria=new CDbCriteria;
            $criteria->compare('gid', $groupId);
            $criteria->index = "id";
            $courseObj = AsaCourse::model()->findAll($criteria);
            echo CJSON::encode($courseObj);
        }else{
            echo CJSON::encode($courseObj);
        }
    }

    public function actionShowRefund()
    {
        $childId = Yii::app()->request->getParam('childId', 0);
        $courseId = Yii::app()->request->getParam('courseId', 0);

        $criteria=new CDbCriteria;
        $criteria->compare('asainvoice.childid', $childId);
        $criteria->compare('t.course_id', $courseId);
        $criteria->compare('t.status', AsaInvoice::STATS_PAID);
        $criteria->compare('t.spot_hold', AsaInvoiceItem::SPOT_HOLD_ACTIVE);
        $criteria->with = 'asainvoice';
        $criteria->order = 't.id DESC';
        $criteria->limit = 1;
        $item = AsaInvoiceItem::model()->find($criteria);  // 现在上的课程的账单

        $criteria=new CDbCriteria;
        $criteria->compare('invoice_item_id', $item->id);
        $criteria->compare('status', array(1,2,3,4,5));
        $refundObj = AsaRefund::model()->findAll($criteria);
        $courseData = array(
            'courseUnitPrice' => $item->unit_price,
            'courseNumber' => $item->class_count,
            'status' => 0,
        );

        if($refundObj){
            $courseData['status'] = 99;
            if(!$refundObj->asaCourseGroup->program_type == 20 ){ // 等于夏令营时候 有退费直接不让在换课
                $isRefund = 0;
                $courseRefund = 0;
                $refund = array();
                foreach($refundObj as $item){

                    if(in_array($item->status, array(1,2,3,4))){
                        $isRefund = 99; //有退费
                    }
                    if(in_array($item->refund_type, array(1,2,4)) && $item->status ==5){
                        $courseRefund = 99; //课程已经退班
                    }
                    if($item->refund_type == 3 && $item->status ==5){
                        $refund[] = count($item->asaRefunditem);
                    }
                }

                if($isRefund || $courseRefund){
                    $courseData['status'] = 99;
                }else if ($refund) {
                    $courseData['status'] = array_sum($refund);
                }
            }
            echo CJSON::encode($courseData);
        }else{
            echo CJSON::encode($courseData);
        }
    }


    /**
     *
     * 参数描述
     * oldCourseId   老课程ID
     * newCourseId   新课程ID
     * oldCourseDetail   老课程ID
     * newCourseDetail   新课程ID
     * childId   孩子ID
     *
     */

    public function actionUpdataExchange()
    {
        $oldCourseId = Yii::app()->request->getParam('oldCourseId', 0);
        $NewCourseId = Yii::app()->request->getParam('NewCourseId', 0);
        $childId = Yii::app()->request->getParam('childId', 0);
        $difference = Yii::app()->request->getParam('difference', 0);
        $invoiceTitle = Yii::app()->request->getParam('inoviceTitle', "");
        $oldCourseDetail = Yii::app()->request->getParam('oldCourseDetail', 0);
        $newCourseDetail = Yii::app()->request->getParam('newCourseDetail', 0);   //1

        if(!$oldCourseId){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message','旧课程ID 不能为空'));
            $this->showMessage();
        }

        if(!$difference){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message','差额 不能为空'));
            $this->showMessage();
        }

        if($difference < 0 ) {
            if (!$invoiceTitle) {
                $this->addMessage('state', 'fail');
                $this->addMessage('message', Yii::t('message', '账单标题 不能为空'));
                $this->showMessage();
            }
        }

        if(!$NewCourseId){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message','新课程ID 不能为空'));
            $this->showMessage();
        }

        if(!$childId){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message','学生ID 不能为空'));
            $this->showMessage();
        }

        if($oldCourseDetail === ''){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message','旧课程上的节数 不能为空'));
            $this->showMessage();
        }

        if(!$newCourseDetail){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message','新课程 要上的节数不能为空'));
            $this->showMessage();
        }

        $criteria=new CDbCriteria;
        $criteria->compare('asainvoice.childid', $childId);
        $criteria->compare('t.course_id', $oldCourseId);
        $criteria->compare('t.status', AsaInvoice::STATS_PAID);
        $criteria->compare('t.spot_hold', AsaInvoiceItem::SPOT_HOLD_ACTIVE);
        $criteria->with = 'asainvoice';
        $criteria->order = 't.id DESC';
        $criteria->limit = 1;
        $invoiceItemModel = AsaInvoiceItem::model()->find($criteria);  // 现在上的课程的账单   1

        $oldCourseObj = AsaCourse::model()->findByPk($oldCourseId); // 老课程对象
        $newCourseObj = AsaCourse::model()->findByPk($NewCourseId); // 新课程对象   1
        $oldRefundData = AsaRefund::refundMoery($childId,$oldCourseId); // 退费的钱数 和 退费的课程数

        if($invoiceItemModel->class_count - $oldCourseDetail - $oldRefundData['refundAmount'] < 1){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message','课程余额不足'));
            $this->showMessage();
        }    // 上课时间+ 退费时间 大于 总共课时

        $oldCoursenum = $invoiceItemModel->actual_total - ($invoiceItemModel->unit_price * $oldCourseDetail) - $oldRefundData['refundMoney']; // 余额 = 总钱数 - 上过课程的钱数 - 退费的钱数

        $diffAmount = $oldCoursenum - $newCourseObj->unit_price * $newCourseDetail;

        if(abs($difference - $diffAmount)  > 0.001){
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message','差价不一致'));
            $this->showMessage();
        }

        $criteria=new CDbCriteria;
        $criteria->compare('course_id', $NewCourseId);
        $courseNumber = AsaCourseNumber::model()->find($criteria);
        if($newCourseObj->capacity <= $courseNumber->number_taken + $courseNumber->number_hold)
        {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', Yii::t('message','新课程已经满班'));
            $this->showMessage();
        }

        $exchangeModel = new AsaCourseExchange();
        $exchangeModel->childid = $childId;
        $exchangeModel->schoolid = $this->branchId;
        $exchangeModel->new_cid = $newCourseObj->id;
        $exchangeModel->old_cid = $oldCourseObj->id;
        $exchangeModel->new_course_num = $newCourseDetail;
        $exchangeModel->old_course_num = $oldCourseDetail;
        $exchangeModel->new_course_amount = $newCourseObj->unit_price * $newCourseDetail;
        $exchangeModel->old_course_amount = $oldCoursenum;
        $exchangeModel->updated_userid = $this->staff->uid;
        $exchangeModel->updated_time = time();
        $exchangeModel->create_userid = $this->staff->uid;
        $exchangeModel->create_time = time();

        //Yii::msg($exchangeModel);
        $status = $this->ExecuteExchange($newCourseObj, $exchangeModel, $invoiceItemModel, $invoiceTitle);
        if($status){
            $this->addMessage('state', 'success');
            $this->addMessage('message', Yii::t('message', "换课申请已提交"));
            $this->addMessage('callback', 'exchangeSuccess');
        }else{
            $this->addMessage('state', 'fail');
        }
        $this->showMessage();
        //   判断新课程是否已经满班
    }

    /**
     * [ExecuteExchange 执行换课操作]
     * @param [type] $newCourseModel   [新课程model]
     * @param [type] $exchangeModel    [换课表model]
     * @param [type] $invoiceItemModel [旧课程账单关联表model]
     */
    public function ExecuteExchange($newCourseModel, $exchangeModel, $invoiceItemModel, $invoiceTitle)
    {
        $childid = $exchangeModel->childid;
        $childModel = ChildProfileBasic::model()->findByPk($childid);
        $schoolid = $exchangeModel->schoolid;

        $newCourseAmount = $exchangeModel->new_course_amount;   // 新课程价格
        $oldCourseAmount = $exchangeModel->old_course_amount;   // 旧课程价格

        $newCourseNum = $exchangeModel->new_course_num;    // 新课程数量
        $oldCourseNum = $exchangeModel->old_course_num;    // 旧课程数量
        
        $newCourseModel = $newCourseModel;
        $oldCourseModel = $invoiceItemModel->asacourse;
        if (!$newCourseModel || !$oldCourseModel) {
            return false;
        }
        if ($newCourseAmount < 0 || $oldCourseAmount < 0 || $newCourseNum < 0 || $oldCourseNum < 0) {
            return false;
        }

        $difference = $newCourseAmount - $oldCourseAmount;    // 差价（新价格-旧价格）

        if ($difference >= 0.01) {
            $transferToCredit = $oldCourseAmount;
            $refundToParent = 0;
            $payBack = $difference;
        } else {
            $transferToCredit = $newCourseAmount;  // 转到个人账户的钱
            $refundToParent = 0-$difference;    // 退给家长的钱
            $payBack = 0;   // 需要补缴的钱
        }
        
        // 1，原账单退到个人账户
        // 2，退给家长钱操作
        // 3.1，不需要补缴差价（个人账户付款到新课账单）
        // 3.2，需要补缴差价（生成差价订单）

        // 开启事物
        $transaction = Yii::app()->subdb->beginTransaction();
        try {
            $linkId = 0;
            $exchangeStatus = AsaCourseExchange::SUBMIT;
            // ***************** 原账单钱退个人账户 ****************
            // 保存退费表
            $refundModel = new AsaRefund();
            $refundModel->site_id = $schoolid;
            $refundModel->childid = $childid;
            $refundModel->invoice_item_id = $invoiceItemModel->id;
            $refundModel->program_id = $oldCourseModel->gid;
            $refundModel->course_id = $oldCourseModel->id;
            $refundModel->refund_class_count = round($transferToCredit/$invoiceItemModel->unit_price);
            $refundModel->refund_total_amount = $transferToCredit;
            $refundModel->dropout_date = strtotime('today');
            $refundModel->memo = '退费到个人账户';
            $refundModel->refund_reason = 4;
            $refundModel->refund_type = 4;
            $refundModel->refund_method = 3;
            $refundModel->status = 5;
            $refundModel->updated = time();
            $refundModel->updated_by = $this->staff->uid;
            if (!$refundModel->save()) {
                throw new Exception('原账单钱退个人账户——保存退费表失败');
            }

            // 保存个人账户流水表
            $criteria = new CDbCriteria();
            $criteria->compare('childid', $childid);
            $criteria->order = 'id desc';
            $creditModel = AsaCredit::model()->find($criteria);
            $balance = $creditModel->balance;

            $creditModel = new AsaCredit();
            $creditModel->childid = $childid;
            $creditModel->schoolid = $schoolid;
            $creditModel->amount = $transferToCredit;
            $creditModel->balance = $balance + $transferToCredit;
            $creditModel->link_id = $refundModel->id;
            $creditModel->updated_userid = $this->staff->uid;
            $creditModel->updated_time = time();
            if (!$creditModel->save()) {
                throw new Exception('原账单钱退个人账户——保存个人账户流水表失败');
            }
            // ***************** 退款给家长 ****************
            if ($refundToParent > 0) {
                $payType = $invoiceItemModel->asainvoice->pay_type;
                $refundModel = new AsaRefund();
                $refundModel->site_id = $schoolid;
                $refundModel->childid = $childid;
                $refundModel->invoice_item_id = $invoiceItemModel->id;
                $refundModel->program_id = $oldCourseModel->gid;
                $refundModel->course_id = $oldCourseModel->id;
                $refundModel->refund_class_count = round($refundToParent/$invoiceItemModel->unit_price);
                $refundModel->refund_total_amount = $refundToParent;
                $refundModel->dropout_date = strtotime('today');
                $refundModel->memo = '换课费用退费';
                $refundModel->refund_reason = 5;
                $refundModel->refund_type = 5;
                $refundModel->refund_method = AsaRefund::refundType($payType);
                $refundModel->status = 2;
                if ($payType == AsaInvoice::WECHAT) {
                    $refundModel->status = 1;
                }
                $refundModel->updated = time();
                $refundModel->updated_by = $this->staff->uid;
                if (!$refundModel->save()) {
                    throw new Exception('退款给家长——保存退费表失败');
                }
                $linkId = $refundModel->id;
            }
            // ***************** 补缴差价 ****************
            // 判断是否需要补缴差价
            if ($payBack > 0) {
                // 生成差价订单
                $payBackInvoice = new AsaInvoice();
                $payBackInvoice->title = $invoiceTitle;
                $payBackInvoice->amount_original = $payBack;
                $payBackInvoice->amount_actual = 0;
                $payBackInvoice->status = AsaInvoice::STATS_UNPAID;
                $payBackInvoice->childid = $childid;
                $payBackInvoice->schoolid = $schoolid;
                $payBackInvoice->created_from = 4;  // 区分是否为补差价账单
                $payBackInvoice->updated = time();
                $payBackInvoice->updated_userid = $this->staff->uid;
                $payBackInvoice->classid = $childModel->classid;
                if (!$payBackInvoice->save()) {
                    throw new Exception('补缴差价——保存差价订单失败');
                }
                // 保存差价账单item表
                $payBackInvoiceItem = new AsaInvoiceItem();
                $payBackInvoiceItem->course_group_id = $newCourseModel->gid;
                $payBackInvoiceItem->course_id = $newCourseModel->id;
                $payBackInvoiceItem->order_id = $payBackInvoice->id;
                $payBackInvoiceItem->class_count = 1;
                $payBackInvoiceItem->unit_price = $payBack;
                $payBackInvoiceItem->actual_total = $payBack;
                $payBackInvoiceItem->status = AsaInvoice::STATS_UNPAID;
                $payBackInvoiceItem->schoolid = $schoolid;
                $payBackInvoiceItem->spot_hold = AsaInvoiceItem::SPOT_HOLD_FREE;;
                $payBackInvoiceItem->updated = time();
                $payBackInvoiceItem->updated_userid = $this->staff->uid;
                if (!$payBackInvoiceItem->save()) {
                    throw new Exception('补缴差价——保存差价订单关联表失败');
                }
                $linkId = $payBackInvoice->id;
            } else {
                // 个人账户付款给新课程账单
                $newInvoice = new AsaInvoice();
                $newInvoice->title = $childModel->getChildName() .' '. $newCourseModel->coursesgroup->getName();
                $newInvoice->amount_original = $newCourseAmount;
                $newInvoice->amount_actual = $newCourseAmount;
                $newInvoice->pay_type = AsaInvoice::CREDIT;
                $newInvoice->status = AsaInvoice::STATS_PAID;
                $newInvoice->childid = $childid;
                $newInvoice->schoolid = $schoolid;
                $newInvoice->created_from = 3;
                $newInvoice->updated = time();
                $newInvoice->updated_userid = $this->staff->uid;
                $newInvoice->classid = $childModel->classid;
                if (!$newInvoice->save()) {
                    throw new Exception('新课程账单付款失败');
                }
                // 保存新课程账单item表
                $newInvoiceItem = new AsaInvoiceItem();
                $newInvoiceItem->course_group_id = $newCourseModel->gid;
                $newInvoiceItem->course_id = $newCourseModel->id;
                $newInvoiceItem->order_id = $newInvoice->id;
                $newInvoiceItem->class_count = $newCourseNum;
                $newInvoiceItem->unit_price = $newCourseModel->unit_price;
                $newInvoiceItem->actual_total = $newCourseAmount;
                $newInvoiceItem->status = AsaInvoice::STATS_PAID;
                $newInvoiceItem->schoolid = $schoolid;
                $newInvoiceItem->spot_hold = 1;
                $newInvoiceItem->updated = time();
                $newInvoiceItem->updated_userid = $this->staff->uid;
                if (!$newInvoiceItem->save()) {
                    throw new Exception('新课程账单关联表失败');
                }
                // 操作个人账户
                $balance = $creditModel->balance;
                $creditModel = new AsaCredit();
                $creditModel->childid = $childid;
                $creditModel->schoolid = $schoolid;
                $creditModel->amount = '-' . $newCourseAmount;
                $creditModel->balance = $balance + $creditModel->amount;
                $creditModel->link_id = $newInvoice->id;
                $creditModel->updated_userid = $this->staff->uid;
                $creditModel->updated_time = time();
                if (!$creditModel->save()) {
                    throw new Exception('个人账户付款给新课程账单——保存个人账户流水表失败');
                }
                $exchangeStatus = AsaCourseExchange::SUCCESS;
                // 计算课程 MQ 
                Yii::import('common.components.AliYun.MQ.MQProducer');
                CommonUtils::addProducer(MQProducer::TAG_ASA, "Invoice.releaseHoldSpot", CJSON::encode(array($exchangeModel->new_cid, $exchangeModel->old_cid)), 0);
            }
            // 根据旧课程账单支付方式判断旧课程是否为否换课后的课程
            if ($invoiceItemModel->asainvoice->pay_type == 3) {
                // 查找旧课程换课信息
                $criteria = new CDbCriteria();
                $criteria->compare('schoolid', $schoolid);
                $criteria->compare('childid', $childid);
                $criteria->compare('new_cid', $oldCourseModel->id);
                $criteria->compare('status', AsaCourseExchange::SUCCESS);
                $criteria->order = 'ID desc';
                $oldExchange = AsaCourseExchange::model()->find($criteria);
                if ($oldExchange && $oldExchange->first_invoice) {
                    $pay_invoice = $oldExchange->pay_invoice ? json_decode($oldExchange->pay_invoice) : array();
                    $refund_invoice = $oldExchange->refund_invoice ? json_decode($oldExchange->refund_invoice) : array();
                    if ($difference > 0) {
                        $pay_invoice[] = $linkId;
                    } elseif ($difference < 0) {
                        $refund_invoice[] = $linkId;
                    }

                    $exchangeModel->first_invoice = $oldExchange->first_invoice;
                    $exchangeModel->pay_invoice = json_encode($pay_invoice);
                    $exchangeModel->refund_invoice = json_encode($refund_invoice);
                }
            } else {
                $exchangeModel->first_invoice = $invoiceItemModel->id;
                if ($difference > 0) {
                    $exchangeModel->pay_invoice = json_encode(array($linkId));
                } elseif ($difference < 0) {
                    $exchangeModel->refund_invoice = json_encode(array($linkId));
                }
            }
            // ***************** 保存换课表 ****************
            $exchangeModel->difference = $difference;
            $exchangeModel->status = $exchangeStatus;
            $exchangeModel->link_id = $linkId;
            if (!$exchangeModel->save()) {
                throw new Exception('保存换课表——保存换课表失败');
            }

            $transaction->commit();
            return true;
        } catch (Exception $e) {
            $transaction->rollBack();
            $this->addMessage('message', $e->getMessage());
            return false;
        }
    }
}