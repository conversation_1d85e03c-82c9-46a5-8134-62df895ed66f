<?php


class BranchSelector extends CWidget
{
	const GROUP_NONE = 0;
	const GROUP_BY_CITY = 10;
	const GROUP_BY_PROGRAM = 20;
	
	public $hideOffice = true;
	public $accessBranches = array();
	public $adminBranches = array();
	public $allBranches = array();
	public $showList = null;
	public $selectedBid = '';
	public $selectLabel = '';
	public $urlArray=array('/settings/demo', 'test1'=>'test');
	public $extraUrlArray=null;
	public $keyId = 'branchId';
	
	public $grouped = GROUP_BY_PROGRAM;
	
	public function run()
	{
		$this->selectLabel = ($this->selectLabel == "") ? Yii::t("global", "Please Select") : $this->selectLabel;
		if(empty($this->accessBranches)) $this->accessBranches = $this->getController()->accessBranch;
		if(empty($this->adminBranches))  $this->adminBranches  = $this->getController()->adminBranch;
		if(empty($this->allBranches))  $this->allBranches  = $this->getController()->getAllBranch();
		if(empty($this->selectedBid)) $this->selectedBid = $this->getController()->branchId;
		if(empty($this->selectedBid) && is_null($this->showList)) $this->showList = true;
		
		if(empty($this->accessBranches) && empty($this->adminBranches))
			$singleBranch = true;
		
		$this->render('branch', array('singleBranch'=>$singleBranch));
	}
}

