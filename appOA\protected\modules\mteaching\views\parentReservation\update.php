<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
    </button>
    <h4 class="modal-title" id="exampleModalLabel"><?php echo $model->id ? Yii::t('global','Update'): Yii::t('global','Add'); ?>：</h4>
</div>
<?php $form = $this->beginWidget('CActiveForm', array(
    'id' => 'course-form',
    'enableAjaxValidation' => false,
    'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form'),
)); ?>
<div class="modal-body">
    <!-- 适用学年 -->
    <div class="form-group">
        <label class="col-sm-2 control-label required"><?php echo Yii::t('payment', 'Annual') ?> * </label>
        <div class="col-xs-10">
            <?php echo $form->dropDownList($model, 'starYear', $starYear, array('class' => 'form-control', 'empty' => Yii::t('global','Please Select'))); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo Yii::t('event', 'Title Cn') ?> * </label>

        <div class="col-xs-10">
            <?php echo $form->textField($model, 'title_cn', array('maxlength' => 255, 'class' => 'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo Yii::t('event', 'Title En') ?> * </label>

        <div class="col-xs-10">
            <?php echo $form->textField($model, 'title_en', array('maxlength' => 255, 'class' => 'form-control')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo Yii::t('event', '开始时间') ?> * </label>

        <div class="col-xs-10">
            <?php echo $form->textField($model, 'start_date', array('maxlength' => 255, 'class' => 'form-control date')); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo Yii::t('event', '结束时间') ?> * </label>

        <div class="col-xs-10">
            <?php echo $form->textField($model, 'end_date', array('maxlength' => 255, 'class' => 'form-control date')); ?>
        </div>
    </div>
</div>
<div class="modal-footer">
    <span id="start_fail_info" class="pull-left text-warning" style="display: none;"><i
                class="glyphicon glyphicon-remove text-warning"></i><?php echo Yii::t('asa', '当天该时间段已有课程') ?></span>
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
    <button type="button" class="btn btn-default"
            data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
</div>
<?php $this->endWidget(); ?>

<script>
    $('.date').datepicker({
        changeMonth: true,
        changeYear: true,
        'dateFormat': 'yy-mm-dd'
    });

</script>
