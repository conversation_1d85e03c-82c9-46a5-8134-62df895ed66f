<?php
return array (
  'Purpose of Referral' => '提请的目的',
  'Location' => '事件发生地点',
  'Core value that was not met or violated' => '与哪些核心价值观相违背',
  'Problem Behavior' => '问题行为',
  'What previous steps have you taken to address this behavior' => '请说明你此前已经采取过哪些措施进行干预',
  'Possible Motivation' => '可能的动机',
  'Follow up actions' => '后续处理',
  'In school suspension' => '在校停课',
  'Individual Instruction' => '个别指导',
  'Community Service' => '社区服务',
  'Loss of priviledge' => '丧失权利',
  'Time in office' => '去办公室',
  'Conference w/ student' => '和学生开会',
  'Parent contact' => '联系家长',
  'Out of school suspension' => '离校停课',
  'Learning Support' => '学习支持计划',
  'Behavior Support' => '行为支持计划',
  'I am the explanatory text for this label' => '我是该标签的说明文本。',
  'Need to involve Support Group only this time' => '仅本次需要支持团队协助',
  'Teacher can directly request assistance from ISP to this student in future.' => '以后该生的行为问题，可由老师直接提交给支持团队协助',
  'Transfer to Support Group' => '转给ISP处理',
  'Assign to' =>'分配给',
  'Elementary School' => '小学部',
  'Secondary School' => '中学部',
  'Comment' => '处理意见',
  'Transfer to Head of Grade' => '转给年级组长处理',
  'Support Groups' => '支持计划',
  'Head of Grade' => '年级组长',
  'Head of Department' => '学科组长',
  'Transfer to Head of Department' => '转给学科组长处理',
  "Head of Grade group is not enabled in this division" => '该学部没有启动年级组长管理组',
  "Head of Department group is not enabled in this division" => '该学部没有启动学科组长管理组',
  'Support Type' => '选择协助类型',
  'Upload' => '上传',
  'Submitter' => '提请人',
  'Time' => '提交时间',
  'Status' => '处理状态',
  'My Tasks' => '需要我处理',
  'In progress'=>'处理中',
  'Frequency Attention'=>'高频预警',
  'More' => '更多',
  'New Entry' => '新建提请单',
  'Input name' => '请输入姓名',
  'All Entries' => '全部提请单',
  'Today' => '今日',
  'Week' => '本周',
  'Month' => '本月',
  'Year' => '本学年',
  'In Process' => '等待处理',
  'Record only, no followup needed' => '只记录，无需后续处理',
  'Notify class teachers' => '同步通知班级教师',
  'Event Description' => '事件简要经过',
  'Student' => '选择学生',
  'Suspention Schedule' => '停课时间',
  'Logs' => '认定记录',
  'Result' => '认定结果',
  'Interview duration' => '面谈时长',
  'Time in Office' => '面谈时长',
  'Followup Log' => '跟进记录',
  'Completed' => '已完成',
  'Description' => '事件信息',
  'Lead of Grade' => '年级组长',
  'Principal Office' => '校长办公室',
  'Support Group' => '支持小组',
  'Final Decision' => '最终认定',
  'Transfer to Principal Office' => '转给校长办公室',
  'It is not a behavior issue' => '非行为问题认定',
  'Process' => '去处理',
  'Complete Final Decision' => '进行最终认定',
  'Assign to Principal Office' => '通知校长办公室其他人',
  'Followup' => '后续',
  'Support Type' => '协助类型',
  'Support Group' => '支持小组',
  'Transfer to Support Groups' => '转给支持小组',
  'Close this entry' => '标记为完成',
  'Closed' => '处理完成',
  'Decision' => '认定',
  'Final Result' => '最终结果',
  'Contact parents' => '联系家长',
  'By' => '认定人',
  'Time' => '认定时间',
  'Reset' => '重新认定',
  'Notify To' => '通知老师',
  'Send Notify' => '发通知',
  'All notification logs' => '查看通知记录',
  'Behavior Submission Entries' => '行为问题记录',
  'NOT a behavior issue' => '非行为问题',
  'A behavior issue' => '行为问题',
  'Wechat scan for detail' => '扫码查看提请单详情',
  'Different result should be processed one by one.' => '若学生的处理结果不同，需要对学生单个认定',
  'Notification of a behaviour handling' => '行为问题处理通知' ,
  'Detail' => '详情',
  'Behavior Management' => '学生行为管理',
  'Record only' => '只记录',
  'Discard unsaved data?' =>'关闭弹窗将不能保存已填写的数据，是否确认关闭？',
  'Waiting' => '待处理',
  'Total ' => '共',
  'Students from different divisions should be submitted in different entries.' =>'不同学部的学生请分开提交',
  'Processed' => '已认定',
  'Add other student' =>'认定其他学生',
  'Collaborators' => '协作者',
  'Select a staff' =>'通知对象',
  ' ' => '人',
  'Share To' => '共享给',
  'Staff whom shared to will be added as collaborator' => '被共享人将被增加为协作者',
  'Share Logs' => '共享记录',
  'Hour' => '小时',
  'Notification will be sent to all collaborators and members of next node, proceed?' => '提交后会给协作者和待处理团队发送通知，确定提交吗？',
  'Notification will be sent to all collaborators, proceed?' => '提交后会给协作者发送通知，确定提交吗？',
  'Waiting for ' => '等待',
  ' to handle' => '处理',
  'Behavioral' => '行为相关',
  'Academic' => '学术相关',
  'Positive Behavior' => '学术相关',
  'Type' => '类型',
  "'Hour'" => '小时',
  "'Minutes'" => '分钟',
  'Type of this entry will be converted to Academic when transferred to Lead of Subjects' => '转给学科组长后，提请单类型将变为“学术相关”',
  'Mark as completed'=>'标记为处理完成',
  'Complete'=>'完成处理',
  'Handling Completed'=>'已处理完成',
  'To Complete'=>'待完成',
  'Result already issued' => '已含处理结果',
  'Submit on Wechat' =>'也可在微信端提交',
  'Wechat scan'=>'微信扫一扫',
  'Get handling progress notifications.' => '接收行为管理处理通知',
  'Select Student'=>'选择学生',
  'Select Class'=>'选择班级',
  'Filter by this student'=>'只查看本学生的记录  （点击头像和名字）',
  'Filter by this class'=>'只查看本班级的记录 （点击班级）',
  'Notification will be sending to:' => '通知将会发送给：',
  "Click on a student's photo or class to add a filter"=>'点击学生头像或班级，可快速添加过滤器',
  'Double-click grid to edit the score' => '双击单元格可编辑分数',
  'Filter by problem behavior' => '按问题行为过滤',
  'All Problem Behavior' => '全部问题行为',
  'Negative Behavior' => '不好的行为',
  'Attachments Error' => '附件数据异常',
  'Roaster Generator' => '考试花名册',
  'Settings' => '设置',
  'Generate Roaster' => '生成考试名单',
  'Roaster' => '考试花名册',
  'Export' => '导出',
  'Generated at %s' => '于 %s 生成',
  'Teacher Setting' => '设置老师',
  'Teacher not set' => '未设置老师',
  'Special Tracking' => '专项记录',
  'This entry will inform SMT when checkbox below is checked' => '勾选本项，本条信息将提交至校长办公室',
  'Semester' => '本学期',
);
