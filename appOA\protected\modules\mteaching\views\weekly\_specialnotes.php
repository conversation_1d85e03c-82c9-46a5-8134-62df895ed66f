<div class="col-md-9">
    <div class="panel panel-default">
        <div class="panel-heading"><?php echo Yii::t("teaching", "Special Notes");?></div>
        <div class="panel-body">
			<div id="info-guide-box"><?php echo Yii::t('teaching','Click child list right side to start');?></div>
            <div id="specialnote" style="display: none;">
                <?php echo CHtml::form('', 'post', array('class'=>'form-horizontal J_ajaxForm', 'role'=>'form'));?>
                <div class="form-group">
                    <label class="col-sm-2 control-label" id="name-child"></label>
                    <div class="col-sm-10">
                        <div id="photo-child" style="max-width: 200px;"></div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label"><?php echo Yii::t('teaching','Photos');?></label>
                    <div class="col-sm-10">
                        <div class="mb10">
                            <?php $this->widget('ext.selectMedia.SelectMedia', array(
                                'weeknum'=>$this->selectedWeeknum,
                                'classid'=>$this->selectedClassId,
                                'branchid'=>$this->branchId,
                                'startyear'=>$this->calendarModel->startyear,
                                'yid'=>$this->calendarId,
                                'children'=>$taskData['children'],
                                'multiple'=>true,
                                'callback'=>'callback',
                                'buttonLabel' => Yii::t('global','Add'),
                            ));
                            ?>
                            <div class="btn-group">
                                <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
                                    <?php echo Yii::t('teaching', 'Add monthly tagged photos'); ?>
                                        <span class="caret"></span>
                                </button>
                                <ul class="dropdown-menu" role="menu">
                                    <?php
                                    $months = Yii::app()->getLocale()->monthNames;
                                    foreach($taskData['months'] as $mon):
                                    ?>
                                    <li>
                                        <a href="javascript:addMonth(<?php echo intval($mon)?>);">
                                            <?php echo $months[$mon];?>
                                        </a>
                                    </li>
                                    <?php endforeach;?>
                                </ul>
                            </div>
                        </div>
                        <div id="items-child"></div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label"><?php echo Yii::t('teaching','Make Online');?></label>
                    <div class="col-sm-10">
                        <div class="checkbox">
                            <label>
                                <?php echo CHtml::activeCheckBox($taskData['model'], 'stat', array('value'=>20));?>
                                &nbsp;
                            </label>
                        </div>
                        <?php echo CHtml::activeHiddenField($taskData['model'], 'childid');?>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2"></label>
                    <div class="col-sm-10">
                        <button class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Save');?></button>
                    </div>
                </div>
                <?php echo CHtml::endForm();?>
            </div>
        </div>
    </div>
</div>

<div class="col-md-3">
    <div class="panel panel-default">
        <div class="panel-heading">
            <?php echo Yii::t('navs','Child List');?>
            <div class="pull-right dropdown">
                <button class="btn btn-default btn-xs" data-toggle="dropdown"><?php echo Yii::t('teaching',
                        'Batch');?> <span class="caret"></span></button>
                <ul class="dropdown-menu">
                    <li><a href="javascript:;" onclick="setAll('online');"><?php echo Yii::t('teaching','Make All Online');?></a></li>
                    <li><a href="javascript:;" onclick="setAll('offline');"><?php echo Yii::t('teaching','Make All Offline');?></a></li>
                </ul>
            </div>
        </div>
        <div class="panel-body">
            <ul class="media-list" id="weekly-media-child-list"></ul>
        </div>
    </div>
</div>

<style>
    .img-item{background: #f2f2f2;border: 1px solid #d5d5d5}
    .child-face{max-width: 40px;}
    #weekly-media-child-list .media{padding-left: 22px; position: relative}
    #weekly-media-child-list h5.flag{position: absolute;top: 2px; left: 0px; font-size:16px;color: #f2f2f2}
    #weekly-media-child-list .online h5.flag{color: #008000}
    .customWH{width: 460px;height: 85px !important;}
</style>

<script type="text/template" id="child-li-item-template">
    <li class="media class-child" childid=<%- id %>>
        <a class="pull-left" href="javascript:specialNotes(<%= id%>);">
            <img class="media-object child-face img-thumbnail" src="<?php echo Yii::app()->params['OAUploadBaseUrl']?>/childmgt/<%= photo %>" title="<%- name %>">
        </a>
        <div class="media-body">
            <a href="javascript:specialNotes(<%= id%>);"><h5 class="media-heading"><%- name %></h5></a>
        </div>
        <h5 class="flag"><span class="glyphicon glyphicon-ok-sign"></span></h5>
    </li>
</script>

<script type="text/template" id="child-photo-item">
    <div id="item<%= id%>" data-isnew="<%= isNew%>">
        <input name="pid[]" value="<%= id%>" type="hidden">
        <div class="pull-left mr10">
            <div><img class="img-thumbnail" src="<%= url%>"></div>
            <div class="text-center p10"><span class="glyphicon glyphicon-remove" title="<?php echo Yii::t('global','Delete');?>" style="cursor: pointer;" onclick="delItem(<%= id%>);"></span></div>
        </div>
        <div class="pull-left">
            <textarea class="form-control customWH mb10" name="caption[]" placeholder="<?php echo Yii::t('labels','Description');?>"><%= val%></textarea>
            <input type="text" name="weight[]" class="form-control length_2 pull-left mr10" placeholder="<?php echo Yii::t('global','Sort Number');?>" value="<%= weight%>">
            <p class="form-control-static"><?php echo Yii::t('teaching','sort order, photos with small number displays first.');?></p>
        </div>
        <div class="clearfix"></div>
        <hr>
    </div>
</script>

<script>
    var onlines = <?php echo CJSON::encode($taskData['onlines']); ?>;
    var childDataRaw = <?php echo CJSON::encode($taskData['children']); ?>;
    var childData = _.sortBy(childDataRaw, 'name');
    var childItemTemplate = _.template( $('#child-li-item-template').html() );
    $('#weekly-media-child-list').html('');

    function renderChildList()
    {
        $('#weekly-media-child-list').html('');
        _.each(childData, function(_data){

            var _item = childItemTemplate(_data);
            var _child = _.first($(_item));
            if(_.indexOf(onlines, _data.id) != -1) $(_child).addClass('online');

            $('#weekly-media-child-list').append( $(_child) );
        });
    }
    renderChildList();

    function specialNotes(id)
    {
        if(id){
            $('#info-guide-box').html('<?php echo Yii::t("global", "Loading Data...");?>').show();
            $('#specialnote').hide();
            $('#NotesSpecial_childid').val(id);
            $('#photo-child').html('<img src="<?php echo Yii::app()->params['OAUploadBaseUrl']?>/childmgt/'+childDataRaw[id].photo+'" class="img-thumbnail">');
            $('#name-child').html( childDataRaw[id].name );
            $.getJSON('<?php echo $this->createUrl('specialNotes');?>', {id: id, classid: classid, weeknum: weeknum}, function(data){
                $('#items-child').html('');
                if(data.photoData.length>0){
                    for(var i=0; i<data.photoData.length; i++){
                        var item = _.template($('#child-photo-item').html(), data.photoData[i]);
                        $('#items-child').append(item);
                    }
                }

                if(data.stat == 20){
                    $('#NotesSpecial_stat').attr('checked', true);
                }
                else{
                    $('#NotesSpecial_stat').attr('checked', false);
                }
                $('#info-guide-box').hide();
                $('#specialnote').show();
            });
        }
    }

    function callback(data)
    {
        var pids = [];
        $('#items-child input[name="pid[]"]').each(function(){
            pids.push( $(this).val() );
        });
        $.each(data, function(index, value){
            if(_.indexOf(pids, index) == -1){
                var item = _.template($('#child-photo-item').html(), {id: index, url: value, val: '', weight: '', isNew: 1});
                $('#items-child').append(item);
            }
        });
    }
	
	function callback1(data)
    {
        $('#items-child > div').each(function(){
            $(this).data('isnew', 0);
        });
    }

    function delItem(id)
    {
        if(confirm('<?php echo Yii::t("message","Sure to delete?");?>')){
            var obj=$('#item'+id);
            if(obj.data('isnew') == 1){
                obj.remove();
            }
            else{
                $.post('<?php echo $this->createUrl('delPhotoLink', array('category'=>'snotes'))?>', {pid: id, classid: classid, weeknum: weeknum}, function(){
                    obj.remove();
                });
            }
        }
    }

    function addMonth(month){
        var childid = $('#NotesSpecial_childid').val();
        $.getJSON('<?php echo $this->createUrl('monthPhoto');?>', {month: month, classid: classid, childid: childid}, function(data){
            if(data.state == 'success'){
                callback(data.data);
            }
            else{
                resultTip({error: 1, msg: data.message});
            }
        });
    }

    function setAll(type)
    {
        $.post(
            '<?php echo $this->createUrl('setAll', array('classid'=>$this->selectedClassId, 'weeknum'=>$this->selectedWeeknum, 'flag'=>'special'))?>',
            {type: type},
            function(data){
                if(data.state == 'success'){
                    resultTip({msg: data.message});
                    onlines = data.data;
                    renderChildList();
                }
                else{
                    resultTip({msg: data.message, error: 1});
                }
            },
            'json'
        );
    }
</script>