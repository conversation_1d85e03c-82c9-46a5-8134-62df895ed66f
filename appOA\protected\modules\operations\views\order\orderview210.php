<div class="h_a"><?php echo $v;?></div>
<div class="prompt_text">
	<ul>
		<li><?php echo $v;?></li>
	</ul>
</div>
<?php
$this->widget('ext.ivyCGridView.IvyCGridView', array(
	'id'=>'points-product-grid',
	'dataProvider'=>$sData,
//	'filter'=>$model,
    'colgroups'=>array(
        array(
            "colwidth"=>array(50,300,150,150,150,150),
//            "htmlOptions"=>array("align"=>"left", "span"=>2)
        )
    ),
	'columns'=>array(
        'quantity'=>array(
            'name'=>'数量',
            'value'=>'$data->quantity',
        ),
		'productid'=>array(
            'name'=>'商品',
            'value'=>'$data->Product->getContent()',
        ),
		'childid'=>array(
            'name'=>'孩子',
            'value'=>'$data->ChildProfile->getChildName()',
        ),
		'schoolid'=>array(
            'name'=>'学校',
            'value'=>'$data->school->title',
        ),
		'created_timestamp'=>array(
            'name'=>'创建时间',
            'value'=>'OA::formatDateTime($data->created_timestamp, "medium", "short")',
        ),
		'created_userid'=>array(
            'name'=>'创建人',
            'value'=>'$data->orderUser->getName()',
        ),
	),
)); ?>

