<?php

class DefaultController extends BranchBasedController
{
    public function createUrl($route, $params = array(), $ampersand = '&')
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function init(){
        parent::init();
        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'main';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('user','总部管理区域');

        //初始化选择校园页面
        $this->branchSelectParams['hideOffice'] = false;
        $this->branchSelectParams['urlArray'] = array('//mhr/default/index',"category" => "base");
    }

    public function actionSelect(){
        $this->render('//layouts/common/branchSelect');
    }

	public function actionIndex()
	{
        $category = Yii::app()->request->getParam('category','leaveBase');
        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/underscore-min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/backbone-min.js');
        $cs->registerCssFile($cs->getCoreScriptUrl().'/jui/css/base/jquery-ui.css');
		$this->render('index',array(
              'category'=>$category,
        ));
	}

    //保存员工假期基数
    public function actionSaveAsignHoliday(){

        if (Yii::app()->request->isAjaxRequest && isset($_POST['UserLeave'])){
            $startYear = $_POST['startYear'];
            $dates = $this->formatConfigDate($startYear);
            foreach ($_POST['UserLeave']['staff_uid'] as $k=>$val){
                $hours = isset($_POST['UserLeave']['hours'][$k]) ? UserLeave::setLeaveFormat($_POST['UserLeave']['hours'][$k], 0) : 0;
                $model = UserLeave::model()->find('staff_uid=:staff_uid and type=:type and flag=:flag and startyear=:startyear',array(':staff_uid'=>$val,'type'=>UserLeave::TYPE_ANNUAL_LEAVE,'flag'=>0,'startyear'=>$startYear));
                if (empty($model)){
                    $model = new UserLeave();
                }else{
                    if ($model->hours == $hours){
                        $this->addMessage('state','success');
                        $this->addMessage('message', Yii::t('mhr', '保存成功.'));
                        continue;
                    }
                }
                if ($hours == 0){
                    $this->addMessage('state','success');
                    $this->addMessage('message', Yii::t('mhr', '保存成功.'));
                    continue;
                }else{
                    $model->type = UserLeave::TYPE_ANNUAL_LEAVE;
                    $model->staff_uid = $val;
                    $model->branchid = $this->branchId;
                    $model->hours = $hours;
                    $model->startdate = strtotime($dates['startDate']);
                    $model->enddate = strtotime($dates['endDate']);
                    $model->startyear = $startYear;
                    $model->balance = $hours;
                    $model->flag = 0;
                    $model->created = time();
                    $model->creator = Yii::app()->user->getId();
                    if ($model->save()){
                        $userIds[$model->staff_uid] = $_POST['UserLeave']['hours'][$k];
                        $this->addMessage('data',$userIds);
                        $this->addMessage('state','success');
                        $this->addMessage('callback','succCallback');
                        $this->addMessage('message', Yii::t('mhr', '保存成功.'));
                    }else{
                        $this->addMessage('state','fail');
                        $this->addMessage('message', Yii::t('mhr', '保存失败.'));
                    }
                }
            }
        }else{
            $this->addMessage('state','fail');
            $this->addMessage('message', Yii::t('mhr', '请填写年假基数.'));
        }
        $this->showMessage();
    }

    //格式化配置日期
    public function formatConfigDate($startYear){
        $dataList = $this->readConfigDate();
        $dates = array();
        if (is_array($dataList) && count($dataList)){
            $dataList = explode(';', $dataList['data']);
            $dates['startDate'] =  sprintf($dataList[0],$startYear);
            $dates['endDate'] =  sprintf($dataList[1],$startYear+1);
            unset($dataList);
        }
        return $dates;
    }

    //读取学校的配置
    public function readConfigDate(){
        $command = Yii::app()->db->createCommand();
        $command->from(BranchVar::model()->tableName());
        $command->where('category=:category and flag=:flag and branchid=:branchid',array(':category'=>'holidaycfg',':flag'=>1,':branchid'=>$this->branchId));
        $dataList = $command->queryRow();
        return $dataList;
    }

    //保存假期延休
    public function actionSaveExtensionHistory(){
        if (Yii::app()->request->isAjaxRequest && isset($_POST['AsLeaveExtensionHistory'])){
            foreach ($_POST['AsLeaveExtensionHistory']['leave_id'] as $val){
                $userModel = UserLeave::model()->findByPk($val);
                $model = new AsLeaveExtensionHistory();
                $extensionDate = isset($_POST['AsLeaveExtensionHistory']['extension_date'][$val]) ? strtotime($_POST['AsLeaveExtensionHistory']['extension_date'][$val]) : 0;
                $extensionDesc = isset($_POST['AsLeaveExtensionHistory']['desc'][$val]) ? $_POST['AsLeaveExtensionHistory']['desc'][$val] : '';
                $staffId = isset($_POST['AsLeaveExtensionHistory']['staff_uid'][$val]) ? $_POST['AsLeaveExtensionHistory']['staff_uid'][$val] : 0;
                if ($extensionDate === 0){
                    $this->addMessage('state','success');
                    $this->addMessage('message', Yii::t('mhr', '保存成功.'));
                    continue;
                }else{
                    $count = LeaveItem::model()->getUsedLeaveList($userModel->staff_uid,$val,false,array(LeaveItem::STATUS_WAITING));
                    $count = count($count) ? $count[$val]['sum'] : 0;
                    if (!$count && ($userModel->staff_uid == $staffId)){
                        $model->leave_id = $val;
                        $model->extension_date = $extensionDate;
                        $model->desc = $extensionDesc;
                        $model->creator = Yii::app()->user->getId();
                        $model->created = time();
                        if ($model->save()){
                            UserLeave::model()->updateByPk($val, array('enddate'=>$extensionDate));
                            $data[$val] = array('date'=>OA::formatDateTime($extensionDate),'desc'=>$extensionDesc);
                            $this->addMessage('data',$data);
                            $this->addMessage('state','success');
                            $this->addMessage('callback','succCallback');
                            $this->addMessage('message', Yii::t('mhr', '保存成功.'));
                        }else{
                            $this->addMessage('state','fail');
                            $this->addMessage('message', Yii::t('mhr', '保存失败.'));
                        }
                    }
                }
            }
        }
        $this->showMessage();
    }

    //员工折现保存
    public function actionSaveStaffDiscounted(){
        if (Yii::app()->request->isAjaxRequest && isset($_POST['LeaveItem'])){
            foreach ($_POST['LeaveItem']['leave_id'] as $val){
                $model = UserLeave::model()->findByPk($val);
                if (!empty($model)){
                    $staffId = isset($_POST['LeaveItem']['staff_uid'][$val]) ? $_POST['LeaveItem']['staff_uid'][$val] : 0;
                    $desc = isset($_POST['LeaveItem']['desc'][$val]) ? $_POST['LeaveItem']['desc'][$val] : '';
                    $apped = LeaveItem::model()->getUsedLeaveList($model->staff_uid,$val,false);
                    $balance = $model->hours - (count($apped) ? $apped[$val]['sum'] : 0);
                    if (($staffId == $model->staff_uid) && $model->balance && ($model->balance == $balance)){
                        $itmeModel = new LeaveItem();
                        $itmeModel->staff_uid = $staffId;
                        $itmeModel->category = UserLeave::USER_TYPE_LEAVE;
                        $itmeModel->branchid = $model->branchid;
                        $itmeModel->classid = 0;
                        $itmeModel->leave_id = $val;
                        $itmeModel->type = UserLeave::TYPE_DISCOUNTED;
                        $itmeModel->startdate = 0;
                        $itmeModel->enddate = 0;
                        $itmeModel->month = 0;
                        $itmeModel->hours = $model->balance;
                        $itmeModel->applydate = time();
                        $itmeModel->status = LeaveItem::STATUS_AGREE;
                        $itmeModel->approvedate = time();
                        $itmeModel->approver = Yii::app()->user->getId();
                        $itmeModel->check_desc = $desc;
                        $itmeModel->creator = Yii::app()->user->getId();
                        $itmeModel->created = time();
                        if ($itmeModel->save()){
                            $model->balance = 0;
                            $model->save();
                            $data[$val] = $val;
                            $this->addMessage('data',$data);
                            $this->addMessage('state','success');
                            $this->addMessage('callback','succCallback');
                            $this->addMessage('message', Yii::t('mhr', '保存成功.'));
                        }else{
                            $this->addMessage('state','fail');
                            $this->addMessage('message', Yii::t('mhr', '保存失败.'));
                        }
                    }
                }
            }
        }
        $this->showMessage();
    }
}