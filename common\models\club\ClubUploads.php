<?php

/**
 * This is the model class for table "ivy_club_uploads".
 *
 * The followings are the available columns in table 'ivy_club_uploads':
 * @property integer $id
 * @property integer $article_id
 * @property integer $mime_type
 * @property string $filename
 * @property integer $server_id
 * @property string $sizes
 * @property string $subfolder
 * @property string $original
 */
class ClubUploads extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @return ClubUploads the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_club_uploads';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('mime_type', 'required'),
			array('article_id, server_id', 'numerical', 'integerOnly'=>true),
			array('filename, subfolder, original', 'length', 'max'=>255),
			array('sizes', 'length', 'max'=>64),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, article_id, mime_type, filename, server_id, sizes, subfolder, original', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'article_id' => 'Article',
			'mime_type' => 'Mime Type',
			'filename' => 'Filename',
			'server_id' => 'Server',
			'sizes' => 'Sizes',
			'subfolder' => 'Subfolder',
			'original' => 'Original',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('article_id',$this->article_id);
		$criteria->compare('mime_type',$this->mime_type);
		$criteria->compare('filename',$this->filename,true);
		$criteria->compare('server_id',$this->server_id);
		$criteria->compare('sizes',$this->sizes,true);
		$criteria->compare('subfolder',$this->subfolder,true);
		$criteria->compare('original',$this->original,true);

		return new CActiveDataProvider(get_class($this), array(
			'criteria'=>$criteria,
		));
	}
}