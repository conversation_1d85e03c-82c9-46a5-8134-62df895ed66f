<?php

class ParentSurveyController extends BranchBasedController
{
    public $actionAccessAuths = array();

    public function init()
    {
        parent::init();

        Yii::app()->theme = 'blue';
        $this->layout = "//layouts/column1";

        $this->modernMenuFlag = 'campusOp';
        $this->modernMenu = OA::genUserMenu($this->modernMenuFlag);
        $this->modernMenuCategoryTitle = Yii::t('site', 'Campus Workspace');
        $schoolList = CommonUtils::dsSchoolList(array('BJ_IASLT'));
        foreach ($this->accessBranch as $key => $item) {
            if (!in_array($item, $schoolList)) {
                unset($this->accessBranch[$key]);
            }
        }
        //初始化选择校园页面
        $this->branchSelectParams['urlArray'] = array('//mcampus/parentSurvey/index',);
        //初始化选择校园页面
        // $this->multipleBranch = $this->accessMultipleBranches();

        $cs = Yii::app()->clientScript;
        $cs->registerCoreScript('jquery.ui');
        $cs->registerCssFile($cs->getCoreScriptUrl() . '/jui/css/base/jquery-ui.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/vue.global.js');
        $cs->registerCssFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.css');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/element/index.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl.'/base/js/echarts/echarts.min.js');
        $cs->registerScriptFile(Yii::app()->themeManager->baseUrl . '/base/js/xlsx.full.min.js');

    }

    public function createUrl($route, $params = array(), $ampersand = '&', $parentOnly = false)
    {
        if (empty($params['branchId'])) {
            $params['branchId'] = $this->branchId;
        }
        return parent::createUrl($route, $params, $ampersand);
    }

    public function actionIndex()
    {
        $show = false;
        if (in_array(Yii::app()->user->id, array(2, 5, 8001584))) {
            $show = true;
        }

        $this->render('index', array('show' => $show));
    }

    public function actionApi()
    {
        $url = Yii::app()->request->getParam('url');
        $apiList = $this->getApiList();
        if (!isset($apiList[$url])) {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', '请求地址错误');
            $this->showMessage();
        }
        $remoteUrl = $apiList[$url];
        if ($_GET) {
            $getPath = '';
            foreach ($_GET as $key => $value) {
                if ($getPath == '') {
                    $getPath .= "$key=$value";
                } else {
                    $getPath .= "&$key=$value";
                }
            }
            $remoteUrl = $remoteUrl . '?' . $getPath;
        }
        $requestData = $_POST;
        $this->remote($remoteUrl, $requestData);
    }

    public function getApiList()
    {
        return array(
            'config' => 'parentSurvey/config',
            'overview' => 'parentSurvey/teacherOverview',
            'detail' => 'parentSurvey/teacherDetail',
            'export' => 'parentSurvey/teacherExport',
        );
    }

    public function checkRole()
    {
        $roleList = array('ivystaff_it', 'ivystaff_hr');
        foreach ($roleList as $role) {
            if (Yii::app()->user->checkAccess($role)) {
                return true;
                break;
            }
        }
        return false;
    }


    public function remote($requestUrl, $requestData = array())
    {
        if (empty($requestData['schoolId'])) {
            $requestData['schoolId'] = $this->branchId;
        }
        $res = CommonUtils::requestDsOnline($requestUrl, $requestData);
        if ($res['code'] == 0) {
            $this->addMessage('state', 'success');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        } else {
            $this->addMessage('state', 'fail');
            $this->addMessage('message', $res['msg']);
            $this->addMessage('data', $res['data']);
        }
        $this->showMessage();
    }
}
