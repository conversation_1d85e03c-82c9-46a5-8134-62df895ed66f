/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.1.6 (2020-01-28)
 */
!function(f){"use strict";function o(e){return e}var R=function(e){function n(){return t}var t=e;return{get:n,set:function(e){t=e},clone:function(){return R(n())}}},T=function(){},O=function(t,r){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return t(r.apply(null,e))}},D=function(e){return function(){return e}};function b(r){for(var o=[],e=1;e<arguments.length;e++)o[e-1]=arguments[e];return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];var t=o.concat(e);return r.apply(null,t)}}function d(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return!t.apply(null,e)}}function e(){return u}var n,s=D(!1),i=D(!0),u=(n={fold:function(e,n){return e()},is:s,isSome:s,isNone:i,getOr:c,getOrThunk:r,getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},getOrNull:D(null),getOrUndefined:D(undefined),or:c,orThunk:r,map:e,each:T,bind:e,exists:s,forall:i,filter:e,equals:t,equals_:t,toArray:function(){return[]},toString:D("none()")},Object.freeze&&Object.freeze(n),n);function t(e){return e.isNone()}function r(e){return e()}function c(e){return e}function a(n){return function(e){return function(e){if(null===e)return"null";var n=typeof e;return"object"==n&&(Array.prototype.isPrototypeOf(e)||e.constructor&&"Array"===e.constructor.name)?"array":"object"==n&&(String.prototype.isPrototypeOf(e)||e.constructor&&"String"===e.constructor.name)?"string":n}(e)===n}}function l(e,n){return-1<function(e,n){return Ue.call(e,n)}(e,n)}function m(e,n){for(var t=0,r=e.length;t<r;t++){if(n(e[t],t))return!0}return!1}function g(e,n){for(var t=e.length,r=new Array(t),o=0;o<t;o++){var i=e[o];r[o]=n(i,o)}return r}function p(e,n){for(var t=0,r=e.length;t<r;t++){n(e[t],t)}}function h(e,n){for(var t=[],r=0,o=e.length;r<o;r++){var i=e[r];n(i,r)&&t.push(i)}return t}function v(e,n,t){return function(e,n){for(var t=e.length-1;0<=t;t--){n(e[t],t)}}(e,function(e){t=n(t,e)}),t}function w(e,n,t){return p(e,function(e){t=n(t,e)}),t}function y(e,n){for(var t=0,r=e.length;t<r;t++){var o=e[t];if(n(o,t))return Me.some(o)}return Me.none()}function C(e,n){for(var t=0,r=e.length;t<r;t++){if(n(e[t],t))return Me.some(t)}return Me.none()}function S(e){for(var n=[],t=0,r=e.length;t<r;++t){if(!Le(e[t]))throw new Error("Arr.flatten item "+t+" was not an array, input: "+e);qe.apply(n,e[t])}return n}function x(e,n){var t=g(e,n);return S(t)}function A(e,n){for(var t=0,r=e.length;t<r;++t){if(!0!==n(e[t],t))return!1}return!0}function E(e){var n=Fe.call(e,0);return n.reverse(),n}function N(e,n){for(var t=Ve(e),r=0,o=t.length;r<o;r++){var i=t[r];n(e[i],i)}}function k(e,t){return Ye(e,function(e,n){return{k:n,v:t(e,n)}})}function I(e,n){return Ke(e,n)?Me.from(e[n]):Me.none()}function B(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];if(n.length!==t.length)throw new Error('Wrong number of arguments to struct. Expected "['+n.length+']", got '+t.length+" arguments");var r={};return p(n,function(e,n){r[e]=D(t[n])}),r}}function P(e){return e.slice(0).sort()}function M(e,n){throw new Error("All required keys ("+P(e).join(", ")+") were not specified. Specified keys were: "+P(n).join(", ")+".")}function W(e){throw new Error("Unsupported keys for object: "+P(e).join(", "))}function _(n,e){if(!Le(e))throw new Error("The "+n+" fields must be an array. Was: "+e+".");p(e,function(e){if(!_e(e))throw new Error("The value "+e+" in the "+n+" fields was not a string.")})}function L(e){var t=P(e);y(t,function(e,n){return n<t.length-1&&e===t[n+1]}).each(function(e){throw new Error("The field: "+e+" occurs more than once in the combined fields: ["+t.join(", ")+"].")})}function j(e){return e.dom().nodeType}function z(n){return function(e){return j(e)===n}}function H(e){return j(e)===$e||"#comment"===en(e)}function F(e,n,t){if(!(_e(t)||je(t)||He(t)))throw f.console.error("Invalid call to Attr.set. Key ",n,":: Value ",t,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(n,t+"")}function U(e,n,t){F(e.dom(),n,t)}function q(e,n){var t=e.dom();N(n,function(e,n){F(t,n,e)})}function V(e,n){var t=e.dom().getAttribute(n);return null===t?undefined:t}function G(e,n){var t=e.dom();return!(!t||!t.hasAttribute)&&t.hasAttribute(n)}function Y(e,n){e.dom().removeAttribute(n)}function K(e){return w(e.dom().attributes,function(e,n){return e[n.name]=n.value,e},{})}function X(e,n,t){return""===n||!(e.length<n.length)&&e.substr(t,t+n.length)===n}function $(e,n){return-1!==e.indexOf(n)}function J(e,n){return X(e,n,0)}function Q(e){return e.style!==undefined&&ze(e.style.getPropertyValue)}function Z(t){var r,o=!1;return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return o||(o=!0,r=t.apply(null,e)),r}}function ee(e){var n=tn(e)?e.dom().parentNode:e.dom();return n!==undefined&&null!==n&&n.ownerDocument.body.contains(n)}function ne(e,n,t){if(!_e(t))throw f.console.error("Invalid call to CSS.set. Property ",n,":: Value ",t,":: Element ",e),new Error("CSS value must be a string: "+t);Q(e)&&e.style.setProperty(n,t)}function te(e,n,t){var r=e.dom();ne(r,n,t)}function re(e,n){var t=e.dom();N(n,function(e,n){ne(t,n,e)})}function oe(e,n){var t=e.dom(),r=f.window.getComputedStyle(t).getPropertyValue(n),o=""!==r||ee(e)?r:an(t,n);return null===o?undefined:o}function ie(e,n){var t=e.dom(),r=an(t,n);return Me.from(r).filter(function(e){return 0<e.length})}function ue(e,n){!function(e,n){Q(e)&&e.style.removeProperty(n)}(e.dom(),n),G(e,"style")&&""===function(e){return e.replace(/^\s+|\s+$/g,"")}(V(e,"style"))&&Y(e,"style")}function ce(e,n,t){return 0!=(e.compareDocumentPosition(n)&t)}function ae(e,n){var t=function(e,n){for(var t=0;t<e.length;t++){var r=e[t];if(r.test(n))return r}return undefined}(e,n);if(!t)return{major:0,minor:0};function r(e){return Number(n.replace(t,"$"+e))}return dn(r(1),r(2))}function le(e,n){return function(){return n===e}}function fe(e,n){return function(){return n===e}}function se(e,n){var t=String(n).toLowerCase();return y(e,function(e){return e.search(t)})}function de(n){return function(e){return $(e,n)}}function me(){return Nn.get()}function ge(e,n){var t=e.dom();if(t.nodeType!==kn)return!1;var r=t;if(r.matches!==undefined)return r.matches(n);if(r.msMatchesSelector!==undefined)return r.msMatchesSelector(n);if(r.webkitMatchesSelector!==undefined)return r.webkitMatchesSelector(n);if(r.mozMatchesSelector!==undefined)return r.mozMatchesSelector(n);throw new Error("Browser lacks native selectors")}function pe(e){return e.nodeType!==kn&&e.nodeType!==In||0===e.childElementCount}function he(e){return on.fromDom(e.dom().ownerDocument)}function ve(e){return Me.from(e.dom().parentNode).map(on.fromDom)}function be(e,n){for(var t=ze(n)?n:s,r=e.dom(),o=[];null!==r.parentNode&&r.parentNode!==undefined;){var i=r.parentNode,u=on.fromDom(i);if(o.push(u),!0===t(u))break;r=i}return o}function we(e){return Me.from(e.dom().previousSibling).map(on.fromDom)}function ye(e){return Me.from(e.dom().nextSibling).map(on.fromDom)}function Ce(e){return g(e.dom().childNodes,on.fromDom)}function Se(e,n){var t=e.dom().childNodes;return Me.from(t[n]).map(on.fromDom)}function xe(n,t){ve(n).each(function(e){e.dom().insertBefore(t.dom(),n.dom())})}function Re(e,n){ye(e).fold(function(){ve(e).each(function(e){Wn(e,n)})},function(e){xe(e,n)})}function Te(n,t){(function(e){return Se(e,0)})(n).fold(function(){Wn(n,t)},function(e){n.dom().insertBefore(t.dom(),e.dom())})}function Oe(e,n){xe(e,n),Wn(n,e)}function De(r,o){p(o,function(e,n){var t=0===n?r:o[n-1];Re(t,e)})}function Ae(n,e){p(e,function(e){Wn(n,e)})}function Ee(e){e.dom().textContent="",p(Ce(e),function(e){_n(e)})}function Ne(e){var n=Ce(e);0<n.length&&function(n,e){p(e,function(e){xe(n,e)})}(e,n),_n(e)}function ke(e,n,t){return function(e,n,t){return h(be(e,t),n)}(e,function(e){return ge(e,n)},t)}function Ie(e,n){return function(e,n){return h(Ce(e),n)}(e,function(e){return ge(e,n)})}function Be(e,n){return function(e,n){var t=n===undefined?f.document:n.dom();return pe(t)?[]:g(t.querySelectorAll(e),on.fromDom)}(n,e)}var Pe=function(t){function e(){return o}function n(e){return e(t)}var r=D(t),o={fold:function(e,n){return n(t)},is:function(e){return t===e},isSome:i,isNone:s,getOr:r,getOrThunk:r,getOrDie:r,getOrNull:r,getOrUndefined:r,or:e,orThunk:e,map:function(e){return Pe(e(t))},each:function(e){e(t)},bind:n,exists:n,forall:n,filter:function(e){return e(t)?o:u},toArray:function(){return[t]},toString:function(){return"some("+t+")"},equals:function(e){return e.is(t)},equals_:function(e,n){return e.fold(s,function(e){return n(t,e)})}};return o},Me={some:Pe,none:e,from:function(e){return null===e||e===undefined?u:Pe(e)}},We=tinymce.util.Tools.resolve("tinymce.PluginManager"),_e=a("string"),Le=a("array"),je=a("boolean"),ze=a("function"),He=a("number"),Fe=Array.prototype.slice,Ue=Array.prototype.indexOf,qe=Array.prototype.push,Ve=(ze(Array.from)&&Array.from,Object.keys),Ge=Object.hasOwnProperty,Ye=function(e,r){var o={};return N(e,function(e,n){var t=r(e,n);o[t.k]=t.v}),o},Ke=function(e,n){return Ge.call(e,n)},Xe=function(o,i){var u=o.concat(i);if(0===u.length)throw new Error("You must specify at least one required or optional field.");return _("required",o),_("optional",i),L(u),function(n){var t=Ve(n);A(o,function(e){return l(t,e)})||M(o,t);var e=h(t,function(e){return!l(u,e)});0<e.length&&W(e);var r={};return p(o,function(e){r[e]=D(n[e])}),p(i,function(e){r[e]=D(Object.prototype.hasOwnProperty.call(n,e)?Me.some(n[e]):Me.none())}),r}},$e=(f.Node.ATTRIBUTE_NODE,f.Node.CDATA_SECTION_NODE,f.Node.COMMENT_NODE),Je=f.Node.DOCUMENT_NODE,Qe=(f.Node.DOCUMENT_TYPE_NODE,f.Node.DOCUMENT_FRAGMENT_NODE,f.Node.ELEMENT_NODE),Ze=f.Node.TEXT_NODE,en=(f.Node.PROCESSING_INSTRUCTION_NODE,f.Node.ENTITY_REFERENCE_NODE,f.Node.ENTITY_NODE,f.Node.NOTATION_NODE,"undefined"!=typeof f.window?f.window:Function("return this;")(),function(e){return e.dom().nodeName.toLowerCase()}),nn=z(Qe),tn=z(Ze),rn=function(e){if(null===e||e===undefined)throw new Error("Node cannot be null or undefined");return{dom:D(e)}},on={fromHtml:function(e,n){var t=(n||f.document).createElement("div");if(t.innerHTML=e,!t.hasChildNodes()||1<t.childNodes.length)throw f.console.error("HTML does not have a single root node",e),new Error("HTML must have a single root node");return rn(t.childNodes[0])},fromTag:function(e,n){var t=(n||f.document).createElement(e);return rn(t)},fromText:function(e,n){var t=(n||f.document).createTextNode(e);return rn(t)},fromDom:rn,fromPoint:function(e,n,t){var r=e.dom();return Me.from(r.elementFromPoint(n,t)).map(rn)}},un=Z(function(){return cn(on.fromDom(f.document))}),cn=function(e){var n=e.dom().body;if(null===n||n===undefined)throw new Error("Body is not available yet");return on.fromDom(n)},an=function(e,n){return Q(e)?e.style.getPropertyValue(n):""},ln=function(e,n){return ce(e,n,f.Node.DOCUMENT_POSITION_CONTAINED_BY)},fn=function(){return(fn=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++)for(var o in n=arguments[t])Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o]);return e}).apply(this,arguments)},sn=function(){return dn(0,0)},dn=function(e,n){return{major:e,minor:n}},mn={nu:dn,detect:function(e,n){var t=String(n).toLowerCase();return 0===e.length?sn():ae(e,t)},unknown:sn},gn="Firefox",pn=function(e){var n=e.current;return{current:n,version:e.version,isEdge:le("Edge",n),isChrome:le("Chrome",n),isIE:le("IE",n),isOpera:le("Opera",n),isFirefox:le(gn,n),isSafari:le("Safari",n)}},hn={unknown:function(){return pn({current:undefined,version:mn.unknown()})},nu:pn,edge:D("Edge"),chrome:D("Chrome"),ie:D("IE"),opera:D("Opera"),firefox:D(gn),safari:D("Safari")},vn="Windows",bn="Android",wn="Solaris",yn="FreeBSD",Cn="ChromeOS",Sn=function(e){var n=e.current;return{current:n,version:e.version,isWindows:fe(vn,n),isiOS:fe("iOS",n),isAndroid:fe(bn,n),isOSX:fe("OSX",n),isLinux:fe("Linux",n),isSolaris:fe(wn,n),isFreeBSD:fe(yn,n),isChromeOS:fe(Cn,n)}},xn={unknown:function(){return Sn({current:undefined,version:mn.unknown()})},nu:Sn,windows:D(vn),ios:D("iOS"),android:D(bn),linux:D("Linux"),osx:D("OSX"),solaris:D(wn),freebsd:D(yn),chromeos:D(Cn)},Rn=function(e,t){return se(e,t).map(function(e){var n=mn.detect(e.versionRegexes,t);return{current:e.name,version:n}})},Tn=function(e,t){return se(e,t).map(function(e){var n=mn.detect(e.versionRegexes,t);return{current:e.name,version:n}})},On=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Dn=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(e){return $(e,"edge/")&&$(e,"chrome")&&$(e,"safari")&&$(e,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,On],search:function(e){return $(e,"chrome")&&!$(e,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(e){return $(e,"msie")||$(e,"trident")}},{name:"Opera",versionRegexes:[On,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:de("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:de("firefox")},{name:"Safari",versionRegexes:[On,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(e){return($(e,"safari")||$(e,"mobile/"))&&$(e,"applewebkit")}}],An=[{name:"Windows",search:de("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(e){return $(e,"iphone")||$(e,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:de("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:de("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:de("linux"),versionRegexes:[]},{name:"Solaris",search:de("sunos"),versionRegexes:[]},{name:"FreeBSD",search:de("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:de("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],En={browsers:D(Dn),oses:D(An)},Nn=R(function(e,n){var t=En.browsers(),r=En.oses(),o=Rn(t,e).fold(hn.unknown,hn.nu),i=Tn(r,e).fold(xn.unknown,xn.nu);return{browser:o,os:i,deviceType:function(e,n,t,r){var o=e.isiOS()&&!0===/ipad/i.test(t),i=e.isiOS()&&!o,u=e.isiOS()||e.isAndroid(),c=u||r("(pointer:coarse)"),a=o||!i&&u&&r("(min-device-width:768px)"),l=i||u&&!a,f=n.isSafari()&&e.isiOS()&&!1===/safari/i.test(t),s=!l&&!a&&!f;return{isiPad:D(o),isiPhone:D(i),isTablet:D(a),isPhone:D(l),isTouch:D(c),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:D(f),isDesktop:D(s)}}(i,o,e,n)}}(f.navigator.userAgent,function(e){return f.window.matchMedia(e).matches})),kn=Qe,In=Je,Bn=function(e,n){return e.dom()===n.dom()},Pn=me().browser.isIE()?function(e,n){return ln(e.dom(),n.dom())}:function(e,n){var t=e.dom(),r=n.dom();return t!==r&&t.contains(r)},Mn=ge,Wn=(B("element","offset"),function(e,n){e.dom().appendChild(n.dom())}),_n=function(e){var n=e.dom();null!==n.parentNode&&n.parentNode.removeChild(n)},Ln=(B("width","height"),B("width","height"),B("rows","columns")),jn=B("row","column"),zn=(B("x","y"),B("element","rowspan","colspan")),Hn=B("element","rowspan","colspan","isNew"),Fn=B("element","rowspan","colspan","row","column"),Un=B("element","cells","section"),qn=B("element","isNew"),Vn=B("element","cells","section","isNew"),Gn=B("cells","section"),Yn=B("details","section"),Kn=B("startRow","startCol","finishRow","finishCol"),Xn=function(e,n){var t=[];return p(Ce(e),function(e){n(e)&&(t=t.concat([e])),t=t.concat(Xn(e,n))}),t};function $n(e,n,t,r,o){return e(t,r)?Me.some(t):ze(o)&&o(t)?Me.none():n(t,r,o)}function Jn(e,n,t){for(var r=e.dom(),o=ze(t)?t:D(!1);r.parentNode;){r=r.parentNode;var i=on.fromDom(r);if(n(i))return Me.some(i);if(o(i))break}return Me.none()}function Qn(e,n,t){return Jn(e,function(e){return ge(e,n)},t)}function Zn(e,n){return function(e,n){return y(e.dom().childNodes,function(e){return n(on.fromDom(e))}).map(on.fromDom)}(e,function(e){return ge(e,n)})}function et(e,n){return function(e,n){var t=n===undefined?f.document:n.dom();return pe(t)?Me.none():Me.from(t.querySelector(e)).map(on.fromDom)}(n,e)}function nt(e,n,t){return $n(ge,Qn,e,n,t)}function tt(e,n,t){return void 0===t&&(t=s),t(n)?Me.none():l(e,en(n))?Me.some(n):Qn(n,e.join(","),function(e){return ge(e,"table")||t(e)})}function rt(n,e){return ve(e).map(function(e){return Ie(e,n)})}function ot(e,n){return parseInt(V(e,n),10)}function it(e,n){return e+","+n}var ut=function(e,n,t){return x(Ce(e),function(e){return ge(e,n)?t(e)?[e]:[]:ut(e,n,t)})},ct={firstLayer:function(e,n){return ut(e,n,D(!0))},filterFirstLayer:ut},at=b(rt,"th,td"),lt=b(rt,"tr"),ft={cell:function(e,n){return tt(["td","th"],e,n)},firstCell:function(e){return et(e,"th,td")},cells:function(e){return ct.firstLayer(e,"th,td")},neighbourCells:at,table:function(e,n){return nt(e,"table",n)},row:function(e,n){return tt(["tr"],e,n)},rows:function(e){return ct.firstLayer(e,"tr")},notCell:function(e,n){return tt(["caption","tr","tbody","tfoot","thead"],e,n)},neighbourRows:lt,attr:ot,grid:function(e,n,t){var r=ot(e,n),o=ot(e,t);return Ln(r,o)}},st=function(e){var n=ft.rows(e);return g(n,function(e){var n=e,t=ve(n).map(function(e){var n=en(e);return"tfoot"===n||"thead"===n||"tbody"===n?n:"tbody"}).getOr("tbody"),r=g(ft.cells(e),function(e){var n=G(e,"rowspan")?parseInt(V(e,"rowspan"),10):1,t=G(e,"colspan")?parseInt(V(e,"colspan"),10):1;return zn(e,n,t)});return Un(n,r,t)})},dt=function(e,t){return g(e,function(e){var n=g(ft.cells(e),function(e){var n=G(e,"rowspan")?parseInt(V(e,"rowspan"),10):1,t=G(e,"colspan")?parseInt(V(e,"colspan"),10):1;return zn(e,n,t)});return Un(e,n,t.section())})},mt=function(e,n){var t=x(e.all(),function(e){return e.cells()});return h(t,n)},gt={generate:function(e){var l={},n=[],t=e.length,f=0;p(e,function(e,c){var a=[];p(e.cells(),function(e){for(var n=0;l[it(c,n)]!==undefined;)n++;for(var t=Fn(e.element(),e.rowspan(),e.colspan(),c,n),r=0;r<e.colspan();r++)for(var o=0;o<e.rowspan();o++){var i=n+r,u=it(c+o,i);l[u]=t,f=Math.max(f,i+1)}a.push(t)}),n.push(Un(e.element(),a,e.section()))});var r=Ln(t,f);return{grid:D(r),access:D(l),all:D(n)}},getAt:function(e,n,t){var r=e.access()[it(n,t)];return r!==undefined?Me.some(r):Me.none()},findItem:function(e,n,t){var r=mt(e,function(e){return t(n,e.element())});return 0<r.length?Me.some(r[0]):Me.none()},filterItems:mt,justCells:function(e){var n=g(e.all(),function(e){return e.cells()});return S(n)}},pt=B("minRow","minCol","maxRow","maxCol"),ht=function(e,n){function t(e){return ge(e.element(),n)}var r=st(e),o=gt.generate(r),i=function(e,i){var n=e.grid().columns(),u=e.grid().rows(),c=n,a=0,l=0;return N(e.access(),function(e){if(i(e)){var n=e.row(),t=n+e.rowspan()-1,r=e.column(),o=r+e.colspan()-1;n<u?u=n:a<t&&(a=t),r<c?c=r:l<o&&(l=o)}}),pt(u,c,a,l)}(o,t),u="th:not("+n+"),td:not("+n+")",c=ct.filterFirstLayer(e,"th,td",function(e){return ge(e,u)});return p(c,_n),function(e,n,t,r){for(var o,i,u,c=n.grid().columns(),a=n.grid().rows(),l=0;l<a;l++)for(var f=!1,s=0;s<c;s++){if(!(l<t.minRow()||l>t.maxRow()||s<t.minCol()||s>t.maxCol()))gt.getAt(n,l,s).filter(r).isNone()?(o=f,void 0,i=e[l].element(),u=on.fromTag("td"),Wn(u,on.fromTag("br")),(o?Wn:Te)(i,u)):f=!0}}(r,o,i,t),function(e,n){var t=h(ct.firstLayer(e,"tr"),function(e){return 0===e.dom().childElementCount});p(t,_n),n.minCol()!==n.maxCol()&&n.minRow()!==n.maxRow()||p(ct.firstLayer(e,"th,td"),function(e){Y(e,"rowspan"),Y(e,"colspan")}),Y(e,"width"),Y(e,"height"),ue(e,"width"),ue(e,"height")}(e,i),e};function vt(e){return Pt.get(e)}function bt(e){return Pt.getOption(e)}function wt(e,n){Pt.set(e,n)}function yt(e){return"img"===en(e)?1:bt(e).fold(function(){return Ce(e).length},function(e){return e.length})}function Ct(e){return function(e){return bt(e).filter(function(e){return 0!==e.trim().length||-1<e.indexOf("\xa0")}).isSome()}(e)||l(Mt,en(e))}function St(e){return function(e,o){var i=function(e){for(var n=0;n<e.childNodes.length;n++){var t=on.fromDom(e.childNodes[n]);if(o(t))return Me.some(t);var r=i(e.childNodes[n]);if(r.isSome())return r}return Me.none()};return i(e.dom())}(e,Ct)}function xt(e){return Wt(e,Ct)}function Rt(e,n){return on.fromDom(e.dom().cloneNode(n))}function Tt(e){return Rt(e,!1)}function Ot(e){return Rt(e,!0)}function Dt(e,n){var t=function(e,n){var t=on.fromTag(n),r=K(e);return q(t,r),t}(e,n),r=Ce(Ot(e));return Ae(t,r),t}function At(){var e=on.fromTag("td");return Wn(e,on.fromTag("br")),e}function Et(e,n,t){var r=Dt(e,n);return N(t,function(e,n){null===e?Y(r,n):U(r,n,e)}),r}function Nt(e){return e}function kt(e){return function(){return on.fromTag("tr",e.dom())}}function It(e,n){return n.column()>=e.startCol()&&n.column()+n.colspan()-1<=e.finishCol()&&n.row()>=e.startRow()&&n.row()+n.rowspan()-1<=e.finishRow()}function Bt(e,n,t){var r=gt.findItem(e,n,Bn),o=gt.findItem(e,t,Bn);return r.bind(function(n){return o.map(function(e){return function(e,n){return Kn(Math.min(e.row(),n.row()),Math.min(e.column(),n.column()),Math.max(e.row()+e.rowspan()-1,n.row()+n.rowspan()-1),Math.max(e.column()+e.colspan()-1,n.column()+n.colspan()-1))}(n,e)})})}var Pt=function Zf(t,r){var n=function(e){return t(e)?Me.from(e.dom().nodeValue):Me.none()};return{get:function(e){if(!t(e))throw new Error("Can only get "+r+" value of a "+r+" node");return n(e).getOr("")},getOption:n,set:function(e,n){if(!t(e))throw new Error("Can only set raw "+r+" value of a "+r+" node");e.dom().nodeValue=n}}}(tn,"text"),Mt=["img","br"],Wt=function(e,i){var u=function(e){for(var n=Ce(e),t=n.length-1;0<=t;t--){var r=n[t];if(i(r))return Me.some(r);var o=u(r);if(o.isSome())return o}return Me.none()};return u(e)},_t={cellOperations:function(i,e,u){return{row:kt(e),cell:function(e){var n=he(e.element()),t=on.fromTag(en(e.element()),n.dom()),r=u.getOr(["strong","em","b","i","span","font","h1","h2","h3","h4","h5","h6","p","div"]),o=0<r.length?function(r,o,i){return St(r).map(function(e){var n=i.join(","),t=ke(e,n,function(e){return Bn(e,r)});return v(t,function(e,n){var t=Tt(n);return Y(t,"contenteditable"),Wn(e,t),t},o)}).getOr(o)}(e.element(),t,r):t;return Wn(o,on.fromTag("br")),function(e,n){var t=e.dom(),r=n.dom();Q(t)&&Q(r)&&(r.style.cssText=t.style.cssText)}(e.element(),t),ue(t,"height"),1!==e.colspan()&&ue(e.element(),"width"),i(e.element(),t),t},replace:Et,gap:At}},paste:function(e){return{row:kt(e),cell:At,replace:Nt,gap:At}}},Lt=function(e,n){var t=n.column(),r=n.column()+n.colspan()-1,o=n.row(),i=n.row()+n.rowspan()-1;return t<=e.finishCol()&&r>=e.startCol()&&o<=e.finishRow()&&i>=e.startRow()},jt=function(e,n){for(var t=!0,r=b(It,n),o=n.startRow();o<=n.finishRow();o++)for(var i=n.startCol();i<=n.finishCol();i++)t=t&&gt.getAt(e,o,i).exists(r);return t?Me.some(n):Me.none()},zt=Bt,Ht=function(n,e,t){return Bt(n,e,t).bind(function(e){return jt(n,e)})},Ft=function(r,e,o,i){return gt.findItem(r,e,Bn).bind(function(e){var n=0<o?e.row()+e.rowspan()-1:e.row(),t=0<i?e.column()+e.colspan()-1:e.column();return gt.getAt(r,n+o,t+i).map(function(e){return e.element()})})},Ut=function(t,e,n){return zt(t,e,n).map(function(e){var n=gt.filterItems(t,b(Lt,e));return g(n,function(e){return e.element()})})},qt=function(e,n){return gt.findItem(e,n,function(e,n){return Pn(n,e)}).map(function(e){return e.element()})},Vt=function(e){var n=st(e);return gt.generate(n)},Gt=function(t,r,o){return ft.table(t).bind(function(e){var n=Vt(e);return Ft(n,t,r,o)})},Yt=function(e,n,t){var r=Vt(e);return Ut(r,n,t)},Kt=function(e,n,t,r,o){var i=Vt(e),u=Bn(e,t)?Me.some(n):qt(i,n),c=Bn(e,o)?Me.some(r):qt(i,r);return u.bind(function(n){return c.bind(function(e){return Ut(i,n,e)})})},Xt=function(e,n,t){var r=Vt(e);return Ht(r,n,t)},$t=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","li","table","thead","tbody","tfoot","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"];function Jt(){return{up:D({selector:Qn,closest:nt,predicate:Jn,all:be}),down:D({selector:Be,predicate:Xn}),styles:D({get:oe,getRaw:ie,set:te,remove:ue}),attrs:D({get:V,set:U,remove:Y,copyTo:function(e,n){var t=K(e);q(n,t)}}),insert:D({before:xe,after:Re,afterAll:De,append:Wn,appendAll:Ae,prepend:Te,wrap:Oe}),remove:D({unwrap:Ne,remove:_n}),create:D({nu:on.fromTag,clone:function(e){return on.fromDom(e.dom().cloneNode(!1))},text:on.fromText}),query:D({comparePosition:function(e,n){return e.dom().compareDocumentPosition(n.dom())},prevSibling:we,nextSibling:ye}),property:D({children:Ce,name:en,parent:ve,document:function(e){return e.dom().ownerDocument},isText:tn,isComment:H,isElement:nn,getText:vt,setText:wt,isBoundary:function(e){return!!nn(e)&&("body"===en(e)||l($t,en(e)))},isEmptyTag:function(e){return!!nn(e)&&l(["br","img","hr","input"],en(e))}}),eq:Bn,is:Mn}}function Qt(e,n,t){var r=e.property().children(n);return C(r,b(e.eq,t)).map(function(e){return{before:D(r.slice(0,e)),after:D(r.slice(e+1))}})}function Zt(e,n){return b(e.eq,n)}function er(n,e,t,r){function o(n){return C(n,r).fold(function(){return n},function(e){return n.slice(0,e+1)})}void 0===r&&(r=s);var i=[e].concat(n.up().all(e)),u=[t].concat(n.up().all(t)),c=o(i),a=o(u),l=y(c,function(e){return m(a,Zt(n,e))});return{firstpath:D(c),secondpath:D(a),shared:D(l)}}function nr(e){return Qn(e,"table")}function tr(c,a,r){function l(n){return function(e){return r!==undefined&&r(e)||Bn(e,n)}}return Bn(c,a)?Me.some(dr.create({boxes:Me.some([c]),start:c,finish:a})):nr(c).bind(function(u){return nr(a).bind(function(i){if(Bn(u,i))return Me.some(dr.create({boxes:Yt(u,c,a),start:c,finish:a}));if(Pn(u,i)){var e=0<(n=ke(a,"td,th",l(u))).length?n[n.length-1]:a;return Me.some(dr.create({boxes:Kt(u,c,u,a,i),start:c,finish:e}))}if(Pn(i,u)){var n,t=0<(n=ke(c,"td,th",l(i))).length?n[n.length-1]:c;return Me.some(dr.create({boxes:Kt(i,c,u,a,i),start:c,finish:t}))}return sr.ancestors(c,a).shared().bind(function(e){return nt(e,"table",r).bind(function(e){var n=ke(a,"td,th",l(e)),t=0<n.length?n[n.length-1]:a,r=ke(c,"td,th",l(e)),o=0<r.length?r[r.length-1]:c;return Me.some(dr.create({boxes:Kt(e,c,u,a,i),start:o,finish:t}))})})})})}function rr(e,n){return Tr.cata(n.get(),D([]),o,D([e]))}function or(e){return{element:D(e),mergable:Me.none,unmergable:Me.none,selection:D([e])}}var ir=B("left","right"),ur=B("first","second","splits"),cr=function(r,o,e,n){var t=o(r,e);return v(n,function(e,n){var t=o(r,n);return ar(r,e,t)},t)},ar=function(n,e,t){return e.bind(function(e){return t.filter(b(n.eq,e))})},lr={sharedOne:function(e,n,t){return 0<t.length?function(e,n,t,r){return r(e,n,t[0],t.slice(1))}(e,n,t,cr):Me.none()},subset:function(n,e,t){var r=er(n,e,t);return r.shared().bind(function(e){return function(o,i,e,n){var u=o.property().children(i);if(o.eq(i,e[0]))return Me.some([e[0]]);if(o.eq(i,n[0]))return Me.some([n[0]]);function t(e){var n=E(e),t=C(n,Zt(o,i)).getOr(-1),r=t<n.length-1?n[t+1]:n[t];return C(u,Zt(o,r))}var r=t(e),c=t(n);return r.bind(function(r){return c.map(function(e){var n=Math.min(r,e),t=Math.max(r,e);return u.slice(n,t+1)})})}(n,e,r.firstpath(),r.secondpath())})},ancestors:er,breakToLeft:function(t,r,o){return Qt(t,r,o).map(function(e){var n=t.create().clone(r);return t.insert().appendAll(n,e.before().concat([o])),t.insert().appendAll(r,e.after()),t.insert().before(r,n),ir(n,r)})},breakToRight:function(t,r,e){return Qt(t,r,e).map(function(e){var n=t.create().clone(r);return t.insert().appendAll(n,e.after()),t.insert().after(r,n),ir(r,n)})},breakPath:function(i,e,u,c){var a=function(e,n,o){var t=ur(e,Me.none(),o);return u(e)?ur(e,n,o):i.property().parent(e).bind(function(r){return c(i,r,e).map(function(e){var n=[{first:e.left,second:e.right}],t=u(r)?r:e.left();return a(t,Me.some(e.right()),o.concat(n))})}).getOr(t)};return a(e,Me.none(),[])}},fr=Jt(),sr={sharedOne:function(t,e){return lr.sharedOne(fr,function(e,n){return t(n)},e)},subset:function(e,n){return lr.subset(fr,e,n)},ancestors:function(e,n,t){return lr.ancestors(fr,e,n,t)},breakToLeft:function(e,n){return lr.breakToLeft(fr,e,n)},breakToRight:function(e,n){return lr.breakToRight(fr,e,n)},breakPath:function(e,n,r){return lr.breakPath(fr,e,n,function(e,n,t){return r(n,t)})}},dr={create:Xe(["boxes","start","finish"],[])},mr=tr,gr=function(e,n){var t=Be(e,n);return 0<t.length?Me.some(t):Me.none()},pr=function(e,n,t,r,o){return function(e,n){return y(e,function(e){return ge(e,n)})}(e,o).bind(function(e){return Gt(e,n,t).bind(function(e){return function(n,t){return Qn(n,"table").bind(function(e){return et(e,t).bind(function(e){return tr(e,n).bind(function(n){return n.boxes().map(function(e){return{boxes:D(e),start:D(n.start()),finish:D(n.finish())}})})})})}(e,r)})})},hr=function(e,n,r){return et(e,n).bind(function(t){return et(e,r).bind(function(n){return sr.sharedOne(nr,[t,n]).map(function(e){return{first:D(t),last:D(n),table:D(e)}})})})},vr=function(e,n){return gr(e,n)},br=function(o,e,n){return hr(o,e,n).bind(function(t){function e(e){return Bn(o,e)}var n=Qn(t.first(),"thead,tfoot,tbody,table",e),r=Qn(t.last(),"thead,tfoot,tbody,table",e);return n.bind(function(n){return r.bind(function(e){return Bn(n,e)?Xt(t.table(),t.first(),t.last()):Me.none()})})})},wr="data-mce-selected",yr="data-mce-first-selected",Cr="data-mce-last-selected",Sr={selected:D(wr),selectedSelector:D("td[data-mce-selected],th[data-mce-selected]"),attributeSelector:D("[data-mce-selected]"),firstSelected:D(yr),firstSelectedSelector:D("td[data-mce-first-selected],th[data-mce-first-selected]"),lastSelected:D(Cr),lastSelectedSelector:D("td[data-mce-last-selected],th[data-mce-last-selected]")},xr=function(u){if(!Le(u))throw new Error("cases must be an array");if(0===u.length)throw new Error("there must be at least one case");var c=[],t={};return p(u,function(e,r){var n=Ve(e);if(1!==n.length)throw new Error("one and only one name per case");var o=n[0],i=e[o];if(t[o]!==undefined)throw new Error("duplicate key detected:"+o);if("cata"===o)throw new Error("cannot have a case named cata (sorry)");if(!Le(i))throw new Error("case arguments must be an array");c.push(o),t[o]=function(){var e=arguments.length;if(e!==i.length)throw new Error("Wrong number of arguments to case "+o+". Expected "+i.length+" ("+i+"), got "+e);for(var t=new Array(e),n=0;n<t.length;n++)t[n]=arguments[n];return{fold:function(){if(arguments.length!==u.length)throw new Error("Wrong number of arguments to fold. Expected "+u.length+", got "+arguments.length);return arguments[r].apply(null,t)},match:function(e){var n=Ve(e);if(c.length!==n.length)throw new Error("Wrong number of arguments to match. Expected: "+c.join(",")+"\nActual: "+n.join(","));if(!A(c,function(e){return l(n,e)}))throw new Error("Not all branches were specified when using match. Specified: "+n.join(", ")+"\nRequired: "+c.join(", "));return e[o].apply(null,t)},log:function(e){f.console.log(e,{constructors:c,constructor:o,params:t})}}}}),t},Rr=xr([{none:[]},{multiple:["elements"]},{single:["selection"]}]),Tr={cata:function(e,n,t,r){return e.fold(n,t,r)},none:Rr.none,multiple:Rr.multiple,single:Rr.single},Or=function(t,e){return Tr.cata(e.get(),Me.none,function(n,e){return 0===n.length?Me.none():br(t,Sr.firstSelectedSelector(),Sr.lastSelectedSelector()).bind(function(e){return 1<n.length?Me.some({bounds:D(e),cells:D(n)}):Me.none()})},Me.none)},Dr=function(e,n){var t=rr(e,n);return 0<t.length&&A(t,function(e){return G(e,"rowspan")&&1<parseInt(V(e,"rowspan"),10)||G(e,"colspan")&&1<parseInt(V(e,"colspan"),10)})?Me.some(t):Me.none()},Ar=rr,Er=B("element","clipboard","generators"),Nr={noMenu:or,forMenu:function(e,n,t){return{element:D(t),mergable:D(Or(n,e)),unmergable:D(Dr(t,e)),selection:D(Ar(t,e))}},notCell:function(e){return or(e)},paste:Er,pasteRows:function(e,n,t,r,o){return{element:D(t),mergable:Me.none,unmergable:Me.none,selection:D(Ar(t,e)),clipboard:D(r),generators:D(o)}}},kr={registerEvents:function(c,e,a,l){c.on("BeforeGetContent",function(n){!0===n.selection&&Tr.cata(e.get(),T,function(e){n.preventDefault(),function(e){return ft.table(e[0]).map(Ot).map(function(e){return[ht(e,Sr.attributeSelector())]})}(e).each(function(e){n.content="text"===n.format?function(e){return g(e,function(e){return e.dom().innerText}).join("")}(e):function(n,e){return g(e,function(e){return n.selection.serializer.serialize(e.dom(),{})}).join("")}(c,e)})},T)}),c.on("BeforeSetContent",function(u){!0===u.selection&&!0===u.paste&&Me.from(c.dom.getParent(c.selection.getStart(),"th,td")).each(function(e){var i=on.fromDom(e);ft.table(i).each(function(n){var e=h(function(e,n){var t=(n||f.document).createElement("div");return t.innerHTML=e,Ce(on.fromDom(t))}(u.content),function(e){return"meta"!==en(e)});if(1===e.length&&"table"===en(e[0])){u.preventDefault();var t=on.fromDom(c.getDoc()),r=_t.paste(t),o=Nr.paste(i,e[0],r);a.pasteCells(n,o).each(function(e){c.selection.setRng(e),c.focus(),l.clear(n)})}})})})}};function Ir(r,o){function e(e){var n=o(e);if(n<=0||null===n){var t=oe(e,r);return parseFloat(t)||0}return n}function i(o,e){return w(e,function(e,n){var t=oe(o,n),r=t===undefined?0:parseInt(t,10);return isNaN(r)?e:e+r},0)}return{set:function(e,n){if(!He(n)&&!n.match(/^[0-9]+$/))throw new Error(r+".set accepts only positive integer values. Value was "+n);var t=e.dom();Q(t)&&(t.style[r]=n+"px")},get:e,getOuter:e,aggregate:i,max:function(e,n,t){var r=i(e,t);return r<n?n-r:0}}}function Br(e){return Zr.get(e)}function Pr(e){return Zr.getOuter(e)}function Mr(e){return eo.get(e)}function Wr(e){return eo.getOuter(e)}function _r(e,n,t){return function(e,n){var t=parseFloat(e);return isNaN(t)?n:t}(oe(e,n),t)}function Lr(e,n){te(e,"height",n+"px")}function jr(e,n,t,r){var o=parseInt(e,10);return function(e,n){return X(e,n,e.length-n.length)}(e,"%")&&"table"!==en(n)?function(e,t,r,n){var o=ft.table(e).map(function(e){var n=r(e);return Math.floor(t/100*n)}).getOr(t);return n(e,o),o}(n,o,t,r):o}function zr(e){var n=function(e){return ie(e,"height").getOrThunk(function(){return to(e)+"px"})}(e);return n?jr(n,e,Br,Lr):Br(e)}function Hr(e){return ie(e,"width").fold(function(){return Me.from(V(e,"width"))},function(e){return Me.some(e)})}function Fr(e,n){return e/n.pixelWidth()*100}function Ur(e,n){return e!==undefined?e:n!==undefined?n:0}function qr(e){var n=e.dom().ownerDocument,t=n.body,r=n.defaultView,o=n.documentElement;if(t===e.dom())return fo(t.offsetLeft,t.offsetTop);var i=Ur(r.pageYOffset,o.scrollTop),u=Ur(r.pageXOffset,o.scrollLeft),c=Ur(o.clientTop,t.clientTop),a=Ur(o.clientLeft,t.clientLeft);return so(e).translate(u-a,i-c)}function Vr(e){return qr(e).left()+Wr(e)}function Gr(e){return qr(e).left()}function Yr(e,n){return go(e,Gr(n))}function Kr(e,n){return go(e,Vr(n))}function Xr(e){return qr(e).top()}function $r(e,n){return mo(e,Xr(n))}function Jr(e,n){return mo(e,Xr(n)+Pr(n))}function Qr(t,n,r){if(0===r.length)return[];var e=g(r.slice(1),function(e,n){return e.map(function(e){return t(n,e)})}),o=r[r.length-1].map(function(e){return n(r.length-1,e)});return e.concat([o])}var Zr=Ir("height",function(e){var n=e.dom();return ee(e)?n.getBoundingClientRect().height:n.offsetHeight}),eo=Ir("width",function(e){return e.dom().offsetWidth}),no=me(),to=function(e){return no.browser.isIE()||no.browser.isEdge()?function(e){var n=_r(e,"padding-top",0),t=_r(e,"padding-bottom",0),r=_r(e,"border-top-width",0),o=_r(e,"border-bottom-width",0),i=e.dom().getBoundingClientRect().height;return"border-box"===oe(e,"box-sizing")?i:i-n-t-(r+o)}(e):_r(e,"height",Br(e))},ro=/(\d+(\.\d+)?)(\w|%)*/,oo=/(\d+(\.\d+)?)%/,io=/(\d+(\.\d+)?)px|em/,uo=function(e,n){return G(e,n)?parseInt(V(e,n),10):1},co={percentageBasedSizeRegex:D(oo),pixelBasedSizeRegex:D(io),setPixelWidth:function(e,n){te(e,"width",n+"px")},setPercentageWidth:function(e,n){te(e,"width",n+"%")},setHeight:Lr,getPixelWidth:function(n,t){return Hr(n).fold(function(){return Mr(n)},function(e){return function(e,n,t){var r=io.exec(n);if(null!==r)return parseInt(r[1],10);var o=oo.exec(n);return null===o?Mr(e):function(e,n){return e/100*n.pixelWidth()}(parseFloat(o[1]),t)}(n,e,t)})},getPercentageWidth:function(n,t){return Hr(n).fold(function(){var e=Mr(n);return Fr(e,t)},function(e){return function(e,n,t){var r=oo.exec(n);if(null!==r)return parseFloat(r[1]);var o=Mr(e);return Fr(o,t)}(n,e,t)})},getGenericWidth:function(e){return Hr(e).bind(function(e){var n=ro.exec(e);return null!==n?Me.some({width:D(parseFloat(n[1])),unit:D(n[3])}):Me.none()})},setGenericWidth:function(e,n,t){te(e,"width",n+t)},getHeight:function(e){return function(e,n,t){return t(e)/uo(e,n)}(e,"rowspan",zr)},getRawWidth:Hr},ao=function(t,r){co.getGenericWidth(t).each(function(e){var n=e.width()/2;co.setGenericWidth(t,n,e.unit()),co.setGenericWidth(r,n,e.unit())})},lo=function(t,r){return{left:D(t),top:D(r),translate:function(e,n){return lo(t+e,r+n)}}},fo=lo,so=function(e){var n=e.dom(),t=n.ownerDocument.body;return t===n?fo(t.offsetLeft,t.offsetTop):ee(e)?function(e){var n=e.getBoundingClientRect();return fo(n.left,n.top)}(n):fo(0,0)},mo=B("row","y"),go=B("col","x"),po={height:{delta:o,positions:function(e){return Qr($r,Jr,e)},edge:Xr},rtl:{delta:function(e){return-e},edge:Vr,positions:function(e){return Qr(Kr,Yr,e)}},ltr:{delta:o,edge:Gr,positions:function(e){return Qr(Yr,Kr,e)}}},ho={ltr:po.ltr,rtl:po.rtl};function vo(n){function t(e){return n(e).isRtl()?ho.rtl:ho.ltr}return{delta:function(e,n){return t(n).delta(e,n)},edge:function(e){return t(e).edge(e)},positions:function(e,n){return t(n).positions(e,n)}}}function bo(e){for(var n=[],t=function(e){n.push(e)},r=0;r<e.length;r++)e[r].each(t);return n}function wo(e,n){for(var t=0;t<e.length;t++){var r=n(e[t],t);if(r.isSome())return r}return Me.none()}function yo(e,n,t,r){t===r?Y(e,n):U(e,n,t)}function Co(e,n){var t=V(e,n);return t===undefined||""===t?[]:t.split(" ")}function So(e){return e.dom().classList!==undefined}function xo(e,n){return function(e,n,t){var r=Co(e,n).concat([t]);return U(e,n,r.join(" ")),!0}(e,"class",n)}function Ro(e,n){return function(e,n,t){var r=h(Co(e,n),function(e){return e!==t});return 0<r.length?U(e,n,r.join(" ")):Y(e,n),!1}(e,"class",n)}function To(e,n){So(e)?e.dom().classList.add(n):xo(e,n)}function Oo(e){0===(So(e)?e.dom().classList:function(e){return Co(e,"class")}(e)).length&&Y(e,"class")}function Do(e,n){return So(e)&&e.dom().classList.contains(n)}function Ao(e,n){for(var t=[],r=e;r<n;r++)t.push(r);return t}function Eo(n,t){if(t<0||t>=n.length-1)return Me.none();var e=n[t].fold(function(){var e=E(n.slice(0,t));return wo(e,function(e,n){return e.map(function(e){return{value:e,delta:n+1}})})},function(e){return Me.some({value:e,delta:0})}),r=n[t+1].fold(function(){var e=n.slice(t+1);return wo(e,function(e,n){return e.map(function(e){return{value:e,delta:n+1}})})},function(e){return Me.some({value:e,delta:1})});return e.bind(function(t){return r.map(function(e){var n=e.delta+t.delta;return Math.abs(e.value-t.value)/n})})}function No(e){var n=e.replace(/\./g,"-");return{resolve:function(e){return n+"-"+e}}}function ko(e){var n=Be(e.parent(),"."+uu);p(n,_n)}function Io(t,e,r){var o=t.origin();p(e,function(e,n){e.each(function(e){var n=r(o,e);To(n,uu),Wn(t.parent(),n)})})}function Bo(e,n,t,r,o,i){var u=qr(n);!function(e,n,r,o){Io(e,n,function(e,n){var t=iu(n.row(),r.left()-e.left(),n.y()-e.top(),o,7);return To(t,cu),t})}(e,0<t.length?o.positions(t,n):[],u,Wr(n)),function(e,n,r,o){Io(e,n,function(e,n){var t=ou(n.col(),n.x()-e.left(),r.top()-e.top(),7,o);return To(t,au),t})}(e,0<r.length?i.positions(r,n):[],u,Pr(n))}function Po(e,n){var t=Be(e.parent(),"."+uu);p(t,n)}function Mo(e,n){return e.cells()[n]}function Wo(e,n){if(0===e.length)return 0;var t=e[0];return C(e,function(e){return!n(t.element(),e.element())}).fold(function(){return e.length},function(e){return e})}function _o(e,t){return g(e,function(e){var n=function(e){return wo(e,function(e){return ve(e.element()).map(function(e){var n=ve(e).isNone();return qn(e,n)})}).getOrThunk(function(){return qn(t.row(),!0)})}(e.details());return Vn(n.element(),e.details(),e.section(),n.isNew())})}function Lo(e,n){var t=bu(e,Bn);return _o(t,n)}function jo(e,n){var t=S(g(e.all(),function(e){return e.cells()}));return y(t,function(e){return Bn(n,e.element())})}function zo(c,a,l,f,s){return function(t,r,e,o,i){var n=st(r),u=gt.generate(n);return a(u,e).map(function(e){var n=function(e,n){return wu(e,n,!1)}(u,o),t=c(n,e,Bn,s(o)),r=Lo(t.grid(),o);return{grid:D(r),cursor:t.cursor}}).fold(function(){return Me.none()},function(e){var n=Qi(r,e.grid());return l(r,e.grid(),i),f(r),lu(t,r,po.height,i),Me.some({cursor:e.cursor,newRows:n.newRows,newCells:n.newCells})})}}function Ho(n,e){return ft.cell(e.element()).bind(function(e){return jo(n,e)})}function Fo(n,e){var t=g(e.selection(),function(e){return ft.cell(e).bind(function(e){return jo(n,e)})}),r=bo(t);return 0<r.length?Me.some({cells:r,generators:e.generators,clipboard:e.clipboard}):Me.none()}function Uo(n,e){var t=g(e.selection(),function(e){return ft.cell(e).bind(function(e){return jo(n,e)})}),r=bo(t);return 0<r.length?Me.some(r):Me.none()}function qo(e,n){return g(e,function(){return qn(n.cell(),!0)})}function Vo(n,e,t){return n.concat(function(e,n){for(var t=[],r=0;r<e;r++)t.push(n(r));return t}(e,function(e){return hu.setCells(n[n.length-1],qo(n[n.length-1].cells(),t))}))}function Go(e,n,t){return g(e,function(e){return hu.setCells(e,e.cells().concat(qo(Ao(0,n),t)))})}function Yo(e,t,r,n){return g(e,function(e){return hu.mapCells(e,function(e){return function(n){return m(t,function(e){return r(n.element(),e.element())})}(e)?qn(n(e.element(),r),!0):e})})}function Ko(e,n,t,r){return hu.getCellElement(e[n],t)!==undefined&&0<n&&r(hu.getCellElement(e[n-1],t),hu.getCellElement(e[n],t))}function Xo(e,n,t){return 0<n&&t(hu.getCellElement(e,n-1),hu.getCellElement(e,n))}function $o(e,n){return G(e,n)&&1<parseInt(V(e,n),10)}function Jo(e,n,t){return ie(e,n).fold(function(){return t(e)+"px"},function(e){return e})}function Qo(e,n){return Jo(e,"width",function(e){return co.getPixelWidth(e,n)})}function Zo(e){return Jo(e,"height",co.getHeight)}function ei(e,n,t,r,o){var i=nu(e),u=g(i,function(e){return e.map(n.edge)});return g(i,function(e,n){return e.filter(d(Fu.hasColspan)).fold(function(){var e=Eo(u,n);return r(e)},function(e){return t(e,o)})})}function ni(e){return e.map(function(e){return e+"px"}).getOr("")}function ti(e,n,t,r){var o=tu(e),i=g(o,function(e){return e.map(n.edge)});return g(o,function(e,n){return e.filter(d(Fu.hasRowspan)).fold(function(){var e=Eo(i,n);return r(e)},function(e){return t(e)})})}function ri(e,n,t){for(var r=0,o=e;o<n;o++)r+=t[o]!==undefined?t[o]:0;return r}function oi(e){var n=o;return{width:D(e),pixelWidth:D(e),getWidths:Uu.getPixelWidths,getCellDelta:n,singleColumnWidth:function(e,n){return[Math.max(Fu.minWidth(),e+n)-e]},minCellWidth:Fu.minWidth,setElementWidth:co.setPixelWidth,setTableWidth:function(e,n,t){var r=v(n,function(e,n){return e+n},0);co.setPixelWidth(e,r)}}}function ii(e,n){var t=co.percentageBasedSizeRegex().exec(n);if(null!==t)return function(e,n){var o=parseFloat(e),t=Mr(n);return{width:D(o),pixelWidth:D(t),getWidths:Uu.getPercentageWidths,getCellDelta:function(e){return e/t*100},singleColumnWidth:function(e,n){return[100-e]},minCellWidth:function(){return Fu.minWidth()/t*100},setElementWidth:co.setPercentageWidth,setTableWidth:function(e,n,t){var r=t/100*o;co.setPercentageWidth(e,o+r)}}}(t[1],e);var r=co.pixelBasedSizeRegex().exec(n);if(null!==r){var o=parseInt(r[1],10);return oi(o)}var i=Mr(e);return oi(i)}function ui(e){return gt.generate(e)}function ci(e){var n=st(e);return ui(n)}function ai(n,e){var t=h(e,function(e){return!l(n,e)});0<t.length&&W(t)}function li(e){return function(e,n){return Ju(e,n,{validate:ze,label:"function"})}(ai,e)}function fi(e){var n=G(e,"colspan")?parseInt(V(e,"colspan"),10):1,t=G(e,"rowspan")?parseInt(V(e,"rowspan"),10):1;return{element:D(e),colspan:D(n),rowspan:D(t)}}function si(e,n){var t=e.property().name(n);return l(tc,t)}function di(e,n){return l(["br","img","hr","input"],e.property().name(n))}function mi(e){0===ft.cells(e).length&&_n(e)}function gi(e,n,t){return dc(e,n,t).orThunk(function(){return dc(e,0,0)})}function pi(e,n,t){return sc(e,dc(e,n,t))}function hi(e){return w(e,function(e,n){return m(e,function(e){return e.row()===n.row()})?e:e.concat([n])},[]).sort(function(e,n){return e.row()-n.row()})}function vi(e){return w(e,function(e,n){return m(e,function(e){return e.column()===n.column()})?e:e.concat([n])},[]).sort(function(e,n){return e.column()-n.column()})}function bi(e,n,t){var r=dt(e,t),o=gt.generate(r);return wu(o,n,!0)}function wi(e){return e.getBoundingClientRect().width}function yi(e){return e.getBoundingClientRect().height}function Ci(e){return/^[0-9]+$/.test(e)&&(e+="px"),e}function Si(e){var n=Be(e,"td[data-mce-style],th[data-mce-style]");Y(e,"data-mce-style"),p(n,function(e){Y(e,"data-mce-style")})}function xi(e){return e.getParam("table_default_attributes",Cc,"object")}function Ri(e){return e.getParam("table_default_styles",yc,"object")}function Ti(e){return e.getParam("table_cell_advtab",!0,"boolean")}function Oi(e){return e.getParam("table_row_advtab",!0,"boolean")}function Di(e){return e.getParam("table_advtab",!0,"boolean")}function Ai(e){return e.getParam("table_style_by_css",!1,"boolean")}function Ei(e){return e.getParam("table_class_list",[],"array")}function Ni(e){return!1===e.getParam("table_responsive_width")}function ki(e,n){return e.fire("newrow",{node:n})}function Ii(e,n){return e.fire("newcell",{node:n})}function Bi(e,n,t,r){e.fire("ObjectResizeStart",{target:n,width:t,height:r})}function Pi(e,n,t,r){e.fire("ObjectResized",{target:n,width:t,height:r})}function Mi(n,e){function t(e){return J(e,"rgb")?n.toHex(e):e}return{borderwidth:ie(on.fromDom(e),"border-width").getOr(""),borderstyle:ie(on.fromDom(e),"border-style").getOr(""),bordercolor:ie(on.fromDom(e),"border-color").map(t).getOr(""),backgroundcolor:ie(on.fromDom(e),"background-color").map(t).getOr("")}}function Wi(e,n,t,r,o){var i={};return Ac.each(e.split(" "),function(e){r.formatter.matchNode(o,n+e)&&(i[t]=e)}),i[t]||(i[t]=""),i}function _i(e,n){e.setAttrib("scope",n.scope),e.setAttrib("class",n["class"]),e.setStyle("width",Ci(n.width)),e.setStyle("height",Ci(n.height))}function Li(e,n){e.setStyle("background-color",n.backgroundcolor),e.setStyle("border-color",n.bordercolor),e.setStyle("border-style",n.borderstyle),e.setStyle("border-width",Ci(n.borderwidth))}function ji(e,n,t){var r=e.dom,o=t.celltype&&n[0].nodeName.toLowerCase()!==t.celltype?r.rename(n[0],t.celltype):n[0],i=Vc.normal(r,o);_i(i,t),Ti(e)&&Li(i,t),kc(e,o),Ic(e,o),t.halign&&Ec(e,o,t.halign),t.valign&&Nc(e,o,t.valign)}function zi(t,e,r){var o=t.dom;Ac.each(e,function(e){r.celltype&&e.nodeName.toLowerCase()!==r.celltype&&(e=o.rename(e,r.celltype));var n=Vc.ifTruthy(o,e);_i(n,r),Ti(t)&&Li(n,r),r.halign&&Ec(t,e,r.halign),r.valign&&Nc(t,e,r.valign)})}function Hi(e,n,t){var r=t.getData();t.close(),e.undoManager.transact(function(){(1===n.length?ji:zi)(e,n,r),e.focus()})}function Fi(t,e,r,n){var o=t.dom,i=n.getData();n.close();var u=1===e.length?Vc.normal:Vc.ifTruthy;t.undoManager.transact(function(){Ac.each(e,function(e){i.type!==e.parentNode.nodeName.toLowerCase()&&function(e,n,t){var r=e.getParent(n,"table"),o=n.parentNode,i=e.select(t,r)[0];i||(i=e.create(t),r.firstChild?"CAPTION"===r.firstChild.nodeName?e.insertAfter(i,r.firstChild):r.insertBefore(i,r.firstChild):r.appendChild(i)),"tbody"===t&&"THEAD"===o.nodeName&&i.firstChild?i.insertBefore(n,i.firstChild):i.appendChild(n),o.hasChildNodes()||e.remove(o)}(t.dom,e,i.type);var n=u(o,e);n.setAttrib("scope",i.scope),n.setAttrib("class",i["class"]),n.setStyle("height",Ci(i.height)),Oi(t)&&function(e,n){e.setStyle("background-color",n.backgroundcolor),e.setStyle("border-color",n.bordercolor),e.setStyle("border-style",n.borderstyle)}(n,i),i.align!==r.align&&(kc(t,e),Ec(t,e,i.align))}),t.focus()})}function Ui(e,n,t,r,o){void 0===o&&(o=Zc);var i=on.fromTag("table");re(i,o.styles),q(i,o.attributes);var u=on.fromTag("tbody");Wn(i,u);for(var c=[],a=0;a<e;a++){for(var l=on.fromTag("tr"),f=0;f<n;f++){var s=a<t||f<r?on.fromTag("th"):on.fromTag("td");f<r&&U(s,"scope","row"),a<t&&U(s,"scope","col"),Wn(s,on.fromTag("br")),o.percentages&&te(s,"width",100/n+"%"),Wn(l,s)}c.push(l)}return Ae(u,c),i}function qi(e,n){e.selection.select(n.dom(),!0),e.selection.collapse(!0)}function Vi(t,r,e){var o,i=t.dom,u=e.getData();e.close(),""===u["class"]&&delete u["class"],t.undoManager.transact(function(){if(!r){var e=parseInt(u.cols,10)||1,n=parseInt(u.rows,10)||1;r=ea(t,e,n)}!function(e,n,t){var r=e.dom,o={},i={};if(o["class"]=t["class"],i.height=Ci(t.height),r.getAttrib(n,"width")&&!Ai(e)?o.width=function(e){return e?e.replace(/px$/,""):""}(t.width):i.width=Ci(t.width),Ai(e)?(i["border-width"]=Ci(t.border),i["border-spacing"]=Ci(t.cellspacing)):(o.border=t.border,o.cellpadding=t.cellpadding,o.cellspacing=t.cellspacing),Ai(e)&&n.children)for(var u=0;u<n.children.length;u++)ta(r,n.children[u],{"border-width":Ci(t.border),padding:Ci(t.cellpadding)}),Di(e)&&ta(r,n.children[u],{"border-color":t.bordercolor});Di(e)&&(i["background-color"]=t.backgroundcolor,i["border-color"]=t.bordercolor,i["border-style"]=t.borderstyle),o.style=r.serializeStyle(Jc(Ri(e),i)),r.setAttribs(n,Jc(xi(e),o))}(t,r,u),(o=i.select("caption",r)[0])&&!u.caption&&i.remove(o),!o&&u.caption&&((o=i.create("caption")).innerHTML=Qc.ie?"\xa0":'<br data-mce-bogus="1"/>',r.insertBefore(o,r.firstChild)),""===u.align?kc(t,r):Ec(t,r,u.align),t.focus(),t.addVisual()})}function Gi(n){return function(e){return Me.from(e.dom.getParent(e.selection.getStart(),n)).map(on.fromDom)}}function Yi(e){function n(){e.stopPropagation()}function t(){e.preventDefault()}var r=on.fromDom(e.target),o=O(t,n);return function(e,n,t,r,o,i,u){return{target:D(e),x:D(n),y:D(t),stop:r,prevent:o,kill:i,raw:D(u)}}(r,e.clientX,e.clientY,n,t,o,e)}function Ki(e,n,t,r,o){var i=function(n,t){return function(e){n(e)&&t(Yi(e))}}(t,r);return e.dom().addEventListener(n,i,o),{unbind:b(da,e,n,i,o)}}function Xi(e,n,t){return function(e,n,t,r){return Ki(e,n,t,r,!1)}(e,n,ma,t)}var $i,Ji=function(e){var n=st(e);return gt.generate(n).grid()},Qi=function(o,e){function n(e,n){0<e.length?function(e,n){var t=Zn(o,n).getOrThunk(function(){var e=on.fromTag(n,he(o).dom());return Wn(o,e),e});Ee(t);var r=g(e,function(e){e.isNew()&&i.push(e.element());var n=e.element();return Ee(n),p(e.cells(),function(e){e.isNew()&&u.push(e.element()),yo(e.element(),"colspan",e.colspan(),1),yo(e.element(),"rowspan",e.rowspan(),1),Wn(n,e.element())}),n});Ae(t,r)}(e,n):function(e){Zn(o,e).each(_n)}(n)}var i=[],u=[],t=[],r=[],c=[];return p(e,function(e){switch(e.section()){case"thead":t.push(e);break;case"tbody":r.push(e);break;case"tfoot":c.push(e)}}),n(t,"thead"),n(r,"tbody"),n(c,"tfoot"),{newRows:D(i),newCells:D(u)}},Zi=function(e){return g(e,function(e){var t=Tt(e.element());return p(e.cells(),function(e){var n=Ot(e.element());yo(n,"colspan",e.colspan(),1),yo(n,"rowspan",e.rowspan(),1),Wn(t,n)}),t})},eu=function(e,n,t){var r=e();return y(r,n).orThunk(function(){return Me.from(r[0]).orThunk(t)}).map(function(e){return e.element()})},nu=function(t){var e=t.grid(),n=Ao(0,e.columns()),r=Ao(0,e.rows());return g(n,function(n){return eu(function(){return x(r,function(e){return gt.getAt(t,e,n).filter(function(e){return e.column()===n}).fold(D([]),function(e){return[e]})})},function(e){return 1===e.colspan()},function(){return gt.getAt(t,0,n)})})},tu=function(t){var e=t.grid(),n=Ao(0,e.rows()),r=Ao(0,e.columns());return g(n,function(n){return eu(function(){return x(r,function(e){return gt.getAt(t,n,e).filter(function(e){return e.row()===n}).fold(D([]),function(e){return[e]})})},function(e){return 1===e.rowspan()},function(){return gt.getAt(t,n,0)})})},ru={resolve:No("ephox-snooker").resolve},ou=function(e,n,t,r,o){var i=on.fromTag("div");return re(i,{position:"absolute",left:n-r/2+"px",top:t+"px",height:o+"px",width:r+"px"}),q(i,{"data-column":e,role:"presentation"}),i},iu=function(e,n,t,r,o){var i=on.fromTag("div");return re(i,{position:"absolute",left:n+"px",top:t-o/2+"px",height:o+"px",width:r+"px"}),q(i,{"data-row":e,role:"presentation"}),i},uu=ru.resolve("resizer-bar"),cu=ru.resolve("resizer-rows"),au=ru.resolve("resizer-cols"),lu=function(e,n,t,r){ko(e);var o=st(n),i=gt.generate(o),u=tu(i),c=nu(i);Bo(e,n,u,c,t,r)},fu=function(e){Po(e,function(e){te(e,"display","none")})},su=function(e){Po(e,function(e){te(e,"display","block")})},du=ko,mu=function(e){return Do(e,cu)},gu=function(e){return Do(e,au)},pu=function(e,n){return Gn(n,e.section())},hu={addCell:function(e,n,t){var r=e.cells(),o=r.slice(0,n),i=r.slice(n),u=o.concat([t]).concat(i);return pu(e,u)},setCells:pu,mutateCell:function(e,n,t){e.cells()[n]=t},getCell:Mo,getCellElement:function(e,n){return Mo(e,n).element()},mapCells:function(e,n){var t=e.cells(),r=g(t,n);return Gn(r,e.section())},cellLength:function(e){return e.cells().length}},vu=function(e,n,t,r){var o=function(e,n){return e[n]}(e,n).cells().slice(t),i=Wo(o,r),u=function(e,n){return g(e,function(e){return hu.getCell(e,n)})}(e,t).slice(n),c=Wo(u,r);return{colspan:D(i),rowspan:D(c)}},bu=function(o,i){var u=g(o,function(e,n){return g(e.cells(),function(e,n){return!1})});return g(o,function(e,r){var n=x(e.cells(),function(e,n){if(!1!==u[r][n])return[];var t=vu(o,r,n,i);return function(e,n,t,r){for(var o=e;o<e+t;o++)for(var i=n;i<n+r;i++)u[o][i]=!0}(r,n,t.rowspan(),t.colspan()),[Hn(e.element(),t.rowspan(),t.colspan(),e.isNew())]});return Yn(n,e.section())})},wu=function(e,n,t){for(var r=[],o=0;o<e.grid().rows();o++){for(var i=[],u=0;u<e.grid().columns();u++){var c=gt.getAt(e,o,u).map(function(e){return qn(e.element(),t)}).getOrThunk(function(){return qn(n.gap(),!0)});i.push(c)}var a=Gn(i,e.all()[o].section());r.push(a)}return r},yu=function(t){return{is:function(e){return t===e},isValue:i,isError:s,getOr:D(t),getOrThunk:D(t),getOrDie:D(t),or:function(e){return yu(t)},orThunk:function(e){return yu(t)},fold:function(e,n){return n(t)},map:function(e){return yu(e(t))},mapError:function(e){return yu(t)},each:function(e){e(t)},bind:function(e){return e(t)},exists:function(e){return e(t)},forall:function(e){return e(t)},toOption:function(){return Me.some(t)}}},Cu=function(t){return{is:s,isValue:s,isError:i,getOr:o,getOrThunk:function(e){return e()},getOrDie:function(){return function(e){return function(){throw new Error(e)}}(String(t))()},or:function(e){return e},orThunk:function(e){return e()},fold:function(e,n){return e(t)},map:function(e){return Cu(t)},mapError:function(e){return Cu(e(t))},each:T,bind:function(e){return Cu(t)},exists:s,forall:i,toOption:Me.none}},Su={value:yu,error:Cu,fromOption:function(e,n){return e.fold(function(){return Cu(n)},yu)}},xu=function(e,n,t){if(e.row()>=n.length||e.column()>hu.cellLength(n[0]))return Su.error("invalid start address out of table bounds, row: "+e.row()+", column: "+e.column());var r=n.slice(e.row()),o=r[0].cells().slice(e.column()),i=hu.cellLength(t[0]),u=t.length;return Su.value({rowDelta:D(r.length-u),colDelta:D(o.length-i)})},Ru=function(e,n){var t=hu.cellLength(e[0]),r=hu.cellLength(n[0]);return{rowDelta:D(0),colDelta:D(t-r)}},Tu=function(e,n,t){var r=n.colDelta()<0?Go:o;return(n.rowDelta()<0?Vo:o)(r(e,Math.abs(n.colDelta()),t),Math.abs(n.rowDelta()),t)},Ou=function(e,n,t,r){if(0===e.length)return e;for(var o=n.startRow();o<=n.finishRow();o++)for(var i=n.startCol();i<=n.finishCol();i++)hu.mutateCell(e[o],i,qn(r(),!1));return e},Du=function(e,n,t,r){for(var o=!0,i=0;i<e.length;i++)for(var u=0;u<hu.cellLength(e[0]);u++){var c=t(hu.getCellElement(e[i],u),n);!0===c&&!1===o?hu.mutateCell(e[i],u,qn(r(),!0)):!0===c&&(o=!1)}return e},Au=function(i,t,u,c){if(0<t&&t<i.length){var e=function(e,t){return w(e,function(e,n){return m(e,function(e){return t(e.element(),n.element())})?e:e.concat([n])},[])}(i[t-1].cells(),u);p(e,function(r){for(var o=Me.none(),e=function(t){for(var e=function(n){var e=i[t].cells()[n];u(e.element(),r.element())&&(o.isNone()&&(o=Me.some(c())),o.each(function(e){hu.mutateCell(i[t],n,qn(e,!0))}))},n=0;n<hu.cellLength(i[0]);n++)e(n)},n=t;n<i.length;n++)e(n)})}return i},Eu=function(t,r,o,i,u){return xu(t,r,o).map(function(e){var n=Tu(r,e,i);return function(e,n,t,r,o){for(var i,u,c,a,l,f=e.row(),s=e.column(),d=f+t.length,m=s+hu.cellLength(t[0]),g=f;g<d;g++)for(var p=s;p<m;p++){i=n,u=g,c=p,l=a=void 0,a=b(o,hu.getCell(i[u],c).element()),l=i[u],1<i.length&&1<hu.cellLength(l)&&(0<c&&a(hu.getCellElement(l,c-1))||c<l.cells().length-1&&a(hu.getCellElement(l,c+1))||0<u&&a(hu.getCellElement(i[u-1],c))||u<i.length-1&&a(hu.getCellElement(i[u+1],c)))&&Du(n,hu.getCellElement(n[g],p),o,r.cell);var h=hu.getCellElement(t[g-f],p-s),v=r.replace(h);hu.mutateCell(n[g],p,qn(v,!0))}return n}(t,n,o,i,u)})},Nu=function(e,n,t,r,o){Au(n,e,o,r.cell);var i=Ru(t,n),u=Tu(t,i,r),c=Ru(n,u),a=Tu(n,c,r);return a.slice(0,e).concat(u).concat(a.slice(e,a.length))},ku=function(t,r,e,o,i){var n=t.slice(0,r),u=t.slice(r),c=hu.mapCells(t[e],function(e,n){return 0<r&&r<t.length&&o(hu.getCellElement(t[r-1],n),hu.getCellElement(t[r],n))?hu.getCell(t[r],n):qn(i(e.element(),o),!0)});return n.concat([c]).concat(u)},Iu=function(e,t,r,o,i){return g(e,function(e){var n=0<t&&t<hu.cellLength(e)&&o(hu.getCellElement(e,t-1),hu.getCellElement(e,t))?hu.getCell(e,t):qn(i(hu.getCellElement(e,r),o),!0);return hu.addCell(e,t,n)})},Bu=function(e,r,o,i,u){var c=o+1;return g(e,function(e,n){var t=n===r?qn(u(hu.getCellElement(e,o),i),!0):hu.getCell(e,o);return hu.addCell(e,c,t)})},Pu=function(e,n,t,r,o){var i=n+1,u=e.slice(0,i),c=e.slice(i),a=hu.mapCells(e[n],function(e,n){return n===t?qn(o(e.element(),r),!0):e});return u.concat([a]).concat(c)},Mu=function(e,n,t){return e.slice(0,n).concat(e.slice(t+1))},Wu=function(e,t,r){var n=g(e,function(e){var n=e.cells().slice(0,t).concat(e.cells().slice(r+1));return Gn(n,e.section())});return h(n,function(e){return 0<e.cells().length})},_u=function(t,r,o,e){var n=x(t,function(e,n){return Ko(t,n,r,o)||Xo(e,r,o)?[]:[hu.getCell(e,r)]});return Yo(t,n,o,e)},Lu=function(t,r,o,e){var i=t[r],n=x(i.cells(),function(e,n){return Ko(t,r,n,o)||Xo(i,n,o)?[]:[e]});return Yo(t,n,o,e)},ju=xr([{none:[]},{only:["index"]},{left:["index","next"]},{middle:["prev","index","next"]},{right:["prev","index"]}]),zu=fn({},ju),Hu=function(e,n,i,u){function c(e){return g(e,D(0))}function r(e,n){if(0<=i){var t=Math.max(u.minCellWidth(),a[n]-i);return c(a.slice(0,e)).concat([i,t-a[n]]).concat(c(a.slice(n+1)))}var r=Math.max(u.minCellWidth(),a[e]+i),o=a[e]-r;return c(a.slice(0,e)).concat([r-a[e],o]).concat(c(a.slice(n+1)))}var a=e.slice(0),t=function(e,n){return 0===e.length?zu.none():1===e.length?zu.only(0):0===n?zu.left(0,1):n===e.length-1?zu.right(n-1,n):0<n&&n<e.length-1?zu.middle(n-1,n,n+1):zu.none()}(e,n),o=D(c(a)),l=r;return t.fold(o,function(e){return u.singleColumnWidth(a[e],i)},l,function(e,n,t){return r(n,t)},function(e,n){if(0<=i)return c(a.slice(0,n)).concat([i]);var t=Math.max(u.minCellWidth(),a[n]+i);return c(a.slice(0,n)).concat([t-a[n]])})},Fu={hasColspan:function(e){return $o(e,"colspan")},hasRowspan:function(e){return $o(e,"rowspan")},minWidth:D(10),minHeight:D(10),getInt:function(e,n){return parseInt(oe(e,n),10)}},Uu={getRawWidths:function(e,n,t){return ei(e,n,Qo,ni,t)},getPixelWidths:function(e,n,t){return ei(e,n,co.getPixelWidth,function(e){return e.getOrThunk(t.minCellWidth)},t)},getPercentageWidths:function(e,n,t){return ei(e,n,co.getPercentageWidth,function(e){return e.fold(function(){return t.minCellWidth()},function(e){return e/t.pixelWidth()*100})},t)},getPixelHeights:function(e,n){return ti(e,n,co.getHeight,function(e){return e.getOrThunk(Fu.minHeight)})},getRawHeights:function(e,n){return ti(e,n,Zo,ni)}},qu=function(e,t){var n=gt.justCells(e);return g(n,function(e){var n=ri(e.column(),e.column()+e.colspan(),t);return{element:e.element,width:D(n),colspan:e.colspan}})},Vu=function(e,t){var n=gt.justCells(e);return g(n,function(e){var n=ri(e.row(),e.row()+e.rowspan(),t);return{element:e.element,height:D(n),rowspan:e.rowspan}})},Gu=function(e,t){return g(e.all(),function(e,n){return{element:e.element,height:D(t[n])}})},Yu=function(n){return co.getRawWidth(n).fold(function(){var e=Mr(n);return oi(e)},function(e){return ii(n,e)})},Ku=function(e,n,t,r){var o=Yu(e),i=o.getCellDelta(n),u=ci(e),c=o.getWidths(u,r,o),a=Hu(c,t,i,o),l=g(a,function(e,n){return e+c[n]}),f=qu(u,l);p(f,function(e){o.setElementWidth(e.element(),e.width())}),t===u.grid().columns()-1&&o.setTableWidth(e,l,i)},Xu=function(e,t,r,n){var o=ci(e),i=Uu.getPixelHeights(o,n),u=g(i,function(e,n){return r===n?Math.max(t+e,Fu.minHeight()):e}),c=Vu(o,u),a=Gu(o,u);p(a,function(e){co.setHeight(e.element(),e.height())}),p(c,function(e){co.setHeight(e.element(),e.height())});var l=function(e){return v(e,function(e,n){return e+n},0)}(u);co.setHeight(e,l)},$u=function(e,n,t){var r=Yu(e),o=ui(n),i=r.getWidths(o,t,r),u=qu(o,i);p(u,function(e){r.setElementWidth(e.element(),e.width())}),0<u.length&&r.setTableWidth(e,i,r.getCellDelta(0))},Ju=function(r,o,i){if(0===o.length)throw new Error("You must specify at least one required field.");return _("required",o),L(o),function(n){var t=Ve(n);A(o,function(e){return l(t,e)})||M(o,t),r(o,t);var e=h(o,function(e){return!i.validate(n[e],e)});return 0<e.length&&function(e,n){throw new Error("All values need to be of type: "+n+". Keys ("+P(e).join(", ")+") were not.")}(e,i.label),n}},Qu=li(["cell","row","replace","gap"]),Zu=function(n,t){void 0===t&&(t=fi),Qu(n);function r(e){return function(e){return n.cell(e)}(t(e))}function o(e){var n=r(e);return i.get().isNone()&&i.set(Me.some(n)),u=Me.some({item:e,replacement:n}),n}var i=R(Me.none()),u=Me.none();return{getOrInit:function(n,t){return u.fold(function(){return o(n)},function(e){return t(n,e.item)?e.replacement:o(n)})},cursor:i.get}},ec=function(c,a){return function(r){var o=R(Me.none());Qu(r);function i(e){var n={scope:c},t=r.replace(e,a,n);return u.push({item:e,sub:t}),o.get().isNone()&&o.set(Me.some(t)),t}var u=[];return{replaceOrInit:function(n,t){return function(n,t){return y(u,function(e){return t(e.item,n)})}(n,t).fold(function(){return i(n)},function(e){return t(n,e.item)?e.sub:i(n)})},cursor:o.get}}},nc=function(t){Qu(t);var e=R(Me.none());return{combine:function(n){return e.get().isNone()&&e.set(Me.some(n)),function(){var e=t.cell({element:D(n),colspan:D(1),rowspan:D(1)});return ue(e,"width"),ue(n,"width"),e}},cursor:e.get}},tc=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","table","thead","tfoot","tbody","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"],rc=si,oc=function(e,n){var t=e.property().name(n);return l(["ol","ul"],t)},ic=di,uc=Jt(),cc=function(e){return rc(uc,e)},ac=function(e){return oc(uc,e)},lc=function(e){return ic(uc,e)},fc=function(e){function o(e){return"br"===en(e)}function t(r){return xt(r).bind(function(n){var t=function(e){return ye(e).map(function(e){return!!cc(e)||!!lc(e)&&"img"!==en(e)}).getOr(!1)}(n);return ve(n).map(function(e){return!0===t||function(e){return"li"===en(e)||Jn(e,ac).isSome()}(e)||o(n)||cc(e)&&!Bn(r,e)?[]:[on.fromTag("br")]})}).getOr([])}var n,r=0===(n=x(e,function(e){var n=Ce(e);return function(e){return A(e,function(e){return o(e)||tn(e)&&0===vt(e).trim().length})}(n)?[]:n.concat(t(e))})).length?[on.fromTag("br")]:n;Ee(e[0]),Ae(e[0],r)},sc=B("grid","cursor"),dc=function(e,n,t){return Me.from(e[n]).bind(function(e){return Me.from(e.cells()[t]).bind(function(e){return Me.from(e.element())})})},mc=$u,gc={insertRowBefore:zo(function(e,n,t,r){var o=n.row(),i=n.row(),u=ku(e,i,o,t,r.getOrInit);return pi(u,i,n.column())},Ho,T,T,Zu),insertRowsBefore:zo(function(e,n,t,r){var o=n[0].row(),i=n[0].row(),u=hi(n),c=w(u,function(e,n){return ku(e,i,o,t,r.getOrInit)},e);return pi(c,i,n[0].column())},Uo,T,T,Zu),insertRowAfter:zo(function(e,n,t,r){var o=n.row(),i=n.row()+n.rowspan(),u=ku(e,i,o,t,r.getOrInit);return pi(u,i,n.column())},Ho,T,T,Zu),insertRowsAfter:zo(function(e,n,t,r){var o=hi(n),i=o[o.length-1].row(),u=o[o.length-1].row()+o[o.length-1].rowspan(),c=w(o,function(e,n){return ku(e,u,i,t,r.getOrInit)},e);return pi(c,u,n[0].column())},Uo,T,T,Zu),insertColumnBefore:zo(function(e,n,t,r){var o=n.column(),i=n.column(),u=Iu(e,i,o,t,r.getOrInit);return pi(u,n.row(),i)},Ho,mc,T,Zu),insertColumnsBefore:zo(function(e,n,t,r){var o=vi(n),i=o[0].column(),u=o[0].column(),c=w(o,function(e,n){return Iu(e,u,i,t,r.getOrInit)},e);return pi(c,n[0].row(),u)},Uo,mc,T,Zu),insertColumnAfter:zo(function(e,n,t,r){var o=n.column(),i=n.column()+n.colspan(),u=Iu(e,i,o,t,r.getOrInit);return pi(u,n.row(),i)},Ho,mc,T,Zu),insertColumnsAfter:zo(function(e,n,t,r){var o=n[n.length-1].column(),i=n[n.length-1].column()+n[n.length-1].colspan(),u=vi(n),c=w(u,function(e,n){return Iu(e,i,o,t,r.getOrInit)},e);return pi(c,n[0].row(),i)},Uo,mc,T,Zu),splitCellIntoColumns:zo(function(e,n,t,r){var o=Bu(e,n.row(),n.column(),t,r.getOrInit);return pi(o,n.row(),n.column())},Ho,mc,T,Zu),splitCellIntoRows:zo(function(e,n,t,r){var o=Pu(e,n.row(),n.column(),t,r.getOrInit);return pi(o,n.row(),n.column())},Ho,T,T,Zu),eraseColumns:zo(function(e,n,t,r){var o=vi(n),i=Wu(e,o[0].column(),o[o.length-1].column()),u=gi(i,n[0].row(),n[0].column());return sc(i,u)},Uo,mc,mi,Zu),eraseRows:zo(function(e,n,t,r){var o=hi(n),i=Mu(e,o[0].row(),o[o.length-1].row()),u=gi(i,n[0].row(),n[0].column());return sc(i,u)},Uo,T,mi,Zu),makeColumnHeader:zo(function(e,n,t,r){var o=_u(e,n.column(),t,r.replaceOrInit);return pi(o,n.row(),n.column())},Ho,T,T,ec("row","th")),unmakeColumnHeader:zo(function(e,n,t,r){var o=_u(e,n.column(),t,r.replaceOrInit);return pi(o,n.row(),n.column())},Ho,T,T,ec(null,"td")),makeRowHeader:zo(function(e,n,t,r){var o=Lu(e,n.row(),t,r.replaceOrInit);return pi(o,n.row(),n.column())},Ho,T,T,ec("col","th")),unmakeRowHeader:zo(function(e,n,t,r){var o=Lu(e,n.row(),t,r.replaceOrInit);return pi(o,n.row(),n.column())},Ho,T,T,ec(null,"td")),mergeCells:zo(function(e,n,t,r){var o=n.cells();fc(o);var i=Ou(e,n.bounds(),t,D(o[0]));return sc(i,Me.from(o[0]))},function(e,n){return n.mergable()},T,T,nc),unmergeCells:zo(function(e,n,t,r){var o=v(n,function(e,n){return Du(e,n,t,r.combine(n))},e);return sc(o,Me.from(n[0]))},function(e,n){return n.unmergable()},mc,T,nc),pasteCells:zo(function(e,t,n,r){var o,i,u,c,a=(o=t.clipboard(),i=t.generators(),u=st(o),c=gt.generate(u),wu(c,i,!0)),l=jn(t.row(),t.column());return Eu(l,e,a,t.generators(),n).fold(function(){return sc(e,Me.some(t.element()))},function(e){var n=gi(e,t.row(),t.column());return sc(e,n)})},function(n,t){return ft.cell(t.element()).bind(function(e){return jo(n,e).map(function(e){return fn(fn({},e),{generators:t.generators,clipboard:t.clipboard})})})},mc,T,Zu),pasteRowsBefore:zo(function(e,n,t,r){var o=e[n.cells[0].row()],i=n.cells[0].row(),u=bi(n.clipboard(),n.generators(),o),c=Nu(i,e,u,n.generators(),t),a=gi(c,n.cells[0].row(),n.cells[0].column());return sc(c,a)},Fo,T,T,Zu),pasteRowsAfter:zo(function(e,n,t,r){var o=e[n.cells[0].row()],i=n.cells[n.cells.length-1].row()+n.cells[n.cells.length-1].rowspan(),u=bi(n.clipboard(),n.generators(),o),c=Nu(i,e,u,n.generators(),t),a=gi(c,n.cells[0].row(),n.cells[0].column());return sc(c,a)},Fo,T,T,Zu)},pc=function(e){return on.fromDom(e.getBody())},hc=function(n){return function(e){return Bn(e,pc(n))}},vc={isRtl:D(!1)},bc={isRtl:D(!0)},wc={directionAt:function(e){return"rtl"===function(e){return"rtl"===oe(e,"direction")?"rtl":"ltr"}(e)?bc:vc}},yc={"border-collapse":"collapse",width:"100%"},Cc={border:"1"},Sc=function(e){return e.getParam("table_tab_navigation",!0,"boolean")},xc=function(e){var n=e.getParam("table_clone_elements");return _e(n)?Me.some(n.split(/[ ,]/)):Array.isArray(n)?Me.some(n):Me.none()},Rc=function(e,n,t,r,o){e.fire("TableSelectionChange",{cells:n,start:t,finish:r,otherCells:o})},Tc=function(e){e.fire("TableSelectionClear")},Oc=function(f,e){function t(e){return"table"===en(pc(e))}function n(u,c,a,l){return function(e,n){Si(e);var t=l(),r=on.fromDom(f.getDoc()),o=vo(wc.directionAt),i=_t.cellOperations(a,r,s);return c(e)?u(t,e,n,i,o).bind(function(e){return p(e.newRows(),function(e){ki(f,e.dom())}),p(e.newCells(),function(e){Ii(f,e.dom())}),e.cursor().map(function(e){var n=f.dom.createRng();return n.setStart(e.dom(),0),n.setEnd(e.dom(),0),n})}):Me.none()}}var s=xc(f);return{deleteRow:n(gc.eraseRows,function(e){var n=Ji(e);return!1===t(f)||1<n.rows()},T,e),deleteColumn:n(gc.eraseColumns,function(e){var n=Ji(e);return!1===t(f)||1<n.columns()},T,e),insertRowsBefore:n(gc.insertRowsBefore,i,T,e),insertRowsAfter:n(gc.insertRowsAfter,i,T,e),insertColumnsBefore:n(gc.insertColumnsBefore,i,ao,e),insertColumnsAfter:n(gc.insertColumnsAfter,i,ao,e),mergeCells:n(gc.mergeCells,i,T,e),unmergeCells:n(gc.unmergeCells,i,T,e),pasteRowsBefore:n(gc.pasteRowsBefore,i,T,e),pasteRowsAfter:n(gc.pasteRowsAfter,i,T,e),pasteCells:n(gc.pasteCells,i,T,e)}},Dc=function(e,n,r){var t=st(e),o=gt.generate(t);return Uo(o,n).map(function(e){var n=wu(o,r,!1).slice(e[0].row(),e[e.length-1].row()+e[e.length-1].rowspan()),t=Lo(n,r);return Zi(t)})},Ac=tinymce.util.Tools.resolve("tinymce.util.Tools"),Ec=function(e,n,t){t&&e.formatter.apply("align"+t,{},n)},Nc=function(e,n,t){t&&e.formatter.apply("valign"+t,{},n)},kc=function(n,t){Ac.each("left center right".split(" "),function(e){n.formatter.remove("align"+e,{},t)})},Ic=function(n,t){Ac.each("top middle bottom".split(" "),function(e){n.formatter.remove("valign"+e,{},t)})},Bc=function(o,e,i){var n;return n=function(e,n){for(var t=0;t<n.length;t++){var r=o.getStyle(n[t],i);if(void 0===e&&(e=r),e!==r)return""}return e}(n,o.select("td,th",e))},Pc=b(Wi,"left center right"),Mc=b(Wi,"top middle bottom"),Wc=function(e,r,n){var o=function(e,t){return t=t||[],Ac.each(e,function(e){var n={text:e.text||e.title};e.menu?n.menu=o(e.menu):(n.value=e.value,r&&r(n)),t.push(n)}),t};return o(e,n||[])},_c=function(e){var o=e[0],n=e.slice(1),t=Ve(o);return p(n,function(e){p(t,function(r){N(e,function(e,n){var t=o[r];""!==t&&r===n&&t!==e&&(o[r]="")})})}),o},Lc=function(e){var n=[{name:"borderstyle",type:"selectbox",label:"Border style",items:[{text:"Select...",value:""},{text:"Solid",value:"solid"},{text:"Dotted",value:"dotted"},{text:"Dashed",value:"dashed"},{text:"Double",value:"double"},{text:"Groove",value:"groove"},{text:"Ridge",value:"ridge"},{text:"Inset",value:"inset"},{text:"Outset",value:"outset"},{text:"None",value:"none"},{text:"Hidden",value:"hidden"}]},{name:"bordercolor",type:"colorinput",label:"Border color"},{name:"backgroundcolor",type:"colorinput",label:"Background color"}];return{title:"Advanced",name:"advanced",items:"cell"===e?[{name:"borderwidth",type:"input",label:"Border width"}].concat(n):n}},jc=function(e,n,t){var r,o,i,u=e.dom;return fn(fn({width:u.getStyle(n,"width")||u.getAttrib(n,"width"),height:u.getStyle(n,"height")||u.getAttrib(n,"height"),cellspacing:u.getStyle(n,"border-spacing")||u.getAttrib(n,"cellspacing"),cellpadding:u.getAttrib(n,"cellpadding")||Bc(e.dom,n,"padding"),border:(r=u,o=n,i=ie(on.fromDom(o),"border-width"),Ai(e)&&i.isSome()?i.getOr(""):r.getAttrib(o,"border")||Bc(e.dom,o,"border-width")||Bc(e.dom,o,"border")),caption:!!u.select("caption",n)[0],"class":u.getAttrib(n,"class","")},Pc("align","align",e,n)),t?Mi(u,n):{})},zc=function(e,n,t){var r=e.dom;return fn(fn({height:r.getStyle(n,"height")||r.getAttrib(n,"height"),scope:r.getAttrib(n,"scope"),"class":r.getAttrib(n,"class",""),align:"",type:n.parentNode.nodeName.toLowerCase()},Pc("align","align",e,n)),t?Mi(r,n):{})},Hc=function(e,n,t){var r=e.dom;return fn(fn(fn({width:r.getStyle(n,"width")||r.getAttrib(n,"width"),height:r.getStyle(n,"height")||r.getAttrib(n,"height"),scope:r.getAttrib(n,"scope"),celltype:n.nodeName.toLowerCase(),"class":r.getAttrib(n,"class","")},Pc("align","halign",e,n)),Mc("valign","valign",e,n)),t?Mi(r,n):{})},Fc=function(e,n){var t,r,o,i,u=Ri(e),c=xi(e),a=e.dom,l=n?(t=a,r=I(u,"border-style").getOr(""),o=I(u,"border-color").getOr(""),i=I(u,"background-color").getOr(""),{borderstyle:r,bordercolor:f(o),backgroundcolor:f(i)}):{};function f(e){return J(e,"rgb")?t.toHex(e):e}var s,d,m;return fn(fn(fn(fn(fn(fn({},{height:"",width:"100%",cellspacing:"",cellpadding:"",caption:!1,"class":"",align:"",border:""}),u),c),l),(m=u["border-width"],Ai(e)&&m?{border:m}:I(c,"border").fold(function(){return{}},function(e){return{border:e}}))),(s=I(u,"border-spacing").or(I(c,"cellspacing")).fold(function(){return{}},function(e){return{cellspacing:e}}),d=I(u,"border-padding").or(I(c,"cellpadding")).fold(function(){return{}},function(e){return{cellpadding:e}}),fn(fn({},s),d)))},Uc=[{name:"width",type:"input",label:"Width"},{name:"height",type:"input",label:"Height"},{name:"celltype",type:"selectbox",label:"Cell type",items:[{text:"Cell",value:"td"},{text:"Header cell",value:"th"}]},{name:"scope",type:"selectbox",label:"Scope",items:[{text:"None",value:""},{text:"Row",value:"row"},{text:"Column",value:"col"},{text:"Row group",value:"rowgroup"},{text:"Column group",value:"colgroup"}]},{name:"halign",type:"selectbox",label:"H Align",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{name:"valign",type:"selectbox",label:"V Align",items:[{text:"None",value:""},{text:"Top",value:"top"},{text:"Middle",value:"middle"},{text:"Bottom",value:"bottom"}]}],qc=function(e){return function(n){var e=function(e){return e.getParam("table_cell_class_list",[],"array")}(n),t=Wc(e,function(e){e.value&&(e.textStyle=function(){return n.formatter.getCssText({block:"tr",classes:[e.value]})})});return 0<e.length?Me.some({name:"class",type:"selectbox",label:"Class",items:t}):Me.none()}(e).fold(function(){return Uc},function(e){return Uc.concat(e)})},Vc={normal:function(t,r){return{setAttrib:function(e,n){t.setAttrib(r,e,n)},setStyle:function(e,n){t.setStyle(r,e,n)}}},ifTruthy:function(t,r){return{setAttrib:function(e,n){n&&t.setAttrib(r,e,n)},setStyle:function(e,n){n&&t.setStyle(r,e,n)}}}},Gc=function(n){var e,t=[];if(t=n.dom.select("td[data-mce-selected],th[data-mce-selected]"),e=n.dom.getParent(n.selection.getStart(),"td,th"),!t.length&&e&&t.push(e),e=e||t[0]){var r=Ac.map(t,function(e){return Hc(n,e,Ti(n))}),o=_c(r),i={type:"tabpanel",tabs:[{title:"General",name:"general",items:qc(n)},Lc("cell")]},u={type:"panel",items:[{type:"grid",columns:2,items:qc(n)}]};n.windowManager.open({title:"Cell Properties",size:"normal",body:Ti(n)?i:u,buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:o,onSubmit:b(Hi,n,t)})}},Yc=[{type:"selectbox",name:"type",label:"Row type",items:[{text:"Header",value:"thead"},{text:"Body",value:"tbody"},{text:"Footer",value:"tfoot"}]},{type:"selectbox",name:"align",label:"Alignment",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{label:"Height",name:"height",type:"input"}],Kc=function(e){return function(n){var e=function(e){return e.getParam("table_row_class_list",[],"array")}(n),t=Wc(e,function(e){e.value&&(e.textStyle=function(){return n.formatter.getCssText({block:"tr",classes:[e.value]})})});return 0<e.length?Me.some({name:"class",type:"selectbox",label:"Class",items:t}):Me.none()}(e).fold(function(){return Yc},function(e){return Yc.concat(e)})},Xc=function(n){var e,t,r=n.dom,o=[];if((e=r.getParent(n.selection.getStart(),"table"))&&(t=r.getParent(n.selection.getStart(),"td,th"),Ac.each(e.rows,function(n){Ac.each(n.cells,function(e){if((r.getAttrib(e,"data-mce-selected")||e===t)&&o.indexOf(n)<0)return o.push(n),!1})}),o[0])){var i=Ac.map(o,function(e){return zc(n,e,Oi(n))}),u=_c(i),c={type:"tabpanel",tabs:[{title:"General",name:"general",items:Kc(n)},Lc("row")]},a={type:"panel",items:[{type:"grid",columns:2,items:Kc(n)}]};n.windowManager.open({title:"Row Properties",size:"normal",body:Oi(n)?c:a,buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:u,onSubmit:b(Fi,n,o,u)})}},$c=Object.prototype.hasOwnProperty,Jc=($i=function(e,n){return n},function(){for(var e=new Array(arguments.length),n=0;n<e.length;n++)e[n]=arguments[n];if(0===e.length)throw new Error("Can't merge zero objects");for(var t={},r=0;r<e.length;r++){var o=e[r];for(var i in o)$c.call(o,i)&&(t[i]=$i(t[i],o[i]))}return t}),Qc=tinymce.util.Tools.resolve("tinymce.Env"),Zc={styles:{"border-collapse":"collapse",width:"100%"},attributes:{border:"1"},percentages:!0},ea=function(n,e,t){var r=Ri(n),o={styles:r,attributes:xi(n),percentages:function(e){return _e(e)&&-1!==e.indexOf("%")}(r.width)&&!Ni(n)},i=Ui(t,e,0,0,o);U(i,"data-mce-id","__mce");var u=function(e){var n=on.fromTag("div"),t=on.fromDom(e.dom().cloneNode(!0));return Wn(n,t),function(e){return e.dom().innerHTML}(n)}(i);return n.insertContent(u),et(pc(n),'table[data-mce-id="__mce"]').map(function(e){return Ni(n)&&te(e,"width",oe(e,"width")),Y(e,"data-mce-id"),function(n,e){p(Be(e,"tr"),function(e){ki(n,e.dom()),p(Be(e,"th,td"),function(e){Ii(n,e.dom())})})}(n,e),function(e,n){et(n,"td,th").each(b(qi,e))}(n,e),e.dom()}).getOr(null)},na=function(n,e,t){var r=t?[{type:"input",name:"cols",label:"Cols",inputMode:"numeric"},{type:"input",name:"rows",label:"Rows",inputMode:"numeric"}]:[],o=function(e){return e.getParam("table_appearance_options",!0,"boolean")}(n)?[{type:"input",name:"cellspacing",label:"Cell spacing",inputMode:"numeric"},{type:"input",name:"cellpadding",label:"Cell padding",inputMode:"numeric"},{type:"input",name:"border",label:"Border width"},{type:"label",label:"Caption",items:[{type:"checkbox",name:"caption",label:"Show caption"}]}]:[],i=e?[{type:"selectbox",name:"class",label:"Class",items:Wc(Ei(n),function(e){e.value&&(e.textStyle=function(){return n.formatter.getCssText({block:"table",classes:[e.value]})})})}]:[];return r.concat([{type:"input",name:"width",label:"Width"},{type:"input",name:"height",label:"Height"}]).concat(o).concat([{type:"selectbox",name:"align",label:"Alignment",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]}]).concat(i)},ta=function(e,n,t,r){if("TD"===n.tagName||"TH"===n.tagName)_e(t)?e.setStyle(n,t,r):e.setStyle(n,t);else if(n.children)for(var o=0;o<n.children.length;o++)ta(e,n.children[o],t,r)},ra=function(e,n){var t,r=e.dom,o=Fc(e,Di(e));!1===n?(t=r.getParent(e.selection.getStart(),"table"))?o=jc(e,t,Di(e)):Di(e)&&(o.borderstyle="",o.bordercolor="",o.backgroundcolor=""):(o.cols="1",o.rows="1",Di(e)&&(o.borderstyle="",o.bordercolor="",o.backgroundcolor=""));var i=0<Ei(e).length;i&&o["class"]&&(o["class"]=o["class"].replace(/\s*mce\-item\-table\s*/g,""));var u={type:"grid",columns:2,items:na(e,i,n)},c=Di(e)?{type:"tabpanel",tabs:[{title:"General",name:"general",items:[u]},Lc("table")]}:{type:"panel",items:[u]};e.windowManager.open({title:"Table Properties",size:"normal",body:c,onSubmit:b(Vi,e,t),buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:o})},oa=Gi("th,td"),ia=Gi("th,td,caption"),ua=Ac.each,ca={registerCommands:function(c,n,a,l,t){function f(e){return ft.table(e,s)}function i(e){return{width:wi(e.dom()),height:wi(e.dom())}}function r(o){oa(c).each(function(r){f(r).each(function(n){var e=Nr.forMenu(l,n,r),t=i(n);o(n,e).each(function(e){!function(e,n,t){var r=i(t);n.width===r.width&&n.height===r.height||(Bi(e,t.dom(),n.width,n.height),Pi(e,t.dom(),r.width,r.height))}(c,t,n),c.selection.setRng(e),c.focus(),a.clear(n),Si(n)})})})}function o(e){return oa(c).map(function(o){return f(o).bind(function(e){var n=on.fromDom(c.getDoc()),t=Nr.forMenu(l,e,o),r=_t.cellOperations(T,n,Me.none());return Dc(e,t,r)})})}function u(u){t.get().each(function(e){var i=g(e,function(e){return Ot(e)});oa(c).each(function(o){f(o).each(function(n){var e=on.fromDom(c.getDoc()),t=_t.paste(e),r=Nr.pasteRows(l,n,o,i,t);u(n,r).each(function(e){c.selection.setRng(e),c.focus(),a.clear(n)})})})})}var s=hc(c);ua({mceTableSplitCells:function(){r(n.unmergeCells)},mceTableMergeCells:function(){r(n.mergeCells)},mceTableInsertRowBefore:function(){r(n.insertRowsBefore)},mceTableInsertRowAfter:function(){r(n.insertRowsAfter)},mceTableInsertColBefore:function(){r(n.insertColumnsBefore)},mceTableInsertColAfter:function(){r(n.insertColumnsAfter)},mceTableDeleteCol:function(){r(n.deleteColumn)},mceTableDeleteRow:function(){r(n.deleteRow)},mceTableCutRow:function(e){o().each(function(e){t.set(e),r(n.deleteRow)})},mceTableCopyRow:function(e){o().each(function(e){t.set(e)})},mceTablePasteRowBefore:function(e){u(n.pasteRowsBefore)},mceTablePasteRowAfter:function(e){u(n.pasteRowsAfter)},mceTableDelete:function(){ia(c).each(function(e){ft.table(e,s).filter(d(s)).each(function(e){var n=on.fromText("");if(Re(e,n),_n(e),c.dom.isEmpty(c.getBody()))c.setContent(""),c.selection.setCursorLocation();else{var t=c.dom.createRng();t.setStart(n.dom(),0),t.setEnd(n.dom(),0),c.selection.setRng(t),c.nodeChanged()}})})}},function(e,n){c.addCommand(n,e)}),ua({mceInsertTable:b(ra,c,!0),mceTableProps:b(ra,c,!1),mceTableRowProps:b(Xc,c),mceTableCellProps:b(Gc,c)},function(e,n){c.addCommand(n,function(){e()})})}},aa=function(e){var n=Me.from(e.dom().documentElement).map(on.fromDom).getOr(e);return{parent:D(n),view:D(e),origin:D(fo(0,0))}},la=function(e,n){return{parent:D(n),view:D(e),origin:D(fo(0,0))}},fa=function(e){var r=B.apply(null,e),o=[];return{bind:function(e){if(e===undefined)throw new Error("Event bind error: undefined handler");o.push(e)},unbind:function(n){o=h(o,function(e){return e!==n})},trigger:function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];var t=r.apply(null,e);p(o,function(e){e(t)})}}},sa={create:function(e){return{registry:k(e,function(e){return{bind:e.bind,unbind:e.unbind}}),trigger:k(e,function(e){return e.trigger})}}},da=function(e,n,t,r){e.dom().removeEventListener(n,t,r)},ma=D(!0),ga={resolve:No("ephox-dragster").resolve},pa=li(["compare","extract","mutate","sink"]),ha=li(["element","start","stop","destroy"]),va=li(["forceDrop","drop","move","delayDrop"]),ba=pa({compare:function(e,n){return fo(n.left()-e.left(),n.top()-e.top())},extract:function(e){return Me.some(fo(e.x(),e.y()))},sink:function(e,n){var t=function(e){var n=Jc({layerClass:ga.resolve("blocker")},e),t=on.fromTag("div");U(t,"role","presentation"),re(t,{position:"fixed",left:"0px",top:"0px",width:"100%",height:"100%"}),To(t,ga.resolve("blocker")),To(t,n.layerClass);return{element:function(){return t},destroy:function(){_n(t)}}}(n),r=Xi(t.element(),"mousedown",e.forceDrop),o=Xi(t.element(),"mouseup",e.drop),i=Xi(t.element(),"mousemove",e.move),u=Xi(t.element(),"mouseout",e.delayDrop);return ha({element:t.element,start:function(e){Wn(e,t.element())},stop:function(){_n(t.element())},destroy:function(){t.destroy(),o.unbind(),i.unbind(),u.unbind(),r.unbind()}})},mutate:function(e,n){e.mutate(n.left(),n.top())}});function wa(){var r=Me.none(),t=sa.create({move:fa(["info"])});return{onEvent:function(e,n){n.extract(e).each(function(e){(function(n,t){var e=r.map(function(e){return n.compare(e,t)});return r=Me.some(t),e})(n,e).each(function(e){t.trigger.move(e)})})},reset:function(){r=Me.none()},events:t.registry}}function ya(){var e=function r(){return{onEvent:T,reset:T}}(),n=wa(),t=e;return{on:function(){t.reset(),t=n},off:function(){t.reset(),t=e},isOn:function(){return t===n},onEvent:function(e,n){t.onEvent(e,n)},events:n.events}}function Ca(){var t=sa.create({drag:fa(["xDelta","yDelta","target"])}),r=Me.none(),e=function(){var t=sa.create({drag:fa(["xDelta","yDelta"])});return{mutate:function(e,n){t.trigger.drag(e,n)},events:t.registry}}();return e.events.drag.bind(function(n){r.each(function(e){t.trigger.drag(n.xDelta(),n.yDelta(),e)})}),{assign:function(e){r=Me.some(e)},get:function(){return r},mutate:e.mutate,events:t.registry}}function Sa(e){return"true"===V(e,"contenteditable")}function xa(o,n,i){function e(e,n){return Me.from(V(e,n))}var t=Ca(),r=bl(t,{}),u=Me.none();function c(e,n){return Fu.getInt(e,n)-parseInt(V(e,"data-initial-"+n),10)}function a(e,n){m.trigger.startAdjust(),t.assign(e),U(e,"data-initial-"+n,parseInt(oe(e,n),10)),To(e,wl),te(e,"opacity","0.2"),r.go(o.parent())}function l(e){return Bn(e,o.view())}function f(e){return nt(e,"table",l).filter(function(e){return function(e,n){return nt(e,"[contenteditable]",n)}(e,l).exists(Sa)})}t.events.drag.bind(function(t){e(t.target(),"data-row").each(function(e){var n=Fu.getInt(t.target(),"top");te(t.target(),"top",n+t.yDelta()+"px")}),e(t.target(),"data-column").each(function(e){var n=Fu.getInt(t.target(),"left");te(t.target(),"left",n+t.xDelta()+"px")})}),r.events.stop.bind(function(){t.get().each(function(r){u.each(function(t){e(r,"data-row").each(function(e){var n=c(r,"top");Y(r,"data-initial-top"),m.trigger.adjustHeight(t,n,parseInt(e,10))}),e(r,"data-column").each(function(e){var n=c(r,"left");Y(r,"data-initial-left"),m.trigger.adjustWidth(t,n,parseInt(e,10))}),lu(o,t,i,n)})})});var s=Xi(o.parent(),"mousedown",function(e){mu(e.target())&&a(e.target(),"top"),gu(e.target())&&a(e.target(),"left")}),d=Xi(o.view(),"mouseover",function(e){f(e.target()).fold(function(){ee(e.target())&&du(o)},function(e){u=Me.some(e),lu(o,e,i,n)})}),m=sa.create({adjustHeight:fa(["table","delta","row"]),adjustWidth:fa(["table","delta","column"]),startAdjust:fa([])});return{destroy:function(){s.unbind(),d.unbind(),r.destroy(),du(o)},refresh:function(e){lu(o,e,i,n)},on:r.on,off:r.off,hideBars:b(fu,o),showBars:b(su,o),events:m.registry}}function Ra(e,n){return wi(e.dom())/wi(n.dom())*100+"%"}function Ta(t,e){return ft.table(t,e).bind(function(e){var n=ft.cells(e);return C(n,function(e){return Bn(t,e)}).map(function(e){return{index:D(e),all:D(n)}})})}function Oa(e,n,t){var r=e.document.createRange();return function(t,e){e.fold(function(e){t.setStartBefore(e.dom())},function(e,n){t.setStart(e.dom(),n)},function(e){t.setStartAfter(e.dom())})}(r,n),function(t,e){e.fold(function(e){t.setEndBefore(e.dom())},function(e,n){t.setEnd(e.dom(),n)},function(e){t.setEndAfter(e.dom())})}(r,t),r}function Da(e,n,t,r,o){var i=e.document.createRange();return i.setStart(n.dom(),t),i.setEnd(r.dom(),o),i}function Aa(e){return{left:D(e.left),top:D(e.top),right:D(e.right),bottom:D(e.bottom),width:D(e.width),height:D(e.height)}}function Ea(e,n,t){return n(on.fromDom(t.startContainer),t.startOffset,on.fromDom(t.endContainer),t.endOffset)}function Na(e,n){return function(e,n){var t=n.ltr();return t.collapsed?n.rtl().filter(function(e){return!1===e.collapsed}).map(function(e){return Pl.rtl(on.fromDom(e.endContainer),e.endOffset,on.fromDom(e.startContainer),e.startOffset)}).getOrThunk(function(){return Ea(0,Pl.ltr,t)}):Ea(0,Pl.ltr,t)}(0,function(o,e){return e.match({domRange:function(e){return{ltr:D(e),rtl:Me.none}},relative:function(e,n){return{ltr:Z(function(){return Oa(o,e,n)}),rtl:Z(function(){return Me.some(Oa(o,n,e))})}},exact:function(e,n,t,r){return{ltr:Z(function(){return Da(o,e,n,t,r)}),rtl:Z(function(){return Me.some(Da(o,t,r,e,n))})}}})}(e,n))}function ka(e,n,t){return n>=e.left&&n<=e.right&&t>=e.top&&t<=e.bottom}function Ia(t,r,e,n,o){function i(e){var n=t.dom().createRange();return n.setStart(r.dom(),e),n.collapse(!0),n}var u=vt(r).length,c=function(e,n,t,r,o){if(0===o)return 0;if(n===r)return o-1;for(var i=r,u=1;u<o;u++){var c=e(u),a=Math.abs(n-c.left);if(t<=c.bottom){if(t<c.top||i<a)return u-1;i=a}}return 0}(function(e){return i(e).getBoundingClientRect()},e,n,o.right,u);return i(c)}function Ba(e,n){return n-e.left<e.right-n}function Pa(e,n,t){var r=e.dom().createRange();return r.selectNode(n.dom()),r.collapse(t),r}function Ma(n,e,t){var r=n.dom().createRange();r.selectNode(e.dom());var o=r.getBoundingClientRect(),i=Ba(o,t);return(!0===i?St:xt)(e).map(function(e){return Pa(n,e,i)})}function Wa(e,n,t){var r=n.dom().getBoundingClientRect(),o=Ba(r,t);return Me.some(Pa(e,n,o))}function _a(e,n,t,r){var o=e.dom().createRange();o.selectNode(n.dom());var i=o.getBoundingClientRect();return function(e,n,t,r){var o=e.dom().createRange();o.selectNode(n.dom());var i=o.getBoundingClientRect(),u=Math.max(i.left,Math.min(i.right,t)),c=Math.max(i.top,Math.min(i.bottom,r));return Wl(e,n,u,c)}(e,n,Math.max(i.left,Math.min(i.right,t)),Math.max(i.top,Math.min(i.bottom,r)))}function La(e,n){var t=en(e);return"input"===t?Nl.after(e):l(["br","img"],t)?0===n?Nl.before(e):Nl.after(e):Nl.on(e,n)}function ja(e,n){var t=e.fold(Nl.before,La,Nl.after),r=n.fold(Nl.before,La,Nl.after);return Il.relative(t,r)}function za(e,n,t,r){var o=La(e,n),i=La(t,r);return Il.relative(o,i)}function Ha(e,n,t,r){var o=function(e,n,t,r){var o=he(e).dom().createRange();return o.setStart(e.dom(),n),o.setEnd(t.dom(),r),o}(e,n,t,r),i=Bn(e,t)&&n===r;return o.collapsed&&!i}function Fa(e,n){Me.from(e.getSelection()).each(function(e){e.removeAllRanges(),e.addRange(n)})}function Ua(e,n,t,r,o){var i=Da(e,n,t,r,o);Fa(e,i)}function qa(u,e){return Na(u,e).match({ltr:function(e,n,t,r){Ua(u,e,n,t,r)},rtl:function(e,n,t,r){var o=u.getSelection();if(o.setBaseAndExtent)o.setBaseAndExtent(e.dom(),n,t.dom(),r);else if(o.extend)try{!function(e,n,t,r,o,i){n.collapse(t.dom(),r),n.extend(o.dom(),i)}(0,o,e,n,t,r)}catch(i){Ua(u,t,r,e,n)}else Ua(u,t,r,e,n)}})}function Va(e,n,t,r,o){var i=za(n,t,r,o);qa(e,i)}function Ga(e,n,t){var r=ja(n,t);qa(e,r)}function Ya(e){function n(e,n,t,r){return Da(o,e,n,t,r)}var o=Il.getWin(e).dom(),t=function(e){return e.match({domRange:function(e){var n=on.fromDom(e.startContainer),t=on.fromDom(e.endContainer);return za(n,e.startOffset,t,e.endOffset)},relative:ja,exact:za})}(e);return Na(o,t).match({ltr:n,rtl:n})}function Ka(e){var n=on.fromDom(e.anchorNode),t=on.fromDom(e.focusNode);return Ha(n,e.anchorOffset,t,e.focusOffset)?Me.some(Al.create(n,e.anchorOffset,t,e.focusOffset)):function(e){if(0<e.rangeCount){var n=e.getRangeAt(0),t=e.getRangeAt(e.rangeCount-1);return Me.some(Al.create(on.fromDom(n.startContainer),n.startOffset,on.fromDom(t.endContainer),t.endOffset))}return Me.none()}(e)}function Xa(e,n){var t=function(e,n){var t=e.document.createRange();return Bl(t,n),t}(e,n);Fa(e,t)}function $a(e){return function(e){return Me.from(e.getSelection()).filter(function(e){return 0<e.rangeCount}).bind(Ka)}(e).map(function(e){return Il.exact(e.start(),e.soffset(),e.finish(),e.foffset())})}function Ja(e,n){return function(e){var n=e.getClientRects(),t=0<n.length?n[0]:e.getBoundingClientRect();return 0<t.width||0<t.height?Me.some(t).map(Aa):Me.none()}(Ml(e,n))}function Qa(e,n,t){return function(e,n,t){var r=on.fromDom(e.document);return _l(r,n,t).map(function(e){return Al.create(on.fromDom(e.startContainer),e.startOffset,on.fromDom(e.endContainer),e.endOffset)})}(e,n,t)}function Za(e,n,t,r){return jl(e,n,Ol(t),r)}function el(e,n,t,r){return jl(e,n,Dl(t),r)}function nl(e,n){var t=Il.exact(n,0,n,0);return Ya(t)}function tl(e,n){return function(e){return 0===e.length?Me.none():Me.some(e[e.length-1])}(Be(n,"tr")).bind(function(e){return et(e,"td,th").map(function(e){return nl(0,e)})})}function rl(e,n,t,r){return void 0===r&&(r=Kl),e.property().parent(n).map(function(e){return Yl(e,r)})}function ol(n){return function(e){return 0===n.property().children(e).length}}function il(e,n){return function(e,n,t){return nf(e,n,ol(e),t)}(rf,e,n)}function ul(e,n){return function(e,n,t){return tf(e,n,ol(e),t)}(rf,e,n)}function cl(e){return nt(e,"tr")}function al(e){return"br"===en(e)}function ll(n,e,t,r){return function(e,n){return Se(e,n).filter(al).orThunk(function(){return Se(e,n-1).filter(al)})}(e,t).bind(function(e){return r.traverse(e).fold(function(){return ff(e,r.gather,n).map(r.relative)},function(e){return function(r){return ve(r).bind(function(n){var t=Ce(n);return lf(t,r).map(function(e){return af(n,t,r,e)})})}(e).map(function(e){return Nl.on(e.parent(),e.index())})})})}function fl(e){return gf.nu({left:e.left,top:e.top,right:e.right,bottom:e.bottom})}function sl(e,n){return Me.some(e.getRect(n))}function dl(n,e,t){return function(e,n,t){return $n(function(e,n){return n(e)},Jn,e,n,t)}(e,cc).fold(D(!1),function(e){return hf(n,e).exists(function(e){return function(e,n){return e.left()<n.left()||Math.abs(n.right()-e.left())<1||e.left()>n.right()}(t,e)})})}function ml(n,t,e){var r=n.move(e,5),o=yf(t,n,e,r,100).getOr(r);return function(e,n,t){return e.point(n)>t.getInnerHeight()?Me.some(e.point(n)-t.getInnerHeight()):e.point(n)<0?Me.some(-e.point(n)):Me.none()}(n,o,t).fold(function(){return t.situsFromPoint(o.left(),n.point(o))},function(e){return t.scrollBy(0,e),t.situsFromPoint(o.left(),n.point(o)-e)})}function gl(e,n){return function(e,n,t){return Jn(e,n,t).isSome()}(e,function(e){return ve(e).exists(function(e){return Bn(e,n)})})}function pl(n,r,o,e,i){return nt(e,"td,th",r).bind(function(t){return nt(t,"table",r).bind(function(e){return gl(i,e)?Df(n,r,o).bind(function(n){return nt(n.finish(),"td,th",r).map(function(e){return{start:D(t),finish:D(e),range:D(n)}})}):Me.none()})})}function hl(e,n){return nt(e,"td,th",n)}var vl=function(n,t,e){function r(){l.stop(),u.isOn()&&(u.off(),i.trigger.stop())}var o=!1,i=sa.create({start:fa([]),stop:fa([])}),u=ya(),c=function(t,r){var o=null;return{cancel:function(){null!==o&&(f.clearTimeout(o),o=null)},throttle:function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];null!==o&&f.clearTimeout(o),o=f.setTimeout(function(){t.apply(null,e),o=null},r)}}}(r,200);u.events.move.bind(function(e){t.mutate(n,e.info())});function a(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];o&&t.apply(null,e)}}var l=t.sink(va({forceDrop:r,drop:a(r),move:a(function(e){c.cancel(),u.onEvent(e,t)}),delayDrop:a(c.throttle)}),e);return{element:l.element,go:function(e){l.start(e),u.on(),i.trigger.start()},on:function(){o=!0},off:function(){o=!1},destroy:function(){l.destroy()},events:i.registry}},bl=function(e,n){void 0===n&&(n={});var t=n.mode!==undefined?n.mode:ba;return vl(e,t,n)},wl=ru.resolve("resizer-bar-dragging"),yl=function(e,t){var r=po.height,n=xa(e,t,r),o=sa.create({beforeResize:fa(["table"]),afterResize:fa(["table"]),startDrag:fa([])});return n.events.adjustHeight.bind(function(e){o.trigger.beforeResize(e.table());var n=r.delta(e.delta(),e.table());Xu(e.table(),n,e.row(),r),o.trigger.afterResize(e.table())}),n.events.startAdjust.bind(function(e){o.trigger.startDrag()}),n.events.adjustWidth.bind(function(e){o.trigger.beforeResize(e.table());var n=t.delta(e.delta(),e.table());Ku(e.table(),n,e.column(),t),o.trigger.afterResize(e.table())}),{on:n.on,off:n.off,hideBars:n.hideBars,showBars:n.showBars,destroy:n.destroy,events:o.registry}},Cl=function(e,n){return e.inline?la(pc(e),function(){var e=on.fromTag("div");return re(e,{position:"static",height:"0",width:"0",padding:"0",margin:"0",border:"0"}),Wn(un(),e),e}()):aa(on.fromDom(e.getDoc()))},Sl=function(e,n){e.inline&&_n(n.parent())},xl=function(u){function c(e){return"TABLE"===e.nodeName}function r(e){var n=u.dom.getStyle(e,"width")||u.dom.getAttrib(e,"width");return Me.from(n).filter(function(e){return 0<e.length})}function e(){return i}var a,l,o=Me.none(),i=Me.none(),f=Me.none(),s=/(\d+(\.\d+)?)%/;return u.on("init",function(){var e=vo(wc.directionAt),n=Cl(u);if(f=Me.some(n),function(e){var n=e.getParam("object_resizing",!0);return _e(n)?"table"===n:n}(u)&&function(e){return e.getParam("table_resize_bars",!0,"boolean")}(u)){var t=yl(n,e);t.on(),t.events.startDrag.bind(function(e){o=Me.some(u.selection.getRng())}),t.events.beforeResize.bind(function(e){var n=e.table().dom();Bi(u,n,wi(n),yi(n))}),t.events.afterResize.bind(function(e){var n=e.table(),t=n.dom();Si(n),o.each(function(e){u.selection.setRng(e),u.focus()}),Pi(u,t,wi(t),yi(t)),u.undoManager.add()}),i=Me.some(t)}}),u.on("ObjectResizeStart",function(e){var n=e.target;if(c(n)){var t=r(n).map(function(e){return s.test(e)}).getOr(!1);t&&Ni(u)?function(e){te(on.fromDom(e),"width",wi(e).toString()+"px")}(n):!t&&function(e){return!0===e.getParam("table_responsive_width")}(u)&&function(e){var n=on.fromDom(e);ve(n).map(function(e){return Ra(n,e)}).each(function(e){te(n,"width",e),p(Be(n,"tr"),function(n){p(Ce(n),function(e){te(e,"width",Ra(e,n))})})})}(n),a=e.width,l=r(n).getOr("")}}),u.on("ObjectResized",function(e){var n=e.target;if(c(n)){var t=n;if(s.test(l)){var r=parseFloat(s.exec(l)[1]),o=e.width*r/a;u.dom.setStyle(t,"width",o+"%")}else{var i=[];Ac.each(t.rows,function(e){Ac.each(e.cells,function(e){var n=u.dom.getStyle(e,"width",!0);i.push({cell:e,width:n})})}),Ac.each(i,function(e){u.dom.setStyle(e.cell,"width",e.width),u.dom.setAttrib(e.cell,"width",null)})}}}),u.on("SwitchMode",function(){e().each(function(e){u.readonly?e.hideBars():e.showBars()})}),{lazyResize:e,lazyWire:function(){return f.getOr(aa(on.fromDom(u.getBody())))},destroy:function(){i.each(function(e){e.destroy()}),f.each(function(e){Sl(u,e)})}}},Rl=xr([{none:["current"]},{first:["current"]},{middle:["current","target"]},{last:["current"]}]),Tl=fn(fn({},Rl),{none:function(e){return void 0===e&&(e=undefined),Rl.none(e)}}),Ol=function(n,e){return Ta(n,e).fold(function(){return Tl.none(n)},function(e){return e.index()+1<e.all().length?Tl.middle(n,e.all()[e.index()+1]):Tl.last(n)})},Dl=function(n,e){return Ta(n,e).fold(function(){return Tl.none()},function(e){return 0<=e.index()-1?Tl.middle(n,e.all()[e.index()-1]):Tl.first(n)})},Al={create:B("start","soffset","finish","foffset")},El=xr([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),Nl={before:El.before,on:El.on,after:El.after,cata:function(e,n,t,r){return e.fold(n,t,r)},getStart:function(e){return e.fold(o,o,o)}},kl=xr([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),Il={domRange:kl.domRange,relative:kl.relative,exact:kl.exact,exactFromRange:function(e){return kl.exact(e.start(),e.soffset(),e.finish(),e.foffset())},getWin:function(e){return function(e){return on.fromDom(e.dom().ownerDocument.defaultView)}(function(e){return e.match({domRange:function(e){return on.fromDom(e.startContainer)},relative:function(e,n){return Nl.getStart(e)},exact:function(e,n,t,r){return e}})}(e))},range:Al.create},Bl=function(e,n){e.selectNodeContents(n.dom())},Pl=xr([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),Ml=function(i,e){return Na(i,e).match({ltr:function(e,n,t,r){var o=i.document.createRange();return o.setStart(e.dom(),n),o.setEnd(t.dom(),r),o},rtl:function(e,n,t,r){var o=i.document.createRange();return o.setStart(t.dom(),r),o.setEnd(e.dom(),n),o}})},Wl=function(e,n,t,r){return tn(n)?function(n,t,r,o){var e=n.dom().createRange();e.selectNode(t.dom());var i=e.getClientRects();return wo(i,function(e){return ka(e,r,o)?Me.some(e):Me.none()}).map(function(e){return Ia(n,t,r,o,e)})}(e,n,t,r):function(n,e,t,r){var o=n.dom().createRange(),i=Ce(e);return wo(i,function(e){return o.selectNode(e.dom()),ka(o.getBoundingClientRect(),t,r)?Wl(n,e,t,r):Me.none()})}(e,n,t,r)},_l=document.caretPositionFromPoint?function(t,e,n){return Me.from(t.dom().caretPositionFromPoint(e,n)).bind(function(e){if(null===e.offsetNode)return Me.none();var n=t.dom().createRange();return n.setStart(e.offsetNode,e.offset),n.collapse(),Me.some(n)})}:document.caretRangeFromPoint?function(e,n,t){return Me.from(e.dom().caretRangeFromPoint(n,t))}:function(t,r,o){return on.fromPoint(t,r,o).bind(function(e){function n(){return function(e,n,t){return(0===Ce(n).length?Wa:Ma)(e,n,t)}(t,e,r)}return 0===Ce(e).length?n():_a(t,e,r,o).orThunk(n)})},Ll=tinymce.util.Tools.resolve("tinymce.util.VK"),jl=function(r,e,n,o,t){return n.fold(Me.none,Me.none,function(e,n){return St(n).map(function(e){return nl(0,e)})},function(t){return ft.table(t,e).bind(function(e){var n=Nr.noMenu(t);return r.undoManager.transact(function(){o.insertRowsAfter(e,n)}),tl(0,e)})})},zl=["table","li","dl"],Hl={handle:function(n,t,r,o){if(n.keyCode===Ll.TAB){var i=pc(t),u=function(e){var n=en(e);return Bn(e,i)||l(zl,n)},e=t.selection.getRng();if(e.collapsed){var c=on.fromDom(e.startContainer);ft.cell(c,u).each(function(e){n.preventDefault(),(n.shiftKey?el:Za)(t,u,e,r,o).each(function(e){t.selection.setRng(e)})})}}}},Fl={create:B("selection","kill")},Ul=function(e,n,t,r){return{start:D(Nl.on(e,n)),finish:D(Nl.on(t,r))}},ql={convertToRange:function(e,n){var t=Ml(e,n);return Al.create(on.fromDom(t.startContainer),t.startOffset,on.fromDom(t.endContainer),t.endOffset)},makeSitus:Ul},Vl=function(t,e,r,n,o){return Bn(r,n)?Me.none():mr(r,n,e).bind(function(e){var n=e.boxes().getOr([]);return 0<n.length?(o(t,n,e.start(),e.finish()),Me.some(Fl.create(Me.some(ql.makeSitus(r,0,r,yt(r))),!0))):Me.none()})},Gl={sync:function(t,r,e,n,o,i,u){return Bn(e,o)&&n===i?Me.none():nt(e,"td,th",r).bind(function(n){return nt(o,"td,th",r).bind(function(e){return Vl(t,r,n,e,u)})})},detect:Vl,update:function(e,n,t,r,o){return pr(r,e,n,o.firstSelectedSelector(),o.lastSelectedSelector()).map(function(e){return o.clearBeforeUpdate(t),o.selectRange(t,e.boxes(),e.start(),e.finish()),e.boxes()})}},Yl=B("item","mode"),Kl=function(e,n,t,r){return void 0===r&&(r=Xl),t.sibling(e,n).map(function(e){return Yl(e,r)})},Xl=function(e,n,t,r){void 0===r&&(r=Xl);var o=e.property().children(n);return t.first(o).map(function(e){return Yl(e,r)})},$l=[{current:rl,next:Kl,fallback:Me.none()},{current:Kl,next:Xl,fallback:Me.some(rl)},{current:Xl,next:Xl,fallback:Me.some(Kl)}],Jl=function(n,t,r,o,e){return void 0===e&&(e=$l),y(e,function(e){return e.current===r}).bind(function(e){return e.current(n,t,o,e.next).orThunk(function(){return e.fallback.bind(function(e){return Jl(n,t,e,o)})})})},Ql=function(){return{sibling:function(e,n){return e.query().prevSibling(n)},first:function(e){return 0<e.length?Me.some(e[e.length-1]):Me.none()}}},Zl=function(){return{sibling:function(e,n){return e.query().nextSibling(n)},first:function(e){return 0<e.length?Me.some(e[0]):Me.none()}}},ef=function(n,e,t,r,o,i){return Jl(n,e,r,o).bind(function(e){return i(e.item())?Me.none():t(e.item())?Me.some(e.item()):ef(n,e.item(),t,e.mode(),o,i)})},nf=function(e,n,t,r){return ef(e,n,t,Kl,Ql(),r)},tf=function(e,n,t,r){return ef(e,n,t,Kl,Zl(),r)},rf=Jt(),of=B("element","offset"),uf=(B("element","deltaOffset"),B("element","start","finish"),B("begin","end"),B("element","text"),xr([{none:["message"]},{success:[]},{failedUp:["cell"]},{failedDown:["cell"]}])),cf=fn(fn({},uf),{verify:function(t,n,e,r,o,i,u){return nt(r,"td,th",u).bind(function(e){return nt(n,"td,th",u).map(function(n){return Bn(e,n)?Bn(r,e)&&yt(e)===o?i(n):uf.none("in same cell"):sr.sharedOne(cl,[e,n]).fold(function(){return function(e,n,t){var r=e.getRect(n),o=e.getRect(t);return o.right>r.left&&o.left<r.right}(t,n,e)?uf.success():i(n)},function(e){return i(n)})})}).getOr(uf.none("default"))},cata:function(e,n,t,r,o){return e.fold(n,t,r,o)}}),af=(B("ancestor","descendants","element","index"),B("parent","children","element","index")),lf=function(e,n){return C(e,b(Bn,n))},ff=function(e,n,t){return n(e,t).bind(function(e){return tn(e)&&0===vt(e).trim().length?ff(e,n,t):Me.some(e)})},sf=function(e,n,t,r){return(al(n)?function(e,n,t){return t.traverse(n).orThunk(function(){return ff(n,t.gather,e)}).map(t.relative)}(e,n,r):ll(e,n,t,r)).map(function(e){return{start:D(e),finish:D(e)}})},df=function(e){return cf.cata(e,function(e){return Me.none()},function(){return Me.none()},function(e){return Me.some(of(e,0))},function(e){return Me.some(of(e,yt(e)))})},mf=Xe(["left","top","right","bottom"],[]),gf={nu:mf,moveUp:function(e,n){return mf({left:e.left(),top:e.top()-n,right:e.right(),bottom:e.bottom()-n})},moveDown:function(e,n){return mf({left:e.left(),top:e.top()+n,right:e.right(),bottom:e.bottom()+n})},moveBottomTo:function(e,n){var t=e.bottom()-e.top();return mf({left:e.left(),top:n-t,right:e.right(),bottom:n})},moveTopTo:function(e,n){var t=e.bottom()-e.top();return mf({left:e.left(),top:n,right:e.right(),bottom:n+t})},getTop:function(e){return e.top()},getBottom:function(e){return e.bottom()},translate:function(e,n,t){return mf({left:e.left()+n,top:e.top()+t,right:e.right()+n,bottom:e.bottom()+t})},toString:function(e){return"("+e.left()+", "+e.top()+") -> ("+e.right()+", "+e.bottom()+")"}},pf=function(e,n,t){return nn(n)?sl(e,n).map(fl):tn(n)?function(e,n,t){return 0<=t&&t<yt(n)?e.getRangedRect(n,t,n,t+1):0<t?e.getRangedRect(n,t-1,n,t):Me.none()}(e,n,t).map(fl):Me.none()},hf=function(e,n){return nn(n)?sl(e,n).map(fl):tn(n)?e.getRangedRect(n,0,n,yt(n)).map(fl):Me.none()},vf=xr([{none:[]},{retry:["caret"]}]),bf={point:gf.getTop,adjuster:function(e,n,t,r,o){var i=gf.moveUp(o,5);return Math.abs(t.top()-r.top())<1?vf.retry(i):t.bottom()<o.top()?vf.retry(i):t.bottom()===o.top()?vf.retry(gf.moveUp(o,1)):dl(e,n,o)?vf.retry(gf.translate(i,5,0)):vf.none()},move:gf.moveUp,gather:il},wf={point:gf.getBottom,adjuster:function(e,n,t,r,o){var i=gf.moveDown(o,5);return Math.abs(t.bottom()-r.bottom())<1?vf.retry(i):t.top()>o.bottom()?vf.retry(i):t.top()===o.bottom()?vf.retry(gf.moveDown(o,1)):dl(e,n,o)?vf.retry(gf.translate(i,5,0)):vf.none()},move:gf.moveDown,gather:ul},yf=function(t,r,o,i,u){return 0===u?Me.some(i):function(e,n,t){return e.elementFromPoint(n,t).filter(function(e){return"table"===en(e)}).isSome()}(t,i.left(),r.point(i))?function(e,n,t,r,o){return yf(e,n,t,n.move(r,5),o)}(t,r,o,i,u-1):t.situsFromPoint(i.left(),r.point(i)).bind(function(e){return e.start().fold(Me.none,function(n){return hf(t,n).bind(function(e){return r.adjuster(t,n,e,o,i).fold(Me.none,function(e){return yf(t,r,o,e,u-1)})}).orThunk(function(){return Me.some(i)})},Me.none)})},Cf={tryUp:b(ml,bf),tryDown:b(ml,wf),ieTryUp:function(e,n){return e.situsFromPoint(n.left(),n.top()-5)},ieTryDown:function(e,n){return e.situsFromPoint(n.left(),n.bottom()+5)},getJumpSize:D(5)},Sf=me(),xf=function(r,o,i,u,c,a){return 0===a?Me.none():Of(r,o,i,u,c).bind(function(e){var n=r.fromSitus(e),t=cf.verify(r,i,u,n.finish(),n.foffset(),c.failure,o);return cf.cata(t,function(){return Me.none()},function(){return Me.some(e)},function(e){return Bn(i,e)&&0===u?Rf(r,i,u,gf.moveUp,c):xf(r,o,e,0,c,a-1)},function(e){return Bn(i,e)&&u===yt(e)?Rf(r,i,u,gf.moveDown,c):xf(r,o,e,yt(e),c,a-1)})})},Rf=function(n,e,t,r,o){return pf(n,e,t).bind(function(e){return Tf(n,o,r(e,Cf.getJumpSize()))})},Tf=function(e,n,t){return Sf.browser.isChrome()||Sf.browser.isSafari()||Sf.browser.isFirefox()||Sf.browser.isEdge()?n.otherRetry(e,t):Sf.browser.isIE()?n.ieRetry(e,t):Me.none()},Of=function(n,e,t,r,o){return pf(n,t,r).bind(function(e){return Tf(n,o,e)})},Df=function(n,t,r){return function(o,i,u){return o.getSelection().bind(function(r){return sf(i,r.finish(),r.foffset(),u).fold(function(){return Me.some(of(r.finish(),r.foffset()))},function(e){var n=o.fromSitus(e),t=cf.verify(o,r.finish(),r.foffset(),n.finish(),n.foffset(),u.failure,i);return df(t)})})}(n,t,r).bind(function(e){return xf(n,t,e.element(),e.offset(),r,20).map(n.fromSitus)})},Af=me(),Ef=function(e,n,t,r,o,i){return Af.browser.isIE()?Me.none():i(r,n).orThunk(function(){return pl(e,n,t,r,o).map(function(e){var n=e.range();return Fl.create(Me.some(ql.makeSitus(n.start(),n.soffset(),n.finish(),n.foffset())),!0)})})},Nf=function(e,n,t,r,o,i,u){return pl(e,t,r,o,i).bind(function(e){return Gl.detect(n,t,e.start(),e.finish(),u)})},kf=function(e,r){return nt(e,"tr",r).bind(function(t){return nt(t,"table",r).bind(function(e){var n=Be(e,"tr");return Bn(t,n[0])?function(e,n,t){return nf(rf,e,n,t)}(e,function(e){return xt(e).isSome()},r).map(function(e){var n=yt(e);return Fl.create(Me.some(ql.makeSitus(e,n,e,n)),!0)}):Me.none()})})},If=function(e,r){return nt(e,"tr",r).bind(function(t){return nt(t,"table",r).bind(function(e){var n=Be(e,"tr");return Bn(t,n[n.length-1])?function(e,n,t){return tf(rf,e,n,t)}(e,function(e){return St(e).isSome()},r).map(function(e){return Fl.create(Me.some(ql.makeSitus(e,0,e,0)),!0)}):Me.none()})})};function Bf(n){return function(e){return e===n}}function Pf(c){return{elementFromPoint:function(e,n){return on.fromPoint(on.fromDom(c.document),e,n)},getRect:function(e){return e.dom().getBoundingClientRect()},getRangedRect:function(e,n,t,r){var o=Il.exact(e,n,t,r);return Ja(c,o).map(zf)},getSelection:function(){return $a(c).map(function(e){return ql.convertToRange(c,e)})},fromSitus:function(e){var n=Il.relative(e.start(),e.finish());return ql.convertToRange(c,n)},situsFromPoint:function(e,n){return Qa(c,e,n).map(function(e){return Ul(e.start(),e.soffset(),e.finish(),e.foffset())})},clearSelection:function(){!function(e){e.getSelection().removeAllRanges()}(c)},collapseSelection:function(u){void 0===u&&(u=!1),$a(c).each(function(e){return e.fold(function(e){return e.collapse(u)},function(e,n){var t=u?e:n;Ga(c,t,t)},function(e,n,t,r){var o=u?e:t,i=u?n:r;Va(c,o,i,o,i)})})},setSelection:function(e){Va(c,e.start(),e.soffset(),e.finish(),e.foffset())},setRelativeSelection:function(e,n){Ga(c,e,n)},selectContents:function(e){Xa(c,e)},getInnerHeight:function(){return c.innerHeight},getScrollY:function(){return function(e){var n=e!==undefined?e.dom():f.document,t=n.body.scrollLeft||n.documentElement.scrollLeft,r=n.body.scrollTop||n.documentElement.scrollTop;return fo(t,r)}(on.fromDom(c.document)).top()},scrollBy:function(e,n){!function(e,n,t){(t!==undefined?t.dom():f.document).defaultView.scrollBy(e,n)}(e,n,on.fromDom(c.document))}}}function Mf(n,e){p(e,function(e){!function(e,n){So(e)?e.dom().classList.remove(n):Ro(e,n);Oo(e)}(n,e)})}var Wf={down:{traverse:ye,gather:ul,relative:Nl.before,otherRetry:Cf.tryDown,ieRetry:Cf.ieTryDown,failure:cf.failedDown},up:{traverse:we,gather:il,relative:Nl.before,otherRetry:Cf.tryUp,ieRetry:Cf.ieTryUp,failure:cf.failedUp}},_f=Bf(38),Lf=Bf(40),jf={ltr:{isBackward:Bf(37),isForward:Bf(39)},rtl:{isBackward:Bf(39),isForward:Bf(37)},isUp:_f,isDown:Lf,isNavigation:function(e){return 37<=e&&e<=40}},zf=function(e){return{left:e.left(),top:e.top(),right:e.right(),bottom:e.bottom(),width:e.width(),height:e.height()}},Hf=(me().browser.isSafari(),B("rows","cols")),Ff={mouse:function(e,n,t,r){var o=function c(o,i,n,u){function t(){r=Me.none()}var r=Me.none();return{mousedown:function(e){u.clear(i),r=hl(e.target(),n)},mouseover:function(e){r.each(function(r){u.clearBeforeUpdate(i),hl(e.target(),n).each(function(t){mr(r,t,n).each(function(e){var n=e.boxes().getOr([]);(1<n.length||1===n.length&&!Bn(r,t))&&(u.selectRange(i,n,e.start(),e.finish()),o.selectContents(t))})})})},mouseup:function(e){r.each(t)}}}(Pf(e),n,t,r);return{mousedown:o.mousedown,mouseover:o.mouseover,mouseup:o.mouseup}},keyboard:function(e,l,f,s){function d(){return s.clear(l),Me.none()}var m=Pf(e);return{keydown:function(e,n,t,r,o,i){var u=e.raw(),c=u.which,a=!0===u.shiftKey;return gr(l,s.selectedSelector()).fold(function(){return jf.isDown(c)&&a?b(Nf,m,l,f,Wf.down,r,n,s.selectRange):jf.isUp(c)&&a?b(Nf,m,l,f,Wf.up,r,n,s.selectRange):jf.isDown(c)?b(Ef,m,f,Wf.down,r,n,If):jf.isUp(c)?b(Ef,m,f,Wf.up,r,n,kf):Me.none},function(n){function e(e){return function(){return wo(e,function(e){return Gl.update(e.rows(),e.cols(),l,n,s)}).fold(function(){return hr(l,s.firstSelectedSelector(),s.lastSelectedSelector()).map(function(e){var n=jf.isDown(c)||i.isForward(c)?Nl.after:Nl.before;return m.setRelativeSelection(Nl.on(e.first(),0),n(e.table())),s.clear(l),Fl.create(Me.none(),!0)})},function(e){return Me.some(Fl.create(Me.none(),!0))})}}return jf.isDown(c)&&a?e([Hf(1,0)]):jf.isUp(c)&&a?e([Hf(-1,0)]):i.isBackward(c)&&a?e([Hf(0,-1),Hf(-1,0)]):i.isForward(c)&&a?e([Hf(0,1),Hf(1,0)]):jf.isNavigation(c)&&!1==a?d:Me.none})()},keyup:function(t,r,o,i,u){return gr(l,s.selectedSelector()).fold(function(){var e=t.raw(),n=e.which;return!1==(!0===e.shiftKey)?Me.none():jf.isNavigation(n)?Gl.sync(l,f,r,o,i,u,s.selectRange):Me.none()},Me.none)}}},external:function(e,r,n,o){var i=Pf(e);return function(e,t){o.clearBeforeUpdate(r),mr(e,t,n).each(function(e){var n=e.boxes().getOr([]);o.selectRange(r,n,e.start(),e.finish()),i.selectContents(t),i.collapseSelection()})}}},Uf={byClass:function(o){function i(e){var n=Be(e,o.selectedSelector());p(n,t)}var u=function(n){return function(e){To(e,n)}}(o.selected()),t=function(n){return function(e){Mf(e,n)}}([o.selected(),o.lastSelected(),o.firstSelected()]);return{clearBeforeUpdate:i,clear:i,selectRange:function(e,n,t,r){i(e),p(n,u),To(t,o.firstSelected()),To(r,o.lastSelected())},selectedSelector:o.selectedSelector,firstSelectedSelector:o.firstSelectedSelector,lastSelectedSelector:o.lastSelectedSelector}},byAttr:function(o,i,n){function t(e){Y(e,o.selected()),Y(e,o.firstSelected()),Y(e,o.lastSelected())}function u(e){U(e,o.selected(),"1")}function c(e){r(e),n()}var r=function(e){var n=Be(e,o.selectedSelector());p(n,t)};return{clearBeforeUpdate:r,clear:c,selectRange:function(e,n,t,r){c(e),p(n,u),U(t,o.firstSelected(),"1"),U(r,o.lastSelected(),"1"),i(n,t,r)},selectedSelector:o.selectedSelector,firstSelectedSelector:o.firstSelectedSelector,lastSelectedSelector:o.lastSelectedSelector}}},qf={getOtherCells:function(e,n,t){var r=st(e),o=gt.generate(r);return Uo(o,n).map(function(e){var n=wu(o,t,!1);return{upOrLeftCells:function(e,t,n){var r=e.slice(0,t[t.length-1].row()+1),o=Lo(r,n);return x(o,function(e){var n=e.cells().slice(0,t[t.length-1].column()+1);return g(n,function(e){return e.element()})})}(n,e,t),downOrRightCells:function(e,t,n){var r=e.slice(t[0].row()+t[0].rowspan()-1,e.length),o=Lo(r,n);return x(o,function(e){var n=e.cells().slice(t[0].column()+t[0].colspan()-1,+e.cells().length);return g(n,function(e){return e.element()})})}(n,e,t)}})}},Vf=function(e){return!1===Do(on.fromDom(e.target),"ephox-snooker-resizer-bar")};function Gf(w,y,e){var C=Xe(["mousedown","mouseover","mouseup","keyup","keydown"],[]),S=Me.none(),a=xc(w),x=Uf.byAttr(Sr,function(i,u,c){e.targets().each(function(o){ft.table(u).each(function(e){var n=on.fromDom(w.getDoc()),t=_t.cellOperations(T,n,a),r=qf.getOtherCells(e,o,t);Rc(w,i,u,c,r)})})},function(){Tc(w)});w.on("init",function(e){var r=w.getWin(),o=pc(w),n=hc(w),t=Ff.mouse(r,o,n,x),c=Ff.keyboard(r,o,n,x),i=Ff.external(r,o,n,x);w.on("TableSelectorChange",function(e){i(e.start,e.finish)});function a(e,n){!function(e){return!0===e.raw().shiftKey}(e)||(n.kill()&&e.kill(),n.selection().each(function(e){var n=Il.relative(e.start(),e.finish()),t=Ml(r,n);w.selection.setRng(t)}))}function u(e){var n=v(e);if(n.raw().shiftKey&&jf.isNavigation(n.raw().which)){var t=w.selection.getRng(),r=on.fromDom(t.startContainer),o=on.fromDom(t.endContainer);c.keyup(n,r,t.startOffset,o,t.endOffset).each(function(e){a(n,e)})}}function l(e){var n=v(e);y().each(function(e){e.hideBars()});var t=w.selection.getRng(),r=on.fromDom(w.selection.getStart()),o=on.fromDom(t.startContainer),i=on.fromDom(t.endContainer),u=wc.directionAt(r).isRtl()?jf.rtl:jf.ltr;c.keydown(n,o,t.startOffset,i,t.endOffset,u).each(function(e){a(n,e)}),y().each(function(e){e.showBars()})}function f(e){return e.hasOwnProperty("x")&&e.hasOwnProperty("y")}function s(e){return 0===e.button}function d(e){s(e)&&Vf(e)&&t.mousedown(v(e))}function m(e){(function(e){return e.buttons===undefined||0!=(1&e.buttons)})(e)&&Vf(e)&&t.mouseover(v(e))}function g(e){s(e)&&Vf(e)&&t.mouseup(v(e))}var p,h,v=function(e){function n(){e.stopPropagation()}function t(){e.preventDefault()}var r=on.fromDom(e.target),o=O(t,n);return{target:D(r),x:D(f(e)?e.x:null),y:D(f(e)?e.y:null),stop:n,prevent:t,kill:o,raw:D(e)}},b=(p=R(on.fromDom(o)),h=R(0),{touchEnd:function(e){var n=on.fromDom(e.target);if("td"===en(n)||"th"===en(n)){var t=p.get(),r=h.get();Bn(t,n)&&e.timeStamp-r<300&&(e.preventDefault(),i(n,n))}p.set(n),h.set(e.timeStamp)}});w.on("mousedown",d),w.on("mouseover",m),w.on("mouseup",g),w.on("touchend",b.touchEnd),w.on("keyup",u),w.on("keydown",l),w.on("NodeChange",function(){var e=w.selection,n=on.fromDom(e.getStart()),t=on.fromDom(e.getEnd());sr.sharedOne(ft.table,[n,t]).fold(function(){x.clear(o)},T)}),S=Me.some(C({mousedown:d,mouseover:m,mouseup:g,keyup:u,keydown:l}))});return{clear:x.clear,destroy:function(){S.each(function(e){})}}}var Yf=function(n){return{get:function(){var e=pc(n);return vr(e,Sr.selectedSelector()).fold(function(){return n.selection.getStart()===undefined?Tr.none():Tr.single(n.selection)},function(e){return Tr.multiple(e)})}}},Kf=function(e,t){function n(){return ia(e).bind(function(n){return ft.table(n).map(function(e){return"caption"===en(n)?Nr.notCell(n):Nr.forMenu(t,e,n)})})}function r(){i.set(Z(n)()),p(u.get(),function(e){return e()})}function o(n,t){function r(){return i.get().fold(function(){n.setDisabled(!0)},function(e){n.setDisabled(t(e))})}return r(),u.set(u.get().concat([r])),function(){u.set(h(u.get(),function(e){return e!==r}))}}var i=R(Me.none()),u=R([]);return e.on("NodeChange TableSelectorChange",r),{onSetupTable:function(e){return o(e,function(e){return!1})},onSetupCellOrRow:function(e){return o(e,function(e){return"caption"===en(e.element())})},onSetupMergeable:function(e){return o(e,function(e){return e.mergable().isNone()})},onSetupUnmergeable:function(e){return o(e,function(e){return e.unmergable().isNone()})},resetTargets:r,targets:function(){return i.get()}}},Xf={addButtons:function(n,e){n.ui.registry.addMenuButton("table",{tooltip:"Table",icon:"table",fetch:function(e){return e("inserttable | cell row column | advtablesort | tableprops deletetable")}});function t(e){return function(){return n.execCommand(e)}}n.ui.registry.addButton("tableprops",{tooltip:"Table properties",onAction:t("mceTableProps"),icon:"table",onSetup:e.onSetupTable}),n.ui.registry.addButton("tabledelete",{tooltip:"Delete table",onAction:t("mceTableDelete"),icon:"table-delete-table",onSetup:e.onSetupTable}),n.ui.registry.addButton("tablecellprops",{tooltip:"Cell properties",onAction:t("mceTableCellProps"),icon:"table-cell-properties",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tablemergecells",{tooltip:"Merge cells",onAction:t("mceTableMergeCells"),icon:"table-merge-cells",onSetup:e.onSetupMergeable}),n.ui.registry.addButton("tablesplitcells",{tooltip:"Split cell",onAction:t("mceTableSplitCells"),icon:"table-split-cells",onSetup:e.onSetupUnmergeable}),n.ui.registry.addButton("tableinsertrowbefore",{tooltip:"Insert row before",onAction:t("mceTableInsertRowBefore"),icon:"table-insert-row-above",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tableinsertrowafter",{tooltip:"Insert row after",onAction:t("mceTableInsertRowAfter"),icon:"table-insert-row-after",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tabledeleterow",{tooltip:"Delete row",onAction:t("mceTableDeleteRow"),icon:"table-delete-row",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tablerowprops",{tooltip:"Row properties",onAction:t("mceTableRowProps"),icon:"table-row-properties",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tableinsertcolbefore",{tooltip:"Insert column before",onAction:t("mceTableInsertColBefore"),icon:"table-insert-column-before",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tableinsertcolafter",{tooltip:"Insert column after",onAction:t("mceTableInsertColAfter"),icon:"table-insert-column-after",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tabledeletecol",{tooltip:"Delete column",onAction:t("mceTableDeleteCol"),icon:"table-delete-column",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tablecutrow",{tooltip:"Cut row",onAction:t("mceTableCutRow"),icon:"temporary-placeholder",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tablecopyrow",{tooltip:"Copy row",onAction:t("mceTableCopyRow"),icon:"temporary-placeholder",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tablepasterowbefore",{tooltip:"Paste row before",onAction:t("mceTablePasteRowBefore"),icon:"temporary-placeholder",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tablepasterowafter",{tooltip:"Paste row after",onAction:t("mceTablePasteRowAfter"),icon:"temporary-placeholder",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tableinsertdialog",{tooltip:"Insert table",onAction:t("mceInsertTable"),icon:"table"})},addToolbars:function(n){var e=function(e){return e.getParam("table_toolbar","tableprops tabledelete | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol")}(n);0<e.length&&n.ui.registry.addContextToolbar("table",{predicate:function(e){return n.dom.is(e,"table")&&n.getBody().contains(e)},items:e,scope:"node",position:"node"})}},$f={addMenuItems:function(r,e){function n(e){return function(){return r.execCommand(e)}}function t(e){var n=e.numRows,t=e.numColumns;r.undoManager.transact(function(){ea(r,t,n)}),r.addVisual()}var o={text:"Table properties",onSetup:e.onSetupTable,onAction:n("mceTableProps")},i={text:"Delete table",icon:"table-delete-table",onSetup:e.onSetupTable,onAction:n("mceTableDelete")},u=[{type:"menuitem",text:"Insert row before",icon:"table-insert-row-above",onAction:n("mceTableInsertRowBefore"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Insert row after",icon:"table-insert-row-after",onAction:n("mceTableInsertRowAfter"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Delete row",icon:"table-delete-row",onAction:n("mceTableDeleteRow"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Row properties",icon:"table-row-properties",onAction:n("mceTableRowProps"),onSetup:e.onSetupCellOrRow},{type:"separator"},{type:"menuitem",text:"Cut row",onAction:n("mceTableCutRow"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Copy row",onAction:n("mceTableCopyRow"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Paste row before",onAction:n("mceTablePasteRowBefore"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Paste row after",onAction:n("mceTablePasteRowAfter"),onSetup:e.onSetupCellOrRow}],c={type:"nestedmenuitem",text:"Row",getSubmenuItems:function(){return u}},a=[{type:"menuitem",text:"Insert column before",icon:"table-insert-column-before",onAction:n("mceTableInsertColBefore"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Insert column after",icon:"table-insert-column-after",onAction:n("mceTableInsertColAfter"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Delete column",icon:"table-delete-column",onAction:n("mceTableDeleteCol"),onSetup:e.onSetupCellOrRow}],l={type:"nestedmenuitem",text:"Column",getSubmenuItems:function(){return a}},f=[{type:"menuitem",text:"Cell properties",icon:"table-cell-properties",onAction:n("mceTableCellProps"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Merge cells",icon:"table-merge-cells",onAction:n("mceTableMergeCells"),onSetup:e.onSetupMergeable},{type:"menuitem",text:"Split cell",icon:"table-split-cells",onAction:n("mceTableSplitCells"),onSetup:e.onSetupUnmergeable}],s={type:"nestedmenuitem",text:"Cell",getSubmenuItems:function(){return f}};!1===function(e){return e.getParam("table_grid",!0,"boolean")}(r)?r.ui.registry.addMenuItem("inserttable",{text:"Table",icon:"table",onAction:n("mceInsertTable")}):r.ui.registry.addNestedMenuItem("inserttable",{text:"Table",icon:"table",getSubmenuItems:function(){return[{type:"fancymenuitem",fancytype:"inserttable",onAction:t}]}}),r.ui.registry.addMenuItem("inserttabledialog",{text:"Insert table",icon:"table",onAction:n("mceInsertTable")}),r.ui.registry.addMenuItem("tableprops",o),r.ui.registry.addMenuItem("deletetable",i),r.ui.registry.addNestedMenuItem("row",c),r.ui.registry.addNestedMenuItem("column",l),r.ui.registry.addNestedMenuItem("cell",s),r.ui.registry.addContextMenu("table",{update:function(){return e.resetTargets(),e.targets().fold(function(){return""},function(e){return"caption"===en(e.element())?"tableprops deletetable":"cell row column | advtablesort | tableprops deletetable"})}})}},Jf=function(t,n,e,r){return{insertTable:function(e,n){return ea(t,e,n)},setClipboardRows:function(e){return function(e,n){var t=g(e,on.fromDom);n.set(Me.from(t))}(e,n)},getClipboardRows:function(){return function(e){return e.get().fold(function(){},function(e){return g(e,function(e){return e.dom()})})}(n)},resizeHandler:e,selectionTargets:r}};function Qf(n){var e=Yf(n),t=Kf(n,e),r=xl(n),o=Gf(n,r.lazyResize,t),i=Oc(n,r.lazyWire),u=R(Me.none());return ca.registerCommands(n,i,o,e,u),kr.registerEvents(n,e,i,o),$f.addMenuItems(n,t),Xf.addButtons(n,t),Xf.addToolbars(n),n.on("PreInit",function(){n.serializer.addTempAttr(Sr.firstSelected()),n.serializer.addTempAttr(Sr.lastSelected())}),Sc(n)&&n.on("keydown",function(e){Hl.handle(e,n,i,r.lazyWire)}),n.on("remove",function(){r.destroy(),o.destroy()}),Jf(n,u,r,t)}!function es(){We.add("table",Qf)}()}(window);