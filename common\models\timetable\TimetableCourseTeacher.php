<?php

/**
 * This is the model class for table "ivy_timetable_course_teacher".
 *
 * The followings are the available columns in table 'ivy_timetable_course_teacher':
 * @property integer $id
 * @property integer $tid
 * @property string $schoolid
 * @property string $term
 * @property integer $course_id
 * @property string $course_code
 * @property integer $teacher_id
 * @property integer $start_at
 * @property integer $end_at
 * @property integer $type
 */
class TimetableCourseTeacher extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_timetable_course_teacher';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('tid, course_id, teacher_id', 'required'),
			array('tid, course_id, teacher_id, start_at, end_at, type, term, status', 'numerical', 'integerOnly'=>true),
			array('schoolid', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, tid, schoolid, course_id, teacher_id, start_at, end_at, type, term, status', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            'course' => array(self::HAS_ONE, 'TimetableCourses', array('id' => 'course_id')),
            'students' => array(self::HAS_MANY, 'TimetableStudentData', array('course_id' => 'course_id'), 'condition'=>'students.status = 1'),
            'students_all' => array(self::HAS_MANY, 'TimetableStudentData', array('course_id' => 'course_id')),#新版的6-12课程表中可以更新学生的status为99 这样的数据属于暂时不上课 有的地方需要查出来
            'user' => array(self::HAS_ONE, 'User', array('uid' => 'teacher_id')),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'tid' => 'Tid',
			'schoolid' => 'Schoolid',
			'course_id' => 'Course',
			'teacher_id' => 'Teacher',
			'start_at' => 'Start At',
			'end_at' => 'End At',
			'term' => 'term',
			'type' => 'Type',
			'status' => 'Status',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('tid',$this->tid);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('course_id',$this->course_id);
		$criteria->compare('teacher_id',$this->teacher_id);
		$criteria->compare('start_at',$this->start_at);
		$criteria->compare('end_at',$this->end_at);
		$criteria->compare('type',$this->type);
		$criteria->compare('term',$this->term);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return TimetableCourseTeacher the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * 获取用户教授的课程列表
	 *
	 * @param [type] $yid
	 * @param [type] $schoolId
	 * @param [type] $startyear
	 * @param [type] $uid
	 * @return void
	 */
	public static function getCourseList($yid, $schoolId, $startyear, $uid)
	{
		$timeTable = Timetable::model()->findByAttributes(array('yid' => $yid, 'schoolid' => $schoolId));
        $list = array();
        if ($timeTable) {
            $tid = $timeTable->id;
            $criteria = new CDbCriteria();
            $criteria->compare('t.tid', $tid);
            $criteria->compare('t.schoolid', $schoolId);
            $criteria->compare('t.teacher_id', $uid);
            $criteria->compare('t.status', 1);
//            $criteria->compare('course.start_year', $startyear);
            $criteria->with = array('course');
            $courseTeachers = TimetableCourseTeacher::model()->findAll($criteria);
            foreach ($courseTeachers as $item) {
                $list[] = array(
                    'id' => $item->course_id,
                    'course_code' => $item->course->course_code,
                    'title' => $item->course->course_code .' '. $item->course->getAliasTitle($tid),
                );
            }
        }
		return $list;
	}
}
