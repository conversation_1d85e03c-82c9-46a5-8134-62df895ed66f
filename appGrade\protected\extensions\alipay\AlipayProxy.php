<?php

class AlipayProxy {

    public $partner = "";
    public $key = "";
    public $seller_email = "";
    public $return_url = "";
    public $notify_url = "";
    public $schoolid = "";
    public $number_code = "";
    public $_input_charset = "utf-8";
    public $sign_type = "MD5";
    public $transport = "http";

    public function __construct($schoolid) {
        $this->schoolid = $schoolid;
        $this->init();
    }

    public function init() {
        Yii::import('ext.alipay.lib.*');
        $alipayPartnerInfo = Mims::LoadConfig('CfgAlipayPartnerInfo');
        if (!empty($alipayPartnerInfo[$this->schoolid])) {
            $this->partner = $alipayPartnerInfo[$this->schoolid]['partner'];
            $this->key = $alipayPartnerInfo[$this->schoolid]['key'];
            $this->seller_email = $alipayPartnerInfo[$this->schoolid]['seller_email'];
            $this->number_code = $alipayPartnerInfo[$this->schoolid]['number_code'];
        }
        $this->return_url = Yii::app()->params['AlipayResponds']['return'];
        $this->notify_url = Yii::app()->params['AlipayResponds']['notify'];
    }
 
    public function getAttributes() {
        return array(
            'partner' => $this->partner,
            'key' => $this->key,
            'seller_email' => $this->seller_email,
            'return_url' => $this->return_url,
            'notify_url' => $this->notify_url,
            '_input_charset' => $this->_input_charset,
            'sign_type' => $this->sign_type,
            'transport' => $this->transport,
        );
    }

    public function buildForm($request) {
        $params = array(
            'partner' => $this->partner,
            'seller_email' => $this->seller_email,
            'return_url' => $this->return_url,
            'notify_url' => $this->notify_url,
            '_input_charset' => $this->_input_charset,
            'show_url' => $this->show_url,
        );
        $params = array_merge($params, $request->getParams());
        $service = new AlipayService($params, $this->key, $this->sign_type);
        return $service->build_form();
    }

    public function verifyNotify() {
        $notify = new AlipayNotify($this->partner, $this->key, $this->sign_type, $this->_input_charset, $this->transport);
        return $notify->notify_verify();
    }

    public function verifyReturn() {
        $notify = new AlipayNotify($this->partner, $this->key, $this->sign_type, $this->_input_charset, $this->transport);
        return $notify->return_verify();
    }

}

