    <style>

        html, body, #carousel, #carousel ul, #carousel li.bigpane {
            min-height: 100%;
            height: 100%;
            padding: 0;
            margin: 0;
            position: relative;
        }

        #carousel {
            /*background: silver;*/
            overflow: hidden;
            width:100%;
            -webkit-backface-visibility: hidden;
            -webkit-transform: translate3d(0,0,0) scale3d(1,1,1);
            -webkit-transform-style: preserve-3d;
        }

        #carousel ul.animate {
            -webkit-transition: all .3s;
            -moz-transition: all .3s;
            -o-transition: all .3s;
            transition: all .3s;
        }

        #carousel ul {
            transform: translate3d(0%,0,0) scale3d(1,1,1);
            -o-transform: translate3d(0%,0,0) scale3d(1,1,1);
            -ms-transform: translate3d(0%,0,0) scale3d(1,1,1);
            -moz-transform: translate3d(0%,0,0) scale3d(1,1,1);
            -webkit-transform: translate3d(0%,0,0) scale3d(1,1,1);
            overflow: hidden;
            -webkit-backface-visibility: hidden;
            -webkit-transform-style: preserve-3d;
        }

        #carousel ul {
/*            -webkit-box-shadow: 0 0 20px rgba(0,0,0,.2);
            box-shadow: 0 0 20px rgba(0,0,0,.2);
*/            position: relative;
        }

        #carousel li.bigpane {
            float: left;
            overflow: hidden;
            -webkit-transform-style: preserve-3d;
            -webkit-transform: translate3d(0,0,0);
        }

        #carousel li h2 {
            color: #fff;
            font-size: 14px;
            text-align: center;
            text-shadow: -1px -1px 0 rgba(0,0,0,.2);
        }
        #carousel li h3 {
            color: #fff;
            font-size: 12px;
            text-align: center;
            text-shadow: -1px -1px 0 rgba(0,0,0,.2);
			font-weight: normal;
        }

        #carousel li.pane1 { background: #AF81C9; }
        #carousel li.pane2 { background: #F89A7E; }
        #carousel li.pane3 { background: #F2CA85; }
        #carousel li.pane4 { background: #54D1F1; }
        #carousel li.pane5 { background: #7C71AD; }
        #carousel li.pane5 { background: #445569; }

		.child-index{text-align:center}
		.child-index img.smallface{width:60px;}
		.child-index li{float: left; height: 60px; width: 60px; display: block; overflow: hidden;
			-webkit-border-radius: 50em;
			-moz-border-radius: 50em;
			border-radius: 50em;
			padding:0;
			margin-right:3px;
			margin-bottom:3px;
		}
		li.singlechild{
			text-align:center;
			padding-top:20px !important;
			min-height: 350px !important;
		}
		li.singlechild img.bigface{
			-webkit-border-radius: 50em;
			-moz-border-radius: 50em;
			border-radius: 50em;		
		}
    </style>
	
	<h2>我的小伙伴们<span><?php echo $class->schoolInfo->title;?></span></h2>
	<p class="week">
	<?php echo $class->title;?><br />
	本列表只包含了当前在读的孩子信息，完整记录或历史信息请登录网页系统查看，谢谢！
	</p>
	
	
    <div id="carousel">
        <ul style="width: 6225px; -webkit-transform: translate3d(-60%, 0px, 0px) scale3d(1, 1, 1);" class="animate" id="carousel-ul">
            <li class="bigpane" style="width: 1245px;">
				<ul class="child-index" id="child-index">
				</ul>
			</li>

        </ul>
    </div>



    <script>


    /**
     * requestAnimationFrame and cancel polyfill
     */
    (function() {
        var lastTime = 0;
        var vendors = ['ms', 'moz', 'webkit', 'o'];
        for(var x = 0; x < vendors.length && !window.requestAnimationFrame; ++x) {
            window.requestAnimationFrame = window[vendors[x]+'RequestAnimationFrame'];
            window.cancelAnimationFrame =
                    window[vendors[x]+'CancelAnimationFrame'] || window[vendors[x]+'CancelRequestAnimationFrame'];
        }

        if (!window.requestAnimationFrame)
            window.requestAnimationFrame = function(callback, element) {
                var currTime = new Date().getTime();
                var timeToCall = Math.max(0, 16 - (currTime - lastTime));
                var id = window.setTimeout(function() { callback(currTime + timeToCall); },
                        timeToCall);
                lastTime = currTime + timeToCall;
                return id;
            };

        if (!window.cancelAnimationFrame)
            window.cancelAnimationFrame = function(id) {
                clearTimeout(id);
            };
    }());


    /**
    * super simple carousel
    * animation between panes happens with css transitions
    */
    function Carousel(element)
    {
        var self = this;
        element = $(element);

        var container = $(">ul", element);
        var panes = $(">ul>li", element);

        var pane_width = 0;
        var pane_count = panes.length;

        var current_pane = 0;


        /**
         * initial
         */
        this.init = function() {
            setPaneDimensions();

            $(window).on("load resize orientationchange", function() {
                setPaneDimensions();
                //updateOffset();
            })
        };


        /**
         * set the pane dimensions and scale the container
         */
        function setPaneDimensions() {
            pane_width = element.width();
            panes.each(function() {
                $(this).width(pane_width);
            });
            container.width(pane_width*pane_count);
        };


        /**
         * show pane by index
         */
        this.showPane = function(index, animate) {
            // between the bounds
            index = Math.max(0, Math.min(index, pane_count-1));
            current_pane = index;

            var offset = -((100/pane_count)*current_pane);
            setContainerOffset(offset, animate);
        };


        function setContainerOffset(percent, animate) {
            container.removeClass("animate");

            if(animate) {
                container.addClass("animate");
            }

            if(Modernizr.csstransforms3d) {
                container.css("transform", "translate3d("+ percent +"%,0,0) scale3d(1,1,1)");
            }
            else if(Modernizr.csstransforms) {
                container.css("transform", "translate("+ percent +"%,0)");
            }
            else {
                var px = ((pane_width*pane_count) / 100) * percent;
                container.css("left", px+"px");
            }
        }

        this.next = function() { return this.showPane(current_pane+1, true); };
        this.prev = function() { return this.showPane(current_pane-1, true); };



        function handleHammer(ev) {
            //console.log(ev);
            // disable browser scrolling
            ev.gesture.preventDefault();

            switch(ev.type) {
                case 'dragright':
                case 'dragleft':
                    // stick to the finger
                    var pane_offset = -(100/pane_count)*current_pane;
                    var drag_offset = ((100/pane_width)*ev.gesture.deltaX) / pane_count;

                    // slow down at the first and last pane
                    if((current_pane == 0 && ev.gesture.direction == "right") ||
                        (current_pane == pane_count-1 && ev.gesture.direction == "left")) {
                        drag_offset *= .4;
                    }

                    setContainerOffset(drag_offset + pane_offset);
                    break;

                case 'swipeleft':
                    self.next();
                    ev.gesture.stopDetect();
                    break;

                case 'swiperight':
                    self.prev();
                    ev.gesture.stopDetect();
                    break;

                case 'release':
                    // more then 50% moved, navigate
                    if(Math.abs(ev.gesture.deltaX) > pane_width/2) {
                        if(ev.gesture.direction == 'right') {
                            self.prev();
                        } else {
                            self.next();
                        }
                    }
                    else {
                        self.showPane(current_pane, true);
                    }
                    break;
            }
        }

        var hammertime = new Hammer(element[0], { drag_lock_to_axis: true });
        hammertime.on("release dragleft dragright swipeleft swiperight", handleHammer);
    }
	
	var classmates = <?php echo $classmates;?>;
	for( var i in classmates){
		var img = $('<img class="smallface" />')
			.attr('paneIndex', (parseInt(i)+1))
			.attr('src', classmates[i]['smallface'])
			.attr('alt', classmates[i]['name']);
		var li=$('<li></li>').append(img);
		$('ul.child-index').append(li);
		
		var img2 = $('<img class="bigface" />')
			.attr('src', classmates[i]['face'])
			.attr('alt', classmates[i]['name']);
		var li2 =$('<li class="bigpane singlechild"></li>')
			.addClass('pane'+Math.ceil(Math.random()*5))
			.append(img2)
			.append('<h2>'+classmates[i]['name']+'</h2>')
			.append('<h3>'+classmates[i]['gender']+' '+classmates[i]['age']+'</h3>');
		$('ul#carousel-ul').append(li2);
	}

    var carousel = new Carousel("#carousel");
    carousel.init();
	carousel.showPane(0);
	
    $('img.smallface').click(function(){
		carousel.showPane($(this).attr('paneIndex'), 200);
	});

    </script>	