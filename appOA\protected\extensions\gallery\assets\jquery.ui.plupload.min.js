(function(c){var d={};function a(e){return plupload.translate(e)||e}function b(f,e){e.contents().each(function(g,h){h=c(h);if(!h.is(".plupload")){h.remove()}});e.prepend('<div class="plupload_wrapper"><div id="'+f+'_container" class="ui-widget-content plupload_container"><div class="plupload"><div class="ui-state-default ui-widget-header plupload_header"><div class="plupload_header_content"><div class="plupload_header_title">'+a("Select files")+'</div><div class="plupload_header_text">'+a("Add files to the upload queue and click the start button.")+'</div></div></div><div class="plupload_content"><table class="plupload_filelist"><tr class="ui-widget-header plupload_filelist_header"><td class="plupload_cell plupload_file_name">'+a("Filename")+'</td><td class="plupload_cell plupload_file_status">'+a("Status")+'</td><td class="plupload_cell plupload_file_size">'+a("Size")+'</td><td class="plupload_cell plupload_file_action">&nbsp;</td></tr></table><div class="plupload_scroll"><table class="plupload_filelist" id="'+f+'_filelist"></table></div><table class="plupload_filelist"><tr class="ui-widget-header ui-widget-content plupload_filelist_footer"><td class="plupload_cell plupload_file_name"><div class="plupload_buttons"><!-- Visible --><a id="'+f+'_browse" class="plupload_button plupload_add plupload ui-button ui-widget ui-state-default ui-corner-all ui-button-text-icon"><span class="ui-button-icon-primary ui-icon ui-icon-circle-plus"></span><span class="ui-button-text">'+a("Add Files")+'</span></a><a class="plupload_button plupload_start ui-button ui-widget ui-state-default ui-corner-all ui-button-text-icon"><span class="ui-button-icon-primary ui-icon ui-icon-circle-arrow-e"></span><span class="ui-button-text">'+a("Start Upload")+'</span></a></div><div class="plupload_started plupload_hidden"><!-- Hidden --><div class="plupload_progress plupload_right"><div class="plupload_progress_container ui-progressbar ui-widget ui-widget-content ui-corner-all"><div class="plupload_progress_bar ui-progressbar-value ui-widget-header ui-corner-left"></div></div></div><div class="plupload_cell plupload_upload_status"></div><div class="plupload_clearer">&nbsp;</div></div></td><td class="plupload_file_status"><span class="plupload_total_status">0%</span></td><td class="plupload_file_size"><span class="plupload_total_file_size">400 kb</span></td><td class="plupload_file_action"></td></tr></table></div></div></div><input id="'+f+'_count" name="'+f+'_count" value="0" type="hidden"></div>')}c.fn.pluploadQueue=function(e){if(e){this.each(function(){var j,i,k;i=c(this);k=i.attr("id");if(!k){k=plupload.guid();i.attr("id",k)}j=new plupload.Uploader(c.extend({dragdrop:true,container:k},e));d[k]=j;function h(m){var n,l;if(m.status==plupload.DONE){n="plupload_done";l="ui-icon ui-icon-circle-check"}if(m.status==plupload.FAILED){n="ui-state-error plupload_failed";l="ui-icon ui-icon-alert"}if(m.status==plupload.QUEUED){n="plupload_delete";l="ui-icon ui-icon-circle-minus"}if(m.status==plupload.UPLOADING){n="ui-state-highlight plupload_uploading";l="ui-icon ui-icon-circle-arrow-w"}n+=" ui-state-default plupload_file";c("#"+m.id).attr("class",n).find(".ui-icon").attr("class",l)}function f(){c(".plupload_total_status",i).html(j.total.percent+"%");c(".plupload_progress_bar",i).css("width",j.total.percent+"%");c(".plupload_upload_status",i).text("Uploaded "+j.total.uploaded+"/"+j.files.length+" files");if(j.total.uploaded==j.files.length){j.stop()}}function g(){var m=c("#"+k+"_filelist",i).html(""),n=0,l;c.each(j.files,function(p,o){l="";if(o.status==plupload.DONE){if(o.target_name){l+='<input type="hidden" name="'+k+"_"+n+'_tmpname" value="'+plupload.xmlEncode(o.target_name)+'" />'}l+='<input type="hidden" name="'+k+"_"+n+'_name" value="'+plupload.xmlEncode(o.name)+'" />';l+='<input type="hidden" name="'+k+"_"+n+'_status" value="'+(o.status==plupload.DONE?"done":"failed")+'" />';n++;c("#"+k+"_count").val(n)}m.append('<tr class="ui-state-default plupload_file" id="'+o.id+'"><td class="plupload_cell plupload_file_name"><span>'+o.name+'</span></td><td class="plupload_cell plupload_file_status">'+o.percent+'%</td><td class="plupload_cell plupload_file_size">'+plupload.formatSize(o.size)+'</td><td class="plupload_cell plupload_file_action"><div class="ui-icon"></div>'+l+"</td></tr>");h(o);c("#"+o.id+".plupload_delete .ui-icon").click(function(q){c("#"+o.id).remove();j.removeFile(o);q.preventDefault()})});c(".plupload_total_file_size",i).html(plupload.formatSize(j.total.size));if(j.total.queued===0){c(".plupload_add_text",i).text(a("Add files."))}else{c(".plupload_add_text",i).text(j.total.queued+" files queued.")}c(".plupload_start",i).toggleClass("ui-state-disabled",j.files.length==(j.total.uploaded+j.total.failed));m[0].scrollTop=m[0].scrollHeight;f();if(!j.files.length&&j.features.dragdrop&&j.settings.dragdrop){c("#"+k+"_filelist").append('<tr><td class="plupload_droptext">'+a("Drag files here.")+"</td></tr>")}}j.bind("UploadFile",function(l,m){c("#"+m.id).addClass("ui-state-highlight plupload_current_file")});j.bind("Init",function(l,m){b(k,i);if(!e.unique_names&&e.rename){c("#"+k+"_filelist .plupload_file_name span",i).live("click",function(s){var q=c(s.target),o,r,n,p="";o=l.getFile(q.parents("li")[0].id);n=o.name;r=/^(.+)(\.[^.]+)$/.exec(n);if(r){n=r[1];p=r[2]}q.hide().after('<input type="text" />');q.next().val(n).focus().blur(function(){q.show().next().remove()}).keydown(function(u){var t=c(this);if(u.keyCode==13){u.preventDefault();o.name=t.val()+p;q.text(o.name);t.blur()}})})}c(".plupload_add",i).attr("id",k+"_browse");l.settings.browse_button=k+"_browse";if(l.features.dragdrop&&l.settings.dragdrop){l.settings.drop_element=k+"_filelist";c("#"+k+"_filelist").append('<tr><td class="plupload_droptext">'+a("Drag files here.")+"</td></tr>")}c("#"+k+"_container").attr("title","Using runtime: "+m.runtime);c(".plupload_start",i).click(function(n){if(!c(this).hasClass("ui-state-disabled")){j.start()}n.preventDefault()});c(".plupload_stop",i).click(function(n){j.stop();n.preventDefault()});c(".plupload_start",i).addClass("ui-state-disabled")});j.init();if(e.setup){e.setup(j)}j.bind("Error",function(l,o){var m=o.file,n;if(m){n=o.message;if(o.details){n+=" ("+o.details+")"}c("#"+m.id).attr("class","ui-state-error plupload_failed").find(".ui-icon").attr({"class":"ui-icon ui-icon-alert",title:n})}});j.bind("StateChanged",function(){if(j.state===plupload.STARTED){c(".plupload_start,.plupload_add",i).addClass("ui-state-disabled");c(".plupload_buttons,.plupload_started,.plupload_progress",i).removeClass("plupload_hidden");c(".plupload_upload_status",i).text("Uploaded "+j.total.uploaded+"/"+j.files.length+" files");c(".plupload_header_content",i).addClass("plupload_header_content_bw")}else{if(e.multiple_queues){c(".plupload_start,.plupload_add",i).removeClass("ui-state-disabled")}c(".plupload_progress",i).toggleClass("plupload_hidden");g()}});j.bind("QueueChanged",g);j.bind("FileUploaded",function(l,m){h(m)});j.bind("UploadProgress",function(l,m){c("#"+m.id+" .plupload_file_status",i).html(m.percent+"%");h(m);f()})});return this}else{return d[c(this[0]).attr("id")]}};if(c.ui&&c.widget){c.widget("ui.plupload",{options:{},bak:null,_create:function(){this.bak=this.element.html();this.element.pluploadQueue(this.options)},getUploader:function(){return this.element.pluploadQueue()},destroy:function(){this.getUploader().events={};c(".plupload_start, .plupload_stop",this.element).unbind();this.element.html(this.bak);c.Widget.prototype.destroy.apply(this)}})}else{c.fn.plupload=function(e){if(typeof e=="string"){switch(e){case"getUploader":return this.pluploadQueue();case"destroy":this.pluploadQueue().events={};c(".plupload_start, .plupload_stop",this).unbind();this.html(this.data("plupload_bak"));c.removeData("plupload_bak");break}}else{this.data("plupload_bak",this.html());this.pluploadQueue(e)}}}})(jQuery);