<?php

/**
 * This is the model class for table "ivy_reg_car_site".
 *
 * The followings are the available columns in table 'ivy_reg_car_site':
 * @property integer $id
 * @property integer $reg_id
 * @property string $name_cn
 * @property string $name_en
 * @property string $park_address_cn
 * @property string $park_address_en
 * @property string $site_name
 * @property integer $longitude
 * @property integer $latitude
 * @property integer $status
 * @property string $schoolid
 * @property integer $updated_at
 * @property integer $updated_by
 * @property integer $created_at
 * @property integer $created_by
 * @property integer $yid
 */
class RegCarSite extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_reg_car_site';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
            array('reg_id, name_cn, name_en, park_address_cn, park_address_cn, site_name, longitude, latitude, status, schoolid, updated_at, updated_by, created_at, created_by', 'required'),
			array('reg_id, status, updated_at, updated_by, created_at, created_by', 'numerical', 'integerOnly'=>true),
			array('site_name, name_cn, name_en, park_address_cn, park_address_en, schoolid, longitude, latitude', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, reg_id, site_name, longitude, latitude, status, schoolid, updated_at, updated_by, created_at, created_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'reg_id' => 'Reg',
			'name_cn' => 'Input Name',
			'name_en' => 'Input Name',
			'park_address_cn' => 'park_address_cn',
			'park_address_en' => 'park_address_cn',
			'site_name' => 'Site Name',
			'longitude' => 'Longitude',
			'latitude' => 'Latitude',
			'status' => 'Status',
			'schoolid' => 'Schoolid',
			'updated_at' => 'Updated At',
			'updated_by' => 'Updated By',
			'created_at' => 'Created At',
			'created_by' => 'Created By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('reg_id',$this->reg_id);
		$criteria->compare('name_cn',$this->name_cn,true);
		$criteria->compare('name_en',$this->name_en,true);
		$criteria->compare('park_address_cn',$this->park_address_cn,true);
		$criteria->compare('park_address_en',$this->park_address_en,true);
		$criteria->compare('site_name',$this->site_name,true);
		$criteria->compare('longitude',$this->longitude);
		$criteria->compare('latitude',$this->latitude);
		$criteria->compare('status',$this->status);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('updated_at',$this->updated_at);
		$criteria->compare('updated_by',$this->updated_by);
		$criteria->compare('created_at',$this->created_at);
		$criteria->compare('created_by',$this->created_by);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return RegCarSite the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	public static function getCarsite($regId, $schoolid)
	{
		// 映射关系，传入的schoolid是否使用其他学校配置
		$config = array('BJ_IASLT' => 'BJ_SLT');

		$_schoolid = $schoolid;
		$_regId = $regId;
		if (isset($config[$schoolid])) {
			$_schoolid = $config[$schoolid];
			$reg = Reg::model()->findByPk($regId);
			if (!$reg) {
				return false;
			}
			$newReg = Reg::model()->findByAttributes(array(
				'startyear' => $reg->startyear,
				'schoolid' => $_schoolid,
			));
			if (!$newReg) {
				return false;
			}
			$_regId = $newReg->id;
		}
		$criteria = new CDbCriteria();
		$criteria->compare('reg_id', $_regId);
		$criteria->compare('schoolid', $_schoolid);
		$models = RegCarSite::model()->findAll($criteria);
		return $models;
	}
}
