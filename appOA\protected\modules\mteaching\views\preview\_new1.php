<style>
.spinner {
  margin: 100px auto;
  width: 90px;
  height: 90px;
  position: relative;
  text-align: center; 
  -webkit-animation: rotate 2.0s infinite linear;
  animation: rotate 2.0s infinite linear;
  z-index:99;
}
 .conter{
   background:#000;
   opacity:0.2;
   width:100%;
   height:100%;
   position:fixed;
   left:0;
   top:0;
   z-index:9
 }
.dot1, .dot2 {
  width: 60%;
  height: 60%;
  display: inline-block;
  position: absolute;
  top: 0;
  background-color: #428BCA;
  border-radius: 100%;
   
  -webkit-animation: bounce 2.0s infinite ease-in-out;
  animation: bounce 2.0s infinite ease-in-out;
}
 
.dot2 {
  top: auto;
  bottom: 0px;
  -webkit-animation-delay: -1.0s;
  animation-delay: -1.0s;
}
 
.option{
    word-wrap: break-word;
    overflow: hidden;
    /* word-break: break-all; */
    /* text-align:center */
}
@-webkit-keyframes rotate { 100% { -webkit-transform: rotate(360deg) }}
@keyframes rotate { 100% { transform: rotate(360deg); -webkit-transform: rotate(360deg) }}
 
@-webkit-keyframes bounce {
  0%, 100% { -webkit-transform: scale(0.0) }
  50% { -webkit-transform: scale(1.0) }
}
 
@keyframes bounce {
  0%, 100% {
    transform: scale(0.0);
    -webkit-transform: scale(0.0);
  } 50% {
    transform: scale(1.0);
    -webkit-transform: scale(1.0);
  }
}
.btnWidth{
    width:160px
}
.btnWidth100 {
    width: 100%;
}
.width2{
    display:block
}
.width12{
    display:none
}
@media screen and (max-width: 1200px) {
    .btnWidth {
        width:100px
    }
    .width12{
        display:block
    }
    .width2{
        display:none
    }
}
</style>
<div class="col-md-12 width12 mb10">
    <select class="form-control select_3" id='classData' onchange="switchCourse()">
        <option value=""><?php echo Yii::t("global", "Please Select");?></option>
        <?php foreach ($data['classData'] as $classid => $title) {
            echo '<option value="'.$classid.'">'.$title.'</option>';
        } ?>
    </select>
</div>
<div class="col-md-2 width2">
    <div class="list-group">
        <?php foreach ($data['classData'] as $classid => $title) {
            echo '<span class="list-group-item" onclick="switchCourse(this, ' . $classid. ')">';
            echo $title;
            echo '</span>';
        } ?>
    </div>
</div>

<div class="col-md-12 col-lg-10"  style="display: none" id="student_style">
    <div class='box'>
    <div class='conter'></div>
    <div class="spinner">
    <div class="dot1"></div>
    <div class="dot2"></div>
    </div>
    </div>
    <div class='fs-demo fs-whatwg' id='fs-whatwg' style='max-width:100%;overflow:auto'>
        <table class="table table-bordered hidden " id="student-table" style='table-layout:fixed;width:100%;'>
            <thead class='thead'>  
            </thead>
            <tbody>          
            </tbody>
        </table>
    </div> 
</div>
<script>
    var report = <?php echo CJSON::encode($report); ?>;
    var data = <?php echo CJSON::encode($data); ?>;
    var childids;
    var childCourse;
    var classid_global;
    function switchCourse(_this, classid) {
        $(".fs-whatwg").floatingScroll();
    	$('.thead').html('')
        childids = [];
        $('#student-table').removeClass('hidden');
        $('#student-table tbody').empty();
        $("#student_style").css('display','block')
        $('.list-group-item').removeClass('active');
        if(_this){
            $(_this).addClass('active');
            $('#classData').val(classid)
        }else{
            classid=$('#classData').val()
            Object.keys(data.classData).forEach(function(key,index){
                if(classid==key){
                    let thisData=$('.list-group-item')[index]
                    $(thisData).addClass('active');
                }
            });
        }
        
        $.ajax({
            type: 'post',
            url: '<?php echo $this->createUrl('showChildReport');?>',
            dataType: 'json',
            data: {classid,yid,semester},
            async: true,
             beforeSend: function(){    
           $('.box').show()
           
            },   
            complete: function(){    
             $('.box').hide()
            },
            success:function(data){
                var class_type = data.class_type;
                var str=''
                var tbody=''
                online='online'
                offline='offline'
                str+='<tr><th width="70" rowspan=2 class="text-center"><?php echo Yii::t("campus", "Students List")?></th><th width="140" rowspan=2><?php echo Yii::t("principal", "Course Title")?></th>'
                for(var i=0;i<data.stand.length;i++){
                    if(data.stand.length>3){
                        if(data.cycle=='4'){
                            if(i==5){
                                str+='<th  width="70"  rowspan=2 colspan=2 >'+data.stand[i]+'</th>'
                            }else if(i==4 || i==6){
                                str+='<th  width="300"  rowspan=2 colspan=2 >'+data.stand[i]+'</th>'
                            }else{
                                str+='<th  width="100" colspan=2  >'+data.stand[i]+'</th>'
                            }
                        }else{
                            if(i+1==data.stand.length){
                                str+='<th  width="300"  rowspan=2 colspan=2 >'+data.stand[i]+'</th>'
                            }else{
                                str+='<th  width="50"  rowspan=2 colspan=2 >'+data.stand[i]+'</th>'
                            }
                        }
                    }else if(data.sample_progerss){
                        str+='<th  width="100" colspan=2 >'+data.stand[i]+'</th>'
                    }else{
                        if(data.cycle=='4'){
                            if(i==0){
                                str+='<th  width="100" colspan=2  >'+data.stand[i]+'</th>'
                            }else if(i==1){
                                if(class_type=='e11' || class_type=='e12'){
                                    str+='<th  width="500" rowspan=2 colspan=2  >'+data.stand[i]+'</th>'
                                }else{
                                    str+='<th  width="300" rowspan=2 colspan=2  >'+data.stand[i]+'</th>'
                                }

                            }else{
                                str+='<th  width="100" rowspan=2 colspan=2  >'+data.stand[i]+'</th>'
                            }
                        }else{
                            if(i+1==data.stand.length){
                                str+='<th  width="300" colspan=2 >'+data.stand[i]+'</th>'
                            }else{
                                str+='<th  width="50" colspan=2 >'+data.stand[i]+'</th>'
                            }
                        }
                    }
                }
                if(data.cycle=='4'&& (class_type=='e11' || class_type=='e12')){
                    str+='<th  rowspan=2 width="500" ><?php echo Yii::t("teaching", "Other Comments")?></th>'
                }else{
                    if(data.sample_progerss){
                        str+='<th  rowspan=2 width="10" ><?php echo Yii::t("teaching", "-")?></th>'
                    } else {
                        str+='<th  rowspan=2 width="300" ><?php echo Yii::t("teaching", "Other Comments")?></th>'
                    }
                }
                if(data.cycle == '4' && (class_type !='e11' || class_type!='e12') ){
                    str+= '<td  rowspan=2 class="text-center" width="245">';
                }else{
                    str+='<td  rowspan=2 class="text-center btnWidth">';

                }
                str+='<button class="btn btn-success btn-sm mb5" onclick="allOnline()"><?php echo Yii::t("teaching", "All Online")?></button> '+
                '<button class="btn btn-danger btn-sm mb5" onclick="allOffline()"><?php echo Yii::t("teaching", "All Offline")?></button>'+
                '</td></tr>'
                if(data.cycle=='4'){
                    str+='<tr>'
                    if(data.stand.length>4){
                        for(var i=0;i<4;i++){
                            str+='<th class="text-center"><?php echo Yii::t("teaching","4th period score") ?></th>'
                            str+='<th class="text-center"><?php echo Yii::t("teaching","Final Score") ?></th>'
                        }
                    }else{
                        str+='<th class="text-center"><?php echo Yii::t("teaching","4th period score") ?></th>'
                        str+='<th class="text-center"><?php echo Yii::t("teaching","Final Score") ?></th>'
                    }
                    
                    str+='</tr>'
                }
                for (a in data.item) {
                    childid=a
                    childids.push(a)
                    if(data.item[a].course_num==0){
                        tbody+='<tr>'+
                               '<td><dl><dt class="text-center"> <img style="width:50px" class="img-rounded" src='+data.item[a].child_photo+' alt=""></dt><dd style="padding-top:10px" class="text-center">'+data.item[a].child_name+'<dd></dl></td>'+
                               '<td>-</td>'
                        for(var i=0;i<data.stand.length;i++){
                            tbody+='<td  colspan=2  >-</td>'
                        }
                        if((data.item[a].counselor==null || data.item[a].counselor=='') && (data.item[a].college_counseling==null || data.item[a].college_counseling=='') && (data.item[a].social_innovation==null || data.item[a].social_innovation=='')){
                            tbody+='<td>-</td>'
                        }else{
                            tbody+='<td>'
                                if(data.item[a].counselor!=null && data.item[a].counselor!=''){
                                    tbody+='<p><strong><?php echo Yii::t("teaching", "Comment from advisor")?></strong></p>'
                                    tbody+='<p class="mb20">'+ data.item[a].counselor+'</p>'
                                }
                                if(data.item[a].college_counseling!=null && data.item[a].college_counseling!=''){
                                    tbody+='<p><strong><?php echo Yii::t("teaching", "College Counseling")?></strong></p>'
                                    tbody+='<p class="mb20">'+ data.item[a].college_counseling+'</p>'
                                } 
                                if(data.item[a].social_innovation!=null && data.item[a].social_innovation!=''){
                                    tbody+='<p><strong><?php echo Yii::t("teaching", "Social Innovation")?></strong></p>'
                                    tbody+='<p class="mb20">'+ data.item[a].social_innovation+'</p>'
                                }
                            tbody+= '</td>'
                        }
                         tbody+='<td class="text-center">'+ '<button class="btn btn-info btn-sm mb5" onclick="reportPreview('+childid+','+semester+','+classid+','+yid+')"><?php echo Yii::t("teaching", "Preview")?></button> '+ '<span id="status_'+childid+'">'

                        if(data.item[a].is_stat == 1){
                            tbody+='<button class="btn btn-danger btn-sm mb5" onclick="setReportStatus('+childid+','+offline+',this)"><?php echo Yii::t("teaching", "Make Offline")?></button>'
                            if (data.item[a].is_cache != 1) {
                                tbody+="<br/><?php echo Yii::t('teaching','PDF being generated');?>"; 
                            }
                        }else{
                            tbody+= '<button class="btn btn-success btn-sm mb5" onclick="setReportStatus('+childid+','+online+',this)"><?php echo Yii::t("teaching", "Make Online")?></button>'
                        }

                        tbody+='</span></td>'
                        tbody+='</tr>'
                    }else{
                        tbody+='<tr><td rowspan='+data.item[a].course_num+' width="70" class="text-center"><dl><dt class="text-center"> <img style="width:50px" class="img-rounded" src='+data.item[a].child_photo+' alt=""></dt><dd style="padding-top:10px" class="text-center">'+data.item[a].child_name+'<dd></dl></td>'
                        if(data.item[a].courseList.length!=0){
                            for (var i=0;i<data.item[a].courseList.length;i++) {
                                tbody+='<td>'+data.item[a].courseList[i].title+'</td>'
                                var len=Object.keys(data.item[a].courseList[i].items)
                                if(len.length<data.stand.length){
                                    for(var k=0;k<data.stand.length-len.length;k++){
                                        data.item[a].courseList[i].items['999999'+k]='-'
                                    }
                                }
                                var keys = Object.keys(data.item[a].courseList[i].items);
                                keys.forEach(function(j, index) {
                                    if(data.item[a].courseList[i].items[j]==null){
                                         tbody+='<td  colspan=2  class="option" width="50">-uu</td>'
                                    }else{
                                        if(data.item[a].courseList[i].final){
                                            if(data.item[a].courseList[i].final.length!=0){
                                                if(data.item[a].courseList[i].final[j]){
                                                    if(data.item[a].finalScoresData[data.item[a].courseList[i].final[j]]){
                                                        tbody+='<td width="90" class="option" >'+
                                                            '<span>'+data.item[a].courseList[i].items[j]+'</span>'+
                                                        '</td>'+
                                                        '<td width="90" class="option" >'+
                                                            '<span>'+data.item[a].finalScoresData[data.item[a].courseList[i].final[j]]+'</span>'+
                                                        '</td>'
                                                    }else{
                                                        tbody+='<td width="90" class="option" >'+data.item[a].courseList[i].items[j]+'</td>'+
                                                                '<td width="90" class="option" >'+
                                                                    '<span>'+data.item[a].courseList[i].final[j]+'</span>'+
                                                                '</td>'
                                                    }
                                                }else{
                                                    if(data.item[a].courseList[i].items[j]==""){
                                                        tbody+='<td  colspan=2 class="option" width="50">-</td>'
                                                    }else{
                                                        if(index==keys.length){
                                                            tbody+='<td colspan=2 class="option" width="300" style="text-align:left">'+data.item[a].courseList[i].items[j]+'</td>'
                                                        }else{
                                                            if((class_type=='e11' || class_type=='e12') && j =='oprion_value' && data.cycle == '4'){
                                                                //11 12年级不显示最终分数
                                                            }else{
                                                                tbody += '<td width="50" colspan=2 class="option" >' + data.item[a].courseList[i].items[j] + '</td>'
                                                            }
                                                        }
                                                        // tbody+='<td  colspan=2 class="option" width="50">'+data.item[a].courseList[i].items[j]+'</td>'
                                                    }
                                                }
                                            }else{
                                               if(index<4){
                                                   if( data.cycle == '4' && ( class_type == 'e11' || class_type=='e12')){
                                                       //第四期 11 12 年级不用显示最终分数  9999996代表空的最总分数
                                                       if(j!='9999996'){
                                                           if(index == 1){
                                                               tbody+= '<td colspan=2 width="70" class="option" >-</td>'
                                                           }else {
                                                               tbody+='<td width="90" class="option" >'+data.item[a].courseList[i].items[j]+'</td>'+
                                                                   '<td width="90" class="option" >-</td>'
                                                           }
                                                       }
                                                   }else{
                                                       tbody+='<td width="90" class="option" >'+data.item[a].courseList[i].items[j]+'</td>'+
                                                           '<td width="90" class="option" >-</td>'
                                                   }
                                                }else{
                                                    tbody+='<td colspan=2  width="70" class="option" >'+data.item[a].courseList[i].items[j]+'</td>'
                                                }

                                            }
                                        }else{
                                            if(index==keys.length){
                                                tbody+='<td colspan=2 class="option" width="300" style="text-align:left">'+data.item[a].courseList[i].items[j]+'</td>'
                                            }else{
                                                tbody+='<td width="50" colspan=2 class="option" >'+data.item[a].courseList[i].items[j]+'</td>'
                                            }                                   
                                        }
                                    }
                                })
                                if(i==0){
                                    if((data.item[a].counselor==null || data.item[a].counselor=='') && (data.item[a].college_counseling==null || data.item[a].college_counseling=='') && (data.item[a].social_innovation==null || data.item[a].social_innovation=='')){
                                        tbody+='<td rowspan='+data.item[a].course_num+'>-</td>'
                                    }else{
                                        tbody+='<td rowspan='+data.item[a].course_num+'>'
                                            if(data.item[a].counselor!=null && data.item[a].counselor!=''){
                                                tbody+='<p><strong><?php echo Yii::t("teaching", "Comment from advisor")?></strong></p>'
                                                tbody+='<p class="mb20">'+ data.item[a].counselor+'</p>'
                                            }
                                            if(data.item[a].college_counseling!=null && data.item[a].college_counseling!=''){
                                                tbody+='<p><strong><?php echo Yii::t("teaching", "College Counseling")?></strong></p>'
                                                tbody+='<p  class="mb20">'+ data.item[a].college_counseling+'</p>'
                                            } 
                                            if(data.item[a].social_innovation!=null && data.item[a].social_innovation!=''){
                                                tbody+='<p><strong><?php echo Yii::t("teaching", "Social Innovation")?></strong></p>'
                                                tbody+='<p  class="mb20">'+ data.item[a].social_innovation+'</p>'
                                            }
                                        tbody+= '</td>'
                                    }
                                    if(data.cycle == '4' && (class_type =='e11' || class_type =='e12')){
                                        tbody+='<td rowspan='+data.item[a].course_num+'  class="text-center btnWidth" colspan="4">'+ '<button class="btn btn-info btn-sm mb5" onclick="reportPreview('+childid+','+semester+','+classid+','+yid+')"><?php echo Yii::t("teaching", "Preview")?></button> '+ '<span id="status_'+childid+'">'
                                    }else{
                                        tbody+='<td rowspan='+data.item[a].course_num+'  class="text-center btnWidth">'+ '<button class="btn btn-info btn-sm mb5" onclick="reportPreview('+childid+','+semester+','+classid+','+yid+')"><?php echo Yii::t("teaching", "Preview")?></button> '+ '<span id="status_'+childid+'">'
                                    }
                                    if(data.item[a].is_stat == 1){
                                        tbody+='<button class="btn btn-danger btn-sm mb5" onclick="setReportStatus('+childid+','+offline+',this)"><?php echo Yii::t("teaching", "Make Offline")?></button>'
                                        if (data.item[a].is_cache != 1) {
                                            tbody+="<br/><?php echo Yii::t('teaching','PDF being generated');?>"; 
                                        }
                                    }else{
                                        tbody+= '<button class="btn btn-success btn-sm mb5" onclick="setReportStatus('+childid+','+online+',this)"><?php echo Yii::t("teaching", "Make Online")?></button>'
                                    }
                                    tbody+='</span></td>'
                                }
                                tbody+='</tr>'
                            }
                        } else{
                            tbody+='<td >-</td>'
                            for(var i=0;i<data.stand.length;i++){
                                tbody+='<td  colspan=2 >-</td>'
                            }
                        }
                    }
                }
            $('.thead').html(str)     
            $('#student-table tbody').html(tbody)
            $(".fs-whatwg").floatingScroll("update");
            }
        })
    }
    function setReportStatus(childid, status,obj) {   
        $(obj).attr('disabled', true);
      $.ajax({
            type: 'post',
            url: '<?php echo $this->createUrl('saveReportMessage');?>',
            dataType: 'json',
            data: {semester, yid, childid, status:status}
        }).done(function(data){
            if(data.state == 'success') {
                if(data.data == 1){
                    var html = '<button class="btn btn-danger btn-sm" onclick="setReportStatus(' + childid + ','+offline+',this)"><?php echo Yii::t("teaching", "Make Offline")?></button>';
                }else{
                    var html = '<button class="btn btn-success btn-sm" onclick="setReportStatus(' + childid + ', '+online+',this)"><?php echo Yii::t("teaching", "Make Online")?></button>';
                }
                $(obj).attr('disabled', false);
                $("#status_"+childid).html(html)
            }else{
                resultTip({msg: data.message, error: 1});
            }
        });
    }
    
    function reportPreview(childid, report_id, classid, yid) {
        var url = "<?php echo $this->createUrl('newPreviewReportDs', array(
            'childid'=>'-childid-',
            'report_id'=>'-report_id-',
            'classid'=>'-classid-',
            'yid'=>'-yid-',
        ));?>";
        url = url.replace('-childid-', childid);
        url = url.replace('-report_id-', report_id);
        url = url.replace('-classid-', classid);
        url = url.replace('-yid-', yid);
        window.open(url);
    }

    function allOnline() {
        $('.modal-body p').text('');
        $('#process-modal').modal('show');
        $('.modal-footer button').attr('disabled', true);
        $('.modal-title').text('<?php echo Yii::t("teaching", "Make Online")?>');
        Online(0, 'online');
    }

    function allOffline() {
        $('.modal-body p').text('');
        $('#process-modal').modal('show');
        $('.modal-footer button').attr('disabled', true);
        $('.modal-title').text('<?php echo Yii::t("teaching", "Make Offline")?>');
        Online(0, 'offline');
    }

    function Online(i, status) {
        var childid = childids[i];
        $.ajax({
            type: 'post',
            url: '<?php echo $this->createUrl('saveReportMessage');?>',
            dataType: 'json',
            data: {semester, yid, childid, status}
        }).done(function(data){
            if(data.state == 'success') {
                if(data.data == 1){
                    var html = '<button class="btn btn-danger btn-sm" onclick="setReportStatus(' + childid + ', '+offline+',this)"><?php echo Yii::t("teaching", "Make Offline")?></button>';
                }else{
                    var html = '<button class="btn btn-success btn-sm" onclick="setReportStatus(' + childid + ', '+online+',this)"><?php echo Yii::t("teaching", "Make Online")?></button>';
                }
                $("#status_"+childid).html(html);

                var percent = ((i+1) / (childids.length))*100;
                $('.progress-bar').attr('aria-valuenow', percent).css('width', percent + '%');
                $('.complete').text(i+1 +'/'+ (childids.length));
            }
            if ((i+1) == childids.length) {
                $('.modal-body p').text(data.message);
                $('.modal-footer button').attr('disabled', false);
                return true;
            }
            if (childids[i + 1])
                return Online(i + 1, status);
        });
    }

</script>