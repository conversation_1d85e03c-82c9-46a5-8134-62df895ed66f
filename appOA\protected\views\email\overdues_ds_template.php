<div style="color:#274257;">
    <div style="font-family: 'Microsoft Yahei'">
        <p>尊敬的家长:</p>
        <?php if (isset($invoiceList['overdues']) && count($invoiceList['overdues']) == count($invoiceList['info'])){?>
<!--------------------------------------------------逾期的--------------------------------->
        <?php echo $email_view['overdue']['cn']['message1'];?>
        <?php $this->renderPartial('email_overdues_detail', array('language'=>'cn','name' => $name, 'invoiceList' => $invoiceList,'allbranches'=>$allbranches,'schoolid'=>$schoolid)) ?>
        <?php echo $email_view['overdue']['cn']['message2']; ?>
        <?php echo $email_view['overdue']['cn']['message3'];?>
<!------------------------------------------------逾期提示结束----------------------------------------------------------------------->
        <?php }elseif (isset($invoiceList['unpay']) && count($invoiceList['unpay']) == count($invoiceList['info'])){?>
<!----------------------------------------------未逾期提示----------------------------------------------------------------------->
            <?php echo $email_view['not_overdue']['cn']['message1'];?>
            <?php $this->renderPartial('email_overdues_detail', array('language'=>'cn','name' => $name, 'invoiceList' => $invoiceList,'allbranches'=>$allbranches,'schoolid'=>$schoolid)) ?>
            <?php echo $email_view['not_overdue']['cn']['message2'];?>
            <?php echo $email_view['not_overdue']['cn']['message3'];?>
<!----------------------------------------------未逾期提示结束----------------------------------------------------------------------->
        <?php }else{?>
<!---------------------------------------------逾期和未逾期的一起发送邮件的提示--------------------------------------------->
            <?php echo $email_view['mix']['cn']['message1'];?>
            <?php $this->renderPartial('email_overdues_detail', array('language'=>'cn','name' => $name, 'invoiceList' => $invoiceList,'allbranches'=>$allbranches,'schoolid'=>$schoolid)) ?>
            <?php echo $email_view['mix']['cn']['message2'];?>
            <?php echo $email_view['mix']['cn']['message3'];?>
<!---------------------------------------------逾期和未逾期的一起发送邮件的提示结束--------------------------------------------->
        <?php }?>
        <p>自动邮件，无需回复
        <p><?php echo OA::formatDateTime(time());?></p>
    </div>
</div>
<?php
echo '<br/><br/>';
?>
<div style="color:#644436;">
    <div style="font-family: 'Trebuchet MS','Microsoft Yahei',Arial,Helvetica,sans-serif">
        <p>Dear Parents,</p>
<!------------------------------逾期的--------------------------------->
        <?php if (isset($invoiceList['overdues']) && count($invoiceList['overdues']) == count($invoiceList['info'])):?>
            <?php echo $email_view['overdue']['en']['message1'];?>
            <?php $this->renderPartial('email_overdues_detail', array('language'=>'en','name' => $name, 'invoiceList' => $invoiceList,'allbranches'=>$allbranches,'schoolid'=>$schoolid)) ?>
            <?php echo $email_view['overdue']['en']['message2'];?>
            <?php echo $email_view['overdue']['en']['message3'];?>
<!------------------------------逾期的结束--------------------------------->
        <?php elseif (isset($invoiceList['unpay']) && count($invoiceList['unpay']) == count($invoiceList['info'])):?>
<!------------------------------未逾期的--------------------------------->
            <?php echo $email_view['not_overdue']['en']['message1'];?>
            <?php $this->renderPartial('email_overdues_detail', array('language'=>'en','name' => $name, 'invoiceList' => $invoiceList,'allbranches'=>$allbranches,'schoolid'=>$schoolid)) ?>
            <?php echo $email_view['not_overdue']['en']['message2'];?>
            <?php echo $email_view['not_overdue']['en']['message3'];?>
<!------------------------------未逾期的结束--------------------------------->
        <?php else:?>
<!--------------------------------逾期和未逾期的一起发送邮件的提示-------------------------------->
            <?php echo $email_view['mix']['en']['message1'];?>
            <?php $this->renderPartial('email_overdues_detail', array('language'=>'en','name' => $name, 'invoiceList' => $invoiceList,'allbranches'=>$allbranches,'schoolid'=>$schoolid)) ?>
            <?php echo $email_view['mix']['en']['message2'];?>
            <?php echo $email_view['mix']['en']['message3'];?>
<!--------------------------------逾期和未逾期的一起发送邮件的提示结束-------------------------------->
        <?php endif;?>
        <p> <?php echo OA::formatDateTime(time());?></p>
    </div>
</div>
<p></p>
<hr>
<div>
    <p style="font-weight: bold;">Bank Information / 银行信息</p>
    <table cellpadding="0" border="0">
        <?php foreach ($bankinfo as $info){?>
            <tr style="padding: 5px 0;">
                <td style="font-family: 'Trebuchet MS','Microsoft Yahei',Arial,Helvetica,sans-serif;font-size: 12px;" valign="top">
                    <?php echo $info['title_en'];?>：<br/>
                    <?php echo $info['title_cn'];?>：
                </td>
                <td style="font-family: 'Trebuchet MS','Microsoft Yahei',Arial,Helvetica,sans-serif;font-size: 12px;">
                    <?php  echo $info['content'];?>
                </td>
            </tr>
        <?php }?>
    </table>
</div>

