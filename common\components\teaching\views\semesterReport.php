<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="language" content="en" />
    <title><?php echo CHtml::encode($this->pageTitle); ?></title>
</head>

<body>

    <?php if($withToolBar):?>
        <div class="navbar navbar-fixed-top navbar-inverse" role="navigation">
            <?php
            echo CHtml::form( $downloadActionUrl );
            echo CHtml::hiddenField('schoolid',     $params['schoolid']);
            echo CHtml::hiddenField('childid',      $params['childid']);
            echo CHtml::hiddenField('id',           $params['id']);
            echo CHtml::submitButton('Download PDF / 下载 PDF', array('style'=>'font-family: Microsoft Yahei'));
            echo CHtml::endForm();
            ?>
        </div>
    <?php endif;?>

    <div class="report-wrapper web-view">
    <!--page one-->
    <div id="page-1" class="report-page page-break-after">
        <!-- page content-->
        <div class="page-content">

            <?php
            if($data['child']->photo){
                echo CHtml::image(CommonUtils::childPhotoUrl($data['child']->photo),
                    null,
                    array('class' => 'profile-photo')
                );
            }
            ?>

            <div class="profile-box">
                <dl>
                    <dt><span class="cn">姓名</span> <span class="en">Name</span></dt>
                    <dd><?php
                        $childName = $data['child']->getChildName();
                        $hasCNChars = CommonUtils::hasCNChars( $childName );

                        if( $hasCNChars > 0){
                            echo '<span class="cn">';
                        }else{
                            echo '<span class="en">';
                        }

                        echo $childName . '</span>';

                        ?></dd>

                    <dt><span class="cn">出生日期</span> <span class="en">DOB</span></dt>
                    <dd><?php echo $data['child']->birthday_search?></dd>

                    <dt><span class="cn">校园</span> <span class="en">Campus</span></dt>
                    <dd><?php
                        $title = $data['campusTitle'];
                        $title = str_replace('(', '(<span class="cn">', $title);
                        $title = str_replace(')', '</span>)', $title);
                        echo $title;
                        ?></dd>

                    <dt><span class="cn">学期</span> <span class="en">Semester</span></dt>
                    <dd>
                        <?php echo IvyClass::formatSchoolYear($data['report']['startyear'])?>
                        <?php echo ($data['report']['semester'] == 1) ?
                            '<span class="cn">秋季</span> <span class="en">Fall</span>' :
                            '<span class="cn">春季</span> <span class="en">Spring</span>'
                        ?>
                    </dd>

                    <dt><span class="cn">班级</span> <span class="en">Class</span></dt>
                    <dd><?php echo $data['classTitle'];?></dd>

                    <dt><span class="cn">班级教师</span> <span class="en">Class Teachers</span></dt>
                    <dd>
                        <?php
                        $listClass = (count($data['teachers']) < 9 ) ? 'a' :(
                        (count($data['teachers']) < 16 ) ? 'b' : 'c'
                        );
                        ?>
                        <div class="teacher-list teacher-list-<?php echo $listClass;?>">
                            <?php
                            $idx = 0;
                            if ($data['teachers']):?>
                                <?php foreach ($data['teachers'] as $tName):?>
                                    <span class="item"><?php echo $tName;?></span>
                                <?php endforeach;?>
                            <?php endif;?>
                        </div>
                        <div style="clear: both"></div>
                    </dd>
                </dl>
            </div>

            <div class="cover-bottom">
                <?php
                if($data['items']['FrontCoverWork']['media_id']){
                    echo CHtml::image($data['photos'][
                        $data['items']['FrontCoverWork']['media_id']
                        ], "", array('class'=>'middle-size')
                    );
                }
                ?>
                <p class="tac"><span class="cn">孩子的作品</span> <span class="en">Your Child's Creation</span></p>

            </div>


        </div><!-- page content -->

        <div class="top-title"><div class="cn">学期报告</div><div class="en">Semester Report</div></div>
        <div class="top-school"><div class="cn">艾毅幼儿园</div><div class="en">IvySchools</div></div>
        <div class="logo-bottom-center"></div>

    </div><!--page one-->

    <!--page 2-->
    <div id="page-2" class="report-page page-break-after">

        <!-- page content -->
        <div class="page-content">
            <h3><span class="cn">优势</span> <span class="en">Strengths</span></h3>
            <?php echo CHtml::image($data['photos'] [
                $data['items']['Strength1']['media_id']
                ], "", array('class'=>'middle-size middle-size-'.$data['items']['Strength1']['langClass'])
            );
            ?>
            <p class="<?php echo $data['items']['Strength1']['langClass']; ?>">

                <?php
                echo '<span class="ld">'.$data['learningDomains'][$data['items']['Strength1']['ld_id']].'</span>';
                echo Yii::app()->format->ntext($data['items']['Strength1']['content']);
                ?></p>


            <?php
            if($data['items']['Strength2']['media_id']){
                echo CHtml::image($data['photos'] [
                    $data['items']['Strength2']['media_id']
                    ], "", array('class'=>'middle-size middle-size-'. $data['items']['Strength2']['langClass'])
                );
            }
            ?>
            <p class="<?php echo $data['items']['Strength2']['langClass']; ?>"><?php
                echo '<span class="ld">'.$data['learningDomains'][$data['items']['Strength2']['ld_id']].'</span>';
                echo Yii::app()->format->ntext($data['items']['Strength2']['content']); ?></p>

        </div><!-- page content -->

        <div class="top-title"><div class="cn">学期报告</div><div class="en">Semester Report</div></div>
<!--        <div class="logo-bottom-center"></div>-->

    </div><!--page 2-->

    <!--page 3-->
    <div id="page-3" class="report-page page-break-after">

        <!-- page content -->
        <div class="page-content">
            <h3><span class="cn">兴趣</span> <span class="en">Interest</span></h3>
            <?php
            if($data['items']['Interest']['media_id']){
                echo CHtml::image($data['photos'] [
                    $data['items']['Interest']['media_id']
                    ], "", array('class'=>'middle-size')
                );
            }
            ?>
            <p class="<?php echo $data['items']['Interest']['langClass']; ?>"><?php echo Yii::app()->format->ntext($data['items']['Interest']['content']); ?></p>

            <h3><span class="cn">概述</span> <span class="en">General Comments</span></h3>
            <p class="<?php echo $data['items']['Comment']['langClass']; ?>"><?php echo Yii::app()->format->ntext($data['items']['Comment']['content']); ?></p>

        </div><!-- page content -->

        <div class="cd-signature">
            _____________________________<br>
            <span class="cn">园长</span> <span class="en">Campus Director</span>
        </div>
<!--        <div class="logo-bottom-center"></div>-->
        <div class="top-title"><div class="cn">学期报告</div><div class="en">Semester Report</div></div>

    </div><!--page 3-->

    <!--page 4-->
    <div id="page-4" class="report-page">
        <!-- page content -->
        <div class="page-content">
            <h3><span class="cn"> &nbsp;</span> <span class="en"> </span></h3>
            <?php
            if($data['items']['BackCoverPhoto']['media_id']){
                echo CHtml::image($data['photos'] [
                    $data['items']['BackCoverPhoto']['media_id']
                    ], "", array('class'=>'middle-size')
                );
            }
            ?>

            <hr class="sep">

            <div class="cn">
                <h3>使命</h3>
                <p>艾毅幼儿园的使命是通过营造良好的教育环境，使每位幼儿都能成为：</p>
                <ul class="mission">
                    <li>自信的人</li>
                    <li>懂得与他人融洽相处的人</li>
                    <li>能够自主解决问题的人</li>
                    <li>能够做出好的选择的人</li>
                    <li>知道自己是有好想法、有创造力的人</li>
                </ul>
            </div>
            <div class="en">
                <h3>Mission</h3>
                <p>The Ivy Schools' mission is to educate and provide an environment which enables each child:</p>
                <ul class="mission">
                    <li>To become self-confident</li>
                    <li>To know how to get along well with others</li>
                    <li>To be self-directed problem solvers</li>
                    <li>To make good choices</li>
                    <li>To see themselves as children with good ideas</li>
                </ul>
            </div>

        </div><!-- page content -->

        <div class="logo-contact-left">
            <span class="phone">************</span>
            <span class="url">www.ivyschools.com</span>
        </div>
        <div class="logo-bottom-right"></div>
        <div class="top-title"><div class="cn">学期报告</div><div class="en">Semester Report</div></div>

    </div><!--page 4-->

    </div>


    <style>
        .en{font-family: Tahoma,'Trebuchet','Utopia';}
        .cn{font-family:'Microsoft Yahei'}
        .report-page{
            padding:0;
            height: 877px;
            position: relative;
            background: url('http://m1.files.ivykids.cn/images/sreport/header.gif') no-repeat;
            background-size: 100%;
            background-color: #ffffff;
        }
        .report-wrapper{width:620px; margin: 0px auto; padding: 0; font-size:10px;}
        .logo-bottom-center{
            width: 160px;
            height: 58px;
            background: url('http://m1.files.ivykids.cn/images/sreport/footer.gif') no-repeat;
            background-size: 100%;
            position: absolute;
            left: 230px;
            bottom: 0;
        }
        .logo-bottom-right{
            width: 160px;
            height: 58px;
            background: url('http://m1.files.ivykids.cn/images/sreport/footer.gif') no-repeat;
            background-size: 100%;
            position: absolute;
            right: 20px;
            bottom: 0;
        }
        .top-title{
            width: 160px;
            height: 60px;
            position: absolute;
            top: 20px;
            right: 50px;
            color: #ffffff;
            font-size: 20px;
            text-align: center;
        }
        .top-school{
            width: 160px;
            height: 60px;
            position: absolute;
            top: 60px;
            left: 80px;
            font-size: 22px;
            text-align: center;
        }
        .logo-contact-left{
            width: 160px;
            height: 58px;
            position: absolute;
            left: 20px;
            bottom: 0;
        }
        .cd-signature{
            position: absolute;
            right: 60px;
            bottom: 120px;
            text-align: right;
            font-size: 12px;
        }
        .logo-contact-left .phone{font-size:18px;display:block;}
        .logo-contact-left .url{font-size:12px;}
        .page-content{
            margin: 0 60px;position: absolute;top: 90px; width: 500px;
            position: relative;
        }
        .page-content .profile-photo{
            position: absolute;
            right: 0;
            top: 56px;
            width: 150px;
        }

        .page-content .profile-box{
            padding-top: 50px;
        }

        .page-content .profile-box dl{line-height: 25px;}
        .page-content .profile-box dt{float: left; width: 112px; clear: left;color: #999;text-align: right}
        .page-content .profile-box dt:after{content: ': '}
        .page-content .profile-box dd{margin-left: 118px;border-bottom: 1px dotted #efefef; width: 220px;}
        .page-content .profile-box dd:before{display: table}
        .page-content .profile-box dd:after{clear: both;}
        .teacher-list span.item{display: inline-block; text-align: left;color: #666666;}
        .teacher-list-a span.item{display: block; }
        .teacher-list-b span.item{width: 110px;float: left; height: 25px;}

        .page-content .cover-bottom{
            position: absolute;
            width: 100%;
            top: 395px;
        }

        span.ld{
            color: #000;
            font-size: 110%;
            padding: 1px 3px;
            margin-right: 4px;
            background: #efefef;
        }

        span.ld:after{
        }

        hr.sep{
            margin: 5em 0 3em;
            border: 0;
            height: 1px;
            background: #333;
            background-image: -webkit-linear-gradient(left, #efefef, #ccc, #efefef);
            background-image:    -moz-linear-gradient(left, #efefef, #999, #ccc);
            background-image:     -ms-linear-gradient(left, #efefef, #999, #ccc);
            background-image:      -o-linear-gradient(left, #efefef, #999, #ccc);
        }

        ul.mission li{line-height: 1.5em;}
        /*.web-view .report-page{margin: 1em 0;}*/

        .tac{text-align: center}
        .page-break-after{page-break-after: always;}
        img.middle-size{
            max-width: 360px;
            max-height: 272px;
            margin: 10px auto;
            display: block;
            border: 4px solid #efefef;
        }
        body {
            font-family: Utopia, "Trebuchet MS", Arial, "Microsoft Yahei", "微软雅黑", STXihei, "华文细黑", sans-serif;
            font-size: 12px;
            line-height: 1.42857143;
            color: #333333;
            margin: 0;
            padding: 0;
            background-color: #efefef;
        <?php if($withToolBar):?>
            padding-top: 70px;
            padding-bottom: 20px;
        <?php endif;?>
        }

        @media print {
            /**.web-view .report-page {background: none;}**/
            /*.web-view .report-page{margin: 0;}*/
        }
        .navbar-inverse {
            background-color: #333333;
            border-color: #efefef;
        }
        .navbar-inverse form {
            line-height: 50px;
            height: 50px;
            text-align: center;
        }
        .navbar-inverse form input {
            margin-top: 13px;
        }
        .navbar-fixed-top {
            top: 0;
            position: fixed;
            right: 0;
            left: 0;
            z-index: 1030;
            -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
        }
        .navbar-inverse a{
            color: #f2f2f2;
        }
    </style>
</body>
</html>