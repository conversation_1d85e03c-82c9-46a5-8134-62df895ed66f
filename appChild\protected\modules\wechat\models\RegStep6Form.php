<?php
class RegStep6Form extends CFormModel
{
	public $firstLanguage;
	public $familyLanguage;
	public $commonlyLanguage;


	public function rules()
	{
		return array(
			array('firstLanguage, familyLanguage, commonlyLanguage', 'required'),
		);
	}

	public function attributeLabels()
	{
		return array(
			'firstLanguage' => Yii::t("reg", "Which language did your child learn first?"),
			'familyLanguage' => Yii::t("reg", "Which language is most often spoken at home?"),
			'commonlyLanguage' => Yii::t("reg", "Which language does your child usually speak?"),
			'interviewTime' => Yii::t("reg", "面试时间"),
		);
	}


}