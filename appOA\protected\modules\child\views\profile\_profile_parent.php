<div class="form">
<?php $form=$this->beginWidget('CActiveForm', array(
	'id'=>'child-form',
	'enableClientValidation'=>true,
	'clientOptions'=>array(
		'validateOnSubmit'=>true,
	),
	'htmlOptions' => array('enctype' => 'multipart/form-data'),
)); ?>
	
	<div class="table_full">
		<table width="100%">
			<colgroup>
				<col width="460">
				<col width="460">
				<col>
			</colgroup>
			<tbody>
				<tr>
					<th style="padding-bottom:0px">
						<span style="font-size: 14px;font-weight: 600;color: #333">
                            <?php echo Yii::t("child",'Father'); ?> <?php echo isset($model['father'])?' ( '.$model['father']->uid.' )':'';?>
                        </span>
                        <?php if(!empty($model['father'])){?>
                            <div class="bootstrap-iso flag_div mt5">
                                <div class="mb5">
                                    <a href="javascript:;" class="mr16 fw400" @click="showFlag('father')"><?php echo Yii::t('global','Tagging')?></a>
                                </div>
                                <!-----已经选择的标签---->
                                <div class="choose_flag_div">
                                    <span v-for="(item,index) in parentFlagRaw.father"
                                          v-if="flagList[item]"
                                          :key="index"
                                          class="label mr10 mb5 flag_span"
                                          :title="flagList[item]['desc']"
                                          :style="{backgroundColor:flagList[item]['color']}" >
                                        {{flagList[item]['name']}}
                                    </span>
                                </div>
                            </div>
                        <?php }?>
                    </th>
					<th style="padding-bottom:0px">
                        <span style="font-size: 14px;font-weight: 600;color: #333">
						<?php echo Yii::t("child",'Mother'); ?> <?php echo isset($model['mother'])?' ( '.$model['mother']->uid.' )':'';?>
                        </span>
                        <?php if(!empty($model['mother'])){?>
                            <div class="bootstrap-iso flag_div mt5">
                                <div  class="mb5">
                                    <a href="javascript:;" class="mr16 fw400" @click="showFlag('mother')"><?php echo Yii::t('global','Tagging')?></a>
                                </div>
                                <!-----已经选择的标签---->
                                <div class="choose_flag_div">
                                    <span v-for="(item,index) in parentFlagRaw.mother"
                                          v-if="flagList[item]"
                                            :key="index"
                                            class="label  mr10 mb5 flag_span"
                                            :title="flagList[item]['desc']"
                                            :style="{backgroundColor:flagList[item]['color']}" >
                                    {{flagList[item]['name']}}
                                    </span>
                                </div>

                            </div>
                        <?php }?>
                    </th>
				</tr>
				<tr>
					<td>
						<?php $this->renderPartial("_profile_single_parent", array("role"=>'father', "model"=>$model['father']));?>
					</td>
					<td>
						<?php $this->renderPartial("_profile_single_parent", array("role"=>'mother', "model"=>$model['mother']));?>
					</td>
				</tr>
			</tbody>
		</table>
	</div>
		<div class="btn_wrap_pd">
			<?php
				echo CHtml::submitButton(Yii::t("global", "Save"), array("class"=>"btn btn_submit"));
                foreach(Yii::app()->user->getFlashes() as $key => $message) {
                    echo '<div class="tips_' . $key . '">' . $message . "</div>\n";
                }
			?>
		</div>
<?php $this->endWidget(); ?>
</div><!-- form -->
<div class="bootstrap-iso">
    <div class="modal fade bs-example-modal-lg" id="parentFlag" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog" >
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                    <h4 class="modal-title" v-if="parent=='father'"><?php echo Yii::t('global','Setting tags for father')?></h4>
                    <h4 class="modal-title" v-else><?php echo Yii::t('global','Setting tags for mother')?></h4>
                </div>
                <div class="modal-body min-height-300" v-if="parent">
                    <div>
                        <p class="parent_name">{{parentMainInfo[parent]['name']}}</p>
                        <span class="parent_flag_name" v-if="parent=='father'"><?php echo Yii::t('global','Father')?></span>
                        <span class="parent_flag_name" v-else><?php echo Yii::t('global','Mother')?></span>
                    </div>
                    <div class="mt14 flag_div">
                        <div class="mb10" style="margin-right:20px;font-size: 14px" >
                            <span class="lh30"><?php echo Yii::t('global','Changeable Tags')?></span>
                        </div>
                        <div class="choose_flag_div">
                            <span v-for="(item,index) in flagList"
                                  :key="index"
                                  v-if="item['editable_by_staff'] != false"
                                  @click="chooseFlag(item['flag'],index)"
                                  style="cursor: pointer;line-height: 20px;"
                                  class="label mr10 p4-12 mb10"
                                  :title="item['desc']"
                                  :class="label_no_select"
                                  :style="active_style[index]" >
                            {{item['name']}}
                            </span>
                        </div>
                    </div>
                    <!--系统打的标签不能选择-->
                    <div class="mt30 flag_div" v-if="showNonSelectable">
                        <div class="mb10" style="margin-right:20px;font-size: 14px">
                            <span class="lh30"><?php echo Yii::t('global','System Tags')?></span>
                        </div>
                        <div class="choose_flag_div">
                            <span v-for="(item,index) in flagList"
                                  v-if="item['editable_by_staff'] == false"
                                  :key="index"
                                  :style="active_style[item['flag']]"
                                  style="line-height: 20px;"
                                  :title="item['desc']"
                                  :class="label_no_select"
                                  class="label mr10 p4-12 mb10" >
                            {{item['name']}}
                            </span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel') ?></button>
                    <button type="button" class="btn btn-primary" @click='setFlag()'><?php echo Yii::t('global', 'Save') ?></button>
                </div>
            </div>
        </div>
    </div>
</div>
