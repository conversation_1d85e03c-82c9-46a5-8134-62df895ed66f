<?php

/**
 * This is the model class for table "ivy_lib_books".
 *
 * The followings are the available columns in table 'ivy_lib_books':
 * @property integer $id
 * @property string $title
 * @property string $barcode
 * @property string $isbn
 * @property string $referenceno
 * @property string $published_price
 * @property double $import_price
 * @property double $selling_price
 * @property double $selling_percent_price
 * @property string $author
 * @property string $publisher
 * @property integer $publicationdate
 * @property string $buy_from
 * @property integer $language
 * @property integer $agegroup
 * @property integer $categroup
 * @property integer $location
 * @property string $book_cover
 * @property string $synopsis
 * @property string $schoolid
 * @property integer $uid
 * @property integer $booking_number
 * @property integer $borrowstatus
 * @property integer $status
 * @property integer $updated_timestamp
 */
class Books extends CActiveRecord
{
	public $uploadFile;
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return Books the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_lib_books';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('title, barcode, schoolid, uid', 'required'),
			array('publicationdate, language, agegroup, categroup, location, uid, booking_number, borrowstatus, status, updated_timestamp', 'numerical', 'integerOnly'=>true),
			array('import_price, selling_price, selling_percent_price', 'numerical'),
			array('title, barcode, isbn, referenceno, published_price, author, publisher, buy_from, book_cover', 'length', 'max'=>255),
			array('schoolid', 'length', 'max'=>15),
			array('synopsis', 'safe'),
			//upload file
			array('uploadFile', 'file', 'types'=>'jpg, gif ,png ,jpeg', 'maxSize'=>1024*1024*2, 'allowEmpty'=>true,),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, title, barcode, isbn, referenceno, published_price, import_price, selling_price, selling_percent_price, author, publisher, publicationdate, buy_from, language, agegroup, categroup, location, book_cover, synopsis, schoolid, uid, booking_number, borrowstatus, status, updated_timestamp', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'title' => 'Title',
			'barcode' => 'Barcode',
			'isbn' => 'Isbn',
			'referenceno' => 'Referenceno',
			'published_price' => 'Published Price',
			'import_price' => 'Import Price',
			'selling_price' => 'Selling Price',
			'selling_percent_price' => 'Selling Percent Price',
			'author' => 'Author',
			'publisher' => 'Publisher',
			'publicationdate' => 'Publicationdate',
			'buy_from' => 'Buy From',
			'language' => 'Language',
			'agegroup' => 'Agegroup',
			'categroup' => 'Categroup',
			'location' => 'Location',
			'book_cover' => 'Book Cover',
			'synopsis' => 'Synopsis',
			'schoolid' => 'Schoolid',
			'uid' => 'Uid',
			'booking_number' => 'Booking Number',
			'borrowstatus' => 'Borrowstatus',
			'status' => 'Status',
			'updated_timestamp' => 'Updated Timestamp',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('title',$this->title,true);
		$criteria->compare('barcode',$this->barcode,true);
		$criteria->compare('isbn',$this->isbn,true);
		$criteria->compare('referenceno',$this->referenceno,true);
		$criteria->compare('published_price',$this->published_price,true);
		$criteria->compare('import_price',$this->import_price);
		$criteria->compare('selling_price',$this->selling_price);
		$criteria->compare('selling_percent_price',$this->selling_percent_price);
		$criteria->compare('author',$this->author,true);
		$criteria->compare('publisher',$this->publisher,true);
		$criteria->compare('publicationdate',$this->publicationdate);
		$criteria->compare('buy_from',$this->buy_from,true);
		$criteria->compare('language',$this->language);
		$criteria->compare('agegroup',$this->agegroup);
		$criteria->compare('categroup',$this->categroup);
		$criteria->compare('location',$this->location);
		$criteria->compare('book_cover',$this->book_cover,true);
		$criteria->compare('synopsis',$this->synopsis,true);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('uid',$this->uid);
		$criteria->compare('booking_number',$this->booking_number);
		$criteria->compare('borrowstatus',$this->borrowstatus);
		$criteria->compare('status',$this->status);
		$criteria->compare('updated_timestamp',$this->updated_timestamp);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
			'sort'=>array(
			        'defaultOrder'=>'updated_timestamp DESC',
			    ),
		));
	}

	protected function beforeSave()
	{
		if (parent::beforeSave()) {
			if($file = CUploadedFile::getInstance($this,'uploadFile')) {
			   $file_ext = $file->getExtensionName();
			   	//处理图片
			   	Yii::import('application.extensions.image.Image');
	         	$picPath = Yii::app()->params['OAUploadBasePath'] . '/library/';
	         	$picName = 'books_' . uniqid() . '.' . $file_ext;
	         	if ($file->saveAs($picPath . $picName)){
		   	      	$image = new Image($picPath . $picName);
		   	      	$image->quality(100);
		   	      	$image->resize(200);
		   	      	$image->save( $picPath . $picName);
		   	      	//保存缩略图
		   	      	$image = new Image($picPath . $picName);
		   	      	$image->quality(100);
		   	      	$image->resize(55);
		   	      	$image->save( $picPath . 'thumbs/' . $picName);

		   	      	$this->book_cover = $picName;		   	      	
			   	}
			}
		    return true;
		}
		return false;
	}

	public function deleteCover()
	{
		//删除关联的封面
		$picPath = Yii::app()->params['OAUploadBasePath'] . '/library/';
		$picName = $this->book_cover;
		unlink($picPath . $picName);
		unlink($picPath . 'thumbs/' . $picName);
	}
}