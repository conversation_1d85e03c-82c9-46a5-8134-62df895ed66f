<?php

/**
 * This is the model class for table "ivy_yp_pay_habit".
 *
 * The followings are the available columns in table 'ivy_yp_pay_habit':
 * @property integer $id
 * @property integer $childId
 * @property string $pd_FrpId
 * @property integer $hotValue
 * @property integer $updateTime
 */
class YpPayHabit extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return WYpPayHabit the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_yp_pay_habit';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('childId', 'required'),
			array('childId, hotValue, updateTime', 'numerical', 'integerOnly'=>true),
			array('pd_FrpId', 'length', 'max'=>50),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('id, childId, pd_FrpId, hotValue, updateTime', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'childId' => 'Child',
			'pd_FrpId' => 'Pd Frp',
			'hotValue' => 'Hot Value',
			'updateTime' => 'Update Time',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('childId',$this->childId);
		$criteria->compare('pd_FrpId',$this->pd_FrpId,true);
		$criteria->compare('hotValue',$this->hotValue);
		$criteria->compare('updateTime',$this->updateTime);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
}