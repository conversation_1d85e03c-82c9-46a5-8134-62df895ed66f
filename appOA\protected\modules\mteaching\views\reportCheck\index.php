<div class="container-fluid">
    <!-- 面包屑导航 -->
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Teaching Tasks'), array('//mteaching/default/index')) ?></li>
        <li class="active"><?php echo Yii::t('global','Semester Report Review');?></li>

    </ol>
    <div class="row">
        <!-- 左侧菜单栏 -->
        <div class="col-md-2">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->getReportMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                'activeCssClass' => 'active',
            ));
            ?>
        </div>
        <div class="col-md-10">
            <div class="mb10 row">
                <!-- 搜索框 -->
                <form class="" style="float: left;width: 100%" action="<?php echo $this->createUrl('index', array('branchId' => $branchId)); ?>"
                      method="post">
                    <div class="col-sm-2 form-group">
                        <?php echo Chtml::dropDownList('yid', $yid, $schoolList, array('class' => 'form-control')); ?>
                    </div>
                    <div class="col-sm-2 form-group">
                        <?php echo Chtml::dropDownList('semester', $semester, array(1=> Yii::t('principal','Fall'), 2=> Yii::t('principal','Spring')), array('class' => 'form-control')); ?>
                    </div>
                    <div class="col-sm-2 form-group">
                        <?php echo Chtml::dropDownList('stat', $stat, array(14=> Yii::t('reg','Rejected'), 15=> Yii::t('lunch','Awaiting for approval'), 16 => Yii::t('reg','Confirmed')), array('class' => 'form-control', 'empty' => Yii::t('admissions','Search by status'))); ?>
                    </div>
                    <div class="col-sm-2 form-group">
                        <?php echo Chtml::dropDownList('classid', $classid, $classData, array('class' => 'form-control', "empty" => Yii::t('campus','All classes'))); ?>
                    </div>
                    <div class="">
                        <div class="">
                            <button class="btn btn-default ml5" type="submit"><span class="glyphicon glyphicon-search"> </span></button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div class="col-md-10">
            <?php if($classModel){ ?>
                <?php foreach($classModel as $classid){ ?>
                    <div class="panel panel-default bs-example" data-example-id="simple-table">
                        <div class="panel-heading">
                        <h3 class="panel-title"><?php echo $classid->title; ?></h3></div>
                        <div class="panel-body">
                            <table class="table">
                                <thead>
                                <tr>
                                    <th  width='100'>#</th>
                                    <th  width='300'><?php echo Yii::t('user','Child Name') ?></th>
                                    <th  width='300'><?php echo Yii::t('invoice','Add Timestamp') ?></th>
                                    <th  width='300'><?php echo Yii::t('labels','Status') ?></th>
                                    <th  width='300'><?php echo Yii::t('invoice','Created By') ?></th>
                                    <th  width='200'><?php echo Yii::t('global','Action') ?></th>
                                </tr>
                                </thead>
                                <tbody>
                                <?php
                                foreach ($reportList[$classid->classid] as $key=>$report){ ?>
                                    <tr>
                                        <th width='100'><?php echo $key+1 ?></th>
                                        <td width='300'><?php echo $childNames[$report['childid']] ?></td>
                                        <td width='300' class="timestamp_<?php echo $report['reportid'] ?>"><?php echo $report['timestamp'] ?></td>
                                        <td width='300' class="stat_<?php echo $report['reportid'] ?>"><?php echo $report['statName'] ?></td>
                                        <td  width='300' class="uid_<?php echo $report['reportid'] ?>"><?php echo $userName[$report['uid']] ?></td>
                                        <td width='200'>
                                            <span class="report_<?php echo $report['reportid'] ?>">
                                                <?php if($report['stat'] == SReport::STATUS_PASS){ ?>
                                                    <a class="btn btn-success J_ajax_del btn-xs" data-msg="<?php echo Yii::t('global','Back to pending approve?') ?>" href="<?php echo $this->createUrl('updateStatus', array('branchId' => $branchId, 'reportid' => $report['reportid'], 'type' => 'offline')); ?>"><?php echo Yii::t('global','Confirmed') ?></a>
                                                <?php }else if($report['stat'] == SReport::STATUS_CHECK){ ?>
                                                    <a class="btn btn-primary J_ajax_del btn-xs" data-msg="<?php echo Yii::t('global','Confirm to approve?') ?>" href="<?php echo $this->createUrl('updateStatus', array('branchId' => $branchId, 'reportid' => $report['reportid'], 'type' => 'online')); ?>"><?php echo Yii::t('labels','Yes') ?></a>
                                                    <a class="btn btn-danger J_modal btn-xs" href="<?php echo $this->createUrl('overrule', array('branchId' => $branchId, 'reportid' => $report['reportid'])); ?>"><?php echo Yii::t('reg','Rejected') ?></a>
                                                <?php } ?>
                                            </span>
                                            <a class="btn btn-info btn-xs" target="_blank" href="<?php echo $this->createUrl('semester/' . $previewurl, array('branchId' => $branchId, 'id' => $report['reportid'], 'childid' => $report['childid'])); ?>"><?php echo Yii::t('teaching','Preview') ?></a>
                                        </td>
                                    </tr>
                                <?php } ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                <?php } ?>
            <?php } ?>
        </div>
    </div>
</div>
<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    function cb_updated(data) {
        $('#modal').modal('hide');
        $(".report_"+data.id).empty();
        $(".uid_"+data.id).text(data.uid);
        $(".timestamp_"+data.id).text(data.timestamp);
        $(".stat_"+data.id).text(data.statName);
        $(".report_"+data.id).append(data.html);
        head.Util.modal()
        head.Util.ajaxDel()
    }

    var modal = '<div class="modal fade" id="modal" tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog" role="document"><div class="modal-content"></div></div></div>';
    $('body').append(modal);
</script>
