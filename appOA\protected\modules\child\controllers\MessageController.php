<?php

class MessageController extends ProtectedController
{
	public function actionError()
	{
		if(Yii::app()->request->isAjaxRequest){
			$ret = array(
				"message" => Yii::t("message", "Unauthorized operation, please contact IT Dept."),
				"state" => 'fail'
			);
			echo CJSON::encode($ret);
			Yii::app()->end();
		}
		$this->render('error');
	}

	// Uncomment the following methods and override them if needed
	/*
	public function filters()
	{
		// return the filter configuration for this controller, e.g.:
		return array(
			'inlineFilterName',
			array(
				'class'=>'path.to.FilterClass',
				'propertyName'=>'propertyValue',
			),
		);
	}

	public function actions()
	{
		// return external action classes, e.g.:
		return array(
			'action1'=>'path.to.ActionClass',
			'action2'=>array(
				'class'=>'path.to.AnotherActionClass',
				'propertyName'=>'propertyValue',
			),
		);
	}
	*/
}