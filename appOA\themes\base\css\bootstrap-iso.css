.bootstrap-iso html {
    font-family: sans-serif;
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
}
.bootstrap-iso body {
    margin: 0;
}
.bootstrap-iso article,
.bootstrap-iso aside,
.bootstrap-iso details,
.bootstrap-iso figcaption,
.bootstrap-iso figure,
.bootstrap-iso footer,
.bootstrap-iso header,
.bootstrap-iso hgroup,
.bootstrap-iso main,
.bootstrap-iso nav,
.bootstrap-iso section,
.bootstrap-iso summary {
    display: block;
}
.bootstrap-iso audio,
.bootstrap-iso canvas,
.bootstrap-iso progress,
.bootstrap-iso video {
    display: inline-block;
    vertical-align: baseline;
}
.bootstrap-iso audio:not([controls]) {
    display: none;
    height: 0;
}
.bootstrap-iso [hidden],
.bootstrap-iso template {
    display: none;
}
.bootstrap-iso a {
    background: transparent;
}
.bootstrap-iso a:active,
.bootstrap-iso a:hover {
    outline: 0;
}
.bootstrap-iso abbr[title] {
    border-bottom: 1px dotted;
}
.bootstrap-iso b,
.bootstrap-iso strong {
    font-weight: bold;
}
.bootstrap-iso dfn {
    font-style: italic;
}
.bootstrap-iso h1 {
    font-size: 2em;
    margin: 0.67em 0;
}
.bootstrap-iso mark {
    background: #ff0;
    color: #000;
}
.bootstrap-iso small {
    font-size: 80%;
}
.bootstrap-iso sub,
.bootstrap-iso sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
}
.bootstrap-iso sup {
    top: -0.5em;
}
.bootstrap-iso sub {
    bottom: -0.25em;
}
.bootstrap-iso img {
    border: 0;
}
.bootstrap-iso svg:not(:root) {
    overflow: hidden;
}
.bootstrap-iso figure {
    margin: 1em 40px;
}
.bootstrap-iso hr {
    -moz-box-sizing: content-box;
    box-sizing: content-box;
    height: 0;
}
.bootstrap-iso pre {
    overflow: auto;
}
.bootstrap-iso code,
.bootstrap-iso kbd,
.bootstrap-iso pre,
.bootstrap-iso samp {
    font-family: monospace, monospace;
    font-size: 1em;
}
.bootstrap-iso button,
.bootstrap-iso input,
.bootstrap-iso optgroup,
.bootstrap-iso select,
.bootstrap-iso textarea {
    color: inherit;
    font: inherit;
    margin: 0;
}
.bootstrap-iso button {
    overflow: visible;
}
.bootstrap-iso button,
.bootstrap-iso select {
    text-transform: none;
}
.bootstrap-iso button,
.bootstrap-iso html input[type="button"],
.bootstrap-iso input[type="reset"],
.bootstrap-iso input[type="submit"] {
    -webkit-appearance: button;
    cursor: pointer;
}
.bootstrap-iso button[disabled],
.bootstrap-iso html input[disabled] {
    cursor: default;
}
.bootstrap-iso button::-moz-focus-inner,
.bootstrap-iso input::-moz-focus-inner {
    border: 0;
    padding: 0;
}
.bootstrap-iso input {
    line-height: normal;
}
.bootstrap-iso input[type="checkbox"],
.bootstrap-iso input[type="radio"] {
    box-sizing: border-box;
    padding: 0;
}
.bootstrap-iso input[type="number"]::-webkit-inner-spin-button,
.bootstrap-iso input[type="number"]::-webkit-outer-spin-button {
    height: auto;
}
.bootstrap-iso input[type="search"] {
    -webkit-appearance: textfield;
    -moz-box-sizing: content-box;
    -webkit-box-sizing: content-box;
    box-sizing: content-box;
}
.bootstrap-iso input[type="search"]::-webkit-search-cancel-button,
.bootstrap-iso input[type="search"]::-webkit-search-decoration {
    -webkit-appearance: none;
}
.bootstrap-iso fieldset {
    border: 1px solid #c0c0c0;
    margin: 0 2px;
    padding: 0.35em 0.625em 0.75em;
}
.bootstrap-iso legend {
    border: 0;
    padding: 0;
}
.bootstrap-iso textarea {
    overflow: auto;
}
.bootstrap-iso optgroup {
    font-weight: bold;
}
.bootstrap-iso table {
    border-collapse: collapse;
    border-spacing: 0;
}
.bootstrap-iso td,
.bootstrap-iso th {
    padding: 0;
}
@media print {
    .bootstrap-iso * {
        text-shadow: none !important;
        color: #000 !important;
        background: transparent !important;
        box-shadow: none !important;
    }
    .bootstrap-iso a,
    .bootstrap-iso a:visited {
        text-decoration: underline;
    }
    .bootstrap-iso a[href]:after {
        content: " (" attr(href) ")";
    }
    .bootstrap-iso abbr[title]:after {
        content: " (" attr(title) ")";
    }
    .bootstrap-iso a[href^="javascript:"]:after,
    .bootstrap-iso a[href^="#"]:after {
        content: "";
    }
    .bootstrap-iso pre,
    .bootstrap-iso blockquote {
        border: 1px solid #999;
        page-break-inside: avoid;
    }
    .bootstrap-iso thead {
        display: table-header-group;
    }
    .bootstrap-iso tr,
    .bootstrap-iso img {
        page-break-inside: avoid;
    }
    .bootstrap-iso img {
        max-width: 100% !important;
    }
    .bootstrap-iso p,
    .bootstrap-iso h2,
    .bootstrap-iso h3 {
        orphans: 3;
        widows: 3;
    }
    .bootstrap-iso h2,
    .bootstrap-iso h3 {
        page-break-after: avoid;
    }
    .bootstrap-iso select {
        background: #fff !important;
    }
    .bootstrap-iso .navbar {
        display: none;
    }
    .bootstrap-iso .table td,
    .bootstrap-iso .table th {
        background-color: #fff !important;
    }
    .bootstrap-iso .btn > .caret,
    .bootstrap-iso .dropup > .btn > .caret {
        border-top-color: #000 !important;
    }
    .bootstrap-iso .label {
        border: 1px solid #000;
    }
    .bootstrap-iso .table {
        border-collapse: collapse !important;
    }
    .bootstrap-iso .table-bordered th,
    .bootstrap-iso .table-bordered td {
        border: 1px solid #ddd !important;
    }
}
@font-face {
    font-family: 'Glyphicons Halflings';
    src: url('../fonts/glyphicons-halflings-regular.eot');
    src: url('../fonts/glyphicons-halflings-regular.eot?#iefix') format('embedded-opentype'), url('../fonts/glyphicons-halflings-regular.woff') format('woff'), url('../fonts/glyphicons-halflings-regular.ttf') format('truetype'), url('../fonts/glyphicons-halflings-regular.svg#glyphicons_halflingsregular') format('svg');
}
.bootstrap-iso .glyphicon {
    position: relative;
    top: 1px;
    display: inline-block;
    font-family: 'Glyphicons Halflings';
    font-style: normal;
    font-weight: normal;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
.bootstrap-iso .glyphicon-asterisk:before {
    content: "\2a";
}
.bootstrap-iso .glyphicon-plus:before {
    content: "\2b";
}
.bootstrap-iso .glyphicon-euro:before {
    content: "\20ac";
}
.bootstrap-iso .glyphicon-minus:before {
    content: "\2212";
}
.bootstrap-iso .glyphicon-cloud:before {
    content: "\2601";
}
.bootstrap-iso .glyphicon-envelope:before {
    content: "\2709";
}
.bootstrap-iso .glyphicon-pencil:before {
    content: "\270f";
}
.bootstrap-iso .glyphicon-glass:before {
    content: "\e001";
}
.bootstrap-iso .glyphicon-music:before {
    content: "\e002";
}
.bootstrap-iso .glyphicon-search:before {
    content: "\e003";
}
.bootstrap-iso .glyphicon-heart:before {
    content: "\e005";
}
.bootstrap-iso .glyphicon-star:before {
    content: "\e006";
}
.bootstrap-iso .glyphicon-star-empty:before {
    content: "\e007";
}
.bootstrap-iso .glyphicon-user:before {
    content: "\e008";
}
.bootstrap-iso .glyphicon-film:before {
    content: "\e009";
}
.bootstrap-iso .glyphicon-th-large:before {
    content: "\e010";
}
.bootstrap-iso .glyphicon-th:before {
    content: "\e011";
}
.bootstrap-iso .glyphicon-th-list:before {
    content: "\e012";
}
.bootstrap-iso .glyphicon-ok:before {
    content: "\e013";
}
.bootstrap-iso .glyphicon-remove:before {
    content: "\e014";
}
.bootstrap-iso .glyphicon-zoom-in:before {
    content: "\e015";
}
.bootstrap-iso .glyphicon-zoom-out:before {
    content: "\e016";
}
.bootstrap-iso .glyphicon-off:before {
    content: "\e017";
}
.bootstrap-iso .glyphicon-signal:before {
    content: "\e018";
}
.bootstrap-iso .glyphicon-cog:before {
    content: "\e019";
}
.bootstrap-iso .glyphicon-trash:before {
    content: "\e020";
}
.bootstrap-iso .glyphicon-home:before {
    content: "\e021";
}
.bootstrap-iso .glyphicon-file:before {
    content: "\e022";
}
.bootstrap-iso .glyphicon-time:before {
    content: "\e023";
}
.bootstrap-iso .glyphicon-road:before {
    content: "\e024";
}
.bootstrap-iso .glyphicon-download-alt:before {
    content: "\e025";
}
.bootstrap-iso .glyphicon-download:before {
    content: "\e026";
}
.bootstrap-iso .glyphicon-upload:before {
    content: "\e027";
}
.bootstrap-iso .glyphicon-inbox:before {
    content: "\e028";
}
.bootstrap-iso .glyphicon-play-circle:before {
    content: "\e029";
}
.bootstrap-iso .glyphicon-repeat:before {
    content: "\e030";
}
.bootstrap-iso .glyphicon-refresh:before {
    content: "\e031";
}
.bootstrap-iso .glyphicon-list-alt:before {
    content: "\e032";
}
.bootstrap-iso .glyphicon-lock:before {
    content: "\e033";
}
.bootstrap-iso .glyphicon-flag:before {
    content: "\e034";
}
.bootstrap-iso .glyphicon-headphones:before {
    content: "\e035";
}
.bootstrap-iso .glyphicon-volume-off:before {
    content: "\e036";
}
.bootstrap-iso .glyphicon-volume-down:before {
    content: "\e037";
}
.bootstrap-iso .glyphicon-volume-up:before {
    content: "\e038";
}
.bootstrap-iso .glyphicon-qrcode:before {
    content: "\e039";
}
.bootstrap-iso .glyphicon-barcode:before {
    content: "\e040";
}
.bootstrap-iso .glyphicon-tag:before {
    content: "\e041";
}
.bootstrap-iso .glyphicon-tags:before {
    content: "\e042";
}
.bootstrap-iso .glyphicon-book:before {
    content: "\e043";
}
.bootstrap-iso .glyphicon-bookmark:before {
    content: "\e044";
}
.bootstrap-iso .glyphicon-print:before {
    content: "\e045";
}
.bootstrap-iso .glyphicon-camera:before {
    content: "\e046";
}
.bootstrap-iso .glyphicon-font:before {
    content: "\e047";
}
.bootstrap-iso .glyphicon-bold:before {
    content: "\e048";
}
.bootstrap-iso .glyphicon-italic:before {
    content: "\e049";
}
.bootstrap-iso .glyphicon-text-height:before {
    content: "\e050";
}
.bootstrap-iso .glyphicon-text-width:before {
    content: "\e051";
}
.bootstrap-iso .glyphicon-align-left:before {
    content: "\e052";
}
.bootstrap-iso .glyphicon-align-center:before {
    content: "\e053";
}
.bootstrap-iso .glyphicon-align-right:before {
    content: "\e054";
}
.bootstrap-iso .glyphicon-align-justify:before {
    content: "\e055";
}
.bootstrap-iso .glyphicon-list:before {
    content: "\e056";
}
.bootstrap-iso .glyphicon-indent-left:before {
    content: "\e057";
}
.bootstrap-iso .glyphicon-indent-right:before {
    content: "\e058";
}
.bootstrap-iso .glyphicon-facetime-video:before {
    content: "\e059";
}
.bootstrap-iso .glyphicon-picture:before {
    content: "\e060";
}
.bootstrap-iso .glyphicon-map-marker:before {
    content: "\e062";
}
.bootstrap-iso .glyphicon-adjust:before {
    content: "\e063";
}
.bootstrap-iso .glyphicon-tint:before {
    content: "\e064";
}
.bootstrap-iso .glyphicon-edit:before {
    content: "\e065";
}
.bootstrap-iso .glyphicon-share:before {
    content: "\e066";
}
.bootstrap-iso .glyphicon-check:before {
    content: "\e067";
}
.bootstrap-iso .glyphicon-move:before {
    content: "\e068";
}
.bootstrap-iso .glyphicon-step-backward:before {
    content: "\e069";
}
.bootstrap-iso .glyphicon-fast-backward:before {
    content: "\e070";
}
.bootstrap-iso .glyphicon-backward:before {
    content: "\e071";
}
.bootstrap-iso .glyphicon-play:before {
    content: "\e072";
}
.bootstrap-iso .glyphicon-pause:before {
    content: "\e073";
}
.bootstrap-iso .glyphicon-stop:before {
    content: "\e074";
}
.bootstrap-iso .glyphicon-forward:before {
    content: "\e075";
}
.bootstrap-iso .glyphicon-fast-forward:before {
    content: "\e076";
}
.bootstrap-iso .glyphicon-step-forward:before {
    content: "\e077";
}
.bootstrap-iso .glyphicon-eject:before {
    content: "\e078";
}
.bootstrap-iso .glyphicon-chevron-left:before {
    content: "\e079";
}
.bootstrap-iso .glyphicon-chevron-right:before {
    content: "\e080";
}
.bootstrap-iso .glyphicon-plus-sign:before {
    content: "\e081";
}
.bootstrap-iso .glyphicon-minus-sign:before {
    content: "\e082";
}
.bootstrap-iso .glyphicon-remove-sign:before {
    content: "\e083";
}
.bootstrap-iso .glyphicon-ok-sign:before {
    content: "\e084";
}
.bootstrap-iso .glyphicon-question-sign:before {
    content: "\e085";
}
.bootstrap-iso .glyphicon-info-sign:before {
    content: "\e086";
}
.bootstrap-iso .glyphicon-screenshot:before {
    content: "\e087";
}
.bootstrap-iso .glyphicon-remove-circle:before {
    content: "\e088";
}
.bootstrap-iso .glyphicon-ok-circle:before {
    content: "\e089";
}
.bootstrap-iso .glyphicon-ban-circle:before {
    content: "\e090";
}
.bootstrap-iso .glyphicon-arrow-left:before {
    content: "\e091";
}
.bootstrap-iso .glyphicon-arrow-right:before {
    content: "\e092";
}
.bootstrap-iso .glyphicon-arrow-up:before {
    content: "\e093";
}
.bootstrap-iso .glyphicon-arrow-down:before {
    content: "\e094";
}
.bootstrap-iso .glyphicon-share-alt:before {
    content: "\e095";
}
.bootstrap-iso .glyphicon-resize-full:before {
    content: "\e096";
}
.bootstrap-iso .glyphicon-resize-small:before {
    content: "\e097";
}
.bootstrap-iso .glyphicon-exclamation-sign:before {
    content: "\e101";
}
.bootstrap-iso .glyphicon-gift:before {
    content: "\e102";
}
.bootstrap-iso .glyphicon-leaf:before {
    content: "\e103";
}
.bootstrap-iso .glyphicon-fire:before {
    content: "\e104";
}
.bootstrap-iso .glyphicon-eye-open:before {
    content: "\e105";
}
.bootstrap-iso .glyphicon-eye-close:before {
    content: "\e106";
}
.bootstrap-iso .glyphicon-warning-sign:before {
    content: "\e107";
}
.bootstrap-iso .glyphicon-plane:before {
    content: "\e108";
}
.bootstrap-iso .glyphicon-calendar:before {
    content: "\e109";
}
.bootstrap-iso .glyphicon-random:before {
    content: "\e110";
}
.bootstrap-iso .glyphicon-comment:before {
    content: "\e111";
}
.bootstrap-iso .glyphicon-magnet:before {
    content: "\e112";
}
.bootstrap-iso .glyphicon-chevron-up:before {
    content: "\e113";
}
.bootstrap-iso .glyphicon-chevron-down:before {
    content: "\e114";
}
.bootstrap-iso .glyphicon-retweet:before {
    content: "\e115";
}
.bootstrap-iso .glyphicon-shopping-cart:before {
    content: "\e116";
}
.bootstrap-iso .glyphicon-folder-close:before {
    content: "\e117";
}
.bootstrap-iso .glyphicon-folder-open:before {
    content: "\e118";
}
.bootstrap-iso .glyphicon-resize-vertical:before {
    content: "\e119";
}
.bootstrap-iso .glyphicon-resize-horizontal:before {
    content: "\e120";
}
.bootstrap-iso .glyphicon-hdd:before {
    content: "\e121";
}
.bootstrap-iso .glyphicon-bullhorn:before {
    content: "\e122";
}
.bootstrap-iso .glyphicon-bell:before {
    content: "\e123";
}
.bootstrap-iso .glyphicon-certificate:before {
    content: "\e124";
}
.bootstrap-iso .glyphicon-thumbs-up:before {
    content: "\e125";
}
.bootstrap-iso .glyphicon-thumbs-down:before {
    content: "\e126";
}
.bootstrap-iso .glyphicon-hand-right:before {
    content: "\e127";
}
.bootstrap-iso .glyphicon-hand-left:before {
    content: "\e128";
}
.bootstrap-iso .glyphicon-hand-up:before {
    content: "\e129";
}
.bootstrap-iso .glyphicon-hand-down:before {
    content: "\e130";
}
.bootstrap-iso .glyphicon-circle-arrow-right:before {
    content: "\e131";
}
.bootstrap-iso .glyphicon-circle-arrow-left:before {
    content: "\e132";
}
.bootstrap-iso .glyphicon-circle-arrow-up:before {
    content: "\e133";
}
.bootstrap-iso .glyphicon-circle-arrow-down:before {
    content: "\e134";
}
.bootstrap-iso .glyphicon-globe:before {
    content: "\e135";
}
.bootstrap-iso .glyphicon-wrench:before {
    content: "\e136";
}
.bootstrap-iso .glyphicon-tasks:before {
    content: "\e137";
}
.bootstrap-iso .glyphicon-filter:before {
    content: "\e138";
}
.bootstrap-iso .glyphicon-briefcase:before {
    content: "\e139";
}
.bootstrap-iso .glyphicon-fullscreen:before {
    content: "\e140";
}
.bootstrap-iso .glyphicon-dashboard:before {
    content: "\e141";
}
.bootstrap-iso .glyphicon-paperclip:before {
    content: "\e142";
}
.bootstrap-iso .glyphicon-heart-empty:before {
    content: "\e143";
}
.bootstrap-iso .glyphicon-link:before {
    content: "\e144";
}
.bootstrap-iso .glyphicon-phone:before {
    content: "\e145";
}
.bootstrap-iso .glyphicon-pushpin:before {
    content: "\e146";
}
.bootstrap-iso .glyphicon-usd:before {
    content: "\e148";
}
.bootstrap-iso .glyphicon-gbp:before {
    content: "\e149";
}
.bootstrap-iso .glyphicon-sort:before {
    content: "\e150";
}
.bootstrap-iso .glyphicon-sort-by-alphabet:before {
    content: "\e151";
}
.bootstrap-iso .glyphicon-sort-by-alphabet-alt:before {
    content: "\e152";
}
.bootstrap-iso .glyphicon-sort-by-order:before {
    content: "\e153";
}
.bootstrap-iso .glyphicon-sort-by-order-alt:before {
    content: "\e154";
}
.bootstrap-iso .glyphicon-sort-by-attributes:before {
    content: "\e155";
}
.bootstrap-iso .glyphicon-sort-by-attributes-alt:before {
    content: "\e156";
}
.bootstrap-iso .glyphicon-unchecked:before {
    content: "\e157";
}
.bootstrap-iso .glyphicon-expand:before {
    content: "\e158";
}
.bootstrap-iso .glyphicon-collapse-down:before {
    content: "\e159";
}
.bootstrap-iso .glyphicon-collapse-up:before {
    content: "\e160";
}
.bootstrap-iso .glyphicon-log-in:before {
    content: "\e161";
}
.bootstrap-iso .glyphicon-flash:before {
    content: "\e162";
}
.bootstrap-iso .glyphicon-log-out:before {
    content: "\e163";
}
.bootstrap-iso .glyphicon-new-window:before {
    content: "\e164";
}
.bootstrap-iso .glyphicon-record:before {
    content: "\e165";
}
.bootstrap-iso .glyphicon-save:before {
    content: "\e166";
}
.bootstrap-iso .glyphicon-open:before {
    content: "\e167";
}
.bootstrap-iso .glyphicon-saved:before {
    content: "\e168";
}
.bootstrap-iso .glyphicon-import:before {
    content: "\e169";
}
.bootstrap-iso .glyphicon-export:before {
    content: "\e170";
}
.bootstrap-iso .glyphicon-send:before {
    content: "\e171";
}
.bootstrap-iso .glyphicon-floppy-disk:before {
    content: "\e172";
}
.bootstrap-iso .glyphicon-floppy-saved:before {
    content: "\e173";
}
.bootstrap-iso .glyphicon-floppy-remove:before {
    content: "\e174";
}
.bootstrap-iso .glyphicon-floppy-save:before {
    content: "\e175";
}
.bootstrap-iso .glyphicon-floppy-open:before {
    content: "\e176";
}
.bootstrap-iso .glyphicon-credit-card:before {
    content: "\e177";
}
.bootstrap-iso .glyphicon-transfer:before {
    content: "\e178";
}
.bootstrap-iso .glyphicon-cutlery:before {
    content: "\e179";
}
.bootstrap-iso .glyphicon-header:before {
    content: "\e180";
}
.bootstrap-iso .glyphicon-compressed:before {
    content: "\e181";
}
.bootstrap-iso .glyphicon-earphone:before {
    content: "\e182";
}
.bootstrap-iso .glyphicon-phone-alt:before {
    content: "\e183";
}
.bootstrap-iso .glyphicon-tower:before {
    content: "\e184";
}
.bootstrap-iso .glyphicon-stats:before {
    content: "\e185";
}
.bootstrap-iso .glyphicon-sd-video:before {
    content: "\e186";
}
.bootstrap-iso .glyphicon-hd-video:before {
    content: "\e187";
}
.bootstrap-iso .glyphicon-subtitles:before {
    content: "\e188";
}
.bootstrap-iso .glyphicon-sound-stereo:before {
    content: "\e189";
}
.bootstrap-iso .glyphicon-sound-dolby:before {
    content: "\e190";
}
.bootstrap-iso .glyphicon-sound-5-1:before {
    content: "\e191";
}
.bootstrap-iso .glyphicon-sound-6-1:before {
    content: "\e192";
}
.bootstrap-iso .glyphicon-sound-7-1:before {
    content: "\e193";
}
.bootstrap-iso .glyphicon-copyright-mark:before {
    content: "\e194";
}
.bootstrap-iso .glyphicon-registration-mark:before {
    content: "\e195";
}
.bootstrap-iso .glyphicon-cloud-download:before {
    content: "\e197";
}
.bootstrap-iso .glyphicon-cloud-upload:before {
    content: "\e198";
}
.bootstrap-iso .glyphicon-tree-conifer:before {
    content: "\e199";
}
.bootstrap-iso .glyphicon-tree-deciduous:before {
    content: "\e200";
}
.bootstrap-iso * {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
.bootstrap-iso *:before,
.bootstrap-iso *:after {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
.bootstrap-iso html {
    font-size: 10px;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
.bootstrap-iso body {
    font-family: "Trebuchet MS", Tahoma, Arial, "Microsoft Yahei", "微软雅黑", STXihei, "华文细黑", sans-serif;
    font-size: 12px;
    line-height: 1.42857143;
    color: #333333;
    background-color: #ffffff;
}
.bootstrap-iso input,
.bootstrap-iso button,
.bootstrap-iso select,
.bootstrap-iso textarea {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
}
.bootstrap-iso a {
    color: #428bca;
    text-decoration: none;
}
.bootstrap-iso a:hover,
.bootstrap-iso a:focus {
    color: #2a6496;
    text-decoration: underline;
}
.bootstrap-iso a:focus {
    outline: thin dotted;
    outline: 5px auto -webkit-focus-ring-color;
    outline-offset: -2px;
}
.bootstrap-iso figure {
    margin: 0;
}
.bootstrap-iso img {
    vertical-align: middle;
}
.bootstrap-iso .img-responsive,
.bootstrap-iso .thumbnail > img,
.bootstrap-iso .thumbnail a > img,
.bootstrap-iso .carousel-inner > .item > img,
.bootstrap-iso .carousel-inner > .item > a > img {
    display: block;
    width: 100% \9;
    max-width: 100%;
    height: auto;
}
.bootstrap-iso .img-rounded {
    border-radius: 6px;
}
.bootstrap-iso .img-thumbnail {
    padding: 4px;
    line-height: 1.42857143;
    background-color: #ffffff;
    border: 1px solid #dddddd;
    border-radius: 4px;
    -webkit-transition: all 0.2s ease-in-out;
    -o-transition: all 0.2s ease-in-out;
    transition: all 0.2s ease-in-out;
    display: inline-block;
    width: 100% \9;
    max-width: 100%;
    height: auto;
}
.bootstrap-iso .img-circle {
    border-radius: 50%;
}
.bootstrap-iso hr {
    margin-top: 17px;
    margin-bottom: 17px;
    border: 0;
    border-top: 1px solid #eeeeee;
}
.bootstrap-iso .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
}
.bootstrap-iso .sr-only-focusable:active,
.bootstrap-iso .sr-only-focusable:focus {
    position: static;
    width: auto;
    height: auto;
    margin: 0;
    overflow: visible;
    clip: auto;
}
.bootstrap-iso h1,
.bootstrap-iso h2,
.bootstrap-iso h3,
.bootstrap-iso h4,
.bootstrap-iso h5,
.bootstrap-iso h6,
.bootstrap-iso .h1,
.bootstrap-iso .h2,
.bootstrap-iso .h3,
.bootstrap-iso .h4,
.bootstrap-iso .h5,
.bootstrap-iso .h6 {
    font-family: inherit;
    font-weight: 500;
    line-height: 1.1;
    color: inherit;
}
.bootstrap-iso h1 small,
.bootstrap-iso h2 small,
.bootstrap-iso h3 small,
.bootstrap-iso h4 small,
.bootstrap-iso h5 small,
.bootstrap-iso h6 small,
.bootstrap-iso .h1 small,
.bootstrap-iso .h2 small,
.bootstrap-iso .h3 small,
.bootstrap-iso .h4 small,
.bootstrap-iso .h5 small,
.bootstrap-iso .h6 small,
.bootstrap-iso h1 .small,
.bootstrap-iso h2 .small,
.bootstrap-iso h3 .small,
.bootstrap-iso h4 .small,
.bootstrap-iso h5 .small,
.bootstrap-iso h6 .small,
.bootstrap-iso .h1 .small,
.bootstrap-iso .h2 .small,
.bootstrap-iso .h3 .small,
.bootstrap-iso .h4 .small,
.bootstrap-iso .h5 .small,
.bootstrap-iso .h6 .small {
    font-weight: normal;
    line-height: 1;
    color: #777777;
}
.bootstrap-iso h1,
.bootstrap-iso .h1,
.bootstrap-iso h2,
.bootstrap-iso .h2,
.bootstrap-iso h3,
.bootstrap-iso .h3 {
    margin-top: 17px;
    margin-bottom: 8.5px;
}
.bootstrap-iso h1 small,
.bootstrap-iso .h1 small,
.bootstrap-iso h2 small,
.bootstrap-iso .h2 small,
.bootstrap-iso h3 small,
.bootstrap-iso .h3 small,
.bootstrap-iso h1 .small,
.bootstrap-iso .h1 .small,
.bootstrap-iso h2 .small,
.bootstrap-iso .h2 .small,
.bootstrap-iso h3 .small,
.bootstrap-iso .h3 .small {
    font-size: 65%;
}
.bootstrap-iso h4,
.bootstrap-iso .h4,
.bootstrap-iso h5,
.bootstrap-iso .h5,
.bootstrap-iso h6,
.bootstrap-iso .h6 {
    margin-top: 8.5px;
    margin-bottom: 8.5px;
}
.bootstrap-iso h4 small,
.bootstrap-iso .h4 small,
.bootstrap-iso h5 small,
.bootstrap-iso .h5 small,
.bootstrap-iso h6 small,
.bootstrap-iso .h6 small,
.bootstrap-iso h4 .small,
.bootstrap-iso .h4 .small,
.bootstrap-iso h5 .small,
.bootstrap-iso .h5 .small,
.bootstrap-iso h6 .small,
.bootstrap-iso .h6 .small {
    font-size: 75%;
}
.bootstrap-iso h1,
.bootstrap-iso .h1 {
    font-size: 31px;
}
.bootstrap-iso h2,
.bootstrap-iso .h2 {
    font-size: 25px;
}
.bootstrap-iso h3,
.bootstrap-iso .h3 {
    font-size: 21px;
}
.bootstrap-iso h4,
.bootstrap-iso .h4 {
    font-size: 15px;
}
.bootstrap-iso h5,
.bootstrap-iso .h5 {
    font-size: 12px;
}
.bootstrap-iso h6,
.bootstrap-iso .h6 {
    font-size: 11px;
}
.bootstrap-iso p {
    margin: 0 0 8.5px;
}
.bootstrap-iso .lead {
    margin-bottom: 17px;
    font-size: 13px;
    font-weight: 300;
    line-height: 1.4;
}
@media (min-width: 768px) {
    .bootstrap-iso .lead {
        font-size: 18px;
    }
}
.bootstrap-iso small,
.bootstrap-iso .small {
    font-size: 91%;
}
.bootstrap-iso cite {
    font-style: normal;
}
.bootstrap-iso mark,
.bootstrap-iso .mark {
    background-color: #fcf8e3;
    padding: .2em;
}
.bootstrap-iso .text-left {
    text-align: left;
}
.bootstrap-iso .text-right {
    text-align: right;
}
.bootstrap-iso .text-center {
    text-align: center;
}
.bootstrap-iso .text-justify {
    text-align: justify;
}
.bootstrap-iso .text-nowrap {
    white-space: nowrap;
}
.bootstrap-iso .text-lowercase {
    text-transform: lowercase;
}
.bootstrap-iso .text-uppercase {
    text-transform: uppercase;
}
.bootstrap-iso .text-capitalize {
    text-transform: capitalize;
}
.bootstrap-iso .text-muted {
    color: #777777;
}
.bootstrap-iso .text-primary {
    color: #428bca;
}
.bootstrap-iso a.text-primary:hover {
    color: #3071a9;
}
.bootstrap-iso .text-success {
    color: #3c763d;
}
.bootstrap-iso a.text-success:hover {
    color: #2b542c;
}
.bootstrap-iso .text-info {
    color: #31708f;
}
.bootstrap-iso a.text-info:hover {
    color: #245269;
}
.bootstrap-iso .text-warning {
    color: #8a6d3b;
}
.bootstrap-iso a.text-warning:hover {
    color: #66512c;
}
.bootstrap-iso .text-danger {
    color: #a94442;
}
.bootstrap-iso a.text-danger:hover {
    color: #843534;
}
.bootstrap-iso .bg-primary {
    color: #fff;
    background-color: #428bca;
}
.bootstrap-iso a.bg-primary:hover {
    background-color: #3071a9;
}
.bootstrap-iso .bg-success {
    background-color: #dff0d8;
}
.bootstrap-iso a.bg-success:hover {
    background-color: #c1e2b3;
}
.bootstrap-iso .bg-info {
    background-color: #d9edf7;
}
.bootstrap-iso a.bg-info:hover {
    background-color: #afd9ee;
}
.bootstrap-iso .bg-warning {
    background-color: #fcf8e3;
}
.bootstrap-iso a.bg-warning:hover {
    background-color: #f7ecb5;
}
.bootstrap-iso .bg-danger {
    background-color: #f2dede;
}
.bootstrap-iso a.bg-danger:hover {
    background-color: #e4b9b9;
}
.bootstrap-iso .page-header {
    padding-bottom: 7.5px;
    margin: 34px 0 17px;
    border-bottom: 1px solid #eeeeee;
}
.bootstrap-iso ul,
.bootstrap-iso ol {
    margin-top: 0;
    margin-bottom: 8.5px;
}
.bootstrap-iso ul ul,
.bootstrap-iso ol ul,
.bootstrap-iso ul ol,
.bootstrap-iso ol ol {
    margin-bottom: 0;
}
.bootstrap-iso .list-unstyled {
    padding-left: 0;
    list-style: none;
}
.bootstrap-iso .list-inline {
    padding-left: 0;
    list-style: none;
    margin-left: -5px;
}
.bootstrap-iso .list-inline > li {
    display: inline-block;
    padding-left: 5px;
    padding-right: 5px;
}
.bootstrap-iso dl {
    margin-top: 0;
    margin-bottom: 17px;
}
.bootstrap-iso dt,
.bootstrap-iso dd {
    line-height: 1.42857143;
}
.bootstrap-iso dt {
    font-weight: bold;
}
.bootstrap-iso dd {
    margin-left: 0;
}
@media (min-width: 768px) {
    .bootstrap-iso .dl-horizontal dt {
        float: left;
        width: 160px;
        clear: left;
        text-align: right;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .bootstrap-iso .dl-horizontal dd {
        margin-left: 180px;
    }
}
.bootstrap-iso abbr[title],
.bootstrap-iso abbr[data-original-title] {
    cursor: help;
    border-bottom: 1px dotted #777777;
}
.bootstrap-iso .initialism {
    font-size: 90%;
    text-transform: uppercase;
}
.bootstrap-iso blockquote {
    padding: 8.5px 17px;
    margin: 0 0 17px;
    font-size: 15px;
    border-left: 5px solid #eeeeee;
}
.bootstrap-iso blockquote p:last-child,
.bootstrap-iso blockquote ul:last-child,
.bootstrap-iso blockquote ol:last-child {
    margin-bottom: 0;
}
.bootstrap-iso blockquote footer,
.bootstrap-iso blockquote small,
.bootstrap-iso blockquote .small {
    display: block;
    font-size: 80%;
    line-height: 1.42857143;
    color: #777777;
}
.bootstrap-iso blockquote footer:before,
.bootstrap-iso blockquote small:before,
.bootstrap-iso blockquote .small:before {
    content: '\2014 \00A0';
}
.bootstrap-iso .blockquote-reverse,
.bootstrap-iso blockquote.pull-right {
    padding-right: 15px;
    padding-left: 0;
    border-right: 5px solid #eeeeee;
    border-left: 0;
    text-align: right;
}
.bootstrap-iso .blockquote-reverse footer:before,
.bootstrap-iso blockquote.pull-right footer:before,
.bootstrap-iso .blockquote-reverse small:before,
.bootstrap-iso blockquote.pull-right small:before,
.bootstrap-iso .blockquote-reverse .small:before,
.bootstrap-iso blockquote.pull-right .small:before {
    content: '';
}
.bootstrap-iso .blockquote-reverse footer:after,
.bootstrap-iso blockquote.pull-right footer:after,
.bootstrap-iso .blockquote-reverse small:after,
.bootstrap-iso blockquote.pull-right small:after,
.bootstrap-iso .blockquote-reverse .small:after,
.bootstrap-iso blockquote.pull-right .small:after {
    content: '\00A0 \2014';
}
.bootstrap-iso blockquote:before,
.bootstrap-iso blockquote:after {
    content: "";
}
.bootstrap-iso address {
    margin-bottom: 17px;
    font-style: normal;
    line-height: 1.42857143;
}
.bootstrap-iso code,
.bootstrap-iso kbd,
.bootstrap-iso pre,
.bootstrap-iso samp {
    font-family: Menlo, Monaco, Consolas, "Courier New", monospace;
}
.bootstrap-iso code {
    padding: 2px 4px;
    font-size: 90%;
    color: #c7254e;
    background-color: #f9f2f4;
    border-radius: 4px;
}
.bootstrap-iso kbd {
    padding: 2px 4px;
    font-size: 90%;
    color: #ffffff;
    background-color: #333333;
    border-radius: 3px;
    box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.25);
}
.bootstrap-iso kbd kbd {
    padding: 0;
    font-size: 100%;
    box-shadow: none;
}
.bootstrap-iso pre {
    display: block;
    padding: 8px;
    margin: 0 0 8.5px;
    font-size: 11px;
    line-height: 1.42857143;
    word-break: break-all;
    word-wrap: break-word;
    color: #333333;
    background-color: #f5f5f5;
    border: 1px solid #cccccc;
    border-radius: 4px;
}
.bootstrap-iso pre code {
    padding: 0;
    font-size: inherit;
    color: inherit;
    white-space: pre-wrap;
    background-color: transparent;
    border-radius: 0;
}
.bootstrap-iso .pre-scrollable {
    max-height: 340px;
    overflow-y: scroll;
}
.bootstrap-iso .container {
    margin-right: auto;
    margin-left: auto;
    padding-left: 15px;
    padding-right: 15px;
}
@media (min-width: 768px) {
    .bootstrap-iso .container {
        width: 750px;
    }
}
@media (min-width: 992px) {
    .bootstrap-iso .container {
        width: 970px;
    }
}
@media (min-width: 1200px) {
    .bootstrap-iso .container {
        width: 1170px;
    }
}
.bootstrap-iso .container-fluid {
    margin-right: auto;
    margin-left: auto;
    padding-left: 15px;
    padding-right: 15px;
}
.bootstrap-iso .row {
    margin-left: -15px;
    margin-right: -15px;
}
.bootstrap-iso .col-xs-1,
.bootstrap-iso .col-sm-1,
.bootstrap-iso .col-md-1,
.bootstrap-iso .col-lg-1,
.bootstrap-iso .col-xs-2,
.bootstrap-iso .col-sm-2,
.bootstrap-iso .col-md-2,
.bootstrap-iso .col-lg-2,
.bootstrap-iso .col-xs-3,
.bootstrap-iso .col-sm-3,
.bootstrap-iso .col-md-3,
.bootstrap-iso .col-lg-3,
.bootstrap-iso .col-xs-4,
.bootstrap-iso .col-sm-4,
.bootstrap-iso .col-md-4,
.bootstrap-iso .col-lg-4,
.bootstrap-iso .col-xs-5,
.bootstrap-iso .col-sm-5,
.bootstrap-iso .col-md-5,
.bootstrap-iso .col-lg-5,
.bootstrap-iso .col-xs-6,
.bootstrap-iso .col-sm-6,
.bootstrap-iso .col-md-6,
.bootstrap-iso .col-lg-6,
.bootstrap-iso .col-xs-7,
.bootstrap-iso .col-sm-7,
.bootstrap-iso .col-md-7,
.bootstrap-iso .col-lg-7,
.bootstrap-iso .col-xs-8,
.bootstrap-iso .col-sm-8,
.bootstrap-iso .col-md-8,
.bootstrap-iso .col-lg-8,
.bootstrap-iso .col-xs-9,
.bootstrap-iso .col-sm-9,
.bootstrap-iso .col-md-9,
.bootstrap-iso .col-lg-9,
.bootstrap-iso .col-xs-10,
.bootstrap-iso .col-sm-10,
.bootstrap-iso .col-md-10,
.bootstrap-iso .col-lg-10,
.bootstrap-iso .col-xs-11,
.bootstrap-iso .col-sm-11,
.bootstrap-iso .col-md-11,
.bootstrap-iso .col-lg-11,
.bootstrap-iso .col-xs-12,
.bootstrap-iso .col-sm-12,
.bootstrap-iso .col-md-12,
.bootstrap-iso .col-lg-12 {
    position: relative;
    min-height: 1px;
    padding-left: 15px;
    padding-right: 15px;
}
.bootstrap-iso .col-xs-1,
.bootstrap-iso .col-xs-2,
.bootstrap-iso .col-xs-3,
.bootstrap-iso .col-xs-4,
.bootstrap-iso .col-xs-5,
.bootstrap-iso .col-xs-6,
.bootstrap-iso .col-xs-7,
.bootstrap-iso .col-xs-8,
.bootstrap-iso .col-xs-9,
.bootstrap-iso .col-xs-10,
.bootstrap-iso .col-xs-11,
.bootstrap-iso .col-xs-12 {
    float: left;
}
.bootstrap-iso .col-xs-12 {
    width: 100%;
}
.bootstrap-iso .col-xs-11 {
    width: 91.66666667%;
}
.bootstrap-iso .col-xs-10 {
    width: 83.33333333%;
}
.bootstrap-iso .col-xs-9 {
    width: 75%;
}
.bootstrap-iso .col-xs-8 {
    width: 66.66666667%;
}
.bootstrap-iso .col-xs-7 {
    width: 58.33333333%;
}
.bootstrap-iso .col-xs-6 {
    width: 50%;
}
.bootstrap-iso .col-xs-5 {
    width: 41.66666667%;
}
.bootstrap-iso .col-xs-4 {
    width: 33.33333333%;
}
.bootstrap-iso .col-xs-3 {
    width: 25%;
}
.bootstrap-iso .col-xs-2 {
    width: 16.66666667%;
}
.bootstrap-iso .col-xs-1 {
    width: 8.33333333%;
}
.bootstrap-iso .col-xs-pull-12 {
    right: 100%;
}
.bootstrap-iso .col-xs-pull-11 {
    right: 91.66666667%;
}
.bootstrap-iso .col-xs-pull-10 {
    right: 83.33333333%;
}
.bootstrap-iso .col-xs-pull-9 {
    right: 75%;
}
.bootstrap-iso .col-xs-pull-8 {
    right: 66.66666667%;
}
.bootstrap-iso .col-xs-pull-7 {
    right: 58.33333333%;
}
.bootstrap-iso .col-xs-pull-6 {
    right: 50%;
}
.bootstrap-iso .col-xs-pull-5 {
    right: 41.66666667%;
}
.bootstrap-iso .col-xs-pull-4 {
    right: 33.33333333%;
}
.bootstrap-iso .col-xs-pull-3 {
    right: 25%;
}
.bootstrap-iso .col-xs-pull-2 {
    right: 16.66666667%;
}
.bootstrap-iso .col-xs-pull-1 {
    right: 8.33333333%;
}
.bootstrap-iso .col-xs-pull-0 {
    right: auto;
}
.bootstrap-iso .col-xs-push-12 {
    left: 100%;
}
.bootstrap-iso .col-xs-push-11 {
    left: 91.66666667%;
}
.bootstrap-iso .col-xs-push-10 {
    left: 83.33333333%;
}
.bootstrap-iso .col-xs-push-9 {
    left: 75%;
}
.bootstrap-iso .col-xs-push-8 {
    left: 66.66666667%;
}
.bootstrap-iso .col-xs-push-7 {
    left: 58.33333333%;
}
.bootstrap-iso .col-xs-push-6 {
    left: 50%;
}
.bootstrap-iso .col-xs-push-5 {
    left: 41.66666667%;
}
.bootstrap-iso .col-xs-push-4 {
    left: 33.33333333%;
}
.bootstrap-iso .col-xs-push-3 {
    left: 25%;
}
.bootstrap-iso .col-xs-push-2 {
    left: 16.66666667%;
}
.bootstrap-iso .col-xs-push-1 {
    left: 8.33333333%;
}
.bootstrap-iso .col-xs-push-0 {
    left: auto;
}
.bootstrap-iso .col-xs-offset-12 {
    margin-left: 100%;
}
.bootstrap-iso .col-xs-offset-11 {
    margin-left: 91.66666667%;
}
.bootstrap-iso .col-xs-offset-10 {
    margin-left: 83.33333333%;
}
.bootstrap-iso .col-xs-offset-9 {
    margin-left: 75%;
}
.bootstrap-iso .col-xs-offset-8 {
    margin-left: 66.66666667%;
}
.bootstrap-iso .col-xs-offset-7 {
    margin-left: 58.33333333%;
}
.bootstrap-iso .col-xs-offset-6 {
    margin-left: 50%;
}
.bootstrap-iso .col-xs-offset-5 {
    margin-left: 41.66666667%;
}
.bootstrap-iso .col-xs-offset-4 {
    margin-left: 33.33333333%;
}
.bootstrap-iso .col-xs-offset-3 {
    margin-left: 25%;
}
.bootstrap-iso .col-xs-offset-2 {
    margin-left: 16.66666667%;
}
.bootstrap-iso .col-xs-offset-1 {
    margin-left: 8.33333333%;
}
.bootstrap-iso .col-xs-offset-0 {
    margin-left: 0%;
}
@media (min-width: 768px) {
    .bootstrap-iso .col-sm-1,
    .bootstrap-iso .col-sm-2,
    .bootstrap-iso .col-sm-3,
    .bootstrap-iso .col-sm-4,
    .bootstrap-iso .col-sm-5,
    .bootstrap-iso .col-sm-6,
    .bootstrap-iso .col-sm-7,
    .bootstrap-iso .col-sm-8,
    .bootstrap-iso .col-sm-9,
    .bootstrap-iso .col-sm-10,
    .bootstrap-iso .col-sm-11,
    .bootstrap-iso .col-sm-12 {
        float: left;
    }
    .bootstrap-iso .col-sm-12 {
        width: 100%;
    }
    .bootstrap-iso .col-sm-11 {
        width: 91.66666667%;
    }
    .bootstrap-iso .col-sm-10 {
        width: 83.33333333%;
    }
    .bootstrap-iso .col-sm-9 {
        width: 75%;
    }
    .bootstrap-iso .col-sm-8 {
        width: 66.66666667%;
    }
    .bootstrap-iso .col-sm-7 {
        width: 58.33333333%;
    }
    .bootstrap-iso .col-sm-6 {
        width: 50%;
    }
    .bootstrap-iso .col-sm-5 {
        width: 41.66666667%;
    }
    .bootstrap-iso .col-sm-4 {
        width: 33.33333333%;
    }
    .bootstrap-iso .col-sm-3 {
        width: 25%;
    }
    .bootstrap-iso .col-sm-2 {
        width: 16.66666667%;
    }
    .bootstrap-iso .col-sm-1 {
        width: 8.33333333%;
    }
    .bootstrap-iso .col-sm-pull-12 {
        right: 100%;
    }
    .bootstrap-iso .col-sm-pull-11 {
        right: 91.66666667%;
    }
    .bootstrap-iso .col-sm-pull-10 {
        right: 83.33333333%;
    }
    .bootstrap-iso .col-sm-pull-9 {
        right: 75%;
    }
    .bootstrap-iso .col-sm-pull-8 {
        right: 66.66666667%;
    }
    .bootstrap-iso .col-sm-pull-7 {
        right: 58.33333333%;
    }
    .bootstrap-iso .col-sm-pull-6 {
        right: 50%;
    }
    .bootstrap-iso .col-sm-pull-5 {
        right: 41.66666667%;
    }
    .bootstrap-iso .col-sm-pull-4 {
        right: 33.33333333%;
    }
    .bootstrap-iso .col-sm-pull-3 {
        right: 25%;
    }
    .bootstrap-iso .col-sm-pull-2 {
        right: 16.66666667%;
    }
    .bootstrap-iso .col-sm-pull-1 {
        right: 8.33333333%;
    }
    .bootstrap-iso .col-sm-pull-0 {
        right: auto;
    }
    .bootstrap-iso .col-sm-push-12 {
        left: 100%;
    }
    .bootstrap-iso .col-sm-push-11 {
        left: 91.66666667%;
    }
    .bootstrap-iso .col-sm-push-10 {
        left: 83.33333333%;
    }
    .bootstrap-iso .col-sm-push-9 {
        left: 75%;
    }
    .bootstrap-iso .col-sm-push-8 {
        left: 66.66666667%;
    }
    .bootstrap-iso .col-sm-push-7 {
        left: 58.33333333%;
    }
    .bootstrap-iso .col-sm-push-6 {
        left: 50%;
    }
    .bootstrap-iso .col-sm-push-5 {
        left: 41.66666667%;
    }
    .bootstrap-iso .col-sm-push-4 {
        left: 33.33333333%;
    }
    .bootstrap-iso .col-sm-push-3 {
        left: 25%;
    }
    .bootstrap-iso .col-sm-push-2 {
        left: 16.66666667%;
    }
    .bootstrap-iso .col-sm-push-1 {
        left: 8.33333333%;
    }
    .bootstrap-iso .col-sm-push-0 {
        left: auto;
    }
    .bootstrap-iso .col-sm-offset-12 {
        margin-left: 100%;
    }
    .bootstrap-iso .col-sm-offset-11 {
        margin-left: 91.66666667%;
    }
    .bootstrap-iso .col-sm-offset-10 {
        margin-left: 83.33333333%;
    }
    .bootstrap-iso .col-sm-offset-9 {
        margin-left: 75%;
    }
    .bootstrap-iso .col-sm-offset-8 {
        margin-left: 66.66666667%;
    }
    .bootstrap-iso .col-sm-offset-7 {
        margin-left: 58.33333333%;
    }
    .bootstrap-iso .col-sm-offset-6 {
        margin-left: 50%;
    }
    .bootstrap-iso .col-sm-offset-5 {
        margin-left: 41.66666667%;
    }
    .bootstrap-iso .col-sm-offset-4 {
        margin-left: 33.33333333%;
    }
    .bootstrap-iso .col-sm-offset-3 {
        margin-left: 25%;
    }
    .bootstrap-iso .col-sm-offset-2 {
        margin-left: 16.66666667%;
    }
    .bootstrap-iso .col-sm-offset-1 {
        margin-left: 8.33333333%;
    }
    .bootstrap-iso .col-sm-offset-0 {
        margin-left: 0%;
    }
}
@media (min-width: 992px) {
    .bootstrap-iso .col-md-1,
    .bootstrap-iso .col-md-2,
    .bootstrap-iso .col-md-3,
    .bootstrap-iso .col-md-4,
    .bootstrap-iso .col-md-5,
    .bootstrap-iso .col-md-6,
    .bootstrap-iso .col-md-7,
    .bootstrap-iso .col-md-8,
    .bootstrap-iso .col-md-9,
    .bootstrap-iso .col-md-10,
    .bootstrap-iso .col-md-11,
    .bootstrap-iso .col-md-12 {
        float: left;
    }
    .bootstrap-iso .col-md-12 {
        width: 100%;
    }
    .bootstrap-iso .col-md-11 {
        width: 91.66666667%;
    }
    .bootstrap-iso .col-md-10 {
        width: 83.33333333%;
    }
    .bootstrap-iso .col-md-9 {
        width: 75%;
    }
    .bootstrap-iso .col-md-8 {
        width: 66.66666667%;
    }
    .bootstrap-iso .col-md-7 {
        width: 58.33333333%;
    }
    .bootstrap-iso .col-md-6 {
        width: 50%;
    }
    .bootstrap-iso .col-md-5 {
        width: 41.66666667%;
    }
    .bootstrap-iso .col-md-4 {
        width: 33.33333333%;
    }
    .bootstrap-iso .col-md-3 {
        width: 25%;
    }
    .bootstrap-iso .col-md-2 {
        width: 16.66666667%;
    }
    .bootstrap-iso .col-md-1 {
        width: 8.33333333%;
    }
    .bootstrap-iso .col-md-pull-12 {
        right: 100%;
    }
    .bootstrap-iso .col-md-pull-11 {
        right: 91.66666667%;
    }
    .bootstrap-iso .col-md-pull-10 {
        right: 83.33333333%;
    }
    .bootstrap-iso .col-md-pull-9 {
        right: 75%;
    }
    .bootstrap-iso .col-md-pull-8 {
        right: 66.66666667%;
    }
    .bootstrap-iso .col-md-pull-7 {
        right: 58.33333333%;
    }
    .bootstrap-iso .col-md-pull-6 {
        right: 50%;
    }
    .bootstrap-iso .col-md-pull-5 {
        right: 41.66666667%;
    }
    .bootstrap-iso .col-md-pull-4 {
        right: 33.33333333%;
    }
    .bootstrap-iso .col-md-pull-3 {
        right: 25%;
    }
    .bootstrap-iso .col-md-pull-2 {
        right: 16.66666667%;
    }
    .bootstrap-iso .col-md-pull-1 {
        right: 8.33333333%;
    }
    .bootstrap-iso .col-md-pull-0 {
        right: auto;
    }
    .bootstrap-iso .col-md-push-12 {
        left: 100%;
    }
    .bootstrap-iso .col-md-push-11 {
        left: 91.66666667%;
    }
    .bootstrap-iso .col-md-push-10 {
        left: 83.33333333%;
    }
    .bootstrap-iso .col-md-push-9 {
        left: 75%;
    }
    .bootstrap-iso .col-md-push-8 {
        left: 66.66666667%;
    }
    .bootstrap-iso .col-md-push-7 {
        left: 58.33333333%;
    }
    .bootstrap-iso .col-md-push-6 {
        left: 50%;
    }
    .bootstrap-iso .col-md-push-5 {
        left: 41.66666667%;
    }
    .bootstrap-iso .col-md-push-4 {
        left: 33.33333333%;
    }
    .bootstrap-iso .col-md-push-3 {
        left: 25%;
    }
    .bootstrap-iso .col-md-push-2 {
        left: 16.66666667%;
    }
    .bootstrap-iso .col-md-push-1 {
        left: 8.33333333%;
    }
    .bootstrap-iso .col-md-push-0 {
        left: auto;
    }
    .bootstrap-iso .col-md-offset-12 {
        margin-left: 100%;
    }
    .bootstrap-iso .col-md-offset-11 {
        margin-left: 91.66666667%;
    }
    .bootstrap-iso .col-md-offset-10 {
        margin-left: 83.33333333%;
    }
    .bootstrap-iso .col-md-offset-9 {
        margin-left: 75%;
    }
    .bootstrap-iso .col-md-offset-8 {
        margin-left: 66.66666667%;
    }
    .bootstrap-iso .col-md-offset-7 {
        margin-left: 58.33333333%;
    }
    .bootstrap-iso .col-md-offset-6 {
        margin-left: 50%;
    }
    .bootstrap-iso .col-md-offset-5 {
        margin-left: 41.66666667%;
    }
    .bootstrap-iso .col-md-offset-4 {
        margin-left: 33.33333333%;
    }
    .bootstrap-iso .col-md-offset-3 {
        margin-left: 25%;
    }
    .bootstrap-iso .col-md-offset-2 {
        margin-left: 16.66666667%;
    }
    .bootstrap-iso .col-md-offset-1 {
        margin-left: 8.33333333%;
    }
    .bootstrap-iso .col-md-offset-0 {
        margin-left: 0%;
    }
}
@media (min-width: 1200px) {
    .bootstrap-iso .col-lg-1,
    .bootstrap-iso .col-lg-2,
    .bootstrap-iso .col-lg-3,
    .bootstrap-iso .col-lg-4,
    .bootstrap-iso .col-lg-5,
    .bootstrap-iso .col-lg-6,
    .bootstrap-iso .col-lg-7,
    .bootstrap-iso .col-lg-8,
    .bootstrap-iso .col-lg-9,
    .bootstrap-iso .col-lg-10,
    .bootstrap-iso .col-lg-11,
    .bootstrap-iso .col-lg-12 {
        float: left;
    }
    .bootstrap-iso .col-lg-12 {
        width: 100%;
    }
    .bootstrap-iso .col-lg-11 {
        width: 91.66666667%;
    }
    .bootstrap-iso .col-lg-10 {
        width: 83.33333333%;
    }
    .bootstrap-iso .col-lg-9 {
        width: 75%;
    }
    .bootstrap-iso .col-lg-8 {
        width: 66.66666667%;
    }
    .bootstrap-iso .col-lg-7 {
        width: 58.33333333%;
    }
    .bootstrap-iso .col-lg-6 {
        width: 50%;
    }
    .bootstrap-iso .col-lg-5 {
        width: 41.66666667%;
    }
    .bootstrap-iso .col-lg-4 {
        width: 33.33333333%;
    }
    .bootstrap-iso .col-lg-3 {
        width: 25%;
    }
    .bootstrap-iso .col-lg-2 {
        width: 16.66666667%;
    }
    .bootstrap-iso .col-lg-1 {
        width: 8.33333333%;
    }
    .bootstrap-iso .col-lg-pull-12 {
        right: 100%;
    }
    .bootstrap-iso .col-lg-pull-11 {
        right: 91.66666667%;
    }
    .bootstrap-iso .col-lg-pull-10 {
        right: 83.33333333%;
    }
    .bootstrap-iso .col-lg-pull-9 {
        right: 75%;
    }
    .bootstrap-iso .col-lg-pull-8 {
        right: 66.66666667%;
    }
    .bootstrap-iso .col-lg-pull-7 {
        right: 58.33333333%;
    }
    .bootstrap-iso .col-lg-pull-6 {
        right: 50%;
    }
    .bootstrap-iso .col-lg-pull-5 {
        right: 41.66666667%;
    }
    .bootstrap-iso .col-lg-pull-4 {
        right: 33.33333333%;
    }
    .bootstrap-iso .col-lg-pull-3 {
        right: 25%;
    }
    .bootstrap-iso .col-lg-pull-2 {
        right: 16.66666667%;
    }
    .bootstrap-iso .col-lg-pull-1 {
        right: 8.33333333%;
    }
    .bootstrap-iso .col-lg-pull-0 {
        right: auto;
    }
    .bootstrap-iso .col-lg-push-12 {
        left: 100%;
    }
    .bootstrap-iso .col-lg-push-11 {
        left: 91.66666667%;
    }
    .bootstrap-iso .col-lg-push-10 {
        left: 83.33333333%;
    }
    .bootstrap-iso .col-lg-push-9 {
        left: 75%;
    }
    .bootstrap-iso .col-lg-push-8 {
        left: 66.66666667%;
    }
    .bootstrap-iso .col-lg-push-7 {
        left: 58.33333333%;
    }
    .bootstrap-iso .col-lg-push-6 {
        left: 50%;
    }
    .bootstrap-iso .col-lg-push-5 {
        left: 41.66666667%;
    }
    .bootstrap-iso .col-lg-push-4 {
        left: 33.33333333%;
    }
    .bootstrap-iso .col-lg-push-3 {
        left: 25%;
    }
    .bootstrap-iso .col-lg-push-2 {
        left: 16.66666667%;
    }
    .bootstrap-iso .col-lg-push-1 {
        left: 8.33333333%;
    }
    .bootstrap-iso .col-lg-push-0 {
        left: auto;
    }
    .bootstrap-iso .col-lg-offset-12 {
        margin-left: 100%;
    }
    .bootstrap-iso .col-lg-offset-11 {
        margin-left: 91.66666667%;
    }
    .bootstrap-iso .col-lg-offset-10 {
        margin-left: 83.33333333%;
    }
    .bootstrap-iso .col-lg-offset-9 {
        margin-left: 75%;
    }
    .bootstrap-iso .col-lg-offset-8 {
        margin-left: 66.66666667%;
    }
    .bootstrap-iso .col-lg-offset-7 {
        margin-left: 58.33333333%;
    }
    .bootstrap-iso .col-lg-offset-6 {
        margin-left: 50%;
    }
    .bootstrap-iso .col-lg-offset-5 {
        margin-left: 41.66666667%;
    }
    .bootstrap-iso .col-lg-offset-4 {
        margin-left: 33.33333333%;
    }
    .bootstrap-iso .col-lg-offset-3 {
        margin-left: 25%;
    }
    .bootstrap-iso .col-lg-offset-2 {
        margin-left: 16.66666667%;
    }
    .bootstrap-iso .col-lg-offset-1 {
        margin-left: 8.33333333%;
    }
    .bootstrap-iso .col-lg-offset-0 {
        margin-left: 0%;
    }
}
.bootstrap-iso table {
    background-color: transparent;
}
.bootstrap-iso th {
    text-align: left;
}
.bootstrap-iso .table {
    width: 100%;
    max-width: 100%;
    margin-bottom: 17px;
}
.bootstrap-iso .table > thead > tr > th,
.bootstrap-iso .table > tbody > tr > th,
.bootstrap-iso .table > tfoot > tr > th,
.bootstrap-iso .table > thead > tr > tds,
.bootstrap-iso .table > tbody > tr > tds,
.bootstrap-iso .table > tfoot > tr > tds {
    padding: 8px;
    line-height: 1.42857143;
    vertical-align: top;
    border-top: 1px solid #dddddd;
}
.bootstrap-iso .table > thead > tr > th {
    vertical-align: bottom;
    border-bottom: 2px solid #dddddd;
}
.bootstrap-iso .table > caption + thead > tr:first-child > th,
.bootstrap-iso .table > colgroup + thead > tr:first-child > th,
.bootstrap-iso .table > thead:first-child > tr:first-child > th,
.bootstrap-iso .table > caption + thead > tr:first-child > tds,
.bootstrap-iso .table > colgroup + thead > tr:first-child > tds,
.bootstrap-iso .table > thead:first-child > tr:first-child > tds {
    border-top: 0;
}
.bootstrap-iso .table > tbody + tbody {
    border-top: 2px solid #dddddd;
}
.bootstrap-iso .table .table {
    background-color: #ffffff;
}
.bootstrap-iso .table-condensed > thead > tr > th,
.bootstrap-iso .table-condensed > tbody > tr > th,
.bootstrap-iso .table-condensed > tfoot > tr > th,
.bootstrap-iso .table-condensed > thead > tr > tds,
.bootstrap-iso .table-condensed > tbody > tr > tds,
.bootstrap-iso .table-condensed > tfoot > tr > tds {
    padding: 5px;
}
.bootstrap-iso .table-bordered {
    border: 1px solid #dddddd;
}
.bootstrap-iso .table-bordered > thead > tr > th,
.bootstrap-iso .table-bordered > tbody > tr > th,
.bootstrap-iso .table-bordered > tfoot > tr > th,
.bootstrap-iso .table-bordered > thead > tr > tds,
.bootstrap-iso .table-bordered > tbody > tr > tds,
.bootstrap-iso .table-bordered > tfoot > tr > tds {
    border: 1px solid #dddddd;
}
.bootstrap-iso .table-bordered > thead > tr > th,
.bootstrap-iso .table-bordered > thead > tr > tds {
    border-bottom-width: 2px;
}
.bootstrap-iso .table-striped > tbody > tr:nth-child(odd) > tds,
.bootstrap-iso .table-striped > tbody > tr:nth-child(odd) > th {
    background-color: #f9f9f9;
}
.bootstrap-iso .table-hover > tbody > tr:hover > tds,
.bootstrap-iso .table-hover > tbody > tr:hover > th {
    background-color: #f5f5f5;
}
.bootstrap-iso table col[class*="col-"] {
    position: static;
    float: none;
    display: table-column;
}
.bootstrap-iso table tds[class*="col-"],
.bootstrap-iso table th[class*="col-"] {
    position: static;
    float: none;
    display: table-cell;
}
.bootstrap-iso .table > thead > tr > tds.active,
.bootstrap-iso .table > tbody > tr > tds.active,
.bootstrap-iso .table > tfoot > tr > tds.active,
.bootstrap-iso .table > thead > tr > th.active,
.bootstrap-iso .table > tbody > tr > th.active,
.bootstrap-iso .table > tfoot > tr > th.active,
.bootstrap-iso .table > thead > tr.active > tds,
.bootstrap-iso .table > tbody > tr.active > tds,
.bootstrap-iso .table > tfoot > tr.active > tds,
.bootstrap-iso .table > thead > tr.active > th,
.bootstrap-iso .table > tbody > tr.active > th,
.bootstrap-iso .table > tfoot > tr.active > th {
    background-color: #f5f5f5;
}
.bootstrap-iso .table-hover > tbody > tr > tds.active:hover,
.bootstrap-iso .table-hover > tbody > tr > th.active:hover,
.bootstrap-iso .table-hover > tbody > tr.active:hover > tds,
.bootstrap-iso .table-hover > tbody > tr:hover > .active,
.bootstrap-iso .table-hover > tbody > tr.active:hover > th {
    background-color: #e8e8e8;
}
.bootstrap-iso .table > thead > tr > tds.success,
.bootstrap-iso .table > tbody > tr > tds.success,
.bootstrap-iso .table > tfoot > tr > tds.success,
.bootstrap-iso .table > thead > tr > th.success,
.bootstrap-iso .table > tbody > tr > th.success,
.bootstrap-iso .table > tfoot > tr > th.success,
.bootstrap-iso .table > thead > tr.success > tds,
.bootstrap-iso .table > tbody > tr.success > tds,
.bootstrap-iso .table > tfoot > tr.success > tds,
.bootstrap-iso .table > thead > tr.success > th,
.bootstrap-iso .table > tbody > tr.success > th,
.bootstrap-iso .table > tfoot > tr.success > th {
    background-color: #dff0d8;
}
.bootstrap-iso .table-hover > tbody > tr > tds.success:hover,
.bootstrap-iso .table-hover > tbody > tr > th.success:hover,
.bootstrap-iso .table-hover > tbody > tr.success:hover > tds,
.bootstrap-iso .table-hover > tbody > tr:hover > .success,
.bootstrap-iso .table-hover > tbody > tr.success:hover > th {
    background-color: #d0e9c6;
}
.bootstrap-iso .table > thead > tr > tds.info,
.bootstrap-iso .table > tbody > tr > tds.info,
.bootstrap-iso .table > tfoot > tr > tds.info,
.bootstrap-iso .table > thead > tr > th.info,
.bootstrap-iso .table > tbody > tr > th.info,
.bootstrap-iso .table > tfoot > tr > th.info,
.bootstrap-iso .table > thead > tr.info > tds,
.bootstrap-iso .table > tbody > tr.info > tds,
.bootstrap-iso .table > tfoot > tr.info > tds,
.bootstrap-iso .table > thead > tr.info > th,
.bootstrap-iso .table > tbody > tr.info > th,
.bootstrap-iso .table > tfoot > tr.info > th {
    background-color: #d9edf7;
}
.bootstrap-iso .table-hover > tbody > tr > tds.info:hover,
.bootstrap-iso .table-hover > tbody > tr > th.info:hover,
.bootstrap-iso .table-hover > tbody > tr.info:hover > tds,
.bootstrap-iso .table-hover > tbody > tr:hover > .info,
.bootstrap-iso .table-hover > tbody > tr.info:hover > th {
    background-color: #c4e3f3;
}
.bootstrap-iso .table > thead > tr > tds.warning,
.bootstrap-iso .table > tbody > tr > tds.warning,
.bootstrap-iso .table > tfoot > tr > tds.warning,
.bootstrap-iso .table > thead > tr > th.warning,
.bootstrap-iso .table > tbody > tr > th.warning,
.bootstrap-iso .table > tfoot > tr > th.warning,
.bootstrap-iso .table > thead > tr.warning > td,
.bootstrap-iso .table > tbody > tr.warning > td,
.bootstrap-iso .table > tfoot > tr.warning > td,
.bootstrap-iso .table > thead > tr.warning > th,
.bootstrap-iso .table > tbody > tr.warning > th,
.bootstrap-iso .table > tfoot > tr.warning > th {
    background-color: #fcf8e3;
}
.bootstrap-iso .table-hover > tbody > tr > td.warning:hover,
.bootstrap-iso .table-hover > tbody > tr > th.warning:hover,
.bootstrap-iso .table-hover > tbody > tr.warning:hover > td,
.bootstrap-iso .table-hover > tbody > tr:hover > .warning,
.bootstrap-iso .table-hover > tbody > tr.warning:hover > th {
    background-color: #faf2cc;
}
.bootstrap-iso .table > thead > tr > td.danger,
.bootstrap-iso .table > tbody > tr > td.danger,
.bootstrap-iso .table > tfoot > tr > td.danger,
.bootstrap-iso .table > thead > tr > th.danger,
.bootstrap-iso .table > tbody > tr > th.danger,
.bootstrap-iso .table > tfoot > tr > th.danger,
.bootstrap-iso .table > thead > tr.danger > td,
.bootstrap-iso .table > tbody > tr.danger > td,
.bootstrap-iso .table > tfoot > tr.danger > td,
.bootstrap-iso .table > thead > tr.danger > th,
.bootstrap-iso .table > tbody > tr.danger > th,
.bootstrap-iso .table > tfoot > tr.danger > th {
    background-color: #f2dede;
}
.bootstrap-iso .table-hover > tbody > tr > td.danger:hover,
.bootstrap-iso .table-hover > tbody > tr > th.danger:hover,
.bootstrap-iso .table-hover > tbody > tr.danger:hover > td,
.bootstrap-iso .table-hover > tbody > tr:hover > .danger,
.bootstrap-iso .table-hover > tbody > tr.danger:hover > th {
    background-color: #ebcccc;
}
@media screen and (max-width: 1024px) {
    .bootstrap-iso .table-responsive {
        width: 100%;
        margin-bottom: 12.75px;
        overflow-y: hidden;
        overflow-x: auto;
        -ms-overflow-style: -ms-autohiding-scrollbar;
        border: 1px solid #dddddd;
        -webkit-overflow-scrolling: touch;
    }
    .bootstrap-iso .table-responsive > .table {
        margin-bottom: 0;
    }
    .bootstrap-iso .table-responsive > .table > thead > tr > th,
    .bootstrap-iso .table-responsive > .table > tbody > tr > th,
    .bootstrap-iso .table-responsive > .table > tfoot > tr > th,
    .bootstrap-iso .table-responsive > .table > thead > tr > td,
    .bootstrap-iso .table-responsive > .table > tbody > tr > td,
    .bootstrap-iso .table-responsive > .table > tfoot > tr > td {
        white-space: nowrap;
    }
    .bootstrap-iso .table-responsive > .table-bordered {
        border: 0;
    }
    .bootstrap-iso .table-responsive > .table-bordered > thead > tr > th:first-child,
    .bootstrap-iso .table-responsive > .table-bordered > tbody > tr > th:first-child,
    .bootstrap-iso .table-responsive > .table-bordered > tfoot > tr > th:first-child,
    .bootstrap-iso .table-responsive > .table-bordered > thead > tr > td:first-child,
    .bootstrap-iso .table-responsive > .table-bordered > tbody > tr > td:first-child,
    .bootstrap-iso .table-responsive > .table-bordered > tfoot > tr > td:first-child {
        border-left: 0;
    }
    .bootstrap-iso .table-responsive > .table-bordered > thead > tr > th:last-child,
    .bootstrap-iso .table-responsive > .table-bordered > tbody > tr > th:last-child,
    .bootstrap-iso .table-responsive > .table-bordered > tfoot > tr > th:last-child,
    .bootstrap-iso .table-responsive > .table-bordered > thead > tr > td:last-child,
    .bootstrap-iso .table-responsive > .table-bordered > tbody > tr > td:last-child,
    .bootstrap-iso .table-responsive > .table-bordered > tfoot > tr > td:last-child {
        border-right: 0;
    }
    .bootstrap-iso .table-responsive > .table-bordered > tbody > tr:last-child > th,
    .bootstrap-iso .table-responsive > .table-bordered > tfoot > tr:last-child > th,
    .bootstrap-iso .table-responsive > .table-bordered > tbody > tr:last-child > td,
    .bootstrap-iso .table-responsive > .table-bordered > tfoot > tr:last-child > td {
        border-bottom: 0;
    }
}
.bootstrap-iso fieldset {
    padding: 0;
    margin: 0;
    border: 0;
    min-width: 0;
}
.bootstrap-iso legend {
    display: block;
    width: 100%;
    padding: 0;
    margin-bottom: 17px;
    font-size: 18px;
    line-height: inherit;
    color: #333333;
    border: 0;
    border-bottom: 1px solid #e5e5e5;
}
.bootstrap-iso label {
    display: inline-block;
    max-width: 100%;
    margin-bottom: 5px;
    font-weight: bold;
}
.bootstrap-iso input[type="search"] {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
.bootstrap-iso input[type="radio"],
.bootstrap-iso input[type="checkbox"] {
    margin: 4px 0 0;
    margin-top: 1px \9;
    line-height: normal;
}
.bootstrap-iso input[type="file"] {
    display: block;
}
.bootstrap-iso input[type="range"] {
    display: block;
    width: 100%;
}
.bootstrap-iso select[multiple],
.bootstrap-iso select[size] {
    height: auto;
}
.bootstrap-iso input[type="file"]:focus,
.bootstrap-iso input[type="radio"]:focus,
.bootstrap-iso input[type="checkbox"]:focus {
    outline: thin dotted;
    outline: 5px auto -webkit-focus-ring-color;
    outline-offset: -2px;
}
.bootstrap-iso output {
    display: block;
    padding-top: 7px;
    font-size: 12px;
    line-height: 1.42857143;
    color: #555555;
}
.bootstrap-iso .form-control {
    display: block;
    width: 100%;
    height: 31px;
    padding: 6px 12px;
    font-size: 12px;
    line-height: 1.42857143;
    color: #555555;
    background-color: #ffffff;
    background-image: none;
    border: 1px solid #cccccc;
    border-radius: 4px;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    -webkit-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
    -o-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
    transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
}
.bootstrap-iso .form-control:focus {
    border-color: #66afe9;
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(102, 175, 233, 0.6);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(102, 175, 233, 0.6);
}
.bootstrap-iso .form-control::-moz-placeholder {
    color: #777777;
    opacity: 1;
}
.bootstrap-iso .form-control:-ms-input-placeholder {
    color: #777777;
}
.bootstrap-iso .form-control::-webkit-input-placeholder {
    color: #777777;
}
.bootstrap-iso .form-control[disabled],
.bootstrap-iso .form-control[readonly],
.bootstrap-iso fieldset[disabled] .form-control {
    cursor: not-allowed;
    background-color: #eeeeee;
    opacity: 1;
}
.bootstrap-iso textarea.form-control {
    height: auto;
}
.bootstrap-iso input[type="search"] {
    -webkit-appearance: none;
}
.bootstrap-iso input[type="date"],
.bootstrap-iso input[type="time"],
.bootstrap-iso input[type="datetime-local"],
.bootstrap-iso input[type="month"] {
    line-height: 31px;
    line-height: 1.42857143 \0;
}
.bootstrap-iso input[type="date"].input-sm,
.bootstrap-iso input[type="time"].input-sm,
.bootstrap-iso input[type="datetime-local"].input-sm,
.bootstrap-iso input[type="month"].input-sm {
    line-height: 28px;
}
.bootstrap-iso input[type="date"].input-lg,
.bootstrap-iso input[type="time"].input-lg,
.bootstrap-iso input[type="datetime-local"].input-lg,
.bootstrap-iso input[type="month"].input-lg {
    line-height: 42px;
}
.bootstrap-iso .form-group {
    margin-bottom: 15px;
}
.bootstrap-iso .radio,
.bootstrap-iso .checkbox {
    position: relative;
    display: block;
    min-height: 17px;
    margin-top: 10px;
    margin-bottom: 10px;
}
.bootstrap-iso .radio label,
.bootstrap-iso .checkbox label {
    padding-left: 20px;
    margin-bottom: 0;
    font-weight: normal;
    cursor: pointer;
}
.bootstrap-iso .radio input[type="radio"],
.bootstrap-iso .radio-inline input[type="radio"],
.bootstrap-iso .checkbox input[type="checkbox"],
.bootstrap-iso .checkbox-inline input[type="checkbox"] {
    position: absolute;
    margin-left: -20px;
    margin-top: 4px \9;
}
.bootstrap-iso .radio + .radio,
.bootstrap-iso .checkbox + .checkbox {
    margin-top: -5px;
}
.bootstrap-iso .radio-inline,
.bootstrap-iso .checkbox-inline {
    display: inline-block;
    padding-left: 20px;
    margin-bottom: 0;
    vertical-align: middle;
    font-weight: normal;
    cursor: pointer;
}
.bootstrap-iso .radio-inline + .radio-inline,
.bootstrap-iso .checkbox-inline + .checkbox-inline {
    margin-top: 0;
    margin-left: 10px;
}
.bootstrap-iso input[type="radio"][disabled],
.bootstrap-iso input[type="checkbox"][disabled],
.bootstrap-iso input[type="radio"].disabled,
.bootstrap-iso input[type="checkbox"].disabled,
.bootstrap-iso fieldset[disabled] input[type="radio"],
.bootstrap-iso fieldset[disabled] input[type="checkbox"] {
    cursor: not-allowed;
}
.bootstrap-iso .radio-inline.disabled,
.bootstrap-iso .checkbox-inline.disabled,
.bootstrap-iso fieldset[disabled] .radio-inline,
.bootstrap-iso fieldset[disabled] .checkbox-inline {
    cursor: not-allowed;
}
.bootstrap-iso .radio.disabled label,
.bootstrap-iso .checkbox.disabled label,
.bootstrap-iso fieldset[disabled] .radio label,
.bootstrap-iso fieldset[disabled] .checkbox label {
    cursor: not-allowed;
}
.bootstrap-iso .form-control-static {
    padding-top: 7px;
    padding-bottom: 7px;
    margin-bottom: 0;
}
.bootstrap-iso .form-control-static.input-lg,
.bootstrap-iso .form-control-static.input-sm {
    padding-left: 0;
    padding-right: 0;
}
.bootstrap-iso .input-sm,
.bootstrap-iso .form-horizontal .form-group-sm .form-control {
    height: 28px;
    padding: 5px 10px;
    font-size: 11px;
    line-height: 1.5;
    border-radius: 3px;
}
.bootstrap-iso select.input-sm {
    height: 28px;
    line-height: 28px;
}
.bootstrap-iso textarea.input-sm,
.bootstrap-iso select[multiple].input-sm {
    height: auto;
}
.bootstrap-iso .input-lg,
.bootstrap-iso .form-horizontal .form-group-lg .form-control {
    height: 42px;
    padding: 10px 16px;
    font-size: 15px;
    line-height: 1.33;
    border-radius: 6px;
}
.bootstrap-iso select.input-lg {
    height: 42px;
    line-height: 42px;
}
.bootstrap-iso textarea.input-lg,
.bootstrap-iso select[multiple].input-lg {
    height: auto;
}
.bootstrap-iso .has-feedback {
    position: relative;
}
.bootstrap-iso .has-feedback .form-control {
    padding-right: 38.75px;
}
.bootstrap-iso .form-control-feedback {
    position: absolute;
    top: 22px;
    right: 0;
    z-index: 2;
    display: block;
    width: 31px;
    height: 31px;
    line-height: 31px;
    text-align: center;
}
.bootstrap-iso .input-lg + .form-control-feedback {
    width: 42px;
    height: 42px;
    line-height: 42px;
}
.bootstrap-iso .input-sm + .form-control-feedback {
    width: 28px;
    height: 28px;
    line-height: 28px;
}
.bootstrap-iso .has-success .help-block,
.bootstrap-iso .has-success .control-label,
.bootstrap-iso .has-success .radio,
.bootstrap-iso .has-success .checkbox,
.bootstrap-iso .has-success .radio-inline,
.bootstrap-iso .has-success .checkbox-inline {
    color: #3c763d;
}
.bootstrap-iso .has-success .form-control {
    border-color: #3c763d;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.bootstrap-iso .has-success .form-control:focus {
    border-color: #2b542c;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #67b168;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #67b168;
}
.bootstrap-iso .has-success .input-group-addon {
    color: #3c763d;
    border-color: #3c763d;
    background-color: #dff0d8;
}
.bootstrap-iso .has-success .form-control-feedback {
    color: #3c763d;
}
.bootstrap-iso .has-warning .help-block,
.bootstrap-iso .has-warning .control-label,
.bootstrap-iso .has-warning .radio,
.bootstrap-iso .has-warning .checkbox,
.bootstrap-iso .has-warning .radio-inline,
.bootstrap-iso .has-warning .checkbox-inline {
    color: #8a6d3b;
}
.bootstrap-iso .has-warning .form-control {
    border-color: #8a6d3b;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.bootstrap-iso .has-warning .form-control:focus {
    border-color: #66512c;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #c0a16b;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #c0a16b;
}
.bootstrap-iso .has-warning .input-group-addon {
    color: #8a6d3b;
    border-color: #8a6d3b;
    background-color: #fcf8e3;
}
.bootstrap-iso .has-warning .form-control-feedback {
    color: #8a6d3b;
}
.bootstrap-iso .has-error .help-block,
.bootstrap-iso .has-error .control-label,
.bootstrap-iso .has-error .radio,
.bootstrap-iso .has-error .checkbox,
.bootstrap-iso .has-error .radio-inline,
.bootstrap-iso .has-error .checkbox-inline {
    color: #a94442;
}
.bootstrap-iso .has-error .form-control {
    border-color: #a94442;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.bootstrap-iso .has-error .form-control:focus {
    border-color: #843534;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ce8483;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #ce8483;
}
.bootstrap-iso .has-error .input-group-addon {
    color: #a94442;
    border-color: #a94442;
    background-color: #f2dede;
}
.bootstrap-iso .has-error .form-control-feedback {
    color: #a94442;
}
.bootstrap-iso .has-feedback label.sr-only ~ .form-control-feedback {
    top: 0;
}
.bootstrap-iso .help-block {
    display: block;
    margin-top: 5px;
    margin-bottom: 10px;
    color: #737373;
}
@media (min-width: 768px) {
    .bootstrap-iso .form-inline .form-group {
        display: inline-block;
        margin-bottom: 0;
        vertical-align: middle;
    }
    .bootstrap-iso .form-inline .form-control {
        display: inline-block;
        width: auto;
        vertical-align: middle;
    }
    .bootstrap-iso .form-inline .input-group {
        display: inline-table;
        vertical-align: middle;
    }
    .bootstrap-iso .form-inline .input-group .input-group-addon,
    .bootstrap-iso .form-inline .input-group .input-group-btn,
    .bootstrap-iso .form-inline .input-group .form-control {
        width: auto;
    }
    .bootstrap-iso .form-inline .input-group > .form-control {
        width: 100%;
    }
    .bootstrap-iso .form-inline .control-label {
        margin-bottom: 0;
        vertical-align: middle;
    }
    .bootstrap-iso .form-inline .radio,
    .bootstrap-iso .form-inline .checkbox {
        display: inline-block;
        margin-top: 0;
        margin-bottom: 0;
        vertical-align: middle;
    }
    .bootstrap-iso .form-inline .radio label,
    .bootstrap-iso .form-inline .checkbox label {
        padding-left: 0;
    }
    .bootstrap-iso .form-inline .radio input[type="radio"],
    .bootstrap-iso .form-inline .checkbox input[type="checkbox"] {
        position: relative;
        margin-left: 0;
    }
    .bootstrap-iso .form-inline .has-feedback .form-control-feedback {
        top: 0;
    }
}
.bootstrap-iso .form-horizontal .radio,
.bootstrap-iso .form-horizontal .checkbox,
.bootstrap-iso .form-horizontal .radio-inline,
.bootstrap-iso .form-horizontal .checkbox-inline {
    margin-top: 0;
    margin-bottom: 0;
    padding-top: 7px;
}
.bootstrap-iso .form-horizontal .radio,
.bootstrap-iso .form-horizontal .checkbox {
    min-height: 24px;
}
.bootstrap-iso .form-horizontal .form-group {
    margin-left: -15px;
    margin-right: -15px;
}
@media (min-width: 768px) {
    .bootstrap-iso .form-horizontal .control-label {
        text-align: right;
        margin-bottom: 0;
        padding-top: 7px;
    }
}
.bootstrap-iso .form-horizontal .has-feedback .form-control-feedback {
    top: 0;
    right: 15px;
}
@media (min-width: 768px) {
    .bootstrap-iso .form-horizontal .form-group-lg .control-label {
        padding-top: 14.3px;
    }
}
@media (min-width: 768px) {
    .bootstrap-iso .form-horizontal .form-group-sm .control-label {
        padding-top: 6px;
    }
}
.bootstrap-iso .btn {
    display: inline-block;
    margin-bottom: 0;
    font-weight: normal;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    background-image: none;
    border: 1px solid transparent;
    white-space: nowrap;
    padding: 6px 12px;
    font-size: 12px;
    line-height: 1.42857143;
    border-radius: 4px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    text-shadow:none;
}
.bootstrap-iso .btn:focus,
.bootstrap-iso .btn:active:focus,
.bootstrap-iso .btn.active:focus {
    outline: thin dotted;
    outline: 5px auto -webkit-focus-ring-color;
    outline-offset: -2px;
}
.bootstrap-iso .btn:hover,
.bootstrap-iso .btn:focus {
    color: #333333;
    text-decoration: none;
}
.bootstrap-iso .btn:active,
.bootstrap-iso .btn.active {
    outline: 0;
    background-image: none;
    -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}
.bootstrap-iso .btn.disabled,
.bootstrap-iso .btn[disabled],
.bootstrap-iso fieldset[disabled] .btn {
    cursor: not-allowed;
    pointer-events: none;
    opacity: 0.65;
    filter: alpha(opacity=65);
    -webkit-box-shadow: none;
    box-shadow: none;
}
.bootstrap-iso .btn-default {
    color: #333333;
    background-color: #ffffff;
    border-color: #cccccc;
}
.bootstrap-iso .btn-default:hover,
.bootstrap-iso .btn-default:focus,
.bootstrap-iso .btn-default:active,
.bootstrap-iso .btn-default.active,
.bootstrap-iso .open > .dropdown-toggle.btn-default {
    color: #333333;
    background-color: #e6e6e6;
    border-color: #adadad;
}
.bootstrap-iso .btn-default:active,
.bootstrap-iso .btn-default.active,
.bootstrap-iso .open > .dropdown-toggle.btn-default {
    background-image: none;
}
.bootstrap-iso .btn-default.disabled,
.bootstrap-iso .btn-default[disabled],
.bootstrap-iso fieldset[disabled] .btn-default,
.bootstrap-iso .btn-default.disabled:hover,
.bootstrap-iso .btn-default[disabled]:hover,
.bootstrap-iso fieldset[disabled] .btn-default:hover,
.bootstrap-iso .btn-default.disabled:focus,
.bootstrap-iso .btn-default[disabled]:focus,
.bootstrap-iso fieldset[disabled] .btn-default:focus,
.bootstrap-iso .btn-default.disabled:active,
.bootstrap-iso .btn-default[disabled]:active,
.bootstrap-iso fieldset[disabled] .btn-default:active,
.bootstrap-iso .btn-default.disabled.active,
.bootstrap-iso .btn-default[disabled].active,
.bootstrap-iso fieldset[disabled] .btn-default.active {
    background-color: #ffffff;
    border-color: #cccccc;
}
.bootstrap-iso .btn-default .badge {
    color: #ffffff;
    background-color: #333333;
}
.bootstrap-iso .btn-primary {
    color: #ffffff;
    background-color: #428bca;
    border-color: #357ebd;
}
.bootstrap-iso .btn-primary:hover,
.bootstrap-iso .btn-primary:focus,
.bootstrap-iso .btn-primary:active,
.bootstrap-iso .btn-primary.active,
.bootstrap-iso .open > .dropdown-toggle.btn-primary {
    color: #ffffff;
    background-color: #3071a9;
    border-color: #285e8e;
}
.bootstrap-iso .btn-primary:active,
.bootstrap-iso .btn-primary.active,
.bootstrap-iso .open > .dropdown-toggle.btn-primary {
    background-image: none;
}
.bootstrap-iso .btn-primary.disabled,
.bootstrap-iso .btn-primary[disabled],
.bootstrap-iso fieldset[disabled] .btn-primary,
.bootstrap-iso .btn-primary.disabled:hover,
.bootstrap-iso .btn-primary[disabled]:hover,
.bootstrap-iso fieldset[disabled] .btn-primary:hover,
.bootstrap-iso .btn-primary.disabled:focus,
.bootstrap-iso .btn-primary[disabled]:focus,
.bootstrap-iso fieldset[disabled] .btn-primary:focus,
.bootstrap-iso .btn-primary.disabled:active,
.bootstrap-iso .btn-primary[disabled]:active,
.bootstrap-iso fieldset[disabled] .btn-primary:active,
.bootstrap-iso .btn-primary.disabled.active,
.bootstrap-iso .btn-primary[disabled].active,
.bootstrap-iso fieldset[disabled] .btn-primary.active {
    background-color: #428bca;
    border-color: #357ebd;
}
.bootstrap-iso .btn-primary .badge {
    color: #428bca;
    background-color: #ffffff;
}
.bootstrap-iso .btn-success {
    color: #ffffff;
    background-color: #5cb85c;
    border-color: #4cae4c;
}
.bootstrap-iso .btn-success:hover,
.bootstrap-iso .btn-success:focus,
.bootstrap-iso .btn-success:active,
.bootstrap-iso .btn-success.active,
.bootstrap-iso .open > .dropdown-toggle.btn-success {
    color: #ffffff;
    background-color: #449d44;
    border-color: #398439;
}
.bootstrap-iso .btn-success:active,
.bootstrap-iso .btn-success.active,
.bootstrap-iso .open > .dropdown-toggle.btn-success {
    background-image: none;
}
.bootstrap-iso .btn-success.disabled,
.bootstrap-iso .btn-success[disabled],
.bootstrap-iso fieldset[disabled] .btn-success,
.bootstrap-iso .btn-success.disabled:hover,
.bootstrap-iso .btn-success[disabled]:hover,
.bootstrap-iso fieldset[disabled] .btn-success:hover,
.bootstrap-iso .btn-success.disabled:focus,
.bootstrap-iso .btn-success[disabled]:focus,
.bootstrap-iso fieldset[disabled] .btn-success:focus,
.bootstrap-iso .btn-success.disabled:active,
.bootstrap-iso .btn-success[disabled]:active,
.bootstrap-iso fieldset[disabled] .btn-success:active,
.bootstrap-iso .btn-success.disabled.active,
.bootstrap-iso .btn-success[disabled].active,
.bootstrap-iso fieldset[disabled] .btn-success.active {
    background-color: #5cb85c;
    border-color: #4cae4c;
}
.bootstrap-iso .btn-success .badge {
    color: #5cb85c;
    background-color: #ffffff;
}
.bootstrap-iso .btn-info {
    color: #ffffff;
    background-color: #5bc0de;
    border-color: #46b8da;
}
.bootstrap-iso .btn-info:hover,
.bootstrap-iso .btn-info:focus,
.bootstrap-iso .btn-info:active,
.bootstrap-iso .btn-info.active,
.bootstrap-iso .open > .dropdown-toggle.btn-info {
    color: #ffffff;
    background-color: #31b0d5;
    border-color: #269abc;
}
.bootstrap-iso .btn-info:active,
.bootstrap-iso .btn-info.active,
.bootstrap-iso .open > .dropdown-toggle.btn-info {
    background-image: none;
}
.bootstrap-iso .btn-info.disabled,
.bootstrap-iso .btn-info[disabled],
.bootstrap-iso fieldset[disabled] .btn-info,
.bootstrap-iso .btn-info.disabled:hover,
.bootstrap-iso .btn-info[disabled]:hover,
.bootstrap-iso fieldset[disabled] .btn-info:hover,
.bootstrap-iso .btn-info.disabled:focus,
.bootstrap-iso .btn-info[disabled]:focus,
.bootstrap-iso fieldset[disabled] .btn-info:focus,
.bootstrap-iso .btn-info.disabled:active,
.bootstrap-iso .btn-info[disabled]:active,
.bootstrap-iso fieldset[disabled] .btn-info:active,
.bootstrap-iso .btn-info.disabled.active,
.bootstrap-iso .btn-info[disabled].active,
.bootstrap-iso fieldset[disabled] .btn-info.active {
    background-color: #5bc0de;
    border-color: #46b8da;
}
.bootstrap-iso .btn-info .badge {
    color: #5bc0de;
    background-color: #ffffff;
}
.bootstrap-iso .btn-warning {
    color: #ffffff;
    background-color: #f0ad4e;
    border-color: #eea236;
}
.bootstrap-iso .btn-warning:hover,
.bootstrap-iso .btn-warning:focus,
.bootstrap-iso .btn-warning:active,
.bootstrap-iso .btn-warning.active,
.bootstrap-iso .open > .dropdown-toggle.btn-warning {
    color: #ffffff;
    background-color: #ec971f;
    border-color: #d58512;
}
.bootstrap-iso .btn-warning:active,
.bootstrap-iso .btn-warning.active,
.bootstrap-iso .open > .dropdown-toggle.btn-warning {
    background-image: none;
}
.bootstrap-iso .btn-warning.disabled,
.bootstrap-iso .btn-warning[disabled],
.bootstrap-iso fieldset[disabled] .btn-warning,
.bootstrap-iso .btn-warning.disabled:hover,
.bootstrap-iso .btn-warning[disabled]:hover,
.bootstrap-iso fieldset[disabled] .btn-warning:hover,
.bootstrap-iso .btn-warning.disabled:focus,
.bootstrap-iso .btn-warning[disabled]:focus,
.bootstrap-iso fieldset[disabled] .btn-warning:focus,
.bootstrap-iso .btn-warning.disabled:active,
.bootstrap-iso .btn-warning[disabled]:active,
.bootstrap-iso fieldset[disabled] .btn-warning:active,
.bootstrap-iso .btn-warning.disabled.active,
.bootstrap-iso .btn-warning[disabled].active,
.bootstrap-iso fieldset[disabled] .btn-warning.active {
    background-color: #f0ad4e;
    border-color: #eea236;
}
.bootstrap-iso .btn-warning .badge {
    color: #f0ad4e;
    background-color: #ffffff;
}
.bootstrap-iso .btn-danger {
    color: #ffffff;
    background-color: #d9534f;
    border-color: #d43f3a;
}
.bootstrap-iso .btn-danger:hover,
.bootstrap-iso .btn-danger:focus,
.bootstrap-iso .btn-danger:active,
.bootstrap-iso .btn-danger.active,
.bootstrap-iso .open > .dropdown-toggle.btn-danger {
    color: #ffffff;
    background-color: #c9302c;
    border-color: #ac2925;
}
.bootstrap-iso .btn-danger:active,
.bootstrap-iso .btn-danger.active,
.bootstrap-iso .open > .dropdown-toggle.btn-danger {
    background-image: none;
}
.bootstrap-iso .btn-danger.disabled,
.bootstrap-iso .btn-danger[disabled],
.bootstrap-iso fieldset[disabled] .btn-danger,
.bootstrap-iso .btn-danger.disabled:hover,
.bootstrap-iso .btn-danger[disabled]:hover,
.bootstrap-iso fieldset[disabled] .btn-danger:hover,
.bootstrap-iso .btn-danger.disabled:focus,
.bootstrap-iso .btn-danger[disabled]:focus,
.bootstrap-iso fieldset[disabled] .btn-danger:focus,
.bootstrap-iso .btn-danger.disabled:active,
.bootstrap-iso .btn-danger[disabled]:active,
.bootstrap-iso fieldset[disabled] .btn-danger:active,
.bootstrap-iso .btn-danger.disabled.active,
.bootstrap-iso .btn-danger[disabled].active,
.bootstrap-iso fieldset[disabled] .btn-danger.active {
    background-color: #d9534f;
    border-color: #d43f3a;
}
.bootstrap-iso .btn-danger .badge {
    color: #d9534f;
    background-color: #ffffff;
}
.bootstrap-iso .btn-link {
    color: #428bca;
    font-weight: normal;
    cursor: pointer;
    border-radius: 0;
}
.bootstrap-iso .btn-link,
.bootstrap-iso .btn-link:active,
.bootstrap-iso .btn-link[disabled],
.bootstrap-iso fieldset[disabled] .btn-link {
    background-color: transparent;
    -webkit-box-shadow: none;
    box-shadow: none;
}
.bootstrap-iso .btn-link,
.bootstrap-iso .btn-link:hover,
.bootstrap-iso .btn-link:focus,
.bootstrap-iso .btn-link:active {
    border-color: transparent;
}
.bootstrap-iso .btn-link:hover,
.bootstrap-iso .btn-link:focus {
    color: #2a6496;
    text-decoration: underline;
    background-color: transparent;
}
.bootstrap-iso .btn-link[disabled]:hover,
.bootstrap-iso fieldset[disabled] .btn-link:hover,
.bootstrap-iso .btn-link[disabled]:focus,
.bootstrap-iso fieldset[disabled] .btn-link:focus {
    color: #777777;
    text-decoration: none;
}
.bootstrap-iso .btn-lg,
.bootstrap-iso .btn-group-lg > .btn {
    padding: 10px 16px;
    font-size: 15px;
    line-height: 1.33;
    border-radius: 6px;
}
.bootstrap-iso .btn-sm,
.bootstrap-iso .btn-group-sm > .btn {
    padding: 5px 10px;
    font-size: 11px;
    line-height: 1.5;
    border-radius: 3px;
}
.bootstrap-iso .btn-xs,
.bootstrap-iso .btn-group-xs > .btn {
    padding: 1px 5px;
    font-size: 11px;
    line-height: 1.5;
    border-radius: 3px;
}
.bootstrap-iso .btn-block {
    display: block;
    width: 100%;
}
.bootstrap-iso .btn-block + .btn-block {
    margin-top: 5px;
}
.bootstrap-iso input[type="submit"].btn-block,
.bootstrap-iso input[type="reset"].btn-block,
.bootstrap-iso input[type="button"].btn-block {
    width: 100%;
}
.bootstrap-iso .fade {
    opacity: 0;
    -webkit-transition: opacity 0.15s linear;
    -o-transition: opacity 0.15s linear;
    transition: opacity 0.15s linear;
}
.bootstrap-iso .fade.in {
    opacity: 1;
}
.bootstrap-iso .collapse {
    display: none;
}
.bootstrap-iso .collapse.in {
    display: block;
}
.bootstrap-iso tr.collapse.in {
    display: table-row;
}
.bootstrap-iso tbody.collapse.in {
    display: table-row-group;
}
.bootstrap-iso .collapsing {
    position: relative;
    height: 0;
    overflow: hidden;
    -webkit-transition: height 0.35s ease;
    -o-transition: height 0.35s ease;
    transition: height 0.35s ease;
}
.bootstrap-iso .caret {
    display: inline-block;
    width: 0;
    height: 0;
    margin-left: 2px;
    vertical-align: middle;
    border-top: 4px solid;
    border-right: 4px solid transparent;
    border-left: 4px solid transparent;
}
.bootstrap-iso .dropdown {
    position: relative;
}
.bootstrap-iso .dropdown-toggle:focus {
    outline: 0;
}
.bootstrap-iso .dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    display: none;
    float: left;
    min-width: 160px;
    padding: 5px 0;
    margin: 2px 0 0;
    list-style: none;
    font-size: 12px;
    text-align: left;
    background-color: #ffffff;
    border: 1px solid #cccccc;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
    background-clip: padding-box;
}
.bootstrap-iso .dropdown-menu.pull-right {
    right: 0;
    left: auto;
}
.bootstrap-iso .dropdown-menu .divider {
    height: 1px;
    margin: 7.5px 0;
    overflow: hidden;
    background-color: #e5e5e5;
}
.bootstrap-iso .dropdown-menu > li > a {
    display: block;
    padding: 3px 20px;
    clear: both;
    font-weight: normal;
    line-height: 1.42857143;
    color: #333333;
    white-space: nowrap;
}
.bootstrap-iso .dropdown-menu > li > a:hover,
.bootstrap-iso .dropdown-menu > li > a:focus {
    text-decoration: none;
    color: #262626;
    background-color: #f5f5f5;
}
.bootstrap-iso .dropdown-menu > .active > a,
.bootstrap-iso .dropdown-menu > .active > a:hover,
.bootstrap-iso .dropdown-menu > .active > a:focus {
    color: #ffffff;
    text-decoration: none;
    outline: 0;
    background-color: #428bca;
}
.bootstrap-iso .dropdown-menu > .disabled > a,
.bootstrap-iso .dropdown-menu > .disabled > a:hover,
.bootstrap-iso .dropdown-menu > .disabled > a:focus {
    color: #777777;
}
.bootstrap-iso .dropdown-menu > .disabled > a:hover,
.bootstrap-iso .dropdown-menu > .disabled > a:focus {
    text-decoration: none;
    background-color: transparent;
    background-image: none;
    filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
    cursor: not-allowed;
}
.bootstrap-iso .open > .dropdown-menu {
    display: block;
}
.bootstrap-iso .open > a {
    outline: 0;
}
.bootstrap-iso .dropdown-menu-right {
    left: auto;
    right: 0;
}
.bootstrap-iso .dropdown-menu-left {
    left: 0;
    right: auto;
}
.bootstrap-iso .dropdown-header {
    display: block;
    padding: 3px 20px;
    font-size: 11px;
    line-height: 1.42857143;
    color: #777777;
    white-space: nowrap;
}
.bootstrap-iso .dropdown-backdrop {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    z-index: 990;
}
.bootstrap-iso .pull-right > .dropdown-menu {
    right: 0;
    left: auto;
}
.bootstrap-iso .dropup .caret,
.bootstrap-iso .navbar-fixed-bottom .dropdown .caret {
    border-top: 0;
    border-bottom: 4px solid;
    content: "";
}
.bootstrap-iso .dropup .dropdown-menu,
.bootstrap-iso .navbar-fixed-bottom .dropdown .dropdown-menu {
    top: auto;
    bottom: 100%;
    margin-bottom: 1px;
}
@media (min-width: 769px) {
    .bootstrap-iso .navbar-right .dropdown-menu {
        left: auto;
        right: 0;
    }
    .bootstrap-iso .navbar-right .dropdown-menu-left {
        left: 0;
        right: auto;
    }
}
.bootstrap-iso .btn-group,
.bootstrap-iso .btn-group-vertical {
    position: relative;
    display: inline-block;
    vertical-align: middle;
}
.bootstrap-iso .btn-group > .btn,
.bootstrap-iso .btn-group-vertical > .btn {
    position: relative;
    float: left;
}
.bootstrap-iso .btn-group > .btn:hover,
.bootstrap-iso .btn-group-vertical > .btn:hover,
.bootstrap-iso .btn-group > .btn:focus,
.bootstrap-iso .btn-group-vertical > .btn:focus,
.bootstrap-iso .btn-group > .btn:active,
.bootstrap-iso .btn-group-vertical > .btn:active,
.bootstrap-iso .btn-group > .btn.active,
.bootstrap-iso .btn-group-vertical > .btn.active {
    z-index: 2;
}
.bootstrap-iso .btn-group > .btn:focus,
.bootstrap-iso .btn-group-vertical > .btn:focus {
    outline: 0;
}
.bootstrap-iso .btn-group .btn + .btn,
.bootstrap-iso .btn-group .btn + .btn-group,
.bootstrap-iso .btn-group .btn-group + .btn,
.bootstrap-iso .btn-group .btn-group + .btn-group {
    margin-left: -1px;
}
.bootstrap-iso .btn-toolbar {
    margin-left: -5px;
}
.bootstrap-iso .btn-toolbar .btn-group,
.bootstrap-iso .btn-toolbar .input-group {
    float: left;
}
.bootstrap-iso .btn-toolbar > .btn,
.bootstrap-iso .btn-toolbar > .btn-group,
.bootstrap-iso .btn-toolbar > .input-group {
    margin-left: 5px;
}
.bootstrap-iso .btn-group > .btn:not(:first-child):not(:last-child):not(.dropdown-toggle) {
    border-radius: 0;
}
.bootstrap-iso .btn-group > .btn:first-child {
    margin-left: 0;
}
.bootstrap-iso .btn-group > .btn:first-child:not(:last-child):not(.dropdown-toggle) {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0;
}
.bootstrap-iso .btn-group > .btn:last-child:not(:first-child),
.bootstrap-iso .btn-group > .dropdown-toggle:not(:first-child) {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0;
}
.bootstrap-iso .btn-group > .btn-group {
    float: left;
}
.bootstrap-iso .btn-group > .btn-group:not(:first-child):not(:last-child) > .btn {
    border-radius: 0;
}
.bootstrap-iso .btn-group > .btn-group:first-child > .btn:last-child,
.bootstrap-iso .btn-group > .btn-group:first-child > .dropdown-toggle {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0;
}
.bootstrap-iso .btn-group > .btn-group:last-child > .btn:first-child {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0;
}
.bootstrap-iso .btn-group .dropdown-toggle:active,
.bootstrap-iso .btn-group.open .dropdown-toggle {
    outline: 0;
}
.bootstrap-iso .btn-group > .btn + .dropdown-toggle {
    padding-left: 8px;
    padding-right: 8px;
}
.bootstrap-iso .btn-group > .btn-lg + .dropdown-toggle {
    padding-left: 12px;
    padding-right: 12px;
}
.bootstrap-iso .btn-group.open .dropdown-toggle {
    -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
    box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}
.bootstrap-iso .btn-group.open .dropdown-toggle.btn-link {
    -webkit-box-shadow: none;
    box-shadow: none;
}
.bootstrap-iso .btn .caret {
    margin-left: 0;
}
.bootstrap-iso .btn-lg .caret {
    border-width: 5px 5px 0;
    border-bottom-width: 0;
}
.bootstrap-iso .dropup .btn-lg .caret {
    border-width: 0 5px 5px;
}
.bootstrap-iso .btn-group-vertical > .btn,
.bootstrap-iso .btn-group-vertical > .btn-group,
.bootstrap-iso .btn-group-vertical > .btn-group > .btn {
    display: block;
    float: none;
    width: 100%;
    max-width: 100%;
}
.bootstrap-iso .btn-group-vertical > .btn-group > .btn {
    float: none;
}
.bootstrap-iso .btn-group-vertical > .btn + .btn,
.bootstrap-iso .btn-group-vertical > .btn + .btn-group,
.bootstrap-iso .btn-group-vertical > .btn-group + .btn,
.bootstrap-iso .btn-group-vertical > .btn-group + .btn-group {
    margin-top: -1px;
    margin-left: 0;
}
.bootstrap-iso .btn-group-vertical > .btn:not(:first-child):not(:last-child) {
    border-radius: 0;
}
.bootstrap-iso .btn-group-vertical > .btn:first-child:not(:last-child) {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
}
.bootstrap-iso .btn-group-vertical > .btn:last-child:not(:first-child) {
    border-bottom-left-radius: 4px;
    border-top-right-radius: 0;
    border-top-left-radius: 0;
}
.bootstrap-iso .btn-group-vertical > .btn-group:not(:first-child):not(:last-child) > .btn {
    border-radius: 0;
}
.bootstrap-iso .btn-group-vertical > .btn-group:first-child:not(:last-child) > .btn:last-child,
.bootstrap-iso .btn-group-vertical > .btn-group:first-child:not(:last-child) > .dropdown-toggle {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
}
.bootstrap-iso .btn-group-vertical > .btn-group:last-child:not(:first-child) > .btn:first-child {
    border-top-right-radius: 0;
    border-top-left-radius: 0;
}
.bootstrap-iso .btn-group-justified {
    display: table;
    width: 100%;
    table-layout: fixed;
    border-collapse: separate;
}
.bootstrap-iso .btn-group-justified > .btn,
.bootstrap-iso .btn-group-justified > .btn-group {
    float: none;
    display: table-cell;
    width: 1%;
}
.bootstrap-iso .btn-group-justified > .btn-group .btn {
    width: 100%;
}
.bootstrap-iso .btn-group-justified > .btn-group .dropdown-menu {
    left: auto;
}
.bootstrap-iso [data-toggle="buttons"] > .btn > input[type="radio"],
.bootstrap-iso [data-toggle="buttons"] > .btn > input[type="checkbox"] {
    position: absolute;
    z-index: -1;
    opacity: 0;
    filter: alpha(opacity=0);
}
.bootstrap-iso .input-group {
    position: relative;
    display: table;
    border-collapse: separate;
}
.bootstrap-iso .input-group[class*="col-"] {
    float: none;
    padding-left: 0;
    padding-right: 0;
}
.bootstrap-iso .input-group .form-control {
    position: relative;
    z-index: 2;
    float: left;
    width: 100%;
    margin-bottom: 0;
}
.bootstrap-iso .input-group-lg > .form-control,
.bootstrap-iso .input-group-lg > .input-group-addon,
.bootstrap-iso .input-group-lg > .input-group-btn > .btn {
    height: 42px;
    padding: 10px 16px;
    font-size: 15px;
    line-height: 1.33;
    border-radius: 6px;
}
.bootstrap-iso select.input-group-lg > .form-control,
.bootstrap-iso select.input-group-lg > .input-group-addon,
.bootstrap-iso select.input-group-lg > .input-group-btn > .btn {
    height: 42px;
    line-height: 42px;
}
.bootstrap-iso textarea.input-group-lg > .form-control,
.bootstrap-iso textarea.input-group-lg > .input-group-addon,
.bootstrap-iso textarea.input-group-lg > .input-group-btn > .btn,
.bootstrap-iso select[multiple].input-group-lg > .form-control,
.bootstrap-iso select[multiple].input-group-lg > .input-group-addon,
.bootstrap-iso select[multiple].input-group-lg > .input-group-btn > .btn {
    height: auto;
}
.bootstrap-iso .input-group-sm > .form-control,
.bootstrap-iso .input-group-sm > .input-group-addon,
.bootstrap-iso .input-group-sm > .input-group-btn > .btn {
    height: 28px;
    padding: 5px 10px;
    font-size: 11px;
    line-height: 1.5;
    border-radius: 3px;
}
.bootstrap-iso select.input-group-sm > .form-control,
.bootstrap-iso select.input-group-sm > .input-group-addon,
.bootstrap-iso select.input-group-sm > .input-group-btn > .btn {
    height: 28px;
    line-height: 28px;
}
.bootstrap-iso textarea.input-group-sm > .form-control,
.bootstrap-iso textarea.input-group-sm > .input-group-addon,
.bootstrap-iso textarea.input-group-sm > .input-group-btn > .btn,
.bootstrap-iso select[multiple].input-group-sm > .form-control,
.bootstrap-iso select[multiple].input-group-sm > .input-group-addon,
.bootstrap-iso select[multiple].input-group-sm > .input-group-btn > .btn {
    height: auto;
}
.bootstrap-iso .input-group-addon,
.bootstrap-iso .input-group-btn,
.bootstrap-iso .input-group .form-control {
    display: table-cell;
}
.bootstrap-iso .input-group-addon:not(:first-child):not(:last-child),
.bootstrap-iso .input-group-btn:not(:first-child):not(:last-child),
.bootstrap-iso .input-group .form-control:not(:first-child):not(:last-child) {
    border-radius: 0;
}
.bootstrap-iso .input-group-addon,
.bootstrap-iso .input-group-btn {
    width: 1%;
    white-space: nowrap;
    vertical-align: middle;
}
.bootstrap-iso .input-group-addon {
    padding: 6px 12px;
    font-size: 12px;
    font-weight: normal;
    line-height: 1;
    color: #555555;
    text-align: center;
    background-color: #eeeeee;
    border: 1px solid #cccccc;
    border-radius: 4px;
}
.bootstrap-iso .input-group-addon.input-sm {
    padding: 5px 10px;
    font-size: 11px;
    border-radius: 3px;
}
.bootstrap-iso .input-group-addon.input-lg {
    padding: 10px 16px;
    font-size: 15px;
    border-radius: 6px;
}
.bootstrap-iso .input-group-addon input[type="radio"],
.bootstrap-iso .input-group-addon input[type="checkbox"] {
    margin-top: 0;
}
.bootstrap-iso .input-group .form-control:first-child,
.bootstrap-iso .input-group-addon:first-child,
.bootstrap-iso .input-group-btn:first-child > .btn,
.bootstrap-iso .input-group-btn:first-child > .btn-group > .btn,
.bootstrap-iso .input-group-btn:first-child > .dropdown-toggle,
.bootstrap-iso .input-group-btn:last-child > .btn:not(:last-child):not(.dropdown-toggle),
.bootstrap-iso .input-group-btn:last-child > .btn-group:not(:last-child) > .btn {
    border-bottom-right-radius: 0;
    border-top-right-radius: 0;
}
.bootstrap-iso .input-group-addon:first-child {
    border-right: 0;
}
.bootstrap-iso .input-group .form-control:last-child,
.bootstrap-iso .input-group-addon:last-child,
.bootstrap-iso .input-group-btn:last-child > .btn,
.bootstrap-iso .input-group-btn:last-child > .btn-group > .btn,
.bootstrap-iso .input-group-btn:last-child > .dropdown-toggle,
.bootstrap-iso .input-group-btn:first-child > .btn:not(:first-child),
.bootstrap-iso .input-group-btn:first-child > .btn-group:not(:first-child) > .btn {
    border-bottom-left-radius: 0;
    border-top-left-radius: 0;
}
.bootstrap-iso .input-group-addon:last-child {
    border-left: 0;
}
.bootstrap-iso .input-group-btn {
    position: relative;
    font-size: 0;
    white-space: nowrap;
}
.bootstrap-iso .input-group-btn > .btn {
    position: relative;
}
.bootstrap-iso .input-group-btn > .btn + .btn {
    margin-left: -1px;
}
.bootstrap-iso .input-group-btn > .btn:hover,
.bootstrap-iso .input-group-btn > .btn:focus,
.bootstrap-iso .input-group-btn > .btn:active {
    z-index: 2;
}
.bootstrap-iso .input-group-btn:first-child > .btn,
.bootstrap-iso .input-group-btn:first-child > .btn-group {
    margin-right: -1px;
}
.bootstrap-iso .input-group-btn:last-child > .btn,
.bootstrap-iso .input-group-btn:last-child > .btn-group {
    margin-left: -1px;
}
.bootstrap-iso .nav {
    margin-bottom: 0;
    padding-left: 0;
    list-style: none;
}
.bootstrap-iso .nav > li {
    position: relative;
    display: block;
}
.bootstrap-iso .nav > li > a {
    position: relative;
    display: block;
    padding: 10px 15px;
}
.bootstrap-iso .nav > li > a:hover,
.bootstrap-iso .nav > li > a:focus {
    text-decoration: none;
    background-color: #eeeeee;
}
.bootstrap-iso .nav > li.disabled > a {
    color: #777777;
}
.bootstrap-iso .nav > li.disabled > a:hover,
.bootstrap-iso .nav > li.disabled > a:focus {
    color: #777777;
    text-decoration: none;
    background-color: transparent;
    cursor: not-allowed;
}
.bootstrap-iso .nav .open > a,
.bootstrap-iso .nav .open > a:hover,
.bootstrap-iso .nav .open > a:focus {
    background-color: #eeeeee;
    border-color: #428bca;
}
.bootstrap-iso .nav .nav-divider {
    height: 1px;
    margin: 7.5px 0;
    overflow: hidden;
    background-color: #e5e5e5;
}
.bootstrap-iso .nav > li > a > img {
    max-width: none;
}
.bootstrap-iso .nav-tabs {
    border-bottom: 1px solid #dddddd;
}
.bootstrap-iso .nav-tabs > li {
    float: left;
    margin-bottom: -1px;
}
.bootstrap-iso .nav-tabs > li > a {
    margin-right: 2px;
    line-height: 1.42857143;
    border: 1px solid transparent;
    border-radius: 4px 4px 0 0;
}
.bootstrap-iso .nav-tabs > li > a:hover {
    border-color: #eeeeee #eeeeee #dddddd;
}
.bootstrap-iso .nav-tabs > li.active > a,
.bootstrap-iso .nav-tabs > li.active > a:hover,
.bootstrap-iso .nav-tabs > li.active > a:focus {
    color: #555555;
    background-color: #ffffff;
    border: 1px solid #dddddd;
    border-bottom-color: transparent;
    cursor: default;
}
.bootstrap-iso .nav-tabs.nav-justified {
    width: 100%;
    border-bottom: 0;
}
.bootstrap-iso .nav-tabs.nav-justified > li {
    float: none;
}
.bootstrap-iso .nav-tabs.nav-justified > li > a {
    text-align: center;
    margin-bottom: 5px;
}
.bootstrap-iso .nav-tabs.nav-justified > .dropdown .dropdown-menu {
    top: auto;
    left: auto;
}
@media (min-width: 769px) {
    .bootstrap-iso .nav-tabs.nav-justified > li {
        display: table-cell;
        width: 1%;
    }
    .bootstrap-iso .nav-tabs.nav-justified > li > a {
        margin-bottom: 0;
    }
}
.bootstrap-iso .nav-tabs.nav-justified > li > a {
    margin-right: 0;
    border-radius: 4px;
}
.bootstrap-iso .nav-tabs.nav-justified > .active > a,
.bootstrap-iso .nav-tabs.nav-justified > .active > a:hover,
.bootstrap-iso .nav-tabs.nav-justified > .active > a:focus {
    border: 1px solid #dddddd;
}
@media (min-width: 769px) {
    .bootstrap-iso .nav-tabs.nav-justified > li > a {
        border-bottom: 1px solid #dddddd;
        border-radius: 4px 4px 0 0;
    }
    .bootstrap-iso .nav-tabs.nav-justified > .active > a,
    .bootstrap-iso .nav-tabs.nav-justified > .active > a:hover,
    .bootstrap-iso .nav-tabs.nav-justified > .active > a:focus {
        border-bottom-color: #ffffff;
    }
}
.bootstrap-iso .nav-pills > li {
    float: left;
}
.bootstrap-iso .nav-pills > li > a {
    border-radius: 4px;
}
.bootstrap-iso .nav-pills > li + li {
    margin-left: 2px;
}
.bootstrap-iso .nav-pills > li.active > a,
.bootstrap-iso .nav-pills > li.active > a:hover,
.bootstrap-iso .nav-pills > li.active > a:focus {
    color: #ffffff;
    background-color: #428bca;
}
.bootstrap-iso .nav-stacked > li {
    float: none;
}
.bootstrap-iso .nav-stacked > li + li {
    margin-top: 2px;
    margin-left: 0;
}
.bootstrap-iso .nav-justified {
    width: 100%;
}
.bootstrap-iso .nav-justified > li {
    float: none;
}
.bootstrap-iso .nav-justified > li > a {
    text-align: center;
    margin-bottom: 5px;
}
.bootstrap-iso .nav-justified > .dropdown .dropdown-menu {
    top: auto;
    left: auto;
}
@media (min-width: 769px) {
    .bootstrap-iso .nav-justified > li {
        display: table-cell;
        width: 1%;
    }
    .bootstrap-iso .nav-justified > li > a {
        margin-bottom: 0;
    }
}
.bootstrap-iso .nav-tabs-justified {
    border-bottom: 0;
}
.bootstrap-iso .nav-tabs-justified > li > a {
    margin-right: 0;
    border-radius: 4px;
}
.bootstrap-iso .nav-tabs-justified > .active > a,
.bootstrap-iso .nav-tabs-justified > .active > a:hover,
.bootstrap-iso .nav-tabs-justified > .active > a:focus {
    border: 1px solid #dddddd;
}
@media (min-width: 769px) {
    .bootstrap-iso .nav-tabs-justified > li > a {
        border-bottom: 1px solid #dddddd;
        border-radius: 4px 4px 0 0;
    }
    .bootstrap-iso .nav-tabs-justified > .active > a,
    .bootstrap-iso .nav-tabs-justified > .active > a:hover,
    .bootstrap-iso .nav-tabs-justified > .active > a:focus {
        border-bottom-color: #ffffff;
    }
}
.bootstrap-iso .tab-content > .tab-pane {
    display: none;
}
.bootstrap-iso .tab-content > .active {
    display: block;
}
.bootstrap-iso .nav-tabs .dropdown-menu {
    margin-top: -1px;
    border-top-right-radius: 0;
    border-top-left-radius: 0;
}
.bootstrap-iso .navbar {
    position: relative;
    min-height: 50px;
    margin-bottom: 17px;
    border: 1px solid transparent;
}
@media (min-width: 769px) {
    .bootstrap-iso .navbar {
        border-radius: 4px;
    }
}
@media (min-width: 769px) {
    .bootstrap-iso .navbar-header {
        float: left;
    }
}
.bootstrap-iso .navbar-collapse {
    overflow-x: visible;
    padding-right: 15px;
    padding-left: 15px;
    border-top: 1px solid transparent;
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
    -webkit-overflow-scrolling: touch;
}
.bootstrap-iso .navbar-collapse.in {
    overflow-y: auto;
}
@media (min-width: 769px) {
    .bootstrap-iso .navbar-collapse {
        width: auto;
        border-top: 0;
        box-shadow: none;
    }
    .bootstrap-iso .navbar-collapse.collapse {
        display: block !important;
        height: auto !important;
        padding-bottom: 0;
        overflow: visible !important;
    }
    .bootstrap-iso .navbar-collapse.in {
        overflow-y: visible;
    }
    .bootstrap-iso .navbar-fixed-top .navbar-collapse,
    .bootstrap-iso .navbar-static-top .navbar-collapse,
    .bootstrap-iso .navbar-fixed-bottom .navbar-collapse {
        padding-left: 0;
        padding-right: 0;
    }
}
.bootstrap-iso .navbar-fixed-top .navbar-collapse,
.bootstrap-iso .navbar-fixed-bottom .navbar-collapse {
    max-height: 340px;
}
@media (max-width: 480px) and (orientation: landscape) {
    .bootstrap-iso .navbar-fixed-top .navbar-collapse,
    .bootstrap-iso .navbar-fixed-bottom .navbar-collapse {
        max-height: 200px;
    }
}
.bootstrap-iso .container > .navbar-header,
.bootstrap-iso .container-fluid > .navbar-header,
.bootstrap-iso .container > .navbar-collapse,
.bootstrap-iso .container-fluid > .navbar-collapse {
    margin-right: -15px;
    margin-left: -15px;
}
@media (min-width: 768px) {
    .bootstrap-iso .container > .navbar-header,
    .bootstrap-iso .container-fluid > .navbar-header,
    .bootstrap-iso .container > .navbar-collapse,
    .bootstrap-iso .container-fluid > .navbar-collapse {
        margin-right: 0;
        margin-left: 0;
    }
}
.bootstrap-iso .navbar-static-top {
    z-index: 1000;
    border-width: 0 0 1px;
}
@media (min-width: 769px) {
    .bootstrap-iso .navbar-static-top {
        border-radius: 0;
    }
}
.bootstrap-iso .navbar-fixed-top,
.bootstrap-iso .navbar-fixed-bottom {
    position: fixed;
    right: 0;
    left: 0;
    z-index: 1030;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
}
@media (min-width: 769px) {
    .bootstrap-iso .navbar-fixed-top,
    .bootstrap-iso .navbar-fixed-bottom {
        border-radius: 0;
    }
}
.bootstrap-iso .navbar-fixed-top {
    top: 0;
    border-width: 0 0 1px;
}
.bootstrap-iso .navbar-fixed-bottom {
    bottom: 0;
    margin-bottom: 0;
    border-width: 1px 0 0;
}
.bootstrap-iso .navbar-brand {
    float: left;
    padding: 16.5px 15px;
    font-size: 15px;
    line-height: 17px;
    height: 50px;
}
.bootstrap-iso .navbar-brand:hover,
.bootstrap-iso .navbar-brand:focus {
    text-decoration: none;
}
@media (min-width: 769px) {
    .bootstrap-iso .navbar > .container .navbar-brand,
    .bootstrap-iso .navbar > .container-fluid .navbar-brand {
        margin-left: -15px;
    }
}
.bootstrap-iso .navbar-toggle {
    position: relative;
    float: right;
    margin-right: 15px;
    padding: 9px 10px;
    margin-top: 8px;
    margin-bottom: 8px;
    background-color: transparent;
    background-image: none;
    border: 1px solid transparent;
    border-radius: 4px;
}
.bootstrap-iso .navbar-toggle:focus {
    outline: 0;
}
.bootstrap-iso .navbar-toggle .icon-bar {
    display: block;
    width: 22px;
    height: 2px;
    border-radius: 1px;
}
.bootstrap-iso .navbar-toggle .icon-bar + .icon-bar {
    margin-top: 4px;
}
@media (min-width: 769px) {
    .bootstrap-iso .navbar-toggle {
        display: none;
    }
}
.bootstrap-iso .navbar-nav {
    margin: 8.25px -15px;
}
.bootstrap-iso .navbar-nav > li > a {
    padding-top: 10px;
    padding-bottom: 10px;
    line-height: 17px;
}
@media (max-width: 768px) {
    .bootstrap-iso .navbar-nav .open .dropdown-menu {
        position: static;
        float: none;
        width: auto;
        margin-top: 0;
        background-color: transparent;
        border: 0;
        box-shadow: none;
    }
    .bootstrap-iso .navbar-nav .open .dropdown-menu > li > a,
    .bootstrap-iso .navbar-nav .open .dropdown-menu .dropdown-header {
        padding: 5px 15px 5px 25px;
    }
    .bootstrap-iso .navbar-nav .open .dropdown-menu > li > a {
        line-height: 17px;
    }
    .bootstrap-iso .navbar-nav .open .dropdown-menu > li > a:hover,
    .bootstrap-iso .navbar-nav .open .dropdown-menu > li > a:focus {
        background-image: none;
    }
}
@media (min-width: 769px) {
    .bootstrap-iso .navbar-nav {
        float: left;
        margin: 0;
    }
    .bootstrap-iso .navbar-nav > li {
        float: left;
    }
    .bootstrap-iso .navbar-nav > li > a {
        padding-top: 16.5px;
        padding-bottom: 16.5px;
    }
    .bootstrap-iso .navbar-nav.navbar-right:last-child {
        margin-right: -15px;
    }
}
@media (min-width: 769px) {
    .bootstrap-iso .navbar-left {
        float: left !important;
    }
    .bootstrap-iso .navbar-right {
        float: right !important;
    }
}
.bootstrap-iso .navbar-form {
    margin-left: -15px;
    margin-right: -15px;
    padding: 10px 15px;
    border-top: 1px solid transparent;
    border-bottom: 1px solid transparent;
    -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 1px 0 rgba(255, 255, 255, 0.1);
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 1px 0 rgba(255, 255, 255, 0.1);
    margin-top: 9.5px;
    margin-bottom: 9.5px;
}
@media (min-width: 769px) {
    .bootstrap-iso .navbar-form .form-group {
        display: inline-block;
        margin-bottom: 0;
        vertical-align: middle;
    }
    .bootstrap-iso .navbar-form .form-control {
        display: inline-block;
        width: auto;
        vertical-align: middle;
    }
    .bootstrap-iso .navbar-form .input-group {
        display: inline-table;
        vertical-align: middle;
    }
    .bootstrap-iso .navbar-form .input-group .input-group-addon,
    .bootstrap-iso .navbar-form .input-group .input-group-btn,
    .bootstrap-iso .navbar-form .input-group .form-control {
        width: auto;
    }
    .bootstrap-iso .navbar-form .input-group > .form-control {
        width: 100%;
    }
    .bootstrap-iso .navbar-form .control-label {
        margin-bottom: 0;
        vertical-align: middle;
    }
    .bootstrap-iso .navbar-form .radio,
    .bootstrap-iso .navbar-form .checkbox {
        display: inline-block;
        margin-top: 0;
        margin-bottom: 0;
        vertical-align: middle;
    }
    .bootstrap-iso .navbar-form .radio label,
    .bootstrap-iso .navbar-form .checkbox label {
        padding-left: 0;
    }
    .bootstrap-iso .navbar-form .radio input[type="radio"],
    .bootstrap-iso .navbar-form .checkbox input[type="checkbox"] {
        position: relative;
        margin-left: 0;
    }
    .bootstrap-iso .navbar-form .has-feedback .form-control-feedback {
        top: 0;
    }
}
@media (max-width: 768px) {
    .bootstrap-iso .navbar-form .form-group {
        margin-bottom: 5px;
    }
}
@media (min-width: 769px) {
    .bootstrap-iso .navbar-form {
        width: auto;
        border: 0;
        margin-left: 0;
        margin-right: 0;
        padding-top: 0;
        padding-bottom: 0;
        -webkit-box-shadow: none;
        box-shadow: none;
    }
    .bootstrap-iso .navbar-form.navbar-right:last-child {
        margin-right: -15px;
    }
}
.bootstrap-iso .navbar-nav > li > .dropdown-menu {
    margin-top: 0;
    border-top-right-radius: 0;
    border-top-left-radius: 0;
}
.bootstrap-iso .navbar-fixed-bottom .navbar-nav > li > .dropdown-menu {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
}
.bootstrap-iso .navbar-btn {
    margin-top: 9.5px;
    margin-bottom: 9.5px;
}
.bootstrap-iso .navbar-btn.btn-sm {
    margin-top: 11px;
    margin-bottom: 11px;
}
.bootstrap-iso .navbar-btn.btn-xs {
    margin-top: 14px;
    margin-bottom: 14px;
}
.bootstrap-iso .navbar-text {
    margin-top: 16.5px;
    margin-bottom: 16.5px;
}
@media (min-width: 769px) {
    .bootstrap-iso .navbar-text {
        float: left;
        margin-left: 15px;
        margin-right: 15px;
    }
    .bootstrap-iso .navbar-text.navbar-right:last-child {
        margin-right: 0;
    }
}
.bootstrap-iso .navbar-default {
    background-color: #f8f8f8;
    border-color: #e7e7e7;
}
.bootstrap-iso .navbar-default .navbar-brand {
    color: #777777;
}
.bootstrap-iso .navbar-default .navbar-brand:hover,
.bootstrap-iso .navbar-default .navbar-brand:focus {
    color: #5e5e5e;
    background-color: transparent;
}
.bootstrap-iso .navbar-default .navbar-text {
    color: #777777;
}
.bootstrap-iso .navbar-default .navbar-nav > li > a {
    color: #777777;
}
.bootstrap-iso .navbar-default .navbar-nav > li > a:hover,
.bootstrap-iso .navbar-default .navbar-nav > li > a:focus {
    color: #333333;
    background-color: transparent;
}
.bootstrap-iso .navbar-default .navbar-nav > .active > a,
.bootstrap-iso .navbar-default .navbar-nav > .active > a:hover,
.bootstrap-iso .navbar-default .navbar-nav > .active > a:focus {
    color: #555555;
    background-color: #e7e7e7;
}
.bootstrap-iso .navbar-default .navbar-nav > .disabled > a,
.bootstrap-iso .navbar-default .navbar-nav > .disabled > a:hover,
.bootstrap-iso .navbar-default .navbar-nav > .disabled > a:focus {
    color: #cccccc;
    background-color: transparent;
}
.bootstrap-iso .navbar-default .navbar-toggle {
    border-color: #dddddd;
}
.bootstrap-iso .navbar-default .navbar-toggle:hover,
.bootstrap-iso .navbar-default .navbar-toggle:focus {
    background-color: #dddddd;
}
.bootstrap-iso .navbar-default .navbar-toggle .icon-bar {
    background-color: #888888;
}
.bootstrap-iso .navbar-default .navbar-collapse,
.bootstrap-iso .navbar-default .navbar-form {
    border-color: #e7e7e7;
}
.bootstrap-iso .navbar-default .navbar-nav > .open > a,
.bootstrap-iso .navbar-default .navbar-nav > .open > a:hover,
.bootstrap-iso .navbar-default .navbar-nav > .open > a:focus {
    background-color: #e7e7e7;
    color: #555555;
}
@media (max-width: 768px) {
    .bootstrap-iso .navbar-default .navbar-nav .open .dropdown-menu > li > a {
        color: #777777;
    }
    .bootstrap-iso .navbar-default .navbar-nav .open .dropdown-menu > li > a:hover,
    .bootstrap-iso .navbar-default .navbar-nav .open .dropdown-menu > li > a:focus {
        color: #333333;
        background-color: transparent;
    }
    .bootstrap-iso .navbar-default .navbar-nav .open .dropdown-menu > .active > a,
    .bootstrap-iso .navbar-default .navbar-nav .open .dropdown-menu > .active > a:hover,
    .bootstrap-iso .navbar-default .navbar-nav .open .dropdown-menu > .active > a:focus {
        color: #555555;
        background-color: #e7e7e7;
    }
    .bootstrap-iso .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a,
    .bootstrap-iso .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:hover,
    .bootstrap-iso .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:focus {
        color: #cccccc;
        background-color: transparent;
    }
}
.bootstrap-iso .navbar-default .navbar-link {
    color: #777777;
}
.bootstrap-iso .navbar-default .navbar-link:hover {
    color: #333333;
}
.bootstrap-iso .navbar-default .btn-link {
    color: #777777;
}
.bootstrap-iso .navbar-default .btn-link:hover,
.bootstrap-iso .navbar-default .btn-link:focus {
    color: #333333;
}
.bootstrap-iso .navbar-default .btn-link[disabled]:hover,
.bootstrap-iso fieldset[disabled] .navbar-default .btn-link:hover,
.bootstrap-iso .navbar-default .btn-link[disabled]:focus,
.bootstrap-iso fieldset[disabled] .navbar-default .btn-link:focus {
    color: #cccccc;
}
.bootstrap-iso .navbar-inverse {
    background-color: #222222;
    border-color: #080808;
}
.bootstrap-iso .navbar-inverse .navbar-brand {
    color: #777777;
}
.bootstrap-iso .navbar-inverse .navbar-brand:hover,
.bootstrap-iso .navbar-inverse .navbar-brand:focus {
    color: #ffffff;
    background-color: transparent;
}
.bootstrap-iso .navbar-inverse .navbar-text {
    color: #777777;
}
.bootstrap-iso .navbar-inverse .navbar-nav > li > a {
    color: #777777;
}
.bootstrap-iso .navbar-inverse .navbar-nav > li > a:hover,
.bootstrap-iso .navbar-inverse .navbar-nav > li > a:focus {
    color: #ffffff;
    background-color: transparent;
}
.bootstrap-iso .navbar-inverse .navbar-nav > .active > a,
.bootstrap-iso .navbar-inverse .navbar-nav > .active > a:hover,
.bootstrap-iso .navbar-inverse .navbar-nav > .active > a:focus {
    color: #ffffff;
    background-color: #080808;
}
.bootstrap-iso .navbar-inverse .navbar-nav > .disabled > a,
.bootstrap-iso .navbar-inverse .navbar-nav > .disabled > a:hover,
.bootstrap-iso .navbar-inverse .navbar-nav > .disabled > a:focus {
    color: #444444;
    background-color: transparent;
}
.bootstrap-iso .navbar-inverse .navbar-toggle {
    border-color: #333333;
}
.bootstrap-iso .navbar-inverse .navbar-toggle:hover,
.bootstrap-iso .navbar-inverse .navbar-toggle:focus {
    background-color: #333333;
}
.bootstrap-iso .navbar-inverse .navbar-toggle .icon-bar {
    background-color: #ffffff;
}
.bootstrap-iso .navbar-inverse .navbar-collapse,
.bootstrap-iso .navbar-inverse .navbar-form {
    border-color: #101010;
}
.bootstrap-iso .navbar-inverse .navbar-nav > .open > a,
.bootstrap-iso .navbar-inverse .navbar-nav > .open > a:hover,
.bootstrap-iso .navbar-inverse .navbar-nav > .open > a:focus {
    background-color: #080808;
    color: #ffffff;
}
@media (max-width: 768px) {
    .bootstrap-iso .navbar-inverse .navbar-nav .open .dropdown-menu > .dropdown-header {
        border-color: #080808;
    }
    .bootstrap-iso .navbar-inverse .navbar-nav .open .dropdown-menu .divider {
        background-color: #080808;
    }
    .bootstrap-iso .navbar-inverse .navbar-nav .open .dropdown-menu > li > a {
        color: #777777;
    }
    .bootstrap-iso .navbar-inverse .navbar-nav .open .dropdown-menu > li > a:hover,
    .bootstrap-iso .navbar-inverse .navbar-nav .open .dropdown-menu > li > a:focus {
        color: #ffffff;
        background-color: transparent;
    }
    .bootstrap-iso .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a,
    .bootstrap-iso .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:hover,
    .bootstrap-iso .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:focus {
        color: #ffffff;
        background-color: #080808;
    }
    .bootstrap-iso .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a,
    .bootstrap-iso .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a:hover,
    .bootstrap-iso .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a:focus {
        color: #444444;
        background-color: transparent;
    }
}
.bootstrap-iso .navbar-inverse .navbar-link {
    color: #777777;
}
.bootstrap-iso .navbar-inverse .navbar-link:hover {
    color: #ffffff;
}
.bootstrap-iso .navbar-inverse .btn-link {
    color: #777777;
}
.bootstrap-iso .navbar-inverse .btn-link:hover,
.bootstrap-iso .navbar-inverse .btn-link:focus {
    color: #ffffff;
}
.bootstrap-iso .navbar-inverse .btn-link[disabled]:hover,
.bootstrap-iso fieldset[disabled] .navbar-inverse .btn-link:hover,
.bootstrap-iso .navbar-inverse .btn-link[disabled]:focus,
.bootstrap-iso fieldset[disabled] .navbar-inverse .btn-link:focus {
    color: #444444;
}
.bootstrap-iso .breadcrumb {
    padding: 8px 15px;
    margin-bottom: 17px;
    list-style: none;
    background-color: #f5f5f5;
    border-radius: 4px;
}
.bootstrap-iso .breadcrumb > li {
    display: inline-block;
}
.bootstrap-iso .breadcrumb > li + li:before {
    content: "/\00a0";
    padding: 0 5px;
    color: #cccccc;
}
.bootstrap-iso .breadcrumb > .active {
    color: #777777;
}
.bootstrap-iso .pagination {
    display: inline-block;
    padding-left: 0;
    margin: 17px 0;
    border-radius: 4px;
}
.bootstrap-iso .pagination > li {
    display: inline;
}
.bootstrap-iso .pagination > li > a,
.bootstrap-iso .pagination > li > span {
    position: relative;
    float: left;
    padding: 6px 12px;
    line-height: 1.42857143;
    text-decoration: none;
    color: #428bca;
    background-color: #ffffff;
    border: 1px solid #dddddd;
    margin-left: -1px;
}
.bootstrap-iso .pagination > li:first-child > a,
.bootstrap-iso .pagination > li:first-child > span {
    margin-left: 0;
    border-bottom-left-radius: 4px;
    border-top-left-radius: 4px;
}
.bootstrap-iso .pagination > li:last-child > a,
.bootstrap-iso .pagination > li:last-child > span {
    border-bottom-right-radius: 4px;
    border-top-right-radius: 4px;
}
.bootstrap-iso .pagination > li > a:hover,
.bootstrap-iso .pagination > li > span:hover,
.bootstrap-iso .pagination > li > a:focus,
.bootstrap-iso .pagination > li > span:focus {
    color: #2a6496;
    background-color: #eeeeee;
    border-color: #dddddd;
}
.bootstrap-iso .pagination > .active > a,
.bootstrap-iso .pagination > .active > span,
.bootstrap-iso .pagination > .active > a:hover,
.bootstrap-iso .pagination > .active > span:hover,
.bootstrap-iso .pagination > .active > a:focus,
.bootstrap-iso .pagination > .active > span:focus {
    z-index: 2;
    color: #ffffff;
    background-color: #428bca;
    border-color: #428bca;
    cursor: default;
}
.bootstrap-iso .pagination > .disabled > span,
.bootstrap-iso .pagination > .disabled > span:hover,
.bootstrap-iso .pagination > .disabled > span:focus,
.bootstrap-iso .pagination > .disabled > a,
.bootstrap-iso .pagination > .disabled > a:hover,
.bootstrap-iso .pagination > .disabled > a:focus {
    color: #777777;
    background-color: #ffffff;
    border-color: #dddddd;
    cursor: not-allowed;
}
.bootstrap-iso .pagination-lg > li > a,
.bootstrap-iso .pagination-lg > li > span {
    padding: 10px 16px;
    font-size: 15px;
}
.bootstrap-iso .pagination-lg > li:first-child > a,
.bootstrap-iso .pagination-lg > li:first-child > span {
    border-bottom-left-radius: 6px;
    border-top-left-radius: 6px;
}
.bootstrap-iso .pagination-lg > li:last-child > a,
.bootstrap-iso .pagination-lg > li:last-child > span {
    border-bottom-right-radius: 6px;
    border-top-right-radius: 6px;
}
.bootstrap-iso .pagination-sm > li > a,
.bootstrap-iso .pagination-sm > li > span {
    padding: 5px 10px;
    font-size: 11px;
}
.bootstrap-iso .pagination-sm > li:first-child > a,
.bootstrap-iso .pagination-sm > li:first-child > span {
    border-bottom-left-radius: 3px;
    border-top-left-radius: 3px;
}
.bootstrap-iso .pagination-sm > li:last-child > a,
.bootstrap-iso .pagination-sm > li:last-child > span {
    border-bottom-right-radius: 3px;
    border-top-right-radius: 3px;
}
.bootstrap-iso .pager {
    padding-left: 0;
    margin: 17px 0;
    list-style: none;
    text-align: center;
}
.bootstrap-iso .pager li {
    display: inline;
}
.bootstrap-iso .pager li > a,
.bootstrap-iso .pager li > span {
    display: inline-block;
    padding: 5px 14px;
    background-color: #ffffff;
    border: 1px solid #dddddd;
    border-radius: 15px;
}
.bootstrap-iso .pager li > a:hover,
.bootstrap-iso .pager li > a:focus {
    text-decoration: none;
    background-color: #eeeeee;
}
.bootstrap-iso .pager .next > a,
.bootstrap-iso .pager .next > span {
    float: right;
}
.bootstrap-iso .pager .previous > a,
.bootstrap-iso .pager .previous > span {
    float: left;
}
.bootstrap-iso .pager .disabled > a,
.bootstrap-iso .pager .disabled > a:hover,
.bootstrap-iso .pager .disabled > a:focus,
.bootstrap-iso .pager .disabled > span {
    color: #777777;
    background-color: #ffffff;
    cursor: not-allowed;
}
.bootstrap-iso .label {
    display: inline;
    padding: .2em .6em .3em;
    font-size: 75%;
    line-height: 1;
    color: #ffffff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: .25em;
}
.bootstrap-iso a.label:hover,
.bootstrap-iso a.label:focus {
    color: #ffffff;
    text-decoration: none;
    cursor: pointer;
}
.bootstrap-iso .label:empty {
    display: none;
}
.bootstrap-iso .btn .label {
    position: relative;
    top: -1px;
}
.bootstrap-iso .label-default {
    background-color: #777777;
}
.bootstrap-iso .label-default[href]:hover,
.bootstrap-iso .label-default[href]:focus {
    background-color: #5e5e5e;
}
.bootstrap-iso .label-primary {
    background-color: #428bca;
}
.bootstrap-iso .label-primary[href]:hover,
.bootstrap-iso .label-primary[href]:focus {
    background-color: #3071a9;
}
.bootstrap-iso .label-success {
    background-color: #5cb85c;
}
.bootstrap-iso .label-success[href]:hover,
.bootstrap-iso .label-success[href]:focus {
    background-color: #449d44;
}
.bootstrap-iso .label-info {
    background-color: #5bc0de;
}
.bootstrap-iso .label-info[href]:hover,
.bootstrap-iso .label-info[href]:focus {
    background-color: #31b0d5;
}
.bootstrap-iso .label-warning {
    background-color: #f0ad4e;
}
.bootstrap-iso .label-warning[href]:hover,
.bootstrap-iso .label-warning[href]:focus {
    background-color: #ec971f;
}
.bootstrap-iso .label-danger {
    background-color: #d9534f;
}
.bootstrap-iso .label-danger[href]:hover,
.bootstrap-iso .label-danger[href]:focus {
    background-color: #c9302c;
}
.bootstrap-iso .badge {
    display: inline-block;
    min-width: 10px;
    padding: 3px 7px;
    font-size: 11px;
    font-weight: bold;
    color: #ffffff;
    line-height: 1;
    vertical-align: baseline;
    white-space: nowrap;
    text-align: center;
    background-color: #777777;
    border-radius: 10px;
}
.bootstrap-iso .badge:empty {
    display: none;
}
.bootstrap-iso .btn .badge {
    position: relative;
    top: -1px;
}
.bootstrap-iso .btn-xs .badge {
    top: 0;
    padding: 1px 5px;
}
.bootstrap-iso a.badge:hover,
.bootstrap-iso a.badge:focus {
    color: #ffffff;
    text-decoration: none;
    cursor: pointer;
}
.bootstrap-iso a.list-group-item.active > .badge,
.bootstrap-iso .nav-pills > .active > a > .badge {
    color: #428bca;
    background-color: #ffffff;
}
.bootstrap-iso .nav-pills > li > a > .badge {
    margin-left: 3px;
}
.bootstrap-iso .jumbotron {
    padding: 30px;
    margin-bottom: 30px;
    color: inherit;
    background-color: #eeeeee;
}
.bootstrap-iso .jumbotron h1,
.bootstrap-iso .jumbotron .h1 {
    color: inherit;
}
.bootstrap-iso .jumbotron p {
    margin-bottom: 15px;
    font-size: 18px;
    font-weight: 200;
}
.bootstrap-iso .jumbotron > hr {
    border-top-color: #d5d5d5;
}
.bootstrap-iso .container .jumbotron {
    border-radius: 6px;
}
.bootstrap-iso .jumbotron .container {
    max-width: 100%;
}
@media screen and (min-width: 768px) {
    .bootstrap-iso .jumbotron {
        padding-top: 48px;
        padding-bottom: 48px;
    }
    .bootstrap-iso .container .jumbotron {
        padding-left: 60px;
        padding-right: 60px;
    }
    .bootstrap-iso .jumbotron h1,
    .bootstrap-iso .jumbotron .h1 {
        font-size: 54px;
    }
}
.bootstrap-iso .thumbnail {
    display: block;
    padding: 4px;
    margin-bottom: 17px;
    line-height: 1.42857143;
    background-color: #ffffff;
    border: 1px solid #dddddd;
    border-radius: 4px;
    -webkit-transition: all 0.2s ease-in-out;
    -o-transition: all 0.2s ease-in-out;
    transition: all 0.2s ease-in-out;
}
.bootstrap-iso .thumbnail > img,
.bootstrap-iso .thumbnail a > img {
    margin-left: auto;
    margin-right: auto;
}
.bootstrap-iso a.thumbnail:hover,
.bootstrap-iso a.thumbnail:focus,
.bootstrap-iso a.thumbnail.active {
    border-color: #428bca;
}
.bootstrap-iso .thumbnail .caption {
    padding: 9px;
    color: #333333;
}
.bootstrap-iso .alert {
    padding: 15px;
    margin-bottom: 17px;
    border: 1px solid transparent;
    border-radius: 4px;
}
.bootstrap-iso .alert h4 {
    margin-top: 0;
    color: inherit;
}
.bootstrap-iso .alert .alert-link {
    font-weight: bold;
}
.bootstrap-iso .alert > p,
.bootstrap-iso .alert > ul {
    margin-bottom: 0;
}
.bootstrap-iso .alert > p + p {
    margin-top: 5px;
}
.bootstrap-iso .alert-dismissable,
.bootstrap-iso .alert-dismissible {
    padding-right: 35px;
}
.bootstrap-iso .alert-dismissable .close,
.bootstrap-iso .alert-dismissible .close {
    position: relative;
    top: -2px;
    right: -21px;
    color: inherit;
}
.bootstrap-iso .alert-success {
    background-color: #dff0d8;
    border-color: #d6e9c6;
    color: #3c763d;
}
.bootstrap-iso .alert-success hr {
    border-top-color: #c9e2b3;
}
.bootstrap-iso .alert-success .alert-link {
    color: #2b542c;
}
.bootstrap-iso .alert-info {
    background-color: #d9edf7;
    border-color: #bce8f1;
    color: #31708f;
}
.bootstrap-iso .alert-info hr {
    border-top-color: #a6e1ec;
}
.bootstrap-iso .alert-info .alert-link {
    color: #245269;
}
.bootstrap-iso .alert-warning {
    background-color: #fcf8e3;
    border-color: #faebcc;
    color: #8a6d3b;
}
.bootstrap-iso .alert-warning hr {
    border-top-color: #f7e1b5;
}
.bootstrap-iso .alert-warning .alert-link {
    color: #66512c;
}
.bootstrap-iso .alert-danger {
    background-color: #f2dede;
    border-color: #ebccd1;
    color: #a94442;
}
.bootstrap-iso .alert-danger hr {
    border-top-color: #e4b9c0;
}
.bootstrap-iso .alert-danger .alert-link {
    color: #843534;
}
@-webkit-keyframes progress-bar-stripes {
    from {
        background-position: 40px 0;
    }
    to {
        background-position: 0 0;
    }
}
@keyframes progress-bar-stripes {
    from {
        background-position: 40px 0;
    }
    to {
        background-position: 0 0;
    }
}
.bootstrap-iso .progress {
    overflow: hidden;
    height: 17px;
    margin-bottom: 17px;
    background-color: #f5f5f5;
    border-radius: 4px;
    -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}
.bootstrap-iso .progress-bar {
    float: left;
    width: 0%;
    height: 100%;
    font-size: 11px;
    line-height: 17px;
    color: #ffffff;
    text-align: center;
    background-color: #428bca;
    -webkit-box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
    box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
    -webkit-transition: width 0.6s ease;
    -o-transition: width 0.6s ease;
    transition: width 0.6s ease;
}
.bootstrap-iso .progress-striped .progress-bar,
.bootstrap-iso .progress-bar-striped {
    background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-size: 40px 40px;
}
.bootstrap-iso .progress.active .progress-bar,
.bootstrap-iso .progress-bar.active {
    -webkit-animation: progress-bar-stripes 2s linear infinite;
    -o-animation: progress-bar-stripes 2s linear infinite;
    animation: progress-bar-stripes 2s linear infinite;
}
.bootstrap-iso .progress-bar[aria-valuenow="1"],
.bootstrap-iso .progress-bar[aria-valuenow="2"] {
    min-width: 30px;
}
.bootstrap-iso .progress-bar[aria-valuenow="0"] {
    color: #777777;
    min-width: 30px;
    background-color: transparent;
    background-image: none;
    box-shadow: none;
}
.bootstrap-iso .progress-bar-success {
    background-color: #5cb85c;
}
.bootstrap-iso .progress-striped .progress-bar-success {
    background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}
.bootstrap-iso .progress-bar-info {
    background-color: #5bc0de;
}
.bootstrap-iso .progress-striped .progress-bar-info {
    background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}
.bootstrap-iso .progress-bar-warning {
    background-color: #f0ad4e;
}
.bootstrap-iso .progress-striped .progress-bar-warning {
    background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}
.bootstrap-iso .progress-bar-danger {
    background-color: #d9534f;
}
.bootstrap-iso .progress-striped .progress-bar-danger {
    background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}
.bootstrap-iso .media,
.bootstrap-iso .media-body {
    overflow: hidden;
    zoom: 1;
}
.bootstrap-iso .media,
.bootstrap-iso .media .media {
    margin-top: 15px;
}
.bootstrap-iso .media:first-child {
    margin-top: 0;
}
.bootstrap-iso .media-object {
    display: block;
}
.bootstrap-iso .media-heading {
    margin: 0 0 5px;
}
.bootstrap-iso .media > .pull-left {
    margin-right: 10px;
}
.bootstrap-iso .media > .pull-right {
    margin-left: 10px;
}
.bootstrap-iso .media-list {
    padding-left: 0;
    list-style: none;
}
.bootstrap-iso .list-group {
    margin-bottom: 20px;
    padding-left: 0;
}
.bootstrap-iso .list-group-item {
    position: relative;
    display: block;
    padding: 10px 15px;
    margin-bottom: -1px;
    background-color: #ffffff;
    border: 1px solid #dddddd;
}
.bootstrap-iso .list-group-item:first-child {
    border-top-right-radius: 4px;
    border-top-left-radius: 4px;
}
.bootstrap-iso .list-group-item:last-child {
    margin-bottom: 0;
    border-bottom-right-radius: 4px;
    border-bottom-left-radius: 4px;
}
.bootstrap-iso .list-group-item > .badge {
    float: right;
}
.bootstrap-iso .list-group-item > .badge + .badge {
    margin-right: 5px;
}
.bootstrap-iso a.list-group-item {
    color: #555555;
}
.bootstrap-iso a.list-group-item .list-group-item-heading {
    color: #333333;
}
.bootstrap-iso a.list-group-item:hover,
.bootstrap-iso a.list-group-item:focus {
    text-decoration: none;
    color: #555555;
    background-color: #f5f5f5;
}
.bootstrap-iso .list-group-item.disabled,
.bootstrap-iso .list-group-item.disabled:hover,
.bootstrap-iso .list-group-item.disabled:focus {
    background-color: #eeeeee;
    color: #777777;
}
.bootstrap-iso .list-group-item.disabled .list-group-item-heading,
.bootstrap-iso .list-group-item.disabled:hover .list-group-item-heading,
.bootstrap-iso .list-group-item.disabled:focus .list-group-item-heading {
    color: inherit;
}
.bootstrap-iso .list-group-item.disabled .list-group-item-text,
.bootstrap-iso .list-group-item.disabled:hover .list-group-item-text,
.bootstrap-iso .list-group-item.disabled:focus .list-group-item-text {
    color: #777777;
}
.bootstrap-iso .list-group-item.active,
.bootstrap-iso .list-group-item.active:hover,
.bootstrap-iso .list-group-item.active:focus {
    z-index: 2;
    color: #ffffff;
    background-color: #428bca;
    border-color: #428bca;
}
.bootstrap-iso .list-group-item.active .list-group-item-heading,
.bootstrap-iso .list-group-item.active:hover .list-group-item-heading,
.bootstrap-iso .list-group-item.active:focus .list-group-item-heading,
.bootstrap-iso .list-group-item.active .list-group-item-heading > small,
.bootstrap-iso .list-group-item.active:hover .list-group-item-heading > small,
.bootstrap-iso .list-group-item.active:focus .list-group-item-heading > small,
.bootstrap-iso .list-group-item.active .list-group-item-heading > .small,
.bootstrap-iso .list-group-item.active:hover .list-group-item-heading > .small,
.bootstrap-iso .list-group-item.active:focus .list-group-item-heading > .small {
    color: inherit;
}
.bootstrap-iso .list-group-item.active .list-group-item-text,
.bootstrap-iso .list-group-item.active:hover .list-group-item-text,
.bootstrap-iso .list-group-item.active:focus .list-group-item-text {
    color: #e1edf7;
}
.bootstrap-iso .list-group-item-success {
    color: #3c763d;
    background-color: #dff0d8;
}
.bootstrap-iso a.list-group-item-success {
    color: #3c763d;
}
.bootstrap-iso a.list-group-item-success .list-group-item-heading {
    color: inherit;
}
.bootstrap-iso a.list-group-item-success:hover,
.bootstrap-iso a.list-group-item-success:focus {
    color: #3c763d;
    background-color: #d0e9c6;
}
.bootstrap-iso a.list-group-item-success.active,
.bootstrap-iso a.list-group-item-success.active:hover,
.bootstrap-iso a.list-group-item-success.active:focus {
    color: #fff;
    background-color: #3c763d;
    border-color: #3c763d;
}
.bootstrap-iso .list-group-item-info {
    color: #31708f;
    background-color: #d9edf7;
}
.bootstrap-iso a.list-group-item-info {
    color: #31708f;
}
.bootstrap-iso a.list-group-item-info .list-group-item-heading {
    color: inherit;
}
.bootstrap-iso a.list-group-item-info:hover,
.bootstrap-iso a.list-group-item-info:focus {
    color: #31708f;
    background-color: #c4e3f3;
}
.bootstrap-iso a.list-group-item-info.active,
.bootstrap-iso a.list-group-item-info.active:hover,
.bootstrap-iso a.list-group-item-info.active:focus {
    color: #fff;
    background-color: #31708f;
    border-color: #31708f;
}
.bootstrap-iso .list-group-item-warning {
    color: #8a6d3b;
    background-color: #fcf8e3;
}
.bootstrap-iso a.list-group-item-warning {
    color: #8a6d3b;
}
.bootstrap-iso a.list-group-item-warning .list-group-item-heading {
    color: inherit;
}
.bootstrap-iso a.list-group-item-warning:hover,
.bootstrap-iso a.list-group-item-warning:focus {
    color: #8a6d3b;
    background-color: #faf2cc;
}
.bootstrap-iso a.list-group-item-warning.active,
.bootstrap-iso a.list-group-item-warning.active:hover,
.bootstrap-iso a.list-group-item-warning.active:focus {
    color: #fff;
    background-color: #8a6d3b;
    border-color: #8a6d3b;
}
.bootstrap-iso .list-group-item-danger {
    color: #a94442;
    background-color: #f2dede;
}
.bootstrap-iso a.list-group-item-danger {
    color: #a94442;
}
.bootstrap-iso a.list-group-item-danger .list-group-item-heading {
    color: inherit;
}
.bootstrap-iso a.list-group-item-danger:hover,
.bootstrap-iso a.list-group-item-danger:focus {
    color: #a94442;
    background-color: #ebcccc;
}
.bootstrap-iso a.list-group-item-danger.active,
.bootstrap-iso a.list-group-item-danger.active:hover,
.bootstrap-iso a.list-group-item-danger.active:focus {
    color: #fff;
    background-color: #a94442;
    border-color: #a94442;
}
.bootstrap-iso .list-group-item-heading {
    margin-top: 0;
    margin-bottom: 5px;
}
.bootstrap-iso .list-group-item-text {
    margin-bottom: 0;
    line-height: 1.3;
}
.bootstrap-iso .panel {
    margin-bottom: 17px;
    background-color: #ffffff;
    border: 1px solid transparent;
    border-radius: 4px;
    -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
}
.bootstrap-iso .panel-body {
    padding: 15px;
}
.bootstrap-iso .panel-heading {
    padding: 10px 15px;
    border-bottom: 1px solid transparent;
    border-top-right-radius: 3px;
    border-top-left-radius: 3px;
}
.bootstrap-iso .panel-heading > .dropdown .dropdown-toggle {
    color: inherit;
}
.bootstrap-iso .panel-title {
    margin-top: 0;
    margin-bottom: 0;
    font-size: 14px;
    color: inherit;
}
.bootstrap-iso .panel-title > a {
    color: inherit;
}
.bootstrap-iso .panel-footer {
    padding: 10px 15px;
    background-color: #f5f5f5;
    border-top: 1px solid #dddddd;
    border-bottom-right-radius: 3px;
    border-bottom-left-radius: 3px;
}
.bootstrap-iso .panel > .list-group {
    margin-bottom: 0;
}
.bootstrap-iso .panel > .list-group .list-group-item {
    border-width: 1px 0;
    border-radius: 0;
}
.bootstrap-iso .panel > .list-group:first-child .list-group-item:first-child {
    border-top: 0;
    border-top-right-radius: 3px;
    border-top-left-radius: 3px;
}
.bootstrap-iso .panel > .list-group:last-child .list-group-item:last-child {
    border-bottom: 0;
    border-bottom-right-radius: 3px;
    border-bottom-left-radius: 3px;
}
.bootstrap-iso .panel-heading + .list-group .list-group-item:first-child {
    border-top-width: 0;
}
.bootstrap-iso .list-group + .panel-footer {
    border-top-width: 0;
}
.bootstrap-iso .panel > .table,
.bootstrap-iso .panel > .table-responsive > .table,
.bootstrap-iso .panel > .panel-collapse > .table {
    margin-bottom: 0;
}
.bootstrap-iso .panel > .table:first-child,
.bootstrap-iso .panel > .table-responsive:first-child > .table:first-child {
    border-top-right-radius: 3px;
    border-top-left-radius: 3px;
}
.bootstrap-iso .panel > .table:first-child > thead:first-child > tr:first-child td:first-child,
.bootstrap-iso .panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child td:first-child,
.bootstrap-iso .panel > .table:first-child > tbody:first-child > tr:first-child td:first-child,
.bootstrap-iso .panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child td:first-child,
.bootstrap-iso .panel > .table:first-child > thead:first-child > tr:first-child th:first-child,
.bootstrap-iso .panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child th:first-child,
.bootstrap-iso .panel > .table:first-child > tbody:first-child > tr:first-child th:first-child,
.bootstrap-iso .panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child th:first-child {
    border-top-left-radius: 3px;
}
.bootstrap-iso .panel > .table:first-child > thead:first-child > tr:first-child td:last-child,
.bootstrap-iso .panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child td:last-child,
.bootstrap-iso .panel > .table:first-child > tbody:first-child > tr:first-child td:last-child,
.bootstrap-iso .panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child td:last-child,
.bootstrap-iso .panel > .table:first-child > thead:first-child > tr:first-child th:last-child,
.bootstrap-iso .panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child th:last-child,
.bootstrap-iso .panel > .table:first-child > tbody:first-child > tr:first-child th:last-child,
.bootstrap-iso .panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child th:last-child {
    border-top-right-radius: 3px;
}
.bootstrap-iso .panel > .table:last-child,
.bootstrap-iso .panel > .table-responsive:last-child > .table:last-child {
    border-bottom-right-radius: 3px;
    border-bottom-left-radius: 3px;
}
.bootstrap-iso .panel > .table:last-child > tbody:last-child > tr:last-child td:first-child,
.bootstrap-iso .panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child td:first-child,
.bootstrap-iso .panel > .table:last-child > tfoot:last-child > tr:last-child td:first-child,
.bootstrap-iso .panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child td:first-child,
.bootstrap-iso .panel > .table:last-child > tbody:last-child > tr:last-child th:first-child,
.bootstrap-iso .panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child th:first-child,
.bootstrap-iso .panel > .table:last-child > tfoot:last-child > tr:last-child th:first-child,
.bootstrap-iso .panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child th:first-child {
    border-bottom-left-radius: 3px;
}
.bootstrap-iso .panel > .table:last-child > tbody:last-child > tr:last-child td:last-child,
.bootstrap-iso .panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child td:last-child,
.bootstrap-iso .panel > .table:last-child > tfoot:last-child > tr:last-child td:last-child,
.bootstrap-iso .panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child td:last-child,
.bootstrap-iso .panel > .table:last-child > tbody:last-child > tr:last-child th:last-child,
.bootstrap-iso .panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child th:last-child,
.bootstrap-iso .panel > .table:last-child > tfoot:last-child > tr:last-child th:last-child,
.bootstrap-iso .panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child th:last-child {
    border-bottom-right-radius: 3px;
}
.bootstrap-iso .panel > .panel-body + .table,
.bootstrap-iso .panel > .panel-body + .table-responsive {
    border-top: 1px solid #dddddd;
}
.bootstrap-iso .panel > .table > tbody:first-child > tr:first-child th,
.bootstrap-iso .panel > .table > tbody:first-child > tr:first-child td {
    border-top: 0;
}
.bootstrap-iso .panel > .table-bordered,
.bootstrap-iso .panel > .table-responsive > .table-bordered {
    border: 0;
}
.bootstrap-iso .panel > .table-bordered > thead > tr > th:first-child,
.bootstrap-iso .panel > .table-responsive > .table-bordered > thead > tr > th:first-child,
.bootstrap-iso .panel > .table-bordered > tbody > tr > th:first-child,
.bootstrap-iso .panel > .table-responsive > .table-bordered > tbody > tr > th:first-child,
.bootstrap-iso .panel > .table-bordered > tfoot > tr > th:first-child,
.bootstrap-iso .panel > .table-responsive > .table-bordered > tfoot > tr > th:first-child,
.bootstrap-iso .panel > .table-bordered > thead > tr > td:first-child,
.bootstrap-iso .panel > .table-responsive > .table-bordered > thead > tr > td:first-child,
.bootstrap-iso .panel > .table-bordered > tbody > tr > td:first-child,
.bootstrap-iso .panel > .table-responsive > .table-bordered > tbody > tr > td:first-child,
.bootstrap-iso .panel > .table-bordered > tfoot > tr > td:first-child,
.bootstrap-iso .panel > .table-responsive > .table-bordered > tfoot > tr > td:first-child {
    border-left: 0;
}
.bootstrap-iso .panel > .table-bordered > thead > tr > th:last-child,
.bootstrap-iso .panel > .table-responsive > .table-bordered > thead > tr > th:last-child,
.bootstrap-iso .panel > .table-bordered > tbody > tr > th:last-child,
.bootstrap-iso .panel > .table-responsive > .table-bordered > tbody > tr > th:last-child,
.bootstrap-iso .panel > .table-bordered > tfoot > tr > th:last-child,
.bootstrap-iso .panel > .table-responsive > .table-bordered > tfoot > tr > th:last-child,
.bootstrap-iso .panel > .table-bordered > thead > tr > td:last-child,
.bootstrap-iso .panel > .table-responsive > .table-bordered > thead > tr > td:last-child,
.bootstrap-iso .panel > .table-bordered > tbody > tr > td:last-child,
.bootstrap-iso .panel > .table-responsive > .table-bordered > tbody > tr > td:last-child,
.bootstrap-iso .panel > .table-bordered > tfoot > tr > td:last-child,
.bootstrap-iso .panel > .table-responsive > .table-bordered > tfoot > tr > td:last-child {
    border-right: 0;
}
.bootstrap-iso .panel > .table-bordered > thead > tr:first-child > td,
.bootstrap-iso .panel > .table-responsive > .table-bordered > thead > tr:first-child > td,
.bootstrap-iso .panel > .table-bordered > tbody > tr:first-child > td,
.bootstrap-iso .panel > .table-responsive > .table-bordered > tbody > tr:first-child > td,
.bootstrap-iso .panel > .table-bordered > thead > tr:first-child > th,
.bootstrap-iso .panel > .table-responsive > .table-bordered > thead > tr:first-child > th,
.bootstrap-iso .panel > .table-bordered > tbody > tr:first-child > th,
.bootstrap-iso .panel > .table-responsive > .table-bordered > tbody > tr:first-child > th {
    border-bottom: 0;
}
.bootstrap-iso .panel > .table-bordered > tbody > tr:last-child > td,
.bootstrap-iso .panel > .table-responsive > .table-bordered > tbody > tr:last-child > td,
.bootstrap-iso .panel > .table-bordered > tfoot > tr:last-child > td,
.bootstrap-iso .panel > .table-responsive > .table-bordered > tfoot > tr:last-child > td,
.bootstrap-iso .panel > .table-bordered > tbody > tr:last-child > th,
.bootstrap-iso .panel > .table-responsive > .table-bordered > tbody > tr:last-child > th,
.bootstrap-iso .panel > .table-bordered > tfoot > tr:last-child > th,
.bootstrap-iso .panel > .table-responsive > .table-bordered > tfoot > tr:last-child > th {
    border-bottom: 0;
}
.bootstrap-iso .panel > .table-responsive {
    border: 0;
    margin-bottom: 0;
}
.bootstrap-iso .panel-group {
    margin-bottom: 17px;
}
.bootstrap-iso .panel-group .panel {
    margin-bottom: 0;
    border-radius: 4px;
}
.bootstrap-iso .panel-group .panel + .panel {
    margin-top: 5px;
}
.bootstrap-iso .panel-group .panel-heading {
    border-bottom: 0;
}
.bootstrap-iso .panel-group .panel-heading + .panel-collapse > .panel-body {
    border-top: 1px solid #dddddd;
}
.bootstrap-iso .panel-group .panel-footer {
    border-top: 0;
}
.bootstrap-iso .panel-group .panel-footer + .panel-collapse .panel-body {
    border-bottom: 1px solid #dddddd;
}
.bootstrap-iso .panel-default {
    border-color: #dddddd;
}
.bootstrap-iso .panel-default > .panel-heading {
    color: #333333;
    background-color: #f5f5f5;
    border-color: #dddddd;
}
.bootstrap-iso .panel-default > .panel-heading + .panel-collapse > .panel-body {
    border-top-color: #dddddd;
}
.bootstrap-iso .panel-default > .panel-heading .badge {
    color: #f5f5f5;
    background-color: #333333;
}
.bootstrap-iso .panel-default > .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #dddddd;
}
.bootstrap-iso .panel-primary {
    border-color: #428bca;
}
.bootstrap-iso .panel-primary > .panel-heading {
    color: #ffffff;
    background-color: #428bca;
    border-color: #428bca;
}
.bootstrap-iso .panel-primary > .panel-heading + .panel-collapse > .panel-body {
    border-top-color: #428bca;
}
.bootstrap-iso .panel-primary > .panel-heading .badge {
    color: #428bca;
    background-color: #ffffff;
}
.bootstrap-iso .panel-primary > .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #428bca;
}
.bootstrap-iso .panel-success {
    border-color: #d6e9c6;
}
.bootstrap-iso .panel-success > .panel-heading {
    color: #3c763d;
    background-color: #dff0d8;
    border-color: #d6e9c6;
}
.bootstrap-iso .panel-success > .panel-heading + .panel-collapse > .panel-body {
    border-top-color: #d6e9c6;
}
.bootstrap-iso .panel-success > .panel-heading .badge {
    color: #dff0d8;
    background-color: #3c763d;
}
.bootstrap-iso .panel-success > .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #d6e9c6;
}
.bootstrap-iso .panel-info {
    border-color: #bce8f1;
}
.bootstrap-iso .panel-info > .panel-heading {
    color: #31708f;
    background-color: #d9edf7;
    border-color: #bce8f1;
}
.bootstrap-iso .panel-info > .panel-heading + .panel-collapse > .panel-body {
    border-top-color: #bce8f1;
}
.bootstrap-iso .panel-info > .panel-heading .badge {
    color: #d9edf7;
    background-color: #31708f;
}
.bootstrap-iso .panel-info > .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #bce8f1;
}
.bootstrap-iso .panel-warning {
    border-color: #faebcc;
}
.bootstrap-iso .panel-warning > .panel-heading {
    color: #8a6d3b;
    background-color: #fcf8e3;
    border-color: #faebcc;
}
.bootstrap-iso .panel-warning > .panel-heading + .panel-collapse > .panel-body {
    border-top-color: #faebcc;
}
.bootstrap-iso .panel-warning > .panel-heading .badge {
    color: #fcf8e3;
    background-color: #8a6d3b;
}
.bootstrap-iso .panel-warning > .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #faebcc;
}
.bootstrap-iso .panel-danger {
    border-color: #ebccd1;
}
.bootstrap-iso .panel-danger > .panel-heading {
    color: #a94442;
    background-color: #f2dede;
    border-color: #ebccd1;
}
.bootstrap-iso .panel-danger > .panel-heading + .panel-collapse > .panel-body {
    border-top-color: #ebccd1;
}
.bootstrap-iso .panel-danger > .panel-heading .badge {
    color: #f2dede;
    background-color: #a94442;
}
.bootstrap-iso .panel-danger > .panel-footer + .panel-collapse > .panel-body {
    border-bottom-color: #ebccd1;
}
.bootstrap-iso .embed-responsive {
    position: relative;
    display: block;
    height: 0;
    padding: 0;
    overflow: hidden;
}
.bootstrap-iso .embed-responsive .embed-responsive-item,
.bootstrap-iso .embed-responsive iframe,
.bootstrap-iso .embed-responsive embed,
.bootstrap-iso .embed-responsive object {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    height: 100%;
    width: 100%;
    border: 0;
}
.bootstrap-iso .embed-responsive.embed-responsive-16by9 {
    padding-bottom: 56.25%;
}
.bootstrap-iso .embed-responsive.embed-responsive-4by3 {
    padding-bottom: 75%;
}
.bootstrap-iso .well {
    min-height: 20px;
    padding: 19px;
    margin-bottom: 20px;
    background-color: #f5f5f5;
    border: 1px solid #e3e3e3;
    border-radius: 4px;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
}
.bootstrap-iso .well blockquote {
    border-color: #ddd;
    border-color: rgba(0, 0, 0, 0.15);
}
.bootstrap-iso .well-lg {
    padding: 24px;
    border-radius: 6px;
}
.bootstrap-iso .well-sm {
    padding: 9px;
    border-radius: 3px;
}
.bootstrap-iso .close {
    float: right;
    font-size: 18px;
    font-weight: bold;
    line-height: 1;
    color: #000000;
    text-shadow: 0 1px 0 #ffffff;
    opacity: 0.2;
    filter: alpha(opacity=20);
}
.bootstrap-iso .close:hover,
.bootstrap-iso .close:focus {
    color: #000000;
    text-decoration: none;
    cursor: pointer;
    opacity: 0.5;
    filter: alpha(opacity=50);
}
.bootstrap-iso button.close {
    padding: 0;
    cursor: pointer;
    background: transparent;
    border: 0;
    -webkit-appearance: none;
}
.modal-open {
    overflow: hidden;
}
.bootstrap-iso .modal {
    display: none;
    overflow: hidden;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1050;
    -webkit-overflow-scrolling: touch;
    outline: 0;
}
.bootstrap-iso .modal.fade .modal-dialog {
    -webkit-transform: translate3d(0, -25%, 0);
    transform: translate3d(0, -25%, 0);
    -webkit-transition: -webkit-transform 0.3s ease-out;
    -moz-transition: -moz-transform 0.3s ease-out;
    -o-transition: -o-transform 0.3s ease-out;
    transition: transform 0.3s ease-out;
}
.bootstrap-iso .modal.in .modal-dialog {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
}
.modal-open .modal {
    overflow-x: hidden;
    overflow-y: auto;
}
.bootstrap-iso .modal-dialog {
    position: relative;
    width: auto;
    margin: 10px;
}
.bootstrap-iso .modal-content {
    position: relative;
    background-color: #ffffff;
    border: 1px solid #999999;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    -webkit-box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
    box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
    background-clip: padding-box;
    outline: 0;
}
.bootstrap-iso .modal-backdrop {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1040;
    background-color: #000000;
}
.bootstrap-iso .modal-backdrop.fade {
    opacity: 0;
    filter: alpha(opacity=0);
}
.bootstrap-iso .modal-backdrop.in {
    opacity: 0.5;
    filter: alpha(opacity=50);
}
.bootstrap-iso .modal-header {
    padding: 15px;
    border-bottom: 1px solid #e5e5e5;
    min-height: 16.42857143px;
}
.bootstrap-iso .modal-header .close {
    margin-top: -2px;
}
.bootstrap-iso .modal-title {
    margin: 0;
    line-height: 1.42857143;
}
.bootstrap-iso .modal-body {
    position: relative;
    padding: 15px;
}
.bootstrap-iso .modal-footer {
    padding: 15px;
    text-align: right;
    border-top: 1px solid #e5e5e5;
}
.bootstrap-iso .modal-footer .btn + .btn {
    margin-left: 5px;
    margin-bottom: 0;
}
.bootstrap-iso .modal-footer .btn-group .btn + .btn {
    margin-left: -1px;
}
.bootstrap-iso .modal-footer .btn-block + .btn-block {
    margin-left: 0;
}
.bootstrap-iso .modal-scrollbar-measure {
    position: absolute;
    top: -9999px;
    width: 50px;
    height: 50px;
    overflow: scroll;
}
@media (min-width: 768px) {
    .bootstrap-iso .modal-dialog {
        width: 600px;
        margin: 30px auto;
    }
    .bootstrap-iso .modal-content {
        -webkit-box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
    }
    .bootstrap-iso .modal-sm {
        width: 300px;
    }
}
@media (min-width: 992px) {
    .bootstrap-iso .modal-lg {
        width: 900px;
    }
}
.bootstrap-iso .tooltip {
    position: absolute;
    z-index: 1070;
    display: block;
    visibility: visible;
    font-size: 11px;
    line-height: 1.4;
    opacity: 0;
    filter: alpha(opacity=0);
}
.bootstrap-iso .tooltip.in {
    opacity: 0.9;
    filter: alpha(opacity=90);
}
.bootstrap-iso .tooltip.top {
    margin-top: -3px;
    padding: 5px 0;
}
.bootstrap-iso .tooltip.right {
    margin-left: 3px;
    padding: 0 5px;
}
.bootstrap-iso .tooltip.bottom {
    margin-top: 3px;
    padding: 5px 0;
}
.bootstrap-iso .tooltip.left {
    margin-left: -3px;
    padding: 0 5px;
}
.bootstrap-iso .tooltip-inner {
    max-width: 200px;
    padding: 3px 8px;
    color: #ffffff;
    text-align: center;
    text-decoration: none;
    background-color: #000000;
    border-radius: 4px;
}
.bootstrap-iso .tooltip-arrow {
    position: absolute;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
}
.bootstrap-iso .tooltip.top .tooltip-arrow {
    bottom: 0;
    left: 50%;
    margin-left: -5px;
    border-width: 5px 5px 0;
    border-top-color: #000000;
}
.bootstrap-iso .tooltip.top-left .tooltip-arrow {
    bottom: 0;
    left: 5px;
    border-width: 5px 5px 0;
    border-top-color: #000000;
}
.bootstrap-iso .tooltip.top-right .tooltip-arrow {
    bottom: 0;
    right: 5px;
    border-width: 5px 5px 0;
    border-top-color: #000000;
}
.bootstrap-iso .tooltip.right .tooltip-arrow {
    top: 50%;
    left: 0;
    margin-top: -5px;
    border-width: 5px 5px 5px 0;
    border-right-color: #000000;
}
.bootstrap-iso .tooltip.left .tooltip-arrow {
    top: 50%;
    right: 0;
    margin-top: -5px;
    border-width: 5px 0 5px 5px;
    border-left-color: #000000;
}
.bootstrap-iso .tooltip.bottom .tooltip-arrow {
    top: 0;
    left: 50%;
    margin-left: -5px;
    border-width: 0 5px 5px;
    border-bottom-color: #000000;
}
.bootstrap-iso .tooltip.bottom-left .tooltip-arrow {
    top: 0;
    left: 5px;
    border-width: 0 5px 5px;
    border-bottom-color: #000000;
}
.bootstrap-iso .tooltip.bottom-right .tooltip-arrow {
    top: 0;
    right: 5px;
    border-width: 0 5px 5px;
    border-bottom-color: #000000;
}
.bootstrap-iso .popover {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1060;
    display: none;
    max-width: 276px;
    padding: 1px;
    text-align: left;
    background-color: #ffffff;
    background-clip: padding-box;
    border: 1px solid #cccccc;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
    white-space: normal;
}
.bootstrap-iso .popover.top {
    margin-top: -10px;
}
.bootstrap-iso .popover.right {
    margin-left: 10px;
}
.bootstrap-iso .popover.bottom {
    margin-top: 10px;
}
.bootstrap-iso .popover.left {
    margin-left: -10px;
}
.bootstrap-iso .popover-title {
    margin: 0;
    padding: 8px 14px;
    font-size: 12px;
    font-weight: normal;
    line-height: 18px;
    background-color: #f7f7f7;
    border-bottom: 1px solid #ebebeb;
    border-radius: 5px 5px 0 0;
}
.bootstrap-iso .popover-content {
    padding: 9px 14px;
}
.bootstrap-iso .popover > .arrow,
.bootstrap-iso .popover > .arrow:after {
    position: absolute;
    display: block;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
}
.bootstrap-iso .popover > .arrow {
    border-width: 11px;
}
.bootstrap-iso .popover > .arrow:after {
    border-width: 10px;
    content: "";
}
.bootstrap-iso .popover.top > .arrow {
    left: 50%;
    margin-left: -11px;
    border-bottom-width: 0;
    border-top-color: #999999;
    border-top-color: rgba(0, 0, 0, 0.25);
    bottom: -11px;
}
.bootstrap-iso .popover.top > .arrow:after {
    content: " ";
    bottom: 1px;
    margin-left: -10px;
    border-bottom-width: 0;
    border-top-color: #ffffff;
}
.bootstrap-iso .popover.right > .arrow {
    top: 50%;
    left: -11px;
    margin-top: -11px;
    border-left-width: 0;
    border-right-color: #999999;
    border-right-color: rgba(0, 0, 0, 0.25);
}
.bootstrap-iso .popover.right > .arrow:after {
    content: " ";
    left: 1px;
    bottom: -10px;
    border-left-width: 0;
    border-right-color: #ffffff;
}
.bootstrap-iso .popover.bottom > .arrow {
    left: 50%;
    margin-left: -11px;
    border-top-width: 0;
    border-bottom-color: #999999;
    border-bottom-color: rgba(0, 0, 0, 0.25);
    top: -11px;
}
.bootstrap-iso .popover.bottom > .arrow:after {
    content: " ";
    top: 1px;
    margin-left: -10px;
    border-top-width: 0;
    border-bottom-color: #ffffff;
}
.bootstrap-iso .popover.left > .arrow {
    top: 50%;
    right: -11px;
    margin-top: -11px;
    border-right-width: 0;
    border-left-color: #999999;
    border-left-color: rgba(0, 0, 0, 0.25);
}
.bootstrap-iso .popover.left > .arrow:after {
    content: " ";
    right: 1px;
    border-right-width: 0;
    border-left-color: #ffffff;
    bottom: -10px;
}
.bootstrap-iso .carousel {
    position: relative;
}
.bootstrap-iso .carousel-inner {
    position: relative;
    overflow: hidden;
    width: 100%;
}
.bootstrap-iso .carousel-inner > .item {
    display: none;
    position: relative;
    -webkit-transition: 0.6s ease-in-out left;
    -o-transition: 0.6s ease-in-out left;
    transition: 0.6s ease-in-out left;
}
.bootstrap-iso .carousel-inner > .item > img,
.bootstrap-iso .carousel-inner > .item > a > img {
    line-height: 1;
}
.bootstrap-iso .carousel-inner > .active,
.bootstrap-iso .carousel-inner > .next,
.bootstrap-iso .carousel-inner > .prev {
    display: block;
}
.bootstrap-iso .carousel-inner > .active {
    left: 0;
}
.bootstrap-iso .carousel-inner > .next,
.bootstrap-iso .carousel-inner > .prev {
    position: absolute;
    top: 0;
    width: 100%;
}
.bootstrap-iso .carousel-inner > .next {
    left: 100%;
}
.bootstrap-iso .carousel-inner > .prev {
    left: -100%;
}
.bootstrap-iso .carousel-inner > .next.left,
.bootstrap-iso .carousel-inner > .prev.right {
    left: 0;
}
.bootstrap-iso .carousel-inner > .active.left {
    left: -100%;
}
.bootstrap-iso .carousel-inner > .active.right {
    left: 100%;
}
.bootstrap-iso .carousel-control {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 15%;
    opacity: 0.5;
    filter: alpha(opacity=50);
    font-size: 20px;
    color: #ffffff;
    text-align: center;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
}
.bootstrap-iso .carousel-control.left {
    background-image: -webkit-linear-gradient(left, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.0001) 100%);
    background-image: -o-linear-gradient(left, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.0001) 100%);
    background-image: linear-gradient(to right, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.0001) 100%);
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#80000000', endColorstr='#00000000', GradientType=1);
}
.bootstrap-iso .carousel-control.right {
    left: auto;
    right: 0;
    background-image: -webkit-linear-gradient(left, rgba(0, 0, 0, 0.0001) 0%, rgba(0, 0, 0, 0.5) 100%);
    background-image: -o-linear-gradient(left, rgba(0, 0, 0, 0.0001) 0%, rgba(0, 0, 0, 0.5) 100%);
    background-image: linear-gradient(to right, rgba(0, 0, 0, 0.0001) 0%, rgba(0, 0, 0, 0.5) 100%);
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#00000000', endColorstr='#80000000', GradientType=1);
}
.bootstrap-iso .carousel-control:hover,
.bootstrap-iso .carousel-control:focus {
    outline: 0;
    color: #ffffff;
    text-decoration: none;
    opacity: 0.9;
    filter: alpha(opacity=90);
}
.bootstrap-iso .carousel-control .icon-prev,
.bootstrap-iso .carousel-control .icon-next,
.bootstrap-iso .carousel-control .glyphicon-chevron-left,
.bootstrap-iso .carousel-control .glyphicon-chevron-right {
    position: absolute;
    top: 50%;
    z-index: 5;
    display: inline-block;
}
.bootstrap-iso .carousel-control .icon-prev,
.bootstrap-iso .carousel-control .glyphicon-chevron-left {
    left: 50%;
    margin-left: -10px;
}
.bootstrap-iso .carousel-control .icon-next,
.bootstrap-iso .carousel-control .glyphicon-chevron-right {
    right: 50%;
    margin-right: -10px;
}
.bootstrap-iso .carousel-control .icon-prev,
.bootstrap-iso .carousel-control .icon-next {
    width: 20px;
    height: 20px;
    margin-top: -10px;
    font-family: serif;
}
.bootstrap-iso .carousel-control .icon-prev:before {
    content: '\2039';
}
.bootstrap-iso .carousel-control .icon-next:before {
    content: '\203a';
}
.bootstrap-iso .carousel-indicators {
    position: absolute;
    bottom: 10px;
    left: 50%;
    z-index: 15;
    width: 60%;
    margin-left: -30%;
    padding-left: 0;
    list-style: none;
    text-align: center;
}
.bootstrap-iso .carousel-indicators li {
    display: inline-block;
    width: 10px;
    height: 10px;
    margin: 1px;
    text-indent: -999px;
    border: 1px solid #ffffff;
    border-radius: 10px;
    cursor: pointer;
    background-color: #000 \9;
    background-color: rgba(0, 0, 0, 0);
}
.bootstrap-iso .carousel-indicators .active {
    margin: 0;
    width: 12px;
    height: 12px;
    background-color: #ffffff;
}
.bootstrap-iso .carousel-caption {
    position: absolute;
    left: 15%;
    right: 15%;
    bottom: 20px;
    z-index: 10;
    padding-top: 20px;
    padding-bottom: 20px;
    color: #ffffff;
    text-align: center;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
}
.bootstrap-iso .carousel-caption .btn {
    text-shadow: none;
}
@media screen and (min-width: 768px) {
    .bootstrap-iso .carousel-control .glyphicon-chevron-left,
    .bootstrap-iso .carousel-control .glyphicon-chevron-right,
    .bootstrap-iso .carousel-control .icon-prev,
    .bootstrap-iso .carousel-control .icon-next {
        width: 30px;
        height: 30px;
        margin-top: -15px;
        font-size: 30px;
    }
    .bootstrap-iso .carousel-control .glyphicon-chevron-left,
    .bootstrap-iso .carousel-control .icon-prev {
        margin-left: -15px;
    }
    .bootstrap-iso .carousel-control .glyphicon-chevron-right,
    .bootstrap-iso .carousel-control .icon-next {
        margin-right: -15px;
    }
    .bootstrap-iso .carousel-caption {
        left: 20%;
        right: 20%;
        padding-bottom: 30px;
    }
    .bootstrap-iso .carousel-indicators {
        bottom: 20px;
    }
}
.bootstrap-iso .clearfix:before,
.bootstrap-iso .clearfix:after,
.bootstrap-iso .dl-horizontal dd:before,
.bootstrap-iso .dl-horizontal dd:after,
.bootstrap-iso .container:before,
.bootstrap-iso .container:after,
.bootstrap-iso .container-fluid:before,
.bootstrap-iso .container-fluid:after,
.bootstrap-iso .row:before,
.bootstrap-iso .row:after,
.bootstrap-iso .form-horizontal .form-group:before,
.bootstrap-iso .form-horizontal .form-group:after,
.bootstrap-iso .btn-toolbar:before,
.bootstrap-iso .btn-toolbar:after,
.bootstrap-iso .btn-group-vertical > .btn-group:before,
.bootstrap-iso .btn-group-vertical > .btn-group:after,
.bootstrap-iso .nav:before,
.bootstrap-iso .nav:after,
.bootstrap-iso .navbar:before,
.bootstrap-iso .navbar:after,
.bootstrap-iso .navbar-header:before,
.bootstrap-iso .navbar-header:after,
.bootstrap-iso .navbar-collapse:before,
.bootstrap-iso .navbar-collapse:after,
.bootstrap-iso .pager:before,
.bootstrap-iso .pager:after,
.bootstrap-iso .panel-body:before,
.bootstrap-iso .panel-body:after,
.bootstrap-iso .modal-footer:before,
.bootstrap-iso .modal-footer:after {
    content: " ";
    display: table;
}
.bootstrap-iso .clearfix:after,
.bootstrap-iso .dl-horizontal dd:after,
.bootstrap-iso .container:after,
.bootstrap-iso .container-fluid:after,
.bootstrap-iso .row:after,
.bootstrap-iso .form-horizontal .form-group:after,
.bootstrap-iso .btn-toolbar:after,
.bootstrap-iso .btn-group-vertical > .btn-group:after,
.bootstrap-iso .nav:after,
.bootstrap-iso .navbar:after,
.bootstrap-iso .navbar-header:after,
.bootstrap-iso .navbar-collapse:after,
.bootstrap-iso .pager:after,
.bootstrap-iso .panel-body:after,
.bootstrap-iso .modal-footer:after {
    clear: both;
}
.bootstrap-iso .center-block {
    display: block;
    margin-left: auto;
    margin-right: auto;
}
.bootstrap-iso .pull-right {
    float: right !important;
}
.bootstrap-iso .pull-left {
    float: left !important;
}
.bootstrap-iso .hide {
    display: none !important;
}
.bootstrap-iso .show {
    display: block !important;
}
.bootstrap-iso .invisible {
    visibility: hidden;
}
.bootstrap-iso .text-hide {
    font: 0/0 a;
    color: transparent;
    text-shadow: none;
    background-color: transparent;
    border: 0;
}
.bootstrap-iso .hidden {
    display: none !important;
    visibility: hidden !important;
}
.bootstrap-iso .affix {
    position: fixed;
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
}
@-ms-viewport {
    width: device-width;
}
.bootstrap-iso .visible-xs,
.bootstrap-iso .visible-sm,
.bootstrap-iso .visible-md,
.bootstrap-iso .visible-lg {
    display: none !important;
}
.bootstrap-iso .visible-xs-block,
.bootstrap-iso .visible-xs-inline,
.bootstrap-iso .visible-xs-inline-block,
.bootstrap-iso .visible-sm-block,
.bootstrap-iso .visible-sm-inline,
.bootstrap-iso .visible-sm-inline-block,
.bootstrap-iso .visible-md-block,
.bootstrap-iso .visible-md-inline,
.bootstrap-iso .visible-md-inline-block,
.bootstrap-iso .visible-lg-block,
.bootstrap-iso .visible-lg-inline,
.bootstrap-iso .visible-lg-inline-block {
    display: none !important;
}
@media (max-width: 768px) {
    .bootstrap-iso .visible-xs {
        display: block !important;
    }
    .bootstrap-iso table.visible-xs {
        display: table;
    }
    .bootstrap-iso tr.visible-xs {
        display: table-row !important;
    }
    .bootstrap-iso th.visible-xs,
    .bootstrap-iso td.visible-xs {
        display: table-cell !important;
    }
}
@media (max-width: 768px) {
    .bootstrap-iso .visible-xs-block {
        display: block !important;
    }
}
@media (max-width: 768px) {
    .bootstrap-iso .visible-xs-inline {
        display: inline !important;
    }
}
@media (max-width: 768px) {
    .bootstrap-iso .visible-xs-inline-block {
        display: inline-block !important;
    }
}
@media (min-width: 768px) and (max-width: 991px) {
    .bootstrap-iso .visible-sm {
        display: block !important;
    }
    .bootstrap-iso table.visible-sm {
        display: table;
    }
    .bootstrap-iso tr.visible-sm {
        display: table-row !important;
    }
    .bootstrap-iso th.visible-sm,
    .bootstrap-iso td.visible-sm {
        display: table-cell !important;
    }
}
@media (min-width: 768px) and (max-width: 991px) {
    .bootstrap-iso .visible-sm-block {
        display: block !important;
    }
}
@media (min-width: 768px) and (max-width: 991px) {
    .bootstrap-iso .visible-sm-inline {
        display: inline !important;
    }
}
@media (min-width: 768px) and (max-width: 991px) {
    .bootstrap-iso .visible-sm-inline-block {
        display: inline-block !important;
    }
}
@media (min-width: 992px) and (max-width: 1199px) {
    .bootstrap-iso .visible-md {
        display: block !important;
    }
    .bootstrap-iso table.visible-md {
        display: table;
    }
    .bootstrap-iso tr.visible-md {
        display: table-row !important;
    }
    .bootstrap-iso th.visible-md,
    .bootstrap-iso td.visible-md {
        display: table-cell !important;
    }
}
@media (min-width: 992px) and (max-width: 1199px) {
    .bootstrap-iso .visible-md-block {
        display: block !important;
    }
}
@media (min-width: 992px) and (max-width: 1199px) {
    .bootstrap-iso .visible-md-inline {
        display: inline !important;
    }
}
@media (min-width: 992px) and (max-width: 1199px) {
    .bootstrap-iso .visible-md-inline-block {
        display: inline-block !important;
    }
}
@media (min-width: 1200px) {
    .bootstrap-iso .visible-lg {
        display: block !important;
    }
    .bootstrap-iso table.visible-lg {
        display: table;
    }
    .bootstrap-iso tr.visible-lg {
        display: table-row !important;
    }
    .bootstrap-iso th.visible-lg,
    .bootstrap-iso td.visible-lg {
        display: table-cell !important;
    }
}
@media (min-width: 1200px) {
    .bootstrap-iso .visible-lg-block {
        display: block !important;
    }
}
@media (min-width: 1200px) {
    .bootstrap-iso .visible-lg-inline {
        display: inline !important;
    }
}
@media (min-width: 1200px) {
    .bootstrap-iso .visible-lg-inline-block {
        display: inline-block !important;
    }
}
@media (max-width: 768px) {
    .bootstrap-iso .hidden-xs {
        display: none !important;
    }
}
@media (min-width: 768px) and (max-width: 991px) {
    .bootstrap-iso .hidden-sm {
        display: none !important;
    }
}
@media (min-width: 992px) and (max-width: 1199px) {
    .bootstrap-iso .hidden-md {
        display: none !important;
    }
}
@media (min-width: 1200px) {
    .bootstrap-iso .hidden-lg {
        display: none !important;
    }
}
.bootstrap-iso .visible-print {
    display: none !important;
}
@media print {
    .bootstrap-iso .visible-print {
        display: block !important;
    }
    .bootstrap-iso table.visible-print {
        display: table;
    }
    .bootstrap-iso tr.visible-print {
        display: table-row !important;
    }
    .bootstrap-iso th.visible-print,
    .bootstrap-iso td.visible-print {
        display: table-cell !important;
    }
}
.bootstrap-iso .visible-print-block {
    display: none !important;
}
@media print {
    .bootstrap-iso .visible-print-block {
        display: block !important;
    }
}
.bootstrap-iso .visible-print-inline {
    display: none !important;
}
@media print {
    .bootstrap-iso .visible-print-inline {
        display: inline !important;
    }
}
.bootstrap-iso .visible-print-inline-block {
    display: none !important;
}
@media print {
    .bootstrap-iso .visible-print-inline-block {
        display: inline-block !important;
    }
}
@media print {
    .bootstrap-iso .hidden-print {
        display: none !important;
    }
}

/***admin-style**/
.bootstrap-iso cite,
.bootstrap-iso em,
.bootstrap-iso th {
    font-style: inherit;
    font-weight: inherit;
}
.bootstrap-iso strong {
    font-weight: 700;
}
.bootstrap-iso td,
.bootstrap-iso th,
.bootstrap-iso div {
    word-break: break-word;
    word-wrap: break-word;
}
.bootstrap-iso table {
    border-collapse: collapse;
    border-spacing: 0;
}
.bootstrap-iso th {
    text-align: left;
    font-weight: 100;
}
.bootstrap-iso ol li {
    list-style: decimal outside;
}
.bootstrap-iso ol {
    margin: 0;
    padding: 0 0 0 18px;
}
.bootstrap-iso li {
    list-style: none;
}
.bootstrap-iso img {
    border: 0;
}
.bootstrap-iso html {
    -webkit-text-size-adjust: none;
}
.bootstrap-iso .cc {
    zoom: 1;
}

.bootstrap-iso .p15 {
    padding: 15px;
}
.bootstrap-iso .b {
    font-weight: 700!important;
}
.bootstrap-iso .u {
    text-decoration: underline!important;
}
.bootstrap-iso .i {
    font-style: italic!important;
}
.bootstrap-iso .tal {
    text-align: left!important;
}
.bootstrap-iso .tac {
    text-align: center!important;
}
.bootstrap-iso .tar {
    text-align: right!important;
}
.bootstrap-iso .fl {
    float: left!important;
    display: inline;
}
.bootstrap-iso .fr {
    float: right!important;
    display: inline;
}
.bootstrap-iso .pr {
    position: relative;
}
.bootstrap-iso .cp {
    cursor: pointer;
}
.bootstrap-iso .f10 {
    font-size: 10px;
}
.bootstrap-iso .f12 {
    font-size: 12px!important;
}
.bootstrap-iso .f16 {
    font-size: 16px;
}
.bootstrap-iso .f24 {
    font-size: 24px;
    font-family: Arial, "Microsoft Yahei", Simsun;
}
.bootstrap-iso .fn {
    font-weight: 400!important;
}
.bootstrap-iso .red {
    color: red!important;
}
.bootstrap-iso .org {
    color: #f60!important;
}
.bootstrap-iso .gray {
    color: #999!important;
}
.bootstrap-iso .blue {
    color: #266aae!important;
}
.bootstrap-iso .s6 {
    color: #666;
}
.bootstrap-iso button::-moz-focus-inner,
.bootstrap-iso input::-moz-focus-inner {
    border: 0;
    padding: 0;
}
.bootstrap-iso .btn_big {
    font-size: 1.2em;
    line-height: normal;
    border-radius: 2px;
    padding: 7px 18px;
}
.bootstrap-iso input.btn_big,
.bootstrap-iso button.btn_big {
    padding: 6px 18px 3px!important;
}
.bootstrap-iso .btn_error,
.bootstrap-iso .btn_error:hover,
.bootstrap-iso .btn_success,
.bootstrap-iso .btn_success:hover,
.bootstrap-iso .btn_submit,
.bootstrap-iso .btn_submit:hover {
    color: #fff!important;
}
.bootstrap-iso .btn_submit {
    background-position: 0 -120px;
    background-color: #1b75b6;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
    border-color: #106bab #106bab #0d68a9;
}
.bootstrap-iso .btn_submit:hover {
    background-position: 0 -160px;
}
.bootstrap-iso .btn_submit:active {
    background-position: 0 -201px;
}
.bootstrap-iso .btn_success {
    background-color: #89bf00;
    background-position: 0 -240px;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
    border-color: #6bad01 #6bad01 #63a100;
}
.bootstrap-iso .btn_success:hover {
    background-position: 0 -280px;
}
.bootstrap-iso .btn_success:active {
    background-position: 0 -321px;
}
.bootstrap-iso .btn_error {
    background-color: #f29307;
    background-position: 0 -360px;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
    border-color: #e77c0e #e77c0e #dd7204;
}
.bootstrap-iso .btn_error:hover {
    background-position: 0 -400px;
}
.bootstrap-iso .btn_error:active {
    background-position: 0 -441px;
}
.bootstrap-iso input.disabled,
.bootstrap-iso input.disabled:hover,
.bootstrap-iso textarea.disabled,
.bootstrap-iso textarea.disabled:hover,
.bootstrap-iso select.disabled,
.bootstrap-iso button.disabled,
.bootstrap-iso button.disabled:hover {
    background: #e9e9e9!important;
    box-shadow: none!important;
    color: #999!important;
    cursor: not-allowed;
    text-shadow: 1px 1px 0 #fff!important;
    border-color: #ccc!important;
}
.bootstrap-iso .input,
.bootstrap-iso textarea,
.bootstrap-iso select {
    font-size: 100%;
    line-height: 18px;
    border: 1px solid #ccc;
    background-color: #fff;
    box-shadow: 2px 2px 2px #f0f0f0 inset;
    vertical-align: middle;
    font-family: inherit;
    margin: 0;
    padding: 4px;
}
.bootstrap-iso .input:focus,
.bootstrap-iso textarea:focus {
    outline: 2px solid #99d8f5;
    background-color: #fffbde;
    border-color: #7bbff2;
}

.bootstrap-iso .input {
    height: 18px;
}
.bootstrap-iso input.radio,
.bootstrap-iso input[type=radio],
.bootstrap-iso input.checkbox,
.bootstrap-iso input[type=checkbox] {
    vertical-align: -3px;
    margin-right: 3px;
    marign-rignt: 0;
    padding: 0;
}
.bootstrap-iso textarea {
    height: 72px;
    overflow: auto;
    vertical-align: top;
    resize: vertical;
}
.bootstrap-iso select,
.bootstrap-iso input[type=file] {
    height: 28px;
    line-height: 28px;
}
.bootstrap-iso select[size] {
    height: auto;
}
.bootstrap-iso .form_success {
    outline: 2px solid #87c787;
    border-color: #3e973e #87c787 #87c787!important;
}
.bootstrap-iso .form_error {
    outline: 2px solid #f2a6a6;
    border-color: #bc5050 #f2a6a6 #f2a6a6!important;
}
.bootstrap-iso .length_0 {
    width: 20px;
}
.bootstrap-iso .length_2 {
    width: 110px;
}
.bootstrap-iso .length_4 {
    width: 230px;
}
.bootstrap-iso .length_5 {
    width: 290px;
}
.bootstrap-iso .select_4 {
    width: 240px;
}
.bootstrap-iso .must_red {
    font-family: Simsun;
    color: red;
    margin-top: 3px;
    margin-left: -13px;
    position: absolute;
    font-size: 14px;
}
.bootstrap-iso .pages a,
.bootstrap-iso .pages strong {
    display: inline-block;
    line-height: 28px;
    border: 1px solid #d9d9d9;
    background-color: #fff;
    text-decoration: none;
    color: #666;
    font-family: Simsun;
    -webkit-transition: all 0.2s ease-out;
    transition: all 0.2s ease-out;
    margin-right: 3px;
    padding: 0 10px;
}
.bootstrap-iso .pages a:hover {
    color: #fff;
    background-color: #24557d;
    text-decoration: none;
    border-color: #fff;
}
.bootstrap-iso .pages strong {
    color: #fff;
    background-color: #24557d;
    border-color: #fff;
}
.bootstrap-iso .pages span {
    line-height: 23px;
    padding: 0 10px;
}


.bootstrap-iso .loading_error {
    background: #ffe3e3;
    padding-left: 15px;
}
.bootstrap-iso .tips,
.bootstrap-iso .tips_block span {
    line-height: 25px;
    text-align: center;
    padding: 0 10px;
}
.bootstrap-iso .tips {
    border: 1px solid #f4d5ba;
    background: #ffffe9;
}

.bootstrap-iso .tips_success {
    color: #080;
    background-position: 0 -19px;
}
.bootstrap-iso .tips_block .tips_error {
    background: #c33;
    color: #fff;
    display: block;
    margin: 0;
    padding: 0;
}
.bootstrap-iso .tips_block .tips_success {
    background: #080;
    color: #fff;
    display: block;
    margin: 0;
    padding: 0;
}

.bootstrap-iso .tips_bubble {
    z-index: 1;
    position: absolute;
    margin-top: 45px;
    line-height: 1.5;
    color: #666;
    border: 1px solid #f4d1a5;
    background-color: #fffbe7;
    width: 180px;
    margin-left: 30px;
    padding: 10px;
}
.bootstrap-iso .tips_bubble .core_arrow_bottom {
    position: absolute;
    bottom: 9px;
    right: 10px;
    padding: 0 20px;
}
.bootstrap-iso .tips_bubble .core_arrow_bottom span {
    border-top: 8px #fffbe7 solid;
}
.bootstrap-iso .tips_bubble .core_arrow_bottom em {
    border-top: 8px #f4d1a5 solid;
    margin-top: 10px;
}
.bootstrap-iso .core_pop_wrap {
    background-color: #fff;
    border: 1px solid #ccc;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 3px;
    position: fixed;
    _position: absolute;
    z-index: 10;
    color: #333;
    outline: none;
}
.bootstrap-iso .core_pop_wrap a {
    color: #369;
}
.bootstrap-iso .pop_top {
    line-height: 18px;
    border-top: 1px solid #fff;
    border-bottom: 1px solid #e7e7e7;
    background: #f6f6f6;
    zoom: 1;
    margin-bottom: 5px;
    padding: 9px 0 8px;
}
.bootstrap-iso .pop_top:after {
    content: '\20';
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
    width: 1px;
}
.bootstrap-iso .pop_top strong {
    text-indent: 15px;
    font-size: 14px;
    color: #333;
    font-weight: 700;
    white-space: nowrap;
    margin-right: 10px;
    float: left;
}
.bootstrap-iso .pop_top select {
    float: left;
    line-height: 22px;
    height: 22px;
    padding: 1px;
}
.bootstrap-iso .pop_top ul {
    border-bottom: 1px solid #ccc;
    height: 25px;
}
.bootstrap-iso .pop_top li {
    float: left;
    display: block;
    line-height: 25px;
    height: 25px;
    cursor: pointer;
    padding: 0 15px;
}
.bootstrap-iso .pop_top li.current {
    float: left;
    border: 1px solid #ccc;
    background: #fff;
    border-bottom: 0 none;
}
.bootstrap-iso .pop_top .pop_close {
    margin-right: 15px;
}
.bootstrap-iso .pop_bottom {
    border-top: 1px solid #e9e9e9;
    height: 30px;
    padding: 10px 15px;
}
.bootstrap-iso .pop_bottom label {
    display: inline-block;
    padding-top: 3px;
}
.bootstrap-iso .pop_bottom .btn {
    padding-left: 20px;
    padding-right: 20px;
}
.bootstrap-iso .pop_bottom .tips_error,
.bootstrap-iso .pop_bottom .tips_success {
    max-width: 200px;
    float: left;
}
.bootstrap-iso .founder_pop .pop_bottom .tips_error {
    width: 150px;
}
.bootstrap-iso .pop_cont {
    background: #fff;
    color: #333;
    padding: 10px 15px;
}
.bootstrap-iso .pop_table {
    height: 365px;
    overflow-y: auto;
    position: relative;
}
.bootstrap-iso .pop_table th,
.bootstrap-iso .pop_table td {
    padding: 6px 0;
}
.bootstrap-iso .pop_table th {
    height: 26px;
    line-height: 26px;
    vertical-align: top;
    white-space: nowrap;
    padding-right: 20px;
}
.bootstrap-iso .pop_tips {
    background: #f7f7f7;
    line-height: 24px;
    color: #666;
    margin: 0 10px;
    padding: 0 10px;
}


.bootstrap-iso body {
    font-family: "Microsoft Yahei", Arial;
    color: #333;
    font-size: 12px;
    background: #f3f3f3;
    line-height: 1.5;
    _width: 98%;
    overflow-x: auto;
    overflow-y: auto;
}
.bootstrap-iso a {
    color: #266aae;
    text-decoration: none;
}
.bootstrap-iso .wrap {
    padding: 20px 20px 70px;
}
.bootstrap-iso .nav {
    margin-bottom: 15px;
    min-height: 35px;
}
.bootstrap-iso .nav ul {
    height: 35px;
    float: left;
}
.bootstrap-iso .nav li a,
.bootstrap-iso .nav .return a {
    float: left;
    line-height: 32px;
    color: #666;
    text-align: center;
    padding: 0 15px;
}
.bootstrap-iso .nav li a:hover {
    border-bottom: 2px solid #aaa;
    color: #333;
    text-decoration: none;
}
.bootstrap-iso .nav li.current a {
    border-bottom: 2px solid #266aae;
    font-weight: 700;
    color: #266aae;
}


.bootstrap-iso #branch-selector .selected:hover {
    background-position: right -94px;
    color: #F27B04;
}
.bootstrap-iso #branch-selector .list {
    left: 0;
    top: 22px;
    width: 280px;
    background: #333;
    position: absolute;
    z-index: 999;
    padding: 10px;
}
.bootstrap-iso #branch-selector .list em {
    position: absolute;
    right: 4px;
    top: 2px;
    font-style: normal;
    color: #fff;
    cursor: pointer;
}
.bootstrap-iso #branch-selector .list a {
    text-decoration: none;
    color: #ddd;
    margin-right: 10px;
    line-height: 20px;
    display: inline-block;
    border-radius: 2px;
    margin-bottom: 6px;
    padding: 0 4px;
}
.bootstrap-iso #branch-selector .list a.current {
    background: #fff;
    color: #333;
}
.bootstrap-iso #branch-selector .list a:hover {
    color: #fff;
    background-color: #F27B04;
}
.bootstrap-iso #branch-selector .list a i.extra-number {
    margin-left: 2px;
    font-style: normal;
    background: #dedede;
    border-radius: 6px;
    font-size: 10px;
    color: #333;
    padding: 0 3px;
}
.bootstrap-iso #branch-selector .list a.current i.extra-number {
    color: #fff;
    background: #F27B04;
}
.bootstrap-iso .nav_minor {
    margin-top: -5px;
    position: relative;
    margin-bottom: 15px;
    height: 24px;
}
.bootstrap-iso .nav_minor li a {
    float: left;
    line-height: 24px;
    color: #666;
    text-align: center;
    padding: 0 15px;
}
.bootstrap-iso .nav_minor li.current a {
    background: #ddd;
    color: #333;
    font-weight: 700;
}
.bootstrap-iso .h_a {
    border-top: 1px solid #ddd;
    border-bottom: 1px solid #fff;
    color: #666;
    font-weight: 700;
    background: #e6e6e6;
    font-size: 12px;
    padding: 5px 9px;
}
.bootstrap-iso tr.h_a {
    background: #e6e6e6;
    padding: 0;
}
.bootstrap-iso tr.h_a th,
.bootstrap-iso tr.h_a td {
    padding-top: 5px;
    padding-bottom: 5px;
    line-height: 18px;
    background: #e6e6e6!important;
    border-top: 1px solid #ddd;
    border-bottom: 1px solid #fff;
}
.bootstrap-iso .table_list {
    border-top: 1px solid #ebebeb;
    margin-bottom: 10px;
}
.bootstrap-iso .table_list .h_a {
    border-top: 0 none;
}
.bootstrap-iso .table_list td {
    padding: 7px 10px 9px;
}
.bootstrap-iso .table_list tr:hover td {
    color: #000;
    background-color: #f9f9f9;
}
.bootstrap-iso .table_list thead tr td,
.bootstrap-iso .table_list thead tr th,
.bootstrap-iso .table_list thead tr:hover td {
    background: #e6e6e6;
    border-top: 1px solid #ddd;
    border-bottom: 1px solid #fff;
    color: #666!important;
    padding: 5px 9px;
}
.bootstrap-iso .table_list thead tr td select {
    height: 23px;
    line-height: 23px;
    padding: 1px;
}
.bootstrap-iso .table_list .thumb {
    line-height: 1;
    vertical-align: top;
    margin-right: 10px;
    float: left;
}
.bootstrap-iso .table_list tr.bgA td {
    background-color: #f0f8fc;
}
.bootstrap-iso .table_full table {
    table-layout: fixed;
}

.bootstrap-iso .table_full th {
    background-color: #f8f8f8;
    border-right: 1px solid #e5e3e3;
    font-weight: 100;
    line-height: 24px;
    vertical-align: top;
    padding: 7px 10px 9px;
}
.bootstrap-iso .table_full .th {
    width: 160px;
}
.bootstrap-iso .table_full th .s1 {
    padding-left: 5px;
}
.bootstrap-iso .table_full td {
    color: #666;
    vertical-align: top;
    padding: 7px 10px 9px 15px;
}
.bootstrap-iso .table_full tr:hover th {
    background-color: #eef3f6;
}
.bootstrap-iso .table_full tr:hover td {
    color: #000;
    background-color: #eef3f6;
}
.bootstrap-iso .table_full tr .fun_tips {
    color: #999;
    line-height: 24px;
}
.bootstrap-iso .table_full tr.tr_checkbox th,
.bootstrap-iso .table_full tr.tr_checkbox td {
    background-color: #fffaeb;
}
.bootstrap-iso .table_purview th {
    font-weight: 100;
    background-color: #f9f9f9;
    padding: 7px 10px 9px;
}
.bootstrap-iso .table_purview td {
    border-left: 1px solid #e5e3e3;
    padding: 5px 10px 7px 15px;
}
.bootstrap-iso .table_purview tr:hover th,
.bootstrap-iso .table_purview tr:hover td {
    background-color: #f9f9f9;
}
.bootstrap-iso .table_purview tr.hd_bar td {
    border: 0 none;
    background-color: #f9f9f9;
}
.bootstrap-iso .table_purview tr.hd_bar th {
    color: #000;
    font-weight: 700;
    background-color: #f9f9f9;
}
.bootstrap-iso .table_purview tr.hd td,
.bootstrap-iso .table_purview tr.hd th {
    background-color: #fffeee;
}

.bootstrap-iso .three_list li {
    float: left;
    width: 33%;
    height: 25px;
    line-height: 25px;
    overflow: hidden;
}
.bootstrap-iso .three_list li.all_check {
    width: 100%;
    padding-left: 2px;
}
.bootstrap-iso .double_list li {
    float: left;
    width: 49%;
    line-height: 25px;
    height: 25px;
    overflow: hidden;
}
.bootstrap-iso .double_list li span {
    padding-left: 20px;
    color: #666;
}
.bootstrap-iso .double_list li label span {
    color: #666;
    padding: inherit;
}
.bootstrap-iso .single_list li {
    line-height: 25px;
    height: 25px;
}
.bootstrap-iso .switch_list {
    height: 25px;
    line-height: 25px;
    overflow: hidden;
}
.bootstrap-iso .switch_list li {
    float: left;
    width: 120px;
}
.bootstrap-iso .user_group {
    border: 1px solid #ccc;
    background: #fff;
    position: relative;
}
.bootstrap-iso .user_group dt {
    line-height: 25px;
    height: 25px;
    font-weight: 700;
    color: #000;
    padding: 0 5px;
}
.bootstrap-iso .user_group dt input {
    _margin: 3px 0 0 0;
    _padding: 0;
}
.bootstrap-iso .user_group dd {
    zoom: 1;
    position: relative;
    padding: 5px 0;
}
.bootstrap-iso .user_group dd label {
    display: inline;
    margin-bottom: 1px;
    float: left;
    width: 336px;
    margin-left: 5px;
}
.bootstrap-iso .user_group dd input {
    float: left;
    margin-top: 0;
}
.bootstrap-iso .user_group dd span {
    color: #666;
    padding: 2px 0 2px 5px;
}
.bootstrap-iso .sql_list {
    border: 1px solid #ccc;
    background: #fff;
    width: 558px;
    position: relative;
}
.bootstrap-iso .sql_list dt {
    line-height: 25px;
    height: 25px;
    font-weight: 700;
    color: #666;
    padding: 0 5px;
}
.bootstrap-iso .sql_list dd {
    height: 300px;
    overflow: hidden;
    overflow-y: auto;
}
.bootstrap-iso .sql_list dd p {
    border-bottom: 1px solid #e4e4e4;
    line-height: 25px;
    height: 25px;
    overflow: hidden;
    padding: 3px 5px;
}
.bootstrap-iso .task_item_list {
    border: 1px solid #ccc;
    background: #fff;
    width: 358px;
    overflow: hidden;
}
.bootstrap-iso .task_item_list .hd {
    height: 25px;
}
.bootstrap-iso .task_item_list .hd li {
    margin-left: -1px;
    border-left: 1px solid #dfdfdf;
    float: left;
    width: 50%;
    text-align: center;
}
.bootstrap-iso .task_item_list .hd a {
    line-height: 25px;
    font-weight: 700;
    color: #000;
    display: block;
}
.bootstrap-iso .task_item_list .hd .current a {
    background: #fff;
    height: 26px;
}
.bootstrap-iso .task_item_list .ct ul {
    padding: 5px 0 10px 10px;
}
.bootstrap-iso .task_item_list .ct li {
    float: left;
    width: 33%;
    line-height: 24px;
    height: 24px;
    overflow: hidden;
}
.bootstrap-iso .cross {
    width: 390px;
    overflow: hidden;
}
.bootstrap-iso .cross ul {
    margin-left: -10px;
}
.bootstrap-iso .cross li {
    line-height: 30px;
    height: 30px;
    overflow: hidden;
    padding: 0 0 10px;
}
.bootstrap-iso .cross img {
    vertical-align: middle;
}
.bootstrap-iso .cross li span {
    margin-left: 10px;
    float: left;
    height: 30px;
}
.bootstrap-iso .cross li .span_1 {
    width: 30px;
}

.bootstrap-iso .start_icon,
.bootstrap-iso .away_icon,
.bootstrap-iso .zero_icon,
.bootstrap-iso .plus_none_icon {
    width: 20px;
    height: 20px;
    overflow: hidden;
    cursor: pointer;
}
.bootstrap-iso .away_icon {
    background-position: 0 -20px;
}
.bootstrap-iso .zero_icon {
    background-position: -20px 0;
    cursor: default;
}
.bootstrap-iso .plus_icon,
.bootstrap-iso .plus_end_icon {
    width: 40px;
    height: 24px;
    background-position: 0 -43px;
    margin-right: 5px;
}
.bootstrap-iso .plus_end_icon {
    background-position: 0 -70px;
}
.bootstrap-iso .plus_on_icon {
    background-position: 0 -100px;
    cursor: default;
}
.bootstrap-iso .plus_none_icon {
    background-position: 999px 999px;
    cursor: default;
}
.bootstrap-iso .btn_wrap_pd {
    padding: 0 10px 10px;
}

.bootstrap-iso .btn_wrap_pd .btn {
    min-width: 80px;
    margin-right: 10px;
    _width: 100px;
}
.bootstrap-iso .select_pages {
    float: right;
    line-height: 26px;
    color: #999;
}
.bootstrap-iso .select_pages span {
    font-family: Simsun;
    color: #999;
    padding: 0 10px;
}
.bootstrap-iso .shift select {
    width: 160px;
    padding: 5px;
}
.bootstrap-iso .shift_operate {
    padding: 70px 10px 0;
}
.bootstrap-iso .shift h4 {
    margin-bottom: 5px;
    font-size: 12px;
}
.bootstrap-iso .shift .btn {
    font-family: Simsun;
}
.bootstrap-iso .shift .select_div {
    border: 1px solid #ccc;
    background: #fff;
    height: 180px;
    overflow-y: auto;
    width: 158px;
    float: left;
    line-height: 25px;
    text-indent: 10px;
    padding-bottom: 5px;
}
.bootstrap-iso .shift .select_div dl {
    padding: 5px 5px 0;
}

.bootstrap-iso .shift .select_div a {
    display: block;
    color: #666;
}
.bootstrap-iso .shift .select_div a:hover,
.bootstrap-iso .shift .select_div a.current {
    display: block;
    background: #e6e6e6;
    text-decoration: none;
}
.bootstrap-iso .prompt_text {
    color: #666;
    margin-bottom: 10px;
    padding: 10px;
}
.bootstrap-iso .prompt_text ol,
.bootstrap-iso .prompt_text ul {
    line-height: 1.5;
    margin: 0;
    padding: 0 0 5px 2em;
}
.bootstrap-iso .prompt_text ul li {
    list-style: disc outside;
}

.bootstrap-iso .error_return {
    padding: 10px 0 0;
}
.bootstrap-iso .search_type li {
    _height: 30px;
    float: left;
    width: 350px;
    margin-right: 20px;
    min-height: 30px;
    padding-bottom: 5px;
}
.bootstrap-iso .search_type li label {
    display: inline-block;
    width: 70px;
    line-height: 24px;
}
.bootstrap-iso .search_type .ul_wrap {
    width: 100%;
    min-height: 1px;
    zoom: 1;
}
.bootstrap-iso .search_type li.two {
    width: 700px;
}
.bootstrap-iso .search_type .btn_side {
    text-align: center;
    padding-top: 10px;
    padding-right: 140px;
}

.bootstrap-iso .locate {
    border: 1px solid #ccc;
    background: #fff;
    width: 209px;
}
.bootstrap-iso .locate ul {
    margin-top: -1px;
    margin-left: -1px;
}
.bootstrap-iso .locate li {
    border-left: 1px solid #ccc;
    border-top: 1px solid #ccc;
    float: left;
    width: 69px;
    height: 40px;
    text-align: center;
}
.bootstrap-iso .locate li a {
    color: #333;
    display: block;
    border: 1px solid #fff;
    line-height: 38px;
}
.bootstrap-iso .forum_search_pop {
    position: absolute;
    border: 1px solid #ccc;
    border-bottom-width: 2px;
    background: #fff;
    width: 178px;
}
.bootstrap-iso .forum_search_pop ul {
    border: 1px solid #fff;
}
.bootstrap-iso .forum_search_pop li {
    line-height: 25px;
    height: 25px;
    overflow: hidden;
    text-indent: 15px;
}
.bootstrap-iso .forum_search_pop li a {
    text-decoration: none;
    display: block;
    color: #333;
}
.bootstrap-iso .forum_search_pop li.current a {
    background: #f0f0f0;
    color: #266aae;
}
.bootstrap-iso .yarnball {
    border-left: 1px solid #d9d9d9;
    overflow: hidden;
    padding-right: 10px;
    vertical-align: middle;
    display: inline-block;
    position: relative;
}
.bootstrap-iso .yarnball ul {
    float: left;
    margin-left: -5px;
}

.bootstrap-iso .yarnball ul li a {
    color: #666;
    display: block;
    line-height: 30px;
    height: 30px;
    overflow: hidden;
    text-decoration: none;
    text-align: center;
    padding: 0 16px 0 12px;
}
.bootstrap-iso .yarnball ul li em {
    width: 9px;
    height: 30px;
    display: block;
    background-position: right 0;
    margin-top: -30px;
    z-index: 2;
}
.bootstrap-iso .yarnball ul li.hover a {
    background-position: 0 -30px;
    color: #333;
}
.bootstrap-iso .yarnball ul li.hover em {
    background-position: right -30px;
}
.bootstrap-iso .yarnball ul li.li_disabled a {
    background-position: 0 -60px;
    color: #333;
    cursor: default;
}
.bootstrap-iso .yarnball ul li.li_disabled em {
    background-position: right -60px;
}
.bootstrap-iso .textarea_code {
    border: 1px solid #ccc;
    background: #fff;
    font-family: Consolas, "Bitstream Vera Sans Mono", "Courier New", Courier, monospace !important;
    line-height: 1.8;
    width: 280px;
    overflow: hidden;
    padding: 2px 5px;
}
.bootstrap-iso .cate_link span {
    display: inline-block;
    background: #ddd;
    margin: 0 5px 5px 0;
    padding: 0 5px;
}

.bootstrap-iso .cate_link_down:hover {
    background-position: 0 bottom;
}
.bootstrap-iso .core_menu {
    position: absolute;
}
.bootstrap-iso .core_menu_list {
    border: 1px solid #ddd;
    background: #fff;
    width: 118px;
    box-shadow: 3px 3px 5px #ccc;
    padding: 5px;
}
.bootstrap-iso .core_menu_list li {
    float: left;
    width: 100%;
    line-height: 18px;
    height: 18px;
    margin: 0;
    padding: 2px 5px;
}
.bootstrap-iso .core_menu_list li a {
    display: block;
    text-indent: 10px;
    color: #333;
    border: 0 none;
    float: left;
    width: 100%;
    border-radius: 0;
    margin: 0;
}
.bootstrap-iso .core_menu_list li a:hover {
    border: 0 none;
    background: #36c;
    color: #fff;
    text-decoration: none;
}
.bootstrap-iso .medal_term .hd span {
    font-family: Simsun;
    margin: 0 10px;
}
.bootstrap-iso .medal_term .ct {
    border: 1px solid #ccc;
    width: 358px;
    border-bottom: 0 none;
}
.bootstrap-iso .medal_term .ct h6 {
    height: 24px;
    line-height: 24px;
    text-align: center;
    font-size: 12px;
}
.bootstrap-iso .medal_term .ct dl {
    height: 24px;
    line-height: 24px;
    border-bottom: 1px solid #ccc;
    overflow: hidden;
}
.bootstrap-iso .medal_term .ct dt {
    float: left;
    width: 80px;
    text-align: center;
}
.bootstrap-iso .medal_term .ct dd {
    float: left;
    border-left: 1px solid #ccc;
}
.bootstrap-iso .medal_term .ct dd.num {
    width: 100px;
    font-family: Simsun;
    text-align: center;
}
.bootstrap-iso .medal_term .ct dd.title {
    width: 160px;
    text-indent: 10px;
}
.bootstrap-iso .single_image_up a {
    width: 0;
    height: 0;
    display: block;
    overflow: hidden;
    text-indent: -2000em;
}
.bootstrap-iso .single_file_up {
    width: 80px;
    height: 20px;
    position: relative;
    margin-bottom: 5px;
    overflow: hidden;
}

.bootstrap-iso .single_file_up:hover a {
    text-decoration: none;
    filter: alpha(opacity=100);
    -moz-opacity: 1;
    opacity: 1;
}
.bootstrap-iso .single_file_up input,
.bootstrap-iso .single_file_up object {
    width: 80px;
    height: 22px;
    position: absolute;
    top: 0;
    right: 0;
    background: none;
    filter: alpha(opacity=0);
    -moz-opacity: 0;
    opacity: 0;
    cursor: pointer;
    outline: none;
}
.bootstrap-iso .progress_bar {
    border: 1px solid #c6c6c6;
    background: #fff;
    height: 9px;
    display: inline-block;
    width: 100%;
    vertical-align: middle;
    position: relative;
}
.bootstrap-iso .progress_bar span {
    background-color: #669201;
    border: 1px solid #577d00;
    display: inline-block;
    height: 9px;
    position: absolute;
    margin-top: -1px;
    left: -1px;
    font: 0/0 Arial;
}

.bootstrap-iso .color_pick:hover {
    background-color: #fffbde;
}
.bootstrap-iso .color_pick.color_current {
    background-color: #fffbde;
    border-color: #aaa #aaa #555;
}

.bootstrap-iso .color_pick.color_big {
    background-image: none;
    width: 80px;
    height: 60px;
    text-align: center;
    color: #333;
    padding: 3px;
}
.bootstrap-iso .color_pick.color_big em {
    width: 80px;
    height: 38px;
    margin-bottom: 3px;
    text-align: center;
}
.bootstrap-iso .color_pick_dom ul {
    height: 29px;
    width: 170px;
    padding: 3px 0 0;
}
.bootstrap-iso .color_pick_dom li {
    float: left;
    margin-right: 10px;
    _margin-right: 8px;
    white-space: nowrap;
}
.bootstrap-iso .color_pick_dom li input {
    _margin: 0 0 -1px -3px;
    _padding: 0;
}
.bootstrap-iso .color_pick_dom li.none {
    margin-right: 0;
}
.bootstrap-iso .color_pick_dom .color_pick {
    background-position: 151px center;
}
.bootstrap-iso .color_pick_dom .color_pick em {
    width: 145px;
}
.bootstrap-iso .color_pick_dom .case {
    float: right;
    width: 100px;
    border: 1px solid #ccc;
    background: #fff;
    padding: 10px;
}
.bootstrap-iso .pop_region_list ul {
    padding-left: 2px;
}
.bootstrap-iso .pop_region_list ul li {
    float: left;
    line-height: 20px;
}
.bootstrap-iso .pop_region_list ul li a,
.bootstrap-iso .pop_region_list ul li span {
    display: block;
    color: #333;
    white-space: nowrap;
    border-radius: 2px;
    padding: 0 5px;
}
.bootstrap-iso .pop_region_list ul li a:hover {
    background: #e0e0e0;
    text-decoration: none;
}
.bootstrap-iso .pop_region_list .hr {
    background: #e4e4e4;
    height: 1px;
    overflow: hidden;
    font: 0/0 Arial;
    clear: both;
    margin: 10px 0;
}
.bootstrap-iso .pop_region_list .filter a {
    margin-right: 12px;
}
.bootstrap-iso .pop_region_list .filter a.current {
    color: #333;
    font-weight: 700;
}
.bootstrap-iso .pop_region_list .list {
    border: 1px solid #ccc;
    height: 108px;
    overflow-x: hidden;
    overflow-y: auto;
}
.bootstrap-iso .pop_region_list .list li {
    float: left;
    width: 33%;
    cursor: pointer;
    text-indent: 5px;
}
.bootstrap-iso .pop_seo {
    border: 1px solid #ccc;
    background: #fffeee;
    width: 280px;
    position: absolute;
    padding: 9px;
}

.bootstrap-iso .pop_seo .hd {
    margin-bottom: 5px;
    color: #000;
}
.bootstrap-iso .pop_seo .ct a {
    display: inline-block;
    line-height: 25px;
    margin-right: 20px;
}
.bootstrap-iso .agreements .pre {
    border: 1px solid #ccc;
    background: #fff;
    width: 500px;
    height: 200px;
    overflow-x: hidden;
    overflow-y: scroll;
    margin-bottom: 10px;
    line-height: 1.7;
    padding: 15px;
}
.bootstrap-iso .agreements p {
    margin-bottom: 1em;
    text-indent: 2em;
    color: #666;
}
.bootstrap-iso .agreements h1 {
    font-size: 18px;
    text-align: center;
    margin-bottom: 15px;
}
.bootstrap-iso .agreements h2,
.bootstrap-iso .agreements h3,
.bootstrap-iso .agreements h4 {
    margin-bottom: 1em;
}
.bootstrap-iso .agreements ol,
.bootstrap-iso .agreements ul {
    margin-left: 1em;
    margin-bottom: 1em;
}

.bootstrap-iso .sort_down_old {
    background-position: right 3px;
}
.bootstrap-iso .sort_up {
    background-position: right -117px;
}
.bootstrap-iso .sort_up_old {
    background-position: right -77px;
}
.bootstrap-iso .app_info h1 {
    font-size: 24px;
    font-family: "Microsoft Yahei";
    font-weight: 400;
    margin-bottom: 5px;
}
.bootstrap-iso .app_info li {
    line-height: 25px;
    float: left;
    width: 45%;
}
.bootstrap-iso .app_info li em {
    display: inline-block;
    width: 80px;
    color: #666;
}
.bootstrap-iso .app_thumb li {
    float: left;
    margin-right: 20px;
    margin-top: 20px;
}
.bootstrap-iso .app_thumb li img {
    vertical-align: top;
    height: 266px;
    width: 200px;
}
.bootstrap-iso .app_updata .time {
    font-weight: 700;
    margin-bottom: 5px;
}
.bootstrap-iso .app_updata .version {
    margin-bottom: 5px;
}
.bootstrap-iso .pop_stats table td,
.bootstrap-iso .pop_stats table th {
    border: 1px solid #ccc;
    padding: 5px 10px;
}
.bootstrap-iso .widget_upload_photos {
    padding-bottom: 5px;
}
.bootstrap-iso .widget_upload_photos li {
    position: relative;
    float: left;
    width: 50px;
    height: 50px;
    border: 1px solid #ccc;
    background: #f7f7f7;
    overflow: hidden;
    margin-right: 5px;
    line-height: 50px;
    text-align: center;
}
.bootstrap-iso .widget_upload_photos li input {
    height: 50px;
    border: 0;
    width: 50px;
    position: absolute;
    left: 0;
    top: 0;
    filter: alpha(opacity=00);
    -moz-opacity: 0;
    opacity: 0;
    margin: 0;
    padding: 0;
}
.bootstrap-iso .widget_history_file {
    border: 1px solid #ccc;
    background: #fffeee;
    margin: 0 auto 15px;
}
.bootstrap-iso .widget_history_file dt {
    float: left;
    width: 70px;
    padding: 5px 10px;
}
.bootstrap-iso .widget_history_file dd {
    float: left;
    border-left: 1px solid #ccc;
    padding: 5px 10px;
}

.bootstrap-iso .widget_update_log h3 {
    font-size: 12px;
    margin-bottom: 5px;
}
.bootstrap-iso .widget_update_log table {
    border: 1px solid #ccc;
}
.bootstrap-iso .widget_update_log td,
.bootstrap-iso .widget_update_log th {
    border: 1px solid #ccc;
    padding: 5px 10px;
}
.bootstrap-iso .pop_advanced_search {
    width: 450px;
}
.bootstrap-iso .pop_advanced_search .pop_cont {
    overflow: hidden;
}
.bootstrap-iso .pop_advanced_search ul {
    margin-left: -4%;
}
.bootstrap-iso .pop_advanced_search li {
    float: left;
    width: 46%;
    margin-left: 4%;
    height: 60px;
}
.bootstrap-iso .pop_advanced_search p {
    padding-bottom: 3px;
}
.bootstrap-iso .pop_advanced_search .gap {
    display: inline-block;
    width: 42px;
    text-align: center;
}
.bootstrap-iso .about_list {
    border-top: 1px dashed #ccc;
    margin-top: 10px;
}
.bootstrap-iso .about_list dl {
    padding-top: 10px;
}
.bootstrap-iso .about_list dt {
    float: left;
    width: 80px;
    line-height: 26px;
}
.bootstrap-iso .about_list dd {
    overflow: hidden;
    line-height: 26px;
}
.bootstrap-iso .about_list label {
    display: inline-block;
    width: 100px;
    color: #666;
}
.bootstrap-iso .about_list:hover label {
    color: #333;
}
.bootstrap-iso .email_example {
    border: 1px solid #ccc;
    background: #fff;
    color: #333;
    width: 268px;
    line-height: 1.8;
    padding: 10px 15px;
}
.bootstrap-iso .variable_sample {
    border-top: 1px solid #e4e4e4;
    border-left: 1px solid #e4e4e4;
    border-right: 1px solid #e4e4e4;
    background: #fff;
    float: left;
}
.bootstrap-iso .variable_sample li {
    border-bottom: 1px solid #e4e4e4;
    line-height: 25px;
    height: 25px;
}
.bootstrap-iso .variable_sample li span {
    float: left;
    width: 200px;
    font-family: "Courier New", Courier, monospace;
    border-left: 1px solid #e4e4e4;
    padding-left: 10px;
    color: #333;
}
.bootstrap-iso .variable_sample li em {
    padding-left: 10px;
    float: left;
    width: 200px;
    background: #f7fbff;
}
.bootstrap-iso .search_photo_list {
    padding: 20px 0 0;
}
.bootstrap-iso .search_photo_list li {
    float: left;
    width: 90px;
    height: 90px;
    border: 1px solid #ccc;
    background: #fff;
    position: relative;
    display: inline;
    margin: 0 20px 20px 0;
    padding: 4px;
}
.bootstrap-iso .search_photo_list li input {
    position: absolute;
    right: 0;
    bottom: 0;
}
.bootstrap-iso .design_ct dt {
    float: left;
    width: 120px;
    line-height: 26px;
    padding: 0 10px;
}
.bootstrap-iso .design_ct dd {
    margin-left: 150px;
}
.bootstrap-iso .pop_design_code {
    width: 440px;
    float: left;
}
.bootstrap-iso .pop_design_code textarea {
    width: 430px;
    height: 280px;
    font-family: "Courier New", Courier, monospace;
    margin-bottom: 5px;
}
.bootstrap-iso .pop_design_case {
    float: left;
    width: 180px;
    height: 350px;
    overflow-x: hidden;
    overflow-y: auto;
    padding: 10px 0;
}
.bootstrap-iso .pop_design_tablelist {
    width: 100%;
    table-layout: fixed;
    border-top: 1px solid #e4e4e4;
    margin-bottom: 10px;
}
.bootstrap-iso .pop_design_tablelist thead td,
.bootstrap-iso .pop_design_tablelist thead th {
    background-color: #f7f7f7;
}
.bootstrap-iso .pop_design_tablelist thead td select {
    line-height: 22px;
    height: 22px;
    padding: 1px;
}
.bootstrap-iso .pop_design_tablelist th {
    padding-left: 10px;
}
.bootstrap-iso .pop_design_tablelist .subject {
    white-space: nowrap;
    text-overflow: ellipsis;
    line-height: 18px;
    height: 18px;
    overflow: hidden;
}
.bootstrap-iso .pop_showmsg_wrap:focus {
    outline: 0 none;
}

.bootstrap-iso .pop_showmsg_wrap {
    position: fixed;
    _position: absolute;
    z-index: 11;
    height: 55px;
    padding-right: 9px;
    background-position: right 0;
    border-radius: 8px;
    box-shadow: 0 0 10px #e1e1e1;
}
.bootstrap-iso .pop_showmsg {
    height: 55px;
    font-size: 14px;
    background-position: left 0;
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
    display: inline-block;
}
.bootstrap-iso .pop_showmsg span {
    display: inline-block;
    line-height: 36px;
    height: 35px;
    text-shadow: 0 1px 1px #eee;
    color: #333;
    padding: 10px 10px 10px 68px;
}

.bootstrap-iso .design_page {
    padding-bottom: 10px;
    width: 800px;
}
.bootstrap-iso .design_page li {
    float: left;
    margin-right: 23px;
    display: inline;
    box-shadow: 0 0 1px rgba(0, 0, 0, 0.1);
    background: #fff;
    margin-bottom: 20px;
    border: 1px solid;
    width: 230px;
    height: 310px;
    position: relative;
    border-color: #ecebeb #e1e0e0 #d5d5d5;
}
.bootstrap-iso .design_page .img {
    display: block;
    padding: 10px;
}
.bootstrap-iso .design_page li .ft {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    background: #f8f8f8;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #fff;
    padding: 5px 0;
}
.bootstrap-iso .design_page li .ft .org {
    padding: 0 0 0 10px;
}
.bootstrap-iso .design_page li .ft a {
    color: #666;
    margin: 0 0 0 10px;
}
.bootstrap-iso .design_page li .title {
    font-size: 14px;
    line-height: 18px;
    height: 18px;
    overflow: hidden;
    margin-bottom: 3px;
    white-space: nowrap;
    text-overflow: ellipsis;
    -ms-text-overflow: ellipsis;
    word-wrap: normal;
    padding: 0 10px;
}
.bootstrap-iso .design_page li .descrip {
    color: #999;
    line-height: 18px;
    height: 18px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    -ms-text-overflow: ellipsis;
    word-wrap: normal;
    padding: 0 10px 3px;
}
.bootstrap-iso .design_page li .type {
    color: #999;
    padding: 0 10px 8px;
}
.bootstrap-iso .design_page li .type span {
    margin-right: 10px;
}
.bootstrap-iso .home_tips {
    border: 1px solid #f0d9af;
    background: #fdf8e4;
    color: #666;
    margin-bottom: 15px;
    padding: 10px 15px;
}
.bootstrap-iso .home_tips h4 {
    font-size: 12px;
    font-weight: 700;
    color: #af8133;
    margin-bottom: 10px;
}
.bootstrap-iso .home_info {
    color: #666;
    padding: 5px 10px 20px;
}
.bootstrap-iso .home_info li {
    line-height: 25px;
    zoom: 1;
}
.bootstrap-iso .home_info li em {
    float: left;
    width: 100px;
    font-style: normal;
}
.bootstrap-iso .home_info li span {
    display: block;
    overflow: hidden;
}

.bootstrap-iso .install_schedule_bg {
    height: 15px;
    width: 202px;
    margin: 5px 0;
    padding: 0 4px;
}
.bootstrap-iso .install_schedule {
    background-position: right -17px;
    height: 15px;
}
.bootstrap-iso .install_schedule span {
    background-position: 0 -17px;
    display: block;
    width: 5px;
    height: 15px;
}
.bootstrap-iso .install_load {
    line-height: 18px;
}
.bootstrap-iso .search_list h2 {
    font-size: 14px;
    padding: 10px;
}
.bootstrap-iso .search_list dl {
    margin: 0 0 10px;
    padding: 0 2em 10px;
}
.bootstrap-iso .pop_expand {
    bottom: 50px;
    position: fixed;
    left: 120px;
    width: 440px;
    z-index: 6;
}
.bootstrap-iso .pop_expand .core_arrow_bottom {
    position: absolute;
    bottom: 9px;
    left: 25px;
    padding: 0 25px;
}
.bootstrap-iso .pop_expand .core_arrow_bottom span {
    border-top: 8px #fff solid;
}
.bootstrap-iso .pop_expand .core_arrow_bottom em {
    border-top: 8px #c1c1c1 solid;
    margin-top: 10px;
}
.bootstrap-iso .stepstat .current {
    color: #090;
}
.bootstrap-iso .stepstat li {
    color: #CCC;
    float: left;
    margin-right: 15px;
}
.bootstrap-iso .system_update {
    border: 1px solid #d3d3c8;
    background: #ffe;
    position: absolute;
    width: 500px;
    text-align: center;
    left: 50%;
    margin-left: -250px;
    top: 10px;
    padding: 8px 20px;
}
.bootstrap-iso .system_update a {
    color: #266AAE;
    margin: 0 0 0 20px;
}
.bootstrap-iso .app_icon {
    display: inline-block;
    vertical-align: top;
    width: 80px;
    height: 80px;
    position: relative;
    background: #f7f7f7;
}

.bootstrap-iso .upgrade_page {
    width: 800px;
}
.bootstrap-iso .not_content_mini {
    border: 1px solid #e4e4e4;
    background: #fff;
    color: #666;
    text-align: center;
    margin-bottom: 20px;
    padding: 20px;
}

.bootstrap-iso .not_content_mini i.success {
    background-position: -40px -20px;
}
.bootstrap-iso div.form span.required {
    color: red;
}
.bootstrap-iso div.form div.error label,
.bootstrap-iso div.form label.error,
.bootstrap-iso div.form span.error {
    color: #C00;
}
.bootstrap-iso div.form div.error input,
.bootstrap-iso div.form div.error textarea,
.bootstrap-iso div.form div.error select,
.bootstrap-iso div.form input.error,
.bootstrap-iso div.form textarea.error,
.bootstrap-iso div.form select.error {
    background: #FEE;
    border-color: #C00;
}
.bootstrap-iso ul.yiiPager {
    font-size: 11px;
    border: 0;
    line-height: 100%;
    display: inline;
    margin: 0;
    padding: 0;
}
.bootstrap-iso ul.yiiPager li {
    display: inline;
}
.bootstrap-iso ul.yiiPager a:link,
.bootstrap-iso ul.yiiPager a:visited {
    border: solid 1px #9aafe5;
    font-weight: 700;
    color: #0e509e;
    text-decoration: none;
    padding: 1px 6px;
}
.bootstrap-iso ul.yiiPager .page a {
    font-weight: 400;
}
.bootstrap-iso ul.yiiPager a:hover {
    border: solid 1px #0e509e;
}
.bootstrap-iso ul.yiiPager .selected a {
    background: #2e6ab1;
    color: #FFF;
    font-weight: 700;
}
.bootstrap-iso ul.yiiPager .hidden a {
    border: solid 1px #DEDEDE;
    color: #888;
}

.bootstrap-iso .error_span {
    background-position: 0 -23px;
}
.bootstrap-iso .displayResult {
    height: 320px;
    overflow: hidden;
    display: block;
    overflow-y: scroll;
    font-size: 12px;
    outline: none;
    margin: 0 auto;
    padding: 10px;
}
.bootstrap-iso .displayResult ul {
    line-height: 1.8;
}
.bootstrap-iso html,
.bootstrap-iso body,
.bootstrap-iso div,
.bootstrap-iso dl,
.bootstrap-iso dt,
.bootstrap-iso dd,
.bootstrap-iso ul,
.bootstrap-iso p,
.bootstrap-iso th,
.bootstrap-iso td,
.bootstrap-iso h1,
.bootstrap-iso h2,
.bootstrap-iso h3,
.bootstrap-iso h4,
.bootstrap-iso h5,
.bootstrap-iso h6,
.bootstrap-iso pre,
.bootstrap-iso code,
.bootstrap-iso form,
.bootstrap-iso fieldset,
.bootstrap-iso legend,
.bootstrap-iso .pop_design_tablelist th input {
    margin: 0;
    padding: 0;
}
.bootstrap-iso article,
.bootstrap-iso aside,
.bootstrap-iso details,
.bootstrap-iso figcaption,
.bootstrap-iso figure,
.bootstrap-iso footer,
.bootstrap-iso header,
.bootstrap-iso hgroup,
.bootstrap-iso nav,
.bootstrap-iso section,
.bootstrap-iso .shift .select_div dl.current dd,
.bootstrap-iso .search_photo_list li label,
.bootstrap-iso .design_page li img {
    display: block;
}
.bootstrap-iso .cc:after,
.bootstrap-iso .user_group dd:after,
.bootstrap-iso .search_type .ul_wrap:after,
.bootstrap-iso .home_info li:after {
    content: '\20';
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
}
.bootstrap-iso .p5,
.bootstrap-iso .pop_region_list .list ul {
    padding: 5px;
}
.bootstrap-iso .p10,
.bootstrap-iso .search_type,
.bootstrap-iso .design_ct .pop_design_code {
    padding: 10px;
}
.bootstrap-iso .p20,
.bootstrap-iso .wrap_pop {
    padding: 20px;
}
.bootstrap-iso .w,
.bootstrap-iso .table_list thead label {
    white-space: nowrap;
}
.bootstrap-iso .vt,
.bootstrap-iso .search_type li select {
    vertical-align: top;
}
.bootstrap-iso .dn,
.bootstrap-iso .shift .select_div dl dd,
.bootstrap-iso ul.yiiPager .first,
.bootstrap-iso ul.yiiPager .last {
    display: none;
}
.bootstrap-iso .f14,
.bootstrap-iso .select_pages a,
.bootstrap-iso .search_list dt {
    font-size: 14px;
}
.bootstrap-iso .green,
.bootstrap-iso input:checked + span,
.bootstrap-iso input:radio + span {
    color: #080!important;
}
.bootstrap-iso .placeholder,
.bootstrap-iso .about_list dd p {
    color: #999;
}
.bootstrap-iso .length_1,
.bootstrap-iso .sql_list .span_1 {
    width: 50px;
}
.bootstrap-iso .length_3,
.bootstrap-iso .sql_list .span_3 {
    width: 170px;
}
.bootstrap-iso .length_6,
.bootstrap-iso .pop_stats {
    width: 350px;
}
.bootstrap-iso .select_1,
.bootstrap-iso .widget_history_file dd.num {
    width: 60px;
}
.bootstrap-iso .select_2,
.bootstrap-iso .cross li .span_3,
.bootstrap-iso .pop_stats thead th {
    width: 120px;
}
.bootstrap-iso .select_3,
.bootstrap-iso .cross li .span_4 {
    width: 180px;
}
.bootstrap-iso .select_5,
.bootstrap-iso .sql_list .span_2,
.bootstrap-iso .color_pick_dom {
    width: 300px;
}
.bootstrap-iso .select_6,
.bootstrap-iso .design_ct dd .three_list {
    width: 360px;
}
.bootstrap-iso .tips_bubble .core_arrow_bottom span,
.bootstrap-iso .tips_bubble .core_arrow_bottom em,
.bootstrap-iso .pop_expand .core_arrow_bottom span,
.bootstrap-iso .pop_expand .core_arrow_bottom em {
    position: absolute;
    width: 0;
    height: 0;
    border-bottom: 8px transparent dashed;
    border-right: 8px transparent dashed;
    border-left: 8px transparent dashed;
    overflow: hidden;
    margin: 9px 0 0 2px;
    padding: 0;
}
.bootstrap-iso .pop_table tr:hover th,
.bootstrap-iso .table_full tr:hover .fun_tips,
.bootstrap-iso .prompt_text:hover {
    color: #000;
}
.bootstrap-iso .pop_close:hover,
.bootstrap-iso .pop_seo .close:hover {
    background-position: 0 -8px;
}
.bootstrap-iso body.body_none,
.bootstrap-iso .pop_design_case .thbg {
    background: #fff;
}
.bootstrap-iso a:hover,
.bootstrap-iso .system_update a:hover {
    text-decoration: underline;
}
.bootstrap-iso .nav li,
.bootstrap-iso .nav .return,
.bootstrap-iso #branch-selector,
.bootstrap-iso .nav_minor li,
.bootstrap-iso .sql_list span,
.bootstrap-iso .yarnball ul li {
    float: left;
}
.bootstrap-iso .nav_minor li a:hover,
.bootstrap-iso .locate li a:hover {
    text-decoration: none;
    background: #e4e4e4;
}
.bootstrap-iso .table_full,
.bootstrap-iso .medal_term .hd,
.bootstrap-iso .CodeMirror {
    margin-bottom: 10px;
}
.bootstrap-iso .double_list li.all_check,
.bootstrap-iso .app_info li.li,
.bootstrap-iso .pop_advanced_search li.all {
    width: 100%;
}
.bootstrap-iso .user_group dl,
.bootstrap-iso .sql_list dl {
    border-top: 1px solid #ccc;
    position: relative;
    margin-top: -1px;
}
.bootstrap-iso .sql_list dd p:hover,
.bootstrap-iso .pop_region_list .list li:hover,
.bootstrap-iso .pop_stats thead td,
.bootstrap-iso .pop_stats thead th,
.bootstrap-iso .widget_update_log thead th,
.bootstrap-iso .widget_update_log thead td {
    background: #f7f7f7;
}
.bootstrap-iso .task_item_list .hd a:hover,
.bootstrap-iso .yarnball ul li a:hover {
    text-decoration: none;
}
.bootstrap-iso .cross li .span_2,
.bootstrap-iso .widget_history_file dd.time {
    width: 80px;
}
.bootstrap-iso .locate li.current a,
.bootstrap-iso .pop_region_list ul li.current a,
.bootstrap-iso .pop_region_list ul li.current span {
    background: #266aae;
    color: #fff;
}
.bootstrap-iso .pop_region_list,
.bootstrap-iso .app_info ul {
    padding-bottom: 10px;
}
.bootstrap-iso .pop_region_list .filter,
.bootstrap-iso .agreements {
    padding: 10px 0;
}
.bootstrap-iso .app_info,
.bootstrap-iso .app_present,
.bootstrap-iso .app_updata {
    border: 1px solid #e4e4e4;
    background: #fff;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}
.bootstrap-iso .app_info .hd,
.bootstrap-iso .app_present .hd,
.bootstrap-iso .app_updata .hd {
    padding: 8px 15px;
}
.bootstrap-iso .app_info .ct,
.bootstrap-iso .app_present .ct,
.bootstrap-iso .app_updata .ct {
    padding: 8px 15px 20px;
}
.bootstrap-iso .design_ct dl,
.bootstrap-iso .pop_design_tablelist td,
.bootstrap-iso .pop_design_tablelist th {
    padding: 7px 0;
}
