<?php

/**
 * This is the model class for table "ivy_learning_health".
 *
 * The followings are the available columns in table 'ivy_learning_health':
 * @property integer $id
 * @property integer $yid
 * @property integer $semester
 * @property integer $childid
 * @property integer $classid
 * @property integer $img1
 * @property integer $img2
 * @property integer $img3
 * @property string $content1
 * @property string $content2
 * @property integer $status
 * @property integer $created_at
 * @property integer $created_by
 * @property integer $updated_at
 * @property integer $updated_by
 */
class LearningHealth extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_learning_health';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('yid, semester, childid, classid, status, created_at, created_by, updated_at, updated_by', 'required'),
			array('yid, semester, childid, classid, img1, img2, img3, status, created_at, created_by, updated_at, updated_by', 'numerical', 'integerOnly'=>true),
			array('content1, content2', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, yid, semester, childid, classid, img1, img2, img3, content1, content2, status, created_at, created_by, updated_at, updated_by', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'yid' => 'Yid',
			'semester' => 'Semester',
			'childid' => 'Childid',
			'classid' => 'Classid',
			'img1' => 'Img1',
			'img2' => 'Img2',
			'img3' => 'Img3',
			'content1' => 'Content1',
			'content2' => 'Content2',
			'status' => 'Status',
			'created_at' => 'Created At',
			'created_by' => 'Created By',
			'updated_at' => 'Updated At',
			'updated_by' => 'Updated By',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('semester',$this->semester);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('img1',$this->img1);
		$criteria->compare('img2',$this->img2);
		$criteria->compare('img3',$this->img3);
		$criteria->compare('content1',$this->content1,true);
		$criteria->compare('content2',$this->content2,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('created_at',$this->created_at);
		$criteria->compare('created_by',$this->created_by);
		$criteria->compare('updated_at',$this->updated_at);
		$criteria->compare('updated_by',$this->updated_by);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return LearningHealth the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
