<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
    </button>
    <h4 class="modal-title" id="exampleModalLabel"><?php echo $model->groupid ? '更新：' : '添加：'; ?></h4>
</div>
<?php $form = $this->beginWidget('CActiveForm', array(
    'id' => 'protocol-form',
    'enableAjaxValidation' => false,
    'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form'),
)); ?>
<div class="modal-body">
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'content_cn'); ?></label>

        <div class="col-xs-9">
            <?php echo $form->textArea($model, 'content_cn', array('class' => 'form-control', 'rows' => 6)); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'content_en'); ?></label>

        <div class="col-xs-9">
            <?php echo $form->textArea($model, 'content_en', array('class' => 'form-control', 'rows' => 6)); ?>
        </div>
    </div>
    <div class="form-group">
        <label class="col-xs-2 control-label"><?php echo $form->labelEx($model, 'status'); ?></label>

        <div class="col-xs-9">
            <?php echo $form->dropDownList($model, 'status', array(1 => '有效', 0 => '无效'), array('class' => 'form-control')); ?>
        </div>
    </div>
</div>
<div class="modal-footer">
    <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global', 'Submit'); ?></button>
    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t('global', 'Cancel'); ?></button>
</div>
<?php $this->endWidget(); ?>

<script>
    function cbAgreement() {
        var groupId = '<?php echo $model->groupid; ?>';
        $('#modal').modal('hide');
        window.location.reload(true);
    }
</script>
