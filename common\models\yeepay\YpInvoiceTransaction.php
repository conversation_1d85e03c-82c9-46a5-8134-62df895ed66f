<?php

/**
 * This is the model class for table "ivy_yp_invoice_transaction".
 *
 * The followings are the available columns in table 'ivy_yp_invoice_transaction':
 * @property string $orderId
 * @property double $fact_amount
 * @property double $payable_amount
 * @property string $r2_TrxId
 * @property string $r7_Uid
 * @property string $rb_BankId
 * @property string $ro_BankOrderId
 * @property string $rp_PayDate
 * @property string $schoolId
 * @property integer $childId
 * @property integer $operatorId
 * @property integer $payment_method
 * @property integer $status
 * @property integer $orderTime
 * @property integer $updateTime
 * @property string $payed_status
 */
class YpInvoiceTransaction extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return WYpInvoiceTransaction the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_yp_invoice_transaction';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
				array('orderId, payable_amount', 'required'),
				array('childId, operatorId, payment_method, status, orderTime, updateTime', 'numerical', 'integerOnly'=>true),
				array('fact_amount, payable_amount', 'numerical'),
				array('orderId, r2_TrxId, r7_Uid, rb_BankId', 'length', 'max'=>50),
				array('ro_BankOrderId', 'length', 'max'=>100),
				array('rp_PayDate', 'length', 'max'=>20),
				array('schoolId', 'length', 'max'=>15),
				array('payed_status', 'length', 'max'=>2),
				// The following rule is used by search().
				// Please remove those attributes that should not be searched.
				array('orderId, fact_amount, payable_amount, r2_TrxId, r7_Uid, rb_BankId, ro_BankOrderId, rp_PayDate, schoolId, childId, operatorId, payment_method, status, orderTime, updateTime, payed_status', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}
	/**
	 * 获取订单后缀
	 * @param   $orderPrefix 订单前缀
	 * @return boolean
	 */
	public function getPostfix($orderPrefix) {
		$postfix = 0;
		$fix_cri = new CDbCriteria();
		$fix_cri->addCondition("`orderId` like '".$orderPrefix."%'");
		$fix_cri->order = 'orderId desc';
		$order = $this->find($fix_cri);
		if(!empty($order)){
			$id = substr($order->orderId,9,3);
			$postfix = intval($id)+1;
		}else{
			$postfix = 1;
		}
		return sprintf("%03s\n",$postfix);
	}
	
	/**
	 * 在将数据提交到易宝之前 记录账单信息
	 * @param array $orderInfo InvoiceTransaction 需要插入的信息
	 * @param array $detailInfo TransactionDetail 需要插入的信息
	 * @param array $due_amount 支付的账单及应付金额
	 * @return boolean
	 * <AUTHOR>
	 * @time 2012-7-19
	 */
	public function insertOrderAndDetail($orderInfo,$detailInfo,$due_amount) {
		
		$execute_failed = false;
		$yp_trans= $this->dbConnection->beginTransaction();
		$it_model = new YpInvoiceTransaction();
		$it_model->attributes = $orderInfo;
		try {
			if($it_model->save()){
				$td_model = new YpTransactionDetail();
				$td_model->attributes = $detailInfo;
				foreach ($due_amount as $invoice_id => $amount) {
					$td_model->invocieId = $invoice_id;
					$td_model->payable_amount = $amount;
					if(!$td_model->save()){
						$execute_failed = true;
						break;
					}
					$td_model->setIsNewRecord(true);
					$td_model->detailId = null;
				}
			}else{
				$execute_failed = true;
			}
		} catch (Exception $e) {
			$execute_failed = true;
		}
		
		if (true == $execute_failed) {
			// 回滚
			$yp_trans->rollback();
		} else {
			// 提交
			$yp_trans->commit();
		}
		
		return !$execute_failed;
		
	}
	
	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
				'orderId' => 'Order',
				'fact_amount' => 'Actual Amount',
				'payable_amount' => 'Payable Amount',
				'r2_TrxId' => 'R2 Trx',
				'r7_Uid' => 'R7 Uid',
				'rb_BankId' => 'Rb Bank',
				'ro_BankOrderId' => 'Ro Bank Order',
				'rp_PayDate' => 'Rp Pay Date',
				'schoolId' => 'Campus',
				'childId' => 'Child',
				'operatorId' => 'Operator',
				'payment_method' => 'Payment Method',
				'status' => 'Status',
				'orderTime' => 'Order Time',
				'updateTime' => 'Update Time',
				'payed_status' => 'Payment Status',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('orderId',$this->orderId,true);
		$criteria->compare('fact_amount',$this->fact_amount);
		$criteria->compare('payable_amount',$this->payable_amount);
		$criteria->compare('r2_TrxId',$this->r2_TrxId,true);
		$criteria->compare('r7_Uid',$this->r7_Uid,true);
		$criteria->compare('rb_BankId',$this->rb_BankId,true);
		$criteria->compare('ro_BankOrderId',$this->ro_BankOrderId,true);
		$criteria->compare('rp_PayDate',$this->rp_PayDate,true);
		$criteria->compare('schoolId',$this->schoolId,true);
		$criteria->compare('childId',$this->childId);
		$criteria->compare('operatorId',$this->operatorId);
		$criteria->compare('payment_method',$this->payment_method);
		$criteria->compare('status',$this->status);
		$criteria->compare('orderTime',$this->orderTime);
		$criteria->compare('updateTime',$this->updateTime);
		$criteria->compare('payed_status',$this->payed_status,true);

		return new CActiveDataProvider($this, array(
				'criteria'=>$criteria,
		));
	}
}