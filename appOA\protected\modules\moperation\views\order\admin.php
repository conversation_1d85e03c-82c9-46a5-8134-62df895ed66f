<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site','HQ Operations'), array('/moperation'))?></li>
        <li><?php echo CHtml::link(Yii::t('site','Support'), array('default/index'));?></li>
        <li class="active"><?php echo Yii::t('site','Points Exchange Order') ?></li>
    </ol>

    <div class="row">
        <div class="col-md-12">
            <div class="form-inline J_ce">
                <p><?php echo Yii::t('site', 'HQ initiated'); ?></p>
                <?php
                $statusList = PointsOrder::model()->statusList();
                foreach(array(PointsStatus::STATS_CREATED, PointsStatus::STATS_CONFIRMED, PointsStatus::STATS_READYTOSHIP) as $stat):
                    ?>
                    <?php echo CHtml::link($statusList[$stat].'(<span>'.count(${'sData'.$stat}).'</span>)', '#', array('class'=>'btn btn-default mb5 form-control', 'rel'=>'s'.$stat));?>
                <?php endforeach;?>
            </div>
        </div>
        <div class="col-md-12">
            <div class="form-inline J_ce">
                <p><?php echo Yii::t('site', 'Campus initiated'); ?></p>
                <?php foreach(array(PointsStatus::STATS_SHIPPING, PointsStatus::STATS_RECEIVED) as $stat):?>
                    <?php echo CHtml::link($statusList[$stat].'(<span>'.count(${'sData'.$stat}).'</span>)', '#', array('class'=>'btn btn-default mb5 form-control', 'rel'=>'s'.$stat));?>
                <?php endforeach;?>
                <?php echo CHtml::link($statusList[PointsStatus::STATS_COMPLETED], '#', array('class'=>'btn btn-default mb5 form-control', 'rel'=>'s210'));;?>
            </div>
        </div>
        <div class="col-md-12 fmain">
            <?php foreach ($statusList as $s=>$v):?>
                <div id="s<?php echo $s?>" class="J_wr" style="display: none;">
                    <?php $this->renderPartial('moperation.views.order.orderview'.$s, array('sData'=>${'sData'.$s}, 'branchId'=>$branchId, 'v'=>$v, 's'=>$s))?>
                </div>
            <?php endforeach;?>
        </div>
    </div>
</div>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>

<script type="text/javascript">
    function pack(_this)
    {
        $(_this).attr('disabled', 'disabled');
        $.ajax({
            'type': 'post',
            'url': $(_this.form).attr('action'),
            'data': $(_this.form).serialize(),
            'dataType': 'json',
            'success': function(data){
                if (data.status == 'success'){
                    $.fn.yiiGridView.update("way_list");
                    $.fn.yiiGridView.update("ready_list");
                    $('#mydialog').dialog('close');
                }
            }
        });
    }
    $('.fmain').on('click', '.split', function(){
        if (confirm('确定拆分此包吗？')){
            $.get($(this).attr('href'), null, function(data){
                if (data.status == 'success'){
                    reloadPage(window)
                }
            }, 'json')
        }
        return false;
    });
    $('.fmain').on('click', '.receive', function(){
        if (confirm('您确定已经收到此包了？')){
            $.get($(this).attr('href'), null, function(data){
                if (data.status == 'success'){
                    reloadPage(window)
                }
            }, 'json')
        }
        return false;
    });
    $('.fmain').on('click', '.godone', function(){
        if (confirm('您确定直接发放次订单吗？')){
            $.get($(this).attr('href'), null, function(data){
                if (data.state == 'success'){
                    reloadPage(window)
                }
            }, 'json')
        }
        return false;
    });
    $('.J_ce a.btn').on('click', function(){
        $('.fmain div.J_wr').each(function(){
            $(this).hide();
        });
        $('#'+$(this).attr('rel')).show();
        return false;
    })
    $(document).ready(function(){
        var schoolsCount = <?php echo $schoolsCount;?>;
        for(var schoolid in schoolsCount){
            $('#branch-selector div.list a#school_'+schoolid).append('<i class="extra-number">'+schoolsCount[schoolid]+'</i>');
        }
    })
</script>

<style type="text/css">
    fieldset{
        border: 3px solid #E5F1F4;
        margin-bottom: 20px;
    }
    fieldset legend{
        margin-left: 20px;
        font-weight: bold;
    }
    fieldset div.fmain{
        padding: 0 20px;
    }
</style>