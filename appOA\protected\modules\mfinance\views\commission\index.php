<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo Yii::t('site','Finance Management');?></li>
        <li class="active">手续费查询</li>
    </ol>
    <form action="<?php echo $this->createUrl('query')?>" class="form-inline mb15 J_ajaxForm" method="post">
        <?php echo CHtml::dropDownList('schoolid', '', $branchs, array('class'=>'form-control form-group', 'empty'=>Yii::t('global', 'All')));?>
        <?php
        $this->widget('zii.widgets.jui.CJuiDatePicker',array(
            'name'=>'startdate',
            'options'=>array(
                'dateFormat'=>'yy-mm-dd',
            ),
            'htmlOptions'=>array(
                'class'=>'form-control form-group',
                'placeholder'=>'开始日期',
            ),
        ));
        ?>
        <?php
        $this->widget('zii.widgets.jui.CJuiDatePicker',array(
            'name'=>'enddate',
            'options'=>array(
                'dateFormat'=>'yy-mm-dd',
            ),
            'htmlOptions'=>array(
                'class'=>'form-control form-group',
                'placeholder'=>'结束日期',
            ),
        ));
        ?>
        <?php echo CHtml::dropDownList('type', '', array('alipay'=>'支付宝', 'allinpay'=>'通联', 'yeepay'=>'易宝'), array('class'=>'form-control form-group', 'empty'=>Yii::t('global', 'Please Select')))?>
        <button class="btn btn-primary J_ajax_submit_btn">查询</button>
    </form>
    <div class="row hide" id="viewData">
        <div class="col-md-12">
            <table class="table table-bordered table-hover">
                <thead>
                <tr>
                    <th style="width: 25%;">校园</th>
                    <th style="width: 25%;">收费金额</th>
                    <th style="width: 25%;">费率</th>
                    <th style="width: 25%;">手续费</th>
                </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script type="text/template" id="feerate-template">
    <tr>
        <td><%= title%></td>
        <td class="text-right"><%= total%></td>
        <td class="text-right"><%= feerate%></td>
        <td class="text-right"><%= commission%></td>
    </tr>
</script>

<script>
    function callback(data)
    {
        var html = '';
        $('#viewData table tbody').html(html);
        var feerateTemplate = $('#feerate-template').html();
        for(var datum in data){
            html += _.template(feerateTemplate, data[datum]);
        }
        $('#viewData table tbody').html(html);
        $('#viewData').removeClass('hide');
    }
</script>