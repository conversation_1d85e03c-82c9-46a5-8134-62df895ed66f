<?php
class YeepayCommon {

    #学校ID
    private $schoolId = null;
    
    #商户编号
    private $p1_MerId = "";
    
    #商户密钥
    private $merchantKey = "";
    
    #日志文件的地址
    private $logPath = "";
    
    #日志文件的名称
    private $logName = "";
    
    #正式请求地址
    private $reqURL_onLine = "";
    
    #业务类型 固定值“Buy”
    private $p0_Cmd = "Buy";
    
    #交易币种 固定值 ”CNY”.
    private $p4_Cur = "CNY";
    
    #送货地址 “0”: 不需要，默认为 ”0”.
    private $p9_SAF = "0";
    
    #商户接收支付成功数据的地址
    private $p8_Url = "";
    
    #应答机制 固定值为“1”: 需要应答机制
    private $pr_NeedResponse = 1;
    
    private $xoops_var_path = "";
    
    private $xoops_url = "";

    function __construct($schoolid = null) {

        $this->xoops_var_path = Yii::app()->params['xoopsVarPath'];
        $this->xoops_url = Yii::app()->params['YeepayRespondeUrl'];
        $this->logPath = $this->xoops_var_path . "/logs/payment/yeepay/";
    }

    public function init($schoolid = null) {

        $this->schoolId = $schoolid;
        $yeepayMeridInfo = CommonUtils::LoadConfig('CfgYeePayGlobal');

        if (isset($yeepayMeridInfo[$this->schoolId])) {
            $this->p1_MerId = $yeepayMeridInfo[$this->schoolId]['p1_MerId'];
            $this->merchantKey = $yeepayMeridInfo[$this->schoolId]['merchantKey'];
        }
        $this->logName = $this->logPath . "YeePay_" . date("Ymd", time()) . ".log";
        $this->reqURL_onLine = "https://www.yeepay.com/app-merchant-proxy/node";
        $langKey = Yii::app()->language == 'en_us' ? 'english' : 'schinese_utf8';
        $this->p8_Url = $this->xoops_url;
    }

    /*
     * 测试
     */
    public function initTest($schoolid = null) {

        $this->schoolId = $schoolid;
        $this->p1_MerId = "10001126856";
        $this->merchantKey = "69cl522AV6q613Ii4W6u8K6XuW8vM1N6bFgyv769220IuYe9u37N4y7rI4Pl";
        $this->logName = $this->logPath . "YeePay_" . date("Ymd", time()) . ".log";
        $this->reqURL_onLine = "http://tech.yeepay.com:8080/robot/debug.action";
        $langKey = Yii::app()->language == 'en_us' ? 'english' : 'schinese_utf8';
        $this->p8_Url = $this->xoops_url;
    }

    #签名函数生成签名串
    public function getReqHmacString($p2_Order, $p3_Amt, $p5_Pid, $p6_Pcat, $p7_Pdesc, $pa_MP, $pd_FrpId) {

        #进行签名处理，一定按照文档中标明的签名顺序进行
        $sbOld = "";
        #加入业务类型
        $sbOld = $sbOld . $this->p0_Cmd;
        #加入商户编号
        $sbOld = $sbOld . $this->p1_MerId;
        #加入商户订单号
        $sbOld = $sbOld . $p2_Order;
        #加入支付金额
        $sbOld = $sbOld . $p3_Amt;
        #加入交易币种
        $sbOld = $sbOld . $this->p4_Cur;
        #加入商品名称
        $sbOld = $sbOld . $p5_Pid;
        #加入商品分类
        $sbOld = $sbOld . $p6_Pcat;
        #加入商品描述
        $sbOld = $sbOld . $p7_Pdesc;
        #加入商户接收支付成功数据的地址
        $sbOld = $sbOld . $this->p8_Url;
        #加入送货地址标识
        $sbOld = $sbOld . $this->p9_SAF;
        #加入商户扩展信息
        $sbOld = $sbOld . $pa_MP;
        #加入支付通道编码
        $sbOld = $sbOld . $pd_FrpId;
        #加入是否需要应答机制
        $sbOld = $sbOld . $this->pr_NeedResponse;

        $hmac = $this->HmacMd5($sbOld, $this->merchantKey);
        $request_info = array(
            "schoolid" => $this->schoolId,
            "p0_Cmd" => $this->p0_Cmd,
            "p1_MerId" => $this->p1_MerId,
            "merchantKey" => $this->merchantKey,
            "p2_Order" => $p2_Order,
            "p3_Amt" => $p3_Amt,
            "p4_Cur" => $this->p4_Cur,
            "p5_Pid" => $p5_Pid,
            "p6_Pcat" => $p6_Pcat,
            "p7_Pdesc" => $p7_Pdesc,
            "p8_Url" => $this->p8_Url,
            "pa_MP" => $pa_MP,
            "pd_FrpId" => $pd_FrpId,
            "pr_NeedResponse" => $this->pr_NeedResponse,
            "hmac" => $hmac
        );
        $this->logstr($this->logName, $request_info, $type = "request");
        return $hmac;
    }

    public function HmacMd5($data, $key) {
        // RFC 2104 HMAC implementation for php.
        // Creates an md5 HMAC.
        // Eliminates the need to install mhash to compute a HMAC
        // Hacked by Lance Rushing(NOTE: Hacked means written)
        //需要配置环境支持iconv，否则中文参数不能正常处理
        $key = iconv("GB2312", "UTF-8", $key);
        $data = iconv("GB2312", "UTF-8", $data);

        $b = 64; // byte length for md5
        if (strlen($key) > $b) {
            $key = pack("H*", md5($key));
        }
        $key = str_pad($key, $b, chr(0x00));
        $ipad = str_pad('', $b, chr(0x36));
        $opad = str_pad('', $b, chr(0x5c));
        $k_ipad = $key ^ $ipad;
        $k_opad = $key ^ $opad;

        return md5($k_opad . pack("H*", md5($k_ipad . $data)));
    }

    public function logstr($logName, $info, $type = "response1") {
        $james = fopen($logName, "a+");
        if ("response1" == $type || "response2" == $type) {
            $orderid = $info['r6_Order'];
        } else {
            $orderid = $info['p2_Order'];
        }
        $writeStr = date("Y-m-d H:i:s") . "|type[" . $type . "]|orderid[" . $orderid . "]";
        if (is_array($info)) {
            foreach ($info as $key => $value) {
                $writeStr.="|" . $key . "[" . $value . "]";
            }
        }
        $writeStr .= "\r\n";

        fwrite($james, $writeStr);
        fclose($james);
    }

    public function logCustomStr($logName=null, $str, $type = "custom") {
        $logName = is_null($logName) ? $this->logPath.'/YeePay_RawPost_'.date('Ymd').'.log' : $logName;
        $james = fopen($logName, "a+");
        
        $writeStr = date("Y-m-d H:i:s") . "|type[" . $type . "]|string[" . $str . "]";
        $writeStr .= "\r\n";

        fwrite($james, $writeStr);
        fclose($james);
    }
    
    #	取得返回串中的所有参数
    public function getCallBackValue(&$p1_MerId,&$r0_Cmd,&$r1_Code,&$r2_TrxId,&$r3_Amt,&$r4_Cur,&$r5_Pid,&$r6_Order,&$r7_Uid,&$r8_MP,&$r9_BType,&$rb_BankId, &$ro_BankOrderId, &$rp_PayDate, &$rq_CardNo,&$ru_Trxtime,&$hmac)
    {
        $p1_MerId	= $_REQUEST['p1_MerId'];
        $r0_Cmd		= $_REQUEST['r0_Cmd'];
        $r1_Code	= $_REQUEST['r1_Code'];
        $r2_TrxId	= $_REQUEST['r2_TrxId'];
        $r3_Amt		= $_REQUEST['r3_Amt'];
        $r4_Cur		= $_REQUEST['r4_Cur'];
        $r5_Pid		= $_REQUEST['r5_Pid'];
        $r6_Order	= $_REQUEST['r6_Order'];
        $r7_Uid		= $_REQUEST['r7_Uid'];
        $r8_MP		= $_REQUEST['r8_MP'];
        $r9_BType	= $_REQUEST['r9_BType'];
        $rb_BankId	= $_REQUEST['rb_BankId'];
        $ro_BankOrderId	= $_REQUEST['ro_BankOrderId'];
        $rp_PayDate	= $_REQUEST['rp_PayDate'];
        $rq_CardNo	= $_REQUEST['rq_CardNo'];
        $ru_Trxtime	= $_REQUEST['ru_Trxtime'];
        $hmac       = $_REQUEST['hmac'];

        return null;
    }
    
    public function getCallbackHmacString($p1_MerId,$r0_Cmd,$r1_Code,$r2_TrxId,$r3_Amt,$r4_Cur,$r5_Pid,$r6_Order,$r7_Uid,$r8_MP,$r9_BType)
    {
        #取得加密前的字符串
        $sbOld = "";
        #加入商家ID
        $sbOld = $sbOld.$p1_MerId;
        #加入消息类型
        $sbOld = $sbOld.$r0_Cmd;
        #加入业务返回码
        $sbOld = $sbOld.$r1_Code;
        #加入交易ID
        $sbOld = $sbOld.$r2_TrxId;
        #加入交易金额
        $sbOld = $sbOld.$r3_Amt;
        #加入货币单位
        $sbOld = $sbOld.$r4_Cur;
        #加入产品Id
        $sbOld = $sbOld.$r5_Pid;
        #加入订单ID
        $sbOld = $sbOld.$r6_Order;
        #加入用户ID
        $sbOld = $sbOld.$r7_Uid;
        #加入商家扩展信息
        $sbOld = $sbOld.$r8_MP;
        #加入交易结果返回类型
        $sbOld = $sbOld.$r9_BType;

        $this->logstr($this->logName, array('r0_Cmd'=>$r0_Cmd,'r1_Code'=>$r1_Code,'r2_TrxId'=>$r2_TrxId,'r3_Amt'=>$r3_Amt,'r4_Cur'=>$r4_Cur,'r5_Pid'=>$r5_Pid,'r6_Order'=>$r6_Order,'r7_Uid'=>$r7_Uid,'r8_MP'=>$r8_MP,'r9_BType'=>$r9_BType), 'response2');
        $valid = $this->HmacMd5($sbOld, $this->merchantKey);
        $this->logCustomStr(null,  $valid? "valid request" : "invliad request");
        return $valid;
    }
    
    public function CheckHmac($p1_MerId,$r0_Cmd,$r1_Code,$r2_TrxId,$r3_Amt,$r4_Cur,$r5_Pid,$r6_Order,$r7_Uid,$r8_MP,$r9_BType,$hmac)
    {
        if($hmac==$this->getCallbackHmacString($p1_MerId,$r0_Cmd,$r1_Code,$r2_TrxId,$r3_Amt,$r4_Cur,$r5_Pid,$r6_Order,$r7_Uid,$r8_MP,$r9_BType))
        return true;
        else
        return false;
    }

    public function getP0Cmd() {
        return $this->p0_Cmd;
    }

    public function getP1MerId() {
        return $this->p1_MerId;
    }

    public function getP4Cur() {
        return $this->p4_Cur;
    }

    public function getP8Url() {
        return $this->p8_Url;
    }

    public function getP9Saf() {
        return $this->p9_SAF;
    }

    public function getPrNeedResponse() {
        return $this->pr_NeedResponse;
    }

    public function getReqUrlOnline() {
        return $this->reqURL_onLine;
    }
}
