<?php

/**
 * This is the model class for table "ivy_child_credit".
 *
 * The followings are the available columns in table 'ivy_child_credit':
 * @property integer $cid
 * @property integer $classid
 * @property string $schoolid
 * @property integer $yid
 * @property integer $childid
 * @property double $amount
 * @property string $inout
 * @property string $itemname
 * @property integer $invoice_id
 * @property integer $transaction_id
 * @property integer $userid
 * @property integer $updated_timestamp
 * @property integer $transfer_to_uid
 * @property integer $transfer_timestamp
 * @property integer $transactiontype
 * @property double $balance
 * @property integer $cash_status
 */
class ChildCredit extends CActiveRecord
{
	/**
	 * Returns the static model of the specified AR class.
	 * @param string $className active record class name.
	 * @return ChildCredit the static model class
	 */
	const TYPE_CASHOUT = 'cash'; //提现类型
	const TYPE_TRANSFER = 'transfer'; //转移类型
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_child_credit';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('classid, schoolid, yid, childid, amount, inout, userid, updated_timestamp', 'required'),
			array('classid, yid, childid, invoice_id, transaction_id, userid, updated_timestamp, transfer_to_uid, transfer_timestamp, transactiontype, cash_status', 'numerical', 'integerOnly'=>true),
			array('amount, balance', 'numerical'),
			array('schoolid', 'length', 'max'=>11),
			array('inout', 'length', 'max'=>3),
			array('itemname', 'length', 'max'=>100),
			// The following rule is used by search().
			// Please remove those attributes that should not be searched.
			array('cid, classid, schoolid, yid, childid, amount, inout, itemname, invoice_id, transaction_id, userid, updated_timestamp, transfer_to_uid, transfer_timestamp, transactiontype, balance, cash_status', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			'refundlunch'=>array(self::HAS_MANY, 'RefundLunch', 'child_credit_id','select'=>'target_date,target_timestamp'),
            'transaction' => array(self::BELONGS_TO, 'InvoiceTransaction', 'transaction_id'),
		);
	}

	public function scopes()
	{
		return array(
			'lunch' => array(
				'select' =>'childid'
			)
		);
	}
	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'cid' => 'Cid',
			'classid' => 'Classid',
			'schoolid' => 'Schoolid',
			'yid' => 'Yid',
			'childid' => 'Childid',
			'amount' => Yii::t("payment", 'Amount'),
			'inout' => 'Inout',
			'itemname' => Yii::t("payment", 'Program Name'),
			'invoice_id' => 'Invoice',
			'transaction_id' => 'Transaction',
			'userid' => 'Userid',
			'updated_timestamp' => Yii::t("payment", 'Transaction Time'),
			'transfer_to_uid' => 'Transfer To Uid',
			'transfer_timestamp' => 'Transfer Timestamp',
			'transactiontype' => 'Transactiontype',
			'balance' => 'Balance',
			'cash_status' => 'Cash Status',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 * @return CActiveDataProvider the data provider that can return the models based on the search/filter conditions.
	 */
	public function search()
	{
		// Warning: Please modify the following code to remove attributes that
		// should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('cid',$this->cid);
		$criteria->compare('classid',$this->classid);
		$criteria->compare('schoolid',$this->schoolid,true);
		$criteria->compare('yid',$this->yid);
		$criteria->compare('childid',$this->childid);
		$criteria->compare('amount',$this->amount);
		$criteria->compare('inout',$this->inout,true);
		$criteria->compare('itemname',$this->itemname,true);
		$criteria->compare('invoice_id',$this->invoice_id);
		$criteria->compare('transaction_id',$this->transaction_id);
		$criteria->compare('userid',$this->userid);
		$criteria->compare('updated_timestamp',$this->updated_timestamp);
		$criteria->compare('transfer_to_uid',$this->transfer_to_uid);
		$criteria->compare('transfer_timestamp',$this->transfer_timestamp);
		$criteria->compare('transactiontype',$this->transactiontype);
		$criteria->compare('balance',$this->balance);
		$criteria->compare('cash_status',$this->cash_status);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}
	
	
	public function renderInout($template=null){
		//to do , parse template
		$title = ($this->inout == "in") ? Yii::t("payment", "Refund to general credit") : Yii::t("payment", "Use general credit");
		return sprintf("<em class='inout-icon-%s' title='%s'></em>", $this->inout, $title);
	}
	
 	/**
     * 获取孩子个人账户余额
     * @param int $childId
     * @return number
     */
    public function getChildCredit($childId=0)
    {
    	$amount = 0;
    	if ($childId)
    	{
    		$criteria = new CDbCriteria();
    		$criteria->select = 'cid,amount,`inout`';
    		$criteria->compare('childid', $childId);
    		$model = $this->findAll($criteria);
    		if (!empty($model))
    		{
    			foreach ($model as $val)
    			{
    				if ($val->inout == 'in')
    				{
    					$amount += $val->amount;
    				}
    				else
    				{
    					$amount -= $val->amount;
    				}
    			}
    		}
    	}
    	return $amount;
    }
}