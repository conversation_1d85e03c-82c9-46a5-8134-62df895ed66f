<?php

class ClubModule extends CWebModule
{
    public $ipFilters=array('127.0.0.1','::1');
	public function init()
	{
		// this method is called when the module is being created
		// you may place code here to customize the module or the application

		// import the module-level models and components
		$this->setImport(array(
			'club.models.*',
			'club.components.*',
		));
	}

	public function beforeControllerAction($controller, $action)
	{
		if(parent::beforeControllerAction($controller, $action))
		{
			// this method is called before any module controller action is performed
			// you may place customized code here
            $route=$controller->id.'/'.$action->id;
            if(!$this->allowIp(Yii::app()->request->userHostAddress) && $route!=='default/error')
                throw new CHttpException(403,"You are not allowed to access this page.");

			return true;
		}
		else
			return false;
	}

    /**
     * Checks to see if the user IP is allowed by {@link ipFilters}.
     * @param string $ip the user IP
     * @return boolean whether the user IP is allowed by {@link ipFilters}.
     */
    protected function allowIp($ip)
    {
        if(empty($this->ipFilters))
            return true;
        foreach($this->ipFilters as $filter)
        {
            if($filter==='*' || $filter===$ip || (($pos=strpos($filter,'*'))!==false && !strncmp($ip,$filter,$pos)))
                return true;
        }
        return false;
    }
}
