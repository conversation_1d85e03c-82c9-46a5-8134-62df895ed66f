<?php

/**
 * This is the model class for table "ivy_products_stock".
 *
 * The followings are the available columns in table 'ivy_products_stock':
 * @property integer $id
 * @property integer $cid
 * @property integer $pid
 * @property integer $paid
 * @property integer $num
 * @property string $school_id
 * @property integer $updated_userid
 * @property integer $updated_time
 */
class ProductsStock extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_products_stock';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('cid, pid, paid, num, school_id, updated_userid, updated_time', 'required'),
			array('cid, pid, paid, num, updated_userid, updated_time', 'numerical', 'integerOnly'=>true),
			array('school_id', 'length', 'max'=>255),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, cid, pid, paid, num, school_id, updated_userid, updated_time', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            //'stockItem' => array(self::HAS_MANY, 'ProductsStockItem', 'psid' => 'id'),
			'product' => array(self::HAS_ONE, 'Products', array('id'=>'pid')),
			'attr' => array(self::HAS_ONE, 'ProductsAttr', array('id'=>'paid')),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'cid' => 'Cid',
			'pid' => 'Pid',
			'paid' => 'Paid',
			'num' => 'Num',
			'school_id' => 'School',
			'updated_userid' => 'Updated Userid',
			'updated_time' => 'Updated Time',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('cid',$this->cid);
		$criteria->compare('pid',$this->pid);
		$criteria->compare('paid',$this->paid);
		$criteria->compare('num',$this->num);
		$criteria->compare('school_id',$this->school_id,true);
		$criteria->compare('updated_userid',$this->updated_userid);
		$criteria->compare('updated_time',$this->updated_time);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return ProductsStock the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	/**
	 * [changeStock 库存变动]
	 * @param  [type] $num  [变动数量，可为负数]
	 * @param  [type] $memo [变动备注]
	 * @param  [type] $uid  [操作人]
	 * @return [type]       [description]
	 */
	public function changeStock($num, $memo, $uid)
	{
		$this->num += $num;
		$this->updated_userid = $uid;
		$this->updated_time = time();
		if(!$this->save()){
		    return false;
		}
		$stockItemModel = new ProductsStockItem();
		$stockItemModel->cid = $this->cid;
		$stockItemModel->pid = $this->pid;
		$stockItemModel->paid = $this->paid;
		$stockItemModel->psid = $this->id;
		$stockItemModel->num = $num;
		$stockItemModel->memo = $memo;
		$stockItemModel->school_id = $this->school_id;
		$stockItemModel->updated_userid = $uid;
		$stockItemModel->updated_time = time();
		if(!$stockItemModel->save()){
			return false;
		}
		return true;
	}
}
