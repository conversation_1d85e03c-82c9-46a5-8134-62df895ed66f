<div>
    <div class='flex align-items  mb24 mt10'>
        <span class='font14 color3'>处理人：</span>
        <div class='flex1 flex align-items lib_class_handle'>
        </div>
    </div>
    <div class='reject redColor'>
        <div class='font14'><span class='el-icon-warning mr5 font16'></span>已驳回</div>
        <div class='mt8'>
        <?php echo Yii::t('withdrawal','Comment');?>：<span class='rejectMemo'></span>
        </div>
    </div>
    <div class='flex align-items mb20 settlement'>
        <span class='redColor font14 settlement_date'></span> 
        <span class='color6 font12 ml15'><span class='el-icon-warning-outline mr5'></span>以此日期为在学截止日进行费用清算</span>
    </div>
    <form class="J_ajaxForm formStu" action="<?php echo $this->createUrl("saveNode");?>" method="post" onsubmit="return check(this)">
        <table  class="table tableCss">
            <thead>
                <tr style='background:#FAFAFA' class='font14 color3'>
                    <td  width='100'><?php echo Yii::t('leave','Title');?></td>
                    <td  width='100'><?php echo Yii::t('withdrawal','Type');?></td>
                    <td width='100'>金额/元</td>
                    <td  width='200'><?php echo Yii::t('withdrawal','Comment');?></td>
                </tr>
            </thead>
            <tbody class='tbody'>
                <tr> 
                    <td><?php echo Yii::t('withdrawal','School Lunch Fee');?> <input type="hidden" name="name[]" value='School Lunch Fee'></td>
                    <td>
                        <label class="radio-inline font14 color6 lableHeight">
                            <input type="radio"  name="balance[]" value="0"> <?php echo Yii::t('withdrawal','Refund');?>
                        </label>
                        <label class="radio-inline font14 color6 lableHeight">
                            <input type="radio"  name="balance[]" value="1"> 欠费
                        </label>
                    </td>
                    <td><input type="number" min="0" step="0.01" class="form-control" name="amount[]" value='' id='amount' placeholder="<?php echo Yii::t('leave','Input');?>"></td>
                    <td><input type="text" class="form-control" name="memo[]" value='' id='memo' placeholder="<?php echo Yii::t('leave','Input');?>"></td>
                </tr>
            </tbody>
        </table>
        <textarea class="form-control mb24 mt8" rows="3" placeholder="<?php echo Yii::t('withdrawal','Comment');?>" name="remark" id='remark'  class='font14'></textarea>
        <input type="hidden" name="type" id="type"  value='lunch'>
        <input type="hidden" name="applicationId" id="applicationId">
        <input type="hidden" name="node" id="node">
        <div class='modal-footer borderTopNone'>
            <label class="checkbox-inline">
                <input type="checkbox" name="status" value="1"> <?php echo Yii::t('withdrawal', 'Mark this step as completed'); ?>
            </label>
            <button type="submit" class="btn btn-primary ml24 submitSend"><?php echo Yii::t('global','Submit');?></button>
            <button type="button" class="btn btn-primary J_ajax_submit_btn ml24"><?php echo Yii::t('global','Submit');?></button>
        </div>
    </form>
</div>

<script>
     lib()
     $(document).ready(function() {
        $('input[name="amount[]"]').on('input', function() {
            var value = $(this).val();
            if (!/^[1-9]\d*|0$/.test(value)) {
                $(this).val('');
            }
        });
    });
     function lib(){
        let forms=childDetails.forms.find(item2 =>item2.key === 'lunch')
        for(var key in forms.owner_info){
            if (key !== '2' && key !== '5') {
                $('.lib_class_handle').append('<div class="flex align-items mr16"><img src='+forms.owner_info[key].photoUrl+' alt="" class="img28"/><div class="font14 color3 ml8">'+forms.owner_info[key].name+'</div></div>')
            }
        }
        if(childDetails.info.appInfo.settlement_date){
            $('.settlement_date').text('清算日期：'+childDetails.info.appInfo.settlement_date)
        }else{
            $('.settlement').hide()
        }
        $('#type').val(forms.key)
        if(forms.confirm_status==2){
            $('.reject').show()
            $('.rejectMemo').html(forms.reject_memo)
        }else{
            $('.reject').hide()
            $('.rejectMemo').html(forms.reject_memo)
        }
        if(forms.data.goods && forms.data.goods.length){
            if(forms.data.goods[0]){
                $('input[name="balance[]"]').filter('[value="'+forms.data.goods[0].balance+'"]').prop('checked', true);
                $('#amount').val(forms.data.goods[0].amount);
                $('#memo').val(forms.data.goods[0].memo);
            }
        }
        if(forms.data.remark!=''){
            $('#remark').val(forms.data.remark);
        }
     }
     $('.submitSend').show()
    $('.J_ajax_submit_btn').hide()
    function check(_this){
        $('#J_fail_info').remove();
        var button = $(_this).find('button[type="submit"]');
        var flag = true, msg=[];
        if($('input[name="balance[]"]:checked').val()==undefined){
            flag = false;
            msg.push('<?php echo Yii::t("teaching","请选择类型");?>');
        }
        if($("input[name='amount[]").val()==''){
            flag = false;
            msg.push('<?php echo Yii::t("teaching","请输入金额");?>');
        }
        if(flag){
            $('.submitSend').hide()
            $('.J_ajax_submit_btn').show()
            $('.formStu .J_ajax_submit_btn').click();
            return false;
        } else{
            $( '<span id="J_fail_info" class="text-warning"><i class="glyphicon glyphicon-remove text-warning"></i> ' + msg.join('、') + '！</span>' ).appendTo(button.parent()).fadeIn( 'fast' );
            return false;
        }
    }
</script>
