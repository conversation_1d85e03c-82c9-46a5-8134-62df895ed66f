<div class="modal-header">
	<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
	<h4 class="modal-title">预览订单:<?php echo $orderModel->title; ?><a href="#" id="print"> [打印]</a></h4>
</div>
<div class="modal-body" id="order">
	<h3 class="text-center"><?php echo $orderModel->title; ?></h3>
	<h4>采购编号：<?php echo $orderModel->pid;?></h4>
	<h4>供应方：<?php echo Vendor::model()->findByPk($orderModel->vendorid)->cn_title;?></h4>
	<h4>订购校园：<?php echo $orderModel->school_name;?></h4>
	<h4>一、详细商品：</h4>
	<table class="table table-condensed">
		<colgroup>
			<col width="100">
			<col width="300">
			<col width="100">	
			<col width="100">
			<col width="100">
		</colgroup>
		<tr>
			<th>序号</th>
			<th>商品名称</th>
			<th>单价</th>
			<th>数量</th>
			<th>总价</th>
		</tr>
		<?php
			$i = 1;
			$totalNum = 0;
			$totalPrice = 0;
			foreach ($orderModel->item as $orderItem):
				$totalNum += $orderItem->num;
				$totalPrice += $orderItem->num*$orderItem->product->price;
		?>
			<tr>
				<th><?php echo $i; ?></th>
				<th><?php echo $orderItem->product->title;?></th>
				<th><?php echo $orderItem->product->price;?></th>
				<th><?php echo $orderItem->num;?></th>
				<th><?php echo number_format($orderItem->num*$orderItem->product->price,2,'.',',');?></th>
			</tr>
		<?php 
			$i++;
			endforeach; 
		?>
		<tr class="active">
			<td></td>
			<td></td>
			<td></td>
			<td><?php echo $totalNum; ?></td>
			<td><?php echo number_format($totalPrice,2,'.',','); ?></td>
		</tr>
	</table>
	<h4>二、交货日期、交货地点：</h4>
	<p>1. 交货日期：<?php echo date('Y-m-d', $orderModel->delivery_timestamp);?></p>
	<p>2. 交货地点：<?php echo $orderModel->delivery_place;?></p>
	<h4>三、付款及结算方式</h4>
	<p>1. 付款方式：<?php echo $orderModel->pay_type;?></p>
	<p>2. 结算方式：<?php echo $orderModel->billing_type;?></p>
	<!-- <p>3. 发票抬头：<?php echo $orderModel->invoice_title;?></p> -->
	<h4>四、产品质量要求及售后服务：</h4>
		<?php echo $orderModel->quality_aftersale;?>
	<h4>五、联络信息：请提前和校园联系后再进行配送</h4>
		<?php echo $orderModel->content;?>
	<h4>六、验收：</h4>
	<table class="table table-bordered">
		<colgroup>
			<col width="200">
			<col width="400">
		</colgroup>
		<tr>
			<td>货品是否存在差异：</td>
			<td>
				<label class="radio-inline">
				  <input type="radio" name="goods"> 是
				</label>
				<label class="radio-inline">
				  <input type="radio" name="goods"> 否
				</label>
			</td>
		</tr>
		<tr class="active">
			<td></td>
			<td class="text-center">(无差异时无需填写)</td>
		</tr>
		<tr class="active">
			<td>差异具体说明：</td>
			<td height="100"></td>
		</tr>
		<tr>
			<td>
				<p>申请人/日期</p> 
				Applicant/Date
			</td>
			<td></td>
		</tr>
		<tr>
			<td>
				<p>校园经理/日期</p>
				Campus Director/Date
			</td>
			<td></td>
		</tr>
		<tr>
			<td>
				<p>验收人/日期</p> 
				Acceptance /Date
			</td>
			<td></td>
		</tr>
	</table>

</div>
<div class="modal-footer">
	<label class="col-xs-2 control-label"></label>
	<!-- <button type="submit" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Submit');?></button> -->
	<button type="button" class="btn btn-default"  data-dismiss="modal"><?php echo Yii::t('global','Cancel');?></button>
</div>
<script>
	$("#print").click(function(){
	  	$("#order").printThis();
	})  
	// 回调：添加成功
	function cbOrder() {
	    // $('#modal').modal('hide');
	    $.fn.yiiGridView.update('purchase-order-form');
	}
</script>