<div class="container">
    <div class="row vertical-offset-100">
        <div class="col-md-6 col-md-offset-3">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title"><span class="glyphicon glyphicon-user"></span> <?php echo Yii::t('user','Lost Password?');?></h3>
                </div>
                <div class="panel-body">
                    <div class="mb10 logo-wrapper">
                        <img src="<?php echo Yii::app()->theme->baseUrl.'/images/logo.png'; ?>" class="img-responsive" alt="Responsive image">
                    </div>

                    <?php if ( Yii::app()->user->hasFlash('loginMessage')): ?>

                        <div class="well bg-warning">
                            <span class="text-danger">
                            <?php echo Yii::app()->user->getFlash('loginMessage');?>
                            </span>
                        </div>

                    <?php endif; ?>

                    <div class="form">
                        <?php echo CHtml::beginForm(); ?>
							<fieldset>
								<div class="form-group">
									<?php echo CHtml::activeTextField($model, 'username', array('class' => 'form-control', "placeholder"=>$model->getAttributeLabel('username'))) ?>
								</div>
								<div class="form-group">
									<button id="J_submit" type="button" class="btn btn-lg btn-success btn-block" disabled>
                                        <?php echo Yii::t("user", "Send Verification") ?>
                                    </button>
								</div>
								
                            </fieldset> 
                        <?php echo CHtml::endForm(); ?>
                    </div><!-- form -->
                </div>
                <div class="panel-footer">
                    <div class="text-right">
                        <img src="<?php echo Yii::app()->theme->baseUrl.'/images/logo_ig_small.png'?>">
                        an <a href="http://www.ivygroup.cn" target="_blank">IvyGroup</a> site
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    body{
        background: url('<?php echo Yii::app()->theme->baseUrl;?>/images/ds00<?php echo rand(1,4);?>.jpg');
        background-size: cover;
    }
</style>

<script>
    var submitBtn = $('#J_submit');
    submitBtn.removeAttr('disabled');
    submitBtn.click(function(){
        var email = $('#UserLostpass_username').val();
        if(email){
            var text = submitBtn.text();
            submitBtn.text(text+'...').attr('disabled', true);
            head.load(GV.themeManagerBaseUrl+'/base/js/util_libs/ajaxForm.js',function() {
                var form = submitBtn.parents('form');
                form.ajaxSubmit({
                    dataType	: 'json',
                    success     : function(data){
                        submitBtn.text(text).attr('disabled', false);
                        if(data.state == 'success'){
                            var opts = {delay: 15000, msg: data.message};
                        }
                        else{
                            var opts = {error: 1, msg: data.message};
                        }
                        resultTip(opts);
                    }
                });
            });
        }
        else{
            resultTip({error: 'warning', msg: '<?php echo Yii::t('message', 'Please input your email address.')?>'});
        }
    });

    $('#UserLostpass_username').keydown(function(e){
        if(e.keyCode==13){
            submitBtn.click();
            return false;
        }
    });
</script>
