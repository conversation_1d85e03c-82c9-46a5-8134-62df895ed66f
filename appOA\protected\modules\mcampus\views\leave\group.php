<div id='container' v-cloak>
  <ul class="nav nav-tabs" role="tablist">
    <li role="presentation" class="active"><a href="#home" aria-controls="home" role="tab" data-toggle="tab">按审批组显示</a></li>
    <li role="presentation"><a href="#user" aria-controls="profile" role="tab" data-toggle="tab" @click='getUserData()'>按用户显示</a></li>
  </ul>
  <div class="tab-content">
    <div role="tabpanel" class="tab-pane active pt24" id="home">
        <div class='row' v-if='groupData.group_list'>
            <div class='col-md-2'>
                <div class='mb20 text-center'>
                    <button type="button" class="btn btn-primary" @click='addGroup'><span class='el-icon-circle-plus-outline'></span> 添加分组</button>
                </div>
                <div>
                    <div class='flex groupList p10 font14' @click='getGroupUser()' :class='currentGroup.id=="ungroup"?"currentGroup":""'>
                        <div class='flex1  color3'>未分组</div>
                        <div class='color6'>{{groupData.ungrouped_user_count}}人</div>
                    </div>
                    <div v-for='(list,key,index) in groupData.group_list' :class='currentGroup.id==key?"currentGroup":""' class='flex groupList font14' >
                        <div class='flex1 font14 color3 p10' @click='getGroupUser(list)'>{{list.group_name}}</div>
                        <div class='dsdropdown'>
                            <el-dropdown trigger="click">
                                <span class="el-dropdown-link">
                                <i class='el-icon-more'></i>
                                </span>
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item @click.native='getDetail(list)'>修改</el-dropdown-item>
                                    <el-dropdown-item @click.native='delList(list)'>删除</el-dropdown-item>
                                </el-dropdown-menu>
                            </el-dropdown>
                       </div>
                    </div>
                </div>
            </div>
            <div class='col-md-10'>
                <div v-if='currentGroup.id=="ungroup" && unGroupData.user_list'>
                    <div class='row ' v-if='Object.keys(unGroupData.user_list).length>0 '>
                        <div  class='col-lg-4 col-md-4 col-sm-6 col-xs-12 pb24' v-for='(list,key,index) in unGroupData.user_list'>
                            <div class='flex align-items groupMedia'>
                                <div class="flex flex1">
                                    <div class="">
                                        <img :src="list.photoUrl" class="img-circle img42">
                                    </div>
                                    <div class="flex1 ml10 flex1Text mt5">
                                        <div class="font14 text_overflow">{{list.name}}</div>
                                        <p class="color6 font12 text_overflow">{{list.hrPosition}}</p>
                                    </div>
                                </div>
                                <div>
                                <el-dropdown trigger="click">
                                    <span class="el-dropdown-link text-primary">
                                        分组<i class="el-icon-arrow-down el-icon--right"></i>
                                    </span>
                                    <el-dropdown-menu slot="dropdown">
                                        <el-dropdown-item v-for='(item,keys,idx) in unGroupData.group_list' @click.native='operateGroup(list.uid,item.id)'>{{item.group_name}}</el-dropdown-item>
                                    </el-dropdown-menu>
                                    </el-dropdown>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-else-if='unGroupData.user_list && Object.keys(unGroupData.user_list).length==0'>
                        <el-empty description="暂无数据"></el-empty>
                    </div>
                </div>
                <div v-else-if='groupListUser.user_info'>
                    <div class='titleGroup'>第一审批人 <span class="badge">1</span></div>
                    <div class='row pb24'>
                        <div  class='col-lg-4 col-md-4 col-sm-6 col-xs-12'>
                            <div class="flex" v-if='groupListUser.user_info[groupListUser.detail.approver_first]'>
                                <div class="">
                                    <img :src="groupListUser.user_info[groupListUser.detail.approver_first].photoUrl" class="img-circle img42">
                                </div>
                                <div class="flex1 ml15 mt5">
                                    <div class="font14">{{groupListUser.user_info[groupListUser.detail.approver_first].name}}</div>
                                    <div class="color6 font12">{{groupListUser.user_info[groupListUser.detail.approver_first].hrPosition}}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class='titleGroup'>第二审批人 <span class="badge">{{groupListUser.detail.approver_second==0?0:1}}</span></div>
                    <div class='row pb24'>
                        <div  class='col-lg-4 col-md-4 col-sm-6 col-xs-12'>
                            <div class="flex" v-if='groupListUser.user_info[groupListUser.detail.approver_second]'>
                                <div class="">
                                    <img :src="groupListUser.user_info[groupListUser.detail.approver_second].photoUrl" class="img-circle img42">
                                </div>
                                <div class="flex1 ml15 mt5">
                                    <div class="font14">{{groupListUser.user_info[groupListUser.detail.approver_second].name}}</div>
                                    <div class="color6 font12">{{groupListUser.user_info[groupListUser.detail.approver_second].hrPosition}}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class='titleGroup'>抄送人 <span class="badge">{{groupListUser.detail.approver_cc.length}}</span></div>
                    <div class='row pb24'>
                        <div  class='col-lg-4 col-md-4 col-sm-6 col-xs-12 mb20' v-for='(list,index) in groupListUser.detail.approver_cc'>
                            <div class="flex" v-if='groupListUser.user_info[list]'>
                                <div class="">
                                    <img :src="groupListUser.user_info[list].photoUrl" class="img-circle img42">
                                </div>
                                <div class="flex1 ml15 mt5">
                                    <div class="font14">{{groupListUser.user_info[list].name}}</div>
                                    <div class="color6 font12">{{groupListUser.user_info[list].hrPosition}}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class='titleGroup'>成员 <span class="badge">{{groupListUser.detail.staff_id.length}}</span></div>
                    <div class='row pb24'>
                        <div  class='col-lg-4 col-md-4 col-sm-6 col-xs-12 mb20' v-for='(list,index) in groupListUser.detail.staff_id'>
                            <div class="flex" v-if='groupListUser.user_info[list]'>
                                <div class="">
                                    <img :src="groupListUser.user_info[list].photoUrl" class="img-circle img42">
                                </div>
                                <div class="flex1 ml15 flex1Text mt5">
                                    <div class="font14">{{groupListUser.user_info[list].name}}</div>
                                    <div class="color6 font12">{{groupListUser.user_info[list].hrPosition}}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div role="tabpanel" class="tab-pane pt24" id="user">
        <div class='row ' v-if='userData.user_list'>
            <div  class='col-lg-4 col-md-4 col-sm-6 col-xs-12 pb24' v-for='(list,key,index) in userData.user_list'>
                <div class='flex align-items groupMedia'>
                    <div class="flex flex1">
                        <div class="">
                            <img :src="list.photoUrl" class="img-circle img42">
                        </div>
                        <div class="flex1 ml10 flex1Text mt5">
                            <div class="font14 text_overflow">{{list.name}}</div>
                            <div class="color6 font12 text_overflow">{{list.hrPosition}}</div>
                        </div>
                    </div>
                    <div>
                    <el-dropdown trigger="click">
                        <span class="el-dropdown-link text-success" v-if='list.group!=0'>
                            {{userData.group_list[list.group].group_name}}
                            <i class="el-icon-arrow-down el-icon--right"></i>
                        </span>
                        <span class="el-dropdown-link text-primary" v-else>
                        分组<i class="el-icon-arrow-down el-icon--right"></i>
                        </span>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item v-for='(item,keys,idx) in userData.group_list' @click.native='operateGroup(list.uid,item.id)'>{{item.group_name}}</el-dropdown-item>
                        </el-dropdown-menu>
                        </el-dropdown>
                    </div>
                </div>
                
            </div>
        </div>
    </div>
  </div>
    <div class="modal fade" id="addGroupModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" data-backdrop="static">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("directMessage", "分组");?></h4>
            </div>
            <div class="modal-body p24" >
                <div>
                    <div class="form-group">
                        <span class='fontWeight'>分组名称</span>
                        <div><el-input v-model="group_name" placeholder="分组名称" class='width100' ></el-input></div>
                    </div>
                    <div class="form-group">
                        <span class='fontWeight'>添加第一审批人</span>
                        <div>
                            <el-select v-model="approver_first" filterable  clearable  class='width100' placeholder="请选择">
                                <el-option
                                v-for="(item,key,index) in selectDept.user_info"
                                :key="key"
                                :label="item.name"
                                :value="key">
                                </el-option>
                            </el-select>
                        </div>
                    </div>
                    <div class="form-group">
                        <span class='fontWeight'>添加第二审批人</span>
                        <div>
                            <el-select v-model="approver_second" filterable  clearable  class='width100' placeholder="请选择">
                                <el-option
                                v-for="(item,key,index) in selectDept.user_info"
                                :key="key"
                                :label="item.name"
                                :value="key">
                                </el-option>
                            </el-select>
                        </div>
                    </div>
                    <div class="form-group">
                        <span class='fontWeight'>抄送人</span>
                        <div>
                            <div class='staffData flex align-items' @click='showStaff("cc")' v-if='approver_cc.length==0'>
                                <div class='flex1'>
                                    请选择抄送人
                                </div>
                                <span class='el-icon-arrow-down downIcon'></span>
                            </div>
                            <div class='el-select staff_ids flex align-items' v-else @click='showStaff("cc")'>
                                <div class='flex1'>
                                    <span class="el-tag el-tag--info el-tag--small el-tag--light" v-for='(list,index) in approver_cc'>
                                        <span class="el-select__tags-text" v-if='allDept[list]'>{{allDept[list].name}}</span>
                                        <i class="el-tag__close el-icon-close" @click.stop='ccUnassign(list,index)'></i>
                                    </span>
                                </div>
                                <span class='el-icon-arrow-down downIcon'></span>
                            </div>
                            <!-- <el-select v-model="approver_cc" clearable  class='width100' multiple placeholder="请选择">
                                <el-option
                                v-for="(item,key,index) in defaultDept.user_info"
                                :key="key"
                                :label="item.name"
                                :value="key">
                                </el-option>
                            </el-select> -->
                        </div>
                    </div>
                    <div class="form-group">
                        <span class='fontWeight'>成员</span>
                        <div class='staffData flex align-items' @click='showStaff("staff")' v-if='staff_ids.length==0'>
                            <div class='flex1'>
                                请选择成员
                            </div>
                            <span class='el-icon-arrow-down downIcon'></span>
                        </div>
                        <div class='el-select staff_ids flex align-items' v-else @click='showStaff("staff")'>
                            <div class='flex1'>
                                <span class="el-tag el-tag--info el-tag--small el-tag--light" v-for='(list,index) in staff_ids'>
                                    <span class="el-select__tags-text" v-if='allDept[list]'>{{allDept[list].name}}</span>
                                    <i class="el-tag__close el-icon-close" @click.stop='staffUnassign(list,index)'></i>
                                </span>
                            </div>
                            <span class='el-icon-arrow-down downIcon'></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                <button type="button" class="btn btn-primary" @click="saveGroup()"><?php echo Yii::t("newDS", "保存");?></button>
            </div>
            </div>
        </div>
    </div>
    <!-- 选择成员 -->
    <div class="modal fade" id="addStaffModal" tabindex="-1" role="dialog" data-backdrop="static" data-keyboard="false" data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t("newDS", "选择成员");?></h4>
                </div>
                <div class="modal-body relative" style='padding:0'>
                    <span class='borderLeftpos'></span>
                    <div style='max-height:600px;' class='p24 row'>
                        <div class='col-md-6 col-sm-6'>
                            <div>
                                <el-select v-model="schoolId" class='width100' placeholder="请选择" @change='getSchoolData'>
                                    <el-option
                                        v-for="(item,key,index) in schoolList"
                                        :key="key"
                                        :label="item.title"
                                        :value="key">
                                    </el-option>
                                </el-select>
                            </div>
                            <div class='mt10'>
                                <el-input
                                placeholder="搜索"
                                v-model='searchText' 
                                clearable>
                                </el-input>
                            </div>
                            <div  class="tab-pane active mt15 scroll-box" id="class" v-if='searchText==""'  style='max-height:460px;overflow-y:auto'>
                                <div v-for='(list,index) in currentDept.list' class='relative mb16'>
                                    <p  @click='showDepname(list)'>
                                        <span  class='font14 color606 cur-p'>{{list.dep_name}} </span>
                                        <span class='el-icon-arrow-down ml5' v-if='dep_name!=list.dep_name'></span>
                                        <span class='el-icon-arrow-up ml5' v-else></span>
                                    </p>
                                    <p  class='allCheck' v-if='dep_name==list.dep_name'><button class="btn btn-default pull-right btn-xs" type="button" @click='selectAll(list,index)' ><?php echo Yii::t("global", "Select All");?></button></p>
                                    <div  class='border scroll-box mr10 childList' v-if='dep_name==list.dep_name'>
                                        <div class="flex align-items listMedia" v-for='(item,key,idx) in list.user'>
                                            <div class='flex flex1' v-if='currentDept.user_info[item.uid]'>
                                                <img :src="currentDept.user_info[item.uid].photoUrl" data-holder-rendered="true" class="media-object img-circle img42">
                                                <div class="flex1 ml10 flex1Text">
                                                    <div class=" font14 mt4 color3 text_overflow">{{currentDept.user_info[item.uid].name}}</div>
                                                    <div class="font12 color6 text_overflow">{{currentDept.user_info[item.uid].hrPosition}}</div>
                                                </div>
                                            </div>
                                            <div >
                                                <div v-if='item.group_id!=0'  >
                                                    <div v-if='DetailId.id && item.group_id==DetailId.id'>
                                                        <span class='cur-p font16 text-primary  el-icon-circle-plus-outline' v-if='!currentDept.user_info[item.uid].disabled' @click='assignStaff(item,index,idx)'></span>
                                                        <span v-else>已选择</span>
                                                    </div>
                                                    <div v-else>{{currentDept.group_list[item.group_id]}}</div>
                                                </div>
                                                <div v-else>
                                                    <span class='cur-p font16 text-primary  el-icon-circle-plus-outline' v-if='!currentDept.user_info[item.uid].disabled' @click='assignStaff(item,index,idx)'></span>
                                                    <span v-else>已选择</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else>
                                <div v-if='searchStaffList.length!=0' class='mt24 scroll-box'  style='max-height:460px;overflow-y:auto'>  
                                    <div class="flex align-items listMedia" v-for='(item,idx) in searchStaffList'>
                                        <div class='flex flex1' >
                                            <img :src="item.photoUrl" data-holder-rendered="true" class="media-object img-circle img42">
                                            <div class="flex1 ml10 flex1Text">
                                                <div class=" font14 mt4 color3 text_overflow">{{item.name}}</div>
                                                <div class="font12 color6 text_overflow">{{item.hrPosition}}</div>
                                            </div>
                                        </div>
                                        <div >
                                            <div v-if='item.group_id!=0'  >
                                                    <div v-if='DetailId.id && item.group_id==DetailId.id'>
                                                        <span class='cur-p font16 text-primary  el-icon-circle-plus-outline' v-if='!currentDept.user_info[item.uid].disabled' @click='assignStaff(item,idx,"search")'></span>
                                                        <span v-else>已选择</span>
                                                    </div>
                                                    <div v-else>{{currentDept.group_list[item.group_id]}}</div>
                                                </div>
                                                <div v-else>
                                                    <span class='cur-p font16 text-primary  el-icon-circle-plus-outline' v-if='!currentDept.user_info[item.uid].disabled' @click='assignStaff(item,idx,"search")'></span>
                                                    <span v-else>已选择</span>
                                                </div>
                                        </div>
                                    </div> 
                                </div>
                                <div v-else-if='searchText!=""'>
                                        <div class='font14 color6 text-center mt20'>暂无数据</div>    
                                </div>
                            </div>
                        </div>
                        <div class='col-md-6 col-sm-6 borderLeft'>
                            <p class='mt10 font14 color606'>
                            <?php echo Yii::t("newDS", " ");?>{{staffSelected.length}}<?php echo Yii::t("newDS", "成员");?>
                                <button class="btn btn-link pull-right btn-xs font14" v-if='staffSelected.length!=0' type="button" @click='batchDel("staff")'><?php echo Yii::t("newDS", "清空");?></button>
                            </p>
                            <div class='scroll-box p10 overflow-y' style='height:500px'>
                                <div class="flex align-items listMedia" v-for='(item,idx) in staffSelected'>
                                    <div class='flex flex1' v-if='allDept[item]'>
                                        <img :src="allDept[item].photoUrl" data-holder-rendered="true" class="media-object img-circle img42">
                                        <div class="flex1 ml10 flex1Text">
                                            <div class=" font14 mt4 color3 text_overflow">{{allDept[item].name}}</div>
                                            <div class="font12 color6 text_overflow">{{allDept[item].hrPosition}}</div>
                                        </div>
                                    </div>
                                    <div @click='Unassign(item,idx)'>
                                        <span class='closeChild cur-p mt10 font16 el-icon-circle-close'></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class='clearfix'></div>
                    </div>
                </div> 
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" @click='confirmSatff()'>确定</button>
                </div>
            </div>
        </div>
    </div>
    <!-- 抄送 -->
    <div class="modal fade" id="ccStaffModal" tabindex="-1" role="dialog" data-backdrop="static" data-keyboard="false" data-backdrop="static">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentCommentsModalLabel"><?php echo Yii::t("newDS", "抄送");?></h4>
                </div>
                <div class="modal-body relative" style='padding:0'>
                    <span class='borderLeftpos'></span>
                    <div style='max-height:600px;' class='p24 row'>
                        <div class='col-md-6 col-sm-6'>
                            <div>
                                <el-select v-model="schoolId" class='width100' placeholder="请选择" @change='getSchoolData'>
                                    <el-option
                                        v-for="(item,key,index) in schoolList"
                                        :key="key"
                                        :label="item.title"
                                        :value="key">
                                    </el-option>
                                </el-select>
                            </div>
                            <div class='mt10'>
                                <el-input
                                placeholder="搜索"
                                v-model='searchText' 
                                clearable>
                                </el-input>
                            </div>
                            <div  class="tab-pane active mt15 scroll-box" id="class" v-if='searchText==""'  style='max-height:460px;overflow-y:auto'>
                                <div v-for='(list,index) in currentDept.list' class='relative mb16'>
                                    <p  @click='showDepname(list)'>
                                        <span  class='font14 color606 cur-p'>{{list.dep_name}} </span>
                                        <span class='el-icon-arrow-down ml5' v-if='dep_name!=list.dep_name'></span>
                                        <span class='el-icon-arrow-up ml5' v-else></span>
                                    </p>
                                    <p  class='allCheck' v-if='dep_name==list.dep_name'><button class="btn btn-default pull-right btn-xs" type="button" @click='ccselectAll(list,index)' ><?php echo Yii::t("global", "Select All");?></button></p>
                                    <div  class='border scroll-box mr10 childList' v-if='dep_name==list.dep_name'>
                                        <div class="flex align-items listMedia" v-for='(item,key,idx) in list.user'>
                                            <div class='flex flex1' v-if='currentDept.user_info[item.uid]'>
                                                <img :src="currentDept.user_info[item.uid].photoUrl" data-holder-rendered="true" class="media-object img-circle img42">
                                                <div class="flex1 ml10 flex1Text">
                                                    <div class=" font14 mt4 color3 text_overflow">{{currentDept.user_info[item.uid].name}}</div>
                                                    <div class="font12 color6 text_overflow">{{currentDept.user_info[item.uid].hrPosition}}</div>
                                                </div>
                                            </div>
                                            <div >
                                                <span class='cur-p font16 text-primary  el-icon-circle-plus-outline' v-if='!currentDept.user_info[item.uid].disabled' @click='assignCcStaff(item,index,idx)'></span>
                                                <span v-else>已选择</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else>
                                <div v-if='searchStaffList.length!=0' class='mt24 scroll-box'  style='max-height:460px;overflow-y:auto'>  
                                    <div class="flex align-items listMedia" v-for='(item,idx) in searchStaffList'>
                                        <div class='flex flex1' >
                                            <img :src="item.photoUrl" data-holder-rendered="true" class="media-object img-circle img42">
                                            <div class="flex1 ml10 flex1Text">
                                                <div class=" font14 mt4 color3 text_overflow">{{item.name}}</div>
                                                <div class="font12 color6 text_overflow">{{item.hrPosition}}</div>
                                            </div>
                                        </div>
                                        <div >
                                            <span class='cur-p font16 text-primary  el-icon-circle-plus-outline' v-if='!currentDept.user_info[item.uid].disabled' @click='assignCcStaff(item,idx,"search")'></span>
                                            <span v-else>已选择</span>
                                        </div>
                                    </div> 
                                </div>
                                <div v-else-if='searchText!=""'>
                                        <div class='font14 color6 text-center mt20'>暂无数据</div>    
                                </div>
                            </div>
                        </div>
                        <div class='col-md-6 col-sm-6 borderLeft'>
                            <p class='mt10 font14 color606'>
                            <?php echo Yii::t("newDS", " ");?>{{ccSelected.length}}<?php echo Yii::t("newDS", "成员");?>
                                <button class="btn btn-link pull-right btn-xs font14" v-if='ccSelected.length!=0' type="button" @click='batchDel("cc")'><?php echo Yii::t("newDS", "清空");?></button>
                            </p>
                            <div class='scroll-box p10 overflow-y' style='height:500px'>
                                <div class="flex align-items listMedia" v-for='(item,idx) in ccSelected'>
                                    <div class='flex flex1' v-if='allDept[item]'>
                                        <img :src="allDept[item].photoUrl" data-holder-rendered="true" class="media-object img-circle img42">
                                        <div class="flex1 ml10 flex1Text">
                                            <div class=" font14 mt4 color3 text_overflow">{{allDept[item].name}}</div>
                                            <div class="font12 color6 text_overflow">{{allDept[item].hrPosition}}</div>
                                        </div>
                                    </div>
                                    <div @click='ccUnassign(item,idx)'>
                                        <span class='closeChild cur-p mt10 font16 el-icon-circle-close'></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class='clearfix'></div>
                    </div>
                </div> 
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" @click='ccconfirmSatff()'>确定</button>
                </div>
            </div>
        </div>
    </div>
    <!-- 撤销 -->
    <div class="modal fade" id="delModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel"><?php echo Yii::t("global", "删除分组"); ?></h4>
                </div>
                <div class="modal-body p24" >
                    确定删除吗？
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                    <button type="button" class="btn btn-primary" @click='delList("del")'><?php echo Yii::t("global", "OK"); ?></button>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
 var container=new Vue({
        el: "#container",
        data: {
            groupData:{},
            currentGroup:{
                id:'ungroup'
            },
            unGroupData:{},
            groupListUser:{},
            userData:{},
            defaultDept:{},
            approver_cc:[],
            group_name:'',
            approver_first:'',
            approver_second:'',
            staff_ids:[],
            searchText:'',
            staffSelected:[],
            dep_name:'',
            schoolList:{},
            schoolId:'',
            currentDept:{},
            DetailId:{},
            allDept:{},
            activeType:'index',
            delId:'',
            groupDetail:{},
            ccSelected:[],
            selectDept:{}
        },
        created: function() {
            this.groupList()
            this.getGroupUser()
        },
        watch:{
        },
        computed: {
            searchStaffList: function() {
                var search = this.searchText;
                var searchVal = ''; //搜索后的数据
                if(search) {
                    searchVal =Object.values(this.currentDept.user_info).filter(function(product) {
                        return Object.keys(product).some(function(key) {
                            return String(product['name'].toLowerCase()).indexOf(search.toLowerCase()) !== -1;
                        })
                    })
                    return searchVal;
                }
                return this.searchStaffList;
            },
        },
        methods: {
            groupList(){
                this.activeType='index'
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getApprovalGroupList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                        that.groupData=data.data
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            getGroupUser(list){
                let that=this
                if(list){
                    this.currentGroup=list
                    $.ajax({
                        url: '<?php echo $this->createUrl("getApprovalGroupDetail") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            id:list.id
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.groupListUser=data.data
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        },
                    })
                }else{
                    this.currentGroup={id:'ungroup'}
                    $.ajax({
                        url: '<?php echo $this->createUrl("getUngroupedDetail") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.unGroupData=data.data
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        },
                    })
                }
               
                
            },
            getUserData(){
                this.activeType='user'
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getApprovalUserList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.userData=data.data
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            addGroup(){
                let that=this
                this.DetailId={}
                that.group_name=''
                that.approver_first=''
                that.approver_second=''
                that.approver_cc=[]
                that.staff_ids=[]
                that.staffSelected=[]
                this.ccSelected=[]
                this.getSchoolData('all')
            },
            getSchoolData(type){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getAllDepartment") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        school_id:this.schoolId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.currentDept=data.data
                            that.allDept=Object.assign(that.allDept, data.data.user_info)
                            if(that.schoolId==''){
                                that.selectDept=data.data
                            }
                            if(type=='all'){
                                that.defaultDept=data.data
                                if(that.DetailId.id){
                                    that.detailData()
                                }else{
                                    $("#addGroupModal").modal('show')  
                                }
                            }else{
                                that.staffSelected=that.staffSelected.map(String);
                                for(var key in that.currentDept.user_info){
                                    if(that.staffSelected.indexOf(key+'')!=-1){
                                        Vue.set(that.currentDept.user_info[key], 'disabled', true);
                                    }else{
                                        Vue.set(that.currentDept.user_info[key], 'disabled', false);
                                    }
                                }
                                that.ccSelected=that.ccSelected.map(String);
                                for(var key in that.currentDept.user_info){
                                    if(that.ccSelected.indexOf(key+'')!=-1){
                                        Vue.set(that.currentDept.user_info[key], 'disabled', true);
                                    }else{
                                        Vue.set(that.currentDept.user_info[key], 'disabled', false);
                                    }
                                }
                                
                            }
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            showDepname(list){
                if(this.dep_name==list.dep_name){
                    this.dep_name=''
                    return
                }
                this.dep_name=list.dep_name
                for(let i=0;i<list.user.length;i++){
                    if(this.staffSelected.indexOf(list.user[i].uid+'')!=-1){
                        Vue.set(this.currentDept.user_info[list.user[i].uid], 'disabled', true);
                    }
                }
            },
            showStaff(type){
                let that=this
                if(type=='staff'){
                    that.staffSelected=JSON.parse( JSON.stringify (that.staff_ids)) 
                    for(var key in that.currentDept.user_info){
                        if(that.staffSelected.indexOf(key+'')!=-1){
                            Vue.set(that.currentDept.user_info[key], 'disabled', true);
                        }else{
                            Vue.set(that.currentDept.user_info[key], 'disabled', false);
                        }
                    }
                }else{
                    that.ccSelected=JSON.parse( JSON.stringify (that.approver_cc)) 
                    for(var key in that.currentDept.user_info){
                        if(that.ccSelected.indexOf(key+'')!=-1){
                            Vue.set(that.currentDept.user_info[key], 'disabled', true);
                        }else{
                            Vue.set(that.currentDept.user_info[key], 'disabled', false);
                        }
                    }
                }
                if(Object.keys(that.schoolList).length!=0){
                    if(type=='staff'){
                        $("#addStaffModal").modal('show')  
                    }else{
                        $("#ccStaffModal").modal('show')  
                    }
                    return
                }
                $.ajax({
                    url: '<?php echo $this->createUrl("schoolList") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.schoolList=data.data.list  
                            that.schoolId=data.data.current_school 
                            if(type=='staff'){
                                $("#addStaffModal").modal('show')  
                            }else{
                                $("#ccStaffModal").modal('show')  
                            }
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            
            },
            getDetail(list){
                this.DetailId=list
                this.getSchoolData('all')
            },
            detailData(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("getApprovalGroupDetail") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        id:this.DetailId.id
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.allDept=Object.assign(that.allDept, data.data.user_info)
                            that.group_name=data.data.detail.group_name
                            that.approver_first=data.data.detail.approver_first==0?'':data.data.detail.approver_first+''
                            that.approver_second=data.data.detail.approver_second==0?'':data.data.detail.approver_second+''
                            that.approver_cc=data.data.detail.approver_cc
                            that.staff_ids= data.data.detail.staff_id
                            that.groupDetail=data.data.detail
                            $("#addGroupModal").modal('show')
                            
                            
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            assignCcStaff(list,index,idx){
                if(idx=='search'){
                    this.searchStaffList[index].disabled=true
                }else{
                    Vue.set(this.currentDept.user_info[list.uid], 'disabled', true);
                }
                this.ccSelected.push(list.uid)
            },
            ccUnassign(list,index){
                if(this.currentDept.user_info[list]){
                    Vue.set(this.currentDept.user_info[list], 'disabled', false);
                }
                this.ccSelected.splice(index,1)
                this.approver_cc.splice(index,1)
            },
            ccconfirmSatff(){
                this.approver_cc=this.ccSelected
                $("#ccStaffModal").modal('hide')  
            },
            ccselectAll(list,index){
                list.user.forEach(item => {
                    if(!this.currentDept.user_info[item.uid].disabled){
                        this.ccSelected.push(item.uid)
                        Vue.set(this.currentDept.user_info[item.uid], 'disabled', true);
                    }
                });
            },
            assignStaff(list,index,idx){
                if(idx=='search'){
                    this.searchStaffList[index].disabled=true
                }else{
                    Vue.set(this.currentDept.user_info[list.uid], 'disabled', true);
                }
                this.staffSelected.push(list.uid)
            },
            Unassign(list,index){
                if(this.currentDept.user_info[list]){
                    Vue.set(this.currentDept.user_info[list], 'disabled', false);
                }
                this.staffSelected.splice(index,1)
            },
            staffUnassign(list,index){
                if(this.currentDept.user_info[list]){
                    Vue.set(this.currentDept.user_info[list], 'disabled', false);
                }
                this.staff_ids.splice(index,1)
            },
            confirmSatff(){
                this.staff_ids=this.staffSelected
                $("#addStaffModal").modal('hide')  
            },
            selectAll(list,index){
                list.user.forEach(item => {
                    if(!this.currentDept.user_info[item.uid].disabled && item.group_id==0){
                        this.staffSelected.push(item.uid)
                        Vue.set(this.currentDept.user_info[item.uid], 'disabled', true);
                    }
                });
            },
            batchDel(type){
                if(type=='staff'){
                    this.staffSelected=[]
                    for(var key in this.currentDept.user_info){
                        if(this.staffSelected.indexOf(key)!=-1){
                            Vue.set(this.currentDept.user_info[key], 'disabled', true);
                        }else{
                            Vue.set(this.currentDept.user_info[key], 'disabled', false);
                        }
                    }
                }else{
                    this.ccSelected=[]
                    for(var key in this.currentDept.user_info){
                        if(this.ccSelected.indexOf(key)!=-1){
                            Vue.set(this.currentDept.user_info[key], 'disabled', true);
                        }else{
                            Vue.set(this.currentDept.user_info[key], 'disabled', false);
                        }
                    }
                }
            },
            saveGroup(){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("saveApprovalGroup") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        "group_name":this.group_name,//分组名
                        "approver_first":this.approver_first,//第一审批人
                        "approver_second":this.approver_second,//第二审批人
                        "approver_cc":this.approver_cc,//抄送人
                        "staff_ids": this.staff_ids,//成员
                        "id":this.DetailId.id?this.DetailId.id:''
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg: data.state
                            });
                            that.groupList()
                            that.getGroupUser(that.currentGroup)
                            $("#addGroupModal").modal('hide')  
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            operateGroup(uid,groupid){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("saveMember") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        group_id: groupid,
	                    user_id: uid
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            resultTip({
                                msg: data.state
                            });
                            if(that.activeType=='user'){
                                that.getUserData()
                            }else{
                                that.getGroupUser()
                                that.groupList()
                            }
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            },
            delList(list){
                if(list!='del'){
                    this.delId=list.id
                    $("#delModal").modal('show')  
                    return
                }
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("delApprovalGroup") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
	                    id:this.delId
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            $("#delModal").modal('hide')
                            resultTip({
                                msg: data.state
                            });
                            that.unGroupData={}
                            that.groupList()
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        resultTip({
                            error: 'warning',
                            msg: data.message
                        });
                    },
                })
            }
        }
    })
</script>
<style>
    .groupList{
        /* padding:10px; */
        align-items:center;

    }
    .groupList:hover,.currentGroup{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        cursor: pointer;
    }
    .dsdropdown{
        padding:10px 10px 10px 0
    }
    .titleGroup{
        font-size:14px;
        font-weight: 500;
        color: #333333;
        margin-bottom:20px
    }
    
    .text_overflow {
        overflow: hidden; 
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .flex1Text{
        overflow: hidden; 
        text-overflow: ellipsis;
        white-space: nowrap;
        width:0
    }
    .width100{
        width:100%
    }
    .staffData{
        width:100%;
        background-color: #FFF;
        border-radius: 4px;
        border: 1px solid #DCDFE6;
        box-sizing: border-box;
        color: #606266;
        font-size: inherit;
        min-height: 40px;
        padding: 0 15px;
        line-height: 40px;
        color: #C0C4CC;
        font-size: 14px;
        cursor: pointer;
    }
    .staff_ids{
        border-radius: 4px;
        border: 1px solid #DCDFE6;
        cursor: pointer;
        min-height: 40px;
    }
    .staff_ids .flex1{
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        -ms-flex-wrap: wrap;
        flex-wrap: wrap;
        display: flex;
    }
    .downIcon{
        width: 25px;
        color: #C0C4CC;
        font-size: 14px;
    }
    .borderLeftpos {
        display: inline-block;
        border-left: 1px solid #E4E7ED;
        height: 100%;
        width: 1px;
        position: absolute;
        left: 50%;
    }
    .allCheck{
        position: absolute;
        right: 10px;
        top: 0;
    }
    .border{
        border: 1px solid #DCDFE6;
        padding: 16px;
        border-radius: 4px;
    }
    .listMedia{
        padding:8px;
        border: 1px solid #fff;
    }
    .listMedia:hover{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        border: 1px solid #4D88D2;
        cursor: pointer;
    }
    .groupMedia{
        padding:8px;
        /* border: 1px solid #fff; */
    }
    .groupMedia:hover{
        background:#F7F7F8;
        border-radius: 4px;
        /* border: 1px solid #4D88D2; */
        cursor: pointer;
    }
    .fontWeight{
        font-weight:500;
        font-size:14px;
        margin-bottom:5px;
        display:inline-block
    }
</style>