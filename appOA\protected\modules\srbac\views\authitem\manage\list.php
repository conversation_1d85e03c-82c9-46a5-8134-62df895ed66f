<?php if (Yii::app()->user->hasFlash('updateName')): ?>
    <div id="messageUpd"
         style="color:red;font-weight:bold;font-size:14px;text-align:center
       ;position:relative;border:solid black 2px;background-color:#DDDDDD"
        >
        <?php echo Yii::app()->user->getFlash('updateName'); ?>
        <?php
        Yii::app()->clientScript->registerScript(
            'myHideEffect',
            '$("#messageUpd").animate({opacity: 0}, 2000).fadeOut(500);',
            CClientScript::POS_READY
        );
        ?>
    </div>
<?php endif; ?>

<table class="table table-hover">
    <tr>
        <th colspan="4" class="form-inline">
            <?php
            echo SHtml::dropDownList('selectedType', $selectedType,
                AuthItem::$TYPES,
                array(
                    'class' => 'form-control',
                    'prompt' => Helper::translate('srbac', 'Select one'),
                    'onchange' => 'location.href="'.$this->createUrl('manage').'?selectedType="+this.value',
                )
            );
            ?>
            <?php
            echo CHtml::link('<i class="glyphicon glyphicon-plus"></i> '.Helper::translate('srbac', 'Create'), array('create'), array('class'=>'J_dialog btn btn-primary', 'title'=>Helper::translate('srbac','Create New Item')));
            ?>
        </th>
    </tr>
    <?php foreach ($models as $n => $model): ?>
        <tr class="<?php echo $n % 2 ? 'even' : 'odd'; ?>">
            <td><?php echo CHtml::encode($model->name);?></td>
            <td><?php echo CHtml::encode($model->description);?></td>
            <td class="text-right">
                <?php echo CHtml::link('<i class="glyphicon glyphicon-pencil"></i>', array('update', 'id' => $model->name), array('class'=>'J_dialog btn btn-info btn-xs', 'title'=>Yii::t('global', 'Edit')));?>
                <?php echo CHtml::link('<i class="glyphicon glyphicon-remove"></i>', array('delete', 'id' => $model->name), array('class'=>'J_ajax_del btn btn-danger btn-xs', 'title'=>Yii::t('global', 'Delete')));?>
            </td>
        </tr>
    <?php endforeach; ?>
</table>
<div class="form-group">
    <?php
    $this->widget('BsCLinkPager', array(
        'pages' => $pages,
    ));
    ?>
</div>
