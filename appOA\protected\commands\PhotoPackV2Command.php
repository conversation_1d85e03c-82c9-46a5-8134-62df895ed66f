<?php

class PhotoPackV2Command extends CConsoleCommand {

	public $batchNum=200;

	private $qiniuConfig;

	public function init(){
		Yii::setPathOfAlias('common', dirname(__FILE__) . '/../../../common');
		Yii::import('common.models.portfolio.*');

		require_once( Yii::getPathOfAlias('common.components.qiniu.io') . '.php' );
		require_once( Yii::getPathOfAlias('common.components.qiniu.rs') . '.php' );

        $serverConfs = CommonUtils::LoadConfig('CfgMediaServer');
        $this->qiniuConfig = $serverConfs['servers'][20];
	}

    public function actionMake() {
        $crit = new CDbCriteria();
        $crit->addCondition('listurl is null');
        $crit->compare('pack_time', 0);
        $crit->compare('pcount', 0);
        $crit->order='create_time ASC';
        $packModel = TaskPackpic::model()->find($crit);

        if($packModel) {
            $packInfo = $this->makePhotoList($packModel->startyear,
                $packModel->childid,
                $packModel->sweek,
                $packModel->eweek
            );

            if ($this->uploadQiniu($packInfo)) {
                if ($this->mkzip($packInfo['fileName'])) {
                    echo "success uploaded...";
                    $packModel->pcount = $packInfo['picCount'];
                    $packModel->listurl = $packInfo['fileName'];
                    $packModel->pack_time = time();
                    $packModel->save();
                }
                else {
                    $this->mailAlert();
                }
            }
            else{
                $this->mailAlert();
            }
            //删除临时文件
            @unlink($packInfo['filePath']);

        }
    }

    private function makePhotoList($startyear, $childid, $sweek, $eweek){
        $baseDir = rtrim(Yii::app()->basePath, '/')  . '/runtime/';
        $subDir = 'packphoto/' . substr($childid, -1) . '/';
        $fileName = $childid . "_" . $startyear . "_" . $sweek . "-" . $eweek;

        CommonUtils::createDir($baseDir . $subDir);

        ChildMedia::setStartYear($startyear);
        ChildMediaLinks::setStartYear($startyear);

        $sql = "SELECT * FROM `ivy_notes_child` WHERE stat=20 and `childid` = ".$childid." and startyear=".$startyear;


//        $sql = "select distinct(classid) from ivy_child_media_links_".$startyear." where childid=".$childid;
        $items = Yii::app()->db->createCommand($sql)->queryAll();
        $classids = array();
        $weeks = array();
        foreach($items as $item){
            $classids[] = $item['classid'];
            $weeks[] = $item['weeknumber'];
        }

        if(!$classids || !$weeks) {
            return false;
        }

        // 此处条件会有分班bug
        $criteria = new CDbCriteria();
        $criteria->compare('t.classid', $classids);
        $criteria->compare('t.childid', array($childid, 0));
        $criteria->compare('t.category', 'week');
//        $criteria->addBetweenCondition('t.weeknum', $sweek, $eweek);
        $criteria->addBetweenCondition('t.weeknum', min($weeks), max($weeks));
        $count = ChildMediaLinks::model()->count($criteria);

        $r_or_n = PATH_SEPARATOR == ':' ? "\n" : "\r\n";

        $file_path = $baseDir . $subDir . $fileName;

        $fhandle = fopen($file_path, "w+");
        $pcount = 0;

        $cycle = ceil($count/$this->batchNum);
        for($i=0; $i<$cycle; $i++) {
            echo $i . "\n";
            $criteria->limit = $this->batchNum;
            $criteria->offset = $i * $this->batchNum;
            $photoLinks = ChildMediaLinks::model()->with('photoInfo')->findAll($criteria);
            $lines = array();
            foreach ($photoLinks as $link) {
                if(isset($link->photoInfo) && $link->photoInfo->type != '' && $link->photoInfo->type == 'photo') {
                    $pcount++;
                    $url = $link->photoInfo->getOriginal();
                    if(strpos($url, '!AA') !== false)
                        $url = str_replace('!AA', '!w2000A', $url);
                    elseif(strpos($url, '!BB') !== false)
                        $url = str_replace('!BB', '!w2000B', $url);
                    elseif(strpos($url, '!CC') !== false)
                        $url = str_replace('!CC', '!w2000C', $url);
                    else
                        $url .= '!w2000';

                    $lines[$pcount] = array(
                        'cid' => $link->photoInfo->classid,
                        'data' => sprintf("/url/%s/alias/%s",
                            base64_encode($url), base64_encode('week_'.$link->photoInfo->weeknum.'_'.$pcount.'.jpg')),
                    );
                }
            }

            foreach($lines as $_index => $_arr) {
                fwrite($fhandle, $_arr['data'] . $r_or_n);
            }
        }

        fclose($fhandle);
        return array(
            'filePath' => $file_path,
            'picCount' => $pcount,
            'fileName' => $fileName,
        );
    }

    private function uploadQiniu($packInfo=null)
    {
        global $QINIU_ACCESS_KEY;
        global $QINIU_SECRET_KEY;

        if ($packInfo) {
            $auth = new Qiniu_Mac($QINIU_ACCESS_KEY, $QINIU_SECRET_KEY);
            $bucket = $this->qiniuConfig['bucket'];
            $key = 'mkzip_'.base64_encode($packInfo['fileName']);

            $putPolicy = new Qiniu_RS_PutPolicy($bucket.':'.$key);
            $upToken = $putPolicy->Token($auth);

            $ret = Qiniu_PutFile($upToken, $key, $packInfo['filePath'], null);

            if(isset($ret[0]['key']) && $ret[0]['key']) {
                return $ret[0]['key'];
            }
            else {
                return false;
            }
        }
        return false;
    }

    private function mkzip($fileName)
    {
        global $QINIU_API_HOST;
        global $QINIU_ACCESS_KEY;
        global $QINIU_SECRET_KEY;

        if ($fileName){
            $key = 'mkzip_'.base64_encode($fileName);
            $zipName = 'photo_pack_'.$fileName.'.zip';
            $auth = new Qiniu_Mac($QINIU_ACCESS_KEY, $QINIU_SECRET_KEY);
            $ret = Qiniu_Client_CallWithForm(new Qiniu_MacHttpClient($auth), $QINIU_API_HOST.'/pfop/', array(
                'bucket' => $this->qiniuConfig['bucket'],
                'key' => $key,
                'fops' => 'mkzip/4/|saveas/'.base64_encode($this->qiniuConfig['bucket'].':'.$zipName),
                'pipeline' => $this->qiniuConfig['pipeline'],
                'notifyURL' => 'http://apps.ivyonline.cn/qiniu/packpic',
            ));
            if (isset($ret[0]['persistentId']) && $ret[0]['persistentId']) {
                return true;
            }
            else {
                return false;
            }
        }
        return false;
    }

    private function mailAlert()
    {
        $mailer = Yii::createComponent('common.extensions.mailer.EMailer');
        $mailer->Subject = "打包图片报警";
        $mailer->AddAddress('<EMAIL>');
        $isProduction = defined("IS_PRODUCTION") ? IS_PRODUCTION : false;
        $mailer->iniMail($isProduction);
        $mailer->Send();
    }
}
