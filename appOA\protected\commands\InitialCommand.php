<?php

/**
 *
 */

class InitialCommand extends CConsoleCommand {

	public function init(){
		Yii::setPathOfAlias('common', dirname(__FILE__) . '/../../../common');
		Yii::import('common.models.*');
	}
	public $batchNum = 200;
	/**
	 * 将SQL文件切割成SQL命令
	 * @param	Object	$sqlfile	SQL文件路径
	 * @param	Boolean	$exeSql		是否执行
	 * @return	Object				Description
	 */
	//private function iniDB($sqlfile, $exeSql=false){
	//	if(!$exeSql) return true;
	//	$error = false;
	//	if (!file_exists($sqlfile)) {
	//		$errs[] = sprintf( "%s not found!", $sqlfile);
	//		$error = true;
	//	} else {
	//		$sql_query = fread(fopen($sqlfile, 'r'), filesize($sqlfile));
	//		$sql_query = trim($sql_query);
	//		$sqlUtility = new SqlUtility();
	//		$sqlUtility->splitMySqlFile($pieces, $sql_query);
	//		$created_tables = array();
	//		foreach ($pieces as $piece) {
	//			Yii::app()->db->createCommand($piece)->execute();
	//		}
	//		// if there was an error, delete the tables created so far, so the next installation will not fail
	//		if ($error == true) {
	//			print_r($errs);
	//			Yii::app()->end();
	//		}
	//	}
	//	return true;
	//}
	//
	///**
	// * 准备数据库表
	// * @return	Object		Description
	// */
	//public function actionIniDB(){
	//	$sqlfile = dirname(__FILE__) . "./../data/child.mysql.sql";
	//	$this->iniDB($sqlfile, true);
	//}
	
	/**
	 * 导入将XOOPS中的用户和用户组的关系表
	 * @param	Number	$superUid		把哪个Uid设置为超级用户
	 * @param	Boolean	$forceRefresh	强制刷新所有内容
	 * @return	Object					Description
	 */
	public function actionInitAssignments($superUid=1, $forceRefresh=true){
		if($forceRefresh > 0){
			Assignments::model()->deleteAll();
			$superDude = new Assignments;
			$superDude->userid=$superUid;
			$superDude->itemname = 'superDude';
			$superDude->save();
		}
		$criteria = new CDbCriteria;
		$criteria->compare('group_type','ivystaff',true);
		$groups = XoGroups::model()->findAll($criteria);
		foreach($groups as $group){
			echo "begin with " . $group->group_type . "\r\n";
			$crit2 = new CDbCriteria;
			$crit2->compare('groupid', $group->groupid);
			$crit2->order = 'linkid asc';
			$links = XoGroupsUsersLink::model()->findAll($crit2);
			foreach($links as $link){
				if($link->uid){
					$assginment = new Assignments;
					$assginment->itemname = $group->group_type;
					$assginment->userid=$link->uid;
					$assginment->bizrule="";
					echo '.';
					try{
						$assginment->save();
					}catch(Exception $e){
						echo $e;
						echo "\r\n";
					}				
				}
			}
			echo "\r\n";
		}
		echo "clearing quit staff data:\r\n";
		$crit3 = new CDbCriteria;
		$crit3->compare('isstaff',1);
		$crit3->compare('level',0);
		//$crit3->compare('uid',8002053);
		$crit3->index='uid';
		$users = User::model()->findAll($crit3);
		//print_r(array_keys($users));
		$uidsArr = array_chunk(array_keys($users), 20);
		//print_r($uidsArr);
		foreach($uidsArr as $uids){
			echo "  removing data of ".implode(",",$uids);
			echo "\r\n";
			Assignments::model()->deleteAllByAttributes(array("userid"=>$uids));
		}
		return true;
	}
	
	
	/**
	 * 把XOOPS中的用户组转换为YII中的ROLE
	 * @return	Object		Description
	 */
	public function actionInitRoles()
	{
		$auth=Yii::app()->authManager;

		echo "clearing exsited ivystaff roles:\r\n";
		AuthItem::model()->deleteAll();
		
		echo "adding super user.\r\n";
		$superDude = new AuthItem;
		$superDude->name = 'superDude';
		$superDude->type = 2;
		$superDude->description = null;
		$superDude->bizrule = null;
		$superDude->data = null;
		$superDude->save();
		
		// remove ivystaff roles before adding;
		$criteria1 = new CDbCriteria;
		$criteria1->compare('name', 'ivystaff', true);
		$criteria1->compare('type',2);
		
		// fetch all ivystaff groups and insert;
		$criteria = new CDbCriteria;
		$criteria->compare('group_type','ivystaff',true);
		$groups = XoGroups::model()->findAll($criteria);
		echo "converting Xoops user groups to Yii roles.\r\n";
		foreach($groups as $group){
		  $role = new AuthItem;
		  $role->name = $group->group_type;
		  $role->type = 2;
		  $role->description = null;
		  $role->bizrule = null;
		  $role->data = null;
		  $role->save();
		  echo $group->group_type . " inserted...\r\n";
		}
		
		Yii::app()->end();
		echo "finished";
	}
	
	/**
	 * 在执行了用户组和用户-用户组关系的脚本之后；再用该函数生成RBAC的数据
	 * @return	Object		Description	
	 */
	public function actionGenRBAC($forceFlush=true){
		if($forceFlush){
			//删除所有任务和操作的定义
			$deleteTasksOperations = Yii::app()->db->createCommand()->delete(
				Yii::app()->authManager->itemTable,
				'`type` IN (0,1)'
			);
			//删除所有的权限分配
			$deleteItemChildren = Yii::app()->db->createCommand()->delete(
				Yii::app()->authManager->itemChildTable
			);
		}
		
		$rbac = include_once(dirname(__FILE__) . "/./include/rbac.config.php");
		$auth=Yii::app()->authManager;
		
		foreach($rbac['operations'] as $operation=>$comment){
			$auth->createOperation($operation,$comment);
		}
		
		$roles = $auth->getRoles();
		
		foreach($rbac['tasks'] as $key=>$_task){
			$bizRule = isset($_task['bizRule']) ? $_task['bizRule'] : NULL;
			$desc = isset($_task['desc']) ? $_task['desc'] : "";
			$task=$auth->createTask($key,$desc,$bizRule);
			if(isset($_task['items']) && count($_task['items'])){
				foreach($_task['items'] as $child){
					$task->addChild($child);
					echo $child.' added.'."\r\n";
				}
			}
			if(isset($_task['assignTo']) && count($_task['assignTo'])){
				foreach($_task['assignTo'] as $roleName){
					if(isset($roles[$roleName])){
						$roles[$roleName]->addChild($key);
						echo $key.' added to '.$roleName."\r\n";
					}
				}
			}
		}
		
//		$mainMenus = include_once(dirname(__FILE__) . "./include/mainMenu.config.php");
//		AppsNavigations::model()->deleteAll();
//		
//		foreach($mainMenus as $mKey=>$mVal){
//			$an = new AppsNavigations;
//			foreach($mVal as $_ak=>$_av){
//				$an->$_ak = $_av;
//			}
//			$an->link = serialize($an->link);
//			$an->params = serialize($an->params);
//			$an->itemname = $mKey;
//			$an->save();
//			echo $mKey . ' added.' . "\r\n";
//			
//		}
		//$roles = $auth->getRoles(); 
		//foreach($rbac['roles'] as $key=>$_role){
		//	$bizRule = isset($_role['bizRule']) ? $_role['bizRule'] : NULL;
		//	$desc = isset($_role['desc']) ? $_role['desc'] : "";
		//	if(isset($roles[$key]))
		//	$role=$auth->createRole($key, $desc, $bizRule);
		//	foreach($_role['items'] as $child){
		//		$role->addChild($child);
		//	}
		//}
		
		//$auth=Yii::app()->authManager;
		//$auth->createOperation('oPreviewCC','Preview Child Center');
		//$task=$auth->createTask('tPreviewCC','Preview Child Center');
		//$task->addChild('oPreviewCC');
		//
		//$roles = array('ivystaff_eduhead', 'ivystaff_opschool', 'ivystaff_teacher', 'ivystaff_admin','ivystaff_it','ivystaff_tygmgt','ivystaff_pd','ivystaff_training','ivystaff_depthead');
		//foreach($roles as $_role){
		//	$crit = new CDbCriteria;
		//	$crit->compare('type',2);
		//	$crit->compare('name',$_role);
		//	$role = $auth->getAuthItem($_role);
		//	if($role!=null){
		//		try{
		//			$role->addChild('tPreviewCC');
		//			echo $role->name . "\r\n";			
		//		}catch(Exception $e){
		//			echo $e;
		//		}
		//	}
		//}
		
		/*
		$auth->createOperation('readLunchMenu','Read child lunch menu');
		$auth->createOperation('readClassContact','Read child contact list');
		$auth->createOperation('readClassTeacher','Read class teacher contact list');
		
		$auth->createOperation('updateChildInfo','update child information');
		$auth->createOperation('updateFamilyInfo','update child family information');
		$auth->createOperation('cancelLunch','Cancel lunch');
		/*
		$bizRule='return (in_array($params["childid"], Yii::app()->user->getVisitCIds() ) || Yii::app()->user->isStaff() || in_array($params["childid"], Yii::app()->user->getAdminCIds() ) );';
		$task=$auth->createTask('visitChild','access child info by visitor',$bizRule);
		$task->addChild('readJournal');
		$task->addChild('readLunchMenu');

		$bizRule='return (in_array($params["childid"], Yii::app()->user->getAdminCIds() ) || Yii::app()->user->isStaff() );';
		$task=$auth->createTask('privateInfo','Access child private info',$bizRule);
		$task->addChild('readClassContact');
		$task->addChild('readClassTeacher');

		$bizRule='return in_array($params["childid"], Yii::app()->user->getAdminCIds() );';
		$task=$auth->createTask('adminChild','admin child info by parent',$bizRule);
		$task->addChild('updateChildInfo');
		$task->addChild('updateFamilyInfo');
		$task->addChild('cancelLunch');
		
		
		$role=$auth->createRole('visitor');
		$role->addChild('visitChild');
		
		$role=$auth->createRole('staff');
		$role->addChild('privateInfo');
		
		$role=$auth->createRole('parent');
		$role->addChild('visitor');
		$role->addChild('privateInfo');
		$role->addChild('adminChild');
		*/
		//$role->save();
		//$auth->assign('admin',2);
		
		//$auth->createOperation('createPost','create a post');
		//$auth->createOperation('readPost','read a post');
		//$auth->createOperation('updatePost','update a post');
		//$auth->createOperation('deletePost','delete a post');
		//
		//$bizRule='return Yii::app()->user->id==$params["post"]->authID;';
		//$task=$auth->createTask('updateOwnPost','update a post by author himself',$bizRule);
		//$task->addChild('updatePost');
		// 
		//$role=$auth->createRole('reader');
		//$role->addChild('readPost');
		// 
		//$role=$auth->createRole('author');
		//$role->addChild('reader');
		//$role->addChild('createPost');
		//$role->addChild('updateOwnPost');
		//
		//$role=$auth->createRole('editor');
		//$role->addChild('reader');
		//$role->addChild('updatePost');
		// 
		//$role=$auth->createRole('admin');
		//$role->addChild('editor');
		//$role->addChild('author');
		//$role->addChild('deletePost');
		// 
		//$auth->assign('reader','readerA');
		//$auth->assign('author','authorB');
		//$auth->assign('editor','editorC');
		//$auth->assign('admin','adminD');
		//		
	}
	
	/*
	 * 把calendar_semester表中校历的4个日期迁移到calendar_yearly
	 */
	public function actionIniSemesterTime(){
		Yii::import('common.models.calendar.*');
		$calendarObj = Calendar::model()->findAll();
		foreach ($calendarObj as $val){
			$semesterOdj = CalendarSemester::model()->findAll('yid=:yid',array('yid'=>$val->yid));
			foreach ($semesterOdj as $csa){
				if ($csa->semester_flag == 10)
				{
					$semesterList["fall_start"] = $csa->school_start_timestamp;
					$semesterList["fall_end"] = $csa->school_end_timestamp;
				}
				else
				{
					$semesterList["spring_start"] = $csa->school_start_timestamp;
					$semesterList["spring_end"] = $csa->school_end_timestamp;
            	}
			}
			$val->timepoints = $semesterList['fall_start'].','.$semesterList["fall_end"].','.$semesterList["spring_start"].','.$semesterList["spring_end"];
			if ($val->save()){
				echo "The results:".$val->yid.".......".$val->title."........ok!\r\n";
			}else{
				print_r($val->getErrors());
			}
		}
	}

    /*
     * 初始化FamilyId；涉及三个表：孩子基本信息；父母表；家庭地址表
     */
    public function actionFamilyID_init(){
        Yii::import('common.models.child.*');
        $total = IvyParent::model()->count();
        $cycle = ceil( $total / $this->batchNum );
        echo $cycle."\r\n";
        for( $i=0; $i<$cycle; $i++){
//        for( $i=0; $i<1; $i++){
            echo "\r\n".'round '.$i."\r\n";
            $crit = new CDbCriteria();
            $crit->order = 'pid ASC';
            $crit->offset = $i * $this->batchNum;
            $crit->limit = $this->batchNum;

            $parents = IvyParent::model()->findAll($crit);
            $errors = array();
            foreach($parents as $parent){
                $children = unserialize( $parent->childs );
                if(count($children)){
                    sort($children);
                    $familyId = md5( $children[0] );
                    if(1 && empty($parent->family_id)){
                        for($ci = 0; $ci<count($children); $ci++){
                            $childObj = ChildProfileBasic::model()->findByPk( $children[$ci] );
                            $homeAddObj = HomeAddress::model()->findByPk( $children[$ci] );

                            if($childObj){
                                if( empty($childObj->seniority) || empty($childObj->family_id ) ){
                                    $childObj->setAttributes(
                                        array(
                                            'seniority' => $ci,
                                            'family_id' => $familyId
                                        )
                                    );
                                    $childObj->save();
                                }
                            }else{
                                $errors[] = $parent->pid;
                            }

                            if($homeAddObj){
                                $homeAddObj->family_id = $familyId;
                                $homeAddObj->save();
                            }
                        }
                        $parent->family_id = $familyId;
                        $parent->save();
                    }
                }else{
                    $errors[] = $parent->pid;
                }

                echo '.';
            }

            echo implode(',', $errors);
        }

    }

    public function actionMigrateSReport(){
        Yii::import('common.models.portfolio.*');
        Yii::import('common.models.calendar.*');

        $calendars = Calendar::model()->findAll();
        foreach($calendars as $calendar){
            $calendarData[$calendar->yid] = $calendar->startyear;
        }

        $total = SemesterReport::model()->count();

        $cycle = ceil($total/$this->batchNum);
        $crit = new CDbCriteria();
//        $crit->compare('id', 2);

        for($i=0; $i<$cycle; $i++){
//        for($i=0; $i<1; $i++){
            $crit->limit = $this->batchNum;
            $crit->offset = $i*$this->batchNum;
            $crit->order = 'id ASC';
            $origReports = SemesterReport::model()->with('extInfo')->findAll($crit);
            foreach($origReports as $_orig_report){
                $newReport = new SReport();
                $attrSet1 = array(
                    'schoolid', 'classid', 'childid', 'yid', 'stat', 'timestamp', 'uid',
                    'custom', 'custom_pdf', 'pdf_file', 'semester', 'id'
                );
                $data1 = $_orig_report->getAttributes($attrSet1);
                $newReport->setAttributes($data1);
                $newReport->id = $_orig_report->id;
                $newReport->pdf_file = 0;
                $newReport->template_id = 'ivy01';
                $newReport->semester = $newReport->semester / 10;
                $newReport->startyear = isset($calendarData[$newReport->yid]) ?
                    $calendarData[$newReport->yid] : 0;

                if(!$newReport->save()){
                    echo '>>>ERROR:: ' . $newReport->id . "\r\n";
                }else{
                    echo '-'.$newReport->id;
                }

                if($_orig_report->custom){
                    continue;
                }

                //拆分封面、封底和评论
                $splitAttrs = array(
                    'pre_cover'=>array('FrontCover', 'FrontCoverWork'),
                    'suf_cover'=>array('BackCover', 'BackCoverPhoto'),
                    'comments'=>array('InterestComment', 'Comment')
                );

                foreach($splitAttrs as $_attr => $mapping){
                    $newReportItem = new SReportItem();
                    $newReportItem->setAttributes(
                        array(
                            'report_id' => $newReport->id,
                            'category' => $mapping[0],
                            'subcategory' => $mapping[1],
                        )
                    );
                    switch($_attr){
                        case 'pre_cover':
                            $newReportItem->media_id = $_orig_report->pre_cover;
                            break;
                        case 'suf_cover':
                            $newReportItem->media_id = $_orig_report->suf_cover;
                            break;
                        case 'comments':
                            $newReportItem->content = $_orig_report->comments;
                            break;
                    }
                    if(! $newReportItem->save()){
                        echo '>>>ERROR:: ' . $_orig_report->id . "\r\n";
                    }else{
                        echo '-'.$_orig_report->id;
                    }
                }

                //处理子项
                $isStrengthsDone = false;
                foreach($_orig_report->extInfo as $_subItem){
                    $newReportItem = new SReportItem();
                    $newReportItem->setAttributes(array(
                        'report_id' => $_subItem->reportid,
                        'media_id' => $_subItem->pid,
                        'ld_id' => $_subItem->tid,
                        'content' => $_subItem->content,
                    ));
                    switch($_subItem->category){
                        case 'strengths':
                            $newReportItem->category = 'Strength';
                            if($isStrengthsDone){
                                $newReportItem->subcategory = 'Strength2';
                            }else{
                                $newReportItem->subcategory = 'Strength1';
                            }
                            $isStrengthsDone = true;
                            break;
                        case 'interest':
                            $newReportItem->category = 'InterestComment';
                            $newReportItem->subcategory = 'Interest';
                            break;
                    }
                    if(!$newReportItem->save()){
                        echo '>>>ERROR:: ' . $_subItem->id . "\r\n";
                    }else{
                        echo '-'.$_subItem->id;
                    }
                }

            }
        }
        echo $total;
    }

    public function actionMigrateXoopsDataUploads($uploadRootPath=null, $subPath='uploads',$bucket="private") {
        $ossSDK = CommonUtils::initOSS($bucket);
        $folders = array(
            '2011' => array('11', '12'),
            '2012' => array('01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'),
            '2013' => array('01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'),
            '2014' => array('01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'),
            '2015' => array('01', '02', '03', '04', '05', '06'),
        );
        foreach($folders as $year => $months) {
            foreach($months as $month) {
                $_subPath = sprintf('%s/%s/%s', $subPath, $year, $month);
                echo $_subPath . "\n";
                $this->doMigrateOSS($uploadRootPath, $_subPath, "", 100,  $bucket, $ossSDK );
            }
        }

    }

    public function actionMigrateAliYunOSS($uploadRootPath=null, $subPath=null, $newPrefix="", $batchNum=100,
                                           $bucket="public") {

        $ossSDK = CommonUtils::initOSS($bucket);
        //$this->doMigrateOSS($uploadRootPath, $subPath, $newPrefix, $batchNum, $bucket, $ossSDK);
        $this->doMigrateOSSDel($uploadRootPath, $subPath, $newPrefix, $ossSDK);
    }

    private function doMigrateOSS($uploadRootPath=null, $subPath=null, $newPrefix="", $batchNum=100,
                                  $bucket="public", $ossSDK) {


        if(!is_null($uploadRootPath) && !is_null($subPath)) {
            $filePath = rtrim($uploadRootPath, '/') . '/' . trim($subPath, '/') . '/';
            if( file_exists($filePath)) {
                $iterator = new DirectoryIterator($filePath);
                $counter = 0;
                while($iterator->valid()) {
                    if($iterator->isFile()){
                        $counter++;
                        $filename = $iterator->getFileName();
                        $aliYunOss['new'][] = trim($subPath, '/') . '/' . $filename;

                        if($counter == $batchNum) {
                            $counter = 0;
                            $ossSDK->processBatch($aliYunOss, $uploadRootPath, $newPrefix);
                            $aliYunOss['new'] = array();
                        }
                    }
                    $iterator->next();
                }

                if(!empty($aliYunOss['new'])) {
                    $ossSDK->processBatch($aliYunOss, $uploadRootPath, $newPrefix);
                    $aliYunOss['new'] = array();
                }
            } else {
                echo "-------------------------";
                echo "\n";
                echo $filePath . ' is not a valid path';
                echo "\n";
            }
        }
    }


    public function actionScheduleMigrateOSS($bucket="public") {
        $isProduction = defined("IS_PRODUCTION")? IS_PRODUCTION : false;
        $oaUploads = ($isProduction) ?
            "/hdc/www/ivysites/oa.ivyonline.cn/trunk/oasrc/htdocs/uploads" :
            "F:/works/ivyoa/trunk/oasrc/htdocs/uploads";
        $ivySchoolsUploads = ($isProduction) ?
            "/var/www/ivysites/userUploads/ivyschools/upload" :
            "F:/works/com.ivyschools.www/upload";
        $xoopsDataPath = ($isProduction) ?
            "/hdc/www/ivysites/oa.ivyonline.cn/trunk/oasrc/XOOPS-data" :
            "";

        $yesterdayTime = strtotime('yesterday');
        $_year = date('Y', $yesterdayTime);
        $_month = date('m', $yesterdayTime);

        $gFolder = array(
            'public' => array(
                'rootPath' => $oaUploads,
                'newPrefix' => "",
                'items' => array(
                    'childmgt' => array('thumbs'),
                    'users' => array('thumbs'),
                    'newbb' => array('thumbs'),
                    'lunch' => array(),
                    'information' => array(),
                    'purchase' => array('thumbs'),
                    'curriculum' => array('thumbs'),
                    'infopub' => array('staff'),
                    'crads' => array(),
                    'event' => array(),
                    'points' => array('thumb'),
                    'products' => array('thumbs'),
                    'productsCat' => array(),
//                    'classNotes' => array('663', '503', '638', '531', '637', '498', '639', '502', '825', '687', '666', '691', '813', '679', '669', '696', '673', '672', '675', '681', '676', '680', '682', '674', '678', '668', '685', '683', '677', '667', '670'),
//                    'classNotes' => array('887', '840'),
//                    'classNotes' => array('845', '842', '857', '850', '851'),
//                    'classNotes' => array('852', '853', '854', '855', '856', '858', '860', '861', '862', '863', '864', '865', '866', '872', '873', '878', '879', '886'),
//                    'classNotes' => array('841', '843', '844', '846', '847', '849'),
//                    'classNotes' => array('928', '929', '924', '933', '938', '939', '940', '946', '972', '976'),
//                    'classNotes' => array('1002', '1034', '1035', '1036', '1037', '1038', '1039', '1040', '1049'),
//                    'classNotes' => array('1050', '1054', '1056', '1057', '1058', '1060', '1061', '1062', '1064', '1065', '1066', '1067', '1069'),
//                    'classNotes' => array('1072', '1075', '1076', '1078', '1079', '1080', '1081', '1082', '1084', '1085', '1086', '1087', '1089', '1091', '1092', '1093', '1094', '1095', '1096', '1097', '1098'),
//                    'classNotes' => array('1100', '1101', '1102', '1103', '1106', '1109', '1112', '1123', '1141', '1155', '1174', '1191', '1199'),
//                    'classNotes' => array('1178', '1234', '1253', '1254', '1282', '1286', '1288', '1310', '1316', '1345', '1346', '1351', '1352', '1354', '1355'),
//                    'classNotes' => array('1356', '1357', '1358', '1359', '1360', '1361', '1362', '1363', '1364', '1365', '1366', '1367', '1368', '1369'),
//                    'classNotes' => array('1371', '1372', '1373', '1375', '1376', '1377', '1378', '1379', '1380', '1381', '1382', '1383', '1384', '1385', '1386', '1388', '1389', '1390', '1392', '1393'),
//                    'classNotes' => array('1527', '1543', '1442', '1566', '1580', '1581', '1582', '1583', '1585', '1586', '1589', '1590', '1591', '1592', '1596', '1597', '1598', '1599'),
//                    'classNotes' => array('1054', '1055', '1056', '1057', '1058', '1059'),
//                    'classNotes' => array('1600', '1602', '1603', '1604', '1605', '1608', '1609', '1610', '1611', '1612', '1614', '1615', '1616', '1619', '1620', '1621', '1623', '1624', '1626', '1627', '1629', '1631', '1636', '1637', '1638', '1640', '1641', '1642', '1643', '1644', '1646', '1661', '1688', '1689'),
//                    'classNotes' => array('1711', '1715', '1733', '1736', '1745', '1768', '1769', '1770', '1771', '1772', '1790', '1799'),
//                    'classNotes' => array('1801', '1819', '1822', '1826', '1827', '1830', '1833', '1834', '1837', '1838', '1843', '1845', '1846', '1847', '1848', '1849', '1850', '1851', '1852', '1855', '1856', '1857', '1858', '1860', '1861', '1862', '1864', '1868', '1869', '1870', '1872', '1873', '1874', '1875', '1876', '1877', '1878', '1879', '1880', '1881', '1883', '1893', '2009', '2027', '2044'),
                    'classNotes' => array('asa', '2014'),
                )
            ),
            'private' => array(
                'rootPath' => $xoopsDataPath,
                'newPrefix' => "",
                'items' => array(
                    'docs_pdf' => array(),
					'semester_report' => array('custom_pdf', 'custom_pdf/el_2012_2013_fall/Nursery', 'custom_pdf/el_2012_2013_fall/K1', 'custom_pdf/el_2012_2013_fall/K2', 'custom_pdf/el_2012_2013_fall/Kindergarten'),
                    'uploads' => array($_year.'/'.$_month, 'thumbs/'.$_year.'/'.$_month, '2023/09', 'thumbs/2023/09', '2023/10', 'thumbs/2023/10', '2023/11', 'thumbs/2023/11', '2024/01', 'thumbs/2024/01', '2024/02', 'thumbs/2024/02', '2024/03', 'thumbs/2024/03', '2024/04', 'thumbs/2024/04'),
                    'asa' => array('expense', 'handover', 'refund'),
                )
            ),
            'ivyschools' => array(
                'rootPath' => $ivySchoolsUploads,
                'newPrefix' => 'ivyschools',
            )
        );

        $ossSDK = CommonUtils::initOSS($bucket);
        foreach($gFolder[$bucket]['items'] as $folder=>$subfolders) {
            $this->doMigrateOSSDel($gFolder[$bucket]['rootPath'], $folder, $gFolder[$bucket]['newPrefix'], $ossSDK);
            if(!empty($subfolders)) {
                foreach($subfolders as $subfolder) {
                    $this->doMigrateOSSDel($gFolder[$bucket]['rootPath'],
                        rtrim($folder, '/') . '/' . ltrim($subfolder, '/'),
                        $gFolder[$bucket]['newPrefix'],
                        $ossSDK);
                }
            }
        }
    }


    private function doMigrateOSSDel($uploadRootPath=null,
                                     $subPath=null,
                                     $newPrefix="",
                                     $ossSDK) {
        if(!is_null($uploadRootPath) && !is_null($subPath)) {
            $ossSDK->debugMode = false;
            $uploadRootPath = rtrim($uploadRootPath, '/') . '/';
            $subPath = trim($subPath, '/') . '/';
            $filePath = $uploadRootPath . $subPath;
            $test = 0;
            if( file_exists($filePath)) {
                $iterator = new DirectoryIterator($filePath);
                while($iterator->valid()) {
                    if($iterator->isFile()){
                        $filename = $iterator->getFileName();
                        $ext = end(explode('.', $filename));
                        if( !in_array($ext, array('duped', 'cped'))) {

                            $subFilePath = trim($subPath, '/') . '/' . $filename;

//                            $ret = $ossSDK->oss->get_object_meta($ossSDK->bucket, $subFilePath);
//                            if( $ret->status == 404 ) {
                                echo $uploadRootPath . "\n";
                                echo $subFilePath . "\n";
                                $ret1 = $ossSDK->upload_by_file($subFilePath, $uploadRootPath, $newPrefix);
                                if($ret1->status == 200) {
                                    //@unlink( $filePath . $filename );
                                    rename( $filePath . $filename, $filePath . $filename . '.cped');
                                }
//                            } elseif( $ret->status == 200) {
//                                //@unlink( $filePath . $filename );
//                                rename( $filePath . $filename, $filePath . $filename . '.duped');
//                            }

                        } else {
                            echo "skipped " . $filename . "\n";
                        }
                    }
                    $iterator->next();
                    $test++;
                }
            } else {
                echo "-------------------------";
                echo "\n";
                echo $filePath . ' is not a valid path';
                echo "\n";
            }
        }
    }

    public function actionChangeOSSfolder($uploadRootPath=null, $subPath=null) {
        if(!is_null($subPath) && !is_null($uploadRootPath)) {
            $bucket = 'ivy-www-uploads';
            $ossSDK = Yii::createComponent(array(
                'class' => 'common.components.AliYun.OSS',
                'bucket' => $bucket
            ));
            $filePath = rtrim($uploadRootPath, '/') . '/' . trim($subPath, '/') . '/';
            $iterator = new DirectoryIterator($filePath);
            while($iterator->valid()) {
                if($iterator->isFile()) {
                    $file = $iterator->getFilename();
                    $object = trim($subPath, '/') . '/' . $file;
                    $newObject = 'ivyonline/' . trim($subPath, '/') . '/' . $file;
                    $ossSDK->oss->copy_object($bucket, $object, $bucket, $newObject);
                    echo $newObject . "\n";
                }
                $iterator->next();
            }
        }
    }

}
