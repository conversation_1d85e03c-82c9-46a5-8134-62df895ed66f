<?php

/**
 * This is the model class for table "ivy_achievement_report_course".
 *
 * The followings are the available columns in table 'ivy_achievement_report_course':
 * @property integer $id
 * @property integer $td
 * @property integer $course_program
 * @property integer $weight
 * @property string $title_cn
 * @property string $title_en
 * @property string $title_alias
 * @property string $status
 * @property string $create_time
 * @property string $update_time
 * @property string $uid
 */
class AchievementReportCourse extends CActiveRecord
{
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_achievement_report_course';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('td, weight', 'numerical', 'integerOnly'=>true),
			array('status, create_time, course_program, update_time, uid', 'length', 'max'=>255),
			array('title_cn, title_en, title_alias', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, td, course_program, weight, title_cn, title_en, status, create_time, update_time, uid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
			"courseScores" => array(self::HAS_MANY, 'AchievementReportCourseStandard', array('courseid' => "id")),
            'grade' => array(self::HAS_MANY, 'SecondaryReportGrade', array('td' => "id")),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'td' => 'Td',
			'course_program' => 'course_program',
			'weight' => 'Weight',
			'title_cn' => 'Title Cn',
			'title_en' => 'Title En',
			'title_alias' => 'Title Alias',
			'status' => 'Status',
			'create_time' => 'Create Time',
			'update_time' => 'Update Time',
			'uid' => 'Uid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('td',$this->td);
		$criteria->compare('course_program',$this->course_program);
		$criteria->compare('weight',$this->weight);
		$criteria->compare('title_cn',$this->title_cn,true);
		$criteria->compare('title_en',$this->title_en,true);
		$criteria->compare('status',$this->status,true);
		$criteria->compare('create_time',$this->create_time,true);
		$criteria->compare('update_time',$this->update_time,true);
		$criteria->compare('uid',$this->uid,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AchievementReportCourse the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	public function getName()
	{
		switch (Yii::app()->language) {
			case "zh_cn":
				return  $this->title_cn ;
				break;
			case "en_us":
				return  $this->title_en;
				break;
		}
	}

	// 根据课程的 program 获取某个课程的评分标准
	public function getAchievement($program)
	{
		$crit = new CDbCriteria();
		$crit->compare('t.course_program', $program);
		$crit->with = 'courseScores';
		$model = $this->find($crit);
		$data = array();
		if ($model) {
			foreach ($model->courseScores as $k => $v) {
				$data[$program]['items'][$v->id]['title'] = $v->title_cn;
				$data[$program]['courseid'] = $v->id;
				foreach ($v->items as $item) {
					$data[$program]['items'][$v->id]['fraction'][] = $item->fraction;
				}
			}
		}
		return $data;

	}
}
