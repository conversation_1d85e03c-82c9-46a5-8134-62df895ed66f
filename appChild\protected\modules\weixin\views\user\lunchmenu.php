<div id="journal">
	<h2>每周食谱<span><?php echo sprintf('第%s周', $menuLink->week_num);?></span></h2>
	<p class="week">
	<?php echo sprintf('%s - %s', CommonUtils::formatDateTime($menuLink->monday_timestamp), CommonUtils::formatDateTime($menuLink->monday_timestamp + 5 * 24 * 3600));?><br />
	<?php echo $menuLink->branch->title; ?>
	</p>
	
	<?php
	$attrs = array('menu_id', 'allergy_id');
	
	foreach($attrs as $attr):
		$menuId = $menuLink->getAttribute($attr);
		if(isset($result[$menuId])):
			echo CHtml::openTag('h3');
			echo ($menus[$menuId]->is_allergy == 0) ? '营养餐' : '替代餐';
			echo CHtml::closeTag('h3');
			
			if($menus[$menuId]->is_allergy > 0){
				echo CHtml::openTag('p');
				echo '适用于有食物过敏症状或其他有禁忌要求的孩子，如需食用本食谱，请确认与校园签订过食用替代餐协议。';
				echo CHtml::closeTag('p');			
			}
			?>
			<?php
			foreach($weekDays as $weekDay=>$wdtitle):
				
				$data = $result[$menuId][$weekDay];
				echo CHtml::openTag('h4', array('class'=>'weekday'));
				echo $wdtitle;
				echo CHtml::closeTag('h4');
			?>
				<table width="100%" class="table-1">
					<th width="20%" style="text-align:right;padding-right:0.3em;">类型</th>
					<th width="80%">餐品</th>
				<?php
				foreach($data as $item):
				if(!empty($item['content'])):
				?>
					<tr>
						<th style="text-align:right;padding-right:0.3em;"><?php echo $item['title']; ?></th>
						<td>
                            <?php
                            if(!empty($item['photo'])){
                                echo CHtml::openTag('div',array('class'=>'menupic'));
                                echo CHtml::image(Yii::app()->params['uploadBaseUrl'] . 'lunch/'.  $item['photo']);
                                echo CHtml::closeTag('div');
                            }

                            ?>

                            <?php echo $item['content']; ?>
                        </td>
					</tr>
				<?php
				endif;
				endforeach;?>
				</table>
				
			<?php
			endforeach;?>
			
			<?php
		endif;
		?>
	
	<?php
	endforeach;	
	?>
		
</div>

<style>
table.table-1 th,table.table-1 td{border-bottom:1px solid #dedede; border-right:1px solid #dedede; padding: 0.1em;}
table.table-1 th{background:#dedede;}
table.table-1{margin-bottom: 1em;}
div.menupic img{padding: 4px;background: #fff;border: 1px solid #dedede;max-width: 90%}
div.menupic{text-align: left}
</style>