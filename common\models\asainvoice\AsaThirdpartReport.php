<?php

/**
 * This is the model class for table "ivy_asa_thirdpart_report".
 *
 * The followings are the available columns in table 'ivy_asa_thirdpart_report':
 * @property integer $id
 * @property string $school_id
 * @property string $course_month
 * @property integer $report_month
 * @property integer $program_id
 * @property integer $course_id
 * @property integer $course_staff_id
 * @property double $unit_price
 * @property double $total_amount
 * @property double $assistant_pay
 * @property double $repatch_amount
 * @property string $repatch_reason
 * @property string $settle_month
 * @property integer $status
 * @property integer $update_time
 * @property integer $uid
 */
class AsaThirdpartReport extends CActiveRecord
{
    const STATUS_LSSUE = 2;
    const STATUS_ACTIVE = 1;
    const STATUS_FAIL = 0;
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_asa_thirdpart_report';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('school_id, course_month, report_month, program_id, course_id, course_staff_id, unit_price, total_amount, settle_month, status, update_time, uid', 'required'),
			array('report_month, program_id, course_id, course_staff_id, status, update_time, uid', 'numerical', 'integerOnly'=>true),
			array('unit_price, total_amount, repatch_amount, assistant_pay', 'numerical'),
			array('school_id, course_month, repatch_reason', 'length', 'max'=>255),
			array('settle_month', 'length', 'max'=>11),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, school_id, course_month, report_month, program_id, course_id, course_staff_id, unit_price, total_amount, repatch_amount, assistant_pay, repatch_reason, settle_month, status, update_time, uid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
            "vendor" => array(self::BELONGS_TO, 'AsaVendor',  array("course_staff_id" =>"vendor_id")),
            'course' => array(self::HAS_ONE, 'AsaCourse', array('id'=>'course_id')),
            'courseStaff' => array(self::HAS_ONE, 'AsaCourseStaff', array('course_id'=>'course_id', 'vendor_id'=>'course_staff_id')),
            'group' => array(self::HAS_ONE, 'AsaCourseGroup', array('id'=>'program_id')),
            'items' => array(self::HAS_MANY, 'AsaThirdpartReportItem', array('course_month'=>'course_month')),
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'school_id' => 'School',
			'course_month' => 'Course Month',
			'report_month' => 'Report Month',
			'program_id' => 'Program',
			'course_id' => 'Course',
			'course_staff_id' => 'Course Staff',
			'unit_price' => 'Unit Price',
			'total_amount' => 'Total Amount',
			'repatch_amount' => 'Repatch Amount',
			'assistant_pay' => 'Assistant Pay',
			'repatch_reason' => 'Repatch Reason',
			'settle_month' => 'Settle Month',
			'status' => 'Status',
			'update_time' => 'Update Time',
			'uid' => 'Uid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('school_id',$this->school_id,true);
		$criteria->compare('course_month',$this->course_month,true);
		$criteria->compare('report_month',$this->report_month);
		$criteria->compare('program_id',$this->program_id);
		$criteria->compare('course_id',$this->course_id);
		$criteria->compare('course_staff_id',$this->course_staff_id);
		$criteria->compare('unit_price',$this->unit_price);
		$criteria->compare('total_amount',$this->total_amount);
		$criteria->compare('assistant_pay',$this->assistant_pay);
		$criteria->compare('repatch_amount',$this->repatch_amount);
		$criteria->compare('repatch_reason',$this->repatch_reason,true);
		$criteria->compare('settle_month',$this->settle_month,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('update_time',$this->update_time);
		$criteria->compare('uid',$this->uid);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AsaThirdpartReport the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
