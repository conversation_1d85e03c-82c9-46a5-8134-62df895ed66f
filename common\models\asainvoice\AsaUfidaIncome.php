<?php

/**
 * This is the model class for table "ivy_asa_ufida_income".
 *
 * The followings are the available columns in table 'ivy_asa_ufida_income':
 * @property integer $id
 * @property integer $invoice_id 账单item表id
 * @property integer $order_id 账单id
 * @property string $payment_type
 * @property integer $transactiontype
 * @property string $borrow
 * @property string $loan
 * @property string $borrow_accounts_code
 * @property string $loan_accounts_code
 * @property double $amount
 * @property string $school_id
 * @property string $school_name
 * @property integer $group_id
 * @property string $group_name
 * @property integer $course_id
 * @property string $course_name
 * @property string $type
 * @property integer $fee_type
 * @property integer $payment_timestamp
 * @property string $memo
 * @property integer $status
 * @property integer $timestmp
 * @property string $transferid
 */
class AsaUfidaIncome extends CActiveRecord
{
	const LOAN = "预收账款-预收学费";
	const LOAN_CODE = "220311";
	const WECHAT_BORROW = "应收账款-微信支付";
	const WECHAT_BORROW_CODE = "112220";
	const BANK_BORROW = "银行存款";
	const BANK_BORROW_CODE = "1002";
	const SHARE = "提供服务收入-学费收入";
	const SHARE_CODE = "600106";

	const TYPE_PAY = "1";
	const TYPE_REFUND = "2";
	const TYPE_EXCHANGE = "3";

	const UFIDA_TYPE_PAY = "ufida_income";
	const UFIDA_TYPE_REFUND = "ufida_refund";


	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'ivy_asa_ufida_income';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		// NOTE: you should only define rules for those attributes that
		// will receive user inputs.
		return array(
			array('transactiontype, borrow, loan, borrow_accounts_code, loan_accounts_code, type, transferid, child_id', 'required'),
			array('invoice_id, order_id, transactiontype, group_id, course_id, fee_type, payment_timestamp, status, timestmp, child_id', 'numerical', 'integerOnly'=>true),
			array('amount', 'numerical'),
			array('payment_type, school_name, group_name, course_name', 'length', 'max'=>255),
			array('borrow, loan', 'length', 'max'=>500),
			array('borrow_accounts_code, loan_accounts_code, school_id, type', 'length', 'max'=>100),
			array('transferid', 'length', 'max'=>64),
			array('memo', 'safe'),
			// The following rule is used by search().
			// @todo Please remove those attributes that should not be searched.
			array('id, invoice_id, order_id, payment_type, transactiontype, borrow, loan, borrow_accounts_code, loan_accounts_code, amount, school_id, school_name, group_id, group_name, course_id, course_name, type, fee_type, payment_timestamp, memo, status, timestmp, transferid', 'safe', 'on'=>'search'),
		);
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'id' => 'ID',
			'invoice_id' => 'Invoice',
			'order_id' => 'Order',
			'payment_type' => 'Payment Type',
			'transactiontype' => 'Transactiontype',
			'borrow' => 'Borrow',
			'loan' => 'Loan',
			'borrow_accounts_code' => 'Borrow Accounts Code',
			'loan_accounts_code' => 'Loan Accounts Code',
			'amount' => 'Amount',
			'school_id' => 'School',
			'school_name' => 'School Name',
			'group_id' => 'Group',
			'group_name' => 'Group Name',
			'course_id' => 'Course',
			'course_name' => 'Course Name',
			'type' => 'Type',
			'fee_type' => 'Fee Type',
			'payment_timestamp' => 'Payment Timestamp',
			'memo' => 'Memo',
			'status' => 'Status',
			'timestmp' => 'Timestmp',
			'transferid' => 'Transferid',
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('id',$this->id);
		$criteria->compare('invoice_id',$this->invoice_id);
		$criteria->compare('order_id',$this->order_id);
		$criteria->compare('payment_type',$this->payment_type,true);
		$criteria->compare('transactiontype',$this->transactiontype);
		$criteria->compare('borrow',$this->borrow,true);
		$criteria->compare('loan',$this->loan,true);
		$criteria->compare('borrow_accounts_code',$this->borrow_accounts_code,true);
		$criteria->compare('loan_accounts_code',$this->loan_accounts_code,true);
		$criteria->compare('amount',$this->amount);
		$criteria->compare('school_id',$this->school_id,true);
		$criteria->compare('school_name',$this->school_name,true);
		$criteria->compare('group_id',$this->group_id);
		$criteria->compare('group_name',$this->group_name,true);
		$criteria->compare('course_id',$this->course_id);
		$criteria->compare('course_name',$this->course_name,true);
		$criteria->compare('type',$this->type,true);
		$criteria->compare('fee_type',$this->fee_type);
		$criteria->compare('payment_timestamp',$this->payment_timestamp);
		$criteria->compare('memo',$this->memo,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('timestmp',$this->timestmp);
		$criteria->compare('transferid',$this->transferid,true);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * @return CDbConnection the database connection used for this class
	 */
	public function getDbConnection()
	{
		return Yii::app()->subdb;
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return AsaUfidaIncome the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}
}
