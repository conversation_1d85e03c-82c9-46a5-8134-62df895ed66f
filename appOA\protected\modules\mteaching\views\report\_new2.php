<style>
    #student-table textarea.form-control {
        min-width: 350px;
    }
    .avatar {
        width: 54px;
        height: 54px;
        border-radius: 50%;
        object-fit: cover;
    }
    .avatar32{
        width: 32px;
        height: 32px;
        border-radius: 50%;
        object-fit: cover;
    }
    .mb0{
        margin-bottom: 0;
    }
    .pr10{
        padding-right: 10px;
    }
    .flex-align-item-center {
        flex-wrap: nowrap;
        align-items:center
    }
    .teacherName{
        color: #333333;
        line-height: 22px;
        font-size: 14px
    }
    .flex-wrap{
        display: flex;
        flex-wrap: wrap;
    }
    .audit-setup-text{
        font-size: 14px;
        line-height: 20px;
        font-weight: 400;
        padding: 10px;
    }
    .auditor-active{
        background: rgba(77,136,210,0.1);
        color: #4D88D2;
        border-radius: 4px;
    }
    .auditor-div{
        background: #FAFAFA;
        border-radius: 4px;
    }
    .with-100{
        width: 100%;
    }
    .select-width{
        width: 90%;
    }
    .ml30{
        margin-left: 30px;
    }
    .auditee-list-div{
        background: #FFFFFF;
        border-radius: 4px 4px 0px 0px;
        border: 1px solid #E8EAED;
        margin-top: 25px;
        padding: 10px;
    }
    .auditee-list{
        display: flex;
        flex-wrap: wrap;
    }
    .auditee-item{
        display:flex;
        align-items: center;
        width: 100%;
        padding: 10px;
    }
    .add-auditee-item{
        display:flex;
        align-items: center;
        width: 45%;
        padding: 10px;
        background: #FAFAFA;
        margin: 10px;
    }
    .add-auditee-item:hover{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        cursor: pointer;
    }
    .auditee-item-close{
        margin-left: auto;
    }
    .auditee-item:hover{
        background: rgba(77,136,210,0.1);
        border-radius: 4px;
        cursor: pointer;
    }
    .auditor—avatar {
        width: 44px;
        height: 44px;
        border-radius: 50%;
        object-fit: cover;
    }
    .auditee—avatar{
        width: 32px;
        height: 32px;
        border-radius: 50%;
        object-fit: cover;
    }

    .auditee-text{
        font-weight: 500;
        color: #333333;
        line-height: 22px;
        font-size: 14px;
        padding: 15px 10px;
    }
    .height40{
        /*height: 40px;*/
    }
    .optionSearch{
        height:auto
    }
    .imageSearch{
        width: 44px;
        height: 44px;
        object-fit: cover;
    }
    .text_overflow {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        line-height: 14px;
    }
    .h4-weight{
        font-weight: 700;
        font-size: 14px;
        margin: 0;
    }
    .reviewer{
        display:table-cell;
        height:20px;
        vertical-align: middle;
        font-size: 15px;
        padding-left: 20px;
        padding-top: 4px;
        padding-bottom: 24px;
    }
    .canceller{
        margin-left: auto;
        margin-right: 24px;
    }
    .mr5{
        margin-right: 5px;
    }
    .height200{
        min-height:200px
    }
    .flag_span{
       display:flex;
       align-items:center;
       margin-right:5px

    }
    .iconfont  {
        font-size:14px
    }
    .hover-cursor {
        cursor: pointer; /* 设置悬停时的光标为箭头 */
    }
    .td_center{
        text-align: center;
        vertical-align: middle !important;
    }
    .select_center{
        margin: 0 auto;
    }
    textarea {
        resize: none;
    }
    .pr15{
        padding-right: 15px;
    }
    dl {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
    }
    dl dt,
    dl dd {
        margin: 0;
    }
    .deadline{
        font-size: 11px;
        color: #F56B6C;
        margin-bottom: 16px;
    }
    .saveData[disabled]{
        pointer-events:auto
    }
</style>
<?php
$width = '3%';

foreach ($achievements as $v) {
    # code...
}
?>
<div id="container">
    <div class="col-md-12 nameOption">
        <h4 style="display: flex;align-items: center">
            <span class="glyphicon glyphicon-user"></span>
            <span id="chooseTeacher" style="cursor:pointer" <?php if(Yii::app()->user->checkAccess('o_MS_RrportMgt') || $data['isSupervisor']){?> onclick="chooseTeacher()" <?php }?> >
               <span style="color: #4D88D2;margin: 0 8px"><?php echo $staffName; ?></span>
            <?php if (Yii::app()->user->checkAccess('o_MS_RrportMgt') || $data['isSupervisor']) { ?>
                <span title="查看其它老师课表" class="el-icon-arrow-down down" style="color: #4D88D2"></span>
            <?php } ?>
            </span>

        </h4>
        <div class="reviewer">
            <?php if(empty($data['supervisorNum'])){?>
                <span style="color: #666;font-size: 14px"><?php echo Yii::t('teaching', 'No reviewer assigned');?></span>
            <?php }else{?>
                <span style="cursor:pointer;" @click="showAuditor()"><?php echo Yii::t('teaching', 'Reviewer');?><span style="color: #4D88D2">(<?php echo $data['supervisorNum']?>)</span></span>
            <?php }?>
            <!--中学校长展示审核设置操作按钮-->
            <?php if((Yii::app()->user->checkAccess('ivystaff_cd') || Yii::app()->user->checkAccess('ivystaff_it')) && Yii::app()->user->checkAccess('o_MS_RrportMgt')){?>
                <span class="glyphicon glyphicon-cog" style="color: #4D88D2;margin-left: 5px"></span>
                <span style="color: #4D88D2;cursor:pointer;" @click="setupSupervision(1)"><?php echo Yii::t('teaching', 'Reviewer Settings');?></span>
            <?php }?>
        </div>

    </div>
    <?php if ($data['courses']) { ?>
        <div class="col-md-2">
            <div class="list-group">
                <?php foreach ($data['courses'] as $course_id => $course) {
                    if ($course['report_course_id'] == '0') {
                        echo '<span class="list-group-item text-muted" style="cursor:pointer" onclick="switchCourse(this, ' . $course_id . ',\'' . $course['program'] . '\')">';
                        echo "{$course['program']} " .  CommonUtils::autoLang($course['title_cn'],$course['title_en']);
                        echo '</span>';
                    } else {
                        echo '<span class="list-group-item" style="cursor:pointer" onclick="switchCourse(this, ' . $course_id . ',\'' . $course['program'] . '\')">';
                        echo "{$course['program']} " . CommonUtils::autoLang($course['title_cn'],$course['title_en']);
                        echo '</span>';
                    }
                } ?>
            </div>
        </div>
        <div class="col-md-10">
            <div id="report_course_title"></div>
            <div style="display:flex;justify-content:space-between;align-items: center; width: 100%" class="">
                <div class="col-md-8" id="fill_in" style="display:none;font-size: 14px;color: #D9534F;">
                    <?php if(!empty($report[$this->selectedSemester]->fill_in_end_time)){?>
                        <span class="glyphicon glyphicon-info-sign mr5"></span>
                        <span style="font-weight: bold;"><?php echo Yii::t("principal","Reporting Deadline")?>：<?php echo date('Y/m/d H:i',$report[$this->selectedSemester]->fill_in_end_time )?></span>
                    <?php }?>
                </div>
                <!--只有第二第四次评估的且配置了模板的课程可以展示-->
                <div class="pull-right ml15" style="display: none" id="templateBtn">
                    <button type="button" id="downloadTemplate" class="btn btn-default mr5" onclick="downloadTemplate()">
                        <?php echo Yii::t('ptc','Download class template') ?>
                    </button>
                    <label>
                        <input type="button" id="i-check" value="<?php echo Yii::t('teaching','Import');?>" class="btn btn-primary mr5" onclick="$('#i-file').click();">
                        <input type="file" name="file" id='i-file'  accept=".xls, .xlsx" onchange="uploadPlan()" style="display: none">
                    </label>
                    <button type="button" class="btn btn-primary" onclick="daochu()">
                        <?php echo Yii::t('user', 'Export');?>
                    </button>
                </div>
            </div>
            <?php if($report['deadline'] == 1){?>
                    <div style="margin-top: 7px;margin-bottom: 9px;" class="clearfix">
                        <div  id="titleDeadline" style="display:none;float:right;font-size:12px;color:#F56B6C;">
                            <span class="el-icon-warning"></span>
                            <?php echo Yii::t('principal',"The deadline for the report has passed, please contact the principal's Office.")?>
                        </div>
                    </div>

            <?php }?>
            <table class="table table-bordered hidden" id="student-table">
                <thead>
                <tr style="background:#FBFBFB;">
                    <th class="td_center" rowspan="2" width="10%" data-switchable="false"><?php echo Yii::t('attends', 'Student List'); ?></th>
                    <th class="td_center" rowspan="2" width="15%"><?php echo Yii::t('teaching', 'Criteria'); ?></th>
                    <th class="td_center" colspan="<?php echo count($report)?>"><?php echo Yii::t('principal','Report NO.')?></th>
                    <?php if ($report[$this->selectedSemester]->cycle == 4): ?>
                        <th class="td_center" rowspan="2" width="3%"><?php echo Yii::t('teaching', 'Final Score'); ?></th>
                    <?php endif ?>
                    <?php if ($report[$this->selectedSemester]->type == 1): ?>
                        <th class="td_center" rowspan="2"><?php echo Yii::t('teaching', 'Comment from teacher'); ?></th>
                        <!-- <th><?php echo Yii::t('teaching', 'Comment from student'); ?></th> -->
                    <?php endif ?>
                    <!--                 year_end_score 控制 年级去掉年终分数-->
                    <?php if ($report[$this->selectedSemester]->cycle == 4 ): ?>
                        <th class="td_center" rowspan="2" width="3%" id="year_end_score">
                            <?php echo Yii::t('teaching', 'Year-End Score'); ?>
                        </th>
                    <?php endif ?>
                    <th class="td_center text-center " rowspan="2" width="7%"  data-switchable="false">
                        <button onclick="saveAll(0)" class="btn btn-primary saveData" title="<?php echo Yii::t('teaching', 'Save All'); ?>" <?php if($report['deadline'] == 1){ echo "disabled='disabled'";} ?>>
<!--                            <span class="glyphicon glyphicon-floppy-saved" aria-hidden="true"></span>-->
                            <?php echo Yii::t('teaching', 'Save All'); ?>
                        </button>
<!--                        --><?php //if($report['deadline'] == 1){?>
<!--                            <p class="deadline" style="margin-top: 9px">-->
<!--                                <span class="el-icon-warning"></span>-->
<!--                                --><?php //echo Yii::t('principal',"The deadline for the report has passed, please contact the principal's Office.")?>
<!--                            </p>-->
<!--                        --><?php //}?>
                    </th>
                </tr>
                <tr style="background:#FBFBFB;">
                    <?php foreach ($report as $_report): ?>
                        <th width="40" class="td_center">#<?php echo $_report->cycle ?></th>
                    <?php endforeach; ?>
                </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
    <?php } else { ?>
        <div class="col-md-12">
            <div class="alert alert-danger text-center text-info" role="alert">
                <strong><?php echo Yii::t('teaching', 'You are not assigned to any MS course, please contact MS teaching administration.'); ?></strong>
            </div>
        </div>
    <?php } ?>

    <!--选择老师窗口-->
    <div class="modal fade in" id="remarks" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="false">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                aria-hidden="true">×</span></button>
                    <h4 class="modal-title"><?php echo Yii::t('teaching','Select a Teacher')?> <span id="remarks_t"></span></h4>
                </div>
                <div class="form-horizontal">
                    <div class="modal-body">
                        <div name="teacherId" id="mySelect">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!--查看审核人窗口-->
    <div class="modal fade" id="viewReviewer" tabindex="-1" role="dialog" aria-labelledby="modal" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title"><?php echo Yii::t('teaching', 'Reviewers');?></h4>
                </div>
                <div class="form-horizontal">
                    <div class="modal-body">
                        <div class="flex flex-align-item-center mt10 mb24">
                            <img class="avatar" :src="showAuditorDataTeacher['photoUrl']"/>
                            <div class="ml10">
                                <span class="flex flex1 teacherName">{{showAuditorDataTeacher['name']}}</span>
                                <span style="color: #666666;line-height: 25px">{{showAuditorDataTeacher['hrPosition']}}</span>
                            </div>
                        </div>
                        <h4 class="h4-weight"><?php echo Yii::t('teaching', 'Who can review my reports');?></h4>
                        <div class="flex" style="flex-wrap: wrap" v-if="showAuditorDataSupervisor.length > 0">
                            <div v-for="(item,index) in showAuditorDataSupervisor" :key="index" class="mt16" style="width: 49%">
                                <img class="avatar32 mr10" referrerpolicy="no-referrer" :src="item.photoUrl"/>
                                <span class="teacherName">{{item.name}}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!--设置审核窗口-->
    <div class="modal fade" tabindex="-1" role="dialog" aria-labelledby="modal" id="ReviewerSettings" >
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title"><?php echo Yii::t('teaching', 'Reviewer Settings');?></h4>
                </div>
                <div class="form-horizontal">
                    <div class="modal-body" style="padding-left: 24px;padding-top: 24px">
                        <h4 class="h4-weight"><?php echo Yii::t('teaching','Add Reviewer')?></h4>
                        <div class="flex pt16 pb24">
                            <el-select
                                    class="select-width inline-input flex1 formControl"
                                    v-model="addSupervisorUid"
                                    filterable
                                    remote
                                    reserve-keyword
                                    clearable
                                    @visible-change="clearOptions($event)"
                                    placeholder="<?php echo Yii::t("directMessage", "Key words"); ?>"
                                    :remote-method="remoteMethod"
                                    :loading="loading">
                                <el-option
                                        class='optionSearch mb8'
                                        v-for="item in options"
                                        :key="item.uid"
                                        :label="item.name"
                                        :value="item.uid">
                                    <div class="media">
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                                <img :src="item.photoUrl" data-holder-rendered="true" class="media-object img-circle imageSearch">
                                            </a>
                                        </div>
                                        <div class="media-body mt5 media-middle">
                                            <h4 class="media-heading font14 color3 text_overflow">{{item.name}}</h4>
                                            <div class="text-muted text_overflow font12">{{ item.hrPosition }}</div>
                                        </div>
                                    </div>
                                </el-option>
                            </el-select>
                            <button type="button" :disabled='addSupervisorUid==""?true:false'
                                    class="btn btn-primary ml16 height40" @click="addAuditor()"><?php echo Yii::t("directMessage", "Proceed to add"); ?></button>
                        </div>
                        <h4 class="h4-weight mb16"><?php echo Yii::t('teaching', 'Reviewer');?></h4>
                        <div v-if="Object.keys(allSupervisorList).length > 0" class="flex">
                            <div style="width: 40%" class="overflow-y height200 scroll-box" :style="'height:'+(height-100)+'px'">
                                <div class="flex-wrap auditor-div">
                                    <p v-for="(item,teacher_id) in allSupervisorList" :key="teacher_id"
                                       class="audit-setup-text with-100 pt10"
                                       :class="selectedSupervisor && teacher_id == selectedSupervisor.uid ? 'auditor-active' : ''"
                                    @click="chooseAuditor(teacher_id)" style="margin: 0">
                                        {{allSupervisorTeacherInfo[teacher_id]['name']}}
                                    </p>
                                </div>
                            </div>
                            <div class="ml30" style="width: 60%;" v-if="selectedSupervisor !='' ">
                                <div class="flex flex-align-item-center" >
                                    <img class="auditor—avatar" :src="allSupervisorTeacherInfo[selectedSupervisor.uid]['photoUrl']"/>
                                    <div  class="ml10">
                                        <span class="flex flex1 teacherName">
                                            {{allSupervisorTeacherInfo[selectedSupervisor.uid]['name']}}
                                        </span>
                                        <span style="color: #666666;line-height: 25px">
                                            {{allSupervisorTeacherInfo[selectedSupervisor.uid]['hrPosition']}}
                                        </span>
                                    </div>
                                    <div class="canceller">
                                        <a href="javascript:;" style="color:#D9534F;" @click="cancellerShow()"><?php echo Yii::t('global', 'Delete') ?></a>
                                    </div>
                                </div>

                                <div class="auditee-list-div">
                                    <div class="auditee-text">
                                        <span><?php echo Yii::t('teaching','Review reports from the below list')?></span>
                                        <span class="el-icon-edit pull-right pt4" style="color:#4D88D2;cursor: pointer; " @click="showAuditee()">&nbsp;<?php echo Yii::t('global','Add') ?></span>
                                    </div>
                                    <div class="auditee-list">
                                        <div v-for="(item,index) in allSupervisorList[selectedSupervisor.uid]" :key="index"
                                             v-if= "item.teacher_id"
                                             class="auditee-item">
                                            <img class="auditee—avatar mr10" referrerpolicy="no-referrer" :src="allSupervisorTeacherInfo[item.teacher_id]['photoUrl']"/>
                                            <span class="teacherName">{{allSupervisorTeacherInfo[item.teacher_id]['name']}}</span>
                                            <span class="el-icon-circle-close font16 auditee-item-close" @click="delAuditee(item.teacher_id)"></span>
                                        </div>
                                        <div class="with-100" v-else>
                                            <div class="alert alert-warning with-100" role="alert"><span class="glyphicon glyphicon-info-sign mr5"></span><?php echo Yii::t("teaching", "No Data"); ?></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-else class="with-100">
                            <div class="alert alert-warning with-100" role="alert"><span class="glyphicon glyphicon-info-sign mr5" ></span><?php echo Yii::t("teaching", "No Data"); ?></div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <!--添加被审核成员窗口-->
    <div class="modal fade" tabindex="-1" role="dialog" aria-labelledby="modal" id="addAuditeeTeacher" >
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title"><?php echo Yii::t('teaching', 'Add members');?></h4>
                </div>
                <div class="form-horizontal">
                    <div class="modal-body">
                        <div class="flex mt16 mb15">
                            <el-select
                                    class="select-width inline-input flex1 formControl"
                                    v-model="addAuditeeUid"
                                    filterable
                                    remote
                                    reserve-keyword
                                    clearable
                                    @visible-change="clearOptions($event)"
                                    placeholder="<?php echo Yii::t("directMessage", "Key words"); ?>"
                                    :remote-method="remoteMethod"
                                    :loading="loading">
                                <el-option
                                        class='optionSearch mb8'
                                        v-for="item in options"
                                        :key="item.uid"
                                        :label="item.name"
                                        :value="item.uid">
                                    <div class="media">
                                        <div class="media-left pull-left media-middle">
                                            <a href="javascript:void(0)">
                                                <img :src="item.photoUrl" data-holder-rendered="true" class="media-object img-circle imageSearch">
                                            </a>
                                        </div>
                                        <div class="media-body mt5 media-middle">
                                            <h4 class="media-heading font14 color3 text_overflow">{{item.name}}</h4>
                                            <div class="text-muted text_overflow font12">{{ item.hrPosition }}</div>
                                        </div>
                                    </div>
                                </el-option>
                            </el-select>
                            <button type="button" :disabled='addAuditeeUid==""?true:false'
                                    class="btn btn-primary ml16 height40" @click="addAuditee()"><?php echo Yii::t("directMessage", "Proceed to add"); ?></button>
                        </div>
<!--                        <div class="flex">-->
                            <div class="flex-wrap" style="justify-content: space-between;">
                                <div v-for="(item,index) in allSupervisorList[selectedSupervisor.uid]" :key="index"
                                     v-if= "item.teacher_id"
                                     class="add-auditee-item">
                                    <img class="auditee—avatar mr10" referrerpolicy="no-referrer" :src="allSupervisorTeacherInfo[item.teacher_id]['photoUrl']"/>
                                    <div  class="flex-wrap ml10" style="flex: 1">
                                        <span class="teacherName with-100">
                                            {{allSupervisorTeacherInfo[item.teacher_id]['name']}}
                                        </span>
                                        <span class="with-100" style="color: #666666;margin-top: 5px;">
                                            {{allSupervisorTeacherInfo[item.teacher_id]['hrPosition']}}
                                        </span>
                                    </div>
                                    <span class="el-icon-circle-close font16 auditee-item-close" @click="delAuditee(item.teacher_id)"></span>
                                </div>
                                <div class="with-100" v-else>
                                    <div class="alert alert-warning with-100" role="alert"><span class="glyphicon glyphicon-info-sign mr5" ></span><?php echo Yii::t("teaching", "No Data"); ?></div>
                                </div>
                            </div>
<!--                        </div>-->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 删除确认框 -->
    <div class="modal fade" id="delModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel" >
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="parentReplyLabel">
                        <span><?php echo Yii::t("newDS", "Delete");?></span>
                    </h4>
                </div>
                <div class="modal-body" >
                    <div><?php echo Yii::t("directMessage", "Proceed to remove?"); ?></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close");?></button>
                    <button type="button" class="btn btn-primary" @click='canceller()'><?php echo Yii::t("global", "OK");?></button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    var semester = <?php echo $_GET['semester']?>;
    var deadline = <?php echo $report['deadline']?>;
    if($('.navbar-fixed-bottom').length>0){
        var height=document.documentElement.clientHeight-$('.breadcrumb').outerHeight(true)-$('.nameOption').outerHeight(true)-140
    }else{
        var height=document.documentElement.clientHeight-$('.breadcrumb').outerHeight(true)-$('.nameOption').outerHeight(true)-89
    }
    var vendorAttendVue = new Vue({     //签到数据模型
        el: "#container",
        data: {
            showAuditorDataTeacher:'',//查看审核人数据
            showAuditorDataSupervisor:'',
            allSupervisorList:'',//所有审核人（审核人和被审核成员关系）
            allSupervisorTeacherInfo:'',//所有审核人和被审核成员的信息
            selectedSupervisor:'',//选中的审核人信息
            addSupervisorUid:'',//添加审核人的id
            addAuditeeUid:'',//添加被审核成员的uid
            options: [],
            loading: false,
            height:height,
            rightHeight:(height-22)/2,
        },
        mounted(){

        },
        methods: {
            clearOptions(e){
                var that=this
                if(e === false){
                    that.options = [];
                }
            },
            remoteMethod(query) {
                let that=this
                if (query !== '') {
                    this.loading = true;
                    $.ajax({
                        url: '<?php echo $this->createUrl("getSSTeacher") ?>',
                        type: "post",
                        dataType: 'json',
                        data: {
                            searchString:query
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.options = Object.values(data.data) ;
                            }else{
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                            that.loading = false;
                        },
                        error: function(data) {
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                            that.loading = false;
                        },
                    })
                } else {
                    this.options = [];
                }
            },
            //查看审核人
            showAuditor(){
                var that=this
                //查询
                $.ajax({
                    url: '<?php echo $this->createUrl("getOneSupervisor") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        teacherId: teacherId,
                    },
                    success: function (data) {
                        if(data.state == 'success'){
                            that.showAuditorDataTeacher = data.data.teacher_info
                            that.showAuditorDataSupervisor = data.data.supervisor_info
                            $("#viewReviewer").modal('show');
                        }
                    }
                })

            },
            //设置监管人页面
            setupSupervision(show){
                var that=this
                //查询所有审核人
                $.ajax({
                    url: '<?php echo $this->createUrl("getAllSupervisor") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {},
                    success: function (data) {
                        if(data.state == 'success'){
                            that.allSupervisorList = data.data.supervisor;
                            that.allSupervisorTeacherInfo = data.data.teacher_info;
                            if(show){
                                $("#ReviewerSettings").modal('show');
                            }
                        }
                    }
                })
            },
            //选择审核人
            chooseAuditor(teacher_id){
                var that=this
                that.selectedSupervisor = that.allSupervisorTeacherInfo[teacher_id]
            },
            //添加审核人
            addAuditor(){
                var that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("addSupervisor") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        teacher_id:that.addSupervisorUid
                    },
                    success: function (data) {
                        if(data.state == 'success'){
                            resultTip({"msg": "success!"});
                            that.setupSupervision(0)
                            that.addSupervisorUid = ''
                        }else{
                            resultTip({error : true,msg: data.message});
                        }
                    }
                })
            },
            //展示被审核成员modal
            showAuditee(){
                var that = this
                that.options = [];
                // $("#ReviewerSettings").modal('hide')
                $("#addAuditeeTeacher").modal('show');
            },
            //添加被审核成员
            addAuditee()
            {
                var that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("addAuditee") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        supervisor_id:that.selectedSupervisor.uid,
                        teacher_id:that.addAuditeeUid
                    },
                    success: function (data) {
                        if(data.state == 'success'){
                            resultTip({"msg": "success!"});
                            that.setupSupervision(0)
                            that.addAuditeeUid = ''
                        }else{
                            resultTip({error : true,msg: data.message});
                        }
                    }
                })
            },
            //删除被审核人
            delAuditee(teacher_id){
                var that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("delAuditee") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        supervisor_id:that.selectedSupervisor.uid,
                        teacher_id:teacher_id
                    },
                    success: function (data) {
                        if(data.state == 'success'){
                            resultTip({"msg": "success!"});
                            that.setupSupervision(0)
                        }else{
                            resultTip({error : true,msg: data.message});
                        }
                    }
                })
            },
            //取消审核人弹窗
            cancellerShow(){
                var that=this
                if(that.allSupervisorList[that.selectedSupervisor.uid].length>0 && that.allSupervisorList[that.selectedSupervisor.uid][0]['teacher_id']){
                    resultTip({error : true,msg: "<?php echo Yii::t('teaching','Please remove members first')?>"});
                    return false;
                }
                $("#delModal").modal('show');
            },
            //取消审核人
            canceller(){
                var that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("delSupervisor") ?>',
                    type: "post",
                    dataType: 'json',
                    data: {
                        supervisor_id:that.selectedSupervisor.uid,
                    },
                    success: function (data) {
                        if(data.state == 'success'){
                            resultTip({"msg": "success!"});
                            that.selectedSupervisor = '';
                            that.setupSupervision(0)
                            $("#delModal").modal('hide');
                        }else{
                            resultTip({error : true,msg: data.message});
                        }
                    }
                })
            },
        }
    });
</script>

<script type="text/template" id="student-template">
    <% var i=0; _.each(achievements[courseid], function(achievement, id, list) { var count=Object.keys(achievements[courseid]).length;%>
    <tr class="anole_<%= childid %>"  onmouseenter="changeBackgroundColor('<%= childid %>')" onmouseleave="resetBackgroundColor('<%= childid %>')">
        <% if(i == 0){ %>，
        <td rowspan="<%= count%>">
            <dl style="text-align: center;">
                <dt style="width:80px" class="visible-lg"><img class="img-rounded" style="width:100%" src=<%= childPhoto %> ></dt>
                <dt style="width:40px" class="hidden-lg"><img class="img-rounded" style="width:100%" src=<%= childPhoto %> ></dt>
                <dd style="padding-top:10px;text-align: center;">
                    <div><%= childname %></div>
                    <div class="font12 mt5" style='text-align:center'><span  style="height: 20px;width:50px ;line-height: 20px;background-color:#EBEDF0;border-radius: 2px;display:inline-block">#<%= childid %></span> </div>
                    <div class='mt10' style='display: flex;flex-wrap: wrap;align-items: center;align-content: center;justify-content: center;'>
                    <% _.each(childLabel, function(cchildLabel, rid, list) { %>
<!--                    <span  class="label flag_span hover-cursor" title="<%= cchildLabel.desc %>" style="padding:4px;background-color: rgb(<%= cchildLabel.color %>)"><%= cchildLabel.name %> <span class="iconfont"><%= cchildLabel.flag_text %></span></span>-->
                        <span  class="flag_span" title="<%= cchildLabel.desc %>" style="color: rgb(<%= cchildLabel.color %>)">
                            <span><%= cchildLabel.name %></span>
                            <span style="line-height:20px"><%= cchildLabel.flag_text %></span>
                        </span>
                    <% }); %>
                    </div>
                </dd>
            </dl>
        </td>
        <% }; %>
        <td class="td_center"><%= achievement.title %></td>


        <% _.each(report, function(creport, rid, list) { %>
        <td class=" td_center">
            <% if(creport.id != semester){ %>
            <%
            var cfid = '';
            if (reports[creport.id] && reports[creport.id][childid] && reports[creport.id][childid][courseid]) {
            cfid = reports[creport.id][childid][courseid][id];
            }
            if (creport.id != semester) {
            %>
            <%= achievement.fraction[cfid] %>
            <% } %>
            <% }else { %>
            <select class='form-control select_center height40' style="width:auto" name="" data-child-id="<%= childid %>"
                    id="ach_<%= creport.id + '_'+ childid +'_'+ achievement.id %>" <%= semester == creport.id ? '' :
            'disabled' %> >
            <option value="-">-</option>
            <% _.each(achievement.fraction, function(fraction, fid, list) { %>
            <%
            var selected = '';
            var cfid = '';
            if (reports[creport.id] && reports[creport.id][childid] && reports[creport.id][childid][courseid]) {
            cfid = reports[creport.id][childid][courseid][id];
            }
            if (cfid==fid) {
            selected = 'selected';
            }
            %>
            <option value="<%= fid %>"
            <%= selected %>><%= fraction %></option>
            <% }); %>
            </select>
            <% }; %>
        </td>
        <% }); %>
        <!-- 最终成绩水平 -->
        <% if(report[semester].cycle == 4){ %>
        <td class="td_center">
            <select class='form-control select_center height40' style="width:auto" name="<%= childid %>_item_score" data-child-id="<%= childid %>"
                    id="final_<%= semester + '_'+ childid +'_'+ achievement.id %>" onchange="calcFinal(<%= childid %>)">
                <option value="-">-</option>
                <% _.each(achievement.fraction, function(fraction, fid, list) { %>
                <%
                var selected = '';
                var cfid = '';
                if (finals[childid] && finals[childid][courseid] && finals[childid][courseid][id]) {
                cfid = finals[childid][courseid][id];
                }
                if (cfid==fid) {
                selected = 'selected';
                }
                %>
                <option value="<%= fid %>"
                <%= selected %>><%= fraction %></option>
                <% }); %>
            </select>
        </td>
        <% }; %>

        <% if(i == 0){ %>
        <% if(report[semester].type == 1){ %>
        <% if(!evaluationTemplate){ %>
            <!--没有模板配置的课程使用输入框-->
            <td rowspan="<%= count%>"><textarea class="form-control" onfocus="limitTextarea(this,'.state');" data-child-id="<%= childid %>"
                                                id="<%= childid %>_teacher_message" rows="9"><%= reports[semester][childid][courseid].teacher_message_cn %></textarea>
                <p class="text-right" style="display: none"><span class="state">0</span>/2000</p>
            </td>
        <% }else{%>
            <!--使用模板-->
            <td rowspan="<%= count%>" style="padding-top: 10px;padding-bottom: 10px;" class="table-responsive">
                <% if(evaluationTemplate['options'].length > 0){%>
                <div class="col-md-6">
                    <% _.each(evaluationTemplate['options'], function(item, key, list) { %>
                    <div class="mb5 <%if(key>0){%> mt10 <% }%> pl-2 font14"><%= item['title'] %></div>
                    <% if(item['options']){%>
                    <select class='form-control height40 select_center' id="<%= childid %>_<%= item['flag'] %>" data-child-id="<%= childid %>" onchange="plan_comment('<%= childid %>')">
                        <% _.each(item['options'],function(item2, key2, list2){%>
                        <option value="<%= item2['value'] %>" <%= reports[semester][childid][courseid]['template_data']!=null && reports[semester][childid][courseid]['template_data'][item['flag']] == item2['value'] ? 'selected' : '' %>   ><%= item2['title'] %></option>
                        <%})%>
                    </select>
                    <% }else{ %>
                    <textarea class="form-control" style="min-width: unset"  placeholder="teacher enters - past tense, no spaces" rows="3"  data-child-id="<%= childid %>"  id="<%= childid %>_<%= item['flag'] %>" oninput="plan_comment('<%= childid %>')"><%= reports[semester][childid][courseid]['template_data'] != null ? reports[semester][childid][courseid]['template_data'][ item['flag'] ] : '' %></textarea>
                    <% } %>
                    <% });%>
                </div>
                <div class="col-md-6">
                    <textarea class="form-control col-md-5 mt10 mb10" rows="25" style="min-width: unset" data-child-id="<%= childid %>" onfocus="limitTextarea(this,'.state');" id="<%= childid %>_teacher_message" ><%= reports[semester][childid][courseid]['teacher_message_cn'] %></textarea>
                    <p class="text-right" style="display: none"><span class="state">0</span>/2000</p>
                </div>
                <% }else{%>
                <div>
                    <!--只有评语没有选项内容-->
                    <textarea class="form-control col-md-5 mt10 mb10" rows="9" style="min-width: unset" data-child-id="<%= childid %>" onfocus="limitTextarea(this,'.state');" id="<%= childid %>_teacher_message"><%= reports[semester][childid][courseid]['teacher_message_cn'] %></textarea>
                    <p class="text-right" style="display: none"><span class="state">0</span>/2000</p>
                </div>
                <% }%>
            </td>
        <% } %>
        <% };i++; %>
        <!--  11 12年级去掉年终分数 -->
        <% if(report[semester].cycle == 4){ %>
            <% if(child_class_type=='e11' || child_class_type=='e12') {%>
                <td class="td_center" rowspan="<%= count%>" style="display: none">
            <%}else{%>
                <td class="td_center" rowspan="<%= count%>">
            <%}%>
            <select class='form-control height40' style="width:auto" id="<%= childid %>_total" data-child-id="<%= childid %>">
                <option value="-">-</option>
                <% for (var j=0;j<8;j++){ var sel = (totals[childid] != undefined && totals[childid][courseid] == j) ?
                'selected' : ''; %>
                <option
                <%= sel %> value="<%= j %>"><%= j %></option>
                <% }; %>
                <option
                <%= (totals[childid] != undefined) && (totals[childid][courseid]=='NA')?'selected':'' %>
                value="NA">NA</option>
                <option
                <%= (totals[childid] != undefined) && (totals[childid][courseid]=='NE')?'selected':'' %>
                value="NE">NE</option>
                <option
                <%= (totals[childid] != undefined) && (totals[childid][courseid]=='Pass')?'selected':'' %>
                value="Pass">Pass</option>
                <option
                <%= (totals[childid] != undefined) && (totals[childid][courseid]=='Fail')?'selected':'' %>
                value="Fail">Fail</option>
            </select>
        </td>
        <% };i++; %>
        <td rowspan="<%= count%>" class="text-center td_center">
            <p>
                <?php if($report['deadline'] == 1){?>
                <button type="button" disabled='disabled' class="btn btn-primary saveData" title="<?php echo Yii::t('global', 'Save'); ?>">
                    <?php echo Yii::t('global', 'Save'); ?>
                </button>
                <?php }else{?>
                <button type="button" onclick="saveData(this, <%= childid %>, <%= courseid %>)"
                        class="btn btn-primary saveData" title="<?php echo Yii::t('global', 'Save'); ?>">
                    <?php echo Yii::t('global', 'Save'); ?>
                </button>
                <?php }?>

            </p>
            <?php if($report['deadline'] == 1){?>
            <p class="deadline">
                <span class="el-icon-warning"></span>
                <?php echo Yii::t('principal',"The deadline for the report has passed, please contact the principal's Office.")?>
            </p>
            <?php }?>
            <p>
                <button class="btn btn-info"
                        onclick="reportPreview(<%= childid %>,<%= semester %>,<%= child_class_id %>,<%= yid %>)" title="<?php echo Yii::t('teaching', 'Preview'); ?>">
                    <?php echo Yii::t('teaching', 'Preview'); ?>
                </button>
            </p>
            <p style="color: #999999;font-size: 12px" class="save_status_<%= childid %>" id="statusText_<%= childid %>">
            </p>

            <% if (reportChildArr && reportChildArr[childid] && reportChildArr[childid].is_stat == 1 &&
            reportChildArr[childid].is_cache == 0) { %>
            <?php echo Yii::t('teaching', 'PDF being generated'); ?>
            <% } %>
        </td>
        <% };i++; %>
    </tr>
    <% }); %>
</script>
<script>
    $('#addAuditeeTeacher,#ReviewerSettings').on('hidden.bs.modal', function (e) {
        vendorAttendVue.options = [];
        vendorAttendVue.addSupervisorUid = '';//添加审核人的id
        vendorAttendVue.addAuditeeUid = '';//添加被审核成员的uid
    })
    $('#addAuditeeTeacher').on('hidden.bs.modal', function (e) {
        // $("#ReviewerSettings").modal('show')
    })

    var report = <?php echo CJSON::encode($report); ?>;

    var template = _.template($('#student-template').html());
    var courses = <?php echo CJSON::encode($data['courses']); ?>;
    var acourses = <?php echo CJSON::encode($data['acourses']); ?>;
    var students = <?php echo CJSON::encode($data['students']); ?>;
    var teacherId = '<?php echo $data['teacherId']; ?>';
    var childids;
    var courseid_global;
    var reports = <?php echo CJSON::encode($data['reports']); ?>;
    var totals = <?php echo CJSON::encode($data['totals']); ?>;
    var finals = <?php echo CJSON::encode($data['finals']); ?>;
    var achievements = <?php echo CJSON::encode($data['achievementArr']); ?>;
    var reportChildArr = <?php echo CJSON::encode($data['reportChildArr']); ?>;
    var table = $('#student-table tbody');
    var timetable_records_id;
    var timetable_records_code;
    var childTemp = [];//组装学生对应的教师评语模板数据
    function limitTextarea(self, nowleng) {
        $(self).on('input propertychange', function (event) {
            textareaAssist(self)
        });
    }

    function textareaAssist(self)
    {
        $(self).next().show();
        var _val = $(self).val();
        // _val = _val < 500 ? _val : _val.substr(0, 500);
        if (_val.length < 2000) {
            $(self).val(_val);
            $(self).next().find('.state').html(_val.length);
            $(self).next().css('color', '#b2b2b2');
        } else {
            var num = $(self).val().substr(0, 2000);
            $(self).val(num);
            $(self).next().find('.state').html(num.length);
            $(self).next().css('color', 'red');
        }
    }

    function chooseTeacher() {
        $("#mySelect a").remove();
        $("#chooseTeacher").css("pointer-events", "none");
        $.ajax({
            type: 'post',
            url: '<?php echo $this->createUrl('teacher');?>',
            dataType: 'json',
            data: {yid, semester}
        }).done(function (data) {
            $("#chooseTeacher").css("pointer-events", "auto");
            if (data.state == 'success') {
                if(Object.keys(data.data).length>0){
                    var html= '<a class="mb5 btn btn-default mr15" href="<?php echo Yii::app()->getController()->createUrl('/mteaching/report/new',array('classid' => 2, 'semester' => $_GET['semester']));?>">'+
                        '<?php echo $this->staff->getName();?><span class="label label-primary tagLabel ml4"><?php echo Yii::t('directMessage', 'Me'); ?></span>'+
                        '</a>';
                    $.each(data.data, function (_i, _v) {
                        html += '<a class="mb5 btn btn-default mr10" href="' + _v.url + '">' + _v.name + '</a> ';
                        $("#mySelect").append(html);
                        html = '';
                    });
                }else{
                    $("#mySelect").empty();
                    var html = '<div class="alert alert-warning with-100" role="alert"><span class="glyphicon glyphicon-info-sign mr5"></span><?php echo Yii::t('ptc','No Data') ?></div>'
                    $("#mySelect").append(html);
                }
                $("#remarks").modal('show');
            } else {
                resultTip({msg: data.message, error: 1});
            }
        });

    }

    function sortTea(list) {
        list.sort((x, y) => {
            if (x['childname'] && y['childname']) {
                return x['childname'].localeCompare(y['childname'])
            }
        })
        return list
    }

    function switchCourse(_this, course_id, course_code) {
        var aid = courses[course_id].report_course_id;
        if (aid == 0) {
            resultTip({msg: 'No criteria template assigned, please contact teaching administrator.', error: 1});
            return;
        }
        timetable_records_id = course_id;
        timetable_records_code = course_code;
        courseid_global = courses[course_id].report_course_id;
        if (students[course_id]) {
            $('#report_course_title').html('<span class="glyphicon glyphicon-book"></span> <strong style="font-size: 14px">' + acourses[aid].name + '<span class="badge" style="margin-left: 8px">' + Object.keys(students[course_id]).length + '</span></strong> ');
            $("#fill_in").show()
            $("#titleDeadline").show()
            tempId = 0
            <?php if(in_array($report[$this->selectedSemester]->cycle,array(2,4))){?>
            for (var temp_id in evaluationTemplateConfig['courseTemp']) {
                if(evaluationTemplateConfig['courseTemp'][temp_id].includes(course_code)){
                    tempId = temp_id;
                    continue;
                }
            }
            if(tempId){
                evaluationTemplate = evaluationTemplateConfig['tempData'][tempId];//模板信息
                import_map = evaluationTemplate['import_map']//导入数据字段对应关系
                tempOptions = evaluationTemplate['options']//模板所有的可选择项
                if(evaluationTemplateConfig['tempData'][tempId]['template_file'].length > 0){
                    $("#templateBtn").show()//展示模板下载 导入导出按钮
                }else{
                    $("#templateBtn").hide()
                }
            }else{
                evaluationTemplate = '';
                $("#templateBtn").hide()//展示模板下载 导入导出按钮
            }
            <?php }?>
        }
        $('#student-table').removeClass('hidden');
        table.empty();
        $('.list-group-item').removeClass('active');
        $(_this).addClass('active');
        var data = students[course_id];
        childids = [];
        var child_sort;
        if (!_.isNull(data)) {
            list = [];
            for (var key in data) {
                list.push({
                    id: key,
                    childname: data[key]['childname'],//childname
                    childLabel: data[key]['childLabel'],
                })
                // 只留一条学生的年级信息 11 12 年级的不显示年终分数
                grade = data[key]['child_class_type'];
            }
            child_sort = sortTea(list);
            $.each(child_sort, function (x, y) {
                if (y.id && y.childname) {
                    childids.push(y.id);
                    var view = template(data[y.id]);
                    table.append(view);
                }
            })
            if (grade == 'e11' || grade == 'e12') {
                $("#year_end_score").hide();
            } else {
                $("#year_end_score").show();
            }
            //只有评语没有选项的模板处理
            if(tempId){
                if(evaluationTemplate['options'].length === 0 ){
                    $.each(students[course_id], function (childId, item) {
                        if($('#' + childId + '_teacher_message').val().length===0){
                            plan_comment(childId,'switchCourse')
                        }
                    })
                }
            }

        }

        // 暂时隐藏
        // $('#student-table').bootstrapTable('destroy').bootstrapTable({
        //     showColumns: true,
        //     fixedColumns: true,
        //     fixedNumber: 2,
        //     fixedRightNumber: 1
        // })
        for (var i = 0; i < childids.length; i++) {
            calcFinalDisabled(childids[i])
        }
    }

    function saveData(btn, childid, courseid) {
        $(btn).attr("disabled", true);
        var total = $('#' + childid + '_total').val();
        if (total) {
            if (totals[childid] == undefined) {
                totals[childid] = [];
            }
            totals[childid][courseid] = total;
        }
        var teacherMessage = $('#' + childid + '_teacher_message').val();
        var studentMessage = $('#' + childid + '_student_message').val();
        if (reports[semester][childid] == undefined) {
            reports[semester][childid] = [];
            reports[semester][childid][courseid] = [];
        }
        reports[semester][childid][courseid].teacher_message_cn = teacherMessage;
        reports[semester][childid][courseid].student_message_cn = studentMessage;

        var fra = {};
        var fids = [];
        var final = {};
        var childTemp = {};
        _.each(achievements[courseid], function (fraction, fid, list) {
            fra[fid] = $('#ach_' + semester + '_' + +childid + '_' + fid).val();

            reports[semester][childid][courseid][fid] = fra[fid];
            fids.push(fid);
            final[fid] = $('#final_' + semester + '_' + +childid + '_' + fid).val();
            if (finals[childid] == undefined) {
                finals[childid] = [];
                finals[childid][courseid] = [];
            }
            if (finals[childid][courseid] == undefined) {
                finals[childid][courseid] = [];
            }
            finals[childid][courseid][fid] = final[fid];
        });
        <!--只有第二第四次评估且有模板配置的课程需要模板-->
        <?php if(in_array($report[$this->selectedSemester]->cycle,array(2,4))){?>
            var flagData = {};
            _.each(evaluationTemplate['options'],function (item, b, c){
                flagData[item['flag']] = $("#" + childid + "_" + item['flag']).val()
            })
            childTemp[childid] = flagData;
            _.each(evaluationTemplate['textarea'],function (item, b, c){
                flagData[item['flag']] = $("#" + childid + "_" + item['flag']).val()
            })
            childTemp[childid] = flagData
        <?php }?>

        $.ajax({
            type: 'post',
            url: '<?php echo $this->createUrl('saveChildCourseReport');?>',
            dataType: 'json',
            data: {
                semester,
                childid,
                courseid,
                teacherMessage,
                studentMessage,
                fra,
                fids,
                total,
                timetable_records_id,
                timetable_records_code,
                teacherId,
                final,
                childTemp
            }
        }).done(function (data) {
            if (data.state == 'success') {
                $("#statusText_"+childid).text('')
                resultTip({msg: data.message});
            } else {
                resultTip({msg: data.message, error: 1});
            }
            $(btn).attr("disabled", false);
        });
    }

    function saveAll(i) {
        $('.modal-body p').text('');
        $('#process-modal').modal('show');
        $('.modal-footer button').attr('disabled', true);
        $('.modal-title').text('<?php echo Yii::t('teach', 'Save All');?>');
        var childid = childids[i];
        var courseid = courseid_global;
        var total = $('#' + childid + '_total').val();
        if (total) {
            if (totals[childid] == undefined) {
                totals[childid] = [];
            }
            totals[childid][courseid] = total;
        }
        var teacherMessage = $('#' + childid + '_teacher_message').val();
        var studentMessage = $('#' + childid + '_student_message').val();
        if (reports[semester][childid] == undefined || reports[semester][childid][courseid] == undefined) {
            reports[semester][childid] = [];
            reports[semester][childid][courseid] = [];
        }

        reports[semester][childid][courseid].teacher_message_cn = teacherMessage;
        reports[semester][childid][courseid].student_message_cn = studentMessage;

        var fra = {};
        var fids = [];
        var final = {};
        var childTemp = {};
        _.each(achievements[courseid], function (fraction, fid, list) {
            fra[fid] = $('#ach_' + semester + '_' + +childid + '_' + fid).val();
            reports[semester][childid][courseid][fid] = fra[fid];
            fids.push(fid);
            final[fid] = $('#final_' + semester + '_' + +childid + '_' + fid).val();
            if (finals[childid] == undefined) {
                finals[childid] = [];
                finals[childid][courseid] = [];
            }
            if (finals[childid][courseid] == undefined) {
                finals[childid][courseid] = [];
            }
            finals[childid][courseid][fid] = final[fid];
        });
        <!--只有第二第四次评估且有模板配置的课程需要模板-->
        <?php if(in_array($report[$this->selectedSemester]->cycle,array(2,4))){?>
            var flagData = {};
            _.each(evaluationTemplate['options'],function (item, b, c){
                flagData[item['flag']] = $("#" + childid + "_" + item['flag']).val()
            })
            childTemp[childid] = flagData;
            _.each(evaluationTemplate['textarea'],function (item, b, c){
                flagData[item['flag']] = $("#" + childid + "_" + item['flag']).val()
            })
            childTemp[childid] = flagData
        <?php }?>

        $.ajax({
            type: 'post',
            url: '<?php echo $this->createUrl('saveChildCourseReport');?>',
            dataType: 'json',
            data: {
                semester,
                childid,
                courseid,
                teacherMessage,
                studentMessage,
                fra,
                fids,
                total,
                timetable_records_id,
                timetable_records_code,
                teacherId,
                final,
                childTemp
            }
        }).done(function (data) {
            $("#statusText_"+childid).text('')
            var percent = ((i + 1) / (childids.length)) * 100;
            $('.progress-bar').attr('aria-valuenow', percent).css('width', percent + '%');
            $('.complete').text(i + 1 + '/' + (childids.length));
            if ((i + 1) == childids.length) {
                $('.modal-body p').text(data.message);
                $('.modal-footer button').attr('disabled', false);
                return true;
            }
            if (childids[i + 1])
                return saveAll(i + 1);
        });
    }

    function reportPreview(childid, report_id, classid, yid) {
        var url = "<?php echo $this->createUrl('newPreviewReportDs', array(
            'childid' => '-childid-',
            'report_id' => '-report_id-',
            'classid' => '-classid-',
            'yid' => '-yid-',
        ));?>";
        url = url.replace('-childid-', childid);
        url = url.replace('-report_id-', report_id);
        url = url.replace('-classid-', classid);
        url = url.replace('-yid-', yid);
        window.open(url);
    }
    var evaluationTemplateConfig = <?php echo CJSON::encode($data['evaluationTemplate']); ?>;
    var evaluationTemplate;//匹配到的模板信息
    var import_map;//导入数据字段对应关系
    var tempOptions;//模板所有的可选择项
    var tempId;//模板id
    var unsavedText = '<?php echo  Yii::t('teaching', 'Changes not saved');?>';
    //评语 拼接 that.ptcPlanDefComment
    function plan_comment(child_id,type){
        let comment = evaluationTemplate['template']; //评语模板
        let options = tempOptions;//可填写的评语项
        comment = comment.replace(/%name%/g, students[timetable_records_id][child_id]['nick'])
        comment = comment.replace(/%child_name%/g, students[timetable_records_id][child_id]['child_name'])
        for (const optionKey in options) {
            let flag = options[optionKey]['flag']
            let isOption = options[optionKey]['options']
            let isTextarea = options[optionKey]['textarea']
            let flagData = $("#"+child_id+"_"+flag).val()
            let optionTemp = '';
            if(isOption){
                optionTemp = options[optionKey]['options'][flagData]['template']
            }
            if(isTextarea){
                optionTemp = flagData
            }
            comment = comment.replace("%"+flag+"%", optionTemp)
        }
        $("#"+child_id+"_teacher_message").val(comment)
        textareaAssist($("#"+child_id+"_teacher_message")[0])
        $("#statusText_"+child_id).text(unsavedText)
    }
    function downloadTemplate(){
        var report_course_id = courseid_global;
        var course_id = timetable_records_id;
        var course_code = timetable_records_code;
        let url='<?php echo $this->createUrl('report/downloadTemplate', array('branchId' => $this->branchId,'is_template'=>0)); ?>'+'&report_course_id='+report_course_id+'&course_id='+course_id+'&course_code='+course_code+'&teacher_id='+teacherId+'&semester='+semester
        downloadExcel(url)
    }
    function daochu(){
        var report_course_id = courseid_global;
        var course_id = timetable_records_id;
        var course_code = timetable_records_code;
        let url='<?php echo $this->createUrl('report/downloadTemplate', array('branchId' => $this->branchId,'is_template'=>1)); ?>'+'&report_course_id='+report_course_id+'&course_id='+course_id+'&course_code='+course_code+'&teacher_id='+teacherId+'&semester='+semester
        downloadExcel(url)
    }
    function uploadPlan(){
        var file = document.querySelector("#i-file").files[0];
        //可重复传同一文件
        $("#i-file").val('')
        if(!file){
            return false;
        }
        let file_name = file.name
        let xlx_child_ids = [] //需要导入的所有孩子的id
        var type = file.name.split('.');
        if (type[type.length - 1] !== 'xlsx' && type[type.length - 1] !== 'xls') {
            resultTip({error: 'warning', msg: '只能选择xlsx或者xls文件导入'});
            that.loading_read = false
            return false;
        }
        const reader = new FileReader();
        reader.readAsBinaryString(file);
        reader.onload = (e) => {
            const data = e.target.result;
            const my_excel = window.XLS.read(data, {
                type: 'binary'
            });
            const sheet2JSONOpts = {
                defval: ''//给defval赋值为空的字符串
            }
            let xlx_json = window.XLS.utils.sheet_to_json(my_excel.Sheets[my_excel.SheetNames[0]],sheet2JSONOpts);
            for (const xlxJsonKey in xlx_json) {
                let column = 0;
                let child_id = 0
                for (const key in xlx_json[xlxJsonKey]) {
                    //表格数据检查
                    if(column === 0){
                        if(xlx_json[xlxJsonKey][key]){
                            child_id = $.trim(xlx_json[xlxJsonKey][key])
                            xlx_child_ids.push(child_id)
                            // resultTip({error: 'warning', msg: 'student id null'});
                            // return ;
                        }
                    }
                    //转换成 A B C 列
                    let excel_col = String.fromCharCode(65 + parseInt(column));
                    let flag = import_map[excel_col]
                    let filteredData = tempOptions.find(item => item.flag === flag);
                    if(xlx_json[xlxJsonKey][key] !== ''){
                        let input_data = xlx_json[xlxJsonKey][key]
                        if(typeof filteredData !== 'undefined'){
                            if(filteredData['options']){
                                var filteredData2 = filteredData['options'].find(item => item.title === input_data)
                                if(typeof filteredData2 !== 'undefined'){
                                    $("#"+child_id+"_"+flag).val(filteredData2['value'])
                                }
                            }
                            if(filteredData['textarea']){
                                $("#"+child_id+"_"+flag).val(input_data)
                            }
                        }
                    }
                    column++
                }
                if(child_id > 0){
                    plan_comment(child_id)
                }
            }
        }
    }
    function downloadExcel(url){
        let link = document.createElement('a');
        link.href = url;
        link.download = '';
        link.click();
        setTimeout(function() {
            // 延时释放掉obj
            URL.revokeObjectURL(link.href);
            link.remove();
        }, 500);
    }
    function changeBackgroundColor(childid) {
        $(".anole_"+childid).css('background-color', '#FAFAFA');//修改背景色
    }
    function resetBackgroundColor(childid) {
        $(".anole_"+childid).css('background-color', '');// 恢复背景色为默认值
    }
    // 监听父元素上的input、textarea和select元素的变化
    document.addEventListener('input', function(event) {
        const target = event.target;
        if (target.matches('textarea, select')) {
            const childId = target.dataset.childId; // 假设孩子的ID存储在data-child-id属性中
            $("#statusText_"+childId).text(unsavedText)
        }
    });

    function calcFinal(childId) {
        // 先判断 $(`#${childId}_total`) 的父类有没有隐藏
        if($(`#${childId}_total`).parent().css('display') == 'none'){
            return;
        }
        let itemName = `${childId}_item_score`;

        // 遍历所有 name 为 _item_score 的 select
        let total = 0;
        let flag = false;
        let flagText = '';
        let standardNum = 0;
        $(`select[name=${itemName}]`).each(function(index, item) {
            standardNum++;
            let selectedText = $(item).find("option:selected").text() || "";
            let score = parseInt(selectedText);
            if (score >= 0) {
                total += score;
            } else {
                flag = true;
                flagText = selectedText;
            }
        });
        if(flag){
            $(`#${childId}_total`).prop('disabled', false);
            $(`#${childId}_total`).val(flagText)
        }else{
            let final = 0;
            let level1 = 6
            let level2 = 10
            let level3 = 15
            let level4 = 19
            let level5 = 24
            let level6 = 28
            if(standardNum == 3){
                level1 = 4
                level2 = 7
                level3 = 11
                level4 = 14
                level5 = 18
                level6 = 21
            }
            if (total >= level6) {
                final = 7
            } else if (total >= level5) {
                final = 6
            } else if (total >= level4) {
                final = 5
            } else if (total >= level3) {
                final = 4
            } else if (total >= level2) {
                final = 3
            } else if (total >= level1) {
                final = 2
            } else {
                final = 1
            }
            
            $(`#${childId}_total`).prop('disabled', true);
            $(`#${childId}_total`).val(final)
        }
    }
    
    function calcFinalDisabled(childId) {
        // 先判断 $(`#${childId}_total`) 的父类有没有隐藏 display: none
        if($(`#${childId}_total`).parent().css('display') == 'none'){
            return;
        }
        let itemName = `${childId}_item_score`;

        // 遍历所有 name 为 _item_score 的 select
        let total = 0;
        let flag = false;
        let flagText = '';
        $(`select[name=${itemName}]`).each(function(index, item) {
            let selectedText = $(item).find("option:selected").text() || "";
            let score = parseInt(selectedText);
            if (score >= 0) {
                total += score;
            } else {
                flag = true;
            }
        });
        if(flag){
            $(`#${childId}_total`).prop('disabled', false);
        }else{
            $(`#${childId}_total`).prop('disabled', true);
        }
    }
</script>
