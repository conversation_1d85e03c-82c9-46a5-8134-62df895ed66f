<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo Yii::t('site','Finance Management');?></li>
        <li class="active">课后课财务</li>
    </ol>

    <div class="row">
        <!-- 左侧菜单 -->
        <div class="col-md-2 col-sm-2 mb10">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->getMenu(),
                'id' => 'pageCategory',
                'htmlOptions' => array('class' => 'nav nav-pills nav-stacked background-gray'),
                'activeCssClass' => 'active',
                'itemCssClass' => ''
            ));
            ?>
        </div>

        <div class="col-md-10 col-sm-12">
            <?php
            $this->widget('zii.widgets.CMenu', array(
                'items' => $this->ExpenseMen(),
                'id' => 'AttendMenu',
                'htmlOptions' => array('class' => 'nav nav-tabs'),
                'activeCssClass' => 'active',
                'itemCssClass' => ''
            ));?>
            <div class="mb10 col-sm-1 row">
            </div>
            <div class="mb10 row">
                <!-- 搜索框 -->
                <form  class="" style="float: left;width: 100%" action="<?php echo $this->createUrl('expenseHistory'); ?>" method="get">
                    <div class="col-sm-2 form-group">
                        <input type="text" class="form-control" name="username" placeholder="申请人姓名" value="<?php echo Yii::app()->request->getParam('username','')?Yii::app()->request->getParam('username',''):''; ?>">
                    </div>
                    <div class="col-sm-2 form-group">
                        <?php echo Chtml::dropDownList('branch',$_GET['branch'], $schoolsName,array('class'=>'form-control','empty' => '请选择学校')); ?>
                    </div>
                    <div class="col-sm-2 form-group">
                        <?php echo Chtml::dropDownList('type',$_GET['type'], array(1 => '报销', 2 => '付款'),array('class'=>'form-control','empty' => '请选择申请类型')); ?>
                    </div>
                    <div class="col-sm-2 form-group">
                        <?php echo Chtml::textField('starTime',$_GET['starTime'], array('class'=>'form-control datepicker', "placeholder"=>"开始时间")); ?>
                    </div>
                    <div class="col-sm-2 form-group">
                        <?php echo Chtml::textField('starEnd',$_GET['starEnd'], array('class'=>'form-control datepicker', "placeholder"=>"结束时间")); ?>
                    </div>
                    <div class="">
                        <div class="">
                            <button class="btn btn-default ml5" type="submit"><span class="glyphicon glyphicon-search"> </span> </button>
                        </div>
                    </div>
                </form>
            </div>
            <?php
            $this->widget('ext.ivyCGridView.BsCGridView', array(
                'id' => 'expense',
                'afterAjaxUpdate' => 'js:head.Util.modal',
                'dataProvider' => $expenseModel,
                'template' => "{items}{pager}",
                //状态为无效时标红
                //'rowCssClassExpression' => '( $data->status == 0 ? "active" : "" )',
                'colgroups' => array(
                    array(
                        "colwidth" => array(100, 100, 50, 100, 50, 100, 100, 100, 50),
                    )
                ),
                'columns' => array(
                    array(
                        'name' => 'voucherid',
                        'value' => 'sprintf("%08d",$data->id)',
                    ),
                    array(
                        'name' => 'title',
                        'value' => '$data->title',
                    ),
                    array(
                        'name' => 'type',
                        'value' => '$data->type == 1 ? "报销" : "付款"',
                    ),
                    array(
                        'name' => 'branch',
                        'value' => array($this, "getBranch"),
                    ),
                    array(
                        'name' => 'amount',
                        'value' => '$data->amount',
                    ),
                    array(
                        'name' => 'expense_uid',
                        'value' => array($this, "getUserName"),
                    ),
                    array(
                        'name' => 'created',
                        'value' => 'date("Y-m-d", $data->created)',
                    ),                    
                    array(
                        'name' => 'updated',
                        'value' => 'date("Y-m-d", $data->updated)',
                    ),
                    array(
                        'name' => Yii::t('asa','操作'),
                        'value' => array($this, "getExpenseButton"),
                    ),
                ),
            ));
            ?>
        </div>
    </div>
</div>
<script>
    $('.datepicker').datepicker({
        changeMonth: true,
        changeYear: true,
        dateFormat:'yy-mm-dd'
    });

    var modal = '<div class="modal fade" id="modal" class="1123" tabindex="-1" role="dialog" aria-labelledby="modal"><div class="modal-dialog modal-lg" role="document"><div class="modal-content"></div></div></div>';
    $('body').append(modal);

    function cbUpdateExpense(){
        $('#modal').modal('hide');
        $.fn.yiiGridView.update('expense');
    }
</script>