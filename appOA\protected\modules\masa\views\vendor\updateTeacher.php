<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
    </button>
    <h4 class="modal-title"
        id="exampleModalLabel"><?php echo $model->vendor_id ? Yii::t('asa', 'Edit Vendor') : Yii::t('asa', 'Add Vendor'); ?></h4>
</div>
<?php $form = $this->beginWidget('CActiveForm', array(
    'id' => 'course-form',
    'enableAjaxValidation' => false,
    'htmlOptions' => array('class' => 'J_ajaxForm form-horizontal', 'role' => 'form'),
)); ?>
<div class="modal-body">

    <div class="alert alert-info">
        <ul>
            <li><?php echo Yii::t("asa", "In-hourse staff can only be selected from the exsistence list."); ?></li>
            <li><?php echo Yii::t("asa", "Input the CN and EN the same content if the name is not in bilingual."); ?></li>
        </ul>
    </div>


    <ul class="nav nav-tabs" id="myTab">
        <input type="hidden" id="keys" value="<?php echo $model->type; ?>"/>
        <?php foreach ($typeList['type'] as $key => $type) {
            ; ?>
            <li id='#tab<?php echo $key; ?>'>
                <a href='#tab<?php echo $key; ?>' role="tab" checktab='tab<?php echo $key; ?>' checknum='$key'
                   data-toggle="tab" class="tabs">
                    <?php echo Yii::app()->language == "zh_cn" ? $type['cn'] : $type['en']; ?>
                </a></li>
        <?php } ?>
    </ul>
    <div class="row">
        <div class="tab-content" style="padding: 14px">
            <div class="tab-pane" id="tab1">

                <div class="col-md-12">
                    <div class="form-group">
                        <input type="hidden" id="teacherId" name="teacherId" value="<?php echo $model->ivy_uid; ?>">
                        <input type="text" id="project" name="project" class="form-control"
                               placeholder="<?php echo Yii::t("asa", "Input to filter, press down key(↓) to list all."); ?>"
                               value="<?php echo $model->getName(); ?>">
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="form-group">
                        <input type="text" class="form-control" name="teacherPhone" id="teacherPhone"
                               readonly="readonly"
                               placeholder="<?php echo $model->getAttributeLabel('cellphone'); ?>"
                               value="<?php echo $model->cellphone; ?>">
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="form-group">
                        <input type="text" class="form-control innerTeacherEmail" name="teacherEmail" id="teacherEmail"
                               readonly="readonly"
                               placeholder="<?php echo $model->getAttributeLabel('email'); ?>"
                               value="<?php echo $model->email; ?>">
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="form-group">
                        <select class="form-control status" name="active" disabled>
                            <option value="1" <?php if ($model->active == 1) {
                                echo 'selected';
                            } ?>>
                                <?php echo Yii::t("asa", "Active"); ?></option>
                            <option value="0" <?php if ($model->active == 0) {
                                echo 'selected';
                            } ?>>
                                <?php echo Yii::t("asa", "Inactive"); ?></option>
                        </select>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="form-group">
                        <div class="checkbox">
                            <label>
                                <?= CHtml::checkBox('fee_merge_1', $model->fee_merge); ?> 费用合并（IVY KG 使用）
                            </label>
                        </div>
                    </div>
                </div>
                <input type="hidden" name="teacherType" value="1"/>
            </div>

            <div class="tab-pane" id="tab2">
                <div role="tabpanel" class="col-md-12">
                    <div class="form-group">
                        <input type="hidden" name="teacherId" disabled>
                        <input type="text" name="name_cn" class="form-control" disabled
                               placeholder="<?php echo Yii::t("asa", "Name of the 3rd-party Company (CN)"); ?>"
                               value="<?php echo $model->name_cn; ?>">
                    </div>
                </div>
                <div role="tabpanel" class="col-md-12">
                    <div class="form-group">
                        <input type="hidden" name="teacherId" disabled>
                        <input type="text" name="name_en" class="form-control" disabled
                               placeholder="<?php echo Yii::t("asa", "Name of the 3rd-party Company (EN)"); ?>"
                               value="<?php echo $model->name_en; ?>">
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="form-group">
                        <input type="text" class="form-control" name="teacherPhone" disabled
                               placeholder="<?php echo $model->getAttributeLabel('cellphone'); ?>"
                               value="<?php echo $model->cellphone; ?>">
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="form-group">
                        <input type="text" class="form-control" name="teacherEmail" disabled
                               placeholder="<?php echo $model->getAttributeLabel('email'); ?>"
                               value="<?php echo $model->email; ?>">
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="form-group">
                        <select class="form-control status" name="active" disabled>
                            <option value="1" <?php if ($model->active == 1) {
                                echo 'selected';
                            } ?>>
                                <?php echo Yii::t("asa", "Active"); ?></option>
                            <option value="0" <?php if ($model->active == 0) {
                                echo 'selected';
                            } ?>>
                                <?php echo Yii::t("asa", "Inactive"); ?></option>
                        </select>
                    </div>
                </div>
                <input type="hidden" name="teacherType" value="2" disabled/>
            </div>

            <div class="tab-pane" id="tab3">
                <div role="tabpanel" class="col-md-12">
                    <div class="form-group">
                        <input type="hidden" name="teacherId" disabled>
                        <input type="text" name="name_cn" class="form-control" disabled
                               placeholder='<?php echo Yii::t("asa", "Name of freelance staff (CN)"); ?>'
                               value="<?php echo $model->name_cn; ?>">
                    </div>
                </div>
                <div role="tabpanel" class="col-md-12">
                    <div class="form-group">
                        <input type="hidden" name="teacherId" disabled>
                        <input type="text" name="name_en" class="form-control" disabled
                               placeholder='<?php echo Yii::t("asa", "Name of freelance staff (EN)"); ?>'
                               value="<?php echo $model->name_en; ?>">
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="form-group">
                        <input type="text" class="form-control" name="teacherPhone" disabled
                               placeholder="<?php echo $model->getAttributeLabel('cellphone'); ?>"
                               value="<?php echo $model->cellphone; ?>">
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="form-group">
                        <input type="text" class="form-control" name="teacherEmail" disabled
                               placeholder="<?php echo $model->getAttributeLabel('email'); ?>"
                               value="<?php echo $model->email; ?>">
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="form-group">
                        <select class="form-control status" name="active" disabled>
                            <option value="1" <?php if ($model->active == 1) {
                                echo 'selected';
                            } ?>>
                                <?php echo Yii::t("asa", "Active"); ?></option>
                            <option value="0" <?php if ($model->active == 0) {
                                echo 'selected';
                            } ?>>
                                <?php echo Yii::t("asa", "Inactive"); ?></option>
                        </select>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="form-group">
                        <div class="checkbox">
                            <label>
                                <?= CHtml::checkBox('fee_merge_3', $model->fee_merge); ?> 费用合并（IVY KG 使用）
                            </label>
                        </div>
                    </div>
                </div>
                <input type="hidden" name="teacherType" value="3" disabled/>

            </div>
        </div>
    </div>
</div>
<div class="modal-footer">
    <label id="J_fail_info" class="text-warning" style="display: none;"><i
            class="glyphicon glyphicon-remove text-warning"></i><span></span></label>
    <button type="button" class="btn btn-primary J_ajax_submit_btn">
        <?php echo $model->vendor_id ? Yii::t('global', 'Update') : Yii::t('global', 'Save'); ?></button>
    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Close"); ?></button>
    <input type="reset" class="_reset" style="display: none"/>
</div>

<script>
    var projects = <?php echo CJSON::encode($teacherList);?>;

    $("#project").autocomplete({
        minLength: 0,
        source: projects,
        focus: function (event, ui) {
            $("#project").val(ui.item.value);
            $("#teacherPhone").val(ui.item.teacherPhone).removeAttr('readonly');
            $(".status").removeAttr('disabled');

            if (ui.item.teacherEmail !== '') {
                $("#teacherEmail").val(ui.item.teacherEmail).attr('readonly', 'readonly');
            } else {
                $("#teacherEmail").val('').removeAttr('readonly');
            }
            return false;
        },
        select: function (event, ui) {
            $("#project").val(ui.item.value);
            $("#teacherId").val(ui.item.teacherId);
            return false;
        }
    });


    <?php if($model->vendor_id == 0){?>
    //表示点击添加老师时触发事件
    $(function () {
        $('#myTab a:first').tab('show');
    });
    <?php }else{?>

    //表示点击编辑老师时触发的事件
    $(function () {
        var val = $('#keys').val();
        $('#myTab > li').eq(val - 1).addClass('active');
        $('#myTab > li').eq(val).addClass('hidden');
        $('#myTab > li').eq(val - 2).addClass('hidden');
        $('#myTab > li').eq(val - 3).addClass('hidden');
        $('.tab-content > .tab-pane').eq(val - 1).addClass('active');
        $('.tab-content > .tab-pane').eq(0).find('input,select').attr('disabled', 'disabled');
        $('.tab-pane.active').find('input,select').removeAttr('disabled');
        $('.tab-pane.active').find('input,select').removeAttr('readonly');
        $('.tab-pane.active .innerTeacherEmail').attr('readonly','readonly');


    });
    <?php }?>

    $('.tab-pane' + 'input').attr('disabled', 'disabled');

    $('.tab-pane' + 'option').attr('disabled', 'disabled');
    $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
        var checktabname = $(e.target).attr('checktab');// 激活的标签页
        $('#' + checktabname + ' :input').removeAttr('disabled');
        if (checktabname == 'tab1') {
            $('#' + checktabname + ' select').attr('disabled', 'disabled');
        }
        var checkdtabname = $(e.relatedTarget).attr('checktab');// 前一个激活的标签页
        $('#' + checkdtabname + ' :input').attr('disabled', 'disabled');
    })

</script>

<?php $this->endWidget(); ?>