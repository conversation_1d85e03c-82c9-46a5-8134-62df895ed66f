<?php
Yii::import('zii.widgets.grid.CGridView');
class IvyCGridView extends CGridView
{
    public $cssFile = false;
    public $htmlOptions=array('class'=>'table_list grid-view');
    public $template="{items}\n{pager}";
    public $rowCssClassExpression = "";
    public $colgroups = array();
    
    public function renderItems()
	{
		if($this->dataProvider->getItemCount()>0 || $this->showTableOnEmpty)
		{
			echo "<table width=\"100%\" id=\"J_table_list\" class=\"{$this->itemsCssClass}\" >\n";
            $this->renderColgroup();
			$this->renderTableHeader();
			ob_start();
			$this->renderTableBody();
			$body=ob_get_clean();
			$this->renderTableFooter();
			echo $body; // TFOOT must appear before TBODY according to the standard.
			echo "</table>";
		}
		else
			$this->renderEmptyText();
	}
       
    public function renderColgroup()
    {
        if ( !empty($this->colgroups) ){
            foreach ($this->colgroups as $colg){
                echo CHtml::openTag('colgroup', isset($colg['htmlOptions'])?$colg['htmlOptions']:array());
                foreach ($colg['colwidth'] as $col){
                    if ($col){
                        $colOptions['width'] = $col;
                    }
                    echo CHtml::tag('col', $colOptions);
                }
            }
            echo CHtml::closeTag('colgroup');
        }
    }
}