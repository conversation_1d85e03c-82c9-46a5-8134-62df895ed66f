<!--校历模版ITEM显示-->
<script type="text/template" id="calendar-item-template">
    <div class="panel panel-default" calendarid=<%= yid %>>
        <div class="panel-heading">
            <span class="dropdown">
                <a href="javascript:void(0)" calendar-dropdown data-toggle="dropdown"> <span class="glyphicon glyphicon-th-list"></span></a>
            </span>
            <% print(calendarData[yid].title); %>
        </div>
        <div class="panel-body">
            <%= campuses %>
            <div class="pt10 dropdown assign-campus">
                <button type="button" class="btn btn-default" data-toggle="dropdown" onclick="showUnassignedCampus(this)">
                    <span class="glyphicon glyphicon-plus"></span>
                </button>
                <ul class="dropdown-menu">
                </ul>
            </div>
        </div>
    </div>
</script>

<!--模版所分配至校园的ITEM：该模版当前未使用-->
<script type="text/template" id="calendar-campusNo-template">
    <span class="dropdown mr10" data-schoolid="<%= branchid%>">
        <a style="display: inline-block" href="javascript:void()" campus-dropdown data-toggle="dropdown"><% print(branchData[branchid]);%><span class="caret"></span></a>
    </span>
</script>

<!--模版所分配至校园的ITEM：该模版当前在使用-->
<script type="text/template" id="calendar-campusYes-template">
    <span class="dropdown mr10" data-schoolid="<%= branchid%>">
        <a style="display: inline-block" href="javascript:void()" campus-dropdown data-toggle="dropdown" class="text-danger">
            <span class="glyphicon glyphicon-star" title="当前使用"></span>
        <% print(branchData[branchid]);%><span class="caret"></span></a>
    </span>
</script>

<!--模版基本信息-->
<script type="text/template" id="calendar-basicEdit-template">
    <div class="form-group" model-attribute="startyear">
        <?php echo CHtml::label($clabels['startyear'], CHtml::getIdByName('Calendar[startyear]'), array('class'=>'col-sm-2 control-label'));?>
        <div class="col-sm-10">
            <?php echo CHtml::dropDownList('Calendar[startyear]','<%= startyear %>',$optionYears, array('class'=>'form-control select_3','onchange'=>'startYearChange(this)'));?>
        </div>
    </div>
    <div class="form-group" model-attribute="title">
        <?php echo CHtml::label($clabels['title'], CHtml::getIdByName('Calendar[title]'), array('class'=>'col-sm-2 control-label'));?>
        <div class="col-sm-10">
            <?php echo CHtml::textField('Calendar[title]','<%= title %>', array('encode'=>false,'class'=>'form-control length_5')); ?>
        </div>
    </div>
    <div class="form-group" model-attribute="firstSemester">
        <?php echo CHtml::label($clabels['firstSemester'], CHtml::getIdByName('Calendar[firstSemester]'), array('class'=>'col-sm-2 control-label'));?>
        <div class="col-sm-10 form-inline">
            <?php echo CHtml::textField('Calendar[firstSemester][0]','<%= firstSemester[0] %>', array('encode'=>false,'class'=>'form-control length_2 mb5','original'=>'<%= firstSemester[0] %>','semester'=>'1')); ?> 至
            <?php echo CHtml::textField('Calendar[firstSemester][1]','<%= firstSemester[1] %>', array('encode'=>false,'class'=>'form-control length_2 mb5','original'=>'<%= firstSemester[1] %>','semester'=>'1')); ?>
            <a onclick="enableEdit(this)" semester="1" enableEdit="Calendar[firstSemester][]" href="javascript:void(0);"> <span class="glyphicon glyphicon-pencil"></span></a>
        </div>
    </div>
    <div class="form-group" model-attribute="secondSemester">
        <?php echo CHtml::label($clabels['secondSemester'], CHtml::getIdByName('Calendar[secondSemester]'), array('class'=>'col-sm-2 control-label'));?>
        <div class="col-sm-10 form-inline">
            <?php echo CHtml::textField('Calendar[secondSemester][0]','<%= secondSemester[0] %>', array('encode'=>false,'class'=>'form-control length_2 mb5','original'=>'<%= firstSemester[2] %>','semester'=>'2')); ?> 至
            <?php echo CHtml::textField('Calendar[secondSemester][1]','<%= secondSemester[1] %>', array('encode'=>false,'class'=>'form-control length_2 mb5','original'=>'<%= firstSemester[3] %>','semester'=>'2')); ?>
            <a onclick="enableEdit(this)" semester="2" enableEdit="Calendar[secondSemester][]" href="javascript:void(0);"> <span class="glyphicon glyphicon-pencil"></span></a>
        </div>
    </div>
    <div class="form-group" model-attribute="title">
        <?php echo CHtml::label($clabels['stat'], CHtml::getIdByName('Calendar[stat]'), array('class'=>'col-sm-2 control-label'));?>
        <div class="col-sm-10 form-inline">
            <% if(isStatus==10){%>
                <div class="checkbox">
                    <label>
                       <input type="checkbox" value="1" name='Calendar[stat]' checked="checked"><span>家长是否能查看校历</span>
                    </label>
                </div>
            <%}else{%>
                <div class="checkbox">
                    <label>
                       <input type="checkbox" value="1" name='Calendar[stat]'><span>家长是否能查看校历</span>
                    </label>
                </div>
            <%}%> 
            
        </div>
    </div>
    </script>
<script type="text/template" id="schooldays">
    <%if(isshow!='0'){ %>
     <div class="form-group">
        <div class="col-sm-2"></div>
        <div class="col-sm-10 form-inline">

                <p><span><%= '<?php echo Yii::t('global', 'Total School Days: %d Days'); ?>'.replace(/%d/g,year) %> </span></p>
                <p><span> 
                <% if(semester[10]){%>
                    <%= '<?php echo Yii::t('global', 'Fall Semester School Days: %d Days'); ?>'.replace(/%d/g,semester[10])  %>
                <%}else{%>
                    <%= '<?php echo Yii::t('global', 'Fall Semester School Days: %d Days'); ?>'.replace(/%d/g,0) %><%}%> 
                </span></p>
                <p><span> 
                <% if(semester[20]){%>
                    <%= '<?php echo Yii::t('global', 'Spring Semester School Days: %d Days'); ?>'.replace(/%d/g,semester[20])  %>
                <%}else{%>
                    <%= '<?php echo Yii::t('global', 'Spring Semester School Days: %d Days'); ?>'.replace(/%d/g,0)  %>
                <%}%> 
                </span></p>
            
        </div>
    </div>
    <%} %>
</script>
<!--Calendar Day List Template 校园-->
<script type="text/template" id="day-item-template">
    <dl theday="<%= date %>" dayid="<%= did %>">
        <dt>
            <a theday="<%= date %>" href="javascript:;" onclick="setCalendarDate(this)"><span class="glyphicon glyphicon-calendar"></span> <%= datestr %></a>
            <a class="hover text-danger J_ajax_del" href="<?php echo $this->createUrl('//moperation/calendar/delDay');?>&did=<%= did%>&yid=<%= currentCalendarId%>"> <span class="glyphicon glyphicon-remove"></span></a>
        </dt>
        <dd><%= title_cn %> <%= title_en %></dd>
        <dd><%= memo_cn %> <%= memo_en %></dd>
    </dl>
</script>
<!--Calendar Day List Template 总部-->
<script type="text/template" id="days-item-template">
    <dl theday="<%= date %>" dayid="<%= did %>">
        <dt>
            <a theday="<%= date %>" href="javascript:;" onclick="setCalendarDate(this)"><span class="glyphicon glyphicon-calendar"></span> <%= datestr %></a>
            <a class="hover text-danger J_ajax_del" href="<?php echo $this->createUrl('//moperation/calendar/delDay');?>?did=<%= did%>&yid=<%= currentCalendarId%>"> <span class="glyphicon glyphicon-remove"></span></a>
        </dt>
        <dd><%= title_cn %> <%= title_en %></dd>
        <dd><%= memo_cn %> <%= memo_en %></dd>
    </dl>
</script>
<!--Calendar Day List Template-->
<script type="text/template" id="day-item-readonly-template">
    <dl theday="<%= date %>" dayid="<%= did %>">
        <dt>
            <span class="glyphicon glyphicon-calendar"></span> <%= datestr %>
        </dt>
        <dd><%= title_cn %> <%= title_en %></dd>
        <dd><%= memo_cn %> <%= memo_en %></dd>
    </dl>
</script>

<!--Calendar Day Edit Template-->
<script type="text/template" id="day-item-edit-template">
    <div class="row form-horizontal">
        <div class="col-md-2">
            <ul class="list-group" id="editing-days-list">
            </ul>
            <div class="checkbox">
                <label>
                    <input type="checkbox" name="selectManyDays" id="selectManyDays"> <?php echo Yii::t('message','Edit Multiple Days');?>
                </label>
            </div>
        </div>
        <div class="col-md-9">
            <div class="form-group">
                <?php echo CHtml::label($cdLabels['privacy'], CHtml::getIdByName('CalendarDay[privacy]'), array('class'=>'col-sm-2 control-label'));?>
                <div class="col-sm-10">
                    <div class="checkbox">
                        <?php echo CHtml::checkBox('CalendarDay[privacy]','<%= privacy %>', array('class'=>''));?>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <?php echo CHtml::label($cdLabels['day_type'], CHtml::getIdByName('CalendarDay[day_type]'), array('class'=>'col-sm-2 control-label'));?>
                <div class="col-sm-10">
                    <?php echo CHtml::dropDownList('CalendarDay[day_type]','<%= day_type %>', CalendarDay::getTimeType(), array('class'=>'form-control select_3'));?>
                </div>
            </div>
            <div class="form-group">
                <?php echo CHtml::label($cdLabels['event'], CHtml::getIdByName('CalendarDay[event]'), array('class'=>'col-sm-2 control-label'));?>
                <div class="col-sm-10">
                    <?php echo CHtml::dropDownList('CalendarDay[event]','<%= event %>',
                        CalendarDay::getDayType(),
                        array('class'=>'form-control length_3','empty'=>Yii::t("global", 'Please Select'),'onChange'=>'changeEventType(this)',
                        'options'=>array('10'=>array('disabled'=>$campus),'30'=>array('disabled'=>$campus))
                        )
                        );
                    ?>
                    <ul id="event-default-list" class="nav nav-pills pt10" style="display: none">
                    </ul>
                </div>
            </div>
            <div class="form-group">
                <?php echo CHtml::label($cdLabels['title_cn'], CHtml::getIdByName('CalendarDay[title_cn]'), array('class'=>'col-sm-2 control-label'));?>
                <div class="col-sm-10">
                    <?php echo CHtml::textField('CalendarDay[title_cn]','<%= title_cn %>', array('encode'=>false,'class'=>'form-control length_5')); ?>
                </div>
            </div>
            <div class="form-group">
                <?php echo CHtml::label($cdLabels['memo_cn'], CHtml::getIdByName('CalendarDay[memo_cn]'), array('class'=>'col-sm-2 control-label'));?>
                <div class="col-sm-10">
                    <?php echo CHtml::textField('CalendarDay[memo_cn]','<%= memo_cn %>', array('encode'=>false,'class'=>'form-control length_5')); ?>
                </div>
            </div>
            <div class="form-group">
                <?php echo CHtml::label($cdLabels['title_en'], CHtml::getIdByName('CalendarDay[title_en]'), array('class'=>'col-sm-2 control-label'));?>
                <div class="col-sm-10">
                    <?php echo CHtml::textField('CalendarDay[title_en]','<%= title_en %>', array('encode'=>false,'class'=>'form-control length_5')); ?>
                </div>
            </div>
            <div class="form-group">
                <?php echo CHtml::label($cdLabels['memo_en'], CHtml::getIdByName('CalendarDay[memo_en]'), array('class'=>'col-sm-2 control-label'));?>
                <div class="col-sm-10">
                    <?php echo CHtml::textField('CalendarDay[memo_en]','<%= memo_en %>', array('encode'=>false,'class'=>'form-control length_5')); ?>
                </div>
            </div>
            <div class="form-group">
                <div class="col-sm-2">
                    &nbsp;
                </div>
                <div class="col-sm-10">
                    <?php echo CHtml::hiddenField('editingDays'); ?>
                    <?php echo CHtml::hiddenField('CalendarDay[yid]'); ?>
                    <button type="button" class="btn btn-primary J_ajax_submit_btn"><?php echo Yii::t('global','Save');?></button>
                    <button onclick="closeEditingDayWindow()" type="button" class="btn btn-default"><?php echo Yii::t('global','Cancel');?></button>
                </div>
            </div>

        </div>
    </div>
</script>