<div class="table_list">
	<table width="100%" id="J_table_list" style="table-layout:fixed;">
		<colgroup>
			<col width="30">
			<col width="380">
			<col width="260">
			<col>
		</colgroup>
		<thead>
			<tr>
				<td></td>
				<td>[标记] 英文名称</td>
				<td>中文名称</td>
				<td>操作</td>
			</tr>
		</thead>
	<?php
		foreach ($items as $item) {
		$count=count($item->diglossia);
		$icon='zero_icon';
		if($count>0){
			$icon='J_start_icon away_icon';
		}
	?>
		<tbody>
		<tr>
			<td><span class="<?php echo $icon?>" data-id="<?php echo $item->category_id?>"></span></td>
			<td>
				<input name="data[<?php echo $item->category_id?>][orderid]" type="text" class="input length_1 mr10" value="<?php echo $item->category_sign?>" readonly>
				<input name="data[<?php echo $item->category_id?>][name]" type="text" class="input length_3 mr5" value="<?php echo $item->category_entitle?>" readonly>
				<a href="<?php echo $this->createUrl('/settings/default/index', array('target'=>'editDiglossia', 'type'=>2, 'catid'=>$item->category_id));?>" class="link_add J_dialog add_nav" data-id="<?php echo $item->category_id?>" data-html="tbody" data-type="nav_2" title='添加二级选项'>添加二级选项</a>
			</td>
			<td><input name="data[<?php echo $item->category_id?>][link]" type="text" class="input length_4" value="<?php echo $item->category_cntitle?>" readonly></td>
			<td>
				<a href="<?php echo $this->createUrl('/settings/default/index', array('target'=>'editDiglossia', 'type'=>1, 'id'=>$item->category_id));?>" class="mr10 J_dialog" title="编辑">[编辑]</a>
				<a href="<?php echo $this->createUrl('/settings/default/index', array('target'=>'delDiglossia', 'type'=>1, 'id'=>$item->category_id));?>" class="mr10 J_ajax_del">[删除]</a>
			</td>
		</tr>
		</tbody>
		<?php if($count>0){?>
			<tbody id="J_table_list_<?php echo $item->category_id?>">
		<?php
            $i = 0;
			foreach ($item->diglossia as  $childValue) {
            $endicon=($i++==$count-1)?' plus_end_icon':'';
		?>
			<tr>
				<td>&nbsp;</td>
				<td><span class="plus_icon<?php echo $endicon;?> mr10"></span><input name="data[<?php echo $childValue->diglossia_id?>][navid]" type="hidden" value="<?php echo $childValue->diglossia_id?>" readonly ><input name="data[<?php echo $childValue->diglossia_id?>][orderid]" type="text" class="input length_0 mr10" value="<?php echo $childValue->weight?>" style="width:20px;" readonly><input name="data[<?php echo $childValue->diglossia_id?>][name]" type="text" class="input length_3 mr5" value="<?php echo $childValue->entitle?>" readonly>
					</td>
				<td>
					<input name="data[<?php echo $childValue->diglossia_id?>][link]" type="text" class="input length_4" value="<?php echo $childValue->cntitle?>" readonly>
				</td>
				<td>
					<a href="<?php echo $this->createUrl('/settings/default/index', array('target'=>'editDiglossia', 'type'=>2, 'id'=>$childValue->diglossia_id));?>" class="mr10 J_dialog" title='编辑'>[编辑]</a><a href="<?php echo $this->createUrl('/settings/default/index', array('target'=>'delDiglossia', 'type'=>2, 'id'=>$childValue->diglossia_id));?>" class="mr10 J_ajax_del">[删除]</a>
				</td>
			</tr>
            <?php }?>
		</tbody>
        <?php }}?>
	</table>
	<table width="100%">
		<tr class="ct">
            <td colspan="5" style="padding-left:38px;">
                <a data-type="nav_1" data-html="tbody" href="<?php echo $this->createUrl('/settings/default/index', array('target'=>'editDiglossia', 'type'=>1));?>" class="link_add J_dialog" title='添加选项分类'>添加选项分类</a>
            </td>
        </tr>
	</table>
</div>

<script>

head.js(GV.JS_ROOT+ 'pages/admin/common/forumTree_table.js?v=' +GV.JS_VERSION);
</script>