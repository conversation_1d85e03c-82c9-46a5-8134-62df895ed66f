<div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
    </button>
    <h4 class="modal-title" id="exampleModalLabel">查看详细信息 [<a href="javascript:;" id="print">打印</a>]</h4>
</div>
<?php $form = $this->beginWidget('CActiveForm', array(
    'id' => 'course-forms',
    'enableAjaxValidation' => false,
    'htmlOptions' => array('class' => 'form-horizontal', 'role' => 'form'),
)); ?>
<div class="modal-body" id="workflow-modal-body">
    <h4 class="text-center"><?php echo Yii::t('asa', '现金交接详细信息'); ?> #<?php echo $model->id; ?></h4>
    <div class="table-responsive">
        <table class="table table-bordered">
            <colgroup>
                <col width="200">
                <col width="200">
                <col width="200">
                <col width="200">
            </colgroup>
            <tbody>
            <tr>
                <td>
                    <?php echo $form->labelEx($model, 'amount'); ?>
                </td>
                <td>
                    <div><?php echo $model->amount ?></div>
                </td>
                <td><?php echo $form->labelEx($model, 'item_number'); ?></td>
                <td><?php echo $model->item_number ?></td>
            </tr>
            <tr>

                <td><?php echo $form->labelEx($model, 'status'); ?></td>
                <td><?php echo $status[$model->status] ?></td>
                <td>
                    <?php echo $form->labelEx($model, 'bank_ref_url'); ?>
                </td>
                <td>
                    <?php if ($model->bank_ref_url) { ?>
                        <a href="<?php echo $this->createUrl('downloadsHand', array('id' => $model->id)); ?>"
                           target="_blank"
                           class="btn btn-info btn-xs"><span class="glyphicon glyphicon-file"></span> 点击查看附件</a>
                    <?php } ?>
                </td>
            </tr>
            <tr>
                <td>
                    <?php echo $form->labelEx($model, 'bank_ref'); ?>
                </td>
                <td>
                    <?php echo $model->bank_ref ?>
                </td>
                <td>
                    <?php echo $form->labelEx($model, 'bank_deposit_time'); ?>
                </td>
                <td>
                    <?php echo date("Y-m-d", $model->bank_deposit_time) ?>
                </td>
            </tr>

            <tr>

                <td><?php echo $form->labelEx($model, 'creater'); ?></td>
                <td><?php echo $userNames[$model->creater] ?></td>

                <td>
                    <?php echo $form->labelEx($model, 'receiver'); ?>
                </td>
                <td>
                    <?php echo ($model->receiver) ? $userNames[$model->receiver] : ""; ?>
                </td>
            </tr>
            <tr>
                <td>
                    <?php echo $form->labelEx($model, 'created'); ?></td>
                <td>
                    <?php echo date("Y-m-d", $model->created) ?></td>

                <td>
                    <?php echo $form->labelEx($model, 'received'); ?>
                </td>
                <td>
                    <?php echo ($model->received) ? date("Y-m-d", $model->received) : ""; ?>
                </td>
            </tr>
            <tr>

                <td>
                    <?php echo $form->labelEx($model, 'creater_memo'); ?>
                </td>
                <td>
                    <?php echo $model->receiver_memo ?>
                </td>
                <td>
                    <?php echo $form->labelEx($model, 'received_memo'); ?>
                </td>
                <td>
                    <?php echo $model->creater_memo ?>
                </td>
            </tr>
            </tbody>
        </table>
    </div>

    <label for=""><?php echo Yii::t('asa', '账单明细'); ?></label>
    <table class="table table-bordered">
        <colgroup>
            <col width="400">
            <col width="200">
            <col width="200">
        </colgroup>
        <tr>
            <td><?php echo Yii::t('asa', '账单名字'); ?></td>
            <td><?php echo Yii::t('asa', '账单金额'); ?></td>
            <td><?php echo Yii::t('asa', '付款时间'); ?></td>
        </tr>
        <?php foreach ($invoices as $_invoice): ?>
            <tr>
                <td><?php echo $_invoice->title ?></td>
                <td><?php echo $_invoice->amount_actual ?></td>
                <td><?php echo date("Y-m-d", $_invoice->updated) ?></td>
            </tr>
        <?php endforeach; ?>
    </table>

    <?php $this->endWidget(); ?>
    <script>

        $(function () {
            $("#print").click(function () {
                $("#workflow-modal-body a").removeAttr('href');
                $("#workflow-modal-body").printThis();
            })
        });
    </script>