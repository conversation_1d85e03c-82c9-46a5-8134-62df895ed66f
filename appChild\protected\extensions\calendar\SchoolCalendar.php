<?php
class SchoolCalendar extends CWidget{
	public $startYear;
	public $lang;
	public $schoolId;
	public $stat;

	public function init(){
		Yii::import('common.models.calendar.*');
	}
	
	public function run(){
		$base = CalendarSchool::model()->getCalendar($this->startYear, $this->schoolId);
		$calendar = $base['calendar'];
		$calendarDays = $calendar->getCalendarDays(); //事件或假期
		$months = $calendar->getCalendarMonths();   //每月工作日
		$semesters = $calendar->getCalendarSemesters();
		$descs = $calendar->getCalendarDesc();
				
		$schoolYearStart = $semesters['10']['school_start_timestamp'];
		$schoolYearEnd   = $semesters['20']['school_end_timestamp'];
		$cycletime = mktime(0,0,0, date('m',$schoolYearStart),1, date('Y',$schoolYearStart));
		$i = 0;
		$month = new PublicMonth(date('Y', $cycletime), date('m',$cycletime), $semesters);
		while( $cycletime <= $schoolYearEnd ){
			$month->resetYm(date('Y', $cycletime), date('m',$cycletime));
			$_key = date('Ym',$cycletime);
			$schoolDays = (!empty($months[$_key]['schoolday_array'])) ? explode(",", $months[$_key]['schoolday_array']) : array();
			$months[$_key]['calendars'] = $month->getMonthData($schoolDays,$calendarDays);
			$i++;
			$cycletime = mktime(0,0,0, date('m',$schoolYearStart) + $i, 1, date('Y',$schoolYearStart));
		}
		$stats = $month->getStats();
		$this->render('calendar',array("months"=>$months,"descs"=>$descs,"branch"=>$base['branch'],"stats"=>$stats,'stat'=>$this->stat));
	}
	
}