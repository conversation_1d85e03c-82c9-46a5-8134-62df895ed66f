<style>
    [v-cloak] {
        display: none;
    }
    .spanTag{
        padding:4px 8px;
        border-radius: 2px;
        font-weight: 500;
        font-size: 12px;
        line-height: 12px;
        display: inline-block;
    }
    .warningBg{
        background: #F9E2E1;
    }
    .successBg{
        background: #E5F3E5;
    }
    .nav-stacked > li > a{
        color:#333333
    }
    .tableTop{
        background: #F2F3F5;
        border-radius: 16px;
        width:100%;
    }
    .tableTop th{
        padding:10px;
        text-align:center;
        font-size:14px;
        font-weight:bold;
        color:#333
    }
    .table td{
        vertical-align: middle !important;
        padding:16px !important
    }
    .overflow {
        word-break: break-word;
        text-overflow: ellipsis;
        display: -webkit-box; 
        -webkit-box-orient: vertical; 
        overflow: hidden;
      }
      .avatar{
        width: 62px;
        height: 62px;
        border-radius: 8px;
      }
      .warningIcon{
        position: absolute;
        top: -9px;
        right: -9px;
        font-size: 16px;
      }
      .red{
        color: #D9534F;
      }
      .green{
        color: #5CB85C;
      }
      .down{
        position: absolute;
        width: 20px;
        height: 20px;
        background: #DCE8F6;
        border-radius: 4px 0px 0px 0px;
        right: 0;
        bottom: 0;
        text-align: center;
        line-height: 20px;
        font-size: 14px;
        color: #4D88D2;
        cursor: pointer;
      }
      .up{
        position: absolute;
        width: 20px;
        height: 20px;
        background: #4D88D2;
        border-radius: 4px 0px 0px 0px;
        right: 0;
        bottom: 0;
        text-align: center;
        line-height: 20px;
        font-size: 14px;
        color: #fff;
        cursor: pointer; 
      }
      .minheight{
        min-height:120px
      }
      .height{
        height:120px
      }
      .currentBg{
        background:#F0F5FB
      }
      .current{
        position: absolute;
        background: rgb(240, 245, 251);
        z-index: 99;
        margin: -59px 16px -16px -16px;
        padding:16px;
        box-shadow: 0px 4px 6px 0px rgba(0,0,0,0.08);
        width: 100%;
      }
    .expand{
        max-height:86px;
      }
    .scroll-box::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius   : 10px;
        background-color: skyblue;
        background-color: #D8D8D8;
        background-image: none
    }
    .scroll-box::-webkit-scrollbar-track {
        /*滚动条里面轨道*/
        box-shadow   : inset 0 0 5px rgba(0, 0, 0, 0);
        background   : #fff;
        border-radius: 10px;
    }
    .classList{
        padding:10px;
        color:#333
    }
    .font10{
        font-size:11px;
        color:#666
    }
    .online{
        margin-top:2px
    }
    .online .spanTag{
        padding:3px 4px;
    }
    .classList:hover{
        background: #F2F3F5;
        cursor: pointer;
    }
    .classListActive{
        background: #4D88D2 !important;
        color:#fff !important;
        border-radius:4px
    }
    /* .classListActive .font10 , .classListActive .font10 span{
        color:#fff !important;
    } */
    .el-loading-mask{
        z-index:1000
    }
    .overFlow{
        overflow: hidden;
    }
</style>
<div class="container-fluid">
    <ol class="breadcrumb">
        <li><?php echo CHtml::link(Yii::t('site', 'Campus Workspace'), array('//mcampus/default/index')) ?></li>
        <li><?php echo Yii::t('site','ES Batch Preview Reports');?></li>
    </ol>
    <div class="row overFlow" id='container' v-cloak>
        <div class="col-md-12 col-sm-12 ml8 mb20" v-if="semester">
            <select class="form-control length_3 inline-block" v-model='semester' @change='tabSemester()'>
                <option v-for='(item,index) in semesterList' :value='item.value'>{{item.title}}</option>
            </select>
        </div>
        <div class="col-md-2 col-sm-12 pr8">
            <div class=" overflow-y scroll-box pr8" :style="'max-height:'+(height-241)+'px;overflow-x: hidden;'">
                <div class='classList' :class='classId==list.classid?"classListActive":""' v-for='(list,index) in classList' @click='getData(list.classid,"init")'>
                    <div class='font14 '  >{{list.title}}</div>
                    <div class='online' v-if='classOnlineData[list.classid]'>
                        <span class='spanTag successBg green mr4' v-if='classOnlineData[list.classid].online!=0'><span class='el-icon-success mr4 font12'></span><?php echo Yii::t("teaching", "Online"); ?>：{{classOnlineData[list.classid].online}}</span>
                        <span class='spanTag warningBg red '  v-if='classOnlineData[list.classid].offline!=0'><span class='el-icon-warning mr4 font12'></span><?php echo Yii::t("teaching", "Offline"); ?>：{{classOnlineData[list.classid].offline}}</span>
                        <!-- <span class='mr8'> <?php echo Yii::t("teaching", "Online"); ?>：<span class='green fontBold'>{{classOnlineData[list.classid].online}}</span></span>
                         <?php echo Yii::t("teaching", "Offline"); ?>： <span class='red fontBold'>{{classOnlineData[list.classid].offline}}</span> -->
                    </div>
                </div>
           </div>
        </div>
        <div class='col-md-10 col-sm-12' v-loading='loading' v-if='classList.length' :style="'height:'+(height-241)+'px;overflow-x: hidden;border-left: 1px solid #E5E6EB;'">
            <div class='row'  v-if='tableData.online_number'>
                <div class='col-md-8 col-sm-12'>
                    <span class='font14 color3'><?php echo Yii::t("teaching", "Reports: "); ?></span>
                    <div class='spanTag successBg green ml5'><span class='el-icon-success mr4 font14'></span><?php echo Yii::t("teaching", "Online"); ?>：{{tableData.online_number.online}}</div>
                    <div class='spanTag warningBg red ml16'><span class='el-icon-warning mr4 font14'></span><?php echo Yii::t("teaching", "Offline"); ?>：{{tableData.online_number.offline}}</div>
                </div>
                <div class='col-md-4 col-sm-12' v-if='tableData.child_list && tableData.child_list.length'>
                    <button type="button" class="btn btn-success ml16 pull-right" @click='confirmOnline(1,"all")'><?php echo Yii::t("teaching", "Make All Online"); ?></button>
                    <button type="button" class="btn btn-danger pull-right" @click='confirmOnline(0,"all")'><?php echo Yii::t("teaching", "Make All Offline"); ?></button>
                </div>
            </div>
            <div class='mt24' v-if='tableData.child_list && tableData.child_list.length'>
                <table class='tableTop'>
                    <tr>
                        <th width='200'><?php echo Yii::t("campus", "Students List"); ?></th>
                        <th width='250'><?php echo Yii::t("ptc", "Subject"); ?></th>
                        <th><?php echo Yii::t('report', 'Teacher\'s Comment / Next Step'); ?></th>
                    </tr>
                </table>
                <div class='overflow-y mt16 scroll-box' :style="'max-height:'+(height-352)+'px;overflow-x: hidden;margin-right:-15px;padding-right:15px'">                
                    <table class='table table-bordered mb16' v-for='(list,i) in tableData.child_list'>
                        <tr v-for='(item,index) in tableData.child_comments[list.id]'>
                            <td :rowspan='tableData.child_comments[list.id].length' v-if='index==0' width='200' style='background:#F2F3F5;text-align:center'>
                                <div class='inline-block relative'>
                                    <img :src="list.avatar" data-holder-rendered="true" class="avatar">
                                    <!-- <span class='el-icon-success warningIcon green'></span> -->
                                    <img  class='warningIcon'  v-if='tableData.reports_online[list.id] && tableData.reports_online[list.id].status==1' src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/success.png' ?>" width='16' alt="">
                                    <img v-else  class='warningIcon' src="<?php echo Yii::app()->themeManager->baseUrl . '/base/images/error.png' ?>" width='16' alt="">
                                    <!-- <span class='el-icon-warning warningIcon red' v-else></span> -->
                                </div>
                                <div class='font12 color3 mt8'>{{list.name}}</div>
                                <div class='mt20 mb16'>   
                                    <button type="button" class="btn btn-default btn-sm" @click='reportPreview(list.id,tableData.reports_online[list.id].data_id)'><?php echo Yii::t("teaching", "Preview"); ?></button>
                                </div>
                                <button type="button" class="btn btn-danger btn-sm" v-if='tableData.reports_online[list.id] && tableData.reports_online[list.id].status==1' @click='confirmOnline(0,list.id)'><?php echo Yii::t("teaching", "Make Offline"); ?></button>
                                <button type="button" class="btn btn-success btn-sm" v-else @click='confirmOnline(1,list.id)'><?php echo Yii::t("teaching", "Make Online"); ?></button>
                            </td>
                            <td width='250'  :class='expandId==list.id+"_"+item.category_id?"currentBg":""'>
                                <div class='font14 color3 mb5 fontBold'>{{item.program_name}}</div>
                                <div class='font12 color6' v-for='(tea,id) in item.uid'><span class='el-icon-user-solid'></span> {{tableData.teacher_list[tea].name}}</div>
                            </td>
                            <td class='relative' :class="[expandStates[list.id + '_' + item.category_id] ? 'height' : 'minheight',expandId == list.id + '_' + item.category_id ? 'currentBg' : '']">
                                <div class='overflow' :class="[ expandId == list.id + '_' + item.category_id ? 'current relative' : 'expand']"
                                 > 
                                    <div v-html='item.comments' class='cur-p' :class="list.id + '_' + item.category_id" @click='showExpand(list.id,item.category_id)'></div>
                                    <span class='el-icon-arrow-up up' @click='expandId=null' v-if='expandId==list.id+"_"+item.category_id' ></span>
                                </div>
                                <span class='el-icon-arrow-down down' v-if='item.comments!="" && expandId!=list.id+"_"+item.category_id && expandStates[list.id+"_"+item.category_id]' @click='showExpand(list.id,item.category_id)'></span>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class='mt24' v-else-if='tableData.child_list'>
                <el-empty description="<?php echo Yii::t("ptc", "No Data"); ?>"></el-empty>
            </div>
        </div>
        <div v-else-if='semesterList.length' class="col-md-12 col-sm-12 ml10 mb20">
            <el-empty description="<?php echo Yii::t("ptc", "No Data"); ?>"></el-empty>
        </div>
        <!-- 删除 -->
        <div class="modal fade" id="confirmModal" tabindex="-1" role="dialog" aria-labelledby="wantSayModalLabel">
            <div class="modal-dialog modal-sm" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="parentReplyLabel">{{childStatus=='1'?'<?php echo Yii::t("teaching", "Make Online"); ?>':"<?php echo Yii::t("teaching", "Make Offline"); ?>"}}</h4>
                    </div>
                    <div class="modal-body p24" >
                        <div v-if='statusAll'>
                            {{childStatus=='1'?'<?php echo Yii::t('newDS', '确认全部上线吗？') ?>':"确认全部下线吗？"}}
                        </div>
                        <div v-else>
                            {{childStatus=='1'?'<?php echo Yii::t('newDS', '确认上线吗？') ?>':"确认下线吗？"}}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo Yii::t("global", "Cancel"); ?></button>
                        <button type="button" class="btn btn-primary" :disabled='btnDisanled' @click='onlineChild()'><?php echo Yii::t("global", "OK"); ?></button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$this->renderPartial('//layouts/common/branchSelectBottom');
?>
<script>
    var height=document.documentElement.clientHeight;

    var container = new Vue({
        el: "#container",
        data: {
            height:height,
            expandId:null,
            classList:[],
            classId:'',
            semesterList:[],
            semester:'',
            tableData:{},
            expandStates:{},
            childStatus:null,
            statusAll:false,
            btnDisanled:false,
            ids:[],
            loading:false
        },
        watch: {},
        created: function() {
           this.initClass()
        },
        methods: {
            initClass(semester){
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("ESReportClassList") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        semester:semester
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.classList=data.data.list
                            that.classOnlineData=data.data.classOnlineData
                            that.semesterList=data.data.semester_list
                            if(!semester){
                                let semester=data.data.semester_list.find(i => i.current ==1);
                                that.semester=semester.value
                            }
                            if(that.classId!=''){
                                let classData=that.classList.find(i => i.classid ==that.classId);
                                if(classData){
                                    that.getData(that.classId,'init')
                                }else{
                                    that.tableData={}
                                }
                            }
                        }else{
                            resultTip({
                                error: 'warning',
                                msg: data.message
                            }); 
                        }
                    },
                    error: function(data) {
                        
                    },
                })
            },
            showExpand(index,id){
                if(!this.expandStates[index+"_"+id]){
                    return
                }
                if(this.expandId==index+"_"+id){
                    this.expandId=''
                }else{
                    this.expandId=index+"_"+id
                }
            },
            tabSemester(){
                this.initClass(this.semester)
            },
            getData(id,type){
                let that=this
                this.classId=id
                this.loading=true
                if(this.classId){
                    $.ajax({
                        url: '<?php echo $this->createUrl("ESReportTeacherComment") ?>',
                        type: "get",
                        dataType: 'json',
                        data: {
                            class_id:this.classId,
                            semester:this.semester
                        },
                        success: function(data) {
                            if (data.state == 'success') {
                                that.loading=false
                                that.sortData(data.data.child_list)
                                that.tableData=data.data
                                for(let key in data.data.child_comments){
                                    data.data.child_comments[key].forEach(item => {
                                        that.checkHeightAndSetExpandState(key,item.category_id);
                                    });
                                }
                            }else{
                                that.loading=false
                                resultTip({
                                    error: 'warning',
                                    msg: data.message
                                });
                            }
                        },
                        error: function(data) {
                            that.loading=false
                        },
                    })
                }else{
                    that.loading=false
                }

            },
            sortData(list){
                <?php if (Yii::app()->language == 'zh_cn') { ?>
                    list.sort((a, b)=> a.name.localeCompare(b.name, 'zh'));
                <?php } else { ?>
                    list.sort((a, b) => a.name.charCodeAt(0) - b.name.charCodeAt(0));
                <?php } ?>
            },
            async checkHeightAndSetExpandState(listId, categoryId) {
                const expandId = `${listId}_${categoryId}`;
                await this.$nextTick(); 
                const element = $(`.${expandId}`);
                if (element) {
                    const height = element.height();
                    this.$set(this.expandStates, expandId, height > 86);
                }
            },
            confirmOnline(status,id){
                this.childStatus=status
                this.ids=[]
                if(id=='all'){
                    this.statusAll=true
                    for(let key in this.tableData.reports_online){
                        this.ids.push(this.tableData.reports_online[key].data_id)
                    }
                }else{
                    this.ids=[this.tableData.reports_online[id].data_id]
                    this.statusAll=false

                }
                $('#confirmModal').modal('show')
            },
            onlineChild(){
                this.btnDisanled=true
                let that=this
                $.ajax({
                    url: '<?php echo $this->createUrl("setESReportsOnline") ?>',
                    type: "get",
                    dataType: 'json',
                    data: {
                        status:this.childStatus,
	                    data_ids:this.ids
                    },
                    success: function(data) {
                        if (data.state == 'success') {
                            that.initClass(that.semester)
                           that.btnDisanled=false
                           resultTip({
                                msg: data.message
                            });
                            $('#confirmModal').modal('hide')

                        }else{
                           that.btnDisanled=false
                           resultTip({
                                error: 'warning',
                                msg: data.message
                            });
                        }
                    },
                    error: function(data) {
                        that.btnDisanled=false
                        
                    },
                })
            },
            reportPreview(childid, id) {
                var url = "<?php echo $this->createUrl('semester/previewReportDs', array(
                            'childid' => '-childid-',
                            'id' => '-id-',
                            'classid' => '-classid-',
                        )); ?>";
                url = url.replace('-childid-', childid);
                url = url.replace('-id-', id);
                url = url.replace('-classid-', this.classId);
                window.open(url);
            }
        }
    })
</script>